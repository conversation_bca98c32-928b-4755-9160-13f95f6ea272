clmapp.controller("createTemplatectrl", function ($scope, $location, $http,$timeout,$window, $rootScope,AuthService, $cookieStore, AppUtil) {
	
$scope.init = function(){
		
		$scope.templateAttributeName = [];
		$scope.selectTemplatesFileData = [];
		$scope.templateAttributeDescription=[];
		//$scope.selectTemplatesFileData=[];
		
		 //$scope.injectedObject = {};
	}
	
	$scope.templateCategoryLists = [{code: "PROMOTIONAL", name:"PROMOTIONAL"}, {code: "TRANSACTIONAL", name:"TRANSACTIONAL"}];
	 $scope.templateCategory=$scope.templateCategoryLists[0];
	 
	 $scope.templateTypeLists = [{code: "SMS", name:"SMS"}, {code: "EMAIL", name:"EMAIL"}];
	 $scope.selectedTemplateType=$scope.templateTypeLists[0];
	 
	 $scope.templateFile = [{code: "FILE", name:"FIL<PERSON>"}, {code: "TEXT", name:"TEXT"}];
	 $scope.selectTemplatesFileData=$scope.templateFile[0];
	
	 console.log($scope.selectTemplatesFileData);
	   // templateAttributeName[templateAttributeResult]
	 
	    $scope.showTemplateDetails=function(val,indexes,nameValue)
		{
			if(val.name=='TEXT')
				{
				$("#text"+nameValue).show();
				}
			else
				{
				$("#text"+nameValue).hide();
				}
		}
	 
	    
	    $scope.sort_by = function(predicate) {
	        $scope.predicate = predicate;
	        $scope.reverse = !$scope.reverse;
	    };
	    
	    $scope.updatePerm = function(val){
	    	if(val==='true'){
	    		$scope.permanentAddressSame = true;
	    	}else{
	    		$scope.permanentAddressSame = false;
	    	}
	    }
	    
	 $http({
			method: 'GET',
			  url: AppUtil.restUrls.clmMetaData.viewTemplate
			}).then(function success(response) {
				console.log(response.data);
				$rootScope.showFullScreenLoader = false;
				$scope.viewTemplateData=response.data;
				console.log($scope.viewTemplateData);
				$scope.currentPage = 1; //current page
				$scope.entryLimit = 50; //max no of items to display in a page
				$scope.filteredItems = $scope.viewTemplateData.length; //Initially for no filter  
				$scope.totalItems = $scope.viewTemplateData.length;
				//console.log(viewTemplateData);
				//$scope.selectedIntentType=$scope.intentData[0];
				
			}, function error(response) {
				$rootScope.showFullScreenLoader = false;
				  console.log("error:"+response);
		});
	 
	 
	 
	 
	 
	 $scope.parseTemplate=function()
	    {
		 
		 
		 /*if($scope.action="add")
			 {*/
			 $scope.templateAttributeDataDisplay=[];
					var re= /\$(.*?)\$/g;
					for(m = re.exec($scope.templateDescription); m; m = re.exec($scope.templateDescription)){
					   // alert(m[1])
					    $scope.templateAttributeDataDisplay.push(m[1]);
					}
					
			// }	
		 //else if($scope.action="edit")
			// {
			 console.log($scope.templateAttributeDataDisplay);
			 
			 $scope.templateAttributeDataDisplay.forEach(function(attributeDisplayVal){
				
				//$scope.selectTemplatesFileData=$scope.selectTemplatesFileData[attributeDisplayVal];
				
				 $scope.selectTemplatesFileData[attributeDisplayVal]=$scope.templateFile[0];
				
				
			});
			 
			 
			// $scope.templateFile = [{code: "FILE", name:"FILE"}, {code: "TEXT", name:"TEXT"}];
			 //$scope.selectTemplatesFileData=$scope.templateFile[0];
			 
			 //}
					
				
					/*$scope.templateAttributeDataDisplay.forEach(function(attributeDisplayVal){
						
						$scope.selectTemplatesFileData=$scope.selectTemplatesFileData[attributeDisplayVal];
						
						 $scope.selectTemplatesFileData=$scope.templateFile[0];
						
						
					});*/
		 
		 /*$scope.templateObj = {
					name:$scope.templateName,
					category:$scope.templateCategory,
					type:$scope.selectedTemplateType,
					attribute:selectedTemplateAttribute
				} */
		 
		 $("#templateAttributeListDiv").show();
		 
	    }
	 
	 $scope.editCreateTemplate=function(templateId)
	    {
		$scope.templateAttributeDataDisplay=[];
		$scope.editTemplateData=[];
		//$scope.templateAttributeDataDisplay={};
	    $scope.templateID=templateId;
	    $scope.action="edit";
    	$scope.viewTemplateData.forEach(function(tempDetails){
    		//console.log(tempDetails);
    		
    		
    		if(tempDetails.id==templateId){
    				//$scope.campaignName			=	campaignDetails.name;
	    			//$scope.campaignDescription	=	campaignDetails.description;
	    			//$scope.campaignID			=	campaignId;
    			$scope.editTemplateData.push(tempDetails);
    				var selectedTemplateCategoryData 	= $scope.templateCategoryLists.filter(function(catDataType){
    				return catDataType.name == tempDetails.category;
    				});
    				var indexOfCategoryStatus 			= $scope.templateCategoryLists.indexOf(selectedTemplateCategoryData[0]);
    				$scope.templateCategory 			= $scope.templateCategoryLists[indexOfCategoryStatus];
    				var selectedTemplateCategoryType 	= $scope.templateTypeLists.filter(function(tempDataType){
        				return tempDataType.name == tempDetails.type;
        				});
        				var indexOfCategoryStatus 			= 	$scope.templateTypeLists.indexOf(selectedTemplateCategoryType[0]);
        				$scope.selectedTemplateType 		= 	$scope.templateTypeLists[indexOfCategoryStatus];
        				$scope.templateDescription			=	tempDetails.text;
        				$scope.templateName					=	tempDetails.name;
        				
        				console.log('rr',tempDetails);
        				
        				//console.log("YY=",tempDetails.attributes[0].name);
        				
        				//$scope.templateAttributeDataDisplay	=	tempDetails.attributes;
        				
        				
        				
        				$scope.detailAttributeList	=	tempDetails.attributes;
        				tempDetails.attributes.forEach(function(attributeLists){
        					var vals=attributeLists.name;
        					//console.log(attributeLists.name);
        					// $("#text"+attributeLists.name).show();
        					// $("#text"+attributeLists.name[0]).show();
        					//console.log("aa=",attributeLists);
        					$scope.templateAttributeDataDisplay.push(attributeLists.name);
        					//$scope.templateAttributeDataDisplay.push(attributeLists.type);
        					
        					var selectedTemplateFileData 	= $scope.templateFile.filter(function(fileDataType){
                			return fileDataType.name==attributeLists.type;
        					});
        					var indexOfFileStatus 		= $scope.templateFile.indexOf(selectedTemplateFileData[0]);
            				$scope.selectTemplatesFileData[attributeLists.name] = $scope.templateFile[indexOfFileStatus];
            				
            				//console.log(attributeLists.type);
            				if(attributeLists.type=='TEXT')
            					{
            					$scope.templateAttributeDescription[attributeLists.name]=attributeLists.value;
            					//console.log("helllll=",angular.element("#text"+vals));
            					
            					//$scope.templateAttributeDescription[attributeLists.name]
            					
            					//angular.element("#text"+vals).show();
            					
            					
            					
            					}
        				});
        				
    		}
    	});
    	
    	
  
    $("#createTemplateModal").modal("show");
    $("#templateAttributeListDiv").show();
    
    console.log("specifiDATA=",$scope.editTemplateData);
	    }

	$scope.addCreateTemplate=function()
    {
    	$scope.action="add";
    	$scope.templateName='';
		$scope.templateDescription='';
    	$("#createTemplateModal").modal("show");
    }
	
	
	$scope.sbmtCreateTemplates=function()
    {
		$scope.templateAttributeValueResult=[];
		$scope.templateAttributeDataDisplay.forEach(function(tempDetailsData){
			if($scope.templateAttributeDescription[tempDetailsData]==undefined)
				{
					$scope.templateAttributeDescription[tempDetailsData]="FILE";
				}
		$scope.templateAttributeValueResult.push({name:tempDetailsData,type:$scope.selectTemplatesFileData[tempDetailsData].code,value:$scope.templateAttributeDescription[tempDetailsData]});
		});
		///console.log($scope.templateAttributeValueResult);
		$scope.templateAddObj = {
		name:$scope.templateName,
		category:$scope.templateCategory.code,
		type:$scope.selectedTemplateType.code,
		text:$scope.templateDescription,
		attributes:$scope.templateAttributeValueResult
		//attributes:selectedTemplateAttribute
	}	
		
		//$scope.templateAttributeValueResult.push({id:15});
		
			$http({
			  method: 'POST',
			  url: AppUtil.restUrls.clmMetaData.addTemplate,
			  data: $scope.templateAddObj,
			}).then(function success(response) {
				console.log("AllRecords=",$scope.viewTemplateData);
				
				console.log("resultAdd=",response.data);
				if(response.status==200){
					$scope.viewTemplateData.push(response.data);
					console.log("updatedRecords=",$scope.viewTemplateData);
					
					
					$rootScope.showFullScreenLoader = false;
					alert("Template added successfully!");
					$("#createTemplateModal").modal("hide");
					
					
				}else{
					$rootScope.showFullScreenLoader = false;
					console.log(response);					
				}
			}, function error(response) {
				  console.log("error:"+response);
			});
			console.log($scope.templateAddObj);
	    }
	
	$scope.updateCreateTemplates=function(templateID)
    {
		$scope.templateAttributeUpdate=[];
			$scope.editTemplateData.forEach(function(existTemplateInfo){
				for (var i=0;i<existTemplateInfo.attributes.length;i++)
					{
					console.log(existTemplateInfo.attributes[i].name);
					
					$scope.templateAttributeDataDisplay.forEach(function(templateInfo){
						
						if($scope.selectTemplatesFileData[templateInfo].code=='FILE')
							{
							$scope.templateAttributeDescription[templateInfo]='';
							}
						if(existTemplateInfo.attributes[i].name==templateInfo)
							{
							$scope.templateAttributeUpdate.push({id:existTemplateInfo.attributes[i].id,name:templateInfo,type:$scope.selectTemplatesFileData[templateInfo].code,value:$scope.templateAttributeDescription[templateInfo],templateId:existTemplateInfo.attributes[i].templateId});
							}
						/*else if(existTemplateInfo.attributes[i].name!=templateInfo)
							{
							$scope.templateAttributeUpdate.push({name:templateInfo,type:$scope.selectTemplatesFileData[templateInfo].code,value:$scope.templateAttributeDescription[templateInfo],templateId:existTemplateInfo.attributes[i].templateId});

							}*/
					});
					}
				});
		
		$scope.templateUpdateObj = {
				id:templateID,
				name:$scope.templateName,
				category:$scope.templateCategory.code,
				type:$scope.selectedTemplateType.code,
				text:$scope.templateDescription,
				attributes:$scope.templateAttributeUpdate
			}	
		
		console.log($scope.templateAttributeUpdate);
		
		$http({
			  method: 'POST',
			  url: AppUtil.restUrls.clmMetaData.updateTemplate,
			  data: $scope.templateUpdateObj,
			}).then(function success(response) {
				//console.log("resultAdd=",response.data);
				if(response.status=200){
					alert("Template Successfully updated!");
					window.location.reload();
					console.log("updatedRecords=",$scope.viewTemplateData);
					$rootScope.showFullScreenLoader = false;
					
				}else{
					$rootScope.showFullScreenLoader = false;
					console.log(response);					
				}
			}, function error(response) {
				  console.log("error:"+response);
			});
    };
});