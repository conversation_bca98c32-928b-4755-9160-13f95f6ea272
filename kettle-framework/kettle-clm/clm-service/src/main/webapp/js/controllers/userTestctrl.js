clmapp.controller("userTestctrl",function ($log, AuthService,$window, $cookieStore, $rootScope,$timeout, $scope, $http, $location, AppUtil)
 {
 
	$scope.init = function(){
		
		$scope.freeTextData = [];
		 $scope.injectedObject = {};
	}
	
	 $http({
			method: 'GET',
			  url: AppUtil.restUrls.clmMetaData.userView
			}).then(function success(response) {
				//console.log(response.data);
				$rootScope.showFullScreenLoader = false;
				$scope.userTestData=response.data;
				console.log($scope.userTestData);
				$scope.currentPage = 1; //current page
				$scope.entryLimit = 50; //max no of items to display in a page
				$scope.filteredItems = $scope.userTestData.length; //Initially for no filter  
				$scope.totalItems = $scope.userTestData.length;
				
			}, function error(response) {
				$rootScope.showFullScreenLoader = false;
				  console.log("error:"+response);
		});	
	
	 $scope.sort_by = function(predicate) {
	        $scope.predicate = predicate;
	        $scope.reverse = !$scope.reverse;
	    };
	    $scope.updatePerm = function(val){
	    	if(val==='true'){
	    		$scope.permanentAddressSame = true;
	    	}else{
	    		$scope.permanentAddressSame = false;
	    	}
	    }
	
	 
	 
	 $scope.addUserTest = function () {
	    	$scope.action="add";
	    	$scope.testUserName='';
			$scope.testUserContact='';
			$scope.testUserEmail='';
	    	 $("#userTestModal").modal("show");
	    }
	    
	  $scope.sbmtUserTest = function () {
	    	
		     $scope.userTestObj = {
		    		 name:$scope.testUserName,
		    		 contactNumber:$scope.testUserContact,
		    		 emailId:$scope.testUserEmail
				}
		     
		     $http({
				  method: 'POST',
				  url: AppUtil.restUrls.clmMetaData.userTestAdd,
				  data: $scope.userTestObj,
				}).then(function success(response) {
					console.log(response.data);
					if(response.status==200){
						$scope.userTestData.push(response.data);
						$rootScope.showFullScreenLoader = false;
						alert("User Test added successfully!");
						$("#userTestModal").modal("hide");
					}else{
						$rootScope.showFullScreenLoader = false;
						console.log(response);					
					}
				}, function error(response) {
					
					  console.log("error:"+response);
				});
		    }
	 
	  $scope.editUserTest =function(userTestId)
	    {
	    	$scope.action="edit";
	    	$scope.userTestID=userTestId;
	    	$scope.userTestData.forEach(function(userTestDetails){
	    		if(userTestDetails.id==userTestId){
	    			$scope.testUserName=userTestDetails.name;
	    			$scope.testUserContact=userTestDetails.contactNumber;
	    			$scope.testUserEmail=userTestDetails.emailId }
	    	});
	     $("#userTestModal").modal("show");
	    }
	 
	  $scope.updateUserTest=function(userTestIDs)
	    {
	    console.log(userTestIDs);
	    $scope.updateUserTestObj={
	    		id:userTestIDs,
	    		name:$scope.testUserName,
	    		contactNumber:$scope.testUserContact,
	    		emailId:$scope.testUserEmail
	    		
	    }
	    $http({
			  method: 'POST',
			  url: AppUtil.restUrls.clmMetaData.userTestUpdate,
			  data: $scope.updateUserTestObj,
			}).then(function success(response) {
				console.log(response.data);
				if(response.status==200){
					$rootScope.showFullScreenLoader = false;
					alert("User Test update successfully!");
					window.location.reload();
				}else{
					$rootScope.showFullScreenLoader = false;
					console.log(response);					
				}
			}, function error(response) {
				  console.log("error:"+response);
			});
	    }
	});