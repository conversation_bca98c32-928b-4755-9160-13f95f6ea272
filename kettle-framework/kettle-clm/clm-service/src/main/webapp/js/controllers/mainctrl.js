/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

clmapp.controller("MainController", function ($scope, $location, $http,$cookieStore, AuthService, AppUtil) {

    if ($location.path() == "/dashboard") {
        $location.path("dashboard/unit");
    }
    
    
    $scope.showIntent = function () {
        $location.path("dashboard/intent");
    }
    
    $scope.showCampaign = function () {
        $location.path("dashboard/campaign");
    }
    
    $scope.showTemplates = function (type) {
    	$scope.bulkSMSTypes=type;
        $location.path("dashboard/template");
    }
    $scope.showCreatTemplate = function () {
        $location.path("dashboard/createTemplate");
    }
    
    $scope.showBulkSmsPromotional = function () {
        $location.path("dashboard/bulkSmsPromo");
    }
    $scope.showBulkSmsTransactional = function () {
        $location.path("dashboard/bulkSmsTrans");
    }
    
    $scope.showCreatUser = function () {
        $location.path("dashboard/userTest");
    }
    
    
    
    $scope.logout = function () {
        $http({
            method: 'POST',
            url: AppUtil.restUrls.users.logout,
            data: $cookieStore.get('adminglobals')
        }).then(function success(response) {
            $cookieStore.remove('adminglobals');
            AuthService.setAuthorization(null);
            $location.path("/login");
        });
    };

});