/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

clmapp.controller("UnitDataController", function($rootScope,$scope,$window,$http,$location,$stateParams,AppUtil){
	$rootScope.showFullScreenLoader = true;
	
	$scope.init = function(){		
		$http({
		  method: 'GET',
		  url: AppUtil.restUrls.unitMetaData.allUnits+'?category='+$stateParams.category
		}).then(function success(response) {
			$scope.unitlist = response.data;
			$rootScope.showFullScreenLoader = false;
			
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			  console.log("error:"+response);
		});
		
		if($stateParams.category == 'DELIVERY'){
			//$scope.loading = false;
			$scope.unitCat = "COD";
		}else if($stateParams.category == 'COD'){
			//$scope.loading = false;
			$scope.unitCat = "Call Center";
		}else if($stateParams.category == 'categoryList'){
			$scope.unitCat = "categoryList";
		}else if ($stateParams.category == 'TAKE_AWAY'){
			$scope.unitCat = "Takeaway";
		}
		else if ($stateParams.category == 'KITCHEN'){
			$scope.unitCat = "KITCHEN";
		}
		else if ($stateParams.category == 'WAREHOUSE'){
			$scope.unitCat = "WAREHOUSE";
		}
		
		else if ($stateParams.category == 'OFFICE'){
			$scope.unitCat = "OFFICE";
		}
		else if ($stateParams.category == 'CHAI_MONK'){
			$scope.unitCat = "CHAI_MONK";
		}
		else{
			$scope.unitCat = "Cafe";
		}
	};
	
	
	$scope.getUnitData = function(id){
		$scope.unitIdData=id;
		$scope.isUnitDetailLoaded(id);
		if(!$scope.loaded){
			$rootScope.showFullScreenLoader = true;
			var flagAction=true;
			$http({
			  method: 'POST',
			  url: AppUtil.restUrls.unitMetaData.unit,
			  data: id
			}).then(function success(response) {
				$scope.UnitResponseData=response.data;
				$http({
					  method: 'POST',
					  url: AppUtil.restUrls.unitMetaData.viewUnitDeliveryPartners,
					  data: response.data.id
					}).then(function success(response) {
						console.log(response.data);
						//setDetailInUnit(id,response.data);
						$scope.unitDeliveryData=response.data;
						$scope.UnitResponseData.deliveryPartners=response.data;
						setDetailInUnit(id,$scope.UnitResponseData);
						$scope.unitsDet=$scope.UnitResponseData;
						$rootScope.showFullScreenLoader = false;
					},  function error(response) {
						  console.log("error:"+response);
					});	
			});	
		}	
		}	
	
	function setDetailInUnit(unitId, unitDetail){
		$scope.unitlist.forEach(function(unit,index){
			if(unit.id === unitId){
				$scope.unitlist[index].unitDetail = unitDetail;
			}
		});
	}
	$scope.isUnitDetailLoaded = function(unitId){
		$scope.unitlist.forEach(function(unit){
			if(unit.id === unitId){
				if(angular.isUndefined(unit.unitDetail) || unit.unitDetail==null){
					$scope.loaded = false;
				}else{
					$scope.loaded = true;
					}
			}
		});
	}
	$scope.activateUnit = function(unitId){
		$http({
		  method: 'POST',
		  url: AppUtil.restUrls.unitMetaData.activateUnit,
		  data: unitId
		}).then(function success(response) {
			if(response.status==200){
				$scope.unitlist.forEach(function(unit,index){
					if(unit.id===unitId){
						unit.status = "ACTIVE";
						//$scope.unitlist.splice(index,1,response);
						alert("Unit activated successfully!");
						//window.location.reload();
					}
				});
			}
		}, function error(response) {
			  console.log("error:"+response);
		});
	}
	$scope.deactivateUnit = function(unitId){
		$rootScope.showFullScreenLoader = true;
		$http({
		  method: 'POST',
		  url: AppUtil.restUrls.unitMetaData.deactivateUnit,
		  data: unitId
		}).then(function success(response) {
			if(response.status==200){
				$rootScope.showFullScreenLoader = false;
				$scope.unitlist.forEach(function(unit,index){
					if(unit.id===unitId){
						unit.status = "IN_ACTIVE";
						//$scope.unitlist.splice(index,1,response);
						alert("Unit deactivated successfully!");
						//window.location.reload();
						
					}
				});
			}
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			  console.log("error:"+response);
		});
	}
	$scope.editUnit = function(unit){
	console.log(unit);
	$rootScope.$broadcast("editUnit", {unitToEdit: unit });
	};
	
	
	console.log("UnitID=",$rootScope.unitToEdit)
	
	$scope.editProductDetailModal = function(unitID,productID,productName,prices){
	//console.log(products);
	$scope.producttNamee=productName;
	$scope.unitIDD=unitID;
	$scope.productIDD=productID;
	$scope.productPrices=prices;
	 for(var index in $scope.productPrices){
		 $scope.productPrices[index].newprice="";
	 }
	$scope.filteredProducts = $scope.productPrices.length;
	$scope.productName=productName;
	$("#productUnitDetailModal").modal("show");
	$scope.count = 0;
	};	
	$scope.cancelPrice = function()
	{
		$("#productUnitDetailModal").hide();
	}
	$scope.inActiveProduct = function(unitID,productID){
		var inactiveProductString = $.param({ unitId: unitID,
			productId: productID});
			$http({
				url: AppUtil.restUrls.productMetaData.deactivateProductMapping,
					  method: "POST",
					  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
					  data: inactiveProductString
					}).success(function(data) {
						console.log(data);
						alert("Status Successfully updated");
						window.location.reload();
			}); 
	}	
	$scope.activeProduct = function(unitID,productID){
		var activeProductString = $.param({ unitId: unitID,
			productId: productID});
		
		if ($window.confirm("Are you sure, you want to activate/deactivate Product item?")) {
		} else {
	    	return false;
	    }
		
		
			$http({
				url: AppUtil.restUrls.productMetaData.activateProductMapping,
					 method: "POST",
					 headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
					 data: activeProductString
					}).success(function(data) {
						//console.log(data);
					alert("Status Successfully updated");
					window.location.reload();
			}); 
	}	
	$scope.updateProductPriceUnit = function(unitIDD,productID,products,productName)
	{
		//console.log($scope.unitsDet);
		for(var index in products){
			if(products[index].newprice!="")
				{
				var newprice = products[index].newprice;
				var dimensionList = products[index].dimension;
				var payload = $.param({ unitId: unitIDD,
					productId:$scope.newUnitRegion,
					productId: productID,
					price: newprice,
					dimensionCode: dimensionList});
					$http({
					url: AppUtil.restUrls.productMetaData.unitProductPriceUpdate,
					  method: "POST",
					  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
					  data: payload
					}).success(function(data) {
						alert(productName+ "dimension price updated");
						window.location.reload();
					});
				}
		}	
	}
});