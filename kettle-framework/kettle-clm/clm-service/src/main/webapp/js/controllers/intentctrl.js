/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

clmapp.controller("intentctrl", function ($log, AuthService,$window, $cookieStore, $rootScope,$timeout, $scope, $http, $location, AppUtil) {

    $scope.init = function () {
       console.log("hello"); 
    }
    
   
    $scope.addIntent = function () {
    	$scope.action="add";
    	$scope.intentName='';
		$scope.intentDescription='';
    	 $("#intentModal").modal("show");
    }
    
    $http({
		method: 'GET',
		  url: AppUtil.restUrls.clmMetaData.intentView
		}).then(function success(response) {
			console.log(response.data);
			$rootScope.showFullScreenLoader = false;
			$scope.intentData=response.data;
			$scope.currentPage = 1; //current page
			$scope.entryLimit = 50; //max no of items to display in a page
			$scope.filteredItems = $scope.intentData.length; //Initially for no filter  
			$scope.totalItems = $scope.intentData.length;
			
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			  console.log("error:"+response);
	});
    
    $scope.sort_by = function(predicate) {
        $scope.predicate = predicate;
        $scope.reverse = !$scope.reverse;
    };
    $scope.updatePerm = function(val){
    	if(val==='true'){
    		$scope.permanentAddressSame = true;
    	}else{
    		$scope.permanentAddressSame = false;
    	}
    }
    $scope.sbmtIntent = function () {
    	
     $scope.intentObj = {
			name:$scope.intentName,
			description:$scope.intentDescription
		}
     
     $http({
		  method: 'POST',
		  url: AppUtil.restUrls.clmMetaData.intentAdd,
		  data: $scope.intentObj,
		}).then(function success(response) {
			console.log(response.data);
			if(response.status==200){
				$rootScope.showFullScreenLoader = false;
				alert("Intent added successfully!");
			}else{
				$rootScope.showFullScreenLoader = false;
				console.log(response);					
			}
		}, function error(response) {
			
			  console.log("error:"+response);
		});
    }
    
    clmapp.filter('startFrom', function() {
        return function(input, start) {
            if(input) {
                start = +start; //parse to int
                return input.slice(start);
            }
            return [];
        };
    });   
    
    $scope.editIntent =function(intentId)
    {
    	$scope.action="edit";
    	$scope.intentData.forEach(function(intentDetails){
    		if(intentDetails.id==intentId){
    			$scope.intentName=intentDetails.name;
    			$scope.intentDescription=intentDetails.description;
    			$scope.intentID=intentDetails.id;
    		}
    	});
     $("#intentModal").modal("show");
    }
    
    $scope.updateIntent=function(intentID)
    {
    console.log(intentID);
    $scope.updateIntentObj={
    		id:intentID,
    		name:$scope.intentName,
    		description:$scope.intentDescription
    }
    $http({
		  method: 'POST',
		  url: AppUtil.restUrls.clmMetaData.intentUpdate,
		  data: $scope.updateIntentObj,
		}).then(function success(response) {
			console.log(response.data);
			if(response.status==200){
				$rootScope.showFullScreenLoader = false;
				alert("Intent update successfully!");
				window.location.reload();
			}else{
				$rootScope.showFullScreenLoader = false;
				console.log(response);					
			}
		}, function error(response) {
			
			  console.log("error:"+response);
		});
    }
});

