clmapp.controller("bulkSmsTransctrl",function ($log, AuthService,$window, $cookieStore, $rootScope,$timeout, $scope, $http, $location, AppUtil)
 {
 
$http({
	method: 'GET',
	  url: AppUtil.restUrls.clmMetaData.campaignView
	}).then(function success(response) {
		//console.log(response.data);
		$rootScope.showFullScreenLoader = false;
		$scope.campaignData=response.data;
		$scope.selectedCampaignType=$scope.campaignData[0];
	}, function error(response) {
		$rootScope.showFullScreenLoader = false;
		  console.log("error:"+response);
});

});