clmapp.controller("templatectrl",function ($log, AuthService,$window, $cookieStore, $rootScope,$timeout, $scope, $http, $location, AppUtil)
 {
 
	$scope.init = function(){
		
		$scope.freeTextData = [];
		$scope.templateDescription = [];
		//$scope.selectTemplatesFileData=[];
		 $scope.injectedObject = {};
	}
	
	$scope.templateType = [{code: "SMS", name:"SMS"}, {code: "EMAIL", name:"EMAIL"}];
    $scope.selectedTemplateType=$scope.templateType[0];
    $scope.templateCategory = [{code: "PROMOTIONAL", name:"PROMOTION<PERSON>"}, {code: "TRANSACTIONAL", name:"T<PERSON><PERSON><PERSON><PERSON>ON<PERSON>"}];
    $scope.selectedCategoryType=$scope.templateCategory[0];
    
    $scope.templateFile = [{code: "FILE", name:"FILE"}, {code: "TEXT", name:"TEXT"}];
    $scope.selectTemplatesFileData=$scope.templateFile[0];
    
    $scope.serviceProviderList = [{code: "SMS_GUPSHUP", name:"SMS_GUPSHUP"}, {code: "SOLN_INFINI", name:"SOLN_INFINI"}];
    $scope.selectTemplatesFileData=$scope.templateFile[0];
    
    $scope.test = "Das ist ein Test";
    $scope.colors = [{name: 'FRAHEEM',number: '**********'}];
    
	$scope.addTemplate = function () {
    	$scope.action="add";
    	$scope.intentName='';
		$scope.intentDescription='';
    	$("#templateModal").modal("show");
    }
	
	$http({
		method: 'GET',
		  url: AppUtil.restUrls.clmMetaData.viewTemplate
		}).then(function success(response) {
			console.log(response.data);
			$rootScope.showFullScreenLoader = false;
			$scope.viewTemplateData=response.data;
			$scope.selectTemplatesNameData=$scope.viewTemplateData[0];
			
			
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			  console.log("error:"+response);
	});
	
	$http({
		method: 'GET',
		  url: AppUtil.restUrls.clmMetaData.userView
		}).then(function success(response) {
			//console.log(response.data);
			$rootScope.showFullScreenLoader = false;
			$scope.userTestData=response.data;
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			  console.log("error:"+response);
	});	
    
	
	/*$http({
		  method: 'POST',
		  url: AppUtil.restUrls.clmMetaData.templateServiceProvider,
		  data: $scope.intentObj,
		}).then(function success(response) {
			console.log(response.data);
			if(response.statusText==="OK"){
				$rootScope.showFullScreenLoader = false;
				alert("Intent added successfully!");
			}else{
				$rootScope.showFullScreenLoader = false;
				console.log(response);					
			}
		}, function error(response) {
			
			  console.log("error:"+response);
		});
	*/
	

	$scope.showTemplateDetails=function(val,indexes,nameValue)
	{
		if(val.name=='TEXT')
			{
			$("#test"+nameValue).show();
			}
		else
			{
			$("#test"+nameValue).hide();
			}
	}
	
	
	$scope.backSMSTemplate=function() 
	{
		$("#smsTestTemplateDiv").hide();
		$("#mainTemplateDiv").show();
		$("#templateStructureDiv").show();
	}
	
	$scope.previewSmsTemplate=function()
	{
		$scope.fullCustomerObj=[];
		$scope.templateAttributeValue;
		console.log("11",$scope.templateAttributeValue);
			$scope.templateAttributeValue.forEach(function(selectTemplateAttribute)
			{
				//console.log("22=",selectTemplateAttribute);
				$scope.fullCustomerObj.push({key:selectTemplateAttribute,value:$scope.freeTextData[selectTemplateAttribute]});
			});
			
			//console.log($scope.fullCustomerObj);
			$scope.fullCustomerObj.forEach(function(custObj){
				//console.log("hello=",custObj);
				var orignalVal='$'+custObj.key+'$';
				$scope.selectParseTemplate= $scope.selectParseTemplate.replace(orignalVal, custObj.value);
				//console.log($scope.selectParseTemplate);
			});
			
		$("#smsPreviewModal").modal("show");
		var selectedNumberLists = [];
		$scope.testingSmsNumber =	$scope.selectedColorsResult;
		$scope.selectedColors.forEach(function(selectedNumbers){
			//console.log(selectedNumbers.number);
			selectedNumberLists.push(selectedNumbers.contactNumber);
			//$scope.fullSubCatObj.push({id:subType.id,check:$scope.subCatObj[subType.id],subCategoryName:subType.name});
		});
		
		$scope.testingSmsNumber=selectedNumberLists;
		//console.log($scope.selectParseTemplate);
		$scope.testingSmsDetails=$scope.selectParseTemplate;
	}
	
	$scope.submitSendSMS=function ()
	{
		$scope.newContact=[];
		$scope.newText=[];
		$("#smsPreviewModal").hide();
		$("#smsTestTemplateDiv").hide();
		$("#smsTestTemplateDiv").hide();
		$("#smsPreviewModal").modal("hide");
		$("#fileUploadDiv").show();
		$("#smsFileUploadMainDiv").show();
		
		$scope.newContact='contact';
		$scope.newText='textSms';
		$scope.templateAttributeValue.unshift($scope.newText);
		$scope.templateAttributeValue.unshift($scope.newContact);
				
		//console.log("shouldbe done=",$scope.templateAttributeValue)
		
		/*$scope.testingSmsNumber=$scope.testingSmsNumber.toString();
		var smsObj={
				campaignType:"PROMOTIONAL",
				serviceProvider:"SOLUTION_INFINI",
				contactNumber:$scope.testingSmsNumber,
				message:$scope.testingSmsDetails
		};
		$http({
		  method: 'POST',
		  url: AppUtil.restUrls.clmMetaData.sendSMS,
		  data: smsObj,
		}).then(function success(response) {
			console.log(response.data);
			if(response.statusText==="OK"){
				$rootScope.showFullScreenLoader = false;
				alert("Intent added successfully!");
			}else{
				$rootScope.showFullScreenLoader = false;
				console.log(response);					
			}
		}, function error(response) {
			  console.log("error:"+response);
		});*/
	}
	
	$scope.changeUserText=function(fileContentData,mobileNo)
	{
		$("#smsUpdateModal").modal("show");
		//console.log(fileContentData);
		//console.log(mobileNo);
		fileContentData.forEach(function(fileContentResult)
				{
				//console.log(fileContentResult);		
				if(fileContentResult.contact==mobileNo)
					{
					$scope.updateSmsDetailsData=fileContentResult.testSms;
					$scope.updateMobileNumber=fileContentResult.contact;
					}
				});
	}
	
	
	$scope.updateSMS=function(smsMobileNumber)
	{
		//console.log("111=",smsMobileNumber);
		
		///console.log("3333=",$scope.selectParseTemplate);
		$scope.fileContent.forEach(function(fileContentChangeSMS)
				{
			//console.log("222=",fileContentChangeSMS);
			
				if(fileContentChangeSMS.contact==smsMobileNumber)
					{
					fileContentChangeSMS.testSms=$scope.updateSmsDetailsData;
					$("#smsUpdateModal").modal("hide");
					}
				});
	}
			
	
	$scope.parseTemplate = function () {
		$scope.selectParseTemplate="";
		
		
		$scope.selectParseTemplate=$scope.selectTemplatesNameData.text;
		
		$("#templateStructureDiv").show();
		$scope.templateAttributeValue=[];
			var re= /\$(.*?)\$/g;
			
			//selectTemplatesNameData
			
			for(m = re.exec($scope.selectTemplatesNameData.text); m; m = re.exec($scope.selectTemplatesNameData.text)){
			   // alert(m[1])
			    $scope.templateAttributeValue.push(m[1]);
			}
			console.log($scope.templateAttributeValue);
		};
		
		$scope.sbmtTemplates=function() {
			
			$scope.objTemplateAttribute=[];
			
			$scope.templateAttributeValue.forEach(function(selectTemplateAttribute)
					{
						console.log("22=",selectTemplateAttribute);
						$scope.objTemplateAttribute.push({key:selectTemplateAttribute,value:$scope.templateDescription[selectTemplateAttribute]});
					});
			
			console.log("HERS IS ",$scope.objTemplateAttribute);
			
			/*$scope.objTemplateAttribute.forEach(function(textDescription)
					{
						if(textDescription.value!="")
							{
							$scope.freeTextData.push($scope.templateDescription[selectTemplateAttribute]);
					
							}
							});*/
			
			
			$("#mainTemplateDiv").hide();
			$("#smsTestTemplateDiv").show();
		};	
		
			$scope.checkAll = function () {
			$scope.fileContent.forEach(function(fileContentChangeSMS)
					{
						fileContentChangeSMS.checkValue=$scope.allChecked=='YES';
					});
			//console.log($scope.fileContent);
		};
		
		$scope.finalSmsListSend = function () {
			
			console.log($scope.fileContent);
		};
		
		$scope.downloadExcelFormat=function() {
			//alert("test197");
			console.log("ss---->",$scope.templateAttributeValue);
			//$("#mainTemplateDiv").hide();
			
			
			/*function myFunction() {
			    var table = document.getElementById("myTable");
			    var row = table.insertRow(0);
			    var cell1 = row.insertCell(0);
			    var cell2 = row.insertCell(1);
			    cell1.innerHTML = "NEW CELL1";
			    cell2.innerHTML = "NEW CELL2";
			}*/
			
			var blob = new Blob([document.getElementById('exportable').innerHTML], {
	            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
	        });
	        saveAs(blob, "Report.xls");
			
			$("#smsPreviewModalLableTitle").show();
		};
		
		console.log("testing=",$scope.injectedObject);
		
 	});

clmapp.filter('ceil', function() {
	  return function(input) {
	    return Math.ceil(input);
	  };
	});

clmapp.directive('exportToCsv',function(){
  	return {
    	restrict: 'A',
    	link: function (scope, element, attrs) {
    		var el = element[0];
	        element.bind('click', function(e){
	        	//var table = e.target.nextElementSibling;
	        	var table = document.getElementById('myTable');  
	        	var csvString = '';
	        	for(var i=0; i<table.rows.length;i++){
	        		var rowData = table.rows[i].cells;
	        		for(var j=0; j<rowData.length;j++){
	        			csvString = csvString+rowData[j].innerHTML+",";
	        		}
	        		console.log(csvString);
	        		csvString = csvString.substring(0,csvString.length - 1);
	        		csvString = csvString + "\n";
			    }
	         	csvString = csvString.substring(0, csvString.length - 1);
	         	var a = $('<a/>', {
		            style:'display:none',
		            href:'data:application/octet-stream;base64,'+btoa(csvString),
		            download:'smsFormat.csv'
		        }).appendTo('body')
		        a[0].click()
		        a.remove();
	        });
    	}
  	}
  	$("#smsPreviewModalLableTitle").show();
	});

clmapp.directive('fileReader', function() {
return {
	    scope: {
	      fileReader:"="
	    	  //fileReader:"&"
	    }, 
	    link: function(scope, element) {
	      $(element).on('change', function(changeEvent) {
	    	var id = $(element).parent('span').attr('id'); // dvPhoneNoCust
	    	$('#textSmsGridDiv').show();
	        var files = changeEvent.target.files;  			// file config size etc
	        if (files.length) {
	          var r = new FileReader();
	          scope.newArrayData=[];
	          r.onload = function(e) {
	           var contents = e.target.result;			// whole csv file contents
	                scope.$apply(function () {
	                	
	                	
	            	scope.fileReader = CSV2JSON(contents);  // all csv json object 
	            	console.log("fileHeresss111=",scope.fileReader); 
	            	
	            	scope.newArrayData=scope.fileReader
	                scope.fileReader.forEach(function(fReader){
	                	//console.log("ss=",);
	                	
	                	if(fReader.contact!=""){
	                		console.log("fff=",fReader.contact);
	                scope.fullAttributeObj=[];           	
	     			var re= /\$(.*?)\$/g;
	     			var contentAttributeData=[];
	     			for(m = re.exec(fReader.testSms); 
	     			m; m = re.exec(fReader.testSms)){
	     				contentAttributeData.push(m[1]);
	     			}
	     		
	     			contentAttributeData.forEach(function(contentAttributeResultShow){
	     				scope.fullAttributeObj.push({key:contentAttributeResultShow,value:fReader[contentAttributeResultShow]});
	     			});
	     			scope.fullAttributeObj.forEach(function(custObj){
	    				var orignalVal='$'+custObj.key+'$';
	    				fReader.testSms= fReader.testSms.replace(orignalVal, custObj.value);
	    				
	    			});
	                }
	                });
	            		
	            	
	                });
	          };
	          r.readAsText(files[0]);
	        }
	      });
	    }
	  };
	  
	  function CSVToArray(strData, strDelimiter) {
		    // Check to see if the delimiter is defined. If not,
		    // then default to comma.
		    strDelimiter = (strDelimiter || ",");
		    // Create a regular expression to parse the CSV values.
		    var objPattern = new RegExp((
		    // Delimiters.
		    "(\\" + strDelimiter + "|\\r?\\n|\\r|^)" +
		    // Quoted fields.
		    "(?:\"([^\"]*(?:\"\"[^\"]*)*)\"|" +
		    // Standard fields.
		    "([^\"\\" + strDelimiter + "\\r\\n]*))"), "gi");
		    // Create an array to hold our data. Give the array
		    // a default empty first row.
		    var arrData = [[]];
		    // Create an array to hold our individual pattern
		    // matching groups.
		    var arrMatches = null;
		    // Keep looping over the regular expression matches
		    // until we can no longer find a match.
		    while (arrMatches = objPattern.exec(strData)) {
		        // Get the delimiter that was found.
		        var strMatchedDelimiter = arrMatches[1];
		        // Check to see if the given delimiter has a length
		        // (is not the start of string) and if it matches
		        // field delimiter. If id does not, then we know
		        // that this delimiter is a row delimiter.
		        if (strMatchedDelimiter.length && (strMatchedDelimiter != strDelimiter)) {
		            // Since we have reached a new row of data,
		            // add an empty row to our data array.
		            arrData.push([]);
		        }
		        // Now that we have our delimiter out of the way,
		        // let's check to see which kind of value we
		        // captured (quoted or unquoted).
		        if (arrMatches[2]) {
		            // We found a quoted value. When we capture
		            // this value, unescape any double quotes.
		            var strMatchedValue = arrMatches[2].replace(
		            new RegExp("\"\"", "g"), "\"");
		        } else {
		            // We found a non-quoted value.
		            var strMatchedValue = arrMatches[3];
		        }
		        // Now that we have our value string, let's add
		        // it to the data array.
		        arrData[arrData.length - 1].push(strMatchedValue);
		    }
		    // Return the parsed data.
		    return (arrData);
		}

		function CSV2JSON(csv) {
			//console.log("helllo=",csv);
		    var array = CSVToArray(csv);
		    var objArray = [];
		    //console.log("dd=",array);
		    array.pop();
		    for (var i = 1; i < array.length; i++) {
		        objArray[i - 1] = {};
		        for (var k = 0; k < array[0].length && k < array[i].length; k++) {
		            var key = array[0][k];
		            objArray[i - 1][key] = array[i][k]
		        }
		    }
		    var json = JSON.stringify(objArray);
		    var str = json.replace(/},/g,"},\r\n");
		   // console.log("fraheemAkhtar=",str);
		   return JSON.parse(str);
		}
	});

	