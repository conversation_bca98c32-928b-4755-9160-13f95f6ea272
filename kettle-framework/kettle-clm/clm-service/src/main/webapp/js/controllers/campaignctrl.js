/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

clmapp.controller("campaignctrl", function ($log, AuthService,$window, $cookieStore, $rootScope,$timeout, $scope, $http, $location, AppUtil) {

    $scope.init = function () {
       console.log("hello"); 
    }
    
    $scope.campaignTypeLists = [{code: "PROMOTIONAL", name:"PROMOTIONAL"}, {code: "TRANSACTIONAL", name:"TRANSACTIONAL"}];
    $scope.campaignType=$scope.campaignTypeLists[0];
    
    $http({
		method: 'GET',
		  url: AppUtil.restUrls.clmMetaData.campaignView
		}).then(function success(response) {
			console.log(response.data);
			$rootScope.showFullScreenLoader = false;
			$scope.campaignData=response.data;
			$scope.currentPage = 1; //current page
			$scope.entryLimit = 50; //max no of items to display in a page
			$scope.filteredItems = $scope.campaignData.length; //Initially for no filter  
			$scope.totalItems = $scope.campaignData.length;
			//$scope.selectedIntentType=$scope.intentData[0];
			
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			  console.log("error:"+response);
	});
    
    $http({
		method: 'GET',
		  url: AppUtil.restUrls.clmMetaData.intentView
		}).then(function success(response) {
			console.log(response.data);
			$rootScope.showFullScreenLoader = false;
			$scope.intentData=response.data;
			$scope.selectedIntentType=$scope.intentData[0];
			
		}, function error(response) {
			$rootScope.showFullScreenLoader = false;
			  console.log("error:"+response);
	});
    
    
    
    $scope.addCampaign=function()
    {
    	$scope.action="add";
    	$scope.campaignName='';
		$scope.intentDescription='';
    	$("#campaignModal").modal("show");
    }
    
    
    $scope.updateCampaign=function(campaginId)
    {
   // console.log(intentID);
    
    $scope.updateCampaignObj = {
    		id:campaginId,
			name:$scope.campaignName,
			type:$scope.campaignType.name,
			status:"IN_ACTIVE",
			intent:$scope.selectedIntentType,
			description:$scope.campaignDescription,
		}
    
    console.log($scope.updateCampaignObj);
    
    $http({
		  method: 'POST',
		  url: AppUtil.restUrls.clmMetaData.campaignUpdate,
		  data: $scope.updateCampaignObj,
		}).then(function success(response) {
			console.log(response.data);
			if(response.status==200){
				$rootScope.showFullScreenLoader = false;
				alert("Campaign update successfully!");
				window.location.reload();
			}else{
				$rootScope.showFullScreenLoader = false;
				console.log(response);					
			}
		}, function error(response) {
			
			  console.log("error:"+response);
		});
    }
    
    
    $scope.editCampaign =function(campaignId)
    {
    	$scope.action="edit";
    	$scope.campaignData.forEach(function(campaignDetails){
    		if(campaignDetails.id==campaignId){
    				$scope.campaignName			=	campaignDetails.name;
	    			$scope.campaignDescription	=	campaignDetails.description;
	    			$scope.campaignID			=	campaignId;
	    			
    				var selectedCampaignTypeStatus 	= $scope.campaignTypeLists.filter(function(campaignDataType){
    				return campaignDataType.name == campaignDetails.type;
    				});
    				var indexOfCampaignStatus 		= $scope.campaignTypeLists.indexOf(selectedCampaignTypeStatus[0]);
    				$scope.campaignType 			= $scope.campaignTypeLists[indexOfCampaignStatus];
    				
    				var selectedIntentTypeStatus	=	$scope.intentData.filter(function(intentDataStructure){
    				return intentDataStructure.name	=	campaignDetails.intent.name;
    				
    				});
    				//console.log("here==",selectedIntentTypeStatus[0]);
    				
    				var indexOfIntentStatus 		= 	$scope.intentData.indexOf(selectedIntentTypeStatus[0]);
    				
    				console.log("ppp=",indexOfIntentStatus);
    				
    				console.log("ppp=",$scope.intentData[indexOfIntentStatus]);
    				
    				$scope.selectedIntentType 		= 	$scope.intentData[indexOfIntentStatus];
    			
    		}
    	});
     $("#campaignModal").modal("show");
    }
    
   
    $scope.sbmtCampaign=function()
    {
    	$scope.campaignObj = {
    			name:$scope.campaignName,
    			type:$scope.campaignType.name,
    			intent:$scope.selectedIntentType,
    			status:'IN_ACTIVE',
    			description:$scope.campaignDescription,
    		}
    	console.log($scope.campaignObj);
    	 $http({
   		  method: 'POST',
   		  url: AppUtil.restUrls.clmMetaData.campaignAdd,
   		  data:$scope.campaignObj,
   		}).then(function success(response) {
   			console.log(response.data);
   			if(response.status==200){
   				$rootScope.showFullScreenLoader = false;
   				alert("campaign added successfully!");
   			}else{
   				$rootScope.showFullScreenLoader = false;
   				console.log(response);					
   			}
   		}, function error(response) {
   			  console.log("error:"+response);
   		});
    }
});

