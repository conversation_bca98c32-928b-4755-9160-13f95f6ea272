/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

@charset "utf-8";
/* CSS Document */

*{
	outline:none;
}
.nav, .pagination, .carousel, .panel-title a { cursor: pointer; outline:none; }
.accordion-toggle{
	display:block;
	padding:10px 15px;
}

.panel-group .panel-heading{
	padding:0;
}

.panel-group .panel-title button{
	background:none;
	padding:10px 15px;
	display:block;
	border:none;
	width:100%;
	text-align:left;
}

#employeeModal .ng-invalid.ng-dirty{
	border-color:#F00;
}

#employeeModal .ng-valid.ng-dirty{
	border-color:#060;
}

/***************** login page style *********************/
.loginBack{
	background:url(../img/110.jpg) center no-repeat;
	background-size:cover;	
    position: fixed;
    width: 100%;
    height: 100%;
}

.responseBox{
	width:400px;
	margin:auto;
	margin-top:100px;
	height:50px;
}

.alert-success {
    color: #FFFFFF;
    background-color: #237900;
    border-color: #1E4200;
}

.loginBox{
	width:400px; 
	margin:auto;
	border-radius:2px;
	border:#DDD 1px solid; 
	box-shadow:#CCC 0 0 8px 0; 
	padding:20px;
	text-align:center;
	background:rgba(255,255,255,.9);
}

.loginDisabled{
	cursor:no-drop;
	pointer-events:none;
}

.responseBox .alert{
	padding:10px;
}



/***************** unit page styles *********************/
.title{
	font-size:24px;
}

.nav-tabs li a{
	pointer-events:none;
}

.nav-tabs li.disabled{
	cursor:no-drop;
}

.redBg{
	background-color:#FF0000 !important;
}
.error{
	color:#FF0000;
}




.fullScreenLoader{
    position: fixed;
    top:0;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    background: rgba(255,255,255,.8);
    z-index: 99999999999999;
}

.fullScreenLoader img{
    margin-top: 10%;
}

.hidden{display:none;}
