DROP SCHEMA IF EXISTS KETTLE_CLM_DEV;
CREATE SCHEMA KETTLE_CLM_DEV;

DROP TABLE IF EXISTS KETTLE_CLM_DEV.CAMPAIGN_DETAIL ;
CREATE TABLE KETTLE_CLM_DEV.CAMPAIGN_DETAIL (
    CAMPAIGN_ID INT NOT NULL AUTO_INCREMENT,
    CAMPAIGN_NAME VARCHAR(20) NOT NULL,
    CAMPAIGN_TYPE VARCHAR(20) NOT NULL,
    CAMPAIGN_DESCRIPTION VARCHAR(255) NOT NULL,
    CAMPAIGN_STATUS VARCHAR(10) NOT NULL,
    INTENT_ID INT NOT NULL,
    PRIMARY KEY (CAMPAIGN_ID)
);
DROP TABLE IF EXISTS KETTLE_CLM_DEV.CAMPAIGN_EVENT ;
CREATE TABLE KETTLE_CLM_DEV.CAMPAIGN_EVENT (
    CAMPAIGN_ID INT NOT NULL AUTO_INCREMENT,
    CREATION_TIME TIMESTAMP,
    TOTAL_SMS INT NOT NULL,
    TOTAL_SMS_COST INT NOT NULL,
    TOTAL_SMS_SENT INT NOT NULL,
    TOTAL_EMAIL_SENT INT NOT NULL,
    TOTAL_COST DECIMAL(10 , 2 ) NOT NULL,
    CREATED_BY INTEGER NOT NULL,
    CREATED_BY_NAME VARCHAR(20) NOT NULL,
    PRIMARY KEY (CAMPAIGN_ID)
);

DROP TABLE IF EXISTS KETTLE_CLM_DEV.CAMPAIGN_TARGET ;
CREATE TABLE KETTLE_CLM_DEV.CAMPAIGN_TARGET (
    TARGET_ID INT NOT NULL AUTO_INCREMENT,
    TARGET_NAME VARCHAR(20) NOT NULL,
    CONTACT_NUMBER VARCHAR(10) NOT NULL,
    EMAIL_ID VARCHAR(20) NOT NULL,
    TARGET_TEXT VARCHAR(255) NOT NULL,
    SMS_LENGTH INT NOT NULL,
    SMS_COUNT INT NOT NULL,
    CAMPAIGN_TYPE VARCHAR(20) NOT NULL,
    NOTIFICATION_TYPE VARCHAR(20) NOT NULL,
    DELIVERY_STATUS VARCHAR(10) NOT NULL,
    SERVICE_PROVIDER VARCHAR(20) NOT NULL,
    CUSTOMER_ID INT NOT NULL,
    NOTIFICATION_DATE TIMESTAMP,
    CAMPAIGN_EVENT_ID INT NOT NULL,
    PRIMARY KEY (TARGET_ID)
);

DROP TABLE IF EXISTS KETTLE_CLM_DEV.INTENT_DETAIL ;
CREATE TABLE KETTLE_CLM_DEV.INTENT_DETAIL (
    INTENT_ID INT NOT NULL AUTO_INCREMENT,
    INTENT_NAME VARCHAR(20) NOT NULL,
    INTENT_DESCRIPTION VARCHAR(255) NOT NULL,
    PRIMARY KEY (INTENT_ID)
);

DROP TABLE IF EXISTS KETTLE_CLM_DEV.SERVICE_PROVIDER ;
CREATE TABLE KETTLE_CLM_DEV.SERVICE_PROVIDER (
    PROVIDER_ID INT NOT NULL AUTO_INCREMENT,
    PROVIDER_NAME VARCHAR(20) NOT NULL,
    PROVIDER_CODE VARCHAR(20) NOT NULL,
    PROVIDER_URL VARCHAR(255),
    PROVIDER_TOPUP_URL VARCHAR(255),
    PRIMARY KEY (PROVIDER_ID)
);

DROP TABLE IF EXISTS KETTLE_CLM_DEV.TEMPLATE_DETAIL ;
CREATE TABLE KETTLE_CLM_DEV.TEMPLATE_DETAIL (
    TEMPLATE_ID INT NOT NULL AUTO_INCREMENT,
    TEMPLATE_NAME VARCHAR(255) NOT NULL,
    TEMPLATE_TEXT VARCHAR(255) NOT NULL,
    TEMPLATE_TYPE VARCHAR(20) NOT NULL,
    TEMPLATE_CATEGORY VARCHAR(20) NOT NULL,
    PRIMARY KEY (TEMPLATE_ID)
);

DROP TABLE IF EXISTS KETTLE_CLM_DEV.TEMPLATE_ATTRIBUTE ;
CREATE TABLE KETTLE_CLM_DEV.TEMPLATE_ATTRIBUTE (
    ATTRIBUTE_ID INT NOT NULL AUTO_INCREMENT,
    ATTRIBUTE_NAME VARCHAR(20) NOT NULL,
    ATTRIBUTE_TYPE VARCHAR(20) NOT NULL,
    ATTRIBUTE_VALUE VARCHAR(20) NOT NULL,
    TEMPLATE_ID INT NOT NULL,
    PRIMARY KEY (ATTRIBUTE_ID)
);

DROP TABLE IF EXISTS KETTLE_CLM_DEV.TEST_USER ;
CREATE TABLE KETTLE_CLM_DEV.TEST_USER (
    TEST_USER_ID INT NOT NULL AUTO_INCREMENT,
    CONTACT_NUMBER VARCHAR(10) NOT NULL,
    EMAIL_ID VARCHAR(20) NOT NULL,
    TEST_USER_NAME VARCHAR(20) NOT NULL,
    PRIMARY KEY (TEST_USER_ID)
);

