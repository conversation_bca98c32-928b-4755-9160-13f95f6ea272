package com.stpl.tech.clm.core.service.impl;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;


@Service
public class ClmEnvironmentProperties {

	@Autowired
	private Environment env;

	public EnvType getEnvironmentType() {
		return EnvType.valueOf(env.getProperty("environment.type"));
	}

	public String getBasePath() {
		return env.getProperty("server.base.dir");
	}
	public boolean publishToPusher(){
		return Boolean.valueOf(env.getProperty("publish.to.pusher"));
	}
	
}
