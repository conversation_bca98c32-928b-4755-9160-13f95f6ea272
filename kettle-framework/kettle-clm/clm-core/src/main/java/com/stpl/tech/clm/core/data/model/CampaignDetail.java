/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.clm.core.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "CAMPAIGN_DETAIL")
public class CampaignDetail implements java.io.Serializable {

	private Integer id;
	private String name;
	private String type;
	private String description;
	private String status;
	private IntentDetail intent;

	public CampaignDetail() {
	}

	@Id
	@GeneratedValue(strategy =  GenerationType.IDENTITY)
	@Column(name = "CAMPAIGN_ID")
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "CAMPAIGN_NAME", nullable = false)
	public String getName() {
		return name;
	}

	public void setName(String campaignName) {
		this.name = campaignName;
	}

	@Column(name = "CAMPAIGN_TYPE", nullable = false)
	public String getType() {
		return type;
	}

	public void setType(String campaignType) {
		this.type = campaignType;
	}

	@Column(name = "CAMPAIGN_DESCRIPTION", nullable = false)
	public String getDescription() {
		return description;
	}

	public void setDescription(String campaignDescription) {
		this.description = campaignDescription;
	}

	@Column(name = "CAMPAIGN_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String campaignStatus) {
		this.status = campaignStatus;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "INTENT_ID", nullable = true)
	public IntentDetail getIntent() {
		return intent;
	}

	public void setIntent(IntentDetail intent) {
		this.intent = intent;
	}

}
