/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.clm.core.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@PropertySource({ "classpath:props/jdbc-${env.type}.properties" })
@EnableTransactionManagement
@ComponentScan(basePackages = { "com.stpl.tech.clm" })
@EnableJpaRepositories(basePackages = {
		"com.stpl.tech.clm.core.data.dao.impl" }, entityManagerFactoryRef = "CLMDataSourceEMFactory", transactionManagerRef = "CLMDataSourceTM")
//@EnableMongoRepositories(basePackages = "com.stpl.tech.clm.core.dao")
public class ClmConfig {

	@Autowired
	private Environment env;

	public ClmConfig() {
		super();
	}

	@Bean(name = "CLMDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean clmEntityManagerFactory() {
		final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(clmDataSource());
		em.setPackagesToScan("com.stpl.tech.clm.core.data.model");
		final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		em.setJpaProperties(hibernateProperties());
		em.setPersistenceUnitName("CLMDataSourcePUName");
		return em;
	}

	@Bean(name = "CLMDataSource")
	public DataSource clmDataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(env.getProperty("clm.jdbc.driverClassName"));
		dataSource.setUrl(env.getProperty("clm.jdbc.url"));
		dataSource.setUsername(env.getProperty("clm.jdbc.user"));
		dataSource.setPassword(env.getProperty("clm.jdbc.pass"));
		return dataSource;
	}

	@Bean(name = "CLMDataSourceTM")
	public PlatformTransactionManager clmTransactionManager() {
		final JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(clmEntityManagerFactory().getObject());
		return transactionManager;
	}

	@Bean(name = "CLMDataSourceET")
	public PersistenceExceptionTranslationPostProcessor clmExceptionTranslation() {
		return new PersistenceExceptionTranslationPostProcessor();
	}

	final Properties hibernateProperties() {
		final Properties hibernateProperties = new Properties();
		hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
		hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
		hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
		return hibernateProperties;
	}

//	@Bean
//	public MongoClient factory() throws UnknownHostException {
//		return new MongoClient(env.getProperty("clm.mongo.url"), Integer.parseInt(env.getProperty("clm.mongo.port")));
//	}
//
//	@Bean
//	public MongoDbFactory getMongoDbFactory() throws Exception {
//		return new SimpleMongoDbFactory(factory(), env.getProperty("clm.mongo.schema"));
//	}
//
//	@Bean(name = "mongoTemplate")
//	public MongoTemplate getMongoTemplate() throws Exception {
//		MongoTemplate mongoTemplate = new MongoTemplate(getMongoDbFactory());
//		return mongoTemplate;
//	}
}
