package com.stpl.tech.clm.core.data.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CAMPAIGN_EVENT")
public class CampaignEventDetail implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private int id;
    private Date creationTime;
    private int totalSMSSent;
    private int totalSMS;
    private int totalSMSCost;
    private BigDecimal totalcost;
    private int createdby;
    private int totalEmailSent;
    private String createdbyName;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CAMPAIGN_ID", nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime=creationTime;
    }

    @Column(name = "TOTAL_SMS_SENT", nullable = false)
    public Integer getTotalSMSSent() {
        return totalSMSSent;
    }

    public void setTotalSMSSent(Integer TotalSMSSent) {
        this.totalSMSSent = TotalSMSSent;
    }

    @Column(name = "TOTAL_EMAIL_SENT", nullable = false)
    public Integer getTotalEmailSent() {
        return totalEmailSent;
    }

    public void setTotalEmailSent(Integer TotalEmailSent) {
        this.totalEmailSent = TotalEmailSent;
    }

    @Column(name = "TOTAL_SMS_COST", nullable = false)
    public Integer getTotalSMSCost() {
        return totalSMSCost;
    }

    public void setTotalSMSCost(Integer TotalSMSCost) {
        this.totalSMSCost = TotalSMSCost;
    }

    @Column(name = "TOTAL_SMS", nullable = false)
    public Integer getTotalSMS() {
        return totalSMS;
    }

    public void setTotalSMS(Integer TotalSMS) {
        this.totalSMS = TotalSMS;
    }

    @Column(name = "TOTAL_COST", nullable = false)
    public BigDecimal getTotalcost() {
        return totalcost;
    }

    public void setTotalcost(BigDecimal Totalcost) {
        this.totalcost = Totalcost;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedby() {
        return createdby;
    }

    public void setCreatedby(Integer createdby) {
        this.createdby = createdby;
    }

    @Column(name = "CREATED_BY_NAME", nullable = false, length = 20)
    public String getCreatedbyName() {
        return createdbyName;
    }

    public void setCreatedbyName(String createdbyName) {
        this.createdbyName = createdbyName;
    }

}