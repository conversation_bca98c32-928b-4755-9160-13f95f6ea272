package com.stpl.tech.clm.core.data.model;

import javax.persistence.*;

@Entity
@Table(name = "TEMPLATE_ATTRIBUTE")
public class TemplateAttributeDetail implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private String name;
    private String type;
    private String value;
    private TemplateDetail template;

    public TemplateAttributeDetail() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ATTRIBUTE_ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "ATTRIBUTE_NAME", nullable = false)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "ATTRIBUTE_TYPE", nullable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "ATTRIBUTE_VALUE", nullable = true)
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TEMPLATE_ID", nullable = false)
    public TemplateDetail getTemplate() {
        return template;
    }

    public void setTemplate(TemplateDetail template) {
        this.template = template;
    }

}