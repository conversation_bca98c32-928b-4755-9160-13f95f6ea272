package com.stpl.tech.clm.core.service.impl;

import com.stpl.tech.clm.core.dao.ClmMetadataDao;
import com.stpl.tech.clm.core.data.converter.ClmDataConverter;
import com.stpl.tech.clm.core.data.model.*;
import com.stpl.tech.clm.core.service.ClmMetadataService;
import com.stpl.tech.clm.domain.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ClmMetadataServiceImpl implements ClmMetadataService {

    private static final Logger LOG = LoggerFactory.getLogger(ClmMetadataServiceImpl.class);

    @Autowired
    private ClmMetadataDao dao;

    @Override
    @Transactional(rollbackFor=Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Campaign> getAllCampaigns() {
        List<Campaign> r = new ArrayList<Campaign>();
        Optional.of(dao.findAll(CampaignDetail.class)).ifPresent(p -> p.forEach(q -> r.add(ClmDataConverter.convert(q))));
        return r;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ServiceProvider> getAllServiceProviders() {
        List<ServiceProvider> r = new ArrayList<ServiceProvider>();
        Optional.of(dao.findAll(ServiceProviderDetail.class)).ifPresent(p -> p.forEach(q -> r.add(ClmDataConverter.convert(q))));
        return r;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CampaignTarget> getAllCampaignTargets() {
        List<CampaignTarget> r = new ArrayList<CampaignTarget>();
        Optional.of(dao.findAll(CampaignTargetDetail.class)).ifPresent(p -> p.forEach(q -> r.add(ClmDataConverter.convert(q))));
        return r;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CampaignEvent> getAllCampaignEvents() {
        List<CampaignEvent> r = new ArrayList<CampaignEvent>();
        Optional.of(dao.findAll(CampaignEventDetail.class)).ifPresent(p -> p.forEach(q -> r.add(ClmDataConverter.convert(q))));
        return r;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Intent> getAllIntents() {
        List<Intent> r = new ArrayList<Intent>();
        Optional.of(dao.findAll(IntentDetail.class)).ifPresent(p -> p.forEach(q -> r.add(ClmDataConverter.convert(q))));
        return r;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Template> getAllTemplates() {
        List<Template> r = new ArrayList<Template>();
        Optional.of(dao.findAll(TemplateDetail.class)).ifPresent(p -> p.forEach(q -> r.add(ClmDataConverter.convert(q))));
        return r;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<TemplateAttribute> getAllTemplateAttributes() {
        List<TemplateAttribute> r = new ArrayList<TemplateAttribute>();
        Optional.of(dao.findAll(TemplateAttributeDetail.class)).ifPresent(p -> p.forEach(q -> r.add(ClmDataConverter.convert(q))));

        return r;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "CLMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
     public List<TestUser> getAllTestUsers() {
            List<TestUser> r = new ArrayList<TestUser>();
            Optional.of(dao.findAll(TestUserData.class)).ifPresent(p -> p.forEach(q -> r.add(ClmDataConverter.convert(q))));
            return r;
    }

}
