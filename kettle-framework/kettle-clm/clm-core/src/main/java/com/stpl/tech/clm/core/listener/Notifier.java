package com.stpl.tech.clm.core.listener;

import com.pusher.rest.Pusher;

public class Notifier {

	private final Pusher pusher;

	private final boolean sendNotification;

	public Notifier(boolean sendNotification) {
		this.sendNotification = sendNotification;
		pusher = new Pusher("205234", "668c61c6259750d8ab74", "3f209e608e6391fa0a6c");
		pusher.setCluster("eu");
		pusher.setEncrypted(true);

	}

//	public void addNotification(EnvType type, UnitReportData data) {
//		if (!sendNotification || data == null) {
//			return;
//		}
//		pusher.trigger(type.name().toLowerCase() + "_" + "sales_data", data.getDetail().getId() + "",
//				Collections.singletonMap("reportData", data));
//
//	}
//
//	public void addNotification(EnvType type, AggregatedReportData data) {
//		if (!sendNotification || data == null) {
//			return;
//		}
//		pusher.trigger(type.name().toLowerCase() + "_" + "sales_data", data.getId() + "",
//				Collections.singletonMap("aggregateData", data));
//
//	}

}
