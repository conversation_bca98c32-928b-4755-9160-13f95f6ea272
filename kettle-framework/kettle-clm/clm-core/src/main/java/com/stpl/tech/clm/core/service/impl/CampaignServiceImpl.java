package com.stpl.tech.clm.core.service.impl;

import com.stpl.tech.clm.core.dao.CampaignDao;
import com.stpl.tech.clm.core.data.converter.ClmDataConverter;
import com.stpl.tech.clm.core.data.model.CampaignDetail;
import com.stpl.tech.clm.core.data.model.CampaignEventDetail;
import com.stpl.tech.clm.core.data.model.CampaignTargetDetail;
import com.stpl.tech.clm.core.data.model.IntentDetail;
import com.stpl.tech.clm.core.service.CampaignService;
import com.stpl.tech.clm.core.util.ServiceProviderInstance;
import com.stpl.tech.clm.domain.model.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.List;

/**
 * Created by Chaayos-Tech on 12-08-2016.
 */
@Service
public class CampaignServiceImpl implements CampaignService {

    @Autowired
    private CampaignDao dao;

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public Campaign addCampaigns(Campaign campaign) {
        CampaignDetail campaignDetail = new CampaignDetail();
        setValues(campaign, campaignDetail);
        return ClmDataConverter.convert((CampaignDetail) dao.add(campaignDetail));
    }

    private void setValues(Campaign campaign, CampaignDetail campaignDetail) {
        campaignDetail.setType(campaign.getType().value());
        campaignDetail.setStatus(campaign.getStatus().value());
        campaignDetail.setName(campaign.getName());
        campaignDetail.setIntent(dao.find(IntentDetail.class, campaign.getIntent().getId()));
        campaignDetail.setDescription(campaign.getDescription());
        campaignDetail.setStatus(campaign.getStatus().name());
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public Campaign updateCampaigns(Campaign campaign) {
        CampaignDetail campaignDetail = dao.find(CampaignDetail.class, campaign.getId());
        setValues(campaign, campaignDetail);
        return ClmDataConverter.convert((CampaignDetail) dao.update(campaignDetail));
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public boolean changeCampaignStatus(int campaignId, RecordStatus status) {

        CampaignDetail campaignDetail = dao.find(CampaignDetail.class, campaignId);
        campaignDetail.setStatus(status.name());
        dao.update(campaignDetail);
        return true;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public CampaignEvent addCampaignEvents(CampaignEvent campaignEvent) {
        CampaignEventDetail campaignEventDetail = new CampaignEventDetail();
        setValues(campaignEvent, campaignEventDetail);
        return ClmDataConverter.convert((CampaignEventDetail) dao.add(campaignEventDetail));
    }

    private void setValues(CampaignEvent campaignEvent, CampaignEventDetail campaignEventDetail) {
        campaignEventDetail.setCreationTime(campaignEvent.getCreationTime());
        campaignEventDetail.setTotalSMS(campaignEvent.getTotalSMS());
        campaignEventDetail.setTotalSMSSent(campaignEvent.getTotalSMSSent());
        campaignEventDetail.setTotalEmailSent(campaignEvent.getTotalEmailSent());
        campaignEventDetail.setTotalSMSCost(campaignEvent.getTotalSMSCost());
        campaignEventDetail.setTotalcost(campaignEvent.getTotalCost());
        campaignEventDetail.setTotalSMSSent(campaignEvent.getTotalSMSSent());
        campaignEventDetail.setCreationTime(campaignEvent.getCreationTime());
        campaignEventDetail.setCreatedby(campaignEvent.getCreatedBy());
        campaignEventDetail.setCreatedbyName(campaignEvent.getCreatedByName());
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public CampaignTarget addCampaignTargets(CampaignTarget campaignTarget) {
        CampaignTargetDetail campaignTargetDetail = new CampaignTargetDetail();
        setValues(campaignTarget, campaignTargetDetail);
        return ClmDataConverter.convert((CampaignTargetDetail) dao.add(campaignTargetDetail));
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addCampaignTargets(List<CampaignTarget> campaignTargets) {
        try {
          /*  Reference For Loop

          for (int i = 0; i < campaignTargets.size(); i++) {
                addCampaignTargets(campaignTargets.get(i));
            }*/

            /*Reference For Loop
            Optional.of(campaignTargets).ifPresent(p -> p.forEach(t -> addCampaignTargets(t)));*/

            for (CampaignTarget t : campaignTargets) {
                addCampaignTargets(t);
            }

            return true;
        } catch (Exception e) {

            return false;

        }
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public CampaignTarget updateCampaignTarget(CampaignTarget campaignTarget) {
        CampaignTargetDetail campaignTargetDetail = dao.find(CampaignTargetDetail.class, campaignTarget);
        setValues(campaignTarget, campaignTargetDetail);
        return ClmDataConverter.convert((CampaignTargetDetail) dao.update(campaignTargetDetail));
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCampaignTargets(List<CampaignTarget> campaignTargets) {
        try {
          /*  for (int i = 0; i < campaignTargets.size(); i++) {
                addCampaignTargets(campaignTargets.get(i));
            }*/
            for (CampaignTarget t : campaignTargets) {
                updateCampaignTarget(t);
            }
            /*Optional.of(campaignTargets).ifPresent(p -> p.forEach(t -> addCampaignTargets(t)));*/
            return true;
        } catch (Exception e) {

            return false;

        }
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public boolean removeCampaignTarget(CampaignTarget campaignTarget) {
        try {
            CampaignTargetDetail campaignTargetDetail = dao.find(CampaignTargetDetail.class, campaignTarget);
            dao.delete(campaignTarget);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public boolean removeCampaignTargets(List<CampaignTarget> campaignTargets) {
        try {
            dao.delete(campaignTargets);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    @Override
    public boolean testCampaign(ServiceProviderInstance provider, Campaign campaign, TestUser user, String message) throws IOException {
        return sendMessage(provider, campaign.getType(), user.getContactNumber(), message);
    }

    @Override
    public boolean sendCampaignMessage(ServiceProviderInstance provider, CampaignType campaignType, CampaignTarget target, String message) throws IOException {

        return sendMessage(provider, campaignType, target.getContactNumber(), message);
    }
    public boolean sendMessage(ServiceProviderInstance provider, CampaignType campaignType,String contactNumber, String message) throws IOException{
        if (campaignType.equals(CampaignType.PROMOTIONAL)) {
            return provider.getPromotional().sendMessage(message, contactNumber);
        } else if (campaignType.equals(CampaignType.TRANSACTIONAL)) {
            return provider.getTransactional().sendMessage(message, contactNumber);
        }
        return false;
    }
    private void setValues(CampaignTarget campaignTarget, CampaignTargetDetail campaignTargetDetail) {
        campaignTargetDetail.setName(campaignTarget.getName());
        campaignTargetDetail.setContactNumber(campaignTarget.getContactNumber());
        campaignTargetDetail.setEmail(campaignTarget.getEmail());
        campaignTargetDetail.setText(campaignTarget.getText());
        campaignTargetDetail.setSmsLength(campaignTarget.getSmsLength());
        campaignTargetDetail.setSmsCount(campaignTarget.getSmsCount());
        campaignTargetDetail.setCampaignType(campaignTarget.getCampaignType().value());
        campaignTargetDetail.setCampaignEventId(campaignTarget.getCampaignEventId());
        campaignTargetDetail.setNotificationType(campaignTarget.getNotificationType().value());
        campaignTargetDetail.setDeliveryStatus(campaignTarget.getDeliveryStatus().value());
        campaignTargetDetail.setServiceProvider(campaignTarget.getServiceProvider());
        campaignTargetDetail.setCustomerId(campaignTarget.getCustomerId());
        campaignTargetDetail.setNotificationDate(campaignTarget.getNotificationDate());

    }


}
//CRUD
//C -> Create
//R -> Read
//U -> Update
//D -> Delete