package com.stpl.tech.clm.core.data.model;


import javax.persistence.*;
import java.util.Date;
import java.lang.String;

@Entity
@Table(name = "CAMPAIGN_TARGET")
public class CampaignTargetDetail implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private String name;
    private String contactNumber;
    private String email;
    private String text;
    private Integer smsLength;
    private Integer smsCount;
    private String campaignType;
    public int campaignEventId;
    private String notificationType;
    private String deliveryStatus;
    private String serviceProvider;
    private Integer customerId;
    private Date notificationDate;


    public CampaignTargetDetail() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TARGET_ID", nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer Id) {
        this.id = Id;
    }

    @Column(name = "TARGET_NAME", nullable = false)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "CONTACT_NUMBER", nullable = false)
    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    @Column(name = "EMAIL_ID", nullable = false)
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "TARGET_TEXT", nullable = false)
    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    @Column(name = "SMS_LENGTH", nullable = false)
    public Integer getSmsLength() {
        return smsLength;
    }

    public void setSmsLength(Integer smsLength) {
        this.smsLength = smsLength;
    }

    @Column(name = "SMS_COUNT", nullable = false)
    public Integer getSmsCount() {
        return smsCount;
    }

    public void setSmsCount(Integer smsCount) {
        this.smsCount = smsCount;
    }

    @Column(name = "CAMPAIGN_TYPE", nullable = false)
    public String getCampaignType() {
        return campaignType;
    }

    public void setCampaignType(String campaignType) {
        this.campaignType = campaignType;
    }

    @Column(name = "CAMPAIGN_EVENT_ID", nullable = false)
    public int getCampaignEventId() {
        return campaignEventId;
    }

    public void setCampaignEventId(int campaignType) {
        this.campaignEventId = campaignType;
    }

    @Column(name = "NOTIFICATION_TYPE", nullable = false)
    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    @Column(name = "DELIVERY_STATUS", nullable = false)
    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    @Column(name = "SERVICE_PROVIDER", nullable = false)
    public String getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(String serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    @Column(name = "CUSTOMER_ID", nullable = false)
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "NOTIFICATION_DATE", nullable = false)
    public Date getNotificationDate() {
        return notificationDate;
    }

    public void setNotificationDate(Date notificationDate) {
        this.notificationDate = notificationDate;
    }

}
