/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.clm.core.dao.impl;

import com.stpl.tech.clm.core.dao.ClmAbstractDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.List;

@Repository
public class ClmAbstractDaoImpl implements ClmAbstractDao {

    private static final Logger LOG = LoggerFactory.getLogger(ClmAbstractDaoImpl.class);

    @PersistenceContext(unitName = "CLMDataSourcePUName")
    @Qualifier(value = "CLMDataSourceEMFactory")
    protected EntityManager manager;

    @Override
    public <T> Object update(T data) {

        try {
            data = manager.merge(data);
            manager.flush();
            return data;
        } catch (Exception e) {
            LOG.error("Error updating " + data.getClass().getName() + " {}", e.getMessage());
        }
        return null;
    }

    @Override
    public <T> Object add(T data) {
        try {
            manager.persist(data);
            manager.flush();
            return data;
        } catch (Exception e) {
            LOG.error("Error adding " + data.getClass().getName() + " {}", e.getMessage());
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> List<T> findAll(Class<T> clazz) {
        Query query = manager.createQuery("FROM " + clazz.getName() + " T");
        return query.getResultList();
    }

    @Override
    public <T, R> T find(Class<T> clazz, R key) {
        return manager.find(clazz, key);
    }

    @Override
    public <T> void delete(T data) {
        try {
            manager.remove(data);
            manager.flush();
        } catch (Exception e) {
            LOG.error("Error deleting " + data.getClass().getName() + " {}", e.getMessage());
        }
    }

}
