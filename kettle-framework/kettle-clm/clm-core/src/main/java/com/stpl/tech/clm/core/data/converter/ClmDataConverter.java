package com.stpl.tech.clm.core.data.converter;

import com.stpl.tech.clm.core.data.model.*;
import com.stpl.tech.clm.domain.model.*;

import java.util.ArrayList;
import java.util.List;

public class ClmDataConverter {

    private static final ObjectFactory factory = new ObjectFactory();

    /**
     * Utility method to convert Campaign Detail to Campaign
     *
     * @param d
     * @return
     */
    public static Campaign convert(CampaignDetail d) {
        Campaign c = factory.createCampaign();
        c.setId(d.getId());
        c.setIntent(convert(d.getIntent()));
        c.setDescription(d.getDescription());
        c.setName(d.getName());
        c.setStatus(RecordStatus.valueOf(d.getStatus()));
        c.setType(CampaignType.valueOf(d.getType()));
        return c;
    }

    /**
     * Utility method to convert IntentDetail to Intent
     *
     * @param
     * @return
     */
    public static Intent convert(IntentDetail d) {
        Intent i = factory.createIntent();
        i.setId(d.getId());
        i.setName(d.getName());
        i.setDescription(d.getDescription());
        return i;
    }

    /**
     * Utility method to convert ServiceProviderDetail to ServiceProvider
     *
     * @param
     * @return
     */
    public static ServiceProvider convert(ServiceProviderDetail d) {
        ServiceProvider c = factory.createServiceProvider();
        c.setId(d.getId());
        c.setName(d.getName());
        c.setCode(d.getCode());
        c.setTopUpURL(d.getTopUpURL());
        c.setUrl(d.getUrl());
        return c;
    }

    /**
     * Utility method to convert CampaignTargetDetail to CampaignTarget
     *
     * @param
     * @return
     */
    public static CampaignTarget convert(CampaignTargetDetail d) {
        CampaignTarget c = factory.createCampaignTarget();
        c.setId(d.getId());
        c.setName(d.getName());
        c.setContactNumber(d.getContactNumber());
        c.setEmail(d.getEmail());
        c.setText(d.getText());
        c.setSmsLength(d.getSmsLength());
        c.setSmsCount(d.getSmsCount());
        c.setCampaignEventId(d.getCampaignEventId());
        c.setNotificationType(NotificationType.valueOf(d.getNotificationType()));
        c.setNotificationDate(d.getNotificationDate());
        c.setDeliveryStatus(DeliveryStatus.valueOf(d.getDeliveryStatus()));
        c.setServiceProvider(d.getServiceProvider());
        c.setCustomerId(d.getCustomerId());
        return c;
    }

    /**
     * Utility method to convert CampaignEventDetail to CampaignEvent
     *
     * @param
     * @return
     */
    public static CampaignEvent
    convert(CampaignEventDetail d) {
        CampaignEvent c = factory.createCampaignEvent();
        c.setId(d.getId());
        c.setCreationTime(d.getCreationTime());
        c.setTotalSMSSent(d.getTotalSMSSent());
        c.setTotalEmailSent(d.getTotalEmailSent());
        c.setCreatedBy(d.getCreatedby());
        c.setCreatedByName(d.getCreatedbyName());
        c.setTotalSMS(d.getTotalSMS());
        return c;
    }

    /**
     * Utility method to convert TemplateAttribute to TemplateAttributeDetail
     *
     * @param
     * @return
     */
    public static TemplateAttribute convert(TemplateAttributeDetail d) {
        TemplateAttribute c = factory.createTemplateAttribute();
        c.setId(d.getId());
        c.setName(d.getName());
        c.setValue(d.getValue());
        c.setType(TemplateAttributeType.valueOf(d.getType()));
        c.setTemplateId(d.getTemplate().getId());
        return c;
    }


    /**
     * Utility method to convert TemplateDetail to Template
     *
     * @param
     * @return
     */
    public static Template convert(TemplateDetail d) {
        Template c = factory.createTemplate();
        c.setId(d.getId());
        c.setText(d.getText());
        c.setName(d.getName());
        List<TemplateAttribute> templateAttributes= new ArrayList<>();
        List<TemplateAttributeDetail> templateAttributeDetailList = d.getAttributes();
        if(templateAttributeDetailList !=null && !templateAttributeDetailList.isEmpty()){
            for(TemplateAttributeDetail data : templateAttributeDetailList){
                templateAttributes.add(convert(data));
            }
        }
        c.setAttributes(templateAttributes);
        c.setType(TemplateType.valueOf(d.getType()));
        c.setCategory(TemplateCategory.valueOf(d.getCategory()));
        return c;
    }

    /**
     * Utility method to convert TestUserData to TestUser
     *
     * @param
     * @return
     */
    public static TestUser convert(TestUserData d) {
        TestUser c = factory.createTestUser();
        c.setId(d.getId());
        c.setName(d.getName());
        c.setContactNumber(d.getContactNumber());
        c.setEmailId(d.getEmailId());
        return c;
    }
}
