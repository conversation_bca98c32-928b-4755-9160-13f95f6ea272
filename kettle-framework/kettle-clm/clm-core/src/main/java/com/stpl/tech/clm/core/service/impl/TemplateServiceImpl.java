package com.stpl.tech.clm.core.service.impl;

import com.stpl.tech.clm.core.dao.TemplateServiceDao;
import com.stpl.tech.clm.core.data.converter.ClmDataConverter;
import com.stpl.tech.clm.core.data.model.TemplateAttributeDetail;
import com.stpl.tech.clm.core.data.model.TemplateDetail;
import com.stpl.tech.clm.core.service.TemplateService;
import com.stpl.tech.clm.domain.model.Template;
import com.stpl.tech.clm.domain.model.TemplateAttribute;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by Chaayos-Tech on 22-08-2016.
 */
@Service
public class TemplateServiceImpl implements TemplateService {
    @Autowired
    private TemplateServiceDao dao;


    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public Template addTemplate(Template template) {
        TemplateDetail templateDetail = new TemplateDetail();
        //setValues(template, templateDetail);
        templateDetail.setcategory(template.getCategory().value());
        templateDetail.setName(template.getName());
        templateDetail.setText(template.getText());
        templateDetail.setType(template.getType().value());
        dao.add(templateDetail);
        List<TemplateAttributeDetail> templateAttributeDetailList = new ArrayList<>();
        for(TemplateAttribute templateAttribute : template.getAttributes()){
            TemplateAttributeDetail templateAttributeDetail = new TemplateAttributeDetail();
            templateAttributeDetail.setName(templateAttribute.getName());
            templateAttributeDetail.setTemplate(templateDetail);
            templateAttributeDetail.setType(templateAttribute.getType().value());
            templateAttributeDetail.setValue(templateAttribute.getValue());
            dao.add(templateAttributeDetail);
            templateAttributeDetailList.add(templateAttributeDetail);
        }
        templateDetail.setAttributes(templateAttributeDetailList);
        return ClmDataConverter.convert(templateDetail);

    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public Template updateTemplate(Template template) {
        TemplateDetail templateDetail = dao.find(TemplateDetail.class, template.getId());
        setValues(template, templateDetail);
        return ClmDataConverter.convert((TemplateDetail) dao.update(templateDetail));

    }

    private void setValues(Template template, TemplateDetail templateDetail) {
        templateDetail.setName(template.getName());
        templateDetail.setText(template.getText());
        if(templateDetail.getAttributes() == null) {
            templateDetail.setAttributes(new ArrayList<TemplateAttributeDetail>(0));
        }
        boolean updated = false;
        Set<TemplateAttributeDetail> attributes = new HashSet<>();

        for (TemplateAttribute t : template.getAttributes()) {
            updated = false;
            // case 1: update existing attribute
            for(TemplateAttributeDetail td : templateDetail.getAttributes()){
                if(td.getId() == t.getId() && t.getId() != 0 && td.getId() != 0){
                    setValues(t,td,templateDetail);
                    attributes.add(td);
                    updated = true;
                }
            }
        }
         // case 2: remove not required attributes
        templateDetail.getAttributes().remove(attributes);

        // case 3: add attribute that does not exist
        for (TemplateAttribute t : template.getAttributes()) {
            if (t.getId() == 0) {
                templateDetail.getAttributes().add(createTemplateAttributeDetail(t, templateDetail));
            }
        }

        templateDetail.setcategory(template.getCategory().value());
        templateDetail.setType(template.getType().value());
    }

    private TemplateAttributeDetail createTemplateAttributeDetail(TemplateAttribute template, TemplateDetail templateDetail) {
        TemplateAttributeDetail data = new TemplateAttributeDetail();
        setValues(template, data, templateDetail);
        dao.add(data);
        return data;
    }


    private void setValues(TemplateAttribute templateAttribute, TemplateAttributeDetail templateAttributeDetail, TemplateDetail templateDetail) {
        templateAttributeDetail.setName(templateAttribute.getName());
        templateAttributeDetail.setType(templateAttribute.getType().name());
        templateAttributeDetail.setValue(templateAttribute.getValue());
        templateAttributeDetail.setTemplate(templateDetail);
    }
}
