package com.stpl.tech.clm.core.util;

import com.stpl.tech.master.core.notification.sms.SMSGupshupWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;

/**
 * Created by Chaayos-Tech on 22-08-2016.
 */
public enum ServiceProviderInstance {

	

    SMS_GUPSHUP(SMSGupshupWebServiceClient.getPromotionalInstance(), SMSGupshupWebServiceClient.getRegularInstance()),
    SOLUTION_INFINI(SolsInfiniWebServiceClient.getTransactionalClient(), SolsInfiniWebServiceClient.getTransactionalClient());

    private SMSWebServiceClient promotional;
    private SMSWebServiceClient transactional;

    ServiceProviderInstance(SMSWebServiceClient promotional, SMSWebServiceClient transactional) {
        this.promotional = promotional;
        this.transactional = transactional;
    }

    public SMSWebServiceClient getPromotional() {
        return promotional;
    }

    public SMSWebServiceClient getTransactional() {
        return transactional;
    }

}
