package com.stpl.tech.clm.core.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "INTENT_DETAIL")
public class IntentDetail implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	private Integer id;
	private String name;
	private String description;

	public IntentDetail() {
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "INTENT_ID")
	public Integer getId() {
		return id;
	}

	public void setId(Integer IntentId) {
		this.id = IntentId;
	}

	@Column(name = "INTENT_NAME", nullable = false)
	public String getName() {
		return name;
	}

	public void setName(String IntentName) {
		this.name = IntentName;
	}

	@Column(name = "INTENT_DESCRIPTION", nullable = false)
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}