package com.stpl.tech.clm.core.service;

import com.stpl.tech.clm.core.util.ServiceProviderInstance;
import com.stpl.tech.clm.domain.model.*;

import java.io.IOException;
import java.util.List;

/**
 * Created by Chaayos-Tech on 12-08-2016.
 */
public interface CampaignService {

    public Campaign addCampaigns(Campaign campaign);

    public Campaign updateCampaigns(Campaign campaign);

    public boolean changeCampaignStatus(int campaignId, RecordStatus status);

    public CampaignEvent addCampaignEvents(CampaignEvent campaignEvent);

    public CampaignTarget addCampaignTargets(CampaignTarget campaignTarget);

    public boolean addCampaignTargets(List<CampaignTarget> campaignTarget);

    public CampaignTarget updateCampaignTarget(CampaignTarget campaignTarget);

    public boolean updateCampaignTargets(List<CampaignTarget> campaignTargets);

    public boolean removeCampaignTarget(CampaignTarget campaignTarget);

    public boolean removeCampaignTargets(List<CampaignTarget> campaignTargets);

    public boolean testCampaign(ServiceProviderInstance provider, Campaign campaign, TestUser user, String message) throws IOException;
    public boolean sendCampaignMessage(ServiceProviderInstance provider, CampaignType campaignType, CampaignTarget target, String message) throws IOException;
    public boolean sendMessage(ServiceProviderInstance provider, CampaignType campaignType,String contactNumber, String message) throws IOException;

    }
