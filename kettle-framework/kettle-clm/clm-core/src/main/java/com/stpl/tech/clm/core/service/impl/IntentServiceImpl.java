package com.stpl.tech.clm.core.service.impl;

import com.stpl.tech.clm.core.dao.IntentDao;
import com.stpl.tech.clm.core.data.converter.ClmDataConverter;
import com.stpl.tech.clm.core.data.model.IntentDetail;
import com.stpl.tech.clm.core.service.IntentService;
import com.stpl.tech.clm.domain.model.Intent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Chaayos-Tech on 17-08-2016.
 */
@Service
public class IntentServiceImpl implements IntentService {

    @Autowired
    IntentDao dao;

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public Intent addIntent(Intent intent) {
        IntentDetail intentDetail = new IntentDetail();
        setValues(intent, intentDetail);
        return ClmDataConverter.convert((IntentDetail) dao.add(intentDetail));

    }

    private void setValues(Intent intent, IntentDetail intentDetail) {
        intentDetail.setName(intent.getName());
        intentDetail.setDescription(intent.getDescription());
    }


    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public Intent updateIntent(Intent intent) {
        IntentDetail intentDetail = dao.find(IntentDetail.class, intent.getId());
        setValues(intent, intentDetail);
        return ClmDataConverter.convert((IntentDetail) dao.update(intentDetail));
    }
}
