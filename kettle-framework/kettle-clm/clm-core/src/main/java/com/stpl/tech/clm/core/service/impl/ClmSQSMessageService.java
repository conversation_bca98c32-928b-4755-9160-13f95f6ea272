package com.stpl.tech.clm.core.service.impl;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Session;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.clm.core.listener.ClmOrderMessageListener;
import com.stpl.tech.clm.core.service.ClmOrderProcessingService;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;

@Service
public class ClmSQSMessageService {

	@Autowired
	private ClmEnvironmentProperties props;

	@Autowired
	private ClmOrderProcessingService service;

	@PostConstruct
	public void init() throws JMSException {
		Regions region  = AppUtils.getRegion(props.getEnvironmentType());
		SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
		MessageProducer producer = SQSNotification.getInstance().getProducer(session, props.getEnvironmentType().name(),
				"_ORDERS_ERRORS");
		ClmOrderMessageListener listener = new ClmOrderMessageListener(props.getEnvironmentType(), producer, service,
				props.publishToPusher());
		MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvironmentType().name(),
				"_ORDERS");
		consumer.setMessageListener(listener);
		SQSNotification.getInstance().getSqsConnection(region).start();
	}
}
