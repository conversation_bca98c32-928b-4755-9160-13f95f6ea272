package com.stpl.tech.clm.core.service.impl;

import com.stpl.tech.clm.core.dao.TestUserDao;
import com.stpl.tech.clm.core.data.converter.ClmDataConverter;
import com.stpl.tech.clm.core.data.model.TestUserData;
import com.stpl.tech.clm.core.service.TestUserService;
import com.stpl.tech.clm.domain.model.TestUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

;

/**
 * Created by Chaayos-Tech on 19-08-2016.
 */
@Service
public class TestUserServiceImpl implements TestUserService {

    @Autowired
    private TestUserDao dao;

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public TestUser addTestUser(TestUser testUser) {
        TestUserData testUserData = new TestUserData();
        setValues(testUser, testUserData);
        return ClmDataConverter.convert((TestUserData) dao.add(testUserData));
    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public TestUser updateTestUser(TestUser testUser) {
        TestUserData testUserData = dao.find(TestUserData.class, testUser.getId());
        setValues(testUser, testUserData);
        return ClmDataConverter.convert((TestUserData) dao.update(testUserData));

    }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public boolean removeTestUser(TestUser testUser) {
        try {
            TestUserData testUserData = dao.find(TestUserData.class, testUser.getId());
            dao.delete(testUserData);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private void setValues(TestUser testUser, TestUserData testUserData) {
        testUserData.setName(testUser.getName());
        testUserData.setContactNumber(testUser.getContactNumber());
        testUserData.setEmailId(testUser.getEmailId());

    }
}
