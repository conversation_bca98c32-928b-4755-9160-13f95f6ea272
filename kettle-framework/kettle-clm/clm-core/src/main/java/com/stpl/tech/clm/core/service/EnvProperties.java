/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.clm.core.service;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;


@Service
public class EnvProperties {

    @Autowired
    Environment env;

    public String getBasePath() {
        return env.getProperty("server.base.dir");
    }

    public EnvType getEnvType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public boolean getRunAutoReports() {
        return Boolean.valueOf(env.getProperty("run.auto.report", "false"));
    }
    
}
