package com.stpl.tech.clm.core.data.model;


import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;


@Entity
@Table(name = "TEMPLATE_DETAIL")
public class TemplateDetail implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private String text;
    private String name;
    private String type;
    private List<TemplateAttributeDetail> attributes = new ArrayList<>(0);
    private String category;

    public TemplateDetail() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TEMPLATE_ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer templateId) {
        this.id = templateId;
    }

    @Column(name = "TEMPLATE_NAME")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "TEMPLATE_TEXT")
    public String getText() {
        return text;
    }

    public void setText(String templatetext) {
        this.text = templatetext;
    }


    @Column(name = "TEMPLATE_TYPE")
    public String getType() {
        return type;
    }

    public void setType(String templateType) {
        this.type = templateType;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "template", cascade = CascadeType.PERSIST)
    public List<TemplateAttributeDetail> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<TemplateAttributeDetail> attributes) {
        this.attributes = attributes;
    }

    @Column(name = "TEMPLATE_CATEGORY")
    public String getCategory() {
        return category;
    }

    public void setcategory(String category) {
        this.category = category;
    }
}