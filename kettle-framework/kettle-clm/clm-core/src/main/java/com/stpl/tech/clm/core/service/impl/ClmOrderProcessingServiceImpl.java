package com.stpl.tech.clm.core.service.impl;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.clm.core.dao.ClmOrderProcessingDao;
import com.stpl.tech.clm.core.service.ClmOrderProcessingService;
import com.stpl.tech.kettle.domain.model.OrderResponse;

@Service
public class ClmOrderProcessingServiceImpl implements ClmOrderProcessingService {
	private static final Logger LOG = LoggerFactory.getLogger(ClmOrderProcessingServiceImpl.class);

	@Autowired
	private ClmOrderProcessingDao orderDao;

	@Override
	public List<OrderResponse> findByOrderId(int orderId) {
		LOG.info("Searching for orderId: {} in MongoDB", orderId);
		return orderDao.findByOrderId(orderId);
	}

	@Override
	public OrderResponse addOrder(OrderResponse order) {
		LOG.info("Adding OrderResponse to MongoDB");
		return orderDao.save(order);
	}
}
