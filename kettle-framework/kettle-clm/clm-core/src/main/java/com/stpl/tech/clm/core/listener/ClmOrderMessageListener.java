package com.stpl.tech.clm.core.listener;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.stpl.tech.clm.core.service.ClmOrderProcessingService;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClmOrderMessageListener implements MessageListener {

	private static final Logger LOG = LoggerFactory.getLogger(ClmOrderMessageListener.class);

	private ClmOrderProcessingService service;

	private MessageProducer errorQueue;

	private Notifier notifier;

	private EnvType env;

	private Map<Integer, OrderResponse> cancelledOrder = new HashMap<>();

	public ClmOrderMessageListener(EnvType env, MessageProducer errorQueue, ClmOrderProcessingService service,
			boolean sendNotification) {
		this.env = env;
		this.errorQueue = errorQueue;
		this.service = service;
		this.notifier = new Notifier(sendNotification);
	}

	@Override
	public void onMessage(Message message) {
		try {
			LOG.info("On Message " + message.getJMSMessageID());
			if (message instanceof SQSObjectMessage) {
				SQSObjectMessage object = (SQSObjectMessage) message;
				if (object.getObject() instanceof OrderResponse) {
					message.acknowledge();
					OrderResponse response = (OrderResponse) object.getObject();
					processMessage(response);
					message.acknowledge();
				}
			}
		} catch (JMSException e) {
			LOG.error("Error while saving the message", e);
			try {
				LOG.info("Publishing Error Message to Error Queue " + message.getJMSMessageID());
				errorQueue.send(message);
			} catch (JMSException e1) {
				LOG.error("Error while saving the message to error queue", e);
			}
			return;
		}
	}

	private void processMessage(OrderResponse response) {
		LOG.info("Got Message " + response.getOrder().getOrderId() + " status : " + response.getOrder().getStatus());
		List<OrderResponse> orders = service.findByOrderId(response.getOrder().getOrderId());
		if (orders != null && orders.size() > 0) {
			OrderResponse order = orders.get(0);
			if (order.getOrder().getStatus().equals(response.getOrder().getStatus())) {
				LOG.error("Order has already been processed " + response.getOrder().getOrderId());
			} else {
				LOG.error("Order status is changed " + response.getOrder().getOrderId());
				saveAndNotify(response);
			}
		} else {
			// Incase an order with cancelled status comes before
			if (OrderStatus.CANCELLED.equals(response.getOrder().getStatus())) {
				cancelledOrder.put(response.getOrder().getOrderId(), response);
			} else {
				saveAndNotify(response);
				if (cancelledOrder.containsKey(response.getOrder().getOrderId())) {
					processMessage(cancelledOrder.get(response.getOrder().getOrderId()));
					cancelledOrder.remove(response.getOrder().getOrderId());
				}
			}
		}
	}

	private void saveAndNotify(OrderResponse order) {
		service.addOrder(order);
	}
}
