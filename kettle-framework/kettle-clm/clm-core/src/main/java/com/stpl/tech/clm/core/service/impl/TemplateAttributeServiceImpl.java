package com.stpl.tech.clm.core.service.impl;

import com.stpl.tech.clm.core.dao.TemplateAttributeServiceDao;
import com.stpl.tech.clm.core.data.converter.ClmDataConverter;
import com.stpl.tech.clm.core.data.model.TemplateAttributeDetail;
import com.stpl.tech.clm.core.service.TemplateAttributeService;
import com.stpl.tech.clm.domain.model.TemplateAttribute;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Chaayos-Tech on 06-09-2016.
 */
@Service
public class TemplateAttributeServiceImpl implements TemplateAttributeService {

    @Autowired
    private TemplateAttributeServiceDao dao;

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public TemplateAttribute addTemplateAttribute(TemplateAttribute templateAttribute) {
        TemplateAttributeDetail templateAttributeDetail =new TemplateAttributeDetail();
        setValues(templateAttribute,templateAttributeDetail);
        return ClmDataConverter.convert((TemplateAttributeDetail) dao.add(templateAttributeDetail));

        }

    @Override
    @Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
    public TemplateAttribute updateTemplateAttribute(TemplateAttribute templateAttribute) {
        TemplateAttributeDetail templateAttributeDetail =dao.find(TemplateAttributeDetail.class, templateAttribute.getId());
        setValues(templateAttribute,templateAttributeDetail);
        return ClmDataConverter.convert((TemplateAttributeDetail) dao.update(templateAttributeDetail));

    }

    private void setValues(TemplateAttribute templateAttribute, TemplateAttributeDetail templateAttributeDetail) {
        templateAttributeDetail.setName(templateAttribute.getName());
        templateAttributeDetail.setType(templateAttribute.getType().value());
        templateAttributeDetail.setValue(templateAttribute.getValue());
        templateAttributeDetail.setValue(String.valueOf(templateAttribute.getTemplateId()));
    }

}
