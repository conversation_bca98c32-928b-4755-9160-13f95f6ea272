package com.stpl.tech.clm.core.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "SERVICE_PROVIDER")
public class ServiceProviderDetail implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	private Integer id;
	private String name;
	private String code;
	private String url;
	private String topUpURL;

	public ServiceProviderDetail() {
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "PROVIDER_ID", nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer ProviderId) {
		this.id = ProviderId;
	}

	@Column(name = "PROVIDER_NAME", nullable = false)
	public String getName() {
		return name;
	}

	public void setName(String providerName) {
		this.name = providerName;
	}

	@Column(name = "PROVIDER_CODE", nullable = false)
	public String getCode() {
		return code;
	}

	public void setCode(String providerCode) {
		this.code = providerCode;
	}

	@Column(name = "PROVIDER_URL", nullable = true)
	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	@Column(name = "PROVIDER_TOPUP_URL", nullable = true)
	public String getTopUpURL() {
		return topUpURL;
	}

	public void setTopUpURL(String topUpURL) {
		this.topUpURL = topUpURL;
	}

}