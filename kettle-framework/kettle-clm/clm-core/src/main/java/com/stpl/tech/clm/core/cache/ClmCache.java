/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.clm.core.cache;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.clm.core.service.ClmMetadataService;

@Service
public class ClmCache {

	private static final Logger LOG = LoggerFactory.getLogger(ClmCache.class);

	@Autowired
	private ClmMetadataService metadataService;

	public ClmCache() {

	}

	public void clearCache() {
		LOG.info("Clearing CLM Cache");
		// attributeDefinitions = new TreeMap<Integer, AttributeDefinition>();
	}

	@PostConstruct
	public void loadCache() {

	}

}
