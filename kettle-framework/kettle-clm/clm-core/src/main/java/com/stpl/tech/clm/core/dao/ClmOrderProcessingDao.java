package com.stpl.tech.clm.core.dao;

import com.stpl.tech.kettle.domain.model.OrderResponse;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ClmOrderProcessingDao extends MongoRepository<OrderResponse, String> {

	@Query("{'order.billingServerTime': {$gte : ?0}, 'order.status' : {$ne : ?1}  }")
	public List<OrderResponse> findByOrderTime(final Date billingTime, final String status);

	@Query("{'order.orderId': ?0}")
	public List<OrderResponse> findByOrderId(final int orderId);
}
