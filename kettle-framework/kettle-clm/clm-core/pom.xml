<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kettle-clm</artifactId>
        <groupId>com.stpl.tech.kettle.clm</groupId>
        <version>4.1.0-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>clm-core</artifactId>
    <packaging>jar</packaging>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <name>clm-core</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>com.stpl.tech.kettle.clm</groupId>
            <artifactId>clm-domain</artifactId>
            <version>4.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.transaction</groupId>
            <artifactId>data-model</artifactId>
            <version>4.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.subtlelib</groupId>
            <artifactId>poi.builder</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.1</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>com.pusher</groupId>
            <artifactId>pusher-http-java</artifactId>
            <version>0.9.3</version>
        </dependency>
    </dependencies>
</project>
