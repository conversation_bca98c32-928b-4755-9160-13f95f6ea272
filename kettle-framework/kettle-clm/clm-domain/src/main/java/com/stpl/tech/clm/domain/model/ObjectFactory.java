//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.05 at 06:00:40 PM IST 
//


package com.stpl.tech.clm.domain.model;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.clm.domain.model package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.clm.domain.model
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Campaign }
     * 
     */
    public Campaign createCampaign() {
        return new Campaign();
    }

    /**
     * Create an instance of {@link CampaignEvent }
     * 
     */
    public CampaignEvent createCampaignEvent() {
        return new CampaignEvent();
    }

    /**
     * Create an instance of {@link CampaignTarget }
     * 
     */
    public CampaignTarget createCampaignTarget() {
        return new CampaignTarget();
    }

    /**
     * Create an instance of {@link Intent }
     * 
     */
    public Intent createIntent() {
        return new Intent();
    }

    /**
     * Create an instance of {@link ServiceProvider }
     * 
     */
    public ServiceProvider createServiceProvider() {
        return new ServiceProvider();
    }

    /**
     * Create an instance of {@link TemplateAttribute }
     * 
     */
    public TemplateAttribute createTemplateAttribute() {
        return new TemplateAttribute();
    }

    /**
     * Create an instance of {@link Template }
     * 
     */
    public Template createTemplate() {
        return new Template();
    }

    /**
     * Create an instance of {@link TestUser }
     * 
     */
    public TestUser createTestUser() {
        return new TestUser();
    }

}
