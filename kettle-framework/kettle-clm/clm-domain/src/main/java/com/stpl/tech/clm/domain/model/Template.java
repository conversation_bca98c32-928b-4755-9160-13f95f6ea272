//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.05 at 06:00:40 PM IST 
//


package com.stpl.tech.clm.domain.model;

import javax.xml.bind.annotation.*;
import java.util.List;


/**
 * <p>Java class for Template complex type.
 * <p/>
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p/>
 * <pre>
 * &lt;complexType name="Template"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="text" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attributes" type="{http://www.w3schools.com}TemplateAttribute" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}TemplateType"/&gt;
 *         &lt;element name="category" type="{http://www.w3schools.com}TemplateCategory"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Template", propOrder = {
        "id",
        "name",
        "text",
        "attributes",
        "type",
        "category"
})
public class Template {

    protected int id;
    @XmlElement(required = true)
    protected String text;
    protected String name;
    protected List<TemplateAttribute> attributes;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected TemplateType type;

   @XmlElement(required = true)

    @XmlSchemaType(name = "string")
    protected TemplateCategory category;

    /**
     * Gets the value of the id property.
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the text property.
     *
     * @return possible object is
     * {@link String }
     */

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets the value of the text property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getText() {
        return text;
    }

    /**
     * Sets the value of the text property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setText(String value) {
        this.text = value;
    }

    /**
     * Gets the value of the attributes property.
     * <p/>
     * <p/>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the attributes property.
     * <p/>
     * <p/>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAttributes().add(newItem);
     * </pre>
     * <p/>
     * <p/>
     * <p/>
     * Objects of the following type(s) are allowed in the list
     * {@link TemplateAttribute }
     */
    public List<TemplateAttribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<TemplateAttribute> attributes) {
        this.attributes = attributes;
    }

    /**
     * Gets the value of the type property.
     *
     * @return possible object is
     * {@link TemplateType }
     */
    public TemplateType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value allowed object is
     *              {@link TemplateType }
     */
    public void setType(TemplateType value) {
        this.type = value;
    }

    /**
     * Gets the value of the category property.
     *
     * @return possible object is
     * {@link TemplateCategory }
     */
    public TemplateCategory getCategory() {
        return category;
    }

    /**
     * Sets the value of the category property.
     *
     * @param value allowed object is
     *              {@link TemplateCategory }
     */
    public void setCategory(TemplateCategory value) {
        this.category = value;
    }

}
