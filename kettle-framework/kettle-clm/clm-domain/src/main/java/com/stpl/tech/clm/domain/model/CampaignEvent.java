//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.05 at 06:00:40 PM IST 
//


package com.stpl.tech.clm.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <p>Java class for CampaignEvent complex type.
 * <p/>
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p/>
 * <pre>
 * &lt;complexType name="CampaignEvent"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="creationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="totalSMSSent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="totalEmailSent" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="totalSMSCost" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="createdByName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CampaignEvent", propOrder = {
        "id",
        "creationTime",
        "totalSMSSent",
        "totalSMS",
        "totalEmailSent",
        "totalSMSCost",
        "createdBy",
        "createdByName"
})
public class CampaignEvent {

    protected int id;
    @XmlElement(required = true)
    @XmlSchemaType(name = "date")
    protected Date creationTime;
    protected int totalSMSSent;
    protected int totalSMS;
    protected int totalEmailSent;
    protected int totalSMSCost;
    protected int createdBy;
    @XmlElement(required = true)
    protected String createdByName;
    protected BigDecimal totalCost;

    /**
     * Gets the value of the id property.
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the creationTime property.
     *
     * @return possible object is
     * {@link XMLGregorianCalendar }
     */
    public Date getCreationTime() {
        return creationTime;
    }

    /**
     * Sets the value of the creationTime property.
     *
     * @param value allowed object is
     *              {@link XMLGregorianCalendar }
     */
    public void setCreationTime(Date value) {
        this.creationTime = value;
    }

    /**
     * Gets the value of the totalSMSSent property.
     */
    public int getTotalSMSSent() {
        return totalSMSSent;
    }

    /**
     * Sets the value of the totalSMSSent property.
     */
    public void setTotalSMSSent(int value) {
        this.totalSMSSent = value;
    }

    /**
     * Gets the value of the totalSMS property.
     */
    public int getTotalSMS() {
        return totalSMS;
    }

    /**
     * Sets the value of the totalSMS property.
     */
    public void setTotalSMS(int value) {
        this.totalSMS = value;
    }


    /**
     * Gets the value of the totalEmailSent property.
     */
    public int getTotalEmailSent() {
        return totalEmailSent;
    }

    /**
     * Sets the value of the totalEmailSent property.
     */
    public void setTotalEmailSent(int value) {
        this.totalEmailSent = value;
    }

    /**
     * Gets the value of the totalSMSCost property.
     */
    public int getTotalSMSCost() {
        return totalSMSCost;
    }

    /**
     * Sets the value of the totalSMSCost property.
     */
    public void setTotalSMSCost(int value) {
        this.totalSMSCost = value;
    }

    /**
     * Gets the value of the createdBy property.
     */
    public int getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     */
    public void setCreatedBy(int value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the createdByName property.
     *
     * @return possible object is
     * {@link String }
     */
    public java.lang.String getCreatedByName() {
        return createdByName;
    }

    /**
     * Sets the value of the createdByName property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCreatedByName(String value) {
        this.createdByName = value;
    }


    public BigDecimal getTotalCost() {
        return totalCost;
    }
}
