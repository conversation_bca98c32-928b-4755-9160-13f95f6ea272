<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:complexType name="Campaign">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="type" type="CampaignType" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="status" type="RecordStatus" />
			<xs:element name="intent" type="Intent" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CampaignEvent">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="creationTime" type="xs:date" />
			<xs:element name="totalSMSSent" type="xs:int" />
			<xs:element name="totalEmailSent" type="xs:int" />
			<xs:element name="totalSMSCost" type="xs:int" />
			<xs:element name="createdBy" type="xs:int" />
			<xs:element name="createdByName" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CampaignTarget">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="contactNumber" type="xs:string" />
			<xs:element name="email" type="xs:string" />
			<xs:element name="text" type="xs:string" />
			<xs:element name="smsLength" type="xs:int" />
			<xs:element name="smsCount" type="xs:int" />
			<xs:element name="campaignEventId" type="xs:int" />
			<xs:element name="campaignType" type="CampaignType" />
			<xs:element name="notificationType" type="NotificationType" />
			<xs:element name="deliveryStatus" type="DeliveryStatus" />
			<xs:element name="serviceProvider" type="ServiceProvider" />
			<xs:element name="customerId" type="xs:int" />
			<xs:element name="notificationDate" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Intent">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="Name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ServiceProvider">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="url" type="xs:string" />
			<xs:element name="topUpURL" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TemplateAttribute">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="type" type="TemplateAttributeType" />
			<xs:element name="value" type="xs:string" />
			<xs:element name="templateId" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Template">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="text" type="xs:string" />
			<xs:element name="attributes" type="TemplateAttribute"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="type" type="TemplateType" />
			<xs:element name="category" type="TemplateCategory" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TestUser">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="contactNumber" type="xs:string" />
			<xs:element name="emailId" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="DeliveryStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DELIVERED" />
			<xs:enumeration value="UNDELIVERED" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="NotificationType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SMS" />
			<xs:enumeration value="EMAIL" />
			<xs:enumeration value="PUSH NOTIFICATION" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CampaignType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TRANSACTIONAL" />
			<xs:enumeration value="PROMOTIONAL" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="RecordStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TemplateAttributeType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FILE" />
			<xs:enumeration value="TEXT" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TemplateType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SMS" />
			<xs:enumeration value="EMAIL" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TemplateCategory" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TRANSACTIONAL" />
			<xs:enumeration value="PROMOTIONAL" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>