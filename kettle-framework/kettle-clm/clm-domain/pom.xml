<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kettle-clm</artifactId>
        <groupId>com.stpl.tech.kettle.clm</groupId>
        <version>4.1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>clm-domain</artifactId>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <build>
        <finalName>${project.artifactId}</finalName>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.stpl.tech.util</groupId>
            <artifactId>utility-service</artifactId>
            <version>4.1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <!--    <build>-->
    <!--        <plugins>-->
    <!--            &lt;!&ndash;  <plugin>-->
    <!--                 <groupId>org.jvnet.jaxb2.maven2</groupId>-->
    <!--                 <artifactId>maven-jaxb2-plugin</artifactId>-->
    <!--                 <version>0.11.0</version>-->
    <!--                 <executions>-->
    <!--                     <execution>-->
    <!--                         <id>clm-schema</id>-->
    <!--                         <phase>generate-sources</phase>-->
    <!--                         <goals>-->
    <!--                             <goal>generate</goal>-->
    <!--                         </goals>-->
    <!--                         <configuration>-->
    <!--                             <schemaDirectory>src/main/xsds/</schemaDirectory>-->
    <!--                             <generatePackage>com.stpl.tech.clm.domain.model</generatePackage>-->
    <!--                             <generateDirectory>${project.build.directory}/generated-sources/xjc-clm</generateDirectory>-->
    <!--                         </configuration>-->
    <!--                     </execution>-->
    <!--                 </executions>-->
    <!--             </plugin> &ndash;&gt;-->
    <!--        </plugins>-->
    <!--    </build>-->
</project>
