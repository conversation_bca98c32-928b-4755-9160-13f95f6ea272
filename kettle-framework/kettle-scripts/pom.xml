<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.stpl.tech.kettle</groupId>
        <artifactId>kettle-framework</artifactId>
        <version>4.1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>kettle-scripts</artifactId>
    <name>kettle-scripts</name>
<!--	<build>-->
<!--		<plugins>-->
<!--			<plugin>-->
<!--				<artifactId>maven-resources-plugin</artifactId>-->
<!--				<version>2.6</version>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>copy-scripts</id>-->
<!--						<goals>-->
<!--							<goal>copy-resources</goal>-->
<!--						</goals>-->
<!--						<configuration>-->
<!--							<outputDirectory>/home/<USER>/scripts</outputDirectory>-->
<!--							<resources>-->
<!--								<resource>-->
<!--									<directory>${project.basedir}/src/main/resources/build-scripts/common</directory>-->
<!--									<filtering>true</filtering>-->
<!--								</resource>-->
<!--								<resource>-->
<!--									<directory>${project.basedir}/src/main/resources/build-scripts/${server.name}</directory>-->
<!--									<filtering>true</filtering>-->
<!--								</resource>-->
<!--							</resources>-->
<!--						</configuration>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->
<!--		</plugins>-->
<!--	</build>-->
    <properties>
		<spring-boot.repackage.skip>true</spring-boot.repackage.skip>
	</properties>
</project>
