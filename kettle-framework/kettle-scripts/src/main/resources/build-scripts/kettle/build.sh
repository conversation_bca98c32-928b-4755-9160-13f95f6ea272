cd /home/<USER>/scripts/
cat version.txt |awk -F. -v OFS=. 'NF==1{print ++$NF}; NF>1{if(length($NF+1)>length($NF))$(NF-1)++; $NF=sprintf("%0*d", length($NF), ($NF+1)%(10^length($NF))); print}' >> version1.txt
yes | cp -rf version1.txt version.txt
rm -rf version1.txt
buildVersion=`cat version.txt`
echo "New Build Version is $buildVersion"
echo "gulp --deployment=prod --version=$buildVersion"
cd /home/<USER>/builds/application/chaayos/
echo "Stashing Any Local Changes..."
git stash
echo "Pulling Out New Changes..."
git pull
PULL_STATUS=$?
git stash pop
if [ $PULL_STATUS -eq 0 ]; then
        echo "Running front end build process KETTLE..."
        cd kettle-framework/kettle-transaction/kettle-service/src/main/webapp/
        npm install
        gulp --deployment=prod --version=$buildVersion
        cd /home/<USER>/builds/application/chaayos/
        echo "Running maven Build Process..."
        mvn clean install -Dmaven.test.skip=true -Denv.type=prod
        STATUS=$?
        if [ $STATUS -eq 0 ]; then
                echo "UI Build Successful..."
  				sh /home/<USER>/scripts/backup.sh
        fi
fi
