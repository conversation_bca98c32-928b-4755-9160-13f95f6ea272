cd /home/<USER>/builds/application/chaayos/
echo "Stashing Any Local Changes..."
git stash
echo "Pulling Out New Changes..."
git pull
PULL_STATUS=$?
git stash pop
if [ $PULL_STATUS -eq 0 ]; then
        cd /home/<USER>/builds/application/chaayos/
        echo "Running maven Build Process..."
        mvn clean install -Dmaven.test.skip=true -Denv.type=prod
        STATUS=$?
        if [ $STATUS -eq 0 ]; then
                echo "UI Build Successful..."
				sh /home/<USER>/scripts/backup.sh
        fi

f