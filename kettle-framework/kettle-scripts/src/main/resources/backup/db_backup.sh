#!/bin/sh

source_env_name=uat
source_schema_name=KETTLE_DUMP
target_env_name=backup
current_week=$(date +"%V")
current_day=$(date +"%a" | tr '[:lower:]' '[:upper:]')
target_schema_name=KETTLE_DUMP_$current_day

current_date=$(date "+%Y.%m.%d")

dump_dir=/data/db/dump/$source_env_name/$source_schema_name
echo "Dump Dir :" "$dump_dir"
mkdir -p $dump_dir
file_name=$source_schema_name

current_time=$(date "+%Y.%m.%d-%H.%M.%S")
echo "Current Time : $current_time"

new_fileName=$file_name.$current_time.sql
echo "New FileName: " "$new_fileName"

dump_file_name=$dump_dir/$new_fileName

echo "Dumping data to file $dump_file_name"

function failure_email {
        failure_message="Failed to copy data from schema $source_env_name : $source_schema_name to schema $target_env_name : $target_schema_name"
        aws sns publish --topic-arn arn:aws:sns:eu-west-1:427994371464:DATA_COPY_FAILURE_EMAIL --subject "DB Dump Failure for $current_date" --message "$failure_message"
}

function success_email {
        success_message="Data copied successfully from schema $source_env_name : $source_schema_name to schema $target_env_name : $target_schema_name"
        aws sns publish --topic-arn arn:aws:sns:eu-west-1:427994371464:DATA_COPY_SUCCESS_EMAIL --subject "DB Dump Successful for $current_date" --message "$success_message"
}

if ./dump_data.sh $source_env_name $source_schema_name $dump_file_name
then
        echo "Data dump completed successfully from $source_env_name : $source_schema_name and the file is created at $dump_file_name"
        echo "Now Copying the data to the target database  $target_env_name : $target_schema_name"

        if ./copy_data.sh $target_env_name $target_schema_name $dump_file_name
        then
                echo "Data copied successfully to  $target_env_name : $target_schema_name"
                success_email
        else
                echo "Failed to copy data to  $target_env_name : $target_schema_name"
                failure_email
                exit
        fi

                if [ $current_day = "SUN" ];
                then
                target_schema_name=KETTLE_DUMP_$current_week
                echo "copying data for weekly copy to $target_schema_name "
                if ./copy_data.sh $target_env_name $target_schema_name $dump_file_name
                        then
                echo "Data copied successfully to  $target_env_name : $target_schema_name"
                success_email
                else
                failure_email
                exit
        fi
                fi

else
        echo -e "mysqldump failed at $(date +'%d-%m-%Y %H:%M:%S')"$'\r' >> $dump_file_name.log
        failure_email
fi
