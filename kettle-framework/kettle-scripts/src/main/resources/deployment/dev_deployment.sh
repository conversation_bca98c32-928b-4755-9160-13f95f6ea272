#!/bin/sh

echo "Deploying The module at source location $1 and module $2 and sub module $3 and version $4"

cd $1
echo "############Stashing Any Local Changes############"
git stash
echo "############Pulling out new changes############"
git pull
echo "############Running Build Process############"
cd $1/$2
mvn clean install -Dmaven.test.skip=true
STATUS=$?
if [ $STATUS -eq 0 ]; then
echo "############Deployment Successful############"
cd /usr/share/tomcat8/webapps
echo "############Stopping Tomcat Server############"
service tomcat8 stop
echo "############Copying The war file############"
cp $1/$2/$3/target/$3-$4.war ./$3.war
echo "############Starting Tomcat Server############"
service tomcat8 start
else
echo "############Deployment Failed############"
fi

