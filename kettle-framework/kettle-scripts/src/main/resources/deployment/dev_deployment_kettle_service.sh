#!/bin/sh

cd /home/<USER>/application/chaayos/kettle-framework
echo "############Stashing Any Local Changes############"
git stash
echo "############Pulling out new changes############"
git pull
echo "############Running Build Process############"
mvn clean install -Dmaven.test.skip=true
STATUS=$?
if [ $STATUS -eq 0 ]; then
echo "############Deployment Successful############"
cd /usr/share/tomcat8/webapps
echo "############Stopping Tomcat Server############"
service tomcat8 stop
echo "############Copying The war file############"
cp /home/<USER>/application/chaayos/kettle-framework/kettle-service/target/kettle-service-1.2.0.war ./kettle-service.war
echo "############Starting Tomcat Server############"
service tomcat8 start
else
echo "############Deployment Failed############"
fi
