package com.stpl.tech.loggingservice.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.loggingservice.core.LoggingUtil;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkAssemblyTaskLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkConsumptionLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkStatusLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkTaskStatusLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.UnitMonkTaskSummaryDataDao;
import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyOrderLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkConsumptionLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkFileUploadType;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskSummary;
import com.stpl.tech.loggingservice.model.chaimonk.log.UnitMonkTaskSummaryData;
import com.stpl.tech.loggingservice.reporting.mapper.MonkLogDataHelper;
import com.stpl.tech.loggingservice.reporting.mapper.ShiftViewForUnit;
import com.stpl.tech.loggingservice.reporting.model.MonkCumulativeReport;
import com.stpl.tech.loggingservice.reporting.model.MonkOpsReport;
import com.stpl.tech.loggingservice.reporting.model.MonkReport;
import com.stpl.tech.loggingservice.reporting.model.MonkTaskMtdView;
import com.stpl.tech.loggingservice.reporting.model.MonkTaskOpsView;
import com.stpl.tech.loggingservice.reporting.notification.MonkReportNotification;
import com.stpl.tech.loggingservice.service.LoggingService;
import com.stpl.tech.loggingservice.util.KettleLoggingUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.io.IOUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeFieldType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class LoggingServiceImpl implements LoggingService {

    private static final Logger LOG = LoggerFactory.getLogger(LoggingServiceImpl.class);

    private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();

    private static final Gson GSON = LoggingUtil.getCustomSerializer();

    @Autowired
    private UnitMonkTaskSummaryDataDao monkTaskDao;

    @Autowired
    private MonkConsumptionLogDao consumptionLogDao;

    @Autowired
    private MonkTaskStatusLogDao taskStatusLogDao;

    @Autowired
    private MonkAssemblyTaskLogDao monkAssemblyTaskLogDao;

    @Autowired
    private MonkStatusLogDao statusLogDao;

    @Autowired
    private LoggingProperties props;

    @Autowired
    private MasterDataCache masterDataCache;

    @Override
    public boolean addMonkConsumptionLogData(List<MonkConsumptionLog> consumptionLogList) {
        for (MonkConsumptionLog consumptionLog : consumptionLogList) {
            try {
                consumptionLogDao.save(consumptionLog);
            } catch (Exception e) {
                LOG.error("Error while saving consumption log from monk", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean addMonkStatusLogData(List<MonkStatusLog> statusLogList) {
        for (MonkStatusLog log : statusLogList) {
            try {
                statusLogDao.save(log);
            } catch (Exception e) {
                LOG.error("Error while saving status log from monk", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean addMonkTaskStatusLogData(List<MonkTaskStatusLog> taskStatusLogList) {
        for (MonkTaskStatusLog log : taskStatusLogList) {
            try {
                taskStatusLogDao.save(log);
            } catch (Exception e) {
                LOG.error("Error while saving consumption log from monk", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean addAssemblyOrderLog(List<AssemblyOrderLog> orderLogList) {
        for (AssemblyOrderLog log : orderLogList) {
            try {
                monkAssemblyTaskLogDao.save(log);
            } catch (Exception e) {
                LOG.error("Error while saving order log from assembly screen", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public void uploadLogFile(MultipartFile file, Integer unitId, MonkFileUploadType fileType) throws IOException {
        uploadFileToPath(file, unitId, fileType);
        String fileContent = new String(file.getBytes());
        LOG.info(fileContent);
        try {
            String[] objects = fileContent.split("#END");
            for (int i = 0; i < objects.length; i++) {
                String object = objects[i];
                switch (fileType) {
                    case STATUS_LOG:
                        saveStatusLog(object);
                        break;
                    case CONSUMPTION_LOG:
                        saveConsumptionLog(object);
                        break;
                    case TASK_STATUS_LOG:
                        saveTaskStatusLog(object);
                        break;
                    default:
                        saveStatusLog(object);
                        break;
                }
            }

            UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
            StringBuilder message = new StringBuilder(":::: MONK FILE UPLOAD SUCCESS ALARM NOTIFICATION :::: \n");
            message.append("UNIT ID :: ").append(unitBasicDetail.getId()).append("\n");
            message.append("UNIT NAME :: ").append(unitBasicDetail.getName()).append("\n");
            message.append(":::::::::::::::::::::::::::::::::\n");
            KettleLoggingUtil.sendSlackMessage(props.getEnvironmentType(),message, SlackNotification.MONK_SYNC_ERRORS);
        } catch (Exception e) {
            UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
            StringBuilder message = new StringBuilder(":::: MONK FILE UPLOAD FAILURE ALARM NOTIFICATION :::: \n");
            message.append("UNIT ID :: ").append(unitBasicDetail.getId()).append("\n");
            message.append("UNIT NAME :: ").append(unitBasicDetail.getName()).append("\n");
            message.append("ERROR MESSAGE :::::::\n").append(e.getMessage());
            message.append(":::::::::::::::::::::::::::::::::\n");
            KettleLoggingUtil.sendSlackMessage(props.getEnvironmentType(),message, SlackNotification.MONK_SYNC_ERRORS);
        }
    }

    @Override
    public void sendMonkLogs(Date startDate, Date endDate) {
        Map<Integer, Unit> units = masterDataCache.getUnits();
        for (Integer unitId : units.keySet()) {
            Unit unit = units.get(unitId);
            try {
                if (unit.isWorkstationEnabled()) {
                    sendMonkLogs(endDate, unitId);
                }
            } catch (Exception e) {
                StringBuilder message = new StringBuilder(":::: MONK LOGS DAILY JOB FAILURE NOTIFICATION :::: \n");
                message.append("UNIT ID :: ").append(unit.getId()).append("\n");
                message.append("UNIT NAME :: ").append(unit.getName()).append("\n");
                message.append("ERROR MESSAGE :::::::\n").append(e.getMessage());
                message.append(":::::::::::::::::::::::::::::::::\n");
                KettleLoggingUtil.sendSlackMessage(props.getEnvironmentType(),message, SlackNotification.MONK_SYNC_ERRORS);
            }
        }
    }

    @Override
    public void sendMonkLogs(Date date, Integer unitId) {
        Unit unit = masterDataCache.getUnit(unitId);
        sendMonkLogs(date, date, unit);
    }

    @Override
    public void sendMonkLogs(Integer unitId, Date startDate, Date endDate) {
        Unit unit = masterDataCache.getUnit(unitId);
        sendMonkLogs(startDate, endDate, unit);
    }


    @Override
    public void sendMonkMachineLogs(Date date) {
        Map<Integer, Unit> units = masterDataCache.getUnits();
        for (Integer unitId : units.keySet()) {
            Unit unit = units.get(unitId);
            try {
                if (unit.isWorkstationEnabled()) {
                    sendMonkMachineLogs(date, unitId);
                }
            } catch (Exception e) {
                StringBuilder message = new StringBuilder(":::: MONK MACHINE LOGS DAILY JOB FAILURE NOTIFICATION :::: \n");
                message.append("UNIT ID :: ").append(unit.getId()).append("\n");
                message.append("UNIT NAME :: ").append(unit.getName()).append("\n");
                message.append("ERROR MESSAGE :::::::\n").append(e.getMessage());
                message.append(":::::::::::::::::::::::::::::::::\n");
                KettleLoggingUtil.sendSlackMessage(props.getEnvironmentType(), message, SlackNotification.MONK_SYNC_ERRORS);
            }
        }
    }

    @Override
    public void sendMonkMachineLogs(Date date, Integer unitId) {
        Unit unit = masterDataCache.getUnit(unitId);
        sendMonkMachineLogs(date, date, unit);
    }

    @Override
    public void sendMonkMachineLogs(Integer unitId, Date startTime, Date endTime) {
        Unit unit = masterDataCache.getUnit(unitId);
        sendMonkMachineLogs(startTime, endTime, unit);
    }

    @Override
    public void sendCumulativeMonkLogs(Date startDate, Date endDate, List<String>toMails) throws IOException, EmailGenerationException {
        long startTime = AppUtils.getStartOfBusinessDay(startDate).getTime();
        long endTime = AppUtils.getEndOfBusinessDay(endDate).getTime();
        List<MonkTaskOpsView> tasksTillNow = new ArrayList<>();
        for(Unit unit : masterDataCache.getUnits().values()){
            if(unit.isWorkstationEnabled() && unit.isHotAndColdMerged()){ // this condition means monk is deployed
                processMonkLogs(startTime, endTime, unit);
                tasksTillNow.addAll(getMtdData(unit.getId(), startDate, endDate));
            }
        }
        MonkCumulativeReport view = new MonkCumulativeReport(ctxFactory, tasksTillNow, startDate, endDate, props.getBasePath());
        view.renderTaskView();
        sendNotification(view, toMails);
    }

    private void sendMonkMachineLogs(Date startDate, Date endDate, Unit unit){ }


    private void sendMonkLogs(Date startDate, Date endDate, Unit unit) {
        long startTime = AppUtils.getStartOfBusinessDay(startDate).getTime();
        long endTime = AppUtils.getEndOfBusinessDay(endDate).getTime();
        List<MonkTaskOpsView> taskSummaryList = processMonkLogs(startTime, endTime, unit);
        List<ShiftViewForUnit> shifts = getShiftsForUnitAndDate(unit, startDate, startTime, endTime);
        MonkOpsReport view = new MonkOpsReport(ctxFactory, taskSummaryList, startDate, endDate, props.getBasePath(), unit);
        try {
            List<MonkTaskOpsView> tasksTillNow = getMtdData(unit.getId(),startDate,endDate);
            MonkTaskMtdView mtd = MonkLogDataHelper.processMTDView(tasksTillNow);
            MonkTaskMtdView today = MonkLogDataHelper.processMTDView(taskSummaryList);
            view.renderSummaryView(mtd, today);
            view.renderShiftSummaries(shifts);
            view.renderTaskView();
            sendNotification(view, toMails(view));
        } catch (IOException e) {
            LOG.error("Encountered IO Exception while trying to send Monk Logs {} {}", unit.getId(), startDate, e);
        } catch (EmailGenerationException e) {
            LOG.error("Encountered Email Generation Exception while trying to send Monk Logs {} {}", unit.getId(), startDate, e);
        }
    }

    public List<MonkTaskOpsView> getMtdData(Integer unitId, Date startDate, Date endDate) {
        Date startOfMonth = AppUtils.getStartOfMonth(startDate.getYear(),startDate.getMonth());
        Date end = AppUtils.getEndOfBusinessDay(endDate);
        List<UnitMonkTaskSummaryData> summariesTillDate = monkTaskDao.findByBusinessDate(unitId,startOfMonth,end);
        List<MonkTaskOpsView> allSummaries = new ArrayList<>();
        for(UnitMonkTaskSummaryData unit : summariesTillDate){
            List<MonkTaskOpsView> opsView = unit.getTaskSummaryList().stream()
                    .map(MonkTaskOpsView::new)
                    .collect(Collectors.toList());
            allSummaries.addAll(opsView);
        }
        return allSummaries;
    }

    private List<ShiftViewForUnit> getShiftsForUnitAndDate(Unit unit, Date businessDate, long startTime, long endTime) {
        List<ShiftViewForUnit> shifts = new ArrayList<>();
        DateTime dateTime = new DateTime(businessDate);
        int dayOfWeek = dateTime.get(DateTimeFieldType.dayOfWeek());
        List<UnitHours> operationalHours = unit.getOperationalHours();
        Optional<UnitHours> hours = operationalHours.stream()
                .filter(unitHours -> unitHours.getDayOfTheWeekNumber() == dayOfWeek)
                .findFirst();
        if (hours.isPresent()) {
            UnitHours unitHours = hours.get();
            int noOfShifts = unitHours.getNoOfShifts();
            List<Long> handOverTimes = new ArrayList<>();
            handOverTimes.add(startTime);
            Date shiftOneHandOver = LoggingUtil.addTimeToDate(businessDate,unitHours.getShiftOneHandoverTime());
            handOverTimes.add(shiftOneHandOver.getTime());
            if (unitHours.getShiftTwoHandoverTime() != null) {
                Date shiftTwoHandOver = LoggingUtil.addTimeToDate(businessDate, unitHours.getShiftTwoHandoverTime());
                handOverTimes.add(shiftTwoHandOver.getTime());
            }
            handOverTimes.add(endTime);
            for (int i = 0; i < noOfShifts; i++) {
                int j = i + 1;
                long shiftStart = handOverTimes.get(i);
                long shiftEnd = handOverTimes.get(j);
                ShiftViewForUnit shift = new ShiftViewForUnit(j, shiftStart, shiftEnd);
                shifts.add(shift);
            }
        }
        return shifts;
    }

    private List<MonkTaskOpsView> processMonkLogs(long startTime, long endTime, Unit unit) {
        List<MonkTaskSummary> taskSummaryList = getTaskSummaries(startTime, endTime, unit);
        List<MonkTaskOpsView> opsViewList = taskSummaryList.stream()
                .map(MonkTaskOpsView::new).collect(Collectors.toList());
        return opsViewList;
    }

    private List<MonkTaskSummary> getTaskSummaries(long startTime, long endTime, Unit unit){
        List<MonkTaskSummary> taskSummaryList = new ArrayList<>();
        List<MonkTaskStatusLog> taskStatusLogs = taskStatusLogDao.findMonkTaskStatusLogs(unit.getId(), startTime, endTime);
        if (taskStatusLogs != null) {
            List<Integer> taskIds = taskStatusLogs.stream()
                    .mapToInt(MonkTaskStatusLog::getTaskId)
                    .distinct().boxed().collect(Collectors.toList());
            List<MonkStatusLog> statusLogs = statusLogDao.getMonkStatusLogforTasks(taskIds);
            taskSummaryList = MonkLogDataHelper.processLogs(statusLogs, taskStatusLogs, taskSummaryList, unit);
            saveTaskSummaries(taskSummaryList, unit, AppUtils.getBusinessDate(endTime));
        }
        return taskSummaryList;
    }

    private void saveTaskSummaries(List<MonkTaskSummary> taskSummaries, Unit unit, Date businessDate){
        UnitMonkTaskSummaryData summaryData = monkTaskDao.findDistinctFirstByBusinessDateAndUnitId(businessDate,unit.getId());
        if(summaryData == null){
            summaryData = new UnitMonkTaskSummaryData();
            summaryData.setBusinessDate(businessDate);
            summaryData.setUnitId(unit.getId());
            summaryData.setUnitName(unit.getName());
            summaryData.setTaskSummaryList(taskSummaries);
            monkTaskDao.save(summaryData);
        }else {
            StringBuilder message = new StringBuilder("Failed to save task summary list for ")
                    .append(unit.getName()).append(" and business date").append(businessDate);
            KettleLoggingUtil.sendSlackMessage(props.getEnvironmentType(),message,SlackNotification.MONK_SYNC_ERRORS);
        }
    }

    private void saveStatusLog(String object) {
        MonkStatusLog statusLog = GSON.fromJson(object, MonkStatusLog.class);
        statusLog.setUploadTime(AppUtils.getCurrentTimestamp());
        statusLogDao.save(statusLog);
    }

    private void saveConsumptionLog(String object) {
        MonkConsumptionLog log = GSON.fromJson(object, MonkConsumptionLog.class);
        log.setUploadTime(AppUtils.getCurrentTimestamp());
        consumptionLogDao.save(log);
    }

    private void saveTaskStatusLog(String object) {
        MonkTaskStatusLog log = GSON.fromJson(object, MonkTaskStatusLog.class);
        log.setUploadTime(AppUtils.getCurrentTimestamp());
        taskStatusLogDao.save(log);
    }

    private String uploadFileToPath(MultipartFile file, Integer unitId, MonkFileUploadType type) throws IOException {
        byte[] bytes = file.getBytes();
        // Creating the directory to store file
        String rootPath = props.getBasePath();
        String parentDir = "monkLogFiles" + File.separator + unitId + File.separator + type;
        String name = "MONK_LOG_FILE_" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".log";
        return AppUtils.write(bytes, rootPath, parentDir, name, LOG);
    }

    private List<String> toMails(MonkReport view){
        Unit unit = view.getUnit();
        List<String> toMails = new ArrayList<>();
        toMails.add(unit.getUnitEmail());
        toMails.add(unit.getManagerEmail());
        if(unit.getCafeManager()!=null){ // adding deputy area cafe manager email id if present
            EmployeeBasicDetail cafeManager = masterDataCache.getEmployeeBasicDetail(unit.getCafeManager().getId());
            toMails.add(cafeManager.getEmailId());
        }
        return toMails;
    }

    private void sendNotification(MonkReport view, List<String> toMails) throws IOException, EmailGenerationException {
        MonkReportNotification notification = new MonkReportNotification(view, props.getEnvironmentType(), toMails, view.getName());
        view.writeReport();
        List<AttachmentData> attachments = new ArrayList<>();
        if (view.isGenerated()) {
            File file = new File(view.getFilePath());
            if (file.exists()) {
                AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)),
                        view.getFileName(), notification.getMimeType());
                attachments.add(reports);
                notification.sendRawMail(attachments);
            } else {
                StringBuilder message = new StringBuilder("Failed to send monk log report mail \n");
                message.append("Unit Details :: " + view.getUnit().getName() + "\n");
                SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Kettle",
                        SlackNotification.SYSTEM_ERRORS, message.toString());
            }
        }
    }
}
