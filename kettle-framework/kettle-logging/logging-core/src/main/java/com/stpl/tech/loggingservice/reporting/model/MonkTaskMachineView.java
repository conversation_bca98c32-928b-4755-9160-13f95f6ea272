package com.stpl.tech.loggingservice.reporting.model;

import com.stpl.tech.loggingservice.model.chaimonk.log.FatalErrorCode;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by s<PERSON><PERSON> on 29-03-2018.
 */
public class MonkTaskMachineView implements MonkTaskView {

    @Override
    public int getOrderId() {
        return 0;
    }

    @Override
    public int getNumberOfBoils() {
        return 0;
    }

    @Override
    public FatalErrorCode getFatal() {
        return null;
    }

    @Override
    public double getDeviationFromIdeal() {
        return 0;
    }

    @Override
    public int getQuantity() {
        return 0;
    }

    @Override
    public double getPrepTime() {
        return 0;
    }

    @Override
    public long getBillServerTime() {
        return 0;
    }

    @Override
    public double getWaitingTime() {
        return 0;
    }

    @Override
    public double getPickUpTime() {
        return 0;
    }

    @Override
    public double getAddPattiTime() {
        return 0;
    }
}
