package com.stpl.tech.loggingservice.model.chaimonk.log;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 10-01-2019.
 */

@Entity
@Document
public class MonkTaskStatusDetailLog implements BasicLogDetail,Serializable {

    @Id
    private String _id;

    private String id;
    @Indexed
    private int orderId;
    @Indexed
    private int orderItemId;
    @Indexed
    private Date orderCreationTime;
    @Indexed
    private long orderCreationTimeinMillis;
    
    @Indexed
    private int  linkedTaskId;
    private long orderAcknowledgeTime; // order acknowleged at assembly
    private long taskCreationTime; //task received first time at monk
    @Indexed
    private int unitId;
    @Indexed
    private String unitName;
    @Indexed
    private int pId;
    @Indexed
    private String pName;
    @Indexed
    private String dimension;
    @Indexed
    private int quantity;
    private boolean isSteepedChai;
    private int remakeCount;
    private boolean isManualOrder;
    private boolean isSplittedOrder;
    private boolean isManuallyHandled;

    //Allocation Data

    private String monkName;
    private int freeMonks;
    private int totalMonks;
    private int bulkMonks;
    private boolean isBulkOrder;
    private boolean isBulkMonkAllocated;
    private long waitTime;

    //In Process Data

    private boolean failure;
    private String  failureReason;
    private double milkQuantityDispensed;
    private double waterQuantityDispensed;
    private long waterPourTime;
    private long milkPourTime;
    private long addPattiTime;
    private long brewingTime;
    private long pickUpTime;
    private long processingTime;
    private long totalTime;

    // PreAllocation Data
    private int noOfAttempts;
    private List<MonkPreAllocationLog> monkPreAllocationLogs;

    private String monkPayload;

    private Date uploadTime;


    private String skId;

    private String className;
    

    public long getOrderCreationTimeinMillis() {
		return orderCreationTimeinMillis;
	}

	public void setOrderCreationTimeinMillis(long orderCreationTimeinMillis) {
		this.orderCreationTimeinMillis = orderCreationTimeinMillis;
	}

	public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public int getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(int orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Date getOrderCreationTime() {
        return orderCreationTime;
    }

    public void setOrderCreationTime(Date orderCreationTime) {
        this.orderCreationTime = orderCreationTime;
    }

    public int getLinkedTaskId() {
        return linkedTaskId;
    }

    public void setLinkedTaskId(int linkedTaskId) {
        this.linkedTaskId = linkedTaskId;
    }

    public long getOrderAcknowledgeTime() {
        return orderAcknowledgeTime;
    }

    public void setOrderAcknowledgeTime(long orderAcknowledgeTime) {
        this.orderAcknowledgeTime = orderAcknowledgeTime;
    }

    public long getTaskCreationTime() {
        return taskCreationTime;
    }

    public void setTaskCreationTime(long taskCreationTime) {
        this.taskCreationTime = taskCreationTime;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public int getpId() {
        return pId;
    }

    public void setpId(int pId) {
        this.pId = pId;
    }

    public String getpName() {
        return pName;
    }

    public void setpName(String pName) {
        this.pName = pName;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public boolean isSteepedChai() {
        return isSteepedChai;
    }

    public void setSteepedChai(boolean steepedChai) {
        isSteepedChai = steepedChai;
    }

    public int getRemakeCount() {
        return remakeCount;
    }

    public void setRemakeCount(int remakeCount) {
        this.remakeCount = remakeCount;
    }

    public boolean isManualOrder() {
        return isManualOrder;
    }

    public void setManualOrder(boolean manualOrder) {
        isManualOrder = manualOrder;
    }

    public boolean isSplittedOrder() {
        return isSplittedOrder;
    }

    public void setSplittedOrder(boolean splittedOrder) {
        isSplittedOrder = splittedOrder;
    }

    public String getMonkName() {
        return monkName;
    }

    public void setMonkName(String monkName) {
        this.monkName = monkName;
    }

    public int getFreeMonks() {
        return freeMonks;
    }

    public void setFreeMonks(int freeMonks) {
        this.freeMonks = freeMonks;
    }

    public int getTotalMonks() {
        return totalMonks;
    }

    public void setTotalMonks(int totalMonks) {
        this.totalMonks = totalMonks;
    }

    public int getBulkMonks() {
        return bulkMonks;
    }

    public void setBulkMonks(int bulkMonks) {
        this.bulkMonks = bulkMonks;
    }

    public boolean isBulkOrder() {
        return isBulkOrder;
    }

    public void setBulkOrder(boolean bulkOrder) {
        isBulkOrder = bulkOrder;
    }

    public boolean isBulkMonkAllocated() {
        return isBulkMonkAllocated;
    }

    public void setBulkMonkAllocated(boolean bulkMonkAllocated) {
        isBulkMonkAllocated = bulkMonkAllocated;
    }

    public long getWaitTime() {
        return waitTime;
    }

    public void setWaitTime(long waitTime) {
        this.waitTime = waitTime;
    }

    public boolean isFailure() {
        return failure;
    }

    public void setFailure(boolean failure) {
        this.failure = failure;
    }

    public String getFailureReason() {
        return failureReason;
    }

    public void setFailureReason(String failureReason) {
        this.failureReason = failureReason;
    }

    public double getMilkQuantityDispensed() {
        return milkQuantityDispensed;
    }

    public void setMilkQuantityDispensed(double milkQuantityDispensed) {
        this.milkQuantityDispensed = milkQuantityDispensed;
    }

    public double getWaterQuantityDispensed() {
        return waterQuantityDispensed;
    }

    public void setWaterQuantityDispensed(double waterQuantityDispensed) {
        this.waterQuantityDispensed = waterQuantityDispensed;
    }

    public long getWaterPourTime() {
        return waterPourTime;
    }

    public void setWaterPourTime(long waterPourTime) {
        this.waterPourTime = waterPourTime;
    }

    public long getMilkPourTime() {
        return milkPourTime;
    }

    public void setMilkPourTime(long milkPourTime) {
        this.milkPourTime = milkPourTime;
    }

    public long getAddPattiTime() {
        return addPattiTime;
    }

    public void setAddPattiTime(long addPattiTime) {
        this.addPattiTime = addPattiTime;
    }

    public long getBrewingTime() {
        return brewingTime;
    }

    public void setBrewingTime(long brewingTime) {
        this.brewingTime = brewingTime;
    }

    public long getPickUpTime() {
        return pickUpTime;
    }

    public void setPickUpTime(long pickUpTime) {
        this.pickUpTime = pickUpTime;
    }

    public long getProcessingTime() {
        return processingTime;
    }

    public void setProcessingTime(long processingTime) {
        this.processingTime = processingTime;
    }

    public long getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }

    public int getNoOfAttempts() {
        return noOfAttempts;
    }

    public void setNoOfAttempts(int noOfAttempts) {
        this.noOfAttempts = noOfAttempts;
    }

    public List<MonkPreAllocationLog> getMonkPreAllocationLogs() {
        return monkPreAllocationLogs;
    }

    public void setMonkPreAllocationLogs(List<MonkPreAllocationLog> monkPreAllocationLogs) {
        this.monkPreAllocationLogs = monkPreAllocationLogs;
    }

    public boolean isManuallyHandled() {
        return isManuallyHandled;
    }

    public void setManuallyHandled(boolean manuallyHandled) {
        isManuallyHandled = manuallyHandled;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public String getMonkPayload() {
        return monkPayload;
    }

    public void setMonkPayload(String monkPayload) {
        this.monkPayload = monkPayload;
    }

    @Override
    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public String getSkId() {
        return skId;
    }

    public void setSkId(String skId) {
        this.skId = skId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }
}
