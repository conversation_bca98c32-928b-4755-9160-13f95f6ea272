/*
 * Created By Shanmukh
 */

package com.stpl.tech.loggingservice.model.chaimonk.log;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.stpl.tech.kettle.domain.model.ChaiMonkXTwo;
import com.stpl.tech.kettle.domain.model.WorkTask;
import com.stpl.tech.util.domain.adapter.DateDeserializer;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;
import lombok.Data;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@Document
@JsonIgnoreProperties(ignoreUnknown=true)
public class MonkXTwoLog {

    @JsonProperty("_id")
    @Id
    protected String _id;

    @JsonProperty("chaiMonkMap")
    private ChaiMonkXTwo chaiMonkMap;

    @JsonProperty("workTask")
    private WorkTask workTask;

    @JsonProperty("unitId")
    private Integer unitId;

    @JsonProperty("webAppVersion")
    private String webAppVersion;

    @JsonProperty("logAddTime")
    private Date logAddTime;

    @JsonProperty("recipeVersionAtMonk")
    private String recipeVersionAtMonk;

    @JsonProperty("recipeRegionAtMonk")
    private String recipeRegionAtMonk;

    @JsonProperty("recipeStepVersion")
    private String recipeStepVersion;
}
