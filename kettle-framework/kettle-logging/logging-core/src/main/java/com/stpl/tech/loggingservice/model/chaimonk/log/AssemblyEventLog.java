//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.11.02 at 02:05:53 PM IST 
//


package com.stpl.tech.loggingservice.model.chaimonk.log;

import com.stpl.tech.master.domain.model.Adapter2;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;

/**
 * <p>Java class for AssemblyEventLog complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AssemblyEventLog"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="eventType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="wsHotState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="wsColdState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="wsFoodState" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="eventTimeStamp" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AssemblyEventLog", propOrder = {
    "eventType",
    "unitId",
    "wsHotState",
    "wsColdState",
    "wsFoodState",
    "comment",
    "eventTimeStamp"
})
public class AssemblyEventLog {

    @XmlElement(required = true)
    protected String eventType;
    protected int unitId;
    @XmlElement(required = true)
    protected String wsHotState;
    @XmlElement(required = true)
    protected String wsColdState;
    @XmlElement(required = true)
    protected String wsFoodState;
    @XmlElement(required = true)
    protected String comment;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date eventTimeStamp;

    /**
     * Gets the value of the eventType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEventType() {
        return eventType;
    }

    /**
     * Sets the value of the eventType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEventType(String value) {
        this.eventType = value;
    }

    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the wsHotState property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWsHotState() {
        return wsHotState;
    }

    /**
     * Sets the value of the wsHotState property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWsHotState(String value) {
        this.wsHotState = value;
    }

    /**
     * Gets the value of the wsColdState property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWsColdState() {
        return wsColdState;
    }

    /**
     * Sets the value of the wsColdState property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWsColdState(String value) {
        this.wsColdState = value;
    }

    /**
     * Gets the value of the wsFoodState property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWsFoodState() {
        return wsFoodState;
    }

    /**
     * Sets the value of the wsFoodState property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWsFoodState(String value) {
        this.wsFoodState = value;
    }

    /**
     * Gets the value of the comment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the eventTimeStamp property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getEventTimeStamp() {
        return eventTimeStamp;
    }

    /**
     * Sets the value of the eventTimeStamp property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEventTimeStamp(Date value) {
        this.eventTimeStamp = value;
    }

}
