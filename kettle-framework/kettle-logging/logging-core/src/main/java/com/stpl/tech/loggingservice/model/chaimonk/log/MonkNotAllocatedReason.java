package com.stpl.tech.loggingservice.model.chaimonk.log;

public enum MonkNotAllocatedReason {
    INACTIVE(1),
    PAN_NOT_PLACED(2),
    INSUFFICIENT_WATER(3),
    INSUFFICIENT_MILK(4),
    DIRTY_SENSOR(5),
    <PERSON><PERSON><PERSON>(6),
    UNDER_CLEANING(7),
    UNDER_REFILLING(8),
    BUSY(9);

    private final int priority;

    private MonkNotAllocatedReason(int priority) {
        this.priority = priority;
    }

    public int getPriority() {
        return priority;
    }
}
