package com.stpl.tech.loggingservice.reporting.model;

import com.stpl.tech.loggingservice.reporting.mapper.ShiftViewForUnit;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.ExcelWriter;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 15-02-2019.
 */
public class MonkCumulativeReport extends MonkReport {
    private WorkbookContext workbookCtx;
    private String filePath;
    private Date startDate;
    private Date endDate;
    private boolean generated = false;
    private String fileName;
    private ExcelWriter writer;

    private List<MonkTaskOpsView> opsTaskView;

    public MonkCumulativeReport(WorkbookContextFactory factory, List<MonkTaskOpsView> opsTaskView,
                                Date start, Date end, String baseDir) {
        this.workbookCtx = factory.createWorkbook();
        this.opsTaskView = opsTaskView;
        this.startDate = start;
        this.endDate = end;
        this.fileName = "CHAI_MONK_CUMULATIVE_LOGS_" + AppUtils.getCurrentTimeISTStringWithNoColons();
        this.filePath = baseDir + File.separator + "MONK" + File.separator + "CUMULATIVE_LOGS";
        this.writer = new ExcelWriter(getWorkbookCtx().toNativeWorkbook());
    }

    public WorkbookContext getWorkbookCtx() {
        return workbookCtx;
    }

    public String getName() {
        return "CHAI MONK CUMULATIVE LOGS";
    }

    public String getFilePath() {
        return filePath;
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public boolean isGenerated() {
        return generated;
    }

    public String getFileName() {
        return fileName;
    }


    @Override
    public void renderTaskView() {
        writer.writeSheet(opsTaskView, MonkTaskOpsView.class);
    }

    @Override
    public void renderSummaryView(MonkTaskMtdView mtd, MonkTaskMtdView today) {}

    @Override
    public void renderShiftSummaries(List<ShiftViewForUnit> shifts) {}

    @Override
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public void setGenerated(boolean value) {
        this.generated = value;
    }

    @Override
    public Unit getUnit() {
        return null;
    }
}