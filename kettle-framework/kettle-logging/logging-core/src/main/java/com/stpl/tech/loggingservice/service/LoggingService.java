package com.stpl.tech.loggingservice.service;

import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyOrderLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkConsumptionLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkFileUploadType;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import com.stpl.tech.util.EmailGenerationException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;

@Service
public interface LoggingService {

    public boolean addMonkConsumptionLogData(List<MonkConsumptionLog> consumptionLogList);

    public boolean addMonkStatusLogData(List<MonkStatusLog> statusLogList);

    public boolean addMonkTaskStatusLogData(List<MonkTaskStatusLog> taskStatusLogList);

    public boolean addAssemblyOrderLog(List<AssemblyOrderLog> taskLogList);

    public void uploadLogFile(MultipartFile file, Integer unitId, MonkFileUploadType fileType) throws IOException;

    public void sendMonkLogs(Date startDate, Date endDate);

    public void sendMonkLogs(Date date, Integer unitId);

    public void sendMonkLogs(Integer unitId, Date startTime, Date endTime);

    public void sendMonkMachineLogs(Date date);

    public void sendMonkMachineLogs(Date date, Integer unitId);

    public void sendMonkMachineLogs(Integer unitId, Date startTime, Date endTime);

    public void sendCumulativeMonkLogs(Date startTime, Date endTime, List<String> toMails) throws IOException, EmailGenerationException;
}
