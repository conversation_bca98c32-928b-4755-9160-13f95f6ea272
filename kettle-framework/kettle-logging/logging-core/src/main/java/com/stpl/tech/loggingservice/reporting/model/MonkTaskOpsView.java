package com.stpl.tech.loggingservice.reporting.model;

import com.stpl.tech.loggingservice.core.LoggingConstants;
import com.stpl.tech.loggingservice.model.chaimonk.log.FatalErrorCode;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskSummary;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 29-03-2018.
 */

@ExcelSheet(value = "Task Breakdown Sheet")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class MonkTaskOpsView implements MonkTaskView {

    @ExcelField
    private int orderId;
    @ExcelField
    private int taskId;
    @ExcelField
    private String product;
    @ExcelField
    private String dimension;
    @ExcelField
    private int quantity;
    @ExcelField
    private double waitingTime;
    @ExcelField
    private double waitingExceededBy;
    @ExcelField
    private double addPattiTime;
    @ExcelField
    private double addPattiExceededBy;
    //@ExcelField
    private double pickUpTime;
    //@ExcelField
    private double pickUpExceededBy;
    @ExcelField
    private FatalErrorCode fatalCode;
    @ExcelField
    private double deviationFromIdeal;
    @ExcelField
    private String beforeTime;
    private long billServerTime;
    private double prepTime;
    private FatalErrorCode waitReason;


    public MonkTaskOpsView(MonkTaskSummary monkTaskSummary) {
        this.orderId = monkTaskSummary.getOrderId();
        this.taskId = monkTaskSummary.getTaskId();
        this.product = monkTaskSummary.getProductName();
        this.dimension= monkTaskSummary.getDimension();
        this.quantity = monkTaskSummary.getQuantity();
        if(monkTaskSummary.getFatal()!=null){
            this.fatalCode = FatalErrorCode.valueOf(monkTaskSummary.getFatal());
        }else{
            this.fatalCode = FatalErrorCode.NO_ERROR;
        }
        this.addPattiTime = monkTaskSummary.getAddPattiTime();
        this.addPattiExceededBy = monkTaskSummary.getAddPattiTime() - LoggingConstants.PATTI_THRESHOLD;
        this.waitingTime = monkTaskSummary.getWaitTime();
        this.waitingExceededBy = monkTaskSummary.getWaitTime() - LoggingConstants.WAIT_THRESHOLD;
        this.deviationFromIdeal = monkTaskSummary.getDeviationFromIdeal();
        this.beforeTime = monkTaskSummary.getDeviationFromIdeal() >= 0 ? AppConstants.NO : AppConstants.YES;
        this.pickUpTime = monkTaskSummary.getPickupTime();
        this.pickUpExceededBy = monkTaskSummary.getPickupTime() - LoggingConstants.PICKUP_THRESHOLD;
        this.billServerTime = monkTaskSummary.getBillServerTime();
        this.prepTime = monkTaskSummary.getPrepTime();
        this.waitReason = monkTaskSummary.getWaitReason();
    }


    public int getTaskId() {
        return taskId;
    }

    public double getWaitingTime() {
        return waitingTime;
    }

    public double getWaitingExceededBy() {
        return waitingExceededBy;
    }

    public double getAddPattiTime() {
        return addPattiTime;
    }

    public double getAddPattiExceededBy() {
        return addPattiExceededBy;
    }

    public double getPickUpTime() {
        return pickUpTime;
    }

    public double getPickUpExceededBy() {
        return pickUpExceededBy;
    }

    public FatalErrorCode getFatalCode() {
        return fatalCode;
    }

    public String getBeforeTime() {
        return beforeTime;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }

    public void setWaitingTime(double waitingTime) {
        this.waitingTime = waitingTime;
    }

    public void setWaitingExceededBy(double waitingExceededBy) {
        this.waitingExceededBy = waitingExceededBy;
    }

    public void setAddPattiTime(double addPattiTime) {
        this.addPattiTime = addPattiTime;
    }

    public void setAddPattiExceededBy(double addPattiExceededBy) {
        this.addPattiExceededBy = addPattiExceededBy;
    }

    public void setPickUpTime(double pickUpTime) {
        this.pickUpTime = pickUpTime;
    }

    public void setPickUpExceededBy(double pickUpExceededBy) {
        this.pickUpExceededBy = pickUpExceededBy;
    }

    public void setFatalCode(FatalErrorCode fatalCode) {
        this.fatalCode = fatalCode;
    }

    public void setBeforeTime(String beforeTime) {
        this.beforeTime = beforeTime;
    }

    public String getProduct() {
        return product;
    }

    public String getDimension() {
        return dimension;
    }

    public void setProduct(String product) {

        this.product = product;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public void setDeviationFromIdeal(double deviationFromIdeal) {
        this.deviationFromIdeal = deviationFromIdeal;
    }

    @Override
    public int getOrderId() {
        return this.orderId;
    }

    @Override
    public int getNumberOfBoils() {
        return 0;
    }

    @Override
    public FatalErrorCode getFatal() {
        return fatalCode;
    }

    @Override
    public double getDeviationFromIdeal() {
        return this.deviationFromIdeal;
    }

    @Override
    public int getQuantity() {
        return this.quantity;
    }

    @Override
    public double getPrepTime() {
        return this.prepTime;
    }

    @Override
    public long getBillServerTime() {
        return this.billServerTime;
    }

    public FatalErrorCode getWaitReason() {
        return waitReason;
    }

    public void setWaitReason(FatalErrorCode waitReason) {
        this.waitReason = waitReason;
    }
}
