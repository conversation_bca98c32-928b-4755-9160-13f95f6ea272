package com.stpl.tech.loggingservice.model.chaimonk.log;

import java.util.Date;
import java.util.List;

import javax.persistence.Entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 26-10-2017.
 */

@Entity
@Document
public class AssemblyOrderLog {

    @Id
    protected String _id;

    protected int unitId;
    protected int orderId;
    protected Date billingServerTime;
    protected String source;
    protected String sourceId;
    protected int employeeId;
    protected List<MonkLogOrderItem> orders;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public Date getBillingServerTime() {
        return billingServerTime;
    }

    public void setBillingServerTime(Date billingServerTime) {
        this.billingServerTime = billingServerTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public int getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(int employeeId) {
        this.employeeId = employeeId;
    }

    public List<MonkLogOrderItem> getOrders() {
        return orders;
    }

    public void setOrders(List<MonkLogOrderItem> orders) {
        this.orders = orders;
    }
}
