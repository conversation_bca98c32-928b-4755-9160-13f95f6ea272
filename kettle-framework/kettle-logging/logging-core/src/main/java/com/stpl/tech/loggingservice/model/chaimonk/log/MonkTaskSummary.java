package com.stpl.tech.loggingservice.model.chaimonk.log;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 17-03-2018.
 */
@Document
public class MonkTaskSummary {

    @Id
    private String id;
    @Indexed
    private int unitId;
    @Indexed
    private String productName;
    @Indexed
    private String dimension;
    @Indexed
    private int quantity;
    @Indexed
    private String monkName;
    @Indexed
    private long billServerTime;

    private int orderId;
    private int taskId;
    private String unitName;
    private int numberOfBoils;
    private double prepTime;
    private double targetWeight;
    private double actualWeight;
    private double deviationFromIdeal;
    private long waitTime;
    private double addPattiTime;
    private double targetTime;
    private String fatal;
    private double pickupTime;
    private List<ProcessedMonk> processedMonks;
    private FatalErrorCode waitReason;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public int getTaskId() {
        return taskId;
    }

    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getMonkName() {
        return monkName;
    }

    public void setMonkName(String monkName) {
        this.monkName = monkName;
    }

    public int getNumberOfBoils() {
        return numberOfBoils;
    }

    public void setNumberOfBoils(int numberOfBoils) {
        this.numberOfBoils = numberOfBoils;
    }

    public double getPrepTime() {
        return prepTime;
    }

    public void setPrepTime(double prepTime) {
        this.prepTime = prepTime;
    }

    public double getTargetWeight() {
        return targetWeight;
    }

    public void setTargetWeight(double targetWeight) {
        this.targetWeight = targetWeight;
    }

    public double getActualWeight() {
        return actualWeight;
    }

    public void setActualWeight(double actualWeight) {
        this.actualWeight = actualWeight;
    }

    public double getDeviationFromIdeal() {
        return deviationFromIdeal;
    }

    public void setDeviationFromIdeal(double deviationFromIdeal) {
        this.deviationFromIdeal = deviationFromIdeal;
    }

    public List<ProcessedMonk> getProcessedMonks() {
        if( processedMonks==null ){
            processedMonks = new ArrayList<>();
        }
        return processedMonks;
    }

    public long getBillServerTime() {
        return billServerTime;
    }

    public void setBillServerTime(long billServerTime) {
        this.billServerTime = billServerTime;
    }

    public void setTargetTime(double targetTime) {
        this.targetTime = targetTime;
    }

    public double getTargetTime() {
        return targetTime;
    }

    public long getWaitTime() {
        return waitTime;
    }

    public void setWaitTime(long waitTime) {
        this.waitTime = waitTime;
    }

    public String getFatal() {
        return fatal;
    }

    public void setFatal(String fatal) {
        this.fatal = fatal;
    }

    public void setAddPattiTime(double addPattiTime) {
        this.addPattiTime = addPattiTime;
    }

    public double getAddPattiTime() {
        return addPattiTime;
    }

    public double getPickupTime() {
        return pickupTime;
    }

    public void setPickupTime(double pickupTime) {
        this.pickupTime = pickupTime;
    }

    public FatalErrorCode getWaitReason() {
        return waitReason;
    }

    public void setWaitReason(FatalErrorCode waitReason) {
        this.waitReason = waitReason;
    }
}
