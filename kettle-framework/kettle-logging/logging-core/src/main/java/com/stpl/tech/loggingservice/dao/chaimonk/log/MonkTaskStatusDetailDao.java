package com.stpl.tech.loggingservice.dao.chaimonk.log;

import com.stpl.tech.loggingservice.model.chaimonk.log.MonkConsumptionLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusDetailLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 10-01-2019.
 */
@Repository
public interface MonkTaskStatusDetailDao extends MongoRepository<MonkTaskStatusDetailLog, String> {

    @Query(value = "{'orderItemId': ?0 }",sort = "{'_id': -1}")
    public List<MonkTaskStatusDetailLog> findMonkTaskStatusDetailByItemId(Integer itemId);

    Optional<List<MonkTaskStatusDetailLog>> findByUnitIdAndOrderCreationTimeGreaterThanEqual(int unitId, Date orderCreationTime);


}
