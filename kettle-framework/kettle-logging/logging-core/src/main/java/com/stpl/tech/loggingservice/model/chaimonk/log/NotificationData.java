package com.stpl.tech.loggingservice.model.chaimonk.log;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class NotificationData {

    private List<Integer> unitIds;
    private List<String> notificationType;
    private Integer notificationTriggeredBy;
}
