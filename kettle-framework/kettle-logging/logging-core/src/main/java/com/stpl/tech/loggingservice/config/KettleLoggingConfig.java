package com.stpl.tech.loggingservice.config;

import java.util.TimeZone;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.stpl.tech.master.core.config.MasterExternalConfig;

@Configuration
@EnableScheduling
@EnableTransactionManagement
@ComponentScan(basePackages = {"com.stpl.tech.loggingservice"})
@EnableMongoRepositories(basePackages = "com.stpl.tech.loggingservice.dao")
@Import(value = {MasterExternalConfig.class})
public class KettleLoggingConfig {

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	public KettleLoggingConfig() {
		super();
		System.out.println("config read...................................................");
	}

//	@Bean
//	public MongoClient factory() throws UnknownHostException {
//		MongoClientURI uri = new MongoClientURI(env.getProperty("spring.data.mongodb.uri"));
//		return new MongoClient(uri);
//	}
//
//	@Bean
//	public MongoDbFactory getMongoDbFactory() throws Exception {
//		return new SimpleMongoDbFactory(factory(), env.getProperty("spring.data.mongodb.database"));
//	}
//
//	@Bean(name = "mongoTemplate")
//	public MongoTemplate getMongoTemplate() throws Exception {
//		MongoTemplate mongoTemplate = new MongoTemplate(getMongoDbFactory());
//		return mongoTemplate;
//	}

}
