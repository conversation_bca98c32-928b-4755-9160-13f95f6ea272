package com.stpl.tech.loggingservice.dao.chaimonk.log;

import java.util.Date;
import java.util.List;

import com.stpl.tech.loggingservice.model.chaimonk.log.UnitMonkTaskSummaryData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-04-2018.
 */
@Repository
public interface UnitMonkTaskSummaryDataDao extends MongoRepository<UnitMonkTaskSummaryData,String> {

    @Query("{'unitId': ?0, 'businessDate': {'$gte' : ?1, '$lte' : ?2}}")
    public List<UnitMonkTaskSummaryData> findByBusinessDate(Integer unitId, Date startOfMonth, Date currentDate);

    public UnitMonkTaskSummaryData findDistinctFirstByBusinessDateAndUnitId(Date businessDate, Integer unitId);
}
