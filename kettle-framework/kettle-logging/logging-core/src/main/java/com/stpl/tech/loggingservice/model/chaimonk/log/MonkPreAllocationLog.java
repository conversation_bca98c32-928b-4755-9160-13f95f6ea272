package com.stpl.tech.loggingservice.model.chaimonk.log;

import java.io.Serializable;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 10-01-2019.
 */
public class MonkPreAllocationLog implements Serializable {

	private String id;
    private int attemptNo;
    private int totalMonks;
    private int activeMonks;
    private int inProcessingMonks;
    private int lessMilkMonks;
    private int lessWaterMonks;
    private int noPanMonks;
    private int notConnectedMonks;
    private int underCleaningMonks;
    private int underResetMonks;
    private int underRemake;
    private int underCleanSensor;
    private boolean allocated;

    private long allocationLogTime;
    private String chaiMonk1Status;
    private String chaiMonk2Status;
    private String chaiMonk3Status;
    private String chaiMonk4Status;
    private String chaiMonk5Status;

    private int wrongPanMonks;
    private int addPattiMonks;
    private int deactivatedMonks;
    private int volumeRemakeMonk;
    private int addPattiRemakeMonks;
    private int powerOutageRemakeMonks;
    private int waterEmptyRemakeMonks;
    private int milkEmptyRemakeMonks;
    private int notConnectedErrorMonks;
    private int noCommunicationErrorMonks;
	private String notAllocatedReason;
    
    
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public int getAttemptNo() {
		return attemptNo;
	}
	public void setAttemptNo(int attemptNo) {
		this.attemptNo = attemptNo;
	}
	public int getTotalMonks() {
		return totalMonks;
	}
	public void setTotalMonks(int totalMonks) {
		this.totalMonks = totalMonks;
	}
	public int getActiveMonks() {
		return activeMonks;
	}
	public void setActiveMonks(int activeMonks) {
		this.activeMonks = activeMonks;
	}
	public int getInProcessingMonks() {
		return inProcessingMonks;
	}
	public void setInProcessingMonks(int inProcessingMonks) {
		this.inProcessingMonks = inProcessingMonks;
	}
	public int getLessMilkMonks() {
		return lessMilkMonks;
	}
	public void setLessMilkMonks(int lessMilkMonks) {
		this.lessMilkMonks = lessMilkMonks;
	}
	public int getLessWaterMonks() {
		return lessWaterMonks;
	}
	public void setLessWaterMonks(int lessWaterMonks) {
		this.lessWaterMonks = lessWaterMonks;
	}
	public int getNoPanMonks() {
		return noPanMonks;
	}
	public void setNoPanMonks(int noPanMonks) {
		this.noPanMonks = noPanMonks;
	}
	public int getNotConnectedMonks() {
		return notConnectedMonks;
	}
	public void setNotConnectedMonks(int notConnectedMonks) {
		this.notConnectedMonks = notConnectedMonks;
	}
	public int getUnderCleaningMonks() {
		return underCleaningMonks;
	}
	public void setUnderCleaningMonks(int underCleaningMonks) {
		this.underCleaningMonks = underCleaningMonks;
	}
	public int getUnderResetMonks() {
		return underResetMonks;
	}
	public void setUnderResetMonks(int underResetMonks) {
		this.underResetMonks = underResetMonks;
	}
	public int getUnderRemake() {
		return underRemake;
	}
	public void setUnderRemake(int underRemake) {
		this.underRemake = underRemake;
	}
	public int getUnderCleanSensor() {
		return underCleanSensor;
	}
	public void setUnderCleanSensor(int underCleanSensor) {
		this.underCleanSensor = underCleanSensor;
	}
	public boolean isAllocated() {
		return allocated;
	}
	public void setAllocated(boolean allocated) {
		this.allocated = allocated;
	}
	public long getAllocationLogTime() {
		return allocationLogTime;
	}
	public void setAllocationLogTime(long allocationLogTime) {
		this.allocationLogTime = allocationLogTime;
	}
	public String getChaiMonk1Status() {
		return chaiMonk1Status;
	}
	public void setChaiMonk1Status(String chaiMonk1Status) {
		this.chaiMonk1Status = chaiMonk1Status;
	}
	public String getChaiMonk2Status() {
		return chaiMonk2Status;
	}
	public void setChaiMonk2Status(String chaiMonk2Status) {
		this.chaiMonk2Status = chaiMonk2Status;
	}
	public String getChaiMonk3Status() {
		return chaiMonk3Status;
	}
	public void setChaiMonk3Status(String chaiMonk3Status) {
		this.chaiMonk3Status = chaiMonk3Status;
	}
	public String getChaiMonk4Status() {
		return chaiMonk4Status;
	}
	public void setChaiMonk4Status(String chaiMonk4Status) {
		this.chaiMonk4Status = chaiMonk4Status;
	}
	public String getChaiMonk5Status() {
		return chaiMonk5Status;
	}
	public void setChaiMonk5Status(String chaiMonk5Status) {
		this.chaiMonk5Status = chaiMonk5Status;
	}
	public int getWrongPanMonks() {
		return wrongPanMonks;
	}
	public void setWrongPanMonks(int wrongPanMonks) {
		this.wrongPanMonks = wrongPanMonks;
	}
	public int getAddPattiMonks() {
		return addPattiMonks;
	}
	public void setAddPattiMonks(int addPattiMonks) {
		this.addPattiMonks = addPattiMonks;
	}
	public int getDeactivatedMonks() {
		return deactivatedMonks;
	}
	public void setDeactivatedMonks(int deactivatedMonks) {
		this.deactivatedMonks = deactivatedMonks;
	}
	public int getVolumeRemakeMonk() {
		return volumeRemakeMonk;
	}
	public void setVolumeRemakeMonk(int volumeRemakeMonk) {
		this.volumeRemakeMonk = volumeRemakeMonk;
	}
	public int getAddPattiRemakeMonks() {
		return addPattiRemakeMonks;
	}
	public void setAddPattiRemakeMonks(int addPattiRemakeMonks) {
		this.addPattiRemakeMonks = addPattiRemakeMonks;
	}
	public int getPowerOutageRemakeMonks() {
		return powerOutageRemakeMonks;
	}
	public void setPowerOutageRemakeMonks(int powerOutageRemakeMonks) {
		this.powerOutageRemakeMonks = powerOutageRemakeMonks;
	}
	public int getWaterEmptyRemakeMonks() {
		return waterEmptyRemakeMonks;
	}
	public void setWaterEmptyRemakeMonks(int waterEmptyRemakeMonks) {
		this.waterEmptyRemakeMonks = waterEmptyRemakeMonks;
	}
	public int getMilkEmptyRemakeMonks() {
		return milkEmptyRemakeMonks;
	}
	public void setMilkEmptyRemakeMonks(int milkEmptyRemakeMonks) {
		this.milkEmptyRemakeMonks = milkEmptyRemakeMonks;
	}
	public int getNotConnectedErrorMonks() {
		return notConnectedErrorMonks;
	}
	public void setNotConnectedErrorMonks(int notConnectedErrorMonks) {
		this.notConnectedErrorMonks = notConnectedErrorMonks;
	}
	public int getNoCommunicationErrorMonks() {
		return noCommunicationErrorMonks;
	}
	public void setNoCommunicationErrorMonks(int noCommunicationErrorMonks) {
		this.noCommunicationErrorMonks = noCommunicationErrorMonks;
	}

	public String getNotAllocatedReason() {
		return notAllocatedReason;
	}

	public void setNotAllocatedReason(String notAllocatedReason) {
		this.notAllocatedReason = notAllocatedReason;
	}
}
