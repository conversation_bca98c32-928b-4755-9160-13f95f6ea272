package com.stpl.tech.loggingservice.reporting.mapper;

import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 18-03-2018.
 */
public class MonkStatusAggregatedView {

    private double prepTime;
    private List<String> monkNames;
    private int boils;
    private Integer fatals;
    private double actualWeight;
    private double targetWeight;
    private double deviation;
    private double targetTime;
    private double addPattiTime;


    public double getPrepTime() {
        return prepTime;
    }

    public void setPrepTime(double prepTime) {
        this.prepTime = prepTime;
    }

    public List<String> getMonkNames() {
        return monkNames;
    }

    public void setMonkNames(List<String> monkNames) {
        this.monkNames = monkNames;
    }

    public int getBoils() {
        return boils;
    }

    public void setBoils(int boils) {
        this.boils = boils;
    }

    public Integer getFatals() {
        return fatals;
    }

    public void setFatals(Integer fatals) {
        this.fatals = fatals;
    }

    public double getActualWeight() {
        return actualWeight;
    }

    public void setActualWeight(double actualWeight) {
        this.actualWeight = actualWeight;
    }

    public double getTargetWeight() {
        return targetWeight;
    }

    public void setTargetWeight(double targetWeight) {
        this.targetWeight = targetWeight;
    }

    public double getDeviation() {
        return deviation;
    }

    public void setDeviation(double deviation) {
        this.deviation = deviation;
    }

    public double getTargetTime() {
        return targetTime;
    }

    public void setTargetTime(double targetTime) {
        this.targetTime = targetTime;
    }

    public void setAddPattiTime(double addPattiTime) {
        this.addPattiTime = addPattiTime;
    }

    public double getAddPattiTime() {
        return addPattiTime;
    }
}
