package com.stpl.tech.loggingservice.model.chaimonk.log;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 02-02-2017.
 */

@Document
public class MonkDiagnosisLogData {


    @Id
    protected String _id;

    @Indexed
    int userId;

    String password;

    @Indexed
    int unitId;

    String className;

    Date createdAt;

    Map<Date, Map<String, List<TroubleShoot>>> monkDiagnosisData;

    Integer monkDiagnosisEventId;

    String unitName;

    String userName;

    String userIdsName;

    String monkName;

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public Map<Date, Map<String, List<TroubleShoot>>> getMonkDiagnosisData() {
        return monkDiagnosisData;
    }

    public void setMonkDiagnosisData(Map<Date, Map<String, List<TroubleShoot>>> monkDiagnosisData) {
        this.monkDiagnosisData = monkDiagnosisData;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Integer getMonkDiagnosisEventId() {
        return monkDiagnosisEventId;
    }

    public void setMonkDiagnosisEventId(Integer monkDiagnosisEventId) {
        this.monkDiagnosisEventId = monkDiagnosisEventId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserIdsName() {
        return userIdsName;
    }

    public void setUserIdsName(String userIdsName) {
        this.userIdsName = userIdsName;
    }

    public String getMonkName() {
        return monkName;
    }

    public void setMonkName(String monkName) {
        this.monkName = monkName;
    }
}
