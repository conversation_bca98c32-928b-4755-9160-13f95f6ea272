package com.stpl.tech.loggingservice.model.chaimonk.log;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Entity;
import java.util.Date;

@Entity
@Document
public class MonkStatusLogPayloadDetails {


    @Id
    protected String _id;

    @Indexed
    int sCode;
    int t;
    Date currDate;
    String payload;
    String type;
    boolean isLastStateCodeLog;

    public int getsCode() {
        return sCode;
    }

    public void setsCode(int sCode) {
        this.sCode = sCode;
    }

    public int getT() {
        return t;
    }

    public void setT(int t) {
        this.t = t;
    }

    public Date getCurrDate() {
        return currDate;
    }

    public void setCurrDate(Date currDate) {
        this.currDate = currDate;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isLastStateCodeLog() {
        return isLastStateCodeLog;
    }

    public void setLastStateCodeLog(boolean lastStateCodeLog) {
        isLastStateCodeLog = lastStateCodeLog;
    }
}
