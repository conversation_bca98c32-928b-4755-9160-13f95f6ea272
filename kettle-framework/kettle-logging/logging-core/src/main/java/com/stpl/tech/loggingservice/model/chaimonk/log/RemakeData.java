package com.stpl.tech.loggingservice.model.chaimonk.log;

import com.stpl.tech.kettle.domain.model.WorkTask;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
public class RemakeData {

    @Id
    private String _id;

    Double statusWhenErrorOccurred;
    Double actualBoilCount;
    Double expectedBoilCount;
    Double pouringTime;
    Double addPattiTime;
    Double brewingTime;
    Double totalTime;
    Double actualWaterPoured;
    Double actualMilkPoured;
    Double actualWeight;
    Double boilPattiFlag;
    Double minimumTargetWeight;
    Double firmwareVersion;
    Double weightCalibrationValue;
    Integer remakeErrorCode;
    String remakeReason;
    WorkTask workTask;
    String receivedString;

    @Override
    public String toString() {
        return "RemakeData{" +
                "statusWhenErrorOccurred=" + statusWhenErrorOccurred +
                ", actualBoilCount=" + actualBoilCount +
                ", expectedBoilCount=" + expectedBoilCount +
                ", pouringTime=" + pouringTime +
                ", addPattiTime=" + addPattiTime +
                ", brewingTime=" + brewingTime +
                ", totalTime=" + totalTime +
                ", actualWaterPoured=" + actualWaterPoured +
                ", actualMilkPoured=" + actualMilkPoured +
                ", actualWeight=" + actualWeight +
                ", boilPattiFlag=" + boilPattiFlag +
                ", minimumTargetWeight=" + minimumTargetWeight +
                ", firmwareVersion=" + firmwareVersion +
                ", weightCalibrationValue=" + weightCalibrationValue +
                ", remakeErrorCode=" + remakeErrorCode +
                ", remakeReason='" + remakeReason + '\'' +
                '}';
    }
}
