package com.stpl.tech.loggingservice.dao.chaimonk.log;

import java.util.List;

import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 22-10-2017.
 */

@Repository
public interface MonkTaskStatusLogDao extends MongoRepository<MonkTaskStatusLog,String>{

    @Query("{'uId': ?0, 'billServerTime' : {'$gte' : ?1, '$lte' : ?2}}")
    public List<MonkTaskStatusLog> findMonkTaskStatusLogs(Integer unitId, long startTime, long endTime);

    @Query(value = "{'orderId': ?0 }",sort = "{'billServerTime': -1}")
    public List<MonkTaskStatusLog> findMonkTaskStatusLogsByOrderId(Integer orderId);
}
