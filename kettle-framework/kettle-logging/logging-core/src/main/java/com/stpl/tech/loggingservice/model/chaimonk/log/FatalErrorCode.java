package com.stpl.tech.loggingservice.model.chaimonk.log;

import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-03-2018.
 */
public enum FatalErrorCode {

    NO_WATER(112),NO_MILK(113),

    NO_POWER(116),PATTI_DELAY(119), NO_ERROR(0), NO_PAN(10);

    private final int errorCode;

    private static final Map<Integer, FatalErrorCode> lookup = new HashMap<>();

    static {
        for (FatalErrorCode code : FatalErrorCode.values()) {
            lookup.put(code.getCode(), code);
        }
    }

    FatalErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public int getCode(){
        return this.errorCode;
    }

    public static FatalErrorCode get(Integer enumValue) {
        return lookup.get(enumValue)!=null ? lookup.get(enumValue) : lookup.get(0);
    }

}
