package com.stpl.tech.loggingservice.reporting.model;

import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.loggingservice.reporting.mapper.ShiftViewForUnit;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import java.util.Date;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 27-03-2018.
 */
public abstract class MonkReport extends ReportData {

    private static final Logger LOG = LoggerFactory.getLogger(MonkReport.class);

    public void writeReport(){
        byte[] bytes = getWorkbookCtx().toNativeBytes();
        String writtenPath = AppUtils.write(bytes, getFilePath(), getName(), getFileName(), LOG);
        if (writtenPath != null) {
            setFilePath(writtenPath);
            setGenerated(true);
        }
    }

    public abstract Unit getUnit();

    public abstract boolean isGenerated();

    public abstract String getFilePath();

    public abstract String getFileName();

    public abstract void renderSummaryView(MonkTaskMtdView mtd, MonkTaskMtdView today);

    public abstract void renderShiftSummaries(List<ShiftViewForUnit> shifts);

    public abstract void renderTaskView();

    public abstract WorkbookContext getWorkbookCtx();

    public abstract void setFilePath(String filePath);

    public abstract void setGenerated(boolean value);

    public abstract Date getStartDate();

    public abstract Date getEndDate();

}
