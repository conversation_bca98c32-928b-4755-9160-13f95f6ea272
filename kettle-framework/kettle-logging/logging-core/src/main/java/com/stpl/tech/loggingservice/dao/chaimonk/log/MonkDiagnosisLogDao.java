package com.stpl.tech.loggingservice.dao.chaimonk.log;

import com.stpl.tech.loggingservice.model.chaimonk.log.MonkDiagnosisLogData;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 22-10-2017.
 */
@Repository
public interface MonkDiagnosisLogDao extends MongoRepository<MonkDiagnosisLogData, String> {

}
