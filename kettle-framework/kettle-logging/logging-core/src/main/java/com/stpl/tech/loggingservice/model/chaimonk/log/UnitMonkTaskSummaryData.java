package com.stpl.tech.loggingservice.model.chaimonk.log;

import java.util.Date;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-04-2018.
 */
@Document
public class UnitMonkTaskSummaryData {

    @Id
    private String id;
    @Indexed
    private Date businessDate;
    @Indexed
    private int unitId;
    private String unitName;
    private List<MonkTaskSummary> taskSummaryList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public List<MonkTaskSummary> getTaskSummaryList() {
        return taskSummaryList;
    }

    public void setTaskSummaryList(List<MonkTaskSummary> taskSummaryList) {
        this.taskSummaryList = taskSummaryList;
    }
}
