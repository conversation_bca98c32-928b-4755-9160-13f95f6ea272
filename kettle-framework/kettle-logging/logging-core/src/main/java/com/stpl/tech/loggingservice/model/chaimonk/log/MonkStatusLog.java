package com.stpl.tech.loggingservice.model.chaimonk.log;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Entity;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 22-10-2017.
 */
@Entity
@Document
public class MonkStatusLog implements BasicLogDetail{

    @Id
    protected String _id;

    @Indexed
    String name;
    @Indexed
    int uid;
    @Indexed
    int taskId;//orderItemId
    @Indexed
    int orderId; //orderIdAtserver
    @Indexed
    int pId;
    @Indexed
    String pName;
    @Indexed
    int quantity;
    @Indexed
    String dimension;

    @Indexed
    boolean isForcefullyRemoved;

    int userid;
    protected Date uploadTime;

    List<MonkStatusLogPayloadDetails> monkMessages;

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getTaskId() {
        return taskId;
    }

    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }

    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public int getpId() {
        return pId;
    }

    public void setpId(int pId) {
        this.pId = pId;
    }

    public String getpName() {
        return pName;
    }

    public void setpName(String pName) {
        this.pName = pName;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public int getUserid() {
        return userid;
    }

    public void setUserid(int userid) {
        this.userid = userid;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public List<MonkStatusLogPayloadDetails> getMonkMessages() {
        return monkMessages;
    }

    public void setMonkMessages(List<MonkStatusLogPayloadDetails> monkMessages) {
        this.monkMessages = monkMessages;
    }

    @Override
    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public boolean isForcefullyRemoved() {
        return isForcefullyRemoved;
    }

    public void setForcefullyRemoved(boolean forcefullyRemoved) {
        isForcefullyRemoved = forcefullyRemoved;
    }
}
