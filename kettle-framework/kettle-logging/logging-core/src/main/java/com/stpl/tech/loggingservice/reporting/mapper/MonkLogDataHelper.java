package com.stpl.tech.loggingservice.reporting.mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.OptionalDouble;
import java.util.stream.Collectors;

import com.google.common.base.Joiner;
import com.stpl.tech.loggingservice.model.chaimonk.log.FatalErrorCode;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLogPayloadDetails;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskSummary;
import com.stpl.tech.loggingservice.model.chaimonk.log.ProcessedMonk;
import com.stpl.tech.loggingservice.reporting.model.MonkShiftSummaryView;
import com.stpl.tech.loggingservice.reporting.model.MonkSummaryView;
import com.stpl.tech.loggingservice.reporting.model.MonkTaskMtdView;
import com.stpl.tech.loggingservice.reporting.model.MonkTaskOpsView;
import com.stpl.tech.loggingservice.reporting.model.MonkTaskView;
import com.stpl.tech.loggingservice.util.KettleLoggingUtil;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 17-03-2018.
 */
public class MonkLogDataHelper {
    private static final Logger LOG = LoggerFactory.getLogger(MonkLogDataHelper.class);


    public static List<MonkTaskSummary> processLogs(List<MonkStatusLog> monkStatusLogs, List<MonkTaskStatusLog> taskStatusLogs, List<MonkTaskSummary> taskSummaryList, Unit unit) {
        Map<Integer, MonkTaskSummary> taskSummaryMap = prepareTaskSummaryMap(taskStatusLogs, unit);
        Map<Integer, List<MonkStatusLog>> statusLogMap = prepareStatusLogMap(monkStatusLogs);
        for (Integer taskId : statusLogMap.keySet()) {
            taskSummaryMap.put(taskId, process(taskSummaryMap.get(taskId), statusLogMap.get(taskId)));
        }
        taskSummaryList.addAll(taskSummaryMap.values());
        return taskSummaryList;
    }

    private static MonkSummaryView getSummary(List<? extends MonkTaskView> taskSummaryList, MonkSummaryView summaryView) {
        int numberOfOrders = (int) taskSummaryList.stream().map(MonkTaskView::getOrderId).distinct().count();
        summaryView.setNumberOfOrders(numberOfOrders);
        summaryView.setDeviationFromIdeal(getAverageDeviation(taskSummaryList));
        summaryView.setAverageTAT(KettleLoggingUtil.toTimeString(getTAT(taskSummaryList)));
        summaryView.setAverageWaitingTime(KettleLoggingUtil.toTimeString(getWaitingTime(taskSummaryList)));
        summaryView.setAverageAddPattiTime(KettleLoggingUtil.toTimeString(getAddPattiTime(taskSummaryList)));
        summaryView.setAveragePickUpTime(KettleLoggingUtil.toTimeString(getPickupTime(taskSummaryList)));
        setFatalsSummary(summaryView,taskSummaryList);
        return summaryView;
    }

    private static void setFatalsSummary(MonkSummaryView summaryView, List<? extends MonkTaskView> taskSummaryList) {
        int waterFatals=0,milkFatals=0,powerFatals=0,pattiFatals=0;
        for(MonkTaskView view : taskSummaryList){
            switch (view.getFatal()) {
                case NO_MILK:
                    milkFatals += 1;
                    break;
                case NO_WATER:
                    waterFatals += 1;
                    break;
                case NO_POWER:
                    powerFatals += 1;
                    break;
                case PATTI_DELAY:
                    pattiFatals += 1;
                    break;
            }
        }

        summaryView.setWaterFatals(waterFatals);
        summaryView.setMilkFatals(milkFatals);
        summaryView.setPowerOutFatals(powerFatals);
        summaryView.setPattiDelayFatals(pattiFatals);
    }

    private static double getAddPattiTime(List<? extends MonkTaskView> taskSummaryList) {
        OptionalDouble avgPatti = taskSummaryList.stream().mapToDouble(MonkTaskView::getAddPattiTime).average();
        return avgPatti.orElse(0d);
    }

    private static double getPickupTime(List<? extends MonkTaskView> taskSummaryList) {
        OptionalDouble avgPickup = taskSummaryList.stream().mapToDouble(MonkTaskView::getPickUpTime).average();
        return avgPickup.orElse(0d);
    }

    private static double getWaitingTime(List<? extends MonkTaskView> taskSummaryList) {
        OptionalDouble avgWait = taskSummaryList.stream().mapToDouble(MonkTaskView::getWaitingTime).average();
        return avgWait.orElse(0d);
    }

    private static double getTAT(List<? extends MonkTaskView> taskSummaryList){
        OptionalDouble avgTAT  = taskSummaryList.stream().mapToDouble(MonkLogDataHelper::getTotalTime).average();
        return avgTAT.orElse(0d);
    }

    private static double getTotalTime(MonkTaskView taskSummary){
        return taskSummary.getWaitingTime() + taskSummary.getPrepTime() + taskSummary.getPickUpTime();
    }

    private static double getAverageDeviation(List<? extends MonkTaskView> taskSummaryList){
        double totalDeviation = 0L;
        int totalDeviations = 1;
        for (MonkTaskView taskSummary : taskSummaryList) {
            totalDeviation += taskSummary.getDeviationFromIdeal();
            if(taskSummary.getDeviationFromIdeal()!=0){
                totalDeviations += 1;
            }
        }
        double averageDeviation = (totalDeviation / totalDeviations);
        return averageDeviation;
    }



    private static Map<Integer, MonkTaskSummary> prepareTaskSummaryMap(List<MonkTaskStatusLog> taskStatusLogs, Unit unit) {
        Map<Integer, MonkTaskSummary> taskSummaryMap = new HashMap<>();
        for (MonkTaskStatusLog taskStatusLog : taskStatusLogs) {
            MonkTaskSummary taskSummary = taskSummaryMap.get(taskStatusLog.getTaskId());
            if (taskSummary == null) {
                taskSummary = initSummaryObject(taskStatusLog, unit);
            }
            taskSummaryMap.put(taskStatusLog.getTaskId(), taskSummary);
        }
        return taskSummaryMap;
    }

    private static Map<Integer, List<MonkStatusLog>> prepareStatusLogMap(List<MonkStatusLog> monkStatusLogs) {
        Map<Integer, List<MonkStatusLog>> statusLogMap = new HashMap<>();
        for (MonkStatusLog statusLog : monkStatusLogs) {
            List<MonkStatusLog> statusLogList = statusLogMap.get(statusLog.getTaskId());
            if (statusLogList == null) {
                statusLogList = new ArrayList<>();
            }
            statusLogList.add(statusLog);
            statusLogMap.put(statusLog.getTaskId(), statusLogList);
        }
        return statusLogMap;
    }

    private static MonkTaskSummary initSummaryObject(MonkTaskStatusLog taskStatusLog, Unit unit) {
        MonkTaskSummary taskSummary = new MonkTaskSummary();
        taskSummary.setTaskId(taskStatusLog.getTaskId());
        taskSummary.setProductName(taskStatusLog.getpName());
        taskSummary.setUnitId(taskStatusLog.getuId());
        taskSummary.setUnitName(unit.getName());
        taskSummary.setBillServerTime(taskStatusLog.getBillServerTime());
        taskSummary.setOrderId(taskStatusLog.getOrderId());
        taskSummary.setDimension(taskStatusLog.getD());
        taskSummary.setQuantity(taskStatusLog.getQ());
        taskSummary.getProcessedMonks().addAll(taskStatusLog.getpMonkList());
        taskSummary.setWaitTime(taskStatusLog.getNtTmeSum());
        return taskSummary;
    }

    private static MonkTaskSummary process(MonkTaskSummary monkTaskSummary, List<MonkStatusLog> monkStatusLogs) {
        if (monkTaskSummary != null && monkStatusLogs != null && monkStatusLogs.size() > 0) {
            MonkStatusAggregatedView aggregatedView = getAggregatedView(monkTaskSummary.getTaskId(), monkStatusLogs, monkTaskSummary );
            if (aggregatedView != null) {
                monkTaskSummary.setMonkName(Joiner.on(",").skipNulls().join(aggregatedView.getMonkNames()));
                monkTaskSummary.setFatal(FatalErrorCode.get(aggregatedView.getFatals()).name());
                monkTaskSummary.setDeviationFromIdeal(aggregatedView.getDeviation());
                monkTaskSummary.setActualWeight(aggregatedView.getActualWeight());
                monkTaskSummary.setTargetWeight(aggregatedView.getTargetWeight());
                monkTaskSummary.setNumberOfBoils(aggregatedView.getBoils());
                monkTaskSummary.setPrepTime(aggregatedView.getPrepTime());
                monkTaskSummary.setTargetTime(aggregatedView.getTargetTime());
                monkTaskSummary.setAddPattiTime(aggregatedView.getAddPattiTime());
                monkTaskSummary.setPickupTime(0);
                monkTaskSummary.setWaitReason(checkWaitReason(monkTaskSummary));
            }
        }
        return monkTaskSummary;
    }

    private static FatalErrorCode checkWaitReason(MonkTaskSummary task) {
        Optional<ProcessedMonk> noPanWait = task.getProcessedMonks().stream()
                .filter(processedMonk -> processedMonk.getNtCodes() != null &&
                        (processedMonk.getNtCodes().contains("10") || processedMonk.getNtCodes().contains("0")))
                .findFirst();
        return noPanWait.isPresent() ? FatalErrorCode.NO_PAN : FatalErrorCode.NO_ERROR;
    }

    private static MonkStatusAggregatedView getAggregatedView(Integer taskId, List<MonkStatusLog> monkStatusLogs,
                                                              MonkTaskSummary task) {
        MonkStatusAggregatedView aggregatedView = new MonkStatusAggregatedView();
        aggregatedView.setMonkNames(monkStatusLogs.stream().map(MonkStatusLog::getName).distinct().collect(Collectors.toList()));
        monkStatusLogs.forEach(log->{
            log.getMonkMessages().sort((o1, o2) -> o2.getsCode() - o1.getsCode());
        });
        setFatalCode(taskId, aggregatedView, task);
        setBoils(taskId, aggregatedView, monkStatusLogs);
        setTimes(aggregatedView, monkStatusLogs);
        return aggregatedView;
    }

    private static void setFatalCode(Integer taskId, MonkStatusAggregatedView aggregatedView, MonkTaskSummary task){
        Optional<ProcessedMonk> fatalCodeMonk = task.getProcessedMonks().stream()
                .filter(processedMonk -> processedMonk.getReCode() != 0)
                .findFirst();

        Integer fatalCode = 0;
        if(fatalCodeMonk.isPresent()){
            ProcessedMonk monk = fatalCodeMonk.get();
            LOG.info("{} {}", taskId, monk.getReCode());
            fatalCode = monk.getReCode();
        }
        aggregatedView.setFatals(fatalCode);

    }

    private static void setTimes(MonkStatusAggregatedView aggregatedView, List<MonkStatusLog> monkStatusLogs) {

        for(MonkStatusLog statusLog : monkStatusLogs){
            List<MonkStatusLogPayloadDetails> filteredLogs = statusLog.getMonkMessages().stream()
                    .filter(monkStatusLog -> monkStatusLog.getPayload()!=null).collect(Collectors.toList());

            List<MonkStatusLogPayloadDetails> pouringLogs = filteredLogs.stream()
                    .filter(log -> Arrays.asList(8, 7).contains(log.getsCode()))
                    .collect(Collectors.toList());

            List<MonkStatusLogPayloadDetails> addPattiLogs = filteredLogs.stream()
                    .filter(log -> Arrays.asList(16, 17).contains(log.getsCode()))
                    .sorted((o1, o2) -> o2.getsCode() - o1.getsCode())
                    .collect(Collectors.toList());

            Optional<MonkStatusLogPayloadDetails> firstBoilLog = filteredLogs.stream()
                    .filter(monkStatusLog -> monkStatusLog.getsCode()==6)
                    .findFirst();

            Optional<MonkStatusLogPayloadDetails> terminalStatus = filteredLogs.stream()
                    .filter(monkStatusLog -> monkStatusLog.getsCode() == 1 || monkStatusLog.getsCode() > 100)
                    .findFirst();

            if (terminalStatus.isPresent()) {
                List<Double> payload = getPayload(terminalStatus.get().getPayload());
                if(!payload.isEmpty()){
                    aggregatedView.setPrepTime(payload.get(3));
                    aggregatedView.setActualWeight(payload.get(5));
                    aggregatedView.setTargetWeight(payload.get(6));
                }
            }

            if(addPattiLogs!=null && addPattiLogs.size()>0 && firstBoilLog.isPresent()){
                MonkStatusLogPayloadDetails firstBoil = firstBoilLog.get();
                List<Double> payload = getPayload(firstBoil.getPayload());
                List<Double> addPattiEndLog = getPayload(addPattiLogs.get(0).getPayload());
                if(!payload.isEmpty() && !addPattiEndLog.isEmpty()){
                    double addPattiTime = payload.get(3) - addPattiEndLog.get(3);
                    aggregatedView.setAddPattiTime(addPattiTime);
                }
            }

            if (pouringLogs != null && pouringLogs.size() > 0) {
                List<Double> payload = getPayload(pouringLogs.get(pouringLogs.size() - 1).getPayload());
                if (!payload.isEmpty()){
                    aggregatedView.setTargetTime(payload.get(4));
                    aggregatedView.setDeviation(aggregatedView.getPrepTime() - aggregatedView.getTargetTime());
                }
            }
        }
    }

    private static List<Double> getPayload(String payload){
        if (payload!=null && !payload.isEmpty()){
            try {
                return (List<Double>) JSONSerializer.toJSON(payload, List.class);
            }catch(Exception e){
                LOG.info(" :::: Error while reading payload using JSON serializer {} Splitting the payload instead ::::",payload);
                String[] payloadArr = payload.split(",");
                List<Double> returnList = new ArrayList<>();
                for(int i=0;i<payloadArr.length;i++){
                    returnList.add(Double.parseDouble(payloadArr[i]));
                }
                return returnList;
            }
        }else{
            return Collections.emptyList();
        }
    }

    private static void setBoils(Integer taskId, MonkStatusAggregatedView aggregatedView, List<MonkStatusLog> monkStatusLogs) {
        for(MonkStatusLog statusLog : monkStatusLogs){
            int boils = 0;
            List<Integer> boilsArr = statusLog.getMonkMessages().stream()
                    .filter(monkStatusLog -> 6 >= monkStatusLog.getsCode() && monkStatusLog.getsCode() > 0)
                    .mapToInt(MonkStatusLogPayloadDetails::getsCode).boxed().collect(Collectors.toList());

            // only in case boilsArr contains at least two elements
            //  number of boils would be 6-(2-1), 6-(3-1), 6-(4-1), 6-(5-1), 6-(6-1)
            if(boilsArr.size() >= 2){
                boilsArr.sort((o1, o2) -> o2 - o1);
                int last = boilsArr.get(boilsArr.size() - 1);
                int secondLast = boilsArr.get(boilsArr.size() - 2);
                boils = 6 - (secondLast - last);
            }
            aggregatedView.setBoils(boils);
        }
    }

    public static void generateShiftSummaryView(List<MonkSummaryView> shiftSummaries,
                                                ShiftViewForUnit shift, List<? extends MonkTaskView> taskSummaryList) {

        List<MonkTaskView> taskInShift = taskSummaryList.stream()
                .filter(task -> task.getBillServerTime() < shift.getEndTime()
                        && task.getBillServerTime() >= shift.getStartTime())
                .collect(Collectors.toList());
        MonkSummaryView shiftSummary = getSummary(taskInShift, new MonkShiftSummaryView(shift));
        shiftSummaries.add(shiftSummary);
    }

    public static MonkTaskMtdView processMTDView(List<MonkTaskOpsView> tasksTillNow) {
        MonkTaskMtdView view = new MonkTaskMtdView();
        int numberOfOrders = (int) tasksTillNow.stream().mapToInt(MonkTaskOpsView::getOrderId).distinct().count();
        view.setNumberOfOrders(numberOfOrders);
        view.setAverageTAT(getTAT(tasksTillNow));
        view.setNoPanOrderDelays(getNoPanDelays(tasksTillNow));
        view.setAverageNoPanDelay(getAverageNoPanDelay(tasksTillNow));
        view.setWaitingTime(getWaitingTime(tasksTillNow));
        view.setPickupTime(getPickupTime(tasksTillNow));
        view.setAddPattiTime(getAddPattiTime(tasksTillNow));
        view.setDeviationFromIdeal(getAverageDeviation(tasksTillNow));
        setFatalsSummary(view,tasksTillNow);
        return view;
    }

    private static double getAverageNoPanDelay(List<MonkTaskOpsView> tasksTillNow) {
        int count = getNoPanDelays(tasksTillNow);
        double waitingTime = tasksTillNow.stream()
                .filter(task -> FatalErrorCode.NO_PAN.equals(task.getWaitReason()))
                .mapToDouble(MonkTaskOpsView::getWaitingTime).sum();
        return count > 0 ? (waitingTime/count) : 0;
    }

    private static int getNoPanDelays(List<MonkTaskOpsView> tasksTillNow) {
        return (int) tasksTillNow.stream()
                .filter(monkTaskOpsView -> FatalErrorCode.NO_PAN.equals(monkTaskOpsView.getWaitReason()))
                .count();
    }
}
