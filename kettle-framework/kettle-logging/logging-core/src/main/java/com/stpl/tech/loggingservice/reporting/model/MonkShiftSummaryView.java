package com.stpl.tech.loggingservice.reporting.model;

import java.util.Date;

import com.stpl.tech.loggingservice.reporting.mapper.ShiftViewForUnit;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 19-03-2018.
 */
@ExcelSheet(value = "Shift Summary View")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class MonkShiftSummaryView implements MonkSummaryView{

    @ExcelField(headerName = "Shift Number")
    private int shiftNumber;
    @ExcelField(headerName = "Shift Start Time")
    private String shiftStartTime;
    @ExcelField(headerName = "Shift End Time")
    private String shiftEndTime;
    @ExcelField
    private int numberOfOrders;
    @ExcelField
    private String averageTAT;
    @ExcelField
    private int waterFatals;
    @ExcelField
    private int milkFatals;
    @ExcelField
    private int powerOutFatals;
    @ExcelField
    private int pattiDelayFatals;
    @ExcelField
    private String averageWaitingTime;
    @ExcelField
    private String averagePickUpTime;
    @ExcelField
    private String averageAddPattiTime;
    @ExcelField
    private double averageDeviationFromIdeal;

    public MonkShiftSummaryView(ShiftViewForUnit shift) {
        this.shiftNumber = shift.getShift();
        this.shiftStartTime = AppUtils.getOnlyTime(new Date(shift.getStartTime()));
        this.shiftEndTime = AppUtils.getOnlyTime(new Date(shift.getEndTime()));
    }

    public MonkShiftSummaryView() {}

    public int getNumberOfOrders() {
        return numberOfOrders;
    }

    public void setNumberOfOrders(int numberOfOrders) {
        this.numberOfOrders = numberOfOrders;
    }

    public String getAverageTAT() {
        return averageTAT;
    }

    public void setAverageTAT(String averageTAT) {
        this.averageTAT = averageTAT;
    }

    @Override
    public void setDeviationFromIdeal(double avgDeviation) {
        this.averageDeviationFromIdeal = avgDeviation;
    }

    public int getWaterFatals() {
        return waterFatals;
    }

    public int getMilkFatals() {
        return milkFatals;
    }

    public int getPowerOutFatals() {
        return powerOutFatals;
    }

    public int getPattiDelayFatals() {
        return pattiDelayFatals;
    }

    public String getAverageWaitingTime() {
        return averageWaitingTime;
    }

    public String getAveragePickUpTime() {
        return averagePickUpTime;
    }

    public String getAverageAddPattiTime() {
        return averageAddPattiTime;
    }

    public double getAverageDeviationFromIdeal() {
        return averageDeviationFromIdeal;
    }

    public void setAverageDeviationFromIdeal(double averageDeviationFromIdeal) {
        this.averageDeviationFromIdeal = averageDeviationFromIdeal;
    }

    @Override
    public void setAverageWaitingTime(String avgWaitingTime) {
        this.averageWaitingTime = avgWaitingTime;
    }

    @Override
    public void setAveragePickUpTime(String averagePickUpTime) {
        this.averagePickUpTime = averagePickUpTime;
    }

    @Override
    public void setAverageAddPattiTime(String averageAddPattiTime) {
        this.averageAddPattiTime = averageAddPattiTime;
    }

    @Override
    public void setMilkFatals(int value) {
        this.milkFatals = value;
    }

    @Override
    public void setWaterFatals(int value) {
        this.waterFatals = value;
    }

    @Override
    public void setPowerOutFatals(int value) {
        this.powerOutFatals = value;
    }

    @Override
    public void setPattiDelayFatals(int value) {
        this.pattiDelayFatals = value;
    }


    public int getShiftNumber() {
        return shiftNumber;
    }

    public void setShiftNumber(int shiftNumber) {
        this.shiftNumber = shiftNumber;
    }

    public String getShiftStartTime() {
        return shiftStartTime;
    }

    public void setShiftStartTime(String shiftStartTime) {
        this.shiftStartTime = shiftStartTime;
    }

    public String getShiftEndTime() {
        return shiftEndTime;
    }

    public void setShiftEndTime(String shiftEndTime) {
        this.shiftEndTime = shiftEndTime;
    }
}
