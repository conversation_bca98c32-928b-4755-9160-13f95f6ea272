package com.stpl.tech.loggingservice.reporting.mapper;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 19-03-2018.
 */
public class ShiftViewForUnit {

    private int shift;
    private long startTime;
    private long endTime;

    public ShiftViewForUnit(int shift, long startTime, long endTime) {
        this.shift = shift;
        this.startTime = startTime;
        this.endTime = endTime;
    }


    public int getShift() {
        return shift;
    }

    public void setShift(int shift) {
        this.shift = shift;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
}
