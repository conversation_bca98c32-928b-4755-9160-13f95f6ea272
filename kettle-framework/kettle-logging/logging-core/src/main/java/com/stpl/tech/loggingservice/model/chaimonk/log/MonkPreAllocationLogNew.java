package com.stpl.tech.loggingservice.model.chaimonk.log;

/**
 * Copyright (C) $today.year, Sunshine Teahouse Private Limited - All Rights
 * Reserved Unauthorized copying of this file, via any medium is strictly
 * prohibited Proprietary and confidential Created by shikhar on 10-01-2019.
 */
public class MonkPreAllocationLogNew {

	private String id;
	private int aNo;
	private int tM;
	private int aM;
	private int iPM;
	private int lMM;
	private int lWM;
	private int nCM;
	private int uCM;
	private int uRM;
	private int uR;
	private int uCS;
	private boolean al;

	private long aLT;
	private String m1;
	private String m2;
	private String m3;
	private String m4;
	private String m5;
	private String m6;

	private int dM;

	private int ntCEM;
	private int nCEM;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public int getaNo() {
		return aNo;
	}

	public void setaNo(int aNo) {
		this.aNo = aNo;
	}

	public int gettM() {
		return tM;
	}

	public void settM(int tM) {
		this.tM = tM;
	}

	public int getaM() {
		return aM;
	}

	public void setaM(int aM) {
		this.aM = aM;
	}

	public int getiPM() {
		return iPM;
	}

	public void setiPM(int iPM) {
		this.iPM = iPM;
	}

	public int getlMM() {
		return lMM;
	}

	public void setlMM(int lMM) {
		this.lMM = lMM;
	}

	public int getlWM() {
		return lWM;
	}

	public void setlWM(int lWM) {
		this.lWM = lWM;
	}

	public int getnCM() {
		return nCM;
	}

	public void setnCM(int nCM) {
		this.nCM = nCM;
	}

	public int getuCM() {
		return uCM;
	}

	public void setuCM(int uCM) {
		this.uCM = uCM;
	}

	public int getuRM() {
		return uRM;
	}

	public void setuRM(int uRM) {
		this.uRM = uRM;
	}

	public int getuR() {
		return uR;
	}

	public void setuR(int uR) {
		this.uR = uR;
	}

	public int getuCS() {
		return uCS;
	}

	public void setuCS(int uCS) {
		this.uCS = uCS;
	}

	public boolean isAl() {
		return al;
	}

	public void setAl(boolean al) {
		this.al = al;
	}

	public long getaLT() {
		return aLT;
	}

	public void setaLT(long aLT) {
		this.aLT = aLT;
	}

	public String getM1() {
		return m1;
	}

	public void setM1(String m1) {
		this.m1 = m1;
	}

	public String getM2() {
		return m2;
	}

	public void setM2(String m2) {
		this.m2 = m2;
	}

	public String getM3() {
		return m3;
	}

	public void setM3(String m3) {
		this.m3 = m3;
	}

	public String getM4() {
		return m4;
	}

	public void setM4(String m4) {
		this.m4 = m4;
	}

	public String getM5() {
		return m5;
	}

	public void setM5(String m5) {
		this.m5 = m5;
	}

	public String getM6() {
		return m6;
	}

	public void setM6(String m6) {
		this.m6 = m6;
	}

	public int getdM() {
		return dM;
	}

	public void setdM(int dM) {
		this.dM = dM;
	}

	public int getNtCEM() {
		return ntCEM;
	}

	public void setNtCEM(int ntCEM) {
		this.ntCEM = ntCEM;
	}

	public int getnCEM() {
		return nCEM;
	}

	public void setnCEM(int nCEM) {
		this.nCEM = nCEM;
	}

}
