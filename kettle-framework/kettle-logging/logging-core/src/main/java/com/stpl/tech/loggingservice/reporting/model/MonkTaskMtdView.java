package com.stpl.tech.loggingservice.reporting.model;

import org.apache.commons.lang.NotImplementedException;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-04-2018.
 */
public class MonkTaskMtdView implements MonkSummaryView {

    private int numberOfOrders;
    private double averageTAT;
    private int waterFatals;
    private int powerFatals;
    private int milkFatals;
    private int pattiFatals;
    private double waitingTime;
    private double pickupTime;
    private double addPattiTime;
    private double deviationFromIdeal;
    private int noPanOrderDelays;
    private double averageNoPanDelay;


    public int getNumberOfOrders() {
        return numberOfOrders;
    }

    public void setNumberOfOrders(int numberOfOrders) {
        this.numberOfOrders = numberOfOrders;
    }

    @Override
    public void setAverageTAT(String averageTAT) {
        throw new NotImplementedException("Not implemented for this class");
    }

    public double getAverageTAT() {
        return averageTAT;
    }

    public void setAverageTAT(double averageTAT) {
        this.averageTAT = averageTAT;
    }

    public int getWaterFatals() {
        return waterFatals;
    }

    public void setWaterFatals(int waterFatals) {
        this.waterFatals = waterFatals;
    }

    @Override
    public void setPowerOutFatals(int value) {
        this.powerFatals = value;
    }

    @Override
    public void setPattiDelayFatals(int value) {
        this.pattiFatals = value;
    }

    public int getPowerFatals() {
        return powerFatals;
    }

    public void setPowerFatals(int powerFatals) {
        this.powerFatals = powerFatals;
    }

    public int getMilkFatals() {
        return milkFatals;
    }

    public void setMilkFatals(int milkFatals) {
        this.milkFatals = milkFatals;
    }

    public int getPattiFatals() {
        return pattiFatals;
    }

    public void setPattiFatals(int pattiFatals) {
        this.pattiFatals = pattiFatals;
    }

    public double getWaitingTime() {
        return waitingTime;
    }

    public void setWaitingTime(double waitingTime) {
        this.waitingTime = waitingTime;
    }

    public double getPickupTime() {
        return pickupTime;
    }

    public void setPickupTime(double pickupTime) {
        this.pickupTime = pickupTime;
    }

    public double getAddPattiTime() {
        return addPattiTime;
    }

    public void setAddPattiTime(double addPattiTime) {
        this.addPattiTime = addPattiTime;
    }

    public double getDeviationFromIdeal() {
        return deviationFromIdeal;
    }

    public void setDeviationFromIdeal(double deviationFromIdeal) {
        this.deviationFromIdeal = deviationFromIdeal;
    }

    @Override
    public void setAverageWaitingTime(String avgWaitingTime) {
        throw new NotImplementedException("Not implemented for this class");
    }

    @Override
    public void setAveragePickUpTime(String averagePickUpTime) {
        throw new NotImplementedException("Not implemented for this class");
    }

    @Override
    public void setAverageAddPattiTime(String averageAddPattiTime) {
        throw new NotImplementedException("Not implemented for this class");
    }

    public int getNoPanOrderDelays() {
        return this.noPanOrderDelays;
    }

    public void setNoPanOrderDelays(int noPanOrderDelays) {
        this.noPanOrderDelays = noPanOrderDelays;
    }

    public void setAverageNoPanDelay(double averageNoPanDelay) {
        this.averageNoPanDelay = averageNoPanDelay;
    }

    public double getAverageNoPanDelay() {
        return averageNoPanDelay;
    }
}
