package com.stpl.tech.loggingservice.dao.chaimonk.log;

import java.util.List;

import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 22-10-2017.
 */
@Repository
public interface MonkStatusLogDao extends MongoRepository<MonkStatusLog, String> {

    @Query("{'sCode': {'$nin': [10,9]}, 'taskId': {'$in': ?0}}")
    public List<MonkStatusLog> getMonkStatusLogforTasks(List<Integer> taskIds);

    @Query(value = "{'taskId': ?0 }",sort = "{'_id': -1}")
    public List<MonkStatusLog> findMonkStatusLogsByItemId(Integer itemId);
}
