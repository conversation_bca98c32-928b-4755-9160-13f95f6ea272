package com.stpl.tech.loggingservice.model.chaimonk.log;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Entity;
import java.util.Date;
import java.util.List;

/**
 * Created by shikhar on 24/6/19.
 */

@Entity
@Document
public class WsCrashLog implements BasicLogDetail{

    @Id
    private String id;
    private Date uploadTime;

    private String unitId;
    private String unitName;
    private String wsType;
    private String buildNumber;
    private String appV;
    private String apiV;
    private String osV;
    private String crashTime;
    private String crashType;
    private String stackTrace;
    private String crashReason;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    @Override
    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }
    
    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getWsType() {
        return wsType;
    }

    public void setWsType(String wsType) {
        this.wsType = wsType;
    }

    public String getBuildNumber() {
        return buildNumber;
    }

    public void setBuildNumber(String buildNumber) {
        this.buildNumber = buildNumber;
    }

    public String getAppV() {
        return appV;
    }

    public void setAppV(String appV) {
        this.appV = appV;
    }

    public String getApiV() {
        return apiV;
    }

    public void setApiV(String apiV) {
        this.apiV = apiV;
    }

    public String getOsV() {
        return osV;
    }

    public void setOsV(String osV) {
        this.osV = osV;
    }

    public String getCrashTime() {
        return crashTime;
    }

    public void setCrashTime(String crashTime) {
        this.crashTime = crashTime;
    }

    public String getCrashType() {
        return crashType;
    }

    public void setCrashType(String crashType) {
        this.crashType = crashType;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }

    public String getCrashReason() {
        return crashReason;
    }

    public void setCrashReason(String crashReason) {
        this.crashReason = crashReason;
    }
}
