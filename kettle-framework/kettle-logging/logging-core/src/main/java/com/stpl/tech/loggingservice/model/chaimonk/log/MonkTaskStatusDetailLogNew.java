package com.stpl.tech.loggingservice.model.chaimonk.log;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Entity;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C) $today.year, Sunshine Teahouse Private Limited - All Rights
 * Reserved Unauthorized copying of this file, via any medium is strictly
 * prohibited Proprietary and confidential Created by shikhar on 10-01-2019.
 */

@Entity
@Document
public class MonkTaskStatusDetailLogNew implements BasicLogDetail {

	@Id
	private String _id;

	private String id;
	private int oId;
	private int oIId;
	private Date oCT;
	private long orderCreationTimeinMillis;
	private int lTId;
	private long oAT; // order acknowleged at assembly
	private long tCT; // task received first ..time at monk
	private int uId;
	private String uN;
	private int pId;
	private String pN;
	private String dim;
	private int qu;
	private boolean iSC;
	private int rCnt;
	private boolean iManOd;
	private boolean iSplOd;
	private boolean iManH;

	private String mN;
	private int fM;
	private int tM;
	private int bM;
	private boolean iBOd;
	private boolean iBMAl;
	private long wT;

	private boolean f;
	private String fR;
	private double mQD;
	private double wQD;
	private long wPT;
	private long mPT;
	private long aPT;
	private long bT;
	private long pUT;
	private long pT;
	private long tT;

	private int nOA;
	private List<MonkPreAllocationLogNew> monkPreAllocationLogs;

	private Date uploadTime;

	public MonkTaskStatusDetailLogNew(int orderId, int orderItemId, int linkedTaskId, Date orderCreationTime,
			long orderCreationTimeinMillis, long orderAcknowledgeTime, long taskCreationTime, boolean isBulkOrder,
			boolean isManualOrder, boolean isSteepedChai, boolean isSplittedOrder) {
		this.oId = orderId;
		this.oIId = orderItemId;
		this.lTId = linkedTaskId;
		this.oCT = orderCreationTime;
		this.orderCreationTimeinMillis = orderCreationTimeinMillis;
		this.oAT = orderAcknowledgeTime;
		this.tCT = taskCreationTime;
		this.uId = uId;
		this.uN = uN;
		this.iBOd = isBulkOrder;
		this.iManOd = isManualOrder;
		this.iSC = isSteepedChai;
		this.iSplOd = isSplittedOrder;
	}

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public int getoId() {
		return oId;
	}

	public void setoId(int oId) {
		this.oId = oId;
	}

	public int getoIId() {
		return oIId;
	}

	public void setoIId(int oIId) {
		this.oIId = oIId;
	}

	public Date getoCT() {
		return oCT;
	}

	public void setoCT(Date oCT) {
		this.oCT = oCT;
	}

	public long getOrderCreationTimeinMillis() {
		return orderCreationTimeinMillis;
	}

	public void setOrderCreationTimeinMillis(long orderCreationTimeinMillis) {
		this.orderCreationTimeinMillis = orderCreationTimeinMillis;
	}

	public int getlTId() {
		return lTId;
	}

	public void setlTId(int lTId) {
		this.lTId = lTId;
	}

	public long getoAT() {
		return oAT;
	}

	public void setoAT(long oAT) {
		this.oAT = oAT;
	}

	public long gettCT() {
		return tCT;
	}

	public void settCT(long tCT) {
		this.tCT = tCT;
	}

	public int getuId() {
		return uId;
	}

	public void setuId(int uId) {
		this.uId = uId;
	}

	public String getuN() {
		return uN;
	}

	public void setuN(String uN) {
		this.uN = uN;
	}

	public int getpId() {
		return pId;
	}

	public void setpId(int pId) {
		this.pId = pId;
	}

	public String getpN() {
		return pN;
	}

	public void setpN(String pN) {
		this.pN = pN;
	}

	public String getDim() {
		return dim;
	}

	public void setDim(String dim) {
		this.dim = dim;
	}

	public int getQu() {
		return qu;
	}

	public void setQu(int qu) {
		this.qu = qu;
	}

	public boolean isiSC() {
		return iSC;
	}

	public void setiSC(boolean iSC) {
		this.iSC = iSC;
	}

	public int getrCnt() {
		return rCnt;
	}

	public void setrCnt(int rCnt) {
		this.rCnt = rCnt;
	}

	public boolean isiManOd() {
		return iManOd;
	}

	public void setiManOd(boolean iManOd) {
		this.iManOd = iManOd;
	}

	public boolean isiSplOd() {
		return iSplOd;
	}

	public void setiSplOd(boolean iSplOd) {
		this.iSplOd = iSplOd;
	}

	public boolean isiManH() {
		return iManH;
	}

	public void setiManH(boolean iManH) {
		this.iManH = iManH;
	}

	public String getmN() {
		return mN;
	}

	public void setmN(String mN) {
		this.mN = mN;
	}

	public int getfM() {
		return fM;
	}

	public void setfM(int fM) {
		this.fM = fM;
	}

	public int gettM() {
		return tM;
	}

	public void settM(int tM) {
		this.tM = tM;
	}

	public int getbM() {
		return bM;
	}

	public void setbM(int bM) {
		this.bM = bM;
	}

	public boolean isiBOd() {
		return iBOd;
	}

	public void setiBOd(boolean iBOd) {
		this.iBOd = iBOd;
	}

	public boolean isiBMAl() {
		return iBMAl;
	}

	public void setiBMAl(boolean iBMAl) {
		this.iBMAl = iBMAl;
	}

	public long getwT() {
		return wT;
	}

	public void setwT(long wT) {
		this.wT = wT;
	}

	public boolean isF() {
		return f;
	}

	public void setF(boolean f) {
		this.f = f;
	}

	public String getfR() {
		return fR;
	}

	public void setfR(String fR) {
		this.fR = fR;
	}

	public double getmQD() {
		return mQD;
	}

	public void setmQD(double mQD) {
		this.mQD = mQD;
	}

	public double getwQD() {
		return wQD;
	}

	public void setwQD(double wQD) {
		this.wQD = wQD;
	}

	public long getwPT() {
		return wPT;
	}

	public void setwPT(long wPT) {
		this.wPT = wPT;
	}

	public long getmPT() {
		return mPT;
	}

	public void setmPT(long mPT) {
		this.mPT = mPT;
	}

	public long getaPT() {
		return aPT;
	}

	public void setaPT(long aPT) {
		this.aPT = aPT;
	}

	public long getbT() {
		return bT;
	}

	public void setbT(long bT) {
		this.bT = bT;
	}

	public long getpUT() {
		return pUT;
	}

	public void setpUT(long pUT) {
		this.pUT = pUT;
	}

	public long getpT() {
		return pT;
	}

	public void setpT(long pT) {
		this.pT = pT;
	}

	public long gettT() {
		return tT;
	}

	public void settT(long tT) {
		this.tT = tT;
	}

	public int getnOA() {
		return nOA;
	}

	public void setnOA(int nOA) {
		this.nOA = nOA;
	}

	public List<MonkPreAllocationLogNew> getMonkPreAllocationLogs() {
		return monkPreAllocationLogs;
	}

	public void setMonkPreAllocationLogs(List<MonkPreAllocationLogNew> monkPreAllocationLogs) {
		this.monkPreAllocationLogs = monkPreAllocationLogs;
	}

	public Date getUploadTime() {
		return uploadTime;
	}

	@Override
	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}
}
