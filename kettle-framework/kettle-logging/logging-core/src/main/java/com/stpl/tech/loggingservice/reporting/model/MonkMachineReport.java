package com.stpl.tech.loggingservice.reporting.model;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskSummary;
import com.stpl.tech.loggingservice.reporting.mapper.MonkLogDataHelper;
import com.stpl.tech.loggingservice.reporting.mapper.ShiftViewForUnit;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.ExcelWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 27-03-2018.
 */
public class MonkMachineReport extends MonkReport {
    private final static Logger LOG = LoggerFactory.getLogger(MonkReport.class);

    private WorkbookContext workbookCtx;
    private Unit unit;
    private String filePath;
    private Date startDate;
    private Date endDate;
    private boolean generated = false;
    private String fileName;
    private ExcelWriter writer;

    private List<MonkTaskView> taskSummaryList;

    public MonkMachineReport(WorkbookContextFactory factory, List<MonkTaskView> taskSummaryList, Date start, Date end, String baseDir, Unit unit) {
        this.workbookCtx = factory.createWorkbook();
        this.taskSummaryList = taskSummaryList;
        this.startDate = start;
        this.endDate = end;
        this.unit = unit;
        this.fileName = "CHAI_MONK_MACHINE_LOGS_" + AppUtils.getCurrentTimeISTStringWithNoColons();
        this.filePath = baseDir + File.separator + "MONK" + File.separator + "MACHINE_LOGS";
        this.writer = new ExcelWriter(getWorkbookCtx().toNativeWorkbook());
    }

    public WorkbookContext getWorkbookCtx() {
        return workbookCtx;
    }

    @Override
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public void setGenerated(boolean value) {
        this.generated = value;
    }

    public Unit getUnit() {
        return unit;
    }

    public String getFilePath() {
        return filePath;
    }

    public Date getStartDate() {
        return startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public boolean isGenerated() {
        return generated;
    }

    public String getFileName() {
        return fileName;
    }

    @Override
    public void renderSummaryView(MonkTaskMtdView mtd, MonkTaskMtdView today) {

    }


    public void renderTaskView() {
        writer.writeSheet(taskSummaryList, MonkTaskSummary.class);
    }

    public void renderSummaryView(MonkTaskMtdView mtd) {
        /*List<MonkSummaryView> summaryViews = MonkLogDataHelper.generateSummaryView(new MonkReportSummaryView(), this.taskSummaryList);
        writer.writeSheet(summaryViews, MonkReportSummaryView.class);*/
    }

    public void renderShiftSummaries(List<ShiftViewForUnit> shifts){
        List<MonkSummaryView> shiftSummaries = new ArrayList<>();
        for(ShiftViewForUnit shift : shifts){
            MonkLogDataHelper.generateShiftSummaryView(shiftSummaries, shift, this.taskSummaryList);
        }
        writer.writeSheet(shiftSummaries,MonkShiftSummaryView.class);
    }



    public void writeReport() {
        byte[] bytes = this.workbookCtx.toNativeBytes();
        String writtenPath = AppUtils.write(bytes, this.filePath, this.unit.getName(), this.fileName, LOG);
        if (writtenPath != null) {
            this.filePath = writtenPath;
            this.generated = true;
        }
    }

}
