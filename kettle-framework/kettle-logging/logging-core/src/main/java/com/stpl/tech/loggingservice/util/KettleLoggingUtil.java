package com.stpl.tech.loggingservice.util;

import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-04-2018.
 */
public class KettleLoggingUtil extends AppUtils{
    public static final int COLUMN_WIDTH = 15;
    public static final int NAME_COLUMN_WIDTH = 35;
    public static final int SECONDS_IN_A_MINUTE = 60;
    public static final int SECONDS_IN_HOUR = 3600;


    public static void sendSlackMessage(EnvType envType, StringBuilder message, SlackNotification notification) {
        message.append("--------------------------------------------------------");
        SlackNotificationService.getInstance().sendNotification(envType, "KETTLE_LOGGING",
                notification, message.toString());
    }

    public static String toTimeString(Double seconds) {
        if (seconds!=null){
            Double minutes = (seconds % SECONDS_IN_HOUR) / SECONDS_IN_A_MINUTE;
            seconds = seconds % SECONDS_IN_A_MINUTE;
            return String.format("%02d:%02d",minutes.intValue(),seconds.intValue());
        }else {
            return "N/A";
        }
    }

    public static void main(String[] args) {
        System.out.println(toTimeString(489d));
    }

}

