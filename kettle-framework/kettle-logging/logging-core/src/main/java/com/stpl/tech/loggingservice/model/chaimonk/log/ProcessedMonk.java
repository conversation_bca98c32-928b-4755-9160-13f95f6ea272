package com.stpl.tech.loggingservice.model.chaimonk.log;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 22-10-2017.
 */
public class ProcessedMonk {
    protected String name;
    protected String ntCodes;
    protected int reCode;
    protected long ntTme;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNtCodes() {
        return ntCodes;
    }

    public void setNtCodes(String ntCodes) {
        this.ntCodes = ntCodes;
    }

    public int getReCode() {
        return reCode;
    }

    public void setReCode(int reCode) {
        this.reCode = reCode;
    }

    public long getNtTme() {
        return ntTme;
    }

    public void setNtTme(long ntTme) {
        this.ntTme = ntTme;
    }
}
