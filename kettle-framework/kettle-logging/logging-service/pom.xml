<?xml version="1.0"?>
<!-- ~ Copyright (C) 2016, Sunshine Teahouse Private Limited - All Rights
	Reserved ~ Unauthorized copying of this file, via any medium is strictly
	prohibited ~ Proprietary and confidential ~ Written by Chaayos Technology
	Team, 2015 -->

<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.stpl.tech.kettle.log</groupId>
        <artifactId>kettle-logging</artifactId>
        <version>6.2.41</version>
		<relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>logging-service</artifactId>
    <name>logging-service</name>
    <packaging>war</packaging>
    <url>http://maven.apache.org</url>
<!--    <build>-->
<!--        <plugins>-->
<!--            &lt;!&ndash; <plugin>-->
<!--                <groupId>org.jvnet.jaxb2.maven2</groupId>-->
<!--                <artifactId>maven-jaxb2-plugin</artifactId>-->
<!--                <version>0.11.0</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>domain-schema</id>-->
<!--                        <phase>generate-sources</phase>-->
<!--                        <goals>-->
<!--                            <goal>generate</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <schemaDirectory>src/main/xsds</schemaDirectory>-->
<!--                            <generatePackage>com.stpl.tech.analytics.model</generatePackage>-->
<!--                            <generateDirectory>${project.build.directory}/generated-sources/xjc-domain</generateDirectory>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin> &ndash;&gt;-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-war-plugin</artifactId>-->
<!--                <version>2.6</version>-->
<!--                <configuration>-->
<!--                    <webXml>WebContent/WEB-INF/web.xml</webXml>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--        <finalName>logging-service</finalName>-->
<!--    </build>-->
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
    		<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>net.bull.javamelody</groupId>
            <artifactId>javamelody-spring-boot-starter</artifactId>
            <version>1.90.0</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.log</groupId>
            <artifactId>logging-core</artifactId>
            <version>6.2.41</version>
        </dependency>
        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
		    <groupId>javax.servlet</groupId>
		    <artifactId>javax.servlet-api</artifactId>
		    <scope>provided</scope>
		</dependency>
		<dependency>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
    </dependencies>
</project>
