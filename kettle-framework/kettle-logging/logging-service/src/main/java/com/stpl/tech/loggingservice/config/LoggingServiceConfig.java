package com.stpl.tech.loggingservice.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;

@SpringBootApplication
public class LoggingServiceConfig extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(LoggingServiceConfig.class);
    }
    
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(LoggingServiceConfig.class);
    }
}
