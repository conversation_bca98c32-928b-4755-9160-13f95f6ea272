/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.loggingservice.controller;

import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyOrderLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkConsumptionLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkFileUploadType;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import com.stpl.tech.loggingservice.service.LoggingService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;

import javax.ws.rs.core.MediaType;
import java.util.Date;
import java.util.List;

import static com.stpl.tech.loggingservice.core.LoggingConstants.API_VERSION;
import static com.stpl.tech.loggingservice.core.LoggingConstants.LOGGING_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.loggingservice.core.LoggingConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + LOGGING_SERVICES_ROOT_CONTEXT) // v1/log
public class LoggingResources {

    private static final Logger LOG = LoggerFactory.getLogger(LoggingResources.class);

    @Autowired
    private LoggingService service;

    @RequestMapping(method = RequestMethod.POST, value = "monk-consumption", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkConsumption(@RequestBody final List<MonkConsumptionLog> consumptionLogList) {
        LOG.info("Adding Monk Consumption Log - {}", JSONSerializer.toJSON(consumptionLogList));
        return service.addMonkConsumptionLogData(consumptionLogList);
    }


    @RequestMapping(method = RequestMethod.POST, value = "monk-status-log", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkStatusLog(@RequestBody final List<MonkStatusLog> statusLogList) {
        LOG.info("Adding Monk Status Log - {}", JSONSerializer.toJSON(statusLogList));
        return service.addMonkStatusLogData(statusLogList);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk-task-log", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkTaskLog(@RequestBody final List<MonkTaskStatusLog> taskLogList) {
        LOG.info("Adding Monk Task Status Log - {}", JSONSerializer.toJSON(taskLogList));
        return service.addMonkTaskStatusLogData(taskLogList);
    }

    @RequestMapping(method = RequestMethod.POST, value = "save-assembly-order-log", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addAssemblyOrderTaskLog(@RequestBody final List<AssemblyOrderLog> taskLogList) {
        LOG.info("Adding Assembly Order Status Log - {}", JSONSerializer.toJSON(taskLogList));
        return service.addAssemblyOrderLog(taskLogList);
    }


    @RequestMapping(method = RequestMethod.POST, value = "save-monk-log-file", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean uploadMonkLogFile(@RequestParam(value = "unitId") final Integer unitId,
                                     @RequestParam(value = "file") final MultipartFile file,
                                     @RequestParam(value = "type") final String fileType)
            throws Exception {
        try{
            LOG.info("Request to upload monk log file {} {} {}", unitId, file.getName(), fileType);
            LOG.info("Enum constant found :::: {}", MonkFileUploadType.valueOf(fileType));
            service.uploadLogFile(file, unitId, MonkFileUploadType.valueOf(fileType));
            return true;
        }catch (Exception e){
            LOG.error("Exception occurred while uploading log file {}", unitId, e);
            throw e;
        }
        //return flag;
    }

    @Scheduled(cron = "0 0 7 * * *", zone = "GMT+05:30")
    public void sendMonkLogs(){
        service.sendMonkLogs(AppUtils.getPreviousDate(), AppUtils.getPreviousDate());
    }

    @RequestMapping(method = RequestMethod.GET, value = "send-monk-logs", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void sendMonkLogs(@RequestParam Integer unitId,@RequestParam String date) {
        Date parsed = AppUtils.parseDate(date);
        service.sendMonkLogs(parsed,unitId);
    }
}
