<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="container" data-ng-init="init()" style="max-width: 500px; margin: auto; margin-top: 50px;">
    <div class="card-panel teal lighten-2 white-text" data-ng-if="showMessage">{{message}}</div>
    <div class="card #f5f5f5 grey lighten-4">
        <div class="card-content">
            <h4 style="margin-top: 0px;">Welcome</h4>
            <div class="row" style="margin-bottom: 0px;">
                <div class="col s12">
                    <div class="form-element">
                        <label for="userId" class="active">User Id</label>
                        <input id="userId" type="number" autocomplete="off" class="validate" data-ng-model="userId">
                    </div>
                    <div class="form-element">
                        <label for="passcode" class="active">Passcode</label>
                        <input id="passcode" type="password" autocomplete="off" class="validate" data-ng-model="passcode">
                    </div>
                    <div class="form-element right-align">
                        <input type="button" value="Login" data-ng-click="login(email,password)" class="btn" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
