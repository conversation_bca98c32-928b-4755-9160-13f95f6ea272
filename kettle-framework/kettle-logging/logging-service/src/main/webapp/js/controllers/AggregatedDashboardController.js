/**
 * http://usejsdoc.org/
 */
analyticapp
		.controller(
				"AggregatedDashboardController",
				function($rootScope, $scope, $http, $location, APIJson,AuthService ) {
					$scope.init = function() {
						$scope.envType = null;
						$scope.pusher = null;
						$scope.channel = null;
						$scope.aggregatedReportData = null;
						subscribeChannel();
						fetchAllSalesData();
					}
					function createBinding(aggregatedData) {
						// var channel = $scope.pusher.subscribe('sales_data');
						$scope.channel
								.bind(
										aggregatedData.id,
										function(data) {
											//console.log(data);
											$scope
													.$apply(function() {
														for ( var i in $scope.aggregatedReportData) {
															if ($scope.aggregatedReportData[i].id == data.aggregateData.id) {
																$scope.aggregatedReportData[i] = data.aggregateData;
																break;
															}
														}
													});
										});

					}

					function fetchAllSalesData() {
						$http({
							method : 'GET',
							url : APIJson.urls.analyticsData.allAggregatedSales
						}).then(function success(response) {
							$scope.aggregatedReportData = response.data;
							$scope.aggregatedReportData.forEach(function(aggregatedData) {
								createBinding(aggregatedData);
							});
						}, function error(response) {
							console.log("error:" + response);
						});
					}

					function subscribeChannel() {
						$scope.envType = "dev";
						if ($location.host().indexOf("orient.chaayos.com") != -1 || $location.host().indexOf("chaudhary.chaayos.com") != -1) {
							$scope.envType = "prod";
						}
						console.log("initializing...");
						Pusher.logToConsole = true;
						$scope.pusher = new Pusher('668c61c6259750d8ab74', {
							cluster : 'eu',
							encrypted : true
						});

						$scope.channel = $scope.pusher.subscribe($scope.envType
								+ '_sales_data');

					}
				});
