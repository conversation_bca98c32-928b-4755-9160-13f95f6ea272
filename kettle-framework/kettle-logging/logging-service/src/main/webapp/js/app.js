/**
 * http://usejsdoc.org/
 */
var analyticapp = angular.module('analyticapp',['ui.router', 'ui.materialize','ngCookies', 'ui.select2']).config(function ($stateProvider, $urlRouterProvider){
	
	  $urlRouterProvider.otherwise("/login");

	    $stateProvider.state('menu.dashboard', {
	        url: "/dashboard",
	        templateUrl: "views/dashboard.html",
	        controller: 'DashboardController'

	    }).state('menu.aggregated', {
	        url: "/aggregated",
	        templateUrl: "views/aggregated.html",
	        controller: 'AggregatedDashboardController'   
	    }).state('menu.product', {
	        url: "/product",
	        templateUrl: "views/product.html",
	        controller: 'ProductDashboardController'   
	    })
	    .state('menu', {
            url: '/menu',
            templateUrl: 'views/menu.html',
            controller: 'MainController'
        }).state('login', {
	        url: "/login",
	        templateUrl: 'views/login.html',
	        params: {accessDenied: false},
	        controller: 'LoginController'
	    })
}).service('AuthService', function () {
    var service = this;
    service.authorization = null;
    service.getAuthorization = getAuthorization;
    service.setAuthorization = function (authorization) {
        service.authorization = authorization;
    };
    function getAuthorization() {
        return service.authorization;
    }

    return service;
}).factory('httpAuthInterceptor', function ($q, $rootScope, AuthService, $location) {
    return {
        request: function (config) {
            $rootScope.showSpinner = true;
            config.headers.auth = AuthService.getAuthorization();
            return config;
        },
        requestError: function (rejection) {
            $rootScope.showSpinner = false;
            return $q.reject(rejection);
        },
        response: function (response) {
            $rootScope.showSpinner = false;
            return response || $q.when(response);
        },

        responseError: function (response) {
            $rootScope.showSpinner = false;
            if (response.status === 401) {
                $location.path('/login');
            }
            return $q.reject(response);
        }
    };
}).config(['$httpProvider', function ($httpProvider) {
    $httpProvider.interceptors.push('httpAuthInterceptor');
}]);