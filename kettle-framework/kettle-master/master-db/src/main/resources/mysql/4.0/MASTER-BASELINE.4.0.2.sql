
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('SHFUD', '7', 'ACTION', 'SHOW', 'Show filtered unit list in manage mappings sub menus', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`PRE_AUTHENTICATED_API` (`API`, `STATUS`) VALUES ('scm-service.stock-management.verify-price-data', 'ACTIVE');

 CREATE TABLE `KETTLE_MASTER_DEV`.`FICO_DETAIL` (
  `FICO_DETAIL_ID` INT NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT NOT NULL,
  `FICO_NAME` VARCHAR(255) NOT NULL,
  `COMPANY_NAME` VARCHAR(200) NOT NULL,
  `REGISTERED_ADDRESS_ID` INT NULL,
  `G<PERSON><PERSON>` VARCHAR(25) NOT NULL,
  `E<PERSON>IL_ID` VARCHAR(45) NOT NULL,
  `CONTACT_1` VARCHAR(12) NOT NULL,
  `CONTACT_2` VARCHAR(12) NULL,
  `REPORTING_EMAIL_ID` VARCHAR(300) NOT NULL,
  `OFFER_CODE` VARCHAR(30) NULL,
  PRIMARY KEY (`FICO_DETAIL_ID`),
  INDEX `FICO_DETAIL_UNIT_ID` USING BTREE (`UNIT_ID` ASC));

  
ALTER TABLE `KETTLE_MASTER_DEV`.`UNIT_DETAIL`  ADD COLUMN `BUSINESS_TYPE` VARCHAR(15) NULL AFTER `TOKEN_LIMIT`, ADD INDEX `BUSINESS_TYPE_UNIT_DETAIL` USING BTREE (`BUSINESS_TYPE` ASC);
