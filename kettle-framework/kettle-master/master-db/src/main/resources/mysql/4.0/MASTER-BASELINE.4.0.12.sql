ALTER TABLE `KETTLE_MASTER_DEV`.`UNIT_DETAIL` 
ADD COLUMN `ELECTRICITY_METER_COUNT` INT(11) NOT NULL,
ADD COLUMN `IS_DG_AVAILABLE` VARCHAR(5) NULL DEFAULT 'N';

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET ELECTRICITY_METER_COUNT = 1;

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET IS_DG_AVAILABLE = 'Y' WHERE UNIT_ID IN (26005,12015,26027,10012,26026); 

ALTER TABLE KETTLE_MASTER_DEV.COMPANY_DETAIL ADD COLUMN SHORT_CODE VARCHAR(10);
ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN COMPANY_ID INTEGER NOT NULL;

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET COMPANY_ID = 1000;

UPDATE `KETTLE_MASTER_DEV`.`UNIT_DETAIL` SET `COMPANY_ID`='1001' WHERE `UNIT_ID`='22001';
UPDATE `KETTLE_MASTER_DEV`.`UNIT_DETAIL` SET `COMPANY_ID`='1001' WHERE `UNIT_ID`='22002';

INSERT INTO `KETTLE_MASTER_DEV`.`ADDRESS_INFO` (`ADDRESS_ID`, `ADDRESS_LINE_1`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `ADDRESS_TYPE`, `LATITUDE`, `LONGITUDE`) 
VALUES (NULL, 'Anandvan Building 6, 31 III Floor, Paschim Vihar', 'New Delhi', 'Delhi', 'India', '110063', '1166512670', 'OFFICIAL', '28.4964413', '77.13938719999');

INSERT INTO `KETTLE_MASTER_DEV`.`COMPANY_DETAIL` (`COMPANY_ID`, `COMPANY_NAME`, `COMPANY_DESCRIPTION`, `REGD_ADDR_ID`, `CIN`, `SERVICE_TAX_NO`, `WEBSITE_ADDR`, `SHORT_CODE`)
VALUES ('1001', 'DKC Teahouse Private Limited', 'DKC Teahouse Private Limited', '15557', 'U74999DL2017PTC325306', 'AARCS3853MSD001', 'www.dkc.com', 'DKC');


--Remove access of block/unblock from Executive

DELETE FROM KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING WHERE ROLE_ID = (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_EXECUTIVE')  
AND ACTION_DETAIL_ID =(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRBLK');

DELETE FROM KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING WHERE ROLE_ID = (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_EXECUTIVE')  
AND ACTION_DETAIL_ID =(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRUBL');

