ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION ADD COLUMN FORMS_SYSTEM_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N';

INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (R<PERSON><PERSON>_NAME,ROL<PERSON>_DESCRIPTION,ROL<PERSON>_STATUS) VALUES ('AUDITOR','AUDITOR','ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.EMPLOYEE_ROLE_MAPPING (EMPLOYEE_ID,ROLE_ID,UPDATED_BY,LAST_UPDATE_TIME,MAPPING_STATUS)
VALUES (120103,(SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AUDITOR'),120103,'2017-07-15 18:45:06','ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`APPLICATION_DATA` (`APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES ('FORMS_SERVICE', 'FORMS_SERVICE', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE,APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_DESCRIPTION,ACTION_STATUS)
VALUES('FSAM', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'MENU', 'VIEW', 'Forms service -> audit menu -> show', 'ACTIVE'),
('FSSTAM', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'SUBMENU', 'VIEW', 'Forms service -> audit menu -> start audit', 'ACTIVE'),
('FSSEAM', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'SUBMENU', 'VIEW', 'Forms service -> audit menu -> search audit', 'ACTIVE'),
('FSSTBD', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'ACTION', 'VIEW', 'Forms service -> audit menu -> start audit -> backdate form', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AUDITOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSAM'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AUDITOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSSTAM'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AUDITOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSSEAM'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSAM'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSSTAM'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSSEAM'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AUDITOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSSTBD'),'ACTIVE',120103,'2017-10-17 17:56:00');


INSERT INTO `KETTLE_MASTER_DEV`.`DESIGNATION` (`DESIGNATION_NAME`, `DESIGNATION_DESC`, `TRANSACTION_SYSTEM_ACCESS`, `SCM_SYSTEM_ACCESS`, `ANALYTICS_SYSTEM_ACCESS`, `ADMIN_SYSTEM_ACCESS`, `CLM_SYSTEM_ACCESS`, `CRM_SYSTEM_ACCESS`, `FORMS_SYSTEM_ACCESS`) VALUES ('DGM', 'Deputy General Manager', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y');


UPDATE KETTLE_MASTER_DEV.EMPLOYEE_DETAIL SET DESIGNATION_ID =
(SELECT DESIGNATION_ID FROM KETTLE_MASTER_DEV.DESIGNATION WHERE DESIGNATION_NAME = 'DGM') WHERE EMP_ID IN (100037, 120032);


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES ('MAINTENANCE_EXECUTIVE', 'MAINTENANCE_EXECUTIVE', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES ('MARKETING_EXECUTIVE', 'MARKETING_EXECUTIVE', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE,APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_DESCRIPTION,ACTION_STATUS)
VALUES('FSME', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'MENU', 'VIEW', 'Forms service -> Maintenance Expense -> show', 'ACTIVE'),
('FSMEAE', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'SUBMENU', 'VIEW', 'Forms service ->  Maintenance Expense -> Add Expense', 'ACTIVE'),
('FSMEVE', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'SUBMENU', 'VIEW', 'Forms service -> Maintenance Expense -> View Expense', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'MAINTENANCE_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSME'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'MAINTENANCE_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSMEAE'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'MAINTENANCE_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSMEVE'),'ACTIVE',120103,'2017-10-17 17:56:00');


INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE,APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_DESCRIPTION,ACTION_STATUS)
VALUES('FSMRE', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'MENU', 'VIEW', 'Forms service -> Marketing Expense -> show', 'ACTIVE'),
('FSMREAE', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'SUBMENU', 'VIEW', 'Forms service ->  Marketing Expense -> Add Expense', 'ACTIVE'),
('FSMREVE', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'SUBMENU', 'VIEW', 'Forms service -> Marketing Expense -> View Expense', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'MARKETING_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSMRE'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'MARKETING_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSMREAE'),'ACTIVE',120103,'2017-10-17 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'MARKETING_EXECUTIVE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSMREVE'),'ACTIVE',120103,'2017-10-17 17:56:00');

INSERT INTO `KETTLE_MASTER_DEV`.`DEPARTMENT_DESIGNATION_MAPPING` (`DEPT_ID`, `DESIGNATION_ID`) VALUES
((SELECT DEPT_ID FROM KETTLE_MASTER_DEV.DEPARTMENT WHERE DEPT_NAME = 'Cafe Operations'),
(SELECT DESIGNATION_ID FROM KETTLE_MASTER_DEV.DESIGNATION WHERE DESIGNATION_NAME = 'DGM'));