DROP TABLE KETTLE_MASTER_DEV.EXPENSE_METADATA;
CREATE TABLE KETTLE_MASTER_DEV.EXPENSE_METADATA(
    EXPENSE_METADATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    CATEGORY_NAME VARCHAR(50) NOT NULL,
    EXPENSE_HEADER VARCHAR(200) NOT NULL,
    ACCOUNTABLE_IN_PNL VARCHAR(1) NOT NULL,
    BUDGET_CATEGORY VARCHAR(200) NULL,
    EXPENSE_STATUS VARCHAR(15) NULL
);

CREATE INDEX EXPENSE_METADATA_CATEGORY_NAME ON KETTLE_MASTER_DEV.EXPENSE_METADATA(CATEGORY_NAME) USING BTREE;
CREATE INDEX EXPENSE_METADATA_ACCOUNTABLE_IN_PNL ON KETTLE_MASTER_DEV.EXPENSE_METADATA(ACCOUNTABLE_IN_PNL) USING BTREE;
CREATE INDEX EXPENSE_METADATA_BUDGET_CATEGORY ON KETTLE_MASTER_DEV.EXPENSE_METADATA(BUDGET_CATEGORY) USING BTREE;
CREATE INDEX EXPENSE_METADATA_EXPENSE_STATUS ON KETTLE_MASTER_DEV.EXPENSE_METADATA(EXPENSE_STATUS) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (1,'PETTY_CASH','Bike Repair Maintance','Y','ACTIVE','MAINTENANCE_VEHICLE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (2,'PETTY_CASH','Change Commission','Y','ACTIVE','COMMISSIONS_CHANGE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (3,'PETTY_CASH','Cleaning Expenses','Y','ACTIVE','CLEANING_EXPENSE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (4,'PETTY_CASH','COGS - Bakery - Bread','N','ACTIVE',NULL);
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (5,'PETTY_CASH','COGS - Others','N','ACTIVE',NULL);
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (6,'PETTY_CASH','Conveyance - Marketing','Y','ACTIVE','LOCAL_CONVEYANCE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (7,'PETTY_CASH','Conveyance - Operations','Y','ACTIVE','LOCAL_CONVEYANCE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (8,'PETTY_CASH','Conveyance - Others','Y','ACTIVE','LOCAL_CONVEYANCE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (9,'PETTY_CASH','Courier','Y','ACTIVE','COURIER_CHARGES');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (10,'PETTY_CASH','Diesel Charges','Y','ACTIVE','ENERGY_DG_RUNNING');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (11,'PETTY_CASH','Excess Bank Deposit','N','ACTIVE',NULL);
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (12,'PETTY_CASH','Marketing - Data Analysis','Y','ACTIVE','ADVERTISEMENT_BTL');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (13,'PETTY_CASH','Marketing-NPI','Y','ACTIVE','ADVERTISEMENT_BTL');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (14,'PETTY_CASH','Newspaper','Y','ACTIVE','NEWSPAPER_CHARGES');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (15,'PETTY_CASH','Parking Charges','Y','ACTIVE','PARKING_CHARGES');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (16,'PETTY_CASH','Photocopy Expenses','Y','ACTIVE','PRINTING_STATIONERY');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (17,'PETTY_CASH','R&D-Engineering Expenses','Y','ACTIVE','MAINTENANCE_EQUIPMENT');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (18,'PETTY_CASH','Repair & Maintenance - Building','Y','ACTIVE','MAINTENANCE_BUILDING');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (19,'PETTY_CASH','Repair & Maintenance - Computer','Y','ACTIVE','MAINTENANCE_COMPUTER');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (20,'PETTY_CASH','Repair & Maintenance - Electricity','Y','ACTIVE','MAINTENANCE_EQUIPMENT');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (21,'PETTY_CASH','Riders Bike Petrol','Y','ACTIVE','FUEL_CHARGES');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (22,'PETTY_CASH','Staff Welfare','Y','ACTIVE','STAFF_EXPENSES');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (23,'PETTY_CASH','Stationery','Y','ACTIVE','PRINTING_STATIONERY');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (24,'PETTY_CASH','Travelling Expense-ODC','Y','ACTIVE','LOCAL_CONVEYANCE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (25,'PETTY_CASH','Travelling Expenses','Y','ACTIVE','LOCAL_CONVEYANCE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (26,'PETTY_CASH','Water Charges','Y','ACTIVE','WATER_CHARGES');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (27,'PETTY_CASH','COGS - Dairy','N','ACTIVE',NULL);
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (28,'MAINTENANCE','Electrical','Y','ACTIVE','MAINTENANCE_EQUIPMENT');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (29,'MAINTENANCE','Equipments','Y','ACTIVE','MAINTENANCE_EQUIPMENT');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (30,'MAINTENANCE','AC','Y','ACTIVE','MAINTENANCE_EQUIPMENT');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (31,'MAINTENANCE','Plumbing','Y','ACTIVE','MAINTENANCE_BUILDING');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (32,'MAINTENANCE','Civil','Y','ACTIVE','MAINTENANCE_BUILDING');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (33,'MAINTENANCE','Seating','Y','ACTIVE','MAINTENANCE_BUILDING');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (34,'MAINTENANCE','Branding','Y','ACTIVE','MAINTENANCE_BUILDING');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (35,'MARKETING_CORPORATE','Digital Marketing Exp','Y','ACTIVE','DIGITAL_MARKETING_EXPENSE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (36,'MARKETING_CORPORATE','Advertisement - Online','Y','ACTIVE','ADVERTISEMENT_ONLINE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (37,'MARKETING_CORPORATE','Advertisement-Offline','Y','ACTIVE','ADVERTISEMENT_OFFLINE');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (38,'MARKETING_CORPORATE','Marketing-Outdoor','Y','ACTIVE','MARKETING_OUTDOOR');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (39,'MARKETING_CORPORATE','Advertisement-P&S','Y','ACTIVE','ADVERTISEMENT_PNS');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (40,'MARKETING_CORPORATE','Advertisement-Agency Fees','Y','ACTIVE','ADVERTISEMENT_AGENCY_FEES');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (41,'MAINTENANCE','Pest Control','Y','ACTIVE','MAINTENANCE_PEST_CONTROL');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,EXPENSE_STATUS,BUDGET_CATEGORY) VALUES (42,'PETTY_CASH','Pest Control Cafe','Y','ACTIVE','MAINTENANCE_PEST_CONTROL');
INSERT INTO KETTLE_MASTER_DEV.EXPENSE_METADATA (EXPENSE_METADATA_ID,CATEGORY_NAME,EXPENSE_HEADER,ACCOUNTABLE_IN_PNL,BUDGET_CATEGORY,EXPENSE_STATUS) VALUES (43,'PETTY_CASH', 'Business Promotion Cafe', 'Y', 'BUSINESS_PROMOTION', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`EXPENSE_METADATA` (`CATEGORY_NAME`, `EXPENSE_HEADER`, `ACCOUNTABLE_IN_PNL`, `BUDGET_CATEGORY`, `EXPENSE_STATUS`) VALUES ('PETTY_CASH', 'Legal Charges Cafe', 'Y', 'LEGAL_CHARGES_CAFE', 'ACTIVE');

