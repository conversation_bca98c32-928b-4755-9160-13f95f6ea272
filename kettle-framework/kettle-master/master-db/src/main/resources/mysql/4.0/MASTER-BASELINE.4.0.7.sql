ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
ADD COLUMN FREE_ITEM_PRODUCT_ID INTEGER NULL,
ADD COLUMN FREE_ITEM_QUANTITY INTEGER NULL,
ADD COLUMN FREE_ITEM_OFFER_TYPE VARCHAR(10) NULL,
ADD COLUMN FREE_ITEM_OFFER_VALUE DECIMAL(10,2) NULL
;
ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
ADD COLUMN FREE_ITEM_DIMENSION VARCHAR(20) NULL;

INSERT INTO `KETTLE_MASTER_DEV`.`OFFER_DETAIL_DATA` (`OFFER_CATEGORY`, `OFFER_TYPE`, `OFFER_TEXT`, `OFFER_DESCRIPTION`, `START_DATE`, `END_DATE`, `OFFER_STATUS`, `MIN_VALUE`, `VALIDATE_CUSTOMER`, `INCLUDE_TAXES`, `PRIORITY`, `OFFER_SCOPE`, `MIN_ITEM_COUNT`, `QUANTITY_LIMIT`, `LOYALTY_LIMIT`, `OFFER_VALUE`, `FREE_ITEM_PRODUCT_ID`, `FREE_ITEM_QUANTITY`, `FREE_ITEM_OFFER_TYPE`, `FREE_ITEM_OFFER_VALUE`, FREE_ITEM_DIMENSION) VALUES 
('BILL', 'OFFER_WITH_FREE_ITEM_STRATEGY', 'Chaayos Birthday : 10% Off For Red FM customer and a free Chaayos Anniversary Gift Pack', 'Chaayos Birthday : 10% Off For Red FM customer and a free Chaayos Anniversary Gift Pack', '2017-11-01', '2017-11-05', 'ACTIVE', '199', 'Y', 'Y', '1', 'MASS', '2', '0', '0', '0', '700', '1', 'PERCENTAGE', '10', 'None');

INSERT INTO `KETTLE_MASTER_DEV`.`OFFER_DETAIL_DATA` (`OFFER_CATEGORY`, `OFFER_TYPE`, `OFFER_TEXT`, `OFFER_DESCRIPTION`, `START_DATE`, `END_DATE`, `OFFER_STATUS`, `MIN_VALUE`, `VALIDATE_CUSTOMER`, `INCLUDE_TAXES`, `PRIORITY`, `OFFER_SCOPE`, `MIN_ITEM_COUNT`, `QUANTITY_LIMIT`, `LOYALTY_LIMIT`, `OFFER_VALUE`, `FREE_ITEM_PRODUCT_ID`, `FREE_ITEM_QUANTITY`, `FREE_ITEM_OFFER_TYPE`, `FREE_ITEM_OFFER_VALUE`, FREE_ITEM_DIMENSION) 
VALUES ('BILL', 'OFFER_WITH_FREE_ITEM_STRATEGY', 'Chaayos Birthday : Free Chaayos Anniversary Gift Pack', 'Chaayos Birthday : Free Chaayos Anniversary Gift Pack', '2017-11-01', '2017-11-05', 'ACTIVE', '199', 'N', 'Y', '1', 'MASS', '2', '0', '0', '0', '700', '1', 'AMOUNT', '0', 'None');

INSERT INTO `KETTLE_MASTER_DEV`.`COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`, `MANUAL_OVERRIDE`) 
VALUES ((select OFFER_DETAIL_ID from KETTLE_MASTER_DEV.OFFER_DETAIL_DATA where OFFER_TYPE = 'OFFER_WITH_FREE_ITEM_STRATEGY' and OFFER_TEXT = 'Chaayos Birthday : 10% Off For Red FM customer and a free Chaayos Anniversary Gift Pack'), 'REDFM', '2017-11-01', '2017-11-05', 'Y', 'Y', '25000', 'ACTIVE', '0', 'N');

INSERT INTO `KETTLE_MASTER_DEV`.`COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`, `MANUAL_OVERRIDE`) 
VALUES ((select OFFER_DETAIL_ID from KETTLE_MASTER_DEV.OFFER_DETAIL_DATA where OFFER_TYPE = 'OFFER_WITH_FREE_ITEM_STRATEGY' and OFFER_TEXT = 'Chaayos Birthday : Free Chaayos Anniversary Gift Pack'), '5YOC', '2017-11-01', '2017-11-05', 'Y', 'Y', '25000', 'ACTIVE', '0', 'N');

ALTER TABLE KETTLE_MASTER_DEV.EMPLOYEE_SESSION_DETAILS MODIFY COLUMN SESSION_ID VARCHAR(150);