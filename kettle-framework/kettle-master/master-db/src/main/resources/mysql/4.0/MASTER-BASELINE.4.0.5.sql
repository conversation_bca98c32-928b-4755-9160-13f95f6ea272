DROP TABLE IF EXISTS  KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY;
CREATE TABLE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY(
CATEGORY_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
CATEGORY_NAME VARCHAR(50) NOT NULL,
CATEGORY_DESCRIPTION VARCHAR(100) NOT NULL,
CATEGORY_STATUS VARCHAR(10) NOT NULL
);


INSERT INTO  KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY(CATEGORY_NAME,CATEGORY_DESCRIPTION,CATEGORY_STATUS)
VALUES('Fixed Value - Marketing', 'Marketing barter activity coupons','ACTIVE'),
('Fixed Value - BD', 'BD Coupons', 'ACTIVE'),
('Marketing Offers', 'CLM Coupons', 'ACTIVE'),
('Corporate Coupons', 'Corporate Coupons Like <PERSON>ra, Amex', 'ACTIVE'),
('ODC Discounts', 'Coupons for ODC', 'ACTIVE'),
('Loyaltea', 'Loyaltea Program', 'ACTIVE'),
('Employee Discount', 'EMP35', 'ACTIVE'),
('Marketing Alliance', 'Marks & Spencer', 'ACTIVE'),
('FICO Discounts', 'Business Partners', 'ACTIVE'),
('PR Coupons', 'People Relationships', 'ACTIVE'),
('Partners Discount', 'Delivery Partners', 'ACTIVE'),
('Bulk Order Discount', 'Discount on Bulk Orders', 'ACTIVE');


ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN ACCOUNTS_CATEGORY INTEGER;

/*
INSERT INTO KETTLE_MASTER.PAYMENT_MODE (PAYMENT_MODE_ID, MODE_NAME, MODE_TYPE, MODE_DESCRIPTION, SETTLEMENT_TYPE, GENERATE_PULL, COMMISSION_RATE, MODE_STATUS, MODE_CATEGORY, AUTOMATIC_PULL_VALIDATE, AUTOMATIC_TRANSFER, AUTOMATIC_CLOSE_TRANSFER, IS_EDITABLE, NEEDS_SETTLEMENT_SLIP_NUMBER, VALIDATION_SOURCE) VALUES (NULL, 'Dine Out', 'CARD', 'Dine Out', 'CREDIT', '1', '3.25', 'ACTIVE', 'OFFLINE', 'N', 'Y', 'N', 'Y', 'N', 'Dashboard');

INSERT INTO KETTLE_MASTER.UNIT_PAYMENT_MODE_MAPPING (UNIT_ID, PAYMENT_MODE_ID, MAPPING_STATUS) VALUES ('12030', '17', 'ACTIVE');
*/

INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (API_ID, API, STATUS) 
VALUES ('25', 'kettle-service.order-management.missed-call-response', 'ACTIVE');


ALTER TABLE KETTLE_MASTER_DEV.ADDRESS_INFO
MODIFY COLUMN CONTACT_NUM_1	varchar(32);

UPDATE KETTLE_MASTER_DEV.ADDRESS_INFO SET CONTACT_NUM_1 = null, CONTACT_NUM_2 = null;

UPDATE KETTLE_MASTER_DEV.ADDRESS_INFO ai LEFT JOIN EMPLOYEE_DETAIL ed ON  ed.EMP_CURRENT_ADDR = ai.ADDRESS_ID
SET ai.CONTACT_NUM_1 = ed.EMP_CONTACT_NUM_1 , ai.CONTACT_NUM_2 = ed.EMP_CONTACT_NUM_2 WHERE ed.EMP_CONTACT_NUM_1;

