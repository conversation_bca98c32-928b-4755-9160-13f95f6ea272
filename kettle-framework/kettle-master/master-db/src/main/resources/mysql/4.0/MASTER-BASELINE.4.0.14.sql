#TO BE DELETED
CREATE TABLE KETTLE_MASTER_DEV.UNIT_BUDGET_DETAIL (
BUDGET_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
ENTRY_MONTH INTEGER NOT NULL,
ENTRY_YEAR INTEGER NOT NULL,
<PERSON><PERSON><PERSON><PERSON><PERSON> DECIMAL(20,2),
TRANSACTIONS DECIMAL(20,2),
COGS DECIMAL(20,2),
PAPER_AND_CONDIMENT DECIMAL(20,2),
WASTAGE DECIMAL(20,2),
VARIANCE DECIMAL(20,2),
CONSUMABLES DECIMAL(20,2),
LOGISTICS DECIMAL(20,2),
<PERSON>MP_MEAL DECIMAL(20,2),
ELECTRICITY DECIMAL(20,2),
HAPPAY DECIMAL(20,2),
CHANNEL_PARTNER DECIMAL(20,2),
MAINTENANCE DECIMAL(20,2),
DELIVERY_CHARGES DECIMAL(20,2),
<PERSON>NPOWER DECIMAL(20,2),
RENT_OR_REVENUE DECIMAL(20,2),
<PERSON><PERSON><PERSON>EN_AND_WAREHOUSE DECIMAL(20,2),
TURNOVER DECIMAL (20,2),
HQ_EXPENSE DECIMAL(20,2),
TECH_EXPENSE DECIMAL(20,2),
LSM_FLIERS DECIMAL(20,2),
LSM_NPI DECIMAL(20,2),
MARKETING_CORPORATE DECIMAL(20,2),
FIXED_ASSETS DECIMAL(20,2)
);


#TO BE DELETED
CREATE TABLE KETTLE_MASTER_DEV.UNIT_EXPENSE_METADATA_DETAIL (
METADATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
UNIT_NAME VARCHAR(100),
ENTRY_MONTH  INTEGER NOT NULL,
ENTRY_YEAR INTEGER NOT NULL,
FIXED_RENT DECIMAL(20,2),
IS_REVENUE_SHARED VARCHAR(1),
REVENUE_SHARE_PERCENTAGE DECIMAL(20,2),
INTERNET_CHARGES DECIMAL(20,2),
PHONE_CHARGES DECIMAL(20,2),
MANPOWER_TURNOVER DECIMAL(20,2),
IDEAL_MANPOWER_COST DECIMAL(20,2),
ACTUAL_MANPOWER_COST DECIMAL(20,2),
IS_ACTUAL_MANPOWER_AVAILABLE VARCHAR(1)
);

# to be deleted
CREATE TABLE KETTLE_MASTER_DEV.UNIT_METADATA_EVENT (
EVENT_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
ENTRY_MONTH INTEGER NOT NULL,
ENTRY_YEAR INTEGER NOT NULL,
CREATED_AT TIMESTAMP NULL,
CREATED_BY INTEGER NOT NULL,
EVENT_TYPE VARCHAR(100),
FILE_PATH VARCHAR(200)
);












