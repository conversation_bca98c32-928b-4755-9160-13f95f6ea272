INSERT INTO `KETTLE_MASTER_DEV`.`OFFER_DETAIL_DATA` (`OFFER_CATEGORY`, `OFFER_TYPE`, `OFFER_TEXT`, `OFFER_DESCRIPTION`, `START_DATE`, `END_DATE`, `OFFER_STATUS`, `MIN_VALUE`, `VALIDATE_CUSTOMER`, `INCLUDE_TAXES`, `PRIORITY`, `OFFER_SCOPE`, `MIN_ITEM_COUNT`, `QUANTITY_LIMIT`, `LOYALTY_LIMIT`, `OFFER_VALUE`, `FREE_ITEM_PRODUCT_ID`, `FREE_ITEM_QUANTITY`, `FREE_ITEM_OFFER_TYPE`, `FREE_ITEM_OFFER_VALUE`, FREE_ITEM_DIMENSION) 
VALUES ('BILL', 'OFFER_WITH_FREE_ITEM_STRATEGY', 'Republic Day : Free <PERSON><PERSON><PERSON>', 'Republic Day : Free Chaayos Gur <PERSON>', '2018-01-26', '2018-01-28', 'ACTIVE', '250', 'N', 'Y', '1', 'MA<PERSON>', '2', '0', '0', '0', '1169', '1', 'AMOUNT', '0', 'None');

INSERT INTO `KETTLE_MASTER_DEV`.`COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`, `MANUAL_OVERRIDE`) 
VALUES ((select OFFER_DETAIL_ID from KETTLE_MASTER_DEV.OFFER_DETAIL_DATA where OFFER_TYPE = 'OFFER_WITH_FREE_ITEM_STRATEGY' and OFFER_TEXT = 'Republic Day : Free Chaayos Gur Para'), '26GURPARA', '2018-01-26', '2018-01-28', 'Y', 'Y', '25000', 'ACTIVE', '0', 'N');


INSERT INTO `KETTLE_MASTER_DEV`.`OFFER_DETAIL_DATA` (`OFFER_CATEGORY`, `OFFER_TYPE`, `OFFER_TEXT`, `OFFER_DESCRIPTION`, `START_DATE`, `END_DATE`, `OFFER_STATUS`, `MIN_VALUE`, `VALIDATE_CUSTOMER`, `INCLUDE_TAXES`, `PRIORITY`, `OFFER_SCOPE`, `MIN_ITEM_COUNT`, `QUANTITY_LIMIT`, `LOYALTY_LIMIT`, `OFFER_VALUE`, `FREE_ITEM_PRODUCT_ID`, `FREE_ITEM_QUANTITY`, `FREE_ITEM_OFFER_TYPE`, `FREE_ITEM_OFFER_VALUE`, FREE_ITEM_DIMENSION) 
VALUES ('BILL', 'OFFER_WITH_FREE_ITEM_STRATEGY', 'Republic Day : Free Chaayos Methi Matthi', 'Republic Day : Free Chaayos Methi Matthi', '2018-01-26', '2018-01-28', 'ACTIVE', '250', 'N', 'Y', '1', 'MASS', '2', '0', '0', '0', '1170', '1', 'AMOUNT', '0', 'None');

INSERT INTO `KETTLE_MASTER_DEV`.`COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`, `MANUAL_OVERRIDE`) 
VALUES ((select OFFER_DETAIL_ID from KETTLE_MASTER_DEV.OFFER_DETAIL_DATA where OFFER_TYPE = 'OFFER_WITH_FREE_ITEM_STRATEGY' and OFFER_TEXT = 'Republic Day : Free Chaayos Methi Matthi'), '26MATTHI', '2018-01-26', '2018-01-28', 'Y', 'Y', '25000', 'ACTIVE', '0', 'N');

INSERT INTO `KETTLE_MASTER_DEV`.`OFFER_DETAIL_DATA` (`OFFER_CATEGORY`, `OFFER_TYPE`, `OFFER_TEXT`, `OFFER_DESCRIPTION`, `START_DATE`, `END_DATE`, `OFFER_STATUS`, `MIN_VALUE`, `VALIDATE_CUSTOMER`, `INCLUDE_TAXES`, `PRIORITY`, `OFFER_SCOPE`, `MIN_ITEM_COUNT`, `QUANTITY_LIMIT`, `LOYALTY_LIMIT`, `OFFER_VALUE`, `FREE_ITEM_PRODUCT_ID`, `FREE_ITEM_QUANTITY`, `FREE_ITEM_OFFER_TYPE`, `FREE_ITEM_OFFER_VALUE`, FREE_ITEM_DIMENSION) 
VALUES ('BILL', 'OFFER_WITH_FREE_ITEM_STRATEGY', 'Republic Day : Free Chaayos Chivda Mix', 'Republic Day : Free Chaayos Chivda Mix', '2018-01-26', '2018-01-28', 'ACTIVE', '250', 'N', 'Y', '1', 'MASS', '2', '0', '0', '0', '1171', '1', 'AMOUNT', '0', 'None');

INSERT INTO `KETTLE_MASTER_DEV`.`COUPON_DETAIL_DATA` (`OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`, `MANUAL_OVERRIDE`) 
VALUES ((select OFFER_DETAIL_ID from KETTLE_MASTER_DEV.OFFER_DETAIL_DATA where OFFER_TYPE = 'OFFER_WITH_FREE_ITEM_STRATEGY' and OFFER_TEXT = 'Republic Day : Free Chaayos Chivda Mix'), '26CHIVDA', '2018-01-26', '2018-01-28', 'Y', 'Y', '25000', 'ACTIVE', '0', 'N');
