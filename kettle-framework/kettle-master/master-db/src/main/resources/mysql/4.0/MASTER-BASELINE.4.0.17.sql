DROP TABLE KETTLE_MASTER_DEV.MON<PERSON>_CONFIGURATION;
CREATE TABLE KETTLE_MASTER_DEV.MONK_CONFIGURATION(
CONF_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
<PERSON>IT_ID INTEGER NOT NULL,
SCOPE VARCHAR(25) NOT NULL,
CONF_KEY VARCHAR(75) NOT NULL,
CONF_VALUE VARCHAR(75) NOT NULL,
LAST_UPDATED_AT TIMESTAMP NOT NULL,
LAST_UPDATED_BY INTEGER NOT NULL,
CONF_STATUS VARCHAR(45) NOT NULL
);

DROP TABLE KETTLE_MASTER_DEV.MONK_CONFIGURATION_ATTR;
CREATE TABLE KETTLE_MASTER_DEV.MONK_CONFIGURATION_ATTR(
ATTR_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
SCOPE VARCHAR(75) NOT NULL,
ATTR VARCHAR(75) NOT NULL,
ATTR_LABEL VARCHAR(75) NOT NULL,
ATTR_TYPE VARCHAR(75) NOT NULL,
ATTR_STATUS VARCHAR(45) NOT NULL
);

INSERT INTO KETTLE_MASTER_DEV.MONK_CONFIGURATION_ATTR(ATTR,SCOPE,ATTR_LABEL,ATTR_TYPE,ATTR_STATUS)
VALUES('MONK_ENABLED','UNIT','Monk Enabled','boolean','ACTIVE'),
('HOT_COLD_MERGED','UNIT','Hot and Cold Merged','boolean','ACTIVE'),
('VERSION','UNIT','Version','number','ACTIVE'),
('PUBLIC_IP','UNIT','Public IP','text','IN_ACTIVE'),
('NUMBER_OF_MONKS','UNIT','Number of Monks','text','ACTIVE'),
('MAC_ADDRESS','MONK','Mac Address','number','ACTIVE'),
('WEIGHT_COEFFICIENT','MONK','Weight Coefficient','text','ACTIVE'),
('WATER_DENSITY','MONK','Water Density','text','IN_ACTIVE'),
('MILK_DENSITY','MONK','Milk Density','text','IN_ACTIVE'),
('SMALL_PAN_WEIGHT','MONK','Small pan weight','text','IN_ACTIVE'),
('BIG_PAN_WEIGHT','MONK','Big pan weight','text','IN_ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`PRE_AUTHENTICATED_API` (`API_ID`, `API`, `STATUS`) 
VALUES (NULL, 'scm-service.stock-management.calculate-pnl', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`PRE_AUTHENTICATED_API` (`API_ID`, `API`, `STATUS`) 
VALUES (NULL, 'kettle-service.budget-metadata.*', 'ACTIVE');
