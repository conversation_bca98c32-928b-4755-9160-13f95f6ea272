db.recipes.update({},{$set : {"profile":"P0"}},false,true) // add default profile to every recipes

use kettle_master

db.createCollection("recipeProfile");

db.recipeProfile.insertMany([{name: "P0", code : "P0",type : "NON_SCM", status : "ACTIVE"}, {name: "P1", code : "P1",type : "NON_SCM", status : "ACTIVE"},
{name: "P2", code : "P2",type : "NON_SCM", status : "ACTIVE"}, {name: "P3", code : "P3",type : "NON_SCM", status : "ACTIVE"}])