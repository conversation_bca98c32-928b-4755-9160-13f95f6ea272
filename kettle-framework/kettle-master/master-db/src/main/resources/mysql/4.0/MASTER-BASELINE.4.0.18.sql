INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('PRFCP', '7', 'ACTION', 'UPDATE', 'SUMO -> <PERSON><PERSON>GE PAYMENTS -> PROCESS PAYMENT REQUEST -> FORCE CLOSE PAYMENT', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_HEAD'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRFCP'), 'ACTIVE', '100000', '2017-07-01 05:30:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_HEAD'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRFCP'), 'ACTIVE', '100000', '2017-07-01 05:30:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_MANAGER'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'PRFCP'), 'ACTIVE', '100000', '2017-07-01 05:30:00');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('VGRDP', '7', 'ACTION', 'UPDATE', "SUMO -> View venddor GR -> Don\'t pay", 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_HEAD'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VGRDP'), 'ACTIVE', '100000', '2017-07-01 05:30:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_PLANNER'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VGRDP'), 'ACTIVE', '100000', '2017-07-01 05:30:00');

INSERT INTO `KETTLE_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('kettle-service.budget-metadata.*', 'Budget Metadata', 'ACTIVE', 'KETTLE_SERVICE');

INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES
 ((SELECT `EXTERNAL_PARTNER_INFO_ID` FROM  `KETTLE_MASTER_DEV`.`EXTERNAL_PARTNER_INFO` WHERE PARTNER_NAME = 'scm-service'), '1111', (SELECT `ACL_ID` FROM  `KETTLE_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` WHERE 
ACL_MODULE = 'kettle-service.budget-metadata.*'), 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES ('SOROIBFS', '7', 'ACTION', 'ADD', 'SuMo -> Request Ordering(Adhoc & Fixed Asset) -> Ignore Budget -> Force Submit', 'ACTIVE');
 
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME) VALUES
 ((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(select ACTION_DETAIL_ID from KETTLE_MASTER_DEV.ACTION_DETAIL where ACTION_CODE = 'SOROIBFS'), 
 'ACTIVE', '120955', '2018-02-23 00:00:00');
 
 
ALTER TABLE KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING ADD COLUMN RECIPE_PROFILE VARCHAR(5) NOT NULL;

UPDATE KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING SET RECIPE_PROFILE = "P0";