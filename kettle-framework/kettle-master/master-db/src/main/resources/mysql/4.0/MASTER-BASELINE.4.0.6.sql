INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP_TYPE` (`RTL_GROUP`, `RTL_CODE`, `RTL_NAME`, `STATUS`) 
VALUES ('OPTIONS', 'Hot Options', 'Hot Options', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP_TYPE` (`RTL_GROUP`, `RTL_CODE`, `RTL_NAME`, `STATUS`) 
VALUES ('OPTIONS', 'Cold Options', 'Cold Options', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP_TYPE` (`RTL_GROUP`, `RTL_CODE`, `RTL_NAME`, `STATUS`) 
VALUES ('OPTIONS', 'Food Options', 'Food Options', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) 
VALUES ('39', 'HoneyBySide', 'Honey By Side', 'HSID', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ('39', 'MilkBySide', 'Milk By Side', 'MSID', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ('39', 'NoHoney', 'No Honey', 'NHON', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) 
VALUES ('39', 'NoAlmond', 'No Almond', 'NALM', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ('40', 'NoSugar', 'No Sugar', 'NSUG', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) 
VALUES ('40', 'NoIce', 'No Ice', 'NICE', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ('40', 'NoLemon', 'No Lemon', 'NLMN', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ('41', 'NoButter', 'No Butter', 'NBTR', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) 
VALUES ('41', 'NoChutney', 'No Chutney', 'NCHT', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) 
VALUES ('41', 'NoCheese', 'No Cheese', 'NCHE', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) 
VALUES ('41', 'NoOnion', 'No Onion', 'NONI', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) 
VALUES ('41', 'NoCapcium', 'No Capcium', 'NCAP', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ('41', 'NoTomato', 'NoTomato', 'NTOM', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ('41', 'NoCucumber', 'NoCucumber', 'NCUC', 'ACTIVE');
