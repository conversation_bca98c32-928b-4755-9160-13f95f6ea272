CREATE TABLE KETTLE_MASTER_DEV.CONFIG_ATTRIBUTE_DEFINITION
(
ATTRIBUTE_DEF_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ATTRIBUTE_TYPE VARCHAR(20) NOT NULL,
ATTRIBUTE_CODE VARCHAR(100) NOT NULL,
ATTRIBUTE_NAME VARCHAR(100) NOT NULL,
DEFAULT_VALUE VARCHAR(500) NULL);

CREATE TABLE KETTLE_MASTER_DEV.CONFIG_ATTRIBUTE_VALUE
(
ATTRIBUTE_VALUE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ATTRIBUTE_DEF_ID INTEGER NOT NULL,
ATTRIBUTE_VALUE VARCHAR(500) NOT NULL,
APPLICATION_NAME VARCHAR(100) NOT NULL);

 
ALTER TABLE KETTLE_MASTER_DEV.CONFIG_ATTRIBUTE_VALUE ADD CONSTRAINT FK_CONFIG_ATTRIBUTE_DEFINITION
FOREIGN KEY (ATTRIBUTE_DEF_ID) REFERENCES CONFIG_ATTRIBUTE_DEFINITION(ATTRIBUTE_DEF_ID);



INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_DEF_ID`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`, `DEFAULT_VALUE`) VALUES ('1', 'boolean', 'DELASS', 'delivery.allotment.sdp.sms', 'false');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_DEF_ID`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('2', 'String', 'OTPCL', 'sms.otp.client');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_DEF_ID`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('3', 'String', 'TRCL', 'sms.transactional.client');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_DEF_ID`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('4', 'String', 'PRCL', 'sms.promotional.client');


INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ('1', '1', 'true', 'KETTLE_SERVICE');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ('2', '2', 'SOLUTION_INFINI', 'KETTLE_SERVICE');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ('3', '3', 'SOLUTION_INFINI', 'KETTLE_SERVICE');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ('4', '4', 'SMS_GUPSHUP', 'KETTLE_SERVICE');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ('5', '2', 'SOLUTION_INFINI', 'KETTLE_CRM');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ('6', '3', 'SOLUTION_INFINI', 'KETTLE_CRM');
INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ('7', '3', 'SOLUTION_INFINI', 'SCM_SERVICE');


INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_DEF_ID`, `ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('5', 'StringDelimited', 'ILNO', 'internal.nos');

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_VALUE` (`ATTRIBUTE_VALUE_ID`, `ATTRIBUTE_DEF_ID`, `ATTRIBUTE_VALUE`, `APPLICATION_NAME`) VALUES ('8', '5', '9599598307,9599598306', 'KETTLE_SERVICE');
