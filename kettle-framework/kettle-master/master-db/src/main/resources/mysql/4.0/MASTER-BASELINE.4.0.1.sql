CREATE TABLE KETTLE_MASTER_DEV.CANCELLATION_REASON(
CANCELLATION_REASON_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
ORDER_SOURCE VARCHAR(15) NOT NULL,
CANCELLATION_REASON_CODE VARCHAR(20) NOT NULL,
CANCELLATION_REASON_DESC VARCHAR(400) NOT NULL,
NO_WASTAGE VARCHAR(1) NOT NULL,
PARTIAL_WASTAGE VARCHAR(1) NOT NULL,
COMPLETE_WASTAGE VARCHAR(1) NOT NULL,
REASON_STATUS VARCHAR(15) NULL
);

INSERT INTO KETTLE_MASTER_DEV.CANCELLATION_REASON(ORDER_SOURCE,CANCELLATION_REASON_CODE,CANCELLATION_REASON_DESC,NO_WASTAGE,PARTIAL_WASTAGE,COMPLETE_WASTAGE,REASON_STATUS)
VALUES
('CAFE','CAFEIO','Incorrect order OR Customer Changed/Canceled Order','Y','N','N','ACTIVE'),
('CAFE','CAFEOD','Order Delayed','Y','Y','Y','ACTIVE'),
('CAFE','CAFECC','Customer Complaint','Y','Y','Y','ACTIVE'),
('TAKE_AWAY','TAKE_AWAYIO','Incorrect order OR Customer Changed/Canceled Order','Y','N','N','ACTIVE'),
('TAKE_AWAY','TAKE_AWAYOD','Order Delayed','Y','Y','Y','ACTIVE'),
('TAKE_AWAY','TAKE_AWAYCC','Customer Complaint','Y','Y','Y','ACTIVE'),
('COD','CODCNA','Customer Not Available','N','N','Y','ACTIVE'),
('COD','CODINA','Inventory Not Availabe','Y','N','N','ACTIVE'),
('COD','CODIO','Incorrect Order','Y','N','N','ACTIVE'),
('COD','CODITIAC','Internet Technical Issue At Cafe','Y','N','N','ACTIVE'),
('COD','CODOODA','Out Of Delivery Area','Y','Y','Y','ACTIVE'),
('COD','CODOCBP','Order Cancelled By Partner','Y','Y','Y','ACTIVE'),
('COD','CODPWDD','Product Wastage During Delivery','N','Y','Y','ACTIVE'),
('COD','CODDWSD','Delivery of Wrong/Short Order','N','Y','Y','ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (API, STATUS) VALUES ('scm-service.stock-management.kettle-wastage-event', 'ACTIVE');

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN IS_TOKEN_ENABLED VARCHAR(1) DEFAULT 'N';

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN TOKEN_LIMIT INTEGER NOT NULL DEFAULT 0;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS) VALUES ('ORDASO', '7', 'SUBMENU', 'SHOW', 'SuMo -> Ordering -> Asset Ordering -> ORDASO -> SHOW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME) VALUES ('2',(select ACTION_DETAIL_ID from KETTLE_MASTER_DEV.ACTION_DETAIL where ACTION_CODE = 'ORDASO'), 'ACTIVE', '100000', '2017-07-01 00:00:00');


/* DENOMINATION 200 ADDED TO CASH
INSERT INTO `KETTLE_MASTER_DEV`.`DENOMINATION` (`PAYMENT_MODE`, `DENOMINATION_TEXT`, `DENOMINATION_CODE`, `DENOMINATION_VALUE`, `DISPLAY_ORDER`, `STATUS`, `BUNDLE_SIZE`) VALUES ('1', 'Two Hundred', 'TWO HUNDRED', '200', '4', 'ACTIVE', '100');
UPDATE `KETTLE_MASTER_DEV`.`DENOMINATION` SET `DISPLAY_ORDER`='5' WHERE `DENOMINATION_ID`='3';
UPDATE `KETTLE_MASTER_DEV`.`DENOMINATION` SET `DISPLAY_ORDER`='6' WHERE `DENOMINATION_ID`='4';
UPDATE `KETTLE_MASTER_DEV`.`DENOMINATION` SET `DISPLAY_ORDER`='7' WHERE `DENOMINATION_ID`='5';
UPDATE `KETTLE_MASTER_DEV`.`DENOMINATION` SET `DISPLAY_ORDER`='8' WHERE `DENOMINATION_ID`='6';
UPDATE `KETTLE_MASTER_DEV`.`DENOMINATION` SET `DISPLAY_ORDER`='9' WHERE `DENOMINATION_ID`='7';
UPDATE `KETTLE_MASTER_DEV`.`DENOMINATION` SET `DISPLAY_ORDER`='10' WHERE `DENOMINATION_ID`='8';
UPDATE `KETTLE_MASTER_DEV`.`DENOMINATION` SET `DISPLAY_ORDER`='11' WHERE `DENOMINATION_ID`='9';
*/