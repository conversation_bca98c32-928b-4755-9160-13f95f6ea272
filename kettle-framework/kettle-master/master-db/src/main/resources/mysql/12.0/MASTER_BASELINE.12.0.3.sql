
CREATE INDEX IDX_UNIT_CLOSURE_EVENT_UNIT_ID ON KETTLE_MASTER_DEV.UNIT_CLOSURE_EVENT(UNIT_ID);


INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA
(ROL<PERSON>_NAME, <PERSON><PERSON><PERSON>_DESCRIPTION, R<PERSON><PERSON>_STATUS, APPLICATION_ID)
VALUES ('System Metadata Update Access', 'Access to update system metadata', 'ACTIVE', '5');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_SM_MGT', '5', 'MENU', 'SHOW', 'Admin -> System Metadata Mapping', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (<PERSON><PERSON><PERSON>_<PERSON>, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'System Metadata Update Access'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_SM_MGT'), 'ACTIVE', '120057', '2023-12-28 10:00:00');

CREATE TABLE IF NOT EXISTS KETTLE_MASTER_DEV.FEEDBACK_QUESTIONS_DETAIL (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    QUESTION TEXT,
    QUESTION_TYPE VARCHAR(255),
    STATUS VARCHAR(255),
    UPDATED_BY INT,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE IF NOT EXISTS KETTLE_MASTER_DEV.FEEDBACK_QUESTIONS_UNIT_MAPPING (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    UNIT_NAME VARCHAR(255),
    QUESTION_ID INT,
    QUESTION_TYPE VARCHAR(255),
    STATUS VARCHAR(255),
    UPDATED_BY INT,
    UPDATED_AT TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE KETTLE_MASTER_DEV.FEEDBACK_QUESTIONS_DETAIL
ADD COLUMN QUESTION_REASON TEXT DEFAULT NULL;

alter table KETTLE_STAGE.CHAAYOS_BRANCH_UNIT_MAPPING ADD column BRAND_ID integer default NULL;
alter table KETTLE_MASTER_STAGE.CHANNEL_PARTNER ADD  PARTNER_NAME VARCHAR(20) DEFAULT NULL;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('KETTLE_REF_ODR_CACHE', '1', 'ACTION', 'SHOW', 'Kettle-Service -> REFRESH ORDER CACHE', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Kettle Refresh Order Cache', 'Refresh Order Info Cache for Unit', 'ACTIVE', '1');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Kettle Refresh Order Cache'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'KETTLE_REF_ODR_CACHE'), 'ACTIVE', '100026', '2024-01-16 00:00:00');


CREATE TABLE KETTLE_MASTER_DEV.UNIT_CLOSURE_EVENT (
  REQUEST_ID BIGINT AUTO_INCREMENT NOT NULL,
   UNIT_ID INT NOT NULL,
   CLOSURE_DATE DATE NOT NULL,
   CLOSURE_STATUS VARCHAR(255) NOT NULL,
   CREATED_BY VARCHAR(255) NOT NULL,
   CREATION_TIME DATETIME NOT NULL,
   UPDATED_BY VARCHAR(255) NULL,
   LAST_UPDATION_TIME DATETIME NULL,
   CONSTRAINT PK_UNIT_CLOSURE_EVENT PRIMARY KEY (REQUEST_ID)
);

CREATE INDEX IDX_UNIT_CLOSURE_EVENT_UNIT_ID ON KETTLE_MASTER_DEV.UNIT_CLOSURE_EVENT(UNIT_ID);

CREATE TABLE KETTLE_MASTER_STAGE.CUSTOMER_WINBACK_OFFER_INFO (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    CUSTOMER_ID INT DEFAULT NULL,
    CONTACT_NUMBER VARCHAR(255) DEFAULT NULL,
    CUSTOMER_NAME VARCHAR(255) DEFAULT NULL,
    OFFER_ID INT DEFAULT NULL,
    OFFER_DESCRIPTION TEXT DEFAULT NULL,
    COUPON_CODE VARCHAR(255) DEFAULT NULL,
    COUPON_DETAIL_ID INT DEFAULT NULL,
    START_DATE VARCHAR(255) DEFAULT NULL,
    END_DATE VARCHAR(255) DEFAULT NULL,
    VALIDITY_DAYS INT DEFAULT NULL,
    COMPANSATION_REASON TEXT DEFAULT NULL,
    COMPLAIN_SOURCE VARCHAR(255) DEFAULT NULL,
    COMMENT TEXT DEFAULT NULL,
    REFERENCE_ORDER_ID VARCHAR(255) DEFAULT NULL,
    ORDER_SOURCE VARCHAR(255) DEFAULT NULL,
    CHANNEL_PARTNER VARCHAR(255) DEFAULT NULL,
    IS_NOTIFIED VARCHAR(255) DEFAULT NULL,
    UPDATED_BY INT DEFAULT NULL,
    UPDATED_AT DATE DEFAULT NULL
);



CREATE TABLE KETTLE_MASTER_STAGE.DELIVERY_OFFER_DETAIL_DATA (
    ID INT AUTO_INCREMENT PRIMARY KEY,
    OFFER_CODE VARCHAR(255) DEFAULT NULL,
    OFFER_DESCRIPTION TEXT DEFAULT NULL,
    OFFER_STATUS VARCHAR(255) DEFAULT NULL,
    OFFER_CATEGORIES VARCHAR(255) DEFAULT NULL
);

ALTER TABLE `KETTLE_MASTER_STAGE`.`DELIVERY_COUPON_ALLOCATION_DETAIL_DATA`
CHANGE COLUMN `CUSTOMER_ID` `CUSTOMER_ID` INT(11) DEFAULT NULL ;

ALTER TABLE KETTLE_STAGE.ORDER_COMPLAINT_DETAIL_DATA ADD COLUMN FALLBACK_ERRORS TEXT default NULL;

NSERT INTO KETTLE_MASTER_STAGE.ACCESS_CONTROL_LIST_DATA (ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME) VALUES ('channel-partner.partner-management.get-edited-order-original-request', 'GET edit order Api', 'ACTIVE', 'CHANNEL_PARTNER');
INSERT INTO  KETTLE_MASTER_STAGE.PARTNER_PERMISSION_MAPPING (PARTNER_ID, PERMISSION, ACL_ID, PPM_STATUS)
VALUES (
(SELECT EXTERNAL_PARTNER_INFO_ID FROM KETTLE_MASTER_STAGE.EXTERNAL_PARTNER_INFO where PARTNER_CODE = "neo-redis-client")
, 1111
,(Select ACL_ID from KETTLE_MASTER_STAGE.ACCESS_CONTROL_LIST_DATA where ACL_MODULE like "channel-partner.partner-management.get-edited-order-original-request")
, "ACTIVE");

UPDATE `KETTLE_MASTER_STAGE`.`BRAND_ATTRIBUTE_DETAIL` SET `ATTRIBUTE_VALUE` = 'CHAAYS' WHERE (`ATTRIBUTE_ID` = '24');

CREATE TABLE KETTLE_MASTER_STAGE.UNIT_WS_TO_STATION_CATEGORY_MAPPING
(
    UNIT_WS_TO_STATION_MAPPING_ID INT AUTO_INCREMENT NOT NULL PRIMARY KEY ,
    UNIT_ID                       INT                NOT NULL,
    WORK_STATION_NAME             VARCHAR(255)       NOT NULL,
    STATION_CATEGORY              VARCHAR(255)       NOT NULL,
    CREATED_BY                    INT                NOT NULL,
    CREATION_TIME                 DATETIME           NULL,
    MAPPING_STATUS                VARCHAR(255)       NOT NULL
);

INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP_TYPE` (`RTL_GROUP`, `RTL_CODE`, `RTL_NAME`, `STATUS`) VALUES
    ('WORK_STATIONS_STATION_CATEGORY', 'WSSC', 'WORK_STATIONS_STATION_CATEGORY', 'ACTIVE');



INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES (
                                                                                                                   (SELECT RTL_ID FROM KETTLE_MASTER.REF_LOOKUP_TYPE WHERE RTL_GROUP = "WORK_STATIONS_STATION_CATEGORY"),
                                                                                                                   'PIZZA', 'PIZZA', 'PZA', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES (
                                                                                                                   (SELECT RTL_ID FROM KETTLE_MASTER.REF_LOOKUP_TYPE WHERE RTL_GROUP = "WORK_STATIONS_STATION_CATEGORY"),
                                                                                                                   'CHAAT', 'CHAAT', 'CHT', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES (
                                                                                                                   (SELECT RTL_ID FROM KETTLE_MASTER.REF_LOOKUP_TYPE WHERE RTL_GROUP = "WORK_STATIONS_STATION_CATEGORY"),
                                                                                                                   'BAKERY', 'BAKERY', 'BKRY', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES (
                                                                                                                   (SELECT RTL_ID FROM KETTLE_MASTER.REF_LOOKUP_TYPE WHERE RTL_GROUP = "WORK_STATIONS_STATION_CATEGORY"),
                                                                                                                   'MEALS', 'MEALS', 'MEALS', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES (
                                                                                                                   (SELECT RTL_ID FROM KETTLE_MASTER.REF_LOOKUP_TYPE WHERE RTL_GROUP = "WORK_STATIONS_STATION_CATEGORY"),
                                                                                                                   'COLD', 'COLD', 'COLD', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES (
                                                                                                                   (SELECT RTL_ID FROM KETTLE_MASTER.REF_LOOKUP_TYPE WHERE RTL_GROUP = "WORK_STATIONS_STATION_CATEGORY"),
                                                                                                                   'SANDWITCH', 'SANDWITCH', 'SANDWITCH', 'ACTIVE');


ALTER TABLE `KETTLE_MASTER_STAGE`.`PRODUCT_DETAIL`
    ADD COLUMN `STATION_CATEGORY` INT(11) NULL AFTER `PRODUCT_SUB_TYPE`;



CREATE TABLE `KETTLE_MASTER_STAGE`.`UNIT_PRODUCT_PACKAGING_MAPPING` (
  `MAPPING_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NULL,
  `PRODUCT_ID` INT(11) NULL,
  `PACKAGING_TYPE` VARCHAR(45) NULL,
  `MAPPING_STATUS` VARCHAR(45) NULL,
  `PACKAGING_VALUE` DECIMAL(16,6) NULL,
  PRIMARY KEY (`MAPPING_ID`));

  ALTER TABLE `KETTLE_MASTER_STAGE`.`UNIT_PRODUCT_PACKAGING_MAPPING`
  ADD COLUMN `UPDATED_BY` INT(11) NULL AFTER `PACKAGING_VALUE`,
  ADD COLUMN `UPDATED_AT` DATE NULL AFTER `UPDATED_BY`;

  ALTER TABLE `KETTLE_MASTER_STAGE`.`UNIT_PRODUCT_PACKAGING_MAPPING`
  CHANGE COLUMN `UPDATED_AT` `UPDATED_AT` TIMESTAMP NULL DEFAULT NULL ;

  ALTER TABLE `KETTLE_MASTER_STAGE`.`UNIT_PRODUCT_PACKAGING_MAPPING`
  CHANGE COLUMN `UNIT_ID` `UNIT_ID` INT(11) NOT NULL ,
  ADD INDEX `UNIQUE_UNIT_PRODUCT_IDX` (`UNIT_ID` ASC, `PRODUCT_ID` ASC);

  ALTER TABLE `KETTLE_MASTER_STAGE`.`MENU_SEQUENCE_TIMING_DATA`
  ADD COLUMN `DAY_SLOTS` VARCHAR(1000) NULL AFTER `END_DATE`;


INSERT INTO `KETTLE_MASTER`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES (
                                                                                                                   (SELECT RTL_ID FROM KETTLE_MASTER.REF_LOOKUP_TYPE WHERE RTL_GROUP = "WORK_STATIONS_STATION_CATEGORY"),
                                                                                                                   'SANDWITCH', 'SANDWITCH', 'SANDWITCH', 'ACTIVE');

CREATE TABLE KETTLE_MASTER_STAGE.UNIT_DROOL_VERSION_MAPPING (
	MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    DROOL_TYPE VARCHAR(500) NOT NULL,
    UNIT_ID INTEGER NOT NULL,
    MAPPING_STATUS VARCHAR(100) NOT NULL,
    VERSION VARCHAR(100) NOT NULL,
    CREATION_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CREATED_BY INTEGER,
    LAST_UPDATION_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UPDATED_BY INTEGER,
    REMARKS TEXT,
    KEY `DROOL_MAPPING_UNIT_ID` (`UNIT_ID`) USING BTREE,
    KEY `DROOL_MAPPING_VERSION` (`VERSION`) USING BTREE,
    KEY `DROOL_MAPPING_MAPPING_STATUS` (`MAPPING_STATUS`) USING BTREE
);

