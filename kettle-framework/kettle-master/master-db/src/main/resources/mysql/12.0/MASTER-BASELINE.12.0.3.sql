CREATE TABLE KETTLE_MASTER_STAGE.MONK_RECIPE_VERSION_DETAIL(
	MONK_RECIPE_VERSION_DETAIL_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INT(11),
    PREVIOUS_VERSION VARCHAR(30),
    PREVIOUS_RECIPE_COUNT INT(11),
    CURRENT_VERSION VARCHAR(30),
    CURRENT_RECIPE_COUNT INT(11),
    UPDATED_BY INT(11),
    UPDATED_TIME TIMESTAMP
);

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_RPT_MPG', '5', 'SUBMENU', 'SHOW', 'Admin -> BRM Reports -> Unit Report Mapping', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, R<PERSON><PERSON>_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('BRM Reports Unit Report Mapping', 'Add Unit Report Mapping access to kettle admin', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'BRM Reports Unit Report Mapping'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_RPT_MPG'), 'ACTIVE', '120063', '2023-11-17 00:00:00');

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN ASSEMBLY_STRICT_MODE VARCHAR(1) default 'N';
ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN ASSEMBLY_OTP_MODE VARCHAR(1) default 'N';