CREATE TABLE KETTLE_MASTER_DEV.CACHE_REFERENCE_METADATA (
    CACHE_ID INT AUTO_INCREMENT NOT NULL,
    REFERENCE_TYPE VARCHAR(50) NOT NULL,
    REFERENCE_VALUE VARCHAR(50) NOT NULL,
    CACHE_STATUS VARCHAR(50) NOT NULL,
    ADDED_BY VARCHAR(50) NOT NULL,
    UPDATED_BY VARCHAR(50) NOT NULL,
    CONSTRAINT PK_CACHE_REFERENCE_METADATA PRIMARY KEY (CACHE_ID)
);

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_RCP_ADD', '5', 'SUBMENU', 'SHOW', 'Admin -> Add Recipe -> show', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Kettle Recipe Management Add Recipe', 'Add Recipe access to kettle admin', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Kettle Recipe Management Add Recipe'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_RCP_ADD'), 'ACTIVE', '120063', '2023-03-30 00:00:00');


INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_RCP_FND', '5', 'SUBMENU', 'SHOW', 'Admin -> Find Recipe -> show', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Kettle Recipe Management Find Recipe', 'Find Recipe access to kettle admin', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Kettle Recipe Management Find Recipe'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_RCP_FND'), 'ACTIVE', '120063', '2023-03-30 00:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Master Documents', 'Access to see Master Documents Tab', 'ACTIVE', '12');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL`
 (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SMD', '12', 'MENU', 'SHOW', 'SuMo -> Service ->Master Documents', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING`
 (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Master Documents'),
(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SMD'),
'ACTIVE', '120063', '2023-01-12 12:00:00');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA(ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME) VALUES ('kettle-service.order-management.order.monthly.aov', 'Caculate Monthly AOV For Zomato', 'ACTIVE', 'KETTLE_SERVICE');
INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING(`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ((SELECT EXTERNAL_PARTNER_INFO_ID FROM KETTLE_MASTER_DEV.EXTERNAL_PARTNER_INFO WHERE PARTNER_CODE="CHANNEL-PARTNER" ) , '1111', (SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE="kettle-service.order-management.order.monthly.aov"), 'ACTIVE');

CREATE TABLE KETTLE_MASTER_DEV.UNIT_TO_PARTNER_EDC_MAPPING (
                                                               MAPPING_ID INT(11) NOT NULL AUTO_INCREMENT,
                                                               UNIT_ID INT(11) NOT NULL,
                                                               STATUS VARCHAR(45) NULL,
                                                               PARTNER_NAME VARCHAR(100) NULL,
                                                               MERCHANT_ID VARCHAR(100) NULL,
                                                               TERMINAL_ID VARCHAR(100) NULL,
                                                               MERCHANT_KEY VARCHAR(100) NULL,
                                                               VERSION VARCHAR(100) NULL,
                                                               PRIMARY KEY (MAPPING_ID));

CREATE INDEX UNIT_TO_PARTNER_EDC_MAPPING_UNIT_ID ON KETTLE_MASTER_DEV.UNIT_TO_PARTNER_EDC_MAPPING(UNIT_ID) USING BTREE;
CREATE INDEX UNIT_TO_PARTNER_EDC_MAPPING_STATUS ON KETTLE_MASTER_DEV.UNIT_TO_PARTNER_EDC_MAPPING(STATUS) USING BTREE;




ALTER TABLE `KETTLE_MASTER_DEV`.`UNIT_DETAIL`
ADD COLUMN `IS_CLOSED` VARCHAR(1) NULL;

INSERT INTO KETTLE_MASTER_DEV.CACHE_REFERENCE_METADATA
(REFERENCE_TYPE, REFERENCE_VALUE, CACHE_STATUS, ADDED_BY, UPDATED_BY) VALUES
("IS_HOTSPOT_ENABLED_FOR_UNIT","","ACTIVE","SYSTEM",100026);

INSERT INTO KETTLE_MASTER_DEV.CACHE_REFERENCE_METADATA
(REFERENCE_TYPE, REFERENCE_VALUE, CACHE_STATUS, ADDED_BY, UPDATED_BY) VALUES
("HOTSPOT_ENABLED_FOR_SYSTEM","N","ACTIVE","SYSTEM",100026); 
