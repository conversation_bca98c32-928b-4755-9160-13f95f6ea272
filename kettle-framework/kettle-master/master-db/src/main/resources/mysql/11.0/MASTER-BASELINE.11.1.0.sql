CREATE TABLE KETTLE_MASTER`PRODUCT_CITY_IMAGE_MAPPING` (
  `PRODUCT_CITY_IMAGE_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PRODUCT_ID` int(11) DEFAULT NULL,
  `IMAGE_URL` varchar(255) DEFAULT NULL,
  `IMAGE_TYPE` varchar(100) DEFAULT NULL,
  `STATUS` varchar(50) DEFAULT NULL,
  `LAST_UPDATED_BY` int(11) DEFAULT NULL,
  `LAST_UPDATION_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `CITY_NAME` VARCHAR(200) DEFAULT NULL,
  PRIMARY KEY (`PRODUCT_CITY_IMAGE_MAPPING_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2855 DEFAULT CHARSET=latin1