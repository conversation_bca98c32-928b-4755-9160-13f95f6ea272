ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN FSSAI VARCHAR(100);

ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING
    ADD COLUMN PRICE_PROFILE_ID INTEGER NULL;
CREATE TABLE KETTLE_MASTER_DEV.PRICE_PROFILE(
                                                PRICE_PROFILE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
                                                PROFILE_DESCRIPTION VARCHAR(300) NOT NULL,
                                                PROFILE_TYPE VARCHAR(30) NOT NULL,
                                                PROFILE_CREATION_TIME TIMESTAMP NOT NULL,
                                                LAST_UPDATE_TIME TIMESTAMP NOT NULL,
                                                LAST_UPDATE_BY VARCHAR(100) NOT NULL,
                                                PROFILE_STATUS VARCHAR(15) NOT NULL
);
CREATE INDEX PRICE_PROFILE_PROFILE_TYPE ON KETTLE_MASTER_DEV.PRICE_PROFILE(PROFILE_TYPE) USING BTREE;
CREATE INDEX PRICE_PROFILE_PROFILE_STATUS ON KETTLE_MASTER_DEV.PRICE_PROFILE(PROFILE_STATUS) USING BTREE;
CREATE TABLE KETTLE_MASTER_DEV.PRICE_PROFILE_RANGE_VALUES(
                                                             PRICE_PROFILE_RANGE_VALUES_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
                                                             PRICE_PROFILE_ID INTEGER NOT NULL,
                                                             START_PRICE DECIMAL(10,2) NOT NULL,
                                                             END_PRICE DECIMAL(10,2) NOT NULL,
                                                             DELTA_PRICE DECIMAL(10,2) NOT NULL,
                                                             RANGE_VALUES_STATUS VARCHAR(15) NOT NULL,
                                                             LAST_UPDATE_TIME TIMESTAMP NOT NULL,
                                                             LAST_UPDATE_BY VARCHAR(100) NOT NULL,
                                                             RANGE_VALUES_ACTIVATION_TIME TIMESTAMP NOT NULL
);
CREATE INDEX PRICE_PROFILE_RANGE_VALUES_PRICE_PROFILE_ID ON KETTLE_MASTER_DEV.PRICE_PROFILE_RANGE_VALUES(PRICE_PROFILE_ID) USING BTREE;
CREATE INDEX PRICE_PROFILE_RANGE_VALUES_START_PRICE ON KETTLE_MASTER_DEV.PRICE_PROFILE_RANGE_VALUES(START_PRICE) USING BTREE;
CREATE INDEX PRICE_PROFILE_RANGE_VALUES_END_PRICE ON KETTLE_MASTER_DEV.PRICE_PROFILE_RANGE_VALUES(END_PRICE) USING BTREE;
CREATE INDEX PRICE_PROFILE_RANGE_VALUES_DELTA_PRICE ON KETTLE_MASTER_DEV.PRICE_PROFILE_RANGE_VALUES(DELTA_PRICE) USING BTREE;
CREATE INDEX PRICE_PROFILE_RANGE_VALUES_RANGE_VALUES_STATUS ON KETTLE_MASTER_DEV.PRICE_PROFILE_RANGE_VALUES(RANGE_VALUES_STATUS) USING BTREE;


ALTER TABLE KETTLE_MASTER_DEV.PRICE_PROFILE ADD THRESHOLD_PERCENTAGE DECIMAL(10,2) NOT NULL;

CREATE INDEX UNIT_CHANNEL_PARTNER_MENU_MAPPING_PRICE_PROFILE_ID ON KETTLE_MASTER.UNIT_CHANNEL_PARTNER_MENU_MAPPING(PRICE_PROFILE_ID) USING BTREE
;