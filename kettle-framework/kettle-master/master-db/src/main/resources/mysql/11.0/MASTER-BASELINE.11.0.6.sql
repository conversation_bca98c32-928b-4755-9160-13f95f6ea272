INSERT INTO `<PERSON>ETTL<PERSON>_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`)
 VALUES ('scm-service.production-booking.*', 'Production Booking ', 'ACTIVE', 'SCM_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID , PERMISSION, ACL_ID, PPM_STATUS)
VALUES( 4,1111, (SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA  WHERE ACL_MODULE ='scm-service.production-booking.*') ,'ACTIVE')



ALTER TABLE KETTLE_MASTER_STAGE.UNIT_TO_PARTNER_EDC_MAPPING ADD COLUMN T_ID VARCHAR(20) NOT NULL;