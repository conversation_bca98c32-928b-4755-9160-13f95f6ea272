ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN CLONED_FROM INT NULL;


INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING( PARTNER_ID, PERMISSION, AC<PERSON>_ID, PPM_STATUS)
SELECT 4,1111,AC<PERSON>_ID,'ACTIVE' FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA where ACL_MODULE='scm-service.reference-order-management.*';

INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('19', '1111', '112', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `R<PERSON>E_STATUS`, `APPLICATION_ID`)
VALUES ('Remove Duplicate Keys', 'Access to Remove Duplicate Keys', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('RDK', '7', 'ACTION', 'UPDATE', 'SuMo -> CURRENT INVENTORY -> REMOVE DUPLICATE KEYS', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Remove Duplicate Keys'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'RDK'), 'ACTIVE', '120057', '2021-12-29 12:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` (`ACL_ID`, `ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES (NULL, 'kettle-service.order-management.order.cancel', 'Kettle Order cancel API access', 'ACTIVE', 'KETTLE_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID,PERMISSION,ACL_ID,PPM_STATUS)
VALUES(10, 1111, (SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE = 'kettle-service.order-management.order.cancel'), 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (API, STATUS) VALUES ('channel-partner.partner-metadata-management.activate-cafe-for-unit.*', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (API,STATUS) VALUES ('master-service.users.single-sign-in','ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.APPLICATION_DATA (APPLICATION_NAME, APPLICATION_DESCRIPTION,APPLICATION_STATUS)
VALUES ('KNOCK_SERVICE', 'KNOCK_SERVICE', 'ACTIVE');

ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION ADD COLUMN KNOCK_APPLICATION_ACCESS VARCHAR(10)  DEFAULT 'N' NOT  NULL ;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN SALES_CLONED_FROM INTEGER(11);

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN PROBABLE_OPENING_DATE TIMESTAMP NULL;

INSERT INTO `KETTLE_MASTER_DEV`.`BRAND_ATTRIBUTE_DETAIL` (`BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('1', 'CHAAYOS_SUBSCRIPTION', 'chaayos.com/pages/chaayos-select', 'java.lang.string');

INSERT INTO `KETTLE_MASTER_DEV`.`BRAND_ATTRIBUTE_DETAIL` (`BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('2', 'CHAAYOS_SUBSCRIPTION', 'chaayos.com/pages/chaayos-select', 'java.lang.string');
