ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN SHORT_CODE varchar(10) UNIQUE;

CREATE TABLE `KETTLE_MASTER_DEV.MENU_TO_SCM_PRODUCT_MAP` (
  `KEY_ID` int(11) NOT NULL  AUTO_INCREMENT PRIMARY KEY,
  `MENU_PRODUCT_ID` int(11) DEFAULT NULL,
  `MENU_PRODUCT_DIMENSION` varchar(50) DEFAULT NULL,
  `RECIPE_PROFILE` varchar(6) DEFAULT NULL,
  `RECIPE_ID` int(11) DEFAULT NULL,
  `SCM_PRODUCT_ID` int(11) DEFAULT NULL,
  `SCM_PRODUCT_NAME` varchar(200) DEFAULT NULL,
  `SCM_PRODUCT_UOM` varchar(15) DEFAULT NULL,
  `SCM_PRODUCT_QUANTITY` decimal(10,2) DEFAULT NULL,
  <PERSON><PERSON><PERSON> `MENU_TO_SCM_PRODUCT_MAP_MENU_PRODUCT_ID` (`MENU_PRODUCT_ID`) USING BTREE,
  <PERSON><PERSON><PERSON> `MENU_TO_SCM_PRODUCT_MAP_MENU_PRODUCT_DIMENSION` (`MENU_PRODUCT_DIMENSION`) USING BTREE,
  KEY `MENU_TO_SCM_PRODUCT_MAP_RECIPE_PROFILE` (`RECIPE_PROFILE`) USING BTREE,
  KEY `MENU_TO_SCM_PRODUCT_MAP_RECIPE_ID` (`RECIPE_ID`) USING BTREE,
  KEY `MENU_TO_SCM_PRODUCT_MAP_SCM_PRODUCT_ID` (`SCM_PRODUCT_ID`) USING BTREE,
  KEY `MENU_TO_SCM_PRODUCT_MAP_SCM_PRODUCT_UOM` (`SCM_PRODUCT_UOM`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Force Upload Payment Sheet', 'Access to Force Upload Payment Sheet', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('FUPS', '7', 'ACTION', 'INSERT', 'SuMo -> Manage Payments -> Settle Payments -> Force Upload Payment Sheet', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Force Upload Payment Sheet'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FUPS'), 'ACTIVE', '120057', '2022-01-07 12:00:00');
