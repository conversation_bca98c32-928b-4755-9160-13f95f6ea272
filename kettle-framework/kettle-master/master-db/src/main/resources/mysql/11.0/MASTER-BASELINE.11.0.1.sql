ALTER TABLE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY
ADD COLUMN DISCOUNT_CATEGORY VARCHAR(50) NULL;

ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_SEQUENCE
ADD COLUMN IS_RECOMMENDED VARCHAR(1) NULL;

UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='MARKETING' WHERE CATEGORY_NAME ='Fixed Value - Marketing';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='BUSINESS_DEVELOPMENT' WHERE CATEGORY_NAME ='Fixed Value - BD';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='MARKETING' WHERE CATEGORY_NAME ='Marketing Offer';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='MARKETING' WHERE CATEGORY_NAME ='Corporate Coupon';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='OPERATIONS' WHERE CATEGORY_NAME ='ODC Discount';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='LOYAL_TEA' WHERE CATEGORY_NAME ='Loyaltea';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='EMP_DISCOUNT_EMPLOYEE_FICO' WHERE CATEGORY_NAME ='Employee Discount';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='MARKETING' WHERE CATEGORY_NAME ='Marketing Alliance';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='EMPLOYEE_FICO' WHERE CATEGORY_NAME ='FICO Discount';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='MARKETING' WHERE CATEGORY_NAME ='PR Coupon';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='MARKETING' WHERE CATEGORY_NAME ='Partners Discount';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='OPERATIONS' WHERE CATEGORY_NAME ='Bulk Order Discount';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='MARKETING' WHERE CATEGORY_NAME ='Prepaid Coupons';
UPDATE KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY SET DISCOUNT_CATEGORY='MARKETING' WHERE CATEGORY_NAME ='CLM Offer';

CREATE TABLE KETTLE_MASTER_DEV.SIGNUP_OFFER_COUPON_DETAILS(
    KEY_ID INT AUTO_INCREMENT,
    OFFER_DETAIL_ID INT NOT NULL,
    COUPON_CODE VARCHAR(50) NOT NULL,
    COUPON_PREFIX VARCHAR(40) NOT NULL,
    COUPON_STATUS VARCHAR(40) NOT NULL,
    COUPON_VALIDITY VARCHAR(40),
    PRIMARY KEY(KEY_ID)
);

ALTER TABLE `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL`
CHANGE COLUMN `TAXABLE_COGS` `TAXABLE_COGS` VARCHAR(10) NULL DEFAULT 'Y' ;

ALTER TABLE `KETTLE_MASTER_DEV`.`UNIT_PRODUCT_PRICING`
ADD COLUMN UPDATED_BY INT NULL;




INSERT INTO `KETTLE_MASTER_DEV`.`OFFER_DETAIL_DATA`
 ( `OFFER_CATEGORY`, `OFFER_TYPE`, `OFFER_TEXT`, `OFFER_DESCRIPTION`, `START_DATE`, `END_DATE`, `OFFER_STATUS`, `MIN_VALUE`, `VALIDATE_CUSTOMER`, `INCLUDE_TAXES`,
 `PRIORITY`, `OFFER_SCOPE`, `MIN_ITEM_COUNT`, `QUANTITY_LIMIT`, `LOYALTY_LIMIT`, `OFFER_VALUE`, `ACCOUNTS_CATEGORY`, `PREPAID`, `REMOVE_LOYALTY_REWARD`, `OTP_REQUIRED`)
 VALUES ( 'BILL', 'PERCENTAGE_BILL_STRATEGY', 'Channel Partner Delivery', 'Channel Partner Delivery', '2017-10-12', '2017-12-03', 'ARCHIVED', '0', 'Y', 'Y', '0', 'CUSTOMER', '1',
 '1', '0', '0', (SELECT CATEGORY_ID FROM KETTLE_MASTER_DEV.OFFER_ACCOUNT_CATEGORY WHERE CATEGORY_NAME="Partners Discount"), 'N', 'N', 'N');




 INSERT INTO `KETTLE_MASTER_DEV`.`COUPON_DETAIL_DATA` ( `OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`, `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`, `MANUAL_OVERRIDE`)
 VALUES ( (SELECT OFFER_DETAIL_ID FROM `KETTLE_MASTER_DEV`.`OFFER_DETAIL_DATA` WHERE OFFER_TEXT='Channel Partner Delivery'), 'ZOMATO', '2017-10-12', '2017-12-03', 'Y', 'Y', '500', 'ACTIVE', '256', 'N');


UPDATE  KETTLE_DEV.ORDER_DETAIL O
JOIN KETTLE_DEV.CHANNEL_PARTNER C
ON C.PARTNER_ID=O.CHANNEL_PARTNER_ID
SET O.OFFER_CODE=C.PARTNER_CODE
WHERE O.ORDER_SOURCE='COD' AND OFFER_CODE IS NULL AND  O.TAXABLE_AMOUNT<>O.TOTAL_AMOUNT AND O.ORDER_STATUS NOT IN ('CANCELLED','CANCELLED_REQUESTED');



INSERT INTO `KETTLE_MASTER_DEV`.`PRE_AUTHENTICATED_API` (`API`, `STATUS`) VALUES ('scm-service.stock-management.get-consumption', 'ACTIVE');



INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('FORMS SERVICE PNL_ADJUSTMENT_ROLE', 'Access to PnL Adjustment', 'ACTIVE',11);

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE,APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_DESCRIPTION,ACTION_STATUS)
VALUES('FSPNL', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'MENU', 'VIEW', 'Forms service -> PnL Adjustment -> show', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FORMS SERVICE PNL_ADJUSTMENT_ROLE'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSPNL'),'ACTIVE',120057,'2021-06-22 17:56:00');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('FORMS SERVICE PNL_ADJUSTMENT_CREATE', 'Access to PnL Adjustment Creation', 'ACTIVE',11);

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE,APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_DESCRIPTION,ACTION_STATUS)
VALUES('FSPNL_CR', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'),
       'SUBMENU', 'SHOW', 'PnL Adjustment -> PnL Adjustment create -> show', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FORMS SERVICE PNL_ADJUSTMENT_CREATE'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSPNL_CR'),'ACTIVE',120057,'2021-06-22 17:56:00');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('FORMS SERVICE PNL_ADJUSTMENT_ARA', 'Access to PnL Adjustment ARA', 'ACTIVE',11);

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE,APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_DESCRIPTION,ACTION_STATUS)
VALUES('FSPNL_ARA', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'),
       'SUBMENU', 'SHOW', 'PnL Adjustment -> PnL Adjustment ARA -> show', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FORMS SERVICE PNL_ADJUSTMENT_ARA'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSPNL_ARA'),'ACTIVE',120057,'2021-06-22 17:56:00');



INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('KETTLE ADMIN PnL Report', 'Access to PnL Report', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,`ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ADMN_BGT_PRD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> PnL Report ->SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'KETTLE ADMIN PnL Report'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_PRD'), 'ACTIVE', 120057, '2021-01-19 00:00:00');

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN FREQUENCY_STRATEGY VARCHAR(30) NULL;
CREATE INDEX OFFER_DETAIL_DATA_FREQUENCY_STRATEGY ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(FREQUENCY_STRATEGY)USING BTREE;

update KETTLE_MASTER_DEV.OFFER_DETAIL_DATA set FREQUENCY_STRATEGY = 'TIME_BASED' where OFFER_DETAIL_ID IN (3451,3452);

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN MAX_QUANTITY INT(10);
CREATE INDEX OFFER_DETAIL_DATA_MAX_QUANTITY ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(MAX_QUANTITY)USING BTREE;
