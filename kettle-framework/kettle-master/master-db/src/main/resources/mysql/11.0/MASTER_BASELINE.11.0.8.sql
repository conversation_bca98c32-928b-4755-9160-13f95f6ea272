DROP TABLE IF EXISTS KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA;
CREATE TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA (
  CAMPAIGN_ID int(11) NOT NULL AUTO_INCREMENT,
  PRIMARY_URL varchar(200) DEFAULT NULL,
  CAMPAIGN_SOURCE varchar(100) DEFAULT NULL,
  CAMPAIGN_MEDIUM varchar(100) DEFAULT NULL,
  CAMPAIGN_NAME varchar(100) DEFAULT NULL,
  CAMPAIGN_CATEGORY varchar(100) DEFAULT NULL,
  CAMPAIGN_DESC varchar(300) DEFAULT NULL,
  COUPON_CODE varchar(50) DEFAULT NULL,
  COUPON_CODE_DESC varchar(300) DEFAULT NULL,
  IS_COUPON_CLONE varchar(1) DEFAULT NULL,
  REGION varchar(500) DEFAULT NULL,
  CITY TEXT DEFAULT NULL,
  UNIT_IDS TEXT DEFAULT NULL,
  USAGE_LIMIT int(11) DEFAULT NULL,
  START_DATE timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  VALIDITY int(11) DEFAULT NULL,
  HERO_BANNER_MOBILE varchar(200) DEFAULT NULL,
  HERO_BANNER_DESKTOP varchar(200) DEFAULT NULL,
  LANDING_PAGE_DESC varchar(300) DEFAULT NULL,
  SMS_TEMPLATE varchar(300) DEFAULT NULL,
  SMS_REMINDER varchar(300) DEFAULT NULL,
  WHATSAPP_TEMPLATE varchar(300) DEFAULT NULL,
  WHATSAPP_REMINDER varchar(300) DEFAULT NULL,
  REMINDER_DAY_GAP int(11) DEFAULT NULL,
  UTM_HEADING varchar(100) DEFAULT NULL,
  UTM_DESC varchar(300) DEFAULT NULL,
  UTM_IMAGE_URL varchar(200) DEFAULT NULL,
  REDIRECTION_URL varchar(200) DEFAULT NULL,
  CAMPAIGN_STATUS varchar(20) DEFAULT NULL,
  CAMPAIGN_TOKEN varchar(100) DEFAULT NULL,
  IMAGE1 varchar(300) DEFAULT NULL,
  IMAGE2 varchar(300) DEFAULT NULL,
  IMAGE3 varchar(300) DEFAULT NULL,
  LONG_URL varchar(300) DEFAULT NULL,
  SHORT_URL varchar(100) DEFAULT NULL,
  NEW_CUSTOMER_ONLY varchar(3) DEFAULT NULL,
  PRIMARY KEY (CAMPAIGN_ID)
);

CREATE INDEX CAMPAIGN_DETAIL_DATA_PRIMARY_URL ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(PRIMARY_URL) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_CAMPAIGN_SOURCE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_SOURCE) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_CAMPAIGN_MEDIUM ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_MEDIUM) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_CAMPAIGN_NAME ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_NAME) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_CAMPAIGN_CATEGORY ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_CATEGORY) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_CAMPAING_DESC ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_DESC) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_COUPON_CODE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(COUPON_CODE) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_COUPON_CODE_DESC ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(COUPON_CODE_DESC) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_IS_COUPON_CLONE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(IS_COUPON_CLONE) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_REGION ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(REGION) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_USAGE_LIMIT ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(USAGE_LIMIT) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_START_DATE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(START_DATE) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_VALIDITY ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(VALIDITY) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_HERO_BANNER_MOBILE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(HERO_BANNER_MOBILE) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_HERO_BANNER_DESKTOP ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(HERO_BANNER_DESKTOP) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_LANDING_PAGE_DESC ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(LANDING_PAGE_DESC) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_SMS_TEMPLATE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(SMS_TEMPLATE) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_SMS_REMINDER ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(SMS_REMINDER) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_WHATSAPP_TEMPLATE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(WHATSAPP_TEMPLATE) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_WHATSAPP_REMINDER ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(WHATSAPP_REMINDER) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_REMINDER_DAY_GAP ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(REMINDER_DAY_GAP) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_UTM_HEADING ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(UTM_HEADING) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_UTM_DESC ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(UTM_DESC) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_DATA_UTM_IMAGE_URL ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(UTM_IMAGE_URL) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_REDIRECTION_URL ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(REDIRECTION_URL) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_CAMPAIGN_STATUS ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_STATUS) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_CAMPAIGN_TOKEN ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_TOKEN) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_IMAGE1 ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(IMAGE1) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_IMAGE2 ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(IMAGE2) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_IMAGE3 ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(IMAGE3) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_LONG_URL ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(LONG_URL) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_SHORT_URL ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(SHORT_URL) USING BTREE;
CREATE INDEX CAMPAIGN_DETAIL_NEW_CUSTOMER_ONLY ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(NEW_CUSTOMER_ONLY) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD CAMPAIGN_REACH varchar(20);
CREATE INDEX CAMPAIGN_DETAIL_CAMPAIGN_REACH ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_REACH) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA add column CAMPAIGN_STRATEGY varchar(20);
CREATE INDEX CAMPAIGN_DETAIL_DATA_CAMPAIGN_STRATEGY ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_STRATEGY) USING BTREE;


ALTER TABLE KETTLE_MASTER_DEV.EMPLOYEE_SESSION_DETAILS
ADD COLUMN APPLICATION_NAME VARCHAR(40),
ADD COLUMN MODULE_NAME VARCHAR(40),
ADD COLUMN OS_VERSION VARCHAR(40),
ADD COLUMN DEVICE_MODEL VARCHAR(40);

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL add column PRICING_PROFILE INTEGER;
CREATE INDEX UNIT_DETAIL_PRICING_PROFILE ON KETTLE_MASTER_DEV.UNIT_DETAIL(PRICING_PROFILE) USING BTREE;

CREATE TABLE KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(
    CAMPAIGN_COUPON_MAPPING_ID INT(11) AUTO_INCREMENT  NOT NULL,
    CAMPAIGN_ID INT(11) NOT NULL,
    CUSTOMER_TYPE VARCHAR(40) NOT NULL,
    JOURNEY_NUMBER INT(11) NOT NULL,
    CLONE_CODE VARCHAR(20) NOT NULL,
    CLONE_CODE_DESC VARCHAR(300) NOT NULL,
    VALIDITY_IN_DAYS INT(11) NOT NULL,
    PRIMARY KEY(CAMPAIGN_COUPON_MAPPING_ID),
    FOREIGN KEY (CAMPAIGN_ID) REFERENCES KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAMPAIGN_ID)
);
CREATE INDEX CAMPAIGN_COUPON_MAPPING_CAMPAIGN_COUPON_MAPPING_ID ON KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(CAMPAIGN_COUPON_MAPPING_ID) USING BTREE;
CREATE INDEX CAMPAIGN_COUPON_MAPPING_CAMPAIGN_ID ON KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(CAMPAIGN_ID) USING BTREE;
CREATE INDEX CAMPAIGN_COUPON_MAPPING_CUSTOMER_TYPE ON KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(CUSTOMER_TYPE) USING BTREE;
CREATE INDEX CAMPAIGN_COUPON_MAPPING_VALIDITY_IN_DAYS ON KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(VALIDITY_IN_DAYS) USING BTREE;
CREATE INDEX CAMPAIGN_COUPON_MAPPING_CLONE_CODE_DESC ON KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(CLONE_CODE_DESC) USING BTREE;
CREATE INDEX CAMPAIGN_COUPON_MAPPING_JOURNEY_NUMBER ON KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(JOURNEY_NUMBER) USING BTREE;
CREATE INDEX CAMPAIGN_COUPON_MAPPING_CLONE_CODE ON KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(CLONE_CODE) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD COLUMN COUPON_PREFIX VARCHAR(3);
CREATE INDEX CAMPAIGN_DETAIL_DATA_COUPON_PREFIX ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(COUPON_PREFIX) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING(PARTNER_ID,PERMISSION,ACL_ID,PPM_STATUS)
SELECT B.EXTERNAL_PARTNER_INFO_ID, 1111, A.ACL_ID, "ACTIVE" FROM
KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA A, KETTLE_MASTER_DEV.EXTERNAL_PARTNER_INFO B
WHERE A.ACL_MODULE="offer-service.*" AND B.PARTNER_NAME="neo-redis-client";


ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD END_DATE timestamp NOT NULL;
CREATE INDEX CAMPAIGN_DETAIL_END_DATE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(END_DATE) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN FREQUENCY_APPLICABLE varchar(3);
ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN FREQUENCY_COUNT INTEGER;
CREATE INDEX OFFER_DETAIL_DATA_FREQUENCY_APPLICABLE ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(FREQUENCY_APPLICABLE) USING BTREE;
CREATE INDEX OFFER_DETAIL_DATA_FREQUENCY_COUNT ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(FREQUENCY_COUNT) USING BTREE;

CREATE TABLE KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA (
    DELIVERY_COUPON_ID INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    BRAND_ID INT(11) NOT NULL,
    CHANNEL_PARTNER_ID INT(11) NOT NULL,
    COUPON_STRATEGY VARCHAR(50) NOT NULL,
    MASTER_COUPON VARCHAR(50) NOT NULL,
    COUPON_CODE VARCHAR(100) NOT NULL UNIQUE,
    START_DATE DATETIME NOT NULL,
    END_DATE DATETIME NOT NULL,
    VALIDITY_IN_DAYS INTEGER(11) NOT NULL,
    MAX_NO_OF_DISTRIBUTIONS INTEGER(11) NOT NULL,
    CREATION_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    NO_OF_ALLOCATIONS INTEGER(11) NOT NULL DEFAULT 0,
    IS_EXHAUSTED VARCHAR(3) NOT NULL DEFAULT 'N',
    LAST_ALLOCATION_TIME TIMESTAMP NULL DEFAULT NULL
);

CREATE TABLE KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA(
	ALLOCATION_ID INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	DELIVERY_COUPON_ID INT(11) NOT NULL,
  CUSTOMER_ID INT(11) NOT NULL,
  CONTACT_NUMBER INT(20) NOT NULL,
  CAMPAIGN_ID INT(11) NOT NULL,
  ALLOTMENT_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ALLOTMENT_ORDER_ID INT(11) NOT NULL
);

ALTER TABLE KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA
    CHANGE COLUMN CONTACT_NUMBER CONTACT_NUMBER VARCHAR(10) NOT NULL ;

ALTER TABLE KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA
    ADD COLUMN DELIVERY_COUPON_STATUS VARCHAR(15) NOT NULL DEFAULT "AVAILABLE";

DROP TABLE IF EXISTS KETTLE_MASTER_DEV.REGION_MAP;

CREATE TABLE KETTLE_MASTER_DEV.REGION_MAP (
                                           ID int(11) NOT NULL AUTO_INCREMENT,
                                           UNIT_REGION varchar(40) NOT NULL,
                                           REGION_VALUE varchar(20) NOT NULL,
                                           PRIMARY KEY (ID)
);

CREATE INDEX REGION_MAP_UNIT_REGION ON KETTLE_MASTER_DEV.REGION_MAP(UNIT_REGION) USING BTREE;
CREATE INDEX REGION_MAP_REGION_VALUE ON KETTLE_MASTER_DEV.REGION_MAP(REGION_VALUE) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.REGION_MAP (`UNIT_REGION`,`REGION_VALUE`) VALUES ('BANGALORE','SOUTH');
INSERT INTO KETTLE_MASTER_DEV.REGION_MAP (`UNIT_REGION`,`REGION_VALUE`) VALUES ('CHANDIGARH','NORTH');
INSERT INTO KETTLE_MASTER_DEV.REGION_MAP (`UNIT_REGION`,`REGION_VALUE`) VALUES ('MUMBAI','WEST');
INSERT INTO KETTLE_MASTER_DEV.REGION_MAP (`UNIT_REGION`,`REGION_VALUE`) VALUES ('NCR','NORTH');
INSERT INTO KETTLE_MASTER_DEV.REGION_MAP (`UNIT_REGION`,`REGION_VALUE`) VALUES ('NCR_EDU','NORTH');
INSERT INTO KETTLE_MASTER_DEV.REGION_MAP (`UNIT_REGION`,`REGION_VALUE`) VALUES ('PUNE','WEST');
INSERT INTO KETTLE_MASTER_DEV.REGION_MAP (`UNIT_REGION`,`REGION_VALUE`) VALUES ('CHENNAI','SOUTH');
INSERT INTO KETTLE_MASTER_DEV.REGION_MAP (`UNIT_REGION`,`REGION_VALUE`) VALUES ('HYDERABAD','SOUTH');

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL
    ADD COLUMN COUPON_TYPE VARCHAR(20);

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL
    ADD COLUMN CHANNEL_PARTNER INT(11);

CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_BRAND_ID ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(BRAND_ID) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_CHANNEL_PARTNER_ID ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(CHANNEL_PARTNER_ID) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_COUPON_STRATEGY ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(COUPON_STRATEGY) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_MASTER_COUPON ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(MASTER_COUPON) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_COUPON_CODE ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(COUPON_CODE) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_START_DATE ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(START_DATE) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_END_DATE ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(END_DATE) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_VALIDITY_IN_DAYS ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(VALIDITY_IN_DAYS) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_MAX_NO_OF_DISTRIBUTIONS ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(MAX_NO_OF_DISTRIBUTIONS) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_CREATION_TIME ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(CREATION_TIME) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_NO_OF_ALLOCATIONS ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(NO_OF_ALLOCATIONS) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_IS_EXHAUSTED ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(IS_EXHAUSTED) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_LAST_ALLOCATION_TIME ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(LAST_ALLOCATION_TIME) USING BTREE;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_DELIVERY_COUPON_STATUS ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(DELIVERY_COUPON_STATUS) USING BTREE;

CREATE INDEX DELIVERY_COUPON_ALLOCATION_DETAIL_DATA_DELIVERY_COUPON_ID ON KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA(DELIVERY_COUPON_ID) USING BTREE;
CREATE INDEX DELIVERY_COUPON_ALLOCATION_DETAIL_DATA_CUSTOMER_ID ON KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA(CUSTOMER_ID) USING BTREE;
CREATE INDEX DELIVERY_COUPON_ALLOCATION_DETAIL_DATA_CONTACT_NUMBER ON KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA(CONTACT_NUMBER) USING BTREE;
CREATE INDEX DELIVERY_COUPON_ALLOCATION_DETAIL_DATA_CAMPAIGN_ID ON KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA(CAMPAIGN_ID) USING BTREE;
CREATE INDEX DELIVERY_COUPON_ALLOCATION_DETAIL_DATA_ALLOTMENT_TIME ON KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA(ALLOTMENT_TIME) USING BTREE;
CREATE INDEX DELIVERY_COUPON_ALLOCATION_DETAIL_DATA_ALLOTMENT_ORDER_ID ON KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA(ALLOTMENT_ORDER_ID) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD COLUMN LINKED_CAMPAIGN_ID INT(11);
CREATE INDEX CAMPAIGN_DETAIL_DATA_LINKED_CAMPAIGN_ID ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(LINKED_CAMPAIGN_ID) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING ADD COLUMN REMINDER_DAYS INTEGER;
CREATE INDEX CAMPAIGN_COUPON_MAPPING_REMINDER_DAYS ON KETTLE_MASTER_DEV.CAMPAIGN_COUPON_MAPPING(REMINDER_DAYS) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL
(PRODUCT_ID, PRODUCT_NAME, PRODUCT_DESCRIPTION, PRODUCT_TYPE, PRODUCT_SUB_TYPE,
PRODUCT_STATUS, PRODUCT_START_DATE, PRODUCT_END_DATE, PRODUCT_SKU_CODE, DIMENSION_CODE,
PRICE_TYPE, ADDITIONAL_ITEM_TYPES, IN_TMSTMP, OUT_TMSTMP, IS_INVENTORY_TRACKED,
EMPLOYEE_MEAL_COMPONENT, PRODUCT_CLASSIFICATION, SHORT_CODE, SUPPORTS_VARIANT_LEVEL_ORDERING,
WEB_CATEGORY_TYPE, TAX_CODE, SHORT_NAME, BRAND_ID, PREP_TIME)
VALUES ('2', 'Micro Card', 'Micro Card', '9', '904', 'ACTIVE', '2016-08-22', '2090-10-31','GV1', '1', 'ZERO_TAX', '4',
 '2022-08-22 15:30:00', '2030-12-01 00:00:00', 'N', 'N', 'MENU', 'GV1', 'N', '3632', 'GIFT_CARD', 'GC1', '1', '0.0000');

ALTER TABLE KETTLE_MASTER_DEV.BUSINESS_HOURS
    ADD COLUMN `BRAND_ID` INT NOT NULL DEFAULT 1 AFTER `BUSINESS_HOURS_STATUS`;


INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (`ACL_ID`, `ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('164', 'channel-partner.partner-metadata-management.unit-toggle-knockapp', 'Unit Switch Off on zomato and Swiggy From  KnockApp', 'ACTIVE', 'CHANNEL_PARTNER');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (`PPM_ID`, `PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('76', '21', '1111', '164', 'ACTIVE');

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD COLUMN COUPON_APPLICABLE_AFTER INTEGER;
CREATE INDEX CAMPAIGN_DETAIL_DATA_COUPON_APPLICABLE_AFTER ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(COUPON_APPLICABLE_AFTER) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS) VALUES ('ADMN_OM_DCU', '5', 'SUBMENU', 'SHOW', 'Admin -> Delivery Coupon Upload -> show', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID) VALUES ('Offer Management Delivery Coupon Upload', 'Delivery Coupon Upload access to kettle admin', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME) VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Offer Management Delivery Coupon Upload'), (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_OM_DCU'), 'ACTIVE', '120063', '2022-08-3 00:00:00');


DROP TABLE IF EXISTS KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA;
CREATE TABLE KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (
                                                            `UNIT_OFF_REASON_ID` INT NOT NULL AUTO_INCREMENT,
                                                            `UNIT_REASON` VARCHAR(255) NULL,
                                                            `PRIORITY` VARCHAR(255) NULL,
                                                            `STATUS` VARCHAR(45) NULL,
                                                            `AUTO_SWITCH` VARCHAR(1) NULL DEFAULT NULL,
                                                            PRIMARY KEY (`UNIT_OFF_REASON_ID`));


INSERT INTO KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (`UNIT_OFF_REASON_ID`, `UNIT_REASON`, `PRIORITY`, `STATUS`, `AUTO_SWITCH`) VALUES ('1', 'Electricity Issue', 'High', 'ACTIVE', 'Y');
INSERT INTO KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (`UNIT_OFF_REASON_ID`, `UNIT_REASON`, `PRIORITY`, `STATUS`, `AUTO_SWITCH`) VALUES ('2', 'Environmental Issue', 'High', 'ACTIVE', 'Y');
INSERT INTO KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (`UNIT_OFF_REASON_ID`, `UNIT_REASON`, `PRIORITY`, `STATUS`, `AUTO_SWITCH`) VALUES ('3', 'Machine Downtime', 'High', 'ACTIVE', 'Y');
INSERT INTO KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (`UNIT_OFF_REASON_ID`, `UNIT_REASON`, `PRIORITY`, `STATUS`, `AUTO_SWITCH`) VALUES ('4', 'Maintenance', 'High', 'ACTIVE', 'Y');
INSERT INTO KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (`UNIT_OFF_REASON_ID`, `UNIT_REASON`, `PRIORITY`, `STATUS`, `AUTO_SWITCH`) VALUES ('5', 'Network Down', 'High', 'ACTIVE', 'Y');
INSERT INTO KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (`UNIT_OFF_REASON_ID`, `UNIT_REASON`, `PRIORITY`, `STATUS`, `AUTO_SWITCH`) VALUES ('6', 'Ops Downtime', 'High', 'ACTIVE', 'N');
INSERT INTO KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (`UNIT_OFF_REASON_ID`, `UNIT_REASON`, `PRIORITY`, `STATUS`, `AUTO_SWITCH`) VALUES ('7', 'Others', 'High', 'ACTIVE', 'Y');
INSERT INTO KETTLE_MASTER_DEV.UNIT_OFF_REASON_METADATA (`UNIT_OFF_REASON_ID`, `UNIT_REASON`, `PRIORITY`, `STATUS`, `AUTO_SWITCH`) VALUES ('8', 'Partner Issue', 'High', 'ACTIVE', 'N');

ALTER TABLE KETTLE_DEV.LOYALTY_SCORE
ADD COLUMN SIGNUP_OFFER_STATUS VARCHAR(15),
ADD COLUMN REDEMPTION_ORDER_ID INT(11);

CREATE INDEX LOYALTY_SCORE_SIGNUP_OFFER_STATUS ON KETTLE_DEV.LOYALTY_SCORE(SIGNUP_OFFER_STATUS) using BTREE;
CREATE INDEX LOYALTY_SCORE_REDEMPTION_ORDER_ID ON KETTLE_DEV.LOYALTY_SCORE(REDEMPTION_ORDER_ID) using BTREE;

ALTER TABLE KETTLE_DEV.ORDER_INVOICE_DETAIL ADD COLUMN STATE_CODE varchar(50) NOT NULL DEFAULT '0';
ALTER TABLE KETTLE_DEV.ORDER_INVOICE_DETAIL ADD COLUMN TAX_TYPE varchar(50) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD COLUMN BRAND_ID INTEGER;
CREATE INDEX CAMPAIGN_DETAIL_DATA_BRAND_ID ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(BRAND_ID) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.PAYMENT_MODE MODIFY COLUMN GENERATE_PULL TINYINT(4);

ALTER TABLE KETTLE_MASTER_DEV.MENU_RECOMMENDATION_MAPPING_DATA ADD COLUMN RECOMMENDATION_TITLE VARCHAR(30) NULL DEFAULT "Best Paired With";

INSERT INTO KETTLE_MASTER_DEV.BRAND_ATTRIBUTE_DETAIL (BRAND_ID, ATTRIBUTE_KEY, ATTRIBUTE_VALUE, ATTRIBUTE_TYPE)
VALUES ('1', 'GOOGLE_ADS_CUSTOMER_ID', '3754301113', 'java.lang.String');
INSERT INTO KETTLE_MASTER_DEV.BRAND_ATTRIBUTE_DETAIL (BRAND_ID, ATTRIBUTE_KEY, ATTRIBUTE_VALUE, ATTRIBUTE_TYPE)
VALUES ('3', 'GOOGLE_ADS_CUSTOMER_ID', '7083875933', 'java.lang.String');