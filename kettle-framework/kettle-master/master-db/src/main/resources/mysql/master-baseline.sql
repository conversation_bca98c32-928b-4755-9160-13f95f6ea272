CREATE TABLE ACCESS_CONTROL_LIST_DATA (
  ACL_ID                 INT PRIMARY KEY AUTO_INCREMENT,
  ACL_MODULE             VARCHAR(250) NOT NULL,
  ACL_MODULE_DESCRIPTION VARCHAR(255),
  ACL_STATUS             VARCHAR(20)
);

CREATE TABLE EMPLOYEE_PERMISSION_MAPPING (
  EPM_ID      INT PRIMARY KEY AUTO_INCREMENT,
  <PERSON><PERSON><PERSON>OYEE_ID INT NOT NULL,
  ACL_ID      INT NOT NULL,
  PERMISSION  INT NOT NULL,
  EPM_STATUS  VARCHAR(20),
  FOREIGN KEY (EMPLOYEE_ID) REFERENCES EMPLOYEE_DETAIL (EMP_ID),
  FOREIGN KEY (ACL_ID) REFERENCES ACCESS_CONTROL_LIST_DATA (ACL_ID)
);

INSERT INTO ACCESS_CONTROL_LIST_DATA (<PERSON>L_<PERSON>ODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS)
VALUES ('kettle-admin.*', '', 'ACTIVE'), ('master-service.*', '', 'ACTIVE');

INSERT INTO EMPLOYEE_PERMISSION_MAPPING (EMPLOYEE_ID, ACL_ID, PERMISSION, EPM_STATUS)
VALUES (100000, 1, 1111, 'ACTIVE'), (100000, 2, 1111, 'ACTIVE');