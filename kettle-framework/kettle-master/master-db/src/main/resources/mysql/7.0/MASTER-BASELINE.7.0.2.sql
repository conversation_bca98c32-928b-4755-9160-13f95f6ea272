DROP TABLE `KETTLE_MASTER_DEV`.`XML_REPORT_DEFINITION`;
CREATE TABLE `KETTLE_MASTER_DEV`.`XML_REPORT_DEFINITION` (
  `REPORT_ID` INTEGER NOT NULL AUTO_INCREMENT,
  `REPORT_NAME` VARCHAR(250) NULL DEFAULT NULL,
  `XML_FILE_PATH` VARCHAR(1000) NULL,
  `REPORT_STATUS` VARCHAR(100) NULL,
  `VERSION` INTEGER NULL,
  `REPORT_TYPE` VARCHAR(45) NULL,
  `EXECUTION_ENVIRONMENT` VARCHAR(45) NULL,
  `DEPARTMENT_ID` INTEGER NULL,
  `DEPARTMENT_NAME` VARCHAR(100) NULL,
  `LAST_UPDATED` TIMESTAMP NULL,
PRIMARY KEY (`REPORT_ID`),
UNIQUE KEY `XML_REPORT_DEFINITION_DUPLICATE_REPORT`(`EXECUTION_ENVIRONMENT` , `REPORT_TYPE`, `REPORT_NAME`)
  );

DROP TABLE KETTLE_MASTER_DEV.`XML_REPORT_VERSION`;
CREATE TABLE KETTLE_MASTER_DEV.`XML_REPORT_VERSION` (
  `VERSION_ID` INTEGER NOT NULL AUTO_INCREMENT,
  `REPORT_ID` INTEGER DEFAULT NULL,
  `VERSION_NO` INTEGER DEFAULT NULL,
  `S3_LINK` VARCHAR(1000) DEFAULT NULL,
  `FILE_NAME` varchar(200) DEFAULT NULL,
  `MIME_TYPE` varchar(45) DEFAULT NULL,
  `S3_BUCKET` varchar(100) DEFAULT NULL,
  `S3_FILE_KEY` varchar(500) DEFAULT NULL,
  `DEFAULT_VERSION` VARCHAR(1) DEFAULT NULL,
  `COMMENTS` VARCHAR(1000) DEFAULT NULL,
  `LAST_UPDATED` TIMESTAMP NULL,
  PRIMARY KEY (`VERSION_ID`),
  CONSTRAINT `XML_REPORT_VERSION_REPORT_ID_DEF` FOREIGN KEY (`REPORT_ID`) REFERENCES `KETTLE_MASTER_DEV`.`XML_REPORT_DEFINITION` (`REPORT_ID`)); 

