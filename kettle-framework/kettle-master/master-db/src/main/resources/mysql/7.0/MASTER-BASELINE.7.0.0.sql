
CREATE TABLE KETTLE_MASTER_DEV.`CHANNEL_PARTNER` (
   `PARTNER_ID` int(11) NOT NULL AUTO_INCREMENT,
   `PARTNER_CODE` varchar(50) NOT NULL,
   `PARTNER_DISPLAY_NAME` varchar(100) NOT NULL,
   `SERVICE_TYPE` varchar(15) NOT NULL DEFAULT 'THIRD_PARTY',
   `CREDIT_ACCOUNT_ID` int(11) DEFAULT NULL,
   `API_INTEGRATED` varchar(1) NOT NULL DEFAULT 'N',
   PRIMARY KEY (`PARTNER_ID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=latin1;

 INSERT INTO KETTLE_MASTER_DEV.CHANNEL_PARTNER SELECT * FROM KETTLE_DEV.CHANNEL_PARTNER;

CREATE TABLE KETTLE_MASTER_DEV.`CHANNEL_PARTNER_COMMISSION` (
   `PARTNER_COMMISSION_ID` int(11) NOT NULL AUTO_INCREMENT,
   `PARTNER_ID` int(11) NOT NULL,
   `COMMISSION_RATE` decimal(10,2) NOT NULL,
   `TAX_RATE` decimal(10,2) NOT NULL,
   `START_DATE` date NOT NULL,
   `END_DATE` date NOT NULL,
   PRIMARY KEY (`PARTNER_COMMISSION_ID`),
   KEY `CHANNEL_PARTNER_COMMISSION_PARTNER_ID` (`PARTNER_ID`) USING BTREE,
   KEY `CHANNEL_PARTNER_COMMISSION_START_DATE` (`START_DATE`) USING BTREE,
   KEY `CHANNEL_PARTNER_COMMISSION_END_DATE` (`END_DATE`) USING BTREE
 ) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=latin1;

 INSERT INTO KETTLE_MASTER_DEV.`CHANNEL_PARTNER_COMMISSION` SELECT * FROM KETTLE_DEV.`CHANNEL_PARTNER_COMMISSION`;

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_CHANNEL_PARTNER_MAPPING` (
   `ID` int(11) NOT NULL AUTO_INCREMENT,
   `UNIT_ID` int(11) NOT NULL,
   `CHANNEL_PARTNER_ID` int(11) NOT NULL,
   `STATUS` varchar(10) NOT NULL DEFAULT 'IN_ACTIVE',
   `DELIVERY_PARTNER_ID` int(11) NOT NULL DEFAULT '5',
   PRIMARY KEY (`ID`)
 ) ENGINE=InnoDB AUTO_INCREMENT=150 DEFAULT CHARSET=latin1;


ALTER TABLE KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING ADD COLUMN PRICING_STATUS VARCHAR(12) NOT NULL;
UPDATE KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING SET PRICING_STATUS = 'ACTIVE';

CREATE TABLE KETTLE_MASTER_DEV.BRAND_DETAIL (
  BRAND_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  BRAND_NAME VARCHAR(50) NOT NULL,
  BRAND_CODE VARCHAR(10) NOT NULL,
  BRAND_TAGLINE VARCHAR(300) NOT NULL,
  BRAND_DOMAIN VARCHAR(30) NOT NULL,
  BRAND_BILL_TAGLINE VARCHAR(500) NOT NULL,
  BRAND_WEBSITE_LINK VARCHAR(200) NOT NULL,
  BRAND_STATUS VARCHAR(15) NOT NULL,
  BRAND_SUPPORT_CONTACT VARCHAR(20) NOT NULL,
  BRAND_SUPPORT_EMAIL VARCHAR(50) NOT NULL,
  BRAND_VERBIAGE VARCHAR(500) NOT NULL
);


CREATE TABLE KETTLE_MASTER_DEV.BRAND_ATTRIBUTE_DETAIL (
  ATTRIBUTE_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  BRAND_ID INTEGER NOT NULL,
  ATTRIBUTE_KEY VARCHAR(50) NOT NULL,
  ATTRIBUTE_VALUE VARCHAR(500) NOT NULL,
  ATTRIBUTE_TYPE VARCHAR(50) NOT NULL
);

INSERT INTO `KETTLE_MASTER_DEV`.`BRAND_DETAIL` (`BRAND_NAME`, `BRAND_CODE`, `BRAND_TAG_LINE`, `BRAND_DOMAIN`, `BRAND_BILL_TAGLINE`, `BRAND_WEBSITE_LINK`, `BRAND_STATUS`, `BRAND_SUPPORT_CONTACT`, `BRAND_SUPPORT_EMAIL`, `BRAND_VERBIAGE`) VALUES ('Chaayos', 'CH', 'Experiments with Chai', 'www.chaayos.com', 'FOr a blazing fast delivery. order from Chaayos.com', 'https://cafes.chaayos.com', 'ACTIVE', '18001202424', '<EMAIL>', 'Test');

INSERT INTO `KETTLE_MASTER_DEV`.`BRAND_ATTRIBUTE_DETAIL` (`BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('1', 'SEND_EMAIL', 'Y', 'java.lang.String');
INSERT INTO `KETTLE_MASTER_DEV`.`BRAND_ATTRIBUTE_DETAIL` (`BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('1', 'SEND_WELCOME_MESSAGE', 'Y', 'java.lang.String');
INSERT INTO `KETTLE_MASTER_DEV`.`BRAND_ATTRIBUTE_DETAIL` (`BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('1', 'AWARD_LOYALTY', 'Y', 'java.lang.String');



CREATE TABLE KETTLE_MASTER_DEV.CHANNEL_PARTNER(
PARTNER_ID INT NOT NULL AUTO_INCREMENT,
PARTNER_CODE VARCHAR(50) NOT NULL,
PARTNER_DISPLAY_NAME VARCHAR(100) NOT NULL,
PRIMARY KEY(PARTNER_ID)
);
ALTER TABLE KETTLE_MASTER_DEV.CHANNEL_PARTNER ADD COLUMN SERVICE_TYPE VARCHAR(15) NOT NULL DEFAULT 'THIRD_PARTY';
ALTER TABLE KETTLE_MASTER_DEV.CHANNEL_PARTNER ADD COLUMN CREDIT_ACCOUNT_ID INT NULL;
ALTER TABLE KETTLE_MASTER_DEV.CHANNEL_PARTNER ADD COLUMN API_INTEGRATED VARCHAR(1) DEFAULT 'N' NOT NULL;

INSERT INTO KETTLE_MASTER_DEV.CHANNEL_PARTNER SELECT * FROM KETTLE_DEV.CHANNEL_PARTNER;




CREATE TABLE KETTLE_MASTER_DEV.CHANNEL_PARTNER_COMMISSION(
PARTNER_COMMISSION_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
PARTNER_ID INTEGER NOT NULL,
COMMISSION_RATE DECIMAL(10,2) NOT NULL,
TAX_RATE DECIMAL(10,2) NOT NULL,
START_DATE DATE NOT NULL,
END_DATE DATE NOT NULL
);

CREATE INDEX CHANNEL_PARTNER_COMMISSION_PARTNER_ID ON KETTLE_MASTER_DEV.CHANNEL_PARTNER_COMMISSION(PARTNER_ID) USING BTREE;
CREATE INDEX CHANNEL_PARTNER_COMMISSION_START_DATE ON KETTLE_MASTER_DEV.CHANNEL_PARTNER_COMMISSION(START_DATE) USING BTREE;
CREATE INDEX CHANNEL_PARTNER_COMMISSION_END_DATE ON KETTLE_MASTER_DEV.CHANNEL_PARTNER_COMMISSION(END_DATE) USING BTREE;
INSERT INTO KETTLE_MASTER_DEV.CHANNEL_PARTNER_COMMISSION SELECT * FROM KETTLE_DEV.CHANNEL_PARTNER_COMMISSION;


CREATE TABLE KETTLE_MASTER_DEV.`UNIT_CHANNEL_PARTNER_MAPPING` (
   `ID` int(11) NOT NULL AUTO_INCREMENT,
   `UNIT_ID` int(11) NOT NULL,
   `CHANNEL_PARTNER_ID` int(11) NOT NULL,
   `STATUS` varchar(10) NOT NULL DEFAULT 'IN_ACTIVE',
   `DELIVERY_PARTNER_ID` int(11) NOT NULL DEFAULT '5',
   PRIMARY KEY (`ID`)
 ) ENGINE=InnoDB DEFAULT CHARSET=latin1;
INSERT INTO KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MAPPING SELECT * FROM KETTLE_DEV.UNIT_CHANNEL_PARTNER_MAPPING;



CREATE TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING (
	ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    UNIT_CHANNEL_PARTNER_ID INTEGER NOT NULL,
    MENU_SEQUENCE_ID INTEGER NOT NULL,
    MENU_START_TIME VARCHAR(20) NULL,
    MENU_END_TIME VARCHAR(20) NULL,
    MENU_DAY INTEGER NULL,
    MENU_TYPE VARCHAR(20) NOT NULL,
    MENU_APP VARCHAR(20) NOT NULL
);

ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN STATUS VARCHAR(20) NOT NULL;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN CREATED_AT TIMESTAMP NULL;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN UPDATED_AT TIMESTAMP NULL;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN CREATED_BY INTEGER NOT NULL;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN UPDATED_BY INTEGER NOT NULL;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN MENU_SEQUENCE_NAME VARCHAR(250) NULL;

INSERT INTO KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING SELECT * FROM KETTLE_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING;

CREATE TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING(
	UNIT_PARTNER_BRAND_MAPPING_ID INT  AUTO_INCREMENT,
	RESTAURANT_ID VARCHAR(50) NOT NULL,
  UNIT_ID INT NOT NULL,
	PARTNER_ID VARCHAR(50) NOT NULL,
	PRICE_PROFILE_UNIT_ID INT NOT NULL,
	PARENT_PARTNER_ID INT NOT NULL,
	PRIMARY KEY (UNIT_PARTNER_BRAND_MAPPING_ID)
);

CREATE TABLE KETTLE_MASTER_DEV.ENTITY_ALIAS_MAPPING(
	ENTITY_ALIAS_MAPPING_ID INT  AUTO_INCREMENT,
    ENTITY_ID INT NOT NULL,
	ENTITY_TYPE VARCHAR(50) NOT NULL,
	PARTNER_ID VARCHAR(50) NOT NULL,
	ALIAS VARCHAR(255) NOT NULL,
	PRIMARY KEY (ENTITY_ALIAS_MAPPING_ID)
);

ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN ACQUISITION_BRAND_ID INT NOT NULL;

ALTER TABLE KETTLE_DEV.CUSTOMER_INFO ADD COLUMN IS_CHAAYOS_CUSTOMER VARCHAR(1) NOT NULL;

UPDATE  KETTLE_DEV.CUSTOMER_INFO SET ACQUISITION_BRAND_ID = 1;

UPDATE  KETTLE_DEV.CUSTOMER_INFO SET IS_CHAAYOS_CUSTOMER = "Y";

ALTER TABLE KETTLE_DEV.ORDER_DETAIL ADD COLUMN BRAND_ID INT NOT NULL, ALGORITHM=INPLACE, LOCK=NONE;

UPDATE KETTLE_DEV.ORDER_DETAIL SET BRAND_ID = 1;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING CHANGE COLUMN PARTNER_ID PARTNER_ID INT NOT NULL;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING CHANGE COLUMN PARENT_PARTNER_ID BRAND_ID INTEGER NOT NULL;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING ADD COLUMN PARTNER_SOURCE_SYSTEM_ID  INT(11);

ALTER TABLE KETTLE_MASTER_DEV.ENTITY_ALIAS_MAPPING CHANGE COLUMN PARTNER_ID BRAND_ID INT NOT NULL ;

ALTER TABLE `KETTLE_MASTER_DEV`.`ENTITY_ALIAS_MAPPING`
CHANGE COLUMN `ALIAS` `ALIAS` VARCHAR(255) NULL COMMENT '' ;


ALTER TABLE KETTLE_MASTER_DEV.ENTITY_ALIAS_MAPPING ADD COLUMN IMAGE_URL VARCHAR(255);


ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN BRAND_ID INTEGER NOT NULL;
UPDATE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING SET BRAND_ID = 1;

ALTER TABLE KETTLE_MASTER_DEV.ENTITY_ALIAS_MAPPING ADD COLUMN DESCRIPTION VARCHAR(255);


ALTER TABLE KETTLE_DEV.ORDER_FEEDBACK_EVENT ADD COLUMN BRAND_ID INT NOT NULL;
UPDATE KETTLE_DEV.ORDER_FEEDBACK_EVENT SET BRAND_ID = 1;


INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 2, 11001, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'NCR' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 2, 12027, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'MUMBAI' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 2, 12024, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'CHANDIGARH' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 2, 26102, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'BANGALORE' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 2, 26057, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'NCR_EDU' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';

INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 3, 26031, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'NCR' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 3, 26032, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'MUMBAI' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 3, 26047, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'CHANDIGARH' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 3, 26118, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'BANGALORE' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 3, 26101, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'NCR_EDU' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';

INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 6, 26031, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'NCR' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 6, 26032, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'MUMBAI' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 6, 26047, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'CHANDIGARH' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 6, 26118, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'BANGALORE' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING (RESTAURANT_ID, UNIT_ID, PARTNER_ID,PRICE_PROFILE_UNIT_ID, BRAND_ID)
SELECT UNIT_ID, UNIT_ID, 6, 26101, 1 FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE' AND UNIT_REGION = 'NCR_EDU' AND UNIT_STATUS = 'ACTIVE' AND UNIT_NAME NOT LIKE '%Odc%';


ALTER TABLE KETTLE_MASTER_UAT.PRODUCT_GROUP_DATA ADD COLUMN GROUP_TYPE VARCHAR(200) NOT NULL;
UPDATE KETTLE_MASTER_UAT.PRODUCT_GROUP_DATA SET GROUP_TYPE = GROUP_TAG;
UPDATE KETTLE_MASTER_UAT.PRODUCT_GROUP_DATA SET GROUP_TAG = 'Chaayos';

ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING ADD COLUMN MAPPING_STATUS VARCHAR(50) NOT NULL;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET MAPPING_STATUS = 'ACTIVE';

ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING CHANGE COLUMN RESTAURANT_ID RESTAURANT_ID VARCHAR(255) NOT NULL;


INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('2', '1', 'SEND_WELCOME_MESSAGE', 'Y', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('4', '1', 'SEND_EMAIL', 'Y', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('6', '1', 'AWARD_LOYALTY', 'Y', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('7', '1', 'SEND_NPS', 'Y', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('8', '1', 'FEEDBACK_URL', 'https://stpltd.typeform.com/to/', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('9', '1', 'FEEDBACK_ENDPOINT_NPS_DELIVERY_ONLY_ORDER', 'pQrvPP', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('10', '1', 'SEND_FEEDBACK_MESSAGE_DELIVERY_SWIGGY', 'Y', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('11', '1', 'FEEDBACK_ENDPOINT_DINEIN', 'mmE184?', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('12', '1', 'FEEDBACK_ENDPOINT_DELIVERY', 'zdyg68?', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('13', '1', 'FEEDBACK_ENDPOINT_LOW_RATING_DINEIN', 'ibLp8L?', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('14', '1', 'FEEDBACK_ENDPOINT_LOW_RATING_DELIVERY', 'Wm2NKz?', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('15', '1', 'FEEDBACK_ENDPOINT_REDIRECT_URL', 'whoIvV?', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('16', '1', 'FEEDBACK_ENDPOINT_NPS_CAFE', 'BL9oYs', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('17', '1', 'FEEDBACK_ENDPOINT_NPS_DELIVERY', 'oJgh2w', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('18', '1', 'VERIFICATION_EMAIL_TEMPLATE', 'template/VerificationTemplate.html', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('19', '2', 'SEND_WELCOME_MESSAGE', 'N', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('20', '2', 'SEND_EMAIL', 'N', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('21', '2', 'AWARD_LOYALTY', 'N', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('22', '2', 'SEND_NPS', 'Y', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('23', '2', 'FEEDBACK_URL', 'https://stpltd.typeform.com/to/', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('25', '2', 'SEND_FEEDBACK_MESSAGE_DELIVERY_SWIGGY', 'Y', 'java.lang.String');
INSERT INTO `kettle_master_dev`.`brand_attribute_detail` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('32', '2', 'FEEDBACK_ENDPOINT_NPS_DELIVERY', 'LOFeMw', 'java.lang.String');

UPDATE `KETTLE_MASTER_DEV`.`BRAND_DETAIL` SET `BRAND_TAGLINE`='All Day Food Jugaad ', `BRAND_DOMAIN`='Zomato / Swiggy' WHERE `BRAND_ID`='2';

INSERT INTO `KETTLE_MASTER_DEV`.`BRAND_ATTRIBUTE_DETAIL` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('33', '2', 'SMS_ID', 'GODESI', 'java.lang.String');
INSERT INTO `KETTLE_MASTER_DEV`.`BRAND_ATTRIBUTE_DETAIL` (`ATTRIBUTE_ID`, `BRAND_ID`, `ATTRIBUTE_KEY`, `ATTRIBUTE_VALUE`, `ATTRIBUTE_TYPE`) VALUES ('34', '1', 'SMS_ID', 'CHAYOS', 'java.lang.String');
