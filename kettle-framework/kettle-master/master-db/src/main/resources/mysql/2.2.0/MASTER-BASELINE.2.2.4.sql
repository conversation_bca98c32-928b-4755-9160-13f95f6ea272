ALTER TABLE KETTLE_MASTER_DEV.PAYMENT_MODE 
ADD COLUMN MODE_CATEGORY VARCHAR(10) NULL;

CREATE TABLE KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(
UNIT_PAYMENT_MODE_MAPPING_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
PAYMENT_MODE_ID INTEGER NOT NULL,
MAPPING_STATUS VARCHAR(15) NOT NULL,
UNIQUE INDEX UNIT_ID_PAYMENT_MODE_ID(UNIT_ID,PAYMENT_MODE_ID)
);

INSERT INTO `KETTLE_MASTER_DEV`.`PAYMENT_MODE` (`PAYMENT_MODE_ID`, `MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`, `GENERATE_PULL`, `COMMISSION_RATE`, `MODE_STATUS`, `M<PERSON>E_CATEGORY`) 
VALUES ('12', '<PERSON><PERSON>Pay', 'CARD', '<PERSON><PERSON> Pay', '<PERSON><PERSON>T', '1', '1.65', 'ACTIVE', 'ONLINE');

UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='1';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='2';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='3';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='4';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='5';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='6';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='7';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='8';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='9';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='10';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='OFFLINE' WHERE `PAYMENT_MODE_ID`='11';
UPDATE `KETTLE_MASTER_DEV`.`PAYMENT_MODE` SET `MODE_CATEGORY`='ONLINE' WHERE `PAYMENT_MODE_ID`='12';
INSERT INTO `KETTLE_MASTER_DEV`.`PAYMENT_MODE` (`PAYMENT_MODE_ID`, `MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`, `GENERATE_PULL`, `COMMISSION_RATE`, `MODE_STATUS`, `MODE_CATEGORY`) VALUES ('13', 'PayTmOnline', 'CARD', 'PayTm Online', 'DEBIT', '1', '1.65', 'ACTIVE', 'ONLINE');

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 1, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN( 'CAFE','COD','DELIVERY','TAKE_AWAY','CHAI_MONK');
INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 2, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN( 'CAFE','COD','DELIVERY','TAKE_AWAY');

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 3, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN( 'CAFE','COD','DELIVERY','TAKE_AWAY');

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 4, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN('TAKE_AWAY') AND UNIT_NAME LIKE '%ODC%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 4, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_ID IN (10001,10002, 10003,12030);

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 5, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN('TAKE_AWAY') AND UNIT_NAME LIKE '%ODC%';
INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 5, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_ID IN (10001,10002, 10003,12030);

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 6, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN( 'COD','DELIVERY');
INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID,6, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_ID IN (10014);

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 7, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN( 'CAFE', 'TAKE_AWAY');

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 8, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN( 'CAFE', 'TAKE_AWAY');

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 10, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN( 'CAFE', 'TAKE_AWAY');

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID ,PAYMENT_MODE_ID, MAPPING_STATUS)
select UNIT_ID, 11, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_STATUS = 'ACTIVE' and 
UNIT_CATEGORY IN( 'CAFE', 'TAKE_AWAY','COD','DELIVERY');