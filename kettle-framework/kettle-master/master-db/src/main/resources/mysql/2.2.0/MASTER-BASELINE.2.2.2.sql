CREATE TABLE KETTLE_MASTER_DEV.RIDER_UNIT_MAPPING(
RIDER_UNIT_MAPPING_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
EMPLOYEE_ID INTEGER NOT NULL,
UNIT_ID INTEGER NOT NULL,
MAPPING_STATUS VARCHAR(20) NOT NULL,
LAST_UPDATE_TIME TIMESTAMP NOT NULL
);

ALTER TABLE KETTLE_MASTER_DEV.KIOSK_COMPANY_DETAILS ADD COLUMN KIOSK_SUB_DOMAIN VARCHAR(45) NOT NULL;

INSERT INTO KETTLE_MASTER_DEV.PAYMENT_MODE (MODE_NAME, MODE_TYPE, MODE_DESCRIPTION, SETTLEMENT_TYPE, GENERATE_PULL, COMMISSION_RATE)
VALUES ('Paytm', 'CARD', 'Paytm', 'CREDIT', '1', '1.65');

ALTER TABLE KETTLE_MASTER_DEV.PAYMENT_MODE
ADD COLUMN MODE_STATUS VARCHAR(10) DEFAULT 'ACTIVE';

UPDATE KETTLE_MASTER_DEV.PAYMENT_MODE SET MODE_STATUS='IN_ACTIVE' WHERE PAYMENT_MODE_ID='9';
