
INSERT INTO `KETTLE_MASTER_DEV`.`EXTERNAL_PARTNER_INFO` (`EXTERNAL_PARTNER_INFO_ID`, `PARTNER_NAME`, `PARTNER_CODE`, `PASS_CODE`, `API_KEY`, `CREATION_DATE`, `PARTNER_STATUS`)
VALUES ('2', 'neo-redis-client', 'neo-redis-client', 'E7N69', 'eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw', '2016-09-20', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`ACCESS_CONTROL_LIST_DATA` (`ACL_ID`, `ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) 
VALUES ('119', 'kettle-crm.*', 'CRM All Module Privilge', 'ACTIVE', 'KETTLE_CRM');

INSERT INTO `KETTLE_MASTER`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) 
VALUES ( '2', '1111', '105', 'ACTIVE');

INSERT INTO `KETTLE_MASTER`.`PARTNER_PERMISSION_MAPPING` ( `PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) 
VALUES ( '2', '1111', '119', 'ACTIVE');


ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_DETAIL ADD COLUMN WEB_CATEGORY_TYPE INTEGER;
// NEED TO FIX THIS
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET WEB_CATEGORY_TYPE = PRODUCT_SUB_TYPE