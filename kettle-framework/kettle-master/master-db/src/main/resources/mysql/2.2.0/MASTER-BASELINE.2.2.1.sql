ALTER TABLE KETTLE_MASTER_DEV.<PERSON><PERSON><PERSON>OYEE_DETAIL
ADD COLUMN COMMUNICATION_CHANNEL VARCHAR(50) UNIQUE;

CREATE TABLE KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING(
PPM_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
PARTNER_ID INT NOT NULL,
PERMISSION INT NOT NULL,
ACL_ID INT NOT NULL,
PPM_STATUS VARCHAR(30) NOT NULL,
FOREIGN KEY (PARTNER_ID) REFERENCES EXTERNAL_PARTNER_INFO(EXTERNAL_PARTNER_INFO_ID),
FOREIGN KEY (ACL_ID) REFERENCES ACCESS_CONTROL_LIST_DATA(ACL_ID)
);

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTRO_LIST_DATA (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('master-service.external.redis-cache.*', 'resources for redis cache', 'ACTIVE', 'MASTER_SERVICE');
INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTRO_LIST_DATA (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('kettle-service.external.web-order.*', 'web order create', 'ACTIVE', 'KETTLE_SERVICE');
