UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Fluffy Omelette topped with our secret spice inside a Bun' WHERE PRODUCT_ID = 992;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Co<PERSON>, Spinach & Cheese in a lattice pie crust' WHERE PRODUCT_ID = 993;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Perfect Chai companion, crisp and light' WHERE PRODUCT_ID = 1003;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Customise & discover 12000 ways to make your \'Meri Wali Chai\'' WHERE PRODUCT_ID = 11;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Choti Masala Chai' WHERE PRODUCT_ID = 30;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Customise & discover 12000 ways to make your \'Meri Wali Chai\'' WHERE PRODUCT_ID = 70;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Healthy and creamy with fresh Mango pulp' WHERE PRODUCT_ID = 400;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Unique blend of supreme quality classic Desi Chai' WHERE PRODUCT_ID = 700;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Unique blend of supreme quality Desi Chai with Tulsi & Ginger flakes' WHERE PRODUCT_ID = 710;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Our special blend of classic Green Tea' WHERE PRODUCT_ID = 720;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Our unique blend of Spices to create your perfect Masala Chai' WHERE PRODUCT_ID = 730;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Additional Kulhad' WHERE PRODUCT_ID = 861;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Extra Coffee Powder' WHERE PRODUCT_ID = 862;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Extra Chocolate Syrup' WHERE PRODUCT_ID = 863;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Extra RoofAfza Syrup' WHERE PRODUCT_ID = 864;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chicken, Jalapeno & Cheese in a Lattice pie crust' WHERE PRODUCT_ID = 994;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Black Lemon Chai' WHERE PRODUCT_ID = 20;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Coffee the way you like it' WHERE PRODUCT_ID = 271;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = '1 Big Kettle + Bun Bhujiya+ Mom\'s Poha + Vada Pav + Napoli Sandwich' WHERE PRODUCT_ID = 999;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = '1 Big Kettle + Keema Pav + Blueberry Cake + Sicilian Chicken Sandwich + Egg Bun' WHERE PRODUCT_ID = 1000;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = '1 Desi Chai Kettle + any 2 items' WHERE PRODUCT_ID = 996;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = '1 Desi Chai Kettle + Any 2 Nashta items veg' WHERE PRODUCT_ID = 995;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = '1 Small Kettle + Napoli Sandwich + Vada Pav/Mom\'s Poha' WHERE PRODUCT_ID = 997;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = '1 Small Kettle + Sicilian Chicken Sandwich + Keema Pav/Egg Bun' WHERE PRODUCT_ID = 998;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'A Bombay Favorite-Spicy Potato Vada With Hari Chutney And Lasun Chutney In A Homemade Pav' WHERE PRODUCT_ID = 671;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Adrak' WHERE PRODUCT_ID = 969;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Adrak Chai' WHERE PRODUCT_ID = 1018;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Adrak Chai No Sugar' WHERE PRODUCT_ID = 1019;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Ajwain' WHERE PRODUCT_ID = 970;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'An all time favourite' WHERE PRODUCT_ID = 1028;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chaayos Gift Cards' WHERE PRODUCT_ID = 1026;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chaayos Gift Cards' WHERE PRODUCT_ID = 1027;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chaayos HKV Event' WHERE PRODUCT_ID = 1049;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chaayos Special assorted Pakora basket' WHERE PRODUCT_ID = 991;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chaayos\' Signature Cake. Your favourite flavour' WHERE PRODUCT_ID = 1038;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chai that makes you pop' WHERE PRODUCT_ID = 1035;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chocolate lover\'s delight' WHERE PRODUCT_ID = 420;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chopped boiled Eggs with basil & Corn in a sesame bun' WHERE PRODUCT_ID = 650;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Christmas Special Cake' WHERE PRODUCT_ID = 1051;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Cinnamon' WHERE PRODUCT_ID = 971;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Breakfast' WHERE PRODUCT_ID = 1010;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Breakfast' WHERE PRODUCT_ID = 1011;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Breakfast' WHERE PRODUCT_ID = 1016;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Breakfast' WHERE PRODUCT_ID = 1017;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Lunch' WHERE PRODUCT_ID = 1008;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Lunch' WHERE PRODUCT_ID = 1009;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Lunch' WHERE PRODUCT_ID = 1012;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Lunch' WHERE PRODUCT_ID = 1013;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Lunch' WHERE PRODUCT_ID = 1014;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Combo for Lunch' WHERE PRODUCT_ID = 1015;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Customise & discover 12000 ways to make your \'Meri Wali Chai\'' WHERE PRODUCT_ID = 10;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Delhis Favorite Dish!' WHERE PRODUCT_ID = 570;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Delivery Charges' WHERE PRODUCT_ID = 1044;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Dense, dark and devilish' WHERE PRODUCT_ID = 1045;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Desi Chai Kadak' WHERE PRODUCT_ID = 13;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Diwali Gift Box' WHERE PRODUCT_ID = 692;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Eliachi' WHERE PRODUCT_ID = 972;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Everyday comfort food - bun maska with crunchy Bhujia' WHERE PRODUCT_ID = 680;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Exclusive Platter for Navratra 2016' WHERE PRODUCT_ID = 1022;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Exclusive Platter for Navratra 2016' WHERE PRODUCT_ID = 1025;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Extra Pav for Pav Bhaji' WHERE PRODUCT_ID = 1047;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Filter Coffee' WHERE PRODUCT_ID = 1021;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Fresh Spinach, crispy Corn, melted Cheese served in a croissant' WHERE PRODUCT_ID = 540;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Freshly toasted bun with a more than generous dollop of butter' WHERE PRODUCT_ID = 690;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Freshness redefined' WHERE PRODUCT_ID = 450;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'From boring to bold - Green Tea in a flavourful new avatar' WHERE PRODUCT_ID = 1034;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Fully loaded Cheese on a Paneer veggie mix' WHERE PRODUCT_ID = 990;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Fully loaded Chicken and Cheese delight' WHERE PRODUCT_ID = 989;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Garden Fresh' WHERE PRODUCT_ID = 868;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Garlic roasted mushrooms with Italian herbs in a Focaccia bread' WHERE PRODUCT_ID = 530;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Ghar Jaisa Aloo Sandwich,Guranteed To Remind You Of Mom' WHERE PRODUCT_ID = 620;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Gift for Children' WHERE PRODUCT_ID = 1046;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Go Green, Go Lean' WHERE PRODUCT_ID = 170;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Green Chilli Mayo' WHERE PRODUCT_ID = 973;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Hari Chutni' WHERE PRODUCT_ID = 975;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Hari Mirch' WHERE PRODUCT_ID = 974;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Healthy and creamy with fresh Banana pulp' WHERE PRODUCT_ID = 390;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Healthy and creamy with fresh Chikoo pulp' WHERE PRODUCT_ID = 380;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Heavenly Mango and Chai' WHERE PRODUCT_ID = 140;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Honey' WHERE PRODUCT_ID = 976;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Honey Garlic Mayo' WHERE PRODUCT_ID = 977;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Indulgent winter classic' WHERE PRODUCT_ID = 1037;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Kadhai Paneer' WHERE PRODUCT_ID = 590;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Kali Mirch' WHERE PRODUCT_ID = 978;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Kangra Masala from the hills' WHERE PRODUCT_ID = 60;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Kashmiri Kahwa' WHERE PRODUCT_ID = 150;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Kesar' WHERE PRODUCT_ID = 979;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Laung' WHERE PRODUCT_ID = 980;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Lemon Ice Tea' WHERE PRODUCT_ID = 300;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Less Milk More Water' WHERE PRODUCT_ID = 12;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Maggie with Monk Special Masala' WHERE PRODUCT_ID = 1023;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Masala' WHERE PRODUCT_ID = 981;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Masala Chai No Sugar' WHERE PRODUCT_ID = 1020;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Masaledar Maggi With Crunchy Veggies In A Sandwich' WHERE PRODUCT_ID = 610;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Melt-in Mouth Mutton Keema In A Homemade Pav' WHERE PRODUCT_ID = 641;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Melt-in-mouth Mutton keema in a pav' WHERE PRODUCT_ID = 640;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Mint' WHERE PRODUCT_ID = 982;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'More Milk Less Water' WHERE PRODUCT_ID = 50;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Moti Eliachi' WHERE PRODUCT_ID = 983;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Mulethi' WHERE PRODUCT_ID = 984;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'New Cake Flavour - Red Velvet' WHERE PRODUCT_ID = 869;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'New Flavour Chocolate Cake' WHERE PRODUCT_ID = 870;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Our Masala Chai Frappe' WHERE PRODUCT_ID = 40;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Our winter favourite - flavourful & healthy' WHERE PRODUCT_ID = 1033;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Packaging Charges' WHERE PRODUCT_ID = 1043;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Passion Fruit Ice Tea' WHERE PRODUCT_ID = 320;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Peach Ice Tea' WHERE PRODUCT_ID = 310;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Perfect Chai companion, crisp and light' WHERE PRODUCT_ID = 780;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Poha - with the tanginess of Lemon & crispiness of Peanuts' WHERE PRODUCT_ID = 660;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Printed Kulhad' WHERE PRODUCT_ID = 1050;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Ready to Mix Masala Chai' WHERE PRODUCT_ID = 1057;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Red Chilli Mayo' WHERE PRODUCT_ID = 985;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Sab Kuch' WHERE PRODUCT_ID = 988;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Saffron Masala Chai served in a traditional Indian Kulhad' WHERE PRODUCT_ID = 80;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Saunf' WHERE PRODUCT_ID = 987;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Shaami Nachos' WHERE PRODUCT_ID = 1029;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Simple and refreshing Cold Coffee' WHERE PRODUCT_ID = 480;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Spicy cutlet for all seasons' WHERE PRODUCT_ID = 1024;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Spicy Italian style grilled Chicken in a Focaccia bread' WHERE PRODUCT_ID = 500;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Spicy Potato vada with hari chutney and lassoon chutney in a pav' WHERE PRODUCT_ID = 670;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Teatime favourite topped with walnuts and raisins' WHERE PRODUCT_ID = 1036;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'The original, all the way from Modinagar' WHERE PRODUCT_ID = 460;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Thepla served in Tacos style with juicy, spicy Paneer chunks' WHERE PRODUCT_ID = 1031;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Thepla served in Tacos style with roasted Chicken in spicy red sauce' WHERE PRODUCT_ID = 1032;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Thick cocoa indulgence' WHERE PRODUCT_ID = 272;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Traditional Indian Breakfast' WHERE PRODUCT_ID = 1030;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Tulsi' WHERE PRODUCT_ID = 986;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Warm your soul' WHERE PRODUCT_ID = 130;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Jeera Cookies' WHERE PRODUCT_ID = 790;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Badam Pista Cookies' WHERE PRODUCT_ID = 800;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Oatmeal Cookies' WHERE PRODUCT_ID = 810;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Badi Ketli (1000 ml, Serves 10)' WHERE PRODUCT_ID = 840;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Choti Ketli (400 ml, Serves 4)' WHERE PRODUCT_ID = 850;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Additional Honey' WHERE PRODUCT_ID = 860;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Kiwi Shake' WHERE PRODUCT_ID = 360;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Masala Chai' WHERE PRODUCT_ID = 1001;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Strawberry Shake' WHERE PRODUCT_ID = 370;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Tulsi Green Chai' WHERE PRODUCT_ID = 1002;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Dry Fruit Cookie Pack' WHERE PRODUCT_ID = 1004;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Whole Wheat Cookie Pack' WHERE PRODUCT_ID = 1005;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Shortbread Cookie Pack' WHERE PRODUCT_ID = 1006;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Jeera Cookie Pack' WHERE PRODUCT_ID = 1007;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Paneer Sambossa' WHERE PRODUCT_ID = 651;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Black Grape Shake' WHERE PRODUCT_ID = 410;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Rooh Afza' WHERE PRODUCT_ID = 430;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Thandai' WHERE PRODUCT_ID = 440;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chicken Sambossa' WHERE PRODUCT_ID = 652;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Bun Maska - Achari' WHERE PRODUCT_ID = 681;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Bun Maska - Honey Lemon' WHERE PRODUCT_ID = 682;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Bun Maska - Sundried Tomatoes & Olives' WHERE PRODUCT_ID = 683;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Bun Maska - Mint Jalapeno' WHERE PRODUCT_ID = 684;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Super Saver Combo' WHERE PRODUCT_ID = 834;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Bournvita' WHERE PRODUCT_ID = 290;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Mint N Lemon Green' WHERE PRODUCT_ID = 110;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Hot Chocolate' WHERE PRODUCT_ID = 270;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Hot Coffee' WHERE PRODUCT_ID = 280;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Lemon Green' WHERE PRODUCT_ID = 100;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Fresh Lime' WHERE PRODUCT_ID = 470;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Home Made Chaach' WHERE PRODUCT_ID = 490;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Adrak Tulsi Chai' WHERE PRODUCT_ID = 1040;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Adrak Tulsi Elaichi Chai' WHERE PRODUCT_ID = 1041;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Babycorn,Roma Tomatoes And Peppers Dressed With Olive Oil In A Multigrain Bread' WHERE PRODUCT_ID = 550;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Breakfast Special 299 Combo' WHERE PRODUCT_ID = 1052;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chaayos Gift Cards' WHERE PRODUCT_ID = 1048;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chaayos Gift Cards' WHERE PRODUCT_ID = 1056;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chaayos Special Playing Cards' WHERE PRODUCT_ID = 691;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chai Meal Combo' WHERE PRODUCT_ID = 833;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Cheapest Ticket To Morocco' WHERE PRODUCT_ID = 120;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chicken,Cheese And Coriander In A Multigrain Bread Sandwich Extremely Flavourful' WHERE PRODUCT_ID = 510;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Chocolate Cupcake' WHERE PRODUCT_ID = 866;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Cold + Nashta Combo' WHERE PRODUCT_ID = 836;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Cold + Sandwich Combo' WHERE PRODUCT_ID = 835;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Darjeeling 2nd Flush' WHERE PRODUCT_ID = 250;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Gift from Our Chinese Brothers' WHERE PRODUCT_ID = 240;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Golden Flowery Orange Pekoe from Lopchu Estates' WHERE PRODUCT_ID = 260;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Green Tea With A Hint Of Cinnamon' WHERE PRODUCT_ID = 90;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Gujiya' WHERE PRODUCT_ID = 865;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Hazelnut Flavoured Nutella Dip With Toasted Bun,Couldnt Get Sweeter' WHERE PRODUCT_ID = 560;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Holiday Kettle & Nashta Combo Non-Veg' WHERE PRODUCT_ID = 822;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Holiday Kettle & Nashta Combo Veg' WHERE PRODUCT_ID = 821;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Holiday Sandwich Combo Non-Veg' WHERE PRODUCT_ID = 832;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Holiday Sandwich Combo Veg' WHERE PRODUCT_ID = 831;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Homemade Soya Kebab With Chatpata Flavours' WHERE PRODUCT_ID = 600;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Indias finest' WHERE PRODUCT_ID = 210;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Kiwi Ice Tea' WHERE PRODUCT_ID = 340;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Lunch Special 399 Combo' WHERE PRODUCT_ID = 1053;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Made in India , named after them' WHERE PRODUCT_ID = 180;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Melt-in Mouth Spicy Hyderabadi Mutton Kebab' WHERE PRODUCT_ID = 580;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Nothing Orange about it' WHERE PRODUCT_ID = 200;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Our Best, You Gotta try this' WHERE PRODUCT_ID = 160;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Party Combo 1499' WHERE PRODUCT_ID = 1055;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Pepper Roasted Chicken With A Dash Of Honey In A Croissant' WHERE PRODUCT_ID = 520;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Redberry Ice Tea' WHERE PRODUCT_ID = 350;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Saunf Elaichi Chai' WHERE PRODUCT_ID = 1042;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Staple Street Food Of Bombay!' WHERE PRODUCT_ID = 630;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Staple Street Food Of Bombay!' WHERE PRODUCT_ID = 631;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Strawberry Cupcake' WHERE PRODUCT_ID = 867;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Strawberry Ice Tea' WHERE PRODUCT_ID = 330;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'The Earl left , Tea reamined ' WHERE PRODUCT_ID = 190;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Tired of Your Boss, Try This' WHERE PRODUCT_ID = 220;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Tired of Your Boss, Try This' WHERE PRODUCT_ID = 230;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Vatika Special Combo' WHERE PRODUCT_ID = 1039;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Winter Feast 449 Combo' WHERE PRODUCT_ID = 1054;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Blueberry Cake' WHERE PRODUCT_ID = 740;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Banana Cake' WHERE PRODUCT_ID = 750;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Carrot Cake' WHERE PRODUCT_ID = 760;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Lemon Cake' WHERE PRODUCT_ID = 770;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Nashta Combo' WHERE PRODUCT_ID = 820;
UPDATE KETTLE_MASTER.PRODUCT_DETAIL SET PRODUCT_DESCRIPTION = 'Lunch Combo' WHERE PRODUCT_ID = 830;



#BUSINESS HOUR UPDATES

TRUNCATE KETTLE_MASTER_DEV.BUSINESS_HOURS;

INSERT INTO BUSINESS_HOURS (UNIT_ID,IS_OPERATIONAL,HAS_DINE_IN,HAS_DELIVERY,HAS_TAKE_AWAY,DAY_OF_WEEK_NUMBER,DAY_OF_WEEK_TEXT,NO_OF_SHIFTS,DINE_IN_OPEN_TIME,DINE_IN_CLOSE_TIME,DELIVERY_OPEN_TIME,DELIVERY_CLOSE_TIME,TAKE_AWAY_OPEN_TIME,TAKE_AWAY_CLOSE_TIME,SHIFT_ONE_HANDOVER_TIME,SHIFT_TWO_HANDOVER_TIME)
VALUES
('10000','Y','Y','Y','Y','1','Sunday','2','08:00:00','23:30:00','09:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10000','Y','Y','Y','Y','2','Monday','2','08:00:00','23:30:00','09:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10000','Y','Y','Y','Y','3','Tuesday','2','08:00:00','23:30:00','09:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10000','Y','Y','Y','Y','4','Wednesday','2','08:00:00','23:30:00','09:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10000','Y','Y','Y','Y','5','Thursday','2','08:00:00','23:30:00','09:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10000','Y','Y','Y','Y','6','Friday','2','08:00:00','23:30:00','09:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10000','Y','Y','Y','Y','7','Saturday','2','08:00:00','23:30:00','09:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10001','Y','Y','N','Y','1','Sunday','2','07:30:00','22:30:00','09:00:00','22:30:00','07:30:00','22:30:00','14:00:00',null),
('10001','Y','Y','N','Y','2','Monday','2','07:30:00','22:30:00','09:00:00','22:30:00','07:30:00','22:30:00','14:00:00',null),
('10001','Y','Y','N','Y','3','Tuesday','2','07:30:00','22:30:00','09:00:00','22:30:00','07:30:00','22:30:00','14:00:00',null),
('10001','Y','Y','N','Y','4','Wednesday','2','07:30:00','22:30:00','09:00:00','22:30:00','07:30:00','22:30:00','14:00:00',null),
('10001','Y','Y','N','Y','5','Thursday','2','07:30:00','22:30:00','09:00:00','22:30:00','07:30:00','22:30:00','14:00:00',null),
('10001','Y','Y','N','Y','6','Friday','2','07:30:00','22:30:00','09:00:00','22:30:00','07:30:00','22:30:00','14:00:00',null),
('10001','Y','Y','N','Y','7','Saturday','2','07:30:00','22:30:00','09:00:00','22:30:00','07:30:00','22:30:00','14:00:00',null),
('10002','N','N','N','N','1','Sunday','2','08:00:00','21:00:00','08:00:00','21:00:00','08:00:00','21:00:00','14:00:00',null),
('10002','Y','Y','Y','Y','2','Monday','2','08:00:00','21:00:00','08:00:00','21:00:00','08:00:00','21:00:00','14:00:00',null),
('10002','Y','Y','Y','Y','3','Tuesday','2','08:00:00','21:00:00','08:00:00','21:00:00','08:00:00','21:00:00','14:00:00',null),
('10002','Y','Y','Y','Y','4','Wednesday','2','08:00:00','21:00:00','08:00:00','21:00:00','08:00:00','21:00:00','14:00:00',null),
('10002','Y','Y','Y','Y','5','Thursday','2','08:00:00','21:00:00','08:00:00','21:00:00','08:00:00','21:00:00','14:00:00',null),
('10002','Y','Y','Y','Y','6','Friday','2','08:00:00','21:00:00','08:00:00','21:00:00','08:00:00','21:00:00','14:00:00',null),
('10002','Y','Y','Y','Y','7','Saturday','2','08:00:00','21:00:00','08:00:00','21:00:00','08:00:00','21:00:00','14:00:00',null),
('10003','N','N','N','N','1','Sunday','2','08:00:00','23:00:00','08:00:00','08:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('10003','Y','Y','N','Y','2','Monday','2','08:00:00','23:00:00','08:00:00','08:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('10003','Y','Y','N','Y','3','Tuesday','2','08:00:00','23:00:00','08:00:00','08:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('10003','Y','Y','N','Y','4','Wednesday','2','08:00:00','23:00:00','08:00:00','08:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('10003','Y','Y','N','Y','5','Thursday','2','08:00:00','23:00:00','08:00:00','08:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('10003','Y','Y','N','Y','6','Friday','2','08:00:00','23:00:00','08:00:00','08:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('10003','Y','Y','N','Y','7','Saturday','2','08:00:00','23:00:00','08:00:00','08:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('10004','N','N','N','N','1','Sunday','2','08:00:00','21:00:00','08:00:00','08:00:00','08:00:00','21:00:00','13:00:00',null),
('10004','Y','Y','Y','Y','2','Monday','2','08:00:00','21:00:00','08:00:00','08:00:00','08:00:00','21:00:00','13:00:00',null),
('10004','Y','Y','Y','Y','3','Tuesday','2','08:00:00','21:00:00','08:00:00','08:00:00','08:00:00','21:00:00','13:00:00',null),
('10004','Y','Y','Y','Y','4','Wednesday','2','08:00:00','21:00:00','08:00:00','08:00:00','08:00:00','21:00:00','13:00:00',null),
('10004','Y','Y','Y','Y','5','Thursday','2','08:00:00','21:00:00','08:00:00','08:00:00','08:00:00','21:00:00','13:00:00',null),
('10004','Y','Y','Y','Y','6','Friday','2','08:00:00','21:00:00','08:00:00','08:00:00','08:00:00','21:00:00','13:00:00',null),
('10004','Y','Y','Y','Y','7','Saturday','2','08:00:00','21:00:00','08:00:00','08:00:00','08:00:00','21:00:00','13:00:00',null),
('10005','Y','Y','N','Y','1','Sunday','2','08:00:00','23:30:00','08:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10005','Y','Y','N','Y','2','Monday','2','08:00:00','23:30:00','08:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10005','Y','Y','N','Y','3','Tuesday','2','08:00:00','23:30:00','08:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10005','Y','Y','N','Y','4','Wednesday','2','08:00:00','23:30:00','08:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10005','Y','Y','N','Y','5','Thursday','2','08:00:00','23:30:00','08:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10005','Y','Y','N','Y','6','Friday','2','08:00:00','23:30:00','08:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10005','Y','Y','N','Y','7','Saturday','2','08:00:00','23:30:00','08:00:00','23:30:00','08:00:00','23:30:00','15:00:00',null),
('10006','Y','Y','Y','Y','1','Sunday','2','08:00:00','23:00:00','07:30:00','21:30:00','08:00:00','23:00:00','15:00:00',null),
('10006','Y','Y','Y','Y','2','Monday','2','08:00:00','23:00:00','07:30:00','21:30:00','08:00:00','23:00:00','15:00:00',null),
('10006','Y','Y','Y','Y','3','Tuesday','2','08:00:00','23:00:00','07:30:00','21:30:00','08:00:00','23:00:00','15:00:00',null),
('10006','Y','Y','Y','Y','4','Wednesday','2','08:00:00','23:00:00','07:30:00','21:30:00','08:00:00','23:00:00','15:00:00',null),
('10006','Y','Y','Y','Y','5','Thursday','2','08:00:00','23:00:00','07:30:00','21:30:00','08:00:00','23:00:00','15:00:00',null),
('10006','Y','Y','Y','Y','6','Friday','2','08:00:00','23:00:00','07:30:00','21:30:00','08:00:00','23:00:00','15:00:00',null),
('10006','Y','Y','Y','Y','7','Saturday','2','08:00:00','23:00:00','07:30:00','21:30:00','08:00:00','23:00:00','15:00:00',null),
('10007','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('10007','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('10007','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('10007','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('10007','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('10007','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('10007','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('10008','Y','Y','N','Y','1','Sunday','2','08:00:00','23:00:00',null,null,'08:00:00','23:00:00','15:00:00',null),
('10008','Y','Y','N','Y','2','Monday','2','08:00:00','23:00:00',null,null,'08:00:00','23:00:00','15:00:00',null),
('10008','Y','Y','N','Y','3','Tuesday','2','08:00:00','23:00:00',null,null,'08:00:00','23:00:00','15:00:00',null),
('10008','Y','Y','N','Y','4','Wednesday','2','08:00:00','23:00:00',null,null,'08:00:00','23:00:00','15:00:00',null),
('10008','Y','Y','N','Y','5','Thursday','2','08:00:00','23:00:00',null,null,'08:00:00','23:00:00','15:00:00',null),
('10008','Y','Y','N','Y','6','Friday','2','08:00:00','23:00:00',null,null,'08:00:00','23:00:00','15:00:00',null),
('10008','Y','Y','N','Y','7','Saturday','2','08:00:00','23:00:00',null,null,'08:00:00','23:00:00','15:00:00',null),
('10009','Y','Y','Y','Y','1','Sunday','2','07:00:00','00:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('10009','Y','Y','Y','Y','2','Monday','2','07:00:00','00:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('10009','Y','Y','Y','Y','3','Tuesday','2','07:00:00','00:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('10009','Y','Y','Y','Y','4','Wednesday','2','07:00:00','00:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('10009','Y','Y','Y','Y','5','Thursday','2','07:00:00','00:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('10009','Y','Y','Y','Y','6','Friday','2','07:00:00','00:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('10009','Y','Y','Y','Y','7','Saturday','2','07:00:00','00:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('10010','Y','Y','Y','Y','1','Sunday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('10010','Y','Y','Y','Y','2','Monday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('10010','Y','Y','Y','Y','3','Tuesday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('10010','Y','Y','Y','Y','4','Wednesday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('10010','Y','Y','Y','Y','5','Thursday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('10010','Y','Y','Y','Y','6','Friday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('10010','Y','Y','Y','Y','7','Saturday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('10011','N','N','N','N','1','Sunday','1','08:00:00','18:30:00',null,null,'08:00:00','18:30:00','07:00:00',null),
('10011','Y','Y','N','Y','2','Monday','1','08:00:00','18:30:00',null,null,'08:00:00','18:30:00','07:00:00',null),
('10011','Y','Y','N','Y','3','Tuesday','1','08:00:00','18:30:00',null,null,'08:00:00','18:30:00','07:00:00',null),
('10011','Y','Y','N','Y','4','Wednesday','1','08:00:00','18:30:00',null,null,'08:00:00','18:30:00','07:00:00',null),
('10011','Y','Y','N','Y','5','Thursday','1','08:00:00','18:30:00',null,null,'08:00:00','18:30:00','07:00:00',null),
('10011','Y','Y','N','Y','6','Friday','1','08:00:00','18:30:00',null,null,'08:00:00','18:30:00','07:00:00',null),
('10011','Y','Y','N','Y','7','Saturday','1','08:00:00','18:30:00',null,null,'08:00:00','18:30:00','07:00:00',null),
('10012','Y','Y','Y','Y','1','Sunday','3','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','08:00:00','12:00:00','16:00:00'),
('10012','Y','Y','Y','Y','2','Monday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','08:00:00','16:00:00',null),
('10012','Y','Y','Y','Y','3','Tuesday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','08:00:00','16:00:00',null),
('10012','Y','Y','Y','Y','4','Wednesday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','08:00:00','16:00:00',null),
('10012','Y','Y','Y','Y','5','Thursday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','08:00:00','16:00:00',null),
('10012','Y','Y','Y','Y','6','Friday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','08:00:00','16:00:00',null),
('10012','Y','Y','Y','Y','7','Saturday','3','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','08:00:00','12:00:00','16:00:00'),
('10013','Y','Y','Y','Y','1','Sunday','3','08:00:00','12:00:00','09:00:00','21:00:00','08:00:00','00:00:00','07:00:00','15:00:00'),
('10013','Y','Y','Y','Y','2','Monday','3','08:00:00','12:00:00','09:00:00','21:00:00','08:00:00','00:00:00','07:00:00','15:00:00'),
('10013','Y','Y','Y','Y','3','Tuesday','3','08:00:00','12:00:00','09:00:00','21:00:00','08:00:00','00:00:00','07:00:00','15:00:00'),
('10013','Y','Y','Y','Y','4','Wednesday','3','08:00:00','12:00:00','09:00:00','21:00:00','08:00:00','00:00:00','07:00:00','15:00:00'),
('10013','Y','Y','Y','Y','5','Thursday','3','08:00:00','12:00:00','09:00:00','21:00:00','08:00:00','00:00:00','07:00:00','15:00:00'),
('10013','Y','Y','Y','Y','6','Friday','3','08:00:00','12:00:00','09:00:00','21:00:00','08:00:00','00:00:00','07:00:00','15:00:00'),
('10013','Y','Y','Y','Y','7','Saturday','3','08:00:00','12:00:00','09:00:00','21:00:00','08:00:00','00:00:00','07:00:00','15:00:00'),
('10014','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('10014','Y','Y','N','N','2','Monday','1','10:00:00','18:00:00',null,null,null,null,'09:00:00',null),
('10014','Y','Y','N','N','3','Tuesday','1','10:00:00','18:00:00',null,null,null,null,'09:00:00',null),
('10014','Y','Y','N','N','4','Wednesday','1','10:00:00','18:00:00',null,null,null,null,'09:00:00',null),
('10014','Y','Y','N','N','5','Thursday','1','10:00:00','18:00:00',null,null,null,null,'09:00:00',null),
('10014','Y','Y','N','N','6','Friday','1','10:00:00','18:00:00',null,null,null,null,'09:00:00',null),
('10014','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('11001','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('11001','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('11001','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('11001','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('11001','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('11001','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('11001','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12001','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12001','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12001','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12001','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12001','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12001','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12001','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12002','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12002','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12002','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12002','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12002','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12002','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12002','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12003','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12003','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12003','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12003','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12003','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12003','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12003','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12004','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12004','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12004','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12004','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12004','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12004','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12004','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12005','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12005','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12005','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12005','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12005','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12005','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12005','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12006','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12006','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12006','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12006','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12006','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12006','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12006','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12007','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12007','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12007','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12007','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12007','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12007','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12007','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12008','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12008','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12008','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12008','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12008','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12008','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12008','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12009','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12009','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12009','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12009','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12009','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12009','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12009','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12010','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12010','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12010','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12010','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12010','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12010','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12010','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12011','Y','Y','N','Y','1','Sunday','2','10:00:00','22:00:00',null,null,'10:00:00','22:00:00','09:00:00','14:00:00'),
('12011','Y','Y','N','Y','2','Monday','2','10:00:00','22:00:00',null,null,'10:00:00','22:00:00','09:00:00','14:00:00'),
('12011','Y','Y','N','Y','3','Tuesday','2','10:00:00','22:00:00',null,null,'10:00:00','22:00:00','09:00:00','14:00:00'),
('12011','Y','Y','N','Y','4','Wednesday','2','10:00:00','22:00:00',null,null,'10:00:00','22:00:00','09:00:00','14:00:00'),
('12011','Y','Y','N','Y','5','Thursday','2','10:00:00','22:00:00',null,null,'10:00:00','22:00:00','09:00:00','14:00:00'),
('12011','Y','Y','N','Y','6','Friday','2','10:00:00','22:00:00',null,null,'10:00:00','22:00:00','09:00:00','14:00:00'),
('12011','Y','Y','N','Y','7','Saturday','2','10:00:00','22:00:00',null,null,'10:00:00','22:00:00','09:00:00','14:00:00'),
('12012','Y','Y','Y','Y','1','Sunday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','14:00:00',null),
('12012','Y','Y','Y','Y','2','Monday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','14:00:00',null),
('12012','Y','Y','Y','Y','3','Tuesday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','14:00:00',null),
('12012','Y','Y','Y','Y','4','Wednesday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','14:00:00',null),
('12012','Y','Y','Y','Y','5','Thursday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','14:00:00',null),
('12012','Y','Y','Y','Y','6','Friday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','14:00:00',null),
('12012','Y','Y','Y','Y','7','Saturday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','14:00:00',null),
('12013','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12013','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12013','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12013','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12013','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12013','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12013','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12014','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12014','Y','Y','Y','Y','2','Monday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12014','Y','Y','Y','Y','3','Tuesday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12014','Y','Y','Y','Y','4','Wednesday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12014','Y','Y','Y','Y','5','Thursday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12014','Y','Y','Y','Y','6','Friday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12014','Y','Y','Y','Y','7','Saturday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12015','Y','Y','Y','Y','1','Sunday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12015','Y','Y','Y','Y','2','Monday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12015','Y','Y','Y','Y','3','Tuesday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12015','Y','Y','Y','Y','4','Wednesday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12015','Y','Y','Y','Y','5','Thursday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12015','Y','Y','Y','Y','6','Friday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12015','Y','Y','Y','Y','7','Saturday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12016','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12016','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12016','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12016','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12016','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12016','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12016','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12017','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12017','Y','Y','N','Y','2','Monday','1','08:00:00','19:00:00',null,null,'08:00:00','19:00:00','08:00:00',null),
('12017','Y','Y','N','Y','3','Tuesday','1','08:00:00','19:00:00',null,null,'08:00:00','19:00:00','08:00:00',null),
('12017','Y','Y','N','Y','4','Wednesday','1','08:00:00','19:00:00',null,null,'08:00:00','19:00:00','08:00:00',null),
('12017','Y','Y','N','Y','5','Thursday','1','08:00:00','19:00:00',null,null,'08:00:00','19:00:00','08:00:00',null),
('12017','Y','Y','N','Y','6','Friday','1','08:00:00','19:00:00',null,null,'08:00:00','19:00:00','08:00:00',null),
('12017','Y','Y','N','Y','7','Saturday','1','08:00:00','19:00:00',null,null,'08:00:00','19:00:00','08:00:00',null),
('12018','Y','Y','N','Y','1','Sunday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12018','Y','Y','N','Y','2','Monday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12018','Y','Y','N','Y','3','Tuesday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12018','Y','Y','N','Y','4','Wednesday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12018','Y','Y','N','Y','5','Thursday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12018','Y','Y','N','Y','6','Friday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12018','Y','Y','N','Y','7','Saturday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12019','Y','Y','N','Y','1','Sunday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12019','Y','Y','N','Y','2','Monday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12019','Y','Y','N','Y','3','Tuesday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12019','Y','Y','N','Y','4','Wednesday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12019','Y','Y','N','Y','5','Thursday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12019','Y','Y','N','Y','6','Friday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12019','Y','Y','N','Y','7','Saturday','2','10:00:00','23:00:00',null,null,'10:00:00','23:00:00','14:00:00',null),
('12020','Y','Y','Y','Y','1','Sunday','2','09:00:00','23:00:00','09:00:00','21:30:00','09:00:00','23:00:00','14:00:00',null),
('12020','Y','Y','Y','Y','2','Monday','2','09:00:00','23:00:00','09:00:00','21:30:00','09:00:00','23:00:00','14:00:00',null),
('12020','Y','Y','Y','Y','3','Tuesday','2','09:00:00','23:00:00','09:00:00','21:30:00','09:00:00','23:00:00','14:00:00',null),
('12020','Y','Y','Y','Y','4','Wednesday','2','09:00:00','23:00:00','09:00:00','21:30:00','09:00:00','23:00:00','14:00:00',null),
('12020','Y','Y','Y','Y','5','Thursday','2','09:00:00','23:00:00','09:00:00','21:30:00','09:00:00','23:00:00','14:00:00',null),
('12020','Y','Y','Y','Y','6','Friday','2','09:00:00','23:00:00','09:00:00','21:30:00','09:00:00','23:00:00','14:00:00',null),
('12020','Y','Y','Y','Y','7','Saturday','2','09:00:00','23:00:00','09:00:00','21:30:00','09:00:00','23:00:00','14:00:00',null),
('12021','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12021','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12021','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12021','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12021','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12021','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12021','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12022','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12022','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12022','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12022','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12022','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12022','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12022','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12023','Y','Y','Y','Y','1','Sunday','2','07:00:00','12:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('12023','Y','Y','Y','Y','2','Monday','2','07:00:00','12:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('12023','Y','Y','Y','Y','3','Tuesday','2','07:00:00','12:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('12023','Y','Y','Y','Y','4','Wednesday','2','07:00:00','12:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('12023','Y','Y','Y','Y','5','Thursday','2','07:00:00','12:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('12023','Y','Y','Y','Y','6','Friday','2','07:00:00','12:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('12023','Y','Y','Y','Y','7','Saturday','2','07:00:00','12:30:00','07:30:00','23:00:00','07:00:00','12:30:00','16:00:00',null),
('12027','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12027','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('12027','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('12027','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('12027','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('12027','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('12027','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('12028','Y','Y','Y','Y','1','Sunday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12028','Y','Y','Y','Y','2','Monday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12028','Y','Y','Y','Y','3','Tuesday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12028','Y','Y','Y','Y','4','Wednesday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12028','Y','Y','Y','Y','5','Thursday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12028','Y','Y','Y','Y','6','Friday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12028','Y','Y','Y','Y','7','Saturday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','07:00:00','15:00:00'),
('12029','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('12029','Y','Y','Y','Y','2','Monday','1','09:00:00','19:00:00','09:00:00','19:00:00','09:00:00','19:00:00','08:00:00',null),
('12029','Y','Y','Y','Y','3','Tuesday','1','09:00:00','19:00:00','09:00:00','19:00:00','09:00:00','19:00:00','08:00:00',null),
('12029','Y','Y','Y','Y','4','Wednesday','1','09:00:00','19:00:00','09:00:00','19:00:00','09:00:00','19:00:00','08:00:00',null),
('12029','Y','Y','Y','Y','5','Thursday','1','09:00:00','19:00:00','09:00:00','19:00:00','09:00:00','19:00:00','08:00:00',null),
('12029','Y','Y','Y','Y','6','Friday','1','09:00:00','19:00:00','09:00:00','19:00:00','09:00:00','19:00:00','08:00:00',null),
('12029','Y','Y','Y','Y','7','Saturday','1','09:00:00','19:00:00','09:00:00','19:00:00','09:00:00','19:00:00','08:00:00',null),
('12030','Y','Y','Y','Y','1','Sunday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','23:00:00','14:00:00',null),
('12030','Y','Y','Y','Y','2','Monday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','23:00:00','14:00:00',null),
('12030','Y','Y','Y','Y','3','Tuesday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','23:00:00','14:00:00',null),
('12030','Y','Y','Y','Y','4','Wednesday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','23:00:00','14:00:00',null),
('12030','Y','Y','Y','Y','5','Thursday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','23:00:00','14:00:00',null),
('12030','Y','Y','Y','Y','6','Friday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','23:00:00','14:00:00',null),
('12030','Y','Y','Y','Y','7','Saturday','2','08:00:00','23:00:00','08:00:00','21:00:00','08:00:00','23:00:00','14:00:00',null),
('12031','Y','Y','Y','Y','1','Sunday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('12031','Y','Y','Y','Y','2','Monday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('12031','Y','Y','Y','Y','3','Tuesday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('12031','Y','Y','Y','Y','4','Wednesday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('12031','Y','Y','Y','Y','5','Thursday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('12031','Y','Y','Y','Y','6','Friday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('12031','Y','Y','Y','Y','7','Saturday','2','07:00:00','02:00:00','07:30:00','23:00:00','07:00:00','02:00:00','16:00:00',null),
('22001','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('22001','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('22001','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('22001','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('22001','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('22001','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('22001','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('22002','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('22002','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('22002','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('22002','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('22002','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('22002','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('22002','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('24001','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('24001','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('24001','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('24001','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('24001','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('24001','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('24001','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('24002','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('24002','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('24002','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('24002','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('24002','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('24002','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('24002','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26001','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26001','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26001','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26001','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26001','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26001','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26001','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26002','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26002','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26002','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26002','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26002','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26002','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26002','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26003','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26003','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26003','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26003','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26003','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26003','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26003','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26004','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26004','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26004','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26004','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26004','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26004','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26004','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26005','Y','Y','Y','Y','1','Sunday','3','08:00:00','01:00:00','08:00:00','21:30:00','08:00:00','01:00:00','13:00:00','17:00:00'),
('26005','Y','Y','Y','Y','2','Monday','3','08:00:00','01:00:00','08:00:00','21:30:00','08:00:00','01:00:00','13:00:00','17:00:00'),
('26005','Y','Y','Y','Y','3','Tuesday','3','08:00:00','01:00:00','08:00:00','21:30:00','08:00:00','01:00:00','13:00:00','17:00:00'),
('26005','Y','Y','Y','Y','4','Wednesday','3','08:00:00','01:00:00','08:00:00','21:30:00','08:00:00','01:00:00','13:00:00','17:00:00'),
('26005','Y','Y','Y','Y','5','Thursday','3','08:00:00','01:00:00','08:00:00','21:30:00','08:00:00','01:00:00','13:00:00','17:00:00'),
('26005','Y','Y','Y','Y','6','Friday','3','08:00:00','01:00:00','08:00:00','21:30:00','08:00:00','01:00:00','13:00:00','17:00:00'),
('26005','Y','Y','Y','Y','7','Saturday','3','08:00:00','01:00:00','08:00:00','21:30:00','08:00:00','01:00:00','13:00:00','17:00:00'),
('26006','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26006','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26006','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26006','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26006','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26006','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26006','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26007','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26007','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26007','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26007','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26007','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26007','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26007','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26008','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26008','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26008','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26008','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26008','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26008','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26008','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26009','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26009','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26009','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26009','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26009','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26009','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26009','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26010','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26010','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26010','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26010','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26010','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26010','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26010','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26011','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26011','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26011','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26011','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26011','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26011','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26011','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26012','Y','Y','Y','Y','1','Sunday','3','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','15:00:00',null),
('26012','Y','Y','Y','Y','2','Monday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','15:00:00',null),
('26012','Y','Y','Y','Y','3','Tuesday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','15:00:00',null),
('26012','Y','Y','Y','Y','4','Wednesday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','15:00:00',null),
('26012','Y','Y','Y','Y','5','Thursday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','15:00:00',null),
('26012','Y','Y','Y','Y','6','Friday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','15:00:00',null),
('26012','Y','Y','Y','Y','7','Saturday','2','10:00:00','23:00:00','10:00:00','21:00:00','10:00:00','23:00:00','15:00:00',null),
('26013','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26013','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26013','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26013','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26013','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26013','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26013','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26014','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26014','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26014','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26014','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26014','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26014','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26014','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26015','Y','Y','Y','Y','1','Sunday','3','00.00.00','23.59.00','09:00:00','21:00:00','00.00.00','23.59.00','16:00:00','23:00:00'),
('26015','Y','Y','Y','Y','2','Monday','3','00.00.00','23.59.00','09:00:00','21:00:00','00.00.00','23.59.00','16:00:00','23:00:00'),
('26015','Y','Y','Y','Y','3','Tuesday','3','00.00.00','23.59.00','09:00:00','21:00:00','00.00.00','23.59.00','16:00:00','23:00:00'),
('26015','Y','Y','Y','Y','4','Wednesday','3','00.00.00','23.59.00','09:00:00','21:00:00','00.00.00','23.59.00','16:00:00','23:00:00'),
('26015','Y','Y','Y','Y','5','Thursday','3','00.00.00','23.59.00','09:00:00','21:00:00','00.00.00','23.59.00','16:00:00','23:00:00'),
('26015','Y','Y','Y','Y','6','Friday','3','00.00.00','23.59.00','09:00:00','21:00:00','00.00.00','23.59.00','16:00:00','23:00:00'),
('26015','Y','Y','Y','Y','7','Saturday','3','00.00.00','23.59.00','09:00:00','21:00:00','00.00.00','23.59.00','16:00:00','23:00:00'),
('26016','Y','Y','Y','Y','1','Sunday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','12:00:00','16:00:00'),
('26016','Y','Y','Y','Y','2','Monday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','15:00:00',null),
('26016','Y','Y','Y','Y','3','Tuesday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','15:00:00',null),
('26016','Y','Y','Y','Y','4','Wednesday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','15:00:00',null),
('26016','Y','Y','Y','Y','5','Thursday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','15:00:00',null),
('26016','Y','Y','Y','Y','6','Friday','2','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','15:00:00',null),
('26016','Y','Y','Y','Y','7','Saturday','3','08:00:00','23:00:00','09:00:00','21:00:00','08:00:00','23:00:00','12:00:00','16:00:00'),
('26017','Y','Y','Y','Y','1','Sunday','2','11:00:00','23:00:00','10:00:00','21:00:00','11:00:00','23:00:00','15:00:00',null),
('26017','Y','Y','Y','Y','2','Monday','2','11:00:00','23:00:00','10:00:00','21:00:00','11:00:00','23:00:00','15:00:00',null),
('26017','Y','Y','Y','Y','3','Tuesday','2','11:00:00','23:00:00','10:00:00','21:00:00','11:00:00','23:00:00','15:00:00',null),
('26017','Y','Y','Y','Y','4','Wednesday','2','11:00:00','23:00:00','10:00:00','21:00:00','11:00:00','23:00:00','15:00:00',null),
('26017','Y','Y','Y','Y','5','Thursday','2','11:00:00','23:00:00','10:00:00','21:00:00','11:00:00','23:00:00','15:00:00',null),
('26017','Y','Y','Y','Y','6','Friday','2','11:00:00','23:00:00','10:00:00','21:00:00','11:00:00','23:00:00','15:00:00',null),
('26017','Y','Y','Y','Y','7','Saturday','2','11:00:00','23:00:00','10:00:00','21:00:00','11:00:00','23:00:00','15:00:00',null),
('26018','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26018','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26018','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26018','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26018','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26018','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26018','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null),
('26019','N','N','N','N','1','Sunday','0',null,null,null,null,null,null,null,null),
('26019','N','N','N','N','2','Monday','0',null,null,null,null,null,null,null,null),
('26019','N','N','N','N','3','Tuesday','0',null,null,null,null,null,null,null,null),
('26019','N','N','N','N','4','Wednesday','0',null,null,null,null,null,null,null,null),
('26019','N','N','N','N','5','Thursday','0',null,null,null,null,null,null,null,null),
('26019','N','N','N','N','6','Friday','0',null,null,null,null,null,null,null,null),
('26019','N','N','N','N','7','Saturday','0',null,null,null,null,null,null,null,null);


INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP (RL_ID, RTL_ID, RL_CODE, RL_NAME, RL_STATUS)
VALUES ('3632', '37', 'Other', 'Other', 'ACTIVE');
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET WEB_CATEGORY_TYPE='3632' WHERE PRODUCT_ID='1043';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET WEB_CATEGORY_TYPE='3632' WHERE PRODUCT_ID='1044';
