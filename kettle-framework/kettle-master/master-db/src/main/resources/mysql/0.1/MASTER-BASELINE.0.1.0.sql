CREATE TABLE KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API(
  API_ID INT PRIMARY KEY AUTO_INCREMENT,
  API VARCHAR(255) UNIQUE NOT NULL
);

INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('kettle-service.delivery-management.orders-for-feedback');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('kettle-service.delivery-management.update.*');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('master-service.users.login');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('master-service.users.admin.login');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('master-service.users.authenticateMac');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('master-service.users.logout');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('master-service.users.changePasscode');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('master-service.unit-metadata.all-units');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('master-service.unit-metadata.takeaway-units');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('kettle-service.pos-metadata.unit.report.manager.download');
INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (`API`) VALUES ('kettle-service.delivery-management.submit-order-feedback');
