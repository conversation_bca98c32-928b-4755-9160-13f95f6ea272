###### address for mumbai warehouse kitchen and office
INSERT INTO KETTLE_MASTER_DEV.ADDRESS_INFO (`ADDRESS_LINE_1`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `ADDRESS_TYPE`, `LATITUDE`, `LON<PERSON>TUDE`) VALUES ('23 & 24, Bldg No. 1 Ajay <PERSON>l Ind. Pre. Co-up Soc. Ltd., Marol Naka, Andheri (E)', 'Mumbai', 'Maharashtra', 'India', '400059', '9893598230', 'OFFICIAL', '19.1163793', '72.87985830000002');
###### address for delhi warehouse
INSERT INTO KETTLE_MASTER_DEV.ADDRESS_INFO (`ADDRESS_LINE_1`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `ADDRESS_TYPE`, `LATITUDE`, `LONGITUDE`) VALUES ('Building No. 382, Ground Floor, 100 ft. road, Ghitorni', 'Delhi', 'Delhi', 'India', '110030', '9599598308', 'OFFICIAL', '28.4964413', '77.***********');
###### address for delhi kitchen
INSERT INTO KETTLE_MASTER_DEV.ADDRESS_INFO (`ADDRESS_LINE_1`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `ADDRESS_TYPE`, `LATITUDE`, `LONGITUDE`) VALUES ('Building No. 382, Ground Floor, 100 ft. road, Ghitorni', 'Delhi', 'Delhi', 'India', '110030', '9599786493', 'OFFICIAL', '28.4964413', '77.***********');
###### address for delhi office
INSERT INTO KETTLE_MASTER_DEV.ADDRESS_INFO (`ADDRESS_LINE_1`, `CITY`, `STATE`, `COUNTRY`, `ZIPCODE`, `CONTACT_NUM_1`, `ADDRESS_TYPE`, `LATITUDE`, `LONGITUDE`) VALUES ('Building No. 382, Ground Floor, 100 ft. road, Ghitorni', 'Delhi', 'Delhi', 'India', '110030', '9971023887', 'OFFICIAL', '28.4964413', '77.***********');

###### unit detail for delhi warehouse  update with address id of warehouse delhi
INSERT INTO KETTLE_MASTER_DEV.UNIT_DETAIL (`UNIT_ID`, `UNIT_NAME`, `UNIT_REGION`, `UNIT_CATEGORY`, `UNIT_EMAIL`, `START_DATE`, `UNIT_STATUS`, `TIN`, `UNIT_ADDR_ID`, `BUSINESS_DIV_ID`, `NO_OF_TERMINALS`, `NO_OF_TA_TERMINALS`, `WORKSTATION_ENABLED`, `NO_OF_TABLES`, `UNIT_SUB_CATEGORY`) VALUES ('22001', 'Warehouse Delhi', 'NCR', 'WAREHOUSE', '<EMAIL>', '2016-07-05 05:30:00', 'ACTIVE', '***********', '12188', '1000', '0', '0', 'N', '0', 'INTL');

###### unit detail for delhi kitchen  update with address id of kitchen delhi
INSERT INTO KETTLE_MASTER_DEV.UNIT_DETAIL (`UNIT_ID`, `UNIT_NAME`, `UNIT_REGION`, `UNIT_CATEGORY`, `UNIT_EMAIL`, `START_DATE`, `UNIT_STATUS`, `TIN`, `UNIT_ADDR_ID`, `BUSINESS_DIV_ID`, `NO_OF_TERMINALS`, `NO_OF_TA_TERMINALS`, `WORKSTATION_ENABLED`, `NO_OF_TABLES`, `UNIT_SUB_CATEGORY`) VALUES ('24001', 'Kitchen Delhi', 'NCR', 'KITCHEN', '<EMAIL>', '2016-07-05 05:30:00', 'ACTIVE', '***********', '12189', '1000', '0', '0', 'N', '0', 'INTL');

###### unit detail for delhi office  update with address id of office delhi
INSERT INTO KETTLE_MASTER_DEV.UNIT_DETAIL (`UNIT_ID`, `UNIT_NAME`, `UNIT_REGION`, `UNIT_CATEGORY`, `UNIT_EMAIL`, `START_DATE`, `UNIT_STATUS`, `TIN`, `UNIT_ADDR_ID`, `BUSINESS_DIV_ID`, `NO_OF_TERMINALS`, `NO_OF_TA_TERMINALS`, `WORKSTATION_ENABLED`, `NO_OF_TABLES`, `UNIT_SUB_CATEGORY`) VALUES ('26001','Office Delhi', 'NCR', 'OFFICE', '<EMAIL>', '2016-07-05 05:30:00', 'ACTIVE', '***********', '12190', '1000', '0', '0', 'N', '0', 'INTL');

###### unit detail for mumbai warehouse  update with address id of warehouse mumbai
INSERT INTO KETTLE_MASTER_DEV.UNIT_DETAIL (`UNIT_ID`, `UNIT_NAME`, `UNIT_REGION`, `UNIT_CATEGORY`, `UNIT_EMAIL`, `START_DATE`, `UNIT_STATUS`, `TIN`, `UNIT_ADDR_ID`, `BUSINESS_DIV_ID`, `NO_OF_TERMINALS`, `NO_OF_TA_TERMINALS`, `WORKSTATION_ENABLED`, `NO_OF_TABLES`, `UNIT_SUB_CATEGORY`) VALUES ('22002', 'Warehouse Mumbai', 'MUMBAI', 'WAREHOUSE', '<EMAIL>', '2016-07-05 05:30:00', 'ACTIVE', '27141149418V', '12187', '1000', '0', '0', 'N', '0', 'INTL');

###### unit detail for mumbai kitchen  update with address id of kitchen mumbai
INSERT INTO KETTLE_MASTER_DEV.UNIT_DETAIL (`UNIT_ID`, `UNIT_NAME`, `UNIT_REGION`, `UNIT_CATEGORY`, `UNIT_EMAIL`, `START_DATE`, `UNIT_STATUS`, `TIN`, `UNIT_ADDR_ID`, `BUSINESS_DIV_ID`, `NO_OF_TERMINALS`, `NO_OF_TA_TERMINALS`, `WORKSTATION_ENABLED`, `NO_OF_TABLES`, `UNIT_SUB_CATEGORY`) VALUES ('24002', 'Kitchen Mumbai', 'MUMBAI', 'KITCHEN', '<EMAIL>', '2016-07-05 05:30:00', 'ACTIVE', '27141149418V', '12187', '1000', '0', '0', 'N', '0', 'INTL');

###### unit detail for mumbai office  update with address id of office mumbai
INSERT INTO KETTLE_MASTER_DEV.UNIT_DETAIL (`UNIT_ID`, `UNIT_NAME`, `UNIT_REGION`, `UNIT_CATEGORY`, `UNIT_EMAIL`, `START_DATE`, `UNIT_STATUS`, `TIN`, `UNIT_ADDR_ID`, `BUSINESS_DIV_ID`, `NO_OF_TERMINALS`, `NO_OF_TA_TERMINALS`, `WORKSTATION_ENABLED`, `NO_OF_TABLES`, `UNIT_SUB_CATEGORY`) VALUES ('26002','Office Mumbai', 'MUMBAI', 'OFFICE', '<EMAIL>', '2016-07-05 05:30:00', 'ACTIVE', '27141149418V', '12187', '1000', '0', '0', 'N', '0', 'INTL');