DROP TABLE IF EXISTS KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST;
CREATE TABLE KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST(
PRODUCT_RECIPE_COST_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
RECIPE_ID INTEGER NOT NULL,
RECIPE_NAME VARCHAR(200) NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
DIMENSION VARCHAR(20) NOT NULL,
COST_TYPE VARCHAR(20) NOT NULL,
COST DECIMAL(10,2) NULL,
EVENT_ENTRY_ID INTEGER NULL,
UPDATED_BY VARCHAR(100) NULL,
UPDATED_BY_ID INTEGER NULL,
LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL
);

CREATE INDEX RECIPE_ID_PRODUCT_RECIPE_COST ON KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST(RECIPE_ID) USING BTREE;
CREATE INDEX PRODUCT_ID_PRODUCT_RECIPE_COST ON KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST(PRODUCT_ID) USING BTREE;
CREATE INDEX DIMENSION_PRODUCT_RECIPE_COST ON KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST(DIMENSION) USING BTREE;
CREATE INDEX EVENT_ENTRY_ID_PRODUCT_RECIPE_COST ON KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST(EVENT_ENTRY_ID) USING BTREE;
CREATE INDEX COST_TYPE_PRODUCT_RECIPE_COST ON KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST(COST_TYPE) USING BTREE;



INSERT INTO `KETTLE_MASTER_DEV`.`EXTERNAL_PARTNER_INFO` 
(`EXTERNAL_PARTNER_INFO_ID`, `PARTNER_NAME`, `PARTNER_CODE`, `PASS_CODE`, `API_KEY`, `CREATION_DATE`, `PARTNER_STATUS`) 
VALUES ('4', 'scm-service', 'scm-service', 'I2YW4', 'eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6InNjbS1zZXJ2aWNlIiwiZW52VHlwZSI6IlBST0QiLCJwYXNzQ29kZSI6IkkyWVc0IiwiaWF0IjoxNDg4ODA2NzIyfQ.GVmjU7X5vDMNFSzKhlS67wlSpTwIRw9VLry5MOOLf0c', '2017-03-06', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) 
VALUES ('4', '1111', '112', 'ACTIVE');

