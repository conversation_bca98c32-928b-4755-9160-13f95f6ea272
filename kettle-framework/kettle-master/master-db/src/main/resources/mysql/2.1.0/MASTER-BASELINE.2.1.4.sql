CREATE TABLE KETTLE_MASTER_DEV.KIOSK_COMPANY_DETAILS (
  COMPANY_ID INT NOT NULL AUTO_INCREMENT ,
  COMPANY_NAME VARCHAR(45) NOT NULL ,
  COMPANY_EMAIL VARCHAR(45) NOT NULL ,
  COMPANY_STATUS VARCHAR(45) NOT NULL ,
  CONTACT_NAME VARCHAR(45) NOT NULL ,
  CONTACT_EMAIL VARCHAR(45) NOT NULL ,
  CONTACT_PHONE VARCHAR(45) NOT NULL ,
  COUNTRY VARCHAR(45) NOT NULL ,
  PAYMENT_MODE VARCHAR(45) NOT NULL ,
  PRIMARY KEY (COMPANY_ID)  ,
  UNIQUE INDEX COMPANY_ID_UNIQUE (COMPANY_ID ASC)
);

CREATE TABLE KETTLE_MASTER_DEV.KIOSK_COMPANY_DOMAIN_DATA (
  DOMAIN_ID INT NOT NULL AUTO_INCREMENT,
  D<PERSON><PERSON>IN_NAME VARCHAR(45) NOT NULL,
  DOMAIN_STATUS VARCHAR(45) NOT NULL,
  COMPANY_ID INT NOT NULL,
  PRIMARY KEY (DOMAIN_ID) ,
  UNIQUE INDEX DOMAIN_ID_UNIQUE (DOMAIN_ID ASC) ,
  CONSTRAINT
    FOREIGN KEY (COMPANY_ID)
    REFERENCES KETTLE_MASTER_DEV.KIOSK_COMPANY_DETAILS (COMPANY_ID)
);

CREATE TABLE KETTLE_MASTER_DEV.KIOSK_OFFICE_DETAILS (
  OFFICE_ID INT NOT NULL AUTO_INCREMENT ,
  OFFICE_NAME VARCHAR(45) NOT NULL ,
  OFFICE_SHORT_CODE VARCHAR(45) NOT NULL ,
  OFFICE_REGION VARCHAR(45) NOT NULL ,
  OFFICE_TIN VARCHAR(45) NOT NULL ,
  OFFICE_STATUS VARCHAR(45) NOT NULL ,
  OFFICE_PAYMENT_CODE VARCHAR(45) NOT NULL ,
  OFFICE_CONTACT_NAME VARCHAR(45) NOT NULL ,
  OFFICE_CONTACT_EMAIL VARCHAR(45) NOT NULL ,
  OFFICE_CONTACT_PHONE VARCHAR(45) NOT NULL ,
  OFFICE_ADDR_ID INT NULL ,
  COMPANY_ID INT NULL ,
  PRIMARY KEY (OFFICE_ID),
  UNIQUE INDEX idKIOSK_OFFICE_DETAILS_UNIQUE (OFFICE_ID ASC),
  CONSTRAINT
    FOREIGN KEY (COMPANY_ID)
    REFERENCES KETTLE_MASTER_DEV.KIOSK_COMPANY_DETAILS (COMPANY_ID),
  CONSTRAINT
    FOREIGN KEY (OFFICE_ADDR_ID)
    REFERENCES KETTLE_MASTER_DEV.ADDRESS_INFO (ADDRESS_ID)
);

CREATE TABLE KETTLE_MASTER_DEV.KIOSK_OFFICE_LOCATION_DETAILS (
  KIOSK_LOCATION_ID INT NOT NULL AUTO_INCREMENT ,
  LOCATION_NAME VARCHAR(45) NULL ,
  LOCATION_ADDRESS VARCHAR(45) NULL ,
  LOCATION_SHORT_CODE VARCHAR(45) NULL ,
  LOCATION_STATUS VARCHAR(45) NULL ,
  UNIT_ID INT NULL ,
  OFFICE_ID INT NULL ,
  PRIMARY KEY (KIOSK_LOCATION_ID)  ,
  UNIQUE INDEX idKIOSK_OFFICE_LOCATION_DETAILS_UNIQUE (KIOSK_LOCATION_ID ASC)  ,
  CONSTRAINT
    FOREIGN KEY (OFFICE_ID)
    REFERENCES KETTLE_MASTER_DEV.KIOSK_OFFICE_DETAILS (OFFICE_ID),
  CONSTRAINT
    FOREIGN KEY (UNIT_ID)
    REFERENCES KETTLE_MASTER_DEV.UNIT_DETAIL (UNIT_ID)
);

CREATE TABLE KETTLE_MASTER_DEV.KIOSK_MACHINE_DETAILS (
  MACHINE_ID INT NOT NULL AUTO_INCREMENT ,
  MACHINE_UUID VARCHAR(45) NULL ,
  INSTALLATION_DATE TIMESTAMP NULL ,
  MANUFACTURING_DATE TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  DEACTIVATION_DATE TIMESTAMP NULL ,
  MACHINE_STATUS VARCHAR(45) NOT NULL ,
  BATCH_NUMBER VARCHAR(45) NOT NULL ,
  KIOSK_LOCATION_ID INT NULL ,
  PRIMARY KEY (MACHINE_ID)  ,
  UNIQUE INDEX idKIOSK_MACHINE_DETAILS_UNIQUE (MACHINE_ID ASC),
  CONSTRAINT
    FOREIGN KEY (KIOSK_LOCATION_ID)
    REFERENCES KETTLE_MASTER_DEV.KIOSK_OFFICE_LOCATION_DETAILS (KIOSK_LOCATION_ID)
);

INSERT INTO KETTLE_MASTER_DEV.KIOSK_COMPANY_DETAILS (COMPANY_ID, COMPANY_NAME, COMPANY_EMAIL, COMPANY_STATUS, CONTACT_NAME, CONTACT_EMAIL, CONTACT_PHONE, COUNTRY, PAYMENT_MODE, KIOSK_SUB_DOMAIN)
 VALUES ('3', 'Oyo Rooms', '<EMAIL>', 'ACTIVE', 'Vinod Rathore', '<EMAIL>', '9988556633', 'IN', 'EMPLOYEE_PAID', 'oyo');
 
INSERT INTO KETTLE_MASTER_DEV.KIOSK_COMPANY_DOMAIN_DATA (DOMAIN_NAME, DOMAIN_STATUS, COMPANY_ID)
VALUES ('@oyo.com', 'ACTIVE', '3');

INSERT INTO KETTLE_MASTER_DEV.KIOSK_OFFICE_DETAILS (OFFICE_NAME, OFFICE_SHORT_CODE, OFFICE_REGION, OFFICE_TIN, OFFICE_STATUS, OFFICE_PAYMENT_CODE, OFFICE_CONTACT_NAME, OFFICE_CONTACT_EMAIL, OFFICE_CONTACT_PHONE, OFFICE_ADDR_ID, COMPANY_ID)
VALUES ('Oyo Gurgaon', 'OYOGGN', 'NCR', '9988556633', 'ACTIVE', 'EMPLOYEE_PAID', 'Vinod Rathod', '<EMAIL>', '9988556633', '12267', '3');

INSERT INTO KETTLE_MASTER_DEV.KIOSK_OFFICE_LOCATION_DETAILS (LOCATION_NAME, LOCATION_ADDRESS, LOCATION_SHORT_CODE, LOCATION_STATUS, UNIT_ID, OFFICE_ID)
VALUES ('Spaze Palazzo Office', '9th floor, Oyo Rooms, Space Palazzo IT Park', 'OYO9F', 'ACTIVE', '26006', '3');



INSERT INTO KETTLE_MASTER_DEV.EXTERNAL_PARTNER_INFO (EXTERNAL_PARTNER_INFO_ID, PARTNER_NAME, PARTNER_CODE, PASS_CODE, API_KEY, CREATION_DATE, PARTNER_STATUS)
VALUES (NULL, 'noah', 'noah', '2V14K', 'eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5vYWgiLCJlbnZUeXBlIjoiREVWIiwicGFzc0NvZGUiOiIyVjE0SyIsImlhdCI6MTQ3NTgzNDMwOX0.m7kmHK7MeV2AhhTZNvRh1hBDmsh1UI4GcF0fHUhzfak', '2016-10-07', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID, PERMISSION, ACL_ID, PPM_STATUS)
VALUES ((select EXTERNAL_PARTNER_INFO_ID from EXTERNAL_PARTNER_INFO where PARTNER_NAME = "noah") , '1111', '105', 'ACTIVE');


