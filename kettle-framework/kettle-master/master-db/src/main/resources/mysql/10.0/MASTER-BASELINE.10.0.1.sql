CREATE TABLE KETTLE_MASTER_DEV.STRATEGY_DEFINITION(
	STRATEGY_ID INT(11) AUTO_INCREMENT NOT NULL PRIMARY KEY,
    STRATEGY_NAME VARCHAR(50) NOT NULL,
    DESC<PERSON>PTION VARCHAR(100) NOT NULL,
    STATUS VARCHAR(50) NOT NULL,
    CREATED_AT TIMESTAMP NOT NULL,
    CREATED_BY INT NOT NULL
);

CREATE TABLE KETTLE_MASTER_DEV.WALLET_STRATEGY_DEFINITION(
	WALLET_STRATEGY_ID INT(11) AUTO_INCREMENT NOT NULL PRIMARY KEY,
    STRATEGY_ID INT NOT NULL,
    STRATEGY_GROUP_ID INT NOT NULL,
    STATUS VARCHAR(50) NOT NULL,
    MIN_AMOUNT DECIMAL(10,2) NOT NULL,
    MAX_AMOUNT DECIMAL(10,2) NOT NULL,
    VALUE DECIMAL(10,2) NOT NULL,
    ALLOTMENT_TYPE VARCHAR(50) NOT NULL,
    ALLOTMENT_VALUE DECIMAL(10,2) NOT NULL,
    OFFER_VALUE DECIMAL(10,2) NOT NULL
);

CREATE TABLE KETTLE_MASTER_DEV.WALLET_VERBIAGE_STRATEGY_DEFINITION(
	WALLET_VERBIAGE_STRATEGY_ID INT(11) AUTO_INCREMENT NOT NULL PRIMARY KEY,
    STRATEGY_ID INT NOT NULL,
    STRATEGY_GROUP_ID INT NOT NULL,
    STATUS VARCHAR(50) NOT NULL,
    VERBIAGE VARCHAR(255) NOT NULL
);

CREATE TABLE KETTLE_MASTER_DEV.STRATEGY_GROUP(
	STRATEGY_GROUP_ID INT(11) AUTO_INCREMENT NOT NULL PRIMARY KEY,
    STRATEGY_GROUP_NAME VARCHAR(50) NOT NULL,
    DESCRIPTION VARCHAR(100) NOT NULL,
    STATUS VARCHAR(50) NOT NULL,
    CREATED_AT TIMESTAMP NOT NULL,
    CREATED_BY INT NOT NULL
);

ALTER TABLE KETTLE_MASTER_DEV.STRATEGY_DEFINITION
ADD COLUMN STRATEGY_GROUP_ID INT NOT NULL AFTER STRATEGY_NAME;

ALTER TABLE KETTLE_MASTER_DEV.STRATEGY_DEFINITION
ADD FOREIGN KEY STRATEGY_GROUP_ID(STRATEGY_GROUP_ID) REFERENCES KETTLE_MASTER_DEV.STRATEGY_GROUP(STRATEGY_GROUP_ID);
ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_DETAIL
ADD COLUMN TAXABLE_COGS VARCHAR(10) NULL;