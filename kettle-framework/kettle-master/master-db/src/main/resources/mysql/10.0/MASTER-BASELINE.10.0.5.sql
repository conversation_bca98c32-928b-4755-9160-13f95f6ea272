CREATE TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING_METADATA (
  METADATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  MAPPING_ID INTEGER NOT NULL,
  METADATA_KEY VARCHAR(50) NOT NULL,
  METADATA_VALUE VARCHAR(50) NOT NULL,
  MAPPING_STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE'
);
ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING_METADATA DROP COLUMN MAPPING_ID;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING_METADATA ADD COLUMN UNIT_ID INTEGER NOT NULL;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING_METADATA ADD COLUMN PARTNER_ID INTEGER NOT NULL;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING_METADATA ADD COLUMN BRAND_ID INTEGER NOT NULL;