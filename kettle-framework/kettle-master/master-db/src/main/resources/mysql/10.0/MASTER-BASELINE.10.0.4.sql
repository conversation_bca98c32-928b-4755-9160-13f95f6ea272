INSERT INTO KETTLE_MASTER_DEV.WALLET_STRATEGY_DEFINITION (WALLET_STRATEGY_ID, STRATEGY_ID, STRATEGY_GROUP_ID, STATUS,
MIN_AMOUNT, MAX_AMOUNT, VALUE, ALLOTMENT_TYPE, ALLOTMENT_VALUE, OFFER_VALUE)
VALUES
    (9,  1, 1, 'ACTIVE', 800.0, 899.0, 900.0, 'FIXED_AMOUNT', 90.0, 90.0),
    (10,  1, 1, 'ACTIVE', 900.0, 999.0, 1000.0, 'FIXED_AMOUNT', 100.0, 100.0),
    (11,  1, 1, 'ACTIVE', 1000.0, 1099.0, 1100.0, 'FIXED_AMOUNT', 110.0, 110.0),
    (12,  1, 1, 'ACTIVE', 1100.0, 1199.0, 1200.0, 'FIXED_AMOUNT', 120.0, 120.0),
    (13,  1, 1, 'ACTIVE', 1200.0, 1299.0, 1300.0, 'FIXED_AMOUNT', 130.0, 130.0),
    (14,  1, 1, 'ACTIVE', 1300.0, 1399.0, 1400.0, 'FIXED_AMOUNT', 140.0, 140.0),
    (15,  1, 1, 'ACTIVE', 1400.0, 1499.0, 1500.0, 'FIXED_AMOUNT', 150.0, 150.0),
    (16,  1, 1, 'ACTIVE', 1500.0, 1599.0, 1600.0, 'FIXED_AMOUNT', 160.0, 160.0),
    (17,  1, 1, 'ACTIVE', 1600.0, 1699.0, 1700.0, 'FIXED_AMOUNT', 170.0, 170.0),
    (18,  1, 1, 'ACTIVE', 1700.0, 1799.0, 1800.0, 'FIXED_AMOUNT', 180.0, 180.0),
    (19,  1, 1, 'ACTIVE', 1800.0, 1899.0, 1900.0, 'FIXED_AMOUNT', 190.0, 190.0),
    (20,  1, 1, 'ACTIVE', 1900.0, 1999.0, 2000.0, 'FIXED_AMOUNT', 200.0, 200.0);


    INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('Admin Suggestive Ordering', 'Access to set Exception Date and UPT Calc', 'ACTIVE',5);




 INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_SGT_ORD', '5', 'MENU', 'SHOW', 'ADMIN-> SUGGESTIVE ORDERING->SHOW', 'ACTIVE');




  INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'ADMIN SUGGESTIVE ORDERING'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_SGT_ORD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');




INSERT INTO KETTLE_MASTER_DEV.BANNER_METADATA(NAME,VALUE,TYPE,DESCRIPTION)
       VALUES('MOVE_TO_CATEGORY','MOVE_TO_CATEGORY','ACTION_TYPE','MOVE_TO_CATEGORY');

ALTER TABLE KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA
ADD COLUMN MENU_CATEGORY_ID INT NULL;


ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_GROUP_DATA
ADD  COLUMN CATEGORY_TAG VARCHAR(20) NULL;

ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_GROUP_DATA
ADD COLUMN MENU_CATEGORY_IMAGE VARCHAR(30) NULL,
ADD COLUMN MENU_CATEGORY_DETAILS VARCHAR(255) NULL;

ALTER TABLE `KETTLE_MASTER_DEV`.`PRODUCT_GROUP_DATA`
CHANGE COLUMN `MENU_CATEGORY_IMAGE` `MENU_CATEGORY_IMAGE` VARCHAR(500) NULL DEFAULT NULL ;