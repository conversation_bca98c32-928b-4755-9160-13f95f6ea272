 ALTER TABLE
     KETTLE_MASTER_DEV.WALLET_VERBIAGE_STRATEGY_DEFINITION
    CHANGE VERBIAGE VERBIAGE
    VARCHAR(255)
    CHARACTER SET utf8mb4
    COLLATE utf8mb4_unicode_ci;

ALTER TABLE KETTLE_MASTER_DEV.WALLET_VERBIAGE_STRATEGY_DEFINITION
ADD COLUMN TITLE VARCHAR(256) NOT NULL,
ADD COLUMN COUNT INT(11) NOT NULL,
ADD COLUMN BUTTON_POSITION VARCHAR(50);

UPDATE KETTLE_MASTER_DEV.WALLET_VERBIAGE_STRATEGY_DEFINITION
SET TITLE = 'Get extra with each order!', COUNT = '1',
BUTTON_POSITION = 'ABOVE_TEXT' where WALLET_VERBIAGE_STRATEGY_ID = 1;

INSERT INTO KETTLE_MASTER_DEV.STRATEGY_DEFINITION (STRATEGY_ID, STRATEGY_NAME, STRATEGY_G<PERSON>UP_ID, DESCRIPTION, STATUS, CREATED_AT, CREATED_BY)
VALUES
	(3, 'Wallet Verbiage count 2', 2,'Wallet Verbiage count 2 Suggestion Strategy', 'ACTIVE', now(), 120057),
    (4, 'Wallet Verbiage count 3', 2,'Wallet Verbiage count 3 Suggestion Strategy', 'ACTIVE', now(), 120057);

INSERT INTO KETTLE_MASTER_DEV.WALLET_VERBIAGE_STRATEGY_DEFINITION (
WALLET_VERBIAGE_STRATEGY_ID, STRATEGY_ID, STRATEGY_GROUP_ID, STATUS, VERBIAGE, TITLE, COUNT, BUTTON_POSITION)
VALUES
	(2,  3, 2, 'ACTIVE', "PAY ₹ {extra} EXTRA & GET ₹ {offerValue} MORE", "Fuel your wallet with each order!",2, 'BELOW_TEXT'),
    (3,  4, 2, 'ACTIVE', "GET ₹ {offerValue} EXTRA", "Get extra with each order!",3, 'ABOVE_TEXT');

    UPDATE KETTLE_MASTER_DEV.WALLET_VERBIAGE_STRATEGY_DEFINITION
SET VERBIAGE = "GET ₹ {resultant} IN WALLET" where WALLET_VERBIAGE_STRATEGY_ID = 1;