INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('PnL Complete Budget', 'Access to Upload Complete Budget', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('PnL Manpower Budget', 'Access to Upload PnL Manpower Budget', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('PnL Facilities Budget', 'Access to Upload PnL Facilities Budget', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('PnL Channel Partner Charges Budget', 'Access to Upload PnL Channel Partner Charges Budget', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('PnL Bank Charges Budget', 'Access to Upload PnL Bank Charges Budget', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('Penetration Targets', 'Access to Upload Penetration Targets', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('Regenerate PnL For Units', 'Access to Regenerate PnL For Units', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('Regenerate PnL For All', 'Access to Regenerate PnL For All', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('Regenerate Finalized PnL', 'Access to Regenerate Finalized PnL', 'ACTIVE',5);


INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_CMP_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> PnL Complete Budget->SHOW', 'ACTIVE');
  INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_MP_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> PnL Manpower Budget->SHOW', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_FCL_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> PnL Facilities Budget->SHOW', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_CPC_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> PnL Channel Partner Charges Budget->SHOW', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_BC_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> PnL Bank Charges Budget->SHOW', 'ACTIVE');
 INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_PT_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> Penetration Targets->SHOW', 'ACTIVE');
 INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_RPU_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> Regenerate PnL For Units->SHOW', 'ACTIVE');
 INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_RPA_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> Regenerate PnL For All->SHOW', 'ACTIVE');
 INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_RFP_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> Regenerate Finalized PnL->SHOW', 'ACTIVE');


 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PnL Complete Budget'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_CMP_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PnL Manpower Budget'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_MP_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PnL Facilities Budget'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_FCL_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PnL Channel Partner Charges Budget'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_CPC_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PnL Bank Charges Budget'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_BC_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Penetration Targets'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_PT_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Regenerate PnL For Units'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_RPU_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Regenerate PnL For All'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_RPA_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');

 INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Regenerate Finalized PnL'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_RFP_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');



INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
 VALUES ('ADMN_BGT_SERCHR_UPD', '5', 'SUBMENU', 'SHOW', 'ADMIN-> PnL Service Charges->SHOW', 'ACTIVE');

 INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`,`APPLICATION_ID`)
VALUES ('PnL Service Charges', 'Access to Upload Service Charges', 'ACTIVE',5);

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
 VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PnL Service Charges'),
 (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_BGT_SERCHR_UPD'), 'ACTIVE', '120057', '2021-01-19 00:00:00');
