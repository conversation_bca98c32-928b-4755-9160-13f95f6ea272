ALTER TABLE KETTLE_MASTER_DEV.DESIGNATION ADD COLUMN CHANNEL_PARTNER_ACCESS VARCHAR(1) NOT NULL DEFAULT 'N';

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('channel-partner.swiggy.*', 'Swiggy partner permissions', 'ACTIVE', 'CHANNEL_PARTNER');
INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('channel-partner.*', 'Channel partner service full access', 'ACTIVE', 'CHANNEL_PARTNER');
INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('channel-partner.partner-order.*', 'Partner order permissions', 'ACTIVE', 'CHANNEL_PARTNER');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING(PARTNER_ID, PERMISSION,ACL_ID,PPM_STATUS)
VALUES((SELECT EXTERNAL_PARTNER_INFO_ID FROM KETTLE_MASTER_DEV.EXTERNAL_PARTNER_INFO WHERE PARTNER_NAME='SWIGGY'),1111,
(SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE = 'channel-partner.swiggy.*'),'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'DEPUTY_GENERAL_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSAM'),'ACTIVE',120955,'2018-05-23 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'DEPUTY_GENERAL_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSSTAM'),'ACTIVE',120955,'2018-05-23 17:56:00'),
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'DEPUTY_GENERAL_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSSEAM'),'ACTIVE',120955,'2018-05-23 17:56:00');