ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN IS_LIVE VARCHAR(1) NOT NULL DEFAULT 'N';

CREATE INDEX UNIT_DETAIL_IS_LIVE ON KETTLE_MASTER_DEV.UNIT_DETAIL(IS_LIVE) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN HOT_N_COLD_MERGED VARCHAR(1) NOT NULL DEFAULT 'N';

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL
SET HOT_N_COLD_MERGED = "Y"
WHERE UNIT_ID NOT IN (12019,26048);

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL
SET IS_LIVE= 'Y'
WHERE UNIT_STATUS = 'ACTIVE';

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL
SET IS_LIVE= 'N'
WHERE UNIT_STATUS != 'ACTIVE';


ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN LIVE_INVENTORY_ENABLED VARCHAR(1) NOT NULL DEFAULT 'N';

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL
SET LIVE_INVENTORY_ENABLED= 'N';


INSERT INTO `KETTLE_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('kettle-service.order-management.last-day-close', 'Collect Last Day Close for Cafe', 'ACTIVE', 'KETTLE_SERVICE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES ('kettle-service.order-management.sync-order-inventory', 'Sync Order Inventory', 'ACTIVE', 'KETTLE_SERVICE');
INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('7', '1111', '110', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('7', '1111', '145', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('7', '1111', '146', 'ACTIVE');

