ALTER TABLE KETTLE_DEV.CHANNEL_PARTNER ADD COLUMN API_INTEGRATED VARCHAR(1) DEFAULT 'N' NOT NULL;

INSERT INTO `KETTLE_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`)
VALUES ('channel-partner.zomato.*', 'Zomato partner permissions', 'ACTIVE', 'CHANNEL_PARTNER');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`)
VALUES ('11', '1111', (SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE = 'channel-partner.zomato.*'), 'ACTIVE');
