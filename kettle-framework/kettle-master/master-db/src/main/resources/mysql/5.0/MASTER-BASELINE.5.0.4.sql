DROP TABLE IF EXISTS `KETTLE_MASTER_DEV`.`WARNING_REASON_DETAIL`;
CREATE TABLE `KETTLE_MASTER_DEV`.`WARNING_REASON_DETAIL` (
  `REASON_ID` INT NOT NULL AUTO_INCREMENT,
  `CATEGORY` VARCHAR(50) NOT NULL,
  `REASON_NAME` VARCHAR(150) NOT NULL,
  `REASON_RESULT` VARCHAR(150) NOT NULL,
  `WARNING_TYPE` VARCHAR(2) NOT NULL,
  STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE',
  PRIMARY KEY (`REASON_ID`));
  
   INSERT INTO `KETTLE_MASTER_DEV`.`WARNING_REASON_DETAIL`
(`CATEGORY`,`REASON_NAME`,`REASON_RESULT`,`WARNING_TYPE`)
VALUES
('Behavioural','Consumption of alcohol on shift','Direct Termination','L3'),
('Behavioural','Consumption of tobacco on shift','3 Warning Letters Process','L1'),
('Behavioural','Smoking on shift (except breaks)','3 Warning Letters Process','L1'),
('Behavioural','Physical fight with anyone on shift','Direct Termination','L3'),
('Behavioural','Grooming violation','3 Warning Letters Process','L1'),
('Behavioural','Carrying Arms & Ammunition in Cafe','Direct Termination','L3'),
('Cash','Order made without being punched in POS','Direct Termination','L3'),
('Cash','Orders clubbed together in POS','3 Warning Letters Process','L1'),
('Cash','IOU taken by any member of the company','3 Warning Letters Process','L1'),
('Cash','Cash Variance  (Till & Imprest )+/- 50','1 Warning Letter Process','L2'),
('Cash','Till operated by another person log in Id','1 Warning Letter Process','L2'),
('Cash','Employee meal sale','Direct Termination','L3'),
('Cash','System Bills not tagged on Manual bill book','1 Warning Letter Process','L2'),
('Communication','Using Local Language (only Eng & Hindi allowed)','3 Warning Letters Process','L1'),
('Communication','Using abusive language on shift','3 Warning Letters Process','L1'),
('Communication','Shouting/Talking loudly in Cafe','3 Warning Letters Process','L1'),
('Inventory','Fake Inventory entered in Kettle','Direct Termination','L3'),
('Product','Expiry items found in Cafe','3 Warning Letters Process','L1'),
('Product','Expiry items used in Cafe','Direct Termination','L3'),
('Product','Accepting/Using unapproved inventory','3 Warning Letters Process','L1'),
('Product','Accepting/Using products with temperature abuse','3 Warning Letters Process','L1'),
('Product','Incorrect SOP/Recipe followed and served','3 Warning Letters Process','L1'),
('Product','Cross Contamination of any kind','3 Warning Letters Process','L1'),
('Service','Charging Customer for replacement','3 Warning Letters Process','L1'),
('Systems','Cannot switch off CCTV or change Camera position','Direct Termination','L3'),
('Systems','Cannot access CC Storage/DVR Box','Direct Termination','L3'),
('Systems','Tamper with Biometric machine','Direct Termination','L3'),
('Systems','Fake attendance on Bio metrics','Direct Termination','L3'),
('Systems','Songs played from source other than Radiowala','3 Warning Letters Process','L1'),
('Systems','Using internet for personal use','3 Warning Letters Process','L1'),
('Systems','Using Cafe PCs for personal use','3 Warning Letters Process','L1'),
('Systems','Sharing Kettle ID & Password','3 Warning Letters Process','L1'),
('Systems','Using another persons Kettle ID to take orders','3 Warning Letters Process','L1'),
('Systems','Falsely filled checklists','3 Warning Letters Process','L1'),
('Systems','False inventory/wastage updated','3 Warning Letters Process','L1'),
('Systems','Carrying Cafe assets home, e,g. laptop, etc.','1 Warning Letter Process','L2'),
('Training','Training workbook incomplete','3 Warning Letters Process','L1'),
('Training','Training videos incomplete','3 Warning Letters Process','L1'),
('Training','Completing online video for someone else','1 Warning Letter Process','L2'),
('Wastage','Fake Wastage punched in Sumo','1 Warning Letter Process','L2');


INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('StringDelimited', 'AMPE', 'am.pending.emails');

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('StringDelimited', 'DGMPE', 'dgm.pending.emails');

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('StringDelimited', 'HRPE', 'hr.pending.emails');

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('StringDelimited', 'WAE', 'warning.approved.emails');

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('StringDelimited', 'WCE', 'warning.cancelled.emails');

INSERT INTO `KETTLE_MASTER_DEV`.`CONFIG_ATTRIBUTE_DEFINITION` (`ATTRIBUTE_TYPE`, `ATTRIBUTE_CODE`, `ATTRIBUTE_NAME`) VALUES ('StringDelimited', 'WRE', 'warning.rejected.emails');


INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME,ROLE_DESCRIPTION,ROLE_STATUS) VALUES ('DEPUTY_GENERAL_MANAGER','DEPUTY_GENERAL_MANAGER','ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA (ROLE_NAME,ROLE_DESCRIPTION,ROLE_STATUS) VALUES ('HR_HEAD','HR_HEAD','ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE,APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_DESCRIPTION,ACTION_STATUS)
VALUES
('FSWM', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'MENU', 'VIEW', 'Forms service -> Warning Management -> show', 'ACTIVE'),
('FSWMIWWA', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'SUBMENU', 'VIEW', 'Forms service -> Warning Management -> Issue Warning -> Without Audit', 'ACTIVE'),
('FSWMIWBA', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'ACTION', 'ADD', 'Forms service -> Warning Management -> Issue Warning -> By Audit', 'ACTIVE'),
('FSWMAMR', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'ACTION', 'UPDATE', 'Forms service -> Warning Management -> AM Response', 'ACTIVE'),
('FSWMDGMR', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'ACTION', 'UPDATE', 'Forms service -> Warning Management -> DGM Response', 'ACTIVE'),
('FSWMHRR', (SELECT APPLICATION_ID FROM `KETTLE_MASTER_DEV`.`APPLICATION_DATA` WHERE APPLICATION_NAME = 'FORMS_SERVICE'), 'ACTION', 'UPDATE', 'Forms service -> Warning Management -> HR Response', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
VALUES
((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWMIWWA'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWMIWBA'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AUDITOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWMIWBA'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWMAMR'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'DEPUTY_GENERAL_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWMDGMR'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'HR_HEAD'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWMHRR'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWM'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AUDITOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWM'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'DEPUTY_GENERAL_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWM'),'ACTIVE',120955,'2018-04-09 17:56:00'),

((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'HR_HEAD'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'FSWM'),'ACTIVE',120955,'2018-04-09 17:56:00');