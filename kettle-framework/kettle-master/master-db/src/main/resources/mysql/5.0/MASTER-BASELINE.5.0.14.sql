INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES ('channel-partner.zomato.*','Zomato partner permissions', 'ACTIVE', 'CHANNEL_PARTNER');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID, PERMISSION, ACL_ID, PPM_STATUS)
VALUES (11, 1111, (SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE = 'channel-partner.zomato.*'), 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` (`ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`)
VALUES ('kettle-crm.cod-customer.*', 'COD customer lookup and update', 'ACTIVE', 'KET<PERSON>E_CRM');

INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`)
VALUES ('10', '1111', (SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE = 'kettle-crm.cod-customer.*'), 'ACTIVE');
