DROP TABLE IF EXISTS KETTLE_MASTER_DEV.METADATA_ACTIVITY_LOGGER;
CREATE TABLE KETTLE_MASTER_DEV.METADATA_ACTIVITY_LOGGER(
METADATA_ACTIVITY_LOGGER_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
KEY_TYPE VARCHAR(50) NOT NULL,
KEY_ID VARCHAR(50) NOT NULL,
ACTIVITY_TYPE VARCHAR(30) NOT NULL,
ACTIVITY_USER_ID INTEGER NOT NULL,
ACTIVITY_LOG TEXT NOT NULL,
ACTIVITY_TIME TIMESTAMP NOT NULL
);

CREATE INDEX METADATA_ACTIVITY_LOGGER_KEY_TYPE ON KETTLE_MASTER_DEV.METADATA_ACTIVITY_LOGGER(KEY_TYPE) USING BTREE;
CREATE INDEX METADATA_ACTIVITY_LOGGER_ACTIVITY_TYPE ON KETTLE_MASTER_DEV.METADATA_ACTIVITY_LOGGER(ACTIVITY_TYPE) USING BTREE;
CREATE INDEX METADATA_ACTIVITY_LOGGER_KEY_ID ON KETTLE_MASTER_DEV.METADATA_ACTIVITY_LOGGER(KEY_ID) USING BTREE;
