CREATE TABLE KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING(
UNIT_ATTRIBUTE_MAPPING_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
ATTRIBUTE_ID INTEGER NOT NULL,
ATTRIBUTE_CODE VARCHAR(50) NOT NULL,
ATTRIBUTE_TYPE VARCHAR(20) NOT NULL,
ATTRIBUTE_VALUE VARCHAR(500) NOT NULL,
MAPPING_STATUS VARCHAR(15) NOT NULL
);

CREATE INDEX UNIT_ATTRIBUTE_MAPPING_ATTRIBUTE_ID ON KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING(ATTRIBUTE_ID) USING BTREE;
CREATE INDEX UNIT_ATTRIBUTE_MAPPING_UNIT_ID ON KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING(UNIT_ID) USING BTREE;
CREATE INDEX UNIT_ATTRIBUTE_MAPPING_ATTRIBUTE_CODE ON KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING(ATTRIBUTE_CODE) USING BTREE;
CREATE INDEX UNIT_ATTRIBUTE_MAPPING_ATTRIBUTE_TYPE ON KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING(ATTRIBUTE_TYPE) USING BTREE;
CREATE INDEX UNIT_ATTRIBUTE_MAPPING_MAPPING_STATUS ON KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING(MAPPING_STATUS) USING BTREE;



INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,1,'WORKSTATION_ENABLED','STRING', WORKSTATION_ENABLED,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,2,'NO_OF_TABLES','INTEGER', NO_OF_TABLES,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,3,'HAS_TABLE_SERVICE','STRING', HAS_TABLE_SERVICE,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,4,'FREE_INTERNET_ACCESS','STRING', FREE_INTERNET_ACCESS,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,5,'IS_PARTNER_PRICED','STRING', case when IS_PARTNER_PRICED IS NULL THEN 'N' ELSE IS_PARTNER_PRICED end,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,6,'IS_TOKEN_ENABLED','STRING', IS_TOKEN_ENABLED,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,7,'TOKEN_LIMIT','INTEGER', TOKEN_LIMIT,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,8,'ELECTRICITY_METER_COUNT','INTEGER', ELECTRICITY_METER_COUNT,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,9,'IS_DG_AVAILABLE','STRING', IS_DG_AVAILABLE,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,10,'TRUE_CALLER_ONBOARDING','STRING', case when TRUE_CALLER_ONBOARDING is null then  'DEFAULT' else TRUE_CALLER_ONBOARDING end  ,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,11,'HOT_N_COLD_MERGED','STRING', HOT_N_COLD_MERGED,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;
INSERT INTO KETTLE_MASTER_DEV.UNIT_ATTRIBUTE_MAPPING (UNIT_ID,ATTRIBUTE_ID,ATTRIBUTE_CODE,ATTRIBUTE_TYPE,ATTRIBUTE_VALUE,MAPPING_STATUS)
select UNIT_ID ,12,'LIVE_INVENTORY_ENABLED','STRING', LIVE_INVENTORY_ENABLED,'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL 
DROP COLUMN WORKSTATION_ENABLED,
DROP COLUMN NO_OF_TABLES, 
DROP COLUMN HAS_TABLE_SERVICE,
DROP COLUMN FREE_INTERNET_ACCESS,
DROP COLUMN IS_PARTNER_PRICED,
DROP COLUMN IS_TOKEN_ENABLED,
DROP COLUMN ELECTRICITY_METER_COUNT,
DROP COLUMN IS_DG_AVAILABLE,
DROP COLUMN TRUE_CALLER_ONBOARDING,
DROP COLUMN HOT_N_COLD_MERGED,
DROP COLUMN LIVE_INVENTORY_ENABLED,
DROP COLUMN TOKEN_LIMIT
;


CREATE TABLE `KETTLE_MASTER_DEV`.`APPLICATION_INSTALLATION_DATA` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `APPLICATION_NAME` VARCHAR(100) NOT NULL,
  `SCREEN_TYPE` VARCHAR(50) NULL,
  `UNIT_ID` INT(11) NOT NULL,
  `TERMINAL` INT(11) NULL,
  `MACHINE_ID` VARCHAR(200) NOT NULL,
  `MACHINE_DETAILS` VARCHAR(500) NULL,
  `CREATED_BY` INT(11) NOT NULL,
  `CREATED_ON` TIMESTAMP NULL,
  `LAST_UPDATED_BY` INT(11) NULL,
  `LAST_UPDATED_ON` TIMESTAMP NULL,
  `STATUS` VARCHAR(15) NOT NULL,
  PRIMARY KEY (`ID`),
  INDEX `APPLICATION_INSTALLATION_DATA_APPLICATION_NAME` (`APPLICATION_NAME` ASC),
  INDEX `APPLICATION_INSTALLATION_DATA_UNIT_ID` USING BTREE (`UNIT_ID` ASC));
  
   CREATE TABLE `KETTLE_MASTER_DEV`.`UNIT_RESTRICTED_APPLICATION` (
  `ID` INT(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` INT(11) NOT NULL,
  `APPLICATION_NAME` VARCHAR(100) NOT NULL,
  `LAST_UPDATED_BY` INT(11) NULL,
  `LAST_UPDATED_ON` TIMESTAMP NULL,
  `STATUS` VARCHAR(15) NOT NULL,
	PRIMARY KEY (`ID`));
