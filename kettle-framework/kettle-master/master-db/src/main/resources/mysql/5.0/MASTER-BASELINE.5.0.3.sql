INSERT INTO `KETTLE_MASTER_DEV`.`EXTERNAL_PARTNER_INFO` (`EXTERNAL_PARTNER_INFO_ID`, `PARTNER_NAME`, `PARTNER_CODE`, `PASS_CODE`, `API_KEY`, `CREATION_DATE`, `PARTNER_STATUS`)
VALUES ('7', 'inventory-service', 'inventory-service', 'E7254', 'eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImludmVudG9yeS1zZXJ2aWNlIiwiZW52VHlwZSI6IlBST0QiLCJwYXNzQ29kZSI6IkU3MjU0IiwiaWF0IjoxNTIxMDIzMzkwfQ.LbBQlZofH0m1vDu4gmdKjZXwY4f48plQ0DJnZR36zVo', '2018-03-14', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA` (`ACL_ID`, `ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`)
VALUES ('134', 'scm-service.scm-data.*', 'SCM Data Service for Inventory', 'ACTIVE', 'SCM_SERVICE');

INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('7', '1111', '117', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('7', '1111', '134', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING` (`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('7', '1111', '111', 'ACTIVE');

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
ADD COLUMN PREPAID VARCHAR(1) NULL DEFAULT 'N';

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
ADD COLUMN PREPAID_AMOUNT DECIMAL(10,4) NULL;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_METADATA
ADD COLUMN MAPPING_CLASS VARCHAR (100) NULL;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_METADATA
MODIFY COLUMN MAPPING_VALUE VARCHAR (250) NULL;

INSERT INTO `KETTLE_MASTER_DEV`.`PAYMENT_MODE` (`MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`,
 `SETTLEMENT_TYPE`, `GENERATE_PULL`, `COMMISSION_RATE`, `MODE_STATUS`, `MODE_CATEGORY`, `AUTOMATIC_PULL_VALIDATE`,
  `AUTOMATIC_TRANSFER`, `AUTOMATIC_CLOSE_TRANSFER`, `IS_EDITABLE`, `NEEDS_SETTLEMENT_SLIP_NUMBER`, `VALIDATION_SOURCE`)
   VALUES ('Prepaid', 'PREPAID', 'Prepaid', 'DEBIT', '1', '0.00', 'ACTIVE', 'OFFLINE', 'Y', 'Y', 'Y', 'N', 'N', 'Settlement Report');

INSERT INTO KETTLE_MASTER_DEV.UNIT_PAYMENT_MODE_MAPPING(UNIT_ID,PAYMENT_MODE_ID,MAPPING_STATUS)
SELECT UNIT_ID,(SELECT PAYMENT_MODE_ID FROM KETTLE_MASTER_DEV.PAYMENT_MODE WHERE MODE_TYPE="PREPAID"),'ACTIVE'
FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY="CAFE" AND UNIT_STATUS="ACTIVE";

INSERT INTO `KETTLE_MASTER_DEV`.`OFFER_DETAIL_DATA`
 (`OFFER_CATEGORY`, `OFFER_TYPE`, `OFFER_TEXT`, `OFFER_DESCRIPTION`, `START_DATE`, `END_DATE`, `OFFER_STATUS`,
  `MIN_VALUE`, `VALIDATE_CUSTOMER`, `INCLUDE_TAXES`, `PRIORITY`, `OFFER_SCOPE`, `MIN_ITEM_COUNT`, `QUANTITY_LIMIT`,
   `LOYALTY_LIMIT`, `OFFER_VALUE`, `PREPAID`, `PREPAID_AMOUNT`)
    VALUES ('ITEM', 'COMBO_STRATEGY', 'VADA PAV & CHAI @ 125', 'VADA PAV & CHAI @ 125', '2017-09-19', '2017-09-19',
     'ACTIVE', '0', 'Y', 'Y', '0', 'CUSTOMER', '1', '20', '0', '15', 'Y', '125');


INSERT INTO `KETTLE_MASTER_DEV`.`COUPON_DETAIL_DATA` ( `OFFER_DETAIL_ID`, `COUPON_CODE`, `START_DATE`,
 `END_DATE`, `COUPON_REUSE`, `CUSTOMER_REUSE`, `MAX_USAGE`, `COUPON_STATUS`, `USAGE_COUNT`, `MANUAL_OVERRIDE`)
 VALUES ( (SELECT OFFER_DETAIL_ID from KETTLE_MASTER_DEV.OFFER_DETAIL_DATA where OFFER_DESCRIPTION = 'VADA PAV & CHAI @ 125'),
 'VADCHAI', '2017-09-19', '2017-09-19', 'Y', 'Y', '50000', 'ACTIVE', '0', 'N');
