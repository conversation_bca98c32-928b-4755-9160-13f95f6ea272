CREATE TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_MAPPING(
  ID INT PRIMARY KEY AUTO_INCREMENT,
  UNIT_ID INT NOT NULL,
  CHANNEL_PARTNER_ID INT NOT NULL,
  STATUS VARCHAR(10) NOT NULL DEFAULT 'IN_ACTIVE'
);

INSERT INTO KETTLE_DEV.UNIT_CHANNEL_PARTNER_MAPPING (`UNIT_ID`, `CHANNEL_PARTNER_ID`, `STATUS`)
(SELECT UNIT_ID, 6, 'IN_ACTIVE' FROM KETTLE_MASTER_DEV.UNIT_DETAIL WHERE UNIT_CATEGORY = 'CAFE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (ACL_MODULE,ACL_MODULE_DESCRIPTION,ACL_STATUS,APPLICATION_NAME)
VALUES ('kettle-service.pos-metadata.unit-channel-partner','Unit channel partner mapping access', 'ACTIVE', 'KETTLE_SERVICE'),
('kettle-crm.cod-customer.lookup','cod customer lookup access', 'ACTIVE', 'KETTLE_CRM');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID, PERMISSION, ACL_ID, PPM_STATUS) VALUES
(10,1111,(SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE = 'kettle-service.pos-metadata.unit-channel-partner'), 'ACTIVE'),
(10,1111,(SELECT ACL_ID FROM KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA WHERE ACL_MODULE = 'kettle-crm.cod-customer.lookup'), 'ACTIVE');