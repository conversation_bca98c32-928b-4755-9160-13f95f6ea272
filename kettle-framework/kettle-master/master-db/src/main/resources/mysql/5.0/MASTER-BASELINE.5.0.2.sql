ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
ADD COLUMN OFFER_LAUNCH_START_DATE DATE NULL;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
ADD COLUMN OFFER_LAUNCH_END_DATE DATE NULL;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
ADD COLUMN OFFER_LAUNCH_STRATEGY VARCHAR(100) NULL;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
ADD COLUMN OFFER_LAUNCH_MESSAGE VARCHAR(500) NULL;

CREATE INDEX OFFER_DETAIL_DATA_OFFER_LAUNCH_START_DATE ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(OFFER_LAUNCH_START_DATE) USING BTREE;
CREATE INDEX OFFER_DETAIL_DATA_OFFER_LAUNCH_END_DATE ON KET<PERSON>E_MASTER_DEV.OFFER_DETAIL_DATA(OFFER_LAUNCH_END_DATE) USING BTREE;
CREATE INDEX OFFER_DETAIL_DATA_OFFER_LAUNCH_STRATEGY ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(OFFER_LAUNCH_STRATEGY) USING BTREE;

CREATE TABLE KETTLE_MASTER_DEV.CUSTOMER_OFFER_MAPPING_DATA(
CUSTOMER_OFFER_MAPPING_DATA_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
COUPON_DETAIL_ID INTEGER NOT NULL,
CUSTOMER_NAME VARCHAR(100) NULL,
CONTACT_NUMBER VARCHAR(50) NULL,
UNIT_ID INTEGER NULL,
COUPON_CODE VARCHAR(20) NULL,
OFFER_DETAIL_ID INTEGER NOT NULL,
CREATION_TIME TIMESTAMP NOT NULL
);

CREATE INDEX CUSTOMER_OFFER_MAPPING_DATA_CREATION_TIME ON KETTLE_MASTER_DEV.CUSTOMER_OFFER_MAPPING_DATA(CREATION_TIME) USING BTREE;
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_OFFER_MAPPING_DATA ADD COLUMN IS_NOTIFIED VARCHAR(1) NULL ;
CREATE INDEX CUSTOMER_OFFER_MAPPING_DATA_IS_NOTIFIED ON KETTLE_MASTER_DEV.CUSTOMER_OFFER_MAPPING_DATA(IS_NOTIFIED) USING BTREE;
