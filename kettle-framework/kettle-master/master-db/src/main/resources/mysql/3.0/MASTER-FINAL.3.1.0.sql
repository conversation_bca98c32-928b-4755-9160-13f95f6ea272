UPDATE KETTLE_MASTER_UAT.UNIT_DETAIL SET GSTIN='07AARCS3853M1ZL' Where UNIT_ID in (10006,
10007	,
10008	,
10012	,
10013	,
12006	,
12007	,
12008	,
12009	,
12010	,
12011	,
12013	,
12015	,
12016	,
12019	,
12028	,
22001	,
24001	,
26001	,
26003	,
26005	,
26008	,
26012	,
26014	,
26017	,
26018	,
26020	,
26026	,
26027
);
UPDATE KETTLE_MASTER_UAT.UNIT_DETAIL SET GSTIN='09AARCS3853M1ZH' Where UNIT_ID in (10002	,
10004	,
12005	,
12012	,
12018	,
12020	,
12021	,
26010	,
26013	,
26016	,
26019	,
26025	
);

UPDATE KETTLE_MASTER_UAT.UNIT_DETAIL SET GSTIN='27AARCS3853M1ZJ' Where UNIT_ID in (10009	,
10010	,
12022	,
12023	,
12027	,
12031	,
22002	,
24002	,
26002	,
26004	,
26009	,
26021	,
26022	,
26028	,
26029		
);
UPDATE KETTLE_MASTER_UAT.UNIT_DETAIL SET GSTIN='06AARCS3853M1ZN' Where UNIT_ID in (10000	,
10001	,
10003	,
10005	,
10011	,
10014	,
11001	,
12001	,
12002	,
12003	,
12004	,
12014	,
12017	,
12029	,
12030	,
26006	,
26007	,
26011	,
26015			
);

UPDATE KETTLE_MASTER_UAT.UNIT_DETAIL SET GSTIN='06AARCS3853M1ZN' Where UNIT_ID in (26023	,
26024			
);

UPDATE `KETTLE_MASTER_UAT`.`TAX_CATEGORY_DATA` SET `CATEGORY_CODE`='00009963' WHERE `TAX_CATEGORY_DATA_ID`='208';
UPDATE `KETTLE_MASTER_UAT`.`TAX_CATEGORY_DATA` SET `CATEGORY_CODE`='00009963_22021010' WHERE `TAX_CATEGORY_DATA_ID`='256';
UPDATE `KETTLE_MASTER_UAT`.`TAX_CATEGORY_DATA` SET `CATEGORY_CODE`='00009963_22021020' WHERE `TAX_CATEGORY_DATA_ID`='257';
UPDATE `KETTLE_MASTER_UAT`.`TAX_CATEGORY_DATA` SET `CATEGORY_DESCRIPTION`='Food and beverages services', `CATEGORY_INTERNAL_DESCRIPTION`='Food and beverages services' WHERE `TAX_CATEGORY_DATA_ID`='208';
UPDATE `KETTLE_MASTER_UAT`.`TAX_CATEGORY_DATA` SET `CATEGORY_DESCRIPTION`='Food and beverages services with cess1 peach iced tea passion fruit iced tea' , `CATEGORY_INTERNAL_DESCRIPTION`='Food and beverages services with cess1 peach iced tea passion fruit iced tea' WHERE `TAX_CATEGORY_DATA_ID`='256';
UPDATE `KETTLE_MASTER_UAT`.`TAX_CATEGORY_DATA` SET `CATEGORY_DESCRIPTION`='Food and beverages services with cess1 Mint Lemonade Modinagar Shikanji Fresh Lime' , `CATEGORY_INTERNAL_DESCRIPTION`='Food and beverages services with cess1 Mint Lemonade Modinagar Shikanji Fresh Lime' WHERE `TAX_CATEGORY_DATA_ID`='257';

UPDATE KETTLE_MASTER_UAT.PRODUCT_DETAIL SET TAX_CODE = '00009963' WHERE TAX_CODE = '00441067';
UPDATE KETTLE_MASTER_UAT.PRODUCT_DETAIL SET TAX_CODE = '00009963_22021010' WHERE TAX_CODE = '00441067_22021010';
UPDATE KETTLE_MASTER_UAT.PRODUCT_DETAIL SET TAX_CODE = '00009963_22021020' WHERE TAX_CODE = '00441067_22021020';


ALTER TABLE TAX_CATEGORY_DATA ADD CONSTRAINT UNIQUE(CATEGORY_CODE);


ALTER TABLE KETTLE_MASTER_UAT.PRODUCT_RECIPE_COST
ADD COLUMN PRODUCT_SOURCE VARCHAR(25) NULL;
;
UPDATE KETTLE_MASTER_UAT.PRODUCT_RECIPE_COST prc, KETTLE_MASTER_UAT.PRODUCT_DETAIL pd
SET PRODUCT_SOURCE = pd.PRODUCT_CLASSIFICATION
where pd.PRODUCT_ID =  prc.PRODUCT_ID
;

ALTER TABLE KETTLE_MASTER_UAT.STATE_DETAIL
ADD COLUMN FUNCTIONAL_FLAG VARCHAR(1) NULL;

UPDATE KETTLE_MASTER_UAT.STATE_DETAIL
SET FUNCTIONAL_FLAG = 'N';

UPDATE `KETTLE_MASTER_UAT`.`STATE_DETAIL` SET `FUNCTIONAL_FLAG`='Y' WHERE STATE IN ('Haryana','Maharashtra','Uttar Pradesh','Chandigarh','Delhi');

ALTER TABLE KETTLE_MASTER_UAT.LOCATION_DETAIL
ADD COLUMN FUNCTIONAL_FLAG VARCHAR(1) NULL;

UPDATE KETTLE_MASTER_UAT.LOCATION_DETAIL
SET FUNCTIONAL_FLAG = 'N';

UPDATE `KETTLE_MASTER_UAT`.`LOCATION_DETAIL` SET `FUNCTIONAL_FLAG`='Y'
 WHERE CITY IN ('Mumbai','Ghaziabad','Chandigarh','Gurgaon','Noida','New Delhi' );

DROP TABLE IF EXISTS KETTLE_MASTER_UAT.APPLICATION_DATA;
CREATE TABLE KETTLE_MASTER_UAT.APPLICATION_DATA(
APPLICATION_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
APPLICATION_NAME VARCHAR(50) NOT NULL,
APPLICATION_DESCRIPTION VARCHAR(500) NOT NULL,
APPLICATION_STATUS VARCHAR(15) NOT NULL
);

CREATE UNIQUE INDEX APPLICATION_DATA_APPLICATION_NAME ON KETTLE_MASTER_UAT.APPLICATION_DATA(APPLICATION_NAME);

DROP TABLE IF EXISTS KETTLE_MASTER_UAT.USER_ROLE_DATA;

CREATE TABLE KETTLE_MASTER_UAT.USER_ROLE_DATA(
ROLE_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ROLE_NAME VARCHAR(50) NOT NULL,
ROLE_DESCRIPTION VARCHAR(500) NOT NULL,
ROLE_STATUS VARCHAR(15) NOT NULL
);

CREATE UNIQUE INDEX USER_ROLE_DATA_ROLE_NAME ON KETTLE_MASTER_UAT.USER_ROLE_DATA(ROLE_NAME);
DROP TABLE IF EXISTS KETTLE_MASTER_UAT.EMPLOYEE_ROLE_MAPPING;

CREATE TABLE KETTLE_MASTER_UAT.EMPLOYEE_ROLE_MAPPING(
EMPLOYEE_ROLE_MAPPING_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
EMPLOYEE_ID INTEGER NOT NULL,
ROLE_ID INTEGER NOT NULL,
MAPPING_STATUS VARCHAR(15) NOT NULL,
UPDATED_BY INTEGER NULL,
LAST_UPDATE_TIME TIMESTAMP NOT NULL
);

CREATE INDEX EMPLOYEE_ROLE_MAPPING_EMPLOYEE_ID ON KETTLE_MASTER_UAT.EMPLOYEE_ROLE_MAPPING(EMPLOYEE_ID) USING BTREE;
CREATE INDEX EMPLOYEE_ROLE_MAPPING_ROLE_ID ON KETTLE_MASTER_UAT.EMPLOYEE_ROLE_MAPPING(ROLE_ID) USING BTREE;
CREATE INDEX EMPLOYEE_ROLE_MAPPING_MAPPING_STATUS ON KETTLE_MASTER_UAT.EMPLOYEE_ROLE_MAPPING(MAPPING_STATUS) USING BTREE;

CREATE UNIQUE INDEX EMPLOYEE_ROLE_MAPPING_UNIQUE_INDEX ON KETTLE_MASTER_UAT.EMPLOYEE_ROLE_MAPPING(EMPLOYEE_ID,ROLE_ID,MAPPING_STATUS);
;
DROP TABLE IF EXISTS KETTLE_MASTER_UAT.ACTION_DETAIL;
CREATE TABLE KETTLE_MASTER_UAT.ACTION_DETAIL(
ACTION_DETAIL_ID  INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ACTION_CODE VARCHAR(20) NOT NULL,
APPLICATION_ID INTEGER NOT NULL,
ACTION_TYPE VARCHAR(10) NOT NULL,
ACTION_CATEGORY VARCHAR(15) NOT NULL,
ACTION_DESCRIPTION VARCHAR(200) NOT NULL,
ACTION_STATUS VARCHAR(15) NOT NULL);

CREATE INDEX ACTION_DETAIL_APPLICATION_ID ON KETTLE_MASTER_UAT.ACTION_DETAIL(APPLICATION_ID) USING BTREE;
CREATE INDEX ACTION_DETAIL_ACTION_TYPE ON KETTLE_MASTER_UAT.ACTION_DETAIL(ACTION_TYPE) USING BTREE;
CREATE INDEX ACTION_DETAIL_ACTION_STATUS ON KETTLE_MASTER_UAT.ACTION_DETAIL(ACTION_STATUS) USING BTREE;
CREATE UNIQUE INDEX ACTION_DETAIL_ACTION_CODE ON KETTLE_MASTER_UAT.ACTION_DETAIL(ACTION_CODE);

DROP TABLE IF EXISTS KETTLE_MASTER_UAT.ROLE_ACTION_MAPPING;

CREATE TABLE KETTLE_MASTER_UAT.ROLE_ACTION_MAPPING(
ROLE_ACTION_MAPPING_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ROLE_ID INTEGER NOT NULL,
ACTION_DETAIL_ID INTEGER NOT NULL,
MAPPING_STATUS VARCHAR(15) NOT NULL,
UPDATED_BY INTEGER NULL,
LAST_UPDATE_TIME TIMESTAMP NOT NULL
);

CREATE INDEX ROLE_ACTION_MAPPING_ROLE_ID ON KETTLE_MASTER_UAT.ROLE_ACTION_MAPPING(ROLE_ID) USING BTREE;
CREATE INDEX ROLE_ACTION_MAPPING_ACTION_DETAIL_ID ON KETTLE_MASTER_UAT.ROLE_ACTION_MAPPING(ACTION_DETAIL_ID) USING BTREE;
CREATE INDEX ROLE_ACTION_MAPPING_MAPPING_STATUS ON KETTLE_MASTER_UAT.ROLE_ACTION_MAPPING(MAPPING_STATUS) USING BTREE;
CREATE UNIQUE INDEX ROLE_ACTION_MAPPING_UNIQUE_INDEX ON KETTLE_MASTER_UAT.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS);

INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (1,'KETTLE_SERVICE','KETTLE_SERVICE','ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (2,'NOAH_SERVICE', 'NOAH_SERVICE', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (3,'NEO_SERVICE', 'NEO_SERVICE', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (4,'MASTER_SERVICE', 'MASTER_SERVICE', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (5,'KETTLE_ADMIN', 'KETTLE_ADMIN', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (6,'KETTLE_CRM', 'KETTLE_ADMIN', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (7,'SCM_SERVICE', 'SCM_SERVICE', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (8,'KETTLE_ANALYTICS', 'KETTLE_ANALYTICS', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (9,'KETTLE_CHECKLIST', 'KETTLE_CHECKLIST', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`APPLICATION_DATA` (`APPLICATION_ID`, `APPLICATION_NAME`, `APPLICATION_DESCRIPTION`, `APPLICATION_STATUS`) VALUES (10,'WORKSTATION', 'WORKSTATION', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_UAT`.`USER_ROLE_DATA` (`ROLE_ID`, `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES (1,'SCM_HEAD', 'SCM_HEAD', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`USER_ROLE_DATA` (`ROLE_ID`, `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES (2,'SCM_PLANNER', 'SCM_PLANNER', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`USER_ROLE_DATA` (`ROLE_ID`, `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES (3,'SCM_UNIT_MANAGER', 'SCM_UNIT_MANAGER', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`USER_ROLE_DATA` (`ROLE_ID`, `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES (4,'CAFE_UNIT_MANAGER', 'CAFE_UNIT_MANAGER', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`USER_ROLE_DATA` (`ROLE_ID`, `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES (5,'PURCHASER', 'PURCHASER', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`USER_ROLE_DATA` (`ROLE_ID`, `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES (6,'FINANCE_HEAD', 'FINANCE_HEAD', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`USER_ROLE_DATA` (`ROLE_ID`, `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES (7,'FINANCE_MANAGER', 'FINANCE_MANAGER', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_UAT`.`USER_ROLE_DATA` (`ROLE_ID`, `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES (8,'FINANCE_EXECUTIVE', 'FINANCE_EXECUTIVE', 'ACTIVE');

INSERT INTO KETTLE_MASTER_UAT.ACTION_DETAIL(APPLICATION_ID,ACTION_TYPE,ACTION_CATEGORY,ACTION_CODE,ACTION_DESCRIPTION,ACTION_STATUS) VALUES
(7,'MENU','SHOW','SPM','SuMo -> Product Management -> Menu -> SPM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SPMANP','SuMo -> Product Management New Product -> SPMANP -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SPMANS','SuMo -> Product Management New SKU -> SPMANS -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SPMPL','SuMo -> Product Management -> Product List -> SPMPL -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SPMSL','SuMo -> Product Management -> SKU List -> SPMSL -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SPMSPU','SuMo -> Product Management -> Sku Price Update -> SPMSPU -> SHOW','ACTIVE'),
(7,'ACTION','ADD','SPAAPD','SuMo -> Product Management New Product -> SPAAPD -> ADD','ACTIVE'),
(7,'ACTION','APPROVE','SPAAPP','SuMo -> Product Management New Product -> SPAAPP -> APPROVE','ACTIVE'),
(7,'ACTION','DELETE','SPADPE','SuMo -> Product Management New Product -> SPADPE -> DELETE','ACTIVE'),
(7,'ACTION','UPDATE','SPAUPP','SuMo -> Product Management New Product -> SPAUPP -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SPAVPI','SuMo -> Product Management New Product -> SPAVPI -> VIEW','ACTIVE'),
(7,'ACTION','ACTIVATE','SPAASC','SuMo -> Product Management New SKU -> SPAASC -> ACTIVATE','ACTIVE'),
(7,'ACTION','ADD','SPAASD','SuMo -> Product Management New SKU -> SPAASD -> ADD','ACTIVE'),
(7,'ACTION','APPROVE','SPAASP','SuMo -> Product Management New SKU -> SPAASP -> APPROVE','ACTIVE'),
(7,'ACTION','DELETE','SPADSE','SuMo -> Product Management New SKU -> SPADSE -> DELETE','ACTIVE'),
(7,'ACTION','UPDATE','SPAUSP','SuMo -> Product Management New SKU -> SPAUSP -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SPAVSI','SuMo -> Product Management New SKU -> SPAVSI -> VIEW','ACTIVE'),
(7,'ACTION','ACTIVATE','SPPAPC','SuMo -> Product Management -> Product List -> SPPAPC -> ACTIVATE','ACTIVE'),
(7,'ACTION','ADD','SPPAPD','SuMo -> Product Management -> Product List -> SPPAPD -> ADD','ACTIVE'),
(7,'ACTION','APPROVE','SPPAPP','SuMo -> Product Management -> Product List -> SPPAPP -> APPROVE','ACTIVE'),
(7,'ACTION','DELETE','SPPDPE','SuMo -> Product Management -> Product List -> SPPDPE -> DELETE','ACTIVE'),
(7,'ACTION','UPDATE','SPPUPP','SuMo -> Product Management -> Product List -> SPPUPP -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SPPVPI','SuMo -> Product Management -> Product List -> SPPVPI -> VIEW','ACTIVE'),
(7,'ACTION','ACTIVATE','SPSACS','SuMo -> Product Management -> SKU List -> SPSACS -> ACTIVATE','ACTIVE'),
(7,'ACTION','ADD','SPSASL','SuMo -> Product Management -> SKU List -> SPSASL -> ADD','ACTIVE'),
(7,'ACTION','APPROVE','SPSAPS','SuMo -> Product Management -> SKU List -> SPSAPS -> APPROVE','ACTIVE'),
(7,'ACTION','DELETE','SPSDSL','SuMo -> Product Management -> SKU List -> SPSDSL -> DELETE','ACTIVE'),
(7,'ACTION','UPDATE','SPSUSL','SuMo -> Product Management -> SKU List -> SPSUSL -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SPSVSL','SuMo -> Product Management -> SKU List -> SPSVSL -> VIEW','ACTIVE'),
(7,'ACTION','ACTIVATE','SPSASC','SuMo -> Product Management -> Sku Price Update -> SPSASC -> ACTIVATE','ACTIVE'),
(7,'ACTION','ADD','SPSASD','SuMo -> Product Management -> Sku Price Update -> SPSASD -> ADD','ACTIVE'),
(7,'ACTION','APPROVE','SPSASP','SuMo -> Product Management -> Sku Price Update -> SPSASP -> APPROVE','ACTIVE'),
(7,'ACTION','DELETE','SPSDSE','SuMo -> Product Management -> Sku Price Update -> SPSDSE -> DELETE','ACTIVE'),
(7,'ACTION','UPDATE','SPSUSP','SuMo -> Product Management -> Sku Price Update -> SPSUSP -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SPSVSI','SuMo -> Product Management -> Sku Price Update -> SPSVSI -> VIEW','ACTIVE'),
(7,'ACTION','ACTIVATE','SPAAPC','SuMo -> Manage Attributes New Product -> SPAAPC -> ACTIVATE','ACTIVE'),
(7,'MENU','SHOW','MPMMS','SuMo -> Manage Packaging Mappings -> Menu -> MPMMS -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','MPMPMS','SuMo -> Manage Packaging Mappings -> Product Mapping -> MPMPMS -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','MPMSMS','SuMo -> Manage Packaging Mappings -> SKU Mapping -> MPMSMS -> SHOW','ACTIVE'),
(7,'ACTION','VIEW','MPMPMV','SuMo -> Manage Packaging Mappings -> Product Mapping -> MPMPMV -> VIEW','ACTIVE'),
(7,'ACTION','ADD','MPMPMA','SuMo -> Manage Packaging Mappings -> Product Mapping -> MPMPMA -> ADD','ACTIVE'),
(7,'ACTION','ACTIVATE','MPMPMAC','SuMo -> Manage Packaging Mappings -> Product Mapping -> MPMPMAC -> ACTIVATE','ACTIVE'),
(7,'ACTION','DEACTIVATE','MPMPMDC','SuMo -> Manage Packaging Mappings -> Product Mapping -> MPMPMDC -> DEACTIVATE','ACTIVE'),
(7,'ACTION','VIEW','MPMSMV','SuMo -> Manage Packaging Mappings -> SKU Mapping -> MPMSMV -> VIEW','ACTIVE'),
(7,'ACTION','ADD','MPMSMA','SuMo -> Manage Packaging Mappings -> SKU Mapping -> MPMSMA -> ADD','ACTIVE'),
(7,'ACTION','ACTIVATE','MPMSMAC','SuMo -> Manage Packaging Mappings -> SKU Mapping -> MPMSMAC -> ACTIVATE','ACTIVE'),
(7,'ACTION','DEACTIVATE','MPMSMDC','SuMo -> Manage Packaging Mappings -> SKU Mapping -> MPMSMDC -> DEACTIVATE','ACTIVE'),
(7,'MENU','SHOW','SVMMSH','SuMo -> Vendor Management -> Menu -> SVMMSH -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SVMVSH','SuMo -> Vendor Management -> Vendors -> SVMVSH -> SHOW','ACTIVE'),
(7,'ACTION','VIEW','SVMVVI','SuMo -> Vendor Management -> Vendors -> SVMVVI -> VIEW','ACTIVE'),
(7,'ACTION','EDIT','SVMVED','SuMo -> Vendor Management -> Vendors -> SVMVED -> EDIT','ACTIVE'),
(7,'ACTION','APPROVE','SVMVAP','SuMo -> Vendor Management -> Vendors -> SVMVAP -> APPROVE','ACTIVE'),
(7,'SUBMENU','SHOW','SVMVRS','SuMo -> Vendor Management -> Vendor Requests -> SVMVRS -> SHOW','ACTIVE'),
(7,'ACTION','ADD','SVMVRA','SuMo -> Vendor Management -> Vendor Requests -> SVMVRA -> ADD','ACTIVE'),
(7,'ACTION','CANCEL','SVMVRC','SuMo -> Vendor Management -> Vendor Requests -> SVMVRC -> CANCEL','ACTIVE'),
(7,'MENU','SHOW','SMMMSH','SuMo -> Manage Mappings -> Menu -> SMMMSH -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SMMUSSH','SuMo -> Manage Mappings -> Unit To Sku -> SMMUSSH -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SMMVSSH','SuMo -> Manage Mappings -> Vendor To Sku -> SMMVSSH -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SMVUSSH','SuMo -> Manage Mappings -> Vendor To Unit To Sku -> SMVUSSH -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','SMVUSPU','SuMo -> Manage Mappings -> Vendor To Unit To Sku Price Update -> SMVUSPU -> SHOW','ACTIVE'),
(7,'ACTION','ADD','SMMUSAD','SuMo -> Manage Mappings -> Unit To Sku -> SMMUSAD -> ADD','ACTIVE'),
(7,'ACTION','UPDATE','SMMUSUP','SuMo -> Manage Mappings -> Unit To Sku -> SMMUSUP -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SMMUSVI','SuMo -> Manage Mappings -> Unit To Sku -> SMMUSVI -> VIEW','ACTIVE'),
(7,'ACTION','ADD','SMMVSAD','SuMo -> Manage Mappings -> Vendor To Sku -> SMMVSAD -> ADD','ACTIVE'),
(7,'ACTION','UPDATE','SMMVSUP','SuMo -> Manage Mappings -> Vendor To Sku -> SMMVSUP -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SMMVSVI','SuMo -> Manage Mappings -> Vendor To Sku -> SMMVSVI -> VIEW','ACTIVE'),
(7,'ACTION','ADD','SMVUSAD','SuMo -> Manage Mappings -> Vendor To Unit To Sku -> SMVUSAD -> ADD','ACTIVE'),
(7,'ACTION','UPDATE','SMVUSUP','SuMo -> Manage Mappings -> Vendor To Unit To Sku -> SMVUSUP -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SMVUSVI','SuMo -> Manage Mappings -> Vendor To Unit To Sku -> SMVUSVI -> VIEW','ACTIVE'),
(7,'ACTION','ADD','SMVUSPAD','SuMo -> Manage Mappings -> Vendor To Unit To Sku Price Update -> SMVUSPAD -> ADD','ACTIVE'),
(7,'ACTION','UPDATE','SMVUSPUP','SuMo -> Manage Mappings -> Vendor To Unit To Sku Price Update -> SMVUSPUP -> UPDATE','ACTIVE'),
(7,'ACTION','VIEW','SMVUSPVI','SuMo -> Manage Mappings -> Vendor To Unit To Sku Price Update -> SMVUSPVI -> VIEW','ACTIVE'),
(7,'SUBMENU','SHOW','SMUVMCSH','SuMo -> Manage Mappings -> Unit To Vendor Mapping for Cafes -> SMUVMCSH -> SHOW','ACTIVE'),
(7,'SUBMENU','ACTIVATE','SMUVMCAC','SuMo -> Manage Mappings -> Unit To Vendor Mapping for Cafes -> SMUVMCAC -> ACTIVATE','ACTIVE'),
(7,'SUBMENU','DEACTIVATE','SMUVMCDA','SuMo -> Manage Mappings -> Unit To Vendor Mapping for Cafes -> SMUVMCDA -> DEACTIVATE','ACTIVE'),
(7,'SUBMENU','UPDATE','SMUVMCUP','SuMo -> Manage Mappings -> Unit To Vendor Mapping for Cafes -> SMUVMCUP -> UPDATE','ACTIVE'),
(7,'SUBMENU','VIEW','SMUVMCVI','SuMo -> Manage Mappings -> Unit To Vendor Mapping for Cafes -> SMUVMCVI -> VIEW','ACTIVE'),
(7,'SUBMENU','ADD','SMUVMCAD','SuMo -> Manage Mappings -> Unit To Vendor Mapping for Cafes -> SMUVMCAD -> ADD','ACTIVE'),
(7,'MENU','SHOW','ARM','SuMo -> Admin Reports -> Menu -> ARM -> ARM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','ARSOR','SuMo -> Admin Reports -> Specialized Ordering Report -> ARSOR -> SHOW','ACTIVE'),
(7,'ACTION','VIEW','ADSORV','SuMo -> Admin Reports -> Specialized Ordering Report -> ADSORV -> VIEW','ACTIVE'),
(7,'MENU','SHOW','AWM','SuMo Wastage -> Menu -> AWM -> SHOW','ACTIVE'),
(7,'ACTION','VIEW','AWVWV','SuMo Wastage Wastage -> AWVWV -> VIEW','ACTIVE'),
(7,'ACTION','ADD','AWVWA','SuMo Wastage Wastage -> AWVWA -> ADD','ACTIVE'),
(7,'ACTION','CANCEL','AWVWC','SuMo Wastage Wastage -> AWVWC -> CANCEL','ACTIVE'),
(7,'MENU','SHOW','DCM','SuMo -> Day Close -> Menu -> DCM -> SHOW','ACTIVE'),
(7,'ACTION','ADD','DCDCA','SuMo -> Day Close -> Day Close -> DCDCA -> ADD','ACTIVE'),
(7,'MENU','SHOW','CIM','SuMo -> Current Inventory -> Menu -> CIM -> SHOW','ACTIVE'),
(7,'MENU','SHOW','MTM','SuMo -> Manage Transactions -> Menu -> MTM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','MTRFOM','SuMo -> Manage Transactions -> Reference Order Management -> MTRFOM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','MTRQOM','SuMo -> Manage Transactions -> Request Order Management -> MTRQOM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','MTTOM','SuMo -> Manage Transactions -> Transfer Order Management -> MTTOM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','MTGRM','SuMo -> Manage Transactions -> Goods Received Management -> MTGRM -> SHOW','ACTIVE'),
(7,'ACTION','VIEW','MTRFOMV','SuMo -> Manage Transactions -> Reference Order Management -> MTRFOMV -> VIEW','ACTIVE'),
(7,'ACTION','VIEW','MTRQOMV','SuMo -> Manage Transactions -> Request Order Management -> MTRQOMV -> VIEW','ACTIVE'),
(7,'ACTION','CANCEL','MTRQOMC','SuMo -> Manage Transactions -> Request Order Management -> MTRQOMC -> CANCEL','ACTIVE'),
(7,'ACTION','VIEW','MTTOMV','SuMo -> Manage Transactions -> Transfer Order Management -> MTTOMV -> VIEW','ACTIVE'),
(7,'ACTION','CANCEL','MTTOMC','SuMo -> Manage Transactions -> Transfer Order Management -> MTTOMC -> CANCEL','ACTIVE'),
(7,'ACTION','VIEW','MTGRMV','SuMo -> Manage Transactions -> Goods Received Management -> MTGRMV -> VIEW','ACTIVE'),
(7,'MENU','SHOW','RECM','SuMo -> Receiving -> Menu -> RECM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','RECPR','SuMo -> Receiving -> Pending Receiving -> RECPR -> SHOW','ACTIVE'),
(7,'ACTION','VIEW','RECPRV','SuMo -> Receiving -> Pending Receiving -> RECPRV -> VIEW','ACTIVE'),
(7,'ACTION','REJECT','RECPRR','SuMo -> Receiving -> Pending Receiving -> RECPRR -> REJECT','ACTIVE'),
(7,'ACTION','ADD','RECPRA','SuMo -> Receiving -> Pending Receiving -> RECPRA -> ADD','ACTIVE'),
(7,'MENU','SHOW','TRNM','SuMo -> Transfers -> Menu -> TRNM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','TRNCT','SuMo -> Transfers -> Create Transfer -> TRNCT -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','TRNST','SuMo -> Transfers -> Standalone Transfer -> TRNST -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','TRNAO','SuMo -> Transfers -> Acknowledge Orders -> TRNAO -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','TRNPP','SuMo -> Transfers -> Production Planning -> TRNPP -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','TRNPH','SuMo -> Transfers -> Production History -> TRNPH -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','TRNPB','SuMo -> Transfers -> Production Booking -> TRNPB -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','TRNBH','SuMo -> Transfers -> Booking History -> TRNBH -> SHOW','ACTIVE'),
(7,'ACTION','VIEW','TRNCTV','SuMo -> Transfers -> Create Transfer -> TRNCTV -> VIEW','ACTIVE'),
(7,'ACTION','ADD','TRNCTA','SuMo -> Transfers -> Create Transfer -> TRNCTA -> ADD','ACTIVE'),
(7,'ACTION','ADD','TRNSTA','SuMo -> Transfers -> Standalone Transfer -> TRNSTA -> ADD','ACTIVE'),
(7,'ACTION','VIEW','TRNAOV','SuMo -> Transfers -> Acknowledge Orders -> TRNAOV -> VIEW','ACTIVE'),
(7,'ACTION','ADD','TRNAOA','SuMo -> Transfers -> Acknowledge Orders -> TRNAOA -> ADD','ACTIVE'),
(7,'ACTION','VIEW','TRNPPV','SuMo -> Transfers -> Production Planning -> TRNPPV -> VIEW','ACTIVE'),
(7,'ACTION','ADD','TRNPPA','SuMo -> Transfers -> Production Planning -> TRNPPA -> ADD','ACTIVE'),
(7,'ACTION','VIEW','TRNPHV','SuMo -> Transfers -> Production History -> TRNPHV -> VIEW','ACTIVE'),
(7,'ACTION','CANCEL','TRNPHC','SuMo -> Transfers -> Production History -> TRNPHC -> CANCEL','ACTIVE'),
(7,'ACTION','VIEW','TRNPBV','SuMo -> Transfers -> Production Booking -> TRNPBV -> VIEW','ACTIVE'),
(7,'ACTION','ADD','TRNPBA','SuMo -> Transfers -> Production Booking -> TRNPBA -> ADD','ACTIVE'),
(7,'ACTION','CANCEL','TRNPBC','SuMo -> Transfers -> Production Booking -> TRNPBC -> CANCEL','ACTIVE'),
(7,'ACTION','VIEW','TRNBHV','SuMo -> Transfers -> Booking History -> TRNBHV -> VIEW','ACTIVE'),
(7,'ACTION','CANCEL','TRNBHC','SuMo -> Transfers -> Booking History -> TRNBHC -> CANCEL','ACTIVE'),
(7,'MENU','SHOW','ORDM','SuMo -> Ordering -> Menu -> ORDM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','ORDRO','SuMo -> Ordering -> Regular Ordering -> ORDRO -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','ORDAO','SuMo -> Ordering -> Adhoc Ordering -> ORDAO -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','ORDSO','SuMo -> Ordering -> Specialized Ordering -> ORDSO -> SHOW','ACTIVE'),
(7,'MENU','SHOW','VOM','SuMo -> Vendor Ordering -> Menu -> VOM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','VOMRPO','SuMo -> Vendor Ordering -> Raise PO -> VOMRPO -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','VOMAPO','SuMo -> Vendor Ordering PO -> VOMAPO -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','VOMVPO','SuMo -> Vendor Ordering PO -> VOMVPO -> SHOW','ACTIVE'),
(7,'ACTION','VIEW','VOMVPOV','SuMo -> Vendor Ordering PO -> VOMVPOV -> VIEW','ACTIVE'),
(7,'ACTION','CANCEL','VOMVPOC','SuMo -> Vendor Ordering PO -> VOMVPOC -> CANCEL','ACTIVE'),
(7,'ACTION','CLOSE','VOMVPOCL','SuMo -> Vendor Ordering PO -> VOMVPOCL -> CLOSE','ACTIVE'),
(7,'MENU','SHOW','VRECM','SuMo -> Vendor Receiving -> Menu -> VRECM -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','VRECCGR','SuMo -> Vendor Ordering -> Create GR -> VRECCGR -> SHOW','ACTIVE'),
(7,'SUBMENU','SHOW','VRECVGR','SuMo -> Vendor Ordering GR -> VRECVGR -> SHOW','ACTIVE'),
(7,'MENU','SHOW','UIM','SuMo Inventory -> Menu -> UIM -> SHOW','ACTIVE');

INSERT INTO ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)VALUES
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMANP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMANS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMPL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSPU'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAAPD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAAPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPADPE'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAUPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAVPI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAASC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAASD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAASP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPADSE'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAUSP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAVSI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPAPC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPAPD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPAPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPDPE'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPUPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPVPI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSACS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSASL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSAPS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSDSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSUSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSVSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSASC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSASD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSASP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSDSE'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSUSP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSVSI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAAPC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMMS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMAC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMDC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMAC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMDC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVED'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVAP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPU'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCAC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCDA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ARM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ARSOR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ADSORV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'DCM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'DCDCA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'CIM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRFOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTGRM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRFOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOMC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOMC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTGRMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCT'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNST'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPB'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCTV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCTA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNSTA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAOA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPPV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPPA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPHV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPHC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBHV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBHC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDRO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDAO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDSO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMRPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMAPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOCL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECCGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECVGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(1,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'UIM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMANP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMANS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMPL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSPU'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAAPD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAAPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPADPE'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAUPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAVPI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAASC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAASD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAASP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPADSE'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAUSP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPAVSI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPAPC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPAPD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPAPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPDPE'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPUPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPVPI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSACS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSASL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSAPS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSDSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSUSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSVSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSASC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSASD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSDSE'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSUSP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSVSI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMMS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMAC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMPMDC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMAC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MPMSMDC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVED'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPU'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCAC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCDA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCUP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCAD'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ARM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ARSOR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ADSORV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'CIM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRFOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTGRM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRFOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOMC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOMC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTGRMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCT'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNST'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPB'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCTV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCTA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNSTA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAOA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPPV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPPA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPHV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPHC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBHV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBHC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDRO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDAO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDSO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMRPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMAPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOCL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECCGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(2,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECVGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'DCM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'DCDCA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'CIM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRFOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTGRM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRFOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOMC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOMC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTGRMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCT'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNST'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPB'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCTV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCTA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNSTA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAOA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPPV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPPA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPHV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPHC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNPBC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBHV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNBHC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDRO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDAO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDSO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECCGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(3,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECVGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'AWVWC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRFOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTGRM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRFOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTRQOMC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTTOMC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'MTGRMV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'RECPRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCT'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNST'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCTV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNCTA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNSTA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'TRNAOA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDRO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDAO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'ORDSO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(4,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'UIM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPU'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMUSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMMVSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMVUSPVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SMUVMCVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'CIM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMRPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMAPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOCL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(5,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECVGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMPL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSPU'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPVPI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSVSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSASP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSVSI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVED'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVAP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'CIM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(6,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECVGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMPL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSPU'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPVPI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSVSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVED'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVAP'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'CIM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(7,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECVGR'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMPL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPMSPU'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPPVPI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SPSVSL'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMMSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVSH'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVVI'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRS'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRA'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'SVMVRC'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPO'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VOMVPOV'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECM'),'ACTIVE','100000', '2017-07-01 00:00:00'),
(8,(select ACTION_DETAIL_ID FROM KETTLE_MASTER_UAT.ACTION_DETAIL where ACTION_CODE = 'VRECVGR'),'ACTIVE','100000', '2017-07-01 00:00:00');


INSERT INTO `KETTLE_MASTER_UAT`.`EMPLOYEE_ROLE_MAPPING` (`EMPLOYEE_ID`, `ROLE_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('120503', '1', 'ACTIVE', '100000', '2017-07-01 00:00:00');
INSERT INTO `KETTLE_MASTER_UAT`.`EMPLOYEE_ROLE_MAPPING` (`EMPLOYEE_ID`, `ROLE_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('120053', '1', 'ACTIVE', '100000', '2017-07-01 00:00:00');


UPDATE `KETTLE_MASTER_UAT`.`UNIT_DETAIL` SET `GSTIN`='APPLIED FOR' WHERE `UNIT_ID`='26023';

update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '474.29' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (692));
update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '189.52' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (700,710));
update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '284.76' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (720,730));
update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '19.05' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (1003));
update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '16.95' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (1004,1005, 1006,1007));
update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '40.00' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (1044));
update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '83.90' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (1057));
update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '20.00' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (1047));
update KETTLE_MASTER_UAT.UNIT_PRODUCT_PRICING SET PRICE = '20.00' WHERE UNIT_PROD_REF_ID IN(select UNIT_PROD_REF_ID from KETTLE_MASTER_UAT.UNIT_PRODUCT_MAPPING WHERE PRODUCT_ID IN (860, 861));

DELETE FROM KETTLE_MASTER_UAT.CATEGORY_ADDITIONAL_TAX_DATA WHERE CATEGORY_ID = 208;

UPDATE `KETTLE_MASTER_UAT`.`UNIT_DETAIL` SET `GSTIN`='APPLIED FOR' WHERE `UNIT_ID`='26024';
UPDATE `KETTLE_MASTER_UAT`.`LOCATION_DETAIL` SET `CITY_CODE`='CHANDIGARH' WHERE `LOCATION_ID`='48';
UPDATE `KETTLE_MASTER_UAT`.`UNIT_DETAIL` SET `LOCATION_DETAIL_ID`='48' WHERE `UNIT_ID`='26024';
UPDATE `KETTLE_MASTER_UAT`.`UNIT_DETAIL` SET `LOCATION_DETAIL_ID`='48' WHERE `UNIT_ID`='26023';


update KETTLE_MASTER.UNIT_DETAIL set LOCATION_DETAIL_ID=185 where LOCATION_DETAIL_ID=2;

update KETTLE_SCM.ADDRESS_DETAIL_DATA set LOCATION_ID='NEW DELHI' where LOCATION_ID='DELHI' and ADDRESS_TYPE='DISPATCH_ADDRESS';


INSERT INTO `KETTLE_MASTER`.`EMPLOYEE_ROLE_MAPPING` (`EMPLOYEE_ID`, `ROLE_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
select EMP_ID, 4,'ACTIVE', 100000, '2017-07-01 00:00:00' from EMPLOYEE_DETAIL where
EMP_ID IN (100056,120454,120034,120720,120492,120109,120101,120003,100041,120643,120024,120043,120072,120063,120716,120747,120761,100050,120059,120060,120678,120097,120071,100002,120807,120421,100045,120132,120560,120661,120070,120600,100053,120107,120537,120436,120091,120683,120031,120125,120388,120506,100052,120055,120760,120598,120622,120423,120424,120133,120682,120422,120221,110004,120624,100031,120135,120016,100004,100039,120721,120007,120418,120463,120491,120656,120577,120705,120443,120082,120690,120458,120850,100057,120814,120249,120659,120666,120846,120067,120106,110015,120053,120502,110001,120054,120223,100013,120027,110017,120497,120058,120805,120126,120651,120536,120112,120113,100028,120460,120813,120836,120108,120104,120703,100035,120110,120498,110011,120658,120618,120420,120244,120777,120829,100040,120539,120050,120103,120663,120137,120487,120114,120801,120573,120083,120574,120096,120851,120787,100027,120210,120841,120576,120535,120033,120809,120080,120057,100011,120430,110020,120839,120503,120863,120898,120592,120279,120748,100012,120248,120085,120124,120465,100037,120032,120213,120428,120117,100007,120378,120074,120704,120844,120139,120707,120275,100000,100044,120902)
AND EMP_ID NOT IN (110004,
110011,
110015,
110017,
110020,
120082,
120083,
120418,
120573,
120080,
120839,
120117)
;
