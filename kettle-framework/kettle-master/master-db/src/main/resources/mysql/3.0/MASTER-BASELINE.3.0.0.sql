DROP TABLE IF EXISTS KETTLE_MASTER_DEV.COUNTRY_DETAIL;
CREATE TABLE KETTLE_MASTER_DEV.COUNTRY_DETAIL(
COUNTRY_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
COUNTRY VARCHAR(100),
COUNTRY_CODE VARCHAR(10),
COUNTRY_ISD_CODE VARCHAR(10),
COUNTRY_STATUS VARCHAR(15)
);

DROP TABLE IF EXISTS KETTLE_MASTER_DEV.STATE_DETAIL;
CREATE TABLE KETTLE_MASTER_DEV.STATE_DETAIL(
STATE_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
STATE VARCHAR(100),
STATE_CODE VARCHAR(10),
STATE_SHORT_CODE VARCHAR(10),
COUNTRY_DETAIL_ID INTEGER NOT NULL,
IS_UT VARCHAR(1) NOT NULL DEFAULT 'N',
STATE_STATUS VARCHAR(15)
);

DROP TABLE IF EXISTS KETTLE_MASTER_DEV.LOCATION_DETAIL;
CREATE TABLE KETTLE_MASTER_DEV.LOCATION_DETAIL(
LOCATION_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
CITY VARCHAR(100) NOT NULL,
CITY_CODE VARCHAR(10) NULL,
STATE_DETAIL_ID INTEGER NOT NULL,
COUNTRY_CODE VARCHAR(10) NULL,
LOCATION_STATUS VARCHAR(15)
);

DROP TABLE IF EXISTS KETTLE_MASTER_DEV.TAX_PROFILE_DATA;
CREATE TABLE KETTLE_MASTER_DEV.TAX_PROFILE_DATA (
    TAX_DATA_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    TAX_CODE VARCHAR(20) NOT NULL,
    TAX_NAME VARCHAR(50) NOT NULL,
    TAX_DATA_STATUS VARCHAR(15) NOT NULL,
    TAX_DESCRIPTION VARCHAR(1000) NOT NULL,
    TAX_INTERNAL_DESCRIPTION VARCHAR(1000) NOT NULL,
    APPLICABLE_ON VARCHAR(15) NOT NULL
);

DROP TABLE IF EXISTS KETTLE_MASTER_DEV.TAX_CATEGORY_DATA;
CREATE TABLE KETTLE_MASTER_DEV.TAX_CATEGORY_DATA (
    TAX_CATEGORY_DATA_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    CATEGORY_CODE VARCHAR(100) NOT NULL,
    CATEGORY_DESCRIPTION VARCHAR(1000) NOT NULL,
    CATEGORY_STATUS VARCHAR(15) NOT NULL,
    CATEGORY_INTERNAL_DESCRIPTION VARCHAR(1000) NOT NULL,
    IS_EXEMPTED VARCHAR(1) NOT NULL DEFAULT 'N'
);


DROP TABLE IF EXISTS KETTLE_MASTER_DEV.CATEGORY_TAX_DATA;
CREATE TABLE KETTLE_MASTER_DEV.CATEGORY_TAX_DATA (
    CATEGORY_TAX_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    CATEGORY_ID INTEGER NOT NULL,
    TAX_ID INTEGER NOT NULL,
    COUNTRY_DETAIL_ID INTEGER NOT NULL,
    STATE_DETAIL_ID INTEGER NOT NULL,
    IGST_TAX_RATE DECIMAL(10 , 2 ) NOT NULL,
    CGST_TAX_RATE DECIMAL(10 , 2 ) NOT NULL,
    SGST_TAX_RATE DECIMAL(10 , 2 ) NOT NULL,
    START_DATE DATE NOT NULL,
    TAX_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL
);
DROP TABLE IF EXISTS KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA;
CREATE TABLE KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA (
    CATEGORY_ADDITIONAL_TAX_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    CATEGORY_ID INTEGER NOT NULL,
    TAX_ID INTEGER NOT NULL,
    COUNTRY_DETAIL_ID INTEGER NOT NULL,
    STATE_DETAIL_ID INTEGER NOT NULL,
    TAX_RATE DECIMAL(10 , 2 ) NOT NULL,
    START_DATE DATE NOT NULL,
    TAX_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL
);

DROP TABLE IF EXISTS KETTLE_MASTER_DEV.CATEGORY_TAX_HISTORY;
CREATE TABLE KETTLE_MASTER_DEV.CATEGORY_TAX_HISTORY (
    CATEGORY_TAX_HISTORY_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    CATEGORY_TAX_DATA_ID INTEGER NOT NULL,
    START_DATE DATE NOT NULL,
    CURRENT_TAX_RATE DECIMAL(10 , 2 ) NOT NULL,
    NEGOTIATED_TAX_RATE DECIMAL(10 , 2 ) NOT NULL,
    RECORD_STATUS VARCHAR(50) NOT NULL,
    CHANGE_TYPE VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL
);


ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN LOCATION_CODE VARCHAR(15);

INSERT INTO `KETTLE_MASTER_DEV`.`TAX_PROFILE_DATA` (`TAX_DATA_ID`, `TAX_CODE`, `TAX_NAME`, `TAX_DATA_STATUS`, `TAX_DESCRIPTION`, `TAX_INTERNAL_DESCRIPTION`, `APPLICABLE_ON`) VALUES ('1', 'GST', 'GST', 'ACTIVE', 'GST', 'GST', 'ON_SALE');
INSERT INTO `KETTLE_MASTER_DEV`.`TAX_PROFILE_DATA` (`TAX_DATA_ID`, `TAX_CODE`, `TAX_NAME`, `TAX_DATA_STATUS`, `TAX_DESCRIPTION`, `TAX_INTERNAL_DESCRIPTION`, `APPLICABLE_ON`) VALUES ('2', 'CESS1', 'CESS1', 'ACTIVE', '22021020', '22021020_', 'ON_SALE');
