ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST
ADD COLUMN PRODUCT_SOURCE VARCHAR(25) NULL;
;
UPDATE KETTLE_MASTER_DEV.PRODUCT_RECIPE_COST prc, KETTLE_MASTER_DEV.PRODUCT_DETAIL pd
SET PRODUCT_SOURCE = pd.PRODUCT_CLASSIFICATION
where pd.PRODUCT_ID =  prc.PRODUCT_ID
;

ALTER TABLE KETTLE_MASTER_DEV.STATE_DETAIL
ADD COLUMN FUNCTIONAL_FLAG VARCHAR(1) NULL;

UPDATE KETTLE_MASTER_DEV.STATE_DETAIL
SET FUNCTIONAL_FLAG = 'N';

UPDATE `KETTLE_MASTER_DEV`.`STATE_DETAIL` SET `FUNCTIONAL_FLAG`='Y' WHERE STATE IN ('Haryana','Maharashtra','Uttar Pradesh','Chandigarh','Delhi');

ALTER TABLE KETTLE_MASTER_DEV.LOCATION_DETAIL
ADD COLUMN FUNCTIONAL_FLAG VARCHAR(1) NULL;

UPDATE KETTLE_MASTER_DEV.LOCATION_DETAIL
SET FUNCTIONAL_FLAG = 'N';

UPDATE `KETTLE_MASTER_DEV`.`LOCATION_DETAIL` SET `FUNCTIONAL_FLAG`='Y'
 WHERE CITY IN ('Mumbai','Ghaziabad','Chandigarh','Gurgaon','Noida','New Delhi' );

