ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN LOCATION_DETAIL_ID INTEGER NULL;


ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_DETAIL ADD COLUMN
TAX_CODE VARCHAR(20) NULL;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN GSTIN VARCHAR(20) NULL;


INSERT INTO COUNTRY_DETAIL VALUES (1,'India','IND','91','ACTIVE');
INSERT INTO STATE_DETAIL VALUES (1,'Andhra Pradesh','AP','AP',1,'N','ACTIVE'),(2,'Arunachal Pradesh','AR','AR',1,'N','ACTIVE'),(3,'Assam','AS','AS',1,'N','ACTIVE'),(4,'Bihar','BR','BR',1,'N','ACTIVE'),(5,'Chhattisgarh','CT','CT',1,'N','ACTIVE'),(6,'Goa','GA','GA',1,'N','ACTIVE'),(7,'Gujarat','GJ','GJ',1,'N','ACTIVE'),(8,'Haryana','HR','HR',1,'N','ACTIVE'),(9,'Himachal Pradesh','HP','HP',1,'N','ACTIVE'),(10,'Jammu and Kashmir','JK','JK',1,'N','ACTIVE'),(11,'Jharkhand','JH','JH',1,'N','ACTIVE'),(12,'Karnataka','KA','KA',1,'N','ACTIVE'),(13,'Kerala','KL','KL',1,'N','ACTIVE'),(14,'Madhya Pradesh','MP','MP',1,'N','ACTIVE'),(15,'Maharashtra','MH','MH',1,'N','ACTIVE'),(16,'Manipur','MN','MN',1,'N','ACTIVE'),(17,'Meghalaya','ML','ML',1,'N','ACTIVE'),(18,'Mizoram','MZ','MZ',1,'N','ACTIVE'),(19,'Nagaland','NL','NL',1,'N','ACTIVE'),(20,'Odisha','OR','OR',1,'N','ACTIVE'),(21,'Punjab','PB','PB',1,'N','ACTIVE'),(22,'Rajasthan','RJ','RJ',1,'N','ACTIVE'),(23,'Sikkim','SK','SK',1,'N','ACTIVE'),(24,'Tamil Nadu','TN','TN',1,'N','ACTIVE'),(25,'Telangana','TG','TG',1,'N','ACTIVE'),(26,'Tripura','TR','TR',1,'N','ACTIVE'),(27,'Uttarakhand','UT','UT',1,'N','ACTIVE'),(28,'Uttar Pradesh','UP','UP',1,'N','ACTIVE'),(29,'West Bengal','WB','WB',1,'N','ACTIVE'),(30,'Andaman and Nicobar Islands','AN','AN',1,'Y','ACTIVE'),(31,'Chandigarh','CH','CH',1,'Y','ACTIVE'),(32,'Delhi','DL','DL',1,'Y','ACTIVE'),(33,'Dadra and Nagar Haveli','DN','DN',1,'Y','ACTIVE'),(34,'Daman and Diu','DD','DD',1,'Y','ACTIVE'),(35,'Lakshadweep','LD','LD',1,'Y','ACTIVE'),(36,'Puducherry','PY','PY',1,'Y','ACTIVE');
INSERT INTO LOCATION_DETAIL VALUES (1,'Mumbai','MUMBAI',15,'IND','ACTIVE'),(2,'Delhi','DELHI',32,'IND','ACTIVE'),(3,'Bangalore','BANGALORE',12,'IND','ACTIVE'),(4,'Hyderabad','HYDERABAD',25,'IND','ACTIVE'),(5,'Ahmedabad','AHMEDABAD',7,'IND','ACTIVE'),(6,'Chennai','CHENNAI',24,'IND','ACTIVE'),(7,'Kolkata','KOLKATA',29,'IND','ACTIVE'),(8,'Surat','SURAT',7,'IND','ACTIVE'),(9,'Pune','PUNE',15,'IND','ACTIVE'),(10,'Jaipur','JAIPUR',22,'IND','ACTIVE'),(11,'Lucknow','LUCKNOW',28,'IND','ACTIVE'),(12,'Kanpur','KANPUR',28,'IND','ACTIVE'),(13,'Nagpur','NAGPUR',15,'IND','ACTIVE'),(14,'Visakhapatnam','VISAKHAPA',1,'IND','ACTIVE'),(15,'Indore','INDORE',14,'IND','ACTIVE'),(16,'Thane','THANE',15,'IND','ACTIVE'),(17,'Bhopal','BHOPAL',14,'IND','ACTIVE'),(18,'Pimpri-Chinchwad','PIMPRI-CH',15,'IND','ACTIVE'),(19,'Patna','PATNA',4,'IND','ACTIVE'),(20,'Vadodara','VADODARA',7,'IND','ACTIVE'),(21,'Ghaziabad','GHAZIABAD',28,'IND','ACTIVE'),(22,'Ludhiana','LUDHIANA',21,'IND','ACTIVE'),(23,'Coimbatore','COIMBATOR',24,'IND','ACTIVE'),(24,'Agra','AGRA',28,'IND','ACTIVE'),(25,'Madurai','MADURAI',24,'IND','ACTIVE'),(26,'Nashik','NASHIK',15,'IND','ACTIVE'),(27,'Faridabad','FARIDABAD',8,'IND','ACTIVE'),(28,'Meerut','MEERUT',28,'IND','ACTIVE'),(29,'Rajkot','RAJKOT',7,'IND','ACTIVE'),(30,'Kalyan-Dombivali','KALYAN-DO',15,'IND','ACTIVE'),(31,'Vasai-Virar','VASAI-VIR',15,'IND','ACTIVE'),(32,'Varanasi','VARANASI',28,'IND','ACTIVE'),(33,'Srinagar','SRINAGAR',10,'IND','ACTIVE'),(34,'Aurangabad','AURANGABA',15,'IND','ACTIVE'),(35,'Dhanbad','DHANBAD',11,'IND','ACTIVE'),(36,'Amritsar','AMRITSAR',21,'IND','ACTIVE'),(37,'Navi Mumbai','NAVI MUMB',15,'IND','ACTIVE'),(38,'Allahabad','ALLAHABAD',28,'IND','ACTIVE'),(39,'Ranchi','RANCHI',11,'IND','ACTIVE'),(40,'Howrah','HOWRAH',29,'IND','ACTIVE'),(41,'Jabalpur','JABALPUR',14,'IND','ACTIVE'),(42,'Gwalior','GWALIOR',14,'IND','ACTIVE'),(43,'Vijayawada','VIJAYAWAD',1,'IND','ACTIVE'),(44,'Jodhpur','JODHPUR',22,'IND','ACTIVE'),(45,'Raipur','RAIPUR',5,'IND','ACTIVE'),(46,'Kota','KOTA',22,'IND','ACTIVE'),(47,'Guwahati','GUWAHATI',3,'IND','ACTIVE'),(48,'Chandigarh','CHANDIGAR',31,'IND','ACTIVE'),(49,'Thiruvananthapuram','THIRUVANA',13,'IND','ACTIVE'),(50,'Solapur','SOLAPUR',15,'IND','ACTIVE'),(51,'Hubballi-Dharwad','HUBBALLI-',12,'IND','ACTIVE'),(52,'Tiruchirappalli','TIRUCHIRA',24,'IND','ACTIVE'),(53,'Bareilly','BAREILLY',28,'IND','ACTIVE'),(54,'Moradabad','MORADABAD',28,'IND','ACTIVE'),(55,'Mysore','MYSORE',12,'IND','ACTIVE'),(56,'Tiruppur','TIRUPPUR',24,'IND','ACTIVE'),(57,'Gurgaon','GURGAON',8,'IND','ACTIVE'),(58,'Aligarh','ALIGARH',28,'IND','ACTIVE'),(59,'Jalandhar','JALANDHAR',21,'IND','ACTIVE'),(60,'Bhubaneswar','BHUBANESW',20,'IND','ACTIVE'),(61,'Salem','SALEM',24,'IND','ACTIVE'),(62,'Mira-Bhayandar','MIRA-BHAY',15,'IND','ACTIVE'),(63,'Warangal','WARANGAL',25,'IND','ACTIVE'),(64,'Gunturs','GUNTURS',1,'IND','ACTIVE'),(65,'Bhiwandi','BHIWANDI',15,'IND','ACTIVE'),(66,'Saharanpur','SAHARANPU',28,'IND','ACTIVE'),(67,'Gorakhpur','GORAKHPUR',28,'IND','ACTIVE'),(68,'Bikaner','BIKANER',22,'IND','ACTIVE'),(69,'Amravati','AMRAVATI',15,'IND','ACTIVE'),(70,'Noida','NOIDA',28,'IND','ACTIVE'),(71,'Jamshedpur','JAMSHEDPU',11,'IND','ACTIVE'),(72,'Bhilai','BHILAI',5,'IND','ACTIVE'),(73,'Cuttack','CUTTACK',20,'IND','ACTIVE'),(74,'Firozabad','FIROZABAD',28,'IND','ACTIVE'),(75,'Kochi','KOCHI',13,'IND','ACTIVE'),(76,'Nellore','NELLORE',1,'IND','ACTIVE'),(77,'Bhavnagar','BHAVNAGAR',7,'IND','ACTIVE'),(78,'Dehradun','DEHRADUN',27,'IND','ACTIVE'),(79,'Durgapur','DURGAPUR',29,'IND','ACTIVE'),(80,'Asansol','ASANSOL',29,'IND','ACTIVE'),(81,'Rourkela','ROURKELA',20,'IND','ACTIVE'),(82,'Nanded','NANDED',15,'IND','ACTIVE'),(83,'Kolhapur','KOLHAPUR',15,'IND','ACTIVE'),(84,'Ajmer','AJMER',22,'IND','ACTIVE'),(85,'Gulbarga','GULBARGA',12,'IND','ACTIVE'),(86,'Jamnagar','JAMNAGAR',7,'IND','ACTIVE'),(87,'Ujjain','UJJAIN',14,'IND','ACTIVE'),(88,'Loni','LONI',28,'IND','ACTIVE'),(89,'Siliguri','SILIGURI',29,'IND','ACTIVE'),(90,'Jhansi','JHANSI',28,'IND','ACTIVE'),(91,'Ulhasnagar','ULHASNAGA',15,'IND','ACTIVE'),(92,'Jammu','JAMMU',10,'IND','ACTIVE'),(93,'Sangli-Miraj & Kupwad','SANGLI-MI',15,'IND','ACTIVE'),(94,'Mangalore','MANGALORE',12,'IND','ACTIVE'),(95,'Erode','ERODE',24,'IND','ACTIVE'),(96,'Belgaum','BELGAUM',12,'IND','ACTIVE'),(97,'Ambattur','AMBATTUR',24,'IND','ACTIVE'),(98,'Tirunelveli','TIRUNELVE',24,'IND','ACTIVE'),(99,'Malegaon','MALEGAON',15,'IND','ACTIVE'),(100,'Gaya','GAYA',4,'IND','ACTIVE'),(101,'Jalgaon','JALGAON',15,'IND','ACTIVE'),(102,'Udaipur','UDAIPUR',22,'IND','ACTIVE'),(103,'Maheshtala','MAHESHTAL',29,'IND','ACTIVE'),(104,'Davanagere','DAVANAGER',12,'IND','ACTIVE'),(105,'Kozhikode','KOZHIKODE',13,'IND','ACTIVE'),(106,'Akola','AKOLA',15,'IND','ACTIVE'),(107,'Kurnool','KURNOOL',1,'IND','ACTIVE'),(108,'Rajpur Sonarpur','RAJPUR SO',29,'IND','ACTIVE'),(109,'Rajahmundry','RAJAHMUND',1,'IND','ACTIVE'),(110,'Bokaro','BOKARO',11,'IND','ACTIVE'),(111,'South Dumdum','SOUTH DUM',29,'IND','ACTIVE'),(112,'Bellary','BELLARY',12,'IND','ACTIVE'),(113,'Patiala','PATIALA',21,'IND','ACTIVE'),(114,'Gopalpur','GOPALPUR',29,'IND','ACTIVE'),(115,'Agartala','AGARTALA',26,'IND','ACTIVE'),(116,'Bhagalpur','BHAGALPUR',4,'IND','ACTIVE'),(117,'Muzaffarnagar','MUZAFFARN',28,'IND','ACTIVE'),(118,'Bhatpara','BHATPARA',29,'IND','ACTIVE'),(119,'Panihati','PANIHATI',29,'IND','ACTIVE'),(120,'Latur','LATUR',15,'IND','ACTIVE'),(121,'Dhule','DHULE',15,'IND','ACTIVE'),(122,'Tirupati','TIRUPATI',1,'IND','ACTIVE'),(123,'Rohtak','ROHTAK',8,'IND','ACTIVE'),(124,'Korba','KORBA',5,'IND','ACTIVE'),(125,'Bhilwara','BHILWARA',22,'IND','ACTIVE'),(126,'Berhampur','BERHAMPUR',20,'IND','ACTIVE'),(127,'Muzaffarpur','MUZAFFARP',4,'IND','ACTIVE'),(128,'Ahmednagar','AHMEDNAGA',15,'IND','ACTIVE'),(129,'Mathura','MATHURA',28,'IND','ACTIVE'),(130,'Kollam','KOLLAM',13,'IND','ACTIVE'),(131,'Avadi','AVADI',24,'IND','ACTIVE'),(132,'Kadapa','KADAPA',1,'IND','ACTIVE'),(133,'Kamarhati','KAMARHATI',29,'IND','ACTIVE'),(134,'Sambalpur','SAMBALPUR',20,'IND','ACTIVE'),(135,'Bilaspur','BILASPUR',5,'IND','ACTIVE'),(136,'Shahjahanpur','SHAHJAHAN',28,'IND','ACTIVE'),(137,'Satara','SATARA',15,'IND','ACTIVE'),(138,'Bijapur','BIJAPUR',12,'IND','ACTIVE'),(139,'Rampur','RAMPUR',28,'IND','ACTIVE'),(140,'Shivamogga','SHIVAMOGG',12,'IND','ACTIVE'),(141,'Chandrapur','CHANDRAPU',15,'IND','ACTIVE'),(142,'Junagadh','JUNAGADH',7,'IND','ACTIVE'),(143,'Thrissur','THRISSUR',13,'IND','ACTIVE'),(144,'Alwar','ALWAR',22,'IND','ACTIVE'),(145,'Bardhaman','BARDHAMAN',29,'IND','ACTIVE'),(146,'Kulti','KULTI',29,'IND','ACTIVE'),(147,'Kakinada','KAKINADA',1,'IND','ACTIVE'),(148,'Nizamabad','NIZAMABAD',25,'IND','ACTIVE'),(149,'Parbhani','PARBHANI',15,'IND','ACTIVE'),(150,'Tumkur','TUMKUR',12,'IND','ACTIVE'),(151,'Khammam','KHAMMAM',25,'IND','ACTIVE'),(152,'Ozhukarai','OZHUKARAI',36,'IND','ACTIVE'),(153,'Bihar Sharif','BIHAR SHA',4,'IND','ACTIVE'),(154,'Panipat','PANIPAT',8,'IND','ACTIVE'),(155,'Darbhanga','DARBHANGA',4,'IND','ACTIVE'),(156,'Bally','BALLY',29,'IND','ACTIVE'),(157,'Aizawl','AIZAWL',18,'IND','ACTIVE'),(158,'Dewas','DEWAS',14,'IND','ACTIVE'),(159,'Ichalkaranji','ICHALKARA',15,'IND','ACTIVE'),(160,'Karnal','KARNAL',8,'IND','ACTIVE'),(161,'Bathinda','BATHINDA',21,'IND','ACTIVE'),(162,'Jalna','JALNA',15,'IND','ACTIVE'),(163,'Eluru','ELURU',1,'IND','ACTIVE'),(164,'Kirari Suleman Nagar','KIRARI SU',32,'IND','ACTIVE'),(165,'Barasat','BARASAT',29,'IND','ACTIVE'),(166,'Purnia','PURNIA',4,'IND','ACTIVE'),(167,'Satna','SATNA',14,'IND','ACTIVE'),(168,'Mau','MAU',28,'IND','ACTIVE'),(169,'Sonipat','SONIPAT',8,'IND','ACTIVE'),(170,'Farrukhabad','FARRUKHAB',28,'IND','ACTIVE'),(171,'Sagar','SAGAR',14,'IND','ACTIVE'),(172,'Rourkela','ROURKELA',20,'IND','ACTIVE'),(173,'Durg','DURG',5,'IND','ACTIVE'),(174,'Imphal','IMPHAL',16,'IND','ACTIVE'),(175,'Ratlam','RATLAM',14,'IND','ACTIVE'),(176,'Hapur','HAPUR',28,'IND','ACTIVE'),(177,'Arrah','ARRAH',4,'IND','ACTIVE'),(178,'Karimnagar','KARIMNAGA',25,'IND','ACTIVE'),(179,'Anantapur','ANANTAPUR',1,'IND','ACTIVE'),(180,'Etawah','ETAWAH',28,'IND','ACTIVE'),(181,'Ambernath','AMBERNATH',15,'IND','ACTIVE'),(182,'North Dumdum','NORTH DUM',29,'IND','ACTIVE'),(183,'Bharatpur','BHARATPUR',22,'IND','ACTIVE'),(184,'Begusarai','BEGUSARAI',4,'IND','ACTIVE'),(185,'New Delhi','NEW DELHI',32,'IND','ACTIVE'),(186,'Gandhidham','GANDHIDHA',7,'IND','ACTIVE'),(187,'Baranagar','BARANAGAR',29,'IND','ACTIVE'),(188,'Tiruvottiyur','TIRUVOTTI',24,'IND','ACTIVE'),(189,'Puducherry','PUDUCHERR',36,'IND','ACTIVE'),(190,'Sikar','SIKAR',22,'IND','ACTIVE'),(191,'Thoothukudi','THOOTHUKU',24,'IND','ACTIVE'),(192,'Rewa','REWA',14,'IND','ACTIVE'),(193,'Mirzapur','MIRZAPUR',28,'IND','ACTIVE'),(194,'Raichur','RAICHUR',12,'IND','ACTIVE'),(195,'Pali','PALI',22,'IND','ACTIVE'),(196,'Ramagundam','RAMAGUNDA',25,'IND','ACTIVE'),(197,'Haridwar','HARIDWAR',27,'IND','ACTIVE'),(198,'Vijayanagaram','VIJAYANAG',1,'IND','ACTIVE'),(199,'Katihar','KATIHAR',4,'IND','ACTIVE'),(200,'Nagarcoil','NAGARCOIL',24,'IND','ACTIVE'),(201,'Sri Ganganagar','SRI GANGA',22,'IND','ACTIVE'),(202,'Karawal Nagar','KARAWAL N',32,'IND','ACTIVE'),(203,'Mango','MANGO',11,'IND','ACTIVE'),(204,'Thanjavur','THANJAVUR',24,'IND','ACTIVE'),(205,'Bulandshahr','BULANDSHA',28,'IND','ACTIVE'),(206,'Uluberia','ULUBERIA',29,'IND','ACTIVE'),(207,'Murwara','MURWARA',28,'IND','ACTIVE'),(208,'Sambhal','SAMBHAL',28,'IND','ACTIVE'),(209,'Singrauli','SINGRAULI',14,'IND','ACTIVE'),(210,'Nadiad','NADIAD',7,'IND','ACTIVE'),(211,'Secunderabad','SECUNDERA',1,'IND','ACTIVE'),(212,'Naihati','NAIHATI',29,'IND','ACTIVE'),(213,'Yamunanagar','YAMUNANAG',8,'IND','ACTIVE'),(214,'Bidhan Nagar','BIDHAN NA',29,'IND','ACTIVE'),(215,'Pallavaram','PALLAVARA',24,'IND','ACTIVE'),(216,'Bidar','BIDAR',12,'IND','ACTIVE'),(217,'Munger','MUNGER',4,'IND','ACTIVE'),(218,'Panchkula','PANCHKULA',8,'IND','ACTIVE'),(219,'Burhanpur','BURHANPUR',14,'IND','ACTIVE'),(220,'Raurkela Industrial Township','RAURKELA ',20,'IND','ACTIVE'),(221,'Kharagpur','KHARAGPUR',29,'IND','ACTIVE'),(222,'Dindigul','DINDIGUL',24,'IND','ACTIVE'),(223,'Gandhinagar','GANDHINAG',7,'IND','ACTIVE'),(224,'Hospet','HOSPET',12,'IND','ACTIVE'),(225,'Nangloi Jat','NANGLOI J',32,'IND','ACTIVE'),(226,'English Bazar','ENGLISH B',29,'IND','ACTIVE'),(227,'Ongole','ONGOLE',1,'IND','ACTIVE'),(228,'Deoghar','DEOGHAR',11,'IND','ACTIVE'),(229,'Chapra','CHAPRA',4,'IND','ACTIVE'),(230,'Haldia','HALDIA',29,'IND','ACTIVE'),(231,'Khandwa','KHANDWA',14,'IND','ACTIVE'),(232,'Nandyal','NANDYAL',1,'IND','ACTIVE'),(233,'Chittoor','CHITTOOR',1,'IND','ACTIVE'),(234,'Morena','MORENA',14,'IND','ACTIVE'),(235,'Amroha','AMROHA',28,'IND','ACTIVE'),(236,'Anand','ANAND',7,'IND','ACTIVE'),(237,'Bhind','BHIND',14,'IND','ACTIVE'),(238,'Bhalswa Jahangir Pur','BHALSWA J',32,'IND','ACTIVE'),(239,'Madhyamgram','MADHYAMGR',29,'IND','ACTIVE'),(240,'Bhiwani','BHIWANI',8,'IND','ACTIVE'),(241,'Navi Mumbai Panvel Raigad','NAVI MUMB',15,'IND','ACTIVE'),(242,'Baharampur','BAHARAMPU',29,'IND','ACTIVE'),(243,'Ambala','AMBALA',8,'IND','ACTIVE'),(244,'Morvi','MORVI',7,'IND','ACTIVE'),(245,'Fatehpur','FATEHPUR',28,'IND','ACTIVE'),(246,'Rae Bareli','RAE BAREL',28,'IND','ACTIVE'),(247,'Khora','KHORA',28,'IND','ACTIVE'),(248,'Bhusawal','BHUSAWAL',15,'IND','ACTIVE'),(249,'Orai','ORAI',28,'IND','ACTIVE'),(250,'Bahraich','BAHRAICH',28,'IND','ACTIVE'),(251,'Vellore','VELLORE',24,'IND','ACTIVE'),(252,'Mahesana','MAHESANA',7,'IND','ACTIVE'),(253,'Sambalpur','SAMBALPUR',20,'IND','ACTIVE'),(254,'Raiganj','RAIGANJ',29,'IND','ACTIVE'),(255,'Sirsa','SIRSA',8,'IND','ACTIVE'),(256,'Danapur','DANAPUR',4,'IND','ACTIVE'),(257,'Serampore','SERAMPORE',29,'IND','ACTIVE'),(258,'Sultan Pur Majra','SULTAN PU',32,'IND','ACTIVE'),(259,'Guna','GUNA',14,'IND','ACTIVE'),(260,'Jaunpur','JAUNPUR',28,'IND','ACTIVE'),(261,'Panvel','PANVEL',15,'IND','ACTIVE'),(262,'Shivpuri','SHIVPURI',14,'IND','ACTIVE'),(263,'Surendranagar Dudhrej','SURENDRAN',7,'IND','ACTIVE'),(264,'Unnao','UNNAO',28,'IND','ACTIVE'),(265,'Hugli and Chinsurah','HUGLI AND',29,'IND','ACTIVE'),(266,'Alappuzha','ALAPPUZHA',13,'IND','ACTIVE'),(267,'Kottayam','KOTTAYAM',13,'IND','ACTIVE'),(268,'Shimla','SHIMLA',9,'IND','ACTIVE'),(269,'Karaikudi','KARAIKUDI',24,'IND','ACTIVE'),(270,'Adilabad','ADILABAD',25,'IND','ACTIVE'),(271,'Amaravati','AMARAVATI',1,'IND','ACTIVE');


UPDATE KETTLE_MASTER_DUMP.ADDRESS_INFO 
SET 
    CITY = 'New Delhi'
WHERE
    ADDRESS_ID IN (14492,14493,106,107,108,112,113,1206,1207,1208,1209,12030,12031,12037,12041,12050,12055,12104,12199,12200,12201,12230,12244,13593,13710,14000,14001,14170,13717);
    
    UPDATE KETTLE_MASTER_DUMP.ADDRESS_INFO 
SET 
    STATE = 'New Delhi'
WHERE
    ADDRESS_ID IN (14492,14493,106,107,108,112,113,1206,1207,1208,1209,12030,12031,12037,12041,12050,12055,12104,12199,12200,12201,12230,12244,13593,13710,14000,14001,14170,13717);
    
UPDATE KETTLE_MASTER_DUMP.UNIT_DETAIL SET GSTIN='07AARCS3853M1ZL' Where UNIT_ID in (10006,
10007	,
10008	,
10012	,
10013	,
12006	,
12007	,
12008	,
12009	,
12010	,
12011	,
12013	,
12015	,
12016	,
12019	,
12028	,
22001	,
24001	,
26001	,
26003	,
26005	,
26008	,
26012	,
26014	,
26017	,
26018	,
26020	,
26026	,
26027
);
UPDATE KETTLE_MASTER_DUMP.UNIT_DETAIL SET GSTIN='09AARCS3853M1ZH' Where UNIT_ID in (10002	,
10004	,
12005	,
12012	,
12018	,
12020	,
12021	,
26010	,
26013	,
26016	,
26019	,
26025	
);
UPDATE KETTLE_MASTER_DUMP.UNIT_DETAIL SET GSTIN='27AARCS3853M1ZJ' Where UNIT_ID in (10009	,
10010	,
12022	,
12023	,
12027	,
12031	,
22002	,
24002	,
26002	,
26004	,
26009	,
26021	,
26022	,
26028	,
26029		
);
UPDATE KETTLE_MASTER_DUMP.UNIT_DETAIL SET GSTIN='06AARCS3853M1ZN' Where UNIT_ID in (10000	,
10001	,
10003	,
10005	,
10011	,
10014	,
11001	,
12001	,
12002	,
12003	,
12004	,
12014	,
12017	,
12029	,
12030	,
26006	,
26007	,
26011	,
26015			
);

UPDATE KETTLE_MASTER_DUMP.UNIT_DETAIL SET GSTIN='06AARCS3853M1ZN' Where UNIT_ID in (26023	,
26024			
);

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET LOCATION_DETAIL_ID='2' Where UNIT_ID in (10006,
10007	,
10008	,
10012	,
10013	,
12006	,
12007	,
12008	,
12009	,
12010	,
12011	,
12013	,
12015	,
12016	,
12019	,
12028	,
22001	,
24001	,
26001	,
26003	,
26005	,
26008	,
26012	,
26014	,
26017	,
26018	,
26020	,
26026	,
26027
);
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET LOCATION_DETAIL_ID='70' Where UNIT_ID in (10002	,
10004	,
12005	,
12012	,
12018	,
12020	,
12021	,
26010	,
26013	,
26016	,
26025	
);
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET LOCATION_DETAIL_ID='21' Where UNIT_ID in (
26019
);
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET LOCATION_DETAIL_ID='1' Where UNIT_ID in (10009	,
10010	,
12022	,
12023	,
12027	,
12031	,
22002	,
24002	,
26002	,
26004	,
26009	,
26021	,
26022	,
26028	,
26029		
);
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET LOCATION_DETAIL_ID='57' Where UNIT_ID in (10000	,
10001	,
10003	,
10005	,
10011	,
10014	,
11001	,
12001	,
12002	,
12003	,
12004	,
12014	,
12017	,
12029	,
12030	,
26006	,
26007	,
26011	,
26015			
);

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL SET LOCATION_DETAIL_ID='57' Where UNIT_ID in (26023	,
26024			
);

DELETE FROM KETTLE_MASTER_DEV.TAX_PROFILE_DATA;
DELETE FROM KETTLE_MASTER_DEV.TAX_CATEGORY_DATA;
DELETE FROM KETTLE_MASTER_DEV.CATEGORY_TAX_DATA;
DELETE FROM KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA;
INSERT INTO TAX_PROFILE_DATA VALUES (1,'GST','GST','ACTIVE','GST','GST','ON_SALE'),
(2,'CESS1','CESS1','ACTIVE','22021020','22021020_','ON_SALE'),
(3,'CESS2','CESS2','ACTIVE','22021020','22021020_','ON_SALE');

INSERT INTO KETTLE_MASTER_DEV.TAX_CATEGORY_DATA 
(CATEGORY_CODE, CATEGORY_DESCRIPTION, CATEGORY_STATUS, CATEGORY_INTERNAL_DESCRIPTION, IS_EXEMPTED) 
VALUES ('00441067','Restaurant Services','IN_ACTIVE','Restaurant Services','N'),
('09024090','\"TEA, WHETHER OR NOT FLAVOURED OTHER BLACK TEA (FERMENTED) AND OTHER PARTLY FERMENTED TEA : OTHER\"','ACTIVE','\"TEA, WHETHER OR NOT FLAVOURED OTHER BLACK TEA (FERMENTED) AND OTHER PARTLY FERMENTED TEA : OTHER\"','N'),
('00441067_22021010','Restaurant Services With Cess1','ACTIVE','Restaurant Services With Cess1','N'),
('00441067_22021020','Restaurant Services With Cess2','ACTIVE','Restaurant Services With Cess2','N'),
('09022090', 'TEA, WHETHER OR NOT FLAVOURED OTHER GREEN TEA (NOT FERMENTED) : OTHER', 'ACTIVE', 'TEA, WHETHER OR NOT FLAVOURED OTHER GREEN TEA (NOT FERMENTED) : OTHER', 'N'),
('09109100', '\"GINGER, SAFFRON, TURMERIC (CURCUMA), THYME, BAY LEAVES, CURRY AND OTHER SPICES\nOther spices : Mixtures referred to in Note 1(b) to this Chapter\"', 'ACTIVE', '\"GINGER, SAFFRON, TURMERIC (CURCUMA), THYME, BAY LEAVES, CURRY AND OTHER SPICES', 'N'),
('COMBO', 'COMBO', 'ACTIVE', 'Combo', 'Y'),
('GIFT_CARD', 'GIFT_CARD', 'ACTIVE', 'Gift Card', 'Y');

UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL
set TAX_CODE = '00441067'
where PRODUCT_ID IN (10,11,12,20,30,40,50,60,70,80,90,100,110,120,130,140,150,160,170,180,190,200,210,220,230,240,250,260,270,271,272,280,290,300,330,340,350,360,370,380,390,400,410,420,430,440,480,490,500,510,520,530,540,550,560,570,580,590,600,610,620,630,631,640,641,650,651,652,660,670,671,680,681,682,683,684,690,740,750,760,770,780,790,800,810,820,821,822,830,831,832,833,834,835,836,840,850,860,861,862,863,864,865,866,867);

UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL
set TAX_CODE = '09024090'
where PRODUCT_ID IN (691,692,700,710,720,730);

UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL
set TAX_CODE = '00441067_22021010'
where PRODUCT_ID IN (310,320);

UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL
set TAX_CODE = '00441067_22021020'
where PRODUCT_ID IN (450,460,470);

UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL
set TAX_CODE = '00441067'
where TAX_CODE IS NULL;

UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='GIFT_CARD' WHERE PRODUCT_ID='1056';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='GIFT_CARD' WHERE PRODUCT_ID='1048';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='GIFT_CARD' WHERE PRODUCT_ID='1026';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='GIFT_CARD' WHERE PRODUCT_ID='1027';

UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='820';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='821';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='822';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='830';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='831';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='832';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='833';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='834';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='835';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='836';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='995';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='996';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1008';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1009';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1010';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1011';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1012';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1013';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1014';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1015';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1016';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1017';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1039';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1052';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1053';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1054';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1055';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1067';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1096';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1097';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1098';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='997';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='998';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='999';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1000';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1025';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='COMBO' WHERE PRODUCT_ID='1022';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='09109100' WHERE PRODUCT_ID='730';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE='09022090' WHERE PRODUCT_ID='720';

INSERT INTO KETTLE_MASTER_DEV.CATEGORY_TAX_DATA ( CATEGORY_ID, TAX_ID, COUNTRY_DETAIL_ID, STATE_DETAIL_ID, IGST_TAX_RATE, CGST_TAX_RATE, SGST_TAX_RATE, START_DATE, TAX_STATUS, CREATED_AT, CREATED_BY) 
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = 'COMBO'),1,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,0.00,0.00,0.00, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;
INSERT INTO KETTLE_MASTER_DEV.CATEGORY_TAX_DATA ( CATEGORY_ID, TAX_ID, COUNTRY_DETAIL_ID, STATE_DETAIL_ID, IGST_TAX_RATE, CGST_TAX_RATE, SGST_TAX_RATE, START_DATE, TAX_STATUS, CREATED_AT, CREATED_BY) 
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = 'GIFT_CARD'),1,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,0.00,0.00,0.00, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;
INSERT INTO KETTLE_MASTER_DEV.CATEGORY_TAX_DATA ( CATEGORY_ID, TAX_ID, COUNTRY_DETAIL_ID, STATE_DETAIL_ID, IGST_TAX_RATE, CGST_TAX_RATE, SGST_TAX_RATE, START_DATE, TAX_STATUS, CREATED_AT, CREATED_BY) 
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = '00441067'),1,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,18.00,9.00,9.00, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;
INSERT INTO KETTLE_MASTER_DEV.CATEGORY_TAX_DATA ( CATEGORY_ID, TAX_ID, COUNTRY_DETAIL_ID, STATE_DETAIL_ID, IGST_TAX_RATE, CGST_TAX_RATE, SGST_TAX_RATE, START_DATE, TAX_STATUS, CREATED_AT, CREATED_BY) 
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = '00441067_22021010'),1,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,18.00,9.00,9.00, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;
INSERT INTO KETTLE_MASTER_DEV.CATEGORY_TAX_DATA ( CATEGORY_ID, TAX_ID, COUNTRY_DETAIL_ID, STATE_DETAIL_ID, IGST_TAX_RATE, CGST_TAX_RATE, SGST_TAX_RATE, START_DATE, TAX_STATUS, CREATED_AT, CREATED_BY) 
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = '00441067_22021020'),1,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,18.00,9.00,9.00, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;
INSERT INTO KETTLE_MASTER_DEV.CATEGORY_TAX_DATA ( CATEGORY_ID, TAX_ID, COUNTRY_DETAIL_ID, STATE_DETAIL_ID, IGST_TAX_RATE, CGST_TAX_RATE, SGST_TAX_RATE, START_DATE, TAX_STATUS, CREATED_AT, CREATED_BY) 
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = '09024090'),1,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,5.00,2.50,2.50, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;
INSERT INTO KETTLE_MASTER_DEV.CATEGORY_TAX_DATA ( CATEGORY_ID, TAX_ID, COUNTRY_DETAIL_ID, STATE_DETAIL_ID, IGST_TAX_RATE, CGST_TAX_RATE, SGST_TAX_RATE, START_DATE, TAX_STATUS, CREATED_AT, CREATED_BY) 
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = '09022090'),1,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,5.00,2.50,2.50, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;
INSERT INTO KETTLE_MASTER_DEV.CATEGORY_TAX_DATA ( CATEGORY_ID, TAX_ID, COUNTRY_DETAIL_ID, STATE_DETAIL_ID, IGST_TAX_RATE, CGST_TAX_RATE, SGST_TAX_RATE, START_DATE, TAX_STATUS, CREATED_AT, CREATED_BY) 
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = '09109100'),1,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,5.00,2.50,2.50, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;

INSERT INTO KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA
(CATEGORY_ID,TAX_ID,COUNTRY_DETAIL_ID,STATE_DETAIL_ID,TAX_RATE,START_DATE,TAX_STATUS,CREATED_AT,CREATED_BY)
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = '00441067_22021010'),2,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,12.00, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;

INSERT INTO KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA
(CATEGORY_ID,TAX_ID,COUNTRY_DETAIL_ID,STATE_DETAIL_ID,TAX_RATE,START_DATE,TAX_STATUS,CREATED_AT,CREATED_BY)
select (select TAX_CATEGORY_DATA_ID from TAX_CATEGORY_DATA where CATEGORY_CODE = '00441067_22021020'),3,COUNTRY_DETAIL_ID,STATE_DETAIL_ID ,12.00, '2017-07-01','ACTIVE','2017-07-01 00:00:00','Mayank Malik [120057]' from KETTLE_MASTER_DEV.STATE_DETAIL ;


ALTER TABLE TAX_CATEGORY_DATA ADD COLUMN CATEGORY_TYPE VARCHAR(5) DEFAULT NULL;


ALTER TABLE TAX_CATEGORY_DATA ADD CONSTRAINT UNIQUE(CATEGORY_CODE);


UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 10;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 11;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 12;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 13;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 20;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 30;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 40;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 50;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 60;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 70;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 80;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 90;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 100;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 110;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 120;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 130;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 140;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 150;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 160;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 170;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 180;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 190;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 200;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 210;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 220;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 230;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 240;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 250;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 260;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 270;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 271;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 272;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 280;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 290;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 300;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 330;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 340;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 350;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 360;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 370;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 380;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 390;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 400;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 410;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 420;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 430;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 440;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 480;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 490;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 500;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 510;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 520;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 530;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 540;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 550;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 560;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 570;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 580;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 590;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 600;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 610;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 620;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 630;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 631;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 640;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 641;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 650;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 651;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 652;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 660;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 670;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 671;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 680;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 681;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 682;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 683;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 684;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 690;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 691;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '09024090' WHERE PRODUCT_ID = 692;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '09024090' WHERE PRODUCT_ID = 700;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '09024090' WHERE PRODUCT_ID = 710;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '09022090' WHERE PRODUCT_ID = 720;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '09109100' WHERE PRODUCT_ID = 730;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 740;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 750;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 760;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 770;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 780;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 790;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 800;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 810;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 820;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 821;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 822;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 830;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 831;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 832;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 833;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 834;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 835;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 836;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 840;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 850;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 860;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 861;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 862;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 863;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 864;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 865;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 866;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 867;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 868;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 869;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 870;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 969;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 970;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 971;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 972;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 973;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 974;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 975;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 976;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 977;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 978;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 979;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 980;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 981;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 982;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 983;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 984;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 985;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 986;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 987;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 988;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 989;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 990;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 991;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 992;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 993;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 994;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 995;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 996;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 997;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 998;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 999;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1000;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1001;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1002;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '19054000' WHERE PRODUCT_ID = 1003;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '19059020' WHERE PRODUCT_ID = 1004;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '19059020' WHERE PRODUCT_ID = 1005;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '19059020' WHERE PRODUCT_ID = 1006;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '19059020' WHERE PRODUCT_ID = 1007;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1008;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1009;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1010;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1011;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1012;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1013;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1014;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1015;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1016;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1017;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1018;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1019;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1020;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1021;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1022;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1023;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1024;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1025;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'GIFT_CARD' WHERE PRODUCT_ID = 1026;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'GIFT_CARD' WHERE PRODUCT_ID = 1027;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1028;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1029;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1030;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1031;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1032;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1033;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1034;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1035;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1036;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1037;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1038;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1039;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1040;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1041;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1042;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1043;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1044;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1045;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1046;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1047;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'GIFT_CARD' WHERE PRODUCT_ID = 1048;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1049;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1050;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1051;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1052;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1053;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1054;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1055;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'GIFT_CARD' WHERE PRODUCT_ID = 1056;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '21012010' WHERE PRODUCT_ID = 1057;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1058;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '21012010' WHERE PRODUCT_ID = 1059;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1060;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1061;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1062;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1063;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1064;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1065;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1066;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1067;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1068;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1069;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1070;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1071;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1072;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1073;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1074;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1075;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1076;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1077;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1078;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1079;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1080;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1081;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1082;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1083;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1084;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1085;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1086;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1087;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1088;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1089;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1090;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1091;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1092;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1093;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1094;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1095;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1096;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1097;
UPDATE PRODUCT_DETAIL SET TAX_CODE = 'COMBO' WHERE PRODUCT_ID = 1098;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1099;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1100;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1101;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1102;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1103;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1104;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1105;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1106;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1107;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1108;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1109;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1110;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1111;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1112;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1113;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067' WHERE PRODUCT_ID = 1114;


UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067_22021010' WHERE PRODUCT_ID = 310;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067_22021010' WHERE PRODUCT_ID = 320;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067_22021020' WHERE PRODUCT_ID = 450;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067_22021020' WHERE PRODUCT_ID = 460;
UPDATE PRODUCT_DETAIL SET TAX_CODE = '00441067_22021020' WHERE PRODUCT_ID = 470;



UPDATE `KETTLE_MASTER_DEV`.`TAX_CATEGORY_DATA` SET `CATEGORY_CODE`='00009963' WHERE `TAX_CATEGORY_DATA_ID`='208';
UPDATE `KETTLE_MASTER_DEV`.`TAX_CATEGORY_DATA` SET `CATEGORY_CODE`='00009963_22021010' WHERE `TAX_CATEGORY_DATA_ID`='256';
UPDATE `KETTLE_MASTER_DEV`.`TAX_CATEGORY_DATA` SET `CATEGORY_CODE`='00009963_22021020' WHERE `TAX_CATEGORY_DATA_ID`='257';
UPDATE `KETTLE_MASTER_DEV`.`TAX_CATEGORY_DATA` SET `CATEGORY_DESCRIPTION`='Food and beverages services'AND `CATEGORY_INTERNAL_DESCRIPTION`='Food and beverages services' WHERE `TAX_CATEGORY_DATA_ID`='208';
UPDATE `KETTLE_MASTER_DEV`.`TAX_CATEGORY_DATA` SET `CATEGORY_DESCRIPTION`='Food and beverages services with cess1 peach iced tea passion fruit iced tea' AND `CATEGORY_INTERNAL_DESCRIPTION`='Food and beverages services with cess1 peach iced tea passion fruit iced tea' WHERE `TAX_CATEGORY_DATA_ID`='256';
UPDATE `KETTLE_MASTER_DEV`.`TAX_CATEGORY_DATA` SET `CATEGORY_DESCRIPTION`='Food and beverages services with cess1 Mint Lemonade Modinagar Shikanji Fresh Lime' AND `CATEGORY_INTERNAL_DESCRIPTION`='Food and beverages services with cess1 Mint Lemonade Modinagar Shikanji Fresh Lime' WHERE `TAX_CATEGORY_DATA_ID`='257';

UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE = '00009963' WHERE TAX_CODE = '00441067';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE = '00009963_22021010' WHERE TAX_CODE = '00441067_22021010';
UPDATE KETTLE_MASTER_DEV.PRODUCT_DETAIL SET TAX_CODE = '00009963_22021020' WHERE TAX_CODE = '00441067_22021020';

