set foreign_key_checks =0;
#FEEDBACK Cleanup
TRUNCATE KETTLE_DEV.ORDER_FEEDBACK_DETAIL;
TRUNCATE KETTLE_DEV.ORDER_FEEDBACK_EVENT;
TRUNCATE KETTLE_DEV.FEEDBACK_FIELD;
TRUNCATE KETTLE_DEV.FEEDBACK_FORM;
TRUNCATE KETTLE_DEV.FEEDBACK_INFO;
TRUNCATE KETTLE_DEV.FEEDBACK_RESPONSE;
TRUNCATE KETTLE_DEV.FEEDBACK_RESPONSE_DATA;
TRUNCATE KETTLE_DEV.CUSTOMER_OFFER_DETAIL;

#BASIC CLEAN-UP
TRUNCATE KETTLE_DEV.LOYALTY_SCORE;

INSERT INTO KETTLE_DEV.LOYALTY_SCORE (LOYALTY_POINTS_ID, CUSTOMER_ID, ACQUIRED_POINTS, CUMULATIVE_POINTS, LAST_ORDER_TIME, LAST_ORDER_ID, ORDER_COUNT, AVAILED_SIGNUP_OFFER)
VALUES('1', '1', '10', '10', '2016-08-06 22:00:17', '860283', '2', 'Y');

TRUNCATE KETTLE_DEV.LOYALTY_EVENTS;
TRUNCATE KETTLE_DEV.CUSTOMER_ADDRESS_INFO;

SELECT * FROM KETTLE_DEV.CUSTOMER_INFO where CUSTOMER_ID <6;
TRUNCATE KETTLE_DEV.CUSTOMER_INFO;
INSERT INTO `KETTLE_DEV`.`CUSTOMER_INFO` (`CUSTOMER_ID`, `FIRST_NAME`, `COUNTRY_CODE`, `CONTACT_NUMBER`, `IS_NUMBER_VERIFIED`, `IS_EMAIL_VERIFIED`, `ADD_TIME`, `EMAIL_VERIFICATION_TIME`, `ACQUISITION_SOURCE`, `ACQUISITION_TOKEN`, `REGISTRATION_UNIT_ID`, `IS_SMS_SUBSCRIBER`, `IS_EMAIL_SUBSCRIBER`, `IS_LOYALTY_SUBSCRIBER`, `IS_BLACKLISTED`, `IS_DND`, `IS_INTERNAL`, `REF_CODE`, `IS_REF_SUBSCRIBER`, `IS_REFERRER_AWARDED`, `ACQUISITION_BRAND_ID`, `IS_CHAAYOS_CUSTOMER`, `OPT_OUT_FACE_IT`, `OPT_OUT_TIME`) VALUES ('67456', 'Swiggy', '+91', '9599598307', 'N', 'Y', '2016-01-04 17:45:19', '2018-02-15 20:47:08', 'Chaayos-COD', '110016', '11001', 'N', 'Y', 'N', 'Y', 'Y', 'N', 'SWI8307', 'N', 'N', '1', 'Y', 'N', NULL);
INSERT INTO `KETTLE_DEV`.`CUSTOMER_INFO` (`CUSTOMER_ID`, `FIRST_NAME`, `MIDDLE_NAME`, `LAST_NAME`, `COUNTRY_CODE`, `CONTACT_NUMBER`, `IS_NUMBER_VERIFIED`, `ADD_TIME`, `ACQUISITION_SOURCE`, `ACQUISITION_TOKEN`, `IS_SMS_SUBSCRIBER`, `IS_EMAIL_SUBSCRIBER`, `IS_LOYALTY_SUBSCRIBER`, `IS_BLACKLISTED`, `IS_DND`, `IS_INTERNAL`, `IS_REF_SUBSCRIBER`, `IS_REFERRER_AWARDED`, `OPT_OUT_FACE_IT`, `ACQUISITION_BRAND_ID`, `IS_CHAAYOS_CUSTOMER`) VALUES ('5', 'Chaayos', 'Dev', 'Receipt', '+91', '2222222222', 'N', '2014-01-01 00:00:00', 'SQUARE_COINS', 'SQUARE_COINS', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', '1', 'Y');
INSERT INTO `KETTLE_DEV`.`CUSTOMER_INFO` (`CUSTOMER_ID`, `FIRST_NAME`, `LAST_NAME`, `COUNTRY_CODE`, `CONTACT_NUMBER`, `IS_NUMBER_VERIFIED`, `ADD_TIME`, `ACQUISITION_SOURCE`, `ACQUISITION_TOKEN`, `IS_SMS_SUBSCRIBER`, `IS_EMAIL_SUBSCRIBER`, `IS_LOYALTY_SUBSCRIBER`, `IS_BLACKLISTED`, `IS_DND`, `IS_INTERNAL`, `IS_REF_SUBSCRIBER`, `IS_REFERRER_AWARDED`, `OPT_OUT_FACE_IT`, `ACQUISITION_BRAND_ID`, `IS_CHAAYOS_CUSTOMER`) VALUES ('1', 'Chaayos', 'Receipt', '+91', '1111111111', 'N', '2014-01-01 00:00:00', 'SQUARE_COINS', 'SQUARE_COINS', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', '1', 'Y');
truncate KETTLE_DEV.ORDER_EMAIL_NOTIFICATION;
update KETTLE_DEV.ORDER_DETAIL set CUSTOMER_ID = 5 where CUSTOMER_ID <> 5;
TRUNCATE KETTLE_DEV.ORDER_ENQUIRY_ITEM;

#EMPLOYEE CLEAN-UP
#password 321321
update KETTLE_MASTER_DEV.EMPLOYEE_PASS_CODE set EMP_PASS_CODE = 'O0US+kuO+OaGLXvOSRtymA==';

truncate KETTLE_MASTER_DEV.EMPLOYEE_UNIT_MAPPING;
insert into KETTLE_MASTER_DEV.EMPLOYEE_UNIT_MAPPING(EMP_ID, UNIT_ID)
select ed.EMP_ID, ud.UNIT_ID from KETTLE_MASTER_DEV.EMPLOYEE_DETAIL ed, KETTLE_MASTER_DEV.UNIT_DETAIL ud;
UPDATE KETTLE_MASTER_DEV.DESIGNATION
SET TRANSACTION_SYSTEM_ACCESS = 'Y', SCM_SYSTEM_ACCESS = 'Y',
ANALYTICS_SYSTEM_ACCESS = 'Y', ADMIN_SYSTEM_ACCESS = 'Y', CLM_SYSTEM_ACCESS ='Y' , CRM_SYSTEM_ACCESS = 'Y';

#SUBSCRIPTION CLEAN-UP
set foreign_key_checks =0;
truncate table KETTLE_DEV.SUBSCRIPTION_DETAIL;
truncate table KETTLE_DEV.SUBSCRIPTION_ITEM;
truncate table KETTLE_DEV.SUBSCRIPTION_ITEM_ADDON;
truncate table KETTLE_DEV.SUBSCRIPTION_SETTLEMENT;
truncate table KETTLE_DEV.SUBSCRIPTION_EVENT_ITEM;
truncate table KETTLE_DEV.SUBSCRIPTION_EVENT_DETAIL;
truncate table KETTLE_DEV.SUBSCRIPTION_STATUS_EVENT;
update KETTLE_DEV.ORDER_DETAIL set SUBSCRIPTION_ID = null;


TRUNCATE KETTLE_MASTER_DEV.NOTIFICATION_LOG_DETAIL;
TRUNCATE KETTLE_DEV.WORKSTATION_LOG;
TRUNCATE KETTLE_DEV.ASSEMBLY_LOG_DATA;

TRUNCATE KETTLE_DEV.SALES_REPORT_DATA;


#SCM CLEANUP
UPDATE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA
SET
    VENDOR_PRIMARY_CONTACT = 1111111111,
    VENDOR_SECONDARY_CONTACT = NULL,
    VENDOR_PRIMARY_EMAIL = '<EMAIL>',
    VENDOR_SECONDARY_EMAIL = NULL;

UPDATE KETTLE_MASTER_DEV.EMPLOYEE_DETAIL set EMP_EMAIL = "<EMAIL>";
UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL set UNIT_EMAIL = "<EMAIL>";

INSERT INTO `KETTLE_DEV`.`CUSTOMER_INFO` (`CUSTOMER_ID`, `FIRST_NAME`, `COUNTRY_CODE`, `CONTACT_NUMBER`, `IS_NUMBER_VERIFIED`, `IS_EMAIL_VERIFIED`, `ADD_TIME`, `EMAIL_VERIFICATION_TIME`, `ACQUISITION_SOURCE`, `ACQUISITION_TOKEN`, `REGISTRATION_UNIT_ID`, `IS_SMS_SUBSCRIBER`, `IS_EMAIL_SUBSCRIBER`, `IS_LOYALTY_SUBSCRIBER`, `IS_BLACKLISTED`, `IS_DND`, `IS_INTERNAL`, `REF_CODE`, `IS_REF_SUBSCRIBER`, `IS_REFERRER_AWARDED`, `ACQUISITION_BRAND_ID`, `IS_CHAAYOS_CUSTOMER`, `OPT_OUT_FACE_IT`, `OPT_OUT_TIME`) VALUES ('67456', 'Swiggy', '+91', '9599598307', 'N', 'Y', '2016-01-04 17:45:19', '2018-02-15 20:47:08', 'Chaayos-COD', '110016', '11001', 'N', 'Y', 'N', 'Y', 'Y', 'N', 'SWI8307', 'N', 'N', '1', 'Y', 'N', NULL);
INSERT INTO `KETTLE_DEV`.`CUSTOMER_INFO` (`CUSTOMER_ID`, `FIRST_NAME`, `MIDDLE_NAME`, `LAST_NAME`, `COUNTRY_CODE`, `CONTACT_NUMBER`, `IS_NUMBER_VERIFIED`, `ADD_TIME`, `ACQUISITION_SOURCE`, `ACQUISITION_TOKEN`, `IS_SMS_SUBSCRIBER`, `IS_EMAIL_SUBSCRIBER`, `IS_LOYALTY_SUBSCRIBER`, `IS_BLACKLISTED`, `IS_DND`, `IS_INTERNAL`, `IS_REF_SUBSCRIBER`, `IS_REFERRER_AWARDED`, `OPT_OUT_FACE_IT`, `ACQUISITION_BRAND_ID`, `IS_CHAAYOS_CUSTOMER`) VALUES ('5', 'Chaayos', 'Dev', 'Receipt', '+91', '2222222222', 'N', '2014-01-01 00:00:00', 'SQUARE_COINS', 'SQUARE_COINS', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', '1', 'Y');
INSERT INTO `KETTLE_DEV`.`CUSTOMER_INFO` (`CUSTOMER_ID`, `FIRST_NAME`, `LAST_NAME`, `COUNTRY_CODE`, `CONTACT_NUMBER`, `IS_NUMBER_VERIFIED`, `ADD_TIME`, `ACQUISITION_SOURCE`, `ACQUISITION_TOKEN`, `IS_SMS_SUBSCRIBER`, `IS_EMAIL_SUBSCRIBER`, `IS_LOYALTY_SUBSCRIBER`, `IS_BLACKLISTED`, `IS_DND`, `IS_INTERNAL`, `IS_REF_SUBSCRIBER`, `IS_REFERRER_AWARDED`, `OPT_OUT_FACE_IT`, `ACQUISITION_BRAND_ID`, `IS_CHAAYOS_CUSTOMER`) VALUES ('1', 'Chaayos', 'Receipt', '+91', '1111111111', 'N', '2014-01-01 00:00:00', 'SQUARE_COINS', 'SQUARE_COINS', 'Y', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', '1', 'Y');