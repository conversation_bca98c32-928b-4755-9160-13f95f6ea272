
INSERT INTO `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`, `EMPLOYEE_MEAL_COMPONENT`, `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `SUPPORTS_VARIANT_LEVEL_ORDERING`, `TAX_CODE`) VALUES ('14', '<PERSON><PERSON><PERSON> <PERSON> <PERSON>', 'Customise & discover 12000 ways to make your \'<PERSON><PERSON>', '5', '501', 'ACTIVE', '2018-09-15', '9999-12-01', 'CH1011014', '2', 'NET_PRICE', '4', '2018-09-15 00:00:00', '2030-12-01 05:30:00', 'N', 'Y', 'MENU', 'DCD', 'N', '00009963');
INSERT INTO `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL` (`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`, `PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`, `PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`, `EMPLOYEE_MEAL_COMPONENT`, `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `SUPPORTS_VARIANT_LEVEL_ORDERING`, `TAX_CODE`) VALUES ('15', 'Desi Chai - Double Dash Milk', 'Customise & discover 12000 ways to make your \'Meri Wali Chai', '5', '501', 'ACTIVE', '2018-09-15', '9999-12-01', 'CH1011015', '2', 'NET_PRICE', '4', '2018-09-15 00:00:00', '2030-12-01 05:30:00', 'N', 'Y', 'MENU', 'DCDD', 'N', '00009963');

INSERT INTO KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING(UNIT_ID,PRODUCT_ID,PRODUCT_STATUS,LAST_UPDATE_TMSTMP,PRODUCT_START_DATE,PRODUCT_END_DATE)
select upm.UNIT_ID, 14, 'ACTIVE', '2018-09-15 00:00:00', '2018-09-15', '9999-12-01' from KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm, KETTLE_MASTER_DEV.UNIT_DETAIL ud
where upm.UNIT_ID = ud.UNIT_ID and ud.UNIT_STATUS = 'ACTIVE'
and ud.UNIT_CATEGORY IN ('CAFE', 'EMPLOYEE_MEAL')
AND upm.PRODUCT_ID = 10;

INSERT INTO KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING(UNIT_ID,PRODUCT_ID,PRODUCT_STATUS,LAST_UPDATE_TMSTMP,PRODUCT_START_DATE,PRODUCT_END_DATE)
select upm.UNIT_ID, 15, 'ACTIVE', '2018-09-15 00:00:00', '2018-09-15', '9999-12-01' from KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm, KETTLE_MASTER_DEV.UNIT_DETAIL ud
where upm.UNIT_ID = ud.UNIT_ID and ud.UNIT_STATUS = 'ACTIVE'
and ud.UNIT_CATEGORY IN ('CAFE', 'EMPLOYEE_MEAL')
AND upm.PRODUCT_ID = 10;

INSERT INTO KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE,LAST_UPDATE_TMSTMP,PRICE,COST,COD_COST,BUFFER_QUANTITY,THRESHOLD_QUANTITY,RECIPE_PROFILE)
select upm1.UNIT_PROD_REF_ID, upp.DIMENSION_CODE,upp.LAST_UPDATE_TMSTMP,upp.PRICE,
upp.COST,upp.COD_COST,upp.BUFFER_QUANTITY,upp.THRESHOLD_QUANTITY,upp.RECIPE_PROFILE  from KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm,
KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING upp,
KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm1, KETTLE_MASTER_DEV.UNIT_DETAIL ud
where 
upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
AND upm.UNIT_ID = ud.UNIT_ID 
AND upm.UNIT_ID = upm1.UNIT_ID and ud.UNIT_STATUS = 'ACTIVE'
and upm1.PRODUCT_ID = 14
and ud.UNIT_CATEGORY IN ('CAFE', 'EMPLOYEE_MEAL')
AND upm.PRODUCT_ID = 10;

INSERT INTO KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING(UNIT_PROD_REF_ID,DIMENSION_CODE,LAST_UPDATE_TMSTMP,PRICE,COST,COD_COST,BUFFER_QUANTITY,THRESHOLD_QUANTITY,RECIPE_PROFILE)
select upm1.UNIT_PROD_REF_ID, upp.DIMENSION_CODE,upp.LAST_UPDATE_TMSTMP,upp.PRICE,
upp.COST,upp.COD_COST,upp.BUFFER_QUANTITY,upp.THRESHOLD_QUANTITY,upp.RECIPE_PROFILE  from KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm,
KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING upp,
KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING upm1, KETTLE_MASTER_DEV.UNIT_DETAIL ud
where 
upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
AND upm.UNIT_ID = ud.UNIT_ID 
AND upm.UNIT_ID = upm1.UNIT_ID and ud.UNIT_STATUS = 'ACTIVE'
and upm1.PRODUCT_ID = 15
and ud.UNIT_CATEGORY IN ('CAFE', 'EMPLOYEE_MEAL')
AND upm.PRODUCT_ID = 10;
