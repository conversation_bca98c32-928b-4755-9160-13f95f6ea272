
CREATE INDEX index_current_status ON KETTLE_DUMP.VOUCHER_DATA (CURRENT_STATUS) USING BTREE;
ALTER TABLE KETTLE_DUMP.VOUCHER_STATUS_DATA ADD INDEX index_from_status(FROM_STATUS) USING BTREE;
ALTER TABLE KETTLE_DUMP.VOUCHER_STATUS_DATA ADD INDEX index_to_status(TO_STATUS) USING BTREE;

INSERT INTO `KETTLE_MASTER_DEV`.`PAYMENT_MODE` (`PAYMENT_MODE_ID`, `MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`, `GENERATE_PULL`, `COMMISSION_RATE`, `MODE_STATUS`, `MODE_CATEGORY`, `AUTOMATIC_PULL_VALIDATE`, `AUTOMATIC_TRANSFER`, `AUTOMATIC_CLOSE_TRANSFER`, `IS_EDITABLE`, `NEEDS_SETTLEMENT_SLIP_NUMBER`, `VALIDATION_SOURCE`) VALUES ('26', 'AGSKioskOnline', 'CARD', 'AGS Kiosk Online', 'DEBIT', '1', '1.50', 'ACTIVE', 'ONLINE', 'Y', 'Y', 'N', 'N', 'N', 'Dashboard');
