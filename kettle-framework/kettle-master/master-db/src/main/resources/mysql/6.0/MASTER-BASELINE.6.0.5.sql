
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_OFFER_MAPPING_DATA
ADD COLUMN OFFER_DAY_COUNT INTEGER NULL,
ADD COLUMN OFFER_CODE_USED VARCHAR(1) NULL,
ADD COLUMN OFFER_STRATEGY VARCHAR(50) NULL,
ADD COLUMN LAST_UPDATE_TIME TIMESTAMP NULL,
ADD COLUMN ACQUISITION_SOURCE VARCHAR(50) NULL,
ADD COLUMN URL_END_POINT VARCHAR(200) NULL;



CREATE INDEX CUSTOMER_OFFER_MAPPING_DATA_OFFER_CODE_USED ON KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA (OFFER_CODE_USED) USING BTREE;
CREATE INDEX CUSTOMER_OFFER_MAPPING_DATA_ACQUISITION_SOURCE ON KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA(ACQUISITION_SOURCE) USING BTREE;
CREATE INDEX CUSTOMER_OFFER_MAPPING_DATA_OFFER_STRATEGY ON KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA(OFFER_STRATEGY) USING BTREE;

 
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA SET OFFER_DAY_COUNT = 365;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA SET OFFER_CODE_USED = 'Y';
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA SET OFFER_STRATEGY = 'HOURLY_FOR_EACH_UNIT';
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA SET ACQUISITION_SOURCE = 'CAFE_VISIT';


UPDATE `KETTLE_MASTER`.`OFFER_DETAIL_DATA` SET `OFFER_LAUNCH_START_DATE`='2019-04-07', `OFFER_LAUNCH_END_DATE`='2019-05-07', `OFFER_LAUNCH_STRATEGY`='N_DAY_FREE_ITEM' WHERE `OFFER_DETAIL_ID`='1447';


INSERT INTO KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA (OFFER_DETAIL_ID,UNIT_ID,COUPON_CODE, COUPON_DETAIL_ID, IS_NOTIFIED, OFFER_CODE_USED,OFFER_STRATEGY, URL_END_POINT)
select OFFER_DETAIL_ID,26094, COUPON_CODE, COUPON_DETAIL_ID, 'N','N', 'N_DAY_FREE_ITEM', 'platina' from KETTLE_MASTER.COUPON_DETAIL_DATA WHERE OFFER_DETAIL_ID = 1447;

select @MIN_VALUE := (MIn(CUSTOMER_OFFER_MAPPING_DATA_ID) -1) from KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA where OFFER_DETAIL_ID = 1447;

UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = NULL where OFFER_DETAIL_ID = 1447; 

UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 1 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((0*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((61*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 2 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((61*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((66*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 3 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((66*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((70*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 4 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((70*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((73*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 5 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((73*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((75*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 6 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((75*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((76*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 7 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((76*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((77*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 8 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((77*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((78*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 9 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((78*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((79*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 10 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((79*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((80*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 11 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((80*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((81*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 12 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((81*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((82*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 13 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((82*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((83*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 14 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((83*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((84*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 15 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((84*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((85*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 16 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((85*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((86*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 17 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((86*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((87*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 18 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((87*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((88*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 19 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((88*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((89*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 20 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((89*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((90*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 21 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((90*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((91*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 22 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((91*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((92*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 23 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((92*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((93*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 24 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((93*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((94*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 25 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((94*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((95*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 26 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((95*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((96*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 27 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((96*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((97*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 28 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((97*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((98*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 29 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((98*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((99*10000/100)) AND OFFER_DAY_COUNT IS NULL;
UPDATE KETTLE_MASTER.CUSTOMER_OFFER_MAPPING_DATA set OFFER_DAY_COUNT = 30 where OFFER_DETAIL_ID = 1447 and CUSTOMER_OFFER_MAPPING_DATA_ID >= (@MIN_VALUE + ROUND((99*10000/100)))+1 AND CUSTOMER_OFFER_MAPPING_DATA_ID <= @MIN_VALUE + ROUND((100*10000/100)) AND OFFER_DAY_COUNT IS NULL;
