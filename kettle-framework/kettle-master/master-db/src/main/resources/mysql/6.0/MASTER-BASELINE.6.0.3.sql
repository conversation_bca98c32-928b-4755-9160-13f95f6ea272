INSERT INTO KETTLE_MASTER_DEV.APPLICATION_DATA
(APPLICATION_NAME, APPLICATION_DESCRIPTION, APPLICATION_STATUS)
 VALUES ('SERVICE_ORDER', 'SERVICE_ORDER', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
(ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL, 'SOM', '12', 'MENU', 'VIEW', 'SERVICE ORDERING -> SERVICE ORDER MANAGEMENT -> VIEW', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
 (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL, 'CRESO', '12', 'SUBMENU', 'SHOW', 'SERVICE ORDERING -> CREATE SERVICE ORDER -> VIEW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
  VALUES (NULL, 'VWSO', '12', 'SUBMENU', 'SHOW', 'SERVICE ORDERING -> VIEW SERVICE ORDER -> VIEW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
  VALUES (NULL, 'APRSO', '12', 'SUBMENU', 'SHOW', 'SERVICE ORDERING -> APPROVE SERVICE ORDER -> VIEW', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
 (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL,'SOAPR', '12', 'ACTION', 'UPDATE', 'SERVICE ORDERING -> APPROVE SERVICE ORDER -> APPROVE', 'ACTIVE');

 INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
 (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL,'SOREJ', '12', 'ACTION', 'UPDATE', 'SERVICE ORDERING -> APPROVE SERVICE ORDER -> REJECT', 'ACTIVE');

 INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
 (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL,'SOCNCL', '12', 'ACTION', 'UPDATE', 'SERVICE ORDERING -> APPROVE SERVICE ORDER -> CANCEL', 'ACTIVE');

 INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
 (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL,'SOCLS', '12', 'ACTION', 'UPDATE', 'SERVICE ORDERING -> VIEW SERVICE ORDER -> CLOSE', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
(ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL, 'SRM', '12', 'MENU', 'VIEW', 'SERVICE RECEIVING -> SERVICE RECEIVING MANAGEMENT -> VIEW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
(ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL, 'CRESR', '12', 'SUBMENU', 'SHOW', 'SERVICE RECEIVING -> CREATE SERVICE RECEIVING -> VIEW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
(ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL, 'VSRCV', '12', 'SUBMENU', 'SHOW', 'SERVICE RECEIVING -> VIEW SERVICE RECEIVING -> VIEW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
(ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL, 'SRMNP', '12', 'MENU', 'SHOW', 'SERVICE RECEIVING -> MANAGE PAYMENTS -> VIEW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
(ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL, 'RSRPR', '12', 'SUBMENU', 'SHOW', 'SERVICE RECEIVING -> RAISE PAYMENT REQUEST -> VIEW', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL
(ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (NULL, 'SRCNCL', '12', 'ACTION', 'UPDATE', 'SERVICE RECEIVING -> SEARCH SERVICE RECEIVING -> VIEW', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS) 
VALUES ('CCMAP', '12', 'MENU', 'VIEW', 'Cost Center Mapping', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('CCC', '12', 'SUBMENU', 'SHOW', 'Cost Center Mapping ->Create Cost Center', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS) 
VALUES ('CCE', '12', 'SUBMENU', 'SHOW', 'Cost Center Mapping ->Create Cost Element', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS) 
VALUES ('UCCMAP', '12', 'SUBMENU', 'SHOW', 'Cost Center Mapping ->User Cost Center Mapping', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA
(ROLE_ID, ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS)
VALUES (NULL, 'SERVICE_ORDER_MANAGER', 'ACCESS TO RAISE AND APPROVE SERVICE ORDERS IN SUMO', 'ACTIVE');



INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
SELECT
  (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME="SERVICE_ORDER_MANAGER"),
    ACTION_DETAIL_ID,'ACTIVE',120502, CURRENT_TIMESTAMP()
FROM KETTLE_MASTER_DEV.ACTION_DETAIL A
INNER JOIN KETTLE_MASTER_DEV.APPLICATION_DATA B ON A.APPLICATION_ID = B.APPLICATION_ID
WHERE B.APPLICATION_NAME="SERVICE_ORDER";



INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP (RTL_ID, RL_CODE, RL_NAME, RL_SHORT_CODE, RL_STATUS)
SELECT RTL_ID, 'SERVICE_RECEIVED', 'SERVICE RECEIVED', 'SR', 'ACTIVE'
FROM KETTLE_MASTER_DEV.REF_LOOKUP_TYPE WHERE RTL_GROUP="PR_TYPE";


INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID, PERMISSION, ACL_ID, PPM_STATUS) 
VALUES ('7', '1111', '106', 'ACTIVE');



