INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP_TYPE` (`RTL_GROUP`, `RTL_CODE`, `RTL_NAME`, `STATUS`) 
VALUES ('ITEM_PER_TICKET', 'SEASONAL', 'Seasonal Products', 'ACTIVE');

#INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) 
#VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE WHERE RTL_GROUP = 'ITEM_PER_TICKET' AND RTL_CODE = 'SEASONAL'),
# '1280', 'LemonGrass', 'LG', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`PAYMENT_MODE` (`PAYMENT_MODE_ID`, `MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`, `GENERATE_PULL`, `COMMISSION_RATE`, `MODE_STATUS`, `MODE_CATEGORY`, `AUTOMATIC_PULL_VALIDATE`, `AUTOMATIC_TRANSFER`, `AUTOMATIC_CLOSE_TRANSFER`, `IS_EDITABLE`, `NEEDS_SETTLEMENT_SLIP_NUMBER`, `VALIDATION_SOURCE`) VALUES (NULL, 'PayTmKioskOnline', 'CARD', 'PayTm Kiosk Online', 'DEBIT', '1', '1.50', 'ACTIVE', 'ONLINE', 'Y', 'Y', 'N', 'N', 'N', 'Dashboard');
INSERT INTO `KETTLE_MASTER_DEV`.`PAYMENT_MODE` (`PAYMENT_MODE_ID`, `MODE_NAME`, `MODE_TYPE`, `MODE_DESCRIPTION`, `SETTLEMENT_TYPE`, `GENERATE_PULL`, `COMMISSION_RATE`, `MODE_STATUS`, `MODE_CATEGORY`, `AUTOMATIC_PULL_VALIDATE`, `AUTOMATIC_TRANSFER`, `AUTOMATIC_CLOSE_TRANSFER`, `IS_EDITABLE`, `NEEDS_SETTLEMENT_SLIP_NUMBER`, `VALIDATION_SOURCE`) VALUES (NULL, 'IngenicoOnline', 'CARD', 'Ingenico Online', 'DEBIT', '1', '1.50', 'ACTIVE', 'ONLINE', 'Y', 'Y', 'N', 'N', 'N', 'Dashboard');
ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN MAX_DISCOUNT_AMOUNT DECIMAL(10,2);
ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA MODIFY COLUMN OFFER_TYPE VARCHAR (50) NOT NULL;