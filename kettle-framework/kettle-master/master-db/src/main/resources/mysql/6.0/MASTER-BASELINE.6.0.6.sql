ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD OTP_REQUIRED VARCHAR(1) DEFAULT 'N';
CREATE TABLE KETTLE_MASTER_DEV.PRODUCT_SEQUENCE (
  PRODUCT_SEQUENCE_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  PRODUCT_ID INTEGER NOT NULL,
  PRODUCT_INDEX INTEGER NOT NULL,
  PRODUCT_GROUP_ID INTEGER NOT NULL,
  CREATED_BY INTEGER NOT NULL,
  CREATION_TIME TIMESTAMP NULL,
  LAST_UPDATE_TIME TIMESTAMP NULL,
  STATUS VARCHAR(20) NOT NULL
);

CREATE TABLE KETTLE_MASTER_DEV.PRODUCT_GROUP_DATA(
  PRODUCT_GROUP_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  GROUP_NAME VARCHAR(100) NOT NULL,
  GRO<PERSON>_TAG VARCHAR(100) NOT NULL,
  <PERSON><PERSON><PERSON>_DESCRIPTION VARCHAR(500) NOT NULL,
  CREATED_BY INTEGER NOT NULL,
  CREATION_TIME TIMESTAMP  NULL,
  STATUS VARCHAR(20) NOT NULL
);

CREATE TABLE KETTLE_MASTER_DEV.MENU_SEQUENCE_DATA (
  MENU_SEQUENCE_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  MENU_SEQUENCE_NAME VARCHAR(100),
  MENU_SEQUENCE_DESCRIPTION VARCHAR(500) NOT NULL,
  CREATED_BY INTEGER NOT NULL,
  CREATION_TIME TIMESTAMP NULL,
  LAST_UPDATE_TIME TIMESTAMP NULL
);

ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_SEQUENCE ADD COLUMN UPDATED_BY INTEGER NOT NULL;

CREATE TABLE KETTLE_MASTER_DEV.MENU_SEQUENCE_MAPPING_DATA (
  MENU_SEQUENCE_MAPPING_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
  MENU_SEQUENCE_ID INTEGER NOT NULL,
  PRODUCT_GROUP_ID INTEGER NOT NULL,
  PRODUCT_GROUP_PARENT_ID INTEGER NULL,
  GROUP_INDEX INTEGER NOT NULL,
  CREATED_BY INTEGER NOT NULL,
  UPDATED_BY INTEGER NOT NULL,
  CREATION_TIME TIMESTAMP NULL,
  LAST_UPDATE_TIME TIMESTAMP NULL
);
ALTER TABLE KETTLE_MASTER_DEV.MENU_SEQUENCE_MAPPING_DATA ADD COLUMN STATUS VARCHAR(20) NOT NULL;


CREATE TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING (
	ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    UNIT_CHANNEL_PARTNER_ID INTEGER NOT NULL,
    MENU_SEQUENCE_ID INTEGER NOT NULL,
    MENU_START_TIME VARCHAR(20) NULL,
    MENU_END_TIME VARCHAR(20) NULL,
    MENU_DAY INTEGER NULL,
    MENU_TYPE VARCHAR(20) NOT NULL,
    MENU_APP VARCHAR(20) NOT NULL
);

ALTER TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN CREATED_AT TIMESTAMP NULL;
ALTER TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN UPDATED_AT TIMESTAMP NULL;
ALTER TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN CREATED_BY INTEGER NOT NULL;
ALTER TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN UPDATED_BY INTEGER NOT NULL;
ALTER TABLE KETTLE_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING ADD COLUMN STATUS VARCHAR(20) NOT NULL;


INSERT INTO `KETTLE_MASTER_DEV`.`PRE_AUTHENTICATED_API` (`API`, `STATUS`) VALUES ('master-service.unit-metadata.area-manager-units', 'ACTIVE');
