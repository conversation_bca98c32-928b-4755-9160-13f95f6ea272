ALTER TABLE `KETTLE_MASTER_DEV`.`R<PERSON>_LOOKUP` MODIFY RL_SHORT_CODE varchar(10);
ALTER TABLE `KETTLE_MASTER_DEV`.`REF_LOOKUP` MODIFY RL_NAME varchar(50);


INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP_TYPE` (`RTL_GROUP`, `RTL_CODE`, `RTL_NAME`, `STATUS`) VALUES ('RECOMMENDATION', 'CORE_PRODUCTS', 'CORE_PRODUCTS', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP_TYPE` (`RTL_GROUP`, `RTL_CODE`, `RTL_NAME`, `STATUS`) VALUES ('RECOMMENDATION', 'CATEGORY_PRODUCTS', 'CATEGORY_PRODUCTS', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `R<PERSON>_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '10', 'Desi Chai', 'DC', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '130', 'Honey Ginger Lemon', 'HGL', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '1021', 'Filter Coffee', 'FC10', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '460', 'Modinagar Shikanji', 'MS', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '1179', 'Kulfi Shake', 'S20183', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '1156', 'Banana Walnut Cake', 'BWC', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '1037', 'Date & Fig Cake', 'CAKE02', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '868', 'Garden Fresh Sandwich', 'GF', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '1064', 'Kaala Chana Chaat', 'NPD317', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '670', 'Vada Pav', 'VP', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '690', 'Bun Maska', 'BM', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '680', 'Bun Bhujia', 'BB', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '660', 'Mom s Poha', 'POH', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '992', 'Bun Omelette', 'BO', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '1231', 'Chilli Cheese Toast', 'CCT', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '1201', 'Samosa Matar Chaat', 'SMC', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '1065', 'Palak Patta Crispies', 'NPD302', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '620', 'Homestyle Aloo Sandwich', 'ALO', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '640', 'Keema Pav', 'KP', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`) VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CORE_PRODUCTS'), '641', 'Bun Maska Keema', 'BML', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '80', 'Kulhad Chai', 'KUL', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '130', 'Honey Ginger Lemon', 'HGL', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '440', 'Badam Thandai', 'Thanda', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '1296', 'Kasundi Paneer Sandwich', 'KPS', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '1111', 'Paneer Loaded Open Paratha', 'NPDJU3', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '500', 'Sicilian Chicken Sandwich', 'SIC', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '1151', 'Loaded Open Chicken Keema Parantha', 'LOPCK', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '1045', 'Chocolate Factory', 'CAKE05', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`REF_LOOKUP` (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_CODE = 'CATEGORY_PRODUCTS'), '1165', 'Spiced Chai Patti - Adrak Elaichi Chai', 'CCB02', 'ACTIVE');



