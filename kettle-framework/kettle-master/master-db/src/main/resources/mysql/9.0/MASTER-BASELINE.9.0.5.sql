CREATE INDEX MENU_SEQUENCE_MAPPING_DATA_MENU_SEQUENCE_ID ON KETTLE_MASTER_DEV.MENU_SEQUENCE_MAPPING_DATA (MENU_SEQUENCE_ID) USING BTREE;
CREATE INDEX MENU_SEQUENCE_MAPPING_DATA_GROUP_INDEX ON KETTLE_MASTER_DEV.MENU_SEQUENCE_MAPPING_DATA (GROUP_INDEX) USING BTREE;
CREATE INDEX MENU_SEQUENCE_MAPPING_DATA_STATUS ON KETTLE_MASTER_DEV.MENU_SEQUENCE_MAPPING_DATA (STATUS) USING BTREE;

CREATE INDEX PRODUCT_GROUP_DATA_STATUS ON KETTLE_MASTER_DEV.PRODUCT_GROUP_DATA (STATUS) USING BTREE;

CREATE INDEX PRODUCT_SEQUENCE_PRODUCT_ID ON KETTLE_MASTER_DEV.PRODUCT_SEQUENCE (PRODUCT_ID) USING BTREE;
CREATE INDEX PRODUCT_SEQUENCE_PRODUCT_INDEX ON KETTLE_MASTER_DEV.PRODUCT_SEQUENCE (PRODUCT_INDEX) USING BTREE;
CREATE INDEX PRODUCT_SEQUENCE_PRODUCT_GROUP_ID ON KETTLE_MASTER_DEV.PRODUCT_SEQUENCE (PRODUCT_GROUP_ID) USING BTREE;
CREATE INDEX PRODUCT_SEQUENCE_STATUS ON KETTLE_MASTER_DEV.PRODUCT_SEQUENCE (STATUS) USING BTREE;

CREATE INDEX CATEGORY_TAX_DATA_CATEGORY_ID ON KETTLE_MASTER_DEV.CATEGORY_TAX_DATA (CATEGORY_ID) USING BTREE;
CREATE INDEX CATEGORY_TAX_DATA_TAX_ID ON KETTLE_MASTER_DEV.CATEGORY_TAX_DATA (TAX_ID) USING BTREE;
CREATE INDEX CATEGORY_TAX_DATA_COUNTRY_DETAIL_ID ON KETTLE_MASTER_DEV.CATEGORY_TAX_DATA (COUNTRY_DETAIL_ID) USING BTREE;
CREATE INDEX CATEGORY_TAX_DATA_STATE_DETAIL_ID ON KETTLE_MASTER_DEV.CATEGORY_TAX_DATA (STATE_DETAIL_ID) USING BTREE;
CREATE INDEX CATEGORY_TAX_DATA_TAX_STATUS ON KETTLE_MASTER_DEV.CATEGORY_TAX_DATA (TAX_STATUS) USING BTREE;
CREATE INDEX CATEGORY_TAX_DATA_START_DATE ON KETTLE_MASTER_DEV.CATEGORY_TAX_DATA (START_DATE) USING BTREE;


CREATE INDEX CATEGORY_ADDITIONAL_TAX_DATA_CATEGORY_ID ON KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA (CATEGORY_ID) USING BTREE;
CREATE INDEX CATEGORY_ADDITIONAL_TAX_DATA_TAX_ID ON KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA (TAX_ID) USING BTREE;
CREATE INDEX CATEGORY_ADDITIONAL_TAX_DATA_COUNTRY_DETAIL_ID ON KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA (COUNTRY_DETAIL_ID) USING BTREE;
CREATE INDEX CATEGORY_ADDITIONAL_TAX_DATA_STATE_DETAIL_ID ON KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA (STATE_DETAIL_ID) USING BTREE;
CREATE INDEX CATEGORY_ADDITIONAL_TAX_DATA_TAX_STATUS ON KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA (TAX_STATUS) USING BTREE;
CREATE INDEX CATEGORY_ADDITIONAL_TAX_DATA_START_DATE ON KETTLE_MASTER_DEV.CATEGORY_ADDITIONAL_TAX_DATA (START_DATE) USING BTREE;

CREATE INDEX TAX_CATEGORY_DATA_CATEGORY_CODE ON KETTLE_MASTER_DEV.TAX_CATEGORY_DATA (CATEGORY_CODE) USING BTREE;
CREATE INDEX TAX_CATEGORY_DATA_CATEGORY_STATUS ON KETTLE_MASTER_DEV.TAX_CATEGORY_DATA (CATEGORY_STATUS) USING BTREE;
CREATE INDEX TAX_CATEGORY_DATA_IS_EXEMPTED ON KETTLE_MASTER_DEV.TAX_CATEGORY_DATA (IS_EXEMPTED) USING BTREE;
CREATE INDEX TAX_CATEGORY_DATA_CATEGORY_TYPE ON KETTLE_MASTER_DEV.TAX_CATEGORY_DATA (CATEGORY_TYPE) USING BTREE;


CREATE INDEX PRODUCT_IMAGE_MAPPING_STATUS ON KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING (STATUS) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MAPPING(UNIT_ID,CHANNEL_PARTNER_ID,STATUS,DELIVERY_PARTNER_ID)
select UNIT_ID, 21, 'ACTIVE', 5 from KETTLE_MASTER_DEV.UNIT_DETAIL ud
where UNIT_STATUS = 'ACTIVE'
and UNIT_CATEGORY = 'CAFE'
and UNIT_NAME NOT LIKE '%cod%'
and UNIT_NAME NOT LIKE '%odc%'
and UNIT_NAME NOT LIKE '%dark%'
and UNIT_NAME NOT LIKE '%kitchen%'
and UNIT_NAME NOT LIKE '% dk %'
and UNIT_NAME NOT LIKE '%test%'
;

INSERT INTO KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING(RESTAURANT_ID,UNIT_ID,PARTNER_ID,PRICE_PROFILE_UNIT_ID,BRAND_ID,MAPPING_STATUS)
select UNIT_ID ,UNIT_ID , 21, UNIT_ID, 1, 'ACTIVE' from KETTLE_MASTER_DEV.UNIT_DETAIL ud
where UNIT_STATUS = 'ACTIVE'
and UNIT_CATEGORY = 'CAFE'
and UNIT_NAME NOT LIKE '%cod%'
and UNIT_NAME NOT LIKE '%odc%'
and UNIT_NAME NOT LIKE '%dark%'
and UNIT_NAME NOT LIKE '%kitchen%'
and UNIT_NAME NOT LIKE '% dk %'
and UNIT_NAME NOT LIKE '%test%'
;

INSERT INTO KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING(UNIT_CHANNEL_PARTNER_ID,MENU_SEQUENCE_ID,MENU_START_TIME,MENU_END_TIME,MENU_DAY,MENU_TYPE,
MENU_APP,STATUS,CREATED_AT,UPDATED_AT,CREATED_BY,UPDATED_BY,MENU_SEQUENCE_NAME,BRAND_ID)
select ID, 5, null, null, null,'DEFAULT', 'DINE_IN_APP', 'ACTIVE', '2020-05-01 00:00:00',
'2020-05-01 00:00:00', 100026, 100026, null, 1
  from  KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MAPPING
  where CHANNEL_PARTNER_ID = 21;
;

INSERT INTO KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING(PRODUCT_ID,IMAGE_URL,IMAGE_TYPE,STATUS,LAST_UPDATED_BY,LAST_UPDATION_TIME) VALUES
(1109, '1109_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(30, '30_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(130, '130_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(640, '640_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1179, '1179_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(460, '460_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1065, '1065_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1065, '1065_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1111, '1111_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1201, '1201_special_high_1.jpg','SPECIAL_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1109, '1109_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(30, '30_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(130, '130_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(640, '640_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1179, '1179_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(460, '460_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1065, '1065_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1065, '1065_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1111, '1111_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1201, '1201_special_low_1.jpg','SPECIAL_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1156, '1156_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(690, '690_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(992, '992_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1375, '1375_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1334, '1334_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1028, '1028_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1236, '1236_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(450, '450_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(660, '660_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(670, '670_trending_high_1.jpg','TRENDING_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1156, '1156_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(690, '690_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(992, '992_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1375, '1375_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1334, '1334_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1028, '1028_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1236, '1236_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(450, '450_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(660, '660_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(670, '670_trending_low_1.jpg','TRENDING_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1109, '1109_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1200, '1200_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1311, '1311_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1203, '1203_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1064, '1064_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1065, '1065_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1310, '1310_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(140, '140_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(271, '271_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(272, '272_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(60, '60_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1334, '1334_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(130, '130_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(150, '150_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1156, '1156_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1045, '1045_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1037, '1037_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1224, '1224_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1110, '1110_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1155, '1155_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1376, '1376_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1205, '1205_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(30, '30_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1375, '1375_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(170, '170_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(80, '80_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1114, '1114_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(20, '20_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1233, '1233_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1236, '1236_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1234, '1234_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1232, '1232_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(450, '450_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(460, '460_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1281, '1281_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(680, '680_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(690, '690_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(992, '992_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1113, '1113_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1231, '1231_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(640, '640_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(660, '660_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1150, '1150_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(670, '670_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(610, '610_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(868, '868_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(620, '620_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1296, '1296_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(530, '530_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1297, '1297_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(500, '500_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(540, '540_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1235, '1235_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1178, '1178_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(420, '420_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(480, '480_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1179, '1179_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(400, '400_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(370, '370_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(40, '40_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1109, '1109_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1200, '1200_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1311, '1311_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1203, '1203_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1064, '1064_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1065, '1065_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1310, '1310_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(140, '140_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(271, '271_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(272, '272_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(60, '60_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1334, '1334_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(130, '130_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(150, '150_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1156, '1156_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1045, '1045_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1037, '1037_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1224, '1224_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1110, '1110_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1155, '1155_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1060, '1060_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1061, '1061_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(310, '310_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(310, '310_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1376, '1376_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1205, '1205_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(30, '30_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1375, '1375_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(170, '170_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(80, '80_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1114, '1114_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(20, '20_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1233, '1233_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1236, '1236_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1234, '1234_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1232, '1232_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(450, '450_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(460, '460_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1281, '1281_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(680, '680_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(690, '690_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(992, '992_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1113, '1113_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1231, '1231_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(640, '640_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(660, '660_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1150, '1150_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(670, '670_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(610, '610_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(868, '868_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(620, '620_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1296, '1296_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(530, '530_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1297, '1297_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(500, '500_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(540, '540_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1235, '1235_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1178, '1178_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(420, '420_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(480, '480_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1179, '1179_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(400, '400_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(370, '370_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(40, '40_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1109, '1109_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1200, '1200_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1311, '1311_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1112, '1112_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1203, '1203_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1064, '1064_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1065, '1065_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1310, '1310_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1201, '1201_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(140, '140_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(271, '271_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(272, '272_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(60, '60_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1334, '1334_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(130, '130_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(150, '150_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1156, '1156_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1045, '1045_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1037, '1037_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1110, '1110_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1155, '1155_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1224, '1224_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1060, '1060_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1061, '1061_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(300, '300_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(310, '310_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1376, '1376_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1205, '1205_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(30, '30_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1375, '1375_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(170, '170_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(80, '80_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1114, '1114_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(20, '20_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1233, '1233_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1236, '1236_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1234, '1234_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1232, '1232_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(450, '450_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(460, '460_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1112, '1112_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1063, '1063_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1028, '1028_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1111, '1111_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1281, '1281_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(680, '680_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(690, '690_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(992, '992_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1113, '1113_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1231, '1231_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(640, '640_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(660, '660_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1150, '1150_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(670, '670_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(610, '610_list_menu_high_1.png','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(868, '868_list_menu_high_1.png','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(620, '620_list_menu_high_1.png','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1296, '1296_list_menu_high_1.png','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(530, '530_list_menu_high_1.png','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1297, '1297_list_menu_high_1.png','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(500, '500_list_menu_high_1.png','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(540, '540_list_menu_high_1.png','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1235, '1235_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1178, '1178_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(420, '420_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(480, '480_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1179, '1179_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(400, '400_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(370, '370_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(40, '40_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1365, '1365_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1366, '1366_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1238, '1238_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1239, '1239_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1004, '1004_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1003, '1003_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1003, '1003_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1005, '1005_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1303, '1303_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1304, '1304_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1370, '1370_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1171, '1171_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1169, '1169_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1170, '1170_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1369, '1369_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1154, '1154_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1368, '1368_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1367, '1367_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1283, '1283_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1057, '1057_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1289, '1289_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1165, '1165_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1166, '1166_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(730, '730_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(730, '730_grid_menu_high_1.jpg','GRID_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1365, '1365_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1366, '1366_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1238, '1238_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1239, '1239_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1004, '1004_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1003, '1003_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1005, '1005_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1303, '1303_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1304, '1304_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1370, '1370_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1169, '1169_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1171, '1171_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1170, '1170_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1369, '1369_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1154, '1154_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1368, '1368_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1367, '1367_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1283, '1283_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1057, '1057_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1289, '1289_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1165, '1165_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1166, '1166_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(730, '730_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(730, '730_list_menu_high_1.jpg','LIST_MENU_HIGH', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1365, '1365_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1366, '1366_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1238, '1238_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1239, '1239_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1004, '1004_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1003, '1003_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1005, '1005_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1303, '1303_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1304, '1304_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1370, '1370_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1171, '1171_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1169, '1169_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1170, '1170_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1369, '1369_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1154, '1154_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1368, '1368_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1367, '1367_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1283, '1283_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1057, '1057_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1289, '1289_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1165, '1165_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(1166, '1166_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(730, '730_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00'),
(730, '730_grid_menu_low_1.jpg','GRID_MENU_LOW', 'ACTIVE', '120057','2020-05-01 00:00:00');

Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "80,000 ways to personalize your cup of Chai, just the way you like it! Choose from Adrak, Tulsi, Cinnamon and 9 other immunity boosting add-ons" Where PRODUCT_ID = 10;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "80,000 ways to personalize your cup of Chai, just the way you like it! Choose from Adrak, Tulsi, Cinnamon and 9 other immunity boosting add-ons" Where PRODUCT_ID = 11;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "80,000 ways to personalize your cup of Chai, just the way you like it! Choose from Adrak, Tulsi, Cinnamon and 9 other immunity boosting add-ons" Where PRODUCT_ID = 12;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "80,000 ways to personalize your cup of Chai, just the way you like it! Choose from Adrak, Tulsi, Cinnamon and 9 other immunity boosting add-ons" Where PRODUCT_ID = 14;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "80,000 ways to personalize your cup of Chai, just the way you like it! Choose from Adrak, Tulsi, Cinnamon and 9 other immunity boosting add-ons" Where PRODUCT_ID = 15;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "80,000 ways to personalize your cup of Chai, just the way you like it! Choose from Adrak, Tulsi, Cinnamon and 9 other immunity boosting add-ons" Where PRODUCT_ID = 50;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "80,000 ways to personalize your cup of Chai, just the way you like it! Choose from Adrak, Tulsi, Cinnamon and 9 other immunity boosting add-ons" Where PRODUCT_ID = 70;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A cup full of good health and positivity to lift your spirits! Boost your immunity with antiviral properties of Green Tea." Where PRODUCT_ID = 170;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Strong dark Classic Assam tea" Where PRODUCT_ID = 1205;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A rich, thick, creamy indulgence - truly loyal Chai!" Where PRODUCT_ID = 1114;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Our special Saffron Masala Chai served in traditional Indian Kulhad" Where PRODUCT_ID = 80;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A perfect cup for Monsoons, with the freshness of Lemongrass and flavour of Ginger" Where PRODUCT_ID = 1282;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A perfect cup for Monsoons, with the freshness of Lemongrass and flavour of Ginger" Where PRODUCT_ID = 1292;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A perfect cup for Monsoons, with the freshness of Lemongrass and flavour of Ginger" Where PRODUCT_ID = 1293;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A perfect cup for Monsoons, with the freshness of Lemongrass and flavour of Ginger" Where PRODUCT_ID = 1294;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Our special Masala Chai served in a cutting glass " Where PRODUCT_ID = 30;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Refreshing Black Lemon Chai" Where PRODUCT_ID = 20;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Traditional Kashmiri Kahwa with cloves, cinnamon and cardamom" Where PRODUCT_ID = 150;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Nostalgia! Chai with a tangy mango flavour" Where PRODUCT_ID = 140;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Thick and luscious hot chocolate. Feels like a hug in a mug!" Where PRODUCT_ID = 272;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A soothing blend and a perfect tonic to kick the cold" Where PRODUCT_ID = 130;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Tea inspired from the hills, with the aroma of Kangra Masala. Rich in antioxidants. " Where PRODUCT_ID = 60;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "The ultimate taste of South India - Coffee or KAAPI just the way you like it!" Where PRODUCT_ID = 271;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "The classic home remedy to boost immunity" Where PRODUCT_ID = 1334;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Goodness of Mangoes with freshness of Iced Tea" Where PRODUCT_ID = 1060;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Your favourite Chuski flavour in an Iced Tea avatar" Where PRODUCT_ID = 1061;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Lemon flavoured Iced Tea to revitalise your senses" Where PRODUCT_ID = 300;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Peach flavoured Iced Tea to refresh your mood" Where PRODUCT_ID = 310;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "The celebrated Modinagar Shikanji to electrify your body and mind" Where PRODUCT_ID = 460;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Refreshing summer cooler with invigorating mint flavour" Where PRODUCT_ID = 450;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Classic Indian beverage in Mint-masala flavour" Where PRODUCT_ID = 1234;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Classic Indian beverage in gratifying strawberry flavour" Where PRODUCT_ID = 1232;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Classic Indian beverage in pleasing Litchi flavour" Where PRODUCT_ID = 1233;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Classic Indian beverage in an indulging Mango flavour" Where PRODUCT_ID = 1236;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "An ultimate bliss for all the chocolate lovers" Where PRODUCT_ID = 420;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "The awesomeness of Chocolate Cake served in the form of a shake" Where PRODUCT_ID = 1178;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "The freshness of Kulfi served in the form of a shake" Where PRODUCT_ID = 1179;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "An all-time favourite summer treat " Where PRODUCT_ID = 370;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Healthy and creamy shake with fresh Banana pulp and Caramel flavour" Where PRODUCT_ID = 1235;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Mandatory summer indulgence - sweet & juicy Mango Shake! " Where PRODUCT_ID = 400;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Our Masala Chai Frappe, a Firangi twist to the classic Masala Chai! " Where PRODUCT_ID = 40;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Simple and refreshing Cold Coffee" Where PRODUCT_ID = 480;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Melt-in mouth juicy Paneer Bhurji, lavishly heaped over two soft open buns" Where PRODUCT_ID = 1150;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Perfect fix for your all-time hunger pangs" Where PRODUCT_ID = 1231;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Fluffy Omelette topped with our secret spice inside a Bun" Where PRODUCT_ID = 992;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Melt-in-mouth Mutton keema in a pav, served with Mint chutney" Where PRODUCT_ID = 640;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Home-style Poha with the tanginess of Lemon & crispiness of Peanuts" Where PRODUCT_ID = 660;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "An all-time snack - spicy samosa with chatpati Imli sauce in bun" Where PRODUCT_ID = 1113;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Every Mumbaikar's favourite - spicy Potato vada in a Pav, served with Mint chutney " Where PRODUCT_ID = 670;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Everyday comfort food - bun maska with crunchy Bhujia" Where PRODUCT_ID = 680;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Freshly toasted bun with a more than generous dollop of butter" Where PRODUCT_ID = 690;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Melt-in-mouth Mutton keema filled in a soft buttered bun, served with Mint chutney" Where PRODUCT_ID = 641;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Crispy Papdi (4 pieces) topped with achari paneer and dal fusion" Where PRODUCT_ID = 1310;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Crispy Papdi (4 pieces) topped with juicy chicken and dal fusion" Where PRODUCT_ID = 1311;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "An assorted platter with onion, paneer & chilly pakoras" Where PRODUCT_ID = 1109;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Chatpati crispy Palak chaat (6 pieces) with Jalapenos and Imli chutney" Where PRODUCT_ID = 1065;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Crispy and crumbled Samosa topped with spicy Amritsari Matar and tangy Imli chutney" Where PRODUCT_ID = 1201;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Tasty combination of chopped boiled eggs with Chaat ingredients" Where PRODUCT_ID = 1203;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A healthy summer snack! Tangy black chickpeas, onion, and tomatoes flavoured with spices" Where PRODUCT_ID = 1064;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Tangy & spicy Bombay bhel " Where PRODUCT_ID = 1200;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Flavourful Paneer cubes in our secret Kasundi sauce (Bengali Mustard) served with a cheese slice in the bread of your choice " Where PRODUCT_ID = 1296;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Spicy Italian style grilled chicken in a Focaccia bread" Where PRODUCT_ID = 500;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Spicy Patiala chicken chunks in a cheesy Sandwich" Where PRODUCT_ID = 1297;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Cheesy garlic roasted mushrooms with Italian herbs in a Focaccia bread" Where PRODUCT_ID = 530;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Fresh Spinach & crispy Corn sandwiched with melted cheese!" Where PRODUCT_ID = 540;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Ultimate healthy sandwich with fresh & crunchy veggies " Where PRODUCT_ID = 868;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Masaledar MAGGI with Crunchy veggies in a Sandwich " Where PRODUCT_ID = 610;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Ghar Jaisa Aloo Sandwich, guaranteed to remind you of Mom." Where PRODUCT_ID = 620;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Delectable chicken chunks & gravy spread on a crispy thin Paratha topped with oodles of Cheese!" Where PRODUCT_ID = 1112;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Delectable chicken keema & gravy spread on a crispy thin Paratha topped with oodles of Cheese!" Where PRODUCT_ID = 1151;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Delectable cottage cheese & gravy spread on a crispy thin Paratha topped with oodles of Cheese!" Where PRODUCT_ID = 1111;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "All time favourite spicy and buttery Pav Bhaji served in a Kulhad" Where PRODUCT_ID = 1028;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Delhi's classic street food served in a Kulhad" Where PRODUCT_ID = 1063;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Classic Mac & Cheese with garlic roasted mushrooms and Italian herbs" Where PRODUCT_ID = 1281;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Dense, dark & devilish chocolate cake slice" Where PRODUCT_ID = 1045;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A wholesome indulgence with real Dates & Figs" Where PRODUCT_ID = 1037;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Sweetness of banana & crunch of nuts in a cake slice" Where PRODUCT_ID = 1156;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Delectable Chocolate Cake - Soft inside, sinful outside" Where PRODUCT_ID = 1155;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "A perfect winter indulgence, served in a Kulhad" Where PRODUCT_ID = 1224;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Sweet & hot Jalebis served in an earthy Kulhad" Where PRODUCT_ID = 1110;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Perfect Chai companion, crisp and light." Where PRODUCT_ID = 1003;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Crispy, melt-in-mouth cookies with rich dry fruits" Where PRODUCT_ID = 1004;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Baked, wholesome cookies to go well with your perfect cup of Chai!" Where PRODUCT_ID = 1005;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Baked chocolate chip cookies with ultimate crispy edges and chewy middles!" Where PRODUCT_ID = 1238;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Baked soft and chewy on the inside and crunchy on the outside coconut cookies" Where PRODUCT_ID = 1239;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Safe & hygienic bottle of water to quench your thirst" Where PRODUCT_ID = 1154;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Improve immunity & build your body defence with this tasty 100% natural coconut water natural blend. No preservative, no added sugar!" Where PRODUCT_ID = 1366;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Boost your immunity & also get a glowing skin with this tasty 100% natural coconut water based blend rich in antioxidants. No preservative, no added sugar!" Where PRODUCT_ID = 1365;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Authentic sweet with the  goodness of Jaggery & flavour of Cardamom" Where PRODUCT_ID = 1169;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Flaky & Crispy Methi Matthi, perfect Chai time Snack" Where PRODUCT_ID = 1170;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Sweet & Spicy chivda mixture with dry fruits & raisins" Where PRODUCT_ID = 1171;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Mini version of India's favourite snack, spicy-sweet flavour bursting out with every bite" Where PRODUCT_ID = 1369;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Sweet and spicy bite-sized spirals that taste perfect with a hot cup of Chai" Where PRODUCT_ID = 1370;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Munch on these gourmet popcorns coated with signature handcrafted cheddar cheese, sprinkled with smoky hint of Hawaiian sweetness." Where PRODUCT_ID = 1368;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Munch on these gourmet popcorns with signature handcrafted caramel sprinkled with Himalayan Salt" Where PRODUCT_ID = 1367;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Ready to mix Chai powder. Just add hot water & enjoy your favourite Chai on the go." Where PRODUCT_ID = 1057;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Ready to mix Chai powder. Just add hot water & enjoy your favourite Adrak Elaichi Chai on the go." Where PRODUCT_ID = 1283;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Masala Chai Tea Bag with Ginger, Cinnamon, Black Pepper, and Clove whole" Where PRODUCT_ID = 1289;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Perfect blend of finest tea leaves and dried natural spices recreating the classic taste and aroma of India’s favourite Masala Chai" Where PRODUCT_ID = 1164;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Great Chai blend with sweetness of Elaichi & punch of Adrak" Where PRODUCT_ID = 1165;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Perfect blend of Assam tea leaves, dried ginger flakes and Tulsi (holy basil) to recreate your favourite Adrak-Tulsi Chai" Where PRODUCT_ID = 1166;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Our unique blend of Spices to create your perfect Masala Chai" Where PRODUCT_ID = 1168;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Our unique blend of Spices to create your perfect Masala Chai" Where PRODUCT_ID = 730;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Perfect gift for every Chai lover. Premium blend of Adrak Elaichi Chai in a Beautiful Metallic Tin Box along with a ceramic Kulhad." Where PRODUCT_ID = 1303;
Update KETTLE_MASTER_DEV.PRODUCT_DETAIL Set PRODUCT_DESCRIPTION = "Perfect gift for every Chai lover. Premium blend of Adrak Elaichi Chai and Organic Green Tea in a Beautiful Metallic Tin Box along with two ceramic Kulhad." Where PRODUCT_ID = 1304;

CREATE INDEX  APP_OFFER_MAPPING_DATA_MAPPING_TYPE ON KETTLE_MASTER_DEV.APP_OFFER_MAPPING_DATA(MAPPING_TYPE) USING BTREE;
CREATE INDEX  APP_OFFER_MAPPING_DATA_MAPPING_VALUE ON KETTLE_MASTER_DEV.APP_OFFER_MAPPING_DATA(MAPPING_VALUE) USING BTREE;
CREATE INDEX  APP_OFFER_MAPPING_DATA_MAPPING_STATUS ON KETTLE_MASTER_DEV.APP_OFFER_MAPPING_DATA(MAPPING_STATUS) USING BTREE;
CREATE INDEX  APP_OFFER_DETAIL_DATA_OFFER_INDEX ON KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA(OFFER_INDEX) USING BTREE;
CREATE INDEX  APP_OFFER_DETAIL_DATA_START_DATE ON KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA(START_DATE) USING BTREE;
CREATE INDEX  APP_OFFER_DETAIL_DATA_END_DATE ON KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA(END_DATE) USING BTREE;
CREATE INDEX  APP_OFFER_DETAIL_DATA_COUPON_CODE ON KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA(COUPON_CODE) USING BTREE;
CREATE INDEX  APP_OFFER_DETAIL_DATA_STATUS ON KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA(STATUS) USING BTREE;
CREATE INDEX  APP_OFFER_DETAIL_ACTION_TYPE ON KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA(ACTION_TYPE) USING BTREE;
CREATE INDEX  APP_OFFER_DETAIL_OFFER_TYPE ON KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA(OFFER_TYPE) USING BTREE;

-- Webp Image Product_Image_Mapping Update
INSERT INTO KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING (PRODUCT_ID, IMAGE_URL, IMAGE_TYPE, STATUS, LAST_UPDATED_BY, LAST_UPDATION_TIME, PRODUCT_INDEX)
(SELECT PRODUCT_ID, IMAGE_URL, "GRID_MENU_LOW_WEBP", STATUS, LAST_UPDATED_BY, LAST_UPDATION_TIME, PRODUCT_INDEX
FROM KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING WHERE IMAGE_TYPE="GRID_MENU_LOW");

UPDATE KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING E
SET E.IMAGE_URL = replace(E.IMAGE_URL, '.jpg','.webp')
WHERE E.IMAGE_TYPE="GRID_MENU_LOW_WEBP";