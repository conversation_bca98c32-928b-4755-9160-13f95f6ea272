INSERT INTO `KETTL<PERSON>_MASTER_DEV`.`ACCESS_CONTROL_LIST_DATA`
(`ACL_ID`, `ACL_MODULE`, `ACL_MODULE_DESCRIPTION`, `ACL_STATUS`, `APPLICATION_NAME`) VALUES
('155', 'offer-service.*', 'Offer Service', 'ACTIVE', 'OFFER_SERVICE');


INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING`
(`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('15', '1111', '155', 'ACTIVE');
ALTER TABLE KETTLE_MASTER_DEV.PRODUCT_GROUP_DATA
ADD COLUMN MENU_APP VARCHAR(45)  NULL;

ALTER TABLE KETTLE_MASTER_DEV.MENU_SEQUENCE_DATA
ADD COLUMN MENU_APP VARCHAR(45)  NULL;


UPDATE  KETTLE_MASTER_DEV.MENU_SEQUENCE_DATA MENU
JOIN KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING MAPPING
ON MAPPING.MENU_SEQUENCE_ID=MENU.MENU_SEQUENCE_ID
SET MENU.MENU_APP=MAPPING.MENU_APP;


UPDATE  KETTLE_MASTER_DEV.PRODUCT_GROUP_DATA PRODUCT
JOIN KETTLE_MASTER_DEV.MENU_SEQUENCE_MAPPING_DATA MAPPING
ON MAPPING.PRODUCT_GROUP_ID=PRODUCT.PRODUCT_GROUP_ID
JOIN KETTLE_MASTER_DEV.MENU_SEQUENCE_DATA MENU
ON MAPPING.MENU_SEQUENCE_ID=MENU.MENU_SEQUENCE_ID
SET PRODUCT.MENU_APP=MENU.MENU_APP;
