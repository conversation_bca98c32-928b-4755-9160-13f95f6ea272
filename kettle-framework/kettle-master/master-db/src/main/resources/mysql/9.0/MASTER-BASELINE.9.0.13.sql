CREATE TABLE KETTLE_MASTER_DEV.BANNER_DETAIL_DATA(
	BANNER_DETAIL_DATA_ID INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    BANNER_TYPE VARCHAR(40) NOT NULL,
    BANNER_TITLE VARCHAR(100) NOT NULL,
    BANNER_SUB_TITLE VARCHAR(100),
    BANNER_DESCRIPTION VARCHAR(100),
    BANNER_IMAGE_URL VARCHAR(255) NOT NULL,
    BANNER_BUTTON_TEXT VARCHAR(50),
    BANNER_BUTTON_ACTION VARCHAR(50),
    BANNER_ACTIVATION_TIME TIMESTAMP NOT NULL,
    BANNER_EXPIRY_TIME TIMESTAMP,
    BANNER_CODE VARCHAR(40),
    CREATED_BY INT NOT NULL,
    CREATED_ON TIMESTAMP NOT NULL,
    BANNER_STATUS VARCHAR(10) NOT NULL
);


CREATE TABLE `KETTLE_MASTER_DEV`.`PRODUCT_GROUP_IMAGE_DATA` (
  `PRODUCT_GROUP_IMAGE_ID` INT NOT NULL AUTO_INCREMENT ,
  `ICON_NAME` VARCHAR(45) NOT NULL ,
  `ICON_DESCRIPTION` VARCHAR(255) NOT NULL ,
  `ICON_URL` VARCHAR(500) NOT NULL ,
  `STATUS` VARCHAR(45) NOT NULL ,
  `CREATED_BY` INT NOT NULL,
  `CREATION_TIME` TIMESTAMP NOT NULL,
  PRIMARY KEY (`PRODUCT_GROUP_IMAGE_ID`) );


ALTER TABLE `KETTLE_MASTER_DEV`.`PRODUCT_GROUP_DATA`
ADD COLUMN `PRODUCT_GROUP_IMAGE_ID` INT,
ADD INDEX `PRODUCT_GROUP_IMAGE_ID_idx` (`PRODUCT_GROUP_IMAGE_ID` ASC);
;
ALTER TABLE `KETTLE_MASTER_DEV`.`PRODUCT_GROUP_DATA`
ADD CONSTRAINT `PRODUCT_GROUP_IMAGE_ID`
  FOREIGN KEY (`PRODUCT_GROUP_IMAGE_ID`)
  REFERENCES `KETTLE_MASTER_DEV`.`PRODUCT_GROUP_IMAGE_DATA` (`PRODUCT_GROUP_IMAGE_ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;



ALTER TABLE `KETTLE_MASTER_DEV`.`BANNER_DETAIL_DATA`
CHANGE COLUMN `BANNER_TITLE` `BANNER_TITLE` VARCHAR(100) NULL ;

ALTER TABLE KETTLE_MASTER_DEV.BANNER_DETAIL_DATA
ADD COLUMN SECTION_TYPE VARCHAR (50),
ADD COLUMN PRODUCT_ID INT ,
ADD COLUMN CATEGORY_ID INT;

