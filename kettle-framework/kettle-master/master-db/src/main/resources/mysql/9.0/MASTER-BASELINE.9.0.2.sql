INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING`
(`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`)
VALUES ('15', '1111', '105', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING`
(`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES
('15', '1111', '119', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING`
(`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`)
VALUES ('15', '1111', '121', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING`
(`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`)
VALUES ('15', '1111', '120', 'ACTIVE');


INSERT INTO `KETTLE_MASTER_DEV`.`PARTNER_PERMISSION_MAPPING`
(`PARTNER_ID`, `PERMISSION`, `ACL_ID`, `PPM_STATUS`) VALUES ('15', '1111', '122', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`PRODUCT_DETAIL`
(`PRODUCT_ID`, `PRODUCT_NAME`, `PRODUCT_DESCRIPTION`, `PRODUCT_TYPE`, `PRODUCT_SUB_TYPE`,
`PRODUCT_STATUS`, `PRODUCT_START_DATE`, `PRODUCT_END_DATE`, `PRODUCT_SKU_CODE`, `DIMENSION_CODE`,
`PRICE_TYPE`, `ADDITIONAL_ITEM_TYPES`, `IN_TMSTMP`, `OUT_TMSTMP`, `IS_INVENTORY_TRACKED`,
`EMPLOYEE_MEAL_COMPONENT`, `PRODUCT_CLASSIFICATION`, `SHORT_CODE`, `SUPPORTS_VARIANT_LEVEL_ORDERING`,
`WEB_CATEGORY_TYPE`, `TAX_CODE`, `SHORT_NAME`)
VALUES ('1', 'Pseudo Gift Card', 'Pseudo Gift Card', '9', '904', 'ACTIVE', '2016-10-22', '2069-10-31',
'GV1000', '1', 'ZERO_TAX', '4', '2019-12-05 22:23:13', '2030-12-01 00:00:00', 'N', 'N', 'MENU', 'GV1000',
'N', '3632', 'GIFT_CARD', 'Gift Card');
