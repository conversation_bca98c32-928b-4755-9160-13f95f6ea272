DROP TABLE IF EXISTS KETTLE_MASTER_DEV.MENU_RECOMMENDATION_DATA;
CREATE TABLE KETTLE_MASTER_DEV.MENU_RECOMMENDATION_DATA(
MENU_RECOMMENDATION_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
MENU_RECOMMENDATION_NAME VARCHAR(100),
MENU_RECOMMENDATION_DESCRIPTION VARCHAR(500) NULL,
RECOMMENDATION_TYPE VARCHAR(50) NOT NULL, #MENU, CART 
CREATED_BY INTEGER NULL,
CREATION_TIME TIMESTAMP NULL,
LAST_UPDATE_TIME TIMESTAMP NULL,
<PERSON><PERSON><PERSON>E_ID INTEGER NULL,
STATUS VARCHAR(15) NULL);

DROP TABLE IF EXISTS KETTLE_MASTER_DEV.MENU_RECOMMENDATION_MAPPING_DATA;
CREATE TABLE KETTLE_MASTER_DEV.MENU_RECOMMENDATION_MAPPING_DATA(
MENU_RECOMMENDATION_MAPPING_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
MENU_RECOMMENDATION_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
DIMENSION VARCHAR(50) NULL,
RECOMMENDED_PRODUCT_ID INTEGER NOT NULL,
RECOMMENDED_DIMENSION VARCHAR(50) NULL,
SEQUENCE_INDEX INTEGER NOT NULL,
CREATED_BY INTEGER NULL,
UPDATED_BY INTEGER NULL,
CREATION_TIME TIMESTAMP NULL,
LAST_UPDATE_TIME TIMESTAMP NULL,
STATUS VARCHAR(15) NULL);

ALTER TABLE KETTLE_MASTER_DEV.UNIT_CHANNEL_PARTNER_MENU_MAPPING
ADD COLUMN CART_RECOMMENDATION_SEQUENCE_ID INTEGER NULL,
ADD COLUMN MENU_RECOMMENDATION_SEQUENCE_ID INTEGER NULL;


INSERT INTO KETTLE_MASTER_DEV.MENU_RECOMMENDATION_DATA(MENU_RECOMMENDATION_NAME, MENU_RECOMMENDATION_DESCRIPTION, RECOMMENDATION_TYPE
, CREATED_BY, CREATION_TIME
, LAST_UPDATE_TIME
, CLONE_ID
, STATUS)
VALUES
('Dine In App Recommendation - All Day - All Region','Dine In App Recommendation - All Day - All Region',
'MENU',
'120057', '2020-09-10 00:00:00',
NULL,
NULL,
'ACTIVE'
);

INSERT INTO KETTLE_MASTER_DEV.MENU_RECOMMENDATION_MAPPING_DATA(MENU_RECOMMENDATION_ID, PRODUCT_ID, DIMENSION, RECOMMENDED_PRODUCT_ID, RECOMMENDED_DIMENSION, SEQUENCE_INDEX, CREATED_BY, UPDATED_BY, CREATION_TIME, LAST_UPDATE_TIME, STATUS)
VALUES
(1,1376,NULL,690, NULL,1,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE'),
(1,1376,NULL,1028, NULL,2,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE'),
(1,1376,NULL,500, NULL,3,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE'),
(1,1376,NULL,1109, NULL,4,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE'),
(1,1376,NULL,1112, NULL,5,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE'),
(1,1376,NULL,540, NULL,6,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE'),
(1,1376,NULL,670, NULL,7,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE'),
(1,1024,NULL,80, NULL,1,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE'),
(1,1024,NULL,1376, NULL,2,120057, NULL, '2020-09-10 00:00:00', NULL, 'ACTIVE')
;


CREATE INDEX MENU_RECOMMENDATION_MAPPING_DATA_MENU_RECOMMENDATION_ID 
ON KETTLE_MASTER_DEV.MENU_RECOMMENDATION_MAPPING_DATA(MENU_RECOMMENDATION_ID) 
USING BTREE;

ALTER TABLE `KETTLE_DEV`.`ORDER_DETAIL` 
ADD COLUMN `PARTNER_CUSTOMER_ID` VARCHAR(45) NULL;
