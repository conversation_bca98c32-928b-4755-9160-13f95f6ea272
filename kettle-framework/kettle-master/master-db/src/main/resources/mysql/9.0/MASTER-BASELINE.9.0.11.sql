ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN CAFE_APP_STATUS VARCHAR(10) NOT NULL,
ADD COLUMN CAFE_NEO_STATUS VARCHAR(10) NOT NULL;


UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL u
SET CAFE_NEO_STATUS="ACTIVE"
 WHERE u.UNIT_ID = '26164' OR ( u.IS_LIVE = 'Y' AND u.UNIT_CATEGORY = 'CAFE' AND u.UNIT_STATUS = 'ACTIVE' AND (u.UNIT_NAME NOT LIKE '%cod%' OR u.UNIT_NAME NOT LIKE '% dk %'
OR u.UNIT_NAME NOT LIKE '%test%' OR u.UNIT_NAME NOT LIKE '%odc%' OR u.UNIT_NAME NOT LIKE '%kitchen%' OR u.UNIT_NAME NOT LIKE '%dark%'));


UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL
SET CAFE_NEO_STATUS="IN_ACTIVE"
WHERE UNIT_ID NOT IN ( select u_id from  (select u.UNIT_ID as u_id FROM KETTLE_MASTER_DEV.UNIT_DETAIL u
 WHERE u.UNIT_ID = '26164' OR ( u.IS_LIVE = 'Y' AND u.UNIT_CATEGORY = 'CAFE' AND u.UNIT_STATUS = 'ACTIVE' AND
 (u.UNIT_NAME NOT LIKE '%cod%' OR u.UNIT_NAME NOT LIKE '% dk %'
OR u.UNIT_NAME NOT LIKE '%test%' OR u.UNIT_NAME NOT LIKE '%odc%' OR u.UNIT_NAME NOT LIKE '%kitchen%' OR u.UNIT_NAME NOT LIKE '%dark%')) ) as unit );

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL u
SET CAFE_APP_STATUS="ACTIVE"
 WHERE u.UNIT_ID = '26164' OR ( u.IS_LIVE = 'Y' AND u.UNIT_CATEGORY = 'CAFE' AND u.UNIT_STATUS = 'ACTIVE' AND (u.UNIT_NAME NOT LIKE '%cod%' OR u.UNIT_NAME NOT LIKE '% dk %'
OR u.UNIT_NAME NOT LIKE '%test%' OR u.UNIT_NAME NOT LIKE '%odc%' OR u.UNIT_NAME NOT LIKE '%kitchen%' OR u.UNIT_NAME NOT LIKE '%dark%'));

UPDATE KETTLE_MASTER_DEV.UNIT_DETAIL
SET CAFE_APP_STATUS="IN_ACTIVE"
WHERE UNIT_ID NOT IN ( select u_id from  (select u.UNIT_ID as u_id FROM KETTLE_MASTER_DEV.UNIT_DETAIL u
 WHERE u.UNIT_ID = '26164' OR ( u.IS_LIVE = 'Y' AND u.UNIT_CATEGORY = 'CAFE' AND u.UNIT_STATUS = 'ACTIVE' AND
 (u.UNIT_NAME NOT LIKE '%cod%' OR u.UNIT_NAME NOT LIKE '% dk %'
OR u.UNIT_NAME NOT LIKE '%test%' OR u.UNIT_NAME NOT LIKE '%odc%' OR u.UNIT_NAME NOT LIKE '%kitchen%' OR u.UNIT_NAME NOT LIKE '%dark%')) ) as unit );
