DROP TABLE KETTLE_MASTER_DEV.`APP_OFFER_DETAIL_DATA`;
CREATE TABLE `KETTLE_MASTER_DEV`.`APP_OFFER_DETAIL_DATA` (
  `ID` INT NOT NULL AUTO_INCREMENT COMMENT '',
  `OFFER_TYPE` VARCHAR(45) NULL COMMENT '',
  `OFFER_INDEX` INT NULL COMMENT '',
  `DESCRIPTION` VARCHAR(255) NULL COMMENT '',
  `START_DATE` DATE NULL COMMENT '',
  `END_DATE` DATE NULL COMMENT '',
  `COUPON_CODE` VARCHAR(45) NULL COMMENT '',
  `OFFER_ID` INT NULL COMMENT '',
  `COUPON_ID` INT NULL COMMENT '',
  `LIST_IMAGE` VARCHAR(100) NULL COMMENT '',
  `LIST_IMAGE_URL` VARCHAR(500) NULL COMMENT '',
  `GRID_IMAGE` VARCHAR(100) NULL COMMENT '',
  `GRID_IMAGE_URL` VARCHAR(500) NULL COMMENT '',
  `ACTION_TYPE` VARCHAR(45) NULL COMMENT '',
  `STATUS` VARCHAR(45) NULL COMMENT '',
  `CREATED_BY` VARCHAR(45) NULL COMMENT '',
  `CREATE_TIME` TIMESTAMP NULL COMMENT '',
  `UPDATED_BY` VARCHAR(45) NULL COMMENT '',
  `UPDATE_TIME` TIMESTAMP NULL COMMENT '',
  PRIMARY KEY (`ID`)  COMMENT '',
  UNIQUE INDEX `ID_UNIQUE` (`ID` ASC)  COMMENT '');

DROP TABLE KETTLE_MASTER_DEV.`APP_OFFER_MAPPING_DATA`;
CREATE TABLE `KETTLE_MASTER_DEV`.`APP_OFFER_MAPPING_DATA` (
  `OFFER_MAPPING_ID` INT NOT NULL AUTO_INCREMENT COMMENT '',
  `APP_OFFER_ID` INT NULL COMMENT '',
  `MAPPING_TYPE` VARCHAR(45) NULL COMMENT '',
  `MAPPING_VALUE` INT NULL COMMENT '',
  `MAPPING_UNIT_NAME` VARCHAR(100) NULL COMMENT '',
  `MAPPING_STATUS` VARCHAR(45) NULL COMMENT '',
  `UPDATED_BY` VARCHAR(45) NULL COMMENT '',
  `UPDATE_TIME` TIMESTAMP NULL COMMENT '',
  PRIMARY KEY (`OFFER_MAPPING_ID`)  COMMENT '',
  CONSTRAINT `APP_OFFER_MAPPING_DATA_ID_DEF` FOREIGN KEY (`APP_OFFER_ID`) REFERENCES `KETTLE_MASTER_DEV`.`APP_OFFER_DETAIL_DATA` (`ID`),
  UNIQUE INDEX `ID_UNIQUE` (`OFFER_MAPPING_ID` ASC)  COMMENT '');
