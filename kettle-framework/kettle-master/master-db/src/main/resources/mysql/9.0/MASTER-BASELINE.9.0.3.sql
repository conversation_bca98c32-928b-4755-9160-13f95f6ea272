DROP TABLE IF EXISTS KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING;

CREATE TABLE KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING(
PRODUCT_IMAGE_MAPPING_ID INT AUTO_INCREMENT,
PRODUCT_ID INT,
IMAGE_URL VARCHAR(255),
IMAGE_TYPE VARCHAR(100),
STATUS VARCHAR(50),
LAST_UPDATED_BY INT,
LAST_UPDATION_TIME TIMESTAMP,
PRIMARY KEY (PRODUCT_IMAGE_MAPPING_ID)
);

CREATE INDEX PRODUCT_IMAGE_MAPPING_PRODUCT_ID ON
KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING(PRODUCT_ID) USING BTREE;

CREATE INDEX PRODUCT_IMAGE_MAPPING_IMAGE_TYPE ON
KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING(IMAGE_TYPE) USING BTREE;


CREATE INDEX PRODUCT_IMAGE_MAPPING_STATUS ON
KETTLE_MASTER_DEV.PRODUCT_IMAGE_MAPPING(STATUS) USING BTREE;
