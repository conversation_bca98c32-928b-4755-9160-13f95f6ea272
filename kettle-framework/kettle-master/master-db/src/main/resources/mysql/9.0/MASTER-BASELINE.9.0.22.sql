DROP  TABLE IF EXISTS KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL;

CREATE TABLE KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(
LOG_DETAIL_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
LOCATION_TYPE VARCHAR(100) NULL,
LOCATION_ID VARCHAR(100) NULL,
DEVICE_NAME VARCHAR(100) NULL,
DEVICE_LOCATION VARCHAR(100) NULL,
LOG_DATE DATE NULL,
LOG_TIME TIMESTAMP NULL,
TEMPERATURE_VALUE DECIMAL(10,2) NULL,
TEM<PERSON>ERATURE_THRESHOLD_VALUE DECIMAL(10,2) NULL,
HUMIDITY_VALUE DECIMAL(10,2) NULL,
HUMIDITY_THRESHOLD_VALUE DECIMAL(10,2) NULL,
BREACH_DETECTED VARCHAR(1) NULL,
IS_VALID VARCHAR(1) NULL,
NOTIFICATION_TIME TIMESTAMP NULL,
IS_NOTIFIED VARCHAR(1) NULL,
MAC_ADDRESS VARCHAR(100) NULL,
IP_ADDRESS VARCHAR(100)
);

CREATE INDEX TEMPERATURE_LOG_DETAIL_LOCATION_TYPE ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(LOCATION_TYPE) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_LOCATION_ID ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(LOCATION_ID) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_DEVICE_LOCATION ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(DEVICE_LOCATION) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_DEVICE_NAME ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(DEVICE_NAME) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_LOG_TIME ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(LOG_TIME) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_NOTIFICATION_TIME ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(NOTIFICATION_TIME) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_LOG_DATE ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(LOG_DATE) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_BREACH_DETECTED ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(BREACH_DETECTED) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_IS_VALID ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(IS_VALID) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_IS_NOTIFIED ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(IS_NOTIFIED) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_MAC_ADDRESS ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(MAC_ADDRESS) USING BTREE;
CREATE INDEX TEMPERATURE_LOG_DETAIL_IP_ADDRESS ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(IP_ADDRESS) USING BTREE;


INSERT INTO `KETTLE_MASTER_DEV`.`PRE_AUTHENTICATED_API` (`API`, `STATUS`) VALUES ('report-service.log-data.temperature', 'ACTIVE');


ALTER TABLE KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL
ADD COLUMN UNIT_ID INTEGER NULL;

CREATE INDEX TEMPERATURE_LOG_DETAIL_UNIT_ID ON KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL(UNIT_ID) USING BTREE;



ALTER TABLE KETTLE_MASTER_DEV.TEMPERATURE_LOG_DETAIL DROP COLUMN NEEDS_NOTIFICATION;
