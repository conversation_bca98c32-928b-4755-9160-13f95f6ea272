DROP PROCEDURE IF EXISTS RESET_AUTO_INCREMENT;
delimiter $$
CREATE PROCEDURE RESET_AUTO_INCREMENT (IN SOURCE_SCHEMA VARCHAR(30), IN  TARGET_SCHEMA VARCHAR(30),IN  TARGET_TABLE_NAME VARCHAR(100))
  BEGIN

    SELECT @maxVal := `AUTO_INCREMENT` FROM  INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = SOURCE_SCHEMA AND TABLE_NAME = TARGET_TABLE_NAME ;
    SET @stmt = CONCAT('ALTER TABLE ', TARGET_SCHEMA , '.', TARGET_TABLE_NAME , ' AUTO_INCREMENT =  ',@maxVal);
    PREPARE stmt1 FROM @stmt;
    EXECUTE stmt1 ;
    DEALLOCATE PREPARE stmt1;

  END$$
DELIMITER ;


CREATE TABLE KETTLE_MASTER_DEV.`ADDRESS_INFO` (
  `ADDRESS_ID` int(11) NOT NULL AUTO_INCREMENT,
  `ADDRESS_LINE_1` varchar(255) NOT NULL,
  `ADDRESS_LINE_2` varchar(255) DEFAULT NULL,
  `ADDRESS_LINE_3` varchar(255) DEFAULT NULL,
  `CITY` varchar(128) NOT NULL,
  `STATE` varchar(128) NOT NULL,
  `COUNTRY` varchar(128) NOT NULL,
  `ZIPCODE` varchar(40) NOT NULL,
  `CONTACT_NUM_1` varchar(32) NOT NULL,
  `CONTACT_NUM_2` varchar(32) DEFAULT NULL,
  `ADDRESS_TYPE` varchar(50) NOT NULL DEFAULT 'RESIDENTIAL',
  `LATITUDE` varchar(15) DEFAULT NULL,
  `LONGITUDE` varchar(15) DEFAULT NULL,
  PRIMARY KEY (`ADDRESS_ID`)
) ;

CREATE TABLE KETTLE_MASTER_DEV.`COMPANY_DETAIL` (
  `COMPANY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `COMPANY_NAME` varchar(255) NOT NULL,
  `COMPANY_DESCRIPTION` varchar(5000) NOT NULL,
  `REGD_ADDR_ID` int(11) NOT NULL,
  `CIN` varchar(21) NOT NULL,
  `SERVICE_TAX_NO` varchar(15) NOT NULL,
  `WEBSITE_ADDR` varchar(255) NOT NULL,
  PRIMARY KEY (`COMPANY_ID`),
  KEY `FK_COMP_REG_ADDR` (`REGD_ADDR_ID`),
  CONSTRAINT `COMPANY_DETAIL_ibfk_1` FOREIGN KEY (`REGD_ADDR_ID`) REFERENCES `ADDRESS_INFO` (`ADDRESS_ID`) ON UPDATE CASCADE
) ;

CREATE TABLE KETTLE_MASTER_DEV.`BUSINESS_DIVISION` (
  `BUSINESS_DIV_ID` int(11) NOT NULL AUTO_INCREMENT,
  `BUSINESS_DIV_NAME` varchar(255) NOT NULL,
  `BUSIENSS_DIV_DESC` varchar(5000) NOT NULL,
  `BUSIENSS_DIV_CATEGORY` varchar(30) NOT NULL,
  `COMPANY_ID` int(11) NOT NULL,
  PRIMARY KEY (`BUSINESS_DIV_ID`),
  KEY `FK_BUSINESS_DIV_COMPANY_ID` (`COMPANY_ID`),
  CONSTRAINT `BUSINESS_DIVISION_ibfk_1` FOREIGN KEY (`COMPANY_ID`) REFERENCES `COMPANY_DETAIL` (`COMPANY_ID`) ON UPDATE CASCADE
) ;

CREATE TABLE KETTLE_MASTER_DEV.`DEPARTMENT` (
  `DEPT_ID` int(11) NOT NULL AUTO_INCREMENT,
  `DEPT_NAME` varchar(255) NOT NULL,
  `DEPT_DESC` varchar(500) NOT NULL,
  `BUSINESS_DIV_ID` int(11) NOT NULL,
  PRIMARY KEY (`DEPT_ID`),
  KEY `FK_DEPT_BUSINESS_DIV` (`BUSINESS_DIV_ID`),
  CONSTRAINT `DEPARTMENT_ibfk_1` FOREIGN KEY (`BUSINESS_DIV_ID`) REFERENCES `BUSINESS_DIVISION` (`BUSINESS_DIV_ID`) ON UPDATE CASCADE
) ;

CREATE TABLE KETTLE_MASTER_DEV.`DESIGNATION` (
  `DESIGNATION_ID` int(11) NOT NULL,
  `DESIGNATION_NAME` varchar(255) NOT NULL,
  `DESIGNATION_DESC` varchar(500) NOT NULL,
  PRIMARY KEY (`DESIGNATION_ID`)
) ;

CREATE TABLE KETTLE_MASTER_DEV.`DEPARTMENT_DESIGNATION_MAPPING` (  
 `DEPT_ID` int(11) NOT NULL,  
 `DESIGNATION_ID` int(11) NOT NULL,  
 PRIMARY KEY (`DEPT_ID`,`DESIGNATION_ID`),  
 KEY `FK_DESIGNATION_MAPPING` (`DESIGNATION_ID`),   
 CONSTRAINT `DEPARTMENT_DESIGNATION_MAPPING_ibfk_1` 
 FOREIGN KEY (`DEPT_ID`) REFERENCES `DEPARTMENT` (`DEPT_ID`) ON UPDATE CASCADE,  
 CONSTRAINT `DEPARTMENT_DESIGNATION_MAPPING_ibfk_2` FOREIGN KEY (`DESIGNATION_ID`) 
 REFERENCES `DESIGNATION` (`DESIGNATION_ID`) ON UPDATE CASCADE )
 ;

CREATE TABLE KETTLE_MASTER_DEV.`EMPLOYEE_DETAIL` (
  `EMP_ID` int(11) NOT NULL AUTO_INCREMENT,
  `EMP_NAME` varchar(255) NOT NULL,
  `EMP_GENDER` varchar(1) NOT NULL,
  `EMP_CURRENT_ADDR` int(11) NOT NULL,
  `EMP_PERMANENT_ADDR` int(11) NOT NULL,
  `EMP_CONTACT_NUM_1` varchar(32) NOT NULL,
  `EMP_CONTACT_NUM_2` varchar(32) DEFAULT NULL,
  `DEPTARTMENT_ID` int(11) NOT NULL,
  `DESIGNATION_ID` int(11) NOT NULL,
  `EMPLOYMENT_TYPE` varchar(10) NOT NULL,
  `EMPLOYMENT_STATUS` varchar(10) NOT NULL,
  `BIOMETRIC_IDENTIFIER` varchar(255) DEFAULT NULL,
  `JOINING_DATE` date NOT NULL,
  `TERMINATION_DATE` date DEFAULT NULL,
  `REPORTING_MANAGER_ID` int(11) DEFAULT NULL,
  PRIMARY KEY (`EMP_ID`),
  KEY `FK_EMP_CURRENT_ADDR` (`EMP_CURRENT_ADDR`),
  KEY `FK_EMP_PERMANENT_ADDR` (`EMP_PERMANENT_ADDR`),
  KEY `FK_EMP_DEPARTMENT` (`DEPTARTMENT_ID`),
  KEY `FK_EMP_DESIGNATION` (`DESIGNATION_ID`),
  KEY `FK_EMP_REPORTING_MANAGER` (`REPORTING_MANAGER_ID`),
  CONSTRAINT `EMPLOYEE_DETAIL_ibfk_1` FOREIGN KEY (`EMP_CURRENT_ADDR`) REFERENCES `ADDRESS_INFO` (`ADDRESS_ID`) ON UPDATE CASCADE,
  CONSTRAINT `EMPLOYEE_DETAIL_ibfk_2` FOREIGN KEY (`EMP_PERMANENT_ADDR`) REFERENCES `ADDRESS_INFO` (`ADDRESS_ID`) ON UPDATE CASCADE,
  CONSTRAINT `EMPLOYEE_DETAIL_ibfk_3` FOREIGN KEY (`DEPTARTMENT_ID`) REFERENCES `DEPARTMENT` (`DEPT_ID`) ON UPDATE CASCADE,
  CONSTRAINT `EMPLOYEE_DETAIL_ibfk_4` FOREIGN KEY (`DESIGNATION_ID`) REFERENCES `DESIGNATION` (`DESIGNATION_ID`) ON UPDATE CASCADE,
  CONSTRAINT `EMPLOYEE_DETAIL_ibfk_5` FOREIGN KEY (`REPORTING_MANAGER_ID`) REFERENCES `EMPLOYEE_DETAIL` (`EMP_ID`) ON UPDATE CASCADE
) ;

CREATE TABLE KETTLE_MASTER_DEV.`AUTHORIZED_MAC_ADDRESSES` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC_ADDRESS` varchar(20) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `MAC_ADDRESS` (`MAC_ADDRESS`)
) ;

CREATE TABLE KETTLE_MASTER_DEV.`COUPON_DETAIL_DATA` (
  `COUPON_DETAIL_ID` int(11) NOT NULL AUTO_INCREMENT,
  `OFFER_DETAIL_ID` int(11) NOT NULL,
  `COUPON_CODE` varchar(20) NOT NULL,
  `START_DATE` date NOT NULL,
  `END_DATE` date NOT NULL,
  `COUPON_REUSE` varchar(1) NOT NULL,
  `CUSTOMER_REUSE` varchar(1) NOT NULL,
  `MAX_USAGE` int(11) DEFAULT NULL,
  `COUPON_STATUS` varchar(15) NOT NULL DEFAULT 'INACTIVE',
  `USAGE_COUNT` int(11) NOT NULL DEFAULT '0',
  `MANUAL_OVERRIDE` varchar(1) NOT NULL DEFAULT 'N',
  PRIMARY KEY (`COUPON_DETAIL_ID`),
  UNIQUE KEY `COUPON_CODE_UNIQUE` (`COUPON_CODE`),
  KEY `index_coupon_code` (`COUPON_CODE`(10)) USING BTREE
) ;

CREATE TABLE KETTLE_MASTER_DEV.`COUPON_DETAIL_MAPPING_DATA` (
  `COUPON_DETAIL_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `COUPON_DETAIL_ID` int(11) NOT NULL,
  `MAPPING_TYPE` varchar(50) NOT NULL,
  `MAPPING_VALUE` varchar(100) NOT NULL,
  `MAPPING_DATA_TYPE` varchar(100) NOT NULL,
  `MIN_VALUE` varchar(10) NOT NULL DEFAULT '1',
  `MAPPING_GROUP` int(11) NOT NULL DEFAULT '1',
  `DIMENSION` varchar(15) DEFAULT NULL,
  `STATUS` varchar(10) DEFAULT 'ACTIVE',
  PRIMARY KEY (`COUPON_DETAIL_MAPPING_ID`)
) ;

CREATE TABLE KETTLE_MASTER_DEV.`COUPON_CODE_USAGE_DATA` (
  `COUPON_CODE_USAGE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `COUPON_DETAIL_ID` int(11) NOT NULL,
  `ORDER_ID` int(11) NOT NULL,
  `CUSTOMER_ID` int(11) NOT NULL,
  `DISCOUNT_AMOUNT` decimal(10,2) DEFAULT NULL,
  `COMPLIMENTARY_PRODUCT_ID` int(11) DEFAULT NULL,
  `COMPLIMENTARY_PRODUCT_QUANTITY` int(11) DEFAULT NULL,
  `ADD_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`COUPON_CODE_USAGE_ID`)
) ;

CREATE TABLE KETTLE_MASTER_DEV.`EMPLOYEE_PASS_CODE` (
  `EMP_SURROGATE_ID` int(11) NOT NULL AUTO_INCREMENT,
  `EMP_ID` int(11) NOT NULL,
  `EMP_PASS_CODE` varchar(255) NOT NULL,
  `LAST_UPDATE_TMSTMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UPDATED_BY` int(11) DEFAULT NULL,
  PRIMARY KEY (`EMP_SURROGATE_ID`),
  KEY `FK_EMPLOYEE_PASS_CODE_EMP_DETAIL` (`EMP_ID`),
  CONSTRAINT `EMPLOYEE_PASS_CODE_ibfk_1` FOREIGN KEY (`EMP_ID`) REFERENCES `EMPLOYEE_DETAIL` (`EMP_ID`) ON UPDATE CASCADE
) ;

CREATE TABLE KETTLE_MASTER_DEV.`EMPLOYEE_SESSION_DETAILS` (
  `SESSION_DETAIL_ID` int(11) NOT NULL AUTO_INCREMENT,
  `EMPLOYEE_ID` int(11) NOT NULL,
  `UNIT_ID` int(11) NOT NULL,
  `LOGIN_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `LOGIN_ATTEMPT` varchar(30) DEFAULT NULL,
  `LOGOUT_TIME` timestamp NULL DEFAULT NULL,
  `SESSION_ID` varchar(100) DEFAULT NULL,
  `TERMINAL_ID` int(11) DEFAULT '1',
  `IP_ADDRESS` varchar(20) DEFAULT NULL,
  `MAC_ADDRESS` varchar(20) DEFAULT NULL,
  `USER_AGENT` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`SESSION_DETAIL_ID`)
) ;

CREATE TABLE KETTLE_MASTER_DEV.`MARKETING_PARTNER` (
   `PARTNER_ID` int(11) NOT NULL AUTO_INCREMENT,
   `PARTNER_NAME` varchar(30) NOT NULL DEFAULT 'CHAAYOS',
   `PARTNER_TYPE` varchar(30) NOT NULL DEFAULT 'INTERNAL',
   `STATUS` varchar(10) NOT NULL DEFAULT 'ACTIVE',
   `AUTHORIZATION_KEY` varchar(100) DEFAULT NULL,
   PRIMARY KEY (`PARTNER_ID`),
   UNIQUE KEY `AUTHORIZATION_KEY` (`AUTHORIZATION_KEY`)
 ) ;

 CREATE TABLE KETTLE_MASTER_DEV.`OFFER_DETAIL_DATA` (
   `OFFER_DETAIL_ID` int(11) NOT NULL AUTO_INCREMENT,
   `OFFER_CATEGORY` varchar(30) NOT NULL,
   `OFFER_TYPE` varchar(30) NOT NULL,
   `OFFER_TEXT` varchar(500) NOT NULL,
   `OFFER_DESCRIPTION` varchar(1000) NOT NULL,
   `START_DATE` date NOT NULL,
   `END_DATE` date NOT NULL,
   `OFFER_STATUS` varchar(15) NOT NULL,
   `MIN_VALUE` int(11) NOT NULL DEFAULT '0',
   `VALIDATE_CUSTOMER` varchar(1) NOT NULL DEFAULT 'Y',
   `INCLUDE_TAXES` varchar(1) NOT NULL DEFAULT 'N',
   `PRIORITY` int(11) DEFAULT NULL,
   `OFFER_SCOPE` varchar(10) NOT NULL DEFAULT 'MASS',
   `MIN_ITEM_COUNT` int(11) NOT NULL DEFAULT '1',
   `QUANTITY_LIMIT` int(11) NOT NULL DEFAULT '1',
   `LOYALTY_LIMIT` int(11) NOT NULL DEFAULT '0',
   `OFFER_VALUE` int(11) NOT NULL DEFAULT '0',
   PRIMARY KEY (`OFFER_DETAIL_ID`)
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`OFFER_METADATA` (
   `ID` int(11) NOT NULL AUTO_INCREMENT,
   `OFFER_ID` int(11) NOT NULL,
   `MAPPING_TYPE` varchar(30) NOT NULL,
   `MAPPING_VALUE` varchar(30) NOT NULL,
   `STATUS` varchar(10) DEFAULT 'ACTIVE',
   PRIMARY KEY (`ID`)
 ) ;

 CREATE TABLE KETTLE_MASTER_DEV.`OFFER_PARTNERS` (
   `MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
   `PARTNER_ID` int(11) NOT NULL,
   `OFFER_ID` int(11) NOT NULL,
   `STATUS` varchar(10) DEFAULT 'ACTIVE',
   PRIMARY KEY (`MAPPING_ID`)
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`PAYMENT_MODE` (
   `PAYMENT_MODE_ID` int(11) NOT NULL AUTO_INCREMENT,
   `MODE_NAME` varchar(100) NOT NULL,
   `MODE_TYPE` varchar(15) NOT NULL,
   `MODE_DESCRIPTION` varchar(255) NOT NULL,
   `SETTLEMENT_TYPE` varchar(20) NOT NULL,
   `GENERATE_PULL` tinyint(1) NOT NULL,
   `COMMISSION_RATE` decimal(10,2) NOT NULL DEFAULT '0.00',
   PRIMARY KEY (`PAYMENT_MODE_ID`)
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`REF_LOOKUP_TYPE` (
   `RTL_ID` int(11) NOT NULL AUTO_INCREMENT,
   `RTL_GROUP` varchar(15) NOT NULL,
   `RTL_CODE` varchar(20) NOT NULL,
   `RTL_NAME` varchar(30) NOT NULL,
   `STATUS` varchar(10) DEFAULT 'ACTIVE',
   PRIMARY KEY (`RTL_ID`),
   UNIQUE KEY `RTL_GROUP` (`RTL_GROUP`,`RTL_CODE`)
 ) ;

 CREATE TABLE KETTLE_MASTER_DEV.`REF_LOOKUP` (
   `RL_ID` int(11) NOT NULL AUTO_INCREMENT,
   `RTL_ID` int(11) NOT NULL,
   `RL_CODE` varchar(20) NOT NULL,
   `RL_NAME` varchar(30) NOT NULL,
   `RL_SHORT_CODE` varchar(4) DEFAULT NULL,
   `RL_STATUS` varchar(10) NOT NULL DEFAULT 'ACTIVE',
   PRIMARY KEY (`RL_ID`),
   UNIQUE KEY `RTL_ID` (`RTL_ID`,`RL_CODE`),
   CONSTRAINT `REF_LOOKUP_ibfk_1` FOREIGN KEY (`RTL_ID`) REFERENCES `REF_LOOKUP_TYPE` (`RTL_ID`) ON UPDATE CASCADE
 ) ;

 CREATE TABLE KETTLE_MASTER_DEV.`PRODUCT_DETAIL` (
   `PRODUCT_ID` int(11) NOT NULL AUTO_INCREMENT,
   `PRODUCT_NAME` varchar(255) NOT NULL,
   `PRODUCT_DESCRIPTION` varchar(5000) NOT NULL,
   `PRODUCT_TYPE` int(11) NOT NULL,
   `PRODUCT_SUB_TYPE` int(11) NOT NULL,
   `PRODUCT_STATUS` varchar(20) NOT NULL,
   `ATTRIBUTE` varchar(20) DEFAULT NULL,
   `PRODUCT_START_DATE` date NOT NULL,
   `PRODUCT_END_DATE` date NOT NULL DEFAULT '9999-12-01',
   `PRODUCT_SKU_CODE` varchar(30) NOT NULL,
   `DIMENSION_CODE` int(11) NOT NULL DEFAULT '1',
   `PRICE_TYPE` varchar(10) NOT NULL DEFAULT 'NET_PRICE',
   `ADDITIONAL_ITEM_TYPES` int(11) DEFAULT NULL,
   `IN_TMSTMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `OUT_TMSTMP` timestamp NOT NULL DEFAULT '2030-12-01 00:00:00',
   `IS_INVENTORY_TRACKED` varchar(1) NOT NULL DEFAULT 'N',
   PRIMARY KEY (`PRODUCT_ID`),
   KEY `FK_DIMENSION_CODE_RTL_TYPE` (`DIMENSION_CODE`),
   KEY `FK_ADDITIONAL_ITEM_TYPES_RTL_TYPE` (`ADDITIONAL_ITEM_TYPES`),
   KEY `FK_PRODUCT_TYPE_REF_LOOKUP` (`PRODUCT_TYPE`),
   KEY `FK_PRODUCT_SUB_TYPE_REF_LOOKUP` (`PRODUCT_SUB_TYPE`),
   CONSTRAINT `PRODUCT_DETAIL_ibfk_2` FOREIGN KEY (`DIMENSION_CODE`) REFERENCES `REF_LOOKUP_TYPE` (`RTL_ID`) ON UPDATE CASCADE,
   CONSTRAINT `PRODUCT_DETAIL_ibfk_3` FOREIGN KEY (`ADDITIONAL_ITEM_TYPES`) REFERENCES `REF_LOOKUP_TYPE` (`RTL_ID`) ON UPDATE CASCADE,
   CONSTRAINT `PRODUCT_DETAIL_ibfk_4` FOREIGN KEY (`PRODUCT_TYPE`) REFERENCES `REF_LOOKUP_TYPE` (`RTL_ID`) ON UPDATE CASCADE,
   CONSTRAINT `PRODUCT_DETAIL_ibfk_5` FOREIGN KEY (`PRODUCT_SUB_TYPE`) REFERENCES `REF_LOOKUP` (`RL_ID`) ON UPDATE CASCADE
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`PRODUCT_RELATIONSHIP` (
   `PRODUCT_ID` int(11) NOT NULL,
   `CONSTITUENT_PRODUCT_ID` int(11) NOT NULL,
   `RELATIONSHIP_TYPE` varchar(10) NOT NULL,
   `QUANTITY` int(11) NOT NULL,
   `PRICE_MULTIPLIER` decimal(4,4) NOT NULL,
   KEY `FK_PRODUCT_RELATIONSHIP_PRODUCT` (`PRODUCT_ID`),
   KEY `FK_PRODUCT_RELATIONSHIP_CONSTITUENT_PRODUCT` (`CONSTITUENT_PRODUCT_ID`),
   CONSTRAINT `PRODUCT_RELATIONSHIP_ibfk_1` FOREIGN KEY (`PRODUCT_ID`) REFERENCES `PRODUCT_DETAIL` (`PRODUCT_ID`) ON UPDATE CASCADE,
   CONSTRAINT `PRODUCT_RELATIONSHIP_ibfk_2` FOREIGN KEY (`CONSTITUENT_PRODUCT_ID`) REFERENCES `PRODUCT_DETAIL` (`PRODUCT_ID`) ON UPDATE CASCADE
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`TAX_PROFILE` (
   `TAX_PROFILE_ID` int(11) NOT NULL AUTO_INCREMENT,
   `TAX_TYPE` varchar(20) NOT NULL,
   `TAX_NAME` varchar(40) NOT NULL,
   `TAX_PROFILE_STATUS` varchar(10) NOT NULL DEFAULT 'ACTIVE',
   PRIMARY KEY (`TAX_PROFILE_ID`)
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_DETAIL` (
   `UNIT_ID` int(11) NOT NULL AUTO_INCREMENT,
   `UNIT_NAME` varchar(255) NOT NULL,
   `UNIT_REGION` varchar(25) NOT NULL,
   `UNIT_CATEGORY` varchar(30) NOT NULL,
   `UNIT_EMAIL` varchar(50) NOT NULL,
   `START_DATE` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `UNIT_STATUS` varchar(15) NOT NULL,
   `TIN` varchar(15) NOT NULL,
   `UNIT_ADDR_ID` int(11) NOT NULL,
   `BUSINESS_DIV_ID` int(11) NOT NULL,
   `NO_OF_TERMINALS` int(11) NOT NULL DEFAULT '1',
   `NO_OF_TA_TERMINALS` int(11) DEFAULT '0',
   `WORKSTATION_ENABLED` varchar(1) DEFAULT 'N',
   PRIMARY KEY (`UNIT_ID`),
   KEY `FK_UNIT_ADDR_INFO` (`UNIT_ADDR_ID`),
   KEY `FK_UNIT_BUSINESS_DIV` (`BUSINESS_DIV_ID`),
   CONSTRAINT `UNIT_DETAIL_ibfk_1` FOREIGN KEY (`UNIT_ADDR_ID`) REFERENCES `ADDRESS_INFO` (`ADDRESS_ID`) ON UPDATE CASCADE,
   CONSTRAINT `UNIT_DETAIL_ibfk_2` FOREIGN KEY (`BUSINESS_DIV_ID`) REFERENCES `BUSINESS_DIVISION` (`BUSINESS_DIV_ID`) ON UPDATE CASCADE
 ) ;

 CREATE TABLE KETTLE_MASTER_DEV.`UNIT_PRODUCT_MAPPING` (
   `UNIT_PROD_REF_ID` int(11) NOT NULL AUTO_INCREMENT,
   `UNIT_ID` int(11) NOT NULL,
   `PRODUCT_ID` int(11) NOT NULL,
   `PRODUCT_STATUS` varchar(15) NOT NULL DEFAULT 'ACTIVE',
   `LAST_UPDATE_TMSTMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `PRODUCT_START_DATE` date NOT NULL,
   `PRODUCT_END_DATE` date NOT NULL DEFAULT '9999-12-01',
   PRIMARY KEY (`UNIT_PROD_REF_ID`),
   UNIQUE KEY `UK_UNIT_PRODUCT_MAPPING` (`UNIT_ID`,`PRODUCT_ID`),
   KEY `FK_PRODUCT_UNIT_PRODUCT_MAPPING` (`PRODUCT_ID`),
   CONSTRAINT `UNIT_PRODUCT_MAPPING_ibfk_1` FOREIGN KEY (`PRODUCT_ID`) REFERENCES `PRODUCT_DETAIL` (`PRODUCT_ID`) ON UPDATE CASCADE,
   CONSTRAINT `UNIT_PRODUCT_MAPPING_ibfk_2` FOREIGN KEY (`UNIT_ID`) REFERENCES `UNIT_DETAIL` (`UNIT_ID`) ON UPDATE CASCADE
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_PRODUCT_PRICING` (
   `UNIT_PROD_PRICE_ID` int(11) NOT NULL AUTO_INCREMENT,
   `UNIT_PROD_REF_ID` int(11) NOT NULL,
   `DIMENSION_CODE` int(11) NOT NULL DEFAULT '1',
   `LAST_UPDATE_TMSTMP` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
   `PRICE` decimal(10,2) DEFAULT NULL,
   `COST` decimal(10,2) DEFAULT NULL,
   `COD_COST` decimal(10,2) DEFAULT NULL,
   PRIMARY KEY (`UNIT_PROD_PRICE_ID`),
   UNIQUE KEY `UNIT_PROD_REF_ID` (`UNIT_PROD_REF_ID`,`DIMENSION_CODE`),
   KEY `FK_UNIT_PRODUCT_DIMENSION_CODE` (`DIMENSION_CODE`),
   CONSTRAINT `UNIT_PRODUCT_PRICING_ibfk_1` FOREIGN KEY (`UNIT_PROD_REF_ID`) REFERENCES `UNIT_PRODUCT_MAPPING` (`UNIT_PROD_REF_ID`) ON UPDATE CASCADE,
   CONSTRAINT `UNIT_PRODUCT_PRICING_ibfk_2` FOREIGN KEY (`DIMENSION_CODE`) REFERENCES `REF_LOOKUP` (`RL_ID`) ON UPDATE CASCADE
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`UNIT_TAX_MAPPING` (
   `UNIT_TAX_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
   `TAX_PROFILE_ID` int(11) NOT NULL,
   `UNIT_ID` int(11) NOT NULL,
   `TAX_PERCENTAGE` decimal(10,2) NOT NULL,
   `PROFILE_STATUS` varchar(20) NOT NULL DEFAULT 'ACTIVE',
   `STATE` varchar(20) NOT NULL,
   PRIMARY KEY (`UNIT_TAX_MAPPING_ID`),
   KEY `FK_TAX_PROFILE_MAPPING` (`TAX_PROFILE_ID`),
   KEY `FK_TAX_UNIT_ID_MAPPING` (`UNIT_ID`),
   CONSTRAINT `UNIT_TAX_MAPPING_ibfk_1` FOREIGN KEY (`TAX_PROFILE_ID`) REFERENCES `TAX_PROFILE` (`TAX_PROFILE_ID`) ON UPDATE CASCADE,
   CONSTRAINT `UNIT_TAX_MAPPING_ibfk_2` FOREIGN KEY (`UNIT_ID`) REFERENCES `UNIT_DETAIL` (`UNIT_ID`) ON UPDATE CASCADE
 ) ;

CREATE TABLE KETTLE_MASTER_DEV.`EMPLOYEE_UNIT_MAPPING` (
  `EMP_UNIT_KEY_ID` int(11) NOT NULL AUTO_INCREMENT,
  `EMP_ID` int(11) NOT NULL,
  `UNIT_ID` int(11) NOT NULL,
  `MAPPING_STATUS` varchar(15) NOT NULL DEFAULT 'ACTIVE',
  `LAST_UPDATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`EMP_UNIT_KEY_ID`),
  UNIQUE KEY `EMP_ID` (`EMP_ID`,`UNIT_ID`,`MAPPING_STATUS`),
  KEY `FK_EMPLOYEE_PASS_CODE_UNIT_DETAIL` (`UNIT_ID`),
  CONSTRAINT `EMPLOYEE_UNIT_MAPPING_ibfk_2` FOREIGN KEY (`UNIT_ID`) REFERENCES `UNIT_DETAIL` (`UNIT_ID`) ON UPDATE CASCADE,
  CONSTRAINT `EMPLOYEE_UNIT_MAPPING_ibfk_3` FOREIGN KEY (`EMP_ID`) REFERENCES `EMPLOYEE_DETAIL` (`EMP_ID`) ON UPDATE CASCADE
) ;

CREATE TABLE KETTLE_MASTER_DEV.`ADDON_PRODUCT_DATA` (
  `ADDON_PRODUCT_DATA_ID` int(11) NOT NULL AUTO_INCREMENT,
  `ADDON_ID` int(11) NOT NULL,
  `PRODUCT_ID` int(11) NOT NULL,
  PRIMARY KEY (`ADDON_PRODUCT_DATA_ID`),
  UNIQUE KEY `ADDON_ID` (`ADDON_ID`,`PRODUCT_ID`)
) ;

CREATE TABLE KETTLE_MASTER_DEV.`DENOMINATION` (
  `DENOMINATION_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PAYMENT_MODE` int(11) NOT NULL,
  `DENOMINATION_TEXT` varchar(100) NOT NULL,
  `DENOMINATION_CODE` varchar(20) NOT NULL,
  `DENOMINATION_VALUE` int(11) NOT NULL,
  `DISPLAY_ORDER` int(11) NOT NULL,
  `STATUS` varchar(20) NOT NULL DEFAULT 'IN_ACTIVE',
  `BUNDLE_SIZE` int(11) NOT NULL DEFAULT '100',
  PRIMARY KEY (`DENOMINATION_ID`),
  KEY `PAYMENT_MODE` (`PAYMENT_MODE`),
  CONSTRAINT `DENOMINATION_ibfk_1` FOREIGN KEY (`PAYMENT_MODE`) REFERENCES `PAYMENT_MODE` (`PAYMENT_MODE_ID`)
) ;

ALTER TABLE KETTLE_DEV.PRODUCT_DETAIL DROP FOREIGN KEY PRODUCT_DETAIL_ibfk_1;
ALTER TABLE KETTLE_DEV.PRODUCT_DETAIL DROP COLUMN VENDOR_ID;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN NO_OF_TABLES INTEGER DEFAULT 1;
ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN UNIT_SUB_CATEGORY VARCHAR(15);

INSERT INTO KETTLE_MASTER_DEV.ADDRESS_INFO
SELECT * FROM KETTLE_DEV.ADDRESS_INFO;

INSERT INTO KETTLE_MASTER_DEV.COMPANY_DETAIL
SELECT * FROM KETTLE_DEV.COMPANY_DETAIL;

INSERT INTO KETTLE_MASTER_DEV.BUSINESS_DIVISION
SELECT * FROM KETTLE_DEV.BUSINESS_DIVISION;

INSERT INTO KETTLE_MASTER_DEV.DEPARTMENT
SELECT * FROM KETTLE_DEV.DEPARTMENT;

INSERT INTO KETTLE_MASTER_DEV.DESIGNATION
SELECT * FROM KETTLE_DEV.DESIGNATION;

INSERT INTO KETTLE_MASTER_DEV.DEPARTMENT_DESIGNATION_MAPPING
SELECT * FROM KETTLE_DEV.DEPARTMENT_DESIGNATION_MAPPING;

INSERT INTO KETTLE_MASTER_DEV.AUTHORIZED_MAC_ADDRESSES
SELECT * FROM KETTLE_DEV.AUTHORIZED_MAC_ADDRESSES;

INSERT INTO KETTLE_MASTER_DEV.COUPON_DETAIL_DATA
SELECT * FROM KETTLE_DEV.COUPON_DETAIL_DATA;

INSERT INTO KETTLE_MASTER_DEV.COUPON_DETAIL_MAPPING_DATA
SELECT * FROM KETTLE_DEV.COUPON_DETAIL_MAPPING_DATA;

INSERT INTO KETTLE_MASTER_DEV.COUPON_CODE_USAGE_DATA
SELECT * FROM KETTLE_DEV.COUPON_CODE_USAGE_DATA;

INSERT INTO KETTLE_MASTER_DEV.MARKETING_PARTNER
SELECT * FROM KETTLE_DEV.MARKETING_PARTNER;

INSERT INTO KETTLE_MASTER_DEV.OFFER_DETAIL_DATA
SELECT * FROM KETTLE_DEV.OFFER_DETAIL_DATA;

INSERT INTO KETTLE_MASTER_DEV.OFFER_METADATA
SELECT * FROM KETTLE_DEV.OFFER_METADATA;

INSERT INTO KETTLE_MASTER_DEV.OFFER_PARTNERS
SELECT * FROM KETTLE_DEV.OFFER_PARTNERS;

INSERT INTO KETTLE_MASTER_DEV.PAYMENT_MODE
SELECT * FROM KETTLE_DEV.PAYMENT_MODE;

INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP_TYPE
SELECT * FROM KETTLE_DEV.REF_LOOKUP_TYPE;

INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP
SELECT * FROM KETTLE_DEV.REF_LOOKUP;

INSERT INTO KETTLE_MASTER_DEV.PRODUCT_DETAIL
SELECT * FROM KETTLE_DEV.PRODUCT_DETAIL;

INSERT INTO KETTLE_MASTER_DEV.PRODUCT_RELATIONSHIP
SELECT * FROM KETTLE_DEV.PRODUCT_RELATIONSHIP;

INSERT INTO KETTLE_MASTER_DEV.TAX_PROFILE
SELECT * FROM KETTLE_DEV.TAX_PROFILE;

INSERT INTO `KETTLE_MASTER_DEV`.`UNIT_DETAIL`
(`UNIT_ID`,
`UNIT_NAME`,
`UNIT_REGION`,
`UNIT_CATEGORY`,
`UNIT_EMAIL`,
`START_DATE`,
`UNIT_STATUS`,
`TIN`,
`UNIT_ADDR_ID`,
`BUSINESS_DIV_ID`,
`NO_OF_TERMINALS`,
`NO_OF_TA_TERMINALS`,
`WORKSTATION_ENABLED`,
`NO_OF_TABLES`,
`UNIT_SUB_CATEGORY`)
select `UNIT_ID`,
`UNIT_NAME`,
`UNIT_REGION`,
`UNIT_CATEGORY`,
`UNIT_EMAIL`,
`START_DATE`,
`UNIT_STATUS`,
`TIN`,
`UNIT_ADDR_ID`,
`BUSINESS_DIV_ID`,
`NO_OF_TERMINALS`,
`NO_OF_TA_TERMINALS`,
`WORKSTATION_ENABLED`,
`NO_OF_TABLES`,
`UNIT_SUB_CATEGORY` from KETTLE_DEV.UNIT_DETAIL;



INSERT INTO KETTLE_MASTER_DEV.UNIT_PRODUCT_MAPPING
SELECT * FROM KETTLE_DEV.UNIT_PRODUCT_MAPPING;

INSERT INTO KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING
SELECT * FROM KETTLE_DEV.UNIT_PRODUCT_PRICING;

INSERT INTO KETTLE_MASTER_DEV.UNIT_TAX_MAPPING
SELECT * FROM KETTLE_DEV.UNIT_TAX_MAPPING;

INSERT INTO KETTLE_MASTER_DEV.ADDON_PRODUCT_DATA
SELECT * FROM KETTLE_DEV.ADDON_PRODUCT_DATA;

INSERT INTO KETTLE_MASTER_DEV.DENOMINATION
SELECT * FROM KETTLE_DEV.DENOMINATION;

set FOREIGN_KEY_CHECKS=0;

INSERT INTO KETTLE_MASTER_DEV.EMPLOYEE_DETAIL
SELECT * FROM KETTLE_DEV.EMPLOYEE_DETAIL;

set FOREIGN_KEY_CHECKS=1;

INSERT INTO KETTLE_MASTER_DEV.EMPLOYEE_UNIT_MAPPING
SELECT * FROM KETTLE_DEV.EMPLOYEE_UNIT_MAPPING;

INSERT INTO KETTLE_MASTER_DEV.EMPLOYEE_PASS_CODE
SELECT * FROM KETTLE_DEV.EMPLOYEE_PASS_CODE;

INSERT INTO KETTLE_MASTER_DEV.EMPLOYEE_SESSION_DETAILS
SELECT * FROM KETTLE_DEV.EMPLOYEE_SESSION_DETAILS;


call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'ADDON_PRODUCT_DATA');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'EMPLOYEE_UNIT_MAPPING');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'UNIT_TAX_MAPPING');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'UNIT_PRODUCT_PRICING');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'UNIT_PRODUCT_MAPPING');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'UNIT_DETAIL');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'TAX_PROFILE');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'REF_LOOKUP');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'REF_LOOKUP_TYPE');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'PRODUCT_DETAIL');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'PAYMENT_MODE');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'OFFER_PARTNERS');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'OFFER_METADATA');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'OFFER_DETAIL_DATA');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'MARKETING_PARTNER');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'EMPLOYEE_SESSION_DETAILS');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'EMPLOYEE_PASS_CODE');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'COUPON_CODE_USAGE_DATA');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'COUPON_DETAIL_MAPPING_DATA');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'COUPON_DETAIL_DATA');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'AUTHORIZED_MAC_ADDRESSES');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'EMPLOYEE_DETAIL');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'ADDRESS_INFO');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'DEPARTMENT');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'BUSINESS_DIVISION');
call RESET_AUTO_INCREMENT('KETTLE_DEV', 'KETTLE_MASTER_DEV', 'COMPANY_DETAIL');

ALTER TABLE KETTLE_DEV.ORDER_DETAIL DROP FOREIGN KEY ORDER_DETAIL_ibfk_1;
ALTER TABLE KETTLE_DEV.ORDER_DETAIL DROP FOREIGN KEY ORDER_DETAIL_ibfk_2;
ALTER TABLE KETTLE_DEV.ORDER_DETAIL DROP FOREIGN KEY ORDER_DETAIL_ibfk_4;

ALTER TABLE KETTLE_DEV.ORDER_ITEM DROP FOREIGN KEY ORDER_ITEM_ibfk_2;
ALTER TABLE KETTLE_DEV.ORDER_ITEM DROP FOREIGN KEY ORDER_ITEM_ibfk_4;

ALTER TABLE KETTLE_DEV.ORDER_SETTLEMENT DROP FOREIGN KEY ORDER_SETTLEMENT_ibfk_2;

ALTER TABLE KETTLE_DEV.PULL_DETAIL DROP FOREIGN KEY PULL_DETAIL_ibfk_1;
ALTER TABLE KETTLE_DEV.PULL_DETAIL DROP FOREIGN KEY PULL_DETAIL_ibfk_3;
ALTER TABLE KETTLE_DEV.PULL_DETAIL DROP FOREIGN KEY PULL_DETAIL_ibfk_4;

ALTER TABLE KETTLE_DEV.SETTLEMENT_DETAIL DROP FOREIGN KEY SETTLEMENT_DETAIL_ibfk_1;
ALTER TABLE KETTLE_DEV.SETTLEMENT_DETAIL DROP FOREIGN KEY SETTLEMENT_DETAIL_ibfk_2;

UPDATE `KETTLE_MASTER_DEV`.`REF_LOOKUP_TYPE` SET `RTL_CODE`='OthersAddons' WHERE `RTL_ID`='27';


ALTER TABLE KETTLE_DEV.SUBSCRIPTION_DETAIL DROP FOREIGN KEY SUBSCRIPTION_DETAIL_ibfk_1;
ALTER TABLE KETTLE_DEV.SUBSCRIPTION_DETAIL DROP FOREIGN KEY SUBSCRIPTION_DETAIL_ibfk_2;
ALTER TABLE KETTLE_DEV.SUBSCRIPTION_DETAIL DROP FOREIGN KEY SUBSCRIPTION_DETAIL_ibfk_4;

ALTER TABLE KETTLE_DEV.SUBSCRIPTION_ITEM DROP FOREIGN KEY SUBSCRIPTION_ITEM_ibfk_2;

ALTER TABLE KETTLE_DEV.SUBSCRIPTION_SETTLEMENT DROP FOREIGN KEY SUBSCRIPTION_SETTLEMENT_ibfk_2;

ALTER TABLE KETTLE_DEV.ORDER_ENQUIRY_ITEM DROP FOREIGN KEY ORDER_ENQUIRY_ITEM_ibfk_1;
ALTER TABLE KETTLE_DEV.ORDER_ENQUIRY_ITEM DROP FOREIGN KEY ORDER_ENQUIRY_ITEM_ibfk_4;
