spring.application.name=master-service
spring.config.import=configserver:${config.url}


spring.cloud.config.label =${spring.profiles.active}

management.endpoints.web.exposure.include=refresh
spring.cloud.config.username=pakoda
spring.cloud.config.password=CozyChaiMoment!

spring.jpa.properties.hibernate.envers.autoRegisterListeners=true
spring.jpa.properties.hibernate.envers.store_data_at_delete=true