#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#

environment.type=DEV
server.base.dir=/data/app/kettle/dev
amazon.s3.report.bucket=chaayosdevtest

#Master Data Source
master.jdbc.driverClassName=org.gjt.mm.mysql.Driver
master.jdbc.url=****************************************************************
master.jdbc.user=root
master.jdbc.pass=321in#@!

master.mongo.schema=kettle_master
master.mongo.uri=mongodb://kettleusrmongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000

# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate

# properties for MAC Address authentication
mac.auth.user=muser
mac.auth.pass=mpass

business.regions=NCR,MUMBAI,CHANDIGARH

run.validate.filter=true
run.aclInterceptor=false

master.cache.host.details=localhost
master.cache.host.ports=5701,5702,5703,5704

external.partner.interceptor=false
scm.recipe.cost.detail.url=http://localhost:9595/scm-service/rest/v1/product-management/recipe/cost/detail


amazon.s3.product.bucket=product.image.dev
icon.image.host.url=https://d1nqp92n3q8zl7.cloudfront.net/product_image/

amazon.s3.offer.bucket=dev.offer.image
offer.category.image.host.url=http://dwzoagfmf5sib.cloudfront.net/

base.path.kettle.service=http://localhost:9595/kettle-service/
kettle.auth.internal=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImtldHRsZSIsImVudlR5cGUiOiJTUFJPRCIsInBhc3NDb2RlIjoiQlMyMzkiLCJpYXQiOjE1NDAyNzMxMTV9.EslEr7vSXZjKa3OrtHvXNN05pWXG7nhlowrHPIokzLc

scm.service.url=http://localhost:9595/scm-service/
scm.auth.internal=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6InNjbS1zZXJ2aWNlIiwiZW52VHlwZSI6IlBST0QiLCJwYXNzQ29kZSI6IkkyWVc0IiwiaWF0IjoxNDg4ODA2NzIyfQ.GVmjU7X5vDMNFSzKhlS67wlSpTwIRw9VLry5MOOLf0c

amazon.s3.recipe.media.bucket=dev.recipe.image
recipe.step.media.host.url=https://d2h1o90ax52pv2.cloudfront.net/

subscription.product.type=3810

unit.cache.threads.count=20
