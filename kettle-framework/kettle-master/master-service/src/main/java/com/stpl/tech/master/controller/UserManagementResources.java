/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.controller;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.RiderMappingService;
import com.stpl.tech.master.core.service.UserService;
import com.stpl.tech.master.domain.model.DocUploadTypeDTO;
import com.stpl.tech.master.domain.model.DocumentDetailDTO;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.EmployeeApplicationMapping;
import com.stpl.tech.master.domain.model.EmployeeRole;
import com.stpl.tech.master.domain.model.EmployeeRoleUpdateRequest;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import com.stpl.tech.master.domain.model.FileTypeDTO;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UserPolicyDataDTO;
import com.stpl.tech.master.service.model.EmployeeMappingData;
import com.stpl.tech.master.service.model.EmployeePasscodeData;
import com.stpl.tech.master.service.model.EmployeeUpdateData;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.RandomStringGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.print.attribute.standard.Media;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.USER_MANAGEMENT_SERVICES_ROOT_CONTEXT;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + USER_MANAGEMENT_SERVICES_ROOT_CONTEXT)
public class UserManagementResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(UserManagementResources.class);

	@Autowired
	private UserService userService;

	@Autowired
	private RiderMappingService riderService;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private MasterProperties props;

	@RequestMapping(method = RequestMethod.POST, value = "user/add", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Employee addEmployee(@RequestBody final Employee employee)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Adding Employee with name  " + employee.getName());
		return userService.addEmployee(employee,null,null);

	}

	@RequestMapping(method = RequestMethod.POST, value = "user/update/roles", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean updateEmployeeRoles(@RequestBody final EmployeeRole roles, @RequestParam(required = false) Integer uploadedDocId)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Updating roles for employee " + roles.getEmployeeId());
		userService.logRoleChangeAudit(roles.getEmployeeId(), "EMPLOYEE_ROLE_EDIT", roles.getUpdatedBy(), uploadedDocId);
		return userService.updateRoles(roles);

	}

	@RequestMapping(method = RequestMethod.POST, value = "user/update/roles-v2", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean updateEmployeeRolesV2(@RequestBody final EmployeeRoleUpdateRequest request, @RequestParam(required = false) Integer uploadedDocId)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Updating roles for employee " + request.getEmployeeId());
		userService.logRoleChangeAudit(request.getEmployeeId(), "EMPLOYEE_ROLE_EDIT", request.getUpdatedBy(), uploadedDocId);
		return userService.updateRolesV2(request);
	}

	@RequestMapping(method = RequestMethod.POST, value = "user/active/roles", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdCodeName> getActiveRoles(@RequestBody Integer employeeId)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting All Active Roles for Employee " + employeeId);
		return userService.getActiveRoles(employeeId);

	}

	@RequestMapping(method = RequestMethod.POST, value = "active/roles", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdCodeName> getActiveSystemRoles() throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting All Active Roles");
		return userService.getActiveRoles();

	}

	@RequestMapping(method = RequestMethod.GET, value = "users", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<EmployeeBasicDetail> getAllEmployees() throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting All Employee");
		return userService.getAllEmployees(null);

	}

	@RequestMapping(method = RequestMethod.POST, value = "users/unit", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<EmployeeBasicDetail> getEmployeesForUnit(@RequestBody Integer unitId)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting All Employee");
		return userService.getEmployeesForUnit(unitId);

	}

	@RequestMapping(method = RequestMethod.POST, value = "users/unit/trimmed", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdCodeName> getEmployeesForUnitTrimmed(@RequestBody Integer unitId)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting All Employee");
		return userService.getEmployeesForUnitTrimmed(unitId);

	}

	@RequestMapping(method = RequestMethod.POST, value = "users/unit/employee-meal", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<EmployeeBasicDetail> getEmployeesForEmployeeMealForUnit(@RequestBody Integer unitId)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting All Employee for Employee Meal");
		return userService.getEmployeesForEmployeeMealForUnit(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "user", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Employee getEmployee(@RequestParam("userId") final int userId)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting Employee with empId {}", userId);
		return userService.getEmployee(userId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "user-role", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<Integer,String> getEmployeeWithRole(@RequestParam("role") final String role)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting Employees with role {}", role);
		return userService.getEmployeeWithRole(role);
	}

	@RequestMapping(method = RequestMethod.POST, value = "user/active", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public EmployeeBasicDetail getActiveEmployee(@RequestBody final int userId)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting Active Employee with empId {}", userId);
		return userService.getActiveEmployee(userId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "managers", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<EmployeeBasicDetail> getAllManagers() throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Getting All Managers");
		return userService.getAllEmployees("Area Manager");
	}

	@RequestMapping(method = RequestMethod.GET, value = "employees/active", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<EmployeeBasicDetail> getActiveEmployees(
			@RequestParam(required = false) String designation,
			@RequestParam(required = false) String department) {
		LOG.info("Getting All Employees");
		return userService.getActiveEmployees(designation, department);
	}

	@RequestMapping(method = RequestMethod.POST, value = "user/update", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Employee updateEmployee(@RequestBody final Employee employee)
			throws AuthenticationFailureException, DataUpdationException {
		LOG.info("Updating employee with name  " + employee.getName());
		return userService.updateEmployee(employee);

	}

	@RequestMapping(method = RequestMethod.POST, value = "user/update-mapping", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean updateEmployeeMapping(@RequestBody final EmployeeUpdateData employeeData)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			EmailGenerationException {
		return updateEmployeeMappingData(employeeData, "update mapping");
	}

	@RequestMapping(method = RequestMethod.POST, value = "user/units", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<UnitBasicDetail> getUnitsForEmployee(@RequestBody Map<String, Object> request) throws DataNotFoundException {
		int employeeId = (int) request.get("employeeId");
		boolean onlyActive = (boolean) request.get("onlyActive");
		return userService.getUnitsForEmployee(employeeId, onlyActive);
	}

	@RequestMapping(method = RequestMethod.POST, value = "user/activate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean activateEmployee(@RequestBody final EmployeeUpdateData employeeData)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			EmailGenerationException {
		return updateEmployeeMappingData(employeeData, "activate");
	}

	private boolean updateEmployeeMappingData(EmployeeUpdateData employeeData, String action)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			EmailGenerationException {
		LOG.info(action + " employee with productId  " + employeeData.getEmployeeId() + " and unit ids "
				+ employeeData.getUnitIds());
		boolean employeeActivated = false;
		String passCode = null;
		boolean mappingsUpdated = userService.addEmployeeUnitMapping(employeeData.getEmployeeId(),
				employeeData.getUnitIds());
		if (action.equalsIgnoreCase("activate") && mappingsUpdated) {
			employeeActivated = userService.updateStatus(employeeData.getEmployeeId(), EmploymentStatus.ACTIVE);
		}
		if (action.equalsIgnoreCase("activate") && employeeActivated) {
			RandomStringGenerator generator = new RandomStringGenerator();
			passCode = generator.getRandonNumber(6);
			employeeActivated = userService.createPasscode(employeeData.getEmployeeId(), passCode);
		}
		if (employeeActivated) {
			List<String> mappedUnits = new ArrayList<>();
			for (Integer unitId : employeeData.getUnitIds()) {
				mappedUnits.add(masterCache.getUnit(unitId).getName());
			}
			Employee updatedEmployee = userService.getEmployee(employeeData.getEmployeeId());
			/*
			 * EmployeeActivationReceipt receipt; if (action.equalsIgnoreCase("activate")) {
			 * receipt = new EmployeeActivationReceipt(action, updatedEmployee, mappedUnits,
			 * props.getBasePath(), passCode); } else { receipt = new
			 * EmployeeActivationReceipt(action, updatedEmployee, mappedUnits,
			 * props.getBasePath(), "UNCHANGED"); }
			 */
			//EmployeeActivationEmail email = new EmployeeActivationEmail(receipt, props);
			//email.sendEmail();
		}
		return employeeActivated;
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "reset-passcode", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean changePassCode(@RequestBody final EmployeePasscodeData employeePassCode)
			throws AuthenticationFailureException {
		LOG.info("Resetting passcode for user  = " + employeePassCode.getEmployeeId() + " done by user "
				+ employeePassCode.getEmployeeId());
		return userService.resetPasscode(employeePassCode.getEmployeeId(), employeePassCode.getNewPasscode(),
				employeePassCode.getUpdatedBy());
	}

	@RequestMapping(method = RequestMethod.POST, value = "user/deactivate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean deactivateEmployee(@RequestBody final int employeeId) throws AuthenticationFailureException,
			DataUpdationException, DataNotFoundException, EmailGenerationException {
		return userService.updateStatus(employeeId, EmploymentStatus.IN_ACTIVE);
	}

	@RequestMapping(method = RequestMethod.POST, value = "rider/set-sdp-contact", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean setSDPContact(@RequestBody final EmployeeBasicDetail emp)
			throws AuthenticationFailureException, MasterException, DataUpdationException {
		Employee e = userService.getEmployeeBySdpContact(emp.getSdpContact());
		if (e != null) {
			String message = String.format("Contact Number %s is already assigned to %s.\n Please use any other contact", emp.getSdpContact(),
					e.getName());
			throw new MasterException("SDP Contact Error", message);
		}
		Employee employee = userService.getEmployee(emp.getId());
		employee.setSdpContact(emp.getSdpContact());
		employee = userService.updateEmployee(employee);
		riderService.removeFromCache(emp.getId());
		return employee != null;
	}

	@RequestMapping(method = RequestMethod.POST, value = "rider/clear-sdp-contact", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean clearSDPContact(@RequestBody final EmployeeBasicDetail emp)
			throws AuthenticationFailureException, MasterException, DataUpdationException {
		Employee employee = userService.getEmployee(emp.getId());
		employee.setSdpContact(null);
		employee = userService.updateEmployee(employee);
		riderService.removeFromCache(emp.getId());
		return employee != null;
	}

	@RequestMapping(method = RequestMethod.POST, value = "rider/add", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean addMapping(@RequestBody final EmployeeMappingData mapping) throws AuthenticationFailureException,
			DataUpdationException, DataNotFoundException, EmailGenerationException, MasterException {
		return riderService.addMapping(mapping.getEmployeeId(), mapping.getUnitId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "rider/enable", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean enableMapping(@RequestBody final EmployeeMappingData mapping) throws AuthenticationFailureException,
			DataUpdationException, DataNotFoundException, EmailGenerationException, MasterException {
		return riderService.enableMapping(mapping.getEmployeeId(), mapping.getUnitId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "rider/disable", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean disableMapping(@RequestBody final EmployeeMappingData mapping) throws AuthenticationFailureException,
			DataUpdationException, DataNotFoundException, EmailGenerationException {
		return riderService.disableMapping(mapping.getEmployeeId(), mapping.getUnitId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "rider/delete", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean deleteMapping(@RequestBody final EmployeeMappingData mapping) throws AuthenticationFailureException,
			DataUpdationException, DataNotFoundException, EmailGenerationException {
		return riderService.deleteMapping(mapping.getEmployeeId(), mapping.getUnitId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "rider/active", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<EmployeeBasicDetail> getActiveMapping(@RequestBody final int unitId)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
			EmailGenerationException {
		return riderService.getActiveMapping(unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "rider/all", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<EmployeeBasicDetail> getAllMapping(@RequestBody final int unitId) throws AuthenticationFailureException,
			DataUpdationException, DataNotFoundException, EmailGenerationException {
		return riderService.getAllMapping(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "employee-onboarding-sheet")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public View getEmployeeOnboardingSheet() {
		LOG.info("Request to get employee onboarding sheet");
		return userService.getEmployeeOnboardingView();
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload/employeeOnboardingSheet", consumes = MediaType.MULTIPART_FORM_DATA)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void uploadEmployeeOnboardingSheet(HttpServletRequest request,
											 @RequestParam(value = "file") final MultipartFile file) throws Exception {
		LOG.info("Request to upload employee onboarding sheet");
		userService.uploadEmployeeOnboardingSheet(file);
	}

	@RequestMapping(method = RequestMethod.POST, value = "user/addEmpAppMapping", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean addEmployeeAppMapping(@RequestBody final EmployeeApplicationMapping employeeMapping) {
		LOG.info("Adding Employee mapping with employee id: {}", employeeMapping.getEmployeeId());
		try {
			userService.addEmployeeAppMapping(employeeMapping);
		} catch (Exception e) {
			LOG.error("Error in adding employee mapping for employee id: {}", employeeMapping.getEmployeeId(), e);
			return false;
		}
		return true;
	}

	@RequestMapping(method = RequestMethod.POST, value = "user/updateEmpMapping", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean updateEmployeeAppMapping(@RequestBody final EmployeeApplicationMapping employeeMapping) {
		LOG.info("Updating employee mapping with id: {}", employeeMapping.getMappingId());
		try {
			userService.updateEmployeeAppMapping(employeeMapping);
		} catch (Exception e) {
			LOG.error("Error in updating employee mapping for mapping id: {}", employeeMapping.getMappingId(), e);
			return false;
		}
		return true;
	}

	@RequestMapping(method = RequestMethod.GET, value = "empMapping", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public EmployeeApplicationMapping getEmpAppMapping(@RequestParam("userId") final int userId) {
		LOG.info("Getting Employee application mapping with empId: {}", userId);
		return userService.getEmpAppMapping(userId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "upload-document", consumes = MediaType.MULTIPART_FORM_DATA)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public DocumentDetailDTO uploadDocument(HttpServletRequest request,
													@RequestParam(value = "type") FileTypeDTO type,
													@RequestParam(value = "mimeType") MimeType mimeType,
													@RequestParam(value = "userId") Integer userId,
													@RequestParam(value = "docType") DocUploadTypeDTO docType,
													@RequestParam(value = "file") final MultipartFile file,
													@RequestParam String docName) throws DocumentException, IOException {
		return userService.uploadDocument(type, mimeType, docType, userId, file, docName);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-user-policies", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<UserPolicyDataDTO> getUserPolicies() {
		return userService.getUserPolicies();
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-update-user-roles-for-policy", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean createUpdateUserRolesForPolicy(@RequestBody final EmployeeRole employeeRole,
												  @RequestParam(required = false) Integer uploadedDocId, @RequestParam(required = false) String departmentDesignation) throws DataUpdationException {
		LOG.info("Creating Updating roles for Policy ");
		if (Objects.nonNull(employeeRole.getUpdatePolicy()) && employeeRole.getUpdatePolicy()) {
			userService.logRoleChangeAudit(employeeRole.getUserPolicyId(), "USER_POLICY_ROLE_EDIT", employeeRole.getUpdatedBy(), uploadedDocId);
		}
		return userService.createUpdateUserRolesForPolicy(employeeRole, uploadedDocId, departmentDesignation);
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-employees-with-department-designation", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<EmployeeBasicDetail> getEmployeesWithDepartmentDesignation(@RequestParam Integer departmentId, @RequestParam Integer designationId) {
		LOG.info("Getting Employees With Department : {} and Designation : {} ",departmentId,designationId);
		return userService.getEmployeesWithDepartmentDesignation(departmentId,designationId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "reset-update-employee-user-policy", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean resetUpdateEmployeeUserPolicy(@RequestParam Integer employeeId, @RequestParam Integer policyId,
												 @RequestParam Integer updatedBy, @RequestParam Integer uploadedDocId, @RequestParam Boolean overrideRoles) {
		LOG.info("Updating Employee With Employee Id : {} With Policy Id : {} ",employeeId,policyId);
		boolean check = userService.resetUpdateEmployeeUserPolicy(employeeId,policyId,updatedBy,uploadedDocId);
		if (check) {
			userService.logRoleChangeAudit(employeeId, "EMPLOYEE_ROLE_EDIT", updatedBy, uploadedDocId);
			List<Integer> roleIds = new ArrayList<>();
			if (Boolean.FALSE.equals(overrideRoles)) {
				List<IdCodeName> activeRoles = userService.getActiveRoles(employeeId);
				if (Objects.nonNull(activeRoles) && !activeRoles.isEmpty()) {
					for (IdCodeName activeRole: activeRoles) {
						if (!roleIds.contains(activeRole.getId())) {
							roleIds.add(activeRole.getId());
						}
					}
				}
			}
			userService.updateEmployeeRoles(employeeId, updatedBy, policyId, roleIds);
		}
		return check;
	}
}
