/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import com.stpl.tech.master.core.external.cache.ACLCache;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.LocationValidationService;
import com.stpl.tech.master.core.service.UserService;
import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.master.core.service.model.UserDetailForOrderCancellation;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.model.ApplicationData;
import com.stpl.tech.master.data.repository.ApplicationDataRepository;
import com.stpl.tech.master.domain.model.ACLRequest;
import com.stpl.tech.master.domain.model.ACLResponse;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Department;
import com.stpl.tech.master.domain.model.Division;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.service.model.UnitLoginData;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.spring.crypto.CryptoService;
import com.stpl.tech.util.AppConstants;
import io.jsonwebtoken.Claims;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Cipher;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;
import static com.stpl.tech.master.service.core.MasterServiceConstants.USER_SERVICES_ROOT_CONTEXT;


@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + USER_SERVICES_ROOT_CONTEXT)
public class UserResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(UserResources.class);

	@Autowired
	private MasterProperties props;

	@Autowired
	private UserService userService;

	@Autowired
	private ACLCache aclCache;

	@Autowired
	private TokenService<JWTToken> jwtService;

	@Value("${data.encryption.passcode}")
	private String passCode;

	@Value("${data.encryption.salt}")
	private String salt;

	@Autowired
	private CryptoService cryptoService;

	@Autowired
	@Qualifier("encryptionCipher")
	private Cipher encryptionCipher;

	@Autowired
	private LocationValidationService locationValidationService;

	@Autowired
	private TokenService tokenService;

	@Autowired
	private ApplicationDataRepository applicationDataRepository;



	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws DataNotFoundException
	 * @throws AuthenticationFailureException
	 */
	@SuppressWarnings("deprecation")
	@RequestMapping(method = RequestMethod.POST, value = "login", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public UserSessionDetail authenticateUser(@RequestBody final UserSessionDetail userSession,
											  final HttpServletResponse response, final HttpServletRequest request,
											  @RequestParam(required = false) Integer companyId,
											  @RequestParam(required = false) Integer brandId,
											  @RequestHeader(value = "X-Application-Id", required = false) Integer applicationId,
											  @RequestHeader(value = "X-MAC-Address", required = false) String macAddressHeader,
											  @RequestHeader(value = "X-Geo-Location", required = false) String geoLocation)
			throws DataNotFoundException, AuthenticationFailureException {
		LOG.info("Authenticating User with userID  = " + userSession.getUserId() + " for application: "
				+ userSession.getApplication());
		if (userSession.getApplication() == null) {
			LOG.info("Login called with invalid application!!!");
			return null;
		}
		String passCode = userSession.getPassword();
		String ipAddress = MasterUtil.getUserIpAddress(request);
		String macAddress = userSession.getMacAddress() != null ? URLDecoder.decode(userSession.getMacAddress())
				: userSession.getMacAddress();
		// Validate location if headers are provided
		if (applicationId != null && (macAddressHeader != null || geoLocation != null) && userSession.getUnitId() >0) {
			//locationValidationService.validateLocation(applicationId, macAddressHeader, geoLocation, userSession.getUnitId());
			userSession.setMacAddress(macAddressHeader);
			userSession.setGeoLocation(geoLocation);
		}
		String userAgent = request.getHeader("user-agent");
		try {
			ApplicationName app =   ApplicationName.valueOf(userSession.getApplication());
			Employee emp = userService.getEmployee(userSession.getUserId());
			if (emp != null && emp.getEmploymentStatus().equals(EmploymentStatus.ACTIVE)
					&& emp.hasAccess(ApplicationName.valueOf(userSession.getApplication()))) {
				String sessionKey = userService.authenticateUser(userSession, ipAddress, macAddressHeader, userAgent,
						isAdmin(emp));
				List<Integer> mappedUnits = userService.getActiveEmpUnitIds(emp.getId());
				if(Objects.nonNull(userSession.getScreenType()) && userSession.getScreenType().equals(ScreenType.POS) &&
						Objects.nonNull(userSession.getUnitId())){
					Integer unitPosVersion = userService.getPosVersionEnabledForUnit(userSession.getUnitId());
					userSession.setPosVersionEnabled(unitPosVersion);
				}

				if(app.getRefreshTokenEnabled()){
					// Create refresh token
					String refreshToken = jwtService.createRefreshToken(
							sessionKey,
							userSession.getUnitId(),
							userSession.getUserId(),
							userSession.getApplication(),
							macAddressHeader,
							geoLocation,
							userSession.getTerminalId()
					);

					// Set refresh token in response header
					response.setHeader("X-Refresh-Token", refreshToken);
					response.setHeader("Access-Control-Expose-Headers", "x-access-token, x-refresh-token");
				}

				UserSessionDetail sessionDetail = setUserSession(userSession, sessionKey, passCode, emp, userSession.getApplication(),
						mappedUnits, companyId, brandId);
				
				// Set access token from setUserSession in response header
				response.setHeader("X-Access-Token", sessionDetail.getJwtToken());
				
				return sessionDetail;
			} else {
				String message = String.format("User %s does not have Access to application %s",
						userSession.getUserId(), userSession.getApplication());
				LOG.info(message);
				throw new AuthenticationFailureException(message);
			}
		} catch (AuthenticationFailureException e) {
			LOG.info("Error in authenticating user {} at Unit {}, ERROR : {}", userSession.getUserId(),
					userSession.getUnitId(), e.getMessage());
			throw e;
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "single-sign-in", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public UserSessionDetail singleSignIn(@RequestBody final UserSessionDetail userSession, final HttpServletRequest request)
			throws DataNotFoundException, AuthenticationFailureException {
		LOG.info("Authenticating User with userID  = " + userSession.getUserEmail() + " for application: "
				+ userSession.getApplication());
		if (userSession.getApplication() == null) {
			LOG.info("Login called with invalid application!!!");
			return null;
		}
		String passCode = userSession.getPassword();
		String ipAddress = MasterUtil.getUserIpAddress(request);
		String macAddress = userSession.getMacAddress() != null ? URLDecoder.decode(userSession.getMacAddress())
				: userSession.getMacAddress();
		String userAgent = request.getHeader("user-agent");
		try {
			Integer empId = userService.getEmployeeIdByEmail(userSession.getUserEmail());
			if(empId == null){
				String message = String.format("User with email %s does not exist",
						userSession.getUserEmail());
				LOG.info(message);
				throw new AuthenticationFailureException(message);
			}
			Employee emp = userService.getEmployee(empId);
			userSession.setUserId(emp.getId());
			if (emp != null && EmploymentStatus.ACTIVE.equals(emp.getEmploymentStatus())
					&& emp.hasAccess(ApplicationName.valueOf(userSession.getApplication()))) {
				String sessionKey = userService.authenticateUser(userSession, ipAddress, macAddress, userAgent,
						isAdmin(emp));
				List<Integer> mappedUnits = userService.getActiveEmpUnitIds(emp.getId());
				return setUserSession(userSession, sessionKey, passCode, emp, userSession.getApplication(),
						mappedUnits, null, null);
			} else {
				String message = String.format("User %s does not have Access to application %s",
						userSession.getUserId(), userSession.getApplication());
				LOG.info(message);
				throw new AuthenticationFailureException(message);
			}
		} catch (AuthenticationFailureException e) {
			LOG.info("Error in authenticating user {} at Unit {}, ERROR : {}", userSession.getUserId(),
					userSession.getUnitId(), e.getMessage());
			throw e;
		}
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws DataNotFoundException
	 * @throws AuthenticationFailureException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "session-login", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public UserSessionDetail authenticateSessionUser(@RequestBody String applicationName,
			final HttpServletResponse response, final HttpServletRequest request)
			throws DataNotFoundException, AuthenticationFailureException {
		applicationName = MasterUtil.removeDoubleQuotes(applicationName);
		String authHeaderEncrypted = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
		if(authHeaderEncrypted != null){
			authHeaderEncrypted = URLDecoder.decode(authHeaderEncrypted).replace("\r\n","");
		}
		String authHeader = PasswordImpl.decrypt(authHeaderEncrypted);
		UserSessionDetail userSession = validateAndCreate(applicationName,authHeader, request, null);
		return authenticateUser(userSession, response, request, null, null,null,null,null);
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws DataNotFoundException
	 * @throws AuthenticationFailureException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "unit-session-login", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public UserSessionDetail authenticateSessionUser(@RequestBody final UnitLoginData login,
			final HttpServletResponse response, final HttpServletRequest request)
			throws DataNotFoundException, AuthenticationFailureException {
		
		String authHeaderEncrypted = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
		if(authHeaderEncrypted != null){
			authHeaderEncrypted = URLDecoder.decode(authHeaderEncrypted).replace("\r\n","");
		}
//		String authHeader = PasswordImpl.decrypt(authHeaderEncrypted);
		UserSessionDetail userSession = validateAndCreate(login.getApplicationName(),authHeaderEncrypted, request, login.getUnitId());
		return authenticateUser(userSession, response, request, null, null,null,null,null);
	}

	@RequestMapping(method = RequestMethod.POST, value = "encrypt-jwtToken", consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String encryptJwtToken(final HttpServletResponse response, final HttpServletRequest request, @RequestParam(value = "applicationName") String applicationName)
			throws DataNotFoundException, AuthenticationFailureException {

		String authHeader = null;
		if (Objects.equals(applicationName, "KNOCK_SERVICE")) {
			authHeader = request.getHeader("jwtToken") != null ? request.getHeader("jwtToken").trim() : null;
		}
		return PasswordImpl.encrypt(authHeader);
	}
	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws DataNotFoundException
	 * @throws AuthenticationFailureException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "encrypt-auth", consumes = MediaType.APPLICATION_JSON, produces = MediaType.TEXT_PLAIN)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String encryptAuth(final HttpServletResponse response, final HttpServletRequest request)
			throws DataNotFoundException, AuthenticationFailureException {
		String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
		return PasswordImpl.encrypt(authHeader);
	}

	/**
	 * @param applicationName
	 * @param authHeader
	 * @param request
	 * @return
	 */
	private UserSessionDetail validateAndCreate(String applicationName, String authHeader, HttpServletRequest request, Integer loginUnitId) throws AuthenticationFailureException {
		if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
			JWTToken jwtToken = new JWTToken();
			jwtService.parseToken(jwtToken, authHeader);
			int terminalId = Integer.valueOf(jwtToken.getTerminalId());
			int userId = Integer.valueOf(jwtToken.getUserId());
			int unitId = loginUnitId != null ? loginUnitId : Integer.valueOf(jwtToken.getUnitId());
			UserSessionDetail detail = new UserSessionDetail();
			detail.setApplication(applicationName);
			detail.setTerminalId(terminalId);
			detail.setUnitId(unitId);
			detail.setUserId(userId);
			detail.setPassword(PasswordImpl.decrypt(userService.getEmplyeePassCode(userId)));
			String userAgent = request.getHeader("User-Agent");
			detail.setUserAgent(userAgent);
			detail.setIpAddress(request.getRemoteAddr());
			return detail;
		}
		return null;
	}

	private boolean isAdmin(Employee emp) {
		return emp.getDepartment().getId() == AppConstants.DEPARTMEMT_CORPORATE_ID
				&& emp.getDesignation().getId() == AppConstants.DESIGNATION_ADMIN_ID;
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "changePasscode", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean changePassCode(@RequestBody final UserSessionDetail userSession, final HttpServletRequest request) {
		LOG.info("Changing passcode for user  = " + userSession.getUserId());
		boolean status = false;
		try {
			String macAddress = userSession.getMacAddress() != null ? URLDecoder.decode(userSession.getMacAddress())
					: userSession.getMacAddress();
			status = userService.changePasscode(userSession, MasterUtil.getUserIpAddress(request), macAddress,
					request.getHeader("user-agent"));
		} catch (AuthenticationFailureException e) {
			LOG.info(e.getMessage());
			status = false;
		} catch (Exception e) {
			LOG.error("ERROR while changing Passcode ", e);
			status = false;
		}
		return status;
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "logout", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean logout(@RequestBody final UserSessionDetail userSession)
			throws AuthenticationFailureException, DataNotFoundException {
		LOG.info("Logging Out User with userID  = " + userSession.getUserId());
		Integer unitId = userSession.getUnitId();
		boolean logout = userService.logout(unitId, userSession.getUserId(), userSession.getSessionKeyId());
		if (logout) {
			aclCache.removeFromCache(userSession.getSessionKeyId());
		}
		return logout;
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataNotFoundException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "verifyUser", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean verifyUser(@RequestBody final UserSessionDetail userSession, final HttpServletRequest request)
			throws AuthenticationFailureException, DataNotFoundException {
		LOG.info("Verify User with userID  = " + userSession.getUserId());
		String macAddress = userSession.getMacAddress() != null ? URLDecoder.decode(userSession.getMacAddress())
				: userSession.getMacAddress();
		boolean result = false;
		try {
			result = userService.verifyUser(userSession, MasterUtil.getUserIpAddress(request), macAddress,
					request.getHeader("user-agent"));
		} catch (AuthenticationFailureException e) {
			LOG.info("Verification failed for user with User with userID  = " + userSession.getUserId());
		}
		return result;
	}

	// example URL for usage
	// http://localhost:8080/kettle-service/rest/v1/users/authenticateMac?cuser=muser&cpass=mpass&cmac=z2
	@RequestMapping(method = RequestMethod.GET, value = "authenticateMac", consumes = MediaType.TEXT_PLAIN, produces = MediaType.TEXT_PLAIN)
	@ResponseBody
	public Boolean authenticateMac(@RequestParam final String cuser, @RequestParam final String cpass,
			@RequestParam final String cmac, HttpServletRequest request, HttpServletResponse response) {
		String user = null;
		String pass = null;
		String macAddress = null;
		user = cuser;
		pass = cpass;
		macAddress = cmac;
		String storedUser = props.getMacAuthUser();
		String storedPass = props.getMacAuthPassword();
		if (user.equals(storedUser) && pass.equals(storedPass)) {
			userService.authorizeMac(macAddress);
			return true;
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.POST, value = "authenticateMac", consumes = MediaType.TEXT_PLAIN, produces = MediaType.TEXT_PLAIN)
	@ResponseBody
	public Boolean authenticateMacSecure(@RequestBody final String macAuthParams) {
		String storedUser = props.getMacAuthUser();
		String storedPass = props.getMacAuthPassword();
		String[] params = macAuthParams.split("#");
		String user = params[0];
		String pass = params[1];
		String macAddress = params[2];
		if (user.equals(storedUser) && pass.equals(storedPass)) {
			userService.authorizeMac(macAddress);
			return true;
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.GET, value = "all", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdName> getAllEmployees() throws AuthenticationFailureException, DataUpdationException {
		return userService.getActiveEmployeeIdName();
	}

	@RequestMapping(method = RequestMethod.GET, value = "purchaseRoles", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public List<IdCodeName> getPurchaseRoles(@RequestParam final int empId)
			throws AuthenticationFailureException, DataNotFoundException {
		return userService.getPurchaseRoles(empId);
	}

	private long getTokenTTL(ApplicationName app) {
		// Default TTL is -1 (no expiration)
		long ttl = -1L;
		if(!app.getRefreshTokenEnabled()) {
			return ttl;
		}
		try {
			ApplicationData appData = applicationDataRepository.findByName(app.name());
			if (appData != null && appData.getTokenTtl() != null) {
				ttl = appData.getTokenTtl().longValue() * 60 * 60 * 1000L; // Convert hours to milliseconds
			}
		} catch (Exception e) {
			LOG.warn("Failed to fetch TTL for application {}: {}", app, e.getMessage());
		}
		
		return ttl;
	}

	private UserSessionDetail setUserSession(UserSessionDetail userSession, String sessionKey, String passCode,
			Employee emp1, String issuer, List<Integer> mappedUnits, Integer companyId, Integer brandId) {
		Employee emp = emp1;
		emp.setUnits(new HashSet<Integer>(mappedUnits));
		ApplicationName app = ApplicationName.valueOf(issuer);
		if ((app.equals(ApplicationName.KETTLE_ADMIN) || app.equals(ApplicationName.KETTLE_ANALYTICS)
				|| app.equals(ApplicationName.KETTLE_CHECKLIST)) && emp1 != null) {
			emp = new Employee();
			Department department = new Department();
			department.setId(emp1.getDepartment().getId());
			department.setName(emp1.getDepartment().getName());
			Division division = new Division();
			division.setId(emp1.getDepartment().getDivision().getId());
			division.setName(emp1.getDepartment().getDivision().getName());
			department.setDivision(division);
			emp.setDepartment(department);
			emp.setName(emp1.getName());
			emp.setId(emp1.getId());
			emp.setDesignation(emp1.getDesignation());
			emp.setEmployeeEmail(emp1.getEmployeeEmail());
			emp.setEmployeeCode(emp1.getEmployeeCode());
			emp.setCompany(emp1.getCompany());
			if (!app.equals(ApplicationName.KETTLE_CHECKLIST)) {
				userSession.setUnitId(0);
				userSession.setTerminalId(0);
			}
		}
		userSession.setSessionKeyId(sessionKey);
		userSession.setNewPassword(null);
		userSession.setPassword(null);
		userSession.setUser(emp);
		userSession.setIpAddress(null);
		//userSession.setMacAddress(null);
		userSession.setUserAgent(null);
		userSession.setIsDefaultPasscode(false);
		userSession.setPermissions(aclCache.getPermissions(sessionKey));

		JWTToken token = new JWTToken();
		if(StringUtils.isNotBlank(userSession.getMacAddress()) || StringUtils.isNotBlank(userSession.getGeoLocation())){
			token = new JWTToken(sessionKey, userSession.getUnitId(), issuer, userSession.getUserId(),
					userSession.getTerminalId(),userSession.getMacAddress(),userSession.getGeoLocation());
		}else{
			// Create JWT token with TTL from application data
			token = new JWTToken(sessionKey, userSession.getUnitId(), issuer, userSession.getUserId(),
					userSession.getTerminalId());

			// Get TTL from application data
		}

		long ttl = getTokenTTL(app);
		userSession.setJwtToken(jwtService.createToken(token, ttl));

		if (passCode != null && passCode.equals(AppConstants.DEFAULT_PASSCODE)) {
			userSession.setIsDefaultPasscode(true);
		}
		setAclData(app, userSession, companyId, brandId);
		return userSession;
	}

	private void setAclData(ApplicationName app, UserSessionDetail userSession, Integer companyId, Integer brandId) {
		userSession.setAcl(userService.getAcl(app, userSession.getUserId(), companyId, brandId));
	}

	@RequestMapping(method = RequestMethod.POST, value = "verifyUserForCancellation", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public Boolean verifyUserForOrderCancellation(@RequestBody UserDetailForOrderCancellation userDetail)
			throws AuthenticationFailureException, DataNotFoundException {
		return userService.verifyUserForOrderCancellation(userDetail.getEmpId(),userDetail.getPasscode());
	}

	@GetMapping(value = "/validate/session", consumes = MediaType.TEXT_PLAIN)
	@ResponseBody
	public Boolean validateSessionCache(@RequestBody String sessionKey){
		return Objects.nonNull(aclCache.getPermissions(sessionKey).get(sessionKey));
	}

	@PostMapping(value = "/invalidate/unit/session", consumes = MediaType.APPLICATION_JSON)
	@ResponseBody
	public void inValidateSessionCache(@RequestBody List<Integer> unitId){
		userService.inValidateSessionCache(unitId);
	}


	@RequestMapping(method = RequestMethod.POST, value = "login/creds", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<String,String> getCreds(@RequestBody final UserSessionDetail userSession,
									   final HttpServletResponse response, final HttpServletRequest request) {
		LOG.info("Authenticating User with userID  = " + userSession.getUserId() + " for application: "
				+ userSession.getApplication());
		if (userSession.getApplication() == null) {
			LOG.info("Get creds called with invalid application!!!");
			return null;
		}
		try {
			Employee emp = userService.getEmployee(userSession.getUserId());
			if (emp != null && emp.getEmploymentStatus().equals(EmploymentStatus.ACTIVE)
					&& emp.hasAccess(ApplicationName.valueOf(userSession.getApplication()))) {
				Map<String,String> map = new HashMap<>();
				map.put("p",passCode);
				map.put("s",salt);
				map.put("ak",cryptoService.encrypt("********************", encryptionCipher));
				map.put("sk",cryptoService.encrypt("oqPiJswD/nuFTlL++KYivNq68D7n+KOYgKxNRpq0", encryptionCipher));
				return map;
			} else {
				String message = String.format("User %s does not have Access to application %s",
						userSession.getUserId(), userSession.getApplication());
				LOG.info(message);
				throw new AuthenticationFailureException(message);
			}
		} catch (Exception e) {
			LOG.info("Error in fetching creds for user {} at Unit {}, ERROR : {}", userSession.getUserId(),
					userSession.getUnitId(), e.getMessage());
		}
		return new HashMap<>();
	}

	@GetMapping(value = "get-acl", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public ACLResponse getAcl(@RequestParam ApplicationName applicationName, @RequestParam Integer userId,
													@RequestParam(required = false) Integer companyId,
													@RequestParam(required = false) Integer brandId) {
		return new ACLResponse(userService.getAcl(applicationName, userId, companyId, brandId));
	}

	// Add new endpoint for token refresh
	@RequestMapping(method = RequestMethod.POST, value = "refresh-token", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ResponseEntity<?> refreshToken(
			@RequestHeader("X-Refresh-Token") String refreshToken,
			@RequestHeader(value = "X-MAC-Address", required = false) String macAddress,
			@RequestHeader(value = "X-Geo-Location", required = false) String geoLocation,
			HttpServletResponse response) {
		try {
			// Parse refresh token
			Claims claims = jwtService.parseRefreshToken(refreshToken);
			
			// Validate refresh token
			if (jwtService.isRefreshTokenExpired(refreshToken)) {
				throw new AuthenticationFailureException("Refresh token has expired");
			}

			// Get application type and TTL
			ApplicationName app = ApplicationName.valueOf((String) claims.get("application"));
			long ttl = getTokenTTL(app);

			// Create new JWT token with TTL from application data
			JWTToken newToken = new JWTToken(
				(String) claims.get("sessionKey"),
				(Integer) claims.get("unitId"),
				(String) claims.get("application"),
					(int)claims.get("userId"),
					(int)claims.get("terminalId"),
					(String) 	claims.get("macAddress"),
					(String) claims.get("geoLocation")
			);
			String newAccessToken = jwtService.createToken(newToken, ttl);

			// Increment refresh token usage counter
			//refreshTokenCache.incrementRefreshTokenUsage(refreshToken);

			// Only create new refresh token if max uses reached
			String newRefreshToken = refreshToken;
			newRefreshToken = jwtService.createRefreshToken(
					(String) claims.get("sessionKey"),
					(int) claims.get("unitId"),
					(int) claims.get("userId"),
					(String) claims.get("application"),
					macAddress,
					geoLocation,
					newToken.getTerminalId()
			);
			/*if (refreshTokenCache.shouldRotateRefreshToken(refreshToken)) {
				newRefreshToken = jwtService.createRefreshToken(
					(String) claims.get("sessionKey"),
					(String) claims.get("userId"),
					(String) claims.get("application"),
					macAddress,
					geoLocation
				);
				// Remove old refresh token from cache
				refreshTokenCache.removeRefreshToken(refreshToken);
			}*/

			// Set tokens in response headers
			response.setHeader("X-Access-Token", newAccessToken);
			response.setHeader("X-Refresh-Token", newRefreshToken);

			return ResponseEntity.ok()
					.body(Map.of("message", "Token refresh successful"));

		} catch (Exception e) {
			LOG.error("Token refresh failed", e);
			return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
					.body(Map.of("message", "Token refresh Failed !"));
		}
	}

	@PostMapping("get/emp-by-acl")
	public ApiResponse getEmpListByAcl(@RequestBody ACLRequest aclRequest) {
		return new ApiResponse( userService.getEmpListByAcl(aclRequest) );
	}

    /**
     * Send employee credentials via email to all active employees
     * @return ResponseEntity with success message and count of emails sent
     */
    @PostMapping(value = "send-employee-credentials-email", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ResponseEntity<?> sendEmployeeCredentialsEmail(@RequestBody(required = false) List<Integer> employeeIds) {
        LOG.info("Received request to send employee credentials email to all active employees");

        try {
            Integer emailsSent = userService.sendEmployeeCredentialsEmail(employeeIds);

            String message = String.format("Successfully sent employee credentials email to %d active employees", emailsSent);
            LOG.info(message);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", message);
            response.put("emailsSent", emailsSent);
            return ResponseEntity.ok().body(response);

        } catch (Exception e) {
            LOG.error("Error sending employee credentials email", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to send employee credentials emails: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

}
