/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.master.controller;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.kettle.report.metadata.model.NameValue;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.MasterMetadataService;
import com.stpl.tech.master.core.service.ProductManagementService;
import com.stpl.tech.master.data.model.NutritionDataEnum;
import com.stpl.tech.master.data.model.ProductCheckListEvent;
import com.stpl.tech.master.data.model.ProductDescription;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.data.model.ProductNutritionDetail;
import com.stpl.tech.master.data.model.ProductRecipeBulkRequest;
import com.stpl.tech.master.data.model.UnitProductPriceCategoryDomain;
import com.stpl.tech.master.data.model.UnitProductProfileContext;
import com.stpl.tech.master.data.model.UnitProductUpdateType;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ImageCategoryType;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.MachineProductMetaDataResponse;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductCheckList;
import com.stpl.tech.master.domain.model.ProductCityImageMappingDomain;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.ProductImageMappingDetail;
import com.stpl.tech.master.domain.model.ProductImageMappingDetailList;
import com.stpl.tech.master.domain.model.ProductPackagingMappingSheet;
import com.stpl.tech.master.domain.model.ProductRecipeMappingRequest;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitProductMappingData;
import com.stpl.tech.master.domain.model.UnitProductPackagingRequest;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.domain.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.PRODUCT_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PRODUCT_METADATA_ROOT_CONTEXT)
public class ProductManagementResources extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(ProductManagementResources.class);

    @Autowired
    private MasterMetadataService masterMetadataService;

    @Autowired
    private ProductManagementService productServices;

    @Autowired
    private MasterDataCache cache;

    @Autowired
    private MasterDataCacheService masterDataCacheService;

    @Autowired
    @Qualifier("taskExecutor")
    private ThreadPoolTaskExecutor taskExecutor;

    @RequestMapping(method = RequestMethod.POST, value = "products", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Product> getAllProducts(@RequestParam (required = false) Integer brandId) throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting All Products");
        return masterMetadataService.getAllProducts(brandId);
    }

    @GetMapping("products-by-brand")
    public Map<Integer, Map<Integer, List<IdCodeName>>> allProducts() {
        return masterMetadataService.allProducts();
    }

    @RequestMapping(method = RequestMethod.POST, value = "products/active", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Product> allActiveProducts(@RequestParam (required = false) Integer brandId) throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting All Active Products");
        return masterMetadataService.getAllProducts(brandId).stream().filter(product -> product.getStatus().equals(ProductStatus.ACTIVE)).collect(Collectors.toList());
    }

    @RequestMapping(method = RequestMethod.GET, value = "product-info", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductBasicDetail> allProductsInfo() throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting All Products Basic Detail");
        return cache.getAllProductsBasicDetail();
    }

    @RequestMapping(method = RequestMethod.GET, value = "products/active/web", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductBasicDetail> allProductsActiveOnWeb()
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting All Products Active On Web Detail");
        List<ProductBasicDetail> activeProducts = new ArrayList<>();
        for (ProductBasicDetail product : cache.getAllProductsBasicDetail()) {
            if (ProductStatus.ACTIVE.equals(product.getStatus())
                && product.getType() != AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
                activeProducts.add(product);
            }
        }
        return activeProducts;
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/add", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Product addProduct(@RequestBody final Product product)
        throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {
        LOG.info("Adding product with name  " + product.getName());
        return productServices.addProduct(product);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Product updateProduct(@RequestBody final Product product)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Updating product with name  " + product.getName());
        return productServices.updateProduct(product);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/activate", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean activateProduct(@RequestBody final int product) throws DataUpdationException, DataNotFoundException {
        LOG.info("Activating product with productId  " + product);
        return productServices.changeProductStatus(product, ProductStatus.ACTIVE);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/deactivate", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean deactivateProduct(@RequestBody final int product)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Deactivating product with productId  " + product);
        return productServices.changeProductStatus(product, ProductStatus.IN_ACTIVE);
    }

    // TODO remove after discussion with Mohit
    /*
     * @RequestMapping(method = RequestMethod.GET, value = "vendor", produces =
     * MediaType.APPLICATION_JSON)
     *
     * @ResponseStatus(HttpStatus.OK)
     *
     * @ResponseBody public List<Vendor> getVendors() throws DataUpdationException,
     * DataNotFoundException { LOG.info( "Getting All Vendors"); return
     * productServices.getVendors(); }
     */

    @RequestMapping(method = RequestMethod.POST, value = "product/price/mappings", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<UnitProductMappingData> getUnitProductPrice(@RequestParam UnitCategory unitCategory,
                                                            @RequestParam String unitRegion, @RequestParam int productId, @RequestParam int dimensionId)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Request for product prices");
        return productServices.getUnitProductPrice(unitCategory, unitRegion, productId, dimensionId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "exception-day/unit-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> getUnitExceptionDetail(@RequestParam UnitCategory unitCategory,
                                                   @RequestParam String unitRegion)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Request for Unit Detail");
        return productServices.getUnitDetails(unitCategory, unitRegion);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/price/update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateUnitProductPrice(@RequestBody final List<UnitProductMappingData> mappings, @RequestParam Integer updatedBy)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Updating product prices");
        Set<Integer> unitSet = productServices.updateUnitProductPrice(mappings, updatedBy);
        /*
         * for (Integer unitId : unitSet) { try { productServices.refreshUnit(unitId); }
         * catch (DataNotFoundException e) {
         * LOG.error("Error While updating Unit {} in cache", unitId); } }
         */
        return true;
    }

    @PostMapping(value = "unit-product-profile/update")
    public Boolean updateUnitProductProfile(@RequestBody final List<UnitProductMappingData> mappings, @RequestParam Integer updatedBy) {
        LOG.info("Updating product profiles");
        return productServices.updateUnitProductProfile(mappings, updatedBy);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-product/price/update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateUnitProductPrice(@RequestParam int unitId, @RequestParam int productId,
                                          @RequestParam String dimensionCode, @RequestParam BigDecimal price, @RequestParam String profile)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Updating product prices to {} at Unit {} for Product {} with Dimension {} with profile : ", price, unitId, productId,
            dimensionCode, profile);
        boolean status = productServices.updateUnitProductPrice(unitId, productId, dimensionCode, price, profile);
        //productServices.refreshUnit(unitId);
        return status;
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/mapping/activate", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean activateUnitProductMapping(@RequestParam int unitId, @RequestParam int productId)
        throws DataNotFoundException {
        LOG.info("Activating Unit Product Mapping at unit {} for product {} ", unitId, productId);
        boolean status = productServices.updateUnitProductMappingStatus(unitId, productId, ProductStatus.ACTIVE);
        //productServices.refreshUnit(unitId);
        return status;
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/mapping/deactivate", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean deactivateUnitProductMapping(@RequestParam int unitId, @RequestParam int productId)
        throws DataNotFoundException {
        LOG.info("Deactivating Unit Product Mapping at unit {} for product {} ", unitId, productId);
        boolean status = productServices.updateUnitProductMappingStatus(unitId, productId, ProductStatus.IN_ACTIVE);
        //productServices.refreshUnit(unitId);
        return status;
    }

    @RequestMapping(method = RequestMethod.GET, value = "classifications", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductClassification[] allProductClassification() {
        LOG.info("Getting All Product Classifications");
        return ProductClassification.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "categories/web", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> getWebCategories() throws DataNotFoundException {
        LOG.info("Request to get web categories");
        List<ListData> data = masterMetadataService.getAllListData(AppConstants.RTL_GROUP_WEB_CATEGORY, false);
        if (data != null && !data.isEmpty()) {
            return data.get(0).getContent();
        }
        return null;
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-product-image", consumes = MediaType.MULTIPART_FORM_DATA)
    public ProductImageMappingDetail uploadProductImage(HttpServletRequest request,
                                                        @RequestParam(required = false, value = "mimeType") MimeType mimeType,
                                                        @RequestParam(value = "productId") int productId,
                                                        @RequestParam(value = "imageCategoryType") ImageCategoryType imageCategoryType,
                                                        @RequestParam(required = false, value = "file") final MultipartFile file,
                                                        @RequestParam(value = "updatedBy") int updatedBy,
                                                        @RequestParam(required = false, value = "index") Integer index, @RequestParam(required = false, value = "Link") String link) {
        return productServices.saveProductImage(mimeType, productId, imageCategoryType, file, updatedBy, index == null ? 1 : index, link);
    }

    @RequestMapping(method = RequestMethod.GET, value = "deactivate-product-image-by-city")
    public Boolean deactivateProductImageByCity(HttpServletRequest request,
                                                              @RequestParam(value = "productId") int productId,
                                                              @RequestParam(required = false, value = "cityName") String cityName,
                                                              @RequestParam(required = false) String daySlot) {
        Integer updatedBy = getLoggedInUser(request);
        return productServices.deactivateImageByCity(productId,  updatedBy, cityName, daySlot);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-product-image-by-city", consumes = MediaType.MULTIPART_FORM_DATA)
    public ProductImageMappingDetail uploadProductImageByCity(HttpServletRequest request,
                                                              @RequestParam(required = false, value = "mimeType") MimeType mimeType,
                                                              @RequestParam(value = "productId") int productId,
                                                              @RequestParam(required = false, value = "file") final MultipartFile file,
                                                              @RequestParam(value = "updatedBy") int updatedBy,
                                                              @RequestParam(required = false, value = "cityName") String cityName,
                                                              @RequestParam(required = false, value = "Link") String link,
                                                              @RequestParam(required = false) String daySlot) {
        return productServices.saveProductImageByCity(mimeType, productId,  file, updatedBy, cityName, link,daySlot);
    }

    @RequestMapping(method = RequestMethod.GET, value = "uploaded-product-image", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductImageMappingDetail getProductImages(@RequestParam(value = "productId") int productId) throws DataNotFoundException {
        return productServices.getProductImages(productId);
    }


    @RequestMapping(method = RequestMethod.GET, value = "all-uploaded-product-image", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductImageMappingDetailList getAllProductImages() throws DataNotFoundException {
        return productServices.getAllProductImages();
    }

    @RequestMapping(method = RequestMethod.POST, value = "uploaded-product-images-for-productIds", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductImageMappingDetailList getProductImages(@RequestBody List<Integer> list) throws DataNotFoundException {
        return productServices.getAllProductImages(list);
    }

    @RequestMapping(method = RequestMethod.POST, value = "uploaded-product-images-for-productIds-by-city", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer,Map<String, ProductCityImageMappingDomain>> getProductImagesByCity(@RequestBody List<Integer> list ,
                                                                                          @RequestParam String cityName) throws DataNotFoundException {
        return productServices.getAllProductImagesByCity(list,cityName);
    }

    @RequestMapping(method = RequestMethod.POST, value = "uploaded-product-image-for-productId-by-city", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductCityImageMappingDomain getProductImagesByIdAndCity(@RequestParam Integer productId , @RequestParam String cityName
    ,@RequestParam(required = false) String daySlot) throws DataNotFoundException {
        return productServices.getProductImagesByCity(productId,cityName,daySlot);
    }

    @RequestMapping(method = RequestMethod.POST, value = "uploaded-product-images-by-city", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer,Map<String, ProductCityImageMappingDomain>> getProductImagesByCity(@RequestParam String cityName , @RequestParam(required = false) String daySlot) throws DataNotFoundException {
        LOG.info("Got Request To get product image mapping for City ::::: {} ", cityName);
        return productServices.getAllProductImagesByCity(cityName , daySlot);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product-image-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductImageMapping> getProductImageDetail(@RequestBody List<Integer> list) throws DataNotFoundException {
        return productServices.getProductImageDetail(list);
    }

    @RequestMapping(method = RequestMethod.GET, value = "image-category-type", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<NameValue> getProductImageCategory() throws DataNotFoundException {
        return productServices.getImageCategoryType();
    }

    @RequestMapping(method = RequestMethod.POST, value = "add/update-product-description", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    public ProductDescription updateProductDescriptionData(@RequestBody ProductDescription request) {
        return productServices.saveProductDescriptionData(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-product-description", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductDescription getSingleProductDescription(@RequestParam(value = "productId") int productId) {
        return productServices.getProductDescriptionData(productId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all-product-description", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductDescription> getProductDescriptionList(@RequestBody List<Integer> list) {
        return productServices.getProductDescriptionDataList(list);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/price/mappings/selectedRegionAndProfileWise", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<UnitProductMappingData> getUnitProductPriceForMultipleRegionAndProfiles(
            @RequestParam String unitCategory,
            @RequestParam Integer productId,
            @RequestParam(required = false) Integer dimensionId,
            @RequestParam(required = false) String pricingProfileIds,
            @RequestParam(required = false) String locationIds,
            @RequestParam(required = false) String region,
            @RequestParam(required = false) Integer companyId) {
        return productServices.getUnitProductPrice(unitCategory, productId, dimensionId, pricingProfileIds, locationIds, region, companyId);
    }

    @PostMapping(value = "get/unit-product-mappings", produces = MediaType.APPLICATION_JSON)
    public ApiResponse getUnitProductMapping(@RequestBody ProductRecipeMappingRequest request) throws MasterException {
        return productServices.getUnitProductMapping(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "product/price/profiles", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer, Map<Integer, List<ProductRecipeKey>>> getUnitProductPriceProfiles() {
        LOG.info("Request for product price profiles");
        return productServices.getUnitProductPriceProfiles();
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/nutrition", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductNutritionDetail> getProductsNutritionInfo(@RequestBody List<Integer> productIds){
        LOG.info("Request to get nutrition detail for products {}",productIds);
        return productServices.getProductsNutritionInfo(productIds);
    }

    @RequestMapping(method = RequestMethod.GET, value = "recipe-profile-inactive-check", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> recipeProfileInactivationCheck(@RequestParam Integer productId, @RequestParam Integer dimensionId, @RequestParam String profile) {
        LOG.info("Checking for Inactivating the Recipe Profile for Active units");
        return productServices.recipeProfileInactivationCheck(productId,dimensionId,profile);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/packaging")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getUnitProductPackagingSheetTemplate(@RequestBody UnitProductPackagingRequest request) {
        LOG.info("Request to download Unit Product Packaging sheet for request {}", request);
        return productServices.getUnitProductPackagingTemplate(request);

    }

    @RequestMapping(method = RequestMethod.POST, value = "product/packaging/mapping")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer,ProductPackagingMappingSheet> getUnitProductPackagingMapping(@RequestParam(required = true) Integer pricingUnitId) {
        LOG.info("Request to download Unit Product Packaging Mapping for request {}",pricingUnitId );
        return productServices.getUnitProductPackagingMapping(pricingUnitId);

    }

    @RequestMapping(method = RequestMethod.POST, value = "upload/product/packaging", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductPackagingMappingSheet> updateUnitProductPackaging(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file,
                                                                   Integer updatedBy) throws IOException {
             LOG.info("Request to Upload And Save Unit Product Mappings By : {} and file name : {}  ",updatedBy , file.getName());
             return productServices.parseAndUpdateUnitProductPackaging(file,updatedBy);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/nutrition/data", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String,Map<Integer,Map<NutritionDataEnum,BigDecimal>>> getProductsNutritionInfoBySourceTypes(@RequestBody List<String> sourceType){
        LOG.info("Request to get nutrition detail for source type {}",sourceType);
        return productServices.getProductsNutritionData(sourceType);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update/product/nutrition", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean updateProductsNutritionInfo(@RequestBody Map<Integer,Map<NutritionDataEnum,BigDecimal>> data, @RequestParam String sourceType){
        LOG.info("Request to update nutrition detail for source type {}",sourceType);
        return productServices.updateProductsNutritionInfo(data,sourceType);
    }

    @GetMapping(value = "product/price/units-products-price")
    public Map< String,Map<Integer,  Set<UnitProductPriceCategoryDomain>>> getUnitsSpecificProductsPrice() {
//        masterDataCacheService.refreshPriceCategoryWiseProductsPrice();
        LOG.info("Request to get Price Category wise Products price");
        return cache.getPriceCategoryWiseProductsPrice();
    }

    @RequestMapping(method = RequestMethod.POST, value = "product-checklist")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer createProductCheckList(HttpServletRequest request, @RequestBody List<ProductCheckList> productCheckList) throws DataUpdationException {
        LOG.info("Request to create Product Checklist");
        try {
            Integer userId = getLoggedInUser(request);
            ProductCheckListEvent checkListEvent = productServices.createCheckListEvent(userId);
            taskExecutor.execute(() -> {
                productServices.createProductCheckList(productCheckList, checkListEvent);
            });
            return checkListEvent.getEventId();
        } catch (Exception e) {
            LOG.info("Error while creating Product CheckList", e);
        }
        return null;
    }

    @RequestMapping(method = RequestMethod.GET, value = "product-checklist/get")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductCheckListEvent getProductCheckList(@RequestParam Integer eventId) {
        if (Objects.isNull(eventId)) {
            return null;
        }
        return productServices.getProductCheckListEvent(eventId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "machine-product-mapping")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<MachineProductMetaDataResponse> getMachineProductMappingData(@RequestParam Integer unitId, @RequestParam Integer brandId) {
        LOG.info("Request to Get Machine Product Mapping Data");
        return productServices.getMachineProductMappingData(unitId, brandId);
    }

    @PostMapping(value = "get/unit-product-mappings/email", produces = MediaType.APPLICATION_JSON)
    public ApiResponse getUnitProductMappingToEMail(@RequestBody ProductRecipeMappingRequest request) throws MasterException {
        Integer userId = RequestContext.getContext().getLoggedInUserId();
        taskExecutor.execute(() -> {
            RequestContext.getContext().setLoggedInUserId(userId);
            LOG.info("Sending Mail for Unit Product Mapping to Email");
            try {
                productServices.getUnitProductMappingToEmail(request);
            } catch (Exception e) {
                LOG.error("Error while getting unit product mapping to email", e);
                productServices.sendFailureEmail(null, e.getMessage());
            }
        });

        return new ApiResponse(true, "Your request is being processing in background. You'll receive an email once completed.", null);
    }

    @PostMapping(value = "product/profile/change")
    public ApiResponse changeUnitProductProfile(@RequestBody final List<UnitProductMappingData> data,
                                                @RequestParam(value = "pageType") UnitProductUpdateType type,
                                                HttpServletRequest request) throws MasterException {
        Integer userId = RequestContext.getContext().getLoggedInUserId();
        taskExecutor.execute(() -> {
            try {
                RequestContext.getContext().setLoggedInUserId(userId);
                UnitProductProfileContext context = new UnitProductProfileContext();
                context.setMappings(data);
                context.setPageType(type);
                context.setLoggedInUser(getLoggedInUser(request));
                productServices.changeUnitProductProfile(context);
            } catch (Exception e) {
                LOG.error("Error while updating unit product profile mappings", e);
                productServices.sendFailureEmail("Failure in updating unit product profile mappings", e.getMessage());
            }
        });

        return new ApiResponse(true, "Your request is being processing in background. You'll receive an email once completed.", null);
    }

    @PostMapping(value = "bulk/product-profile-change", consumes = MediaType.MULTIPART_FORM_DATA)
    public ApiResponse bulkUpdateProductProfile(@RequestParam("file") MultipartFile file,
                                                @RequestPart("requestDto") ProductRecipeBulkRequest requestDto,
                                                HttpServletRequest request) throws MasterException, DocumentException, IOException {
        Integer userId = RequestContext.getContext().getLoggedInUserId();
        taskExecutor.execute(() -> {
            try {
                RequestContext.getContext().setLoggedInUserId(userId);
                UnitProductProfileContext context = new UnitProductProfileContext();
                context.setFile(file);
                context.setExcelData(requestDto.getExcelData());
                context.setPageType(requestDto.getPageType());
                context.setLoggedInUser(getLoggedInUser(request));
                productServices.bulkUpdateUnitProductProfile(context);
            } catch (Exception e) {
                LOG.error("Error while updating unit product profile mappings", e);
                productServices.sendFailureEmail("Bulk Update Failed", e.getMessage());
            }
        });

        return new ApiResponse(true, "Your request is being processing in background. You'll receive an email once completed.", null);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-product-dimension-image", consumes = MediaType.MULTIPART_FORM_DATA)
    public ProductImageMappingDetail uploadProductDimensionImage(HttpServletRequest request,
                                                        @RequestParam(required = false, value = "mimeType") MimeType mimeType,
                                                        @RequestParam(value = "productId") int productId,
                                                        @RequestParam(value = "dimension") int dimensionCode,
                                                        @RequestParam(value = "imageCategoryType") ImageCategoryType imageCategoryType,
                                                        @RequestParam(required = false, value = "file") final MultipartFile file,
                                                        @RequestParam(value = "updatedBy") int updatedBy,
                                                        @RequestParam(required = false, value = "index") Integer index, @RequestParam(required = false, value = "Link") String link) {
        return productServices.saveProductDimensionImage(mimeType, productId, imageCategoryType, file, updatedBy, index == null ? 1 : index, link,dimensionCode);
    }

    @RequestMapping(method = RequestMethod.GET, value = "uploaded-product-dimension-images-for-productIds", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductImageMappingDetailList getProductDimensionImages(@RequestBody List<Integer> productList) throws DataNotFoundException {
        return productServices.getAllProductDimensionImages(productList);
    }

    @RequestMapping(method = RequestMethod.GET, value = "uploaded-product-dimension-image", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductImageMappingDetail getProductDimensionImages(@RequestParam(value = "productId") int productId,@RequestParam(value = "dimension") int dimensionCode) throws DataNotFoundException {
        return productServices.getProductDimensionImages(productId,dimensionCode);
    }

    @RequestMapping(method = RequestMethod.GET, value = "g-a-p-d-f-m", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ResponseEntity<ApiResponse> getAllProductDimensionImagesForMonk() throws DataNotFoundException {
        return ResponseEntity.ok(new ApiResponse(productServices.getProductDimensionImageData()));
    }

    @GetMapping(value = "get/units-short-by-brand/{brandId}")
    public ApiResponse getUnitsShortByBrandId(@PathVariable Integer brandId, @RequestParam(required = false) String unitType) {
        return productServices.getUnitsShortByBrandId(brandId, unitType);
    }

}