package com.stpl.tech.master.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.EmployeeManagementService;
import com.stpl.tech.master.core.service.model.UnitEmpData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import java.util.Map;
import java.util.Set;

import static com.stpl.tech.master.service.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.master.service.core.MasterServiceConstants.EMPLOYEE_MANAGEMENT;
import static com.stpl.tech.master.service.core.MasterServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + EMPLOYEE_MANAGEMENT)
public class EmployeeManagementResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeManagementResource.class);

    @Autowired
    private EmployeeManagementService employeeManagementService;

    @RequestMapping(method = RequestMethod.GET, value = "syncEmployeeData", produces = MediaType.APPLICATION_JSON)
    public Integer syncEmployeeData() {
        LOG.info("Calling employee sync API");
        return employeeManagementService.syncEmployeeData();
    }

    @RequestMapping(method = RequestMethod.GET, value = "syncInactiveEmployeeData", produces = MediaType.APPLICATION_JSON)
    public Integer syncInactiveEmployeeData() {
        LOG.info("Calling inactive employee sync API");
        return employeeManagementService.syncInactiveEmployeeData();
    }

    /**
     * Resume employee sync from a specific employee code
     * @param startFromEmployeeCode The employee code to start processing from
     * @return Number of employees processed
     */
    @RequestMapping(method = RequestMethod.GET, value = "syncEmployeeDataFromEmployee", produces = MediaType.APPLICATION_JSON)
    public Integer syncEmployeeDataFromEmployee(@RequestParam("startFromEmployeeCode") String startFromEmployeeCode) {
        LOG.info("Calling employee sync API from employee code: {}", startFromEmployeeCode);
        return employeeManagementService.syncEmployeeDataFromEmployee(startFromEmployeeCode);
    }

    /**
     * Utility endpoint to find the next employee to process after a given employee code
     * @param lastProcessedEmployeeCode The employee code that was last processed
     * @return The next employee code to start from, or null if not found
     */
    @RequestMapping(method = RequestMethod.GET, value = "findNextEmployeeToProcess", produces = MediaType.APPLICATION_JSON)
    public String findNextEmployeeToProcess(@RequestParam("lastProcessedEmployeeCode") String lastProcessedEmployeeCode) {
        LOG.info("Finding next employee to process after: {}", lastProcessedEmployeeCode);
        return employeeManagementService.findNextEmployeeToProcess(lastProcessedEmployeeCode);
    }

    @RequestMapping(method = RequestMethod.GET, value = "lookUpDesignationDepartment", produces = MediaType.APPLICATION_JSON)
    public Map<String, Set<String>> lookUpDesignationDepartment() {
        return employeeManagementService.lookUpDesignationDepartment();
    }

    @RequestMapping(method = RequestMethod.GET, value = "getDesignationDepartmentMapping", produces = MediaType.APPLICATION_JSON)
    public Map<String, Set<String>> getDesignationDepartmentMapping() {
        return employeeManagementService.getDesignationDepartmentMapping();
    }
    @GetMapping("/get-unit-employees")
    UnitEmpData getUnitEmployees(@QueryParam("unitId")Integer unitId) throws DataNotFoundException {
         return employeeManagementService.getUnitEmployee(unitId);
    }
}
