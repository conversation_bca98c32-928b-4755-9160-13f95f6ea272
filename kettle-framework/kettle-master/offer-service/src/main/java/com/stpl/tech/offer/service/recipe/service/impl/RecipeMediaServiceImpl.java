package com.stpl.tech.offer.service.recipe.service.impl;

import com.stpl.tech.master.recipe.model.RecipeMediaDetail;
import com.stpl.tech.master.recipe.model.RecipeStep;
import com.stpl.tech.offer.core.OfferProperties;
import com.stpl.tech.offer.service.recipe.dao.RecipeMediaManagementDao;
import com.stpl.tech.offer.service.recipe.service.RecipeMediaService;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Objects;

@Service
public class RecipeMediaServiceImpl implements RecipeMediaService {

    private static final Logger LOG = LoggerFactory.getLogger(RecipeMediaServiceImpl.class);

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private RecipeMediaManagementDao recipeMediaDao;

    @Autowired
    private OfferProperties offerProperties;


    @Override
    public Integer getRecipeStepCount(Integer recipeId) {
        List<RecipeMediaDetail> details = recipeMediaDao.findByRecipeId(recipeId);
        if (Objects.nonNull(details) && !details.isEmpty()) {
            return details.get(AppConstants.ZERO).getRecipeSteps().size();
        }
        return AppConstants.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void downloadRecipeMedia(HttpServletResponse response, Integer recipeId, Integer stepCount) throws IOException {
        String s3RecipeMediaBucket = offerProperties.getS3RecipeMediaBucket();
        List<RecipeMediaDetail> details = recipeMediaDao.findByRecipeId(recipeId);
        if (Objects.nonNull(details) && !details.isEmpty()) {
            RecipeStep recipeStep = details.get(AppConstants.ZERO).getRecipeSteps().get(stepCount);
            if (Objects.nonNull(recipeStep)) {
                FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, recipeStep.getS3Key(), null);
                File file = fileArchiveService.getFileFromS3(offerProperties.getBasePath() + File.separator + "s3", fileDetail);
                setFileToResponse(response, recipeStep, file);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void downloadRecipeMedia(HttpServletResponse response, RecipeStep recipeStep) throws IOException {
        String s3RecipeMediaBucket = offerProperties.getS3RecipeMediaBucket();
        FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, recipeStep.getS3Key(), null);
        File file = fileArchiveService.getFileFromS3(offerProperties.getBasePath() + File.separator + "s3", fileDetail);
        setFileToResponse(response, recipeStep, file);
    }

    private void setFileToResponse(HttpServletResponse response, RecipeStep recipeStep, File file) throws IOException {
        if (Objects.nonNull(file)) {
            response.setContentType(recipeStep.getMediaType());
            response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
            byte[] bytesArray = new byte[(int) file.length()];
            response.setContentLength(bytesArray.length);
            try {
                OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = new FileInputStream(file);
                int counter = 0;
                while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                    outputStream.write(bytesArray, 0, counter);
                    outputStream.flush();
                }
                outputStream.close();
                inputStream.close();
            } catch (IOException e) {
                LOG.error("Encountered error while writing file to response stream", e);
                throw e;
            } finally {
                response.getOutputStream().flush();
                file.delete(); // delete the temporary file created after completing request
            }
        }
    }
}
