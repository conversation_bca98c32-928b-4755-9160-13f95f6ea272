package com.stpl.tech.offer.controller;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.domain.model.CouponCloneRequest;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.offer.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.offer.core.MasterServiceConstants.EXTERNAL_OFFER_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.offer.core.MasterServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + EXTERNAL_OFFER_MANAGEMENT_ROOT_CONTEXT)
public class ExternalOfferManagementResources {

	@Autowired
	private OfferManagementService offerService;

	@RequestMapping(method = RequestMethod.POST, value = "coupon/clone", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CouponCloneResponse generateCoupon(@RequestBody final CouponCloneRequest coupon)
			throws DataUpdationException {
		return offerService.generateCoupon(coupon);
	}

}
