package com.stpl.tech.offer.controller;

import com.stpl.tech.master.core.external.price.service.ProductPriceService;
import com.stpl.tech.master.data.model.UnitProductPricingBulkUpdateEvent;
import com.stpl.tech.master.domain.model.UnitProductPriceBulkRequest;
import com.stpl.tech.master.domain.model.UnitProductPricingDetail;
import com.stpl.tech.offer.core.OfferProperties;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.offer.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.offer.core.MasterServiceConstants.PRICE_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.offer.core.MasterServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PRICE_MANAGEMENT_ROOT_CONTEXT)
public class ProductPriceManagementResources {

    private static final Logger LOG = LoggerFactory.getLogger(ProductPriceManagementResources.class);

    @Autowired
    private ProductPriceService productServices;

    @Autowired
    private OfferProperties offerProperties;

    @RequestMapping(method = RequestMethod.POST, value = "get-unit-product-price-sheet")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getUnitProductPriceSheet(@RequestBody UnitProductPriceBulkRequest request) {
        LOG.info("Request to download Unit Product Price sheet for request {}", request);
        return productServices.getUnitProductPriceSheet(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "product/price/bulk-update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitProductPricingBulkUpdateEvent updateBulkUnitProductPrice(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file, Integer updatedBy) {
        Date startTime = AppUtils.getCurrentTimestamp();
        LOG.info("Request to Upload sheet and Update Bulk Unit Product Prices starting at time {}", startTime);
        UnitProductPricingBulkUpdateEvent updateEvent = new UnitProductPricingBulkUpdateEvent();
        try {
            Map<BigDecimal, List<UnitProductPricingDetail>> priceToUnitProductMap = productServices.parseAndCreateBulkUnitProductPriceUpdateEvent(file, updatedBy, updateEvent);
            String s3FilePath = productServices.saveBulkProductPriceSheet(offerProperties.getS3PriceSheetBucket(), file);
            updateEvent.setSheetPath(s3FilePath);
            LOG.info("Update Bulk Unit Product Price event {}", updateEvent);
            for (Map.Entry<BigDecimal, List<UnitProductPricingDetail>> entry : priceToUnitProductMap.entrySet()) {
                List<UnitProductPricingDetail> pricingDetail = entry.getValue();
                LOG.info("Bulk Updating price {} for product count {}", entry.getKey(), pricingDetail.size());
                try {
                    productServices.bulkUpdateUnitProductPrice(entry.getKey(), pricingDetail, updatedBy, updateEvent);
                    if (Objects.isNull(updateEvent.getTotalRecordsUpdatedSuccessfully())) {
                        updateEvent.setTotalRecordsUpdatedSuccessfully(pricingDetail.size());
                    } else {
                        updateEvent.setTotalRecordsUpdatedSuccessfully(updateEvent.getTotalRecordsUpdatedSuccessfully() + pricingDetail.size());
                    }
                } catch (Exception e) {
                    LOG.error("Error in Bulk updating for price {}", entry.getKey(), e);
                    if (Objects.isNull(updateEvent.getTotalFailureRecords())) {
                        updateEvent.setTotalFailureRecords(pricingDetail.size());
                    } else {
                        updateEvent.setTotalFailureRecords(updateEvent.getTotalFailureRecords() + pricingDetail.size());
                    }
                }
            }
        } catch (ExcelParsingException e1) {
            throw e1;
        } catch (Exception e) {
            LOG.error("Error in Bulk updating prices for event {}", updateEvent, e);
        }
        try {
            productServices.updateBulkUpdateUnitProductPriceEvent(updateEvent);
        } catch (Exception e) {
            LOG.error("Error while updating bulk price update event {}", updateEvent, e);
        }
        Date endTime = AppUtils.getCurrentTimestamp();
        productServices.sendProductProfileBulkUpdateMail(updateEvent,offerProperties.getBasePath(),offerProperties.getEnvironmentType());
        LOG.info("Bulk Updated Product Price completed at time {} and took Total Time {} secs", endTime, ((double) (endTime.getTime() - startTime.getTime())) / 1000);
        return updateEvent;
    }
}
