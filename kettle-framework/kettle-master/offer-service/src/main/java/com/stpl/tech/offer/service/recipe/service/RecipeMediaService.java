package com.stpl.tech.offer.service.recipe.service;

import com.stpl.tech.master.recipe.model.RecipeStep;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface RecipeMediaService {

    void downloadRecipeMedia(HttpServletResponse response, Integer recipeId, Integer stepCount) throws IOException;

    Integer getRecipeStepCount(Integer recipeId);

    void downloadRecipeMedia(HttpServletResponse response, RecipeStep recipeStep) throws IOException;
}