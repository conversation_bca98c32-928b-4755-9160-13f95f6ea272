package com.stpl.tech.offer.controller;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.banner.service.BannerService;
import com.stpl.tech.master.data.model.BannerDetailData;
import com.stpl.tech.master.domain.model.BannerActionTypeResponse;
import com.stpl.tech.master.domain.model.BannerDetail;
import com.stpl.tech.master.domain.model.BannerDetailListWrapper;
import com.stpl.tech.master.domain.model.BannerType;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.SectionTypeResponse;
import com.stpl.tech.offer.core.OfferProperties;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.offer.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.offer.core.MasterServiceConstants.BANNER_ROOT_CONTEXT;
import static com.stpl.tech.offer.core.MasterServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + BANNER_ROOT_CONTEXT)
public class BannerManagementResources {

    private static final Logger LOG = LoggerFactory.getLogger(BannerManagementResources.class);

    @Autowired
    private BannerService bannerService;

    @Autowired
    private FileArchiveService fileArchiveService;
    @Autowired
    private OfferProperties props;


    @RequestMapping(method = RequestMethod.GET, value = "banner-list", produces = MediaType.APPLICATION_JSON)
    public BannerDetailListWrapper getActivatedBannerList(@RequestParam String date) {
        LOG.info("Request to get list of activated banners for date " + date);
        return bannerService.getActiveBannerList(date);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-banner", consumes = MediaType.MULTIPART_FORM_DATA)
    public IdName saveBanner(HttpServletRequest request,
                             @RequestParam(value = "mimeType") MimeType mimeType,
                             @RequestParam(value = "file") final MultipartFile file,
                             @RequestParam(value = "name") String name) {
        LOG.info("Request to upload  banner " + name);
//                                             http://d1nqp92n3q8zl7.cloudfront.net/product_image/10_special_1.jpg
        String baseDir = "offer-service/app_offer_image";
        name=name.replaceAll(" ", "_").toLowerCase();
        FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3OfferBucket(), baseDir, name, file, true);
        if (s3File != null) {
            return new IdName(props.getOfferHostUrl() + name);
        }

        return null;
    }


    @RequestMapping(method = RequestMethod.POST, value = "save-banner", produces = MediaType.APPLICATION_JSON)
    public BannerDetailData saveBanner(@RequestBody BannerDetail bannerDetail) throws DataUpdationException {
        LOG.info("Request to save  banner " + bannerDetail);
        return bannerService.saveBanner(bannerDetail);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-banner", produces = MediaType.APPLICATION_JSON)
    public List<BannerDetail> getBanner() {
        LOG.info("Request to get  banners ");
        return bannerService.getBanner();
    }


    @RequestMapping(method = RequestMethod.POST, value = "update-banner-status", produces = MediaType.APPLICATION_JSON)
    public Boolean updateBannerStatus(@RequestBody IdName idName) throws DataUpdationException {
        LOG.info("Request to update status for   banner " + idName.getId() + "status" + idName.getName());
        return bannerService.updateBannerStatus(idName.getId(), idName.getName());
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-banner-type", produces = MediaType.APPLICATION_JSON)
    public Object[] getBannerType() {
        LOG.info("Request to get  banners type ");
        return BannerType.values();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-banner-action-type", produces = MediaType.APPLICATION_JSON)
    public List<BannerActionTypeResponse> getBannerActionType() {
        LOG.info("Request to get  banners action type ");
        return bannerService.getBannerActionType();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-section-type", produces = MediaType.APPLICATION_JSON)
    public List<SectionTypeResponse> getSectionType() {
        LOG.info("Request to get  section type ");
        return bannerService.getSectionType();
    }
}
