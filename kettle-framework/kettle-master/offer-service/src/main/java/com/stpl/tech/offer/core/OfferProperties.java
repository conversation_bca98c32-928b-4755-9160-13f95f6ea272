/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.offer.core;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;


@Service
public class OfferProperties {

    @Autowired
    private Environment env;

    public EnvType getEnvironmentType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public String getBasePath() {
        return env.getProperty("server.base.dir");
    }

    public String getS3Bucket() {
        return env.getProperty("amazon.s3.bucket", "chaayosdevtest");
    }

    public String getS3OfferBucket() {
        return env.getProperty("amazon.s3.offer.bucket", "dev.offer.image");
    }

	public String getOfferHostUrl() {
        return env.getProperty("offer.image.host.url", "https://d3rcyooxiaudps.cloudfront.net/");
	}

    public String getCampaignHostUrl() {
        return env.getProperty("campaign.image.host.url", "https://d2aqq5tjluujuh.cloudfront.net/");
    }

	public String getS3PriceSheetBucket() {
        return env.getProperty("amazon.s3.product.price.bucket","dev.product.price");
    }

    public String getS3RecipeMediaBucket(){
        return env.getProperty("amazon.s3.recipe.media.bucket", "dev.recipe.image");
    }
}
