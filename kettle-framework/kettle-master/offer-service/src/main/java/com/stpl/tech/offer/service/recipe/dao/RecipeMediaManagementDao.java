package com.stpl.tech.offer.service.recipe.dao;

import com.stpl.tech.master.recipe.model.RecipeMediaDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RecipeMediaManagementDao extends MongoRepository<RecipeMediaDetail, String> {
    List<RecipeMediaDetail> findByRecipeId(int recipeId);
}