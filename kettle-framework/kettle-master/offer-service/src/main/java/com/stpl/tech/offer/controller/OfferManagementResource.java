/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.offer.controller;

import com.stpl.tech.kettle.report.metadata.model.NameValue;
import com.stpl.tech.master.CampaignImageDetail;
import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.FrequencyOfferType;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.data.model.BannerMetadata;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.OfferDescriptionMetadata;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.AppOfferDetail;
import com.stpl.tech.master.domain.model.BannerMetadataRequest;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.CouponBulkUpdateDomain;
import com.stpl.tech.master.domain.model.CouponCreateRequest;
import com.stpl.tech.master.domain.model.CouponCreateResponse;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.DeliveryCouponStatus;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.OfferDayDto;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferImageDetail;
import com.stpl.tech.master.domain.model.OfferTimeDto;
import com.stpl.tech.master.domain.model.OfferTypeFlag;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.ProductGroup;
import com.stpl.tech.offer.core.OfferProperties;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.offer.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.offer.core.MasterServiceConstants.OFFER_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.offer.core.MasterServiceConstants.SEPARATOR;

@CrossOrigin(origins = "*", allowedHeaders = "*")
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + OFFER_MANAGEMENT_ROOT_CONTEXT) // 'v1/offer-management'
public class OfferManagementResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(OfferManagementResource.class);

    private static final int MAX_COUPON_IN_A_BATCH = 50;

    @Autowired
    private OfferManagementService offerService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private OfferProperties offerProperties;

    /**
     * Regular offers shown on POS screen. These are MASS offers.
     *
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "coupon/offers", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CouponDetail> getRegularCoupons(HttpServletRequest request) throws DataNotFoundException {
        LOG.info("Request to get MASS offer Coupon Codes");
        Integer loggedInUnit = getLoggedInUnit(request);
        Integer loggedInBrandId = getBrandIdFromHeaders(request);
        return offerService.getRegularCoupons(loggedInUnit, loggedInBrandId);
    }

    /**
     * Get All offers in the system
     *
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "offers", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OfferDetail> getAllOffers() throws DataNotFoundException {
        LOG.info("Request for getting all offers");
        return offerService.getAllOffers(true);
    }

    /**
     * Get All offers in the system
     *
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "offer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OfferDetail getOffers(@RequestBody final int offerId) throws DataNotFoundException {
        LOG.info("Request for getting offer with id " + offerId);
        return offerService.getOffer(offerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "offers/search", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OfferDetail> getOffer(@RequestBody final String key) throws DataNotFoundException {
        LOG.info("Request for getting offer by key");
        return offerService.getOffer(key, true);
    }


    @RequestMapping(method = RequestMethod.POST, value = "offers/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OfferDetail addOffer(@RequestBody final OfferDetail offer) throws DataNotFoundException, OfferValidationException {
        LOG.info("Request to Add offer");
        return offerService.addOffer(offer);
    }

    @RequestMapping(method = RequestMethod.POST, value = "offers/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OfferDetail updateOffer(@RequestBody final OfferDetail offer) throws DataNotFoundException, OfferValidationException {
        LOG.info("Request for updating offer {}", offer.getId());
        return offerService.updateOffer(offer);
    }

    /**
     * Coupons for a particular offer
     *
     * @param offerId
     * @return
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "offer/coupons", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CouponDetail> getOfferCoupons(@RequestBody final int offerId,
                                              @RequestParam(required = false) boolean applyLimit) throws DataNotFoundException {
        LOG.info("Request for getting all Coupons for offer productId {}", offerId);
        return offerService.getOfferCoupons(offerId, applyLimit);
    }

    @RequestMapping(method = RequestMethod.POST, value = "marketing-partner", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> getMarketingPartners() throws DataNotFoundException {
        LOG.info("Request for getting all Marketing partners");
        return offerService.getMarketingPartners();
    }

    @RequestMapping(method = RequestMethod.POST, value = "marketing-partner/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public IdCodeName addMarketingPartners(@RequestBody final IdCodeName marketingPartner)
            throws DataNotFoundException {
        LOG.info("Request for Adding Marketing Partner with name:", marketingPartner.getName());
        return offerService.addMarketingPartners(marketingPartner);
    }

    @RequestMapping(method = RequestMethod.POST, value = "marketing-partner/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public IdCodeName updateMarketingPartners(@RequestBody final IdCodeName marketingPartner)
            throws DataNotFoundException {
        LOG.info("Request for updating marketing Partner with Id {}", marketingPartner.getId());
        return offerService.updateMarketingPartners(marketingPartner);
    }

    @RequestMapping(method = RequestMethod.POST, value = "marketing-partner/activate", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean activateMarketingPartners(@RequestBody final int marketingPartnerId) throws DataNotFoundException {
        LOG.info("Request for activating marketing Partner with Id {}", marketingPartnerId);
        return offerService.changeMarketingPartnerStatus(marketingPartnerId, AppConstants.ACTIVE);
    }

    @RequestMapping(method = RequestMethod.POST, value = "marketing-partner/deactivate", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean deactivateMarketingPartners(@RequestBody final int marketingPartnerId) throws DataNotFoundException {
        LOG.info("Request for deactivating marketing Partner with Id {}", marketingPartnerId);
        return offerService.changeMarketingPartnerStatus(marketingPartnerId, AppConstants.IN_ACTIVE);
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/search", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CouponDetail searchCoupon(@RequestBody final String couponCode,
                                     @RequestParam(required = false) boolean applyLimit) throws DataNotFoundException {
        LOG.info("Request for getting coupon details for code {}", couponCode);
        return offerService.searchCoupon(couponCode, applyLimit);
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CouponDetail addCoupon(@RequestBody final CouponDetail coupon) throws DataNotFoundException {
        LOG.info("Request for Adding Coupon with code: {}", coupon.getCode());
        return offerService.addCoupon(coupon);
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/generate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CouponCreateResponse generateCoupon(@RequestBody final CouponCreateRequest coupon)
            throws DataNotFoundException, IOException, DataUpdationException {
        LOG.info("Request for Generating Coupon with code: {}", coupon);
        CouponCreateResponse response = new CouponCreateResponse();
        List<String> couponCodes = offerService.generateUniqueCoupons(coupon.getSampleCode(), coupon.getCouponPrefix(),
                1);
        String generatedCoupon = couponCodes.get(0);
        response.setCouponCode(generatedCoupon);
        CouponDetail detail = offerService.searchCoupon(generatedCoupon, false);
        if (coupon.getContactNumber() != null) {
            CouponMapping mapping = new CouponMapping();
            mapping.setDataType(String.class.getCanonicalName());
            mapping.setGroup(1);
            mapping.setMinValue("1");
            mapping.setStatus("ACTIVE");
            mapping.setType(CouponMappingType.CONTACT_NUMBER.name());
            mapping.setValue(coupon.getContactNumber());
            detail.getCouponMappingList().add(mapping);
        }
        if (coupon.getCompressUrl() != null && coupon.getCompressUrl()) {
            if (coupon.getUrl() != null) {
                String url = String.format(
                        coupon.getUrl() + (!coupon.getUrl().contains("?") ? "?couponCode=%s" : "&couponCode=%s"),
                        generatedCoupon);
                ShortUrlData urlData = SolsInfiniWebServiceClient.getOTPClient().getShortUrl(url);
                response.setCompressedUrl(urlData.getUrl());
            }
        }

        if (coupon.getStartsFromToday() != null && coupon.getStartsFromToday()) {
            detail.setStartDate(AppUtils.getBusinessDate());
            detail.setEndDate(AppUtils.getDayBeforeOrAfterDay(detail.getStartDate(), coupon.getValidDays()));
        }else if(coupon.getStartDate() != null) {
        	detail.setStartDate(coupon.getStartDate());
        	detail.setEndDate(AppUtils.getDayBeforeOrAfterDay(detail.getStartDate(), coupon.getValidDays()));
        }
        offerService.updateCoupon(detail);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateCoupon(@RequestBody final CouponDetail coupon) throws DataNotFoundException {
        LOG.info("Request for updating coupon with Id {}", coupon.getId());
        offerService.updateCoupon(coupon);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/update/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateCoupons(@RequestBody final List<CouponDetail> coupons) throws DataNotFoundException {
        LOG.info("Request for updating coupons", JSONSerializer.toJSON(coupons));
        return offerService.updateCoupons(coupons);
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/update/mappings", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateCouponMappings(@RequestBody final List<CouponDetail> coupons) throws DataNotFoundException {
        LOG.info("Request for updating coupons", JSONSerializer.toJSON(coupons));
        return offerService.updateCouponMappings(coupons);
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/mapping/activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean activateCouponMapping(@RequestBody final int couponMappingId) throws DataNotFoundException {
        LOG.info("Request for Activating oupon mapping with Id {}", couponMappingId);
        return offerService.updateCouponMapping(couponMappingId, AppConstants.ACTIVE);
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/mapping/deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean deactivateCouponMapping(@RequestBody final int couponMappingId) throws DataNotFoundException {
        LOG.info("Request for De-Activating coupon mapping with Id {}", couponMappingId);
        return offerService.updateCouponMapping(couponMappingId, AppConstants.IN_ACTIVE);
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/auto", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<String> generateUniqueCoupons(@RequestParam final String couponCode,
                                              @RequestParam final String couponPrefix, @RequestParam final int replicateCount)
            throws DataNotFoundException, DataUpdationException {
        LOG.info("Request to replicate coupon code: {} to {} times", couponCode, replicateCount);
        int batchCount = replicateCount / MAX_COUPON_IN_A_BATCH;
        int residual = replicateCount % MAX_COUPON_IN_A_BATCH;
        List<String> codes = offerService.getUniqueCoupons(couponCode, couponPrefix, replicateCount);
        CouponDetail modalCoupon = offerService.getModalCoupon(couponCode);
        List<String> list = new ArrayList<String>();
        for (int i = 0; i < batchCount; i++) {
            LOG.info("Generatig coupons for Start Index {} of End Index {} ", i * MAX_COUPON_IN_A_BATCH,
                    (i + 1) * MAX_COUPON_IN_A_BATCH);
            List<String> current = null;
            try {
                current = offerService.generateCopyCoupons(modalCoupon,
                        codes.subList(i * MAX_COUPON_IN_A_BATCH, (i + 1) * MAX_COUPON_IN_A_BATCH));
            } catch (Exception e) {
                LOG.error("Error in generating batch : " + i + " of coupons", e);
            }
            if (current != null) {
                list.addAll(current);
            }
        }
        if (residual > 0) {
            LOG.info("Generating residual coupons for Start Index {} of End Index {} ", replicateCount - residual,
                    replicateCount);
            List<String> current = null;
            try {
                current = offerService.generateCopyCoupons(modalCoupon,
                        codes.subList(replicateCount - residual, replicateCount));
            } catch (Exception e) {
                LOG.error("Error in generating residual batch of size : " + residual + " of coupons", e);
            }
            if (current != null) {
                list.addAll(current);
            }
        }
        return list;
    }

    public static void main(String[] args) {
        int replicateCount = 1680;
        int batchCount = replicateCount / MAX_COUPON_IN_A_BATCH;
        int residual = replicateCount % MAX_COUPON_IN_A_BATCH;
        for (int i = 0; i < batchCount; i++) {
            LOG.info("Start Index {} of End Index {} ", i * MAX_COUPON_IN_A_BATCH, (i + 1) * MAX_COUPON_IN_A_BATCH);
        }
        LOG.info("Residual Start Index {} of End Index {} ", replicateCount - residual, replicateCount);

    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/availablity", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean checkCodeAvailiblity(@RequestBody final String couponCode) throws DataNotFoundException {
        LOG.info("Request to Check couponCode Availablity for code {}", couponCode);
        return offerService.checkCodeAvailiblity(couponCode);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-modes", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Collection<PaymentMode> getAllPaymentModes() throws DataNotFoundException {
        LOG.info("Request to get all Payment Modes");
        return masterDataCache.getAllPaymentMode();
    }

    @RequestMapping(method = RequestMethod.POST, value = "offer-category", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, List<OfferCategoryType>> getOfferTypes() throws DataNotFoundException {
        return offerService.getOfferCategories();
    }

    @RequestMapping(method = RequestMethod.GET, value = "offerAccountsCategories", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdName> offerAccountsCategories() throws DataNotFoundException {
        return offerService.getOfferAccountsCategories();
    }

    @RequestMapping(method = RequestMethod.GET, value = "app-offer-types", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OfferTypeFlag> allAppOfferType() {
        return offerService.getOfferTypes();
    }

    @RequestMapping(method = RequestMethod.GET, value = "offer-action-type", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<NameValue> getOfferActionTypes() throws DataNotFoundException {
        return offerService.getOfferActionTypes();
    }

    @Deprecated
    @RequestMapping(method = RequestMethod.GET, value = "getAllAppOffers", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<AppOfferDetail> getActInactOffers() {
        return offerService.getActInactOffers();
    }

    @RequestMapping(method = RequestMethod.GET, value = "getAppOffersByPartnerId", produces = MediaType.APPLICATION_JSON)
    public List<AppOfferDetail> getAppOffersByPartnerId(@RequestParam Integer partnerId) {
        return offerService.getAppOffersByPartnerId(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "updateStatusForAppOffers", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean setStatusForAppOffer(@RequestBody IdCodeName idCodeName) {
        return offerService.setStatusForAppOffer(idCodeName);
    }

    @RequestMapping(method = RequestMethod.POST, value = "updateOrderingForAppOffers", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean setOrderingForAppOffer(@RequestBody List<IdIndex> list) {
        return offerService.setOrderingForAppOffer(list);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-offer-Image", produces = MediaType.APPLICATION_JSON,
            consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OfferImageDetail uploadOfferImage(HttpServletRequest request,
                                             @RequestParam(value = "mimeType") MimeType mimeType,
                                             @RequestParam(value = "couponCode") String couponCode,
                                             @RequestParam(value = "imageType") String imageType,
                                             @RequestParam(value = "file") final MultipartFile file) {

        return offerService.saveOfferImage(mimeType, couponCode, imageType, file, offerProperties.getS3OfferBucket(), offerProperties.getOfferHostUrl());
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-new-app-offer", consumes = MediaType.APPLICATION_JSON,
            produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AppOfferDetail addAppOffer(@RequestBody final AppOfferDetail offer) throws DataNotFoundException {
        LOG.info("Request to Add New App offer");
        return offerService.addNewAppOffer(offer);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-app-offer", consumes = MediaType.APPLICATION_JSON,
            produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AppOfferDetail updateAppOffer(@RequestBody final AppOfferDetail offer) throws DataNotFoundException {
        LOG.info("Request to Update App offer {appOfferId} " + offer.getAppOfferId());
        return offerService.updateAppOffer(offer);
    }

    @Scheduled(cron = "0 15 05 * * *", zone = "GMT+05:30")
    public void archiveOffers() {
        offerService.markExpiredOffersAsArchived();
    }


    @RequestMapping(method = RequestMethod.POST, value = "save-banner-metadata", consumes = MediaType.APPLICATION_JSON,
            produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<BannerMetadata> saveBannerMetadata(@RequestBody List<BannerMetadataRequest> request){
        LOG.info("Request to save banner metadata " + request);
        return offerService.saveBannerMetadata(request);

    }


    @RequestMapping(method = RequestMethod.GET, value = "get-banner-metadata", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<BannerMetadataRequest> getBannerMetadata(){
        LOG.info("Request to get banner metadata ");
        return offerService.getBannerMetadata();

    }


    @RequestMapping(method = RequestMethod.GET, value = "get-active-app-menu-category", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductGroup> getCategoryFromMenuSequence(@RequestParam String menuApp) throws DataNotFoundException {
        LOG.info("Request to get menu sequence category for:"+menuApp);
        return offerService.getCategoryFromMenuSequence(MenuApp.valueOf(menuApp));

    }

    @RequestMapping(method = RequestMethod.POST, value = "add-new-campaign", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CampaignDetail addNewCampaign(@RequestBody CampaignDetail campaignDetail) throws DataUpdationException, NoSuchAlgorithmException, UnsupportedEncodingException {
        LOG.info("Request to add Campaign Detail: {}", campaignDetail);
        CampaignDetail response = null;
        try {
            response = offerService.addCampaignDetailData(campaignDetail);
            LOG.info("Saved Campaign detail: {}", response);
        } catch (Exception e) {
            LOG.error("Error while adding campaign: ", e);
            throw e;
        }
        return response;
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all-campaigns", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CampaignDetail> getAllCampaigns() throws DataNotFoundException {
        LOG.info("Request for getting all campaigns");
        return offerService.getAllCampaigns();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-campaign-by-id", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CampaignDetail getCampaignById(@RequestParam Integer campaignId) throws DataNotFoundException {
        LOG.info("Request for getting Campaign by id: {}", campaignId);
        return offerService.getCampaignById(campaignId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-campaign-by-token", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CampaignDetailResponse getCampaignByTokenAndStatus(@RequestParam String campaignToken, @RequestParam String status) throws DataNotFoundException {
        LOG.info("Request for getting Campaign by token: {} and status: {}", campaignToken, status);
        return offerService.getCampaignByTokenAndStatus(campaignToken, status);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-campaigns-by-campaign-desc", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CampaignDetail> getCampaignsCampaignDesc(@RequestParam String campaignDesc, @RequestParam Boolean fetchAll) throws DataNotFoundException {
        LOG.info("Request for getting Campaigns by name or description: {}", campaignDesc);
        return offerService.getCampaignsByCampaignDesc(campaignDesc, fetchAll);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-campaign-status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CampaignDetail updateCampaignStatus(@RequestBody CampaignDetail campaignDetail) throws DataUpdationException {
        LOG.info("Request to update Campaign Status: {}", campaignDetail);
        CampaignDetail response = null;
        try {
            response = offerService.updateCampaignStatus(campaignDetail);
            LOG.info("Saved Campaign detail: {}", campaignDetail);
        } catch (Exception e) {
            LOG.error("Error while updating campaign status:", e);
            throw e;
        }
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-campaign", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CampaignDetail updateCampaign(@RequestBody CampaignDetail campaignDetail) throws DataUpdationException, IOException {
        LOG.info("Request to update Campaign: {}", campaignDetail);
        CampaignDetail response = null;
        try {
            response = offerService.updateCampaign(campaignDetail);
            LOG.info("Saved Campaign detail: {}", response);
        } catch (Exception e) {
            LOG.error("Error while updating campaign status:", e);
            throw e;
        }
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-campaign-short-url", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CampaignDetail updateCampaignShortUrl(@RequestBody CampaignDetail campaignDetail) throws DataUpdationException {
        LOG.info("Request to update Campaign Short Url: {}", campaignDetail);
        CampaignDetail response = null;
        try {
            response = offerService.updateCampaignShortUrl(campaignDetail);
            LOG.info("Saved Campaign detail: {}", campaignDetail);
        } catch (Exception e) {
            LOG.error("Error while updating campaign short url: {}", campaignDetail.getShortUrl(),e);
            throw e;
        }
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-campaign-image",
            produces = MediaType.APPLICATION_JSON, consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CampaignImageDetail uploadCampaignImage(HttpServletRequest request,
                                                   @RequestParam(value = "file") final MultipartFile file) {
        LOG.info("Request to upload Campaign Image");
        return offerService.saveCampaignImage(file, offerProperties.getS3OfferBucket(), offerProperties.getCampaignHostUrl());
    }

    @RequestMapping(method = RequestMethod.GET, value = "create-campaign-short-url", produces = MediaType.TEXT_PLAIN)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String createCampaignShortUrl(@RequestParam String longUrl) throws DataNotFoundException, IOException {
        LOG.info("Request for creating Short URL: {}", longUrl);
        return offerService.createCampaignShortUrl(longUrl);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-delivery-coupon-sheet", consumes = MediaType.MULTIPART_FORM_DATA, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<String> uploadDeliveryCouponSheet(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file) throws Exception {
        LOG.info("Request to upload Delivery Coupon Sheet");
        List<String> couponWithErrors = new ArrayList<>();
        List<DeliveryCouponDetailData> couponDetailDataList = new ArrayList<>();
        try {
            Workbook workbook;
            if (file.getName().endsWith("xls")) {
                workbook = new HSSFWorkbook(file.getInputStream());
            } else {
                workbook = new XSSFWorkbook(file.getInputStream());
            }
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<DeliveryCouponDetailData> entityList = parser.createEntity(workbook.getSheetAt(0), DeliveryCouponDetailData.class, errors::add);
            if (errors.isEmpty()) {
                for (DeliveryCouponDetailData deliveryCouponDetailData : entityList) {
                    if (deliveryCouponDetailData != null && deliveryCouponDetailData.getMasterCoupon() != null) {
                        try {
                            deliveryCouponDetailData.setIsExhausted(AppConstants.NO);
                            deliveryCouponDetailData.setNoOfAllocations(AppConstants.ZERO);
                            deliveryCouponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.AVAILABLE.name());
                            deliveryCouponDetailData.setCreationTime(AppUtils.getCurrentTimestamp());
                            if (Objects.isNull(deliveryCouponDetailData.getMaxUsage()) || deliveryCouponDetailData.getMaxUsage() < 1) {
                                deliveryCouponDetailData.setMaxUsage(AppConstants.ONE);
                            }
                            if (Objects.isNull(deliveryCouponDetailData.getBrandId()) || deliveryCouponDetailData.getBrandId() < 1) {
                                couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                            } else if (Objects.isNull(deliveryCouponDetailData.getChannelPartnerId()) || deliveryCouponDetailData.getChannelPartnerId() < 1) {
                                couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                            } else if (Objects.isNull(deliveryCouponDetailData.getCouponStrategy()) || deliveryCouponDetailData.getCouponStrategy().trim().isEmpty()) {
                                couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                            } else if (deliveryCouponDetailData.getMasterCoupon().trim().isEmpty()) {
                                couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                            } else if (Objects.isNull(deliveryCouponDetailData.getCouponCode()) || deliveryCouponDetailData.getCouponCode().trim().isEmpty()) {
                                couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                            } else if (Objects.isNull(deliveryCouponDetailData.getStartDate()) || Objects.isNull(deliveryCouponDetailData.getEndDate()) || deliveryCouponDetailData.getStartDate().equals(DateUtil.getJavaDate(00.00))
                                    || deliveryCouponDetailData.getEndDate().equals(DateUtil.getJavaDate(00.00)) || deliveryCouponDetailData.getStartDate().after(deliveryCouponDetailData.getEndDate())
                                    || deliveryCouponDetailData.getStartDate().equals(deliveryCouponDetailData.getEndDate())) {
                                couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                            } else if (Objects.isNull(deliveryCouponDetailData.getValidityInDays()) || deliveryCouponDetailData.getValidityInDays() < 1) {
                                couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                            } else if (Objects.isNull(deliveryCouponDetailData.getMaxNoOfDistributions()) || deliveryCouponDetailData.getMaxNoOfDistributions() < 1) {
                                couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                            } else {
                                couponDetailDataList.add(deliveryCouponDetailData);
                            }
                        } catch (Exception e) {
                            LOG.error("Error in adding Delivery Coupon Detail", e);
                            couponWithErrors.add(deliveryCouponDetailData.getCouponCode());
                        }
                    }
                }
                offerService.addDeliveryCoupons(couponDetailDataList);
            } else {
                LOG.info("Error parsing Workbook for Delivery Coupon Sheet, total errors: {}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage() + '\n'));
                LOG.info("{}", sb.toString());
                throw new ExcelParsingException(sb.toString());
            }
            workbook.close();
        } catch (ExcelParsingException e1) {
            throw e1;
        } catch (Exception e) {
            LOG.error("Error while uploading Delivery coupon sheet", e);
            throw new Exception("Error in uploading Delivery coupons");
        }
        return couponWithErrors;
    }

    @RequestMapping(method = RequestMethod.GET, value = "delivery-coupon-sheet-template")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getDeliveryCouponSheet() {
        LOG.info("Request to get Delivery Coupon sheet");
        return offerService.getDeliveryCouponSheet();
    }

    @RequestMapping(method = RequestMethod.GET, value = "validate-delivery-coupon")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Long validateDeliveryCoupon(@RequestParam String masterCoupon) {
        LOG.info("Request to validate Delivery Coupon for {}", masterCoupon);
        return offerService.validateDeliveryCoupon(masterCoupon);
    }

    @RequestMapping(method = RequestMethod.GET, value = "campaign-list-by-valid-date", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CampaignDetail> getCampaignListByValidDate() {
        LOG.info("Request to get Active Campaigns from according to valid end date");
        return offerService.getActiveCampaignListByValidDate();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-auto-applicable-offer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CouponDetail getAutoApplicableOfferForUnit(@RequestParam Integer unitId) {
        LOG.info("Request to get Active Campaigns from according to valid end date");
        return offerService.getAutoApplicableOfferForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-frequency-strategy", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Object[] getFrequencyOfferTypes(){
        return FrequencyOfferType.values();
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/bulk/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateBulkCoupon(@RequestBody final CouponBulkUpdateDomain couponDomain) throws DataNotFoundException {
        LOG.info("Request for updating coupon with offer Id {}", couponDomain.getOfferId());
        return offerService.updateBulkCoupon(couponDomain);
    }

    @GetMapping("offer/day-schedule")
    public ResponseEntity<ApiResponse> getOfferDayScheduleDetails(@RequestParam final Integer offerId) {
        OfferDayDto offerDayTimeDetails = offerService.getOfferDayScheduleDetails(offerId);
        return ResponseEntity.ok(new ApiResponse(offerDayTimeDetails));
    }

    @GetMapping("offer/time-schedule")
    public ResponseEntity<ApiResponse> getOfferTimeDetails(@RequestParam final Integer offerId) {
        OfferTimeDto offerTimeDetails = offerService.getOfferTimeDetails(offerId);
        return ResponseEntity.ok(new ApiResponse(offerTimeDetails));
    }

    @GetMapping("offer/day-time-schedule")
    public ResponseEntity<ApiResponse> getOfferDayTimeScheduleDetails(@RequestParam final Integer offerId) {
        return ResponseEntity.ok( new ApiResponse(offerService.getOfferDayTimeScheduleDetails(offerId)) );
    }

    @RequestMapping(method = RequestMethod.POST, value = "offer-description-metadata", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer,List<OfferDescriptionMetadata>> getOfferDescriptionMetadata(@RequestParam Boolean getAll, @RequestParam (required = false) Integer offerId) throws DataNotFoundException {
        LOG.info("Request for getting offer description metadata");
        return offerService.getOfferDescriptionMetadata(getAll,Objects.nonNull(offerId) ? offerId : null);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-offer-description-metadata", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean updateOfferDescriptionMetadata(@RequestBody Map<Integer,List<OfferDescriptionMetadata>> offerDescriptionData) throws DataNotFoundException {
        LOG.info("Request for updating offer description metadata");
        return offerService.updateOfferDescriptionMetadata(offerDescriptionData);
    }

}
