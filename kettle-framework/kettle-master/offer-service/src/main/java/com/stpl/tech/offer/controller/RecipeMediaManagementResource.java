package com.stpl.tech.offer.controller;

import com.stpl.tech.master.recipe.model.RecipeStep;
import com.stpl.tech.offer.service.recipe.service.RecipeMediaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.stpl.tech.offer.core.MasterServiceConstants.API_VERSION;
import static com.stpl.tech.offer.core.MasterServiceConstants.RECIPE_MEDIA_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.offer.core.MasterServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + RECIPE_MEDIA_MANAGEMENT_ROOT_CONTEXT)
public class RecipeMediaManagementResource {

    private static final Logger LOG = LoggerFactory.getLogger(RecipeMediaManagementResource.class);

    @Autowired
    private RecipeMediaService recipeMediaService;

    @RequestMapping(method = RequestMethod.GET, value = "get-recipe-step-count")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer getRecipeStepCount(Integer recipeId) {
        LOG.info("Request to get recipe step count for recipe id {}", recipeId);
        return recipeMediaService.getRecipeStepCount(recipeId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "download-recipe-media")
    public void downloadRecipeMedia(HttpServletResponse response, @RequestParam Integer recipeId, @RequestParam Integer stepCount) throws IOException {
        LOG.info("Request to download recipe media for recipe id {} and step count {}", recipeId, stepCount);
        recipeMediaService.downloadRecipeMedia(response, recipeId, stepCount);
    }

    @RequestMapping(method = RequestMethod.POST, value = "download-recipe-media-by-step")
    public void downloadRecipeMediaByStep(HttpServletResponse response, @RequestBody RecipeStep recipeStep) throws IOException {
        LOG.info("Request to download recipe media for s3Key {}", recipeStep.getS3Key());
        recipeMediaService.downloadRecipeMedia(response, recipeStep);
    }
}