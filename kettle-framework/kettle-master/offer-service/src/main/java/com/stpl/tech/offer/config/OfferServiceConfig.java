/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.offer.config;

import java.util.TimeZone;

import com.stpl.tech.master.core.service.filter.BrandIdFilter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import com.stpl.tech.master.core.config.KettleInterceptorConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;
import com.stpl.tech.spring.config.MasterSecurityConfiguration;

@SpringBootApplication
@EnableWebMvc
@Configuration
@EnableScheduling
@EnableTransactionManagement
@ComponentScan("com.stpl.tech.offer")
@EnableMongoRepositories(basePackages = {"com.stpl.tech.offer.service.recipe.dao"})
@Import(value = { MasterExternalConfig.class, KettleInterceptorConfig.class, MasterSecurityConfiguration.class , BrandIdFilter.class
})
public class OfferServiceConfig extends SpringBootServletInitializer {

	public OfferServiceConfig() {
		super();
	}

	public static void main(String[] args) {
		SpringApplication.run(OfferServiceConfig.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(OfferServiceConfig.class);
	}

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}
	// beans
	@Bean(name = "multipartResolver")
    public CommonsMultipartResolver commonsMultipartResolver(){
        CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
        commonsMultipartResolver.setDefaultEncoding("utf-8");
        commonsMultipartResolver.setMaxUploadSize(8388608);
        return commonsMultipartResolver;
    }
}