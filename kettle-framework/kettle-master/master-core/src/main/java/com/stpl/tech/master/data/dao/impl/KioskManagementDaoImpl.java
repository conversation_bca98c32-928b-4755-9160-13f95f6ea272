/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.KioskManagementDao;
import com.stpl.tech.master.data.dao.UnitManagementDao;
import com.stpl.tech.master.data.model.KioskCompanyDetailsData;
import com.stpl.tech.master.data.model.KioskCompanyDomainData;
import com.stpl.tech.master.data.model.KioskLocationDetailsData;
import com.stpl.tech.master.data.model.KioskMachineDetailsData;
import com.stpl.tech.master.data.model.KioskOfficeDetailsData;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskLocationDetails;
import com.stpl.tech.master.domain.model.KioskMachine;
import com.stpl.tech.master.domain.model.KioskOfficeDetails;
import com.stpl.tech.master.domain.model.SwitchStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class KioskManagementDaoImpl extends AbstractMasterDaoImpl implements KioskManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(KioskManagementDaoImpl.class);

    @Autowired
    UnitManagementDao unitMetadataDao;

    @Override
    public KioskLocationDetails saveKioskLocationDetails(KioskLocationDetails kioskLocationDetails, UnitDetail unitDetail) {
        KioskLocationDetailsData  data = MasterDataConverter.convert(kioskLocationDetails,unitDetail);
        try {
            add(data);
            manager.flush();
            return MasterDataConverter.convert(data,true);
        }catch (Exception e){
            LOG.error("Exception while adding kiosk location details",e);
        }
        return null;
    }

    @Override
    public KioskCompanyDetails saveCompanyDetails(KioskCompanyDetails kioskCompanyDetails) {
        KioskCompanyDetailsData  data = MasterDataConverter.convert(kioskCompanyDetails);
        try {
            data.setCompanyStatus(SwitchStatus.IN_ACTIVE.toString());
            add(data);
            manager.flush();
            KioskCompanyDetails companyDetails = MasterDataConverter.convert(data);
            if(kioskCompanyDetails.getCompanyDomains() == null){
                throw new DataNotFoundException();
            }else{
                List<IdCodeName> companyDomains = new ArrayList<>();
                kioskCompanyDetails.getCompanyDomains().stream()
                        .filter(companyDomain -> companyDomain != null)
                        .forEach(companyDomain -> {
                            IdCodeName domain = MasterDataConverter.convertToIdCodeName(saveCompanyDomain(companyDomain, data));
                            companyDomains.add(domain);
                        });
                companyDetails.getCompanyDomains().addAll(companyDomains);
            }
            return companyDetails;
        }catch (Exception e){
            LOG.error("Exception while adding kiosk location details",e);
        }
        return null;
    }

    private KioskCompanyDomainData saveCompanyDomain(IdCodeName companyDomain, KioskCompanyDetailsData companyDetailsData){
        KioskCompanyDomainData  data = MasterDataConverter.convert(companyDomain,companyDetailsData);
        try {
            add(data);
            manager.flush();
            return data;
        }catch (Exception e){
            LOG.error("Exception while adding kiosk location details",e);
        }
        return null;
    }

    @Override
    public KioskOfficeDetails saveOfficeDetails(KioskOfficeDetails officeDetails) {
        KioskOfficeDetailsData  data = MasterDataConverter.convert(officeDetails);
        try {
            add(data.getAddressInfo());
            add(data);
            manager.flush();
            return MasterDataConverter.convert(data,true);
        }catch (Exception e){
            LOG.error("Exception while adding kiosk location details",e);
        }
        return null;
    }

    @Override
    public KioskCompanyDetails updateKioskCompanyDetails(KioskCompanyDetails companyDetails) {
        try{
            KioskCompanyDetailsData companyDetailsData = find(KioskCompanyDetailsData.class,companyDetails.getCompanyId());
            if(companyDetailsData!=null){
                companyDetailsData.setCompanyName(companyDetails.getCompanyName());
                companyDetailsData.setCountry(companyDetails.getCountry());
                companyDetailsData.setSubDomain(companyDetails.getKioskSubDomain());
                companyDetailsData.setCompanyStatus(companyDetails.getCompanyStatus().toString());
                IdCodeName contactDetails = companyDetails.getContactDetails();
                if(contactDetails!=null){
                    companyDetailsData.setContactPhone(contactDetails.getShortCode());
                    companyDetailsData.setContactName(contactDetails.getName());
                    companyDetailsData.setContactEmail(contactDetails.getCode());
                }
                companyDetailsData.setPaymentMode(companyDetails.getPaymentMode().toString());
                companyDetailsData = manager.merge(companyDetailsData);
                manager.flush();
                List<IdCodeName> domainList = new ArrayList<>();
                for(IdCodeName domain : companyDetails.getCompanyDomains()){
                    domain = updateCompanyDomain(domain, companyDetailsData);
                    if(domain!=null){
                        domainList.add(domain);
                    }
                }
                KioskCompanyDetails company = MasterDataConverter.convert(companyDetailsData);
                company.getCompanyDomains().addAll(domainList);
            }
        }catch(Exception e){
            LOG.error("Error occurred while updating company details",e);
        }
        return null;
    }

    private IdCodeName updateCompanyDomain(IdCodeName idCodeName,KioskCompanyDetailsData companyDetailsData) {
        KioskCompanyDomainData domainData = find(KioskCompanyDomainData.class,idCodeName.getId());
        if(domainData!=null){
            domainData.setDomainStatus(idCodeName.getStatus().toString());
            domainData.setDomain(idCodeName.getName());
            domainData.setCompanyDetailsData(companyDetailsData);
            domainData = manager.merge(domainData);
            manager.flush();
            return MasterDataConverter.convertToIdCodeName(domainData);
        }
        return null;
    }

    @Override
    public KioskOfficeDetails updateKioskOfficeDetails(KioskOfficeDetails officeDetails) {
        try{
            KioskOfficeDetailsData officeDetailsData = find(KioskOfficeDetailsData.class,officeDetails.getOfficeId());
            if(officeDetailsData!=null){
                IdCodeName contact = officeDetails.getOfficeContact();
                if(contact!=null) {
                    officeDetailsData.setOfficeContactEmail(contact.getName());
                    officeDetailsData.setOfficeContactEmail(contact.getCode());
                    officeDetailsData.setOfficeContactPhone(contact.getShortCode());
                }
                officeDetailsData.setOfficeStatus(officeDetails.getOfficeStatus().toString());
                officeDetailsData.setPaymentMode(officeDetails.getPaymentMode().toString());
                officeDetailsData.setOfficeName(officeDetails.getOfficeName());
                if(officeDetails.getOfficeAddress() != null){
                    officeDetailsData.setAddressInfo(unitMetadataDao.updateAddress(officeDetails.getOfficeAddress(), officeDetailsData.getAddressInfo()));
                }
                officeDetailsData = manager.merge(officeDetailsData);
                manager.flush();
                KioskOfficeDetails office = MasterDataConverter.convert(officeDetailsData,true);
                return office;
            }
        }catch(Exception e){
            LOG.error("Error occurred while updating office details",e);
        }
        return null;
    }

    @Override
    public KioskLocationDetails updateKioskLocationDetails(KioskLocationDetails locationDetails, Integer unitId) {
        try{
            KioskLocationDetailsData locationDetailsData = find(KioskLocationDetailsData.class, locationDetails.getLocationId());
            if(locationDetailsData!=null){
                if(unitId!=null) {
                    UnitDetail unitDetail = find(UnitDetail.class, unitId);
                    locationDetailsData.setAssignedUnit(unitDetail);
                }
                locationDetailsData.setLocationShortCode(locationDetails.getLocationShortCode());
                locationDetailsData.setLocationName(locationDetails.getLocationName());
                locationDetailsData.setLocationStatus(locationDetails.getLocationStatus().toString());
                locationDetailsData.setLocationAddress(locationDetails.getLocationAddress());
                locationDetailsData = manager.merge(locationDetailsData);
                manager.flush();
                KioskLocationDetails kioskLocationDetails = MasterDataConverter.convert(locationDetailsData,true);
                return kioskLocationDetails;
            }
        }catch(Exception e){
            LOG.error("Error occurred while updating office details",e);
        }
        return null;
    }

    @Override
    public KioskLocationDetails getLocationDetails(int locationId) {
        KioskLocationDetailsData locationDetails = find(KioskLocationDetailsData.class,locationId);
        return MasterDataConverter.convert(locationDetails,true);
    }

    @Override
    public KioskCompanyDetails getCompanyDetails(int companyId) {
        KioskCompanyDetailsData companyDetailsData = find(KioskCompanyDetailsData.class,companyId);
        return MasterDataConverter.convert(companyDetailsData);
    }

    @Override
    public KioskOfficeDetails getOfficeDetails(int officeId) {
        KioskOfficeDetailsData officeDetailsData = find(KioskOfficeDetailsData.class, officeId);
        return MasterDataConverter.convert(officeDetailsData,true);
    }

    @Override
    public KioskMachine getKioskMachineDetails(int machineId, IdCodeName unitData) {
        KioskMachineDetailsData machineDetailsData = find(KioskMachineDetailsData.class, machineId);
        return MasterDataConverter.convert(machineDetailsData,unitData);
    }

    @Override
    public KioskLocationDetails updateLocationStatus(Integer locationId, SwitchStatus switchStatus) {
        KioskLocationDetailsData locationDetails = find(KioskLocationDetailsData.class,locationId);
        locationDetails.setLocationStatus(switchStatus.toString());
        locationDetails = manager.merge(locationDetails);
        manager.flush();
        return MasterDataConverter.convert(locationDetails,true);
    }

    @Override
    public KioskOfficeDetails updateOfficeStatus(Integer officeId, SwitchStatus switchStatus) {
        KioskOfficeDetailsData kioskOfficeDetailsData = find(KioskOfficeDetailsData.class,officeId);
        if(kioskOfficeDetailsData!=null){
            kioskOfficeDetailsData.setOfficeStatus(switchStatus.toString());
            kioskOfficeDetailsData.getLocationDetailsDataList().stream()
                    .forEach(kioskLocationDetailsData -> {
                        kioskLocationDetailsData.setLocationStatus(switchStatus.toString());
                        manager.merge(kioskLocationDetailsData);
                    });
            kioskOfficeDetailsData = manager.merge(kioskOfficeDetailsData);
            manager.flush();
            return MasterDataConverter.convert(kioskOfficeDetailsData,true);
        }
        return null;
    }

    @Override
    public KioskCompanyDetails updateCompanyStatus(Integer companyId, SwitchStatus switchStatus) {
        KioskCompanyDetailsData companyDetailsData = find(KioskCompanyDetailsData.class,companyId);
        if(companyDetailsData!=null){
            companyDetailsData.setCompanyStatus(switchStatus.toString());
            companyDetailsData.getOfficeDetailsDataList().stream()
                    .forEach(kioskOfficeDetailsData -> {
                        kioskOfficeDetailsData.setOfficeStatus(switchStatus.toString());
                        kioskOfficeDetailsData = manager.merge(kioskOfficeDetailsData);
                        kioskOfficeDetailsData.getLocationDetailsDataList().stream().forEach(location ->{
                            location.setLocationStatus(switchStatus.toString());
                            manager.merge(location);
                        });
                    });
            companyDetailsData = manager.merge(companyDetailsData);
            manager.flush();
            return MasterDataConverter.convert(companyDetailsData);
        }
        return null;
    }

    @Override
    public KioskLocationDetails assignUnitToLocation(Integer locationId, Integer unitId) {
        try {
            KioskLocationDetailsData locationDetailsData = find(KioskLocationDetailsData.class,locationId);
            if(locationDetailsData!=null){
                UnitDetail unitDetail = find(UnitDetail.class,unitId);
                locationDetailsData.setAssignedUnit(unitDetail);
                locationDetailsData = manager.merge(locationDetailsData);
                manager.flush();
                return MasterDataConverter.convert(locationDetailsData,true);
            }
        }catch (Exception e){
            LOG.error("Error while updating location with unit",e);
        }
        return null;
    }

}
