/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.BrandManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.BrandManagementDao;
import com.stpl.tech.master.data.model.BrandAttributes;
import com.stpl.tech.master.data.model.BrandDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadata;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.RestaurantPartnerKey;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.domain.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

@Service
public class BrandManagementServiceImpl implements BrandManagementService {
    private static final Logger LOG = LoggerFactory.getLogger(BrandManagementServiceImpl.class);

    @Autowired
    private BrandManagementDao dao;

    @Autowired
    private MasterDataCache masterDataCache;
    @Autowired
    private FileArchiveService fileArchiveService;
    @Autowired
    private MasterProperties props;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Brand> getBrands() {
        List<BrandDetail> data = dao.findAll(BrandDetail.class);

        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedBrands = MasterUtil.getMappedBrands();
            data = data.stream().filter(d -> mappedBrands.contains(d.getBrandId())).toList();
        }

        List<Brand> brands = new ArrayList<>();
        for (BrandDetail brandDetail : data) {
            List<BrandAttributes> attributes = dao.getBrandAttributesByBrandId(brandDetail.getBrandId());
            brands.add(MasterDataConverter.convert(brandDetail, attributes));
        }
        return brands;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitPartnerBrandMappingData> getUnitPartnerBrandMappingList() {
        List<UnitPartnerBrandMappingData> data = dao.findAll(UnitPartnerBrandMappingData.class);
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public UnitPartnerBrandMappingData getUnitPartnerMapping(String restaurantId, int partnerId) {
        RestaurantPartnerKey restaurantPartnerKey = new RestaurantPartnerKey(restaurantId, partnerId);
        return masterDataCache.getUnitPartnerBrandMappingMetaData2().get(restaurantPartnerKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public UnitPartnerBrandMappingData addUnitPartnerMapping(UnitPartnerBrandMappingData unitPartnerBrandMappingData,String editLiveDate) throws DataUpdationException {
        UnitPartnerBrandKey unitPartnerBrandKey = MasterDataConverter.convert(unitPartnerBrandMappingData);
        UnitPartnerBrandMappingData existingData = dao.checkUnitPartnerBrandMapping(unitPartnerBrandKey,unitPartnerBrandMappingData.getPriceProfileUnitId(),unitPartnerBrandMappingData.getRestaurantId());

        if(Objects.equals(editLiveDate, "Y") && unitPartnerBrandMappingData.getLiveDate()!=null){
            existingData.setLiveDate(unitPartnerBrandMappingData.getLiveDate());
            existingData =dao.update(existingData);
            updateUnitPartnerBrandCache(unitPartnerBrandMappingData, masterDataCache, unitPartnerBrandKey, LOG);
            return existingData;
        }
        if (existingData == null) {
//            dao.inactiveAllRestMapping(unitPartnerBrandKey);
            unitPartnerBrandMappingData = dao.add(unitPartnerBrandMappingData);
            updateUnitPartnerBrandCache(unitPartnerBrandMappingData, masterDataCache, unitPartnerBrandKey, LOG);
            return unitPartnerBrandMappingData;
        } else if (existingData.getLiveDate()==null) {
            existingData.setPriceProfileUnitId(unitPartnerBrandMappingData.getPriceProfileUnitId());
            existingData.setPartnerSourceSystemId(unitPartnerBrandMappingData.getPartnerSourceSystemId());
            existingData.setLiveDate(unitPartnerBrandMappingData.getLiveDate());
            existingData.setStatus(AppConstants.ACTIVE);
            existingData =dao.update(existingData);
            updateUnitPartnerBrandCache(unitPartnerBrandMappingData, masterDataCache, unitPartnerBrandKey, LOG);
            return existingData;
        } else {
//            throw new DataUpdationException("Mapping already exists");
            existingData.setPriceProfileUnitId(unitPartnerBrandMappingData.getPriceProfileUnitId());
            existingData.setPartnerSourceSystemId(unitPartnerBrandMappingData.getPartnerSourceSystemId());
            existingData.setStatus(AppConstants.ACTIVE);
            existingData =dao.update(existingData);
            updateUnitPartnerBrandCache(unitPartnerBrandMappingData, masterDataCache, unitPartnerBrandKey, LOG);
            return existingData;
        }
    }

    private void updateUnitPartnerBrandCache(UnitPartnerBrandMappingData unitPartnerBrandMappingData, MasterDataCache masterDataCache, UnitPartnerBrandKey unitPartnerBrandKey, Logger log) {
        RestaurantPartnerKey restaurantPartnerKey = MasterDataConverter.convertToKey(unitPartnerBrandMappingData);
        masterDataCache.getUnitPartnerBrandMappingMetaData2().put(restaurantPartnerKey, unitPartnerBrandMappingData);
        masterDataCache.getUnitPartnerBrandMappingMetaData().put(unitPartnerBrandKey, unitPartnerBrandMappingData);
        masterDataCache.getUnitwisePartnerBrandMappingMetaData().put(unitPartnerBrandKey.getUnitId(), unitPartnerBrandMappingData);
        log.info("Successful in setting and updtaing cache for unit:{}", unitPartnerBrandKey.getUnitId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<UnitPartnerBrandMappingData> addAllUnitPartnerMapping(List<UnitPartnerBrandMappingData> unitPartnerBrandMappingDatas) throws DataUpdationException {
        List<UnitPartnerBrandMappingData> list = new ArrayList<UnitPartnerBrandMappingData>();
        for (UnitPartnerBrandMappingData data : unitPartnerBrandMappingDatas) {
            list.add(addUnitPartnerMapping(data,null));
        }
        return list;
    }

    @Override
    public List<UnitPartnerBrandMappingData> getAllUnitPartnerBrandMappings() {
        return new ArrayList<>(masterDataCache.getUnitPartnerBrandMappingMetaData().values());
    }

    @Override
    public UnitPartnerBrandMappingData getUnitPartnerBrandMapping(UnitPartnerBrandKey key) {
        return dao.getUnitPartnerBrandMapping(key);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitPartnerBrandMappingData> getAllUnitPartnerBrandMapping(Integer brandId,Integer partnerId) {
        return dao.getAllUnitPartnerBrandMapping(brandId,partnerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public UnitPartnerBrandMappingData activateUnitPartnerBrandMapping(Integer mappingId, boolean activate) throws DataUpdationException {
        UnitPartnerBrandMappingData data = dao.find(UnitPartnerBrandMappingData.class, mappingId);
        if (data != null) {
            dao.inactiveAllRestMapping(MasterDataConverter.convert(data));
            data.setStatus(activate ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE);
            data = dao.update(data);
            if (data == null) {
                throw new DataUpdationException("Error updating unit partner brand mapping status");
            }
            UnitPartnerBrandKey unitPartnerBrandKey = MasterDataConverter.convert(data);
            RestaurantPartnerKey restaurantPartnerKey = MasterDataConverter.convertToKey(data);
            if (activate) {
                masterDataCache.getUnitPartnerBrandMappingMetaData2().put(restaurantPartnerKey, data);
                masterDataCache.getUnitPartnerBrandMappingMetaData().put(unitPartnerBrandKey, data);
                masterDataCache.getUnitwisePartnerBrandMappingMetaData().put(data.getUnitId(), data);
            } else {
                masterDataCache.getUnitPartnerBrandMappingMetaData2().remove(restaurantPartnerKey);
                masterDataCache.getUnitPartnerBrandMappingMetaData().remove(unitPartnerBrandKey);
                //masterDataCache.getUnitwisePartnerBrandMappingMetaData().get(data.getUnitId()).remove(data);
                masterDataCache.getUnitwisePartnerBrandMappingMetaData().remove(data.getUnitId(), data);
            }
            return data;
        }
        throw new DataUpdationException("Mapping id is not valid");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void toggleUnitPartnerBrandMapping(List<Integer> mappingids, Boolean status) throws DataUpdationException {
        for(Integer id: mappingids) {
            activateUnitPartnerBrandMapping(id,status);
        }
    }

    @Override
    public List<UnitPartnerBrandMappingMetadata> getAllUnitPartnerBrandMappingMetadata() {
        return dao.getAllUnitPartnerBrandMappingMetadata();
    }

    @Override
    public List<UnitPartnerBrandMappingMetadata> getUnitPartnerBrandMappingMetadataByKey(UnitPartnerBrandMappingMetadataType key) {
        return dao.getUnitPartnerBrandMappingMetadataByKey(key);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public List<UnitPartnerBrandMappingMetadata> addUnitPartnerBrandMappingMetadataList(List<UnitPartnerBrandMappingMetadata> data)
        throws DataUpdationException {
        List<UnitPartnerBrandMappingMetadata> updatedMetadata = new ArrayList<>();
        for (UnitPartnerBrandMappingMetadata metadata : data) {
            UnitPartnerBrandMappingMetadata updatedData = addUnitPartnerBrandMappingMetadata(metadata);
            if (updatedData != null) {
                updatedMetadata.add(updatedData);
            }
        }
        return updatedMetadata;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public UnitPartnerBrandMappingMetadata addUnitPartnerBrandMappingMetadata(UnitPartnerBrandMappingMetadata data)
        throws DataUpdationException {
        if (data.getUnitId() == null || data.getPartnerId() == null || data.getBrandId() == null || data.getKey() == null
            || data.getValue() == null) {
            StringBuilder error = new StringBuilder("Missing keys: ");
            error.append(data.getUnitId() == null ? "unitId, " : "");
            error.append(data.getPartnerId() == null ? "partnerId, " : "");
            error.append(data.getBrandId() == null ? "brandId, " : "");
            error.append(data.getValue() == null ? "value, " : "");
            error.append(data.getKey() == null ? "key, " : "");
            throw new DataUpdationException(error.toString());
        }
        UnitPartnerBrandMappingMetadata metadata = dao.getUnitPartnerBrandMappingMetadata(data.getKey(), data.getUnitId(),
            data.getPartnerId(), data.getBrandId());
        if (metadata != null) {
            metadata.setValue(data.getValue());
            metadata.setStatus(data.getStatus());
            return dao.add(metadata);
        } else {
            data.setMetadataId(null);
            return dao.add(data);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public UnitPartnerBrandMappingMetadata changeStatusUnitPartnerBrandMappingMetadata(Integer metadataId, boolean activate)
        throws DataUpdationException {
        if (metadataId == null) {
            throw new DataUpdationException("Metadata id is missing");
        }
        UnitPartnerBrandMappingMetadata metadata = dao.find(UnitPartnerBrandMappingMetadata.class, metadataId);
        if (metadata != null) {
            metadata.setStatus(activate ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE);
            return dao.add(metadata);
        }
        throw new DataUpdationException("Invalid metadata key");
    }

    @Override
    public Map<UnitPartnerBrandMappingMetadataType, String> unitPartnerBrandMappingMetadataByUnitPartnerBrand(UnitPartnerBrandKey key) {
        Map<UnitPartnerBrandMappingMetadataType, String> metadata;
        if (!masterDataCache.getUnitPartnerBrandMetadataMap().containsKey(key) ||
            masterDataCache.getUnitPartnerBrandMetadataMap().get(key) == null) {
            metadata = new HashMap<>();
            Arrays.asList(UnitPartnerBrandMappingMetadataType.values()).forEach(type ->
                metadata.put(type, type.getDefaultValue()));
            return metadata;
        } else {
            metadata = masterDataCache.getUnitPartnerBrandMetadataMap().get(key);
            Arrays.asList(UnitPartnerBrandMappingMetadataType.values()).forEach(type -> {
                if (!masterDataCache.getUnitPartnerBrandMetadataMap().get(key).containsKey(type) ||
                    masterDataCache.getUnitPartnerBrandMetadataMap().get(key).get(type) == null) {
                    metadata.put(type, type.getDefaultValue());
                }
            });
            return metadata;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean uploadMonkMetaData(MimeType mimeType,Integer brandId, String attributeKey, String attributeValue,
                                      MultipartFile file, boolean isImage) {
        String fileName = null;
        BrandAttributes brandAttributesPresent = dao.getBrandAttributesByBrandIdAndAttributeKey(brandId, attributeKey);
        if (brandAttributesPresent != null) {
            if (isImage) {
                fileName=getNewFileName(brandAttributesPresent.getAttributeValue(),mimeType);
            } else {
                fileName = attributeValue;
            }
            brandAttributesPresent.setAttributeValue(fileName);
        } else {

            if (isImage) {
                fileName=attributeKey+"_1" + "." + mimeType.name().toLowerCase();
            } else {
                fileName = attributeValue;
            }
            brandAttributesPresent = new BrandAttributes();
            brandAttributesPresent.setBrandId(brandId);
            brandAttributesPresent.setAttributeKey(attributeKey);
            brandAttributesPresent.setAttributeValue(fileName);
            brandAttributesPresent.setAttributeType("java.lang.String");
        }

        String baseDir = "master-service/product_image";
        FileDetail s3File = null;


        try {
            s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, fileName, file,
                true);
            if(s3File!=null) {
                dao.add(brandAttributesPresent);
            }
            return true;
        } catch (Exception e) {
            LOG.info("error while updating monk- metadata  ", e);
            return false;
        }

    }

    private String getNewFileName(String fileName, MimeType mimeType) {
        String[] strings = fileName.split("_");
        int lastIndex = strings.length - 1;
        fileName = "";
        for (int i = 0; i < lastIndex; i++) {
            fileName += strings[i] + "_";
        }
        fileName += (Integer.valueOf(strings[lastIndex].split("\\.")[0]) + 1);
        return fileName + "." + mimeType.name().toLowerCase();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<BrandAttributes> getAllBrandAttributes() {
        return dao.getBrandAttributes();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean updateBrandAttributes(List<BrandAttributes> brandAttributes){
        try {
            if(!CollectionUtils.isEmpty(brandAttributes)) {
                for (BrandAttributes attribute : brandAttributes) {
                    dao.updateBrandAttributes(attribute);
                }
                Map<Integer, Brand> proxyBrandMetadataCache = new HashMap<>();
                List<Brand> brands = getBrands();
                for (Brand brand : brands) {
                    proxyBrandMetadataCache.put(brand.getBrandId(), brand);
                }
                if (Objects.nonNull(proxyBrandMetadataCache)) {
                    masterDataCache.clearBrandMetaData();
                    masterDataCache.getBrandMetaData().putAll(proxyBrandMetadataCache);
                }
                proxyBrandMetadataCache.clear();
            }
            return true;
        }catch (Exception e){
            LOG.info("Error while updating brand attributes :::: {}",e);
            return false;
        }
    }

    @Override
    public List<IdCodeName> getBrandsInShort(Integer brandId) {
        List<IdCodeName> codeNames = new ArrayList<>();
        brandId = RequestContext.getBrandId();
        Integer companyId = RequestContext.getCompanyId();
        if(Objects.nonNull(brandId)) {
            for(Brand brand : masterDataCache.getAllBrands()) {
                if(Objects.equals(brand.getBrandId(), brandId)) {
                    codeNames.add(new IdCodeName(
                            brand.getBrandId(), brand.getBrandName(),
                            brand.getStatus(), null
                    ));
                    return codeNames;
                }
            }
            return null;
        } else if (Objects.nonNull(companyId)) {
            List<Brand> mappedBrands = masterDataCache.getCompanyBrandsMap().getOrDefault(companyId, new ArrayList<>());
            for (Brand b : mappedBrands) {
                codeNames.add(new IdCodeName(
                        b.getBrandId(), b.getBrandName(),
                        b.getStatus(), null
                ));
            }
            codeNames.sort(Comparator.comparing(IdCodeName :: getId));
            return codeNames;
        }

        for(Brand brand : masterDataCache.getAllBrands()) {
            codeNames.add(new IdCodeName(
                    brand.getBrandId(), brand.getBrandName(),
                    brand.getStatus(), null
            ));
        }

        codeNames.sort(Comparator.comparing(IdCodeName::getId));
        return codeNames;
    }

}
