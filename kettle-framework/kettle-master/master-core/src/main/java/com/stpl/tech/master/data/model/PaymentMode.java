/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * PaymentMode generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PAYMENT_MODE")
public class PaymentMode implements java.io.Serializable {

	private Integer paymentModeId;
	private String modeName;
	private String modeType;
	private String modeDescription;
	private String settlementType;
	private boolean generatePull;
	private List<Denomination> denominations;
	private BigDecimal commissionRate;
	private String modeStatus;
	private String modeCategory;
	private String editable;
	private String applicableOnDiscountedOrders;
	private String automaticPullValidate;
	private String automaticTransfer;
	private String automaticCloseTransfer;
	private String needsSettlementSlip;
	private String validationSource;
	private String ledgerName;

	public PaymentMode() {
	}

	public PaymentMode(String modeName, String modeType, String modeDescription, String settlementType) {
		this.modeName = modeName;
		this.modeType = modeType;
		this.modeDescription = modeDescription;
		this.settlementType = settlementType;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PAYMENT_MODE_ID", unique = true, nullable = false)
	public Integer getPaymentModeId() {
		return this.paymentModeId;
	}

	public void setPaymentModeId(Integer paymentModeId) {
		this.paymentModeId = paymentModeId;
	}

	@Column(name = "MODE_NAME", nullable = false, length = 100)
	public String getModeName() {
		return this.modeName;
	}

	public void setModeName(String modeName) {
		this.modeName = modeName;
	}

	@Column(name = "MODE_TYPE", nullable = false, length = 15)
	public String getModeType() {
		return this.modeType;
	}

	public void setModeType(String modeType) {
		this.modeType = modeType;
	}

	@Column(name = "MODE_DESCRIPTION", nullable = false)
	public String getModeDescription() {
		return this.modeDescription;
	}

	public void setModeDescription(String modeDescription) {
		this.modeDescription = modeDescription;
	}

	@Column(name = "SETTLEMENT_TYPE", nullable = false, length = 20)
	public String getSettlementType() {
		return this.settlementType;
	}

	public void setSettlementType(String settlementType) {
		this.settlementType = settlementType;
	}

	@Column(name = "GENERATE_PULL", nullable = false,columnDefinition = "TINYINT(1)")
	public boolean isGeneratePull() {
		return generatePull;
	}

	public void setGeneratePull(boolean generatePull) {
		this.generatePull = generatePull;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "paymentMode")
	public List<Denomination> getDenominations() {
		return denominations;
	}

	public void setDenominations(List<Denomination> denominations) {
		this.denominations = denominations;
	}

	@Column(name = "COMMISSION_RATE", nullable = false)
	public BigDecimal getCommissionRate() {
		return commissionRate;
	}

	public void setCommissionRate(BigDecimal commissionRate) {
		this.commissionRate = commissionRate;
	}

	@Column(name = "MODE_STATUS", nullable = false)
	public String getModeStatus() {
		return modeStatus;
	}

	public void setModeStatus(String modeStatus) {
		this.modeStatus = modeStatus;
	}

	@Column(name = "MODE_CATEGORY", nullable = false)
	public String getModeCategory() {
		return modeCategory;
	}

	public void setModeCategory(String modeCategory) {
		this.modeCategory = modeCategory;
	}

	@Column(name = "AUTOMATIC_PULL_VALIDATE", nullable = false)
	public String getAutomaticPullValidate() {
		return automaticPullValidate;
	}

	public void setAutomaticPullValidate(String automaticPullValidate) {
		this.automaticPullValidate = automaticPullValidate;
	}
	@Column(name = "AUTOMATIC_TRANSFER", nullable = false)
	public String getAutomaticTransfer() {
		return automaticTransfer;
	}

	public void setAutomaticTransfer(String automaticTransfer) {
		this.automaticTransfer = automaticTransfer;
	}
	@Column(name = "AUTOMATIC_CLOSE_TRANSFER", nullable = false)
	public String getAutomaticCloseTransfer() {
		return automaticCloseTransfer;
	}

	public void setAutomaticCloseTransfer(String automaticCloseTransfer) {
		this.automaticCloseTransfer = automaticCloseTransfer;
	}

	@Column(name = "IS_EDITABLE", nullable = false)
	public String getEditable() {
		return editable;
	}

	public void setEditable(String editable) {
		this.editable = editable;
	}

	@Column(name = "APPLICABLE_ON_DISCOUNTED_ORDERS", nullable = true)
	public String getApplicableOnDiscountedOrders() {
		return applicableOnDiscountedOrders;
	}

	public void setApplicableOnDiscountedOrders(String applicableOnDiscountedOrders) {
		this.applicableOnDiscountedOrders = applicableOnDiscountedOrders;
	}

	@Column(name = "NEEDS_SETTLEMENT_SLIP_NUMBER", nullable = true)
	public String getNeedsSettlementSlip() {
		return needsSettlementSlip;
	}

	public void setNeedsSettlementSlip(String needsSettlementSlip) {
		this.needsSettlementSlip = needsSettlementSlip;
	}

	@Column(name = "VALIDATION_SOURCE", nullable = true)
	public String getValidationSource() {
		return validationSource;
	}

	public void setValidationSource(String validationSource) {
		this.validationSource = validationSource;
	}

	@Column(name = "LEDGER_NAME")
	public String getLedgerName() {
		return ledgerName;
	}

	public void setLedgerName(String ledgerName) {
		this.ledgerName = ledgerName;
	}


}
