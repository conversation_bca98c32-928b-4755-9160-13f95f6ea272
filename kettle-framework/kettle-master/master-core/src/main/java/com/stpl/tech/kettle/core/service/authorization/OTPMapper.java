/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.authorization;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.domain.model.OtpType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.RandomStringGenerator;

public class OTPMapper {

	private static final Map<OtpType, Map<String, Pair<String, Integer>>> otpMapping = new HashMap<>();
	private static final Map<String, Long> otpTraffic = new HashMap<>();
	private static final OTPMapper INSTANCE = new OTPMapper();
	private static final RandomStringGenerator generator = new RandomStringGenerator();

	private OTPMapper() {
		for (OtpType type : OtpType.values()) {
			otpMapping.put(type, new HashMap<String, Pair<String, Integer>>());
		}
	}

	public static OTPMapper getInstance() {
		return INSTANCE;
	}

	public synchronized String getOTP(OtpType otpType, String contactNumber) {
		// checkDuplicates(getKey(otpType, contactNumber));
		Map<String, Pair<String, Integer>> otpTypeData = otpMapping.get(otpType);
		Pair<String, Integer> otpData = otpTypeData.get(contactNumber);
		if (otpData != null) {
			if (otpData.getValue() > 2) {
				removeOTP(otpType, contactNumber);
			} else {
				otpData.setValue(otpData.getValue() + 1);
				otpTypeData.put(contactNumber, otpData);
				otpMapping.put(otpType, otpTypeData);
			}
		}
		return otpData != null ? otpData.getKey() : null;
	}

	public synchronized void removeOTP(OtpType otpType, String contactNumber) {
		otpMapping.get(otpType).remove(contactNumber);
	}

	public synchronized String generateOTP(boolean lastFourDigits, OtpType otpType, String contactNumber,
			EnvType envType) throws DuplicateRequestException {
		String key = getKey(otpType, contactNumber);
		checkDuplicates(key);
		String otp = envType == null || AppUtils.isProd(envType)
				? lastFourDigits ? getLastFour(contactNumber) : generator.getRandonNumber(4)
				: "1234";
		Map<String, Pair<String, Integer>> otpTypeData = otpMapping.get(otpType);
		Pair<String, Integer> otpData = otpTypeData.get(contactNumber);
		int count = 0;
		if (otpData != null) {
			otp = otpData.getKey();
			count = otpData.getValue();
		}
		otpMapping.get(otpType).put(contactNumber, new Pair<String, Integer>(otp, count));
		otpTraffic.put(key, System.currentTimeMillis() + 3000);
		return otpMapping.get(otpType).get(contactNumber).getKey();
	}

	private String getLastFour(String contactNumber) {
		return contactNumber.substring(contactNumber.length() - 4);
	}

	private String getKey(OtpType otpType, String contactNumber) {
		return otpType.name() + contactNumber;
	}

	private void checkDuplicates(String key) throws DuplicateRequestException {
		if (otpTraffic.containsKey(key)) {
			Long l = otpTraffic.get(key);
			if (l < System.currentTimeMillis()) {
				otpTraffic.remove(key);
			} else {
				throw new DuplicateRequestException("Duplicate Request for OTP");
			}
		}
	}

	public synchronized String setOTP(OtpType otpType, String contactNumber, EnvType envType, String otp) {
		otpMapping.get(otpType).put(contactNumber, new Pair<String, Integer>(otp, 0));
		return otpMapping.get(otpType).get(contactNumber).getKey();
	}

	public synchronized String generateOTP(boolean lastFourDigits, OtpType otpType, String contactNumber)
			throws DuplicateRequestException {
		return generateOTP(lastFourDigits, otpType, contactNumber, null);
	}
}
