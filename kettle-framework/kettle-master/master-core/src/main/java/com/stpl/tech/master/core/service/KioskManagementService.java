/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskLocationDetails;
import com.stpl.tech.master.domain.model.KioskMachine;
import com.stpl.tech.master.domain.model.KioskOfficeDetails;

import java.util.List;

public interface KioskManagementService {

    public KioskLocationDetails saveLocationDetails(KioskLocationDetails kioskLocationDetails) throws DataNotFoundException;
    public KioskOfficeDetails saveOfficeDetails(KioskOfficeDetails kioskOfficeDetails) throws DataNotFoundException;
    public KioskCompanyDetails saveCompanyDetails(KioskCompanyDetails kioskCompanyDetails);

    public KioskCompanyDetails updateCompanyDetails(KioskCompanyDetails kioskCompanyDetails);
    public KioskOfficeDetails updateOfficeDetails(KioskOfficeDetails kioskOfficeDetails) throws DataNotFoundException;
    public KioskLocationDetails updateLocationDetails(KioskLocationDetails kioskLocationDetails) throws DataNotFoundException;

    public KioskLocationDetails getLocationDetails(int locationId);
    public KioskCompanyDetails getCompanyDetails(int companyId);
    public KioskOfficeDetails getOfficeDetails(int officeId);
    public KioskMachine getMachineDetailsByOffice(int officeId,Integer unitId);
    public List<KioskCompanyDetails> getCompanyDetailMap();

    public Boolean changeCompanyStatus(Integer companyId, boolean isActivate);
    public Boolean changeOfficeStatus(Integer officeId, Integer companyId, boolean isActivate);
    public Boolean changeLocationStatus(Integer locationId,Integer officeId, Integer companyId, boolean isActivate);

    public KioskLocationDetails assignUnit(Integer locationId, Integer unitId);
}
