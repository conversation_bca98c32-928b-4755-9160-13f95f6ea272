package com.stpl.tech.master.core.data.vo;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serial;
import java.io.Serializable;

@ExcelSheet(value = "Monk Recipe Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkRecipeDetailVo implements Serializable {

    private static final long serialVersionUID = 2738110072058423782L;

    @ExcelField(headerName = "KEY")
    private String key;
    @ExcelField(headerName = "PRODUCT_ID")
    private Integer productId;
    @ExcelField(headerName = "PRODUCT_NAME")
    private String productName;
    @ExcelField(headerName = "DIMENSION")
    private String dimension;
    @ExcelField(headerName = "BOIL_SETTLE")
    private Integer boilSettle;
    @ExcelField(headerName = "WATER")
    private Integer water;
    @ExcelField(headerName = "MILK")
    private Integer milk;
    @ExcelField(headerName = "NO_OF_BOILS")
    private Integer noOfBoils;
    @ExcelField(headerName = "HEATING_TIME_MINS")
    private Integer heatingTimeMins;
    @ExcelField(headerName = "HEATING_TIME_SECS")
    private Integer heatingTimeSecs;
    @ExcelField(headerName = "QUANTITY")
    private Integer quantity;
    @ExcelField(headerName = "PREP")
    private String prep;
    @ExcelField(headerName = "CONTENT")
    private String content;
    @ExcelField(headerName = "MONK_VERSION")
    private String monkVersion = AppConstants.CHAI_MONK_DEFAULT_VERSION;
    @ExcelField(headerName = "MILK_VARIANT_MONK_VERSION")
    private String milkVariantMonkVersion = AppConstants.CHAI_MONK_DEFAULT_VERSION;

}
