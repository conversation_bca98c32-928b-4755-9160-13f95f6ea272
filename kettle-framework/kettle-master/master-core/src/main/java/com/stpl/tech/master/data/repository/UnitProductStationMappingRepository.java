package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.Status;
import com.stpl.tech.master.data.model.UnitProductStationMapping;
import com.stpl.tech.master.data.model.UnitStationCategoryMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Repository
public interface UnitProductStationMappingRepository extends JpaRepository<UnitProductStationMapping, Long> {
    
    @Query("SELECT m FROM UnitProductStationMapping m LEFT JOIN m.stationCategoryMapping scm " +
           "WHERE scm.unitId = :unitId AND m.productId = :productId AND m.status = 'ACTIVE'")
    List<UnitProductStationMapping> findByUnitIdAndProductId(
        @Param("unitId") Integer unitId,
        @Param("productId") Integer productId
    );

    @Query("SELECT m FROM UnitProductStationMapping m " +
                   "JOIN FETCH m.stationCategoryMapping scm " +
                   "WHERE scm.unitId = :unitId " +
                   "AND scm.status = :status " +
                   "AND m.status = :status")
    List<UnitProductStationMapping> findProductRefLookupMappings(
            @Param("unitId") Integer unitId,
            @Param("status") Status status
    );
    
    @Query("SELECT DISTINCT scm FROM UnitStationCategoryMapping scm " +
           "JOIN FETCH scm.productMappings pm " +
           "WHERE scm.unitId = :unitId " +
           "AND scm.status = :status " +
           "AND pm.status = :status")
    List<UnitStationCategoryMapping> findUnitStationMappingsWithProducts(
            @Param("unitId") Integer unitId,
            @Param("status") Status status
    );
    
}