package com.stpl.tech.master.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ExcelSheet(value = "Redeemed Coupon Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Entity
@Table(name = "REDEEMED_COUPON_DETAIL_DATA")
public class RedeemedCouponDetailData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DELIVERY_COUPON_ID", unique = true, nullable = false)
    private Integer RedeemedCouponId;
    @Column(name = "COUPON_CODE")
    @ExcelField
    private String couponCode;
    @Column(name = "BRAND_ID")
    @ExcelField
    private Integer brandId;
    @Column(name = "BILL_AMOUNT")
    @ExcelField
    private Double billAmount;
    @Column(name = "COUPON_DISCOUNT")
    @ExcelField
    private Double couponDiscount;
    @Column(name = "MERCHENT_DISCOUNT")
    @ExcelField
    private Double merchentDiscount;
    @Column(name = "REGION")
    @ExcelField
    private String region;
    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
    @Column(name = "UPDATED_AT")
    private Date updatedAt;

}
