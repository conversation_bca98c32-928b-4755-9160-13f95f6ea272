package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


/**
 * XMLReportVersionData generated by Sourabh
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "XML_REPORT_VERSION")
public class XMLReportVersionData implements java.io.Serializable {

    private Integer versionId;
    private Integer versionNo;
    private String s3Link;
    private String fileName;
    private String mimeType;
    private String s3Bucket;
    private String s3Key;
    private String defaultV;
    private String comment;
    private Date lastUpdated;

    private XMLReportDefinitionData xmlReportDefinitionData;

    public XMLReportVersionData() {

    }
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "VERSION_ID", unique = true, nullable = false)
    public Integer getVersionId() {
        return versionId;
    }

    public void setVersionId(Integer versionId) {
        this.versionId = versionId;
    }

    @Column(name = "VERSION_NO", nullable = true)
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Column(name = "S3_LINK", nullable = true, length = 1000)
    public String getS3Link() {
        return s3Link;
    }

    public void setS3Link(String s3Link) {
        this.s3Link = s3Link;
    }

    @Column(name = "FILE_NAME", nullable = true, length = 200)
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Column(name = "MIME_TYPE", nullable = true, length = 45)
    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    @Column(name = "S3_BUCKET", nullable = true, length = 100)
    public String getS3Bucket() {
        return s3Bucket;
    }

    public void setS3Bucket(String s3Bucket) {
        this.s3Bucket = s3Bucket;
    }

    @Column(name = "S3_FILE_KEY", nullable = true, length = 500)
    public String getS3Key() {
        return s3Key;
    }

    public void setS3Key(String s3Key) {
        this.s3Key = s3Key;
    }

    @Column(name = "DEFAULT_VERSION", nullable = true, length = 1)
    public String getDefaultV() {
        return defaultV;
    }

    public void setDefaultV(String defaultV) {
        this.defaultV = defaultV;
    }

    @Column(name = "COMMENTS", nullable = true, length = 1000)
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATED", nullable = true, length = 19)
    public Date getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(Date lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REPORT_ID", nullable = false)
    public XMLReportDefinitionData getXmlReportDefinitionData() {
        return xmlReportDefinitionData;
    }

    public void setXmlReportDefinitionData(XMLReportDefinitionData xmlReportDefinitionData) {
        this.xmlReportDefinitionData = xmlReportDefinitionData;
    }
}
