/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.offer.service;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.stpl.tech.master.core.LaunchOfferStrategy;
import com.stpl.tech.master.core.data.vo.HourlyOfferUnitMapping;
import com.stpl.tech.master.data.model.CampaignCouponMapping;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CustomerOfferMappingData;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.domain.model.CouponDetail;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

public interface OfferManagementExternalService {

	public void updateCouponUsageByOne(int id, String offerCode, int orderId);
	
	public CouponDetailData getCoupon(String offerCode);

	/**
	 * @param contactNumber
	 * @param couponCode
	 */
	public void setContactNumber(String contactNumber, String couponCode);

	/**
	 * @param offerCode
	 * @param count
	 */
	public void setUsageCountOfAllCouponsForAnOffer(int offerId, int count);
	
	public List<CouponDetailData> getAllInactiveCoupons(int offerId);

	/**
	 * @param offerDetailId
	 * @return
	 */
	public Set<String> getAllCustomerWithOffer(Integer offerDetailId);

	List<CouponDetail> getAllCouponForCustomer(String number, String date, Integer brandId);

    /**
	 * @return
	 */
	public List<OfferDetailData> getAllValidOffersWithLaunchStrategy(List<String> launchStrategy);
	
	/**
	 * @return
	 */
	public List<OfferDetailData> getAllOffersWithLaunchStrategy(List<String> launchStrategy);

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    HourlyOfferUnitMapping getHourlyOfferUnitMapping(int offerDetailId);

    /**
	 * @param offerDetailId
	 */
	public void deactiveOffer(Integer offerDetailId);

	/**
	 * @param mapping
	 */
	public void add(CustomerOfferMappingData mapping);

	/**
	 * @param endTime
	 * @return
	 */
	public List<CustomerOfferMappingData> getAllPendingNotificationMassOffers(int offerId, Date endTime);

	/**
	 * @param customerOfferMappingDataId
	 */
	public void setCustomerOfferMappingDataNotified(Integer customerOfferMappingDataId);


    public Set<String> getCurrentCycleWinners(List<LaunchOfferStrategy> hourlyForEachUnit);
    
	public String getOfferBudgerCategory(String couponCode);

	public void createPartnerDiscountCoupon(String couponName,String channelPartner);
	
	List<CampaignCouponMapping> getActiveCampaignsByJourney(String repeatType, Integer journey);

	public boolean updateCustomerCouponData(String oldContactNumber, String newContactNumber);

	List<CouponDetail> getAllCouponForChannelPartner(String partnerId,String offerScope);
}
