/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.ListTypes;
import com.stpl.tech.master.core.external.cache.MasterDataCacheProxy;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitContactDetailsData;
import com.stpl.tech.master.domain.model.CondimentGroupData;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.PriceProfileKey;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductDimensionKey;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitPartnerMenuMapping;
import com.stpl.tech.master.locality.model.LocalityMapping;
import com.stpl.tech.master.recipe.model.RecipeDetail;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface MasterDataCacheService {

	public void updateListData(String type) throws DataNotFoundException;

	public void updateListData(ListTypes type) throws DataNotFoundException;

	public void refreshUnits(boolean flushInventory) throws DataNotFoundException;

	UnitBasicDetail addUnit(Unit unit) throws DataNotFoundException;

	public UnitBasicDetail addUnit(Unit unit, MasterDataCacheProxy cacheProxy) throws DataNotFoundException;

	public void changeUnitLiveStatus(int unitId, boolean status);

	public void addRecipe(RecipeDetail recipe) throws DataNotFoundException;

	public String addEmployee(Employee e);

	public EmployeeBasicDetail addEmployeeBasicDetail(EmployeeBasicDetail e);

	public void loadCache() throws DataNotFoundException;

	void refreshUnitToPartnerEdcMapping() ;

//	void refreshAddressInfo() throws DataNotFoundException;

    void refreshCacheReferenceValue();

    void refreshExternalPartnerDetailCache();

	void loadUnitChannelPartnerMapping();

    public void loadEmployeeMealDimensions();

	public void refreshRecipeCache();

	public void refreshPaymentCache() throws DataNotFoundException;

	public void refreshDivisions() throws DataNotFoundException;

	public void refreshDepartments() throws DataNotFoundException;

	public void refreshDesignations() throws DataNotFoundException;

	public void refreshTaxProfiles() throws DataNotFoundException;

	public void refreshDenominations() throws DataNotFoundException;

	public void refreshEmployees() throws DataNotFoundException;

	public void refreshPreAuthenticatedAPIs() throws DataNotFoundException;

	boolean addSubscriptionProduct(List<Product> allProducts);

	public void refreshListData() throws DataNotFoundException;

	public void refreshAddonData() throws DataNotFoundException;

	public void refreshProductCache() throws DataNotFoundException;

	public void addProductToMap(Map<String, ListData> map, Product product) throws DataNotFoundException;

	public void refreshKioskCompanies() throws DataNotFoundException;

	public void refreshKioskMachines() throws DataNotFoundException;

	public void refreshLocalities();

	public void refreshExternalAPICacheCache();

	public Map<String, LocalityMapping> getAllLocalities();

    void refreshLocalityMappings();

    public void refreshLocations();

	public void refreshStates();

	boolean refreshCancellationReasons();

	public Set<IdCodeName> getUnitCityList();

	public Set<IdCodeName> getUnitsOfLocation(int locationId);

	public void refreshTokenizedApis();

	public void refreshCompanies() throws DataNotFoundException;

	public void refreshEnvironmentPropsCache();

	public void refreshItemPerTicket() throws DataNotFoundException;

    void updatePartnerListData(ListTypes type) throws DataNotFoundException;

    void refreshChannelPartnerCache() throws DataNotFoundException;

    public void refreshBrandMetaData();

	public void refreshExpenseMetaDataCache();

	public void loadChaayosCashConfig();

	void loadUnitPartnerMenuMapping();

	public Unit getUnit(Integer unitId);

    public void refreshUnitPartnerBrandMapping();

	void refreshUnitPartnerBrandMetadata();

	public void refreshEntityAliasMappingData();

	void loadCache(boolean flushInventory) throws DataNotFoundException;

	boolean refreshUnit(Integer unitId, Boolean flushInventory);

	void loadUnitPartnerMenuMapping(UnitPartnerMenuMapping mapping);

    void loadUnitChannelPartnerMapping(UnitChannelPartnerMappingData mapping);

	public void refreshRegionsMapData();

	public void refreshSourceCondimentMapping() throws DataNotFoundException;

	public void refreshGroupCondimentMapping() throws DataNotFoundException;

	public  Map<String,List<CondimentGroupData> > getSrcToCondimentMap();

	public void refreshUnitTerminalDataMap();

    void removeRecipeFromCache(RecipeDetail recipeDetail);

	void refreshUnitMonkRecipeProfileVersion();

	void refreshUnitClosureStateMetadata();

	void refreshSingleUnitContacts(UnitContactDetailsData unitContactDetailsData);

	public Product getProduct(Integer productId);

	public void clearCustomerAppliedCouponCache();

	void refreshPriceCategoryWiseProductsPrice();

	void refreshWorkStationsStationCategories();

	public void updatePriceProfileStateMapForUnit(Integer unitId , Boolean value);

	public boolean refreshUnitDroolVersionMapping();

	public void refreshSpecialMilkVariantMap();

	public void refreshAllPriceProfileToProductCache();

	public Map<PriceProfileKey,Map<ProductDimensionKey, BigDecimal>> getPriceProfileMapFromDao();

	public void refreshCompanyBrandsMap();

	public void refreshUnitBrandMaps();
	
	public void refreshProductToWorStationMapping();

}
