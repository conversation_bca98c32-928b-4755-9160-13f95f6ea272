package com.stpl.tech.master.data.model;

import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.UnitProductMappingData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnitProductProfileContext {

    private MultipartFile file;
    private List<UnitProductProfileExcelData> excelData;
    private UnitProductUpdateType pageType;
    private Integer loggedInUser;
    
    // Maps for data lookup
    private Map<Integer, UnitDetail> unitDetailMap;
    private Map<Integer, ProductDetail> productDetailMap;
    private Map<Integer, RefLookup> dimensionMap;
    private Map<String, UnitProductMapping> unitProductMappingMap;
    private Map<String, List<RecipeDetail>> recipeDetailMap = new HashMap<>();
    
    // Logging and tracking
    private Map<String, List<String>> logEntries = new HashMap<>();
    private BulkUploadData bulkUploadData;
    private List<BulkUploadDataLog> bulkUploadDataLogList;

    // Lists for insert and update operations in bulk
    private List<UnitProductMapping> updateMappingList = new ArrayList<>();
    private List<UnitProductPricing> insertPricingList = new ArrayList<>();
    private List<UnitProductPricing> updatePricingList = new ArrayList<>();

    private List<UnitProductMappingData> mappings = new ArrayList<>();
    private Map<Integer, UnitProductMapping> upmMap = new HashMap<>();
    private Map<ProductDetail, List<UnitProductMappingData>> mapForSendingMail = new HashMap<>();
    private Map<ProductDetail, List<UnitProductProfileExcelData>> mapForSendingMailForBulkUpload = new HashMap<>();

    private ForkJoinPool forkJoinPool;


}
