package com.stpl.tech.master.core.external.banner.dao.impl;

import com.stpl.tech.master.core.external.banner.dao.BannerDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.BannerDetailData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;

@Repository
public class BannerDaoImpl extends AbstractMasterDaoImpl implements BannerDao {


    @Override
    public List<BannerDetailData> getActiveBannerListForDate(String dateString) {
        Date date = AppUtils.getDate(dateString, "yyyy-MM-dd");
        Query query = manager.createQuery("From BannerDetailData b where DATE(b.bannerActivationDate) <= :date " +
                "and DATE(b.bannerExpiryDate) >= :date and b.status = :status ");
        query.setParameter("date" , date);
        query.setParameter("status", "ACTIVE");
        List<BannerDetailData> bannerDetailData = query.getResultList();
        return bannerDetailData;
    }

    @Override
    public Boolean setStatus(int id, String status) {
      BannerDetailData bannerDetailData = manager.find(BannerDetailData.class,id);
      if(bannerDetailData!=null){
          System.out.println("updating status to "+status);
          bannerDetailData.setStatus(status.equalsIgnoreCase("ACTIVE") ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE);
          manager.flush();
          return true;
      }
        return false;
    }
}
