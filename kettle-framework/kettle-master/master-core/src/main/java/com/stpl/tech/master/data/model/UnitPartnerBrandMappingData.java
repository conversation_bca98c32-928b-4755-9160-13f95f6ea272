/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UNIT_PARTNER_BRAND_MAPPING")
public class UnitPartnerBrandMappingData implements java.io.Serializable {

    private static final long serialVersionUID = -9133952113962183323L;
    private Integer mappingId;
    private String restaurantId;
    private Integer unitId;
    private Integer partnerId;
    private Integer priceProfileUnitId;
    private Integer brandId;
    private String status;
    private Integer partnerSourceSystemId ;
    private String swiggyCloudKitchen;
    private Date liveDate;

    public UnitPartnerBrandMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "UNIT_PARTNER_BRAND_MAPPING_ID", unique = true, nullable = false)
    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    @Column(name = "RESTAURANT_ID", nullable = false)
    public String getRestaurantId() {
        return restaurantId;
    }

    public void setRestaurantId(String restaurantId) {
        this.restaurantId = restaurantId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }


    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "PARTNER_ID", nullable = false)
    public Integer getPartnerId() {
        return partnerId;
    }


    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    @Column(name = "PRICE_PROFILE_UNIT_ID", nullable = false)
    public Integer getPriceProfileUnitId() {
        return priceProfileUnitId;
    }

    public void setPriceProfileUnitId(Integer priceProfileUnitId) {
        this.priceProfileUnitId = priceProfileUnitId;
    }

    @Column(name = "BRAND_ID", nullable = false)
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "MAPPING_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "PARTNER_SOURCE_SYSTEM_ID", nullable = false)
    public Integer getPartnerSourceSystemId() {
        return partnerSourceSystemId;
    }

    public void setPartnerSourceSystemId(Integer partnerSourceSystemId) {
        this.partnerSourceSystemId = partnerSourceSystemId;
    }
    @Column(name="SWIGGY_CLOUD_KITCHEN")
    public String getSwiggyCloudKitchen() {
        return swiggyCloudKitchen;
    }
    public void setSwiggyCloudKitchen(String swiggyCloudKitchen) {
        this.swiggyCloudKitchen = swiggyCloudKitchen;
    }
    @Column(name = "LIVE_DATE")
    public Date getLiveDate() {
        return liveDate;
    }
    public void setLiveDate(Date liveDate) {
        this.liveDate = liveDate;
    }

    @Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((mappingId == null) ? 0 : mappingId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UnitPartnerBrandMappingData other = (UnitPartnerBrandMappingData) obj;
		if (mappingId == null) {
			if (other.mappingId != null)
				return false;
		} else if (!mappingId.equals(other.mappingId))
			return false;
		return true;
	}
    
    
}