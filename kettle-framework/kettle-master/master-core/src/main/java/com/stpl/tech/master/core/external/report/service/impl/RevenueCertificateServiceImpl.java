package com.stpl.tech.master.core.external.report.service.impl;

import com.stpl.tech.master.RevenueCertificateMapping;
import com.stpl.tech.master.core.AttributeDefinitionEnum;
import com.stpl.tech.master.core.external.report.dao.RevenueCertificateDao;
import com.stpl.tech.master.core.external.report.service.RevenueCertificateService;
import com.stpl.tech.master.data.dao.UnitManagementDao;
import com.stpl.tech.master.data.model.UnitAttributeMapping;
import com.stpl.tech.master.data.model.UnitDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class RevenueCertificateServiceImpl implements RevenueCertificateService {

    @Autowired
    RevenueCertificateDao dao;

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean saveEmail(Integer unitId, String toBeGenerated, String emailId) {
        return dao.saveEmailId(unitId,toBeGenerated,emailId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public RevenueCertificateMapping getEmail(Integer unitId) {
        RevenueCertificateMapping revenueCertificateMapping = new RevenueCertificateMapping();
        List<UnitAttributeMapping> mappings = dao.getEmailIdByUnitIdAndAttributeIds(unitId,List.of(17,18));
        if(Objects.nonNull(mappings)) {
            mappings.forEach(mapping -> {
                if (mapping.getAttributeId() == AttributeDefinitionEnum.REVENUE_CERTIFICATE_EMAIL.getId()) {
                    revenueCertificateMapping.setEmailId(mapping.getAttributeValue());
                } else if (mapping.getAttributeId() == AttributeDefinitionEnum.REVENUE_CERTIFICATE_GENERATION_ENABLE.getId()) {
                    String isGenerated = null;
                    if(mapping.getAttributeValue().equals("Y")){
                        isGenerated = "YES";
                    } else if (mapping.getAttributeValue().equals("N")) {
                        isGenerated = "NO";
                    }
                    revenueCertificateMapping.setToBeGenerated(isGenerated);
                    return;
                }
            });
        }
        return revenueCertificateMapping;
    }
}
