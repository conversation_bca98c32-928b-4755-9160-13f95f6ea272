package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG")
public class UnitProductPricingBulkUpdateLog {

    private Integer updateLogId;
    private UnitProductPricingBulkUpdateEvent eventId;
    private Integer unitProdPriceId;
    private BigDecimal oldPrice;
    private BigDecimal newPrice;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UPDATE_LOG_ID", unique = true, nullable = false)
    public Integer getUpdateLogId() {
        return updateLogId;
    }

    public void setUpdateLogId(Integer updateLogId) {
        this.updateLogId = updateLogId;
    }

    @ManyToOne
    @JoinColumn(name = "EVENT_ID", nullable = false)
    public UnitProductPricingBulkUpdateEvent getEventId() {
        return eventId;
    }

    public void setEventId(UnitProductPricingBulkUpdateEvent eventId) {
        this.eventId = eventId;
    }

    @Column(name = "UNIT_PROD_PRICE_ID")
    public Integer getUnitProdPriceId() {
        return unitProdPriceId;
    }

    public void setUnitProdPriceId(Integer unitProdPriceId) {
        this.unitProdPriceId = unitProdPriceId;
    }

    @Column(name = "OLD_PRICE")
    public BigDecimal getOldPrice() {
        return oldPrice;
    }

    public void setOldPrice(BigDecimal oldPrice) {
        this.oldPrice = oldPrice;
    }

    @Column(name = "NEW_PRICE")
    public BigDecimal getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(BigDecimal newPrice) {
        this.newPrice = newPrice;
    }

    @Override
    public String toString() {
        return "UnitProductPricingBulkUpdateLog{" +
                "updateLogId=" + updateLogId +
                ", eventId=" + eventId +
                ", unitProdPriceId=" + unitProdPriceId +
                ", oldPrice=" + oldPrice +
                ", newPrice=" + newPrice +
                '}';
    }
}
