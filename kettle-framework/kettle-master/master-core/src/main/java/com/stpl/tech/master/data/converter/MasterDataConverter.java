/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.converter;

import com.stpl.tech.kettle.report.metadata.model.ReportSummary;
import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import com.stpl.tech.kettle.report.metadata.model.WarningMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseValidation;
import com.stpl.tech.master.budget.metadata.model.ExpenseValidationType;
import com.stpl.tech.master.core.AttributeDefinitionEnum;
import com.stpl.tech.master.core.BrandAttributeEnum;
import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.model.UnitClosureFormDataDomain;
import com.stpl.tech.master.data.model.AccessControlListData;
import com.stpl.tech.master.data.model.AddonProductData;
import com.stpl.tech.master.data.model.AddressInfo;
import com.stpl.tech.master.data.model.AppOfferApplicabilityData;
import com.stpl.tech.master.data.model.AppOfferDetailData;
import com.stpl.tech.master.data.model.AppOfferMappingData;
import com.stpl.tech.master.data.model.ApplicationInstallationData;
import com.stpl.tech.master.data.model.BannerDetailData;
import com.stpl.tech.master.data.model.BrandAttributes;
import com.stpl.tech.master.data.model.BrandDetail;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.BusinessHours;
import com.stpl.tech.master.data.model.CampaignCouponMapping;
import com.stpl.tech.master.data.model.CampaignDetailData;
import com.stpl.tech.master.data.model.CancellationReasonData;
import com.stpl.tech.master.data.model.CashMetadataDetail;
import com.stpl.tech.master.data.model.CategoryAdditionalTaxData;
import com.stpl.tech.master.data.model.CategoryTaxData;
import com.stpl.tech.master.data.model.ChannelPartner;
import com.stpl.tech.master.data.model.ChannelPartnerCommission;
import com.stpl.tech.master.data.model.CompanyDetail;
import com.stpl.tech.master.data.model.ConfigAttributeDefinitionData;
import com.stpl.tech.master.data.model.ConfigAttributeValueData;
import com.stpl.tech.master.data.model.CountryDetail;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CouponDetailMappingData;
import com.stpl.tech.master.data.model.Denomination;
import com.stpl.tech.master.data.model.DocumentDetailData;
import com.stpl.tech.master.data.model.EmployeeApplicationMappingDetail;
import com.stpl.tech.master.data.model.EmployeeDetail;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.data.model.ExpenseMetadataDetail;
import com.stpl.tech.master.data.model.ExpenseValidationDetail;
import com.stpl.tech.master.data.model.ImageMapping;
import com.stpl.tech.master.data.model.KioskCompanyDetailsData;
import com.stpl.tech.master.data.model.KioskCompanyDomainData;
import com.stpl.tech.master.data.model.KioskLocationDetailsData;
import com.stpl.tech.master.data.model.KioskMachineDetailsData;
import com.stpl.tech.master.data.model.KioskOfficeDetailsData;
import com.stpl.tech.master.data.model.LocationDetail;
import com.stpl.tech.master.data.model.MarketingPartner;
import com.stpl.tech.master.data.model.MenuRecommendationData;
import com.stpl.tech.master.data.model.MenuSequenceData;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.MenuSequenceTimingData;
import com.stpl.tech.master.data.model.MonkAttrMetaData;
import com.stpl.tech.master.data.model.MonkConfigurationData;
import com.stpl.tech.master.data.model.OfferAccountCategory;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.OfferDetailMappingData;
import com.stpl.tech.master.data.model.OfferMetadata;
import com.stpl.tech.master.data.model.OfferPartner;
import com.stpl.tech.master.data.model.PreAuthenticatedApiData;
import com.stpl.tech.master.data.model.PriceProfile;
import com.stpl.tech.master.data.model.PriceProfileRangeValues;
import com.stpl.tech.master.data.model.ProductCondimentGroup;
import com.stpl.tech.master.data.model.ProductCondimentItem;
import com.stpl.tech.master.data.model.ProductDetail;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.ProductGroupImageData;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.data.model.ProductSequenceData;
import com.stpl.tech.master.data.model.ProductTagMappingDetail;
import com.stpl.tech.master.data.model.RefLookup;
import com.stpl.tech.master.data.model.RefLookupType;
import com.stpl.tech.master.data.model.StateDetail;
import com.stpl.tech.master.data.model.TaxCategoryData;
import com.stpl.tech.master.data.model.TaxProfileData;
import com.stpl.tech.master.data.model.TokenizedApiData;
import com.stpl.tech.master.data.model.UnitAttributeMapping;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.master.data.model.UnitClosureFormData;
import com.stpl.tech.master.data.model.UnitClosureFormMetaData;
import com.stpl.tech.master.data.model.UnitContactDetailsData;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPaymentModeMapping;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.data.model.UnitProductPricing;
import com.stpl.tech.master.data.model.UnitProductProfileContext;
import com.stpl.tech.master.data.model.UnitRestrictedApplicationData;
import com.stpl.tech.master.data.model.UnitTaxMapping;
import com.stpl.tech.master.data.model.UserRoleData;
import com.stpl.tech.master.data.model.WarningReasonDetail;
import com.stpl.tech.master.data.model.XMLReportDefinitionData;
import com.stpl.tech.master.data.model.XMLReportVersionData;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.master.enums.OfferApplicabilityFlag;
import com.stpl.tech.master.enums.OfferApplicable;
import com.stpl.tech.master.monk.configuration.model.MonkAttr;
import com.stpl.tech.master.monk.configuration.model.MonkConfiguration;
import com.stpl.tech.master.monk.configuration.model.MonkConfigurationScope;
import com.stpl.tech.master.monk.configuration.model.MonkConfigurationType;
import com.stpl.tech.master.monk.configuration.model.MonkConfigurationValue;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.master.tax.model.AdditionalTax;
import com.stpl.tech.master.tax.model.StateTax;
import com.stpl.tech.master.tax.model.TaxApplicability;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxInfo;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
@Slf4j
public class MasterDataConverter {

    @Autowired
    static
    MasterDataCache masterDataCache ;

    private static final MasterObjectFactory masterFactory = new MasterObjectFactory();

    public static Employee convert(EmployeeDetail details) {
        return convert(details, true);
    }


    public static  Employee convert(EmployeeDetail details, boolean getManager) {
        if (details == null) {
            return null;
        }
        Employee data = masterFactory.createEmployee();
        data.setId(details.getEmpId());
        data.setGender(details.getEmpGender());
        data.setEmploymentStatus(EmploymentStatus.fromValue(details.getEmploymentStatus()));
        data.setEmploymentType(EmploymentType.fromValue(details.getEmploymentType()));
        data.setName(details.getEmpName());
        data.setPrimaryContact(details.getEmpContactNum1());
        data.setSecondaryContact(details.getEmpContactNum2());
        data.setBiometricId(details.getBiometricIdentifier());
        data.setJoiningDate(details.getJoiningDate());
        if (getManager) {
            data.setReportingManager(convert(details.getReportingManager(), false));
        }
        data.setDepartment(convert(details.getDepartment(), true));

//        data.setDepartment(masterDataCache.getDepartment(details.getDepartmentId()));
        data.setDesignation(convert(details.getDesignation()));
//        data.setDesignation(masterDataCache.getDesignation(details.getDesignationId()));
        data.setCurrentAddress(convert(details.getAddressInfoByEmpCurrentAddr()));
        data.setPermanentAddress(convert(details.getAddressInfoByEmpPermanentAddr()));
        data.setEmployeeEmail(details.getEmployeeEmail());
        data.setApplications(ApplicationName.accessFor(data.getDesignation()));
        data.setEmployeeCode(details.getEmployeeCode());
        data.setCommunicationChannel(details.getCommunicationChannel());
        data.setEmployeeMealEligible(AppConstants.getValue(details.getEmployeeMealEligible()));
        data.setDob(details.getDob());
        data.setHrExecutive(details.getHrExecutive());
        data.setLeaveApprovalAuthority(details.getLeaveApprovalAuthority());
        data.setLocCode(details.getLocCode());
        data.setReasonForTermination(details.getReasonForTermination());
        if (Objects.nonNull(details.getUserPolicyData())) {
            data.setUserPolicyId(details.getUserPolicyData().getUserPolicyId());
        }
        if(Objects.nonNull(details.getCompanyDetail())){
            data.setCompany(convert(details.getCompanyDetail()));
        }
        data.setImageKey(details.getImageKey());
        return data;
    }

    public static Department convert(com.stpl.tech.master.data.model.Department department,
                                     boolean loadAllDesignations) {
        Department data = masterFactory.createDepartment();
        data.setId(department.getDeptId());
        data.setName(department.getDeptName());
        data.setDescription(department.getDeptDesc());
        BusinessDivision businessDivision = department.getBusinessDivision();
        try {
            data.setDivision(convert(businessDivision));
        }catch (Exception e){
            //cant set division
        }

        if (loadAllDesignations) {
            try {
                for (com.stpl.tech.master.data.model.Designation designation : department.getDesignations()) {
                    data.getDesignations().add(convert(designation));
                }
            }catch (Exception e){
                 //cant set designation
            }
        }
        return data;
    }

    public static Department convert(com.stpl.tech.master.data.model.Department department) {
        Department data = masterFactory.createDepartment();
        data.setId(department.getDeptId());
        data.setName(department.getDeptName());
        data.setDescription(department.getDeptDesc());
        BusinessDivision businessDivision = department.getBusinessDivision();
        try {
            data.setDivision(convert(businessDivision));
        }catch (Exception e){
            return data;
        }
        return data;
    }

    public static Designation convert(com.stpl.tech.master.data.model.Designation designation) {
        Designation data = masterFactory.createDesignation();
        data.setId(designation.getDesignationId());
        data.setName(designation.getDesignationName());
        data.setDescription(designation.getDesignationDesc());
        data.setAdminSystemAccess(AppConstants.getValue(designation.getAdminSystemAccess()));
        data.setAnalyticsSystemAccess(AppConstants.getValue(designation.getAnalyticsSystemAccess()));
        data.setClmSystemAccess(AppConstants.getValue(designation.getClmSystemAccess()));
        data.setCrmSystemAccess(AppConstants.getValue(designation.getCrmSystemAccess()));
        data.setTransactionSystemAccess(AppConstants.getValue(designation.getTransactionSystemAccess()));
        data.setScmSystemAccess(AppConstants.getValue(designation.getScmSystemAccess()));
        data.setFormsSystemAccess(AppConstants.getValue(designation.getFormsSystemAccess()));
        data.setChannelPartnerSystemAccess(AppConstants.getValue(designation.getChannelPartnerAccess()));
        data.setAppInstallerAccess(AppConstants.getValue(designation.getAppInstallerAccess()));
        data.setAttendanceAccess(AppConstants.getValue(designation.getAttendanceAccess()));
        data.setKnockApplicationAccess(AppConstants.getValue(designation.getKnockApplicationAccess()));
        //System.out.println("Designation Id " + designation.getDesignationId() + " Max Allowed Unit = "
        //+ designation.getMaxAllocatedUnits());
        data.setMaxAllocatedUnits(designation.getMaxAllocatedUnits() == null ? -1 : designation.getMaxAllocatedUnits());
        return data;
    }

    public static ListData convert(RefLookupType type, boolean getAll) {

        ListData data = masterFactory.createListData();
        if (type == null) {
            return data;
        }
        IdCodeName details = masterFactory.createIdCodeName();
        details.setId(type.getRtlId());
        details.setCode(type.getRtlCode());
        details.setName(type.getRtlName());
        details.setType(type.getRtlGroup());
        details.setStatus(type.getStatus());
        data.setDetail(details);
        for (RefLookup value : type.getRefLookups()) {
            if (getAll || value.getRlStatus().equals("ACTIVE")) {
                data.getContent().add(convert(value));
            }
        }
        return data;
    }

    public static ListData dummy(String type) {

        ListData data = masterFactory.createListData();
        data.setDetail(dummyIdCodeName(type));
        data.getContent().add(dummyIdCodeName(type + "-Value"));
        return data;
    }

    public static IdCodeName dummyIdCodeName(String type) {

        IdCodeName details = masterFactory.createIdCodeName();
        details.setId(1);
        details.setCode(type);
        details.setName(type);
        return details;
    }

    public static IdCodeName convert(RefLookup type) {

        IdCodeName details = masterFactory.createIdCodeName();
        if (type == null) {
            return details;
        }
        details.setId(type.getRlId());
        details.setName(type.getRlName());
        details.setCode(type.getRlCode());
        details.setShortCode(type.getRlShortCode());
        details.setStatus(type.getRlStatus());
        return details;

    }

    public static List<Product> convertToProducts(UnitDetail obj, boolean getAll , Map<ProductDimensionKey,BigDecimal> productPriceMap) {
        List<Product> products = new ArrayList<>();
        if (obj.getUnitProductMappings() != null) {
            Boolean pricingProfileEnabled = !CollectionUtils.isEmpty(productPriceMap);
            for (UnitProductMapping mapping : obj.getUnitProductMappings()) {
                if (ProductStatus.ACTIVE.name().equals(mapping.getProductStatus())
                    && ProductStatus.ACTIVE.name().equals(mapping.getProductDetail().getProductStatus())) {
                    products.add(convert(mapping.getProductDetail(), mapping.getUnitProductPricings(),
                        mapping.getProductStatus(),productPriceMap,pricingProfileEnabled));
                } else if (getAll) {
                    products.add(convert(mapping.getProductDetail(), mapping.getUnitProductPricings(),
                        mapping.getProductStatus(),productPriceMap,pricingProfileEnabled));
                }
            }
        }
        return products;
    }

    public static Map<Integer, List<ProductPrice>> convertToActiveProductPrices(UnitDetail obj , Map<ProductDimensionKey,BigDecimal> productPriceMap) {
        Map<Integer, List<ProductPrice>> map = new HashMap<>();
        if (obj.getUnitProductMappings() != null) {
            Boolean pricingProfileEnabled = !CollectionUtils.isEmpty(productPriceMap);
            for (UnitProductMapping mapping : obj.getUnitProductMappings()) {
                if (ProductStatus.ACTIVE.name().equals(mapping.getProductStatus())
                    && ProductStatus.ACTIVE.name().equals(mapping.getProductDetail().getProductStatus())) {
                    map.put(mapping.getProductDetail().getProductId(),
                         convert(mapping.getUnitProductPricings(), mapping.getProductDetail().getProductId(), productPriceMap,pricingProfileEnabled));
                }
            }
        }
        return map;
    }

//    public static ProductImageMappingDetail convert(List<ProductImageMapping> productImageMappingList, int productId) {
//        ProductImageMappingDetail data = masterFactory.createProductImageMappingDetail();
//        data.setProductId(productId);
//        for (ProductImageMapping mapping : productImageMappingList) {
//            ImageCategoryType categoryType = ImageCategoryType.valueOf(mapping.getImageType());
//            switch (categoryType) {
//                case SHOWCASE_VIDEO:
//                    data.setShowcaseVideo(mapping.getImageUrl());
//                    break;
//                case SPECIAL_LOW:
//                    data.setSpecialLow(mapping.getImageUrl());
//                    break;
//                case TRENDING_LOW:
//                    data.setTrendLow(mapping.getImageUrl());
//                    break;
//                case SPECIAL_HIGH:
//                    data.setSpecialHigh(mapping.getImageUrl());
//                    break;
//                case GRID_MENU_LOW:
//                    if (mapping.getIndex() == 1) {
//                        data.setGridLow(getIndexUrl(mapping));
//                        data.getGridLows().add(getIndexUrl(mapping));
////                        data.setGridLow(mapping.getImageUrl());
////                        data.getGridLows().add(mapping.getImageUrl());
//                    } else {
//                        data.getGridLows().add(getIndexUrl(mapping));
//                    }
//                    break;
//                case GRID_MENU_LOW_WEBP:
//                    if (mapping.getIndex() == 1) {
//                        data.setGridLowWebp(getIndexUrl(mapping));
//                        data.getGridLowsWebp().add(getIndexUrl(mapping));
//                    } else {
//                        data.getGridLowsWebp().add(getIndexUrl(mapping));
//                    }
//                    break;
//                case GRID_MENU_100X100:
//                    if(mapping.getIndex()==1){
//                        data.setGrid100X100(getIndexUrl(mapping));
//                        data.getGrids100X100().add(getIndexUrl(mapping));
//                    }else {
//                        data.getGrids100X100().add(getIndexUrl(mapping));
//                    }
//                    break;
//                case GRID_MENU_400X400:
//                    if(mapping.getIndex()==1){
//                        data.setGrid400X400(getIndexUrl(mapping));
//                        data.getGrids400X400().add(getIndexUrl(mapping));
//                    }else {
//                        data.getGrids400X400().add(getIndexUrl(mapping));
//                    }
//                    break;
//                case TRENDING_HIGH:
//                    data.setTrendHigh(mapping.getImageUrl());
//                    break;
//                case LIST_MENU_LOW:
//                    data.setListLow(mapping.getImageUrl());
//                    break;
//                case COMBO_GRID_LOW:
//                    data.setComboLow(mapping.getImageUrl());
//                    break;
//                case GRID_MENU_HIGH:
//                    data.setGridHigh(mapping.getImageUrl());
//                    break;
//                case LIST_MENU_HIGH:
//                    data.setListHigh(mapping.getImageUrl());
//                    break;
//                case COMBO_GRID_HIGH:
//                    data.setComboHigh(mapping.getImageUrl());
//                    break;
//
//                case MARKETING_IMAGE_MOB_VIEW:
//                    if(mapping.getIndex()==1){
//                        data.setMarketingImage(getIndexUrl(mapping));
//                        data.getMarketingImages().add(getIndexUrl(mapping));
//                    }else{
//                        data.getMarketingImages().add(getIndexUrl(mapping));
//                    }
//                    break;
//
//                case MARKETING_IMAGE_WEB_VIEW:
//                    if(mapping.getIndex()==1){
//                        data.setMarketingImageWebView(getIndexUrl(mapping));
//                        data.getMarketingImageWebViews().add(getIndexUrl(mapping));
//                    }else{
//                        data.getMarketingImageWebViews().add(getIndexUrl(mapping));
//                    }
//                case RECOMMENDATION_IMAGE_1200X1200:
//                    if(mapping.getIndex()==1){
//                        data.setRecommendationImage1200X1200(getIndexUrl(mapping));
//                        data.getRecommendationImages1200X1200().add(getIndexUrl(mapping));
//                    }else {
//                        data.getGrids400X400().add(getIndexUrl(mapping));
//                    }
//                    break;
//            }
//        }
//        return data;
//    }
public static ProductImageMappingDetail convert(List<? extends ImageMapping> imageMappings, int productId,Integer dimensionCode) {
    ProductImageMappingDetail data = masterFactory.createProductImageMappingDetail();
    data.setProductId(productId);
    data.setDimensionCode(dimensionCode);

    for (ImageMapping mapping : imageMappings) {
        ImageCategoryType categoryType = ImageCategoryType.valueOf(mapping.getImageType());
        switch (categoryType) {
            case SHOWCASE_VIDEO:
                data.setShowcaseVideo(mapping.getImageUrl());
                break;
            case SPECIAL_LOW:
                data.setSpecialLow(mapping.getImageUrl());
                break;
            case TRENDING_LOW:
                data.setTrendLow(mapping.getImageUrl());
                break;
            case SPECIAL_HIGH:
                data.setSpecialHigh(mapping.getImageUrl());
                break;
            case GRID_MENU_LOW:
                if (mapping.getIndex() == 1) {
                    data.setGridLow(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                    data.getGridLows().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                } else {
                    data.getGridLows().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                }
                break;
            case GRID_MENU_LOW_WEBP:
                if (mapping.getIndex() == 1) {
                    data.setGridLowWebp(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                    data.getGridLowsWebp().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                } else {
                    data.getGridLowsWebp().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                }
                break;
            case GRID_MENU_100X100:
                if (mapping.getIndex() == 1) {
                    data.setGrid100X100(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                    data.getGrids100X100().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                } else {
                    data.getGrids100X100().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                }
                break;
            case GRID_MENU_400X400:
                if (mapping.getIndex() == 1) {
                    data.setGrid400X400(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                    data.getGrids400X400().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                } else {
                    data.getGrids400X400().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                }
                break;
            case TRENDING_HIGH:
                data.setTrendHigh(mapping.getImageUrl());
                break;
            case LIST_MENU_LOW:
                data.setListLow(mapping.getImageUrl());
                break;
            case COMBO_GRID_LOW:
                data.setComboLow(mapping.getImageUrl());
                break;
            case GRID_MENU_HIGH:
                data.setGridHigh(mapping.getImageUrl());
                break;
            case LIST_MENU_HIGH:
                data.setListHigh(mapping.getImageUrl());
                break;
            case COMBO_GRID_HIGH:
                data.setComboHigh(mapping.getImageUrl());
                break;
            case MARKETING_IMAGE_MOB_VIEW:
                if (mapping.getIndex() == 1) {
                    data.setMarketingImage(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                    data.getMarketingImages().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                } else {
                    data.getMarketingImages().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                }
                break;
            case MARKETING_IMAGE_WEB_VIEW:
                if (mapping.getIndex() == 1) {
                    data.setMarketingImageWebView(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                    data.getMarketingImageWebViews().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                } else {
                    data.getMarketingImageWebViews().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                }
                break;
            case RECOMMENDATION_IMAGE_1200X1200:
                if (mapping.getIndex() == 1) {
                    data.setRecommendationImage1200X1200(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                    data.getRecommendationImages1200X1200().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                } else {
                    data.getRecommendationImages1200X1200().add(new IndexUrl(mapping.getIndex(), mapping.getImageUrl()));
                }
                break;
        }
    }

    return data;
}


    private static IndexUrl getIndexUrl(ProductImageMapping mapping) {
        return new IndexUrl(mapping.getIndex(), mapping.getImageUrl());
    }

    private static void setSweetnerAddons(Unit unit , MasterProperties props){
         try{
             unit.setSweetnerAddons(props.getSweetnerAddonsList(unit.getId()));
         }catch (Exception e){
             log.error("Error While Setting Sweetner Addons : {} " , e);
         }
    }

    public static Unit convert(UnitDetail obj, IdCodeName cafeManager, boolean getAll , MasterProperties props) {
        if (obj == null) {
            return null;
        }
        Unit data = masterFactory.createUnit();
        setAttributeMap(data, obj);
        data.setId(obj.getUnitId());
        setSweetnerAddons(data,props);
        data.setName(obj.getUnitName());
        if (obj.getShortName() != null) {
            data.setShortName(obj.getShortName());
        }
        if (obj.getProbableOpeningDate() != null){
            data.setProbableOpeningDate(obj.getProbableOpeningDate());
        }
        if (obj.getSalesClonedFrom() != null) {
            data.setSalesClonedFrom(obj.getSalesClonedFrom());
        }
        if (Objects.nonNull(obj.getF9Enabled())) {
            data.setF9Enabled(obj.getF9Enabled());
        }
        if(Objects.nonNull(obj.getIsClosed())){
            data.setClosed(AppUtils.getStatus(obj.getIsClosed()));
        }
        data.setRegion(obj.getUnitRegion());
        data.setStatus(UnitStatus.valueOf(obj.getUnitStatus()));
        if (obj.getCafeNeoStatus() != null) {
            data.setCafeNeoStatus(obj.getCafeNeoStatus());
        }
        if (obj.getCafeAppStatus() != null) {
            data.setCafeAppStatus(obj.getCafeAppStatus());
        }
        data.setFamily(UnitCategory.valueOf(obj.getUnitCategory()));
        data.setAddress(convert(obj.getAddressInfo()));
        data.setDivision(convert(obj.getBusinessDivision()));
        data.setUnitEmail(obj.getUnitEmail());
        data.setTin(obj.getGstin());
        data.setNoOfTerminals(obj.getNoOfTerminals());
        data.setNoOfTakeawayTerminals(obj.getNoOfTakeawayTerminals());
        data.setSubCategory(UnitSubCategory.valueOf(obj.getUnitSubCategory()));
        data.setCompany(convert(obj.getCompanyDetail()));
        data.setFssai(obj.getFssai());
        data.setShortCode(obj.getShortCode());
        if (obj.getCafeManager() != null) {
            data.setCafeManager(cafeManager);
        }
        EmployeeDetail manager = obj.getUnitManager();
        if (getAll) {
            data.setUnitManager(convert(manager));
        }
        if (manager != null) {
            data.setManagerId(manager.getEmpId());
            data.setManagerEmail(manager.getEmployeeEmail());
            data.setManagerChannel(manager.getCommunicationChannel());
            data.setManagerContact(manager.getEmpContactNum1());
            data.setManagerName(manager.getEmpName());
        }
        data.setReferenceName(obj.getReferenceName());
        data.setChannel(obj.getCommunicationChannel());
        data.setStartDate(obj.getStartDate());
        if(obj.getClonedFrom()!=null) {
            data.setCloneUnitId(obj.getClonedFrom());
        }
        data.setUnitBusinessType(obj.getBusinessType() != null ?
            UnitBusinessType.valueOf(obj.getBusinessType()) : UnitBusinessType.COCO);

        if (obj.getUnitTaxMappings() != null) {
            for (UnitTaxMapping mapping : obj.getUnitTaxMappings()) {
                data.getTaxProfiles().add(convert(mapping));
            }
        }

        if (obj.getUnitPaymentMappings() != null) {
            for (UnitPaymentModeMapping mapping : obj.getUnitPaymentMappings()) {
                if (AppConstants.ACTIVE.equals(mapping.getPaymentMode().getModeStatus())
                    && AppConstants.ACTIVE.equals(mapping.getMappingStatus())) {
                    data.getPaymentModes().add(mapping.getPaymentMode().getPaymentModeId());
                }
            }
        }

        if (obj.getBusinessHours() != null) {
            obj.getBusinessHours().forEach(businessHours -> {
                UnitHours unitHour = convert(businessHours);
                if(businessHours.getBrandId() == AppConstants.CHAAYOS_BRAND_ID){
                    data.getOperationalHours().add(unitHour);
                } else if (businessHours.getBrandId() == AppConstants.GNT_BRAND_ID) {
                    data.getGntOperationalHours().add(unitHour);
                }
                if(Objects.nonNull(unitHour.getDayOfTheWeekNumber()) && businessHours.getDayOfTheWeekNumber()==2 &&
                        businessHours.getBrandId() == AppConstants.CHAAYOS_BRAND_ID){
                    Time openingTime = unitHour.getDineInOpeningTime();
                    Time closingTime = unitHour.getDineInClosingTime();
                    if(Objects.nonNull(openingTime) && Objects.nonNull(closingTime)) {
                        CafeTimingTrimmedData cafeTimingTrimmedData = new CafeTimingTrimmedData();
                        boolean isOpen24X7 = AppUtils.isCafeOpen24x7(openingTime, closingTime);
                        cafeTimingTrimmedData.setIscafe24x7(isOpen24X7);
                        if (!isOpen24X7) {
                            String closingMeridiem = Integer.parseInt(AppUtils.convertToHourMinute(closingTime).substring(0, 2)) < 12 ? "AM" : "PM";
                            String timeString = AppUtils.convertToHourMinute(openingTime) + "AM TO " + AppUtils.convertToHourMinute(closingTime) + closingMeridiem;
                            CafeTimings cafeTimings = new CafeTimings();
                            cafeTimings.setId(businessHours.getBrandId());
                            cafeTimings.setTimingString(timeString);
                            cafeTimingTrimmedData.setDineInTimings(new HashMap<>());
                            cafeTimingTrimmedData.getDineInTimings().put(businessHours.getBrandId(), cafeTimings);
                        }
                        data.setCafeTimingTrimmedData(cafeTimingTrimmedData);
                    }
                }
            });
        }
        data.setCreditAccount(obj.getCreditAccountId());
        data.setLive(AppConstants.getValue(obj.getIsLive()));
        data.setHandoverDate(obj.getHandoverDate());
        if (obj.getFssai() != null) {
            data.setFssai(obj.getFssai());
        }
        if (obj.getCostCenter() != null) {
            data.setCostCenterName(obj.getCostCenter());
        }
        setLocation(data, obj);
        if(obj.getPricingProfile() != null) {
            data.setPricingProfile(obj.getPricingProfile());
        }
        if(Objects.nonNull(obj.getUnitZone())){
            data.setUnitZone(obj.getUnitZone());
        }
        data.setPosVersion(obj.getPosVersion());
        data.setFaDaycloseEnabled(obj.getFaDaycloseEnabled());
        if(Objects.nonNull(obj.getUnitCafeManager())){
            data.setUnitCafeManager(obj.getUnitCafeManager());
        }
        if(Objects.nonNull(obj.getLastHandoverDate())){
            data.setLastHandoverDate(obj.getLastHandoverDate());
        }
        if(Objects.nonNull(obj.getLastHandoverFrom())){
            data.setLastHandoverFrom(obj.getLastHandoverFrom());
        }
        if(Objects.nonNull(obj.getVarianceAcknowledgementData())){
            data.setVarianceAcknowledgementRequired(obj.getVarianceAcknowledgementData());
        }
        if(Objects.nonNull(obj.getIsHotSpotLive())){
            if(AppConstants.YES.equalsIgnoreCase(obj.getIsHotSpotLive()))
                data.setHotspotEnabled(true);
            if(AppConstants.NO.equalsIgnoreCase(obj.getIsHotSpotLive()))
                data.setHotspotEnabled(false);
        }

        if(Objects.nonNull(obj.getAssemblyStrictMode())){
            if(AppConstants.YES.equalsIgnoreCase(obj.getAssemblyStrictMode()))
                data.setAssemblyStrictMode(true);
            if(AppConstants.NO.equalsIgnoreCase(obj.getAssemblyStrictMode()))
                data.setAssemblyStrictMode(false);
        }
        else{
            data.setAssemblyStrictMode(false);
        }
        if (Objects.nonNull(obj.getClosureStatus())) {
            data.setClosure(obj.getClosureStatus());
        }

        if(Objects.nonNull(obj.getAssemblyOtpMode())){
            if(AppConstants.YES.equalsIgnoreCase(obj.getAssemblyOtpMode()))
                data.setAssemblyOtpMode(true);
            if(AppConstants.NO.equalsIgnoreCase(obj.getAssemblyOtpMode()))
                data.setAssemblyOtpMode(false);
        }
        else{
            data.setAssemblyOtpMode(false);
        }
        if(Objects.nonNull(obj.getCustomerLogin())){
            data.setCustomerLogin(AppUtils.getStatus(obj.getCustomerLogin()));
        }
        if(Objects.nonNull(data.getServiceCharge())){
            data.setServiceChargeDesc(AppConstants.SERVICE_CHARGE_DESCRIPTION);
        }
        return data;
    }

    /**
     * @param obj
     * @return
     */
    private static void setAttributeMap(Unit data, UnitDetail obj) {
        List<String> availableAttributes = Arrays.stream(AttributeDefinitionEnum.values()).map(AttributeDefinitionEnum::name).toList();
        for (UnitAttributeMapping mapping : obj.getUnitAttributeMappings()) {
            if (!availableAttributes.contains(mapping.getAttributeCode())) {
                log.info("FOr Unit Id : {} , in Attribute Definition Enum can not find attribute : {} so ignoring", obj.getUnitId(), mapping.getAttributeCode());
                continue;
            }
            AttributeDefinitionEnum def = AttributeDefinitionEnum.valueOf(mapping.getAttributeCode());
            String value = mapping.getAttributeValue();
            if (def.equals(AttributeDefinitionEnum.WORKSTATION_ENABLED)) {
                data.setWorkstationEnabled(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.FREE_INTERNET_ACCESS)) {
                data.setFreeInternetAccess(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.HAS_TABLE_SERVICE)) {
                data.setTableService(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.TABLE_SERVICE_TYPE)) {
                data.setTableServiceType(Integer.valueOf(value));
            } else if (def.equals(AttributeDefinitionEnum.NO_OF_TABLES)) {
                data.setNoOfTables(Integer.valueOf(value));
            } else if (def.equals(AttributeDefinitionEnum.TRUE_CALLER_ONBOARDING)) {
                TrueCallerSettings trueCallerSettings = value == null ? TrueCallerSettings.DEFAULT
                    : TrueCallerSettings.valueOf(value);
                data.setTrueCallerEnabled(trueCallerSettings);
            } else if (def.equals(AttributeDefinitionEnum.IS_PARTNER_PRICED)) {
                data.setPartnerPriced(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.IS_TOKEN_ENABLED)) {
                data.setTokenEnabled(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.TOKEN_LIMIT)) {
                data.setTokenLimit(Integer.valueOf(value));
            } else if (def.equals(AttributeDefinitionEnum.ELECTRICITY_METER_COUNT)) {
                data.setNoOfMeter(Integer.valueOf(value));
            } else if (def.equals(AttributeDefinitionEnum.IS_DG_AVAILABLE)) {
                data.setdGAvailable(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.HOT_N_COLD_MERGED)) {
                data.setHotAndColdMerged(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.LIVE_INVENTORY_ENABLED)) {
                data.setLiveInventoryEnabled(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.PACKAGING_TYPE)) {
                data.setPackagingType(value);
            } else if (def.equals(AttributeDefinitionEnum.PACKAGING_VALUE)) {
                data.setPackagingValue(new BigDecimal(value));
            } else if (def.equals(AttributeDefinitionEnum.GOOGLE_MERCHANT_ID)) {
                data.setGoogleMerchantId(value == null ? "" : value);
            } else if (def.equals(AttributeDefinitionEnum.REVENUE_CERTIFICATE_EMAIL)) {
                data.setRevenueCertificateEmail(value);
            } else if (def.equals(AttributeDefinitionEnum.REVENUE_CERTIFICATE_GENERATION_ENABLE)) {
                data.setRevenueCertificateGenerationEnable(AppConstants.getValue(value));
            }else if (def.equals(AttributeDefinitionEnum.VARIANCE_ACK_EMP_ID)) {
                data.setVarianceAcknowledgementEmployees(value);
            } else if (def.equals(AttributeDefinitionEnum.REAL_TIME_SALE_PUSH)) {
                data.setPublishRealTimeSale(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.PARTNER_TO_PUSH_SALE)) {
                data.setPartnerToPublishSale(value);
            }else if(def.equals(AttributeDefinitionEnum.LOYALTY_REDEMPTION_ALLOWED)){
                data.setLoyalTeaRedemptionAllowed(value);
            }else if(def.equals(AttributeDefinitionEnum.AUTO_EDC_PAYMENT)){
                if(Objects.isNull(value)){
                    data.setAutoEdcPayment(AppConstants.YES);
                }else{
                    data.setAutoEdcPayment(value);
                }
            } else if (def.equals(AttributeDefinitionEnum.MONK_PRICE_PROFILE)) {
                data.setMonkRecipeProfile(value);
            } else if (def.equals(AttributeDefinitionEnum.NO_OF_MONK_NEEDED)) {
                data.setNoOfMonksNeeded(Integer.valueOf(value));
            }else if(def.equals(AttributeDefinitionEnum.LOYALTY_BURN_SWIGGY_ALLOWED)){
                data.setLoyalTeaBurnSwiggyAllowed(value);
            }else if (def.equals(AttributeDefinitionEnum.MILK_TRACKING)) {
                data.setMilkTrackingEnabled(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.SHOW_LOYALTY_SCREEN)) {
                data.setShowLoyalteaScreen(value);
            } else if(def.equals(AttributeDefinitionEnum.OTP_VIA_EMAIL)){
                data.setIsOtpViaEmail(AppConstants.getValue(value));
            }else if(def.equals(AttributeDefinitionEnum.SUPERU_ENABLED)){
                data.setIsSuperUEnabled(value);
            }else if(def.equals(AttributeDefinitionEnum.CHECK_BIG_PAN_THRESHOLD)){
                data.setCheckBigPanThreshold(value);
            }else if(def.equals(AttributeDefinitionEnum.AUTO_TOKEN_ENABLED)){
                data.setAutoTokenEnabled(AppConstants.getValue(value));
            }else if(def.equals(AttributeDefinitionEnum.SERVICE_CHARGE_VALUE)){
                data.setServiceCharge(new BigDecimal(value));
            }else if (def.equals(AttributeDefinitionEnum.SERVICE_CHARGE_POS_ENABLED)) {
                data.setServiceChargePosEnabled(AppConstants.getValue(value));
            }else if (def.equals(AttributeDefinitionEnum.SERVICE_CHARGE_APP_ENABLED)) {
                data.setServiceChargeAppEnabled(AppConstants.getValue(value));
            }else if(def.equals(AttributeDefinitionEnum.IS_TESTING_UNIT)) {
                data.setIsTestingUnit(AppConstants.getValue(value));
            }else if(def.equals(AttributeDefinitionEnum.CAFE_SERVICE_TYPE)){
                CafeType cafe = getCafeTypeFromValue(value);
                String orderingType;
                if (value == null || value.trim().isEmpty()) {
                    orderingType=AppConstants.CAFE;
                }else{
                    orderingType=value.toUpperCase();
                }
                data.setOrderingType(orderingType);
                data.setDineIn(cafe.isDineIn());
                data.setTableSelection(cafe.isTableSelection());
                data.setTokenSelection(cafe.isTokenSelection());
                data.setTakeAway(cafe.isTakeAway());
                data.setCafeType(cafe);
            } else if (def.equals(AttributeDefinitionEnum.CUSTOM_ADDONS_LIMIT)) {
                data.setCustomAddonsLimit(Integer.valueOf(value));
            } else if (def.equals(AttributeDefinitionEnum.FEEDBACK_FORM_POS_ENABLED)){
                data.setFeedbackFormEnabledForPos(AppConstants.getValue(value));
            } else if (def.equals(AttributeDefinitionEnum.DREAM_FOLKS_OUTLET_ID)){
                data.setDreamfolksOutletId(value);
            }
        }
        for (AttributeDefinitionEnum attEnum : AttributeDefinitionEnum.values()) {
            boolean setDefault = true;
            for (UnitAttributeMapping mapping : obj.getUnitAttributeMappings()) {
                if (!availableAttributes.contains(mapping.getAttributeCode())) {
                    continue;
                }
                if (attEnum.equals(AttributeDefinitionEnum.valueOf(mapping.getAttributeCode()))) {
                    setDefault = false;
                }
            }
            if (setDefault && attEnum.equals(AttributeDefinitionEnum.PACKAGING_TYPE)) {
                data.setPackagingType(attEnum.getDefaultValue().toString());
            }
            if (setDefault && attEnum.equals(AttributeDefinitionEnum.PACKAGING_VALUE)) {
                data.setPackagingValue(new BigDecimal(attEnum.getDefaultValue().toString()));
            }
        }
    }

    /**
     * @param data
     */
    private static void setLocation(Unit data, UnitDetail obj) {
        LocationDetail location = obj.getLocation();
        if (location == null) {
            return;
        }
        Location loc = new Location();
        loc.setCode(location.getCityCode());
        loc.setId(location.getId());
        loc.setName(location.getCity());
        loc.setBusiness(AppConstants.getValue(location.getFunctionalFlag()));
        StateDetail stateDetail = location.getState();
        State state = new State();
        if (stateDetail != null) {
            state.setCode(location.getState().getStateCode());
            state.setId(location.getState().getId());
            state.setName(location.getState().getState());
            state.setUt(AppConstants.getValue(location.getState().getIsUt()));
            state.setBusiness(AppConstants.getValue(location.getState().getFunctionalFlag()));
            Country country = new Country();
            CountryDetail countryDetail = location.getState().getCountry();
            if (countryDetail != null) {
                country.setId(countryDetail.getId());
                country.setCode(countryDetail.getCountryCode());
                country.setName(countryDetail.getCountry());
            }
            state.setCountry(country);
            loc.setState(state);
        }
        data.setLocation(loc);
    }

    public static UnitHours convert(BusinessHours data) {

        UnitHours hours = new UnitHours();
        hours.setUnitId(data.getUnitDetail().getUnitId());
        hours.setId(data.getBusinessHoursId());
        hours.setDayOfTheWeek(data.getDayOfTheWeek());
        hours.setDayOfTheWeekNumber(data.getDayOfTheWeekNumber());
        hours.setDeliveryClosingTime(convert(data.getDeliveryClosingTime()));
        hours.setDeliveryOpeningTime(convert(data.getDeliveryOpeningTime()));
        hours.setDineInClosingTime(convert(data.getDineInClosingTime()));
        hours.setDineInOpeningTime(convert(data.getDineInOpeningTime()));
        hours.setHasDelivery(AppConstants.getValue(data.getHasDelivery()));
        hours.setHasDineIn(AppConstants.getValue(data.getHasDineIn()));
        hours.setHasTakeAway(AppConstants.getValue(data.getHasTakeAway()));
        hours.setIsOperational(AppConstants.getValue(data.getIsOperational()));
        hours.setNoOfShifts(data.getNoOfShifts());
        hours.setShiftOneHandoverTime(convert(data.getShiftOneHandoverTime()));
        hours.setShiftTwoHandoverTime(convert(data.getShiftTwoHandoverTime()));
        hours.setStatus(data.getStatus());
        hours.setTakeAwayClosingTime(convert(data.getTakeAwayClosingTime()));
        hours.setTakeAwayOpeningTime(convert(data.getTakeAwayOpeningTime()));
        return hours;

    }

    private static Time convert(Date date) {
        return date == null ? null : new Time(date.getTime());
    }

    public static Division convert(BusinessDivision obj) {
        Division div = masterFactory.createDivision();
        div.setId(obj.getBusinessDivId());
        div.setName(obj.getBusinessDivName());
        div.setCategory(obj.getBusienssDivCategory());
        div.setDescription(obj.getBusienssDivDesc());
        // div.setCompany(convert(obj.getCompanyDetail()));
        return div;
    }

    public static Company convert(CompanyDetail companyDetail) {
        Company com = masterFactory.createCompany();
        com.setId(companyDetail.getCompanyId());
        com.setName(companyDetail.getCompanyName());
        com.setDescription(companyDetail.getCompanyDescription());
        com.setCin(companyDetail.getCin());
        com.setServiceTaxNumber(companyDetail.getServiceTaxNo());
        com.setWebsiteAddress(companyDetail.getWebsiteAddr());
        com.setRegisteredAddress(convert(companyDetail.getAddressInfo()));
        com.setShortCode(companyDetail.getShortCode());
        return com;
    }


    public static Address convert(AddressInfo obj) {
        Address add = masterFactory.createAddress();
        add.setId(obj.getAddressId());
        add.setLine1(obj.getAddressLine1());
        add.setLine2(obj.getAddressLine2());
        add.setLine3(obj.getAddressLine3());
        add.setCity(obj.getCity());
        add.setState(obj.getState());
        add.setCountry(obj.getCountry());
        add.setZipCode(obj.getZipcode());
        add.setContact1(obj.getContactNum1());
        add.setContact2(obj.getContactNum2());
        add.setAddressType(obj.getAddressType());
        add.setLatitude(obj.getLatitude());
        add.setLongitude(obj.getLongitude());
        return add;
    }

    public static Product convert(ProductDetail obj) {
        Product data = masterFactory.createProduct();
        data.setId(obj.getProductId());
        data.setName(obj.getProductName());
        data.setDescription(obj.getProductDescription());
        data.setType(obj.getProductType().getRtlId());
        data.setSubType(obj.getProductSubType().getRlId());
        if (Objects.nonNull(obj.getStationCategory())) {
            data.setStationCategory(obj.getStationCategory().getRlId());
            data.setStationCategoryName(obj.getStationCategory().getRlName());
        }
        if (obj.getWebType() != null && obj.getWebType().getRlId() != null) {
            data.setWebType(obj.getWebType().getRlId());
        }
        data.setAttribute(obj.getAttribute());
        data.setSkuCode(obj.getProductSkuCode());
        data.setInventoryTracked(AppConstants.getValue(obj.getIsInventoryTracked()));
        data.setEmployeeMealComponent(AppConstants.getValue(obj.getEmployeeMealComponent()));
        data.setTaxableCogs(AppConstants.getValue(obj.getTaxableCogs()));
        data.setHasAddons(obj.getAddonTypes() != null);
        data.setHasSizeProfile(
            obj.getDimensionCode() != null && obj.getDimensionCode().getRtlId() != AppConstants.NO_DIMENSION_CODE);
        data.setBillType(BillType.fromValue(obj.getPriceType()));
        data.setClassification(ProductClassification.valueOf(obj.getClassification()));
        data.setShortCode(obj.getShortCode());
        data.setAddOnProfile(obj.getAddonTypes() != null ? obj.getAddonTypes().getRtlCode() : null);
        data.setStartDate(obj.getProductStartDate());
        data.setEndDate(obj.getProductEndDate());
        data.setSupportsVariantLevelOrdering(AppUtils.getStatus(obj.getSupportsVariantLevelOrdering()));
        data.setStatus(ProductStatus.valueOf(obj.getProductStatus()));
        data.setDimensionProfileId(obj.getDimensionCode().getRtlId());
        data.setTaxCode(obj.getTaxCode());
        data.setPreparation(obj.getPreparationMode());
        data.setPrepTime(obj.getPrepTime());
        data.setBrandId(obj.getBrandId());
        data.setUpdatedBy(Objects.nonNull(obj.getUpdatedBy()) ? obj.getUpdatedBy() : null);
        data.setLastUpdateTime(Objects.nonNull(obj.getLastUpdateTime()) ? obj.getLastUpdateTime() : null);
        if(!StringUtils.isEmpty(obj.getInventoryTrackedLevel())){
            data.setInventoryTrackedLevel(obj.getInventoryTrackedLevel());
        }
        getMappingTags(obj,data,null);
        data.setServiceChargeApplicable(obj.getServiceChargeApplicable());
        return data;
    }

    public static Product convert(ProductDetail obj, List<UnitProductPricing> pricings, String productStatus
    , Map<ProductDimensionKey,BigDecimal> productPriceMap , Boolean priceProfileEnabled) {
        Product data = convert(obj);
        data.setStatus(ProductStatus.valueOf(productStatus));
        if (pricings != null && pricings.size() > 0) {
            for (UnitProductPricing pricing : pricings) {
                ProductDimensionKey dimensionKey = ProductDimensionKey.builder().productId(obj.getProductId()).dimension(pricing.getRefLookup().getRlCode()).build();
                if (AppConstants.ACTIVE.equalsIgnoreCase(pricing.getStatus()) &&
                        (Boolean.FALSE.equals(priceProfileEnabled) || productPriceMap.containsKey(dimensionKey))) {
                    data.getPrices().add(convert(pricing,obj.getProductId(),productPriceMap,priceProfileEnabled));
                }
            }
        }
        return data;
    }

    public static List<ProductPrice> convert(List<UnitProductPricing> pricings, Integer productId ,
                                             Map<ProductDimensionKey,BigDecimal> productPriceMap , Boolean pricingProfileEnabled) {
        List<ProductPrice> list = new ArrayList<ProductPrice>();
        if (pricings != null && pricings.size() > 0) {
            for (UnitProductPricing pricing : pricings) {
                ProductDimensionKey productDimensionKey = ProductDimensionKey.builder().
                        productId(productId).dimension(pricing.getRefLookup().getRlCode()).build();
                if (AppConstants.ACTIVE.equalsIgnoreCase(pricing.getStatus()) &&
                        (Boolean.FALSE.equals(pricingProfileEnabled) || productPriceMap.containsKey(productDimensionKey))) {
                    list.add(convert(pricing,productId,productPriceMap,pricingProfileEnabled));
                }
            }
        }
        return list;
    }

    /**
     * Converting price Mapping to a particular product price object Id of product
     * price is the Id of the product
     *
     * @param pricing
     * @return
     */
    public static ProductPrice convert(UnitProductPricing pricing , Integer productId ,
                                       Map<ProductDimensionKey,BigDecimal> productPriceMap , Boolean priceProfileEnabled) {
        ProductPrice price = masterFactory.createProductPrice();
        ProductDimensionKey dimensionKey = ProductDimensionKey.builder().productId(productId).dimension(pricing.getRefLookup().getRlCode()).build();
        price.setId(pricing.getUnitProductMapping().getProductDetail().getProductId());
        price.setDimension(pricing.getRefLookup().getRlCode());
        if(Boolean.TRUE.equals(priceProfileEnabled)){
            price.setPrice(productPriceMap.get(dimensionKey));
        }else{
            price.setPrice(pricing.getPrice());
        }
        price.setCost(pricing.getCost());
        price.setCodCost(pricing.getCodCost());
        price.setBuffer(pricing.getBuffer());
        price.setThreshold(pricing.getThreshold());
        price.setProfile(pricing.getRecipeProfile());
        price.setStatus(pricing.getStatus());
        if (pricing.getAliasProductName() != null) {
            price.setAliasProductName(pricing.getAliasProductName());
        }
        if (pricing.getDimensionDescriptor() != null) {
            price.setDimensionDescriptor(pricing.getDimensionDescriptor());
        }
        price.setIsDeliveryOnlyProduct(AppUtils.getStatus(pricing.getIsDeliveryOnlyProduct()));
        price.setPickDineInConsumables(AppUtils.getStatus(pricing.getPickDineInConsumables()));
        return price;
    }

    public static ProductPrice convert(UnitProductPricing pricing , Integer productId) {
        ProductPrice price = masterFactory.createProductPrice();
        price.setId(pricing.getUnitProductMapping().getProductDetail().getProductId());
        price.setDimension(pricing.getRefLookup().getRlCode());
        price.setPrice(pricing.getPrice());
        price.setCost(pricing.getCost());
        price.setCodCost(pricing.getCodCost());
        price.setBuffer(pricing.getBuffer());
        price.setThreshold(pricing.getThreshold());
        price.setProfile(pricing.getRecipeProfile());
        price.setStatus(pricing.getStatus());
        if (pricing.getAliasProductName() != null) {
            price.setAliasProductName(pricing.getAliasProductName());
        }
        if (pricing.getDimensionDescriptor() != null) {
            price.setDimensionDescriptor(pricing.getDimensionDescriptor());
        }
        price.setIsDeliveryOnlyProduct(AppUtils.getStatus(pricing.getIsDeliveryOnlyProduct()));
        price.setPickDineInConsumables(AppUtils.getStatus(pricing.getPickDineInConsumables()));
        return price;
    }

    public static ProductBasicDetail convertToProductBasicDetail(ProductDetail product) {
        IdCodeName p = new IdCodeName();
        p.setId(product.getProductId());
        p.setName(product.getProductName());
        p.setCode(product.getProductName());
        p.setShortCode(product.getShortCode());
        ProductBasicDetail pbd = new ProductBasicDetail();
        pbd.setDetail(p);
        pbd.setInventoryTracked(AppConstants.getValue(product.getIsInventoryTracked()));
        pbd.setEmployeeMealComponent(AppConstants.getValue(product.getEmployeeMealComponent()));
        pbd.setType(product.getProductType().getRtlId());
        pbd.setSubType(product.getProductSubType().getRlId());
        if (Objects.nonNull(product.getStationCategory())) {
            pbd.setStationCategory(product.getStationCategory().getRlId());
            pbd.setStationCategoryName(product.getStationCategory().getRlName());
        }
        pbd.setClassification(ProductClassification.valueOf(product.getClassification()));
        pbd.setWebType(product.getWebType().getRlId());
        getMappingTags(product,null,pbd);
        pbd.setStatus(ProductStatus.valueOf(product.getProductStatus()));
        pbd.setCode(product.getTaxCode());
        return pbd;
    }

    public static ProductBasicDetail convertToProductBasicDetail(Product product) {
        IdCodeName p = new IdCodeName();
        p.setId(product.getId());
        p.setName(product.getName());
        p.setCode(product.getName());
        p.setShortCode(product.getShortCode());
        ProductBasicDetail pbd = new ProductBasicDetail();
        pbd.setDetail(p);
        pbd.setInventoryTracked(product.isInventoryTracked());
        pbd.setType(product.getType());
        pbd.setSubType(product.getSubType());
        pbd.setClassification(product.getClassification());
        pbd.setWebType(product.getWebType());
        pbd.setStatus(product.getStatus());
        pbd.setCode(product.getTaxCode());
        return pbd;
    }

    public static PaymentMode convert(com.stpl.tech.master.data.model.PaymentMode paymentMode) {
        PaymentMode mode = masterFactory.createPaymentMode();
        mode.setDescription(paymentMode.getModeDescription());
        mode.setId(paymentMode.getPaymentModeId());
        mode.setName(paymentMode.getModeName());
        mode.setType(paymentMode.getModeType());
        mode.setSettlementType(paymentMode.getSettlementType());
        mode.setStatus(paymentMode.getModeStatus());
        mode.setLedgerName(paymentMode.getLedgerName());
        mode.setGeneratePull(paymentMode.isGeneratePull());
        mode.setAutoCloseTransfer(AppConstants.getValue(paymentMode.getAutomaticCloseTransfer()));
        mode.setAutoTransfer(AppConstants.getValue(paymentMode.getAutomaticTransfer()));
        mode.setAutoPullValidate(AppConstants.getValue(paymentMode.getAutomaticPullValidate()));
        mode.setEditable(AppConstants.getValue(paymentMode.getEditable()));
        mode.setApplicableOnDiscountedOrders(AppConstants.getValue(paymentMode.getApplicableOnDiscountedOrders()));
        mode.setNeedsSettlementSlip(AppConstants.getValue(paymentMode.getNeedsSettlementSlip()));
        mode.setValidationSource(paymentMode.getValidationSource());
        mode.setCategory(PaymentCategory.valueOf(paymentMode.getModeCategory()));
        mode.setCommissionRate(paymentMode.getCommissionRate());
        if (paymentMode.getDenominations() != null && paymentMode.getDenominations().size() > 0) {
            for (Denomination denom : paymentMode.getDenominations()) {
                mode.getDenominations().add(convert(denom));
            }
        }
        return mode;
    }
    public static com.stpl.tech.master.data.model.PaymentMode convert(PaymentMode paymentMode) {
        com.stpl.tech.master.data.model.PaymentMode mode = new com.stpl.tech.master.data.model.PaymentMode();
        mode.setModeDescription(paymentMode.getDescription());
        mode.setPaymentModeId(paymentMode.getId());
        mode.setModeName(paymentMode.getName());
        mode.setModeType(paymentMode.getType());
        mode.setModeStatus(paymentMode.getStatus());
        mode.setSettlementType(paymentMode.getSettlementType());
        mode.setGeneratePull(paymentMode.isGeneratePull());
        mode.setAutomaticCloseTransfer(paymentMode.isAutoCloseTransfer() ? "Y":"N");
        mode.setAutomaticTransfer(paymentMode.isAutoTransfer() ? "Y":"N");
        mode.setAutomaticPullValidate(paymentMode.isAutoPullValidate() ? "Y":"N");
        mode.setEditable(paymentMode.isEditable() ? "Y":"N");
        mode.setApplicableOnDiscountedOrders(paymentMode.isApplicableOnDiscountedOrders() ? "Y" : "N");
        mode.setNeedsSettlementSlip(paymentMode.isNeedsSettlementSlip() ? "Y":"N");
        mode.setValidationSource(paymentMode.getValidationSource());
        mode.setModeCategory(paymentMode.getCategory().value());
        mode.setLedgerName(paymentMode.getLedgerName());
        mode.setCommissionRate(paymentMode.getCommissionRate());
//        if (paymentMode.getDenominations() != null && paymentMode.getDenominations().size() > 0) {
//            for (Denomination denom : paymentMode.getDenominations()) {
//                mode.getDenominations().add(convert(denom));
//            }
//        }
        return mode;
    }
    public static AddonList convert(ListData data, Map<Integer, AddonProductData> map) {
        AddonList list = masterFactory.createAddonList();
        list.setDetail(data.getDetail());
        for (IdCodeName content : data.getContent()) {
            list.getContent().add(convert(content, map.get(content.getId())));
        }
        return list;

    }

    public static AddonData convert(IdCodeName data, AddonProductData addonProductData) {
        AddonData addonData = masterFactory.createAddonData();
        addonData.setId(data.getId());
        addonData.setCode(data.getCode());
        addonData.setName(data.getName());
        addonData.setShortCode(data.getShortCode());
        if (addonProductData != null) {
            addonData.setLinkedProductId(addonProductData.getProductId());
        }
        return addonData;
    }

    public static AppOfferDetail convert(AppOfferDetailData appOfferDetailData, List<AppOfferMappingData> mappingDataList) {
        AppOfferDetail offer = new AppOfferDetail();
        offer.setAppOfferId(appOfferDetailData.getId());
        offer.setMov(appOfferDetailData.getMov());
        offer.setOfferValue(appOfferDetailData.getOfferValue());
        offer.setOfferCategory(appOfferDetailData.getOfferCategory());
        offer.setMaxDiscount(appOfferDetailData.getMaxDiscount());
        offer.setOfferOrderType(appOfferDetailData.getOfferOrderType());
        offer.setOfferType(appOfferDetailData.getOfferType());
        offer.setOfferIndex(appOfferDetailData.getOfferIndex());
        offer.setDescription(appOfferDetailData.getDescription());
        offer.setStartDate(appOfferDetailData.getStartDate());
        offer.setEndDate(appOfferDetailData.getEndDate());
        offer.setStatus(appOfferDetailData.getStatus());
        offer.setOfferId(appOfferDetailData.getOfferId());
        offer.setActionType(appOfferDetailData.getActionType());
        offer.setCouponCode(appOfferDetailData.getCouponCode());
        offer.setRedirectionLink(appOfferDetailData.getRedirectionLink());
        offer.setCouponId(appOfferDetailData.getCouponId());
        offer.setCreatedBy(appOfferDetailData.getCreatedBy());
        offer.setGridImage(appOfferDetailData.getGridImage());
        offer.setGridImageUrl(appOfferDetailData.getGridImageUrl());
        offer.setListImage(appOfferDetailData.getListImage());
        offer.setListImageUrl(appOfferDetailData.getListImageUrl());
        offer.setTitle(appOfferDetailData.getTitle());
        offer.setActionCategory(appOfferDetailData.getActionCategory());
        offer.setMenuCategoryId(appOfferDetailData.getMenuCategoryId());
        offer.setPartnerId(appOfferDetailData.getPartnerId());
        offer.setBrandId(appOfferDetailData.getBrandId());
        if(Objects.isNull(appOfferDetailData.getAppOfferType())) {
            offer.setAppOfferType(AppOfferType.GENERIC_OFFER);
        } else {
            offer.setAppOfferType(AppOfferType.fromValue(appOfferDetailData.getAppOfferType()));
        }

        if(!Objects.isNull(appOfferDetailData.getAppOfferApplicabilityDataList()) && appOfferDetailData.getAppOfferApplicabilityDataList().size() > 0){
            List<AppOfferApplicability> appOfferApplicabilityList = appOfferDetailData.getAppOfferApplicabilityDataList().stream().map(e->convert(e)).collect(Collectors.toList());
           offer.setAppOfferApplicabilityList(appOfferApplicabilityList);
        }else{
            offer.setAppOfferApplicabilityList(Arrays.stream(OfferApplicabilityFlag.values()).map(e->{
                AppOfferApplicability appOfferApplicability = new AppOfferApplicability();
                appOfferApplicability.setAppOfferApplicabilityFlag(e);
                appOfferApplicability.setIsApplicable(OfferApplicable.N);
                return  appOfferApplicability;
            }).collect(Collectors.toList()));
        }

        offer.setAppofferApplicabilityFlags(Arrays.stream(OfferApplicabilityFlag.values()).map(e->e.name()).collect(Collectors.toList()));

        if (mappingDataList != null) {
            List<AppOfferUnitDetail> unitDetailList = new ArrayList<>();
            AppOfferUnitDetail unitDetail = null;
            for (AppOfferMappingData offerMappingData : mappingDataList) {
                unitDetail = new AppOfferUnitDetail(offerMappingData.getMappingValue(),
                    offerMappingData.getMappingUnitName());
                unitDetailList.add(unitDetail);
            }
            offer.setUnitDetailList(unitDetailList);
        }

        return offer;
    }

    public static AppOfferApplicability convert(AppOfferApplicabilityData appOfferApplicabilityData){
        AppOfferApplicability appOfferApplicability = new AppOfferApplicability();
        appOfferApplicability.setAppOfferApplicabilityId(appOfferApplicabilityData.getAppOfferApplicabilityId());
        appOfferApplicability.setAppOfferApplicabilityFlag(appOfferApplicabilityData.getAppOfferApplicabilityFlag());
        appOfferApplicability.setIsApplicable(appOfferApplicabilityData.getIsApplicable());
        return  appOfferApplicability;
    }

    public static AppOfferApplicabilityData convert(AppOfferApplicability appOfferApplicabilityData,AppOfferDetailData appOfferDetailData){
        AppOfferApplicabilityData appOfferApplicability = new AppOfferApplicabilityData();
        appOfferApplicability.setAppOfferApplicabilityId(appOfferApplicabilityData.getAppOfferApplicabilityId());
        appOfferApplicability.setAppOfferApplicabilityFlag(appOfferApplicabilityData.getAppOfferApplicabilityFlag());
        appOfferApplicability.setIsApplicable(appOfferApplicabilityData.getIsApplicable());
        appOfferApplicability.setAppOfferDetailData(appOfferDetailData);
        return  appOfferApplicability;
    }
    public static AddonData getDummyAddon(int id) {
        AddonData addOn = new AddonData();
        addOn.setId(id);
        addOn.setName("DUMMY");
        addOn.setCode("DUMMY");
        return addOn;
    }

    private static TaxProfile convert(UnitTaxMapping mapping) {
        TaxProfile profile = convert(mapping.getTaxProfile());
        profile.setId(mapping.getUnitTaxMappingId());
        profile.setPercentage(mapping.getTaxPercentage());
        return profile;
    }

    public static TaxProfile convert(com.stpl.tech.master.data.model.TaxProfile data) {
        TaxProfile profile = masterFactory.createTaxProfile();
        profile.setName(data.getTaxName());
        profile.setType(TaxType.fromValue(data.getTaxType()));
        profile.setProfileId(data.getTaxProfileId());
        profile.setStatus(data.getTaxProfileStatus());
        return profile;
    }

    public static AddressInfo createAddress(Address address) {
        AddressInfo info = new AddressInfo(address.getLine1(), address.getLine2(), address.getLine3(),
            address.getCity(), address.getState(), address.getCountry(), address.getZipCode(),
            address.getContact1(), address.getContact2(), address.getAddressType(), address.getLatitude(),
            address.getLongitude());
        info.setAddressId(address.getId() != 0 ? address.getId() : null);
        return info;
    }

    public static RefLookup convert(IdCodeName idCodeName, RefLookupType refLookupType) {
        RefLookup ref = new RefLookup();
        ref.setRefLookupType(refLookupType);
        ref.setRlName(idCodeName.getName());
        ref.setRlCode(idCodeName.getCode());
        ref.setRlShortCode(idCodeName.getShortCode());
        ref.setRlStatus(idCodeName.getStatus());
        return ref;
    }

    public static CouponDetail convert(CouponDetailData o, List<CouponDetailMappingData> mappings, boolean getAll, boolean getParentMapping) {
        CouponDetail detail = masterFactory.createCouponDetail();
        detail.setCode(o.getCouponCode());
        detail.setEndDate(o.getEndDate());
        detail.setId(o.getCouponDetailId());
        detail.setMaxUsage(o.getMaxUsage());
        detail.setMaxCustomerUsage(o.getMaxCustomerUsage());
        detail.setReusable(AppConstants.getValue(o.getCouponReuse()));
        detail.setReusableByCustomer(AppConstants.getValue(o.getCustomerReuse()));
        detail.setStartDate(o.getStartDate());
        detail.setStatus(o.getCouponStatus());
        detail.setUsage(o.getUsageCount());
        detail.setManualOverride(AppConstants.getValue(o.getManualOverride()));
        detail.setOffer(convert(o.getOfferDetail(), getAll, false));
        if(Objects.nonNull(o.getCouponApplicability())){
            detail.setCouponApplicability(o.getCouponApplicability());
        }
        addMappings(detail, mappings, getAll);
        if (getParentMapping) {
            addMappings(detail, o.getOfferDetail().getMappings());
        }
        if (AppConstants.YES.equals(o.getCustomerVisibility())) {
            detail.setCustomerVisibility(Boolean.TRUE);
        } else {
            detail.setCustomerVisibility(Boolean.FALSE);
        }

        return detail;
    }

    public static CouponDetail convert(CouponDetailData o) {
        CouponDetail detail = masterFactory.createCouponDetail();
        detail.setCode(o.getCouponCode());
        detail.setEndDate(o.getEndDate());
        detail.setId(o.getCouponDetailId());
        detail.setMaxUsage(o.getMaxUsage());
        detail.setMaxCustomerUsage(o.getMaxCustomerUsage());
        detail.setReusable(AppConstants.getValue(o.getCouponReuse()));
        detail.setReusableByCustomer(AppConstants.getValue(o.getCustomerReuse()));
        detail.setStartDate(o.getStartDate());
        detail.setStatus(o.getCouponStatus());
        detail.setUsage(o.getUsageCount());
        detail.setManualOverride(AppConstants.getValue(o.getManualOverride()));
        detail.setOffer(convert(o.getOfferDetail(), false, true));
        if (AppConstants.YES.equals(o.getCustomerVisibility())) {
            detail.setCustomerVisibility(Boolean.TRUE);
        } else {
            detail.setCustomerVisibility(Boolean.FALSE);
        }
        return detail;
    }

    private static void addMappings(CouponDetail detail, List<CouponDetailMappingData> mappings, boolean getAll) {
        for (CouponDetailMappingData mapping : mappings) {
            if (AppUtils.isActive(mapping.getStatus()) || getAll) {
                addMapping(detail, mapping, getAll);
            }
        }
    }

    private static void addMappings(CouponDetail detail, List<OfferDetailMappingData> mappings) {
        for (OfferDetailMappingData mapping : mappings) {
            if (AppUtils.isActive(mapping.getStatus())) {
                addMapping(detail, mapping, false);
            }
        }
    }

    private static void addMappings(OfferDetail detail, List<OfferDetailMappingData> mappings, boolean getAll) {
        for (OfferDetailMappingData mapping : mappings) {
            if (AppUtils.isActive(mapping.getStatus()) || getAll) {
                addMapping(detail, mapping, getAll);
            }
        }
    }

    public static List<CouponMapping> convert(List<CouponDetailMappingData> mappingDataList) {
        List<CouponMapping> response = new ArrayList<>();
        mappingDataList.forEach(couponDetailMappingData -> {
            CouponMapping data = masterFactory.createCouponMapping();
            data.setType(couponDetailMappingData.getMappingType());
            data.setId(couponDetailMappingData.getCouponDetailMappingId());
            data.setValue(couponDetailMappingData.getMappingValue());
            data.setDataType(couponDetailMappingData.getDataType());
            data.setMinValue(couponDetailMappingData.getMinValue());
            data.setGroup(couponDetailMappingData.getMappingGroup());
            data.setDimension(couponDetailMappingData.getDimension());
            data.setStatus(couponDetailMappingData.getStatus());
            data.setSource(AppConstants.MAPPING_SOURCE_COUPON);
            response.add(data);
        });
        return response;
    }

    private static void addMapping(CouponDetail detail, CouponDetailMappingData mapping, boolean isAdmin) {
        CouponMapping data = masterFactory.createCouponMapping();
        data.setType(mapping.getMappingType());
        data.setId(mapping.getCouponDetailMappingId());
        data.setValue(mapping.getMappingValue());
        data.setDataType(mapping.getDataType());
        data.setMinValue(mapping.getMinValue());
        data.setGroup(mapping.getMappingGroup());
        data.setDimension(mapping.getDimension());
        data.setStatus(mapping.getStatus());
        data.setSource(AppConstants.MAPPING_SOURCE_COUPON);
        if (isAdmin) {
            detail.getCouponMappingList().add(data);
        }
        addToMap(detail, CouponMappingType.valueOf(mapping.getMappingType()).name(), data);
    }

    private static void addMapping(CouponDetail detail, OfferDetailMappingData mapping, boolean isAdmin) {
        CouponMapping data = createMapping(mapping);
        if (isAdmin) {
            detail.getCouponMappingList().add(data);
        }
        addToMap(detail, CouponMappingType.valueOf(mapping.getMappingType()).name(), data);
    }

    private static void addMapping(OfferDetail detail, OfferDetailMappingData mapping, boolean isAdmin) {
        CouponMapping data = createMapping(mapping);
        if (isAdmin) {
            detail.getCouponMappingList().add(data);
        }
    }

    private static CouponMapping createMapping(OfferDetailMappingData mapping) {
        CouponMapping data = masterFactory.createCouponMapping();
        data.setType(mapping.getMappingType());
        data.setId(mapping.getOfferDetailMappingId());
        data.setValue(mapping.getMappingValue());
        data.setDataType(mapping.getDataType());
        data.setMinValue(mapping.getMinValue());
        data.setGroup(mapping.getMappingGroup());
        data.setDimension(mapping.getDimension());
        data.setStatus(mapping.getStatus());
        data.setSource(AppConstants.MAPPING_SOURCE_OFFER);
        return data;
    }

    private static void addToMap(CouponDetail couponDetail, String key, CouponMapping value) {
        if (!couponDetail.getMappings().containsKey(key)) {
            couponDetail.getMappings().put(key, new HashSet<CouponMapping>());
        }
        couponDetail.getMappings().get(key).add(value);
    }

    public static OfferDetail convert(OfferDetailData detail, boolean getAll, boolean trimmed) {
        OfferDetail offer = new OfferDetail();
        offer.setId(detail.getOfferDetailId());
        offer.setCategory(detail.getOfferCategory());
        offer.setType(detail.getOfferType());
        offer.setText(detail.getOfferText());
        offer.setDescription(detail.getOfferDescription());
        offer.setStartDate(detail.getStartDate());
        offer.setEndDate(detail.getEndDate());
        offer.setMinValue(detail.getMinValue());
        offer.setStatus(detail.getOfferStatus());
        offer.setOfferValue(detail.getValue());
        offer.setOfferScope(detail.getOfferScope());
        offer.setBrandId(detail.getBrandId());
        offer.setAccountsCategory(convertToIdName(detail.getAccountsCategory()));
        if(Objects.nonNull(detail.getAccountsCategory())){
            offer.setBudgetCategory(detail.getAccountsCategory().getBudgetCategory());
        }

        if (!trimmed) {
            offer.setValidateCustomer(AppConstants.getValue(detail.getValidateCustomer()));
            offer.setIncludeTaxes(AppConstants.getValue(detail.getIncludeTaxes()));
            offer.setRemoveLoyalty(AppConstants.getValue(detail.getRemoveLoyaltyReward()));
            offer.setMinQuantity(detail.getMinQuantity());
            offer.setMinLoyalty(detail.getMinLoyalty());
            offer.setMinItemCount(detail.getMinItemCount());
            offer.setEmailDomain(detail.getEmailDomain());
            if (detail.getFreeItemProductId() != null && detail.getFreeItemProductId() > 0
                && detail.getOfferType().equals(OfferCategoryType.OFFER_WITH_FREE_ITEM_STRATEGY.name())) {
                OfferWithFreeItemData data = new OfferWithFreeItemData();
                data.setProductId(detail.getFreeItemProductId());
                data.setQuantity(detail.getFreeItemQuantity());
                data.setType(OfferValueType.valueOf(detail.getFreeItemOfferType()));
                data.setValue(detail.getFreeItemOfferValue());
                data.setDimension(detail.getFreeItemDimension());
                offer.setOfferWithFreeItem(data);
            }
            offer.setPrepaid(AppConstants.YES.equalsIgnoreCase(detail.getPrepaid()));
            offer.setPrepaidAmount(detail.getPrepaidAmount());
            offer.setMaxBillValue(detail.getMaxBillValue());
            offer.setOtpRequired(AppConstants.getValue(detail.getOtpRequired()));
            offer.setMaxDiscountAmount(detail.getMaxDiscountAmount());
            addOfferPartners(offer, detail.getPartners(), getAll);
            addMetaDataMappings(offer, detail.getMetaDataMappings(), getAll);
            addMappings(offer, detail.getMappings(), getAll);
        }
        if (detail.getFrequencyApplicable() != null && AppConstants.getValue(detail.getFrequencyApplicable())) {
            offer.setFrequencyApplicable(AppConstants.getValue(detail.getFrequencyApplicable()));
            offer.setFrequencyCount(detail.getFrequencyCount());
            offer.setFrequencyStrategy(detail.getFrequencyStrategy());
            offer.setMaxQuantity(detail.getMaxQuantity());
        } else {
            offer.setFrequencyApplicable(null);
            offer.setFrequencyCount(null);
            offer.setFrequencyStrategy(null);
            offer.setMaxQuantity(null);
        }
        offer.setDailyFrequencyCount(Objects.nonNull(detail.getDailyFrequencyCount()) ? detail.getDailyFrequencyCount() : null);
        offer.setApplicableHour(Objects.nonNull(detail.getApplicableHour()) ? detail.getApplicableHour() : null);
        offer.setAutoApplicableforUnit(Objects.nonNull(detail.getAutoApplicableforUnit()) ? AppConstants.getValue(detail.getAutoApplicableforUnit()) : null);
        offer.setTermsAndConditions(detail.getTermsAndConditions());
        if(Objects.nonNull(detail.getLoyaltyBurnPoints())){
            offer.setLoyaltyBurnPoints(detail.getLoyaltyBurnPoints());
        }
        if(Objects.nonNull(detail.getSignupOfferApplicable())) {
            offer.setSignupOfferApplicable(AppConstants.getValue(detail.getSignupOfferApplicable()));
        }
        return offer;
    }

    private static void addMetaDataMappings(OfferDetail offer, List<OfferMetadata> metaDataMappings, boolean getAll) {
        for (OfferMetadata metaDataMapping : metaDataMappings) {
            if (AppUtils.isActive(metaDataMapping.getStatus()) || getAll) {
                offer.getMetaDataMappings().add(convert(metaDataMapping));
            }
        }
    }

    private static IdCodeName convert(OfferMetadata metaDataMapping) {
        IdCodeName mapping = new IdCodeName();
        mapping.setId(metaDataMapping.getId());
        mapping.setName(metaDataMapping.getMappingType());
        mapping.setCode(metaDataMapping.getMappingValue());
        mapping.setStatus(metaDataMapping.getStatus());
        mapping.setType(metaDataMapping.getMappingClass());
        return mapping;
    }

    private static void addOfferPartners(OfferDetail offer, List<OfferPartner> partners, boolean getAll) {
        for (OfferPartner partner : partners) {
            if (AppUtils.isActive(partner.getStatus()) || getAll) {
                offer.getPartners().add(convert(partner));
            }
        }
    }

    private static MappingIdCodeName convert(OfferPartner partner) {
        MappingIdCodeName code = new MappingIdCodeName();
        code.setMappingId(partner.getMappingId());
        code.setId(partner.getPartner().getPartnerId());
        code.setName(partner.getPartner().getPartnerName());
        code.setType(partner.getPartner().getPartnerType());
        code.setStatus(partner.getStatus());
        return code;
    }

    public static UnitProductMappingData convert(UnitProductMapping mapping, int dimensionId) {
        UnitProductMappingData target = new UnitProductMappingData();
        target.setId(mapping.getUnitProdRefId());
        target.setUnit(convertToIdCodeName(mapping.getUnitDetail()));
        target.setProduct(convertToIdCodeName(mapping.getProductDetail()));
        for (UnitProductPricing pricing : mapping.getUnitProductPricings()) {
            if (pricing.getRefLookup().getRlId().equals(dimensionId)) {
                target.setPrice(convertToProductPrice(pricing));
            }
        }
        return target;
    }

    @SneakyThrows
    public static Map<String, UnitProductMappingData> convert(List<UnitProductMapping> mappings, List<Integer> dimensionIds, UnitProductProfileContext context) {
        ConcurrentHashMap<String, UnitProductMappingData> mappingDataMap = new ConcurrentHashMap<>();
        try {
            ConcurrentHashMap<Integer, IdCodeName> temporaryUnitMap = new ConcurrentHashMap<>();
            ConcurrentHashMap<Integer, IdCodeName> temporaryProductMap = new ConcurrentHashMap<>();
            ConcurrentHashMap<Integer, IdCodeName> temporaryRefLookUpMap = new ConcurrentHashMap<>();


            context.getForkJoinPool().submit(() -> {
                mappings.parallelStream().forEach(mapping -> {
                    try {
                        for (UnitProductPricing pricing : mapping.getUnitProductPricings()) {
                            if (CollectionUtils.isEmpty(dimensionIds) || dimensionIds.contains(pricing.getRefLookup().getRlId())) {
                                String key = generateKey_U_P_D(mapping.getUnitDetail().getUnitId(),
                                        mapping.getProductDetail().getProductId(),
                                        pricing.getRefLookup().getRlId());

                                UnitProductMappingData target = new UnitProductMappingData();
                                target.setId(mapping.getUnitProdRefId());
                                target.setPrice(convertUnitProductPricingToProductPrice(pricing));

                                Integer unitId = mapping.getUnitDetail().getUnitId();
                                Integer productId = mapping.getProductDetail().getProductId();
                                Integer refLookUpId = pricing.getRefLookup().getRlId();

                                temporaryUnitMap.computeIfAbsent(unitId, k -> convertToIdCodeName(mapping.getUnitDetail()));
                                target.setUnit(temporaryUnitMap.get(unitId));

                                temporaryProductMap.computeIfAbsent(productId, k -> convertToIdCodeName(mapping.getProductDetail()));
                                target.setProduct(temporaryProductMap.get(productId));

                                temporaryRefLookUpMap.computeIfAbsent(refLookUpId, k -> convertToIdCodeName(pricing.getRefLookup()));
                                target.setDimension(temporaryRefLookUpMap.get(refLookUpId));

                                mappingDataMap.put(key, target);
                            }
                        }
                    } catch (Exception ex) {
                        log.error("Error processing mapping ID [{}]: {}", mapping.getUnitProdRefId(), ex.getMessage(), ex);
                    }
                });
            }).get();
        } catch (Exception e) {
            log.error("Error while adding mapped unit products for mapping: ", e);
        }
        return mappingDataMap;
    }

    public static String generateKey_U_P_D(Integer unitId, Integer productId, Integer dimensionId) {
        return unitId + "_" + productId + "_" + dimensionId;
    }

    /**
     * Converting Data to Domain Object
     *
     * @param pricing
     * @return
     */
    private static ProductPrice convertToProductPrice(UnitProductPricing pricing) {
        ProductPrice price = masterFactory.createProductPrice();
        price.setId(pricing.getUnitProdPriceId());
        price.setDimension(pricing.getRefLookup().getRlCode());
        price.setPrice(pricing.getPrice());
        price.setCost(pricing.getCost());
        price.setCodCost(pricing.getCodCost());
        price.setBuffer(pricing.getBuffer());
        price.setThreshold(pricing.getThreshold());
        price.setProfile(pricing.getRecipeProfile());
        price.setStatus(pricing.getStatus());
        if (pricing.getAliasProductName() != null) {
            price.setAliasProductName(pricing.getAliasProductName());
        }
        if (pricing.getDimensionDescriptor() != null) {
            price.setDimensionDescriptor(pricing.getDimensionDescriptor());
        }
        price.setIsDeliveryOnlyProduct(AppUtils.getStatus(pricing.getIsDeliveryOnlyProduct()));
        price.setPickDineInConsumables(AppUtils.getStatus(pricing.getPickDineInConsumables()));
        return price;
    }

    private static ProductPrice convertUnitProductPricingToProductPrice(UnitProductPricing pricing) {
        ProductPrice price = new ProductPrice();
        price.setId(pricing.getUnitProdPriceId());
        price.setProfile(pricing.getRecipeProfile());
        price.setCurrentProfile(pricing.getRecipeProfile());
        price.setStatus(pricing.getStatus());
        price.setAliasProductName(pricing.getAliasProductName());
        price.setDimensionDescriptor(pricing.getDimensionDescriptor());
        price.setIsDeliveryOnlyProduct(AppUtils.getStatus(pricing.getIsDeliveryOnlyProduct()));
        price.setPickDineInConsumables(AppUtils.getStatus(pricing.getPickDineInConsumables()));
        return price;
    }

    public static IdCodeName convertToIdCodeName(RefLookup refLookUp) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(refLookUp.getRlId());
        idCodeName.setName(refLookUp.getRlName());
        idCodeName.setCode(refLookUp.getRlCode());
        return idCodeName;
    }

    public static IdCodeName convertToIdCodeName(ProductDetail productDetail) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(productDetail.getProductId());
        idCodeName.setName(productDetail.getProductName());
        idCodeName.setStatus(productDetail.getProductStatus());
        return idCodeName;
    }

    public static IdCodeName convertToIdCodeName(UnitDetail unitDetail) {
        IdCodeName idCodeName = null;
        if (unitDetail != null) {
            idCodeName = new IdCodeName();
            idCodeName.setId(unitDetail.getUnitId());
            idCodeName.setName(unitDetail.getUnitName());
            idCodeName.setStatus(unitDetail.getUnitStatus());
        }
        return idCodeName;
    }

    public static DenominationDetail convert(Denomination denom) {
        DenominationDetail denominationDetail = new DenominationDetail();
        denominationDetail.setBundleSize(denom.getBundleSize());
        denominationDetail.setDenominationCode(denom.getDenominationCode());
        denominationDetail.setDenominationId(denom.getId());
        denominationDetail.setDenominationText(denom.getDenominationText());
        denominationDetail.setDenominationValue(denom.getDenominationValue());
        denominationDetail.setDisplayOrder(denom.getDisplayOrder());
        denominationDetail.setPaymentMode(denom.getPaymentMode().getPaymentModeId());
        denominationDetail.setStatus(denom.getStatus());
        return denominationDetail;
    }

    public static IdCodeName convert(MarketingPartner partner) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(partner.getPartnerId());
        idCodeName.setName(partner.getPartnerName());
        idCodeName.setType(partner.getPartnerType());
        idCodeName.setStatus(partner.getStatus());
        return idCodeName;
    }

    public static EmployeeBasicDetail convert(Employee employee) {
        EmployeeBasicDetail employeeBasicDetail = new EmployeeBasicDetail();
        employeeBasicDetail.setId(employee.getId());
        employeeBasicDetail.setName(employee.getName());
        employeeBasicDetail.setDepartmentName(employee.getDepartment().getName());
        employeeBasicDetail.setDesignationId(employee.getDesignation().getId());
        employeeBasicDetail.setDesignation(employee.getDesignation().getName());
        employeeBasicDetail.setStatus(employee.getEmploymentStatus());
        employeeBasicDetail.setEmailId(employee.getEmployeeEmail());
        employeeBasicDetail.setContactNumber(employee.getPrimaryContact());
        if (employee.getEmployeeCode() != null) {
            employeeBasicDetail.setEmployeeCode(employee.getEmployeeCode());
        }
        if (employee.getReportingManager() != null) {
            employeeBasicDetail.setReportingManagerId(employee.getReportingManager().getId());
        }
        employeeBasicDetail.setSlackChannel(employee.getCommunicationChannel());
        employeeBasicDetail.setMaxAllocatedUnits(employee.getDesignation().getMaxAllocatedUnits());
        if (Objects.nonNull(employee.getDepartment()) && Objects.nonNull(employee.getDepartment().getId())) {
            employeeBasicDetail.setDepartmentId(employee.getDepartment().getId());
        }
        if (Objects.nonNull(employee.getUserPolicyId())) {
            employeeBasicDetail.setUserPolicyId(employee.getUserPolicyId());
        }
        employeeBasicDetail.setCompanyId(employee.getCompany().getId());
        employeeBasicDetail.setImagekey(employee.getImageKey());
        employeeBasicDetail.setGender(employee.getGender());
        employeeBasicDetail.setLocCode(employee.getLocCode());
     return employeeBasicDetail;
    }

    public static EmployeeBasicDetail convertToEmployeeBasicDetail(EmployeeDetail employee) {
        if (employee == null) {
            return null;
        }
        EmployeeBasicDetail employeeBasicDetail = new EmployeeBasicDetail();
        employeeBasicDetail.setId(employee.getEmpId());
        employeeBasicDetail.setName(employee.getEmpName());
        employeeBasicDetail.setDepartmentName(employee.getDepartment().getDeptName());
        employeeBasicDetail.setDesignationId(employee.getDesignation().getDesignationId());
        employeeBasicDetail.setDesignation(employee.getDesignation().getDesignationName());
        employeeBasicDetail.setStatus(EmploymentStatus.valueOf(employee.getEmploymentStatus()));
        employeeBasicDetail.setContactNumber(employee.getEmpContactNum1());
        employeeBasicDetail.setEmailId(employee.getEmployeeEmail());
        employeeBasicDetail.setSdpContact(employee.getSdpContact());
        if (employee.getReportingManager() != null) {
            employeeBasicDetail.setReportingManagerId(employee.getReportingManager().getEmpId());
        }
        employeeBasicDetail.setSlackChannel(employee.getCommunicationChannel());
        employeeBasicDetail.setMaxAllocatedUnits(employee.getDesignation().getMaxAllocatedUnits());
        employeeBasicDetail.setImagekey(employee.getImageKey());
        employeeBasicDetail.setGender(employee.getEmpGender());
        employeeBasicDetail.setLocCode(employee.getLocCode());
        return employeeBasicDetail;
    }

    public static AccessControlList convert(AccessControlListData accessControlListData) {
        AccessControlList accessControlList = new AccessControlList();
        accessControlList.setApplicationName(ApplicationName.valueOf(accessControlListData.getApplicationName()));
        accessControlList.setDescription(accessControlListData.getDescription());
        accessControlList.setId(accessControlListData.getId());
        accessControlList.setModule(accessControlListData.getModule());
        accessControlList.setStatus(SwitchStatus.valueOf(accessControlListData.getStatus()));
        return accessControlList;
    }

    public static PreAuthApi convert(PreAuthenticatedApiData preAuthenticatedApiData) {
        PreAuthApi preAuthApi = new PreAuthApi();
        preAuthApi.setApi(preAuthenticatedApiData.getApi());
        preAuthApi.setId(preAuthenticatedApiData.getId());
        preAuthApi.setStatus(SwitchStatus.valueOf(preAuthenticatedApiData.getStatus()));
        return preAuthApi;
    }

    public static AccessControlListData convert(AccessControlList accessControlList) {
        AccessControlListData accessControlListData = new AccessControlListData();
        accessControlListData.setId(accessControlList.getId());
        accessControlListData.setStatus(accessControlList.getStatus().value());
        accessControlListData.setApplicationName(accessControlList.getApplicationName().value());
        accessControlListData.setDescription(accessControlList.getDescription());
        accessControlListData.setModule(accessControlList.getModule());
        return accessControlListData;
    }

    public static KioskLocationDetailsData convert(KioskLocationDetails kioskLocationDetails, UnitDetail unitDetail) {
        KioskLocationDetailsData data = new KioskLocationDetailsData();
        if (kioskLocationDetails.getLocationId() != null) {
            data.setLocationId(kioskLocationDetails.getLocationId());
        }
        data.setLocationName(kioskLocationDetails.getLocationName());
        data.setLocationAddress(kioskLocationDetails.getLocationAddress());
        data.setLocationShortCode(kioskLocationDetails.getLocationShortCode());
        data.setLocationStatus(kioskLocationDetails.getLocationStatus().toString());
        data.setAssignedUnit(unitDetail);
        data.setOfficeDetailsData(convert(kioskLocationDetails.getOfficeDetails()));
        return data;
    }

    public static KioskOfficeDetailsData convert(KioskOfficeDetails officeDetails) {
        KioskOfficeDetailsData data = new KioskOfficeDetailsData();
        if (officeDetails.getOfficeId() != null) {
            data.setOfficeId(officeDetails.getOfficeId());
        }
        data.setAddressInfo(createAddress(officeDetails.getOfficeAddress()));
        data.setCompanyDetailsData(convert(officeDetails.getCompanyDetails()));
        data.setOfficeName(officeDetails.getOfficeName());
        data.setOfficeShortCode(officeDetails.getOfficeShortCode());
        if (officeDetails.getOfficeContact() != null) {
            data.setOfficeContactName(officeDetails.getOfficeContact().getName());
            data.setOfficeContactEmail(officeDetails.getOfficeContact().getCode());
            data.setOfficeContactPhone(officeDetails.getOfficeContact().getShortCode());
        }
        data.setPaymentMode(officeDetails.getPaymentMode().toString());
        data.setTin(officeDetails.getTin());
        data.setRegion(officeDetails.getRegion().toString());
        data.setOfficeStatus(officeDetails.getOfficeStatus().toString());
        return data;
    }

    public static KioskCompanyDetailsData convert(KioskCompanyDetails companyDetails) {
        KioskCompanyDetailsData data = new KioskCompanyDetailsData();
        if (companyDetails.getCompanyId() != null) {
            data.setCompanyId(companyDetails.getCompanyId());
        }
        data.setPaymentMode(companyDetails.getPaymentMode().toString());
        data.setCompanyEmail(companyDetails.getCompanyEmail());
        data.setCompanyName(companyDetails.getCompanyName());
        data.setCompanyStatus(companyDetails.getCompanyStatus().toString());
        data.setCountry(companyDetails.getCountry());
        data.setSubDomain(companyDetails.getKioskSubDomain());
        if (companyDetails.getContactDetails() != null) {
            data.setContactName(companyDetails.getContactDetails().getName());
            data.setContactEmail(companyDetails.getContactDetails().getCode());
            data.setContactPhone(companyDetails.getContactDetails().getShortCode());
        }
        return data;
    }

    public static KioskCompanyDomainData convert(IdCodeName companyDomain, KioskCompanyDetailsData companyDetails) {
        KioskCompanyDomainData domainData = new KioskCompanyDomainData();
        domainData.setCompanyDetailsData(companyDetails);
        domainData.setDomain(companyDomain.getName());
        domainData.setDomainStatus(SwitchStatus.ACTIVE.toString());
        return domainData;
    }

    public static KioskLocationDetails convert(KioskLocationDetailsData data, boolean getParent) {
        KioskLocationDetails kioskLocationDetails = new KioskLocationDetails();

        kioskLocationDetails.setLocationId(data.getLocationId());
        kioskLocationDetails.setLocationName(data.getLocationName());
        kioskLocationDetails.setLocationAddress(data.getLocationAddress());
        if (getParent) {
            kioskLocationDetails.setOfficeDetails(convert(data.getOfficeDetailsData(), getParent));
        }
        kioskLocationDetails.setLocationShortCode(data.getLocationShortCode());
        kioskLocationDetails.setAssigned(data.getAssignedUnit() != null);
        kioskLocationDetails.setAssignedUnit(convertToIdCodeName(data.getAssignedUnit()));
        if (!data.getKioskMachineDetailsDataList().isEmpty()) {
            kioskLocationDetails.getKioskMachines()
                .addAll(convert(data.getKioskMachineDetailsDataList(), kioskLocationDetails.getAssignedUnit()));
        }
        kioskLocationDetails.setLocationStatus(SwitchStatus.valueOf(data.getLocationStatus()));
        return kioskLocationDetails;
    }

    public static List<KioskMachine> convert(List<KioskMachineDetailsData> machineDetailsDataList,
                                             IdCodeName assignedUnit) {
        List<KioskMachine> kioskMachines = machineDetailsDataList.stream()
            .map(kioskMachineDetailsData -> convert(kioskMachineDetailsData, assignedUnit))
            .collect(Collectors.toList());
        return kioskMachines;
    }

    public static KioskMachine convert(KioskMachineDetailsData kioskMachineDetailsData, IdCodeName assignedUnit) {
        KioskMachine machine = new KioskMachine();

        machine.setMachineId(kioskMachineDetailsData.getMachineId());
        machine.setBatchNo(kioskMachineDetailsData.getBatchNo());
        machine.setInstallationUnit(assignedUnit);
        machine.setUuid(kioskMachineDetailsData.getUuid());
        machine.setMachineStatus(SwitchStatus.valueOf(kioskMachineDetailsData.getMachineStatus()));
        machine.setDeactivationDate(kioskMachineDetailsData.getDeactivationDate());
        machine.setInstallationDate(kioskMachineDetailsData.getInstallationDate());
        machine.setManufacturingDate(kioskMachineDetailsData.getManufacturingDate());

        return machine;
    }

    public static KioskOfficeDetails convert(KioskOfficeDetailsData officeDetailsData, boolean getParent) {
        KioskOfficeDetails officeDetails = new KioskOfficeDetails();

        officeDetails.setOfficeId(officeDetailsData.getOfficeId());
        if (getParent) {
            officeDetails.setCompanyDetails(convert(officeDetailsData.getCompanyDetailsData()));
        }
        officeDetails.setTin(officeDetailsData.getTin());
        officeDetails.setOfficeName(officeDetailsData.getOfficeName());
        officeDetails.setOfficeShortCode(officeDetailsData.getOfficeShortCode());
        officeDetails.setPaymentMode(KioskPaymentMode.valueOf(officeDetailsData.getPaymentMode()));
        officeDetails.setOfficeStatus(SwitchStatus.valueOf(officeDetailsData.getOfficeStatus()));
        officeDetails.setRegion(officeDetailsData.getRegion());
        officeDetails.setOfficeAddress(convert(officeDetailsData.getAddressInfo()));
        IdCodeName contact = new IdCodeName();
        contact.setName(officeDetailsData.getOfficeContactName());
        contact.setCode(officeDetailsData.getOfficeContactEmail());
        contact.setShortCode(officeDetailsData.getOfficeContactPhone());
        contact.setStatus(SwitchStatus.ACTIVE.toString());
        officeDetails.setOfficeContact(contact);
        List<KioskLocationDetailsData> locationDataList = officeDetailsData.getLocationDetailsDataList();
        List<KioskLocationDetails> locationDetails = locationDataList.stream().map(location -> convert(location, false))
            .collect(Collectors.toList());
        officeDetails.getLocationList().addAll(locationDetails);
        return officeDetails;
    }

    public static KioskCompanyDetails convert(KioskCompanyDetailsData companyDetailsData) {
        KioskCompanyDetails companyDetails = new KioskCompanyDetails();

        companyDetails.setPaymentMode(KioskPaymentMode.valueOf(companyDetailsData.getPaymentMode()));
        companyDetails.setCompanyId(companyDetailsData.getCompanyId());
        companyDetails.setCompanyName(companyDetailsData.getCompanyName());
        companyDetails.setCompanyEmail(companyDetailsData.getCompanyEmail());

        IdCodeName contact = new IdCodeName();
        contact.setName(companyDetailsData.getContactName());
        contact.setCode(companyDetailsData.getContactEmail());
        contact.setShortCode(companyDetailsData.getContactPhone());
        contact.setStatus(SwitchStatus.ACTIVE.toString());
        companyDetails.setContactDetails(contact);
        companyDetails.setKioskSubDomain(companyDetailsData.getSubDomain());
        List<KioskOfficeDetailsData> officeDetailDataList = companyDetailsData.getOfficeDetailsDataList();
        List<KioskOfficeDetails> officeDetailList = officeDetailDataList.stream()
            .map(officeDetails -> convert(officeDetails, false)).collect(Collectors.toList());
        companyDetails.getOfficeList().addAll(officeDetailList);
        List<KioskCompanyDomainData> domainDataList = companyDetailsData.getCompanyDomainDataList();
        List<IdCodeName> domainList = domainDataList.stream()
            .filter(domain -> domain.getDomainStatus().equals(SwitchStatus.ACTIVE.toString()))
            .map(domain -> convertToIdCodeName(domain)).collect(Collectors.toList());
        companyDetails.getCompanyDomains().addAll(domainList);
        companyDetails.setCountry(companyDetailsData.getCountry());
        companyDetails.setCompanyStatus(SwitchStatus.valueOf(companyDetailsData.getCompanyStatus()));
        return companyDetails;

    }

    public static IdCodeName convertToIdCodeName(KioskCompanyDomainData domain) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(domain.getDomainId());
        idCodeName.setName(domain.getDomain());
        idCodeName.setStatus(domain.getDomainStatus());
        return idCodeName;
    }

    public static IdCodeName convertToIdCodeName(UnitBasicDetail unit) {
        IdCodeName unitData = new IdCodeName();
        unitData.setId(unit.getId());
        unitData.setName(unit.getName());
        unitData.setStatus(unit.getStatus().toString());
        return unitData;
    }

    /**
     * @param detail
     * @return
     */
    public static Location convert(LocationDetail detail) {
        Location location = new Location();
        location.setCode(detail.getCityCode());
        location.setId(detail.getId());
        location.setName(detail.getCity());
        location.setState(convert(detail.getState()));
        location.setBusiness(AppConstants.getValue(detail.getFunctionalFlag()));
        location.setCountry(location.getState().getCountry());
        return location;
    }

    /**
     * @param state
     * @return
     */
    public static State convert(StateDetail state) {
        State data = new State();
        data.setCode(state.getStateCode());
        data.setId(state.getId());
        data.setName(state.getState());
        data.setShortCode(state.getStateShortCode());
        data.setUt(AppConstants.getValue(state.getIsUt()));
        data.setBusiness(AppConstants.getValue(state.getFunctionalFlag()));
        data.setCountry(convert(state.getCountry()));
        return data;
    }

    /**
     * @param state
     * @return
     */
    public static IdCodeName convertToIdCodeName(StateDetail state) {
        IdCodeName data = new IdCodeName();
        data.setCode(state.getStateCode());
        data.setId(state.getId());
        data.setName(state.getState());
        return data;
    }

    /**
     * @param country
     * @return
     */
    private static Country convert(CountryDetail country) {
        Country data = new Country();
        data.setCode(country.getCountryCode());
        data.setId(country.getId());
        data.setIsdCode(country.getIsdCode());
        data.setName(country.getCountry());
        return data;
    }

    /**
     * @param record
     * @return
     */
    public static StateTax convert(CategoryTaxData record) {
        StateTax tax = new StateTax();
        tax.setCgst(record.getCgstTaxRate());
        tax.setIgst(record.getIgstTaxRate());
        tax.setSgst(record.getSgstTaxRate());
        tax.setDate(record.getStartDate());
        tax.setKeyId(record.getCategoryTaxDataId());
        tax.setState(convertToIdCodeName(record.getState()));
        return tax;
    }

    /**
     * @param record
     * @return
     */
    public static AdditionalTax convert(CategoryAdditionalTaxData record) {
        AdditionalTax tax = new AdditionalTax();
        tax.setTax(record.getTaxRate());
        tax.setDate(record.getStartDate());
        tax.setKeyId(record.getCategoryAdditionalTaxDataId());
        tax.setState(convertToIdCodeName(record.getState()));
        tax.setApplicability(TaxApplicability.valueOf(record.getTaxData().getApplicableOn()));
        tax.setType(record.getTaxData().getTaxCode());
        return tax;
    }

    public static TaxCategory convert(TaxCategoryData record) {
        TaxCategory cat = new TaxCategory();
        cat.setCode(record.getCategoryCode());
        cat.setDesc(record.getCategoryDescription());
        cat.setExempted(AppConstants.getValue(record.getExempted()));
        cat.setId(record.getTaxCategoryDataId());
        cat.setIntDesc(record.getCategoryInternalDescription());
        cat.setStatus(record.getCategoryStatus());
        return cat;
    }

    public static IdName convertToIdName(TaxCategoryData record) {
        IdName cat = new IdName();
        cat.setName(record.getCategoryCode());
        cat.setId(record.getTaxCategoryDataId());
        return cat;
    }

    public static IdCodeName convertToIdCodeName(TaxCategoryData record) {
        IdCodeName cat = new IdCodeName();
        cat.setCode(record.getCategoryCode());
        cat.setId(record.getTaxCategoryDataId());
        cat.setName(record.getCategoryDescription());
        cat.setStatus(record.getCategoryStatus());
        return cat;
    }

    public static TaxInfo convert(TaxProfileData record) {
        TaxInfo cat = new TaxInfo();
        cat.setCode(record.getTaxCode());
        cat.setDesc(record.getTaxDescription());
        cat.setApplicability(TaxApplicability.valueOf(record.getApplicableOn()));
        cat.setId(record.getTaxDataId());
        cat.setIntDesc(record.getTaxInternalDescription());
        cat.setStatus(record.getTaxDataStatus());
        cat.setName(record.getTaxName());
        return cat;
    }

    public static IdCodeName convertToIdCodeName(TaxProfileData record) {
        IdCodeName cat = new IdCodeName();
        cat.setCode(record.getTaxCode());
        cat.setId(record.getTaxDataId());
        cat.setStatus(record.getTaxDataStatus());
        cat.setName(record.getTaxName());
        return cat;
    }

    public static IdCodeName convertToIdCodeName(CountryDetail countryDetail) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(countryDetail.getId());
        idCodeName.setName(countryDetail.getCountry());
        idCodeName.setCode(countryDetail.getIsdCode());
        idCodeName.setStatus(countryDetail.getStatus());
        return idCodeName;
    }

    /**
     * @param detail
     * @return
     */
    public static CancellationReason convert(CancellationReasonData detail) {
        CancellationReason r = new CancellationReason();
        r.setCode(detail.getCode());
        r.setCompleteWastage(AppConstants.getValue(detail.getCompleteWastage()));
        r.setPartialWastage(AppConstants.getValue(detail.getPartialWastage()));
        r.setNoWastage(AppConstants.getValue(detail.getNoWastage()));
        r.setDesc(detail.getDescription());
        r.setId(detail.getCancellationReasonId());
        r.setSource(UnitCategory.valueOf(detail.getSource()));
        r.setStatus(detail.getStatus());
        return r;
    }

    public static IdName convertToIdName(EmployeeDetail e) {
        IdName i = new IdName();
        i.setId(e.getEmpId());
        i.setName(e.getEmpName());
        return i;
    }

    public static IdName convertToIdName(OfferAccountCategory e) {
        return e != null ? new IdName(e.getId(), e.getName()) : null;
    }

    public static TokenizedApi convert(TokenizedApiData tokenizedApiData) {
        TokenizedApi tokenizedApi = new TokenizedApi();
        tokenizedApi.setApiId(tokenizedApiData.getId());
        tokenizedApi.setApiName(tokenizedApiData.getApi());
        tokenizedApi.setApplication(ApplicationName.fromValue(tokenizedApiData.getApplication()));
        tokenizedApi.setApiMethod(tokenizedApiData.getApiMethod());
        tokenizedApi.setStatus(AppUtils.getStatus(tokenizedApiData.getStatus()));
        return tokenizedApi;
    }

    public static ConfigAttributeValue convert(ConfigAttributeValueData valueData) {
        ConfigAttributeValue attributeValue = new ConfigAttributeValue();
        attributeValue.setAttributeValue(valueData.getAttributeValue());
        attributeValue.setApplicationName(valueData.getApplicationName());
        attributeValue.setId(valueData.getValueId());
        if (valueData.getAttributeDefinition() != null) {
            attributeValue.setAttributeDef(convert(valueData.getAttributeDefinition()));
        }
        return attributeValue;
    }

    public static ConfigAttributeDefinition convert(ConfigAttributeDefinitionData defData) {
        ConfigAttributeDefinition attributeDef = new ConfigAttributeDefinition();
        attributeDef.setId(defData.getAttributeId());
        attributeDef.setAttributeCode(defData.getAttributeCode());
        attributeDef.setAttributeName(defData.getAttributeName());
        attributeDef.setAttributeType(defData.getAttributeType());
        attributeDef.setDefaultValue(defData.getDefaultValue());
        return attributeDef;
    }

    public static ExpenseMetadata convert(ExpenseMetadataDetail data) {
        ExpenseMetadata m = new ExpenseMetadata();
        m.setAccountable(AppConstants.getValue(data.getAccountableInPnL()));
        m.setBudgetCat(data.getBudgetCategory());
        m.setDesc(data.getExpenseHeader());
        m.setId(data.getExpenseMetadataId());
        m.setType(data.getCategoryName());
        m.setIsInternalExpense(data.getIsInternalExpense());
        return m;
    }

    public static WarningMetadata convert(WarningReasonDetail data) {
        WarningMetadata warning = new WarningMetadata();
        warning.setReasonId(data.getReasonId());
        warning.setReasonName(data.getReasonName());
        warning.setCategory(data.getCategory());
        warning.setReasonResult(data.getReasonResult());
        warning.setWarningType(data.getWarningType());
        warning.setStatus(data.getStatus());
        return warning;
    }

    public static List<MonkConfigurationData> convert(MonkConfiguration configuration) {
        List<MonkConfigurationData> dataList = new ArrayList<>();
        final int unitId = configuration.getUnitId();
        final int updatedBy = configuration.getUpdatedBy().getId();
        final Date updatedAt = configuration.getUpdatedAt();
        final SwitchStatus status = configuration.getStatus();
        configuration.getConfigurationList().forEach(conf -> {
            MonkConfigurationData configurationData = new MonkConfigurationData();
            configurationData.setConfId(conf.getConfId());
            configurationData.setConfKey(conf.getType().value());
            configurationData.setConfValue(conf.getValue());
            configurationData.setUnitId(unitId);
            configurationData.setScope(conf.getScope().value());
            configurationData.setUpdatedAt(updatedAt);
            configurationData.setUpdatedBy(updatedBy);
            configurationData.setStatus(status.value());
            dataList.add(configurationData);
        });
        return dataList;
    }

    public static MonkAttr convertToAttr(MonkAttrMetaData data) {
        MonkAttr monkAttr = new MonkAttr();
        monkAttr.setAttrId(data.getAttrId());
        monkAttr.setAttr(data.getAttr());
        monkAttr.setLabel(data.getLabel());
        monkAttr.setStatus(SwitchStatus.valueOf(data.getStatus()));
        monkAttr.setScope(data.getScope());
        monkAttr.setType(data.getType());
        return monkAttr;
    }

    public static MonkConfigurationValue convert(MonkConfigurationData data) {
        MonkConfigurationValue value = new MonkConfigurationValue();
        value.setConfId(data.getConfId());
        value.setType(MonkConfigurationType.valueOf(data.getConfKey()));
        value.setValue(data.getConfValue());
        value.setScope(MonkConfigurationScope.valueOf(data.getScope()));
        return value;
    }

    public static ProductBasicDetail convert(ProductVO productVO) {
        ProductBasicDetail detail = new ProductBasicDetail();
        detail.setClassification(productVO.getClassification());
        detail.setCode(productVO.getAttribute());
        detail.setDetail(new IdCodeName(productVO.getId(), productVO.getName(), productVO.getShortCode()));
        detail.setInventoryTracked(productVO.isInventoryTracked());
        detail.setSubType(productVO.getSubType());
        detail.setType(productVO.getType());
        detail.setWebType(productVO.getWebType());
        detail.setEmployeeMealComponent(productVO.isEmployeeMealComponent());
        return detail;
    }

    public static ApplicationInstallationDetail convert(ApplicationInstallationData data, MasterDataCache masterCache) {
        ApplicationInstallationDetail detail = new ApplicationInstallationDetail();
        detail.setId(data.getId());
        detail.setApplicationName(ApplicationName.valueOf(data.getApplicationName()));
        if (data.getScreenType() != null) {
            detail.setScreenType(data.getScreenType());
        }
        detail.setUnit(new IdCodeName(data.getUnitId(), "", masterCache.getUnit(data.getUnitId()).getName()));
        if (data.getTerminal() != null) {
            detail.setTerminal(data.getTerminal());
        }
        detail.setMachineId(data.getMachineId());
        if (data.getMachineDetails() != null) {
            detail.setMachineDetails(data.getMachineDetails());
        }
        detail.setCreatedBy(new IdCodeName(data.getCreatedBy(), "", masterCache.getEmployee(data.getCreatedBy())));
        detail.setCreatedOn(data.getCreatedOn());
        if (data.getLastUpdatedBy() != null) {
            detail.setLastUpdatedBy(
                new IdCodeName(data.getLastUpdatedBy(), "", masterCache.getEmployee(data.getLastUpdatedBy())));
        }
        if (data.getLastUpdatedOn() != null) {
            detail.setLastUpdatedOn(data.getLastUpdatedOn());
        }
        detail.setStatus(ApplicationInstallationStatus.valueOf(data.getStatus()));
        return detail;
    }

    public static UnitRestrictedApplicationDetail convert(UnitRestrictedApplicationData data, MasterDataCache masterCache) {
        UnitRestrictedApplicationDetail detail = new UnitRestrictedApplicationDetail();
        detail.setId(data.getId());
        detail.setApplicationName(ApplicationName.valueOf(data.getApplicationName()));
        detail.setUnit(new IdCodeName(data.getUnitId(), "", masterCache.getUnit(data.getUnitId()).getName()));
        if (data.getLastUpdatedBy() != null) {
            detail.setLastUpdatedBy(
                new IdCodeName(data.getLastUpdatedBy(), "", masterCache.getEmployee(data.getLastUpdatedBy())));
        }
        if (data.getLastUpdatedOn() != null) {
            detail.setLastUpdatedOn(data.getLastUpdatedOn());
        }
        detail.setStatus(ApplicationInstallationStatus.valueOf(data.getStatus()));
        return detail;
    }

    public static ExpenseValidation convert(ExpenseValidationDetail expenseValidationDetail) {
        ExpenseValidation expenseValidation = new ExpenseValidation();
        expenseValidation.setId(expenseValidationDetail.getId());
        expenseValidation.setValidationName(expenseValidationDetail.getValidationName());
        expenseValidation.setValidationStatus(expenseValidationDetail.getValidationStatus());
        expenseValidation.setValidationType(ExpenseValidationType.valueOf(expenseValidationDetail.getValidationType()));
        return expenseValidation;
    }

    public static ProductGroup convert(ProductGroupData productGroupData, List<ProductSequenceData> productSequenceDataList, MasterDataCache masterCache, Map<Integer, Product> mapIdToProduct) {
        ProductGroup productGroup = new ProductGroup();
        productGroup.setGroupDescription(productGroupData.getGroupDescription());
        productGroup.setGroupId(productGroupData.getGroupId());
        productGroup.setGroupName(productGroupData.getGroupName());
        productGroup.setGroupTag(productGroupData.getGroupTag());
        productGroup.setGroupType(productGroupData.getGroupType());
        productGroup.setCloneId(productGroupData.getCloneId());
        productGroup.setCategoryTag(productGroupData.getCategoryTag());
        productGroup.setMenuCategoryImage(productGroupData.getMenuCategoryImage());
        productGroup.setMenuCategoryDetails(productGroupData.getMenuCategoryDetails());
        productGroup.setMenuType(MenuType.valueOf(productGroupData.getMenuType()));
        productGroup.setMenuApp(productGroupData.getMenuApp() != null ? MenuApp.valueOf(productGroupData.getMenuApp()) : null);
        if (productGroupData.getProductGroupImageData() != null) {
            productGroup.setIcon(convert(productGroupData.getProductGroupImageData()));
        }
        String employee = masterCache.getEmployee(productGroupData.getCreatedBy());
        productGroup.setCreatedBy(new IdName(productGroupData.getCreatedBy(), employee));
        productGroup.setCreationTime(productGroupData.getCreationTime());
        productGroup.setProductSequenceList(new ArrayList<>());
        if (productSequenceDataList != null) {
            for (ProductSequenceData sequenceData : productSequenceDataList) {
                // Pass employee in this convert
                if (mapIdToProduct != null) {
                    productGroup.getProductSequenceList().add(convert(productGroupData, sequenceData, masterCache, employee, mapIdToProduct));
                } else {
                    productGroup.getProductSequenceList().add(convert(productGroupData, sequenceData, masterCache,new HashMap<>()));
                }
            }
        }
        return productGroup;
    }

    public static ProductGroupImage convert(ProductGroupImageData data) {
        ProductGroupImage productGroupImage = new ProductGroupImage();
        productGroupImage.setProductGroupImageId(data.getProductGroupImageId());
        productGroupImage.setIconName(data.getIconName());
        productGroupImage.setIconDescription(data.getIconDescription());
        productGroupImage.setIconUrl(data.getIconUrl());
        productGroupImage.setStatus(data.getStatus());
        productGroupImage.setCreatedBy(data.getCreatedBy());
        productGroupImage.setCreationTime(data.getCreationTime());
        return productGroupImage;
    }

    public static ProductSequence convert(ProductGroupData productGroupData, ProductSequenceData productSequenceData, MasterDataCache masterDataCache, String employee, Map<Integer, Product> mapIdToProduct) {
        ProductSequence productSequence = new ProductSequence();
        productSequence.setCreatedBy(new IdName(productSequenceData.getCreatedBy(), employee));
        productSequence.setCreationTime(productSequenceData.getCreationTime());
        productSequence.setId(productSequenceData.getId());
        productSequence.setLastUpdateTime(productSequenceData.getLastUpdateTime());
        productSequence.setProduct(new IdName(productSequenceData.getProductId(), mapIdToProduct.get(productSequenceData.getProductId()).getName()));
        productSequence.setProductGroup(new IdName(productGroupData.getGroupId(), productGroupData.getGroupName()));
        productSequence.setProductIndex(productSequenceData.getProductIndex());
        productSequence.setStatus(productSequenceData.getStatus());
        if (productSequenceData.getIsRecommended() != null) {
            productSequence.setRecommended(AppUtils.getStatus(productSequenceData.getIsRecommended()));
        }
        productSequence.setUpdatedBy(new IdName(productSequenceData.getUpdatedBy(), employee));
        return productSequence;
    }

    public static ProductSequence convert(ProductGroupData productGroupData, ProductSequenceData productSequenceData, MasterDataCache masterDataCache
    ,Map<Integer, Boolean> productRecommendationMappingMap) {
        ProductSequence productSequence = new ProductSequence();
        productSequence.setCreatedBy(new IdName(productSequenceData.getCreatedBy(), masterDataCache.getEmployee(productSequenceData.getCreatedBy())));
        productSequence.setCreationTime(productSequenceData.getCreationTime());
        productSequence.setId(productSequenceData.getId());
        productSequence.setLastUpdateTime(productSequenceData.getLastUpdateTime());
        productSequence.setProduct(new IdName(productSequenceData.getProductId(), masterDataCache.getProduct(productSequenceData.getProductId()).getName()));
        productSequence.setProductGroup(new IdName(productGroupData.getGroupId(), productGroupData.getGroupName()));
        productSequence.setProductIndex(productSequenceData.getProductIndex());
        productSequence.setStatus(productSequenceData.getStatus());
        if (productSequenceData.getIsRecommended() != null) {
            productSequence.setRecommended(AppUtils.getStatus(productSequenceData.getIsRecommended()));
        }
        if(Boolean.TRUE.equals(productRecommendationMappingMap.get(productSequenceData.getProductId()))){
            productSequence.setRecommended(Boolean.TRUE);
        }
        productSequence.setUpdatedBy(new IdName(productSequenceData.getUpdatedBy(), masterDataCache.getEmployee(productSequenceData.getUpdatedBy())));
        return productSequence;
    }


    public static MenuSequence convert(MenuSequenceData menuSequenceData, List<MenuSequenceMappingData> sequenceMappingDataList,
                                       List<ProductGroupData> productGroupDataList, MasterDataCache masterDataCache,
                                       List<ProductSequenceData> productSequenceDataList, Integer menuRecommendationSequenceId,Integer priceProfileId,
                                       Map<Integer,List<MenuSequenceTimingData>> menuSequenceTimingDataMap , Map<Integer, Boolean> productRecommendationMappingMap) {
        MenuSequence menuSequence = new MenuSequence();
        menuSequence.setCreatedBy(new IdName(menuSequenceData.getCreatedBy(), masterDataCache.getEmployee(menuSequenceData.getCreatedBy())));
        menuSequence.setCreationTime(menuSequenceData.getCreationTime());
        menuSequence.setLastUpdateTime(menuSequenceData.getLastUpdateTime());
        menuSequence.setMenuSequenceDescription(menuSequenceData.getGetMenuSequenceDescription());
        menuSequence.setMenuSequenceId(menuSequenceData.getMenuSequenceId());
        menuSequence.setMenuSequenceName(menuSequenceData.getMenuSequenceName());
        menuSequence.setMenuType(MenuType.valueOf(menuSequenceData.getMenuType()));
        menuSequence.setMenuApp(menuSequenceData.getMenuApp() != null ? MenuApp.valueOf(menuSequenceData.getMenuApp()) : null);
        menuSequence.setCloneId(menuSequenceData.getCloneId());
        menuSequence.setMenuRecommendationSequenceId(menuRecommendationSequenceId);
        menuSequence.setProductGroupSequences(new HashSet<>());
        menuSequence.setDynamicPriceProfileId(priceProfileId);
        Map<Integer, List<ProductSequenceData>> productSequenceDataMap = new HashMap<>();
        if (productGroupDataList != null) {
            for (ProductSequenceData sequenceData : productSequenceDataList) {
                List<ProductSequenceData> productSequenceList = productSequenceDataMap.get(sequenceData.getProductGroupId());
                if (productSequenceList == null) {
                    productSequenceList = new ArrayList<>();
                }
                productSequenceList.add(sequenceData);
                productSequenceDataMap.put(sequenceData.getProductGroupId(), productSequenceList);
            }
        }
        if (productGroupDataList != null && !productGroupDataList.isEmpty()) {
            Map<Integer, ProductGroupData> productGroupDataMap = new HashMap<>();
            for (ProductGroupData data : productGroupDataList) {
                productGroupDataMap.put(data.getGroupId(), data);
            }
            if (sequenceMappingDataList != null && !sequenceMappingDataList.isEmpty()) {
                Map<Integer, MenuSequenceMappingData> catMap = new HashMap<>();
                Map<Integer, MenuSequenceMappingData> subCatMap = new HashMap<>();
                for (MenuSequenceMappingData menuSequenceMappingData : sequenceMappingDataList) {
                    if (menuSequenceMappingData.getProductGroupParentId() == null) {
                        catMap.put(menuSequenceMappingData.getProductGroupId(), menuSequenceMappingData);
                    } else {
                        subCatMap.put(menuSequenceMappingData.getProductGroupId(), menuSequenceMappingData);
                    }
                }
                Map<Integer, ProductGroupSequence> productGroupSequenceMap = new HashMap<>();
                for (MenuSequenceMappingData menuSequenceMappingData : catMap.values()) {
                    if (menuSequenceMappingData.getStatus().equals(UnitStatus.ACTIVE.value())) {
                        productGroupSequenceMap.put(menuSequenceMappingData.getProductGroupId(),
                                convert(menuSequenceMappingData, productGroupDataMap.get(menuSequenceMappingData.getProductGroupId()),
                                        masterDataCache, productSequenceDataMap.get(menuSequenceMappingData.getProductGroupId()),productRecommendationMappingMap));
                    }
                }
                for (MenuSequenceMappingData menuSequenceMappingData : subCatMap.values()) {
                    if (menuSequenceMappingData.getStatus().equals(UnitStatus.ACTIVE.value())) {
                        ProductGroupSequence parentGroupSequence = productGroupSequenceMap.get(menuSequenceMappingData.getProductGroupParentId());
                        parentGroupSequence.getSubGroups().add(convert(menuSequenceMappingData, productGroupDataMap.get(menuSequenceMappingData.getProductGroupId()),
                                masterDataCache, productSequenceDataMap.get(menuSequenceMappingData.getProductGroupId()),productRecommendationMappingMap));
                        productGroupSequenceMap.put(parentGroupSequence.getGroupId(), parentGroupSequence);
                    }
                }
                menuSequence.getProductGroupSequences().addAll(productGroupSequenceMap.values());
            }
        }
        if(Objects.nonNull(menuSequenceTimingDataMap) && !menuSequenceTimingDataMap.isEmpty()){
            for(ProductGroupSequence productGroupSequence : menuSequence.getProductGroupSequences()){
                if(menuSequenceTimingDataMap.containsKey(productGroupSequence.getGroupId())){
                    productGroupSequence.setTimings(MasterDataConverter.convertMenuSequenceTiming(menuSequenceTimingDataMap.get(productGroupSequence.getGroupId())));
                }
                else{
                    productGroupSequence.setTimings(new ArrayList<>());
                }
            }
        }
        return menuSequence;
    }

    public static List<MenuSequenceTiming> convertMenuSequenceTiming(List<MenuSequenceTimingData> menuSequenceTimingDataList){
        List<MenuSequenceTiming> result = new ArrayList<>();
        if(Objects.nonNull(menuSequenceTimingDataList) && !menuSequenceTimingDataList.isEmpty()) {
            for (MenuSequenceTimingData menuSequenceTimingData : menuSequenceTimingDataList) {
                for (String service : AppConstants.MENU_TIMING_SERVICES) {
                    MenuSequenceTiming menuSequenceTiming = new MenuSequenceTiming();
                    menuSequenceTiming.setService(service);
                    menuSequenceTiming.setTime1From(menuSequenceTimingData.getTime1From());
                    menuSequenceTiming.setTime1To(menuSequenceTimingData.getTime1To());
                    menuSequenceTiming.setTime2From(menuSequenceTimingData.getTime2From());
                    menuSequenceTiming.setTime2To(menuSequenceTimingData.getTime2To());
                    menuSequenceTiming.setTime3From(menuSequenceTimingData.getTime3From());
                    menuSequenceTiming.setTime3To(menuSequenceTimingData.getTime3To());
                    menuSequenceTiming.setDayMonday(menuSequenceTimingData.isDayMonday());
                    menuSequenceTiming.setDayTuesday(menuSequenceTimingData.isDayTuesday());
                    menuSequenceTiming.setDayWednesday(menuSequenceTimingData.isDayWednesday());
                    menuSequenceTiming.setDayThursday(menuSequenceTimingData.isDayThursday());
                    menuSequenceTiming.setDayFriday(menuSequenceTimingData.isDayFriday());
                    menuSequenceTiming.setDaySaturday(menuSequenceTimingData.isDaySaturday());
                    menuSequenceTiming.setDaySunday(menuSequenceTimingData.isDaySunday());
                    menuSequenceTiming.setStartDate(menuSequenceTimingData.getStartDate());
                    menuSequenceTiming.setEndDate(menuSequenceTimingData.getEndDate());
                    menuSequenceTiming.setDaySlots(menuSequenceTimingData.getDaySlots());
                    result.add(menuSequenceTiming);
                }
            }
        }
        return result;
    }

    public static MenuSequence convert(MenuSequenceData menuSequenceData, MasterDataCache masterDataCache, Integer menuRecommendationSequenceId,
                                       Integer priceProfileId) {
        MenuSequence menuSequence = new MenuSequence();
        menuSequence.setCreatedBy(new IdName(menuSequenceData.getCreatedBy(), masterDataCache.getEmployee(menuSequenceData.getCreatedBy())));
        menuSequence.setCreationTime(menuSequenceData.getCreationTime());
        menuSequence.setLastUpdateTime(menuSequenceData.getLastUpdateTime());
        menuSequence.setMenuSequenceDescription(menuSequenceData.getGetMenuSequenceDescription());
        menuSequence.setMenuSequenceId(menuSequenceData.getMenuSequenceId());
        menuSequence.setMenuSequenceName(menuSequenceData.getMenuSequenceName());
        menuSequence.setMenuType(MenuType.valueOf(menuSequenceData.getMenuType()));
        menuSequence.setMenuStatus(MenuStatus.valueOf(menuSequenceData.getMenuStatus()));
        menuSequence.setMenuApp(menuSequenceData.getMenuApp() != null ? MenuApp.valueOf(menuSequenceData.getMenuApp()) : null);
        menuSequence.setCloneId(menuSequenceData.getCloneId());
        menuSequence.setMenuRecommendationSequenceId(menuRecommendationSequenceId);
        menuSequence.setProductGroupSequences(new HashSet<>());
        menuSequence.setDynamicPriceProfileId(priceProfileId);
        return menuSequence;
    }

    public static ReportSummary convert(XMLReportDefinitionData definitionData, XMLReportVersionData xmlReportVersionData) {
        ReportSummary summary = new ReportSummary();
        summary.setReportId(definitionData.getReportId());
        summary.setReportType(definitionData.getReportType());
        summary.setReportVersion("v" + xmlReportVersionData.getVersionNo());
        summary.setXmlFilePath(xmlReportVersionData.getS3Link());
        summary.setDepartmentName(definitionData.getDepartmentName());
        summary.setStatus(definitionData.getReportStatus());
        summary.setVersionId(xmlReportVersionData.getVersionId());
        summary.setReportName(xmlReportVersionData.getFileName());
        summary.setDefaultV(xmlReportVersionData.getDefaultV());
        summary.setExecutionEnvironment(definitionData.getExecutionEnvironment());
        return summary;
    }

    public static ProductGroupSequence convert(MenuSequenceMappingData menuSequenceMappingData, ProductGroupData productGroupData,
                                               MasterDataCache masterDataCache, List<ProductSequenceData> productSequenceDataList ,
                                               Map<Integer, Boolean> productRecommendationMappingMap) {
        ProductGroupSequence productGroupSequence = new ProductGroupSequence();
        productGroupSequence.setId(menuSequenceMappingData.getId());
        productGroupSequence.setCreatedBy(new IdName(menuSequenceMappingData.getCreatedBy(), masterDataCache.getEmployee(menuSequenceMappingData.getCreatedBy())));
        productGroupSequence.setCreationTime(menuSequenceMappingData.getCreationTime());
        productGroupSequence.setGroupDescription(productGroupData.getGroupDescription());
        productGroupSequence.setGroupId(productGroupData.getGroupId());
        productGroupSequence.setGroupIndex(menuSequenceMappingData.getIndex());
        productGroupSequence.setListingType(menuSequenceMappingData.getListingType());
        productGroupSequence.setGroupName(productGroupData.getGroupName());
        productGroupSequence.setGroupTag(productGroupData.getGroupTag());
        productGroupSequence.setMenuType(MenuType.valueOf(productGroupData.getMenuType()));
        productGroupSequence.setGroupType(productGroupData.getGroupType());
        productGroupSequence.setLastUpdateTime(menuSequenceMappingData.getLastUpdateTime());
        productGroupSequence.setProductSequenceList(new ArrayList<>());
        productGroupSequence.setSubGroups(new ArrayList<>());
        productGroupSequence.setCategoryTag(productGroupData.getCategoryTag());
        productGroupSequence.setMenuCategoryDetails(productGroupData.getMenuCategoryDetails());
        productGroupSequence.setMenuCategoryImage(productGroupData.getMenuCategoryImage());
        productGroupSequence.setUpdatedBy(new IdName(menuSequenceMappingData.getUpdatedBy(), masterDataCache.getEmployee(menuSequenceMappingData.getUpdatedBy())));
        if (productSequenceDataList != null) {
            if (productGroupSequence.getProductSequenceList() == null) {
                productGroupSequence.setProductSequenceList(new ArrayList<>());
            }
            for (ProductSequenceData sequenceData : productSequenceDataList) {
                productGroupSequence.getProductSequenceList().add(convert(productGroupData, sequenceData, masterDataCache,productRecommendationMappingMap));
            }
        }
        return productGroupSequence;
    }


    public static CashMetadata convert(CashMetadataDetail cmd) {
        CashMetadata c = new CashMetadata();
        c.setDataType(cmd.getDataType());
        c.setEndDate(cmd.getEndDate());
        c.setStartDate(cmd.getStartDate());
        c.setType(cmd.getType());
        c.setValue(cmd.getValue());
        return c;
    }

    public static ChannelPartnerDetail convert(ChannelPartner type, ChannelPartnerCommission commission) {

        ChannelPartnerDetail detail = masterFactory.createChannelPartnerDetail();
        detail.setId(type.getPartnerId());
        detail.setName(type.getPartnerDisplayName());
        detail.setCode(type.getPartnerCode());
        detail.setCreditAccount(type.getCreditAccountId());
        detail.setType(type.getServiceType());
        detail.setStatus(type.getStatus());
        detail.setPartnerName(type.getPartnerName());
        if (commission != null) {
            detail.setCommission(commission.getCommissionRate());
            detail.setTaxRate(commission.getTaxRate());
        }
        detail.setApiIntegrated(AppUtils.getStatus(type.getApiIntegrated()));
        return detail;
    }

    public static UnitPartnerMenuMapping convert(UnitChannelPartnerMenuMappingData unitChannelPartnerMenuMappingData,
                                                 UnitChannelPartnerMappingData unitChannelPartnerMappingData, MasterDataCache masterDataCache) {
        UnitPartnerMenuMapping unitPartnerMenuMapping = new UnitPartnerMenuMapping();
        unitPartnerMenuMapping
            .setUnitChannelPartnerMappingId(unitChannelPartnerMenuMappingData.getUnitPartnerMappingId());
        unitPartnerMenuMapping.setUnit(new IdCodeName(unitChannelPartnerMappingData.getUnitId(),
            masterDataCache.getUnit(unitChannelPartnerMappingData.getUnitId()).getName(), ""));
        unitPartnerMenuMapping
            .setDeliveryPartner(new IdCodeName(unitChannelPartnerMappingData.getDeliveryPartnerId(), "", ""));
        unitPartnerMenuMapping
            .setChannelPartner(new IdCodeName(unitChannelPartnerMappingData.getChannelPartnerId(), "", ""));
        unitPartnerMenuMapping.setCreatedAt(unitChannelPartnerMenuMappingData.getCreatedAt());
        unitPartnerMenuMapping.setCreatedBy(new IdCodeName(unitChannelPartnerMenuMappingData.getCreatedBy(),
            masterDataCache.getEmployee(unitChannelPartnerMenuMappingData.getCreatedBy()), ""));
        if (unitChannelPartnerMenuMappingData.getMenuDay() != null) {
            unitPartnerMenuMapping.setDayNumber(unitChannelPartnerMenuMappingData.getMenuDay());
        }
        if (unitChannelPartnerMenuMappingData.getEndTime() != null) {
            unitPartnerMenuMapping.setEndTime(unitChannelPartnerMenuMappingData.getEndTime());
        }
        unitPartnerMenuMapping.setId(unitChannelPartnerMenuMappingData.getId());
        unitPartnerMenuMapping.setMenuApp(MenuApp.valueOf(unitChannelPartnerMenuMappingData.getMenuApp()));
        unitPartnerMenuMapping
            .setMenuSequence(new IdCodeName(unitChannelPartnerMenuMappingData.getMenuSequenceId(), "", ""));
        unitPartnerMenuMapping.setMenuType(MenuType.valueOf(unitChannelPartnerMenuMappingData.getMenuType()));
        if (unitChannelPartnerMenuMappingData.getStartTime() != null) {
            unitPartnerMenuMapping.setStartTime(unitChannelPartnerMenuMappingData.getStartTime());
        }
        unitPartnerMenuMapping.setStatus(unitChannelPartnerMenuMappingData.getStatus());
        unitPartnerMenuMapping.setUpdatedAt(unitChannelPartnerMenuMappingData.getUpdatedAt());
        unitPartnerMenuMapping.setUpdatedBy(new IdCodeName(unitChannelPartnerMenuMappingData.getUpdatedBy(),
            masterDataCache.getEmployee(unitChannelPartnerMenuMappingData.getUpdatedBy()), ""));
        unitPartnerMenuMapping.setCartRecommendationSequenceId(unitChannelPartnerMenuMappingData.getCartRecommendationSequenceId());
        unitPartnerMenuMapping.setMenuRecommendationSequenceId(unitChannelPartnerMenuMappingData.getMenuRecommendationSequenceId());
        unitPartnerMenuMapping.setBrand(new IdCodeName(unitChannelPartnerMenuMappingData.getBrandId(),"",""));
        return unitPartnerMenuMapping;
    }

    public static UnitPartnerBrandKey convert(UnitPartnerBrandMappingData unitPartnerBrandMappingData) {
        UnitPartnerBrandKey unitPartnerData = masterFactory.createUnitPartnerData();
        unitPartnerData.setPartnerId(unitPartnerBrandMappingData.getPartnerId());
        unitPartnerData.setBrandId(unitPartnerBrandMappingData.getBrandId());
        unitPartnerData.setUnitId(unitPartnerBrandMappingData.getUnitId());
        return unitPartnerData;
    }

    public static UnitPartnerBrandRestaurantKey convertWithRestaurantKey (UnitPartnerBrandMappingData unitPartnerBrandMappingData){
        UnitPartnerBrandRestaurantKey unitPartnerBrandRestaurantKey= masterFactory.createUnitPartnerBrandRestaurantData();
        unitPartnerBrandRestaurantKey.setPartnerId(unitPartnerBrandMappingData.getPartnerId());
        unitPartnerBrandRestaurantKey.setBrandId(unitPartnerBrandMappingData.getBrandId());
        unitPartnerBrandRestaurantKey.setUnitId(unitPartnerBrandMappingData.getUnitId());
        unitPartnerBrandRestaurantKey.setRestaurantId(unitPartnerBrandMappingData.getRestaurantId());
        return unitPartnerBrandRestaurantKey;
    }
    public static RestaurantPartnerKey convertToKey(UnitPartnerBrandMappingData unitPartnerBrandMappingData) {
        RestaurantPartnerKey restaurantPartnerKey = masterFactory.createRestaurantPartnerKey();
        restaurantPartnerKey.setPartnerId(unitPartnerBrandMappingData.getPartnerId());
        restaurantPartnerKey.setRestaurantId(unitPartnerBrandMappingData.getRestaurantId());
        return restaurantPartnerKey;
    }

    public static EntityAliasKey convert(EntityAliasMappingData entityAliasMappingData) {
        EntityAliasKey entityAliasKey = masterFactory.createEntityAlias();
        entityAliasKey.setEntityId(entityAliasMappingData.getEntityId());
        entityAliasKey.setEntityType(entityAliasMappingData.getEntityType());
        entityAliasKey.setBrandId(entityAliasMappingData.getBrandId());
        return entityAliasKey;
    }

    public static Brand convert(BrandDetail brandDetail, List<BrandAttributes> brandAttributes) {
        Brand brand = masterFactory.createBrand();
        brand.setBillTag(brandDetail.getBillTag());
        brand.setBrandCode(brandDetail.getBrandCode());
        brand.setBrandId(brandDetail.getBrandId());
        brand.setBrandName(brandDetail.getBrandName());
        brand.setDomain(brandDetail.getDomain());
        brand.setTagLine(brandDetail.getTagLine());
        brand.setWebsiteLink(brandDetail.getWebsiteLink());
        brand.setStatus(brandDetail.getStatus());
        brand.setSupportContact(brandDetail.getSupportContact());
        brand.setSupportEmail(brandDetail.getSupportEmail());
        brand.setVerbiage(brandDetail.getVerbiage());
        brand.setBrandContactCode(Objects.nonNull(brandDetail.getBrandContactCode()) ? brandDetail.getBrandContactCode() : null);
        for (BrandAttributes brandAttribute : brandAttributes) {
            BrandAttributeEnum def = BrandAttributeEnum.valueOf(brandAttribute.getAttributeKey());
            setBrandAttributes(brand, def, brandAttribute.getAttributeValue());
        }
        for (BrandAttributeEnum attEnum : BrandAttributeEnum.values()) {
            boolean setDefault = true;
            for (BrandAttributes brandAttribute : brandAttributes) {
                if (attEnum.equals(BrandAttributeEnum.valueOf(brandAttribute.getAttributeKey()))) {
                    setDefault = false;
                }
            }
            if (attEnum.getDefaultValue() != null && setDefault) {
                setBrandAttributes(brand, attEnum, attEnum.toString());
            }
        }
        return brand;
    }

    public static BannerDetail convert(BannerDetailData bannerDetailData) {
        BannerDetail bannerDetail = masterFactory.createBannerDetail();
        bannerDetail.setButtonText(bannerDetailData.getBannerButtonText());
        bannerDetail.setButtonAction(bannerDetailData.getBannerButtonAction());
        bannerDetail.setCode(bannerDetailData.getBannerCode());
        bannerDetail.setDesc(bannerDetailData.getBannerDescription());
        bannerDetail.setExp(bannerDetailData.getBannerExpiryDate());
        bannerDetail.setId(bannerDetailData.getBannerId());
        bannerDetail.setImageUrl(bannerDetailData.getBannerUrl());
        bannerDetail.setStart(bannerDetailData.getBannerActivationDate());
        bannerDetail.setSubTitle(bannerDetailData.getBannerSubTitle());
        bannerDetail.setTitle(bannerDetailData.getBannerTitle());
        bannerDetail.setType(bannerDetailData.getBannerType());
        bannerDetail.setStatus(bannerDetailData.getStatus());
        bannerDetail.setProductId(bannerDetailData.getProductId());
        bannerDetail.setCategoryId(bannerDetailData.getCategoryId());
        bannerDetail.setSectionType(bannerDetailData.getSectionType());
        return bannerDetail;
    }

    private static void setBrandAttributes(Brand brand, BrandAttributeEnum attEnum, String value) {
        switch (attEnum) {
            case SEND_NPS:
                brand.setSendNPS(AppConstants.getValue(value));
                break;
            case AWARD_LOYALTY:
                brand.setAwardLoyalty(AppConstants.getValue(value));
                break;
            case SEND_WELCOME_MESSAGE:
                brand.setSendWelcomeMessage(AppConstants.getValue(value));
                break;
            case SEND_EMAIL:
                brand.setSendEmail(AppConstants.getValue(value));
                break;
            case FEEDBACK_URL:
                brand.setFeedBackUrl(value);
                break;
            case FEEDBACK_ENDPOINT_DINEIN:
                brand.setFeedbackEndpointDinein(value);
                break;
            case FEEDBACK_ENDPOINT_DELIVERY:
                brand.setFeedbackEndpointDelivery(value);
                break;
            case FEEDBACK_ENDPOINT_NPS_CAFE:
                brand.setFeedbackEndpointNPSCafe(value);
                break;
            case FEEDBACK_ENDPOINT_NPS_DELIVERY:
                brand.setFeedbackEndpointNPSDelivery(value);
                break;
            case FEEDBACK_ENDPOINT_REDIRECT_URL:
                brand.setFeedbackEndpointRedirectUrl(value);
                break;
            case FEEDBACK_ENDPOINT_LOW_RATING_DINEIN:
                brand.setFeedbackEndpointLowRatingDinein(value);
                break;
            case FEEDBACK_ENDPOINT_LOW_RATING_DELIVERY:
                brand.setFeedbackEndpointLowRatingDelivery(value);
                break;
            case SEND_FEEDBACK_MESSAGE_DELIVERY_SWIGGY:
                brand.setSendFeedbackMessageDeliverySwiggy(AppConstants.getValue(value));
                break;
            case FEEDBACK_ENDPOINT_NPS_DELIVERY_ONLY_ORDER:
                brand.setFeedBackEndpointNPSDeliveryOnlyOrder(value);
                break;
            case VERIFICATION_EMAIL_TEMPLATE:
                brand.setVerificationEmailTemplate(value);
                break;
            case MONK_IMAGE_LOGO:
                brand.setMonkImageLogoUrl(value);
                break;
            case MONK_WELCOME_VIDEO:
                brand.setMonkWelcomeVideoUrl(value);
                break;
            case MONK_SCREEN_SAVER:
                brand.setMonkScreenSaver(value);
                break;
            case MONK_BACKGROUND:
                brand.setMonkBackgroundUrl(value);
                break;
            case SMS_ID:
                brand.setSmsId(value);
                break;
            case INTERNAL_ORDER_FEEDBACK_URL:
                brand.setInternalOrderFeedbackUrl(value);
                break;
            case CHAAYOS_SUBSCRIPTION:
                brand.setChaayosSubscription(value);
                break;
            case GOOGLE_ADS_CUSTOMER_ID:
                brand.setGoogleAdsCustomerId(value);
            case DELIVERY_ORDER_CANCELLATION_TIME:
                brand.setDeliveryOrderCancellationTime(value);
            case DINEIN_ORDER_CANCELLATION_TIME:
                brand.setDineInOrderCancellationTime(value);
        }
    }

    public static MenuRecommendation convert(MenuRecommendationData request, List<MenuRecommendationMapping> list) {
        MenuRecommendation data = new MenuRecommendation();
        data.setMenuRecommendationId(request.getMenuRecommendationId());
        data.setMenuRecommendationName(request.getMenuRecommendationName());
        data.setMenuRecommendationDescription(request.getMenuRecommendationDescription());
        data.setRecommendationType(request.getRecommendationType());
        data.setRecommendationProduct(list);
        data.setCloneId(request.getCloneId());
        data.setCreatedBy(request.getCreatedBy());
        data.setStatus(request.getStatus());
        data.setCreationTime(request.getCreationTime());
        data.setLastUpdateTime(request.getLastUpdateTime());
        return data;
    }

    public static UserRole convert(UserRoleData userRoleData) {
        UserRole userRole = new UserRole();
        userRole.setApplicationId(userRoleData.getApplicationId());
        userRole.setDescription(userRoleData.getDescription());
        userRole.setId(userRoleData.getId());
        userRole.setName(userRoleData.getName());
        userRole.setStatus(userRoleData.getStatus());
        return userRole;
    }

    public static List<ProductGroup> convert(List<ProductGroupData> productGroupDataList, MasterDataCache masterDataCache) {
        List<ProductGroup> productGroupList = new ArrayList<>();
        for (ProductGroupData productGroupData : productGroupDataList) {
            ProductGroup productGroup = new ProductGroup();
            productGroup.setGroupDescription(productGroupData.getGroupDescription());
            productGroup.setGroupId(productGroupData.getGroupId());
            productGroup.setGroupName(productGroupData.getGroupName());
            productGroup.setGroupTag(productGroupData.getGroupTag());
            productGroup.setGroupType(productGroupData.getGroupType());
            productGroup.setCloneId(productGroupData.getCloneId());
            productGroup.setMenuType(MenuType.valueOf(productGroupData.getMenuType()));
            productGroup.setMenuApp(productGroupData.getMenuApp() != null ? MenuApp.valueOf(productGroupData.getMenuApp()) : null);
            if (productGroupData.getProductGroupImageData() != null) {
                productGroup.setIcon(convert(productGroupData.getProductGroupImageData()));
            }
            productGroup.setCreatedBy(new IdName(productGroupData.getCreatedBy(), masterDataCache.getEmployee(productGroupData.getCreatedBy())));
            productGroup.setCreationTime(productGroupData.getCreationTime());
            productGroup.setProductSequenceList(new ArrayList<>());
            productGroupList.add(productGroup);
        }
        return productGroupList;
    }


    public static PriceProfileDetail  convert(PriceProfile priceProfile, List<PriceProfileRangeValues> priceProfileRangeValues) {
        if (priceProfile != null) {
            PriceProfileDetail detail = new PriceProfileDetail();
            detail.setPriceProfileId(priceProfile.getPriceProfileId());
            detail.setLastUpdatedBy(priceProfile.getLastUpdatedBy());
            detail.setLastUpdateTime(priceProfile.getLastUpdateTime());
            detail.setProfileCreationTime(priceProfile.getProfileCreationTime());
            detail.setProfileDescription(priceProfile.getProfileDescription());
            detail.setProfileStatus(priceProfile.getProfileStatus());
            detail.setProfileType(PriceProfileStrategy.fromValue(priceProfile.getProfileType()));
            detail.setThresholdPercentage(priceProfile.getThresholdPercentage());
            List<PriceProfileRangeValueDetail> profileRangeValueDetails = new ArrayList<>();
            for (PriceProfileRangeValues rangeValues : priceProfileRangeValues) {
                PriceProfileRangeValueDetail values = new PriceProfileRangeValueDetail();
                values.setProfileProfileRangeValuesId(rangeValues.getProfileProfileRangeValuesId());
                values.setPriceProfileId(rangeValues.getPriceProfileId());
                values.setDeltaPrice(rangeValues.getDeltaPrice());
                values.setEndPrice(rangeValues.getEndPrice());
                values.setLastUpdatedBy(rangeValues.getLastUpdatedBy());
                values.setStartPrice(rangeValues.getStartPrice());
                values.setRangeValuesStatus(rangeValues.getRangeValuesStatus());
                values.setStartPrice(rangeValues.getStartPrice());
                values.setLastUpdateTime(rangeValues.getLastUpdateTime());
                profileRangeValueDetails.add(values);
            }
            detail.setProfileRangeValueDetails(profileRangeValueDetails);
            return detail;
        }
        return null;
    }

    public static CampaignDetail convert(CampaignDetailData campaignDetailData) {
        CampaignDetail campaignDetail = new CampaignDetail();
        if (campaignDetailData != null) {
            campaignDetail.setCampaignId(campaignDetailData.getCampaignId());
            campaignDetail.setPrimaryUrl(campaignDetailData.getPrimaryUrl());
            campaignDetail.setCampaignStrategy(campaignDetailData.getCampaignStrategy());
            campaignDetail.setCampaignSource(campaignDetailData.getCampaignSource());
            campaignDetail.setCampaignMedium(campaignDetailData.getCampaignMedium());
            campaignDetail.setCampaignName(campaignDetailData.getCampaignName());
            campaignDetail.setCampaignCategory(campaignDetailData.getCampaignCategory());
            campaignDetail.setCampaignDesc(campaignDetailData.getCampaignDesc());
            campaignDetail.setCouponCode(campaignDetailData.getCouponCode());
            campaignDetail.setCouponCodeDesc(campaignDetailData.getCouponCodeDesc());
            campaignDetail.setCouponClone(AppConstants.getValue(campaignDetailData.getIsCouponClone()));
            campaignDetail.setRegion(campaignDetailData.getRegion());
            campaignDetail.setCity(campaignDetailData.getCity());
            campaignDetail.setUnitIds(campaignDetailData.getUnitIds());
            campaignDetail.setUsageLimit(campaignDetailData.getUsageLimit());
            campaignDetail.setStartDate(campaignDetailData.getStartDate());
            campaignDetail.setEndDate(campaignDetailData.getEndDate());
            campaignDetail.setValidity(campaignDetailData.getValidity());
            campaignDetail.setHeroBannerMobile(campaignDetailData.getHeroBannerMobile());
            campaignDetail.setHeroBannerDesktop(campaignDetailData.getHeroBannerDesktop());
            campaignDetail.setLandingPageDesc(campaignDetailData.getLandingPageDesc());
            campaignDetail.setSmsTemplate(campaignDetailData.getSmsTemplate());
            campaignDetail.setSmsReminder(campaignDetailData.getSmsReminder());
            campaignDetail.setWhatsappTemplate(campaignDetailData.getWhatsappTemplate());
            campaignDetail.setWhatsappReminder(campaignDetailData.getWhatsappReminder());
            campaignDetail.setReminderDayGap(campaignDetailData.getReminderDayGap());
            campaignDetail.setUtmHeading(campaignDetailData.getUtmHeading());
            campaignDetail.setUtmDesc(campaignDetailData.getUtmDesc());
            campaignDetail.setUtmImageUrl(campaignDetailData.getUtmImageUrl());
            campaignDetail.setRedirectionUrl(campaignDetailData.getRedirectionUrl());
            campaignDetail.setCampaignStatus(campaignDetailData.getCampaignStatus());
            campaignDetail.setCampaignToken(campaignDetailData.getCampaignToken());
            campaignDetail.setImage1(campaignDetailData.getImage1());
            campaignDetail.setImage2(campaignDetailData.getImage2());
            campaignDetail.setImage3(campaignDetailData.getImage3());
            campaignDetail.setLongUrl(campaignDetailData.getLongUrl());
            campaignDetail.setShortUrl(campaignDetailData.getShortUrl());
            campaignDetail.setNewCustomerOnly(campaignDetailData.getNewCustomerOnly());
            campaignDetail.setCampaignReach(campaignDetailData.getCampaignReach());
            campaignDetail.setCouponPrefix(campaignDetailData.getCouponPrefix());
            campaignDetail.setLinkedCampaignId(campaignDetailData.getLinkedCampaignId());
            campaignDetail.setCouponApplicableAfter(campaignDetailData.getCouponApplicableAfter());
            campaignDetail.setBrandId(campaignDetailData.getBrandId());
            campaignDetail.setApplicableForOrder(AppConstants.getValue(campaignDetailData.getApplicableForOrder()));
            campaignDetail.setParentCampaignStrategy(campaignDetailData.getParentCampaignStrategy());
            campaignDetail.setLaunchUnitId(campaignDetailData.getLaunchUnitId());
            campaignDetail.setCafeLaunchDate(campaignDetailData.getCafeLaunchDate());
            campaignDetail.setCrmAppBannerUrl(campaignDetailData.getCrmAppBannerUrl());
            Map<String, Map<Integer, CampaignMapping>> campaignMap = new HashMap<>();
            for(CampaignCouponMapping mapping : campaignDetailData.getCouponMapping() ){
                if(campaignMap.containsKey(mapping.getCustomerType())){
                    campaignMap.get(mapping.getCustomerType()).put(mapping.getJourneyNumber(),getCampaignMapping(mapping));
                }else{
                    Map<Integer, CampaignMapping> innerMap = new HashMap<>();
                    innerMap.put(mapping.getJourneyNumber(),getCampaignMapping(mapping));
                    campaignMap.put(mapping.getCustomerType(),innerMap);
                }
            }
            campaignDetail.setMappings(campaignMap);
            return campaignDetail;
        }
        return null;
    }


    public static CampaignMapping getCampaignMapping(CampaignCouponMapping mapping ){
        CampaignMapping campaignMapping = new CampaignMapping();
        campaignMapping.setCampaignId(mapping.getCampaignDetailData().getCampaignId());
        campaignMapping.setCode(mapping.getCloneCode());
        campaignMapping.setDesc(mapping.getCloneCodeDesc());
        campaignMapping.setJourney(mapping.getJourneyNumber());
        campaignMapping.setValidityInDays(mapping.getValidityInDays());
        campaignMapping.setReminderDays(mapping.getReminderDays());
        campaignMapping.setCampaignCouponMappingId(mapping.getCampaignCouponMappingId());
        return campaignMapping;
    }

    public static EmployeeApplicationMapping convert(EmployeeApplicationMappingDetail mappingDetail) {
        EmployeeApplicationMapping empMappingDetail = new EmployeeApplicationMapping();
        empMappingDetail.setMappingId(mappingDetail.getMappingId());
        empMappingDetail.setEmployeeId(mappingDetail.getEmployeeId());
        empMappingDetail.setMappingType(mappingDetail.getMappingType());
        empMappingDetail.setMappingValue(Collections.singletonList(mappingDetail.getMappingValue()));
        empMappingDetail.setRecordStatus(mappingDetail.getRecordStatus());
        return empMappingDetail;
    }


    public static List<EmployeeBasicDetail> convert(EmployeeResponse employeeResponse){
        List<EmployeeBasicDetail> employeeBasicDetailList = new ArrayList<>();
        List<EmployeeData> employeeDataList = employeeResponse.getData();
        for(EmployeeData empData : employeeDataList){
            EmployeeBasicDetail employeeBasicDetail = new EmployeeBasicDetail();
            employeeBasicDetail.setEmployeeCode(empData.getEmpId());
            employeeBasicDetail.setContactNumber(empData.getContact());
            employeeBasicDetail.setDesignation(empData.getDesignation());
            employeeBasicDetail.setEmailId(empData.getEmail());
            employeeBasicDetail.setDepartmentName(empData.getDepartment());
            employeeBasicDetail.setName(empData.getFirstName().concat(empData.getMiddleName()).concat(empData.getLastName()));
            employeeBasicDetail.setReportingManagerId(Integer.parseInt(empData.getReportingManagerEmpId()));

            String status = empData.getEmployeeStatus();
            if(status.toLowerCase().contains("inactive"))
                   employeeBasicDetail.setStatus(EmploymentStatus.IN_ACTIVE);
            else if(status.toLowerCase().contains("archived"))
                employeeBasicDetail.setStatus(EmploymentStatus.ARCHIEVED);
            else
                employeeBasicDetail.setStatus(EmploymentStatus.ACTIVE);

            employeeBasicDetailList.add(employeeBasicDetail);
        }

        return employeeBasicDetailList;
    }

    public static Employee convert(EmployeeData employeeData,Department department,Designation designation , Employee reportingManager,Integer locCode){
        Employee employee = new Employee();
        employee.setEmployeeCode(employeeData.getEmpId());
        employee.setEmployeeEmail(employeeData.getEmail());
        StringBuilder stringBuilder = new StringBuilder();
//        log.info("Employeee name Middle name Last Name :::::::::::::::::::::::::::::::::::::::{} {}", employeeData.getFirstName() , employeeData.getMiddleName());
        stringBuilder.append(employeeData.getFirstName().trim()).append(' ').append(employeeData.getMiddleName().trim()).append(" ").append(employeeData.getLastName().trim());
        String name  = AppUtils.unaccent(stringBuilder.toString());
        log.info("emp name to set : {}" ,name);
        employee.setName(name);
        employee.setDesignation(designation);
        employee.setDepartment(department);
        employee.setPrimaryContact(employeeData.getContact());
        String s = employeeData.getEmployeeType().replace(' ','_').toUpperCase();
        EmploymentType emp = EmploymentType.valueOf(s);
        employee.setEmploymentType(emp);
//        if(employeeData.getEmployeeStatusString().toLowerCase().contains("inactive")){
//            employee.setEmploymentStatus(EmploymentStatus.IN_ACTIVE);
//            employee.setReasonForTermination(employeeData.getEmployeeStatusString());
//        }else if(employeeData.getEmployeeStatusString().toLowerCase().contains("active")){
//            employee.setEmploymentStatus(EmploymentStatus.ACTIVE);
//        }else if(employeeData.getEmployeeStatusString().toLowerCase().contains("archieved")){
//            employee.setEmploymentStatus(EmploymentStatus.ARCHIEVED);
//        }

        if(Objects.equals(employeeData.getEmployeeStatus(), "1")){
            employee.setEmploymentStatus(EmploymentStatus.ACTIVE);
        }
        else if(employeeData.getEmployeeStatusString().toLowerCase().contains("archieved")){
            employee.setEmploymentStatus(EmploymentStatus.ARCHIEVED);
        }else{
            employee.setEmploymentStatus(EmploymentStatus.IN_ACTIVE);
            employee.setReasonForTermination(employeeData.getEmployeeStatusString());
        }

        employee.setReportingManager(reportingManager);
        Date doj = AppUtils.getDate(AppUtils.formatDate(AppUtils.getDate(employeeData.getDOJ(),"MMM dd,yyyy"),"yyyy-MM-dd"),"yyyy-MM-dd");
        employee.setJoiningDate(doj);
        if(!employeeData.getDOB().equalsIgnoreCase("-") && !employeeData.getDOB().equalsIgnoreCase("") && !employeeData.getDOB().equalsIgnoreCase(" ")) {
            Date dob = AppUtils.getDate(AppUtils.formatDate(AppUtils.getDate(employeeData.getDOB(), "dd/MM/yyyy"), "yyyy-MM-dd"), "yyyy-MM-dd");
            employee.setDob(dob);
        }
        employee.setHrExecutive(employeeData.getHrExecutive());
        employee.setLeaveApprovalAuthority(employeeData.getLeaveApprovalAuthority());
        employee.setLocCode(Integer.valueOf(locCode));
        employee.setEmployeeMealEligible(AppConstants.employeeMealEligibleDepartments.contains(employee.getDepartment().getId())); //only for cafe employees
        employee.setImageKey(employeeData.getProfilePic());
        employee.setGender(StringUtils.isEmpty(employeeData.getGender()) ? null : 
            employeeData.getGender().equalsIgnoreCase("Male") ? "M" : 
            employeeData.getGender().equalsIgnoreCase("Female") ? "F" : null);
        return employee;
    }

    public static DocumentDetailData convert(DocumentDetailDTO documentDetailDTO) {
        DocumentDetailData documentDetailData = new DocumentDetailData();
        documentDetailData.setDocumentId(documentDetailDTO.getDocumentId());
        documentDetailData.setUpdatedBy(documentDetailDTO.getUpdatedBy().getId());
        documentDetailData.setDocumentLink(documentDetailDTO.getDocumentLink());
        documentDetailData.setFileUrl(documentDetailDTO.getFileUrl());
        documentDetailData.setDocumentUploadType(documentDetailDTO.getUploadType().name());
        documentDetailData.setDocumentUploadTypeId(documentDetailDTO.getUploadTypeId());
        documentDetailData.setFileType(documentDetailDTO.getFileType().name());
        documentDetailData.setMimeType(documentDetailDTO.getMimeType().name());
        documentDetailData.setUpdateTime(AppUtils.getCurrentTimestamp());
        documentDetailData.setS3Key(documentDetailDTO.getS3Key());
        documentDetailData.setS3Bucket(documentDetailDTO.getS3Bucket());
        return documentDetailData;
    }

    public static DocumentDetailDTO convert(DocumentDetailData detailData) {
        if (detailData == null) {
            return null;
        }
        DocumentDetailDTO detail = new DocumentDetailDTO();
        detail.setDocumentId(detailData.getDocumentId());
        detail.setFileUrl(detailData.getFileUrl());
        detail.setDocumentLink(detailData.getDocumentLink());
        detail.setUpdatedBy(generateIdCodeName(detailData.getUpdatedBy(), "", ""));
        detail.setFileType(FileTypeDTO.valueOf(detailData.getFileType()));
        detail.setMimeType(MimeType.valueOf(detailData.getMimeType()));
        detail.setUploadType(DocUploadTypeDTO.valueOf(detailData.getDocumentUploadType()));
        detail.setUploadTypeId(detailData.getDocumentUploadTypeId());
        detail.setS3Bucket(detailData.getS3Bucket());
        detail.setS3Key(detailData.getS3Key());
        detail.setDocumentLink(detailData.getDocumentLink());
        detail.setUpdateTime(detailData.getUpdateTime());
        return detail;
    }

    public static IdCodeName generateIdCodeName(Integer id, String code, String name) {
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(id);
        idCodeName.setCode(code);
        idCodeName.setName(name);
        return idCodeName;
    }

    public static UnitContactDetails convert(UnitContactDetailsData unitContactDetailsData){
        UnitContactDetails unitContactDetails = new UnitContactDetails();
        unitContactDetails.setUnitId(unitContactDetailsData.getUnitId());
        unitContactDetails.setUnitName(unitContactDetailsData.getUnitName());

        List<IdCodeName> idCodeNameList = new ArrayList<>();
        if(Objects.nonNull(unitContactDetailsData.getFirstContactName()) && Objects.nonNull(unitContactDetailsData.getFirstContactNumber())) {
            idCodeNameList.add(new IdCodeName(unitContactDetailsData.getFirstContactName(), unitContactDetailsData.getFirstContactNumber()));
        }
        if(Objects.nonNull(unitContactDetailsData.getSecondContactName()) && Objects.nonNull(unitContactDetailsData.getSecondContactNumber())) {
            idCodeNameList.add(new IdCodeName(unitContactDetailsData.getSecondContactName(), unitContactDetailsData.getSecondContactNumber()));
        }
        if(Objects.nonNull(unitContactDetailsData.getThirdContactName()) && Objects.nonNull(unitContactDetailsData.getThirdContactNumber())) {
            idCodeNameList.add(new IdCodeName(unitContactDetailsData.getThirdContactName(), unitContactDetailsData.getThirdContactNumber()));
        }
        unitContactDetails.setContactDetails(idCodeNameList);
        return unitContactDetails;
    }
    public static UnitClosureFormData convert(UnitClosureFormDataDomain u){
        return UnitClosureFormData.builder().unitClosureFormDataId(u.getUnitClosureFormDataId()).unitClosureEvent(u.getUnitClosureEvent())
                .unitClosureMetaData(u.getUnitClosureMetaData())
                .date(u.getDate()).comment(u.getComment()).attachmentId(u.getAttachmentId())
                .build();
    }
    public static UnitClosureFormDataDomain convert(UnitClosureFormData u){
        return UnitClosureFormDataDomain.builder().unitClosureFormDataId(u.getUnitClosureFormDataId()).unitClosureEvent(u.getUnitClosureEvent())
                .unitClosureMetaData(u.getUnitClosureMetaData())
                .date(u.getDate()).comment(u.getComment()).attachmentId(u.getAttachmentId())
                .build();
    }

    public static UnitClosureFormDataDomain convert(UnitClosureFormMetaData unitClosureFormMetaData, UnitClosureEvent unitClosureEvent){
        return UnitClosureFormDataDomain.builder().unitClosureFormDataId(null).unitClosureEvent(unitClosureEvent)
                .unitClosureMetaData(unitClosureFormMetaData)
                .date(null).comment(null).attachmentId(null)
                .build();

    }

    private static void getMappingTags(ProductDetail productDetail, Product product, ProductBasicDetail productBasicDetail) {
        Set<IdCodeNameValue> regularTags = new HashSet<>();
        Set<IdCodeNameValue> nutritionTags = new HashSet<>();
        if (!CollectionUtils.isEmpty(productDetail.getProductTagMapping())) {
            getTags(productDetail.getProductTagMapping(), regularTags, nutritionTags);
            if (Objects.nonNull(product)) {
                product.setRegularTags(regularTags);
                product.setNutritionTags(nutritionTags);
            }
            if (Objects.nonNull(productBasicDetail)) {
                productBasicDetail.setRegularTags(regularTags);
                productBasicDetail.setNutritionTags(nutritionTags);
            }
        }
    }

    private static void getTags(List<ProductTagMappingDetail> tagMappings, Set<IdCodeNameValue> regularTags, Set<IdCodeNameValue> nutritionTags) {
        for (ProductTagMappingDetail tagMapping : tagMappings) {
            if (AppConstants.ACTIVE.equals(tagMapping.getMappingStatus())) {
                IdCodeNameValue idName = new IdCodeNameValue(tagMapping.getRlId(), tagMapping.getMappingValue(), tagMapping.getRtlId().toString());
                if (AppConstants.REGULAR_TAG.equalsIgnoreCase(tagMapping.getMappingType())) {
                    idName.setCode(AppConstants.REGULAR_TAG);
                    regularTags.add(idName);
                } else if (AppConstants.NUTRITION_TAG.equalsIgnoreCase(tagMapping.getMappingType())) {
                    idName.setCode(AppConstants.NUTRITION_TAG);
                    nutritionTags.add(idName);
                }
            }
        }
    }
    private static CafeType getCafeTypeFromValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return CafeType.CAFE;
        }
        try {
            return CafeType.valueOf(value.toUpperCase());
        } catch (IllegalArgumentException e) {
            return CafeType.CAFE;
        }
    }

    public static ProductCondimentItemDTO convertCondimentItemToDTO(ProductCondimentItem entity) {
        if (entity == null) {
            return null;
        }

        ProductCondimentItemDTO dto = new ProductCondimentItemDTO();
        dto.setId(entity.getId());
        dto.setGroupId(entity.getGroupId());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreationTime(entity.getCreationTime());
        dto.setName(entity.getName());
        dto.setQuantity(entity.getQuantity());
        dto.setStatus(entity.getStatus());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setUpdationTime(entity.getUpdationTime());

        return dto;
    }

    public static ProductCondimentGroupDTO convertCondimentGroupToDTO(ProductCondimentGroup entity) {
        if (entity == null) {
            return null;
        }

        ProductCondimentGroupDTO dto = new ProductCondimentGroupDTO();
        dto.setGroupId(entity.getGroupId());
        dto.setName(entity.getName());
        dto.setSource(entity.getSource());
        dto.setStatus(entity.getStatus());
        dto.setQuantity(entity.getQuantity());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCreationTime(entity.getCreationTime());
        return dto;
    }

    public static List<IdCodeName> filterAndConvertProductDetails(List<ProductDetail> productDetails, ConcurrentHashMap<Integer, List<Integer>> dimensionCodeMap) {
        List<IdCodeName> products = new ArrayList<>();
        for (ProductDetail productDetail : productDetails) {
            RefLookupType dimensionCode = productDetail.getDimensionCode();
            if (dimensionCode == null || CollectionUtils.isEmpty(dimensionCode.getRefLookups())) {
                continue;
            };

            Set<Integer> refLookUpIds = dimensionCode.getRefLookups().stream()
                    .map(RefLookup::getRlId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(refLookUpIds)) {
                continue;
            }
            dimensionCodeMap.computeIfAbsent(productDetail.getProductId(), k -> new ArrayList<>()).addAll(refLookUpIds);
            IdCodeName product = new IdCodeName();
            product.setId(productDetail.getProductId());
            product.setName(productDetail.getProductName());
            product.setStatus(productDetail.getProductStatus());
            products.add(product);
        }
            return products;
    }
}
