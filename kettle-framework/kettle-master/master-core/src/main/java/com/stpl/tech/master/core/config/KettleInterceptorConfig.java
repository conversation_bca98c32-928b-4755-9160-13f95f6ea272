package com.stpl.tech.master.core.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.stpl.tech.master.core.external.interceptor.ACLInterceptor;
import com.stpl.tech.master.core.external.interceptor.SessionAuthInterceptor;
import com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIAuthInterceptor;

@Configuration
public class KettleInterceptorConfig implements WebMvcConfigurer {
	@Autowired
	private SessionAuthInterceptor sessionAuthInterceptor;
	@Autowired
	private ACLInterceptor aclInterceptor;
	@Autowired
	private ExternalAPIAuthInterceptor externalAPIAuthInterceptor;

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(sessionAuthInterceptor).addPathPatterns("/v1/**").excludePathPatterns("/v1/external/**","/actuator/**");
		registry.addInterceptor(aclInterceptor).addPathPatterns("/**").excludePathPatterns("/v1/external/**");
		registry.addInterceptor(externalAPIAuthInterceptor).addPathPatterns("/v1/external/**");
	}
}
