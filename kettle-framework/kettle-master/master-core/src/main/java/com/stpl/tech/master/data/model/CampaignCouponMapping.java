package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "CAMPAIGN_COUPON_MAPPING")
public class CampaignCouponMapping implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -1724873883508884430L;
	private Integer campaignCouponMappingId;
    private CampaignDetailData campaignDetailData;
    private String customerType;
    private Integer journeyNumber;
    private String cloneCode;
    private String cloneCodeDesc;
    private Integer validityInDays;
    private Integer reminderDays;

    public CampaignCouponMapping() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CAMPAIGN_COUPON_MAPPING_ID", unique = true, nullable = false)
    public Integer getCampaignCouponMappingId() {
        return campaignCouponMappingId;
    }

    public void setCampaignCouponMappingId(Integer campaignCouponMappingId) {
        this.campaignCouponMappingId = campaignCouponMappingId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CAMPAIGN_ID", nullable = false)
    public CampaignDetailData getCampaignDetailData() {
        return campaignDetailData;
    }

    public void setCampaignDetailData(CampaignDetailData campaignDetailData) {
        this.campaignDetailData = campaignDetailData;
    }

    @Column(name = "CUSTOMER_TYPE")
    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    @Column(name = "JOURNEY_NUMBER")
    public Integer getJourneyNumber() {
        return journeyNumber;
    }

    public void setJourneyNumber(Integer journeyNumber) {
        this.journeyNumber = journeyNumber;
    }

    @Column(name = "CLONE_CODE")
    public String getCloneCode() {
        return cloneCode;
    }

    public void setCloneCode(String cloneCode) {
        this.cloneCode = cloneCode;
    }

    @Column(name = "CLONE_CODE_DESC")
    public String getCloneCodeDesc() {
        return cloneCodeDesc;
    }

    public void setCloneCodeDesc(String cloneCodeDesc) {
        this.cloneCodeDesc = cloneCodeDesc;
    }

    @Column(name = "VALIDITY_IN_DAYS")
    public Integer getValidityInDays() {
        return validityInDays;
    }

    public void setValidityInDays(Integer validityInDays) {
        this.validityInDays = validityInDays;
    }

    @Column(name = "REMINDER_DAYS")
    public Integer getReminderDays() {
        return reminderDays;
    }

    public void setReminderDays(Integer reminderDays) {
        this.reminderDays = reminderDays;
    }
}
