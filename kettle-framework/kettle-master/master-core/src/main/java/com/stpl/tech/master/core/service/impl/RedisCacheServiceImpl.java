package com.stpl.tech.master.core.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.service.ProductManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.domain.model.ProductImageMappingDetail;
import com.stpl.tech.master.domain.model.ProductImageMappingDetailList;
import com.stpl.tech.master.util.DataSourceConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.service.MasterMetadataService;
import com.stpl.tech.master.core.service.RedisCacheService;
import com.stpl.tech.master.core.service.model.RecipeData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.SwitchStatus;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.readonly.domain.model.UnitProductData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;

/**
 * Created by Chaayos on 01-10-2016.
 */
@Service
public class RedisCacheServiceImpl implements RedisCacheService {

    private static final Logger LOG = LoggerFactory.getLogger(RedisCacheServiceImpl.class);

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private MasterMetadataService masterMetadataService;

    @Autowired
    private TaxDataCache taxDataCache;

    @Autowired
    private ProductManagementService productManagementService;

    @Override
    public List<com.stpl.tech.master.domain.model.Unit> getAllUnits() throws DataNotFoundException {
        List<com.stpl.tech.master.domain.model.Unit> units = new ArrayList<>();
        masterDataCache.getAllUnits().forEach(unitBasicDetail -> {
            try {
                if (unitBasicDetail.getCategory().equals(UnitCategory.CAFE)
                    || unitBasicDetail.getCategory().equals(UnitCategory.DELIVERY)
                    || unitBasicDetail.getCategory().equals(UnitCategory.TAKE_AWAY)) {
                    units.add(getUnit(unitBasicDetail.getId()));
                }
            } catch (Exception e) {
                LOG.error("Error while getting all units for redis cache", e);
            }
        });
        return units;
    }

    @Override
    public List<com.stpl.tech.master.domain.model.Location> getAllLocations() throws DataNotFoundException {
        List<com.stpl.tech.master.domain.model.Location> locations = new ArrayList<>();
        masterDataCache.getAllUnits().forEach(unitBasicDetail -> {
            try {
                masterDataCache.getAllLocations().forEach((s, location) -> {
                    locations.add(location);
                });
            } catch (Exception e) {
                LOG.error("Error while getting all states for redis cache", e);
            }
        });
        return locations;
    }

    @Override
    public List<KioskCompanyDetails> getAllKioskCompanies() {
        Optional<List<KioskCompanyDetails>> companies = Optional
            .ofNullable(masterMetadataService.getAllKioskCompanies());
        if (companies.isPresent()) {
            return companies.get().stream()
                .filter(kioskCompanyDetails -> kioskCompanyDetails.getCompanyStatus().equals(SwitchStatus.ACTIVE))
                .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public Unit getUnit(int unitId) {
        return masterDataCache.getUnit(unitId);
    }

    @Override
    public TransactionMetadata getTransactionMetaData(int unitId) throws DataNotFoundException {
        return masterMetadataService.getTransactionData(true);
    }

    @Override
    public List<RecipeDetail> getRecipe(int productId) {
        List<RecipeDetail> recipeDetails = new ArrayList<>();
        recipeCache.getRecipes().forEach((productRecipeKey, recipeDetail) -> {
            if (productRecipeKey.getProductId() == productId) {
                recipeDetails.add(recipeDetail);
            }
        });
        return recipeDetails;
    }

    @Override
    public RecipeData getRecipe(RecipeData recipes) {
        Set<Integer> requested = new HashSet<>(recipes.getProductIds());
        recipeCache.getRecipes().forEach((productRecipeKey, recipeDetail) -> {
            if (AppConstants.RECIPE_PROFILE_P0.equals(productRecipeKey.getProfile())
                && requested.contains(productRecipeKey.getProductId())) {
                if (!recipes.getRecipes().containsKey(productRecipeKey.getProductId())) {
                    recipes.getRecipes().put(productRecipeKey.getProductId(), new HashMap<>());
                }
                recipes.getRecipes().get(productRecipeKey.getProductId()).put(productRecipeKey.getDimension(),
                    recipeDetail);
            }
        });
        return recipes;
    }

    @Override
    public RecipeDetail getRecipeDetail(int recipeId) {
        RecipeDetail r = null;
        for (RecipeDetail rd : recipeCache.getRecipes().values()) {
            if (rd.getRecipeId() == recipeId) {
                r = rd;
                break;
            }
        }
        return r;
    }

    @Override
    public List<IdCodeName> getProducts() {
        List<IdCodeName> products = new ArrayList<>();
        masterDataCache.getProductBasicDetails().values().stream().forEach(productBasicDetail -> {
            IdCodeName idCodeName = productBasicDetail.getDetail();
            idCodeName.setStatus(productBasicDetail.getStatus().value());
            products.add(idCodeName);
        });
        return products;
    }

    @Override
    public List<UnitBasicDetail> getAllUnitBasicDetail() {
        List<UnitBasicDetail> ubd = new ArrayList<UnitBasicDetail>();
        for (UnitBasicDetail unit : masterDataCache.getAllUnits()) {
            if (!unit.isPartnerPriced()) {
                ubd.add(unit);
            }
        }
        return ubd;
    }

    @Override
    public UnitBasicDetail getUnitBasicDetail(int unitId) {
        return masterDataCache.getUnitBasicDetail(unitId);
    }

    @Override
    public List<IdCodeName> getWebCategories() throws DataNotFoundException {
        List<ListData> data = masterMetadataService.getAllListData(AppConstants.RTL_GROUP_WEB_CATEGORY, false);
        if (data != null && !data.isEmpty()) {
            return data.get(0).getContent();
        }
        return null;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.service.RedisCacheService#getUnitProducts(int)
     */
    @Override
    public UnitProductData getUnitProducts(int unitId) {
        Unit unit = masterDataCache.getUnit(unitId);
        Collection<Product> products = masterDataCache.getUnitProductDetails(unitId);
        // return masterDataCache.getUnitProductDetails(unitId);
        return getResult(unit, products);
    }

    // TODO remove this method as duplicate exists in UnitManagementResources
    private UnitProductData getResult(Unit unit, Collection<Product> products) {
        UnitProductData result = new UnitProductData();
        Collection<com.stpl.tech.master.readonly.domain.model.ProductVO> set = new TreeSet<>();
        if (unit.getLocation() != null) {
            int stateId = unit.getLocation().getState().getId();
            Set<String> codes = new HashSet<>();
            products.forEach(product -> {
                if (product.getWebType() != null) {
                    set.add(new com.stpl.tech.master.readonly.domain.model.ProductVO(product));
                    codes.add(product.getTaxCode());
                }
            });
            codes.forEach(taxCode ->
                result.getTaxes().add(new TaxDataVO(taxDataCache.getTaxData(stateId, taxCode)))
            );
            result.getProducts().addAll(set);
        }
        return result;
    }

    @Override
    public Map<Integer, Map<Integer, List<ProductRecipeKey>>> getAllUnitProductPriceProfiles() {
        return productManagementService.getUnitProductPriceProfiles();
    }

    @Override
    public Map<Integer, List<ProductRecipeKey>> getProductPriceProfilesForUnit(int unitId) {
        return productManagementService.getProductPriceProfilesForUnit(unitId);
    }

    @Override
    public ProductImageMappingDetailList getAllProductImages() {
        return productManagementService.getAllProductImages();
    }

    @Override
    public ProductImageMappingDetailList getAllProductImages(List<Integer> list) {
        return productManagementService.getAllProductImages(list);
    }

}