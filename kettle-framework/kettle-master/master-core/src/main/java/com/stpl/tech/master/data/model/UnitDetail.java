/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import org.hibernate.annotations.Filter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

/**
 * UnitDetail generated by hbm2java
 */
@Entity
@Table(name = "UNIT_DETAIL")
public class UnitDetail implements Serializable {

	private static final long serialVersionUID = -1921810774766928272L;

	private Integer unitId;
	private BusinessDivision businessDivision;
	private AddressInfo addressInfo;
	private String unitName;
	private String unitRegion;
	private String unitEmail;
	private String unitCategory;
	private String unitSubCategory;
	private Date startDate;
	private String unitStatus;
	private String tin;
	private String fssai;
	private String gstin;
	private Integer noOfTerminals;
	private int noOfTakeawayTerminals;
	private EmployeeDetail unitManager;
	private String referenceName;
	private String communicationChannel;
	private Integer creditAccountId;
	private LocationDetail location;
	private List<UnitProductMapping> unitProductMappings = new ArrayList<UnitProductMapping>(0);
	private List<UnitAttributeMapping> unitAttributeMappings = new ArrayList<UnitAttributeMapping>(0);
	private List<UnitTaxMapping> unitTaxMappings = new ArrayList<UnitTaxMapping>(0);
	private List<UnitPaymentModeMapping> unitPaymentMappings = new ArrayList<UnitPaymentModeMapping>(0);
	private List<BusinessHours> businessHours = new ArrayList<BusinessHours>(0);

	private List<UnitPriceProfileMapping> unitPriceProfileMappings = new ArrayList<>();
	private String businessType;
	private CompanyDetail companyDetail;
	private Integer cafeManager;
	private String isLive;
	private Date handoverDate;
	private String cafeAppStatus;
	private String cafeNeoStatus;
	private String shortName;
	private String costCenter;
	private String consideredForAccounting;
	private String shortCode;
	private Integer clonedFrom;
	private Integer salesClonedFrom;
	private Date probableOpeningDate;
	private Integer pricingProfile;
	private String unitZone;
	private String f9Enabled;
	private String isClosed;

	private Integer posVersion;
	private String faDaycloseEnabled;

	private Integer unitCafeManager;

	private Date lastHandoverDate;

	private String lastHandoverFrom;
	private String varianceAcknowledgementData;
	private String closureStatus;

	private String isHotSpotLive;

	private String assemblyStrictMode;

	private String assemblyOtpMode;

	private String customerLogin;

	//mapped by field will not create any FK and Which has a ref of Fk is owing one or parent
	private MonkXTwoMetadata monkXTwoMetadata;

	public UnitDetail() {
	}

	public UnitDetail(BusinessDivision businessDivision, AddressInfo addressInfo, String unitName, String unitRegion,
			String unitEmail, String unitCategory, Date startDate, String unitStatus, String tin, int noOfTerminals,
			int noOfTakeawayTerminals, String unitSubCategory, String shortName, String varianceAcknowledgementData,String closure) {
		this.businessDivision = businessDivision;
		this.addressInfo = addressInfo;
		this.unitName = unitName;
		this.unitRegion = unitRegion;
		this.unitEmail = unitEmail;
		this.unitCategory = unitCategory;
		this.unitSubCategory = unitSubCategory;
		this.startDate = startDate;
		this.unitStatus = unitStatus;
		this.gstin = tin;
		this.noOfTerminals = noOfTerminals;
		this.noOfTakeawayTerminals = noOfTakeawayTerminals;
		this.unitSubCategory = unitSubCategory;
		this.shortName = shortName;
		this.varianceAcknowledgementData = varianceAcknowledgementData;
		this.closureStatus = closure;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_ID", unique = true, nullable = false)
	public Integer getUnitId() {
		return this.unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "BUSINESS_DIV_ID", nullable = false)
	public BusinessDivision getBusinessDivision() {
		return this.businessDivision;
	}

	public void setBusinessDivision(BusinessDivision businessDivision) {
		this.businessDivision = businessDivision;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ADDR_ID", nullable = false)
	public AddressInfo getAddressInfo() {
		return this.addressInfo;
	}

	public void setAddressInfo(AddressInfo addressInfo) {
		this.addressInfo = addressInfo;
	}

	@Column(name = "UNIT_NAME", nullable = false)
	public String getUnitName() {
		return this.unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	@Column(name = "UNIT_REGION", nullable = false, length = 25)
	public String getUnitRegion() {
		return this.unitRegion;
	}

	public void setUnitRegion(String unitRegion) {
		this.unitRegion = unitRegion;
	}

	@Column(name = "SHORT_NAME")
	public String getShortName() {
		return shortName;
	}
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	@Column(name = "UNIT_EMAIL", nullable = false, length = 50)
	public String getUnitEmail() {
		return this.unitEmail;
	}

	public void setUnitEmail(String unitEmail) {
		this.unitEmail = unitEmail;
	}

	@Column(name = "UNIT_CATEGORY", nullable = false, length = 30)
	public String getUnitCategory() {
		return this.unitCategory;
	}

	public void setUnitCategory(String unitCategory) {
		this.unitCategory = unitCategory;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "START_DATE", nullable = true, length = 19)
	public Date getStartDate() {
		return this.startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Column(name = "UNIT_STATUS", nullable = false, length = 15)
	public String getUnitStatus() {
		return this.unitStatus;
	}

	public void setUnitStatus(String unitStatus) {
		this.unitStatus = unitStatus;
	}

	@Column(name = "NO_OF_TERMINALS", nullable = false)
	public Integer getNoOfTerminals() {
		return this.noOfTerminals;
	}

	public void setNoOfTerminals(Integer noOfTerminals) {
		this.noOfTerminals = noOfTerminals;
	}

	@Column(name = "NO_OF_TA_TERMINALS", nullable = true)
	public int getNoOfTakeawayTerminals() {
		return noOfTakeawayTerminals;
	}

	public void setNoOfTakeawayTerminals(int noOfTakeawayTerminals) {
		this.noOfTakeawayTerminals = noOfTakeawayTerminals;
	}

	@Column(name = "TIN", nullable = true, length = 15)
	public String getTin() {
		return this.tin;
	}

	public void setTin(String tin) {
		this.tin = tin;
	}

	@Column(name = "GSTIN", nullable = true, length = 20)
	public String getGstin() {
		return this.gstin;
	}

	public void setGstin(String gstin) {
		this.gstin = gstin;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	@OrderBy("productDetail.productId asc")
	public List<UnitProductMapping> getUnitProductMappings() {
		return this.unitProductMappings;
	}

	public void setUnitProductMappings(List<UnitProductMapping> unitProductMappings) {
		this.unitProductMappings = unitProductMappings;
	}

	@OneToMany(fetch = FetchType.LAZY,mappedBy = "unitId")
	@Filter(name = "activeFilter", condition = "mappingStatus = 'ACTIVE'")
	public List<UnitPriceProfileMapping> getUnitPriceProfileMappings() {
		return unitPriceProfileMappings;
	}

	public void setUnitPriceProfileMappings(List<UnitPriceProfileMapping> unitPriceProfileMappings) {
		this.unitPriceProfileMappings = unitPriceProfileMappings;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	public List<UnitAttributeMapping> getUnitAttributeMappings() {
		return unitAttributeMappings;
	}

	public void setUnitAttributeMappings(List<UnitAttributeMapping> unitAttributeMappings) {
		this.unitAttributeMappings = unitAttributeMappings;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	@OrderBy("taxProfile.taxProfileId asc")
	public List<UnitTaxMapping> getUnitTaxMappings() {
		return this.unitTaxMappings;
	}

	public void setUnitTaxMappings(List<UnitTaxMapping> unitTaxMappings) {
		this.unitTaxMappings = unitTaxMappings;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	@OrderBy("paymentMode.paymentModeId asc")
	public List<UnitPaymentModeMapping> getUnitPaymentMappings() {
		return this.unitPaymentMappings;
	}

	public void setUnitPaymentMappings(List<UnitPaymentModeMapping> unitPaymentMappings) {
		this.unitPaymentMappings = unitPaymentMappings;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitDetail")
	@OrderBy("dayOfTheWeekNumber asc")
	public List<BusinessHours> getBusinessHours() {
		return this.businessHours;
	}

	public void setBusinessHours(List<BusinessHours> businessHours) {
		this.businessHours = businessHours;
	}

	@Column(name = "UNIT_SUB_CATEGORY", nullable = false)
	public String getUnitSubCategory() {
		return unitSubCategory;
	}

	public void setUnitSubCategory(String unitSubCategory) {
		this.unitSubCategory = unitSubCategory;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_MANAGER", nullable = true)
	public EmployeeDetail getUnitManager() {
		return unitManager;
	}

	public void setUnitManager(EmployeeDetail unitManager) {
		this.unitManager = unitManager;
	}

	@Column(name = "UNIT_REFERENCE_NAME", nullable = true)
	public String getReferenceName() {
		return referenceName;
	}

	public void setReferenceName(String referenceName) {
		this.referenceName = referenceName;
	}

	@Column(name = "COMMUNICATION_CHANNEL", nullable = true)
	public String getCommunicationChannel() {
		return communicationChannel;
	}

	public void setCommunicationChannel(String communicationChannel) {
		this.communicationChannel = communicationChannel;
	}

	@Column(name = "CREDIT_ACCOUNT_ID", nullable = true)
	public Integer getCreditAccountId() {
		return creditAccountId;
	}

	public void setCreditAccountId(Integer creditAccountId) {
		this.creditAccountId = creditAccountId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "LOCATION_DETAIL_ID", nullable = false)
	public LocationDetail getLocation() {
		return location;
	}

	public void setLocation(LocationDetail location) {
		this.location = location;
	}

	@Column(name = "BUSINESS_TYPE", nullable = true)
	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COMPANY_ID", nullable = false)
	public CompanyDetail getCompanyDetail() {
		return companyDetail;
	}

	public void setCompanyDetail(CompanyDetail companyDetail) {
		this.companyDetail = companyDetail;
	}

	@Column(name = "CAFE_MANAGER")
	public Integer getCafeManager() {
		return cafeManager;
	}

	public void setCafeManager(Integer cafeManager) {
		this.cafeManager = cafeManager;
	}

	@Column(name = "IS_LIVE")
	public String getIsLive() {
		return isLive;
	}

	public void setIsLive(String isLive) {
		this.isLive = isLive;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "HANDOVER_DATE")
	public Date getHandoverDate() {
		return this.handoverDate;
	}

	public void setHandoverDate(Date handoverDate) {
		this.handoverDate = handoverDate;
	}

	@Column(name = "CAFE_APP_STATUS")
	public String getCafeAppStatus() {
		return cafeAppStatus;
	}

	public void setCafeAppStatus(String cafeAppStatus) {
		this.cafeAppStatus = cafeAppStatus;
	}

	@Column(name = "CAFE_NEO_STATUS")
	public String getCafeNeoStatus() {
		return cafeNeoStatus;
	}
	public void setCafeNeoStatus(String cafeNeoStatus) {
		this.cafeNeoStatus = cafeNeoStatus;
	}

	@Column(name = "COST_CENTER")
	public String getCostCenter() {
		return costCenter;
	}

	public void setCostCenter(String costCenter) {
		this.costCenter = costCenter;
	}

	@Column(name = "CONSIDERED_FOR_ACCOUNTING")
	public String getConsideredForAccounting() {
		return consideredForAccounting;
	}

	public void setConsideredForAccounting(String consideredForAccounting) {
		this.consideredForAccounting = consideredForAccounting;
	}

	@Column(name = "FSSAI")
	public String getFssai() {
		return fssai;
	}

	public void setFssai(String fssai) {
		this.fssai = fssai;
	}

	@Column(name = "SHORT_CODE")
	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	@Column(name = "CLONED_FROM")
	public Integer getClonedFrom() {
		return clonedFrom;
	}

	public void setClonedFrom(Integer clonedFrom) {
		this.clonedFrom = clonedFrom;
	}

	@Column(name = "SALES_CLONED_FROM")
	public Integer getSalesClonedFrom() {
		return salesClonedFrom;
	}

	public void setSalesClonedFrom(Integer salesClonedFrom) {
		this.salesClonedFrom = salesClonedFrom;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "PROBABLE_OPENING_DATE")
	public Date getProbableOpeningDate() {
		return probableOpeningDate;
	}

	public void setProbableOpeningDate(Date probableOpeningDate) {
		this.probableOpeningDate = probableOpeningDate;
	}

	@Column(name = "PRICING_PROFILE")
	public Integer getPricingProfile() {
		return pricingProfile;
	}

	public void setPricingProfile(Integer pricingProfile) {
		this.pricingProfile = pricingProfile;
	}

	@Column(name = "UNIT_ZONE")
	public String getUnitZone() {
		return unitZone;
	}

	public void setUnitZone(String unitZone) {
		this.unitZone = unitZone;
	}

	@Column(name = "F9_ENABLED")
	public String getF9Enabled() {
		return f9Enabled;
	}

	public void setF9Enabled(String f9Enabled) {
		this.f9Enabled = f9Enabled;
	}


	@Column(name = "IS_CLOSED")
	public String getIsClosed() {
		return isClosed;
	}

	public void setIsClosed(String isClosed) {
		this.isClosed = isClosed;
	}


	@Column(name = "POS_VERSION")
	public Integer getPosVersion() {
		return posVersion;
	}

	public void setPosVersion(Integer posVersion) {
		this.posVersion = posVersion;
	}

	@Column(name = "FA_DAYCLOSE_ENABLED")
	public String getFaDaycloseEnabled() {
		return faDaycloseEnabled;
	}

	public void setFaDaycloseEnabled(String faDaycloseEnabled) {
		this.faDaycloseEnabled = faDaycloseEnabled;
	}

	@Column(name = "UNIT_CAFE_MANAGER")
	public Integer getUnitCafeManager() {
		return unitCafeManager;
	}

	public void setUnitCafeManager(Integer unitCafeManager) {
		this.unitCafeManager = unitCafeManager;
	}

	@Column(name = "LAST_HANDOVER_TIME")
	public Date getLastHandoverDate() {
		return lastHandoverDate;
	}

	public void setLastHandoverDate(Date lastHandoverDate) {
		this.lastHandoverDate = lastHandoverDate;
	}

	@Column(name = "LAST_HANDOVER_FROM")
	public String getLastHandoverFrom() {
		return lastHandoverFrom;
	}

	public void setLastHandoverFrom(String lastHandoverFrom) {
		this.lastHandoverFrom = lastHandoverFrom;
	}

	@Column(name = "IS_HOTSPOT_LIVE")
	public String getIsHotSpotLive() {
		return isHotSpotLive;
	}

	public void setIsHotSpotLive(String isHotSpotLive) {
		this.isHotSpotLive = isHotSpotLive;
	}

	@Column(name = "VARIANCE_ACKNOWLEDGEMENT_REQUIRED")
	public String getVarianceAcknowledgementData() {
		return varianceAcknowledgementData;
	}

	public void setVarianceAcknowledgementData(String varianceAcknowledgementData) {
		this.varianceAcknowledgementData = varianceAcknowledgementData;
	}
	@Column(name = "ASSEMBLY_STRICT_MODE")
	public String getAssemblyStrictMode() {
		return assemblyStrictMode;
	}

	public void setAssemblyStrictMode(String  assemblyStrictMode) {
		this.assemblyStrictMode = assemblyStrictMode;
	}

	@Column(name = "CLOSURE_STATUS")
	public String getClosureStatus() {
		return closureStatus;
	}

	public void setClosureStatus(String closureStatus) {
		this.closureStatus = closureStatus;
	}

	@Column(name = "ASSEMBLY_OTP_MODE")
	public String getAssemblyOtpMode() {
		return assemblyOtpMode;
	}

	public void setAssemblyOtpMode(String assemblyOtpMode) {
		this.assemblyOtpMode = assemblyOtpMode;
	}

	@Column(name = "CUSTOMER_LOGIN")
	public String getCustomerLogin() {
		return customerLogin;
	}

	public void setCustomerLogin(String customerLogin) {
		this.customerLogin = customerLogin;
	}

	@OneToOne(mappedBy = "unitDetail", fetch = FetchType.LAZY)
	public MonkXTwoMetadata getMonkXTwoMetadata() {
		return monkXTwoMetadata;
	}

	public void setMonkXTwoMetadata(MonkXTwoMetadata monkXTwoMetadata) {
		this.monkXTwoMetadata = monkXTwoMetadata;
	}


}
