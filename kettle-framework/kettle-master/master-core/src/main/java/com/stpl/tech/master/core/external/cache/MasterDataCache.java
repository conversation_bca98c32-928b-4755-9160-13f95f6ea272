/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.cache;

import com.hazelcast.collection.IList;
import com.hazelcast.collection.ISet;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import com.stpl.tech.master.data.model.RefLookupInfo;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.data.model.UnitClosureState;
import com.stpl.tech.master.data.model.UnitIpAddressData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.data.model.UnitProductAsKey;
import com.stpl.tech.master.data.model.UnitProductPriceCategoryDomain;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.domain.model.AddonData;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CancellationReason;
import com.stpl.tech.master.domain.model.CashMetadata;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.CityLocalityKey;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.CondimentGroupData;
import com.stpl.tech.master.domain.model.CondimentItemData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CustomerAppliedCouponDetail;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.Department;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.Division;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.EntityAliasKey;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskLocationDetails;
import com.stpl.tech.master.domain.model.KioskMachine;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.PriceProfileDetail;
import com.stpl.tech.master.domain.model.PriceProfileKey;
import com.stpl.tech.master.domain.model.PriceProfileRangeValueDetail;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.ProductDimensionKey;
import com.stpl.tech.master.domain.model.RestaurantPartnerKey;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitChannelPartnerMapping;
import com.stpl.tech.master.domain.model.UnitClosureStateDomain;
import com.stpl.tech.master.domain.model.UnitContactDetails;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.UnitMenuTypePrice;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitPartnerMenuMapping;
import com.stpl.tech.master.domain.model.UnitProductKey;
import com.stpl.tech.master.enums.ConsumableSource;
import com.stpl.tech.master.locality.model.LocalityMapping;
import com.stpl.tech.master.readonly.domain.model.ProductPriceVO;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.transaction.CashMetadataType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;

@Service
public class MasterDataCache {

    private static final Logger LOG = LoggerFactory.getLogger(MasterDataCache.class);

    @Autowired
    @Qualifier(value = "MasterHazelCastInstance")
    private HazelcastInstance instance;

    private IMap<ListTypes, Map<Integer, IdCodeName>> listMappingData;
    private IMap<Integer, ChannelPartnerDetail> channelPartnerMap;
    private IMap<Integer, UnitChannelPartnerMapping> unitChannelPartnerMapping;
    private IMap<UnitPartnerBrandKey, List<UnitPartnerMenuMapping>> unitPartnerMenuMappingsMap;
    private IMap<Integer, PaymentMode> paymentModes;
    private IMap<Integer, Unit> units;
    private IMap<Integer, Company> companies;
    private MultiMap<UnitCategory, UnitBasicDetail> unitsMetadata;
    private MultiMap<UnitCategory, CancellationReason> cancellationReason;
    private IMap<Integer, Integer> deliveryUnits;
    private IMap<Integer, Integer> partnerUnits;
    private IMap<Integer, UnitBasicDetail> unitsBasicDetails;
    private MultiMap<ListTypes, IdCodeName> listData;
    private IMap<Integer, AddonData> addonData;
    private IMap<Integer, ListData> listCategoryData;// TreeMap
    private IMap<String, ListData> listAdjustmentCommentData;// TreeMap
    private IMap<Integer, ListData> dimensionProfileData;// TreeMap
    private IMap<Integer, Product> productDetails;// TreeMap
    private MultiMap<Integer, Product> unitProductDetails;// TreeMap
    private Map<Integer, Map<Integer,Map<Pair<BigDecimal,String>,String>>> unitProductAlias;// TreeMap
    private MultiMap<Integer, ProductVO> unitProductTrimmedDetails;// TreeMap
    private IMap<Integer, Map<String, Boolean>> consumablesUnitProductFlagMap;
    private IMap<Integer, ProductBasicDetail> productBasicDetails;// TreeMap
    private IMap<String, AddonData> parcelCodes;
    private MultiMap<Integer, TaxProfile> unitTaxProfiles;
    private IMap<Integer, Division> divisions;// TreeMap
    private IMap<Integer, Department> departments;// TreeMap
    private IMap<Integer, Designation> designations;// TreeMap
    private MultiMap<Integer, IdCodeName> productAddons;
    private IList<TaxProfile> taxProfiles;
    private IMap<Integer, DenominationDetail> denominations;
    private IMap<Integer, String> employees;
    private IMap<Integer, EmployeeBasicDetail> employeeBasicDetails;
    private ISet<Integer> employeeMealProducts;
    private ISet<Integer> criticalSCMProducts;
    private ISet<String> employeeMealDimensions;
    private IMap<Integer, List<UnitHours>> operationalHours; // Business Hours for unit
    private IMap<Integer, KioskLocationDetails> kioskLocations;
    private IMap<Integer, KioskMachine> kioskMachines;
    private IMap<Integer, KioskCompanyDetails> kioskCompanies;
    private IMap<String, LocalityMapping> localities;
    private IMap<CityLocalityKey, LocalityMapping> localityMappingMap;
    private IMap<String, Location> locations;
    private IMap<Integer, Location> locationsData;
    private IMap<String, State> states;
    private IMap<Integer, IdCodeName> countries;
    private MultiMap<Integer, IdCodeName> countryToState;
    private MultiMap<Integer, Location> stateToLocation;
    private ISet<IdCodeName> cityList;
    private IMap<Integer, Set<IdCodeName>> cityUnitMapping;
    private IMap<String, String> tokenizedApis;
    private IMap<Integer, BigDecimal> paymentModesCommission;
    private IMap<String, ExternalPartnerDetail> externalPartnerMap;
    private IMap<String, ListData> itemPerTicket;// TreeMap
    private IMap<CashMetadataType, CashMetadata> chaayosCashMetadata;
    private IMap<Integer, Brand> brandMetaData;
    private IMap<UnitPartnerBrandKey, UnitPartnerBrandMappingData> unitPartnerBrandMappingMetaData;
    private IMap<Integer, List<UnitToPartnerEdcMapping>> unitPartnerEdcMappingMetadata;
    private MultiMap<Integer, UnitPartnerBrandMappingData> unitwisePartnerBrandMappingMetaData;
    private IMap<RestaurantPartnerKey, UnitPartnerBrandMappingData> unitPartnerBrandMappingMetaData2;
    private IMap<EntityAliasKey, EntityAliasMappingData> entityAliasMappingData;
    private IMap<UnitPartnerBrandKey, Map<UnitPartnerBrandMappingMetadataType, String>> unitPartnerBrandMetadataMap;
    private IMap<UnitPartnerBrandKey, Map<MenuType, PriceProfileDetail>> priceProfileMap;
    private IMap<Integer,Product> subscriptionProduct;
    private IMap<String,Pair<CouponDetail,Product>> subscriptionSkuCodeDetail;
    private IMap<String,String> regionMap;
    private ISet<String> unitsEmailIds;
    private ISet<String> workStationsStationCategories;

    private IMap<CacheReferenceType,String> cacheReferenceValue;

    private IMap<String,List<CondimentGroupData> > sourceCondimentDataMapping;
    private IMap<Integer,List<CondimentItemData>> groupItemCondimentMapping;
    private IMap<String, UnitIpAddressData> unitTerminalIpAddressMap;

    private IMap<Integer, Address> addressInfo;

    private IMap<Integer, UnitContactDetails> unitContactDetailsIMap;

    private IMap<Long, UnitClosureState> unitClosureStateIMap;

    private IMap<String, CustomerAppliedCouponDetail> customerAppliedCouponDetailMap ;

    private IMap< String,Map<Integer,  Set<UnitProductPriceCategoryDomain>>> priceCategoryWiseProductsPrice ;
    private IMap<Integer,Boolean> unitPriceProfileUpdateMap;
    private IMap<Integer,Map<String, DroolVersionDomain>> unitDroolVersionMapping ;

    private IMap<String,Integer> milkVariantMap ;
    private IMap<PriceProfileKey,Map<ProductDimensionKey,BigDecimal>> productPriceMapByProfile;
    private IMap<Integer, ExpenseMetadata> expenseMetadataIMap;
    private IMap<UnitProductAsKey, RefLookupInfo> workstationRoutingCache;

    private Map<Integer, List<Brand>> companyBrandsMap;
    private Map<Integer, List<Brand>> unitBrandsMap;
    private Map<Integer, List<Integer>> brandUnitsMap;
    private ISet<String> dreamFolksVoucherCodesUsed;

    public MasterDataCache() {

    }

    @PostConstruct
    public void createCache() {
        LOG.info("POST-CONSTRUCT MasterDataCache - STARTED");
        LOG.info("$$$$$$$$$$$$$$$Creating Cache$$$$$$$$$$$$$$$");
        listMappingData = instance.getMap("MasterDataCache:listMappingData");
        channelPartnerMap = instance.getMap("MasterDataCache:channelPartnerMap");
        unitChannelPartnerMapping = instance.getMap("MasterDataCache:unitChannelPartnerMapping");
        unitPartnerMenuMappingsMap = instance.getMap("MasterDataCache:unitPartnerMenuMappingsMap");
        paymentModes = instance.getMap("MasterDataCache:paymentModes");
        paymentModesCommission = instance.getMap("MasterDataCache:paymentModesCommission");
        units = instance.getMap("MasterDataCache:units");
        companies = instance.getMap("MasterDataCache:companies");
        unitsMetadata = instance.getMultiMap("MasterDataCache:unitsMetadata");
        cancellationReason = instance.getMultiMap("MasterDataCache:cancellationReason");
        unitsBasicDetails = instance.getMap("MasterDataCache:unitsBasicDetails");
        listData = instance.getMultiMap("MasterDataCache:listData");
        addonData = instance.getMap("MasterDataCache:addonData");// TreeMap
        listCategoryData = instance.getMap("MasterDataCache:listCategoryData");// TreeMap
        listAdjustmentCommentData = instance.getMap("MasterDataCache:listAdjustmentCommentData");// TreeMap
        dimensionProfileData = instance.getMap("MasterDataCache:dimensionProfileData");// TreeMap
        productDetails = instance.getMap("MasterDataCache:productDetails");// TreeMap
        expenseMetadataIMap = instance.getMap("MasterDataCache:expenseMetadataIMap");
        unitProductDetails = instance.getMultiMap("MasterDataCache:unitProductDetails");// MultiMap
        unitProductAlias = instance.getMap("MasterDataCache:unitProductAlias");// MultiMap
        unitProductTrimmedDetails = instance.getMultiMap("MasterDataCache:unitProductTrimmedDetails");// MultiMap
        consumablesUnitProductFlagMap=instance.getMap("MasterDataCache:consumablesUnitProductFlagMap");
        productBasicDetails = instance.getMap("MasterDataCache:productBasicDetails");// TreeMap
        parcelCodes = instance.getMap("MasterDataCache:parcelCodes");
        unitTaxProfiles = instance.getMultiMap("MasterDataCache:unitTaxProfiles");
        divisions = instance.getMap("MasterDataCache:divisions");// TreeMap
        departments = instance.getMap("MasterDataCache:departments");// TreeMap
        designations = instance.getMap("MasterDataCache:designations");// TreeMap
        productAddons = instance.getMultiMap("MasterDataCache:productAddons");
        taxProfiles = instance.getList("MasterDataCache:taxProfiles");
        denominations = instance.getMap("MasterDataCache:denominations");
        employees = instance.getMap("MasterDataCache:employees");
        employeeBasicDetails = instance.getMap("MasterDataCache:employeeBasicDetails");
        criticalSCMProducts = instance.getSet("MasterDataCache:criticalSCMProducts");
        employeeMealProducts = instance.getSet("MasterDataCache:employeeMealProducts");
        employeeMealDimensions = instance.getSet("MasterDataCache:employeeMealDimensions");
        operationalHours = instance.getMap("MasterDataCache:operationalHours");
        kioskLocations = instance.getMap("MasterDataCache:kioskLocations");
        kioskMachines = instance.getMap("MasterDataCache:kioskMachines");
        kioskCompanies = instance.getMap("MasterDataCache:kioskCompanies");
        localities = instance.getMap("MasterDataCache:localities");
        localityMappingMap = instance.getMap("MasterDataCache:localityMappingMap");
        locations = instance.getMap("MasterDataCache:locations");
        locationsData = instance.getMap("MasterDataCache:locationsData");
        states = instance.getMap("MasterDataCache:states");
        countries = instance.getMap("MasterDataCache:countries");
        countryToState = instance.getMultiMap("MasterDataCache:countryToState");
        stateToLocation = instance.getMultiMap("MasterDataCache:stateToLocation");
        cityList = instance.getSet("MasterDataCache:cityList");
        cityUnitMapping = instance.getMap("MasterDataCache:cityUnitMapping");
        tokenizedApis = instance.getMap("MasterDataCache:tokenizedApis");
        deliveryUnits = instance.getMap("MasterDataCache:deliveryUnits");
        partnerUnits = instance.getMap("MasterDataCache:partnerUnits");
        externalPartnerMap = instance.getMap("MasterDataCache:externalPartnerMap");
        itemPerTicket = instance.getMap("MasterDataCache:itemPerTicket");
        brandMetaData = instance.getMap("MasterDataCache:brandMetaData");
        unitPartnerEdcMappingMetadata = instance.getMap("MasterDataCache:unitPartnerEdcMappingMetadata");
        unitPartnerBrandMappingMetaData = instance.getMap("MasterDataCache:unitPartnerBrandMappingMetaData");
        unitPartnerBrandMappingMetaData2 = instance.getMap("MasterDataCache:unitPartnerBrandMappingMetaData2");
        unitwisePartnerBrandMappingMetaData = instance.getMultiMap("MasterDataCache:unitwisePartnerBrandMappingMetaData");
        entityAliasMappingData = instance.getMap("MasterDataCache:entityAliasMappingData");
        chaayosCashMetadata = instance.getMap("MasterDataCache:chaayosCashMetadata");
        unitPartnerBrandMetadataMap = instance.getMap("MasterDataCache:unitPartnerBrandMetadataMap");
        priceProfileMap = instance.getMap("MasterDataCache:priceProfileMap");
        subscriptionProduct = instance.getMap("MasterDataCache:subscriptionProduct");
        subscriptionSkuCodeDetail = instance.getMap("MasterDataCache:subscriptionSkuCodeDetail");
        regionMap = instance.getMap("MasterDataCache:regionMap");
        unitsEmailIds = instance.getSet("MasterDataCache:unitsEmailIds");
        workStationsStationCategories = instance.getSet("MasterDataCache:workStationsStationCategories");
        cacheReferenceValue = instance.getMap("MasterDataCache:cacheReferenceValue");
        sourceCondimentDataMapping = instance.getMap("MasterDataCache:sourceCondimentDataMapping");
        groupItemCondimentMapping = instance.getMap("MasterDataCache:groupItemCondimentMapping");
        unitTerminalIpAddressMap = instance.getMap("MasterDataCache:unitTerminalDataMap");

        addressInfo = instance.getMap("MasterDataCache:addressInfo");
        unitContactDetailsIMap = instance.getMap("MasterDataCache:unitContacts");
        unitClosureStateIMap = instance.getMap("MasterDataCache:unitClosureStateIMap");
        customerAppliedCouponDetailMap = instance.getMap("MasterDataCache:customerAppliedCouponDetailMap");
        priceCategoryWiseProductsPrice = instance.getMap("MasterDataCache:priceCategoryWiseProductsPriceMap");
        unitPriceProfileUpdateMap = instance.getMap("MasterDataCache:unitPriceProfileUpdateMap");
        unitDroolVersionMapping = instance.getMap("MasterDataCache:unitDroolVersionMapping");
        milkVariantMap = instance.getMap("MasterDataCache:specialMilkVariant");
        productPriceMapByProfile = instance.getMap("MasterDataCache:productPriceMapByProfile");
        workstationRoutingCache = instance.getMap("MasterDataCache:workstationRoutingCache");

        companyBrandsMap = instance.getMap("MasterDataCache:companyBrandsMap");
        unitBrandsMap = instance.getMap("MasterDataCache:unitBrandsMap");
        brandUnitsMap = instance.getMap("MasterDataCache:brandUnitsMap");
        dreamFolksVoucherCodesUsed = instance.getSet("MasterDataCache:dreamFolksVoucherCodesUsed");
    }

    public void clearCache() {
        LOG.info("$$$$$$$$$$$$$$$Clearing Cache$$$$$$$$$$$$$$$");
        instance.getMap("MasterDataCache:listMappingData").clear();
        instance.getMap("MasterDataCache:channelPartnerMap").clear();
        instance.getMap("MasterDataCache:unitChannelPartnerMapping").clear();
        instance.getMap("MasterDataCache:unitPartnerMenuMappingsMap").clear();
        instance.getMap("MasterDataCache:paymentModes").clear();
        instance.getMap("MasterDataCache:paymentModesCommission").clear();
        //instance.getMap("MasterDataCache:units").clear();
        instance.getMap("MasterDataCache:companies").clear();
        //instance.getMultiMap("MasterDataCache:unitsMetadata").clear();
        //instance.getMap("MasterDataCache:unitsBasicDetails").clear();
        instance.getMultiMap("MasterDataCache:listData").clear();
        instance.getMap("MasterDataCache:addonData").clear();// TreeMap
        instance.getMap("MasterDataCache:listCategoryData").clear();// TreeMap
        instance.getMap("MasterDataCache:listAdjustmentCommentData").clear();// TreeMap
        instance.getMap("MasterDataCache:dimensionProfileData").clear();
        instance.getMap("MasterDataCache:productDetails").clear();// TreeMap
        instance.getMap("MasterDataCache:expenseMetadataIMap").clear();
        instance.getMap("MasterDataCache:productBasicDetails").clear();// TreeMap
        instance.getMultiMap("MasterDataCache:unitProductDetails").clear();
        instance.getMap("MasterDataCache:unitProductAlias").clear();
        instance.getMap("MasterDataCache:parcelCodes").clear();
        //instance.getMultiMap("MasterDataCache:unitTaxProfiles").clear();
        instance.getMap("MasterDataCache:divisions").clear();// TreeMap
        instance.getMap("MasterDataCache:departments").clear();// TreeMap
        instance.getMap("MasterDataCache:designations").clear();// TreeMap
        instance.getMultiMap("MasterDataCache:productAddons").clear();
        instance.getList("MasterDataCache:taxProfiles").clear();
        instance.getMap("MasterDataCache:denominations").clear();
        instance.getMap("MasterDataCache:employees").clear();
        instance.getMap("MasterDataCache:employeeBasicDetails").clear();
        instance.getSet("MasterDataCache:employeeMealProducts").clear();
        instance.getSet("MasterDataCache:criticalSCMProducts").clear();
        instance.getSet("MasterDataCache:employeeMealDimensions").clear();
        //instance.getSet("MasterDataCache:operationalHours").clear();
        instance.getMap("MasterDataCache:kioskLocations").clear();
        instance.getMap("MasterDataCache:kioskMachines").clear();
        instance.getMap("MasterDataCache:kioskCompanies").clear();
        instance.getMap("MasterDataCache:localities").clear();
        instance.getMap("MasterDataCache:localityMappingMap");
        instance.getMap("MasterDataCache:locations").clear();
        instance.getMap("MasterDataCache:states").clear();
        instance.getMap("MasterDataCache:countries").clear();
        instance.getMultiMap("MasterDataCache:countryToState").clear();
        instance.getMultiMap("MasterDataCache:stateToLocation").clear();
        instance.getMultiMap("MasterDataCache:cancellationReason").clear();
        //instance.getSet("MasterDataCache:cityList").clear();
        //instance.getMap("MasterDataCache:cityUnitMapping").clear();
        instance.getSet("MasterDataCache:tokenizedApis").clear();
        instance.getMap("MasterDataCache:partnerUnits").clear();
        instance.getMap("MasterDataCache:deliveryUnits").clear();
        instance.getMap("MasterDataCache:externalPartnerMap").clear();
        instance.getMap("MasterDataCache:itemPerTicket").clear();
        instance.getMap("MasterDataCache:brandMetaData").clear();
        instance.getMap("MasterDataCache:unitPartnerBrandMappingMetaData").clear();
        instance.getMap("MasterDataCache:unitPartnerEdcMappingMetadata").clear();
        instance.getMultiMap("MasterDataCache:unitwisePartnerBrandMappingMetaData").clear();
        instance.getMap("MasterDataCache:unitPartnerBrandMappingMetaData2").clear();
        instance.getMap("MasterDataCache:entityAliasMappingData").clear();
        instance.getMap("MasterDataCache:chaayosCashMetadata").clear();
        instance.getMap("MasterDataCache:unitPartnerBrandMetadataMap").clear();
        instance.getMap("MasterDataCache:priceProfileMap").clear();
        instance.getMap("MasterDataCache:subscriptionProduct").clear();
        instance.getMap("MasterDataCache:subscriptionSkuCodeDetail").clear();
        instance.getMap("MasterDataCache:regionMap").clear();
        instance.getMap("MasterDataCache:cacheReferenceValue").clear();
        instance.getMap("MasterDataCache:sourceCondimentDataMapping").clear();
        instance.getMap("MasterDataCache:groupItemCondimentMapping").clear();
        instance.getMap("MasterDataCache:unitTerminalDataMap").clear();
        //instance.getMap("MasterDataCache:unitsEmailIds").clear();
        instance.getMap("MasterDataCache:addressInfo").clear();
        instance.getMap("MasterDataCache:unitContacts").clear();
        instance.getMap("MasterDataCache:customerAppliedCouponDetailMap").clear();
        instance.getMap("MasterDataCache:unitPriceProfileUpdateMap").clear();
        instance.getMap("MasterDataCache:unitDroolVersionMapping").clear();
        instance.getMap("MasterDataCache:specialMilkVariant").clear();
        instance.getMap("MasterDataCache:productPriceMapByProfile").clear();
        
        instance.getMap("MasterDataCache:companyBrandsMap").clear();
        instance.getMap("MasterDataCache:unitBrandsMap").clear();
        instance.getMap("MasterDataCache:brandUnitsMap").clear();
        instance.getMap("MasterDataCache:workstationRoutingCache").clear();
    }

    /////////////////// clearing individual caches //////////////////////////
    public void clearPaymentModes() {
        instance.getMap("MasterDataCache:paymentModes").clear();
    }

    public void clearAddressInfo(){
        instance.getMap("MasterDataCache:addressInfo").clear();
    }

    public void clearUnitToPartnerEdcMappings() {
        instance.getMap("MasterDataCache:unitPartnerEdcMappingMetadata").clear();
    }

    public void clearCacheReferenceValue() {
        instance.getMap("MasterDataCache:cacheReferenceValue").clear();
    }

    public void clearSourceCondimentData() {
        instance.getMap("MasterDataCache:sourceCondimentDataMapping").clear();
    }

    public void clearUnitStateMetadata() {
        instance.getMap("MasterDataCache:unitClosureStateIMap").clear();
    }

    public void clearGroupCondimentMapping(){
        instance.getMap("MasterDataCache:groupItemCondimentMapping").clear();
    }


    public void clearItemPerTicket() {
        instance.getMap("MasterDataCache:itemPerTicket").clear();
    }

    public void clearBrandMetaData() {
        instance.getMap("MasterDataCache:brandMetaData").clear();
    }

    public void clearUnitPartnerBrandMapping() {
        instance.getMap("MasterDataCache:unitPartnerBrandMappingMetaData");
        instance.getMap("MasterDataCache:unitwisePartnerBrandMappingMetaData");
    }

    public void clearUnitPartnerBrandMapping2() {
        instance.getMap("MasterDataCache:unitPartnerBrandMappingMetaData2");
    }

    public void clearEntityAliasMappingData() {
        instance.getMap("MasterDataCache:entityAliasMappingData");
    }/////////////////// clearing individual caches //////////////////////////

    public void clearPaymentModesCommission() {
        instance.getMap("MasterDataCache:paymentModesCommission").clear();
    }

    public void clearCityUnitMapping() {
        instance.getMap("MasterDataCache:cityUnitMapping").clear();
    }

    public IMap<Integer, Set<IdCodeName>> getCityUnitMapping() {
        return cityUnitMapping;
    }
    public IMap<Long, UnitClosureState> getUnitClosureStateMetadata() {
        return unitClosureStateIMap;
    }

    public Set<IdCodeName> getCityUnitMapping(int locationId) {
        return cityUnitMapping.get(locationId);
    }

    public void updateCityUnitMapping(int locationId, Set<IdCodeName> unitSet) {
        cityUnitMapping.put(locationId, unitSet);
    }

    public void clearCancellationReason() {
        instance.getMultiMap("MasterDataCache:cancellationReason").clear();
    }

    public void clearUnits() {
        instance.getMap("MasterDataCache:units").clear();
    }

    public void clearCompanies() {
        instance.getMap("MasterDataCache:companies").clear();
    }

    public void clearUnitsMetadata() {
        instance.getMultiMap("MasterDataCache:unitsMetadata").clear();
    }

    public void clearUnitsBasicDetails() {
        instance.getMap("MasterDataCache:unitsBasicDetails").clear();
    }

    public void clearListData() {
        instance.getMap("MasterDataCache:listData").clear();
    }

    public void clearAddonData() {
        instance.getMap("MasterDataCache:addonData").clear();
    }

    public void clearListCategoryData() {
        instance.getMap("MasterDataCache:listCategoryData").clear();
    }
    public void clearListAdjustmentCommentData() {
        instance.getMap("MasterDataCache:listAdjustmentCommentData").clear();
    }

    public void clearDimensionProfileData() {
        instance.getMap("MasterDataCache:dimensionProfileData").clear();
    }

    public void clearProductDetails() {
        instance.getMap("MasterDataCache:productDetails").clear();
    }

    public void clearExpenseMetadataMap() {
        instance.getMap("MasterDataCache:expenseMetadataIMap").clear();
    }

    public void clearProductBasicDetails() {
        instance.getMap("MasterDataCache:productBasicDetails").clear();
    }

    public void clearParcelCodes() {
        instance.getMap("MasterDataCache:parcelCodes").clear();
    }

    public void clearUnitTaxProfiles() {
        instance.getMap("MasterDataCache:unitTaxProfiles").clear();
    }

    public void clearDivisions() {
        instance.getMap("MasterDataCache:divisions").clear();
    }

    public void clearDepartments() {
        instance.getMap("MasterDataCache:departments").clear();
    }

    public void clearDesignations() {
        instance.getMap("MasterDataCache:designations").clear();
    }

    public void clearProductAddons() {
        instance.getMap("MasterDataCache:productAddons").clear();
    }

    public void clearTaxProfiles() {
        instance.getMap("MasterDataCache:taxProfiles").clear();
    }

    public void clearDenominations() {
        instance.getMap("MasterDataCache:denominations").clear();
    }

    public void clearEmployees() {
        instance.getMap("MasterDataCache:employees").clear();
        instance.getMap("MasterDataCache:employeeBasicDetails").clear();
    }

    public void clearEmployeeMealProducts() {
        instance.getMap("MasterDataCache:employeeMealProducts").clear();
    }

    public void clearEmployeeMealDimensions() {
        instance.getMap("MasterDataCache:employeeMealDimensions").clear();
    }

    public void clearItemEstimation() {
        instance.getMap("MasterDataCache:itemEstimation").clear();
    }

    public void clearOperationalHours() {
        instance.getMap("MasterDataCache:operationalHours").clear();
    }

    public void clearCriticalProducts() {
        instance.getMap("MasterDataCache:criticalSCMProducts").clear();
    }

    public void clearLocalities() {
        instance.getMap("MasterDataCache:localities").clear();
    }

    public void clearTokenizedApis() {
        instance.getSet("MasterDataCache:tokenizedApis").clear();
    }

    public void clearLocations() {
        instance.getMap("MasterDataCache:locations").clear();
        /////////////////////////////////////////////////////
        instance.getSet("MasterDataCache:cities").clear();
        instance.getMap("MasterDataCache:countries").clear();
        instance.getMultiMap("MasterDataCache:countryToState").clear();
        instance.getMultiMap("MasterDataCache:stateToLocation").clear();
        instance.getMultiMap("MasterDataCache:locationsData").clear();
    }

    public void clearStates() {
        instance.getMap("MasterDataCache:states").clear();
    }


    public void clearLocalityMappingMap() {
        instance.getSet("MasterDataCache:localityMappingMap").clear();
    }

    public void clearChaayosCashMetadata() {
        instance.getMap("MasterDataCache:chaayosCashMetadata").clear();
    }

    public void clearUnitPartnerBrandLoyaltyMap() {
        instance.getMap("MasterDataCache:unitPartnerBrandMetadataMap").clear();
    }

    public void clearPriceProfileMap() {
        instance.getMap("MasterDataCache:priceProfileMap").clear();
    }

    public void clearSubscriptionProduct() {
        instance.getMap("MasterDataCache:subscriptionProduct").clear();
    }

    public void clearSubscriptionSkuCodeDetail() {
        instance.getMap("MasterDataCache:subscriptionSkuCodeDetail").clear();
    }

    public void clearUnitMetadata() {
        this.units.clear();
        this.unitsBasicDetails.clear();
    }
    
    public void clearCompanyBrandsMap() {
        instance.getMap("MasterDataCache:companyBrandsMap").clear();
    }
    
    public void clearUnitBrandsMap() {
        instance.getMap("MasterDataCache:unitBrandsMap").clear();
    }
    
    public void clearBrandUnitsMap() {
        instance.getMap("MasterDataCache:brandUnitsMap").clear();
    }

    public IMap<Integer,Address> getAddresses(){
        return addressInfo;
    }

    public Address getAddress(Integer id){
        return addressInfo.get(id);
    }

    public void clearRegionMap(){
        this.regionMap.clear();
    }

    /////////////////////////// Getters ////////////////////////////////

    public ISet<IdCodeName> getUnitCityList() {
        return cityList;
    }

    public Map<Integer, BigDecimal> getAllPaymentModeComission() {
        return paymentModesCommission;
    }

    public List<PaymentMode> getAllPaymentMode() {
        return sort(paymentModes.values());
    }

    public IMap<String, ListData> getItemPerTicket() {
        return itemPerTicket;
    }

    public IMap<Integer, Brand> getBrandMetaData() {
        return brandMetaData;
    }

    public IMap<Integer, List<UnitToPartnerEdcMapping>> getUnitPartnerEdcMappingMetadata() {
        return unitPartnerEdcMappingMetadata;
    }

    public  List<UnitToPartnerEdcMapping> getPartnerEdcMappingForUnit(Integer unitId){
        return unitPartnerEdcMappingMetadata.get(unitId);
    }

    public IMap<UnitPartnerBrandKey, UnitPartnerBrandMappingData> getUnitPartnerBrandMappingMetaData() {
        return unitPartnerBrandMappingMetaData;
    }

    public MultiMap<Integer, UnitPartnerBrandMappingData> getUnitwisePartnerBrandMappingMetaData() {
        return unitwisePartnerBrandMappingMetaData;
    }

    public Collection<UnitPartnerBrandMappingData> getUnitwisePartnerBrandMappingMetaData(int unitId) {
        return unitwisePartnerBrandMappingMetaData.containsKey(unitId) ? unitwisePartnerBrandMappingMetaData.get(unitId)
                : new ArrayList<>();
    }

    public IMap<RestaurantPartnerKey, UnitPartnerBrandMappingData> getUnitPartnerBrandMappingMetaData2() {
        return unitPartnerBrandMappingMetaData2;
    }

    public HazelcastInstance getInstance() {
        return instance;
    }


    public IMap<EntityAliasKey, EntityAliasMappingData> getEntityAliasMappingData() {
        return entityAliasMappingData;
    }

    public PaymentMode getPaymentMode(int id) {
        return paymentModes.get(id);
    }

    public List<Product> getAllProducts() {
        return sort(productDetails.values());
    }

    public IMap<String, LocalityMapping> getAllLocalities() {
        return localities;
    }

    public IMap<String, Location> getAllLocations() {
        return locations;
    }
    public List<Location> getActiveLocations() {
        List<Location> activeLocations = new ArrayList<>();
        for (Map.Entry<String, Location> entry : locations.entrySet()) {
            Location value = entry.getValue();
            if(value.isBusiness()){
                activeLocations.add(value);
            }
        }
        return activeLocations;
    }

    public IMap<Integer, Location> getAllLocationsData() {
        return locationsData;
    }

    public Location getLocationbyId(int id) {
        return locationsData.get(id);
    }

    public IMap<String, State> getAllStates() {
        return states;
    }

    public IMap<CashMetadataType, CashMetadata> getChaayosCashMetadata() {
        return chaayosCashMetadata;
    }


    public State getStateById(Integer id) {
        return states.values().stream()
                .filter(s -> id.equals(s.getId()))
                .findFirst()
                .orElse(null);
    }


    public Map<String, IdCodeName> getAddonsForAProduct(int productId) throws DataNotFoundException {
        Map<String, IdCodeName> map = new HashMap<>();
        for (IdCodeName data : productAddons.get(productId)) {
            map.put(data.getCode(), data);
        }
        return map;
    }

    public Product getProduct(int id) {
        return productDetails.get(id);
    }

    public ExpenseMetadata getExpenseDataById(int id) {
        return expenseMetadataIMap.get(id);
    }

    public List<ProductBasicDetail> getAllProductsBasicDetail() {
        return sort(productBasicDetails.values());
    }

    private <T extends Comparable<T>> List<T> sort(Collection<T> values) {
        List<T> list = new ArrayList<T>(values);
        Collections.<T>sort(list);
        return list;
    }

    public ProductBasicDetail getProductBasicDetail(int id) {
        return productBasicDetails.get(id);
    }

    public IdCodeName getDiscountCode(int id) {
        return get(listData.get(ListTypes.DISCOUNT_CODES), id);
    }

    public Integer getSystemEmployeeId() {
        for (Integer empId : employees.keySet()) {
            if (employees.get(empId).equals("System")) {
                return empId;
            }
        }
        return null;
    }

    private IdCodeName get(Collection<IdCodeName> values, int id) {

        for (IdCodeName val : values) {
            if (val.getId() == id) {
                return val;
            }
        }
        return null;

    }

    public List<IdCodeName> getAllDiscountCodes() {
        return sort(listData.get(ListTypes.DISCOUNT_CODES));
    }

    public ListData getProductCategory(int id) {
        return listCategoryData.get(id);
    }

    public ListData getDimensionProfile(int id) {
        return dimensionProfileData.get(id);
    }

    public List<ListData> getAllProductCategories() {
        return sort(listCategoryData.values());
    }

    public Division getDivision(int id) {
        return divisions.get(id);
    }

    public List<Division> getDivisionsValues() {
        return sort(divisions.values());
    }

    public Department getDepartment(int id) {
        return departments.get(id);
    }

    public List<Department> getDepartmentsValues() {
        return sort(departments.values());
    }

    public Designation getDesignation(int id) {
        return designations.get(id);
    }

    public List<Designation> getDesignationsValues() {
        return sort(designations.values());
    }

    public IdCodeName getProductSubCategory(int id) {
        return get(listData.get(ListTypes.SUB_CATEGORIES), id);
    }

    public IdCodeName getDimensionCode(int id) {
        return get(listData.get(ListTypes.DIMENSION_CODES), id);
    }

    public List<IdCodeName> getAllProductSubCategories() {
        return sort(listData.get(ListTypes.SUB_CATEGORIES));
    }

    public List<IdCodeName> getAllPaymentRequestTypes() {
        return sort(listData.get(ListTypes.PR_TYPE));
    }

    public AddonData getAddon(int id) {
        if (!addonData.containsKey(id)) {
            return MasterUtil.getDummyAddon(id);
        }
        return addonData.get(id);
    }

    public List<AddonData> getAllAddons() {
        if (addonData == null || addonData.size() == 0) {
            return new ArrayList<AddonData>();
        }
        List<AddonData> list = new ArrayList<AddonData>(addonData.values());
        Collections.<AddonData>sort(list);
        return list;
    }

    public AddonData getParcelCode(String profileId) {
        return parcelCodes.get(profileId);
    }

    public DenominationDetail getDenominationDetail(int id) {
        return denominations.get(id);
    }

    public List<UnitBasicDetail> getUnits(UnitCategory category) {
        return sort(unitsMetadata.get(category));
    }

    public List<UnitBasicDetail> getAllUnits() {
        return new ArrayList<>(unitsMetadata.values());
    }

    public List<Company> getAllCompanies() {
        return new ArrayList<Company>(companies.values());
    }

    public List<Brand> getAllBrands() {
        return new ArrayList<Brand>(brandMetaData.values());
    }

    public List<TaxProfile> getTaxProfiles() {
        return taxProfiles;
    }

    public void addToCompanyMap(Company c) {
        companies.put(c.getId(), c);
    }

    public void addProductToMap(Product product) {
        productDetails.put(product.getId(), product);
        productBasicDetails.put(product.getId(), MasterDataConverter.convertToProductBasicDetail(product));
    }
    public void addDesignationToDepartment(Designation designation ,Department department){
        List<Designation> designations = department.getDesignations();
        designations.add(designation);
        department.getDesignations().addAll(designations);
        departments.put(department.getId(),department);
    }

    public Map<Integer, PaymentMode> getPaymentModesMap() {
        return paymentModes;
    }

    public Map<Integer, PaymentMode> getPaymentModes() {
        return paymentModes;
    }

    public Map<Integer, Unit> getUnits() {
        return units;
    }

    public MultiMap<UnitCategory, UnitBasicDetail> getUnitsMetadata() {
        return unitsMetadata;
    }

    public Map<Integer, UnitBasicDetail> getUnitsBasicDetails() {
        return unitsBasicDetails;
    }

    public MultiMap<ListTypes, IdCodeName> getListData() {
        return listData;
    }

    public Map<Integer, AddonData> getAddonData() {
        return addonData;
    }

    public Map<Integer, ListData> getListCategoryData() {
        return listCategoryData;
    }

    public Map<String, ListData> getListAdjustmentCommentData() {
        return listAdjustmentCommentData;
    }

    public IMap<Integer, ListData> getDimensionProfileData() {
        return dimensionProfileData;
    }

    public Map<Integer, Product> getProductDetails() {
        return productDetails;
    }

    public void setProductDetails(Integer key , Product value){
        productDetails.put(key,value);
    }

    public IMap<Integer, ExpenseMetadata> getExpenseMetadataIMap() {
        return expenseMetadataIMap;
    }

    public void setExpenseMetadataIMap(IMap<Integer, ExpenseMetadata> expenseMetadataIMap) {
        this.expenseMetadataIMap = expenseMetadataIMap;
    }

    public Map<Integer,Product> getSubscriptionProductDetails() {
        return subscriptionProduct;
    }

    public Product getSubscriptionProductDetail(Integer productId) {
        return subscriptionProduct.get(productId);
    }

    public Map<String, Pair<CouponDetail,Product>> getSubscriptionSkuCodeDetail() {
        return subscriptionSkuCodeDetail;
    }

    public Pair<CouponDetail,Product> getSubscriptionSkuCodeDetail(String skuCode) {
        return subscriptionSkuCodeDetail.get(skuCode);
    }

    public Map<Integer, ProductBasicDetail> getProductBasicDetails() {
        return productBasicDetails;
    }

    public Map<String, AddonData> getParcelCodes() {
        return parcelCodes;
    }

    public MultiMap<Integer, TaxProfile> getUnitTaxProfiles() {
        return unitTaxProfiles;
    }

    public MultiMap<Integer, IdCodeName> getProductAddons() {
        return productAddons;
    }

    public Map<Integer, DenominationDetail> getDenominations() {
        return denominations;
    }

    public Map<Integer, Division> getDivisions() {
        return divisions;
    }

    public Map<Integer, Department> getDepartments() {
        return departments;
    }

    public Map<Integer, Designation> getDesignations() {
        return designations;
    }

    public Unit getUnit(int unitId) {
        return units.get(unitId);
    }

    public Company getCompany(int id) {
        return companies.get(id);
    }

    public Collection<Product> getUnitProductDetails(int unitId) {
        return unitProductDetails.get(unitId);
    }

    public Map<Pair<BigDecimal,String>, String> getUnitProductAlias(int unitId, int productId) {
        return unitProductAlias.containsKey(unitId) ? unitProductAlias.get(unitId).get(productId) : new HashMap<>();
    }

    public Collection<ProductVO> getUnitProductTrimmedDetails(int unitId) {
        return unitProductTrimmedDetails.get(unitId);
    }

    public Set<Integer> getUnitProductIds(int unitId) {
        Set<Integer> set = new HashSet<>();
        for (ProductVO product : getUnitProductTrimmedDetails(unitId)) {
            set.add(product.getId());
        }
        return set;
    }

    public UnitBasicDetail getUnitBasicDetail(int unitId) {
        return unitsBasicDetails.get(unitId);
    }

    public Collection<TaxProfile> getTaxProfile(int unitId) {
        return unitTaxProfiles.get(unitId);
    }

    public String getEmployee(int createdBy) {
        return employees.get(createdBy);
    }

    public String addEmployee(int id, String name) {
        return employees.put(id, name);
    }

    public Map<Integer, String> getEmployees() {
        return employees;
    }

    public EmployeeBasicDetail getEmployeeBasicDetail(Integer employeeId) {
        return employeeBasicDetails.get(employeeId);
    }

    public EmployeeBasicDetail addEmployeeDetail(EmployeeBasicDetail employee) {
        return employeeBasicDetails.put(employee.getId(), employee);
    }

    public Map<Integer, EmployeeBasicDetail> getEmployeeBasicDetails() {
        return employeeBasicDetails;
    }

    public ISet<Integer> getEmployeeMealProducts() {
        return employeeMealProducts;
    }

    public ISet<String> getEmployeeMealDimensions() {
        return employeeMealDimensions;
    }

    public IMap<Integer, List<UnitHours>> getOperationalHours() {
        return operationalHours;
    }

    public void setOperationalHours(IMap<Integer, List<UnitHours>> unitHours) {
        this.operationalHours = unitHours;
    }

    public List<UnitHours> getOperationalHoursForUnit(int unitId) {
        return operationalHours.get(unitId);
    }

    public void setOperationalHoursForUnit(int unitId, List<UnitHours> unitHours) {
        this.operationalHours.set(unitId, unitHours);
    }

    public IMap<Integer, KioskLocationDetails> getKioskLocations() {
        return kioskLocations;
    }

    public IMap<Integer, KioskMachine> getKioskMachines() {
        return kioskMachines;
    }

    public IMap<Integer, KioskCompanyDetails> getKioskCompanies() {
        return kioskCompanies;
    }

    public Set<Integer> getCriticalSCMProducts() {
        return criticalSCMProducts;
    }

    public void addCriticalSCMProducts(Collection<Integer> products) {
        criticalSCMProducts.addAll(products);
    }

    public Collection<IdCodeName> getAllCountries() {
        return countries.values();
    }

    public Collection<IdCodeName> getAllStates(int countryId) {
        return countryToState.get(countryId);
    }

    public Collection<Location> getAllLocations(int stateId) {
        return stateToLocation.get(stateId);
    }

    public IMap<Integer, IdCodeName> getCountries() {
        return countries;
    }

    public MultiMap<Integer, IdCodeName> getCountryToState() {
        return countryToState;
    }

    public MultiMap<Integer, Location> getStateToLocation() {
        return stateToLocation;
    }

    public MultiMap<Integer, Product> getUnitProductDetails() {
        return unitProductDetails;
    }


    public Map<Integer, Map<Integer, Map<Pair<BigDecimal,String> ,String >>> getUnitProductAlias() {
        return unitProductAlias;
    }

    public MultiMap<Integer, ProductVO> getUnitProductTrimmedDetails() {
        return unitProductTrimmedDetails;
    }

    public IMap<Integer, Map<String, Boolean>> getConsumablesUnitProductFlagMap() {
        return consumablesUnitProductFlagMap;
    }

    public boolean pickDineInConsumablesProduct(int unitId, int productId, String dimension) {
        String key = AppUtils.generateUniqueKey(String.valueOf(productId), dimension);
        return Optional.ofNullable(getConsumablesUnitProductFlagMap().get(unitId))
                .map(m -> m.getOrDefault(key, true))
                .orElse(true);
    }

    public MultiMap<UnitCategory, CancellationReason> getCancellationReason() {
        return cancellationReason;
    }

    public Collection<CancellationReason> getCancellationReason(UnitCategory category) {
        return cancellationReason.get(category);
    }

    public IMap<CityLocalityKey, LocalityMapping> getLocalityMappingMap() {
        return localityMappingMap;
    }

    public Map<String, String> getTokenizedApis() {
        return tokenizedApis;
    }

    public Map<String, ExternalPartnerDetail> getExternalPartnerMap() {
        return externalPartnerMap;
    }

    public Map<CacheReferenceType,String> getCacheReferenceMetadata() {
        return cacheReferenceValue;
    }

    public String getCacheReferenceMetadata(CacheReferenceType cacheReferenceType) {
        return cacheReferenceValue.get(cacheReferenceType);
    }

    public IMap<String, List<CondimentGroupData> > getSourceCondimentDataMapping() {
        return sourceCondimentDataMapping;
    }

    public  IMap<Integer,List<CondimentItemData>> getGroupItemCondimentMapping(){
        return groupItemCondimentMapping;
    }

    public List<CondimentItemData> getGroupItemCondimentMapping(Integer id){
        return  groupItemCondimentMapping.get(id) !=null? groupItemCondimentMapping.get(id) : null;
    }

    @Deprecated
    public Integer getDeliveryUnit(int unitId, boolean partnerOrder) {
        if (partnerOrder) {
            if (!partnerUnits.containsKey(unitId)) {
                int partnerUnitId = fetchDeliveryUnit(unitId, partnerOrder);
                partnerUnits.put(unitId, partnerUnitId);
            }
            return partnerUnits.get(unitId);
        } else {
            if (!deliveryUnits.containsKey(unitId)) {
                int deliveryUnitId = fetchDeliveryUnit(unitId, partnerOrder);
                deliveryUnits.put(unitId, deliveryUnitId);
            }
            return deliveryUnits.get(unitId);
        }
    }

    public Integer getDeliveryUnit(int unitId, int partnerId, int brandId, boolean isCod) {
        if(!isCod){
            return unitId;
        }
        if(partnerId == 0) {
            partnerId = AppConstants.CHAAYOS_DELIVERY_PARTNER_ID; // Chaayos Delivery partner id
        }
        LOG.info("Getting delivery unit for unitId:" + unitId + " partnerId:" + partnerId + " brandId:" + brandId);
        UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitId, brandId, partnerId);
        return getUnitPartnerBrandMappingMetaData().get(key).getPriceProfileUnitId();
    }

    private int fetchDeliveryUnit(int unitId, boolean partnerOrder) {
        String region = getUnitBasicDetail(unitId).getRegion();
        List<UnitBasicDetail> codUnits = getUnits(UnitCategory.COD);
        Optional<UnitBasicDetail> unit = codUnits.stream().filter(new Predicate<UnitBasicDetail>() {
            @Override
            public boolean test(UnitBasicDetail unit) {
                return (partnerOrder && region.equals(unit.getRegion()) && unit.isPartnerPriced())
                        || (!partnerOrder && region.equals(unit.getRegion()));
            }
        }).findAny();
        return unit.isPresent() ? unit.get().getId() : null;
    }

    public CashMetadata getCashMetadata(CashMetadataType type) {
        return chaayosCashMetadata.get(type);
    }

    public void loadUnitChannelPartnerMapping(List<UnitChannelPartnerMappingData> mappings) {
        unitChannelPartnerMapping.clear();

        if (mappings != null) {
            mappings.forEach(mapping -> {
                UnitChannelPartnerMapping mapping1 = new UnitChannelPartnerMapping();
                mapping1.setId(mapping.getId());
                // This is not used anywhere
                /*
                 * mapping1.setDeliveryPartner(
                 * listMappingData.get(ListTypes.DELIVERY_PARTNERS).get(mapping.
                 * getDeliveryPartnerId()));
                 */
                mapping1.setChannelPartner(getChannelPartner(mapping.getChannelPartnerId()));
                mapping1.setUnit(new IdCodeName(mapping.getUnitId(), getUnit(mapping.getUnitId()).getName(),
                        getUnit(mapping.getUnitId()).getRegion()));
                mapping1.setStatus(mapping.getStatus());
                unitChannelPartnerMapping.put(mapping1.getId(), mapping1);
            });
        }
    }

    public void loadUnitPartnerMenuMapping(List<UnitChannelPartnerMenuMappingData> mappings,
                                           Map<Integer, PriceProfileDetail> priceProfilesData) {
        unitPartnerMenuMappingsMap.clear();
        Map<UnitPartnerBrandKey, List<UnitPartnerMenuMapping>> unitPartnerMenuMappingsMapTemp = new HashMap<>();
        priceProfileMap.clear();
        Map<UnitPartnerBrandKey, Map<MenuType, PriceProfileDetail>> priceProfileMapTemp = new HashMap<>();

        mappings.forEach(mapping -> {
//			if (mapping.getStatus().equals("ACTIVE")) {
            UnitPartnerMenuMapping unitPartnerMenuMapping = new UnitPartnerMenuMapping();
            boolean found = false;
            UnitChannelPartnerMapping partnerMapping = unitChannelPartnerMapping.get(mapping.getUnitPartnerMappingId());
            if (partnerMapping != null) {
                unitPartnerMenuMapping.setChannelPartner(getChannelPartner(partnerMapping.getChannelPartner().getId()));
                unitPartnerMenuMapping.setUnit(partnerMapping.getUnit());
                found = true;
            }
            if (!found) {
                LOG.error("Did Not Find Mapping for {}", mapping);
                return;
            }
            MenuType dayPart = MenuType.valueOf(mapping.getMenuType());
            unitPartnerMenuMapping.setCreatedAt(mapping.getCreatedAt());
            unitPartnerMenuMapping
                    .setCreatedBy(new IdCodeName(mapping.getCreatedBy(), getEmployee(mapping.getCreatedBy()), ""));
            // unitPartnerMenuMapping.setDay(mapping.getMenuDay());
            unitPartnerMenuMapping.setDayNumber(mapping.getMenuDay());
            unitPartnerMenuMapping.setEndTime(mapping.getEndTime());
            unitPartnerMenuMapping.setId(mapping.getId());
            unitPartnerMenuMapping.setMenuApp(MenuApp.valueOf(mapping.getMenuApp()));
            unitPartnerMenuMapping.setMenuSequence(new IdCodeName(mapping.getMenuSequenceId(), "", ""));
            unitPartnerMenuMapping.setMenuType(dayPart);
            unitPartnerMenuMapping.setStartTime(mapping.getStartTime());
            unitPartnerMenuMapping.setStatus(mapping.getStatus());
            unitPartnerMenuMapping.setUnitChannelPartnerMappingId(mapping.getUnitPartnerMappingId());
            unitPartnerMenuMapping.setPriceProfileId(mapping.getPriceProfileId());
            unitPartnerMenuMapping.setUpdatedAt(mapping.getUpdatedAt());
            unitPartnerMenuMapping.setMenuRecommendationSequenceId(mapping.getMenuRecommendationSequenceId());
            unitPartnerMenuMapping
                    .setUpdatedBy(new IdCodeName(mapping.getUpdatedBy(), getEmployee(mapping.getUpdatedBy()), ""));
            unitPartnerMenuMapping.setBrand(new IdCodeName(mapping.getBrandId(),
                    getBrandMetaData().get(mapping.getBrandId()).getBrandName(), ""));
            // unitPartnerMenuMappings.add(unitPartnerMenuMapping);
            UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitPartnerMenuMapping.getUnit().getId(),
                    unitPartnerMenuMapping.getBrand().getId(), unitPartnerMenuMapping.getChannelPartner().getId());
            List<UnitPartnerMenuMapping> mappingsList = unitPartnerMenuMappingsMapTemp.get(key);
            if (mappingsList == null) {
                mappingsList = new ArrayList<>();
            }
            mappingsList.add(unitPartnerMenuMapping);
            unitPartnerMenuMappingsMapTemp.put(key, mappingsList);
            if (priceProfilesData.containsKey(mapping.getPriceProfileId())) {
                Map<MenuType, PriceProfileDetail> pricePriofiles = priceProfileMapTemp.get(key);
                if (pricePriofiles == null) {
                    pricePriofiles = new HashMap<>();
                }
                pricePriofiles.put(dayPart, priceProfilesData.get(mapping.getPriceProfileId()));
                priceProfileMapTemp.put(key, pricePriofiles);
//                LOG.info("Available Key {}", priceProfileMap.get(key));
//                LOG.info("Available Price {}", mapping.getPriceProfileId());
//                LOG.info("Available Price {}", priceProfilesData.get(mapping.getPriceProfileId()));
//                LOG.info("Available Key {}", priceProfileMap.get(key).get(dayPart));
            }
//			}
        });
        unitPartnerMenuMappingsMap.putAll(unitPartnerMenuMappingsMapTemp);
        priceProfileMap.putAll(priceProfileMapTemp);
    }

    public void loadSpecificUnitPartnerMenuMapping(List<UnitChannelPartnerMenuMappingData> mappings,
                                                   Map<Integer, PriceProfileDetail> priceProfilesData) {
        AtomicBoolean flag = new AtomicBoolean(true);
        mappings.forEach(mapping -> {
            UnitPartnerMenuMapping unitPartnerMenuMapping = new UnitPartnerMenuMapping();
            boolean found = false;
            if (unitChannelPartnerMapping.containsKey(mapping.getUnitPartnerMappingId())) {
                UnitChannelPartnerMapping partnerMapping = unitChannelPartnerMapping
                        .get(mapping.getUnitPartnerMappingId());
                unitPartnerMenuMapping.setChannelPartner(getChannelPartner(partnerMapping.getChannelPartner().getId()));
                unitPartnerMenuMapping.setUnit(partnerMapping.getUnit());
                found = true;
            }
            if (!found) {
                LOG.error("Did Not Find Mapping for {}", mapping);
                return;
            }
            MenuType dayPart = MenuType.valueOf(mapping.getMenuType());
            unitPartnerMenuMapping.setCreatedAt(mapping.getCreatedAt());
            unitPartnerMenuMapping
                    .setCreatedBy(new IdCodeName(mapping.getCreatedBy(), getEmployee(mapping.getCreatedBy()), ""));
            unitPartnerMenuMapping.setDayNumber(mapping.getMenuDay());
            unitPartnerMenuMapping.setEndTime(mapping.getEndTime());
            unitPartnerMenuMapping.setId(mapping.getId());
            unitPartnerMenuMapping.setMenuApp(MenuApp.valueOf(mapping.getMenuApp()));
            unitPartnerMenuMapping.setMenuSequence(new IdCodeName(mapping.getMenuSequenceId(), "", ""));
            unitPartnerMenuMapping.setMenuType(dayPart);
            unitPartnerMenuMapping.setStartTime(mapping.getStartTime());
            unitPartnerMenuMapping.setStatus(mapping.getStatus());
            unitPartnerMenuMapping.setUnitChannelPartnerMappingId(mapping.getUnitPartnerMappingId());
            unitPartnerMenuMapping.setPriceProfileId(mapping.getPriceProfileId());
            unitPartnerMenuMapping.setUpdatedAt(mapping.getUpdatedAt());
            unitPartnerMenuMapping.setMenuRecommendationSequenceId(mapping.getMenuRecommendationSequenceId());
            unitPartnerMenuMapping
                    .setUpdatedBy(new IdCodeName(mapping.getUpdatedBy(), getEmployee(mapping.getUpdatedBy()), ""));
            unitPartnerMenuMapping.setBrand(new IdCodeName(mapping.getBrandId(),
                    getBrandMetaData().get(mapping.getBrandId()).getBrandName(), ""));
            UnitPartnerBrandKey key = new UnitPartnerBrandKey(unitPartnerMenuMapping.getUnit().getId(),
                    unitPartnerMenuMapping.getBrand().getId(), unitPartnerMenuMapping.getChannelPartner().getId());
            List<UnitPartnerMenuMapping> mappingList = new ArrayList<>();
            if (!flag.get()) {
                mappingList = unitPartnerMenuMappingsMap.get(key);
            }
            unitPartnerMenuMappingsMap.remove(key);
            mappingList.add(unitPartnerMenuMapping);
            unitPartnerMenuMappingsMap.put(key, mappingList);
            priceProfileMap.remove(key);
            if (priceProfilesData.containsKey(mapping.getPriceProfileId())) {
                Map<MenuType, PriceProfileDetail> pricePriofiles = priceProfileMap.get(key);
                if (pricePriofiles == null) {
                    pricePriofiles = new HashMap<>();
                }
                pricePriofiles.put(dayPart, priceProfilesData.get(mapping.getPriceProfileId()));
                priceProfileMap.put(key, pricePriofiles);
            }
            flag.set(false);
        });
    }

    public List<UnitChannelPartnerMapping> getActiveUnitChannelPartnerMapping() {
        List<UnitChannelPartnerMapping> output = new ArrayList<>();
        unitChannelPartnerMapping.values().forEach(value -> {
            if (value.getStatus().equals(AppConstants.ACTIVE)){
                output.add(value);
            }
        });
        return output;
    }

    public Collection<UnitChannelPartnerMapping> getAllUnitChannelPartnerMapping() {
        return unitChannelPartnerMapping.values();
    }

    public Collection<UnitChannelPartnerMapping> getBrandAllUnitChannelPartnerMapping() {
        return unitChannelPartnerMapping.values();
    }

    public Map<ListTypes, Map<Integer, IdCodeName>> getListMappingData() {
        return listMappingData;
    }

    public Map<Integer, ChannelPartnerDetail> getChannelPartnerMap() {
        return channelPartnerMap;
    }

    public IdCodeName getChannelPartner(int id) {
        return channelPartnerMap.get(id);
    }

    public ChannelPartnerDetail getChannelPartnerDetail(int id){
        return channelPartnerMap.get(id);
    }

    public Collection<ChannelPartnerDetail> getAllChannelPartners() {
        return channelPartnerMap.values();
    }

    public Map<UnitPartnerBrandKey, List<UnitPartnerMenuMapping>> getUnitPartnerMenuMappingsMap() {
        Map<UnitPartnerBrandKey, List<UnitPartnerMenuMapping>> map = new HashMap<>();
        for(Map.Entry<UnitPartnerBrandKey, List<UnitPartnerMenuMapping>> key : unitPartnerMenuMappingsMap.entrySet()){
            List<UnitPartnerMenuMapping> list= key.getValue();
            List<UnitPartnerMenuMapping> output= new ArrayList<>();
            for(UnitPartnerMenuMapping v :list){
                if(v.getStatus().equals(AppConstants.ACTIVE)){
                    output.add(v);
                }
            }
            map.put(key.getKey(),output);
        }
        return map;
    }

    public List<UnitPartnerMenuMapping> getUnitPartnerMenuMappings() {
        List<UnitPartnerMenuMapping> mappings = new ArrayList<>();
        for(UnitPartnerBrandKey key : unitPartnerMenuMappingsMap.keySet()) {
            mappings.addAll(unitPartnerMenuMappingsMap.get(key));
        }
        return mappings;
    }

    public List<UnitPartnerMenuMapping> getUnitPartnerMenuMappings(Integer brandId) {
        List<UnitPartnerMenuMapping> mappings = new ArrayList<>();
        for(UnitPartnerBrandKey key : unitPartnerMenuMappingsMap.keySet()) {
            if(Objects.equals(key.getBrandId(), brandId)) {
                mappings.addAll(unitPartnerMenuMappingsMap.get(key));
            }
        }
        return mappings;
    }

    public IMap<UnitPartnerBrandKey, Map<UnitPartnerBrandMappingMetadataType, String>> getUnitPartnerBrandMetadataMap() {
        return unitPartnerBrandMetadataMap;
    }

    public Map<MenuType, PriceProfileDetail> getPriceProfileMap(UnitPartnerBrandKey unitId) {
        return priceProfileMap.get(unitId);
    }

    public Integer getUnitPartnerBrandLoyalty(UnitPartnerBrandKey key) {
        if(unitPartnerBrandMetadataMap.containsKey(key) && unitPartnerBrandMetadataMap.get(key) != null &&
                unitPartnerBrandMetadataMap.get(key).containsKey(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS) &&
                unitPartnerBrandMetadataMap.get(key).get(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS) != null) {
            return Integer.parseInt(unitPartnerBrandMetadataMap.get(key).get(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS));
        }
        return Integer.parseInt(UnitPartnerBrandMappingMetadataType.LOYALTY_POINTS.getDefaultValue());
    }

    public List<String> getEmailHeirarchy(Integer unitId, boolean unitEmailInclude) {
        Set<String> emails = new HashSet<>();
        Unit unit = getUnit(unitId);
        if (unitEmailInclude) {
            emails.add(unit.getUnitEmail());
        }
        EmployeeBasicDetail employee = getEmployeeBasicDetail(unit.getCafeManager().getId());
        Integer noOfCancelOrderEmailsToBeSent = AppConstants.NO_OF_CANCEL_ORDER_EMAILS_TO_BE_SENT;
        while (employee != null && noOfCancelOrderEmailsToBeSent-- > 0) {
            if (employee.getEmailId() != null && employee.getEmailId().trim().length() > 0) {
                emails.add(employee.getEmailId());
            }
            if (!employee.getDesignation().equals("DGM") && employee.getReportingManagerId() != null) {
                employee = getEmployeeBasicDetail(employee.getReportingManagerId());
            } else {
                employee = null;
            }
        }
        if (unit.getManagerEmail() != null && !emails.contains(unit.getManagerEmail())) {
            emails.add(unit.getManagerEmail());
        }
        return new ArrayList<>(emails);
    }

    @Override
    public String toString() {
        return "MasterDataCache{" +
                "paymentModes=" + paymentModes.size() +
                ", units=" + units.size() +
                ", companies=" + companies.size() +
                ", unitsMetadata=" + unitsMetadata.size() +
                ", cancellationReason=" + cancellationReason.size() +
                ", deliveryUnits=" + deliveryUnits.size() +
                ", partnerUnits=" + partnerUnits.size() +
                ", unitsBasicDetails=" + unitsBasicDetails.size() +
                ", listData=" + listData.size() +
                ", addonData=" + addonData.size() +
                ", listCategoryData=" + listCategoryData.size() +
                ", listAdjustmentCommentData=" + listAdjustmentCommentData.size() +
                ", dimensionProfileData=" + dimensionProfileData.size() +
                ", productDetails=" + productDetails.size() +
                ", expenseMetadataIMap=" + expenseMetadataIMap.size() +
                ", unitProductDetails=" + unitProductDetails.size() +
                ", unitProductAlias=" + unitProductAlias.size() +
                ", unitProductTrimmedDetails=" + unitProductTrimmedDetails.size() +
                ", consumablesUnitProductFlagMap=" + consumablesUnitProductFlagMap.size() +
                ", productBasicDetails=" + productBasicDetails.size() +
                ", parcelCodes=" + parcelCodes.size() +
                ", unitTaxProfiles=" + unitTaxProfiles.size() +
                ", divisions=" + divisions.size() +
                ", departments=" + departments.size() +
                ", designations=" + designations.size() +
                ", productAddons=" + productAddons.size() +
                ", taxProfiles=" + taxProfiles.size() +
                ", denominations=" + denominations.size() +
                ", employees=" + employees.size() +
                ", employeeBasicDetails=" + employeeBasicDetails.size() +
                ", employeeMealProducts=" + employeeMealProducts.size() +
                ", criticalSCMProducts=" + criticalSCMProducts.size() +
                ", employeeMealDimensions=" + employeeMealDimensions.size() +
                ", operationalHours=" + operationalHours.size() +
                ", kioskLocations=" + kioskLocations.size() +
                ", kioskMachines=" + kioskMachines.size() +
                ", kioskCompanies=" + kioskCompanies.size() +
                ", localities=" + localities.size() +
                ", localityMappingMap=" + localityMappingMap.size() +
                ", locations=" + locations.size() +
                ", locationsData=" + locationsData.size() +
                ", states=" + states.size() +
                ", countries=" + countries.size() +
                ", countryToState=" + countryToState.size() +
                ", stateToLocation=" + stateToLocation.size() +
                ", cityList=" + cityList.size() +
                ", cityUnitMapping=" + cityUnitMapping.size() +
                ", tokenizedApis=" + tokenizedApis.size() +
                ", paymentModesCommission=" + paymentModesCommission.size() +
                ", externalPartnerMap=" + externalPartnerMap.size() +
                ", itemPerTicket=" + itemPerTicket.size() +
                ", chaayosCashMetadata=" + chaayosCashMetadata.size() +
                ", chaayosCashMetadata=" + chaayosCashMetadata.size() +
                ", regionMap=" + regionMap.size() +
                '}';
    }

    public Map<MenuType, Map<Integer, List<ProductPriceVO>>> getUnitPriceMap(Integer unit, int brandId,
                                                                             int partnerId, int subscriptionProductId) {
        try {
            UnitPartnerBrandKey unitId = new UnitPartnerBrandKey(unit, brandId, partnerId);
            Map<MenuType, PriceProfileDetail> map = getPriceProfileMap(unitId);
            LOG.info("Available MenuType Mapping Size {}", map.size());
            if (map == null) {
                return null;
            }
            Map<MenuType, Map<Integer, List<ProductPriceVO>>> result = new HashMap<>();
            for (Map.Entry<MenuType, PriceProfileDetail> entry : map.entrySet()) {
                PriceProfileDetail priceProfileDetail = entry.getValue();
                switch (priceProfileDetail.getProfileType()) {
                    case RANGE_FLAT_AMOUNT:
                    case FLAT_AMOUNT:
                        Map<Integer, List<ProductPriceVO>> productFlatMap = new HashMap<>();
                        for (PriceProfileRangeValueDetail priceProfileRangeValueDetail : priceProfileDetail.getProfileRangeValueDetails()) {
                            Collection<ProductVO> products = getUnitProductTrimmedDetails(unit);
                            if (products == null) {
                                return null;
                            }
                            for (ProductVO product : products) {
                                List<ProductPriceVO> priceVOList = new ArrayList<>();
                                if (product.getBrandId().equals(brandId) && !product.getTaxCode().equals("COMBO") &&
                                        !product.getTaxCode().equals("GIFT_CARD") && product.getClassification().equals(ProductClassification.MENU)
                                        && product.getSubType() != subscriptionProductId ) {
                                    List<ProductPriceVO> prices = product.getPrices();
                                    for (ProductPriceVO productPriceVO : prices) {
                                        ProductPriceVO productPriceVOCopy = new ProductPriceVO(productPriceVO);
                                        productPriceVO.setOriginalPrice(productPriceVO.getOriginalPrice() == null ?
                                                productPriceVO.getPrice() : productPriceVO.getOriginalPrice());
                                        productPriceVOCopy.setOriginalPrice(productPriceVOCopy.getOriginalPrice() == null ?
                                                productPriceVO.getPrice() : productPriceVOCopy.getOriginalPrice());
                                        if (product.getBillType().equals(BillType.NET_PRICE) && productPriceVO.getOriginalPrice().compareTo(priceProfileRangeValueDetail.getStartPrice()) >= 0 &&
                                                productPriceVO.getOriginalPrice().compareTo(priceProfileRangeValueDetail.getEndPrice()) <= 0) {
                                            BigDecimal threshold = AppUtils.multiply(AppUtils.divide(priceProfileDetail.getThresholdPercentage()
                                                    , new BigDecimal(100)), productPriceVO.getPrice());
                                            if(priceProfileRangeValueDetail.getDeltaPrice().compareTo(BigDecimal.ZERO)>0) {
                                                if (threshold.compareTo(priceProfileRangeValueDetail.getDeltaPrice()) < 0) {
                                                    productPriceVOCopy.setPrice(AppUtils.add(productPriceVO.getPrice(), threshold).setScale(0, RoundingMode.UP));
                                                } else {
                                                    productPriceVOCopy.setPrice(AppUtils.add(productPriceVO.getPrice(), priceProfileRangeValueDetail.getDeltaPrice()).setScale(0, RoundingMode.UP));
                                                }
                                            }
                                            else{
                                                if (threshold.compareTo(priceProfileRangeValueDetail.getDeltaPrice().abs()) < 0) {
                                                    productPriceVOCopy.setPrice(AppUtils.subtract(productPriceVO.getPrice(), threshold).setScale(0, RoundingMode.UP));
                                                } else {
                                                    productPriceVOCopy.setPrice(AppUtils.subtract(productPriceVO.getPrice(), priceProfileRangeValueDetail.getDeltaPrice().abs()).setScale(0, RoundingMode.UP));
                                                }
                                            }
                                            LOG.info("{} price Changed {} ::: {}",productPriceVO.getPrice(),productPriceVOCopy.getPrice(),product.getId());
                                            priceVOList.add(productPriceVOCopy);
                                        }
                                    }
                                    List<ProductPriceVO> data=productFlatMap.get(product.getId());
                                    if(data!=null)
                                        priceVOList.addAll(data);
                                    productFlatMap.put(product.getId(), priceVOList);
                                } else {
                                    List<ProductPriceVO> prices = product.getPrices();
                                    for (ProductPriceVO productPriceVO : prices) {
                                        priceVOList.add(productPriceVO);
                                    }
                                    productFlatMap.put(product.getId(), priceVOList);
                                }
                            }
                        }
                        result.put(entry.getKey(), productFlatMap);
                        break;
                    case RANGE_PERCENTAGE_AMOUNT:
                    case PERCENTAGE_AMOUNT:
                        Map<Integer, List<ProductPriceVO>> productPercentageMap = new HashMap<>();
                        for (PriceProfileRangeValueDetail priceProfileRangeValueDetail : priceProfileDetail.getProfileRangeValueDetails()) {
                            Collection<ProductVO> products = getUnitProductTrimmedDetails(unit);
                            if (products == null) {
                                return null;
                            }
                            for (ProductVO product : products) {
                                List<ProductPriceVO> priceVOList = new ArrayList<>();
                                if (product.getBrandId().equals(brandId) && !product.getTaxCode().equals("COMBO") &&
                                        !product.getTaxCode().equals("GIFT_CARD") && product.getClassification().equals(ProductClassification.MENU)
                                        && product.getSubType() != subscriptionProductId ) {
                                    List<ProductPriceVO> prices = product.getPrices();
                                    for (ProductPriceVO productPriceVO : prices) {
                                        ProductPriceVO productPriceVOCopy = new ProductPriceVO(productPriceVO);
                                        productPriceVO.setOriginalPrice(productPriceVO.getOriginalPrice() == null ?
                                                productPriceVO.getPrice() : productPriceVO.getOriginalPrice());
                                        productPriceVOCopy.setOriginalPrice(productPriceVOCopy.getOriginalPrice() == null ?
                                                productPriceVO.getPrice() : productPriceVOCopy.getOriginalPrice());
                                        if (product.getBillType().equals(BillType.NET_PRICE) && productPriceVO.getOriginalPrice().compareTo(priceProfileRangeValueDetail.getStartPrice()) >= 0 &&
                                                productPriceVO.getOriginalPrice().compareTo(priceProfileRangeValueDetail.getEndPrice()) <= 0) {
                                            BigDecimal deltaValue = AppUtils.multiply(AppUtils.divide(priceProfileRangeValueDetail.getDeltaPrice(),
                                                    new BigDecimal(100)), productPriceVO.getPrice());
                                            if (deltaValue.compareTo(BigDecimal.ZERO)>0) {
                                                productPriceVOCopy.setPrice(AppUtils.add(productPriceVO.getPrice(), deltaValue).setScale(0, RoundingMode.UP));
                                            }
                                            else{
                                                productPriceVOCopy.setPrice(AppUtils.subtract(productPriceVO.getPrice(), deltaValue.abs()).setScale(0, RoundingMode.UP));
                                            }
                                            priceVOList.add(productPriceVOCopy);
                                        }
                                    }
                                    List<ProductPriceVO> data=productPercentageMap.get(product.getId());
                                    if(data!=null)
                                        priceVOList.addAll(data);
                                    productPercentageMap.put(product.getId(), priceVOList);
                                } else {
                                    List<ProductPriceVO> prices = product.getPrices();
                                    for (ProductPriceVO productPriceVO : prices) {
                                        priceVOList.add(productPriceVO);
                                    }
                                    productPercentageMap.put(product.getId(), priceVOList);
                                }
                            }
                        }
                        result.put(entry.getKey(), productPercentageMap);
                        break;
                }
            }
            return result;
        } catch (Exception e){
            LOG.error("Error Faced while getting the Price Profile Products for Unit {}",unit);
            return null;
        }
    }

    public List<UnitMenuTypePrice> getUnitPriceDetails(Unit unit, Map<MenuType, Map<Integer, List<ProductPriceVO>>> priceMap) {
        Map<UnitProductKey,UnitMenuTypePrice> result=new HashMap<>();
        for (Map.Entry<MenuType, Map<Integer, List<ProductPriceVO>>> entry : priceMap.entrySet()) {
            Map<Integer, List<ProductPriceVO>> productDetailDaySlot= entry.getValue();
            for(Map.Entry<Integer,List<ProductPriceVO>> productPrice: productDetailDaySlot.entrySet()){
                List<ProductPriceVO> dimensionPrice=productPrice.getValue();
                Product product=getProduct(productPrice.getKey());
                for(ProductPriceVO priceVO:dimensionPrice){
                    UnitProductKey key=new UnitProductKey(unit.getId(),product.getId(),priceVO.getDimension());
                    UnitMenuTypePrice productMenuTypeDetail;
                    if(!result.containsKey(key)){
                        productMenuTypeDetail= new UnitMenuTypePrice(unit.getId(),unit.getName(),product.getType(),product.getSubType(),product.getName(),
                                priceVO.getDimension(),product.getId(),priceVO.getOriginalPrice());
                    }
                    else{
                        productMenuTypeDetail=result.get(key);
                    }
                    switch (entry.getKey()){
                        case DEFAULT:
                            productMenuTypeDetail.setDefaultvalue(priceVO.getPrice());
                            break;
                        case DAY_SLOT:
                            productMenuTypeDetail.setDaySlot(priceVO.getPrice());
                            break;
                        case DAY_SLOT_BREAKFAST:
                            productMenuTypeDetail.setDaySlotBreakfast(priceVO.getPrice());
                            break;
                        case DAY_SLOT_LUNCH:
                            productMenuTypeDetail.setDaySlotLunch(priceVO.getPrice());
                            break;
                        case DAY_SLOT_EVENING:
                            productMenuTypeDetail.setDaySlotEvening(priceVO.getPrice());
                            break;
                        case DAY_SLOT_DINNER:
                            productMenuTypeDetail.setDaySlotDinner(priceVO.getPrice());
                            break;
                        case DAY_SLOT_POST_DINNER:
                            productMenuTypeDetail.setDaySlotPostDinner(priceVO.getPrice());
                            break;
                        case DAY_SLOT_OVERNIGHT:
                            productMenuTypeDetail.setDaySlotOvernight(priceVO.getPrice());
                            break;
                        case SINGLE_SERVE:
                            productMenuTypeDetail.setSingleServe(priceVO.getPrice());
                            break;
                    }
                    result.put(key,productMenuTypeDetail);
                }
            }
        }
        List<UnitMenuTypePrice> resultant= new ArrayList<>();
        for(Map.Entry<UnitProductKey,UnitMenuTypePrice> data:result.entrySet()){
            resultant.add(data.getValue());
        }
        return resultant;
    }

    public void loadUnitChannelPartnerMapping(UnitChannelPartnerMappingData mapping) {
        UnitChannelPartnerMapping mapping1 = new UnitChannelPartnerMapping();
        mapping1.setId(mapping.getId());
        mapping1.setChannelPartner(getChannelPartner(mapping.getChannelPartnerId()));
        mapping1.setUnit(new IdCodeName(mapping.getUnitId(), getUnit(mapping.getUnitId()).getName(),
                getUnit(mapping.getUnitId()).getRegion()));
        mapping1.setStatus(mapping.getStatus());
        if (unitChannelPartnerMapping.containsKey(mapping.getId())) {
            unitChannelPartnerMapping.remove(mapping.getId());
        }
        unitChannelPartnerMapping.put(mapping1.getId(), mapping1);
    }

    public Set<String> getAllRegions(){
        return regionMap.keySet();
    }

    public IMap<String, String> getRegionMap(){
        return regionMap;
    }

    public ISet<String> getAllUnitsEmailId() {
        return unitsEmailIds;
    }

    public void clearUnitsEmailId() {
        instance.getSet("MasterDataCache:unitsEmailIds").clear();
    }

    public Integer getUnitId(String unitName){
        Map<Integer,Unit> units = getUnits();
        for(Map.Entry<Integer,Unit> entry : units.entrySet()){
            Unit unit = entry.getValue();
            if(unit.getName().equalsIgnoreCase(unitName)){
                return unit.getId();
            }
        }
        return 0;
    }

    public Integer getDeptId(String deptName){
        Map<Integer,Department> departments = getDepartments();
        for(Map.Entry<Integer,Department> entry : departments.entrySet()){
            Department department= entry.getValue();
            if(department.getName().equalsIgnoreCase(deptName)){
                return department.getId();
            }
        }
        return 0;
    }

    public Department getDeptartment(String deptName){
        Map<Integer,Department> departments = getDepartments();
        for(Map.Entry<Integer,Department> entry : departments.entrySet()){
            Department department= entry.getValue();
            if(department.getName().equalsIgnoreCase(deptName)){
                return department;
            }
        }
        return null;
    }

    public Integer getDesignationId(String designationName){
        Map<Integer,Designation> designations = getDesignations();
        for(Map.Entry<Integer,Designation> entry : designations.entrySet()){
            Designation designation= entry.getValue();
            if(designation.getName().equalsIgnoreCase(designationName)){
                return designation.getId();
            }
        }
        return 0;
    }

    public Designation getDesignation(String designationName){
        Map<Integer,Designation> designations = getDesignations();
        for(Map.Entry<Integer,Designation> entry : designations.entrySet()){
            Designation designation= entry.getValue();
            if(designation.getName().equalsIgnoreCase(designationName)){
                return designation;
            }
        }
        return null;
    }

    public IMap<String, UnitIpAddressData> getUnitTerminalIpAddressMap() {
        return unitTerminalIpAddressMap;
    }

    public UnitClosureState getUnitClosureState(Long stateId) {
        return unitClosureStateIMap.get(stateId);
    }

    public String getUnitClosureStateQuery(Long stateId) {
        return unitClosureStateIMap.get(stateId).getQuery();
    }

    public void clearUnitTerminalDataMap() {
        instance.getMap("MasterDataCache:unitTerminalDataMap").clear();
    }

    public IMap<Integer, UnitContactDetails> getUnitContactDetailsIMap() {
        return unitContactDetailsIMap;
    }
    public void clearUnitContactDetailsMap(){
        instance.getMap("MasterDataCache:unitContacts").clear();
    }

    public IMap<String, CustomerAppliedCouponDetail> getCustomerAppliedCouponDetailMap() { return customerAppliedCouponDetailMap; }
    public void clearCustomerAppliedCouponDetailMap() { instance.getMap("MasterDataCache:customerAppliedCouponDetailMap").clear(); }

    public IMap<String, Map<Integer, Set<UnitProductPriceCategoryDomain>>> getPriceCategoryWiseProductsPrice() {
        return priceCategoryWiseProductsPrice;
    }

    public void clearPriceCategoryWiseProductsPrice(){
        instance.getMap("MasterDataCache:priceCategoryWiseProductsPriceMap").clear();
    }

    public ISet<String> getWorkStationsStationCategories() {
        return workStationsStationCategories;
    }

    public void clearWorkStationsStationCategories() {
        instance.getMap("MasterDataCache:workStationsStationCategories").clear();
    }

    public Boolean getUnitPriceProfileUpdateMap(Integer unitId){
         if(!unitPriceProfileUpdateMap.containsKey(unitId)){
             unitPriceProfileUpdateMap.put(unitId,false);
         }
         return unitPriceProfileUpdateMap.get(unitId);
    }

    public void updateUnitPriceProfileUpdateMap(Integer unitId, Boolean value){
        unitPriceProfileUpdateMap.put(unitId,value);
    }

    public IMap<Integer, Map<String, DroolVersionDomain>> getUnitDroolVersionMapping() {
        return unitDroolVersionMapping;
    }

    public void clearUnitDroolVersionMapping() {
        instance.getMap("MasterDataCache:unitDroolVersionMapping").clear();
    }

    public Integer getSpecialMilkVariant(String specialMilkVariantName){
        return milkVariantMap.get(specialMilkVariantName);
    }

    public void setSpecialMilkVariantMap(String key , Integer value){
        this.milkVariantMap.put(key,value);
    }

    public void clearSpecialMilkVariantMap(){
        instance.getMap("MasterDataCache:specialMilkVariant").clear();
    }



    public Map<ProductDimensionKey,BigDecimal> getProductPriceMapByProfile(PriceProfileKey priceProfileKey){
        return productPriceMapByProfile.get(priceProfileKey);
    }

    public IMap<PriceProfileKey,Map<ProductDimensionKey,BigDecimal>>  getAllProductPriceMap(){
        return productPriceMapByProfile;
    }

    public void clearProductPriceMap(){
        instance.getMap("MasterDataCache:productPriceMapByProfile").clear();
    }

    public void putProductPriceMap(PriceProfileKey priceProfileKey , Map<ProductDimensionKey,BigDecimal> value){
        productPriceMapByProfile.put(priceProfileKey,value);
    }
    
    public Map<Integer, List<Brand>> getCompanyBrandsMap() { return this.companyBrandsMap; }
    
    public void putCompanyBrandsMap(Integer companyId, List<Brand> brandDetails) { companyBrandsMap.put(companyId, brandDetails); }
    
    public Map<Integer, List<Brand>> getUnitBrandsMap() { return this.unitBrandsMap; }
    
    public void putUnitBrandsMap(Integer unitId, List<Brand> brandDetails) { unitBrandsMap.put(unitId, brandDetails); }
    
    public Map<Integer, List<Integer>> getBrandUnitsMap() { return this.brandUnitsMap; }

    public void putBrandUnitsMap(Integer brandId, List<Integer> unitIds) { brandUnitsMap.put(brandId, unitIds); }

    public IMap<UnitProductAsKey, RefLookupInfo> getWorkstationRoutingCache() {
        return workstationRoutingCache;
    }

    public void clearWorkstationRoutingCache() {
        instance.getMap("MasterDataCache:workstationRoutingCache").clear();
    }

    public ISet<String> getDreamFolksVoucherCodesUsed() {
        return dreamFolksVoucherCodesUsed;
    }

}
