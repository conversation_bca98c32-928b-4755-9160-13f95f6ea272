/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UNIT_PARTNER_BRAND_MAPPING_METADATA",
        uniqueConstraints = @UniqueConstraint(columnNames = {"UNIT_ID", "PARTNER_ID", "BRAND_ID", "METADATA_KEY"}))
public class UnitPartnerBrandMappingMetadata implements java.io.Serializable {

    private static final long serialVersionUID = -9133952113962183323L;
    private Integer metadataId;
    private Integer unitId;
    private Integer partnerId;
    private Integer brandId;
    private UnitPartnerBrandMappingMetadataType key;
    private String value;
    private String status;

    public UnitPartnerBrandMappingMetadata() {
    }

    public UnitPartnerBrandMappingMetadata(Integer unitId, Integer partnerId, Integer brandId, UnitPartnerBrandMappingMetadataType key, String value, String status) {
        this.unitId = unitId;
        this.partnerId = partnerId;
        this.brandId = brandId;
        this.key = key;
        this.value = value;
        this.status = status;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "METADATA_ID", unique = true, nullable = false)
    public Integer getMetadataId() {
        return metadataId;
    }

    public void setMetadataId(Integer metadataId) {
        this.metadataId = metadataId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "PARTNER_ID", nullable = false)
    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    @Column(name = "BRAND_ID", nullable = false)
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "METADATA_KEY", nullable = false)
    public UnitPartnerBrandMappingMetadataType getKey() {
        return key;
    }

    public void setKey(UnitPartnerBrandMappingMetadataType key) {
        this.key = key;
    }

    @Column(name = "METADATA_VALUE", nullable = false)
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Column(name = "MAPPING_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}