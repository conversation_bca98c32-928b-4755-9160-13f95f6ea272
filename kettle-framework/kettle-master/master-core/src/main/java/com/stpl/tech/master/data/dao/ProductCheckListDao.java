package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.ProductCheckListEvent;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.domain.model.Product;

import java.util.List;

public interface ProductCheckListDao {

    public String getProductStatus(Product product);

    public List<UnitProductMapping> getUnitProductMapping(Product product, List<Integer> unitIds);

    public String getUnitProductPricing(Integer unitProdRefId, Integer dimensionCode);

    public List<Integer> getMenuProductMappings(Integer productId, List<Integer> menuSequenceIds);

    public List<ProductImageMapping> getProductImageMapping(Integer productId);

    public ProductCheckListEvent getProductCheckListEventById(Integer eventId);

}
