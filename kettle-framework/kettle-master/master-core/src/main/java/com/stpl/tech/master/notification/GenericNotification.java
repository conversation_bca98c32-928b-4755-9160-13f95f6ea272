/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.kettle.report.metadata.model.ExecutionType;
import com.stpl.tech.kettle.report.metadata.model.ReportOutput;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.util.notification.ReportDetailData;
import org.springframework.util.StringUtils;
import org.subtlelib.poi.api.row.RowContext;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class GenericNotification {

	private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();
	private String[] toEmails;
	private String fromEmail;
	private String subject;
	private String schedule;
	private ReportOutput outputType;
	private boolean needsCompression;
	private boolean attachFile;
	private ExecutionType executionType;
	private List<ReportDetailData> reportsData;
	private List<AttachmentData> attachmentData;
	private Boolean forceEmail;
	WorkbookContext workbookCtx = ctxFactory.createWorkbook();
	private static final String LINE_DELIMITER = "\r\n";

	public String[] getToEmails() {
		return toEmails;
	}

	public void setToEmails(String[] toEmails) {
		this.toEmails = toEmails;
	}

	public String getFromEmail() {
		return fromEmail;
	}

	public void setFromEmail(String fromEmail) {
		this.fromEmail = fromEmail;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getSchedule() {
		return schedule;
	}

	public void setSchedule(String schedule) {
		this.schedule = schedule;
	}

	public ExecutionType getExecutionType() {
		return executionType;
	}

	public void setExecutionType(ExecutionType executionType) {
		this.executionType = executionType;
	}

	public List<ReportDetailData> getReportsData() {
		if (reportsData == null) {
			reportsData = new ArrayList<>();
		}
		return reportsData;
	}

	public void setReportsData(List<ReportDetailData> reportsData) {
		this.reportsData = reportsData;
	}

	public List<AttachmentData> getAttachmentData() {
		if (attachmentData == null) {
			attachmentData = new ArrayList<>();
		}
		return attachmentData;
	}

	public void setAttachmentData(List<AttachmentData> attachmentData) {
		this.attachmentData = attachmentData;
	}

	public ReportOutput getOutputType() {
		return outputType;
	}

	public void setOutputType(ReportOutput outputType) {
		this.outputType = outputType;
	}

	public boolean isNeedsCompression() {
		return needsCompression;
	}

	public void setNeedsCompression(boolean needsCompression) {
		this.needsCompression = needsCompression;
	}

	public boolean isAttachFile() {
		return attachFile;
	}

	public void setAttachFile(boolean attachFile) {
		this.attachFile = attachFile;
	}

	public void addReportData(ReportDetailData reportDetailData, boolean skipHeader) throws IOException {
		if (isAttachFile() && outputType != null && outputType.equals(ReportOutput.EXCEL)) {
			AttachmentData data = null;
			if (getAttachmentData().size() == 0) {
				data = new AttachmentData(subject, AppConstants.EXCEL_MIME_TYPE);
				getAttachmentData().add(data);
			} else {
				data = getAttachmentData().get(0);
			}
			QueryToExcelWriter writer = new QueryToExcelWriter(workbookCtx);
			writer.writeToSheet(reportDetailData, skipHeader);
			data.setAttachment(workbookCtx.toNativeBytes());
		} else if (isAttachFile() && outputType != null && outputType.equals(ReportOutput.CSV)) {
			AttachmentData data = new AttachmentData(
					reportDetailData.getName() + "-" + AppUtils.getCurrentDateISTFormatted(),
					AppConstants.CSV_MIME_TYPE);
			getAttachmentData().add(data);
			ByteArrayOutputStream b = new ByteArrayOutputStream();
			for (List<String> list : reportDetailData.getContent()) {
				b.write(StringUtils.collectionToCommaDelimitedString(list).getBytes());
				b.write(LINE_DELIMITER.getBytes());
			}
			data.setAttachment(b.toByteArray());
		}
		this.getReportsData().add(reportDetailData);
	}

	public Boolean getForceEmail() {
		return forceEmail;
	}

	public void setForceEmail(Boolean forceEmail) {
		this.forceEmail = forceEmail;
	}
}
