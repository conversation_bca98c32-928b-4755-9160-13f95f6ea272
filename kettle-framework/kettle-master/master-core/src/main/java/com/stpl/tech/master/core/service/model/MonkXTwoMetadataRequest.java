package com.stpl.tech.master.core.service.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class MonkXTwoMetadataRequest {
    private Integer unitId;
    private Integer noOfXTwoMachines;
    private Integer vesselSenseDelay;
    private Integer spiceSenseDelay;
    private Date lastUpdatedTimestamp;
    private List<MonkUrlDto> monkUrls;
}
