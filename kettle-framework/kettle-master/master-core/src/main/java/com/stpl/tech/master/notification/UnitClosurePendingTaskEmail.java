package com.stpl.tech.master.notification;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

public class UnitClosurePendingTaskEmail extends EmailNotification {

  private   UnitClosurePendingTaskTemplate template;
    private MasterProperties props;
    private String unitName;
    public UnitClosurePendingTaskEmail(UnitClosurePendingTaskTemplate template, MasterProperties props,String unitName){
        this.template = template;
        this.props = props;
        this.unitName   =unitName;
    }

    @Override
    public String[] getToEmails() {
        return new String[]{"<EMAIL>"};
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        return unitName + " Closure Pending Task Notification ";
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return props.getEnvironmentType();
    }
}
