/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.listeners;

import java.util.Date;

import org.hibernate.event.internal.DefaultSaveOrUpdateEventListener;
import org.hibernate.event.spi.SaveOrUpdateEvent;

import com.stpl.tech.master.core.CreationTime;

public class SaveOrUpdateDateListener extends DefaultSaveOrUpdateEventListener {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2552668506872082355L;

	public void onSaveOrUpdate(SaveOrUpdateEvent event) {
		Object object = event.getEntity();
		if (object instanceof CreationTime) {
			CreationTime creationObject = (CreationTime) object;
			creationObject.setCreationTime(new Date());
		}
		super.onSaveOrUpdate(event);
	}
}
