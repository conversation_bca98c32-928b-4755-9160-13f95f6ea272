package com.stpl.tech.master.core.service;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.data.model.UnitDroolVersionMapping;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.UnitDroolVersionDomain;

import java.util.List;
import java.util.Map;

public interface UnitDroolMappingService {

    public Map<Integer, Map<String, DroolVersionDomain>> getUnitDroolVersionMapping();
    public Boolean updateUnitDroolVersionMapping(String droolFile,Integer updatedBy, List<UnitDroolVersionDomain> mappingList);
}
