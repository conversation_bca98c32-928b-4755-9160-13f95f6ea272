package com.stpl.tech.master.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;

import javax.activation.UnsupportedDataTypeException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;

import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.kettle.report.metadata.model.ReportParam;
import com.stpl.tech.util.AppUtils;

public class AbstractQueryExecutor {
	private static final Logger LOG = LoggerFactory.getLogger(AbstractQueryExecutor.class);

	protected MapSqlParameterSource getParamSource(ReportData reportDefinition)
			throws UnsupportedDataTypeException, ParseException {
		MapSqlParameterSource paramSource = new MapSqlParameterSource();
		if (reportDefinition.getParam() != null && reportDefinition.getParam().size() > 0) {
			for (ReportParam param : reportDefinition.getParam()) {
				if (param.isMultiValued() != null && param.isMultiValued()) {
					paramSource.addValue(param.getName(), Arrays.asList(param.getValue().split(param.getDelimiter())));
				} else {
					Object value = getValue(param);
					LOG.info("Value Obtained from Param Value Conversion : " + value);
					paramSource.addValue(param.getName(), value);
				}
			}
		}
		return paramSource;
	}

	protected Object getValue(ReportParam param) throws ParseException, UnsupportedDataTypeException {
		LOG.info("Getting Param value for parameter " + param);
		switch (param.getDataType()) {
		case LONG:
			return param.getValue() != null ? Long.valueOf(param.getValue()) : 0L;
		case STRING:
			return param.getValue();
		case INTEGER:
			return param.getValue() != null ? Integer.valueOf(param.getValue()) : 0;
		case DOUBLE:
			return param.getValue() != null ? Double.valueOf(param.getValue()) : 0D;
		case BOOLEAN:
			return param.getValue() != null ? Boolean.valueOf(param.getValue()) : false;
		case DATE:
			SimpleDateFormat format = new SimpleDateFormat(param.getFormat());
			return param.getValue() != null ? format.parse(param.getValue()) : AppUtils.getCurrentDate();
		case TIMESTAMP:
			SimpleDateFormat format1 = new SimpleDateFormat(param.getFormat());
			return param.getValue() != null ? format1.parse(param.getValue()) : AppUtils.getCurrentTimestamp();
		default:
			throw new UnsupportedDataTypeException("Unsupported Data Type for " + param);
		}

	}

}
