/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 3 Aug, 2015 5:36:58 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * TaxProfile generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TAX_PROFILE")
public class TaxProfile implements java.io.Serializable {

	private Integer taxProfileId;
	private String taxType;
	private String taxName;
	private String taxProfileStatus;
	private List<UnitTaxMapping> unitTaxMappings = new ArrayList<UnitTaxMapping>(0);

	public TaxProfile() {
	}

	public TaxProfile(String taxType, String taxName) {
		this.taxType = taxType;
		this.taxName = taxName;
	}

	public TaxProfile(String taxType, String taxName, String state, List<UnitTaxMapping> unitTaxMappings) {
		this.taxType = taxType;
		this.taxName = taxName;
		this.unitTaxMappings = unitTaxMappings;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TAX_PROFILE_ID", unique = true, nullable = false)
	public Integer getTaxProfileId() {
		return this.taxProfileId;
	}

	public void setTaxProfileId(Integer taxProfileId) {
		this.taxProfileId = taxProfileId;
	}

	@Column(name = "TAX_TYPE", nullable = false, length = 20)
	public String getTaxType() {
		return this.taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

	@Column(name = "TAX_NAME", nullable = false, length = 40)
	public String getTaxName() {
		return this.taxName;
	}

	public void setTaxName(String taxName) {
		this.taxName = taxName;
	}

	@Column(name = "TAX_PROFILE_STATUS", nullable = false, length = 15)
	public String getTaxProfileStatus() {
		return taxProfileStatus;
	}

	public void setTaxProfileStatus(String taxProfileStatus) {
		this.taxProfileStatus = taxProfileStatus;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "taxProfile")
	public List<UnitTaxMapping> getUnitTaxMappings() {
		return this.unitTaxMappings;
	}

	public void setUnitTaxMappings(List<UnitTaxMapping> unitTaxMappings) {
		this.unitTaxMappings = unitTaxMappings;
	}

}
