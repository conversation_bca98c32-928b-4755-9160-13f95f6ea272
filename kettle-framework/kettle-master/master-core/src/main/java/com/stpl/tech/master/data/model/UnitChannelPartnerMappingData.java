package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "UNIT_CHANNEL_PARTNER_MAPPING",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"UNIT_ID", "CHANNEL_PARTNER_ID"})})
public class UnitChannelPartnerMappingData {

    private Integer id;
    private Integer unitId;
    private Integer channelPartnerId;
    private Integer deliveryPartnerId;
    private String status;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "CHANNEL_PARTNER_ID", nullable = false)
    public Integer getChannelPartnerId() {
        return channelPartnerId;
    }

    public void setChannelPartnerId(Integer channelPartnerId) {
        this.channelPartnerId = channelPartnerId;
    }

    @Column(name = "DELIVERY_PARTNER_ID", nullable = false)
    public Integer getDeliveryPartnerId() {
        return deliveryPartnerId;
    }

    public void setDeliveryPartnerId(Integer deliveryPartnerId) {
        this.deliveryPartnerId = deliveryPartnerId;
    }

    @Column(name = "STATUS", length = 10)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
