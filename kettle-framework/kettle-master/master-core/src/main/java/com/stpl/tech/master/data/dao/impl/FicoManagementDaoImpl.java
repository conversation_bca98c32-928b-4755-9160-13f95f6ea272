package com.stpl.tech.master.data.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.FicoManagementDao;
import com.stpl.tech.master.data.model.FicoDetailData;
import com.stpl.tech.master.domain.model.FicoDetail;

@Repository
public class FicoManagementDaoImpl extends AbstractMasterDaoImpl implements FicoManagementDao {

	@Override
	public List<FicoDetailData> getFicoList() {
		Query query = manager.createQuery("FROM FicoDetailData f ");
		return query.getResultList();
	}

	@Override
	public boolean addFicoDetails(FicoDetail ficoDetail) throws DataUpdationException {
		FicoDetailData ficoDetailData = new FicoDetailData();
		ficoDetailData.setUnitId(ficoDetail.getUnitId().getId());
		ficoDetailData.setCompanyName(ficoDetail.getCompanyName());
		ficoDetailData.setName(ficoDetail.getName());
		ficoDetailData.setContact1(ficoDetail.getContact1());
		ficoDetailData.setEmailId(ficoDetail.getEmailId());
		ficoDetailData.setGstin(ficoDetail.getGstin());
		ficoDetailData.setRegisteredAddressId(ficoDetail.getAddresssId());
		ficoDetailData.setReportingEmailId(ficoDetail.getReportingEmailId());
		if (ficoDetail.getContact2() != null) {
			ficoDetailData.setContact2(ficoDetail.getContact2());
		}
		if (ficoDetail.getOfferCode() != null) {
			ficoDetailData.setOfferCode(ficoDetail.getOfferCode());
		}
		try {
			add(ficoDetailData);
		} catch (Exception e) {
			throw new DataUpdationException("Exception occured while adding Fico details" + e.getMessage());
		}
		return false;
	}

	@Override
	public boolean updateFicoDetails(FicoDetail ficoDetail) {
		// TODO Auto-generated method stub
		return false;
	}

}
