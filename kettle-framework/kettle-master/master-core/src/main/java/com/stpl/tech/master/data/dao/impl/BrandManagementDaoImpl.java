/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.BrandManagementDao;
import com.stpl.tech.master.data.model.BrandAttributes;
import com.stpl.tech.master.data.model.BrandDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadata;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
public class BrandManagementDaoImpl extends AbstractMasterDaoImpl implements BrandManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(BrandManagementDaoImpl.class);

    @Override
    public UnitPartnerBrandMappingData getUnitPartnerBrandMapping(UnitPartnerBrandKey key) {
        Query query = manager.createQuery("FROM UnitPartnerBrandMappingData e WHERE e.unitId = :unitId AND e.brandId = :brandId " +
                "AND e.partnerId = :partnerId AND e.status = :status");
        query.setParameter("unitId", key.getUnitId());
        query.setParameter("brandId", key.getBrandId());
        query.setParameter("partnerId", key.getPartnerId());
        query.setParameter("status", AppConstants.ACTIVE);
        try {
            return (UnitPartnerBrandMappingData) query.getSingleResult();
        } catch (NoResultException | NonUniqueResultException e) {
            LOG.error("error getting UnitPartnerBrandMappingData", e);
            return null;
        }
    }

    @Override
    public List<UnitPartnerBrandMappingData> getAllUnitPartnerBrandMapping(Integer brandId,Integer partnerId) {
        Query query = manager.createQuery("FROM UnitPartnerBrandMappingData e WHERE  e.brandId = :brandId " +
                "AND e.partnerId = :partnerId");
        query.setParameter("brandId", brandId);
        query.setParameter("partnerId", partnerId);
        //query.setParameter("status", AppConstants.ACTIVE);
        try {
            List<UnitPartnerBrandMappingData> unitPartnerBrandMappingData =  query.getResultList();
            if(unitPartnerBrandMappingData==null){
                unitPartnerBrandMappingData = new ArrayList<>();
            }
            return unitPartnerBrandMappingData;
        } catch (NoResultException | NonUniqueResultException e) {
            LOG.error("error getting UnitPartnerBrandMappingData", e);
            return null;
        }
        }

    @Override
    public List<BrandAttributes> getBrandAttributesByBrandId(int brandId) {
        Query query = manager.createQuery("FROM BrandAttributes b where b.brandId =:brandId");
        query.setParameter("brandId", brandId);

        List<BrandAttributes> brandAttributes = query.getResultList();
        if(brandAttributes == null){
            brandAttributes = new ArrayList<>();
        }
        return brandAttributes;
    }

    @Override
    public  List<UnitPartnerBrandMappingMetadata> getAllUnitPartnerBrandMappingMetadata() {
        Query query = manager.createQuery("FROM UnitPartnerBrandMappingMetadata e WHERE e.status = :status");
        query.setParameter("status", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public  List<UnitPartnerBrandMappingMetadata> getUnitPartnerBrandMappingMetadataByKey(UnitPartnerBrandMappingMetadataType key) {
        Query query = manager.createQuery("FROM UnitPartnerBrandMappingMetadata e WHERE key = :key");
        query.setParameter("key", key);
        return query.getResultList();
    }

    @Override
    public UnitPartnerBrandMappingMetadata getUnitPartnerBrandMappingMetadata(UnitPartnerBrandMappingMetadataType key, Integer unitId, Integer partnerId, Integer brandId) {
        Query query = manager.createQuery("FROM UnitPartnerBrandMappingMetadata e WHERE e.unitId = :unitId " +
                "AND e.partnerId = :partnerId AND e.brandId = :brandId AND e.key = :key");
        query.setParameter("unitId", unitId);
        query.setParameter("partnerId", partnerId);
        query.setParameter("brandId", brandId);
        query.setParameter("key", key);
        try {
            return (UnitPartnerBrandMappingMetadata) query.getSingleResult();
        } catch (NoResultException | NonUniqueResultException e) {
            LOG.error("Error getting UnitPartnerBrandMappingMetadata", e);
            return null;
        }
    }

    @Override
    public BrandAttributes getBrandAttributesByBrandIdAndAttributeKey(Integer brandId, String attributeKey) {
        Query query=manager.createQuery("FROM BrandAttributes b where b.brandId = :brandId and b.attributeKey= :attributeKey");
        query.setParameter("brandId",brandId);
        query.setParameter("attributeKey",attributeKey);
        try {
            query.getSingleResult();
        }catch (Exception e){
            LOG.info("no result found ",e);
        }
        return null;
    }

    @Override
    public UnitPartnerBrandMappingData checkUnitPartnerBrandMapping(UnitPartnerBrandKey key, Integer priceProfileId, String restaurantId) {
        Query query = manager.createQuery("FROM UnitPartnerBrandMappingData e WHERE e.unitId = :unitId AND e.brandId = :brandId " +
            "AND e.partnerId = :partnerId");
        query.setParameter("unitId", key.getUnitId());
        query.setParameter("brandId", key.getBrandId());
        query.setParameter("partnerId", key.getPartnerId());
//        query.setParameter("priceProfileId", priceProfileId);
//        query.setParameter("restaurantId", restaurantId);
        try {
            return (UnitPartnerBrandMappingData) query.getSingleResult();
        } catch ( NonUniqueResultException e) {
            LOG.error("Non Unique UnitPartnerBrandMappingData", e);
            return (UnitPartnerBrandMappingData) query.getSingleResult();
        }catch (NoResultException e){
            LOG.info("No result found  ", e);
            return null;
        }
    }

    @Override
    public Boolean inactiveAllRestMapping(UnitPartnerBrandKey key) {
        Query query = manager.createQuery("UPDATE  UnitPartnerBrandMappingData e  SET e.status = :status" +
            "  WHERE e.unitId = :unitId AND e.brandId = :brandId " +
            "AND e.partnerId = :partnerId AND e.status=:currentStatus ");
        query.setParameter("unitId", key.getUnitId());
        query.setParameter("brandId", key.getBrandId());
        query.setParameter("partnerId", key.getPartnerId());
        query.setParameter("status", AppConstants.IN_ACTIVE);
        query.setParameter("currentStatus", AppConstants.ACTIVE);
        int t=query.executeUpdate();
        LOG.info("Number of query executed {}",t);
        manager.flush();
        return t > 0;

    }

    @Override
    public List<BrandAttributes> getBrandAttributes() {
        Query query = manager.createQuery("FROM BrandAttributes b");
        List<BrandAttributes> brandAttributes = query.getResultList();
        if(brandAttributes == null){
            brandAttributes = new ArrayList<>();
        }
        return brandAttributes;
    }

    @Override
    public void updateBrandAttributes(BrandAttributes brandAttribute){
        try {
            Query query = manager.createQuery("UPDATE BrandAttributes b SET b.attributeValue = :value where b.attributeKey = :key AND b.brandId = :brandId");
            query.setParameter("value",brandAttribute.getAttributeValue());
            query.setParameter("key",brandAttribute.getAttributeKey());
            query.setParameter("brandId",brandAttribute.getBrandId());
            query.executeUpdate();
        }catch (Exception e){
            LOG.info("Error while updating brand attributes :::: {}",e);
        }
    }

    @Override
    public List<BrandDetail> getActiveBrandDetails() {
        try {
            Query query = manager.createQuery("SELECT bd FROM BrandDetail bd where bd.status = :status");
            query.setParameter("status", AppConstants.ACTIVE);
            if (Objects.isNull(query.getResultList()) || query.getResultList().isEmpty()) {
                return new ArrayList<>();
            }
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error occurred while getting ACTIVE brands : {}", e);
        }
        return new ArrayList<>();
    }

}
