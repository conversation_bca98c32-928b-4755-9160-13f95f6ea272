/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.notification.sms;

public class SMSConfiguration {

	private String userId;

	private String passCode;

	private String url;

	public SMSConfiguration(String passCode, String url){
		super ();
		this.passCode=passCode;
		this.url=url;
	}
	public SMSConfiguration(String userId, String passCode, String url) {
		super();
		this.userId = userId;
		this.passCode = passCode;
		this.url = url;
	}

	public String getUserId() {
		return userId;
	}

	public String getPassCode() {
		return passCode;
	}

	public String getUrl() {
		return url;
	}

}
