package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.ApplicationData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApplicationDataRepository extends JpaRepository<ApplicationData, Integer> {
    
    /**
     * Find application by name
     * @param name application name
     * @return ApplicationData or null if not found
     */
    ApplicationData findByName(String name);
    
    /**
     * Find all active applications
     * @return List of active applications
     */
    List<ApplicationData> findByStatus(String status);
    
    /**
     * Find application by name and status
     * @param name application name
     * @param status application status
     * @return ApplicationData or null if not found
     */
    ApplicationData findByNameAndStatus(String name, String status);
} 