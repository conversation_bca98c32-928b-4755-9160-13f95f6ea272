package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "UNIT_CLOSURE_EVENT", indexes = {
        @Index(name = "IDX_UNIT_CLOSURE_EVENT_UNIT_ID", columnList = "UNIT_ID"),
        @Index(name = "IDX_UNIT_CLOSURE_EVENT_CLOSURE_STATUS", columnList = "CLOSURE_STATUS")
})
public class UnitClosureEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "REQUEST_ID", nullable = false)
    private Long requestId;
    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;
    @Temporal(TemporalType.DATE)
    @Column(name = "CLOSURE_DATE", nullable = false)
    private Date closureDate;
    @Temporal(TemporalType.DATE)
    @Column(name = "OPERATION_STOP_DATE", nullable = false)
    private Date operationStopDate;
    @Column(name = "CLOSURE_STATUS")
    private String closureStatus;
    @Column(name = "MESSAGE", nullable = false)
    private String message;
    @Column(name = "CREATED_BY", nullable = false)
    private Integer createdBy;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false)
    private Date creationTIme;
    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATION_TIME")
    private Date lastUpdationTime;
    @Column(name = "IS_FORM_SUBMITTED")
    private String isFormSubmitted;
}