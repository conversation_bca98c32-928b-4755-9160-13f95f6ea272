package com.stpl.tech.master.core.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Stopwatch;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.EmployeeManagementService;
import com.stpl.tech.master.core.service.UserService;
import com.stpl.tech.master.core.service.model.UnitEmpData;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.TimeUnit;

// Simple wrapper class for API response
class LucidApiResponse<T> {
    private String result;
    private T data;
    
    public String getResult() { return result; }
    public void setResult(String result) { this.result = result; }
    public T getData() { return data; }
    public void setData(T data) { this.data = data; }
}

@Slf4j
@Service
public class EmployeeManagementServiceImpl implements EmployeeManagementService {

    public static final String HTTPS_CHAAYOS_LUCIDTECH_COM_API_V_1 = "https://chaayos.lucidtech.com/api/v1/";
    public static final String LAST_ACTIVITY_DATE = "2015-01-01T00:00:00";
    public static final String PAGE_LIMIT = "500";
    public static final String API_KEY_FOR_ALL_EMP_DATA = "44294289-1592-4f7e-915d-ec8b0e789b76";
    public static final String API_KEY_FOR_EMP_DATA = "9277c74d-b701-4dfd-ac2c-56a5ab0c897b";
    public static final String HEAD_OFFICE = "National Head Office";
    public static final String OFFICE_DELHI = "Office Delhi";
    public static final String OPERATIONS_CAFE = "Operations, Cafe";
    public static final String LUCID = "LUCID";
    public static final String KETTLE = "KETTLE";

    @Autowired
    private UserService userService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private MasterProperties masterProperties;



    // Cache for frequently accessed data
    private final Map<String, Employee> reportingManagerCache = new HashMap<>();
    private final Map<String, Map<String, Integer>> departmentDesignationCache = new HashMap<>();
    
    // Pre-loaded department and designation mappings for batch processing
    private Map<String, Department> departmentCache = new HashMap<>();
    private Map<String, Designation> designationCache = new HashMap<>();
    private Map<String, Integer> departmentDesignationMappingCache = new HashMap<>();
    
    // Additional caches for better performance
    private Map<Integer, com.stpl.tech.master.data.model.Department> departmentDataCache = new HashMap<>();
    private Map<Integer, com.stpl.tech.master.data.model.Designation> designationDataCache = new HashMap<>();
    private Map<Integer, Set<com.stpl.tech.master.data.model.Designation>> departmentDesignationsCache = new HashMap<>();
    private Company companyCache = null;

    /**
     * Pre-load all department and designation mappings to avoid database calls during processing
     */
    private void preloadDepartmentDesignationMappings() {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        
        log.info("Pre-loading department and designation mappings...");
        
        try {
            // Pre-load all departments
            List<Department> allDepartments = masterDataCache.getDepartmentsValues();
            for (Department dept : allDepartments) {
                departmentCache.put(dept.getName(), dept);
            }
            
            // Pre-load all designations
            List<Designation> allDesignations = masterDataCache.getDesignationsValues();
            for (Designation desig : allDesignations) {
                designationCache.put(desig.getName(), desig);
            }
            
            log.info("Pre-loaded {} departments and {} designations in {}ms", 
                    departmentCache.size(), designationCache.size(),
                    watch.stop().elapsed(TimeUnit.MILLISECONDS));
                    
        } catch (Exception e) {
            log.error("Error pre-loading department/designation mappings", e);
        }
    }

    /**
     * Optimized department/designation lookup using pre-loaded data
     */
    private Map<String, Integer> lookUpDepartmentDesignationOptimized(String departmentName, String designationName) {
        Map<String, Integer> result = new HashMap<>();
        
        // Get department from cache
        Department department = departmentCache.get(departmentName);
        if (Objects.isNull(department)) {
            log.warn("Department not found in cache: {}", departmentName);
            return result;
        }
        
        Integer deptId = department.getId();
        
        // Check if designation exists in cache
        Designation designation = designationCache.get(designationName);
        if (Objects.isNull(designation)) {
            log.warn("Designation not found in cache: {}", designationName);
            return result;
        }
        
        Integer designationId = designation.getId();
        
        // Check if mapping already exists using the existing lookup method
        Integer existingMapping = userService.lookUpDepartmentDesignation(department, designationName);
        if (existingMapping > 0) {
            result.put("departmentId", deptId);
            result.put("designationId", existingMapping);
            return result;
        }
        
        // If mapping doesn't exist, create it (this should be rare in production)
        try {
            Boolean mappingCreated = userService.addDepartmentDesignationMapping(deptId, designationId);
            if (mappingCreated) {
                result.put("departmentId", deptId);
                result.put("designationId", designationId);
                log.debug("Created new department-designation mapping: {} -> {}", departmentName, designationName);
            }
        } catch (Exception e) {
            log.error("Error creating department-designation mapping for {} -> {}", departmentName, designationName, e);
        }
        
        return result;
    }

    @Cacheable("departmentDesignationMapping")
    public Map<String, Integer> lookUpDepartmentDesignation(String departmentName, String designationName) {
        log.info("call lookUpDepartmentDesignation for department : {} and designation : {}", departmentName, designationName);
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        
        Map<String, Integer> map = new HashMap<>();
        Department department = masterDataCache.getDeptartment(departmentName);
        
        if (Objects.nonNull(department)) {
            Integer deptId = department.getId();
            Integer designationId = userService.lookUpDepartmentDesignation(department, designationName);
            Boolean flag = true;
            
            if (designationId == 0) {
                Designation designation = masterDataCache.getDesignation(designationName);
                if (Objects.nonNull(designation)) {
                    designationId = designation.getId();
                    flag = userService.addDepartmentDesignationMapping(deptId, designationId);
                    log.info("value flag is : {} for designation : {} with id 0", flag, designationName);
                    if (flag) {
                        masterDataCache.addDesignationToDepartment(designation, department);
                    }
                } else {
                    log.info("Designation : {} does not exist in Kettle", designationName);
                    flag = false;
                }
            }
            
            if (flag) {
                log.info("department and designation id in Map is : {},{}", deptId, designationId);
                map.put("departmentId", deptId);
                map.put("designationId", designationId);
            }
        }
        
        log.info("&&&&&&&&&&  - ,LOOK UP FOR DEPARTMENT DESIGNATION COMPLETED ----------, {}ms", 
                watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return map;
    }

    // New method for non-paginated API call
    private EmployeeResponse sendRequestForAllEmployeeDetailDataNonPaginated(String lastActivityDate) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        
        EmployeeResponse employeeResponse = new EmployeeResponse();
        List<EmployeeData> data = new ArrayList<>();
        
        // Create request body with lastActivityDate
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("lastActivityDate", lastActivityDate);
        
        String endPoint = HTTPS_CHAAYOS_LUCIDTECH_COM_API_V_1 + "active-employees-realtime?api_key=" + masterProperties.getLucidApiKey();
        
        try {
            // Handle new response structure with wrapper
            LucidApiResponse<List<Object>> responseWrapper = WebServiceHelper.getRequestWithBodyAndHeader(endPoint, masterProperties.getLucidApiKey(), requestBody, LucidApiResponse.class, false);
            
            if (responseWrapper != null && "Success".equalsIgnoreCase(responseWrapper.getResult())) {
                List<Object> rawData = responseWrapper.getData();
                
                if (rawData != null && !rawData.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    
                    for (Object rawItem : rawData) {
                        try {
                            EmployeeData employeeData = objectMapper.convertValue(rawItem, EmployeeData.class);
                            data.add(employeeData);
                        } catch (Exception e) {
                            log.error("Error converting raw data to EmployeeData: {}", e.getMessage(), e);
                        }
                    }
                } else {
                    log.info("No employee data received from API");
                }
            } else {
                log.warn("API response indicates failure: {}", responseWrapper != null ? responseWrapper.getResult() : "null response");
            }
            
            employeeResponse.setData(data);
            employeeResponse.setTotalCount(data.size());
            
            log.info("Retrieved {} employees from Lucid API in {}ms", data.size(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
            
        } catch (IOException | URISyntaxException e) {
            log.error("Error in fetching employee data from Lucid", e);
            throw new RuntimeException("Failed to fetch employee data from Lucid", e);
        }
        
        return employeeResponse;
    }

    private EmployeeDataResponse sendRequestForEmployeeDetailData(String empId) {
        Map<String, String> params = new HashMap<>();
        params.put("attr", "empId");
        params.put("val", empId);
        String endPoint = HTTPS_CHAAYOS_LUCIDTECH_COM_API_V_1 + "get-employee";
        try {
            EmployeeDataResponse employeeData = WebServiceHelper.getRequestWithParamAndHeader(endPoint, API_KEY_FOR_EMP_DATA, params, EmployeeDataResponse.class, false);
            return employeeData;
        } catch (IOException | URISyntaxException e) {
            log.error("Error in fetching employee data from lucid for empid :{}", empId, e);
        }
        return null;
    }

    @Override
    public Integer syncEmployeeData() {
        return syncEmployeeDataInternal(null);
    }

    @Override
    public Integer syncEmployeeDataFromEmployee(String startFromEmployeeCode) {
        if (StringUtils.isEmpty(startFromEmployeeCode)) {
            log.warn("Employee code is empty, falling back to normal sync");
            return syncEmployeeData();
        }
        log.info("Starting employee sync from employee code: {}", startFromEmployeeCode);
        return syncEmployeeDataInternal(startFromEmployeeCode);
    }

    /**
     * Internal method that handles employee sync (defaults to active employees)
     * @param startFromEmployeeCode If not null, start processing from this employee code
     * @return Number of employees processed
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    private Integer syncEmployeeDataInternal(String startFromEmployeeCode) {
        return syncEmployeeDataInternal(startFromEmployeeCode, false);
    }

    /**
     * Internal method that handles employee sync with optional inactive filtering
     * @param startFromEmployeeCode If not null, start processing from this employee code
     * @param syncInactiveOnly If true, only process inactive employees (employeeStatus != "1")
     * @return Number of employees processed
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    private Integer syncEmployeeDataInternal(String startFromEmployeeCode, boolean syncInactiveOnly) {
        Stopwatch totalWatch = Stopwatch.createUnstarted();
        totalWatch.start();
        
        int noOfEmployeesAddedOrUpdated = 0;
        String currentTimeStamp = AppUtils.getCurrentUTCDateTime();
        String lastActivityDate = userService.getLastActivityTime(LUCID, KETTLE);
        if (Objects.isNull(lastActivityDate)) {
            lastActivityDate = LAST_ACTIVITY_DATE;
        }
        
        // Clear caches at the start
        reportingManagerCache.clear();
        departmentDesignationCache.clear();
        
        // Pre-load department and designation mappings for better performance
        preloadDepartmentDesignationMappings();
        
        try {
            // Get all employee data in one API call
            EmployeeResponse employeeResponse = sendRequestForAllEmployeeDetailDataNonPaginated(lastActivityDate);
            
            if (Objects.isNull(employeeResponse) || employeeResponse.getData().isEmpty()) {
                log.warn("No employee data received from Lucid API");
                return 0;
            }
            
            log.info("Processing {} employees from Lucid API{}", 
                    employeeResponse.getData().size(), 
                    syncInactiveOnly ? " for inactive sync" : "");
            
            // Filter employees if starting from specific employee code
            List<EmployeeData> employeesToProcess = employeeResponse.getData();
            if (StringUtils.isNotEmpty(startFromEmployeeCode)) {
                employeesToProcess = filterEmployeesFromCode(employeeResponse.getData(), startFromEmployeeCode);
                log.info("Filtered to {} employees starting from employee code: {}", 
                        employeesToProcess.size(), startFromEmployeeCode);
            }
            
            // Filter for inactive employees if requested
            if (syncInactiveOnly) {
                List<EmployeeData> inactiveEmployees = new ArrayList<>();
                for (EmployeeData employeeData : employeesToProcess) {
                    if (!employeeData.getEmployeeStatus().equalsIgnoreCase("1")) {
                        inactiveEmployees.add(employeeData);
                    }
                }
                employeesToProcess = inactiveEmployees;
                log.info("Filtered to {} inactive employees out of {} total employees", 
                        inactiveEmployees.size(), employeeResponse.getData().size());
                
                if (inactiveEmployees.isEmpty()) {
                    log.info("No inactive employees found to process");
                    return 0;
                }
            }
            
            // Process employees in a single transaction
            Map<String, String> employeeReporterMapping = new HashMap<>();
            Map<String, EmployeeData> employeeDataMap = new HashMap<>();
            
            // Create lookup maps for better performance
            Map<String, Employee> existingEmployeesCache = new HashMap<>();
            
            // Phase 1: Process employees with existing reporting managers
            for (EmployeeData employeeData : employeesToProcess) {
                try {
                    employeeDataMap.put(employeeData.getEmpId(), employeeData);

                    // Skip operations cafe (same logic for both active and inactive sync)
                    if (!isValidEmployee(employeeData)) {
                        log.debug("Skipping employee {} - invalid employee data", employeeData.getEmpId());
                        continue;
                    }

                    // Check if reporting manager exists
                    Employee reporter = getReportingManagerWithCache(employeeData, existingEmployeesCache);

                    if (Objects.isNull(employeeData.getReportingManagerEmpId())) {
                        log.debug("Employee {} has no reporting manager, skipping", employeeData.getEmpId());
                        continue;
                    }

                    if (Objects.isNull(reporter)) {
                        employeeReporterMapping.put(employeeData.getEmpId(), employeeData.getReportingManagerEmpId());
                        log.debug("Employee {} reporting manager not found, will process in phase 2", employeeData.getEmpId());
                    } else {
                        // Process employee immediately if reporting manager exists
                        if (processEmployeeInTransaction(employeeData)) {
                            noOfEmployeesAddedOrUpdated++;
                            log.debug("Successfully processed employee: {}", employeeData.getEmpId());
                        } else {
                            log.warn("Failed to process employee: {}", employeeData.getEmpId());
                        }
                    }
                    log.info("number of employees{} is : {}", 
                            syncInactiveOnly ? " processed for inactive sync" : " added or updated", 
                            noOfEmployeesAddedOrUpdated);
                } catch (Exception e) {
                    log.error("Error processing employee: {} - Error: {}", employeeData.getEmpId(), e.getMessage(), e);
                    // Continue processing other employees - don't let one failure stop the entire sync
                }
            }
            
            // Phase 2: Process employees with missing reporting managers
            if (!employeeReporterMapping.isEmpty()) {
                log.info("Processing {} employees with missing reporting managers", employeeReporterMapping.size());
                noOfEmployeesAddedOrUpdated += processEmployeesWithMissingManagersInTransaction(employeeReporterMapping, employeeDataMap);
            }
            
            // Update last activity time
            userService.addLastActivityTime(LUCID, KETTLE, currentTimeStamp);
            
            String syncType = syncInactiveOnly ? "INACTIVE EMPLOYEE" : "EMPLOYEE";
            log.info("******{} DATA SYNC FROM LUCID COMPLETED****** Total processed: {}, Time taken: {}ms", 
                    syncType, noOfEmployeesAddedOrUpdated, totalWatch.stop().elapsed(TimeUnit.MILLISECONDS));
            
            return noOfEmployeesAddedOrUpdated;
            
        } catch (Exception e) {
            String syncType = syncInactiveOnly ? "inactive employee" : "employee";
            log.error("Critical error in {} sync process", syncType, e);
            throw new RuntimeException(syncType + " sync failed", e);
        }
    }

    /**
     * Filter employees starting from a specific employee code
     * @param allEmployees List of all employees from API
     * @param startFromEmployeeCode Employee code to start from
     * @return Filtered list of employees
     */
    private List<EmployeeData> filterEmployeesFromCode(List<EmployeeData> allEmployees, String startFromEmployeeCode) {
        List<EmployeeData> filteredEmployees = new ArrayList<>();
        boolean foundStartEmployee = false;
        
        for (EmployeeData employee : allEmployees) {
            if (foundStartEmployee) {
                filteredEmployees.add(employee);
            } else if (startFromEmployeeCode.equals(employee.getEmpId())) {
                foundStartEmployee = true;
                filteredEmployees.add(employee);
                log.info("Found starting employee: {}, will process this and subsequent employees", startFromEmployeeCode);
            }
        }
        
        if (!foundStartEmployee) {
            log.warn("Employee code {} not found in API response. Processing all employees.", startFromEmployeeCode);
            return allEmployees;
        }
        
        return filteredEmployees;
    }

    /**
     * Utility method to find the last processed employee from the API response
     * This can help identify where the process got stuck
     * @param lastProcessedEmployeeCode The employee code that was last processed
     * @return The next employee code to start from, or null if not found
     */
    public String findNextEmployeeToProcess(String lastProcessedEmployeeCode) {
        try {
            String lastActivityDate = userService.getLastActivityTime(LUCID, KETTLE);
            if (Objects.isNull(lastActivityDate)) {
                lastActivityDate = LAST_ACTIVITY_DATE;
            }
            
            EmployeeResponse employeeResponse = sendRequestForAllEmployeeDetailDataNonPaginated(lastActivityDate);
            
            if (Objects.isNull(employeeResponse) || employeeResponse.getData().isEmpty()) {
                log.warn("No employee data received from Lucid API");
                return null;
            }
            
            List<EmployeeData> employees = employeeResponse.getData();
            boolean foundLastEmployee = false;
            
            for (EmployeeData employee : employees) {
                if (foundLastEmployee) {
                    log.info("Next employee to process after {} is: {}", lastProcessedEmployeeCode, employee.getEmpId());
                    return employee.getEmpId();
                } else if (lastProcessedEmployeeCode.equals(employee.getEmpId())) {
                    foundLastEmployee = true;
                    log.info("Found last processed employee: {}", lastProcessedEmployeeCode);
                }
            }
            
            if (!foundLastEmployee) {
                log.warn("Last processed employee {} not found in API response", lastProcessedEmployeeCode);
                return null;
            }
            
            log.info("Last processed employee {} was the last in the list", lastProcessedEmployeeCode);
            return null;
            
        } catch (Exception e) {
            log.error("Error finding next employee to process", e);
            return null;
        }
    }

    // Helper method to validate employee data
    private boolean isValidEmployee(EmployeeData employeeData) {
        return
               !employeeData.getDepartment().equalsIgnoreCase(OPERATIONS_CAFE);
    }

    // Cached version of getReportingManager
    private Employee getReportingManagerWithCache(EmployeeData employeeData, Map<String, Employee> cache) {
        if (Objects.isNull(employeeData.getReportingManagerEmpId())) {
            return null;
        }
        
        return cache.computeIfAbsent(employeeData.getReportingManagerEmpId(), 
            empId -> userService.getEmployee(empId));
    }

    /**
     * Process employee within the main transaction (simplified version)
     */
    private boolean processEmployeeInTransaction(EmployeeData employeeData) {
        try {
            // Get department/designation mapping with caching
            String cacheKey = employeeData.getDepartment() + ":" + employeeData.getDesignation();
            Map<String, Integer> departmentDesignationIdMap = departmentDesignationCache.computeIfAbsent(cacheKey, 
                key -> lookUpDepartmentDesignationOptimized(employeeData.getDepartment(), employeeData.getDesignation()));
            if(departmentDesignationIdMap.isEmpty()){
                departmentDesignationIdMap =  lookUpDepartmentDesignationOptimized(employeeData.getDepartment(), employeeData.getDesignation());
                departmentDesignationCache.put(cacheKey, departmentDesignationIdMap);
            }
            if (departmentDesignationIdMap.getOrDefault("departmentId", 0) == 0 || 
                departmentDesignationIdMap.getOrDefault("designationId", 0) == 0) {
                log.warn("Invalid department/designation mapping for employee: {}", employeeData.getEmpId());
                return false;
            }
            
            Employee reporter = getReportingManagerWithCache(employeeData, reportingManagerCache);
            if (Objects.isNull(reporter)) {
                log.warn("Reporting manager not found for employee: {}", employeeData.getEmpId());
                return false;
            }
            
            return updateEmployeeDetailOptimized(employeeData, departmentDesignationIdMap, reporter);
            
        } catch (Exception e) {
            log.error("Error processing employee: {}", employeeData.getEmpId(), e);
            return false;
        }
    }

    /**
     * Process employees with missing managers within the main transaction
     */
    private int processEmployeesWithMissingManagersInTransaction(Map<String, String> employeeReporterMapping, 
                                                               Map<String, EmployeeData> employeeDataMap) {
        int processedCount = 0;
        int maxRetries = 3; // Prevent infinite loops
        int currentRetry = 0;
        
        while (!employeeReporterMapping.isEmpty() && currentRetry < maxRetries) {
            currentRetry++;
            log.info("Processing missing managers - Attempt {}, Remaining employees: {}", 
                    currentRetry, employeeReporterMapping.size());
            
            Map<String, String> remainingEmployees = new HashMap<>();
            
            for (Map.Entry<String, String> entry : employeeReporterMapping.entrySet()) {
                String empId = entry.getKey();
                String reporterEmpId = entry.getValue();
                
                try {
                    EmployeeData employeeData = employeeDataMap.get(empId);
                    if (Objects.isNull(employeeData)) {
                        log.warn("Employee data not found for ID: {}", empId);
                        continue;
                    }
                    
                    // Check if employee is inactive
                    if (LucidEmploymentStatusType.IN_ACTIVE.getMappingStatus().equalsIgnoreCase(employeeData.getEmployeeStatus())) {
                        setEmployeeInactiveInTransaction(employeeData.getEmpId(), employeeData.getContact());
                        log.debug("Set employee {} as inactive", employeeData.getEmpId());
                        continue;
                    }
                    
                    // Try to get or create reporting manager
                    Employee reporter = getOrCreateReportingManagerInTransaction(reporterEmpId, employeeDataMap);
                    
                    if (Objects.nonNull(reporter)) {
                        if (processEmployeeInTransaction(employeeData)) {
                            processedCount++;
                            log.debug("Successfully processed employee with missing manager: {}", empId);
                        } else {
                            log.warn("Failed to process employee with missing manager: {}", empId);
                        }
                    } else {
                        // Keep for next iteration
                        remainingEmployees.put(empId, reporterEmpId);
                        log.debug("Employee {} still has missing manager, will retry", empId);
                    }
                    log.info("processed count is : {}", processedCount);
                } catch (Exception e) {
                    log.error("Error processing employee with missing manager: {} - Error: {}", empId, e.getMessage(), e);
                    // Continue processing other employees - don't let one failure stop the entire sync
                }
            }
            
            employeeReporterMapping = remainingEmployees;
        }
        
        if (!employeeReporterMapping.isEmpty()) {
            log.warn("Could not process {} employees after {} attempts - possible circular dependencies", 
                    employeeReporterMapping.size(), maxRetries);
        }
        
        return processedCount;
    }

    /**
     * Set employee inactive within the main transaction
     */
    private void setEmployeeInactiveInTransaction(String empId, String contact) {
        try {
            userService.setEmployeeInActive(empId, contact);
            log.info("Successfully set employee {} as inactive", empId);
        } catch (Exception e) {
            log.error("Error setting employee {} as inactive: {}", empId, e.getMessage(), e);
            throw e; // Re-throw to trigger rollback
        }
    }

    /**
     * Get or create reporting manager within the main transaction
     */
    private Employee getOrCreateReportingManagerInTransaction(String reporterEmpId, Map<String, EmployeeData> employeeDataMap) {
        // First check if manager already exists in our system
        Employee existingManager = userService.getEmployee(reporterEmpId);
        if (Objects.nonNull(existingManager)) {
            return existingManager;
        }
        
        // Try to get manager data from the API response
        EmployeeData reporterData = employeeDataMap.get(reporterEmpId);
        if (Objects.isNull(reporterData)) {
            // Fetch individual employee data if not in batch
            EmployeeDataResponse reporterDataResponse = sendRequestForEmployeeDetailData(reporterEmpId);
            if (Objects.isNull(reporterDataResponse) || reporterDataResponse.getData().isEmpty()) {
                log.warn("Could not fetch reporting manager data for: {}", reporterEmpId);
                return null;
            }
            reporterData = reporterDataResponse.getData().get(0);
        }
        
        // Process the reporting manager first
        if (processEmployeeInTransaction(reporterData)) {
            // Now try to get the manager again
            return userService.getEmployee(reporterEmpId);
        }
        
        return null;
    }

    /**
     * Optimized employee detail update with caching and proper transaction handling
     */
    private boolean updateEmployeeDetailOptimized(EmployeeData employeeData, Map<String, Integer> departmentDesignationIdMap, Employee reportingManager) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        
        if (reportingManager == null)
            return false;
            
        try {
            // Get company from cache
            if (companyCache == null) {
                companyCache = masterDataCache.getCompany(AppConstants.SUNSHINE_COMPANY_ID);
            }
            
            Integer deptId = departmentDesignationIdMap.get("departmentId");
            Integer designationId = departmentDesignationIdMap.get("designationId");
            
            // Get department data from cache
            com.stpl.tech.master.data.model.Department department1 = departmentDataCache.computeIfAbsent(deptId, 
                id -> userService.getDepartment(id));
            
            if (department1 == null) {
                log.warn("Department not found for ID: {}", deptId);
                return false;
            }
            
            // Get designation data from cache
            com.stpl.tech.master.data.model.Designation designation1 = designationDataCache.computeIfAbsent(designationId, 
                id -> userService.getDesignation(id));
            
            if (designation1 == null) {
                log.warn("Designation not found for ID: {}", designationId);
                return false;
            }
            
            // Get department designations from cache
            Set<com.stpl.tech.master.data.model.Designation> designations = departmentDesignationsCache.computeIfAbsent(deptId, 
                id -> {
                    Department departmentData = masterDataCache.getDepartment(id);
                    Set<com.stpl.tech.master.data.model.Designation> deptDesignations = new HashSet<>();
                    if (departmentData != null) {
                        for (Designation designation : departmentData.getDesignations()) {
                            com.stpl.tech.master.data.model.Designation desig = userService.getDesignation(designation.getId());
                            if (Objects.nonNull(desig)) {
                                deptDesignations.add(desig);
                            }
                        }
                    }
                    return deptDesignations;
                });
            
            department1.setDesignations(designations);
            
            // Parse location code
            Integer locCode;
            try {
                locCode = StringUtils.isEmpty(employeeData.getLocation()) ? 0 : Integer.parseInt(employeeData.getLocation());
            } catch (NumberFormatException e) {
                locCode = 0;
            }
            
            // Create employee object
            Department departmentData = masterDataCache.getDepartment(deptId);
            Designation designation = MasterDataConverter.convert(designation1);
            Employee employee = MasterDataConverter.convert(employeeData, departmentData, designation, reportingManager, locCode);
            employee.setCompany(companyCache);
            
            // Update employee data using userService directly (since we're in a single transaction)
            if (userService.updateEmpData(employee, department1, designation1)) {
                log.info("&&&&&&&&&&  - ,DATA UPDATED ----------, {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
                return true;
            }
        } catch (Exception e) {
            log.error("Exception while updating employee data: {}", e.getMessage(), e);
        }
        
        log.info("&&&&&&&&&&  - ,DATA UPDATE FAILED ----------, {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return false;
    }



    private Employee getReportingManager(EmployeeData employeeData) {
        if (Objects.nonNull(employeeData.getReportingManagerEmpId())) {
            Employee reportingManager = userService.getEmployee(employeeData.getReportingManagerEmpId());
            return reportingManager;
        }
        return null;
    }

    private boolean updateEmployeeDetail(EmployeeData employeeData, Map<String, Integer> departmentDesignationIdMap, Employee reportingManager) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        
        if (reportingManager == null)
            return false;
            
        try {
            Company company = masterDataCache.getCompany(AppConstants.SUNSHINE_COMPANY_ID);
            Integer deptId = departmentDesignationIdMap.get("departmentId");
            Department departmentData = masterDataCache.getDepartment(deptId);
            com.stpl.tech.master.data.model.Department department1 = userService.getDepartment(deptId);
            
            Set<com.stpl.tech.master.data.model.Designation> designations = new HashSet<>();
            for (Designation designation : departmentData.getDesignations()) {
                com.stpl.tech.master.data.model.Designation designation1 = userService.getDesignation(designation.getId());
                if (Objects.nonNull(designation1)) {
                    designations.add(designation1);
                }
            }
            department1.setDesignations(designations);
            
            com.stpl.tech.master.data.model.Designation designation1 = userService.getDesignation(departmentDesignationIdMap.get("designationId"));
            Designation designation = MasterDataConverter.convert(designation1);
            //Integer locCode = masterDataCache.getUnitId(employeeData.getLocation().equalsIgnoreCase(HEAD_OFFICE) ? OFFICE_DELHI : employeeData.getLocation());
            Integer locCode;
            try{
                locCode = StringUtils.isEmpty(employeeData.getLocation()) ? 0 : Integer.parseInt(employeeData.getLocation());
            }catch (NumberFormatException e){
                 locCode = 0;
            }
            Employee employee = MasterDataConverter.convert(employeeData, departmentData, designation, reportingManager, locCode);
            employee.setCompany(company);
            
            if (userService.updateEmpData(employee, department1, designation1)) {
                log.info("&&&&&&&&&&  - ,DATA UPDATED ----------, {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
                return true;
            }
        } catch (Exception e) {
            log.error("Exception while getting data of employee from kettle ::", e);
        }
        
        log.info("&&&&&&&&&&  - ,DATA UPDATED ----------, {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return false;
    }

    // Keep existing methods for backward compatibility
    private EmployeeResponse sendRequestForAllEmployeeDetailData(int page, String lastActivityDate) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        EmployeeResponse employeeResponse = new EmployeeResponse();
        employeeResponse.setStartIndex(1);
        employeeResponse.setEndIndex(2);
        List<EmployeeData> data = new ArrayList<>();
        Map<String, String> params = new HashMap<>();
        params.put("page", page + "");
        params.put("limit", PAGE_LIMIT);
        String endPoint = HTTPS_CHAAYOS_LUCIDTECH_COM_API_V_1 + "active-employees-realtime?api_key=" + masterProperties.getLucidApiKey();
        try {
            List<?> rawData = WebServiceHelper.getRequestWithParamAndHeader(endPoint, masterProperties.getLucidApiKey(), params, List.class, false);

            if (rawData != null && !rawData.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                for (Object rawItem : rawData) {
                    try {
                        EmployeeData employeeData = objectMapper.convertValue(rawItem, EmployeeData.class);
                        data.add(employeeData);
                    } catch (Exception e) {
                        log.error("Error converting raw data to EmployeeData: {}", e.getMessage(), e);
                    }
                }
            }

            employeeResponse.setData(data);
            employeeResponse.setTotalCount(data.size());
            log.info("Total Count of employees ::: " + employeeResponse.getTotalCount());

        } catch (IOException | URISyntaxException e) {
            log.error("Error in fetching employee data from lucid", e);
        }
        log.info("&&&&&&&&&&  - ,LUCID API CALLED----------, {}ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        return employeeResponse;
    }

    public Map<String, Set<String>> lookUpDesignationDepartment() {
        EmployeeResponse employeeResponse = null;
        int page = 1;
        Map<String, Set<String>> map = new HashMap<>();
        Set<String> departmentsToAdd = new HashSet<>();
        Set<String> designationsToAdd = new HashSet<>();
        String lastActivityDate = userService.getLastActivityTime("LUCID", "KETTLE");
        if (lastActivityDate == null) {
            lastActivityDate = LAST_ACTIVITY_DATE;
        }
        do {
            employeeResponse = sendRequestForAllEmployeeDetailData(page, lastActivityDate);
            for (EmployeeData employeeData : employeeResponse.getData()) {
                Map<String, Integer> departmentDesignationIdMap = lookUpDepartmentDesignation(employeeData.getDepartment(), employeeData.getDesignation());
                if (departmentDesignationIdMap.get("departmentId") == 0)
                    departmentsToAdd.add(employeeData.getDepartment());
                if (departmentDesignationIdMap.get("designationId") == 0)
                    designationsToAdd.add(employeeData.getDesignation());
            }
            page++;
        } while (employeeResponse != null && employeeResponse.getData().size() > 0);
        map.put("departments", departmentsToAdd);
        map.put("designations", designationsToAdd);
        return map;
    }

    public Map<String, Set<String>> getDesignationDepartmentMapping() {
        EmployeeResponse employeeResponse = null;
        int page = 1;
        Map<String, Set<String>> map = new HashMap<>();
        String lastActivityDate = userService.getLastActivityTime("LUCID", "KETTLE");
        if (lastActivityDate == null) {
            lastActivityDate = LAST_ACTIVITY_DATE;
        }
        do {
            employeeResponse = sendRequestForAllEmployeeDetailData(page, lastActivityDate);
            for (EmployeeData employeeData : employeeResponse.getData()) {
                Set<String> designations;
                if (map.containsKey(employeeData.getDepartment())) {
                    designations = map.get(employeeData.getDepartment());
                } else {
                    designations = new HashSet<>();
                }
                designations.add(employeeData.getDesignation());
                map.put(employeeData.getDepartment(), designations);
            }
            page++;
        } while (employeeResponse != null && employeeResponse.getData().size() > 0);
        return map;
    }

    @Override
    public UnitEmpData getUnitEmployee(Integer unitId) throws DataNotFoundException {
        List<Employee> emps = userService.getEmployees(unitId);
        UnitEmpData unitEmpData = new UnitEmpData();
        unitEmpData.setUnitId(unitId);
        ArrayList<String> empData = new ArrayList<>();
        for (Employee e : emps) {
            empData.add(e.getName() + "-" + e.getId());
        }
        unitEmpData.setEmpNameAndId(empData);
        return unitEmpData;
    }

    @Override
    public Integer syncInactiveEmployeeData() {
        return syncEmployeeDataInternal(null, true);
    }
}
