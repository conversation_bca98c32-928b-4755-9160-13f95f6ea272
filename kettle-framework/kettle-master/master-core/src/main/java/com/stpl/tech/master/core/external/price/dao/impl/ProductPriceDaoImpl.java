package com.stpl.tech.master.core.external.price.dao.impl;

import com.stpl.tech.master.core.NamedQueryDefinition;
import com.stpl.tech.master.core.external.price.dao.ProductPriceDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.UnitProductPricingBulkUpdateEvent;
import com.stpl.tech.master.data.model.UnitProductPricingBulkUpdateLog;
import com.stpl.tech.master.domain.model.UnitProductPriceSheetDetail;
import com.stpl.tech.master.domain.model.UnitProductPricingDetail;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Repository
public class ProductPriceDaoImpl extends AbstractMasterDaoImpl implements ProductPriceDao {

    private static final Logger LOG = LoggerFactory.getLogger(ProductPriceDaoImpl.class);

    @Override
    public List<UnitProductPriceSheetDetail> getUnitProductPriceDetail(String unitCategory, Integer brandId, List<String> regions, List<String> pricingProfile, List<String> productIds) {
        StringBuilder nativeQuery = new StringBuilder(NamedQueryDefinition.UNIT_PRODUCT_PRICE_DATA.getQuery());
        if(!CollectionUtils.isEmpty(regions)) {
            nativeQuery.append("AND ud.UNIT_REGION IN (:regions)");
        }
        if(!CollectionUtils.isEmpty(productIds)) {
            nativeQuery.append("AND pd.PRODUCT_ID in(:productIds)");
        }
        if(!CollectionUtils.isEmpty(pricingProfile)) {
            nativeQuery.append("AND ud.PRICING_PROFILE IN :pricingProfile AND rlup.RL_ID IN :pricingProfile");
        }
        nativeQuery.append("ORDER BY ud.UNIT_ID ASC, pd.PRODUCT_ID ASC,upp.DIMENSION_CODE ASC") ;
        Query nq = manager.createNativeQuery(nativeQuery.toString(), "UnitProductPriceSheetDetail");
        nq.setParameter("unitCategory", unitCategory);
        nq.setParameter("brandId", brandId);
        if(!CollectionUtils.isEmpty(regions)) {
            nq.setParameter("regions",regions);
        }
        if(!CollectionUtils.isEmpty(productIds)) {
            nq.setParameter("productIds",productIds);
        }
        if(!CollectionUtils.isEmpty(pricingProfile)) {
            nq.setParameter("pricingProfile", pricingProfile);
        }
        List<UnitProductPriceSheetDetail> resultList = nq.getResultList();
        LOG.info("Result count for pricing profile : {}", resultList.size());
        return resultList;
    }

    @Override
    public List<UnitProductPricingDetail> getUnitProductPriceByMappingIds(List<Integer> unitProductPriceIds) {
        Query query = manager.createQuery("SELECT upp.unitProdPriceId as unitProdPriceId, upp.price as price FROM UnitProductPricing upp " +
                "WHERE upp.unitProdPriceId IN :unitProductPriceIds");
        query.setParameter("unitProductPriceIds", unitProductPriceIds);
        List<Object[]> resultList = query.getResultList();
        List<UnitProductPricingDetail> pricingList = new ArrayList<>();
        for (Object[] productPricing : resultList) {
            pricingList.add(new UnitProductPricingDetail((Integer) productPricing[0], (BigDecimal) productPricing[1]));
        }
        return pricingList;
    }

    @Override
    public void bulkUpdateUnitProductPrice(BigDecimal price, List<Integer> productPriceIds, Integer updatedBy) {
        Query query = manager.createQuery("UPDATE UnitProductPricing upp SET upp.price = :price, upp.updatedBy = :updatedBy, upp.lastUpdateTmstmp = :lastUpdateTmstmp " +
                "WHERE upp.unitProdPriceId IN :productPriceIds");
        query.setParameter("price", price);
        query.setParameter("updatedBy", updatedBy);
        query.setParameter("lastUpdateTmstmp", AppUtils.getCurrentTimestamp());
        query.setParameter("productPriceIds", productPriceIds);
        query.executeUpdate();
        manager.flush();
    }

    @Override
    public void addBulkUpdateUnitProductPriceLog(BigDecimal price, List<UnitProductPricingDetail> productPricingList,
                                                 UnitProductPricingBulkUpdateEvent updateEvent) {
        int batchSize = 100;
        for (int i = 0; i < productPricingList.size(); i++) {
            if (i > 0 && i % batchSize == 0) {
                manager.flush();
            }
            UnitProductPricingDetail pricing = productPricingList.get(i);
            UnitProductPricingBulkUpdateLog updateLog = new UnitProductPricingBulkUpdateLog();
            updateLog.setEventId(updateEvent);
            updateLog.setUnitProdPriceId(pricing.getUnitProdPriceId());
            updateLog.setOldPrice(pricing.getPrice());
            updateLog.setNewPrice(price);
            manager.persist(updateLog);
        }
    }
}
