package com.stpl.tech.master.data.dao;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.recipe.monk.model.MonkRecipeDetail;

@Repository
public interface MonkRecipeDao extends MongoRepository<MonkRecipeDetail, String> {

	List<MonkRecipeDetail> findByName(String name);

	List<MonkRecipeDetail> findByStatus(String status);

	@Query("{'product.productId' : ?0, 'dimension.infoId' : ?1}")
	List<MonkRecipeDetail> findByProductIdAndDimension(int productId, int dimensionId);

	@Query("{'product.productId' : ?0}")
	List<MonkRecipeDetail> findByProductId(int productId);

	List<MonkRecipeDetail> findByRecipeId(int id);
	
	@Query("{'name' : { $regex : ?0 ,  '$options' : 'i' }}")
	List<MonkRecipeDetail> findRecipesContainingName(String name);
}
