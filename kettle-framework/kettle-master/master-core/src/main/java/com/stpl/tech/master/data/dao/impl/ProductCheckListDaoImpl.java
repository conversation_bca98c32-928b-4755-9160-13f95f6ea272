package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.ProductCheckListDao;
import com.stpl.tech.master.data.model.ProductCheckListEvent;
import com.stpl.tech.master.data.model.ProductDetail;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Repository
public class ProductCheckListDaoImpl extends AbstractMasterDaoImpl implements ProductCheckListDao {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCheckListDaoImpl.class);

    @Override
    public String getProductStatus(Product product) {
        return manager.find(ProductDetail.class, product.getId()).getProductStatus();
    }

    @Override
    public List<UnitProductMapping> getUnitProductMapping(Product product, List<Integer> unitIds) {
        Query query = manager.createQuery("SELECT upm FROM UnitProductMapping upm " +
                "WHERE upm.productDetail.productId = :productId AND upm.unitDetail.unitId IN :unitIds");
        query.setParameter("productId", product.getId());
        query.setParameter("unitIds", unitIds);
        return query.getResultList();
    }

    @Override
    public List<Integer> getMenuProductMappings(Integer productId, List<Integer> menuSequenceIds) {
        Query query = manager.createNativeQuery("select MS.MENU_SEQUENCE_ID from MENU_SEQUENCE_DATA\n" +
                "MS INNER JOIN MENU_SEQUENCE_MAPPING_DATA MP \n" +
                "ON MS.MENU_SEQUENCE_ID = MP.MENU_SEQUENCE_ID\n" +
                "INNER JOIN PRODUCT_GROUP_DATA PG ON PG.PRODUCT_GROUP_ID = MP.PRODUCT_GROUP_ID AND \n" +
                "MP.PRODUCT_GROUP_PARENT_ID IS NOT NULL AND PG.GROUP_TYPE = \"SUB_CATEGORY\" \n" +
                "INNER JOIN PRODUCT_SEQUENCE PS ON PS.PRODUCT_GROUP_ID = PG.PRODUCT_GROUP_ID\n" +
                "WHERE MS.MENU_SEQUENCE_ID IN :menuSequenceIds AND PS.PRODUCT_ID = :productId AND MP.STATUS = \"ACTIVE\" AND PS.STATUS = \"ACTIVE\" AND PG.STATUS = \"ACTIVE\"; ");
        query.setParameter("productId", productId);
        query.setParameter("menuSequenceIds", menuSequenceIds);
        return query.getResultList();
    }

    @Override
    public String getUnitProductPricing(Integer unitProdRefId, Integer dimensionCode) {
        String rlCode = dimensionCode.toString();
        Query query = manager.createQuery("SELECT status FROM UnitProductPricing upp " +
                "WHERE upp.unitProductMapping.unitProdRefId = :unitProdRefId AND upp.refLookup.rlCode = :rlCode");
        query.setParameter("unitProdRefId", unitProdRefId);
        query.setParameter("rlCode", rlCode);
        return (String) query.getResultList().get(0);
    }

    @Override
    public List<ProductImageMapping> getProductImageMapping(Integer productId) {
        Query query = manager.createQuery("SELECT pim FROM ProductImageMapping pim " +
                "WHERE pim.productId = :productId AND pim.status = :status");
        query.setParameter("productId", productId);
        query.setParameter("status", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public ProductCheckListEvent getProductCheckListEventById(Integer eventId) {
        Query query = manager.createQuery("SELECT e FROM ProductCheckListEvent e " +
                "WHERE e.eventId = :eventId");
        query.setParameter("eventId", eventId);
        try {
            return (ProductCheckListEvent) query.getSingleResult();
        } catch (Exception e) {
            LOG.info("Error while getting product checklist event for id {}", eventId);
        }
        return null;
    }

}
