/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.config;

import com.hazelcast.client.config.ClientConnectionStrategyConfig;
import com.hazelcast.client.config.ConnectionRetryConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.client.config.ClientConfig;
import com.hazelcast.config.NearCacheConfig;
import com.hazelcast.core.HazelcastInstance;

@Configuration
@ConditionalOnProperty(value = "is.client.node", havingValue = "true", matchIfMissing = false)
@ComponentScan({ "com.stpl.tech.master.core.external.interceptor", "com.stpl.tech.master.core.external.acl.service",
		"com.stpl.tech.master.core.external.cache" })
public class MasterCacheClientConfig {

	@Value("${client.node.ip.details}")
	private String serverNodeIps;

	public MasterCacheClientConfig() {
		super();
	}

	@Bean(name = "MasterHazelCastClientSessionConfig")
	public ClientConfig clientConfig() {
		ClientConfig clientConfig = new ClientConfig();
//		clientConfig.getNetworkConfig().setSmartRouting(true);
		clientConfig.setClusterName("MasterHazelCastCacheCluster");
		NearCacheConfig nearCacheConfig = new NearCacheConfig("NearHazelCastCache");
		nearCacheConfig.setMaxIdleSeconds(60);
		nearCacheConfig.setInvalidateOnChange(true);
		clientConfig.addNearCacheConfig(nearCacheConfig);
		clientConfig.setInstanceName("MasterDataCache");
		String ips[] = serverNodeIps.split(",");
		clientConfig.getNetworkConfig().addAddress(ips);
		ClientConnectionStrategyConfig clientConnectionStrategyConfig = new ClientConnectionStrategyConfig();
		ConnectionRetryConfig connectionRetryConfig = new ConnectionRetryConfig();
		connectionRetryConfig.setClusterConnectTimeoutMillis(-1);
		clientConnectionStrategyConfig.setConnectionRetryConfig(connectionRetryConfig);
		clientConfig.setConnectionStrategyConfig(clientConnectionStrategyConfig);
		return clientConfig;
	}

	@Bean(name = "MasterHazelCastInstance")
	public HazelcastInstance hazelcastInstance() {
			return HazelcastClient.newHazelcastClient(clientConfig());
	}

}
