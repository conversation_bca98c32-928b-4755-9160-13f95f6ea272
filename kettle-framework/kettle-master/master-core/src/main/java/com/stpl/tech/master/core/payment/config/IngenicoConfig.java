package com.stpl.tech.master.core.payment.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Repository;

/**
 * Created by shikhar on 15/7/19.
 */

@Configuration
@PropertySource({ "classpath:props/ingenico-${env.type}.properties" })
public class IngenicoConfig {

    @Autowired
    private Environment env;

    public IngenicoConfig() {
        super();
    }

    public String getMerchantId() {
        return env.getProperty("ingenico.mid");
    }

    public String getSALT(){
        return env.getProperty("ingenico.SALT");
    }

    public String getHashingAlgorithm(){
        return env.getProperty("ingenixo.hash.type");
    }

    public String getPaymentMode(){
        return env.getProperty("ingenico.payment.mode");
    }

    public String getCurrency(){
        return env.getProperty("ingenico.currency");
    }

    public String getCallbackUrl(){
        return env.getProperty("ingenico.callback.url");
    }

    public String getIV() {
        return env.getProperty("ingenico.encryption.iv");
    }

    public String getIngenicoUrl() {
        return env.getProperty("ingenico.gateway.url");
    }

    public String getIngenicoRefundUrl() {
        return env.getProperty("ingenico.refund.url");
    }

    public String getIngenicoApiLinkUrl() {
        return env.getProperty("ingenico.apilink.url");
    }

    public String getIdentifier() {
        return env.getProperty("ingenico.identifier");
    }

    public Integer getBharatqr() {
        return Integer.parseInt(env.getProperty("ingenico.bharatQr"));
    }

    public Integer getUpiQr() {
        return Integer.parseInt(env.getProperty("ingenico.upiQr"));
    }
}
