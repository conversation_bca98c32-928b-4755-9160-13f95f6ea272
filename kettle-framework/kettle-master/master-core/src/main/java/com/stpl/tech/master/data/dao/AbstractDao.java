/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.core.service.batchProcess.BatchProcessQueryGenerator;
import com.stpl.tech.master.core.service.batchProcess.BatchProcessor;
import com.stpl.tech.spring.service.util.SpringBeanProvider;
import lombok.SneakyThrows;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import java.util.EnumMap;
import java.util.List;
import java.util.Set;

public interface AbstractDao {

    public <T> T update(T data);

    public <T> T add(T data);
    
    public <T> List<T> addAll(List<T> list);

    public <T> List<T> findAll(Class<T> data);

    public <T, R> T find(Class<T> data, R key);

    public <T> void delete(T data);

    default <T> void batchUpdate(List<T> entries) {
        batchUpdate(entries, null, null);
    }
    default <T> void batchUpdate(List<T> entries, Integer batchSize) {
        batchUpdate(entries, null, batchSize);
    }
    default <T> void batchUpdate(List<T> entries, EnumMap<BatchProcessQueryGenerator.Condition, Set<String>> columnsToUpdate) {
        batchUpdate(entries, columnsToUpdate, null);
    }

    default <T> void batchUpdate(List<T> entries, EnumMap<BatchProcessQueryGenerator.Condition, Set<String>> columnsToUpdate, Integer batchSize) {
        BatchProcessor batchProcessor = SpringBeanProvider.getBean(BatchProcessor.class);
        batchProcessor.batchUpdate(entries, columnsToUpdate, batchSize);
    }

    default <T> void batchInsert(List<T> entries) {
        batchInsert(entries, null);
    }

    default <T> void batchInsert(List<T> entries, Integer batchSize) {
        BatchProcessor batchProcessor = SpringBeanProvider.getBean(BatchProcessor.class);
        batchProcessor.batchInsert(entries, batchSize);
    }

}
