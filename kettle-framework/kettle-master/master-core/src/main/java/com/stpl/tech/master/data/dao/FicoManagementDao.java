package com.stpl.tech.master.data.dao;

import java.util.List;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.FicoDetailData;
import com.stpl.tech.master.domain.model.FicoDetail;

public interface FicoManagementDao {

	public List<FicoDetailData> getFicoList();

	public boolean addFicoDetails(FicoDetail ficoDetail) throws DataUpdationException;

	public boolean updateFicoDetails(FicoDetail ficoDetail);

}
