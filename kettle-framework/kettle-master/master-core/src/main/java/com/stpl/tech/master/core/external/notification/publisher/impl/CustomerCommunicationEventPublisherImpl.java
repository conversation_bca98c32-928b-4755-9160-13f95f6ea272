package com.stpl.tech.master.core.external.notification.publisher.impl;

import com.google.gson.Gson;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.jms.JMSException;
import java.util.Objects;

@Service
public class CustomerCommunicationEventPublisherImpl implements CustomerCommunicationEventPublisher {

    private static final Logger LOG = LoggerFactory.getLogger(CustomerCommunicationEventPublisherImpl.class);

    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Override
    public void publishCustomerCommunicationEvent(String env, NotificationPayload event) throws JMSException {
        try {
            if(Objects.nonNull(event)){
                if(AppConstants.WA_OPT_IN.equals(event.getMessageType()) || "WELCOME_MESSAGE".equals(event.getMessageType())){
                    sqsNotificationService.publishToSQS(env, new Gson().toJson(event), "_WHATSAPP_SIGNUP");
                    LOG.info("COMMUNICATION MESSAGE STATUS PUBLISHED SUCCESSFULLY :::: {} ", new Gson().toJson(event));
                }
            }
        }catch (Exception e){
            LOG.error("COMMUNICATION MESSAGE STATUS FAILED :::: {} ", new Gson().toJson(event));
        }
    }
}
