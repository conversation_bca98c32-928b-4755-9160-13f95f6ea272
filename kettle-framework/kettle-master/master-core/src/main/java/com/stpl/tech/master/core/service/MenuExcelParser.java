package com.stpl.tech.master.core.service;

import com.stpl.tech.master.data.dao.MasterMetadataDao;
import com.stpl.tech.master.domain.model.menu.excel.MenuExcelData;
import com.stpl.tech.spring.service.FileArchiveService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class MenuExcelParser {

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private MasterMetadataDao masterMetadataDao;


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, List<MenuExcelData>>          parseExcel(File file,Integer uploadedBy, List<String> selectedSheets) {
        Map<String, List<MenuExcelData>> sheetData = new HashMap<>();
        try {
            FileInputStream fis = new FileInputStream(file);
            Workbook workbook = new XSSFWorkbook(fis);

            sheetData = parseAllSheets(workbook);

            workbook.close();
            fis.close();

            // Example: Print data from each sheet
            for (Map.Entry<String, List<MenuExcelData>> entry : sheetData.entrySet()) {
                //System.out.println("Sheet: " + entry.getKey());
                for (MenuExcelData dto : entry.getValue()) {
                    System.out.println(dto);
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return sheetData;
    }

    private static Map<String, List<MenuExcelData>> parseAllSheets(Workbook workbook) {
        Map<String, List<MenuExcelData>> sheetData = new HashMap<>();

        for (Sheet sheet : workbook) {
            List<MenuExcelData> dtos = new ArrayList<>();

            Iterator<Row> rowIterator = sheet.iterator();
            if (!rowIterator.hasNext()) continue;

            // Capture headers
            Row headerRow = rowIterator.next();
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(getCellValue(cell));
            }

            // Capture data rows
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                MenuExcelData dto = new MenuExcelData();
                Boolean nonNullCell = false;
                for (int i = 0; i < headers.size(); i++) {
                    Cell cell = row.getCell(i);
                    String cellValue  = getCellValue(cell);
                    if(Objects.nonNull(cellValue)){
                        nonNullCell = true;
                    }
                    if(headers.get(i).equalsIgnoreCase(cellValue)){
                        continue;
                    }
                    dto.setField(headers.get(i), getCellValue(cell));
                }
                if(Boolean.TRUE.equals(nonNullCell)){
                    dtos.add(dto);
                }
            }

            sheetData.put(sheet.getSheetName(), dtos);
        }

        return sheetData;
    }

    private static String getCellValue(Cell cell) {
        if (cell == null) return null;
        switch (cell.getCellType()) {
            case STRING:
                return Objects.nonNull(cell.getStringCellValue()) ?  (StringUtils.isEmpty(cell.getStringCellValue()) ? null :cell.getStringCellValue() )
                        : cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }
}
