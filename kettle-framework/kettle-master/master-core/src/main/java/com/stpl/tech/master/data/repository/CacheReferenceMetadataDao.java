package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.CacheReferenceMetadata;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CacheReferenceMetadataDao extends JpaRepository<CacheReferenceMetadata,Integer> {

    List<CacheReferenceMetadata> findByCacheStatus(String status);

    Optional<CacheReferenceMetadata> findByReferenceType(String referenceType);
}
