/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * RefLookup generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "REF_LOOKUP", uniqueConstraints = @UniqueConstraint(columnNames = { "RTL_ID", "RL_CODE" }))
public class RefLookup implements java.io.Serializable {

	private Integer rlId;
	private RefLookupType refLookupType;
	private String rlCode;
	private String rlShortCode;
	private String rlName;
	private String rlStatus;

	public RefLookup() {
	}

	public RefLookup(RefLookupType refLookupType, String rlCode, String rlName, String rlStatus) {
		this.refLookupType = refLookupType;
		this.rlCode = rlCode;
		this.rlName = rlName;
		this.rlStatus = rlStatus;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "RL_ID", unique = true, nullable = false)
	public Integer getRlId() {
		return this.rlId;
	}

	public void setRlId(Integer rlId) {
		this.rlId = rlId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "RTL_ID", nullable = false)
	public RefLookupType getRefLookupType() {
		return this.refLookupType;
	}

	public void setRefLookupType(RefLookupType refLookupType) {
		this.refLookupType = refLookupType;
	}

	@Column(name = "RL_CODE", nullable = false, length = 20)
	public String getRlCode() {
		return this.rlCode;
	}

	public void setRlCode(String rlCode) {
		this.rlCode = rlCode;
	}

	@Column(name = "RL_NAME", nullable = false, length = 30)
	public String getRlName() {
		return this.rlName;
	}

	public void setRlName(String rlName) {
		this.rlName = rlName;
	}

	@Column(name = "RL_SHORT_CODE", nullable = true, length = 3)
	public String getRlShortCode() {
		return this.rlShortCode;
	}

	public void setRlShortCode(String rlShortCode) {
		this.rlShortCode = rlShortCode;
	}

	@Column(name = "RL_STATUS", nullable = false, length = 10)
	public String getRlStatus() {
		return this.rlStatus;
	}

	public void setRlStatus(String rlStatus) {
		this.rlStatus = rlStatus;
	}

}
