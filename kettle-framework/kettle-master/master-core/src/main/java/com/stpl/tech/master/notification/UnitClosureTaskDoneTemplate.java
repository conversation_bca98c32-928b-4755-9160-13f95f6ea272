package com.stpl.tech.master.notification;

import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class UnitClosureTaskDoneTemplate extends AbstractTemplate {

    private final Map<String, Object> data = new HashMap<String, Object>();
    private UnitClosureEvent unitClosureEvent;
    private String basePath;

    public UnitClosureTaskDoneTemplate(UnitClosureEvent unitClosureEvent, String basePath){
        this.unitClosureEvent = unitClosureEvent;
        this.basePath = basePath;
    }
    @Override
    public String getTemplatePath() {
        return "template/UnitClosureTaskDoneTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/UnitClosureNotification/"+"Template_"+new Date().getTime()+".html";
    }

    @Override
    public Map<String, Object> getData() {
        data.put("unitClosureEvent", unitClosureEvent);
        return data;
    }
}
