/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.Map;

public class GenericNotificationTemplate extends AbstractTemplate {

	private final GenericNotification mailData;
	private final Map<String, Object> data = new HashMap<String, Object>();

	public GenericNotificationTemplate(GenericNotification mailData) {
		super();
		this.mailData = mailData;
	}

	@Override
	public String getTemplatePath() {
		return "template/GenericTemplate.html";
	}

	@Override
	public String getFilepath() {
		return null;
	}

	@Override
	public Map<String, Object> getData() {
		data.put("reports", mailData.getReportsData());
		return data;
	}

	public GenericNotification getMailData() {
		return mailData;
	}

}
