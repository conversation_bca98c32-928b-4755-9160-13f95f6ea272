package com.stpl.tech.master.notification;

import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractTemplate;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ZomatoBusinessHourChangeEmailTemplate extends AbstractTemplate {

    private List<UnitHours> request;
    private String response;
    private String unitName;
    private Integer brandId;
    private String partnerName;
    private String basePath;
    private String event;

    public ZomatoBusinessHourChangeEmailTemplate(List<UnitHours> request, String response, String unitName, String partnerName, Integer brandId, String basePath,
                                                 String event){
        this.request=request;
        this.response = response;
        this.unitName = unitName;
        this.brandId = brandId;
        this.partnerName = partnerName;
        this.basePath = basePath;
        this.event = event;
    }

    @Override
    public String getTemplatePath() {
        return "template/ZomatoBusinessHourChangeEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "changeBussinessHour" + "/" + unitName + "_" + partnerName + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> diffsMap = new HashMap<>();
        for(UnitHours hours : request){
            String str = "Form : " + AppUtils.convertToHourMinute(hours.getDeliveryOpeningTime()) + " - To : " + AppUtils.convertToHourMinute(hours.getDeliveryClosingTime());
            diffsMap.put(hours.getDayOfTheWeek(),str);
        }
        diffsMap.put("event",event);
        diffsMap.put("response", response.toUpperCase());
        diffsMap.put("unitName", unitName.toUpperCase());
        diffsMap.put("partnerName", partnerName);
        if(brandId==1){
            diffsMap.put("brandName","CHAAYOS");
        }
        if(brandId==3){
            diffsMap.put("brandName","GNT");
        }
        return diffsMap;
    }
}
