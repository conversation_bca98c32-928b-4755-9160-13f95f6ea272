package com.stpl.tech.master.core.external.acl.service.impl;

import com.stpl.tech.master.core.external.acl.service.TokenDao;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.util.AppConstants;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.util.Date;
import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 24-05-2016.
 */
@Service
public class TokenServiceImpl<T extends TokenDao> implements TokenService<T> {

	@Value("${jwt.secret:C4@@y05a^3)H-5uN}")
	private String secret;

	@Value("${jwt.access-token.expiration:3600000}")
	private long accessTokenExpiration;

	/*@Value("${jwt.refresh-token.expiration}")
	private long refreshTokenExpiration;*/

	private final RefreshToken refreshToken;


	public TokenServiceImpl() {
		this.refreshToken = new RefreshToken(AppConstants.PASSPHRASE_KEY, 3600000);
	}

	@Override
	public String createToken(T object, long ttlMillis) {
		return createToken(object, ttlMillis, AppConstants.PASSPHRASE_KEY);
	}

	@Override
	public String createToken(T object, long ttlMillis, String passPhraseKey) {
		SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
		long nowMillis = System.currentTimeMillis();
		Date now = new Date(nowMillis);

		byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(passPhraseKey);
		Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
		Map<String, Object> authClaims = object.createClaims();

		JwtBuilder builder = Jwts.builder()
				.setClaims(authClaims)
				.setIssuedAt(now)
				.signWith(signatureAlgorithm, signingKey);

		if (ttlMillis >= 0) {
			long expMillis = nowMillis + ttlMillis;
			Date exp = new Date(expMillis);
			builder.setExpiration(exp);
		}

		return builder.compact();
	}

	@Override
	public void parseToken(T object, String jwt) {
		parseToken(object, jwt, AppConstants.PASSPHRASE_KEY);
	}

	@Override
	public void parseToken(T object, String jwt, String key) {
		Claims claims = Jwts.parser()
				.setSigningKey(DatatypeConverter.parseBase64Binary(key))
				.parseClaimsJws(jwt)
				.getBody();
		object.parseClaims(claims);
	}

	@Override
	public String createToken(Map<String, Object> claims) {
		return Jwts.builder()
				.setClaims(claims)
				.setExpiration(new Date(System.currentTimeMillis() + accessTokenExpiration))
				.signWith(SignatureAlgorithm.HS512, secret)
				.compact();
	}

	@Override
	public Claims parseToken(String token) {
		return Jwts.parser()
				.setSigningKey(secret)
				.parseClaimsJws(token)
				.getBody();
	}

	@Override
	public String createRefreshToken(String sessionKey, int unitId,int userId, String application,
								   String macAddress, String geoLocation, int terminalId) {
		Map<String, Object> claims = refreshToken.createClaims(
			sessionKey,unitId ,userId, application, macAddress, geoLocation,terminalId);
		return refreshToken.createToken(claims);
	}

	@Override
	public Claims parseRefreshToken(String token) {
		return refreshToken.parseToken(token);
	}

	@Override
	public boolean isTokenExpired(String token) {
		try {
			Claims claims = parseToken(token);
			return claims.getExpiration().before(new Date());
		} catch (ExpiredJwtException e) {
			return true;
		}
	}

	@Override
	public boolean isRefreshTokenExpired(String token) {
		try {
			Claims claims = parseRefreshToken(token);
			return claims.getExpiration().before(new Date());
		} catch (ExpiredJwtException e) {
			return true;
		}
	}
}