package com.stpl.tech.master.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.Getter;
import lombok.Setter;

@ExcelSheet(value = "Unit Product Recipe Profile")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Setter
@Getter
public class UnitProductProfileExcelData {

    @ExcelField(headerName = "UNIT_ID")
    private Integer unitId;

    @ExcelField(headerName = "UNIT_NAME")
    private String unitName;

    @ExcelField(headerName = "PRODUCT_ID")
    private Integer productId;

    @ExcelField(headerName = "PRODUCT_NAME")
    private String productName;

    @ExcelField(headerName = "DIMENSION_ID")
    private Integer dimensionId;

    @ExcelField(headerName = "DIMENSION_CODE")
    private String dimensionCode;

    @ExcelField(headerName = "OLD_PROFILE")
    private String oldRecipeProfile;

    @ExcelField(headerName = "NEW_PROFILE")
    private String newRecipeProfile;

    @ExcelField(headerName = "OLD_STATUS")
    private String oldStatus;

    @ExcelField(headerName = "NEW_STATUS")
    private String newStatus;

    @ExcelField(headerName = "OLD_DELIVERY_ONLY")
    private String oldDeliveryOnly;

    @ExcelField(headerName = "NEW_DELIVERY_ONLY")
    private String newDeliveryOnly;

    @ExcelField(headerName = "OLD_PICK_DINE_IN_CONSUMABLES")
    private String oldPickDineInConsumables;

    @ExcelField(headerName = "NEW_PICK_DINE_IN_CONSUMABLES")
    private String newPickDineInConsumables;

    @ExcelField(headerName = "OLD_PRODUCT_ALIAS")
    private String oldProductAlias;

    @ExcelField(headerName = "NEW_PRODUCT_ALIAS")
    private String newProductAlias;

    @ExcelField(headerName = "OLD_DIMENSION_DESC")
    private String oldDimensionDesc;

    @ExcelField(headerName = "NEW_DIMENSION_DESC")
    private String newDimensionDesc;

}
