package com.stpl.tech.master.core.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.service.IdGeneratorService;
import com.stpl.tech.master.data.dao.IdGeneratorDao;
import com.stpl.tech.master.recipe.model.IdGenerator;

@Service
public class IdGeneratorServiceImpl implements IdGeneratorService {

	@Autowired
	private IdGeneratorDao dao;

	@Override
	public <T> int getNextId(Class<T> context) {
		IdGenerator data = null;
		List<IdGenerator> list = dao.findByName(context.getName());
		if (list == null || list.size() == 0) {
			IdGenerator idGenerator = new IdGenerator();
			idGenerator.setName(context.getName());
			idGenerator.setNextId(1);
			data = dao.save(idGenerator);
		} else {
			data = list.get(0);
		}
		int id = data.getNextId();
		data.setNextId(data.getNextId() + 1);
		dao.save(data);
		return id;
	}

}
