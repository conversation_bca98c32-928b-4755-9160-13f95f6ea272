/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EmployeeActivationReceipt extends AbstractTemplate {

	private final String action;
	private final Employee employee;
	private final List<String> mappedUnits;
	private final String basePath;
	private final String password;
	private final Map<String, Object> data = new HashMap<String, Object>();

	public EmployeeActivationReceipt(String action, Employee employee, List<String> mappedUnits, String basePath,
			String password) {
		super();
		this.action = action;
		this.employee = employee;
		this.mappedUnits = mappedUnits;
		this.basePath = basePath;
		this.password = password;
	}

	public String getTemplatePath() {
		return "template/EmployeeActivation.html";
	}

	public Map<String, Object> getData() {
		// Build the data-model
		data.put("action", action);
		data.put("employee", employee);
		data.put("mappedUnits", mappedUnits);
		data.put("password", password);
		data.put("currentAddress", printAddress(employee.getCurrentAddress()));
		data.put("permanentAddress", printAddress(employee.getPermanentAddress()));

		return data;
	}

	@Override
	public String getFilepath() {
		return basePath + "/EmployeeActivation/" + employee.getId() + ".html";
	}

	private String printAddress(Address address) {
		StringBuffer buffer = new StringBuffer("");
		if (address != null) {
			buffer.append(address.getLine1() + ",\n");
			if (address.getLine2() != null) {
				buffer.append(address.getLine2() + ",\n");
			}
			if (address.getLine3() != null) {
				buffer.append(address.getLine3() + ",\n");
			}
			buffer.append(address.getCity() + ", " + address.getState() + ",\n");
			buffer.append(address.getCountry() + ", " + address.getZipCode() + "\n");
		}
		return buffer.toString();
	}

	public Employee getEmployee() {
		return employee;
	}

	public String getAction() {
		return action;
	}

}
