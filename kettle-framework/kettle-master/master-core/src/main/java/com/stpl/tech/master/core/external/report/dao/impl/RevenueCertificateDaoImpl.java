package com.stpl.tech.master.core.external.report.dao.impl;

import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.stpl.tech.master.core.AttributeDefinitionEnum;
import com.stpl.tech.master.core.external.report.dao.RevenueCertificateDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.UnitAttributeMapping;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.util.AppConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

@Repository
@Log4j2
public class RevenueCertificateDaoImpl extends AbstractMasterDaoImpl implements RevenueCertificateDao {


    @Override
    public boolean saveEmailId(Integer unitId, String toBeGenerated, String emailId) {
        AtomicReference<Boolean> isupdated = new AtomicReference<>(false);
        AtomicReference<Boolean> isValueUpdated = new AtomicReference<>(false);
        List<UnitAttributeMapping> mappings = getEmailIdByUnitIdAndAttributeIds(unitId,
                List.of(AttributeDefinitionEnum.REVENUE_CERTIFICATE_EMAIL.getId(), AttributeDefinitionEnum.REVENUE_CERTIFICATE_GENERATION_ENABLE.getId()));
        UnitDetail detail = findById(unitId);
        if (Objects.isNull(mappings) || mappings.isEmpty()) {
            boolean valueForRevenue = setValueForRevenue(detail, toBeGenerated);
            boolean valueToBeGenerated = setValueForToBeGenerated(detail, emailId);
            if (valueForRevenue && valueToBeGenerated) {
                return true;
            } else {
                return false;
            }
        } else {
            mappings.forEach(detailMapping -> {
                if (AttributeDefinitionEnum.REVENUE_CERTIFICATE_GENERATION_ENABLE.name().equals(detailMapping.getAttributeCode())) {
                    String isGenerated = null;
                    if (toBeGenerated.equals("YES")) {
                        isGenerated = AppConstants.YES;
                    } else {
                        isGenerated = AppConstants.NO;
                    }
                    detailMapping.setAttributeValue(isGenerated);
                    try {
                        manager.persist(detailMapping);
                        isupdated.set(true);
                    } catch (Exception e) {
                        log.error("unable to update mapping", e);
                        isupdated.set(false);

                    }
                } else if (AttributeDefinitionEnum.REVENUE_CERTIFICATE_EMAIL.name().equals(detailMapping.getAttributeCode())) {
                    if (Objects.nonNull(emailId)) {
                        detailMapping.setAttributeValue(emailId);
                    } else {
                        detailMapping.setAttributeValue("<EMAIL>");
                    }
                    try {
                        manager.persist(detailMapping);
                        isValueUpdated.set(true);
                    } catch (Exception e) {
                        log.error("unable to update mapping", e);
                        isValueUpdated.set(true);
                    }
                }
            });
            if(Boolean.TRUE.equals(isupdated.get())
                    && Boolean.TRUE.equals(isValueUpdated.get())){
                return true;
            }
            else {
                return false;
            }
        }
    }


    private boolean setValueForRevenue(UnitDetail detail, String toBeGenerated){
        try {
            UnitAttributeMapping unitAttributeMapping = new UnitAttributeMapping();

                unitAttributeMapping.setAttributeId(AttributeDefinitionEnum.REVENUE_CERTIFICATE_GENERATION_ENABLE.getId());
                unitAttributeMapping.setUnitDetail(detail);
                unitAttributeMapping.setAttributeCode(String.valueOf(AttributeDefinitionEnum.REVENUE_CERTIFICATE_GENERATION_ENABLE));
                unitAttributeMapping.setAttributeType(AppConstants.STRING);
                unitAttributeMapping.setMappingStatus(AppConstants.ACTIVE);
                if (toBeGenerated.equals("YES")) {
                    toBeGenerated = AppConstants.YES;
                } else {
                    toBeGenerated = AppConstants.NO;
                }
                unitAttributeMapping.setAttributeValue(toBeGenerated);

            manager.persist(unitAttributeMapping);
            manager.flush();
            return true;
        } catch (Exception e) {
            log.error("Unable to set if revenue certificate to be enabled or not for unitId :{}",e);
        }
        return false;
    }

    private boolean setValueForToBeGenerated(UnitDetail detail, String emailId){
        try {
            UnitAttributeMapping unitAttributeMapping = new UnitAttributeMapping();
            unitAttributeMapping.setAttributeId(AttributeDefinitionEnum.REVENUE_CERTIFICATE_EMAIL.getId());
            unitAttributeMapping.setUnitDetail(detail);
            if(Objects.nonNull(emailId)) {
                unitAttributeMapping.setAttributeValue(emailId);
            }else{
                unitAttributeMapping.setAttributeValue("<EMAIL>");
            }
            unitAttributeMapping.setAttributeCode(String.valueOf(AttributeDefinitionEnum.REVENUE_CERTIFICATE_EMAIL));

            unitAttributeMapping.setMappingStatus(AppConstants.ACTIVE);
            unitAttributeMapping.setAttributeType(AppConstants.STRING);
            manager.persist(unitAttributeMapping);
            manager.flush();
            return true;
        } catch (Exception e) {
            log.error("Unable to set emailIds for revenue certificate",e);
        }
        return false;
    }

    @Override
    public List<UnitAttributeMapping> getEmailIdByUnitIdAndAttributeIds(Integer unitId, List<Integer> attributeIds) {
        try {
            UnitDetail detail = findById(unitId);
            Query query = manager.createQuery("FROM UnitAttributeMapping where unitDetail = :unitDetail and attributeId IN ( :attributeId)");
            query.setParameter("unitDetail",detail);
            query.setParameter("attributeId",attributeIds);
            List<UnitAttributeMapping> mappings= query.getResultList();
            if(!CollectionUtils.isEmpty(mappings)){
                return mappings;
            }
            return null;
        }catch (NoResultException e){
            log.error("No result found",e);
            return null;
        }
    }
    public UnitDetail findById(Integer unitId) {
        try {
            UnitDetail detail = manager.find(UnitDetail.class,unitId);
            return detail;
        } catch (Exception e) {
            log.error("Unable to get unitdetail for unitId",e);
        }
        return null;
    }
}
