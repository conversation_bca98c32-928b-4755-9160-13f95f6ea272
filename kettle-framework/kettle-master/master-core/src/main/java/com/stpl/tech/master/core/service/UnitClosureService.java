package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.MasterException;;
import com.stpl.tech.master.domain.model.UnitClosureEventDomain;
import com.stpl.tech.master.core.service.model.UnitClosureFormDataDomain;
import com.stpl.tech.master.domain.model.UnitClosureFormMetaDataDomain;
import com.stpl.tech.master.domain.model.UnitClosureStateEventDomain;
import com.stpl.tech.util.EmailGenerationException;

import java.io.IOException;
import java.util.List;

public interface UnitClosureService {
    boolean initiateUnitClosure(UnitClosureEventDomain unitClosureEventDomain) throws MasterException, EmailGenerationException;

    List<UnitClosureEventDomain> getClosureByStatus(String closureStatus);

    List<UnitClosureStateEventDomain> getClosureByState(Long requestId) throws MasterException;

    boolean closeStateEvent(UnitClosureStateEventDomain unitClosureStateEventDomain) throws MasterException;

    boolean closeEvent(UnitClosureEventDomain unitClosureEventDomain) throws MasterException;

    String initiateOperationClose() throws IOException;

    boolean addUnitClosureFormMetadata(List<UnitClosureFormMetaDataDomain> formMetaData);


    boolean addUnitClosureFormData(List<UnitClosureFormDataDomain> unitClosureFormDataDomains, Long unitClosureEventId, Integer userId, Boolean isSaved) throws MasterException;

    List<UnitClosureFormDataDomain> getUnitClosureFormMetaData(Integer unitId);

    List<UnitClosureStateEventDomain> getStateFromUnitId(Integer unitId) throws MasterException;

    void sendUnitClosureTaskDoneNotification(Long eventId) throws EmailGenerationException;

    void sendPendingTaskNotification(Integer unitId) throws EmailGenerationException;

    Boolean suspendUnitClosureEvent(Integer unitId) throws MasterException;
}
