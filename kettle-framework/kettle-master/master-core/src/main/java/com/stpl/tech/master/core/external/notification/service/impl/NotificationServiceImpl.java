package com.stpl.tech.master.core.external.notification.service.impl;

import com.stpl.tech.kettle.core.service.authorization.OTPMapper;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.external.notification.dao.NotificationDao;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.master.core.external.notification.service.MessagingClient;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.data.model.NotificationLogDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.jms.JMSException;
import java.io.IOException;
import java.util.Objects;

/**
 * Created by Chaayos on 21-09-2016.
 */
@Service
public class NotificationServiceImpl implements NotificationService {

	private static final Logger LOG = LoggerFactory.getLogger(NotificationServiceImpl.class);
	@Autowired
	private NotificationDao notificationDao;

	@Autowired
	private EnvironmentPropertiesCache propertiesCache;

	@Autowired
	private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

	@Autowired
	private Environment env;

	@Override
	@Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean sendNotification(String type, String message, String contact, MessagingClient client,
									boolean sendNotification, NotificationPayload payload) throws IOException, JMSException {
		boolean status = false;
		try {
			if (Objects.nonNull(payload) && payload.isWhatsappOptIn() && sendWhatsappNotification(sendNotification,payload.isWhatsappOptIn(),payload.isSendWhatsapp())) {
				customerCommunicationEventPublisher.publishCustomerCommunicationEvent(EnvType.valueOf(env.getProperty("environment.type")).name(), payload);
				return true;
			}
		} catch (Exception e){
			LOG.error("Exception Faced While Sending Whatsapp Notification  {}",payload.getCustomerId());
		}
		if(propertiesCache.getInternalNos() != null && propertiesCache.getInternalNos().contains(contact)){
			LOG.info(" Message not sent to internal contact No : {}", contact);
			sendNotification = false;
		}

		if (sendNotification) {
			status = client.sendMessage(message, contact);
		} else {
			LOG.info("SMS Skipped, Message : {} \nContact Number : {}", message, contact);
			status = true;
		}
		try {
			createNotification(type, message, contact, client, status);
		} catch (Exception e) {
			LOG.error("Error while logging customer notification", e);
		}
		return status;
	}

	private boolean sendWhatsappNotification(Boolean props, Boolean whatsappOptIn, Boolean sendWhatsapp) {
		return props && (whatsappOptIn && sendWhatsapp);
	}

	private void createNotification(String type, String message, String contact, MessagingClient client,
			boolean sendNotification) {

		NotificationLogDetail notificationLogDetail = new NotificationLogDetail();
		notificationLogDetail.setType(type);
		notificationLogDetail.setNotificationTime(AppUtils.getCurrentTimestamp());
		notificationLogDetail.setMessage(message);
		notificationLogDetail.setContact(contact);
		notificationLogDetail.setNotificationSent(AppUtils.setStatus(sendNotification));
		notificationLogDetail.setServiceClient(client.getClass().getSimpleName());
		notificationDao.add(notificationLogDetail);
	}

	@Override
	public OTPMapper getOTPMapperInstance() {
		return OTPMapper.getInstance();
	}

	@Override
	@Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean sendOTPRequestViaIVR(String type, String contact, SMSWebServiceClient client, boolean sendNotification, NotificationPayload payload, String token, Integer ivrId) {
		boolean status = false ;
		if (Objects.nonNull(propertiesCache.getInternalNos()) && propertiesCache.getInternalNos().contains(contact)){
			LOG.info(" Message not sent to internal contact No : {}", contact);
			sendNotification = false;
		}
		if(sendNotification){
			status = client.sendOTPRequestViaIVR(token,contact,ivrId);
		} else {
			LOG.info("OTP via IVR skipped for contact Number :{}", contact);
			status = true;
		}
		try {
			createNotification(type, token, contact, client, status);
		} catch (Exception e) {
			LOG.error("Error while logging customer otp via ivr notification", e);
		}
		return status;
	}

}
