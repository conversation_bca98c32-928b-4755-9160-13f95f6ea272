package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.UnitIpAddressData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UnitIpAddressDao extends JpaRepository<UnitIpAddressData, Integer> {

    UnitIpAddressData findByUnitIdAndTerminalIdAndAppName(Integer unitId, Integer terminalId, String appName);

}
