package com.stpl.tech.master.core.external.acl.service.impl;

import com.stpl.tech.master.core.external.acl.service.TokenDao;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class RefreshToken implements TokenDao {

    protected String sessionKey;
    protected int unitId;
    protected String issuer;
    protected int userId;
    protected int terminalId;
    protected String macAddress;
    protected String geoLocation;
    private static final String SESSION_KEY = "sessionKey";
    private static final String USER_ID = "userId";
    private static final String UNIT_ID = "unitId";
    private static final String APPLICATION = "application";
    private static final String MAC_ADDRESS = "macAddress";
    private static final String GEO_LOCATION = "geoLocation";
    private static final String EXPIRY = "expiry";
    private static final String TERMINAL = "terminalId";

    private final String secret;
    private final long ttl;

    public RefreshToken(String secret, long ttl) {
        this.secret = secret;
        this.ttl = ttl;
    }

    public RefreshToken(String secret , long ttl ,String sessionKey, int unitId, String issuer, int userId, int terminalId , String macAddress
            , String geoLocation) {
        this.secret = secret;
        this.ttl = ttl;
        this.sessionKey = sessionKey;
        this.unitId = unitId;
        this.issuer = issuer;
        this.userId = userId;
        this.terminalId = terminalId;
        this.macAddress = macAddress;
        this.geoLocation = geoLocation;
    }


    public String createToken(Map<String, Object> claims) {
        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(new Date(System.currentTimeMillis() + ttl))
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }


    public Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret)
                .parseClaimsJws(token)
                .getBody();
    }

    public Map<String, Object> createClaims(String sessionKey, int unitId,int userId, String application,
                                          String macAddress, String geoLocation, int terminalId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(SESSION_KEY, sessionKey);
        claims.put(USER_ID, userId);
        claims.put(UNIT_ID, unitId);
        claims.put(APPLICATION, application);
        claims.put(MAC_ADDRESS, macAddress);
        claims.put(GEO_LOCATION, geoLocation);
        claims.put(TERMINAL,terminalId);
        claims.put(EXPIRY, new Date(System.currentTimeMillis() + ttl));
        return claims;
    }

    public Map<String, Object> createClaims() {
        // Setting JWT Claims
        Map<String, Object> authClaims = new HashMap<String, Object>();
        authClaims.put("sessionKey", this.sessionKey);
        authClaims.put("unitId", this.unitId);
        authClaims.put("issuer", this.issuer);
        authClaims.put("userId", this.unitId);
        authClaims.put("terminalId", this.terminalId);
        authClaims.put("macAddress", this.macAddress);
        authClaims.put("geoLocation", this.geoLocation);
        return authClaims;
    }

    public void parseClaims(Claims claims) {
        this.sessionKey = claims.get("sessionKey", String.class);
        this.unitId = claims.get("unitId", Integer.class);
        this.userId = claims.get("userId", Integer.class);
        this.issuer = claims.get("issuer", String.class);
        this.terminalId = claims.get("terminalId", Integer.class);
        this.macAddress = claims.get("macAddress", String.class);
        this.geoLocation = claims.get("geoLocation", String.class);
    }

    }
