/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@Entity
@Table(name = "BANNER_DETAIL_DATA")
public class BannerDetailData implements java.io.Serializable {

    private static final long serialVersionUID = 8672416293090573948L;

    private Integer bannerId;
    private String bannerType;
    private String bannerTitle;
    private String bannerSubTitle;
    private String bannerDescription;
    private String bannerUrl;
    private String bannerButtonAction;
    private String bannerButtonText;
    private Date bannerActivationDate;
    private Date bannerExpiryDate;
    private String bannerCode;
    private String status;
    private Integer createdBy;
    private Date createdOn;
    private String sectionType;
    private Integer productId;
    private Integer categoryId;

    public BannerDetailData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "BANNER_DETAIL_DATA_ID", unique = true, nullable = false)
    public Integer getBannerId() {
        return bannerId;
    }

    public void setBannerId(Integer bannerId) {
        this.bannerId = bannerId;
    }

    @Column(name = "BANNER_TYPE", nullable = false)
    public String getBannerType() {
        return bannerType;
    }

    public void setBannerType(String bannerType) {
        this.bannerType = bannerType;
    }

    @Column(name = "BANNER_TITLE")
    public String getBannerTitle() {
        return bannerTitle;
    }

    public void setBannerTitle(String bannerTitle) {
        this.bannerTitle = bannerTitle;
    }

    @Column(name = "BANNER_SUB_TITLE")
    public String getBannerSubTitle() {
        return bannerSubTitle;
    }

    public void setBannerSubTitle(String bannerSubTitle) {
        this.bannerSubTitle = bannerSubTitle;
    }

    @Column(name = "BANNER_DESCRIPTION")
    public String getBannerDescription() {
        return bannerDescription;
    }

    public void setBannerDescription(String bannerDescription) {
        this.bannerDescription = bannerDescription;
    }

    @Column(name = "BANNER_IMAGE_URL", nullable = false)
    public String getBannerUrl() {
        return bannerUrl;
    }

    public void setBannerUrl(String bannerUrl) {
        this.bannerUrl = bannerUrl;
    }

    @Column(name = "BANNER_BUTTON_ACTION")
    public String getBannerButtonAction() {
        return bannerButtonAction;
    }

    public void setBannerButtonAction(String bannerButtonAction) {
        this.bannerButtonAction = bannerButtonAction;
    }

    @Column(name = "BANNER_BUTTON_TEXT")
    public String getBannerButtonText() {
        return bannerButtonText;
    }

    public void setBannerButtonText(String bannerButtonText) {
        this.bannerButtonText = bannerButtonText;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "BANNER_ACTIVATION_TIME", nullable = false, length = 19)
    public Date getBannerActivationDate() {
        return bannerActivationDate;
    }

    public void setBannerActivationDate(Date bannerActivationDate) {
        this.bannerActivationDate = bannerActivationDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "BANNER_EXPIRY_TIME", length = 19)
    public Date getBannerExpiryDate() {
        return bannerExpiryDate;
    }

    public void setBannerExpiryDate(Date bannerExpityDate) {
        this.bannerExpiryDate = bannerExpityDate;
    }

    @Column(name = "BANNER_CODE")
    public String getBannerCode() {
        return bannerCode;
    }

    public void setBannerCode(String bannerCode) {
        this.bannerCode = bannerCode;
    }

    @Column(name = "BANNER_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_ON", nullable = false, length = 19)
    public Date getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    @Column(name = "SECTION_TYPE")
    public String getSectionType() {
        return sectionType;
    }

    public void setSectionType(String sectionType) {
        this.sectionType = sectionType;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "CATEGORY_ID")
    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }
}
