package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "PRICE_PROFILE")
public class PriceProfile {

    private Integer priceProfileId;
    private String profileDescription;
    private String profileType;
    private Date profileCreationTime;
    private Date lastUpdateTime;
    private String lastUpdatedBy;
    private String profileStatus;
    private BigDecimal thresholdPercentage;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRICE_PROFILE_ID", unique = true, nullable = false)
    public Integer getPriceProfileId() {
        return priceProfileId;
    }

    public void setPriceProfileId(Integer priceProfileId) {
        this.priceProfileId = priceProfileId;
    }

    @Column(name = "PROFILE_DESCRIPTION", nullable = false)
    public String getProfileDescription() {
        return profileDescription;
    }

    public void setProfileDescription(String profileDescription) {
        this.profileDescription = profileDescription;
    }

    @Column(name = "PROFILE_TYPE", nullable = false)
    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "PROFILE_CREATION_TIME", nullable = false)
    public Date getProfileCreationTime() {
        return profileCreationTime;
    }

    public void setProfileCreationTime(Date profileCreationTime) {
        this.profileCreationTime = profileCreationTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name = "LAST_UPDATE_BY", nullable = false)
    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "PROFILE_STATUS", nullable = false)
    public String getProfileStatus() {
        return profileStatus;
    }

    public void setProfileStatus(String profileStatus) {
        this.profileStatus = profileStatus;
    }

    @Column(name = "THRESHOLD_PERCENTAGE")
    public BigDecimal getThresholdPercentage() {
        return thresholdPercentage;
    }

    public void setThresholdPercentage(BigDecimal thresholdPercentage) {
        this.thresholdPercentage = thresholdPercentage;
    }
}
