/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.util;

import com.stpl.tech.util.AppUtils;
import org.springframework.jdbc.core.ResultSetExtractor;

import java.sql.Blob;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;


public class ResultSetConverterToList implements ResultSetExtractor<List<List<String>>> {

	private List<String> header;

	public List<String> getHeader() {
		return this.header;
	}

	public List<String> getHeader(ResultSetMetaData rsmd) throws SQLException {
		List<String> header = new ArrayList<String>();
		for (int i = 1; i <= rsmd.getColumnCount(); i++) {
			header.add(rsmd.getColumnLabel(i));
		}
		return header;
	}

	@Override
	public List<List<String>> extractData(ResultSet rs) throws SQLException {
		List<List<String>> json = new ArrayList<>();
		ResultSetMetaData rsmd = rs.getMetaData();
		this.header = getHeader(rsmd);
		json.add(header);
		while (rs.next()) {
			int numColumns = rsmd.getColumnCount();
			List<String> obj = new ArrayList<>();
			for (int i = 1; i < numColumns + 1; i++) {
				switch (rsmd.getColumnType(i)) {
				case java.sql.Types.BIGINT:
					obj.add(getValue(rs.getInt(i)));
					break;
				case java.sql.Types.BOOLEAN:
					obj.add(getValue(rs.getBoolean(i)));
					break;
				case java.sql.Types.DOUBLE:
					obj.add(getValue(rs.getDouble(i)));
					break;
				case java.sql.Types.FLOAT:
					obj.add(getValue(rs.getFloat(i)));
					break;
				case java.sql.Types.DECIMAL:
					obj.add(getValue(rs.getBigDecimal(i)));
					break;
				case java.sql.Types.INTEGER:
					obj.add(getValue(rs.getInt(i)));
					break;
				case java.sql.Types.LONGNVARCHAR:
					obj.add(getValue(rs.getNString(i)));
					break;
				case java.sql.Types.LONGVARCHAR:
					obj.add(getValue(rs.getString(i)));
					break;
				case java.sql.Types.NVARCHAR:
					obj.add(getValue(rs.getNString(i)));
					break;
				case java.sql.Types.VARCHAR:
					obj.add(getValue(rs.getString(i)));
					break;
				case java.sql.Types.TINYINT:
					obj.add(getValue(rs.getInt(i)));
					break;
				case java.sql.Types.SMALLINT:
					obj.add(getValue(rs.getInt(i)));
					break;
				case java.sql.Types.DATE:
					obj.add(getValue(AppUtils.getTimeWithoutMillisISTString(rs.getDate(i))));
					break;
				case java.sql.Types.TIMESTAMP:
					obj.add(getValue(AppUtils.getTimeWithoutMillisISTString(rs.getTimestamp(i))));
					break;
					case Types.BLOB:
						Blob blob = rs.getBlob(i);
						byte[] bdata = blob.getBytes(1, (int) blob.length());
						String s = new String(bdata);
						obj.add(getValue(s));
						break;
				default:
					break;
				}
			}
			json.add(obj);
		}

		return json;
	}

	private String getValue(Number number) {
		return number == null ? "" : number.toString();
	}

	private String getValue(String number) {
		return number == null ? "" : number;
	}

	private String getValue(Boolean number) {
		return number == null ? "false" : Boolean.toString(number);
	}

}