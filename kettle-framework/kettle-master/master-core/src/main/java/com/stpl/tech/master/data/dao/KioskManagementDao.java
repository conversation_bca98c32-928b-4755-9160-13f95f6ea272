/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskLocationDetails;
import com.stpl.tech.master.domain.model.KioskMachine;
import com.stpl.tech.master.domain.model.KioskOfficeDetails;
import com.stpl.tech.master.domain.model.SwitchStatus;

public interface KioskManagementDao {

    public KioskLocationDetails saveKioskLocationDetails(KioskLocationDetails kioskLocationDetails, UnitDetail unitDetail);

    public KioskCompanyDetails saveCompanyDetails(KioskCompanyDetails kioskCompanyDetails);

    public KioskOfficeDetails saveOfficeDetails(KioskOfficeDetails officeDetails);

    public KioskCompanyDetails updateKioskCompanyDetails(KioskCompanyDetails companyDetails);

    public KioskOfficeDetails updateKioskOfficeDetails(KioskOfficeDetails officeDetails);

    public KioskLocationDetails updateKioskLocationDetails(KioskLocationDetails locationDetails, Integer unitId);

    public KioskLocationDetails getLocationDetails(int locationId);

    public KioskCompanyDetails getCompanyDetails(int companyId);

    public KioskOfficeDetails getOfficeDetails(int officeId);

    public KioskMachine getKioskMachineDetails(int machineId, IdCodeName unitData);

    public KioskLocationDetails updateLocationStatus(Integer locationId, SwitchStatus switchStatus);

    public KioskOfficeDetails updateOfficeStatus(Integer officeId, SwitchStatus switchStatus);

    public KioskCompanyDetails updateCompanyStatus(Integer companyId, SwitchStatus switchStatus);

    public KioskLocationDetails assignUnitToLocation(Integer locationId, Integer unitId);
}
