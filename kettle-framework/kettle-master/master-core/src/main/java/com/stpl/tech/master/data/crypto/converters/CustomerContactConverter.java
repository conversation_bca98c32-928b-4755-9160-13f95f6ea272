package com.stpl.tech.master.data.crypto.converters;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.stpl.tech.spring.crypto.DataEncrypter;
import com.stpl.tech.spring.crypto.DataEncrypterFailureHandler;
import com.stpl.tech.util.AppConstants;

import lombok.extern.slf4j.Slf4j;

@Converter
@Slf4j
public class CustomerContactConverter implements AttributeConverter<String, String> {

	private static DataEncrypter dataEncrypter;

	private static DataEncrypterFailureHandler dataEncrypterFailureHandler;

	@Autowired
	public void initDataEncrypter(DataEncrypter dataEncrypter) {
		CustomerContactConverter.dataEncrypter = dataEncrypter;
	}

	@Autowired
	public void initDataEncrypterFailureHandler(DataEncrypterFailureHandler dataEncrypterFailureHandler) {
		CustomerContactConverter.dataEncrypterFailureHandler = dataEncrypterFailureHandler;
	}

	@Override
	public String convertToDatabaseColumn(String attribute) {
		return dataEncrypter.encryptData(attribute);
	}

	@Override
	public String convertToEntityAttribute(String dbData) {
		String value = dataEncrypter.decryptData(dbData, dataEncrypterFailureHandler, AppConstants.CONACT_NUMBER_DATA,
				dbData, AppConstants.CONACT_NUMBER, AppConstants.CUSTOMER_INFO);
		value = StringEscapeUtils.escapeJava(value);
		return value;
	}

}
