/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao;

import com.stpl.tech.kettle.report.metadata.model.WarningMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseValidation;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.AddressInfo;
import com.stpl.tech.master.data.model.ChannelPartnerCommission;
import com.stpl.tech.master.data.model.EmployeeDetail;
import com.stpl.tech.master.data.model.GroupRecommendationMapping;
import com.stpl.tech.master.data.model.KioskCompanyDetailsData;
import com.stpl.tech.master.data.model.KioskLocationDetailsData;
import com.stpl.tech.master.data.model.KioskMachineDetailsData;
import com.stpl.tech.master.data.model.MachineProductMappingMetaData;
import com.stpl.tech.master.data.model.MenuExcelUploadEvent;
import com.stpl.tech.master.data.model.MenuSequenceData;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.PaymentModeAttributes;
import com.stpl.tech.master.data.model.PriceProfileProductMapping;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.RefLookup;
import com.stpl.tech.master.data.model.RegionMap;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.data.model.UnitContactDetailsData;
import com.stpl.tech.master.data.model.UnitGroupMappingData;
import com.stpl.tech.master.domain.model.AddonList;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.CancellationReason;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.Department;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.Division;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Interface for accessing all metadata related to POS. This is the read only
 * API for the metadata.
 *
 * <AUTHOR>
 */
public interface MasterMetadataDao extends AbstractMasterDao {

    public Unit getUnit(int unitId, boolean getAll) throws DataNotFoundException;

    public Collection<Product> getUnitProducts(int unitId, boolean getAll) throws DataNotFoundException;

    public TransactionMetadata getTransactionData(boolean isAndroid) throws DataNotFoundException;

    public TransactionMetadata getMetadataCategories() throws DataNotFoundException;

    public List<Unit> getAllUnits() throws DataNotFoundException;

    public List<Division> getAllDivisions() throws DataNotFoundException;

    public List<Department> getAllDepartments() throws DataNotFoundException;

    public List<ListData> getAllListData(String group, boolean getAll) throws DataNotFoundException;

    public ListData getListData(String code, boolean getAll) throws DataNotFoundException;

    public List<PaymentMode> getAllPaymentMode(PaymentCategory category) throws DataNotFoundException;

    public List<TaxProfile> getAllTaxProfile() throws DataNotFoundException;

    public ListData getDiscountCodes(boolean getAll) throws DataNotFoundException;

    public List<AddonList> getAllAddons(boolean getAll) throws DataNotFoundException;

    public List<ListData> getAllCategories(boolean getAll) throws DataNotFoundException;

    public List<Product> getAllProducts() throws DataNotFoundException;

    public List<Unit> getAllUnits(UnitCategory category) throws DataNotFoundException;

    public List<Designation> getAllDesignations() throws DataNotFoundException;

    public Map<String, List<ListData>> getAllListData() throws DataNotFoundException;

    public ListData upsertRefLookUp(ListData listData) throws DataUpdationException, DataNotFoundException;

    public List<DenominationDetail> getAllDenominations() throws DataNotFoundException;

    public List<EmployeeDetail> getAllEmployees() throws DataNotFoundException;

    public List<KioskCompanyDetailsData> getAllKioskCompanies();

    public List<KioskMachineDetailsData> getAllKioskMachines();

    public List<KioskLocationDetailsData> getAllKioskLocations();

	public List<PaymentModeAttributes> getPaymentModeAttributes(int paymentModeId);

	public List<Location> getAllLocations();

	public List<State> getAllStates();

	public List<CancellationReason> getAllCancellationReasons();

	public List<Company> getAllCompanies();

    public List<Address> getAllAddress();

	/**
	 * @return
	 */
	public Map<Integer, BigDecimal> getAllPaymentModeCommisson();

    public List<ExpenseMetadata> getAllExpenseList();

    @SuppressWarnings("unchecked")
    Map<Integer, List<ExpenseValidation>> getExpenseValidations(List<Integer> expenseIds);

    public List<WarningMetadata> getWarningReasonList(String type);

	public List<Unit> getUnitsOfAreaManager(Integer amId);

    public Long getAllUnitCount() throws DataNotFoundException;

    public List<Unit> getAllUnits(int start, int batchSize) throws DataNotFoundException;

    List<Unit> getUnitsOfCafeManager(Integer amId);

    ChannelPartnerCommission getChannelPartnerCommission(int partnerId, Date businessDate) throws DataNotFoundException;

	Collection<TrimmedProductVO> getActiveProductMappingsForUnits(Integer primaryUnitId, Set<Integer> unitIds);

    Collection<TrimmedProductVO> getActiveProductMappingsForUnitsV1(Integer primaryUnitId, Set<Integer> unitIds);

    public List<RegionMap> getAllRegions();

    List<String> getDimensionById(int id);

    List<String> getDelayReasonListData() throws DataNotFoundException;
    public List<UnitContactDetailsData> getAllUnitContactDetails() throws DataNotFoundException;

    List<String> getCompensationReasonListData() throws DataNotFoundException;

    public  MenuSequenceData getMenuSequenceByMenuExcelIdAndDaySLot(Integer excelMenuSequenceId , String dayslot);

    public List<ProductGroupData> getAllGroupsByTagAndNameAndGroupType(String groupTag , List<String> groupNames , String groupType);

    public List<MenuSequenceMappingData> getAllGroupsMappingsByMenuSequenceId(Integer menuSequenceId);

    public List<UnitGroupMappingData> getUnitGroupMappings(List<Integer> groupIds);

    public List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings(List<Integer> unitChannelPartnerIds);

    public List<MenuSequenceData> getMenuSequenceDataByExcelSequenceId(Integer excelMenuSequenceId);

    public List<GroupRecommendationMapping> getAllProductRecommendationMappingByGroup(Integer groupId , Integer brandId , Integer partnerId
            , List<String> daySlots);

    public List<MenuExcelUploadEvent> getAllActiveMenuExcelUploadEvents(String menuApp);
    public  UnitChannelPartnerMenuMappingData getChannelPartnerMenuMapping(Integer unitChannelPartnerMappingId,
                                                                           Integer menuSequenceId , String menuApp , String daySlot
            , Integer brandId);

    public List<RefLookup> getRefLookUpByIds(List<Integer> rlIds);

    public List<PriceProfileProductMapping> getPriceProfileProductMappings(
            List<Integer> versionNos , List<Integer> profileIds , List<Integer> productIds
            ,List<Integer> dimensionIds);

    public List<MachineProductMappingMetaData> getMachineProductMappingData();

    Map<String, List<ListData>> getListTypesByType(List<String> refTypes) throws DataNotFoundException;
}
