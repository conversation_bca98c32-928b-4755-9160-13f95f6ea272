package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "UNIT_CLOSURE_FORM_METADATA")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnitClosureFormMetaData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UNIT_CLOSURE_FORM_METADATA_ID")
    Long unitClosureFormMetaDataId;

    @Column(name = "DEPARTMENT")
    String department;

    @Column(name = "TASK_DESCRIPTION")
    String taskDescription;

    @Column(name = "DATE_REQUIRED")
    String dateRequired;
    @Column(name = "COMMENT_REQUIRED")
    String commentRequired;

    @Column(name = "ATTACHMENT_REQUIRED")
    String attachmentRequired;
}
