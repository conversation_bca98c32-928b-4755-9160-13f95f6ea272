package com.stpl.tech.master.notification;

import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ZomatoBusinessHourChangeOnUpdateEmailTemplate extends AbstractTemplate {

    private List<UnitHours> request;
    private String response;
    private String unitName;
    private Integer brandId;
    private String partnerName;
    private String basePath;
    private List<UnitHours> oldBusinessHours;
    private String event;

    public ZomatoBusinessHourChangeOnUpdateEmailTemplate(List<UnitHours> request, String response, String unitName, String partnerName, Integer brandId, String basePath,
                                                         List<UnitHours> oldBusinessHours, String event){
        this.request=request;
        this.response = response;
        this.unitName = unitName;
        this.brandId = brandId;
        this.partnerName = partnerName;
        this.basePath = basePath;
        this.oldBusinessHours = oldBusinessHours;
        this.event = event;
    }

    @Override
    public String getTemplatePath() {
        return "template/ZomatoBusinessHourChangeOnUpdateEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "changeBussinessHourOnUpdate" + "/" + unitName + "_" + partnerName + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> diffsMap = new HashMap<>();
        for(UnitHours hours : request){
            String str = "Form : " + AppUtils.convertToHourMinute(hours.getDeliveryOpeningTime()) + " - To : " + AppUtils.convertToHourMinute(hours.getDeliveryClosingTime());
            diffsMap.put(hours.getDayOfTheWeek(),str);
        }
        if(Objects.nonNull(oldBusinessHours)){
            for(UnitHours hours : oldBusinessHours){
                String str = "Form : " + AppUtils.convertToHourMinute(hours.getDeliveryOpeningTime()) + " - To : " + AppUtils.convertToHourMinute(hours.getDeliveryClosingTime());
                diffsMap.put("Old"+hours.getDayOfTheWeek(),str);
                if(!str.equals(diffsMap.get(hours.getDayOfTheWeek()))){
                    diffsMap.put(hours.getDayOfTheWeek()+"Color","yellow");
                }
                else{
                    diffsMap.put(hours.getDayOfTheWeek()+"Color","white");
                }
            }
        }
        diffsMap.put("event",event);
        diffsMap.put("response", response.toUpperCase());
        diffsMap.put("unitName", unitName.toUpperCase());
        diffsMap.put("partnerName", partnerName);
        if(brandId==1){
            diffsMap.put("brandName","CHAAYOS");
        }
        if(brandId==3){
            diffsMap.put("brandName","GNT");
        }
        return diffsMap;
    }
}
