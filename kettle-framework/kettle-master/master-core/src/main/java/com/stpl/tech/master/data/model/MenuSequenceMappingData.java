package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "MENU_SEQUENCE_MAPPING_DATA")
public class MenuSequenceMappingData {

    private Integer id;
    private Integer menuSequenceId;
    private Integer productGroupId;
    private Integer productGroupParentId;
    private Integer index;
    private Integer createdBy;
    private Integer updatedBy;
    private Date creationTime;
    private Date lastUpdateTime;
    private String status;
    private String listingType;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MENU_SEQUENCE_MAPPING_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "MENU_SEQUENCE_ID", nullable = false)
    public Integer getMenuSequenceId() {
        return menuSequenceId;
    }

    public void setMenuSequenceId(Integer menuSequenceId) {
        this.menuSequenceId = menuSequenceId;
    }

    @Column(name = "PRODUCT_GROUP_ID", nullable = false)
    public Integer getProductGroupId() {
        return productGroupId;
    }

    public void setProductGroupId(Integer productGroupId) {
        this.productGroupId = productGroupId;
    }

    @Column(name = "PRODUCT_GROUP_PARENT_ID")
    public Integer getProductGroupParentId() {
        return productGroupParentId;
    }

    public void setProductGroupParentId(Integer productGroupParentId) {
        this.productGroupParentId = productGroupParentId;
    }

    @Column(name = "GROUP_INDEX", nullable = false)
    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "UPDATED_BY", nullable = false)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "CREATION_TIME", nullable = true)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = true)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Column(name = "LISTING_TYPE", nullable = true)
	public String getListingType() {
		return listingType;
	}

	public void setListingType(String listingType) {
		this.listingType = listingType;
	}

    
}
