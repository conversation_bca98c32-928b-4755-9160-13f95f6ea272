package com.stpl.tech.master.core.external.offer.dao.impl;

import com.stpl.tech.master.data.model.OfferDescriptionMetadata;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface OfferDescriptionMetadataDao extends JpaRepository<OfferDescriptionMetadata,Integer> {

    public OfferDescriptionMetadata findByOfferId(Integer offerId);

    public List<OfferDescriptionMetadata> findByOfferIdInAndStatus(Set<Integer> offerId, String status);

    @Modifying
    @Query(value = "UPDATE OFFER_DESCRIPTION_METADATA odm set odm.STATUS = ?2 WHERE odm.OFFER_ID IN (?1)", nativeQuery = true)
    public void setOfferIdsMappingStatus(Set<Integer> offerIds, String status);
}
