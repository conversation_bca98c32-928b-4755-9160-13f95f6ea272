package com.stpl.tech.master.data.model;

import com.stpl.tech.master.enums.OfferApplicabilityFlag;
import com.stpl.tech.master.enums.OfferApplicable;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "APP_OFFER_APPLICABILITY_DATA")
@Getter
@Setter
public class AppOfferApplicabilityData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "APP_OFFER_APPLICABILITY_ID")
    Integer appOfferApplicabilityId;

    @Column(name = "APP_OFFER_APPLICABILITY_FLAG")
    @Enumerated(EnumType.STRING)
    OfferApplicabilityFlag appOfferApplicabilityFlag;

    @Column(name="IS_APPLICABLE")
    @Enumerated(EnumType.STRING)
    OfferApplicable isApplicable;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "APP_OFFER_DETAIL_DATA_ID")
    AppOfferDetailData appOfferDetailData;

}
