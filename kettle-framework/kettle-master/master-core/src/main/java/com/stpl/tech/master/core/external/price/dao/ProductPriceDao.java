package com.stpl.tech.master.core.external.price.dao;

import com.stpl.tech.master.data.dao.AbstractMasterDao;
import com.stpl.tech.master.data.model.UnitProductPricingBulkUpdateEvent;
import com.stpl.tech.master.domain.model.UnitProductPriceSheetDetail;
import com.stpl.tech.master.domain.model.UnitProductPricingDetail;

import java.math.BigDecimal;
import java.util.List;

public interface ProductPriceDao extends AbstractMasterDao {

    List<UnitProductPriceSheetDetail> getUnitProductPriceDetail(String unitCategory, Integer brandId,
                                                                List<String> asList, List<String> pricingProfile,List<String> productIds);

    List<UnitProductPricingDetail> getUnitProductPriceByMappingIds(List<Integer> productPriceIdList);

    void bulkUpdateUnitProductPrice(BigDecimal price, List<Integer> productPriceIds, Integer updatedBy);

    void addBulkUpdateUnitProductPriceLog(BigDecimal price, List<UnitProductPricingDetail> productPricingList,
                                          UnitProductPricingBulkUpdateEvent updateEvent);
}
