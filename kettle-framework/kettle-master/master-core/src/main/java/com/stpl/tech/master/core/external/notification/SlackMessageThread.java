package com.stpl.tech.master.core.external.notification;

import java.util.concurrent.BlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SlackMessageThread implements Runnable {
	private static final Logger LOG = LoggerFactory.getLogger(SlackMessageThread.class);
	private final BlockingQueue<SlackMessage> queue;
	private final SlackApi api;
	private final GoogleChatApi chatApi;
	private static final boolean isSlackNotification = false;

	public SlackMessageThread(BlockingQueue<SlackMessage> queue) {
		super();
		this.queue = queue;
		this.api = new SlackApi();
		this.chatApi = new GoogleChatApi();
	}

	@Override
	public void run() {
		while (queue.peek() != null) {
			SlackMessage data = null;
			try {
				data = queue.take();
			} catch (InterruptedException e1) {
			}
			if (data != null) {
				try {

					if(isSlackNotification){
						LOG.info(String.format("Publishing to slack for User : %s to Channel %s and text %s",
								data.getUsername(), data.getChannel(), data.getText()));
						api.call(data);
						LOG.info(String.format("Published to slack for User : %s to Channel %s and text %s",
								data.getUsername(), data.getChannel(), data.getText()));
					}else{
						LOG.info(String.format("Publishing to google chat for User : %s to Channel %s and text %s",
								data.getUsername(), data.getChannel(), data.getText()));
						chatApi.call(data);
						LOG.info(String.format("Published to google chat for User : %s to Channel %s and text %s",
								data.getUsername(), data.getChannel(), data.getText()));
					}

				} catch (Exception e) {
					LOG.error(String.format("Error while publishing to slack for User : %s to Channel %s and text %s",
							data.getUsername(), data.getChannel(), data.getText()), e);
				}
			}

		}
	}
}
