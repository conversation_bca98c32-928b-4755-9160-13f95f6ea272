/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 08-05-2016.
 */
package com.stpl.tech.master.data.model;

import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.function.BiConsumer;

@Entity
@Table(name = "APPLICATION_DATA")
public class ApplicationData {

	private int id;
	private String name;
	private String description;
	private String status;
	private String macValidationEnabled;
	private String geoValidationEnabled;
	private BigDecimal geoValidationRadius;
	private BigDecimal tokenTtl;  // in hours

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "APPLICATION_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "APPLICATION_NAME", unique = true, nullable = false)
	public String getName() {
		return name;
	}

	public void setName(String role) {
		this.name = role;
	}

	@Column(name = "APPLICATION_DESCRIPTION", nullable = false)
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(name = "APPLICATION_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "MAC_VALIDATION_ENABLED")
	public String getMacValidationEnabled() {
		return macValidationEnabled;
	}

	public void setMacValidationEnabled(String macValidationEnabled) {
		this.macValidationEnabled = macValidationEnabled;
	}

	@Column(name = "GEO_VALIDATION_ENABLED")
	public String getGeoValidationEnabled() {
		return geoValidationEnabled;
	}

	public void setGeoValidationEnabled(String geoValidationEnabled) {
		this.geoValidationEnabled = geoValidationEnabled;
	}


	@Column(name = "GEO_VALIDATION_RADIUS")
	public BigDecimal getGeoValidationRadius() {
		return geoValidationRadius;
	}

	public void setGeoValidationRadius(BigDecimal geoValidationRadius) {
		this.geoValidationRadius = geoValidationRadius;
	}

	@Column(name = "TOKEN_TTL")
	public BigDecimal getTokenTtl() {
		return tokenTtl;
	}

	public void setTokenTtl(BigDecimal tokenTtl) {
		this.tokenTtl = tokenTtl;
	}
}
