/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * CompanyDetail generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "COMPANY_DETAIL")
public class CompanyDetail implements java.io.Serializable {

	private Integer companyId;
	private AddressInfo addressInfo;
	private String companyName;
	private String companyDescription;
	private String cin;
	private String serviceTaxNo;
	private String websiteAddr;
	private String shortCode;
	private Set<BusinessDivision> businessDivisions = new HashSet<BusinessDivision>(0);

	public CompanyDetail() {
	}

	public CompanyDetail(AddressInfo addressInfo, String companyName, String companyDescription, String cin,
			String serviceTaxNo, String websiteAddr, String shortCode) {
		this.addressInfo = addressInfo;
		this.companyName = companyName;
		this.companyDescription = companyDescription;
		this.cin = cin;
		this.serviceTaxNo = serviceTaxNo;
		this.websiteAddr = websiteAddr;
		this.shortCode = shortCode;
	}

	public CompanyDetail(AddressInfo addressInfo, String companyName, String companyDescription, String cin,
			String serviceTaxNo, String websiteAddr, String shortCode, Set<BusinessDivision> businessDivisions) {
		this.addressInfo = addressInfo;
		this.companyName = companyName;
		this.companyDescription = companyDescription;
		this.cin = cin;
		this.serviceTaxNo = serviceTaxNo;
		this.websiteAddr = websiteAddr;
		this.shortCode = shortCode;
		this.businessDivisions = businessDivisions;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COMPANY_ID", unique = true, nullable = false)
	public Integer getCompanyId() {
		return this.companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REGD_ADDR_ID", nullable = false)
	public AddressInfo getAddressInfo() {
		return this.addressInfo;
	}

	public void setAddressInfo(AddressInfo addressInfo) {
		this.addressInfo = addressInfo;
	}

	@Column(name = "COMPANY_NAME", nullable = false)
	public String getCompanyName() {
		return this.companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	@Column(name = "COMPANY_DESCRIPTION", nullable = false, length = 5000)
	public String getCompanyDescription() {
		return this.companyDescription;
	}

	public void setCompanyDescription(String companyDescription) {
		this.companyDescription = companyDescription;
	}

	@Column(name = "CIN", nullable = false, length = 21)
	public String getCin() {
		return this.cin;
	}

	public void setCin(String cin) {
		this.cin = cin;
	}

	@Column(name = "SERVICE_TAX_NO", nullable = false, length = 15)
	public String getServiceTaxNo() {
		return this.serviceTaxNo;
	}

	public void setServiceTaxNo(String serviceTaxNo) {
		this.serviceTaxNo = serviceTaxNo;
	}

	@Column(name = "WEBSITE_ADDR", nullable = false)
	public String getWebsiteAddr() {
		return this.websiteAddr;
	}

	public void setWebsiteAddr(String websiteAddr) {
		this.websiteAddr = websiteAddr;
	}

	@Column(name = "SHORT_CODE", nullable = false)
	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "companyDetail")
	public Set<BusinessDivision> getBusinessDivisions() {
		return this.businessDivisions;
	}

	public void setBusinessDivisions(Set<BusinessDivision> businessDivisions) {
		this.businessDivisions = businessDivisions;
	}

}
