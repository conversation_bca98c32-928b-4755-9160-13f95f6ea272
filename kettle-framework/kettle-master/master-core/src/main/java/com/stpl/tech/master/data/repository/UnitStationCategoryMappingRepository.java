package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.Status;
import com.stpl.tech.master.data.model.UnitStationCategoryMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UnitStationCategoryMappingRepository extends JpaRepository<UnitStationCategoryMapping, Long> {
    
    @Query("SELECT m FROM UnitStationCategoryMapping m WHERE m.unitId = :unitId AND m.refLookupId = :refLookupId AND m.status = :status")
    Optional<UnitStationCategoryMapping> findByUnitIdAndRefLookupIdAndStatus(
        @Param("unitId") Integer unitId,
        @Param("refLookupId") Integer refLookupId,
        @Param("status") Status status
    );
    
    @Query("SELECT DISTINCT sm FROM UnitStationCategoryMapping sm " +
                   "LEFT JOIN FETCH sm.productMappings pm " +
                   "WHERE sm.unitId = :unitId AND sm.status = :status " +
                   "AND pm.status = :status ")
    List<UnitStationCategoryMapping> findByUnitIdAndStatusWithProducts(
            @Param("unitId") Integer unitId,
            @Param("status") Status status);
    
    @Query("SELECT DISTINCT sm FROM UnitStationCategoryMapping sm " +
                   "LEFT JOIN FETCH sm.productMappings pm " +
                   "WHERE sm.status = :status " +
                   "AND pm.status = :status ")
    List<UnitStationCategoryMapping> findByStatusWithProducts(
            @Param("status") Status status);
    
} 