package com.stpl.tech.master.core.external.partner.dao.impl;

import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.partner.dao.ExternalAPIDao;
import com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIToken;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.ExternalPartnerInfo;
import com.stpl.tech.master.data.model.PartnerPermissionMapping;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.RandomStringGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;

@Repository
public class ExternalAPIDaoImpl extends AbstractMasterDaoImpl implements ExternalAPIDao {

    private RandomStringGenerator generator = new RandomStringGenerator();
    @Autowired
    private TokenService<ExternalAPIToken> tokenService;

    @Override
    public void addPartner(String partnerName, String envType) {
        Object o = null;
        try {
            Query query = manager.createQuery("FROM ExternalPartnerInfo where partnerName = :partnerName");
            query.setParameter("partnerName", partnerName);
            o = query.getSingleResult();
        } catch (NoResultException nre) {

        }
        if (o == null) {
			ExternalPartnerInfo info = new ExternalPartnerInfo();
			String code = generator.getRandomCode(15);
			info.setPartnerName(partnerName);
			info.setApiKey("");
			info.setPartnerCode(partnerName);
			info.setPassCode(code);
			info.setCreationDate(AppUtils.getCurrentDate());
			info.setPartnerStatus(AppConstants.ACTIVE);
			manager.persist(info);
			ExternalAPIToken token = new ExternalAPIToken(info.getId(), partnerName, code, envType);
			info.setApiKey(tokenService.createToken(token, -1));
			manager.merge(info);
			manager.flush();
        }
    }

    @Override
    public List<ExternalPartnerInfo> getAllActivePartners() {
        Query query = manager.createQuery("FROM ExternalPartnerInfo where partnerStatus = :partnerStatus");
        query.setParameter("partnerStatus", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public List<PartnerPermissionMapping> getAllPartnerMappings() {
        Query query = manager.createQuery("FROM PartnerPermissionMapping where status = :mappingStatus");
        query.setParameter("mappingStatus", AppConstants.ACTIVE);
        return query.getResultList();
    }
}
