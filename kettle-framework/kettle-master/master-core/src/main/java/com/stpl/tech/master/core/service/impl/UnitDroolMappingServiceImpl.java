package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.MasterCacheManagementService;
import com.stpl.tech.master.core.service.UnitDroolMappingService;
import com.stpl.tech.master.data.model.UnitDroolVersionMapping;
import com.stpl.tech.master.data.repository.UnitDroolVersionMappingDao;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.UnitDroolVersionDomain;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Log4j2
public class UnitDroolMappingServiceImpl implements UnitDroolMappingService {

    @Autowired
    private UnitDroolVersionMappingDao droolVersionMappingDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private MasterCacheManagementService cacheManagementService;

    public Map<Integer, Map<String, DroolVersionDomain>> getUnitDroolVersionMapping() {
        Map<Integer, Map<String, DroolVersionDomain>> unitDroolVersionMapping = new HashMap<>();
        if (Objects.nonNull(masterDataCache.getUnitDroolVersionMapping())) {
            unitDroolVersionMapping = masterDataCache.getUnitDroolVersionMapping();
        } else {
            List<UnitDroolVersionMapping> droolVersionMappings = droolVersionMappingDao.findByMappingStatus(AppConstants.ACTIVE);
            for (UnitDroolVersionMapping mapping : droolVersionMappings) {
                unitDroolVersionMapping.computeIfAbsent(mapping.getUnitId(), k -> new HashMap<>())
                        .computeIfAbsent(mapping.getDroolType(), k -> getDroolVersionDomain(new DroolVersionDomain(), mapping));
            }
        }
        return unitDroolVersionMapping;
    }

    public DroolVersionDomain getDroolVersionDomain(DroolVersionDomain domain, UnitDroolVersionMapping mapping) {
        domain.setVersion(mapping.getVersion());
        domain.setCreationTime(mapping.getCreationTime());
        domain.setCreatedBy(mapping.getCreatedBy());
        domain.setUpdatedBy(mapping.getUpdatedBy());
        domain.setLastUpdationTime(mapping.getLastUpdationTime());
        domain.setRemarks(mapping.getRemarks());
        return domain;
    }

    public Boolean updateUnitDroolVersionMapping(String droolFile, Integer updatedBy, List<UnitDroolVersionDomain> mappingList){
        try {
            List<Integer> unitIds = mappingList.stream().map(UnitDroolVersionDomain::getUnitId).distinct().collect(Collectors.toList());
            List<UnitDroolVersionMapping> existingMappings = droolVersionMappingDao.findByDroolTypeAndUnitIdIn(droolFile, unitIds);
            List<UnitDroolVersionDomain> toRemove =  new ArrayList<>();
            for(UnitDroolVersionDomain domainMapping : mappingList){
                for(UnitDroolVersionMapping mapping : existingMappings){
                    if(domainMapping.getUnitId().equals(mapping.getUnitId())){
                        mapping.setVersion(Objects.nonNull(domainMapping.getUpdatedVersion()) ? domainMapping.getUpdatedVersion() : mapping.getVersion());
                        mapping.setMappingStatus(Objects.nonNull(domainMapping.getUpdatedStatus()) ? domainMapping.getUpdatedStatus() : mapping.getMappingStatus());
                        mapping.setUpdatedBy(updatedBy);
                        mapping.setLastUpdationTime(AppUtils.getCurrentDateIST());
                        toRemove.add(domainMapping);
                    }
                }
            }
            mappingList.removeAll(toRemove);
            droolVersionMappingDao.saveAll(existingMappings);
            List<UnitDroolVersionMapping> newMappings = new ArrayList<>();
            for(UnitDroolVersionDomain domainMapping : mappingList){
                UnitDroolVersionMapping mapping = new UnitDroolVersionMapping();
                mapping.setUnitId(domainMapping.getUnitId());
                mapping.setDroolType(droolFile);
                mapping.setVersion(domainMapping.getUpdatedVersion());
                mapping.setMappingStatus(AppConstants.ACTIVE);
                mapping.setCreatedBy(updatedBy);
                mapping.setCreationTime(AppUtils.getCurrentDateIST());
                newMappings.add(mapping);
            }
            droolVersionMappingDao.saveAll(newMappings);
            cacheManagementService.refreshUnitDroolVersionMappingCache();
            return true;
        }catch (Exception e){
            log.info("Error while updating unit drool version mapping ::::: {}", e);
            return false;
        }
    }
}
