package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 07-06-2016.
 */

@Entity
@Table(name = "PRE_AUTHENTICATED_API")
public class PreAuthenticatedApiData {

    private Integer id;
    private String api;
    private String status;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "API_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "API", nullable = false, unique = true)
    public String getApi() {
        return api;
    }

    public void setApi(String api) {
        this.api = api;
    }

    @Column(name = "STATUS", length = 30)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
