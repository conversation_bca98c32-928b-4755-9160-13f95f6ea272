package com.stpl.tech.master.core.external.offer.dao.impl;

import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.offer.dao.OfferMetaDataManagementDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.OfferMetadata;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
public class OfferMetaDataManagementDaoImpl extends AbstractMasterDaoImpl implements OfferMetaDataManagementDao {

    @Override
    @SneakyThrows
    @SuppressWarnings("unchecked")
    public List<OfferMetadata> findAllByOfferIdAndMappingTypes(Integer offerId, List<String> mappingTypes, String status) {
        try {
            StringBuilder queryBuilder = new StringBuilder("SELECT omd FROM OfferMetadata omd " +
                    "   WHERE omd.offerId.offerDetailId = :offerId " +
                    "   AND omd.mappingType IN (:mappingTypes) ");
            if( StringUtils.isNotBlank(status) ) {
                queryBuilder.append(" AND omd.status = :status ");
            }
            Query query = manager.createQuery(queryBuilder.toString());
            query.setParameter("offerId", offerId);
            query.setParameter("mappingTypes", mappingTypes);
            if ( StringUtils.isNotBlank(status) ) {
                query.setParameter("status", status);
            }
            return query.getResultList();
        } catch (Exception e) {
            log.error("Exception while fetching offer metadata for offer id : {} and mapping types : {}",
                    offerId, mappingTypes, e);
            throw new OfferValidationException("Exception while fetching offer metadata", WebErrorCode.DATA_NOT_FOUND);
        }
    }

}
