package com.stpl.tech.master.core.external.banner.service.Impl;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.banner.dao.BannerDao;
import com.stpl.tech.master.core.external.banner.service.BannerService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.BannerDetailData;
import com.stpl.tech.master.domain.model.*;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Service
public class BannerServiceImpl implements BannerService {

    @Autowired
    BannerDao bannerDao;


    @Override
    public BannerDetailListWrapper getActiveBannerList(String date) {
        List<BannerDetailData> bannerDetailDataList = bannerDao.getActiveBannerListForDate(date);
        List<BannerDetail> bannerDetails = new ArrayList<>();
        if (bannerDetailDataList != null && bannerDetailDataList.size() > 0) {
            bannerDetailDataList.forEach(b -> {
                bannerDetails.add(MasterDataConverter.convert(b));
            });
        }
        return new BannerDetailListWrapper(bannerDetails);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public BannerDetailData saveBanner(BannerDetail banner) throws DataUpdationException {
        BannerDetailData bannerDetailData = null;
        if (banner.getId() != null) {
            bannerDetailData = bannerDao.find(BannerDetailData.class, banner.getId());
        }

        if (bannerDetailData != null) {
            bannerDetailData.setBannerId(banner.getId());
            bannerDetailData.setBannerType(banner.getType());
            bannerDetailData.setBannerTitle(banner.getTitle());
            bannerDetailData.setBannerSubTitle(banner.getSubTitle());
            bannerDetailData.setBannerDescription(banner.getDesc());
            bannerDetailData.setBannerButtonText(banner.getButtonText());
            bannerDetailData.setBannerButtonAction(banner.getButtonAction());
            bannerDetailData.setBannerCode(banner.getCode());
            bannerDetailData.setBannerActivationDate(banner.getStart());
            bannerDetailData.setBannerExpiryDate(banner.getExp());
            bannerDetailData.setCreatedBy(banner.getCreatedBy());
            bannerDetailData.setCreatedOn(AppUtils.getCurrentTimestamp());
            bannerDetailData.setStatus(AppConstants.ACTIVE);
            bannerDetailData.setProductId(banner.getProductId());
            bannerDetailData.setCategoryId(banner.getCategoryId());
            bannerDetailData.setSectionType(banner.getSectionType());
            bannerDetailData.setBannerUrl(banner.getImageUrl());
//            bannerDetailData.setBannerUrl("http://d1nqp92n3q8zl7.cloudfront.net/product_image/safty-banners_4.jpg");
            bannerDetailData = bannerDao.add(bannerDetailData);
            return bannerDetailData;
        } else {
            bannerDetailData = new BannerDetailData();
            bannerDetailData.setBannerType(banner.getType());
            bannerDetailData.setBannerTitle(banner.getTitle());
            bannerDetailData.setBannerSubTitle(banner.getSubTitle());
            bannerDetailData.setBannerDescription(banner.getDesc());
            bannerDetailData.setBannerButtonText(banner.getButtonText());
            bannerDetailData.setBannerButtonAction(banner.getButtonAction());
            bannerDetailData.setBannerCode(banner.getCode());
            bannerDetailData.setBannerActivationDate(banner.getStart());
            bannerDetailData.setBannerExpiryDate(banner.getExp());
            bannerDetailData.setCreatedBy(banner.getCreatedBy());
            bannerDetailData.setCreatedOn(AppUtils.getCurrentTimestamp());
            bannerDetailData.setStatus(AppConstants.ACTIVE);
            bannerDetailData.setProductId(banner.getProductId());
            bannerDetailData.setCategoryId(banner.getCategoryId());
            bannerDetailData.setSectionType(banner.getSectionType());
            bannerDetailData.setBannerUrl(banner.getImageUrl());
//            bannerDetailData.setBannerUrl("http://d1nqp92n3q8zl7.cloudfront.net/product_image/safty-banners_4.jpg");
            bannerDetailData = bannerDao.add(bannerDetailData);
            return bannerDetailData;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<BannerDetail> getBanner() {
        List<BannerDetailData> list = bannerDao.findAll(BannerDetailData.class);
        List<BannerDetail> bannerList = new ArrayList<>();
        for (BannerDetailData data : list) {
            bannerList.add(MasterDataConverter.convert(data));
        }
        return bannerList;
    }

    @Override
    public List<SectionTypeResponse> getSectionType() {
        List<SectionTypeResponse> list = new ArrayList<>();
        for (SectionType type : SectionType.values()) {
            list.add(new SectionTypeResponse(type.getFullName(), type.getShortName()));
        }
        return list;
    }

    @Override
    public List<BannerActionTypeResponse> getBannerActionType() {
        List<BannerActionTypeResponse> list = new ArrayList<>();
        for (BannerActionType type : BannerActionType.values()) {
            list.add(new BannerActionTypeResponse(type.getActionType(), type.getDetail()));
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateBannerStatus(int id, String status) throws DataUpdationException {
        return bannerDao.setStatus(id, status);

    }

}
