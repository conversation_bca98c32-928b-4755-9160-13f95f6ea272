/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ChannelPartner generated by hbm2java
 */
@Entity
@Table(name = "CHANNEL_PARTNER_COMMISSION")
public class ChannelPartnerCommission implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6957317078436097985L;

	private Integer partnerCommissionId;
	private Integer partnerId;
	private BigDecimal commissionRate;
	private BigDecimal taxRate;
	private Date startDate;
	private Date endDate;

	public ChannelPartnerCommission() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "PARTNER_COMMISSION_ID", unique = true, nullable = false)
	public Integer getPartnerCommissionId() {
		return this.partnerCommissionId;
	}

	public void setPartnerCommissionId(Integer partnerCommissionId) {
		this.partnerCommissionId = partnerCommissionId;
	}

	@Column(name = "PARTNER_ID", nullable = false)
	public Integer getPartnerId() {
		return this.partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}

	@Column(name = "COMMISSION_RATE", nullable = false)
	public BigDecimal getCommissionRate() {
		return commissionRate;
	}

	public void setCommissionRate(BigDecimal commissionRate) {
		this.commissionRate = commissionRate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", nullable = false, length = 10)
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@Column(name = "TAX_RATE", nullable = false)
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

}
