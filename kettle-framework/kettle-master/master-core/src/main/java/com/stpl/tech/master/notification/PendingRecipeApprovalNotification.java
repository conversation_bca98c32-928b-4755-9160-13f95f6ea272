/*
 * Created By Shanmukh
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

public class PendingRecipeApprovalNotification extends EmailNotification {

    private List<String> emailIds;
    private EnvType envType;
    private PendingRecipeApprovalNotificationTemplate template;
    private String emailType;

    public PendingRecipeApprovalNotification() {
    }

    public PendingRecipeApprovalNotification(List<String> emailIds, EnvType envType, PendingRecipeApprovalNotificationTemplate template, String emailType) {
        this.emailIds = emailIds;
        this.envType = envType;
        this.template = template;
        this.emailType = emailType;
    }

    @Override
    public String[] getToEmails() {
        if (AppUtils.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            emailIds.add("<EMAIL>");
            if (!emailIds.contains("<EMAIL>")) {
                emailIds.add("<EMAIL>");
            }
            return this.emailIds.toArray(new String[emailIds.size()]);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = getEmailSubject() + AppUtils.getTimeISTString(AppUtils.getCurrentTimestamp());
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    private String getEmailSubject() {
        if (emailType.equalsIgnoreCase("MARKED_IN_ACTIVE")) {
            return "Recipes Marked As IN_ACTIVE ";
        } else if (emailType.equalsIgnoreCase("PENDING_FOR_APPROVAL")) {
            return "Pending For Approval Recipes ";
        } else {
            return "Recipe Marked As Active";
        }
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }


}
