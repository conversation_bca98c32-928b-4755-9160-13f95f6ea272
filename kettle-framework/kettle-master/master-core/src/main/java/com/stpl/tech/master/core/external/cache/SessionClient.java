/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


package com.stpl.tech.master.core.external.cache;

import com.hazelcast.client.HazelcastClient;
import com.hazelcast.core.HazelcastInstance;

import java.util.Map;

public class SessionClient {

    public static void main(String[] args) {
        HazelcastInstance instance = HazelcastClient.newHazelcastClient();

        Map<String, SessionDetail> sessionKeyToDetailMap = instance.getMap("sessionKeyToDetailMap");
        System.out.println("printing sessionKeyToDetailMap");
        if (sessionKeyToDetailMap != null) {
            System.out.println(sessionKeyToDetailMap.size());
            for (String key : sessionKeyToDetailMap.keySet()) {
                System.out.println("key : " + key + "\nvalue : " + sessionKeyToDetailMap.get(key));
            }
        }
        System.out.println("printing userIdToDetailMap");
        Map<Integer, SessionDetail> userIdToDetailMap = instance.getMap("userIdToDetailMap");
        if (userIdToDetailMap != null) {
            System.out.println(userIdToDetailMap.size());
            for (Integer key : userIdToDetailMap.keySet()) {
                System.out.println("key : " + key + "\nvalue : " + userIdToDetailMap.get(key));
            }
        }

    }

}
