package com.stpl.tech.master.core.external.banner.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.BannerDetailData;
import com.stpl.tech.master.domain.model.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BannerService {
    BannerDetailListWrapper getActiveBannerList(String date);

    BannerDetailData saveBanner(BannerDetail banner) throws DataUpdationException;

    Boolean updateBannerStatus(int id, String status) throws DataUpdationException;

    List<BannerDetail> getBanner();

    List<SectionTypeResponse> getSectionType();

    List<BannerActionTypeResponse> getBannerActionType();

}
