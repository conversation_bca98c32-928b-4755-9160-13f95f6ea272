/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.master.core.external.offer.dao;

import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.LaunchOfferStrategy;
import com.stpl.tech.master.core.data.vo.HourlyOfferUnitMapping;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.data.model.AppOfferApplicabilityData;
import com.stpl.tech.master.data.model.AppOfferMappingData;
import com.stpl.tech.master.data.model.CampaignCouponMapping;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CouponDetailMappingData;
import com.stpl.tech.master.data.model.CustomerOfferMappingData;
import com.stpl.tech.master.data.model.CustomerWinbackOfferInfo;
import com.stpl.tech.master.data.model.DeliveryCouponAllocationDetailData;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.DeliveryOfferDetailData;
import com.stpl.tech.master.data.model.LaunchOfferData;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.OfferDetailMappingData;
import com.stpl.tech.master.data.model.SignupOffersCouponDetails;
import com.stpl.tech.master.domain.model.AppOfferDetail;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CouponBulkUpdateDomain;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.util.EnvType;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;


public interface OfferManagementDao extends AbstractDao {

    public CouponDetail getCouponDetail(String couponCode, boolean getAll, boolean getParentMapping, boolean applyLimit);

    public List<AppOfferDetail> getActInactOffers();

    List<AppOfferApplicabilityData> getAppOfferApplicabilityData(Integer appOfferId);

    public Boolean setStatusForAppOffer(IdCodeName idCodeName);

    public Boolean setOrderingForAppOffer(List<IdIndex> list);

    public List<CouponDetail> getRegularCoupons(Integer loggedInBrandId);

    public List<OfferDetail> getAllOffers(boolean trimmed);

    public OfferDetail getOffer(int offerId);

    public List<CouponDetail> getOfferCoupons(int offerId, boolean applyLimit);

    public OfferDetail addOffer(OfferDetail offerDetail) throws OfferValidationException;

    public OfferDetail updateOffer(OfferDetail offerDetail) throws OfferValidationException;

    public List<IdCodeName> getMarketingPartners();

    public IdCodeName addMarketingPartners(IdCodeName marketingPartner);

    public IdCodeName updateMarketingPartners(IdCodeName marketingPartner);

    public CouponDetail addCoupon(CouponDetail coupon);

    public CouponDetail updateCoupon(CouponDetail coupon, boolean updateMappings);

    public boolean updateBulkCoupon(CouponBulkUpdateDomain couponBulkUpdateDomain);

    public List<String> generateUniqueCoupons(String couponCode, String couponPrefix, int replicateCount) throws DataUpdationException;

    public boolean changeMarketingPartnerStatus(int marketingPartnerId, String status);

    public boolean addCouponMapping(String coupon, CouponMappingType mappingType, String value);

    public IdCodeName getMarketingPartner(String key);

    public String getOfferBudgerCategory(String couponCode);

    public boolean updateCouponMapping(int couponMappingId, String status);

    public void updateCouponUsageByOne(int customerId, String offerCode, int orderId);

    public boolean updateCouponMappings(List<CouponDetail> coupons);

    public List<IdName> getOfferAccountsCategories();

    /**
     * @param customerId
     * @param offerCode
     */
    public void setContactNumber(String contactNumber, String couponCode);

    /**
     * @param offerCode
     * @param count
     */
    public void setUsageCountOfAllCouponsForAnOffer(int offerId, int count);

    /**
     * @param offerCode
     * @return
     */
    public List<CouponDetailData> getAllInactiveCoupons(int offerId);

    /**
     * @param offerDetailId
     * @return
     */
    public Set<String> getAllCustomerWithOffer(Integer offerDetailId);

    List<CouponDetailData> getCouponForCustomer(String contactNumber, String date, Integer brandId);

    /**
     * @return
     */
    public List<OfferDetailData> getAllValidOffersWithLaunchStrategy(List<String> launchStrategy);

    /**
     * @param offerDetailId
     */
    public void deactiveOffer(Integer offerDetailId);

    /**
     * @param endTime
     * @return
     */
    public List<CustomerOfferMappingData> getAllPendingNotificationMassOffers(int offerId, Date endTime);

    /**
     * @param customerOfferMappingDataId
     */
    public void setCustomerOfferMappingDataNotified(Integer customerOfferMappingDataId);

    /**
     * @return
     */
    public List<OfferDetailData> getAllOffersWithLaunchStrategy(List<String> launchStrategy);

    /**
     * @param businessDate
     * @return
     */
    public CouponDetail getFreeItemOffer(Date businessDate);

    public void markExpiredOffersAsArchived();

    OfferDetailData getOfferDetailData(int offerId);

    public boolean existCustomerOfferMappingData(int offerDetailId, String number);

    public LaunchOfferData availLaunchOffer(EnvType env, LaunchOfferData input, boolean addUnitMapping);

    public Set<String> getCurrentCycleWinners(List<LaunchOfferStrategy> launchOfferStrategy, Date currentCycleCutOff);

    List<OfferDetail> getOffer(String key, boolean trimmed);

    List<String> getUniqueCoupons(String couponCode, String couponPrefix, int replicateCount)
            throws DataUpdationException;

    List<String> generateCopyCoupons(CouponDetail modelCoupon, List<String> couponCodeSet) throws DataUpdationException;

    CouponDetail getModalCoupon(String modelCouponCode);

    List<AppOfferDetail> getAppOffersByPartnerId(Integer partnerId);

    AppOfferDetail addNewOffer(AppOfferDetail offer);

    AppOfferDetail updateAppOffer(AppOfferDetail offer);

    List<AppOfferMappingData> getAppOfferMappingDataList(Integer appOfferId);

    AppOfferMappingData getOfferMappingUnitData(Integer unitId, Integer appOfferId);

    SignupOffersCouponDetails getCouponDetails();

    List<SignupOffersCouponDetails> changeSignupStatus();

    CouponDetailData getCoupon(String couponCode);

    String getCouponByOfferId(Integer offerId);

    List<CouponDetailMappingData> getCouponMappings(Integer couponDetailId);

    List<CouponDetailMappingData> getCouponMappingsForCustomerId(Integer couponDetailId, Integer customerId);

    long getCouponMappingsCountForCustomerId(Integer couponDetailId);

    boolean isMappingExistForThisCoupon(Integer couponDetailId);

    long getCouponMappingsCountForContactNumber(Integer couponDetailId);

    List<CouponDetailMappingData> getCouponMappingsForContactNumber(Integer couponDetailId, String contactNumber);

    DeliveryCouponDetailData getDeliveryCoupon(String code, Integer brandId);

    DeliveryCouponDetailData getDeliveryCoupon(String code, Integer brandId, Integer channelpartnerId);

    public HourlyOfferUnitMapping getHourlyOfferUnitMapping(int offerDetailId);

    OfferDetailData getOfferDetailDataFromCoupon(String offerCode);

    public void setCouponCodeAsInactive(Integer couponDetailId);

    CampaignDetail addNewCampaign(CampaignDetail campaignDetail);

    CampaignDetail addCampaignToken(CampaignDetail campaignDetail);

    List<CampaignDetail> getAllCampaigns();

    CampaignDetail getCampaignById(Integer campaignId);

    CampaignDetail getCampaignByTokenAndStatus(String campaignToken, String status);

    List<CampaignDetail> getCampaignsByCampaignNameOrDesc(String campaignDesc, Boolean fetchAll);

    CampaignDetail updateCampaignStatus(CampaignDetail campaignDetail);

    CampaignDetail updateCampaignDetail(CampaignDetail campaignDetail);

    Integer updateAllCoupon(CouponDetail couponDetail);

    public CampaignDetail getActiveCampaignByUnitId(Integer unitId, String strategy);

    public CampaignDetail getActiveCampaignByUnitRegion(String region, String strategy);

    public CampaignDetail getActiveCampaignBySystem(String strategy);

    public List<CampaignCouponMapping> getActiveCampaignsByJourney(String repeatType, Integer journey);

    DeliveryCouponDetailData getMasterCoupon(String code, Integer brandId);

    DeliveryCouponDetailData getDeliveryMasterCoupon(String code, Integer brandId, Integer channelPartnerId);

    Long findValidDeliveryCouponCount(String masterCoupon, Date currentTimestamp);

    List<CampaignDetail> getActiveCampaignListByValidDate();

    List<Integer> getLinkedCampaignIds(Integer campaignId);

    CouponDetail getAutoApplicableOfferForUnit(Integer unitId);

    void addDeliveryCoupons(List<DeliveryCouponDetailData> couponDetailDataList);

    public Integer updateAllCouponWithContact(String oldContactNumber, String newContactNumber);

    List<String> getValidCoupon(List<String> coupons);

    public boolean checkOfferIdForCoupon(List<String> couponCodes,Integer offerId);

    public Integer getCouponOfferId( String couponCode);

    DeliveryCouponAllocationDetailData getDeliveryCouponId(String contactNumber);

    DeliveryCouponDetailData getDeliveryCouponCode(Integer deliveryCouponId);

    List<CouponDetailMappingData> getCouponMappingsForContactNumber(String contactNumber);

    CouponDetailData getCouponByCouponIdAndOfferId(List<Integer> couponIds, Integer offerId);

    List<Integer> getOfferAccountCatorybyBudgetCategory(List<String> budgetCategory);

    List<OfferDetailData> getOfferDetailDataByAccountCategory(List<Integer> ids);

    List<DeliveryOfferDetailData> getDeliveryOfferDetailDataForCategory(String category);

    CouponDetailData getCouponDetailByOfferId(Integer offerId);
    CustomerWinbackOfferInfo getCustomerWinbackOfferInfoById(Integer id);
    List<CustomerWinbackOfferInfo> getWinbackInfo();

    public List<CustomerWinbackOfferInfo> getWinbackInfo(Date startDate,Date endDate);
    Map<String,List<CouponDetail>> getCouponDetailByAccountCategory(List<Integer> categoryIds, boolean onlyActive);

    public CouponDetailData getCouponCustomerMapping(String couponCode, String contactNumber);

    public void updateCustomerCouponMapping(CouponDetailData couponDetailData);

    public CouponDetailData getCouponWithoutMappings(String couponCode);

    List<OfferDetailData> getOfferDetailByScope(String offerScope);

    List<OfferDetailMappingData> getOfferDetailMappingDataByMappingTypeAndMappingValueAndOfferdetailIds(String mappingType, String mappingValue, List<Integer> ids);

    List<CouponDetailData> getCouponDetailByResuseAndStatusAndOfferdetailIds(String couponsResuse, String status, List<Integer> ids);

    public List<CustomerOfferMappingData> existCustomerOfferMappingDataList(int offerDetailId, String contactNumber);
    public CouponDetailData getCouponByCouponDetailId(List<Integer> couponDetailId);

}