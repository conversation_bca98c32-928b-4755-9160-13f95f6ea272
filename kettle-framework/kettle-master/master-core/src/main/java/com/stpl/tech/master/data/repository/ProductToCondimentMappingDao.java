package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.ProductCondimentItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductToCondimentMappingDao extends JpaRepository<ProductCondimentItem,Integer> {

    List<ProductCondimentItem> findAllByStatus(String status);

    List<ProductCondimentItem> findAllByGroupIdAndStatus(Integer groupId, String status);

    List<ProductCondimentItem> findAllByGroupId(Integer groupId);
}
