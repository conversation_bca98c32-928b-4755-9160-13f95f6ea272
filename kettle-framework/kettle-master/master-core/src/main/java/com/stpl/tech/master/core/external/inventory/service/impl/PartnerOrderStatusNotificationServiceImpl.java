package com.stpl.tech.master.core.external.inventory.service.impl;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.Session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.amazon.sqs.javamessaging.SQSSession;
import com.stpl.tech.master.core.external.inventory.service.PartnerOrderStatusNotificationService;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

@Service
public class PartnerOrderStatusNotificationServiceImpl implements PartnerOrderStatusNotificationService {
	private static final Logger LOG = LoggerFactory.getLogger(PartnerOrderStatusNotificationServiceImpl.class);

    private MessageProducer inventoryProducer;

    private SQSSession session;

    @Autowired
    private Environment env;

    @PostConstruct
    public void setQueueSessions() throws JMSException {
    	LOG.info("POST-CONSTRUCT PartnerOrderStatusNotificationServiceImpl - STARTED");
        EnvType envType = EnvType.valueOf(env.getProperty("environment.type"));
        session = SQSNotification.getInstance().getSession(AppUtils.getRegion(envType), Session.AUTO_ACKNOWLEDGE);
    }

    public void publishEvent(String env, QuantityResponseData response) throws JMSException {
        if (inventoryProducer == null) {
            inventoryProducer = SQSNotification.getInstance().getProducer(session, env, "_PARTNER_ORDER_STATUS");
        }
        inventoryProducer.send(session.createObjectMessage(response));
    }
}
