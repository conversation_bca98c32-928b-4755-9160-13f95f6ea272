package com.stpl.tech.master.data.model;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Created by Mohit
 */
@Entity
@Table(name = "COUNTRY_DETAIL")
public class CountryDetail {

    private Integer id;
    private String country;
    private String countryCode;
    private String isdCode;
    private String status;
    private List<StateDetail> states = new ArrayList<StateDetail>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "COUNTRY_DETAIL_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "COUNTRY", nullable = false, length = 100)
    public String getCountry() {
        return country;
    }

    public void setCountry(String status) {
        this.country = status;
    }

    @Column(name = "COUNTRY_CODE", nullable = false, length = 10)
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }


    @Column(name = "COUNTRY_ISD_CODE", nullable = false, length = 10)
    public String getIsdCode() {
		return isdCode;
	}

	public void setIsdCode(String isdCode) {
		this.isdCode = isdCode;
	}

	@Column(name = "COUNTRY_STATUS", nullable = false, length = 15)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "country")
    public List<StateDetail> getStates() {
        return states;
    }

    public void setStates(List<StateDetail> states) {
        this.states = states;
    }

}
