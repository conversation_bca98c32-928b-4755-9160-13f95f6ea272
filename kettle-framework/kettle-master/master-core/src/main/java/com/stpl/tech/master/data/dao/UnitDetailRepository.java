package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitDetailRepository extends JpaRepository<UnitDetail, Integer> {

    @Query("SELECT u FROM UnitDetail u WHERE u.unitStatus = :status")
    public List<UnitDetail> findByStatus(@Param("status") String status);

}