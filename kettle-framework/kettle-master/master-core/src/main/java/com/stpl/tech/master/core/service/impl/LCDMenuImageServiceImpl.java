package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.data.dao.LCDMenuImageDao;
import com.stpl.tech.master.data.dao.LCDMenuImageFolderRepository;
import com.stpl.tech.master.data.dao.LcdMenuImagesUploadVersionDao;
import com.stpl.tech.master.data.dao.specification.LCDMenuImageSpecification;
import com.stpl.tech.master.data.model.LCDMenuImage;
import com.stpl.tech.master.data.model.LCDMenuImageFolder;
import com.stpl.tech.master.data.model.LcdMenuImagesUploadVersion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LCDMenuImageServiceImpl {

    @Autowired
    private LCDMenuImageDao imageRepository;

    @Autowired
    private LCDMenuImageFolderRepository folderRepository;

    @Autowired
    private LcdMenuImagesUploadVersionDao lcdMenuImagesUploadVersionDao;

    public List<LCDMenuImage> getFilesForStep(String step, Map<String, String> params) {
        String version = params.get("version");
        String region = params.get("region");
        String priceProfile = params.get("priceProfile");
        String orientation = params.get("orientation");
        String slot = params.get("slot");
        String lcdType = params.get("lcdType");
        
        Specification<LCDMenuImage> spec = LCDMenuImageSpecification.filterBy(version, region, priceProfile, orientation, slot, lcdType);
        return imageRepository.findAll(spec);
    }
    
    public List<LCDMenuImageFolder> getFoldersForStep(String step, Map<String, String> params) {
        // For version step, return all versions
        if ("version".equals(step)) {
            List<String> versions = new ArrayList<>();
            List<LcdMenuImagesUploadVersion> lcdMenuImagesUploadVersions = lcdMenuImagesUploadVersionDao.findAll();
            for(LcdMenuImagesUploadVersion lcdMenuImagesUploadVersion : lcdMenuImagesUploadVersions){
                versions.add(lcdMenuImagesUploadVersion.getVersionNo().toString());
            }
            return versions.stream()
                .map(v -> LCDMenuImageFolder.builder()
                    .step("version")
                    .name(v)
                    .build())
                .collect(Collectors.toList());
        }
        
        // For other steps, return folders for that step
        return folderRepository.findByStep(step);
    }
    
    private String buildPathFromParams(Map<String, String> params) {
        StringBuilder path = new StringBuilder();
        if (params.containsKey("version")) {
            path.append(params.get("version")).append("/");
        }
        if (params.containsKey("region")) {
            path.append(params.get("region")).append("/");
        }
        if (params.containsKey("priceProfile")) {
            path.append(params.get("priceProfile")).append("/");
        }
        if (params.containsKey("orientation")) {
            path.append(params.get("orientation")).append("/");
        }
        if (params.containsKey("slot")) {
            path.append(params.get("slot")).append("/");
        }
        if (params.containsKey("lcdType")) {
            path.append(params.get("lcdType")).append("/");
        }
        return path.toString();
    }
} 