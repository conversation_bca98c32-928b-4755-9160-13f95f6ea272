/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.kettle.report.metadata.model.NameValue;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.ListTypes;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.service.ChannelPartnerService;
import com.stpl.tech.master.core.external.excel.GenericExcelManagementService;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.ProductManagementService;
import com.stpl.tech.master.core.service.UnitManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.MasterMetadataDao;
import com.stpl.tech.master.data.dao.ProductManagementDao;
import com.stpl.tech.master.data.dao.ProductNutritionDetailDao;
import com.stpl.tech.master.data.dao.UnitProductPackagingMappingDao;
import com.stpl.tech.master.data.dao.RecipeDao;
import com.stpl.tech.master.data.dao.UnitPriceProfileMappingDao;
import com.stpl.tech.master.data.model.*;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.master.domain.model.BrandMapping;
import com.stpl.tech.master.domain.model.DocUploadTypeDTO;
import com.stpl.tech.master.domain.model.DocumentDetailDTO;
import com.stpl.tech.master.domain.model.FileTypeDTO;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.ImageCategoryType;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.MachineProductMetaDataResponse;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.PackagingType;
import com.stpl.tech.master.domain.model.PriceProfileKey;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.PriceProfileKey;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductCheckList;
import com.stpl.tech.master.domain.model.ProductCheckListExcelSheet;
import com.stpl.tech.master.domain.model.ProductCityImageMappingDomain;
import com.stpl.tech.master.domain.model.ProductDimensionKey;
import com.stpl.tech.master.domain.model.ProductImageMappingDetail;
import com.stpl.tech.master.domain.model.ProductImageMappingDetailList;
import com.stpl.tech.master.domain.model.ProductPackagingMappingSheet;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.ProductPriceProfile;
import com.stpl.tech.master.domain.model.ProductRecipeMappingRequest;
import com.stpl.tech.master.domain.model.ProductSequence;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitChannelPartnerMapping;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitPartnerMenuMapping;
import com.stpl.tech.master.domain.model.UnitProductMappingData;
import com.stpl.tech.master.domain.model.UnitProductPackagingRequest;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.notification.EmailNotificationTemplate;
import com.stpl.tech.master.notification.ProductProfileDataChange;
import com.stpl.tech.master.notification.ProductProfileDataChangeEmailTemplate;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.master.util.ProductCheckListUtil;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.commons.lang.StringUtils;
import com.stpl.tech.util.notification.AttachmentData;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service
public class ProductManagementServiceImpl implements ProductManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(ProductManagementServiceImpl.class);

    @Autowired
    private ProductManagementDao dao;

    @Autowired
    private MasterMetadataDao masterDao;

    @Autowired
    private MasterDataCacheService masterCacheService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private MasterProperties props;

    @Autowired
    private UnitProductPackagingMappingDao unitProductPackagingMappingDao;

    @Autowired
    private ProductNutritionDetailDao nutritionDetailDao;

    @Autowired
    private ProductCheckListUtil productCheckListUtil;

    @Autowired
    private UnitPriceProfileMappingDao unitPriceProfileMappingDao;

    @Autowired
    private MasterProperties masterProperties;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private UnitManagementService unitManagementService;

    @Autowired
    private ChannelPartnerService channelPartnerService;

    @Autowired
    private GenericExcelManagementService genericExcelManagementService;

    @Autowired
    private RecipeDao recipeDao;

    @Autowired
    @Qualifier(value = "ForkJoinPool")
    private ForkJoinPool forkJoinPool;


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Product addProduct(Product product) throws DataUpdationException, DataNotFoundException {
        Product p = dao.addProduct(product);
        masterCacheService.addProductToMap(new HashMap<String, ListData>(), p);
        return p;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Product updateProduct(Product product) throws DataNotFoundException {
        Product p = dao.updateProduct(product);
        masterCacheService.addProductToMap(new HashMap<String, ListData>(), p);
        /*
         * if(AppConstants.ACTIVE.equals(p.getStatus().toString()){
         * cache.refreshUnits(); }
         */
        return p;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean changeProductStatus(int productId, ProductStatus status) throws DataNotFoundException {
        boolean updateStatus = dao.changeProductStatus(productId, status);
        if (updateStatus) {
            if (ProductStatus.IN_ACTIVE.equals(status)) {
                dao.deactivateAllUnitProductMappingsForProduct(productId);
            }
            masterCacheService.addProductToMap(new HashMap<String, ListData>(), dao.getProduct(productId));
            // cache.refreshUnits();
        }
        return updateStatus;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<UnitProductMappingData> getUnitProductPrice(UnitCategory unitCategory, String unitRegion,
                                                            int productId, int dimensionId) {
        return dao.getUnitProductPrice(unitCategory, unitRegion, productId, dimensionId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<UnitProductMappingData> getUnitProductPrice(String unitCategory,
                                                            Integer productId, Integer dimensionId, String pricingProfileIds, String locationIds,
                                                            String region, Integer companyId) {

        Set<Integer> pricingProfiles = (pricingProfileIds != null && pricingProfileIds.length() > 0) ?
                Arrays.stream(pricingProfileIds.split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toSet()) : new HashSet<>();

        List<Integer> locationIdsList = (locationIds != null && locationIds.length() > 0) ?
                Arrays.stream(locationIds.split(","))
                        .map(Integer::parseInt)
                        .collect(toList()) : new ArrayList<>();

        List<String> regionList = (region != null && region.length() > 0) ? Arrays.asList(region.split(",")) : new ArrayList<>();

        List<Integer> company = new ArrayList<>( MasterUtil.getCompaniesByBrand(null) );

        Map<Integer, UnitBasicDetail> unitMap = getUnitsBasedOnCategory(unitCategory, company, regionList, locationIdsList, pricingProfiles);

        ProductDetail productDetail = dao.find(ProductDetail.class, productId);
        List<UnitProductMappingData> unitProductMappingDataList = dao.getUnitProductPrice(productId, dimensionId, unitMap.keySet(), productDetail);

        if(unitMap.size() > unitProductMappingDataList.size()) {
            addUnMappedUnitsForMapping(unitProductMappingDataList, unitMap, productDetail, dimensionId);
        }
        return unitProductMappingDataList;
    }

    private Map<Integer, UnitBasicDetail> getUnitsBasedOnCategory(String unitCategory, List<Integer> company, List<String> regionList, List<Integer> locationIdsList, Set<Integer> pricingProfiles) {
        return masterDataCache.getUnits(UnitCategory.fromValue(unitCategory)).stream()
                .filter(unit -> company.contains(unit.getCompanyId()))
                .filter(unit -> regionList.contains(unit.getUnitZone()))
                .filter(unit -> locationIdsList.isEmpty() || (unit.getLocation() != null && locationIdsList.contains(unit.getLocation().getId())))
                .filter(unit -> pricingProfiles.isEmpty() || (unit.getPricingProfile() != null && pricingProfiles.contains(unit.getPricingProfile())))
                .collect(Collectors.toMap(
                        UnitBasicDetail::getId, unit -> unit,
                        (existing, replacement) -> existing
                ));
    }


    private void addUnMappedUnitsForMapping(List<UnitProductMappingData> unitProductMappingDataList, Map<Integer, UnitBasicDetail> unitMap, ProductDetail productDetail, Integer dimensionId) {
        for(UnitProductMappingData data : unitProductMappingDataList) {
            unitMap.remove(data.getUnit().getId());
        }
        for(UnitBasicDetail unit : unitMap.values()) {
            UnitProductMappingData mapping = new UnitProductMappingData();
            mapping.setPrice(new ProductPrice());
            mapping.setUnit(new IdCodeName(unit.getId(), unit.getName(), null, null, null, unit.getStatus().name()));
            mapping.setProduct(MasterDataConverter.convertToIdCodeName(productDetail));
            mapping.getPrice().setDimension(dao.getRefLookUp(dimensionId).getRlCode());
            mapping.getPrice().setStatus(UnitStatus.IN_ACTIVE.name());
            unitProductMappingDataList.add(mapping);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ApiResponse getUnitProductMapping(ProductRecipeMappingRequest request) throws MasterException {
        List<Integer> companies = new ArrayList<>( MasterUtil.getCompaniesByBrand(request.getBrandId()) );

        Map<Integer, UnitBasicDetail> unitMap = new HashMap<>();
        if(AppConstants.GNT_BRAND_ID == Objects.requireNonNullElse(request.getBrandId(), 0) &&
                !UnitCategory.COD.name().equals(request.getUnitType())) {
            unitMap = getUnitMappingByBrandId(request);
        } else {
            unitMap = getUnitsBasedOnCategory(request, companies);
        }
        if(CollectionUtils.isEmpty(unitMap)) {
            throw new MasterException("For selected filter Units not found ,please try again later");
        }
        List<ProductDetail> productDetailList = dao.getProductsByIds(request.getProductIds());
        UnitProductProfileContext context = new UnitProductProfileContext();
        context.setForkJoinPool(forkJoinPool);
        Map<String, UnitProductMappingData> unitProductMappingDataMap = dao.getUnitProductMapping(productDetailList, unitMap.keySet(), request.getDimensionIds(), context);

        if(request.getIsDimensionsSelected()) {
            addUnMappedUnitProductsForMapping(unitProductMappingDataMap, unitMap, productDetailList, dao.getRefLookUpByIds(request.getDimensionIds()), context);
        }

        // return ApiResponse
        ApiResponse response =  new ApiResponse(unitProductMappingDataMap.values());
        response.setMessage(CollectionUtils.isEmpty(unitProductMappingDataMap)
                ? "Data not found for the selected filters, please verify filters."
                : "Data returned successfully.");
        return response;
    }

    private Map<Integer, UnitBasicDetail> getUnitMappingByBrandId(ProductRecipeMappingRequest request) {
        List<BrandMapping> brandMappings = unitManagementService.getPartnerMappingUnits(request.getBrandId());
        Set<Integer> unitIds = brandMappings.stream()
                            .filter(mapping -> Boolean.TRUE.equals(mapping.getUnitPartnerSwiggy()) || Boolean.TRUE.equals(mapping.getUnitPartnerZomato()))
                            .map(BrandMapping::getUnitId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(unitIds)) {
            return new HashMap<>();
        }

        return (StringUtils.isBlank(request.getUnitType()) ? masterDataCache.getAllUnits() : masterDataCache.getUnits(UnitCategory.fromValue(request.getUnitType()))).stream()
                .filter(unit -> CollectionUtils.isEmpty(request.getUnitIds()) || request.getUnitIds().contains(unit.getId()) || unitIds.contains(unit.getId()) )
                .filter(unit -> CollectionUtils.isEmpty(request.getRegions()) || (request.getRegions().contains(unit.getUnitZone())))
                .filter(unit -> CollectionUtils.isEmpty(request.getCityIds()) || (unit.getLocation() != null && request.getCityIds().contains(unit.getLocation().getId())))
                .filter(unit -> UnitStatus.ACTIVE.equals(unit.getStatus()))
                .collect(Collectors.toMap(
                        UnitBasicDetail::getId, unit -> unit,
                        (existing, replacement) -> existing
                ));
    }

    private Map<Integer, UnitBasicDetail> getUnitsBasedOnCategory(ProductRecipeMappingRequest request, List<Integer> companies) {
        return (StringUtils.isBlank(request.getUnitType()) ? masterDataCache.getAllUnits() : masterDataCache.getUnits(UnitCategory.fromValue(request.getUnitType()))).stream()
                .filter( unit -> CollectionUtils.isEmpty(request.getUnitIds()) || request.getUnitIds().contains(unit.getId()) )
                .filter(unit -> companies.contains(unit.getCompanyId()))
                .filter(unit -> CollectionUtils.isEmpty(request.getRegions()) || (request.getRegions().contains(unit.getUnitZone())))
                .filter(unit -> request.getCityIds().isEmpty() || (unit.getLocation() != null && request.getCityIds().contains(unit.getLocation().getId())))
                .filter(unit -> UnitStatus.ACTIVE.equals(unit.getStatus()))
                .collect(Collectors.toMap(
                        UnitBasicDetail::getId, unit -> unit,
                        (existing, replacement) -> existing
                ));
    }

    @SneakyThrows
    private void addUnMappedUnitProductsForMapping(Map<String, UnitProductMappingData> unitProductMappingDataMap,
                                                   Map<Integer, UnitBasicDetail> unitMap,
                                                   List<ProductDetail> productDetails,
                                                   Map<Integer, RefLookup> dimensions, UnitProductProfileContext context) {
        ConcurrentHashMap<Integer, List<Integer>> dimensionCodeMap = new ConcurrentHashMap<>();
        List<IdCodeName> products =  MasterDataConverter.filterAndConvertProductDetails(productDetails, dimensionCodeMap);

        ConcurrentHashMap<Integer, IdCodeName> temporaryUnitMap = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, IdCodeName> temporaryProductMap = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, IdCodeName> temporaryRefLookUpMap = new ConcurrentHashMap<>();

        ConcurrentHashMap<String, UnitProductMappingData> newMappings = new ConcurrentHashMap<>();
        try {
            forkJoinPool.submit(() -> {
                try {
                    unitMap.values().parallelStream().forEach(unit -> {
                        try {
                            addUnMappedUnitProductsForMapping(temporaryUnitMap, temporaryProductMap, temporaryRefLookUpMap, products,
                                    dimensions, newMappings, unitProductMappingDataMap, unit, dimensionCodeMap);
                        } catch (Exception e) {
                            LOG.error("Error while processing unit [{}]: {}", unit.getId(), e);
                        }
                    });
                } catch (Exception e) {
                    LOG.error("Error while processing unit mapping: ", e);
                }
            }).get();
            unitProductMappingDataMap.putAll(newMappings);
        } catch (Exception e) {
            LOG.error("Error while adding unmapped unit products for mapping: ", e);
        }
    }

    private void addUnMappedUnitProductsForMapping(ConcurrentHashMap<Integer, IdCodeName> temporaryUnitMap, ConcurrentHashMap<Integer, IdCodeName> temporaryProductMap,
                                                   ConcurrentHashMap<Integer, IdCodeName> temporaryRefLookUpMap, List<IdCodeName> products,
                                                   Map<Integer, RefLookup> dimensions, ConcurrentHashMap<String, UnitProductMappingData> newMappings,
                                                   Map<String, UnitProductMappingData> unitProductMappingDataMap, UnitBasicDetail unit, ConcurrentHashMap<Integer, List<Integer>> dimensionCodeMap) {
        for (IdCodeName product : products) {

            List<Integer> refLookUpIds = dimensionCodeMap.get(product.getId());
            if (CollectionUtils.isEmpty(refLookUpIds)) continue;

            for (Integer dimensionId : dimensions.keySet()) {
                String key = MasterDataConverter.generateKey_U_P_D(unit.getId(), product.getId(), dimensionId);

                if (unitProductMappingDataMap.containsKey(key) || !refLookUpIds.contains(dimensionId)) continue;

                UnitProductMappingData mapping = new UnitProductMappingData();
                mapping.setPrice(new ProductPrice(UnitStatus.IN_ACTIVE.name()));

                IdCodeName unitVal = temporaryUnitMap.computeIfAbsent(unit.getId(),
                        k -> new IdCodeName(unit.getId(), unit.getName(), null, null, null, unit.getStatus().name()));
                mapping.setUnit(unitVal);

                mapping.setProduct(product);

                IdCodeName dimensionVal = temporaryRefLookUpMap.computeIfAbsent(dimensionId,
                        k -> MasterDataConverter.convertToIdCodeName(dimensions.get(dimensionId)));
                mapping.setDimension(dimensionVal);

                newMappings.put(key, mapping);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public Map<Integer, Map<Integer, List<ProductRecipeKey>>> getUnitProductPriceProfiles() {
        return dao.getUnitProductPriceProfiles();
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Set<Integer> updateUnitProductPrice(List<UnitProductMappingData> mappings, Integer updatedBy)
        throws DataNotFoundException {
        boolean updateStatus = dao.updateUnitProductPrice(mappings, updatedBy);
        Set<Integer> productSet = new HashSet<>();
        Set<Integer> unitSet = new HashSet<>();
        Map<Integer,UnitProductMappingData> unitProductMappingDataMap = new HashMap<>();
        if (updateStatus) {
            for (UnitProductMappingData data : mappings) {
                dao.updateUnitProductMappingStatus(data.getUnit().getId(), data.getProduct().getId(),
                    ProductStatus.ACTIVE);
                productSet.add(data.getProduct().getId());
                unitSet.add(data.getUnit().getId());
                unitProductMappingDataMap.put(data.getProduct().getId(),data);
            }
            for (Integer productId : productSet) {
                Product productDetail = dao.getProduct(productId);
                UnitProductMappingData mappingData = unitProductMappingDataMap.get(productId);
                masterCacheService.addProductToMap(new HashMap<String, ListData>(), productDetail);
                updateUnitProductPriceUpdateMap(mappingData,productDetail);
            }
            sendProductProfileDataChangeMail(mappings,updatedBy);
        }
        return unitSet;
    }

    private void updateUnitProductPriceUpdateMap(UnitProductMappingData data , Product product){
        try{
            if(product.isInventoryTracked()){
                if(data.getId() == 0  || data.getPrice().getCurrentProfile().compareTo(data.getPrice().getProfile()) !=0
                || data.getPrice().getCurrentStatus().compareTo(data.getPrice().getStatus()) != 0){
                    masterCacheService.updatePriceProfileStateMapForUnit(data.getUnit().getId(),true);
                }
            }
        }catch (Exception e){
            LOG.error("Error While Updating Unit Product Price State Map : {} ",e);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean updateUnitProductPrice(int unitId, int productId, String dimensionCode, BigDecimal price,
                                          String profile) {
        boolean updateStatus = dao.updateUnitProductPrice(unitId, productId, dimensionCode, price, profile);
        if (updateStatus) {
            try {
                masterCacheService.addProductToMap(new HashMap<String, ListData>(), dao.getProduct(productId));
            } catch (DataNotFoundException e) {
                LOG.error("Error while updating cache", e);
            }
        }
        return updateStatus;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean updateUnitProductMappingStatus(int unitId, int productId, ProductStatus status) {
        return dao.updateUnitProductMappingStatus(unitId, productId, status);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public void refreshUnit(int unitId) throws DataNotFoundException {
        Unit unit = masterDao.getUnit(unitId, false);
        masterCacheService.addUnit(unit);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<ProductPriceProfile> getProductProfileUnits(Integer productId, String profile) {
        List<UnitProductPricing> pricings = dao.getProductProfileUnits(productId, profile);
        List<ProductPriceProfile> list = new ArrayList<>();
        pricings.forEach(price -> {
            ProductPriceProfile priceProfile = new ProductPriceProfile();
            priceProfile.setProductPrice(MasterDataConverter.convert(price,productId));
            priceProfile.setUnitDetails(
                MasterDataConverter.convertToIdCodeName(price.getUnitProductMapping().getUnitDetail()));
            list.add(priceProfile);
        });
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public ProductImageMappingDetail saveProductImage(MimeType mimeType, int productId,
                                                      ImageCategoryType imageCategoryType, MultipartFile file, int updatedBy, Integer index, String link) {
        try {
            String fileName = null;
            ProductImageMapping productImageMapping = dao.getProductImagesByProductIdAndType(productId,
                imageCategoryType.name(), index);
            if (productImageMapping != null) {
                productImageMapping.setLastUpdatedBy(updatedBy);
                productImageMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
                if (imageCategoryType.equals(ImageCategoryType.SHOWCASE_VIDEO)) {
                    fileName = link;
                } else {
                    fileName = getNewFileName(productImageMapping.getImageUrl(), mimeType);
                }
                productImageMapping.setImageUrl(fileName);
            } else {
                productImageMapping = new ProductImageMapping();
                productImageMapping.setProductId(productId);
                productImageMapping.setLastUpdatedBy(updatedBy);
                productImageMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
                productImageMapping.setStatus(AppConstants.ACTIVE);
                productImageMapping.setImageType(imageCategoryType.name());
                productImageMapping.setIndex(index);
                if (imageCategoryType.equals(ImageCategoryType.SHOWCASE_VIDEO)) {
                    fileName = link;
                } else {
                    fileName = productId + "-" + index + imageCategoryType.value() + "_1" + "."
                        + mimeType.name().toLowerCase();
                }
                productImageMapping.setImageUrl(fileName);
            }
            // http://d1nqp92n3q8zl7.cloudfront.net/product_image/10_special_1.jpg
            String baseDir = "master-service/product_image";
            FileDetail s3File = null;
            if (!imageCategoryType.equals(ImageCategoryType.SHOWCASE_VIDEO)) {
                s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, fileName, file,
                    true);
            }
            if (s3File != null || imageCategoryType.equals(ImageCategoryType.SHOWCASE_VIDEO)) {
                productImageMapping = dao.update(productImageMapping);
                return getProductImages(productId);
            }
        } catch (Exception e) {
            LOG.error("Failed to upload image", e);
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public ProductImageMappingDetail saveProductImageByCity(MimeType mimeType, int productId,
                                                       MultipartFile file, int updatedBy, String cityName ,
                                                            String link , String daySlot) {
        try {
            String fileName = null;
            ProductCityImageMapping productImageMapping = dao.getProductImageByProductIdAndCity(productId,cityName,daySlot);
            if (productImageMapping != null) {
                productImageMapping.setLastUpdatedBy(updatedBy);
                productImageMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
                fileName = getNewFileName(productImageMapping.getImageUrl(), mimeType);
                productImageMapping.setImageUrl(fileName);
            } else {
                productImageMapping = new ProductCityImageMapping();
                productImageMapping.setProductId(productId);
                productImageMapping.setLastUpdatedBy(updatedBy);
                productImageMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
                productImageMapping.setStatus(AppConstants.ACTIVE);
                productImageMapping.setImageType(ImageCategoryType.GRID_MENU_LOW.value());
                productImageMapping.setCityName(cityName);
                productImageMapping.setDaySlot(daySlot);
                fileName = productId + "-" + cityName + "-" + daySlot + ImageCategoryType.GRID_MENU_LOW.value() + "_1" + "."
                        + mimeType.name().toLowerCase();
                productImageMapping.setImageUrl(fileName);
            }
            // http://d1nqp92n3q8zl7.cloudfront.net/product_image/10_special_1.jpg
            String baseDir = "master-service/product_image";
            FileDetail s3File = null;
            s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, fileName, file,
                    true);
            if (s3File != null ) {
                productImageMapping = dao.update(productImageMapping);
                return getProductImages(productId);
            }
        } catch (Exception e) {
            LOG.error("Failed to upload image", e);
        }
        return null;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",propagation = Propagation.REQUIRED)
    public Boolean deactivateImageByCity(int productId,int updatedBy, String cityName ,
                                                            String daySlot) {
            ProductCityImageMapping productImageMapping = dao.getProductImageByProductIdAndCity(productId, cityName,daySlot);
            productImageMapping.setStatus(AppConstants.IN_ACTIVE);
            productImageMapping.setLastUpdatedBy(updatedBy);
            productImageMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
            return  true;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean updateUnitProductProfile(List<UnitProductMappingData> mappings, Integer updatedBy) {
        Map<String, List<Integer>> profileToUnitsMapping = new HashMap<>();
        for (UnitProductMappingData mapping : mappings) {
            String profile = mapping.getPrice().getProfile();
            List<Integer> unitProductDimensionList = profileToUnitsMapping.get(profile);
            if (unitProductDimensionList == null) {
                unitProductDimensionList = new ArrayList<>();
            }
            unitProductDimensionList.add(mapping.getUnit().getId());
            profileToUnitsMapping.put(profile, unitProductDimensionList);
        }
        for(Map.Entry<String, List<Integer>> entry : profileToUnitsMapping.entrySet()) {
            dao.updateUnitProductProfile(entry.getValue(), entry.getKey(), updatedBy, mappings.get(0).getProduct().getId(), mappings.get(0).getPrice().getDimension());
        }
        return true;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public ProductDescription saveProductDescriptionData(ProductDescription request) {
        try {
            ProductDescription productDescription = dao.getProductDescriptionDataByProductID(request.getProductId());
            if (productDescription != null) {
                productDescription.setDescription(request.getDescription());
            } else {
                productDescription = request;
            }
            return dao.update(productDescription);
        } catch (Exception e) {
            LOG.error("Failed to upload Description", e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductDescription getProductDescriptionData(int productId) {
        return dao.getProductDescriptionDataByProductID(productId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductDescription> getProductDescriptionDataList(List<Integer> list) {
        return dao.getProductDescriptionDataListByProductID(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductImageMappingDetail getProductImages(int productId) {
        List<ProductImageMapping> list = dao.getProductImagesByProductId(productId);
        return MasterDataConverter.convert(list, productId,null);
    }

    @Override
    public ProductImageMappingDetail getProductDimensionImages(int productId, Integer dimensionCode) {
        List<ProductDimensionImageMapping> list = dao.getProductImagesByProductIdAndDimension(productId,dimensionCode);
        return MasterDataConverter.convert(list, productId,dimensionCode);
    }

    @Override
    public List<NameValue> getImageCategoryType() {
        List<NameValue> list = new ArrayList<>();
        for (ImageCategoryType imageCategoryType : ImageCategoryType.values()) {
            list.add(new NameValue(imageCategoryType.name(), imageCategoryType.size()));
        }
        return list;
    }

    @Override
    public List<IdCodeName> getUnitDetails(UnitCategory unitCategory, String unitRegion) {
        return dao.getUnitDetailsException(unitCategory, unitRegion);
    }

    @Override
    public List<Integer> findDistinctRecipeIdInMenuToSCMProductMap() {
       return dao.findDistinctMenuToSCMRecipeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveMenuToSCMMapData(List<MenuToSCMProductMapData> menuToSCMProductMapData) {
        try{
            dao.addAll(menuToSCMProductMapData);
        }catch(Exception e){
            LOG.error("Exception occurred while adding menuToSCMMapData");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductImageMapping> getProductImageDetail(List<Integer> list) {
        List<ProductImageMapping> productImageMappings = dao.findInProductId(list);
        return productImageMappings;
    }

    @Override
    public List<ProductNutritionDetail> getProductsNutritionInfo(List<Integer> productIds) {
        return dao.findNutritionForProductIds(productIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> recipeProfileInactivationCheck(Integer productId, Integer dimensionId, String profile) {
        List<IdCodeName> result = new ArrayList<>();
        try {
            List<Object[]> data = dao.checkForRecipeProfiles(productId,dimensionId,profile);
            for (Object[] o : data) {
                result.add(new IdCodeName((Integer)o[0],(String) o[1], (String) o[2],  (String) o[3]));
            }
        }
        catch (Exception e) {
            LOG.error("Exception occurred while checking for recipe profile Inactivation :: ",e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void truncateMenuToScmTable() {
        dao.truncateMenuToScmTable();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductImageMappingDetailList getAllProductImages(List<Integer> productIdList) {
//        LOG.info("Inside getting product images for ids :{}",new Gson().toJson(productIdList));
        List<ProductImageMappingDetail> list = new ArrayList<>();
        List<ProductImageMapping> productImageMappings= new ArrayList<>();
        try{
             productImageMappings = dao.findInProductId(productIdList);
        }catch(Exception e){
            LOG.info("Unable to get data from dao:{}",e);
        }
        if (productImageMappings != null) {
            Map<Integer, List<ProductImageMapping>> listMap = productImageMappings.stream()
                .collect(Collectors.groupingBy(ProductImageMapping::getProductId));
            for (Integer productId : productIdList) {
                if (!listMap.containsKey(productId)) {
                    list.add(new ProductImageMappingDetail(productId));
                }
            }
            for (Map.Entry<Integer, List<ProductImageMapping>> entry : listMap.entrySet()) {
                list.add(MasterDataConverter.convert(entry.getValue(), entry.getKey(),null));
            }
        }
        return new ProductImageMappingDetailList(list);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public ProductImageMappingDetailList getAllProductDimensionImages(List<Integer> productIdList) {
//        LOG.info("Inside getting product images for ids :{}",new Gson().toJson(productIdList));
        List<ProductImageMappingDetail> list = new ArrayList<>();
        List<ProductDimensionImageMapping> productImageMappings= new ArrayList<>();
        try{
            productImageMappings = dao.getProductsImages(productIdList);
        }catch(Exception e){
            LOG.info("Unable to get data from dao:{}",e);
        }
        if (productImageMappings != null) {
            Map<Integer, Map<Integer, List<ProductDimensionImageMapping>>> listMap =
                    productImageMappings.stream()
                            .collect(Collectors.groupingBy(
                                    ProductDimensionImageMapping::getProductId,
                                    Collectors.groupingBy(ProductDimensionImageMapping::getDimensionCode)
                            ));
            for (Integer productId : productIdList) {
                if (!listMap.containsKey(productId)) {
                    list.add(new ProductImageMappingDetail(productId));
                }
            }
            for (Map.Entry<Integer, Map<Integer, List<ProductDimensionImageMapping>>> entry : listMap.entrySet()) {
                Integer productId = entry.getKey();
                for (Map.Entry<Integer, List<ProductDimensionImageMapping>> dimEntry : entry.getValue().entrySet()) {
                    list.add(MasterDataConverter.convert(dimEntry.getValue(), productId, dimEntry.getKey()));
                }
            }
        }
        return new ProductImageMappingDetailList(list);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public Map<String, List<ProductImageMappingDTO>> getProductDimensionImageData() {
        Map<String, List<ProductImageMappingDTO>> mappingDTOS = new HashMap<>();

        try {
            List<Integer> productIds = MasterUtil.getHotBeverageProductIds(masterDataCache.getProductDetails());

            List<ProductDimensionImageMapping> productImageMappings;
            productImageMappings = dao.getProductsImages(productIds);
            Collection<IdCodeName> dimensionCollection = (Collection<IdCodeName>) masterDataCache.getListData().get(ListTypes.DIMENSION_CODES);
            List<IdCodeName> dimensionCodes = new ArrayList<>(dimensionCollection);
            for (ProductDimensionImageMapping mapping : productImageMappings) {
                ProductImageMappingDTO productImageMappingDTO = new ProductImageMappingDTO();
                productImageMappingDTO.setProductId(mapping.getProductId());
                productImageMappingDTO.setDimensionCode(mapping.getDimensionCode());
                productImageMappingDTO.setIndex(mapping.getIndex());
                productImageMappingDTO.setImageType(mapping.getImageType());
                productImageMappingDTO.setImageUrl(mapping.getImageUrl());
                String dimensionName = null;
                Optional<IdCodeName> idCodeName= dimensionCodes
                        .stream().filter(item -> mapping.getDimensionCode().equals(item.getId())).findFirst();
                if(idCodeName.isPresent()){
                    dimensionName = idCodeName.get().getCode();
                }
                productImageMappingDTO.setDimensionName(dimensionName);
                // Ensure the list is initialized
                String key = mapping.getProductId().toString() + "_" + dimensionName;
                mappingDTOS.computeIfAbsent(key, k -> new ArrayList<>()).add(productImageMappingDTO);
            }
        } catch (Exception e) {
            LOG.error("Unexpected error in getProductDimensionImageData", e);
        }

        return mappingDTOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer,Map<String, ProductCityImageMappingDomain>> getAllProductImagesByCity(List<Integer> productIdList , String cityName) {
//        LOG.info("Inside getting product images for ids :{}",new Gson().toJson(productIdList));
        List<ProductCityImageMapping> productImageMappings= new ArrayList<>();
        Map<Integer,Map<String,ProductCityImageMappingDomain>> productCityImageMapping = new HashMap<>();
        try{
            productImageMappings = dao.getProductImagesByProductIdAndCity(productIdList,cityName);
        }catch(Exception e){
            LOG.info("Unable to get data from dao:{}",e);
        }
        if (!CollectionUtils.isEmpty(productImageMappings)) {

            productCityImageMapping = productImageMappings.stream()
                    .collect(Collectors.groupingBy(
                            ProductCityImageMapping::getProductId,
                            Collectors.toMap(
                                    ProductCityImageMapping::getCityName,
                                    imageMapping -> convert(imageMapping)
                            )
                    ));
        }
        return productCityImageMapping;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductCityImageMappingDomain getProductImagesByCity(Integer productId , String cityName, String daySlot) {
//        LOG.info("Inside getting product images for ids :{}",new Gson().toJson(productIdList));
        ProductCityImageMapping productImageMapping = new ProductCityImageMapping();
        try{
            productImageMapping = dao.getProductImageByProductIdAndCity(productId,cityName,daySlot);
        }catch(Exception e){
            LOG.info("Unable to get data from dao:{}",e);
        }
        if(Objects.nonNull(productImageMapping)){
            return convert(productImageMapping);
        }else{
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer,Map<String, ProductCityImageMappingDomain>> getAllProductImagesByCity(String cityName , String daySlot) {
//        LOG.info("Inside getting product images for ids :{}",new Gson().toJson(productIdList));
        List<ProductCityImageMapping> productImageMappings= new ArrayList<>();
        Map<Integer,Map<String,ProductCityImageMappingDomain>> productCityImageMapping = new HashMap<>();
        Map<Integer,ProductCityImageMapping> productCityImageMappingMap = new HashMap<>();
        try{
            productImageMappings = dao.getProductImagesByCity(cityName,daySlot);
            productCityImageMappingMap =  productImageMappings.stream().collect(Collectors.toMap(ProductCityImageMapping::getProductId, Function.identity()));
        }catch(Exception e){
            LOG.info("Unable to get data from dao:{}",e);
        }
        try{
            List<ProductCityImageMapping> daySlotSpecificImageMappings = dao.getProductImagesByCity(cityName,"ALL");
            if(!CollectionUtils.isEmpty(daySlotSpecificImageMappings)){
                for(ProductCityImageMapping productCityImageMapping1 : daySlotSpecificImageMappings){
                    if(!productCityImageMappingMap.containsKey(productCityImageMapping1.getProductId())){
                        productImageMappings.add(productCityImageMapping1);
                    }
                }
            }
        }catch(Exception e){
            LOG.info("Unable to get data from dao:{}",e);
        }


        if (!CollectionUtils.isEmpty(productImageMappings)) {

            productCityImageMapping = productImageMappings.stream()
                    .collect(Collectors.groupingBy(
                            ProductCityImageMapping::getProductId,
                            Collectors.toMap(
                                    ProductCityImageMapping::getCityName,
                                    imageMapping -> convert(imageMapping)
                            )
                    ));
        }
        return productCityImageMapping;
    }

    private ProductCityImageMappingDomain convert(ProductCityImageMapping productCityImageMapping){
        ProductCityImageMappingDomain productCityImageMappingDomain = new ProductCityImageMappingDomain();
        productCityImageMappingDomain.setCityName(productCityImageMapping.getCityName());
        productCityImageMappingDomain.setProductId(productCityImageMapping.getProductId());
        productCityImageMappingDomain.setImageType(productCityImageMapping.getImageType());
        productCityImageMappingDomain.setLastUpdatedBy(productCityImageMapping.getLastUpdatedBy());
        productCityImageMappingDomain.setImageUrl(productCityImageMapping.getImageUrl());
        productCityImageMappingDomain.setStatus(productCityImageMapping.getStatus());
        productCityImageMappingDomain.setLastUpdationTime(productCityImageMapping.getLastUpdationTime());
        return productCityImageMappingDomain;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductImageMappingDetailList getAllProductImages() {
        List<ProductImageMappingDetail> list = new ArrayList<>();
        List<ProductImageMapping> productImageMappings = dao.findAll(ProductImageMapping.class);
        if (productImageMappings != null) {
            Map<Integer, List<ProductImageMapping>> listMap = productImageMappings.stream()
                .collect(Collectors.groupingBy(ProductImageMapping::getProductId));
            for (Map.Entry<Integer, List<ProductImageMapping>> entry : listMap.entrySet()) {
                list.add(MasterDataConverter.convert(entry.getValue(), entry.getKey(),null));
            }
        }
        return new ProductImageMappingDetailList(list);
    }


    @Override
    public List<Integer> getUnitsByRecipeProfileAndProductId(String profile , Integer productId){
      return   dao.getProductProfileUnits(productId,profile).stream().map(priceMaping ->
                priceMaping.getUnitProductMapping().getUnitDetail().getUnitId()).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public Map<Integer, List<ProductRecipeKey>> getProductPriceProfilesForUnit(int unitId) {
        return dao.getProductPriceProfilesForUnit(unitId);
    }

    private String getNewFileName(String fileName, MimeType mimeType) {
        String[] strings = fileName.split("_");
        int lastIndex = strings.length - 1;
        fileName = "";
        for (int i = 0; i < lastIndex; i++) {
            fileName += strings[i] + "_";
        }
        fileName += (Integer.valueOf(strings[lastIndex].split("\\.")[0]) + 1);
        return fileName + "." + mimeType.name().toLowerCase();
    }

    public void sendProductProfileDataChangeMail(List<UnitProductMappingData> mappings, Integer updatedBy){

        String toEmails = masterDataCache.getEmployeeBasicDetail(updatedBy).getEmailId();
        try {
            if(!CollectionUtils.isEmpty(mappings) && Objects.nonNull(updatedBy)) {
//                ProductProfileDataChangeEmailTemplate template = new ProductProfileDataChangeEmailTemplate(props.getBasePath(), mappings, updatedBy);
//                ProductProfileDataChange productProfileDataChange = new ProductProfileDataChange(props.getEnvironmentType(),toEmails, template);
//                productProfileDataChange.sendEmail();
            }
        }catch (Exception e){
            LOG.info("Error while Sending Email For product price Updation :::::");
            LOG.info("EXCEPTION : {}" , e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getUnitProductPackagingTemplate(UnitProductPackagingRequest request) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest httpServletRequest, HttpServletResponse response) throws Exception {
                String fileName = "UnitProductPackagingMappingSheet.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<ProductPackagingMappingSheet> sheetList = new ArrayList<>();
                Map<Integer, Map<Integer,UnitProductPackagingMapping>> packagingMappingMap = unitProductPackagingMappingDao.findByUnitIdIn(request.getPricingUnitIds())
                        .stream().collect(Collectors.groupingBy(UnitProductPackagingMapping::getUnitId,Collectors.toMap(UnitProductPackagingMapping::getProductId,
                                Function.identity())));
                for(Integer unitId : request.getPricingUnitIds()){
                    Unit unit = masterCacheService.getUnit(unitId);
                    for(Integer productId : request.getProductIds()){
                        ProductPackagingMappingSheet packagingMappingSheet;
                        if(packagingMappingMap.containsKey(unitId) && packagingMappingMap.get(unitId).containsKey(productId)){
                            packagingMappingSheet  = convert(packagingMappingMap.get(unitId).get(productId));
                        }else {
                            packagingMappingSheet = new ProductPackagingMappingSheet(unitId,unit.getName()
                                    ,productId,masterCacheService.getProduct(productId).getName());
                        }
                        sheetList.add(packagingMappingSheet);
                    }
                }
                if (!CollectionUtils.isEmpty(sheetList)) {
                    writer.writeSheet(sheetList, ProductPackagingMappingSheet.class);
                }
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer,ProductPackagingMappingSheet> getUnitProductPackagingMapping(Integer pricingUnitId){
        Map<Integer, Map<Integer,ProductPackagingMappingSheet>> packagingMappingMap = unitProductPackagingMappingDao.findByUnitIdIn(new ArrayList<>(Arrays.asList(pricingUnitId)))
                .stream().filter(mapping -> mapping.getMappingStatus().equalsIgnoreCase(AppConstants.ACTIVE))
                .map(mapping -> convert(mapping))
                .collect(Collectors.groupingBy(ProductPackagingMappingSheet::getUnitId,Collectors.toMap(ProductPackagingMappingSheet::getProductId,
                        Function.identity())));
        return  packagingMappingMap.get(pricingUnitId);
    }
    private void validateUploadedPackagingSheet(List<ProductPackagingMappingSheet> uploadedSheets , List<ExcelParsingException> errors){
        for(ProductPackagingMappingSheet sheet : uploadedSheets){
            if(!(sheet.getPackagingType().equalsIgnoreCase(PackagingType.FIXED.name()) ||
                    sheet.getPackagingType().equalsIgnoreCase(PackagingType.PERCENTAGE.name()))){
                errors.add(new ExcelParsingException("Packaging Type Is Invalid"));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductPackagingMappingSheet> parseAndUpdateUnitProductPackaging(
            MultipartFile file, Integer updatedBy) throws IOException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();

        int sheetCount = workbook.getNumberOfSheets();
        List<ProductPackagingMappingSheet> entityList = new ArrayList<>();
        for (int i = 0; i < sheetCount; i++) {
            LOG.info("Parsing Product Packaging Mapping Sheet {}", i + 1);
            List<ProductPackagingMappingSheet> sheetDetailList = parser.createEntity(workbook.getSheetAt(i),
                    ProductPackagingMappingSheet.class, errors::add);
            entityList.addAll(sheetDetailList);
        }
        validateUploadedPackagingSheet(entityList,errors);
        if (errors.isEmpty()) {
            List<Integer> pricingUnitIds =  entityList.stream().map(mapping -> mapping.getUnitId()).collect(Collectors.toList());
            Map<Integer, Map<Integer,UnitProductPackagingMapping>> packagingMappingMap = unitProductPackagingMappingDao.findByUnitIdIn(pricingUnitIds)
                    .stream().collect(Collectors.groupingBy(UnitProductPackagingMapping::getUnitId,Collectors.toMap(UnitProductPackagingMapping::getProductId,
                            Function.identity())));
            List<UnitProductPackagingMapping> unitProductPackagingMappingList = entityList.stream().map(sheet -> convert(sheet,updatedBy))
                    .map(sheet1 -> {
                        if(packagingMappingMap.containsKey(sheet1.getUnitId()) && packagingMappingMap.get(sheet1.getUnitId()).containsKey(sheet1.getProductId())){
                            sheet1.setMappingId(packagingMappingMap.get(sheet1.getUnitId()).get(sheet1.getProductId()).getMappingId());
                        }
                        return sheet1;
                    }).collect(Collectors.toList());
            unitProductPackagingMappingDao.saveAll(unitProductPackagingMappingList);
            workbook.close();
            return entityList;
        } else {
            LOG.info("Error Parsing Workbook for Product Packaging Mapping, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
    }

    private UnitProductPackagingMapping convert(ProductPackagingMappingSheet productPackagingMappingSheet,Integer updatedBy){
        UnitProductPackagingMapping unitProductPackagingMapping = new UnitProductPackagingMapping();
        unitProductPackagingMapping.setProductId(productPackagingMappingSheet.getProductId());
        unitProductPackagingMapping.setUnitId(productPackagingMappingSheet.getUnitId());
        unitProductPackagingMapping.setPackagingType(productPackagingMappingSheet.getPackagingType());
        unitProductPackagingMapping.setPackagingValue(productPackagingMappingSheet.getPackagingValue());
        unitProductPackagingMapping.setMappingStatus(productPackagingMappingSheet.getMappingStatus());
        unitProductPackagingMapping.setUpdatedBy(updatedBy);
        unitProductPackagingMapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
        return unitProductPackagingMapping;
    }

    private ProductPackagingMappingSheet convert(UnitProductPackagingMapping unitProductPackagingMapping){
        ProductPackagingMappingSheet productPackagingMappingSheet = new ProductPackagingMappingSheet();
        productPackagingMappingSheet.setUnitId(unitProductPackagingMapping.getUnitId());
        productPackagingMappingSheet.setProductId(unitProductPackagingMapping.getProductId());
        productPackagingMappingSheet.setMappingStatus(unitProductPackagingMapping.getMappingStatus());
        productPackagingMappingSheet.setPackagingType(unitProductPackagingMapping.getPackagingType());
        productPackagingMappingSheet.setPackagingValue(unitProductPackagingMapping.getPackagingValue());
        productPackagingMappingSheet.setProductName(masterCacheService.getProduct(unitProductPackagingMapping.getProductId()).getName());
        productPackagingMappingSheet.setUnitName(masterCacheService.getUnit(unitProductPackagingMapping.getUnitId()).getName());
        return  productPackagingMappingSheet;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String,Map<Integer,Map<NutritionDataEnum,BigDecimal>>> getProductsNutritionData(List<String> sourceType) {
        Map<String,Map<Integer,Map<NutritionDataEnum,BigDecimal>>> data = new HashMap<>();
        List<ProductNutritionDetail> productNutritionDetails = dao.getProductsNutritionData(sourceType);
        if(!CollectionUtils.isEmpty(productNutritionDetails)) {
            for (ProductNutritionDetail nutritionDetail : productNutritionDetails) {
                Map<NutritionDataEnum,BigDecimal> countData = new HashMap<>();
                countData.put(NutritionDataEnum.CALORIE_COUNT,Objects.nonNull(nutritionDetail.getCalorieCount()) ? nutritionDetail.getCalorieCount() : BigDecimal.ZERO);
                countData.put(NutritionDataEnum.PROTEIN_COUNT,Objects.nonNull(nutritionDetail.getProteinCount()) ? nutritionDetail.getProteinCount() : BigDecimal.ZERO);
                countData.put(NutritionDataEnum.FAT_COUNT,Objects.nonNull(nutritionDetail.getFatCount()) ? nutritionDetail.getFatCount() : BigDecimal.ZERO);
                countData.put(NutritionDataEnum.CARBOHYDRATE_COUNT,Objects.nonNull(nutritionDetail.getCarbohydrateCount()) ? nutritionDetail.getCarbohydrateCount() : BigDecimal.ZERO);
                countData.put(NutritionDataEnum.FIBRE_COUNT,Objects.nonNull(nutritionDetail.getFiberCount()) ? nutritionDetail.getFiberCount() : BigDecimal.ZERO);
                data.computeIfAbsent(nutritionDetail.getSourceType(), k -> new HashMap<>())
                        .put(nutritionDetail.getProductId(), countData);
            }
        }
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean updateProductsNutritionInfo(Map<Integer, Map<NutritionDataEnum, BigDecimal>> data, String sourceType) {
        try {

            List<ProductNutritionDetail> details = nutritionDetailDao.findAllByProductIdInAndSourceType(data.keySet(), sourceType);

            List<ProductNutritionDetail> newUpdates = new ArrayList<>();
            Set<Integer> existingProductIds = new HashSet<>();

            if(!CollectionUtils.isEmpty(details)) {
                for (ProductNutritionDetail existingProductData : details) {
                    if (data.containsKey(existingProductData.getProductId())) {
                        existingProductData = getProductNutritionDetail(existingProductData,data.get(existingProductData.getProductId()),
                                sourceType,existingProductData.getProductId());
                    }
                    existingProductIds.add(existingProductData.getProductId());
                }
                nutritionDetailDao.saveAll(details);
            }

            for (Map.Entry<Integer, Map<NutritionDataEnum, BigDecimal>> entry : data.entrySet()) {
                if(!existingProductIds.contains(entry.getKey())) {
                    newUpdates.add(getProductNutritionDetail(new ProductNutritionDetail(), entry.getValue(), sourceType, entry.getKey()));
                }
            }
            if (!newUpdates.isEmpty()) {
                nutritionDetailDao.saveAll(newUpdates);
            }
        } catch (Exception e) {
            LOG.info("Error while updating product nutrition data ::::: {}", e);
            return false;
        }
        return true;
    }

    public ProductNutritionDetail getProductNutritionDetail(ProductNutritionDetail detail, Map<NutritionDataEnum, BigDecimal> mapData,
                                                            String sourceType, Integer productId) {
        detail.setProductId(productId);
        detail.setCalorieCount(mapData.getOrDefault(NutritionDataEnum.CALORIE_COUNT, null));
        detail.setFatCount(mapData.getOrDefault(NutritionDataEnum.FAT_COUNT, null));
        detail.setFiberCount(mapData.getOrDefault(NutritionDataEnum.FIBRE_COUNT, null));
        detail.setCarbohydrateCount(mapData.getOrDefault(NutritionDataEnum.CARBOHYDRATE_COUNT, null));
        detail.setProteinCount(mapData.getOrDefault(NutritionDataEnum.PROTEIN_COUNT, null));
        detail.setSourceType(sourceType);
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  Map< String,Map<Integer,  Set<UnitProductPriceCategoryDomain>>> getUnitsSpecificProductsPrice(){

        Map< String,Map<Integer,  Set<UnitProductPriceCategoryDomain>>> resultMap = new HashMap<>();
        try {
            List<UnitProductPriceCategoryDetail> resultList = dao.getUnitsSpecificProductsPrice();

            for (UnitProductPriceCategoryDetail result : resultList) {

                String key = result.getPricingCategory();
                if (Objects.isNull(result.getPricingCategory())) {
                    key = AppConstants.OTHERS;
                }
                Integer productId = result.getProductId();
                Map<Integer, Set<UnitProductPriceCategoryDomain>> productIdsMap = resultMap.getOrDefault(key, new HashMap<>());

                UnitProductPriceCategoryDomain domain = new UnitProductPriceCategoryDomain();
                Set<UnitProductPriceCategoryDomain> namesWithPrices = productIdsMap.getOrDefault(productId, new HashSet<>());
                domain.setPrice(result.getPrice());
                domain.setProductName(result.getAliasProductName() != null ? result.getAliasProductName() : result.getProductName());
                namesWithPrices.add(domain);
                productIdsMap.put(productId, namesWithPrices);
                resultMap.put(key, productIdsMap);
            }
        }
        catch (Exception e){
            LOG.info("Error while updating price category wise products price ::::: {}", e);
        }
        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ProductCheckListEvent createCheckListEvent(Integer userId) {
        ProductCheckListEvent checkListEvent = new ProductCheckListEvent();
        checkListEvent.setUserId(userId);
        checkListEvent.setGenerationTimeStamp(AppUtils.getCurrentTimestamp());
        checkListEvent.setStatus(AppConstants.PROCESSING);
        masterDao.add(checkListEvent);
        return checkListEvent;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void createProductCheckList(List<ProductCheckList> productCheckList, ProductCheckListEvent checkListEvent) {
        try {
            List<ProductCheckListExcelSheet> productCheckListExcelSheetData = new ArrayList<>();
            Map<UnitPartnerBrandKey, UnitPartnerBrandMappingData> unitPartnerBrandMappingDataMap =
                    masterDataCache.getUnitPartnerBrandMappingMetaData();
            Map<UnitPartnerBrandKey, List<UnitPartnerMenuMapping>> menuMappings =
                    masterDataCache.getUnitPartnerMenuMappingsMap();
            Map<Integer, UnitBasicDetail> unitsBasicDetail =
                    masterDataCache.getUnitsBasicDetails();
            Map<PriceProfileKey,Map<ProductDimensionKey,BigDecimal>> priceProfileProductMap =
                    masterCacheService.getPriceProfileMapFromDao();

            List<String> recipeStatuses = new ArrayList<>(Arrays.asList(AppConstants.ACTIVE, AppConstants.IN_PROGRESS));

            for (ProductCheckList productCheckListItem : productCheckList) {
                List<IdCodeName> units = productCheckListItem.getUnits();
                Integer dimensionId = productCheckListItem.getDimension().getId();
                Integer brandId = productCheckListItem.getBrandId();
                Integer productId = productCheckListItem.getProduct().getId();
                Product product = masterDataCache.getProduct(productId);
                List<Integer> unitIds = new ArrayList<>();
                List<Integer> allUnitIds = new ArrayList<>();
                ProductDimensionKey productDimensionKey = ProductDimensionKey.builder().productId(productId).
                        dimension(productCheckListItem.getDimension().getCode()).build();

                units.forEach(unit -> {
                    unitIds.add(unit.getId());
                });

                Map<Integer, Set<UnitChannelPartnerMapping>> unitChannelPartnerMappingMap = getUnitChannelPartnerMap(unitIds);
                Map<Integer, List<UnitPartnerBrandKey>> brandPartnerMappingByUnit = new HashMap<>();
                Set<Integer> pricingUnitIds = new HashSet<>();

                createBrandPartnerMappingByUnitMap(unitChannelPartnerMappingMap, unitPartnerBrandMappingDataMap, pricingUnitIds,
                        brandId, brandPartnerMappingByUnit);

                allUnitIds.addAll(unitIds);
                allUnitIds.addAll(pricingUnitIds);
                allUnitIds.add(AppConstants.EMP_MEAL_UNIT_ID);

                Map<Integer, PriceProfileKey> unitPriceProfileKeyMap = getUnitPriceProfileMap(allUnitIds, brandId);

                List<UnitProductMapping> unitProductMappings = productCheckListUtil.getUnitProductMapping
                        (productCheckListItem.getProduct(), allUnitIds);

                Map<Integer, String> unitToStatusMap = new HashMap<>();
                Map<Integer, String> unitPricingStatusMap = new HashMap<>();
                Map<Integer, String> unitRecipeStatusMap = new HashMap<>();

                createUnitSpecificMaps(unitProductMappings, unitToStatusMap, unitPricingStatusMap, unitRecipeStatusMap, dimensionId);

                // Getting Image Mappings Independent of Unit
                Pair<String, String> statusReasonPairForImageMappingCOD = productCheckListUtil.checkForImageMappings(productId,
                        AppConstants.COD_IMAGE_TYPES_FOR_CHECKLIST);
                Pair<String, String> statusReasonPairForImageMappingDineIn = productCheckListUtil.checkForImageMappings(productId,
                        AppConstants.DINE_IN_IMAGE_TYPES_FOR_CHECKLIST);

                for (IdCodeName unit : units) {
                    ProductCheckListExcelSheet productCheckListExcelSheetRow = new ProductCheckListExcelSheet();

                    // Setting Basic Details of Check List Item for each Unit
                    productCheckListExcelSheetRow.setProductId(productId.toString());
                    productCheckListExcelSheetRow.setProductName(productCheckListItem.getProduct().getName());
                    productCheckListExcelSheetRow.setDimension(productCheckListItem.getDimension().getName());
                    productCheckListExcelSheetRow.setUnitName(unit.getName());
                    productCheckListExcelSheetRow.setBrandId(productCheckListItem.getBrandId().toString());
                    productCheckListExcelSheetRow.setLiveDate(productCheckListItem.getLiveDate().toString());

                    // Below are certain number of checks that will be added to the corresponding headers in Excel Sheet with values Y / N

                    // 1. Check for Product Creation
                    productCheckListExcelSheetRow.setProductStatus(productCheckListUtil.checkForProductStatus
                            (productCheckListItem.getProduct()));

                    // 2. Check for Unit Product Mapping
                    productCheckListExcelSheetRow.setUnitProductMapping(productCheckListUtil.checkForUnitProductMapping(
                            unitToStatusMap.get(unit.getId())));

                    // 3. Check for Unit Product Pricing
                    productCheckListExcelSheetRow.setUnitProductPricing(unitPricingStatusMap.get(unit.getId()));

                    // 4. COD to Product Mappings and COD to Product Price Mappings
                    List<UnitPartnerBrandKey> partnerBrandMappings = brandPartnerMappingByUnit.get(unit.getId());
                    Map<Integer, Map<MenuType, Integer>> menuSequenceIdsByPartnerAndSlot = new HashMap<>();
                    Set<Integer> menuSequenceIds = new HashSet<>();

                    if (!CollectionUtils.isEmpty(partnerBrandMappings)) {
                        List<UnitPartnerBrandKey> formattedCodMappings = productCheckListUtil.convert(AppConstants.COD_MAPPINGS_FOR_CHECKLIST,
                                unit.getId(), brandId);
                        List<UnitPartnerBrandKey> excludedPartners = formattedCodMappings.stream().
                                filter(item -> !partnerBrandMappings.contains(item)).toList();

                        processPartnerBrandMappingsForIncludedPartners(partnerBrandMappings, productCheckListExcelSheetRow,
                                unitToStatusMap, unitPricingStatusMap, menuMappings, menuSequenceIdsByPartnerAndSlot, menuSequenceIds,
                                unitPriceProfileKeyMap, priceProfileProductMap, productDimensionKey);
                        processPartnerBrandMappingsForExcludedPartners(excludedPartners, productCheckListExcelSheetRow,
                                unitToStatusMap, unitPricingStatusMap);
                    } else {
                        setCodMappingsAsNotApplicable(productCheckListExcelSheetRow);
                    }

                    // 5. Check For Menu Mapping
                    Map<Integer, Pair<String, String>> statusReasonMapByPartner;
                    if (!CollectionUtils.isEmpty(menuSequenceIdsByPartnerAndSlot)) {
                        statusReasonMapByPartner = productCheckListUtil.checkForMenuProductMapping
                                (menuSequenceIdsByPartnerAndSlot, productId, new ArrayList<>(menuSequenceIds));
                    } else {
                        statusReasonMapByPartner = new HashMap<>();
                    }

                    if (!CollectionUtils.isEmpty(statusReasonMapByPartner)) {
                        List<Integer> excludedPartnerIds = AppConstants.MENU_MAPPING_FOR_CHECKLIST.stream().
                                filter(id -> !statusReasonMapByPartner.containsKey(id)).toList();
                        for (Integer partnerId : statusReasonMapByPartner.keySet()) {
                            setMenuMappingsForChannelPartner(productCheckListExcelSheetRow, partnerId, statusReasonMapByPartner, Boolean.FALSE);
                        }
                        for (Integer partnerId : excludedPartnerIds) {
                            setMenuMappingsForChannelPartner(productCheckListExcelSheetRow, partnerId, statusReasonMapByPartner, Boolean.TRUE);
                        }
                    } else {
                        setMenuMappingsAsNotApplicable(productCheckListExcelSheetRow);
                    }

                    // 6. Check for Image Mapping
                    productCheckListExcelSheetRow.setImageMappingStatusCOD(statusReasonPairForImageMappingCOD.getKey());
                    productCheckListExcelSheetRow.setImageMappingReasonCOD(statusReasonPairForImageMappingCOD.getValue());

                    productCheckListExcelSheetRow.setImageMappingStatusDineIn(statusReasonPairForImageMappingDineIn.getKey());
                    productCheckListExcelSheetRow.setImageMappingReasonDineIn(statusReasonPairForImageMappingDineIn.getValue());

                    // 7. Check for Employee Meal Mapping
                    productCheckListExcelSheetRow.setEmployeeMealMapping(AppUtils.setStatus(AppUtils.getStatus(
                            unitPricingStatusMap.get(AppConstants.EMP_MEAL_UNIT_ID))));

                    // 8. Check for Recipe Live
                    String recipeProfile = unitRecipeStatusMap.get(unit.getId());
                    productCheckListExcelSheetRow.setRecipeLive(productCheckListUtil.checkForProductRecipeStatus(productId,
                            recipeStatuses, dimensionId, recipeProfile, productCheckListItem.getLiveDate()));
                    productCheckListUtil.checkForProductRecipeMisMatch(product,recipeStatuses, dimensionId, recipeProfile, productCheckListItem.getLiveDate()
                    ,productCheckListExcelSheetRow);

                    // 9. Check for Price Profile
                    productCheckListExcelSheetRow.setPriceProfileMappingDineIn(productCheckListUtil.checkForPriceProfileMapping(
                            unitPriceProfileKeyMap, unit.getId(),productDimensionKey,priceProfileProductMap));

                    // 10. Check for Monk Recipe Hot Beverage
                    productCheckListExcelSheetRow.setHotBeverageMonkRecipe(productCheckListUtil.checkForHotBeverageMonkRecipe(
                            productCheckListItem.getProduct(), productCheckListItem.getDimension(), AppConstants.ACTIVATED,
                            unitsBasicDetail.get(unit.getId()).getRegion()));

                    // Updating final status of mappings
                    productCheckListExcelSheetRow.updateOverAllMappingStatus(AppConstants.codMappings, "overallMappingStatusForCOD");
                    productCheckListExcelSheetRow.updateOverAllMappingStatus(AppConstants.dineInMappings, "overallMappingStatusForDineIn");

                    // Pushing Created Row in Sheet
                    productCheckListExcelSheetData.add(productCheckListExcelSheetRow);
                }
            }

            String pathName = masterProperties.getBasePath().concat("/product-checklist");
            String fileLink = buildExcelSheet(productCheckListExcelSheetData, pathName, checkListEvent.getEventId());

            if (Objects.nonNull(fileLink)) {
                checkListEvent.setFileLink(fileLink);
                checkListEvent.setCompletionTimeStamp(AppUtils.getCurrentTimestamp());
                checkListEvent.setStatus(AppConstants.PROCESSED);
            } else {
                checkListEvent.setStatus(AppConstants.FAILED);
            }

        } catch (Exception e) {
            checkListEvent.setStatus(AppConstants.FAILED);
            LOG.info("Failed to create product checklist with event id {}", checkListEvent.getEventId());
            LOG.info("Exception {}", e.toString());
        }
        masterDao.update(checkListEvent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductCheckListEvent getProductCheckListEvent(Integer eventId) {
        return productCheckListUtil.getProductCheckListEventById(eventId);
    }

    private Map<Integer, PriceProfileKey> getUnitPriceProfileMap(List<Integer> unitIds, Integer brandId) {
        return unitPriceProfileMappingDao.findAllMappingsByPartnerIdAndBrand(unitIds,1, 1)
                .stream()
                .collect(Collectors.toMap(UnitPriceProfileMapping:: getUnitId,
                        mapping -> new PriceProfileKey(mapping.getPriceProfileId(), mapping.getPriceProfileVersion()))
                );
    }

    private Map<Integer, Set<UnitChannelPartnerMapping>> getUnitChannelPartnerMap(List<Integer> unitIds) {
        return masterDataCache.getActiveUnitChannelPartnerMapping().stream()
                .filter(mapping -> unitIds.contains(mapping.getUnit().getId()))
                .collect(Collectors.groupingBy(ucmapping -> ucmapping.getUnit().getId(),
                Collectors.mapping(Function.identity(), Collectors.toSet())));
    }

    private void createBrandPartnerMappingByUnitMap(Map<Integer, Set<UnitChannelPartnerMapping>> unitChannelPartnerMappingMap,
                                                    Map<UnitPartnerBrandKey, UnitPartnerBrandMappingData> unitPartnerBrandMappingDataMap,
                                                    Set<Integer> pricingUnitIds, Integer brandId,
                                                    Map<Integer, List<UnitPartnerBrandKey>> brandPartnerMappingByUnit) {
        List<Integer> dineinPartnerIds = new ArrayList<>(Arrays.asList(AppConstants.DINE_IN_CHANNEL_PARTNER,
                AppConstants.CHANNEL_PARTNER_DINE_IN_APP, AppConstants.WEB_APP_CHANNEL_PARTNER_CODE));
        unitChannelPartnerMappingMap.keySet().forEach(unitId -> {
            unitChannelPartnerMappingMap.get(unitId).forEach(ucMapping -> {
                UnitPartnerBrandKey unitPartnerBrandKey = new UnitPartnerBrandKey(unitId, brandId,
                        ucMapping.getChannelPartner().getId());
                if(dineinPartnerIds.contains(ucMapping.getChannelPartner().getId())){
                    brandPartnerMappingByUnit.computeIfAbsent(unitId, uId -> new ArrayList<>());
                    brandPartnerMappingByUnit.get(unitId).add(unitPartnerBrandKey);
                }else  if(unitPartnerBrandMappingDataMap.containsKey(unitPartnerBrandKey)) {
                    UnitPartnerBrandMappingData unitPartnerBrandMappingData = unitPartnerBrandMappingDataMap.get(unitPartnerBrandKey);
                    pricingUnitIds.add(unitPartnerBrandMappingData.getPriceProfileUnitId());
                    brandPartnerMappingByUnit.computeIfAbsent(unitId, uId -> new ArrayList<>());
                    brandPartnerMappingByUnit.get(unitId).add(unitPartnerBrandKey);
                }
            });
        });
    }

    private void createUnitSpecificMaps(List<UnitProductMapping> unitProductMappings, Map<Integer, String> unitToStatusMap,
                                        Map<Integer, String> unitPricingStatusMap, Map<Integer, String> unitRecipeStatusMap,
                                        Integer dimensionId) {
        for (UnitProductMapping unitProductMapping : unitProductMappings) {
            unitToStatusMap.put(unitProductMapping.getUnitDetail().getUnitId(), unitProductMapping.getProductStatus());
            Optional<UnitProductPricing> unitPricingOptional =  unitProductMapping.getUnitProductPricings().stream()
                    .filter(pricing -> pricing.getRefLookup().getRlId().equals(dimensionId) &&
                            pricing.getStatus().equals(AppConstants.ACTIVE)).findAny();
            unitPricingStatusMap.put(unitProductMapping.getUnitDetail().getUnitId(),
                    AppUtils.setStatus(unitPricingOptional.isPresent()));
            unitRecipeStatusMap.put(unitProductMapping.getUnitDetail().getUnitId(),
                    unitPricingOptional.map(UnitProductPricing::getRecipeProfile).orElse(null));
        }
    }

    private void processPartnerBrandMappingsForIncludedPartners(List<UnitPartnerBrandKey> partnerBrandMappings,
                                                                ProductCheckListExcelSheet productCheckListExcelSheetRow,
                                                                Map<Integer, String> unitToStatusMap, Map<Integer, String> unitPricingStatusMap,
                                                                Map<UnitPartnerBrandKey, List<UnitPartnerMenuMapping>> menuMappings,
                                                                Map<Integer, Map<MenuType, Integer>> menuSequenceIdsByPartnerAndSlot,
                                                                Set<Integer> menuSequenceIds, Map<Integer, PriceProfileKey> unitPriceProfileKeyMap,
                                                                Map<PriceProfileKey, Map<ProductDimensionKey, BigDecimal>> priceProfileProductPriceMap,
                                                                ProductDimensionKey productDimensionKey) {
        for(UnitPartnerBrandKey partnerBrandKey : partnerBrandMappings) {
            if (Objects.nonNull(masterDataCache.getUnitPartnerBrandMappingMetaData().get(partnerBrandKey))) {
                Integer pricingUnitId = masterDataCache.getUnitPartnerBrandMappingMetaData().get(partnerBrandKey).getPriceProfileUnitId();
                String pricingStatus = productCheckListUtil.checkForPriceProfileMapping(unitPriceProfileKeyMap, pricingUnitId,
                        productDimensionKey, priceProfileProductPriceMap);
                setCodMappingsForChannelPartner(productCheckListExcelSheetRow, unitToStatusMap,
                        unitPricingStatusMap, partnerBrandKey.getPartnerId(), pricingUnitId, Boolean.FALSE, pricingStatus);
            }
            List<UnitPartnerMenuMapping> menuMappingsList = menuMappings.get(partnerBrandKey);
            if (!CollectionUtils.isEmpty(menuMappingsList)) {
                for (UnitPartnerMenuMapping menuMapping : menuMappingsList) {
                    menuSequenceIdsByPartnerAndSlot.computeIfAbsent(partnerBrandKey.getPartnerId(), pId -> new HashMap<>())
                            .put(menuMapping.getMenuType(), menuMapping.getMenuSequence().getId());
                    menuSequenceIds.add(menuMapping.getMenuSequence().getId());
                }
            }
        }
    }

    private void processPartnerBrandMappingsForExcludedPartners(List<UnitPartnerBrandKey> excludedPartners,
                                                                ProductCheckListExcelSheet productCheckListExcelSheetRow,
                                                                Map<Integer, String> unitToStatusMap,
                                                                Map<Integer, String> unitPricingStatusMap) {
        for (UnitPartnerBrandKey partnerBrandKey : excludedPartners) {
            setCodMappingsForChannelPartner(productCheckListExcelSheetRow, unitToStatusMap,
                    unitPricingStatusMap, partnerBrandKey.getPartnerId(), null, Boolean.TRUE, AppConstants.NOT_APPLICABLE);
        }
    }

    private void setCodMappingsForChannelPartner(ProductCheckListExcelSheet productCheckListExcelSheetRow,
                                                 Map<Integer, String> unitToStatusMap, Map<Integer, String> unitPricingStatusMap,
                                                 Integer partnerId, Integer pricingUnitId, Boolean isNotApplicable, String priceProfileStatus) {
        switch (partnerId) {
            case AppConstants.CHANNEL_PARTNER_ZOMATO:
                if (!isNotApplicable) {
                    productCheckListExcelSheetRow.setCodProductMappingZomato(productCheckListUtil.checkForUnitProductMapping
                            (unitToStatusMap.get(pricingUnitId)));
                    productCheckListExcelSheetRow.setCodProductPriceMappingZomato(unitPricingStatusMap.get(pricingUnitId));
                    productCheckListExcelSheetRow.setPriceProfileMappingZomato(priceProfileStatus);
                } else {
                    productCheckListExcelSheetRow.setCodProductMappingZomato(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setCodProductPriceMappingZomato(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setPriceProfileMappingZomato(priceProfileStatus);
                }
                break;
            case AppConstants.CHANNEL_PARTNER_SWIGGY:
                if (!isNotApplicable) {
                    productCheckListExcelSheetRow.setCodProductMappingSwiggy(productCheckListUtil.checkForUnitProductMapping
                            (unitToStatusMap.get(pricingUnitId)));
                    productCheckListExcelSheetRow.setCodProductPriceMappingSwiggy(unitPricingStatusMap.get(pricingUnitId));
                    productCheckListExcelSheetRow.setPriceProfileMappingSwiggy(priceProfileStatus);
                } else {
                    productCheckListExcelSheetRow.setCodProductMappingSwiggy(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setCodProductPriceMappingSwiggy(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setPriceProfileMappingSwiggy(priceProfileStatus);
                }
                break;
            case AppConstants.CHANNEL_PARTNER_MAGICPIN:
                if (!isNotApplicable) {
                    productCheckListExcelSheetRow.setCodProductMappingMagicPin(productCheckListUtil.checkForUnitProductMapping
                            (unitToStatusMap.get(pricingUnitId)));
                    productCheckListExcelSheetRow.setCodProductPriceMappingMagicPin(unitPricingStatusMap.get(pricingUnitId));
                    productCheckListExcelSheetRow.setPriceProfileMappingMagicPin(priceProfileStatus);

                } else {
                    productCheckListExcelSheetRow.setCodProductMappingMagicPin(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setCodProductPriceMappingMagicPin(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setPriceProfileMappingMagicPin(priceProfileStatus);
                }
                break;
            default:
                break;
        }
    }

    private void setCodMappingsAsNotApplicable(ProductCheckListExcelSheet productCheckListExcelSheetRow) {
        productCheckListExcelSheetRow.setCodProductMappingZomato(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setCodProductMappingSwiggy(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setCodProductMappingMagicPin(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setCodProductPriceMappingZomato(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setCodProductPriceMappingSwiggy(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setCodProductPriceMappingMagicPin(AppConstants.NOT_APPLICABLE);
    }

    private void setMenuMappingsForChannelPartner(ProductCheckListExcelSheet productCheckListExcelSheetRow,
                                                  Integer partnerId, Map<Integer, Pair<String, String>> statusReasonMapByPartner,
                                                  Boolean isNotApplicable) {
        String status = "";
        String reason = "";
        if (!isNotApplicable) {
            status = statusReasonMapByPartner.get(partnerId).getKey();
            reason = statusReasonMapByPartner.get(partnerId).getValue();
        }
        switch (partnerId) {
            case AppConstants.CHANNEL_PARTNER_ZOMATO:
                if (!isNotApplicable) {
                    productCheckListExcelSheetRow.setMenuMappingStatusZomato(status);
                    productCheckListExcelSheetRow.setMenuMappingReasonZomato(reason);
                } else {
                    productCheckListExcelSheetRow.setMenuMappingStatusZomato(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setMenuMappingReasonZomato(AppConstants.NOT_APPLICABLE);
                }
                break;

            case AppConstants.CHANNEL_PARTNER_SWIGGY:
                if (!isNotApplicable) {
                    productCheckListExcelSheetRow.setMenuMappingStatusSwiggy(status);
                    productCheckListExcelSheetRow.setMenuMappingReasonSwiggy(reason);
                } else {
                    productCheckListExcelSheetRow.setMenuMappingStatusSwiggy(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setMenuMappingReasonSwiggy(AppConstants.NOT_APPLICABLE);
                }
                break;

            case AppConstants.CHANNEL_PARTNER_MAGICPIN:
                if (!isNotApplicable) {
                    productCheckListExcelSheetRow.setMenuMappingStatusMagicPin(status);
                    productCheckListExcelSheetRow.setMenuMappingReasonMagicPin(reason);
                } else {
                    productCheckListExcelSheetRow.setMenuMappingStatusMagicPin(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setMenuMappingReasonMagicPin(AppConstants.NOT_APPLICABLE);
                }
                break;

            case AppConstants.CHANNEL_PARTNER_DINE_IN_APP:
                if (!isNotApplicable) {
                    productCheckListExcelSheetRow.setMenuMappingStatusDineInApp(status);
                    productCheckListExcelSheetRow.setMenuMappingReasonDineInApp(reason);
                } else {
                    productCheckListExcelSheetRow.setMenuMappingStatusDineInApp(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setMenuMappingReasonDineInApp(AppConstants.NOT_APPLICABLE);
                }
                break;

            case AppConstants.DINE_IN_CHANNEL_PARTNER:
                if (!isNotApplicable) {
                    productCheckListExcelSheetRow.setMenuMappingStatusChaayosDineIn(status);
                    productCheckListExcelSheetRow.setMenuMappingReasonChaayosDineIn(reason);
                } else {
                    productCheckListExcelSheetRow.setMenuMappingStatusChaayosDineIn(AppConstants.NOT_APPLICABLE);
                    productCheckListExcelSheetRow.setMenuMappingReasonChaayosDineIn(AppConstants.NOT_APPLICABLE);
                }
                break;

            default:
                break;
        }
    }

    private void setMenuMappingsAsNotApplicable(ProductCheckListExcelSheet productCheckListExcelSheetRow) {
        productCheckListExcelSheetRow.setMenuMappingStatusZomato(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingReasonZomato(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingStatusSwiggy(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingReasonSwiggy(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingStatusMagicPin(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingReasonMagicPin(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingStatusDineInApp(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingReasonDineInApp(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingStatusChaayosDineIn(AppConstants.NOT_APPLICABLE);
        productCheckListExcelSheetRow.setMenuMappingReasonChaayosDineIn(AppConstants.NOT_APPLICABLE);
    }

    private String buildExcelSheet(List<ProductCheckListExcelSheet> productCheckListExcelSheetData, String pathName, Integer eventId) {
        try (Workbook workbook = new XSSFWorkbook()) {
            String fileName = "/" + eventId.toString() + ".xlsx";

            ExcelWriter writer = new ExcelWriter(workbook);
            if (!CollectionUtils.isEmpty(productCheckListExcelSheetData)) {
                writer.writeSheet(productCheckListExcelSheetData, ProductCheckListExcelSheet.class);
            }

            File file = new File(pathName + fileName);
            try (FileOutputStream fileOut = new FileOutputStream(file)) {
                workbook.write(fileOut);
            }

            String fileLink = uploadProductCheckListToS3(file, eventId);
            Boolean isDeleted = file.delete();
            return fileLink;
        } catch (Exception e) {
            LOG.info("Error while building product checklist excel sheet with event ID : {}", eventId);
            return null;
        }
    }

    private String uploadProductCheckListToS3(File productCheckListExcelFile, Integer eventId) throws Exception {
        try {
            String baseDir = "master-service/product-checklist";
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, productCheckListExcelFile, Boolean.TRUE);
            System.out.println(s3File);
            return props.getCheckListUrl() + "product-checklist" + "/" + eventId + ".xlsx";
        } catch (Exception e) {
            LOG.info("Error while saving checklist to S3 with eventId {}", eventId);
            throw new Exception(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<MachineProductMetaDataResponse> getMachineProductMappingData(Integer unitId, Integer brandId) {
        List<Product> unitProductDetails = masterDataCache.getUnitProductDetails(unitId).stream().collect(toList());

        List<Integer> unitProductIds = unitProductDetails.stream().filter(product -> product.getBrandId().equals(brandId) &&
                        !product.getPrices().isEmpty()).map(Product::getId).toList();
        List<MachineProductMappingMetaData> machineProductMappingMetaDataList = masterDao.getMachineProductMappingData();
        List<Integer> mappedProductIds = new ArrayList<>();
        machineProductMappingMetaDataList.forEach(mpm -> {
            mappedProductIds.addAll(Arrays.stream(mpm.getProductIds().trim().split(",")).map(Integer :: parseInt).toList());
        });

        List<Integer> otherProductIds = new ArrayList<>();
        unitProductDetails.forEach(upd -> {
            if (upd.getBrandId().equals(brandId) && !upd.getPrices().isEmpty()) {
                if ("MENU".equals(upd.getClassification().value()) && upd.getType() != AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE &&
                        upd.getSubType() != AppConstants.GIFT_SUB_CATEGORY && !mappedProductIds.contains(upd.getId())) {
                    otherProductIds.add(upd.getId());
                }
            }
        });

        return filterMachineProductMappingData(unitProductDetails, unitProductIds, otherProductIds, machineProductMappingMetaDataList);
    }

    private List<MachineProductMetaDataResponse> filterMachineProductMappingData(List<Product> unitProductDetails,
                                                                                 List<Integer> pIds, List<Integer> otherProductIds,
                                                                                 List<MachineProductMappingMetaData> machineProductMappingMetaDataList) {
        List<MachineProductMetaDataResponse> responseObj = new ArrayList<>();

        machineProductMappingMetaDataList.forEach(mpm -> {
            List<Integer> productIds = Arrays.stream(mpm.getProductIds().split(",")).map(Integer :: parseInt).toList();
            productIds = productIds.stream().filter(pIds :: contains).toList();
            mpm.setProductIds(StringUtils.join(productIds, ","));
            List<Integer> finalProductIds = productIds;
            responseObj.add(createMachineProductResponse(finalProductIds, unitProductDetails, mpm.getMachineId(), mpm.getMachineName()));
        });

//        responseObj.add(createMachineProductResponse(otherProductIds, unitProductDetails, -1, "Others"));

        return responseObj;
    }

    private MachineProductMetaDataResponse createMachineProductResponse(List<Integer> productIds, List<Product> unitProductDetails,
                                                                        Integer machineId, String machineName) {
        List<IdCodeName> products = new ArrayList<>();
        unitProductDetails.forEach(product -> {
            if (productIds.contains(product.getId())) {
                IdCodeName productIdCodeName = new IdCodeName();
                productIdCodeName.setId(product.getId());
                productIdCodeName.setName(product.getName());
                productIdCodeName.setStatus(product.getStatus().value());
                productIdCodeName.setCode(product.getShortCode());
                products.add(productIdCodeName);
            }
        });

        MachineProductMetaDataResponse obj = new MachineProductMetaDataResponse();
        obj.setMachineId(machineId);
        obj.setMachineName(machineName);
        obj.setProducts(products);

        return obj;
    }

    private String getFailureUpdatedStr(UnitProductMappingData mapping, UnitProductUpdateType type) {
        return "[Failure] --> Unit Product Mapping Id : " + mapping.getId() + " and unit Id :" + mapping.getUnit().getId() +
                " and product Id : " + mapping.getProduct().getId() + " and dimension : " + mapping.getPrice().getDimension() +
                (UnitProductUpdateType.UNIT_PRODUCT_PRICING_PROFILE.equals(type) ?
                        (" and profile : " + mapping.getPrice().getProfile() + " and status : " + mapping.getPrice().getStatus() + " and delivery only : "
                                + mapping.getPrice().getIsDeliveryOnlyProduct()+ " and dine in consumables : "+ mapping.getPrice().getPickDineInConsumables()) :
                        (" and product alias : " + mapping.getPrice().getAliasProductName()) + " and dimension description : " + mapping.getPrice().getDimensionDescriptor()) + "  and ERROR MESSAGE :: ";
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void changeUnitProductProfile(UnitProductProfileContext context) throws MasterException {

        if(CollectionUtils.isEmpty(context.getMappings())) {
            throw new MasterException("Submitted mappings should not be empty");
        }
        LOG.info("Changing Unit Product Profile");

        // Initialize logging map
        context.getLogEntries().put("success", new ArrayList<>());
        context.getLogEntries().put("failure", new ArrayList<>());

        Set<Integer> upmIds = context.getMappings().stream()
                .map(UnitProductMappingData::getId)
                .filter(id -> id > 0).collect(Collectors.toSet());

        // Get all UPMs at once
        context.setUpmMap(dao.getAllUnitProductMappingsByIds(upmIds));
        context.setProductDetailMap(getAllProductsInMappings(context));

        for(UnitProductMappingData mapping : context.getMappings()) {
            try {
                if(mapping.getId() > 0) {
                    updateUnitProductPrice(context, mapping);
                } else {
                    if (CollectionUtils.isEmpty(context.getUnitDetailMap())) {
                        getAllUnitsAndDimensionsAtOnce(context);
                    }
                    addNewUnitProductMapping(context, mapping);
                }

                if(AppConstants.getValue(context.getProductDetailMap().get(mapping.getProduct().getId()).getIsInventoryTracked())) {
                    updateUnitProductPriceUpdateMap(mapping.getPrice(), mapping.getUnit().getId());
                }

                // Add to mail map
                ProductDetail product = context.getProductDetailMap().get(mapping.getProduct().getId());
                context.getMapForSendingMail().computeIfAbsent(product, upmData -> new ArrayList<>()).add(mapping);
            } catch (Exception exp) {
                context.getLogEntries().get("failure").add(getFailureUpdatedStr(mapping, context.getPageType()) + exp.getMessage());
                LOG.error("Error while updating Unit Product Mapping and Price ", exp);
            }
        }

        if(context.getInsertPricingList().size() > 0) {
            dao.batchInsert(context.getInsertPricingList());
        }

        if(context.getUpdatePricingList().size() > 0) {
            dao.batchUpdate(context.getUpdatePricingList());
        }

        // send failure logs to mail
        if( !CollectionUtils.isEmpty(context.getLogEntries().get("failure")) ) {
            try {
                sendLogsEmail(context.getLogEntries(), "Logs for product profile data change");
            } catch (Exception exp) {
                LOG.error("Error while sending logs to mail for product profile data update", exp);
            }
        }

        try {
            sendProductProfileDataChangeMail(context);
        } catch (Exception exp) {
            LOG.error("Error while sending mail for product profile data change", exp);
        }
    }

    private Map<Integer, ProductDetail> getAllProductsInMappings(UnitProductProfileContext context) {
        Set<Integer> productIds = context.getMappings().stream()
                .map(mapping -> mapping.getProduct().getId())
                .filter(id -> id > 0)
                .collect(Collectors.toSet());

        return dao.getProductsByIds(new ArrayList<>(productIds))
                .stream().collect(Collectors.toMap(ProductDetail::getProductId, pro -> pro));
    }

    private void getAllUnitsAndDimensionsAtOnce(UnitProductProfileContext context) {
        Set<Integer> unitIds = context.getMappings().stream()
                .map(mapping -> mapping.getUnit().getId())
                .filter(id -> id > 0)
                .collect(Collectors.toSet());

        Set<Integer> dimensionIds = context.getMappings().stream()
                .map(mapping -> mapping.getDimension().getId())
                .filter(id -> id > 0)
                .collect(Collectors.toSet());

        context.setUnitDetailMap(
                dao.getUnitsByIds(new ArrayList<>(unitIds))
                        .stream().collect(Collectors.toMap(UnitDetail::getUnitId, unit -> unit))
        );

        context.setDimensionMap(dao.getRefLookUpByIds(new ArrayList<>(dimensionIds)));
    }

    private void updateUnitProductPrice(UnitProductProfileContext context, UnitProductMappingData mapping) {
        UnitProductMapping unitProductMapping = context.getUpmMap().get(mapping.getId());
        if(unitProductMapping == null) {
            unitProductMapping = dao.find(UnitProductMapping.class, mapping.getId());
        }

        unitProductMapping.setProductStatus(ProductStatus.ACTIVE.name());
        unitProductMapping = dao.update(unitProductMapping);
        UnitProductPricing pricing = unitProductMapping.getUnitProductPricings()
                .stream()
                .filter(p -> p.getUnitProdPriceId() == mapping.getPrice().getId())
                .findFirst()
                .orElse(null);

        if(pricing != null) {
            if(UnitProductUpdateType.UNIT_PRODUCT_PRICING_PROFILE.equals(context.getPageType())) {
                pricing.setRecipeProfile(mapping.getPrice().getProfile());
                pricing.setIsDeliveryOnlyProduct(AppUtils.setStatus(mapping.getPrice().getIsDeliveryOnlyProduct()));
                pricing.setPickDineInConsumables(AppUtils.setStatus(mapping.getPrice().getPickDineInConsumables()));
                pricing.setStatus(mapping.getPrice().getStatus());
            } else {
                pricing.setDimensionDescriptor(mapping.getPrice().getDimensionDescriptor());
                pricing.setAliasProductName(mapping.getPrice().getAliasProductName());
            }
            pricing.setUpdatedBy(context.getLoggedInUser());
            pricing.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());

            context.getUpdatePricingList().add(pricing);
//            dao.add(pricing);
        } else {
            addNewProductPriceMapping(context, unitProductMapping, mapping);
        }
    }

    private void addNewUnitProductMapping(UnitProductProfileContext context, UnitProductMappingData mapping) {
        Integer productId = mapping.getProduct().getId();
        Integer unitId = mapping.getUnit().getId();
        UnitProductMapping newMapping = dao.isUnitProductMappingExists(productId, unitId);

        ProductDetail productDetail = context.getProductDetailMap().containsKey(productId) ?
                context.getProductDetailMap().get(productId) :
                dao.getProductsByIds(List.of(productId)).get(0);

        newMapping.setProductDetail(productDetail);

        if(context.getUnitDetailMap().containsKey(unitId)) {
            newMapping.setUnitDetail(context.getUnitDetailMap().get(unitId));
        } else {
            newMapping.setUnitDetail(dao.getUnitsByIds(List.of(unitId)).get(0));
        }

        newMapping.setProductStartDate(productDetail.getProductStartDate());
        newMapping.setProductEndDate(productDetail.getProductEndDate());
        newMapping.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
        newMapping.setProductStatus(ProductStatus.ACTIVE.name());
        dao.add(newMapping);

        addNewProductPriceMapping(context, newMapping, mapping);
    }

    private void addNewProductPriceMapping(UnitProductProfileContext context, UnitProductMapping unitProductMapping, UnitProductMappingData mapping) {
        UnitProductPricing entity = new UnitProductPricing();
        entity.setUnitProductMapping(unitProductMapping);
        entity.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
        entity.setUpdatedBy(context.getLoggedInUser());

        if(context.getDimensionMap().containsKey(mapping.getDimension().getId())) {
            entity.setRefLookup(context.getDimensionMap().get(mapping.getDimension().getId()));
        } else {
            entity.setRefLookup(dao.getRefLookUp(mapping.getDimension().getId()));
        }

        if(UnitProductUpdateType.UNIT_PRODUCT_PRICING_PROFILE.equals(context.getPageType())) {
            entity.setRecipeProfile(mapping.getPrice().getProfile());
            entity.setStatus(mapping.getPrice().getStatus());
            entity.setIsDeliveryOnlyProduct(AppUtils.setStatus(mapping.getPrice().getIsDeliveryOnlyProduct()));
            entity.setPickDineInConsumables(AppUtils.setStatus(mapping.getPrice().getPickDineInConsumables()));
        } else {
            entity.setAliasProductName(mapping.getPrice().getAliasProductName());
            entity.setDimensionDescriptor(mapping.getPrice().getDimensionDescriptor());
        }
        context.getInsertPricingList().add(entity);
//        dao.add(entity);
    }

    private void sendProductProfileDataChangeMail(UnitProductProfileContext context) {
        String toEmail = MasterUtil.getEmailIdOfEmployee(null);
        try {
            if(( !CollectionUtils.isEmpty(context.getMapForSendingMail()) || !CollectionUtils.isEmpty(context.getMapForSendingMailForBulkUpload()) ) && Objects.nonNull(context.getLoggedInUser())) {
                boolean isProfileChange = UnitProductUpdateType.UNIT_PRODUCT_PRICING_PROFILE.equals(context.getPageType());
                ProductProfileDataChangeEmailTemplate template = new ProductProfileDataChangeEmailTemplate(
                        props.getBasePath(), context.getMapForSendingMail(), context.getMapForSendingMailForBulkUpload(), context.getLoggedInUser(), isProfileChange);
                ProductProfileDataChange productProfileDataChange = new ProductProfileDataChange(
                        props.getEnvironmentType(), toEmail, template);
                productProfileDataChange.sendEmail();
            }
        } catch (Exception e) {
            LOG.info("Error while Sending Email For product price Update ::::: {}", e.getMessage());
            LOG.info("EXCEPTION : " , e);
        }
    }

    @SneakyThrows
    private void sendLogsEmail(Map<String, List<String>> logEntries, String subject) {
        String body = generateLogEmailBody(logEntries);
        String toEmail = MasterUtil.getEmailIdOfEmployee(null);
        String fromEmail = AppConstants.REPORTING_EMAIL;
        new EmailNotificationTemplate(subject, body, new String[]{Objects.requireNonNullElse(toEmail, AppConstants.TECHNOLOGY_EMAIL), AppConstants.TECHNOLOGY_EMAIL}, props.getEnvironmentType(), fromEmail).sendEmail();

    }

    private String generateLogEmailBody(Map<String, List<String>> logEntries) {
        StringBuilder htmlBody = new StringBuilder();

        htmlBody.append("<!DOCTYPE html>\n")
                .append("<html>\n")
                .append("<head>\n")
                .append("    <style>\n")
                .append("        body { font-family: Arial, sans-serif; }\n")
                .append("        .container { max-width: 800px; margin: 0 auto; padding: 20px; }\n")
                .append("        .success-section { background-color: #dff0d8; padding: 15px; border-radius: 5px; margin-bottom: 20px; }\n")
                .append("        .failure-section { background-color: #f2dede; padding: 15px; border-radius: 5px; }\n")
                .append("        .section-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }\n")
                .append("        .success-title { color: #3c763d; }\n")
                .append("        .failure-title { color: #a94442; }\n")
                .append("        .log-entry { margin: 5px 0; }\n")
                .append("        .log-count { margin-bottom: 10px; font-style: italic; }\n")
                .append("    </style>\n")
                .append("</head>\n")
                .append("<body>\n")
                .append("    <div class=\"container\">\n");

        List<String> failureEntries = logEntries.get("failure");
        if (failureEntries != null && !failureEntries.isEmpty()) {
            htmlBody.append("        <div class=\"failure-section\">\n")
                    .append("            <div class=\"section-title failure-title\">\n")
                    .append("                Failure <span class=\"section-title\">(")
                    .append(failureEntries.size())
                    .append(" failed operations)</span>\n")
                    .append("            </div>\n");

            for (String entry : failureEntries) {
                htmlBody.append("            <div class=\"log-entry\">").append(entry).append("</div>\n");
            }

            htmlBody.append("        </div>\n");
        }


        htmlBody.append("    </div>\n")
                .append("</body>\n")
                .append("</html>");

        return htmlBody.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void getUnitProductMappingToEmail(ProductRecipeMappingRequest request) throws Exception {
        ApiResponse response = getUnitProductMapping(request);
        if (response.getData() != null) {
            List<String> headerNames = List.of("UNIT_ID", "UNIT_NAME", "PRODUCT_ID", "PRODUCT_NAME", "DIMENSION_ID",
                    "DIMENSION_CODE", "OLD_PROFILE", "NEW_PROFILE", "OLD_STATUS", "NEW_STATUS",
                    "OLD_DELIVERY_ONLY", "NEW_DELIVERY_ONLY", "OLD_PICK_DINE_IN_CONSUMABLES", "NEW_PICK_DINE_IN_CONSUMABLES",
                    "OLD_PRODUCT_ALIAS", "NEW_PRODUCT_ALIAS", "OLD_DIMENSION_DESC", "NEW_DIMENSION_DESC");

            Collection<UnitProductMappingData> collection = (Collection<UnitProductMappingData>) response.getData();
            List<UnitProductMappingData> unitProductMappingDataList = new ArrayList<>(collection);


            ExcelRequestData excelRequestData = new ExcelRequestData();
            List<Object[]> excelDataList = new ArrayList<>();
            for (UnitProductMappingData unitProductMappingData : unitProductMappingDataList) {
                Object[] row = new Object[]{
                        unitProductMappingData.getUnit().getId(),
                        unitProductMappingData.getUnit().getName(),
                        unitProductMappingData.getProduct().getId(),
                        unitProductMappingData.getProduct().getName(),
                        unitProductMappingData.getDimension().getId(),
                        unitProductMappingData.getDimension().getCode(),
                        unitProductMappingData.getPrice().getProfile(),
                        unitProductMappingData.getPrice().getProfile(),
                        unitProductMappingData.getPrice().getStatus(),
                        unitProductMappingData.getPrice().getStatus(),
                        AppUtils.convertBooleanFlagToString(unitProductMappingData.getPrice().getIsDeliveryOnlyProduct()),
                        AppUtils.convertBooleanFlagToString(unitProductMappingData.getPrice().getIsDeliveryOnlyProduct()),
                        AppUtils.convertBooleanFlagToString(unitProductMappingData.getPrice().getPickDineInConsumables()),
                        AppUtils.convertBooleanFlagToString(unitProductMappingData.getPrice().getPickDineInConsumables()),
                        unitProductMappingData.getPrice().getAliasProductName(),
                        unitProductMappingData.getPrice().getAliasProductName(),
                        unitProductMappingData.getPrice().getDimensionDescriptor(),
                        unitProductMappingData.getPrice().getDimensionDescriptor()
                };

                excelDataList.add(row);
            }

            excelRequestData.setFileName("UNIT_PRODUCT_MAPPING_" + AppUtils.getCurrentTimestamp());
            excelRequestData.setHeaderNames(headerNames);
            excelRequestData.setBody(excelDataList);
            excelRequestData.setNonEditableColumnNames(List.of("UNIT_ID", "PRODUCT_ID", "DIMENSION_ID", "OLD_PROFILE", "OLD_STATUS", "OLD_DELIVERY_ONLY", "OLD_PICK_DINE_IN_CONSUMABLES", "OLD_PRODUCT_ALIAS", "OLD_DIMENSION_DESC"));

            View excelView = genericExcelManagementService.downloadExcelFromRequestData(excelRequestData);

            // send Email
            File file = MasterUtil.convertExcelViewToFile(excelView, "UNIT_PRODUCT_MAPPING");
            String[] toEmail = { Objects.requireNonNullElse(MasterUtil.getEmailIdOfEmployee(null), AppConstants.TECHNOLOGY_EMAIL), AppConstants.TECHNOLOGY_EMAIL };
            EmailNotificationTemplate emailTemplate = new EmailNotificationTemplate(
                    "[ Unit product mappings ]", "This file generated with options that you have filtered", toEmail, props.getEnvironmentType(), AppConstants.REPORTING_EMAIL
            );
            List<AttachmentData> attachments = new ArrayList<>();
            AttachmentData attachmentData = new AttachmentData();
            attachmentData.setAttachment(IOUtils.toByteArray(new FileInputStream(file)));
            attachmentData.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            attachmentData.setFileName(file.getName());
            attachments.add(attachmentData);
            emailTemplate.sendRawMail(attachments);
        }
    }

    @Override
    @SneakyThrows
    public void sendFailureEmail(String subject, String body) {
        subject = Objects.requireNonNullElse(subject, "[ ERROR getting Unit Product mappings ]");
        body = Objects.requireNonNullElse(body, "An unexpected error occurred while processing your request to get excel file for UNIT PRODUCT MAPPINGS");
        String[] toEmail = {Objects.requireNonNullElse(MasterUtil.getEmailIdOfEmployee(null), AppConstants.TECHNOLOGY_EMAIL), AppConstants.TECHNOLOGY_EMAIL};
        EmailNotificationTemplate emailTemplate = new EmailNotificationTemplate(
           subject, body, toEmail, props.getEnvironmentType(), AppConstants.REPORTING_EMAIL
        );
        emailTemplate.sendEmail();
    }

    private void updateUnitProductPriceUpdateMap(ProductPrice pricing, Integer unitId) {
        try {
            if(pricing.getCurrentProfile().compareTo(pricing.getProfile()) != 0) {
                masterCacheService.updatePriceProfileStateMapForUnit(unitId, true);
            }
        } catch (Exception e) {
            LOG.error("Error While Updating Unit Product Price State Map : " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void bulkUpdateUnitProductProfile(UnitProductProfileContext context) throws MasterException, DocumentException, IOException {
        if(CollectionUtils.isEmpty(context.getExcelData())) {
            throw new MasterException("Empty data found to update in bulk");
        }
        LOG.info("Bulk updating unit product mappings");

        // Initialize logging data
        context.getLogEntries().put("success", new ArrayList<>());
        context.getLogEntries().put("failure", new ArrayList<>());
        context.setBulkUploadData(new BulkUploadData());
        context.setBulkUploadDataLogList(new ArrayList<>());

        // upload excel to s3 in asynchronous
        CompletableFuture<DocumentDetailDTO> futureDoc = uploadFileInS3(context.getFile(), context.getLoggedInUser());

        getAllUnitDimensionAndProductMaps(context);

        addBulkUploadData(context);

        try {
            updateUnitProductProfilesFromExcelData(context);
            updateBulkUploadDataAndLogs(context, futureDoc);
        } catch (Exception e) {
            LOG.error("Error while processing bulk update: ", e);
            context.getLogEntries().get("failure").add("Error while processing bulk update: " + e.getMessage());
        }

        if(context.getInsertPricingList().size() > 0) {
            dao.batchInsert(context.getInsertPricingList());
        }

        if(context.getUpdatePricingList().size() > 0) {
            dao.batchUpdate(context.getUpdatePricingList());
        }

        if(context.getBulkUploadDataLogList().size() > 0) {
            dao.batchInsert(context.getBulkUploadDataLogList());
        }

        // send failure logs to mail
        if( !CollectionUtils.isEmpty(context.getLogEntries().get("failure")) ) {
            try {
                sendLogsEmail(context.getLogEntries(), "Logs of bulk upload for product profile data change");
            } catch (Exception exp) {
                LOG.error("Error while sending logs to mail for product profile data change", exp);
            }
        }

        try {
            sendProductProfileDataChangeMail(context);
        } catch (Exception exp) {
            LOG.error("Error while sending logs to mail for product profile data change", exp);
        }

    }

    private void updateUnitProductProfilesFromExcelData(UnitProductProfileContext context) throws MasterException {
        for(UnitProductProfileExcelData data : context.getExcelData()) {
            Integer unitId = data.getUnitId() >= 0 ? data.getUnitId() : null;
            Integer productId = data.getProductId() >= 0 ? data.getProductId() : null;
            Integer dimensionId = data.getDimensionId() >= 0 ? data.getDimensionId() : null;

            if (validateIds(unitId, productId, dimensionId, context)) {
                continue;
            }

            String key = unitId + "_" + productId;
            String recipeKey = productId + "_" + dimensionId;

            List<RecipeDetail> recipeDetails = new ArrayList<>();

            context.getRecipeDetailMap().computeIfAbsent(recipeKey, k -> recipeDao.findByProductIdsAndDimensions(List.of(productId), List.of(dimensionId), AppConstants.ACTIVE));

            recipeDetails = context.getRecipeDetailMap().get(recipeKey);
            List<String> recipes = new ArrayList<>();
            recipeDetails.forEach(recipe -> {
                recipes.add(recipe.getProfile());
            });
            if(CollectionUtils.isEmpty(recipes)) {
                context.getLogEntries().get("failure").add(context.getLogEntries().get("failure").size() + ") No Recipe Profile mapped for this unit Id " + unitId + ", product Id : " + productId + ", dimension Id : " + dimensionId);
                continue;
            }

            ProductDetail product = context.getProductDetailMap().get(productId);

            Set<Integer> refLookUpIds = product.getDimensionCode().getRefLookups().stream()
                    .map(RefLookup::getRlId).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(refLookUpIds) || !refLookUpIds.contains(dimensionId)) {
                context.getLogEntries().get("failure").add(context.getLogEntries().get("failure").size() + ") Dimension not exists for this product Id : " + productId + " dimension Id : " + dimensionId);
                continue;
            }
            UnitProductMapping mapping = context.getUnitProductMappingMap().get(key);
            try {
                if(Objects.isNull(mapping)) {
                    if(StringUtils.isBlank(data.getNewRecipeProfile())) {
                        context.getLogEntries().get("failure").add(context.getLogEntries().get("failure").size() + ") Recipe Profile should not be null or empty for new price mappings for " + productId + " dimension Id : " + dimensionId);
                        continue;
                    }
                    if( !recipes.contains(data.getNewRecipeProfile().toUpperCase()) ) {
                        context.getLogEntries().get("failure").add(context.getLogEntries().get("failure").size() + ") Recipe Profile is not mapped for this product and dimension " + productId + " dimension Id : " + dimensionId);
                        continue;
                    }
                    addNewUnitProductMapping(context, data);
                } else {
                    updateUnitProductPricing(context, data, mapping, recipes);
                }

                context.getMapForSendingMailForBulkUpload().computeIfAbsent(product, upmData -> new ArrayList<>()).add(data);

//                context.getLogEntries().get("success").add(context.getLogEntries().get("success").size() + ") SuccessFully updated record for unit: " + unitId + ", product: " + productId + ", dimension: " + dimensionId);
            } catch (Exception exp) {
                LOG.error("Error while updating unit product mapping in bulk ", exp);
                context.getLogEntries().get("failure").add(context.getLogEntries().get("failure").size() + ") Error processing unit: " + unitId + ", product: " + productId + ". Error: " + exp.getMessage());
            }
        }
    }

    private boolean validateIds(Integer unitId, Integer productId, Integer dimensionId, UnitProductProfileContext context) {
        if(Objects.isNull(unitId) || Objects.isNull(context.getUnitDetailMap().get(unitId))) {
            context.getLogEntries().get("failure").add(context.getLogEntries().get("failure").size() + ") Unit Id should not be null or 0 unit Id : " + unitId + " and product Id  : " + productId + " and dimension Id : " + dimensionId);
            return true;
        } else if(Objects.isNull(productId) || Objects.isNull(context.getProductDetailMap().get(productId))) {
            context.getLogEntries().get("failure").add(context.getLogEntries().get("failure").size() + ") Product Id should not be null or 0 product Id : " + productId + " and unit Id : " + unitId + " and dimension Id : " + dimensionId);
            return true;
        } else if(Objects.isNull(dimensionId) || Objects.isNull(context.getDimensionMap().get(dimensionId))) {
            context.getLogEntries().get("failure").add(context.getLogEntries().get("failure").size() + ") Dimension Id should not be null or 0 dimension Id : " + dimensionId + " and unit Id : " + unitId + " and product Id : " + productId);
            return true;
        }
        return false;
    }

    private void updateUnitProductPricing(UnitProductProfileContext context, UnitProductProfileExcelData data, UnitProductMapping mapping, List<String> recipes) {
        RefLookup refLookup = context.getDimensionMap().get(data.getDimensionId());
        mapping.setProductStatus(ProductStatus.ACTIVE.name());

        UnitProductPricing pricing = mapping.getUnitProductPricings()
                .stream()
                .filter(p -> Objects.equals(p.getRefLookup().getRlId(), refLookup.getRlId()))
                .findFirst()
                .orElse(null);

        if(pricing == null) {
            if(StringUtils.isBlank(data.getNewRecipeProfile()) || !recipes.contains(data.getNewRecipeProfile())) {
                LOG.info("Profile was null or empty or not mapped for adding new price profile mapping");
                return;
            }
            dao.add(mapping);
            addNewProductPriceMapping(context, mapping, data, refLookup);
            return;
        }

        // Update fields based on page type
        String oldRecipeProfile = pricing.getRecipeProfile();
        String oldStatus = pricing.getStatus();
        String oldDeliveryOnly = pricing.getIsDeliveryOnlyProduct();
        String oldPickDineInConsumables = pricing.getPickDineInConsumables();
        String oldDimensionDesc = pricing.getDimensionDescriptor();
        String oldProductAlias = pricing.getAliasProductName();

        if (UnitProductUpdateType.UNIT_PRODUCT_PRICING_PROFILE.equals(context.getPageType())) {
            if (StringUtils.isNotBlank(data.getNewRecipeProfile()) && !data.getNewRecipeProfile().equalsIgnoreCase(oldRecipeProfile) && recipes.contains(data.getNewRecipeProfile())) {
                pricing.setRecipeProfile(data.getNewRecipeProfile().toUpperCase());
            }
            if (StringUtils.isNotBlank(data.getNewStatus()) && !data.getNewStatus().equalsIgnoreCase(oldStatus)) {
                pricing.setStatus(AppUtils.getCorrectStatus(data.getNewStatus()));
            }
            if (StringUtils.isNotBlank(data.getNewDeliveryOnly()) && !data.getNewDeliveryOnly().equalsIgnoreCase(oldDeliveryOnly)) {
                pricing.setIsDeliveryOnlyProduct(AppUtils.convertYesToY(data.getNewDeliveryOnly()));
            }
            if (StringUtils.isNotBlank(data.getNewPickDineInConsumables()) && !data.getNewPickDineInConsumables().equalsIgnoreCase(oldPickDineInConsumables)) {
                pricing.setPickDineInConsumables(AppUtils.convertYesToY(data.getNewPickDineInConsumables()));
            }
        } else {
            if (StringUtils.isBlank(data.getNewDimensionDesc()) || !data.getNewDimensionDesc().equalsIgnoreCase(oldDimensionDesc)) {
                pricing.setDimensionDescriptor(AppUtils.getFormattedString(data.getNewDimensionDesc()));
            }
            if (StringUtils.isBlank(data.getNewProductAlias()) || !data.getNewProductAlias().equalsIgnoreCase(oldProductAlias)) {
                pricing.setAliasProductName(AppUtils.getFormattedString(data.getNewProductAlias()));
            }
        }

        ProductDetail prod = mapping.getProductDetail();
        if(prod != null && AppConstants.getValue(prod.getIsInventoryTracked())) {
            ProductPrice price = new ProductPrice();
            price.setProfile(pricing.getRecipeProfile());
            price.setCurrentProfile(oldRecipeProfile);
            updateUnitProductPriceUpdateMap(price, mapping.getUnitDetail().getUnitId());
        }

        dao.add(mapping);
        dao.add(pricing);

        if (UnitProductUpdateType.UNIT_PRODUCT_PRICING_PROFILE.equals(context.getPageType())) {
            if (StringUtils.isNotBlank(data.getNewRecipeProfile()) && !data.getNewRecipeProfile().equalsIgnoreCase(oldRecipeProfile) && recipes.contains(data.getNewRecipeProfile())) {
                context.getBulkUploadDataLogList().add(addLogForBulkUpload(oldRecipeProfile, pricing.getRecipeProfile(), "Recipe profile Entry updated in unit product price profile mapping", BulkUploadStatus.SUCCESS, pricing.getUnitProdPriceId(), BulkUploadFieldType.RECIPE_PROFILE));
            }
            if (StringUtils.isNotBlank(data.getNewStatus()) && !data.getNewStatus().equalsIgnoreCase(oldStatus)) {
                context.getBulkUploadDataLogList().add(addLogForBulkUpload(oldStatus, pricing.getStatus(), "Status updated in unit product price profile mapping", BulkUploadStatus.SUCCESS, pricing.getUnitProdPriceId(), BulkUploadFieldType.PRICING_STATUS));
            }
            if (StringUtils.isNotBlank(data.getNewDeliveryOnly()) && !AppUtils.convertYesToY(data.getNewDeliveryOnly()).equalsIgnoreCase(oldDeliveryOnly)) {
                context.getBulkUploadDataLogList().add(addLogForBulkUpload(oldDeliveryOnly, pricing.getIsDeliveryOnlyProduct(), "Delivery only flag updated in unit product price profile mapping", BulkUploadStatus.SUCCESS, pricing.getUnitProdPriceId(), BulkUploadFieldType.IS_DELIVERY_ONLY));
            }
            if (StringUtils.isNotBlank(data.getNewPickDineInConsumables()) && !AppUtils.convertYesToY(data.getNewPickDineInConsumables()).equalsIgnoreCase(oldPickDineInConsumables)) {
                context.getBulkUploadDataLogList().add(addLogForBulkUpload(oldPickDineInConsumables, pricing.getPickDineInConsumables(), "Dine-in consumables flag updated in unit product price profile mapping", BulkUploadStatus.SUCCESS, pricing.getUnitProdPriceId(), BulkUploadFieldType.PICK_DINE_IN_CONSUMABLES));
            }
        } else {
            if (StringUtils.isBlank(data.getNewDimensionDesc()) || !data.getNewDimensionDesc().equalsIgnoreCase(oldDimensionDesc)) {
                context.getBulkUploadDataLogList().add(addLogForBulkUpload(oldDimensionDesc, pricing.getDimensionDescriptor(), "Dimension descriptor updated in unit product price profile mapping", BulkUploadStatus.SUCCESS, pricing.getUnitProdPriceId(), BulkUploadFieldType.DIMENSION_DESC));
            }
            if (StringUtils.isBlank(data.getNewProductAlias()) || !data.getNewProductAlias().equalsIgnoreCase(oldProductAlias)) {
                context.getBulkUploadDataLogList().add(addLogForBulkUpload(oldProductAlias, pricing.getAliasProductName(), "Product alias updated in unit product price profile mapping", BulkUploadStatus.SUCCESS, pricing.getUnitProdPriceId(), BulkUploadFieldType.PRODUCT_ALIAS));
            }
        }
    }

    private void addNewUnitProductMapping(UnitProductProfileContext context, UnitProductProfileExcelData data) {
        UnitDetail unitDetail = context.getUnitDetailMap().get(data.getUnitId());
        ProductDetail product = context.getProductDetailMap().get(data.getProductId());
        RefLookup refLookup = context.getDimensionMap().get(data.getDimensionId());

        UnitProductMapping newMapping = new UnitProductMapping();
        newMapping.setProductDetail(product);
        newMapping.setUnitDetail(unitDetail);
        newMapping.setProductStatus(ProductStatus.ACTIVE.name());
        newMapping.setProductStartDate(product.getProductStartDate());
        newMapping.setProductEndDate(product.getProductEndDate());
        newMapping.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
        dao.add(newMapping);

        String key = unitDetail.getUnitId() + "_" + product.getProductId();
        context.getUnitProductMappingMap().put(key, newMapping);

        addNewProductPriceMapping(context, newMapping, data, refLookup);
    }

    private void addNewProductPriceMapping(UnitProductProfileContext context, UnitProductMapping newMapping,
                                           UnitProductProfileExcelData data, RefLookup refLookup) {
        UnitProductPricing newPricing = new UnitProductPricing();
        newPricing.setUpdatedBy(context.getLoggedInUser());
        newPricing.setRefLookup(refLookup);
        newPricing.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
        newPricing.setUnitProductMapping(newMapping);

        if(UnitProductUpdateType.UNIT_PRODUCT_PRICING_PROFILE.equals(context.getPageType())) {
            newPricing.setRecipeProfile(data.getNewRecipeProfile().toUpperCase());
            newPricing.setStatus(AppUtils.getCorrectStatus(data.getNewStatus()));
            newPricing.setIsDeliveryOnlyProduct(AppUtils.convertYesToY(data.getNewDeliveryOnly()));
            newPricing.setPickDineInConsumables(AppUtils.convertYesToY(data.getNewPickDineInConsumables()));
        } else {
            newPricing.setDimensionDescriptor(AppUtils.getFormattedString(data.getNewDimensionDesc()));
            newPricing.setAliasProductName(AppUtils.getFormattedString(data.getNewProductAlias()));
        }

        // dao.add(newPricing);
        context.getInsertPricingList().add(newPricing);
        ProductDetail prod = newMapping.getProductDetail();
        if(prod != null && AppConstants.getValue(prod.getIsInventoryTracked())) {
            ProductPrice price = new ProductPrice();
            price.setProfile(newPricing.getRecipeProfile());
            price.setCurrentProfile("");
            updateUnitProductPriceUpdateMap(price, newMapping.getUnitDetail().getUnitId());
        }

        //extra log for new dimension...
        context.getBulkUploadDataLogList().add(addLogForBulkUpload(null, newPricing.getRefLookup().getRlId().toString(),
                "New Dimension added for unit product price mapping", BulkUploadStatus.SUCCESS,
                newPricing.getUnitProdPriceId(), BulkUploadFieldType.DIMENSION_CODE));

        if(UnitProductUpdateType.UNIT_PRODUCT_PRICING_PROFILE.equals(context.getPageType())) {
            context.getBulkUploadDataLogList().add(addLogForBulkUpload(null, newPricing.getRecipeProfile(),
                    "New Recipe profile Entry in unit product price mapping", BulkUploadStatus.SUCCESS,
                    newPricing.getUnitProdPriceId(), BulkUploadFieldType.RECIPE_PROFILE));
            context.getBulkUploadDataLogList().add(addLogForBulkUpload(null, newPricing.getStatus(), "New Pricing Status Entry in unit product price mapping",
                    BulkUploadStatus.SUCCESS, newPricing.getUnitProdPriceId(), BulkUploadFieldType.PRICING_STATUS));
            context.getBulkUploadDataLogList().add(addLogForBulkUpload(null, newPricing.getIsDeliveryOnlyProduct(), "New Delivery only Entry in unit product price mapping",
                    BulkUploadStatus.SUCCESS, newPricing.getUnitProdPriceId(), BulkUploadFieldType.IS_DELIVERY_ONLY));
            context.getBulkUploadDataLogList().add(addLogForBulkUpload(null, newPricing.getPickDineInConsumables(), "New Dine-in consumables Entry in unit product price mapping",
                    BulkUploadStatus.SUCCESS, newPricing.getUnitProdPriceId(), BulkUploadFieldType.PICK_DINE_IN_CONSUMABLES));
        } else {
            context.getBulkUploadDataLogList().add(addLogForBulkUpload(null, newPricing.getAliasProductName(), "New Product Alias Entry in unit product price mapping",
                    BulkUploadStatus.SUCCESS, newPricing.getUnitProdPriceId(), BulkUploadFieldType.PRODUCT_ALIAS));
            context.getBulkUploadDataLogList().add(addLogForBulkUpload(null, newPricing.getDimensionDescriptor(), "New Dimension descriptor Entry in unit product price mapping",
                    BulkUploadStatus.SUCCESS, newPricing.getUnitProdPriceId(), BulkUploadFieldType.DIMENSION_DESC));
        }
    }

    private CompletableFuture<DocumentDetailDTO> uploadFileInS3(MultipartFile file, Integer loggedInUserId) throws DocumentException, IOException {
        MimeType mimeType = MimeType.XLSX;
        if (file.getOriginalFilename().endsWith("xls")) {
            mimeType = MimeType.XLS;
        }
        String fileName = "UNIT_PRODUCT_PROFILE_MAPPING" + "_" + AppUtils.getCurrentTimeISTStringWithNoColons()  + "."
                + mimeType.name().toLowerCase();;
        String baseDir = "UNIT_PRODUCT_PROFILE_MAPPING" + File.separator + AppUtils.getCurrentYear() + File.separator
                + AppUtils.getCurrentMonthName() + File.separator + AppUtils.getCurrentDayofMonth();
        return genericExcelManagementService.uploadDocumentAsync(
                FileTypeDTO.OTHERS, mimeType, DocUploadTypeDTO.BULK_UPLOAD, loggedInUserId,
                file, fileName, baseDir
        );
    }

    private void getAllUnitDimensionAndProductMaps(UnitProductProfileContext context) {
        // loading all units, products and dimensions initially
        context.setUnitDetailMap(new HashMap<>());
        context.setProductDetailMap(new HashMap<>());
        context.setDimensionMap(new HashMap<>());
        context.setUnitProductMappingMap(new HashMap<>());

        Set<Integer> unitIds = context.getExcelData().stream()
                .map(UnitProductProfileExcelData::getUnitId)
                .filter(id -> id > 0)
                .collect(Collectors.toSet());

        Set<Integer> productIds = context.getExcelData().stream()
                .map(UnitProductProfileExcelData::getProductId)
                .filter(id -> id > 0)
                .collect(Collectors.toSet());

        Set<Integer> dimensionIds = context.getExcelData().stream()
                .map(UnitProductProfileExcelData::getDimensionId)
                .filter(id -> id > 0)
                .collect(Collectors.toSet());

        context.getUnitDetailMap().putAll(
                dao.getUnitsByIds(new ArrayList<>(unitIds)).stream().collect(Collectors.toMap(UnitDetail::getUnitId, unit -> unit))
        );
        context.getProductDetailMap().putAll(
                dao.getProductsByIds(new ArrayList<>(productIds)).stream().collect(Collectors.toMap(ProductDetail::getProductId, pro -> pro))
        );

        if (!dimensionIds.isEmpty()) {
            context.getDimensionMap().putAll(dao.getRefLookUpByIds(new ArrayList<>(dimensionIds)));
        }

        for(UnitProductMapping mapping : dao.getUnitProductMapping(new ArrayList<>(unitIds), new ArrayList<>(productIds))) {
            Integer unitId = mapping.getUnitDetail().getUnitId();
            Integer productId = mapping.getProductDetail().getProductId();
            String key = unitId + "_" + productId;
            context.getUnitProductMappingMap().put(key, mapping);
        }
    }

    private void addBulkUploadData(UnitProductProfileContext context) {
        context.getBulkUploadData().setUploadedType(BulkUploadType.UNIT_PRODUCT_RECIPE_UPLOAD);
        context.getBulkUploadData().setCreatedAt(AppUtils.getCurrentTimestamp());
        context.getBulkUploadData().setCreatedBy(context.getLoggedInUser());
        context.getBulkUploadData().setTotalEntriesInFile(context.getExcelData().size());
    }

    private BulkUploadDataLog addLogForBulkUpload(String oldValue, String newValue, String comment,
                                                  BulkUploadStatus bulkUploadStatus, Integer fieldId,
                                                  BulkUploadFieldType fieldType) {
        BulkUploadDataLog bulkLog = new BulkUploadDataLog();
        bulkLog.setUpdatedFieldId(fieldId);
        bulkLog.setUpdatedFieldType(fieldType);
        bulkLog.setComment(comment);
        bulkLog.setOldValue(oldValue);
        bulkLog.setNewValue(newValue);
        bulkLog.setStatus(bulkUploadStatus);
        return bulkLog;
    }

    private void updateBulkUploadDataAndLogs(UnitProductProfileContext context, CompletableFuture<DocumentDetailDTO> futureDoc) {
        DocumentDetailDTO document;
        try {
            document = futureDoc.get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOG.error("Error getting document from Async function: ", e);
            document = new DocumentDetailDTO();
        }

        context.getBulkUploadData().setDocumentId(document.getDocumentId());

        if (!context.getBulkUploadDataLogList().isEmpty()) {
            dao.add(context.getBulkUploadData());
        }

        for(BulkUploadDataLog dataLog : context.getBulkUploadDataLogList()) {
            dataLog.setBulkUploadData(context.getBulkUploadData());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public ProductImageMappingDetail saveProductDimensionImage(MimeType mimeType, int productId,
                                                      ImageCategoryType imageCategoryType, MultipartFile file, int updatedBy, Integer index, String link,Integer dimensionCode) {
        try {
            String fileName = null;
            ProductDimensionImageMapping productImageMapping = dao.getProductImagesByProductIdAndTypeAndDimension(productId,
                    imageCategoryType.name(), index,dimensionCode);
            if (productImageMapping != null) {
                productImageMapping.setLastUpdatedBy(updatedBy);
                productImageMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
                if (imageCategoryType.equals(ImageCategoryType.SHOWCASE_VIDEO)) {
                    fileName = link;
                } else {
                    fileName = getNewFileName(productImageMapping.getImageUrl(), mimeType);
                }
                productImageMapping.setImageUrl(fileName);
            } else {
                productImageMapping = new ProductDimensionImageMapping();
                productImageMapping.setProductId(productId);
                productImageMapping.setLastUpdatedBy(updatedBy);
                productImageMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
                productImageMapping.setStatus(AppConstants.ACTIVE);
                productImageMapping.setImageType(imageCategoryType.name());
                productImageMapping.setIndex(index);
                productImageMapping.setDimensionCode(dimensionCode);
                if (imageCategoryType.equals(ImageCategoryType.SHOWCASE_VIDEO)) {
                    fileName = link;
                } else {
                    fileName = productId + "-" + dimensionCode + "-"+ index + imageCategoryType.value() + "_1" + "."
                            + mimeType.name().toLowerCase();
                }
                productImageMapping.setImageUrl(fileName);
            }
            // http://d1nqp92n3q8zl7.cloudfront.net/product_image/dimension/10_special_1.jpg
            String baseDir = "master-service/product_image/dimension";
            FileDetail s3File = null;
            if (!imageCategoryType.equals(ImageCategoryType.SHOWCASE_VIDEO)) {
                s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, fileName, file,
                        true);
            }
            if (s3File != null || imageCategoryType.equals(ImageCategoryType.SHOWCASE_VIDEO)) {
                productImageMapping = dao.update(productImageMapping);
                return getProductDimensionImages(productId,dimensionCode);
            }
        } catch (Exception e) {
            LOG.error("Failed to upload image", e);
        }
        return null;
    }

    @Override
    public ApiResponse getUnitsShortByBrandId(Integer brandId, String unitType) {
        if(RequestContext.getBrandId() == null || !RequestContext.getBrandId().equals(brandId)) {
            RequestContext.getContext().setBrandId(brandId);
        }
        List<Integer> unitIdsByBrand = MasterUtil.getMappedUnits();
        Map<Integer, Unit> units = masterDataCache.getUnits();

        List<IdName> unitsList = new ArrayList<>();

        for(Integer unitId : unitIdsByBrand) {
            Unit unit = units.get(unitId);
            if(UnitStatus.IN_ACTIVE.equals(unit.getStatus()) || ( StringUtils.isNotBlank(unitType) && !unit.getFamily().name().equalsIgnoreCase(unitType))) {
                continue;
            }
            unitsList.add(new IdName(unit.getId(), unit.getName()));
        }
        return new ApiResponse(unitsList);
    }


}
