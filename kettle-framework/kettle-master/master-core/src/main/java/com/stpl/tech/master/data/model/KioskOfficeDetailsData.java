/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "KIOSK_OFFICE_DETAILS")
public class KioskOfficeDetailsData {

    private Integer officeId;
    private KioskCompanyDetailsData companyDetailsData;
    private String officeName;
    private String officeShortCode;
    private String region;
    private String tin;
    private AddressInfo addressInfo;
    private String officeContactName;
    private String officeContactEmail;
    private String officeContactPhone;
    private String officeStatus;
    private String paymentMode;
    private List<KioskLocationDetailsData> locationDetailsDataList = new ArrayList<>();



    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "OFFICE_ID", nullable = false, unique = true)
    public Integer getOfficeId() {
        return officeId;
    }

    public void setOfficeId(Integer officeId) {
        this.officeId = officeId;
    }

    @Column(name = "OFFICE_NAME", nullable = false)
    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    @Column(name = "OFFICE_SHORT_CODE", nullable = false)
    public String getOfficeShortCode() {
        return officeShortCode;
    }

    public void setOfficeShortCode(String officeShortCode) {
        this.officeShortCode = officeShortCode;
    }

    @Column(name = "OFFICE_REGION", nullable = false)
    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    @Column(name = "OFFICE_TIN", nullable = false)
    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    @Column(name = "OFFICE_STATUS", nullable = false)
    public String getOfficeStatus() {
        return officeStatus;
    }

    public void setOfficeStatus(String officeStatus) {
        this.officeStatus = officeStatus;
    }

    @Column(name = "OFFICE_PAYMENT_CODE", nullable = true)
    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    @Column(name = "OFFICE_CONTACT_NAME", nullable = true)
    public String getOfficeContactName() {
        return officeContactName;
    }

    public void setOfficeContactName(String officeContactName) {
        this.officeContactName = officeContactName;
    }

    @Column(name = "OFFICE_CONTACT_EMAIL", nullable = true)
    public String getOfficeContactEmail() {
        return officeContactEmail;
    }

    public void setOfficeContactEmail(String officeContactEmail) {
        this.officeContactEmail = officeContactEmail;
    }

    @Column(name = "OFFICE_CONTACT_PHONE", nullable = true)
    public String getOfficeContactPhone() {
        return officeContactPhone;
    }

    public void setOfficeContactPhone(String officeContactPhone) {
        this.officeContactPhone = officeContactPhone;
    }

    @ManyToOne
    @JoinColumn(name = "OFFICE_ADDR_ID", nullable = false)
    public AddressInfo getAddressInfo() {
        return addressInfo;
    }

    public void setAddressInfo(AddressInfo addressInfo) {
        this.addressInfo = addressInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COMPANY_ID", nullable = false)
    public KioskCompanyDetailsData getCompanyDetailsData() {
        return companyDetailsData;
    }

    public void setCompanyDetailsData(KioskCompanyDetailsData companyDetailsData) {
        this.companyDetailsData = companyDetailsData;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "officeDetailsData")
    public List<KioskLocationDetailsData> getLocationDetailsDataList() {
        return locationDetailsDataList;
    }

    public void setLocationDetailsDataList(List<KioskLocationDetailsData> locationDetailsDataList) {
        this.locationDetailsDataList = locationDetailsDataList;
    }
}
