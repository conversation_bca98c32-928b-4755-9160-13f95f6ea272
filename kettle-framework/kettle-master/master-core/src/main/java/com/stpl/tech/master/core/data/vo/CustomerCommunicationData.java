package com.stpl.tech.master.core.data.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class CustomerCommunicationData implements Serializable {
    private static final long serialVersionUID = -4781266530676184738L;
    private Integer customerId;
    private String contactNumber;
    private String eventType;
    private String customerName;
    private Date date;
    private Map<String,String> detail;


    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Map<String, String> getDetail() {
        return detail;
    }

    public void setDetail(Map<String, String> detail) {
        this.detail = detail;
    }
}
