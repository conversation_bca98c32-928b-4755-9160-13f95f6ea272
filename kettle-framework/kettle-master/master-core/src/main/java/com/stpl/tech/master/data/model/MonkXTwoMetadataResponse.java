package com.stpl.tech.master.data.model;

import com.stpl.tech.master.core.service.model.MonkUrlDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
public class MonkXTwoMetadataResponse {
    private Integer id;
    private Integer unitId;
    private Integer noOfXTwoMachines;
    private Integer vesselSenseDelay;
    private Integer spiceSenseDelay;
    private Date lastUpdatedTimestamp;
    private List<MonkUrlDto> monkUrls;

    public MonkXTwoMetadataResponse(Integer id, Integer unitId, Integer noOfMonks, Integer vesselSenseDelay,
                                    Integer spiceSenseDelay,Date lastUpdatedTimestamp,List<MonkUrlDto> monkUrls) {
        this.id = id;
        this.unitId = unitId;
        this.noOfXTwoMachines = noOfMonks;
        this.vesselSenseDelay = vesselSenseDelay;
        this.spiceSenseDelay = spiceSenseDelay;
        this.lastUpdatedTimestamp = lastUpdatedTimestamp;
        this.monkUrls = monkUrls;
    }
}
