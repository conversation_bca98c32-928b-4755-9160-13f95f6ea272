package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.master.data.model.UnitClosureFormData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitClosureFormDataRepository extends JpaRepository<UnitClosureFormData,Long> {
    List<UnitClosureFormData> findByUnitClosureEvent(UnitClosureEvent unitClosureEvent);
}
