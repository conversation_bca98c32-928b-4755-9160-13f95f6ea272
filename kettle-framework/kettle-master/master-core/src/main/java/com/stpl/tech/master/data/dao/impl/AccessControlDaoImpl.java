package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.AccessControlDao;
import com.stpl.tech.master.data.model.AccessControlListData;
import com.stpl.tech.master.data.model.UserRoleData;
import com.stpl.tech.master.domain.model.ApplicationName;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 08-07-2016.
 */
@Repository
public class AccessControlDaoImpl extends AbstractMasterDaoImpl implements AccessControlDao {

    @Override
    public List<AccessControlListData> getAccessControls(ApplicationName appName) {
        String queryString = "FROM AccessControlListData a";
        if (appName != null) {
            queryString += " WHERE a.applicationName = :appName";
        }
        Query query = manager.createQuery(queryString);
        if (appName != null) {
            query.setParameter("appName", appName.value());
        }
        return query.getResultList();
    }

    @Override
    public List<UserRoleData> getUserRoleDataByAppName(ApplicationName appName) {
        String queryString = "FROM UserRoleData a";
        if (appName != null) {
            queryString += " WHERE a.applicationId = :appId";
        }
        Query query = manager.createQuery(queryString);
        if (appName != null) {
            query.setParameter("appId", appName.id());
        }
        return query.getResultList();
    }
}
