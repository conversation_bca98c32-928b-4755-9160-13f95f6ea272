package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "UNIT_CLOSURE_STATE_EVENT", indexes = {
        @Index(name = "IDX_UNIT_CLOSURE_STATE_EVENT_REQUEST_ID", columnList = "REQUEST_ID"),
        @Index(name = "IDX_UNIT_CLOSURE_STATE_EVENT_STATE_ID", columnList = "STATE_ID")
})
public class UnitClosureStateEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID", nullable = false)
    private Long eventId;
    @Column(name = "REQUEST_ID", nullable = false)
    private Long requestId;
    @Column(name = "STATE_ID", nullable = false)
    private Long stateId;
    @Column(name = "EVENT_STATUS", nullable = false)
    private String eventStatus;
    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATION_TIME")
    private Date lastUpdationTime;

}