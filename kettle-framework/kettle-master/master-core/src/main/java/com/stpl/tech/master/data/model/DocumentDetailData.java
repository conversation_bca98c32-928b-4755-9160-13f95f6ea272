package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by <PERSON><PERSON><PERSON> on 01-08-2023.
 */
@Entity
@Table(name = "DOCUMENT_DETAIL_DATA")
public class DocumentDetailData {

    private Integer documentId;
    private String fileType;
    private String fileUrl;
    private String documentLink;
    private Date updateTime;
    private Integer updatedBy;
    private String mimeType;
    private String documentUploadType;
    private Integer documentUploadTypeId;

    private String s3Bucket;
    private String s3Key;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DOCUMENT_ID", unique = true, nullable = false)
    public Integer getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Integer documentId) {
        this.documentId = documentId;
    }

    @Column(name = "FILE_TYPE", nullable = false)
    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    @Column(name = "DOCUMENT_URL", nullable = true)
    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String documentLink) {
        this.fileUrl = documentLink;
    }

    @Column(name = "DOCUMENT_LINK", nullable = true)
    public String getDocumentLink() {
        return documentLink;
    }

    public void setDocumentLink(String documentLink) {
        this.documentLink = documentLink;
    }

    @Column(name = "UPDATE_TIME", nullable = false)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "UPDATED_BY", nullable = false)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "DOCUMENT_MIME_TYPE", nullable = false)
    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    @Column(name = "DOCUMENT_UPLOAD_TYPE", nullable = false)
    public String getDocumentUploadType() {
        return documentUploadType;
    }

    public void setDocumentUploadType(String documentUploadType) {
        this.documentUploadType = documentUploadType;
    }

    @Column(name = "DOCUMENT_UPLOAD_TYPE_ID", nullable = true)
    public Integer getDocumentUploadTypeId() {
        return documentUploadTypeId;
    }

    public void setDocumentUploadTypeId(Integer documentUploadTypeId) {
        this.documentUploadTypeId = documentUploadTypeId;
    }

    @Column(name = "DOCUMENT_S3_BUCKET", nullable = true)
    public String getS3Bucket() {
        return s3Bucket;
    }

    public void setS3Bucket(String s3Bucket) {
        this.s3Bucket = s3Bucket;
    }

    @Column(name = "DOCUMENT_S3_KEY", nullable = true)
    public String getS3Key() {
        return s3Key;
    }

    public void setS3Key(String s3Key) {
        this.s3Key = s3Key;
    }
}
