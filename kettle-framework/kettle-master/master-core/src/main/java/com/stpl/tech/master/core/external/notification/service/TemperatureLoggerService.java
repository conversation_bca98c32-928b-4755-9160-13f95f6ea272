package com.stpl.tech.master.core.external.notification.service;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import com.stpl.tech.master.data.model.TemperatureLogDetail;

/**
 * Created by Chaayos on 21-09-2016.
 */
public interface TemperatureLoggerService {

	public TemperatureLogDetail addLog(TemperatureLogDetail log) throws IOException;

	List<TemperatureLogDetail> getTemperatureDetail(String locationId, String deviceName, Date logTime);

	public void markAsNotified(Integer id, boolean notified);

	List<TemperatureLogDetail> findAll(Date startTime, Date endTime);
}
