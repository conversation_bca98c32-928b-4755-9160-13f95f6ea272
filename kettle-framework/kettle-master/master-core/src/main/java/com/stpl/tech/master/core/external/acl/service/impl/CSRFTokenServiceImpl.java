//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.25 at 12:25:34 AM IST 
//

package com.stpl.tech.master.core.external.acl.service.impl;

import java.net.URLEncoder;
import java.util.HashSet;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.external.acl.service.CSRFTokenService;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.util.AppUtils;

@Service
public class CSRFTokenServiceImpl implements CSRFTokenService {

	private static final Logger LOG = LoggerFactory.getLogger(CSRFTokenServiceImpl.class);

	@Autowired
	private TokenService<CSRFToken> jwtService;

	private Set<String> tokens = new HashSet<>();

	public String getToken(String url) {
		CSRFToken token = new CSRFToken(url, AppUtils.getCurrentTimeISTString());
		String data = jwtService.createToken(token, -1l);
		LOG.info("CSRF token:::::::" + data);

		tokens.add(URLEncoder.encode(data));
		return data;
	}

	@Override
	public void clearAll() {
		tokens.clear();
	}

	@Override
	public boolean contains(String key) {
		return tokens.contains(key);
	}

	@Override
	public void remove(String key) {
		tokens.remove(key);
	}
}
