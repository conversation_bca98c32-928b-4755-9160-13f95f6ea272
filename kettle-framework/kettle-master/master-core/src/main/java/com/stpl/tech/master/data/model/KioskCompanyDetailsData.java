/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "KIOSK_COMPANY_DETAILS")
public class KioskCompanyDetailsData {

    private Integer companyId;
    private String companyName;
    private String companyEmail;
    private String companyStatus;
    private String contactName;
    private String contactEmail;
    private String contactPhone;
    private String country;
    private String paymentMode;
    private String subDomain;
    List<KioskOfficeDetailsData> officeDetailsDataList = new ArrayList<>();
    List<KioskCompanyDomainData> companyDomainDataList = new ArrayList<>();

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "COMPANY_ID", nullable = false, unique = true)
    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    @Column(name = "COMPANY_NAME", nullable = false)
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    @Column(name = "COMPANY_EMAIL", nullable = false)
    public String getCompanyEmail() {
        return companyEmail;
    }

    public void setCompanyEmail(String companyEmail) {
        this.companyEmail = companyEmail;
    }

    @Column(name = "COMPANY_STATUS", nullable = false)
    public String getCompanyStatus() {
        return companyStatus;
    }

    public void setCompanyStatus(String companyStatus) {
        this.companyStatus = companyStatus;
    }

    @Column(name = "CONTACT_NAME", nullable = false)
    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    @Column(name = "CONTACT_EMAIL", nullable = false)
    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    @Column(name = "CONTACT_PHONE", nullable = false)
    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    @Column(name = "COUNTRY", nullable = false)
    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Column(name = "PAYMENT_MODE", nullable = false)
    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    @Column(name = "KIOSK_SUB_DOMAIN", nullable = false)
    public String getSubDomain() {
        return subDomain;
    }

    public void setSubDomain(String subDomain) {
        this.subDomain = subDomain;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "companyDetailsData")
    public List<KioskOfficeDetailsData> getOfficeDetailsDataList() {
        return officeDetailsDataList;
    }

    public void setOfficeDetailsDataList(List<KioskOfficeDetailsData> officeDetailsDataList) {
        this.officeDetailsDataList = officeDetailsDataList;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "companyDetailsData")
    public List<KioskCompanyDomainData> getCompanyDomainDataList() {
        return companyDomainDataList;
    }

    public void setCompanyDomainDataList(List<KioskCompanyDomainData> companyDomainDataList) {
        this.companyDomainDataList = companyDomainDataList;
    }
}
