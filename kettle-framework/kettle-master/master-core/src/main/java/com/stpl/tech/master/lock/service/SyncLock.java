package com.stpl.tech.master.lock.service;


import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@Component
@RefreshScope
public class SyncLock {

    @Autowired
    private Environment env;

    private final ConcurrentReferenceHashMap<String, ReentrantLock> map;
    private ReentrantLock currentLock;

    public SyncLock() {
        map = new ConcurrentReferenceHashMap<>();
    }

    public <T> T syncLock(String key, Callable<T> task) {
        return syncLock(key, null, task, isAcquireLockUsed());
    }

    public <T> T syncLock(String key, Long timeInMilliseconds, Callable<T> task, Boolean isAcquiredLockUsed) {
        try {
            currentLock = this.map.compute(key, (K, V) -> V == null ? new ReentrantLock() : V);
            if (!isAcquiredLockUsed) {
                return task.call();
            } else {
                boolean isLockAcquired = currentLock.tryLock(timeInMilliseconds == null ? lockTimeInMilliSecond() : timeInMilliseconds, TimeUnit.MILLISECONDS);
                if (isLockAcquired) {
                    return task.call();
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (currentLock.isHeldByCurrentThread()) {
                currentLock.unlock();
            }
        }
        return null;
    }

    public void syncLock(String key, Runnable task) {
        syncLock(key, null, task);
    }

    public void syncLock(String key, Long timeInMilliseconds, Runnable task) {
        try {
            currentLock = this.map.compute(key, (K, V) -> V == null ? new ReentrantLock() : V);
            boolean isLockAcquired = currentLock.tryLock(timeInMilliseconds == null ? 1000 : timeInMilliseconds, TimeUnit.MILLISECONDS);
            if (isLockAcquired) {
                task.run();
            } else {
                throw new RuntimeException("Not able to acquire lock!");
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            if (currentLock.isHeldByCurrentThread()) {
                currentLock.unlock();
            }
        }
    }

    private Boolean isAcquireLockUsed(){
        return Boolean.valueOf(env.getProperty("acquire.lock.used","true"));
    }

    private Integer lockTimeInMilliSecond(){
        return Integer.valueOf(env.getProperty("lock.time.in.ms", String.valueOf(500)));
    }
}
