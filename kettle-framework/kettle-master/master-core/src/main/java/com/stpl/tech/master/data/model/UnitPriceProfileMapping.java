package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;


@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "UNIT_PRICE_PROFILE_MAPPING")
public class UnitPriceProfileMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UNIT_PRICE_PROFILE_MAPPING_ID", nullable = false)
    private Integer id;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "PRICE_PROFILE_ID")
    private Integer priceProfileId;

    @Column(name = "PRICE_PROFILE_VERSION")
    private Integer priceProfileVersion;

    @Column(name = "CHANNEL_PARTNER_ID")
    private Integer channelPartnerId;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "MAPPING_STATUS", length = 45)
    private String mappingStatus;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
}
