package com.stpl.tech.master.notification;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.Map;

public class ChangesDiffNotificationTemplate extends AbstractTemplate {
    private Map<String, Pair<Object,Object>> diffs;
    private String changedBy;
    private String className;
    private Integer objectId;
    private String basePath;


    public ChangesDiffNotificationTemplate(Map<String, Pair<Object, Object>> diffs, String changedBy, String className,
                                           Integer objectId , String basePath) {
        this.diffs = diffs;
        this.changedBy = changedBy;
        this.className = className;
        this.objectId = objectId;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "template/ChangesDiffTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "ObjectDiffs" + "/" + className + "_" + changedBy + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> diffsMap = new HashMap<>();
        diffsMap.put("diffs",diffs);
        diffsMap.put("keys",diffs.keySet());
        diffsMap.put("Id",objectId);
        diffsMap.put("Object",className);
        return diffsMap;
    }

}
