package com.stpl.tech.master.core.external.notification.service.impl;

import com.google.cloud.firestore.DocumentReference;
import com.google.cloud.firestore.FieldValue;
import com.google.cloud.firestore.SetOptions;
import com.google.firebase.cloud.FirestoreClient;
import com.stpl.tech.master.core.external.notification.FireStoreOrderNotification;
import com.stpl.tech.master.core.external.notification.FireStoreSuperUEvent;
import com.stpl.tech.master.core.external.notification.service.FireStoreService;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Log4j2
public class FireStoreServiceImpl implements FireStoreService {

    private final Map<Integer, DocumentReference> unitWiseFireStoreDocument = new ConcurrentHashMap<>();
    private final Map<Integer, DocumentReference> unitWiseSuperUFireStoreDocument = new ConcurrentHashMap<>();

    @Override
    public boolean sendOrderNotificationThroughFireStore(Integer unitId, Integer orderId, String fireStoreNotificationType,
                                                         Integer androidNotificationId, String androidNotificationType) {
        try {
            DocumentReference docRef;
            if (unitWiseFireStoreDocument.containsKey(unitId)) {
                docRef = unitWiseFireStoreDocument.get(unitId);
            } else {
                docRef = FirestoreClient.getFirestore().collection(unitId.toString()).document("ORDERS");
                unitWiseFireStoreDocument.put(unitId, docRef);
            }
            FireStoreOrderNotification fireStoreOrderNotification = new FireStoreOrderNotification(orderId, FieldValue.serverTimestamp(), fireStoreNotificationType, androidNotificationId, androidNotificationType);
            docRef.set(fireStoreOrderNotification, SetOptions.merge()).get();
            return true;
        } catch (Exception e) {
            log.error("Exception Occurred while writing to Fire store for UnitId : {} and OrderId : {}", unitId, orderId, e);
            // routing through firebase again
        }
        return false;
    }

    @Override
    public boolean sendSuperUNotificationThroughFireStore(Integer unitId,boolean isAlert) {
        try {
            log.info("Sending super u event to firestore for UnitId : {}", unitId);
            DocumentReference docRef;
            if (unitWiseSuperUFireStoreDocument.containsKey(unitId)) {
                docRef = unitWiseSuperUFireStoreDocument.get(unitId);
            } else {
                docRef = FirestoreClient.getFirestore().collection(unitId.toString()).document("SUPERU_EVENT");
                unitWiseSuperUFireStoreDocument.put(unitId, docRef);
            }
            FireStoreSuperUEvent fireStoreSuperUEvent = new FireStoreSuperUEvent(FieldValue.serverTimestamp(),unitId,isAlert);
            docRef.set(fireStoreSuperUEvent, SetOptions.merge()).get();
            log.info("Sent super u event to firestore successfully  for UnitId : {}", unitId);
            return true;
        } catch (Exception e) {
            log.error("Exception Occurred while writing to Fire store for UnitId : {} ", unitId, e);
            // routing through firebase again
        }
        return false;
    }
}
