package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.mapper.DtoDataMapper;
import com.stpl.tech.master.core.mapper.DtoDataMapperUtil;
import com.stpl.tech.master.core.service.LCDMenuVariantManagementService;
import com.stpl.tech.master.data.dao.LCDMenuVariantGroupDao;
import com.stpl.tech.master.data.dao.LCDMenuVariantItemDao;
import com.stpl.tech.master.data.model.LCMMenuVariantMetadataGroup;
import com.stpl.tech.master.data.model.LCMMenuVariantMetadataItem;
import com.stpl.tech.master.domain.model.LCMMenuVariantMetadataGroupDomain;
import com.stpl.tech.master.domain.model.LCMMenuVariantMetadataItemDomain;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
        readOnly = true, propagation = Propagation.REQUIRED)
public class LCDMenuVariantManagementServiceImpl implements LCDMenuVariantManagementService {
    @Autowired
    private LCDMenuVariantGroupDao groupRepository;

    @Autowired
    private LCDMenuVariantItemDao itemRepository;

    @Override
    public List<LCMMenuVariantMetadataGroupDomain> getAllGroups() {
        return DtoDataMapperUtil.mapVariantMetadataToDomainList(groupRepository.findAll());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public LCMMenuVariantMetadataGroupDomain createGroup(LCMMenuVariantMetadataGroup group) {
        return DtoDataMapperUtil.toDomain(groupRepository.save(group));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public LCMMenuVariantMetadataGroupDomain updateGroup(Long groupId, LCMMenuVariantMetadataGroupDomain group) {
        LCMMenuVariantMetadataGroup existingGroup = groupRepository.findById(groupId)
                .orElseThrow(() -> new RuntimeException("Group not found"));

        existingGroup.setGroupName(group.getGroupName());
        existingGroup.setStatus(group.getStatus());
        return DtoDataMapperUtil.toDomain(groupRepository.save(existingGroup));
    }

    @Override
    @Transactional
    public void deleteGroup(Long groupId) {

        /*// Then delete the group
        groupRepository.deleteById(groupId);*/
    }

    @Override
    public List<LCMMenuVariantMetadataItemDomain> getItemsByGroup(Long groupId) {
        return DtoDataMapperUtil.toDomain(groupRepository.findById(groupId)
                .orElseThrow(() -> new RuntimeException("Group not found"))).getItems();

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public LCMMenuVariantMetadataItemDomain createItem(LCMMenuVariantMetadataItem item , Long groupId) {
        LCMMenuVariantMetadataGroup group = groupRepository.findById(groupId)
                .orElseThrow(() -> new RuntimeException("Group not found"));
        item.setGroup(group);
        return DtoDataMapperUtil.toDomain(itemRepository.save(item));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public LCMMenuVariantMetadataItemDomain updateItem(Long itemId, LCMMenuVariantMetadataItemDomain item , Long groupId) {
        LCMMenuVariantMetadataItem existingItem = itemRepository.findById(itemId)
                .orElseThrow(() -> new RuntimeException("Item not found"));
        LCMMenuVariantMetadataGroup group = groupRepository.findById(groupId)
                .orElseThrow(() -> new RuntimeException("Group not found"));
        existingItem.setGroup(group);
        existingItem.setItemName(item.getItemName());
        existingItem.setStatus(item.getStatus());

        return DtoDataMapperUtil.toDomain(itemRepository.save(existingItem));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public void deleteItem(Long itemId) {
        itemRepository.deleteById(itemId);
    }
}
