/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.cache;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.tax.model.AdditionalTax;
import com.stpl.tech.master.tax.model.CategoryAdditionalTax;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.StateTax;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.master.tax.model.Taxation;

@Service
public class TaxDataCache {

	private static final Logger LOG = LoggerFactory.getLogger(TaxDataCache.class);

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	@Autowired
	private MasterDataCache masterCache;

	private IMap<String, TaxCategory> taxCategoryMap;
	private IMap<String, CategoryTax> categoryTaxMap;
	private IMap<String, Map<Integer, TaxData>> hsnStateTaxDataMap;
	private MultiMap<Integer, Taxation> saleTaxations;
	private MultiMap<Integer, Taxation> allTaxations;

	public TaxDataCache() {
	}

	@PostConstruct
	public void createCache() {
    	LOG.info("POST-CONSTRUCT TaxDataCache - STARTED");
		LOG.info("$$$$$$$$$$$$$$$Creating Tax Cache$$$$$$$$$$$$$$$");
		taxCategoryMap = instance.getMap("TaxDataCache:taxCategoryMap");
		categoryTaxMap = instance.getMap("TaxDataCache:categoryTaxMap");
		hsnStateTaxDataMap = instance.getMap("TaxDataCache:hsnStateTaxDataMap");
		saleTaxations = instance.getMultiMap("TaxDataCache:taxations");
		allTaxations = instance.getMultiMap("TaxDataCache:allTaxations");
	}

	public void clearTaxCategoryMapCache() {
		instance.getMap("TaxDataCache:taxCategoryMap").clear();
	}

	public void clearCategoryTaxMapCache() {
		instance.getMap("TaxDataCache:categoryTaxMap").clear();
	}
	
	public void clearHsnStateTaxDataMapCache() {
		instance.getMap("TaxDataCache:hsnStateTaxDataMap").clear();
	}
	
	public void clearTaxationsCache() {
		instance.getMultiMap("TaxDataCache:taxations").clear();
	}
	
	public void clearAllTaxationsCache() {
		instance.getMultiMap("TaxDataCache:allTaxations").clear();
	}
	
	public void clearCache() {
		LOG.info("$$$$$$$$$$$$$$$Clearing Tax Cache$$$$$$$$$$$$$$$");
		instance.getMap("TaxDataCache:taxCategoryMap").clear();
		instance.getMap("TaxDataCache:categoryTaxMap").clear();
		instance.getMap("TaxDataCache:hsnStateTaxDataMap").clear();
		instance.getMultiMap("TaxDataCache:taxations").clear();
		instance.getMultiMap("TaxDataCache:allTaxations").clear();
	}
	
	public HazelcastInstance getInstance() {
		return instance;
	}

	public void setInstance(HazelcastInstance instance) {
		this.instance = instance;
	}

	public Collection<TaxCategory> getAllTaxCategory() {
		return taxCategoryMap.values();
	}

	public Collection<CategoryTax> getAllCategoryTax() {
		return categoryTaxMap.values();
	}

	public TaxCategory getTaxCategory(String code) {
		return taxCategoryMap.get(code);
	}

	public CategoryTax getCategoryTax(String code) {
		return categoryTaxMap.get(code);
	}

	public IMap<String, TaxCategory> getTaxCategoryMap() {
		return taxCategoryMap;
	}

	public IMap<String, CategoryTax> getCategoryTaxMap() {
		return categoryTaxMap;
	}

	public IMap<String, Map<Integer, TaxData>> getHsnStateTaxDataMap() {
		return hsnStateTaxDataMap;
	}

	public MultiMap<Integer, Taxation> getSaleTaxations() {
		return saleTaxations;
	}

	public void setSaleTaxations(MultiMap<Integer, Taxation> saleTaxations) {
		this.saleTaxations = saleTaxations;
	}

	public MultiMap<Integer, Taxation> getAllTaxations() {
		return allTaxations;
	}

	public void setAllTaxations(MultiMap<Integer, Taxation> allTaxations) {
		this.allTaxations = allTaxations;
	}

	public TaxData getTaxData(int stateId, String hsnCode) {
		TaxData taxData = new TaxData();
		if (hsnStateTaxDataMap.containsKey(hsnCode)) {
			taxData = hsnStateTaxDataMap.get(hsnCode).get(stateId);
		}
		return taxData;
	}

	public void addTaxCategoryToCache(TaxCategory category) {
		getTaxCategoryMap().put(category.getCode(), category);
	}

	public void addCategoryTaxToCache(CategoryTax category) {
		getCategoryTaxMap().put(category.getCategory().getCode(), category);
	}

	public void updateTaxCache(List<CategoryTax> categories, Map<String, CategoryAdditionalTax> additionalMap) {
		Set<String> salesTaxCodes = masterCache.getAllProducts().stream().map(Product::getTaxCode)
				.collect(Collectors.toSet());

		Map<Integer, Set<Taxation>> allTaxations = new HashMap<>();
		Map<Integer, Set<Taxation>> salesTaxations = new HashMap<>();
		for (CategoryTax category : categories) {
			Map<Integer, TaxData> stateMap = new HashMap<>();
			for (StateTax stateTax : category.getTaxes()) {
				TaxData td = new TaxData();
				td.setState(stateTax);
				td.setTaxCode(category.getCategory().getCode());
				stateMap.put(stateTax.getState().getId(), td);
				addTax(stateTax, allTaxations);
				if (salesTaxCodes.contains(category.getCategory().getCode())) {
					addTax(stateTax, salesTaxations);
				}
			}
			if (additionalMap.containsKey(category.getCategory().getCode())) {
				CategoryAdditionalTax additionalTax = additionalMap.get(category.getCategory().getCode());
				for (AdditionalTax otherTax : additionalTax.getTaxes()) {
					TaxData td = stateMap.get(otherTax.getState().getId());
					if (td != null && td.getState() != null) {
						td.getOthers().add(otherTax);
					}
					addTax(otherTax, allTaxations);
					if (salesTaxCodes.contains(category.getCategory().getCode())) {
						addTax(otherTax, salesTaxations);
					}
				}
			}
			getHsnStateTaxDataMap().put(category.getCategory().getCode(), stateMap);
		}
		addToCache(getAllTaxations(), allTaxations);
		addToCache(getSaleTaxations(), salesTaxations);
	}

	private void addToCache(MultiMap<Integer, Taxation> taxations, Map<Integer, Set<Taxation>> allTaxations) {
		for (Integer key : allTaxations.keySet()) {
			for (Taxation td : allTaxations.get(key)) {
				System.out.println("State " + key + ", Taxation " + td);
				taxations.put(key, td);
			}
		}
	}

	/**
	 * @param otherTax
	 * @param allTaxations
	 */
	private void addTax(AdditionalTax otherTax, Map<Integer, Set<Taxation>> allTaxations) {
		Taxation td1 = new Taxation("CESS", "CESS", "CESS", otherTax.getTax(), otherTax.getType());
		addToMap(allTaxations, otherTax.getState().getId(), td1);
	}

	/**
	 * @param stateTax
	 * @param allTaxations
	 */
	private void addTax(StateTax stateTax, Map<Integer, Set<Taxation>> allTaxations) {
		Taxation td1 = new Taxation("A", "IGST", "IGST", stateTax.getIgst(), "GST");
		Taxation td2 = new Taxation("A", "CGST", "CGST", stateTax.getCgst(), "GST");
		Taxation td3 = new Taxation("A", "SGST/UTGST", "SGST/UTGST", stateTax.getSgst(), "GST");
		addToMap(allTaxations, stateTax.getState().getId(), td1);
		addToMap(allTaxations, stateTax.getState().getId(), td2);
		addToMap(allTaxations, stateTax.getState().getId(), td3);
	}

	private void addToMap(Map<Integer, Set<Taxation>> allTaxations, int id, Taxation td) {
		if (td.getPercentage().compareTo(BigDecimal.ZERO) == 0) {
			return;
		}
		if (!allTaxations.containsKey(id)) {
			allTaxations.put(id, new TreeSet<>());
		}
		allTaxations.get(id).add(td);
	}

	@Override
	public String toString() {
		return "TaxDataCache{" +
				"taxCategoryMap =" + taxCategoryMap.size() +
				", hsnStateTaxDataMap=" + hsnStateTaxDataMap.size() +
				", saleTaxations=" + saleTaxations.size() +
				", allTaxations=" + allTaxations.size() +
				'}';
	}
}
