package com.stpl.tech.master.core.external.excel;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.data.model.ExcelRequestData;
import com.stpl.tech.master.domain.model.DocUploadTypeDTO;
import com.stpl.tech.master.domain.model.DocumentDetailDTO;
import com.stpl.tech.master.domain.model.FileTypeDTO;
import com.stpl.tech.master.domain.model.MimeType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
public interface GenericExcelManagementService {

    CompletableFuture<DocumentDetailDTO> uploadDocumentAsync(FileTypeDTO type, MimeType mimeType, DocUploadTypeDTO docType, Integer userId, MultipartFile file, String fileName, String baseDir) throws IOException, DocumentException;

    <T> List<T> convertExcelDataIntoList(MultipartFile excelFile, String className);

    View downloadExcelFromRequestData(ExcelRequestData excelRequestData) throws Exception;

}
