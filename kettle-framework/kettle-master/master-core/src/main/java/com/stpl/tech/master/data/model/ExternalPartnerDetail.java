package com.stpl.tech.master.data.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "EXTERNAL_PARTNER_DETAIL")
public class ExternalPartnerDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8527571093464059631L;

	private int id;

	private String partnerName;

	private String partnerCode;

	private Integer linkedProductId;

	private Integer linkedPaymentModeId;

	private Integer linkedCreditAccountId;

	private String endPoint;

	private String username;

	private String passCode;

	private Date creationDate;

	private String partnerStatus;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "DETAIL_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "PARTNER_NAME", nullable = false, length = 50)
	public String getPartnerName() {
		return partnerName;
	}

	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}

	@Column(name = "PARTNER_CODE", nullable = false, length = 20)
	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	@Column(name = "LINKED_PRODUCT_ID", nullable = false)
	public Integer getLinkedProductId() {
		return linkedProductId;
	}

	public void setLinkedProductId(Integer linkedProductId) {
		this.linkedProductId = linkedProductId;
	}

	@Column(name = "LINKED_PAYMENT_MODE_ID", nullable = false)
	public Integer getLinkedPaymentModeId() {
		return linkedPaymentModeId;
	}

	public void setLinkedPaymentModeId(Integer linkedPaymentModeId) {
		this.linkedPaymentModeId = linkedPaymentModeId;
	}

	@Column(name = "LINKED_CREDIT_ACCOUNT_ID", nullable = true)
	public Integer getLinkedCreditAccountId() {
		return linkedCreditAccountId;
	}

	public void setLinkedCreditAccountId(Integer linkedCreditAccountId) {
		this.linkedCreditAccountId = linkedCreditAccountId;
	}

	@Column(name = "END_POINT", nullable = false)
	public String getEndPoint() {
		return endPoint;
	}

	public void setEndPoint(String endPoint) {
		this.endPoint = endPoint;
	}

	@Column(name = "USERNAME", nullable = false)
	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	@Column(name = "PASS_CODE", nullable = false)
	public String getPassCode() {
		return passCode;
	}

	public void setPassCode(String passCode) {
		this.passCode = passCode;
	}

	@Column(name = "STATUS", nullable = false)
	public String getPartnerStatus() {
		return partnerStatus;
	}

	public void setPartnerStatus(String partnerStatus) {
		this.partnerStatus = partnerStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_AT", nullable = true)
	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

}