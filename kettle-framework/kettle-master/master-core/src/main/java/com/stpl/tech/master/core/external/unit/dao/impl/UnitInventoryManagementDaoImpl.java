/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


package com.stpl.tech.master.core.external.unit.dao.impl;

import com.stpl.tech.master.core.external.unit.dao.UnitInventoryManagementDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.util.AppConstants;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.*;

@Repository
public class UnitInventoryManagementDaoImpl extends AbstractMasterDaoImpl implements UnitInventoryManagementDao {

    public Map<Integer, Set<Integer>> getUnitProductMappings(List<Integer> unitId) {

        Map<Integer, Set<Integer>> map = new HashMap<>();
        Query query = manager.createQuery(
                "FROM UnitProductMapping where unitDetail.unitId IN( :unitId ) and productDetail.isInventoryTracked = :isInventoryTracked and productDetail.productStatus = :productStatus and productStatus = :productMappingStatus");
        query.setParameter("unitId", unitId);
        query.setParameter("isInventoryTracked", AppConstants.YES);
        query.setParameter("productStatus", ProductStatus.ACTIVE.name());
        query.setParameter("productMappingStatus", AppConstants.ACTIVE);
        @SuppressWarnings("unchecked")
        List<UnitProductMapping> list = query.getResultList();
        for (UnitProductMapping data : list) {
            if (!map.containsKey(data.getUnitDetail().getUnitId())) {
                map.put(data.getUnitDetail().getUnitId(), new HashSet<>());
            }
            map.get(data.getUnitDetail().getUnitId()).add(data.getProductDetail().getProductId());
        }
        return map;
    }

}
