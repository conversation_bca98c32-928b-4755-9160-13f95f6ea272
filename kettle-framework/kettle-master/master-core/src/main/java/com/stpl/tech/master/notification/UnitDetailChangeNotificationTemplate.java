package com.stpl.tech.master.notification;

import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.Map;

public class UnitDetailChangeNotificationTemplate extends AbstractTemplate {

    private String basePath;
    private UnitDetail unitDetail;
    private Map<String, Pair<Object, Object>> unitDetailDifference;
    private Map<String, Pair<Object, Object>> diffs;
    private String changedBy;
    private String className;
    private Integer objectId;

    private Unit newObject;


    public UnitDetailChangeNotificationTemplate(
            Map<String, Pair<Object, Object>> diffs, String changedBy, String className,
            Integer objectId , String basePath, Unit newObject
            ) {
        this.diffs = diffs;
        this.changedBy = changedBy;
        this.className = className;
        this.objectId = objectId;
        this.basePath = basePath;
        this.newObject = newObject;
    }

    public String getTemplatePath() {
        return "template/UnitDetailChangeTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "ObjectDiffs" + "/" + className + "_" + changedBy + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> diffsMap = new HashMap<>();
        diffsMap.put("diffs", diffs);
        diffsMap.put("keys", diffs.keySet());
        diffsMap.put("Id", objectId);
        diffsMap.put("Object", className);
        diffsMap.put("NewObject", newObject);
        return diffsMap;
    }
}
