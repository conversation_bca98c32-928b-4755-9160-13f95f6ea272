/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.util;

import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import com.stpl.tech.master.core.config.ServiceConfig;

public class ServiceContextFactory {

	private static final AnnotationConfigApplicationContext ctx = new AnnotationConfigApplicationContext();

	static {
		ctx.register(ServiceConfig.class);
		ctx.refresh();
	}

	public static <T> T getBean(Class<T> clazz) {
		return ctx.getBean(clazz);
	}

	public static <T> T getBean(String name, Class<T> clazz) {
		return ctx.getBean(name, clazz);
	}
}
