/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.HashMap;
import java.util.Map;

public class UnitActivationReceipt extends AbstractTemplate {

	private final String action;
	private final String basePath;
	private final Unit unit;
	private final Map<String, Object> data = new HashMap<String, Object>();

	public UnitActivationReceipt(String action, Unit unit, String basePath) {
		super();
		this.action = action;
		this.unit = unit;
		this.basePath = basePath;
	}

	public String getTemplatePath() {
		return "template/UnitActivation.html";
	}

	public Map<String, Object> getData() {
		data.put("action", action);
		data.put("unit", unit);
		return data;
	}

	@Override
	public String getFilepath() {
		return basePath + "/UnitActivation/" + unit.getId() + ".html";
	}

	public String getAction() {
		return action;
	}

	public Unit getUnit() {
		return unit;
	}
}
