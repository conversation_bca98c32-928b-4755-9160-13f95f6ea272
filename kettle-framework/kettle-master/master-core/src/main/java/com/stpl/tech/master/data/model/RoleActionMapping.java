/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

// Generated 20 Jul, 2015 5:25:16 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

/**
 * EmployeeUnitMapping generated by hbm2java
 */
@Entity
@Table(name = "ROLE_ACTION_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "ROLE_ID", "ACTION_DETAIL_ID",
		"MAPPING_STATUS" }))
public class RoleActionMapping implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6413278349556233140L;
	private Integer roleActionMappingId;
	private ActionDetailData actionDetail;
	private UserRoleData roleDetail;
	private String mappingStatus;
	private Integer updatedBy;
	private Date lastUpdateTime;

	public RoleActionMapping() {
	}

	public RoleActionMapping(ActionDetailData actionDetail, UserRoleData roleDetail, String mappingStatus,
			Date lastUpdateTime) {
		super();
		this.actionDetail = actionDetail;
		this.roleDetail = roleDetail;
		this.mappingStatus = mappingStatus;
		this.lastUpdateTime = lastUpdateTime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ROLE_ACTION_MAPPING_ID", unique = true, nullable = false)
	public Integer getRoleActionMappingId() {
		return this.roleActionMappingId;
	}

	public void setRoleActionMappingId(Integer empUnitKeyId) {
		this.roleActionMappingId = empUnitKeyId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ACTION_DETAIL_ID", nullable = false)
	public ActionDetailData getActionDetail() {
		return this.actionDetail;
	}

	public void setActionDetail(ActionDetailData employeeDetail) {
		this.actionDetail = employeeDetail;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROLE_ID", nullable = false)
	public UserRoleData getRoleDetail() {
		return roleDetail;
	}

	public void setRoleDetail(UserRoleData roleDetail) {
		this.roleDetail = roleDetail;
	}

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return this.mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	@Column(name = "UPDATED_BY")
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
	public Date getLastUpdateTime() {
		return this.lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
