/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.ApplicationVersionDetail;
import com.stpl.tech.master.data.model.ApplicationVersionDetailData;
import com.stpl.tech.master.data.model.ApplicationVersionEvent;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.CacheReferenceMetadata;
import com.stpl.tech.master.data.model.FeedbackQuestionsDetail;
import com.stpl.tech.master.data.model.FeedbackQuestionsUnitMapping;
import com.stpl.tech.master.data.model.PaymentMode;
import com.stpl.tech.master.data.model.TaxProfile;
import com.stpl.tech.master.data.model.UnitIpAddressData;
import com.stpl.tech.master.data.model.UnitToPartnerDqrMapping;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.domain.model.ApkUploadResponse;
import com.stpl.tech.master.domain.model.ApplicationVersionDomian;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.BrandMapping;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.FeedbackQuesMappingDomain;
import com.stpl.tech.master.domain.model.FeedbackQuestionDomain;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitContactData;
import com.stpl.tech.master.domain.model.UnitContactDetails;
import com.stpl.tech.master.domain.model.UnitIpAddressRequest;
import com.stpl.tech.master.domain.model.UnitPaymentModeMappingDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.domain.model.UnitToPartnerEdcMappingDetail;
import com.stpl.tech.master.domain.model.UnitVersionDetail;
import com.stpl.tech.master.domain.model.UnitVersionDomain;
import com.stpl.tech.master.domain.model.UnitWsToStationCategoryMappingRequest;
import com.stpl.tech.master.domain.model.VersionEventDomain;
import com.stpl.tech.master.monk.configuration.model.MonkAttr;
import com.stpl.tech.master.monk.configuration.model.MonkConfiguration;
import com.stpl.tech.master.monk.configuration.model.MonkConfigurationValue;
import com.stpl.tech.master.readonly.domain.model.UnitProductData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface UnitManagementService {

	public Unit addUnit(Unit unit) throws DataUpdationException, DataNotFoundException;

	public Unit updateUnit(Unit unit) throws DataUpdationException, DataNotFoundException;

	public boolean updateUnitBusinessHours(CafeTimingChangeRequest request) throws DataUpdationException, DataNotFoundException;

	public Unit changeUnitStatus(int unitId, UnitStatus status) throws DataUpdationException, DataNotFoundException, IOException;

	public Unit updateProductMapping(int unitId, List<ProductPrice> prices) throws DataUpdationException;

	public BusinessDivision getBusinessDivision(int businessDivId);

	public TaxProfile getTaxProfile(int taxProfileId);

    public MonkConfiguration addMonkConfiguration(MonkConfiguration configuration);

    public List<MonkAttr> getMonkMetadata();

    public List<MonkConfigurationValue> getUnitMonkConfigurationData(int unitId);

    public void changeUnitLiveStatus(int unitId, boolean status) throws DataNotFoundException;

	public UnitProductData setProductAliases(UnitProductData unitProductData, int brandId);

    Integer getPriceProfileUnitId(Integer brandId, int unitId, Integer partnerId);

	public Unit changeUnitStatusForDineInAndChaayos(int unitId, Boolean dineIn, Boolean chaayos) throws DataUpdationException, DataNotFoundException;

	void cloneDayCloseDataForUnit(int newUnitId, int cloningUnitId) throws IOException;

	boolean addPaymentMethod(PaymentMode paymentMode);

	List<com.stpl.tech.master.domain.model.PaymentMode> getPaymentMethod();

	Boolean updatePaymentMethod(PaymentMode paymentMode);

    void updatePaymentModeMappingMethod(List<UnitPaymentModeMappingDetail> mapping);

 	List<UnitPaymentModeMappingDetail> getPaymentModeMapping(Integer id);

    public void updateUnitF9Flag(Integer unitId);

	public boolean setSuggestWalletStatus(CacheReferenceMetadata flag);

	public boolean addPartnerEdcMapping(UnitToPartnerEdcMappingDetail unitToPartnerEdcMappingDetail);

	public List<UnitToPartnerEdcMapping> getPartnerEdcMappingList(String status);

	public boolean updatePartnerEdcMapping(List<UnitToPartnerEdcMappingDetail> unitToPartnerEdcMappingDetails);

	boolean updateIpAddressForUnit(UnitIpAddressRequest unitIpAddressRequest) throws Exception;

	UnitIpAddressData getIpAddressForUnit(UnitIpAddressRequest unitIpAddressRequest);

	public Map<Integer,List<ApplicationVersionDetailData>> getUnitVersionMapping();

	public Integer createUpdateVersionEvent(List<VersionEventDomain> eventDomains);

	Map<String,List<String>> getAllActiveApplicationVersion();

	void updateCafePosVersion(String applicationName,String applicationVersion,Integer updatedBy,String releaseType, String buildName,String deploymentDescription);
	ApkUploadResponse uploadApkBuild(MultipartFile file, String s3OfferBucket);
	String getApkBuild(HttpServletResponse response,String applicationName,String applicationVersion) throws IOException;

	public Map<Integer,List<ApplicationVersionEvent>> getAllUnitVersionEvent();

	void addVersionCompatability(String applicationName, String applicationVersion, String posVersion, Integer updatedBy);

	List<String> getCompatibleApplicationVersion(String ApplicationName,String posVersion);

	public Map<String,UnitVersionDetail> getUnitVersions(UnitVersionDomain domain);

	public List<ApplicationVersionDetail> getApplicationVersionDetail(List<ApplicationVersionDomian> applicationVersionList);
	public List<String> getCompatiblePosVersion(String applicationName,String applicationVersion);

	public boolean updateFeedbackQuestions(FeedbackQuestionDomain domain);

	public List<FeedbackQuestionsDetail> getAllFeedBackQuestion(String questionType);

	public boolean deleteFeedBackQuestion(Integer questionId);

	public boolean addFeedbackUnitMapping(FeedbackQuesMappingDomain request);

	public boolean updateFeedbackUnitMapping(Integer mappingId,Integer updatedBy);

	public List<FeedbackQuestionsUnitMapping> getAllFeedbackQuestionUnitMapping(Integer questionId);

	public boolean changeQuestionStatus(Integer questionId,Integer updatedBy);

	public boolean bulkUpdateFeedbackUnitMapping(FeedbackQuesMappingDomain domain);

	public boolean addUnitContacts(UnitContactDetails unitContactDetails);
	boolean deactivateUnit(int unit, Integer userId) throws DataNotFoundException, DataUpdationException, IOException;

	public List<UnitToPartnerDqrMapping> getPartnerDqrMappingList(String status);

	public boolean addPartnerDqrMapping(UnitToPartnerDqrMapping unitToPartnerDqrMapping);

	public boolean updatePartnerDqrMapping(List<UnitToPartnerDqrMapping> unitToPartnerDqrMappings);

    UnitWsToStationCategoryMappingRequest getUnitWsToStationCategoryMapping(Integer unitId);

	Set<String> getAllStations();

	boolean addUnitWsToStationCategoryMapping(UnitWsToStationCategoryMappingRequest wsToStationCategoryMapping);

	Map<String, Set<Pair<Integer, String>>> getLocationByZone();

	List<UnitBasicDetail> getAllTestingUnits(String categroy);

    boolean syncUnitProductPricing(Integer fromUnitId, Integer toUnitId, Integer loggedInUser);

    List<BrandMapping> getPartnerMappingUnits(Integer brandId);

	public List<Brand> getUnitBrandMappings(Integer unitId);

	public Boolean addUnitBrandMappings(Integer unitId, List<Brand> brands, Integer userId);

	/**
	 * Retrieves contact data for a specific unit
	 *
	 * @param unitId the unit ID to fetch contacts for
	 * @return list of unit contact details
	 */
	List<UnitContactData> getUnitContactData(Integer unitId);

	/**
	 * Adds or updates unit contact data in bulk
	 *
	 * @param unitContactData the contact data to save/update
	 * @param loggedInUser the user performing the operation
	 * @return true if operation was successful, false otherwise
	 */
	boolean addOrUpdateUnitContactData(UnitContactData unitContactData, Integer loggedInUser);

}
