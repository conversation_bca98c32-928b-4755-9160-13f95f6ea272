package com.stpl.tech.master.core.config.attribute.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.config.attribute.dao.ConfigAttributeDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.ConfigAttributeValueData;

@Repository
public class ConfigAttributeDaoImpl extends AbstractMasterDaoImpl  implements ConfigAttributeDao {

	@Override
	public List<ConfigAttributeValueData> getCompanyAttributes(String applicationName) {
		Query query = manager.createQuery("FROM ConfigAttributeValueData C Where c.applicationName = :applicationName");
		query.setParameter("applicationName", applicationName);
		List<ConfigAttributeValueData> resultList = query.getResultList();
		return resultList;
	}
	
	@Override
	public List<ConfigAttributeValueData> getAllAttributeValues() {
		Query query = manager.createQuery("FROM ConfigAttributeValueData C");
		List<ConfigAttributeValueData> resultList = query.getResultList();
		return resultList;
	}

}
