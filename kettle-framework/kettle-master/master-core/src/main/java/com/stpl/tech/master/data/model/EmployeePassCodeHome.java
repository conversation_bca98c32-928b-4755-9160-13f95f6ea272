/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 14 Jul, 2015 1:35:15 AM by Hibernate Tools 4.0.0

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * Home object for domain model class EmployeePassCode.
 * 
 * @see com.stpl.tech.master.data.model.EmployeePassCode
 * <AUTHOR> Tools
 */
@Stateless
public class EmployeePassCodeHome {

	private static final Log log = LogFactory.getLog(EmployeePassCodeHome.class);

	@PersistenceContext
	private EntityManager entityManager;

	public void persist(EmployeePassCode transientInstance) {
		log.debug("persisting EmployeePassCode instance");
		try {
			entityManager.persist(transientInstance);
			log.debug("persist successful");
		} catch (RuntimeException re) {
			log.error("persist failed", re);
			throw re;
		}
	}

	public void remove(EmployeePassCode persistentInstance) {
		log.debug("removing EmployeePassCode instance");
		try {
			entityManager.remove(persistentInstance);
			log.debug("remove successful");
		} catch (RuntimeException re) {
			log.error("remove failed", re);
			throw re;
		}
	}

	public EmployeePassCode merge(EmployeePassCode detachedInstance) {
		log.debug("merging EmployeePassCode instance");
		try {
			EmployeePassCode result = entityManager.merge(detachedInstance);
			log.debug("merge successful");
			return result;
		} catch (RuntimeException re) {
			log.error("merge failed", re);
			throw re;
		}
	}

	public EmployeePassCode findById(Integer id) {
		log.debug("getting EmployeePassCode instance with productId: " + id);
		try {
			EmployeePassCode instance = entityManager.find(EmployeePassCode.class, id);
			log.debug("get successful");
			return instance;
		} catch (RuntimeException re) {
			log.error("get failed", re);
			throw re;
		}
	}
}
