package com.stpl.tech.master.core.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.SCMServiceEndpoints;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.data.vo.MonkRecipeDetailVo;
import com.stpl.tech.master.core.data.vo.ScmMissingPriceResponse;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.InvalidRequestException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.SessionCache;
import com.stpl.tech.master.core.service.IdGeneratorService;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.ProductManagementService;
import com.stpl.tech.master.core.service.RecipeService;
import com.stpl.tech.master.data.dao.BasicInfoDao;
import com.stpl.tech.master.data.dao.CompositeIngredientDataDao;
import com.stpl.tech.master.data.dao.CompositeProductDataDao;
import com.stpl.tech.master.data.dao.DispenserConfigDao;
import com.stpl.tech.master.data.dao.DispenserRecipeDao;
import com.stpl.tech.master.data.dao.IngredientDetailDao;
import com.stpl.tech.master.data.dao.IngredientProductDao;
import com.stpl.tech.master.data.dao.IngredientProductDetailDao;
import com.stpl.tech.master.data.dao.IngredientVariantDao;
import com.stpl.tech.master.data.dao.MonkRecipeDao;
import com.stpl.tech.master.data.dao.MonkRecipeVersioningDao;
import com.stpl.tech.master.data.dao.ProductDataDao;
import com.stpl.tech.master.data.dao.RecipeCostDao;
import com.stpl.tech.master.data.dao.RecipeDao;
import com.stpl.tech.master.data.dao.RecipeIterationDao;
import com.stpl.tech.master.data.dao.RecipeMediaDao;
import com.stpl.tech.master.data.dao.RecipeProfileDao;
import com.stpl.tech.master.data.dao.RecipeUpdateLogDetailDao;
import com.stpl.tech.master.data.dao.impl.MonkRecipeVersionStatusDao;
import com.stpl.tech.master.data.model.CriticalProductMenuToSCMMapData;
import com.stpl.tech.master.data.model.MenuToSCMProductMapData;
import com.stpl.tech.master.data.model.MonkRecipeVersionDetail;
import com.stpl.tech.master.domain.model.CondimentGroupData;
import com.stpl.tech.master.domain.model.DispenserBulkRequest;
import com.stpl.tech.master.domain.model.DispenserRecipeRequest;
import com.stpl.tech.master.domain.model.DispenserTagAndRevolution;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPriceProfile;
import com.stpl.tech.master.domain.model.RecipeApprovalDTO;
import com.stpl.tech.master.domain.model.TagValue;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitRegion;
import com.stpl.tech.master.domain.model.ProductRecipeMappingRequest;
import com.stpl.tech.master.notification.PendingRecipeApprovalNotification;
import com.stpl.tech.master.notification.PendingRecipeApprovalNotificationTemplate;
import com.stpl.tech.master.recipe.calculator.model.IterationIngredientDetail;
import com.stpl.tech.master.recipe.calculator.model.RecipeIterationDetail;
import com.stpl.tech.master.recipe.calculator.model.RecipeIterationStatus;
import com.stpl.tech.master.recipe.model.CondimentsData;
import com.stpl.tech.master.recipe.model.CondimentsDetail;
import com.stpl.tech.master.recipe.model.DispenserConfig;
import com.stpl.tech.master.recipe.model.DispenserRecipeDetail;
import com.stpl.tech.master.recipe.model.DispenserTagsMapping;
import com.stpl.tech.master.recipe.model.IngredientDetail;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.OptionData;
import com.stpl.tech.master.recipe.model.RecipeCondimentSheetDetails;
import com.stpl.tech.master.recipe.model.RecipeCostData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.model.RecipeMediaDetail;
import com.stpl.tech.master.recipe.model.RecipeMediaDetailData;
import com.stpl.tech.master.recipe.model.RecipeProductExcelData;
import com.stpl.tech.master.recipe.model.RecipeProfile;
import com.stpl.tech.master.recipe.model.RecipeStep;
import com.stpl.tech.master.recipe.model.RecipeUpdateLogDetail;
import com.stpl.tech.master.recipe.model.UploadRecipeMediaResponse;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeData;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeDetail;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeVersionData;
import com.stpl.tech.master.recipe.monk.model.MonkRecipes;
import com.stpl.tech.master.recipe.monk.model.MonkRecipesVersionRequest;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.master.recipe.monk.model.MonkVersionMetadata;
import com.stpl.tech.spring.exception.FileArchiveServiceException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class RecipeServiceImpl implements RecipeService {

    private static final Logger LOG = LoggerFactory.getLogger(RecipeServiceImpl.class);
    @Autowired
    private RecipeDao recipeDao;

    @Autowired
    private RecipeIterationDao recipeIterationDao;

    @Autowired
	private RecipeMediaDao recipeMediaDao;

	@Autowired
    private RecipeUpdateLogDetailDao recipeUpdateLogDetailDao;

    @Autowired
    private MonkRecipeDao monkRecipeDao;

    @Autowired
    private MonkRecipeVersioningDao monkRecipeVerisoningDao;

    @Autowired
    private RecipeCostDao recipeCostDao;

    @Autowired
    private IngredientDetailDao ingredientDetailDao;

    @Autowired
    private IngredientVariantDao ingredientVariantDao;

    @Autowired
    private ProductDataDao productDataDao;

    @Autowired
    private BasicInfoDao basicInfoDao;

    @Autowired
    private CompositeIngredientDataDao compositeIngredientDataDao;

    @Autowired
    private CompositeProductDataDao compositeProductDataDao;

    @Autowired
    private IngredientProductDao ingredientProductDao;

    @Autowired
    private IngredientProductDetailDao ingredientProductDetailDao;

    @Autowired
    private IdGeneratorService idService;

    @Autowired
    private MasterDataCacheService cache;

    @Autowired
    private RecipeProfileDao profileDao;

    @Autowired
    private ProductManagementService productManagementService;

    @Autowired
    private DispenserRecipeDao dispenserRecipeDao;

    @Autowired
    private DispenserConfigDao dispenserConfigDao;

    @Autowired
    private MasterProperties masterProperties;

    @Autowired
	private SessionCache sessionCache;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private MasterDataCache masterDataCache;

    @Autowired
    private MonkRecipeVersionStatusDao monkRecipeVersionStatusDao;

	@Override
    public int addRecipe(RecipeDetail detail) throws DataNotFoundException {
        detail.setRecipeId(idService.getNextId(RecipeDetail.class));
        saveIngredientDetail(detail);
        saveAddons(detail);
        saveRecommendations(detail);
        saveProduct(detail);
        detail.setCustomizationCount(countIngredientCustomization(detail));
        detail.setContainsCriticalProducts(containsCriticalProducts(detail));
        setCondimentDetails(detail);
        detail.setApprovalRequestedBy(null);
        detail.setApprovedBy(null);
        detail.setApprovedByName(null);
        RecipeDetail data = recipeDao.save(detail);
        if (detail.getStatus() == "ACTIVE") {
            cache.addRecipe(data);
        }
        return data.getRecipeId();
    }

    @Override
    public int addRecipeAfterCheck(RecipeDetail detail) throws InvalidRequestException {
        // checking it is the first Recipe Available For that Recipe So that They Can Map It
        List<RecipeDetail> currentRunningRecipes = recipeDao.findRecipeByStatusesOfProduct(detail.getProduct().getProductId(), Arrays.asList("IN_PROGRESS", "PENDING_APPROVAL", "ACTIVE"), detail.getDimension().getInfoId(), detail.getProfile());
        if (!currentRunningRecipes.isEmpty()) {
            boolean hasInProgressOrPendingApprovalRecipe = false;
            boolean hasActiveRecipe = false;
            for (RecipeDetail recipeDetail : currentRunningRecipes) {
                if (recipeDetail.getStatus().equalsIgnoreCase("ACTIVE")) {
                    hasActiveRecipe = true;
                }
                if (recipeDetail.getStatus().equalsIgnoreCase("IN_PROGRESS") || recipeDetail.getStatus().equalsIgnoreCase("PENDING_APPROVAL")) {
                    hasInProgressOrPendingApprovalRecipe = true;
                }
            }
            if (hasActiveRecipe && hasInProgressOrPendingApprovalRecipe) {
                throw new InvalidRequestException("Selected Product " + detail.getProduct().getName() + "Of Dimension " + detail.getDimension().getName() + "Already Had a ACTIVE Recipe and IN_PROGRESS Recipe Can not Active This Recipe");
            } else {
                if (hasInProgressOrPendingApprovalRecipe) {
                    throw new InvalidRequestException("Selected Product " + detail.getProduct().getName() + "Of Dimension " + detail.getDimension().getName() + "Already Had a IN_PROGRESS Recipe Can not Add This Recipe . Please Refresh and Edit the IN_PROGRESS Recipe");
                }
            }
        }
        detail.setRecipeId(idService.getNextId(RecipeDetail.class));
        saveIngredientDetail(detail);
        saveAddons(detail);
        saveRecommendations(detail);
        saveProduct(detail);
        detail.setCustomizationCount(countIngredientCustomization(detail));
        detail.setContainsCriticalProducts(containsCriticalProducts(detail));
        setCondimentDetails(detail);
        detail.setApprovalRequestedBy(null);
        detail.setApprovedBy(null);
        detail.setApprovedByName(null);
        RecipeDetail data = recipeDao.save(detail);
        return data.getRecipeId();
    }

    @Override
    public synchronized Boolean markInProgressToActiveRecipes(Integer recipeId, Integer userId) {
        try {
            List<RecipeDetail> aboutToActiveRecipes = Objects.nonNull(recipeId) ? findByRecipeId(recipeId) : recipeDao.findByStatus("IN_PROGRESS");
            List<RecipeApprovalDTO> recipeApprovalDTOS = new ArrayList<>();
            List<String> emailIds = new ArrayList<>();
            if (!aboutToActiveRecipes.isEmpty()) {
                for (RecipeDetail detail : aboutToActiveRecipes) {
                    // Get Each recipes List Of Recipies and Make Them Active After Checks
                    List<RecipeDetail> currentRunningRecipes = recipeDao.findRecipeByStatusesOfProduct(detail.getProduct().getProductId(), Collections.singletonList("ACTIVE"), detail.getDimension().getInfoId(), detail.getProfile());
                    if (currentRunningRecipes.isEmpty()) {
                        if ((AppUtils.getDate(detail.getStartDate()).compareTo(AppUtils.getDate(AppUtils.getCurrentTimestamp())) == 0
                                || AppUtils.getDate(detail.getStartDate()).compareTo(AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(), 1)) == 0)
                                && Objects.nonNull(detail.getApprovedBy())) {
                            //Making It Active As it is Going to Live Today Or Tomorrow
                            detail.setStatus(AppConstants.ACTIVE);
                            recipeApprovalDTOS.add(new RecipeApprovalDTO(detail.getRecipeId(), detail.getName(), detail.getDimension().getName(),detail.getProfile(),
                                    detail.getLastUpdatedById(), masterDataCache.getEmployee(detail.getLastUpdatedById())));
                            EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(detail.getLastUpdatedById());
                            if (Objects.nonNull(employeeBasicDetail) && Objects.nonNull(employeeBasicDetail.getEmailId())
                                    && !employeeBasicDetail.getEmailId().equalsIgnoreCase("")) {
                                emailIds.add(employeeBasicDetail.getEmailId());
                            }
                            recipeDao.save(detail);
                            RecipeUpdateLogDetail logDetail = new RecipeUpdateLogDetail();
                            logDetail.setRecipeId(detail.getRecipeId());
                            logDetail.setUpdateLog("Changed To ACTIVE Status As No ACTIVE OR IN_PROGRESS Recipes Found..!");
                            logDetail.setUpdatedBy(new IdCodeName(Objects.nonNull(recipeId) ? userId : AppConstants.SYSTEM_EMPLOYEE_ID,"",Objects.nonNull(recipeId) ? masterDataCache.getEmployee(userId) : AppConstants.SYSTEM_EMPLOYEE_NAME));
                            recipeUpdateLogDetailDao.save(logDetail);
                            cache.addRecipe(detail);
                        }
                    }
                }
            }
            if (!recipeApprovalDTOS.isEmpty()) {
                try {
                    PendingRecipeApprovalNotificationTemplate template = new PendingRecipeApprovalNotificationTemplate(recipeApprovalDTOS, masterProperties.getBasePath(), "MARKED_AS_ACTIVE");
                    PendingRecipeApprovalNotification notification = new PendingRecipeApprovalNotification(emailIds, masterProperties.getEnvironmentType(), template, "MARKED_AS_ACTIVE");
                    notification.sendEmail();
                } catch (Exception e) {
                    LOG.error("Exception Occurred While Sending Email Notification For Recipe ::: ", e);
                }
            }
            return true;
        } catch (Exception e) {
            LOG.error("Exception in markInProgressToActiveRecipes :: ", e);
            return false;
        }
    }

    private List<RecipeDetail> findActiveProductsByProductIdAndDimension(int productId, int dimensionId, String profile) {
        LOG.info("Getting Active Recipe for product id {} and dimension id {} ", productId, dimensionId);
        return recipeDao.findRecipeByStatusesOfProduct(productId, Arrays.asList(AppConstants.ACTIVE),dimensionId, profile);
    }

    @Override
	public int addRecipeMedia(RecipeMediaDetail detail) throws DataNotFoundException {
		RecipeMediaDetail data = recipeMediaDao.save(detail);
		return data.getRecipeId();
	}

	@Override
	public UploadRecipeMediaResponse saveRecipeMedia(String mediaType, MultipartFile file) {
		UploadRecipeMediaResponse uploadRecipeMediaResponse = new UploadRecipeMediaResponse();

		try{
			String baseDir = "media";
			String fileName = file.getOriginalFilename();
			fileName = fileName.replaceAll(" ", "_").toLowerCase();

			FileDetail s3File = fileArchiveService.saveFileToS3(masterProperties.getS3RecipeMediaBucket(), baseDir, fileName,
					file, true);
			if(s3File != null){
				uploadRecipeMediaResponse.setS3Key(s3File.getKey());
				return uploadRecipeMediaResponse;
			} else{
				return uploadRecipeMediaResponse;
			}
		} catch (FileArchiveServiceException e){
			LOG.error("Encountered error while uploading Recipe Media Image to S3", e);
		}

		return uploadRecipeMediaResponse;
	}

	@Override
	public int updateRecipeWithImage(RecipeDetail detail) {
//		detail.setCustomizationCount(countIngredientCustomization(detail));
//		detail.setContainsCriticalProducts(containsCriticalProducts(detail));
		try {
			RecipeDetail data = recipeDao.save(detail);
			if(data.getStatus().equalsIgnoreCase("ACTIVE")){
				cache.addRecipe(data);
			}
		} catch (Exception e) {
			LOG.error("Error updating recipe:::", e);

		}
		return detail.getRecipeId();
	}

    public void setCondimentDetails(RecipeDetail detail){
        if (Objects.nonNull(detail.getCondiments())){
            CondimentsDetail condimentsDetail = CondimentsDetail.builder().build();
            if(Objects.nonNull(detail.getCondiments().getDineIn()) &&
                    Objects.nonNull(detail.getCondiments().getDineIn().getGroupId()) &&
                    Objects.nonNull(detail.getCondiments().getDineIn().getGroupName()) && Objects.nonNull(detail.getCondiments().getDineIn().getQuantity())){
                CondimentsData dinInData = CondimentsData.builder().groupId(detail.getCondiments().getDineIn().getGroupId()).groupName(detail.getCondiments().getDineIn().getGroupName()).quantity(detail.getCondiments().getDineIn().getQuantity()).build();
                condimentsDetail.setDineIn(dinInData);
            }
            if(Objects.nonNull(detail.getCondiments().getDelivery()) &&
                    Objects.nonNull(detail.getCondiments().getDelivery().getGroupId()) &&
                    Objects.nonNull(detail.getCondiments().getDelivery().getGroupName()) && Objects.nonNull(detail.getCondiments().getDelivery().getQuantity())){
                CondimentsData deliveryData = CondimentsData.builder().groupId(detail.getCondiments().getDelivery().getGroupId()).groupName(detail.getCondiments().getDelivery().getGroupName()).quantity(detail.getCondiments().getDelivery().getQuantity()).build();
                condimentsDetail.setDelivery(deliveryData);
            }
            if(Objects.nonNull(detail.getCondiments().getTakeaway()) &&
                    Objects.nonNull(detail.getCondiments().getTakeaway().getGroupId()) &&
                    Objects.nonNull(detail.getCondiments().getTakeaway().getGroupName()) && Objects.nonNull(detail.getCondiments().getTakeaway().getQuantity())){
                CondimentsData takeAwayData = CondimentsData.builder().groupId(detail.getCondiments().getTakeaway().getGroupId()).groupName(detail.getCondiments().getTakeaway().getGroupName()).quantity(detail.getCondiments().getTakeaway().getQuantity()).build();
                condimentsDetail.setTakeaway(takeAwayData);
            }
            detail.setCondiments(condimentsDetail);
        }
    }

	@Override
    public int updateRecipe(RecipeDetail detail) throws DataNotFoundException {
        List<RecipeDetail> updatedRecipes = new ArrayList<>();
        detail.setCustomizationCount(countIngredientCustomization(detail));
        detail.setContainsCriticalProducts(containsCriticalProducts(detail));
        setCondimentDetails(detail);
        RecipeDetail data = recipeDao.save(detail);
        updatedRecipes.add(data);
        LOG.info("recipe data:::" + new Gson().toJson(data));

        /*List<RecipeDetail> recipeProfiles = recipeDao.findByProductIdAndUom(detail.getProduct().getProductId(),
                detail.getDimension().getCode());

		  for (RecipeDetail recipeDetail : recipeProfiles) {
            if (!recipeDetail.getProfile().equalsIgnoreCase(AppConstants.DEFAULT_RECIPE_PROFILE)) {
                recipeDetail.setAddons(data.getAddons() != null ? data.getAddons() : new ArrayList<>());
                recipeDetail.setOptions(data.getOptions() != null ? data.getOptions() : new ArrayList<>());
                recipeDetail.getIngredient().setVariants(data.getIngredient().getVariants() != null
                        ? data.getIngredient().getVariants() : new ArrayList<>());
                recipeDetail.getIngredient().setProducts(data.getIngredient().getProducts() != null
                        ? data.getIngredient().getProducts() : new ArrayList<>());
                if (data.getIngredient().getCompositeProduct() != null) {
                    recipeDetail.getIngredient().setCompositeProduct(data.getIngredient().getCompositeProduct());
                }

		  RecipeDetail updatedRecipe = recipeDao.save(recipeDetail);
                System.out.println("Recipe updated with recipe id : " + updatedRecipe.getRecipeId() + " and profile : "
                        + updatedRecipe.getProfile());
                updatedRecipes.add(updatedRecipe);
            }
        }*/
        try {
            for (RecipeDetail recipeData : updatedRecipes) {
                LOG.info("updated recipe data:::" + new Gson().toJson(recipeData));
                if (recipeData.getStatus().equalsIgnoreCase("ACTIVE")) {
                    cache.addRecipe(recipeData);
                    updateProductionMapping(recipeData.getProduct().getProductId(),recipeData.getProfile());
                }
            }
        } catch (Exception e) {
            LOG.error("Error updating recipe:::", e);

        }

        return detail.getRecipeId();
    }

    private void updateProductionMapping(int productId, String profile) {
        LOG.info("Inactive all Product Planning Mapping with productId{} and Profile{}", productId, profile);
        IdCodeName data = new IdCodeName();
        data.setId(productId);
        data.setName(profile);
        //WEB SERVICE HELPER  DATA= productId, profile
        String endpoint = masterProperties.getSCMEndpoint() + SCMServiceEndpoints.INACTIVE_MAPPING;
        String auth = masterProperties.getSCMAuthInternal();
        try {
           HttpResponse response= WebServiceHelper.postRequestWithAuthInternalTimeout(endpoint, auth, data);
           LOG.info("Product Mapping response  is {}",response);
        } catch (Exception e) {
            LOG.info("error in updating production booking mapping", e);
        }
    }

    @Override
    public List<RecipeUpdateLogDetail> getUpdateLogs(Integer recipeId) {
        if (recipeId != null) {
            return recipeUpdateLogDetailDao.findAllByRecipeId(recipeId);
        }
        return null;
    }

    @Override
    public void updateRecipeLog(RecipeUpdateLogDetail detail) {
        recipeUpdateLogDetailDao.save(detail);
    }

    @Override
    public void updateAllRecipes(List<RecipeDetail> recipes) throws DataNotFoundException {
        for (RecipeDetail r : recipes) {
            r.setCustomizationCount(countIngredientCustomization(r));
            r.setContainsCriticalProducts(containsCriticalProducts(r));
        }
        recipeDao.saveAll(recipes);
        cache.loadCache(true);
    }

    private void saveIngredientDetail(RecipeDetail recipeDetail) {
        IngredientDetail ingredientDetail = recipeDetail.getIngredient();
        saveComboDetail(ingredientDetail);
        ingredientDetail.setComponents(save(ingredientProductDetailDao, ingredientDetail.getComponents()));
        ingredientDetail.setProducts(save(ingredientProductDao, ingredientDetail.getProducts()));
        ingredientDetail.setVariants(save(ingredientVariantDao, ingredientDetail.getVariants()));
        recipeDetail.setIngredient(ingredientDetailDao.save(ingredientDetail));
    }

    private int countIngredientCustomization(RecipeDetail recipeDetail) {
        int count = 0;
        if (recipeDetail.getIngredient().getProducts() != null
                && !recipeDetail.getIngredient().getProducts().isEmpty()) {
            for (IngredientProduct product : recipeDetail.getIngredient().getProducts()) {
                if (product.isCustomize()) {
                    count++;
                }
            }
        }
        if (recipeDetail.getIngredient().getVariants() != null
                && !recipeDetail.getIngredient().getVariants().isEmpty()) {
            for (IngredientVariant variant : recipeDetail.getIngredient().getVariants()) {
                if (variant.isCustomize()) {
                    count++;
                }
            }
        }

        if (recipeDetail.getAddons() != null && !recipeDetail.getAddons().isEmpty()) {
            for (IngredientProductDetail addon : recipeDetail.getAddons()) {
                if (addon.isCustomize()) {
                    count++;
                }
            }
        }

        return count;
    }

    private boolean containsCriticalProducts(RecipeDetail recipeDetail) {
        return recipeDetail.getIngredient().getComponents().stream().filter(IngredientProductDetail::isCritical)
                .findAny().isPresent();
    }

    private void saveComboDetail(IngredientDetail ingredientDetail) {
        if (ingredientDetail.getCompositeProduct() != null) {
            ingredientDetail.getCompositeProduct()
                    .setDetails(save(compositeIngredientDataDao, ingredientDetail.getCompositeProduct().getDetails()));
            ingredientDetail.setCompositeProduct(compositeProductDataDao.save(ingredientDetail.getCompositeProduct()));

        }
    }

    private void saveAddons(RecipeDetail recipeDetail) {
        recipeDetail.setAddons(save(ingredientProductDetailDao, recipeDetail.getAddons()));
        recipeDetail.setDineInConsumables(save(ingredientProductDetailDao, recipeDetail.getDineInConsumables()));
        recipeDetail.setDeliveryConsumables(save(ingredientProductDetailDao, recipeDetail.getDeliveryConsumables()));
        recipeDetail.setTakeawayConsumables(save(ingredientProductDetailDao, recipeDetail.getTakeawayConsumables()));
    }

    //    private void saveMandatoryAddons(RecipeDetail recipeDetail) {
//        recipeDetail.setMandatoryAddons(save(ingredientProductDetailDao, recipeDetail.getMandatoryAddons()));
//    }
    private void saveProduct(RecipeDetail recipeDetail) {
        recipeDetail.setProduct(productDataDao.save(recipeDetail.getProduct()));
        recipeDetail.setDimension(basicInfoDao.save(recipeDetail.getDimension()));
    }

    private void saveRecommendations(RecipeDetail recipeDetail) {
        recipeDetail.setRecommendations(save(ingredientProductDetailDao, recipeDetail.getRecommendations()));
    }

    private <T> List<T> save(MongoRepository<T, String> dao, Iterable<T> list) {
        List<T> finalList = null;
        if (list != null) {
            finalList = new ArrayList<T>();
            Iterable<T> records = dao.saveAll(list);
            for (T record : records) {
                finalList.add(record);
            }
        }
        return finalList;

    }

    @Override
    public List<RecipeDetail> findByName(String name) {
        return recipeDao.findByName(name);
    }

    @Override
    public List<RecipeDetail> findByProductIdAndDimension(int productId, int dimensionId) {
        LOG.info("Getting Recipe for product id {} and dimension id {} ", productId, dimensionId);
        return recipeDao.findByProductIdAndDimension(productId, dimensionId);
    }

    @Override
    public List<RecipeDetail> findByProductIdAndUom(int productId, String uom) {
        LOG.info("Getting Recipe for product id {} and dimension id {} ", productId, uom);
        return recipeDao.findByProductIdAndUom(productId, uom);
    }

    @Override
    public List<RecipeDetail> findByStatus(String status) {
        return recipeDao.findByStatus(status);
    }

    @Override
    public List<IdCodeName> removeRecipe(RecipeDetail detail) {
        try {
            List<ProductPriceProfile> list = productManagementService
                    .getProductProfileUnits(detail.getProduct().getProductId(), detail.getProfile());
            List<IdCodeName> units = new ArrayList<>();
            for (ProductPriceProfile profile : list) {
                IdCodeName unitDetail = profile.getUnitDetails();
                if (profile.getProductPrice().getDimension().equals(detail.getDimension().getName())) {
                    units.add(new IdCodeName(unitDetail.getId(), unitDetail.getName(), null, null, null,
                            unitDetail.getStatus()));
                }
            }
            if (units.isEmpty()) {
                recipeDao.delete(detail);
            }
            return units;
        } catch (Exception e) {
            LOG.error("ERROR while deleting recipe", e);
        }
        return null;
    }

    @Override
    public List<RecipeDetail> findAll() {
        List<RecipeDetail> details = recipeDao.findAllNonSCM();
        details = filterDetailsByCompanyAndBrand(details);
        return details;
    }


    //TODO This needs to be optimized - Mohit
	@Override
	public List<RecipeDetail> findAll(List<Integer> excludeIds) {
		List<RecipeDetail> result = new ArrayList<RecipeDetail>();
		List<RecipeDetail> all = recipeDao.findAllNonSCM();
        all = filterDetailsByCompanyAndBrand(all);
		Set<Integer> ids = new HashSet<>(excludeIds);
		for (RecipeDetail r : all) {
			if (!ids.contains(r.getRecipeId())) {
				result.add(r);
			}
		}
		return result;
	}

    @Override
    public List<RecipeDetail> findAllActive(List<Integer> excludeIds) {
        List<RecipeDetail> result = new ArrayList<RecipeDetail>();
        List<RecipeDetail> all = recipeDao.findAllNonSCMAndActive();
        Set<Integer> ids = new HashSet<>(excludeIds);
        for (RecipeDetail r : all) {
            if (!ids.contains(r.getRecipeId())) {
                result.add(r);
            }
        }
        return result;
    }

    @Override
    public List<Integer> findDistinctRecipeIdInMenuToSCMProductMap() {
        return productManagementService.findDistinctRecipeIdInMenuToSCMProductMap();
    }

    @Override
    public List<RecipeDetail> findRecipeDetailNotInMenuToSCMMap(List<Integer> distinctRecipeId) {
        return recipeDao.findRecipeDetailNotInMTS(distinctRecipeId);
    }

    @Override
    public void saveAllMenuToScmProductMapData(List<MenuToSCMProductMapData> menuToSCMProductMapDataList) {
        productManagementService.saveMenuToSCMMapData(menuToSCMProductMapDataList);
    }

    @Override
    public List<MenuToSCMProductMapData> executeMenuToSCMProductMap() {
        productManagementService.truncateMenuToScmTable();
        List<RecipeDetail> recipeDetailList = recipeDao.findByStatus(AppConstants.ACTIVE);
        LOG.info("Total Active MENU Recipies are : {}", recipeDetailList.size());
        List<MenuToSCMProductMapData> menuToSCMProductMapDataList = new ArrayList<>();
        List<Integer> addOnProductIds = findDistinctAddonProductIds(recipeDetailList);
        List<RecipeDetail> addOnRecipeDetail = findRecipeDetailOfAddOn(addOnProductIds);
        LOG.info("Total Active ADD ON Recipies are : {}", addOnRecipeDetail.size());
        Map<String, List<CriticalProductMenuToSCMMapData>> addOnActiveProductsMap = getAddOnActiveProducts(addOnRecipeDetail);
        Map<RecipeDetail, List<CriticalProductMenuToSCMMapData>> criticalProductsMap = criticalProducts(recipeDetailList, addOnActiveProductsMap);
        for (Map.Entry<RecipeDetail, List<CriticalProductMenuToSCMMapData>> entry : criticalProductsMap.entrySet()) {
            for (CriticalProductMenuToSCMMapData productKey : entry.getValue()) {
                menuToSCMProductMapDataList.add(converter(entry.getKey(), productKey));
            }
        }
        saveAllMenuToScmProductMapData(menuToSCMProductMapDataList);
        return menuToSCMProductMapDataList;
    }

    private Map<String, List<CriticalProductMenuToSCMMapData>> getAddOnActiveProducts(List<RecipeDetail> addOnRecipeDetail) {
        Map<String, List<CriticalProductMenuToSCMMapData>> result = new HashMap<>();
        try {
            for(RecipeDetail recipe : addOnRecipeDetail){
                if (recipe == null || recipe.getIngredient() == null) {
                    continue;
                }
                else {
                    if(recipe.getIngredient().getComponents()!=null) {
                        for (IngredientProductDetail ipd : recipe.getIngredient().getComponents()) {
                            if (ipd.getStatus().equalsIgnoreCase(AppConstants.ACTIVE) && Objects.nonNull(recipe.getProduct()) && Objects.nonNull(recipe.getProduct().getProductId())
                            && Objects.nonNull(recipe.getDimension()) && Objects.nonNull(recipe.getDimension().getCode())) {
                                String key = recipe.getProduct().getProductId() + "_" + recipe.getDimension().getCode();
                                if(result.containsKey(key)) {
                                    result.get(key).add(new CriticalProductMenuToSCMMapData(ipd.getProduct().getProductId(),
                                            ipd.getProduct().getName(),ipd.getUom().name(),ipd.getQuantity(),AppConstants.RTL_GROUP_ADDONS));
                                } else{
                                    List<CriticalProductMenuToSCMMapData> list=new ArrayList<>();
                                    list.add(new CriticalProductMenuToSCMMapData(ipd.getProduct().getProductId(),
                                            ipd.getProduct().getName(),ipd.getUom().name(),ipd.getQuantity(),AppConstants.RTL_GROUP_ADDONS));
                                    result.put(key,list);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting Addon Products Map ::: ",e);
        }
        return result;
    }

    private List<RecipeDetail> findRecipeDetailOfAddOn(List<Integer> addOnProductIds) {
        return recipeDao.findRecipeDetailOfAddOn(addOnProductIds);
    }

    private List<Integer> findDistinctAddonProductIds(List<RecipeDetail> recipeDetailList) {
        List<Integer> result = new ArrayList<>();
        try {
            recipeDetailList.forEach(e -> {
                if (Objects.nonNull(e.getAddons())) {
                    e.getAddons().forEach(addon -> {
                        if (Objects.nonNull(addon.getProduct()) && !result.contains(addon.getProduct().getProductId())) {
                            result.add(addon.getProduct().getProductId());
                        }
                    });
                }
            });
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting distinct add On Product Ids :: ",e);
        }
        return result;
    }

    @Override
	public Boolean updateRecipeProfileStatus(Integer recipeId, Integer userId) {
		try {
			List<RecipeDetail> data = recipeDao.findByRecipeId(recipeId);
			LOG.info("Found {} recipe with recipe Id : {}",data.size(),recipeId);
			for (RecipeDetail recipeDetail : data) {
				recipeDetail.setStatus(AppConstants.IN_ACTIVE);
				recipeDetail.setLastUpdatedById(userId);
				recipeDetail.setLastUpdatedByName(masterDataCache.getEmployee(userId));
				recipeDao.save(recipeDetail);
                RecipeUpdateLogDetail logDetail = new RecipeUpdateLogDetail();
                logDetail.setRecipeId(recipeDetail.getRecipeId());
                logDetail.setUpdateLog("Marking IN_ACTIVE");
                IdCodeName idCodeName = new IdCodeName();
                idCodeName.setId(userId);
                idCodeName.setName(masterDataCache.getEmployee(userId));
                logDetail.setUpdatedBy(idCodeName);
                logDetail.setUpdateTime(AppUtils.getCurrentTimestamp());
                updateRecipeLog(logDetail);
                cache.removeRecipeFromCache(recipeDetail);
			}
			return true;
		}
		catch (Exception e) {
			LOG.error("Error Occurred While Updating status :: ",e);
			return false;
		}
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getBulkUploadRecipeSheetTemplate() {
        return new AbstractXlsxView(){
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "RecipeCondimentSheetDetails.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<RecipeCondimentSheetDetails> all = new ArrayList<>();
                writer.writeSheet(all, RecipeCondimentSheetDetails.class);
            }
        };
    }


    @Override
    public List<RecipeDetail> bulkUploadRecipeCondiment(MultipartFile file){
        try {
            Workbook workbook;
            if (file.getName().endsWith("xls")) {
                workbook = new HSSFWorkbook(file.getInputStream());
            } else {
                workbook = new XSSFWorkbook(file.getInputStream());
            }
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<RecipeCondimentSheetDetails> entityList = parser.createEntity(workbook.getSheetAt(0), RecipeCondimentSheetDetails.class, errors::add);
            if (errors.isEmpty()){
                for (RecipeCondimentSheetDetails sheetDetails : entityList){
                    if(Objects.nonNull(sheetDetails.getProductId()) && Objects.nonNull(sheetDetails.getDimension())){
                        //List<RecipeDetail> recipeDetailList = findByProductIdAndDimension(sheetDetails.getProductId(), sheetDetails.getDimensionId());
                        List<RecipeDetail> recipeDetailList = findByProductIdAndUom(sheetDetails.getProductId(), sheetDetails.getDimension());
                        if(Objects.nonNull(recipeDetailList) && recipeDetailList.size()>0){
                            for(RecipeDetail recipeDetail: recipeDetailList){
                                CondimentsDetail condimentsDetail = CondimentsDetail.builder().build();
                                if(Objects.nonNull(recipeDetail) && Objects.nonNull(recipeDetail.getCondiments())){
                                    if(Objects.nonNull(recipeDetail.getCondiments().getDineIn()) && Objects.nonNull(recipeDetail.getCondiments().getDineIn().getGroupId())){
                                        condimentsDetail.setDineIn(recipeDetail.getCondiments().getDineIn());
                                    }
                                    if(Objects.nonNull(recipeDetail.getCondiments().getDelivery()) && Objects.nonNull(recipeDetail.getCondiments().getDelivery().getGroupId())){
                                        condimentsDetail.setDelivery(recipeDetail.getCondiments().getDelivery());
                                    }
                                    if(Objects.nonNull(recipeDetail.getCondiments().getTakeaway()) && Objects.nonNull(recipeDetail.getCondiments().getTakeaway().getGroupId())){
                                        condimentsDetail.setTakeaway(recipeDetail.getCondiments().getTakeaway());
                                    }
                                }
                                if(Objects.nonNull(sheetDetails.getDineInCGId()) && sheetDetails.getDineInCGId()>0 && Objects.nonNull(sheetDetails.getDineInCGName()) && Objects.nonNull(sheetDetails.getDineInCGQuantity()) && sheetDetails.getDineInCGQuantity()>0){
                                    CondimentsData dinInData = CondimentsData.builder().groupId(sheetDetails.getDineInCGId()).groupName(sheetDetails.getDineInCGName()).quantity(sheetDetails.getDineInCGQuantity()).build();
                                    condimentsDetail.setDineIn(dinInData);
                                }
                                if(Objects.nonNull(sheetDetails.getTakeawayCGId())  && sheetDetails.getTakeawayCGId() >0 && Objects.nonNull(sheetDetails.getTakeawayCGName()) && Objects.nonNull(sheetDetails.getTakeawayCGQuantity()) && sheetDetails.getTakeawayCGQuantity()>0){
                                    CondimentsData takeawayData = CondimentsData.builder().groupId(sheetDetails.getTakeawayCGId()).groupName(sheetDetails.getTakeawayCGName()).quantity(sheetDetails.getTakeawayCGQuantity()).build();
                                    condimentsDetail.setTakeaway(takeawayData);
                                }
                                if(Objects.nonNull(sheetDetails.getDeliveryCGId()) && sheetDetails.getDeliveryCGId() >0 && Objects.nonNull(sheetDetails.getDeliveryCGName()) && Objects.nonNull(sheetDetails.getDeliveryCGQuantity()) && sheetDetails.getDeliveryCGQuantity() >0){
                                    CondimentsData deliveryData = CondimentsData.builder().groupId(sheetDetails.getDeliveryCGId()).groupName(sheetDetails.getDeliveryCGName()).quantity(sheetDetails.getDeliveryCGQuantity()).build();
                                    condimentsDetail.setDelivery(deliveryData);
                                }
                                recipeDetail.setCondiments(condimentsDetail);
                            }
                            recipeDao.saveAll(recipeDetailList);

                        }
                    }
                }
                //cache.loadCache(true);
            }else {
                LOG.info("Error parsing Recipe Media Workbook , total errors: {}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage() + '\n'));
                LOG.info("{}", sb.toString());
                throw new ExcelParsingException(sb.toString());
            }
            workbook.close();

        }catch (Exception e){
            LOG.error("Error while Bulk uploading Recipe Condiment Sheet", e);
        }
        return null;
    }


	@Override
	public List<RecipeMediaDetail> bulkUploadRecipeMedia(MultipartFile file) {
		ArrayList<RecipeMediaDetail> response = new ArrayList<>();
		try {
			Workbook workbook;
			if (file.getName().endsWith("xls")) {
				workbook = new HSSFWorkbook(file.getInputStream());
			} else {
				workbook = new XSSFWorkbook(file.getInputStream());
			}
			List<ExcelParsingException> errors = new ArrayList<>();
			SheetParser parser = new SheetParser();
			List<RecipeMediaDetailData> entityList = parser.createEntity(workbook.getSheetAt(0), RecipeMediaDetailData.class, errors::add);
			if (errors.isEmpty()) {
				for (RecipeMediaDetailData recipeMediaData : entityList) {
					if (Objects.nonNull(recipeMediaData.getRecipeId()) && Objects.nonNull(recipeMediaData.getImage1()) && !recipeMediaData.getImage1().isEmpty()) {
						int count = 1;
						String jpegImageType = MimeType.JPEG.value();
						String stepHeading = "Step ";
						String keyFolder = "media/";
						RecipeDetail recipeDetail = findByRecipeId(recipeMediaData.getRecipeId()).get(0);
						RecipeMediaDetail recipeMediaDetail = new RecipeMediaDetail();
						recipeMediaDetail.setRecipeId(recipeMediaData.getRecipeId());
						recipeMediaDetail.setName(recipeMediaData.getName());
						recipeMediaDetail.setProduct(recipeDetail.getProduct());
						recipeMediaDetail.setDimension(recipeDetail.getDimension());

						ArrayList<RecipeStep> steps = new ArrayList<>();
						RecipeStep image1 = new RecipeStep();
						String fileName = recipeMediaData.getImage1().replaceAll(" ", "_").toLowerCase();
						image1.setMediaType(jpegImageType);
						image1.setRecipeStepName(stepHeading + count);
						image1.setS3Key(keyFolder + fileName);
                        count += 1;
                        steps.add(image1);
                        if (Objects.nonNull(recipeMediaData.getImage2()) && !recipeMediaData.getImage2().isEmpty()) {
                            RecipeStep image2 = new RecipeStep();
                            fileName = recipeMediaData.getImage2().replaceAll(" ", "_").toLowerCase();
                            image2.setMediaType(jpegImageType);
                            image2.setRecipeStepName(stepHeading + count);
                            image2.setS3Key(keyFolder + fileName);
                            count += 1;
                            steps.add(image2);
                        }
                        if (Objects.nonNull(recipeMediaData.getImage3()) && !recipeMediaData.getImage3().isEmpty()) {
                            RecipeStep image3 = new RecipeStep();
                            fileName = recipeMediaData.getImage3().replaceAll(" ", "_").toLowerCase();
                            image3.setMediaType(jpegImageType);
                            image3.setRecipeStepName(stepHeading + count);
							image3.setS3Key(keyFolder + fileName);
							steps.add(image3);
						}
						recipeMediaDetail.setRecipeSteps(steps);
						addRecipeMedia(recipeMediaDetail);
						response.add(recipeMediaDetail);
					}
				}
			} else {
				LOG.info("Error parsing Recipe Media Workbook , total errors: {}", errors.size());
				StringBuilder sb = new StringBuilder();
				errors.forEach(e -> sb.append(e.getMessage() + '\n'));
				LOG.info("{}", sb.toString());
				throw new ExcelParsingException(sb.toString());
			}
			workbook.close();
		} catch (Exception e) {
			LOG.error("Error while Bulk uploading Recipe Media", e);
		}
		LOG.info("Bulk uploaded media for {} recipes", response.size());
		return response;
	}

	@Override
    public List<RecipeDetail> findAllSCM() {
        return recipeDao.findAllSCM();
    }

    @Override
    public List<RecipeDetail> findByRecipeId(int id) {
        return recipeDao.findByRecipeId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateRecipeCost(List<RecipeCostData> data) {
        if (data != null && !data.isEmpty()) {
            recipeCostDao.saveOrUpdateCostData(data);
        }
        return false;
    }

    @Override
    public List<RecipeDetail> findRecipesContainingName(String name) {
        List<RecipeDetail> details = recipeDao.findRecipesContainingName(name);
        details = filterDetailsByCompanyAndBrand(details);
        return details;
    }

    @Override
	public List<RecipeMediaDetail> findRecipeStepMediaDetailByRecipeId(int recipeId){
		return recipeMediaDao.findByRecipeId(recipeId);
	}

	@Override
    public List<RecipeDetail> findSCMRecipesContainingName(String name) {
        List<RecipeDetail> details = recipeDao.findSCMRecipesContainingName(name);
        return details;
    }

    @Override
    public RecipeDetail findSCMProductById(int productId) {
        return recipeDao.findSCMProductById(productId);
    }

    @Override
    public List<RecipeDetail> findSCMRecipeProductById(int productsId) {
        return recipeDao.findSCMRecipeProductById(productsId);
    }

    @Override
	public RecipeDetail findSCMRecipeProductByIdAndProfileAndStatus(int productId, String profile) {
		return recipeDao.findByProduct_productIdAndProfileAndStatus(productId, profile, AppConstants.ACTIVE);
	}

	@Override
    public List<RecipeDetail> findSCMRecipeContainigProductId(int productId) {
        LOG.info("Getting Recipe linked with  product id findSCMRecipeContainigProductId : ", productId);

        List<RecipeDetail> detailsList = new ArrayList<>();
        List<RecipeDetail> scmRecipeList = recipeDao.findAllSCM();
        for (RecipeDetail recipeDetail : scmRecipeList) {
            IngredientDetail ingredientDetail = recipeDetail.getIngredient();
            boolean found = false;
            if (ingredientDetail.getProducts() != null) {
                for (IngredientProduct ingredientProduct : ingredientDetail.getProducts()) {
                    if (ingredientProduct.getDetails() != null) {
                        for (IngredientProductDetail detail : ingredientProduct.getDetails()) {
                            if (detail.getProduct().getProductId() == productId) {
                                detailsList.add(recipeDetail);
                                found = true;
                                break;
                            }
                        }
                    }
                    if (found) {
                        break;
                    }
                }
            }
            if (!found && ingredientDetail.getVariants() != null) {
                for (IngredientVariant ingredientVariant : ingredientDetail.getVariants()) {
                    if (ingredientVariant.getProduct().getProductId() == productId) {
                        detailsList.add(recipeDetail);
                        found = true;
                        break;
                    }
                }
            }
            if (!found && ingredientDetail.getComponents() != null) {
                for (IngredientProductDetail ingredientVariant : ingredientDetail.getComponents()) {
                    if (ingredientVariant.getProduct().getProductId() == productId) {
                        detailsList.add(recipeDetail);
                        found = true;
                        break;
                    }
                }
            }
        }
        return detailsList;
    }

    @Override
    public List<RecipeDetail> findRecipeByRecipeId(Integer recipeId) {
        LOG.info("Getting Recipe With Recipe Id : {}", recipeId);
        List<RecipeDetail> details = recipeDao.findByRecipeId(recipeId);
        details = filterDetailsByCompanyAndBrand(details);
        return details;
    }

    @Override
    public Boolean reActivateRecipe(Integer recipeId, Integer productId, Integer dimensionInfoId, String dimension, Integer userId, String profile) throws InvalidRequestException {
        try {
            LOG.info("Trying To Re Activate Recipe Of recipe Id : {}", recipeId);
            List<RecipeDetail> recipeDetails = recipeDao.findRecipeByStatusesOfProduct(productId, Arrays.asList("IN_PROGRESS", "PENDING_APPROVAL", "ACTIVE"), dimensionInfoId, profile);
            boolean hasActiveRecipe = false;
            boolean hasInProgressOrPendingApprovalRecipe = false;
            for (RecipeDetail recipeDetail : recipeDetails) {
                if (recipeDetail.getStatus().equalsIgnoreCase("ACTIVE")) {
                    hasActiveRecipe = true;
                }
                if (recipeDetail.getStatus().equalsIgnoreCase("IN_PROGRESS") || recipeDetail.getStatus().equalsIgnoreCase("PENDING_APPROVAL")) {
                    hasInProgressOrPendingApprovalRecipe = true;
                }
            }
            if (hasActiveRecipe && hasInProgressOrPendingApprovalRecipe) {
                throw new InvalidRequestException("Selected Product " + productId + "Of Dimension " + dimension + "Already Had a ACTIVE Recipe and IN_PROGRESS Recipe Can not Active This Recipe");
            } else {
                if (hasInProgressOrPendingApprovalRecipe) {
                    throw new InvalidRequestException("Selected Product " + productId + "Of Dimension " + dimension + "Already Had a IN_PROGRESS Recipe Can not Re-Active This Recipe . Please Edit the IN_PROGRESS Recipe");
                }
                LOG.info("Re Activating Recipe ::: {}", recipeId);
                List<RecipeDetail> recipeDetailList = recipeDao.findByRecipeId(recipeId);
                if (recipeDetailList != null && !recipeDetailList.isEmpty()) {
                    RecipeDetail recipeDetail = recipeDetailList.get(0);
                    recipeDetail.setLastUpdatedById(userId);
                    recipeDetail.setStatus("IN_PROGRESS");
                    recipeDetail.setApprovedBy(null);
                    recipeDetail.setApprovedByName(null);
                    recipeDetail.setApprovalRequestedBy(null);
                    recipeDetail.setStartDate(AppUtils.getUtcDate(AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(), 1)));
                    recipeDao.save(recipeDetail);
                    RecipeUpdateLogDetail logDetail = new RecipeUpdateLogDetail();
                    logDetail.setRecipeId(recipeId);
                    logDetail.setUpdateLog("Request to Re Activate Recipe.");
                    logDetail.setUpdatedBy(new IdCodeName(userId, "", masterDataCache.getEmployee(userId)));
                    updateRecipeLog(logDetail);
                    return true;
                } else {
                    throw new InvalidRequestException("No Recipe Found With the Recipe Id : " + recipeId);
                }
            }
        } catch (InvalidRequestException e) {
            throw e;
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While Re Activating recipe :: ", e);
        }
        return false;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.service.RecipeService#
     * findMonkRecipeByProductIdAndDimension(int, int)
     */
    @Override
    public List<MonkRecipeDetail> findMonkRecipeByProductIdAndDimension(int productId, int dimensionId) {
        LOG.info("Getting Monk Recipe for product id {} and dimension id {} ", productId, dimensionId);
        return monkRecipeDao.findByProductIdAndDimension(productId, dimensionId);
    }

    @Override
    public Map<String,List<CondimentGroupData> > getSrcToCondimentMapping(){
        return cache.getSrcToCondimentMap();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.service.RecipeService#findAllMonk()
     */
    @Override
    public List<MonkRecipeDetail> findAllMonk() {
        return monkRecipeDao.findAll();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.service.RecipeService#
     * findMonkRecipesContainingName(java.lang.String)
     */
    @Override
    public List<MonkRecipeDetail> findMonkRecipesContainingName(String name) {
        return monkRecipeDao.findRecipesContainingName(name);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.RecipeService#addMonkRecipe(com.stpl.
     * tech.master.recipe.monk.model.MonkRecipeDetail)
     */
    @Override
    public int addMonkRecipe(int mode, MonkRecipeDetail detail) {
        detail.setRecipeId(idService.getNextId(RecipeDetail.class));
        detail.process(mode);
        MonkRecipeDetail data = monkRecipeDao.save(detail);
        return data.getRecipeId();
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.RecipeService#addMonkRecipe(com.stpl.
     * tech.master.recipe.monk.model.MonkRecipeDetail)
     */
    @Override
    public MonkRecipeVersionData addMonkRecipeVersion(MonkRecipeVersionData detail) {
        detail.setVersion(AppUtils.getCurrentTimeISTStringWithNoColons());
        MonkRecipeVersionData data = monkRecipeVerisoningDao.save(detail);
        return data;
    }

    @Override
    public String updateMonkRecipeVersion(MonkRecipeVersionData detail) {
        MonkRecipeVersionData data = monkRecipeVerisoningDao.save(detail);
        return data.getVersion();
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.RecipeService#updateMonkRecipe(com.stpl
     * .tech.master.recipe.monk.model.MonkRecipeDetail)
     */
    @Override
    public int updateMonkRecipe(int mode, MonkRecipeDetail detail) {
        detail.process(mode);
        MonkRecipeDetail data = monkRecipeDao.save(detail);
        return data.getRecipeId();
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.RecipeService#removeMonkRecipe(com.stpl
     * .tech.master.recipe.monk.model.MonkRecipeDetail)
     */
    @Override
    public boolean removeMonkRecipe(MonkRecipeDetail detail) {
        try {
            monkRecipeDao.delete(detail);
            return true;
        } catch (Exception e) {
            LOG.error("ERROR while deleting recipe", e);
        }
        return false;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.RecipeService#findVersionDataByStatus(
     * java.lang.String)
     */
    @Override
    public List<MonkRecipeVersionData> findVersionDataByStatus(String status) {
        return monkRecipeVerisoningDao.findByStatus(status);
    }

    @Override
	public List<MonkRecipeVersionData> findVersionDataByStatusAndRegion(String status, String region) {
		return monkRecipeVerisoningDao.findByStatusAndRegion(status, region);
	}

	@Override
    public List<RecipeProfile> getRecipeProfiles(String type) {
        return profileDao.getRecipeProfiles(type);
    }

    @Override
    public List<String> findProfilesByProductIdAndDimension(int productId, int dimensionId) {
        LOG.info("findProfilesByProductIdAndDimension product id and dimension id :  ", productId, dimensionId);
        List<RecipeDetail> details = recipeDao.findByProductIdAndDimension(productId, dimensionId);
        List<String> profiles = new ArrayList<>();
        details.forEach(recipe -> {
			if (recipe.getStatus().equalsIgnoreCase(AppConstants.ACTIVE)) {
				profiles.add(recipe.getProfile());
			}});
        return profiles;
    }

    @Override
    public Map<String, List<String>> findProfilesByProductId(int productId) {
        LOG.info("findProfilesByProductId for product id : ", productId);
        List<RecipeDetail> details = recipeDao.findByProductId(productId);
        Map<String, List<String>> dimensionMap = new HashMap<>();
        details.forEach(recipe -> {
            if (dimensionMap.get(recipe.getDimension().getCode()) == null) {
                List<String> profiles = new ArrayList<>();
                profiles.add(recipe.getProfile());
                dimensionMap.put(recipe.getDimension().getCode(), profiles);
            } else {
                dimensionMap.get(recipe.getDimension().getCode()).add(recipe.getProfile());
            }
        });
        return dimensionMap;
    }

    @Override
    public String getMonkRecipeVersion() {
        List<MonkRecipeVersionData> recipeVersions = findVersionDataByStatus("ACTIVATED");
        if (recipeVersions != null && recipeVersions.size() > 0) {
            MonkRecipeVersionData currentVersion = recipeVersions.get(0);
            return currentVersion.getVersion();
        }
        return null;
    }

	@Override
	public String getMonkRecipeVersion(String region) {
		List<MonkRecipeVersionData> recipeVersions = findVersionDataByStatusAndRegion("ACTIVATED", region);
		if (recipeVersions != null && recipeVersions.size() > 0) {
			MonkRecipeVersionData currentVersion = recipeVersions.get(0);
			return currentVersion.getVersion();
		}
		return null;
	}

    @Override
    public MonkRecipes checkAndSendMonkRecipes(Integer unitId, String version, Pair<String, String> unitMonkRecipeProfileVersion) {
        LOG.info("Request to check nd send the monk recipes for the version {}", version);
        List<MonkRecipeVersionData> recipeVersions;
        MonkRecipes recipes = new MonkRecipes();
        boolean checkBigPanThreshold = false;
        Unit unit = null;
        
        if (unitId != null) {
            unit = cache.getUnit(unitId);
            checkBigPanThreshold = AppConstants.getValue(unit.getCheckBigPanThreshold());
        }
        
        if (Objects.nonNull(unitMonkRecipeProfileVersion)) {
            LOG.info("Fetching Monk Recipe Through Cache : for unit id : {} and region : {} and recipe version : {}", unitId, unitMonkRecipeProfileVersion.getKey(), unitMonkRecipeProfileVersion.getValue());
            recipeVersions = monkRecipeVerisoningDao.findByStatusAndRegionAndVersion(AppConstants.ACTIVATED, unitMonkRecipeProfileVersion.getKey(), unitMonkRecipeProfileVersion.getValue());
        } else {
            String region = UnitRegion.NCR.value();
            if (unitId != null) {
                region = getMonkRecipeProfile(unit);
            }
            recipeVersions = monkRecipeVerisoningDao.findByStatusAndRegion("ACTIVATED", region);
        }

        if (recipeVersions != null && recipeVersions.size() > 0) {
            MonkRecipeVersionData currentVersion = recipeVersions.get(0);
            recipes.setVersion(currentVersion.getVersion());
            recipes.setRecipeRegion(currentVersion.getRegion());
            setRecipeData(recipes, currentVersion,checkBigPanThreshold);
        }
        return recipes;
    }

    private void setRecipeData(MonkRecipes recipes, MonkRecipeVersionData currentVersion,boolean checkBigPanThreshold) {
        Map<String, String> allRecipes = new HashMap<>();
        Map<String, MonkVersionMetadata> monkVersionMap = new HashMap<>();
        try{
            for (MonkRecipeData detail : currentVersion.getContent()) {
                String recipeString = detail.getContent();
                if(checkBigPanThreshold && recipeString!=null && recipeString.length()>26 && Integer.parseInt(recipeString.substring(21,22)) > 5){
                    recipeString = recipeString.substring(0,21)+"0"+recipeString.substring(22);
                }
                allRecipes.put(detail.getKey(), recipeString);
                monkVersionMap.put(detail.getKey(), getMonkVersion(detail));
            }
            recipes.setRecipes(allRecipes);
            recipes.setMonkVersionMap(monkVersionMap);
        }
        catch (Exception e){
            LOG.error("Not able to set Monk Recipe Data {}", e);
        }

    }

    @Override
    public MonkRecipes getMonkRecipes(Integer unitId) {
        LOG.info("Request to get the monk recipes for the unit {}", unitId);
        String region = UnitRegion.NCR.value();
        boolean checkBigPanThreshold = false;
        if (unitId != null) {
            Unit unit = cache.getUnit(unitId);
            region = getMonkRecipeProfile(unit);
            checkBigPanThreshold = AppConstants.getValue(unit.getCheckBigPanThreshold());

        }
        MonkRecipes recipes = new MonkRecipes();
        List<MonkRecipeVersionData> recipeVersions = monkRecipeVerisoningDao.findByStatusAndRegion("ACTIVATED", region);
        if (recipeVersions != null && recipeVersions.size() > 0) {
            MonkRecipeVersionData currentVersion = recipeVersions.get(0);
            recipes.setVersion(currentVersion.getVersion());
            recipes.setRecipeRegion(currentVersion.getRegion());
            if (currentVersion.getContent() != null) {
                Map<String, String> allRecipes = new HashMap<>();
                Map<String, MonkVersionMetadata> monkVersionMap = new HashMap<>();
                for (MonkRecipeData detail : currentVersion.getContent()) {
                    String recipeString = detail.getContent();
                    if(checkBigPanThreshold && recipeString!=null && recipeString.length()>26 && Integer.parseInt(recipeString.substring(21,22)) > 5){
                        recipeString = recipeString.substring(0,21)+"0"+recipeString.substring(22);
                    }
                    allRecipes.put(detail.getKey(), recipeString);
                    monkVersionMap.put(detail.getKey(), getMonkVersion(detail));
                }
                recipes.setRecipes(allRecipes);
                recipes.setMonkVersionMap(monkVersionMap);
            }
        }
        return recipes;
    }

    private MonkVersionMetadata getMonkVersion(MonkRecipeData detail) {
        MonkVersionMetadata monkVersionMetadata = new MonkVersionMetadata();
        if (Objects.nonNull(detail) && Objects.nonNull(detail.getMonkVersion())) {
             monkVersionMetadata.setMonkVersion(detail.getMonkVersion());
        } else {
            monkVersionMetadata.setMonkVersion(AppConstants.CHAI_MONK_DEFAULT_VERSION);
        }

        if (Objects.nonNull(detail) && Objects.nonNull(detail.getMilkVariantMonkVersion())) {
            monkVersionMetadata.setMilkVariantMonkVersion(detail.getMilkVariantMonkVersion());
        } else {
            monkVersionMetadata.setMilkVariantMonkVersion(AppConstants.CHAI_MONK_DEFAULT_VERSION);
        }
        if (Objects.nonNull(detail) && Objects.nonNull(detail.getAddons())) {
            monkVersionMetadata.setAddons(detail.getAddons());
        }
        return monkVersionMetadata;
    }

    private String getMonkRecipeProfile(Unit unit){
        if(Objects.nonNull(unit.getMonkRecipeProfile())){
            return unit.getMonkRecipeProfile();
        }
        return unit.getRegion();
    }

    @Override
    public DispenserRecipeDetail saveDispenserData(DispenserRecipeRequest dispenserRecipeRequest) {
        String recipekey = dispenserRecipeRequest.getProduct().getId() + "#" + dispenserRecipeRequest.getProfile() + "#" + dispenserRecipeRequest.getDimension().getName() + "$";

        DispenserRecipeDetail recipe = dispenserRecipeDao.findByRecipeKey(recipekey);
        if (recipe != null) {
            recipe.setProduct(dispenserRecipeRequest.getProduct());
            recipe.setDimension(dispenserRecipeRequest.getDimension());
            recipe.setProfile(dispenserRecipeRequest.getProfile());
            DispenserTagsMapping mapping = new DispenserTagsMapping();
            //for variants
            HashMap<String, TagValue> variants = new HashMap<>();
            mapping.setVariants(convert(dispenserRecipeRequest.getVariants(), variants));
            //for addons
            HashMap<String, TagValue> addons = new HashMap<>();
            mapping.setAddOns(convert(dispenserRecipeRequest.getAddOns(), addons));
            //for mandatoryAddons
            if (dispenserRecipeRequest.getMandatoryAddons() != null) {
                HashMap<String, TagValue> mandatoryAddons = new HashMap<>();
                mapping.setMandatoryAddons(convert(dispenserRecipeRequest.getMandatoryAddons(), mandatoryAddons));
            }
            recipe.setMapping(mapping);
        } else {
            recipe = new DispenserRecipeDetail();
            recipe.setProduct(dispenserRecipeRequest.getProduct());
            recipe.setDimension(dispenserRecipeRequest.getDimension());
            recipe.setProfile(dispenserRecipeRequest.getProfile());
            recipe.setRecipeKey(recipekey);
            DispenserTagsMapping mapping = new DispenserTagsMapping();
            //for variants
            HashMap<String, TagValue> variants = new HashMap<>();
            mapping.setVariants(convert(dispenserRecipeRequest.getVariants(), variants));
            //for addons
            HashMap<String, TagValue> addons = new HashMap<>();
            mapping.setAddOns(convert(dispenserRecipeRequest.getAddOns(), addons));
            //for mandatoryAddons
            if (dispenserRecipeRequest.getMandatoryAddons() != null) {
                HashMap<String, TagValue> mandatoryAddons = new HashMap<>();
                mapping.setMandatoryAddons(convert(dispenserRecipeRequest.getMandatoryAddons(), mandatoryAddons));
            }

            recipe.setMapping(mapping);
        }
        dispenserRecipeDao.save(recipe);
        return recipe;


    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveUpdatedVersionDetail(Integer unitId, String version, Integer count, Integer employeeId) {
        try{
            if(Objects.isNull(unitId) || Objects.isNull(version) || Objects.isNull(count) || Objects.isNull(employeeId)){
                LOG.info("Invalid Data ::::  unitId : {}, version : {}, count : {}, employeeId : {}", unitId, version, count, employeeId);
                return false;
            }
            MonkRecipeVersionDetail mRVD = monkRecipeVersionStatusDao.findByUnitId(unitId);
            LOG.info("New entry for unit is getting created ::::  unitId : {}, version : {}, count : {} ", unitId, version, count);
            if(Objects.nonNull(mRVD) && Objects.nonNull(mRVD.getCurrentVersion())
                    && mRVD.getCurrentVersion().equals(version)){
                LOG.info("Skipping entry as this version entry is already present unitId : {}, version : {}, count : {} ", unitId, version, count);
                return false;
            }
            monkRecipeVersionStatusDao.save(MonkRecipeVersionDetail
                    .builder()
                    .currentVersion(version)
                    .unitId(unitId)
                    .currentCount(count)
                    .previousVersion(Objects.nonNull(mRVD) ?  mRVD.getCurrentVersion() : null)
                    .previousCount(Objects.nonNull(mRVD) ? mRVD.getCurrentCount() : null)
                    .updateBy(employeeId)
                    .updatedTime(AppUtils.getCurrentTimestamp())
                    .build());
            return true;
        }catch (Exception e){
            LOG.error("Error while saving monk version status", e);
        }
        return false;
    }

    private HashMap<String, TagValue> convert(List<DispenserTagAndRevolution> list, HashMap<String, TagValue> map) {
        for (DispenserTagAndRevolution dispenser : list) {
            TagValue tagValue = new TagValue();
            tagValue.setTag(dispenser.getTag());
            tagValue.setValue(dispenser.getRevolution());
            map.put(dispenser.getProduct().getId() + "#" + dispenser.getProduct().getName(), tagValue);
        }
        return map;
    }

    @Override
    public List<HashMap<String, HashMap<String, DispenserTagsMapping>>> getRecipesWithDispensed() {
        List<HashMap<String, HashMap<String, DispenserTagsMapping>>> response = new ArrayList<>();
        List<RecipeDetail> recipeList = recipeDao.findByStatusAndDispensed(AppConstants.ACTIVE, true);
        for (RecipeDetail recipeDetail : recipeList) {
            HashMap<String, HashMap<String, DispenserTagsMapping>> mapping = new HashMap<String, HashMap<String, DispenserTagsMapping>>();
            HashMap<String, DispenserTagsMapping> map = new HashMap<>();
            DispenserTagsMapping dispenserTagsMapping = new DispenserTagsMapping();
            String recipeKey = recipeDetail.getProduct().getProductId() + "#" + recipeDetail.getProfile() + "#" + recipeDetail.getDimension().getName() + "$";
            DispenserRecipeDetail dispenserRecipeDetail = dispenserRecipeDao.findByRecipeKey(recipeKey);
            if (dispenserRecipeDetail != null) {
                dispenserTagsMapping.setAddOns(dispenserRecipeDetail.getMapping().getAddOns());
                if (dispenserRecipeDetail.getMapping().getMandatoryAddons() != null) {
                    dispenserTagsMapping.getAddOns().putAll(dispenserRecipeDetail.getMapping().getMandatoryAddons());
                }
                dispenserTagsMapping.setVariants(dispenserRecipeDetail.getMapping().getVariants());
                map.put("mapping", dispenserTagsMapping);
                mapping.put(recipeKey, map);
                response.add(mapping);
            }
        }
        return response;

    }

    @Override
    public List<DispenserRecipeDetail> getDispenserData() {
        List<DispenserRecipeDetail> dispenserRecipeDetails = dispenserRecipeDao.findAll();
        if (dispenserRecipeDetails != null && dispenserRecipeDetails.size() > 0) {
            if (RequestContext.isContextAvailable()) {
                List<Integer> mappedBrands = MasterUtil.getMappedBrands();
                dispenserRecipeDetails = dispenserRecipeDetails.stream().filter(d ->
                        mappedBrands.contains(masterDataCache.getProduct(d.getProduct().getId()).getBrandId())).toList();
            }
            return dispenserRecipeDetails;
        }
        return new ArrayList<>();
    }

    @Override
    public Boolean saveDispenseTag(List<String> tags) {
        List<DispenserConfig> listOfTags = dispenserConfigDao.findAll();
        DispenserConfig dispenseTags = new DispenserConfig();
        if (listOfTags != null && listOfTags.size() > 0) {
            dispenseTags = listOfTags.get(0);
            dispenseTags.setDispenseTags(tags);
        } else {
            dispenseTags = new DispenserConfig();
            LOG.info("setting new  list of tags");
            dispenseTags.setDispenseTags(tags);
        }
        dispenserConfigDao.save(dispenseTags);
        return true;
    }


    @Override
    public DispenserConfig getDispenseTag() {
        List<DispenserConfig> list = dispenserConfigDao.findAll();
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return new DispenserConfig();
    }


    @Override
    public List<RecipeDetail> setRecipesWithDispensedTag(DispenserBulkRequest dispenserBulkRequest) {
        List<RecipeDetail> list = new ArrayList<>();
        if (dispenserBulkRequest.getProducts() != null) {
            for (Integer id : dispenserBulkRequest.getProducts()) {
                List<RecipeDetail> activeProduct = recipeDao.findByProduct_productIdAndStatus(id, AppConstants.ACTIVE);
                for (RecipeDetail recipe : activeProduct) {
                    recipe.setDispensed(true);
                    //for addons
                    if (dispenserBulkRequest.getAddons() != null && recipe.getAddons() != null) {
                        recipe.getAddons().forEach(addon -> {
                            dispenserBulkRequest.getAddons().forEach((key, value) -> {
//                                LOG.info("product is" + addon.getProduct().getName() + "key is " + key);
                                if (addon.getProduct().getName().equalsIgnoreCase(key)) {
                                    addon.setDispensed(true);
                                    addon.setDispenseTag(value);
                                }
                            });
                        });
                    }
                    //for mandatory addons
                    if (dispenserBulkRequest.getMandatoryAddons() != null && recipe.getMandatoryAddons() != null) {
                        recipe.getMandatoryAddons().forEach(addon -> {
                            dispenserBulkRequest.getMandatoryAddons().forEach((key, value) -> {
//                                LOG.info("product is" + addon.getProduct().getName() + "key is " + key);
                                if (addon.getProduct().getName().equalsIgnoreCase(key)) {
                                    addon.setDispensed(true);
                                    addon.setDispenseTag(value);
                                }
                            });
                        });
                    }
                    //for variants
                    if (dispenserBulkRequest.getVariants() != null && recipe.getIngredient().getVariants() != null) {
                        recipe.getIngredient().getVariants().forEach(variant -> {
                            dispenserBulkRequest.getVariants().forEach((key, value) -> {
//                                LOG.info("product is" + variant.getProduct().getName() + "key is " + key);
                                if (variant.getProduct().getName().equalsIgnoreCase(key)) {
                                    variant.setDispensed(true);
                                    variant.setDispenseTag(value);
                                }
                            });
                        });
                    }
                    recipeDao.save(recipe);
                    list.add(recipe);
                }
            }
        }

        return list;
    }

    @Override
    public List<MonkRecipeVersionData> saveMonkRecipeVersions(List<MonkRecipesVersionRequest> requests) {
       List<MonkRecipeVersionData> latestVersionList= new ArrayList<>();
        String version = AppUtils.getCurrentTimeISTStringWithNoColons();
        requests.forEach(monkRecipesVersionRequest -> {
            //updating previous version to DEACTIVATED
            List<MonkRecipeVersionData> monkVersion = monkRecipeVerisoningDao.findByStatusAndRegion("ACTIVATED", monkRecipesVersionRequest.getRegion());
            monkVersion.forEach(monkRecipeVersionData -> {
                monkRecipeVersionData.setStatus("DEACTIVATED");
                monkRecipeVersionData.setClosedByName(monkRecipesVersionRequest.getUserName());
                monkRecipeVersionData.setClosedById(monkRecipesVersionRequest.getUserId());
            });
            monkRecipeVerisoningDao.saveAll(monkVersion);

            // Creating New Versions
            MonkRecipeVersionData data = new MonkRecipeVersionData();
            data.setStatus("ACTIVATED");
            data.setUpdatedById(monkRecipesVersionRequest.getUserId());
            data.setUpdatedByName(monkRecipesVersionRequest.getUserName());
            data.setActivatedById(monkRecipesVersionRequest.getUserId());
            data.setUpdatedByName(monkRecipesVersionRequest.getUserName());
            data.setRegion(monkRecipesVersionRequest.getRegion());
            data.setVersion(version);
            data.setContent(convertMonkRecipeVersion(monkRecipesVersionRequest));
            data.setActivationTime(AppUtils.getCurrentTimestamp());
            data.setGenetrationTime(AppUtils.getCurrentTimestamp());
            latestVersionList.add(data);
            monkRecipeVerisoningDao.save(data);
        });
        return latestVersionList;
    }


    private static List<MonkRecipeData> convertMonkRecipeVersion(MonkRecipesVersionRequest monkRecipesVersionRequest) {

        List<MonkRecipeData> list = new ArrayList<>();
        monkRecipesVersionRequest.getRawData().forEach(request -> {
            MonkRecipeData data = new MonkRecipeData();
            String[] productDetails = request.getProduct().split("#");
            Integer productId = 0;
            try {
                productId = Integer.parseInt(productDetails[0]);
            } catch (Exception e) {
                LOG.error("It is not a menu product {}", productDetails);
            }
            data.setProductId(productId);
            data.setProductName(request.getName());
            data.setDimension(request.getDimension());
            data.setQuantity(productDetails.length > 1 ? Integer.parseInt(productDetails[2].substring(0, productDetails[2].length() - 1)) : 0);
            data.setPrep((request.getRecipe().substring(0, 1)));
            data.setWater(Integer.parseInt(request.getRecipe().substring(2, 6)));
            data.setMilk(Integer.parseInt(request.getRecipe().substring(6, 10)));
            data.setBoilSettle(Integer.parseInt(request.getRecipe().substring(10, 12)));
            data.setNoOfBoils(Integer.parseInt(request.getRecipe().substring(12, 13)));
            data.setHeatingTimeMins(Integer.parseInt(request.getRecipe().substring(13, 15)));
            data.setHeatingTimeSecs(Integer.parseInt(request.getRecipe().substring(15, 17)));
            data.setKey(request.getProduct());
            data.setContent(request.getRecipe());
            data.setMonkVersion(request.getMonkVersion());
            data.setMilkVariantMonkVersion(request.getMilkVariantMonkVersion());
            data.setAddons(request.getAddons());
            list.add(data);
        });

        return list;
    }

    public static void getCriticalProducts(RecipeDetail recipe, Map<RecipeDetail, List<CriticalProductMenuToSCMMapData>> criticalProductKeys,
                                           Map<String, List<CriticalProductMenuToSCMMapData>> addOnActiveProductsMap) {
        if (recipe == null || recipe.getIngredient() == null) {
            return;
        }

        // handle for Garden Fresh
        if (recipe.getProduct()!=null && recipe.getProduct().getProductId() == 868) {
            for (IngredientProduct id : recipe.getIngredient().getProducts()) {
                for (IngredientProductDetail ipd : id.getDetails()) {
                    if(criticalProductKeys.containsKey(recipe)){
                        criticalProductKeys.get(recipe).add(new CriticalProductMenuToSCMMapData(ipd.getProduct().getProductId(),
                                ipd.getProduct().getName(),ipd.getUom().name(),ipd.getQuantity(),AppConstants.INGREDIENT));
                    }else{
                        List<CriticalProductMenuToSCMMapData> list=new ArrayList<>();
                        list.add(new CriticalProductMenuToSCMMapData(ipd.getProduct().getProductId(),
                                ipd.getProduct().getName(),ipd.getUom().name(),ipd.getQuantity(),AppConstants.INGREDIENT));
                        criticalProductKeys.put(recipe,list);
                    }
                }
            }
        }

        if(recipe.getIngredient().getProducts()!=null){
            for (IngredientProduct id : recipe.getIngredient().getProducts()) {
                if (id.isCritical()) {
                    for (IngredientProductDetail ipd : id.getDetails()) {
                        if (ipd.isCritical()) {
                            if(criticalProductKeys.containsKey(recipe)){
                                criticalProductKeys.get(recipe).add(new CriticalProductMenuToSCMMapData(ipd.getProduct().getProductId(),
                                        ipd.getProduct().getName(),ipd.getUom().name(),ipd.getQuantity(),AppConstants.INGREDIENT));
                            }else{
                                List<CriticalProductMenuToSCMMapData> list=new ArrayList<>();
                                list.add(new CriticalProductMenuToSCMMapData(ipd.getProduct().getProductId(),
                                        ipd.getProduct().getName(),ipd.getUom().name(),ipd.getQuantity(),AppConstants.INGREDIENT));
                                criticalProductKeys.put(recipe,list);
                            }
                        }
                    }
                }
            }
        }
        if(recipe.getIngredient().getVariants()!=null){
            for (IngredientVariant iv : recipe.getIngredient().getVariants()) {
                if (iv.isCritical()) {
                    for (IngredientVariantDetail ivd : iv.getDetails()) {
                        if (ivd.isDefaultSetting()) {
                            if(criticalProductKeys.containsKey(recipe)){

                                // TODO Aashutosh Sir check in IngredientVariant ProductData in not present, unable to get productName for specific IngredientVariantDetail
                                criticalProductKeys.get(recipe).add(new CriticalProductMenuToSCMMapData(ivd.getProductId(),
                                        iv.getProduct().getName(),ivd.getUom().name(),ivd.getQuantity(),AppConstants.INGREDIENT));
                            }else{
                                List<CriticalProductMenuToSCMMapData> list=new ArrayList<>();
                                list.add(new CriticalProductMenuToSCMMapData(iv.getProduct().getProductId(),
                                        iv.getProduct().getName(),ivd.getUom().name(),ivd.getQuantity(),AppConstants.INGREDIENT));
                                criticalProductKeys.put(recipe,list);
                            }
                        }
                    }
                }
            }
        }
        if(recipe.getIngredient().getComponents()!=null){
            for (IngredientProductDetail ipd : recipe.getIngredient().getComponents()) {
                if (ipd.isCritical()) {
                    if(criticalProductKeys.containsKey(recipe)){
                        criticalProductKeys.get(recipe).add(new CriticalProductMenuToSCMMapData(ipd.getProduct().getProductId(),
                                ipd.getProduct().getName(),ipd.getUom().name(),ipd.getQuantity(),AppConstants.INGREDIENT));
                    }else{
                        List<CriticalProductMenuToSCMMapData> list=new ArrayList<>();
                        list.add(new CriticalProductMenuToSCMMapData(ipd.getProduct().getProductId(),
                                ipd.getProduct().getName(),ipd.getUom().name(),ipd.getQuantity(),AppConstants.INGREDIENT));
                        criticalProductKeys.put(recipe,list);
                    }
                }
            }
        }

        if(recipe.getAddons() != null) {
            Map<Integer,CriticalProductMenuToSCMMapData> aggregatedMap = new HashMap<>();
            for (IngredientProductDetail ipd : recipe.getAddons()) {
                if (ipd.getStatus().equalsIgnoreCase(AppConstants.ACTIVE) && Objects.nonNull(ipd.getProduct()) && Objects.nonNull(ipd.getProduct().getProductId())
                        && Objects.nonNull(ipd.getDimension()) && Objects.nonNull(ipd.getDimension().getCode())) {
                    String key = ipd.getProduct().getProductId() + "_" + ipd.getDimension().getCode();
                    if (addOnActiveProductsMap.containsKey(key)) {
                        ObjectMapper mapper = new ObjectMapper();
                        List<CriticalProductMenuToSCMMapData> data = mapper.convertValue(AppUtils.clone(addOnActiveProductsMap.get(key),List.class),
                                new TypeReference<List<CriticalProductMenuToSCMMapData>>() {});
                        data.forEach(e -> {
                            e.setQuantity(e.getQuantity().multiply(ipd.getQuantity()));
                            if (aggregatedMap.containsKey(e.getProductId())) {
                                CriticalProductMenuToSCMMapData mapData = aggregatedMap.get(e.getProductId());
                                mapData.setQuantity(mapData.getQuantity().add(e.getQuantity()));
                                aggregatedMap.put(e.getProductId(), mapData);
                            } else {
                                aggregatedMap.put(e.getProductId(),e);
                            }
                        });
                    }
                }
            }
            if (criticalProductKeys.containsKey(recipe)) {
                criticalProductKeys.get(recipe).addAll(aggregatedMap.values());
            } else {
                List<CriticalProductMenuToSCMMapData> list = new ArrayList<>(aggregatedMap.values());
                criticalProductKeys.put(recipe,list);
            }
        }

    }

    public Map<RecipeDetail,List<CriticalProductMenuToSCMMapData>> criticalProducts(List<RecipeDetail> recipeDetailList, Map<String,
            List<CriticalProductMenuToSCMMapData>> addOnActiveProductsMap){
        Map<RecipeDetail,List<CriticalProductMenuToSCMMapData>> criticalProductKeys=new HashMap<>();
        int c=0;
        for(RecipeDetail recipeDetail : recipeDetailList){
            getCriticalProducts(recipeDetail,criticalProductKeys,addOnActiveProductsMap);
        }
        return criticalProductKeys;
    }

    public MenuToSCMProductMapData converter(RecipeDetail recipeDetail,CriticalProductMenuToSCMMapData criticalProductDetail){
        return new MenuToSCMProductMapData(recipeDetail.getProduct().getProductId(),recipeDetail.getDimension().getName(),
                recipeDetail.getProfile(),recipeDetail.getRecipeId(),criticalProductDetail.getProductId(),
                criticalProductDetail.getProductName(),criticalProductDetail.getUom(),criticalProductDetail.getQuantity(),criticalProductDetail.getIngredientType(),
                recipeDetail.getProduct().getClassification().value());
    }


    @Override
    public int refreshRecipeCache() {
        try {
            List<RecipeDetail> inProgressRecipes = findByStatus("IN_PROGRESS");
            int recipesActivated = 0;
            for (RecipeDetail recipeDetail : inProgressRecipes) {
                if (AppUtils.getDate(recipeDetail.getStartDate()).compareTo(AppUtils.getDate(AppUtils.getCurrentTimestamp())) <= 0 && Objects.isNull(recipeDetail.getApprovedBy())) {
                    updateRecipeProfileStatus(recipeDetail.getRecipeId(), AppConstants.SYSTEM_EMPLOYEE_ID);
                }
            }
            inProgressRecipes = findByStatus("IN_PROGRESS");
            for (RecipeDetail recipeDetail : inProgressRecipes) {
                if (AppUtils.getBusinessDate(recipeDetail.getStartDate()).before(AppUtils.getBusinessDate())
                        || AppUtils.getBusinessDate(recipeDetail.getStartDate()).equals(AppUtils.getBusinessDate())) {
                    List<RecipeDetail> activeRecipes = findByProductIdAndDimension(
                            recipeDetail.getProduct().getProductId(), recipeDetail.getDimension().getInfoId());
                    for (RecipeDetail activeRecipe : activeRecipes) {
                        if (AppUtils.isActive(activeRecipe.getStatus())
                                && activeRecipe.getProfile().equalsIgnoreCase(recipeDetail.getProfile())) {
                            activeRecipe.setStatus(AppConstants.IN_ACTIVE);
                            activeRecipe.setEndDate(AppUtils.getBusinessDate());
                            try {
                                updateRecipe(activeRecipe);
                            } catch (DataNotFoundException e) {
                                LOG.error("Error in updating active recipe with id " + activeRecipe.getRecipeId());
                            }
                        }
                    }
                    recipeDetail.setStatus(AppConstants.ACTIVE);
                    try {
                        updateRecipe(recipeDetail);
                    } catch (DataNotFoundException e) {
                        LOG.error("Error in updating active recipe with id " + recipeDetail.getRecipeId());
                    }
                    recipesActivated++;
                }
            }
            if (recipesActivated > 0) {
                LOG.info("Recipes changed:::: " + recipesActivated);
                LOG.info("Clearing session cache!!!!!!!!!!!!!!!!!!!!!!!!!!");
                sessionCache.clearSessionCache();
            }
            return recipesActivated;
        } catch (Exception e) {
            LOG.error("Error in marking In progress recipes as active", e);
        }
        return 0;
    }


    private List<RecipeDetail> findAllNonSCMActiveByStartDate(Date startDate){
        return recipeDao.findAllNonSCMAndActiveByStartdate(startDate,AppUtils.getDayBeforeOrAfterDay(
                AppUtils.getCurrentDate(), 1));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ScmMissingPriceResponse getApprovedRecipesByToday() {
        List<RecipeDetail> recipeDetailList = findAllNonSCMActiveByStartDate(AppUtils.getDayBeforeOrAfterDay(
                AppUtils.getCurrentDate(), -1));
        Map<Integer, List<RecipeDetail>> addonRecipeDetailByRecipeId = new HashMap<>();
        Map<Integer, List<Integer>> unitsByRecipeId = new HashMap<>();
        for (RecipeDetail recipeDetail : recipeDetailList) {
            List<Integer> addOnProductIds = findDistinctAddonProductIds(new ArrayList<>(Arrays.asList(recipeDetail)));
            List<RecipeDetail> addOnRecipeDetail = findRecipeDetailOfAddOn(addOnProductIds);
            List<Integer> unitIds = productManagementService.getUnitsByRecipeProfileAndProductId(recipeDetail.getProfile(), recipeDetail.getProduct().getProductId());
            addonRecipeDetailByRecipeId.put(recipeDetail.getRecipeId(), addOnRecipeDetail);
            unitsByRecipeId.put(recipeDetail.getRecipeId(), unitIds);
        }
        ScmMissingPriceResponse response = new ScmMissingPriceResponse();
        response.setRecipeDetailList(recipeDetailList);
        response.setAddOnRecipeDetailByRecipeId(addonRecipeDetailByRecipeId);
        response.setUnitDetailListByRecipeId(unitsByRecipeId);
        return response;
    }

    @Override
    public int approveRecipe(RecipeDetail detail, Integer updatedBy, Boolean sentForApproval) {
       try {
           if (sentForApproval) {
               detail.setApprovalRequestedBy(updatedBy);
               detail.setStatus("PENDING_APPROVAL");
               detail.setApprovedBy(null);
           } else {
               detail.setApprovedBy(updatedBy);
               detail.setStatus("IN_PROGRESS");
           }
           detail.setLastUpdatedById(updatedBy);
           detail.setLastUpdatedByName(AppUtils.getCreatedBy(masterDataCache.getEmployee(updatedBy), updatedBy));
           RecipeDetail data = recipeDao.save(detail);
           return data.getRecipeId();
       } catch (Exception e) {
           LOG.error("Exception Occurred while updating the Status of Recipe :: ",e);
       }
       return -1;
    }

    @Override
    public int rejectRecipe(RecipeDetail recipeDetail, int lastUpdatedById, Boolean sentForRejection) {
        try {
            recipeDetail.setStatus("IN_PROGRESS");
            recipeDetail.setLastUpdatedById(lastUpdatedById);
            recipeDetail.setLastUpdatedByName(AppUtils.getCreatedBy(masterDataCache.getEmployee(lastUpdatedById), lastUpdatedById));
            recipeDetail.setApprovedBy(null);
            RecipeDetail data = recipeDao.save(recipeDetail);
            return data.getRecipeId();
        } catch (Exception e) {
            LOG.error("Exception Occurred while updating the Status of Recipe :: ",e);
        }
        return -1;
    }

    @Override
    public View getSheetForRecipes(String profile) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "MonkRecipeProfile.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<MonkRecipeVersionData> recipeVersions = monkRecipeVerisoningDao.findByStatusAndRegion("ACTIVATED", profile);
                List<MonkRecipeDetailVo> monkRecipeDetailVos = new ArrayList<>();
                if(!recipeVersions.isEmpty()){
                    for(MonkRecipeData data : recipeVersions.get(0).getContent()){
                        monkRecipeDetailVos.add(MonkRecipeDetailVo.builder()
                                .key(data.getKey())
                                .milk(data.getMilk())
                                .boilSettle(data.getBoilSettle())
                                .content(data.getContent())
                                .dimension(data.getDimension())
                                .heatingTimeMins(data.getHeatingTimeMins())
                                .heatingTimeSecs(data.getHeatingTimeSecs())
                                .water(data.getWater())
                                .noOfBoils(data.getNoOfBoils())
                                .prep(data.getPrep())
                                .productName(data.getProductName())
                                .productId(data.getProductId())
                                .quantity(data.getQuantity())
                                .monkVersion(data.getMonkVersion())
                                .milkVariantMonkVersion(data.getMilkVariantMonkVersion()).build());
                    }
                }
                writer.writeSheet(monkRecipeDetailVos, MonkRecipeDetailVo .class);
            }
        };
    }

    @Override
    public MonkRecipeVersionData getBasicDetailForProfile(String profile) {
        List<MonkRecipeVersionData> recipeVersions = monkRecipeVerisoningDao.findByStatusAndRegion("ACTIVATED", profile);
        if(!recipeVersions.isEmpty()){
            MonkRecipeVersionData data = recipeVersions.get(0);
            data.setContent(null);
            return data;
        }
        return null;
    }

    @Override
    public void markRecipeInActive() {
        try {
            List<RecipeDetail> recipeDetails = recipeDao.findByStatuses(Arrays.asList("IN_PROGRESS", "PENDING_APPROVAL"));
            List<RecipeDetail> notifyRecipeList = new ArrayList<>();
            LOG.info("Got {} recipes", recipeDetails.size());
            for (RecipeDetail recipeDetail : recipeDetails) {
                if (recipeDetail.getStatus().equalsIgnoreCase("PENDING_APPROVAL")) {
                    if (AppUtils.getDate(recipeDetail.getStartDate()).compareTo(AppUtils.getDate(AppUtils.getCurrentTimestamp())) <= 0) {
                        LOG.info("Marking recipe : {} as In active", recipeDetail.getRecipeId());
                        updateRecipeProfileStatus(recipeDetail.getRecipeId(), AppConstants.SYSTEM_EMPLOYEE_ID);
                        notifyRecipeList.add(recipeDetail);
                    }
                } else {
                    if (AppUtils.getDate(recipeDetail.getStartDate()).compareTo(AppUtils.getDate(AppUtils.getCurrentTimestamp())) <= 0 && Objects.isNull(recipeDetail.getApprovedBy())) {
                        LOG.info("Marking recipe : {} as In active", recipeDetail.getRecipeId());
                        updateRecipeProfileStatus(recipeDetail.getRecipeId(), AppConstants.SYSTEM_EMPLOYEE_ID);
                        notifyRecipeList.add(recipeDetail);
                    }
                }
            }
            if (!notifyRecipeList.isEmpty()) {
                notifyRecipesForApproval(true, notifyRecipeList);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while marking the Recipe as IN Active ::: ", e);
        }
    }

    @Override
    public void notifyRecipesForApproval(boolean isMarkedAsInActive, List<RecipeDetail> recipeDetailsList) {
        try {
            List<RecipeDetail> recipeDetails = isMarkedAsInActive ? recipeDetailsList : recipeDao.findInProgressAndNotStartedRecipes(Arrays.asList("IN_PROGRESS", "PENDING_APPROVAL"),
                    AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(), 1), AppUtils.getDateAfterDays(AppUtils.getCurrentTimestamp(), 2));
            List<RecipeApprovalDTO> recipeApprovalDTOS = new ArrayList<>();
            List<String> emailIds = new ArrayList<>();
            if (!recipeDetails.isEmpty()) {
                for (RecipeDetail recipeDetail : recipeDetails) {
                    if (Objects.isNull(recipeDetail.getApprovedBy())) {
                        recipeApprovalDTOS.add(new RecipeApprovalDTO(recipeDetail.getRecipeId(), recipeDetail.getName(), recipeDetail.getDimension().getName(),recipeDetail.getProfile(),
                                recipeDetail.getLastUpdatedById(), masterDataCache.getEmployee(recipeDetail.getLastUpdatedById())));
                        EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(recipeDetail.getLastUpdatedById());
                        if (Objects.nonNull(employeeBasicDetail) && Objects.nonNull(employeeBasicDetail.getEmailId())
                                && !employeeBasicDetail.getEmailId().equalsIgnoreCase("")) {
                            emailIds.add(employeeBasicDetail.getEmailId());
                        }
                    }
                }
                if (!recipeApprovalDTOS.isEmpty()) {
                    PendingRecipeApprovalNotificationTemplate template = new PendingRecipeApprovalNotificationTemplate(recipeApprovalDTOS, masterProperties.getBasePath(), isMarkedAsInActive ? "MARKED_IN_ACTIVE" : "PENDING_FOR_APPROVAL");
                    PendingRecipeApprovalNotification notification = new PendingRecipeApprovalNotification(emailIds,masterProperties.getEnvironmentType(),template, isMarkedAsInActive ? "MARKED_IN_ACTIVE" : "PENDING_FOR_APPROVAL");
                    notification.sendEmail();
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while Notifying Recipes For Approval ::: ", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addRemoveProductsToPaidAddonsOfRecipes(Map<Integer, List<Integer>> recipesForUpdate, boolean needToRemove) throws DataNotFoundException {
        try {
            if (recipesForUpdate.size() > 0) {
                LOG.info("Starting Adding Paid Addon Products :::  for Recipe Id's {} ", Arrays.toString(recipesForUpdate.keySet().toArray()));
                LOG.info("Trying to get Recipes for : {}" , recipesForUpdate.keySet().size());
                Map<Integer, RecipeDetail> allRecipes = recipeDao.findAllRecipeByRecipeIds(new ArrayList<>(recipesForUpdate.keySet())).stream().collect(Collectors.toMap(RecipeDetail::getRecipeId, Function.identity()));
                LOG.info("Found Recipes for : {}" , recipesForUpdate.keySet().size());
                List<String> recipeWithZeroProductsOrInvalidProducts = new ArrayList<>();
                recipesForUpdate.forEach((recipeId, productIds) -> {
                    if (Objects.isNull(allRecipes.get(recipeId))) {
                        recipeWithZeroProductsOrInvalidProducts.add("For Recipe Id : " + recipeId + " No Recipe Found in Database ..!");
                    }
                    productIds.forEach(productId -> {
                        Product product = masterDataCache.getProduct(productId);
                        if (Objects.isNull(product)) {
                            recipeWithZeroProductsOrInvalidProducts.add("For Recipe Id : " + recipeId + " No product Found With Product Id : " + productId);
                        } else {
                            if (!(product.getSubType() == 1201)) {
                                recipeWithZeroProductsOrInvalidProducts.add("For Recipe Id : " + recipeId + " Product Id : " + productId +" Product Sub Type is : " + product.getSubType() + " ONLY 1201 IS ACCEPTED..!");
                            }
                        }
                    });
                    if (productIds.size() == 0) {
                        recipeWithZeroProductsOrInvalidProducts.add("For Recipe Id : " + recipeId + " No product List Found ");
                    }
                });
                if (!recipeWithZeroProductsOrInvalidProducts.isEmpty()) {
                    throw new DataNotFoundException(" ERROR WHILE ADDING OPTIONS IN RECIPES Please Check the below Errors ::: " + Arrays.toString(recipeWithZeroProductsOrInvalidProducts.toArray()));
                }
                // now iterating through all Recipes again and updating them
                recipesForUpdate.forEach((recipeId, productIds) -> {
                    productIds.forEach(productId -> {
                        RecipeDetail recipeDetail = allRecipes.get(recipeId);
                        Product product = masterDataCache.getProduct(productId);
                        if (needToRemove) {
                            LOG.info("Trying to REMOVE Product to Options List :: {} for Recipe Id : {}", productId, recipeId);
                            if (isProductAlreadyExistsInOptions(recipeDetail, productId)) {
                                LOG.info("Removing Product Options :: {} for Recipe Id : {}", productId, recipeId);
                                List<OptionData> optionDataList = recipeDetail.getOptions();
                                recipeDetail.setOptions(optionDataList.stream().filter(e -> !e.getProductId().equals(productId)).collect(Collectors.toList()));
                                allRecipes.put(recipeId, recipeDetail);
                            } else {
                                LOG.info("product Not Found In Options :: {} for Recipe Id : {} !", productId, recipeId);
                            }
                        } else {
                            LOG.info("Trying to add Product to Options List :: {} for Recipe Id : {}", productId, recipeId);
                            if (!isProductAlreadyExistsInOptions(recipeDetail, productId)) {
                                LOG.info("Adding product to Options :: {} for Recipe Id : {}", productId, recipeId);
                                List<OptionData> optionDataList;
                                if (Objects.isNull(recipeDetail.getOptions())) {
                                    optionDataList = new ArrayList<>();
                                } else {
                                    optionDataList = recipeDetail.getOptions();
                                }
                                OptionData optionData = new OptionData();
                                optionData.setId(productId);
                                optionData.setProductId(productId);
                                optionData.setName(product.getName());
                                optionData.setCode(product.getName());
                                optionData.setShortCode(product.getShortCode());
                                optionData.setType("PRODUCT");
                                optionDataList.add(optionData);
                                recipeDetail.setOptions(optionDataList);
                                allRecipes.put(recipeId, recipeDetail);
                            } else {
                                LOG.info("Not Adding product to Options :: {} for Recipe Id : {} as it is already present..!", productId, recipeId);
                            }
                        }
                    });
                    RecipeDetail recipeDetail = allRecipes.get(recipeId);
                    recipeDao.save(recipeDetail);
                });
            } else {
                LOG.info("No recipes Received For Paid Addon Addition...!");
            }
            LOG.info("{} of Paid Addons Process Completed ..!", needToRemove ? "REMOVE" : "ADDITION");
            return true;
        } catch (Exception e) {
            LOG.error("Exception Occurred while addProductsToPaidAddonsOfActiveRecipes ::: ", e);
            throw e;
        }
    }

    private boolean isProductAlreadyExistsInOptions(RecipeDetail recipeDetail, Integer productId) {
        if (Objects.nonNull(recipeDetail.getOptions())) {
            Optional<OptionData> foundProduct = recipeDetail.getOptions().stream().filter(e -> e.getType().equalsIgnoreCase("PRODUCT")).filter(e -> e.getProductId().equals(productId)).findFirst();
            return foundProduct.isPresent();
        } else {
            return false;
        }
    }

    private boolean isValidExcelFile(MultipartFile file) {
        if (file.isEmpty()) {
            return false;
        }

        String contentType = file.getContentType();
        String originalFilename = file.getOriginalFilename();

        return (contentType != null && (contentType.equals("application/vnd.ms-excel") ||
                contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")))
                && (originalFilename != null && (originalFilename.endsWith(".xls") || originalFilename.endsWith(".xlsx")));
    }

    @Override
    public ResponseEntity<Map<String, List<String>>> updateRecipeByProducts(MultipartFile excelFile) throws IOException {
        Map<String, List<String>> returnMap = new HashMap<>();
        try {
            if (!isValidExcelFile(excelFile)) {
                returnMap.put("error", List.of("Invalid file type. Please upload an Excel file (.xls or .xlsx)"));
                return ResponseEntity.badRequest().body(returnMap);
            }
            List<ExcelParsingException> errors = new ArrayList<>();
            List<RecipeProductExcelData> recipeProductList = parseExcel(excelFile, errors);
            if(errors.isEmpty()) {
                return updateCriticalInRecipeByProducts(recipeProductList);
            }

            // else if any errors...
            LOG.info("Error parsing Recipe Media Workbook , total errors: {}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage()).append('\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        } catch (Exception exp) {
            String errorMsg = "Error while Bulk uploading Recipe Condiment Sheet" + exp.getMessage();
            returnMap.put("exception", List.of(errorMsg));
            LOG.error(errorMsg, exp);
        }
        return ResponseEntity.badRequest().body(returnMap);
    }

    private List<RecipeProductExcelData> parseExcel(MultipartFile excelFile, List<ExcelParsingException> errors) throws IOException {
        Workbook workbook;
        if (excelFile.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(excelFile.getInputStream());
        } else {
            workbook = new XSSFWorkbook(excelFile.getInputStream());
        }
        SheetParser parser = new SheetParser();
        return parser.createEntity(workbook.getSheetAt(0), RecipeProductExcelData.class, errors::add);
    }

    private boolean isPositiveNumber(String str, List<String> errors) {
        if(StringUtils.isBlank(str)) {
            return false;
        }
        try {
            // skip if its negative value or 0...
            if(Integer.parseInt(str) > 0) {
                return true;
            }
            errors.add("Product Id should be positive value : " + str);
        } catch (Exception exp) {
            errors.add("Error while parsing productId from string to integer" + exp.getMessage());
            LOG.info("Error while parsing productId from string to integer --> {},", exp.getMessage());
        }
        return false;
    }

    private ResponseEntity<Map<String, List<String>>> updateCriticalInRecipeByProducts(List<RecipeProductExcelData> recipeProductExcelDataList) {
        List<String> messages = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        Map<Integer, List<Integer>> recipeIdToProductIdsMap = new HashMap<>();

        // make a map of Recipe id and List of productIds from the Excel...
        for(RecipeProductExcelData recipeProductExcelData : recipeProductExcelDataList) {
            if(Objects.nonNull(recipeProductExcelData.getRecipeId())) {
                if(recipeIdToProductIdsMap.containsKey(recipeProductExcelData.getRecipeId())) {
                    errors.add("Duplicate recipe ID : " + recipeProductExcelData.getRecipeId() + ". Considering 1st recipe id and skipping duplicate recipe ID");
                    continue;
                }
                if(StringUtils.isNotBlank(recipeProductExcelData.getProductIds())) {
                    List<String> productIdStr = Arrays.stream(recipeProductExcelData.getProductIds().split(",")).collect(Collectors.toList());
                    List<Integer> productIds = productIdStr.stream()
                            .filter(s -> isPositiveNumber(s, errors))
                            .map(Integer::parseInt)
                            .collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(productIds)) {
                        errors.add("Product ids after splitting was empty for recipe Id : " + recipeProductExcelData.getRecipeId());
                    } else {
                        recipeIdToProductIdsMap.put(recipeProductExcelData.getRecipeId(), productIds);
                    }
                } else {
                    errors.add("Product ids null or empty for recipe Id : " + recipeProductExcelData.getRecipeId());
                }
            }
        }

        List<RecipeDetail> recipeDetailList = recipeDao.findAllRecipeByRecipeIds(recipeIdToProductIdsMap.keySet().stream().toList());
        for(RecipeDetail recipe : recipeDetailList) {
            if(recipe.getIngredient() != null) {
                IngredientDetail ingredientDetail = recipe.getIngredient();

                // mark critical in variants
                if(CollectionUtils.isNotEmpty(ingredientDetail.getVariants())) {
                    markCriticalInVariants(ingredientDetail.getVariants(), recipeIdToProductIdsMap.get(recipe.getRecipeId()), messages, recipe.getRecipeId());
                }

                // mark critical in products
                if(CollectionUtils.isNotEmpty(ingredientDetail.getProducts())) {
                    markCriticalInProducts(ingredientDetail.getProducts(), recipeIdToProductIdsMap.get(recipe.getRecipeId()), messages, recipe.getRecipeId());
                }

                // mark critical in components
                if(CollectionUtils.isNotEmpty(ingredientDetail.getComponents())) {
                    markCriticalInComponents(ingredientDetail.getComponents(), recipeIdToProductIdsMap.get(recipe.getRecipeId()), messages, recipe.getRecipeId());
                }
                recipeDao.save(recipe);
                continue;
            }
            messages.add("Recipe Ingredient was null for recipe id : " + recipe.getRecipeId());
        }
        Map<String, List<String>> returnMsg = new HashMap<>();
        returnMsg.put("errors", errors);
        returnMsg.put("message", messages);
        return ResponseEntity.ok(returnMsg);
    }

    private void markCriticalInVariants(List<IngredientVariant> variants, List<Integer> productIds, List<String> messages, Integer recipeId) {
        for(IngredientVariant variant : variants) {
            if(CollectionUtils.isEmpty(variant.getDetails()) || variant.isCritical()) {
                continue;
            }
            for (IngredientVariantDetail detail : variant.getDetails()) {
                if(productIds.contains(detail.getProductId())) {
                    variant.setCritical(true);
                    messages.add("Recipe Id : " + recipeId + " and Product Id : " + detail.getProductId() + " critical marked as true.");
                    return;
                }
            }
        }
    }

    private void markCriticalInProducts(List<IngredientProduct> products, List<Integer> productIds, List<String> messages, int recipeId) {
        for (IngredientProduct product : products) {
            if(CollectionUtils.isEmpty(product.getDetails())) {
                continue;
            }
            for (IngredientProductDetail detail : product.getDetails()) {
                if(detail.getProduct() == null) {
                    continue;
                }
                if(productIds.contains(detail.getProduct().getProductId()) && !detail.isCritical()) {
                    detail.setCritical(true);
                    product.setCritical(true);
                    messages.add("Recipe Id : " + recipeId + " and Product Id : " + detail.getProduct().getProductId() + " critical marked as true.");
                }
            }

        }
    }

    private void markCriticalInComponents(List<IngredientProductDetail> components, List<Integer> productIds, List<String> messages, int recipeId) {
        for (IngredientProductDetail component : components) {
            if(component.getProduct() == null || component.isCritical()) {
                continue;
            }
            if(productIds.contains(component.getProduct().getProductId())) {
                component.setCritical(true);
                messages.add("Recipe Id : " + recipeId + " and Product Id : " + component.getProduct().getProductId() + " critical marked as true.");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String, Set<String>> findRecipeByProductsAndDimensions(ProductRecipeMappingRequest request) {
        List<RecipeDetail> details = recipeDao.findByProductIdsAndDimensions(request.getProductIds(), request.getDimensionIds(), RecipeIterationStatus.ACTIVE);
        if(CollectionUtils.isEmpty(details)) {
            return new HashMap<>();
        }

        Map<String, Set<String>> recipesByProductAndDimensionMap = new HashMap<>();
        for(RecipeDetail recipe : details) {
            String product_dimensionKey = recipe.getProduct().getProductId() + "_" + recipe.getDimension().getInfoId();
            recipesByProductAndDimensionMap.computeIfAbsent((product_dimensionKey), r -> new HashSet<>()).add(recipe.getProfile());
        }

        return recipesByProductAndDimensionMap;
    }

    @Override
    public List<RecipeDetail> filterDetailsByCompanyAndBrand(List<RecipeDetail> details) {
        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedBrands = MasterUtil.getMappedBrands();
            details = details.stream().filter(d -> {
                Product p = masterDataCache.getProduct(d.getProduct().getProductId());
                return (Objects.isNull(p) || mappedBrands.contains(p.getBrandId()));
            }).collect(Collectors.toList());
        }
        return details;
    }

    @Override
    public List<RecipeDetail> applyConversionRatiosToRecipeIngredients(Map<Integer, Map<Integer, BigDecimal>> productConvesrionMap) {
        List<RecipeDetail> recipeDetailList = recipeDao.findAllSCMProductRecipeByRecipeIds(productConvesrionMap.keySet().stream().toList());
        for(RecipeDetail recipeDetail : recipeDetailList) {
            if (Objects.nonNull(recipeDetail.getIngredient()) && Objects.nonNull(recipeDetail.getIngredient().getComponents())) {
                for (IngredientProductDetail component : recipeDetail.getIngredient().getComponents()) {
                    if (Objects.nonNull(component.getProduct()) && Objects.nonNull(productConvesrionMap.get(recipeDetail.getRecipeId()))
                            && Objects.nonNull(productConvesrionMap.get(recipeDetail.getRecipeId()).get(component.getProduct().getProductId()))) {
                        if(component.getOldQuantity() == null) {
                            component.setOldQuantity(AppUtils.multiplyWithScale10(component.getQuantity(), BigDecimal.ONE));
                        }
                        component.setQuantity(AppUtils.multiplyWithScale10(component.getQuantity(), productConvesrionMap.get(recipeDetail.getRecipeId()).get(component.getProduct().getProductId())));
                    }
                }
            }

            List<RecipeIterationDetail> recipeIterationDetailList = recipeIterationDao.getSCMIterationForProduct(recipeDetail.getProduct().getProductId());
            for(RecipeIterationDetail recipeIterationDetail : recipeIterationDetailList) {
                if (Objects.nonNull(recipeIterationDetail.getComponents())) {
                    for (IterationIngredientDetail component : recipeIterationDetail.getComponents()) {
                        if (Objects.nonNull(component.getProductId()) && Objects.nonNull(productConvesrionMap.get(recipeDetail.getRecipeId()))
                                && Objects.nonNull(productConvesrionMap.get(recipeDetail.getRecipeId()).get(component.getProductId()))) {

                            if(component.getOldQuantity() == null) {
                                component.setOldQuantity(AppUtils.multiplyWithScale10(component.getQuantity(), BigDecimal.ONE));
                            }
                            component.setQuantity(AppUtils.multiplyWithScale10(component.getQuantity(),
                                    productConvesrionMap.get(recipeDetail.getRecipeId()).get(component.getProductId())));

                            if(component.getOldQuantityPerSubUom() == null) {
                                component.setOldQuantityPerSubUom(component.getQuantityPerSubUom().setScale(6, RoundingMode.HALF_UP));
                            }
                            component.setQuantityPerSubUom(AppUtils.multiplyWithScale(
                                    component.getQuantity().divide(recipeIterationDetail.getOutputQuantity(), RoundingMode.HALF_UP),
                                    BigDecimal.ONE, 6));

                            if (component.getOldYieldQuantity() == null){
                                component.setOldYieldQuantity(component.getYieldQuantity().setScale(6, RoundingMode.HALF_UP));
                            }
                            component.setYieldQuantity(AppUtils.multiplyWithScale(
                                    component.getQuantity().divide(recipeIterationDetail.getOutputQuantity(), RoundingMode.HALF_UP),
                                    BigDecimal.ONE, 6));

                            if(component.getOldYieldPercentage() == null) {
                                component.setOldYieldPercentage(component.getYieldPercentage().setScale(2, RoundingMode.HALF_UP));
                            }
                            component.setYieldPercentage(new BigDecimal("100.00"));
                        }
                    }
                }
                recipeIterationDao.save(recipeIterationDetail);
            }
        }
        recipeDao.saveAll(recipeDetailList);
        return recipeDetailList;
    }

}
