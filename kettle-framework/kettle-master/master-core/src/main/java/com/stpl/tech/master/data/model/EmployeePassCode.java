/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 20 Jul, 2015 3:19:50 PM by Hibernate Tools 4.0.0

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * EmployeePassCode generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "EMPLOYEE_PASS_CODE")
public class EmployeePassCode implements java.io.Serializable {

	private Integer empSurrogateId;
	private EmployeeDetail employeeDetail;
	private String empPassCode;
	private Integer updatedBy;
	private Date lastUpdateTmstmp;


	public EmployeePassCode() {
	}

	public EmployeePassCode(EmployeeDetail employeeDetail, String empPassCode, Date lastUpdateTmstmp,
			Integer updatedBy) {
		this.employeeDetail = employeeDetail;
		this.empPassCode = empPassCode;
		this.lastUpdateTmstmp = lastUpdateTmstmp;
		this.updatedBy = updatedBy;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EMP_SURROGATE_ID", unique = true, nullable = false)
	public Integer getEmpSurrogateId() {
		return this.empSurrogateId;
	}

	public void setEmpSurrogateId(Integer empSurrogateId) {
		this.empSurrogateId = empSurrogateId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EMP_ID", nullable = false)
	public EmployeeDetail getEmployeeDetail() {
		return this.employeeDetail;
	}

	public void setEmployeeDetail(EmployeeDetail employeeDetail) {
		this.employeeDetail = employeeDetail;
	}

	@Column(name = "EMP_PASS_CODE", nullable = false)
	public String getEmpPassCode() {
		return this.empPassCode;
	}

	public void setEmpPassCode(String empPassCode) {
		this.empPassCode = empPassCode;
	}

	@Column(name = "UPDATED_BY", nullable = false)
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
	public Date getLastUpdateTmstmp() {
		return this.lastUpdateTmstmp;
	}

	public void setLastUpdateTmstmp(Date lastUpdateTmstmp) {
		this.lastUpdateTmstmp = lastUpdateTmstmp;
	}


}
