package com.stpl.tech.master.data.model;

import com.stpl.tech.master.data.model.Status;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import javax.persistence.*;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(name = "UNIT_STATION_CATEGORY_MAPPING", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"UNIT_ID", "REF_LOOKUP_ID", "STATUS"}))
public class UnitStationCategoryMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "REF_LOOKUP_ID", nullable = false)
    private Integer refLookupId;

    @OneToMany(mappedBy = "stationCategoryMapping", cascade = CascadeType.ALL)
    @EqualsAndHashCode.Exclude
    private Set<UnitProductStationMapping> productMappings = new HashSet<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS", nullable = false)
    private Status status;
    
    @Column(name = "UPDATED_BY")
    private String updatedBy;
    
    @Column(name = "CREATED_AT", nullable = false, updatable = false)
    private Timestamp createdAt;
    
    @Column(name = "UPDATED_AT")
    private Timestamp updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = new Timestamp(System.currentTimeMillis());
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Timestamp(System.currentTimeMillis());
    }
} 