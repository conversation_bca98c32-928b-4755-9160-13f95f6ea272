package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.LCMMenuVariantMetadataGroup;
import org.apache.commons.math3.linear.RealVector;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface LCDMenuVariantGroupDao extends JpaRepository<LCMMenuVariantMetadataGroup,Long> {
    List<LCMMenuVariantMetadataGroup> findAllById(Long id);

    Optional<LCMMenuVariantMetadataGroup> findByGroupName(String groupName);
}
