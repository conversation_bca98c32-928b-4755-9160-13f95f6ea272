/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.master.core.external.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

public class AuthorizedMacCache {

	private static final Logger LOG = LoggerFactory.getLogger(AuthorizedMacCache.class);

	private static final AuthorizedMacCache INSTANCE = new AuthorizedMacCache();
	private static final Set<String> macAddressSet = new HashSet<String>();

	private AuthorizedMacCache() {

	}

	public static AuthorizedMacCache getInstance() {
		return INSTANCE;
	}

	public Boolean addMacAddress(String macAddress) {
		if (!macAddressSet.contains(macAddress)) {
			if (macAddressSet.add(macAddress)) {
				LOG.info("macCache after add::::" + macAddressSet);
				return true;
			}
		} else {
			return true;
		}

		return false;
	}

	public Set<String> getMacAddressCache() {
		return macAddressSet;
	}

	public Boolean setMacAddressCache(Set<String> addressesSet) {
		if (!macAddressSet.isEmpty()) {
			macAddressSet.clear();
		}
		if (macAddressSet.addAll(addressesSet)) {
			return true;
		}
		return false;
	}

	public Boolean isValidMacAddress(String macAddress) {
		if (macAddressSet.contains(macAddress)) {
			return true;
		}
		return false;
	}

}
