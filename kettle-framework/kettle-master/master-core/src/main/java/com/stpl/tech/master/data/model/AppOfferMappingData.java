package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "APP_OFFER_MAPPING_DATA")
public class AppOfferMappingData implements java.io.Serializable {

    private Integer offerMappingId;
    private String mappingType;
    private Integer mappingValue;
    private String mappingUnitName;
    private String mappingStatus;
    private String updatedBy;
    private Date updateTime;

    private AppOfferDetailData appOfferDetailData;

    public AppOfferMappingData() {

    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "OFFER_MAPPING_ID", unique = true, nullable = false)
    public Integer getOfferMappingId() {
        return offerMappingId;
    }

    public void setOfferMappingId(Integer offerMappingId) {
        this.offerMappingId = offerMappingId;
    }

    @Column(name = "MAPPING_TYPE", nullable = true, length = 45)
    public String getMappingType() {
        return mappingType;
    }

    public void setMappingType(String mappingType) {
        this.mappingType = mappingType;
    }

    @Column(name = "MAPPING_VALUE", nullable = true)
    public Integer getMappingValue() {
        return mappingValue;
    }

    public void setMappingValue(Integer mappingValue) {
        this.mappingValue = mappingValue;
    }

    @Column(name = "MAPPING_UNIT_NAME", nullable = true, length = 100)
    public String getMappingUnitName() {
        return mappingUnitName;
    }

    public void setMappingUnitName(String mappingUnitName) {
        this.mappingUnitName = mappingUnitName;
    }

    @Column(name = "MAPPING_STATUS", nullable = true, length = 45)
    public String getMappingStatus() {
        return mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    @Column(name = "UPDATED_BY", nullable = true, length = 45)
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_TIME", nullable = true, length = 19)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "APP_OFFER_ID", nullable = false)
    public AppOfferDetailData getAppOfferDetailData() {
        return appOfferDetailData;
    }

    public void setAppOfferDetailData(AppOfferDetailData appOfferDetailData) {
        this.appOfferDetailData = appOfferDetailData;
    }

}
