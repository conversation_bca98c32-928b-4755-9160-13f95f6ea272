/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.cache;

import com.stpl.tech.master.domain.model.CityLocalityKey;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.locality.model.LocalityMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class MasterDataCacheProxy {

    private static final Logger LOG = LoggerFactory.getLogger(MasterDataCacheProxy.class);

    private Map<Integer, Unit> units;
    private Map<UnitCategory, List<UnitBasicDetail>> unitsMetadata;
    private Map<Integer, UnitBasicDetail> unitsBasicDetails;
    private Map<Integer, Product> unitProductDetails;// TreeMap
    private Map<Integer, Map<Integer,Map<Pair<BigDecimal,String>,String>>> unitProductAlias;// TreeMap
    private Map<Integer, List<TaxProfile>> unitTaxProfiles;
    private List<TaxProfile> taxProfiles;
    private Map<Integer, List<UnitHours>> operationalHours; // Business Hours for unit
    private Map<String, LocalityMapping> localities;
    private Map<CityLocalityKey, LocalityMapping> localityMappingMap;
    private Map<String, Location> locations;
    private Map<Integer, Location> locationsData;
    private Set<IdCodeName> cityList;
    private Map<Integer, Set<IdCodeName>> cityUnitMapping;
    private Map<String,String> regionMap;
    private Set<String> unitsEmailIds;

    public MasterDataCacheProxy() {
        createCache();
    }

    public void createCache() {
        LOG.info("POST-CONSTRUCT MasterDataCacheProxy - STARTED");
        LOG.info("$$$$$$$$$$$$$$$Creating Proxy Cache$$$$$$$$$$$$$$$");
        units = new HashMap<>();
        unitsMetadata = new HashMap<>();
        unitsBasicDetails = new HashMap<>();
        unitProductDetails = new HashMap<>();
        unitProductAlias = new HashMap<>();
        unitTaxProfiles = new HashMap<>();
        taxProfiles = new ArrayList<>();
        operationalHours = new HashMap<>();
        localities = new HashMap<>();
        localityMappingMap = new HashMap<>();
        locations = new HashMap<>();
        locationsData = new HashMap<>();
        cityList = new HashSet<>();
        cityUnitMapping = new ConcurrentHashMap<>();
        regionMap = new HashMap<>();
        unitsEmailIds = new HashSet<>();
    }

    public Map<Integer, Unit> getUnits() {
        return units;
    }

    public Map<UnitCategory, List<UnitBasicDetail>> getUnitsMetadata() {
        return unitsMetadata;
    }

    public Map<Integer, UnitBasicDetail> getUnitsBasicDetails() {
        return unitsBasicDetails;
    }

    public Map<Integer, Product> getUnitProductDetails() {
        return unitProductDetails;
    }

    public Map<Integer, Map<Integer, Map<Pair<BigDecimal, String>, String>>> getUnitProductAlias() {
        return unitProductAlias;
    }

    public Map<Integer, List<TaxProfile>> getUnitTaxProfiles() {
        return unitTaxProfiles;
    }

    public List<TaxProfile> getTaxProfiles() {
        return taxProfiles;
    }

    public Map<Integer, List<UnitHours>> getOperationalHours() {
        return operationalHours;
    }

    public Map<String, LocalityMapping> getLocalities() {
        return localities;
    }

    public Map<CityLocalityKey, LocalityMapping> getLocalityMappingMap() {
        return localityMappingMap;
    }

    public Map<String, Location> getLocations() {
        return locations;
    }

    public Map<Integer, Location> getLocationsData() {
        return locationsData;
    }

    public Set<IdCodeName> getCityList() {
        return cityList;
    }

    public Map<Integer, Set<IdCodeName>> getCityUnitMapping() {
        return cityUnitMapping;
    }

    public Set<IdCodeName> getCityUnitMapping(Integer locationId) {
        return cityUnitMapping.get(locationId);
    }

    public Map<String, String> getRegionMap() {
        return regionMap;
    }

    public Set<String> getUnitsEmailIds() {
        return unitsEmailIds;
    }

    public Unit getUnit(Integer unitId) {
        return units.get(unitId);
    }

    public UnitBasicDetail getUnitBasicDetail(Integer unitId) {
        return unitsBasicDetails.get(unitId);
    }

    public void updateCityUnitMapping(Integer locationId, Set<IdCodeName> unitSet) {
        cityUnitMapping.put(locationId, unitSet);
    }

    public void setOperationalHoursForUnit(Integer unitId, List<UnitHours> unitHours) {
        operationalHours.put(unitId, unitHours);
    }
}
