package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.mapper.DtoDataMapper;
import com.stpl.tech.master.core.mapper.DtoDataMapperUtil;
import com.stpl.tech.master.data.dao.LCDMenuImageDao;
import com.stpl.tech.master.data.dao.LCDMenuImageFolderRepository;
import com.stpl.tech.master.data.dao.LCDMenuImageParameterDao;
import com.stpl.tech.master.data.dao.LCDMenuImageVariantDao;
import com.stpl.tech.master.data.dao.LCDMenuVariantMetadataGroupRepository;
import com.stpl.tech.master.data.dao.LcdMenuImagesUploadVersionDao;
import com.stpl.tech.master.data.dao.PriceProfileDao;
import com.stpl.tech.master.data.dao.specification.LCDMenuImageSpecification;
import com.stpl.tech.master.data.model.LCDMenuImage;
import com.stpl.tech.master.data.model.LCDMenuImageFolder;
import com.stpl.tech.master.data.model.LCDMenuImageParameter;
import com.stpl.tech.master.data.model.LCDMenuImageVariant;
import com.stpl.tech.master.data.model.LcdMenuImagesUploadVersion;
import com.stpl.tech.master.data.model.LCDMenuStep;
import com.stpl.tech.master.data.dao.LCDMenuStepRepository;
import com.stpl.tech.master.domain.model.LCDMenuImageDomain;
import com.stpl.tech.master.domain.model.LCDMenuImageMetaData;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.Collections;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
        readOnly = true, propagation = Propagation.REQUIRED)
public class LCDMenuImageService {

    @Autowired
    private LCDMenuImageDao imageRepository;

    @Autowired
    private FileArchiveService s3Service;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private PriceProfileDao priceProfileDao;

    @Autowired
    private LCDMenuVariantMetadataGroupRepository lcdMenuVariantMetadataGroupDao;

    @Autowired
    private LcdMenuImagesUploadVersionDao lcdMenuImagesUploadVersionDao;

    @Autowired
    private LCDMenuImageVariantDao lcdMenuImageVariantDao;

    @Autowired
    private LCDMenuImageParameterDao lcdMenuImageParameterDao;

    @Autowired
    private LCDMenuImageFolderRepository folderRepository;

    @Autowired
    private LCDMenuStepRepository stepRepository;

    @Value("${lcdMenuImages.s3.bucket:dev.lcdmenu.images}")
    private String bucketName;

    @Value("${lcdMenuImages.cloudfront:https://d2oix11ew5j7nu.cloudfront.net/}")
    private String cloudFrontUrl;


    public List<String> getAllVersions(){
        List<String> versions = new ArrayList<>();
        List<LcdMenuImagesUploadVersion> lcdMenuImagesUploadVersions = lcdMenuImagesUploadVersionDao.findAll();
        for(LcdMenuImagesUploadVersion lcdMenuImagesUploadVersion : lcdMenuImagesUploadVersions){
            versions.add(lcdMenuImagesUploadVersion.getVersionNo().toString());
        }
        return versions;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public void createVersion(String versionNo , Integer createdBy){
        LcdMenuImagesUploadVersion lcdMenuImagesUploadVersion = new LcdMenuImagesUploadVersion();
        lcdMenuImagesUploadVersion.setVersionNo(versionNo);
        lcdMenuImagesUploadVersion.setCreatedBy(createdBy);
        lcdMenuImagesUploadVersion.setCreationDate(AppUtils.getCurrentTimestamp());
        lcdMenuImagesUploadVersionDao.save(lcdMenuImagesUploadVersion);
        createFolder("version",versionNo.toString());
    }

    // Get predefined metadata
    public LCDMenuImageMetaData getMetadata() {
        LCDMenuImageMetaData metadata = new LCDMenuImageMetaData();

        // These would typically come from a database or configuration
        metadata.setRegions(masterDataCache.getAllRegions().stream().toList());
        metadata.setPriceProfiles(priceProfileDao.findPriceProfileNameByStatus(AppConstants.ACTIVE));
        metadata.setSlots(Arrays.stream(MenuType.values()).map(MenuType::name).toList());
        metadata.setLcdTypes(Arrays.asList("LED", "OLED", "LCD", "QLED"));
        metadata.setVariants(lcdMenuVariantMetadataGroupDao.findAllByStatus(AppConstants.ACTIVE)
                .stream().map(DtoDataMapperUtil::toDomain).toList());

        return metadata;
    }

    // Get images by version
    public List<LCDMenuImageDomain> getImagesByVersion(String version) {
        return DtoDataMapperUtil.mapToLCDMenuImageDomainList(imageRepository.findByVersion(version));
    }

    public List<LCDMenuImageDomain> searchImages(String version, String region, String priceProfile, String orientation,
                                           String slot, String lcdType) {
        Specification<LCDMenuImage> spec = LCDMenuImageSpecification.filterBy(version, region, priceProfile, orientation, slot, lcdType);
        return DtoDataMapperUtil.mapToLCDMenuImageDomainList(imageRepository.findAll(spec));
    }

    /**
     * Search images using a map of parameters
     * @param params Map of parameter name to parameter value
     * @return List of matching images
     */
    public List<LCDMenuImageDomain> searchImagesByParams(Map<String, String> params) {
        Map<String,String> searchParams  = filterSearchParams(params);
        Specification<LCDMenuImage> spec = LCDMenuImageSpecification.filterByParams(searchParams);
        return DtoDataMapperUtil.mapToLCDMenuImageDomainList(imageRepository.findAll(spec));
    }

    // update image
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public String updateImage(
            Long updateImageId,
            MultipartFile file , Integer updatedBy) throws IOException, DataUpdationException {
        LCDMenuImage lcdMenuImage = imageRepository.findById(updateImageId).orElse(null);
        if(Objects.isNull(lcdMenuImage)){
            throw new DataUpdationException("Image not found");
        }
        lcdMenuImage.setUpdatedBy(updatedBy);
        lcdMenuImage.setUpdationTime(AppUtils.getCurrentTimestamp());
        File fileToUpload = s3Service.convertFromMultiPart(lcdMenuImage.getName(), file);
        FileDetail fileDetail =  s3Service.saveFileToS3WithSameBaseDir(bucketName + "/lcd-media", removeFileName(lcdMenuImage.getPath()) + "/", fileToUpload);
        fileToUpload.deleteOnExit();
        lcdMenuImage.setUrl(cloudFrontUrl + fileDetail.getKey());
        return lcdMenuImage.getName();
    }

    // Upload images
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public List<String> uploadImages(
            String version, String region, String priceProfile,
            String orientation, String slot, String lcdType,
            List<String> variant,
            MultipartFile[] files,Integer uploadedBy) throws IOException {
        // Convert legacy parameters to a map for the new method
        Map<String, String> params = new HashMap<>();
        if (version != null) params.put("version", version);
        if (region != null) params.put("region", region);
        if (priceProfile != null) params.put("priceProfile", priceProfile);
        if (orientation != null) params.put("orientation", orientation);
        if (slot != null) params.put("slot", slot);
        if (lcdType != null) params.put("lcdType", lcdType);

        return uploadImagesWithParams(params, variant, files, uploadedBy);
    }

    Map<String , String> filterSearchParams(Map<String, String> params){
        List<LCDMenuStep> orderedSteps = stepRepository.findAll();
        // Create a map to store step name to order mapping
        Map<String, Integer> stepOrderMap = new HashMap<>();
        for (int j = 0; j < orderedSteps.size(); j++) {
            stepOrderMap.put(orderedSteps.get(j).getStepName(), j);
        }
        Map<String,String> searchParams = params.entrySet().stream().filter(s -> stepOrderMap.containsKey(s.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        return searchParams;
    }

    /**
     * Upload images with a map of parameters
     * @param params Map of parameter name to parameter value
     * @param variant List of variant values
     * @param files Array of files to upload
     * @param uploadedBy ID of the user uploading the files
     * @return List of uploaded file paths
     * @throws IOException If there's an error uploading the files
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public List<String> uploadImagesWithParams(
            Map<String, String> params,
            List<String> variant,
            MultipartFile[] files,Integer uploadedBy) throws IOException {
        Map<String,String> searchParams  = filterSearchParams(params);
        List<String> uploadedFiles = new ArrayList<>();
        List<LCDMenuImageDomain> currentUploadedImages = searchImagesByParams(params);
        AtomicInteger currentImageCount = new AtomicInteger(currentUploadedImages.size());
        for (int i = 0; i < files.length; i++) {
            try{
                MultipartFile file = files[i];

                // Build path with all parameters (both legacy and dynamic)
                String pathStr = buildPathFromParams(searchParams);
                StringBuilder path = new StringBuilder(pathStr);

                // Extract path components for filename
                List<String> pathComponents = new ArrayList<>();
                if (!pathStr.isEmpty()) {
                    String[] components = pathStr.split("/");
                    pathComponents.addAll(Arrays.asList(components));
                }

                // Build filename based on path components and index
                StringBuilder filename = new StringBuilder();

                // Join path components with underscores
                if (!pathComponents.isEmpty()) {
                    filename.append(String.join("_", pathComponents));
                }

                // Add variants in sorted order if present
                if (variant != null && !variant.isEmpty()) {
                    List<String> sortedVariants = new ArrayList<>(variant);
                    Collections.sort(sortedVariants);

                    if (filename.length() > 0) {
                        filename.append("_");
                    }
                    filename.append(String.join("_", sortedVariants));
                }

                // Add index based on current count
                int index = currentImageCount.incrementAndGet();
                if (filename.length() > 0) {
                    filename.append("_");
                }
                filename.append("image").append(index);

                // Add file extension from original filename
                String originalFilename = file.getOriginalFilename();
                if (originalFilename != null && originalFilename.contains(".")) {
                    filename.append(originalFilename.substring(originalFilename.lastIndexOf('.')));
                }

                // Upload to S3
                File fileToUpload = s3Service.convertFromMultiPart(filename.toString(), file);
                FileDetail fileDetail =  s3Service.saveFileToS3WithSameBaseDir(bucketName + "/lcd-media", path.toString(), fileToUpload);
                fileToUpload.deleteOnExit();
                uploadedFiles.add(path.toString());

                // Save image metadata to database
                LCDMenuImage image = new LCDMenuImage();
                image.setName(filename.toString());
                image.setPath(fileDetail.getKey());
                image.setUrl(cloudFrontUrl + fileDetail.getKey());
                // Set legacy fields for backward compatibility
                image.setVersion(searchParams.getOrDefault("version", ""));
                image.setRegion(searchParams.getOrDefault("region", ""));
                image.setPriceProfile(searchParams.getOrDefault("priceProfile", ""));
                image.setOrientation(searchParams.getOrDefault("orientation", ""));
                image.setSlot(searchParams.getOrDefault("slot", ""));
                image.setLcdType(searchParams.getOrDefault("lcdType", ""));

                // Set parameters from the map
                image.setParametersFromMap(searchParams);
                image.setUploadedBy(uploadedBy);
                image.setUploadTime(AppUtils.getCurrentTimestamp());
                image.setType(file.getContentType());
                image.setSize(String.valueOf(file.getSize()));
                image.setStatus(AppConstants.ACTIVE);
                image = imageRepository.save(image);
                List<LCDMenuImageVariant> variants = new ArrayList<>();
                if(!CollectionUtils.isEmpty(variant)){
                    for(String v : variant) {
                        if(v != null && !v.isBlank()){
                            variants.add(LCDMenuImageVariant.builder().variant(v).lcdMenuImage(image).build());
                        }
                    }
                    variants =  lcdMenuImageVariantDao.saveAll(variants);
                }

            }catch (Exception e) {
                e.printStackTrace();
            }
        }

        return uploadedFiles;
    }

    // Helper method to append only non-null values
    private void appendIfNotNull(StringBuilder builder, String value, String separator) {
        if (!StringUtils.isEmpty(value)) {
            builder.append(value).append(separator);
        } else {
            return; // Stop appending when null is encountered
        }
    }

    public String buildFolderPathFiltered(Map<String,String> params){
        Map<String,String> filteredParams = filterSearchParams(params);
        return buildPathFromParams(filteredParams);
    }

    /**
     * Build a path from parameters in the correct order based on step order
     * @param params Map of parameter name to parameter value
     * @return Path string with parameters in the correct order
     */
    private String buildPathFromParams(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        StringBuilder path = new StringBuilder();

        // Get steps in their defined order
        List<LCDMenuStep> orderedSteps = stepRepository.findByStatusOrderByStepOrderAsc(AppConstants.ACTIVE);

        // Create a map to store step name to order mapping
        Map<String, Integer> stepOrderMap = new HashMap<>();
        for (int j = 0; j < orderedSteps.size(); j++) {
            stepOrderMap.put(orderedSteps.get(j).getStepName(), j);
        }

        // Sort parameters based on step order
        List<Map.Entry<String, String>> sortedEntries = new ArrayList<>(params.entrySet());
        sortedEntries.sort((e1, e2) -> {
            Integer order1 = stepOrderMap.getOrDefault(e1.getKey(), Integer.MAX_VALUE);
            Integer order2 = stepOrderMap.getOrDefault(e2.getKey(), Integer.MAX_VALUE);
            return order1.compareTo(order2);
        });

        // Add all parameters to the path in the correct order
        for (Map.Entry<String, String> entry : sortedEntries) {
            String value = entry.getValue();
            if (value != null && !value.isEmpty()) {
                appendIfNotNull(path, value, "/");
            }
        }

        return path.toString();
    }

    public List<LCDMenuImageFolder> getFoldersByStep(String step) {
        return folderRepository.findByStepAndStatus(step,AppConstants.ACTIVE);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public LCDMenuImageFolder createFolder(String step, String name) {
        LCDMenuImageFolder folder = new LCDMenuImageFolder();
        // Check if folder already exists
        Optional<LCDMenuImageFolder> existingFolder = folderRepository.findByStepAndName(step, name);
        if (existingFolder.isPresent()) {
            folder = existingFolder.get();
            folder.setStatus(AppConstants.ACTIVE);
            folder.setUpdatedAt(AppUtils.getCurrentTimestamp());
        }else{
            folder.setStep(step);
            folder.setName(name);
            folder.setStatus(AppConstants.ACTIVE);
            folder.setCreatedAt(AppUtils.getCurrentTimestamp());
            folder.setUpdatedAt(AppUtils.getCurrentTimestamp());
        }

        return folderRepository.save(folder);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public void deleteFolder(String step, String name) {
        Optional<LCDMenuImageFolder> folder = folderRepository.findByStepAndName(step, name);
        if (folder.isPresent()) {
            LCDMenuImageFolder existingFolder = folder.get();
            existingFolder.setStatus(AppConstants.IN_ACTIVE);
            existingFolder.setUpdatedAt(AppUtils.getCurrentTimestamp());
            folderRepository.save(existingFolder);
        } else {
            throw new RuntimeException("Folder not found");
        }
    }


    public List<LCDMenuImageDomain> getFilesForStep(String step, Map<String, String> params) {
        // Use the new parameter-based filtering
        Specification<LCDMenuImage> spec = LCDMenuImageSpecification.filterByParams(params);
        return DtoDataMapperUtil.mapToLCDMenuImageDomainList(imageRepository.findAll(spec));
    }


    public List<LCDMenuImageFolder> getFoldersForStep(String step, Map<String, String> params) {
        // For version step, return all versions
        if ("version".equals(step)) {
            List<String> versions = new ArrayList<>();
            List<LcdMenuImagesUploadVersion> lcdMenuImagesUploadVersions = lcdMenuImagesUploadVersionDao.findAll();
            for(LcdMenuImagesUploadVersion lcdMenuImagesUploadVersion : lcdMenuImagesUploadVersions){
                versions.add(lcdMenuImagesUploadVersion.getVersionNo().toString());
            }
            return versions.stream()
                    .map(v -> LCDMenuImageFolder.builder()
                            .step("version")
                            .name(v)
                            .build())
                    .collect(Collectors.toList());
        }

        // For other steps, return folders based on the current path
        String path = buildPathFromParams(params);
        return folderRepository.findByStep(step);
    }

    // This method is now replaced by the more comprehensive version above
    // that sorts parameters based on step order

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public void deactivateImage(Long imageId) {
        Optional<LCDMenuImage> image = imageRepository.findById(imageId);
        if (image.isPresent()) {
            LCDMenuImage existingImage = image.get();
            existingImage.setStatus(AppConstants.IN_ACTIVE);
            existingImage.setUploadTime(AppUtils.getCurrentTimestamp());
            imageRepository.save(existingImage);
        } else {
            throw new RuntimeException("Image not found");
        }
    }

    // Helper method to get file extension
    private String getFileExtension(String filename) {
        if (filename == null) return ".jpg";
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) return ".jpg";
        return filename.substring(lastDotIndex);
    }

    public static String removeFileName(String fullPath) {
        if (fullPath == null || fullPath.isEmpty()) {
            return "";
        }

        int lastSlashIndex = fullPath.lastIndexOf('/');

        if (lastSlashIndex == -1) {
            return fullPath; // No slash found, return as is
        }

        return fullPath.substring(0, lastSlashIndex);
    }

    public List<String> getAllSteps() {
        return stepRepository.findByStatusOrderByStepOrderAsc(AppConstants.ACTIVE)
                .stream()
                .map(LCDMenuStep::getStepName)
                .collect(Collectors.toList());
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",
            readOnly = false, propagation = Propagation.REQUIRED)
    public void createNewStep(String stepName) {
        int maxOrder = stepRepository.findByStatusOrderByStepOrderAsc(AppConstants.ACTIVE)
                .stream()
                .mapToInt(LCDMenuStep::getStepOrder)
                .max()
                .orElse(0);

        LCDMenuStep step = LCDMenuStep.builder()
                .stepName(stepName)
                .stepOrder(maxOrder + 1)
                .status(AppConstants.ACTIVE)
                .createdAt(AppUtils.getCurrentTimestamp())
                .updatedAt(AppUtils.getCurrentTimestamp())
                .build();

        stepRepository.save(step);
    }

    // Add to existing getFolderContents method
    public Map<String, Object> getFolderContents(String path) {
        Map<String, Object> contents = new HashMap<>();

        // Existing folder content logic...

        // Add preview images and count for each folder
        if (!CollectionUtils.isEmpty((Collection<?>) contents.get("folders"))) {
            List<Map<String, Object>> foldersWithPreviews = new ArrayList<>();
            for (LCDMenuImageFolder folder : (List<LCDMenuImageFolder>)contents.get("folders")) {
                String folderPath = path + "/" + folder.getName();
                List<LCDMenuImage> previews = imageRepository.findByPathStartingWith(
                    folderPath,
                    PageRequest.of(0, 5, Sort.by("uploadTime").descending())
                );
                long count = imageRepository.countByPathStartingWith(folderPath);

                Map<String, Object> folderData = new HashMap<>();
                folderData.put("folder", folder);
                folderData.put("previews", previews.stream().map(img -> img.getUrl()).collect(Collectors.toList()));
                folderData.put("totalCount", count);
                foldersWithPreviews.add(folderData);
            }
            contents.put("folders", foldersWithPreviews);
        }

        return contents;
    }

    // Add method for date range downloads
    public List<LCDMenuImage> getImagesInPathByDateRange(String path, LocalDateTime startDate, LocalDateTime endDate) {
        return imageRepository.findByPathStartingWithAndUploadTimeBetween(path, startDate, endDate);
    }


}
