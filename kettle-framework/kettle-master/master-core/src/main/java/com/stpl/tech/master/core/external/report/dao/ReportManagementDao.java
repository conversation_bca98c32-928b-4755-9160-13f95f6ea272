package com.stpl.tech.master.core.external.report.dao;

import com.stpl.tech.kettle.report.metadata.model.ReportSummary;
import com.stpl.tech.kettle.report.metadata.model.ReportVersionDetail;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.data.model.XMLReportDefinitionData;
import com.stpl.tech.master.data.model.XMLReportVersionData;

import java.util.List;

public interface ReportManagementDao extends AbstractDao {

    List<ReportSummary> getAllReportVersionHistoryById(Integer reportId);

    List<XMLReportDefinitionData> getAllReportCategories(String executionEnvironment);

    List<XMLReportDefinitionData> getAllReportCategoryList(String executionEnvironment);

    List<XMLReportVersionData> getXMLReportVersionData(Integer reportId);

	int addNewReport(ReportSummary report) throws DataUpdationException;

	int updateStatus(int reportId, String status);

    int addNewReportVersion(ReportVersionDetail report);

    int setDefaultReportVersion(int reportId, int versionId);

    XMLReportVersionData getActiveReportVersion(Integer reportId, Integer versionNo);
}
