/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * UnitProductMapping generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_PAYMENT_MODE_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "UNIT_ID", "PAYMENT_MODE_ID" }))
public class UnitPaymentModeMapping implements java.io.Serializable {

	private Integer unitPaymentModeMappingId;
	private PaymentMode paymentMode;
	private UnitDetail unitDetail;
	private String mappingStatus;

	public UnitPaymentModeMapping() {
	}

	public UnitPaymentModeMapping(PaymentMode paymentMode, UnitDetail unitDetail, String mappingStatus) {
		this.paymentMode = paymentMode;
		this.unitDetail = unitDetail;
		this.mappingStatus = mappingStatus;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_PAYMENT_MODE_MAPPING_ID", unique = true, nullable = false)
	public Integer getUnitPaymentModeMappingId() {
		return this.unitPaymentModeMappingId;
	}

	public void setUnitPaymentModeMappingId(Integer unitProdRefId) {
		this.unitPaymentModeMappingId = unitProdRefId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PAYMENT_MODE_ID", nullable = false)
	public PaymentMode getPaymentMode() {
		return this.paymentMode;
	}

	public void setPaymentMode(PaymentMode paymentMode) {
		this.paymentMode = paymentMode;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	public UnitDetail getUnitDetail() {
		return this.unitDetail;
	}

	public void setUnitDetail(UnitDetail unitDetail) {
		this.unitDetail = unitDetail;
	}

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return this.mappingStatus;
	}

	public void setMappingStatus(String productStatus) {
		this.mappingStatus = productStatus;
	}

}
