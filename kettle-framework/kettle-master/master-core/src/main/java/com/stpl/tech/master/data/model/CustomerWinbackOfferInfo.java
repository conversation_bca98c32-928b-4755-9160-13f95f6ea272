package com.stpl.tech.master.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ExcelSheet(value = "Winback Offer Info")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Entity
@Table(name = "CUSTOMER_WINBACK_OFFER_INFO")
public class CustomerWinbackOfferInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    @ExcelField
    private Integer id;
    @Column(name = "CUSTOMER_ID")
    @ExcelField
    private Integer customerId;
    @Column(name = "CONTACT_NUMBER")
    @ExcelField
    private String contactNumber;
    @Column(name = "CUSTOMER_NAME")
    @ExcelField
    private String customerName;
    @Column(name = "OFFER_ID")
    @ExcelField
    private Integer offerId;
    @Column(name = "OFFER_DESCRIPTION",columnDefinition = "TEXT")
    @ExcelField
    private String offerDescription;
    @Column(name = "COUPON_CODE")
    @ExcelField
    private String couponCode;
    @Column(name = "COUPON_DETAIL_ID")
    @ExcelField
    private Integer couponDetailId;
    @Column(name = "START_DATE")
    @ExcelField
    private String startDate;
    @Column(name = "END_DATE")
    @ExcelField
    private String endDate;
    @Column(name = "VALIDITY_DAYS")
    @ExcelField
    private Integer validityInDays;
    @Column(name = "COMPANSATION_REASON",columnDefinition = "TEXT")
    @ExcelField
    private String compansationReason;
    @Column(name = "COMMENT",columnDefinition = "TEXT")
    @ExcelField
    private String comment;
    @Column(name = "REFERENCE_ORDER_ID")
    @ExcelField
    private String orderId;
    @Column(name = "ORDER_SOURCE")
    @ExcelField
    private String orderSource;
    @Column(name = "COMPLAIN_SOURCE")
    @ExcelField
    private String complainSource;
    @Column(name = "CHANNEL_PARTNER")
    @ExcelField
    private String channelPartner;
    @Column(name = "IS_NOTIFIED")
    @ExcelField
    private String isNotified;
    @Column(name = "UPDATED_BY")
    @ExcelField
    private Integer updatedBy;
    @Column(name = "UPDATED_AT")
    @ExcelField
    private Date updatedAt;

}
