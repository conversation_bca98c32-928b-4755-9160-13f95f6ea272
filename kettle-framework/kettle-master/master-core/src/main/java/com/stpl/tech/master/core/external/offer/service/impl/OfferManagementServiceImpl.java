/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.offer.service.impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.report.metadata.model.DayType;
import com.stpl.tech.kettle.report.metadata.model.NameValue;
import com.stpl.tech.master.CampaignImageDetail;
import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.OfferCategoryHelper;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.dao.OfferMetaDataManagementDao;
import com.stpl.tech.master.core.external.offer.dao.impl.OfferDescriptionMetadataDao;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.external.partner.dao.ChannelPartnerDao;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.BannerMetadata;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CouponDetailMappingData;
import com.stpl.tech.master.data.model.CustomerOfferMappingData;
import com.stpl.tech.master.data.model.CustomerWinbackOfferInfo;
import com.stpl.tech.master.data.model.DeliveryCouponAllocationDetailData;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.DeliveryOfferDetailData;
import com.stpl.tech.master.data.model.LaunchOfferData;
import com.stpl.tech.master.data.model.OfferDescriptionMetadata;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.OfferMetadata;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.SignupOffersCouponDetails;
import com.stpl.tech.master.domain.model.AppOfferDetail;
import com.stpl.tech.master.domain.model.BannerMetadataRequest;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.CloneCouponData;
import com.stpl.tech.master.domain.model.CouponBulkUpdateDomain;
import com.stpl.tech.master.domain.model.CouponCloneRequest;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.CustomerWinbackOfferInfoDomain;
import com.stpl.tech.master.domain.model.DeliveryCouponStatus;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.IdentifierType;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.OfferActionType;
import com.stpl.tech.master.domain.model.OfferDayDto;
import com.stpl.tech.master.domain.model.OfferDayTimeDto;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferImageDetail;
import com.stpl.tech.master.domain.model.OfferMetaDataType;
import com.stpl.tech.master.domain.model.OfferResponse;
import com.stpl.tech.master.domain.model.OfferTimeDto;
import com.stpl.tech.master.domain.model.OfferType;
import com.stpl.tech.master.domain.model.OfferTypeFlag;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.ProductGroup;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.util.BudgetCategoryConstants;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.spring.exception.FileArchiveServiceException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.HashGenerator;
import com.stpl.tech.util.RandomStringGenerator;
import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.excelparser.ExcelWriter;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
public class OfferManagementServiceImpl implements OfferManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(OfferManagementServiceImpl.class);

    @Autowired
    private OfferManagementDao offerDao;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    ChannelPartnerDao channelPartnerDao;

    @Autowired
    MasterDataCache masterDataCache;

    @Autowired
    private OfferDescriptionMetadataDao descriptionMetadataDao;

    @Autowired
    private OfferMetaDataManagementDao offerMetaDataManagementDao;


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<CouponDetail> getRegularCoupons(Integer unitId, Integer loggedInBrandId) {
        return filterCoupons(offerDao.getRegularCoupons(loggedInBrandId),unitId,false);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public OfferDetail addOffer(OfferDetail offerDetail) throws OfferValidationException {
        return offerDao.addOffer(offerDetail);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public OfferDetail updateOffer(OfferDetail offerDetail) throws OfferValidationException {
        return offerDao.updateOffer(offerDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public OfferDetail getOffer(int offerId) {
        return offerDao.getOffer(offerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public OfferDetailData getOfferDetailData(int offerId) {
        return offerDao.getOfferDetailData(offerId);
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<OfferDetail> getAllOffers(boolean trimmed) {
        return offerDao.getAllOffers(trimmed);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<CouponDetail> getOfferCoupons(int offerId, boolean applyLimit) {
        return offerDao.getOfferCoupons(offerId, applyLimit);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<IdCodeName> getMarketingPartners() {
        return offerDao.getMarketingPartners();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public IdCodeName addMarketingPartners(IdCodeName marketingPartner) {
        return offerDao.addMarketingPartners(marketingPartner);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public IdCodeName updateMarketingPartners(IdCodeName marketingPartner) {
        return offerDao.updateMarketingPartners(marketingPartner);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public CouponDetail searchCoupon(String couponCode, boolean applyLimit) {
        return offerDao.getCouponDetail(couponCode, true, true, applyLimit);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public CouponDetail addCoupon(CouponDetail coupon) {
        return offerDao.addCoupon(coupon);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public CouponDetail updateCoupon(CouponDetail coupon) {
        return offerDao.updateCoupon(coupon, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public OfferDayDto getOfferDayScheduleDetails(Integer offerId) {
        List<String> mappingTypes = List.of(OfferMetaDataType.DAYS.name());
        Map<String, Map<String, OfferMetadata>> offerMetadataMap = getOfferMetaDataMap(mappingTypes, offerId, AppConstants.ACTIVE);
        Map<String, OfferMetadata> dayTypeMap = offerMetadataMap.getOrDefault(OfferMetaDataType.DAYS.name(), new HashMap<>());
        return getOfferDayScheduleDetails(dayTypeMap);
    }

    public OfferDayDto getOfferDayScheduleDetails(Map<String, OfferMetadata> dayTypeMap) {
        OfferDayDto dto = new OfferDayDto();
        for(DayType dayType : DayType.values()) {
            Pair<DayType, Boolean> newPair = new Pair<>();
            OfferMetadata metadata = dayTypeMap.get(dayType.name());
            if(metadata == null) {
                newPair.setKey( dayType );
                newPair.setValue( false );
            } else {
                newPair.setKey( DayType.fromString(metadata.getMappingValue()) );
                newPair.setValue( AppConstants.ACTIVE.equalsIgnoreCase(metadata.getStatus()) );
            }
            dto.getApplicableDays().add(newPair);
        }
        return dto;
    }

    @Override
    public OfferTimeDto getOfferTimeDetails(Integer offerId) {
        List<String> mappingTypes = List.of(OfferMetaDataType.TIME_RANGE.name());
        Map<String, Map<String, OfferMetadata>> offerMetadataMap = getOfferMetaDataMap(mappingTypes, offerId, AppConstants.ACTIVE);
        if( !CollectionUtils.isEmpty(offerMetadataMap) ) {
            Map<String, OfferMetadata> timeTypeMap = offerMetadataMap.getOrDefault(OfferMetaDataType.TIME_RANGE.name(), new HashMap<>());
            return getOfferTimeDetails(timeTypeMap);
        }
        return null;
    }

    public OfferTimeDto getOfferTimeDetails(Map<String, OfferMetadata> timeTypeMap) {
        if( !CollectionUtils.isEmpty(timeTypeMap) ) {
            String timeKey = timeTypeMap.keySet().iterator().next();
            OfferTimeDto dto = new OfferTimeDto();
            dto.setTimeRange(timeKey);
            return dto;
        }
        return null;
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean updateBulkCoupon(CouponBulkUpdateDomain couponBulkUpdateDomain) {
        updateOfferDateAndTime(couponBulkUpdateDomain);
        return offerDao.updateBulkCoupon(couponBulkUpdateDomain);
    }

    private void updateOfferDateAndTime(CouponBulkUpdateDomain couponBulkUpdateDomain) {
        List<String> mappingTypes = List.of(OfferMetaDataType.DAYS.name(), OfferMetaDataType.TIME_RANGE.name());
        Map<String, Map<String, OfferMetadata>> offerMetaDataMap = getOfferMetaDataMap(mappingTypes, couponBulkUpdateDomain.getOfferId(), null);
        OfferDetailData offerDetailData = offerDao.getOfferDetailData(couponBulkUpdateDomain.getOfferId());
        if( !CollectionUtils.isEmpty(couponBulkUpdateDomain.getOfferDayDetails().getApplicableDays()) ){
            handleDayMetadata(couponBulkUpdateDomain.getOfferDayDetails(), offerMetaDataMap.getOrDefault(OfferMetaDataType.DAYS.name(), new HashMap<>()), offerDetailData);
        }
        handleTimeRangeMetadata(couponBulkUpdateDomain.getOfferTimeDetails(), offerMetaDataMap.getOrDefault(OfferMetaDataType.TIME_RANGE.name(), new HashMap<>()), offerDetailData);
    }

    private Map<String, Map<String, OfferMetadata>> getOfferMetaDataMap(List<String> mappingTypes, Integer offerId, String status) {
        List<OfferMetadata> offerMetadataList = offerMetaDataManagementDao.findAllByOfferIdAndMappingTypes(offerId, mappingTypes, status);
        Map<String, Map<String, OfferMetadata>> offerMetaDataMap = new HashMap<>();
        for(OfferMetadata metadata : offerMetadataList) {
            Map<String, OfferMetadata> valueMap = offerMetaDataMap.computeIfAbsent(metadata.getMappingType(), k -> new HashMap<>());
            valueMap.put(metadata.getMappingValue(), metadata);
        }
        return offerMetaDataMap;
    }

    private void handleDayMetadata(OfferDayDto offerDayTimeDetails, Map<String, OfferMetadata> existingDayMap, OfferDetailData offerDetailData) {
        Set<String> uiDayNames = offerDayTimeDetails.getApplicableDays().stream()
                .filter(Pair::getValue)
                .map(pair -> pair.getKey().name())
                .collect(Collectors.toSet());

        Set<String> processed = new HashSet<>();
        for (String day : uiDayNames) {
            OfferMetadata existing = existingDayMap.get(day);
            if (existing != null) {
                if (!AppConstants.ACTIVE.equalsIgnoreCase(existing.getStatus())) {
                    existing.setStatus(AppConstants.ACTIVE);
                    saveOrUpdateOfferMetadata(existing);
                }
            } else {
                OfferMetadata newMeta = createOfferMetadata(OfferMetaDataType.DAYS.name(), day, offerDetailData);
                saveOrUpdateOfferMetadata(newMeta);
            }
            processed.add(day);
        }

        for (Map.Entry<String, OfferMetadata> entry : existingDayMap.entrySet()) {
            if (!processed.contains(entry.getKey()) && AppConstants.ACTIVE.equalsIgnoreCase(entry.getValue().getStatus())) {
                entry.getValue().setStatus(AppConstants.IN_ACTIVE);
                saveOrUpdateOfferMetadata(entry.getValue());
            }
        }
    }


    private void handleTimeRangeMetadata(OfferTimeDto offerTimeDetails, Map<String, OfferMetadata> existingTimeMap, OfferDetailData offerDetailData) {
        String uiTimeRange = offerTimeDetails.getTimeRange();
        boolean matchFound = false;

        for (Map.Entry<String, OfferMetadata> entry : existingTimeMap.entrySet()) {
            if (entry.getKey().equals(uiTimeRange)) {
                matchFound = true;
                if (AppConstants.IN_ACTIVE.equalsIgnoreCase(entry.getValue().getStatus())) {
                    entry.getValue().setStatus(AppConstants.ACTIVE);
                    saveOrUpdateOfferMetadata(entry.getValue());
                }
            } else {
                if (AppConstants.ACTIVE.equalsIgnoreCase(entry.getValue().getStatus())) {
                    entry.getValue().setStatus(AppConstants.IN_ACTIVE);
                    saveOrUpdateOfferMetadata(entry.getValue());
                }
            }
        }
        if (!matchFound && StringUtils.hasText(uiTimeRange)) {
            OfferMetadata newMeta = createOfferMetadata(OfferMetaDataType.TIME_RANGE.name(), uiTimeRange, offerDetailData);
            saveOrUpdateOfferMetadata(newMeta);
        }
    }

    private OfferMetadata createOfferMetadata(String type, String value, OfferDetailData offerDetailData) {
        OfferMetadata meta = new OfferMetadata();
        meta.setOfferId(offerDetailData);
        meta.setMappingType(type);
        meta.setMappingValue(value);
        meta.setStatus( AppConstants.ACTIVE );
        return meta;
    }

    private void saveOrUpdateOfferMetadata(OfferMetadata meta) {
        offerMetaDataManagementDao.add(meta);
    }



    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean updateCoupons(List<CouponDetail> coupons) {
        if (Objects.nonNull(coupons) && !coupons.isEmpty()) {
            Integer row = offerDao.updateAllCoupon(coupons.get(0));
            if(row > 0){
                return true;
            }
            return false;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public List<String> generateUniqueCoupons(String couponCode, String couponPrefix, int replicateCount) throws DataUpdationException {
        return offerDao.generateUniqueCoupons(couponCode, couponPrefix, replicateCount);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<String> getUniqueCoupons(String couponCode, String couponPrefix, int replicateCount) throws DataUpdationException {
        return offerDao.getUniqueCoupons(couponCode, couponPrefix, replicateCount);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public List<String> generateCopyCoupons(CouponDetail modalCouponCode, List<String> couponCodeSet) throws DataUpdationException {
        return offerDao.generateCopyCoupons(modalCouponCode, couponCodeSet);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public CouponDetail getModalCoupon(String modelCouponCode){
        return offerDao.getModalCoupon(modelCouponCode);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public boolean checkCodeAvailiblity(String couponCode) {
        return offerDao.getCouponDetail(couponCode, false, false, false) != null ? false : true;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean changeMarketingPartnerStatus(int marketingPartnerId, String status) {
        return offerDao.changeMarketingPartnerStatus(marketingPartnerId, status);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public IdCodeName getMarketingPartner(String key) {
        return offerDao.getMarketingPartner(key);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean updateCouponMapping(int couponMappingId, String status) {
        return offerDao.updateCouponMapping(couponMappingId, status);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean updateCouponMappings(List<CouponDetail> coupons) {
        return offerDao.updateCouponMappings(coupons);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<IdName> getOfferAccountsCategories() {
        return offerDao.getOfferAccountsCategories();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void markExpiredOffersAsArchived() {
        offerDao.markExpiredOffersAsArchived();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean existCustomerOfferMappingData(int offerDetailId, String number) {
        return offerDao.existCustomerOfferMappingData(offerDetailId, number);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public LaunchOfferData availLaunchOffer(EnvType env, LaunchOfferData input, boolean addUnitMapping) {
        return offerDao.availLaunchOffer(env, input, addUnitMapping);
    }

    @Override
    public Map<String, List<OfferCategoryType>> getOfferCategories() {
        Map<String, List<OfferCategoryType>> offerCategories = new HashMap<>();
        for (OfferCategoryType category : OfferCategoryType.values()) {
            if(!OfferCategoryType.FREEBIE_STRATEGY_PERCENTAGE.name().equals(category.name()) && !OfferCategoryType.FREEBIE_STRATEGY_FLAT.name().equals(category.name())){
                String parentCategory = OfferCategoryHelper.getCategory(category);
                List<OfferCategoryType> list = offerCategories.get(parentCategory);
                if (list == null) {
                    list = new ArrayList<>();
                }
                list.add(category);
                offerCategories.put(parentCategory, list);
            }
        }
        return offerCategories;
    }

    @Override
    public List<NameValue> getOfferActionTypes() {
        List<NameValue> list = new ArrayList<>();
        for (OfferActionType offerActionType : OfferActionType.values()) {
            list.add(new NameValue(offerActionType.offerName(), offerActionType.value()));
        }
        return list;
    }

    @Override
    public List<OfferTypeFlag> getOfferTypes() {
        List<OfferTypeFlag> list = new ArrayList<>();
        for (OfferType offerTypes : OfferType.values()) {
            list.add(new OfferTypeFlag(offerTypes.getOfferType(), offerTypes.getValidateCoupon()));
        }
        return list;
    }

    @Override
    public List<AppOfferDetail> getActInactOffers() {
        return offerDao.getActInactOffers();
    }

    @Override
    public List<AppOfferDetail> getAppOffersByPartnerId(Integer partnerId) {
        return offerDao.getAppOffersByPartnerId(partnerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean setStatusForAppOffer(IdCodeName idCodeName) {
        return offerDao.setStatusForAppOffer(idCodeName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean setOrderingForAppOffer(List<IdIndex> list) {
        return offerDao.setOrderingForAppOffer(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OfferDetail> getOffer(String key, boolean trimmed) {
        return filterBrandOffers(offerDao.getOffer(key, trimmed));
    }

    private List<OfferDetail> filterBrandOffers(List<OfferDetail> offerDetailList) {
        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedBrands = MasterUtil.getMappedBrands();
            offerDetailList = offerDetailList.stream().filter(d -> d.getBrandId() == null || mappedBrands.contains(d.getBrandId())).toList();
        }
        return offerDetailList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public OfferImageDetail saveOfferImage(MimeType mimeType, String couponCode, String imageType, MultipartFile file, String s3OfferBucket, String hostURL) {

        try {
            String baseDir = "offer-service/app_offer_image";
            String fileName = file.getOriginalFilename();
//            String extension = FilenameUtils.getExtension(fileName);

            fileName = fileName.replaceAll(" ", "_").toLowerCase();

            LOG.info(":::::: Request to upload New App Offer Image ::::::");

            FileDetail s3File = fileArchiveService.saveFileToS3(s3OfferBucket,
                    baseDir, fileName, file, true);
            if (s3File != null) {
                return new OfferImageDetail(s3File.getName(), hostURL + s3File.getName());
            }
        } catch (FileArchiveServiceException e) {
            LOG.error("Encountered error while uploading Offer Image to S3", e);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public AppOfferDetail addNewAppOffer(AppOfferDetail offer) {

        return offerDao.addNewOffer(offer);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public AppOfferDetail updateAppOffer(AppOfferDetail offer) {

        return offerDao.updateAppOffer(offer);
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public List<BannerMetadata> saveBannerMetadata(List<BannerMetadataRequest> list) {
        List<BannerMetadata> banner = new ArrayList<>();
        if (list != null) {

            for (BannerMetadataRequest request : list) {
                BannerMetadata bannerMetadata = new BannerMetadata();
                bannerMetadata.setDescription(request.getDescription());
                bannerMetadata.setName(request.getName());
                bannerMetadata.setType(request.getType());
                bannerMetadata.setValue(request.getValue());
                banner.add(offerDao.add(bannerMetadata));
            }
        }

        return banner;
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<BannerMetadataRequest> getBannerMetadata() {
        List<BannerMetadataRequest> list = new ArrayList<>();
        List<BannerMetadata> banner = offerDao.findAll(BannerMetadata.class);
        for (BannerMetadata request : banner) {
            BannerMetadataRequest bannerMetadata = new BannerMetadataRequest();
            bannerMetadata.setId(request.getId());
            bannerMetadata.setDescription(request.getDescription());
            bannerMetadata.setName(request.getName());
            bannerMetadata.setType(request.getType());
            bannerMetadata.setValue(request.getValue());
            list.add(bannerMetadata);
        }
        return list;
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<ProductGroup> getCategoryFromMenuSequence(MenuApp menuApp) throws DataNotFoundException {
        List<Integer> menuSequenceIds = channelPartnerDao.getMenuSequenceIdsAsPerMenuApp(menuApp);
        if (menuSequenceIds != null && menuSequenceIds.size() > 0) {
            List<Integer> categoryIds = channelPartnerDao.getCategoryIdsMenuSequenceMapping(menuSequenceIds);
            if (categoryIds != null && categoryIds.size() > 0) {
                List<ProductGroupData> productGroupData = channelPartnerDao.getProductGroupDataAsPerIdAndMenuApp(categoryIds, menuApp);
                return MasterDataConverter.convert(productGroupData, masterDataCache);

            }
        }
        throw new DataNotFoundException("No category is mapped menu sequence for:" + menuApp);
    }

    @Override
    public SignupOffersCouponDetails getCouponDetails() {
        return offerDao.getCouponDetails();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean addSignupCouponMapping(SignupOffersCouponDetails couponDetails) {
        try {
            List<SignupOffersCouponDetails> offersCouponDetails = offerDao.changeSignupStatus();
            for (SignupOffersCouponDetails details : offersCouponDetails) {
                details.setCouponStatus(AppConstants.IN_ACTIVE);
                offerDao.update(details);
            }
            offerDao.add(couponDetails);
            return true;
        } catch (Exception e) {
            LOG.error("Error Caught :::", e);
            return false;
        }
    }

    @Override
    public CouponCloneResponse generateCoupon(@RequestBody final CouponCloneRequest coupon) throws DataUpdationException {
    	return generateCoupon(coupon, null);
    }
    @Override
	public CouponCloneResponse generateCoupon(CouponCloneRequest coupon, CouponDetailData cloneCoupon)
			throws DataUpdationException {
		LOG.info("Request for Clone Coupon with code: {}", coupon);
		long startTime = System.currentTimeMillis();
		if (cloneCoupon == null) {
			cloneCoupon = offerDao.getCoupon(coupon.getCode());
		}
		CouponCloneResponse response = new CouponCloneResponse();
		response.setCode(coupon.getCode());
		response.setIdentifier(coupon.getIdentifierType());
		List<String> errors = new ArrayList<String>();
		int length = 0;
		Date startDate = null;
		Date endDate = null;
		String startDateString = null;
		String endDateString = null;
		List<String> couponCodes = new ArrayList<>();
		Integer usageCount = coupon.getUsageCount() == null ? 1 : coupon.getUsageCount();
        CouponDetail modalCouponDetail= new CouponDetail();
		if (cloneCoupon == null) {
			errors.add("Coupon Code Does Not Exist : " + coupon.getCode());
		} else {
//			couponCodes = generateUniqueCoupons(coupon.getCode(), coupon.getPrefix(),
//					coupon.getIdentifier().size());
			couponCodes = new ArrayList<>();
			int maxIteration = 3;
			while (couponCodes.size() == 0 && maxIteration > 0) {
				couponCodes.add(new RandomStringGenerator().getRandomSingleCode(coupon.getPrefix(), 6));
				couponCodes.add(new RandomStringGenerator().getRandomSingleCode(coupon.getPrefix(), 6));
				couponCodes.add(new RandomStringGenerator().getRandomSingleCode(coupon.getPrefix(), 6));
				couponCodes = offerDao.getValidCoupon(couponCodes);
				maxIteration = maxIteration - 1;
				if (maxIteration == 0 && couponCodes.isEmpty()) {
					errors.add("Maximum iteration reached and no offer generated");
				}
			}
			if (Objects.isNull(couponCodes) || couponCodes.isEmpty()) {
				errors.add("Unable to create Coupon code");
			}

            modalCouponDetail = addCouponDetailData(coupon.getCode(), couponCodes.get(0));
			length = coupon.getIdentifier().size() < couponCodes.size() ? coupon.getIdentifier().size()
					: couponCodes.size();
			OfferDetail offer = getOffer(cloneCoupon.getOfferDetail().getOfferDetailId());
			response.setDescription(offer.getDescription());
			if (!AppConstants.ACTIVE.equals(offer.getStatus())) {
				errors.add("Offer is in " + offer.getStatus() + " state");
			}
			if (coupon.getStartDay() != null) {
				startDate = AppUtils.getDate(coupon.getStartDay(), AppConstants.DATE_FORMAT);
			} else {
				startDate = AppUtils.getNextDate(AppUtils.getBusinessDate());
			}
			if (coupon.getValidDays() != null) {
				endDate = AppUtils.getDayBeforeOrAfterDay(startDate, coupon.getValidDays() - 1);
			} else {
				endDate = cloneCoupon.getEndDate();
				if (endDate.before(startDate)) {
					errors.add("Coupon End Date " + endDate + " cannot be before startDate  " + startDate
							+ " as it is picked up from cloned coupon end date which is " + endDate);
				}
			}
			startDateString = AppUtils.getDateString(startDate, AppConstants.DATE_FORMAT);
			endDateString = AppUtils.getDateString(endDate, AppConstants.DATE_FORMAT);
			if (endDate.after(offer.getEndDate())) {
				errors.add("Offer expires on " + offer.getEndDate() + " which is prior to coupon end date "
						+ endDateString);
			}

		}
		if (errors != null && errors.size() > 0) {
			response.setErrors(errors);
			return response;
		}
		Map<String, CouponData> map = new HashMap<>();
		for (int i = 0; i < length; i++) {
			String generatedCoupon = couponCodes.get(i);
			String identifier = coupon.getIdentifier().get(i);
			CouponDetail detail = modalCouponDetail;
			if (identifier != null) {
				CouponMapping mapping = new CouponMapping();
				mapping.setDataType(String.class.getCanonicalName());
				mapping.setGroup(1);
				mapping.setMinValue("1");
				mapping.setStatus("ACTIVE");
				if (IdentifierType.CONTACT_NUMBER.equals(coupon.getIdentifierType())) {
					mapping.setType(CouponMappingType.CONTACT_NUMBER.name());
					mapping.setValue(identifier);
				} else if (IdentifierType.CUSTOMER_ID.equals(coupon.getIdentifierType())) {
					mapping.setType(CouponMappingType.CUSTOMER.name());
					mapping.setValue(identifier);
				}
				detail.getCouponMappingList().add(mapping);
				if (coupon.getApplicableRegion() != null) {
					CouponMapping mapping1 = new CouponMapping();
					mapping1.setDataType(String.class.getCanonicalName());
					mapping1.setGroup(1);
					mapping1.setMinValue("1");
					mapping1.setStatus("ACTIVE");
					mapping1.setType(CouponMappingType.UNIT_REGION.name());
					mapping1.setValue(coupon.getApplicableRegion());
					detail.getCouponMappingList().add(mapping1);
				}
				if (coupon.getApplicableRegion() == null && coupon.getApplicableUnitId() != null) {
					CouponMapping mapping2 = new CouponMapping();
					mapping2.setDataType(String.class.getCanonicalName());
					mapping2.setGroup(1);
					mapping2.setMinValue("1");
					mapping2.setStatus("ACTIVE");
					mapping2.setType(CouponMappingType.UNIT.name());
					mapping2.setValue(coupon.getApplicableUnitId() + "");
					detail.getCouponMappingList().add(mapping2);
				}

			}
			detail.setStartDate(startDate);
			detail.setEndDate(endDate);
			detail.setUsage(0);
			detail.setMaxUsage(usageCount);
			detail.setMaxCustomerUsage(coupon.getMaxCustomerUsage());
			if (usageCount > 1) {
				detail.setReusable(true);
				detail.setReusableByCustomer(true);
			} else {
				detail.setReusable(false);
				detail.setReusableByCustomer(false);
			}
			map.put(identifier, new CouponData(generatedCoupon, startDateString, endDateString, usageCount,
					detail.getId(), detail.getOffer().getId()));
			updateCoupon(detail);
		}
		LOG.info("DINE_IN_POST_OFFER coupon generated in , ---------------- ,{}, milliseconds",
				System.currentTimeMillis() - startTime);
		response.setMappings(map);
		return response;
	}

    @Override
    public void mapCoupon(CouponCloneRequest couponCloneRequest,CouponCloneResponse response){
        CouponDetail couponDetail = offerDao.getCouponDetail(couponCloneRequest.getCode(), true, false, false);
        Map<String, CouponData> map = new HashMap<>();
        int length = couponCloneRequest.getIdentifier().size();
        OfferDetail offer = couponDetail.getOffer();
        response.setDescription(offer.getDescription());
        List<String> errors = new ArrayList<String>();
        if (!AppConstants.ACTIVE.equals(offer.getStatus())) {
            errors.add("Offer is in " + offer.getStatus() + " state");
        }
        if (errors != null && errors.size() > 0) {
            response.setErrors(errors);
        }
        for(int i=0;i<length;i++) {
            String identifier = couponCloneRequest.getIdentifier().get(i);
            boolean isAlreadyActiveMappingExist = false;
            List<CouponDetailMappingData> couponDetailMappingData = offerDao.getCouponMappingsForContactNumber(couponDetail.getId(),identifier);
            for(CouponDetailMappingData data : couponDetailMappingData){
                if(AppConstants.ACTIVE.equals(data.getStatus())){
                    isAlreadyActiveMappingExist = true;
                    break;
                }
            }
            if(isAlreadyActiveMappingExist){
                continue;
            }
            if (identifier != null) {
                CouponMapping mapping = new CouponMapping();
                mapping.setDataType(String.class.getCanonicalName());
                mapping.setGroup(1);
                mapping.setMinValue("1");
                mapping.setStatus("ACTIVE");
                if (IdentifierType.CONTACT_NUMBER.equals(couponCloneRequest.getIdentifierType())) {
                    mapping.setType(CouponMappingType.CONTACT_NUMBER.name());
                    mapping.setValue(identifier);
                } else if (IdentifierType.CUSTOMER_ID.equals(couponCloneRequest.getIdentifierType())) {
                    mapping.setType(CouponMappingType.CUSTOMER.name());
                    mapping.setValue(identifier);
                }
                couponDetail.getCouponMappingList().add(mapping);
            }
            map.put(identifier, new CouponData(couponDetail.getCode(), AppUtils.getDateString(couponDetail.getStartDate()), AppUtils.getDateString(couponDetail.getEndDate()), couponDetail.getUsage(),
                        couponDetail.getId(), couponDetail.getOffer().getId()));
            updateCoupon(couponDetail);
        }
        response.setMappings(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  CouponCloneResponse createCoupon(CloneCouponData info, List<String>contactNumbers, String startDay, Integer applicableUnitId, String applicableRegion, CouponDetailData parentCoupon) throws DataUpdationException {
		CouponCloneRequest request = new CouponCloneRequest();
		request.setIdentifierType(IdentifierType.CONTACT_NUMBER);
		request.setCode(info.getCloneCouponCode());
		request.setPrefix(info.getPrefix());
		request.setStartDay(startDay);
		request.setUsageCount(info.getUsageCount());
        request.setMaxCustomerUsage(info.getMaxCustomerUsage());
		request.setValidDays(info.getValidityInDays());
		request.getIdentifier().addAll(contactNumbers);
		request.setApplicableRegion(applicableRegion);
		request.setApplicableUnitId(applicableUnitId);
		return generateCoupon(request, parentCoupon);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addCustomerOfferMappingData(CouponCloneResponse response,String contactNumber,String acqSrc){
        try {
            CustomerOfferMappingData mapping = new CustomerOfferMappingData();
            mapping.setContactNumber(contactNumber);
            Map<String, CouponData> mappings = response.getMappings();
            mapping.setCouponCode(mappings.get(contactNumber).getCoupon());
            mapping.setCouponDetailId(mappings.get(contactNumber).getCouponDetailId());
            mapping.setCreationTime(AppUtils.getCurrentTimestamp());
            mapping.setOfferDetailId(mappings.get(contactNumber).getOfferDetailId());
            mapping.setNotified(AppConstants.NO);
            mapping.setAcquisitionSource(acqSrc);
            offerDao.add(mapping);
            return true;
        }catch (Exception e){
            LOG.info("Error in updating in customer offer mapping data : {}",e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void setCouponCodeAsInactive(Integer couponDetailId) {
    	offerDao.setCouponCodeAsInactive(couponDetailId);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CampaignDetail addCampaignDetailData(CampaignDetail detail) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        CampaignDetail campaignDetail = offerDao.addNewCampaign(detail);
        String campaignToken = HashGenerator.createHash(AppConstants.MD5_HASH, Integer.toString(campaignDetail.getCampaignId()));
        String longUrl = detail.getPrimaryUrl() + "?" + "utm_source=" + URLEncoder.encode(detail.getCampaignSource(), StandardCharsets.UTF_8.toString())
                + "&utm_medium=" + URLEncoder.encode(detail.getCampaignMedium(), StandardCharsets.UTF_8.toString()) + "&token=" + campaignToken;
        campaignDetail.setLongUrl(longUrl);
        campaignDetail.setCampaignToken(campaignToken);
        return offerDao.addCampaignToken(campaignDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CampaignDetail> getAllCampaigns() {
        return offerDao.getAllCampaigns();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CampaignDetail getCampaignById(Integer campaignId) {
        return offerDao.getCampaignById(campaignId);
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CampaignDetail getActiveCampaignForUnitId(Integer unitId, String strategy) {
		CampaignDetail campaignByUnitId = offerDao.getActiveCampaignByUnitId(unitId,strategy);
		if (campaignByUnitId == null) {
			UnitBasicDetail detail = masterDataCache.getUnitBasicDetail(unitId);
			campaignByUnitId = offerDao.getActiveCampaignByUnitRegion(detail.getRegion(),strategy);
			if (campaignByUnitId == null) {
				campaignByUnitId = offerDao.getActiveCampaignBySystem(strategy);
			}
		}
		return campaignByUnitId;
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CampaignDetailResponse getCampaignByTokenAndStatus(String campaignToken, String status) {
        CampaignDetail detail = offerDao.getCampaignByTokenAndStatus(campaignToken, status);
        if(Objects.nonNull(detail)){
            CampaignDetailResponse response = new CampaignDetailResponse();
            response.setCampaignId(detail.getCampaignId());
            response.setUtmHeading(detail.getUtmHeading());
            response.setUtmDesc(detail.getUtmDesc());
            response.setUtmImageUrl(detail.getUtmImageUrl());
            response.setImage1(detail.getImage1());
            response.setImage2(detail.getImage2());
            response.setImage3(detail.getImage3());
            response.setCrmAppBannerUrl(detail.getCrmAppBannerUrl());
            response.setStartDate(detail.getStartDate());
            response.setEndDate(detail.getEndDate());
            response.setUnitId(detail.getLaunchUnitId());
            response.setUnitName(Objects.nonNull(detail.getLaunchUnitId()) ?
                    masterDataCache.getUnit(detail.getLaunchUnitId()).getName() : null);
            response.setOfferStartDate(detail.getCafeLaunchDate());
            if(Objects.nonNull(detail.getLandingPageDesc()) && detail.getLandingPageDesc().length()>0){
                response.setLandingPageDescription(detail.getLandingPageDesc());
            }else{
                response.setLandingPageDescription("Signup for relaxing free chai");
            }
            return response;
        }else{
            LOG.info("NEO :: No campaign detail found for campaign token :: {} and status :: {}",campaignToken,status);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CampaignDetail> getCampaignsByCampaignDesc(String campaignDesc, Boolean fetchAll) {
        return offerDao.getCampaignsByCampaignNameOrDesc(campaignDesc, fetchAll);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CampaignDetail updateCampaignStatus(CampaignDetail campaignDetail) {
        return offerDao.updateCampaignStatus(campaignDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CampaignDetail updateCampaign(CampaignDetail campaignDetail) throws IOException {
        String longUrl = campaignDetail.getPrimaryUrl() + "?" + "utm_source=" + URLEncoder.encode(campaignDetail.getCampaignSource(), StandardCharsets.UTF_8.toString())
                + "&utm_medium=" + URLEncoder.encode(campaignDetail.getCampaignMedium(), StandardCharsets.UTF_8.toString()) + "&token=" + campaignDetail.getCampaignToken();
        campaignDetail.setLongUrl(longUrl);
        campaignDetail.setShortUrl(createCampaignShortUrl(longUrl));
        return offerDao.updateCampaignDetail(campaignDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CampaignDetail updateCampaignShortUrl(CampaignDetail campaignDetail) {
        return offerDao.updateCampaignDetail(campaignDetail);
    }

    @Override
    public CampaignImageDetail saveCampaignImage(MultipartFile file, String s3OfferBucket, String offerHostUrl) {
        try {
            String baseDir = "offer-service/campaign_image";
            String fileName = file.getOriginalFilename();
            fileName = fileName.replaceAll(" ", "_").toLowerCase();
            LOG.info(":::::: Request to upload New App Campaign Image ::::::");
            FileDetail s3File = fileArchiveService.saveFileToS3(s3OfferBucket,
                    baseDir, fileName, file, true);
            if (s3File != null) {
                return new CampaignImageDetail(s3File.getName(), offerHostUrl + s3File.getName());
            }
        } catch (FileArchiveServiceException e) {
            LOG.error("Encountered error while uploading Offer Image to S3", e);
        }
        return null;
    }

    @Override
    public String createCampaignShortUrl(String longUrl) throws IOException {
        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(masterDataCache.getBrandMetaData().get(1));
        ShortUrlData shortUrlData = smsWebServiceClient.getShortUrl(longUrl);
        if (Objects.nonNull(shortUrlData)) {
            return shortUrlData.getUrl();
        } else {
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addDeliveryCoupons(List<DeliveryCouponDetailData> couponDetailDataList) {
        offerDao.addDeliveryCoupons(couponDetailDataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getDeliveryCouponSheet() {
        return new AbstractXlsxView(){
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "DeliveryCouponSheet.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<DeliveryCouponDetailData> all = new ArrayList<DeliveryCouponDetailData>();
                writer.writeSheet(all, DeliveryCouponDetailData.class);
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateAllocationOfCoupon(DeliveryCouponDetailData deliveryCoupon, Integer customerId, String contactNumber, Integer campaignId, int orderId) {
        LOG.info("Update coupon detail after allocation for Coupon id : {}",deliveryCoupon.getDeliveryCouponId());
        DeliveryCouponDetailData couponDetailData = offerDao.find(DeliveryCouponDetailData.class, deliveryCoupon.getDeliveryCouponId());
        Date allotmentTime = AppUtils.getCurrentTimestamp();
        couponDetailData.setNoOfAllocations(couponDetailData.getNoOfAllocations()+1);
        couponDetailData.setLastAllocationTime(allotmentTime);
        couponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.AVAILABLE.name());
        if(couponDetailData.getNoOfAllocations().equals(couponDetailData.getMaxNoOfDistributions())){
            couponDetailData.setIsExhausted(AppConstants.getValue(true));
            couponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.ALLOTTED.name());
        }
        DeliveryCouponAllocationDetailData allocationData = new DeliveryCouponAllocationDetailData();
        allocationData.setDeliveryCouponId(couponDetailData.getDeliveryCouponId());
        allocationData.setCustomerId(customerId);
        allocationData.setAllotmentTime(allotmentTime);
        allocationData.setCampaignId(campaignId);
        allocationData.setContactNumber(contactNumber);
        allocationData.setAllotmentOrderId(orderId);
        offerDao.add(allocationData);
        offerDao.update(couponDetailData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Long validateDeliveryCoupon(String masterCoupon) {
        Long validCouponCount = offerDao.findValidDeliveryCouponCount(masterCoupon, AppUtils.getCurrentTimestamp());
        if (Objects.nonNull(validCouponCount) && validCouponCount > 0) {
            return validCouponCount;
        }
        return 0L;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CampaignDetail> getActiveCampaignListByValidDate() {
        return offerDao.getActiveCampaignListByValidDate();
    }

    @Override
    public List<Integer> getLinkedCampaignIds(Integer campaignId) {
        return offerDao.getLinkedCampaignIds(campaignId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CouponDetail getAutoApplicableOfferForUnit(Integer unitId) {
        return offerDao.getAutoApplicableOfferForUnit(unitId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CouponDetail addCouponDetailData(String code, String newCode){
        CouponDetail detail = getModalCoupon(code);
        detail.setCode(newCode);
        return offerDao.addCoupon(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OfferResponse> getWinbackOffers(){
        try {
            List<Integer> categoriesIds = offerDao.getOfferAccountCatorybyBudgetCategory(Collections.singletonList(BudgetCategoryConstants.WINBACK));
            List<OfferResponse> responses = new ArrayList<>();
            if (!CollectionUtils.isEmpty(categoriesIds)) {
                List<OfferDetailData> offerDetailData = offerDao.getOfferDetailDataByAccountCategory(categoriesIds);
                if(!CollectionUtils.isEmpty(offerDetailData)) {
                    for (OfferDetailData data : offerDetailData) {
                        OfferResponse offerResponse = new OfferResponse();
                        offerResponse.setOfferId(data.getOfferDetailId());
                        offerResponse.setOfferDescription(data.getOfferDescription());
                        responses.add(offerResponse);
                    }
                    return responses;
                }
            }
        }catch (Exception e){
            LOG.info("Error in Fetching Dinein Offer for winBack customer and error is : {}",e);
        }
        return null;
    }

    @Override
    public List<DeliveryOfferDetailData> getWinbackOfferForDelivery(){
        try {
            List<DeliveryOfferDetailData> deliveryOfferDetailData = offerDao.getDeliveryOfferDetailDataForCategory(BudgetCategoryConstants.WINBACK);
            if(Objects.nonNull(deliveryOfferDetailData)){
                return deliveryOfferDetailData;
            }
        }catch (Exception e){
            LOG.info("Error in Fetching delivery Offer for winBack customer and Error is : {}",e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerWinbackOfferInfo generateWinbackCoupon(CustomerWinbackOfferInfoDomain domain,Integer customerId){
        if(domain.getOrderSource().equals(AppConstants.DINE_IN)){
            return generateDineInWinbackCoupon(domain,customerId);
        } else if (domain.getOrderSource().equals(AppConstants.DELIVERY)) {
            return generateDeliveryWinbackCoupon(domain,customerId);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private CustomerWinbackOfferInfo generateDineInWinbackCoupon(CustomerWinbackOfferInfoDomain domain,Integer customerId){
        try{
            CouponCloneResponse couponCloneResponse = null;
            CouponDetailData clone = offerDao.getCouponDetailByOfferId(domain.getOfferId());
            CloneCouponData cloneCoupon = getCloneCouponData(domain.getValidityInDays(), "THK", clone);
            List<String> contactNumbers = new ArrayList<>();
            contactNumbers.add(domain.getContactNumber());
            String startDay = AppUtils.getDateString(AppUtils.getCurrentDate(), AppConstants.DATE_FORMAT);
            try {
                couponCloneResponse = createCoupon(cloneCoupon, contactNumbers, startDay, null, null, clone);
                if(Objects.nonNull(couponCloneResponse) && CollectionUtils.isEmpty(couponCloneResponse.getErrors())){
                    try {
                        return saveCustomerWinbackOfferInfoForDineIN(domain,couponCloneResponse,customerId);
                    }catch (Exception e){
                        LOG.info("Error in saving dineIn winback data for customer with contact number : {} and Error is : {}",domain.getContactNumber(),e);
                    }
                }
                return null;
            } catch (DataUpdationException e) {
                LOG.info("Coupon can not be Created for Customer with contact number : {} and error is : {}",
                        domain.getContactNumber(),e);
            }
        }catch (Exception e){
            LOG.info("Error in generating DineIn coupon for customer with contact Number : {} and error is : {}",
                    domain.getContactNumber(),e);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private CustomerWinbackOfferInfo generateDeliveryWinbackCoupon(CustomerWinbackOfferInfoDomain domain,Integer customerId){
        try {
            DeliveryCouponDetailData deliveryCoupon = getDeliveryCloneCode(domain.getOfferCode(), 1, domain.getChannelPartnerId(), false);
            if (Objects.nonNull(deliveryCoupon)) {
                Integer couponDelay = 0;
                Date startDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(), couponDelay - 1));
                if (AppUtils.isBefore(startDate, deliveryCoupon.getStartDate())) {
                    startDate = deliveryCoupon.getStartDate();
                }
                Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate, domain.getValidityInDays() - 1);
                if (AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)) {
                    endDate = deliveryCoupon.getEndDate();
                }
                updateAllocationOfCoupon(deliveryCoupon, customerId, domain.getContactNumber(), -1, -1,
                        startDate, endDate);
                try {
                    return saveCustomerWinbackOfferInfoForDelivery(domain, deliveryCoupon,customerId);
                } catch (Exception e) {
                    LOG.info("Error in saving delivery winback data for customer with contact number : {} and Error is : {}", domain.getContactNumber(),e);
                }
            }
        }catch (Exception e){
            LOG.info("Error in generating Delivery coupon for customer with contact Number : {} and error is : {}",
                    domain.getContactNumber(),e);
        }
        return null;
    }

    private CloneCouponData getCloneCouponData(int validityInDays, String prefix,
                                               CouponDetailData clone) {
        CloneCouponData data = new CloneCouponData();
        data.setCloneCouponCode(clone.getCouponCode());
        data.setOfferDetailId(clone.getOfferDetail().getOfferDetailId());
        data.setPrefix(prefix);
        data.setUsageCount(1);
        data.setMaxCustomerUsage(clone.getMaxCustomerUsage());
        data.setValidityInDays(validityInDays);
        data.setApplicableRegion(null);
        data.setApplicableUnitId(null);
        return data;
    }

    private DeliveryCouponDetailData getDeliveryCloneCode(String code, Integer brandId,Integer channelPartnerId ,Boolean getClonedCoupon){
        DeliveryCouponDetailData data;
        if(getClonedCoupon){
            data = offerDao.getDeliveryCoupon(code, brandId,channelPartnerId);
            if(Objects.nonNull(data)){
                data.setDeliveryCouponStatus(DeliveryCouponStatus.PROCESSING.name());
                offerDao.update(data);
            }
        }else{
            data = offerDao.getDeliveryMasterCoupon(code, brandId,channelPartnerId);
        }
        return data;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private CustomerWinbackOfferInfo saveCustomerWinbackOfferInfoForDineIN(CustomerWinbackOfferInfoDomain domain,CouponCloneResponse couponCloneResponse,Integer customerId){
        try {
            CustomerWinbackOfferInfo data = new CustomerWinbackOfferInfo();
            data.setCustomerId(customerId);
            data.setContactNumber(domain.getContactNumber());
            data.setCustomerName(domain.getCustomerName());
            data.setOfferId(domain.getOfferId());
            data.setOfferDescription(domain.getOfferDescription());
            data.setCouponCode(couponCloneResponse.getMappings().get(domain.getContactNumber()).getCoupon());
            data.setCouponDetailId(couponCloneResponse.getMappings().get(domain.getContactNumber()).getCouponDetailId());
            data.setStartDate(couponCloneResponse.getMappings().get(domain.getContactNumber()).getStartDate());
            data.setEndDate(couponCloneResponse.getMappings().get(domain.getContactNumber()).getEndDate());
            data.setValidityInDays(domain.getValidityInDays());
            data.setCompansationReason(domain.getCompensationReason());
            data.setComment(domain.getComment());
            data.setOrderId(domain.getOrderId());
            data.setOrderSource(domain.getOrderSource());
            data.setComplainSource(domain.getComplainSource());
            data.setIsNotified(AppConstants.NO);
            data.setUpdatedBy(domain.getUpdatedBy());
            data.setUpdatedAt(AppUtils.getCurrentTimestamp());
            return offerDao.add(data);
        }catch (Exception e){
            LOG.info("Error in saving Customer Winback DineIn Offer info for customer with contact number : {}",domain.getContactNumber());
        }
        return null;
    }

    private CustomerWinbackOfferInfo saveCustomerWinbackOfferInfoForDelivery(CustomerWinbackOfferInfoDomain domain,DeliveryCouponDetailData deliveryCouponDetailData,Integer customerId){
        try {
            CustomerWinbackOfferInfo data = new CustomerWinbackOfferInfo();
            data.setCustomerId(customerId);
            data.setContactNumber(domain.getContactNumber());
            data.setCustomerName(domain.getCustomerName());
            data.setOfferId(domain.getOfferId());
            data.setOfferDescription(domain.getOfferDescription());
            data.setCouponCode(deliveryCouponDetailData.getCouponCode());
            data.setCouponDetailId(deliveryCouponDetailData.getDeliveryCouponId());
            data.setStartDate(AppUtils.getDateString(deliveryCouponDetailData.getStartDate()));
            data.setEndDate(AppUtils.getDateString(deliveryCouponDetailData.getEndDate()));
            data.setValidityInDays(domain.getValidityInDays());
            data.setCompansationReason(domain.getCompensationReason());
            data.setComment(domain.getComment());
            data.setOrderId(domain.getOrderId());
            data.setOrderSource(domain.getOrderSource());
            data.setIsNotified(AppConstants.NO);
            data.setComplainSource(domain.getComplainSource());
            if(domain.getChannelPartnerId()==3){
                data.setChannelPartner(AppConstants.ZOMATO);
            }
            if(domain.getChannelPartnerId()==6){
                data.setChannelPartner(AppConstants.SWIGGY);
            }
            data.setUpdatedBy(domain.getUpdatedBy());
            data.setUpdatedAt(AppUtils.getCurrentTimestamp());
            return offerDao.add(data);
        }catch (Exception e){
            LOG.info("Error in saving Customer Winback Delivery Offer info for customer with contact number : {}",domain.getContactNumber());
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private void updateAllocationOfCoupon(DeliveryCouponDetailData deliveryCoupon, Integer customerId, String contactNumber, Integer campaignId, Integer orderId,Date startDate,Date endDate) {
        LOG.info("Update coupon detail after allocation for Coupon id : {}",deliveryCoupon.getDeliveryCouponId());
        DeliveryCouponDetailData couponDetailData = offerDao.find(DeliveryCouponDetailData.class, deliveryCoupon.getDeliveryCouponId());
        Date allotmentTime = AppUtils.getCurrentTimestamp();
        couponDetailData.setNoOfAllocations(couponDetailData.getNoOfAllocations()+1);
        couponDetailData.setLastAllocationTime(allotmentTime);
        couponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.AVAILABLE.name());
        if(couponDetailData.getNoOfAllocations().equals(couponDetailData.getMaxNoOfDistributions())){
            couponDetailData.setIsExhausted(AppConstants.getValue(true));
            couponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.ALLOTTED.name());
        }
        DeliveryCouponAllocationDetailData allocationData = new DeliveryCouponAllocationDetailData();
        allocationData.setDeliveryCouponId(couponDetailData.getDeliveryCouponId());
        allocationData.setCustomerId(customerId);
        allocationData.setAllotmentTime(allotmentTime);
        allocationData.setCampaignId(campaignId);
        allocationData.setContactNumber(contactNumber);
        allocationData.setAllotmentOrderId(orderId);
        allocationData.setStartDate(startDate);
        allocationData.setEndDate(endDate);
        offerDao.add(allocationData);
        offerDao.update(couponDetailData);
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerWinbackOfferInfo markNotified(Integer id){
        try {
            CustomerWinbackOfferInfo data = offerDao.getCustomerWinbackOfferInfoById(id);
            if(Objects.nonNull(data)){
                data.setIsNotified(AppConstants.YES);
                return offerDao.update(data);
            }
        }catch (Exception e){
            LOG.info("Error in Marking info Notified for id : {} and Error is : {}",id,e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CustomerWinbackOfferInfo> getWinbackInfo(){
        try {
            return offerDao.getWinbackInfo();
        }catch (Exception e) {
            LOG.error("Error in fetching customer Winback Info",e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CustomerWinbackOfferInfo> getWinbackInfo(Date startDate,Date endDate){
        try {
            return offerDao.getWinbackInfo(startDate,endDate);
        }catch (Exception e) {
            LOG.error("Error in fetching customer Winback Info",e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getWinbackSheet(List<CustomerWinbackOfferInfo> data){
        try {
            if(!CollectionUtils.isEmpty(data)) {
                return new AbstractXlsxView() {
                    @Override
                    protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                                      HttpServletResponse response) throws Exception {
                        String fileName = "WinbackCouponData.xlsx";
                        response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                        ExcelWriter writer = new ExcelWriter(workbook);
                        List<CustomerWinbackOfferInfo> all = new ArrayList<CustomerWinbackOfferInfo>(data);
                        System.out.println(all);
                        for(CustomerWinbackOfferInfo d : all){
                            System.out.println(d);
                            System.out.println(d.toString());
                        }
                        writer.writeSheet(all, CustomerWinbackOfferInfo.class);
                    }
                };
            }
        }catch (Exception e){
            LOG.info("Error in Downloading Winback sheet with error : {}",e);
        }
        return null;
    }

    public List<CouponDetail> filterCoupons(List<CouponDetail> couponDetails, Integer unitId,boolean filterActive) {
        try{
            List<CouponDetail> toFilter = new ArrayList<>();
            for (CouponDetail detail : couponDetails) {
                if (Objects.nonNull(detail.getMappings()) && (Objects.nonNull(detail.getMappings().get("UNIT")) || Objects.nonNull(detail.getMappings().get("UNIT_REGION")))) {
                    AtomicBoolean isPresent = new AtomicBoolean(false);
                    if (Objects.nonNull(detail.getMappings().get("UNIT"))) {
                        detail.getMappings().get("UNIT").forEach(val -> {
                            if (val.getValue().equals(unitId.toString())) {
                                isPresent.set(true);
                            }
                        });
                    }
                    if (Objects.nonNull(detail.getMappings().get("UNIT_REGION"))) {
                        detail.getMappings().get("UNIT_REGION").forEach(val -> {
                            if (val.getValue().equals(masterDataCache.getUnitBasicDetail(unitId).getRegion())) {
                                isPresent.set(true);
                            }
                        });
                    }
                    if (!isPresent.get()) {
                        toFilter.add(detail);
                    }
                }
            }
            couponDetails.removeAll(toFilter);
            getOnlyActiveMappings(couponDetails,filterActive);
        }catch (Exception e){
            LOG.info("Error ::::::::::: {}",e);
        }
        return couponDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String,List<CouponDetail>> getAllLoyaltyBurnOffers(List<String> burnOfferCategoryType,Integer unitId){
        try {
            List<Integer> categoriesIds = offerDao.getOfferAccountCatorybyBudgetCategory(burnOfferCategoryType);
            if(!CollectionUtils.isEmpty(categoriesIds)){
                Map<String,List<CouponDetail>> details = offerDao.getCouponDetailByAccountCategory(categoriesIds,true);
                if(!CollectionUtils.isEmpty(details)){
                    details.forEach((s, couponDetails) -> {
                        details.put(s,filterCoupons(couponDetails,unitId,true));
                    });
                    return details;
                }
            }
            return null;
        }catch (Exception e) {
            LOG.error("Error while getting loyalty burn offers",e);
        }
        return null;
    }

    public void getOnlyActiveMappings(List<CouponDetail> couponDetails, boolean filterActive) {
        if (!CollectionUtils.isEmpty(couponDetails) && filterActive) {
            for (CouponDetail couponDetail : couponDetails) {
                if (Objects.nonNull(couponDetail.getOffer().getCouponMappingList())) {
                    List<CouponMapping> couponMappingList = couponDetail.getCouponMappingList().stream().filter(couponMapping ->
                            AppConstants.ACTIVE.equals(couponMapping.getStatus())).collect(Collectors.toList());
                    couponDetail.setCouponMappingList(couponMappingList);
                }
            }
        }
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public CouponCloneResponse generateCoupon(int validityDays, String contactNumber, String cloneCode, String prefix) {
        CouponCloneResponse couponCloneResponse = null;
        CouponDetailData clone = offerDao.getCoupon(cloneCode);
        CloneCouponData cloneCoupon = getCloneCouponData(validityDays, prefix, clone);
        List<String> contactNumbers = new ArrayList<>();
        contactNumbers.add(contactNumber);
        String startDay = AppUtils.getDateString(AppUtils.getCurrentDate(), AppConstants.DATE_FORMAT);
        try {
            couponCloneResponse = createCoupon(cloneCoupon, contactNumbers, startDay, null, null, clone);
            return couponCloneResponse;
        } catch (DataUpdationException e) {
            LOG.info("Coupon can not be Created for Customer with contact number : {}",contactNumber);
        }
        return null;
    }

    @Override
    public Map<Integer,List<OfferDescriptionMetadata>> getOfferDescriptionMetadata(Boolean getAll, Integer offerId){
        Map<Integer, List<OfferDescriptionMetadata>> descriptionMetadataMap;
        if (getAll) {
            descriptionMetadataMap = descriptionMetadataDao.findAll()
                    .stream()
                    .collect(Collectors.groupingBy(OfferDescriptionMetadata::getOfferId));
        } else {
            descriptionMetadataMap = new HashMap<>();
            Set<Integer> offerIds = new HashSet<>();
            offerIds.add(offerId);
            if (Objects.nonNull(offerId)) {
                Optional.ofNullable(descriptionMetadataDao.findByOfferIdInAndStatus(offerIds,AppConstants.ACTIVE))
                        .ifPresent(data -> descriptionMetadataMap
                                .computeIfAbsent(offerId, k -> new ArrayList<>())
                                .addAll(data));
            }
        }
        return descriptionMetadataMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean updateOfferDescriptionMetadata(Map<Integer,List<OfferDescriptionMetadata>> offerDescriptionData){
        try {
            if(Objects.nonNull(offerDescriptionData)){
                List<OfferDescriptionMetadata> metadataList = new ArrayList<>();
                descriptionMetadataDao.setOfferIdsMappingStatus(offerDescriptionData.keySet(),AppConstants.IN_ACTIVE);
                for(Map.Entry<Integer,List<OfferDescriptionMetadata>> map  : offerDescriptionData.entrySet()){
                    metadataList.addAll(map.getValue());
                }
                descriptionMetadataDao.saveAll(metadataList);
            }
            return true;
        }catch (Exception e){
            LOG.info("Error while updating offer description metadata::::::: {} ",e);
        }
        return false;
    }

    @Override
    public OfferDayTimeDto getOfferDayTimeScheduleDetails(Integer offerId) {
        OfferDayTimeDto offerDayTimeDto = new OfferDayTimeDto();
        List<String> mappingTypes = List.of(OfferMetaDataType.DAYS.name(), OfferMetaDataType.TIME_RANGE.name());
        Map<String, Map<String, OfferMetadata>> offerMetadataMap = getOfferMetaDataMap(mappingTypes, offerId, AppConstants.ACTIVE);
        Map<String, OfferMetadata> dayTypeMap = offerMetadataMap.getOrDefault(OfferMetaDataType.DAYS.name(), new HashMap<>());
        Map<String, OfferMetadata> timeTypeMap = offerMetadataMap.getOrDefault(OfferMetaDataType.TIME_RANGE.name(), new HashMap<>());
        offerDayTimeDto.setOfferDayDetails(getOfferDayScheduleDetails(dayTypeMap));
        offerDayTimeDto.setOfferTimeDetails(getOfferTimeDetails(timeTypeMap));
        return offerDayTimeDto;
    }

    @Override
    public CouponDetail getCouponDetailForUnit(Integer unitId, String couponCode) {
        try {
            CouponDetail couponDetail = searchCoupon(couponCode, true);
            if (Objects.nonNull(couponDetail) && Objects.nonNull(couponDetail.getMappings()) &&
                    Objects.nonNull(couponDetail.getMappings().get("UNIT"))) {

                AtomicBoolean isPresent = new AtomicBoolean(false);
                couponDetail.getMappings().get("UNIT").forEach(val -> {
                    if (val.getValue().equals(unitId.toString())) {
                        isPresent.set(true);
                    }
                });

                if (isPresent.get()) {
                    return couponDetail;
                } else {
                    // Return empty coupon detail if unit is not present in mappings
                    return new CouponDetail();
                }
            }
            return couponDetail;
        } catch (Exception e) {
            LOG.error("Error while fetching coupon detail for unit: {} and coupon code: {}", unitId, couponCode, e);
        }
        return null;
    }
}
