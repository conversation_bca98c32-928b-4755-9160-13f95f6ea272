/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Stopwatch;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.service.TaxDataCacheService;
import com.stpl.tech.master.core.service.TaxManagementService;
import com.stpl.tech.master.tax.model.CategoryAdditionalTax;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.TaxCategory;

@Service
public class TaxDataCacheServiceImpl implements TaxDataCacheService {
	Logger LOG = LoggerFactory.getLogger(TaxDataCacheServiceImpl.class);

	@Autowired
	private TaxDataCache cache;

	@Autowired
	private TaxManagementService manager;

	public TaxDataCacheServiceImpl() {
	}

	@Override
	public void loadCache() {
    	LOG.info("POST-CONSTRUCT TaxDataCacheServiceImpl - STARTED");
		Stopwatch watch = Stopwatch.createUnstarted();
		cache.clearCache();
		cache.createCache();
		watch.start();
		refreshTaxCategoryMap();
		System.out.println("########## , refreshTaxCategoryMap," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshHsnStateTaxDataMap();
		System.out.println("########## , refreshHsnStateTaxDataMap," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshCategoryTaxMap();
		System.out.println("########## , refreshCategoryTaxMap," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
	}

	@Override
	public void refreshTaxCategoryMap() {
		cache.clearTaxCategoryMapCache();
		for (TaxCategory category : manager.getAllTaxCategories()) {
			cache.addTaxCategoryToCache(category);
		}
	}

	@Override
	public void refreshHsnStateTaxDataMap() {
		cache.clearHsnStateTaxDataMapCache();
		Map<String, CategoryAdditionalTax> additionalMap = manager.fetchAllCategoryAdditionalTax();
		List<CategoryTax> categories = manager.fetchAllCategoryTax();
		cache.updateTaxCache(categories, additionalMap);
	}

	@Override
	public void refreshCategoryTaxMap() {
		if(!cache.getAllCategoryTax().isEmpty()) {
			cache.clearCategoryTaxMapCache();
		}
		for (CategoryTax category : manager.fetchAllCategoryTax()) {
			cache.addCategoryTaxToCache(category);
		}
	}

	@Override
	public void updateTaxCache(List<CategoryTax> categories, Map<String, CategoryAdditionalTax> additionalMap) {
		cache.updateTaxCache(categories, additionalMap);
	}

	@Override
	public void addTaxCategoryToCache(TaxCategory taxCategory) {
		cache.addTaxCategoryToCache(taxCategory);
	}
}
