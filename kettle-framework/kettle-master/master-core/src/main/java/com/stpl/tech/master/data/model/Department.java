/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Department generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "DEPARTMENT")
public class Department implements java.io.Serializable {

	private Integer deptId;
	private BusinessDivision businessDivision;
	private String deptName;
	private String deptDesc;
	private Set<EmployeeDetail> employeeDetails = new HashSet<EmployeeDetail>(0);
	private Set<Designation> designations = new HashSet<Designation>(0);

	public Department() {
	}

	public Department(BusinessDivision businessDivision, String deptName, String deptDesc) {
		this.businessDivision = businessDivision;
		this.deptName = deptName;
		this.deptDesc = deptDesc;
	}

	public Department(BusinessDivision businessDivision, String deptName, String deptDesc,
			Set<EmployeeDetail> employeeDetails, Set<Designation> designations) {
		this.businessDivision = businessDivision;
		this.deptName = deptName;
		this.deptDesc = deptDesc;
		this.employeeDetails = employeeDetails;
		this.designations = designations;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "DEPT_ID", unique = true, nullable = false)
	public Integer getDeptId() {
		return this.deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "BUSINESS_DIV_ID", nullable = false)
	public BusinessDivision getBusinessDivision() {
		return this.businessDivision;
	}

	public void setBusinessDivision(BusinessDivision businessDivision) {
		this.businessDivision = businessDivision;
	}

	@Column(name = "DEPT_NAME", nullable = false)
	public String getDeptName() {
		return this.deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	@Column(name = "DEPT_DESC", nullable = false, length = 500)
	public String getDeptDesc() {
		return this.deptDesc;
	}

	public void setDeptDesc(String deptDesc) {
		this.deptDesc = deptDesc;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "department")
	public Set<EmployeeDetail> getEmployeeDetails() {
		return this.employeeDetails;
	}

	public void setEmployeeDetails(Set<EmployeeDetail> employeeDetails) {
		this.employeeDetails = employeeDetails;
	}

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "DEPARTMENT_DESIGNATION_MAPPING", joinColumns = {
			@JoinColumn(name = "DEPT_ID", nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = "DESIGNATION_ID", nullable = false, updatable = false) })
	public Set<Designation> getDesignations() {
		return this.designations;
	}

	public void setDesignations(Set<Designation> designations) {
		this.designations = designations;
	}

}
