package com.stpl.tech.master.core.apps.service.impl;

import com.stpl.tech.master.core.apps.dao.AppManagementDao;
import com.stpl.tech.master.core.apps.dao.ArduinoBuildDao;
import com.stpl.tech.master.core.apps.service.AppsManagementService;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.SwitchStatus;
import com.stpl.tech.master.monk.configuration.model.AppBuildData;
import com.stpl.tech.master.monk.configuration.model.AppBuildVersionData;
import com.stpl.tech.master.monk.configuration.model.AppsVersionMetadata;
import com.stpl.tech.master.monk.configuration.model.ArduinoBuildData;
import com.stpl.tech.master.monk.configuration.model.ArduinoBuildForUnit;
import com.stpl.tech.master.monk.configuration.model.UnitArduinoBuild;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-05-2018.
 */

@Service
public class AppsManagementServiceImpl implements AppsManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(AppsManagementServiceImpl.class);

    @Autowired
    private AppManagementDao dao;

    @Autowired
    private ArduinoBuildDao buildDao;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Override
    public AppsVersionMetadata findLatestVersion() {
        return dao.findFirstByStatusEquals(SwitchStatus.ACTIVE.value());
    }

    @Override
    public List<AppsVersionMetadata> findAllActiveVersions() {
        return dao.findAllByStatusEquals(SwitchStatus.ACTIVE.value());
    }

    @Override
    public AppsVersionMetadata uploadNewBuild(AppsVersionMetadata version) {
        LOG.info("Received app build upload request {}", version.getVersion());
        List<AppsVersionMetadata> allActiveVersions = findAllActiveVersions();
        allActiveVersions.forEach(appsVersionMetadata -> {
            appsVersionMetadata.setStatus(SwitchStatus.IN_ACTIVE);
            dao.save(appsVersionMetadata);
        });
        LOG.info("Marked {} builds as IN_ACTIVE", allActiveVersions.size());
        int versionNumber = version.getVersion();
        versionNumber = (versionNumber == 0) ? AppConstants.APP_BUILD_SEED_VERSION : versionNumber;
        version.setVersion(versionNumber);
        version.setName(getVersionName(version));
        version.setUploadDate(AppUtils.getCurrentTimestamp());
        version.setStatus(SwitchStatus.ACTIVE);
        version = dao.save(version);
        LOG.info("Id of version {} uploaded {}", version.getName(), version.getId());
        return version;
    }

    private String getVersionName(AppsVersionMetadata version) {
        StringBuffer  versionName = new StringBuffer("v");
        versionName.append(version.getMajor())
                .append(".")
                .append(version.getMinor())
                .append(".")
                .append(version.getPatch());
        return versionName.toString();
    }

    @Override
    public AppsVersionMetadata activateVersion(AppsVersionMetadata version) {
        return dao.save(version);
    }

    @Override
    public AppBuildVersionData checkAndReturnVersion(AppBuildVersionData apkVersion) {
        AppsVersionMetadata latest = findLatestVersion();
        if(latest != null){
            Map<String, AppBuildData> apps = latest.getApps().stream()
                    .collect(Collectors.toMap(AppBuildData::getName, Function.identity()));
            if (apps.keySet().contains(apkVersion.getName())){
                AppBuildData buildData = apps.get(apkVersion.getName());
                for(IdCodeName unit : buildData.getUnits()){
                    if(unit.getId() == apkVersion.getUnitId()){
                        Integer versionOnUnit = apkVersion.getVersion();
                        if(apkVersion.getVersion() != latest.getVersion()){
                            // if the unit has not updated to the latest version till now
                            apkVersion.setVersion(latest.getVersion());
                            IdCodeName details = apps.get(apkVersion.getName()).getUploadDetails();
                            URL apkUrl = fileArchiveService.getSignedUrl(details.getName(), details.getCode());
                            apkVersion.setUrl(apkUrl.toString());
                        }
                        unit.setCode(apkVersion.getAppName());
                        unit.setType(versionOnUnit.toString());
                        dao.save(latest);
                        break;
                    }
                }
            }
        }
        return apkVersion;
    }

    @Override
    public ArduinoBuildData initiateBuild(Integer userId){
        ArduinoBuildData buildData = new ArduinoBuildData();
        buildData.setStatus("INITIATED");
        buildData.setUploadedBy(userId);
        buildData.setVersion(AppUtils.getCurrentTimeISTStringWithNoColons());
        buildData.setInitiationTime(AppUtils.getCurrentTimestamp());
        buildData = buildDao.save(buildData);
        return buildData;
    }

    @Override
    public ArduinoBuildData uploadArduinoBuild(MultipartFile file, Integer userId, String monk,
                                               String bucket, Integer unitId, String versionName)
            throws DataNotFoundException {
        LOG.info(":::::: Request to upload build for arduino ::::::");
        ArduinoBuildData buildData  = getInitiatedArduinoBuild(versionName);
        if(buildData==null){
            throw new DataNotFoundException("No Initiated arduino build found");
        }
        String baseDir = unitId + File.separator + monk + File.separator + versionName;
        String fileName = "ARDUINO_BUILD_"+ versionName + ".hex";
        FileDetail detail = fileArchiveService.saveFileToS3(bucket, baseDir, fileName, file, true);
        if(detail!=null){
            UnitArduinoBuild monkBuild = new UnitArduinoBuild();
            monkBuild.setUnitId(unitId);
            monkBuild.setBucket(detail.getBucket());
            monkBuild.setKey(detail.getKey());
            monkBuild.setMonk(monk);
            monkBuild.setUploadTime(AppUtils.getCurrentTimestamp());
            buildData.getBuilds().add(monkBuild);
        }
        buildData = buildDao.save(buildData);
        return buildData;
    }

    @Override
    public ArduinoBuildData getInitiatedArduinoBuild(String versionName) throws DataNotFoundException {
        ArduinoBuildData buildData = getBuildByStatus("INITIATED");
        if (!buildData.getVersion().equals(versionName)){
            throw new DataNotFoundException("No initiated build found with the version provided "+  versionName);
        }
        return buildData;
    }

    @Override
    public ArduinoBuildData getInitiatedArduinoBuild() throws DataNotFoundException {
        return getBuildByStatus("INITIATED");
    }

    @Override
    public boolean activateArduinoBuild(ArduinoBuildData buildData) {
        LOG.info("Call to activate build for arduino, {}", buildData.getVersion());
        boolean flag = true;
        try {
            List<ArduinoBuildData> activeBuilds = buildDao.findAllByStatusEquals(SwitchStatus.ACTIVE.name());
            if(activeBuilds!=null && !activeBuilds.isEmpty()){
                for (ArduinoBuildData build : activeBuilds) {
                    build.setStatus(SwitchStatus.IN_ACTIVE.name());
                    build.setDeactivationTime(AppUtils.getCurrentTimestamp());
                    buildDao.save(build);
                }
            }
            buildData.setStatus(SwitchStatus.ACTIVE.name());
            buildData.setActivationTime(AppUtils.getCurrentTimestamp());
            buildDao.save(buildData);
        }catch (Exception e){
            LOG.error("Error while activating arduino build",  e);
            flag = false;
        }
        return flag;
    }

    @Override
    public ArduinoBuildForUnit getActiveArduinoBuild(Integer unit) throws DataNotFoundException {
        LOG.info(":::::: Request to get active arduino build ::::::");
        ArduinoBuildData buildData = getBuildByStatus(SwitchStatus.ACTIVE.name());
        ArduinoBuildForUnit unitBuild = new ArduinoBuildForUnit();
        unitBuild.setUnitId(unit);
        List<UnitArduinoBuild> builds = buildData.getBuilds().stream()
                .filter(unitArduinoBuild -> unitArduinoBuild.getUnitId().equals(unit))
                .collect(Collectors.toList());
        Map<String,String> buildList = new HashMap<>();
        for(UnitArduinoBuild build : builds){
            String url = String.valueOf(fileArchiveService.getSignedUrl(build.getBucket(), build.getKey()));
            buildList.put(build.getMonk(), url);
        }
        unitBuild.setBuilds(buildList);
        return unitBuild;
    }

    @Override
    public ArduinoBuildData getActiveArduinoBuild() throws DataNotFoundException {
        return getBuildByStatus(SwitchStatus.ACTIVE.name());
    }

    private ArduinoBuildData getBuildByStatus(String status) throws DataNotFoundException {
        ArduinoBuildData buildData = buildDao.findFirstByStatusEquals(status);
        if(buildData==null){
            throw new DataNotFoundException("No arduino build found with status "+ status);
        }
        return buildData;
    }

}
