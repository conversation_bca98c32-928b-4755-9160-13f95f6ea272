package com.stpl.tech.master.core.apps.dao;

import java.util.List;

import com.stpl.tech.master.monk.configuration.model.AppsVersionMetadata;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-05-2018.
 */

@Repository
public interface AppManagementDao  extends MongoRepository<AppsVersionMetadata, String> {

    public AppsVersionMetadata findFirstByStatusEquals(String status);

    public List<AppsVersionMetadata> findAllByStatusEquals(String status);

}
