    package com.stpl.tech.master.data.model;
    
    import com.fasterxml.jackson.annotation.JsonIdentityInfo;
    import com.fasterxml.jackson.annotation.JsonIgnore;
    import com.fasterxml.jackson.annotation.ObjectIdGenerators;
    import lombok.Getter;
    import lombok.Setter;
    
    import javax.persistence.CascadeType;
    import javax.persistence.Column;
    import javax.persistence.Entity;
    import javax.persistence.FetchType;
    import javax.persistence.GeneratedValue;
    import javax.persistence.GenerationType;
    import javax.persistence.Id;
    import javax.persistence.JoinColumn;
    import javax.persistence.OneToMany;
    import javax.persistence.OneToOne;
    import javax.persistence.Table;
    import java.util.Date;
    import java.util.List;
    
    @Getter
    @Setter
    @Entity
    @Table(name = "MONK_X_TWO_METADATA")
    @JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "id")
    public class MonkXTwoMetadata {

        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private Integer id;

        @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
        @JoinColumn(name = "unit_id", referencedColumnName = "UNIT_ID")
        @JsonIgnore
        private UnitDetail unitDetail;

        @Column(name = "NO_OF_X_TWO_MACHINES")
        private Integer noOfX2Machines;

        @Column(name = "VESSEL_SENSE_DELAY")
        private Integer vesselSenseDelay;

        @Column(name = "SPICE_SENSE_DELAY")
        private Integer spiceSenseDelay;
        
        @Column(name = "LAST_UPDATED_TIMESTAMP")
        private Date lastUpdatedTimestamp;
        
        @OneToMany(mappedBy = "monkMetadata", cascade = CascadeType.ALL)
        private List<MonkXTwoUrls> monkUrls;
    }
