package com.stpl.tech.master.data.dao;

import java.util.List;

import com.stpl.tech.master.data.model.AccessControlListData;
import com.stpl.tech.master.data.model.UserRoleData;
import com.stpl.tech.master.domain.model.ApplicationName;

/**
 * Created by <PERSON><PERSON> on 08-07-2016.
 */
public interface AccessControlDao extends AbstractMasterDao {

    public List<AccessControlListData> getAccessControls(ApplicationName appName);

    List<UserRoleData> getUserRoleDataByAppName(ApplicationName appName);
}
