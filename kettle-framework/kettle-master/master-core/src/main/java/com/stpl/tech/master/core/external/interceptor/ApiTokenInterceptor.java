package com.stpl.tech.master.core.external.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import com.stpl.tech.master.core.exception.InvalidRequestException;
import com.stpl.tech.master.core.external.cache.ApiTokenCache;
import com.stpl.tech.util.ACLUtil;

@Component
public class ApiTokenInterceptor implements AsyncHandlerInterceptor {

    @Autowired
    private ApiTokenCache apiTokenCache;

    @Autowired
    private Environment env;

    @Scheduled(fixedRate = 7200000)
    public void clearAclURICache() {
        ACLUtil.getInstance().clearUriCache("SessionAuthInterceptor");
    }

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o)
        throws Exception {
        if (Boolean.TRUE.equals(Boolean.valueOf(env.getProperty("run.apiTokenInterceptor", "false")))) {
            String module = ACLUtil.getInstance().convertURIToModule(httpServletRequest.getRequestURI());
            String token = httpServletRequest.getHeader("apiToken");
            String method = httpServletRequest.getMethod();
            if (apiTokenCache.isValidRequest(module, token, method)) {
                return true;
            }
        } else {
            return true;
        }
        throw new InvalidRequestException("Invalid request: Request token is not valid");
    }
}
