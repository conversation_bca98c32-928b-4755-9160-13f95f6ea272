/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.EntityAliasKey;

import java.util.List;

public interface EntityAliasManagementService {

    public List<EntityAliasMappingData> getAllEntityAlias();

    public EntityAliasMappingData addEntityAlias(EntityAliasMappingData entityAliasMappingData);

    public EntityAliasMappingData updateEntityAlias(EntityAliasMappingData entityAliasMappingData);

    public List<EntityAliasMappingData> addAllEntityAlias(List<EntityAliasMappingData> entityAliasMappingData);

    public List<EntityAliasMappingData> getEntityAliasMappingByIdandType(int entityId, String entityType);

    public EntityAliasMappingData getEntityAliasMappingByIdandTypeandbrand (int entityId, String entityType, int brandId);

    public List<EntityAliasMappingData> getEntityMappingByEntityKey(List<EntityAliasKey> entityAliasKeys);

}
