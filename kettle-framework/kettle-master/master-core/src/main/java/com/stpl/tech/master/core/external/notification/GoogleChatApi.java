package com.stpl.tech.master.core.external.notification;

import com.google.gson.Gson;
import com.stpl.tech.master.core.external.notification.service.impl.GoogleChatServiceImpl;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

public class GoogleChatApi {

	private static final String TEXT = "text";
	private static final Gson gson = new Gson();
	private static final String baseUrl = "https://chat.googleapis.com/v1";
	private final GoogleChatServiceImpl googleChatService = new GoogleChatServiceImpl();

	/**
	 * Prepare Message and send to Slack
	 */
	public void call(SlackMessage message) throws IOException, InterruptedException {
		if (message != null) {
			this.send(message.getText(),message.getChannel(),message.getEnv());
		}
	}

	private String send(String message, String channelId, EnvType env) throws IOException, InterruptedException {
		HttpURLConnection connection = null;

		try {
			String accessToken = googleChatService.getAccessToken(googleChatService.getGoogleCredential());
			Map<String,String> body = new HashMap<>();
			body.put(TEXT,message);
			String msg = gson.toJson(body);
			final URL url = new URL(baseUrl + "/" + channelId + "/messages");
			connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("POST");
			connection.setRequestProperty("Authorization", "Bearer " + accessToken);
			connection.setRequestProperty("Content-Type", "application/json; UTF-8");
			connection.setDoOutput(true);
			connection.setDoOutput(true);
			DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream());
			outputStream.writeBytes(msg);
			outputStream.flush();
			outputStream.close();
			BufferedReader rd = new BufferedReader(new InputStreamReader(connection.getInputStream()));
			String line;
			StringBuffer response = new StringBuffer();
			while ((line = rd.readLine()) != null) {
				response.append(line).append("\n");
			}
			rd.close();
			return response.toString();
		} catch (Exception e) {
			throw new SlackException(e);
		} finally {
			if (connection != null) {
				connection.disconnect();
			}
		}
	}
}
