package com.stpl.tech.master.data.dao;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 08-07-2016.
 */
public interface AbstractMasterDao extends AbstractDao {
    public <T> T update(T data);

    public <T> T add(T data);

    public <T> List<T> findAll(Class<T> data);

    public <T, R> T find(Class<T> data, R key);

    public <T> void delete(T data);

    public <T> List<T> addAll(List<T> list);
}
