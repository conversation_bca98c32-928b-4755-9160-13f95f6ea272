package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "VERSION_COMPATIBILITY_DATA")
public class VersionCompatibilityData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Integer id;
    @Column(name = "APPLICATION_NAME")
    private String applicationName;
    @Column(name = "APPLICATION_VERSION")
    private String applicationVersion;
    @Column(name = "POS_VERSION")
    private String posVersion;
    @Column(name = "VERSION_STATUS")
    private String status;
    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
    @Column(name = "UPDATED_AT")
    private Date updatedAt;
}
