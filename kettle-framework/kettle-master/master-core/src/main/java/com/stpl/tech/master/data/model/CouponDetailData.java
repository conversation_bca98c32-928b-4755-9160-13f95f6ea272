/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * UnitDetail generated by hbm2java
 */
@Entity
@Table(name = "COUPON_DETAIL_DATA")
public class CouponDetailData implements java.io.Serializable {

	private static final long serialVersionUID = 8672416293090573948L;

	private Integer couponDetailId;
	private OfferDetailData offerDetail;
	private String couponCode;
	private String couponReuse; // Y/N if reusable or not, default 'N'
	private String customerReuse; // Y/N if reusable or not, default 'N'
	private Date startDate;
	private Date endDate;
	private String couponStatus; // active or not
	private Integer maxUsage; // maximum usage count of the coupon
	private Integer maxCustomerUsage; // maximum usage count of the coupon
	private int usageCount; // usage count till date
	private String manualOverride;

	private Integer couponApplicability;

	private String customerVisibility; // Y/N, default 'Y'

	// validation mappings of any kind of data regarding order manipulations
//	private List<CouponDetailMappingData> mappings;

	public CouponDetailData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COUPON_DETAIL_ID", unique = true, nullable = false)
	public Integer getCouponDetailId() {
		return this.couponDetailId;
	}

	public void setCouponDetailId(Integer couponDetailId) {
		this.couponDetailId = couponDetailId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "OFFER_DETAIL_ID", nullable = false)
	public OfferDetailData getOfferDetail() {
		return this.offerDetail;
	}

	public void setOfferDetail(OfferDetailData offerDetail) {
		this.offerDetail = offerDetail;
	}

	@Column(name = "COUPON_REUSE", nullable = false, length = 1)
	public String getCouponReuse() {
		return this.couponReuse;
	}

	public void setCouponReuse(String couponReuse) {
		this.couponReuse = couponReuse;
	}

	@Column(name = "COUPON_CODE", nullable = false, length = 8)
	public String getCouponCode() {
		return this.couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	@Column(name = "CUSTOMER_REUSE", nullable = false, length = 1)
	public String getCustomerReuse() {
		return this.customerReuse;
	}

	public void setCustomerReuse(String customerReuse) {
		this.customerReuse = customerReuse;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "START_DATE", nullable = false, length = 19)
	public Date getStartDate() {
		return this.startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "END_DATE", nullable = false, length = 19)
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@Column(name = "COUPON_STATUS", nullable = false, length = 15)
	public String getCouponStatus() {
		return this.couponStatus;
	}

	public void setCouponStatus(String couponStatus) {
		this.couponStatus = couponStatus;
	}

	@Column(name = "MAX_USAGE", nullable = true)
	public Integer getMaxUsage() {
		return maxUsage;
	}

	public void setMaxUsage(Integer maxUsage) {
		this.maxUsage = maxUsage;
	}

	@Column(name = "USAGE_COUNT", nullable = false)
	public int getUsageCount() {
		return usageCount;
	}

	public void setUsageCount(int usageCount) {
		this.usageCount = usageCount;
	}

//	@OneToMany(mappedBy = "couponDetail")
	/*public List<CouponDetailMappingData> getMappings() {
		return mappings;
	}

	public void setMappings(List<CouponDetailMappingData> orderSources) {
		this.mappings = orderSources;
	}*/

	@Column(name = "MANUAL_OVERRIDE", nullable = false)
	public String getManualOverride() {
		return manualOverride;
	}

	public void setManualOverride(String manualOverride) {
		this.manualOverride = manualOverride;
	}

	@Column(name = "MAX_CUSTOMER_USAGE", nullable = true)
	public Integer getMaxCustomerUsage() {
		return maxCustomerUsage;
	}

	public void setMaxCustomerUsage(Integer maxCustomerUsage) {
		this.maxCustomerUsage = maxCustomerUsage;
	}

	@Column(name = "COUPON_APPLICABILITY", nullable = true)
	public Integer getCouponApplicability() {
		return couponApplicability;
	}

	public void setCouponApplicability(Integer couponApplicability) {
		this.couponApplicability = couponApplicability;
	}

	@Column(name = "CUSTOMER_VISIBILITY", nullable = false)
	public String getCustomerVisibility() { return this.customerVisibility; }

	public void setCustomerVisibility(String customerVisibility) { this.customerVisibility = customerVisibility; }

}
