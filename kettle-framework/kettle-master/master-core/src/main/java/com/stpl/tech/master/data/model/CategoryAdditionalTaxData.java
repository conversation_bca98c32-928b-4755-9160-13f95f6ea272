/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * CategoryAdditionalTaxData generated by hbm2java
 */
@Entity
@Table(name = "CATEGORY_ADDITIONAL_TAX_DATA")
public class CategoryAdditionalTaxData implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4217998970116292300L;

	private Integer categoryAdditionalTaxDataId;
	private TaxCategoryData categoryData;
	private TaxProfileData taxData;
	private CountryDetail country;
	private StateDetail state;
	private BigDecimal taxRate;
	private Date startDate;
	private String status;
	private String createdBy;
	private Date createdAt;
	private String updatedBy;
	private Date updatedAt;

	public CategoryAdditionalTaxData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CATEGORY_ADDITIONAL_TAX_DATA_ID", unique = true, nullable = false)
	public Integer getCategoryAdditionalTaxDataId() {
		return this.categoryAdditionalTaxDataId;
	}

	public void setCategoryAdditionalTaxDataId(Integer categoryTaxDataId) {
		this.categoryAdditionalTaxDataId = categoryTaxDataId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CATEGORY_ID", nullable = false)
	public TaxCategoryData getCategoryData() {
		return categoryData;
	}

	public void setCategoryData(TaxCategoryData categoryData) {
		this.categoryData = categoryData;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TAX_ID", nullable = false)
	public TaxProfileData getTaxData() {
		return taxData;
	}

	public void setTaxData(TaxProfileData taxData) {
		this.taxData = taxData;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COUNTRY_DETAIL_ID", nullable = false)
	public CountryDetail getCountry() {
		return country;
	}

	public void setCountry(CountryDetail country) {
		this.country = country;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "STATE_DETAIL_ID", nullable = false)
	public StateDetail getState() {
		return state;
	}

	public void setState(StateDetail state) {
		this.state = state;
	}

	@Column(name = "TAX_RATE", precision = 10)
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Column(name = "TAX_STATUS", nullable = false, length = 15)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	
	@Column(name = "CREATED_BY", length = 50)
	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_AT", nullable = true, length = 19)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "UPDATED_BY", length = 50)
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATED_AT", nullable = true, length = 19)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}


}
