package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name="DEPARTMENT_DESIGNATION_MAPPING")
public class DepartmentDesignationMapping implements java.io.Serializable {

    private int deptId;
    private int designationId;
    private int mappingId;
    private String mappingStatus;
    private boolean isEmpMealEligible;

    public DepartmentDesignationMapping() {
    }

    public DepartmentDesignationMapping(int deptId, int designationId, String mappingStatus, boolean isEmpMealEligible) {
        this.deptId = deptId;
        this.designationId = designationId;
//        this.mappingId = mappingId;
        this.mappingStatus = mappingStatus;
        this.isEmpMealEligible = isEmpMealEligible;
    }

    @Id
    @Column(name="DEPT_ID")
    public int getDeptId() {
        return deptId;
    }

    public void setDeptId(int deptId) {
        this.deptId = deptId;
    }

    @Id
    @Column(name="DESIGNATION_ID")
    public int getDesignationId() {
        return designationId;
    }

    public void setDesignationId(int designationId) {
        this.designationId = designationId;
    }

    @Column(name="MAPPING_ID",unique = true,nullable = false)
    @GeneratedValue(strategy = IDENTITY)
    public int getMappingId() {
        return mappingId;
    }

    public void setMappingId(int mappingId) {
        this.mappingId = mappingId;
    }

    @Column(name="MAPPING_STATUS")
    public String getMappingStatus() {
        return mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    @Column(name="EMPLOYEE_MEAL_ELIGIBLE")
    public boolean isEmpMealEligible() {
        return isEmpMealEligible;
    }

    public void setEmpMealEligible(boolean empMealEligible) {
        isEmpMealEligible = empMealEligible;
    }
}
