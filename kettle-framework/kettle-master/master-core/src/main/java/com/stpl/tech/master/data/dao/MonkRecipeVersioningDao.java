package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.recipe.monk.model.MonkRecipeVersionData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MonkRecipeVersioningDao extends MongoRepository<MonkRecipeVersionData, String> {

	List<MonkRecipeVersionData> findByStatus(String status);

    List<MonkRecipeVersionData> findByStatusAndRegion(String status, String region);

    List<MonkRecipeVersionData> findByStatusAndRegionAndVersion(String status, String region, String version);
}
