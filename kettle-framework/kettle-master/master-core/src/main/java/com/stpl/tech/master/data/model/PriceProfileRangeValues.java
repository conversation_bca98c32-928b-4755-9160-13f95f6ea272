package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "PRICE_PROFILE_RANGE_VALUES")
public class PriceProfileRangeValues {

    private Integer profileProfileRangeValuesId;
    private Integer priceProfileId;
    private BigDecimal startPrice;
    private BigDecimal endPrice;
    private BigDecimal deltaPrice;
    private String rangeValuesStatus;
    private Date lastUpdateTime;
    private String lastUpdatedBy;
    private Date rangeValuesActivationTime;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRICE_PROFILE_RANGE_VALUES_ID", unique = true, nullable = false)
    public Integer getProfileProfileRangeValuesId() {
        return profileProfileRangeValuesId;
    }

    public void setProfileProfileRangeValuesId(Integer profileProfileRangeValuesId) {
        this.profileProfileRangeValuesId = profileProfileRangeValuesId;
    }

    @Column(name = "PRICE_PROFILE_ID", nullable = false)
    public Integer getPriceProfileId() {
        return priceProfileId;
    }

    public void setPriceProfileId(Integer priceProfileId) {
        this.priceProfileId = priceProfileId;
    }

    @Column(name = "START_PRICE", nullable = false)
    public BigDecimal getStartPrice() {
        return startPrice;
    }

    public void setStartPrice(BigDecimal startPrice) {
        this.startPrice = startPrice;
    }

    @Column(name = "END_PRICE", nullable = false)
    public BigDecimal getEndPrice() {
        return endPrice;
    }

    public void setEndPrice(BigDecimal endPrice) {
        this.endPrice = endPrice;
    }

    @Column(name = "DELTA_PRICE", nullable = false)
    public BigDecimal getDeltaPrice() {
        return deltaPrice;
    }

    public void setDeltaPrice(BigDecimal deltaPrice) {
        this.deltaPrice = deltaPrice;
    }

    @Column(name = "RANGE_VALUES_STATUS", nullable = false)
    public String getRangeValuesStatus() {
        return rangeValuesStatus;
    }

    public void setRangeValuesStatus(String rangeValuesStatus) {
        this.rangeValuesStatus = rangeValuesStatus;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name = "LAST_UPDATE_BY", nullable = false)
    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "RANGE_VALUES_ACTIVATION_TIME", nullable = false)
    public Date getRangeValuesActivationTime() {
        return rangeValuesActivationTime;
    }

    public void setRangeValuesActivationTime(Date rangeValuesActivationTime) {
        this.rangeValuesActivationTime = rangeValuesActivationTime;
    }
}
