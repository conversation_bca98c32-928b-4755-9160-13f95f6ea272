/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * BusinessDivision generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "BUSINESS_DIVISION")
public class BusinessDivision implements java.io.Serializable {

	private Integer businessDivId;
	private CompanyDetail companyDetail;
	private String businessDivName;
	private String busienssDivDesc;
	private String busienssDivCategory;
	private Set<UnitDetail> unitDetails = new HashSet<UnitDetail>(0);
	private Set<Department> departments = new HashSet<Department>(0);

	public BusinessDivision() {
	}

	public BusinessDivision(CompanyDetail companyDetail, String businessDivName, String busienssDivDesc,
			String busienssDivCategory) {
		this.companyDetail = companyDetail;
		this.businessDivName = businessDivName;
		this.busienssDivDesc = busienssDivDesc;
		this.busienssDivCategory = busienssDivCategory;
	}

	public BusinessDivision(CompanyDetail companyDetail, String businessDivName, String busienssDivDesc,
			String busienssDivCategory, Set<UnitDetail> unitDetails, Set<Department> departments) {
		this.companyDetail = companyDetail;
		this.businessDivName = businessDivName;
		this.busienssDivDesc = busienssDivDesc;
		this.busienssDivCategory = busienssDivCategory;
		this.unitDetails = unitDetails;
		this.departments = departments;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "BUSINESS_DIV_ID", unique = true, nullable = false)
	public Integer getBusinessDivId() {
		return this.businessDivId;
	}

	public void setBusinessDivId(Integer businessDivId) {
		this.businessDivId = businessDivId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COMPANY_ID", nullable = false)
	public CompanyDetail getCompanyDetail() {
		return this.companyDetail;
	}

	public void setCompanyDetail(CompanyDetail companyDetail) {
		this.companyDetail = companyDetail;
	}

	@Column(name = "BUSINESS_DIV_NAME", nullable = false)
	public String getBusinessDivName() {
		return this.businessDivName;
	}

	public void setBusinessDivName(String businessDivName) {
		this.businessDivName = businessDivName;
	}

	@Column(name = "BUSIENSS_DIV_DESC", nullable = false, length = 5000)
	public String getBusienssDivDesc() {
		return this.busienssDivDesc;
	}

	public void setBusienssDivDesc(String busienssDivDesc) {
		this.busienssDivDesc = busienssDivDesc;
	}

	@Column(name = "BUSIENSS_DIV_CATEGORY", nullable = false, length = 30)
	public String getBusienssDivCategory() {
		return this.busienssDivCategory;
	}

	public void setBusienssDivCategory(String busienssDivCategory) {
		this.busienssDivCategory = busienssDivCategory;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "businessDivision")
	public Set<UnitDetail> getUnitDetails() {
		return this.unitDetails;
	}

	public void setUnitDetails(Set<UnitDetail> unitDetails) {
		this.unitDetails = unitDetails;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "businessDivision")
	public Set<Department> getDepartments() {
		return this.departments;
	}

	public void setDepartments(Set<Department> departments) {
		this.departments = departments;
	}

}
