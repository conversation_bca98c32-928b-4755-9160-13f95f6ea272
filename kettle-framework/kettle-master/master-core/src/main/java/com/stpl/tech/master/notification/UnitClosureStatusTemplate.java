package com.stpl.tech.master.notification;

import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class UnitClosureStatusTemplate extends AbstractTemplate {

   private UnitClosureEvent unitClosureEvent;
    private String basePath;
    private final Map<String, Object> data = new HashMap<String, Object>();
    private String unitName;

    private String status;

    public UnitClosureStatusTemplate(UnitClosureEvent unitClosureEvent, String basePath, String unitName, String status){
        this.unitClosureEvent = unitClosureEvent;
        this.basePath = basePath;
        this.unitName = unitName;
        this.status = status;
    }
    @Override
    public String getTemplatePath() {
        return "template/UnitClosureInitiationTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/UnitClosureNotification/"+"Template_"+new Date().getTime()+".html";
    }

    @Override
    public Map<String, Object> getData() {
        data.put("unitClosureEvent", unitClosureEvent);
        data.put("unitName", unitName);
        data.put("status",status);
        return data;
    }
}
