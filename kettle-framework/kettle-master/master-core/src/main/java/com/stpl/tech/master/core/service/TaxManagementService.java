/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import java.util.List;
import java.util.Map;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.TaxCategoryData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.tax.model.CategoryAdditionalTax;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxInfo;

public interface TaxManagementService {

	public List<TaxCategory> getAllTaxCategories();

	public List<IdCodeName> getAllTaxCategoriesBasicInfo();

	public TaxCategory addTaxCategoy(TaxCategory category);

	public TaxCategory updateTaxCategoy(TaxCategory category);
	
	public List<TaxInfo> getAllTaxes();
	
	public List<IdCodeName> getAllTaxesBasicInfo();
	
	public boolean addTaxData(TaxInfo info);

	public CategoryTax updateCategoryTax(CategoryTax categoryTax);

	public CategoryTax fetchCategoryTax(int countryId, int categoryId, int taxId, boolean getForAllStates);
	
	public List<CategoryTax> fetchAllCategoryTax();

	public CategoryTax fetchAllCategoryTax(int countryId, int categoryId);

	public CategoryAdditionalTax fetchCategoryAdditionalTax(int countryId, int categoryId);

	public Map<String, CategoryAdditionalTax> fetchAllCategoryAdditionalTax();

	public CategoryAdditionalTax fetchCategoryAdditionalTax(int countryId, int categoryId, int taxId);

	public CategoryAdditionalTax updateCategoryAdditionalTax(CategoryAdditionalTax categoryTax);

	public TaxCategoryData updateTaxCategoyStatus(int id, String status) throws DataUpdationException;

	public TaxCategory getTaxCategory(int id);

	public List<IdCodeName> getAllCountries();

}
