package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.CompanyBrandMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyBrandMappingDao extends JpaRepository<CompanyBrandMapping, Integer> {

    @Query("SELECT cbm FROM CompanyBrandMapping cbm WHERE cbm.status = :status")
    public List<CompanyBrandMapping> findByStatus(@Param("status") String status);

}
