package com.stpl.tech.master.data.model;

public enum UnitProductUpdateType {

    UNIT_PRODUCT_PRICING_PROFILE("updating unit product pricing   profile, status, is delivery only and is dine in consumables"),
    UNIT_PRODUCT_PRICING_DESC("updating unit product pricing   Product Alias and dimension desc");

    private final String description;

    UnitProductUpdateType(String description) {
        this.description = description;
    }

    public String getShortName() {
        return this.description;
    }

}
