package com.stpl.tech.master.core.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.stpl.tech.master.core.external.cache.PreAuthenticatedApiCache;
import com.stpl.tech.master.core.service.AccessControlService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.AccessControlDao;
import com.stpl.tech.master.data.model.AccessControlListData;
import com.stpl.tech.master.data.model.PreAuthenticatedApiData;
import com.stpl.tech.master.data.model.UserRoleData;
import com.stpl.tech.master.domain.model.AccessControlList;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.PreAuthApi;
import com.stpl.tech.master.domain.model.SwitchStatus;
import com.stpl.tech.master.domain.model.UserRole;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Rahul Singh on 08-07-2016.
 */
@Service
public class AccessControlServiceImpl implements AccessControlService {

    Logger LOG = LoggerFactory.getLogger(AccessControlServiceImpl.class);

    @Autowired
    private AccessControlDao accessControlDao;

    @Autowired
    private PreAuthenticatedApiCache preAuthenticatedApiCache;

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PreAuthApi> getAllPreAuthenticatedApi() {
        List<PreAuthApi> preAuthApis = new ArrayList<>();
        accessControlDao.findAll(PreAuthenticatedApiData.class).forEach(preAuthenticatedApiData -> {
            preAuthApis.add(MasterDataConverter.convert(preAuthenticatedApiData));
        });
        return preAuthApis;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String addPreAuthenticatedApi(PreAuthApi api) {
       List<String> preAuthApis = preAuthenticatedApiCache.getPreAuthenticatedAPIs();
        if(preAuthApis.contains(api.getApi())){
            return "API already exists";
        }
        PreAuthenticatedApiData preAuthenticatedApiData = new PreAuthenticatedApiData();
        preAuthenticatedApiData.setApi(api.getApi());
        preAuthenticatedApiData.setStatus(api.getStatus().value());
        accessControlDao.add(preAuthenticatedApiData);
        preAuthenticatedApiCache.loadPreAuthenticatedApiCache();
        return "API Added successfully";
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivatePreAuthenticatedApi(int apiId) {
        PreAuthenticatedApiData preAuthenticatedApiData = accessControlDao.find(PreAuthenticatedApiData.class, apiId);
        if(preAuthenticatedApiData!=null){
            preAuthenticatedApiData.setStatus(SwitchStatus.IN_ACTIVE.value());
            final PreAuthenticatedApiData finalPreAuthenticatedApiData = (PreAuthenticatedApiData) accessControlDao.update(preAuthenticatedApiData);
            List<String> preAuthApis = preAuthenticatedApiCache.getPreAuthenticatedAPIs().stream().filter(api -> finalPreAuthenticatedApiData.getApi().equals(api)).collect(Collectors.toList());
            preAuthenticatedApiCache.setPreAuthenticatedAPIs(preAuthApis);
            return true;
        }
        LOG.error("Pre Authenticated Api with Id {} not found to deactivate!", apiId);
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activatePreAuthenticatedApi(int apiId) {
        PreAuthenticatedApiData preAuthenticatedApiData = accessControlDao.find(PreAuthenticatedApiData.class, apiId);
        if(preAuthenticatedApiData!=null){
            preAuthenticatedApiData.setStatus(SwitchStatus.ACTIVE.value());
            preAuthenticatedApiData = (PreAuthenticatedApiData)accessControlDao.update(preAuthenticatedApiData);
            List<String> preAuthApis = preAuthenticatedApiCache.getPreAuthenticatedAPIs();
            if(!preAuthApis.contains(preAuthenticatedApiData.getApi())){
                preAuthApis.add(preAuthenticatedApiData.getApi());
            }
            preAuthenticatedApiCache.setPreAuthenticatedAPIs(preAuthApis);
            return true;
        }
        LOG.error("Pre Authenticated Api with Id {} not found to activate!", apiId);
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AccessControlList> getAccessControls(ApplicationName appName) {
        List<AccessControlListData> accessControlListDatas = accessControlDao.getAccessControls(appName);
        List<AccessControlList> accessControlLists = accessControlListDatas.stream().map(MasterDataConverter::convert).collect(Collectors.toList());
        return accessControlLists;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addAccessControl(AccessControlList accessControlList) {
        LOG.info("Request to get all access controls");
        AccessControlListData accessControlListData = (AccessControlListData)accessControlDao.add(MasterDataConverter.convert(accessControlList));
        if(accessControlListData!=null){
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateAccessControl(int aclId) {
        AccessControlListData accessControlListData = accessControlDao.find(AccessControlListData.class, aclId);
        if(accessControlListData!=null){
            accessControlListData.setStatus(SwitchStatus.IN_ACTIVE.value());
            accessControlListData = (AccessControlListData) accessControlDao.update(accessControlListData);
            if(accessControlListData!=null){
                return true;
            }else{
                LOG.error("Error deactivating ACL ID!", aclId);
                return false;
            }
        }
        LOG.error("Access control list data with Id {} not found to deactivate!", aclId);
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateAccessControl(int aclId) {
        AccessControlListData accessControlListData = accessControlDao.find(AccessControlListData.class, aclId);
        if(accessControlListData!=null){
            accessControlListData.setStatus(SwitchStatus.ACTIVE.value());
            accessControlListData = (AccessControlListData) accessControlDao.update(accessControlListData);
            if(accessControlListData!=null){
                return true;
            }else{
                LOG.error("Error activating ACL ID!", aclId);
                return false;
            }
        }
        LOG.error("Access control list data with Id {} not found to activate!", aclId);
        return false;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UserRole> getUserRolesByApplication(ApplicationName appName) {
        List<UserRoleData> userRoleDataByAppName = accessControlDao.getUserRoleDataByAppName(appName);
        List<UserRole> userRoles = userRoleDataByAppName.stream().map(MasterDataConverter::convert).collect(Collectors.toList());
        return userRoles;
    }
}
