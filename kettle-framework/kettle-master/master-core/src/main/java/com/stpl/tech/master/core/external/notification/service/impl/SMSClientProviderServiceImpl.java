package com.stpl.tech.master.core.external.notification.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.exception.SMSClientAllocationException;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.notification.sms.SMSGupshupWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.SMSClientName;

@Service
public class SMSClientProviderServiceImpl implements SMSClientProviderService {
	private static final Logger LOG = LoggerFactory.getLogger(SMSClientProviderServiceImpl.class);

	@Autowired
	private EnvironmentPropertiesCache propertiesCache;

	/* (non-Javadoc)
	 * @see com.stpl.tech.master.core.external.notification.service.impl.SMSClientProviderService#getSMSClient(com.stpl.tech.master.core.notification.sms.SMSType, com.stpl.tech.master.domain.model.ApplicationName)
	 */
	@Override
	public SMSWebServiceClient getSMSClient(SMSType type, ApplicationName applicationName) {
		String clientName = propertiesCache.getcompanyConfigAttributes(applicationName.name()).get(type.getValue());
		LOG.info("Application name : " + applicationName.name() + " SMS type  : " + type.getKey() + "   Client Name : "
				+ clientName);
		if (clientName == null) {
			try {
				throw new SMSClientAllocationException(
						"No mapping found in CONFIG_ATTRIBUTE_VALUE table for application : " + applicationName.name()
								+ " for SMS type  : " + type.getKey() + " key : " + type.getValue());
			} catch (SMSClientAllocationException e) {
				LOG.info(e.getMessage(), e);
			}
		}
		
		if (clientName.equalsIgnoreCase(SMSClientName.SOLUTION_INFINI.name())) {
			switch (type) {
			case OTP:
				return SolsInfiniWebServiceClient.getOTPClient();
			case OPT_VIA_IVR:
				return SolsInfiniWebServiceClient.getOTPClientViaIVR();
			case TRANSACTIONAL:
				return SolsInfiniWebServiceClient.getTransactionalClient();
			case DOHFUL_OTP:
				return SolsInfiniWebServiceClient.getDohfulOTPClient();
			case PROMOTIONAL:
				LOG.info("Promotional client not available");
				break;
			}
		} else if (clientName.equalsIgnoreCase(SMSClientName.SMS_GUPSHUP.name())) {
			switch (type) {
			case OTP:
				return SMSGupshupWebServiceClient.getOTPInstance();
			case TRANSACTIONAL:
				return SMSGupshupWebServiceClient.getRegularInstance();
			case PROMOTIONAL:
				return SMSGupshupWebServiceClient.getPromotionalInstance();
			}
		}
		return null;
	}
}
