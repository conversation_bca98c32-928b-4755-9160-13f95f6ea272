package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.UnitClosureStateEnum;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.SessionCacheService;
import com.stpl.tech.master.core.service.UnitClosureService;
import com.stpl.tech.master.core.service.UnitManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.UnitClosureEventRepository;
import com.stpl.tech.master.data.dao.UnitClosureFormDataRepository;
import com.stpl.tech.master.data.dao.UnitClosureFormMetaDataRepository;
import com.stpl.tech.master.data.dao.UnitClosureStateEventRepository;
import com.stpl.tech.master.data.dao.UnitClosureStateRepository;
import com.stpl.tech.master.data.dao.UnitDetailRepository;
import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.master.data.model.UnitClosureFormData;
import com.stpl.tech.master.data.model.UnitClosureFormMetaData;
import com.stpl.tech.master.data.model.UnitClosureState;
import com.stpl.tech.master.data.model.UnitClosureStateEvent;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.UnitClosureEventDomain;
import com.stpl.tech.master.core.service.model.UnitClosureFormDataDomain;
import com.stpl.tech.master.domain.model.UnitClosureFormMetaDataDomain;
import com.stpl.tech.master.domain.model.UnitClosureStateDomain;
import com.stpl.tech.master.domain.model.UnitClosureStateEventDomain;
import com.stpl.tech.master.notification.UnitClosureStatusEmail;
import com.stpl.tech.master.notification.UnitClosureStatusTemplate;
import com.stpl.tech.master.notification.UnitClosurePendingTaskEmail;
import com.stpl.tech.master.notification.UnitClosurePendingTaskTemplate;
import com.stpl.tech.master.notification.UnitClosureTaskDoneEmail;
import com.stpl.tech.master.notification.UnitClosureTaskDoneTemplate;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import lombok.extern.log4j.Log4j2;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Log4j2
public class UnitClosureServiceImpl implements UnitClosureService {
    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private UnitClosureEventRepository unitClosureEventRepository;
    @Autowired
    private UnitClosureStateRepository unitClosureStateRepository;
    @Autowired
    private UnitClosureStateEventRepository unitClosureStateEventRepository;

    @Autowired
    private MasterProperties props;

    @Autowired
    private UnitDetailRepository unitDetailRepository;

    @Autowired
    private MasterDataCacheService masterCacheImpl;

    @Autowired
    private UnitClosureFormMetaDataRepository unitClosureFormMetaDataRepository;
    @Autowired
    private UnitClosureFormDataRepository unitClosureFormDataRepository;

    @Autowired
    private UnitManagementService unitManagementService;

    @Autowired
    private SessionCacheService sessionCacheService;

    private boolean updateClosureStatusOnUnitAndCache(Integer unitId, UnitClosureStateEnum status) throws MasterException {
        Optional<UnitDetail> unitDetail = unitDetailRepository.findById(unitId);

        if (unitDetail.isPresent()){
            unitDetail.get().setClosureStatus(status.name());
            if(status == UnitClosureStateEnum.CLOSED) { unitDetail.get().setIsClosed(AppUtils.YES); }
            UnitDetail detail = unitDetailRepository.save(unitDetail.get());
            if (Objects.isNull(detail.getUnitId())) {
                throw new MasterException("Closure State Failed for Unit::" + unitId);
            }
           boolean isCacheRefresh =  masterCacheImpl.refreshUnit(unitId, false);
            log.info("::: Refresh unit {} cache status : {} :::",unitId,isCacheRefresh);
            return true;
        }
        throw new MasterException("Error while updating unit data" + unitId);
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean initiateUnitClosure(UnitClosureEventDomain unitClosureEventDomain) throws MasterException, EmailGenerationException {
        UnitClosureEvent event = unitClosureEventRepository.findByUnitIdAndClosureStatusNot(unitClosureEventDomain.getUnitId(),UnitClosureStateEnum.CANCELLED.name());
        if (Objects.nonNull(event)) {
            throw new MasterException("Closure Event for Unit::" + unitClosureEventDomain.getUnitId() +" Already Exists");
        }
        UnitClosureEvent unitClosureEvent = unitClosureEventRepository.save(
                UnitClosureEvent.builder().unitId(unitClosureEventDomain.getUnitId())
                        .operationStopDate(AppUtils.getDate(unitClosureEventDomain.getOperationStopDate()))
                        .closureStatus(UnitClosureStateEnum.INITIATED.name())
                        .creationTIme(AppUtils.getCurrentTimestamp())
                        .message(unitClosureEventDomain.getMessage())
                        .createdBy(unitClosureEventDomain.getCreatedBy())
                        .isFormSubmitted("N")
                        .build());
        if (Objects.isNull(unitClosureEvent.getRequestId())) {
            throw new MasterException("Closure Event Generation Failed for Unit::" + unitClosureEventDomain.getUnitId());
        }
        List<UnitClosureState> unitClosureStates = unitClosureStateRepository.findByStateStatusOrderByStateIdAsc(AppConstants.ACTIVE);
        for (UnitClosureState state : unitClosureStates) {
            UnitClosureStateEvent unitClosureStateEvent = unitClosureStateEventRepository.save(UnitClosureStateEvent.builder()
                    .requestId(unitClosureEvent.getRequestId())
                    .stateId(state.getStateId())
                    .eventStatus(UnitClosureStateEnum.INITIATED.name()).build());
            if (Objects.isNull(unitClosureStateEvent.getEventId())) {
                throw new MasterException("Closure State"+ state.getStateName() +" Generation Failed for Unit::" + unitClosureEventDomain.getUnitId());
            }
        }
        log.info("UNIT CLOSURE EVENT ::: Generated with Id ->" +unitClosureEvent.getRequestId() + " for Unit Id :" +unitClosureEvent.getUnitId());
        if(updateClosureStatusOnUnitAndCache(unitClosureEvent.getUnitId(),UnitClosureStateEnum.INITIATED)){
            String unitName = masterDataCache.getUnit(unitClosureEvent.getUnitId()).getName();
            UnitClosureStatusTemplate unitClosureInitiationTemplate = new UnitClosureStatusTemplate(unitClosureEvent,props.getBasePath(),unitName,UnitClosureStateEnum.INITIATED.name());
            UnitClosureStatusEmail unitClosureInitiationEmail = new UnitClosureStatusEmail(unitClosureInitiationTemplate,unitName,props,UnitClosureStateEnum.INITIATED.name());
            unitClosureInitiationEmail.sendEmail();
            return true;
       }
        throw new MasterException("Closure State Failed for Unit::" + unitClosureEventDomain.getUnitId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitClosureEventDomain> getClosureByStatus(String closureStatus) {
        List<UnitClosureEvent> unitClosureEvents = unitClosureEventRepository.findByClosureStatus(closureStatus);
        List<UnitClosureEventDomain> unitClosureEventDomains = new ArrayList<>();
//        List<UnitClosureEventDomain> unitClosureEventDomains = DtoDataMapper.INSTANCE.toUnitClosureEventDtoList(unitClosureEvents);
        unitClosureEvents.forEach(event -> {
            unitClosureEventDomains.add(UnitClosureEventDomain.builder()
                    .requestId(event.getRequestId())
                    .unitId(event.getUnitId())
                    .closureStatus(event.getClosureStatus())
                    .operationStopDate(event.getOperationStopDate())
                    .createdBy(event.getCreatedBy())
                    .creationTIme(event.getCreationTIme())
                    .message(event.getMessage()).build());
        });
        unitClosureEventDomains.forEach(event-> event.setUnitName(masterDataCache.getUnit(event.getUnitId()).getName()));
        return unitClosureEventDomains;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitClosureStateEventDomain> getClosureByState(Long requestId) throws MasterException {
        List<UnitClosureStateEvent> unitClosureStateEvents = unitClosureStateEventRepository.findByRequestId(requestId);
        if (unitClosureStateEvents.isEmpty()) {
            throw new MasterException("State events not found for the Requested Id");
        }
//        List<UnitClosureStateEventDomain> unitClosureStateEventDomains= DtoDataMapper.INSTANCE.toUnitClosureStateEventsDtoList(unitClosureStateEvents);
        List<UnitClosureStateEventDomain> unitClosureStateEventDomains= new ArrayList<>();
        unitClosureStateEvents.forEach(event -> {
            unitClosureStateEventDomains.add(UnitClosureStateEventDomain.builder().stateId(event.getStateId())
                    .eventId(event.getEventId())
                    .requestId(event.getRequestId())
                    .eventStatus(event.getEventStatus())
                    .updatedBy(event.getUpdatedBy())
                    .lastUpdationTime(event.getLastUpdationTime()).build());
        });
        for (UnitClosureStateEventDomain unitClosureStateEvent : unitClosureStateEventDomains) {
            UnitClosureState stateEvent = unitClosureStateRepository.findByStateId(unitClosureStateEvent.getStateId());
            unitClosureStateEvent.setUnitClosureStateDomain(UnitClosureStateDomain.builder()
                    .stateId(stateEvent.getStateId())
                    .stateName(stateEvent.getStateName())
                    .stateDescription(stateEvent.getStateDescription())
                    .destinationSource(stateEvent.getDestinationSource())
                    .stateOwner(stateEvent.getStateOwner()).build());
        }
        return unitClosureStateEventDomains;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean closeStateEvent(UnitClosureStateEventDomain unitClosureStateEventDomain) throws MasterException {
        UnitClosureStateEvent unitClosureStateEvent = unitClosureStateEventRepository.findByRequestIdAndStateId(unitClosureStateEventDomain.getRequestId(),
                unitClosureStateEventDomain.getStateId());
        if (UnitClosureStateEnum.CLOSED.name().equalsIgnoreCase(unitClosureStateEvent.getEventStatus())) {
            throw new MasterException("State event Already Closed");
        }
        unitClosureStateEvent.setEventStatus(UnitClosureStateEnum.CLOSED.name());
        unitClosureStateEvent.setUpdatedBy(unitClosureStateEvent.getUpdatedBy());
        unitClosureStateEvent.setLastUpdationTime(AppUtils.getCurrentTimestamp());
        unitClosureStateEvent = unitClosureStateEventRepository.save(unitClosureStateEvent);
        if (Objects.isNull(unitClosureStateEvent.getEventId())) {
            throw new MasterException("State event Closure Failed");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean closeEvent(UnitClosureEventDomain unitClosureEventDomain) throws MasterException {
        Optional<UnitClosureEvent> unitClosureEvent = unitClosureEventRepository.findById(unitClosureEventDomain.getRequestId());
        if (unitClosureEvent.isEmpty() || UnitClosureStateEnum.CLOSED.name().equalsIgnoreCase(unitClosureEvent.get().getClosureStatus())) {
            throw new MasterException("Event Closure Failed as It's already Closed");
        }
        List<UnitClosureStateEvent> unitClosureStateEvents = unitClosureStateEventRepository.findByRequestId(unitClosureEventDomain.getRequestId());
        for (UnitClosureStateEvent event : unitClosureStateEvents) {
            event.setEventStatus(UnitClosureStateEnum.CLOSED.name());
            event.setUpdatedBy(unitClosureEventDomain.getUpdatedBy());
            event.setLastUpdationTime(AppUtils.getCurrentTimestamp());
            event = unitClosureStateEventRepository.save(event);
            if (Objects.isNull(event.getEventId())) {
                throw new MasterException("State event Closure Failed");
            }
        }
        UnitClosureEvent closureEvent = unitClosureEvent.get();
        closureEvent.setClosureStatus(UnitClosureStateEnum.CLOSED.name());
        closureEvent.setClosureDate(AppUtils.getCurrentDate());
        closureEvent.setUpdatedBy(unitClosureEventDomain.getUpdatedBy());
        closureEvent.setLastUpdationTime(AppUtils.getCurrentTimestamp());
        closureEvent = unitClosureEventRepository.save(closureEvent);
        if (Objects.isNull(closureEvent.getRequestId())) {
            throw new MasterException("Event Closure Failed");
        }
        return updateClosureStatusOnUnitAndCache(closureEvent.getUnitId(),UnitClosureStateEnum.CLOSED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String initiateOperationClose() throws IOException {
      try {
          log.info("INITIATED UNIT CLOSURE EVENT FROM INITIATED TO PROCESS FOR BUSINESS DATE {}", AppUtils.getBusinessDate());
          List<UnitClosureEvent> unitClosureEvents = unitClosureEventRepository.findByOperationStopDateAndClosureStatus(AppUtils.getBusinessDate(),UnitClosureStateEnum.INITIATED.name());
          if (!unitClosureEvents.isEmpty()) {
              List<Integer> unitIds = new ArrayList<>();
              for (UnitClosureEvent unitClosureEvent : unitClosureEvents) {
                  try {
                      unitClosureEvent.setClosureStatus(UnitClosureStateEnum.PROCESSING.name());
                      unitClosureEvent = unitClosureEventRepository.save(unitClosureEvent);
                      if (Objects.isNull(unitClosureEvent.getRequestId())) {
                          log.info("Error Updating the Unit Closure for Unit ::: {}", unitClosureEvent.getUnitId());
                      }
                     boolean res = updateClosureStatusOnUnitAndCache(unitClosureEvent.getUnitId(),UnitClosureStateEnum.PROCESSING);
                      log.info("Unit closure status update on unit and cache status ::: {}",res);
                      unitIds.add(unitClosureEvent.getUnitId());
                  } catch (Exception e) {
                      log.error("Error Updating the Unit Closure for Unit ::: {}", unitClosureEvent.getUnitId(),e);
                  }
              }
            log.info("############# Inside initiateOperationClose, SCM endpoint : {} ",props.getSCMEndpoint());
              HttpResponse response =  WebServiceHelper.postRequestWithAuthInternal(props.getSCMEndpoint()+"rest/v1/asset-management/start-unit-closure-regular-event-day-close",props.getSCMAuthInternal(),unitIds);
             log.info("########### Response of start-unit-closure-regular-event-day-close  : {} ",response.getStatusLine().toString());
            if(response.getStatusLine().getStatusCode() != 200) {
                        throw  new RuntimeException("Error from start-unit-closure-regular-event-day-close api : "+ EntityUtils.toString(response.getEntity(), "UTF-8"));
            }
            return response.getStatusLine().toString();
          }
      }catch (Exception e) {
          log.error("Error while initiating unit closure, error:{}",e.getMessage());
          throw new RuntimeException(e);
      }
        return null;
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addUnitClosureFormMetadata(List<UnitClosureFormMetaDataDomain> formMetaData) {
        log.info("Adding Unit Closure Form Meta Data, size :  {}", formMetaData.size());
        formMetaData.forEach(e->{
            unitClosureFormMetaDataRepository.save(UnitClosureFormMetaData.builder()
                            .department(e.getDepartment())
                            .taskDescription(e.getTaskDescription())
                            .dateRequired(e.getDateRequired())
                            .commentRequired(e.getCommentRequired())
                            .attachmentRequired(e.getAttachmentRequired())
                    .build());
        });
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addUnitClosureFormData(List<UnitClosureFormDataDomain> unitClosureFormDataDomains, Long unitClosureEventId, Integer userId, Boolean isSaved) throws MasterException {

   try {
       Optional<UnitClosureEvent> unitClosureEvent = unitClosureEventRepository.findById(unitClosureEventId);
       unitClosureEvent.ifPresentOrElse(e -> {
           if (!e.getClosureStatus().equalsIgnoreCase(UnitClosureStateEnum.CANCELLED.name()) && e.getIsFormSubmitted().equalsIgnoreCase("Y")) {
               throw new RuntimeException("Unit Closure form data is already submitted !");
           }
           unitClosureFormDataDomains.forEach(formData -> {
               unitClosureFormDataRepository.save(MasterDataConverter.convert(formData));
           });
       if(!isSaved) {
           e.setIsFormSubmitted("Y");
           e.setClosureDate(AppUtils.getCurrentDate());
           e.setUpdatedBy(userId);
           e.setLastUpdationTime(AppUtils.getCurrentTimestamp());
           e.setClosureStatus(UnitClosureStateEnum.CLOSED.name());
           unitClosureEventRepository.save(e);
           try {
               unitManagementService.deactivateUnit(e.getUnitId(),userId);

           } catch (DataNotFoundException | DataUpdationException | IOException ex) {
               throw new RuntimeException(ex);
           }
           try {
               updateClosureStatusOnUnitAndCache(e.getUnitId(),UnitClosureStateEnum.CLOSED);
               sessionCacheService.clearUnitSession(e.getUnitId());
           } catch (MasterException ex) {
               throw new RuntimeException(ex);
           }
       }
       }, () -> {
           throw new RuntimeException("Error while fetching unit closure event");
       });

       return true;
   }catch (Exception e) {
       throw new MasterException("UNIT_CLOSURE_ERROR",e.getMessage());
   }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<UnitClosureFormDataDomain> getUnitClosureFormMetaData(Integer unitId){

       UnitClosureEvent unitClosureEvent = unitClosureEventRepository.findByUnitIdAndClosureStatus(unitId,UnitClosureStateEnum.PROCESSING.name());
       if(unitClosureEvent != null){
           List<UnitClosureFormData> unitClosureFormData = unitClosureFormDataRepository.findByUnitClosureEvent(unitClosureEvent);
            if(unitClosureFormData.size()>0) {
                return unitClosureFormData.stream().map(e -> MasterDataConverter.convert(e)).sorted(
                        (e1, e2) -> Long.compare(e1.getUnitClosureMetaData().getUnitClosureFormMetaDataId(), e2.getUnitClosureMetaData().getUnitClosureFormMetaDataId())
                ).collect(Collectors.toList());
            }
            return unitClosureFormMetaDataRepository.findAll()
                   .stream().map(e->MasterDataConverter.convert(e,unitClosureEvent)).sorted(
                           (e1,e2)->Long.compare(e1.getUnitClosureMetaData().getUnitClosureFormMetaDataId(),e2.getUnitClosureMetaData().getUnitClosureFormMetaDataId())
                   ) .collect(Collectors.toList());

       }

       return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<UnitClosureStateEventDomain> getStateFromUnitId(Integer unitId) throws MasterException {

      UnitClosureEvent event =  unitClosureEventRepository.findByUnitIdAndClosureStatusNot(unitId,UnitClosureStateEnum.CANCELLED.name());
        if(event!=null){
           return getClosureByState(event.getRequestId());
        }
        log.info("Unit closure Event not found for unit : "+unitId);
       return new ArrayList<>();
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void sendUnitClosureTaskDoneNotification(Long eventId) throws EmailGenerationException {

        Optional<UnitClosureEvent> res =  unitClosureEventRepository.findById(eventId);
        if(res.isPresent()){
            UnitClosureTaskDoneTemplate template = new UnitClosureTaskDoneTemplate(res.get(),props.getBasePath());
            UnitClosureTaskDoneEmail unitClosureTaskDoneEmail = new UnitClosureTaskDoneEmail(template,res.get(),props);
            unitClosureTaskDoneEmail.sendEmail();
           return;
        }
        log.info("Unit closure event not found for eventId : "+eventId);

    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void sendPendingTaskNotification(Integer unitId) throws EmailGenerationException {
        List<UnitClosureFormDataDomain>  res =  getUnitClosureFormMetaData(unitId);
           Map<String,List<String>> departmentMap = new HashMap<String,List<String>>();

           for(UnitClosureFormDataDomain d : res){
               List<String> messages =  departmentMap.getOrDefault(d.getUnitClosureMetaData().getDepartment(),new ArrayList<String>());
                StringBuffer sb = new StringBuffer(d.getUnitClosureMetaData().getTaskDescription());
                sb.append(d.getUnitClosureMetaData().getDepartment());
                sb.append(" | ");
                boolean check = false;
            if(Objects.equals(d.getUnitClosureMetaData().getCommentRequired(), "Y") && (d.getComment() == null || d.getComment().length()==0)){
                sb.append(" Please provide comment,");
              check = true;
            }
            if(Objects.equals(d.getUnitClosureMetaData().getDateRequired(), "Y") && d.getDate() ==null){
                sb.append(" Please provide date,");
                check = true;
            }
            if(Objects.equals(d.getUnitClosureMetaData().getAttachmentRequired(), "Y") && (d.getAttachmentId() ==null || d.getAttachmentId()==0)){
                sb.append(" Please provide attachment");
                check = true;
            }
            if(check){
                messages.add(sb.toString());
                departmentMap.put(d.getUnitClosureMetaData().getDepartment(),messages);
            }

           }

         if(!departmentMap.isEmpty()){
             String unitName = masterDataCache.getUnit(unitId).getName();
             UnitClosurePendingTaskTemplate unitClosurePendingTaskTemplate = new UnitClosurePendingTaskTemplate(departmentMap,props.getBasePath(),unitName);
             UnitClosurePendingTaskEmail unitClosurePendingTaskEmail = new UnitClosurePendingTaskEmail(unitClosurePendingTaskTemplate,props,unitName);
             unitClosurePendingTaskEmail.sendEmail();
         }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
   public Boolean suspendUnitClosureEvent(Integer unitId) throws MasterException {
   try {
       UnitClosureEvent unitClosureEvent = unitClosureEventRepository.findByUnitIdAndClosureStatus(unitId, UnitClosureStateEnum.PROCESSING.name());
      if(unitClosureEvent==null){
          log.info("No UnitClosureEvent found in Processing for unit id : {}",unitId);
          return  true;
      }
       unitClosureEvent.setClosureStatus(UnitClosureStateEnum.SUSPENDED.name());
       unitClosureEvent.setLastUpdationTime(AppUtils.getCurrentTimestamp());
       unitClosureEvent.setUpdatedBy(AppConstants.SYSTEM_EMPLOYEE_ID);
       unitClosureEventRepository.save(unitClosureEvent);
       updateClosureStatusOnUnitAndCache(unitId, UnitClosureStateEnum.SUSPENDED);
       String unitName = masterDataCache.getUnit(unitClosureEvent.getUnitId()).getName();
       UnitClosureStatusTemplate unitClosureSuspendTemplate = new UnitClosureStatusTemplate(unitClosureEvent,props.getBasePath(),unitName,UnitClosureStateEnum.SUSPENDED.name());
       UnitClosureStatusEmail unitClosureSuspendEmail = new UnitClosureStatusEmail(unitClosureSuspendTemplate,unitName,props,UnitClosureStateEnum.SUSPENDED.name());
       unitClosureSuspendEmail.sendEmail();
       return true;
   }catch(Exception e){
       log.info("Error in suspendUnit Closure Event for unit id : {}, msg : {}",unitId,e.getMessage());
        throw new MasterException("UNIT_CLOSURE_ERROR",e.getMessage());
   }
}


}
