package com.stpl.tech.master.notification;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ProductProfileDataChange extends EmailNotification {

    private EnvType envType;
    private String emailId;

    private ProductProfileDataChangeEmailTemplate changedTemplate;

    public ProductProfileDataChange(EnvType envType, String emailId, ProductProfileDataChangeEmailTemplate changedTemplate) {
        this.envType = envType;
        this.emailId = emailId;
        this.changedTemplate = changedTemplate;
    }

    @Override
    public String[] getToEmails() {
        List<String> toEmails = new ArrayList<>();
        toEmails.add(AppConstants.TECHNOLOGY_EMAIL);
        if (AppUtils.isProd(envType)) {
            if(StringUtils.isNotBlank(emailId)) {
                toEmails.add(emailId);
            }
            toEmails.add("<EMAIL>");
            toEmails.add("<EMAIL>");
        }
        return toEmails.toArray(new String[0]);
    }

    @Override
    public String getFromEmail() {
        return AppConstants.REPORTING_EMAIL;
    }

    @Override
    public String subject() {
        return (AppUtils.isDev(envType) ? "[DEV]" : "") + "Product profile data has been updated at " + AppUtils.getCurrentTimeISTStringWithoutMS();
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            if (Objects.nonNull(changedTemplate)) {
                return changedTemplate.getContent();
            }
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
        return null;
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
