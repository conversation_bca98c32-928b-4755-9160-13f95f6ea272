package com.stpl.tech.master.data.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnitProductPriceCategoryDomain  implements Serializable {


    BigDecimal price;
    String productName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnitProductPriceCategoryDomain domain = (UnitProductPriceCategoryDomain) o;
        return Objects.equals(price ,domain.price) && Objects.equals(productName, domain.productName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(price, productName);
    }
}
