package com.stpl.tech.master.core.external.partner.dao;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.data.model.ChannelPartnerCommission;
import com.stpl.tech.master.data.model.MenuRecommendationMappingData;
import com.stpl.tech.master.data.model.MenuSequenceData;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.MenuSequenceTimingData;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.ProductSequenceData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.PriceProfileDetail;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ChannelPartnerDao extends AbstractDao{

	public List<ChannelPartnerDetail> getAllChannelPartner(Date businessDate) throws DataNotFoundException;

	public ChannelPartnerCommission getChannelPartnerCommission(int partnerId, Date businessDate) throws DataNotFoundException;

	public List<UnitChannelPartnerMappingData> getUnitChannelPartnerMappings();

    UnitChannelPartnerMappingData findMappingByUnitAndPartnerId(Integer unitId, Integer partnerId);

    List<UnitChannelPartnerMenuMappingData> findMenuMappingByUnitPartnerMappingId(Integer unitChannelPartnerMappingId);

	List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings();

    List<UnitChannelPartnerMenuMappingData> findMenuMappingByUnitPartnerBrandMappingId(Integer unitChannelPartnerMappingId, Integer brandId);

    List<UnitChannelPartnerMenuMappingData> findMenuMappingByMenuSequenceId(Integer menuSeqId);

    List<MenuSequenceData> findAllActiveMenuSequenceData();

    List<String> findAllNotActiveMenuSequenceData(List<Integer> menuSequenceIds);

    List<ProductGroupData> getProductGroupByTagAndType(String tag, String type);

    List<ProductGroupData> getProductGroupByNameAndType(String name, String type,String tag);

    List<ProductSequenceData> getProductSequenceByGroupId(Integer groupId);

    List<ProductSequenceData> getProductSequenceByGroupIds(List<Integer> groupIds);

    List<ProductGroupData> findAllProductGroupDataWithFilter(String menuAppType, String groupType);

    List<ProductSequenceData> getAllProductSequenceByGroupId(Integer groupId);

    ProductSequenceData getProductSequenceByGroupIdAndProductId(Integer groupId, Integer productId);

    List<MenuSequenceMappingData> getAllMenuSequenceMappingByMenuSequenceId(Integer menuSequenceId);

    List<MenuSequenceTimingData> getAllMenuSequenceTimingByMenuSequenceId(Integer menuSequenceId);

    MenuSequenceTimingData getMenuSequenceTimingByMenuSequenceIdAndProductGroupId(Integer menuSequenceId,Integer productGroupId);

    MenuSequenceTimingData getMenuSequenceTimingByProductGroupId(Integer productGroupId);

    List<ProductGroupData> getAllProductGroupByGroupIds(List<Integer> groupIds);

    List<ProductSequenceData> getAllSequencedProductInGroupForCloning(Integer cloneId);

    List<MenuSequenceMappingData> getAllSequencedProductInMenuForCloning(Integer cloneId);

    List<MenuRecommendationMappingData> getAllRecommendationMapping(Integer recommendationId,String status);

	public List<Integer> getChannelPartnerIdsForMenuSequence(Integer menuSequenceId);

	public List<Integer> getUnitIdsForMenu(List<Integer> channelPartnerIds, Integer kettlePartnerId);

    List<Integer> getMenuSequenceIdsAsPerMenuApp(MenuApp menuApp);

    List<ProductGroupData> getProductGroupDataAsPerIdAndMenuApp(List<Integer> ids, MenuApp menuApp);

     List<Integer> getCategoryIdsMenuSequenceMapping(List<Integer> menuSequenceIds);

    PriceProfileDetail getUnitPartnerProfilePrice(Integer priceProfileId);

    List<PriceProfileDetail> getUnitPartnerProfilePrice();

    boolean addProfilePriceMapping(PriceProfileDetail detail);

    List<PriceProfileDetail> getProfilePrice(String priceProfileStrategy);

    boolean updateProfilePriceMapping(IdCodeName detail);

	public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles();

	public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles(Integer priceProfileId);

    List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings(Integer unitChannelPartnerMappingId,Integer brandId);

    void inActiveUnitChannelPartnerMenuMapping(UnitChannelPartnerMenuMappingData data);

    public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles(List<Integer> priceProfileIds);

    public List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappingsByChannelPartnerMappingId(List<Integer> unitChannelPartnerMappingIds,Integer brandId);
}
