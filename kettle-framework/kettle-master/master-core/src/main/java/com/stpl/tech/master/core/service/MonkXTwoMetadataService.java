package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.service.model.MonkXTwoMetadataRequest;
import com.stpl.tech.master.data.model.MonkXTwoMetadata;
import com.stpl.tech.master.data.model.MonkXTwoMetadataResponse;

public interface MonkXTwoMetadataService {
    MonkXTwoMetadata createMonkMetadata(MonkXTwoMetadataRequest request);
    MonkXTwoMetadataResponse getMonkMetaDataByUnitId(Integer unitId);
    Boolean updateMonkMetadata(MonkXTwoMetadataRequest request);
}
