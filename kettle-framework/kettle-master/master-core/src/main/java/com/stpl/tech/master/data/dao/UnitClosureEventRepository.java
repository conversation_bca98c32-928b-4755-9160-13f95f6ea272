package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitClosureEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface UnitClosureEventRepository extends JpaRepository<UnitClosureEvent, Long> {
    List<UnitClosureEvent> findByOperationStopDateAndClosureStatus(@NonNull Date operationStopDate, String status);
    UnitClosureEvent findByUnitIdAndClosureStatusNot(Integer unitId, String closureStatus);
    UnitClosureEvent findByUnitIdAndClosureStatus(Integer unitId, String closureStatus);
    List<UnitClosureEvent> findByClosureStatus(String closureStatus);
}