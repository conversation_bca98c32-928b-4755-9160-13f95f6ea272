/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import com.stpl.tech.master.core.LoginStatus;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.ACLCache;
import com.stpl.tech.master.core.external.cache.AuthorizedMacCache;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.cache.SessionCache;
import com.stpl.tech.master.core.service.RecipeService;
import com.stpl.tech.master.core.service.model.AclData;
import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.MasterUserDataDao;
import com.stpl.tech.master.data.model.AddressInfo;
import com.stpl.tech.master.data.model.AuthorizedMacAddress;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.CompanyDetail;
import com.stpl.tech.master.data.model.Department;
import com.stpl.tech.master.data.model.DepartmentDesignationMapping;
import com.stpl.tech.master.data.model.Designation;
import com.stpl.tech.master.data.model.EmployeeApplicationMappingDetail;
import com.stpl.tech.master.data.model.EmployeeDetail;
import com.stpl.tech.master.data.model.EmployeePassCode;
import com.stpl.tech.master.data.model.EmployeePermissionMapping;
import com.stpl.tech.master.data.model.EmployeeRoleMapping;
import com.stpl.tech.master.data.model.EmployeeSessionDetails;
import com.stpl.tech.master.data.model.EmployeeUnitMapping;
import com.stpl.tech.master.data.model.RoleBrandMapping;
import com.stpl.tech.master.data.model.SystemStatusMapping;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UserPolicyData;
import com.stpl.tech.master.data.model.UserPolicyRoleMapping;
import com.stpl.tech.master.data.model.UserRoleData;
import com.stpl.tech.master.domain.model.ACLRequest;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.EmployeeApplicationMapping;
import com.stpl.tech.master.domain.model.EmployeeRole;
import com.stpl.tech.master.domain.model.EmployeeRoleUpdateRequest;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.SwitchStatus;
import com.stpl.tech.master.domain.model.SystemStatus;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.RequestContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Repository
public class MasterUserDataDaoImpl extends AbstractMasterDaoImpl implements MasterUserDataDao {

    private static final Logger LOG = LoggerFactory.getLogger(MasterUserDataDaoImpl.class);
    private static final int HEAD_OFFICE_ID = 12100;

    @Autowired
    private MasterDataCache cache;

    @Autowired
    private MasterProperties props;

    @Autowired
    private SessionCache sessionCache;

    @Autowired
    private ACLCache aclCache;

    @Autowired
    private RecipeService recipeService;

    @Autowired
    private RecipeCache recipeCache;

    public String authenticateUser(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent, boolean isAdmin) throws AuthenticationFailureException {
        try {

            if (userSession.getUnitId() != 0) {
                userSession.setTrueCaller(cache.getUnitBasicDetail(userSession.getUnitId()).getTrueCallerEnabled());
            } else {
                userSession.setTrueCaller(TrueCallerSettings.DEFAULT);
            }
            userSession.setBusinessDate(AppUtils.getBusinessDate());
            UnitBasicDetail unitBasicDetail = cache.getUnitBasicDetail(userSession.getUnitId());
            if (Objects.nonNull(unitBasicDetail) && Objects.nonNull(recipeCache.getUnitMonkRecipeProfileVersion()) &&
                    recipeCache.getUnitMonkRecipeProfileVersion().containsKey(unitBasicDetail.getId())) {
                userSession.setMonkRecipeVersion(recipeCache.getUnitMonkRecipeProfileVersion().get(unitBasicDetail.getId()).getValue());
            } else {
                if (unitBasicDetail != null && unitBasicDetail.getRegion() != null) {
                    String monkRecipeVersion = recipeService.getMonkRecipeVersion(unitBasicDetail.getRegion());
                    if (!StringUtils.isEmpty(unitBasicDetail.getMonkRecipeProfile())) {
                        String profileBasedMonkRecipeVer = recipeService.getMonkRecipeVersion(unitBasicDetail.getMonkRecipeProfile());
                        if (!StringUtils.isEmpty(profileBasedMonkRecipeVer)) {
                            monkRecipeVersion = profileBasedMonkRecipeVer;
                        }
                    }

                    if (monkRecipeVersion != null) {
                        userSession.setMonkRecipeVersion(monkRecipeVersion);
                    } else {
                        userSession.setMonkRecipeVersion(recipeService.getMonkRecipeVersion());
                    }
                } else {
                    userSession.setMonkRecipeVersion(recipeService.getMonkRecipeVersion());
                }
            }
            userSession.setServerTime(AppUtils.getCurrentTimestamp());
            EmployeePassCode employeePassCode = getEmployeePassCode(userSession, ipAddress, macAddress, userAgent);

            if (AppUtils.isProd(props.getEnvironmentType()) && AppUtils.getDateAfterDays(employeePassCode.getLastUpdateTmstmp(), 30).before(AppUtils.getCurrentDate()) && userSession.getApplication().equals(ApplicationName.KETTLE_SERVICE.name()) && userSession.getScreenType().equals(ScreenType.POS)) {
                throw new AuthenticationFailureException("Your passcode has been expired. Please change your passcode");
            }
            if (userSession.getUserEmail() != null) {
                userSession.setPassword(PasswordImpl.decrypt(employeePassCode.getEmpPassCode()));
            }
            if (employeePassCode == null) {
                return null;
            }
            if (validatePassCode(userSession, ipAddress, macAddress, userAgent, employeePassCode.getEmpPassCode())) {
                if (isAdmin && ( Objects.isNull(unitBasicDetail) || Objects.isNull(unitBasicDetail.getClosed()) ||
                        Boolean.FALSE.equals(unitBasicDetail.getClosed()))) {
                    LOG.info("Admin user access to {} at unit {} , terminal {}", userSession.getUserId(), userSession.getUnitId(), userSession.getTerminalId());
                } else if ((userSession.getApplication().equals(ApplicationName.KETTLE_SERVICE.name()) ||
                        userSession.getApplication().equals(ApplicationName.SCM_SERVICE.name())) &&
                        (getEmpUnitMapping(userSession, ipAddress, macAddress, userAgent) == null)) {
                    return null;
                }
                /*
                 * if (userSession.getApplication().equals(ApplicationName. KETTLE_ADMIN.name())
                 * && !hasAdminDepartment(userSession, ipAddress, macAddress, userAgent)) {
                 * return null; }
                 */
                String sessionKey = generateSessionKey(userSession.getUserId(), userSession.getUnitId(), userSession.getTerminalId(), userSession.getScreenType());

                Map<String, Integer> permissionMap = getEmployeePermissions(userSession.getUserId());
                if (aclCache.addPermissions(sessionKey, permissionMap)) {

                    Date loginTime = makeLoginEntry(sessionKey, userSession.getUnitId(), userSession.getTerminalId(),
                            userSession.getUserId(), ipAddress, macAddress, userAgent, LoginStatus.SUCCESS, userSession);

                    String unitName = userSession.getUnitId() > 0 ? cache.getUnitBasicDetail(userSession.getUnitId()).getName() : null;

                    sessionCache.addToCache(props.getEnvironmentType(), sessionKey, userSession.getUnitId(), unitName, userSession.getUserId(), cache.getEmployee(userSession.getUserId()), loginTime, userSession.getScreenType());

                    return sessionKey;
                }
            } else {
                return null;
            }
        } catch (NoResultException e) {
            String msg = String.format("Unknown exception for Employee with ID : %d", userSession.getUserId());
            logInvalidLogin(userSession, ipAddress, macAddress, userAgent, LoginStatus.SYSTEM_ISSUE, msg, e);
        } catch (AuthenticationFailureException afe) {
            String msg = String.format("Authentication failed for Employee with ID : %d", userSession.getUserId());
            if (afe.getMessage().equalsIgnoreCase("Your passcode has been expired. Please change your passcode"))
                msg = String.format("Passcode expired");
            logInvalidLogin(userSession, ipAddress, macAddress, userAgent, LoginStatus.SYSTEM_ISSUE, msg, afe);
        } catch (Exception e) {
            String msg = String.format("Error while getting passcode for Employee with ID : %d", userSession.getUserId());
            LOG.error(msg, e);
            logInvalidLogin(userSession, ipAddress, macAddress, userAgent, LoginStatus.SYSTEM_ISSUE, msg, e);
        }
        return null;
    }

    public boolean changePasscode(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent) throws AuthenticationFailureException {
        try {
            EmployeePassCode employeePassCode = getEmployeePassCode(userSession, ipAddress, macAddress, userAgent);
            if (employeePassCode == null) {
                return false;
            }
            boolean isValidated = validatePassCode(userSession, ipAddress, macAddress, userAgent, employeePassCode.getEmpPassCode());
            if (isValidated) {
                employeePassCode.setEmpPassCode(PasswordImpl.encrypt(userSession.getNewPassword()));
                employeePassCode.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
                return true;
            }
        } catch (Exception e) {
            throw new AuthenticationFailureException(String.format("Error while changing passcode for Employee with ID : %d", userSession.getUserId()), e);
        }
        return false;
    }

    public boolean createPasscode(int userId, String newPasscode) throws AuthenticationFailureException {
        EmployeePassCode employeePassCode = getEmployeePassCode(userId);
        if (employeePassCode == null) {
            EmployeeDetail employeeDetail = manager.find(EmployeeDetail.class, userId);
            employeePassCode = new EmployeePassCode(employeeDetail, PasswordImpl.encrypt(newPasscode), AppUtils.getCurrentTimestamp(), employeeDetail.getEmpId());
            manager.persist(employeePassCode);
        } else {
            employeePassCode.setEmpPassCode(PasswordImpl.encrypt(newPasscode));
            employeePassCode.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
        }
        manager.flush();
        return true;
    }

    private boolean hasAdminDepartment(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent) throws AuthenticationFailureException {
        if (AppConstants.ADMIN_DEPARTMENTS.contains(getEmployee(userSession.getUserId()).getDepartment().getId())) {
            return true;
        }
        makeLoginEntry(userSession.getUnitId(), userSession.getTerminalId(), userSession.getUserId(), ipAddress, macAddress, userAgent, LoginStatus.FAILED_UNAUTHORISED_APPLICATION, userSession);
        throw new AuthenticationFailureException(String.format("Employee with ID : %d does not have belongs to admin.", userSession.getUserId()));
    }

    private EmployeeUnitMapping getEmpUnitMapping(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent) throws AuthenticationFailureException {
        try {
            Query query = manager.createQuery("FROM EmployeeUnitMapping E where E.unitDetail.unitId = :unitId and E.unitDetail.unitStatus= :unitStatus and E.employeeDetail.empId = :empId and E.employeeDetail.employmentStatus = :employmentStatus and E.mappingStatus = :mappingStatus");
            query.setParameter("unitId", userSession.getUnitId());
            query.setParameter("empId", userSession.getUserId());
            query.setParameter("employmentStatus", EmploymentStatus.ACTIVE.value());
            query.setParameter("mappingStatus", EmploymentStatus.ACTIVE.value());
            query.setParameter("unitStatus", UnitStatus.ACTIVE.value());
            return (EmployeeUnitMapping) query.getSingleResult();
        } catch (NoResultException e) {
            String msg = String.format("Employee with ID: %d does not work at Unit with ID: %d or Employee is not active", userSession.getUserId(), userSession.getUnitId());
            logInvalidLogin(userSession, ipAddress, macAddress, userAgent, LoginStatus.FAILED_INCORRECT_USER_UNIT_MAP, msg, e);
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    public List<Integer> getActiveEmpUnitIds(Integer empId) {
        Query query = manager.createQuery("SELECT E.unitDetail.unitId FROM EmployeeUnitMapping E WHERE E.unitDetail.unitStatus = :unitStatus and E.employeeDetail.empId = :empId and E.employeeDetail.employmentStatus = :employmentStatus and E.mappingStatus = :mappingStatus");
        query.setParameter("empId", empId);
        query.setParameter("employmentStatus", EmploymentStatus.ACTIVE.value());
        query.setParameter("mappingStatus", EmploymentStatus.ACTIVE.value());
        query.setParameter("unitStatus", UnitStatus.ACTIVE.value());
        return query.getResultList();
    }

    private void logInvalidLogin(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent, LoginStatus status, String msg, Exception e) throws AuthenticationFailureException {
        makeLoginEntry(userSession.getUnitId(), userSession.getTerminalId(), userSession.getUserId(), ipAddress, macAddress, userAgent, status, userSession);
        throw new AuthenticationFailureException(msg, e);
    }

    @Override
    public String getEmplyeePassCode(int userId) {
        try {
            Query query = manager.createQuery("FROM EmployeePassCode E where E.employeeDetail.empId = :empId");
            query.setParameter("empId", userId);
            Object o = query.getSingleResult();
            if (o != null) {
                return ((EmployeePassCode) o).getEmpPassCode();
            }
        } catch (NoResultException e) {
        }
        return null;

    }


    private EmployeePassCode getEmployeePassCode(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent) throws AuthenticationFailureException {
        try {
            Query query = manager.createQuery("FROM EmployeePassCode E where E.employeeDetail.empId = :empId");
            query.setParameter("empId", userSession.getUserId());
            Object o = query.getSingleResult();
            return (EmployeePassCode) o;
        } catch (NoResultException e) {
            String msg = String.format("Did not find Employee with ID : %d", userSession.getUserId());
            logInvalidLogin(userSession, ipAddress, macAddress, userAgent, LoginStatus.FAILED_INCORRECT_USERID, msg, e);
        }
        return null;
    }

    private EmployeePassCode getEmployeePassCode(int userId) throws AuthenticationFailureException {
        Query query = manager.createQuery("FROM EmployeePassCode E where E.employeeDetail.empId = :empId");
        query.setParameter("empId", userId);
        Object o = null;
        try {
            o = query.getSingleResult();
        } catch (NoResultException nre) {
            LOG.error("No Results for Employee Passcode for employee Id:{}", userId);
            o = null;
        }
        return o == null ? null : (EmployeePassCode) o;
    }

    /**
     * TODO need to add event listener
     *
     * @param userId
     * @param status
     */
    private Date makeLoginEntry(String sessionKey, int unitId, Integer terminalId, int userId, String ipAddress, String macAddress, String userAgent, LoginStatus status, UserSessionDetail userSessionDetail) {
        Date loginTime = AppUtils.getCurrentTimestamp();
        EmployeeSessionDetails session = new EmployeeSessionDetails();
        session.setEmployeeId(userId);
        session.setLoginAttempt(status.name());
        session.setCreationTime(loginTime);
        session.setSessionId(sessionKey);
        session.setUnitId(unitId);
        session.setTerminalId(terminalId);
        session.setIpAddress(ipAddress);
        session.setMacAddress(macAddress);
        session.setUserAgent(userAgent);
        if (Objects.nonNull(userSessionDetail.getVersion())) {
            session.setAppVersion(userSessionDetail.getVersion());
        }
        if (Objects.nonNull(userSessionDetail.getDeviceModel())) {
            session.setDeviceModel(userSessionDetail.getDeviceModel());
        }
        if (Objects.nonNull(userSessionDetail.getOsVersion())) {
            session.setOsVersion(userSessionDetail.getOsVersion());
        }
        if (Objects.nonNull(userSessionDetail.getApplication())) {
            session.setApplicationName(userSessionDetail.getApplication());
        }
        if (Objects.nonNull(userSessionDetail.getScreenType())) {
            session.setModuleName(userSessionDetail.getScreenType().name());
        }
        manager.persist(session);
        manager.flush();
        return loginTime;
    }

    /**
     * TODO need to add event listener
     *
     * @param userId
     * @param status
     */
    private Date makeLoginEntry(int unitId, Integer terminalId, int userId, String ipAddress, String macAddress, String userAgent, LoginStatus status, UserSessionDetail version) {
        return makeLoginEntry(null, unitId, terminalId, userId, ipAddress, macAddress, userAgent, status, version);
    }

    private boolean validatePassCode(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent, String passKey) throws AuthenticationFailureException {
        if (userSession.getPassword().equals(PasswordImpl.decrypt(passKey))) {
            return true;
        } else {
            makeLoginEntry(userSession.getUnitId(), userSession.getTerminalId(), userSession.getUserId(), ipAddress, macAddress, userAgent, LoginStatus.FAILED_INCORRECT_PASSWORD, userSession);
            throw new AuthenticationFailureException(String.format("Invalid Password for Employee with ID : %d", userSession.getUserId()));
        }
    }

    private String generateSessionKey(int userId, int unitId, int terminalId, ScreenType screenType) throws AuthenticationFailureException {
        return PasswordImpl.encrypt(AppUtils.getCurrentTimeISTString() + "userId:" + userId + "unitId:" + unitId + "terminalId:" + terminalId + (screenType != null ? "screenType:" + screenType.name() : ""));
    }

    public boolean logout(int unitId, int userId, String sessionKey) throws AuthenticationFailureException {
        try {
            Query query = manager.createQuery("FROM EmployeeSessionDetails E where E.employeeId = :empId and E.sessionId = :sessionId");
            query.setParameter("empId", userId);
            query.setParameter("sessionId", sessionKey);
            EmployeeSessionDetails emplyeeSessionDetails = (EmployeeSessionDetails) query.getSingleResult();
            emplyeeSessionDetails.setLogoutTime(AppUtils.getCurrentTimestamp());
            sessionCache.removeFromCache(userId, sessionKey);
            return true;
        } catch (NoResultException e) {
            throw new AuthenticationFailureException(String.format("Did not find session login for Employee with ID : %d", userId), e);
        } catch (Exception e) {
            throw new AuthenticationFailureException(String.format("Error while processing logout for Employee with ID : %d", userId), e);
        }

    }

    public List<Employee> getEmployees(int unitId) throws DataNotFoundException {
        try {
            Query query = manager.createQuery("FROM EmployeeUnitMapping E where E.unitDetail.unitId = :unitId and E.employeeDetail.employmentStatus = :status and E.mappingStatus = :mappingStatus");
            query.setParameter("unitId", unitId);
            query.setParameter("status", EmploymentStatus.ACTIVE.value());
            query.setParameter("mappingStatus", EmploymentStatus.ACTIVE.value());
            @SuppressWarnings("unchecked") List<EmployeeUnitMapping> mappings = query.getResultList();
            List<Employee> employees = new ArrayList<Employee>();
            for (EmployeeUnitMapping mapping : mappings) {
                employees.add(MasterDataConverter.convert(mapping.getEmployeeDetail()));
            }
            return employees;
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find Active Employees for unit with ID = %d", unitId), e);
        }
    }

    public Employee getEmployee(int userId) {
        EmployeeDetail detail = manager.find(EmployeeDetail.class, userId);
        return MasterDataConverter.convert(detail);
    }

    public Employee getEmployee(String empCode) {
        try {
            Query query = manager.createQuery("FROM EmployeeDetail emp where emp.employeeCode=:empCode");
            query.setParameter("empCode", empCode);
            if(query.getResultList().size() > 0)
                 return MasterDataConverter.convert((EmployeeDetail) query.getSingleResult());
        }catch(NoResultException e){
            LOG.error("Cannot get Employee for employeeCode:{}",empCode,e);
            return null;
        }
       return null;
    }

    @Override
    public EmployeeBasicDetail getActiveEmployee(int userId) {
        EmployeeDetail detail = manager.find(EmployeeDetail.class, userId);
        if (detail == null || !detail.getEmploymentStatus().equals(EmploymentStatus.ACTIVE.name())) {
            return null;
        } else {
            return MasterDataConverter.convertToEmployeeBasicDetail(detail);
        }
    }

    @Override
    public EmployeeBasicDetail getEmployeeBasicDetail(int userId) {
        EmployeeDetail detail = manager.find(EmployeeDetail.class, userId);
        return MasterDataConverter.convertToEmployeeBasicDetail(detail);
    }

    @Override
    public EmployeeBasicDetail getEmployeeBasicDetail(String empCode) {
        EmployeeDetail detail = manager.find(EmployeeDetail.class, empCode);
        return MasterDataConverter.convertToEmployeeBasicDetail(detail);
    }

    public Boolean authorizeMac(String macAddress) {
        AuthorizedMacAddress mac = new AuthorizedMacAddress();
        mac.setMacAddress(macAddress);
        if (manager.merge(mac) != null) {
            AuthorizedMacCache macCache = AuthorizedMacCache.getInstance();
            macCache.addMacAddress(macAddress);
            return true;
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    public ArrayList<String> getAuthorizedMacs() {
        Query query = manager.createQuery("SELECT E.macAddress FROM AuthorizedMacAddress E");
        return (ArrayList<String>) query.getResultList();
    }

    public AddressInfo getAddress(Integer addressId){
        try{
            AddressInfo info = manager.find(AddressInfo.class,addressId);
            return info;
        }catch (Exception e){
            LOG.error("Cannot find Address info for id :{}",addressId,e);
            return null;
        }
    }

    private void addOrUpdateAddress(Employee employee,EmployeeDetail detail){
        AddressInfo addressInfoByEmpPermanentAddr =null;
        AddressInfo addressInfoByEmpCurrentAddr = null;
        Address empAddress = employee.getCurrentAddress();
        if(Objects.nonNull(empAddress)){
            addressInfoByEmpPermanentAddr = MasterDataConverter.createAddress(employee.getCurrentAddress());
            manager.persist(addressInfoByEmpPermanentAddr);
            manager.flush();
            addressInfoByEmpCurrentAddr = MasterDataConverter.createAddress(employee.getCurrentAddress());
            manager.persist(addressInfoByEmpCurrentAddr);
            manager.flush();
            detail.setAddressInfoByEmpCurrentAddr(addressInfoByEmpCurrentAddr);
            detail.setAddressInfoByEmpPermanentAddr(addressInfoByEmpPermanentAddr);
        }else{
            Integer headOfficeId = HEAD_OFFICE_ID;
            AddressInfo info = getAddress(headOfficeId);
            if(Objects.isNull(info)){
                info = new AddressInfo("NAN", "NAN", "NAN", "Delhi", "Delhi", "India", "0", "", "", "", "", "");
                manager.persist(info);
                manager.flush();
            }
            detail.setAddressInfoByEmpCurrentAddr(info);
            detail.setAddressInfoByEmpPermanentAddr(info);
        }
    }


    public Employee addEmployee(Employee employee,Department department,Designation designation) throws DataUpdationException {
        try {
            LOG.info("Adding employee data for employee code -> " + employee.getEmployeeCode());
            if (employee.getId() > 0) {
                throw new DataUpdationException("Employee with employeeId " + employee.getId() + " already exists in our system");
            }
            EmployeeDetail detail = new EmployeeDetail();
            addOrUpdateAddress(employee,detail);
            updateEmployee(detail, employee,department,designation);
            detail.setImageKey(employee.getImageKey());
            if (employee.getReportingManager() != null && employee.getReportingManager().getId() > 0) {
                EmployeeDetail reportingManager = manager.find(EmployeeDetail.class, employee.getReportingManager().getId());
                detail.setReportingManager(reportingManager);
            }
            try {
                manager.persist(detail);
                return MasterDataConverter.convert(detail);
            }catch (Exception e){
                LOG.error("Exception in persisting data in db for empCode : {} ",employee.getEmployeeCode(),e);
                return null;
            }
        }catch(DataUpdationException e){
            LOG.error("Exception while adding Employee",e);
        }
        return null;
    }

    public Employee updateEmployee(Employee employee) throws DataUpdationException {
        EmployeeDetail employeeDetail = manager.find(EmployeeDetail.class, employee.getId());
        AddressInfo info = manager.find(AddressInfo.class, employee.getCurrentAddress().getId());
        updateAddress(info, employee.getCurrentAddress());
        AddressInfo info1 = manager.find(AddressInfo.class, employee.getPermanentAddress().getId());
        updateAddress(info1, employee.getCurrentAddress());
        updateEmployee(employeeDetail, employee,null,null);
        manager.flush();
        return getEmployee(employee.getId());

    }

    public Employee updateEmployeeForLucid(Employee employee,Department department,Designation designation)  {
        try {
            String contact = Objects.nonNull(employee.getPrimaryContact()) ? employee.getPrimaryContact() :
                    Objects.nonNull(employee.getCurrentAddress()) ? employee.getCurrentAddress().getContact1() : null;
            LOG.info("Getting Employee Detail for employee code :{}",employee.getEmployeeCode());
            Query query = manager.createQuery("FROM EmployeeDetail emp where emp.employeeCode = : empCode or emp.empContactNum1 = : empContact " );
            query.setParameter("empCode", employee.getEmployeeCode())
                .setParameter("empContact", employee.getEmploymentStatus().equals(EmploymentStatus.ACTIVE) ?
                        contact : "-1");
//            if(query.getResultList().size()==0){
//                query = manager.createQuery("FROM EmployeeDetail emp where emp.empContactNum1 = : empContact" );
//                query.setParameter("empContact", employee.getPrimaryContact());
//            }
            if (query.getResultList().size()==0) {
                return addEmployeeData(employee,department,designation);
            } else {
                LOG.info("Updating employee detail");
                for(Object obj : query.getResultList()) {
                    updateEmployee((EmployeeDetail) obj, employee, department, designation);
                    employee.setId(((EmployeeDetail) obj).getEmpId());
                    manager.flush();
                }
                return employee;
            }
        }catch(Exception e){
            LOG.error("Error in getting employee detail for employee code :{}",employee.getEmployeeCode(),e);
        }
        return null;
    }

    private Employee addEmployeeData(Employee employee,Department department,Designation designation){
        try {
            return addEmployee(employee,department,designation);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void updateAddress(AddressInfo info, Address address) {
        info.setAddressLine1(address.getLine1());
        info.setAddressLine2(address.getLine2());
        info.setAddressLine3(address.getLine3());
        info.setAddressType(address.getAddressType());
        info.setCity(address.getCity());
        info.setContactNum1(address.getContact1());
        info.setContactNum2(address.getContact2());
        info.setCountry(address.getCountry());
        info.setState(address.getState());
        info.setZipcode(address.getZipCode());
    }

    private void updateEmployee(EmployeeDetail info, Employee emp,Department department,Designation designation) {
        if(Objects.nonNull(emp.getBiometricId())) {
            info.setBiometricIdentifier(emp.getBiometricId());
        }

        if(Objects.nonNull(department))
            info.setDepartment(department);
        else
            info.setDepartment(getDepartment(emp.getDepartment().getId()));

        if(Objects.nonNull(designation))
            info.setDesignation(designation);
        else
            info.setDesignation(getDesignation(emp.getDesignation().getId()));

        if(Objects.nonNull(emp.getPrimaryContact())) {
            info.setEmpContactNum1(emp.getPrimaryContact());
        }else if(Objects.nonNull(emp.getCurrentAddress())) {
                info.setEmpContactNum1(emp.getCurrentAddress().getContact1());
        }

        if(Objects.nonNull(emp.getCurrentAddress())) {
            info.setEmpContactNum2(emp.getCurrentAddress().getContact2());
        }
        if(emp.getGender()!=null) {
            info.setEmpGender(emp.getGender());
        }

        info.setEmploymentStatus(emp.getEmploymentStatus().name());
        info.setEmploymentType(emp.getEmploymentType().name());
        info.setEmpName(emp.getName());
        info.setJoiningDate(emp.getJoiningDate());
        info.setEmployeeEmail(emp.getEmployeeEmail());
        EmployeeDetail reportingManager = manager.find(EmployeeDetail.class, emp.getReportingManager().getId());
        info.setReportingManager(reportingManager);
        info.setEmployeeCode(emp.getEmployeeCode());
        if(Objects.nonNull(emp.getCommunicationChannel())) {
            info.setCommunicationChannel(emp.getCommunicationChannel());
        }

        if (emp.getSdpContact() != null) {
            info.setSdpContact(emp.getSdpContact());
        }
        info.setEmployeeMealEligible(AppConstants.getValue(emp.isEmployeeMealEligible()));
        if(emp.getDob()!= null) {
            info.setDob(emp.getDob());
        }
        if(emp.getHrExecutive()!=null) {
            info.setHrExecutive(emp.getHrExecutive());
        }
        if(emp.getLeaveApprovalAuthority()!=null) {
            info.setLeaveApprovalAuthority(emp.getLeaveApprovalAuthority());
        }
        if(emp.getLocCode()!=null) {
            info.setLocCode(emp.getLocCode());
        }
        if(emp.getReasonForTermination()!=null) {
            info.setReasonForTermination(emp.getReasonForTermination());
        }
        if (Objects.nonNull(info.getUserPolicyData())) {
            emp.setUserPolicyId(info.getUserPolicyData().getUserPolicyId());
        }
        if(Objects.nonNull(emp.getCompany())){
            info.setCompanyDetail(getCompany(emp.getCompany().getId()));
        }else{
            info.setCompanyDetail(getCompany(AppConstants.SUNSHINE_COMPANY_ID));
        }
        info.setImageKey(emp.getImageKey());
        try{
            manager.flush();
        }
        catch (Exception e){
            LOG.error("Exception while updating for empCode :{}",emp.getEmployeeCode(),e);
        }
    }

    public boolean updateStatus(int employeeId, EmploymentStatus status) throws DataUpdationException {
        EmployeeDetail employeeDetail = manager.find(EmployeeDetail.class, employeeId);
        employeeDetail.setEmploymentStatus(status.name());
        if (EmploymentStatus.IN_ACTIVE.equals(status)) {
            employeeDetail.setSdpContact(null);
        }
        manager.flush();
        return true;

    }

    public Department getDepartment(int deptId) {
        Query query = manager.createQuery("FROM Department where deptId = :deptId");
        query.setParameter("deptId", deptId);
        Object o = query.getSingleResult();
        return o == null ? null : (Department) query.getSingleResult();
    }

    public Department getDepartment(String deptName) {
        Query query = manager.createQuery("FROM Department where deptName = :deptName");
        query.setParameter("deptName", deptName);
        Object o = query.getSingleResult();
        return o == null ? null : (Department) query.getSingleResult();
    }

    public Designation getDesignation(int designationId) {
        Query query = manager.createQuery("FROM Designation where designationId = :designationId");
        query.setParameter("designationId", designationId);
        Object o = query.getSingleResult();
        return o == null ? null : (Designation) query.getSingleResult();
    }

    public Designation getDesignation(String designationName) {
        Query query = manager.createQuery("FROM Designation where designationName = :designationName");
        query.setParameter("designationName", designationName);
        Object o = query.getSingleResult();
        return o == null ? null : (Designation) query.getSingleResult();
    }

    public CompanyDetail getCompany(int companyId) {
        Query query = manager.createQuery("FROM CompanyDetail where companyId = :id");
        query.setParameter("id", companyId);
        Object o = query.getSingleResult();
        return o == null ? null : (CompanyDetail) query.getSingleResult();
    }

    @Override
    public List<Employee> getEmployeesForUnit(Integer unitId) {
        Query query = manager.createQuery("FROM EmployeeUnitMapping " + "WHERE unitDetail.unitId = :unitId and employeeDetail.employmentStatus = :status ");
        query.setParameter("unitId", unitId);
        query.setParameter("status", EmploymentStatus.ACTIVE.value());
        @SuppressWarnings("unchecked") List<EmployeeUnitMapping> list = query.getResultList();
        List<Employee> employees = new ArrayList<Employee>();
        if (list != null) {
            for (EmployeeUnitMapping eum : list) {
                employees.add(MasterDataConverter.convert(eum.getEmployeeDetail()));
            }
        }
        return employees;
    }

    @Override
    public List<EmployeeBasicDetail> getEmployeesForEmployeeMealForUnit(int unitId) {
        Query query = manager.createQuery("FROM EmployeeUnitMapping " + " WHERE unitDetail.unitId = :unitId and employeeDetail.employmentStatus = :status  " + " and employeeDetail.employeeMealEligible = :employeeMealEligible");
        query.setParameter("unitId", unitId);
        query.setParameter("status", EmploymentStatus.ACTIVE.value());
        query.setParameter("employeeMealEligible", AppConstants.YES);
        @SuppressWarnings("unchecked") List<EmployeeUnitMapping> list = query.getResultList();
        return Optional.ofNullable(list).map(Collection::stream).orElse(Stream.empty()).map(EmployeeUnitMapping::getEmployeeDetail).map(MasterDataConverter::convertToEmployeeBasicDetail).collect(Collectors.toList());
    }

    @Override
    public List<EmployeeBasicDetail> getActiveEmployees(String designation, String department) {
        Integer companyId = RequestContext.getCompanyId();
        StringBuilder buffer = new StringBuilder("FROM EmployeeDetail where employmentStatus = :status");
        if (designation != null) {
            buffer.append(" and designation.designationName= :designationName");
        }
        if (department != null) {
            buffer.append(" and department.deptName= :departmentName");
        }
        if (Objects.nonNull(companyId)) {
            buffer.append(" AND companyDetail.companyId = :companyId");
        }
        Query query = manager.createQuery(buffer.toString());
        query.setParameter("status", SwitchStatus.ACTIVE.toString());
        if (designation != null) {
            query.setParameter("designationName", designation);
        }
        if (department != null) {
            query.setParameter("departmentName", department);
        }
        if (Objects.nonNull(companyId)) {
            query.setParameter("companyId", companyId);
        }
        @SuppressWarnings("unchecked") List<EmployeeDetail> list = query.getResultList();
        return Optional.ofNullable(list).map(Collection::stream).orElse(Stream.empty()).map(MasterDataConverter::convertToEmployeeBasicDetail).collect(Collectors.toList());
    }

    @Override
    public List<EmployeeBasicDetail> getAllEmployees(String designation) {
        Integer companyId = RequestContext.getCompanyId();
        StringBuffer buffer = new StringBuffer("select empId, empName, employmentStatus, department.deptName, designation.designationName ,employeeCode, employeeEmail, designation.maxAllocatedUnits,department.deptId,userPolicyData.userPolicyId,designation.designationId, companyDetail.companyId FROM EmployeeDetail" + " where employmentStatus != :archivedStatus");
        if (designation != null) {
            buffer.append(" and designation.designationName= :designationName and employmentStatus= :status");
        }
        if (Objects.nonNull(companyId)) {
            buffer.append(" AND companyDetail.companyId = :companyId");
        }
        Query query = manager.createQuery(buffer.toString());
        query.setParameter("archivedStatus", EmploymentStatus.ARCHIEVED.name());
        if (designation != null) {
            query.setParameter("designationName", designation);
            query.setParameter("status", SwitchStatus.ACTIVE.toString());
        }
        if (Objects.nonNull(companyId)) {
            query.setParameter("companyId", companyId);
        }
        @SuppressWarnings("unchecked") List<Object[]> list = query.getResultList();
        List<EmployeeBasicDetail> employees = new ArrayList<EmployeeBasicDetail>();
        if (list != null) {
            for (Object[] array : list) {
                EmployeeBasicDetail data = new EmployeeBasicDetail((Integer) array[0], (String) array[1], EmploymentStatus.valueOf((String) array[2]), (String) array[3], (String) array[4], (String) array[5], array[6] == null ? null : (String) array[6], "", array[7] == null ? -1 : (Integer) array[7], (Integer) array[8], (Integer) array[9]); // TODO ADD SDP DATA
                Integer designationId = (Integer) array[10];
                if (Objects.nonNull(designationId)) {
                    data.setDesignationId(designationId);
                }
                if(Objects.nonNull(array[11])){
                    data.setCompanyId((Integer) array[11]);
                }
                employees.add(data);
            }
        }
        return employees;
    }

    @Override
    public List<EmployeeBasicDetail> getAllEmployees(){
        StringBuffer buffer = new StringBuffer("select empId, empName, employmentStatus, department.deptName, designation.designationName ,employeeCode, employeeEmail, designation.maxAllocatedUnits,department.deptId,userPolicyData.userPolicyId FROM EmployeeDetail");

        Query query = manager.createQuery(buffer.toString());
        @SuppressWarnings("unchecked") List<Object[]> list = query.getResultList();
        List<EmployeeBasicDetail> employees = new ArrayList<EmployeeBasicDetail>();
        if (list != null) {
            for (Object[] array : list) {
                EmployeeBasicDetail data = new EmployeeBasicDetail((Integer) array[0], (String) array[1], EmploymentStatus.valueOf((String) array[2]), (String) array[3], (String) array[4], (String) array[5], array[6] == null ? null : (String) array[6], "", array[7] == null ? -1 : (Integer) array[7], (Integer) array[8], (Integer) array[9]); // TODO ADD SDP DATA
                employees.add(data);
            }
        }
        return employees;
    }

    @Override
    public List<IdName> getActiveEmployeeDetail() {
        List<IdName> idNameList = new ArrayList<>();
        Query query = manager.createQuery("select empId, empName FROM EmployeeDetail WHERE employmentStatus= :status");
        query.setParameter("status", SwitchStatus.ACTIVE.toString());
        List<Object[]> list = query.getResultList();
        if (list != null) {
            for (Object[] array : list) {
                IdName data = new IdName((Integer) array[0], (String) array[1]);
                idNameList.add(data);
            }
        }
        return idNameList;
    }

    @Override
    public boolean addEmployeeUnitMapping(int empId, List<Integer> unitIds) throws DataUpdationException {
        Query query = manager.createQuery("FROM EmployeeUnitMapping E where E.employeeDetail.empId = :empId");
        query.setParameter("empId", empId);
        @SuppressWarnings("unchecked") List<EmployeeUnitMapping> mappings = query.getResultList();
        Set<Integer> unitIdsTobeUpdated = new HashSet<Integer>(unitIds);
        if (mappings != null) {
            for (EmployeeUnitMapping eum : mappings) {
                int unitId = eum.getUnitDetail().getUnitId();
                if (unitIdsTobeUpdated.contains(unitId)) {
                    if (EmploymentStatus.IN_ACTIVE.name().equals(eum.getMappingStatus())) {
                        eum.setMappingStatus(EmploymentStatus.ACTIVE.name());
                        eum.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                    }
                    unitIdsTobeUpdated.remove(unitId);
                } else {
                    eum.setMappingStatus(EmploymentStatus.IN_ACTIVE.name());
                    eum.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                }
            }
            manager.flush();
        }
        EmployeeDetail employeeDetail = manager.find(EmployeeDetail.class, empId);
        for (Integer unitId : unitIdsTobeUpdated) {
            UnitDetail unitDetail = manager.find(UnitDetail.class, unitId);
            EmployeeUnitMapping mapping = new EmployeeUnitMapping(employeeDetail, unitDetail, EmploymentStatus.ACTIVE.name(), AppUtils.getCurrentTimestamp());
            manager.persist(mapping);
        }
        manager.flush();
        return true;
    }

    @SuppressWarnings("unchecked")
    public List<EmployeeUnitMapping> getEmployeeUnitMapping(int employeeId) {
        Query query = manager.createQuery("FROM EmployeeUnitMapping E where E.employeeDetail.empId = :empId");
        query.setParameter("empId", employeeId);
        return (List<EmployeeUnitMapping>) query.getResultList();
    }

    public List<UnitBasicDetail> getUnitsForEmployee(int employeeId, boolean onlyActive) throws DataNotFoundException {
        List<UnitBasicDetail> unitBasicDetailList = new ArrayList<UnitBasicDetail>();
        List<EmployeeUnitMapping> eumList = getEmployeeUnitMapping(employeeId);
        for (EmployeeUnitMapping eum : eumList) {
            if (onlyActive) {
                if (eum.getMappingStatus().equals("ACTIVE")) {
                    unitBasicDetailList.add(cache.getUnitBasicDetail(eum.getUnitDetail().getUnitId()));
                }
            } else {
                unitBasicDetailList.add(cache.getUnitBasicDetail(eum.getUnitDetail().getUnitId()));
            }
        }
        return unitBasicDetailList;
    }

    @Override
    public boolean resetPasscode(int userId, String newPasscode, int updatedBy) throws AuthenticationFailureException {
        try {
            EmployeePassCode employeePassCode = getEmployeePassCode(userId);
            if (employeePassCode == null) {
                EmployeeDetail employeeDetail = manager.find(EmployeeDetail.class, userId);
                employeePassCode = new EmployeePassCode(employeeDetail, PasswordImpl.encrypt(newPasscode), AppUtils.getCurrentTimestamp(), employeeDetail.getEmpId());
                manager.persist(employeePassCode);
            } else {
                employeePassCode.setEmpPassCode(PasswordImpl.encrypt(newPasscode));
                employeePassCode.setUpdatedBy(updatedBy);
                employeePassCode.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
            }
            manager.flush();
        } catch (NoResultException e) {
            throw new AuthenticationFailureException(String.format("Cannot change employee passcode as employee with ID : %d does not exist", userId), e);
        }
        return true;
    }

    @Override
    public boolean verifyUser(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent) throws AuthenticationFailureException {
        EmployeePassCode employeePassCode = getEmployeePassCode(userSession.getUserId());
        return validatePassCode(userSession, ipAddress, macAddress, userAgent, employeePassCode.getEmpPassCode());
    }

    @Override
    public Employee getSystemEmployee() {
        Query query = manager.createQuery("FROM EmployeeDetail E where E.empName = :empName");
        query.setParameter("empName", "System");
        return MasterDataConverter.convert((EmployeeDetail) query.getSingleResult());
    }

    private Map<String, Integer> getEmployeePermissions(int employeeId) {
        Query query = manager.createQuery("FROM EmployeePermissionMapping E where E.employee.empId = :employeeId AND E.status = :empstatus and E.acl.status = :aclstatus");
        query.setParameter("employeeId", employeeId);
        query.setParameter("empstatus", "ACTIVE");
        query.setParameter("aclstatus", "ACTIVE");
        List<EmployeePermissionMapping> epmList = query.getResultList();
        Map<String, Integer> permissionMap = new HashMap<>();
        for (EmployeePermissionMapping epm : epmList) {
            permissionMap.put(epm.getAcl().getModule(), epm.getPermission());
        }
        return permissionMap;
    }

    private Map<Integer, Map<String, Integer>> getEmployeePermissions(List<Integer> empIds) {
        Map<Integer, Map<String, Integer>> map = new HashMap<>();
        Query query = manager.createQuery(
                "FROM EmployeePermissionMapping E where E.employee.empId IN (:employeeIds) AND E.status = :empstatus and E.acl.status = :aclstatus order by E.employee.empId");
        query.setParameter("employeeIds", empIds);
        query.setParameter("empstatus", "ACTIVE");
        query.setParameter("aclstatus", "ACTIVE");
        List<EmployeePermissionMapping> epmList = query.getResultList();

        for (EmployeePermissionMapping epm : epmList) {
            int empId = epm.getEmployee().getEmpId();
            if (!map.containsKey(empId)) {
                map.put(empId, new HashMap<>());
            }
            map.get(empId).put(epm.getAcl().getModule(), epm.getPermission());

        }
        return map;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.data.dao.MasterUserDataDao#getAcl(com.stpl.tech.
     * master.domain.model.ApplicationName, int)
     */
    @Override
    public List<AclData> getAcl(ApplicationName appName, int employeeId, Integer companyId, Integer brandId) {
        List<AclData> list = new ArrayList<>();
        StringBuilder queryString = new StringBuilder("select ram.actionDetail.type, ram.actionDetail.name, erm.roleDetail.id FROM EmployeeRoleMapping erm, RoleActionMapping ram where erm.employeeId = :employeeId and" + " ram.roleDetail.id = erm.roleDetail.id and ram.mappingStatus = :mappingStatus " + "and erm.mappingStatus = :mappingStatus and " + "erm.roleDetail.status = :mappingStatus and " + "ram.actionDetail.status = :mappingStatus and " + "ram.actionDetail.application.status = :mappingStatus and " + "ram.actionDetail.application.name = :appName");
        StringBuilder roleBrandMapQueryString = new StringBuilder();
        List<Integer> mappedBrands = new ArrayList<>();

        if (Objects.nonNull(brandId)) {
            queryString.append(" AND erm.brandId = :brandId");
            roleBrandMapQueryString.append("SELECT rbm FROM RoleBrandMapping rbm " +
                    "WHERE rbm.brandId = :brandId AND rbm.status = :status");
        } else if (Objects.nonNull(companyId)) {
            mappedBrands = cache.getCompanyBrandsMap().getOrDefault(companyId, new ArrayList<>()).stream().map(Brand::getBrandId).toList();
            queryString.append(" AND erm.brandId IN :mappedBrands");
            roleBrandMapQueryString.append("SELECT rbm FROM RoleBrandMapping rbm " +
                    "WHERE rbm.brandId IN :mappedBrands AND rbm.status = :status");
        }

        Query query = manager.createQuery(queryString.toString());
        Query roleBrandMapQuery = null;
        query.setParameter("employeeId", employeeId);
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name());
        query.setParameter("appName", appName.name());

        if (Objects.nonNull(brandId)) {
            roleBrandMapQuery = manager.createQuery(roleBrandMapQueryString.toString());
            roleBrandMapQuery.setParameter("brandId", brandId);
            roleBrandMapQuery.setParameter("status", AppConstants.ACTIVE);
            query.setParameter("brandId", brandId);
        } else if (Objects.nonNull(companyId)) {
            roleBrandMapQuery = manager.createQuery(roleBrandMapQueryString.toString());
            roleBrandMapQuery.setParameter("mappedBrands", mappedBrands);
            roleBrandMapQuery.setParameter("status", AppConstants.ACTIVE);
            query.setParameter("mappedBrands", mappedBrands);
        }
        List<Integer> mappedActiveRoleIds = new ArrayList<>();
        List<RoleBrandMapping> roleBrandMappings = new ArrayList<>();
        if (Objects.nonNull(brandId) || Objects.nonNull(companyId)) {
            roleBrandMappings = roleBrandMapQuery.getResultList();
        }
        if (Objects.nonNull(roleBrandMappings) && !roleBrandMappings.isEmpty()) {
            roleBrandMappings.forEach(rbm -> {
                mappedActiveRoleIds.add(rbm.getRoleId());
            });
        }
        List<Object[]> actions = query.getResultList();
        if (actions != null && actions.size() > 0) {
            if (!mappedActiveRoleIds.isEmpty()) {
                actions = actions.stream().filter(action -> mappedActiveRoleIds.contains((Integer) action[2])).toList();
            }
            for (Object[] action : actions) {
                list.add(new AclData((String) action[0], (String) action[1]));
            }
        }
        return list;
    }

    @Override
    public Map<Integer, String> getEmployeesWithRole(String role) {
        Map<Integer, String> empIdNameMapping = new HashMap<Integer, String>();
        Query query = manager.createQuery("select emp.empId, emp.empName FROM EmployeeDetail emp, EmployeeRoleMapping empRole " + " where emp.id = empRole.employeeId and empRole.roleDetail.name = :role");
        query.setParameter("role", role);
        List<Object[]> list = query.getResultList();
        if (list != null && list.size() > 0) {
            for (Object[] elem : list) {
                empIdNameMapping.put((Integer) elem[0], (String) elem[1]);
            }
        }
        return empIdNameMapping;
    }

    @Override
    public Map<String, List<IdName>> getEmpListByAcl(ACLRequest aclRequest) {

        StringBuilder queryString = new StringBuilder("SELECT ad.name, erm.employeeId, ed.empName " +
                "FROM RoleActionMapping ram " +
                "JOIN ram.actionDetail ad " +
                "JOIN EmployeeRoleMapping erm ON ram.roleDetail = erm.roleDetail " +
                "JOIN EmployeeDetail ed ON erm.employeeId = ed.empId " +
                "WHERE erm.mappingStatus = :mappingStatus " );

        if(Objects.nonNull(aclRequest.getApplicationId())){
            queryString.append(" AND ad.application.id = :applicationId ");
        }
        if( Objects.nonNull(aclRequest.getAclType()) ) {
            queryString.append(" AND ad.type = :actionType ");
        }
        if( CollectionUtils.isNotEmpty( aclRequest.getAclCode() ) ) {
            queryString.append(" AND ad.name IN (:aclCodes) ");
        }


        Query query = manager.createQuery(queryString.toString());
        query.setParameter("mappingStatus", AppConstants.ACTIVE);
        if(Objects.nonNull(aclRequest.getApplicationId())){
            query.setParameter("applicationId", aclRequest.getApplicationId());
        }
        if( Objects.nonNull(aclRequest.getAclType()) ) {
            query.setParameter("actionType", aclRequest.getAclType());
        }
        if( CollectionUtils.isNotEmpty( aclRequest.getAclCode() ) ) {
            query.setParameter("aclCodes", aclRequest.getAclCode());
        }

        List<Object[]> list = query.getResultList();
        if(CollectionUtils.isNotEmpty(list)) {
            Map<String, List<IdName>> aclToEmpIdNameMap = new HashMap<>();
            for(Object[] row : list ) {
                String aclCode = (String) row[0];
                Integer empId = (Integer) row[1];
                String empName = (String) row[2];

                if (!aclToEmpIdNameMap.containsKey(aclCode)) {
                    aclToEmpIdNameMap.put(aclCode, new ArrayList<IdName>());
                }
                aclToEmpIdNameMap.get(aclCode).add(new IdName(empId, empName));
            }
            return aclToEmpIdNameMap;
        }
        return null;
    }

    @Override
    public Integer getEmployeeIdByEmail(String userEmail) {
        Query query = manager.createQuery("SELECT E.empId FROM EmployeeDetail E where E.employeeEmail = :email");
        query.setParameter("email", userEmail);
        try {
            List<Integer> empId = (List<Integer>) query.getResultList();
            if (empId.size() == 0) {
                return null;
            }
            return empId.get(0);
        } catch (Exception e) {
            LOG.error(e.getMessage());
        }
        return null;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.data.dao.MasterUserDataDao#updateRoles(com.stpl.tech
     * .master.domain.model.EmployeeRole)
     */
    @Override
    public boolean addRole(int employeeId, int roleId, int updatedBy) {
        Query query = manager.createQuery("FROM EmployeeRoleMapping E where E.employeeId = :employeeId and E.roleDetail.id = :roleId");
        query.setParameter("employeeId", employeeId);
        query.setParameter("roleId", roleId);
        try {
            Object roles = query.getSingleResult();
            if (roles == null) {
                addNewRole(employeeId, roleId, updatedBy);
            } else {
                EmployeeRoleMapping mapping = (EmployeeRoleMapping) roles;
                mapping.setMappingStatus(SwitchStatus.ACTIVE.name());
                manager.flush();
            }

        } catch (Exception e) {
            addNewRole(employeeId, roleId, updatedBy);
        }
        return true;
    }

    @Override
    public boolean addUserPolicyRole(Integer userPolicyId, Integer roleId) {
        Query query = manager.createQuery("FROM UserPolicyRoleMapping E where E.userPolicyId = :userPolicyId and E.roleId = :roleId");
        query.setParameter("userPolicyId", userPolicyId);
        query.setParameter("roleId", roleId);
        try {
            Object roles = query.getSingleResult();
            if (roles == null) {
                addNewUserPolicyRole(userPolicyId, roleId);
            } else {
                UserPolicyRoleMapping mapping = (UserPolicyRoleMapping) roles;
                mapping.setMappingStatus(SwitchStatus.ACTIVE.name());
                manager.flush();
            }
        } catch (Exception e) {
            addNewUserPolicyRole(userPolicyId, roleId);
        }
        return true;
    }

    @Override
    public List<UserPolicyData> checkUserPolicyExist(String departmentDesignation) {
        Query query = manager.createQuery("FROM UserPolicyData E where E.departmentDesignation = :departmentDesignation");
        query.setParameter("departmentDesignation", departmentDesignation);
        return query.getResultList();
    }

    private void addNewRole(int employeeId, int roleId, int updatedBy) {
        EmployeeRoleMapping mapping = new EmployeeRoleMapping();
        mapping.setEmployeeId(employeeId);
        mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
        mapping.setMappingStatus(SwitchStatus.ACTIVE.name());
        mapping.setRoleDetail(manager.find(UserRoleData.class, roleId));
        mapping.setUpdatedBy(updatedBy);
        manager.persist(mapping);
        manager.flush();
    }

    private void addNewUserPolicyRole(int userPolicyId, int roleId) {
        UserPolicyRoleMapping mapping = new UserPolicyRoleMapping();
        mapping.setUserPolicyId(userPolicyId);
        mapping.setMappingStatus(SwitchStatus.ACTIVE.name());
        mapping.setRoleId(roleId);
        manager.persist(mapping);
        manager.flush();
    }

    @Override
    public boolean deactivateRoles(EmployeeRole roles) {
        Query query = manager.createQuery("update EmployeeRoleMapping E set E.mappingStatus = :mappingStatus, E.updatedBy = :updatedBy, E.lastUpdateTime = :lastUpdateTime" + " where E.employeeId = :employeeId and E.mappingStatus <> :mappingStatus");
        query.setParameter("employeeId", roles.getEmployeeId());
        query.setParameter("updatedBy", roles.getUpdatedBy());
        query.setParameter("mappingStatus", SwitchStatus.IN_ACTIVE.name());
        query.setParameter("lastUpdateTime", AppUtils.getCurrentTimestamp());
        query.executeUpdate();
        manager.flush();
        return true;
    }

    @Override
    public boolean deactivateRolesInPolicy(EmployeeRole employeeRole) {
        Query query = manager.createQuery("update UserPolicyRoleMapping E set E.mappingStatus = :mappingStatus WHERE E.userPolicyId = :userPolicyId and E.mappingStatus <> :mappingStatus");
        query.setParameter("userPolicyId", employeeRole.getUserPolicyId());
        query.setParameter("mappingStatus", SwitchStatus.IN_ACTIVE.name());
        query.executeUpdate();
        manager.flush();
        return true;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.data.dao.MasterUserDataDao#getActiveRoles()
     */
    @Override
    public List<IdCodeName> getActiveRoles() {

        List<IdCodeName> all = new ArrayList<>();
        Query query = manager.createQuery("FROM UserRoleData E where E.status = :mappingStatus"); // and E.name NOT LIKE :textMatch
        Query roleBrandMappingQuery = manager.createQuery("FROM RoleBrandMapping rbm WHERE rbm.status = :status");
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name());
        roleBrandMappingQuery.setParameter("status", AppConstants.ACTIVE);
        //query.setParameter("textMatch", "%Head%");
        List<UserRoleData> roles = query.getResultList();
        List<RoleBrandMapping> roleBrandMappings = roleBrandMappingQuery.getResultList();
        for (UserRoleData role : roles) {
            List<Integer> mappedBrands = new ArrayList<>();
            for (RoleBrandMapping rbm : roleBrandMappings) {
                if (rbm.getRoleId().equals(role.getId())) {
                    mappedBrands.add(rbm.getBrandId());
                }
            }
            IdCodeName code = new IdCodeName();
            code.setId(role.getId());
            code.setCode(role.getName());
            code.setStatus(role.getStatus());
            code.setName(role.getDescription());
            // Sending mapped brands to the role as string in type field
            code.setType(mappedBrands.stream().map(String :: valueOf).collect(Collectors.joining(", ")));
            all.add(code);
        }
        return all;
    }

    @Override
    public List<IdCodeName> getActiveRoles(int employeeId) {

        List<IdCodeName> all = new ArrayList<>();
        Query query = manager.createQuery("FROM EmployeeRoleMapping E where E.mappingStatus = :mappingStatus and E.employeeId = :employeeId and E.roleDetail.status = :mappingStatus");
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name());
        query.setParameter("employeeId", employeeId);
        List<EmployeeRoleMapping> roles = query.getResultList();
        for (EmployeeRoleMapping role : roles) {
            IdCodeName code = new IdCodeName();
            Boolean found = Boolean.FALSE;
            if (all.isEmpty()) {
                code.setId(role.getRoleDetail().getId());
                code.setCode(role.getRoleDetail().getName());
                code.setStatus(role.getRoleDetail().getStatus());
                code.setName(role.getRoleDetail().getDescription());
                // Sending employee mapped brand as string in type field
                code.setType(role.getBrandId().toString());
                all.add(code);
            } else {
                for (IdCodeName obj : all) {
                    if (role.getRoleDetail().getId() == (obj.getId())) {
                        found = Boolean.TRUE;
                        obj.setType(obj.getType().concat(", " + role.getBrandId().toString()));
                    } else {
                        code.setId(role.getRoleDetail().getId());
                        code.setCode(role.getRoleDetail().getName());
                        code.setStatus(role.getRoleDetail().getStatus());
                        code.setName(role.getRoleDetail().getDescription());
                        // Sending employee mapped brand as string in type field
                        code.setType(role.getBrandId().toString());
                    }
                }
                if (!found) {
                    all.add(code);
                }
            }

        }
        return all;
    }

    @Override
    public Employee getEmployeeBySdpContact(String sdpContact) {
        Query query = manager.createQuery("FROM EmployeeDetail WHERE sdpContact= :sdpContact");
        query.setParameter("sdpContact", sdpContact);
        EmployeeDetail e = null;
        try {
            e = (EmployeeDetail) query.getSingleResult();
        } catch (NoResultException nre) {
            return null;
        }
        return MasterDataConverter.convert(e);
    }


    public Boolean updateEmployeeAppMapping(EmployeeApplicationMapping employeeMapping) {
        try {
            Map<String, List<Pair<String, String>>> empMapping = getEmpAppMapping(employeeMapping.getEmployeeId());
            Query changeAllStatus = manager.createQuery("UPDATE EmployeeApplicationMappingDetail emp SET emp.recordStatus='IN_ACTIVE' WHERE emp.employeeId=:empId ");
            changeAllStatus.setParameter("empId", employeeMapping.getEmployeeId());
            changeAllStatus.executeUpdate();
            manager.flush();
            List<String> toChange = new ArrayList<>();
            List<String> toAdd = new ArrayList<>();
            if (empMapping.containsKey(employeeMapping.getMappingType())) {
                List<Pair<String, String>> dataBack = empMapping.get(employeeMapping.getMappingType());
                for (String value : employeeMapping.getMappingValue()) {
                    boolean flag = true;
                    for (Pair<String, String> pairBack : dataBack) {
                        if (pairBack.getKey().equals(value)) {
                            toChange.add(value);
                            flag = false;
                        }
                    }
                    if (flag) {
                        toAdd.add(value);
                    }
                }
            } else {
                List<EmployeeApplicationMappingDetail> emp = new ArrayList<>();
                for (String mappingValue : employeeMapping.getMappingValue()) {
                    EmployeeApplicationMappingDetail mappingDetail = new EmployeeApplicationMappingDetail();
                    mappingDetail.setEmployeeId(employeeMapping.getEmployeeId());
                    mappingDetail.setMappingValue(mappingValue);
                    mappingDetail.setMappingType(employeeMapping.getMappingType());
                    mappingDetail.setRecordStatus(employeeMapping.getRecordStatus());
                    emp.add(mappingDetail);
                }
                addAll(emp);
            }
            if (!toAdd.isEmpty()) {
                List<EmployeeApplicationMappingDetail> emp = new ArrayList<>();
                for (String mappingValue : toAdd) {
                    EmployeeApplicationMappingDetail mappingDetail = new EmployeeApplicationMappingDetail();
                    mappingDetail.setEmployeeId(employeeMapping.getEmployeeId());
                    mappingDetail.setMappingValue(mappingValue);
                    mappingDetail.setMappingType(employeeMapping.getMappingType());
                    mappingDetail.setRecordStatus(employeeMapping.getRecordStatus());
                    emp.add(mappingDetail);
                }
                addAll(emp);
            }
            if (!toChange.isEmpty()) {
                for (String value : toChange) {
                    Query update = manager.createQuery("UPDATE  EmployeeApplicationMappingDetail emp SET emp.recordStatus=:recordStatus WHERE emp.mappingValue=:value");
                    update.setParameter("value", value);
                    update.setParameter("recordStatus", employeeMapping.getRecordStatus());
                    update.executeUpdate();
                }
            }
        } catch (Exception e) {
            LOG.error("Error while updating Employee Application Mapping for mapping id: {}", employeeMapping.getMappingId(), e);
            return false;
        }
        return true;
    }

    public Map<String, List<Pair<String, String>>> getEmpAppMapping(int userId) {
        try {
            Query userData = manager.createQuery(" FROM EmployeeApplicationMappingDetail emp WHERE emp.employeeId=:empId ");
            userData.setParameter("empId", userId);
            List<EmployeeApplicationMappingDetail> userAllData = userData.getResultList();
            Map<String, List<Pair<String, String>>> mappings = new HashMap<>();
            for (EmployeeApplicationMappingDetail data : userAllData) {
                if (mappings.containsKey(data.getMappingType())) {
                    Pair<String, String> pair = new Pair<>(data.getMappingValue(), data.getRecordStatus());
                    mappings.get(data.getMappingType()).add(pair);
                } else {
                    List<Pair<String, String>> pair = new ArrayList<>();
                    pair.add(new Pair<>(data.getMappingValue(), data.getRecordStatus()));
                    mappings.put(data.getMappingType(), pair);
                }
            }
            return mappings;
        } catch (NoResultException e) {
            LOG.info("Employee Mapping detail not fetched from database");
        }
        return null;
    }

    public Boolean verifyUserForCancellation(Integer empId, String passcode){
        try {
            Query userVerification = manager.createQuery("SELECT ed.empId , epc.empPassCode FROM EmployeePassCode epc INNER JOIN EmployeeDetail ed ON ed.empId = epc.employeeDetail.empId WHERE epc.employeeDetail.empId = :employeeId ");
            userVerification.setParameter("employeeId",empId);
            Object[] empPasscodeDetail = (Object[]) userVerification.getSingleResult();
            if(Objects.nonNull(empPasscodeDetail)) {
                if (PasswordImpl.decrypt((String) empPasscodeDetail[1]).equals(passcode)) {
                    return true;
                }
            }
        }catch (NoResultException e){
            LOG.error("No data found for emp id : {}",empId);
        }
        catch(Exception e){
            LOG.error("Error in verifying empId : {} ",empId,e);
        }
        return false;
    }


    public String getLastActivityTime(String system1 , String system2){
        try{
            Query data =  manager.createQuery("SELECT sysStat.lastActivityTime FROM SystemStatusMapping sysStat WHERE sysStat.system1=:system1 AND sysStat.system2=:system2 AND sysStat.systemStatus='ACTIVE'");
            data.setParameter("system1",system1);
            data.setParameter("system2",system2);
            return (String) data.getSingleResult();
        }catch(NoResultException e){
            LOG.error("System Status Mapping not found :",e);
        }
        return null;
    }

    private Boolean updateLastActivityTime(String system1, String system2) {
        try {
            Query data = manager.createQuery("update SystemStatusMapping sysStat SET sysStat.systemStatus =: status WHERE sysStat.system1=:system1 AND sysStat.system2=:system2 ");
            data.setParameter("system1", system1);
            data.setParameter("system2", system2);
            data.setParameter("status", SystemStatus.IN_ACTIVE.value());
            data.executeUpdate();
            return true;
        } catch (Error e) {
            LOG.error("Error while updating last Activity time in system status mapping", e);
        }
        return false;
    }

    public Boolean addLastActivityTime(String system1, String system2, String lastUpdateTime){
        try{
            LOG.info("Adding lastActivityTime in system status mapping");
            updateLastActivityTime(system1,system2);
            SystemStatusMapping systemStatusMapping = new SystemStatusMapping(system1,system2, SystemStatus.ACTIVE.value(),lastUpdateTime);
            manager.persist(systemStatusMapping);
            return true;
        }catch(Error e){
            LOG.error("Error while updating last Activity time in system status mapping",e);
        }

        return false;
    }


//    public Map<String, Integer> lookUpDepartmentDesignation(String department, String designation){
//
//
////        Integer designationId = cache.getDesignationId(designation);
//
//        if (deptId != 0 && designationId != 0) {
//            try {
//                Query query = manager.createQuery("SELECT DDM.deptId,DDM.designationId from DepartmentDesignationMapping DDM WHERE DDM.deptId=:deptId AND DDM.designationId=:designationId");
//                query.setParameter("designationId", designationId);
//                query.setParameter("deptId", deptId);
//                List<Object[]> list = query.getResultList();
//                if(list != null && list.size() > 0) {
//                    for(Object[] ele : list) {
//                        departmentDesignationId.put("departmentId" , (Integer)ele[0] );
//                        departmentDesignationId.put("designationId", (Integer)ele[1] );
//                    }
//                }else{
//                    return null;
//                }
//            } catch (Exception e) {
//                LOG.error("Error in getting department designation mapping");
//                return null;
//            }
//        } else {
//            departmentDesignationId.put("departmentId" , 0);
//            departmentDesignationId.put("designationId",0);
//        }
//        return departmentDesignationId;
//    }

    public Boolean addDepartmentDesignationMapping(Integer deptId,Integer designationId){
        try{
            DepartmentDesignationMapping departmentDesignationMapping = new DepartmentDesignationMapping(deptId,designationId,SystemStatus.ACTIVE.value(),false);
            manager.persist(departmentDesignationMapping);
            return true;
        }catch (Exception e){
            LOG.error("Error while adding department designation mapping",e);
        }
        return false;
    }

    public Employee updateEmpData(Employee employee,Department department,Designation designation){
        try{
            return updateEmployeeForLucid(employee,department,designation);
        }catch(Exception e){
            LOG.error("Exception in updating employee detail:{}",e);
            return null;
        }
    }

    @Override
    public BusinessDivision getBusinessDivision(Integer businessDivId) {
        try{
            return manager.find(BusinessDivision.class,businessDivId);
        }catch (Exception e){
            LOG.error("Error in getting Busoiness division for id : {}" , businessDivId,e);
        }
        return null;
    }

    @Override
    public boolean getActiveEmployeeSessionDetail(Date creationTime) {
        try {
            List<EmployeeSessionDetails> activeSessionDetailList = getActiveEmployeeSession(creationTime);
            if (Objects.nonNull(activeSessionDetailList)) {
                List<Integer> empIds = activeSessionDetailList.stream().map(x -> x.getEmployeeId())
                        .collect(Collectors.toList());
                Map<Integer, Map<String, Integer>> permissionsMap = getEmployeePermissions(empIds);
                for (EmployeeSessionDetails employee : activeSessionDetailList) {
                    Map<String, Integer> permissionMap = permissionsMap.get(employee.getEmployeeId());
                    if (permissionMap != null && Objects.nonNull(employee.getModuleName())
                            && aclCache.addPermissions(employee.getSessionId(), permissionMap)) {

                        String unitName = employee.getUnitId() > 0 ? cache.getUnitBasicDetail(employee.getUnitId()).getName() : null;
                        sessionCache.addToCache(props.getEnvironmentType(), employee.getSessionId(),
                                employee.getUnitId(), unitName, employee.getEmployeeId(),
                                cache.getEmployee(employee.getEmployeeId()), employee.getCreationTime(),
                                ScreenType.valueOf(employee.getModuleName()));
                    }
                }
                return true;
            }
        } catch (Exception e) {
            LOG.error("Error saving Session Details of Active Employee ",e);
        }
        return false;
    }

    public List<EmployeeSessionDetails> getActiveEmployeeSession(Date creationTime) {
        Query query = manager.createQuery("FROM EmployeeSessionDetails E where E.creationTime >= (:creationTime) AND E.logoutTime IS NULL AND E.applicationName IN(:applicationName)");
        query.setParameter("creationTime", creationTime);
        query.setParameter("applicationName",AppConstants.ACTIVE_EMPLOYEE_APPLICATIONS);
        return query.getResultList();
    }

    @Override
    public void inValidateSessionCache(List<Integer> unitId) {
        Query query = manager.createQuery("FROM EmployeeSessionDetails where unitId in (:unitId) and logoutTime = NULL and creationTime>= :creationTime");
        query.setParameter("unitId",unitId);
        query.setParameter("creationTime",AppUtils.getDateAfterDays(AppUtils.getBusinessDate(),-1));
        List<EmployeeSessionDetails> employeeSessionDetails = query.getResultList();
        for(EmployeeSessionDetails esd : employeeSessionDetails){
            esd.setLogoutTime(AppUtils.getCurrentTimestamp());
            manager.persist(esd);
            sessionCache.removeFromCache(esd.getEmployeeId(), esd.getSessionId());
        }
    }

    @Override
    public void setEmployeeInactive(String empCode,String empContact){
        LOG.info("Getting Employee Detail for employee code :{}",empCode);
        Query query = manager.createQuery("FROM EmployeeDetail emp where emp.employeeCode = : empCode " );
        query.setParameter("empCode", empCode);
        if(query.getMaxResults()==0){
            query = manager.createQuery("FROM EmployeeDetail emp where emp.empContactNum1 = : empContact" );
            query.setParameter("empContact", empContact);
        }
        if (query.getMaxResults()==0) {
           return;
        } else {
            LOG.info("Setting employee inactive");
             Query updateQuery = manager.createQuery("UPDATE EmployeeDetail E SET E.employmentStatus=:status WHERE E.employeeCode=:empId");
             updateQuery.setParameter("status",EmploymentStatus.IN_ACTIVE.value());
             updateQuery.setParameter("empId",empCode);
             updateQuery.executeUpdate();
            return;
        }
    }

    @Override
    public List<Object[]> getRoleActionMappingsForRoles(Set<Integer> roleIds) {
        try {
            Query query = manager.createNativeQuery("SELECT usr.ROLE_ID,usr.ROLE_NAME,usr.ROLE_DESCRIPTION,usr.APPLICATION_ID,ad.ACTION_DETAIL_ID," +
                    "ad.ACTION_CODE,ad.ACTION_TYPE,ad.ACTION_CATEGORY,ad.ACTION_DESCRIPTION,apd.APPLICATION_NAME " +
                    "FROM ROLE_ACTION_MAPPING ram INNER JOIN USER_ROLE_DATA usr ON ram.ROLE_ID = usr.ROLE_ID " +
                    "INNER JOIN ACTION_DETAIL ad ON ram.ACTION_DETAIL_ID = ad.ACTION_DETAIL_ID " +
                    "INNER JOIN APPLICATION_DATA apd ON usr.APPLICATION_ID = apd.APPLICATION_ID " +
                    "WHERE usr.ROLE_ID IN (:roleIds) AND ram.MAPPING_STATUS = :status AND usr.ROLE_STATUS = :status AND ad.ACTION_STATUS  = :status AND apd.APPLICATION_STATUS = :status");
            query.setParameter("status",AppConstants.ACTIVE);
            query.setParameter("roleIds",roleIds);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error getting role action mapping :: ",e);
        }
        return new ArrayList<>();
    }

    @Override
    public Boolean updateEmployeeRoleBrandMappings(EmployeeRoleUpdateRequest request) {
        try {
            List<IdCodeName> roleMappings = request.getMappings();
            if (Objects.isNull(roleMappings) || roleMappings.isEmpty()) {
                return Boolean.TRUE;
            }

            List<Integer> roleIds = roleMappings.stream().map(IdCodeName::getId).collect(Collectors.toList());
            Map<String, EmployeeRoleMapping> existingMappings = new HashMap<>();
            Query query = manager.createQuery("SELECT erm FROM EmployeeRoleMapping erm "
                            + "WHERE erm.employeeId = :empId "
                            + "AND erm.roleDetail.id IN :roleIds");
            query.setParameter("empId", request.getEmployeeId());
            query.setParameter("roleIds", roleIds);
            List<EmployeeRoleMapping> currentMappings = query.getResultList();

            for (EmployeeRoleMapping mapping : currentMappings) {
                // Creating Composite Ket to ensure no data overriding
                String key = mapping.getRoleDetail().getId() + "_" + mapping.getBrandId();
                existingMappings.put(key, mapping);
            }

            for (IdCodeName roleReq : roleMappings) {
                List<Integer> brandIds = new ArrayList<>();
                if (Objects.nonNull(roleReq.getType()) && !roleReq.getType().trim().isEmpty()) {
                    brandIds = Arrays.stream(roleReq.getType().split(",")).map(String::trim)
                            .filter(s -> !s.isEmpty()).map(Integer::parseInt).collect(Collectors.toList());
                }
                if (AppConstants.IN_ACTIVE.equals(roleReq.getStatus())) {
                    Query deactQuery = manager.createQuery("UPDATE EmployeeRoleMapping erm "
                                    + "SET erm.mappingStatus = :mappingStatus, "
                                    + "erm.updatedBy = :updatedBy, "
                                    + "erm.lastUpdateTime = :lastUpdateTime "
                                    + "WHERE erm.employeeId = :empId "
                                    + "AND erm.roleDetail.id = :roleId "
                                    + "AND erm.mappingStatus <> :mappingStatus");
                    deactQuery.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
                    deactQuery.setParameter("updatedBy", request.getUpdatedBy());
                    deactQuery.setParameter("lastUpdateTime", AppUtils.getCurrentTimestamp());
                    deactQuery.setParameter("empId", request.getEmployeeId());
                    deactQuery.setParameter("roleId", roleReq.getId());
                    deactQuery.executeUpdate();
                } else {
                    for (Integer brandId : brandIds) {
                        String key = roleReq.getId() + "_" + brandId;
                        if (!existingMappings.containsKey(key)) {
                            EmployeeRoleMapping newMapping = new EmployeeRoleMapping();
                            newMapping.setEmployeeId(request.getEmployeeId());
                            newMapping.setRoleDetail(manager.find(UserRoleData.class, roleReq.getId()));
                            newMapping.setMappingStatus(AppConstants.ACTIVE);
                            newMapping.setUpdatedBy(request.getUpdatedBy());
                            newMapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                            newMapping.setBrandId(brandId);
                            manager.persist(newMapping);
                            existingMappings.put(key, newMapping);
                        }
                    }
                    updateSpecificEmployeeRoleBrandMappings(request, roleReq, brandIds, Boolean.TRUE);
                    updateSpecificEmployeeRoleBrandMappings(request, roleReq, brandIds, Boolean.FALSE);
                }
            }
            manager.flush();
        } catch (Exception e) {
            LOG.error("Error occurred while updating Employee Mapping", e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void updateSpecificEmployeeRoleBrandMappings(EmployeeRoleUpdateRequest request, IdCodeName mapping,
                                                         List<Integer> brandIds, Boolean isActivated)
            throws DataNotFoundException, DataUpdationException {
        String targetStatus = isActivated ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE;
        String queryString;

        if (Objects.isNull(brandIds) || brandIds.isEmpty()) {
            queryString = "UPDATE EmployeeRoleMapping erm "
                    + "SET erm.mappingStatus = :mappingStatus, "
                    + "erm.updatedBy = :updatedBy, "
                    + "erm.lastUpdateTime = :lastUpdateTime "
                    + "WHERE erm.employeeId = :empId "
                    + "AND erm.roleDetail.id = :roleId";
        } else {
            if (isActivated) {
                queryString = "UPDATE EmployeeRoleMapping erm "
                        + "SET erm.mappingStatus = :mappingStatus, "
                        + "erm.updatedBy = :updatedBy, "
                        + "erm.lastUpdateTime = :lastUpdateTime "
                        + "WHERE erm.employeeId = :empId "
                        + "AND erm.roleDetail.id = :roleId "
                        + "AND erm.brandId IN :brandIds "
                        + "AND erm.mappingStatus <> :mappingStatus";
            } else {
                queryString = "UPDATE EmployeeRoleMapping erm "
                        + "SET erm.mappingStatus = :mappingStatus, "
                        + "erm.updatedBy = :updatedBy, "
                        + "erm.lastUpdateTime = :lastUpdateTime "
                        + "WHERE erm.employeeId = :empId "
                        + "AND erm.roleDetail.id = :roleId "
                        + "AND erm.brandId NOT IN :brandIds "
                        + "AND erm.mappingStatus <> :mappingStatus";
            }
        }

        Query query = manager.createQuery(queryString);
        query.setParameter("mappingStatus", targetStatus);
        query.setParameter("updatedBy", request.getUpdatedBy());
        query.setParameter("lastUpdateTime", AppUtils.getCurrentTimestamp());
        query.setParameter("empId", request.getEmployeeId());
        query.setParameter("roleId", mapping.getId());
        if (Objects.nonNull(brandIds) && !brandIds.isEmpty()) {
            query.setParameter("brandIds", brandIds);
        }
        query.executeUpdate();
        manager.flush();
    }


    @Override
    public Employee getEmployeeByContact(String contact) {
        try {
            Query query = manager.createQuery("FROM EmployeeDetail WHERE empContactNum1 = :contact");
            query.setParameter("contact", contact);
            @SuppressWarnings("unchecked")
            List<EmployeeDetail> employees = query.getResultList();
            
            if (employees != null && !employees.isEmpty()) {
                EmployeeDetail employeeDetail = employees.get(0);
                return MasterDataConverter.convert(employeeDetail);
            }
            return null;
        } catch (Exception e) {
            LOG.error("Error getting employee by contact: {}", contact, e);
            return null;
        }
    }

    @Override
    public List<Employee> getEmployeesWithoutPasscodes(List<Integer> employeeIds) {
        String queryString = "SELECT ed FROM EmployeeDetail ed " +
                "WHERE (ed.employmentStatus = :employmentStatus " +
                "AND ed.employeeEmail IS NOT NULL " +
                "AND ed.employeeEmail != '' " +
                "AND ed.department.deptId != :deptId " +
                "AND NOT EXISTS (SELECT 1 FROM EmployeePassCode epc WHERE epc.employeeDetail.empId = ed.empId)) " +
                "OR (:applyEmployeeIds = false OR ed.empId IN :employeeIds ) " +
                "ORDER BY ed.empId";

        Query query = manager.createQuery(queryString);
        query.setParameter("employmentStatus", EmploymentStatus.ACTIVE.name());
        query.setParameter("applyEmployeeIds", employeeIds != null && !employeeIds.isEmpty());
        query.setParameter("deptId", 101); // Filter out employees from department ID 101
        query.setParameter("employeeIds", employeeIds);

        @SuppressWarnings("unchecked")
        List<EmployeeDetail> employeeDetails = query.getResultList();
        
        // Convert EmployeeDetail to Employee
        List<Employee> employees = new ArrayList<>();
        for (EmployeeDetail employeeDetail : employeeDetails) {
            employees.add(MasterDataConverter.convert(employeeDetail));
        }
        
        return employees;
    }


}
