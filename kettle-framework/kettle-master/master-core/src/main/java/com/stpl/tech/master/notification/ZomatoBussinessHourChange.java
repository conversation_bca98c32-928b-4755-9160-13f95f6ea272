package com.stpl.tech.master.notification;

import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Objects;

public class ZomatoBussinessHourChange extends EmailNotification {

    private List<UnitHours> request;
    private EnvType envType;
    private String response;
    private List<String> toEmails;
    private String placeHolder;
    private String unitName;
    private String partnerName;
    private Integer brandId;
    private ZomatoBusinessHourChangeEmailTemplate template1;

    private ZomatoBusinessHourChangeOnUpdateEmailTemplate template2;


    public ZomatoBussinessHourChange(List<UnitHours> request, String response, String unitName, String partnerName, Integer brandId, List<String> toEmails,
                                     String placeHolder, EnvType envType, ZomatoBusinessHourChangeEmailTemplate template1,ZomatoBusinessHourChangeOnUpdateEmailTemplate template2){
        this.request = request;
        this.response = response;
        this.unitName = unitName;
        this.partnerName = partnerName;
        this.brandId = brandId;
        this.toEmails = toEmails;
        this.placeHolder = placeHolder;
        this.envType = envType;
        this.template1 = template1;
        this.template2 = template2;

    }
    @Override
    public String[] getToEmails() {
        if (AppUtils.isDev(envType)) {
            return new String[]{"<EMAIL>"};
        } else {
            if (Objects.nonNull(toEmails) && toEmails.size() > 0) {
                toEmails.add("<EMAIL>");
            }
            return this.toEmails.toArray(new String[0]);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = placeHolder + " has Been Enabled for Unit " + unitName  + " on " +
                AppUtils.getTimeISTString(AppUtils.getCurrentTimestamp());
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            if(Objects.nonNull(template1)) {
                return template1.getContent();
            }
            else{
                return template2.getContent();
            }
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
