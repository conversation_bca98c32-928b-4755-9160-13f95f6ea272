package com.stpl.tech.master.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.stpl.tech.master.data.dao.MasterMetadataDao;
import com.stpl.tech.master.data.model.GroupRecommendationMapping;
import com.stpl.tech.master.data.model.MenuSequenceGroupMapping;
import com.stpl.tech.master.data.model.MenuSequenceTimingData;
import com.stpl.tech.master.data.model.UnitGroupMappingData;
import com.stpl.tech.master.domain.model.MenuSequenceTiming;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.domain.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.stpl.tech.master.core.CategoryTag;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.partner.dao.ChannelPartnerDao;
import com.stpl.tech.master.core.service.ChannelPartnerService;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.ChannelPartner;
import com.stpl.tech.master.data.model.ChannelPartnerCommission;
import com.stpl.tech.master.data.model.MenuRecommendationData;
import com.stpl.tech.master.data.model.MenuRecommendationMappingData;
import com.stpl.tech.master.data.model.MenuSequenceData;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.ProductGroupImageData;
import com.stpl.tech.master.data.model.ProductSequenceData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MenuRecommendation;
import com.stpl.tech.master.domain.model.MenuRecommendationMapping;
import com.stpl.tech.master.domain.model.MenuSequence;
import com.stpl.tech.master.domain.model.MenuSequenceRequestVO;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.PriceProfileDetail;
import com.stpl.tech.master.domain.model.ProductGroup;
import com.stpl.tech.master.domain.model.ProductGroupImage;
import com.stpl.tech.master.domain.model.ProductGroupSequence;
import com.stpl.tech.master.domain.model.ProductSequence;
import com.stpl.tech.master.domain.model.UnitChannelPartnerMapping;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitPartnerMenuMapping;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.spring.exception.FileArchiveServiceException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Service
public class ChannelPartnerServiceImpl implements ChannelPartnerService {

    @Autowired
    private ChannelPartnerDao dao;

    @Autowired
    private MasterDataCacheService masterDataCacheService;

    @Autowired
    private MasterDataCache masterDataCache;
    @Autowired
    private FileArchiveService fileArchiveService;
    @Autowired
    private MasterProperties props;


    @Autowired
    private MasterMetadataDao masterMetadataDao;
    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerServiceImpl.class);

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> getAllChannelPartners(Date businessDate) {
        List<IdCodeName> list = new ArrayList<>();
        dao.findAll(ChannelPartner.class).forEach(p -> {
            ChannelPartnerCommission commission = null;
            try {
                commission = dao.getChannelPartnerCommission(p.getPartnerId(), businessDate);
            } catch (DataNotFoundException e) {
            }
            list.add(MasterDataConverter.convert(p, commission));
        });
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public IdCodeName updateChannelPartner(ChannelPartner channelPartner, Date businessDate)
            throws DataNotFoundException {
        IdCodeName partner = MasterDataConverter.convert(dao.update(channelPartner),
                dao.getChannelPartnerCommission(channelPartner.getPartnerId(), businessDate));
        masterDataCacheService.refreshChannelPartnerCache();
        return partner;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitChannelPartnerMappingData> getAllUnitChannelPartnerMappings() {
        return dao.findAll(UnitChannelPartnerMappingData.class);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.PosMetadataService#getAllChannelPartner ()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public UnitChannelPartnerMappingData addUnitChannelPartnerMapping(UnitChannelPartnerMapping mapping) throws DataUpdationException {
        if (mapping != null && mapping.getUnit() != null && mapping.getUnit().getId() > 0) {
            if (getUnitChannelPartnerMappingByUnitAndPartnerId(mapping.getUnit().getId(), mapping.getChannelPartner().getId()) != null) {
                throw new DataUpdationException("Unit and partner mapping already exists.");
            }
            UnitChannelPartnerMappingData data = new UnitChannelPartnerMappingData();
            data.setChannelPartnerId(mapping.getChannelPartner().getId());
            data.setDeliveryPartnerId(5);
            data.setStatus("ACTIVE");
            data.setUnitId(mapping.getUnit().getId());
            data = dao.add(data);
            return data;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public UnitChannelPartnerMappingData getUnitChannelPartnerMappingByUnitAndPartnerId(Integer unitId, Integer partnerId) {
        return dao.findMappingByUnitAndPartnerId(unitId, partnerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public UnitChannelPartnerMappingData activateUnitChannelPartnerMapping(Integer mappingId, boolean activate) {
        if (mappingId != null) {
            UnitChannelPartnerMappingData data = dao.find(UnitChannelPartnerMappingData.class, mappingId);
            if (data != null) {
                data.setStatus(activate ? "ACTIVE" : "IN_ACTIVE");
                data = dao.update(data);
                return data;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public UnitPartnerMenuMapping addUnitPartnerMenuMapping(UnitPartnerMenuMapping mapping, Boolean isBulk)
            throws DataUpdationException {
        boolean isNewMapping=true;
        if (mapping != null && mapping.getUnitChannelPartnerMappingId() != null) {
            Date time = AppUtils.getCurrentTimestamp();
            UnitChannelPartnerMappingData data = dao.find(UnitChannelPartnerMappingData.class,
                    mapping.getUnitChannelPartnerMappingId());
            if (!isBulk) {
                MenuSequenceData menuSequenceData = dao.find(MenuSequenceData.class, mapping.getMenuSequence().getId());
                if (Objects.nonNull(menuSequenceData) && !AppConstants.ACTIVE.equals(menuSequenceData.getMenuStatus())) {
                    throw new DataUpdationException("Could Not Add Menu Mapping as Menu " + menuSequenceData.getMenuSequenceName()
                            + " is " + menuSequenceData.getMenuStatus());
                }
            }
            if(data == null) {
                throw new DataUpdationException("Unit channel partner mapping id is not valid");
            }
            List<UnitChannelPartnerMenuMappingData> mappingList = dao
                    .findMenuMappingByUnitPartnerBrandMappingId(mapping.getUnitChannelPartnerMappingId(), mapping.getBrand().getId());
            if (mappingList != null && !mappingList.isEmpty()) {
                for (UnitChannelPartnerMenuMappingData unitChannelPartnerMenuMappingData : mappingList) {
                    if (mapping.getUnitChannelPartnerMappingId().equals(unitChannelPartnerMenuMappingData.getUnitPartnerMappingId()) &&
                            unitChannelPartnerMenuMappingData.getMenuSequenceId().equals(mapping.getMenuSequence().getId()) &&
                            mapping.getMenuApp().name().equals(unitChannelPartnerMenuMappingData.getMenuApp()) &&
                            mapping.getMenuType().name().equals(unitChannelPartnerMenuMappingData.getMenuType())) {
                        isNewMapping=false;
                        if (MenuType.DAY_SLOT.equals(mapping.getMenuType())) {
                            if (unitChannelPartnerMenuMappingData.getMenuDay().equals(mapping.getDayNumber())
                                    && mapping.getStartTime()
                                    .equals(unitChannelPartnerMenuMappingData.getStartTime())) {
//                                throw new DataUpdationException("Mapping already exists");
                                if(AppConstants.IN_ACTIVE.equals(unitChannelPartnerMenuMappingData.getStatus())){
                                    unitChannelPartnerMenuMappingData.setStatus(AppConstants.ACTIVE);
                                }
                            }
                        } else {
//                            throw new DataUpdationException("Mapping already exists");
                            if(AppConstants.IN_ACTIVE.equals(unitChannelPartnerMenuMappingData.getStatus())){
                                unitChannelPartnerMenuMappingData.setStatus(AppConstants.ACTIVE);
                            }
                        }
                        dao.update(unitChannelPartnerMenuMappingData);
                    }
                }
            }
            if(isNewMapping){
                UnitChannelPartnerMenuMappingData partnerMenuMappingData = new UnitChannelPartnerMenuMappingData();
                partnerMenuMappingData.setCreatedAt(time);
                partnerMenuMappingData.setCreatedBy(mapping.getCreatedBy().getId());
                if (mapping.getEndTime() != null) {
                    partnerMenuMappingData.setEndTime(mapping.getEndTime());
                }
                partnerMenuMappingData.setMenuApp(mapping.getMenuApp().name());
                if (mapping.getDayNumber() != null) {
                    partnerMenuMappingData.setMenuDay(mapping.getDayNumber());
                }
                partnerMenuMappingData.setMenuSequenceId(mapping.getMenuSequence().getId());
                partnerMenuMappingData.setMenuSequenceName(mapping.getMenuSequence().getName());
                partnerMenuMappingData.setMenuType(mapping.getMenuType().name());
                if (mapping.getStartTime() != null) {
                    partnerMenuMappingData.setStartTime(mapping.getStartTime());
                }
                partnerMenuMappingData.setStatus(UnitStatus.ACTIVE.value());
                partnerMenuMappingData.setUnitPartnerMappingId(mapping.getUnitChannelPartnerMappingId());
                partnerMenuMappingData.setUpdatedAt(time);
                partnerMenuMappingData.setBrandId(mapping.getBrand().getId());
                partnerMenuMappingData.setUpdatedBy(mapping.getUpdatedBy().getId());
                partnerMenuMappingData.setCartRecommendationSequenceId(mapping.getCartRecommendationSequenceId());
                partnerMenuMappingData.setMenuRecommendationSequenceId(mapping.getMenuRecommendationSequenceId());
                partnerMenuMappingData = dao.add(partnerMenuMappingData);
                if (partnerMenuMappingData != null) {
                    UnitChannelPartnerMappingData unitChannelPartnerMappingData = dao.find(UnitChannelPartnerMappingData.class,
                            partnerMenuMappingData.getUnitPartnerMappingId());
                    return MasterDataConverter.convert(partnerMenuMappingData, unitChannelPartnerMappingData, masterDataCache);
                } else {
                    throw new DataUpdationException("Error adding mapping into the database!");
                }
            }else{
                return null;
            }
        } else {
            throw new DataUpdationException("Request is not valid!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitChannelPartnerMenuMappingData> getAllUnitPartnerMenuMapping() {
        return dao.findAll(UnitChannelPartnerMenuMappingData.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateUnitPartnerMenuMapping(Integer mappingId, boolean activate) throws DataUpdationException {
        if (mappingId != null) {
            UnitChannelPartnerMenuMappingData data = dao.find(UnitChannelPartnerMenuMappingData.class, mappingId);
            MenuSequenceData menuSequenceData = dao.find(MenuSequenceData.class, data.getMenuSequenceId());
            if (Objects.nonNull(menuSequenceData) && !AppConstants.ACTIVE.equals(menuSequenceData.getMenuStatus())) {
                throw new DataUpdationException("Could Not Add Menu Mapping as Menu Sequence '" +
                        menuSequenceData.getMenuSequenceName() + "' is " + menuSequenceData.getMenuStatus());
            }
            if (data != null) {
                data.setStatus(activate ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE);
                data = (UnitChannelPartnerMenuMappingData) dao.update(data);
                if(activate){
                    dao.inActiveUnitChannelPartnerMenuMapping(data);
                }
                List<UnitChannelPartnerMenuMappingData> mappingData= dao.getUnitPartnerMenuMappings(data.getUnitPartnerMappingId(),data.getBrandId());
                Map<Integer, PriceProfileDetail> priceProfilesData=new HashMap<>();
                if(data.getPriceProfileId()!=null){
                    priceProfilesData = dao.getAllActivePriceProfiles(data.getPriceProfileId());
                }
                masterDataCache.loadSpecificUnitPartnerMenuMapping(mappingData,priceProfilesData);
                return data != null;
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean bulkActivateUnitPartnerMenuMapping(List<Integer> mappingIds, boolean action) {
        boolean isUpdated=true;
        for(Integer mappingId : mappingIds){
            if (mappingId != null) {
                UnitChannelPartnerMenuMappingData data = dao.find(UnitChannelPartnerMenuMappingData.class, mappingId);
                if (data != null) {
                    data.setStatus(action ? "ACTIVE" : "IN_ACTIVE");
                    data = (UnitChannelPartnerMenuMappingData) dao.update(data);
                    if(data == null){
                        isUpdated=false;
                        break;
                    };
                }
                if(action){
                    dao.inActiveUnitChannelPartnerMenuMapping(data);
                }
                List<UnitChannelPartnerMenuMappingData> mappingData= dao.getUnitPartnerMenuMappings(data.getUnitPartnerMappingId(),data.getBrandId());
                Map<Integer, PriceProfileDetail> priceProfilesData=new HashMap<>();
                if(data.getPriceProfileId()!=null){
                    priceProfilesData = dao.getAllActivePriceProfiles(data.getPriceProfileId());
                }
                masterDataCache.loadSpecificUnitPartnerMenuMapping(mappingData,priceProfilesData);
            }
            else{
                isUpdated=false;
                break;
            }

        }
        return isUpdated;
    }

    /*private UnitGroupMappingData getGroupMappingByUnitId(Integer unitId){
        List<UnitGroupMappingData> unitGroupMappingDataList = masterMetadataDao.findAll(UnitGroupMappingData.class);
        Map<Integer,UnitGroupMappingData> unitGroupMappingDataMap = new HashMap<>();
     }*/

    private Map<Integer,UnitGroupMappingData> getGroupByUnitIds(List<Integer> groupIds){
        Map<Integer,UnitGroupMappingData> groupByUnitMap  = new HashMap<>();
        masterMetadataDao.getUnitGroupMappings(groupIds).stream().filter(mapping -> AppConstants.ACTIVE.equalsIgnoreCase(mapping.getStatus())).forEach(mapping ->{
            Arrays.stream(mapping.getUnitIds().trim().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt).forEach(unitId ->{
                        if(!groupByUnitMap.containsKey(unitId)){
                            groupByUnitMap.put(unitId,mapping);
                        }else{
                            UnitGroupMappingData currentGroup = groupByUnitMap.get(unitId);
                            if(mapping.getPriority() > currentGroup.getPriority()){
                                groupByUnitMap.put(unitId,mapping);
                            }else if(mapping.getPriority() == currentGroup.getPriority() && AppUtils.isBefore(currentGroup.getCreationTime()
                                    , mapping.getCreationTime())){
                                groupByUnitMap.put(unitId,mapping);
                            }

                        }
                    });
        });
        return groupByUnitMap;
    }

    private String getKeyForGroupRecommendationMapping(Integer productId , Integer partnerId , Integer brandId , String daySlot){
        return productId + "_" + partnerId + "_"
                + brandId + daySlot;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public MenuSequence getMenuSequence(MenuSequenceRequestVO request) throws DataNotFoundException {
        if (request.getUnitId() != null && request.getPartnerId() != null) {
            IdCodeName partnerDetail = masterDataCache.getChannelPartner(request.getPartnerId());
            if (partnerDetail != null) {
                Set<Integer> groupIds = masterMetadataDao.findAll(GroupRecommendationMapping.class).stream().filter(
                        mapping -> AppConstants.ACTIVE.equalsIgnoreCase(mapping.getMappingStatus()) && mapping.getBrandId().equals(request.getBrandId())
                        && mapping.getPartnerId().equals(request.getPartnerId()))
                        .map(mapping -> mapping.getGroupId()).collect(Collectors.toSet());
                UnitPartnerBrandKey key = new UnitPartnerBrandKey(request.getUnitId(), request.getBrandId(), request.getPartnerId());
                Map<Integer,UnitGroupMappingData> groupByUnitMap = getGroupByUnitIds(groupIds.stream().toList());
                UnitGroupMappingData unitGroupMappingData = groupByUnitMap.get(request.getUnitId());
                Map<Integer, Boolean> productRecommendationMappingMap = new HashMap<>();
                if(Objects.nonNull(unitGroupMappingData)){
                    productRecommendationMappingMap =  masterMetadataDao.getAllProductRecommendationMappingByGroup(unitGroupMappingData.
                                    getGroupId(), request.getBrandId(),request.getPartnerId(),new ArrayList<>(Arrays.asList(request.getMenuType().name()))).
                            stream().filter(mapping -> AppConstants.ACTIVE.equalsIgnoreCase(mapping.getMappingStatus())).
                            collect(Collectors.toMap(GroupRecommendationMapping::getProductId,mapping -> Boolean.TRUE));
                }

                List<UnitPartnerMenuMapping> menuMappings = masterDataCache.getUnitPartnerMenuMappingsMap().get(key);
                if (menuMappings != null) {
                    for (UnitPartnerMenuMapping unitPartnerMenuMapping : menuMappings) {
                        if (unitPartnerMenuMapping.getStatus().equalsIgnoreCase(AppConstants.ACTIVE) && unitPartnerMenuMapping.getMenuType().equals(request.getMenuType()) &&
                                MenuApp.CHANNEL_PARTNER.equals(unitPartnerMenuMapping.getMenuApp())) {
                            dao.find(MenuSequenceData.class, unitPartnerMenuMapping.getMenuSequence().getId());
                            return getMenu(unitPartnerMenuMapping.getMenuSequence().getId(), unitPartnerMenuMapping.getMenuRecommendationSequenceId(),
                                    unitPartnerMenuMapping.getPriceProfileId(),productRecommendationMappingMap);
                        }
                    }
                }
                throw new DataNotFoundException("No menu is mapped to this unit!");
            } else {
                throw new DataNotFoundException("Partner id is not valid!");
            }
        } else {
            throw new DataNotFoundException("Request is not valid!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public MenuSequence getMenu(Integer menuSequenceId, Integer menuRecommendationSequenceId,Integer priceProfileId ,
                                Map<Integer, Boolean> productRecommendationMappingMap) throws DataNotFoundException {
        MenuSequenceData menuSequenceData = dao.find(MenuSequenceData.class, menuSequenceId);
        if (menuSequenceData != null) {
            List<MenuSequenceMappingData> menuSequenceMappingDataList = dao.getAllMenuSequenceMappingByMenuSequenceId(menuSequenceData.getMenuSequenceId());
            List<Integer> productGroupIds = menuSequenceMappingDataList.stream().map(MenuSequenceMappingData::getProductGroupId).collect(Collectors.toList());
            List<MenuSequenceTimingData> menuSequenceTimingDataList = dao.getAllMenuSequenceTimingByMenuSequenceId(menuSequenceData.getMenuSequenceId());
            List<ProductSequenceData> productSequenceDataList = null;
            if (productGroupIds != null && !productGroupIds.isEmpty()) {
                productSequenceDataList = dao.getProductSequenceByGroupIds(productGroupIds);
            }
            List<ProductGroupData> productGroupDataList = null;
            if (productGroupIds != null && !productGroupIds.isEmpty()) {
                productGroupDataList = dao.getAllProductGroupByGroupIds(productGroupIds);
            }
            Map<Integer,List<MenuSequenceTimingData>> menuSequenceTimingDataMap = new HashMap<>();
            for(MenuSequenceTimingData menuSequenceTimingData : menuSequenceTimingDataList){
                if(!menuSequenceTimingDataMap.containsKey(menuSequenceTimingData.getProductGroupParentId())){
                    menuSequenceTimingDataMap.put(menuSequenceTimingData.getProductGroupParentId(),new ArrayList<>());
                }
                menuSequenceTimingDataMap.get(menuSequenceTimingData.getProductGroupParentId()).add(menuSequenceTimingData);
            }

            return MasterDataConverter.convert(menuSequenceData, menuSequenceMappingDataList, productGroupDataList, masterDataCache,
                productSequenceDataList, menuRecommendationSequenceId,priceProfileId,menuSequenceTimingDataMap,productRecommendationMappingMap);
        } else {
            throw new DataNotFoundException("Menu sequence does nor exist by sequence id: " + menuSequenceId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductGroup> createProductGroups(List<ProductGroup> request) throws DataUpdationException {
        if (request != null) {
            List<ProductGroup> productGroups = new ArrayList<>();
            for (ProductGroup group : request) {
                List<ProductGroupData> groupData = dao.getProductGroupByTagAndType(group.getGroupTag(), group.getGroupType());
                boolean valid = true;
                if (groupData != null && !groupData.isEmpty()) {
                    for (ProductGroupData data : groupData) {
                        if (valid && data.getGroupName().equalsIgnoreCase(group.getGroupName())) {
                            valid = false;
                        }
                    }
                }
                if (valid) {
                    ProductGroupData data = new ProductGroupData();
                    data.setGroupDescription(group.getGroupDescription());
                    data.setGroupName(group.getGroupName());
                    data.setGroupTag(group.getGroupTag());
                    data.setMenuType(group.getMenuType().name());
                    data.setCloneId(group.getCloneId());
                    data.setGroupType(group.getGroupType());
                    data.setCreatedBy(group.getCreatedBy().getId());
                    data.setCreationTime(AppUtils.getCurrentTimestamp());
                    data.setStatus(UnitStatus.ACTIVE.value());
                    data.setCategoryTag(group.getCategoryTag());
                    data.setMenuCategoryImage(group.getMenuCategoryImage());
                    data.setMenuCategoryDetails(group.getMenuCategoryDetails());
                    data.setMenuApp(group.getMenuApp().name());
                    if ((group.getGroupType().equals("CATEGORY")) && (group.getIcon() != null)) {
                        ProductGroupImage productGroupImage = group.getIcon();
                        data.setProductGroupImageData(new ProductGroupImageData(productGroupImage.getProductGroupImageId(),
                                productGroupImage.getIconName(), productGroupImage.getIconDescription(), productGroupImage.getIconUrl(), productGroupImage.getStatus(),
                                productGroupImage.getCreatedBy(), productGroupImage.getCreationTime()));
                    }
                    data = (ProductGroupData) dao.add(data);
                    if (group.getCloneId() != null) {
                        List<ProductSequenceData> list = dao.getAllSequencedProductInGroupForCloning(group.getCloneId());
                        for (ProductSequenceData sequenceData : list) {
                            ProductSequenceData productSequenceData = new ProductSequenceData();
                            productSequenceData.setProductGroupId(data.getGroupId());
                            productSequenceData.setCreatedBy(data.getCreatedBy());
                            productSequenceData.setCreationTime(data.getCreationTime());
                            productSequenceData.setProductId(sequenceData.getProductId());
                            productSequenceData.setProductIndex(sequenceData.getProductIndex());
                            productSequenceData.setLastUpdateTime(data.getCreationTime());
                            productSequenceData.setStatus(sequenceData.getStatus());
                            productSequenceData.setUpdatedBy(data.getCreatedBy());
                            dao.add(productSequenceData);
                        }
                    }
                    if (data != null) {
                        productGroups.add(MasterDataConverter.convert(data, (List<ProductSequenceData>) null, masterDataCache, null));
                    }
                } else {
                    throw new DataUpdationException("Group already exists.");
                }
            }
            return productGroups;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductGroup> getProductGroups() {
        List<ProductGroup> productGroups = new ArrayList<>();
        List<Integer> productGroupIds = new ArrayList<>();
        Map<Integer, ProductGroupData> mapGroupIdToProdGroupData = new HashMap<>();
        Map<Integer, Product> mapIdToProduct = new HashMap<>();
        List<Product> allProducts = masterDataCache.getAllProducts();
        Map<Integer,Product> productMap = masterDataCache.getProductDetails();
        for (ProductGroupData data : dao.findAll(ProductGroupData.class)) {
            productGroupIds.add(data.getGroupId());
            mapGroupIdToProdGroupData.put(data.getGroupId(), data);
        }
        List<ProductSequenceData> productSequenceDataList = dao.getProductSequenceByGroupIds(productGroupIds);
        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedBrands = MasterUtil.getMappedBrands();
            //allProducts = allProducts.stream().filter(b -> mappedBrands.contains(b.getBrandId())).toList();
            productSequenceDataList = productSequenceDataList.stream()
                    .filter(psd -> mappedBrands.contains(productMap.get(psd.getProductId()).getBrandId()))
                    .toList();
        }

        allProducts.forEach(product -> {
            if (!mapIdToProduct.containsKey(product.getId())) {
                mapIdToProduct.put(product.getId(), product);
            }
        });


        Map<Integer, List<ProductSequenceData>> mapGroupIdToProdSeqData = new HashMap<>();

        productSequenceDataList.forEach(productSequenceData -> {
            Integer productGroupId = productSequenceData.getProductGroupId();
            if (!mapGroupIdToProdSeqData.containsKey(productGroupId)) {
                mapGroupIdToProdSeqData.put(productGroupId, new ArrayList<>());
            }
            mapGroupIdToProdSeqData.get(productGroupId).add(productSequenceData);
        });

        productGroupIds.forEach((id) -> {
            productGroups.add(MasterDataConverter.convert
                    (mapGroupIdToProdGroupData.get(id), mapGroupIdToProdSeqData.get(id), masterDataCache, mapIdToProduct));
        });

        return productGroups;

        // Optimized this
        // for (ProductGroupData data : dao.findAll(ProductGroupData.class)) {
            // List<ProductSequenceData> productSequences = dao.getProductSequenceByGroupId(data.getGroupId());
            // productGroups.add(MasterDataConverter.convert(data, productSequences, masterDataCache));
        // }
        // return productGroups;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductGroup> getProductGroupsByFilter(String groupType, String menuAppType) {
        List<ProductGroup> productGroups = new ArrayList<>();
        Map<Integer, ProductGroupData> mapGroupIdToProductGroupData = new HashMap<>();
        List<Integer> groupIds = new ArrayList<>();

        Map<Integer, Product> productMap = new HashMap<>();
        List<Product> allProducts = masterDataCache.getAllProducts();

        allProducts.forEach(product -> {
            if (!productMap.containsKey(product.getId())) {
                productMap.put(product.getId(), product);
            }
        });

        for (ProductGroupData data : dao.findAllProductGroupDataWithFilter(groupType, menuAppType)) {
            mapGroupIdToProductGroupData.put(data.getGroupId(), data);
            groupIds.add(data.getGroupId());
        }
        List<ProductSequenceData> productSequences = dao.getProductSequenceByGroupIds(groupIds);
        Map<ProductGroupData,List<ProductSequenceData>> mapProductGroupDataToListOfProductSequenceData = new HashMap<>();

        Map<Integer, List<ProductSequenceData>> mapOfProductSequence = new HashMap<>();
        for(ProductSequenceData productSequenceData : productSequences) {
            Integer productGroupId = productSequenceData.getProductGroupId();
            if(!mapOfProductSequence.containsKey(productGroupId)) {
                mapOfProductSequence.put(productGroupId, new ArrayList<>());
            }
            mapOfProductSequence.get(productGroupId).add(productSequenceData);
        }

        for(Map.Entry<Integer, ProductGroupData> map : mapGroupIdToProductGroupData.entrySet()) {
            if(!mapProductGroupDataToListOfProductSequenceData.containsKey(map.getValue())) {
                mapProductGroupDataToListOfProductSequenceData.put(map.getValue(), new ArrayList<>());
            }

            if(mapOfProductSequence.containsKey(map.getKey())) {
                mapProductGroupDataToListOfProductSequenceData.put(map.getValue(), mapOfProductSequence.get(map.getKey()));
            }
        }

        for(Map.Entry<ProductGroupData,List<ProductSequenceData>> map : mapProductGroupDataToListOfProductSequenceData.entrySet()) {
            productGroups.add(MasterDataConverter.convert(map.getKey(), map.getValue(), masterDataCache, productMap));
        }
        return productGroups;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public ProductGroupData updateGroup(ProductGroup group) throws DataUpdationException {
        ProductGroupData data = new ProductGroupData();
        data.setGroupDescription(group.getGroupDescription());
        data.setGroupId(group.getGroupId());
        data.setGroupName(group.getGroupName());
        data.setCloneId(group.getCloneId());
        data.setGroupTag(group.getGroupTag());
        data.setGroupType(group.getGroupType());
        data.setMenuType(group.getMenuType().name());
        data.setCreatedBy(group.getCreatedBy().getId());
        data.setCreationTime(AppUtils.getCurrentTimestamp());
        data.setStatus(UnitStatus.ACTIVE.value());
        data.setMenuApp(group.getMenuApp().name());
        data.setCategoryTag(group.getCategoryTag());
        data.setMenuCategoryDetails(group.getMenuCategoryDetails());
        data.setMenuCategoryImage(group.getMenuCategoryImage());
        if ((group.getGroupType().equals("CATEGORY")) && (group.getIcon() != null)) {
            ProductGroupImage productGroupImage = group.getIcon();
            data.setProductGroupImageData(new ProductGroupImageData(productGroupImage.getProductGroupImageId(),
                    productGroupImage.getIconName(), productGroupImage.getIconDescription(), productGroupImage.getIconUrl(), productGroupImage.getStatus(),
                    productGroupImage.getCreatedBy(), productGroupImage.getCreationTime()));
        }
        return dao.update(data);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductGroup> mapProductSequence(List<ProductGroup> request) throws DataUpdationException {
        Date time = AppUtils.getCurrentTimestamp();
        for (ProductGroup productGroup : request) {
            ProductGroupData data = dao.find(ProductGroupData.class, productGroup.getGroupId());
            if (data != null) {
                Set<Integer> productSet = new HashSet<>();
                for (ProductSequence sequence : productGroup.getProductSequenceList()) {
                    IdName product = sequence.getProduct();
                    if (!productSet.contains(product.getId())) {
                        ProductSequenceData sequenceData = new ProductSequenceData();
                        sequenceData.setCreatedBy(sequence.getCreatedBy().getId());
                        sequenceData.setUpdatedBy(sequence.getUpdatedBy().getId());
                        sequenceData.setCreationTime(time);
                        sequenceData.setLastUpdateTime(time);
                        sequenceData.setProductGroupId(sequence.getProductGroup().getId());
                        sequenceData.setProductId(product.getId());
                        sequenceData.setProductIndex(sequence.getProductIndex());
                        sequenceData.setStatus(UnitStatus.ACTIVE.value());
                        sequenceData.setIsRecommended(AppUtils.setStatus(sequence.isRecommended()));
                        sequenceData = (ProductSequenceData) dao.add(sequenceData);
                        if (sequenceData == null) {
                            throw new DataUpdationException("Error creating sequence");
                        }
                    }
                }
            }
        }
        return request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductGroup> updateProductSequence(List<ProductGroup> request) throws DataUpdationException {
        Date time = AppUtils.getCurrentTimestamp();
        List<ProductGroup> updatedGroups = new ArrayList<>();
        Integer updatedBy = request.get(0).getUpdatedBy().getId();
        for (ProductGroup productGroup : request) {
            ProductGroupData data = dao.find(ProductGroupData.class, productGroup.getGroupId());
            if (data != null) {
                List<ProductSequenceData> sequenceDataList = dao.getAllProductSequenceByGroupId(data.getGroupId());
                Map<Integer, ProductSequenceData> sequenceDataMap = new HashMap<>();
                if (sequenceDataList != null) {
                    for (ProductSequenceData sequenceData : sequenceDataList) {
                        sequenceDataMap.put(sequenceData.getProductId(), sequenceData);
                    }
                }
                List<ProductSequenceData> updatedSequenceList = new ArrayList<>();
                for (ProductSequence sequence : productGroup.getProductSequenceList()) {
                    ProductSequenceData sequenceData = sequenceDataMap.get(sequence.getProduct().getId());
                    if (sequenceData != null) {
                        sequenceData.setLastUpdateTime(time);
                        sequenceData.setProductIndex(sequence.getProductIndex());
                        sequenceData.setUpdatedBy(sequence.getUpdatedBy().getId());
                        sequenceData.setStatus(UnitStatus.ACTIVE.value());
                        sequenceData.setIsRecommended(AppUtils.setStatus(sequence.isRecommended()));
                        sequenceData = (ProductSequenceData) dao.update(sequenceData);
                        if (sequenceData == null) {
                            throw new DataUpdationException("Error updating sequence");
                        }
                    } else {
                        sequenceData = new ProductSequenceData();
                        sequenceData.setUpdatedBy(sequence.getUpdatedBy().getId());
                        sequenceData.setStatus(UnitStatus.ACTIVE.value());
                        sequenceData.setProductIndex(sequence.getProductIndex());
                        sequenceData.setProductId(sequence.getProduct().getId());
                        sequenceData.setProductGroupId(data.getGroupId());
                        sequenceData.setLastUpdateTime(time);
                        sequenceData.setCreationTime(time);
                        sequenceData.setIsRecommended(AppUtils.setStatus(sequence.isRecommended()));
                        sequenceData.setCreatedBy(sequence.getCreatedBy().getId());
                        sequenceData = (ProductSequenceData) dao.add(sequenceData);
                        if (sequenceData == null) {
                            throw new DataUpdationException("Error adding sequence");
                        }
                    }
                    updatedSequenceList.add(sequenceData);
                }
                Map<Integer, ProductSequence> sequenceMap = new HashMap<>();
                for (ProductSequence productSequence : productGroup.getProductSequenceList()) {
                    sequenceMap.put(productSequence.getProduct().getId(), productSequence);
                }
                for (ProductSequenceData sequenceData : sequenceDataMap.values()) {
                    if (!sequenceMap.containsKey(sequenceData.getProductId())) {
                        sequenceData.setStatus(UnitStatus.IN_ACTIVE.value());
                        sequenceData.setLastUpdateTime(time);
                        sequenceData.setUpdatedBy(updatedBy);
                        sequenceData = (ProductSequenceData) dao.update(sequenceData);
                        if (sequenceData == null) {
                            throw new DataUpdationException("Error deactivating sequence");
                        }
                    }
                }
                updatedGroups.add(MasterDataConverter.convert(data, updatedSequenceList, masterDataCache, null));
            }
        }
        return updatedGroups;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<MenuSequence> getMenus() throws DataUpdationException {
        List<MenuSequence> menuSequences = new ArrayList<>();
        List<MenuSequenceData> menuSequenceDataList = dao.findAll(MenuSequenceData.class);
        if (menuSequenceDataList != null && !menuSequenceDataList.isEmpty()) {
            for (MenuSequenceData menuSequenceData : menuSequenceDataList) {
                List<MenuSequenceMappingData> menuSequenceMappingDataList = dao.getAllMenuSequenceMappingByMenuSequenceId(menuSequenceData.getMenuSequenceId());
                List<Integer> productGroupIds = menuSequenceMappingDataList.stream().map(MenuSequenceMappingData::getProductGroupId).collect(Collectors.toList());
                List<MenuSequenceTimingData> menuSequenceTimingDataList = dao.getAllMenuSequenceTimingByMenuSequenceId(menuSequenceData.getMenuSequenceId());
                Map<Integer,List<MenuSequenceTimingData>> menuSequenceTimingDataMap = new HashMap<>();
                for(MenuSequenceTimingData menuSequenceTimingData : menuSequenceTimingDataList){
                    if(!menuSequenceTimingDataMap.containsKey(menuSequenceTimingData.getProductGroupParentId())){
                        menuSequenceTimingDataMap.put(menuSequenceTimingData.getProductGroupParentId(),new ArrayList<>());
                    }
                    menuSequenceTimingDataMap.get(menuSequenceTimingData.getProductGroupParentId()).add(menuSequenceTimingData);
                }
                List<ProductSequenceData> productSequenceDataList = null;
                if (productGroupIds != null && !productGroupIds.isEmpty()) {
                    productSequenceDataList = dao.getProductSequenceByGroupIds(productGroupIds);
                }
                List<ProductGroupData> productGroupDataList = null;
                if (productGroupIds != null && !productGroupIds.isEmpty()) {
                    productGroupDataList = dao.getAllProductGroupByGroupIds(productGroupIds);
                }
                menuSequences.add(MasterDataConverter.convert(menuSequenceData, menuSequenceMappingDataList, productGroupDataList, masterDataCache, productSequenceDataList, null,null,menuSequenceTimingDataMap,
                        new HashMap<>()));
            }
        }
        return menuSequences;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public MenuSequence getMenuForSequenceId(Integer menuSequenceId) throws DataUpdationException {
        MenuSequenceData menuSequenceData = dao.find(MenuSequenceData.class, menuSequenceId);
        List<MenuSequenceMappingData> menuSequenceMappingDataList = dao.getAllMenuSequenceMappingByMenuSequenceId(menuSequenceData.getMenuSequenceId());
        List<MenuSequenceTimingData> menuSequenceTimingDataList = dao.getAllMenuSequenceTimingByMenuSequenceId(menuSequenceData.getMenuSequenceId());
        Map<Integer,List<MenuSequenceTimingData>> menuSequenceTimingDataMap = new HashMap<>();
        for(MenuSequenceTimingData menuSequenceTimingData : menuSequenceTimingDataList){
            if(!menuSequenceTimingDataMap.containsKey(menuSequenceTimingData.getProductGroupParentId())){
                menuSequenceTimingDataMap.put(menuSequenceTimingData.getProductGroupParentId(),new ArrayList<>());
            }
            menuSequenceTimingDataMap.get(menuSequenceTimingData.getProductGroupParentId()).add(menuSequenceTimingData);
        }
        List<Integer> productGroupIds = menuSequenceMappingDataList.stream().map(MenuSequenceMappingData::getProductGroupId).collect(Collectors.toList());
        List<ProductSequenceData> productSequenceDataList = null;
        if (!productGroupIds.isEmpty()) {
            productSequenceDataList = dao.getProductSequenceByGroupIds(productGroupIds);
        }
        List<ProductGroupData> productGroupDataList = null;
        if (!productGroupIds.isEmpty()) {
            productGroupDataList = dao.getAllProductGroupByGroupIds(productGroupIds);
        }
        return MasterDataConverter.convert(menuSequenceData, menuSequenceMappingDataList, productGroupDataList, masterDataCache, productSequenceDataList, null,null,
                menuSequenceTimingDataMap,new HashMap<>());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<MenuSequence> getMenusShort(String status) throws DataUpdationException {
        List<MenuSequence> menuSequences = new ArrayList<>();
        if(AppConstants.ACTIVE.equals(status)) {
            List<MenuSequenceData> menuSequenceDataList = dao.findAllActiveMenuSequenceData();
            if (!CollectionUtils.isEmpty(menuSequenceDataList)) {
                for (MenuSequenceData menuSequenceData : menuSequenceDataList) {
                    menuSequences.add(MasterDataConverter.convert(menuSequenceData, masterDataCache, null,null));
                }
            }
            return menuSequences;
        }
        List<MenuSequenceData> menuSequenceDataList = dao.findAll(MenuSequenceData.class);
        if (menuSequenceDataList != null && !menuSequenceDataList.isEmpty()) {
            for (MenuSequenceData menuSequenceData : menuSequenceDataList) {
                menuSequences.add(MasterDataConverter.convert(menuSequenceData, masterDataCache, null,null));
            }
        }
        return menuSequences;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean changeMenuStatus(Integer menuSeqId, String status) {
        if (status.equals(AppConstants.IN_ACTIVE) || status.equals(AppConstants.ARCHIVED)) {
            List<UnitChannelPartnerMenuMappingData> unitChannelPartnerMenuMappingDataList = dao.findMenuMappingByMenuSequenceId(menuSeqId);
            if (!unitChannelPartnerMenuMappingDataList.isEmpty()) {
                return false;
            }
        }
        MenuSequenceData menuSequenceData = dao.find(MenuSequenceData.class, menuSeqId);
        menuSequenceData.setMenuStatus(status);
        menuSequenceData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
        dao.update(menuSequenceData);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public MenuSequence createMenu(MenuSequence request) throws DataUpdationException {
        Date time = AppUtils.getCurrentTimestamp();
        if (request != null && request.getMenuSequenceName() != null) {
            MenuSequenceData menuSequenceData = new MenuSequenceData();
            menuSequenceData.setCreatedBy(request.getCreatedBy().getId());
            menuSequenceData.setCreationTime(time);
            menuSequenceData.setGetMenuSequenceDescription(request.getMenuSequenceDescription());
            menuSequenceData.setLastUpdateTime(time);
            menuSequenceData.setMenuType(request.getMenuType().name());
            menuSequenceData.setCloneId(request.getCloneId());
            menuSequenceData.setMenuApp(request.getMenuApp().name());
            menuSequenceData.setMenuSequenceName(request.getMenuSequenceName());
            menuSequenceData.setMenuStatus(AppConstants.ACTIVE);
            menuSequenceData = (MenuSequenceData) dao.add(menuSequenceData);
            if (request.getCloneId() != null) {
                List<MenuSequenceMappingData> list = dao.getAllSequencedProductInMenuForCloning(request.getCloneId());
                MenuSequenceData menuToBecloned = dao.find(MenuSequenceData.class, request.getCloneId());
                for (MenuSequenceMappingData data : list) {
//                    MenuSequenceMappingData menuSequenceMappingData = new MenuSequenceMappingData();
//                    menuSequenceMappingData.setMenuSequenceId(menuSequenceData.getMenuSequenceId());
//                    menuSequenceMappingData.setProductGroupId(data.getProductGroupId());
//                    menuSequenceMappingData.setProductGroupParentId(data.getProductGroupParentId());
//                    menuSequenceMappingData.setIndex(data.getIndex());
//                    menuSequenceMappingData.setStatus(data.getStatus());
//                    menuSequenceMappingData.setCreatedBy(menuSequenceData.getCreatedBy());
//                    menuSequenceMappingData.setCreationTime(menuSequenceData.getCreationTime());
//                    menuSequenceMappingData.setLastUpdateTime(menuSequenceData.getLastUpdateTime());
//                    menuSequenceMappingData.setUpdatedBy(menuSequenceData.getCreatedBy());
//                    dao.add(menuSequenceMappingData);
                    createMenuClone(data, menuSequenceData, menuToBecloned, request);
                }
            }
            if (menuSequenceData != null) {
                request = MasterDataConverter.convert(menuSequenceData, null, null, masterDataCache, null, null,null,null,new HashMap<>());
            } else {
                throw new DataUpdationException("Error creating menu");
            }
        }
        return request;
    }

    private MenuSequenceMappingData createMenuClone(MenuSequenceMappingData data, MenuSequenceData newMenu, MenuSequenceData previousMenu, MenuSequence request) throws DataUpdationException {
        MenuSequenceMappingData menuSequenceMappingData = new MenuSequenceMappingData();
        ProductGroupData productGroupData = dao.find(ProductGroupData.class, data.getProductGroupId());
        ProductGroup cloneProductGroup = new ProductGroup();
        cloneProductGroup.setGroupName(productGroupData.getGroupName());
        cloneProductGroup.setMenuType(MenuType.valueOf(productGroupData.getMenuType()));
        cloneProductGroup.setGroupDescription(productGroupData.getGroupDescription());
        cloneProductGroup.setGroupTag(newMenu.getMenuSequenceName());
        cloneProductGroup.setGroupType(productGroupData.getGroupType());
        cloneProductGroup.setCreatedBy(request.getCreatedBy());
        cloneProductGroup.setCreationTime(newMenu.getCreationTime());
        cloneProductGroup.setMenuApp(MenuApp.valueOf(newMenu.getMenuApp()));
        cloneProductGroup.setCategoryTag(productGroupData.getCategoryTag());
        cloneProductGroup.setMenuCategoryDetails(productGroupData.getMenuCategoryDetails());
        cloneProductGroup.setMenuCategoryImage(productGroupData.getMenuCategoryImage());
        cloneProductGroup.setCloneId(productGroupData.getGroupId());
        if (productGroupData.getProductGroupImageData() != null) {
            cloneProductGroup.setIcon(MasterDataConverter.convert(productGroupData.getProductGroupImageData()));
        }
        List<ProductGroup> productGroup = createProductGroups(Arrays.asList(cloneProductGroup));

        menuSequenceMappingData.setIndex(data.getIndex());
        menuSequenceMappingData.setUpdatedBy(newMenu.getCreatedBy());
        menuSequenceMappingData.setCreatedBy(newMenu.getCreatedBy());
        menuSequenceMappingData.setCreationTime(newMenu.getCreationTime());
        menuSequenceMappingData.setLastUpdateTime(newMenu.getCreationTime());
        menuSequenceMappingData.setMenuSequenceId(newMenu.getMenuSequenceId());
        menuSequenceMappingData.setStatus(data.getStatus());
        menuSequenceMappingData.setListingType(data.getListingType());
        menuSequenceMappingData.setProductGroupId(productGroup.get(0).getGroupId());
        if (data.getProductGroupParentId() != null) {
//            System.out.println("parent id is" + data.getProductGroupParentId());

            ProductGroupData p = dao.find(ProductGroupData.class, data.getProductGroupParentId());
            List<ProductGroupData> groupData = dao.getProductGroupByNameAndType(p.getGroupName(), p.getGroupType(), newMenu.getMenuSequenceName());
            if (groupData != null) {
                menuSequenceMappingData.setProductGroupParentId(groupData.get(0).getGroupId());
            }
        }
        dao.add(menuSequenceMappingData);
        return menuSequenceMappingData;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public MenuSequence addMenuSequenceMapping(MenuSequence request) throws DataUpdationException {
        Date time = AppUtils.getCurrentTimestamp();
        MenuSequenceData menuSequenceData = dao.find(MenuSequenceData.class, request.getMenuSequenceId());
        if (menuSequenceData != null) {
            Map<Integer, ProductGroupSequence> newSequenceMap = new HashMap<>();
            Integer updatedBy = null;
            for (ProductGroupSequence productGroupSequence : request.getProductGroupSequences()) {
                if (updatedBy == null) {
                    updatedBy = productGroupSequence.getUpdatedBy().getId();
                }
                newSequenceMap.put(productGroupSequence.getGroupId(), productGroupSequence);
                for (ProductGroupSequence subSequence : productGroupSequence.getSubGroups()) {
                    newSequenceMap.put(subSequence.getGroupId(), subSequence);
                }
            }
            List<MenuSequenceMappingData> menuSequenceMappingDataList = dao.getAllMenuSequenceMappingByMenuSequenceId(menuSequenceData.getMenuSequenceId());
            List<MenuSequenceTimingData> menuSequenceTimingsList = dao.getAllMenuSequenceTimingByMenuSequenceId(menuSequenceData.getMenuSequenceId());
            List<MenuSequenceMappingData> updatedMappingDataList = new ArrayList<>();
            Map<Integer,List<MenuSequenceTimingData>> updateSequenceTiming = new HashMap<>();
            Map<Integer, MenuSequenceMappingData> oldSequenceMap = new HashMap<>();
            Map<Integer,List<MenuSequenceTimingData>> oldSequenceTiming = new HashMap<>();
            for (MenuSequenceMappingData menuSequenceMappingData : menuSequenceMappingDataList) {
                oldSequenceMap.put(menuSequenceMappingData.getProductGroupId(), menuSequenceMappingData);
            }
            for (MenuSequenceTimingData menuSequenceTimingData : menuSequenceTimingsList){
                if(!oldSequenceTiming.containsKey(menuSequenceTimingData.getProductGroupParentId())){
                    oldSequenceTiming.put(menuSequenceTimingData.getProductGroupParentId(),new ArrayList<>());
                }
                oldSequenceTiming.get(menuSequenceTimingData.getProductGroupParentId()).add(menuSequenceTimingData);
            }
            for (ProductGroupSequence productGroupSequence : request.getProductGroupSequences()) {
                if (oldSequenceMap.containsKey(productGroupSequence.getGroupId())) {
                    MenuSequenceMappingData menuSequenceMappingData = oldSequenceMap.get(productGroupSequence.getGroupId());
                    menuSequenceMappingData = updateMenuSequenceMapping(menuSequenceMappingData, productGroupSequence, null, time);
                    if (menuSequenceMappingData != null) {
                        updatedMappingDataList.add(menuSequenceMappingData);
                    }
                } else {
                    updatedMappingDataList.add(createMenuSequenceMapping(menuSequenceData, productGroupSequence, null, time));
                }
                // UPDATE TIMING OF MENU SEQUENCE
                if (oldSequenceTiming.containsKey(productGroupSequence.getGroupId())){
                    List<MenuSequenceTimingData> menuSequenceTimingData = oldSequenceTiming.get(productGroupSequence.getGroupId());
                    boolean isUpdateSuccessfully= updateMenuSequenceTiming(menuSequenceTimingData,productGroupSequence);
                    if (isUpdateSuccessfully){
                        updateSequenceTiming.put(productGroupSequence.getGroupId(),menuSequenceTimingData);
                    }
                } else{
                    List<MenuSequenceTimingData> list = createMenuSequenceTiming(menuSequenceData,productGroupSequence);
                    if(!list.isEmpty()) {
                        updateSequenceTiming.put(productGroupSequence.getGroupId(), list);
                    }
                }
                if (productGroupSequence.getSubGroups() != null && !productGroupSequence.getSubGroups().isEmpty()) {
                    for (ProductGroupSequence subSequence : productGroupSequence.getSubGroups()) {
                        subSequence.setUpdatedBy(new IdName(updatedBy, ""));
                        if (oldSequenceMap.containsKey(subSequence.getGroupId())) {
                            MenuSequenceMappingData menuSequenceMappingData = oldSequenceMap.get(subSequence.getGroupId());
                            menuSequenceMappingData = updateMenuSequenceMapping(menuSequenceMappingData, subSequence, productGroupSequence.getGroupId(), time);
                            if (menuSequenceMappingData != null) {
                                updatedMappingDataList.add(menuSequenceMappingData);
                            }
                        } else {
                            updatedMappingDataList.add(createMenuSequenceMapping(menuSequenceData, subSequence, productGroupSequence.getGroupId(), time));
                        }
                    }
                }
            }
            for (MenuSequenceMappingData menuSequenceMappingData : oldSequenceMap.values()) {
                if (!newSequenceMap.containsKey(menuSequenceMappingData.getProductGroupId())) {
                    deactivateMenuSequenceMapping(menuSequenceMappingData, updatedBy, time);
                }
            }

            if (!updatedMappingDataList.isEmpty()) {
                List<Integer> productGroupIds = updatedMappingDataList.stream().map(MenuSequenceMappingData::getProductGroupId).collect(Collectors.toList());
                List<ProductGroupData> productGroupDataList = null;
                List<ProductSequenceData> productSequenceDataList = null;
                if (productGroupIds != null && !productGroupIds.isEmpty()) {
                    productGroupDataList = dao.getAllProductGroupByGroupIds(productGroupIds);
                    productSequenceDataList = dao.getProductSequenceByGroupIds(productGroupIds);
                }
                request = MasterDataConverter.convert(menuSequenceData, updatedMappingDataList, productGroupDataList, masterDataCache, productSequenceDataList, null,null,
                        updateSequenceTiming,new HashMap<>());
            }

        }
        return request;
    }

    private MenuSequenceMappingData createMenuSequenceMapping(MenuSequenceData menuSequenceData, ProductGroupSequence productGroupSequence, Integer parentId, Date time) throws DataUpdationException {
        MenuSequenceMappingData menuSequenceMappingData = new MenuSequenceMappingData();
        menuSequenceMappingData.setCreatedBy(productGroupSequence.getCreatedBy().getId());
        menuSequenceMappingData.setCreationTime(time);
        menuSequenceMappingData.setIndex(productGroupSequence.getGroupIndex());
        menuSequenceMappingData.setLastUpdateTime(time);
        menuSequenceMappingData.setMenuSequenceId(menuSequenceData.getMenuSequenceId());
        menuSequenceMappingData.setProductGroupId(productGroupSequence.getGroupId());
        if (parentId != null) {
            menuSequenceMappingData.setProductGroupParentId(parentId);
        }
        menuSequenceMappingData.setUpdatedBy(productGroupSequence.getUpdatedBy().getId());
        menuSequenceMappingData.setLastUpdateTime(time);
        menuSequenceMappingData.setStatus(UnitStatus.ACTIVE.value());
        menuSequenceMappingData.setListingType(CategoryTag.REG.name());
        menuSequenceMappingData = (MenuSequenceMappingData) dao.add(menuSequenceMappingData);
        if (menuSequenceMappingData == null) {
            throw new DataUpdationException("Error creating menu sequence mapping data");
        }
        return menuSequenceMappingData;
    }

    private List<MenuSequenceTimingData> createMenuSequenceTiming(MenuSequenceData menuSequenceData,ProductGroupSequence productGroupSequence) throws DataUpdationException{
        List<MenuSequenceTimingData> result = new ArrayList<>();
        List<MenuSequenceTiming> menuSequenceTiming = productGroupSequence.getTimings();
        if(Objects.nonNull(menuSequenceTiming) && !menuSequenceTiming.isEmpty()){
            for(MenuSequenceTiming menuTiming : menuSequenceTiming){
                MenuSequenceTimingData menuTimingData = new MenuSequenceTimingData();
                menuTimingData.setMenuSequenceId(menuSequenceData.getMenuSequenceId());
                menuTimingData.setProductGroupParentId(productGroupSequence.getGroupId());
                menuTimingData.setService(menuTiming.getService());
                menuTimingData.setTime1From(menuTiming.getTime1From());
                menuTimingData.setTime1To(menuTiming.getTime1To());
                menuTimingData.setTime2From(menuTiming.getTime2From());
                menuTimingData.setTime2To(menuTiming.getTime2To());
                menuTimingData.setTime3From(menuTiming.getTime3From());
                menuTimingData.setTime3To(menuTiming.getTime3To());
                menuTimingData.setDayMonday(menuTiming.isDayMonday());
                menuTimingData.setDayTuesday(menuTiming.isDayTuesday());
                menuTimingData.setDayWednesday(menuTiming.isDayWednesday());
                menuTimingData.setDayThursday(menuTiming.isDayThursday());
                menuTimingData.setDayFriday(menuTiming.isDayFriday());
                menuTimingData.setDaySaturday(menuTiming.isDaySaturday());
                menuTimingData.setDaySunday(menuTiming.isDaySunday());
                menuTimingData.setStartDate(menuTiming.getStartDate());
                menuTimingData.setEndDate(menuTiming.getEndDate());
                menuTimingData.setDaySlots(menuTiming.getDaySlots());
                menuTimingData = (MenuSequenceTimingData) dao.add(menuTimingData);
                if(menuTimingData == null){
                    throw new DataUpdationException("Error updating menu sequence timing.");
                }
                result.add(menuTimingData);
            }
        }
        return result;
    }

    private MenuSequenceMappingData updateMenuSequenceMapping(MenuSequenceMappingData menuSequenceMappingData, ProductGroupSequence productGroupSequence, Integer parentId, Date time) throws DataUpdationException {
        menuSequenceMappingData.setLastUpdateTime(time);
        menuSequenceMappingData.setUpdatedBy(productGroupSequence.getUpdatedBy().getId());
        if (parentId != null) {
            menuSequenceMappingData.setProductGroupParentId(parentId);
        }
        menuSequenceMappingData.setIndex(productGroupSequence.getGroupIndex());
        menuSequenceMappingData.setStatus(UnitStatus.ACTIVE.value());
        menuSequenceMappingData = (MenuSequenceMappingData) dao.update(menuSequenceMappingData);
        if (menuSequenceMappingData == null) {
            throw new DataUpdationException("Error updating menu sequence mapping.");
        }
        return menuSequenceMappingData;
    }

    private boolean updateMenuSequenceTiming(List<MenuSequenceTimingData> menuSequenceTimingData,ProductGroupSequence productGroupSequence) throws DataUpdationException{
        List<MenuSequenceTiming> menuSequenceTiming = productGroupSequence.getTimings();
        Integer menuSequenceTimingDataId = menuSequenceTimingData.get(0).getMenuSequenceId();
        for(MenuSequenceTimingData menuTiming : menuSequenceTimingData){
            try {
                dao.delete(menuTiming);
            }catch (Exception e){
                throw new DataUpdationException("Error Removing Old menu sequence timing.");
            }
        }
        for(MenuSequenceTiming menuTiming : menuSequenceTiming){
            MenuSequenceTimingData menuTimingData = new MenuSequenceTimingData();
            menuTimingData.setMenuSequenceId(menuSequenceTimingDataId);
            menuTimingData.setProductGroupParentId(productGroupSequence.getGroupId());
            menuTimingData.setService(menuTiming.getService());
            menuTimingData.setTime1From(menuTiming.getTime1From());
            menuTimingData.setTime1To(menuTiming.getTime1To());
            menuTimingData.setTime2From(menuTiming.getTime2From());
            menuTimingData.setTime2To(menuTiming.getTime2To());
            menuTimingData.setTime3From(menuTiming.getTime3From());
            menuTimingData.setTime3To(menuTiming.getTime3To());
            menuTimingData.setDayMonday(menuTiming.isDayMonday());
            menuTimingData.setDayTuesday(menuTiming.isDayTuesday());
            menuTimingData.setDayWednesday(menuTiming.isDayWednesday());
            menuTimingData.setDayThursday(menuTiming.isDayThursday());
            menuTimingData.setDayFriday(menuTiming.isDayFriday());
            menuTimingData.setDaySaturday(menuTiming.isDaySaturday());
            menuTimingData.setDaySunday(menuTiming.isDaySunday());
            menuTimingData.setStartDate(menuTiming.getStartDate());
            menuTimingData.setEndDate(menuTiming.getEndDate());
            menuTimingData.setDaySlots(menuTiming.getDaySlots());
            menuTimingData = (MenuSequenceTimingData) dao.update(menuTimingData);
            if(menuTimingData == null){
                throw new DataUpdationException("Error updating menu sequence timing.");
            }
        }
        return true;
    }

    private MenuSequenceMappingData deactivateMenuSequenceMapping(MenuSequenceMappingData menuSequenceMappingData, Integer updatedBy, Date time) throws DataUpdationException {
        menuSequenceMappingData.setLastUpdateTime(time);
        menuSequenceMappingData.setUpdatedBy(updatedBy);
        menuSequenceMappingData.setStatus(UnitStatus.IN_ACTIVE.value());
        menuSequenceMappingData = (MenuSequenceMappingData) dao.update(menuSequenceMappingData);
        if (menuSequenceMappingData == null) {
            throw new DataUpdationException("Error deactivating menu sequence mapping.");
        }
        return menuSequenceMappingData;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public ProductGroupImageData saveIcon(MimeType mimeType, String iconName, String iconDescription, MultipartFile file, int createdBy, String hostUrl) throws DataUpdationException {
        try {
            String fileName = null;
            ProductGroupImageData productGroupImageData = new ProductGroupImageData();
            productGroupImageData.setIconName(iconName);
            productGroupImageData.setIconDescription(iconDescription);
            productGroupImageData.setStatus(AppConstants.ACTIVE);
            productGroupImageData.setCreatedBy(createdBy);
            productGroupImageData.setCreationTime(AppUtils.getCurrentTimestamp());
            fileName = AppUtils.generateTimeString(iconName) + "." + mimeType.name().toLowerCase();
            System.out.println(fileName);
            fileName = fileName.replaceAll(" ", "_").toLowerCase();
//            productGroupImageData.setIconUrl("http://d1nqp92n3q8zl7.cloudfront.net/product_image/" + fileName);
            productGroupImageData.setIconUrl(hostUrl + fileName);
            // http://d1nqp92n3q8zl7.cloudfront.net/product_image/10_special_1.jpg
            String baseDir = "master-service/product_image";
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, fileName, file, true);
            System.out.println("///////////////////////////////////////////////////////////////" + s3File);
            if (s3File != null) {
                productGroupImageData = dao.add(productGroupImageData);
                return productGroupImageData;
            }
        } catch (Exception e) {
            throw new DataUpdationException("Failed to upload image");

        }

        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public List<ProductGroupImageData> getIcons() {
        return dao.findAll(ProductGroupImageData.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public List<MenuSequenceMappingData> getGroupMapping() {
        return dao.findAll(MenuSequenceMappingData.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public List<MenuRecommendation> getRecommendation() {
        List<MenuRecommendation> list = new ArrayList<>();
        List<MenuRecommendationData> menuRecommendationData = dao.findAll(MenuRecommendationData.class);
        for (MenuRecommendationData recommendationData : menuRecommendationData) {
            List<MenuRecommendationMapping> menuRecommendationMappingList = new ArrayList<>();
            List<MenuRecommendationMappingData> mappingList = dao.getAllRecommendationMapping(recommendationData.getMenuRecommendationId(),AppConstants.ACTIVE);
            if (mappingList != null) {
                for (MenuRecommendationMappingData menuRecommendationMappingData : mappingList) {
                    List<MenuRecommendationMapping> updatedRecommendationMapping = new ArrayList<>(convert(menuRecommendationMappingData, menuRecommendationMappingList));
                    menuRecommendationMappingList.clear();
                    menuRecommendationMappingList.addAll(updatedRecommendationMapping);
                }
            }
            list.add(MasterDataConverter.convert(recommendationData, menuRecommendationMappingList));
        }
        return list;
    }

    private List<MenuRecommendationMapping> convert(MenuRecommendationMappingData mappingData, List<MenuRecommendationMapping> list) {
        Boolean flag = false;
        if(Objects.isNull(mappingData.getRecommendationTitle())) {
            mappingData.setRecommendationTitle("Best Paired With");
        }
        if (!list.isEmpty()) {
            for (MenuRecommendationMapping menuRecommendationMapping : list) {
                //check for same recommendation group -> product - recommendation group
                if (menuRecommendationMapping.getProductId().equals(mappingData.getProductId())
                        && menuRecommendationMapping.getRecommendationTitle().equals(mappingData.getRecommendationTitle())) {
                    flag = true;
                    List<MenuRecommendationMapping> recommendedProduct = new ArrayList<>();
                    MenuRecommendationMapping recommendation = new MenuRecommendationMapping();
                    recommendation.setProductId(mappingData.getRecommendedProductId());
                    recommendation.setIndex(mappingData.getIndex());
                    recommendation.setDiscountType(mappingData.getDiscountType());
                    recommendation.setDiscountAmount(mappingData.getDiscountAmount());
                    if (menuRecommendationMapping.getRecommendation() != null) {
                        menuRecommendationMapping.getRecommendation().add(recommendation);
                    } else {
                        recommendedProduct.add(recommendation);
                        menuRecommendationMapping.setRecommendation(recommendedProduct);
                    }
                    break;
                }
            }
        }
        // flow for new product - recommendation group
        if (!flag || list.isEmpty()) {
            MenuRecommendationMapping recommendation = new MenuRecommendationMapping();
            recommendation.setProductId(mappingData.getProductId());
            recommendation.setRecommendationTitle(mappingData.getRecommendationTitle());
            if (mappingData.getRecommendedProductId() != null) {
                List<MenuRecommendationMapping> recommendedProduct = new ArrayList<>();
                MenuRecommendationMapping recommendations = new MenuRecommendationMapping();
                recommendations.setProductId(mappingData.getRecommendedProductId());
                recommendations.setIndex(mappingData.getIndex());
                recommendations.setDiscountType(mappingData.getDiscountType());
                recommendations.setDiscountAmount(mappingData.getDiscountAmount());
                recommendedProduct.add(recommendations);
                recommendation.setRecommendation(recommendedProduct);
            }
            list.add(recommendation);
        }

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public MenuRecommendation createRecommendation(MenuRecommendation request) throws DataUpdationException {
        Date time = AppUtils.getCurrentTimestamp();
        MenuRecommendationData data = new MenuRecommendationData();
        data.setMenuRecommendationName(request.getMenuRecommendationName());
        data.setMenuRecommendationDescription(request.getMenuRecommendationDescription());
        data.setRecommendationType(request.getRecommendationType());
        data.setCloneId(request.getCloneId());
        data.setCreatedBy(request.getCreatedBy());
        data.setCreationTime(time);
        data.setStatus(AppConstants.ACTIVE);
        data.setLastUpdateTime(time);
        data = dao.add(data);

        if (request.getCloneId() != null) {
            createRecommendationClone(data);
        }

        request = MasterDataConverter.convert(data, new ArrayList<>());
        return request;
    }

    private void createRecommendationClone(MenuRecommendationData data) {
        List<MenuRecommendationMappingData> mappingList = dao.getAllRecommendationMapping(data.getCloneId(),AppConstants.ACTIVE);
        for (MenuRecommendationMappingData recommendedData : mappingList) {
            MenuRecommendationMappingData menuRecommendationMappingData = new MenuRecommendationMappingData();
            menuRecommendationMappingData.setMenuRecommendationId(data.getMenuRecommendationId());
            menuRecommendationMappingData.setRecommendedProductId(recommendedData.getRecommendedProductId());
            menuRecommendationMappingData.setProductId(recommendedData.getProductId());
            menuRecommendationMappingData.setIndex(recommendedData.getIndex());
            menuRecommendationMappingData.setStatus(recommendedData.getStatus());
            menuRecommendationMappingData.setRecommendedDimension(recommendedData.getRecommendedDimension());
            menuRecommendationMappingData.setDimension(recommendedData.getDimension());
            menuRecommendationMappingData.setCreatedBy(data.getCreatedBy());
            menuRecommendationMappingData.setCreationTime(data.getCreationTime());
            menuRecommendationMappingData.setLastUpdateTime(data.getLastUpdateTime());
            menuRecommendationMappingData.setUpdatedBy(data.getCreatedBy());
            menuRecommendationMappingData.setRecommendationTitle(recommendedData.getRecommendationTitle());
            menuRecommendationMappingData = dao.add(menuRecommendationMappingData);

        }

    }

    private String getRecommendationKey(MenuRecommendationMappingData recommendationMappingData){
        return  recommendationMappingData.getProductId() + "_" + recommendationMappingData.getDimension() + "_" + recommendationMappingData.getRecommendedProductId()
                + "_" + recommendationMappingData.getRecommendedDimension();
    }

    private String getRecommendationProductKey(MenuRecommendationMapping recommendationMappingData , MenuRecommendationMapping productMappping){
        return  productMappping.getProductId() + "_" + productMappping.getDimension() + "_" + recommendationMappingData.getProductId()
                + "_" + recommendationMappingData.getDimension();
    }




    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean createRecommendationMapping(MenuRecommendation request) throws DataUpdationException {
        MenuRecommendationData menuRecommendationData = dao.find(MenuRecommendationData.class, request.getMenuRecommendationId());
        if (menuRecommendationData != null) {
            List<MenuRecommendationMappingData> oldMappingList = dao.getAllRecommendationMapping(menuRecommendationData.getMenuRecommendationId(),null);
            Map<String , MenuRecommendationMappingData> menuRecommendationMappingByProduct = oldMappingList.stream().collect(
                    Collectors.toMap(this::getRecommendationKey, Function.identity()));
            /*for (MenuRecommendationMappingData mappingData : oldMappingList) {
                dao.delete(mappingData);
            }*/

            for (MenuRecommendationMapping product : request.getRecommendationProduct()) { //it is the individual product
                for (MenuRecommendationMapping productMapping : product.getRecommendation()) { //these are recommended products for above product
                    String mappingKey = getRecommendationProductKey(productMapping,product);
                    MenuRecommendationMappingData mapping;
                    if(menuRecommendationMappingByProduct.containsKey(mappingKey)){
                        mapping = menuRecommendationMappingByProduct.get(mappingKey);
                        menuRecommendationMappingByProduct.remove(mappingKey);
                    }else{
                        mapping  = new MenuRecommendationMappingData();
                    }
                    mapping.setMenuRecommendationId(menuRecommendationData.getMenuRecommendationId());
                    //product is simple product in recommendation
                    mapping.setProductId(product. getProductId());
                    mapping.setDimension(product.getDimension());
                    //productMapping are the recommendations for a product
                    mapping.setRecommendedProductId(productMapping.getProductId());
                    mapping.setRecommendedDimension(productMapping.getDimension());
                    mapping.setIndex(productMapping.getIndex());
                    mapping.setDiscountType("FIXED");
                    mapping.setDiscountAmount(Objects.isNull(productMapping.getDiscountAmount())? BigDecimal.ZERO:productMapping.getDiscountAmount());
                    if(Objects.isNull(product.getRecommendationTitle()) || product.getRecommendationTitle().equals("")) {
                        product.setRecommendationTitle("Best Paired With");
                    }
                    mapping.setRecommendationTitle(product.getRecommendationTitle());
                    mapping.setStatus(AppConstants.ACTIVE);
                    mapping.setCreatedBy(request.getCreatedBy());
                    mapping.setCreationTime(AppUtils.getCurrentTimestamp());
                    mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                    mapping.setUpdatedBy(request.getCreatedBy());
//                    newMappingList.add(mapping);
                    dao.add(mapping);
                }
            }
            for(String mappingKey : menuRecommendationMappingByProduct.keySet()){
                MenuRecommendationMappingData menuRecommendationMappingData = menuRecommendationMappingByProduct.get(mappingKey);
                menuRecommendationMappingData.setStatus(AppConstants.IN_ACTIVE);
                dao.update(menuRecommendationMappingData);
            }

        } else {
            throw new DataUpdationException("no menu recommendation found");
        }

        return true;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean unitRecommendationMapping(List<IdIndex>  list) {
        for(IdIndex idIndex : list){
            UnitChannelPartnerMenuMappingData unitChannelPartnerMenuMappingData = dao.find(UnitChannelPartnerMenuMappingData.class,idIndex.getId());
            if(unitChannelPartnerMenuMappingData!=null){
                unitChannelPartnerMenuMappingData.setMenuRecommendationSequenceId(idIndex.getIndex());
                dao.update(unitChannelPartnerMenuMappingData);
            }
            updateMasterCacheData(unitChannelPartnerMenuMappingData);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean unitRecommendationPriceProfileMapping(List<IdIndex> list) {
        for(IdIndex idIndex : list){
            UnitChannelPartnerMenuMappingData unitChannelPartnerMenuMappingData = dao.find(UnitChannelPartnerMenuMappingData.class,idIndex.getId());
            if(unitChannelPartnerMenuMappingData!=null){
                unitChannelPartnerMenuMappingData.setPriceProfileId(idIndex.getIndex());
                dao.update(unitChannelPartnerMenuMappingData);
            }
            updateMasterCacheData(unitChannelPartnerMenuMappingData);
        }
        return true;
    }

    private void updateMasterCacheData(UnitChannelPartnerMenuMappingData unitChannelPartnerMenuMappingData) {
        try{
            UnitPartnerMenuMapping mapping= new UnitPartnerMenuMapping();
            mapping.setId(unitChannelPartnerMenuMappingData.getId());
            mapping.setUnitChannelPartnerMappingId(unitChannelPartnerMenuMappingData.getUnitPartnerMappingId());
            mapping.setBrand(new IdCodeName(unitChannelPartnerMenuMappingData.getBrandId(),"",""));
            if(unitChannelPartnerMenuMappingData.getMenuRecommendationSequenceId()!=null) {
                mapping.setMenuRecommendationSequenceId(unitChannelPartnerMenuMappingData.getMenuRecommendationSequenceId());
            }
            if(unitChannelPartnerMenuMappingData.getPriceProfileId()!=null) {
                mapping.setPriceProfileId(unitChannelPartnerMenuMappingData.getPriceProfileId());
            }
            masterDataCacheService.loadUnitPartnerMenuMapping(mapping);
        }catch (Exception e){
            LOG.error("Exception Caught while Updating the Cache:::",e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean updateRecommendationStatus(Integer id, String status) {
        MenuRecommendationData menuRecommendationData= dao.find(MenuRecommendationData.class,id);
        if(menuRecommendationData!=null){
            menuRecommendationData.setStatus(status);
            return true;
        }

        return false;
    }

	@Override
	public List<Integer> getUnitIdsForMenuSequence(Integer menuSequenceId, Integer kettlePartnerId) {
		List<Integer> channelPartnerIds = dao.getChannelPartnerIdsForMenuSequence(menuSequenceId);
		List<Integer> unitIds = dao.getUnitIdsForMenu(channelPartnerIds, kettlePartnerId);
		return unitIds;
	}


    @Override
    public List<Integer> getMenuSequenceIdsAsPerMenuApp(MenuApp menuApp) {

        return dao.getMenuSequenceIdsAsPerMenuApp(menuApp);
    }

    @Override
    public List<Integer> getCategoryIdsMenuSequenceMapping(List<Integer> menuSequenceIds) {

        return dao.getCategoryIdsMenuSequenceMapping(menuSequenceIds);
    }

    @Override
    public List<ProductGroupData> getProductGroupDataAsPerIdAndMenuApp(List<Integer> ids, MenuApp menuApp) {

        return dao.getProductGroupDataAsPerIdAndMenuApp(ids,menuApp);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public IdCodeName saveCategoryImage(final MultipartFile file,String hostUrl) {

        try {
            String baseDir = "offer-service/category_image";
            String fileName = file.getOriginalFilename();
//            String extension = FilenameUtils.getExtension(fileName);

            fileName = fileName.replaceAll(" ", "_").toLowerCase();

            LOG.info(":::::: Request to upload New Category Image ::::::");

            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3OfferServiceBucket(),
                    baseDir, fileName, file, true);
            if (s3File != null) {
                return new IdCodeName(s3File.getName(), hostUrl + s3File.getName());
            }
        } catch (FileArchiveServiceException e) {
            LOG.error("Encountered error while uploading Category Image to S3", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",readOnly = true, propagation = Propagation.REQUIRED)
    public PriceProfileDetail getUnitPartnerProfilePrice(Integer priceProfileId){
        return dao.getUnitPartnerProfilePrice(priceProfileId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",readOnly = true, propagation = Propagation.REQUIRED)
    public List<PriceProfileDetail> getUnitPartnerProfilePrice(){
        return dao.getUnitPartnerProfilePrice();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addProfilePriceMapping(PriceProfileDetail detail){
        return dao.addProfilePriceMapping(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",readOnly = false, propagation = Propagation.REQUIRED)
    public  List<PriceProfileDetail> getProfilePrice(String priceProfileStrategy){
        return dao.getProfilePrice(priceProfileStrategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM",readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateProfilePriceMapping(IdCodeName detail){
        return dao.updateProfilePriceMapping(detail);
    }

}
