package com.stpl.tech.master.core.exception;

public class MasterError {

    private String errorTitle;
    private String errorMsg;
    private Integer errorCode;

    public MasterError(String errorTitle, String errorMsg, Integer errorCode) {
        this.errorTitle = errorTitle;
        this.errorMsg = errorMsg;
        this.errorCode = errorCode;
    }

    public String getErrorTitle() {
        return errorTitle;
    }

    public void setErrorTitle(String errorTitle) {
        this.errorTitle = errorTitle;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }
}
