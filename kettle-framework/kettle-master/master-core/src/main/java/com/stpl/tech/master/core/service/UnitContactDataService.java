package com.stpl.tech.master.core.service;

import com.stpl.tech.master.domain.model.UnitContactData;
import com.stpl.tech.master.domain.model.UnitContactDetails;

import java.util.List;

/**
 * Service interface for managing unit contact data
 */
public interface UnitContactDataService {

    /**
     * Retrieves contact data for a specific unit
     * 
     * @param unitId the unit ID to fetch contacts for
     * @return list of unit contact details
     */
    List<UnitContactDetails> getUnitContactData(Integer unitId);

    /**
     * Adds or updates unit contact data in bulk
     *
     * @param unitContactData the contact data to save/update
     * @param loggedInUser the user performing the operation
     * @return true if operation was successful, false otherwise
     */
    boolean addOrUpdateUnitContactData(UnitContactData unitContactData, Integer loggedInUser);

    /**
     * Deactivates contacts by setting their status to INACTIVE
     *
     * @param contactIds list of contact IDs to deactivate
     * @param loggedInUser the user performing the operation
     * @return number of contacts deactivated
     */
    int deactivateContacts(List<Integer> contactIds, Integer loggedInUser);

    /**
     * Checks if a contact number already exists for a unit
     *
     * @param unitId the unit ID
     * @param contactNumber the contact number to check
     * @param excludeContactId contact ID to exclude from check (for updates)
     * @return true if contact number exists, false otherwise
     */
    boolean isContactNumberExists(Integer unitId, Long contactNumber, Integer excludeContactId);

    /**
     * Clears all cached contact data
     */
    void clearCache();

    /**
     * Clears cached contact data for a specific unit
     * 
     * @param unitId the unit ID to clear cache for
     */
    void clearCacheForUnit(Integer unitId);

    /**
     * Gets the current cache size
     * 
     * @return number of units in cache
     */
    int getCacheSize();
}
