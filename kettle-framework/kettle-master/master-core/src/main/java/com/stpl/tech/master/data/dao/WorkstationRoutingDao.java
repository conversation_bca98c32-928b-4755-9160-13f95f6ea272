package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitProductStationMapping;
import com.stpl.tech.master.data.model.UnitStationCategoryMapping;

import java.util.List;
import java.util.Map;

public interface WorkstationRoutingDao {
    
    /**
     * Get all product to refLookupId mappings for a unit
     * @param unitId the unit ID
     * @return Map of refLookupId to list of product IDs
     */
    Map<Integer, List<Integer>> getProductRefLookupMappings(Integer unitId);

    /**
     * Save a product to station category mapping
     * @param mapping the mapping to save
     */
    void saveProductRefLookupMapping(UnitProductStationMapping mapping);
    
    /**
     * Clear cache for a specific unit
     * @param unitId the unit ID
     */
    void clearCache(Integer unitId);

    /**
     * Clear cache for a specific unit and product
     * @param unitId the unit ID
     * @param productId the product ID
     */
    void clearCache(Integer unitId, Integer productId);

    /**
     * Clear all caches
     */
    void clearAllCaches();
}