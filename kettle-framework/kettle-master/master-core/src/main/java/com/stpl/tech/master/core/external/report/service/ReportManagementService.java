package com.stpl.tech.master.core.external.report.service;

import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.kettle.report.metadata.model.ReportSummary;
import com.stpl.tech.kettle.report.metadata.model.ReportVersionDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.FileDownloadException;
import com.stpl.tech.master.data.model.XMLReportDefinitionData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

public interface ReportManagementService {

    List<ReportSummary> getReportVersionHistoryById(Integer reportId);

    List<XMLReportDefinitionData> getAllReportCategories(String executionEnvironment);

	int addNewReport(ReportSummary report) throws DataUpdationException;

	int updateStatus(int reportId, String status);

    int addNewReportVersion(ReportVersionDetail report);

    int markDefaultReportVersion(int reportId, int versionId);

    void uploadNewReportVersion(String s3ReportBucket, MultipartFile file, String comments, int reportId) throws IOException;

    void downloadManagementReport(HttpServletResponse response, Integer reportId) throws DataNotFoundException, FileDownloadException, IOException;

    List<XMLReportDefinitionData> getAllReportList(String executionEnvironment);

    File getS3FileFromActiveReportVersion(Integer reportId, Integer version) throws DataNotFoundException, IOException;

	void downloadManagementReport(HttpServletResponse response, String string) throws IOException;

	ReportCategories getReportCategory(int reportId) throws IOException, DataNotFoundException;

}
