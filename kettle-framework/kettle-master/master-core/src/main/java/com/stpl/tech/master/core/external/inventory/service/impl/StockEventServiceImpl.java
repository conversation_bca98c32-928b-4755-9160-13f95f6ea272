package com.stpl.tech.master.core.external.inventory.service.impl;

import com.amazonaws.regions.Regions;
import com.google.gson.Gson;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.inventory.service.StockEventService;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.jms.JMSException;

@Service
public class StockEventServiceImpl implements StockEventService {

    private static final Logger LOG = LoggerFactory.getLogger(StockEventServiceImpl.class);

    @Autowired
    private SQSNotificationService sqsNotificationService;

    @Override
    public void publishStockEvent(String env, UnitProductsStockEvent event) throws JMSException {
        sqsNotificationService.publishToSQSFifo(env, event, "_STOCK_EVENTS.fifo", Regions.EU_WEST_1);
        LOG.info("STOCK EVENT SENT::::" + "\n" + new Gson().toJson(event));
    }

}
