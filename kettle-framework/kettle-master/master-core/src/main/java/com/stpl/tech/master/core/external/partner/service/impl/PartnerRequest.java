package com.stpl.tech.master.core.external.partner.service.impl;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.core.external.acl.service.TokenDao;

import io.jsonwebtoken.Claims;

public class PartnerRequest implements TokenDao, Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5613812343050049455L;
	protected Integer partnerId;
	protected String data;

	public PartnerRequest() {

	}
	
	public Integer getPartnerId() {
		return partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}

	@Override
	public Map<String, Object> createClaims() {
		// Setting JWT Claims
		Map<String, Object> authClaims = new HashMap<String, Object>();
		authClaims.put("partnerId", partnerId);
		authClaims.put("data", data);
		return authClaims;
	}

	@Override
	public void parseClaims(Claims claims) {
		this.partnerId = claims.get("partnerId", Integer.class);
		this.data = claims.get("data", String.class);
	}

}
