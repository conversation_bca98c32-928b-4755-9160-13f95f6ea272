package com.stpl.tech.master.data.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "MENU_RECOMMENDATION_MAPPING_DATA")
public class MenuRecommendationMappingData {

	private Integer id;
	private Integer menuRecommendationId;
	private Integer productId;
	private String dimension;
	private Integer recommendedProductId;
	private String recommendedDimension;
	private Integer index;
	private Integer createdBy;
	private Integer updatedBy;
	private Date creationTime;
	private Date lastUpdateTime;
	private String status;
	private String recommendationTitle;
	private BigDecimal discountAmount;
	private String discountType;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "MENU_RECOMMENDATION_MAPPING_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "MENU_RECOMMENDATION_ID", nullable = false)
	public Integer getMenuRecommendationId() {
		return menuRecommendationId;
	}

	public void setMenuRecommendationId(Integer menuSequenceId) {
		this.menuRecommendationId = menuSequenceId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productGroupId) {
		this.productId = productGroupId;
	}

	@Column(name = "RECOMMENDED_PRODUCT_ID", nullable = false)
	public Integer getRecommendedProductId() {
		return recommendedProductId;
	}

	public void setRecommendedProductId(Integer productGroupParentId) {
		this.recommendedProductId = productGroupParentId;
	}

	@Column(name = "SEQUENCE_INDEX", nullable = false)
	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "UPDATED_BY", nullable = false)
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "CREATION_TIME", nullable = false)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "DIMENSION", nullable = true)
	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Column(name = "RECOMMENDED_DIMENSION", nullable = true)
	public String getRecommendedDimension() {
		return recommendedDimension;
	}

	public void setRecommendedDimension(String recommendedDimension) {
		this.recommendedDimension = recommendedDimension;
	}

	@Column(name = "RECOMMENDATION_TITLE")
	public String getRecommendationTitle() {
		return recommendationTitle;
	}

	public void setRecommendationTitle(String recommendationTitle) {
		this.recommendationTitle = recommendationTitle;
	}

	@Column(name = "DISCOUNT_AMOUNT")
	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	@Column(name = "DISCOUNT_TYPE")
	public String getDiscountType() {
		return discountType;
	}

	public void setDiscountType(String discountType) {
		this.discountType = discountType;
	}
}
