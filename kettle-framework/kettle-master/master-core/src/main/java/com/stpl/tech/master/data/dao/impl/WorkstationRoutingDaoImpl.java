package com.stpl.tech.master.data.dao.impl;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.MasterMetadataService;
import com.stpl.tech.master.data.dao.WorkstationRoutingDao;
import com.stpl.tech.master.data.exception.WorkstationRoutingException;
import com.stpl.tech.master.data.model.RefLookupInfo;
import com.stpl.tech.master.data.model.Status;
import com.stpl.tech.master.data.model.UnitProductAsKey;
import com.stpl.tech.master.data.model.UnitProductStationMapping;
import com.stpl.tech.master.data.model.UnitStationCategoryMapping;
import com.stpl.tech.master.data.repository.UnitProductStationMappingRepository;
import com.stpl.tech.master.data.repository.UnitStationCategoryMappingRepository;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.util.AppConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class WorkstationRoutingDaoImpl implements WorkstationRoutingDao {

    @Autowired
    private UnitProductStationMappingRepository unitProductStationMappingRepository;

    @Autowired
    private UnitStationCategoryMappingRepository stationCategoryMappingRepository;

    @Autowired
    private MasterDataCache masterDataCache;
    
    @Autowired
    private MasterMetadataService metadataDao;
    
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<Integer>> getProductRefLookupMappings(Integer unitId) {
        Assert.notNull(unitId, "Unit ID cannot be null");
        Map<Integer, List<Integer>> result = new HashMap<>();
        
        try {
            IMap<UnitProductAsKey, RefLookupInfo> cache = masterDataCache.getWorkstationRoutingCache();
            
            // First try to get from cache
            if (cache != null && !cache.isEmpty()) {
                cache.entrySet().stream()
                        .filter(entry -> entry.getKey() != null && unitId.equals(entry.getKey().getUnitId()))
                        .forEach(entry -> {
                            Integer refLookupId = entry.getValue().getRefLookupId();
                            if (refLookupId != null) {
                                result.computeIfAbsent(refLookupId, k -> new ArrayList<>())
                                        .add(entry.getKey().getProductId());
                            }
                        });
            }
            
            if (result.isEmpty()) {
                log.info("Cache miss for unit: {}, fetching from database", unitId);
                
                // Use the improved query
                List<UnitStationCategoryMapping> stationMappings = stationCategoryMappingRepository
                                                                   .findByUnitIdAndStatusWithProducts(unitId, Status.ACTIVE);
                
                if (stationMappings != null && !stationMappings.isEmpty()) {
                    // Process the hierarchical data structure
                    stationMappings.forEach(stationMapping -> {
                        try {
                            Integer refLookupId = stationMapping.getRefLookupId();
                            if (refLookupId != null && stationMapping.getProductMappings() != null) {
                                
                                // Process each product mapping under this station
                                List<ListData> allStations = metadataDao.getAllListData(AppConstants.RTL_GROUP_STATION_CATEGORIES, true);
                                stationMapping.getProductMappings().forEach(productMapping -> {
                                    try {
                                        if (productMapping != null &&
                                                    productMapping.getProductId() != null &&
                                                    Status.ACTIVE.equals(productMapping.getStatus())) {
                                            
                                            result.computeIfAbsent(refLookupId, k -> new ArrayList<>())
                                                    .add(productMapping.getProductId());
                                            
                                            if (cache != null) {
                                                try {
                                                    UnitProductAsKey key = new UnitProductAsKey(unitId, productMapping.getProductId());
                                                    if (Objects.nonNull(allStations)) {
                                                        String stationName = allStations.stream()
                                                                                     .flatMap(page -> page.getContent().stream())
                                                                                     .filter(station -> Objects.equals(station.getId(),refLookupId))
                                                                                     .map(IdCodeName::getName)
                                                                                     .findFirst()
                                                                                     .orElse(null);
                                                        cache.put(key, new RefLookupInfo(refLookupId,stationName));
                                                    }
                                                    
                                                } catch (Exception cacheException) {
                                                    log.warn("Failed to cache mapping for unit: {}, product: {} - {}",
                                                            unitId, productMapping.getProductId(), cacheException.getMessage());
                                                }
                                            }
                                        }
                                    } catch (Exception productException) {
                                        log.warn("Error processing product mapping: {}", productException.getMessage());
                                    }
                                });
                            }
                        } catch (Exception stationException) {
                            log.warn("Error processing station mapping for unit: {} - {}", unitId, stationException.getMessage());
                        }
                    });
                    
                    // Remove duplicates from result lists
                    result.forEach((refLookupId, productIds) -> {
                        List<Integer> uniqueProductIds = productIds.stream()
                                                                 .distinct()
                                                                 .collect(Collectors.toList());
                        result.put(refLookupId, uniqueProductIds);
                    });
                    
                    log.info("Found {} station mappings with products for unit: {}", stationMappings.size(), unitId);
                } else {
                    log.info("No station mappings found for unit: {}", unitId);
                }
            } else {
                log.info("Retrieved {} mappings from cache for unit: {}", result.size(), unitId);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("Error fetching product ref lookup mappings for unit: {} - {}", unitId, e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveProductRefLookupMapping(UnitProductStationMapping mapping) {
        Assert.notNull(mapping, "Mapping cannot be null");
        try {
            unitProductStationMappingRepository.save(mapping);
            List<ListData> allStations = metadataDao.getAllListData(AppConstants.RTL_GROUP_STATION_CATEGORIES, true);
            IMap<UnitProductAsKey, RefLookupInfo> cache = masterDataCache.getWorkstationRoutingCache();
            if (cache != null && mapping.getProductId() != null && mapping.getStationCategoryMapping() != null) {
                Integer unitId = mapping.getStationCategoryMapping().getUnitId();
                if (unitId != null) {
                    UnitProductAsKey key = new UnitProductAsKey(unitId, mapping.getProductId());
                    // Update cache with new mapping
                    if (Status.ACTIVE.equals(mapping.getStatus())) {
                        if (Objects.nonNull(allStations)) {
                            String stationName = allStations.stream()
                                                         .flatMap(page -> page.getContent().stream())
                                                         .filter(station -> Objects.equals(station.getId(),  mapping.getStationCategoryMapping().getRefLookupId()))
                                                         .map(IdCodeName::getName)
                                                         .findFirst()
                                                         .orElse(null);
                            cache.put(key, new RefLookupInfo(mapping.getStationCategoryMapping().getRefLookupId(),stationName));
                        }
                        log.debug("Cache updated for unit: {}, product: {}, refLookupId: {}", 
                            unitId, mapping.getProductId(), mapping.getStationCategoryMapping().getRefLookupId());
                    } else {
                        cache.remove(key);
                        log.debug("Cache cleared for unit: {}, product: {}", unitId, mapping.getProductId());
                    }
                }
            }
            log.info("Successfully saved product ref lookup mapping: {}", mapping);
        } catch (Exception e) {
            log.error("Error saving product ref lookup mapping: {}", mapping, e);
            throw new WorkstationRoutingException("Failed to save product ref lookup mapping", e);
        }
    }
    
    @Override
    public void clearCache(Integer unitId) {
        Assert.notNull(unitId, "Unit ID cannot be null");
        try {
            IMap<UnitProductAsKey, RefLookupInfo> cache = masterDataCache.getWorkstationRoutingCache();
            // Remove all entries for this unit
            cache.keySet().stream()
                .filter(key -> key != null && unitId.equals(key.getUnitId()))
                .forEach(cache::remove);
            log.info("Cache cleared for unit: {}", unitId);
        } catch (Exception e) {
            log.error("Error clearing cache for unit: {}", unitId, e);
            throw new WorkstationRoutingException("Failed to clear cache for unit", e);
        }
    }

    @Override
    public void clearCache(Integer unitId, Integer productId) {
        Assert.notNull(unitId, "Unit ID cannot be null");
        Assert.notNull(productId, "Product ID cannot be null");
        try {
            IMap<UnitProductAsKey, RefLookupInfo> cache = masterDataCache.getWorkstationRoutingCache();
            UnitProductAsKey key = new UnitProductAsKey(unitId, productId);
            cache.remove(key);
            log.info("Cache cleared for unit: {}, product: {}", unitId, productId);
        } catch (Exception e) {
            log.error("Error clearing cache for unit: {}, product: {}", unitId, productId, e);
            throw new WorkstationRoutingException("Failed to clear cache for unit and product", e);
        }
    }

    @Override
    public void clearAllCaches() {
        try {
            masterDataCache.clearWorkstationRoutingCache();
            log.info("All caches cleared successfully");
        } catch (Exception e) {
            log.error("Error clearing all caches", e);
            throw new WorkstationRoutingException("Failed to clear all caches", e);
        }
    }
} 