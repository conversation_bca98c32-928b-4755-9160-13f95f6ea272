package com.stpl.tech.master.core.service;

import com.stpl.tech.master.data.model.LCMMenuVariantMetadataGroup;
import com.stpl.tech.master.data.model.LCMMenuVariantMetadataItem;
import com.stpl.tech.master.domain.model.LCMMenuVariantMetadataGroupDomain;
import com.stpl.tech.master.domain.model.LCMMenuVariantMetadataItemDomain;

import java.util.List;

public interface LCDMenuVariantManagementService {
    List<LCMMenuVariantMetadataGroupDomain> getAllGroups();
    LCMMenuVariantMetadataGroupDomain createGroup(LCMMenuVariantMetadataGroup group);
    LCMMenuVariantMetadataGroupDomain updateGroup(Long groupId, LCMMenuVariantMetadataGroupDomain group);
    void deleteGroup(Long groupId);

    // Item operations
    List<LCMMenuVariantMetadataItemDomain> getItemsByGroup(Long groupId ) ;
    LCMMenuVariantMetadataItemDomain createItem(LCMMenuVariantMetadataItem item  , Long groupId);
    LCMMenuVariantMetadataItemDomain updateItem(Long itemId, LCMMenuVariantMetadataItemDomain item  , Long groupId);
    void deleteItem(Long itemId);
}
