/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * EmployeeDetail generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "EMPLOYEE_DETAIL")
public class EmployeeDetail implements java.io.Serializable {

	private Integer empId;
	private AddressInfo addressInfoByEmpPermanentAddr;
	private Department department;
	private AddressInfo addressInfoByEmpCurrentAddr;
	private EmployeeDetail reportingManager;
	private Designation designation;
	private String empName;
	private String empGender;
	private String empContactNum1;
	private String empContactNum2;
	private String employmentType;
	private String employmentStatus;
	private String biometricIdentifier;
	private Date joiningDate;
	private Date terminationDate;
	private String employeeEmail;
	private String employeeCode;
	private String communicationChannel;
	private String sdpContact;
	private String employeeMealEligible;
	private String reasonForTermination;
	private String hrExecutive;
	private String leaveApprovalAuthority;
	private Date dob;
	private Integer locCode;
	private UserPolicyData userPolicyData;

	private CompanyDetail companyDetail;
	private String imageKey;

	public EmployeeDetail() {
	}
	
	public EmployeeDetail(Integer empId, AddressInfo addressInfoByEmpPermanentAddr, Department department, AddressInfo addressInfoByEmpCurrentAddr, EmployeeDetail reportingManager, Designation designation, String empName, String empGender, String empContactNum1, String empContactNum2, String employmentType, String employmentStatus, String biometricIdentifier, Date joiningDate, Date terminationDate, String employeeEmail, String employeeCode, String communicationChannel, String sdpContact, String employeeMealEligible, String reasonForTermination, String hrExecutive, String leaveApprovalAuthority, Date dob) {
		this.empId = empId;
		this.addressInfoByEmpPermanentAddr = addressInfoByEmpPermanentAddr;
		this.department = department;
		this.addressInfoByEmpCurrentAddr = addressInfoByEmpCurrentAddr;
		this.reportingManager = reportingManager;
		this.designation = designation;
		this.empName = empName;
		this.empGender = empGender;
		this.empContactNum1 = empContactNum1;
		this.empContactNum2 = empContactNum2;
		this.employmentType = employmentType;
		this.employmentStatus = employmentStatus;
		this.biometricIdentifier = biometricIdentifier;
		this.joiningDate = joiningDate;
		this.terminationDate = terminationDate;
		this.employeeEmail = employeeEmail;
		this.employeeCode = employeeCode;
		this.communicationChannel = communicationChannel;
		this.sdpContact = sdpContact;
		this.employeeMealEligible = employeeMealEligible;
		this.reasonForTermination = reasonForTermination;
		this.hrExecutive = hrExecutive;
		this.leaveApprovalAuthority = leaveApprovalAuthority;
		this.dob = dob;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EMP_ID", unique = true, nullable = false)
	public Integer getEmpId() {
		return this.empId;
	}

	public void setEmpId(Integer empId) {
		this.empId = empId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EMP_PERMANENT_ADDR", nullable = false)
	public AddressInfo getAddressInfoByEmpPermanentAddr() {
		return this.addressInfoByEmpPermanentAddr;
	}

	public void setAddressInfoByEmpPermanentAddr(AddressInfo addressInfoByEmpPermanentAddr) {
		this.addressInfoByEmpPermanentAddr = addressInfoByEmpPermanentAddr;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DEPTARTMENT_ID", nullable = false)
	public Department getDepartment() {
		return this.department;
	}

	public void setDepartment(Department department) {
		this.department = department;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EMP_CURRENT_ADDR", nullable = false)
	public AddressInfo getAddressInfoByEmpCurrentAddr() {
		return this.addressInfoByEmpCurrentAddr;
	}

	public void setAddressInfoByEmpCurrentAddr(AddressInfo addressInfoByEmpCurrentAddr) {
		this.addressInfoByEmpCurrentAddr = addressInfoByEmpCurrentAddr;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REPORTING_MANAGER_ID")
	public EmployeeDetail getReportingManager() {
		return this.reportingManager;
	}

	public void setReportingManager(EmployeeDetail reportingManager) {
		this.reportingManager = reportingManager;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DESIGNATION_ID", nullable = false)
	public Designation getDesignation() {
		return this.designation;
	}

	public void setDesignation(Designation designation) {
		this.designation = designation;
	}

	@Column(name = "EMP_NAME", nullable = false)
	public String getEmpName() {
		return this.empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	@Column(name = "EMP_GENDER", nullable = true, length = 1)
	public String getEmpGender() {
		return this.empGender;
	}

	public void setEmpGender(String empGender) {
		this.empGender = empGender;
	}

	@Column(name = "EMP_CONTACT_NUM_1", length = 32)
	public String getEmpContactNum1() {
		return this.empContactNum1;
	}

	public void setEmpContactNum1(String empContactNum1) {
		this.empContactNum1 = empContactNum1;
	}

	@Column(name = "EMP_CONTACT_NUM_2", length = 32)
	public String getEmpContactNum2() {
		return this.empContactNum2;
	}

	public void setEmpContactNum2(String empContactNum2) {
		this.empContactNum2 = empContactNum2;
	}

	@Column(name = "EMPLOYMENT_TYPE", nullable = false, length = 10)
	public String getEmploymentType() {
		return this.employmentType;
	}

	public void setEmploymentType(String employmentType) {
		this.employmentType = employmentType;
	}

	@Column(name = "EMPLOYMENT_STATUS", nullable = false, length = 10)
	public String getEmploymentStatus() {
		return this.employmentStatus;
	}

	public void setEmploymentStatus(String employmentStatus) {
		this.employmentStatus = employmentStatus;
	}

	@Column(name = "BIOMETRIC_IDENTIFIER")
	public String getBiometricIdentifier() {
		return this.biometricIdentifier;
	}

	public void setBiometricIdentifier(String biometricIdentifier) {
		this.biometricIdentifier = biometricIdentifier;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "JOINING_DATE", nullable = false, length = 10)
	public Date getJoiningDate() {
		return this.joiningDate;
	}

	public void setJoiningDate(Date joiningDate) {
		this.joiningDate = joiningDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "TERMINATION_DATE", length = 10)
	public Date getTerminationDate() {
		return this.terminationDate;
	}

	public void setTerminationDate(Date terminationDate) {
		this.terminationDate = terminationDate;
	}

	@Column(name = "EMP_EMAIL", nullable = true, length = 10)
	public String getEmployeeEmail() {
		return employeeEmail;
	}

	public void setEmployeeEmail(String employeeEmail) {
		this.employeeEmail = employeeEmail;
	}

	@Column(name = "EMPLOYEE_CODE", nullable = true, length = 15)
	public String getEmployeeCode() {
		return employeeCode;
	}

	public void setEmployeeCode(String employeeCode) {
		this.employeeCode = employeeCode;
	}

	@Column(name = "COMMUNICATION_CHANNEL", nullable = true, length = 50)
	public String getCommunicationChannel() {
		return communicationChannel;
	}

	public void setCommunicationChannel(String communicationChannel) {
		this.communicationChannel = communicationChannel;
	}

	@Column(name = "SDP_CONTACT", length = 20, unique = true)
	public String getSdpContact() {
		return sdpContact;
	}

	public void setSdpContact(String sdpContact) {
		this.sdpContact = sdpContact;
	}

	@Column(name = "EMPLOYEE_MEAL_ELIGIBLE", length = 1)
	public String getEmployeeMealEligible() {
		return employeeMealEligible;
	}

	public void setEmployeeMealEligible(String employeeMealEligible) {
		this.employeeMealEligible = employeeMealEligible;
	}
	

	@Column(name="REASON_FOR_TERMINATION")
	public String getReasonForTermination() {
		return reasonForTermination;
	}

	public void setReasonForTermination(String reasonForTermination) {
		this.reasonForTermination = reasonForTermination;
	}
	@Column(name="HR_EXECUTIVE")
	public String getHrExecutive() {
		return hrExecutive;
	}

	public void setHrExecutive(String hrExecutive) {
		this.hrExecutive = hrExecutive;
	}
	@Column(name="LEAVE_APPROVAL_AUTHORITY")
	public String getLeaveApprovalAuthority() {
		return leaveApprovalAuthority;
	}

	public void setLeaveApprovalAuthority(String leaveApprovalAuthority) {
		this.leaveApprovalAuthority = leaveApprovalAuthority;
	}
	@Column(name="DOB")
	public Date getDob() {
		return dob;
	}

	public void setDob(Date dob) {
		this.dob = dob;
	}

	@Column(name="LOCATION_CODE")
	public Integer getLocCode() {
		return locCode;
	}

	public void setLocCode(Integer locCode) {
		this.locCode = locCode;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_POLICY_ID")
	public UserPolicyData getUserPolicyData() {
		return userPolicyData;
	}

	public void setUserPolicyData(UserPolicyData userPolicyData) {
		this.userPolicyData = userPolicyData;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COMPANY_ID")
	public CompanyDetail getCompanyDetail() {
		return companyDetail;
	}

	public void setCompanyDetail(CompanyDetail companyDetail) {
		this.companyDetail = companyDetail;
	}

	@Column(name = "IMAGE_KEY")
	public String getImageKey() {
		return imageKey;
	}

	public void setImageKey(String imageKey) {
		this.imageKey = imageKey;
	}
}
