/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * UnitDetail generated by hbm2java
 */
@Entity
@Table(name = "UNIT_ATTRIBUTE_MAPPING")
public class UnitAttributeMapping implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7726578415700938848L;
	private Integer attributeMappingId;
	private UnitDetail unitDetail;
	private String attributeCode;
    private Integer attributeId;
	private String attributeValue;
	private String attributeType;
	private String mappingStatus;

	public UnitAttributeMapping() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_ATTRIBUTE_MAPPING_ID", unique = true, nullable = false)
	public Integer getAttributeMappingId() {
		return this.attributeMappingId;
	}

	public void setAttributeMappingId(Integer unitId) {
		this.attributeMappingId = unitId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	public UnitDetail getUnitDetail() {
		return unitDetail;
	}

	public void setUnitDetail(UnitDetail unitDetail) {
		this.unitDetail = unitDetail;
	}

	@Column(name = "ATTRIBUTE_CODE", nullable = false)
	public String getAttributeCode() {
		return this.attributeCode;
	}

	public void setAttributeCode(String unitName) {
		this.attributeCode = unitName;
	}

	@Column(name = "ATTRIBUTE_VALUE", nullable = false, length = 500)
	public String getAttributeValue() {
		return this.attributeValue;
	}

	public void setAttributeValue(String unitRegion) {
		this.attributeValue = unitRegion;
	}

	@Column(name = "ATTRIBUTE_TYPE", nullable = false, length = 20)
	public String getAttributeType() {
		return attributeType;
	}

	public void setAttributeType(String attributeType) {
		this.attributeType = attributeType;
	}

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return mappingStatus;
	}

	public void setMappingStatus(String unitSubCategory) {
		this.mappingStatus = unitSubCategory;
	}

    @Column(name = "ATTRIBUTE_ID", nullable = false, length = 15)
    public Integer getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }
}