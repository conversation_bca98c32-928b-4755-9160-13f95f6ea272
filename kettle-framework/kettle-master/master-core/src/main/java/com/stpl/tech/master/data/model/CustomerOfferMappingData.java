/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * UnitDetail generated by hbm2java
 */
@Entity
@Table(name = "CUSTOMER_OFFER_MAPPING_DATA")
public class CustomerOfferMappingData implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6224595501578269163L;
	private Integer customerOfferMappingDataId;
	private int couponDetailId;
	private int offerDetailId;
	private String contactNumber;
	private String customerName;
	private String couponCode;
	private Integer unitId;
	private Date creationTime;
	private String notified;
	private Integer offerDayCount;
	private String offerCodeUsed;
	private String offerStrategy;
	private String acquisitionSource;
	private String urlEndPoint;
	private Date lastUpdateTime;

	public CustomerOfferMappingData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CUSTOMER_OFFER_MAPPING_DATA_ID", unique = true, nullable = false)
	public Integer getCustomerOfferMappingDataId() {
		return this.customerOfferMappingDataId;
	}

	public void setCustomerOfferMappingDataId(Integer couponDetailMappingId) {
		this.customerOfferMappingDataId = couponDetailMappingId;
	}

	@Column(name = "COUPON_DETAIL_ID", nullable = false)
	public int getCouponDetailId() {
		return couponDetailId;
	}

	public void setCouponDetailId(int couponDetailId) {
		this.couponDetailId = couponDetailId;
	}

	@Column(name = "CUSTOMER_NAME", nullable = true)
	public String getCustomerName() {
		return this.customerName;
	}

	public void setCustomerName(String mappingValue) {
		this.customerName = mappingValue;
	}

	@Column(name = "CONTACT_NUMBER", nullable = true)
	public String getContactNumber() {
		return this.contactNumber;
	}

	public void setContactNumber(String mappingType) {
		this.contactNumber = mappingType;
	}

	@Column(name = "UNIT_ID", nullable = true)
	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer dataType) {
		this.unitId = dataType;
	}

	@Column(name = "COUPON_CODE", nullable = true)
	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String dimension) {
		this.couponCode = dimension;
	}

	@Column(name = "OFFER_DETAIL_ID", nullable = true)
	public int getOfferDetailId() {
		return offerDetailId;
	}

	public void setOfferDetailId(int offerDetailId) {
		this.offerDetailId = offerDetailId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = false, length = 19)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "IS_NOTIFIED", nullable = true)
	public String getNotified() {
		return notified;
	}

	public void setNotified(String notified) {
		this.notified = notified;
	}

	@Column(name = "OFFER_DAY_COUNT", nullable = true)
	public Integer getOfferDayCount() {
		return offerDayCount;
	}

	public void setOfferDayCount(Integer offerDayCount) {
		this.offerDayCount = offerDayCount;
	}

	@Column(name = "OFFER_CODE_USED", nullable = true)
	public String getOfferCodeUsed() {
		return offerCodeUsed;
	}

	public void setOfferCodeUsed(String offerCodeUsed) {
		this.offerCodeUsed = offerCodeUsed;
	}

	@Column(name = "OFFER_STRATEGY", nullable = true)
	public String getOfferStrategy() {
		return offerStrategy;
	}

	public void setOfferStrategy(String offerStrategy) {
		this.offerStrategy = offerStrategy;
	}

	@Column(name = "ACQUISITION_SOURCE", nullable = true)
	public String getAcquisitionSource() {
		return acquisitionSource;
	}

	public void setAcquisitionSource(String acquisitionSource) {
		this.acquisitionSource = acquisitionSource;
	}

	@Column(name = "LAST_UPDATE_TIME", nullable = true, length = 19)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "URL_END_POINT", nullable = true, length = 200)
	public String getUrlEndPoint() {
		return urlEndPoint;
	}

	public void setUrlEndPoint(String urlEndPoint) {
		this.urlEndPoint = urlEndPoint;
	}

	
}
