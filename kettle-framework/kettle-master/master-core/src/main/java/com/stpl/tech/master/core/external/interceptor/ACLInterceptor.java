/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import com.stpl.tech.master.core.exception.AccessDeniedException;
import com.stpl.tech.master.core.external.acl.service.ACLService;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import com.stpl.tech.util.ACLUtil;

/**
 * Created by Rahul Singh on 22-04-2016.
 */
@Component
public class ACLInterceptor implements AsyncHandlerInterceptor {

    @Autowired
    private ACLService aclService;

    @Autowired
    private Environment env;

    @Autowired
    private ExternalAPITokenCache externalTokenCache;

    @Autowired
    private TokenService<JWTToken> jwtService;

    @Scheduled(fixedRate = 7200000)
	public void clearAclURICache() {
		ACLUtil.getInstance().clearUriCache("SessionAuthInterceptor");
	}
    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o)
        throws Exception {
        //LOG.info(":::::::::::::::::::Inside ACL Interceptor ::::::::::::::::: {} for {}", httpServletRequest.getMethod(), httpServletRequest.getPathInfo());

        String module = ACLUtil.getInstance().convertURIToModule(httpServletRequest.getRequestURI());
        if (!Boolean.valueOf(env.getProperty("run.aclInterceptor", "false")) || aclService.isPreAuthenticated(module)) {
            return true;
        }
        String authHeader = httpServletRequest.getHeader("auth") != null ? httpServletRequest.getHeader("auth").trim()
            : httpServletRequest.getHeader("auth");
        String authInternalHeader = httpServletRequest.getHeader("auth-internal")!=null ? httpServletRequest.getHeader("auth-internal").trim()
                : httpServletRequest.getHeader("auth-internal");
        if (authHeader != null) {
            JWTToken jwtToken = new JWTToken();
            jwtService.parseToken(jwtToken, authHeader);
            String sessionKey = jwtToken.getSessionKey();
            if (sessionKey != null && aclService.checkPermission(module, httpServletRequest.getMethod(), sessionKey)) {
                return true;
            }
        }else if(authInternalHeader!=null){
            return externalTokenCache.isValidKey(authInternalHeader) && externalTokenCache.checkAccess(authInternalHeader,
                    httpServletRequest.getRequestURI(), httpServletRequest.getMethod());
        }

        throw new AccessDeniedException(String.format("Unauthorized access..."));
    }
}
