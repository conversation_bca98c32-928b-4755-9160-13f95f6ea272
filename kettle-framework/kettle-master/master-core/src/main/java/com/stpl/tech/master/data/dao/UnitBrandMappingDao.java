package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitBrandMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitBrandMappingDao extends JpaRepository<UnitBrandMapping, Integer> {

    @Query("SELECT ubm FROM UnitBrandMapping ubm WHERE ubm.status = :status")
    public List<UnitBrandMapping> findByStatus(@Param("status") String status);

    @Query("SELECT ubm FROM UnitBrandMapping ubm WHERE ubm.unitId = :unitId")
    public List<UnitBrandMapping> findByUnitId(@Param("unitId") Integer unitId);

}