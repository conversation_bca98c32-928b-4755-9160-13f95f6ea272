package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.PriceProfileData;
import com.stpl.tech.master.data.model.PriceProfileVersion;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface PriceProfileVersionDao extends CrudRepository<PriceProfileVersion,Integer> {
    @Query("SELECT ppv FROM PriceProfileVersion ppv " +
            "WHERE ppv.priceProfileData.priceProfileDataId IN :profileIds " +
            "AND ppv.status = :status")
    List<PriceProfileVersion> findByProfileIdsAndStatus(
            @Param("profileIds") List<Integer> profileIds,
            @Param("status") String status
    );


    @Query("SELECT ppv FROM PriceProfileVersion ppv " +
            "WHERE ppv.priceProfileVersionsId IN :versionIds ")
    List<PriceProfileVersion> findAllByPriceProfileVersionsId(@Param("versionIds") List<Integer> priceProfileVersionsIds);

    @Query("SELECT MAX(p.versionNo) FROM PriceProfileVersion p WHERE p.priceProfileData.priceProfileDataId = :priceProfileDataId")
    Integer findMaxVersionNoByPriceProfileDataId(@Param("priceProfileDataId") Integer priceProfileDataId);

    @Query("SELECT MAX(p.versionNo) FROM PriceProfileVersion p")
    Integer findMaxVersionNo();

    @Query("SELECT ppv.priceProfileData FROM PriceProfileVersion ppv " +
            " WHERE ppv.versionNo = :versionNo")
    List<PriceProfileData> findPriceProfileDataByVersionNo(@Param("versionNo") Integer versionNo);

    @Query("SELECT ppv FROM PriceProfileVersion ppv " +
            " WHERE ppv.versionNo = :versionNo")
    List<PriceProfileVersion> findPriceVersionDataByVersionNo(@Param("versionNo") Integer versionNo);
}
