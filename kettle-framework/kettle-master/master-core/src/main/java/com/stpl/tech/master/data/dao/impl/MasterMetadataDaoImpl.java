/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.report.metadata.model.WarningMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseValidation;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.ListTypes;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.MasterMetadataDao;
import com.stpl.tech.master.data.dao.UnitPriceProfileMappingDao;
import com.stpl.tech.master.data.model.AddonProductData;
import com.stpl.tech.master.data.model.AddressInfo;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.CancellationReasonData;
import com.stpl.tech.master.data.model.ChannelPartnerCommission;
import com.stpl.tech.master.data.model.CompanyDetail;
import com.stpl.tech.master.data.model.Denomination;
import com.stpl.tech.master.data.model.EmployeeDetail;
import com.stpl.tech.master.data.model.ExpenseMetadataDetail;
import com.stpl.tech.master.data.model.ExpenseValidationDetail;
import com.stpl.tech.master.data.model.ExpenseValidationMappingData;
import com.stpl.tech.master.data.model.GroupRecommendationMapping;
import com.stpl.tech.master.data.model.KioskCompanyDetailsData;
import com.stpl.tech.master.data.model.KioskLocationDetailsData;
import com.stpl.tech.master.data.model.KioskMachineDetailsData;
import com.stpl.tech.master.data.model.LocationDetail;
import com.stpl.tech.master.data.model.MachineProductMappingMetaData;
import com.stpl.tech.master.data.model.MenuExcelUploadEvent;
import com.stpl.tech.master.data.model.MenuSequenceData;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.PaymentModeAttributes;
import com.stpl.tech.master.data.model.PriceProfileProductMapping;
import com.stpl.tech.master.data.model.ProductDetail;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.RefLookup;
import com.stpl.tech.master.data.model.RefLookupType;
import com.stpl.tech.master.data.model.RegionMap;
import com.stpl.tech.master.data.model.StateDetail;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.data.model.UnitContactDetailsData;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitGroupMappingData;
import com.stpl.tech.master.data.model.WarningReasonDetail;
import com.stpl.tech.master.domain.model.AddonList;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.CancellationReason;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.Department;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.Division;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.PriceProfileKey;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductDimensionKey;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.TrimmedProductPrice;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.domain.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
public class MasterMetadataDaoImpl extends AbstractMasterDaoImpl implements MasterMetadataDao {

    private static final Logger LOG = LoggerFactory.getLogger(MasterMetadataDaoImpl.class);

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private UnitPriceProfileMappingDao unitPriceProfileMappingDao;

    @Autowired
    private MasterProperties props;

    public Unit getUnit(int unitId, boolean getAll) throws DataNotFoundException {
        try {
            UnitDetail unit = manager.find(UnitDetail.class, unitId);
            IdCodeName cafeManager = null;
            if (unit != null && unit.getCafeManager() != null) {
                cafeManager = new IdCodeName(unit.getCafeManager(), masterDataCache.getEmployees().get(unit.getCafeManager()), null);
            }
            return MasterDataConverter.convert(unit, cafeManager, getAll,props);
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find Unit Details with ID : %d", unitId), e);
        }

    }

    public Collection<Product> getUnitProducts(int unitId, boolean getAll) throws DataNotFoundException {
        try {
            UnitDetail unit = manager.find(UnitDetail.class, unitId);
            Boolean isPricingProfileEnabled = props.isPriceProfileModeEnabled();
            Map<ProductDimensionKey,BigDecimal> productPriceMap = new HashMap<>();
            if(isPricingProfileEnabled){
                Map<UnitPartnerBrandKey, PriceProfileKey> priceProfileMap = unit.getUnitPriceProfileMappings().stream().
                        collect(Collectors.toMap(mapping -> new UnitPartnerBrandKey(mapping.getUnitId(),mapping.getBrandId(),mapping.getChannelPartnerId())
                                ,mapping ->PriceProfileKey.builder().priceProfileId(mapping.getPriceProfileId()).priceProfileVersion(mapping.getPriceProfileVersion()).build()));
                Map<Integer,Integer> defaultPartnerByBrand = Map.of(AppConstants.CHAAYOS_BRAND_ID,AppConstants.CHAAYOS_DINEIN_PARTNER_ID,
                        AppConstants.GNT_BRAND_ID,AppConstants.CHANNEL_PARTNER_ZOMATO,AppConstants.DOHFUL_BRAND_ID,AppConstants.DINE_IN_CHANNEL_PARTNER);
                for(Integer brandId : defaultPartnerByBrand.keySet()){
                    UnitPartnerBrandKey unitPartnerBrandKey = new UnitPartnerBrandKey(unitId,brandId,defaultPartnerByBrand.get(brandId));
                    PriceProfileKey priceProfileKey = priceProfileMap.get(unitPartnerBrandKey);
                    if(Objects.nonNull(priceProfileKey)){
                        LOG.info("PRICE PROFILE :: {} ", new Gson().toJson(priceProfileKey));
                        Map<ProductDimensionKey,BigDecimal> productPriceMapForBrand = masterDataCache.getProductPriceMapByProfile(priceProfileKey);
                        if (Objects.nonNull(productPriceMapForBrand)){
                            LOG.info("PRICE PROFILE size :: {} ", productPriceMapForBrand.size());
                            productPriceMap.putAll(productPriceMapForBrand);
                        }
                    }
                }
            }

            return MasterDataConverter.convertToProducts(unit, getAll,productPriceMap);
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find Unit Details with ID : %d", unitId), e);
        }
    }

	public TransactionMetadata getTransactionData(boolean isAndroid) throws DataNotFoundException {
		try {
			TransactionMetadata metadata = new TransactionMetadata();
			metadata.getCategories().addAll(getAllCategories(false));
			metadata.getBrandList().addAll(masterDataCache.getBrandMetaData().values());
			if (!isAndroid) {
				/*
				 * metadata.getAddOns().addAll(getAllAddons(false));
				 * metadata.setDiscountCodes(getDiscountCodes(false));
				 */
				metadata.getPaymentModes().addAll(getAllPaymentMode(PaymentCategory.OFFLINE));
				metadata.setCoreProducts(getActiveRecommendedProducts(AppConstants.RTL_CODE_CORE_PRODUCTS));
				metadata.setCategoryProducts(getActiveRecommendedProducts(AppConstants.RTL_CODE_CATEGORY_PRODUCTS));
			}
			metadata.setSubscriptionProductId(props.getSubcriptionProductType());
			return metadata;
		} catch (NoResultException e) {
			throw new DataNotFoundException("Did not find TransactionMetadata", e);
		}

	}

    @Override
    public TransactionMetadata getMetadataCategories() throws DataNotFoundException {
        try {
            TransactionMetadata metadata = new TransactionMetadata();
            metadata.getCategories().addAll(getAllCategories(false));
            metadata.setSubscriptionProductId(props.getSubcriptionProductType());
            return metadata;
        } catch (NoResultException e) {
            throw new DataNotFoundException("Did not find Transaction Metadata", e);
        }
    }

    public List<AddonList> getAllAddons(boolean getAll) throws DataNotFoundException {
        List<AddonList> addonList = new ArrayList<>();
        List<ListData> allRefLookups = getAllListData(AppConstants.RTL_GROUP_ADDONS, getAll);
        Map<Integer, AddonProductData> map = getAddonProductData();
        for (ListData data : allRefLookups) {
            addonList.add(MasterDataConverter.convert(data, map));
        }
        return addonList;
    }

    @SuppressWarnings("unchecked")
    private Map<Integer, AddonProductData> getAddonProductData() {
        Map<Integer, AddonProductData> map = new HashMap<>();
        Query query = manager.createQuery("FROM AddonProductData");
        List<AddonProductData> list = query.getResultList();
        for (AddonProductData data : list) {
            map.put(data.getAddonId(), data);
        }
        return map;

    }

    public List<ListData> getAllCategories(boolean getAll) throws DataNotFoundException {
        return getAllListData(AppConstants.RTL_GROUP_CATEGORY, getAll);
    }

    public ListData getDiscountCodes(boolean getAll) {
        return getListData(AppConstants.RTL_CODE_DISCOUNT_CODE, getAll);
    }

    public ListData getActiveRecommendedProducts(String code) {
        return getListData(code, false);
    }

    public List<ListData> getAllListData(String group, boolean getAll) throws DataNotFoundException {
        try {
            List<ListData> addOns = new ArrayList<>();
            List<RefLookupType> types = getLookupTypes(group);
            for (RefLookupType type : types) {
                addOns.add(MasterDataConverter.convert(type, getAll));
            }
            return addOns;
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find RefLookup Data with type %s", group), e);
        }

    }

    public Map<String, List<ListData>> getAllListData() throws DataNotFoundException {
        try {
            List<String> types = getAllLookupTypes();
            Map<String, List<ListData>> mapGroup = new HashMap<>();
            for (String refType : types) {
                List<ListData> refLookUps = getAllListData(refType, true);
                mapGroup.put(refType, refLookUps);
            }
            return mapGroup;
        } catch (NoResultException e) {
            throw new DataNotFoundException("Did not find RefLookup Data", e);
        }
    }

    public ListData upsertRefLookUp(ListData listData) throws DataNotFoundException {
        if (listData == null || listData.getDetail() == null) {
            throw new DataNotFoundException();
        }
        RefLookupType refType = manager.find(RefLookupType.class, listData.getDetail().getId());
        if (refType == null) {
            refType = insertRefLookUpType(listData.getDetail());
            listData.getDetail().setId(refType.getRtlId());
            listData.getDetail().setStatus(refType.getStatus());
        } else {
            updateRefLookUpType(refType, listData.getDetail());
        }
        if (listData.getContent() != null && !listData.getContent().isEmpty()) {
            for (IdCodeName idCodeName : listData.getContent()) {
                RefLookup refLookup = manager.find(RefLookup.class, idCodeName.getId());
                if (refLookup != null) {
                    updateRefLookUp(refLookup, idCodeName);
                } else {
                    idCodeName.setId(insertRefLookUp(idCodeName, refType));
                }
            }
        }
        return listData;
    }

    private RefLookupType updateRefLookUpType(RefLookupType refType, IdCodeName detail) {
        refType.setRtlName(detail.getName());
        refType.setRtlCode(detail.getCode());
        refType.setRtlGroup(detail.getType());
        refType.setStatus(detail.getStatus());
        manager.flush();
        return refType;
    }

    private RefLookupType insertRefLookUpType(IdCodeName detail) {
        RefLookupType refType = new RefLookupType();
        refType.setRtlCode(detail.getCode());
        refType.setRtlGroup(detail.getType());
        refType.setRtlName(detail.getName());
        refType.setStatus(AppConstants.ACTIVE.equalsIgnoreCase(detail.getStatus()) ? AppConstants.ACTIVE
            : AppConstants.IN_ACTIVE);
        manager.persist(refType);
        manager.flush();
        return refType;
    }

    private int insertRefLookUp(IdCodeName refLookUp, RefLookupType refType) {
        RefLookup ref = MasterDataConverter.convert(refLookUp, refType);
        manager.persist(ref);
        manager.flush();
        return ref.getRlId();
    }

    private RefLookup updateRefLookUp(RefLookup refLookup, IdCodeName idCodeName) {
        refLookup.setRlCode(idCodeName.getCode());
        refLookup.setRlName(idCodeName.getName());
        refLookup.setRlShortCode(idCodeName.getShortCode());
        refLookup.setRlStatus(AppConstants.ACTIVE.equalsIgnoreCase(idCodeName.getStatus()) ? AppConstants.ACTIVE
            : AppConstants.IN_ACTIVE);
        manager.flush();
        return refLookup;
    }

    public List<Product> getAllProducts() {
        List<Product> products = new ArrayList<>();
        List<Integer> mappedBrands = new ArrayList<>();
        StringBuilder queryString = new StringBuilder("FROM ProductDetail");
        if (RequestContext.isContextAvailable()) {
            mappedBrands = MasterUtil.getMappedBrands();
            queryString.append(" WHERE brandId IN :mappedBrands");
        }

        Query query = manager.createQuery(queryString.toString());
        if (RequestContext.isContextAvailable()) {
            query.setParameter("mappedBrands", mappedBrands);
        }

        @SuppressWarnings("unchecked")
        List<ProductDetail> details = query.getResultList();
        for (ProductDetail detail : details) {
            products.add(MasterDataConverter.convert(detail));
        }
        return products;
    }

    public ListData getListData(String code, boolean getAll) {

        try {
            RefLookupType type = getLookupType(code);
            return MasterDataConverter.convert(type, getAll);
        } catch (NoResultException e) {
            return MasterDataConverter.dummy(code);
        }

    }

    @SuppressWarnings("unchecked")
    private List<RefLookupType> getLookupTypes(String groupName) {
        Query query = manager.createQuery("FROM RefLookupType E where E.rtlGroup = :groupName order by E.rtlId");
        query.setParameter("groupName", groupName);
        return query.getResultList();

    }

    private RefLookupType getLookupType(String code) {
        Query query = manager.createQuery("FROM RefLookupType E where E.rtlCode = :code");
        query.setParameter("code", code);
        Object o = query.getSingleResult();
        return o == null ? null : (RefLookupType) query.getSingleResult();

    }

    @SuppressWarnings("unchecked")
    private List<String> getAllLookupTypes() {
        Query query = manager.createQuery("Select E.rtlGroup FROM RefLookupType E GROUP BY E.rtlGroup order by E.rtlGroup");
        return query.getResultList();
    }

    public List<PaymentMode> getAllPaymentMode(PaymentCategory category) {
        List<PaymentMode> results = new ArrayList<>();
        String queryString = "FROM com.stpl.tech.master.data.model.PaymentMode E where E.modeStatus <> :modeStatus";
        if (!PaymentCategory.ALL.equals(category)) {
            queryString = queryString + " and modeCategory = :category";
        }
        queryString = queryString + " order by E.paymentModeId";
        Query query = manager.createQuery(queryString);
        query.setParameter("modeStatus", AppConstants.IN_ACTIVE);
        if (!PaymentCategory.ALL.equals(category)) {
            query.setParameter("category", category.name());
        }
        @SuppressWarnings("unchecked")
        List<com.stpl.tech.master.data.model.PaymentMode> list = query.getResultList();
        for (com.stpl.tech.master.data.model.PaymentMode partner : list) {
            results.add(MasterDataConverter.convert(partner));
        }
        return results;
    }

    public Long getAllUnitCount() {
        Query query = manager.createQuery("SELECT COUNT(unitId) FROM UnitDetail");
        Long unitCount = (Long) query.getSingleResult();
        return unitCount != null ? unitCount : 0L;
    }


    public List<Unit> getAllUnits(int start, int batchSize) {

        List<Unit> units = new ArrayList<>();
        try {
            Query query = manager.createQuery("FROM UnitDetail order by unitId").setFirstResult(start)
                .setMaxResults(batchSize);
            @SuppressWarnings("unchecked")
            List<UnitDetail> o = query.getResultList();

            for (UnitDetail d : o) {
                IdCodeName cafeManager = null;
                if (d.getCafeManager() != null) {
                    cafeManager = new IdCodeName(d.getCafeManager(),
                        masterDataCache.getEmployees().get(d.getCafeManager()), null);
                }
                units.add(MasterDataConverter.convert(d, cafeManager, true,props));
            }

        } catch (Exception e) {
            LOG.error("Error while getting units", e);
        }
        return units;
    }


    public List<Unit> getAllUnits() {
        Query query = manager.createQuery("FROM UnitDetail order by unitId ");
        @SuppressWarnings("unchecked")
        List<UnitDetail> o = query.getResultList();
        List<Unit> units = new ArrayList<>();
        for (UnitDetail d : o) {
            IdCodeName cafeManager = null;
            if (d != null && d.getCafeManager() != null) {
                cafeManager = new IdCodeName(d.getCafeManager(), masterDataCache.getEmployees().get(d.getCafeManager()), null);
            }
            if (d != null) {
                units.add(MasterDataConverter.convert(d, cafeManager, true,props));
            }
        }
        return units;
    }

    public List<Unit> getAllUnits(UnitCategory category) {
        Query query = manager.createQuery("FROM UnitDetail where unitCategory = :unitCategory order by unitId");
        query.setParameter("unitCategory", category.name());
        @SuppressWarnings("unchecked")
        List<UnitDetail> o = query.getResultList();
        List<Unit> units = new ArrayList<>();
        for (UnitDetail d : o) {
            IdCodeName cafeManager = null;
            if (d.getCafeManager() != null) {
                cafeManager = new IdCodeName(d.getCafeManager(), masterDataCache.getEmployees().get(d.getCafeManager()), null);
            }
            units.add(MasterDataConverter.convert(d, cafeManager, false,props));
        }
        return units;
    }

    @Override
    public List<Division> getAllDivisions() {
        Query query = manager.createQuery("FROM BusinessDivision order by businessDivId");
        @SuppressWarnings("unchecked")
        List<BusinessDivision> o = query.getResultList();
        List<Division> divisions = new ArrayList<>();
        for (BusinessDivision d : o) {
            divisions.add(MasterDataConverter.convert(d));
        }
        return divisions;
    }

    @Override
    public List<Department> getAllDepartments() {
        Query query = manager.createQuery("FROM com.stpl.tech.master.data.model.Department order by deptId");
        @SuppressWarnings("unchecked")
        List<com.stpl.tech.master.data.model.Department> o = query.getResultList();
        List<Department> departments = new ArrayList<>();
        for (com.stpl.tech.master.data.model.Department department : o) {
            departments.add(MasterDataConverter.convert(department, true));
        }
        return departments;
    }

    @Override
    public List<Designation> getAllDesignations() {
        Query query = manager.createQuery("FROM com.stpl.tech.master.data.model.Designation order by designationId");
        @SuppressWarnings("unchecked")
        List<com.stpl.tech.master.data.model.Designation> o = query.getResultList();
        List<Designation> designations = new ArrayList<>();
        for (com.stpl.tech.master.data.model.Designation designation : o) {
            designations.add(MasterDataConverter.convert(designation));
        }
        return designations;
    }

    @Override
    public List<TaxProfile> getAllTaxProfile() {
        Query query = manager.createQuery(
            "FROM com.stpl.tech.master.data.model.TaxProfile where taxProfileStatus = :taxProfileStatus order by taxProfileId");
        query.setParameter("taxProfileStatus", "ACTIVE");
        @SuppressWarnings("unchecked")
        List<com.stpl.tech.master.data.model.TaxProfile> o = query.getResultList();
        List<TaxProfile> profiles = new ArrayList<>();
        for (com.stpl.tech.master.data.model.TaxProfile d : o) {
            profiles.add(MasterDataConverter.convert(d));
        }
        return profiles;
    }

    @Override
    public List<DenominationDetail> getAllDenominations() {
        Query query = manager.createQuery("FROM Denomination");
        @SuppressWarnings("unchecked")
        List<Denomination> o = query.getResultList();
        List<DenominationDetail> profiles = new ArrayList<>();
        for (Denomination d : o) {
            profiles.add(MasterDataConverter.convert(d));
        }
        return profiles;
    }

    @Override
    public List<EmployeeDetail> getAllEmployees() throws DataNotFoundException {
        Query query = manager.createQuery("FROM EmployeeDetail");
        //where employmentStatus = :employmentStatus
//        query.setParameter("employmentStatus","ACTIVE");
        @SuppressWarnings("unchecked")
        List<EmployeeDetail> o = query.getResultList();
        return o;
    }

    @Override
    public List<KioskCompanyDetailsData> getAllKioskCompanies() {
        return findAll(KioskCompanyDetailsData.class);
    }

    @Override
    public List<KioskMachineDetailsData> getAllKioskMachines() {
        return findAll(KioskMachineDetailsData.class);
    }

    @Override
    public List<KioskLocationDetailsData> getAllKioskLocations() {
        return findAll(KioskLocationDetailsData.class);
    }

    @Override
    public List<PaymentModeAttributes> getPaymentModeAttributes(int paymentModeId) {
        Query query = manager.createQuery(
            "FROM PaymentModeAttributes E where E.paymentModeId = :paymentModeId and E.attributeStatus = :attributeStatus");
        query.setParameter("attributeStatus", AppConstants.ACTIVE);
        query.setParameter("paymentModeId", paymentModeId);
        return query.getResultList();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.data.dao.MasterMetadataDao#getAllLocations()
     */
    @Override
    public List<Location> getAllLocations() {
        List<Location> all = new ArrayList<>();
        Query query = manager.createQuery("FROM LocationDetail E where E.status = :status");
        query.setParameter("status", AppConstants.ACTIVE);
        List<LocationDetail> locations = query.getResultList();
        for (LocationDetail detail : locations) {
            all.add(MasterDataConverter.convert(detail));
        }
        return all;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.data.dao.MasterMetadataDao#getAllStates()
     */
    @Override
    public List<State> getAllStates() {
        List<State> all = new ArrayList<>();
        Query query = manager.createQuery("FROM StateDetail");
        List<StateDetail> states = query.getResultList();
        for (StateDetail detail : states) {
            all.add(MasterDataConverter.convert(detail));
        }
        return all;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.data.dao.MasterMetadataDao#getAllCancellationReasons
     * ()
     */
    @Override
    public List<CancellationReason> getAllCancellationReasons() {
        List<CancellationReason> all = new ArrayList<>();
        Query query = manager.createQuery("FROM CancellationReasonData E where E.status = :status");
        query.setParameter("status", AppConstants.ACTIVE);
        List<CancellationReasonData> reasons = query.getResultList();
        for (CancellationReasonData detail : reasons) {
            all.add(MasterDataConverter.convert(detail));
        }
        return all;
    }

    @Override
    public List<Company> getAllCompanies() {
        List<Company> list = new ArrayList<>();
        Query query = manager.createQuery("FROM CompanyDetail");
        List<CompanyDetail> companies = query.getResultList();
        for (CompanyDetail c : companies) {
            list.add(MasterDataConverter.convert(c));
        }
        return list;
    }

    @Override
    public List<Address> getAllAddress(){
        List<Address> list = new ArrayList<>();
        Query query = manager.createQuery("FROM AddressInfo");
        List<AddressInfo> addresses = query.getResultList();
        for(AddressInfo address : addresses){
            list.add(MasterDataConverter.convert(address));
        }
        return list;
    }
    @Override
    public Map<Integer, BigDecimal> getAllPaymentModeCommisson() {
        Map<Integer, BigDecimal> map = new HashMap<>();
        String queryString = "FROM com.stpl.tech.master.data.model.PaymentMode E where E.modeStatus <> :modeStatus";
        Query query = manager.createQuery(queryString);
        query.setParameter("modeStatus", AppConstants.IN_ACTIVE);
        @SuppressWarnings("unchecked")
        List<com.stpl.tech.master.data.model.PaymentMode> list = query.getResultList();
        for (com.stpl.tech.master.data.model.PaymentMode partner : list) {
            map.put(partner.getPaymentModeId(), partner.getCommissionRate());
        }
        return map;
    }

    @Override
    public List<ExpenseMetadata> getAllExpenseList() {

        List<ExpenseMetadata> list = new ArrayList<>();
        List<ExpenseMetadataDetail> detailList = null;
        Query query = manager
                .createQuery("From ExpenseMetadataDetail where status = :status");
        query.setParameter("status", AppConstants.ACTIVE);
        detailList = query.getResultList();
        if (detailList != null) {
            detailList.forEach(data -> list.add(MasterDataConverter.convert(data)));
        }
        return list;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<Integer, List<ExpenseValidation>> getExpenseValidations(List<Integer> expenseIds) {

        Query query = manager
            .createQuery("From ExpenseValidationMappingData e where e.expenseId IN (:expenseIds) and e.mappingStatus = :status");
        query.setParameter("expenseIds", expenseIds);
        query.setParameter("status", AppConstants.ACTIVE);
        List<ExpenseValidationMappingData> validationMappingDataList = query.getResultList();

        Set<Integer> validations = new HashSet<>();
        for (ExpenseValidationMappingData data : validationMappingDataList) {
            validations.add(data.getValidationId());
        }
        Map<Integer, List<ExpenseValidation>> map = new HashMap<>();
        Map<Integer, ExpenseValidationDetail> integerExpenseValidationDetailMap = new HashMap<>();
        if (!validations.isEmpty()) {
            query = manager
                .createQuery("From ExpenseValidationDetail v where v.id IN (:validations) and v.validationStatus = :status");
            query.setParameter("validations", validations);
            query.setParameter("status", AppConstants.ACTIVE);
            List<ExpenseValidationDetail> expenseValidationDetails = query.getResultList();
            for (ExpenseValidationDetail detail : expenseValidationDetails) {
                integerExpenseValidationDetailMap.put(detail.getId(), detail);
            }
            for (ExpenseValidationMappingData data : validationMappingDataList) {
                List<ExpenseValidation> expenseValidations = map.get(data.getExpenseId());
                if (expenseValidations == null) {
                    expenseValidations = new ArrayList<>();
                }
                expenseValidations.add(MasterDataConverter.convert(integerExpenseValidationDetailMap.get(data.getValidationId())));
                map.put(data.getExpenseId(), expenseValidations);
            }
        }
        return map;
    }

    @Override
    public List<WarningMetadata> getWarningReasonList(String type) {

        List<WarningMetadata> list = new ArrayList<>();
        List<WarningReasonDetail> reasonList = null;
        StringBuilder stringBuilder = new StringBuilder("From WarningReasonDetail where status = :status ");
        if (type.equals(AppConstants.ZTZ)) {
            stringBuilder.append(" and warningType = 'L3'");
        } else if (type.equals("CRITICAL")) {
            stringBuilder.append(" and warningType <> 'L3'");
        }
        Query query = manager.createQuery(stringBuilder.toString());
        query.setParameter("status", AppConstants.ACTIVE);
        reasonList = query.getResultList();
        if (reasonList != null) {
            reasonList.forEach(data -> list.add(MasterDataConverter.convert(data)));
        }
        return list;
    }

    @Override
    public List<Unit> getUnitsOfAreaManager(Integer amId) {
        Query query = manager.createQuery("FROM UnitDetail where unitCategory = :unitCategory and unitManager.empId = :amId order by unitId");
        query.setParameter("unitCategory", UnitCategory.CAFE.name());
        query.setParameter("amId", amId);
        @SuppressWarnings("unchecked")
        List<UnitDetail> o = query.getResultList();
        List<Unit> units = new ArrayList<>();
        for (UnitDetail d : o) {
            IdCodeName cafeManager = new IdCodeName(d.getCafeManager(), masterDataCache.getEmployee(d.getCafeManager()), "");
            units.add(MasterDataConverter.convert(d, cafeManager, true,props));
        }
        return units;
    }

    @Override
    public List<Unit> getUnitsOfCafeManager(Integer amId) {
        Query query = manager.createQuery("FROM UnitDetail where unitCategory = :unitCategory and cafeManager = :amId order by unitId");
        query.setParameter("unitCategory", UnitCategory.CAFE.name());
        query.setParameter("amId", amId);
        @SuppressWarnings("unchecked")
        List<UnitDetail> o = query.getResultList();
        List<Unit> units = new ArrayList<>();
        for (UnitDetail d : o) {
            IdCodeName cafeManager = new IdCodeName(d.getCafeManager(), masterDataCache.getEmployee(d.getCafeManager()), "");
            units.add(MasterDataConverter.convert(d, cafeManager, true,props));
        }
        return units;
    }

    @Override
    public ChannelPartnerCommission getChannelPartnerCommission(int partnerId, Date businessDate) throws DataNotFoundException {
        Query query = manager.createQuery("FROM ChannelPartnerCommission E where E.partnerId = :partnerId and E.startDate <= :businessDate and E.endDate >= :businessDate order by partnerCommissionId desc");
        query.setParameter("partnerId", partnerId);
        query.setParameter("businessDate", businessDate);
        List<ChannelPartnerCommission> list = query.getResultList();
        if (list != null && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

	@Override
	public Collection<TrimmedProductVO> getActiveProductMappingsForUnits(Integer primaryUnitId, Set<Integer> unitIds) {
		Map<Integer, TrimmedProductVO> result = new HashMap<>();
		Query query = manager.createQuery("select upm.productDetail.productId," + " upm.productDetail.productName,"
				+ " upm.productDetail.isInventoryTracked," + " upm.productDetail.productStatus,"
				+ " upm.productDetail.productType.rtlId," + " upm.unitDetail.unitId," + " upp.refLookup.rlCode,"
				+ " upp.recipeProfile,"+"upm.productDetail.brandId" + " FROM UnitProductMapping upm, UnitProductPricing upp where "
				+ " upp.unitProductMapping.unitProdRefId = upm.unitProdRefId and " + " upm.productStatus = :active and "
				+ " upp.status = :active and " + " upm.productDetail.productStatus = :active and "
				+ " upm.unitDetail.unitId in (:unitIds)");
		query.setParameter("active", AppConstants.ACTIVE);
		query.setParameter("unitIds", unitIds);
		List<Object[]> list = query.getResultList();
		if (list == null) {
			LOG.info("Did not find any product pricing mapping for units {} ", unitIds);
			return result.values();
		} else {
			for (Object[] array : list) {
				if ((Integer) array[5] == primaryUnitId) {
					if (!result.containsKey((Integer) array[0])) {
						result.put((Integer) array[0],
								new TrimmedProductVO((Integer) array[0], (String) array[1],
										ProductStatus.valueOf((String) array[3]), AppConstants.getValue((String) array[2]),
										(Integer) array[4], new HashMap<>(),(Integer) array [8]));
					}
					result.get((Integer) array[0]).getPrices().put((String) array[6],
							new TrimmedProductPrice((String) array[6], (String) array[7], (Integer) array[5]));
				}
			}

			for (Object[] array : list) {
				if ((Integer) array[5] != primaryUnitId) {
					if (!result.containsKey((Integer) array[0])) {
						result.put((Integer) array[0],
								new TrimmedProductVO((Integer) array[0], (String) array[1],
										ProductStatus.valueOf((String) array[3]), AppConstants.getValue((String) array[2]),
										(Integer) array[4], new HashMap<>(),(Integer) array [8]));
					}
					if (!result.get((Integer) array[0]).getPrices().containsKey((String) array[6])) {
						result.get((Integer) array[0]).getPrices().put((String) array[6],
								new TrimmedProductPrice((String) array[6], (String) array[7], (Integer) array[5]));
					}
				}
			}

		}
		return result.values();
	}

    @Override
    public Collection<TrimmedProductVO> getActiveProductMappingsForUnitsV1(Integer primaryUnitId, Set<Integer> unitIds){
        Map<Integer, TrimmedProductVO> result = new HashMap<>();
        Query query = manager.createQuery("select upm.productDetail.productId," + " upm.productDetail.productName,"
                + " upm.productDetail.isInventoryTracked," + " upm.productDetail.productStatus,"
                + " upm.productDetail.productType.rtlId," + " upm.unitDetail.unitId," + " upp.refLookup.rlCode,"
                + " upp.recipeProfile,"+"upm.productDetail.brandId" + " FROM UnitProductMapping upm, UnitProductPricing upp where "
                + " upp.unitProductMapping.unitProdRefId = upm.unitProdRefId and " + " upm.productStatus = :active and "
                + " upp.status = :active and " + " upm.productDetail.productStatus = :active and "
                + "upm.productDetail.isInventoryTracked = :yes and"
                + " upm.unitDetail.unitId in (:unitIds)");
        query.setParameter("active", AppConstants.ACTIVE);
        query.setParameter("unitIds", unitIds);
        query.setParameter("yes",AppConstants.YES);
        List<Object[]> list = query.getResultList();
        if (list == null) {
            LOG.info("Did not find any product pricing mapping for units {} ", unitIds);
            return result.values();
        } else {
            for (Object[] array : list) {
                if ((Integer) array[5] == primaryUnitId) {
                    if (!result.containsKey((Integer) array[0])) {
                        result.put((Integer) array[0],
                                new TrimmedProductVO((Integer) array[0], (String) array[1],
                                        ProductStatus.valueOf((String) array[3]), AppConstants.getValue((String) array[2]),
                                        (Integer) array[4], new HashMap<>(),(Integer) array [8]));
                    }
                    result.get((Integer) array[0]).getPrices().put((String) array[6],
                            new TrimmedProductPrice((String) array[6], (String) array[7], (Integer) array[5]));
                }
            }

            for (Object[] array : list) {
                if ((Integer) array[5] != primaryUnitId) {
                    if (!result.containsKey((Integer) array[0])) {
                        result.put((Integer) array[0],
                                new TrimmedProductVO((Integer) array[0], (String) array[1],
                                        ProductStatus.valueOf((String) array[3]), AppConstants.getValue((String) array[2]),
                                        (Integer) array[4], new HashMap<>(),(Integer) array [8]));
                    }
                    if (!result.get((Integer) array[0]).getPrices().containsKey((String) array[6])) {
                        result.get((Integer) array[0]).getPrices().put((String) array[6],
                                new TrimmedProductPrice((String) array[6], (String) array[7], (Integer) array[5]));
                    }
                }
            }

        }
        return result.values();
    }


    @Override
    public List<RegionMap> getAllRegions(){
        Query query = manager.createQuery("FROM RegionMap E ");
        List<RegionMap> list = query.getResultList();
        return list;
    }

    @Override
    public List<String> getDimensionById(int id) {
        try {
            Query query = manager.createNativeQuery("SELECT RL_NAME FROM REF_LOOKUP WHERE RTL_ID = :id");
            query.setParameter("id",id);
            List<Object> objs = query.getResultList();
            List<String> dimensions = new ArrayList<>();
            for(Object obj : objs){
                dimensions.add((String) obj);
            }
            return dimensions;
        }catch (Exception e){
            LOG.error("Error while fetching dimension name for lookup type id : {}",id,e);
        }
        return new ArrayList<>();
    }

    public List<String> getDelayReasonListData() throws DataNotFoundException {
        try {
            Query query = manager.createQuery("SELECT R.RL_NAME from REF_LOOKUP R INNER JOIN REF_LOOKUP_TYPE T ON " +
                    "R.RTL_ID=T.RTL_ID WHERE T.RTL_GROUP = :group  ");
            query.setParameter("group", ListTypes.RIDER_DELAY_REASONS);
            List<String> delayReasons = query.getResultList();
            return delayReasons;

        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find RefLookup Data with type delay reasons"), e);
        }
    }

    public List<UnitContactDetailsData> getAllUnitContactDetails() throws DataNotFoundException{
        try {
            Query query = manager.createQuery(" FROM UnitContactDetailsData WHERE status = :status");
            query.setParameter("status",AppConstants.ACTIVE);
            return query.getResultList();
        } catch (NoResultException e){
            throw new DataNotFoundException(String.format("Did not find Unit Contact Details Data."), e);
        }
    }

    @Override
    public List<String> getCompensationReasonListData() throws DataNotFoundException{
        try {
            Query query = manager.createQuery("SELECT R.rlName from RefLookup R INNER JOIN RefLookupType T ON " +
                    "R.refLookupType.rtlId=T.rtlId WHERE T.rtlGroup = :group");
            query.setParameter("group", ListTypes.COMPENSATION_REASONS.getGroup());
            List<String> delayReasons = query.getResultList();
            return delayReasons;

        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find RefLookup Data with type Compensation reasons"), e);
        }
    }

    @Override
    public  MenuSequenceData getMenuSequenceByMenuExcelIdAndDaySLot(Integer excelMenuSequenceId ,  String dayslot){
        try {
            Query query = manager.createQuery("SELECT m from MenuSequenceData m  WHERE m.menuExcelSequenceId = :menuExcelSequenceId " +
                    " and m.menuType = :daySlot ");
            query.setParameter("menuExcelSequenceId", excelMenuSequenceId).setParameter("daySlot",dayslot);
            return (MenuSequenceData) query.getSingleResult();
        } catch (NoResultException e) {
             return  null;
        }
    }

    @Override
    public List<ProductGroupData> getAllGroupsByTagAndNameAndGroupType(String groupTag , List<String> groupNames , String groupType){
        List<ProductGroupData> productGroupDataList = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT m from ProductGroupData m  WHERE m.groupTag = :groupTag " +
                    " and m.groupName in :groupNames and m.groupType = :groupType ");
            query.setParameter("groupTag", groupTag).setParameter("groupNames",groupNames).
                    setParameter("groupType",groupType);
            productGroupDataList =  query.getResultList();
        } catch (NoResultException e) {
        }
        return productGroupDataList;
    }

    @Override
    public List<MenuSequenceMappingData>  getAllGroupsMappingsByMenuSequenceId(Integer menuSequenceId){
        List<MenuSequenceMappingData> menuGroupDataList = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT m from MenuSequenceMappingData m  WHERE m.menuSequenceId = :menuSequenceId ");
            query.setParameter("menuSequenceId", menuSequenceId);
            menuGroupDataList =  query.getResultList();
        } catch (NoResultException e) {
        }
        return menuGroupDataList;
    }


    @Override
    public List<UnitGroupMappingData> getUnitGroupMappings(List<Integer> groupIds){
        List<UnitGroupMappingData> unitGroupMappingDataList = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT m from UnitGroupMappingData m  WHERE m.groupId in :groupIds ");
            query.setParameter("groupIds", groupIds);
            unitGroupMappingDataList =  query.getResultList();
        } catch (NoResultException e) {
        }
        return unitGroupMappingDataList;
    }

    @Override
    public List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings(List<Integer> unitChannelPartnerIds){
        List<UnitChannelPartnerMenuMappingData> unitChannelPartnerMenuMappingData = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT m from UnitChannelPartnerMenuMappingData m  WHERE m.unitPartnerMappingId in :unitChannelPartnerIds " +
                    " and m.status = :status ");
            query.setParameter("unitChannelPartnerIds", unitChannelPartnerIds).setParameter("status",AppConstants.ACTIVE);
            unitChannelPartnerMenuMappingData =  query.getResultList();
        } catch (NoResultException e) {
        }
        return unitChannelPartnerMenuMappingData;
    }

    @Override
    public List<MenuSequenceData> getMenuSequenceDataByExcelSequenceId(Integer excelMenuSequenceId){
        List<MenuSequenceData> menuSequenceDataList = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT m from MenuSequenceData m  WHERE m.menuExcelSequenceId = :excelMenuSequenceId ");
            query.setParameter("excelMenuSequenceId", excelMenuSequenceId);
            menuSequenceDataList =  query.getResultList();
        } catch (NoResultException e) {
        }
        return menuSequenceDataList;
    }

    @Override
    public List<GroupRecommendationMapping> getAllProductRecommendationMappingByGroup(Integer groupId , Integer brandId , Integer partnerId
    , List<String> daySlots){
        List<GroupRecommendationMapping> groupRecommendationMappingList = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT m from GroupRecommendationMapping m  WHERE m.groupId = :groupId" +
                    " and m.partnerId = :partnerId and m.brandId = :brandId and m.daySlot in :daySlots ");
            query.setParameter("groupId", groupId).setParameter("partnerId",partnerId).setParameter("brandId",brandId)
                    .setParameter("daySlots", daySlots);
            groupRecommendationMappingList =  query.getResultList();
        } catch (NoResultException e) {
        }
        return groupRecommendationMappingList;
    }

    @Override
    public List<MenuExcelUploadEvent> getAllActiveMenuExcelUploadEvents(String menuApp){
        List<MenuExcelUploadEvent> menuExcelUploadEvents = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT m from MenuExcelUploadEvent m  WHERE m.menuApp = :menuApp" +
                    " and m.status = :status ");
            query.setParameter("menuApp", menuApp).setParameter("status",AppConstants.ACTIVE);
            menuExcelUploadEvents =  query.getResultList();
        } catch (NoResultException e) {
        }
        return menuExcelUploadEvents;
    }

    @Override
    public  UnitChannelPartnerMenuMappingData getChannelPartnerMenuMapping(Integer unitChannelPartnerMappingId,
                                                                           Integer menuSequenceId , String menuApp , String daySlot
       , Integer brandId){
        try {
            Query query = manager.createQuery("SELECT m from UnitChannelPartnerMenuMappingData m  WHERE m.unitPartnerMappingId = :unitChannelPartnerMappingId" +
                    " and m.menuSequenceId = :menuSequenceId and m.menuApp = :menuApp and m.menuType = :menuType and m.brandId = :brandId ");
            query.setParameter("menuSequenceId", menuSequenceId).setParameter("unitChannelPartnerMappingId",unitChannelPartnerMappingId)
                    .setParameter("menuApp",menuApp).setParameter("menuType",daySlot).setParameter("brandId",brandId);
            return (UnitChannelPartnerMenuMappingData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public List<RefLookup> getRefLookUpByIds(List<Integer> rlIds){
        List<RefLookup> refLookups = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT m from RefLookup m  WHERE m.rlId in :rlIds ");
            query.setParameter("rlIds", rlIds);
            refLookups =  query.getResultList();
        } catch (NoResultException e) {
        }
        return refLookups;
    }

    @Override
    public List<PriceProfileProductMapping> getPriceProfileProductMappings(
            List<Integer> versionNos , List<Integer> profileIds , List<Integer> productIds
            ,List<Integer> dimensionIds){
        StringBuilder queryString = new StringBuilder("SELECT p FROM PriceProfileProductMapping p WHERE 1=1");

        if (versionNos != null && !versionNos.isEmpty()) {
            queryString.append(" AND p.version IN :versionNos");
        }
        if (profileIds != null && !profileIds.isEmpty()) {
            queryString.append(" AND p.priceProfileData.priceProfileDataId IN :profileIds");
        }
        if (productIds != null && !productIds.isEmpty()) {
            queryString.append(" AND p.productId IN :productIds");
        }
        if (dimensionIds != null && !dimensionIds.isEmpty()) {
            queryString.append(" AND p.dimensionCode.rlId IN :dimensionIds");
        }

        Query query = manager.createQuery(queryString.toString());

        if (versionNos != null && !versionNos.isEmpty()) {
            query.setParameter("versionNos", versionNos);
        }
        if (profileIds != null && !profileIds.isEmpty()) {
            query.setParameter("profileIds", profileIds);
        }
        if (productIds != null && !productIds.isEmpty()) {
            query.setParameter("productIds", productIds);
        }
        if (dimensionIds != null && !dimensionIds.isEmpty()) {
            query.setParameter("dimensionIds", dimensionIds);
        }

        List<PriceProfileProductMapping> list = query.getResultList();
        if (list != null && !list.isEmpty()) {
            return list;
        }

        return new ArrayList<>();
    }

    public List<MachineProductMappingMetaData> getMachineProductMappingData() {
        try {
            Query query = manager.createQuery("SELECT mpm FROM MachineProductMappingMetaData mpm " +
                    "WHERE mpm.machineName <> :machineName");
            query.setParameter("machineName", "Others");
            return query.getResultList();
        } catch (NoResultException e) {
            LOG.info("Error while fetching Machine Product Mapping ");
        }
        return new ArrayList<>();
    }

    @Override
    public Map<String, List<ListData>> getListTypesByType(List<String> refTypes) throws DataNotFoundException {
        try {
            Map<String, List<ListData>> mapGroup = new HashMap<>();
            for (String refType : refTypes) {
                mapGroup.put(refType, getAllListData(refType, true));
            }
            return mapGroup;
        } catch (NoResultException  | DataNotFoundException e) {
            throw new DataNotFoundException("Did not find RefLookup Data", e);
        }
    }

}
