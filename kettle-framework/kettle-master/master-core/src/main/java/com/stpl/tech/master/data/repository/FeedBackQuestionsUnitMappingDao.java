package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.FeedbackQuestionsUnitMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FeedBackQuestionsUnitMappingDao extends JpaRepository<FeedbackQuestionsUnitMapping,Integer> {
    List<FeedbackQuestionsUnitMapping> findByQuestionId(Integer questionId);

    List<FeedbackQuestionsUnitMapping> findByQuestionIdAndMappingStatus(Integer questionId,String mappingStatus);

    List<FeedbackQuestionsUnitMapping> findByQuestionTypeAndUnitNameAndMappingStatus(String questionType,String unitName,String mappingStatus);


}
