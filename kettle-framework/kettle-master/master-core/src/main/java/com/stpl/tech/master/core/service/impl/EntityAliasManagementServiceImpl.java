/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.EntityAliasManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.BrandManagementDao;
import com.stpl.tech.master.data.dao.EntityAliasManagementDao;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.domain.model.EntityAliasKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class EntityAliasManagementServiceImpl implements EntityAliasManagementService {
    private static final Logger LOG = LoggerFactory.getLogger(EntityAliasManagementServiceImpl.class);

    @Autowired
    private EntityAliasManagementDao dao;

    @Autowired
    MasterDataCache masterCache;


    @Autowired
    private MasterProperties props;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EntityAliasMappingData> getAllEntityAlias() {
        return dao.findAll(EntityAliasMappingData.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public EntityAliasMappingData addEntityAlias(EntityAliasMappingData entityAliasMappingData) {
        entityAliasMappingData = dao.add(entityAliasMappingData);
        EntityAliasKey entityAliasKey = MasterDataConverter.convert(entityAliasMappingData);
        masterCache.getEntityAliasMappingData().put(entityAliasKey, entityAliasMappingData);
        return entityAliasMappingData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public EntityAliasMappingData updateEntityAlias(EntityAliasMappingData entityAliasMappingData) {
        entityAliasMappingData = dao.update(entityAliasMappingData);
        EntityAliasKey entityAliasKey = MasterDataConverter.convert(entityAliasMappingData);
        masterCache.getEntityAliasMappingData().put(entityAliasKey, entityAliasMappingData);
        return entityAliasMappingData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<EntityAliasMappingData> addAllEntityAlias(List<EntityAliasMappingData> entityAliasMappingData) {
        List<EntityAliasMappingData> mappingData = new ArrayList<EntityAliasMappingData>();
        for(EntityAliasMappingData data : entityAliasMappingData) {
            data = dao.add(data);
            EntityAliasKey entityAliasKey = MasterDataConverter.convert(data);
            masterCache.getEntityAliasMappingData().put(entityAliasKey, data);
            mappingData.add(data);
        }
        return mappingData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EntityAliasMappingData> getEntityAliasMappingByIdandType(int entityId, String entityType) {
        List<EntityAliasMappingData> entityAliasMappingDataList = dao.getEntityAliasByIdandType(entityId, entityType);
        if(entityAliasMappingDataList == null) {
            entityAliasMappingDataList = new ArrayList<EntityAliasMappingData>();
        }
        return entityAliasMappingDataList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public EntityAliasMappingData getEntityAliasMappingByIdandTypeandbrand(int entityId, String entityType, int brandId) {
        EntityAliasKey entityAliasKey = new EntityAliasKey(entityId, entityType, brandId);
        EntityAliasMappingData aliasMappingData = masterCache.getEntityAliasMappingData().get(entityAliasKey);
        return aliasMappingData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EntityAliasMappingData> getEntityMappingByEntityKey(List<EntityAliasKey> entityAliasKeys) {
        List<EntityAliasMappingData> list = new ArrayList<EntityAliasMappingData>();
        for(EntityAliasKey key : entityAliasKeys) {
            EntityAliasMappingData aliasMappingData = masterCache.getEntityAliasMappingData().get(key);
            if(aliasMappingData != null){
                list.add(aliasMappingData);
            }
        }
        return list;
    }


}