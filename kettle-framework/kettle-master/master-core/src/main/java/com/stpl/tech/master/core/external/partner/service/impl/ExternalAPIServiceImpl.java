package com.stpl.tech.master.core.external.partner.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.interceptor.ExternalAPITokenCache;
import com.stpl.tech.master.core.external.partner.dao.ExternalAPIDao;
import com.stpl.tech.master.core.external.partner.service.ExternalAPIService;
import com.stpl.tech.master.data.model.ExternalPartnerInfo;
import com.stpl.tech.master.data.model.PartnerPermissionMapping;
import com.stpl.tech.util.AppConstants;

@Service
public class ExternalAPIServiceImpl implements ExternalAPIService {

    @Autowired
    private ExternalAPIDao dao;

    @Autowired
    private ExternalAPITokenCache externalAPICache;

    @Autowired
    private TokenService<ExternalAPIToken> tokenService;

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addPartner(String partnerName, String envType) {
        dao.addPartner(partnerName, envType);
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExternalPartnerInfo> getAllActivePartners() {
        return dao.getAllActivePartners();
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void addAccessAPISToToken(IMap<String, ExternalAPIToken> tokenMap) {
        tokenMap.forEach((s, externalAPIToken) -> {
            externalAPIToken.setAccessAPIs(getPermissionMap(dao.getAllPartnerMappings(), s));
            tokenMap.put(s, externalAPIToken);
        });
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshExternalAPICache() {
        externalAPICache.clearCache();
        for (ExternalPartnerInfo info : getAllActivePartners()) {
            ExternalAPIToken token = new ExternalAPIToken();
            tokenService.parseToken(token, info.getApiKey());
            externalAPICache.addToExternalPartnerToken(info.getApiKey(), token);
        }
        addAccessAPISToToken(externalAPICache.getTokenMap());
    }

    private Map<String,Integer> getPermissionMap(List<PartnerPermissionMapping> partnerPermissionMappings, String apiKey){
        Map<String,Integer> mappingMap = new HashMap<>();
        partnerPermissionMappings.forEach(partnerPermissionMapping -> {
            if(partnerPermissionMapping.getPartner().getApiKey().equals(apiKey)){
                if(partnerPermissionMapping.getAcl().getStatus().equals(AppConstants.ACTIVE) && partnerPermissionMapping.getStatus().equals(AppConstants.ACTIVE))
                    mappingMap.put(partnerPermissionMapping.getAcl().getModule(), partnerPermissionMapping.getPermission());
            }
        });
        return mappingMap;
    }
}
