package com.stpl.tech.master.data.model;

import javax.persistence.*;

@Entity
@Table(name = "REGION_MAP")
public class RegionMap {

    @Id
    @Column(name = "ID")
    private Integer id;


    @Column(name = "UNIT_REGION")
    private String unitRegion;

    @Column(name = "REGION_VALUE")
    private String regionValue;


    public String getUnitRegion() {
        return this.unitRegion;
    }

    public void setUnitRegion(String unitRegion) {
        this.unitRegion = unitRegion;
    }

    public String getRegionValue() {
        return this.regionValue;
    }

    public void setRegionValue(String regionValue) {
        this.regionValue = regionValue;
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
