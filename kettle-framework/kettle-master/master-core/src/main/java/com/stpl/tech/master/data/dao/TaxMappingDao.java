package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.tax.model.CategoryAdditionalTax;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxInfo;

import java.util.List;
import java.util.Map;

/**
 * Created by Mohit
 */
public interface TaxMappingDao extends AbstractDao {

	public List<TaxCategory> getAllCategory();

	public TaxCategory addCategoryData(TaxCategory category);

	public TaxCategory updateCategoryData(TaxCategory category);

	public boolean addTaxData(TaxInfo info);

	public List<TaxInfo> getAllTaxes();

	public CategoryTax updateCategoryTax(CategoryTax categoryTax);

	public CategoryTax fetchCategoryTax(int countryId, int categoryId, int taxId, boolean getForAllStates);

	public CategoryAdditionalTax fetchCategoryAdditionalTax(int countryId, int categoryId, int taxId);

	public CategoryAdditionalTax updateCategoryAdditionalTax(CategoryAdditionalTax categoryTax);

	public List<IdCodeName> getAllCategoryBasicInfo();

	public List<IdCodeName> getAllTaxesBasicInfo();

	public TaxCategory getTaxCategory(int id);

	public List<IdCodeName> getAllCountries();

	public List<CategoryTax> fetchAllCategoryTax();

	/**
	 * @return
	 */
	public Map<String, CategoryAdditionalTax> fetchAllCategoryAdditionalTax();

	/**
	 * @param countryId
	 * @param categoryId
	 * @return
	 */
	public CategoryTax fetchAllCategoryTax(int countryId, int categoryId);

	/**
	 * @param countryId
	 * @param categoryId
	 * @return
	 */
	public CategoryAdditionalTax fetchCategoryAdditionalTax(int countryId, int categoryId);

}
