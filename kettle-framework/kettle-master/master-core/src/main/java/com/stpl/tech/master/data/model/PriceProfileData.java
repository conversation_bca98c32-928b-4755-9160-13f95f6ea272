package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PRICE_PROFILE_DATA")
public class PriceProfileData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRICE_PROFILE_DATA_ID")
    private Integer priceProfileDataId;

    @Column(name = "PRICE_PROFILE_NAME", length = 300)
    private String priceProfileName;


    @Column(name = "CHANNEL_PARTNER_ID")
    private Integer channelPartnerId;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "STATUS", length = 45)
    private String status;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "UPDATE_BY")
    private Integer updateBy;

    @OneToMany(mappedBy = "priceProfileData", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PriceProfileVersion> priceProfileVersions;

    public PriceProfileData(Integer id){
        this.priceProfileDataId = id;
    }
}

