package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.ProductCondimentGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SourceToCondimentMappingDao extends JpaRepository<ProductCondimentGroup,Integer> {

    List<ProductCondimentGroup> findAllByStatus(String status);

}
