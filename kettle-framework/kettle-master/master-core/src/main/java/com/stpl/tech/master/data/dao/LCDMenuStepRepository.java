package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.LCDMenuStep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface LCDMenuStepRepository extends JpaRepository<LCDMenuStep, Long> {
    List<LCDMenuStep> findByStatusOrderByStepOrderAsc(String status);
    Optional<LCDMenuStep> findByStepName(String stepName);
}
