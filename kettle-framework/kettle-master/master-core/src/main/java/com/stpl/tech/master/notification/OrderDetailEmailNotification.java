package com.stpl.tech.master.notification;

import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class OrderDetailEmailNotification extends EmailNotification {

    private EnvType envType;
    private Map<String,Object> request;
    private List<String> toEmails;
    private String sourceOrderId;
    private BCXOrderDetailTemplate template;
    private String type;

    public OrderDetailEmailNotification(Map<String,Object> request,String sourceOrderId,String type,List<String> toEmails,EnvType envType,
                                        BCXOrderDetailTemplate template){
        this.request = request;
        this.sourceOrderId = sourceOrderId;
        this.toEmails = toEmails;
        this.envType = envType;
        this.template = template;
        this.type = type;
    }


    @Override
    public String[] getToEmails() {
        if (AppUtils.isDev(envType)) {
            return new String[]{"<EMAIL>"};
        } else {
            if (Objects.nonNull(toEmails) && toEmails.size() > 0) {
                toEmails.add(request.get("UNIT_EMAIL").toString());
                toEmails.add(request.get("CAFE_MANAGER_ID").toString());
                toEmails.add(request.get("UNIT_MANAGER_ID").toString());
            }
            return this.toEmails.toArray(new String[0]);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "["+ request.get("UNIT_REGION").toString().toUpperCase() + "] " + request.get("BRAND_NAME").toString()
                +" " + request.get("PARTNER_CODE").toString() + " " + request.get("COMPLAINT_TYPE") +" received for " + request.get("UNIT_NAME").toString() +
                " and id " + sourceOrderId;
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
