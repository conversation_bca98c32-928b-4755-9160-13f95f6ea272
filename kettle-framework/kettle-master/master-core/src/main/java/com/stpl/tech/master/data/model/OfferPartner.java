/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "OFFER_PARTNERS")
public class OfferPartner implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4794380425785597396L;

	private int mappingId;
	private MarketingPartner partner;
	private OfferDetailData offer;
	private String status;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "MAPPING_ID", unique = true, nullable = false)
	public int getMappingId() {
		return mappingId;
	}

	public void setMappingId(int mappingId) {
		this.mappingId = mappingId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PARTNER_ID", nullable = false)
	public MarketingPartner getPartner() {
		return partner;
	}

	public void setPartner(MarketingPartner partnerId) {
		this.partner = partnerId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "OFFER_ID", nullable = false)
	public OfferDetailData getOffer() {
		return offer;
	}

	public void setOffer(OfferDetailData offerId) {
		this.offer = offerId;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
