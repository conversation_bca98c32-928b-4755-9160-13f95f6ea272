package com.stpl.tech.master.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "SIGNUP_OFFER_COUPON_DETAILS")
public class SignupOffersCouponDetails {

    Integer keyId;
    Integer offerDetailId;
    String couponCode;
    String couponPrefix;
    String couponStatus;
    String couponValidity;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "KEY_ID", unique = true, nullable = false)
    public Integer getKeyId() {
        return keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }
    @Column(name = "OFFER_DETAIL_ID")
    public Integer getOfferDetailId() {
        return offerDetailId;
    }

    public void setOfferDetailId(Integer offerDetailId) {
        this.offerDetailId = offerDetailId;
    }

    @Column(name = "COUPON_CODE")
    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @Column(name = "COUPON_PREFIX")
    public String getCouponPrefix() {
        return couponPrefix;
    }

    public void setCouponPrefix(String couponPrefix) {
        this.couponPrefix = couponPrefix;
    }

    @Column(name = "COUPON_STATUS")
    public String getCouponStatus() {
        return couponStatus;
    }

    public void setCouponStatus(String couponStatus) {
        this.couponStatus = couponStatus;
    }

    @Column(name = "COUPON_VALIDITY")
    public String getCouponValidity() {
        return couponValidity;
    }

    public void setCouponValidity(String couponValidity) {
        this.couponValidity = couponValidity;
    }
}
