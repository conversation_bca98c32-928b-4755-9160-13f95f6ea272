/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.service.model.AclData;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.Department;
import com.stpl.tech.master.data.model.Designation;
import com.stpl.tech.master.data.model.EmployeeUnitMapping;
import com.stpl.tech.master.data.model.UserPolicyData;
import com.stpl.tech.master.domain.model.ACLRequest;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.EmployeeApplicationMapping;
import com.stpl.tech.master.domain.model.EmployeeRole;
import com.stpl.tech.master.domain.model.EmployeeRoleUpdateRequest;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Interface for accessing all metadata related to POS. This is the read only
 * API for the metadata.
 *
 * <AUTHOR>
 *
 */
public interface MasterUserDataDao extends AbstractMasterDao{

	public String authenticateUser(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent,
			boolean isAdmin) throws AuthenticationFailureException;

	public boolean changePasscode(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent)
			throws AuthenticationFailureException;

	public boolean resetPasscode(int userId, String newPasscode, int updatedBy) throws AuthenticationFailureException;

	public boolean logout(int unitId, int userId, String sessionKey)
			throws AuthenticationFailureException;

	public List<Employee> getEmployees(int unitId) throws DataNotFoundException;

	public Employee getEmployee(int userId);

	public Employee getEmployee(String empCode);

	public Employee getEmployeeByContact(String contact);

	public Boolean authorizeMac(String macAddress);

	public Designation getDesignation(int designationId);

	public Designation getDesignation(String designationName);

	public Department getDepartment(int deptId);

	public Department getDepartment(String deptName);

	public ArrayList<String> getAuthorizedMacs();

	public Employee addEmployee(Employee employee,Department department,Designation designation) throws DataUpdationException;

	public Employee updateEmployee(Employee employee) throws DataUpdationException;

	public boolean updateStatus(int employeeId, EmploymentStatus status) throws DataUpdationException;

	public boolean addEmployeeUnitMapping(int employeeId, List<Integer> unitIds) throws DataUpdationException;

	public boolean createPasscode(int userId, String newPasscode) throws AuthenticationFailureException;

    List<EmployeeBasicDetail> getActiveEmployees(String designation, String department);

    public List<EmployeeBasicDetail> getAllEmployees(String designation);

    public List<EmployeeBasicDetail> getAllEmployees();

	public List<EmployeeUnitMapping> getEmployeeUnitMapping(int employeeId);

	public List<UnitBasicDetail> getUnitsForEmployee(int employeeId, boolean onlyActive) throws DataNotFoundException;

	public List<Employee> getEmployeesForUnit(Integer unitId);

	public boolean verifyUser(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent)
			throws AuthenticationFailureException;

	public Employee getSystemEmployee();

	public EmployeeBasicDetail getActiveEmployee(int userId);

	public EmployeeBasicDetail getEmployeeBasicDetail(int userId);

	public EmployeeBasicDetail getEmployeeBasicDetail(String empCode);

	public List<IdName> getActiveEmployeeDetail();

	public List<AclData> getAcl(ApplicationName appName, int employeeId, Integer companyId, Integer brandId);

	public boolean addRole(int employeeId, int roleId, int updatedBy);

	public boolean deactivateRoles(EmployeeRole roles);

	public List<IdCodeName> getActiveRoles();

	public List<IdCodeName> getActiveRoles(int employeeId);

	public List<Integer> getActiveEmpUnitIds(Integer empId);

	public List<EmployeeBasicDetail> getEmployeesForEmployeeMealForUnit(int unitId);

	public Employee getEmployeeBySdpContact(String sdpContact);

	/**
	 * @param userId
	 * @return
	 */
	public String getEmplyeePassCode(int userId);

	public Map<Integer, String> getEmployeesWithRole(String role);

	Map<String, List<IdName>> getEmpListByAcl(ACLRequest aclRequest);

	Integer getEmployeeIdByEmail(String userEmail);

	Boolean updateEmployeeAppMapping(EmployeeApplicationMapping employeeMapping);

	Map<String,List<Pair<String,String>>> getEmpAppMapping(int employeeId);

	Boolean verifyUserForCancellation(Integer empId, String passcode);

	String getLastActivityTime(String system1 , String system2);

	Boolean addLastActivityTime(String system1 , String system2 ,String date);

//	Map<String,Integer> lookUpDepartmentDesignation(String department,String designation);

	Boolean addDepartmentDesignationMapping(Integer deptId,Integer designationId);

	Employee updateEmpData(Employee employee,Department department,Designation designation);

	BusinessDivision getBusinessDivision(Integer businessDivId);

	boolean getActiveEmployeeSessionDetail(Date creationTime);

	void setEmployeeInactive(String empId,String empContact);

	void inValidateSessionCache(List<Integer> unitId);

    List<Object[]> getRoleActionMappingsForRoles(Set<Integer> roleIds);

    boolean deactivateRolesInPolicy(EmployeeRole employeeRole);

	boolean addUserPolicyRole(Integer userPolicyId, Integer roleId);

	List<UserPolicyData> checkUserPolicyExist(String departmentDesignation);

	Boolean updateEmployeeRoleBrandMappings(EmployeeRoleUpdateRequest request);

    /**
     * Get all active employees who don't have entries in EmployeePassCode table
     * @return List of Employee objects
     */
    List<Employee> getEmployeesWithoutPasscodes(List<Integer> employeeIds);

}
