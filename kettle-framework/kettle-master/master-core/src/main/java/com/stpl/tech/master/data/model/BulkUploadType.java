package com.stpl.tech.master.data.model;

import java.util.HashMap;
import java.util.Map;

public enum BulkUploadType {
    UNIT_PRODUCT_RECIPE_UPLOAD("PRODUCT_RECIPE");

    private final String shortName;
    private static final Map<String, BulkUploadType> CODE_TO_ENUM = new HashMap<>();

    static {
        for (BulkUploadType type : values()) {
            CODE_TO_ENUM.put(type.shortName ,type);
        }
    }

    BulkUploadType(String shortName) {
        this.shortName = shortName;
    }

    public String getShortName() {
        return this.shortName;
    }

    public static BulkUploadType fromShortName(String shortName) {
        return CODE_TO_ENUM.get(shortName);
    }

}
