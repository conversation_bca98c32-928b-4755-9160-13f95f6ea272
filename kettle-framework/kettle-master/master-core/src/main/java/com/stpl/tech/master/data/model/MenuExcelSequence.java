package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "MENU_EXCEL_SEQUENCE", schema = "KETTLE_MASTER_STAGE")
public class MenuExcelSequence {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MENU_EXCEL_SEQUENCE_ID", nullable = false)
    private Integer menuExcelSequenceId;

    @Column(name = "SEQUENCE_NAME", length = 200)
    private String sequenceName;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "UPDATED_BY", length = 45)
    private String updatedBy;

    @Column(name = "MENU_APP")
    private String menuApp;


}