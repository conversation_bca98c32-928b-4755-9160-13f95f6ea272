package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.repository.cdi.Eager;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "DELIVERY_OFFER_DETAIL_DATA")
public class DeliveryOfferDetailData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Integer id;
    @Column(name = "OFFER_CODE")
    private String offerCode;
    @Column(name = "OFFER_DESCRIPTION",columnDefinition = "TEXT")
    private String offerDescription;
    @Column(name = "OFFER_STATUS")
    private String offerStatus;
    @Column(name = "OFFER_CATEGORIES")
    private String offerCategory;
}
