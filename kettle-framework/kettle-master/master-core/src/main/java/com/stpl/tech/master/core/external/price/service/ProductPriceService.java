package com.stpl.tech.master.core.external.price.service;

import com.stpl.tech.master.data.model.UnitProductPricingBulkUpdateEvent;
import com.stpl.tech.master.domain.model.UnitProductPriceBulkRequest;
import com.stpl.tech.master.domain.model.UnitProductPricingDetail;
import com.stpl.tech.util.EnvType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ProductPriceService {

    View getUnitProductPriceSheet(UnitProductPriceBulkRequest request);

    Map<BigDecimal, List<UnitProductPricingDetail>> parseAndCreateBulkUnitProductPriceUpdateEvent(MultipartFile file, Integer updatedBy,
                                                                                                  UnitProductPricingBulkUpdateEvent updateEvent) throws IOException;

    void bulkUpdateUnitProductPrice(BigDecimal price, List<UnitProductPricingDetail> productPricingList, Integer updatedBy,
                                    UnitProductPricingBulkUpdateEvent updateEvent);

    void updateBulkUpdateUnitProductPriceEvent(UnitProductPricingBulkUpdateEvent updateEvent);

    String saveBulkProductPriceSheet(String s3ProductBucket, MultipartFile file);

    public void sendProductProfileBulkUpdateMail(UnitProductPricingBulkUpdateEvent updateEvent,String basePath, EnvType envType);
}
