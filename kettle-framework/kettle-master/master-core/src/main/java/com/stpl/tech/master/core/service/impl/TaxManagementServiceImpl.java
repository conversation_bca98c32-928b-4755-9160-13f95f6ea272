/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.TaxManagementService;
import com.stpl.tech.master.data.dao.TaxMappingDao;
import com.stpl.tech.master.data.model.TaxCategoryData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.tax.model.CategoryAdditionalTax;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxInfo;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Service
public class TaxManagementServiceImpl implements TaxManagementService {

	@Autowired
	private TaxMappingDao dao;

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<IdCodeName> getAllCountries() {
		return dao.getAllCountries();
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<TaxCategory> getAllTaxCategories() {
		return dao.getAllCategory();
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<IdCodeName> getAllTaxCategoriesBasicInfo() {
		return dao.getAllCategoryBasicInfo();
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public TaxCategory addTaxCategoy(TaxCategory category) {
		TaxCategory data = dao.addCategoryData(category);
		return data;
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public TaxCategory updateTaxCategoy(TaxCategory category) {
		TaxCategory data = dao.updateCategoryData(category);
		return data;
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<TaxInfo> getAllTaxes() {
		return dao.getAllTaxes();
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<IdCodeName> getAllTaxesBasicInfo() {
		return dao.getAllTaxesBasicInfo();
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public boolean addTaxData(TaxInfo info) {
		return dao.addTaxData(info);
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public CategoryTax updateCategoryTax(CategoryTax categoryTax) {
		CategoryTax data = dao.updateCategoryTax(categoryTax);
		return data;
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CategoryTax fetchCategoryTax(int countryId, int categoryId, int taxId, boolean getForAllStates) {
		return dao.fetchCategoryTax(countryId, categoryId, taxId, getForAllStates);
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public List<CategoryTax> fetchAllCategoryTax() {
		return dao.fetchAllCategoryTax();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.core.service.TaxManagementService#
	 * fetchAllCategoryTax(int, int)
	 */
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CategoryTax fetchAllCategoryTax(int countryId, int categoryId) {
		return dao.fetchAllCategoryTax(countryId, categoryId);
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CategoryAdditionalTax fetchCategoryAdditionalTax(int countryId, int categoryId) {
		return dao.fetchCategoryAdditionalTax(countryId, categoryId);
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public Map<String, CategoryAdditionalTax> fetchAllCategoryAdditionalTax() {
		return dao.fetchAllCategoryAdditionalTax();
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CategoryAdditionalTax fetchCategoryAdditionalTax(int countryId, int categoryId, int taxId) {
		return dao.fetchCategoryAdditionalTax(countryId, categoryId, taxId);
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public CategoryAdditionalTax updateCategoryAdditionalTax(CategoryAdditionalTax categoryTax) {
		CategoryAdditionalTax additionalTax = dao.updateCategoryAdditionalTax(categoryTax);
		return additionalTax;
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public TaxCategoryData updateTaxCategoyStatus(int id, String status) throws DataUpdationException {
		TaxCategoryData data = dao.find(TaxCategoryData.class, id);
		if (data != null) {
			if (AppUtils.isActive(status)) {
				validateCategoryActivation(data);
			}
			data.setCategoryStatus(AppUtils.isActive(status) ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE);
			return (TaxCategoryData) dao.update(data);

		}
		return null;
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public TaxCategory getTaxCategory(int id) {
		return dao.getTaxCategory(id);
	}

	/**
	 * @param data
	 * @throws DataUpdationException
	 */
	private void validateCategoryActivation(TaxCategoryData data) throws DataUpdationException {
		CategoryTax tax = fetchCategoryTax(AppConstants.COUNTRY_ID_INDIA, data.getTaxCategoryDataId(),
				AppConstants.GST_TAX_ID, false);
		if (tax == null || tax.getTaxes() == null || tax.getTaxes().size() == 0) {
			throw new DataUpdationException(
					"Cannot Activate the Tax Category as the GST tax is missing for " + data.getCategoryCode());
		}
	}

}
