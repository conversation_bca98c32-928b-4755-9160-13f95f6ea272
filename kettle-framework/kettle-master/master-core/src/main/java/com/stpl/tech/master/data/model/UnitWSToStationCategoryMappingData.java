/*
 * Created By Shanmukh
 */

package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "UNIT_WS_TO_STATION_CATEGORY_MAPPING")
public class UnitWSToStationCategoryMappingData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "UNIT_WS_TO_STATION_MAPPING_ID", unique = true, nullable = false)
    private Integer unitWSToStationMappingId;

    @Column(name = "UNIT_ID",nullable = false)
    private Integer unitId;

    @Column(name = "WORK_STATION_NAME",nullable = false)
    private String workStationName;

    @Column(name = "STATION_CATEGORY",nullable = false)
    private String stationCategory;

    @Column(name = "CREATED_BY",nullable = false)
    private Integer createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "MAPPING_STATUS",nullable = false)
    private String mappingStatus;
}
