/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.offer.service.impl;

import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.LaunchOfferStrategy;
import com.stpl.tech.master.core.data.vo.HourlyOfferUnitMapping;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.CampaignCouponMapping;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CustomerOfferMappingData;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.OfferDetailMappingData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
public class OfferManagementExternalServiceImpl implements OfferManagementExternalService {

    @Autowired
    private OfferManagementDao offerDao;

    private static final Logger LOG = LoggerFactory.getLogger(OfferManagementExternalServiceImpl.class);

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void updateCouponUsageByOne(int customerId, String offerCode, int orderId) {
        offerDao.updateCouponUsageByOne(customerId, offerCode, orderId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void setContactNumber(String contactNumber, String couponCode) {
        offerDao.setContactNumber(contactNumber, couponCode);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void setUsageCountOfAllCouponsForAnOffer(int offerId, int count) {
        offerDao.setUsageCountOfAllCouponsForAnOffer(offerId, count);
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService#getAllInactiveCoupons(java.lang.String)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CouponDetailData> getAllInactiveCoupons(int offerId) {
        return offerDao.getAllInactiveCoupons(offerId);
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService#getAllCustomerWithOffer(java.lang.Integer)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Set<String> getAllCustomerWithOffer(Integer offerDetailId) {
        return offerDao.getAllCustomerWithOffer(offerDetailId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CouponDetail> getAllCouponForCustomer(String number, String date, Integer brandId) {
        List<CouponDetail> couponDetails = new ArrayList<>();
        List<CouponDetailData> couponDetailDataList = offerDao.getCouponForCustomer(number, date, brandId);
        if (couponDetailDataList != null && couponDetailDataList.size() > 0) {
            couponDetailDataList.forEach(c -> {
                couponDetails.add(MasterDataConverter.convert(c));
            });

        }
        return couponDetails;
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService#getAllValidOffersWithLaunchStrategy()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OfferDetailData> getAllValidOffersWithLaunchStrategy(List<String> launchStrategy) {
        return offerDao.getAllValidOffersWithLaunchStrategy(launchStrategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OfferDetailData> getAllOffersWithLaunchStrategy(List<String> launchStrategy) {
        return offerDao.getAllOffersWithLaunchStrategy(launchStrategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public HourlyOfferUnitMapping getHourlyOfferUnitMapping(int offerDetailId) {
        return offerDao.getHourlyOfferUnitMapping(offerDetailId);
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService#deactiveOffer(java.lang.Integer)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void deactiveOffer(Integer offerDetailId) {
        offerDao.deactiveOffer(offerDetailId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void add(CustomerOfferMappingData mapping) {
        offerDao.add(mapping);
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService#getAllPendingNotificationMassOffers(java.util.Date)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CustomerOfferMappingData> getAllPendingNotificationMassOffers(int offerId, Date endTime) {
        return offerDao.getAllPendingNotificationMassOffers(offerId, endTime);
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService#setCustomerOfferMappingDataNofified(java.lang.Integer)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void setCustomerOfferMappingDataNotified(Integer customerOfferMappingDataId) {
        offerDao.setCustomerOfferMappingDataNotified(customerOfferMappingDataId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Set<String> getCurrentCycleWinners(List<LaunchOfferStrategy> launchOfferStrategy) {
        Date currentCycleCutOff = AppUtils.getDateBeforeOrAfter(AppUtils.getCurrentDate(), -12);
        return offerDao.getCurrentCycleWinners(launchOfferStrategy, currentCycleCutOff);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getOfferBudgerCategory(String couponCode) {
        return offerDao.getOfferBudgerCategory(couponCode);

    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void createPartnerDiscountCoupon(String couponCode, String channelPartner) {
        CouponDetailData detail = offerDao.getCoupon(couponCode);
        CouponDetailData copy = new CouponDetailData();
        copy.setCouponCode(channelPartner);
        copy.setCouponReuse(detail.getCouponReuse());
        copy.setCouponStatus(detail.getCouponStatus());
        copy.setOfferDetail(detail.getOfferDetail());
        copy.setEndDate(detail.getEndDate());
        copy.setStartDate(detail.getStartDate());
        copy.setManualOverride(detail.getManualOverride());
        copy.setMaxUsage(detail.getMaxUsage());
        copy.setMaxCustomerUsage(detail.getMaxCustomerUsage());
        copy.setUsageCount(detail.getUsageCount());
        copy.setCustomerReuse(detail.getCustomerReuse());
        offerDao.add(copy);

    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CouponDetailData getCoupon(String offerCode) {
		return offerDao.getCoupon(offerCode);
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CampaignCouponMapping> getActiveCampaignsByJourney(String repeatType, Integer journey) {
		return offerDao.getActiveCampaignsByJourney(repeatType, journey);
	}
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCustomerCouponData(String oldContactNumber, String newContactNumber) {
        try {
            return offerDao.updateAllCouponWithContact(oldContactNumber, newContactNumber) > 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CouponDetail> getAllCouponForChannelPartner(String partnerId,String offerScope) {
        List<CouponDetail> couponDetails = new ArrayList<>();
        List<OfferDetailData> offerDetailsList = offerDao.getOfferDetailByScope(offerScope);
        List<Integer> ids = new ArrayList<>();
        for(int i=0;i<offerDetailsList.size();i++){
//            LOG.info("Got offerDetailId  : {}",offerDetailsList.get(i).getOfferDetailId());
            ids.add(offerDetailsList.get(i).getOfferDetailId());
        }
        LOG.info("Got offerDetailIdsListbyScope list of size : {}",ids.size());
        List<OfferDetailMappingData> offerDetailMappingData = offerDao.getOfferDetailMappingDataByMappingTypeAndMappingValueAndOfferdetailIds(CouponMappingType.CHANNEL_PARTNER.name(),partnerId,ids);
        ids = new ArrayList<>();
        for(int i=0;i<offerDetailMappingData.size();i++){
//            LOG.info("Got offerDetailIds from offerDetailMappingData : {}",offerDetailMappingData.get(i).getOfferDetail().getOfferDetailId());
            ids.add(offerDetailMappingData.get(i).getOfferDetail().getOfferDetailId());
        }
        LOG.info("Got offerDetailIdsListbyScope list of size : {}",ids.size());
        List<CouponDetailData> couponDetailDataList = offerDao.getCouponDetailByResuseAndStatusAndOfferdetailIds(AppConstants.YES, AppConstants.ACTIVE,ids);
        LOG.info("Got couponDetailList list of size : {}",couponDetailDataList.size());
        if (couponDetailDataList != null && couponDetailDataList.size() > 0) {
            couponDetailDataList.forEach(c -> {
                couponDetails.add(MasterDataConverter.convert(c));
            });

        }
        return couponDetails;
    }
}
