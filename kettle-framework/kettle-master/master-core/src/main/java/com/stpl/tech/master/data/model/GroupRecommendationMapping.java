package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "GROUP_RECOMMENDATION_MAPPING")
public class GroupRecommendationMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GROUP_RECOMMENDATION_MAPPING_ID", nullable = false)
    private Integer id;

    @Column(name = "GROUP_ID")
    private Integer groupId;


    @Column(name = "PRODUCT_ID")
    private Integer productId;

    @Column(name = "DAY_SLOT", length = 100)
    private String daySlot;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "PARTNER_ID")
    private Integer partnerId;

    @Column(name = "MAPPING_STATUS", length = 45)
    private String mappingStatus;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
}
