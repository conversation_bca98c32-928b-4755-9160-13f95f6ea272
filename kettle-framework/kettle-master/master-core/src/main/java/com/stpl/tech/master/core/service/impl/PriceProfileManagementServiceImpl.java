package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.ListTypes;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.PriceProfileManagementService;
import com.stpl.tech.master.data.dao.MasterMetadataDao;
import com.stpl.tech.master.data.dao.PriceProfileDao;
import com.stpl.tech.master.data.dao.PriceProfileProductMappingsDao;
import com.stpl.tech.master.data.dao.PriceProfileVersionDao;
import com.stpl.tech.master.data.dao.ProductManagementDao;
import com.stpl.tech.master.data.dao.UnitPriceProfileMappingDao;
import com.stpl.tech.master.data.model.PriceProfileData;
import com.stpl.tech.master.data.model.PriceProfileProductMapping;
import com.stpl.tech.master.data.model.PriceProfileVersion;
import com.stpl.tech.master.data.model.RefLookup;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitPriceProfileMapping;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PriceProfileDomain;
import com.stpl.tech.master.domain.model.PriceProfileProductMappingDomain;
import com.stpl.tech.master.domain.model.PriceProfileProductMappingRequest;
import com.stpl.tech.master.domain.model.PriceProfileVersionsDomain;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPriceProfileMappingKey;
import com.stpl.tech.master.domain.model.ProductPriceProfileMappingSheet;
import com.stpl.tech.master.domain.model.ProductPricingMappingBulkUploadResponse;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitPriceProfileMappingBulkUploadResponse;
import com.stpl.tech.master.domain.model.UnitPriceProfileMappingDomain;
import com.stpl.tech.master.domain.model.UnitPriceProfileMappingSheet;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;



@Log4j2
@Service
public class PriceProfileManagementServiceImpl implements PriceProfileManagementService {

    @Autowired
    PriceProfileDao priceProfileDao;

    @Autowired
    PriceProfileVersionDao priceProfileVersionDao;


    @Autowired
    PriceProfileProductMappingsDao priceProfileProductMappingsDao;

    @Autowired
    MasterDataCache masterDataCache;


    @Autowired
    ProductManagementDao productManagementDao;

    @Autowired
    UnitPriceProfileMappingDao unitPriceProfileMappingDao;

    @Autowired
    MasterMetadataDao masterMetadataDao;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PriceProfileDomain addNewPriceProfile(PriceProfileDomain priceProfile , Integer createdBy) throws DataUpdationException {
        PriceProfileData existingProfile = priceProfileDao.findByPriceProfileName(priceProfile.getPriceProfileName());
        if(Objects.nonNull(existingProfile)){
            throw new DataUpdationException("A Profile With Same name Already Exists !!");
        }
        Integer newVersion = Objects.isNull(priceProfile.getCloneVersion()) ? 1 : priceProfile.getCloneVersion();
        PriceProfileData priceProfileData = PriceProfileData.builder().priceProfileName(priceProfile.getPriceProfileName())
                .brandId(priceProfile.getBrandId()).channelPartnerId(priceProfile.getChannelPartnerId())
                .status(AppConstants.ACTIVE).createdBy(createdBy).creationTime(AppUtils.getCurrentTimestamp())
                .build();
        priceProfileData = priceProfileDao.save(priceProfileData);
        PriceProfileVersion priceProfileVersion = PriceProfileVersion.builder().priceProfileData(priceProfileData).
                versionNo(newVersion).status(AppConstants.ACTIVE).createdBy(createdBy).
                creationTime(AppUtils.getCurrentTimestamp()).build();
        priceProfileData.setPriceProfileVersions(new ArrayList<>(Arrays.asList(priceProfileVersion)));
        priceProfileVersion = priceProfileVersionDao.save(priceProfileVersion);
        if(Objects.nonNull(priceProfile.getClonePriceProfileId()) && Objects.nonNull(priceProfile.getCloneVersion())){
             clonePriceProfile(priceProfileData.getPriceProfileDataId(),priceProfileVersion.getVersionNo(),priceProfile.getClonePriceProfileId()
            ,priceProfile.getCloneVersion(),priceProfileData,createdBy);
        }
        return toDTO(priceProfileData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PriceProfileDomain addNewPriceProfileVersion(Integer priceProfileId ,Integer clonePriceProfileId
            , Integer clonePriceProfileVersion,Integer createdBy){
        PriceProfileData priceProfileData = priceProfileDao.findById(priceProfileId).get();
        Integer currentMaxVersionNumber = priceProfileVersionDao.findMaxVersionNoByPriceProfileDataId(priceProfileId);
        PriceProfileVersion priceProfileVersion = PriceProfileVersion.builder().priceProfileData(priceProfileData).
                versionNo(currentMaxVersionNumber+1).status(AppConstants.ACTIVE).createdBy(createdBy).
                creationTime(AppUtils.getCurrentTimestamp()).build();
        priceProfileVersion = priceProfileVersionDao.save(priceProfileVersion);
        clonePriceProfile(priceProfileData.getPriceProfileDataId(),priceProfileVersion.getVersionNo(),clonePriceProfileId
                ,clonePriceProfileVersion,priceProfileData,createdBy);
        //priceProfileData.getPriceProfileVersions().add(priceProfileVersion);
        return toDTO(priceProfileData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean addNewPriceProfileVersions(Integer clonePriceProfileVersion,Integer createdBy){
        Integer maxVersionNumber  = priceProfileVersionDao.findMaxVersionNo();
        List<PriceProfileVersion> newPriceProfileVersions = new ArrayList<>();
        List<PriceProfileData> priceProfiles = priceProfileVersionDao.findPriceProfileDataByVersionNo(clonePriceProfileVersion);
        for(PriceProfileData priceProfileData : priceProfiles){
            PriceProfileVersion priceProfileVersion = PriceProfileVersion.builder().priceProfileData(priceProfileData).
                    versionNo(maxVersionNumber+1).status(AppConstants.ACTIVE).createdBy(createdBy).
                    creationTime(AppUtils.getCurrentTimestamp()).build();
            newPriceProfileVersions.add(priceProfileVersion);
            clonePriceProfile(priceProfileData.getPriceProfileDataId(),priceProfileVersion.getVersionNo(),priceProfileData.getPriceProfileDataId()
                    ,clonePriceProfileVersion,priceProfileData,createdBy);
        }
        if(!CollectionUtils.isEmpty(newPriceProfileVersions)){
            priceProfileVersionDao.saveAll(newPriceProfileVersions);
            return true;
        }
        return false;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Integer getMaxVersion(){
        Integer maxVersionNumber  = priceProfileVersionDao.findMaxVersionNo();
        return maxVersionNumber;

    }

    private void clonePriceProfile(Integer newProfileId , Integer newProfileVersion , Integer cloneProfileId ,
                                   Integer cloneProfileVersion , PriceProfileData newPriceProfile , Integer clonedBy){
        Map<Integer,IdCodeName> dimensionCodeMap = masterDataCache.getListData().get(ListTypes.DIMENSION_CODES).stream().
                collect(Collectors.toMap(IdCodeName::getId, Function.identity()));
        Set<Integer> refLookupIds  = new HashSet<>();
        List<PriceProfileProductMappingDomain> priceProfileProductMappings = priceProfileProductMappingsDao.filterMappings(cloneProfileVersion
                        ,cloneProfileId,null,null).stream().map(mapping ->convert(mapping,dimensionCodeMap,masterDataCache)).
                map(domain -> {
                    domain.setPriceProfileId(new IdCodeName(newProfileId,"",""));
                    domain.setVersion(newProfileVersion);
                    domain.setCreatedBy(clonedBy);
                    domain.setCreationTime(AppUtils.getCurrentTimestamp());
                    domain.setLastUpdationTime(null);
                    domain.setLastUpdatedBy(null);
                    domain.setId(null);
                    refLookupIds.add(domain.getDimensionCode().getId());
                    return domain;
                }).
                toList();
        Map<Integer, RefLookup> refLookupMap = masterMetadataDao.getRefLookUpByIds(refLookupIds.stream().toList()).stream()
                .collect(Collectors.toMap(RefLookup::getRlId,Function.identity()));
        List<PriceProfileProductMapping> clonedMappings = priceProfileProductMappings.stream().map(clonedMapping -> convert(clonedMapping,
                newPriceProfile,refLookupMap
                )).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(clonedMappings)){
            priceProfileProductMappingsDao.saveAll(clonedMappings);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean togglePriceProfileStatus(PriceProfileDomain priceProfile , Integer updatedBy) throws DataUpdationException {
        Optional<PriceProfileData> priceProfileDataOptional = priceProfileDao.findById(priceProfile.getPriceProfileDataId());
        if(priceProfileDataOptional.isPresent()){
            List<UnitPriceProfileMapping> unitPriceProfileMappings  = unitPriceProfileMappingDao.
                    findAllByPriceProfileIdAndPriceProfileVersion(priceProfile.getPriceProfileDataId(),null);
            if(!CollectionUtils.isEmpty(unitPriceProfileMappings)){
                throw  new DataUpdationException("This Profile Is Mapped With multiple Units please Checkl !!");
            }
            PriceProfileData priceProfileData = priceProfileDataOptional.get();
            if(priceProfile.getStatus().equals(AppConstants.ACTIVE) ||
                    priceProfile.getStatus().equals(AppConstants.IN_ACTIVE)){
                priceProfileData.setStatus(priceProfile.getStatus());
                priceProfileData.setUpdateBy(updatedBy);
                priceProfileData.setUpdationTime(AppUtils.getCurrentTimestamp());
                return true;
            }
        }
        return false;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean togglePriceProfileVersionStatus(PriceProfileVersionsDomain priceProfileVersion , Integer updatedBy) throws DataUpdationException {
        Optional<PriceProfileVersion> priceProfileVersionDataOptional = priceProfileVersionDao.findById(priceProfileVersion.getPriceProfileVersionsId());
        if(priceProfileVersionDataOptional.isPresent()){
            List<UnitPriceProfileMapping> unitPriceProfileMappings  = unitPriceProfileMappingDao.
                    findAllByPriceProfileIdAndPriceProfileVersion(priceProfileVersion.getPriceProfileId(),
                            priceProfileVersion.getVersionNo());
            if(!CollectionUtils.isEmpty(unitPriceProfileMappings)){
                throw  new DataUpdationException("This Profile and Version Is Mapped With multiple Units please Checkl !!");
            }
            PriceProfileVersion priceProfileVersionData = priceProfileVersionDataOptional.get();
            if(priceProfileVersion.getStatus().equals(AppConstants.ACTIVE) ||
                    priceProfileVersion.getStatus().equals(AppConstants.IN_ACTIVE)){
                priceProfileVersionData.setStatus(priceProfileVersion.getStatus());
                priceProfileVersionData.setUpdatedBy(updatedBy);
                priceProfileVersionData.setUpdationTime(AppUtils.getCurrentTimestamp());
                return true;
            }
        }
        return false;

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean toggleBulkVersionStatus(Integer versionNo , String status , Integer updatedBy) throws DataUpdationException {
        List<PriceProfileVersion> priceProfileVersionDataList = priceProfileVersionDao.findPriceVersionDataByVersionNo
                (versionNo);
        List<PriceProfileVersion> updateList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(priceProfileVersionDataList)){
            List<UnitPriceProfileMapping> unitPriceProfileMappings  = unitPriceProfileMappingDao.
                    findAllByPriceProfileVersion(versionNo);
            if(!CollectionUtils.isEmpty(unitPriceProfileMappings)){
                throw  new DataUpdationException("This Version Is Mapped With multiple Units please Check !!");
            }
            priceProfileVersionDataList.forEach(version -> {
                if(!version.getStatus().equalsIgnoreCase(status)){
                    version.setStatus(status);
                    version.setUpdationTime(AppUtils.getCurrentTimestamp());
                    version.setUpdatedBy(updatedBy);
                    updateList.add(version);
                }
            });
        }
        if(!CollectionUtils.isEmpty(updateList)){
            priceProfileVersionDao.saveAll(updateList);
            return true;
        }
        return false;

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PriceProfileDomain> getAllPriceProfilesByStatus(String status){
        List<PriceProfileDomain> priceProfiles = new ArrayList<>();
        if (Objects.nonNull(status)) {
            priceProfiles = priceProfileDao.findAllByStatus(status).stream().map(this::toDTO).collect(Collectors.toList());
        } else {
            priceProfiles = priceProfileDao.findAll().stream().map(this::toDTO).collect(Collectors.toList());
        }
        return priceProfiles;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PriceProfileProductMappingDomain> getPriceProfileProductMappings(PriceProfileProductMappingRequest request) throws DataNotFoundException {

        List<PriceProfileProductMapping> priceProfileProductMappings = new ArrayList<>();
        List<Integer> versionIds = (request.getVersionIds() == null || request.getVersionIds().isEmpty()) ? null : request.getVersionIds();
        List<Integer> profileIds = (request.getPriceProfileIds() == null ||request.getPriceProfileIds().isEmpty()) ? null : request.getPriceProfileIds();
        List<Integer> productIds = (request.getProductIds() == null || request.getProductIds().isEmpty()) ? null : request.getProductIds();
        List<Integer> dimensionIds = (request.getDimensionIds() == null || request.getDimensionIds().isEmpty()) ? null : request.getDimensionIds();
        Map<Integer,List<Integer>> priceProfileVersionsMap = new HashMap<>();
        Map<Integer,List<IdCodeName>> dimensionListDataMap = new HashMap<>();
        Map<Integer,PriceProfileDomain> priceProfileMap =   priceProfileDao.findAll().stream().map(
                this::toDTO).collect(Collectors.toMap(PriceProfileDomain::getPriceProfileDataId,Function.identity()));
        masterMetadataDao.getAllListData().get(ListTypes.DIMENSION_CODES.getGroup()).stream()
                .forEach(listData -> {
                    if(!dimensionListDataMap.containsKey(listData.getDetail().getId())){
                        dimensionListDataMap.put(listData.getDetail().getId(),listData.getContent());
                    }
                });
        if(!CollectionUtils.isEmpty(request.getPriceProfileIds())){
            if(!CollectionUtils.isEmpty(request.getVersionIds())){
                priceProfileVersionsMap = priceProfileVersionDao.findAllByPriceProfileVersionsId(versionIds)
                        .stream().collect(Collectors.groupingBy(version -> version.getPriceProfileData().getPriceProfileDataId(),
                                Collectors.mapping(PriceProfileVersion::getVersionNo,Collectors.toList())));
                for(Integer priceProfileId : profileIds){
                    List<PriceProfileProductMapping> mappings = masterMetadataDao.getPriceProfileProductMappings(priceProfileVersionsMap.get(priceProfileId)
                    ,new ArrayList<>(Arrays.asList(priceProfileId)),productIds,dimensionIds);
                    priceProfileProductMappings.addAll(mappings);
                }
            }else{
                List<PriceProfileProductMapping> mappings = masterMetadataDao.getPriceProfileProductMappings(null,profileIds
                ,productIds,dimensionIds);
                priceProfileProductMappings.addAll(mappings);
            }

        }else{
            List<PriceProfileProductMapping> mappings = masterMetadataDao.getPriceProfileProductMappings(null,null,productIds
            ,dimensionIds);
            priceProfileProductMappings.addAll(mappings);
        }
        //List<PriceProfileProductMapping> priceProfileProductMappings = priceProfileProductMappingsDao.filterMappings(request.get,profileId,productId,dimensionCode);
        Map<Integer,IdCodeName> dimensionCodeMap = masterDataCache.getListData().get(ListTypes.DIMENSION_CODES).stream().
                collect(Collectors.toMap(IdCodeName::getId, Function.identity()));
        List<PriceProfileProductMappingDomain> result =  priceProfileProductMappings.stream().map(mapping -> convert(mapping,dimensionCodeMap,masterDataCache)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(productIds)) {
            addMissingMappingsForSelection(result, request, productIds, priceProfileProductMappings,
                    priceProfileMap, priceProfileVersionsMap, dimensionListDataMap);
        }

        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedBrands = MasterUtil.getMappedBrands();
            result = result.stream().filter(r -> mappedBrands.contains(r.getProductId().getBrandId())).toList();
        }

        return result;
    }

    private void addMissingMappingsForSelection(List<PriceProfileProductMappingDomain> result , PriceProfileProductMappingRequest request,
                                                List<Integer> productIds, List<PriceProfileProductMapping> priceProfileProductMappings,
                                                Map<Integer, PriceProfileDomain> priceProfileMap, Map<Integer, List<Integer>> priceProfileVersionsMap,
                                                Map<Integer, List<IdCodeName>> dimensionListDataMap){
        // peroduct id - dimension id - price profile id - version - found
        Map<Integer,Map<Integer,Map<Integer,Map<Integer,Boolean>>>> productMappingFound = new HashMap<>();
        for (PriceProfileProductMapping mapping : priceProfileProductMappings) {
            Integer productId = mapping.getProductId();
            Integer dimensionId = mapping.getDimensionCode().getRlId();
            Integer priceProfileId = mapping.getPriceProfileData().getPriceProfileDataId();
            Integer version = mapping.getVersion();

            // Create the nested structure if it doesn't already exist
            productMappingFound
                    .computeIfAbsent(productId, k -> new HashMap<>())
                    .computeIfAbsent(dimensionId, k -> new HashMap<>())
                    .computeIfAbsent(priceProfileId, k -> new HashMap<>())
                    .put(version, true);  // Assuming 'found' is always true, you can modify this condition if needed
        }
        for (Integer productId : productIds) {
            // Get the associated dimensions for the product
            Product product = masterDataCache.getProduct(productId);
            List<IdCodeName> productDimensions = dimensionListDataMap.get(product.getDimensionProfileId());

            // If selected price profile IDs is not empty
            if (!CollectionUtils.isEmpty(request.getPriceProfileIds())) {
                for (Integer priceProfileId : request.getPriceProfileIds()) {
                    PriceProfileDomain priceProfileDomain = priceProfileMap.get(priceProfileId);
                    // Get versions for the selected profile
                    List<Integer> versions = priceProfileVersionsMap.get(priceProfileId); // Versions related to the price profile
                    if (CollectionUtils.isEmpty(versions)) {
                        versions = priceProfileDomain.getPriceProfileVersions().stream().filter(priceProfileVersion ->
                                        AppConstants.ACTIVE.equals(priceProfileVersion.getStatus())).map(PriceProfileVersionsDomain::getVersionNo)
                                .collect(Collectors.toList());
                    }

                    // Iterate through dimensions (either selected or all)
                    List<IdCodeName> dimensionsToCheck = (CollectionUtils.isEmpty(request.getDimensionIds())) ? productDimensions :
                            productDimensions.stream().filter(dimension -> request.getDimensionIds().contains(dimension.getId()))
                                    .toList();
                    for (IdCodeName dimension : dimensionsToCheck) {
                        for (Integer version : versions) {
                            // Check if the combination exists in the mapping
                            if (!productMappingFound.containsKey(productId) ||
                                    !productMappingFound.get(productId).containsKey(dimension.getId()) ||
                                    !productMappingFound.get(productId).get(dimension.getId()).containsKey(priceProfileId) ||
                                    !productMappingFound.get(productId).get(dimension.getId()).get(priceProfileId).containsKey(version)) {

                                // Add missing mapping to result
                                result.add(PriceProfileProductMappingDomain.builder()
                                        .productId(product)
                                        .dimensionCode(dimension)
                                        .status(AppConstants.IN_ACTIVE)
                                        .priceProfileId(new IdCodeName(priceProfileDomain.getPriceProfileDataId()
                                                ,priceProfileDomain.getPriceProfileName(),priceProfileDomain.getPriceProfileName()))
                                        .version(version)
                                        .build());
                            }
                        }
                    }
                }
            } else {
                // If price profile IDs are empty, just add combinations of productId and dimensionId
                /*for (IdCodeName dimension : productDimensions) {
                    if (!productMappingFound.containsKey(productId) ||
                            !productMappingFound.get(productId).containsKey(dimension.getId())){
                        result.add(PriceProfileProductMappingDomain.builder()
                                .productId(product)
                                .dimensionCode(dimension)
                                .status(AppConstants.IN_ACTIVE)
                                .build());
                    }
                }*/
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitPriceProfileMappingDomain> getUnitPriceProfileMappings(UnitCategory unitCategory, String unitRegion  ,Integer partnerId
            ,Integer brandId,Boolean fetchAll) {
        List<String> unitRegions = Arrays.asList(unitRegion.split(" "));
        List<UnitDetail> unitDetailList = productManagementDao.getUnitForSelectedRegion(unitCategory, unitRegions)
                .stream().filter(unit -> Boolean.TRUE.equals(fetchAll) ||
                        unit.getUnitStatus().equals(AppConstants.ACTIVE)).collect(Collectors.toList());
        Map<Integer, Unit> unitDetailMap =  masterDataCache.getUnits();
        Map<Integer,PriceProfileDomain> priceProfileMap =   priceProfileDao.findAllByStatus(AppConstants.ACTIVE).stream().map(
                profile ->toDTO(profile)).collect(Collectors.toMap(PriceProfileDomain::getPriceProfileDataId,Function.identity()));
        List<Integer> unitIds = unitDetailList.stream().map(UnitDetail::getUnitId).collect(Collectors.toList());
        Map<Integer, UnitPriceProfileMappingDomain>  priceProfileMappingsByUnit =    unitPriceProfileMappingDao.findAllMappingsByPartnerIdAndBrand(unitIds,partnerId,brandId)
                .stream().map(mapping -> convert(mapping,unitDetailMap,priceProfileMap)).collect(Collectors.toMap(mapping -> mapping.getUnit().getId(),
                        Function.identity()));
        unitDetailList.forEach(unit ->{
            if(!priceProfileMappingsByUnit.containsKey(unit.getUnitId())){
                UnitPriceProfileMappingDomain unitPriceProfileMappingDomain  = UnitPriceProfileMappingDomain.builder()
                        .unit(new IdCodeName(unit.getUnitId(),unit.getUnitName(),"",unit.getShortCode()
                        ,unit.getUnitSubCategory(), unit.getUnitStatus())).brandId(brandId).channelPartnerId(partnerId).mappingStatus(AppConstants.IN_ACTIVE)
                        .build();
                priceProfileMappingsByUnit.put(unit.getUnitId(),unitPriceProfileMappingDomain);
            }
        });

        List<UnitPriceProfileMappingDomain> unitPriceProfileMappingList = new ArrayList<>(priceProfileMappingsByUnit.values());
        if (RequestContext.isContextAvailable()) {
            List<Integer> mappedUnits = MasterUtil.getMappedUnits();
            unitPriceProfileMappingList = unitPriceProfileMappingList.stream().filter(mapping -> mappedUnits.contains(mapping.getUnit().getId())).toList();
        }

        return unitPriceProfileMappingList;
    }

    private UnitPartnerBrandKey getUnitPartnerBrandKey(Integer brandId , Integer partnerId , Integer unitId){
        return new UnitPartnerBrandKey(unitId,brandId,partnerId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void cloneUnitPriceProfileMappings(UnitDetail unit , Integer cloneUnitId , Integer updatedBy){
        Unit cloneUnit = masterDataCache.getUnit(cloneUnitId);
        Map<Integer,Unit> unitDetailMap = Map.of(cloneUnitId,cloneUnit);
        Map<Integer,PriceProfileDomain> priceProfileMap =   priceProfileDao.findAllByStatus(AppConstants.ACTIVE).stream().map(
                profile ->toDTO(profile)).collect(Collectors.toMap(PriceProfileDomain::getPriceProfileDataId,Function.identity()));
        List<UnitPriceProfileMappingDomain>  priceProfileMappings =   unitPriceProfileMappingDao.findAllMappingsByPartnerIdAndBrand(
                Collections.singletonList(cloneUnitId),null, null)
                .stream().map(mapping -> convert(mapping,unitDetailMap,priceProfileMap))
                .collect(Collectors.toList());
        List<UnitPriceProfileMapping> updateList = priceProfileMappings.stream().map(mapping ->
        {   mapping.setId(null);
            mapping.setCreationTime(AppUtils.getCurrentTimestamp());
            mapping.setCreatedBy(updatedBy);
            mapping.setUpdatedBy(null);
            mapping.setUpdationTime(null);
            mapping.setUnit(new IdCodeName(unit.getUnitId(),unit.getUnitName(),unit.getUnitName()));
            return convert(mapping);
        }).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(updateList)){
            unitPriceProfileMappingDao.saveAll(updateList);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<UnitPriceProfileMappingDomain> saveUnitPriceProfileMappings(List<UnitPriceProfileMappingDomain> mappings  ,
                                                                            Integer brandId , Integer partnerId , Integer updatedBy
                                                                            ,  AtomicInteger recordsUpdated , AtomicInteger recordsAdded
    ){
        Map<UnitPartnerBrandKey,List<Integer>> unitIdsByPartnerBrand =  mappings.stream().collect(Collectors.groupingBy(
                mapping ->getUnitPartnerBrandKey( brandId,partnerId,1),Collectors.mapping(
                        mapping -> mapping.getUnit().getId(),Collectors.toList()
                )));
        List<UnitPriceProfileMapping> updateList = new ArrayList<>();
        Map<Integer, Unit> unitDetailMap =  masterDataCache.getUnits();
        Map<Integer,PriceProfileDomain> priceProfileMap =   priceProfileDao.findAllByStatus(AppConstants.ACTIVE).stream().map(
                this::toDTO).collect(Collectors.toMap(PriceProfileDomain::getPriceProfileDataId,Function.identity()));
        Map<UnitPartnerBrandKey, UnitPriceProfileMappingDomain>  priceProfileMapByUnits = new HashMap<>();
        for(UnitPartnerBrandKey key : unitIdsByPartnerBrand.keySet()){
            List<Integer> unitIds  = unitIdsByPartnerBrand.get(key);
            Map<UnitPartnerBrandKey, UnitPriceProfileMappingDomain>  priceProfileMappingsByUnit =   unitPriceProfileMappingDao.findAllMappingsByPartnerIdAndBrand(unitIds, key.getPartnerId(), key.getBrandId())
                    .stream().map(mapping -> convert(mapping,unitDetailMap,priceProfileMap))
                            .collect(Collectors.toMap(mtt -> getUnitPartnerBrandKey(brandId,partnerId,mtt.getUnit().getId()),Function.identity()));
            priceProfileMapByUnits.putAll(priceProfileMappingsByUnit);
        }

        for(UnitPriceProfileMappingDomain mapping : mappings){
            UnitPartnerBrandKey key = getUnitPartnerBrandKey(brandId,partnerId,mapping.getUnit().getId());
            if(priceProfileMapByUnits.containsKey(key)){
                if(Objects.nonNull(mapping.getPriceProfile()) && Objects.nonNull(mapping.getUnit())){
                    UnitPriceProfileMappingDomain currentMapping = priceProfileMapByUnits.get(key);
                    if(currentMapping.getPriceProfile().getId() !=  mapping.getPriceProfile().getId() ||
                            !Objects.equals(currentMapping.getPriceProfileVersion(), mapping.getPriceProfileVersion()) ||
                            !currentMapping.getMappingStatus().equals(mapping.getMappingStatus())){
                        currentMapping.setPriceProfile(mapping.getPriceProfile());

                        if(Objects.isNull(mapping.getBrandId())){
                            mapping.setBrandId(brandId);
                        }
                        if(Objects.isNull(mapping.getChannelPartnerId())){
                            mapping.setChannelPartnerId(partnerId);
                        }
                        currentMapping.setPriceProfileVersion(mapping.getPriceProfileVersion());
                        currentMapping.setUpdatedBy(updatedBy);
                        currentMapping.setUpdationTime(AppUtils.getCurrentTimestamp());
                        currentMapping.setMappingStatus(AppConstants.ACTIVE);
                        UnitPriceProfileMapping unitPriceProfileMapping = convert(currentMapping);
                        updateList.add(unitPriceProfileMapping);
                        recordsUpdated.incrementAndGet();
                    }
                }
            }else{
                if(Objects.nonNull(mapping.getPriceProfile()) && Objects.nonNull(mapping.getUnit())){
                    if(Objects.isNull(mapping.getBrandId())){
                        mapping.setBrandId(brandId);
                    }
                    if(Objects.isNull(mapping.getChannelPartnerId())){
                        mapping.setChannelPartnerId(partnerId);
                    }
                    mapping.setCreatedBy(updatedBy);
                    mapping.setCreationTime(AppUtils.getCurrentTimestamp());
                    mapping.setMappingStatus(AppConstants.ACTIVE);
                    UnitPriceProfileMapping unitPriceProfileMapping = convert(mapping);
                    updateList.add(unitPriceProfileMapping);
                    recordsAdded.incrementAndGet();
                }
            }

        }
        if(!CollectionUtils.isEmpty(updateList)){
            unitPriceProfileMappingDao.saveAll(updateList);
        }
        return mappings;


    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)

    public UnitPriceProfileMappingBulkUploadResponse parseAndCreateUnitPriceProfileMappings(
            MultipartFile file , Integer updatedBy) throws IOException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        List<String> errorList = new ArrayList<>();
        SheetParser parser = new SheetParser();
        AtomicInteger recordsUpdated  = new AtomicInteger(0);
        AtomicInteger recordsAdded = new AtomicInteger(0);
        AtomicReference<Integer> recordsWithErrors = new AtomicReference<>(0);
        List<UnitPriceProfileMappingSheet> entityList = new ArrayList<>();
       Map<UnitPartnerBrandKey , List<UnitPriceProfileMappingDomain>> unitPriceProfileMappingDomainListByPartnerAndBrand  = new HashMap<>();
        Map<String,PriceProfileDomain> priceProfileMap =   priceProfileDao.findAllByStatus(AppConstants.ACTIVE).stream().map(
                profile ->toDTO(profile)).collect(Collectors.toMap(PriceProfileDomain::getPriceProfileName,Function.identity()));
        Map<Integer, UnitBasicDetail> unitBasicDetailMap = masterDataCache.getUnitsBasicDetails();
        int sheetCount = workbook.getNumberOfSheets();
        for (int i = 0; i < sheetCount; i++) {
            log.info("Parsing Bulk Product Price Profile Sheet {}", i + 1);
            List<UnitPriceProfileMappingSheet> sheetDetailList = parser.createEntity(workbook.getSheetAt(i),
                    UnitPriceProfileMappingSheet.class, errors::add);
            entityList.addAll(sheetDetailList);
        }

        if(CollectionUtils.isEmpty(errors)){
            entityList.stream().filter(sheetData -> {
                if(Objects.isNull(sheetData.getPriceProfile()) || Objects.isNull(sheetData.getPriceProfileVersion())
                        || Objects.isNull(sheetData.getUnitId()) || Objects.isNull(sheetData.getChannelPartnerId())
                        || Objects.isNull(sheetData.getBrandId()) || Objects.isNull(sheetData.getMappingStatus())){
                    errorList.add("Invalid Data ::::: " + sheetData.toString());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return false;
                }else if(unitBasicDetailMap.get(sheetData.getUnitId()) == null) {
                    errorList.add("Invalid Unit ::::: " + sheetData.getUnitId());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return false;
                }else if(!AppConstants.ACTIVE.equals(sheetData.getMappingStatus()) && !AppConstants.IN_ACTIVE.equals(sheetData.getMappingStatus())) {
                    errorList.add("Invalid Mapping Status :::: " + sheetData.getMappingStatus());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return false;
                }else if(priceProfileMap.get(sheetData.getPriceProfile()) == null) {
                    errorList.add("Invalid Price Profile ::::: " + sheetData.getPriceProfile());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return false;
                }else if(priceProfileMap.get(sheetData.getPriceProfile()).getPriceProfileVersions().stream()
                        .noneMatch(version -> version.getVersionNo().equals(sheetData.getPriceProfileVersion()))) {
                    errorList.add("Invalid Price Profile Version ::::: " +  sheetData.getPriceProfileVersion()
                            + " For Price Profile :::: "+ sheetData.getPriceProfile());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return false;
                }
                return true;
            }).forEach(sheetData -> {
                UnitPartnerBrandKey key = getUnitPartnerBrandKey(sheetData.getBrandId(),sheetData.getChannelPartnerId(),1);
                unitPriceProfileMappingDomainListByPartnerAndBrand.computeIfAbsent(key, k -> new ArrayList<>());
                unitPriceProfileMappingDomainListByPartnerAndBrand.get(key).add(
                        convert(sheetData,priceProfileMap,unitBasicDetailMap.get(sheetData.getUnitId())));
            });
        }else{
            log.info("Error Parsing Workbook for Unit Product Price Bulk Update, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            log.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        if(!CollectionUtils.isEmpty(unitPriceProfileMappingDomainListByPartnerAndBrand)){
            unitPriceProfileMappingDomainListByPartnerAndBrand.keySet().stream()
                            .forEach(key -> {
                                List<UnitPriceProfileMappingDomain> unitPriceProfileMappingDomainList = unitPriceProfileMappingDomainListByPartnerAndBrand.get(key);
                                saveUnitPriceProfileMappings(unitPriceProfileMappingDomainList,key.getBrandId(),key.getPartnerId(),updatedBy,recordsUpdated,recordsAdded);
                            });
        }
        return UnitPriceProfileMappingBulkUploadResponse.builder().recordsAdded(recordsAdded.get()).recordsUpdated(recordsUpdated.get())
                .recordsWithErrors(recordsWithErrors.get()).errors(errorList).build();
    }

    public static UnitPriceProfileMappingDomain convert(UnitPriceProfileMappingSheet unitPriceProfileMappingSheet ,
                                                        Map<String,PriceProfileDomain> priceProfileMap , UnitBasicDetail unitBasicDetail){
        UnitPriceProfileMappingDomain unitPriceProfileMappingDomain = new UnitPriceProfileMappingDomain();
        PriceProfileDomain priceProfileDomain  =priceProfileMap.get(unitPriceProfileMappingSheet.getPriceProfile());
        unitPriceProfileMappingDomain.setPriceProfile(new IdCodeName(priceProfileDomain.getPriceProfileDataId(),priceProfileDomain.getPriceProfileName(),
                priceProfileDomain.getPriceProfileName()));
        unitPriceProfileMappingDomain.setPriceProfileVersion(unitPriceProfileMappingSheet.getPriceProfileVersion());
        unitPriceProfileMappingDomain.setUnit(new IdCodeName(unitBasicDetail.getId(),unitBasicDetail.getName(),unitBasicDetail.getName()));
        unitPriceProfileMappingDomain.setChannelPartnerId(unitPriceProfileMappingSheet.getChannelPartnerId());
        unitPriceProfileMappingDomain.setBrandId(unitPriceProfileMappingSheet.getBrandId());
        unitPriceProfileMappingDomain.setMappingStatus(unitPriceProfileMappingSheet.getMappingStatus());
        return  unitPriceProfileMappingDomain;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ProductPricingMappingBulkUploadResponse parseAndCreateBulkProductPricingUpdateEvent(
            MultipartFile file, Integer updatedBy) throws IOException, DataNotFoundException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        Integer recordsUpdated  = 0;
        Integer recordsAdded = 0;
        AtomicReference<Integer> recordsWithErrors = new AtomicReference<>(0);
        List<String> validStatusList = new ArrayList<>(Arrays.asList(AppConstants.ACTIVE,AppConstants.IN_ACTIVE));
        int sheetCount = workbook.getNumberOfSheets();
        List<ProductPriceProfileMappingSheet> entityList = new ArrayList<>();
        for (int i = 0; i < sheetCount; i++) {
            log.info("Parsing Bulk Product Price Profile Sheet {}", i + 1);
            List<ProductPriceProfileMappingSheet> sheetDetailList = parser.createEntity(workbook.getSheetAt(i),
                    ProductPriceProfileMappingSheet.class, errors::add);
            entityList.addAll(sheetDetailList);
        }
        //Set<Integer> priceProfileIds = new HashSet<>();
        Map<Integer,Set<Integer>> versionNumbersByProfile = new HashMap<>();
        Map<Integer,List<PriceProfileProductMappingDomain>> productPricingByProfile = new HashMap<>();
        Map<String,PriceProfileDomain> priceProfileMap =   priceProfileDao.findAllByStatus(AppConstants.ACTIVE).stream().map(
                profile ->toDTO(profile)).collect(Collectors.toMap(PriceProfileDomain::getPriceProfileName,Function.identity()));
        Map<String,PriceProfileData> priceProfileDataMap =   priceProfileDao.findAllByStatus(AppConstants.ACTIVE).stream()
                .collect(Collectors.toMap(PriceProfileData::getPriceProfileName,Function.identity()));
        /*Map<Integer,IdCodeName> dimensionCodeMap = masterDataCache.getListData().get(ListTypes.DIMENSION_CODES).stream().
                collect(Collectors.toMap(IdCodeName::getId, Function.identity()));*/
        Map<Integer,List<IdCodeName>> dimensionListDataMap = new HashMap<>();
        Map<Integer,Product> productMap = masterDataCache.getProductDetails();
        masterMetadataDao.getAllListData().get(ListTypes.DIMENSION_CODES.getGroup()).stream()
                .forEach(listData -> {
                    if(!dimensionListDataMap.containsKey(listData.getDetail().getId())){
                        dimensionListDataMap.put(listData.getDetail().getId(),listData.getContent());
                    }
                });
        Set<Integer> refLookupIds  = new HashSet<>();
        List<String> filerList = new ArrayList<>();
        List<PriceProfileProductMapping> updateList = new ArrayList<>();
        if(CollectionUtils.isEmpty(errors)){
            entityList.stream().filter(mapping ->
                    Objects.nonNull(mapping.getPriceProfileName()) && Objects.nonNull(mapping.getPriceProfileVersion())
               && Objects.nonNull(mapping.getProductId()) && Objects.nonNull(mapping.getPrice())
             && Objects.nonNull(mapping.getDimension()) && Objects.nonNull(mapping.getStatus())).forEach(productPriceMapping ->{
                PriceProfileDomain priceProfileDomain = priceProfileMap.get(productPriceMapping.getPriceProfileName());
                if(Objects.isNull(priceProfileDomain)){
                    filerList.add("InValid Price Profile ::::: " + productPriceMapping.getPriceProfileName());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return;
                }
                Boolean validVersion = priceProfileDomain.getPriceProfileVersions().stream()
                                .anyMatch(version -> version.getVersionNo().equals(productPriceMapping.getPriceProfileVersion()));
                if(Boolean.FALSE.equals(validVersion)){
                    filerList.add("InValid Price Profile Version ::::: " +  productPriceMapping.getPriceProfileVersion()
                            + " For Price Profile :::: "+ productPriceMapping.getPriceProfileName());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return;
                }
                Product product = productMap.get(productPriceMapping.getProductId());
                if(Objects.isNull(product)){
                    filerList.add("Invalid Product  ::: " + productPriceMapping.getProductId());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return;
                }
                String dimesnionName = productPriceMapping.getDimension();
                Integer productDimensionProfile = product.getDimensionProfileId();
                Optional<IdCodeName> dimensionOptional = dimensionListDataMap.get(productDimensionProfile).stream().
                        filter(dim -> dim.getName().equalsIgnoreCase(dimesnionName))
                        .findAny();
                if(!dimensionOptional.isPresent()){
                    filerList.add("Invalid Dimension ::: " +  productPriceMapping.getDimension());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return;
                }
                if(!validStatusList.contains(productPriceMapping.getStatus())){
                    filerList.add("Invalid Status :: " + productPriceMapping.getStatus());
                    recordsWithErrors.set(recordsWithErrors.get() + 1);
                    return;
                }
                refLookupIds.add(dimensionOptional.get().getId());
                if(!productPricingByProfile.containsKey(priceProfileDomain.getPriceProfileDataId())){
                    productPricingByProfile.put(priceProfileDomain.getPriceProfileDataId(),new ArrayList<>());
                }
                productPricingByProfile.get(priceProfileDomain.getPriceProfileDataId()).add(convertFromSheet(productPriceMapping,productMap
                ,priceProfileMap,dimensionOptional.get()));

                //priceProfileIds.add(priceProfileMap.get(productPriceMapping.getPriceProfileName()).getPriceProfileDataId());
                if(!versionNumbersByProfile.containsKey(priceProfileDomain.getPriceProfileDataId())){
                    versionNumbersByProfile.put(priceProfileDomain.getPriceProfileDataId(),new HashSet<>());
                }
                versionNumbersByProfile.get(priceProfileDomain.getPriceProfileDataId()).add(productPriceMapping.getPriceProfileVersion());
            });

        }else{
            log.info("Error Parsing Workbook for  Product Price Bulk Update, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            log.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        Map<Integer, RefLookup> refLookupMap = masterMetadataDao.getRefLookUpByIds(refLookupIds.stream().toList()).stream()
                .collect(Collectors.toMap(RefLookup::getRlId,Function.identity()));
        for(Integer profileId : versionNumbersByProfile.keySet()){
              Set<Integer> versionNumbers = versionNumbersByProfile.get(profileId);
            Map<ProductPriceProfileMappingKey,PriceProfileProductMapping> currentMapings =  priceProfileProductMappingsDao.filterMappingsByProfileAndVersion(versionNumbers.stream().toList(),
                      new ArrayList<>(Arrays.asList(profileId))).stream()
                     .collect(Collectors.toMap(mp -> ProductPriceProfileMappingKey.builder()
                             .priceProfileId(mp.getPriceProfileData().getPriceProfileDataId()).versionNumber(mp.getVersion())
                             .productId(mp.getProductId()).dimensionId(mp.getDimensionCode().getRlId()).build(),Function.identity()));
            Map<ProductPriceProfileMappingKey,PriceProfileProductMappingDomain> newMappings = productPricingByProfile.get(profileId).stream()
                      .collect(Collectors.toMap(mp -> ProductPriceProfileMappingKey.builder()
                              .priceProfileId(mp.getPriceProfileId().getId()).versionNumber(mp.getVersion())
                              .productId(mp.getProductId().getId()).dimensionId(mp.getDimensionCode().getId()).build(),Function.identity()));
            for(ProductPriceProfileMappingKey key : newMappings.keySet()){
                PriceProfileProductMappingDomain newMapping = newMappings.get(key);
                if(currentMapings.containsKey(key)){
                    PriceProfileProductMapping priceProfileProductMapping =  currentMapings.get(key);
                    if(!priceProfileProductMapping.getStatus().equalsIgnoreCase(newMapping.getStatus())
                     || priceProfileProductMapping.getPrice().compareTo(newMapping.getPrice()) != 0){
                        priceProfileProductMapping.setStatus(newMapping.getStatus());
                        priceProfileProductMapping.setPrice(newMapping.getPrice());
                        priceProfileProductMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
                        priceProfileProductMapping.setLastUpdatedBy(updatedBy);
                        recordsUpdated++;
                        updateList.add(priceProfileProductMapping);
                    }
                }else{
                   PriceProfileProductMapping mapping = convert(newMapping,priceProfileDataMap.get(newMapping.getPriceProfileId().getName()),refLookupMap);
                   mapping.setCreatedBy(updatedBy);
                   mapping.setCreationTime(AppUtils.getCurrentTimestamp());
                   recordsAdded++;
                   updateList.add(mapping);
                }
            }


        }
        if(!CollectionUtils.isEmpty(updateList)){
            priceProfileProductMappingsDao.saveAll(updateList);
        }
        return ProductPricingMappingBulkUploadResponse.builder()
                .errors(filerList).recordsWithErrors(recordsWithErrors.get()).recordsAdded(recordsAdded).recordsUpdated(recordsUpdated)
                .build();

    }




    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getUnitPriceProfileSheet(List<UnitPriceProfileMappingDomain> mappings) {
        List<UnitPriceProfileMappingSheet>  rows = mappings.stream().map(mapping ->convertToSheet(mapping)).collect(Collectors.toList());
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest httpServletRequest, HttpServletResponse response) throws Exception {
                String fileName = "UnitProductPriceSheet.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                if (Objects.nonNull(rows) && !rows.isEmpty()) {
                    writer.writeSheet(rows, UnitPriceProfileMappingSheet.class);
                }
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getProductPriceProfileSheet(List<PriceProfileProductMappingDomain> mappings) {
        List<ProductPriceProfileMappingSheet>  rows = mappings.stream().map(mapping ->convertToSheet(mapping)).collect(Collectors.toList());
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest httpServletRequest, HttpServletResponse response) throws Exception {
                String fileName = "ProductPriceProfileSheet.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                if (Objects.nonNull(rows) && !rows.isEmpty()) {
                    writer.writeSheet(rows, ProductPriceProfileMappingSheet.class);
                }
            }
        };
    }

    public static UnitPriceProfileMappingDomain convert(UnitPriceProfileMapping unitPriceProfileMapping , Map<Integer, Unit> unitDetailMap ,
                                                        Map<Integer,PriceProfileDomain> priceProfileMap){
        UnitPriceProfileMappingDomain unitPriceProfileMappingDomain = new UnitPriceProfileMappingDomain();
        unitPriceProfileMappingDomain.setId(unitPriceProfileMapping.getId());
        Unit unit = unitDetailMap.get(unitPriceProfileMapping.getUnitId());
        PriceProfileDomain priceProfile = priceProfileMap.get(unitPriceProfileMapping.getPriceProfileId());
        unitPriceProfileMappingDomain.setUnit(new IdCodeName(unit.getId(),unit.getName(),"",unit.getShortCode()
                ,unit.getSubCategory().value(), unit.getStatus().value()));
        unitPriceProfileMappingDomain.setPriceProfile(new IdCodeName(priceProfile.getPriceProfileDataId(),priceProfile.getPriceProfileName(),""));
        unitPriceProfileMappingDomain.setChannelPartnerId(unitPriceProfileMapping.getChannelPartnerId());
        unitPriceProfileMappingDomain.setBrandId(unitPriceProfileMapping.getBrandId());
        unitPriceProfileMappingDomain.setMappingStatus(unitPriceProfileMapping.getMappingStatus());
        unitPriceProfileMappingDomain.setPriceProfileVersion(unitPriceProfileMapping.getPriceProfileVersion());
        unitPriceProfileMappingDomain.setUpdationTime(unitPriceProfileMapping.getUpdationTime());
        unitPriceProfileMappingDomain.setUpdatedBy(unitPriceProfileMapping.getUpdatedBy());
        unitPriceProfileMappingDomain.setCreationTime(unitPriceProfileMapping.getCreationTime());
        unitPriceProfileMappingDomain.setCreatedBy(unitPriceProfileMapping.getCreatedBy());
        return unitPriceProfileMappingDomain;

    }

    public static UnitPriceProfileMapping convert(UnitPriceProfileMappingDomain unitPriceProfileMappingDomain){
        UnitPriceProfileMapping unitPriceProfileMapping  = new  UnitPriceProfileMapping();
        unitPriceProfileMapping.setId(unitPriceProfileMappingDomain.getId());
        unitPriceProfileMapping.setPriceProfileId(unitPriceProfileMappingDomain.getPriceProfile().getId());
        unitPriceProfileMapping.setPriceProfileVersion(unitPriceProfileMappingDomain.getPriceProfileVersion());
        unitPriceProfileMapping.setUnitId(unitPriceProfileMappingDomain.getUnit().getId());
        unitPriceProfileMapping.setBrandId(unitPriceProfileMappingDomain.getBrandId());
        unitPriceProfileMapping.setChannelPartnerId(unitPriceProfileMappingDomain.getChannelPartnerId());
        unitPriceProfileMapping.setMappingStatus(unitPriceProfileMappingDomain.getMappingStatus());
        unitPriceProfileMapping.setCreatedBy(unitPriceProfileMappingDomain.getCreatedBy());
        unitPriceProfileMapping.setCreationTime(unitPriceProfileMappingDomain.getCreationTime());
        unitPriceProfileMapping.setUpdatedBy(unitPriceProfileMappingDomain.getUpdatedBy());
        unitPriceProfileMapping.setUpdationTime(unitPriceProfileMappingDomain.getUpdationTime());
        return unitPriceProfileMapping;

    }


    public static UnitPriceProfileMappingSheet convertToSheet(UnitPriceProfileMappingDomain unitPriceProfileMappingDomain){
        UnitPriceProfileMappingSheet unitPriceProfileMapping  = new UnitPriceProfileMappingSheet();
        unitPriceProfileMapping.setId(unitPriceProfileMappingDomain.getId());
        if(Objects.nonNull(unitPriceProfileMappingDomain.getPriceProfile())){
            unitPriceProfileMapping.setPriceProfile(unitPriceProfileMappingDomain.getPriceProfile().getName());
        }
        unitPriceProfileMapping.setPriceProfileVersion(unitPriceProfileMappingDomain.getPriceProfileVersion());
        unitPriceProfileMapping.setUnitId(unitPriceProfileMappingDomain.getUnit().getId());
        unitPriceProfileMapping.setUnitName(unitPriceProfileMappingDomain.getUnit().getName());
        unitPriceProfileMapping.setBrandId(unitPriceProfileMappingDomain.getBrandId());
        unitPriceProfileMapping.setChannelPartnerId(unitPriceProfileMappingDomain.getChannelPartnerId());
        unitPriceProfileMapping.setMappingStatus(unitPriceProfileMappingDomain.getMappingStatus());
        unitPriceProfileMapping.setCreatedBy(unitPriceProfileMappingDomain.getCreatedBy());
        unitPriceProfileMapping.setCreationTime(unitPriceProfileMappingDomain.getCreationTime());
        unitPriceProfileMapping.setUpdatedBy(unitPriceProfileMappingDomain.getUpdatedBy());
        unitPriceProfileMapping.setUpdationTime(unitPriceProfileMappingDomain.getUpdationTime());
        return unitPriceProfileMapping;

    }

    public static ProductPriceProfileMappingSheet convertToSheet(PriceProfileProductMappingDomain priceProfileProductMappingDomain){
        ProductPriceProfileMappingSheet productPriceProfileMappingSheet  = new ProductPriceProfileMappingSheet();
        productPriceProfileMappingSheet.setProductId(priceProfileProductMappingDomain.getProductId().getId());
        productPriceProfileMappingSheet.setProductName(priceProfileProductMappingDomain.getProductId().getName());
        if(Objects.nonNull(priceProfileProductMappingDomain.getPriceProfileId())){
            productPriceProfileMappingSheet.setPriceProfileName(priceProfileProductMappingDomain.getPriceProfileId().getName());
        }
        if(Objects.nonNull(priceProfileProductMappingDomain.getDimensionCode())){
            productPriceProfileMappingSheet.setDimension(priceProfileProductMappingDomain.getDimensionCode().getName());
        }
        productPriceProfileMappingSheet.setPriceProfileVersion(priceProfileProductMappingDomain.getVersion());
        productPriceProfileMappingSheet.setPrice(priceProfileProductMappingDomain.getPrice());
        productPriceProfileMappingSheet.setStatus(priceProfileProductMappingDomain.getStatus());
        return productPriceProfileMappingSheet;

    }

    public static PriceProfileProductMappingDomain convertFromSheet(ProductPriceProfileMappingSheet productPriceProfileMappingSheet ,
                                                                    Map<Integer,Product> productMap , Map<String,PriceProfileDomain> priceProfileMap
       ,IdCodeName dimension){
        PriceProfileProductMappingDomain priceProfileProductMappingDomain = new PriceProfileProductMappingDomain();
        PriceProfileDomain priceProfileDomain = priceProfileMap.get(productPriceProfileMappingSheet.getPriceProfileName());
        priceProfileProductMappingDomain.setPriceProfileId(new IdCodeName(priceProfileDomain.getPriceProfileDataId()
                ,priceProfileDomain.getPriceProfileName()
                ,""));
        priceProfileProductMappingDomain.setProductId(productMap.get(productPriceProfileMappingSheet.getProductId()));
        priceProfileProductMappingDomain.setDimensionCode(dimension);
        priceProfileProductMappingDomain.setPrice(productPriceProfileMappingSheet.getPrice());
        priceProfileProductMappingDomain.setNewPrice(productPriceProfileMappingSheet.getPrice());
        priceProfileProductMappingDomain.setStatus(productPriceProfileMappingSheet.getStatus());
        priceProfileProductMappingDomain.setNewStatus(productPriceProfileMappingSheet.getStatus());
        priceProfileProductMappingDomain.setVersion(productPriceProfileMappingSheet.getPriceProfileVersion());
        return priceProfileProductMappingDomain;

    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean savePriceProfileProductMappings(List<PriceProfileProductMappingDomain> priceProfileProductMappingDomains
    , Integer updatedeBy) {
        List<PriceProfileProductMapping> updateMappingsList = new ArrayList<>();
        Set<Integer> priceProfileIds = new HashSet<>();
        Set<Integer> refLookupIds  = new HashSet<>();
        priceProfileProductMappingDomains.forEach(mapping ->{
            priceProfileIds.add(mapping.getPriceProfileId().getId());
            refLookupIds.add(mapping.getDimensionCode().getId());
        });
        Map<Integer,PriceProfileData> priceProfileDataMap = new HashMap<>();
        priceProfileDao.findAllById(priceProfileIds).forEach(priceProfile -> priceProfileDataMap.put(priceProfile.getPriceProfileDataId(),priceProfile));
        Map<Integer, RefLookup> refLookupMap = masterMetadataDao.getRefLookUpByIds(refLookupIds.stream().toList()).stream()
                        .collect(Collectors.toMap(RefLookup::getRlId,Function.identity()));
        Map<Integer,Set<Integer>> versionNumbersByProfile = new HashMap<>();
        Map<Integer,List<PriceProfileProductMappingDomain>> productPricingByProfile = new HashMap<>();
        priceProfileProductMappingDomains.stream().filter(mapping ->
                Objects.nonNull(mapping.getPriceProfileId()) && Objects.nonNull(mapping.getVersion())
                        && Objects.nonNull(mapping.getProductId()) && Objects.nonNull(mapping.getNewPrice())
                        && Objects.nonNull(mapping.getDimensionCode()) && Objects.nonNull(mapping.getNewStatus())).forEach(mapping ->{
                            mapping.setPrice(mapping.getNewPrice());
                            mapping.setStatus(mapping.getNewStatus());
                if(!productPricingByProfile.containsKey(mapping.getPriceProfileId().getId())){
                    productPricingByProfile.put(mapping.getPriceProfileId().getId(),new ArrayList<>());
               }
                 productPricingByProfile.get(mapping.getPriceProfileId().getId()).add(mapping);
                    if(!versionNumbersByProfile.containsKey(mapping.getPriceProfileId().getId())){
                        versionNumbersByProfile.put(mapping.getPriceProfileId().getId(),new HashSet<>());
                    }
                    versionNumbersByProfile.get(mapping.getPriceProfileId().getId()).add(mapping.getVersion());
                }
        );
        for(Integer profileId : versionNumbersByProfile.keySet()){
            Set<Integer> versionNumbers = versionNumbersByProfile.get(profileId);
            Map<ProductPriceProfileMappingKey,PriceProfileProductMapping> currentMapings =  priceProfileProductMappingsDao.filterMappingsByProfileAndVersion(versionNumbers.stream().toList(),
                            new ArrayList<>(Collections.singletonList(profileId))).stream()
                    .collect(Collectors.toMap(mp -> ProductPriceProfileMappingKey.builder()
                            .priceProfileId(mp.getPriceProfileData().getPriceProfileDataId()).versionNumber(mp.getVersion())
                            .productId(mp.getProductId()).dimensionId(mp.getDimensionCode().getRlId()).build(),Function.identity()));
            Map<ProductPriceProfileMappingKey,PriceProfileProductMappingDomain> newMappings = productPricingByProfile.get(profileId).stream()
                    .collect(Collectors.toMap(mp -> ProductPriceProfileMappingKey.builder()
                            .priceProfileId(mp.getPriceProfileId().getId()).versionNumber(mp.getVersion())
                            .productId(mp.getProductId().getId()).dimensionId(mp.getDimensionCode().getId()).build(),Function.identity()));
            for(ProductPriceProfileMappingKey key : newMappings.keySet()){
                PriceProfileProductMappingDomain newMapping = newMappings.get(key);
                if(currentMapings.containsKey(key)){
                    PriceProfileProductMapping priceProfileProductMapping =  currentMapings.get(key);
                    if(!priceProfileProductMapping.getStatus().equalsIgnoreCase(newMapping.getStatus())
                            || priceProfileProductMapping.getPrice().compareTo(newMapping.getPrice())!=0){
                        priceProfileProductMapping.setStatus(newMapping.getStatus());
                        priceProfileProductMapping.setPrice(newMapping.getPrice());
                        priceProfileProductMapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
                        priceProfileProductMapping.setLastUpdatedBy(updatedeBy);
                        updateMappingsList.add(priceProfileProductMapping);
                    }
                }else{
                    PriceProfileProductMapping mapping = convert(newMapping,priceProfileDataMap.get(newMapping.getPriceProfileId().getId()),refLookupMap);
                    mapping.setCreatedBy(updatedeBy);
                    mapping.setCreationTime(AppUtils.getCurrentTimestamp());
                    updateMappingsList.add(mapping);
                }
            }

        }

//        priceProfileProductMappingDomains.stream().filter(mapping ->
//                Objects.nonNull(mapping.getPriceProfileId()) && Objects.nonNull(mapping.getVersion())
//                        && Objects.nonNull(mapping.getProductId()) && Objects.nonNull(mapping.getNewPrice())
//                        && Objects.nonNull(mapping.getDimensionCode()) && Objects.nonNull(mapping.getNewStatus())).forEach(mapping ->{
//            mapping.setLastUpdatedBy(updatedeBy);
//            mapping.setLastUpdationTime(AppUtils.getCurrentTimestamp());
//            if(Boolean.TRUE.equals(mapping.getIsNew())){
//                mapping.setCreationTime(AppUtils.getCurrentTimestamp());
//                mapping.setCreatedBy(updatedeBy);
//            }
//            updateMappingsList.add(convert(mapping,priceProfileDataMap.get(mapping.getPriceProfileId().getId()),refLookupMap));                                                                                                          ));
//        });
        if(!CollectionUtils.isEmpty(updateMappingsList)){
            priceProfileProductMappingsDao.saveAll(updateMappingsList);
            return true;
        }

        return false;


    }

    public static PriceProfileProductMappingDomain convert(PriceProfileProductMapping priceProfileProductMapping
    , Map<Integer,IdCodeName> dimensionCodeMap,MasterDataCache masterDataCache){
        PriceProfileProductMappingDomain priceProfileProductMappingDomain = new PriceProfileProductMappingDomain();
        priceProfileProductMappingDomain.setId(priceProfileProductMapping.getId());
        priceProfileProductMappingDomain.setPriceProfileId(new IdCodeName(priceProfileProductMapping.getPriceProfileData().getPriceProfileDataId()
                        ,priceProfileProductMapping.getPriceProfileData().getPriceProfileName()
        ,""));
        priceProfileProductMappingDomain.setDimensionCode(dimensionCodeMap.get(priceProfileProductMapping.getDimensionCode().getRlId()));
        priceProfileProductMappingDomain.setPrice(priceProfileProductMapping.getPrice());
        priceProfileProductMappingDomain.setNewPrice(priceProfileProductMapping.getPrice());
        priceProfileProductMappingDomain.setStatus(priceProfileProductMapping.getStatus());
        priceProfileProductMappingDomain.setNewStatus(priceProfileProductMapping.getStatus());
        priceProfileProductMappingDomain.setProductId(masterDataCache.getProduct(priceProfileProductMapping.getProductId()));
        priceProfileProductMappingDomain.setCreatedBy(priceProfileProductMapping.getCreatedBy());
        priceProfileProductMappingDomain.setCreationTime(priceProfileProductMapping.getCreationTime());
        priceProfileProductMappingDomain.setLastUpdatedBy(priceProfileProductMapping.getLastUpdatedBy());
        priceProfileProductMappingDomain.setLastUpdationTime(priceProfileProductMapping.getLastUpdationTime());
        priceProfileProductMappingDomain.setVersion(priceProfileProductMapping.getVersion());
        return priceProfileProductMappingDomain;
    }

    public static PriceProfileProductMapping convert(PriceProfileProductMappingDomain priceProfileProductMappingDomain ,
                                                     PriceProfileData priceProfileData ,  Map<Integer, RefLookup> refLookupMap){
        PriceProfileProductMapping priceProfileProductMapping = new PriceProfileProductMapping();
        priceProfileProductMapping.setId(priceProfileProductMappingDomain.getId());
        priceProfileProductMapping.setPriceProfileData(priceProfileData);
        priceProfileProductMapping.setDimensionCode(refLookupMap.get(priceProfileProductMappingDomain.getDimensionCode().getId()));
        priceProfileProductMapping.setPrice(priceProfileProductMappingDomain.getNewPrice());
        priceProfileProductMapping.setStatus(priceProfileProductMappingDomain.getNewStatus());
        priceProfileProductMapping.setProductId(priceProfileProductMappingDomain.getProductId().getId());
        priceProfileProductMapping.setCreatedBy(priceProfileProductMappingDomain.getCreatedBy());
        priceProfileProductMapping.setCreationTime(priceProfileProductMappingDomain.getCreationTime());
        priceProfileProductMapping.setLastUpdatedBy(priceProfileProductMappingDomain.getLastUpdatedBy());
        priceProfileProductMapping.setLastUpdationTime(priceProfileProductMappingDomain.getLastUpdationTime());
        priceProfileProductMapping.setVersion(priceProfileProductMappingDomain.getVersion());
        return priceProfileProductMapping;
    }

    public PriceProfileDomain toDTO(PriceProfileData priceProfileData) {
        if (priceProfileData == null) {
            return null;
        }

        PriceProfileDomain dto = new PriceProfileDomain();
        dto.setPriceProfileDataId(priceProfileData.getPriceProfileDataId());
        dto.setPriceProfileName(priceProfileData.getPriceProfileName());
        dto.setStatus(priceProfileData.getStatus());
        dto.setCreationTime(priceProfileData.getCreationTime());
        dto.setCreatedBy(priceProfileData.getCreatedBy());
        dto.setUpdationTime(priceProfileData.getUpdationTime());
        dto.setUpdateBy(priceProfileData.getUpdateBy());
        dto.setBrandId(priceProfileData.getBrandId());
        dto.setChannelPartnerId(priceProfileData.getChannelPartnerId());

        List<PriceProfileVersionsDomain> versionDTOs = priceProfileData.getPriceProfileVersions().stream()
                .map(this::toDTO)
                .collect(Collectors.toList());
        dto.setPriceProfileVersions(versionDTOs);

        return dto;
    }

    public static PriceProfileData toEntity(PriceProfileDomain priceProfileDataDTO) {
        if (priceProfileDataDTO == null) {
            return null;
        }

        PriceProfileData entity = new PriceProfileData();
        entity.setPriceProfileDataId(priceProfileDataDTO.getPriceProfileDataId());
        entity.setPriceProfileName(priceProfileDataDTO.getPriceProfileName());
        entity.setStatus(priceProfileDataDTO.getStatus());
        entity.setCreationTime(priceProfileDataDTO.getCreationTime());
        entity.setCreatedBy(priceProfileDataDTO.getCreatedBy());
        entity.setUpdationTime(priceProfileDataDTO.getUpdationTime());
        entity.setUpdateBy(priceProfileDataDTO.getUpdateBy());
        entity.setBrandId(priceProfileDataDTO.getBrandId());
        entity.setChannelPartnerId(priceProfileDataDTO.getChannelPartnerId());

        List<PriceProfileVersion> versions = priceProfileDataDTO.getPriceProfileVersions().stream()
                .map(PriceProfileManagementServiceImpl::toEntity)
                .collect(Collectors.toList());
        entity.setPriceProfileVersions(versions);

        return entity;
    }

    public PriceProfileVersionsDomain toDTO(PriceProfileVersion priceProfileVersion) {
        if (priceProfileVersion == null) {
            return null;
        }

        PriceProfileVersionsDomain dto = new PriceProfileVersionsDomain();
        dto.setPriceProfileVersionsId(priceProfileVersion.getPriceProfileVersionsId());
        dto.setPriceProfileId(priceProfileVersion.getPriceProfileData().getPriceProfileDataId());
        dto.setVersionNo(priceProfileVersion.getVersionNo());
        dto.setStatus(priceProfileVersion.getStatus());
        dto.setCreationTime(priceProfileVersion.getCreationTime());
        dto.setCreatedBy(priceProfileVersion.getCreatedBy());
        dto.setUpdatedBy(priceProfileVersion.getUpdatedBy());
        dto.setUpdationTime(priceProfileVersion.getUpdationTime());

        return dto;
    }

    public static PriceProfileVersion toEntity(PriceProfileVersionsDomain priceProfileVersionDTO) {
        if (priceProfileVersionDTO == null) {
            return null;
        }

        PriceProfileVersion entity = new PriceProfileVersion();
        entity.setPriceProfileVersionsId(priceProfileVersionDTO.getPriceProfileVersionsId());
        entity.setVersionNo(priceProfileVersionDTO.getVersionNo());
        entity.setStatus(priceProfileVersionDTO.getStatus());
        entity.setCreationTime(priceProfileVersionDTO.getCreationTime());
        entity.setCreatedBy(priceProfileVersionDTO.getCreatedBy());
        entity.setUpdatedBy(priceProfileVersionDTO.getUpdatedBy());
        entity.setUpdationTime(priceProfileVersionDTO.getUpdationTime());


        //entity.setPriceProfileData(priceProfileVersionDTO.getPriceProfileId());

        return entity;
    }
}
