/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.master.core.exception;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * <AUTHOR>
 *
 */
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR, reason = "File Parsing Exception")
public class FileParsingException extends Exception {


	/**
	 * 
	 */
	private static final long serialVersionUID = -5519581190159057797L;

	private List<String> parsingErrors;
	
	public FileParsingException() {
	}

	public FileParsingException(String message, List<String> parsingErrors) {
		super(message);
		this.parsingErrors = parsingErrors;
	}

	public FileParsingException(Throwable cause) {
		super(cause);
	}

	public FileParsingException(String message, Throwable cause) {
		super(message, cause);
	}

	public FileParsingException(String message, Throwable cause, boolean enableSuppression,
			boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

	public List<String> getParsingErrors() {
		return parsingErrors;
	}

	public void setParsingErrors(List<String> parsingErrors) {
		this.parsingErrors = parsingErrors;
	}
	
	
}
