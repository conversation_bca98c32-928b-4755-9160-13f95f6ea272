package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "PRODUCT_TAG_MAPPING_DETAIL")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductTagMappingDetail implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MAPPING_ID", unique = true, nullable = false)
    private Integer mappingId;

    @Column(name = "RTL_ID", nullable = false)
    private Integer rtlId;

    @Column(name = "RL_ID", nullable = false)
    private Integer rlId;

    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;

    @Column(name = "MAPPING_TYPE", nullable = false)
    private String mappingType;

    @Column(name = "MAPPING_VALUE", nullable = false)
    private String mappingValue;

    @Column(name = "MAPPING_STATUS", nullable = false)
    private String mappingStatus;

}
