package com.stpl.tech.master.data.model;

public enum UnitPartnerBrandMappingMetadataType {
    LOYALTY_POINTS("java.lang.Integer", "10"),
    LOYALTY_REDEMPTION_AVAILABLE("java.lang.String", "N"),
    REFERRAL_REDEMPTION_AVAILABLE("java.lang.String", "N");

    private String dataType;

    private String defaultValue;

    UnitPartnerBrandMappingMetadataType(String dataType, String defaultValue) {
        this.dataType = dataType;
        this.defaultValue = defaultValue;
    }

    public String getDataType() {
        return dataType;
    }

    public String getDefaultValue() {
        return defaultValue;
    }
}
