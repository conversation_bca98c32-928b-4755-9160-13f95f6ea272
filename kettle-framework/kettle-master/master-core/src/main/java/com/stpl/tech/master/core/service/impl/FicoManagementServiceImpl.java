package com.stpl.tech.master.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.FicoManagementService;
import com.stpl.tech.master.data.dao.FicoManagementDao;
import com.stpl.tech.master.domain.model.FicoDetail;

@Service
public class FicoManagementServiceImpl implements FicoManagementService {
	@Autowired
	private FicoManagementDao ficoManagementDao;

	@Override
	public List<FicoDetail> getFicoList() {
		List<FicoDetail> list = new ArrayList<>();
		ficoManagementDao.getFicoList();
		return list;
	}

	@Override
	public boolean addFicoDetails(FicoDetail ficoDetail) throws DataUpdationException {
		return ficoManagementDao.addFicoDetails(ficoDetail);
	}

	@Override
	public boolean updateFicoDetails(FicoDetail ficoDetail) {
		// TODO Auto-generated method stub
		return false;
	}

}
