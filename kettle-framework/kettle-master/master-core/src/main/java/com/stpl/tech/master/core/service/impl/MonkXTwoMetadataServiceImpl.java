package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.service.MonkXTwoMetadataService;
import com.stpl.tech.master.core.service.model.MonkUrlDto;
import com.stpl.tech.master.core.service.model.MonkXTwoMetadataRequest;
import com.stpl.tech.master.data.dao.UnitDetailRepository;
import com.stpl.tech.master.data.model.MonkXTwoMetadata;
import com.stpl.tech.master.data.model.MonkXTwoMetadataResponse;
import com.stpl.tech.master.data.model.MonkXTwoUrls;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.repository.MonkXTwoMetadataRepository;
import com.stpl.tech.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityNotFoundException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class MonkXTwoMetadataServiceImpl implements MonkXTwoMetadataService {

    private static final Logger LOGGER = Logger.getLogger(MonkXTwoMetadataServiceImpl.class.getName());

    @Autowired
    private MonkXTwoMetadataRepository monkXTwoMetadataRepository;

    @Autowired
    private UnitDetailRepository unitDetailRepository;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public MonkXTwoMetadata createMonkMetadata(MonkXTwoMetadataRequest request) {
        try {
            // Fetch UnitDetail by unitId
            UnitDetail unitDetail = unitDetailRepository.findById(request.getUnitId())
                    .orElseThrow(() -> new EntityNotFoundException("UnitDetail with ID " + request.getUnitId() + " not found."));

            // Create new MonkXTwoMetadata instance
            MonkXTwoMetadata monkMetadata = new MonkXTwoMetadata();
            monkMetadata.setNoOfX2Machines(request.getNoOfXTwoMachines());
            monkMetadata.setVesselSenseDelay(request.getVesselSenseDelay());
            monkMetadata.setSpiceSenseDelay(request.getSpiceSenseDelay());

            // Associate with UnitDetail
            monkMetadata.setUnitDetail(unitDetail);
            unitDetail.setMonkXTwoMetadata(monkMetadata);

            return monkXTwoMetadataRepository.save(monkMetadata);
        } catch (Exception e) {
            LOGGER.severe("Error while creating MonkXTwoMetadata: " + e.getMessage());
            throw new RuntimeException("Failed to create MonkXTwoMetadata.", e);
        }
    }

    @Override
    public MonkXTwoMetadataResponse getMonkMetaDataByUnitId(Integer unitId) {
        try {
            return monkXTwoMetadataRepository.findByUnitDetail_UnitIdWithUrls(unitId)
                           .map(monkXTwoMetadata -> {
                               // Convert monk URLs to DTOs if they exist
                               List<MonkUrlDto> urlDtos = null;
                               if (monkXTwoMetadata.getMonkUrls() != null && !monkXTwoMetadata.getMonkUrls().isEmpty()) {
                                   urlDtos = monkXTwoMetadata.getMonkUrls().stream()
                                                     .map(url -> new MonkUrlDto(
                                                             url.getUrlId(),
                                                             url.getMonkUrl(),
                                                             url.getMachineIndex(),
                                                             url.getIsActive(),
                                                             url.getLastUpdatedTimestamp()
                                                     ))
                                                     .collect(Collectors.toList());
                               }
                               
                               return new MonkXTwoMetadataResponse(
                                       monkXTwoMetadata.getId(),
                                       monkXTwoMetadata.getUnitDetail() != null ? monkXTwoMetadata.getUnitDetail().getUnitId() : null,
                                       monkXTwoMetadata.getNoOfX2Machines(),
                                       monkXTwoMetadata.getVesselSenseDelay(),
                                       monkXTwoMetadata.getSpiceSenseDelay(),
                                       monkXTwoMetadata.getLastUpdatedTimestamp(),
                                       urlDtos
                               );
                           })
                           .orElseGet(() -> {
                               // If no data found, return a response with null fields
                               return new MonkXTwoMetadataResponse(null, null, null, null, null, null, null);
                           });
        } catch (Exception e) {
            LOGGER.severe("Error retrieving monk metadata: " + e.getMessage());
            return new MonkXTwoMetadataResponse(null, null, null, null, null, null, null);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateMonkMetadata(MonkXTwoMetadataRequest request) {
        try {
            // Fetch UnitDetail by unitId
            UnitDetail unitDetail = unitDetailRepository.findById(request.getUnitId())
                    .orElseThrow(() -> new EntityNotFoundException("UnitDetail with ID " + request.getUnitId() + " not found."));

            MonkXTwoMetadata monkMetadata = unitDetail.getMonkXTwoMetadata();
            if (monkMetadata == null) {
                monkMetadata = new MonkXTwoMetadata();
                monkMetadata.setUnitDetail(unitDetail);
            }

            monkMetadata.setNoOfX2Machines(request.getNoOfXTwoMachines());
            monkMetadata.setVesselSenseDelay(request.getVesselSenseDelay());
            monkMetadata.setSpiceSenseDelay(request.getSpiceSenseDelay());
            monkMetadata.setLastUpdatedTimestamp(AppUtils.getCurrentTimestamp());
            
            if (request.getMonkUrls() != null && !request.getMonkUrls().isEmpty()) {
                updateMonkUrlsFromRequest(monkMetadata, request.getMonkUrls());
            }

            // Save metadata (cascades to unitDetail if mapped correctly)
            monkXTwoMetadataRepository.save(monkMetadata);

            return true;
        } catch (EntityNotFoundException ex) {
            LOGGER.warning("Unit not found: " + ex.getMessage());
            return false;
        } catch (Exception ex) {
            LOGGER.severe("Unexpected error while updating MonkXTwoMetadata: " + ex.getMessage());
            ex.printStackTrace(); // Optional: helpful in development
            return false;
        }
    }
    
    private void updateMonkUrlsFromRequest(MonkXTwoMetadata monkMetadata, List<MonkUrlDto> urlDtos) {
        
        // Map existing MonkXTwoUrls by urlId for quick lookup
        Map<Integer, MonkXTwoUrls> existingUrlMap = Optional.ofNullable(monkMetadata.getMonkUrls())
                                                            .orElse(Collections.emptyList())
                                                            .stream()
                                                            .filter(url -> url.getUrlId() != null)
                                                            .collect(Collectors.toMap(MonkXTwoUrls::getUrlId, Function.identity()));
        
        List<MonkXTwoUrls> updatedUrls = new ArrayList<>();
        
        for (MonkUrlDto dto : urlDtos) {
            MonkXTwoUrls url;
            
            if (dto.getUrlId() != null && existingUrlMap.containsKey(dto.getUrlId())) {
                // Update existing entry
                url = existingUrlMap.get(dto.getUrlId());
            } else {
                // Create new entry
                url = new MonkXTwoUrls();
                url.setCreatedDate(AppUtils.getCurrentTimestamp());
                url.setMonkMetadata(monkMetadata);
            }
            
            // Set or update common fields
            url.setMonkUrl(dto.getUrl());
            url.setMachineIndex(dto.getMachineIndex());
            url.setIsActive(dto.getIsActive() != null ? dto.getIsActive() : true);
            url.setLastUpdatedTimestamp(AppUtils.getCurrentTimestamp());
            
            updatedUrls.add(url);
        }
        
        // Set the updated list (Hibernate will handle the orphan removal if enabled)
        monkMetadata.setMonkUrls(updatedUrls);
    }
    
    
    
    
}
