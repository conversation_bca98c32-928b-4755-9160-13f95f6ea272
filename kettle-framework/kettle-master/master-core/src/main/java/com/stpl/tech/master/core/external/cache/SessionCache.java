/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.master.core.external.cache;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

@Service
public class SessionCache {

	private static final Logger LOG = LoggerFactory.getLogger(SessionCache.class);

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	private Map<String, SessionDetail> sessionKeyToDetailMap;

	// private Map<Integer, SessionDetail> userIdToDetailMap;

	private Map<Integer, SessionDetail> unitToSessionMap;

	private Map<Integer, Map<ScreenType, String>> screenTypeSessionCache;

	public SessionCache() {

	}

	@PostConstruct
	public void createMaps() {
    	LOG.info("POST-CONSTRUCT SessionCache - STARTED");
		sessionKeyToDetailMap = instance.getMap("sessionKeyToDetailMap");
		// userIdToDetailMap = instance.getMap("userIdToDetailMap");
		unitToSessionMap = instance.getMap("unitToSessionMap");
		screenTypeSessionCache = instance.getMap("screenTypeSessionCache");
	}

	public void addToCache(EnvType env, String sessionKey, int unitId, String unitName, int userId, String userName,
			Date loginTime, ScreenType screenType) {

		SessionDetail detail = sessionKeyToDetailMap.get(sessionKey);
		if (detail != null) {
			removeFromMaps(sessionKey);
		}
		detail = new SessionDetail(userId, loginTime, sessionKey, unitId);
		addToMaps(detail);
		if (!unitToSessionMap.containsKey(unitId)) {
			unitToSessionMap.put(unitId, detail);
			if (unitId > 0) {
				LOG.info(String.format("Publishing to slack user %d for unit %d", userId, unitId));
				SlackNotificationService.getInstance().sendNotification(env, "Kettle", SlackNotification.SYSTEM_LOGIN,
						"Unit : " + unitName + "\nLogin Time : " + AppUtils.getCurrentTimeISTString()
								+ "\nEmployee Name: " + userName);
			} else {
				LOG.info("Not Publishing to slack user {} for unit {}", userId, unitId);
			}
		}
		addToScreenTypeMap(unitId, screenType, sessionKey);
		LOG.info(String.format("Added session key for the user %d for unit %d", userId, unitId));
	}

	private void addToScreenTypeMap(int unitId, ScreenType screenType, String sessionKey) {
		// only for assembly clients right now, can be augmented for other screen types
		// as well
		if (
				ScreenType.ASSEMBLY.equals(screenType) ||
				ScreenType.WORKSTATION.equals(screenType) ||
				ScreenType.WS_COLD.equals(screenType) ||
				ScreenType.WS_FOOD.equals(screenType) ||
				ScreenType.WS_HOT.equals(screenType) ||
				ScreenType.WS_GNT.equals(screenType) ||
				ScreenType.NEW_WS_1.equals(screenType) || ScreenType.NEW_WS_2.equals(screenType) || ScreenType.NEW_WS_3.equals(screenType)
						|| ScreenType.NEW_WS_4.equals(screenType) || ScreenType.NEW_WS_5.equals(screenType)
		) {
			Map<ScreenType, String> screenTypeMap = screenTypeSessionCache.get(unitId);
			if (screenTypeMap == null) {
				screenTypeMap = new HashMap<>();
			}
			screenTypeMap.put(screenType, sessionKey);
			screenTypeSessionCache.put(unitId, screenTypeMap);
		}
	}

	public void removeFromCache(int userId, String sessionKey) {
		SessionDetail detail = sessionKeyToDetailMap.get(sessionKey);
		if (detail == null) {
			LOG.info(String.format("Trying to remove a user with ID %d which does not have a session with key %s",
					userId, sessionKey));
		} else {
			removeFromMaps(sessionKey);
		}
	}

	private void addToMaps(SessionDetail detail) {
		sessionKeyToDetailMap.put(detail.getSessionKey(), detail);
		// userIdToDetailMap.put(detail.getUserId(), detail);
	}

	private void removeFromMaps(String sessionKey) {
		sessionKeyToDetailMap.remove(sessionKey);
		// userIdToDetailMap.remove(userId);
	}

	public void refreshCache() {
		createMaps();
	}

	public boolean validateSession(String sessionKey, int unitId, int userId) throws AuthenticationFailureException {
		SessionDetail detail = sessionKeyToDetailMap.get(sessionKey);
		if(detail != null) {
			detail.setLastAccessTime(AppUtils.getCurrentTimestamp());
		}
		return detail != null && detail.getUserId() == userId && detail.getUnitId() == unitId;
	}

	public Map<Integer, SessionDetail> getUnitToSessionMap() {
		return unitToSessionMap;
	}

	public void setUnitToSessionMap(Map<Integer, SessionDetail> unitToSessionMap) {
		this.unitToSessionMap = unitToSessionMap;
	}

	public String getSessionKeyForScreenType(ScreenType screenType, Integer unitId) {
		String sessionKey = null;
		Map<ScreenType, String> screenTypeMap = screenTypeSessionCache.get(unitId);
		if (screenTypeMap != null && screenTypeMap.containsKey(screenType)) {
			sessionKey = screenTypeMap.get(screenType);
		}
		return sessionKey;
	}

	public void removeExpiredSessions() {
		List<String> list = new ArrayList<>();
		Date current = AppUtils.getCurrentTimestamp();
		for (String key : sessionKeyToDetailMap.keySet()) {
			SessionDetail detail = sessionKeyToDetailMap.get(key);
			long timeDiff = current.getTime()
					- (detail.getLastAccessTime() == null ? 0 : detail.getLastAccessTime().getTime());
			if (timeDiff > 259200000) {
				list.add(key);
			}
		}

		for (String session : list) {
			removeFromMaps(session);
		}
	}

	public void clearSessionCache(){
		sessionKeyToDetailMap.clear();
		unitToSessionMap.clear();
		screenTypeSessionCache.clear();
	}

	public Map<String, SessionDetail> getSessionKeyToDetailMap() {
		return sessionKeyToDetailMap;
	}

	public boolean isPosSession(String sessionKey) throws AuthenticationFailureException {
		String decryptedKey = PasswordImpl.decrypt(sessionKey);
		return decryptedKey.contains("screenType:" + ScreenType.POS.name());
	}

	@Override
	public String toString() {
		return "SessionCache{" +
				"sessionKeyToDetailMap=" + sessionKeyToDetailMap.size() +
				", unitToSessionMap=" + unitToSessionMap.size() +
				", screenTypeSessionCache=" + screenTypeSessionCache.size() +
				'}';
	}
}
