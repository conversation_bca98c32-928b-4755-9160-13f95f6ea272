/*
 * Created By Shanmukh
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "EMPLOYEE_USER_POLICY_RESET_LOG")
public class EmployeeUserPolicyResetLog {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "EMPLOYEE_USER_POLICY_RESET_LOG_ID",unique = true, nullable = false)
    private Integer employeeUserPolicyResetLogId;

    @Column(name = "EMPLOYEE_ID")
    private Integer employeeId;

    @Column(name = "PREVIOUS_USER_POLICY_ID")
    private Integer previousUserPolicyId;

    @Column(name = "UPLOADED_DOCUMENT_ID")
    private Integer uploadedDocumentId;

    @Column(name = "NEW_USER_POLICY_ID")
    private Integer newUserPolicyId;

    @Column(name = "LOGGED_BY")
    private String loggedBy;

    @Column(name = "LOGGED_AT")
    private Date loggedAt;

    public Integer getEmployeeUserPolicyResetLogId() {
        return this.employeeUserPolicyResetLogId;
    }

    public void setEmployeeUserPolicyResetLogId(Integer employeeUserPolicyResetLogId) {
        this.employeeUserPolicyResetLogId = employeeUserPolicyResetLogId;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public Integer getPreviousUserPolicyId() {
        return previousUserPolicyId;
    }

    public void setPreviousUserPolicyId(Integer previousUserPolicyId) {
        this.previousUserPolicyId = previousUserPolicyId;
    }

    public Integer getNewUserPolicyId() {
        return newUserPolicyId;
    }

    public void setNewUserPolicyId(Integer newUserPolicyId) {
        this.newUserPolicyId = newUserPolicyId;
    }

    public Integer getUploadedDocumentId() {
        return uploadedDocumentId;
    }

    public void setUploadedDocumentId(Integer uploadedDocumentId) {
        this.uploadedDocumentId = uploadedDocumentId;
    }

    public String getLoggedBy() {
        return this.loggedBy;
    }

    public void setLoggedBy(String loggedBy) {
        this.loggedBy = loggedBy;
    }

    public Date getLoggedAt() {
        return loggedAt;
    }

    public void setLoggedAt(Date loggedAt) {
        this.loggedAt = loggedAt;
    }
}
