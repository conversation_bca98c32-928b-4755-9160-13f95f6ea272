/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * SkuPriceKey generated by hbm2java
 */
@Entity
@Table(name = "CATEGORY_TAX_HISTORY")
public class CategoryTaxHistory implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4555281760158202631L;
	
	private Integer categoryTaxHistoryId;
	private int categoryTaxDataId;
	private BigDecimal currentTaxRate;
	private BigDecimal negotiatedTaxRate;
	private String changeType;
	private String recordStatus;
	private Date startDate;
	private String createdBy;
	private Date createdAt;
	private String updatedBy;
	private Date updatedAt;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CATEGORY_TAX_HISTORY_ID", unique = true, nullable = false)
	public Integer getCategoryTaxHistoryId() {
		return this.categoryTaxHistoryId;
	}

	public void setCategoryTaxHistoryId(Integer categoryTaxHistoryId) {
		this.categoryTaxHistoryId = categoryTaxHistoryId;
	}

	@Column(name = "CATEGORY_TAX_DATA_ID", nullable = false)
	public int getCategoryTaxDataId() {
		return this.categoryTaxDataId;
	}

	public void setCategoryTaxDataId(int categoryTaxDataId) {
		this.categoryTaxDataId = categoryTaxDataId;
	}

	@Column(name = "CURRENT_TAX_RATE", precision = 10)
	public BigDecimal getCurrentTaxRate() {
		return currentTaxRate;
	}

	public void setCurrentTaxRate(BigDecimal currentTaxRate) {
		this.currentTaxRate = currentTaxRate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Column(name = "NEGOTIATED_TAX_RATE", precision = 10)
	public BigDecimal getNegotiatedTaxRate() {
		return negotiatedTaxRate;
	}

	public void setNegotiatedTaxRate(BigDecimal negotiatedTaxRate) {
		this.negotiatedTaxRate = negotiatedTaxRate;
	}

	@Column(name = "RECORD_STATUS", length = 50)
	public String getRecordStatus() {
		return recordStatus;
	}

	public void setRecordStatus(String recordStatus) {
		this.recordStatus = recordStatus;
	}

	@Column(name = "CREATED_BY", length = 50)
	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_AT", nullable = true, length = 19)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "UPDATED_BY", length = 50)
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATED_AT", nullable = true, length = 19)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	
	@Column(name = "CHANGE_TYPE", nullable = true, length = 15)
	public String getChangeType() {
		return changeType;
	}

	public void setChangeType(String changeType) {
		this.changeType = changeType;
	}

	
}
