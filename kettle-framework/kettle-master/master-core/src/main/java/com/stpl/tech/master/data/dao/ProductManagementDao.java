/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.data.model.ProductCityImageMapping;
import com.stpl.tech.master.data.model.ProductDescription;
import com.stpl.tech.master.data.model.ProductDetail;
import com.stpl.tech.master.data.model.ProductDimensionImageMapping;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.data.model.ProductNutritionDetail;
import com.stpl.tech.master.data.model.RefLookup;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.data.model.UnitProductPriceCategoryDetail;
import com.stpl.tech.master.data.model.UnitProductPricing;
import com.stpl.tech.master.data.model.UnitProductProfileContext;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitProductMappingData;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ProductManagementDao extends AbstractMasterDao {

	public Product getProduct(int productId);

	public Product addProduct(Product product) throws DataUpdationException;

	public Product updateProduct(Product product);

	public boolean changeProductStatus(int productId, ProductStatus status);

	Map<Integer, Map<Integer, List<ProductRecipeKey>>> getUnitProductPriceProfiles();

    public boolean updateUnitProductPrice(List<UnitProductMappingData> mappings, Integer updatedBy);

	public List<UnitProductMappingData> getUnitProductPrice(UnitCategory unitCategory, String unitRegion, int productId,
			int dimensionId);

	RefLookup getRefLookUp(int refLookUpId);

	public List<UnitProductMappingData> getUnitProductPrice(UnitCategory unitCategory, List<String> unitRegions, int productId,
															int dimensionId, Set<String> pricingProfiles);
	public boolean updateUnitProductPrice(int unitId, int productId, String dimensionCode, BigDecimal price, String profile);

	public boolean updateUnitProductMappingStatus(int unitId, int productId, ProductStatus status);

	public boolean deactivateAllUnitProductMappingsForProduct(int productId);

	public List<UnitProductPricing> getProductProfileUnits(Integer productId, String profile);

    ProductImageMapping getProductImagesByProductIdAndType(int productId, String type, Integer index);
	ProductDimensionImageMapping getProductImagesByProductIdAndTypeAndDimension(int productId, String type, Integer index,Integer dimensionCode);
    ProductDescription getProductDescriptionDataByProductID(int productId);
	List<ProductDescription> getProductDescriptionDataListByProductID(List<Integer> list);
	List<ProductImageMapping> getProductImagesByProductId(int productId);
    List<ProductImageMapping> findInProductId(List<Integer> list);
	List<ProductDimensionImageMapping>getProductImagesByProductIdAndDimension(int productId,Integer dimensioncode);
	List<IdCodeName> getUnitDetailsException(UnitCategory unitCategory, String unitRegion);
	List<ProductDimensionImageMapping> getProductsImages(List<Integer> list);
	public List<Integer> findDistinctMenuToSCMRecipeId();

	List<ProductNutritionDetail> findNutritionForProductIds(List<Integer> list);

    List<Object[]> checkForRecipeProfiles(Integer productId, Integer dimensionId, String profile);

    public void truncateMenuToScmTable();

	Map<Integer, List<ProductRecipeKey>> getProductPriceProfilesForUnit(int unitId);


	List<ProductCityImageMapping> getProductImagesByProductIdAndCity(List<Integer> productIds, String cityName);
	ProductCityImageMapping getProductImageByProductIdAndCity(Integer productId, String cityName , String daySlot);

	List<ProductCityImageMapping> getProductImagesByCity(String cityName);

	List<ProductNutritionDetail> getProductsNutritionData(List<String> sourceType);

	List<UnitProductPriceCategoryDetail> getUnitsSpecificProductsPrice();

	List<ProductCityImageMapping> getProductImagesByCity(String cityName , String daySlot);

	List<UnitDetail> getUnitForSelectedRegion(UnitCategory unitCategory, List<String> unitRegion);

  	void updateUnitProductProfile(List<Integer> unitIds, String profile, Integer updatedBy, Integer productId, String dimensionCode);

	List<ProductDetail> getProductsByIds(List<Integer> productIds);

	Map<String, UnitProductMappingData> getUnitProductMapping(List<ProductDetail> productDetails, Set<Integer> unitIds, List<Integer> dimensionIds, UnitProductProfileContext context);

	List<UnitProductMappingData> getUnitProductPrice(int productId, int dimensionId, Set<Integer> unitIds, ProductDetail productDetail);

	Map<Integer, RefLookup> getRefLookUpByIds(List<Integer> refLookUpIds);

	Map<Integer, UnitProductMapping> getAllUnitProductMappingsByIds(Set<Integer> upmIds);

	List<UnitDetail> getUnitsByIds(List<Integer> unitIds);

	List<UnitProductMapping> getUnitProductMapping(ArrayList<Integer> unitIds, ArrayList<Integer> productIds);

	UnitProductMapping isUnitProductMappingExists(Integer productId, Integer unitId);

}
