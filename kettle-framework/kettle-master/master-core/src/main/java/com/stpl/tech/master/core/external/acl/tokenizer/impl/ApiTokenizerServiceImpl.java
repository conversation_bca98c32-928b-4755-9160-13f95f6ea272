package com.stpl.tech.master.core.external.acl.tokenizer.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.external.acl.dao.ApiTokenizerDao;
import com.stpl.tech.master.core.external.acl.tokenizer.ApiTokenizerService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.TokenizedApiData;
import com.stpl.tech.master.domain.model.TokenizedApi;

@Service
public class ApiTokenizerServiceImpl implements ApiTokenizerService {

	Logger LOG = LoggerFactory.getLogger(ApiTokenizerServiceImpl.class);

	@Autowired
	private ApiTokenizerDao apiTokenizerDao;

	@Override
	public List<TokenizedApi> getAllTokenizedApi() {
		return apiTokenizerDao.findAll(TokenizedApiData.class).stream().map(MasterDataConverter::convert)
				.collect(Collectors.toList());
	}
}
