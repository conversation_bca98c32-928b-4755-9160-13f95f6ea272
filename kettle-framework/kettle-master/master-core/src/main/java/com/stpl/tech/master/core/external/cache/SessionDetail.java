/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.cache;

import java.io.Serializable;
import java.util.Date;

import com.stpl.tech.util.AppUtils;

public class SessionDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5216404700526537437L;

	private int userId;

	private Date loginTime;

	private String sessionKey;

	private int unitId;

	private Date lastAccessTime;

	public SessionDetail(int userId, Date loginTime, String sessionKey, int unitId) {
		super();
		this.userId = userId;
		this.loginTime = loginTime;
		this.sessionKey = sessionKey;
		this.unitId = unitId;
		this.lastAccessTime = AppUtils.getCurrentTimestamp();
	}

	public int getUserId() {
		return userId;
	}

	public Date getLoginTime() {
		return loginTime;
	}

	public String getSessionKey() {
		return sessionKey;
	}

	public int getUnitId() {
		return unitId;
	}

	@Override
	public String toString() {
		return "SessionDetail [userId=" + userId + ", loginTime=" + loginTime + ", sessionKey=" + sessionKey
				+ ", unitId=" + unitId + "]";
	}

	public Date getLastAccessTime() {
		return lastAccessTime;
	}

	public void setLastAccessTime(Date lastAccessTime) {
		this.lastAccessTime = lastAccessTime;
	}

}
