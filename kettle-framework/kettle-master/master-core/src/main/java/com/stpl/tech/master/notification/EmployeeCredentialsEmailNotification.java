/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * Email notification for sending employee credentials to access Knock App
 */
public class EmployeeCredentialsEmailNotification extends EmailNotification {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeCredentialsEmailNotification.class);

    private final String employeeName;
    private final String employeeEmail;
    private final Integer employeeId;
    private final String password;
    private final EnvType environmentType;
    private final String fromEmail;

    public EmployeeCredentialsEmailNotification(String employeeEmail, String employeeName, 
                                               Integer employeeId, String password, 
                                               EnvType environmentType, String fromEmail) {
        this.employeeEmail = employeeEmail;
        this.employeeId = employeeId;
        this.password = password;
        this.environmentType = environmentType;
        this.fromEmail = fromEmail;
        this.employeeName = employeeName;
    }

    @Override
    public String[] getToEmails() {
        return getEnvironmentType().equals(EnvType.STAGE) || getEnvironmentType().equals(EnvType.LOCAL) ?
                new String[]{"<EMAIL>"} : new String[]{employeeEmail};
    }

    @Override
    public String getFromEmail() {
        return fromEmail;
    }

    @Override
    public String subject() {
        return "Kettle Credentials - Kettle Ops | Employee ID: " + employeeId;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            // Load the HTML template
            InputStream templateStream = getClass().getClassLoader()
                    .getResourceAsStream("templates/employee-credentials-email.html");
            
            if (templateStream == null) {
                throw new EmailGenerationException("Email template not found");
            }
            
            String template = IOUtils.toString(templateStream, StandardCharsets.UTF_8);
            
            // Replace placeholders with actual values
            String emailBody = template
                    .replace("{emp_id}", String.valueOf(employeeId))
                    .replace("{password}", password)
                    .replace("{emp_name}", employeeName);
            
            return emailBody;
            
        } catch (IOException e) {
            LOG.error("Error loading email template", e);
            throw new EmailGenerationException("Error loading email template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return environmentType;
    }
}
