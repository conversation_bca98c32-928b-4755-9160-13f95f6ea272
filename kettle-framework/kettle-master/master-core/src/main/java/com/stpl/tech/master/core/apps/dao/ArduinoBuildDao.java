package com.stpl.tech.master.core.apps.dao;

import com.stpl.tech.master.monk.configuration.model.ArduinoBuildData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 10-01-2019.
 */
@Repository
public interface ArduinoBuildDao extends MongoRepository<ArduinoBuildData, String> {

    public List<ArduinoBuildData> findAllByStatusEquals(String status);

    public ArduinoBuildData findFirstByStatusEquals(String status);

}
