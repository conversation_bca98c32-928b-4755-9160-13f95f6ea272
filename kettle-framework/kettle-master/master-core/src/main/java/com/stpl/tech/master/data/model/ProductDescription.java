package com.stpl.tech.master.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "PRODUCT_DESCRIPTION_DATA")
public class ProductDescription implements java.io.Serializable {

    private Integer productDescriptionDataID;
    private Integer productId;
    private String description;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PRODUCT_DESCRIPTION_DATA_ID", unique = true, nullable = false)
    public Integer getproductDescriptionDataID() {
        return productDescriptionDataID;
    }

    public void setproductDescriptionDataID(Integer productDescriptionDataID) {
        this.productDescriptionDataID = productDescriptionDataID;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "DESCRIPTION", columnDefinition = "TEXT", nullable = false)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
