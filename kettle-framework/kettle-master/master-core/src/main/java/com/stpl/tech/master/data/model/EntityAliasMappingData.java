/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "ENTITY_ALIAS_MAPPING")
public class EntityAliasMappingData implements java.io.Serializable {

    private static final long serialVersionUID = -9133952679432183323L;
    private Integer entityAliasMappingId;
    private Integer entityId;
    private String entityType;
    private Integer brandId;
    private String alias;
    private String imageUrl;
    private String description;

    public EntityAliasMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ENTITY_ALIAS_MAPPING_ID", unique = true, nullable = false)
    public Integer getEntityAliasMappingId() {
        return entityAliasMappingId;
    }

    public void setEntityAliasMappingId(Integer entityAliasMappingId) {
        this.entityAliasMappingId = entityAliasMappingId;
    }

    @Column(name = "ENTITY_ID", nullable = false)
    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    @Column(name = "ENTITY_TYPE", nullable = false)
    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    @Column(name = "BRAND_ID", nullable = false)
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "ALIAS", nullable = true)
    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    @Column(name = "IMAGE_URL", nullable = true)
    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Column(name = "DESCRIPTION", nullable = true)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}