/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import com.stpl.tech.util.AppConstants;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * UnitDetail generated by hbm2java
 */
@Entity
@Table(name = "OFFER_DETAIL_DATA")
public class OfferDetailData implements java.io.Serializable {

	private static final long serialVersionUID = -6850311105562111784L;

	private Integer offerDetailId;
	// OfferCategory = BILL, ITEM, LOYALITY, BEHAVIOUR, CHEAPER-ONE, BOGO
	private String offerCategory;
	// OfferType = PERCENTAGE_BILL_STRATEGY, FLAT_BILL_STRATEGY etc.
	private String offerType;
	private String offerText;
	private String offerDescription;
	private Date startDate;
	private Date endDate;
	private Date launchStartDate;
	private Date launchEndDate;
	private String launchStrategy;
	private String launchMessage;
	private int minValue;
	private String includeTaxes;
	private String offerStatus;
	private String validateCustomer;
	private Integer minQuantity;
	private int minLoyalty;
	// minItemCount is a check for Minimum Number of items present in order.
	private int minItemCount;
	private int value;
	// offerScope = scope of offer i.e MASS, INTERNAL, CUSTOMER, CORPORATE
	private String offerScope;
	// offerScope = scope of offer i.e MASS, INTERNAL, CUSTOMER, CORPORATE
	private String emailDomain;
	private String removeLoyaltyReward;
	private Integer priority;
	private Integer freeItemProductId;
	private String freeItemDimension;
	private Integer freeItemQuantity;
	private BigDecimal freeItemOfferValue;
	private String freeItemOfferType;
	private OfferAccountCategory accountsCategory;

	// List of marketing partners
	private List<OfferPartner> partners = new ArrayList<OfferPartner>(0);
	private List<OfferMetadata> metaDataMappings = new ArrayList<OfferMetadata>(0);
    private String prepaid = AppConstants.NO;
    private BigDecimal prepaidAmount;
    private BigDecimal maxBillValue;
    private String otpRequired;
	private BigDecimal maxDiscountAmount;

	// validation mappings of any kind of data regarding order manipulations
	private List<OfferDetailMappingData> mappings;
	private String frequencyApplicable;
	private String frequencyStrategy;
	private Integer frequencyCount;
	private Integer dailyFrequencyCount;
	private Integer applicableHour;
	private String autoApplicableforUnit;
	private String termsAndConditions;
	private Integer maxQuantity;

	private Integer loyaltyBurnPoints;

	private String signupOfferApplicable;

	private Integer brandId;

    public OfferDetailData() {
	}

	public OfferDetailData(String offerCategory, String offerType, String offerText, String offerDescription,
			Date startDate, Date endDate, int minValue, String includeTaxes, String offerStatus,
			String validateCustomer, Integer minQuantity, List<OfferPartner> partners, Integer priority, Integer brandId) {
		super();
		this.offerCategory = offerCategory;
		this.offerType = offerType;
		this.offerText = offerText;
		this.offerDescription = offerDescription;
		this.startDate = startDate;
		this.endDate = endDate;
		this.minValue = minValue;
		this.includeTaxes = includeTaxes;
		this.offerStatus = offerStatus;
		this.validateCustomer = validateCustomer;
		this.minQuantity = minQuantity;
		this.partners = partners;
		this.priority = priority;
		this.brandId = brandId;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "OFFER_DETAIL_ID", unique = true, nullable = false)
	public Integer getOfferDetailId() {
		return this.offerDetailId;
	}

	public void setOfferDetailId(Integer unitId) {
		this.offerDetailId = unitId;
	}

	@Column(name = "OFFER_CATEGORY", nullable = false, length = 30)
	public String getOfferCategory() {
		return this.offerCategory;
	}

	public void setOfferCategory(String unitName) {
		this.offerCategory = unitName;
	}

	@Column(name = "OFFER_TYPE", nullable = false, length = 30)
	public String getOfferType() {
		return this.offerType;
	}

	public void setOfferType(String offerType) {
		this.offerType = offerType;
	}

	@Column(name = "OFFER_TEXT", nullable = false, length = 500)
	public String getOfferText() {
		return this.offerText;
	}

	public void setOfferText(String unitEmail) {
		this.offerText = unitEmail;
	}

	@Column(name = "OFFER_DESCRIPTION", nullable = false, length = 1000)
	public String getOfferDescription() {
		return this.offerDescription;
	}

	public void setOfferDescription(String unitCategory) {
		this.offerDescription = unitCategory;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "START_DATE", nullable = false, length = 19)
	public Date getStartDate() {
		return this.startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "END_DATE", nullable = false, length = 19)
	public Date getEndDate() {
		return endDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "OFFER_LAUNCH_START_DATE", nullable = true, length = 10)
	public Date getLaunchStartDate() {
		return launchStartDate;
	}

	public void setLaunchStartDate(Date launchStartDate) {
		this.launchStartDate = launchStartDate;
	}
	@Temporal(TemporalType.DATE)
	@Column(name = "OFFER_LAUNCH_END_DATE", nullable = true, length = 10)
	public Date getLaunchEndDate() {
		return launchEndDate;
	}

	public void setLaunchEndDate(Date launchEndDate) {
		this.launchEndDate = launchEndDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@Column(name = "OFFER_STATUS", nullable = false, length = 15)
	public String getOfferStatus() {
		return this.offerStatus;
	}

	public void setOfferStatus(String unitStatus) {
		this.offerStatus = unitStatus;
	}

	@Column(name = "MIN_VALUE", nullable = false)
	public int getMinValue() {
		return minValue;
	}

	public void setMinValue(int billLimit) {
		this.minValue = billLimit;
	}

	@Column(name = "VALIDATE_CUSTOMER", nullable = false)
	public String getValidateCustomer() {
		return validateCustomer;
	}

	public void setValidateCustomer(String validateCustomer) {
		this.validateCustomer = validateCustomer;
	}

	@Column(name = "INCLUDE_TAXES", nullable = false)
	public String getIncludeTaxes() {
		return includeTaxes;
	}

	public void setIncludeTaxes(String includeTaxes) {
		this.includeTaxes = includeTaxes;
	}

	@Column(name = "PRIORITY", nullable = false)
	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	@Column(name = "QUANTITY_LIMIT", nullable = false)
	public Integer getMinQuantity() {
		return minQuantity;
	}

	public void setMinQuantity(Integer minQuantity) {
		this.minQuantity = minQuantity;
	}

	@Column(name = "LOYALTY_LIMIT", nullable = false)
	public int getMinLoyalty() {
		return minLoyalty;
	}

	public void setMinLoyalty(int minLoyalty) {
		this.minLoyalty = minLoyalty;
	}

	@Column(name = "OFFER_VALUE", nullable = false)
	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "offer")
	public List<OfferPartner> getPartners() {
		return partners;
	}

	public void setPartners(List<OfferPartner> partners) {
		this.partners = partners;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "offerId")
	public List<OfferMetadata> getMetaDataMappings() {
		return metaDataMappings;
	}

	public void setMetaDataMappings(List<OfferMetadata> metaDataMappings) {
		this.metaDataMappings = metaDataMappings;
	}

	@Column(name = "OFFER_SCOPE", nullable = false)
	public String getOfferScope() {
		return offerScope;
	}

	public void setOfferScope(String offerScope) {
		this.offerScope = offerScope;
	}

	@Column(name = "MIN_ITEM_COUNT", nullable = false)
	public int getMinItemCount() {
		return minItemCount;
	}

	public void setMinItemCount(int minItemCount) {
		this.minItemCount = minItemCount;
	}

	@Column(name = "EMAIL_DOMAIN", nullable = true)
	public String getEmailDomain() {
		return emailDomain;
	}

	public void setEmailDomain(String emailDomain) {
		this.emailDomain = emailDomain;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ACCOUNTS_CATEGORY", nullable = true)
	public OfferAccountCategory getAccountsCategory() {
		return accountsCategory;
	}

	public void setAccountsCategory(OfferAccountCategory accountsCategory) {
		this.accountsCategory = accountsCategory;
	}

	@Column(name = "FREE_ITEM_PRODUCT_ID", nullable = true)
	public Integer getFreeItemProductId() {
		return freeItemProductId;
	}

	public void setFreeItemProductId(Integer freeItemProductId) {
		this.freeItemProductId = freeItemProductId;
	}

	@Column(name = "FREE_ITEM_QUANTITY", nullable = true)
	public Integer getFreeItemQuantity() {
		return freeItemQuantity;
	}

	public void setFreeItemQuantity(Integer freeItemQuantity) {
		this.freeItemQuantity = freeItemQuantity;
	}

	@Column(name = "FREE_ITEM_OFFER_VALUE", nullable = true, precision = 10)
	public BigDecimal getFreeItemOfferValue() {
		return freeItemOfferValue;
	}

	public void setFreeItemOfferValue(BigDecimal freeItemOfferValue) {
		this.freeItemOfferValue = freeItemOfferValue;
	}

	@Column(name = "FREE_ITEM_OFFER_TYPE", nullable = true)
	public String getFreeItemOfferType() {
		return freeItemOfferType;
	}

	public void setFreeItemOfferType(String freeItemOfferType) {
		this.freeItemOfferType = freeItemOfferType;
	}

	@Column(name = "FREE_ITEM_DIMENSION", nullable = true)
	public String getFreeItemDimension() {
		return freeItemDimension;
	}

	public void setFreeItemDimension(String freeItemDimension) {
		this.freeItemDimension = freeItemDimension;
	}

	@Column(name = "OFFER_LAUNCH_STRATEGY", nullable = true)
	public String getLaunchStrategy() {
		return launchStrategy;
	}

	public void setLaunchStrategy(String launchStrategy) {
		this.launchStrategy = launchStrategy;
	}
	
	@Column(name = "OFFER_LAUNCH_MESSAGE", nullable = true)
	public String getLaunchMessage() {
		return launchMessage;
	}

	public void setLaunchMessage(String launchMessage) {
		this.launchMessage = launchMessage;
	}

	@Column(name = "REMOVE_LOYALTY_REWARD", nullable = true)
	public String getRemoveLoyaltyReward() {
		return removeLoyaltyReward;
	}

	public void setRemoveLoyaltyReward(String awardLoyalty) {
		this.removeLoyaltyReward = awardLoyalty;
	}

    @Column(name = "PREPAID")
    public String getPrepaid() {
        return prepaid;
    }

    public void setPrepaid(String prepaid) {
        this.prepaid = prepaid;
    }

    @Column(name = "PREPAID_AMOUNT")
    public BigDecimal getPrepaidAmount() {
        return prepaidAmount;
    }

    public void setPrepaidAmount(BigDecimal prepaidAmount) {
        this.prepaidAmount = prepaidAmount;
    }

	@Column(name = "MAX_BILL_VALUE")
	public BigDecimal getMaxBillValue() {
		return maxBillValue;
	}

	public void setMaxBillValue(BigDecimal maxBillValue) {
		this.maxBillValue = maxBillValue;
	}

	@Column(name = "OTP_REQUIRED")
	public String getOtpRequired() {
		return otpRequired;
	}

	public void setOtpRequired(String otpRequired) {
		this.otpRequired = otpRequired;
	}

	@Column(name = "MAX_DISCOUNT_AMOUNT")
	public BigDecimal getMaxDiscountAmount() {
		return maxDiscountAmount;
	}

	public void setMaxDiscountAmount(BigDecimal maxDiscountAmount) {
		this.maxDiscountAmount = maxDiscountAmount;
	}

	@OneToMany(mappedBy = "offerDetail")
	public List<OfferDetailMappingData> getMappings() {
		return mappings;
	}

	public void setMappings(List<OfferDetailMappingData> mappings) {
		this.mappings = mappings;
	}

	@Column(name = "FREQUENCY_APPLICABLE")
	public String getFrequencyApplicable() {
		return frequencyApplicable;
	}

	
	public void setFrequencyApplicable(String frequencyApplicable) {
		this.frequencyApplicable = frequencyApplicable;
	}
	
	@Column(name = "FREQUENCY_STRATEGY")
	public String getFrequencyStrategy() {
		return frequencyStrategy;
	}

	public void setFrequencyStrategy(String frequencyStrategy) {
		this.frequencyStrategy = frequencyStrategy;
	}

	@Column(name = "FREQUENCY_COUNT")
	public Integer getFrequencyCount() {
		return frequencyCount;
	}

	public void setFrequencyCount(Integer frequencyCount) {
		this.frequencyCount = frequencyCount;
	}

	@Column(name = "DAILY_FREQUENCY_COUNT")
	public Integer getDailyFrequencyCount() {
		return dailyFrequencyCount;
	}

	public void setDailyFrequencyCount(Integer dailyFrequencyCount) {
		this.dailyFrequencyCount = dailyFrequencyCount;
	}

	@Column(name = "APPLICABLE_HOUR")
	public Integer getApplicableHour() {
		return applicableHour;
	}

	public void setApplicableHour(Integer applicableHour) {
		this.applicableHour = applicableHour;
	}

	@Column(name = "UNIT_AUTO_APPLICABLE")
	public String getAutoApplicableforUnit() {
		return autoApplicableforUnit;
	}

	public void setAutoApplicableforUnit(String autoApplicableforUnit) {
		this.autoApplicableforUnit = autoApplicableforUnit;
	}

	@Column(name = "TERMS_AND_CONDITIONS")
	public String getTermsAndConditions() {
		return termsAndConditions;
	}

	public void setTermsAndConditions(String termsAndConditions) {
		this.termsAndConditions = termsAndConditions;
	}

	@Column(name = "MAX_QUANTITY")
	public Integer getMaxQuantity() {
		return maxQuantity;
	}

	public void setMaxQuantity(Integer maxQuantity) {
		this.maxQuantity = maxQuantity;
	}

	@Column(name = "LOYALTY_BURN_POINTS")
	public Integer getLoyaltyBurnPoints() {
		return loyaltyBurnPoints;
	}

	public void setLoyaltyBurnPoints(Integer loyaltyBurnPoints) {
		this.loyaltyBurnPoints = loyaltyBurnPoints;
	}

	@Column(name = "SIGNUP_OFFER_APPLICABLE")
	public String getSignupOfferApplicable() {
		return signupOfferApplicable;
	}

	public void setSignupOfferApplicable(String signupOfferApplicable) {
		this.signupOfferApplicable = signupOfferApplicable;
	}

	@Column(name = "BRAND_ID")
	public Integer getBrandId() { return this.brandId; }

	public void setBrandId(Integer brandId) { this.brandId = brandId; }

}
