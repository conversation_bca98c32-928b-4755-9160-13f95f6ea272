package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.MappingStatus;
import com.stpl.tech.master.core.cache.EmployeeCache;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.service.RiderMappingService;
import com.stpl.tech.master.data.dao.RiderMappingDao;
import com.stpl.tech.master.data.model.RiderUnitMapping;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class RiderMappingServiceImpl implements RiderMappingService {

	@Autowired
	private RiderMappingDao dao;

	@Autowired
	private EmployeeCache cache;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addMapping(int employeeId, int unitId) throws MasterException {
		EmployeeBasicDetail employeeBasicDetail = cache.getEmployee(employeeId);
		if (employeeBasicDetail.getSdpContact() != null) {
			return dao.addMapping(employeeId, unitId);
		} else {
			cache.removeEmployee(employeeId);
			throw new MasterException("CONTACT_NOT_AVAILABLE", "");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean deleteMapping(int employeeId, int unitId) {
		return dao.deleteMapping(employeeId, unitId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean disableMapping(int employeeId, int unitId) {
		return dao.disableMapping(employeeId, unitId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean enableMapping(int employeeId, int unitId) throws MasterException {
		EmployeeBasicDetail employeeBasicDetail = cache.getEmployee(employeeId);
		if (employeeBasicDetail.getSdpContact() != null) {
			return dao.enableMapping(employeeId, unitId);
		} else {
			cache.removeEmployee(employeeId);
			throw new MasterException("CONTACT_NOT_AVAILABLE", "");
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<EmployeeBasicDetail> getActiveMapping(int unitId) {
		return getMappings(unitId, Arrays.asList(MappingStatus.ENABLED.name()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<EmployeeBasicDetail> getAllMapping(int unitId) {
		return getMappings(unitId, Arrays.asList(MappingStatus.ENABLED.name(), MappingStatus.DISABLED.name()));
	}

	private List<EmployeeBasicDetail> getMappings(int unitId, List<String> mappingStatus) {
		List<EmployeeBasicDetail> details = new ArrayList<>();
		List<RiderUnitMapping> mappings = dao.getMapping(unitId, mappingStatus);
		if (mappings != null && mappings.size() > 0) {
			for (RiderUnitMapping mapping : mappings) {
				EmployeeBasicDetail detail = cache.getEmployee(mapping.getEmployeeId());
				if (detail != null && detail.getStatus().equals(EmploymentStatus.ACTIVE)) {
					detail.setMappingStatus(mapping.getMappingStatus());
					details.add(detail);
				}
			}
		}
		return details;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.core.service.RiderMappingService#
	 * getUnitsForEmployeeId(int)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getUnitsForEmployeeId(int employeeId) {
		List<RiderUnitMapping> mappings = dao.getMapping(employeeId);
		List<Integer> list = new ArrayList<>();
		if (mappings != null && mappings.size() > 0) {
			mappings.stream().forEach((map) -> {
				list.add(map.getUnitId());
			});
		}
		return list;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.master.core.service.RiderMappingService#removeFromCache(java.lang.Integer, int)
	 */
	@Override
	public void removeFromCache(int employeeId) {
		cache.removeEmployee(employeeId);
	}

}
