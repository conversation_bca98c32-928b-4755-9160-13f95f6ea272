package com.stpl.tech.master.core.external.inventory.service;

import javax.jms.JMSException;

import com.amazonaws.regions.Regions;

import java.io.Serializable;

public interface SQSNotificationService {

    public <T extends Serializable> void publishToSQS(String env, T response, String queueNameSuffix) throws JMSException;

    public <T extends Serializable> void publishToSQS(String env, T response, String queueNameSuffix,  Regions region) throws JMSException;

    public <T extends Serializable> void publishToSQSFifo(String env, T event, String queueNameSuffix,  Regions region) throws JMSException;

    public <T extends Serializable> void publishToSQSFifo(String env, T event, String queueNameSuffix,  Regions region,String messageGrpId) throws JMSException;
}