/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.BrandAttributes;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadata;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface BrandManagementService {

    List<Brand> getBrands();

    List<UnitPartnerBrandMappingData> getUnitPartnerBrandMappingList();

    UnitPartnerBrandMappingData getUnitPartnerMapping(String restaurantId, int partnerId);

    UnitPartnerBrandMappingData addUnitPartnerMapping(UnitPartnerBrandMappingData unitPartnerBrandMappingData,String editLiveDate) throws DataUpdationException;

    List<UnitPartnerBrandMappingData> addAllUnitPartnerMapping(List<UnitPartnerBrandMappingData> unitPartnerBrandMappingDatas) throws DataUpdationException;

    List<UnitPartnerBrandMappingData> getAllUnitPartnerBrandMappings();

    UnitPartnerBrandMappingData getUnitPartnerBrandMapping(UnitPartnerBrandKey key);

    List<UnitPartnerBrandMappingData> getAllUnitPartnerBrandMapping(Integer brandId,Integer partnerId);

    List<UnitPartnerBrandMappingMetadata> getAllUnitPartnerBrandMappingMetadata();

    List<UnitPartnerBrandMappingMetadata> getUnitPartnerBrandMappingMetadataByKey(UnitPartnerBrandMappingMetadataType key);

    UnitPartnerBrandMappingData activateUnitPartnerBrandMapping(Integer mappingId, boolean activate) throws DataUpdationException;

    void toggleUnitPartnerBrandMapping(List<Integer> mappingIds, Boolean status) throws DataUpdationException;

    List<UnitPartnerBrandMappingMetadata> addUnitPartnerBrandMappingMetadataList(List<UnitPartnerBrandMappingMetadata> data) throws DataUpdationException;

    UnitPartnerBrandMappingMetadata addUnitPartnerBrandMappingMetadata(UnitPartnerBrandMappingMetadata data) throws DataUpdationException;

    UnitPartnerBrandMappingMetadata changeStatusUnitPartnerBrandMappingMetadata(Integer metadataId, boolean activate) throws DataUpdationException;

    Map<UnitPartnerBrandMappingMetadataType, String> unitPartnerBrandMappingMetadataByUnitPartnerBrand(UnitPartnerBrandKey key);

    boolean uploadMonkMetaData(MimeType mimeType,Integer brandId, String attributeKey, String attributeValue, MultipartFile file, boolean isImage);

    public Collection<BrandAttributes> getAllBrandAttributes();
    public boolean updateBrandAttributes(List<BrandAttributes> brandAttributes);

    List<IdCodeName> getBrandsInShort(Integer brandId);

}
