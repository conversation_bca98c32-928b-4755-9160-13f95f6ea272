package com.stpl.tech.master.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "UNIT_CONTACT_DETAIL")
@Getter
@Setter
public class UnitContactDetailsData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "UNIT_NAME")
    private String unitName;

    @Column(name = "FIRST_CONTACT_NAME")
    private String firstContactName;

    @Column(name = "FIRST_CONTACT_NUMBER")
    private String firstContactNumber;

    @Column(name = "SECOND_CONTACT_NAME")
    private String secondContactName;

    @Column(name = "SECOND_CONTACT_NUMBER")
    private String secondContactNumber;

    @Column(name = "THIRD_CONTACT_NAME")
    private String thirdContactName;

    @Column(name = "THIRD_CONTACT_NUMBER")
    private String thirdContactNumber;

    @Column(name = "STATUS")
    private String status;
}
