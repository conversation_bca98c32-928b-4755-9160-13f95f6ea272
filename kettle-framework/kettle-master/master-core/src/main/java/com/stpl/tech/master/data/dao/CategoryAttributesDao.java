package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.CategoryAttributes;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface CategoryAttributesDao extends JpaRepository<CategoryAttributes,Integer> {

    List<CategoryAttributes> findByRlIdIn(Set<Integer> rlId);
}
