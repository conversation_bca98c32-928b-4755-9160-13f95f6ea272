package com.stpl.tech.master.data.dao.specification;

import com.stpl.tech.master.data.model.LCDMenuImage;
import com.stpl.tech.master.data.model.LCDMenuImageParameter;
import com.stpl.tech.util.AppConstants;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Subquery;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;


public class LCDMenuImageSpecification {
    /**
     * Legacy filter method using individual parameters
     */
    public static Specification<LCDMenuImage> filterBy(
            String version, String region, String priceProfile,
            String orientation, String slot, String lcdType) {

        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (version != null  && !version.isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("version"), version));
            }
            if (region != null && !region.isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("region"), region));
            }
            if (priceProfile != null && !priceProfile.isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("priceProfile"), priceProfile));
            }
            if (orientation != null && !orientation.isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("orientation"), orientation));
            }
            if (slot != null && !slot.isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("slot"), slot));
            }
            if (lcdType != null && !lcdType.isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("lcdType"), lcdType));
            }

            predicates.add(criteriaBuilder.equal(root.get("status"), AppConstants.ACTIVE));

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * New filter method using a map of parameters
     */
    public static Specification<LCDMenuImage> filterByParams(Map<String, String> params) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Add legacy field filters for backward compatibility
            addLegacyFilters(root, criteriaBuilder, predicates, params);

            // Add dynamic parameter filters
            if (params != null && !params.isEmpty()) {
                // Prevent duplicate results when using joins
                query.distinct(true);

                // Create a join for parameters if needed
                Set<String> legacyParams = Set.of("version", "region", "priceProfile", "orientation", "slot", "lcdType");
                boolean hasCustomParams = params.keySet().stream()
                        .anyMatch(key -> !legacyParams.contains(key));

                if (hasCustomParams) {
                    // For each custom parameter, create a separate subquery
                    params.forEach((key, value) -> {
                        if (!legacyParams.contains(key) && value != null && !value.isEmpty()) {
                            // Create a subquery for each parameter
                            Subquery<Long> subquery = query.subquery(Long.class);
                            Root<LCDMenuImageParameter> subRoot = subquery.from(LCDMenuImageParameter.class);
                            subquery.select(subRoot.get("lcdMenuImage").get("id"));

                            // Add conditions for this parameter
                            subquery.where(
                                criteriaBuilder.and(
                                    criteriaBuilder.equal(subRoot.get("stepName"), key),
                                    criteriaBuilder.equal(subRoot.get("stepValue"), value),
                                    criteriaBuilder.equal(subRoot.get("lcdMenuImage"), root)
                                )
                            );

                            // Add exists predicate to main query
                            predicates.add(criteriaBuilder.exists(subquery));
                        }
                    });
                }
            }

            // Always filter by active status
            predicates.add(criteriaBuilder.equal(root.get("status"), AppConstants.ACTIVE));

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * Helper method to add legacy field filters
     */
    private static void addLegacyFilters(Root<LCDMenuImage> root, CriteriaBuilder criteriaBuilder,
                                        List<Predicate> predicates, Map<String, String> params) {
        if (params == null) {
            return;
        }

        String version = params.get("version");
        if (version != null && !version.isEmpty()) {
            predicates.add(criteriaBuilder.equal(root.get("version"), version));
        }

        String region = params.get("region");
        if (region != null && !region.isEmpty()) {
            predicates.add(criteriaBuilder.equal(root.get("region"), region));
        }

        String priceProfile = params.get("priceProfile");
        if (priceProfile != null && !priceProfile.isEmpty()) {
            predicates.add(criteriaBuilder.equal(root.get("priceProfile"), priceProfile));
        }

        String orientation = params.get("orientation");
        if (orientation != null && !orientation.isEmpty()) {
            predicates.add(criteriaBuilder.equal(root.get("orientation"), orientation));
        }

        String slot = params.get("slot");
        if (slot != null && !slot.isEmpty()) {
            predicates.add(criteriaBuilder.equal(root.get("slot"), slot));
        }

        String lcdType = params.get("lcdType");
        if (lcdType != null && !lcdType.isEmpty()) {
            predicates.add(criteriaBuilder.equal(root.get("lcdType"), lcdType));
        }
    }
}
