package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.VersionCompatibilityData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VersionCompatibilityDao extends JpaRepository<VersionCompatibilityData,Integer> {
    List<VersionCompatibilityData> findByApplicationNameAndApplicationVersionOrderByUpdatedAtDesc(String applicationName,String applicationVersion);
    List<VersionCompatibilityData> findByPosVersion(String posVersion);
    List<VersionCompatibilityData> findByApplicationNameAndPosVersionAndStatus(String applicationName,String posVersion,String status);
    VersionCompatibilityData findByApplicationNameAndApplicationVersionAndStatus(String applicationName,String applicationVersion,String status);

}
