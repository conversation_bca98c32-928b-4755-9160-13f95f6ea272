/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 3 Aug, 2015 5:36:58 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * XMLReportDefinitionData generated by <PERSON><PERSON><PERSON>h
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "XML_REPORT_DEFINITION",
		uniqueConstraints = {@UniqueConstraint(columnNames = { "EXECUTION_ENVIRONMENT","REPORT_TYPE","REPORT_NAME"})})
public class XMLReportDefinitionData implements java.io.Serializable {

	private Integer reportId;
	private String reportName;
	private String xmlFilePath;
	private String reportStatus;
	private Integer version;
	private String reportType;
	private String executionEnvironment;
	private Integer departmentId;
	private String departmentName;
	private Date lastUpdated;

	public XMLReportDefinitionData() {

	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "REPORT_ID", unique = true, nullable = false)
	public Integer getReportId() {
		return reportId;
	}

	public void setReportId(Integer reportId) {
		this.reportId = reportId;
	}

	@Column(name = "REPORT_NAME", nullable = true, length = 250)
	public String getReportName() {
		return reportName;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	@Column(name = "XML_FILE_PATH", nullable = true, length = 1000)
	public String getXmlFilePath() {
		return xmlFilePath;
	}

	public void setXmlFilePath(String xmlFilePath) {
		this.xmlFilePath = xmlFilePath;
	}

	@Column(name = "REPORT_STATUS", nullable = true, length = 100)
	public String getReportStatus() {
		return reportStatus;
	}

	public void setReportStatus(String reportStatus) {
		this.reportStatus = reportStatus;
	}

	@Column(name = "VERSION", nullable = true)
	public Integer getVersion() {
		return version;
	}

	public void setVersion(Integer version) {
		this.version = version;
	}

	@Column(name = "REPORT_TYPE", nullable = true, length = 45)
	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	@Column(name = "EXECUTION_ENVIRONMENT", nullable = true, length = 45)
	public String getExecutionEnvironment() {
		return executionEnvironment;
	}

	public void setExecutionEnvironment(String executionEnvironment) {
		this.executionEnvironment = executionEnvironment;
	}

	@Column(name = "DEPARTMENT_ID", nullable = true)
	public Integer getDepartmentId() {
		return departmentId;
	}

	public void setDepartmentId(Integer departmentId) {
		this.departmentId = departmentId;
	}

	@Column(name = "DEPARTMENT_NAME", nullable = true, length = 100)
	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATED", nullable = true, length = 19)
	public Date getLastUpdated() {
		return lastUpdated;
	}

	public void setLastUpdated(Date lastUpdated) {
		this.lastUpdated = lastUpdated;
	}
}
