/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.KioskManagementService;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.UnitManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.KioskManagementDao;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskLocationDetails;
import com.stpl.tech.master.domain.model.KioskMachine;
import com.stpl.tech.master.domain.model.KioskOfficeDetails;
import com.stpl.tech.master.domain.model.SwitchStatus;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class KioskManagementServiceImpl implements KioskManagementService {

    @Autowired
    KioskManagementDao dao;

    @Autowired
    MasterDataCache masterCache;

    @Autowired
    MasterDataCacheService masterDataCacheService;

    @Autowired
    UnitManagementService unitService;


    private void updateOfficeInCache(KioskOfficeDetails officeDetails, Integer companyId) {
        KioskCompanyDetails company = masterCache.getKioskCompanies().get(companyId);
        List<KioskOfficeDetails> modifiedList = company.getOfficeList().stream()
                .filter(kioskOfficeDetails -> !kioskOfficeDetails.getOfficeId().equals(officeDetails.getOfficeId()))
                .collect(Collectors.toList());
        modifiedList.add(officeDetails);
        company.getOfficeList().clear();
        company.getOfficeList().addAll(modifiedList);
        masterCache.getKioskCompanies().put(company.getCompanyId(), company);

    }

    private void updateLocationInCache(KioskLocationDetails location) {
        if (location != null && location.getOfficeDetails() != null && location.getOfficeDetails().getCompanyDetails() != null) {
            int companyId = location.getOfficeDetails().getCompanyDetails().getCompanyId();
            int officeId = location.getOfficeDetails().getOfficeId();
            KioskCompanyDetails company = masterCache.getKioskCompanies().get(companyId);
            Optional<KioskOfficeDetails> office = company.getOfficeList().stream()
                    .filter(kioskOfficeDetails -> kioskOfficeDetails.getOfficeId().equals(officeId))
                    .findAny();

            office.ifPresent(kioskOfficeDetails -> {
                List<KioskLocationDetails> modifiedLocations = kioskOfficeDetails.getLocationList().stream()
                        .filter(kioskLocationDetails -> !kioskLocationDetails.getLocationId().equals(location.getLocationId()))
                        .collect(Collectors.toList());
                modifiedLocations.add(location);
                kioskOfficeDetails.getLocationList().clear();
                kioskOfficeDetails.getLocationList().addAll(modifiedLocations);
                updateOfficeInCache(kioskOfficeDetails, companyId);
            });
        }
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public KioskLocationDetails saveLocationDetails(KioskLocationDetails kioskLocationDetails) throws DataNotFoundException {
        KioskLocationDetails location = dao.saveKioskLocationDetails(kioskLocationDetails, null);
        masterDataCacheService.refreshKioskCompanies();
        return location;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public KioskOfficeDetails saveOfficeDetails(KioskOfficeDetails kioskOfficeDetails) throws DataNotFoundException {
        KioskOfficeDetails officeDetails = dao.saveOfficeDetails(kioskOfficeDetails);
        masterDataCacheService.refreshKioskCompanies();
        return officeDetails;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public KioskCompanyDetails saveCompanyDetails(KioskCompanyDetails kioskCompanyDetails) {
        KioskCompanyDetails companyDetails = dao.saveCompanyDetails(kioskCompanyDetails);
        masterCache.getKioskCompanies().put(companyDetails.getCompanyId(), companyDetails);
        return companyDetails;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public KioskCompanyDetails updateCompanyDetails(KioskCompanyDetails kioskCompanyDetails) {
        KioskCompanyDetails companyDetails = dao.updateKioskCompanyDetails(kioskCompanyDetails);
        masterCache.getKioskCompanies().put(companyDetails.getCompanyId(), companyDetails);
        return companyDetails;
    }


    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public KioskOfficeDetails updateOfficeDetails(KioskOfficeDetails kioskOfficeDetails) throws DataNotFoundException {
        KioskOfficeDetails officeDetails = dao.updateKioskOfficeDetails(kioskOfficeDetails);
        updateOfficeInCache(officeDetails, officeDetails.getCompanyDetails().getCompanyId());
        return officeDetails;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public KioskLocationDetails updateLocationDetails(KioskLocationDetails kioskLocationDetails) throws DataNotFoundException {
        KioskLocationDetails locationDetails = dao.updateKioskLocationDetails(kioskLocationDetails, kioskLocationDetails.getAssignedUnit().getId());
        if (locationDetails != null) {
            updateLocationInCache(locationDetails);
        }
        return locationDetails;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean changeCompanyStatus(Integer companyId, boolean isActivate) {
        SwitchStatus status = isActivate ? SwitchStatus.ACTIVE : SwitchStatus.IN_ACTIVE;
        boolean flag = false;
        KioskCompanyDetails company = dao.updateCompanyStatus(companyId, status);
        if (company != null) {
            masterCache.getKioskCompanies().put(companyId, company);
            flag = true;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean changeOfficeStatus(Integer officeId, Integer companyId, boolean isActivate) {
        boolean flag = false;
        SwitchStatus status = isActivate ? SwitchStatus.ACTIVE : SwitchStatus.IN_ACTIVE;
        if(officeId!=null) {
            KioskOfficeDetails office = dao.getOfficeDetails(officeId);
            office.setOfficeStatus(status);
            office = dao.updateKioskOfficeDetails(office);
            updateOfficeInCache(office,office.getCompanyDetails().getCompanyId());
            flag = true;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean changeLocationStatus(Integer locationId,Integer officeId, Integer companyId, boolean isActivate) {
        boolean flag = false;
        SwitchStatus status = isActivate ? SwitchStatus.ACTIVE : SwitchStatus.IN_ACTIVE;
        if(locationId!=null){
            KioskLocationDetails locationDetails = dao.getLocationDetails(locationId);
            locationDetails.setLocationStatus(status);
            Integer unitId = locationDetails.getAssignedUnit()!=null
                    ? locationDetails.getAssignedUnit().getId() : null;
            locationDetails = dao.updateKioskLocationDetails(locationDetails, unitId);
            updateLocationInCache(locationDetails);
            flag = true;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public KioskLocationDetails assignUnit(Integer locationId, Integer unitId) {
        KioskLocationDetails locationDetails = dao.assignUnitToLocation(locationId, unitId);
        if (locationDetails != null) {
            updateLocationInCache(locationDetails);
        }
        return locationDetails;
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public KioskLocationDetails getLocationDetails(int locationId) {
        return dao.getLocationDetails(locationId);
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public KioskCompanyDetails getCompanyDetails(int companyId) {
        return dao.getCompanyDetails(companyId);
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public KioskOfficeDetails getOfficeDetails(int officeId) {
        return dao.getOfficeDetails(officeId);
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public KioskMachine getMachineDetailsByOffice(int machineId, Integer unitId) {
        IdCodeName unitData = null;
        if (unitId != null) {
            UnitBasicDetail unit = masterCache.getUnitBasicDetail(unitId);
            unitData = MasterDataConverter.convertToIdCodeName(unit);
        }
        return dao.getKioskMachineDetails(machineId, unitData);
    }

    @Override
    @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<KioskCompanyDetails> getCompanyDetailMap() {
        return new ArrayList<>(masterCache.getKioskCompanies().values());
    }


    /*public static void main(String[] args) {
        List<String> strings = new ArrayList<>();
        strings.add("a");
        strings.add("b");

        String tomodify =   strings.stream().filter(s -> s.equalsIgnoreCase("b")).findAny().get();
        List<String> strings2 = strings.stream().filter(s -> !s.equalsIgnoreCase("b")).collect(Collectors.toList());

        tomodify = tomodify.concat("aa");
        strings2.add(tomodify);
        strings.clear();
        strings.addAll(strings2);
        System.out.println(String.join(",", strings));

    }*/

}
