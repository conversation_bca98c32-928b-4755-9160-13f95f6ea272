/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 3 Aug, 2015 5:36:58 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * TaxProfileData generated by hbm2java
 */
@Entity
@Table(name = "TAX_PROFILE_DATA", uniqueConstraints = @UniqueConstraint(columnNames = { "TAX_CODE" }))
public class TaxProfileData implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2529263919013076026L;
	private Integer taxDataId;
	private String taxCode;
	private String taxName;
	private String taxDescription;
	private String taxInternalDescription;
	private String taxDataStatus;
	private String applicableOn;

	public TaxProfileData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TAX_DATA_ID", unique = true, nullable = false)
	public Integer getTaxDataId() {
		return this.taxDataId;
	}

	public void setTaxDataId(Integer taxDataId) {
		this.taxDataId = taxDataId;
	}

	@Column(name = "TAX_CODE", nullable = false, length = 20)
	public String getTaxCode() {
		return this.taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@Column(name = "TAX_NAME", nullable = false, length = 50)
	public String getTaxName() {
		return this.taxName;
	}

	public void setTaxName(String taxName) {
		this.taxName = taxName;
	}

	@Column(name = "TAX_DATA_STATUS", nullable = false, length = 15)
	public String getTaxDataStatus() {
		return taxDataStatus;
	}

	public void setTaxDataStatus(String taxDataStatus) {
		this.taxDataStatus = taxDataStatus;
	}

	@Column(name = "TAX_DESCRIPTION", nullable = false, length = 1000)
	public String getTaxDescription() {
		return taxDescription;
	}

	public void setTaxDescription(String taxDescription) {
		this.taxDescription = taxDescription;
	}

	@Column(name = "TAX_INTERNAL_DESCRIPTION", nullable = false, length = 1000)
	public String getTaxInternalDescription() {
		return taxInternalDescription;
	}

	public void setTaxInternalDescription(String taxInternalDescription) {
		this.taxInternalDescription = taxInternalDescription;
	}

	@Column(name = "APPLICABLE_ON", nullable = false, length = 15)
	public String getApplicableOn() {
		return applicableOn;
	}

	public void setApplicableOn(String applicableOn) {
		this.applicableOn = applicableOn;
	}

}
