package com.stpl.tech.master.util;

import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;

import java.util.ArrayList;
import java.util.Arrays;

public enum CustomerEventType {
    Birthday, Anniversary;

    public String getCouponPrefix() {
        switch (this) {
            case Birthday:
                return "BTH";
            case Anniversary:
                return "ANVY";
            default:
                return "";
        }
    }


}
