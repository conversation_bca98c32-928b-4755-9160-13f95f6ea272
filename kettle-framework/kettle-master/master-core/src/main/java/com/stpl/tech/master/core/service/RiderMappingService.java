package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;

import java.util.List;

public interface RiderMappingService {

	boolean addMapping(int employeeId, int unitId) throws MasterException;

	boolean deleteMapping(int employeeId, int unitId);

	boolean disableMapping(int employeeId, int unitId);

	boolean enableMapping(int employeeId, int unitId) throws MasterException;

	List<Integer> getUnitsForEmployeeId(int employeeId);

	List<EmployeeBasicDetail> getActiveMapping(int unitId);

	List<EmployeeBasicDetail> getAllMapping(int unitId);

	/**
	 * @param employeeId
	 */
	void removeFromCache(int employeeId);

}
