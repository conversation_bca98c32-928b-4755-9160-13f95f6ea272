package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.ProductNutritionDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ProductNutritionDetailDao extends JpaRepository<ProductNutritionDetail,Integer> {

    List<ProductNutritionDetail> findAllByProductIdInAndSourceType(Set<Integer> productIds,String sourceType);
}
