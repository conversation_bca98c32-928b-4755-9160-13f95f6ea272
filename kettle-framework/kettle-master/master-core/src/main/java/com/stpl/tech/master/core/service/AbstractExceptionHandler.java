/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.FileDownloadException;
import com.stpl.tech.master.core.exception.ManagedException;
import com.stpl.tech.master.core.exception.MasterError;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.exception.MeterDetailEntryException;
import com.stpl.tech.master.core.exception.SDPAllocationException;
import com.stpl.tech.master.core.service.model.ErrorInfo;

public class AbstractExceptionHandler {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractExceptionHandler.class);

	@ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
	@ExceptionHandler(SDPAllocationException.class)
	@ResponseBody
	public ErrorInfo handleSdpAllocationException(Exception ex) {
		LOG.error(HttpStatus.NOT_ACCEPTABLE.name(), ex);
		return new ErrorInfo(HttpStatus.NOT_ACCEPTABLE.name(), ex);
	}

	@ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
	@ExceptionHandler(MeterDetailEntryException.class)
	@ResponseBody
	public ErrorInfo meterDetailEntryException(Exception ex) {
		LOG.error(HttpStatus.NOT_ACCEPTABLE.name(), ex.getMessage());
		return new ErrorInfo(HttpStatus.NOT_ACCEPTABLE.name(), ex);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(DataUpdationException.class)
	@ResponseBody
	public ErrorInfo handleDataUpdationException(Exception ex) {
		LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(DataNotFoundException.class)
	@ResponseBody
	public ErrorInfo handleDataNotFoundException(Exception ex) {
		LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(FileDownloadException.class)
	@ResponseBody
	public ErrorInfo handleFileDownloadException(Exception ex) {
		LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	@ResponseStatus(HttpStatus.UNAUTHORIZED)
	@ExceptionHandler(AuthenticationFailureException.class)
	@ResponseBody
	public ErrorInfo handleAuthenticationFailureException(Exception ex) {
		LOG.info(HttpStatus.UNAUTHORIZED.name(), ex.getMessage());
		return new ErrorInfo(HttpStatus.UNAUTHORIZED.name(), ex);
	}

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(Exception.class)
	@ResponseBody
	public ErrorInfo handleAllException(Exception ex) {
		LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	@ResponseStatus(HttpStatus.OK)
	@ExceptionHandler(ManagedException.class)
	@ResponseBody
	public ErrorInfo handleManagedException(ManagedException ex) {
		LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	@ResponseStatus(HttpStatus.OK)
	@ExceptionHandler(MasterException.class)
	@ResponseBody
	public MasterError handleMasterException(MasterException ex) {
		LOG.info(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex.getMessage());
		return ex.getCode();
	}

}
