/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.offer.service;

import com.stpl.tech.kettle.report.metadata.model.NameValue;
import com.stpl.tech.master.CampaignImageDetail;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.data.model.BannerMetadata;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CustomerWinbackOfferInfo;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.DeliveryOfferDetailData;
import com.stpl.tech.master.data.model.LaunchOfferData;
import com.stpl.tech.master.data.model.OfferDescriptionMetadata;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.SignupOffersCouponDetails;
import com.stpl.tech.master.domain.model.AppOfferDetail;
import com.stpl.tech.master.domain.model.BannerMetadataRequest;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.CloneCouponData;
import com.stpl.tech.master.domain.model.CouponBulkUpdateDomain;
import com.stpl.tech.master.domain.model.CouponCloneRequest;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CustomerWinbackOfferInfoDomain;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.OfferDayDto;
import com.stpl.tech.master.domain.model.OfferDayTimeDto;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferImageDetail;
import com.stpl.tech.master.domain.model.OfferResponse;
import com.stpl.tech.master.domain.model.OfferTimeDto;
import com.stpl.tech.master.domain.model.OfferTypeFlag;
import com.stpl.tech.master.domain.model.ProductGroup;
import com.stpl.tech.util.EnvType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface OfferManagementService {

    public List<CouponDetail> getRegularCoupons(Integer unitId, Integer loggedInBrandId);

    public List<OfferDetail> getAllOffers(boolean trimmed);

    public OfferDetail getOffer(int offerId);

    public List<CouponDetail> getOfferCoupons(int offerId, boolean applyLimit);

    public OfferDetail addOffer(OfferDetail offerDetail) throws OfferValidationException;

    public OfferDetail updateOffer(OfferDetail offerDetail) throws OfferValidationException;

    public List<IdCodeName> getMarketingPartners();

    public IdCodeName addMarketingPartners(IdCodeName marketingPartner);

    public IdCodeName updateMarketingPartners(IdCodeName marketingPartner);

    public CouponDetail searchCoupon(String couponCode, boolean applyLimit);

    public CouponDetail addCoupon(CouponDetail coupon);

    public CouponDetail updateCoupon(CouponDetail coupon);

    public boolean updateCoupons(List<CouponDetail> coupons);

    public boolean checkCodeAvailiblity(String couponCode);

    public List<String> generateUniqueCoupons(String couponCode, String couponPrefix, int replicateCount) throws DataUpdationException;

    public boolean changeMarketingPartnerStatus(int marketingPartnerId, String active);

    public IdCodeName getMarketingPartner(String key);

    public boolean updateCouponMapping(int couponMappingId, String status);

    public boolean updateCouponMappings(List<CouponDetail> coupons);

    public List<IdName> getOfferAccountsCategories();

    public void markExpiredOffersAsArchived();

    public OfferDetailData getOfferDetailData(int offerDetailId);

    public boolean existCustomerOfferMappingData(int offerDetailId, String number);

    public LaunchOfferData availLaunchOffer(EnvType env, LaunchOfferData input, boolean addUnitMapping);

    public Map<String, List<OfferCategoryType>> getOfferCategories();

    public List<OfferDetail> getOffer(String key, boolean trimmed);

    List<String> getUniqueCoupons(String couponCode, String couponPrefix, int replicateCount)
            throws DataUpdationException;

    List<String> generateCopyCoupons(CouponDetail modalCouponCode, List<String> couponCodeSet)
            throws DataUpdationException;

    public List<AppOfferDetail> getActInactOffers();

    List<AppOfferDetail> getAppOffersByPartnerId(Integer partnerId);

    public Boolean setStatusForAppOffer(IdCodeName idCodeName);

    public Boolean setOrderingForAppOffer(List<IdIndex> list);

    CouponDetail getModalCoupon(String modelCouponCode) throws DataUpdationException;


    OfferImageDetail saveOfferImage(MimeType mimeType, String couponCode, String imageType, MultipartFile file, String s3OfferBucket, String hostURL);

    AppOfferDetail addNewAppOffer(AppOfferDetail offer);

    AppOfferDetail updateAppOffer(AppOfferDetail offer);

    List<NameValue> getOfferActionTypes();

    List<OfferTypeFlag> getOfferTypes();

    List<BannerMetadata> saveBannerMetadata(List<BannerMetadataRequest> request);

    List<BannerMetadataRequest> getBannerMetadata();

    List<ProductGroup> getCategoryFromMenuSequence(MenuApp menuApp) throws DataNotFoundException;

    SignupOffersCouponDetails getCouponDetails();

    boolean addSignupCouponMapping(SignupOffersCouponDetails couponDetails);

	CouponCloneResponse generateCoupon(CouponCloneRequest coupon) throws DataUpdationException;

	public void setCouponCodeAsInactive(Integer couponDetailId);

	CouponCloneResponse createCoupon(CloneCouponData info, List<String> contactNumbers, String startDay,
			Integer applicableUnitId, String applicableRegion, CouponDetailData parentCoupon) throws DataUpdationException;

	CampaignDetail addCampaignDetailData(CampaignDetail campaignDetail) throws NoSuchAlgorithmException, UnsupportedEncodingException;

	List<CampaignDetail> getAllCampaigns();

	CampaignDetail getCampaignById(Integer campaignId);

	CampaignDetail getActiveCampaignForUnitId(Integer unitId, String strategy);

    CampaignDetailResponse getCampaignByTokenAndStatus(String campaignToken, String status);

	List<CampaignDetail> getCampaignsByCampaignDesc(String campaignDesc, Boolean fetchAll);

	CampaignDetail updateCampaignStatus(CampaignDetail campaignDetail);

    CampaignDetail updateCampaign(CampaignDetail campaignDetail) throws IOException;

    CampaignDetail updateCampaignShortUrl(CampaignDetail campaignDetail);

    CampaignImageDetail saveCampaignImage(MultipartFile file, String s3OfferBucket, String offerHostUrl);

    String createCampaignShortUrl(String longUrl) throws IOException;

    void addDeliveryCoupons(List<DeliveryCouponDetailData> couponDetailDataList);

    View getDeliveryCouponSheet();

    void updateAllocationOfCoupon(DeliveryCouponDetailData deliveryCoupon, Integer customerId, String contactNumber, Integer campaignId, int orderId);

    Long validateDeliveryCoupon(String masterCoupon);

    List<CampaignDetail> getActiveCampaignListByValidDate();

    List<Integer> getLinkedCampaignIds(Integer campaignId);

    CouponDetail getAutoApplicableOfferForUnit(Integer unitId);

    OfferDayDto getOfferDayScheduleDetails(Integer offerId);

    OfferTimeDto getOfferTimeDetails(Integer offerId);

    public boolean updateBulkCoupon(CouponBulkUpdateDomain couponBulkUpdateDomain);
    
   // CouponDetail pushCouponMappingToClevertap(CouponDetail coupon, String url ,String token);

	CouponCloneResponse generateCoupon(CouponCloneRequest coupon, CouponDetailData cloneCoupon)
			throws DataUpdationException;

    public void mapCoupon(CouponCloneRequest couponCloneRequest,CouponCloneResponse response);

    List<OfferResponse> getWinbackOffers();

    List<DeliveryOfferDetailData> getWinbackOfferForDelivery();

    CustomerWinbackOfferInfo generateWinbackCoupon(CustomerWinbackOfferInfoDomain domain,Integer customerId);
    CustomerWinbackOfferInfo markNotified(Integer id);
    List<CustomerWinbackOfferInfo> getWinbackInfo();
    List<CustomerWinbackOfferInfo> getWinbackInfo(Date startDate, Date endDate);
    View getWinbackSheet(List<CustomerWinbackOfferInfo> data);

    public Map<String,List<CouponDetail>> getAllLoyaltyBurnOffers(List<String> burnOfferCategoryType,Integer unitId);

    public CouponCloneResponse generateCoupon(int validityDays, String contactNumber, String cloneCode, String prefix);

    public Map<Integer,List<OfferDescriptionMetadata>> getOfferDescriptionMetadata(Boolean getAll, Integer offerId);

    public Boolean updateOfferDescriptionMetadata(Map<Integer,List<OfferDescriptionMetadata>> offerDescriptionData);
    public boolean addCustomerOfferMappingData(CouponCloneResponse response,String contactNumber,String acqSrc);

    OfferDayTimeDto getOfferDayTimeScheduleDetails(Integer offerId);

    public CouponDetail getCouponDetailForUnit(Integer unitId, String couponCode) throws DataNotFoundException;
}
