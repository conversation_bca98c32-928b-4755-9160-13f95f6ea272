/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.model;

import com.stpl.tech.kettle.report.metadata.model.SignInProvider;
import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.util.AppUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UserSessionDetail implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -9082744302862361003L;

    private int userId;
    private String sessionKeyId;
    private int unitId;
    private Integer terminalId;
    private String password;
    private String newPassword;
    private String token;
    private Employee user;
    private String ipAddress;
    private String macAddress;
    private String userAgent;
    private ScreenType screenType;
    private Boolean isDefaultPasscode;
    private String jwtToken;
    private String application;
    private String version;
    private String osVersion;
    private String deviceModel;
    private Map<String, Map<String, Integer>> permissions;
    private Map<String, Map<String, Boolean>> acl;
    private TrueCallerSettings trueCaller;
    private String businessDate;
    private String monkRecipeVersion;
    private Date serverTime;
    private String userEmail;
    private SignInProvider signInProvider;

    private Integer posVersionEnabled;

    private List<String> orderStatus;

    private String currentGeneratedOrderId;

    private String geoLocation;

    public UserSessionDetail() {
        super();
    }

    public UserSessionDetail(int userId, int unitId, int terminalId) {
        super();
        this.userId = userId;
        this.unitId = unitId;
        this.terminalId = terminalId;
    }

    public UserSessionDetail(int userId, int unitId, int terminalId, String password) {
        super();
        this.userId = userId;
        this.unitId = unitId;
        this.terminalId = terminalId;
        this.password = password;
        this.businessDate = AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate());
    }

    public UserSessionDetail(int userId, String sessinKeyId, int unitId, int terminalId, String password,
                             String newPassword) {
        super();
        this.userId = userId;
        this.sessionKeyId = sessinKeyId;
        this.unitId = unitId;
        this.terminalId = terminalId;
        this.password = password;
        this.newPassword = newPassword;
        this.businessDate = AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate());
    }

    public UserSessionDetail(int userId, String sessinKeyId, int unitId, int terminalId, String password,
                             String newPassword, String ipAddress, String macAddress, String userAgent) {
        super();
        this.userId = userId;
        this.sessionKeyId = sessinKeyId;
        this.unitId = unitId;
        this.terminalId = terminalId;
        this.password = password;
        this.newPassword = newPassword;
        this.ipAddress = ipAddress;
        this.macAddress = macAddress;
        this.userAgent = userAgent;
        this.businessDate = AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate());
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getSessionKeyId() {
        return sessionKeyId;
    }

    public void setSessionKeyId(String sessionKeyId) {
        this.sessionKeyId = sessionKeyId;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Employee getUser() {
        return user;
    }

    public void setUser(Employee user) {
        this.user = user;
    }

    /**
     * @return the terminalId
     */
    public Integer getTerminalId() {
        return terminalId;
    }

    /**
     * @param terminalId the terminalId to set
     */
    public void setTerminalId(Integer terminalId) {
        this.terminalId = terminalId;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public ScreenType getScreenType() {
        return screenType;
    }

    public void setScreenType(ScreenType screenType) {
        this.screenType = screenType;
    }

    public Boolean getIsDefaultPasscode() {
        return isDefaultPasscode;
    }

    public void setIsDefaultPasscode(Boolean isDefaultPasscode) {
        this.isDefaultPasscode = isDefaultPasscode;
    }

    public Map<String, Map<String, Integer>> getPermissions() {
        return permissions;
    }

    public void setPermissions(Map<String, Map<String, Integer>> permissions) {
        this.permissions = permissions;
    }

    public String getJwtToken() {
        return jwtToken;
    }

    public void setJwtToken(String jwtToken) {
        this.jwtToken = jwtToken;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

	public Map<String, Map<String, Boolean>> getAcl() {
		if(acl == null){
			acl = new HashMap<>();
		}
		return acl;
	}

	public void setAcl(Map<String, Map<String, Boolean>> acl) {
		this.acl = acl;
	}

    public TrueCallerSettings getTrueCaller() {
        return trueCaller;
    }

    public void setTrueCaller(TrueCallerSettings trueCaller) {
        this.trueCaller = trueCaller;
    }

    public String getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = AppUtils.getSQLFormattedDate(businessDate);
    }

    public String getMonkRecipeVersion() {
        return monkRecipeVersion;
    }

    public void setMonkRecipeVersion(String monkRecipeVersion) {
        this.monkRecipeVersion = monkRecipeVersion;
    }

    public Date getServerTime() {
        return serverTime;
    }

    public void setServerTime(Date serverTime) {
        this.serverTime = serverTime;
    }

    public Boolean getDefaultPasscode() {
        return isDefaultPasscode;
    }

    public void setDefaultPasscode(Boolean defaultPasscode) {
        isDefaultPasscode = defaultPasscode;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public SignInProvider getSignInProvider() {
        return signInProvider;
    }

    public void setSignInProvider(SignInProvider signInProvider) {
        this.signInProvider = signInProvider;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public Integer getPosVersionEnabled() {
        return posVersionEnabled;
    }

    public void setPosVersionEnabled(Integer posVersionEnabled) {
        this.posVersionEnabled = posVersionEnabled;
    }

    public List<String> getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(List<String> orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getCurrentGeneratedOrderId() {
        return currentGeneratedOrderId;
    }

    public void setCurrentGeneratedOrderId(String currentGeneratedOrderId) {
        this.currentGeneratedOrderId = currentGeneratedOrderId;
    }

    public String getGeoLocation() {
        return geoLocation;
    }

    public void setGeoLocation(String geoLocation) {
        this.geoLocation = geoLocation;
    }
}
