package com.stpl.tech.master.core.service.batchProcess;

import com.stpl.tech.master.core.config.TransactionUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class DataSourceResolver {

    private final Map<String, JdbcTemplate> jdbcTemplatesMap = new ConcurrentHashMap<>();

    @SneakyThrows
    public JdbcTemplate resolveCurrent() {
        DataSource dataSource = TransactionUtils.currentDataSource();

        if (dataSource != null) {
            String KEY = dataSource.getConnection().getMetaData().getURL();
            log.info("Current DataSource: {}", KEY);
            return jdbcTemplatesMap.computeIfAbsent(KEY, jdbc -> new JdbcTemplate(dataSource));
        }
        throw new IllegalStateException("No current DataSource found");
    }
}
