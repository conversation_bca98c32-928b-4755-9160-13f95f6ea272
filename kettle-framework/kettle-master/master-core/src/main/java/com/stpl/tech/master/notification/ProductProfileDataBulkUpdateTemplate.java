package com.stpl.tech.master.notification;

import com.stpl.tech.master.data.model.UnitProductPricingBulkUpdateEvent;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class ProductProfileDataBulkUpdateTemplate extends AbstractVelocityTemplate {

    private String basePath;

    private String currentDate;
    private UnitProductPricingBulkUpdateEvent updateEvent;

    public ProductProfileDataBulkUpdateTemplate(String basePath, String currentDate, UnitProductPricingBulkUpdateEvent updateEvent) {
        this.basePath = basePath;
        this.currentDate = currentDate;
        this.updateEvent = updateEvent;
    }


    @Override
    public String getTemplatePath() {
        return "template/ProductProfileBulkUpdateTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "productProfileBulkUpdation/report/" + currentDate + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> data = new HashMap<>();
        if (Objects.nonNull(updateEvent)) {
            data.put("totalRecords", updateEvent.getTotalRecords());
            data.put("totalRecordsChanged", updateEvent.getTotalRecordsChanged());
            data.put("totalRecordsUpdatedSuccessfully", updateEvent.getTotalRecordsUpdatedSuccessfully());
            data.put("totalErrorRecords", updateEvent.getTotalErrorRecords());
            data.put("totalFailureRecords", updateEvent.getTotalFailureRecords());
            data.put("updatedBy", updateEvent.getUpdatedBy());
            data.put("brandId", updateEvent.getBrandId());
            data.put("sheetPath", updateEvent.getSheetPath());
            data.put("unitCategory", updateEvent.getUnitCategory());
        }
        return data;
    }
}
