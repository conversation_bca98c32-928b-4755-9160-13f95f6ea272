/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class UnitActivationEmail extends EmailNotification {

	private UnitActivationReceipt receipt;
	private MasterProperties props;

	public UnitActivationEmail(UnitActivationReceipt receipt, MasterProperties props) {
		this.receipt = receipt;
		this.props = props;
	}

	@Override
	public String[] getToEmails() {
		return AppUtils.isDev(props.getEnvironmentType()) ? new String[] { "<EMAIL>" }
				: new String[] { "<EMAIL>", "<EMAIL>", "<EMAIL>",
						"<EMAIL>", "<EMAIL>","<EMAIL>",
						"<EMAIL>", "<EMAIL>" };
	}

	@Override
	public String getFromEmail() {
		return AppUtils.getFormattedEmail(AppUtils.getPrefix(props.getEnvironmentType()) + "Cafe Admin",
				"<EMAIL>");
	}

	@Override
	public String subject() {
		if (receipt.getAction().equalsIgnoreCase("activate")) {
			return AppUtils.getPrefix(props.getEnvironmentType()) + "Unit Creation "
					+ receipt.getUnit().getName();
		} else {
			return AppUtils.getPrefix(props.getEnvironmentType()) + "Unit_Status Changes "
					+ receipt.getUnit().getName();
		}
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return receipt.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return props.getEnvironmentType();
	}

}
