package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.LCDMenuImage;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;


@Repository
public interface LCDMenuImageDao extends JpaRepository<LCDMenuImage,Long> ,
        JpaSpecificationExecutor<LCDMenuImage> {


        // Find by version
        List<LCDMenuImage> findByVersion(String version);

        // Find by region
        List<LCDMenuImage> findByRegion(String region);

        // Find by price profile
        List<LCDMenuImage> findByPriceProfile(String priceProfile);

        // Find by orientation
        List<LCDMenuImage> findByOrientation(String orientation);

        // Find by slot
        List<LCDMenuImage> findBySlot(String slot);

        // Find by LCD type
        List<LCDMenuImage> findByLcdType(String lcdType);

        // Find by version and region
        List<LCDMenuImage> findByVersionAndRegion(String version, String region);

        // Find by multiple filters
        List<LCDMenuImage> findByVersionAndRegionAndPriceProfile(String version, String region, String priceProfile);

        // Find by URL
        LCDMenuImage findByUrl(String url);

    List<LCDMenuImage> findByPathStartingWithAndUploadTimeBetween(String path, LocalDateTime startDate, LocalDateTime endDate);

        List<LCDMenuImage> findByPathStartingWith(String folderPath, PageRequest uploadTime);

        long countByPathStartingWith(String folderPath);
}
