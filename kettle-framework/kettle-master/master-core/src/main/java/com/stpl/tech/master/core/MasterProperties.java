/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@RefreshScope
public class MasterProperties {

    @Autowired
    private Environment env;

    public EnvType getEnvironmentType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public String getBasePath() {
        return env.getProperty("server.base.dir");
    }

    public String getMacAuthUser() {
        return env.getProperty("mac.auth.user");
    }

    public String getMacAuthPassword() {
        return env.getProperty("mac.auth.pass");
    }

    public String[] getBusinessRegions() {
        return env.getProperty("business.regions").split(",");
    }

    public String getSCMRecipeCostUrl() {
        return env.getProperty("scm.recipe.cost.detail.url");
    }

    public  String getSCMEndpoint(){
        return env.getProperty("scm.service.url", "http://*************:9595/scm-service/rest/v1/");
    }

    public  String getKettleEndpoint(){
        return env.getProperty("base.path.kettle.service");//localhost:8080/kettle-service/rest/v1
    }
    public String getKettleAuthInternal(){
        return env.getProperty("kettle.auth.internal");
    }

    public String getSCMAuthInternal(){
        return env.getProperty("scm.auth.internal");
    }

    public String getS3Bucket() {
        return env.getProperty("amazon.s3.bucket", "chaayosdevtest");
    }

    public String getS3ProductBucket() {
        return env.getProperty("amazon.s3.product.bucket","product.image.dev");
    }



    public String getIconHostUrl(){
        return env.getProperty("icon.image.host.url","https://d1nqp92n3q8zl7.cloudfront.net/product_image/");
    }


    public String getS3OfferServiceBucket() {
        return env.getProperty("amazon.s3.offer.bucket","dev.offer.image");
    }

    public String getS3CdnServiceBucket(){
        return env.getProperty("amazon.s3.cdn.bucket","dev.cdn.static.distribution");
    }

    public String getCategoryHostUrl(){
        return env.getProperty("offer.category.image.host.url", "http://dwzoagfmf5sib.cloudfront.net/");
    }

    public String getCdnUrl(){
        return env.getProperty("cdn.host.url","https://d3t8lo3xxvrk8i.cloudfront.net/");
    }
    public int getSubcriptionProductType() {
		return Integer.valueOf(env.getProperty("subscription.product.type", "3810"));
	}
	

    public String getS3RecipeMediaBucket(){
        return env.getProperty("amazon.s3.recipe.media.bucket", "dev.recipe.image");
    }

    public String getRecipeStepMediaHostUrl(){
        return env.getProperty("recipe.step.media.host.url", "https://d2h1o90ax52pv2.cloudfront.net/");
    }

    public int getUnitCacheThreadCount() {
        return Integer.parseInt(env.getProperty("unit.cache.threads.count", "10"));
    }

    public List<String> getSweetnerAddonsList(Integer unitId){
        List<String> sweetnerAddonsList = new ArrayList<>();
        if(Objects.isNull(unitId) || Boolean.TRUE.equals(isSweetnerAddonFlowApplicable(unitId))){
            sweetnerAddonsList =  Arrays.stream(env.getProperty("sweetner.addon.products","").split(",")).toList();
        }
        return sweetnerAddonsList;

    }

    public  Boolean isSweetnerAddonFlowApplicable(Integer unitId){
        String unitList = env.getProperty("sweetner.addon.flow.application","");
        if("ALL".equalsIgnoreCase(unitList)){
            return true;
        }else{
            List<Integer> unitIds = Arrays.stream(unitList.split(",")).
                    map(Integer::parseInt).toList();
            return unitIds.contains(unitId);
        }
    }


    public Map<Integer,Boolean> getMilkSelectionPaidAddons(){
        Map<Integer,Boolean> milkSelectionPaidAddonMap = new HashMap<>();
        try {
            milkSelectionPaidAddonMap =  Arrays.stream(env.getProperty("env.milk-selection-paid-addons","").split(","
            )).map(Integer::parseInt).toList().stream().collect(Collectors.toMap(integer -> integer , integer -> true));
        }catch (Exception e){
        }
        return milkSelectionPaidAddonMap;
    }


    public String getMenuExcelUploadPath(){
        return env.getProperty("menu.excel.upload.path","menu_excel_upload");
    }

    public Boolean isPriceProfileModeEnabled(){
        return Boolean.valueOf(env.getProperty("env.price-profile-enabled","false"));
    }

    public String getCheckListUrl() {
        return env.getProperty("cdn.productCheckList", "http://d1nqp92n3q8zl7.cloudfront.net/");
    }
    
    public String getLucidApiKey(){
        return env.getProperty("lucid.api.key");
    }

}
