/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "DENOMINATION")
public class Denomination implements Serializable {

	private Integer id;

	private PaymentMode paymentMode;

	private String denominationText;

	private String denominationCode;

	private Integer denominationValue;

	private Integer displayOrder;

	private String status;

	private Integer bundleSize;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "DENOMINATION_ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PAYMENT_MODE", nullable = false)
	public PaymentMode getPaymentMode() {
		return paymentMode;
	}

	public void setPaymentMode(PaymentMode paymentMode) {
		this.paymentMode = paymentMode;
	}

	@Column(name = "DENOMINATION_TEXT", nullable = false)
	public String getDenominationText() {
		return denominationText;
	}

	public void setDenominationText(String denominationText) {
		this.denominationText = denominationText;
	}

	@Column(name = "DENOMINATION_CODE", nullable = false)
	public String getDenominationCode() {
		return denominationCode;
	}

	public void setDenominationCode(String denominationCode) {
		this.denominationCode = denominationCode;
	}

	@Column(name = "DENOMINATION_VALUE", nullable = false)
	public Integer getDenominationValue() {
		return denominationValue;
	}

	public void setDenominationValue(Integer denominationValue) {
		this.denominationValue = denominationValue;
	}

	@Column(name = "DISPLAY_ORDER", nullable = false)
	public Integer getDisplayOrder() {
		return displayOrder;
	}

	public void setDisplayOrder(Integer displayOrder) {
		this.displayOrder = displayOrder;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "BUNDLE_SIZE", nullable = false)
	public Integer getBundleSize() {
		return bundleSize;
	}

	public void setBundleSize(Integer bundleSize) {
		this.bundleSize = bundleSize;
	}

}
