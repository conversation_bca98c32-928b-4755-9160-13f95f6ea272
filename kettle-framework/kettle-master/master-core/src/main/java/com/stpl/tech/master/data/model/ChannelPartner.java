/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * ChannelPartner generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CHANNEL_PARTNER")
public class ChannelPartner implements java.io.Serializable {

	private Integer partnerId;
	private String partnerCode;
	private String partnerDisplayName;
	private String serviceType;
	private Integer creditAccountId;
	private String apiIntegrated = "N";
	private String status;
	private String partnerName;

	public ChannelPartner() {
	}

	public ChannelPartner(String partnerCode, String partnerDisplayName) {
		this.partnerCode = partnerCode;
		this.partnerDisplayName = partnerDisplayName;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "PARTNER_ID", unique = true, nullable = false)
	public Integer getPartnerId() {
		return this.partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}

	@Column(name = "PARTNER_CODE", nullable = false, length = 50)
	public String getPartnerCode() {
		return this.partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	@Column(name = "PARTNER_DISPLAY_NAME", nullable = false, length = 100)
	public String getPartnerDisplayName() {
		return this.partnerDisplayName;
	}

	public void setPartnerDisplayName(String partnerDisplayName) {
		this.partnerDisplayName = partnerDisplayName;
	}

	@Column(name = "SERVICE_TYPE", nullable = false, length = 15)
	public String getServiceType() {
		return serviceType;
	}

	public void setServiceType(String serviceType) {
		this.serviceType = serviceType;
	}

	@Column(name = "CREDIT_ACCOUNT_ID", nullable = true)
	public Integer getCreditAccountId() {
		return creditAccountId;
	}

	public void setCreditAccountId(Integer creditAccountId) {
		this.creditAccountId = creditAccountId;
	}

	@Column(name = "API_INTEGRATED", length = 1)
	public String getApiIntegrated() {
		return apiIntegrated;
	}

	public void setApiIntegrated(String apiIntegrated) {
		this.apiIntegrated = apiIntegrated;
	}

	@Column(name = "PARTNER_STATUS", length = 10)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "PARTNER_NAME",length = 100)
	public String getPartnerName() {
		return partnerName;
	}

	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}
}
