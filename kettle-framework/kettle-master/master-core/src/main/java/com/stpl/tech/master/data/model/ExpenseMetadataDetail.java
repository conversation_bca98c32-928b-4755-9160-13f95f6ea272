/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import lombok.Getter;
import lombok.Setter;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "EXPENSE_METADATA")
public class ExpenseMetadataDetail implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -218396955109981059L;
	private Integer expenseMetadataId;
	private String categoryName;
	private String expenseHeader;
	private String status;
	private String accountableInPnL;
	private String budgetCategory;
	private String isInternalExpense;

	public ExpenseMetadataDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EXPENSE_METADATA_ID", unique = true, nullable = false)
	public Integer getExpenseMetadataId() {
		return this.expenseMetadataId;
	}

	public void setExpenseMetadataId(Integer categoryTaxDataId) {
		this.expenseMetadataId = categoryTaxDataId;
	}

	@Column(name = "CATEGORY_NAME")
	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	@Column(name = "EXPENSE_HEADER")
	public String getExpenseHeader() {
		return expenseHeader;
	}

	public void setExpenseHeader(String budgetHeader) {
		this.expenseHeader = budgetHeader;
	}

	@Column(name = "ACCOUNTABLE_IN_PNL")
	public String getAccountableInPnL() {
		return accountableInPnL;
	}

	public void setAccountableInPnL(String visibleInPnL) {
		this.accountableInPnL = visibleInPnL;
	}

	@Column(name = "BUDGET_CATEGORY")
	public String getBudgetCategory() {
		return budgetCategory;
	}
	
	public void setBudgetCategory(String budgetCategory) {
		this.budgetCategory = budgetCategory;
	}

	@Column(name = "EXPENSE_STATUS")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "IS_INTERNAL_EXPENSE")
	public String getIsInternalExpense() {
		return isInternalExpense;
	}
	public void setIsInternalExpense(String isInternalExpense) {
		this.isInternalExpense = isInternalExpense;
	}

	
}
