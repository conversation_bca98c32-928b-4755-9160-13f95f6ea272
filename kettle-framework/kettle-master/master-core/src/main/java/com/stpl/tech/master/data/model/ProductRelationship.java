/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * ProductRelationship generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PRODUCT_RELATIONSHIP")
public class ProductRelationship implements java.io.Serializable {

	private ProductRelationshipId id;
	private ProductDetail productDetailByConstituentProductId;
	private ProductDetail productDetailByProductId;

	public ProductRelationship() {
	}

	public ProductRelationship(ProductRelationshipId id, ProductDetail productDetailByConstituentProductId,
			ProductDetail productDetailByProductId) {
		this.id = id;
		this.productDetailByConstituentProductId = productDetailByConstituentProductId;
		this.productDetailByProductId = productDetailByProductId;
	}

	@EmbeddedId
	@AttributeOverrides({
			@AttributeOverride(name = "productId", column = @Column(name = "PRODUCT_ID", nullable = false)),
			@AttributeOverride(name = "constituentProductId", column = @Column(name = "CONSTITUENT_PRODUCT_ID", nullable = false)),
			@AttributeOverride(name = "relationshipType", column = @Column(name = "RELATIONSHIP_TYPE", nullable = false, length = 10)),
			@AttributeOverride(name = "quantity", column = @Column(name = "QUANTITY", nullable = false)),
			@AttributeOverride(name = "priceMultiplier", column = @Column(name = "PRICE_MULTIPLIER", nullable = false, precision = 4, scale = 4)) })
	public ProductRelationshipId getId() {
		return this.id;
	}

	public void setId(ProductRelationshipId id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CONSTITUENT_PRODUCT_ID", nullable = false, insertable = false, updatable = false)
	public ProductDetail getProductDetailByConstituentProductId() {
		return this.productDetailByConstituentProductId;
	}

	public void setProductDetailByConstituentProductId(ProductDetail productDetailByConstituentProductId) {
		this.productDetailByConstituentProductId = productDetailByConstituentProductId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_ID", nullable = false, insertable = false, updatable = false)
	public ProductDetail getProductDetailByProductId() {
		return this.productDetailByProductId;
	}

	public void setProductDetailByProductId(ProductDetail productDetailByProductId) {
		this.productDetailByProductId = productDetailByProductId;
	}

}
