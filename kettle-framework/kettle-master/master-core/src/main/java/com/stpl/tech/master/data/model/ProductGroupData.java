package com.stpl.tech.master.data.model;

import com.stpl.tech.master.domain.model.MenuApp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "PRODUCT_GROUP_DATA")
public class ProductGroupData {

    private Integer groupId;
    private String groupName;
    private String groupTag;
    private String groupType;
    private String groupDescription;
    private String menuType;
    private Integer createdBy;
    private Date creationTime;
    private String status;
    private Integer cloneId;
    private String menuApp;
    private ProductGroupImageData productGroupImageData;
    private String categoryTag;
    private String menuCategoryImage;
    private String menuCategoryDetails;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRODUCT_GROUP_ID", unique = true, nullable = false)
    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    @Column(name = "GROUP_NAME", nullable = false)
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    @Column(name = "GROUP_TAG", nullable = false)
    public String getGroupTag() {
        return groupTag;
    }

    public void setGroupTag(String groupTag) {
        this.groupTag = groupTag;
    }

    @Column(name = "GROUP_TYPE", nullable = false)
    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    @Column(name = "GROUP_DESCRIPTION", nullable = false)
    public String getGroupDescription() {
        return groupDescription;
    }

    public void setGroupDescription(String groupDescription) {
        this.groupDescription = groupDescription;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "MENU_TYPE", nullable = true)
    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    @Column(name = "CLONE_ID", nullable = true)
    public Integer getCloneId() {
        return cloneId;
    }

    public void setCloneId(Integer cloneId) {
        this.cloneId = cloneId;
    }

    @OneToOne
    @JoinColumn(name = "PRODUCT_GROUP_IMAGE_ID")
    public ProductGroupImageData getProductGroupImageData() {
        return productGroupImageData;
    }

    public void setProductGroupImageData(ProductGroupImageData productGroupImageData) {
        this.productGroupImageData = productGroupImageData;
    }

    @Column(name = "MENU_APP", nullable = true)
    public String getMenuApp() {
        return menuApp;
    }

    public void setMenuApp(String menuApp) {
        this.menuApp = menuApp;
    }

    @Column(name = "CATEGORY_TAG", nullable = true)
    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    @Column(name = "MENU_CATEGORY_IMAGE")
    public String getMenuCategoryImage() {
        return menuCategoryImage;
    }

    public void setMenuCategoryImage(String menuCategoryImage) {
        this.menuCategoryImage = menuCategoryImage;
    }

    @Column(name = "MENU_CATEGORY_DETAILS")
    public String getMenuCategoryDetails() {
        return menuCategoryDetails;
    }

    public void setMenuCategoryDetails(String menuCategoryDetails) {
        this.menuCategoryDetails = menuCategoryDetails;
    }
}
