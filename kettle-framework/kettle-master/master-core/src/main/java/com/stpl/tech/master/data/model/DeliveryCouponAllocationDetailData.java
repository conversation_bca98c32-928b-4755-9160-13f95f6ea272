package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "DELIVERY_COUPON_ALLOCATION_DETAIL_DATA")
public class DeliveryCouponAllocationDetailData {

    private Integer allocationId;
    private Integer deliveryCouponId;
    private Integer customerId;
    private String contactNumber;
    private Integer campaignId;
    private Date allotmentTime;
    private Integer allotmentOrderId;

    private Date startDate;
    private Date endDate;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ALLOCATION_ID", unique = true, nullable = false)
    public Integer getAllocationId() {
        return allocationId;
    }

    public void setAllocationId(Integer allocationId) {
        this.allocationId = allocationId;
    }

    @Column(name = "DELIVERY_COUPON_ID")
    public Integer getDeliveryCouponId() {
        return deliveryCouponId;
    }

    public void setDeliveryCouponId(Integer deliveryCouponId) {
        this.deliveryCouponId = deliveryCouponId;
    }

    @Column(name = "CUSTOMER_ID")
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "CONTACT_NUMBER")
    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    @Column(name = "CAMPAIGN_ID")
    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    @Column(name = "ALLOTMENT_TIME")
    public Date getAllotmentTime() {
        return allotmentTime;
    }

    public void setAllotmentTime(Date allotmentTime) {
        this.allotmentTime = allotmentTime;
    }

    @Column(name = "ALLOTMENT_ORDER_ID")
    public Integer getAllotmentOrderId() {
        return allotmentOrderId;
    }

    public void setAllotmentOrderId(Integer allotmentOrderId) {
        this.allotmentOrderId = allotmentOrderId;
    }

    @Column(name = "START_DATE")
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "END_DATE")
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }
}
