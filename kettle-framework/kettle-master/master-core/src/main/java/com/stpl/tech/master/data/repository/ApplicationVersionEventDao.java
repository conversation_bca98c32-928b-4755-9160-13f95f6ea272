package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.ApplicationVersionEvent;
import com.stpl.tech.util.AppConstants;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ApplicationVersionEventDao extends JpaRepository<ApplicationVersionEvent, Integer> {

    public List<ApplicationVersionEvent> findByStatus(String status);

    @Modifying
    @Query(value = "UPDATE APPLICATION_VERSION_EVENT ave SET ave.EVENT_STATUS = ?2 WHERE ave.UNIT_ID IN (?1) AND ave.EVENT_STATUS IN (?3) AND ave.APPLICATION_NAME = ?4", nativeQuery = true)
    public void updateExistingEvent(Set<Integer> unitIds, String status, List<String> statuses,String applicationName);

    ApplicationVersionEvent findByUnitIdAndApplicationNameAndStatus(Integer unitId, String applicationName, String status);
    List<ApplicationVersionEvent> findByUnitIdAndTerminalIdAndApplicationNameInAndStatus(Integer unitId,Integer terminalId,List<String> applicationName, String status);
    @Modifying
    @Query(value = "UPDATE APPLICATION_VERSION_EVENT ave SET ave.EVENT_STATUS = ?4 WHERE ave.UNIT_ID IN (?1) AND ave.TERMINAL_ID IN (?2) AND ave.APPLICATION_NAME = ?3", nativeQuery = true)
    public void updateVersionEventStatus(Integer unitId,Integer terminalId,String applicationName, String status);

    public List<ApplicationVersionEvent> findByUnitIdAndTerminalIdAndApplicationNameAndApplicationVersionAndStatusIn(Integer unitId,Integer terminalId,String applicationName, String cafeAppVersion, List<String> statuses);


}
