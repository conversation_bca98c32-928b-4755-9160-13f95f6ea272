package com.stpl.tech.master.core.external.report.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.kettle.report.metadata.model.ReportSummary;
import com.stpl.tech.kettle.report.metadata.model.ReportVersionDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.FileDownloadException;
import com.stpl.tech.master.core.external.report.dao.ReportManagementDao;
import com.stpl.tech.master.core.external.report.service.ReportManagementService;
import com.stpl.tech.master.data.model.XMLReportDefinitionData;
import com.stpl.tech.master.data.model.XMLReportVersionData;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.JaxbUtil;

@Service
public class ReportManagementServiceImpl implements ReportManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(ReportManagementServiceImpl.class);
    private static final String TEMP_UPLOAD_PATH = "/data/s3/tempReport/";
    private static final String TEMP_DOWNLOAD_PATH = "/data/s3/download/";

    @Autowired
    private ReportManagementDao reportManagementDao;

    @Autowired
    FileArchiveService fileArchiveService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ReportSummary> getReportVersionHistoryById(Integer reportId) {
        return reportManagementDao.getAllReportVersionHistoryById(reportId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<XMLReportDefinitionData> getAllReportCategories(String executionEnvironment) {
        return reportManagementDao.getAllReportCategories(executionEnvironment);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public int addNewReport(ReportSummary report) throws DataUpdationException {
        return reportManagementDao.addNewReport(report);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int updateStatus(int reportId, String status) {
        return reportManagementDao.updateStatus(reportId, status);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int addNewReportVersion(ReportVersionDetail report) {
        return reportManagementDao.addNewReportVersion(report);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int markDefaultReportVersion(int reportId, int versionId) {
        return reportManagementDao.setDefaultReportVersion(reportId, versionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, readOnly = false, transactionManager = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public void uploadNewReportVersion(String s3ReportBucket, MultipartFile file, String comments, int reportId) throws IOException {
        String filename = file.getOriginalFilename();
        filename = filename.replaceAll(" ", "_").toLowerCase();

        ReportVersionDetail versionDetail = new ReportVersionDetail();
        versionDetail.setReportId(reportId);
        versionDetail.setComments(comments);
        versionDetail.setFileName(filename);

        uploadAuditReportToS3(s3ReportBucket, versionDetail, file);
    }

    private File convertFromMultiPart(String fileName, MultipartFile multipartFile) throws IOException {
        fileName = fileName.replaceAll(" ", "_").toLowerCase();
        File file = new File(TEMP_UPLOAD_PATH + fileName);
        if (!file.exists()) {
            file.getParentFile().mkdirs();
        }
        file.createNewFile();
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(multipartFile.getBytes());
        fos.close();
        return file;
    }

    private int uploadAuditReportToS3(String s3ReportBucket, ReportVersionDetail report, MultipartFile file)
            throws IOException {

        String fileName = report.getFileName();
        String baseDir = AppConstants.S3_REPORT_BASE_PATH + "/" + AppConstants.S3_REPORT_VERSION_FOLDER;
        String extension = FilenameUtils.getExtension(fileName);
        LOG.info(":::::: Request to upload New Report Version ::::::");
        try {
            FileDetail detail = fileArchiveService.saveFileToS3(s3ReportBucket, baseDir,
                    convertFromMultiPart(fileName ,file), true);
            if (detail != null) {
                report.setS3Link(detail.getUrl());
                report.setMimeType(extension);
                report.setS3Bucket(detail.getBucket());
                report.setS3Key(detail.getKey());
                int versionId = reportManagementDao.addNewReportVersion(report);
                return versionId;
            }
        } catch (Exception e) {
            LOG.error("Encountered error while uploading report to S3", e);
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public void downloadManagementReport(HttpServletResponse response, Integer versionId)
			throws DataNotFoundException, FileDownloadException, IOException {
        LOG.info(":::::: Request to download New Report Version ::::::");
        XMLReportVersionData versionDetail = reportManagementDao.find(XMLReportVersionData.class, versionId);
		if (versionDetail != null) {
            FileDetail fileDetail = new FileDetail(versionDetail.getS3Bucket(),
                    versionDetail.getS3Key(), versionDetail.getS3Link());
            File file = fileArchiveService.getFileFromS3(TEMP_DOWNLOAD_PATH, fileDetail);

			convertFileToOutputStream(response, file, versionDetail.getMimeType());

		} else {
			throw new DataNotFoundException("Invalid Version id");
		}
	}

    
	@Override
	public void downloadManagementReport(HttpServletResponse response, String template) throws IOException {
		response.setContentType("text/xml");
        response.addHeader("Content-Disposition", "attachment; filename=" + template);
		InputStream in = this.getClass().getClassLoader().getResourceAsStream(template);
		OutputStream out = response.getOutputStream();
		IOUtils.copy(in, out);
		in.close();
		out.close();
		return;
	}

    private void convertFileToOutputStream(HttpServletResponse response, File file, String mimeType) throws IOException{
        if (file != null) {
            response.setContentType(mimeType);
            response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
            byte[] bytesArray = new byte[(int) file.length()];
            response.setContentLength(bytesArray.length);
            try {
                OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = new FileInputStream(file);
                int counter = 0;
                while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                    outputStream.write(bytesArray, 0, counter);
                    outputStream.flush();
                }
                outputStream.close();
                inputStream.close();
            } catch (IOException e) {
                LOG.error("Encountered error while writing file to response stream", e);
                throw e;
            } finally {
                response.getOutputStream().flush();
                file.delete(); // delete the temporary file created after completing request
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<XMLReportDefinitionData> getAllReportList(String executionEnvironment) {
        return reportManagementDao.getAllReportCategoryList(executionEnvironment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public File getS3FileFromActiveReportVersion(Integer reportId, Integer versionNo) throws DataNotFoundException, IOException {
        if (reportId != null) {
            LOG.info(":::::: Request to GET New Report Version ::::::");
            XMLReportVersionData versionDetail = reportManagementDao.getActiveReportVersion(reportId, versionNo);
            if (versionDetail != null) {
                FileDetail fileDetail = new FileDetail(versionDetail.getS3Bucket(),
                        versionDetail.getS3Key(), versionDetail.getS3Link());
                File file = fileArchiveService.getFileFromS3(TEMP_DOWNLOAD_PATH, fileDetail);
                if (file != null) {
                   return file;
                } else {
                    throw new DataNotFoundException("File Not Found");
                }
            } else {
                throw new DataNotFoundException("Invalid Version id");
            }
        } else {
            throw new DataNotFoundException("Please provide Version id");
        }
    }

	@Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ReportCategories getReportCategory(int reportId) throws IOException, DataNotFoundException {
		XMLReportDefinitionData def = reportManagementDao.find(XMLReportDefinitionData.class, reportId);
		File file = getS3FileFromActiveReportVersion(reportId, def.getVersion());
		return JaxbUtil.jaxbXMLToObject(ReportCategories.class, file);
	}

}
