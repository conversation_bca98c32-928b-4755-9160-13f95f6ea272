package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "FEEDBACK_QUESTIONS_DETAIL")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class FeedbackQuestionsDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Integer id;
    @Column(name = "QUESTION",columnDefinition = "TEXT")
    private String question;
    @Column(name = "QUESTION_TYPE")
    private String questionType;
    @Column(name = "STATUS")
    private String questionStatus;
    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
    @Column(name = "UPDATED_AT")
    private Date updatedAt;
    @Column(name = "QUESTION_REASON",columnDefinition = "TEXT")
    private String questionReason;
}
