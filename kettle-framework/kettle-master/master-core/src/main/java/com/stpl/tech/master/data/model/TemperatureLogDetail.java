/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "TEMPERATURE_LOG_DETAIL")
public class TemperatureLogDetail implements java.io.Serializable {

	private Integer id;
	private Integer unitId;
	private String locationId;
	private String locationType;
	private String deviceName;
	private String deviceLocation;
	private String breachDetected;
	private String isValid;
	private String isNotified;
	private String macAddress;
	private String ipAddress;
	private Date logTime;
	private Date logDate;
	private Date notificationTime;
	private BigDecimal temperatureValue;
	private BigDecimal temperatureThresholdValue;
	private BigDecimal humidityValue;
	private BigDecimal humidityThresholdValue;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "LOG_DETAIL_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	
	@Column(name = "UNIT_ID")
	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	@Column(name = "MAC_ADDRESS")
	public String getMacAddress() {
		return macAddress;
	}

	public void setMacAddress(String macAddress) {
		this.macAddress = macAddress;
	}

	@Column(name = "LOCATION_ID")
	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	@Column(name = "LOCATION_TYPE")
	public String getLocationType() {
		return locationType;
	}

	public void setLocationType(String locationType) {
		this.locationType = locationType;
	}

	@Column(name = "DEVICE_NAME")
	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	@Column(name = "DEVICE_LOCATION")
	public String getDeviceLocation() {
		return deviceLocation;
	}

	public void setDeviceLocation(String deviceLocation) {
		this.deviceLocation = deviceLocation;
	}

	@Column(name = "BREACH_DETECTED")
	public String getBreachDetected() {
		return breachDetected;
	}

	public void setBreachDetected(String breachDetected) {
		this.breachDetected = breachDetected;
	}

	@Column(name = "IS_VALID")
	public String getIsValid() {
		return isValid;
	}

	public void setIsValid(String isValid) {
		this.isValid = isValid;
	}

	@Column(name = "IS_NOTIFIED")
	public String getIsNotified() {
		return isNotified;
	}

	public void setIsNotified(String isNotified) {
		this.isNotified = isNotified;
	}

	@Column(name = "IP_ADDRESS")
	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	@Column(name = "LOG_TIME", nullable = true, length = 19)
	public Date getLogTime() {
		return logTime;
	}

	public void setLogTime(Date logTime) {
		this.logTime = logTime;
	}

	@Column(name = "LOG_DATE", nullable = true, length = 10)
	public Date getLogDate() {
		return logDate;
	}

	public void setLogDate(Date logDate) {
		this.logDate = logDate;
	}

	@Column(name = "TEMPERATURE_VALUE")
	public BigDecimal getTemperatureValue() {
		return temperatureValue;
	}

	public void setTemperatureValue(BigDecimal temperatureValue) {
		this.temperatureValue = temperatureValue;
	}

	@Column(name = "TEMPERATURE_THRESHOLD_VALUE")
	public BigDecimal getTemperatureThresholdValue() {
		return temperatureThresholdValue;
	}

	public void setTemperatureThresholdValue(BigDecimal temperatureThresholdValue) {
		this.temperatureThresholdValue = temperatureThresholdValue;
	}

	@Column(name = "HUMIDITY_VALUE")
	public BigDecimal getHumidityValue() {
		return humidityValue;
	}

	public void setHumidityValue(BigDecimal humidityValue) {
		this.humidityValue = humidityValue;
	}

	@Column(name = "HUMIDITY_THRESHOLD_VALUE")
	public BigDecimal getHumidityThresholdValue() {
		return humidityThresholdValue;
	}

	public void setHumidityThresholdValue(BigDecimal humidityThresholdValue) {
		this.humidityThresholdValue = humidityThresholdValue;
	}

	@Column(name = "NOTIFICATION_TIME", nullable = true, length = 19)
	public Date getNotificationTime() {
		return notificationTime;
	}

	public void setNotificationTime(Date notificationTime) {
		this.notificationTime = notificationTime;
	}

	
	@Override
	public String toString() {
		return "TemperatureLogDetail [id=" + id + ", locationId=" + locationId + ", locationType=" + locationType
				+ ", deviceName=" + deviceName + ", deviceLocation=" + deviceLocation + ", breachDetected="
				+ breachDetected + ", isValid=" + isValid + ", isNotified="
				+ isNotified + ", macAddress=" + macAddress + ", ipAddress=" + ipAddress + ", logTime=" + logTime
				+ ", temperatureValue=" + temperatureValue + ", temperatureThresholdValue=" + temperatureThresholdValue
				+ ", humidityValue=" + humidityValue + ", humidityThresholdValue=" + humidityThresholdValue + "]";
	}


	
}
