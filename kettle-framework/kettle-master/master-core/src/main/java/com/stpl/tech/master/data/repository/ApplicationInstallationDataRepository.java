package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.ApplicationInstallationData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApplicationInstallationDataRepository extends JpaRepository<ApplicationInstallationData, Integer> {
    
    /**
     * Find all active installations for a unit
     * @param unitId unit ID
     * @param status installation status
     * @return List of active installations
     */
    List<ApplicationInstallationData> findByUnitIdAndStatus(Integer unitId, String status);
    
    /**
     * Find installation by unit ID and application name
     * @param unitId unit ID
     * @param applicationName application name
     * @return ApplicationInstallationData or null if not found
     */
    ApplicationInstallationData findByUnitIdAndApplicationName(Integer unitId, String applicationName);
    
    /**
     * Find installation by machine ID
     * @param machineId machine ID (MAC address)
     * @return ApplicationInstallationData or null if not found
     */
    ApplicationInstallationData findByMachineId(String machineId);
    
    /**
     * Find all active installations for an application
     * @param applicationName application name
     * @param status installation status
     * @return List of active installations
     */
    List<ApplicationInstallationData> findByApplicationNameAndStatus(String applicationName, String status);
    
    /**
     * Find installation by unit ID, terminal ID and status
     * @param unitId unit ID
     * @param terminal terminal ID
     * @param status installation status
     * @return ApplicationInstallationData or null if not found
     */
    ApplicationInstallationData findByUnitIdAndTerminalAndStatus(Integer unitId, Integer terminal, String status);
} 