package com.stpl.tech.master.data.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import javax.persistence.*;
import java.sql.Timestamp;

@Getter
@Setter
@Entity
@Table(name = "UNIT_PRODUCT_STATION_MAPPING", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"PRODUCT_ID", "STATION_CATEGORY_MAPPING_ID", "STATUS"}))
public class UnitProductStationMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    
    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "STATION_CATEGORY_MAPPING_ID", nullable = false)
    @EqualsAndHashCode.Exclude
    private UnitStationCategoryMapping stationCategoryMapping;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "STATUS", nullable = false)
    private Status status;
    
    @Column(name = "CREATED_AT", nullable = false, updatable = false)
    private Timestamp createdAt;
    
    @Column(name = "UPDATED_AT")
    private Timestamp updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = new Timestamp(System.currentTimeMillis());
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Timestamp(System.currentTimeMillis());
    }

    public Integer getUnitId() {
        return stationCategoryMapping != null ? stationCategoryMapping.getUnitId() : null;
    }
}