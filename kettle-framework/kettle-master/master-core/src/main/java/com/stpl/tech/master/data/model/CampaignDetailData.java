package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "CAMPAIGN_DETAIL_DATA")
public class CampaignDetailData {

    private Integer campaignId;
    private String primaryUrl;
    private String campaignStrategy;
    private String campaignSource;
    private String campaignMedium;
    private String campaignName;
    private String campaignCategory;
    private String campaignDesc;
    private String couponCode;
    private String couponCodeDesc;
    private String isCouponClone;
    private String region;
    private String city;
    private String unitIds;
    private Integer usageLimit;
    private Date startDate;
    private Date endDate;
    private Integer validity;
    private String heroBannerMobile;
    private String heroBannerDesktop;
    private String landingPageDesc;
    private String smsTemplate;
    private String smsReminder;
    private String whatsappTemplate;
    private String whatsappReminder;
    private Integer reminderDayGap;
    private String utmHeading;
    private String utmDesc;
    private String utmImageUrl;
    private String redirectionUrl;
    private String campaignStatus;
    private String campaignToken;
    private String image1;
    private String image2;
    private String image3;
    private String longUrl;
    private String shortUrl;
    private String newCustomerOnly;
    private String campaignReach;
    private List<CampaignCouponMapping> couponMapping;
    private String couponPrefix;
    private Integer linkedCampaignId;
    private Integer couponApplicableAfter;
    private Integer brandId;
    private String applicableForOrder;
    private String parentCampaignStrategy;
    private Integer launchUnitId;

    private Date cafeLaunchDate;

    private String crmAppBannerUrl;

    public CampaignDetailData() {
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CAMPAIGN_ID", unique = true, nullable = false)
    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    @Column(name = "PRIMARY_URL")
    public String getPrimaryUrl() {
        return primaryUrl;
    }

    public void setPrimaryUrl(String primaryUrl) {
        this.primaryUrl = primaryUrl;
    }

    @Column(name = "CAMPAIGN_STRATEGY")
    public String getCampaignStrategy() {
        return campaignStrategy;
    }

    public void setCampaignStrategy(String campaignStrategy) {
        this.campaignStrategy = campaignStrategy;
    }

    @Column(name = "CAMPAIGN_SOURCE")
    public String getCampaignSource() {
        return campaignSource;
    }

    public void setCampaignSource(String campaignSource) {
        this.campaignSource = campaignSource;
    }

    @Column(name = "CAMPAIGN_MEDIUM")
    public String getCampaignMedium() {
        return campaignMedium;
    }

    public void setCampaignMedium(String campaignMedium) {
        this.campaignMedium = campaignMedium;
    }

    @Column(name = "CAMPAIGN_NAME")
    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    @Column(name = "CAMPAIGN_CATEGORY")
    public String getCampaignCategory() {
        return campaignCategory;
    }

    public void setCampaignCategory(String campaignCategory) {
        this.campaignCategory = campaignCategory;
    }

    @Column(name = "CAMPAIGN_DESC")
    public String getCampaignDesc() {
        return campaignDesc;
    }

    public void setCampaignDesc(String campaignDesc) {
        this.campaignDesc = campaignDesc;
    }

    @Column(name = "COUPON_CODE")
    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @Column(name = "COUPON_CODE_DESC")
    public String getCouponCodeDesc() {
        return couponCodeDesc;
    }

    public void setCouponCodeDesc(String couponCodeDesc) {
        this.couponCodeDesc = couponCodeDesc;
    }

    @Column(name = "IS_COUPON_CLONE")
    public String getIsCouponClone() {
        return isCouponClone;
    }

    public void setIsCouponClone(String isCouponClone) {
        this.isCouponClone = isCouponClone;
    }

    @Column(name = "REGION")
    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    @Column(name = "CITY", columnDefinition = "TEXT")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name = "UNIT_IDS", columnDefinition = "TEXT")
    public String getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(String unitIds) {
        this.unitIds = unitIds;
    }

    @Column(name = "USAGE_LIMIT")
    public Integer getUsageLimit() {
        return usageLimit;
    }

    public void setUsageLimit(Integer usageLimit) {
        this.usageLimit = usageLimit;
    }

    @Column(name = "START_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "END_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Column(name = "VALIDITY")
    public Integer getValidity() {
        return validity;
    }

    public void setValidity(Integer validity) {
        this.validity = validity;
    }

    @Column(name = "HERO_BANNER_MOBILE")
    public String getHeroBannerMobile() {
        return heroBannerMobile;
    }

    public void setHeroBannerMobile(String heroBannerMobile) {
        this.heroBannerMobile = heroBannerMobile;
    }

    @Column(name = "HERO_BANNER_DESKTOP")
    public String getHeroBannerDesktop() {
        return heroBannerDesktop;
    }

    public void setHeroBannerDesktop(String heroBannerDesktop) {
        this.heroBannerDesktop = heroBannerDesktop;
    }

    @Column(name = "LANDING_PAGE_DESC")
    public String getLandingPageDesc() {
        return landingPageDesc;
    }

    public void setLandingPageDesc(String landingPageDesc) {
        this.landingPageDesc = landingPageDesc;
    }

    @Column(name = "SMS_TEMPLATE")
    public String getSmsTemplate() {
        return smsTemplate;
    }

    public void setSmsTemplate(String smsTemplate) {
        this.smsTemplate = smsTemplate;
    }

    @Column(name = "SMS_REMINDER")
    public String getSmsReminder() {
        return smsReminder;
    }

    public void setSmsReminder(String smsReminder) {
        this.smsReminder = smsReminder;
    }

    @Column(name = "WHATSAPP_TEMPLATE")
    public String getWhatsappTemplate() {
        return whatsappTemplate;
    }

    public void setWhatsappTemplate(String whatsappTemplate) {
        this.whatsappTemplate = whatsappTemplate;
    }

    @Column(name = "WHATSAPP_REMINDER")
    public String getWhatsappReminder() {
        return whatsappReminder;
    }

    public void setWhatsappReminder(String whatsappReminder) {
        this.whatsappReminder = whatsappReminder;
    }

    @Column(name = "REMINDER_DAY_GAP")
    public Integer getReminderDayGap() {
        return reminderDayGap;
    }

    public void setReminderDayGap(Integer reminderDayGap) {
        this.reminderDayGap = reminderDayGap;
    }

    @Column(name = "UTM_HEADING")
    public String getUtmHeading() {
        return utmHeading;
    }

    public void setUtmHeading(String utmHeading) {
        this.utmHeading = utmHeading;
    }

    @Column(name = "UTM_DESC")
    public String getUtmDesc() {
        return utmDesc;
    }

    public void setUtmDesc(String utmDesc) {
        this.utmDesc = utmDesc;
    }

    @Column(name = "UTM_IMAGE_URL")
    public String getUtmImageUrl() {
        return utmImageUrl;
    }

    public void setUtmImageUrl(String utmImageUrl) {
        this.utmImageUrl = utmImageUrl;
    }

    @Column(name = "REDIRECTION_URL")
    public String getRedirectionUrl() {
        return redirectionUrl;
    }

    public void setRedirectionUrl(String redirectionUrl) {
        this.redirectionUrl = redirectionUrl;
    }

    @Column(name = "CAMPAIGN_STATUS")
    public String getCampaignStatus() {
        return campaignStatus;
    }

    public void setCampaignStatus(String campaignStatus) {
        this.campaignStatus = campaignStatus;
    }

    @Column(name = "CAMPAIGN_TOKEN")
    public String getCampaignToken() {
        return campaignToken;
    }

    public void setCampaignToken(String campaignToken) {
        this.campaignToken = campaignToken;
    }

    @Column(name = "IMAGE1")
    public String getImage1() {
        return image1;
    }

    public void setImage1(String image1) {
        this.image1 = image1;
    }

    @Column(name = "IMAGE2")
    public String getImage2() {
        return image2;
    }

    public void setImage2(String image2) {
        this.image2 = image2;
    }

    @Column(name = "IMAGE3")
    public String getImage3() {
        return image3;
    }

    public void setImage3(String image3) {
        this.image3 = image3;
    }

    @Column(name = "LONG_URL")
    public String getLongUrl() {
        return longUrl;
    }

    public void setLongUrl(String longUrl) {
        this.longUrl = longUrl;
    }

    @Column(name = "SHORT_URL")
    public String getShortUrl() {
        return shortUrl;
    }

    public void setShortUrl(String shortUrl) {
        this.shortUrl = shortUrl;
    }

    @Column(name = "NEW_CUSTOMER_ONLY")
    public String getNewCustomerOnly() {
        return newCustomerOnly;
    }

    public void setNewCustomerOnly(String newCustomerOnly) {
        this.newCustomerOnly = newCustomerOnly;
    }

    @Column(name = "CAMPAIGN_REACH")
    public String getCampaignReach() {
        return campaignReach;
    }

    public void setCampaignReach(String campaignReach) {
        this.campaignReach = campaignReach;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "campaignDetailData")
    public List<CampaignCouponMapping> getCouponMapping() {
        return couponMapping;
    }

    public void setCouponMapping(List<CampaignCouponMapping> couponMapping) {
        this.couponMapping = couponMapping;
    }

    @Column(name = "COUPON_PREFIX")
    public String getCouponPrefix() {
        return couponPrefix;
    }

    public void setCouponPrefix(String couponPrefix) {
        this.couponPrefix = couponPrefix;
    }

    @Column(name = "LINKED_CAMPAIGN_ID")
    public Integer getLinkedCampaignId() {
        return linkedCampaignId;
    }

    public void setLinkedCampaignId(Integer linkedCampaignId) {
        this.linkedCampaignId = linkedCampaignId;
    }

    @Column(name = "COUPON_APPLICABLE_AFTER")
    public Integer getCouponApplicableAfter() {
        return couponApplicableAfter;
    }

    public void setCouponApplicableAfter(Integer couponApplicableAfter) {
        this.couponApplicableAfter = couponApplicableAfter;
    }

    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "APPLICABLE_FOR_ORDER")
    public String getApplicableForOrder() {
        return applicableForOrder;
    }

    public void setApplicableForOrder(String applicableForOrder) {
        this.applicableForOrder = applicableForOrder;
    }

    @Column(name = "PARENT_CAMPAIGN_STRATEGY")
    public String getParentCampaignStrategy() {
        return parentCampaignStrategy;
    }

    public void setParentCampaignStrategy(String parentCampaignStrategy) {
        this.parentCampaignStrategy = parentCampaignStrategy;
    }

    @Column(name = "LAUNCH_UNIT_ID")
    public Integer getLaunchUnitId() {
        return launchUnitId;
    }

    public void setLaunchUnitId(Integer launchUnitId) {
        this.launchUnitId = launchUnitId;
    }

    @Column(name = "CAFE_LAUNCH_DATE")
    public Date getCafeLaunchDate() {
        return cafeLaunchDate;
    }

    public void setCafeLaunchDate(Date cafeLaunchDate) {
        this.cafeLaunchDate = cafeLaunchDate;
    }

    @Column(name = "CRM_APP_BANNER")
    public String getCrmAppBannerUrl() {
        return crmAppBannerUrl;
    }

    public void setCrmAppBannerUrl(String crmAppBannerUrl) {
        this.crmAppBannerUrl = crmAppBannerUrl;
    }
}
