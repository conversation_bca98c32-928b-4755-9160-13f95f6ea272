package com.stpl.tech.master.data.model;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Data
@Entity
@Table(name = "UNIT_PRODUCT_SYNC_LOGS")
public class UnitProductSyncLog {

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @JoinColumn(name = "FROM_UNIT_ID", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UnitDetail fromUnit;

    @JoinColumn(name = "TO_UNIT_ID", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private UnitDetail toUnit;

    @Column(name = "CREATED_AT", nullable = false)
    private Date createdAt;

    @Column(name = "CREATED_BY", nullable = false)
    private Integer createdBy;

    @Lob
    @Column(name = "COMMENT", columnDefinition = "LONGTEXT", nullable = false)
    private String comment;

}
