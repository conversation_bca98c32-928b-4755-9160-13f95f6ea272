/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 14 Jul, 2015 1:35:15 AM by Hibernate Tools 4.0.0

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * Home object for domain model class ProductRelationship.
 * 
 * @see com.stpl.tech.master.data.model.ProductRelationship
 * <AUTHOR> Tools
 */
@Stateless
public class ProductRelationshipHome {

	private static final Log log = LogFactory.getLog(ProductRelationshipHome.class);

	@PersistenceContext
	private EntityManager entityManager;

	public void persist(ProductRelationship transientInstance) {
		log.debug("persisting ProductRelationship instance");
		try {
			entityManager.persist(transientInstance);
			log.debug("persist successful");
		} catch (RuntimeException re) {
			log.error("persist failed", re);
			throw re;
		}
	}

	public void remove(ProductRelationship persistentInstance) {
		log.debug("removing ProductRelationship instance");
		try {
			entityManager.remove(persistentInstance);
			log.debug("remove successful");
		} catch (RuntimeException re) {
			log.error("remove failed", re);
			throw re;
		}
	}

	public ProductRelationship merge(ProductRelationship detachedInstance) {
		log.debug("merging ProductRelationship instance");
		try {
			ProductRelationship result = entityManager.merge(detachedInstance);
			log.debug("merge successful");
			return result;
		} catch (RuntimeException re) {
			log.error("merge failed", re);
			throw re;
		}
	}

	public ProductRelationship findById(ProductRelationshipId id) {
		log.debug("getting ProductRelationship instance with productId: " + id);
		try {
			ProductRelationship instance = entityManager.find(ProductRelationship.class, id);
			log.debug("get successful");
			return instance;
		} catch (RuntimeException re) {
			log.error("get failed", re);
			throw re;
		}
	}
}
