package com.stpl.tech.master.core.external.refLookup.dao;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.data.dao.AbstractMasterDao;
import com.stpl.tech.master.domain.model.ListData;

import java.util.List;

public interface RefLookupDao extends AbstractMasterDao {

    public List<ListData> getAllListData(String group, boolean getAll) throws DataNotFoundException;

    public void refreshAllListData();
}
