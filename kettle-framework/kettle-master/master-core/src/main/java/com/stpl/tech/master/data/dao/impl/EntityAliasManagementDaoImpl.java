/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.dao.EntityAliasManagementDao;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Repository
public class EntityAliasManagementDaoImpl extends AbstractMasterDaoImpl implements EntityAliasManagementDao {

    @Override
    public List<EntityAliasMappingData> getEntityAliasByIdandType(int entityId, String entityType) {
        Query query = manager.createQuery("FROM EntityAliasMappingData e WHERE e.entityId =:entityId and e.entityType =:entityType");
        query.setParameter("entityId", entityId);
        query.setParameter("entityType", entityType);
        return query.getResultList();
    }
}
