/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * UnitDetail generated by hbm2java
 */
@Entity
@Table(name = "OFFER_DETAIL_MAPPING_DATA"

)
public class OfferDetailMappingData implements java.io.Serializable {

	private static final long serialVersionUID = -821504026019701035L;

	private Integer offerDetailMappingId;
	private OfferDetailData offerDetail;
	private String mappingType;
	private String mappingValue;
	private String dimension;
	private String dataType;
	private String minValue;
	private int mappingGroup;
	private String status;

	public OfferDetailMappingData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "OFFER_DETAIL_MAPPING_DATA_ID", unique = true, nullable = false)
	public Integer getOfferDetailMappingId() {
		return this.offerDetailMappingId;
	}

	public void setOfferDetailMappingId(Integer offerDetailMappingId) {
		this.offerDetailMappingId = offerDetailMappingId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "OFFER_DETAIL_ID", nullable = false)
	public OfferDetailData getOfferDetail() {
		return offerDetail;
	}

	public void setOfferDetail(OfferDetailData offerDetail) {
		this.offerDetail = offerDetail;
	}

	@Column(name = "MAPPING_VALUE", nullable = false, length = 100)
	public String getMappingValue() {
		return this.mappingValue;
	}

	public void setMappingValue(String mappingValue) {
		this.mappingValue = mappingValue;
	}

	@Column(name = "MAPPING_TYPE", nullable = false, length = 50)
	public String getMappingType() {
		return this.mappingType;
	}

	public void setMappingType(String mappingType) {
		this.mappingType = mappingType;
	}

	@Column(name = "MAPPING_DATA_TYPE", nullable = false, length = 50)
	public String getDataType() {
		return dataType;
	}

	public void setDataType(String dataType) {
		this.dataType = dataType;
	}

	@Column(name = "MIN_VALUE", nullable = false, length = 10)
	public String getMinValue() {
		return minValue;
	}

	public void setMinValue(String minValue) {
		this.minValue = minValue;
	}

	@Column(name = "MAPPING_GROUP", nullable = false)
	public int getMappingGroup() {
		return mappingGroup;
	}

	public void setMappingGroup(int mappingGroup) {
		this.mappingGroup = mappingGroup;
	}

	@Column(name = "DIMENSION", nullable = true)
	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;

	}

}
