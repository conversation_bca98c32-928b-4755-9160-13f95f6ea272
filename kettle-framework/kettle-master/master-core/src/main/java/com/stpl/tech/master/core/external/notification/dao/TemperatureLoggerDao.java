package com.stpl.tech.master.core.external.notification.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.master.data.dao.AbstractMasterDao;
import com.stpl.tech.master.data.model.TemperatureLogDetail;

/**
 * Created by Chaayos on 21-09-2016.
 */
public interface TemperatureLoggerDao extends AbstractMasterDao {

	List<TemperatureLogDetail> getTemperatureDetail(String locationId, String deviceName, Date logTime);

	void markAsNotified(Integer id, boolean notified);

	List<TemperatureLogDetail> findAll(Date startTime, Date endTime);
}
