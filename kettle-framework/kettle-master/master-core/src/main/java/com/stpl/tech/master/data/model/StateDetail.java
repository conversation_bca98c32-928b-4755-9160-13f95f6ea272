package com.stpl.tech.master.data.model;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Created by Mohit
 */
@Entity
@Table(name = "STATE_DETAIL")
public class StateDetail {

	private Integer id;
	private String state;
	private String stateCode;
	private CountryDetail country;
	private String stateShortCode;
	private String isUt;
	private List<LocationDetail> locations = new ArrayList<LocationDetail>(0);
	private String functionalFlag;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "STATE_DETAIL_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "STATE", nullable = false, length = 100)
	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	@Column(name = "STATE_CODE", nullable = false, length = 10)
	public String getStateCode() {
		return stateCode;
	}

	public void setStateCode(String countryCode) {
		this.stateCode = countryCode;
	}

	@Column(name = "STATE_SHORT_CODE", nullable = false, length = 10)
	public String getStateShortCode() {
		return stateShortCode;
	}

	public void setStateShortCode(String stateShortCode) {
		this.stateShortCode = stateShortCode;
	}

	@Column(name = "IS_UT", nullable = false, length = 1)
	public String getIsUt() {
		return isUt;
	}

	public void setIsUt(String isUt) {
		this.isUt = isUt;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "COUNTRY_DETAIL_ID", nullable = false)
	public CountryDetail getCountry() {
		return country;
	}

	public void setCountry(CountryDetail country) {
		this.country = country;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "state")
	public List<LocationDetail> getLocations() {
		return locations;
	}

	public void setLocations(List<LocationDetail> locations) {
		this.locations = locations;
	}


	/**
	 * @return
	 */
	@Column(name = "FUNCTIONAL_FLAG", nullable = false, length = 1)
	public String getFunctionalFlag() {
		return functionalFlag;
	}

	public void setFunctionalFlag(String functionalFlag) {
		this.functionalFlag = functionalFlag;
	}

}
