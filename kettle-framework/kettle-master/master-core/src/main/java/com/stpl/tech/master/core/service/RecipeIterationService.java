package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.recipe.calculator.model.IterationIngredientInstructions;
import com.stpl.tech.master.recipe.calculator.model.RecipeIterationDetail;
import com.stpl.tech.master.recipe.calculator.model.UploadSCMRecipeMediaResponse;
import com.stpl.tech.util.EmailGenerationException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public interface RecipeIterationService {

	public RecipeIterationDetail addRecipeIteration(RecipeIterationDetail detail);

	public int updateRecipeIeration(RecipeIterationDetail detail);

	public boolean updateIterationImage(String id,MultipartFile [] file) throws DataNotFoundException;

	public List<RecipeIterationDetail> getIterationForProduct(int productId);

	public List<RecipeIterationDetail> getIterationForConstruct(String constructName);

	public boolean validateConstructName(String constructName);

	public boolean validateIterationName(String iterationName);

	public List<RecipeIterationDetail> getArchivedIterationForConstruct(String constructName);

	public List<RecipeIterationDetail> getArchivedIterationForProduct(int productId);

	public Map<String,Object> getAllSCMConstruct();

	public boolean changeIterationStatus(RecipeIterationDetail recipeIterationDetail) throws DataNotFoundException, EmailGenerationException, IllegalAccessException;

	public boolean changeIterationComment(RecipeIterationDetail recipeIterationDetail) throws DataNotFoundException;

	public boolean addIngredientInstructions(Set<String> instructions);

	public List<IterationIngredientInstructions> getAllIngredientInstructions();

    List<RecipeIterationDetail> getAllApprovedIterationForProducts(List<Integer> productIds);

	String checkRecipeForSmProducts(List<Integer> productIds);
}
