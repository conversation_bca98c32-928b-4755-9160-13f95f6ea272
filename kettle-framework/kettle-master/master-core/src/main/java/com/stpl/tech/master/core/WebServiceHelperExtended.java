package com.stpl.tech.master.core;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

public class WebServiceHelperExtended {

	private static final Logger LOG = LoggerFactory.getLogger(WebServiceHelper.class);

	public static <T> T exchangeWithAuth(String endpoint, String token, HttpMethod method, Class<T> clazz, String body,
			Map<String, ?> uriVariables) throws URISyntaxException {
		return exchangeWithAuth(endpoint, token, method, clazz, body, uriVariables, MediaType.APPLICATION_JSON_UTF8);
	}

	public static <T> T exchangeWithAuth(String endpoint, String token, HttpMethod method, Class<T> clazz, String body,
			Map<String, ?> uriVariables, MediaType type) throws URISyntaxException {
		try {
			RestTemplate restTemplate = new RestTemplate();
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Arrays.asList(type));
			// TODO Check this if something went wrong
			headers.setContentType(type);
			headers.set("auth-internal", token);
			HttpEntity<String> entity = new HttpEntity<String>(body, headers);
			if (uriVariables != null) {
				endpoint += "?";
				for (String key : uriVariables.keySet()) {
					endpoint = endpoint + key + "=" + uriVariables.get(key).toString() + "&";
				}
				endpoint = endpoint.substring(0, endpoint.length() - 1);
			}
			URI uri = new URI(endpoint);
			return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
		} catch (Exception e) {
			LOG.error("ERROR", e);
			throw e;
		}
	}

	public static <E> Collection<E> makeCollection(Iterable<E> iterable) {
		Collection<E> list = new ArrayList<E>();
		for (E item : iterable) {
			list.add(item);
		}
		return list;
	}

	public static <E> List<E> toList(Iterable<E> iterable) {
		List<E> list = new ArrayList<E>();
		for (E item : iterable) {
			list.add(item);
		}
		return list;
	}
}
