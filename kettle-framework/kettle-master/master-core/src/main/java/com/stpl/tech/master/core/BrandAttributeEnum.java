/*
 * SUN<PERSON><PERSON><PERSON>E TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core;

public enum BrandAttributeEnum {

    SEND_EMAIL('N'),
    SEND_WELCOME_MESSAGE('N'),
    AWARD_LOYALTY('N'),
    SEND_NPS('N'),
    FEEDBACK_URL(null),
    // feedback.url
    FEEDBACK_ENDPOINT_NPS_DELIVERY_ONLY_ORDER(null),
    // feedback.endpoint.nps.delivery.only.order
    SEND_FEEDBACK_MESSAGE_DELIVERY_SWIGGY('N'),
    // send.feedback.message.delivery.swiggy
    <PERSON>EED<PERSON>K_ENDPOINT_DINEIN(null),
    // feedback.endpoint.dinein
    FEEDBACK_ENDPOINT_DELIVERY(null),
    // feedback.endpoint.delivery
    FEEDBACK_ENDPOINT_LOW_RATING_DINEIN(null),
    // feedback.endpoint.low.rating.dinein
    FEEDBACK_ENDPOINT_LOW_RATING_DELIVERY(null),
    // feedback.endpoint.low.rating.delivery
    FEEDBACK_ENDPOINT_REDIRECT_URL(null),
    // feedback.endpoint.redirect.url
    FEEDBACK_ENDPOINT_NPS_CAFE(null),
    // feedback.endpoint.nps.cafe
    FEEDBACK_ENDPOINT_NPS_DELIVERY(null),
    // feedback.endpoint.nps.delivery
    VERIFICATION_EMAIL_TEMPLATE(null),
    SMS_ID(null),
    INTERNAL_ORDER_FEEDBACK_URL(null),

    //for brand ID 4 metaData
    MONK_IMAGE_LOGO(null),
    MONK_WELCOME_VIDEO(null),
    MONK_SCREEN_SAVER(null),
    MONK_BACKGROUND(null),
    CHAAYOS_SUBSCRIPTION(null),
    GOOGLE_ADS_CUSTOMER_ID(null),
    DELIVERY_ORDER_CANCELLATION_TIME(null),
    DINEIN_ORDER_CANCELLATION_TIME(null);

    Object defaultValue;

    BrandAttributeEnum() {
    }

    BrandAttributeEnum(Object defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }
}
