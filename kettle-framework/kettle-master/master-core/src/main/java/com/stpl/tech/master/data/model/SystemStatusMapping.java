package com.stpl.tech.master.data.model;

import com.stpl.tech.master.domain.model.SystemStatus;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "SYSTEM_STATUS_MAPPING")
@NoArgsConstructor
@AllArgsConstructor
public class SystemStatusMapping implements java.io.Serializable {

    private Integer mappingId;
    private String system1;
    private String system2;
    private String systemStatus;
    private String lastActivityTime;


    public SystemStatusMapping(String system1, String system2, String systemStatus, String lastActivityTime) {
        this.system1 = system1;
        this.system2 = system2;
        this.systemStatus = systemStatus;
        this.lastActivityTime = lastActivityTime;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MAPPING_ID", unique = true, nullable = false)
    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    @Column(name = "SYSTEM_1")
    public String getSystem1() {
        return system1;
    }

    public void setSystem1(String system1) {
        this.system1 = system1;
    }

    @Column(name = "SYSTEM_2")
    public String getSystem2() {
        return system2;
    }

    public void setSystem2(String system2) {
        this.system2 = system2;
    }

    @Column(name = "STATUS")
    public String getSystemStatus() {
        return systemStatus;
    }

    public void setSystemStatus(String systemStatus) {
        this.systemStatus = systemStatus;
    }

    @Column(name="LAST_ACTIVITY_TIME")
    public String getLastActivityTime() {
        return lastActivityTime;
    }

    public void setLastActivityTime(String lastActivityTime) {
        this.lastActivityTime = lastActivityTime;
    }
}
