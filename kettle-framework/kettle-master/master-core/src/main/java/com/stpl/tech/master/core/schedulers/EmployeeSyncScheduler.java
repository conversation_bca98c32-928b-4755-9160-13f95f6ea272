package com.stpl.tech.master.core.schedulers;

import com.stpl.tech.master.core.service.EmployeeManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class EmployeeSyncScheduler {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeSyncScheduler.class);

    @Autowired
    private EmployeeManagementService employeeManagementService;

    @Scheduled(cron = "0 0 11 * * *", zone = "GMT+05:30")
    public Integer syncEmployeeData() {
        LOG.info("Starting employee sync scheduler:::");
        return employeeManagementService.syncEmployeeData();
    }
}
