/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.TaxMappingDao;
import com.stpl.tech.master.data.model.CategoryAdditionalTaxData;
import com.stpl.tech.master.data.model.CategoryTaxData;
import com.stpl.tech.master.data.model.CountryDetail;
import com.stpl.tech.master.data.model.StateDetail;
import com.stpl.tech.master.data.model.TaxCategoryData;
import com.stpl.tech.master.data.model.TaxProfileData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.tax.model.AdditionalTax;
import com.stpl.tech.master.tax.model.CategoryAdditionalTax;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.StateTax;
import com.stpl.tech.master.tax.model.TaxCategory;
import com.stpl.tech.master.tax.model.TaxInfo;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Repository
public class TaxMappingDaoImpl extends AbstractMasterDaoImpl implements TaxMappingDao {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.master.data.dao.TaxMappingDao#addCategoryData(com.stpl.tech
	 * .master.tax.model.ProductCategory)
	 */
	@Override
	public TaxCategory addCategoryData(TaxCategory category) {
		TaxCategoryData data = new TaxCategoryData();
		data.setCategoryCode(category.getCode());
		data.setCategoryDescription(category.getDesc());
		data.setCategoryInternalDescription(category.getIntDesc());
		data.setCategoryStatus(AppConstants.IN_ACTIVE);
		data.setExempted(AppConstants.getValue(category.isExempted()));
		manager.persist(data);
		manager.flush();
		return MasterDataConverter.convert(data);
	}

	@Override
	public TaxCategory updateCategoryData(TaxCategory category) {
		TaxCategoryData data = manager.find(TaxCategoryData.class, category.getId());
		if (data != null) {
			data.setCategoryCode(category.getCode());
			data.setCategoryDescription(category.getDesc());
			data.setCategoryInternalDescription(category.getIntDesc());
			data.setCategoryStatus(category.getStatus());
			data.setExempted(AppConstants.getValue(category.isExempted()));
			manager.flush();
			return MasterDataConverter.convert(data);
		}
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.data.dao.TaxMappingDao#getAllCategory()
	 */
	@Override
	public List<TaxCategory> getAllCategory() {
		List<TaxCategory> all = new ArrayList<>();
		try {
			Query query = manager.createQuery("FROM TaxCategoryData");
			List<TaxCategoryData> list = query.getResultList();
			list.stream().forEach((record) -> {
				all.add(MasterDataConverter.convert(record));
			});
		} catch (NoResultException e) {
		}
		return all;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.data.dao.TaxMappingDao#getAllCategory()
	 */
	@Override
	public List<IdCodeName> getAllCategoryBasicInfo() {
		List<IdCodeName> all = new ArrayList<>();
		try {
			Query query = manager.createQuery("FROM TaxCategoryData");
			List<TaxCategoryData> list = query.getResultList();
			list.stream().forEach((record) -> {
				all.add(MasterDataConverter.convertToIdCodeName(record));
			});
		} catch (NoResultException e) {
		}
		return all;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.master.data.dao.TaxMappingDao#addTaxData(com.stpl.tech.
	 * master.tax.model.TaxInfo)
	 */
	@Override
	public boolean addTaxData(TaxInfo info) {
		TaxProfileData data = new TaxProfileData();
		data.setApplicableOn(info.getApplicability().name());
		data.setTaxCode(info.getCode());
		data.setTaxDataStatus(info.getStatus());
		data.setTaxDescription(info.getDesc());
		data.setTaxInternalDescription(info.getIntDesc());
		data.setTaxName(info.getName());
		manager.persist(data);
		manager.flush();
		return true;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.data.dao.TaxMappingDao#getAllTaxes()
	 */
	@Override
	public List<TaxInfo> getAllTaxes() {
		List<TaxInfo> all = new ArrayList<>();
		try {
			Query query = manager.createQuery("FROM TaxProfileData");
			List<TaxProfileData> list = query.getResultList();
			list.stream().forEach((record) -> {
				all.add(MasterDataConverter.convert(record));
			});
		} catch (NoResultException e) {
		}
		return all;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.data.dao.TaxMappingDao#getAllTaxes()
	 */
	@Override
	public List<IdCodeName> getAllTaxesBasicInfo() {
		List<IdCodeName> all = new ArrayList<>();
		try {
			Query query = manager.createQuery("FROM TaxProfileData");
			List<TaxProfileData> list = query.getResultList();
			list.stream().forEach((record) -> {
				all.add(MasterDataConverter.convertToIdCodeName(record));
			});
		} catch (NoResultException e) {
		}
		return all;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.master.data.dao.TaxMappingDao#updateCategoryTax(com.stpl.
	 * tech.master.tax.model.CategoryTax)
	 */
	@Override
	public CategoryTax updateCategoryTax(CategoryTax categoryTax) {
		TaxCategoryData categoryData = manager.find(TaxCategoryData.class, categoryTax.getCategory().getId());
		TaxProfileData taxData = manager.find(TaxProfileData.class, categoryTax.getTaxType().getId());
		CountryDetail countryData = manager.find(CountryDetail.class, categoryTax.getCountry().getId());

		categoryTax.getTaxes().stream().forEach((record) -> {
			if (record.getKeyId() != null) {
				lookupAndUpdate(categoryTax, record);
			} else {
				create(categoryTax, countryData, categoryData, taxData, record);
			}
		});
		manager.flush();
		return fetchCategoryTax(countryData.getId(), categoryTax.getCategory().getId(),
				categoryTax.getTaxType().getId(), true);
	}

	/**
	 * @param countryData
	 * @param categoryData
	 * @param taxData
	 * @param record
	 */
	private void create(CategoryTax categoryTax, CountryDetail countryData, TaxCategoryData categoryData,
			TaxProfileData taxProfile, StateTax record) {
		CategoryTaxData taxData = new CategoryTaxData();
		taxData.setCategoryData(categoryData);
		taxData.setCgstTaxRate(record.getCgst());
		taxData.setIgstTaxRate(record.getIgst());
		taxData.setSgstTaxRate(record.getSgst());
		taxData.setCountry(countryData);
		taxData.setCreatedAt(AppUtils.getCurrentTimestamp());
		taxData.setCreatedBy(getName(categoryTax.getEmployeeName(), categoryTax.getEmployeeId()));
		taxData.setStartDate(AppUtils.getCurrentTimestamp());
		taxData.setState(manager.find(StateDetail.class, record.getState().getId()));
		taxData.setStatus(AppConstants.ACTIVE);
		taxData.setTaxData(taxProfile);
		manager.persist(taxData);
	}

	/**
	 * @param record
	 */
	private void lookupAndUpdate(CategoryTax categoryTax, StateTax record) {
		CategoryTaxData data = manager.find(CategoryTaxData.class, record.getKeyId());
		if (data.getCgstTaxRate() != record.getCgst() || data.getIgstTaxRate() != record.getIgst()
				|| data.getSgstTaxRate() != record.getSgst()) {
			data.setIgstTaxRate(record.getIgst());
			data.setCgstTaxRate(record.getCgst());
			data.setSgstTaxRate(record.getSgst());
			data.setUpdatedAt(AppUtils.getCurrentTimestamp());
			data.setUpdatedBy(getName(categoryTax.getEmployeeName(), categoryTax.getEmployeeId()));
			data.setStartDate(record.getDate());
			manager.flush();
		}
	}

	private String getName(String name, int id) {
		return name + " [" + id + "]";
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.data.dao.TaxMappingDao#fetchCategoryTax(int,
	 * int)
	 */
	@Override
	public CategoryTax fetchCategoryTax(int countryId, int categoryId, int taxId, boolean getForAllStates) {
		CategoryTax data = new CategoryTax();
		data.setCategory(MasterDataConverter.convertToIdCodeName(manager.find(TaxCategoryData.class, categoryId)));
		data.setTaxType(MasterDataConverter.convertToIdCodeName(manager.find(TaxProfileData.class, taxId)));
		data.setCountry(MasterDataConverter.convertToIdCodeName(manager.find(CountryDetail.class, countryId)));
		try {
			Query query = manager.createQuery(
					"FROM CategoryTaxData C WHERE C.categoryData.taxCategoryDataId = :categoryId AND C.taxData.taxDataId = :taxId AND C.country.id = :countryId");
			query.setParameter("categoryId", categoryId);
			query.setParameter("taxId", taxId);
			query.setParameter("countryId", countryId);
			List<CategoryTaxData> list = query.getResultList();
			List<Integer> stateIds = new ArrayList<Integer>();
			list.stream().forEach((record) -> {
				stateIds.add(record.getState().getId());
				data.getTaxes().add(MasterDataConverter.convert(record));
			});

			if (getForAllStates) {
				List<StateDetail> stateDetailList = getOtherStates(stateIds, countryId);
				if (stateDetailList != null && !stateDetailList.isEmpty()) {
					for (StateDetail s : stateDetailList) {
						StateTax tax = new StateTax();
						tax.setState(MasterDataConverter.convertToIdCodeName(s));
						data.getTaxes().add(tax);
					}
				}
			}

		} catch (NoResultException e) {

		}
		return data;
	}

	private List<StateDetail> getOtherStates(List<Integer> stateIds, int countryId) {
		Query stateQuery = null;
		if (stateIds != null && !stateIds.isEmpty()) {
			stateQuery = manager
					.createQuery("FROM StateDetail C WHERE C.country.id = :countryId AND C.id NOT IN :stateIds");
			stateQuery.setParameter("stateIds", stateIds);
			stateQuery.setParameter("countryId", countryId);
		} else {
			stateQuery = manager.createQuery("FROM StateDetail C WHERE C.country.id = :countryId ");
			stateQuery.setParameter("countryId", countryId);
		}

		return stateQuery.getResultList();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.master.data.dao.TaxMappingDao#updateCategoryTax(com.stpl.
	 * tech.master.tax.model.CategoryTax)
	 */
	@Override
	public CategoryAdditionalTax updateCategoryAdditionalTax(CategoryAdditionalTax categoryTax) {
		TaxCategoryData categoryData = manager.find(TaxCategoryData.class, categoryTax.getCategory().getId());
		TaxProfileData taxData = manager.find(TaxProfileData.class, categoryTax.getTaxType().getId());
		CountryDetail countryData = manager.find(CountryDetail.class, categoryTax.getCountry().getId());

		categoryTax.getTaxes().stream().forEach((record) -> {
			if (record.getKeyId() != null) {
				lookupAndUpdate(categoryTax, record);
			} else {
				create(categoryTax, countryData, categoryData, taxData, record);
			}
		});
		manager.flush();
		return fetchCategoryAdditionalTax(categoryTax.getCountry().getId(), categoryTax.getCategory().getId(),
				categoryTax.getTaxType().getId());
	}

	/**
	 * @param countryData
	 * @param categoryData
	 * @param taxData
	 * @param record
	 */
	private void create(CategoryAdditionalTax categoryTax, CountryDetail countryData, TaxCategoryData categoryData,
			TaxProfileData taxProfile, AdditionalTax record) {
		CategoryAdditionalTaxData taxData = new CategoryAdditionalTaxData();
		taxData.setCategoryData(categoryData);
		taxData.setTaxRate(record.getTax());
		taxData.setCountry(countryData);
		taxData.setCreatedAt(AppUtils.getCurrentTimestamp());
		taxData.setCreatedBy(getName(categoryTax.getEmployeeName(), categoryTax.getEmployeeId()));
		taxData.setStartDate(record.getDate());
		taxData.setState(manager.find(StateDetail.class, record.getState().getId()));
		taxData.setStatus(AppConstants.ACTIVE);
		taxData.setTaxData(taxProfile);
		taxData.setStartDate(AppUtils.getCurrentTimestamp());
		manager.persist(taxData);
	}

	/**
	 * @param record
	 */
	private void lookupAndUpdate(CategoryAdditionalTax categoryTax, AdditionalTax record) {
		CategoryAdditionalTaxData data = manager.find(CategoryAdditionalTaxData.class, record.getKeyId());
		if (data.getTaxRate() != record.getTax()) {
			data.setTaxRate(record.getTax());
			data.setUpdatedAt(AppUtils.getCurrentTimestamp());
			data.setUpdatedBy(getName(categoryTax.getEmployeeName(), categoryTax.getEmployeeId()));
			data.setStartDate(AppUtils.getCurrentTimestamp());
			manager.flush();
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.data.dao.TaxMappingDao#fetchCategoryTax(int,
	 * int)
	 */
	@Override
	public CategoryAdditionalTax fetchCategoryAdditionalTax(int countryId, int categoryId, int taxId) {
		CategoryAdditionalTax data = new CategoryAdditionalTax();
		data.setCategory(MasterDataConverter.convertToIdCodeName(manager.find(TaxCategoryData.class, categoryId)));
		data.setTaxType(MasterDataConverter.convertToIdCodeName(manager.find(TaxProfileData.class, taxId)));
		data.setCountry(MasterDataConverter.convertToIdCodeName(manager.find(CountryDetail.class, countryId)));

		try {
			Query query = manager.createQuery(
					"FROM CategoryAdditionalTaxData C WHERE C.country.id = :countryId AND C.categoryData.taxCategoryDataId = :categoryId AND C.taxData.taxDataId = :taxId");
			query.setParameter("categoryId", categoryId);
			query.setParameter("taxId", taxId);
			query.setParameter("countryId", countryId);
			List<CategoryAdditionalTaxData> list = query.getResultList();
			List<Integer> stateIds = new ArrayList<Integer>();
			list.stream().forEach((record) -> {
				stateIds.add(record.getState().getId());
				data.getTaxes().add(MasterDataConverter.convert(record));
			});

			List<StateDetail> stateDetailList = getOtherStates(stateIds, countryId);
			if (stateDetailList != null && !stateDetailList.isEmpty()) {
				for (StateDetail s : stateDetailList) {
					AdditionalTax tax = new AdditionalTax();
					tax.setState(MasterDataConverter.convertToIdCodeName(s));
					data.getTaxes().add(tax);
				}
			}
		} catch (NoResultException e) {

		}
		return data;
	}

	@Override
	public TaxCategory getTaxCategory(int id) {
		TaxCategoryData data = manager.find(TaxCategoryData.class, id);
		if (data != null) {
			return MasterDataConverter.convert(data);
		}
		return null;
	}

	@Override
	public List<IdCodeName> getAllCountries() {
		List<IdCodeName> countryList = new ArrayList<>();
		Query query = manager.createQuery("FROM CountryDetail C");
		List<CountryDetail> list = query.getResultList();
		if (list != null && !list.isEmpty()) {
			for (CountryDetail c : list) {
				countryList.add(MasterDataConverter.convertToIdCodeName(c));
			}
		}
		return countryList;
	}

	@Override
	public List<CategoryTax> fetchAllCategoryTax() {
		Map<String, CategoryTax> taxMap = new HashMap<>();
		try {
			Query query = manager.createQuery("FROM CategoryTaxData C");
			List<CategoryTaxData> list = query.getResultList();
			list.stream().forEach((record) -> {
				addToTaxMap(record, taxMap);
			});
		} catch (NoResultException e) {
		}
		return new ArrayList<CategoryTax>(taxMap.values());
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.master.data.dao.TaxMappingDao#fetchAllCategoryTax(int,
	 * int)
	 */
	@Override
	public CategoryTax fetchAllCategoryTax(int countryId, int categoryId) {
		Map<String, CategoryTax> taxMap = new HashMap<>();
		try {
			Query query = manager.createQuery(
					"FROM CategoryTaxData C where C.categoryData.taxCategoryDataId = :categoryId and C.country.id = :countryId");
			query.setParameter("countryId", countryId);
			query.setParameter("categoryId", categoryId);
			List<CategoryTaxData> list = query.getResultList();
			if (list != null) {
				list.stream().forEach((record) -> {
					addToTaxMap(record, taxMap);
				});
			}
		} catch (NoResultException e) {
			return null;
		}
		for(String key : taxMap.keySet()){
			return taxMap.get(key);
		}
		return null;
	}

	private void addToTaxMap(CategoryTaxData record, Map<String, CategoryTax> taxMap) {
		CategoryTax data = taxMap.get(record.getCategoryData().getCategoryCode());
		if (data == null) {
			data = new CategoryTax();
			data.setCategory(MasterDataConverter.convertToIdCodeName(record.getCategoryData()));
			data.setTaxType(MasterDataConverter.convertToIdCodeName(record.getTaxData()));
			data.setCountry(MasterDataConverter.convertToIdCodeName(record.getCountry()));
		}
		data.getTaxes().add(MasterDataConverter.convert(record));
		taxMap.put(record.getCategoryData().getCategoryCode(), data);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.master.data.dao.TaxMappingDao#fetchAllCategoryAdditionalTax
	 * ()
	 */
	@Override
	public Map<String, CategoryAdditionalTax> fetchAllCategoryAdditionalTax() {
		Map<String, CategoryAdditionalTax> taxMap = new HashMap<>();
		try {
			Query query = manager.createQuery("FROM CategoryAdditionalTaxData C");
			List<CategoryAdditionalTaxData> list = query.getResultList();
			list.stream().forEach((record) -> {
				addToTaxMap(record, taxMap);
			});
		} catch (NoResultException e) {
		}
		return taxMap;
	}

	@Override
	public CategoryAdditionalTax fetchCategoryAdditionalTax(int countryId, int categoryId) {
		Map<String, CategoryAdditionalTax> taxMap = new HashMap<>();
		try {
			Query query = manager.createQuery(
					"FROM CategoryAdditionalTaxData C where C.categoryData.taxCategoryDataId = :categoryId and C.country.id = :countryId");
			query.setParameter("countryId", countryId);
			query.setParameter("categoryId", categoryId);
			List<CategoryAdditionalTaxData> list = query.getResultList();
			if (list == null) {
				return null;
			}
			list.stream().forEach((record) -> {
				addToTaxMap(record, taxMap);
			});
		} catch (NoResultException e) {
			return null;
		}
		for (String key : taxMap.keySet()) {
			return taxMap.get(key);
		}
		return null;
	}

	private void addToTaxMap(CategoryAdditionalTaxData record, Map<String, CategoryAdditionalTax> taxMap) {
		CategoryAdditionalTax data = taxMap.get(record.getCategoryData().getCategoryCode());
		if (data == null) {
			data = new CategoryAdditionalTax();
			data.setCategory(MasterDataConverter.convertToIdCodeName(record.getCategoryData()));
			data.setTaxType(MasterDataConverter.convertToIdCodeName(record.getTaxData()));
			data.setCountry(MasterDataConverter.convertToIdCodeName(record.getCountry()));
		}
		data.getTaxes().add(MasterDataConverter.convert(record));
		taxMap.put(record.getCategoryData().getCategoryCode(), data);
	}

}