package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "PRODUCT_NUTRITION_DETAIL")
public class ProductNutritionDetail {
    private Integer productNutritionDetailId;
    private Integer productId;
    private BigDecimal calorieCount;
    private BigDecimal proteinCount;
    private BigDecimal fatCount;
    private BigDecimal carbohydrateCount;
    private BigDecimal fiberCount;

    private String sourceType;

    public ProductNutritionDetail() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PRODUCT_NUTRITION_DETAIL_ID")
    public Integer getProductNutritionDetailId() {
        return productNutritionDetailId;
    }

    public void setProductNutritionDetailId(Integer productNutritionDetailId) {
        this.productNutritionDetailId = productNutritionDetailId;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "CALORIE_COUNT")
    public BigDecimal getCalorieCount() {
        return calorieCount;
    }

    public void setCalorieCount(BigDecimal calorieCount) {
        this.calorieCount = calorieCount;
    }

    @Column(name = "PROTEIN_COUNT")
    public BigDecimal getProteinCount() {
        return proteinCount;
    }

    public void setProteinCount(BigDecimal proteinCount) {
        this.proteinCount = proteinCount;
    }

    @Column(name = "FAT_COUNT")
    public BigDecimal getFatCount() {
        return fatCount;
    }

    public void setFatCount(BigDecimal fatCount) {
        this.fatCount = fatCount;
    }

    @Column(name = "CARBOHYDRATE_COUNT")
    public BigDecimal getCarbohydrateCount() {
        return carbohydrateCount;
    }

    public void setCarbohydrateCount(BigDecimal carbohydrateCount) {
        this.carbohydrateCount = carbohydrateCount;
    }

    @Column(name = "FIBER_COUNT")
    public BigDecimal getFiberCount() {
        return fiberCount;
    }

    public void setFiberCount(BigDecimal fiberCount) {
        this.fiberCount = fiberCount;
    }

    @Column(name = "SOURCE_TYPE")
    public String getSourceType() { return sourceType; }

    public void setSourceType(String sourceType) { this.sourceType = sourceType; }
}
