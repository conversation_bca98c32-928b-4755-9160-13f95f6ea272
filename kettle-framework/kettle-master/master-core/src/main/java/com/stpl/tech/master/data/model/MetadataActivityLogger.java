/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Type;

@Entity
@Table(name = "METADATA_ACTIVITY_LOGGER")
public class MetadataActivityLogger implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1607916313663896920L;
	private Integer metadataActivityLoggerId;
	private String keyType;
	private String keyId;
	private String activityType;
	private Date activityTime;
	private String activityUserName;
	private int activityUserId;
	private String activityLog;

	public MetadataActivityLogger() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "METADATA_ACTIVITY_LOGGER_ID", unique = true, nullable = false)
	public Integer getMetadataActivityLoggerId() {
		return this.metadataActivityLoggerId;
	}

	public void setMetadataActivityLoggerId(Integer metadataActivityLoggerId) {
		this.metadataActivityLoggerId = metadataActivityLoggerId;
	}

	@Column(name = "KEY_TYPE", nullable = false, length = 200)
	public String getKeyType() {
		return this.keyType;
	}

	public void setKeyType(String keyType) {
		this.keyType = keyType;
	}

	@Column(name = "KEY_ID")
	public String getKeyId() {
		return this.keyId;
	}

	public void setKeyId(String keyId) {
		this.keyId = keyId;
	}

	@Column(name = "ACTIVITY_TYPE", nullable = false, length = 30)
	public String getActivityType() {
		return this.activityType;
	}

	public void setActivityType(String activityType) {
		this.activityType = activityType;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ACTIVITY_TIME", nullable = true, length = 19)
	public Date getActivityTime() {
		return activityTime;
	}

	public void setActivityTime(Date activityTime) {
		this.activityTime = activityTime;
	}

	@Column(name = "ACTIVITY_USER_ID", nullable = false)
	public int getActivityUserId() {
		return activityUserId;
	}

	public void setActivityUserId(int activityUserId) {
		this.activityUserId = activityUserId;
	}

	@Column(name = "ACTIVITY_LOG", nullable = false)
	@Type(type="text")
	public String getActivityLog() {
		return activityLog;
	}

	public void setActivityLog(String activityLog) {
		this.activityLog = activityLog;
	}

	@Override
	public String toString() {
		return "MetadataActivityLogger [keyType=" + keyType + ", keyId=" + keyId + ", activityType=" + activityType
				+ ", activityTime=" + activityTime + ", activityUserName=" + activityUserName + ", activityUserId="
				+ activityUserId + ", activityLog=" + activityLog + "]";
	}

}
