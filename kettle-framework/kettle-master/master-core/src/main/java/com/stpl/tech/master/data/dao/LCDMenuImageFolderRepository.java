package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.LCDMenuImageFolder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface LCDMenuImageFolderRepository extends JpaRepository<LCDMenuImageFolder, Long> {


        List<LCDMenuImageFolder> findByStepAndStatus(String step, String status);

        Optional<LCDMenuImageFolder> findByStepAndName(String step, String name);

        List<LCDMenuImageFolder> findByStep(String step);
}

