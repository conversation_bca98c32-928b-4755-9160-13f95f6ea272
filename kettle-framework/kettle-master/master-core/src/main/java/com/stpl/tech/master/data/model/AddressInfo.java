/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * AddressInfo generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ADDRESS_INFO")
public class AddressInfo implements java.io.Serializable {

	private Integer addressId;
	private String addressLine1;
	private String addressLine2;
	private String addressLine3;
	private String city;
	private String state;
	private String country;
	private String zipcode;
	private String contactNum1;
	private String contactNum2;
	private String addressType;
	private String latitude;
	private String longitude;

	public AddressInfo() {
	}

	public AddressInfo(String addressLine1, String city, String state, String country, String zipcode,
			String contactNum1, String addressType) {
		this.addressLine1 = addressLine1;
		this.city = city;
		this.state = state;
		this.country = country;
		this.zipcode = zipcode;
		this.contactNum1 = contactNum1;
		this.addressType = addressType;
	}

	public AddressInfo(String addressLine1, String addressLine2, String addressLine3, String city, String state,
			String country, String zipcode, String contactNum1, String contactNum2, String addressType, String latitude,
			String longitude) {
		this.addressLine1 = addressLine1;
		this.addressLine2 = addressLine2;
		this.addressLine3 = addressLine3;
		this.city = city;
		this.state = state;
		this.country = country;
		this.zipcode = zipcode;
		this.contactNum1 = contactNum1;
		this.contactNum2 = contactNum2;
		this.addressType = addressType;
		this.latitude = latitude;
		this.longitude = longitude;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ADDRESS_ID", unique = true, nullable = false)
	public Integer getAddressId() {
		return this.addressId;
	}

	public void setAddressId(Integer addressId) {
		this.addressId = addressId;
	}

	@Column(name = "ADDRESS_LINE_1", nullable = false)
	public String getAddressLine1() {
		return this.addressLine1;
	}

	public void setAddressLine1(String addressLine1) {
		this.addressLine1 = addressLine1;
	}

	@Column(name = "ADDRESS_LINE_2")
	public String getAddressLine2() {
		return this.addressLine2;
	}

	public void setAddressLine2(String addressLine2) {
		this.addressLine2 = addressLine2;
	}

	@Column(name = "ADDRESS_LINE_3")
	public String getAddressLine3() {
		return this.addressLine3;
	}

	public void setAddressLine3(String addressLine3) {
		this.addressLine3 = addressLine3;
	}

	@Column(name = "CITY", nullable = false, length = 128)
	public String getCity() {
		return this.city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	@Column(name = "STATE", nullable = false, length = 128)
	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	@Column(name = "COUNTRY", nullable = false, length = 128)
	public String getCountry() {
		return this.country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	@Column(name = "ZIPCODE", nullable = false, length = 40)
	public String getZipcode() {
		return this.zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	@Column(name = "CONTACT_NUM_1", length = 32)
	public String getContactNum1() {
		return this.contactNum1;
	}

	public void setContactNum1(String contactNum1) {
		this.contactNum1 = contactNum1;
	}

	@Column(name = "CONTACT_NUM_2", length = 32)
	public String getContactNum2() {
		return this.contactNum2;
	}

	public void setContactNum2(String contactNum2) {
		this.contactNum2 = contactNum2;
	}

	@Column(name = "ADDRESS_TYPE", nullable = false, length = 50)
	public String getAddressType() {
		return this.addressType;
	}

	public void setAddressType(String addressType) {
		this.addressType = addressType;
	}

	@Column(name = "LATITUDE", nullable = true, length = 15)
	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	@Column(name = "LONGITUDE", nullable = true, length = 15)
	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

}
