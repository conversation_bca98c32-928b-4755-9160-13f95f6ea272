/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.util;

import com.google.common.io.Files;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.AddonData;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.spring.service.util.SpringBeanProvider;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.servlet.View;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.zip.Adler32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;



public class MasterUtil {
	private static final MasterDataCache masterDataCache = SpringBeanProvider.getBean(MasterDataCache.class);

	private static final Logger LOG = LoggerFactory.getLogger(MasterUtil.class);

	public static AddonData getDummyAddon(int id) {
		AddonData addOn = new AddonData();
		addOn.setId(id);
		addOn.setName("DUMMY");
		addOn.setCode("DUMMY");
		return addOn;
	}

	public static final String getUserIpAddress(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null) {
			ipAddress = request.getRemoteAddr();
		}
		return ipAddress;
	}

	public static boolean isCODOrder(String orderSource) {
		return orderSource != null && orderSource.equals(UnitCategory.COD.name());
	}

	public static boolean isCODUnit(UnitCategory categoty) {
		return categoty != null && categoty.equals(UnitCategory.DELIVERY);
	}

	public static boolean isMRP(BillType billType) {
		return billType != null && BillType.MRP.equals(billType);
	}

	public static boolean isNetPrice(BillType billType) {
		return billType != null && BillType.NET_PRICE.equals(billType);
	}

	public static boolean isActiveUnit(UnitStatus status) {
		return status != null && (UnitStatus.ACTIVE.equals(status));
	}

	public static String getPromotionalCode(Unit unit) {

		if (AppUtils.getCurrentDate().before(AppUtils.getStartOfMonth(116, 1))) {
			switch (unit.getRegion()) {
			case "NCR":
				return "Get 25% off on all Chaayos deliveries. Call 011-32551234";
			case "MUMBAI":
				return "Get 25% off on all Chaayos deliveries. Call 022-32581234";
			default:
				break;
			}
			return null;
		} else {
			return null;
		}
	}

	public static boolean isTakeawayOrder(String orderSource) {
		return orderSource != null && orderSource.equals(UnitCategory.TAKE_AWAY.name());
	}

	public static AttachmentData compress(String zipFileName, String tempDir, List<AttachmentData> attachments)
			throws IOException {
		AttachmentData data = new AttachmentData(zipFileName, AppConstants.ZIP_MIME_TYPE);
		ByteArrayOutputStream f = new ByteArrayOutputStream();
		CheckedOutputStream csum = new CheckedOutputStream(f, new Adler32());
		ZipOutputStream zos = new ZipOutputStream(csum);
		List<File> inputFiles = new ArrayList<>();
		for (AttachmentData attachment : attachments) {
			String inputFileName = tempDir + attachment.getFileName().replace(" ", "-").replace("/", "-")
					+ attachment.getFileExtension();
			inputFileName = inputFileName.replace(" ", "-");
			File inputFile = new File(inputFileName);
			if (!inputFile.getParentFile().exists()) {
				inputFile.getParentFile().mkdirs();
			}
			Files.write(attachment.getAttachment(), inputFile);
			FileInputStream fis = new FileInputStream(inputFile);

			zos.putNextEntry(new ZipEntry(
					attachment.getFileName().replace(" ", "-").replace("/", "-") + attachment.getFileExtension()));
			byte[] buffer = new byte[4092];
			int byteCount = 0;
			while ((byteCount = fis.read(buffer)) != -1) {
				zos.write(buffer, 0, byteCount);
				System.out.print('.');
				System.out.flush();
			}
			fis.close();
			zos.closeEntry();
			inputFiles.add(inputFile);

		}
		zos.flush();
		zos.close();
		data.setAttachment(f.toByteArray());
		if (inputFiles != null && inputFiles.size() > 0) {
			for (File inputFile : inputFiles) {
				try {
					inputFile.delete();
				} catch (Exception e) {
					LOG.error("Error while deleting temporary file : " + inputFile.getName(), e);
				}
			}
		}

		return data;
	}

	public static void main(String[] args) throws IOException {
		AttachmentData data1 = new AttachmentData("FirstFile".getBytes(),
				"First File" + AppUtils.getCurrentTimeISTStringWithNoColons(), AppConstants.CSV_MIME_TYPE);
		AttachmentData data2 = new AttachmentData("SecondFile".getBytes(),
				"Second File" + AppUtils.getCurrentTimeISTStringWithNoColons(), AppConstants.CSV_MIME_TYPE);
		AttachmentData data3 = new AttachmentData("ThirdFile".getBytes(),
				"Third File" + AppUtils.getCurrentTimeISTStringWithNoColons(), AppConstants.CSV_MIME_TYPE);
		List<AttachmentData> list = new ArrayList<>();
		list.add(data1);
		list.add(data2);
		list.add(data3);
		AttachmentData data = compress("ZipFile", "E:/data/tmp/", list);
		Files.write(data.getAttachment(),
				new File("E:/data/FinalFile" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".zip"));
	}

	public static boolean isActiveCafe(UnitBasicDetail u) {
		return isActiveUnit(u.getStatus()) && isCafe(u.getCategory());
	}

	private static boolean isCafe(UnitCategory category) {
		return UnitCategory.CAFE.equals(category);
	}

	public static Style getHeaderStyle(WorkbookContext workbookContext) {
		Style style = new Style() {
			@Override
			public void enrich(WorkbookContext workbookContext, CellStyle cellStyle) {
				Font font = workbookContext.toNativeWorkbook().createFont();
				font.setFontHeightInPoints((short) 10);
				font.setBold(true);
				cellStyle.setFont(font);
				cellStyle.setAlignment(HorizontalAlignment.forInt(1));
				cellStyle.setVerticalAlignment(VerticalAlignment.forInt(2));
			}
		};
		style.enrich(workbookContext,getWorkbookStyle(workbookContext.toNativeWorkbook()));
		return style;
	}

	public static CellStyle getWorkbookStyle(Workbook workbook) {
		CellStyle headerStyle = workbook.createCellStyle();
		Font font = workbook.createFont();
		font.setFontHeightInPoints((short) 10);
		font.setBold(true);
		headerStyle.setFont(font);
		headerStyle.setAlignment(HorizontalAlignment.forInt(1));
		headerStyle.setVerticalAlignment(VerticalAlignment.forInt(2));
		return headerStyle;
	}

	public static List<MenuType> getAllDaySlots(){
		return new ArrayList<>(Arrays.asList(MenuType.DAY_SLOT_BREAKFAST,MenuType.DAY_SLOT_LUNCH,MenuType.DAY_SLOT_EVENING,MenuType.DAY_SLOT_DINNER
				,MenuType.DAY_SLOT_POST_DINNER,MenuType.DAY_SLOT_OVERNIGHT, MenuType.DEFAULT));
	}

	public static String isHotBeverage(Product product){
		if (!AppConstants.HOT_BEVERAGE_PRODUCT_TYPE.equals(product.getType()) || !(Objects.isNull(product.getStationCategoryName())
				|| AppConstants.HOT_BEVERAGE_STATION_CATEGORY_ID.equals(product.getStationCategory()))) {
			return AppConstants.NOT_APPLICABLE;
		}
		return AppConstants.YES;
	}

	public static List<Integer> getHotBeverageProductIds(Map<Integer, Product> productDetails ) {
		List<Integer> hotBeverageProductIds = new ArrayList<>();

		for (Map.Entry<Integer, Product> entry : productDetails.entrySet()) {
			Product product = entry.getValue();
			String result = isHotBeverage(product);

			if (AppConstants.YES.equals(result)) {
				hotBeverageProductIds.add(entry.getKey());
			}
		}
		return hotBeverageProductIds;
	}


	public static List<Integer> getMappedBrands() {
		Integer brandId = RequestContext.getBrandId();
		Integer companyId = RequestContext.getCompanyId();
		List<Integer> mappedBrands = new ArrayList<>();
		if (Objects.nonNull(brandId)) {
			mappedBrands.add(brandId);
		} else if (Objects.nonNull(companyId)) {
			mappedBrands = masterDataCache.getCompanyBrandsMap().getOrDefault(companyId, new ArrayList<>()).stream()
					.map(Brand::getBrandId).toList();
		}
		return mappedBrands;
	}

	public static List<Integer> getMappedUnits() {
		Integer brandId = RequestContext.getBrandId();
		Integer companyId = RequestContext.getCompanyId();
		List<Integer> mappedUnits = new ArrayList<>();
		if (Objects.nonNull(brandId)) {

			mappedUnits = masterDataCache.getBrandUnitsMap().getOrDefault(brandId, new ArrayList<>()).stream().toList();

		} else if (Objects.nonNull(companyId)) {
			List<Integer> mappedBrands = masterDataCache.getCompanyBrandsMap().getOrDefault(companyId, new ArrayList<>()).stream()
					.map(Brand::getBrandId).toList();
			for (Integer b : mappedBrands) {

				mappedUnits.addAll(masterDataCache.getBrandUnitsMap().getOrDefault(b, new ArrayList<>()).stream().toList());

			}
		}
		return mappedUnits;
	}

	public static List<Integer> getCompaniesByBrand(Integer brandId) {
		if (brandId == null || brandId <= 0) {
			return masterDataCache.getCompanyBrandsMap().keySet().stream().collect(Collectors.toList());
		}
		List<Integer> companies = new ArrayList<>();
		for(Map.Entry<Integer, List<Brand>> companyBrands : masterDataCache.getCompanyBrandsMap().entrySet()) {
			boolean companyFound = companyBrands.getValue().stream()
					.anyMatch(brand -> brandId.equals(brand.getBrandId()));
			if(companyFound) {
				companies.add(companyBrands.getKey());
			}
		}
		return companies;
	}

	public static String getEmailIdOfEmployee(Integer userId) {
		LOG.info("current Logged in userId from params {} and from Request Context {}", userId, RequestContext.getContext().getLoggedInUserId());
		userId = Objects.nonNull(userId) ? userId : RequestContext.getContext().getLoggedInUserId();
		if( userId == null || userId <= 0 ) {
			return null;
		}
		EmployeeBasicDetail emp = masterDataCache.getEmployeeBasicDetail(userId);
		if (Objects.isNull(emp)) {
			return null;
		}
		String email = emp.getEmailId();
		if(StringUtils.isBlank(email)) {
			return null;
		}
		return email;
	}

	public static File convertExcelViewToFile(View excelView, String fileName) throws Exception {
		Map<String, Object> model = new HashMap<>();
		HttpServletRequest request = new MockHttpServletRequest();
		MockHttpServletResponse response = new MockHttpServletResponse();

		excelView.render(model, request, response);

		// Write response content to a temp file
		byte[] excelContent = response.getContentAsByteArray();
		File tempFile = File.createTempFile(fileName, ".xlsx");
		try (FileOutputStream fos = new FileOutputStream(tempFile)) {
			fos.write(excelContent);
		}

		return tempFile;
	}

	public static String removeDoubleQuotes(String str) {
		if (Objects.nonNull(str)) {
			str = str.trim();
			if (str.startsWith("\"") && str.endsWith("\"") && str.length() >= 2) {
				return str.substring(1, str.length() - 1);
			}
		}
		return str;
	}

    public static LocalTime getCurrentLocalTime(String defaultTimeZone) {
		return LocalTime.now(
				DateTimeZone.forTimeZone(TimeZone.getTimeZone(defaultTimeZone))
		);
	}
}
