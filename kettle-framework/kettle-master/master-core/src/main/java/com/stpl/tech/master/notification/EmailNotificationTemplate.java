package com.stpl.tech.master.notification;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;


public class EmailNotificationTemplate extends EmailNotification {
    private String subject;
    private String body;
    private String[] toMailIds;
    private EnvType envType;
    private String fromMail;

    public EmailNotificationTemplate() {}

    public EmailNotificationTemplate(String subject, String body, String[] toMailIds, EnvType envType, String fromMail) {
        this.subject = subject;
        this.body = body;
        this.toMailIds = toMailIds;
        this.envType = envType;
        this.fromMail = fromMail;
    }

    @Override
    public String[] getToEmails() {
        return (AppUtils.isDev(getEnvironmentType()) || ( toMailIds == null || toMailIds.length == 0 ) ? new String[] {AppConstants.TECHNOLOGY_EMAIL} : toMailIds);
    }

    @Override
    public String getFromEmail() {
        return fromMail;
    }

    @Override
    public String subject() {
        return (AppUtils.isDev(getEnvironmentType()) ? "[DEV] " : "") + subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        return body;
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
