package com.stpl.tech.master.core.notification.sms;

import com.stpl.tech.master.core.WebServiceHelper;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

public abstract class AbstractSMSClient implements SMSWebServiceClient {
	private static final Logger LOG = LoggerFactory.getLogger(AbstractSMSClient.class);

	protected final SMSConfiguration config;
	protected final SMSConfiguration shortURLConfig;

	protected AbstractSMSClient(SMSConfiguration config, SMSConfiguration shortURLConfig) {
		this.config = config;
		this.shortURLConfig = shortURLConfig;
	}

	public boolean sendMessage(String message, String contactNumber) throws IOException {
		if (StringUtils.isEmpty(message) || StringUtils.isEmpty(contactNumber)) {
			return false;
		}
		LOG.info(String.format("Message : %s\nContact Number : %s", message, contactNumber));
		String data = getSMSRequest(message, contactNumber);
		String response = callService(config.getUrl(), data);
		//LOG.info("Service Response : ", response);
		return checkSuccess(response);
	}

	public String callService(String urlString, String payLoad) throws IOException {
		URL url = new URL(urlString + payLoad);
		LOG.info("URL : "+urlString + payLoad);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		conn.setRequestMethod("GET");
		conn.setDoOutput(true);
		conn.setDoInput(true);
		conn.setUseCaches(false);
		conn.connect();
		BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
		String line;
		StringBuffer buffer = new StringBuffer();
		while ((line = rd.readLine()) != null) {
			buffer.append(line).append("\n");
		}
		LOG.info(buffer.toString());
		rd.close();
		conn.disconnect();
		return buffer.toString();
	}
	public boolean sendOTPRequestViaIVR(String token, String contact, Integer ivrId){
		if(StringUtils.isEmpty(contact)){
			return false ;
		}
		StringBuilder sb = new StringBuilder();
		String baseUrl = config.getUrl();
		try {
			String s = getOTPRequestViaIVR(token, contact,ivrId.toString());
			sb.append(baseUrl);
			sb.append(s);
		} catch (IOException e) {
			LOG.error("Error while generating url to send request for OTP Via IVR:",e);
		}
		try {
			return createPostRequest(sb.toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return  false;
	}

	public boolean createPostRequest (String url) throws IOException {
		/*HttpPost requestObject = new HttpPost(url);
		requestObject.setHeader("Accept", MediaType.APPLICATION_JSON.toString());
		try {
			HttpResponse response =WebServiceHelper.postRequest(requestObject);
			return checkSuccess(WebServiceHelper.convert(response,String.class));
		} catch (IOException e) {
			LOG.error("Exception while sending request to get otp via IVR :", e);
		}
		return false ;
		URL url1 = new URL(url);
		LOG.info("URL : "+url1);
		HttpURLConnection connection = (HttpURLConnection) url1.openConnection();
		connection.setRequestMethod("POST");
		connection.setDoOutput(true);
		connection.setDoInput(true);
		connection.setUseCaches(false);
		connection.connect();
		BufferedReader rd = new BufferedReader(new InputStreamReader(connection.getInputStream()));
		String line;
		StringBuffer buffer = new StringBuffer();
		while ((line = rd.readLine()) != null) {
			buffer.append(line).append("\n");
		}
		LOG.info(buffer.toString());
		rd.close();
		connection.disconnect();
		return */
		try{
			HttpPost req = new HttpPost(url);
			req.setHeader("Accept", MediaType.APPLICATION_JSON_VALUE);
			org.apache.http.HttpResponse response = WebServiceHelper.postRequest(req, 25, 25);
			String s = EntityUtils.toString(response.getEntity());
			LOG.info("Response from sending request for otp via ivr {}", s);
			return response.getStatusLine().getStatusCode() == HttpStatus.SC_OK;
		}catch(Exception e ){
			LOG.error("Error in sending post request for otp via ivr :", e);
			return false ;
		}
	}

	public abstract boolean checkSuccess(String response);
}
