/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.*;

/**
 * Created by Chaayos on 25-04-2016.
 */

@Entity
@Table(name = "PARTNER_PERMISSION_MAPPING")
public class PartnerPermissionMapping {

    private int id;
    private ExternalPartnerInfo partner;
    private AccessControlListData acl;
    private int permission;
    private String status;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PPM_ID", nullable = false, unique = true)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PARTNER_ID", nullable = false)
    public ExternalPartnerInfo getPartner() {
        return partner;
    }

    public void setPartner(ExternalPartnerInfo partner) {
        this.partner = partner;
    }

    @Column(name = "PERMISSION", nullable = false)
    public int getPermission() {
        return permission;
    }

    public void setPermission(int permission) {
        this.permission = permission;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ACL_ID", nullable = false)
    public AccessControlListData getAcl() {
        return acl;
    }

    public void setAcl(AccessControlListData acl) {
        this.acl = acl;
    }

    @Column(name = "PPM_STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "EmployeePermissionMapping{" +
            "productId=" + id +
            ", partner=" + partner +
            ", acl=" + acl +
            ", permission=" + permission +
            ", status='" + status + '\'' +
            '}';
    }
}
