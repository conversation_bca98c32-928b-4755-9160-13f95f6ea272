package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.data.vo.ScmMissingPriceResponse;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.InvalidRequestException;
import com.stpl.tech.master.data.model.MenuToSCMProductMapData;
import com.stpl.tech.master.domain.model.CondimentGroupData;
import com.stpl.tech.master.domain.model.DispenserBulkRequest;
import com.stpl.tech.master.domain.model.DispenserRecipeRequest;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.ProductRecipeMappingRequest;
import com.stpl.tech.master.recipe.model.DispenserConfig;
import com.stpl.tech.master.recipe.model.DispenserRecipeDetail;
import com.stpl.tech.master.recipe.model.DispenserTagsMapping;
import com.stpl.tech.master.recipe.model.RecipeCostData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.model.RecipeMediaDetail;
import com.stpl.tech.master.recipe.model.RecipeProfile;
import com.stpl.tech.master.recipe.model.RecipeUpdateLogDetail;
import com.stpl.tech.master.recipe.model.UploadRecipeMediaResponse;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeDetail;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeVersionData;
import com.stpl.tech.master.recipe.monk.model.MonkRecipes;
import com.stpl.tech.master.recipe.monk.model.MonkRecipesVersionRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public interface RecipeService {

    public int addRecipe(RecipeDetail detail) throws DataNotFoundException;

    public int addRecipeMedia(RecipeMediaDetail detail) throws DataNotFoundException;

    public UploadRecipeMediaResponse saveRecipeMedia(String mediaType, MultipartFile file);

    public List<RecipeDetail> findByName(String name);

    public List<RecipeDetail> findRecipesContainingName(String name);

    public List<RecipeDetail> findSCMRecipesContainingName(String name);

    public List<RecipeMediaDetail> findRecipeStepMediaDetailByRecipeId(int recipeId);

    public List<RecipeDetail> findByRecipeId(int id);

    public List<RecipeDetail> findByStatus(String status);

    public List<RecipeDetail> findByProductIdAndDimension(int productId, int dimensionId);

    public List<MonkRecipeDetail> findMonkRecipeByProductIdAndDimension(int productId, int dimensionId);

    public List<RecipeDetail> findByProductIdAndUom(int productId, String uom);

    public List<RecipeDetail> findSCMRecipeContainigProductId(int productId);

    public int updateRecipe(RecipeDetail detail) throws DataNotFoundException;

    public int updateRecipeWithImage(RecipeDetail detail) throws DataNotFoundException;

    public List<IdCodeName> removeRecipe(RecipeDetail detail);

    public List<RecipeDetail> findAll();


    public List<MonkRecipeDetail> findAllMonk();

    public List<MonkRecipeDetail> findMonkRecipesContainingName(String name);

    public List<RecipeDetail> findAllSCM();

    List<RecipeUpdateLogDetail> getUpdateLogs(Integer recipeId);

    void updateRecipeLog(RecipeUpdateLogDetail detail);

    public void updateAllRecipes(List<RecipeDetail> recipes) throws DataNotFoundException;

    public boolean updateRecipeCost(List<RecipeCostData> data);

    public RecipeDetail findSCMProductById(int productId);

    public List<RecipeDetail> findSCMRecipeProductById(int productId);

    public RecipeDetail findSCMRecipeProductByIdAndProfileAndStatus(int productId, String profile);

    /**
     * @param detail
     * @return
     */
    public int addMonkRecipe(int mode, MonkRecipeDetail detail);

    /**
     * @param detail
     * @return
     */
    public int updateMonkRecipe(int mode, MonkRecipeDetail detail);

    /**
     * @param detail
     * @return
     */
    public boolean removeMonkRecipe(MonkRecipeDetail detail);

    /**
     * @param detail
     * @return
     */
    public MonkRecipeVersionData addMonkRecipeVersion(MonkRecipeVersionData detail);

    /**
     * @param detail
     * @return
     */
    public String updateMonkRecipeVersion(MonkRecipeVersionData detail);

    /**
     * @param detail
     * @return
     */
    public List<MonkRecipeVersionData> findVersionDataByStatus(String status);

    public List<MonkRecipeVersionData> findVersionDataByStatusAndRegion(String status, String region);

    public List<RecipeProfile> getRecipeProfiles(String type);

    public List<String> findProfilesByProductIdAndDimension(int productId, int dimensionId);

    public Map<String, List<String>> findProfilesByProductId(int productId);

    public MonkRecipes checkAndSendMonkRecipes(Integer unitId, String version, Pair<String, String> unitMonkRecipeProfileVersion);

    public String getMonkRecipeVersion();

    public String getMonkRecipeVersion(String unitRegion);

    MonkRecipes getMonkRecipes(Integer unitId);

    public DispenserRecipeDetail saveDispenserData(DispenserRecipeRequest dispenserRecipeRequest);

    public boolean saveUpdatedVersionDetail(Integer unitId, String version, Integer count, Integer employeeId);

    public List<DispenserRecipeDetail> getDispenserData();

    public Boolean saveDispenseTag(List<String> tags);

    public DispenserConfig getDispenseTag();

    public List<HashMap<String, HashMap<String, DispenserTagsMapping>>> getRecipesWithDispensed();

    public  List<RecipeDetail> setRecipesWithDispensedTag(DispenserBulkRequest dispenserBulkRequest);

    List<MonkRecipeVersionData> saveMonkRecipeVersions(List<MonkRecipesVersionRequest> requests);

	List<RecipeDetail> findAll(List<Integer> excludeIds);

    List<RecipeDetail> findAllActive(List<Integer> excludeIds);

	List<Integer> findDistinctRecipeIdInMenuToSCMProductMap();

	List<RecipeDetail> findRecipeDetailNotInMenuToSCMMap(List<Integer> distinctRecipeId);

	public void saveAllMenuToScmProductMapData(List<MenuToSCMProductMapData> menuToSCMProductMapDataList);

	public List<MenuToSCMProductMapData> executeMenuToSCMProductMap();

	int refreshRecipeCache();

    public Boolean updateRecipeProfileStatus(Integer recipeId, Integer userId);

    List<RecipeMediaDetail> bulkUploadRecipeMedia(MultipartFile file);

    List<RecipeDetail> bulkUploadRecipeCondiment(MultipartFile file);

    View getBulkUploadRecipeSheetTemplate();

    Map<String,List<CondimentGroupData> > getSrcToCondimentMapping();

    public ScmMissingPriceResponse getApprovedRecipesByToday();

    int approveRecipe(RecipeDetail recipeDetail, Integer updatedBy, Boolean sentForApproval);

    void markRecipeInActive();

    int rejectRecipe(RecipeDetail recipeDetail, int lastUpdatedById, Boolean sentForRejection);

    void notifyRecipesForApproval(boolean isMarkedAsInActive, List<RecipeDetail> recipeDetailsList);

    List<RecipeDetail> findRecipeByRecipeId(Integer recipeId);

    Boolean reActivateRecipe(Integer recipeId, Integer productId, Integer dimensionInfoId, String dimension, Integer userId, String profile) throws InvalidRequestException;

    int addRecipeAfterCheck(RecipeDetail detail) throws InvalidRequestException;

    Boolean markInProgressToActiveRecipes(Integer recipeId, Integer userId) throws InvalidRequestException;

    public MonkRecipeVersionData getBasicDetailForProfile(String profile);

    public View getSheetForRecipes(String profile);

    boolean addRemoveProductsToPaidAddonsOfRecipes(Map<Integer, List<Integer>> recipesForUpdate, boolean needToRemove) throws DataNotFoundException;

    ResponseEntity<Map<String, List<String>>> updateRecipeByProducts(MultipartFile excelFile) throws IOException;

    Map<String, Set<String>> findRecipeByProductsAndDimensions(ProductRecipeMappingRequest request);

    public List<RecipeDetail> filterDetailsByCompanyAndBrand(List<RecipeDetail> details);

    List<RecipeDetail> applyConversionRatiosToRecipeIngredients(Map<Integer, Map<Integer, BigDecimal>> productConvesrionMap);
}
