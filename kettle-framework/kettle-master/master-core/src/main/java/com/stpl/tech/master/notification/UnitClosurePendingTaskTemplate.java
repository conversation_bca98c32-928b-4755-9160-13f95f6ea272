package com.stpl.tech.master.notification;

import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.util.notification.AbstractTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

public class UnitClosurePendingTaskTemplate extends AbstractTemplate {

    private final Map<String, Object> data = new HashMap<String, Object>();
    private Map<String,List<String>> messages;

    private String unitName;

    private String basePath;

    public UnitClosurePendingTaskTemplate(Map<String,List<String>> messages, String basePath,String unitName) {
        this.messages = messages;
        this.basePath = basePath;
        this.unitName   = unitName;
    }
    @Override
    public String getTemplatePath() {
        return "template/UnitClosurePendingTaskTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/UnitClosureNotification/"+"Template_"+new Date().getTime()+".html";
    }

    @Override
    public Map<String, Object> getData() {
        data.put("messages", messages);
        data.put("unitName", unitName);
        return data;
    }
}
