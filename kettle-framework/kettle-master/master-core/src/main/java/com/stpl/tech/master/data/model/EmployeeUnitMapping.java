/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

// Generated 20 Jul, 2015 5:25:16 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

/**
 * EmployeeUnitMapping generated by hbm2java
 */
@Entity
@Table(name = "EMPLOYEE_UNIT_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "EMP_ID", "UNIT_ID",
		"MAPPING_STATUS" }))
public class EmployeeUnitMapping implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6056330838231520834L;
	private Integer empUnitKeyId;
	private EmployeeDetail employeeDetail;
	private UnitDetail unitDetail;
	private String mappingStatus;
	private Date lastUpdateTime;

	public EmployeeUnitMapping() {
	}

	public EmployeeUnitMapping(EmployeeDetail employeeDetail, UnitDetail unitDetail, String mappingStatus,
			Date lastUpdateTime) {
		super();
		this.employeeDetail = employeeDetail;
		this.unitDetail = unitDetail;
		this.mappingStatus = mappingStatus;
		this.lastUpdateTime = lastUpdateTime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EMP_UNIT_KEY_ID", unique = true, nullable = false)
	public Integer getEmpUnitKeyId() {
		return this.empUnitKeyId;
	}

	public void setEmpUnitKeyId(Integer empUnitKeyId) {
		this.empUnitKeyId = empUnitKeyId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EMP_ID", nullable = false)
	public EmployeeDetail getEmployeeDetail() {
		return this.employeeDetail;
	}

	public void setEmployeeDetail(EmployeeDetail employeeDetail) {
		this.employeeDetail = employeeDetail;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	public UnitDetail getUnitDetail() {
		return this.unitDetail;
	}

	public void setUnitDetail(UnitDetail unitDetail) {
		this.unitDetail = unitDetail;
	}

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return this.mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
	public Date getLastUpdateTime() {
		return this.lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
