package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "OFFER_DESCRIPTION_METADATA")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OfferDescriptionMetadata {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "METADATA_ID", unique = true, nullable = false)
    private Integer metaDataId;
    @Column(name = "OFFER_ID", nullable = false)
    private Integer offerId;
    @Column(name = "DESCRIPTION_TYPE", nullable = false)
    private String descriptionType;
    @Column(name = "DESCRIPTION_VALUE")
    private String descriptionValue;
    @Column(name = "COLOR")
    private String color;
    @Column(name = "FONT_SIZE")
    private String fontSize;
    @Column(name = "IS_BOLD")
    private String isBold;
    @Column(name = "SEQUENCE_NUMBER")
    private String sequenceNumber;
    @Column(name = "STATUS")
    private String status;
}
