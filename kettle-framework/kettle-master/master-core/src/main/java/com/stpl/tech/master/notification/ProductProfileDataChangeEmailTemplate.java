package com.stpl.tech.master.notification;

import com.stpl.tech.master.data.model.ProductDetail;
import com.stpl.tech.master.data.model.UnitProductProfileExcelData;
import com.stpl.tech.master.domain.model.UnitProductMappingData;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ProductProfileDataChangeEmailTemplate extends AbstractVelocityTemplate {

    private String basePath;

    private String currentDate;
    private Map<ProductDetail, List<UnitProductMappingData>> mappings;
    private Map<ProductDetail, List<UnitProductProfileExcelData>> mapsForBulkUpload;
    private Integer updatedBy;
    private boolean isProfileChange;

    public ProductProfileDataChangeEmailTemplate(String basePath, Map<ProductDetail, List<UnitProductMappingData>> mappings, Map<ProductDetail, List<UnitProductProfileExcelData>> mapsForBulkUpload, Integer updatedBy, boolean isProfileChange) {
        this.basePath = basePath;
        this.mappings = mappings;
        this.updatedBy = updatedBy;
        this.isProfileChange = isProfileChange;
        this.mapsForBulkUpload = mapsForBulkUpload;
    }

    @Override
    public String getTemplatePath() {
        return "template/ProductProfileDataChangeEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "productProfileUpdation/report/" + currentDate + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> data = new HashMap<>();
        data.put("mappingData", mappings);
        data.put("mappingDataForBulkUpload", mapsForBulkUpload);
        data.put("updatedBy", updatedBy);
        data.put("isProfileChange", isProfileChange);
        return data;
    }
}
