/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core;

import java.security.Key;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.util.AppConstants;

import io.jsonwebtoken.impl.Base64UrlCodec;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PasswordImpl {

	public static String encrypt(String value) throws AuthenticationFailureException {
		try {
			Key key = generateKey();
			Cipher cipher = Cipher.getInstance(AppConstants.ALGORITHM);
			cipher.init(Cipher.ENCRYPT_MODE, key);
			byte[] encryptedByteValue = cipher.doFinal(value.getBytes("utf-8"));
			String encryptedValue64 = Base64.getEncoder().encodeToString(encryptedByteValue);
			return encryptedValue64;
		} catch (Exception e) {
			throw new AuthenticationFailureException("Error while encrypting the password", e);
		}
	}
	
	public static String encryptUrlCodec(String value) throws AuthenticationFailureException {
		try {
			Key key = generateKey();
			Cipher cipher = Cipher.getInstance(AppConstants.ALGORITHM);
			cipher.init(Cipher.ENCRYPT_MODE, key);
			byte[] encryptedByteValue = cipher.doFinal(value.getBytes("utf-8"));
			String encryptedValue64 = new Base64UrlCodec().encode(encryptedByteValue);
			return encryptedValue64;
		} catch (Exception e) {
			throw new AuthenticationFailureException("Error while encrypting the password", e);
		}
	}

	public static String decrypt(String value) throws AuthenticationFailureException {
		try {
			Key key = generateKey();
			Cipher cipher = Cipher.getInstance(AppConstants.ALGORITHM);
			cipher.init(Cipher.DECRYPT_MODE, key);
			byte[] decryptedValue64 = Base64.getDecoder().decode(value);
			byte[] decryptedByteValue = cipher.doFinal(decryptedValue64);
			return new String(decryptedByteValue, "utf-8");
		} catch (Exception e) {
			throw new AuthenticationFailureException("Error while decrypting the password", e);
		}
	}

	public static String decryptUrlCodec(String value) throws AuthenticationFailureException {
		try {
			Key key = generateKey();
			Cipher cipher = Cipher.getInstance(AppConstants.ALGORITHM);
			cipher.init(Cipher.DECRYPT_MODE, key);
			byte[] decryptedValue64 = new Base64UrlCodec().decode(value);
			byte[] decryptedByteValue = cipher.doFinal(decryptedValue64);
			return new String(decryptedByteValue, "utf-8");
		} catch (Exception e) {
			throw new AuthenticationFailureException("Error while decrypting the password", e);
		}
	}
	
	private static Key generateKey() throws Exception {
		Key key = new SecretKeySpec(AppConstants.PASSPHRASE_KEY.getBytes(), AppConstants.ALGORITHM);
		return key;
	}

	public static void main(String[] args) {
		try {
			String password = "DUNZO";
			System.out.println("plain pass=" + password);
			String encryptedPassword = PasswordImpl.encrypt(password);
			System.out.println("encrypted pass=" + encryptedPassword);
			String decryptedPassword = PasswordImpl.decrypt("tYuyDsDm3tJSsenz42G0WQ==");
			System.out.println("decrypted pass=" + decryptedPassword);
		} catch (Exception e) {
			System.out.println("bug" + e.getMessage());
		}
	}

    public static String generateRandomPassword(Integer empId, String empName) {
        try {
            if (Objects.isNull(empId) || Objects.isNull(empName) || empName.isEmpty()) {
                throw new Exception("Employee ID / Name is null or empty");
            }

            // 1. Extract initials
            String[] parts = empName.trim().split("\\s+");
            StringBuilder initials = new StringBuilder();
            for (String part : parts) {
                initials.append(Character.toUpperCase(part.charAt(0)));
            }

            // 2. Shuffle empId digits
            String empIdStr = empId.toString();
            List<Character> digits = new ArrayList<>();
            for (char c : empIdStr.toCharArray()) {
                digits.add(c);
            }
            Collections.shuffle(digits, new SecureRandom());

            StringBuilder shuffledEmpId = new StringBuilder();
            for (char d : digits) {
                shuffledEmpId.append(d);
            }

            // 3. Pick a random special character
            String specialChars = "@#$%^&*!?";
            SecureRandom random = new SecureRandom();
            char special = specialChars.charAt(random.nextInt(specialChars.length()));

            // 4. Concatenate
            return initials.toString() + special + shuffledEmpId;

        } catch (Exception e) {
            log.error("Error occurred while generating random password: {}", e);
            throw new RuntimeException(e);
        }
    }
}