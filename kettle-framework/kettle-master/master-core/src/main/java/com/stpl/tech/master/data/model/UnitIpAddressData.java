package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "UNIT_IP_ADDRESS_DATA")
public class UnitIpAddressData implements Serializable {
    private static final long serialVersionUID = 7107967061999806187L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Integer id;
    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;
    @Column(name = "TERMINAL_ID", nullable = false)
    private Integer terminalId;
    @Column(name = "IP_ADDRESS", nullable = false)
    private String ipAddress;
    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    private Date lastUpdateTime;
    @Column(name = "APP_NAME", nullable = false)
    private String appName;
}