package com.stpl.tech.master.core.service;

import java.util.List;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.FicoDetail;

public interface FicoManagementService {

	public List<FicoDetail> getFicoList();

	public boolean addFicoDetails(FicoDetail ficoDetail) throws DataUpdationException;

	public boolean updateFicoDetails(FicoDetail ficoDetail);

}
