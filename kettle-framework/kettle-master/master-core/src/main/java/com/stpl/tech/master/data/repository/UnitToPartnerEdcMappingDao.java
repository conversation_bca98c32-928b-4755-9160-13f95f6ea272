package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UnitToPartnerEdcMappingDao extends JpaRepository<UnitToPartnerEdcMapping, Integer> {

    List<UnitToPartnerEdcMapping> findAllByStatus(String status);

    UnitToPartnerEdcMapping findByUnitIdAndPartnerNameAndTerminalId(Integer unitId, String partnerName,String terminalId);

    @Override
    Optional<UnitToPartnerEdcMapping> findById(Integer integer);

    List<UnitToPartnerEdcMapping> findByMerchantIdAndStatus(String merchantId, String status);

}
