package com.stpl.tech.master.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.InstallationService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.InstallationDao;
import com.stpl.tech.master.data.model.ApplicationInstallationData;
import com.stpl.tech.master.data.model.UnitRestrictedApplicationData;
import com.stpl.tech.master.domain.model.ApplicationInstallationDetail;
import com.stpl.tech.master.domain.model.ApplicationInstallationRequest;
import com.stpl.tech.master.domain.model.ApplicationInstallationStatus;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.UnitRestrictedApplicationDetail;
import com.stpl.tech.util.AppUtils;

@Service
public class InstallationServiceImpl implements InstallationService {

	@Autowired
	private InstallationDao dao;
	@Autowired
	private MasterDataCache masterCache;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ApplicationInstallationDetail> getInstalledMachines(String applicationName, Integer unitId,
			String terminal, String screenType, String status) {
		List<ApplicationInstallationDetail> installationDetails = new ArrayList<>();
		List<ApplicationInstallationData> installationDatas = dao.getInstalledMachines(applicationName, unitId,
				terminal, screenType, status);
		installationDatas.forEach(data -> {
			installationDetails.add(MasterDataConverter.convert(data, masterCache));
		});
		return installationDetails;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public ApplicationInstallationDetail addInstallationMachine(ApplicationInstallationDetail installationDetail) {
		ApplicationInstallationData data = new ApplicationInstallationData();
		data.setApplicationName(installationDetail.getApplicationName().name());
		if (installationDetail.getScreenType() != null) {
			data.setScreenType(installationDetail.getScreenType());
		}
		data.setUnitId(installationDetail.getUnit().getId());
		if (installationDetail.getTerminal() != null) {
			data.setTerminal(installationDetail.getTerminal());
		}

		data.setMachineId(installationDetail.getMachineId());
		if (installationDetail.getMachineDetails() != null) {
			data.setMachineDetails(installationDetail.getMachineDetails());
		}
		data.setCreatedBy(installationDetail.getCreatedBy().getId());
		data.setCreatedOn(AppUtils.getCurrentTimestamp());
		if (installationDetail.getStatus() != null) {
			data.setStatus(installationDetail.getStatus().name());
		}
		data = (ApplicationInstallationData) dao.add(data);

		if (data != null) {
			return MasterDataConverter.convert(data, masterCache);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean updateInstallationMachineStatus(ApplicationInstallationRequest request) {
		ApplicationInstallationData data = dao.find(ApplicationInstallationData.class, request.getId());
		data.setStatus(request.getStatus());
		data.setLastUpdatedBy(request.getUserId());
		data.setLastUpdatedOn(AppUtils.getCurrentTimestamp());
		data = (ApplicationInstallationData) dao.update(data);
		if (data != null) {
			return true;
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean updateUnitRestrictedApplication(List<UnitRestrictedApplicationDetail> applicationList) {

		for (UnitRestrictedApplicationDetail detail : applicationList) {
			UnitRestrictedApplicationData data = dao.find(UnitRestrictedApplicationData.class, detail.getId());
			if (!(data.getStatus().equals(detail.getStatus().name()))) {
				data.setLastUpdatedBy(detail.getLastUpdatedBy().getId());
				data.setLastUpdatedOn(AppUtils.getCurrentTimestamp());
				data.setStatus(detail.getStatus().name());
				dao.update(data);
			}
		}
		return true;
	}

	private void addApplication(Integer unitId) {
		for (ApplicationName name : getEnumApplicationList()) {
			UnitRestrictedApplicationData data = new UnitRestrictedApplicationData();
			data.setApplicationName(name.name());
			data.setUnitId(unitId);
			data.setLastUpdatedBy(-1);
			data.setStatus(ApplicationInstallationStatus.IN_ACTIVE.name());
			data.setLastUpdatedOn(AppUtils.getCurrentTimestamp());
			dao.add(data);
		}
	}

	private List<ApplicationName> getEnumApplicationList() {
		return Arrays.asList(ApplicationName.values());
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<UnitRestrictedApplicationDetail> getApplicationList(Integer unitId, String status) {
		List<UnitRestrictedApplicationDetail> applicationDetails = new ArrayList<>();
		List<UnitRestrictedApplicationData> data = dao.getUnitRestrictedApplication(unitId, null, status);
		if (data.size() == 0) {
			addApplication(unitId);
			data = dao.getUnitRestrictedApplication(unitId, null, status);
		}
		for (UnitRestrictedApplicationData unitRestrictedApplicationData : data) {
			applicationDetails.add(MasterDataConverter.convert(unitRestrictedApplicationData, masterCache));
		}
		return applicationDetails;
	}

}
