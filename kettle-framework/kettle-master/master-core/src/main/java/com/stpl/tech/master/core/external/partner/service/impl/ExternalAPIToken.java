/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.25 at 12:25:34 AM IST 
//

package com.stpl.tech.master.core.external.partner.service.impl;

import com.stpl.tech.master.core.external.acl.service.TokenDao;
import io.jsonwebtoken.Claims;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class ExternalAPIToken implements TokenDao, Serializable {

	private static final long serialVersionUID = 6770737312282844886L;
	private Integer partnerId;
	private String partnerName;
	private String passCode;
	private String envType;
	private Map<String, Integer> accessAPIs;

	public ExternalAPIToken() {

	}

	public ExternalAPIToken(Integer partnerId, String partnerName, String passCode, String envType) {
		super();
		this.partnerId = partnerId;
		this.partnerName = partnerName;
		this.passCode = passCode;
		this.envType = envType;
	}
	
	public ExternalAPIToken(String partnerName, String passCode, String envType) {
		super();
		this.partnerName = partnerName;
		this.passCode = passCode;
		this.envType = envType;
	}

	public Map<String, Object> createClaims() {
		// Setting JWT Claims
		Map<String, Object> authClaims = new HashMap<String, Object>();
		authClaims.put("partnerId", partnerId);
		authClaims.put("partnerName", partnerName);
		authClaims.put("passCode", passCode);
		authClaims.put("envType", envType);
		return authClaims;
	}

	public void parseClaims(Claims claims) {
		this.partnerId = claims.get("partnerId", Integer.class);
		this.partnerName = claims.get("partnerName", String.class);
		this.passCode = claims.get("passCode", String.class);
		this.envType = claims.get("envType", String.class);
	}

	public Integer getPartnerId() {
		return partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}

	public String getPartnerName() {
		return partnerName;
	}

	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}

	public String getEnvType() {
		return envType;
	}

	public void setEnvType(String envType) {
		this.envType = envType;
	}

	public String getPassCode() {
		return passCode;
	}

	public void setPassCode(String passCode) {
		this.passCode = passCode;
	}

	public Map<String, Integer> getAccessAPIs() {
		return accessAPIs;
	}

	public void setAccessAPIs(Map<String, Integer> accessAPIs) {
		this.accessAPIs = accessAPIs;
	}

}
