package com.stpl.tech.master.core.external.inventory.service.impl;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.InventoryService;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.RandomStringGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.Session;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class InventoryServiceImpl implements InventoryService {

	private static final Logger LOG = LoggerFactory.getLogger(InventoryServiceImpl.class);

	@Autowired
	private MasterDataCache cache;

	@Autowired
	private SQSNotificationService sqsNotificationService;
	
	private Map<String,MessageProducer> inventoryProducer = new HashMap<>();

	private RandomStringGenerator randomStringGenerator = new RandomStringGenerator();

	private SQSSession session;

	@Override
	public void publishInventory(String env, QuantityResponseData response) throws JMSException {
		if(MasterUtil.isActiveCafe(cache.getUnitBasicDetail(response.getUnitId()))) {
			if (session == null) {
				Regions region = AppUtils.getRegion(EnvType.valueOf(env));
				session = SQSNotification.getInstance().getSession(region, Session.AUTO_ACKNOWLEDGE);
			}
			String unitZoneKey = "_INVENTORY" +
					(Objects.nonNull(cache.getUnitBasicDetail(response.getUnitId()).getUnitZone())
							? "_" + cache.getUnitBasicDetail(response.getUnitId()).getUnitZone().toUpperCase() : "_NORTH");
			unitZoneKey = unitZoneKey + ".fifo";
			LOG.info("Queue name {}", unitZoneKey);
			if (!inventoryProducer.containsKey(unitZoneKey)) {
				MessageProducer inventoryMessageProducer = SQSNotification.getInstance().getProducer(session, env, unitZoneKey);
				inventoryProducer.put(unitZoneKey,inventoryMessageProducer);
			}
			try {
				inventoryProducer.get(unitZoneKey).send(session.createObjectMessage(response));
			} catch (Exception e){
				LOG.error("Exception Faced While Faetching ZOne for Inventory Queue",e);
			}
		}
	}

	@Override
	public void publishInventorySQSFifo(String env, QuantityResponseData response) throws JMSException {
		String unitZoneKey = "_INVENTORY" +
				(Objects.nonNull(cache.getUnitBasicDetail(response.getUnitId()).getUnitZone())
						? "_" + cache.getUnitBasicDetail(response.getUnitId()).getUnitZone().toUpperCase() : "_NORTH");
		unitZoneKey = unitZoneKey + ".fifo";
		sqsNotificationService.publishToSQSFifo(env,response,unitZoneKey,AppUtils.getRegion(EnvType.valueOf(env)));
	}
}
