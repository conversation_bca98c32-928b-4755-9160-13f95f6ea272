package com.stpl.tech.master.core.data.vo;

import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ScmMissingPriceResponse {
    List<RecipeDetail> recipeDetailList;

    Map<Integer,List<RecipeDetail>> addOnRecipeDetailByRecipeId;

    Map<Integer,List<Integer>> unitDetailListByRecipeId;
}
