package com.stpl.tech.master.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "MENU_RECOMMENDATION_DATA")
public class MenuRecommendationData {

    private Integer menuRecommendationId;
    private String menuRecommendationName;
    private String menuRecommendationDescription;
    private Integer createdBy;
    private Date creationTime;
    private Date lastUpdateTime;
    private String recommendationType;
    private Integer cloneId;
    private String status;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY )
    @Column(name = "MENU_RECOMMENDATION_ID", nullable = false, unique = true)
    public Integer getMenuRecommendationId() {
        return menuRecommendationId;
    }

    public void setMenuRecommendationId(Integer menuSequenceId) {
        this.menuRecommendationId = menuSequenceId;
    }

    @Column(name = "MENU_RECOMMENDATION_NAME", nullable = false)
    public String getMenuRecommendationName() {
        return menuRecommendationName;
    }

    public void setMenuRecommendationName(String menuSequenceName) {
        this.menuRecommendationName = menuSequenceName;
    }

    @Column(name = "MENU_RECOMMENDATION_DESCRIPTION", nullable = false)
    public String getMenuRecommendationDescription() {
        return menuRecommendationDescription;
    }

    public void setMenuRecommendationDescription(String menuRecommendationDescription) {
        this.menuRecommendationDescription = menuRecommendationDescription;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name = "RECOMMENDATION_TYPE", nullable = false)
    public String getRecommendationType() {
        return recommendationType;
    }

    public void setRecommendationType(String menuType) {
        this.recommendationType = menuType;
    }

    @Column(name = "CLONE_ID", nullable = true)
    public Integer getCloneId() {
        return cloneId;
    }

    public void setCloneId(Integer cloneId) {
        this.cloneId = cloneId;
    }

    @Column(name = "STATUS", nullable = true)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
