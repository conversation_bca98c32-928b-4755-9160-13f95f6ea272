package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "CATEGORY_ATTRIBUTES")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CategoryAttributes {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ATTRIBUTE_ID", unique = true, nullable = false)
    private Integer attributeId;
    @Column(name = "RL_ID",nullable = false)
    private Integer rlId;
    @Column(name = "RL_NAME",nullable = false)
    private String rlName;
    @Column(name = "DEFAULT_VISIBILITY",nullable = false)
    private String defaultVisibility;
    @Column(name = "SEQUENCE_NUMBER")
    private Integer sequenceNumber;
    @Column(name = "TAG_IMAGE_URL",columnDefinition = "text")
    private String tagImageUrl;

}
