/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.config;

import java.util.Properties;
import java.util.concurrent.ForkJoinPool;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;

import com.stpl.tech.spring.config.SpringUtilityServiceConfig;

@Configuration
@ComponentScan({"com.stpl.tech.master.core.external.price","com.stpl.tech.master.core.external.banner","com.stpl.tech.master.core.external.unit", "com.stpl.tech.master.core.external.offer", "com.stpl.tech.master.core.external.partner",
        "com.stpl.tech.master.core.external.inventory", "com.stpl.tech.master.core.external.notification", "com.stpl.tech.master.core.external.excel",
        "com.stpl.tech.master.core.external.partnerReport", "com.stpl.tech.master.core.external.report","com.stpl.tech.master.core.external.refLookup", "com.stpl.tech.spring.service",
        "com.stpl.tech.master.core.service.batchProcess"
})
@EnableJpaRepositories(basePackages = {"com.stpl.tech.master.core.external.price.dao.impl","com.stpl.tech.master.core.external.banner.dao.impl","com.stpl.tech.master.core.external.unit.dao.impl", "com.stpl.tech.master.core.external.partner.dao.impl",
    "com.stpl.tech.master.core.external.offer.dao.impl", "com.stpl.tech.master.core.external.notification.dao.impl",
        "com.stpl.tech.master.core.external.partnerReport.dao.impl", "com.stpl.tech.master.core.external.report.dao.impl","com.stpl.tech.master.core.external.refLookup.dao.impl",
        "com.stpl.tech.master.data.repository"},
        entityManagerFactoryRef = "MasterDataSourceEMFactory", transactionManagerRef = "MasterDataSourceTM")
@Import(value = { SpringUtilityServiceConfig.class, MasterHazelcastConfig.class,MasterCacheClientConfig.class})

public class MasterExternalConfig {

    @Autowired
    private Environment env;

    public MasterExternalConfig() {
        super();
    }
    // beans

    @Bean(name = "MasterDataSourceEMFactory")
    public LocalContainerEntityManagerFactoryBean masterEntityManagerFactory() {
        final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(masterDataSource());
        em.setPackagesToScan("com.stpl.tech.master.data.model");
        final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        em.setJpaProperties(masterAdditionalProperties());
        em.setPersistenceUnitName("MasterDataSourcePUName");
        return em;
    }

    @Bean(name = "MasterDataSource")
    public DataSource masterDataSource() {
        final DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(env.getProperty("master.jdbc.driverClassName"));
        dataSource.setUrl(env.getProperty("master.jdbc.url"));
        dataSource.setUsername(env.getProperty("master.jdbc.user"));
        dataSource.setPassword(env.getProperty("master.jdbc.pass"));

        return dataSource;
    }

    @Bean(name = "MasterDataSourceTM")
    public PlatformTransactionManager masterTransactionManager() {
        final JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(masterEntityManagerFactory().getObject());
        return transactionManager;
    }

    @Bean(name = "MasterDataSourceET")
    public PersistenceExceptionTranslationPostProcessor masterExceptionTranslation() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
        pool.setCorePoolSize(5);
        pool.setMaxPoolSize(10);
        pool.setWaitForTasksToCompleteOnShutdown(true);
        pool.setThreadNamePrefix("MasterTaskExecutor-");
        return pool;
    }

    @Bean(name = "ForkJoinPool")
    @ConditionalOnProperty(value = "is.forkJoinPool.in.use", havingValue = "true")
    public ForkJoinPool customForkJoinPool() {
        return new ForkJoinPool(5);
    }

    final Properties masterAdditionalProperties() {
        final Properties hibernateProperties = new Properties();
        hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
        hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
        hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
        // hibernateProperties.setProperty("hibernate.globally_quoted_identifiers",
        // "true");
        return hibernateProperties;
    }

}