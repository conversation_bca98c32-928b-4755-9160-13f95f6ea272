package com.stpl.tech.master.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "UNIT_CHANNEL_PARTNER_MENU_MAPPING",
		uniqueConstraints = @UniqueConstraint(columnNames = {"MENU_SEQUENCE_ID", "UNIT_CHANNEL_PARTNER_ID", "MENU_TYPE", "MENU_APP", "BRAND_ID"}))
public class UnitChannelPartnerMenuMappingData {

	private Integer id;
	private Integer menuSequenceId;
	private Integer cartRecommendationSequenceId;
	private Integer menuRecommendationSequenceId;
	private String menuSequenceName;
	private Integer unitPartnerMappingId;
	private String startTime;
	private String endTime;
	private Integer menuDay;
	private String menuType;
	private String menuApp;
	private Integer createdBy;
	private Integer updatedBy;
	private Date createdAt;
	private Date updatedAt;
	private String status;
	private Integer brandId;
	private Integer priceProfileId;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "MENU_SEQUENCE_ID", nullable = false)
	public Integer getMenuSequenceId() {
		return menuSequenceId;
	}

	public void setMenuSequenceId(Integer menuSequenceId) {
		this.menuSequenceId = menuSequenceId;
	}

	@Column(name = "CART_RECOMMENDATION_SEQUENCE_ID", nullable = true)
	public Integer getCartRecommendationSequenceId() {
		return cartRecommendationSequenceId;
	}

	public void setCartRecommendationSequenceId(Integer cartRecommendationSequenceId) {
		this.cartRecommendationSequenceId = cartRecommendationSequenceId;
	}

	@Column(name = "MENU_RECOMMENDATION_SEQUENCE_ID", nullable = true)
	public Integer getMenuRecommendationSequenceId() {
		return menuRecommendationSequenceId;
	}

	public void setMenuRecommendationSequenceId(Integer menuRecommendationSequenceId) {
		this.menuRecommendationSequenceId = menuRecommendationSequenceId;
	}

	@Column(name = "MENU_SEQUENCE_NAME")
	public String getMenuSequenceName() {
		return menuSequenceName;
	}

	public void setMenuSequenceName(String menuSequenceName) {
		this.menuSequenceName = menuSequenceName;
	}

	@Column(name = "UNIT_CHANNEL_PARTNER_ID", nullable = false)
	public Integer getUnitPartnerMappingId() {
		return unitPartnerMappingId;
	}

	public void setUnitPartnerMappingId(Integer unitPartnerMappingId) {
		this.unitPartnerMappingId = unitPartnerMappingId;
	}

	@Column(name = "MENU_START_TIME")
	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	@Column(name = "MENU_END_TIME")
	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	@Column(name = "MENU_DAY")
	public Integer getMenuDay() {
		return menuDay;
	}

	public void setMenuDay(Integer menuDay) {
		this.menuDay = menuDay;
	}

	@Column(name = "MENU_TYPE", nullable = false)
	public String getMenuType() {
		return menuType;
	}

	public void setMenuType(String menuType) {
		this.menuType = menuType;
	}

	@Column(name = "MENU_APP", nullable = false)
	public String getMenuApp() {
		return menuApp;
	}

	public void setMenuApp(String menuApp) {
		this.menuApp = menuApp;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "UPDATED_BY", nullable = false)
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "CREATED_AT", nullable = false)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "UPDATED_AT", nullable = false)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Column(name = "STATUS")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "BRAND_ID", nullable = false)
	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	@Column(name = "PRICE_PROFILE_ID")
	public Integer getPriceProfileId() {
		return priceProfileId;
	}

	public void setPriceProfileId(Integer priceProfileId) {
		this.priceProfileId = priceProfileId;
	}

	@Override
	public String toString() {
		return "UnitChannelPartnerMenuMappingData [id=" + id + ", menuSequenceId=" + menuSequenceId
				+ ", cartRecommendationSequenceId=" + cartRecommendationSequenceId + ", menuRecommendationSequenceId="
				+ menuRecommendationSequenceId + ", menuSequenceName=" + menuSequenceName + ", unitPartnerMappingId="
				+ unitPartnerMappingId + ", startTime=" + startTime + ", endTime=" + endTime + ", menuDay=" + menuDay
				+ ", menuType=" + menuType + ", menuApp=" + menuApp + ", createdBy=" + createdBy + ", updatedBy="
				+ updatedBy + ", createdAt=" + createdAt + ", updatedAt=" + updatedAt + ", status=" + status
				+ ", brandId=" + brandId + "]";
	}

}
