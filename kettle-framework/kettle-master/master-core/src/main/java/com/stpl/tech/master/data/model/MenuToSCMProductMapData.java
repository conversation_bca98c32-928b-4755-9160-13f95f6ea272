package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "MENU_TO_SCM_PRODUCT_MAP")
public class MenuToSCMProductMapData{
    private Integer id;
    private Integer menuProductId;
    private String menuProductDimension;
    private String recipeProfile;
    private Integer recipeId;
    private Integer scmProductId;
    private String scmProductName;
    private String scmProductUom;
    private BigDecimal scmProductQuantity;
    private String ingredientType;
    private String productClassification;

    public MenuToSCMProductMapData() {

    }

    public MenuToSCMProductMapData(Integer menuProductId, String menuProductDimension, String recipeProfile, Integer recipeId, Integer scmProductId, String scmProductName,
                                   String scmProductUom, BigDecimal scmProductQuantity, String ingredientType, String productClassification) {
        this.menuProductId = menuProductId;
        this.menuProductDimension = menuProductDimension;
        this.recipeProfile = recipeProfile;
        this.recipeId = recipeId;
        this.scmProductId = scmProductId;
        this.scmProductName = scmProductName;
        this.scmProductUom = scmProductUom;
        this.scmProductQuantity = scmProductQuantity;
        this.ingredientType = ingredientType;
        this.productClassification = productClassification;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "KEY_ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "MENU_PRODUCT_ID")
    public Integer getMenuProductId() {
        return menuProductId;
    }

    public void setMenuProductId(Integer menuProductId) {
        this.menuProductId = menuProductId;
    }


    @Column(name = "MENU_PRODUCT_DIMENSION")
    public String getMenuProductDimension() {
        return menuProductDimension;
    }

    public void setMenuProductDimension(String menuProductDimension) {
        this.menuProductDimension = menuProductDimension;
    }


    @Column(name = "RECIPE_PROFILE")
    public String getRecipeProfile() {
        return recipeProfile;
    }

    public void setRecipeProfile(String recipeProfile) {
        this.recipeProfile = recipeProfile;
    }


    @Column(name = "RECIPE_ID")
    public Integer getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(Integer recipeId) {
        this.recipeId = recipeId;
    }

    @Column(name = "SCM_PRODUCT_ID")
    public Integer getScmProductId() {
        return scmProductId;
    }

    public void setScmProductId(Integer scmProductId) {
        this.scmProductId = scmProductId;
    }

    @Column(name = "SCM_PRODUCT_NAME")
    public String getScmProductName() {
        return scmProductName;
    }

    public void setScmProductName(String scmProductName) {
        this.scmProductName = scmProductName;
    }



    @Column(name = "SCM_PRODUCT_UOM")
    public String getScmProductUom() {
        return scmProductUom;
    }

    public void setScmProductUom(String scmProductUom) {
        this.scmProductUom = scmProductUom;
    }



    @Column(name = "SCM_PRODUCT_QUANTITY")
    public BigDecimal getSCMProductQuantity() { return scmProductQuantity; }

    public void setSCMProductQuantity(BigDecimal scmProductQuantity) {
        this.scmProductQuantity = scmProductQuantity;
    }

    @Column(name = "INGREDIENT_TYPE")
    public String getIngredientType() {
        return ingredientType;
    }

    public void setIngredientType(String ingredientType) {
        this.ingredientType = ingredientType;
    }

    @Column(name = "PRODUCT_CLASSIFICATION")
    public String getProductClassification() {
        return productClassification;
    }

    public void setProductClassification(String productClassification) {
        this.productClassification = productClassification;
    }
}