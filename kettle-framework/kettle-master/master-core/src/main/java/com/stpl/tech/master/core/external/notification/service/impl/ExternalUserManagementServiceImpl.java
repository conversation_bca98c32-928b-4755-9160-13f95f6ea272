package com.stpl.tech.master.core.external.notification.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.master.core.external.notification.dao.ExternalUserManagementDao;
import com.stpl.tech.master.core.external.notification.service.ExternalUserManagementService;

@Service
public class ExternalUserManagementServiceImpl implements ExternalUserManagementService {

	@Autowired
	private ExternalUserManagementDao dao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<String> getAllInternalOpsEmployeeContactNumbers() {
		return dao.getAllInternalOpsEmployeeContactNumbers();
	}

}