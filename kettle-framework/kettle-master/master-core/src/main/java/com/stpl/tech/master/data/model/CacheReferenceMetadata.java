package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "CACHE_REFERENCE_METADATA")
public class CacheReferenceMetadata {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CACHE_ID", unique = true, nullable = false)
    private Integer cacheId;
    @Column(name = "REFERENCE_TYPE", nullable = false, length = 50)
    private String referenceType;
    @Column(name = "REFERENCE_VALUE", nullable = false, length = 50)
    private String referenceValue;
    @Column(name = "CACHE_STATUS", nullable = false, length = 50)
    private String cacheStatus;
    @Column(name = "ADDED_BY", nullable = false, length = 50)
    private String addedBy;
    @Column(name = "UPDATED_BY", nullable = false, length = 50)
    private String updatedBy;
}
