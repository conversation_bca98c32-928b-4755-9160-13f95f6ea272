/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.filter;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPOutputStream;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;

public class GZIPResponseStream extends ServletOutputStream {
	protected ByteArrayOutputStream byteArrayOutputStream = null;
	protected GZIPOutputStream gzipoutputstream = null;
	protected boolean closed = false;
	protected HttpServletResponse response = null;
	protected ServletOutputStream output = null;

	public GZIPResponseStream(HttpServletResponse response) throws IOException {
		super();
		closed = false;
		this.response = response;
		this.output = response.getOutputStream();
		byteArrayOutputStream = new ByteArrayOutputStream();
		gzipoutputstream = new GZIPOutputStream(byteArrayOutputStream);
	}

	public void close() throws IOException {
		if (closed) {
			throw new IOException("This output stream has already been closed");
		}
		gzipoutputstream.finish();

		byte[] bytes = byteArrayOutputStream.toByteArray();

		response.addHeader("Content-Length", Integer.toString(bytes.length));
		response.addHeader("Content-Encoding", "gzip");
		output.write(bytes);
		output.flush();
		output.close();
		closed = true;
	}

	public void flush() throws IOException {
		if (closed) {
			throw new IOException("Cannot flush a closed output stream");
		}
		gzipoutputstream.flush();
	}

	public void write(int byteValue) throws IOException {
		if (closed) {
			throw new IOException("Cannot write to a closed output stream");
		}
		gzipoutputstream.write((byte) byteValue);
	}

	public void write(byte byteArray[]) throws IOException {
		write(byteArray, 0, byteArray.length);
	}

	public void write(byte byteArray[], int offset, int length) throws IOException {
		if (closed) {
			throw new IOException("Cannot write to a closed output stream");
		}
		gzipoutputstream.write(byteArray, offset, length);
	}

	public boolean closed() {
		return (this.closed);
	}

	public void reset() {
		
	}

	@Override
	public boolean isReady() {
		return this.output.isReady();
	}

	@Override
	public void setWriteListener(WriteListener listener) {
		this.output.setWriteListener(listener);
	}
}
