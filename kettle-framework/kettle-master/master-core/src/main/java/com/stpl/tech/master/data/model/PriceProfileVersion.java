package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PRICE_PROFILE_VERSION")
public class PriceProfileVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRICE_PROFILE_VERSIONS_ID")
    private Integer priceProfileVersionsId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PRICE_PROFILE_ID", referencedColumnName = "PRICE_PROFILE_DATA_ID")
    private PriceProfileData priceProfileData;

    @Column(name = "VERSION_NO")
    private Integer versionNo;

    @Column(name = "STATUS", length = 45)
    private String status;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;

}