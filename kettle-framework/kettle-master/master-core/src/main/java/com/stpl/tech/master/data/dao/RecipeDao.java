package com.stpl.tech.master.data.dao;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.recipe.model.RecipeDetail;

@Repository
public interface RecipeDao extends MongoRepository<RecipeDetail, String> {

	List<RecipeDetail> findByName(String name);

	List<RecipeDetail> findByStatus(String status);

	List<RecipeDetail> findByProduct_productIdAndStatus(int productId,String status);

	List<RecipeDetail>findByStatusAndDispensed(String status ,Boolean dispensed);

	@Query("{'product.productId' : ?0, 'dimension.infoId' : ?1}")
	List<RecipeDetail> findByProductIdAndDimension(int productId, int dimensionId);

	@Query("{'product.productId' : ?0, 'dimension.code' : ?1}")
	List<RecipeDetail> findByProductIdAndUom(int productId, String dimensionId);
	
	@Query("{'product.productId' : ?0}")
	List<RecipeDetail> findByProductId(int productId);

	List<RecipeDetail> findByRecipeId(int id);

	@Query("{'product.classification' : {'$eq' : 'SCM_PRODUCT'}}")
	public List<RecipeDetail> findAllSCM();
	
	@Query("{'product.productId' : ?0,'product.classification' : {'$eq' : 'SCM_PRODUCT'}}")
	public RecipeDetail findSCMProductById(int productId);

	@Query("{'product.productId' : ?0,'product.classification' : {'$eq' : 'SCM_PRODUCT'}}")
	List<RecipeDetail> findSCMRecipeProductById(int productId);

	@Query("{'product.classification' : {'$ne' : 'SCM_PRODUCT'}}")
	public List<RecipeDetail> findAllNonSCM();

	@Query("{'product.classification' : {'$ne' : 'SCM_PRODUCT'},'status':{'$eq' : 'ACTIVE'}}")
	public List<RecipeDetail> findAllNonSCMAndActive();

	@Query("{'product.classification' : {'$ne' : 'SCM_PRODUCT'},'status':{'$eq' : 'ACTIVE'} , 'startDate' : {'$gte' : ?0 , '$lte' : ?1} }")
	public List<RecipeDetail> findAllNonSCMAndActiveByStartdate(Date startDate,Date maxRange);

	@Query("{'product.classification' : {'$ne' : 'SCM_PRODUCT'},'name' : { $regex : ?0 ,  '$options' : 'i' }}")
	List<RecipeDetail> findRecipesContainingName(String name);

	@Query("{'product.classification' : {'$eq' : 'SCM_PRODUCT'},'name' : { $regex : ?0 ,  '$options' : 'i' }}")
	List<RecipeDetail> findSCMRecipesContainingName(String name);

	@Query("{'recipeId':{'$nin' : ?0}, 'status':{'$eq' : 'ACTIVE'}}")
	List<RecipeDetail> findRecipeDetailNotInMTS(List<Integer> recipeId);

	RecipeDetail findByProduct_productIdAndProfileAndStatus(int productId, String profile, String status);

	@Query("{'product.productId':{'$in' : ?0}, 'status':{'$eq' : 'ACTIVE'}}")
	List<RecipeDetail> findRecipeDetailOfAddOn(List<Integer> addOnProductIds);

	@Query("{'status':{'$in' : ?0}}")
	List<RecipeDetail> findByStatuses(List<String> status);

	@Query("{'product.productId' : ?0, 'dimension.infoId' : ?1,'status' : ?2}")
	List<RecipeDetail> findActiveProductsByProductIdAndDimension(int productId, int dimensionId, String active, String profile);

	@Query("{'status':{'$in' : ?0}, 'startDate' : {'$gte' : ?1 , '$lt' : ?2}}")
	List<RecipeDetail> findInProgressAndNotStartedRecipes(List<String> statusList, Date startDate, Date endDate);

	@Query("{'product.productId' : ?0,'status':{'$in' : ?1}, 'dimension.infoId' : ?2, 'profile' : ?3}")
	List<RecipeDetail> findRecipeByStatusesOfProduct(Integer productId, List<String> statusList, Integer dimensionInfoId, String profile);

	@Query("{'recipeId':{'$in' : ?0}}")
	List<RecipeDetail> findAllRecipeByRecipeIds(List<Integer> recipeIds);

	@Query("{'recipeId':{'$in' : ?0}, 'product.classification' : {'$eq' : 'SCM_PRODUCT'}}")
	List<RecipeDetail> findAllSCMProductRecipeByRecipeIds(List<Integer> recipeIds);

	@Query("{'product.productId' : { $in: ?0}, 'dimension.infoId' : { $in: ?1}, 'status' : ?2}")
	List<RecipeDetail> findByProductIdsAndDimensions(List<Integer> productIds, List<Integer> dimensionIds, String status);

}