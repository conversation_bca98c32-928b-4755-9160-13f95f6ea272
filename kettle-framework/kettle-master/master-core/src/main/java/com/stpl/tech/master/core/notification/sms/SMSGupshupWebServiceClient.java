/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.notification.sms;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import org.apache.poi.ss.formula.eval.NotImplementedException;

public final class SMSGupshupWebServiceClient extends AbstractSMSClient {

	private static SMSWebServiceClient OTP_INSTANCE;
	private static SMSWebServiceClient REGULAR_INSTANCE;
	private static SMSWebServiceClient SUBSCRIPTION_INSTANCE;

	private SMSGupshupWebServiceClient(SMSConfiguration config, SMSConfiguration shortURLConfig) {
		super(config, shortURLConfig);
	}

	public static SMSWebServiceClient getOTPInstance() {
		if (OTP_INSTANCE == null) {
			SMSConfiguration config = new SMSConfiguration("2000148769", "chaayos@123",
					"http://enterprise.smsgupshup.com/GatewayAPI/rest?");
			OTP_INSTANCE = new SMSGupshupWebServiceClient(config, null);
		}
		return OTP_INSTANCE;
	}

	public static SMSWebServiceClient getRegularInstance() {
		if (REGULAR_INSTANCE == null) {
			SMSConfiguration config = new SMSConfiguration("2000148769", "igQb3cKxV",
					"http://enterprise.smsgupshup.com/GatewayAPI/rest?");
			REGULAR_INSTANCE = new SMSGupshupWebServiceClient(config, null);
		}
		return REGULAR_INSTANCE;
	}

	public static SMSWebServiceClient getPromotionalInstance() {
		if (REGULAR_INSTANCE == null) {
			SMSConfiguration config = new SMSConfiguration("2000155363", "q1nb4WKJl",
					"http://enterprise.smsgupshup.com/GatewayAPI/rest?");
			REGULAR_INSTANCE = new SMSGupshupWebServiceClient(config, null);
		}
		return REGULAR_INSTANCE;
	}

	public static SMSWebServiceClient getSubscriptionInstance() {
		if (SUBSCRIPTION_INSTANCE == null) {
			SMSConfiguration config = new SMSConfiguration("2000148769", "igQb3cKxV",
					"http://enterprise.smsgupshup.com/GatewayAPI/rest?");
			SUBSCRIPTION_INSTANCE = new SMSGupshupWebServiceClient(config, null);
		}
		return SUBSCRIPTION_INSTANCE;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.kettle.core.notification.sms.SMSWebServiceClient#getRequest
	 * (java.lang.String, java.lang.String)
	 */
	@Override
	public String getSMSRequest(String message, String contactNumber) throws UnsupportedEncodingException {
		String data = "";
		data += "method=sendMessage";
		data += "&userid=" + config.getUserId(); // your loginId
		data += "&password=" + URLEncoder.encode(config.getPassCode(), "UTF-8");
		data += "&msg=" + URLEncoder.encode(message, "UTF-8");
		data += "&send_to=" + URLEncoder.encode(contactNumber, "UTF-8");
		data += "&v=1.1";
		data += "&msg_type=TEXT"; // Can by "FLASH" or "UNICODE_TEXT" or
									// “BINARY”
		data += "&auth_scheme=PLAIN";
		return data;
	}

	@Override
	public boolean checkSuccess(String response) {
		return response != null && response.contains("success");
	}

	@Override
	public ShortUrlData getShortUrl(String url) throws UnsupportedEncodingException {
		throw new NotImplementedException("SMSGupshupWebServiceClient does not support short urls");
	}

	@Override
	public void updateShortUrl(ShortUrlData url) throws IOException {
		throw new NotImplementedException("SMSGupshupWebServiceClient does not support short urls");
	}

	@Override
	public  String getOTPRequestViaIVR(String token , String contactNumber, String ivrId ) throws IOException {
		throw  new NotImplementedException("SMSGupshupWebServiceClient does not support IVR");
	}

}