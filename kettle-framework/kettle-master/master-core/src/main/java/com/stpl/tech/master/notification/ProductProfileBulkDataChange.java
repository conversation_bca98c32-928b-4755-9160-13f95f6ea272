package com.stpl.tech.master.notification;

import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Objects;

public class ProductProfileBulkDataChange extends EmailNotification {

    private EnvType envType;
    private List<String> toEmails;

    private ProductProfileDataBulkUpdateTemplate changedTemplate;

    public ProductProfileBulkDataChange(EnvType envType, List<String> toEmails, ProductProfileDataBulkUpdateTemplate changedTemplate) {
        this.envType = envType;
        this.toEmails = toEmails;
        this.changedTemplate = changedTemplate;
    }

    @Override
    public String[] getToEmails() {
        if (AppUtils.isDev(envType)) {
            return new String[]{"<EMAIL>"};
        } else {
            if (Objects.nonNull(toEmails) && toEmails.size() > 0) {
                toEmails.add("<EMAIL>");
                toEmails.add("<EMAIL>");
                toEmails.add("<EMAIL>");
            }
            return this.toEmails.toArray(new String[0]);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Bulk product price profile data has been updated at " +
                AppUtils.getCurrentTimeISTStringWithoutMS();
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            if (Objects.nonNull(changedTemplate)) {
                return changedTemplate.getContent();
            }
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
        return null;
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
