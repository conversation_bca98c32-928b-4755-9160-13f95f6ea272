package com.stpl.tech.master.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;


@Getter
@Setter
public class UnitProductPriceCategoryDetail {

    private Integer unitId;
    private String unitName;
    private Integer productId;
    private String productName;
    private Integer dimensionCode;
    private String dimension;
    private String unitCategory;
    private Integer pricingProfile;
    private Integer brandId;
    private String unitRegion;
    private String rtlCode;
    private String rlCode;
    private String productStatus;
    private Integer unitProductMappingId;
    private String unitProductMappingStatus;
    private Integer unitProductPriceId;
    private String unitProductPricingStatus;
    private BigDecimal price;
    private String aliasProductName;

    private String pricingCategory;

    public UnitProductPriceCategoryDetail() {
    }

    public UnitProductPriceCategoryDetail(Integer unitId, String unitName,Integer productId,String productName,Integer dimensionCode,String dimension,
                                          String unitCategory, Integer pricingProfile, Integer brandId, String unitRegion,
                                          String rtlCode, String rlCode, String productStatus, Integer unitProductMappingId,
                                          String unitProductMappingStatus,
                                          Integer unitProductPriceId, String unitProductPricingStatus,
                                          BigDecimal price,
                                          String aliasProductName,String pricingCategory
    ) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.productId = productId;
        this.productName = productName;
        this.dimensionCode = dimensionCode;
        this.dimension = dimension;
        this.unitCategory = unitCategory;
        this.pricingProfile = pricingProfile;
        this.brandId = brandId;
        this.unitRegion = unitRegion;
        this.rtlCode = rtlCode;
        this.rlCode = rlCode;
        this.productName = productName;
        this.productStatus = productStatus;
        this.unitProductMappingId = unitProductMappingId;
        this.unitProductMappingStatus = unitProductMappingStatus;
        this.unitProductPriceId = unitProductPriceId;
        this.unitProductPricingStatus = unitProductPricingStatus;
        this.price = price;
        this.aliasProductName = aliasProductName;
        this.pricingCategory = pricingCategory;

    }

    public UnitProductPriceCategoryDetail(Integer unitId, String unitName, Integer productId, String productName, Integer dimensionCode, String dimension,
                                       String unitCategory, Integer pricingProfile, Integer brandId, String unitRegion, String rtlCode, String rlCode, String productStatus, Integer unitProductMappingId,
                                       String unitProductMappingStatus, Integer unitProductPriceId, String unitProductPricingStatus, BigDecimal price,String aliasProductName) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.unitCategory = unitCategory;
        this.pricingProfile = pricingProfile;
        this.brandId = brandId;
        this.unitRegion = unitRegion;
        this.productId = productId;
        this.rtlCode = rtlCode;
        this.rlCode = rlCode;
        this.productName = productName;
        this.productStatus = productStatus;
        this.unitProductMappingId = unitProductMappingId;
        this.unitProductMappingStatus = unitProductMappingStatus;
        this.dimensionCode = dimensionCode;
        this.unitProductPriceId = unitProductPriceId;
        this.dimension = dimension;
        this.unitProductPricingStatus = unitProductPricingStatus;
        this.price = price;
        this.aliasProductName = aliasProductName;



    }

    @Override
    public String toString() {
        return "UnitProductPriceCategoryDetail{" +
                ", unitId=" + unitId +
                ", unitName='" + unitName + '\'' +
                ", productId=" + productId +
                ", productName='" + productName + '\'' +
                ", dimensionCode=" + dimensionCode +
                ", dimension='" + dimension + '\'' +
                ", unitCategory='" + unitCategory + '\'' +
                ", pricingProfile=" + pricingProfile +
                ", brandId=" + brandId +
                ", unitRegion='" + unitRegion + '\'' +
                ", rtlCode='" + rtlCode + '\'' +
                ", rlCode='" + rlCode + '\'' +
                ", productStatus='" + productStatus + '\'' +
                ", unitProductMappingId=" + unitProductMappingId +
                ", unitProductMappingStatus='" + unitProductMappingStatus + '\'' +
                ", unitProductPriceId=" + unitProductPriceId +
                ", unitProductPricingStatus='" + unitProductPricingStatus + '\'' +
                ", price=" + price +
                ", aliasProductName=" + aliasProductName +
                ", pricingCategory=" + pricingCategory +
                '}';
    }
}
