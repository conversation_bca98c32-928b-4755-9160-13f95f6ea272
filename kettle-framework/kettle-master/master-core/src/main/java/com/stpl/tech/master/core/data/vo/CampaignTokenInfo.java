package com.stpl.tech.master.core.data.vo;

import com.stpl.tech.master.core.external.acl.service.TokenDao;
import io.jsonwebtoken.Claims;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class CampaignTokenInfo implements TokenDao, Serializable {

    private static final long serialVersionUID = -2858429883316871086L;
    private Integer campaignId;

    public CampaignTokenInfo(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public Map<String, Object> createClaims() {
        Map<String, Object> claims = new HashMap<>();
        claims.put("campaignId", this.campaignId);
        return claims;
    }

    @Override
    public void parseClaims(Claims claims) {
        this.campaignId = claims.get("campaignId", Integer.class);
    }

    @Override
    public String toString() {
        return "CampaignTokenInfo{" +
                "campaignId=" + campaignId +
                '}';
    }
}
