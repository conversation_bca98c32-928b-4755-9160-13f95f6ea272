package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.model.UnitEmpData;

import java.util.Map;
import java.util.Set;

public interface EmployeeManagementService {

    public Integer syncEmployeeData();

    /**
     * Resume employee sync from a specific employee code
     * @param startFromEmployeeCode The employee code to start processing from
     * @return Number of employees processed
     */
    public Integer syncEmployeeDataFromEmployee(String startFromEmployeeCode);

    /**
     * Utility method to find the next employee to process after a given employee code
     * @param lastProcessedEmployeeCode The employee code that was last processed
     * @return The next employee code to start from, or null if not found
     */
    public String findNextEmployeeToProcess(String lastProcessedEmployeeCode);

    public Map<String, Set<String>> lookUpDesignationDepartment();

    public Map<String, Set<String>> getDesignationDepartmentMapping();

    public UnitEmpData getUnitEmployee(Integer unitId) throws DataNotFoundException;

    /**
     * Sync only inactive employees from Lucid API
     * @return Number of inactive employees processed
     */
    public Integer syncInactiveEmployeeData();
}
