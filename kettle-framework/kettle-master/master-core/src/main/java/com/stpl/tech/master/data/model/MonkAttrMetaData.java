package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 12-01-2018.
 */
@Entity
@Table(name = "MONK_CONFIGURATION_ATTR")
public class MonkAttrMetaData {

    private static final long serialVersionUID = -1921810774766928272L;

    private Integer attrId;
    private String attr;
    private String scope;
    private String type;
    private String label;
    private String status;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ATTR_DATA_ID", unique = true, nullable = false)
    public Integer getAttrId() {
        return attrId;
    }

    public void setAttrId(Integer attrId) {
        this.attrId = attrId;
    }


    public void setType(String type) {
        this.type = type;
    }

    public void setLabel(String label) {
        this.label = label;
    }


    @Column(name = "SCOPE", nullable = false)
    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    @Column(name = "ATTR", nullable = false)
    public String getAttr() {
        return attr;
    }

    public void setAttr(String attr) {
        this.attr = attr;
    }

    @Column(name = "ATTR_TYPE", nullable = false)
    public String getType() {
        return type;
    }

    @Column(name = "ATTR_LABEL", nullable = false)
    public String getLabel() {
        return label;
    }

    @Column(name = "ATTR_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
