package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 12-01-2018.
 */
@Entity
@Table(name = "MONK_CONFIGURATION")
public class MonkConfigurationData {

    private static final long serialVersionUID = -1921810774766928272L;

    private Integer confId;
    private Integer unitId;
    private String scope;
    private String confKey;
    private String confValue;
    private Integer updatedBy;
    private Date updatedAt;
    private String status;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CONF_DATA_ID", unique = true, nullable = false)
    public Integer getConfId() {
        return confId;
    }

    public void setConfId(Integer confId) {
        this.confId = confId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "SCOPE", nullable = false)
    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    @Column(name = "CONF_KEY", nullable = false)
    public String getConfKey() {
        return confKey;
    }

    public void setConfKey(String confKey) {
        this.confKey = confKey;
    }

    @Column(name = "CONF_VALUE", nullable = false)
    public String getConfValue() {
        return confValue;
    }

    public void setConfValue(String confValue) {
        this.confValue = confValue;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = false)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "LAST_UPDATED_AT", nullable = false)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "CONF_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
