package com.stpl.tech.master.core.service;

import com.stpl.tech.master.domain.model.AccessControlList;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.PreAuthApi;
import com.stpl.tech.master.domain.model.UserRole;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 08-07-2016.
 */
public interface AccessControlService {

    public List<AccessControlList> getAccessControls(ApplicationName appName);

    public List<PreAuthApi> getAllPreAuthenticatedApi();

    public String addPreAuthenticatedApi(PreAuthApi api);

    public boolean deactivatePreAuthenticatedApi(int apiId);

    public boolean activatePreAuthenticatedApi(int apiId);

    public boolean addAccessControl(AccessControlList accessControlList);

    public boolean deactivateAccessControl(int aclId);

    public boolean activateAccessControl(int aclId);

    List<UserRole> getUserRolesByApplication(ApplicationName appName);
}
