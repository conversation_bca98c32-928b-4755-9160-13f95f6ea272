/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * CompanyDetail generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CANCELLATION_REASON")
public class CancellationReasonData implements java.io.Serializable {

	private Integer cancellationReasonId;
	private String code;
	private String source;
	private String description;
	private String noWastage;
	private String partialWastage;
	private String completeWastage;
	private String status;

	public CancellationReasonData() {
	}


	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CANCELLATION_REASON_ID", unique = true, nullable = false)
	public Integer getCancellationReasonId() {
		return this.cancellationReasonId;
	}

	public void setCancellationReasonId(Integer cancellationReasonId) {
		this.cancellationReasonId = cancellationReasonId;
	}

	@Column(name = "CANCELLATION_REASON_CODE", nullable = false, length = 5000)
	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	@Column(name = "ORDER_SOURCE", nullable = false)
	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Column(name = "NO_WASTAGE", nullable = false)
	public String getNoWastage() {
		return this.noWastage;
	}

	public void setNoWastage(String noWastage) {
		this.noWastage = noWastage;
	}


	@Column(name = "CANCELLATION_REASON_DESC", nullable = false)
	public String getDescription() {
		return description;
	}


	public void setDescription(String description) {
		this.description = description;
	}


	@Column(name = "PARTIAL_WASTAGE", nullable = false)
	public String getPartialWastage() {
		return partialWastage;
	}


	public void setPartialWastage(String partialWastage) {
		this.partialWastage = partialWastage;
	}


	@Column(name = "COMPLETE_WASTAGE", nullable = false)
	public String getCompleteWastage() {
		return completeWastage;
	}


	public void setCompleteWastage(String completeWastage) {
		this.completeWastage = completeWastage;
	}


	@Column(name = "REASON_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}


	public void setStatus(String status) {
		this.status = status;
	}
}
