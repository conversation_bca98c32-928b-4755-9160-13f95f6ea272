package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitClosureState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitClosureStateRepository extends JpaRepository<UnitClosureState, Long> {
    UnitClosureState findByStateId(Long stateId);
    List<UnitClosureState> findByStateStatusOrderByStateIdAsc(String stateStatus);
}