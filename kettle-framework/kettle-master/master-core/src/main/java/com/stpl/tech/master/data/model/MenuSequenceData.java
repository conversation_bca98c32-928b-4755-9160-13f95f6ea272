package com.stpl.tech.master.data.model;

import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.util.AppConstants;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "MENU_SEQUENCE_DATA")
public class MenuSequenceData {

    private Integer menuSequenceId;
    private String menuSequenceName;
    private String getMenuSequenceDescription;
    private Integer createdBy;
    private Date creationTime;
    private Date lastUpdateTime;
    private String menuType;
    private Integer cloneId;
    private String menuApp;
    private String menuStatus = AppConstants.ACTIVE;

    private Integer menuExcelSequenceId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY )
    @Column(name = "MENU_SEQUENCE_ID", nullable = false, unique = true)
    public Integer getMenuSequenceId() {
        return menuSequenceId;
    }

    public void setMenuSequenceId(Integer menuSequenceId) {
        this.menuSequenceId = menuSequenceId;
    }

    @Column(name = "MENU_SEQUENCE_NAME", nullable = true)
    public String getMenuSequenceName() {
        return menuSequenceName;
    }

    public void setMenuSequenceName(String menuSequenceName) {
        this.menuSequenceName = menuSequenceName;
    }

    @Column(name = "MENU_SEQUENCE_DESCRIPTION", nullable = false)
    public String getGetMenuSequenceDescription() {
        return getMenuSequenceDescription;
    }

    public void setGetMenuSequenceDescription(String getMenuSequenceDescription) {
        this.getMenuSequenceDescription = getMenuSequenceDescription;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATION_TIME", nullable = true)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = true)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name = "MENU_TYPE", nullable = true)
    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    @Column(name = "CLONE_ID", nullable = true)
    public Integer getCloneId() {
        return cloneId;
    }

    public void setCloneId(Integer cloneId) {
        this.cloneId = cloneId;
    }


    @Column(name = "MENU_APP")
    public String getMenuApp() {
        return menuApp;
    }

    public void setMenuApp(String menuApp) {
        this.menuApp = menuApp;
    }

    @Column(name = "MENU_EXCEL_SEQUENCE_ID")
    public Integer getMenuExcelSequenceId() {
        return menuExcelSequenceId;
    }

    public void setMenuExcelSequenceId(Integer menuExcelSequenceId) {
        this.menuExcelSequenceId = menuExcelSequenceId;
    }

    @Column(name = "MENU_STATUS", nullable = false)
    public String getMenuStatus() { return this.menuStatus; }

    public void setMenuStatus(String menuStatus) { this.menuStatus = menuStatus; }

}
