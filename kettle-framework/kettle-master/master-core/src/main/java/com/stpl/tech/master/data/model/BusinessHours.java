/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import com.stpl.tech.util.AppConstants;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "BUSINESS_HOURS")
public class BusinessHours implements java.io.Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 2266809222944478690L;

	protected int businessHoursId;
	protected UnitDetail unitDetail;
	protected int dayOfTheWeekNumber;
	protected String dayOfTheWeek;
	protected int noOfShifts;
	protected String isOperational;
	protected String hasDelivery;
	protected String hasDineIn;
	protected String hasTakeAway;
	protected Date dineInOpeningTime;
	protected Date dineInClosingTime;
	protected Date deliveryOpeningTime;
	protected Date deliveryClosingTime;
	protected Date takeAwayOpeningTime;
	protected Date takeAwayClosingTime;
	protected Date shiftOneHandoverTime;
	protected Date shiftTwoHandoverTime;
	protected String status;
	protected Integer brandId = AppConstants.CHAAYOS_BRAND_ID;

	public BusinessHours() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "BUSINESS_HOURS_ID", unique = true, nullable = false)
	public int getBusinessHoursId() {
		return businessHoursId;
	}

	public void setBusinessHoursId(int id) {
		this.businessHoursId = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	public UnitDetail getUnitDetail() {
		return unitDetail;
	}

	public void setUnitDetail(UnitDetail unitDetail) {
		this.unitDetail = unitDetail;
	}

	@Column(name = "DAY_OF_WEEK_NUMBER", nullable = false)
	public int getDayOfTheWeekNumber() {
		return dayOfTheWeekNumber;
	}

	public void setDayOfTheWeekNumber(int dayOfTheWeekNumber) {
		this.dayOfTheWeekNumber = dayOfTheWeekNumber;
	}

	@Column(name = "DAY_OF_WEEK_TEXT", nullable = false, length = 10)
	public String getDayOfTheWeek() {
		return dayOfTheWeek;
	}

	public void setDayOfTheWeek(String dayOfTheWeek) {
		this.dayOfTheWeek = dayOfTheWeek;
	}

	@Column(name = "NO_OF_SHIFTS", nullable = false)
	public int getNoOfShifts() {
		return noOfShifts;
	}

	public void setNoOfShifts(int noOfShifts) {
		this.noOfShifts = noOfShifts;
	}

	@Column(name = "IS_OPERATIONAL", nullable = false, length = 1)
	public String getIsOperational() {
		return isOperational;
	}

	public void setIsOperational(String isOperational) {
		this.isOperational = isOperational;
	}

	@Column(name = "HAS_DELIVERY", nullable = false, length = 1)
	public String getHasDelivery() {
		return hasDelivery;
	}

	public void setHasDelivery(String hasDelivery) {
		this.hasDelivery = hasDelivery;
	}

	@Column(name = "HAS_DINE_IN", nullable = false, length = 1)
	public String getHasDineIn() {
		return hasDineIn;
	}

	public void setHasDineIn(String hasDineIn) {
		this.hasDineIn = hasDineIn;
	}

	@Column(name = "HAS_TAKE_AWAY", nullable = false, length = 1)
	public String getHasTakeAway() {
		return hasTakeAway;
	}

	public void setHasTakeAway(String hasTakeAway) {
		this.hasTakeAway = hasTakeAway;
	}

	@Temporal(TemporalType.TIME)
	@Column(name = "DINE_IN_OPEN_TIME", length = 8)
	public Date getDineInOpeningTime() {
		return dineInOpeningTime;
	}

	public void setDineInOpeningTime(Date dineInOpeningTime) {
		this.dineInOpeningTime = dineInOpeningTime;
	}

	@Temporal(TemporalType.TIME)
	@Column(name = "DINE_IN_CLOSE_TIME", length = 8)
	public Date getDineInClosingTime() {
		return dineInClosingTime;
	}

	public void setDineInClosingTime(Date dineInClosingTime) {
		this.dineInClosingTime = dineInClosingTime;
	}

	@Temporal(TemporalType.TIME)
	@Column(name = "DELIVERY_OPEN_TIME", length = 8)
	public Date getDeliveryOpeningTime() {
		return deliveryOpeningTime;
	}

	public void setDeliveryOpeningTime(Date deliveryOpeningTime) {
		this.deliveryOpeningTime = deliveryOpeningTime;
	}

	@Temporal(TemporalType.TIME)
	@Column(name = "DELIVERY_CLOSE_TIME", length = 8)
	public Date getDeliveryClosingTime() {
		return deliveryClosingTime;
	}

	public void setDeliveryClosingTime(Date deliveryClosingTime) {
		this.deliveryClosingTime = deliveryClosingTime;
	}

	@Temporal(TemporalType.TIME)
	@Column(name = "TAKE_AWAY_OPEN_TIME", length = 8)
	public Date getTakeAwayOpeningTime() {
		return takeAwayOpeningTime;
	}

	public void setTakeAwayOpeningTime(Date takeAwayOpeningTime) {
		this.takeAwayOpeningTime = takeAwayOpeningTime;
	}

	@Temporal(TemporalType.TIME)
	@Column(name = "TAKE_AWAY_CLOSE_TIME", length = 8)
	public Date getTakeAwayClosingTime() {
		return takeAwayClosingTime;
	}

	public void setTakeAwayClosingTime(Date takeAwayClosingTime) {
		this.takeAwayClosingTime = takeAwayClosingTime;
	}

	@Temporal(TemporalType.TIME)
	@Column(name = "SHIFT_ONE_HANDOVER_TIME", length = 8)
	public Date getShiftOneHandoverTime() {
		return shiftOneHandoverTime;
	}

	public void setShiftOneHandoverTime(Date shiftOneHandoverTime) {
		this.shiftOneHandoverTime = shiftOneHandoverTime;
	}

	@Temporal(TemporalType.TIME)
	@Column(name = "SHIFT_TWO_HANDOVER_TIME", length = 8)
	public Date getShiftTwoHandoverTime() {
		return shiftTwoHandoverTime;
	}

	public void setShiftTwoHandoverTime(Date shiftTwoHandoverTime) {
		this.shiftTwoHandoverTime = shiftTwoHandoverTime;
	}
	@Column(name = "BUSINESS_HOURS_STATUS", nullable = false, length = 15)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "BRAND_ID", nullable = false)
	public int getBrandId() {
		return brandId;
	}

	public void setBrandId(int brandId) {
		this.brandId = brandId;
	}
}
