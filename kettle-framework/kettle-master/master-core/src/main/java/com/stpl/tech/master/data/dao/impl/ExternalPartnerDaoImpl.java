package com.stpl.tech.master.data.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.master.data.dao.ExternalPartnerDao;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import com.stpl.tech.util.AppConstants;

@Repository
public class ExternalPartnerDaoImpl extends AbstractMasterDaoImpl implements ExternalPartnerDao {

	@Override
	public List<ExternalPartnerDetail> getExternalPartnerDetail(String status) {
		Query query = manager.createQuery("FROM ExternalPartnerDetail where partnerStatus = :partnerStatus");
		query.setParameter("partnerStatus", AppConstants.ACTIVE);
		return query.getResultList();
	}

}
