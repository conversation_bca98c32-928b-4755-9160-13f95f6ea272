/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.BrandAttributes;
import com.stpl.tech.master.data.model.BrandDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadata;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;

import java.util.List;

public interface BrandManagementDao extends AbstractMasterDao {

    UnitPartnerBrandMappingData getUnitPartnerBrandMapping(UnitPartnerBrandKey key);

    List<UnitPartnerBrandMappingData> getAllUnitPartnerBrandMapping(Integer brandId,Integer partnerId);

    List<BrandAttributes> getBrandAttributesByBrandId(int brandId);

    List<UnitPartnerBrandMappingMetadata> getAllUnitPartnerBrandMappingMetadata();

    List<UnitPartnerBrandMappingMetadata> getUnitPartnerBrandMappingMetadataByKey(UnitPartnerBrandMappingMetadataType key);

    UnitPartnerBrandMappingMetadata getUnitPartnerBrandMappingMetadata(UnitPartnerBrandMappingMetadataType key, Integer unitId, Integer partnerId, Integer brandId);

    BrandAttributes getBrandAttributesByBrandIdAndAttributeKey(Integer brandId, String attributeKey);

    UnitPartnerBrandMappingData checkUnitPartnerBrandMapping(UnitPartnerBrandKey key, Integer priceProfileId, String restaurantId);

    public Boolean inactiveAllRestMapping(UnitPartnerBrandKey unitPartnerBrandKey);

    List<BrandAttributes> getBrandAttributes();

    public void updateBrandAttributes(BrandAttributes brandAttribute);

    public List<BrandDetail> getActiveBrandDetails();
}
