package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.joda.time.DateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "LCD_MENU_IMAGES_UPLOAD_VERSIONS")
public class LcdMenuImagesUploadVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false, updatable = false)
    private Integer id;

    @Column(name = "VERSION_NO", nullable = false, precision = 10, scale = 2)
    private String versionNo;

    @Column(name = "CREATION_DATE", nullable = false, columnDefinition = "DATETIME DEFAULT CURRENT_TIMESTAMP")
    private Date creationDate;

    @Column(name = "CREATED_BY", nullable = false)
    private Integer createdBy;

}

