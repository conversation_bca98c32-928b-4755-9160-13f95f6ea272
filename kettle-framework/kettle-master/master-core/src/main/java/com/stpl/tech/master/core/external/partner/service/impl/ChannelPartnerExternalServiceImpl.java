package com.stpl.tech.master.core.external.partner.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.util.AppConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.partner.dao.ChannelPartnerDao;
import com.stpl.tech.master.core.external.partner.service.ChannelPartnerExternalService;
import com.stpl.tech.master.data.model.ChannelPartnerCommission;
import com.stpl.tech.master.data.model.MenuRecommendationMappingData;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.ProductSequenceData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.PriceProfileDetail;

@Service
public class ChannelPartnerExternalServiceImpl implements ChannelPartnerExternalService {

	@Autowired
	private ChannelPartnerDao dao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ChannelPartnerDetail> getAllChannelPartner(Date businessDate) throws DataNotFoundException {
		return dao.getAllChannelPartner(businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ChannelPartnerCommission getChannelPartnerCommission(int partnerId, Date businessDate)
			throws DataNotFoundException {
		return dao.getChannelPartnerCommission(partnerId, businessDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitChannelPartnerMappingData> getUnitChannelPartnerMappings() {
		return dao.getUnitChannelPartnerMappings();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public UnitChannelPartnerMappingData findMappingByUnitAndPartnerId(Integer unitId, Integer partnerId) {
		return dao.findMappingByUnitAndPartnerId(unitId, partnerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitChannelPartnerMenuMappingData> findMenuMappingByUnitPartnerMappingId(
			Integer unitChannelPartnerMappingId) {
		return dao.findMenuMappingByUnitPartnerBrandMappingId(unitChannelPartnerMappingId, unitChannelPartnerMappingId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings() {
		return dao.getUnitPartnerMenuMappings();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings(Integer unitChannelPartnerMappingId,Integer brandId) {
		return dao.getUnitPartnerMenuMappings(unitChannelPartnerMappingId,brandId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<UnitChannelPartnerMenuMappingData> findMenuMappingByUnitPartnerBrandMappingId(
			Integer unitChannelPartnerMappingId, Integer brandId) {
		return dao.findMenuMappingByUnitPartnerBrandMappingId(unitChannelPartnerMappingId, brandId);
	}

	@Override

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductGroupData> getProductGroupByTagAndType(String tag, String type) {
		return dao.getProductGroupByTagAndType(tag, type);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductGroupData> getProductGroupByNameAndType(String name, String type, String tag) {
		return dao.getProductGroupByNameAndType(name, type, tag);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductSequenceData> getProductSequenceByGroupId(Integer groupId) {
		return dao.getProductSequenceByGroupId(groupId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductSequenceData> getProductSequenceByGroupIds(List<Integer> groupIds) {
		return dao.getProductSequenceByGroupIds(groupIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductSequenceData> getAllProductSequenceByGroupId(Integer groupId) {
		return dao.getAllProductSequenceByGroupId(groupId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ProductSequenceData getProductSequenceByGroupIdAndProductId(Integer groupId, Integer productId) {
		return dao.getProductSequenceByGroupIdAndProductId(groupId, productId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<MenuSequenceMappingData> getAllMenuSequenceMappingByMenuSequenceId(Integer menuSequenceId) {
		return dao.getAllMenuSequenceMappingByMenuSequenceId(menuSequenceId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductGroupData> getAllProductGroupByGroupIds(List<Integer> groupIds) {
		return dao.getAllProductGroupByGroupIds(groupIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductSequenceData> getAllSequencedProductInGroupForCloning(Integer cloneId) {
		return dao.getAllSequencedProductInGroupForCloning(cloneId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<MenuSequenceMappingData> getAllSequencedProductInMenuForCloning(Integer cloneId) {
		return dao.getAllSequencedProductInMenuForCloning(cloneId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<MenuRecommendationMappingData> getAllRecommendationMapping(Integer recommendationId) {
		return dao.getAllRecommendationMapping(recommendationId, AppConstants.ACTIVE);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getChannelPartnerIdsForMenuSequence(Integer menuSequenceId) {
		return dao.getChannelPartnerIdsForMenuSequence(menuSequenceId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getUnitIdsForMenu(List<Integer> channelPartnerIds, Integer kettlePartnerId) {
		return dao.getUnitIdsForMenu(channelPartnerIds, kettlePartnerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getMenuSequenceIdsAsPerMenuApp(MenuApp menuApp) {
		return dao.getMenuSequenceIdsAsPerMenuApp(menuApp);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ProductGroupData> getProductGroupDataAsPerIdAndMenuApp(List<Integer> ids, MenuApp menuApp) {
		return dao.getProductGroupDataAsPerIdAndMenuApp(ids, menuApp);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> getCategoryIdsMenuSequenceMapping(List<Integer> menuSequenceIds) {
		return dao.getCategoryIdsMenuSequenceMapping(menuSequenceIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public PriceProfileDetail getUnitPartnerProfilePrice(Integer priceProfileId) {
		return dao.getUnitPartnerProfilePrice(priceProfileId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PriceProfileDetail> getUnitPartnerProfilePrice() {
		return dao.getUnitPartnerProfilePrice();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addProfilePriceMapping(PriceProfileDetail detail) {
		return dao.addProfilePriceMapping(detail);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PriceProfileDetail> getProfilePrice(String priceProfileStrategy) {
		return dao.getProfilePrice(priceProfileStrategy);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateProfilePriceMapping(IdCodeName detail) {
		return dao.updateProfilePriceMapping(detail);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles() {
		return dao.getAllActivePriceProfiles();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles(Integer priceProfileId) {
		return dao.getAllActivePriceProfiles(priceProfileId);
	}

}
