/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.AddressInfo;
import com.stpl.tech.master.data.model.ApplicationVersionDetailData;
import com.stpl.tech.master.data.model.ApplicationVersionEvent;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.Denomination;
import com.stpl.tech.master.data.model.PaymentMode;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.data.model.UnitProductPricing;
import com.stpl.tech.master.data.model.UnitProductSyncLog;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationVersionDetailDomainData;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitPaymentModeMappingDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.domain.model.UnitToPartnerEdcMappingDetail;
import com.stpl.tech.master.monk.configuration.model.MonkAttr;
import com.stpl.tech.master.monk.configuration.model.MonkConfiguration;
import com.stpl.tech.master.monk.configuration.model.MonkConfigurationValue;

import java.util.List;
import java.util.Map;

public interface UnitManagementDao{

	public Unit addUnit(Unit unit) throws DataUpdationException;

	public Unit changeUnitLiveStatus(int unitId, boolean status);

	public Unit updateUnit(Unit unit) throws DataUpdationException;

	public boolean updateUnitBusinessHours(CafeTimingChangeRequest unitRequest) throws DataUpdationException;

	public Unit changeUnitStatus(int unitId, UnitStatus status) throws DataUpdationException;

	public Unit updateProductMapping(int unitId, List<ProductPrice> prices) throws DataUpdationException;

	public BusinessDivision getBusinessDivision(int businessDivId);

	public com.stpl.tech.master.data.model.TaxProfile getTaxProfile(int taxProfileId);

	public AddressInfo updateAddress(Address address, AddressInfo addressInfo);

    public MonkConfiguration addMonkConfiguration(MonkConfiguration configuration);

    public List<MonkAttr> getMonkMetadata();

    public List<MonkConfigurationValue> getUnitMonkConfigurationData(int unitId);

	public Unit changeUnitStatusForDineInAndChaayos(int unitId, Boolean dineIn, Boolean chaayos) throws DataUpdationException;

	PaymentMode addPaymentMethod(PaymentMode paymentMode);

    List<com.stpl.tech.master.domain.model.PaymentMode> getPaymentMethod();

	Boolean updatePaymentMode(PaymentMode paymentMode);

	PaymentMode getPaymentMethod(PaymentMode paymentMode);

	List<Denomination> getDenomination(PaymentMode paymentModeId);

	void updatePaymentModeMappingMethod(UnitPaymentModeMappingDetail modeMapping);

	List<UnitPaymentModeMappingDetail> getPaymentModeMapping(Integer id);

    public Unit updateUnitF9Flag(Integer unitId);

	public boolean addPartnerEdcMapping(UnitToPartnerEdcMappingDetail unitToPartnerEdcMappingDetail);

	public void updatePartnerEdcMapping(UnitToPartnerEdcMappingDetail unitToPartnerEdcMappingDetailList);

	public List<ApplicationVersionDetailData> getUnitVersionMapping();

	public List<ApplicationVersionEvent> getUnitNewVersionMapping(Integer unitId, Integer terminalId);

	void addUnitApplicationVersionDetailStatus(Integer unitId, Integer terminalId,ApplicationVersionDetailDomainData applicationVersionDetailDomainData);

	UnitDetail findById(Integer unitId);

    Map<Integer, Map<Integer, UnitProductMapping>> getUnitProductMappingForUnits(List<Integer> unitIds);

	List<UnitProductMapping> updateUnitProductMapping(List<UnitProductMapping> changedUnitProductMappings);

	void updateUnitProductPricings(List<UnitProductPricing> changedUpp);

	void saveLog(UnitProductSyncLog unitProductSyncLog);
}
