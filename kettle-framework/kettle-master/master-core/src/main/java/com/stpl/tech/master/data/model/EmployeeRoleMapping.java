/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

// Generated 20 Jul, 2015 5:25:16 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

/**
 * EmployeeUnitMapping generated by hbm2java
 */
@Entity
@Table(name = "EMPLOYEE_ROLE_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "EMPLOYEE_ID", "ROLE_ID",
		"MAPPING_STATUS" }))
public class EmployeeRoleMapping implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6413278349556233140L;
	private Integer empRoleMappingId;
	private Integer employeeId;
	private UserRoleData roleDetail;
	private String mappingStatus;
	private Integer updatedBy;
	private Date lastUpdateTime;
	private Integer brandId;

	public EmployeeRoleMapping() {
	}

	public EmployeeRoleMapping(Integer employeeId, UserRoleData roleDetail, String mappingStatus, Date lastUpdateTime) {
		super();
		this.employeeId = employeeId;
		this.roleDetail = roleDetail;
		this.mappingStatus = mappingStatus;
		this.lastUpdateTime = lastUpdateTime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EMPLOYEE_ROLE_MAPPING_ID", unique = true, nullable = false)
	public Integer getEmpRoleMappingId() {
		return this.empRoleMappingId;
	}

	public void setEmpRoleMappingId(Integer empUnitKeyId) {
		this.empRoleMappingId = empUnitKeyId;
	}

	@Column(name = "EMPLOYEE_ID", nullable = false)
	public Integer getEmployeeId() {
		return this.employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ROLE_ID", nullable = false)
	public UserRoleData getRoleDetail() {
		return roleDetail;
	}

	public void setRoleDetail(UserRoleData roleDetail) {
		this.roleDetail = roleDetail;
	}

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return this.mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	@Column(name = "UPDATED_BY")
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
	public Date getLastUpdateTime() {
		return this.lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "BRAND_ID", nullable = false)
	public Integer getBrandId() { return this.brandId; }

	public void setBrandId(Integer brandId) { this.brandId = brandId; }
}
