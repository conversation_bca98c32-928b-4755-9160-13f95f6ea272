package com.stpl.tech.master.core.service;

import com.google.gson.Gson;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.partner.dao.ChannelPartnerDao;
import com.stpl.tech.master.data.dao.MasterMetadataDao;
import com.stpl.tech.master.data.dao.MenuExcelSequenceDao;
import com.stpl.tech.master.data.model.GroupRecommendationMapping;
import com.stpl.tech.master.data.model.GroupUpsellingMapping;
import com.stpl.tech.master.data.model.MenuExcelSequence;
import com.stpl.tech.master.data.model.MenuExcelUploadEvent;
import com.stpl.tech.master.data.model.MenuRecommendationData;
import com.stpl.tech.master.data.model.MenuSequenceData;
import com.stpl.tech.master.data.model.MenuSequenceGroupMapping;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.data.model.UnitGroupMappingData;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.MenuExcelUploadResponse;
import com.stpl.tech.master.domain.model.MenuGroupTypes;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.PriceProfileDetail;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitChannelPartnerMapping;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.menu.excel.GroupMetadataUpdateDomain;
import com.stpl.tech.master.domain.model.menu.excel.MenuExcelData;
import com.stpl.tech.master.domain.model.menu.excel.MenuExcelSheets;
import com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil;
import com.stpl.tech.master.domain.model.menu.excel.MenuGroupData;
import com.stpl.tech.master.domain.model.menu.excel.sheets.CafeGroupMappingSheet;
import com.stpl.tech.master.domain.model.menu.excel.sheets.GroupUpsellingMappingSheet;
import com.stpl.tech.master.domain.model.menu.excel.sheets.MenuSequenceGroupMappingSheet;
import com.stpl.tech.master.domain.model.menu.excel.sheets.MenuSequenceSlotColumns;
import com.stpl.tech.master.domain.model.menu.excel.sheets.RecommendationMappingSheet;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil.NEW_CHANGE_LOG;
import static com.stpl.tech.master.domain.model.menu.excel.MenuExcelUtil.UPDATE_CHANGE_LOG;
import static com.stpl.tech.util.AppUtils.validateValue;


@Service
@Log4j2
public class PartnerMenuMetadataServiceImpl implements PartnerMenuMetadataService{
    @Autowired
    private MenuExcelParser menuExcelParser;

    @Autowired
    private MenuExcelSequenceDao menuExcelSequenceDao;

    @Autowired
    private MasterMetadataDao masterMetadataDao;

    @Autowired
    private FileArchiveService fileArchiveService;


    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private ChannelPartnerDao channelPartnerDao;

    @Autowired
    private MasterProperties props;



    private  String getUnitChannelPartnerMenuMappingKey(Integer unitChannelPartnerMappingId , String menuApp , String daySlot , Integer brandId){
        return unitChannelPartnerMappingId + "_" + menuApp + "_" + daySlot + "_" + brandId;
    }

    private void reloadUnitPartnerMenuMappingCache(Set<Integer> unitChannelPartnerMappingIds , Integer brandId){
        List<UnitChannelPartnerMenuMappingData> mappings = channelPartnerDao.getUnitPartnerMenuMappingsByChannelPartnerMappingId(unitChannelPartnerMappingIds.stream().toList(),
                brandId);
        Map<Integer,List<UnitChannelPartnerMenuMappingData>> menuMappingsByUnitPartnerId = mappings.stream().collect(Collectors.groupingBy(
                UnitChannelPartnerMenuMappingData::getUnitPartnerMappingId
        ));
        List<Integer> priceProfileIds = mappings.stream().filter(mapping ->Objects.nonNull(mapping.getPriceProfileId())).map(mapping -> mapping.getPriceProfileId())
                .collect(Collectors.toList());
        Map<Integer, PriceProfileDetail> priceProfilesData=new HashMap<>();
        if(!CollectionUtils.isEmpty(priceProfileIds)){
            priceProfilesData = channelPartnerDao.getAllActivePriceProfiles(priceProfileIds);
        }
        log.info("Number Of Unit Channel Partner Mapping Ids to Reload :: {}" , unitChannelPartnerMappingIds.size());
        for(Integer key : menuMappingsByUnitPartnerId.keySet()){
            masterDataCache.loadSpecificUnitPartnerMenuMapping(menuMappingsByUnitPartnerId.get(key), priceProfilesData);
        }
    }

    private List<MenuType> getAllDaySlots(){
        return new ArrayList<>(Arrays.asList(MenuType.DAY_SLOT_BREAKFAST,MenuType.DAY_SLOT_LUNCH,MenuType.DAY_SLOT_EVENING,MenuType.DAY_SLOT_DINNER
        ,MenuType.DAY_SLOT_POST_DINNER,MenuType.DAY_SLOT_OVERNIGHT, MenuType.DEFAULT));
    }



    private void updateMappingDataForGroups(List<Integer> groupIds , Map<UnitPartnerBrandKey, List<GroupMetadataUpdateDomain>> groupMetadataUpdateDomainMap ,
                                            Integer uploadedBy , Set<String> errorsList ) throws DataUpdationException {
        //for groupMetadataUpdateDomainMap unitId in unitPartnerBrandKey is Group Id
        Map<Integer, List<Integer>> unitsByGroupMap = new HashMap<>();

        Map<Integer,UnitGroupMappingData> groupByUnitMap  = new HashMap<>();
        masterMetadataDao.getUnitGroupMappings(groupIds).stream().forEach(mapping ->{
            Arrays.stream(mapping.getUnitIds().trim().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt).forEach(unitId ->{
                               if(!groupByUnitMap.containsKey(unitId)){
                                   groupByUnitMap.put(unitId,mapping);
                               }else{
                                   UnitGroupMappingData currentGroup = groupByUnitMap.get(unitId);
                                   if(mapping.getPriority() > currentGroup.getPriority()){
                                       groupByUnitMap.put(unitId,mapping);
                                   }else if(mapping.getPriority() == currentGroup.getPriority() && AppUtils.isBefore(currentGroup.getCreationTime()
                                   , mapping.getCreationTime())){
                                       groupByUnitMap.put(unitId,mapping);
                                   }

                               }
                    });
        });
        for(Integer unitId : groupByUnitMap.keySet()){
            UnitGroupMappingData unitGroupMappingData = groupByUnitMap.get(unitId);
            if(!unitsByGroupMap.containsKey(unitGroupMappingData.getGroupId())){
                unitsByGroupMap.put(unitGroupMappingData.getGroupId(),new ArrayList<>());
            }
            unitsByGroupMap.get(unitGroupMappingData.getGroupId()).add(unitId);
        }
        List<Integer> unitIds = unitsByGroupMap.values().stream().flatMap(List::stream).toList();
        Map<UnitPartnerBrandKey, UnitChannelPartnerMapping> unitChannelPartnerMappingMap = masterDataCache.getActiveUnitChannelPartnerMapping()
                .stream().filter(unitChannelPartnerMapping -> unitIds.contains(unitChannelPartnerMapping.getUnit().getId()))
                .collect(Collectors.toMap(
                        mapping -> new UnitPartnerBrandKey(mapping.getUnit().getId(),AppConstants.CHAAYOS_BRAND_ID,mapping.getChannelPartner().getId()),
                        Function.identity()));
        List<Integer> unitChannelPartnerMappingIds = unitChannelPartnerMappingMap.values().stream().map(UnitChannelPartnerMapping::getId).toList();
        Map<String, UnitChannelPartnerMenuMappingData> unitChannelPartnerMenuMappingDataMap = masterMetadataDao.getUnitPartnerMenuMappings(unitChannelPartnerMappingIds)
                .stream().collect(Collectors.toMap(mapping -> getUnitChannelPartnerMenuMappingKey(mapping.getUnitPartnerMappingId(),
                        mapping.getMenuApp() , mapping.getMenuType(),mapping.getBrandId()),Function.identity(), (m1,m2) -> { return  m2;}));
        List<UnitChannelPartnerMenuMappingData> updateUnitChannelPartnerMenuMappingDataList = new ArrayList<>();
        Map<Integer,List<Integer>> updatedUnitChannelPartnerMappingByBrand = new HashMap<>();
        List<UnitPartnerBrandKey> filteredLists = new ArrayList<>();
        for(UnitPartnerBrandKey key : groupMetadataUpdateDomainMap.keySet()){

            Integer groupId = key.getUnitId();   // unitId is groupId here
            List<Integer> groupUnitIds = unitsByGroupMap.get(groupId);
            if(CollectionUtils.isEmpty(groupUnitIds)){
                errorsList.add("Invalid Group ::: " + groupId);
            }
            List<GroupMetadataUpdateDomain> groupMetadataUpdateDomainList = groupMetadataUpdateDomainMap.get(key);
            for(GroupMetadataUpdateDomain groupMetadataUpdateDomain : groupMetadataUpdateDomainList){
                Integer excelMenuSequenceId = groupMetadataUpdateDomain.getMenuSequenceId();
                Integer upsellingId = groupMetadataUpdateDomain.getUpsellingId();
                /*List<String> daySlots = MenuType.DAY_SLOT_ALL.equals(groupMetadataUpdateDomain.getDaySlot()) ? getAllDaySlots().stream().
                        map(menuType -> menuType.name()).toList() : new ArrayList<>(Arrays.asList(groupMetadataUpdateDomain.getDaySlot().name()));*/
                String daySlot = groupMetadataUpdateDomain.getDaySlot().name();
                String menuApp = groupMetadataUpdateDomain.getMenuApp();
                Map<String,MenuSequenceData> menuSequenceDataMapBySlot = masterMetadataDao.getMenuSequenceDataByExcelSequenceId(excelMenuSequenceId)
                        .stream().collect(Collectors.toMap(menu -> menu.getMenuType(),Function.identity()));
                    for(Integer unitId : groupUnitIds){
                        UnitPartnerBrandKey unitPartnerBrandKey = new UnitPartnerBrandKey(unitId,AppConstants.CHAAYOS_BRAND_ID,key.getPartnerId());
                        if(!unitChannelPartnerMappingMap.containsKey(unitPartnerBrandKey)){
                            if(!filteredLists.contains(unitPartnerBrandKey)){
                                filteredLists.add(unitPartnerBrandKey);
                            }
                            continue;
                        }
                        UnitChannelPartnerMapping unitChannelPartnerMapping = unitChannelPartnerMappingMap.get(unitPartnerBrandKey);
                        if(!updatedUnitChannelPartnerMappingByBrand.containsKey(key.getBrandId())){
                            updatedUnitChannelPartnerMappingByBrand.put(key.getBrandId(),new ArrayList<>());
                        }
                        Integer newMenuSequenceId = Objects.nonNull(menuSequenceDataMapBySlot.get(daySlot)) ?
                                menuSequenceDataMapBySlot.get(daySlot).getMenuSequenceId() : null;
                        updatedUnitChannelPartnerMappingByBrand.get(key.getBrandId()).add(unitChannelPartnerMapping.getId());
                        updateUnitPartnerMenuMapping(unitChannelPartnerMapping,newMenuSequenceId,upsellingId,daySlot,menuApp,
                                unitChannelPartnerMenuMappingDataMap.get(getUnitChannelPartnerMenuMappingKey(unitChannelPartnerMapping.getId(),menuApp,daySlot,key.getBrandId())),
                                key.getBrandId(),uploadedBy,updateUnitChannelPartnerMenuMappingDataList,groupMetadataUpdateDomain.getRemoveMenuSequenceMapping(),
                                groupMetadataUpdateDomain.getRemoveUpsellingMapping());

                    }
            }


        }
        if(!CollectionUtils.isEmpty(updateUnitChannelPartnerMenuMappingDataList)){
            masterMetadataDao.addAll(updateUnitChannelPartnerMenuMappingDataList);
        }

        for(Integer brandId : updatedUnitChannelPartnerMappingByBrand.keySet()){
            if(!CollectionUtils.isEmpty(updatedUnitChannelPartnerMappingByBrand.get(brandId))){
                reloadUnitPartnerMenuMappingCache(updatedUnitChannelPartnerMappingByBrand.get(brandId).stream().collect(Collectors.toSet()),
                        brandId);
            }
        }
        for(UnitPartnerBrandKey key : filteredLists){
            errorsList.add("Filered Row For Unit Id : " + key.getUnitId() +  " Partner Id : {} " + key.getPartnerId() + "As unit channel partner mapping is missing!!");
        }

    }

    private void updateUnitPartnerMenuMapping(UnitChannelPartnerMapping unitChannelPartnerMapping, Integer newMenuSequenceId , Integer upsellingId , String daySlot ,
                                              String menuApp , UnitChannelPartnerMenuMappingData unitChannelPartnerMenuMappingData , Integer brandId ,
                                              Integer uploadedBy , List<UnitChannelPartnerMenuMappingData> updateUnitChannelPartnerMenuMappingDataList
    , Boolean removeMenuSequenceMapping , Boolean removeUpsellingMapping) throws DataUpdationException {

        if(Objects.nonNull(unitChannelPartnerMenuMappingData)){
            if(Objects.nonNull(newMenuSequenceId)){
                unitChannelPartnerMenuMappingData.setMenuSequenceId(newMenuSequenceId);
            }else  if(Boolean.TRUE.equals(removeMenuSequenceMapping)){
                unitChannelPartnerMenuMappingData.setStatus(AppConstants.IN_ACTIVE);
            }
            if(Objects.nonNull(upsellingId)){
                unitChannelPartnerMenuMappingData.setMenuRecommendationSequenceId(upsellingId);
            }else if(Boolean.TRUE.equals(removeUpsellingMapping)){
                unitChannelPartnerMenuMappingData.setMenuRecommendationSequenceId(null);
            }
            unitChannelPartnerMenuMappingData.setUpdatedAt(AppUtils.getCurrentTimestamp());
            unitChannelPartnerMenuMappingData.setUpdatedBy(uploadedBy);
            updateUnitChannelPartnerMenuMappingDataList.add(unitChannelPartnerMenuMappingData);
        }else {
            if(Objects.isNull(newMenuSequenceId)){
                //throw  new DataUpdationException("Menu Sequence Not Found While Mapping Upselling : " + upsellingId + " for Day Slot = " + daySlot);
                log.info("Menu Sequence Not Found While Mapping Upselling : " + upsellingId + " for Day Slot = " + daySlot);
                return;
            }
            UnitChannelPartnerMenuMappingData currentMappingData = masterMetadataDao.getChannelPartnerMenuMapping(unitChannelPartnerMapping.getId(),
                    newMenuSequenceId,menuApp,daySlot,brandId);
            UnitChannelPartnerMenuMappingData unitChannelPartnerMenuMappingData1;
            if(Objects.nonNull(currentMappingData)){
                unitChannelPartnerMenuMappingData1 = currentMappingData;
                if(Objects.nonNull(upsellingId)){
                    unitChannelPartnerMenuMappingData1.setMenuRecommendationSequenceId(upsellingId);
                }
                unitChannelPartnerMenuMappingData1.setStatus(Boolean.TRUE.equals(removeMenuSequenceMapping) ? AppConstants.IN_ACTIVE : AppConstants.ACTIVE);
                unitChannelPartnerMenuMappingData1.setUpdatedAt(AppUtils.getCurrentTimestamp());
                unitChannelPartnerMenuMappingData1.setUpdatedBy(uploadedBy);
            }else{
                unitChannelPartnerMenuMappingData1= new UnitChannelPartnerMenuMappingData();
                unitChannelPartnerMenuMappingData1.setMenuApp(menuApp);
                unitChannelPartnerMenuMappingData1.setMenuType(daySlot);
                unitChannelPartnerMenuMappingData1.setMenuSequenceId(newMenuSequenceId);
                if(Objects.nonNull(upsellingId)){
                    unitChannelPartnerMenuMappingData1.setMenuRecommendationSequenceId(upsellingId);
                }
                unitChannelPartnerMenuMappingData1.setUnitPartnerMappingId(unitChannelPartnerMapping.getId());
                unitChannelPartnerMenuMappingData1.setBrandId(brandId);
                unitChannelPartnerMenuMappingData1.setStatus(Boolean.TRUE.equals(removeMenuSequenceMapping) ? AppConstants.IN_ACTIVE : AppConstants.ACTIVE);
                unitChannelPartnerMenuMappingData1.setCreatedAt(AppUtils.getCurrentTimestamp());
                unitChannelPartnerMenuMappingData1.setCreatedBy(uploadedBy);
                unitChannelPartnerMenuMappingData1.setUpdatedAt(AppUtils.getCurrentTimestamp());
                unitChannelPartnerMenuMappingData1.setUpdatedBy(uploadedBy);
            }
          updateUnitChannelPartnerMenuMappingDataList.add(unitChannelPartnerMenuMappingData1);
        }

    }


    private String getKeyForGroupMetaData(String menuApp , String daySlot){
        return menuApp + "_"  + daySlot;
     }


     private Boolean validateSheet(List<MenuExcelData> sheetExcelData , String sheetName , Set<String> errorsList , String menuApp)
             throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        List<String> sheetErrors = new ArrayList<>();
        MenuExcelSheets menuExcelSheet = MenuExcelSheets.getSheet(sheetName);
        if(menuExcelSheet.equals(MenuExcelSheets.IN_VALID_SHEET_NAME)){
            sheetErrors.add("Invalid Sheet name : {} " + sheetName);
            errorsList.addAll(sheetErrors);
            return false;
        }
        Boolean validSheet = true;
        Class<?> linkedClass = menuExcelSheet.getLinkedClass();
        Method staticMethod = linkedClass.getDeclaredMethod("getColumnEnum", String.class);
        for(MenuExcelData menuExcelData : sheetExcelData){
            for(String key : menuExcelData.getKeys()){
                if(AppConstants.NO.equalsIgnoreCase((String)menuExcelData.getField(MenuExcelUtil.getUpdateFieldName()))){
                    continue;
                }
                Object enumConstant = staticMethod.invoke(null, key);
                Object value = menuExcelData.getField(key);
                if (enumConstant != null) {
                    String columnName = invokeMethod(enumConstant, "getColumnName", String.class);
                    if(columnName.equalsIgnoreCase("")){
                        sheetErrors.add("Column '" + key + "' has invalid value: '" + value + "'" + " in " +  sheetName);
                        continue;
                    }
                    Boolean nullable = invokeMethod(enumConstant, "nullable", Boolean.class);
                    Class<?> expectedType =  invokeMethod(enumConstant, "getDataType", Class.class);
                    Boolean isMandatory =  invokeMethod(enumConstant, "isMandatory", Boolean.class);
                    List<?> acceptableValues  =invokeMethod(enumConstant,"getAcceptableValues",List.class);
                    if(Boolean.FALSE.equals(nullable) && Objects.isNull(value)){
                        sheetErrors.add("field : " + key + " is Null !!" + " in " +  sheetName);
                    }
                    if(Objects.nonNull(value)){
                        Object columnValue;
                        try {
                            columnValue = validateValue(value, expectedType);
                        } catch (Exception e) {
                            sheetErrors.add("Column '" + key + "' has invalid value: '" + value + "'" + " in " +  sheetName);
                            continue;
                        }
                        if(Objects.nonNull(acceptableValues)){
                            if(columnValue instanceof String && ((String) columnValue).contains(",")){
                                List<String> columnValues = Arrays.stream(String.valueOf(columnValue).split(",")).map(String::trim)
                                        .toList();
                                columnValues.forEach(columnVal ->{
                                    Object matchedValue = acceptableValues.stream()
                                            .filter(acceptableValue -> Objects.equals(acceptableValue, columnVal))
                                            .findAny()
                                            .orElse(null);
                                    if(Objects.isNull(matchedValue)){
                                        sheetErrors.add("Column '" + key + "' has invalid value: '" + columnVal + "'" + " in " +  sheetName);
                                    }
                                });

                            }else{
                                Object matchedValue = acceptableValues.stream()
                                        .filter(acceptableValue -> Objects.equals(acceptableValue, columnValue))
                                        .findAny()
                                        .orElse(null);
                                if(Objects.isNull(matchedValue)){
                                    sheetErrors.add("Column '" + key + "' has invalid value: '" + value + "'" + " in " +  sheetName);
                                    continue;
                                }
                            }
                        }
                    }

                } else {

                }
            }
        }
        if(!CollectionUtils.isEmpty(sheetErrors)){
            errorsList.addAll(sheetErrors);
            validSheet = false;
        }
        return validSheet;
     }

    private static <T> T invokeMethod(Object target, String methodName, Class<T> returnType) {
        try {
            Method method = target.getClass().getDeclaredMethod(methodName);
            Object result = method.invoke(target);
            return returnType.cast(result);
        } catch (Exception e) {
            throw new RuntimeException("Error invoking method: " + methodName, e);
        }
    }

    private void processSheetData(String sheetName , List<MenuExcelData> sheetExcelData, Integer uploadedBy , Integer menuUploadEventId , Set<String> errorsList
                                  , Set<Integer> groupIds,
                                  Map<UnitPartnerBrandKey, Map<String,GroupMetadataUpdateDomain>> groupMetadataUpdateMap , String menuApp
                                  , Map<String,Map<String,Map<String,Set<String>>>> changeLog
    ) throws ClassNotFoundException,
            DataUpdationException, InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        Boolean isValidSheet = validateSheet(sheetExcelData,sheetName,errorsList,menuApp);
        Map<String,Map<String,Set<String>>> sheetChangeLog  =new HashMap<>();
        if(Boolean.TRUE.equals(isValidSheet)){
            switch (MenuExcelSheets.getSheet(sheetName)){
                case CATEFORY_SEQUENCING -> processMenuSequencingSheet(sheetExcelData,uploadedBy,menuUploadEventId,errorsList,menuApp,sheetChangeLog);
                case CAFE_GROUP_MAPPING ->  processCafeGroupMappingSheet(sheetExcelData,uploadedBy,menuUploadEventId,errorsList);
                case MENU_SEQUENCE_GROUP_MAPPING -> processMenuSequenceGroupMappingSheet(sheetExcelData,uploadedBy,menuApp,errorsList,groupIds,
                        groupMetadataUpdateMap);
                case GROUP_UPSELLING_MAPPING -> processGroupUpsellingSheet(sheetExcelData,uploadedBy,menuApp,errorsList,groupIds,groupMetadataUpdateMap);
                case SWIGGY_RECOMMENDATION ->   processRecommendationGroupMapingSheet(sheetExcelData,uploadedBy,menuApp,errorsList);
            }
            changeLog.put(sheetName,sheetChangeLog);
        }else{
            errorsList.add("Sheet ::: " + sheetName + " is Skipped Due To Above Errors In this sheet !!");
        }

    }

    private String getGroupTagTypeKey(ProductGroupData productGroupData){
        return  productGroupData.getGroupTag() + "_" + productGroupData.getGroupName() + "_" + productGroupData.getGroupType();

    }

    private String getMenuSequenceMappingKey(MenuSequenceMappingData menuSequenceMappingData){
        return menuSequenceMappingData.getProductGroupId() + "_" + menuSequenceMappingData.getProductGroupParentId();
    }

    private String getMenuSequenceMappingKeyByGroupData(Integer productGroupId , Integer productParentGroupId){
        return productGroupId + "_" + productParentGroupId;
    }

    private String getGroupTagTypeKey(MenuGroupData menuGroupData){
        return  menuGroupData.getGroupTag() + "_" + menuGroupData.getGroupName() + "_" + menuGroupData.getGroupType();

    }

    private String getGroupTagTypeKeyParent(MenuGroupData menuGroupData){
        return  menuGroupData.getParentGroupTag() + "_" + menuGroupData.getParentGroupName() + "_" + MenuGroupTypes.CATEGORY.name();

    }

    private String getMenuSequneceMappingKey(MenuSequenceMappingData menuSequenceMappingData){
        return  menuSequenceMappingData.getProductGroupId() + "_" + menuSequenceMappingData.getProductGroupParentId();

    }


    private void addSlotMenuSequenceMappingFromExcel(Integer menuSequenceId , String menuSequenceName, List<MenuGroupData> menuGroupDataList, Integer uploadedBy
    , Set<String> errorsList , Map<String,Map<String,Set<String>>> sheetChangeLog){
        Map<String,Integer> keyToGroupIdMap = new HashMap<>(); // key is grouptag_groupname_grouptype
        Map<String,List<MenuGroupData>> menuGroupDataByTag = menuGroupDataList.stream().collect(Collectors.groupingBy(MenuGroupData::getGroupTag));
        List<MenuSequenceMappingData> menuSequenceMappingDataList = new ArrayList<>();
        for(String groupTag : menuGroupDataByTag.keySet()){
            Map<String,List<String>> groupNamesByType = menuGroupDataByTag.get(groupTag).stream().collect(Collectors.groupingBy(MenuGroupData::getGroupType,
                   Collectors.mapping(MenuGroupData::getGroupName,Collectors.toList())));
            for(String groupType : groupNamesByType.keySet()){
                List<ProductGroupData> productGroupDataList = masterMetadataDao.getAllGroupsByTagAndNameAndGroupType(groupTag,groupNamesByType.get(groupType),groupType);
                productGroupDataList.stream().forEach(productGroupData -> {
                    keyToGroupIdMap.put(getGroupTagTypeKey(productGroupData),productGroupData.getGroupId());
                });

            }
        }
        for(MenuGroupData menuGroupData : menuGroupDataList){
            if(keyToGroupIdMap.containsKey(getGroupTagTypeKey(menuGroupData))){
                MenuSequenceMappingData menuSequenceMappingData =  new MenuSequenceMappingData();
                menuSequenceMappingData.setMenuSequenceId(menuSequenceId);
                menuSequenceMappingData.setCreatedBy(uploadedBy);
                menuSequenceMappingData.setStatus(AppConstants.ACTIVE);
                menuSequenceMappingData.setProductGroupId(keyToGroupIdMap.get(getGroupTagTypeKey(menuGroupData)));
                menuSequenceMappingData.setIndex(menuGroupData.getGroupSequence());
                menuSequenceMappingData.setUpdatedBy(uploadedBy);
                menuSequenceMappingData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                if(Objects.nonNull(menuGroupData.getParentGroupName())){
                    if(Objects.isNull(keyToGroupIdMap.get(getGroupTagTypeKeyParent(menuGroupData)))){
                        errorsList.add("Please check If Category Group tag for sub Category :: " + menuGroupData.getGroupName() + " is Correct in  menu :: " + menuSequenceName );
                        continue;
                    }
                    menuSequenceMappingData.setProductGroupParentId(keyToGroupIdMap.get(getGroupTagTypeKeyParent(menuGroupData)));
                }
                menuSequenceMappingDataList.add(menuSequenceMappingData);
                String changeLogMsg = "New Menu  Sequence Group Mapping Is Created for Menu Sequence " + menuSequenceName
                        + " with values ::" + " Sequemce : " + menuSequenceMappingData.getIndex()  + " Group Name : " + menuGroupData.getGroupName() + " , Group Tag " + menuGroupData.getGroupTag()
                        + " Group Type :: " + menuGroupData.getGroupType();
                if(Objects.nonNull(menuGroupData.getParentGroupName())){
                    changeLogMsg = changeLogMsg + " Parent Group Name :: " + menuGroupData.getParentGroupName() + " Parent Group Tag :: " + menuGroupData.getParentGroupTag();
                }
                addToSheetChangeLog(sheetChangeLog.get(NEW_CHANGE_LOG),changeLogMsg ,
                        MenuExcelUtil.MENU_SEQUENCE_SUB_CAT_MAPPING);
            }else{
                errorsList.add("Invalid Group Name ::::: " + menuGroupData.getGroupName()  + "(" + menuGroupData.getGroupTag() +")"  + " In " +
                        MenuExcelSheets.CATEFORY_SEQUENCING.sheetName());
            }
        }
        masterMetadataDao.addAll(menuSequenceMappingDataList);

    }

    private void updateSlotMenuSequenceMappingFromExcel(Integer menuSequenceId , String menuSequenceName, List<MenuGroupData> menuGroupDataList, Integer uploadedBy ,
                                                        Set<String> errorsList , Map<String,Map<String,Set<String>>> sheetChangeLog){
        Map<String,Integer> keyToGroupIdMap = new HashMap<>(); // key is grouptag_groupname_grouptype
        List<MenuSequenceMappingData> updateMenuSequenceMappingList = new ArrayList<>();
        List<MenuSequenceMappingData> newMenuSequenceMappingList = new ArrayList<>();

        Map<String,List<MenuGroupData>> menuGroupDataByTag = menuGroupDataList.stream().collect(Collectors.groupingBy(MenuGroupData::getGroupTag));
        List<MenuSequenceMappingData> menuSequenceMappingDataList = new ArrayList<>();
        for(String groupTag : menuGroupDataByTag.keySet()){
            Map<String,List<String>> groupNamesByType = menuGroupDataByTag.get(groupTag).stream().collect(Collectors.groupingBy(MenuGroupData::getGroupType,
                    Collectors.mapping(MenuGroupData::getGroupName,Collectors.toList())));
            for(String groupType : groupNamesByType.keySet()){
                List<ProductGroupData> productGroupDataList = masterMetadataDao.getAllGroupsByTagAndNameAndGroupType(groupTag,groupNamesByType.get(groupType),groupType);
                productGroupDataList.forEach(productGroupData -> {
                    keyToGroupIdMap.put(getGroupTagTypeKey(productGroupData),productGroupData.getGroupId());
                });
            }
        }
        Map<String,MenuSequenceMappingData> currentSequenceMappingByKey = new HashMap<>(); // key is product_group_id + _ + parent_product_group_id
        List<MenuSequenceMappingData> currentMenuSequenceMappingData = masterMetadataDao.getAllGroupsMappingsByMenuSequenceId(menuSequenceId);
        currentMenuSequenceMappingData.forEach(currentMapping ->{
            currentSequenceMappingByKey.put(getMenuSequenceMappingKey(currentMapping),currentMapping);
        });
        for(MenuGroupData menuGroupData : menuGroupDataList){
            if(keyToGroupIdMap.containsKey(getGroupTagTypeKey(menuGroupData))){
                String key = getMenuSequenceMappingKeyByGroupData(keyToGroupIdMap.get(getGroupTagTypeKey(menuGroupData)),
                        keyToGroupIdMap.get(getGroupTagTypeKeyParent(menuGroupData)));
                if(currentSequenceMappingByKey.containsKey(key)){
                      MenuSequenceMappingData currentMapping = currentSequenceMappingByKey.get(key);
                      if(!AppConstants.ACTIVE.equalsIgnoreCase(currentMapping.getStatus())  || !menuGroupData.getGroupSequence().equals(currentMapping.getIndex())){
                          currentMapping.setStatus(AppConstants.ACTIVE);
                          currentMapping.setIndex(menuGroupData.getGroupSequence());
                          currentMapping.setUpdatedBy(uploadedBy);
                          currentMapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                          updateMenuSequenceMappingList.add(currentMapping);
                          String changeLogMsg = "Menu  Sequence Group Mapping Is Updated for Menu Sequence " + menuSequenceName
                                  + " with values :: " + " Sequemce : " + currentMapping.getIndex() + " Group Name : " + menuGroupData.getGroupName() + " , Group Tag " + menuGroupData.getGroupTag()
                                  + " Group Type :: " + menuGroupData.getGroupType();
                          if(Objects.nonNull(menuGroupData.getParentGroupName())){
                              changeLogMsg = changeLogMsg + " Parent Group Name :: " + menuGroupData.getParentGroupName() + " Parent Group Tag :: " + menuGroupData.getParentGroupTag();
                          }
                          addToSheetChangeLog(sheetChangeLog.get(UPDATE_CHANGE_LOG),changeLogMsg ,
                                  MenuExcelUtil.MENU_SEQUENCE_SUB_CAT_MAPPING);
                      }
                      currentSequenceMappingByKey.remove(key);
                }else{
                    MenuSequenceMappingData menuSequenceMappingData =  new MenuSequenceMappingData();
                    menuSequenceMappingData.setMenuSequenceId(menuSequenceId);
                    menuSequenceMappingData.setCreatedBy(uploadedBy);
                    menuSequenceMappingData.setStatus(AppConstants.ACTIVE);
                    menuSequenceMappingData.setProductGroupId(keyToGroupIdMap.get(getGroupTagTypeKey(menuGroupData)));
                    menuSequenceMappingData.setIndex(menuGroupData.getGroupSequence());
                    menuSequenceMappingData.setUpdatedBy(uploadedBy);
                    menuSequenceMappingData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                    if(Objects.nonNull(menuGroupData.getParentGroupName())){
                        menuSequenceMappingData.setProductGroupParentId(keyToGroupIdMap.get(getGroupTagTypeKeyParent(menuGroupData)));
                    }
                    newMenuSequenceMappingList.add(menuSequenceMappingData);
                    String changeLogMsg = "New Menu  Sequence Group Mapping Is Created for Menu Sequence " + menuSequenceName
                            + " with values ::" + " Sequemce : " + menuSequenceMappingData.getIndex()  + " Group Name : " + menuGroupData.getGroupName() + " , Group Tag " + menuGroupData.getGroupTag()
                            + " Group Type :: " + menuGroupData.getGroupType();
                    if(Objects.nonNull(menuGroupData.getParentGroupName())){
                        changeLogMsg = changeLogMsg + " Parent Group Name :: " + menuGroupData.getParentGroupName() + " Parent Group Tag :: " + menuGroupData.getParentGroupTag();
                    }
                    addToSheetChangeLog(sheetChangeLog.get(NEW_CHANGE_LOG),changeLogMsg ,
                            MenuExcelUtil.MENU_SEQUENCE_SUB_CAT_MAPPING);
                }
            }else{
                String msg = "Invalid Group Name ::::: " + menuGroupData.getGroupName()  + "(" + menuGroupData.getGroupTag() +") ,";
                if(Objects.nonNull(menuGroupData.getParentGroupName())){
                    msg =msg +  " Parent Group Name ::::: " + menuGroupData.getParentGroupName()  + "(" + menuGroupData.getParentGroupTag() +")";
                }
                errorsList.add(msg  +
                        " In " + MenuExcelSheets.CATEFORY_SEQUENCING.sheetName());
            }
        }
        for(String key : currentSequenceMappingByKey.keySet()){
            MenuSequenceMappingData remainingMapping = currentSequenceMappingByKey.get(key);
            if(!remainingMapping.getStatus().equalsIgnoreCase(AppConstants.IN_ACTIVE)){
                remainingMapping.setStatus(AppConstants.IN_ACTIVE);
                updateMenuSequenceMappingList.add(remainingMapping);
                String changeLogMsg = "Menu  Sequence Group Mapping Is Removed for Menu Sequence " + menuSequenceName
                        + " with values :: " + " Sequemce : " + remainingMapping.getIndex() + " Group Name : " + remainingMapping.getProductGroupId() ;
                if(Objects.nonNull(remainingMapping.getProductGroupParentId())){
                    changeLogMsg = changeLogMsg + " Parent Group Name :: " + remainingMapping.getProductGroupParentId();
                }
                addToSheetChangeLog(sheetChangeLog.get(UPDATE_CHANGE_LOG),changeLogMsg ,
                        MenuExcelUtil.MENU_SEQUENCE_SUB_CAT_MAPPING);
            }
        }

        if(!updateMenuSequenceMappingList.isEmpty()){
            masterMetadataDao.addAll(updateMenuSequenceMappingList);
        }
        if(!newMenuSequenceMappingList.isEmpty()){
            masterMetadataDao.addAll(newMenuSequenceMappingList);
        }


    }

    private void addDaySlotMenus(MenuExcelSequence menuExcelSequence , Map<String, List<MenuGroupData>> daySlotGroupMap, Integer uploadedBy
       , String menuApp , Set<String> errorsList , Map<String,Map<String,Set<String>>> sheetChangeLog ){
        for(String daySlot : daySlotGroupMap.keySet()){
            MenuSequenceData menuSequenceData = masterMetadataDao.getMenuSequenceByMenuExcelIdAndDaySLot(menuExcelSequence.getMenuExcelSequenceId(),daySlot);
            if(Objects.nonNull(menuSequenceData)){
               updateSlotMenuSequenceMappingFromExcel(menuSequenceData.getMenuSequenceId(),menuSequenceData.getMenuSequenceName(),daySlotGroupMap.get(daySlot),uploadedBy , errorsList,sheetChangeLog);
            }else{
                MenuSequenceData menuSequenceData1 = new MenuSequenceData();
                menuSequenceData1.setMenuApp(menuApp);
                menuSequenceData1.setMenuType(daySlot);
                menuSequenceData1.setCreatedBy(uploadedBy);
                menuSequenceData1.setCreationTime(AppUtils.getCurrentTimestamp());
                menuSequenceData1.setMenuExcelSequenceId(menuExcelSequence.getMenuExcelSequenceId());
                menuSequenceData1.setMenuSequenceName(menuExcelSequence.getSequenceName() + "_" + daySlot.toUpperCase());
                menuSequenceData1.setGetMenuSequenceDescription(menuExcelSequence.getSequenceName() + "_" + daySlot.toUpperCase());
                menuSequenceData1 =masterMetadataDao.add(menuSequenceData1);
                addToSheetChangeLog(sheetChangeLog.get(NEW_CHANGE_LOG),"New Menu  Sequence Is Created With name " + menuSequenceData1.getMenuSequenceName()
                        + "and with Id :: " + menuSequenceData1.getMenuSequenceId() + " For Menu Type " + menuSequenceData1.getMenuType() , MenuExcelUtil.MENU_SEQUENCE);
                addSlotMenuSequenceMappingFromExcel(menuSequenceData1.getMenuSequenceId(),menuSequenceData1.getMenuSequenceName(),daySlotGroupMap.get(daySlot),
                        uploadedBy,errorsList,sheetChangeLog);
            }
        }
    }

  /*  private void processRecommendationSheet(List<MenuExcelData> menuExcelDataList,Integer uploadedBy , Integer menuUploadEventId){
        List<GroupRecommendationMapping> currentGroupRecommendationMappingList = masterMetadataDao.findAll(GroupRecommendationMapping.class);
        Map<Integer,GroupRecommendationMapping> currentGroupMappingByName = currentGroupRecommendationMappingList.stream().collect(Collectors.toMap(GroupRecommendationMapping::getGr,
                Function.identity()));

    }*/

    private String getMenuSequenceGroupMapping(MenuSequenceGroupMapping menuSequenceGroupMapping){
        return  menuSequenceGroupMapping.getGroupId().getGroupId() + "_" + menuSequenceGroupMapping.getPartnerId() + "_" + menuSequenceGroupMapping.getBrandId();
    }
    private String getMenuSequenceGroupMapping(Integer groupId , Integer partnerId , Integer brandId ){
        return  groupId + "_" + partnerId + "_" + brandId;
    }

    private String getUpsellingGroupMappingKey(GroupUpsellingMapping groupUpsellingMapping){
        return  groupUpsellingMapping.getGroupId().getGroupId() + "_" + groupUpsellingMapping.getPartnerId() + "_" + groupUpsellingMapping.getBrandId() +
               "_" +  groupUpsellingMapping.getDaySlot();
    }

    private String getUpsellingGroupMappingKey(Integer groupId , Integer partnerId , Integer brandId , String daySlot){
        return  groupId + "_" + partnerId + "_" + brandId +
                "_" +  daySlot;
    }

    private UnitPartnerBrandKey getUnitPartnerBrandKeyForGroup(Integer groupId , Integer partnerId , Integer brandId){
        return new UnitPartnerBrandKey(groupId,brandId,partnerId);
    }

    private void updateGroupMetadataUpdateMap(List<MenuType> daySlots , String menuApp , Integer menuSequenceId , Integer upsellingId
    , Map<String,GroupMetadataUpdateDomain> groupMappingMap , String  menuSequenceStatus , String upsellingStatus){
        for(MenuType dayslot : daySlots){
            String key = getKeyForGroupMetaData(menuApp,dayslot.name());
            if(!groupMappingMap.containsKey(key)){
                groupMappingMap.put(key,new GroupMetadataUpdateDomain());
            }
            GroupMetadataUpdateDomain groupMetadataUpdateDomain = groupMappingMap.get(key);
            if(Objects.nonNull(menuSequenceId) && AppConstants.ACTIVE.equalsIgnoreCase(menuSequenceStatus)){
                groupMetadataUpdateDomain.setMenuSequenceId(menuSequenceId);
            }
            if(Objects.nonNull(upsellingId) && AppConstants.ACTIVE.equalsIgnoreCase(upsellingStatus)){
                groupMetadataUpdateDomain.setUpsellingId(upsellingId);
            }
            if(Objects.nonNull(menuSequenceStatus)){
                if(AppConstants.IN_ACTIVE.equalsIgnoreCase(menuSequenceStatus)){
                  groupMetadataUpdateDomain.setRemoveMenuSequenceMapping(true);
                }
            }
            if(Objects.nonNull(upsellingStatus)){
                if(AppConstants.IN_ACTIVE.equalsIgnoreCase(upsellingStatus)){
                    groupMetadataUpdateDomain.setRemoveUpsellingMapping(true);
                }
            }
//            if(AppConstants.IN_ACTIVE.equalsIgnoreCase(mappingStatus)){
//                groupMetadataUpdateDomain.set
//            }
            groupMetadataUpdateDomain.setMenuApp(menuApp);
            groupMetadataUpdateDomain.setDaySlot(dayslot);
        }
    }

    private void updateGroupMetadataUpdateMap(MenuType daySlot , String menuApp , Integer menuSequenceId , Integer upsellingId
            , Map<String,GroupMetadataUpdateDomain> groupMappingMap , String  menuSequenceStatus , String upsellingStatus){
            String key = getKeyForGroupMetaData(menuApp,daySlot.name());
            if(!groupMappingMap.containsKey(key)){
                groupMappingMap.put(key,new GroupMetadataUpdateDomain());
            }
            GroupMetadataUpdateDomain groupMetadataUpdateDomain = groupMappingMap.get(key);
            if(Objects.nonNull(menuSequenceId) && AppConstants.ACTIVE.equalsIgnoreCase(menuSequenceStatus)){
                groupMetadataUpdateDomain.setMenuSequenceId(menuSequenceId);
            }
            if(Objects.nonNull(upsellingId)  && AppConstants.ACTIVE.equalsIgnoreCase(upsellingStatus)){
                groupMetadataUpdateDomain.setUpsellingId(upsellingId);
            }
            if(Objects.nonNull(menuSequenceStatus)){
               if(AppConstants.IN_ACTIVE.equalsIgnoreCase(menuSequenceStatus)){
                 groupMetadataUpdateDomain.setRemoveMenuSequenceMapping(true);
               }
            }
            if(Objects.nonNull(upsellingStatus)){
                if(AppConstants.IN_ACTIVE.equalsIgnoreCase(upsellingStatus)){
                     groupMetadataUpdateDomain.setRemoveUpsellingMapping(true);
                 }
            }
            groupMetadataUpdateDomain.setMenuApp(menuApp);
            groupMetadataUpdateDomain.setDaySlot(daySlot);

    }

    private void processMenuSequenceGroupMappingSheet(List<MenuExcelData> menuExcelDataList, Integer uploadedBy , String menuApp ,
                                                      Set<String> errorsList , Set<Integer> groupIds ,
                                                      Map<UnitPartnerBrandKey, Map<String,GroupMetadataUpdateDomain>> groupMetadataUpdateMap ){
        List<MenuSequenceGroupMapping> newMappingList = new ArrayList<>();
        Map<String,UnitGroupMappingData> unitGroupMappingDataMap = masterMetadataDao.findAll(UnitGroupMappingData.class).stream()
                        .collect(Collectors.toMap(UnitGroupMappingData::getGroupName,Function.identity()));
        Map<String,MenuExcelSequence> menuExcelSequenceMap = masterMetadataDao.findAll(MenuExcelSequence.class).stream()
                .collect(Collectors.toMap(MenuExcelSequence::getSequenceName,Function.identity()));
        Map<String,Map<Integer,MenuSequenceGroupMapping>> menuSequenceGroupMappingMapGroup = masterMetadataDao.findAll(MenuSequenceGroupMapping.class).stream()
                .collect(Collectors.groupingBy(this::getMenuSequenceGroupMapping,
                        Collectors.toMap(
                                MenuSequenceGroupMapping::getExcelMenuSequenceId,
                                menuSequenceGroupMapping -> menuSequenceGroupMapping
                        )));
        //List<String> errorList = new ArrayList<>();
        List<Integer> acceptablePartnerIds = MenuExcelUtil.getAcceptablePartnerIdsMapByMenuApp(menuApp);
        menuExcelDataList.stream().filter(menuExcelData -> AppConstants.YES.equalsIgnoreCase((String) menuExcelData.getField(MenuSequenceGroupMappingSheet.UPDATE.getColumnName()))).forEach(menuExcelData -> {
            Boolean isValid = true;
            String groupName = menuExcelData.getField(MenuSequenceGroupMappingSheet.GROUP_NAME.getColumnName()).toString().toLowerCase();
            String menuSequenceName = menuExcelData.getField(MenuSequenceGroupMappingSheet.MENU_SEQUENCE.getColumnName()).toString().toLowerCase();
            String slots = menuExcelData.getField(MenuSequenceGroupMappingSheet.SLOTS.getColumnName()).toString().toUpperCase();
            AtomicReference<Boolean> containsAllSlot = new AtomicReference<>(false);
            List<MenuType> mappingDaySlots = Arrays.stream(slots.trim().split(",")).map(String::trim).map(MenuType::valueOf).map(menuType -> {
                if(MenuType.DAY_SLOT_ALL.equals(menuType)){
                    containsAllSlot.set(true);
                }
                return menuType;
            }).toList();
            if(containsAllSlot.get().equals(Boolean.TRUE)){
                mappingDaySlots = getAllDaySlots();
            }
            Integer partnerId = getIntValueFromCell(menuExcelData.getField(MenuSequenceGroupMappingSheet.PARTNER_ID.getColumnName()));
            if(!acceptablePartnerIds.contains(partnerId)){
                errorsList.add("Invalid Partner Id  ::::: " + partnerId  +  " For " + menuApp + " In " + MenuExcelSheets.MENU_SEQUENCE_GROUP_MAPPING.sheetName());
                isValid = false;
            }
            Integer brandId = getIntValueFromCell(menuExcelData.getField(MenuSequenceGroupMappingSheet.BRAND_ID.getColumnName()));
            if(!unitGroupMappingDataMap.containsKey(groupName)){
                errorsList.add("Invalid Group Name ::::: " + groupName  + " In " + MenuExcelSheets.MENU_SEQUENCE_GROUP_MAPPING.sheetName());
                isValid = false;
            }
            if(!menuExcelSequenceMap.containsKey(menuSequenceName)){
                errorsList.add("Invalid Menu Sequence Name ::::: " + menuSequenceName  + " In " + MenuExcelSheets.MENU_SEQUENCE_GROUP_MAPPING.sheetName());
                isValid = false;
            }
            if(Boolean.FALSE.equals(isValid)){
                return;
            }
            UnitGroupMappingData groupMappingData = unitGroupMappingDataMap.get(groupName);
            groupIds.add(groupMappingData.getGroupId());
            UnitPartnerBrandKey groupPartnerBrandKey = getUnitPartnerBrandKeyForGroup(groupMappingData.getGroupId(),
                    partnerId,brandId);
            if(!groupMetadataUpdateMap.containsKey(groupPartnerBrandKey)){
                groupMetadataUpdateMap.put(groupPartnerBrandKey, new HashMap<>());
            }
            MenuExcelSequence menuExcelSequence = menuExcelSequenceMap.get(menuSequenceName);
            String key = getMenuSequenceGroupMapping(groupMappingData.getGroupId(),partnerId,brandId);
            String mappingStatus = menuExcelData.getField(MenuSequenceGroupMappingSheet.MAPPING_STATUS.getColumnName()).toString().toUpperCase();
            if(menuSequenceGroupMappingMapGroup.containsKey(key)){
                Map<Integer,MenuSequenceGroupMapping> menuSequenceGroupMappingById = menuSequenceGroupMappingMapGroup.get(key);
                if(menuSequenceGroupMappingById.containsKey(menuExcelSequence.getMenuExcelSequenceId())){
                    MenuSequenceGroupMapping menuSequenceGroupMapping  =menuSequenceGroupMappingById.get(menuExcelSequence.getMenuExcelSequenceId());
                    menuSequenceGroupMapping.setUpdatedBy(uploadedBy);
                    menuSequenceGroupMapping.setUpdationTime(AppUtils.getCurrentTimestamp());
                    menuSequenceGroupMapping.setMappingStatus(mappingStatus);
                    newMappingList.add(menuSequenceGroupMapping);
                    updateGroupMetadataUpdateMap(mappingDaySlots,menuApp,menuSequenceGroupMapping.getExcelMenuSequenceId(),null
                    ,groupMetadataUpdateMap.get(groupPartnerBrandKey), mappingStatus,null);
                    menuSequenceGroupMappingMapGroup.get(key).put(menuExcelSequence.getMenuExcelSequenceId(),menuSequenceGroupMapping);
                }else{
                   MenuSequenceGroupMapping menuSequenceGroupMapping = MenuSequenceGroupMapping.builder()
                           .excelMenuSequenceId(menuExcelSequence.getMenuExcelSequenceId()).groupId(groupMappingData)
                           .brandId(brandId).mappingStatus(mappingStatus).creationTime(AppUtils.getCurrentTimestamp())
                           .createdBy(uploadedBy).partnerId(partnerId).build();
                   newMappingList.add(menuSequenceGroupMapping);
                   updateGroupMetadataUpdateMap(mappingDaySlots,menuApp,menuSequenceGroupMapping.getExcelMenuSequenceId(),null
                            ,groupMetadataUpdateMap.get(groupPartnerBrandKey),mappingStatus,null);
                    menuSequenceGroupMappingMapGroup.get(key).put(menuExcelSequence.getMenuExcelSequenceId(),menuSequenceGroupMapping);
                }
            }else{
                MenuSequenceGroupMapping menuSequenceGroupMapping = MenuSequenceGroupMapping.builder()
                        .excelMenuSequenceId(menuExcelSequence.getMenuExcelSequenceId()).groupId(groupMappingData)
                        .brandId(brandId).mappingStatus(mappingStatus).creationTime(AppUtils.getCurrentTimestamp())
                        .createdBy(uploadedBy).partnerId(partnerId).build();
                newMappingList.add(menuSequenceGroupMapping);
                updateGroupMetadataUpdateMap(mappingDaySlots,menuApp,menuSequenceGroupMapping.getExcelMenuSequenceId(),null
                        ,groupMetadataUpdateMap.get(groupPartnerBrandKey),mappingStatus,null);
                menuSequenceGroupMappingMapGroup.put(key,new HashMap<>());
                menuSequenceGroupMappingMapGroup.get(key).put(menuExcelSequence.getMenuExcelSequenceId(),menuSequenceGroupMapping);
            }
        });
        List<String> duplicateActiveMappingErrors = new ArrayList<>();
        for(String key : menuSequenceGroupMappingMapGroup.keySet()){
            Boolean isActive = false;
            for(Integer menuSequenceId : menuSequenceGroupMappingMapGroup.get(key).keySet()){
                MenuSequenceGroupMapping menuSequenceGroupMapping = menuSequenceGroupMappingMapGroup.get(key).get(menuSequenceId);
                Boolean mappingStatus = AppConstants.ACTIVE.equalsIgnoreCase(menuSequenceGroupMappingMapGroup.get(key).get(menuSequenceId).getMappingStatus());
                if(Boolean.TRUE.equals(mappingStatus)){
                    if(!isActive){
                        isActive = true;
                    }else{
                        UnitGroupMappingData unitGroupMappingData = menuSequenceGroupMapping.getGroupId();
                        duplicateActiveMappingErrors.add("multple active mapping for Group : " + unitGroupMappingData.getGroupName() + " Partner Id : "
                         + menuSequenceGroupMapping.getPartnerId() + " Brand Id : " + menuSequenceGroupMapping.getBrandId());
                    }
                }
            }
        }
        if(!duplicateActiveMappingErrors.isEmpty()){
            throw new RuntimeException("Errors while Processing " + MenuExcelSheets.MENU_SEQUENCE_GROUP_MAPPING.sheetName() + " ::: \n" +
                    duplicateActiveMappingErrors);
        }
        if(!newMappingList.isEmpty()){
            masterMetadataDao.addAll(newMappingList);
        }
    }

    private void processGroupUpsellingSheet(List<MenuExcelData> menuExcelDataList, Integer uploadedBy , String menuApp , Set<String> errorsList
    , Set<Integer> groupIds, Map<UnitPartnerBrandKey, Map<String,GroupMetadataUpdateDomain>>  groupMetadataUpdateMap){
        List<GroupUpsellingMapping> newMappingList = new ArrayList<>();
        Map<String,UnitGroupMappingData> unitGroupMappingDataMap = masterMetadataDao.findAll(UnitGroupMappingData.class).stream()
                .collect(Collectors.toMap(UnitGroupMappingData::getGroupName,Function.identity()));
        Map<String,MenuRecommendationData> menuUpsellingMap = masterMetadataDao.findAll(MenuRecommendationData.class).stream()
                .collect(Collectors.toMap(MenuRecommendationData::getMenuRecommendationName,Function.identity()));
        Map<String,Map<Integer,GroupUpsellingMapping>> upsellingGroupMappingMap = masterMetadataDao.findAll(GroupUpsellingMapping.class).stream()
                .collect(Collectors.groupingBy(this::getUpsellingGroupMappingKey,
                        Collectors.toMap(
                                GroupUpsellingMapping::getUpsellingId,
                                groupUpsellingMapping -> groupUpsellingMapping
                        )));
        List<Integer> acceptablePartnerIds = MenuExcelUtil.getAcceptablePartnerIdsMapByMenuApp(menuApp);
        menuExcelDataList.stream().filter(menuExcelData -> AppConstants.YES.equalsIgnoreCase(menuExcelData.getField(MenuSequenceGroupMappingSheet.UPDATE.getColumnName()).
                toString())).forEach(menuExcelData -> {
            Boolean isValid = true;
            String groupName = (String) menuExcelData.getField(GroupUpsellingMappingSheet.GROUP_NAME.getColumnName()).toString().toLowerCase();
            String upsellingGroup = (String) menuExcelData.getField(GroupUpsellingMappingSheet.UPSELLING_GROUP.getColumnName());
            String daySlots = menuExcelData.getField(GroupUpsellingMappingSheet.DAY_SLOT.getColumnName()).toString();
            AtomicReference<Boolean> containsAllSlot = new AtomicReference<>(false);
            List<MenuType> mappingDaySlots = Arrays.stream(daySlots.trim().split(",")).map(String::trim).map(MenuType::valueOf).map(menuType -> {
                if(MenuType.DAY_SLOT_ALL.equals(menuType)){
                    containsAllSlot.set(true);
                }
                return menuType;
            }).toList();
            if(containsAllSlot.get().equals(Boolean.TRUE)){
                mappingDaySlots = getAllDaySlots();
            }
            Integer partnerId = getIntValueFromCell(menuExcelData.getField(GroupUpsellingMappingSheet.PARTNER_ID.getColumnName()));
            if(!acceptablePartnerIds.contains(partnerId)){
                errorsList.add("Invalid Partner Id  ::::: " + partnerId  +  " For " + menuApp + " In " + MenuExcelSheets.GROUP_UPSELLING_MAPPING.sheetName());
                isValid = false;
            }
            Integer brandId = getIntValueFromCell(menuExcelData.getField(GroupUpsellingMappingSheet.BRAND_ID.getColumnName()));
            String mappingStatus = (String) menuExcelData.getField(GroupUpsellingMappingSheet.STATUS.getColumnName());
            if(!unitGroupMappingDataMap.containsKey(groupName)){
                errorsList.add("Invalid Group Name ::::: " + groupName  + " In " + MenuExcelSheets.GROUP_UPSELLING_MAPPING.sheetName());
                isValid = false;
            }
            if(!menuUpsellingMap.containsKey(upsellingGroup)){
                errorsList.add("Invalid Upselling  Name ::::: " + upsellingGroup  + " In " + MenuExcelSheets.GROUP_UPSELLING_MAPPING.sheetName());
                isValid = false;
            }
            if(Boolean.FALSE.equals(isValid)){
                return;
            }
            UnitGroupMappingData groupMappingData = unitGroupMappingDataMap.get(groupName);
            MenuRecommendationData upsellingData =  menuUpsellingMap.get(upsellingGroup);
            UnitPartnerBrandKey groupPartnerBrandKey = getUnitPartnerBrandKeyForGroup(groupMappingData.getGroupId(),
                    partnerId,brandId);
            groupIds.add(groupMappingData.getGroupId());
            if(!groupMetadataUpdateMap.containsKey(groupPartnerBrandKey)){
                groupMetadataUpdateMap.put(groupPartnerBrandKey, new HashMap<>());
            }
            for(MenuType daySlot : mappingDaySlots){
                String key  = getUpsellingGroupMappingKey(groupMappingData.getGroupId(),partnerId,brandId,daySlot.name());
                if(upsellingGroupMappingMap.containsKey(key)){
                    Map<Integer,GroupUpsellingMapping>  upsellingMappingMap = upsellingGroupMappingMap.get(key);
                    if(upsellingMappingMap.containsKey(upsellingData.getMenuRecommendationId())){
                        GroupUpsellingMapping currentUpsellingMapping   = upsellingMappingMap.get(upsellingData.getMenuRecommendationId());
                        currentUpsellingMapping.setUpdatedBy(uploadedBy);
                        currentUpsellingMapping.setUpdationTime(AppUtils.getCurrentTimestamp());
                        currentUpsellingMapping.setStatus(mappingStatus);
                        newMappingList.add(currentUpsellingMapping);
                        updateGroupMetadataUpdateMap(daySlot,menuApp,null, currentUpsellingMapping.getUpsellingId()
                                ,groupMetadataUpdateMap.get(groupPartnerBrandKey),null,mappingStatus);
                        upsellingGroupMappingMap.get(key).put(upsellingData.getMenuRecommendationId(),currentUpsellingMapping);
                    }else{
                        GroupUpsellingMapping groupUpsellingMapping = GroupUpsellingMapping.builder()
                                .upsellingId(upsellingData.getMenuRecommendationId()).groupId(groupMappingData)
                                .brandId(brandId).status(mappingStatus).creationTime(AppUtils.getCurrentTimestamp())
                                .createdBy(uploadedBy).partnerId(partnerId).daySlot(daySlot.name()).build();
                        newMappingList.add(groupUpsellingMapping);
                        updateGroupMetadataUpdateMap(daySlot,menuApp,null, groupUpsellingMapping.getUpsellingId()
                                ,groupMetadataUpdateMap.get(groupPartnerBrandKey),null,mappingStatus);
                        upsellingGroupMappingMap.get(key).put(upsellingData.getMenuRecommendationId(),groupUpsellingMapping);
                    }
                }else{
                    GroupUpsellingMapping groupUpsellingMapping = GroupUpsellingMapping.builder()
                            .upsellingId(upsellingData.getMenuRecommendationId()).groupId(groupMappingData)
                            .brandId(brandId).status(mappingStatus).creationTime(AppUtils.getCurrentTimestamp())
                            .createdBy(uploadedBy).partnerId(partnerId).daySlot(daySlot.name()).build();
                    newMappingList.add(groupUpsellingMapping);
                    updateGroupMetadataUpdateMap(daySlot,menuApp,null, groupUpsellingMapping.getUpsellingId()
                            ,groupMetadataUpdateMap.get(groupPartnerBrandKey),null,mappingStatus);
                    upsellingGroupMappingMap.put(key,new HashMap<>());
                    upsellingGroupMappingMap.get(key).put(upsellingData.getMenuRecommendationId(),groupUpsellingMapping);
                }
            }

                });

        List<String> duplicateActiveMappingErrors = new ArrayList<>();
        for(String key : upsellingGroupMappingMap.keySet()){
            Boolean isActive = false;
            for(Integer menuSequenceId : upsellingGroupMappingMap.get(key).keySet()){
                GroupUpsellingMapping groupUpsellingMapping = upsellingGroupMappingMap.get(key).get(menuSequenceId);
                Boolean mappingStatus = AppConstants.ACTIVE.equalsIgnoreCase(groupUpsellingMapping.getStatus());
                if(Boolean.TRUE.equals(mappingStatus)){
                    if(!isActive){
                        isActive = true;
                    }else{
                        UnitGroupMappingData unitGroupMappingData = groupUpsellingMapping.getGroupId();
                        duplicateActiveMappingErrors.add("multple active mapping for Group : " + unitGroupMappingData.getGroupName() + " Partner Id : "
                                + groupUpsellingMapping.getPartnerId() + " Brand Id : " + groupUpsellingMapping.getBrandId());
                    }
                }
            }
        }
        if(!duplicateActiveMappingErrors.isEmpty()){
            throw new RuntimeException("Errors while Processing " + MenuExcelSheets.GROUP_UPSELLING_MAPPING.sheetName() + " ::: \n" +
                    duplicateActiveMappingErrors);
        }
        if(!newMappingList.isEmpty()){
            masterMetadataDao.addAll(newMappingList);
        }


    }

    private Integer getIntValueFromCell(Object value){
        return (int) Double.parseDouble((String) value);
    }

    private String validateUnits(String unitIds ,  Map<Integer, Unit> unitMap,Set<String> errorsList , String groupName){
        List<Integer> ids = new ArrayList<>();
        List<String> faultyIds = new ArrayList<>();
        Arrays.stream(unitIds.split(",")).map(String::trim).forEach(id ->{
            try {
                Integer unitId = getIntValueFromCell(id);
                if(!unitMap.containsKey(unitId)){
                    faultyIds.add(id);
                }else{
                    ids.add(unitId);
                }
            }catch (Exception e){
                faultyIds.add(id);
            }
        });
        if(!CollectionUtils.isEmpty(faultyIds)){
            errorsList.add("Skipping Invalid Group Ids  :::: " + faultyIds + "For Group : " + groupName);
        }
        return ids.stream().map(String::valueOf).collect(Collectors.joining(","));
    }

    private void processCafeGroupMappingSheet(List<MenuExcelData> menuExcelDataList, Integer uploadedBy , Integer menuUploadEventId , Set<String> errorsList){
        List<UnitGroupMappingData> currentGroupMapping = masterMetadataDao.findAll(UnitGroupMappingData.class);
        Map<Integer, Unit> unitMap = masterDataCache.getUnits();
        Map<String,UnitGroupMappingData> currentGroupMappingByName = currentGroupMapping.stream().collect(Collectors.toMap(UnitGroupMappingData::getGroupName,
                Function.identity()));
        List<UnitGroupMappingData> newUnitGroupMappings = new ArrayList<>();
        List<UnitGroupMappingData> updateUnitGroupMappings = new ArrayList<>();
        menuExcelDataList.stream().filter(menuExcelData -> AppConstants.YES.equalsIgnoreCase((String) menuExcelData.getField(CafeGroupMappingSheet.UPDATE.getColumnName()))).forEach(menuExcelData -> {
            String groupName = (String) menuExcelData.getField(CafeGroupMappingSheet.GROUP_NAME.getColumnName());
            String unitIds =  validateUnits(menuExcelData.getField(CafeGroupMappingSheet.UNIT_IDS.getColumnName()).toString().replaceAll("\\s+", "")
            ,unitMap,errorsList,groupName);
           // log.info("cdcd : {} " , menuExcelData.getField(CafeGroupMappingSheet.PRIORITY_LEVEL.getColumnName()));
            Integer priorityLevel = getIntValueFromCell(menuExcelData.getField(CafeGroupMappingSheet.PRIORITY_LEVEL.getColumnName()));
            String status = (String) menuExcelData.getField(CafeGroupMappingSheet.STATUS.getColumnName());


            if(currentGroupMappingByName.containsKey(groupName.toLowerCase())){
                UnitGroupMappingData currentMapping = currentGroupMappingByName.get(groupName.toLowerCase());
                int currentHash = currentMapping.hashCode();
                currentMapping.setStatus(status);
                currentMapping.setUnitIds(unitIds);
                currentMapping.setPriority(priorityLevel);
                int updatedHashCode = currentMapping.hashCode();
                if(currentHash != updatedHashCode){
                    currentMapping.setUpdatedBy(uploadedBy);
                    currentMapping.setUpdationTime(AppUtils.getCurrentTimestamp());
                    updateUnitGroupMappings.add(currentMapping);
                }
            }else{
                UnitGroupMappingData unitGroupMappingData = UnitGroupMappingData.builder()
                        .groupName(groupName.toLowerCase()).unitIds(unitIds).priority(priorityLevel).status(status)
                        .createdBy(uploadedBy).creationTime(AppUtils.getCurrentTimestamp()).updatedBy(uploadedBy).
                        updationTime(AppUtils.getCurrentTimestamp())
                        .build();
                newUnitGroupMappings.add(unitGroupMappingData);
            }
            if(!updateUnitGroupMappings.isEmpty()){
                masterMetadataDao.addAll(updateUnitGroupMappings);
            }
            if(!newUnitGroupMappings.isEmpty()){
                masterMetadataDao.addAll(newUnitGroupMappings);
            }
        });

    }

    private String getKeyForGroupRecommendationMapping(GroupRecommendationMapping groupRecommendationMapping){
        return groupRecommendationMapping.getProductId() + "_" + groupRecommendationMapping.getPartnerId() + "_"
                + groupRecommendationMapping.getBrandId() + groupRecommendationMapping.getDaySlot();
    }

    private String getKeyForGroupRecommendationMapping(Integer productId , Integer partnerId , Integer brandId , String daySlot){
        return productId + "_" + partnerId + "_"
                + brandId + daySlot;
    }

    private void processRecommendationGroupMapingSheet(List<MenuExcelData> menuExcelDataList, Integer uploadedBy , String menuApp , Set<String> errorsList){
        List<GroupRecommendationMapping> newGroupMappings = new ArrayList<>();
        Map<String,UnitGroupMappingData> unitGroupMappingDataMap = masterMetadataDao.findAll(UnitGroupMappingData.class).stream()
                .collect(Collectors.toMap(UnitGroupMappingData::getGroupName,Function.identity()));
        Map<Integer, Product> productMap = masterDataCache.getProductDetails();
        List<Integer> acceptablePartnerIds = MenuExcelUtil.getAcceptablePartnerIdsMapByMenuApp(menuApp);
        menuExcelDataList.stream().filter(menuExcelData -> AppConstants.YES.equalsIgnoreCase(menuExcelData.getField(MenuSequenceGroupMappingSheet.UPDATE.getColumnName()).
                toString())).forEach(menuExcelData -> {
                    Boolean isValid = true;
                String groupName = menuExcelData.getField(RecommendationMappingSheet.GROUP_NAME.getColumnName()).toString().toLowerCase();
                String productIds =  AppUtils.removesWhiteSpaces(menuExcelData.getField(RecommendationMappingSheet.PRODUCT_IDS.getColumnName())
                        .toString());
                Set<Integer> productIdsList = Arrays.stream(productIds.split(",")).map(this::getIntValueFromCell).collect(Collectors.toSet());
                String daySlots = (String) menuExcelData.getField(RecommendationMappingSheet.DAY_SLOT.getColumnName());
                String status = (String) menuExcelData.getField(RecommendationMappingSheet.STATUS.getColumnName());
                Integer brandId = getIntValueFromCell(menuExcelData.getField(RecommendationMappingSheet.BRAND_ID.getColumnName()));
                Integer partnerId = getIntValueFromCell(menuExcelData.getField(RecommendationMappingSheet.PARTNER_ID.getColumnName()));
            if(!acceptablePartnerIds.contains(partnerId)){
                errorsList.add("Invalid Partner Id  ::::: " + partnerId  +  " For " + menuApp + " In " + MenuExcelSheets.SWIGGY_RECOMMENDATION.sheetName());
                isValid = false;
            }
            if(!unitGroupMappingDataMap.containsKey(groupName)){
                errorsList.add("Invalid Group Name ::::: " + groupName  + " In " + MenuExcelSheets.SWIGGY_RECOMMENDATION.sheetName());
                isValid = false;
            }
            for(Integer productId : productIdsList){
                if(!productMap.containsKey(productId)){
                    errorsList.add("Invalid Product  Id ::::: " + productId  + " In " + MenuExcelSheets.SWIGGY_RECOMMENDATION.sheetName());
                    isValid = false;
                }
            }
            if(Boolean.FALSE.equals(isValid)){
                return;
            }
            UnitGroupMappingData groupMappingData = unitGroupMappingDataMap.get(groupName);
            AtomicReference<Boolean> containsAllSlot = new AtomicReference<>(false);
            List<MenuType> mappingDaySlots = Arrays.stream(daySlots.trim().split(",")).map(String::trim).map(MenuType::valueOf).map(menuType -> {
                if(MenuType.DAY_SLOT_ALL.equals(menuType)){
                    containsAllSlot.set(true);
                }
                return menuType;
            }).toList();
            if(containsAllSlot.get().equals(Boolean.TRUE)){
                mappingDaySlots = getAllDaySlots();
            }
            List<String> slots  =  mappingDaySlots.stream().map(s -> s.name()).collect(Collectors.toList());
           Map<String,GroupRecommendationMapping> groupRecommendationMappingMap =  masterMetadataDao.getAllProductRecommendationMappingByGroup(groupMappingData.
                   getGroupId(), brandId,partnerId,slots).stream().collect(Collectors.toMap(mapping -> getKeyForGroupRecommendationMapping(mapping.getProductId(),
                   mapping.getPartnerId(),mapping.getBrandId(),mapping.getDaySlot()),Function.identity()));
            for(Integer productId : productIdsList){
                   for(MenuType slot : mappingDaySlots){
                        String key = getKeyForGroupRecommendationMapping(productId,partnerId,brandId,slot.name());
                        if(groupRecommendationMappingMap.containsKey(key)){
                            GroupRecommendationMapping groupRecommendationMapping = groupRecommendationMappingMap.get(key);
                            if(!status.equalsIgnoreCase(groupRecommendationMapping.getMappingStatus())){
                                groupRecommendationMapping.setMappingStatus(status.toUpperCase());
                                groupRecommendationMapping.setUpdatedBy(uploadedBy);
                                groupRecommendationMapping.setUpdationTime(AppUtils.getCurrentTimestamp());
                                newGroupMappings.add(groupRecommendationMapping);
                            }
                            groupRecommendationMappingMap.remove(key);
                        }else{
                             GroupRecommendationMapping groupRecommendationMapping = GroupRecommendationMapping.builder().
                            productId(productId).brandId(brandId).partnerId(partnerId).daySlot(slot.name())
                                     .createdBy(uploadedBy).creationTime(AppUtils.getCurrentTimestamp()).mappingStatus(status).
                                     groupId(groupMappingData.getGroupId()).build();
                            newGroupMappings.add(groupRecommendationMapping);
                        }
                   }
            }
            for(String key : groupRecommendationMappingMap.keySet()){
                GroupRecommendationMapping groupRecommendationMapping = groupRecommendationMappingMap.get(key);
                groupRecommendationMapping.setMappingStatus(AppConstants.IN_ACTIVE);
                groupRecommendationMapping.setUpdatedBy(uploadedBy);
                groupRecommendationMapping.setUpdationTime(AppUtils.getCurrentTimestamp());
                newGroupMappings.add(groupRecommendationMapping);
            }

            if(!newGroupMappings.isEmpty()){
                masterMetadataDao.addAll(newGroupMappings);
            }

                });

    }

   private void addToSheetChangeLog(Map<String,Set<String>> changeLogMap , String changeLog  , String key){
        if(!changeLogMap.containsKey(key)){
            changeLogMap.put(key,new HashSet<>());
        }
        changeLogMap.get(key).add(changeLog);
   }

    private void processMenuSequencingSheet(List<MenuExcelData> menuExcelDataList, Integer uploadedBy , Integer menuUploadEventId ,
                                            Set<String> errorsList, String menuApp , Map<String,Map<String,Set<String>>> sheetChangeLog) throws ClassNotFoundException {
        Map<String,Map<String, List<MenuGroupData>>> menuGroupMap  =  getMenuGroupDataMap(menuExcelDataList);
        sheetChangeLog.put(NEW_CHANGE_LOG,new HashMap<>());
        sheetChangeLog.put(UPDATE_CHANGE_LOG,new HashMap<>());
        for(String menuExcelSequenceName : menuGroupMap.keySet()){
            if(!CollectionUtils.isEmpty(menuGroupMap.get(menuExcelSequenceName))){
                MenuExcelSequence menuExcelSequence = menuExcelSequenceDao.findBySequenceNameAndMenuApp(menuExcelSequenceName.toLowerCase(),menuApp);
                if(Objects.isNull(menuExcelSequence)){
                    MenuExcelSequence menuExcelSequence1 = MenuExcelSequence.builder().sequenceName(menuExcelSequenceName.toLowerCase()).
                            createdBy(uploadedBy).creationTime(AppUtils.getCurrentTimestamp()).menuApp(menuApp).
                            build();
                    menuExcelSequence = menuExcelSequenceDao.save(menuExcelSequence1);
                    addToSheetChangeLog(sheetChangeLog.get(NEW_CHANGE_LOG),"New Menu Excel Sequence Is Created With name " + menuExcelSequence1.getSequenceName()
                     + "and with Id :: " + menuExcelSequence.getMenuExcelSequenceId() , MenuExcelUtil.MENU_EXCEL_SEQUENCE);
                }else{
                    menuExcelSequence.setUpdatedBy(uploadedBy.toString());
                    menuExcelSequence.setUpdationTime(AppUtils.getCurrentTimestamp());
                    menuExcelSequenceDao.save(menuExcelSequence);
                    addToSheetChangeLog(sheetChangeLog.get(UPDATE_CHANGE_LOG),"Menu Excel Sequence Is Updated With name " + menuExcelSequence.getSequenceName()
                            + "and with Id :: " + menuExcelSequence.getMenuExcelSequenceId(),MenuExcelUtil.MENU_EXCEL_SEQUENCE);
                }
                addDaySlotMenus(menuExcelSequence,menuGroupMap.get(menuExcelSequenceName),uploadedBy,menuApp,errorsList,sheetChangeLog);

            }

        }
    }



    private Map<String,Map<String, List<MenuGroupData>>> getMenuGroupDataMap(List<MenuExcelData> menuExcelDataList){
        Map<String,Map<String, List<MenuGroupData>>> menuGroupDataMap = new HashMap<>();
        AtomicReference<String> categoryName = new AtomicReference<>("");
        AtomicReference<String> categoryTag = new AtomicReference<>("");
        AtomicReference<String> menuName = new AtomicReference<>("");
        Map<String,Boolean> menuUpdateMap = new HashMap<>();
        menuExcelDataList.stream().forEach(menuExcelData ->{
            if(Objects.nonNull(menuExcelData.getField(MenuSequenceSlotColumns.MENU_NAME.getColumnName()))){
                menuName.set((String) menuExcelData.getField(MenuSequenceSlotColumns.MENU_NAME.getColumnName()));
            }
            String update = (String) menuExcelData.getField(MenuSequenceSlotColumns.UPDATE.getColumnName());
            if(AppConstants.YES.equalsIgnoreCase(update)){
                menuUpdateMap.put(menuName.get(),Boolean.TRUE);
            }
            if(Objects.nonNull(menuExcelData.getField(MenuSequenceSlotColumns.CATEGORY.getColumnName()))){
                categoryName.set((String) menuExcelData.getField(MenuSequenceSlotColumns.CATEGORY.getColumnName()));
            }
            if(Objects.nonNull(menuExcelData.getField(MenuSequenceSlotColumns.CATEGORY_GROUP_TAG.getColumnName()))){
                categoryTag.set((String) menuExcelData.getField(MenuSequenceSlotColumns.CATEGORY_GROUP_TAG.getColumnName()));
            }
            String subCategoryName = (String) menuExcelData.getField(MenuSequenceSlotColumns.SUBCATEGORY.getColumnName());
            String subCategoryTag = (String) menuExcelData.getField(MenuSequenceSlotColumns.SUBCATEGORY_GROUP_TAG.getColumnName());

             if(!menuGroupDataMap.containsKey((String) menuExcelData.getField(MenuSequenceSlotColumns.MENU_NAME.getColumnName()))){
                 menuGroupDataMap.put((String) menuExcelData.getField(MenuSequenceSlotColumns.MENU_NAME.getColumnName()),new HashMap<>());
             }
             addSlotData(menuName,categoryName,categoryTag,subCategoryName,subCategoryTag,menuExcelData,menuGroupDataMap.get(menuName.get()));
        });

        Map<String, Map<String, List<MenuGroupData>>> filteredMap = menuGroupDataMap.entrySet()
                .stream()
                .filter(entry -> menuUpdateMap.containsKey(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));


        return filteredMap;
    }

    private void addSlotData(AtomicReference<String> menuName , AtomicReference<String> categoryName , AtomicReference<String> categoryTag ,
                             String subCategoryName, String subCategoryTag,
                             MenuExcelData menuExcelData,Map<String, List<MenuGroupData>> daySlotMap){
        Arrays.stream(MenuSequenceSlotColumns.values()).filter(column-> !column.getDaySlot().equalsIgnoreCase(AppConstants.NOT_APPLICABLE))
                .filter(column -> Objects.nonNull(menuExcelData.getField(column.getColumnName())))
                .forEach(column->{
                    if(column.getColumnName().equalsIgnoreCase(((String)menuExcelData.getField(column.getColumnName())))){
                        return;
                    }
                    String daySlot = column.getDaySlot();
                    if(!daySlotMap.containsKey(daySlot)){
                        daySlotMap.put(daySlot,new ArrayList<>());
                    }
                    Boolean isSubCategory =  column.getColumnName().endsWith("(Subcat Seq)");
                    Integer groupSequence = (int) Double.parseDouble((String) menuExcelData.getField(column.getColumnName()));
                    //Integer groupSequence = (Integer) menuExcelData.getField(column.getColumnName());
                    MenuGroupData menuGroupData;
                    if(isSubCategory){
                        menuGroupData = MenuGroupData.builder().groupName(subCategoryName)
                                .groupTag(subCategoryTag)
                                .groupType(MenuGroupTypes.SUB_CATEGORY.name())
                                .parentGroupName(categoryName.get()).parentGroupTag(categoryTag.get()).
                                groupSequence(groupSequence)
                                .build();
                    }else{
                        menuGroupData = MenuGroupData.builder().groupName(categoryName.get())
                                .groupTag(categoryTag.get())
                                .groupType(MenuGroupTypes.CATEGORY.name())
                                .groupSequence(groupSequence)
                                .build();
                    }
                    daySlotMap.get(daySlot).add(menuGroupData);
                });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public MenuExcelUploadResponse processExcelUpload(MultipartFile file, Integer uploadedBy, List<String> selectedSheets , String menuApp) throws
            DataUpdationException, IOException {
        if (file.isEmpty()) {
            throw new DataUpdationException( "File is empty.");
        }
        String fileName = file.getOriginalFilename();
        File tempFile = File.createTempFile("uploaded-", fileName);
        file.transferTo(tempFile);

        FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), props.getMenuExcelUploadPath(),
                tempFile);
        List<MenuExcelUploadEvent> currentActiveUploadEvents = masterMetadataDao.getAllActiveMenuExcelUploadEvents(menuApp);
        currentActiveUploadEvents.forEach(event -> event.setStatus(AppConstants.IN_ACTIVE));
        MenuExcelUploadEvent menuExcelUploadEvent = MenuExcelUploadEvent.builder()
                .bucket(fileDetail.getBucket()).filePath(fileDetail.getUrl()).uploadTime(AppUtils.getCurrentTimestamp())
                .fileKey(fileDetail.getKey()).sheetVersion("1.0.0").updationSheets(String.join(",", selectedSheets))
                .uploadedBy(uploadedBy).menuApp(menuApp).status(AppConstants.ACTIVE).build();
        menuExcelUploadEvent  = masterMetadataDao.add(menuExcelUploadEvent);
        Map<String, List<MenuExcelData>> menuExcelDataMap = menuExcelParser.parseExcel(tempFile,uploadedBy,selectedSheets);
        MenuExcelUploadEvent finalMenuExcelUploadEvent = menuExcelUploadEvent;
        Set<String> errorsList = new HashSet<>();
        Set<Integer> groupIds =new HashSet<>();
        Map<String,Map<String,Map<String,Set<String>>>> changeLog = new HashMap<>();
        Map<UnitPartnerBrandKey, List<GroupMetadataUpdateDomain>> groupMetadataUpdateDomainMap = new HashMap<>();
        Map<UnitPartnerBrandKey, Map<String,GroupMetadataUpdateDomain>> groupMetadataUpdateMap = new HashMap<>();
        Arrays.stream(MenuExcelSheets.values()).toList().stream().filter(sheet-> selectedSheets.contains(sheet.sheetName())).
                forEach(sheet -> {
                    if(!menuExcelDataMap.containsKey(sheet.sheetName())){
                        String errorMsg  =  sheet.sheetName() + " is Missing From Excel";
                        errorsList.add(errorMsg);
                        return;
                    }
                    try {
                        String sheetName = sheet.sheetName();
                        processSheetData(sheetName,menuExcelDataMap.get(sheetName),uploadedBy, finalMenuExcelUploadEvent.getId(),errorsList,groupIds
                        ,groupMetadataUpdateMap,menuApp,changeLog);
                    } catch (ClassNotFoundException | DataUpdationException | InvocationTargetException |
                             NoSuchMethodException | IllegalAccessException e) {
                        String errorMsg  = "Error while Parsing sheet : " + sheet.sheetName() ;
                        throw new RuntimeException(errorMsg + e);
                    }
                });
        if(!CollectionUtils.isEmpty(groupMetadataUpdateMap)){
            for(UnitPartnerBrandKey key : groupMetadataUpdateMap.keySet()){
                List<GroupMetadataUpdateDomain> groupMetadataUpdateDomainList = new ArrayList<>(groupMetadataUpdateMap.get(key).values());
                groupMetadataUpdateDomainMap.put(key,groupMetadataUpdateDomainList);
            }
        }
        updateMappingDataForGroups(groupIds.stream().toList(),groupMetadataUpdateDomainMap,uploadedBy,errorsList);
        log.error("Error List : {} " , errorsList);
        String respone = "";
        if(!CollectionUtils.isEmpty(errorsList)){
            respone = errorsList.stream().collect(Collectors.joining("\n"));
        }else{
            respone = "Excel Successfully Parsed With following sheets  : " + '\n' + selectedSheets.stream().collect(Collectors.joining("\n"));
        }
        MenuExcelUploadResponse response =  MenuExcelUploadResponse.builder().response(respone).errorsList(errorsList.stream().toList()).changeLog(new HashMap<>()).build();
        SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                ApplicationName.KETTLE_SERVICE.name(),
                SlackNotification.MENU_UPLOAD_RESPONSE,new Gson().toJson(response));
        return response;


    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public MenuExcelUploadEvent getLatestMenuMappingUploadEvent(String menuApp){
        List<MenuExcelUploadEvent> currentActiveUploadEvents = masterMetadataDao.getAllActiveMenuExcelUploadEvents(menuApp);
        if(!CollectionUtils.isEmpty(currentActiveUploadEvents)){
            return  currentActiveUploadEvents.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public View downloadExcelForMenuMappings(String menuApp , Boolean isTemplate){
        String key = "";
        String bucket = "";
        if(Boolean.TRUE.equals(isTemplate)){
            MenuExcelUploadEvent menuExcelUploadEvent = masterMetadataDao.find(MenuExcelUploadEvent.class,1);
            key = menuExcelUploadEvent.getFileKey();
            bucket = menuExcelUploadEvent.getBucket();
        }else{
            List<MenuExcelUploadEvent> currentActiveUploadEvents = masterMetadataDao.getAllActiveMenuExcelUploadEvents(menuApp);
            if(!CollectionUtils.isEmpty(currentActiveUploadEvents)){
                key = currentActiveUploadEvents.get(0).getFileKey();
                bucket = currentActiveUploadEvents.get(0).getBucket();
            }
        }

        String finalKey = key;
        String finalBucket = bucket;
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                // Set the response headers for file download
                response.setHeader("Content-Disposition", "attachment; filename=MenuTemplate.xlsx");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                FileDetail fileDetail = new FileDetail(finalBucket, finalKey, null);
                File file = fileArchiveService.getFileFromS3(props.getBasePath(),fileDetail);
                if (!file.exists()) {
                    response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                    return;
                }

                response.setHeader("Content-Disposition", "attachment; filename=" + file.getName());
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setContentLengthLong(file.length());

                try (FileInputStream fileInputStream = new FileInputStream(file);
                     OutputStream responseOutputStream = response.getOutputStream()) {

                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                        responseOutputStream.write(buffer, 0, bytesRead);
                    }
                    responseOutputStream.flush();
                } catch (IOException e) {
                    e.printStackTrace();
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                }

            }


        };


    }

}
