/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.partner.service.impl;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.interceptor.ExternalAPITokenCache;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class ExternalAPIAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private ExternalAPITokenCache cache;

    @Autowired
    private Environment env;

    @Override
    public void afterCompletion(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2, Exception arg3)
        throws Exception {
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {

    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws AuthenticationFailureException {
        String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim()
            : request.getHeader("auth");
        if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
            if(Boolean.parseBoolean(env.getProperty("external.partner.interceptor","false"))){
                if (cache.isValidKey(authHeader) && cache.checkAccess(authHeader, request.getRequestURI(), request.getMethod())) {
                    return true;
                } else {
                    throw new AuthenticationFailureException(String.format("Invalid tokens in header : %s", authHeader));
                }
            }else{
                return true;
            }
        } else {
            throw new AuthenticationFailureException(String.format("Invalid tokens in header : %s", authHeader));
        }
    }

}
