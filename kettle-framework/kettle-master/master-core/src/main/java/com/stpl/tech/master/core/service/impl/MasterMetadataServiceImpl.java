/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.master.core.service.impl;

import com.google.common.net.MediaType;
import com.stpl.tech.kettle.report.metadata.model.WarningMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.budget.metadata.model.ExpenseValidation;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.service.MasterMetadataService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.AuditChangeLogDao;
import com.stpl.tech.master.data.dao.AuditChangeLogHistoryDao;
import com.stpl.tech.master.data.dao.CategoryAttributesDao;
import com.stpl.tech.master.data.dao.ExternalPartnerDao;
import com.stpl.tech.master.data.dao.MasterMetadataDao;
import com.stpl.tech.master.data.model.CacheReferenceMetadata;
import com.stpl.tech.master.data.model.CashMetadataDetail;
import com.stpl.tech.master.data.model.CategoryAttributes;
import com.stpl.tech.master.data.model.CountryDetail;
import com.stpl.tech.master.data.model.EmployeeDetail;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import com.stpl.tech.master.data.model.KioskCompanyDetailsData;
import com.stpl.tech.master.data.model.KioskLocationDetailsData;
import com.stpl.tech.master.data.model.KioskMachineDetailsData;
import com.stpl.tech.master.data.model.LocationDetail;
import com.stpl.tech.master.data.model.PaymentModeAttributes;
import com.stpl.tech.master.data.model.PreAuthenticatedApiData;
import com.stpl.tech.master.data.model.ProductCondimentGroup;
import com.stpl.tech.master.data.model.ProductCondimentItem;
import com.stpl.tech.master.data.model.RegionMap;
import com.stpl.tech.master.data.model.StateDetail;
import com.stpl.tech.master.data.model.UnitContactDetailsData;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitIpAddressData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.data.mongo.AuditChangeLog;
import com.stpl.tech.master.data.mongo.AuditChangeLogHistory;
import com.stpl.tech.master.data.repository.CacheReferenceMetadataDao;
import com.stpl.tech.master.data.repository.ProductToCondimentMappingDao;
import com.stpl.tech.master.data.repository.SourceToCondimentMappingDao;
import com.stpl.tech.master.data.repository.UnitIpAddressDao;
import com.stpl.tech.master.data.repository.UnitToPartnerEdcMappingDao;
import com.stpl.tech.master.domain.model.AddLocationRequest;
import com.stpl.tech.master.domain.model.AddonList;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CancellationReason;
import com.stpl.tech.master.domain.model.CashMetadata;
import com.stpl.tech.master.domain.model.CategoryAttributesDomain;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.Department;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.Division;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import com.stpl.tech.master.domain.model.EmploymentType;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskLocationDetails;
import com.stpl.tech.master.domain.model.KioskMachine;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.PreAuthApi;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.TrimmedProductData;
import com.stpl.tech.master.domain.model.TrimmedProductPrice;
import com.stpl.tech.master.domain.model.TrimmedProductVO;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitContactDetails;
import com.stpl.tech.master.readonly.domain.model.UnitProductRecipesKeys;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.domain.RequestContext;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class MasterMetadataServiceImpl implements MasterMetadataService {

    Logger LOG = LoggerFactory.getLogger(MasterMetadataServiceImpl.class);

    @Autowired
    private MasterMetadataDao dao;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private ExternalPartnerDao externalPartnerDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private CacheReferenceMetadataDao cacheReferenceMetadataDao;

    @Autowired
    private UnitToPartnerEdcMappingDao unitToPartnerEdcMappingDao;

    @Autowired
    private AuditChangeLogDao auditChangeLogDao;

    @Autowired
    private AuditChangeLogHistoryDao auditChangeLogHistoryDao;

    @Autowired
    private SourceToCondimentMappingDao sourceToCondimentMappingDao;

    @Autowired
    private ProductToCondimentMappingDao productToCondimentMappingDao;

    @Autowired
    private UnitIpAddressDao unitIpAddressDao;

    @Autowired
    private CategoryAttributesDao categoryAttributesDao;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private MasterProperties properties;

//    @Autowired
//    @Lazy
//    private RecipeService recipeService;

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.PosMetadataService#getUnit(int)
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Unit getUnit(int unitId, boolean getAll) throws DataNotFoundException {
        return dao.getUnit(unitId, getAll);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<Product> getUnitProducts(int unitId, boolean getAll) throws DataNotFoundException {
        Collection<Product> products = dao.getUnitProducts(unitId, getAll);
        for (Product product : products) {
            for (ProductPrice price : product.getPrices()) {
                price.setRecipe(recipeCache.getRecipe(product.getId(), price.getDimension(), price.getProfile()));
            }
        }
        return products;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<Product> getAllUnitProducts(int unitId, boolean getAll, boolean getRecipes) throws DataNotFoundException {
        Collection<Product> products = dao.getUnitProducts(unitId, getAll);
        Map<Integer, Product> productMap = new HashMap<>();
        products.forEach(product -> productMap.put(product.getId(), product));
        Collection<UnitPartnerBrandMappingData> data = masterDataCache.getUnitwisePartnerBrandMappingMetaData(unitId);
        for (UnitPartnerBrandMappingData unitPartnerBrandMappingData : data) {
            if (unitPartnerBrandMappingData.getUnitId().equals(unitId)) {
                products = dao.getUnitProducts(unitPartnerBrandMappingData.getPriceProfileUnitId(), getAll);
                products.forEach(product -> {
                    if (!productMap.containsKey(product.getId())) {
                        productMap.put(product.getId(), product);
                    }
                });
            }
        }
        for (Product product : productMap.values()) {
            for (ProductPrice price : product.getPrices()) {
            	if(getRecipes) {
            		price.setRecipe(recipeCache.getRecipe(product.getId(), price.getDimension(), price.getProfile()));
            		price.setRecipeId(price.getRecipe() == null ? null : price.getRecipe().getRecipeId());
            	}else {
            		RecipeDetail  recipe = recipeCache.getRecipe(product.getId(), price.getDimension(), price.getProfile());
            		price.setRecipeId(recipe == null ? null : recipe.getRecipeId());
            	}
            }
        }
        return productMap.values();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<IdCodeName> getUnitProductsTrimmed(int unitId, boolean getAll) throws DataNotFoundException {
        Collection<Product> products = dao.getUnitProducts(unitId, getAll);
        List<IdCodeName> idCodeNames = new ArrayList<>();
        for (Product product : products) {
            idCodeNames.add(new IdCodeName(product.getId(), product.getName(), product.getShortCode()));
        }
        return idCodeNames;
    }

    @Override
    public Boolean saveAuditLog(Integer keyId, String keyType, Integer changedBy, Object newObject, String changeType) throws DataUpdationException, DataNotFoundException {

        try {
            AuditChangeLog auditChangeLog = auditChangeLogDao.findByKeyId(keyId, keyType);
            if (Objects.nonNull(auditChangeLog)) {
                LOG.info("Found Already Mongo Document  for unit :: {}",keyId);
                auditChangeLog.setNewObject(newObject);
                auditChangeLog.setUpdatedOn(AppUtils.getCurrentTimeIST());
                auditChangeLog.setChangeType(changeType);
                auditChangeLog.setPreviousVersionId(auditChangeLog.getVersion());
            } else {
                LOG.info("Creating New Mongo Document for unit :: {} ",keyId);
                auditChangeLog = new AuditChangeLog();
                auditChangeLog.setKeyId(keyId);
                auditChangeLog.setKeyType(keyType);
                auditChangeLog.setChangeType(changeType);
                if (Objects.nonNull(changedBy)) {
                    auditChangeLog.setChangedBy(changedBy);
                }
                auditChangeLog.setCreatedOn(AppUtils.getCurrentTimeIST());
                auditChangeLog.setUpdatedOn(AppUtils.getCurrentTimeIST());
                auditChangeLog.setNewObject(newObject);
            }
            auditChangeLogDao.save(auditChangeLog);
            AuditChangeLogHistory auditHistory = new AuditChangeLogHistory();
            auditHistory.setAuditChangeLog(auditChangeLog);
            auditChangeLogHistoryDao.save(auditHistory);
            return  true;
        }catch (Exception e){
            LOG.error("Error Saving Unit Details in Mongo");
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean validUnitProductRecipes(UnitProductRecipesKeys request) {
        boolean recipesMatch = true;
        if (request != null && request.getUnitId() != null) {
            for (Integer productId : request.getProductDimensionRecipeIdMap().keySet()) {
                Map<String, Integer> dimensionRecipeIdMap = request.getProductDimensionRecipeIdMap().get(productId);
                for (String dimension : dimensionRecipeIdMap.keySet()) {
                    if (recipesMatch && !recipeCache.getUnitProductRecipeId(request.getUnitId(), productId, dimension).equals(dimensionRecipeIdMap.get(dimension))) {
                        recipesMatch = false;
                    }
                }
            }
        }
        return recipesMatch;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.PosMetadataService#getAllListData(java.
     * lang.String)
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ListData> getAllListData(String group, boolean getAll) throws DataNotFoundException {
        return dao.getAllListData(group, getAll);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.PosMetadataService#getListData(java.
     * lang.String)
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ListData getListData(String code, boolean getAll) throws DataNotFoundException {
        return dao.getListData(code, getAll);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.PosMetadataService#getAllPaymentMode()
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PaymentMode> getAllPaymentMode(PaymentCategory category) throws DataNotFoundException {
        return dao.getAllPaymentMode(category);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.PosMetadataService#getDiscountCodes()
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ListData getDiscountCodes(boolean getAll) throws DataNotFoundException {
        return dao.getDiscountCodes(getAll);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.PosMetadataService#getAllAddons()
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AddonList> getAllAddons(boolean getAll) throws DataNotFoundException {
        return dao.getAllAddons(getAll);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.PosMetadataService#getAllCategories()
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ListData> getAllCategories(boolean getAll) throws DataNotFoundException {
        return dao.getAllCategories(getAll);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.PosMetadataService#getAllProducts()
     */

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Product> getAllProductsFromDao() throws DataNotFoundException {
        return dao.getAllProducts();
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Product> getAllProducts(Integer brandId) throws DataNotFoundException {
        return getAllProductByBrand(masterDataCache.getAllProducts(), brandId);
    }

    public List<Product> getAllProductByBrand(List<Product> products, Integer brandId) {
        brandId = Objects.nonNull(brandId) ? brandId : RequestContext.getBrandId();
        Integer companyId = RequestContext.getCompanyId();

        if(Objects.isNull(brandId) && Objects.isNull(companyId)) {
            return products;
        }

        List<Product> filteredProducts = new ArrayList<>();
        List<Integer> mappedBrands = MasterUtil.getMappedBrands();

        for (Product product : products) {
            if(Objects.nonNull(brandId)) {
                if(product.getBrandId().equals(brandId)) {
                    filteredProducts.add(product);
                }
            } else {
                for (Integer brand : mappedBrands) {
                    if(product.getBrandId().equals(brand)) {
                        filteredProducts.add(product);
                    }
                }
            }
        }
        return filteredProducts;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.kettle.core.service.PosMetadataService#getAllProducts()
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Product> getAllActiveProducts() throws DataNotFoundException {
        List<Product> products = dao.getAllProducts();
        List<Product> activeProducts = new ArrayList<>();
        if (products != null && products.size() > 0) {
            for (Product product : products) {
                if (ProductStatus.ACTIVE.equals(product.getStatus())) {
                    activeProducts.add(product);
                }
            }
        }
        return activeProducts;
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Long getAllUnitCount() throws DataNotFoundException {
        return dao.getAllUnitCount();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Unit> getAllUnits(int start, int batchSize) throws DataNotFoundException {
        return dao.getAllUnits(start, batchSize);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Unit> getAllUnits() throws DataNotFoundException {
        return dao.getAllUnits();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Unit> getAllUnits(UnitCategory category) throws DataNotFoundException {
        return dao.getAllUnits(category);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Division> getAllDivisions() throws DataNotFoundException {
        return dao.getAllDivisions();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Department> getAllDepartments() throws DataNotFoundException {
        return dao.getAllDepartments();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Designation> getAllDesignations() throws DataNotFoundException {
        return dao.getAllDesignations();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<TaxProfile> getAllTaxProfile() throws DataNotFoundException {
        return dao.getAllTaxProfile();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public TransactionMetadata getTransactionData(boolean isAndroid) throws DataNotFoundException {
        return dao.getTransactionData(isAndroid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public TransactionMetadata getMetadataCategories() throws DataNotFoundException {
        return dao.getMetadataCategories();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, List<ListData>> getAllListData() throws DataNotFoundException {
        return dao.getAllListData();
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, List<ListData>> getListTypesByType(List<String> refTypes) throws DataNotFoundException {
        return dao.getListTypesByType(refTypes);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ListData upsertRefLookUp(ListData listData) throws DataUpdationException, DataNotFoundException {
        ListData list = dao.upsertRefLookUp(listData);
        return list;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<DenominationDetail> getAllDenominations() throws DataNotFoundException {
        return dao.getAllDenominations();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Employee> getAllEmployees() throws DataNotFoundException {
        return dao.getAllEmployees().stream().map(employeeDetail -> {
            return convertEmployee(employeeDetail,true);
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Unit updateCacheForManager(Unit newUnit) throws DataNotFoundException {
        UnitDetail unitDetail = dao.find(UnitDetail.class, newUnit.getId());
        unitDetail.setUnitCafeManager(newUnit.getUnitCafeManager());
        unitDetail.setLastHandoverFrom(newUnit.getLastHandoverFrom());
        unitDetail.setLastHandoverDate(newUnit.getLastHandoverDate());
        dao.update(unitDetail);
//        masterDataCache.getUnits().put(newUnit.getId(), newUnit);
//        cache.addUnit(newUnit);
        return newUnit;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PreAuthApi> getPreAuthenticatedApis() {
        List<PreAuthenticatedApiData> preAuthenticatedApiDatas = new ArrayList<PreAuthenticatedApiData>(
            dao.findAll(PreAuthenticatedApiData.class));
        List<PreAuthApi> preAuthApis = preAuthenticatedApiDatas.stream().map(MasterDataConverter::convert)
            .collect(Collectors.toList());
        return preAuthApis;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<KioskCompanyDetails> getAllKioskCompanies() {
        List<KioskCompanyDetailsData> allKioskCompanies = dao.getAllKioskCompanies();
        if (allKioskCompanies != null && !allKioskCompanies.isEmpty()) {
            List<KioskCompanyDetails> companies = allKioskCompanies.stream().map(MasterDataConverter::convert)
                .collect(Collectors.toList());
            return companies;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<KioskMachine> getAllKioskMachines() {
        Optional<List<KioskMachineDetailsData>> kioskLocationDetailsDataList = Optional.of(dao.getAllKioskMachines());
        if (kioskLocationDetailsDataList.isPresent()) {
            return kioskLocationDetailsDataList.get().stream().map(kioskMachineDetailsData -> {
                KioskLocationDetailsData kioskLocationDetailsData = kioskMachineDetailsData.getLocationDetailsData();
                IdCodeName unitData = null;
                if (kioskLocationDetailsData != null && kioskLocationDetailsData.getAssignedUnit() != null) {
                    unitData = MasterDataConverter.convertToIdCodeName(kioskLocationDetailsData.getAssignedUnit());
                }
                return MasterDataConverter.convert(kioskMachineDetailsData, unitData);
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<KioskLocationDetails> getAllKioskLocations() {
        Optional<List<KioskLocationDetailsData>> locationsDataList = Optional.of(dao.getAllKioskLocations());
        List<KioskLocationDetails> locationDetailsList = new ArrayList<>();
        if (locationsDataList.isPresent()) {
            locationsDataList.get().stream().forEach(kioskLocationDetailsData -> locationDetailsList
                .add(MasterDataConverter.convert(kioskLocationDetailsData, true)));
        }
        return locationDetailsList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PaymentModeAttributes> getPaymentModeAttributes(int paymentModeId) throws DataNotFoundException {
        return dao.getPaymentModeAttributes(paymentModeId);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.MasterMetadataService#getAlllocations()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Location> getAllLocations() {
        return dao.getAllLocations();
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.MasterMetadataService#getAllStates()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<State> getAllStates() {
        return dao.getAllStates();
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.service.MasterMetadataService#getAllCancellationReasons()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CancellationReason> getAllCancellationReasons() {
        return dao.getAllCancellationReasons();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Company> getAllCompanies() {
        return dao.getAllCompanies();
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.service.MasterMetadataService#getAllPaymentModeCommisson()
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, BigDecimal> getAllPaymentModeCommisson() throws DataNotFoundException {
        return dao.getAllPaymentModeCommisson();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExpenseMetadata> getExpenseList(String category) {
        return masterDataCache.getExpenseMetadataIMap().values()
                .stream()
                .filter(expense -> AppConstants.YES.equals(expense.getIsInternalExpense()))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExpenseMetadata> getAllExpenseList() {
        List<ExpenseMetadata> data = dao.getAllExpenseList();
        List<Integer> expenseIds = new ArrayList<>();
        for (ExpenseMetadata expenseMetadata : data) {
            expenseIds.add(expenseMetadata.getId());
        }
        if (expenseIds.size() > 0) {
            Map<Integer, List<ExpenseValidation>> map = dao.getExpenseValidations(expenseIds);
            if (map.keySet().size() > 0) {
                for (ExpenseMetadata expenseMetadata : data) {
                    if (map.get(expenseMetadata.getId()) != null) {
                        expenseMetadata.setValidations(map.get(expenseMetadata.getId()));
                    }
                }
            }
        }
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<WarningMetadata> getWarningReasonList(String type) {
        return dao.getWarningReasonList(type);
    }

    @Override
    public List<PaymentMode> getPaymentModes() {
        return new ArrayList<>(masterDataCache.getPaymentModes().values());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Unit> getUnitsOfAreaManager(Integer amId) {
        return dao.getUnitsOfAreaManager(amId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Unit> getUnitsOfCafeManager(Integer amId) {
        return dao.getUnitsOfCafeManager(amId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ExternalPartnerDetail> getExternalPartnerDetail(String status) {
        return externalPartnerDao.getExternalPartnerDetail(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CacheReferenceMetadata> getCacheReferenceMetadata(String status) {
        return cacheReferenceMetadataDao.findByCacheStatus(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitToPartnerEdcMapping> getUnitPartnerEdcMappingMetadata(String status) {
        return unitToPartnerEdcMappingDao.findAllByStatus(status);
    }

    @Override
    public List<UnitIpAddressData> getAllUnitIpAddressData() {
        return unitIpAddressDao.findAll();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CashMetadata> getCashMetadata() {
        List<CashMetadata> resultList = new ArrayList<CashMetadata>();
        List<CashMetadataDetail> list = dao.findAll(CashMetadataDetail.class);
        if (list != null && !list.isEmpty()) {
            for (CashMetadataDetail cmd : list) {
                if (AppConstants.ACTIVE.equals(cmd.getStatus())) {
                    resultList.add(MasterDataConverter.convert(cmd));
                }
            }
        }
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateHandOverDateForUnit(String handOverData, Integer unitId) throws ParseException {
        UnitDetail unit = dao.find(UnitDetail.class, unitId);
        unit.setHandoverDate(new SimpleDateFormat("yyyy-MM-dd").parse(handOverData));
        dao.update(unit);
        return true;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Collection<TrimmedProductVO> getAllUnitProductsPrices(int unitId) {
		Set<Integer> unitIds = new HashSet<>();
		unitIds.add(unitId);
		Collection<UnitPartnerBrandMappingData> data = masterDataCache.getUnitwisePartnerBrandMappingMetaData(unitId);
		for (UnitPartnerBrandMappingData unitPartnerBrandMappingData : data) {
			unitIds.add(unitPartnerBrandMappingData.getPriceProfileUnitId());
		}
		Collection<TrimmedProductVO> productVOs = dao.getActiveProductMappingsForUnits(unitId, unitIds);
		for (TrimmedProductVO product : productVOs) {
			List<String> indexToBeRemoved = new ArrayList<>();
			for (TrimmedProductPrice price : product.getPrices().values()) {
				Integer recipeId = null;
				recipeId = recipeCache.getUnitProductRecipeId(unitId, product.getId(), price.getDimension());
				if (recipeId == null) {
					recipeId = recipeCache.getUnitProductRecipeId(price.getUnitId(), product.getId(),
							price.getDimension());
				}
				if (recipeId == null) {
					indexToBeRemoved.add(price.getDimension());
				}
				price.setRecipeId(recipeId);
			}
			indexToBeRemoved.forEach(p -> {
				product.getPrices().remove(p);
			});
		}
		return productVOs;
	}

    @Override
    public Collection<TrimmedProductVO> getAllUnitProductsPricesV1(int unitId){
        Set<Integer> unitIds = new HashSet<>();
        unitIds.add(unitId);
        Collection<UnitPartnerBrandMappingData> data = masterDataCache.getUnitwisePartnerBrandMappingMetaData(unitId);
        for (UnitPartnerBrandMappingData unitPartnerBrandMappingData : data) {
            unitIds.add(unitPartnerBrandMappingData.getPriceProfileUnitId());
        }
        Collection<TrimmedProductVO> productVOs = dao.getActiveProductMappingsForUnitsV1(unitId, unitIds);
        for (TrimmedProductVO product : productVOs) {
            List<String> indexToBeRemoved = new ArrayList<>();
            for (TrimmedProductPrice price : product.getPrices().values()) {
                Integer recipeId = null;
                recipeId = recipeCache.getUnitProductRecipeId(unitId, product.getId(), price.getDimension());
                if (recipeId == null) {
                    recipeId = recipeCache.getUnitProductRecipeId(price.getUnitId(), product.getId(),
                            price.getDimension());
                }
                if (recipeId == null) {
                    indexToBeRemoved.add(price.getDimension());
                }
                price.setRecipeId(recipeId);
            }
            indexToBeRemoved.forEach(p -> {
                product.getPrices().remove(p);
            });
        }
        return productVOs;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Location addLocation(AddLocationRequest locationDetail) {
        try {
            LOG.info("Adding location detail for data ::: {}",JSONSerializer.toJSON(locationDetail));
            LocationDetail detail = new LocationDetail();
            CountryDetail countryDetail = dao.find(CountryDetail.class, locationDetail.getCountryId());
            StateDetail stateDetail = dao.find(StateDetail.class, locationDetail.getStateId());
            detail.setCity(locationDetail.getCity());
            detail.setCityCode(locationDetail.getCityCode());
            detail.setFunctionalFlag(AppConstants.getValue(locationDetail.getFunctionalFlag()));
            detail.setState(stateDetail);
            detail.setCountryCode(countryDetail.getCountryCode());
            detail.setStatus(locationDetail.getStatus());
            detail = dao.add(detail);
            Location location = MasterDataConverter.convert(detail);
            masterDataCache.getAllLocations().put(location.getCode(),location);
            return location;
        }catch (Exception e){
            LOG.error("Error while adding Location detail for location detail:: {}", JSONSerializer.toJSON(locationDetail));
        }
        return null;
    }

    @Override
    public List<RegionMap> getAllRegions(){
        return dao.getAllRegions();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<TrimmedProductData> getUnitProductDimensions() throws DataNotFoundException {
        List<Product> products = dao.getAllProducts();
        Map<Integer,List<String>> dimensionMap = new HashMap<>();
        List<TrimmedProductData> trimmedData = new ArrayList<>();
        for (Product product : products) {
            List<String> dimensions;
            if(dimensionMap.containsKey(product.getDimensionProfileId())){
                dimensions=dimensionMap.get(product.getDimensionProfileId());
            }else{
                dimensions=dao.getDimensionById(product.getDimensionProfileId());
                dimensionMap.put(product.getDimensionProfileId(), dimensions);
            }
            trimmedData.add(new TrimmedProductData(product.getId(), product.getName(), dimensions));
        }
        return trimmedData;
    }

    public Employee convertEmployee(EmployeeDetail details, boolean getManager) {
        if (details == null) {
            return null;
        }
        Employee data = new Employee();
        data.setId(details.getEmpId());
        data.setGender(details.getEmpGender());
        data.setEmploymentStatus(EmploymentStatus.fromValue(details.getEmploymentStatus()));
        data.setEmploymentType(EmploymentType.fromValue(details.getEmploymentType()));
        data.setName(details.getEmpName());
        data.setPrimaryContact(details.getEmpContactNum1());
        data.setSecondaryContact(details.getEmpContactNum2());
        data.setBiometricId(details.getBiometricIdentifier());
        data.setJoiningDate(details.getJoiningDate());
        if (getManager) {
            data.setReportingManager(convertEmployee(details.getReportingManager(), false));
        }
//        data.setCurrentAddress(masterDataCache.getAddress(details.getEmpCurrentAddr()));
////        data.setCurrentAddress(convertAddress(details.getAddressInfoByEmpCurrentAddr()));
////        data.setPermanentAddress(convertAddress(details.getAddressInfoByEmpPermanentAddr()));
//        data.setPermanentAddress(masterDataCache.getAddress(details.getEmpPermanentAddr()));
        data.setDepartment(masterDataCache.getDepartment(details.getDepartment().getDeptId()));
        data.setDesignation(masterDataCache.getDesignation(details.getDesignation().getDesignationId()));
        data.setEmployeeEmail(details.getEmployeeEmail());
        data.setApplications(ApplicationName.accessFor(data.getDesignation()));
        data.setEmployeeCode(details.getEmployeeCode());
        data.setCommunicationChannel(details.getCommunicationChannel());
        data.setEmployeeMealEligible(AppConstants.getValue(details.getEmployeeMealEligible()));
        data.setDob(details.getDob());
        data.setHrExecutive(details.getHrExecutive());
        data.setLeaveApprovalAuthority(details.getLeaveApprovalAuthority());
        data.setLocCode(details.getLocCode());
        data.setReasonForTermination(details.getReasonForTermination());
        if (Objects.nonNull(details.getUserPolicyData())) {
            data.setUserPolicyId(details.getUserPolicyData().getUserPolicyId());
        }
        data.setCompany(masterDataCache.getCompany(details.getCompanyDetail().getCompanyId()));
        data.setImageKey(details.getImageKey());
        return data;
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  List<ProductCondimentGroup> getAllProductCondimentGroup(String status) throws DataNotFoundException{
        try {
           return sourceToCondimentMappingDao.findAllByStatus(status);
        }catch (Exception e){
            LOG.error("Cannot find any active product Condiment Group",e);
        }
        return  null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  List<ProductCondimentItem> getAllProductCondimentItem(String status) throws DataNotFoundException{
        try {
            return productToCondimentMappingDao.findAllByStatus(status);
        }catch (Exception e){
            LOG.error("Cannot find any active product Condiment Item",e);
        }
        return  null;
    }

    @Override
    public List<String> getDelayReason() throws DataNotFoundException {
        return dao.getDelayReasonListData();
    }
    @Override
    public Map<Integer, UnitContactDetails> getAllUnitContactDetails() throws DataNotFoundException {
        List<UnitContactDetailsData> unitContactDetailsDataList = dao.getAllUnitContactDetails();
        Map<Integer, UnitContactDetails> unitContactDetailsHashMap = new HashMap<>();
        for (UnitContactDetailsData unitContactDetailsData : unitContactDetailsDataList) {

            UnitContactDetails unitContactDetails = new UnitContactDetails();
            unitContactDetails = MasterDataConverter.convert(unitContactDetailsData);
            unitContactDetailsHashMap.put(unitContactDetails.getUnitId(), unitContactDetails);
        }
        return unitContactDetailsHashMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<String> getCompensationReason() throws DataNotFoundException {
        return dao.getCompensationReasonListData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CategoryAttributes> getCategoryAttributes() {
        try {
            return categoryAttributesDao.findAll();
        } catch (Exception e) {
            LOG.info("Error while getting category attributes data !!!!!");
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean updateCategoryAttributes(Map<Integer, CategoryAttributesDomain> categoryAttributes, Boolean removeSequencing) {
        LOG.info("update");
        try {
            List<CategoryAttributes> attributes = categoryAttributesDao.findByRlIdIn(categoryAttributes.keySet());
            List<CategoryAttributes> newEntry = new ArrayList<>();
            if (!CollectionUtils.isEmpty(attributes)) {
                for (CategoryAttributes attribute : attributes) {
                    if (Objects.nonNull(categoryAttributes.get(attribute.getRlId()))) {
                        CategoryAttributesDomain value = categoryAttributes.get(attribute.getRlId());
                        createCategoryAttributes(attribute, value.getRlId(), value.getRlName(),
                                (Objects.nonNull(value.getDefaultVisibility()) && !removeSequencing) ? value.getDefaultVisibility() : AppConstants.NO,
                                (Objects.nonNull(value.getSequenceNumber()) && value.getSequenceNumber() > 0 && !removeSequencing) ? value.getSequenceNumber() : null);
                        categoryAttributes.remove(value.getRlId());
                    }
                }
                categoryAttributesDao.saveAll(attributes);
            }
            if (Objects.nonNull(categoryAttributes)) {
                categoryAttributes.values().stream().forEach(attribute -> {
                    newEntry.add(createCategoryAttributes(new CategoryAttributes(),
                            attribute.getRlId(),
                            attribute.getRlName(),
                            (Objects.nonNull(attribute.getDefaultVisibility()) && !removeSequencing) ? attribute.getDefaultVisibility() : AppConstants.NO,
                            (Objects.nonNull(attribute.getSequenceNumber()) && attribute.getSequenceNumber() > 0 && !removeSequencing) ? attribute.getSequenceNumber() : null));
                });
            }
            if (!CollectionUtils.isEmpty(newEntry)) {
                categoryAttributesDao.saveAll(newEntry);
            }
        } catch (Exception e) {
            LOG.info("Error while updating Category properties ::::: {}", e.getMessage());
        }
        return true;
    }

    public CategoryAttributes createCategoryAttributes(CategoryAttributes attribute, Integer rlId, String rlName,String defaultVisibility,
                                                       Integer sequenceNumber){
        attribute.setRlId(rlId);
        attribute.setRlName(rlName);
        attribute.setDefaultVisibility(defaultVisibility);
        attribute.setSequenceNumber(sequenceNumber);
        return attribute;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean uploadCategoryImage(Map<Integer, CategoryAttributesDomain> categoryImages) throws Exception {
        List<CategoryAttributes> attributes = categoryAttributesDao.findByRlIdIn(categoryImages.keySet());
        List<CategoryAttributes> newEntry = new ArrayList<>();
        if(!CollectionUtils.isEmpty(attributes)) {
            for (CategoryAttributes attribute : attributes) {
                MultipartFile multipartFile = convertFileToMultipartFile(categoryImages.get(attribute.getRlId()).getTagImageFile(),attribute.getRlId().toString());
                String fileUrl = uploadTagImageToS3(multipartFile,attribute.getRlId());
                if(Objects.nonNull(fileUrl)){
                    attribute.setTagImageUrl(fileUrl);
                }
                categoryImages.remove(attribute.getRlId());
            }
            categoryAttributesDao.saveAll(attributes);
        }
        if(Objects.nonNull(categoryImages)){
            for(CategoryAttributesDomain domain : categoryImages.values()) {
                MultipartFile multipartFile = convertFileToMultipartFile(categoryImages.get(domain.getRlId()).getTagImageFile(),domain.getRlId().toString());
                String fileUrl = uploadTagImageToS3(multipartFile,domain.getRlId());
                if(!StringUtils.isEmpty(fileUrl)) {
                    CategoryAttributes attributeData = new CategoryAttributes();
                    attributeData.setRlId(domain.getRlId());
                    attributeData.setRlName(domain.getRlName());
                    attributeData.setDefaultVisibility(AppConstants.NO);
                    attributeData.setTagImageUrl(fileUrl);
                    newEntry.add(attributeData);
                }
            }
            if(!CollectionUtils.isEmpty(newEntry)){
                categoryAttributesDao.saveAll(newEntry);
            }
        }
        return true;
    }

    public String uploadTagImageToS3(MultipartFile file, Integer rlId) throws Exception {
        if (file.isEmpty()) {
            return null;
        }
        if (file.getContentType().equalsIgnoreCase(MediaType.PNG.toString())) {
            String fileExtension = file.getContentType().substring(file.getContentType().indexOf("/") + 1);
            String fileName = rlId  + "." + fileExtension;
            String baseDir = "categoryDataImages/" ;
            FileDetail s3File = fileArchiveService.saveFileToS3(properties.getS3ProductBucket(), baseDir, fileName,
                    file);
            if (Objects.nonNull(s3File) && Objects.nonNull(s3File.getUrl())) {
                return s3File.getUrl();
            }
        } else {
            throw new Exception("Only JPEG, PNG and JPG files are allowed.");
        }
        return null;
    }

    public MultipartFile convertFileToMultipartFile(String base64String,String fileName) throws IOException {
        String[] base64Components = base64String.split(",");
        byte[] decodedBytes = Base64.getDecoder().decode(base64Components[1]);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(decodedBytes);
        return  new MockMultipartFile(
                fileName,
                fileName,
                MediaType.PNG.toString(),
                inputStream
        );
    }

    @Override
    public Map<Integer, Map<Integer, List<IdCodeName>>> allProducts() {
        Integer brandId = RequestContext.getBrandId();
        Integer companyId = RequestContext.getCompanyId();
        List<Product> products = masterDataCache.getAllProducts();
        Map<Integer, Map<Integer, List<IdCodeName>>> brandProductsMap = new HashMap<>();

        if (products == null || products.isEmpty()) {
            return brandProductsMap;
        }

        for (Product product : products) {
            if (product == null || product.getBrandId() == null || product.getType() == 0) {
                continue;
            }
            brandProductsMap.computeIfAbsent(product.getBrandId(), brand -> new HashMap<>());
            Map<Integer, List<IdCodeName>> productAndTypeMap = brandProductsMap.get(product.getBrandId());
            IdCodeName idCodeName = new IdCodeName(product.getId(), product.getName(), product.getStatus().name(), product.getDimensionProfileId()+"");
            productAndTypeMap.computeIfAbsent(product.getType(), type -> new ArrayList<>()).add(idCodeName);
        }

        Map<Integer, Map<Integer, List<IdCodeName>>> filteredMap = new HashMap<>();
        if (Objects.nonNull(brandId)) {
            filteredMap.put(brandId, brandProductsMap.get(brandId));
            return filteredMap;
        } else if (Objects.nonNull(companyId)) {
            List<Integer> mappedBrands = MasterUtil.getMappedBrands();
            for (Integer b : mappedBrands) {
                filteredMap.put(b, brandProductsMap.get(b));
            }
            return filteredMap;
        }
        return brandProductsMap;
    }

    @Override
    public Map<Integer, List<Brand>> getCompanyBrandsMapping() {
        Integer companyId = RequestContext.getCompanyId();
        if (Objects.nonNull(companyId)) {
            Map<Integer, List<Brand>> companyBrandsMap = new HashMap<>();
            companyBrandsMap.put(companyId, masterDataCache.getCompanyBrandsMap().get(companyId));
            return companyBrandsMap;
        }
        return masterDataCache.getCompanyBrandsMap();
    }
}
