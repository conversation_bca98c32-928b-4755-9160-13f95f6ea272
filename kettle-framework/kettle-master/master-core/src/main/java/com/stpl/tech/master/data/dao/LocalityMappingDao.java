package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.locality.model.LocalityMapping;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * Copyright (C) $today.year, Sunshine Teahouse Private Limited - All Rights
 * Reserved Unauthorized copying of this file, via any medium is strictly
 * prohibited Proprietary and confidential Created by shikhar on 20-01-2017.
 */
@Repository
public interface LocalityMappingDao extends MongoRepository<LocalityMapping, String> {

	@Query("{'locality' : ?0, 'city' : ?1, 'state' : ?2}")
	public LocalityMapping findBylocalityAndCityAndState(String locality, String city, String state);
}
