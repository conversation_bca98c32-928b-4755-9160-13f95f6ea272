package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.recipe.model.DispenserRecipeDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DispenserRecipeDao extends MongoRepository<DispenserRecipeDetail, String> {
    DispenserRecipeDetail findByRecipeKey(String recipeKey);


}
