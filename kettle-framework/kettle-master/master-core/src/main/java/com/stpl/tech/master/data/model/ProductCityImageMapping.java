package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "PRODUCT_CITY_IMAGE_MAPPING")
public class ProductCityImageMapping implements java.io.Serializable {

    private Integer productImgMappingId;
    private Integer productId;
    private String imageType;
    private String imageUrl;
    private Integer lastUpdatedBy;
    private Date lastUpdationTime;
    private String status;
    private String cityName;

    private String daySlot;


    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PRODUCT_CITY_IMAGE_MAPPING_ID", unique = true, nullable = false)
    public Integer getProductImgMappingId() {
        return productImgMappingId;
    }

    public void setProductImgMappingId(Integer productImgMappingId) {
        this.productImgMappingId = productImgMappingId;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "IMAGE_TYPE", nullable = false)
    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    @Column(name = "IMAGE_URL", nullable = false)
    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = false, length = 50)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATION_TIME", nullable = false, length = 19)
    public Date getLastUpdationTime() {
        return lastUpdationTime;
    }


    public void setLastUpdationTime(Date lastUpdationTime) {
        this.lastUpdationTime = lastUpdationTime;
    }

    @Column(name = "STATUS", nullable = false, length = 50)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "CITY_NAME")
    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }


    @Column(name = "DAY_SLOT")
    public String getDaySlot() {
        return daySlot;
    }

    public void setDaySlot(String daySlot) {
        this.daySlot = daySlot;
    }
}