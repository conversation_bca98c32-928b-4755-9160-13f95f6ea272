package com.stpl.tech.master.core.external.cache;

import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.util.AppConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


@Slf4j
public class CacheProxy {

    private Map<Integer, Product> productDetails;
    private Map<Integer, ProductBasicDetail> productBasicDetails;
    private Set<Integer> employeeMealProducts;
    private Map<Integer, ListData> listCategoryData;
    private Map<Integer, ListData> dimensionProfileData;
    private Map<String, ListData> itemPerTicket;




    public CacheProxy(String type){
        if(type.equals(AppConstants.GET_PRODUCT_PROXY)) {
            createProductCache();
        }
        if(type.equals(AppConstants.GET_LIST_PROXY)){
            createListCache();
        }
    }

    public void createProductCache(){
        log.info("##### Creating Product Proxy Cache ######");
        productDetails = new HashMap<>();
        productBasicDetails = new HashMap<>();
        employeeMealProducts = new HashSet<>();
    }

    public void createListCache(){
        log.info("##### Creating ListData Proxy Cache ######");
        listCategoryData = new HashMap<>();
        dimensionProfileData = new HashMap<>();
        itemPerTicket = new HashMap<>();
    }

    public Map<Integer, Product> getProductDetails() {
        return productDetails;
    }

    public Map<Integer, ProductBasicDetail> getProductBasicDetails() {
        return productBasicDetails;
    }

    public Set<Integer> getEmployeeMealProducts() {
        return employeeMealProducts;
    }

    public Map<Integer, ListData> getListCategoryData() {
        return listCategoryData;
    }

    public Map<Integer, ListData> getDimensionProfileData() {
        return dimensionProfileData;
    }

    public Map<String, ListData> getItemPerTicket() {
        return itemPerTicket;
    }




}
