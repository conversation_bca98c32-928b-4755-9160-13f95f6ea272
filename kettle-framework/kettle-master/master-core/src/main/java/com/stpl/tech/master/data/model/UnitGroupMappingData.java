package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;


import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;



@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode
@Table(name = "UNIT_GROUP_MAPPING")
public class UnitGroupMappingData implements Serializable {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GROUP_ID")
    private Integer groupId;

    @Column(name = "GROUP_NAME", nullable = false, length = 200)
    private String groupName;

    @Lob
    @Column(name = "UNIT_IDS", nullable = false)
    private String unitIds;

    @Column(name = "PRIORITY")
    private Integer priority;

    @Column(name = "STATUS", length = 45)
    private String status;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;


    public List<Integer> getUnitIdsAsList() {
        if (unitIds == null || unitIds.trim().isEmpty()) {
            return List.of();
        }
        return Arrays.stream(unitIds.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    public void setUnitIdsFromList(List<Integer> unitIdsList) {
        if (unitIdsList == null || unitIdsList.isEmpty()) {
            this.unitIds = "";
        } else {
            this.unitIds = unitIdsList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
    }

}

