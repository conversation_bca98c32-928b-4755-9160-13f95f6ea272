package com.stpl.tech.master.data.model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "PACKAGING_GROUP_MAPPING")
public class PackagingGroupMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PACKAGING_GROUP_MAPPING_ID", nullable = false)
    private Integer id;

    @Column(name = "GROUP_ID")
    private Integer groupId;

    @Column(name = "PARTNER", length = 100)
    private String partner;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "PACKAGING_TYPE", length = 100)
    private String packagingType;

    @Column(name = "PACKAGING_VALUE", precision = 16, scale = 6)
    private BigDecimal packagingValue;

    @Column(name = "MAPPING_STATUS", length = 45)
    private String mappingStatus;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
}
