package com.stpl.tech.master.data.model;

import com.stpl.tech.master.util.CustomerEventType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "CUSTOMER_EVENTS_DETAIL_DATA")
public class CustomerEventDetailData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Integer id;
    @Column(name = "CUSTOMER_ID")
    private Integer customerId;
    @Column(name = "CONTACT_NUMBER")
    private String contactNumber;
    @Column(name = "EVENT_MONTH")
    private Integer eventMonth;
    @Column(name = "EVENT_TYPE", columnDefinition = "varchar")
    @Enumerated(EnumType.STRING)
    private CustomerEventType customerEventType;
    @Column(name = "CREATED_AT")
    private Date createdAt;
    @Column(name = "UPDATED_AT")
    private Date updatedAt;
    @Column(name = "EVENT_DATE")
    private Integer eventDate;
    @Column(name = "ACQUISITION_SOURCE")
    private String acquisitionSource;
    @Column(name = "COUPON_GENERATED_AT")
    private Date couponGenerationAt;
    @Column(name = "EVENT_OFFER_1")
    private String eventOffer1;
    @Column(name = "EVENT_OFFER_2")
    private String eventOffer2;
}
