/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.cache;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.google.common.base.Stopwatch;
import com.hazelcast.collection.ISet;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.ConfigAttributeValue;

@Service
public class EnvironmentPropertiesCache {

	private static final Logger LOG = LoggerFactory.getLogger(EnvironmentPropertiesCache.class);

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	/**
	 * apllicationName : List<ConfigAttributeValue>
	 */
	private IMap<String, List<ConfigAttributeValue>> configAttributes;

	/**
	 * applicationName : (attributeName : attributeValue)
	 */
	private IMap<String, Map<String, String>> applicationAttributeValues;

	private ISet<String> internalNosSet;

	@PostConstruct
	public void createCache() {
		LOG.info("POST-CONSTRUCT EnvironmentPropertiesCache - STARTED");
		LOG.info("$$$$$$$$$$$$$$$Creating Environment Type Cache$$$$$$$$$$$$$$$");
		configAttributes = instance.getMap("MasterDataCache:configAttributes");
		applicationAttributeValues = instance.getMap("MasterDataCache:applicationAttributeValues");
		internalNosSet = instance.getSet("MasterDataCache:internalNosSet");
	}

	public void clearCache() {
		LOG.info("$$$$$$$$$$$$$$$Clearing Environment Type Cache$$$$$$$$$$$$$$$");
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		instance.getMap("MasterDataCache:configAttributes").clear();
		instance.getMap("MasterDataCache:applicationAttributeValues").clear();
		instance.getSet("MasterDataCache:internalNosSet").clear();
		LOG.info("Inside POSTCONSTRUCT - EnvironmentPropertiesCache OVERALL : took {} ms", watch.stop().elapsed(TimeUnit.MILLISECONDS));
	}

	public EnvironmentPropertiesCache() {

	}

	public List<ConfigAttributeValue> getConfigAttributes(String applicationName) {
		return configAttributes.get(applicationName);
	}

	public IMap<String, List<ConfigAttributeValue>> getConfigAttributeValues() {
		return configAttributes;
	}

	public IMap<String, Map<String, String>> getApplicationAttributeValues() {
		return applicationAttributeValues;
	}

	public Map<String, String> getcompanyConfigAttributes(String applicationName) {
		return applicationAttributeValues.get(applicationName);
	}

	public void setInternalNos() {
		String internalNosStr = applicationAttributeValues.get(ApplicationName.KETTLE_SERVICE.name())
				.get("internal.nos");
		if (internalNosStr != null) {
			for (String no : new HashSet<>(Arrays.asList(internalNosStr.split(",")))) {
				internalNosSet.add(no);
			}
		}
		LOG.info("$$$$$$$$$$$$$$$ setInternalNos $$$$$$$$$$$$$$$" + internalNosSet);
	}

	public Set<String> getInternalNos() {
		return internalNosSet;
	}

	public String getCharityMailIds() {
		return applicationAttributeValues.get(ApplicationName.KETTLE_SERVICE.name())
				.get("charity.notification.emailIds");
	}

	public boolean isGyftrActive() {
		String status = applicationAttributeValues.get(ApplicationName.KETTLE_SERVICE.name())
				.get("gyftr.active") ;
		return (status != null ? status.equalsIgnoreCase("true") : false);
	}

	public String getKettleClientToken() {
		return applicationAttributeValues.get(ApplicationName.KETTLE_SERVICE.name()).get("kettle.api.token");
	}

	public String getPendingAmMailIds() {
		return applicationAttributeValues.get(ApplicationName.FORMS_SERVICE.name()).get("am.pending.emails");
	}

	public String getPendingDgmMailIds() {
		return applicationAttributeValues.get(ApplicationName.FORMS_SERVICE.name()).get("dgm.pending.emails");
	}

	public String getPendingHrMailIds() {
		return applicationAttributeValues.get(ApplicationName.FORMS_SERVICE.name()).get("hr.pending.emails");
	}

	public String getWarningApprovedMailIds() {
		return applicationAttributeValues.get(ApplicationName.FORMS_SERVICE.name()).get("warning.approved.emails");
	}

	public String getWarningRejectedMailIds() {
		return applicationAttributeValues.get(ApplicationName.FORMS_SERVICE.name()).get("warning.rejected.emails");
	}

	public String getWarningCancelledMailIds() {
		return applicationAttributeValues.get(ApplicationName.FORMS_SERVICE.name()).get("warning.cancelled.emails");
	}

	@Override
	public String toString() {
		return "EnvironmentPropertiesCache{" +
				"configAttributes=" + configAttributes.size() +
				", applicationAttributeValues=" + applicationAttributeValues.size() +
				", internalNosSet=" + internalNosSet.size() +
				'}';
	}
}
