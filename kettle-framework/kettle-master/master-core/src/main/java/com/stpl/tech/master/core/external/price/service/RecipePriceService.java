/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.price.service;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by Mohit.
 */
public interface RecipePriceService {

	public Map<Integer, BigDecimal> getFinishedProductsRecipePrices(int unitId);

	public Map<Integer, BigDecimal> getSemiFinishedProductsRecipePrices(int unitId);

	public void setFinishedProductsRecipePrices(int unitId, Map<Integer, BigDecimal> finishedRecipePrices);

	public void setSemiFinishedProductsRecipePrices(int unitId, Map<Integer, BigDecimal> semiFinishedRecipePrices);

}
