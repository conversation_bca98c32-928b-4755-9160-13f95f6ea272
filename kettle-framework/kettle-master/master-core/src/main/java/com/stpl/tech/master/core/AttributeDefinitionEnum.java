/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core;

import com.stpl.tech.kettle.report.metadata.model.DataType;
import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import com.stpl.tech.master.domain.model.CafeType;
import com.stpl.tech.util.AppConstants;

public enum AttributeDefinitionEnum {

	WORKSTATION_ENABLED("Has Workstation Screens?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},1, AppConstants.NO),
	NO_OF_TABLES("No. Of Tables", DataType.INTEGER, null,2, 2),
	HAS_TABLE_SERVICE("Has table Service?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},3, AppConstants.NO),
	FREE_INTERNET_ACCESS("Has Free Internet?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},4, AppConstants.NO),
	IS_PARTNER_PRICED("Is Partner Price Unit?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},5, AppConstants.NO),
	IS_TOKEN_ENABLED("Is Token System Enabled?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},6, AppConstants.NO),
	TOKEN_LIMIT("Token Limit", DataType.INTEGER, null,7, 1),
	ELECTRICITY_METER_COUNT("No. Of Electrcity Meters", DataType.INTEGER, new Object[]{1,2,3},8, 1),
	IS_DG_AVAILABLE("Is DG Available?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},9, AppConstants.NO),
	TRUE_CALLER_ONBOARDING("True Caller Strategy", DataType.STRING, new Object[]{TrueCallerSettings.DEFAULT.name(),TrueCallerSettings.TRUE_CALLER_FIRST.name(), TrueCallerSettings.OTP_FIRST.name()}, 10,TrueCallerSettings.DEFAULT.name()),
	HOT_N_COLD_MERGED("Is Hot and Cold Station Merged?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},11, AppConstants.YES),
	LIVE_INVENTORY_ENABLED("Is live Inventory Enabled?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},12, AppConstants.NO),
	PACKAGING_TYPE("FIXED/PERCENTAGE", DataType.STRING, new Object[]{AppConstants.PERCENTAGE,AppConstants.FIXED},13, AppConstants.PERCENTAGE),
	PACKAGING_VALUE("Amount of packaging charge", DataType.BIGDECIMAL, null,14, 13),
	TABLE_SERVICE_TYPE("Table Service Type?", DataType.INTEGER, new Object[]{0,1,2},15, 1),
	GOOGLE_MERCHANT_ID("Google Merchant Id", DataType.STRING, new Object[]{},16,null ),
	REVENUE_CERTIFICATE_EMAIL("E-mail for sending revenue certificate", DataType.STRING, new Object[]{},17, "<EMAIL>"),
	REVENUE_CERTIFICATE_GENERATION_ENABLE("Revenue Certificate to be generated?", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},18, AppConstants.NO),
	REAL_TIME_SALE_PUSH("Real Time Sale Push", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},19, AppConstants.NO),
	PARTNER_TO_PUSH_SALE("Real Time Sale Push Partner", DataType.STRING, new Object[]{},20, null),
	LOYALTY_REDEMPTION_ALLOWED("Is loyalty redemption allowed", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},21, AppConstants.YES),
	AUTO_EDC_PAYMENT("Is auto edc payments allowed", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},22, AppConstants.YES),
	VARIANCE_ACK_EMP_ID("Employee Ids Acknowledging Variance", DataType.STRING, new Object[]{},23, ""),
	LOYALTY_BURN_SWIGGY_ALLOWED("Is loyalty burn Swiggy allowed", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},25, AppConstants.YES),
	MONK_PRICE_PROFILE("Current monk recipe profile mapped to unit", DataType.STRING, null,26, null),
	NO_OF_MONK_NEEDED("Total number of monks needed for this unit", DataType.INTEGER, null,27, null),
	MILK_TRACKING("Is Milk tracking enabled for this unit", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},28, AppConstants.NO),
	SHOW_LOYALTY_SCREEN("Is allowed to show loyalty screen", DataType.STRING, new Object[]{AppConstants.NO,AppConstants.YES},29, AppConstants.YES),
	OTP_VIA_EMAIL("Is Email allowed to send otp",DataType.STRING,new Object[]{AppConstants.NO,AppConstants.YES},30,AppConstants.NO),
	PRICING_CATEGORY("Pricing Category for this unit",DataType.STRING,null,31,null),
	SUPERU_ENABLED("Is SuperU enable for this Unit",DataType.STRING,new Object[]{AppConstants.NO,AppConstants.YES},32,AppConstants.NO),
	CHECK_BIG_PAN_THRESHOLD("Show big pan error for Recipe String > 500 ",DataType.STRING,null,33,AppConstants.NO),
	AUTO_TOKEN_ENABLED("Is auto token enabled for this unit",DataType.STRING,new Object[]{AppConstants.NO,AppConstants.YES},34,AppConstants.NO),
	SERVICE_CHARGE_VALUE("Percentage of service charge", DataType.BIGDECIMAL, null,36, 0),
	SERVICE_CHARGE_POS_ENABLED("Is Service charge enabled for pos in this unit",DataType.STRING,new Object[]{AppConstants.NO, AppConstants.YES},37, AppConstants.NO),
	SERVICE_CHARGE_APP_ENABLED("Is Service charge enabled for app in this unit",DataType.STRING,new Object[]{AppConstants.NO, AppConstants.YES},38, AppConstants.NO),
	CAFE_SERVICE_TYPE("Determines available services based on cafe type",DataType.STRING , new Object[] {CafeType.values()},39 , CafeType.CAFE),
	IS_TESTING_UNIT("Is this unit is Testing Unit", DataType.STRING, new Object[]{AppConstants.NO, AppConstants.YES}, 40, AppConstants.NO),
	CUSTOM_ADDONS_LIMIT("Custom Addons Limit", DataType.INTEGER, null, 41, 3),
	FEEDBACK_FORM_POS_ENABLED("Is Feedback form enabled for pos in this unit",DataType.STRING,new Object[]{AppConstants.NO, AppConstants.YES},42, AppConstants.NO),
	DREAM_FOLKS_OUTLET_ID("Customer offers ", DataType.STRING, new Object[]{},43, "");

	private final String desc;
	private final DataType type;
	private final Object[] values;
	private Integer id;
	private final Object defaultValue;

	AttributeDefinitionEnum(String desc, DataType type, Object[] values, Integer id, Object defaultValue) {
		this.id= id;
		this.desc = desc;
		this.type = type;
		this.values = values;
		this.defaultValue = defaultValue;
	}

	public Object[] getValues() {
		return values;
	}

	public String getDesc() {
		return desc;
	}

	public DataType getType() {
		return type;
	}

	public Object getDefaultValue() {
		return defaultValue;
	}

	public Integer getId(){
		return id;
	}
}
