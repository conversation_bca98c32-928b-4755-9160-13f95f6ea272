package com.stpl.tech.master.util;

import com.stpl.tech.master.data.model.BulkUploadType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Slf4j
@Converter(autoApply = true)
public class BulkUploadTypeConverter implements AttributeConverter<BulkUploadType, String> {

    @Override
    public String convertToDatabaseColumn(BulkUploadType type) {
        if(type == null) {
            log.info("BulkUploadType found null while convertToDatabaseColumn");
            return null;
        }
        return type.getShortName();
    }

    @Override
    public BulkUploadType convertToEntityAttribute(String dbData) {
        if(StringUtils.isBlank(dbData)) {
            log.info("String found null or empty while convertToEntityAttribute");
            return null;
        }
        return BulkUploadType.fromShortName(dbData);
    }

}
