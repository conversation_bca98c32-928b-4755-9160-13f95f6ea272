package com.stpl.tech.master.core.service.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.recipe.model.RecipeDetail;

public class RecipeData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2529088956151193013L;
	private List<Integer> productIds;
	private Map<Integer, Map<String, RecipeDetail>> recipes = new HashMap<Integer, Map<String, RecipeDetail>>();;

	public Map<Integer, Map<String, RecipeDetail>> getRecipes() {
		return recipes;
	}

	public void setRecipes(Map<Integer, Map<String, RecipeDetail>> recipes) {
		this.recipes = recipes;
	}

	public List<Integer> getProductIds() {
		if (productIds == null) {
			productIds = new ArrayList<Integer>();
		}
		return productIds;
	}

	public void setProductIds(List<Integer> productIds) {
		this.productIds = productIds;
	}

}
