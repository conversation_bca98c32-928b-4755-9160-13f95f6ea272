package com.stpl.tech.master.data.model;

import com.stpl.tech.master.util.BulkUploadTypeConverter;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "BULK_UPLOAD_DATA")
@EntityListeners(AuditingEntityListener.class)
public class BulkUploadData {

    @Id
    @Column(name = "BULK_UPLOAD_ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer bulkUploadId;

    @Column(name = "RELATED_ID")
    private String relatedId;

    @Column(name = "DOCUMENT_ID")
    private Integer documentId;

    @Column(name = "UPLOADED_TYPE")
    @Convert(converter = BulkUploadTypeConverter.class)
    private BulkUploadType uploadedType;

    @Column(name = "TOTAL_ENTRIES_IN_FILE")
    private Integer totalEntriesInFile;

    @Column(name = "TOTAL_ENTRIES_UPDATED_SUCCESSFULLY")
    private Integer totalEntriesUpdatedSuccessFully;

    @Column(name = "TOTAL_ENTRIES_FAILED")
    private Integer totalEntriesFailedToUpdate;

    @Column(name = "STATUS")
    @Enumerated(EnumType.STRING)
    private BulkUploadStatus uploadStatus;

    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @OneToMany(mappedBy = "bulkUploadData")
    List<BulkUploadDataLog> bulkUploadDataLogList = new ArrayList<>();

}
