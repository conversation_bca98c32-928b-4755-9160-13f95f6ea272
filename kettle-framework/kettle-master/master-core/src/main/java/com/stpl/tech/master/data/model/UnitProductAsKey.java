package com.stpl.tech.master.data.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Objects;

@Data
@NoArgsConstructor
public class UnitProductAsKey implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer unitId;
    private Integer productId;

    public UnitProductAsKey(Integer unitId, Integer productId) {
        Assert.notNull(unitId, "Unit ID cannot be null");
        Assert.notNull(productId, "Product ID cannot be null");
        this.unitId = unitId;
        this.productId = productId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnitProductAsKey that = (UnitProductAsKey) o;
        return Objects.equals(unitId, that.unitId) && 
               Objects.equals(productId, that.productId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(unitId, productId);
    }
} 