/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * RefLookupType generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "REF_LOOKUP_TYPE", uniqueConstraints = @UniqueConstraint(columnNames = { "RTL_GROUP", "RTL_CODE" }))
public class RefLookupType implements java.io.Serializable {

	private Integer rtlId;
	private String rtlGroup;
	private String rtlCode;
	private String rtlName;
	private String status;
	private List<RefLookup> refLookups = new ArrayList<RefLookup>(0);

	public RefLookupType() {
	}

	public RefLookupType(String rtlGroup, String rtlCode, String rtlName) {
		this.rtlGroup = rtlGroup;
		this.rtlCode = rtlCode;
		this.rtlName = rtlName;
	}

	public RefLookupType(String rtlGroup, String rtlCode, String rtlName, List<RefLookup> refLookups) {
		this.rtlGroup = rtlGroup;
		this.rtlCode = rtlCode;
		this.rtlName = rtlName;
		this.refLookups = refLookups;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "RTL_ID", unique = true, nullable = false)
	public Integer getRtlId() {
		return this.rtlId;
	}

	public void setRtlId(Integer rtlId) {
		this.rtlId = rtlId;
	}

	@Column(name = "RTL_GROUP", nullable = false, length = 15)
	public String getRtlGroup() {
		return this.rtlGroup;
	}

	public void setRtlGroup(String rtlGroup) {
		this.rtlGroup = rtlGroup;
	}

	@Column(name = "RTL_CODE", nullable = false, length = 20)
	public String getRtlCode() {
		return this.rtlCode;
	}

	public void setRtlCode(String rtlCode) {
		this.rtlCode = rtlCode;
	}

	@Column(name = "RTL_NAME", nullable = false, length = 30)
	public String getRtlName() {
		return this.rtlName;
	}

	public void setRtlName(String rtlName) {
		this.rtlName = rtlName;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "refLookupType")
	@OrderBy("rlId asc")
	public List<RefLookup> getRefLookups() {
		return this.refLookups;
	}

	public void setRefLookups(List<RefLookup> refLookups) {
		this.refLookups = refLookups;
	}

	@Column(name = "STATUS", nullable = false, length = 10)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
