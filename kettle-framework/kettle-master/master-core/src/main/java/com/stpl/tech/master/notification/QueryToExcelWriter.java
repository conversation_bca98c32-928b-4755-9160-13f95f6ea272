/**
 *
 */
package com.stpl.tech.master.notification;

import java.util.List;

import com.stpl.tech.master.util.MasterUtil;
import org.subtlelib.poi.api.row.RowContext;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.stpl.tech.util.notification.ReportDetailData;

/**
 * <AUTHOR>
 *
 */
public class QueryToExcelWriter {

	private final WorkbookContext workbookCtx;

	public QueryToExcelWriter(WorkbookContext workbookCtx) {
		super();
		this.workbookCtx = workbookCtx;
	}

	public void writeToSheet(ReportDetailData reportDetailData, boolean skipHeader) {
		SheetContext sheetCtx = workbookCtx.createSheet(reportDetailData.getName());
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);

		if (!skipHeader) {
			sheetCtx.nextRow().mergeCells(10).text(reportDetailData.getName());
		}
		int count = 0;

		for (List<String> list : reportDetailData.getContent()) {
			RowContext row = sheetCtx.nextRow();
			if (count == 0) {
				for (int i = 0; i < list.size(); i++) {
					if (reportDetailData.getSkipColumns().contains(i)) {
						continue;
					}
					row.setTextStyle(headerStyle).text(list.get(i));
				}
			} else {
				for (int i = 0; i < list.size(); i++) {
					if (reportDetailData.getSkipColumns().contains(i)) {
						continue;
					}
					row.text(list.get(i) == null ? "" : list.get(i));
				}
			}
			count++;
		}
	}

	public void writeToSheet(String sheetName, List<ReportDetailData> reportDetailDatas, boolean skipHeader) {
		SheetContext sheetCtx = workbookCtx.createSheet(sheetName);
		sheetCtx.nextRow();
		for (ReportDetailData reportDetailData : reportDetailDatas) {
			writeRows(sheetCtx, reportDetailData, skipHeader);
			sheetCtx.nextRow();
			sheetCtx.nextRow();
		}
	}

	public void writeRows(SheetContext sheetCtx, ReportDetailData reportDetailData, boolean skipHeader) {
		if (!skipHeader) {
			sheetCtx.nextRow().mergeCells(10).text(reportDetailData.getName());
		}
		int count = 0;

		for (List<String> list : reportDetailData.getContent()) {
			RowContext row = sheetCtx.nextRow();
			if (count == 0) {
				for (int i = 0; i < list.size(); i++) {
					if (reportDetailData.getSkipColumns().contains(i)) {
						continue;
					}
					row.text(list.get(i));
				}
			} else {
				for (int i = 0; i < list.size(); i++) {
					if (reportDetailData.getSkipColumns().contains(i)) {
						continue;
					}
					row.text(list.get(i) == null ? "" : list.get(i));
				}
			}
			count++;
		}
	}
}
