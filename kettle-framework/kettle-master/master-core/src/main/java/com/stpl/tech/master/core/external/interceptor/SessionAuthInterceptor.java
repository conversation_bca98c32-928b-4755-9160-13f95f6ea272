/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.interceptor;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.domain.RequestHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.acl.service.ACLService;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import com.stpl.tech.master.core.external.cache.SessionCache;
import com.stpl.tech.util.ACLUtil;

@Component
public class SessionAuthInterceptor implements AsyncHandlerInterceptor {

	private static final Logger LOG = LoggerFactory.getLogger(SessionAuthInterceptor.class);

	@Autowired
	private Environment env;

	@Autowired
	private SessionCache sessionCache;

	@Autowired
	private ExternalAPITokenCache externalTokenCache;

	@Autowired
	private TokenService<JWTToken> jwtService;

	@Autowired
	private ACLService aclService;

	private static Random random;

	static {
		try {
			random = SecureRandom.getInstanceStrong();
		} catch (NoSuchAlgorithmException e) {
			LOG.error("Error generating random value: ", e);
		}
	}

	@Scheduled(fixedRate = 7200000)
	public void clearAclURICache() {
		ACLUtil.getInstance().clearUriCache("SessionAuthInterceptor");
	}

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws AuthenticationFailureException {
		addRequestId(request);
		boolean validate = Boolean.parseBoolean(env.getProperty("run.validate.filter", "false"));
		String module = ACLUtil.getInstance().convertURIToModule(request.getRequestURI());
		if (!validate || aclService.isPreAuthenticated(module)) {
			return true;
		}
		String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
		String authInternalHeader = null;
		String accessKey = null;
		if (request.getHeader("auth-internal") != null) {
			authInternalHeader = request.getHeader("auth-internal").trim();
		} else if (request.getHeader("Authorisation") != null) {
			authInternalHeader = request.getHeader("auth-internal").trim();
		} else if (request.getParameter("accessKey") != null) {
			accessKey = request.getParameter("accessKey").trim();
		}

		if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
			JWTToken jwtToken = new JWTToken();
			jwtService.parseToken(jwtToken, authHeader);
			String sessionKey = jwtToken.getSessionKey();
			int terminalId = jwtToken.getTerminalId();
			int userId = jwtToken.getUserId();
			int unitId = jwtToken.getUnitId();

			addUserAndUnitToRequestContext(userId, unitId);

			try {
				validateSession(unitId, userId, sessionKey);
				return true;
			} catch (AuthenticationFailureException e) {
				String message = String.format(
						"Session expired: Unit Id %d , Terminal Id %d, User Id %d, Session key Id %s", unitId,
						terminalId, userId, sessionKey);
				LOG.info(message);
				throw new AuthenticationFailureException(message, e);
			}
		} else if (authInternalHeader != null && !authInternalHeader.equals("null") && !authInternalHeader.equals("")) {
			if (externalTokenCache.isValidKey(authInternalHeader) && externalTokenCache.checkAccess(authInternalHeader,
					request.getRequestURI(), request.getMethod())) {
				return true;
			} else {
				throw new AuthenticationFailureException(
						String.format("Invalid tokens in header : %s", authInternalHeader));
			}
		} else if (accessKey != null && !accessKey.equals("null") && !accessKey.equals("")) {
			if (externalTokenCache.isValidKey(accessKey)
					&& externalTokenCache.checkAccess(accessKey, request.getRequestURI(), request.getMethod())) {
				return true;
			} else {
				throw new AuthenticationFailureException(String.format("Invalid tokens in header : %s", accessKey));
			}
		} else {
			LOG.info("Session expired: Invalid tokens in header");
			throw new AuthenticationFailureException("Session expired: Invalid tokens in header");
		}
	}

	// add unitId and userId to request context
	private void addUserAndUnitToRequestContext(int userId, int unitId) {
		RequestHolder requestHolder = RequestContext.getContext();
		if(userId > 0) {
			requestHolder.setLoggedInUserId(userId);
		}
		if(unitId > 0) {
			requestHolder.setUnitId(unitId);
		}
		RequestContext.setContext(requestHolder);
	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		MDC.clear();
	}

	private void validateSession(int unitId, int userId, String sessionKey) throws AuthenticationFailureException {
		boolean isValidated = sessionCache.validateSession(sessionKey, unitId, userId);
		if (!isValidated) {
			throw new AuthenticationFailureException(
					String.format("Not able to validate the session for the userId %d", userId));
		}
	}

	private void addRequestId(HttpServletRequest request) {
		String[] appName = request.getContextPath().replace("/", "").split("-");
		StringBuilder key = new StringBuilder();
		Arrays.stream(appName).forEach(s -> key.append(s.length() > 4 ? s.substring(0, 4) : s).append("-"));
		byte[] array = new byte[7]; // length is bounded by 7
		random.nextBytes(array);
		key.append(getRandomString());
		MDC.put("request.id", key.toString());
	}

	private String getRandomString() {
		int leftLimit = 48; // numeral '0'
		int rightLimit = 122; // letter 'z'
		int targetStringLength = 10;
		return random.ints(leftLimit, rightLimit + 1).filter(i -> (i <= 57 || i >= 65) && (i <= 90 || i >= 97))
				.limit(targetStringLength)
				.collect(StringBuilder::new, StringBuilder::appendCodePoint, StringBuilder::append).toString();
	}

}
