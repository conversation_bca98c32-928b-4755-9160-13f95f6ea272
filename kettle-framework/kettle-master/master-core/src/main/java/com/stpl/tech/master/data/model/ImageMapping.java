package com.stpl.tech.master.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@MappedSuperclass
@Getter
@Setter
public class ImageMapping implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PRODUCT_IMAGE_MAPPING_ID", unique = true, nullable = false)
    private Integer productDimensionImgMappingId;
    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;
    @Column(name = "IMAGE_TYPE", nullable = false)
    private String imageType;
    @Column(name = "IMAGE_URL", nullable = false)
    private String imageUrl;
    @Column(name = "LAST_UPDATED_BY", nullable = false, length = 50)
    private Integer lastUpdatedBy;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATION_TIME", nullable = false, length = 19)
    private Date lastUpdationTime;
    @Column(name = "STATUS", nullable = false, length = 50)
    private String status;
    @Column(name = "PRODUCT_INDEX")
    private Integer index;
}
