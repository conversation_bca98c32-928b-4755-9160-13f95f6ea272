/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

// Generated 24 Jul, 2015 1:25:49 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ProductDetail generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PRODUCT_DETAIL")
public class ProductDetail implements java.io.Serializable {

	private Integer productId;
	private RefLookupType dimensionCode;
	private RefLookupType addonTypes;
	private RefLookup productSubType;
	private RefLookup stationCategory;
	private RefLookupType productType;
	private RefLookup webType;
	private String productName;
	private String productDescription;
	private String productStatus;
	private String classification;
	private String shortCode;
	private String isInventoryTracked;
	private String employeeMealComponent;
	private String attribute;
	private Date productStartDate;
	private Date productEndDate;
	private String productSkuCode;
	private String priceType;
	private Date inTmstmp;
	private Date outTmstmp;
	private String taxCode;
	private String preparationMode;
	private String supportsVariantLevelOrdering;
	private Integer brandId;

	private String taxableCogs;
	private Set<ProductRelationship> productRelationshipsForConstituentProductId = new HashSet<>(0);
	private Set<ProductRelationship> productRelationshipsForProductId = new HashSet<>(0);
	private BigDecimal prepTime;

	private Integer updatedBy;

	private Date lastUpdateTime;

	private String inventoryTrackedLevel;

	private List<ProductTagMappingDetail> productTagMapping;

	private String serviceChargeApplicable;

	public ProductDetail() {
	}

	public ProductDetail(RefLookupType dimensionCode, RefLookup productSubType, RefLookupType productType,
			String productName, String productDescription, String productStatus, Date productStartDate,
			Date productEndDate, String productSkuCode, String priceType, Date inTmstmp, Date outTmstmp) {
		this.dimensionCode = dimensionCode;
		this.productSubType = productSubType;
		this.productType = productType;
		this.productName = productName;
		this.productDescription = productDescription;
		this.productStatus = productStatus;
		this.productStartDate = productStartDate;
		this.productEndDate = productEndDate;
		this.productSkuCode = productSkuCode;
		this.priceType = priceType;
		this.inTmstmp = inTmstmp;
		this.outTmstmp = outTmstmp;
	}

	public ProductDetail(RefLookupType dimensionCode, RefLookupType addonTypes, RefLookup productSubType,
			RefLookupType productType, String productName, String productDescription, String productStatus,
			String attribute, Date productStartDate, Date productEndDate, String productSkuCode, String priceType,
			Date inTmstmp, Date outTmstmp, Set<ProductRelationship> productRelationshipsForConstituentProductId,
			Set<ProductRelationship> productRelationshipsForProductId) {
		this.dimensionCode = dimensionCode;
		this.addonTypes = addonTypes;
		this.productSubType = productSubType;
		this.productType = productType;
		this.productName = productName;
		this.productDescription = productDescription;
		this.productStatus = productStatus;
		this.attribute = attribute;
		this.productStartDate = productStartDate;
		this.productEndDate = productEndDate;
		this.productSkuCode = productSkuCode;
		this.priceType = priceType;
		this.inTmstmp = inTmstmp;
		this.outTmstmp = outTmstmp;
		this.productRelationshipsForConstituentProductId = productRelationshipsForConstituentProductId;
		this.productRelationshipsForProductId = productRelationshipsForProductId;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PRODUCT_ID", unique = true, nullable = false)
	public Integer getProductId() {
		return this.productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DIMENSION_CODE", nullable = false)
	public RefLookupType getDimensionCode() {
		return this.dimensionCode;
	}

	public void setDimensionCode(RefLookupType dimensionCode) {
		this.dimensionCode = dimensionCode;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ADDITIONAL_ITEM_TYPES")
	public RefLookupType getAddonTypes() {
		return this.addonTypes;
	}

	public void setAddonTypes(RefLookupType addonTypes) {
		this.addonTypes = addonTypes;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_TYPE", nullable = false)
	public RefLookupType getProductType() {
		return this.productType;
	}

	public void setProductType(RefLookupType productType) {
		this.productType = productType;
	}

	@Column(name = "PRODUCT_NAME", nullable = false)
	public String getProductName() {
		return this.productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Column(name = "PRODUCT_CLASSIFICATION", nullable = false)
	public String getClassification() {
		return classification;
	}

	public void setClassification(String classification) {
		this.classification = classification;
	}

	@Column(name = "SHORT_CODE", nullable = false, length = 6)
	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	@Column(name = "PRODUCT_DESCRIPTION", nullable = false, length = 5000)
	public String getProductDescription() {
		return this.productDescription;
	}

	public void setProductDescription(String productDescription) {
		this.productDescription = productDescription;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_SUB_TYPE", nullable = false)
	public RefLookup getProductSubType() {
		return this.productSubType;
	}

	public void setProductSubType(RefLookup productSubType) {
		this.productSubType = productSubType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "STATION_CATEGORY", nullable = true)
	public RefLookup getStationCategory() {
		return stationCategory;
	}

	public void setStationCategory(RefLookup stationCategory) {
		this.stationCategory = stationCategory;
	}

	@Column(name = "PRODUCT_STATUS", nullable = false, length = 20)
	public String getProductStatus() {
		return this.productStatus;
	}

	public void setProductStatus(String productStatus) {
		this.productStatus = productStatus;
	}

	@Column(name = "IS_INVENTORY_TRACKED", nullable = false, length = 1)
	public String getIsInventoryTracked() {
		return isInventoryTracked;
	}

	public void setIsInventoryTracked(String isInventoryTracked) {
		this.isInventoryTracked = isInventoryTracked;
	}

	@Column(name = "EMPLOYEE_MEAL_COMPONENT", nullable = false, length = 1)
	public String getEmployeeMealComponent() {
		return employeeMealComponent;
	}

	public void setEmployeeMealComponent(String employeeMealComponent) {
		this.employeeMealComponent = employeeMealComponent;
	}

	@Column(name = "ATTRIBUTE", length = 20)
	public String getAttribute() {
		return this.attribute;
	}

	public void setAttribute(String attribute) {
		this.attribute = attribute;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "PRODUCT_START_DATE", nullable = false, length = 10)
	public Date getProductStartDate() {
		return this.productStartDate;
	}

	public void setProductStartDate(Date productStartDate) {
		this.productStartDate = productStartDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "PRODUCT_END_DATE", nullable = false, length = 10)
	public Date getProductEndDate() {
		return this.productEndDate;
	}

	public void setProductEndDate(Date productEndDate) {
		this.productEndDate = productEndDate;
	}

	@Column(name = "PRODUCT_SKU_CODE", nullable = false, length = 30)
	public String getProductSkuCode() {
		return this.productSkuCode;
	}

	public void setProductSkuCode(String productSkuCode) {
		this.productSkuCode = productSkuCode;
	}

	@Column(name = "PRICE_TYPE", nullable = false, length = 10)
	public String getPriceType() {
		return this.priceType;
	}

	public void setPriceType(String priceType) {
		this.priceType = priceType;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "IN_TMSTMP", nullable = false, length = 19)
	public Date getInTmstmp() {
		return this.inTmstmp;
	}

	public void setInTmstmp(Date inTmstmp) {
		this.inTmstmp = inTmstmp;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "OUT_TMSTMP", nullable = false, length = 19)
	public Date getOutTmstmp() {
		return this.outTmstmp;
	}

	public void setOutTmstmp(Date outTmstmp) {
		this.outTmstmp = outTmstmp;
	}

	@Column(name = "SUPPORTS_VARIANT_LEVEL_ORDERING")
	public String getSupportsVariantLevelOrdering() {
		return supportsVariantLevelOrdering;
	}

	public void setSupportsVariantLevelOrdering(String supportsVariantLevelOrdering) {
		this.supportsVariantLevelOrdering = supportsVariantLevelOrdering;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "productDetailByConstituentProductId")
	public Set<ProductRelationship> getProductRelationshipsForConstituentProductId() {
		return this.productRelationshipsForConstituentProductId;
	}

	public void setProductRelationshipsForConstituentProductId(
			Set<ProductRelationship> productRelationshipsForConstituentProductId) {
		this.productRelationshipsForConstituentProductId = productRelationshipsForConstituentProductId;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "productDetailByProductId")
	public Set<ProductRelationship> getProductRelationshipsForProductId() {
		return this.productRelationshipsForProductId;
	}

	public void setProductRelationshipsForProductId(Set<ProductRelationship> productRelationshipsForProductId) {
		this.productRelationshipsForProductId = productRelationshipsForProductId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "WEB_CATEGORY_TYPE", nullable = true)
	public RefLookup getWebType() {
		return this.webType;
	}

	public void setWebType(RefLookup webType) {
		this.webType = webType;
	}

	@Column(name = "TAX_CODE", nullable = true, length = 20)
	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@Column(name = "PREPARATION_MODE", nullable = true, length = 15)
	public String getPreparationMode() {
		return preparationMode;
	}

	public void setPreparationMode(String preparationMode) {
		this.preparationMode = preparationMode;
	}


	@Column(name = "TAXABLE_COGS")
	public String getTaxableCogs() {
		return taxableCogs;
	}

	public void setTaxableCogs(String taxableCogs) {
		this.taxableCogs = taxableCogs;
	}

	@Column(name = "PREP_TIME")
	public BigDecimal getPrepTime() {
		return prepTime;
	}

	public void setPrepTime(BigDecimal prepTime) {
		this.prepTime = prepTime;
	}

	@Column(name = "BRAND_ID")
	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	@Column(name = "UPDATED_BY")
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "LAST_UPDATE_TIME")
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "INVENTORY_TRACK_LEVEL")
	public String getInventoryTrackedLevel() {
		return inventoryTrackedLevel;
	}

	public void setInventoryTrackedLevel(String inventoryTrackedLevel) {
		this.inventoryTrackedLevel = inventoryTrackedLevel;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "productId")
	public List<ProductTagMappingDetail> getProductTagMapping() {
		return productTagMapping;
	}

	public void setProductTagMapping(List<ProductTagMappingDetail> productTagMapping) {
		this.productTagMapping = productTagMapping;
	}

	@Column(name = "SERVICE_CHARGE_APPLICABLE")
	public String getServiceChargeApplicable() {
		return serviceChargeApplicable;
	}


	public void setServiceChargeApplicable(String serviceChargeApplicable) {
		this.serviceChargeApplicable = serviceChargeApplicable;
	}
}
