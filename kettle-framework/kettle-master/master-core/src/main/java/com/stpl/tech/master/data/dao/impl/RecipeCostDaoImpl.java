/**
 * 
 */
package com.stpl.tech.master.data.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.data.dao.RecipeCostDao;
import com.stpl.tech.master.data.model.ProductRecipeCost;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.recipe.model.RecipeCost;
import com.stpl.tech.master.recipe.model.RecipeCostData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppUtils;

@Repository
public class RecipeCostDaoImpl extends AbstractMasterDaoImpl implements RecipeCostDao {

	@Autowired
	private RecipeCache cache;

	@Override
	public RecipeCost saveOrUpdateCost(RecipeCost cost) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean saveOrUpdateCostData(List<RecipeCostData> list) {

		Map<Integer, RecipeDetail> recipeMap = new HashMap<>();
		for (RecipeDetail r : cache.getRecipes().values()) {
			recipeMap.put(r.getRecipeId(), r);
		}
		for (RecipeDetail r : cache.getScmRecipes().values()) {
			recipeMap.put(r.getRecipeId(), r);
		}
		for (RecipeCostData costData : list) {
			ProductRecipeCost cost = getCostData(costData);
			if (cost == null) {
				cost = new ProductRecipeCost();
			}
			setValues(cost, costData, recipeMap.get(costData.getRecipeId()));
			manager.merge(cost);
		}
		manager.flush();
		return true;
	}

	private ProductRecipeCost getCostData(RecipeCostData costData) {
		try {
			Query query = manager
					.createQuery("FROM ProductRecipeCost where recipeId= :recipeId and costType=:costType");
			query.setParameter("recipeId", costData.getRecipeId());
			query.setParameter("costType", costData.getCategory().name());
			return (ProductRecipeCost) query.getSingleResult();
		} catch (NoResultException e) {
			return null;
		}
	}

	private void setValues(ProductRecipeCost cost, RecipeCostData costData, RecipeDetail recipeDetail) {
		cost.setRecipeId(costData.getRecipeId());
		cost.setRecipeName(costData.getRecipeName());
		cost.setCostType(costData.getCategory().name());
		cost.setCost(costData.getCost());
		cost.setEventEntryId(costData.getEventEntryId());
		cost.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
		cost.setUpdatedBy(costData.getUpdatedBy());
		cost.setUpdatedById(costData.getUpdatedById());
		if (recipeDetail.getProduct().getClassification() != null) {
			cost.setProductSource(recipeDetail.getProduct().getClassification().name());

		} else {
			cost.setProductSource(ProductClassification.MENU.name());
		}
		if (recipeDetail != null) {
			cost.setProductId(recipeDetail.getProduct().getProductId());
			cost.setDimension(recipeDetail.getDimension().getCode());
		}
	}

}
