/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.notification;

import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class GenericReportEmail extends EmailNotification {

	private GenericNotificationTemplate notification;
	private EnvType envType;

	public GenericReportEmail(GenericNotificationTemplate notification, EnvType envType) {
		super();
		this.notification = notification;
		this.envType = envType;
	}

	@Override
	public String[] getToEmails() {
		if(this.notification.getMailData().getForceEmail()) {
			return notification.getMailData().getToEmails();
		}
		return AppUtils.isDev(envType) ? new String[] { "<EMAIL>" }
				: notification.getMailData().getToEmails();
	}

	@Override
	public String getFromEmail() {
		return notification.getMailData().getFromEmail();
	}

	@Override
	public String subject() {
		return AppUtils.isDev(envType) ? envType + " " + notification.getMailData().getSubject()
				: notification.getMailData().getSubject();
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return notification.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}

}
