package com.stpl.tech.master.core.config.attribute.dao;

import java.util.List;

import com.stpl.tech.master.data.dao.AbstractMasterDao;
import com.stpl.tech.master.data.model.ConfigAttributeValueData;

public interface ConfigAttributeDao extends AbstractMasterDao {
	public List<ConfigAttributeValueData> getCompanyAttributes(String applicationName);

	public List<ConfigAttributeValueData> getAllAttributeValues();
}
