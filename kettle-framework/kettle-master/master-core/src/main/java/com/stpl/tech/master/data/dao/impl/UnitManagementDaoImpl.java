/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.core.AttributeDefinitionEnum;
import com.stpl.tech.master.core.KettleServiceEndpoints;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.UnitClosureStateEnum;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.PriceProfileManagementService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.UnitManagementDao;
import com.stpl.tech.master.data.model.AddressInfo;
import com.stpl.tech.master.data.model.AppOfferMappingData;
import com.stpl.tech.master.data.model.ApplicationVersionDetail;
import com.stpl.tech.master.data.model.ApplicationVersionDetailData;
import com.stpl.tech.master.data.model.ApplicationVersionEvent;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.BusinessHours;
import com.stpl.tech.master.data.model.CompanyDetail;
import com.stpl.tech.master.data.model.Denomination;
import com.stpl.tech.master.data.model.EmployeeDetail;
import com.stpl.tech.master.data.model.EventStatusType;
import com.stpl.tech.master.data.model.LocationDetail;
import com.stpl.tech.master.data.model.MonkAttrMetaData;
import com.stpl.tech.master.data.model.MonkConfigurationData;
import com.stpl.tech.master.data.model.PaymentMode;
import com.stpl.tech.master.data.model.UnitAttributeMapping;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadata;
import com.stpl.tech.master.data.model.UnitPaymentModeMapping;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.data.model.UnitProductPricing;
import com.stpl.tech.master.data.model.UnitProductSyncLog;
import com.stpl.tech.master.data.model.UnitTaxMapping;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.data.repository.UnitToPartnerEdcMappingDao;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationVersionDetailDomainData;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.master.domain.model.UnitPaymentModeMappingDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.domain.model.UnitToPartnerEdcMappingDetail;
import com.stpl.tech.master.monk.configuration.model.MonkAttr;
import com.stpl.tech.master.monk.configuration.model.MonkConfiguration;
import com.stpl.tech.master.monk.configuration.model.MonkConfigurationValue;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.sql.Time;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Repository
public class UnitManagementDaoImpl extends AbstractMasterDaoImpl implements UnitManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(UnitManagementDaoImpl.class);

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private MasterProperties props;
    @Autowired
    private UnitToPartnerEdcMappingDao unitToPartnerEdcMappingDao;

    @Autowired
    private PriceProfileManagementService priceProfileManagementService;

    public Unit addUnit(Unit unit) throws DataUpdationException {
        if (unitExist(unit.getName())) {
            throw new DataUpdationException("Cannot Create a unit with name " + unit.getName()
                + " as another unit with same name already exists");
        }
        AddressInfo addressDetail = MasterDataConverter.createAddress(unit.getAddress());
        manager.persist(addressDetail);
        manager.flush();
        LOG.info("Unit Address Added");
        UnitDetail detail = new UnitDetail(getBusinessDivision(unit.getDivision().getId()), addressDetail,
            unit.getName(), unit.getRegion(), unit.getUnitEmail(), unit.getFamily().name(),
            unit.getStartDate(), unit.getStatus().name(), unit.getTin(), unit.getNoOfTerminals(),
            unit.getNoOfTakeawayTerminals(), unit.getSubCategory().name(), unit.getShortName(),unit.getVarianceAcknowledgementRequired(),UnitClosureStateEnum.NO.name());
        if(unit.getFssai() != null && !unit.getFssai().isEmpty()){
            detail.setFssai(unit.getFssai());
        }
        detail.setShortCode(unit.getShortCode());
        detail.setShortName(unit.getShortName());
        detail.setReferenceName(unit.getReferenceName());
        detail.setCommunicationChannel(unit.getChannel());
        detail.setCreditAccountId(unit.getCreditAccount());
        detail.setBusinessType(unit.getUnitBusinessType().value());
        detail.setIsLive(AppConstants.getValue(unit.isLive()));
        detail.setCafeNeoStatus(AppConstants.IN_ACTIVE);
        detail.setCafeAppStatus(AppConstants.IN_ACTIVE);
        detail.setHandoverDate(unit.getHandoverDate());
        if (unit.getCafeManager() != null) {
            detail.setCafeManager(unit.getCafeManager().getId());
        }
        if (unit.getCompany() != null) {
            detail.setCompanyDetail(getCompanyDetail(unit.getCompany().getId()));
        } else {
            detail.setCompanyDetail(getCompanyDetail(detail.getBusinessDivision().getCompanyDetail().getCompanyId()));
        }
        if (unit.getLocation() != null) {
            detail.setLocation(manager.find(LocationDetail.class, unit.getLocation().getId()));
        }
        if (unit.getUnitManager() != null) {
            detail.setUnitManager(manager.find(EmployeeDetail.class, unit.getUnitManager().getId()));
        }
        if (unit.getSalesClonedFrom() != null) {
            detail.setSalesClonedFrom(unit.getSalesClonedFrom());
        }
        if (unit.getProbableOpeningDate() != null) {
            detail.setProbableOpeningDate(unit.getProbableOpeningDate());
        }
        detail.setPricingProfile(unit.getPricingProfile());
        if(Objects.nonNull(unit.getUnitZone())){
            detail.setUnitZone(unit.getUnitZone());
        }
        if(Objects.nonNull(unit.getPosVersion())){
            detail.setPosVersion(unit.getPosVersion());
        }
        if(Objects.nonNull(unit.getFaDaycloseEnabled())){
            detail.setFaDaycloseEnabled(unit.getFaDaycloseEnabled());
        }
        if(Objects.nonNull(unit.getUnitCafeManager())){
            detail.setUnitCafeManager(unit.getUnitCafeManager());
        }
        if(Objects.nonNull(unit.getLastHandoverDate())){
            detail.setLastHandoverDate(unit.getLastHandoverDate());
        }
        if(Objects.nonNull(unit.getLastHandoverFrom())){
            detail.setLastHandoverFrom(unit.getLastHandoverFrom());
        }
        if(Objects.nonNull(unit.getVarianceAcknowledgementRequired())){
            detail.setVarianceAcknowledgementData(unit.getVarianceAcknowledgementRequired());
        }
        if(Objects.nonNull(unit.isHotspotEnabled())){
            if(unit.isHotspotEnabled())
                detail.setIsHotSpotLive(AppConstants.YES);
            else
                detail.setIsHotSpotLive(AppConstants.NO);
        }

        if(Objects.nonNull(unit.getAssemblyStrictMode()))
        {
            if(unit.getAssemblyStrictMode())
            {
                detail.setAssemblyStrictMode(AppConstants.YES);
            }
            else {
                detail.setAssemblyStrictMode(AppConstants.NO);
            }
        }
        else
        {
            detail.setAssemblyStrictMode(AppConstants.NO);
        }

        if(Objects.nonNull(unit.getAssemblyOtpMode()))
        {
            if(unit.getAssemblyOtpMode())
            {
                detail.setAssemblyOtpMode(AppConstants.YES);
            }
            else {
                detail.setAssemblyOtpMode(AppConstants.NO);
            }
        }
        else
        {
            detail.setAssemblyOtpMode(AppConstants.NO);
        }


        manager.persist(detail);
        manager.flush();

        String businessCostCenter = detail.getUnitId()+"_"+ detail.getUnitName();
        businessCostCenter = businessCostCenter.replaceAll("[^a-zA-Z0-9]", "_");
        businessCostCenter = businessCostCenter.replaceAll("_{2,}", "_");

        detail.setCostCenter(businessCostCenter.toUpperCase());
        detail.setConsideredForAccounting("Y");
        addAttributes(unit, detail);
        List<BusinessHours> businessHours = unit.getOperationalHours().stream()
            .map(data -> createBusinessHours(data, detail)).collect(Collectors.toList());
        detail.setBusinessHours(businessHours);
        List<UnitTaxMapping> unitTaxMappings = unit.getTaxProfiles().stream()
            .map(profile -> createTaxProfile(profile, detail, unit.getAddress().getState().toUpperCase()))
            .collect(Collectors.toList());
        detail.setUnitTaxMappings(unitTaxMappings);
        if (unit.getCloneUnitId() != null) {
            detail.setClonedFrom(unit.getCloneUnitId());
            UnitDetail cloneUnit = manager.find(UnitDetail.class, unit.getCloneUnitId());
                clonePaymentModeMappings(detail, cloneUnit);
            cloneProductMappings(detail, cloneUnit);
            cloneProductProfileMappings(detail, cloneUnit);
            cloneUnitPartnerBrandMetaDataMapping(detail , cloneUnit);
//            cloneCrmAppScreenDetail(detail,cloneUnit.getUnitId());
        }
        if(unit.getMenuSequenceCloneUnitId() != null){
            UnitDetail cloneUnit = manager.find(UnitDetail.class, unit.getMenuSequenceCloneUnitId());
            LOG.info("Calling clone App Banner clone unit id "+cloneUnit.getUnitId()+"original is "+unit.getId());
            cloneAppBanner(detail , cloneUnit);
            cloneMenuSequence(detail , cloneUnit);
        }
        IdCodeName cafeManager = new IdCodeName(detail.getCafeManager(), masterDataCache.getEmployees().get(detail.getCafeManager()), null);
        return MasterDataConverter.convert(detail, cafeManager, false,props);
    }

    private void cloneCrmAppScreenDetail(UnitDetail detail, Integer unitId) {
        LOG.info("Cloning CRM App Screen Detail for Unit {}",unitId);
        IdCodeName name = new IdCodeName(detail.getUnitId(), detail.getUnitName(), unitId.toString());
        String endPoint = props.getKettleEndpoint() + KettleServiceEndpoints.CLONE_CRM_APP_BANNER;
        try {
            WebServiceHelper.postRequestWithAuthInternalTimeout(endPoint, props.getKettleAuthInternal(), name);
        } catch (Exception e) {
            LOG.error("Error while creating web request to {}", endPoint, e);
        }
    }

    private void addAttributes(Unit unit, UnitDetail detail) {
        addAttribute(AttributeDefinitionEnum.NO_OF_TABLES, unit.getNoOfTables(), detail);
        addAttribute(AttributeDefinitionEnum.WORKSTATION_ENABLED, AppConstants.getValue(unit.isWorkstationEnabled()), detail);
        addAttribute(AttributeDefinitionEnum.FREE_INTERNET_ACCESS, AppConstants.getValue(unit.isFreeInternetAccess()), detail);
        addAttribute(AttributeDefinitionEnum.HAS_TABLE_SERVICE, AppConstants.getValue(unit.isTableService()), detail);
        addAttribute(AttributeDefinitionEnum.TABLE_SERVICE_TYPE,unit.getTableServiceType(), detail);

        addAttribute(AttributeDefinitionEnum.IS_TOKEN_ENABLED, AppConstants.getValue(unit.isTokenEnabled()), detail);
        addAttribute(AttributeDefinitionEnum.IS_DG_AVAILABLE, AppConstants.getValue(unit.isdGAvailable()), detail);
        addAttribute(AttributeDefinitionEnum.HOT_N_COLD_MERGED, AppConstants.getValue(unit.isHotAndColdMerged()), detail);
        addAttribute(AttributeDefinitionEnum.TOKEN_LIMIT, unit.getTokenLimit(), detail);
        addAttribute(AttributeDefinitionEnum.ELECTRICITY_METER_COUNT, unit.getNoOfMeter(), detail);
        addAttribute(AttributeDefinitionEnum.LIVE_INVENTORY_ENABLED, AppConstants.getValue(unit.isLiveInventoryEnabled()), detail);
        addAttribute(AttributeDefinitionEnum.TRUE_CALLER_ONBOARDING, unit.getTrueCallerEnabled(), detail);
        addAttribute(AttributeDefinitionEnum.IS_PARTNER_PRICED, null, detail);
        addAttribute(AttributeDefinitionEnum.GOOGLE_MERCHANT_ID, unit.getGoogleMerchantId(), detail);
        addAttribute(AttributeDefinitionEnum.PACKAGING_TYPE, unit.getPackagingType(), detail);
        addAttribute(AttributeDefinitionEnum.PACKAGING_VALUE, unit.getPackagingValue(), detail);
        addAttribute(AttributeDefinitionEnum.REVENUE_CERTIFICATE_EMAIL,unit.getRevenueCertificateEmail(),detail);
        addAttribute(AttributeDefinitionEnum.REVENUE_CERTIFICATE_GENERATION_ENABLE,AppConstants.getValue(unit.isRevenueCertificateGenerationEnable()),detail);
        addAttribute(AttributeDefinitionEnum.VARIANCE_ACK_EMP_ID,unit.getVarianceAcknowledgementEmployees(),detail);
        addAttribute(AttributeDefinitionEnum.OTP_VIA_EMAIL,unit.getIsOtpViaEmail(),detail);
        addAttribute(AttributeDefinitionEnum.IS_TESTING_UNIT,AppConstants.getValue(unit.getIsTestingUnit()),detail);

        addAttribute(AttributeDefinitionEnum.CUSTOM_ADDONS_LIMIT, unit.getCustomAddonsLimit(), detail);

        if(Objects.nonNull(unit.getIsTestingUnit()) && unit.getIsTestingUnit()) {
            addAttribute(AttributeDefinitionEnum.IS_TESTING_UNIT,AppConstants.getValue(unit.getIsTestingUnit()),detail);
        }

        if(Objects.nonNull(unit.getMonkRecipeProfile())){
            addAttribute(AttributeDefinitionEnum.MONK_PRICE_PROFILE, unit.getMonkRecipeProfile(), detail);
        }
        if(Objects.nonNull(unit.getMilkTrackingEnabled())){
            addAttribute(AttributeDefinitionEnum.MILK_TRACKING, AppConstants.getValue(unit.getMilkTrackingEnabled()), detail);
        }
        if(Objects.nonNull(unit.getNoOfMonksNeeded())){
            addAttribute(AttributeDefinitionEnum.NO_OF_MONK_NEEDED, unit.getNoOfMonksNeeded(), detail);
        }
        if(Objects.nonNull(unit.getCheckBigPanThreshold())){
            addAttribute(AttributeDefinitionEnum.CHECK_BIG_PAN_THRESHOLD, unit.getCheckBigPanThreshold(), detail);
        }
        // added
        if(Objects.nonNull(unit.getServiceCharge())){
            addAttribute(AttributeDefinitionEnum.SERVICE_CHARGE_VALUE, unit.getServiceCharge(), detail);
        }
        if(Objects.nonNull(unit.getServiceChargePosEnabled())){
            addAttribute(AttributeDefinitionEnum.SERVICE_CHARGE_POS_ENABLED,  AppConstants.getValue(unit.getServiceChargePosEnabled()), detail);
        }
        if(Objects.nonNull(unit.getServiceChargeAppEnabled())){
            addAttribute(AttributeDefinitionEnum.SERVICE_CHARGE_APP_ENABLED,  AppConstants.getValue(unit.getServiceChargeAppEnabled()), detail);
        }
        if(Objects.nonNull(unit.getCafeType())){
            addAttribute(AttributeDefinitionEnum.CAFE_SERVICE_TYPE,  unit.getCafeType(), detail);
        }

    }

    private void addAttribute(AttributeDefinitionEnum attribute, Object value, UnitDetail detail) {
        UnitAttributeMapping mapping = new UnitAttributeMapping();
        mapping.setAttributeCode(attribute.name());
        mapping.setAttributeId(attribute.getId());
        mapping.setAttributeType(attribute.getType().name());
        if ((attribute.equals(AttributeDefinitionEnum.GOOGLE_MERCHANT_ID)) && (value == null)) {
            mapping.setAttributeValue("");
        } else {
            mapping.setAttributeValue(value == null ? attribute.getDefaultValue().toString() : value.toString());
        }
        mapping.setMappingStatus(AppConstants.ACTIVE);
        mapping.setUnitDetail(detail);
        manager.persist(mapping);
        manager.flush();
    }

    private void clonePaymentModeMappings(UnitDetail unit, UnitDetail cloneUnit) {
        LOG.info("Cloning Payment Mode Mappings for Unit {} ",unit.getUnitId());
        for (UnitPaymentModeMapping mapping : cloneUnit.getUnitPaymentMappings()) {
            UnitPaymentModeMapping value = new UnitPaymentModeMapping(mapping.getPaymentMode(), unit,
                mapping.getMappingStatus());
            manager.persist(value);
        }
        manager.flush();
    }

    private void cloneUnitPartnerBrandMetaDataMapping(UnitDetail unit, UnitDetail cloneUnit) {
        LOG.info("Cloning Unit Partner Meta Data Mapping for Unit {} ",unit.getUnitId());
        try {
            for (UnitPartnerBrandMappingMetadata data : getCloneUnits(cloneUnit.getUnitId())) {
                UnitPartnerBrandMappingMetadata value = new UnitPartnerBrandMappingMetadata(unit.getUnitId(), data.getPartnerId(),
                        data.getBrandId(), data.getKey(), data.getValue(), data.getStatus());
                manager.persist(value);
            }
            manager.flush();
        }
        catch (Exception e){
            LOG.error("error in cloneUnitPartnerBrandMetaDataMapping");
        }
    }

    public List<UnitPartnerBrandMappingMetadata> getCloneUnits(Integer unitId){
        LOG.info("Getting UnitPartnerBrandMappingMetadata details of {}",unitId);
        Query query = manager.createQuery("FROM UnitPartnerBrandMappingMetadata u WHERE u.unitId =:unitId");
        query.setParameter("unitId",unitId);
        try{
            List<UnitPartnerBrandMappingMetadata> list = query.getResultList();
            return  list;
        }
        catch (NoResultException e){
            LOG.error("No result from getCloneUnits of UnitPartnerBrandMetaDataMapping");
        }
        return new ArrayList<>();
    }

    private void cloneAppBanner(UnitDetail unit, UnitDetail cloneUnit){
        LOG.info("Cloning App Banner for Unit {} ",unit.getUnitId());
        Integer unitId = cloneUnit.getUnitId();
        try{
            Query query = manager.createQuery("FROM AppOfferMappingData a WHERE a.mappingValue =:unitId");
            query.setParameter("unitId",unitId);
            List<AppOfferMappingData> data = query.getResultList();
            for(AppOfferMappingData value : data){
                AppOfferMappingData entry = new AppOfferMappingData();
                entry.setAppOfferDetailData(value.getAppOfferDetailData());
                entry.setMappingType(value.getMappingType());
                entry.setMappingValue(unit.getUnitId());
                entry.setMappingUnitName(unit.getUnitName());
                entry.setMappingStatus(value.getMappingStatus());
                entry.setUpdatedBy(value.getUpdatedBy());
                entry.setUpdateTime(AppUtils.getCurrentTimestamp());
                manager.persist(entry);
            }
            manager.flush();
        }
        catch (NoResultException e){
            LOG.info("No result found while cloning app banner ");
        }
        catch(Exception e){
            LOG.error("Error occured while cloning App Banner : ",e);
        }
    }

    public void cloneMenuSequence(UnitDetail unit, UnitDetail cloneUnit){
        LOG.info("Cloning of Menu sequence initialised");
        Integer unitId = cloneUnit.getUnitId();
        try{
            //query for fetching data to clone
            Query query=manager.createQuery("FROM UnitChannelPartnerMappingData u WHERE u.unitId =:unitId and u.channelPartnerId =:channelPartnerId");
            query.setParameter("unitId",unitId);
            query.setParameter("channelPartnerId",AppConstants.CHANNEL_PARTNER_DINE_IN_APP);
            List<UnitChannelPartnerMappingData> list = query.getResultList();
            Integer cloneId = null,originalId = null;
            for(UnitChannelPartnerMappingData value : list){
                cloneId=value.getId();
                UnitChannelPartnerMappingData entry = new UnitChannelPartnerMappingData();
                entry.setUnitId(unit.getUnitId());
                entry.setChannelPartnerId(value.getChannelPartnerId());
                entry.setStatus(value.getStatus());
                entry.setDeliveryPartnerId(value.getDeliveryPartnerId());
                manager.persist(entry);
            }
            manager.flush();
            try {
                //query for fetching original unit Id
                Query query2 = manager.createQuery("FROM UnitChannelPartnerMappingData u WHERE u.unitId =:unitId and u.channelPartnerId =:channelPartnerId");
                query2.setParameter("unitId", unit.getUnitId());
                query2.setParameter("channelPartnerId", AppConstants.CHANNEL_PARTNER_DINE_IN_APP);
                List<UnitChannelPartnerMappingData> original = query2.getResultList();
                for (UnitChannelPartnerMappingData value : original) {
                    originalId = value.getId();
                }
                try {
                    //query for fetching data of Menu mappings of Clone Unit
                    Query query3 = manager.createQuery("FROM UnitChannelPartnerMenuMappingData u WHERE u.unitPartnerMappingId =:unitChannelPartnerId");
                    query3.setParameter("unitChannelPartnerId",cloneId);
                    List<UnitChannelPartnerMenuMappingData> listOfMenuMappings = query3.getResultList();
                    for(UnitChannelPartnerMenuMappingData value : listOfMenuMappings){
                        UnitChannelPartnerMenuMappingData entry = new UnitChannelPartnerMenuMappingData();
                        entry.setUnitPartnerMappingId(originalId);
                        entry.setMenuSequenceId(value.getMenuSequenceId());
                        entry.setStartTime(value.getStartTime());
                        entry.setEndTime(value.getEndTime());
                        entry.setMenuDay(value.getMenuDay());
                        entry.setMenuApp(value.getMenuApp());
                        entry.setBrandId(value.getBrandId());
                        entry.setMenuType(value.getMenuType());
                        entry.setStatus(value.getStatus());
                        entry.setCreatedAt(AppUtils.getCurrentTimestamp());
                        entry.setUpdatedAt(AppUtils.getCurrentTimestamp());
                        entry.setMenuSequenceName(value.getMenuSequenceName());
                        entry.setCartRecommendationSequenceId(value.getCartRecommendationSequenceId());
                        entry.setMenuRecommendationSequenceId(value.getMenuRecommendationSequenceId());
                        entry.setCreatedBy(value.getCreatedBy());
                        entry.setUpdatedBy(value.getUpdatedBy());
                        manager.persist(entry);
                    }
                    manager.flush();
                }
                catch (Exception e){
                    LOG.error("Error Occurred while fetching Menu mapping",e);
                }
            }
            catch (Exception e){
                LOG.error("Exception occurred while fetching Original unit Id for clonng of Menu Sequence",e);
            }
        }
        catch (Exception e){
            LOG.error("Error occurred in menu sequence Clone",e);
        }

    }

    @Override
    public Unit updateUnit(Unit unit) throws DataUpdationException {
        UnitDetail detail = manager.find(UnitDetail.class, unit.getId());
        updateAddress(unit.getAddress(), detail.getAddressInfo());
        updateDetail(unit, detail);
        updateTaxMapping(detail, unit.getTaxProfiles(), unit.getAddress().getState().toUpperCase());
        updateBusinessHours(detail, unit.getOperationalHours());
        IdCodeName cafeManager = new IdCodeName(detail.getCafeManager(), masterDataCache.getEmployees().get(detail.getCafeManager()), null);
        return MasterDataConverter.convert(detail, cafeManager, false,props);
    }


    @Override
    public boolean updateUnitBusinessHours(CafeTimingChangeRequest unitRequest) throws DataUpdationException {
        try {
            //Updating Dine In and Delivery Business Timings Hours for a particular Unit
            String daysOfWeek= unitRequest.getDayOfWeekText();
            Set<String> daysOfWeekSet = Stream.of(daysOfWeek.trim().split("\\s*,\\s*")).collect(Collectors.toSet());
            Integer unitId = unitRequest.getUnitId();
            Query query = manager.createQuery("FROM BusinessHours b WHERE b.unitDetail.id =:unitId and b.brandId = :chaayosBrandId");
            query.setParameter("unitId", unitId);
            query.setParameter("chaayosBrandId",AppConstants.CHAAYOS_BRAND_ID);
            List<BusinessHours> res = query.getResultList();
            List<UnitHours> lisOperationHours = masterDataCache.getOperationalHours().get(unitRequest.getUnitId());
            for (BusinessHours businessHours : res) {
                if (daysOfWeekSet.contains(businessHours.getDayOfTheWeek())) {
                    int id = businessHours.getBusinessHoursId();
                    if (unitRequest.getDineInOpenTime() != null && unitRequest.getDineInCloseTime() != null) {
                        Date dineInOpen = AppUtils.getDate(unitRequest.getDineInOpenTime(), "hh:mm:ss");
                        Date dineInClose = AppUtils.getDate(unitRequest.getDineInCloseTime(), "hh:mm:ss");
                        Query queryDineIn = manager.createQuery("UPDATE BusinessHours b SET b.dineInOpeningTime= :dineInOpen, b.dineInClosingTime = :dineInClose WHERE b.businessHoursId = :id");
                        queryDineIn.setParameter("dineInOpen", dineInOpen);
                        queryDineIn.setParameter("dineInClose", dineInClose);
                        queryDineIn.setParameter("id", id);
                        queryDineIn.executeUpdate();
                    }

                    if (unitRequest.getDeliveryOpeningTime() != null && unitRequest.getDeliveryClosingTime() != null) {
                        Date deliveryOpen = AppUtils.getDate(unitRequest.getDeliveryOpeningTime(), "hh:mm:ss");
                        Date deliveryClose = AppUtils.getDate(unitRequest.getDeliveryClosingTime(), "hh:mm:ss");
                        Query queryDelivery = manager.createQuery("UPDATE BusinessHours b SET b.deliveryOpeningTime =:deliveryOpen, b.deliveryClosingTime =:deliveryClose WHERE b.businessHoursId = :id");
                        queryDelivery.setParameter("deliveryOpen", deliveryOpen);
                        queryDelivery.setParameter("deliveryClose", deliveryClose);
                        queryDelivery.setParameter("id", id);
                        queryDelivery.executeUpdate();
                    }
                }
            }

            //Updating Business Timing Hours in Master Data Cache
            for(UnitHours unitHours:lisOperationHours){
                if (daysOfWeekSet.contains(unitHours.getDayOfTheWeek())) {
                    if (unitRequest.getDineInOpenTime() != null && unitRequest.getDineInCloseTime() != null) {
                        Time dineInOpen = java.sql.Time.valueOf(unitRequest.getDineInOpenTime());
                        Time dineInClose = java.sql.Time.valueOf(unitRequest.getDineInCloseTime());
                        unitHours.setDineInOpeningTime(dineInOpen);
                        unitHours.setDineInClosingTime(dineInClose);
                    }

                    if (unitRequest.getDeliveryOpeningTime() != null && unitRequest.getDeliveryClosingTime() != null) {
                        Time deliveryOpen =  java.sql.Time.valueOf(unitRequest.getDeliveryOpeningTime());
                        Time deliveryClose = java.sql.Time.valueOf(unitRequest.getDeliveryClosingTime());
                        unitHours.setDeliveryOpeningTime(deliveryOpen);
                        unitHours.setDeliveryClosingTime(deliveryClose);
                    }
                }
            }
            masterDataCache.setOperationalHoursForUnit(unitRequest.getUnitId(),lisOperationHours);
            return true;
        }catch (Exception e){
            LOG.info("Error in Updating Business Hours Table::{}",e.toString());
        }
        return false;
    }


    private void updateTaxMapping(UnitDetail detail, List<TaxProfile> profiles, String state) {
        for (TaxProfile profile : profiles) {
            if (profile.getId() > 0) {
                UnitTaxMapping mapping = manager.find(UnitTaxMapping.class, profile.getId());
                mapping.setProfileStatus(profile.getStatus());
                mapping.setState(profile.getStatus());
                mapping.setTaxPercentage(profile.getPercentage());
            } else {
                createTaxProfile(profile, detail, state);
            }
        }
    }

    private void updateBusinessHours(UnitDetail detail, List<UnitHours> profiles) {
        for (UnitHours profile : profiles) {
            if (profile.getId() > 0) {
                BusinessHours mapping = manager.find(BusinessHours.class, profile.getId());
                convert(profile, mapping, detail);
            } else {
                createBusinessHours(profile, detail);
            }
        }
    }


    private void updateDetail(Unit unit, UnitDetail unitDetail) {
        unitDetail.setNoOfTerminals(unit.getNoOfTerminals());
        unitDetail.setNoOfTakeawayTerminals(unit.getNoOfTakeawayTerminals());
        unitDetail.setStartDate(unit.getStartDate());
        unitDetail.setCompanyDetail(manager.find(CompanyDetail.class, unit.getCompany().getId()));
        unitDetail.setGstin(unit.getTin());
        unitDetail.setShortName(unit.getShortName());
        unitDetail.setUnitCategory(unit.getFamily().name());
        unitDetail.setUnitEmail(unit.getUnitEmail());
        unitDetail.setUnitName(unit.getName());
        unitDetail.setUnitRegion(unit.getRegion());
        unitDetail.setUnitStatus(unit.getStatus().name());
        unitDetail.setUnitSubCategory(unit.getSubCategory().name());
        unitDetail.setReferenceName(unit.getReferenceName());
        unitDetail.setCommunicationChannel(unit.getChannel());
        unitDetail.setCreditAccountId(unit.getCreditAccount());
        unitDetail.setIsLive(AppConstants.getValue(unit.isLive()));
        unitDetail.setHandoverDate(unit.getHandoverDate());
        unitDetail.setShortCode(unit.getShortCode());
        if(unit.getFssai() != null && !unit.getFssai().isEmpty()){
            unitDetail.setFssai(unit.getFssai());
        }
        if (unit.getCafeAppStatus() == null && ((unit.getFamily().equals(UnitCategory.CAFE)))) {
            unitDetail.setCafeAppStatus(AppConstants.IN_ACTIVE);
        }
        if ((unit.getCafeAppStatus() == null) && (unit.getFamily().equals(UnitCategory.CAFE))) {
            unitDetail.setCafeNeoStatus(AppConstants.IN_ACTIVE);
        }
        updateAttributes(unit, unitDetail);
        if (unit.getLocation() != null) {
            unitDetail.setLocation(manager.find(LocationDetail.class, unit.getLocation().getId()));
        }
        if (unit.getUnitManager() != null) {
            unitDetail.setUnitManager(manager.find(EmployeeDetail.class, unit.getUnitManager().getId()));
        }
        if (unit.getCafeManager() != null) {
            unitDetail.setCafeManager(unit.getCafeManager().getId());
        }
        if (unit.getProbableOpeningDate() != null){
            unitDetail.setProbableOpeningDate(unit.getProbableOpeningDate());
        }
        if(unit.getPricingProfile() != null){
            unitDetail.setPricingProfile(unit.getPricingProfile());
        }
        if(Objects.nonNull(unit.getUnitZone())){
            unitDetail.setUnitZone(unit.getUnitZone());
        }
        if(Objects.nonNull(unit.getPosVersion())){
            unitDetail.setPosVersion(unit.getPosVersion());
        }
        if(Objects.nonNull(unit.isHotspotEnabled())){
            if(unit.isHotspotEnabled())
                unitDetail.setIsHotSpotLive(AppConstants.YES);
            else
                unitDetail.setIsHotSpotLive(AppConstants.NO);
        }
        if(Objects.nonNull(unit.getAssemblyStrictMode())){
            if(unit.getAssemblyStrictMode())
                unitDetail.setAssemblyStrictMode(AppConstants.YES);
            else
                unitDetail.setAssemblyStrictMode(AppConstants.NO);
        }
        else
        {
            unitDetail.setAssemblyStrictMode(AppConstants.NO);
        }

        if(Objects.nonNull(unit.getAssemblyOtpMode())){
            if(unit.getAssemblyOtpMode())
                unitDetail.setAssemblyOtpMode(AppConstants.YES);
            else
                unitDetail.setAssemblyOtpMode(AppConstants.NO);
        }
        else
        {
            unitDetail.setAssemblyOtpMode(AppConstants.NO);
        }
        manager.flush();
    }

    /**
     * @param unit
     * @param unitDetail
     */
    private void updateAttributes(Unit unit, UnitDetail unitDetail) {

        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.GOOGLE_MERCHANT_ID))) {
            addAttribute(AttributeDefinitionEnum.GOOGLE_MERCHANT_ID, unit.getGoogleMerchantId(), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.PACKAGING_TYPE))) {
            addAttribute(AttributeDefinitionEnum.PACKAGING_TYPE, unit.getPackagingType(), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.PACKAGING_VALUE))) {
            addAttribute(AttributeDefinitionEnum.PACKAGING_VALUE, unit.getPackagingValue(), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.MONK_PRICE_PROFILE))
                && Objects.nonNull(unit.getMonkRecipeProfile())) {
            addAttribute(AttributeDefinitionEnum.MONK_PRICE_PROFILE, unit.getMonkRecipeProfile(), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.MILK_TRACKING))
                && Objects.nonNull(unit.getMilkTrackingEnabled())) {
            addAttribute(AttributeDefinitionEnum.MILK_TRACKING, AppConstants.getValue(unit.getMilkTrackingEnabled()), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.NO_OF_MONK_NEEDED))
                && Objects.nonNull(unit.getNoOfMonksNeeded())) {
            addAttribute(AttributeDefinitionEnum.NO_OF_MONK_NEEDED, unit.getNoOfMonksNeeded(), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.OTP_VIA_EMAIL))
                && Objects.nonNull(unit.getIsOtpViaEmail())) {
            addAttribute(AttributeDefinitionEnum.OTP_VIA_EMAIL, AppConstants.getValue(unit.getIsOtpViaEmail()), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.CHECK_BIG_PAN_THRESHOLD))
                && Objects.nonNull(unit.getCheckBigPanThreshold())) {
            addAttribute(AttributeDefinitionEnum.CHECK_BIG_PAN_THRESHOLD, AppConstants.getValue(unit.getCheckBigPanThreshold()), unitDetail);
        }
        // added

        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.SERVICE_CHARGE_VALUE))) {
            addAttribute(AttributeDefinitionEnum.SERVICE_CHARGE_VALUE, unit.getServiceCharge(), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.SERVICE_CHARGE_POS_ENABLED))) {
            addAttribute(AttributeDefinitionEnum.SERVICE_CHARGE_POS_ENABLED, AppConstants.getValue(unit.getServiceChargePosEnabled()), unitDetail);
        }
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.SERVICE_CHARGE_APP_ENABLED))) {
            addAttribute(AttributeDefinitionEnum.SERVICE_CHARGE_APP_ENABLED, AppConstants.getValue(unit.getServiceChargeAppEnabled()), unitDetail);
        }


        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.CAFE_SERVICE_TYPE))) {
            addAttribute(AttributeDefinitionEnum.CAFE_SERVICE_TYPE, unit.getCafeType().name(), unitDetail);
        }

        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.TABLE_SERVICE_TYPE))
                && Objects.nonNull(unit.getTableServiceType())) {
            addAttribute(AttributeDefinitionEnum.TABLE_SERVICE_TYPE, unit.getTableServiceType(), unitDetail);
        }
        
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.FEEDBACK_FORM_POS_ENABLED))) {
            addAttribute(AttributeDefinitionEnum.FEEDBACK_FORM_POS_ENABLED, unit.isFeedbackFormEnabledForPos(), unitDetail);
        }
        
        if (!(convert(unitDetail.getUnitAttributeMappings()).contains(AttributeDefinitionEnum.DREAM_FOLKS_OUTLET_ID))) {
            addAttribute(AttributeDefinitionEnum.DREAM_FOLKS_OUTLET_ID, unit.getDreamfolksOutletId(), unitDetail);
        }
        
        
        for (UnitAttributeMapping mapping : unitDetail.getUnitAttributeMappings()) {
            AttributeDefinitionEnum def = AttributeDefinitionEnum.valueOf(mapping.getAttributeCode());
            if (def.equals(AttributeDefinitionEnum.WORKSTATION_ENABLED)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isWorkstationEnabled()));
            } else if (def.equals(AttributeDefinitionEnum.FREE_INTERNET_ACCESS)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isFreeInternetAccess()));
            } else if (def.equals(AttributeDefinitionEnum.HAS_TABLE_SERVICE)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isTableService()));
            } else if(def.equals(AttributeDefinitionEnum.TABLE_SERVICE_TYPE)) {
                mapping.setAttributeValue("" + unit.getTableServiceType());
            } else if (def.equals(AttributeDefinitionEnum.NO_OF_TABLES)) {
                mapping.setAttributeValue("" + unit.getNoOfTables());
            } else if (def.equals(AttributeDefinitionEnum.TRUE_CALLER_ONBOARDING)) {
                if (unit.getTrueCallerEnabled() != null) {
                    mapping.setAttributeValue(unit.getTrueCallerEnabled().name());
                }
            } else if (def.equals(AttributeDefinitionEnum.IS_PARTNER_PRICED)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isPartnerPriced()));
            } else if (def.equals(AttributeDefinitionEnum.IS_TOKEN_ENABLED)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isTokenEnabled()));
            } else if (def.equals(AttributeDefinitionEnum.TOKEN_LIMIT)) {
                mapping.setAttributeValue("" + unit.getTokenLimit());
            } else if (def.equals(AttributeDefinitionEnum.ELECTRICITY_METER_COUNT)) {
                mapping.setAttributeValue("" + unit.getNoOfMeter());
            } else if (def.equals(AttributeDefinitionEnum.IS_DG_AVAILABLE)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isdGAvailable()));
            } else if (def.equals(AttributeDefinitionEnum.HOT_N_COLD_MERGED)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isHotAndColdMerged()));
            } else if (def.equals(AttributeDefinitionEnum.LIVE_INVENTORY_ENABLED)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isLiveInventoryEnabled()));
            } else if (def.equals(AttributeDefinitionEnum.GOOGLE_MERCHANT_ID)) {
//                System.out.println("GOOGLE_MERCHANT_ID");
                if (unit.getGoogleMerchantId() != null) {
                    mapping.setAttributeValue(unit.getGoogleMerchantId());
                }
            } else if (def.equals(AttributeDefinitionEnum.PACKAGING_TYPE)) {
                mapping.setAttributeValue(unit.getPackagingType());
            } else if (def.equals(AttributeDefinitionEnum.PACKAGING_VALUE)) {
                mapping.setAttributeValue(unit.getPackagingValue().toString());
            } else if (def.equals(AttributeDefinitionEnum.REVENUE_CERTIFICATE_GENERATION_ENABLE)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isRevenueCertificateGenerationEnable()));
            } else if (def.equals(AttributeDefinitionEnum.REVENUE_CERTIFICATE_EMAIL)) {
                mapping.setAttributeValue(unit.getRevenueCertificateEmail());
            } else if (def.equals(AttributeDefinitionEnum.VARIANCE_ACK_EMP_ID)) {
                mapping.setAttributeValue(unit.getVarianceAcknowledgementEmployees());
            } else if (def.equals(AttributeDefinitionEnum.NO_OF_MONK_NEEDED)
                    && Objects.nonNull(unit.getNoOfMonksNeeded())) {
                mapping.setAttributeValue(String.valueOf(unit.getNoOfMonksNeeded()));
            }else if (def.equals(AttributeDefinitionEnum.MONK_PRICE_PROFILE)
                    && Objects.nonNull(unit.getMonkRecipeProfile())){
                mapping.setAttributeValue(unit.getMonkRecipeProfile());
            }else if (def.equals(AttributeDefinitionEnum.MILK_TRACKING)
                    && Objects.nonNull(unit.getMilkTrackingEnabled())){
                mapping.setAttributeValue(AppConstants.getValue(unit.getMilkTrackingEnabled()));
            }else if (def.equals(AttributeDefinitionEnum.OTP_VIA_EMAIL)
                    && Objects.nonNull(unit.getIsOtpViaEmail())){
                mapping.setAttributeValue(AppConstants.getValue(unit.getIsOtpViaEmail()));
            }else if (def.equals(AttributeDefinitionEnum.CHECK_BIG_PAN_THRESHOLD)
                    && Objects.nonNull(unit.getCheckBigPanThreshold())){
                mapping.setAttributeValue(unit.getCheckBigPanThreshold());
            } //3 added
              else if (def.equals(AttributeDefinitionEnum.SERVICE_CHARGE_VALUE)) {
                mapping.setAttributeValue(unit.getServiceCharge().toString());
            } else if (def.equals(AttributeDefinitionEnum.SERVICE_CHARGE_POS_ENABLED)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.getServiceChargePosEnabled()));
            } else if (def.equals(AttributeDefinitionEnum.SERVICE_CHARGE_APP_ENABLED)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.getServiceChargeAppEnabled()));
            } else if (def.equals(AttributeDefinitionEnum.CAFE_SERVICE_TYPE)) {
                mapping.setAttributeValue(unit.getCafeType().name());
            } else if (def.equals(AttributeDefinitionEnum.CUSTOM_ADDONS_LIMIT)) {
                mapping.setAttributeValue(unit.getCustomAddonsLimit().toString());
            }  else if (def.equals(AttributeDefinitionEnum.FEEDBACK_FORM_POS_ENABLED)) {
                mapping.setAttributeValue(AppConstants.getValue(unit.isFeedbackFormEnabledForPos()));
            }  else if (def.equals(AttributeDefinitionEnum.DREAM_FOLKS_OUTLET_ID)) {
                mapping.setAttributeValue(unit.getDreamfolksOutletId());
            }
        }
    }

    private List<AttributeDefinitionEnum> convert(List<UnitAttributeMapping> mappings) {
        List<AttributeDefinitionEnum> list = new ArrayList<>();
        List<String> availableAttributes = Arrays.stream(AttributeDefinitionEnum.values()).map(AttributeDefinitionEnum::name).toList();
        for (UnitAttributeMapping mapping : mappings) {
            if (!availableAttributes.contains(mapping.getAttributeCode())) {
                continue;
            }
            list.add(AttributeDefinitionEnum.valueOf(mapping.getAttributeCode()));
        }
        return list;
    }

    public AddressInfo updateAddress(Address address, AddressInfo addressInfo) {
        addressInfo.setAddressLine1(address.getLine1());
        addressInfo.setAddressLine2(address.getLine2());
        addressInfo.setAddressLine3(address.getLine3());
        addressInfo.setAddressType(address.getAddressType());
        addressInfo.setCity(address.getCity());
        addressInfo.setContactNum1(address.getContact1());
        addressInfo.setContactNum2(address.getContact2());
        addressInfo.setCountry(address.getCountry());
        addressInfo.setState(address.getState());
        addressInfo.setZipcode(address.getZipCode());
        addressInfo.setLatitude(address.getLatitude());
        addressInfo.setLongitude(address.getLongitude());
        addressInfo = manager.merge(addressInfo);
        manager.flush();
        return addressInfo;
    }

    private UnitTaxMapping createTaxProfile(TaxProfile profile, UnitDetail unitDetail, String state) {
        UnitTaxMapping mapping = new UnitTaxMapping(getTaxProfile(profile.getProfileId()), unitDetail,
            profile.getPercentage(), profile.getStatus(), state);
        manager.persist(mapping);
        manager.flush();
        return mapping;
    }

    private BusinessHours createBusinessHours(UnitHours data, UnitDetail unitDetail) {
        BusinessHours hours = new BusinessHours();
        BusinessHours gntBusinessHours = new BusinessHours();
        convert(data, hours, unitDetail);
        convertGntOperationHours(data,gntBusinessHours,unitDetail);
        manager.persist(hours);
        manager.persist(gntBusinessHours);
        manager.flush();
        return hours;
    }

    private void convert(UnitHours data, BusinessHours hours, UnitDetail unitDetail) {
        hours.setDayOfTheWeek(data.getDayOfTheWeek());
        hours.setDayOfTheWeekNumber(data.getDayOfTheWeekNumber());
        hours.setDeliveryClosingTime(data.getDeliveryClosingTime());
        hours.setDeliveryOpeningTime(data.getDeliveryOpeningTime());
        hours.setDineInClosingTime(data.getDineInClosingTime());
        hours.setDineInOpeningTime(data.getDineInOpeningTime());
        hours.setHasDelivery(AppConstants.getValue(data.isHasDelivery()));
        hours.setHasDineIn(AppConstants.getValue(data.isHasDineIn()));
        hours.setHasTakeAway(AppConstants.getValue(data.isHasTakeAway()));
        hours.setIsOperational(AppConstants.getValue(data.isIsOperational()));
        hours.setNoOfShifts(data.getNoOfShifts());
        hours.setShiftOneHandoverTime(data.getShiftOneHandoverTime());
        hours.setShiftTwoHandoverTime(data.getShiftTwoHandoverTime());
        hours.setStatus(data.getStatus());
        hours.setTakeAwayClosingTime(data.getTakeAwayClosingTime());
        hours.setTakeAwayOpeningTime(data.getTakeAwayOpeningTime());
        hours.setUnitDetail(unitDetail);
    }

    private void convertGntOperationHours(UnitHours data, BusinessHours hours, UnitDetail unitDetail) {
        Time gntOpeningTime = Time.valueOf("11:00:00");
        Time gntClosingTime = Time.valueOf("23:00:00");
        Time defaultTime = Time.valueOf("00:00:00");
        hours.setDayOfTheWeek(data.getDayOfTheWeek());
        hours.setDayOfTheWeekNumber(data.getDayOfTheWeekNumber());
        hours.setDeliveryClosingTime(gntClosingTime);
        hours.setDeliveryOpeningTime(gntOpeningTime);
        hours.setDineInClosingTime(defaultTime);
        hours.setDineInOpeningTime(defaultTime);
        hours.setHasDelivery(AppUtils.setStatus(true));
        hours.setHasDineIn(AppUtils.setStatus(false));
        hours.setHasTakeAway(AppUtils.setStatus(false));
        hours.setIsOperational(AppUtils.setStatus(true));
        hours.setNoOfShifts(data.getNoOfShifts());
        hours.setShiftOneHandoverTime(data.getShiftOneHandoverTime());
        hours.setShiftTwoHandoverTime(data.getShiftTwoHandoverTime());
        hours.setStatus(AppConstants.ACTIVE);
        hours.setTakeAwayClosingTime(defaultTime);
        hours.setTakeAwayOpeningTime(defaultTime);
        hours.setUnitDetail(unitDetail);
        hours.setBrandId(AppConstants.GNT_BRAND_ID);

    }

    private boolean unitExist(String unitName) {
        Query query = manager.createQuery("FROM UnitDetail where upper(unitName) = :unitName");
        query.setParameter("unitName", unitName.toUpperCase());
        try {
            query.getSingleResult();
        } catch (NoResultException e) {
            return false;
        }
        return true;

    }

    public com.stpl.tech.master.data.model.TaxProfile getTaxProfile(int taxProfileId) {
        Query query = manager
            .createQuery("FROM com.stpl.tech.master.data.model.TaxProfile where taxProfileId = :taxProfileId");
        query.setParameter("taxProfileId", taxProfileId);
        Object o = query.getSingleResult();
        return o == null ? null : (com.stpl.tech.master.data.model.TaxProfile) query.getSingleResult();
    }

    public BusinessDivision getBusinessDivision(int businessDivId) {
        Query query = manager.createQuery("FROM BusinessDivision where businessDivId = :businessDivId");
        query.setParameter("businessDivId", businessDivId);
        Object o = query.getSingleResult();
        return o == null ? null : (BusinessDivision) query.getSingleResult();
    }

    public CompanyDetail getCompanyDetail(int id) {
        return manager.find(CompanyDetail.class, id);
    }

    public void cloneProductMappings(UnitDetail unit, UnitDetail cloneUnit) throws DataUpdationException {
        LOG.info("Cloning Product Mappings for Unit {}",unit.getUnitId());
        for (UnitProductMapping productMapping : cloneUnit.getUnitProductMappings()) {
            clone(productMapping, unit);
        }
    }

    public void cloneProductProfileMappings(UnitDetail unit, UnitDetail cloneUnit) throws DataUpdationException {
        LOG.info("Cloning Product profile Mappings for Unit {}",unit.getUnitId());
        priceProfileManagementService.cloneUnitPriceProfileMappings(unit,cloneUnit.getUnitId(),AppConstants.SYSTEM_EMPLOYEE_ID);
    }

    private UnitProductMapping clone(UnitProductMapping mapping, UnitDetail unitDetail) {
        UnitProductMapping value = new UnitProductMapping(mapping.getProductDetail(), unitDetail,
            mapping.getProductStatus(), AppUtils.getCurrentTimestamp(), AppUtils.getCurrentDate(),
            AppUtils.getInfiniteDate());
        manager.persist(value);
        manager.flush();
        List<UnitProductPricing> pricings = mapping.getUnitProductPricings().stream()
            .map(pricing -> clone(pricing, value)).collect(Collectors.toList());
        value.setUnitProductPricings(pricings);
        return value;
    }

    private UnitProductPricing clone(UnitProductPricing mapping, UnitProductMapping value) {
        UnitProductPricing pricing = new UnitProductPricing(mapping.getRefLookup(), value,
            AppUtils.getCurrentTimestamp(), mapping.getPrice(),mapping.getRecipeProfile(), mapping.getStatus()
        ,mapping.getAliasProductName(),mapping.getIsDeliveryOnlyProduct(), mapping.getPickDineInConsumables(), mapping.getDimensionDescriptor());
        manager.persist(pricing);
        manager.flush();
        return pricing;
    }

    @Override
    public Unit changeUnitStatus(int unitId, UnitStatus status) throws DataUpdationException {
        UnitDetail unit = manager.find(UnitDetail.class, unitId);
        unit.setUnitStatus(status.name());
        manager.flush();
        IdCodeName cafeManager = new IdCodeName(unit.getCafeManager(), masterDataCache.getEmployees().get(unit.getCafeManager()), null);
        return MasterDataConverter.convert(unit, cafeManager, false,props);
    }


    @Override
    public Unit changeUnitStatusForDineInAndChaayos(int unitId, Boolean dineIn, Boolean chaayos) throws DataUpdationException {
        UnitDetail unit = manager.find(UnitDetail.class, unitId);
        if (dineIn != null) {
            unit.setCafeAppStatus(Boolean.TRUE.equals(dineIn) ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE);
        }
        if (chaayos != null) {
            unit.setCafeNeoStatus(Boolean.TRUE.equals(chaayos) ? AppConstants.ACTIVE : AppConstants.IN_ACTIVE);
        }
        manager.flush();
        IdCodeName cafeManager = new IdCodeName(unit.getCafeManager(), masterDataCache.getEmployees().get(unit.getCafeManager()), null);
        return MasterDataConverter.convert(unit, cafeManager, false,props);
    }

    @Override
    public PaymentMode addPaymentMethod(PaymentMode paymentMode) {
        LOG.info("Adding Payment Method");
        try {
            manager.persist(paymentMode);
            manager.flush();
            return paymentMode;
        }catch (Exception e){
            LOG.error("Error ",e);
        }
        return null;
    }

    @Override
    public Unit updateProductMapping(int unitId, List<ProductPrice> prices) throws DataUpdationException {
        throw new NotImplementedException("Update Product Mapping API not implemented");
    }

    @Override
    public MonkConfiguration addMonkConfiguration(MonkConfiguration configuration) {
        List<MonkConfigurationData> configurationDataList = MasterDataConverter.convert(configuration);
        for (MonkConfigurationData data : configurationDataList) {
            data.setUpdatedAt(AppUtils.getCurrentTimestamp());
            manager.persist(data);
        }
        manager.flush();
        return configuration;
    }

    @Override
    public List<MonkAttr> getMonkMetadata() {
        Query query = manager.createQuery("FROM MonkAttrMetaData");
        List<MonkAttrMetaData> metaDataList = query.getResultList();
        List<MonkAttr> monkAttrList = metaDataList.stream()
            .map(MasterDataConverter::convertToAttr)
            .collect(Collectors.toList());
        return monkAttrList;
    }

    @Override
    public List<MonkConfigurationValue> getUnitMonkConfigurationData(int unitId) {
        Query query = manager.createQuery("FROM MonkConfigurationData where unitId = :unitId ");
        query.setParameter("unitId", unitId);
        List<MonkConfigurationData> confDataList = query.getResultList();
        List<MonkConfigurationValue> configurationValues = new ArrayList<>();
        for (MonkConfigurationData data : confDataList) {
            configurationValues.add(MasterDataConverter.convert(data));
        }
        return configurationValues;
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.data.dao.UnitManagementDao#changeUnitLiveStatus(int, boolean)
     */
    @Override
    public Unit changeUnitLiveStatus(int unitId, boolean status) {
        UnitDetail unit = manager.find(UnitDetail.class, unitId);
        unit.setIsLive(AppConstants.getValue(status));
        unit.setFaDaycloseEnabled(AppUtils.setStatus(status));
        manager.flush();
        IdCodeName cafeManager = new IdCodeName(unit.getCafeManager(), masterDataCache.getEmployees().get(unit.getCafeManager()), null);
        return MasterDataConverter.convert(unit, cafeManager, status,props);
    }

    @Override
    public Unit updateUnitF9Flag(Integer unitId) {
        UnitDetail unit = manager.find(UnitDetail.class, unitId);
        unit.setF9Enabled(AppConstants.YES);
        manager.flush();
        IdCodeName cafeManager = new IdCodeName(unit.getCafeManager(), masterDataCache.getEmployees().get(unit.getCafeManager()), null);
        return MasterDataConverter.convert(unit, cafeManager,true,props);
    }

    @Override
    public List<com.stpl.tech.master.domain.model.PaymentMode> getPaymentMethod() {
        List<com.stpl.tech.master.domain.model.PaymentMode> result = new ArrayList<>();
        String queryString = "FROM PaymentMode E order by E.paymentModeId";
        Query query = manager.createQuery(queryString);
        @SuppressWarnings("unchecked")
        List<PaymentMode> list = query.getResultList();
        for(PaymentMode mode : list){
            com.stpl.tech.master.domain.model.PaymentMode paymentMode = MasterDataConverter.convert(mode);
            paymentMode.setCommissionRate(mode.getCommissionRate());
            paymentMode.setId(mode.getPaymentModeId());
            paymentMode.setName(mode.getModeName());
            paymentMode.setDescription(mode.getModeDescription());
            paymentMode.setSettlementType(mode.getSettlementType());
            paymentMode.setGeneratePull(mode.isGeneratePull());
            paymentMode.setCommissionRate(mode.getCommissionRate());
            paymentMode.setStatus(mode.getModeStatus());
            paymentMode.setCategory(PaymentCategory.valueOf(mode.getModeCategory()));
            paymentMode.setEditable(mode.getEditable().equals(AppConstants.YES));
            paymentMode.setApplicableOnDiscountedOrders(mode.getApplicableOnDiscountedOrders().equals(AppConstants.YES));
            paymentMode.setAutoPullValidate(mode.getAutomaticPullValidate().equals(AppConstants.YES));
            paymentMode.setAutoTransfer(mode.getAutomaticTransfer().equals(AppConstants.YES));
            paymentMode.setAutoCloseTransfer(mode.getAutomaticCloseTransfer().equals(AppConstants.YES));
            paymentMode.setNeedsSettlementSlip(mode.getNeedsSettlementSlip().equals(AppConstants.YES));
            paymentMode.setValidationSource(mode.getValidationSource());
            paymentMode.setLedgerName(mode.getLedgerName());
            result.add(paymentMode);
        }
        return result;
    }

    @Override
    public Boolean updatePaymentMode(PaymentMode paymentMode) {
        try {
            manager.persist(paymentMode);
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error updating payment mode: id:{} :: name:{} :: Error: {}",
                    paymentMode.getPaymentModeId(), paymentMode.getModeName(), e);
        }
        return false;
    }

    @Override
    public PaymentMode getPaymentMethod(PaymentMode paymentMode){
        try {
            PaymentMode mode = manager.find(PaymentMode.class, paymentMode.getPaymentModeId());
            if (Objects.isNull(mode)) {
                Exception error = new Exception("DB Error. Object does not exists.");
                throw error;
            }
            return mode;
        } catch (Exception e) {
            LOG.error("Error getting payment mode: id:{} :: name:{} :: Error: {}",
                    paymentMode.getPaymentModeId(), paymentMode.getModeName(), e);
        }
        return null;
    }

    @Override
    public List<Denomination> getDenomination(PaymentMode mode){
        Query query = manager.createQuery("From Denomination where paymentMode = :paymentMode");
        query.setParameter("paymentMode",mode);
        return query.getResultList();
    }

    @Override
    public void updatePaymentModeMappingMethod(UnitPaymentModeMappingDetail modeMapping) {
        try {
            UnitDetail unitDetail = getUnitForPaymentMapping(modeMapping.getUnitDetail());
            PaymentMode mode = getPaymentMode(modeMapping.getPaymentMode());

            UnitPaymentModeMapping paymentModeMapping = new UnitPaymentModeMapping();
            if(Objects.nonNull(modeMapping.getUnitPaymentModeMappingId())){
               paymentModeMapping = find(UnitPaymentModeMapping.class,modeMapping.getUnitPaymentModeMappingId());
                paymentModeMapping.setMappingStatus(modeMapping.getMappingStatus());
            } else {
                paymentModeMapping.setPaymentMode(mode);
                paymentModeMapping.setUnitDetail(unitDetail);
                paymentModeMapping.setMappingStatus(modeMapping.getMappingStatus());
            }
            manager.persist(paymentModeMapping);
            manager.flush();
        } catch (Exception e) {
            LOG.error("Error updating payment mode mapping", e);
        }
    }

    private PaymentMode getPaymentMode(com.stpl.tech.master.domain.model.PaymentMode paymentMode) {
        return manager.find(PaymentMode.class,paymentMode.getId());
    }

    private UnitDetail getUnitForPaymentMapping(UnitBasicDetail unitDetail) {
        return manager.find(UnitDetail.class,unitDetail.getId());
    }

    @Override
    public List<UnitPaymentModeMappingDetail> getPaymentModeMapping(Integer id) {
        PaymentMode mode = manager.find(PaymentMode.class, id);
        Query query = manager.createQuery("From UnitPaymentModeMapping where paymentMode = :paymentMode");
        query.setParameter("paymentMode", mode);
        List<UnitPaymentModeMapping> res = query.getResultList();
        List<UnitPaymentModeMappingDetail> output = new ArrayList<>();
        for (UnitPaymentModeMapping modeMapping : res) {
            UnitPaymentModeMappingDetail mapping = new UnitPaymentModeMappingDetail();
            mapping.setPaymentMode(MasterDataConverter.convert(mode));
            mapping.setUnitPaymentModeMappingId(modeMapping.getUnitPaymentModeMappingId());
            mapping.setUnitDetail(masterDataCache.getUnitBasicDetail(modeMapping.getUnitDetail().getUnitId()));
            mapping.setMappingStatus(modeMapping.getMappingStatus());
            output.add(mapping);
        }
        return output;
    }
    @Override
    public boolean addPartnerEdcMapping(UnitToPartnerEdcMappingDetail unitToPartnerEdcMappingDetail){
        try{
            UnitToPartnerEdcMapping unitToPartnerEdcMapping=unitToPartnerEdcMappingDao.findByUnitIdAndPartnerNameAndTerminalId(unitToPartnerEdcMappingDetail.getUnitBasicDetail().getId(),unitToPartnerEdcMappingDetail.getPartnerName().toUpperCase().trim(),unitToPartnerEdcMappingDetail.getTerminalId());
            if(unitToPartnerEdcMapping==null)
            {
                unitToPartnerEdcMapping=new UnitToPartnerEdcMapping();
            }
            unitToPartnerEdcMapping.setUnitId(unitToPartnerEdcMappingDetail.getUnitBasicDetail().getId());
            unitToPartnerEdcMapping.setPartnerName(unitToPartnerEdcMappingDetail.getPartnerName().toUpperCase().trim());
            unitToPartnerEdcMapping.setStatus(unitToPartnerEdcMappingDetail.getEdcMappingStatus());
            unitToPartnerEdcMapping.setMerchantId(unitToPartnerEdcMappingDetail.getMerchantId());
            unitToPartnerEdcMapping.setMerchantKey(unitToPartnerEdcMappingDetail.getMerchantKey());
            unitToPartnerEdcMapping.setTerminalId(unitToPartnerEdcMappingDetail.getTerminalId());
            unitToPartnerEdcMapping.setTId(unitToPartnerEdcMappingDetail.getTid());
            unitToPartnerEdcMapping.setVersion(unitToPartnerEdcMappingDetail.getVersion());

            manager.persist(unitToPartnerEdcMapping);
            manager.flush();
            return true;
        }
        catch (Exception e)
        {
            LOG.error("Error adding partner Edc Mapping : ", e);
        }
        return false;
    }

    public void updatePartnerEdcMapping(UnitToPartnerEdcMappingDetail unitToPartnerEdcMappingDetail){
       try {
           Optional<UnitToPartnerEdcMapping> unitToPartnerEdcMapping= unitToPartnerEdcMappingDao.findById(unitToPartnerEdcMappingDetail.getId());

           if(unitToPartnerEdcMapping.isPresent() && Objects.nonNull(unitToPartnerEdcMappingDetail.getEdcMappingStatus())){
               unitToPartnerEdcMapping.get().setStatus(unitToPartnerEdcMappingDetail.getEdcMappingStatus());
               unitToPartnerEdcMappingDao.save(unitToPartnerEdcMapping.get());
           }
       }catch (Exception e){
           LOG.error("Error getting unit Mapping details for id ::{}",unitToPartnerEdcMappingDetail.getId());
       }
    }

    @Override
    public List<ApplicationVersionDetailData> getUnitVersionMapping(){
        try{
            Query query = manager.createQuery("FROM ApplicationVersionDetailData where versionStatus = :status");
            query.setParameter("status",AppConstants.ACTIVE);
            return query.getResultList();
        }catch (NoResultException exception){
            LOG.info("Error while getting ");
        }
        return null;
    }

    @Override
    public List<ApplicationVersionEvent> getUnitNewVersionMapping(Integer unitId, Integer terminal) {
        try {
            Query query = manager.createQuery("FROM ApplicationVersionEvent where unitId = :unitId and  status = :status");
            query.setParameter("unitId", unitId);
            query.setParameter("status", EventStatusType.INITIATED.name());
            return query.getResultList();
        } catch (Exception e) {
            LOG.info("Error While getting corresponding applications version against unitId: {}", unitId);
        }
        return null;
    }

//    @Override
//    public void updateUnitApplicationVersionStatus(Integer unitId, Integer terminalId, String currentStatus, String newStatus) {
//        try {
//            Query query = manager.createQuery("UPDATE ApplicationVersionEvent SET status = :newStatus  where unitId = :unitId and status =:currentStatus");
//            query.setParameter("unitId", unitId);
//            query.setParameter("currentStatus", currentStatus);
//            query.setParameter("newStatus", newStatus);
//            query.executeUpdate();
//        } catch (Exception e) {
//            LOG.info("Unable to update status of unitId {} from {} to {}", unitId, currentStatus, newStatus);
//        }
//    }
//
//    @Override
//    public void updateUnitApplicationVersionDetailStatus(Integer unitId, Integer terminalId, String applicationName) {
//        try {
//            Query query = manager.createQuery("UPDATE ApplicationVersionDetail SET status = :newStatus  where unitId = :unitId and status =:currentStatus");
//            query.setParameter("unitId", unitId);
//            query.setParameter("currentStatus", AppConstants.ACTIVE);
//            query.setParameter("newStatus", AppConstants.IN_ACTIVE);
//            query.executeUpdate();
//        } catch (Exception e) {
//            LOG.info("Unable to Inactive status of  {} for unitID to {}", applicationName, unitId, AppConstants.IN_ACTIVE);
//        }
//    }

    @Override
    public void addUnitApplicationVersionDetailStatus(Integer unitId, Integer terminalId, ApplicationVersionDetailDomainData applicationVersionDetailDomainData) {

            ApplicationVersionDetailData applicationVersionDetailData = new ApplicationVersionDetailData();
            applicationVersionDetailData.setApplicationName(applicationVersionDetailDomainData.getApplicationName());
            applicationVersionDetailData.setUnitId(unitId);
            applicationVersionDetailData.setApplicationVersion(applicationVersionDetailDomainData.getApplicationCurrentVersion());
            applicationVersionDetailData.setTerminal(terminalId);
            applicationVersionDetailData.setVersionStatus(AppConstants.ACTIVE);
            manager.persist(applicationVersionDetailData);

    }

    @Override
    public UnitDetail findById(Integer unitId) {
        try {
            UnitDetail detail = manager.find(UnitDetail.class,unitId);
            return detail;
        } catch (Exception e) {
            LOG.error("Unable to get unitdetail for unitId :{}",unitId);
        }
        return null;
    }

    @Override
    public Map<Integer, Map<Integer, UnitProductMapping>> getUnitProductMappingForUnits(List<Integer> unitIds) {
        Query query = manager.createQuery("FROM UnitProductMapping WHERE unitDetail.unitId IN (:unitIds)");
        query.setParameter("unitIds", unitIds);
        List<UnitProductMapping> mappings = query.getResultList();
        return mappings.stream()
                .collect(Collectors.groupingBy(
                        upm -> upm.getUnitDetail().getUnitId(),
                        Collectors.toMap(
                                upm -> upm.getProductDetail().getProductId(),
                                upm -> upm
                        )
                ));
    }

    @Override
    public List<UnitProductMapping> updateUnitProductMapping(List<UnitProductMapping> changedUnitProductMappings) {
        if(CollectionUtils.isEmpty(changedUnitProductMappings)) {
            return new ArrayList<>();
        }
        return addAll(changedUnitProductMappings);
    }

    @Override
    public void updateUnitProductPricings(List<UnitProductPricing> changedUpp) {
        if(CollectionUtils.isEmpty(changedUpp)) {
            return;
        }
        addAll(changedUpp);
    }

    @Override
    public void saveLog(UnitProductSyncLog unitProductSyncLog) {
        add(unitProductSyncLog);
    }

}
