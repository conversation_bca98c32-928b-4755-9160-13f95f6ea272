package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.activity.service.ActivityLoggerService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.IdGeneratorService;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.RecipeIterationService;
import com.stpl.tech.master.core.service.RecipeService;
import com.stpl.tech.master.data.dao.IterationIngredientInstructionsDao;
import com.stpl.tech.master.data.dao.IterationReviewDao;
import com.stpl.tech.master.data.dao.RecipeIterationDao;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.recipe.calculator.model.*;
import com.stpl.tech.master.recipe.model.*;
import com.stpl.tech.spring.exception.FileArchiveServiceException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class RecipeIterationServiceImpl implements RecipeIterationService {

	private static final Logger LOG = LoggerFactory.getLogger(RecipeIterationServiceImpl.class);

	@Autowired
	private IdGeneratorService idService;
	@Autowired
	private MasterDataCacheService cache;
	@Autowired
	private RecipeIterationDao recipeIterationDao;
	@Autowired
	private IterationReviewDao iterationReviewDao;
	@Autowired
	private RecipeService recipeService;
	@Autowired
	private IterationIngredientInstructionsDao iterationIngredientInstructionsDao;
	@Autowired
	private ActivityLoggerService loggerService;

	@Autowired
	private FileArchiveService fileArchiveService;
	@Autowired
	private MasterProperties masterProperties;

	@Autowired
	private MasterDataCache masterDataCache;

	@Override
	public RecipeIterationDetail addRecipeIteration(RecipeIterationDetail recipeIterationDetail) {
		recipeIterationDetail.setIterationId(idService.getNextId(RecipeIterationDetail.class));
		recipeIterationDetail.setCreationDate(AppUtils.getCurrentDate());
		// saveIngredientDetail(detail);
		RecipeIterationDetail data = recipeIterationDao.save(recipeIterationDetail);
		// cache.addRecipe(data);
		return data;
	}

	@Override
	public int updateRecipeIeration(RecipeIterationDetail recipeIterationDetail) {
		// saveIngredientDetail(detail);
		recipeIterationDetail.setModificationDate(AppUtils.getCurrentDate());
		recipeIterationDetail = addIterationIngredientInstructions(recipeIterationDetail);
		RecipeIterationDetail data = recipeIterationDao.save(recipeIterationDetail);
		// cache.addRecipe(data);
		return data.getIterationId();
	}

	public String saveSCMRecipeMedia(MultipartFile file) {
		String imageURL = "";
		try{
			String baseDir = "SCMRecipemedia";
			String fileName = file.getOriginalFilename();
			fileName = fileName.replaceAll(" ", "_").toLowerCase();
			System.out.println(fileName);
			FileDetail s3File = fileArchiveService.saveFileToS3(masterProperties.getS3RecipeMediaBucket(), baseDir, fileName,
					file, true);
			if(s3File != null){
				imageURL = masterProperties.getRecipeStepMediaHostUrl()+fileName;
				return imageURL;
			} else{
				return imageURL;
			}
		} catch (FileArchiveServiceException e){
			LOG.error("Encountered error while uploading Recipe Media Image to S3", e);
		}
		return imageURL;
	}
	@Override
	public boolean updateIterationImage(String id,MultipartFile [] file) throws DataNotFoundException {
		try {
			Optional<RecipeIterationDetail> recipeIterationDetail = recipeIterationDao.findById(id);
			if (recipeIterationDetail.isPresent()){
				List<String> imagesURL = new ArrayList<>();
				for (int i = 0; i < file.length; i++) {
					imagesURL.add(saveSCMRecipeMedia(file[i]));
				}
				recipeIterationDetail.get().setImagesURL(imagesURL);
				if (recipeIterationDetail.get().getStatus().equals(RecipeIterationStatus.APPROVED)) {
					RecipeDetail data = recipeService.findSCMRecipeProductByIdAndProfileAndStatus(recipeIterationDetail.get().getLinkedProductId(), recipeIterationDetail.get().getProfile());
					if (data != null) {
						data.setImagesURL(recipeIterationDetail.get().getImagesURL());
						recipeService.updateRecipeWithImage(data);
					}
					recipeIterationDetail.get().setModificationDate(AppUtils.getCurrentDate());
					recipeIterationDetail.get().setLastUpdatedById(recipeIterationDetail.get().getLastUpdatedById());
					recipeIterationDetail.get().setLastUpdatedByName(recipeIterationDetail.get().getLastUpdatedByName());
					recipeIterationDao.save(recipeIterationDetail.get());
					return true;
				}
			}
			return false;
		}catch ( DataNotFoundException e){
			LOG.error("Encountered error while uploading Recipe Media Image to S3", e);
		}
		return false;
	}

	@Override
	public List<RecipeIterationDetail> getIterationForProduct(int productId) { //List<String> profile
		List<RecipeIterationDetail> recipeIterationDetails = recipeIterationDao.getIterationForProduct(productId, RecipeIterationStatus.ARCHIVED);
		Product product = masterDataCache.getProduct(productId);
		recipeIterationDetails.forEach(recipeIterationDetail -> {
			if(recipeIterationDetail.getProfile() == null) {
				if(Objects.nonNull(product) && Objects.nonNull(product.getBrandId()) && product.getBrandId() == AppConstants.DOHFUL_BRAND_ID){
					recipeIterationDetail.setProfile("DF0");
				}
				else {
					recipeIterationDetail.setProfile("P0");
				}
			}
		});
		return recipeIterationDetails;
	}

	@Override
	public List<RecipeIterationDetail> getIterationForConstruct(String constructName) {
		return recipeIterationDao.getIterationForConstruct(constructName, RecipeIterationStatus.ARCHIVED);
	}

	@Override
	public boolean validateConstructName(String constructName) {
		List<RecipeIterationDetail> list = recipeIterationDao.getAllConstruct(constructName);
		if (list.size() > 0) {
			return false;
		}
		return true;
	}

	@Override
	public Map<String, Object> getAllSCMConstruct() {
		List<RecipeIterationDetail> list = recipeIterationDao.getAllSCMConstruct(null);
		Map<String, Object> constructMap = new HashMap<>();
		for (RecipeIterationDetail detail : list) {
			if (!constructMap.containsKey(detail.getLinkedConstructName())) {
				constructMap.put(detail.getLinkedConstructName(), detail);
			}
		}
		return constructMap;
	}

	@Override
	public boolean changeIterationStatus(RecipeIterationDetail recipeIterationDetail) throws DataNotFoundException, EmailGenerationException, IllegalAccessException {
		recipeIterationDetail.setModificationDate(AppUtils.getCurrentDate());
		recipeIterationDetail.getReview().setReviewDate(AppUtils.getCurrentDate());
		switch (recipeIterationDetail.getStatus()) {
		case RecipeIterationStatus.ARCHIVED:

			break;
		case RecipeIterationStatus.APPROVED:
			RecipeDetail recipeDetail = convert(recipeIterationDetail);

			List<RecipeIterationDetail> existingIterationDetail = recipeIterationDao
					.getApprovedIterationForProduct(recipeIterationDetail.getLinkedProductId(), RecipeIterationStatus.APPROVED);
			RecipeIterationDetail oldRecipe = new RecipeIterationDetail();
			if (existingIterationDetail.size() > 0) {
				for (RecipeIterationDetail detail : existingIterationDetail) {
					Product product = masterDataCache.getProduct(detail.getLinkedProductId());
					if(detail.getProfile().equalsIgnoreCase(recipeIterationDetail.getProfile()) &&
							detail.getStatus().equals(RecipeIterationStatus.APPROVED)){
						oldRecipe = detail;
					}
					if(detail.getProfile() == null) {
						if(Objects.nonNull(product) && Objects.nonNull(product.getBrandId()) && product.getBrandId() == AppConstants.DOHFUL_BRAND_ID){
							recipeIterationDetail.setProfile("DF0");
						}
						else {
							recipeIterationDetail.setProfile("P0");
						}
					}
					if(detail.getProfile().equalsIgnoreCase(recipeIterationDetail.getProfile())) {
						detail.setStatus(RecipeIterationStatus.DECOMISSONED);
						detail.setModificationDate(AppUtils.getCurrentDate());
						detail.setLastUpdatedById(recipeIterationDetail.getLastUpdatedById());
						detail.setLastUpdatedByName(recipeIterationDetail.getLastUpdatedByName());
						recipeIterationDao.save(detail);
					}
				}
			}
			try {
				LOG.info("Trying To Send Diff Email OF Recipe Change of {} and Profile : {}",recipeIterationDetail.getLinkedProductName(),
						recipeIterationDetail.getProfile());
				List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>"));
				loggerService.sendDiffEmail(oldRecipe,recipeIterationDetail,recipeIterationDetail.getLastUpdatedByName(),
						"Recipe" , recipeIterationDetail.getIterationId(),toEmails,"Recipe : " +
								recipeIterationDetail.getLinkedProductName() + "(" + recipeIterationDetail.getProfile() + ") " );
			}catch (Exception e){
				LOG.info("Error While Sending Diff Email Of Recipe Change.");
			}

			createRecipeForProduction(recipeDetail);

			break;
		case RecipeIterationStatus.IMPROVED:

			break;
		}
		recipeIterationDetail.getReview().setReviewId(idService.getNextId(IterationReview.class));
		iterationReviewDao.save(recipeIterationDetail.getReview());
		RecipeIterationDetail data = recipeIterationDao.save(recipeIterationDetail);
		if (data == null) {
			return false;
		}
		return true;
	}


	@Override
	public boolean changeIterationComment(RecipeIterationDetail recipeIterationDetail) throws DataNotFoundException {
		if(recipeIterationDetail.getStatus().equals(RecipeIterationStatus.APPROVED)){
			RecipeDetail data = recipeService.findSCMRecipeProductByIdAndProfileAndStatus(recipeIterationDetail.getLinkedProductId(), recipeIterationDetail.getProfile());
			if (data != null) {
				data.setNotes(recipeIterationDetail.getNotes());
				recipeService.updateRecipe(data);
			}
			recipeIterationDetail.setModificationDate(AppUtils.getCurrentDate());
			recipeIterationDetail.setLastUpdatedById(recipeIterationDetail.getLastUpdatedById());
			recipeIterationDetail.setLastUpdatedByName(recipeIterationDetail.getLastUpdatedByName());
			recipeIterationDao.save(recipeIterationDetail);
			return true;
		}
		return false;
	}

	private boolean createRecipeForProduction(RecipeDetail recipeDetail) throws DataNotFoundException {
		RecipeDetail existingRecipe = null;
//		RecipeDetail existingRecipe = recipeService.findSCMProductById(recipeDetail.getProduct().getProductId());
		List<RecipeDetail> existingRecipeList = recipeService.findSCMRecipeProductById(recipeDetail.getProduct().getProductId());
		for (RecipeDetail recipeDetail1 : existingRecipeList) {
			if (recipeDetail1.getProfile().equals(recipeDetail.getProfile())) {
				existingRecipe = recipeDetail1;
			}
		}
		if (existingRecipe != null) {
			recipeDetail.set_id(existingRecipe.get_id());
			recipeDetail.setRecipeId(existingRecipe.getRecipeId());
			recipeService.updateRecipe(recipeDetail);
		} else {
			recipeService.addRecipe(recipeDetail);
		}
		return true;
	}

	private RecipeDetail convert(RecipeIterationDetail recipeIterationDetail) {
		RecipeDetail recipeDetail = new RecipeDetail();

		BasicInfo dimension = new BasicInfo();
		// dimension.setInfoId(4);

		dimension.setCode(recipeIterationDetail.getProductUom().getUom().name());
		dimension.setName(recipeIterationDetail.getProductUom().getUom().name());

		recipeDetail.setDimension(dimension);
		String recipeName = recipeIterationDetail.getLinkedProductName();
        if (recipeDetail.getDimension().getName() != null) {
        	recipeName = recipeName + " "+ recipeDetail.getDimension().getName();
        }
        recipeName = recipeName + " Recipe";
		recipeDetail.setName(recipeName);
		recipeDetail.setLastUpdatedById(recipeIterationDetail.getLastUpdatedById());
		recipeDetail.setLastUpdatedByName(recipeIterationDetail.getLastUpdatedByName());
		recipeDetail.setCreationDate(recipeIterationDetail.getCreationDate());
		recipeDetail.setStatus(RecipeIterationStatus.ACTIVE);
		recipeDetail.setModificationDate(recipeIterationDetail.getModificationDate());
		recipeDetail.setNotes(recipeIterationDetail.getNotes());
		recipeDetail.setProfile(recipeIterationDetail.getProfile());

		ProductData linkedProductData = new ProductData();
		linkedProductData.setProductId(recipeIterationDetail.getLinkedProductId());
		linkedProductData.setName(recipeIterationDetail.getLinkedProductName());
		linkedProductData.setClassification(ProductClassification.SCM_PRODUCT);
		recipeDetail.setProduct(linkedProductData);

		List<IngredientProductDetail> ingredientComponentList = new ArrayList<>();
		for (IterationIngredientDetail detail : recipeIterationDetail.getComponents()) {
			IngredientProductDetail ingredientProduct = new IngredientProductDetail();
			ingredientProduct.setCritical(true);
			ingredientProduct.setCustomize(false);
			ingredientProduct.setShowRecipe(detail.getShowRecipe());

			/**
			 * converting inputs of ingredients to subuom to uom
			 * gm -kg
			 * pc-pc
			 * ml - kl
			 */
			if(detail.getUom().getRatio() == recipeIterationDetail.getProductUom().getRatio() ){
				/**
				 * gm-gm
				 * pc-pc
				 */
				ingredientProduct.setQuantity(detail.getQuantityPerSubUom());
			}else if(recipeIterationDetail.getProductUom().getRatio() == 1000){
				/**
				 * Product gm -  Ingredient pc
				 */
				ingredientProduct.setQuantity(
						AppUtils.multiplyWithScale10(detail.getQuantityPerSubUom(), new BigDecimal(1000)));

			}else if( recipeIterationDetail.getProductUom().getRatio() == 1){
				/**
				 *Product pc -  Ingeredient gm
				 */
				ingredientProduct.setQuantity(
						AppUtils.divideWithScale10(detail.getQuantityPerSubUom(), new BigDecimal(1000)));
			}
			ingredientProduct.setUom(detail.getUom().getUom());
			ingredientProduct.setYield(detail.getYieldPercentage());
			if(detail.getInstructions() != null){
				ingredientProduct.setInstructions(detail.getInstructions());
			}
			ProductData data = new ProductData();
			data.setProductId(detail.getProductId());
			data.setName(detail.getProductName());
			data.setType(detail.getType());
			data.setSubType(detail.getSubType());
			data.setAutoProduction(detail.getAutoProduction()==null?false:detail.getAutoProduction());
			if(detail.getAutoProduction() != null && detail.getAutoProduction() == true){
				//TODO rahul set recipe id of the item
				//TODO set at the time of view of recipe data
			}
			ingredientProduct.setProduct(data);
			ingredientComponentList.add(ingredientProduct);
		}

		IngredientDetail ingredient = new IngredientDetail();
		ingredient.setComponents(ingredientComponentList);
		recipeDetail.setIngredient(ingredient);

		return recipeDetail;
	}

	@Override
	public boolean validateIterationName(String iterationName) {
		List<RecipeIterationDetail> list = recipeIterationDao.getAllIteration(iterationName);
		if (list.size() > 0) {
			return false;
		}
		return true;
	}

	@Override
	public List<RecipeIterationDetail> getArchivedIterationForConstruct(String constructName) {
		return recipeIterationDao.getArchivedIterationForConstruct(constructName, RecipeIterationStatus.ARCHIVED);
	}

	@Override
	public List<RecipeIterationDetail> getArchivedIterationForProduct(int productId) {
		return recipeIterationDao.getArchivedIterationForProduct(productId, RecipeIterationStatus.ARCHIVED);
	}

	@Override
	public boolean addIngredientInstructions(Set<String> instructions) {
		for(String s : instructions){
			IterationIngredientInstructions iterationIngredientInstructions = new IterationIngredientInstructions();
			iterationIngredientInstructions.setInstruction(s);
			iterationIngredientInstructionsDao.save(iterationIngredientInstructions);
		}
		return true;
	}

	@Override
	public List<IterationIngredientInstructions> getAllIngredientInstructions() {
		return iterationIngredientInstructionsDao.findAll();
	}

	private RecipeIterationDetail addIterationIngredientInstructions(RecipeIterationDetail recipeIterationDetail){
		List<IterationIngredientDetail> iterationIngredientDetails = new ArrayList<>();
		for(IterationIngredientDetail iterationIngredientDetail : recipeIterationDetail.getComponents()){
			List<IterationIngredientInstructions> iterationIngredientInstructionsList = new ArrayList<>();
			if(iterationIngredientDetail.getInstructions() != null){
				for(IterationIngredientInstructions iterationIngredientInstructions : iterationIngredientDetail.getInstructions()){
					IterationIngredientInstructions instruction = iterationIngredientInstructionsDao.findByInstruction(iterationIngredientInstructions.getInstruction());
					if(instruction == null){
						iterationIngredientInstructions = iterationIngredientInstructionsDao.save(iterationIngredientInstructions);
					}else {
						iterationIngredientInstructions = instruction;
					}
					iterationIngredientInstructionsList.add(iterationIngredientInstructions);
				}
			}
			iterationIngredientDetail.setInstructions(iterationIngredientInstructionsList);
			iterationIngredientDetails.add(iterationIngredientDetail);
		}
		recipeIterationDetail.getComponents().clear();
		recipeIterationDetail.getComponents().addAll(iterationIngredientDetails);
		return recipeIterationDetail;
	}

	@Override
	public List<RecipeIterationDetail> getAllApprovedIterationForProducts(List<Integer> productIds) {
		return recipeIterationDao.getAllApprovedIterationForProducts(productIds, RecipeIterationStatus.APPROVED);
	}

	@Override
	public String checkRecipeForSmProducts(List<Integer> productIds) {
		try {
			StringBuilder stringBuilder = new StringBuilder();
			Map<Integer, Boolean> recipeFoundStatus = productIds.stream()
					.collect(Collectors.toMap(
							productId -> productId,
							productId -> Boolean.FALSE,
							(existing, replacement) -> existing
					));
			List<RecipeIterationDetail> recipeIterationDetails = getAllApprovedIterationForProducts(productIds);
			if (Objects.nonNull(recipeIterationDetails) && !recipeIterationDetails.isEmpty()) {
				for (RecipeIterationDetail recipeIterationDetail : recipeIterationDetails) {
					if (recipeFoundStatus.containsKey(recipeIterationDetail.getLinkedProductId()) && !recipeFoundStatus.get(recipeIterationDetail.getLinkedProductId())) {
						recipeFoundStatus.put(recipeIterationDetail.getLinkedProductId(), Boolean.TRUE);
					}
				}
				Set<Integer> notFoundProducts = new HashSet<>();
				for (Map.Entry<Integer, Boolean> entry : recipeFoundStatus.entrySet()) {
					if (entry.getValue().equals(Boolean.FALSE)) {
						notFoundProducts.add(entry.getKey());
					}
				}
				if (!notFoundProducts.isEmpty()) {
					stringBuilder.append("RECIPES_NOT_FOUND#");
					for (Integer productId : notFoundProducts) {
						stringBuilder.append(productId).append(",");
					}
				} else {
					stringBuilder.append("ALL_RECIPES_FOUND");
				}
			} else {
				stringBuilder.append("RECIPES_NOT_FOUND#");
				for (Map.Entry<Integer, Boolean> entry : recipeFoundStatus.entrySet()) {
					stringBuilder.append(entry.getKey()).append(",");
				}
			}
			return stringBuilder.toString();
		} catch (Exception e) {
			LOG.error("Exception Occurred while Checking Recipe For Semi finished Products ::: ", e);
			return "ERROR";
		}
	}
}
