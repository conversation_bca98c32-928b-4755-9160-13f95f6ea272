/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

// Generated 20 Jul, 2015 5:25:16 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

/**
 * EmployeeUnitMapping generated by hbm2java
 */
@Entity
@Table(name = "RIDER_UNIT_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = { "EMPLOYEE_ID", "UNIT_ID",
		"MAPPING_STATUS" }))
public class RiderUnitMapping implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5072425341480276548L;
	
	private Integer riderUnitKeyId;
	private int employeeId;
	private int unitId;
	private String mappingStatus;
	private Date lastUpdateTime;

	public RiderUnitMapping() {
	}

	public RiderUnitMapping(int employeeId, int unitId, String mappingStatus,
			Date lastUpdateTime) {
		super();
		this.employeeId = employeeId;
		this.unitId = unitId;
		this.mappingStatus = mappingStatus;
		this.lastUpdateTime = lastUpdateTime;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "RIDER_UNIT_MAPPING_ID", unique = true, nullable = false)
	public Integer getRiderUnitKeyId() {
		return this.riderUnitKeyId;
	}

	public void setRiderUnitKeyId(Integer empUnitKeyId) {
		this.riderUnitKeyId = empUnitKeyId;
	}
	
	@Column(name = "EMPLOYEE_ID", nullable = false)
	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}
	
	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return this.mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
	public Date getLastUpdateTime() {
		return this.lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
