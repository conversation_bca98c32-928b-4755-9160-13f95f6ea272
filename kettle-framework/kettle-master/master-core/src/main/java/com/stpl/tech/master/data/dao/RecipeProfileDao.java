package com.stpl.tech.master.data.dao;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.stpl.tech.master.recipe.model.RecipeProfile;

public interface RecipeProfileDao extends MongoRepository<RecipeProfile, String>{

	@Query("{'type' : { $regex : ?0 ,  '$options' : 'i' }}")
	List<RecipeProfile> getRecipeProfiles(String type);
}
