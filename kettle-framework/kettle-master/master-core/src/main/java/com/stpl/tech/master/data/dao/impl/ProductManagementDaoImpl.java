/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.core.NamedQueryDefinition;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.activity.service.ActivityLoggerService;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.ProductManagementDao;
import com.stpl.tech.master.data.dao.ProductTagMappingDetailDao;
import com.stpl.tech.master.data.model.ProductCityImageMapping;
import com.stpl.tech.master.data.model.ProductDescription;
import com.stpl.tech.master.data.model.ProductDetail;
import com.stpl.tech.master.data.model.ProductDimensionImageMapping;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.data.model.ProductNutritionDetail;
import com.stpl.tech.master.data.model.ProductTagMappingDetail;
import com.stpl.tech.master.data.model.RefLookup;
import com.stpl.tech.master.data.model.RefLookupType;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.data.model.UnitProductPriceCategoryDetail;
import com.stpl.tech.master.data.model.UnitProductPricing;
import com.stpl.tech.master.data.model.UnitProductProfileContext;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdCodeNameValue;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitProductMappingData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
public class ProductManagementDaoImpl extends AbstractMasterDaoImpl implements ProductManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(ProductManagementDaoImpl.class);

    @Autowired
    private ActivityLoggerService activityLoggerService;

    @Autowired
    private ProductTagMappingDetailDao tagMappingDetailDao;

    @Override
    public Product getProduct(int productId) {
        ProductDetail productDetail = manager.find(ProductDetail.class, productId);
        return MasterDataConverter.convert(productDetail);
    }

    @Override
    public Product addProduct(Product product) throws DataUpdationException {
        if (productExist(product.getName())) {
            throw new DataUpdationException("Cannot create a product with name " + product.getName()
                + " as another product with same name already exists");
        }

        ProductDetail productDetail = new ProductDetail();
        setProductDetail(product, productDetail);
        manager.persist(productDetail);
        addProductTagMapping(product);
        manager.flush();
        return MasterDataConverter.convert(productDetail);
    }

    @Override
    public Product updateProduct(Product product) {
        ProductDetail productDetail = manager.find(ProductDetail.class, product.getId());
        Product oldProduct = getProductClone(productDetail);
        setProductDetail(product, productDetail);
        manager.flush();
        updateProductTagMapping(product);
        if(Objects.nonNull(oldProduct) && Objects.nonNull(product)){
            sendProductUpdateEmail(oldProduct,product);
        }
        return MasterDataConverter.convert(productDetail);
    }

    @Override
    public boolean changeProductStatus(int productId, ProductStatus status) {
        try {
            ProductDetail productDetail = manager.find(ProductDetail.class, productId);
            productDetail.setProductStatus(status.name());
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("ERROR while changing product status for product productId {}", productId, e);
        }
        return false;
    }

    private boolean productExist(String productName) {
        Query query = manager.createQuery("FROM ProductDetail where upper(productName) = :productName");
        query.setParameter("productName", productName.toUpperCase());
        try {
            query.getSingleResult();
        } catch (NoResultException e) {
            return false;
        }
        return true;
    }

    private void setProductDetail(Product product, ProductDetail productDetail) {
        productDetail.setProductName(product.getName());
        productDetail.setClassification(product.getClassification().name());
        productDetail.setShortCode(product.getShortCode());
        productDetail.setProductDescription(product.getDescription());
        productDetail.setAttribute(product.getAttribute());
        productDetail.setAddonTypes(getRefLookUpType(product.getAddOnProfile()));
        productDetail.setProductType(getRefLookUptype(product.getType()));
        productDetail.setProductSubType(getRefLookUp(product.getSubType()));
        productDetail.setStationCategory((getRefLookUp(product.getStationCategory())));
        if (product.getWebType() != null && product.getWebType() > 0) {
            productDetail.setWebType(getRefLookUp(product.getWebType()));
        } else {
            productDetail.setWebType(null);
        }
        productDetail.setIsInventoryTracked(product.isInventoryTracked() ? AppConstants.YES : AppConstants.NO);
        productDetail.setEmployeeMealComponent(AppConstants.getValue(product.isEmployeeMealComponent()));
        productDetail.setTaxableCogs(AppConstants.getValue(product.isTaxableCogs()));
        productDetail.setPriceType(product.getBillType().name());
        productDetail.setProductSkuCode(product.getSkuCode());
        productDetail.setProductStatus(product.getStatus().name());
        productDetail.setDimensionCode(getRefLookUptype(product.getDimensionProfileId()));
        productDetail.setProductStartDate(product.getStartDate());
        productDetail.setProductEndDate(product.getEndDate());
        productDetail.setInTmstmp(AppUtils.getCurrentTimestamp());
        productDetail.setOutTmstmp(AppUtils.getMysqlMaxTimestamp());
        productDetail.setSupportsVariantLevelOrdering(
            product.isSupportsVariantLevelOrdering() ? AppConstants.YES : AppConstants.NO);
        productDetail.setTaxCode(product.getTaxCode());
        if (product.getType() == 5) {
            productDetail.setPreparationMode(product.getPreparation());
        }
        productDetail.setPrepTime(product.getPrepTime());
        productDetail.setBrandId(product.getBrandId());
        productDetail.setUpdatedBy(product.getUpdatedBy());
        productDetail.setLastUpdateTime(AppUtils.getCurrentTimestamp());
        if(Objects.nonNull(product.getInventoryTrackedLevel())){
            productDetail.setInventoryTrackedLevel(product.getInventoryTrackedLevel());
        }
    }

    @Override
    public RefLookup getRefLookUp(int refLookUpId) {
        return manager.find(RefLookup.class, refLookUpId);
    }

    private RefLookupType getRefLookUptype(int refLookUpTypeId) {
        return manager.find(RefLookupType.class, refLookUpTypeId);
    }

    private RefLookupType getRefLookUpType(String code) throws NoResultException {
        Query query = manager.createQuery("FROM RefLookupType E where E.rtlCode = :code");
        query.setParameter("code", code);
        Object o = query.getSingleResult();
        return o == null ? null : (RefLookupType) query.getSingleResult();
    }

    public boolean addUnitProductMapping(int unitId, int productId, Date startDate, Date endDate,
                                         ProductStatus status) {
        try {
            ProductDetail productDetail = manager.find(ProductDetail.class, productId);
            UnitDetail unitDetail = manager.find(UnitDetail.class, unitId);
            UnitProductMapping data = new UnitProductMapping();
            data.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
            data.setProductDetail(productDetail);
            data.setUnitDetail(unitDetail);
            data.setProductStartDate(startDate);
            data.setProductEndDate(endDate);

            manager.persist(data);
            manager.flush();
        } catch (Exception e) {
            LOG.error("Error while adding Unit Product mapping for Unit {} and Product {}", unitId, productId, e);
        }
        return false;
    }

    public boolean addUnitProductPriceMapping(int unitId, int productId) {
        return false;
    }

    @Override
    public List<UnitProductMappingData> getUnitProductPrice(UnitCategory unitCategory, List<String> unitRegions, int productId,
                                                            int dimensionId, Set<String> priceProfiles) {
        List<UnitDetail> unitDetailList = getUnitForSelectedRegion(unitCategory, unitRegions);
        if(priceProfiles!=null && priceProfiles.size()>0){
            List<UnitDetail> profileFilteredUnitDetailList = new ArrayList<>();
            for(int i=0; i<unitDetailList.size(); i++){
                UnitDetail unitDetail = unitDetailList.get(i);
                if(unitDetail.getPricingProfile() != null && priceProfiles.contains(Integer.toString(unitDetail.getPricingProfile()))){
                    profileFilteredUnitDetailList.add(unitDetail);
                }
            }
            unitDetailList = profileFilteredUnitDetailList;
        }
        if(unitDetailList.isEmpty()){
            return null;
        }
        ProductDetail productDetail = manager.find(ProductDetail.class, productId);
        List<UnitProductMappingData> list = new ArrayList<>();
        Query query = manager.createQuery(
            "FROM UnitProductMapping M WHERE M.unitDetail IN :unitDetailList AND M.productDetail = :productDetail");
        query.setParameter("unitDetailList", unitDetailList);
        query.setParameter("productDetail", productDetail);
        @SuppressWarnings("unchecked")
        List<UnitProductMapping> result = query.getResultList();
        result.forEach(mapping -> list.add(MasterDataConverter.convert(mapping, dimensionId)));
        if (unitDetailList.size() > result.size()) {
            addDummyUnitProductMapppingData(unitDetailList, result, list, productDetail, getRefLookUp(dimensionId));
        }
        return list;
    }

    @Override
    public List<UnitProductMappingData> getUnitProductPrice(UnitCategory unitCategory, String unitRegion,
                                                            int productId, int dimensionId) {

        List<UnitDetail> unitDetailList = getUnits(unitCategory, unitRegion);
        ProductDetail productDetail = manager.find(ProductDetail.class, productId);
        List<UnitProductMappingData> list = new ArrayList<>();
        Query query = manager.createQuery(
            "FROM UnitProductMapping M WHERE M.unitDetail IN :unitDetailList AND M.productDetail = :productDetail");
        query.setParameter("unitDetailList", unitDetailList);
        query.setParameter("productDetail", productDetail);
        @SuppressWarnings("unchecked")
        List<UnitProductMapping> result = query.getResultList();
        result.forEach(mapping -> list.add(MasterDataConverter.convert(mapping, dimensionId)));
        if (unitDetailList.size() > result.size()) {
            addDummyUnitProductMapppingData(unitDetailList, result, list, productDetail, getRefLookUp(dimensionId));
        }
        return list;
    }

    @Override
    public Map<Integer, Map<Integer, List<ProductRecipeKey>>> getUnitProductPriceProfiles() {
        Query query = manager.createQuery("FROM UnitProductMapping M WHERE M.productStatus = :pStatus");
        query.setParameter("pStatus", AppConstants.ACTIVE);
        @SuppressWarnings("unchecked")
        List<UnitProductMapping> result = query.getResultList();
        Map<Integer, Map<Integer, List<ProductRecipeKey>>> productRecipeKeys = new HashMap<>();
        result.forEach(unitProductMapping -> {
            int unitId = unitProductMapping.getUnitDetail().getUnitId();
            if (!productRecipeKeys.containsKey(unitProductMapping.getUnitDetail().getUnitId())) {
                productRecipeKeys.put(unitId, new HashMap<>());
            }
            unitProductMapping.getUnitProductPricings().forEach(unitProductPricing -> {
                int productId = unitProductMapping.getProductDetail().getProductId();
                if(!productRecipeKeys.get(unitId).containsKey(productId)) {
                    productRecipeKeys.get(unitId).put(productId, new ArrayList<>());
                }
                productRecipeKeys.get(unitId).get(productId).add(new ProductRecipeKey(unitProductMapping.getProductDetail().getProductId(),
                    unitProductPricing.getRefLookup().getRlCode(), unitProductPricing.getRecipeProfile()));
            });
        });
        return productRecipeKeys;
    }

    private void addDummyUnitProductMapppingData(List<UnitDetail> unitDetailList, List<UnitProductMapping> mappings,
                                                 List<UnitProductMappingData> list, ProductDetail productDetail, RefLookup dimention) {
        Map<Integer, UnitDetail> unitMap = new HashMap<>();
        unitDetailList.forEach(data -> unitMap.put(data.getUnitId(), data));
        for (UnitProductMapping mapping : mappings) {
            if (unitMap.containsKey(mapping.getUnitDetail().getUnitId())) {
                unitMap.remove(mapping.getUnitDetail().getUnitId());
            }
        }
        UnitProductMappingData mappingData = null;
        for (Integer unitId : unitMap.keySet()) {
            mappingData = new UnitProductMappingData();
            mappingData.setUnit(MasterDataConverter.convertToIdCodeName(unitMap.get(unitId)));
            mappingData.setProduct(MasterDataConverter.convertToIdCodeName(productDetail));
            mappingData.setPrice(new ProductPrice());
            mappingData.getPrice().setDimension(dimention.getRlCode());
            list.add(mappingData);
        }
    }

    @Override
    public boolean updateUnitProductPrice(List<UnitProductMappingData> mappings, Integer updatedBy) {
        try {
            for (UnitProductMappingData data : mappings) {
                if (data.getId() > 0) {
                    updateUnitProductPrice(data, updatedBy);
                } else {
                    addUnitProductMapping(data, updatedBy);
                }
            }
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error while saving Unit Product Price", e);
        }
        return false;
    }

    private void addUnitProductMapping(UnitProductMappingData data, Integer updatedBy) {
        ProductDetail productDetail = manager.find(ProductDetail.class, data.getProduct().getId());
        UnitProductMapping mapping = new UnitProductMapping();
        mapping.setProductDetail(productDetail);
        mapping.setUnitDetail(manager.find(UnitDetail.class, data.getUnit().getId()));
        mapping.setProductStartDate(productDetail.getProductStartDate());
        mapping.setProductEndDate(productDetail.getProductEndDate());
        mapping.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
        mapping.setProductStatus(productDetail.getProductStatus());
        manager.persist(mapping);
        addUnitProductPricing(mapping, data.getPrice(), updatedBy);
        // manager.flush();
    }

    private void addUnitProductPricing(UnitProductMapping mapping, ProductPrice price, Integer updatedBy) {
        UnitProductPricing entity = new UnitProductPricing();
        entity.setUnitProductMapping(mapping);
        entity.setRefLookup(getRefLookUp(price.getDimension(), mapping.getProductDetail().getDimensionCode()));
        entity.setPrice(price.getPrice());
        entity.setCost(price.getCost());
        entity.setCodCost(price.getCodCost());
        entity.setBuffer(price.getBuffer());
        entity.setThreshold(price.getThreshold());
        entity.setRecipeProfile(price.getProfile());
        entity.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
        entity.setStatus(price.getStatus());
        entity.setUpdatedBy(updatedBy);
        entity.setAliasProductName(price.getAliasProductName());
        entity.setDimensionDescriptor(price.getDimensionDescriptor());
        entity.setIsDeliveryOnlyProduct(AppUtils.setStatus(price.getIsDeliveryOnlyProduct()));
        entity.setPickDineInConsumables(AppUtils.setStatus(price.getPickDineInConsumables()));
        manager.persist(entity);
    }

    private RefLookup getRefLookUp(String dimension, RefLookupType refLookupType) {
        Query query = manager
            .createQuery("FROM RefLookup R WHERE R.rlCode = :dimension AND R.refLookupType = :refLookupType");
        query.setParameter("dimension", dimension);
        query.setParameter("refLookupType", refLookupType);
        return (RefLookup) query.getSingleResult();
    }

    private void updateUnitProductPrice(UnitProductMappingData data, Integer updatedBy) {
        UnitProductPricing entity = manager.find(UnitProductPricing.class, data.getPrice().getId());
        if (entity != null) {
            entity.setPrice(data.getPrice().getPrice());
            entity.setCost(data.getPrice().getCost());
            entity.setCodCost(data.getPrice().getCodCost());
            entity.setBuffer(data.getPrice().getBuffer());
            entity.setThreshold(data.getPrice().getThreshold());
            entity.setRecipeProfile(data.getPrice().getProfile());
            entity.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
            entity.setStatus(data.getPrice().getStatus());


            entity.setUpdatedBy(updatedBy);
            entity.setAliasProductName(data.getPrice().getAliasProductName());
            entity.setDimensionDescriptor(data.getPrice().getDimensionDescriptor());
            entity.setIsDeliveryOnlyProduct(AppUtils.setStatus(data.getPrice().getIsDeliveryOnlyProduct()));
            entity.setPickDineInConsumables(AppUtils.setStatus(data.getPrice().getPickDineInConsumables()));
        } else {
            UnitProductMapping mapping = manager.find(UnitProductMapping.class, data.getId());
            addUnitProductPricing(mapping, data.getPrice(), updatedBy);
        }
        // manager.flush();
    }

    @Override
    public List<IdCodeName> getUnitDetailsException(UnitCategory unitCategory, String unitRegion) {
        List<UnitDetail> unitDetails = getUnits(unitCategory, unitRegion);
        List<IdCodeName> nameList = new ArrayList<>();
        for (UnitDetail detail : unitDetails) {
            nameList.add(new IdCodeName(detail.getUnitId(), detail.getUnitName(), detail.getUnitRegion()));
        }
        return nameList;
    }

    @Override
    public List<Integer> findDistinctMenuToSCMRecipeId() {
        Query query=manager.createQuery("Select distinct mts.recipeId From MenuToSCMProductMapData mts");
        List<Integer> distinctRecipeId=query.getResultList();
        return distinctRecipeId;
    }

    @Override
    public List<ProductNutritionDetail> findNutritionForProductIds(List<Integer> list) {
        LOG.info("Fetching nutrition detail for product ids {}", list);
        Query query = manager.createQuery("FROM ProductNutritionDetail pnd where pnd.productId IN(:productIds)");
        query.setParameter("productIds",list);
        List<ProductNutritionDetail> nutritionDetails = query.getResultList();
        return nutritionDetails;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Object[]> checkForRecipeProfiles(Integer productId, Integer dimensionId, String profile) {
        List<Object[]> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT upp.unitProductMapping.unitDetail.unitId, " +
                    "upp.unitProductMapping.unitDetail.unitName, " +
                    "upp.status, upp.unitProductMapping.unitDetail.unitStatus " +
                    "FROM UnitProductPricing upp " +
                    "WHERE upp.unitProductMapping.productDetail.productId =:productId AND upp.refLookup.rlId =:dimensionId AND upp.recipeProfile =:profile ");
            query.setParameter("productId",productId);
            query.setParameter("dimensionId",dimensionId);
            query.setParameter("profile",profile);
            result = (List<Object[]>)query.getResultList();
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while checking for recipe inactivation in database :: ",e);
        }
        return result;
    }

    @Override
    public void truncateMenuToScmTable() {
        try {
            Query query = manager.createNativeQuery("TRUNCATE MENU_TO_SCM_PRODUCT_MAP");
            query.executeUpdate();
        } catch (Exception e) {
            LOG.error("Exception Occurred While Truncating the table of menu to scm ::: ",e);
        }
    }

    @Override
    public Map<Integer, List<ProductRecipeKey>> getProductPriceProfilesForUnit(int unitId) {
        Query query = manager.createQuery("FROM UnitProductMapping M WHERE M.unitDetail.unitId = :unitId and M.productStatus = :pStatus");
        query.setParameter("unitId", unitId);
        query.setParameter("pStatus", AppConstants.ACTIVE);
        @SuppressWarnings("unchecked")
        List<UnitProductMapping> result = query.getResultList();
        Map<Integer, List<ProductRecipeKey>> productRecipeKeys = new HashMap<>();
        if(result.isEmpty()){
            return productRecipeKeys;
        }
        result.forEach(unitProductMapping -> {
            unitProductMapping.getUnitProductPricings().forEach(unitProductPricing -> {
                int productId = unitProductMapping.getProductDetail().getProductId();
                if(!productRecipeKeys.containsKey(productId)) {
                    productRecipeKeys.put(productId, new ArrayList<>());
                }
                productRecipeKeys.get(productId).add(new ProductRecipeKey(unitProductMapping.getProductDetail().getProductId(),
                        unitProductPricing.getRefLookup().getRlCode(), unitProductPricing.getRecipeProfile()));
            });
        });
        return productRecipeKeys;
    }

    @SuppressWarnings("unchecked")
    private List<UnitDetail> getUnits(UnitCategory unitCategory, String unitRegion) {
        List<UnitDetail> unitList = new ArrayList<>();
        Query query = getUnitQuery(unitCategory == null ? null : unitCategory.name(),
            unitRegion == null ? null : unitRegion);
        if (query != null) {
            unitList = query.getResultList();
        }
        return unitList;
    }


    @Override
    public List<UnitDetail> getUnitForSelectedRegion(UnitCategory unitCategory, List<String> unitRegion) {
        List<UnitDetail> unitList = new ArrayList<>();
        Query query = getUnitQueryForSelectedRegion(unitCategory == null ? null : unitCategory.name(),
            unitRegion == null ? null : unitRegion);
        if (query != null) {
            unitList = query.getResultList();
        }
        return unitList;
    }

    @Override
    public List<UnitProductMappingData> getUnitProductPrice(int productId, int dimensionId, Set<Integer> unitIds, ProductDetail productDetail) {

        StringBuilder queryString = new StringBuilder("FROM UnitProductMapping M WHERE M.productDetail = :productDetail ");

        if(!CollectionUtils.isEmpty(unitIds)) {
            queryString.append(" AND M.unitDetail.unitId IN (:unitIds) ");
        }

        Query query = manager.createQuery(queryString.toString());

        if(!CollectionUtils.isEmpty(unitIds)) {
            query.setParameter("unitIds", unitIds);
        }

        query.setParameter("productDetail", productDetail);
        List<UnitProductMapping> result = query.getResultList();

        List<UnitProductMappingData> list = new ArrayList<>();
        result.forEach(mapping -> list.add(MasterDataConverter.convert(mapping, dimensionId)));

        return list;
    }

    private Query getUnitQueryForSelectedRegion(String unitCategory, List<String> unitRegion) {
        Query query = null;
        if (unitCategory == null && unitRegion == null) {
            query = manager.createQuery("FROM UnitDetail U");
        } else if (unitCategory != null && unitRegion == null) {
            query = manager.createQuery("FROM UnitDetail U WHERE U.unitCategory = :unitCategory");
            query.setParameter("unitCategory", unitCategory);
        } else if (unitCategory == null && unitRegion != null) {
            query = manager.createQuery("FROM UnitDetail U WHERE U.unitRegion IN :unitRegion");
            query.setParameter("unitRegion", unitRegion);
        } else {
            query = manager.createQuery(
                "FROM UnitDetail U WHERE U.unitCategory = :unitCategory AND U.unitRegion IN :unitRegion");
            query.setParameter("unitCategory", unitCategory);
            query.setParameter("unitRegion", unitRegion);
        }
        return query;
    }

    private Query getUnitQuery(String unitCategory, String unitRegion) {
        Query query = null;
        if (unitCategory == null && unitRegion == null) {
            query = manager.createQuery("FROM UnitDetail U");
        } else if (unitCategory != null && unitRegion == null) {
            query = manager.createQuery("FROM UnitDetail U WHERE U.unitCategory = :unitCategory");
            query.setParameter("unitCategory", unitCategory);
        } else if (unitCategory == null && unitRegion != null) {
            query = manager.createQuery("FROM UnitDetail U WHERE U.unitRegion = :unitRegion");
            query.setParameter("unitRegion", unitRegion);
        } else {
            query = manager.createQuery(
                "FROM UnitDetail U WHERE U.unitCategory = :unitCategory AND U.unitRegion = :unitRegion");
            query.setParameter("unitCategory", unitCategory);
            query.setParameter("unitRegion", unitRegion);
        }
        return query;
    }

    @Override
    public boolean updateUnitProductPrice(int unitId, int productId, String dimensionCode, BigDecimal price, String profile) {
        try {
            UnitProductMapping mapping = getUnitProductMapping(unitId, productId);
            for (UnitProductPricing pricing : mapping.getUnitProductPricings()) {
                if (pricing.getRefLookup().getRlCode().equals(dimensionCode)) {
                    pricing.setPrice(price);
                    pricing.setRecipeProfile(profile);
                    pricing.setLastUpdateTmstmp(AppUtils.getCurrentTimestamp());
                }
            }
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error While updating product price", e);
        }
        return false;
    }

    @Override
    public boolean updateUnitProductMappingStatus(int unitId, int productId, ProductStatus status) {
        try {
            UnitProductMapping mapping = getUnitProductMapping(unitId, productId);
            mapping.setProductStatus(status.name());
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error While updating Unit Product Mapping", e);
        }
        return false;
    }

    private UnitProductMapping getUnitProductMapping(int unitId, int productId) {
        Query query = manager.createQuery(
            "FROM UnitProductMapping M WHERE M.unitDetail.unitId = :unitId AND M.productDetail.productId = :productId");
        query.setParameter("unitId", unitId);
        query.setParameter("productId", productId);
        return (UnitProductMapping) query.getSingleResult();
    }

    @Override
    public boolean deactivateAllUnitProductMappingsForProduct(int productId) {
        try {
            Query query = manager.createQuery(
                "UPDATE UnitProductMapping M SET M.productStatus = :status WHERE M.productDetail.productId = :productId");
            query.setParameter("productId", productId);
            query.setParameter("status", ProductStatus.IN_ACTIVE.name());
            query.executeUpdate();
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error While updating Unit Product Mapping status", e);
        }
        return false;
    }

    @Override
    public List<UnitProductPricing> getProductProfileUnits(Integer productId, String profile) {

        Query query = manager.createQuery(
            "FROM UnitProductPricing where unitProductMapping.productDetail.productId = :productId and recipeProfile = :profile and unitProductMapping.productStatus = :status");
        query.setParameter("productId", productId);
        query.setParameter("profile", profile);
        query.setParameter("status", AppConstants.ACTIVE);
        @SuppressWarnings("unchecked")
        List<UnitProductPricing> list = query.getResultList();

        return list;
    }

    @Override
    public List<ProductImageMapping> getProductImagesByProductId(int productId) {
        Query query = manager.createQuery("FROM ProductImageMapping p where " +
            "p.productId = :productId order by index");
        query.setParameter("productId", productId);
        return query.getResultList();
    }

    @Override
    public List<ProductImageMapping> findInProductId(List<Integer> list) {
        Query query = manager.createQuery("FROM ProductImageMapping p where " +
            "p.productId IN(:list)");
        query.setParameter("list", list);
        return query.getResultList();
    }

    @Override
    public List<ProductDimensionImageMapping> getProductsImages(List<Integer> list) {
        Query query = manager.createQuery("FROM ProductDimensionImageMapping p where " +
                "p.productId IN(:list)");
        query.setParameter("list", list);
        try {
            return (List<ProductDimensionImageMapping>) query.getResultList();
        } catch (NoResultException e) {
            LOG.error("Could not found images for product ",e);
        }
        return null;
    }

    @Override
    public ProductImageMapping getProductImagesByProductIdAndType(int productId, String type, Integer index) {
        Query query = manager.createQuery("FROM ProductImageMapping p where " +
            "p.productId = :productId and p.imageType = :type and p.index = :index");
        query.setParameter("productId", productId);
        query.setParameter("type", type);
        query.setParameter("index", index);
        try {
            return (ProductImageMapping) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Could not found images for product ", productId, e);
        }
        return null;
    }

    @Override
    public ProductDimensionImageMapping getProductImagesByProductIdAndTypeAndDimension(int productId, String type, Integer index, Integer dimensionCode) {
        Query query = manager.createQuery("FROM ProductDimensionImageMapping p WHERE " +
                "p.productId = :productId AND p.imageType = :type AND p.index = :index AND " +
                "p.dimensionCode = :dimensionCode");
        query.setParameter("productId", productId);
        query.setParameter("type", type);
        query.setParameter("index", index);
        query.setParameter("dimensionCode", dimensionCode);

        try {
            return (ProductDimensionImageMapping) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Could not find images for product {}", productId, e);
        }
        return null;
    }


    @Override
    public List<ProductDimensionImageMapping> getProductImagesByProductIdAndDimension(int productId,Integer dimensionCode) {
        Query query = manager.createQuery("FROM ProductDimensionImageMapping p where " +
                "p.productId = :productId and p.dimensionCode = :dimensionCode order by index");
        query.setParameter("productId", productId);
        query.setParameter("dimensionCode", dimensionCode);
        return query.getResultList();
    }

    @Override
    public List<ProductCityImageMapping> getProductImagesByProductIdAndCity(List<Integer> productIds, String cityName) {
        Query query = manager.createQuery("FROM ProductCityImageMapping p where " +
                "p.productId in :productIds and p.cityName = :cityName and p.status = :status ");
        query.setParameter("productIds", productIds);
        query.setParameter("cityName", cityName);
        query.setParameter("status", AppConstants.ACTIVE );
        try {
            return  query.getResultList();
        } catch (NoResultException e) {
            LOG.error("Could not found images for product ", productIds, e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<ProductCityImageMapping> getProductImagesByCity(String cityName) {
        Query query = manager.createQuery("FROM ProductCityImageMapping p where " +
                 " p.cityName = :cityName and p.status = :status ");
        query.setParameter("cityName", cityName);
        query.setParameter("status", AppConstants.ACTIVE );
        try {
            return  query.getResultList();
        } catch (NoResultException e) {
            LOG.error("Could not found images for city ", cityName, e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<ProductCityImageMapping> getProductImagesByCity(String cityName , String daySlot) {
        Query query = manager.createQuery("FROM ProductCityImageMapping p where " +
                " p.cityName = :cityName  and p.daySlot = :daySlot and p.status = :status");
        query.setParameter("cityName", cityName);
        query.setParameter("daySlot", daySlot );
        query.setParameter("status", AppConstants.ACTIVE );
        try {
            return  query.getResultList();
        } catch (NoResultException e) {
            LOG.error("Could not found images for city ", cityName, e);
        }
        return new ArrayList<>();
    }

    @Override
    public void updateUnitProductProfile(List<Integer> unitIds, String profile, Integer updatedBy, Integer productId, String dimensionCode) {
        Query query = manager.createNativeQuery(
                "UPDATE UNIT_PRODUCT_PRICING upp " +
                        "INNER JOIN UNIT_PRODUCT_MAPPING upm ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID " +
                        "SET upp.RECIPE_PROFILE = :profile, upp.UPDATED_BY = :updatedBy, upp.LAST_UPDATE_TMSTMP = :updateTime " +
                        "WHERE upp.DIMENSION_CODE = :dimensionId " +
                        "AND upm.UNIT_ID IN (:unitIds) " +
                        "AND upm.PRODUCT_ID = :productId"
        );

        query.setParameter("profile", profile)
                .setParameter("productId", productId)
                .setParameter("dimensionId", Integer.parseInt(dimensionCode))
                .setParameter("unitIds", unitIds)
                .setParameter("updatedBy", updatedBy)
                .setParameter("updateTime", AppUtils.getCurrentTimestamp())
                .executeUpdate();
    }

    @Override
    public ProductCityImageMapping getProductImageByProductIdAndCity(Integer productId, String cityName, String daySlot) {
        Query query = manager.createQuery("FROM ProductCityImageMapping p where " +
                "p.productId  = :productId and p.cityName = :cityName and p.daySlot = :daySlot and p.status = :status ");
        query.setParameter("productId", productId);
        query.setParameter("cityName", cityName);
        query.setParameter("daySlot", daySlot);
        query.setParameter("status", AppConstants.ACTIVE );
        try {
            return (ProductCityImageMapping) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Could not found images for product ", productId, e);
        }
        return null;
    }

    @Override
    public ProductDescription getProductDescriptionDataByProductID(int productId) {
        Query query = manager.createQuery("FROM ProductDescription p where " +
            "p.productId = :productId ");
        query.setParameter("productId", productId);
        try {
            return (ProductDescription) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Could not found description for product ", productId, e);
        }
        return null;
    }

    public List<ProductDescription> getProductDescriptionDataListByProductID(List<Integer> list) {
        Query query = manager.createQuery("FROM ProductDescription p where " +
            "p.productId IN(:list)");
        query.setParameter("list", list);
        return query.getResultList();
    }

    public void sendProductUpdateEmail(Product oldProduct,Product productDetail){
        try{
            List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>"));
            activityLoggerService.sendDiffEmail(oldProduct,productDetail,productDetail.getUpdatedBy().toString(),
                    "Product" ,productDetail.getId(),toEmails,
                    "Product : " + productDetail.getName() + " " + "has been updated");
        }catch (Exception e){
            LOG.info("Error while sending email on product updation for product id ::::: {}",productDetail.getId());
        }
    }

    private Product getProductClone(ProductDetail productDetail){
        try {
            return MasterDataConverter.convert(productDetail);
        }catch (Exception exception){
            LOG.info("Error while making clone of product detail");
        }
        return null;
    }

    private void addProductTagMapping(Product product) {
        ProductDetail productDetail = manager.find(ProductDetail.class, product.getId());
        if (Objects.nonNull(productDetail)) {
            List<ProductTagMappingDetail> tagMappingDetails = new ArrayList<>();
            if (!CollectionUtils.isEmpty(product.getRegularTags())) {
                createProductTagMappingDetail(product.getRegularTags(), tagMappingDetails, AppConstants.REGULAR_TAG, productDetail.getProductId());
            }
            if (!CollectionUtils.isEmpty(product.getNutritionTags())) {
                createProductTagMappingDetail(product.getNutritionTags(), tagMappingDetails, AppConstants.NUTRITION_TAG, productDetail.getProductId());
            }
            if (!CollectionUtils.isEmpty(tagMappingDetails)) {
                tagMappingDetailDao.saveAll(tagMappingDetails);
            }
        }
    }

    public void updateProductTagMapping(Product product) {
        List<ProductTagMappingDetail> tagMappingDetails = new ArrayList<>();
        List<ProductTagMappingDetail> existingTags = tagMappingDetailDao.findByProductIdAndMappingStatus(product.getId(), AppConstants.ACTIVE);
        if (!CollectionUtils.isEmpty(product.getRegularTags())) {
            processTags(product, product.getRegularTags(), AppConstants.REGULAR_TAG, existingTags, tagMappingDetails);
        }
        if (!CollectionUtils.isEmpty(product.getNutritionTags())) {
            processTags(product, product.getNutritionTags(), AppConstants.NUTRITION_TAG, existingTags, tagMappingDetails);
        }
        if (!CollectionUtils.isEmpty(existingTags)) {
            existingTags.forEach(tag -> {
                tag.setMappingStatus(AppConstants.IN_ACTIVE);
                tagMappingDetails.add(tag);
            });
        }
        if (!CollectionUtils.isEmpty(tagMappingDetails)) {
            tagMappingDetailDao.saveAll(tagMappingDetails);
        }
    }

    public List<ProductTagMappingDetail> createProductTagMappingDetail(Set<IdCodeNameValue> tags, List<ProductTagMappingDetail> tagMappingDetails,
                                                                       String mappingType, Integer productId) {
        for (IdCodeNameValue tag : tags) {
            ProductTagMappingDetail mappingDetail = new ProductTagMappingDetail();
            mappingDetail.setRtlId(Integer.valueOf(tag.getCode()));
            mappingDetail.setRlId(tag.getId());
            mappingDetail.setProductId(productId);
            mappingDetail.setMappingType(mappingType);
            mappingDetail.setMappingValue(tag.getName());
            mappingDetail.setMappingStatus(AppConstants.ACTIVE);
            tagMappingDetails.add(mappingDetail);
        }
        return tagMappingDetails;
    }

    private void processTags(Product product, Set<IdCodeNameValue> tags, String mappingType,
                             List<ProductTagMappingDetail> existingTags, List<ProductTagMappingDetail> tagMappingDetails) {
        Integer productId = product.getId();

        for (IdCodeNameValue tag : tags) {
            ProductTagMappingDetail mappingDetail = existingTags.stream()
                    .filter(existingTag -> existingTag.getMappingValue().equals(tag.getName()) && existingTag.getMappingType().equals(mappingType))
                    .findFirst()
                    .orElseGet(() -> {
                        ProductTagMappingDetail newDetail = new ProductTagMappingDetail();
                        newDetail.setRtlId(Integer.valueOf(tag.getCode()));
                        newDetail.setRlId(tag.getId());
                        newDetail.setProductId(productId);
                        newDetail.setMappingType(mappingType);
                        newDetail.setMappingValue(tag.getName());
                        newDetail.setMappingStatus(AppConstants.ACTIVE);
                        return newDetail;
                    });

            if (Objects.nonNull(mappingDetail.getMappingId())) {
                existingTags.remove(mappingDetail);
            } else {
                mappingDetail.setMappingStatus(AppConstants.ACTIVE);
            }

            tagMappingDetails.add(mappingDetail);
        }
    }

    @Override
    public List<ProductNutritionDetail> getProductsNutritionData(List<String> sourceType) {
        LOG.info("Fetching nutrition detail for source type ids {}", sourceType);
        Query query = manager.createQuery("FROM ProductNutritionDetail pnd where pnd.sourceType IN :sourceType");
        query.setParameter("sourceType",sourceType);
        return query.getResultList();
    }
    @Override
    public List<UnitProductPriceCategoryDetail> getUnitsSpecificProductsPrice() {
        StringBuilder nativeQuery = new StringBuilder(NamedQueryDefinition.UNITS_SPECIFIC_PRODUCTS_PRICE_DATA.getQuery());
        Query nq = manager.createNativeQuery(nativeQuery.toString(), "UnitProductPriceCategoryDetail");
        nq.setParameter("dimensions", Arrays.asList("Regular","None"));
        List<UnitProductPriceCategoryDetail> resultList = nq.getResultList();
        LOG.info("Result for pricing category  : {}", resultList.size());
        return resultList;
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<ProductDetail> getProductsByIds(List<Integer> productIds) {
        if(CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }
        Query query = manager.createQuery("FROM ProductDetail WHERE productId IN (:productIds)");
        query.setParameter("productIds", productIds);
        return query.getResultList();
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, UnitProductMappingData> getUnitProductMapping(List<ProductDetail> productDetails, Set<Integer> unitIds, List<Integer> dimensionIds, UnitProductProfileContext context) {
        StringBuilder queryStr = new StringBuilder(
                "SELECT DISTINCT upm FROM UnitProductMapping upm " +
                        "LEFT JOIN FETCH upm.unitDetail ud " +
                        "LEFT JOIN FETCH upm.productDetail pd " +
                        "LEFT JOIN FETCH upm.unitProductPricings upp " +
                        "LEFT JOIN FETCH upp.refLookup rl " +
                        "WHERE upm.productDetail IN (:productDetails) "
        );

        if(!CollectionUtils.isEmpty(unitIds)) {
            queryStr.append(" AND upm.unitDetail.unitId IN (:unitIds) ");
        }
        Query query = manager.createQuery(queryStr.toString());
        if(!CollectionUtils.isEmpty(unitIds)) {
            query.setParameter("unitIds", unitIds);
        }
        query.setParameter("productDetails", productDetails);
        return MasterDataConverter.convert(query.getResultList(), dimensionIds, context);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<Integer, RefLookup> getRefLookUpByIds(List<Integer> refLookUpIds) {
        Query query = manager.createQuery("FROM RefLookup WHERE rlId IN (:refLookUpIds)");
        query.setParameter("refLookUpIds", refLookUpIds);
        List<RefLookup> refLookups = query.getResultList();
        return refLookups.stream().collect(
                Collectors.toMap(RefLookup::getRlId, Function.identity())
        );
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<Integer, UnitProductMapping> getAllUnitProductMappingsByIds(Set<Integer> upmIds) {
        Query query = manager.createQuery("FROM UnitProductMapping WHERE unitProdRefId IN (:upmIds)");
        query.setParameter("upmIds", upmIds);
        List<UnitProductMapping> result =  query.getResultList();
        return result.stream().collect(Collectors.toMap(
                UnitProductMapping::getUnitProdRefId,
                upm -> upm
        ));
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<UnitDetail> getUnitsByIds(List<Integer> unitIds) {
        if(CollectionUtils.isEmpty(unitIds)) {
            return new ArrayList<>();
        }
        Query query = manager.createQuery("FROM UnitDetail WHERE unitId IN (:unitIds)");
        query.setParameter("unitIds", unitIds);
        return query.getResultList();
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<UnitProductMapping> getUnitProductMapping(ArrayList<Integer> unitIds, ArrayList<Integer> productIds) {
        Query query = manager.createQuery("FROM UnitProductMapping WHERE unitDetail.unitId IN (:unitIds) AND productDetail.productId IN (:productIds)");
        query.setParameter("unitIds", unitIds)
                .setParameter("productIds", productIds);
        return query.getResultList();
    }

    @Override
    public UnitProductMapping isUnitProductMappingExists(Integer productId, Integer unitId) {
        Query query = manager.createQuery("FROM UnitProductMapping WHERE unitDetail.unitId =:unitId AND productDetail.productId =:productId");
        query.setParameter("unitId", unitId)
                .setParameter("productId", productId);
        List<UnitProductMapping> result = query.getResultList();
        return result.isEmpty() ? new UnitProductMapping() : result.get(0);
    }

}
