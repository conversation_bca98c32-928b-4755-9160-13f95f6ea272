/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "KIOSK_OFFICE_LOCATION_DETAILS")
public class KioskLocationDetailsData {

    private Integer locationId;
    private String locationName;
    private String locationAddress;
    private String locationShortCode;
    private String locationStatus;

    private UnitDetail assignedUnit;
    private KioskOfficeDetailsData officeDetailsData;
    private List<KioskMachineDetailsData> kioskMachineDetailsDataList = new ArrayList<>();


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "KIOSK_LOCATION_ID", nullable = false, unique = true)
    public Integer getLocationId() {
        return locationId;
    }

    public void setLocationId(Integer locationId) {
        this.locationId = locationId;
    }

    @Column(name = "LOCATION_NAME", nullable = false, unique = true)
    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    @Column(name = "LOCATION_ADDRESS", nullable = false)
    public String getLocationAddress() {
        return locationAddress;
    }

    public void setLocationAddress(String locationAddress) {
        this.locationAddress = locationAddress;
    }

    @Column(name = "LOCATION_SHORT_CODE", nullable = false)
    public String getLocationShortCode() {
        return locationShortCode;
    }

    public void setLocationShortCode(String locationShortCode) {
        this.locationShortCode = locationShortCode;
    }

    @Column(name = "LOCATION_STATUS", nullable = false)
    public String getLocationStatus() {
        return locationStatus;
    }

    public void setLocationStatus(String locationStatus) {
        this.locationStatus = locationStatus;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "UNIT_ID",nullable = true)
    public UnitDetail getAssignedUnit() {
        return assignedUnit;
    }


    public void setAssignedUnit(UnitDetail assignedUnit) {
        this.assignedUnit = assignedUnit;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "OFFICE_ID",nullable = true)
    public KioskOfficeDetailsData getOfficeDetailsData() {
        return officeDetailsData;
    }

    public void setOfficeDetailsData(KioskOfficeDetailsData officeDetailsData) {
        this.officeDetailsData = officeDetailsData;
    }

    @OneToMany(mappedBy = "locationDetailsData", fetch = FetchType.LAZY)
    public List<KioskMachineDetailsData> getKioskMachineDetailsDataList() {
        return kioskMachineDetailsDataList;
    }

    public void setKioskMachineDetailsDataList(List<KioskMachineDetailsData> kioskMachineDetailsDataList) {
        this.kioskMachineDetailsDataList = kioskMachineDetailsDataList;
    }
}
