package com.stpl.tech.master.core.service.impl;

import java.util.List;
import java.util.Optional;

import com.stpl.tech.master.data.dao.RecipeIterationDao;
import com.stpl.tech.master.recipe.calculator.model.RecipeIterationDetail;
import com.stpl.tech.master.recipe.calculator.model.RecipeIterationStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.core.service.RecipeReadService;
import com.stpl.tech.master.data.dao.RecipeDao;
import com.stpl.tech.master.recipe.model.RecipeDetail;

@Service
public class RecipeReadServiceImpl implements RecipeReadService {

	@Autowired
	private RecipeDao recipeDao;

	@Autowired
	private RecipeIterationDao recipeIterationDao;

	@Override
	public List<RecipeDetail> findByStatus(String status) {
		return recipeDao.findByStatus(status);
	}

	@Override
	public List<RecipeDetail> findAllRecipe() {
		return recipeDao.findAll();
	}

	@Override
	public RecipeIterationDetail getRecipeIterationDetailByProductIdAndProfile(Integer linkedProductId, String profile) {
			return recipeIterationDao.getApprovedIterationForProduct(linkedProductId, RecipeIterationStatus.APPROVED).stream()
					.filter(recipeIterationDetail -> recipeIterationDetail.getProfile().equalsIgnoreCase(profile))
					.findFirst()
					.orElse(null);
	}

}
