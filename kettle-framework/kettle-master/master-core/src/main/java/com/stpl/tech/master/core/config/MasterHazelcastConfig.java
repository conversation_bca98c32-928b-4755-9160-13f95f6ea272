/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;

@Configuration
@ConditionalOnProperty(value = "is.client.node", havingValue = "false", matchIfMissing = false)
@ComponentScan({ "com.stpl.tech.master.core.external.interceptor", "com.stpl.tech.master.core.external.acl.service",
		"com.stpl.tech.master.core.external.cache" })
public class MasterHazelcastConfig {

	@Value("${cluster.node.ip.details}")
	private String clusterIps;

	@Bean(name = "MasterHazelCastSessionConfig")
	public Config config(String instanceName) {
		Config config = new Config();
		config.setClusterName("MasterHazelCastCacheCluster");
		config.getNetworkConfig().getJoin().getMulticastConfig().setEnabled(false);
		config.setProperty("hazelcast.logging.type", "log4j2");
		config.setProperty("hazelcast.health.monitoring.level", "OFF");
		config.setInstanceName(instanceName);
		String ips[] = clusterIps.split(",");
		for (String ip : ips) {
			String address = ip.split(":")[0];
			Integer port = Integer.valueOf(ip.split(":")[1]);
			config.getNetworkConfig().setPort(port);
			config.getNetworkConfig().getJoin().getTcpIpConfig().setEnabled(true).addMember(address);
		}
		return config;
	}

	@Bean(name = "MasterHazelCastInstance")
	public HazelcastInstance hazelcastInstance() {
		return Hazelcast.getOrCreateHazelcastInstance(config("MasterDataCache"));
	}
}
