/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.util;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import javax.activation.UnsupportedDataTypeException;
import javax.sql.DataSource;

import org.apache.commons.lang.WordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import com.stpl.tech.kettle.report.metadata.model.ReportData;

public class QueryExecutorForList extends QueryExecutor<List<List<String>>, List<String>> {

	private static final Logger LOG = LoggerFactory.getLogger(QueryExecutorForList.class);
	public void execute(ReportData reportDefinition, DataSource dataSource)
			throws UnsupportedDataTypeException, ParseException {
		NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
		try {
			LOG.info("Connected to : {}", dataSource.getConnection().getMetaData().getURL());
		} catch (SQLException e) {
		}
		ResultSetConverterToList rse = new ResultSetConverterToList();
		List<List<String>> datas = jdbcTemplate.query(reportDefinition.getContent(), getParamSource(reportDefinition),
				rse);
		List<String> formattedHeader = new ArrayList<String>();
		for (String header : datas.get(0)) {
			formattedHeader.add(WordUtils.capitalizeFully(header.replaceAll("_", " ")));
		}
		datas.set(0, formattedHeader);
		this.data = datas;
		this.header = rse.getHeader();
	}
}
