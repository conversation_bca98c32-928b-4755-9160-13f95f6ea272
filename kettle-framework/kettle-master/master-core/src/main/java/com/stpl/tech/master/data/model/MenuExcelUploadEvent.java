package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "MENU_EXCEL_UPLOAD_EVENT")
public class MenuExcelUploadEvent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MENU_EXCEL_UPLOAD_EVENT_ID", nullable = false)
    private Integer id;

    @Column(name = "FILE_KEY", length = 200)
    private String fileKey;

    @Column(name = "BUCKET", length = 200)
    private String bucket;

    @Column(name = "UPLOAD_TIME")
    private Date uploadTime;

    @Column(name = "UPLOADED_BY")
    private Integer uploadedBy;

    @Column(name = "SHEET_VERSION", length = 45)
    private String sheetVersion;

    @Lob
    @Column(name = "UPDATION_SHEETS")
    private String updationSheets;

    @Column(name = "FILE_PATH", length = 500)
    private String filePath;

    @Column(name = "MENU_APP")
    private String menuApp;

    @Column(name = "STATUS")
    private String status;
}
