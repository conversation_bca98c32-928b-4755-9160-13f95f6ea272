package com.stpl.tech.master.core.service.model;

import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.master.data.model.UnitClosureFormMetaData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnitClosureFormDataDomain {

    Integer unitClosureFormDataId;

    UnitClosureEvent unitClosureEvent;

    UnitClosureFormMetaData unitClosureMetaData;

    Date date;
    String comment;
    Integer attachmentId;
}
