package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT")
public class UnitProductPricingBulkUpdateEvent implements Serializable {

    private static final long serialVersionUID = 8137008431423411386L;
    private Integer eventId;
    private Integer totalRecords;
    private Integer totalRecordsChanged;
    private Integer totalRecordsUpdatedSuccessfully;
    private Integer totalErrorRecords;
    private Integer totalFailureRecords;
    private Integer updatedBy;
    private Date updateTime;
    private String sheetPath;
    private String unitCategory;
    private Integer brandId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID", unique = true, nullable = false)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "TOTAL_RECORDS")
    public Integer getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Integer totalRecords) {
        this.totalRecords = totalRecords;
    }

    @Column(name = "TOTAL_RECORDS_CHANGED")
    public Integer getTotalRecordsChanged() {
        return totalRecordsChanged;
    }

    public void setTotalRecordsChanged(Integer totalRecordsChanged) {
        this.totalRecordsChanged = totalRecordsChanged;
    }

    @Column(name = "TOTAL_RECORDS_UPDATED_SUCCESSFULLY")
    public Integer getTotalRecordsUpdatedSuccessfully() {
        return totalRecordsUpdatedSuccessfully;
    }

    public void setTotalRecordsUpdatedSuccessfully(Integer totalRecordsUpdatedSuccessfully) {
        this.totalRecordsUpdatedSuccessfully = totalRecordsUpdatedSuccessfully;
    }

    @Column(name = "TOTAL_ERROR_RECORDS")
    public Integer getTotalErrorRecords() {
        return totalErrorRecords;
    }

    public void setTotalErrorRecords(Integer totalErrorRecords) {
        this.totalErrorRecords = totalErrorRecords;
    }

    @Column(name = "TOTAL_FAILURE_RECORDS")
    public Integer getTotalFailureRecords() {
        return totalFailureRecords;
    }

    public void setTotalFailureRecords(Integer totalFailureRecords) {
        this.totalFailureRecords = totalFailureRecords;
    }

    @Column(name = "UPDATED_BY")
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "UPDATE_TIME")
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "SHEET_PATH")
    public String getSheetPath() {
        return sheetPath;
    }

    public void setSheetPath(String sheetPath) {
        this.sheetPath = sheetPath;
    }

    @Column(name = "UNIT_CATEGORY")
    public String getUnitCategory() {
        return unitCategory;
    }

    public void setUnitCategory(String unitCategory) {
        this.unitCategory = unitCategory;
    }

    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Override
    public String toString() {
        return "UnitProductPricingBulkUpdateEvent{" +
                "eventId=" + eventId +
                ", totalRecords=" + totalRecords +
                ", totalRecordsChanged=" + totalRecordsChanged +
                ", totalRecordsUpdatedSuccessfully=" + totalRecordsUpdatedSuccessfully +
                ", totalErrorRecords=" + totalErrorRecords +
                ", totalFailureRecords=" + totalFailureRecords +
                ", updatedBy=" + updatedBy +
                ", updateTime=" + updateTime +
                ", sheetPath='" + sheetPath + '\'' +
                ", unitCategory='" + unitCategory + '\'' +
                ", brandId=" + brandId +
                '}';
    }
}
