package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "MONK_RECIPE_VERSION_DETAIL")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MonkRecipeVersionDetail implements Serializable {

    private static final long serialVersionUID = -76772139467537905L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_RECIPE_VERSION_DETAIL_ID")
    private Integer monkRecipeVersionDetailId;
    @Column(name = "UNIT_ID", unique = true, nullable = false)
    private Integer unitId;
    @Column(name = "PREVIOUS_VERSION", nullable = true)
    private String previousVersion;
    @Column(name = "PREVIOUS_RECIPE_COUNT", nullable = true)
    private Integer previousCount;
    @Column(name = "CURRENT_VERSION", nullable = false)
    private String currentVersion;
    @Column(name = "CURRENT_RECIPE_COUNT", nullable = false)
    private Integer currentCount;
    @Column(name = "UPDATED_BY", nullable = false)
    private Integer updateBy;
    @Column(name = "UPDATED_TIME", nullable = false)
    private Date updatedTime;
}
