package com.stpl.tech.master.data.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.master.data.dao.InstallationDao;
import com.stpl.tech.master.data.model.ApplicationInstallationData;
import com.stpl.tech.master.data.model.UnitRestrictedApplicationData;


@SuppressWarnings("unchecked")
@Repository
public class InstallationDaoImpl extends AbstractMasterDaoImpl implements InstallationDao {

	@Override
	public List<ApplicationInstallationData> getInstalledMachines(String applicationName, Integer unitId,
			String terminal, String screenType, String status) {
		
		StringBuilder queryString = new StringBuilder(
				"FROM ApplicationInstallationData a WHERE a.applicationName = :applicationName ");
		if (unitId != null && unitId != 0) {
			queryString.append(" and a.unitId = :unitId ");
		}

		if (terminal != null) {
			queryString.append(" and a.terminal = :terminal ");
		}

		if (screenType != null) {
			queryString.append(" and a.screenType = :screenType ");
		}

		if (status != null) {
			queryString.append(" and a.status = :status ");
		}

		Query query = manager.createQuery(queryString.toString());

		if (unitId != null && unitId != 0) {
			query.setParameter("unitId", unitId);
		}

		if (screenType != null) {
			query.setParameter("screenType", screenType);
		}

		if (terminal != null) {
			query.setParameter("terminal", terminal);
		}

		if (status != null) {
			query.setParameter("status", status);
		}

		return query.getResultList();
	}

	@Override
	public List<UnitRestrictedApplicationData> getUnitRestrictedApplication(Integer unitId, String application, String status) {
		StringBuilder queryString = new StringBuilder(
				"FROM UnitRestrictedApplicationData a WHERE a.unitId = :unitId ");
		if (application != null) {
			queryString.append(" and a.applicationName = :application ");
		}
		if (status != null) {
			queryString.append(" and a.status = :status ");
		}
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("unitId", unitId);
		
		if (application != null) {
			query.setParameter("application", application);
		}

		if (status != null) {
			query.setParameter("status", status);
		}

		return query.getResultList();
	}

}
