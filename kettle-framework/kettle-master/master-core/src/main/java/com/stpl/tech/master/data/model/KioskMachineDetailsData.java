/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
@Entity
@Table(name = "KIOSK_MACHINE_DETAILS")
public class KioskMachineDetailsData {

    private Integer machineId;
    private String uuid;
    private String batchNo;
    private Date installationDate;
    private Date manufacturingDate;
    private Date deactivationDate;
    private String machineStatus;
    private KioskLocationDetailsData locationDetailsData;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MACHINE_ID", nullable = false, unique = true)
    public Integer getMachineId() {
        return machineId;
    }

    public void setMachineId(Integer machineId) {
        this.machineId = machineId;
    }

    @Column(name = "MACHINE_UUID", nullable = true)
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "INSTALLATION_DATE", nullable = true, length = 19)
    public Date getInstallationDate() {
        return installationDate;
    }

    public void setInstallationDate(Date installationDate) {
        this.installationDate = installationDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MANUFACTURING_DATE", nullable = false, length = 19)
    public Date getManufacturingDate() {
        return manufacturingDate;
    }

    public void setManufacturingDate(Date manufacturingDate) {
        this.manufacturingDate = manufacturingDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DEACTIVATION_DATE", nullable = true, length = 19)
    public Date getDeactivationDate() {
        return deactivationDate;
    }

    public void setDeactivationDate(Date deactivationDate) {
        this.deactivationDate = deactivationDate;
    }

    @Column(name = "MACHINE_STATUS", nullable = false)
    public String getMachineStatus() {
        return machineStatus;
    }

    public void setMachineStatus(String machineStatus) {
        this.machineStatus = machineStatus;
    }

    @Column(name = "BATCH_NUMBER", nullable = false)
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "KIOSK_LOCATION_ID", nullable = true)
    public KioskLocationDetailsData getLocationDetailsData() {
        return locationDetailsData;
    }

    public void setLocationDetailsData(KioskLocationDetailsData locationDetailsData) {
        this.locationDetailsData = locationDetailsData;
    }
}
