/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.master.core.external.offer.dao.impl;

import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.LaunchOfferStrategy;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.data.vo.HourlyOfferUnitMapping;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.AppOfferApplicabilityData;
import com.stpl.tech.master.data.model.AppOfferDetailData;
import com.stpl.tech.master.data.model.AppOfferMappingData;
import com.stpl.tech.master.data.model.CampaignCouponMapping;
import com.stpl.tech.master.data.model.CampaignDetailData;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CouponDetailMappingData;
import com.stpl.tech.master.data.model.CustomerOfferMappingData;
import com.stpl.tech.master.data.model.CustomerWinbackOfferInfo;
import com.stpl.tech.master.data.model.DeliveryCouponAllocationDetailData;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.DeliveryOfferDetailData;
import com.stpl.tech.master.data.model.LaunchOfferData;
import com.stpl.tech.master.data.model.MarketingPartner;
import com.stpl.tech.master.data.model.OfferAccountCategory;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.OfferDetailMappingData;
import com.stpl.tech.master.data.model.OfferMetadata;
import com.stpl.tech.master.data.model.OfferPartner;
import com.stpl.tech.master.data.model.SignupOffersCouponDetails;
import com.stpl.tech.master.domain.model.AppOfferDetail;
import com.stpl.tech.master.domain.model.AppOfferType;
import com.stpl.tech.master.domain.model.AppOfferUnitDetail;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignMapping;
import com.stpl.tech.master.domain.model.CampaignReachType;
import com.stpl.tech.master.domain.model.CouponBulkUpdateDomain;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.DeliveryCouponStatus;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.MappingIdCodeName;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.RandomStringGenerator;
import com.stpl.tech.util.domain.RequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Repository
public class OfferManagementDaoImpl extends AbstractMasterDaoImpl implements OfferManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(OfferManagementDaoImpl.class);
    private static final int MAX_COUPONS_RETURNED = 10;

    private static final int MAX_MAPPING_COUNT = 10;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CouponDetail getCouponDetail(String couponCode, boolean getAll, boolean getParentMapping, boolean applyLimit) {
        try {
            CouponDetailData o = getCoupon(couponCode);
            List<CouponDetailMappingData> mappings = getCouponMappings(o.getCouponDetailId());
            if(applyLimit){
                int lastIndex = mappings.size();
                int startIndex = mappings.size() > MAX_MAPPING_COUNT ? lastIndex-MAX_MAPPING_COUNT : 0;
                List<CouponDetailMappingData> newMappingData = mappings.subList(startIndex, lastIndex);
                mappings = newMappingData;
            }
            return o == null ? null : MasterDataConverter.convert(o, mappings, getAll, getParentMapping);
        } catch (Exception e) {
            LOG.info("Error while getting Coupon Code: {} , {}", couponCode, e.getMessage());
        }
        return null;
    }

    @Override
    public CouponDetailData getCoupon(String couponCode) {
        try {
            Query query = manager.createQuery("SELECT data FROM CouponDetailData data where data.couponCode = :couponCode");
            query.setParameter("couponCode", couponCode);
            Object o = query.getSingleResult();
            CouponDetailData couponDetailData = (CouponDetailData) o;
            if(Objects.nonNull(couponDetailData)){
                couponDetailData.getOfferDetail();
                couponDetailData.getOfferDetail().setMetaDataMappings(getMetadataMappings(couponDetailData.getOfferDetail().getOfferDetailId()));
            }
            return o == null ? null : (CouponDetailData) o;
        } catch (Exception e) {
            LOG.info("Error while getting Coupon Code: {} , {}", couponCode, e.getMessage());
        }
        return null;
    }

    private List<OfferMetadata> getMetadataMappings(int offerId){
        try{
            Query query = manager.createQuery("SELECT data FROM OfferMetadata data where data.offerId.offerDetailId = :offerId");
            query.setParameter("offerId", offerId);
            List<OfferMetadata> offerMetadataList = query.getResultList();
            return offerMetadataList;
        }catch (Exception e){
            LOG.error("Exception while getting offer metadata");
        }
        return new ArrayList<>();
    }

    @Override
    public String getCouponByOfferId(Integer offerId){
        try{
            Query query = manager.createQuery("SELECT data From CouponDetailData data where data.offerDetail.offerDetailId = :offerDetailId");
            query.setParameter("offerDetailId",offerId);
            query.setMaxResults(1);
            CouponDetailData result = (CouponDetailData) query.getSingleResult();
            if(Objects.nonNull(result)){
                return result.getCouponCode();
            }
            else{
                LOG.info("No coupon exist for this offerId : {}",offerId);
                return null;
            }
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<CouponDetailMappingData> getCouponMappings(Integer couponDetailId) {
        try {
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CouponDetailMappingData data where data.couponDetail = :couponDetailId AND data.mappingType not in (:mappingTypes)");
            query.setParameter("couponDetailId", couponDetailId);
            query.setParameter("mappingTypes", List.of(CouponMappingType.CONTACT_NUMBER.name(), CouponMappingType.CUSTOMER.name()));
            List<CouponDetailMappingData> mappings = query.getResultList();
            LOG.info("COUPON_APPLY Fetching coupon detail for couponCode : {} ends in {}ms", couponDetailId, System.currentTimeMillis()-startTime);
            return mappings;
        } catch (Exception e) {
            LOG.info("Error while getting Coupon mappings: {} , {}", couponDetailId, e.getMessage());
        }
        return null;
    }

    @Override
    public List<CouponDetailMappingData> getCouponMappingsForCustomerId(Integer couponDetailId, Integer customerId) {
        try {
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CouponDetailMappingData data where data.couponDetail = :couponDetailId AND data.mappingType = :mappingType and data.mappingValue = :customerId");
            query.setParameter("couponDetailId", couponDetailId);
            query.setParameter("mappingType", CouponMappingType.CUSTOMER.name());
            query.setParameter("customerId", customerId.toString());
            List<CouponDetailMappingData> mappings = query.getResultList();
            LOG.info("COUPON_APPLY Fetching coupon detail for couponCode : {} and customerId {} ends in {}ms", couponDetailId, customerId, System.currentTimeMillis()-startTime);
            return mappings;
        } catch (Exception e) {
            LOG.info("Error while getting Coupon mappings: {} , {}", couponDetailId, e.getMessage());
        }
        return new ArrayList<>();
    }

    @Override
    public long getCouponMappingsCountForCustomerId(Integer couponDetailId){
        try{
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select count(*) from CouponDetailMappingData data where data.couponDetail = :couponDetailId AND data.mappingType = :mappingType");
            query.setParameter("couponDetailId", couponDetailId);
            query.setParameter("mappingType", CouponMappingType.CUSTOMER.name());
            long count = (long) query.getSingleResult();
            LOG.info("COUPON_APPLY Fetching coupon Mapping Count for couponCode : {} ends in {}ms", couponDetailId, System.currentTimeMillis()-startTime);
            return count;
        }catch (Exception e){
            LOG.info("Error while getting Coupon mappings count: {} , {}", couponDetailId, e.getMessage());
        }
        return 0;
    }

    @Override
    public boolean isMappingExistForThisCoupon(Integer couponDetailId){
        try{
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CouponDetailMappingData data where data.couponDetail = :couponDetailId");
            query.setParameter("couponDetailId", couponDetailId);
            query.setMaxResults(1);
            CouponDetailMappingData result = (CouponDetailMappingData) query.getSingleResult();
            LOG.info("COUPONDETAILS ARE : {}",result);
            LOG.info("COUPON_APPLY Fetching coupon Mapping Count for couponCode : {} ends in {}ms", couponDetailId, System.currentTimeMillis()-startTime);
            if(Objects.nonNull(result)){
                return true;
            }
            else{
                return false;
            }
        }catch (Exception e){
            LOG.info("Error while getting Coupon mappings count: {} , {}", couponDetailId, e.getMessage());
        }
        return false;
    }



    @Override
    public long getCouponMappingsCountForContactNumber(Integer couponDetailId){
        try{
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select count(*) from CouponDetailMappingData data where data.couponDetail = :couponDetailId AND data.mappingType = :mappingType");
            query.setParameter("couponDetailId", couponDetailId);
            query.setParameter("mappingType", CouponMappingType.CONTACT_NUMBER.name());
            long count = (long) query.getSingleResult();
            LOG.info("COUPON_APPLY Fetching coupon Mapping Count for couponCode : {} ends in {}ms", couponDetailId, System.currentTimeMillis()-startTime);
            return count;
        }catch (Exception e){
            LOG.info("Error while getting Coupon mappings count: {} , {}", couponDetailId, e.getMessage());
        }
        return 0;
    }


    @Override
    public List<CouponDetailMappingData> getCouponMappingsForContactNumber(Integer couponDetailId, String contactNumber) {
        try {
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CouponDetailMappingData data where data.couponDetail = :couponDetailId AND data.mappingType = :mappingType and data.mappingValue = :contactNumber");
            query.setParameter("couponDetailId", couponDetailId);
            query.setParameter("mappingType", CouponMappingType.CONTACT_NUMBER.name());
            query.setParameter("contactNumber", contactNumber);
            List<CouponDetailMappingData> mappings = query.getResultList();
            LOG.info("COUPON_APPLY Fetching coupon mappings for couponCode : {} and contact number {} ends in {}ms", couponDetailId, contactNumber, System.currentTimeMillis()-startTime);
            return mappings;
        } catch (Exception e) {
            LOG.info("Error while getting Coupon mappings: {} , {}", couponDetailId, e.getMessage());
        }
        return new ArrayList<>();
    }

    @Override
    public DeliveryCouponDetailData getDeliveryCoupon(String code, Integer brandId) {
        try {
            Date currentDate = AppUtils.getCurrentTimestamp();
            Query query = manager.createQuery("SELECT DC FROM DeliveryCouponDetailData DC WHERE " +
                    " DC.masterCoupon = :masterCoupon AND DC.endDate >= :endDate " +
                    "AND DC.brandId = :brandId AND DC.channelPartnerId = :channelPartnerId AND DC.deliveryCouponStatus = :status " +
                    "AND DC.noOfAllocations < DC.maxNoOfDistributions AND DC.isExhausted = :isExhausted " +
                    "ORDER BY DC.noOfAllocations,DC.deliveryCouponId ASC");
            query.setParameter("masterCoupon", code);
            query.setParameter("endDate", currentDate);
            query.setParameter("brandId", brandId);
            query.setParameter("channelPartnerId", 3);
            query.setParameter("status", DeliveryCouponStatus.AVAILABLE.name());
            query.setParameter("isExhausted", AppConstants.getValue(false));
            query.setMaxResults(1);
            DeliveryCouponDetailData detailData = (DeliveryCouponDetailData) query.getSingleResult();
            if(Objects.nonNull(detailData)){
                LOG.info("Found valid Coupon : {} for master coupon : {} ",detailData.getCouponCode(),detailData.getMasterCoupon());
                return detailData;
            }else {
                LOG.info("No valid Coupon found for masterCoupon : {}, startDate : {}, endDate :{}, brandId :{}, " +
                        "channelPartnerId : {}, isExhausted : {}",code,currentDate,currentDate,brandId,3,AppConstants.getValue(false));
            }
        } catch (Exception e) {
            LOG.error("Error while getting Coupon Code: {} , {}", code, e.getMessage());
        }
        return null;
    }

    @Override
    public DeliveryCouponDetailData getDeliveryCoupon(String code, Integer brandId,Integer channelPartnerId) {
        try {
            Date currentDate = AppUtils.getCurrentTimestamp();
            Query query = manager.createQuery("SELECT DC FROM DeliveryCouponDetailData DC WHERE " +
                    " DC.masterCoupon = :masterCoupon AND DC.endDate >= :endDate " +
                    "AND DC.brandId = :brandId AND DC.channelPartnerId = :channelPartnerId AND DC.deliveryCouponStatus = :status " +
                    "AND DC.noOfAllocations < DC.maxNoOfDistributions AND DC.isExhausted = :isExhausted " +
                    "ORDER BY DC.noOfAllocations,DC.deliveryCouponId ASC");
            query.setParameter("masterCoupon", code);
            query.setParameter("endDate", currentDate);
            query.setParameter("brandId", brandId);
            query.setParameter("channelPartnerId", channelPartnerId);
            query.setParameter("status", DeliveryCouponStatus.AVAILABLE.name());
            query.setParameter("isExhausted", AppConstants.getValue(false));
            query.setMaxResults(1);
            DeliveryCouponDetailData detailData = (DeliveryCouponDetailData) query.getSingleResult();
            if(Objects.nonNull(detailData)){
                LOG.info("Found valid Coupon : {} for master coupon : {} ",detailData.getCouponCode(),detailData.getMasterCoupon());
                return detailData;
            }else {
                LOG.info("No valid Coupon found for masterCoupon : {}, startDate : {}, endDate :{}, brandId :{}, " +
                        "channelPartnerId : {}, isExhausted : {}",code,currentDate,currentDate,brandId,3,AppConstants.getValue(false));
            }
        } catch (Exception e) {
            LOG.error("Error while getting Coupon Code: {} , {}", code, e.getMessage());
        }
        return null;
    }


    @Override
    public List<CouponDetail> getRegularCoupons(Integer loggedInBrandId) {

        List<CouponDetailData> list = getMassCouponsDetailData(loggedInBrandId);
        List<CouponDetail> coupons = new ArrayList<>();
        if (list != null) {
            list.forEach(data -> {
                List<CouponDetailMappingData> mappings = getCouponMappings(data.getCouponDetailId());
                coupons.add(MasterDataConverter.convert(data, mappings, false, true));
            });
        }
        return coupons;
    }

    @SuppressWarnings("unchecked")
    private List<CouponDetailData> getMassCouponsDetailData(Integer loggedInBrandId) {

        Date currentDate = AppUtils.getCurrentBusinessDate();
        List<CouponDetailData> list = null;
        try {
            Query query = manager.createQuery("SELECT D FROM CouponDetailData D WHERE D.offerDetail.offerScope = :scope "
                    + "AND D.startDate <= :currentDate AND D.endDate >= :currentDate "
                    + "AND D.couponStatus = :couponStatus AND D.offerDetail.offerStatus = :offerStatus "
                    + "AND D.offerDetail.brandId = :loggedInBrandId");
            // Check for MASS Coupons
            query.setParameter("scope", "MASS");
            query.setParameter("currentDate", currentDate);
            query.setParameter("offerStatus", AppConstants.ACTIVE);
            query.setParameter("couponStatus", AppConstants.ACTIVE);
            query.setParameter("loggedInBrandId", loggedInBrandId);
            list = (List<CouponDetailData>) query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting Coupons", e);
        }
        return list;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<OfferDetail> getAllOffers(boolean trimmed) {
        List<OfferDetail> offerDetailList = new ArrayList<>();
        List<OfferDetailData> detailList = null;

        try {
            Query query = manager.createQuery("SELECT D FROM OfferDetailData D WHERE D.offerStatus <> :status");
            query.setParameter("status", AppConstants.ARCHIVED);
            detailList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting All Offer Detail", e);
        }

        if (detailList != null) {
            // Filter out offers with null brandId or brandId matching the logged-in brand
            if (RequestContext.isContextAvailable()) {
                List<Integer> mappedBrands = MasterUtil.getMappedBrands();
                detailList = detailList.stream()
                        .filter(d -> d.getBrandId() == null || mappedBrands.contains(d.getBrandId()))
                        .collect(Collectors.toList());
            }

            // Convert OfferDetailData to OfferDetail
            detailList.forEach(data -> offerDetailList.add(MasterDataConverter.convert(data, false, trimmed)));
        }

        return offerDetailList;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<OfferDetail> getOffer(String key, boolean trimmed) {
        List<OfferDetail> offerDetailList = new ArrayList<>();
        List<OfferDetailData> detailList = null;
        key = "%" + key + "%";
        Query query = manager.createQuery("SELECT D FROM OfferDetailData D WHERE D.offerText LIKE :key");
        query.setParameter("key", key);
        detailList = query.getResultList();

        if (detailList != null) {
            detailList.forEach(data ->
                    offerDetailList.add(MasterDataConverter.convert(data, false, trimmed))
            );
        }
        return offerDetailList;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<CouponDetail> getOfferCoupons(int offerId, boolean applyLimit) {
        try {
            System.gc();
        } catch (Exception e) {

        }
        List<CouponDetail> coupons = new ArrayList<>();
        List<CouponDetailData> dataList = null;

        try {
            OfferDetailData offerDetailData = manager.find(OfferDetailData.class, offerId);
            Query query = manager.createQuery("SELECT D FROM CouponDetailData D WHERE D.offerDetail = :offerDetailData");
            query.setParameter("offerDetailData", offerDetailData);
            query.setMaxResults(MAX_COUPONS_RETURNED);
            dataList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting Coupons", e);
        }

        if (dataList != null) {
           for(CouponDetailData data : dataList){
               List<CouponDetailMappingData> mappings = getCouponMappings(data.getCouponDetailId());
               if(applyLimit){
                   int lastIndex = mappings.size();
                   int startIndex = mappings.size() > MAX_MAPPING_COUNT ? lastIndex-MAX_MAPPING_COUNT : 0;
                   List<CouponDetailMappingData> newMappingData = mappings.subList(startIndex, lastIndex);
                   mappings = newMappingData;
               }
               coupons.add(MasterDataConverter.convert(data, mappings, true, true));
           }
        }

        return coupons;
    }

    @Override
    public OfferDetail addOffer(OfferDetail offerDetail) throws OfferValidationException {
        checkUnitMapping(offerDetail);
        OfferDetailData offer = new OfferDetailData();
        updateOfferDetailData(offer, offerDetail);
        manager.persist(offer);
        manager.flush();
        addMappings(offer, offerDetail);
        addPatners(offer, offerDetail);
        setCouponMappings(offer, offerDetail);
        manager.flush();
        return MasterDataConverter.convert(offer, true, false);
    }

    private void checkUnitMapping(OfferDetail offerDetail) throws OfferValidationException {
        for (CouponMapping mapping : offerDetail.getCouponMappingList()) {
            if(Objects.nonNull(offerDetail.getAutoApplicableforUnit()) && offerDetail.getAutoApplicableforUnit()
                    && mapping.getType().equals(CouponMappingType.UNIT.name())){
                CouponDetail detail=getAutoApplicableOfferForUnit(Integer.valueOf(mapping.getValue()));
                if(Objects.nonNull(detail)){
                    throw new OfferValidationException("Unit "+ mapping.getValue()+ " already have auto aplicable coupon enabled with offer Id "+ detail.getOffer().getId(),
                            WebErrorCode.INVALID_UNIT);
                }
            }
        }
    }

    private void updateOfferDetailData(OfferDetailData offer, OfferDetail offerDetail) {
        offer.setOfferCategory(offerDetail.getCategory());
        offer.setOfferType(offerDetail.getType());
        offer.setOfferText(offerDetail.getText());
        offer.setOfferDescription(offerDetail.getDescription());
        offer.setStartDate(AppUtils.getDate(offerDetail.getStartDate()));
        offer.setEndDate(AppUtils.getDate(offerDetail.getEndDate()));
        offer.setMinValue(offerDetail.getMinValue());
        offer.setIncludeTaxes(AppConstants.getValue(offerDetail.isIncludeTaxes()));
        if (offerDetail.getOfferScope().equals(AppConstants.OFFER_SCOPE_CORPORATE)) {
            offer.setEmailDomain(offerDetail.getEmailDomain());
        } else {
            offer.setEmailDomain(null);
        }
        offer.setOfferStatus(offerDetail.getStatus());
        offer.setValidateCustomer(AppConstants.getValue(offerDetail.isValidateCustomer()));
        offer.setMinQuantity(offerDetail.getMinQuantity());
        offer.setMinLoyalty(offerDetail.getMinLoyalty());
        offer.setMinItemCount(offerDetail.getMinItemCount());
        offer.setValue(offerDetail.getOfferValue());
        offer.setOfferScope(offerDetail.getOfferScope());
        offer.setPriority(offerDetail.getPriority());
        offer.setOtpRequired(AppConstants.getValue(offerDetail.getOtpRequired()));
        offer.setRemoveLoyaltyReward(AppConstants.getValue(offerDetail.getRemoveLoyalty()));
        offer.setMaxBillValue(offerDetail.getMaxBillValue());
        offer.setMaxDiscountAmount(offerDetail.getMaxDiscountAmount());
        if (offerDetail.getAccountsCategory() != null) {
            offer.setAccountsCategory(
                    manager.find(OfferAccountCategory.class, offerDetail.getAccountsCategory().getId()));
        }
        if (offerDetail.isFrequencyApplicable() != null && offerDetail.isFrequencyApplicable()) {
            offer.setFrequencyApplicable(AppConstants.getValue(offerDetail.isFrequencyApplicable()));
            offer.setFrequencyCount(offerDetail.getFrequencyCount());
            offer.setApplicableHour(offerDetail.getApplicableHour());
            offer.setDailyFrequencyCount(offerDetail.getDailyFrequencyCount());
            offer.setFrequencyStrategy(offerDetail.getFrequencyStrategy());
            offer.setMaxQuantity(offerDetail.getMaxQuantity());
        } else {
            offer.setFrequencyApplicable(null);
            offer.setFrequencyCount(null);
            offer.setApplicableHour(null);
            offer.setFrequencyStrategy(null);
            offer.setMaxQuantity(null);
            offer.setDailyFrequencyCount(null);
        }
        offer.setAutoApplicableforUnit(AppConstants.getValue(offerDetail.getAutoApplicableforUnit()));
        offer.setTermsAndConditions(offerDetail.getTermsAndConditions());
        if(Objects.nonNull(offerDetail.getLoyaltyBurnPoints())){
            offer.setLoyaltyBurnPoints(offerDetail.getLoyaltyBurnPoints());
        }
        if(Objects.nonNull(offerDetail.isSignupOfferApplicable())){
            offer.setSignupOfferApplicable(AppConstants.getValue(offerDetail.isSignupOfferApplicable()));
        }
        if (Objects.isNull(offerDetail.getBrandId())) {
            offer.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
        } else {
            offer.setBrandId(offerDetail.getBrandId());
        }
    }

    @Override
    public OfferDetail updateOffer(OfferDetail offerDetail) throws OfferValidationException {
        checkUnitMapping(offerDetail);
        OfferDetailData offer = manager.find(OfferDetailData.class, offerDetail.getId());
        updateOfferDetailData(offer, offerDetail);
        updateCouponStartDateAndEndDate(offerDetail);
        for (MappingIdCodeName mapping : offerDetail.getPartners()) {
            if (mapping.getMappingId() != null && mapping.getMappingId() > 0) {
                updatePartner(mapping, offer);
            } else {
                addPartner(mapping, offer);
            }
        }
        for (IdCodeName mapping : offerDetail.getMetaDataMappings()) {
            if (mapping.getId() > 0) {
                updateMetadata(mapping, offer);
            } else {
                addMetadata(mapping, offer);
            }
        }
        setCouponMappings(offer, offerDetail);
        manager.flush();
        offer = manager.find(OfferDetailData.class, offerDetail.getId());
        return MasterDataConverter.convert(offer, true, false);
    }

    private void updatePartner(MappingIdCodeName mapping, OfferDetailData offer) {
        OfferPartner partnerMapping = manager.find(OfferPartner.class, mapping.getMappingId());
        partnerMapping.setStatus(AppUtils.getCorrectStatus(mapping.getStatus()));
    }

    private OfferPartner addPartner(MappingIdCodeName mapping, OfferDetailData offer) {
        OfferPartner partnerMapping = new OfferPartner();
        partnerMapping.setOffer(offer);
        partnerMapping.setPartner(manager.find(MarketingPartner.class, mapping.getId()));
        partnerMapping.setStatus(AppUtils.getCorrectStatus(mapping.getStatus()));
        manager.persist(partnerMapping);
        return partnerMapping;
    }

    private void addPatners(OfferDetailData offer, OfferDetail offerDetail) {
        if (offer.getPartners() == null) {
            offer.setPartners(new ArrayList<>());
        }
        for (MappingIdCodeName mapping : offerDetail.getPartners()) {
            offer.getPartners().add(addPartner(mapping, offer));
        }
    }

    private void updateMetadata(IdCodeName mapping, OfferDetailData offer) {
        OfferMetadata metadata = manager.find(OfferMetadata.class, mapping.getId());
        metadata.setMappingType(mapping.getName());
        metadata.setMappingValue(mapping.getCode());
        metadata.setStatus(AppUtils.getCorrectStatus(mapping.getStatus()));
    }

    private void addMappings(OfferDetailData offer, OfferDetail offerDetail) {
        if (offer.getMetaDataMappings() == null) {
            offer.setMetaDataMappings(new ArrayList<>());
        }
        for (IdCodeName mapping : offerDetail.getMetaDataMappings()) {
            offer.getMetaDataMappings().add(addMetadata(mapping, offer));
        }
    }

    private OfferMetadata addMetadata(IdCodeName mapping, OfferDetailData offer) {
        OfferMetadata metadata = new OfferMetadata();
        metadata.setOfferId(offer);
        metadata.setMappingType(mapping.getName());
        metadata.setMappingValue(mapping.getCode());
        metadata.setStatus(AppUtils.getCorrectStatus(mapping.getStatus()));
        manager.persist(metadata);
        return metadata;
    }

    @Override
    public List<IdCodeName> getMarketingPartners() {
        List<IdCodeName> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT M FROM MarketingPartner M");
            @SuppressWarnings("unchecked")
            List<MarketingPartner> list = query.getResultList();
            if (list != null) {
                list.forEach(p -> result.add(MasterDataConverter.convert(p)));
            }
        } catch (Exception e) {
            LOG.error("Error while getting Marketing partners", e);
        }
        return result;
    }

    @Override
    public IdCodeName addMarketingPartners(IdCodeName marketingPartner) {
        MarketingPartner partner = new MarketingPartner();
        partner.setPartnerName(marketingPartner.getName());
        partner.setPartnerType(marketingPartner.getType());
        partner.setStatus(AppUtils.getCorrectStatus(marketingPartner.getStatus()));
        manager.persist(partner);
        manager.flush();
        return MasterDataConverter.convert(partner);
    }

    @Override
    public IdCodeName updateMarketingPartners(IdCodeName marketingPartner) {
        MarketingPartner partner = manager.find(MarketingPartner.class, marketingPartner.getId());
        partner.setPartnerName(marketingPartner.getName());
        partner.setPartnerType(marketingPartner.getType());
        partner.setStatus(AppUtils.getCorrectStatus(marketingPartner.getStatus()));
        manager.flush();
        return MasterDataConverter.convert(partner);
    }

    @Override
    public boolean changeMarketingPartnerStatus(int marketingPartnerId, String status) {
        try {
            MarketingPartner partner = manager.find(MarketingPartner.class, marketingPartnerId);
            partner.setStatus(status);
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error while updating partner {} to status {}", marketingPartnerId, status);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public OfferDetail getOffer(int offerId) {
        OfferDetailData offerDetail = manager.find(OfferDetailData.class, offerId);
        return offerDetail == null ? null : MasterDataConverter.convert(offerDetail, true, false);
    }

    @Override
    public OfferDetailData getOfferDetailData(int offerId) {
        return manager.find(OfferDetailData.class, offerId);
    }

    @Override
    public CouponDetail addCoupon(CouponDetail coupon) {
        CouponDetailData couponDetailData = new CouponDetailData();
        setCouponValues(couponDetailData, coupon);
        manager.persist(couponDetailData);
        List<CouponDetailMappingData> mappingsDatalist = setCouponMappings(couponDetailData, coupon);
        // manager.flush();
        return MasterDataConverter.convert(couponDetailData, mappingsDatalist, true, false);
    }

    @Override
    public CouponDetail updateCoupon(CouponDetail coupon, boolean updateMappings) {
        CouponDetailData couponDetailData = manager.find(CouponDetailData.class, coupon.getId());
        List<CouponDetailMappingData> mappingsDatalist = new ArrayList<>();
        if (couponDetailData != null) {
            setCouponValues(couponDetailData, coupon);
            if (updateMappings) {
                mappingsDatalist = setCouponMappings(couponDetailData, coupon);
            }
            manager.flush();
            return MasterDataConverter.convert(couponDetailData, mappingsDatalist, true, false);
        }
        return null;
    }

    private void setCouponValues(CouponDetailData couponDetailData, CouponDetail coupon) {
        couponDetailData.setCouponCode(coupon.getCode());
        couponDetailData.setOfferDetail(manager.find(OfferDetailData.class, coupon.getOffer().getId()));
        couponDetailData.setCouponReuse(AppConstants.getValue(coupon.isReusable()));
        couponDetailData.setCustomerReuse(AppConstants.getValue(coupon.isReusableByCustomer()));
        couponDetailData.setStartDate(AppUtils.getDate(coupon.getStartDate()));
        couponDetailData.setEndDate(AppUtils.getDate(coupon.getEndDate()));
        couponDetailData.setCouponStatus(AppUtils.getCorrectStatus(coupon.getStatus()));
        couponDetailData.setMaxUsage(coupon.getMaxUsage());
        couponDetailData.setMaxCustomerUsage(coupon.getMaxCustomerUsage());
        couponDetailData.setUsageCount(coupon.getUsage());
        couponDetailData.setManualOverride(AppConstants.getValue(coupon.isManualOverride()));
        couponDetailData.setCustomerVisibility(AppConstants.getValue(coupon.getCustomerVisibility(), AppConstants.YES));
    }

    @Override
    public boolean updateBulkCoupon(CouponBulkUpdateDomain couponBulkUpdateDomain){
        if(Objects.nonNull(couponBulkUpdateDomain.getOfferId())) {
            if (Objects.nonNull(couponBulkUpdateDomain.getCouponApplicability()) &&
                    couponBulkUpdateDomain.getCouponApplicability() > 0 &&
                    Objects.nonNull(couponBulkUpdateDomain.getCustomerVisibility())) {
                return setAllCouponApplicability(couponBulkUpdateDomain.getOfferId(),
                        couponBulkUpdateDomain.getCouponApplicability(), couponBulkUpdateDomain.getCustomerVisibility());
            }
            return setAllCouponApplicability(couponBulkUpdateDomain.getOfferId(), null, couponBulkUpdateDomain.getCustomerVisibility());
        }
        return false;
    }

    private boolean setAllCouponApplicability(Integer offerId,Integer days, Boolean customerVisibility){
        try {
            String custVisibility = Boolean.TRUE.equals(customerVisibility) ? AppConstants.YES : AppConstants.NO;
            Query query = manager.createQuery("SELECT max(c.couponDetailId),min(c.couponDetailId) from CouponDetailData c where c.offerDetail.offerDetailId = :offerId");
            query.setParameter("offerId", offerId);
            List<Object[]> minAndMaxKey = query.getResultList();
            if (!CollectionUtils.isEmpty(minAndMaxKey)) {
                Object[] result = minAndMaxKey.get(0);
                int maxKey = (int) result[0];
                int minKey = (int) result[1];
                int batchSize = 500;
                for (int i = minKey; i <= maxKey; i += batchSize) {
                    int max_size = i + batchSize;
                    if (max_size > maxKey) {
                        max_size = maxKey;
                    }
                    Query updateQuery = manager.createQuery("UPDATE CouponDetailData c SET c.couponApplicability = :days, c.customerVisibility = :custVisibility where c.offerDetail.offerDetailId = :offerId And c.couponDetailId <= :maxKey And c.couponDetailId >= :minKey ");
                    updateQuery.setParameter("offerId", offerId);
                    updateQuery.setParameter("minKey", i);
                    updateQuery.setParameter("maxKey", max_size);
                    updateQuery.setParameter("days", days);
                    updateQuery.setParameter("custVisibility", custVisibility);
                    Integer couponDetailDataUpdated = updateQuery.executeUpdate();
                    LOG.info("Updating coupon in batches for all coupons with offer id :::: {}  ::::::and coupon id from {} to {}", offerId,minKey,max_size);
                    manager.flush();
                }
                return true;
            }
        } catch (NoResultException e) {
            LOG.error("No result found to update coupon reusable applicability ::: {}",offerId,e);
            return false;
        }
        return false;
    }

    private List<CouponDetailData> getAllCouponsForOffer(Integer offerId){
        try{
            Query query = manager.createQuery("SELECT c FROM CouponDetailData c where c.offerDetail.offerDetailId = :offerId");
            query.setParameter("offerId", offerId);
            List<CouponDetailData> couponDetailData = query.getResultList();
            LOG.info("Found {} coupon detail for offer id",couponDetailData.size());
            return  couponDetailData;
        }catch (NoResultException e){
            LOG.error("No result found for offer Id ::: {}",offerId,e);
        }
        return null;
    }

    @Override
    public Integer updateAllCoupon(CouponDetail couponDetail){
        Query query = manager.createQuery("UPDATE CouponDetailData c SET c.startDate = :startDate, c.endDate = :endDate," +
                "c.couponStatus= :status, c.couponReuse = :couponReuse, c.customerReuse = :customerReuse, c.maxUsage = :maxUsage," +
                "c.manualOverride = :manualOverride where c.offerDetail.offerDetailId = :offerId");
        query.setParameter("startDate", couponDetail.getStartDate());
        query.setParameter("endDate", couponDetail.getEndDate());
        query.setParameter("status", couponDetail.getStatus());
        query.setParameter("couponReuse", AppConstants.getValue(couponDetail.isReusable()));
        query.setParameter("customerReuse", AppConstants.getValue(couponDetail.isReusableByCustomer()));
        query.setParameter("maxUsage", couponDetail.getMaxUsage());
        query.setParameter("manualOverride", AppConstants.getValue(couponDetail.isManualOverride()));
        query.setParameter("offerId", couponDetail.getOffer().getId());
        return query.executeUpdate();
    }


    private void updateCouponStartDateAndEndDate(OfferDetail offerDetail) {
     List<CouponDetail> couponDetailList=  getOfferCoupons(offerDetail.getId(), false);
     List<CouponDetailData> list = new ArrayList<>();
     if(couponDetailList!=null && couponDetailList.size()>0) {
         couponDetailList.forEach(couponDetail -> {
             couponDetail.setStartDate(AppUtils.getDate(offerDetail.getStartDate()));
             couponDetail.setEndDate(AppUtils.getDate(offerDetail.getEndDate()));
             updateCoupon(couponDetail, false);
         });
     }
    }

    private List<CouponDetailMappingData> setCouponMappings(CouponDetailData couponDetailData, CouponDetail coupon) {
        /*if (couponDetailData.getMappings() == null) {
            couponDetailData.setMappings(new ArrayList<>());
        }*/
        List<CouponDetailMappingData> response = new ArrayList<>();
        coupon.getCouponMappingList()
                .forEach(p -> {
                    response.add(createOrUpdateCouponMappingData(p, couponDetailData));
                });
        return response;
    }

    private void setCouponMappings(OfferDetailData offerDetailData, OfferDetail offer) {
        if (offerDetailData.getMappings() == null) {
            offerDetailData.setMappings(new ArrayList<>());
        }
        offer.getCouponMappingList()
                .forEach(p -> offerDetailData.getMappings().add(createOrUpdateCouponMappingData(p, offerDetailData)));
    }

    private CouponDetailMappingData createOrUpdateCouponMappingData(CouponMapping couponMapping,
                                                                    CouponDetailData couponDetailData) {

        CouponDetailMappingData data = null;

        if (couponMapping.getId() > 0) {
            data = manager.find(CouponDetailMappingData.class, couponMapping.getId());
        }
        if (data == null) {
            data = new CouponDetailMappingData();
            setValues(couponMapping, couponDetailData, data);
            manager.persist(data);
        } else {
            setValues(couponMapping, couponDetailData, data);
        }
        // manager.merge(data);
        // manager.flush();
        return data;
    }

    private OfferDetailMappingData createOrUpdateCouponMappingData(CouponMapping offerMapping,
                                                                   OfferDetailData offerDetailData) {

        OfferDetailMappingData data = null;

        if (offerMapping.getId() > 0) {
            data = manager.find(OfferDetailMappingData.class, offerMapping.getId());
        }
        if (data == null) {
            data = new OfferDetailMappingData();
            setValues(offerMapping, offerDetailData, data);
            manager.persist(data);
        } else {
            setValues(offerMapping, offerDetailData, data);
        }
        // manager.merge(data);
        // manager.flush();
        manager.flush();
        return data;
    }

    private void setValues(CouponMapping couponMapping, CouponDetailData couponDetailData,
                           CouponDetailMappingData data) {
        data.setCouponDetail(couponDetailData.getCouponDetailId());
        data.setMappingType(couponMapping.getType());
        data.setMappingValue(couponMapping.getValue());
        data.setMinValue(couponMapping.getMinValue());
        data.setDimension(couponMapping.getDimension());
        data.setStatus(AppUtils.getCorrectStatus(couponMapping.getStatus()));
        data.setDataType(couponMapping.getDataType());
        data.setMappingGroup(couponMapping.getGroup());
    }

    private void setValues(CouponMapping offerMapping, OfferDetailData offerDetailData,
                           OfferDetailMappingData data) {
        data.setOfferDetail(offerDetailData);
        data.setMappingType(offerMapping.getType());
        data.setMappingValue(offerMapping.getValue());
        data.setMinValue(offerMapping.getMinValue());
        data.setDimension(offerMapping.getDimension());
        data.setStatus(AppUtils.getCorrectStatus(offerMapping.getStatus()));
        data.setDataType(offerMapping.getDataType());
        data.setMappingGroup(offerMapping.getGroup());
    }

    @Override
    public List<String> generateUniqueCoupons(String couponCode, String couponPrefix, int replicateCount) throws DataUpdationException {
        CouponDetail modelCoupon = getCouponDetail(couponCode, true, false, false);
        if (modelCoupon == null) {
            throw new DataUpdationException("Coupon Code Does Not Exists");

        }
        if (modelCoupon.getOffer().getOfferScope().equals("MASS") && replicateCount > 1) {
            throw new DataUpdationException("Cannot create more than 1 offer for offer scope MASS");
        }
        Set<String> existingCoupons = new HashSet<>();
        Set<String> couponCodeSet = new RandomStringGenerator().getRandomCode(couponPrefix, 6, replicateCount,
                existingCoupons);
        modelCoupon = removeIds(modelCoupon);
        List<String> couponList = new ArrayList<>();
        for (String code : couponCodeSet) {
            modelCoupon.setCode(code);
            // Replicating Coupon
            addCoupon(modelCoupon);
            couponList.add(code);
        }
        return couponList;
    }

    @Override
    public List<String> getUniqueCoupons(String couponCode, String couponPrefix, int replicateCount)
            throws DataUpdationException {
        CouponDetail modelCoupon = getCouponDetail(couponCode, true, false, false);
        if (modelCoupon.getOffer().getOfferScope().equals("MASS") && replicateCount > 1) {
            throw new DataUpdationException("Cannot create more than 1 offer for offer scope MASS");

        }
        Set<String> existingCoupons = new HashSet<>(getExistingCoupons(couponPrefix));
        Set<String> couponCodeSet = new RandomStringGenerator().getRandomCode(couponPrefix, 6, replicateCount,
                existingCoupons);
        return new ArrayList<>(couponCodeSet);
    }

	@Override
	public CouponDetail getModalCoupon(String modelCouponCode) {
		CouponDetail modelCoupon = getCouponDetail(modelCouponCode, true, false, false);
		if (modelCoupon == null) {
			return null;
		}
		modelCoupon = removeIds(modelCoupon);
		return modelCoupon;
	}

    @Override
    public List<String> generateCopyCoupons(CouponDetail modelCoupon, List<String> couponCodeSet) throws DataUpdationException {
        List<String> couponList = new ArrayList<>();
        for (String code : couponCodeSet) {
            modelCoupon.setCode(code);
            // Replicating Coupon
            addCoupon(modelCoupon);
            couponList.add(code);
        }
        return couponList;
    }


    private CouponDetail removeIds(CouponDetail modelCoupon) {
        modelCoupon.setId(0);
        for (int i = 0; i < modelCoupon.getCouponMappingList().size(); i++) {
            modelCoupon.getCouponMappingList().get(i).setId(0);
        }
        return modelCoupon;
    }

    private List<String> getExistingCoupons(String prefix) {
        List<String> coupons = new ArrayList<>();
        Query query = manager.createQuery("SELECT data From CouponDetailData data where data.couponCode like :couponCode");
        query.setParameter("couponCode", prefix + "%");
        @SuppressWarnings("unchecked")
        List<CouponDetailData> resultList = query.getResultList();
        for (CouponDetailData data : resultList) {
            coupons.add(data.getCouponCode());
        }
        return coupons;
    }

    @Override
    public boolean addCouponMapping(String couponCode, CouponMappingType mappingType, String value) {
        try {
            Query query = manager.createQuery("SELECT data From CouponDetailData data where data.couponCode = :couponCode");
            query.setParameter("couponCode", couponCode);
            CouponDetailData couponData = (CouponDetailData) query.getSingleResult();
            // TODO dynamically create mapping types
            CouponDetailMappingData mappingData = getDummyCouponMappingType(couponData, mappingType, value, null);
            manager.persist(mappingData);
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error while Adding a Mapping for coupon:{}", couponCode);
        }
        return false;
    }

    private CouponDetailMappingData getDummyCouponMappingType(CouponDetailData couponDetailData,
                                                              CouponMappingType mappingType, String value, String dimension) {
        CouponDetailMappingData data = new CouponDetailMappingData();
        data.setCouponDetail(couponDetailData.getCouponDetailId());
        data.setMappingType(mappingType.name());
        data.setMappingValue(value);
        data.setMinValue("1");
        data.setDimension(dimension);
        data.setStatus(AppConstants.ACTIVE);
        data.setDataType(Integer.class.getCanonicalName());
        data.setMappingGroup(1);
        return null;
    }

    @Override
    public IdCodeName getMarketingPartner(String key) {
        IdCodeName result = null;
        try {
            Query query = manager.createQuery("SELECT M From MarketingPartner M WHERE M.authorizationKey = :key");
            query.setParameter("key", key);
            MarketingPartner partner = (MarketingPartner) query.getSingleResult();
            result = MasterDataConverter.convert(partner);
        } catch (Exception e) {
            LOG.error("Error while getting Marketing partners with key {}", key, e);
        }
        return result;
    }

    @Override
    public boolean updateCouponMapping(int couponMappingId, String status) {
        CouponDetailMappingData mapping = manager.find(CouponDetailMappingData.class, couponMappingId);
        if (mapping != null) {
            mapping.setStatus(status);
            manager.flush();
            return true;
        }
        return false;
    }

    @Override
    public void updateCouponUsageByOne(int customerId, String offerCode, int orderId) {
        LOG.info("Updating coupon usage for coupon code: {}", offerCode);
        Query query = manager.createQuery(
                "UPDATE CouponDetailData c SET c.usageCount = c.usageCount + 1 WHERE c.couponCode = :offerCode");
        query.setParameter("offerCode", offerCode);
        query.executeUpdate();
    }

    @Override
    public boolean updateCouponMappings(List<CouponDetail> coupons) {
        for (CouponDetail coupon : coupons) {
            updateCouponMappingData(coupon);
        }
        return true;
    }

    private void updateCouponMappingData(CouponDetail coupon) {
        CouponDetailData couponDetailData = manager.find(CouponDetailData.class, coupon.getId());
        if (couponDetailData != null) {
            setCouponMappings(couponDetailData, coupon);
            manager.flush();
        }
    }

    @Override
    public List<IdName> getOfferAccountsCategories() {
        return Optional.ofNullable(findAll(OfferAccountCategory.class)).map(Collection::stream).orElse(Stream.empty())
                .map(e -> MasterDataConverter.convertToIdName(e)).collect(Collectors.toList());
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.offer.dao.OfferManagementDao#
     * setContactNumber(java.lang.String, java.lang.String)
     */
    @Override
    public void setContactNumber(String contactNumber, String couponCode) {
        CouponDetailData couponDetailData = getCoupon(couponCode);
        if (couponDetailData != null) {
            deactivateAllMappings(couponDetailData.getCouponDetailId(), CouponMappingType.CONTACT_NUMBER);
            createOrUpdateCouponMappingData(createCustomerCouponMapping(contactNumber), couponDetailData);
        }
        couponDetailData.setCouponStatus(AppConstants.ACTIVE);
        manager.flush();
    }

    /**
     * @param couponDetailId
     */
    private void deactivateAllMappings(Integer couponDetailId, CouponMappingType mappingType) {
        LOG.info("Deactivating coupon Mapping for coupon id {} and mapping type {}", couponDetailId, mappingType);
        Query query = manager.createQuery(
                "UPDATE CouponDetailMappingData c SET c.status = :status where c.couponDetail = :couponDetailId and c.mappingType = :mappingType");
        query.setParameter("status", AppConstants.IN_ACTIVE);
        query.setParameter("couponDetailId", couponDetailId);
        query.setParameter("mappingType", mappingType.name());
        query.executeUpdate();
        manager.flush();
    }

    /**
     * @param contactNumber
     * @return
     */
    private CouponMapping createCustomerCouponMapping(String contactNumber) {
        CouponMapping mapping = new CouponMapping();
        mapping.setDataType(String.class.getCanonicalName());
        mapping.setGroup(1);
        mapping.setMinValue("1");
        mapping.setStatus(AppConstants.ACTIVE);
        mapping.setType(CouponMappingType.CONTACT_NUMBER.name());
        mapping.setValue(contactNumber);
        return mapping;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.offer.dao.OfferManagementDao#
     * setUsageCount(java.lang.String, int)
     */
    @Override
    public void setUsageCountOfAllCouponsForAnOffer(int offerId, int count) {
        LOG.info("updating max usage count of all coupons with offer id {} to {}", offerId, count);
        Query query = manager.createQuery(
                "UPDATE CouponDetailData c SET c.maxUsage = :count, c.usageCount = 0 where c.offerDetail.offerDetailId = :offerId and c.couponStatus = :couponStatus");
        query.setParameter("couponStatus", AppConstants.ACTIVE);
        query.setParameter("count", count);
        query.setParameter("offerId", offerId);
        query.executeUpdate();
        manager.flush();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.offer.dao.OfferManagementDao#
     * getAllInactiveCoupons(int)
     */
    @Override
    public List<CouponDetailData> getAllInactiveCoupons(int offerId) {
        Query query = manager.createQuery(
                "SELECT data From CouponDetailData data where data.offerDetail.offerDetailId = :offerDetailId and data.couponStatus = :couponStatus");
        query.setParameter("offerDetailId", offerId);
        query.setParameter("couponStatus", AppConstants.IN_ACTIVE);
        return query.getResultList();
    }

    @Override
    public Set<String> getAllCustomerWithOffer(Integer offerDetailId) {
        Set<String> coupons = new HashSet<>();
        Query query = manager.createQuery(""
                + "select mapping.mappingValue from CouponDetailData data, CouponDetailMappingData mapping " +
                "where data.couponDetailId = mapping.couponDetail " +
                "and data.offerDetail.offerDetailId = :offerDetailId " +
                "and data.couponStatus = :couponStatus " +
                "and mapping.mappingType = :mappingType " +
                "and mapping.status = :mappingStatus");
        query.setParameter("offerDetailId", offerDetailId);
        query.setParameter("couponStatus", AppConstants.ACTIVE);
        query.setParameter("mappingType", CouponMappingType.CONTACT_NUMBER.name());
        query.setParameter("mappingStatus", AppConstants.ACTIVE);

        @SuppressWarnings("unchecked")
        List<Object> resultList = query.getResultList();
        if (resultList != null && resultList.size() > 0) {
            for (Object data : resultList) {
                coupons.add((String) data);
            }
        }
        return coupons;
    }

    @Override
    public List<CouponDetailData> getCouponForCustomer(String contactNumber, String date, Integer brandId){
        List<CouponDetailData> couponDetailDataList = new ArrayList<>();


        Query query = manager.createQuery(""
                + "select mapping from  CouponDetailMappingData mapping " +
                "where mapping.mappingValue = :mappingValue " +
                "and mapping.mappingType = :mappingType " +
                "and mapping.status = :mappingStatus");
        query.setParameter("mappingValue", contactNumber);
        query.setParameter("mappingType", CouponMappingType.CONTACT_NUMBER.name());
        query.setParameter("mappingStatus", AppConstants.ACTIVE);
        List<Object> resultList = query.getResultList();
        if (resultList != null && resultList.size() > 0) {
            Set<Integer> couponDetailIds = resultList.stream().map(o ->
                ((CouponDetailMappingData) o).getCouponDetail()
            ).collect(Collectors.toSet());
            query = manager.createQuery("select c from  CouponDetailData c where c.couponDetailId in (:couponDetailIds) " +
                    "and c.startDate <= :date and c.endDate >= :date and c.couponStatus = :couponStatus and c.maxUsage > c.usageCount " +
                    "AND c.offerDetail.brandId = :brandId");
            query.setParameter("couponDetailIds",couponDetailIds);
            query.setParameter("date", AppUtils.parseDateSimple(date));
            query.setParameter("couponStatus", AppConstants.ACTIVE);
            query.setParameter("brandId", brandId);
            resultList = query.getResultList();
            couponDetailDataList = resultList.stream().map(o -> (CouponDetailData) o).toList();
        }
        return couponDetailDataList;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.offer.dao.OfferManagementDao#
     * getAllValidOffersWithLaunchStrategy()
     */
    @Override
    public List<OfferDetailData> getAllValidOffersWithLaunchStrategy(List<String> launchStrategy) {
        Query query = manager.createQuery(
                "SELECT data From OfferDetailData data where data.offerStatus = :offerStatus and data.launchStartDate <= :currentDate and  data.launchEndDate >= :currentDate and data.launchStrategy IN (:launchStrategy)");
        query.setParameter("offerStatus", AppConstants.ACTIVE);
        query.setParameter("currentDate", AppUtils.getDate(AppUtils.getCurrentBusinessDate()));
        query.setParameter("launchStrategy", launchStrategy);
        return query.getResultList();
    }

    @Override
    public HourlyOfferUnitMapping getHourlyOfferUnitMapping(int offerDetailId) {
        OfferDetailData offer = manager.find(OfferDetailData.class, offerDetailId);
        int days = 365;
        List<Integer> unitIds = new ArrayList<>();
        for (OfferDetailMappingData metadata : offer.getMappings()) {
            if (CouponMappingType.UNIT.name().equals(metadata.getMappingType())) {
                unitIds.add(Integer.valueOf(metadata.getMappingValue()));
            }
            if (CouponMappingType.NUMBER_OF_DAYS.name().equals(metadata.getMappingType())) {
                days = Integer.valueOf(metadata.getMappingValue());
            }
        }
        HourlyOfferUnitMapping hourlyOfferUnitMapping = new HourlyOfferUnitMapping();
        hourlyOfferUnitMapping.setNumberOfDays(days);
        hourlyOfferUnitMapping.setUnitIds(unitIds);
        return hourlyOfferUnitMapping;
    }

    @Override
    public List<OfferDetailData> getAllOffersWithLaunchStrategy(List<String> launchStrategy) {
        Query query = manager.createQuery(
                "select data From OfferDetailData data where data.offerStatus = :offerStatus and data.startDate <= :currentDate and  data.endDate >= :currentDate and data.launchStrategy IN (:launchStrategy)");
        query.setParameter("offerStatus", AppConstants.ACTIVE);
        query.setParameter("launchStrategy", launchStrategy);
        query.setParameter("currentDate", AppUtils.getDate(AppUtils.getCurrentBusinessDate()));
        return query.getResultList();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.offer.dao.OfferManagementDao#
     * deactiveOffer(java.lang.Integer)
     */
    @Override
    public void deactiveOffer(Integer offerDetailId) {
        OfferDetailData data = manager.find(OfferDetailData.class, offerDetailId);
        data.setOfferStatus(AppConstants.IN_ACTIVE);
        manager.flush();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.offer.dao.OfferManagementDao#
     * getAllPendingNotificationMassOffers(java.util.Date)
     */
    @Override
    public List<CustomerOfferMappingData> getAllPendingNotificationMassOffers(int offerId, Date endTime) {
        Query query = manager.createQuery(
                "select data From CustomerOfferMappingData data where data.notified = :notified and data.creationTime <= :endTime and data.offerDetailId = :offerId");
        query.setParameter("notified", AppConstants.NO);
        query.setParameter("endTime", endTime);
        query.setParameter("offerId", offerId);
        return query.getResultList();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.offer.dao.OfferManagementDao#
     * setCustomerOfferMappingDataNotified(java.lang.Integer)
     */
    @Override
    public void setCustomerOfferMappingDataNotified(Integer customerOfferMappingDataId) {
        CustomerOfferMappingData mapping = manager.find(CustomerOfferMappingData.class, customerOfferMappingDataId);
        mapping.setNotified(AppConstants.YES);
        manager.flush();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.master.core.external.offer.dao.OfferManagementDao#
     * getFreeItemOffer(java.util.Date)
     */
    @Override
    public CouponDetail getFreeItemOffer(Date businessDate) {
        Query query = manager.createQuery(
                "select data From OfferDetailData data where data.offerType = :offerType and data.offerStatus = :offerStatus and data.offerScope = :offerScope and :businessDate >= data.startDate and :businessDate <= data.endDate");
        query.setParameter("offerType", OfferCategoryType.OFFER_WITH_FREE_ITEM_STRATEGY.name());
        query.setParameter("offerStatus", AppConstants.ACTIVE);
        query.setParameter("offerScope", "MASS");
        query.setParameter("businessDate", businessDate);
        List<OfferDetailData> offers = query.getResultList();
        if (offers != null && offers.size() > 0) {
            OfferDetailData offer = offers.get(0);
            Query query1 = manager.createQuery(
                    "select data From CouponDetailData data where data.offerDetail.offerDetailId = :offerDetailId " +
                            "and data.couponStatus = :couponStatus and :businessDate >= data.startDate and :businessDate <= data.endDate");
            query1.setParameter("offerDetailId", offer.getOfferDetailId());
            query1.setParameter("couponStatus", AppConstants.ACTIVE);
            query1.setParameter("businessDate", businessDate);
            List<CouponDetail> coupons = getOfferCoupons(offer.getOfferDetailId(), false);
            if (coupons != null && coupons.size() > 0) {
                return coupons.get(0);
            }
        }

        return null;
    }

    @Override
    public void markExpiredOffersAsArchived() {
        LOG.info("Updating expired Offers as ARCHIVED");
        Query query = manager
                .createQuery("UPDATE OfferDetailData e SET e.offerStatus = :status WHERE e.endDate < :archivingDate");
        query.setParameter("status", AppConstants.ARCHIVED);
        query.setParameter("archivingDate", AppUtils.getDayBeforeOrAfterCurrentDay(-60));
        query.executeUpdate();
    }

    public boolean existCustomerOfferMappingData(int offerDetailId, String contactNumber) {
        Query query = manager.createQuery(
                "select data From CustomerOfferMappingData data where data.offerDetailId = :offerDetailId and data.contactNumber = :contactNumber");
        query.setParameter("offerDetailId", offerDetailId);
        query.setParameter("contactNumber", contactNumber);
        List<CustomerOfferMappingData> list = query.getResultList();
        return list != null && list.size() > 0;
    }

    public List<CustomerOfferMappingData> existCustomerOfferMappingDataList(int offerDetailId, String contactNumber) {
        Query query = manager.createQuery(
                "select data From CustomerOfferMappingData data where data.offerDetailId = :offerDetailId and data.contactNumber = :contactNumber");
        query.setParameter("offerDetailId", offerDetailId);
        query.setParameter("contactNumber", contactNumber);
        List<CustomerOfferMappingData> list = query.getResultList();
        return list != null && list.size() > 0 ? list : null;
    }

    @Override
    public LaunchOfferData availLaunchOffer(EnvType env, LaunchOfferData input, boolean addUnitMapping) {
        Query query = manager.createQuery(
                "select data From CustomerOfferMappingData data where data.offerDetailId = :offerDetailId and data.offerCodeUsed = :offerCodeUsed order by data.couponCode");
        query.setParameter("offerDetailId", input.getOfferDetailId());
        query.setParameter("offerCodeUsed", AppConstants.NO);
        List<CustomerOfferMappingData> list = query.getResultList();
        if (list != null && list.size() > 0) {
            OfferDetailData offer = manager.find(OfferDetailData.class, input.getOfferDetailId());
            if (list.size() < 1000 && list.size() % 100 == 0) {
                SlackNotificationService.getInstance().sendNotification(env, ApplicationName.KETTLE_SERVICE.name(),
                        SlackNotification.TECHNOLOGY, "ACTION REQUIRED : Threshold of number of coupons breached for "
                                + offer.getOfferDescription() + " and the pending coupon count is " + list.size());
            }
            CustomerOfferMappingData mapping = list.get(0);
            mapping.setAcquisitionSource(input.getAcquisitionSource());
            mapping.setContactNumber(input.getContactNumber());
            mapping.setCreationTime(AppUtils.getCurrentTimestamp());
            mapping.setCustomerName(input.getCustomerName());
            mapping.setOfferCodeUsed(AppConstants.YES);
            manager.flush();
            Date today = AppUtils.getBusinessDate();
			Date businessDate = input.getStartDate() != null && input.getStartDate().after(today) ? input.getStartDate()
					: today;
            Date endDate = AppUtils.getDayBeforeOrAfterDay(businessDate, mapping.getOfferDayCount()-1);
            CouponDetailData coupon = manager.find(CouponDetailData.class, mapping.getCouponDetailId());
            Date couponEndDate = AppUtils.isBefore(coupon.getEndDate(),endDate) ? coupon.getEndDate() : endDate;
            coupon.setStartDate(businessDate);
            coupon.setEndDate(couponEndDate);
            coupon.setCouponStatus(AppConstants.ACTIVE);
            manager.flush();
            CouponDetailMappingData couponMapping = new CouponDetailMappingData();
            couponMapping.setCouponDetail(coupon.getCouponDetailId());
            couponMapping.setDataType(String.class.getCanonicalName());
            couponMapping.setMappingGroup(1);
            couponMapping.setMappingType(CouponMappingType.CONTACT_NUMBER.name());
            couponMapping.setMappingValue(input.getContactNumber());
            couponMapping.setMinValue("1");
            couponMapping.setStatus(AppConstants.ACTIVE);
            manager.persist(couponMapping);
            manager.flush();
            if (addUnitMapping) {
                CouponDetailMappingData couponUnitMapping = new CouponDetailMappingData();
                couponUnitMapping.setCouponDetail(coupon.getCouponDetailId());
                couponUnitMapping.setDataType(String.class.getCanonicalName());
                couponUnitMapping.setMappingGroup(1);
                couponUnitMapping.setMappingType(CouponMappingType.UNIT.name());
                couponUnitMapping.setMappingValue(input.getUnitId() + "");
                couponUnitMapping.setMinValue("1");
                couponUnitMapping.setStatus(AppConstants.ACTIVE);
                manager.persist(couponUnitMapping);
                manager.flush();
            }
            input.setCouponCode(mapping.getCouponCode());
            input.setEndDate(coupon.getEndDate());
            input.setOfferText(offer.getOfferText());
            input.setStartDate(coupon.getStartDate());
            input.setOfferDayCount(mapping.getOfferDayCount());
            input.setUrlEndPoint(mapping.getUrlEndPoint());
        } else {
            input.setErrorMessage("This offer has expired. Visit cafe for exiting in store offers.");
        }
        return input;
    }

	@Override
	public Set<String> getCurrentCycleWinners(List<LaunchOfferStrategy> launchOfferStrategy, Date currentCycleCutOff) {
		StringBuilder queryStr = new StringBuilder("SELECT DISTINCT A.contactNumber FROM ");
		queryStr.append("CustomerOfferMappingData A, OfferDetailData B ")
				.append("WHERE A.offerDetailId = B.offerDetailId ")
				.append("AND B.startDate  > :currentCycleCutOff and A.offerStrategy IN (:offerStrategy)");
		Query query = manager.createQuery(queryStr.toString());
		query.setParameter("currentCycleCutOff", currentCycleCutOff);
		List<String> strategies = new ArrayList<String>();
		launchOfferStrategy.forEach(p -> {
			strategies.add(p.name());
		});
		query.setParameter("offerStrategy", strategies);
		Set<String> contacts = new HashSet<>(query.getResultList());
		return contacts;
	}

    @Override
    public List<AppOfferDetail> getActInactOffers() {
        List<AppOfferDetail> offers = new ArrayList<>();
        List<AppOfferDetailData> appOfferDetailDataList = null;
        try {
            Query query = manager.createQuery("select data From AppOfferDetailData data where data.endDate >= :currentDate");
            query.setParameter("currentDate", AppUtils.getDate(AppUtils.getCurrentBusinessDate()));
            //query.setParameter("active", "ACTIVE");
            appOfferDetailDataList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting All App Offers {}", e);
        }
        if (appOfferDetailDataList != null) {
            for (AppOfferDetailData offerDetailData : appOfferDetailDataList) {
                List<AppOfferMappingData> offerMappingDataList = getAppOfferMappingDataList(offerDetailData.getId());
                offers.add(MasterDataConverter.convert(offerDetailData, offerMappingDataList));
            }
//            appOfferDetailDataList.forEach(data ->
//                    offers.add(MasterDataConverter.convert(data)));
        }
        return offers;
    }

    @Override
    public List<AppOfferDetail> getAppOffersByPartnerId(Integer partnerId) {
        List<AppOfferDetail> offers = new ArrayList<>();
        List<AppOfferDetailData> appOfferDetailDataList = null;
        try {
            Query query = manager.createQuery("select data From AppOfferDetailData data where data.endDate >= :currentDate " +
                "AND data.partnerId = :partnerId");
            query.setParameter("currentDate", AppUtils.getDate(AppUtils.getCurrentBusinessDate()));
            query.setParameter("partnerId", partnerId);
            appOfferDetailDataList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting All App Offers {}", e);
        }
        if (appOfferDetailDataList != null) {
            for (AppOfferDetailData offerDetailData : appOfferDetailDataList) {
                List<AppOfferMappingData> offerMappingDataList = getAppOfferMappingDataList(offerDetailData.getId());
                List<AppOfferApplicabilityData> offerApplicabilityDataList = getAppOfferApplicabilityData(offerDetailData.getId());
                offerDetailData.setAppOfferApplicabilityDataList(offerApplicabilityDataList);
                offers.add(MasterDataConverter.convert(offerDetailData, offerMappingDataList));
            }
        }
        return offers;
    }

    @Override
    public AppOfferDetail addNewOffer(AppOfferDetail offerDetail) {
        AppOfferDetailData offerDetailData = null;
        List<AppOfferMappingData> offerMappingDataList = null;
        try {
            offerDetailData = new AppOfferDetailData();
            offerDetailData.setOfferValue(offerDetail.getOfferValue());
            offerDetailData.setMov(offerDetail.getMov());
            offerDetailData.setOfferCategory(offerDetail.getOfferCategory());
            offerDetailData.setOfferOrderType(offerDetail.getOfferOrderType());
            offerDetailData.setMaxDiscount(offerDetail.getMaxDiscount());
            offerDetailData.setOfferType(offerDetail.getOfferType());
            offerDetailData.setOfferIndex(offerDetail.getOfferIndex());
            offerDetailData.setDescription(offerDetail.getDescription());
            offerDetailData.setStartDate(offerDetail.getStartDate());
            offerDetailData.setEndDate(offerDetail.getEndDate());
            offerDetailData.setStatus(offerDetail.getStatus());
            offerDetailData.setOfferId(offerDetail.getOfferId());
            offerDetailData.setActionType(offerDetail.getActionType());
            offerDetailData.setCouponCode(offerDetail.getCouponCode());
            offerDetailData.setCouponId(offerDetail.getCouponId());
            offerDetailData.setCreatedBy(offerDetail.getCreatedBy());
            offerDetailData.setGridImage(offerDetail.getGridImage());
            offerDetailData.setRedirectionLink(offerDetail.getRedirectionLink());
            offerDetailData.setTitle(offerDetail.getTitle());
            offerDetailData.setActionCategory(offerDetail.getActionCategory());
//            offerDetailData.setGridImageUrl(offerDetail.getGridImageUrl());
            offerDetailData.setListImage(offerDetail.getListImage());
//            offerDetailData.setListImageUrl(offerDetail.getListImageUrl());
            offerDetailData.setCreateTime(AppUtils.getCurrentTimestamp());
            offerDetailData.setMenuCategoryId(offerDetail.getMenuCategoryId());
            offerDetailData.setPartnerId(offerDetail.getPartnerId());
            if(Objects.isNull(offerDetail.getAppOfferType())) {
                offerDetailData.setAppOfferType(AppOfferType.GENERIC_OFFER.value());
            } else {
                offerDetailData.setAppOfferType(offerDetail.getAppOfferType().value());
            }
            if (Objects.isNull(offerDetail.getBrandId())) {
                offerDetailData.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
            } else {
                offerDetailData.setBrandId(offerDetail.getBrandId());
            }

            final AppOfferDetailData finalOfferDetail = offerDetailData;
            if(!Objects.isNull(offerDetail.getAppOfferApplicabilityList())){
                offerDetailData.setAppOfferApplicabilityDataList(offerDetail.getAppOfferApplicabilityList().stream().map(e->MasterDataConverter.convert(e,finalOfferDetail)).collect(Collectors.toList()));
            }
            offerDetailData = add(offerDetailData);

            AppOfferMappingData offerMappingData = null;
            if (offerDetailData != null && offerDetail.getUnitDetailList() != null) {
                offerMappingDataList = new ArrayList<>();
                for (AppOfferUnitDetail unitDetail : offerDetail.getUnitDetailList()) {
                    offerMappingData = new AppOfferMappingData();
                    offerMappingData.setMappingType(AppConstants.BATCH_CODE_KEY_TYPE_UNIT);
                    offerMappingData.setMappingValue(unitDetail.getUnitId());
                    offerMappingData.setMappingUnitName(unitDetail.getUnitName());
                    offerMappingData.setMappingStatus(AppConstants.ACTIVE);
                    offerMappingData.setUpdateTime(AppUtils.getCurrentTimestamp());
                    offerMappingData.setUpdatedBy(offerDetailData.getCreatedBy());
                    offerMappingData.setAppOfferDetailData(offerDetailData);
                    offerMappingData = add(offerMappingData);
                    offerMappingDataList.add(offerMappingData);
                    LOG.info("offer mapping Id ", offerMappingData.getOfferMappingId());
                }
            }
        } catch (Exception e) {
            LOG.error("Error adding " + offerDetailData.getClass().getName() + " {}", e.getMessage(), e);
        }
        return MasterDataConverter.convert(offerDetailData, offerMappingDataList);
    }

    @Override
    public AppOfferDetail updateAppOffer(AppOfferDetail offerDetail) {

        AppOfferDetailData offerDetailData = manager.find(AppOfferDetailData.class, offerDetail.getAppOfferId());
        List<AppOfferMappingData> offerMappingDataList = null;
        if (offerDetailData != null) {
            try {

                offerDetailData.setOfferType(offerDetail.getOfferType());
                offerDetailData.setOfferIndex(offerDetail.getOfferIndex());
                offerDetailData.setDescription(offerDetail.getDescription());
                offerDetailData.setStartDate(offerDetail.getStartDate());
                offerDetailData.setEndDate(offerDetail.getEndDate());
                offerDetailData.setStatus(offerDetail.getStatus());
                offerDetailData.setOfferId(offerDetail.getOfferId());
                offerDetailData.setActionType(offerDetail.getActionType());
                offerDetailData.setCouponCode(offerDetail.getCouponCode());
                offerDetailData.setCouponId(offerDetail.getCouponId());
                offerDetailData.setGridImage(offerDetail.getGridImage());
                offerDetailData.setRedirectionLink(offerDetail.getRedirectionLink());
                offerDetailData.setTitle(offerDetail.getTitle());
                offerDetailData.setActionCategory(offerDetail.getActionCategory());
//                offerDetailData.setGridImageUrl(offerDetail.getGridImageUrl());
                offerDetailData.setListImage(offerDetail.getListImage());
//                offerDetailData.setListImageUrl(offerDetail.getListImageUrl());
                offerDetailData.setUpdatedBy(offerDetail.getUpdatedBy());
                offerDetailData.setUpdateTime(AppUtils.getCurrentTimestamp());
                offerDetailData.setMenuCategoryId(offerDetail.getMenuCategoryId());
                offerDetailData.setPartnerId(offerDetail.getPartnerId());
                if(Objects.isNull(offerDetail.getAppOfferType())) {
                    offerDetailData.setAppOfferType(AppOfferType.GENERIC_OFFER.value());
                } else {
                    offerDetailData.setAppOfferType(offerDetail.getAppOfferType().value());
                }
                if (Objects.isNull(offerDetail.getBrandId())) {
                    offerDetailData.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
                } else {
                    offerDetailData.setBrandId(offerDetail.getBrandId());
                }
                final AppOfferDetailData finalOfferDetailData = offerDetailData;
                if (Objects.nonNull(offerDetail.getAppOfferApplicabilityList())
                        && !offerDetail.getAppOfferApplicabilityList().isEmpty()) {
                    offerDetailData.setAppOfferApplicabilityDataList(
                            offerDetail.getAppOfferApplicabilityList().stream().map(e -> MasterDataConverter.convert(e, finalOfferDetailData)).collect(Collectors.toList()));
                }

                offerDetailData = update(offerDetailData);

                AppOfferMappingData offerMappingData = null;

                List<AppOfferMappingData> appOfferMappingDataList = getAppOfferMappingDataList(offerDetailData.getId());

                for (AppOfferMappingData mappingData : appOfferMappingDataList) {
                    mappingData.setUpdateTime(AppUtils.getCurrentTimestamp());
                    mappingData.setUpdatedBy(offerDetailData.getUpdatedBy());
                    mappingData.setMappingStatus(AppConstants.IN_ACTIVE);
                    mappingData.setAppOfferDetailData(offerDetailData);
                    update(mappingData);
                }

                if (offerDetailData != null && offerDetail.getUnitDetailList() != null) {
                    offerMappingDataList = new ArrayList<>();
                    for (AppOfferUnitDetail unitDetail : offerDetail.getUnitDetailList()) {

                        offerMappingData = getOfferMappingUnitData(unitDetail.getUnitId(), offerDetail.getAppOfferId());

                        if (offerMappingData != null) {
                            offerMappingData.setMappingUnitName(unitDetail.getUnitName());
                            offerMappingData.setUpdateTime(AppUtils.getCurrentTimestamp());
                            offerMappingData.setUpdatedBy(offerDetailData.getUpdatedBy());
                            offerMappingData.setAppOfferDetailData(offerDetailData);
                            offerMappingData.setMappingStatus(AppConstants.ACTIVE);
                            offerMappingData = update(offerMappingData);

                            offerMappingDataList.add(offerMappingData);
                            LOG.info("offer mapping Id ", offerMappingData.getOfferMappingId());
                        } else {
                            offerMappingData = new AppOfferMappingData();
                            offerMappingData.setMappingType(AppConstants.BATCH_CODE_KEY_TYPE_UNIT);
                            offerMappingData.setMappingValue(unitDetail.getUnitId());
                            offerMappingData.setMappingUnitName(unitDetail.getUnitName());
                            offerMappingData.setMappingStatus(AppConstants.ACTIVE);
                            offerMappingData.setUpdateTime(AppUtils.getCurrentTimestamp());
                            offerMappingData.setUpdatedBy(offerDetailData.getUpdatedBy());
                            offerMappingData.setAppOfferDetailData(offerDetailData);
                            offerMappingData = add(offerMappingData);

                            offerMappingDataList.add(offerMappingData);
                            LOG.info("offer mapping Id ", offerMappingData.getOfferMappingId());
                        }

                    }
                }
            } catch (Exception e) {
                LOG.error("Error while updating " + offerDetailData.getClass().getName() + " {}", e.getMessage(), e);
            }
        }
        return MasterDataConverter.convert(offerDetailData, offerMappingDataList);
    }

    @Override
    public AppOfferMappingData getOfferMappingUnitData(Integer unitId, Integer appOfferId) {
        Query query = manager.createQuery("select X FROM AppOfferMappingData X where" +
                " X.appOfferDetailData.id = :appOfferId AND X.mappingValue = :mappingValue");
        query.setParameter("appOfferId", appOfferId);
        query.setParameter("mappingValue", unitId);
        try {
            return (AppOfferMappingData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public SignupOffersCouponDetails getCouponDetails() {
        Query query =manager.createQuery("select data FROM SignupOffersCouponDetails data where data.couponStatus = :couponStatus");
        query.setParameter("couponStatus",AppConstants.ACTIVE);
        return (SignupOffersCouponDetails) query.getSingleResult();
    }

    @Override
    public List<SignupOffersCouponDetails> changeSignupStatus() {
        Query query =manager.createQuery("select data FROM SignupOffersCouponDetails data where data.couponStatus = :couponStatus");
        query.setParameter("couponStatus",AppConstants.ACTIVE);
        List<SignupOffersCouponDetails> offersCouponDetails = query.getResultList();
        return offersCouponDetails;
    }

    @Override
    public List<AppOfferMappingData> getAppOfferMappingDataList(Integer appOfferId) {
        Query query = manager.createQuery("select X FROM AppOfferMappingData X where X.appOfferDetailData.id = :appOfferId" +
                " AND X.mappingStatus=:mappingStatus");
        query.setParameter("appOfferId", appOfferId);
        query.setParameter("mappingStatus",AppConstants.ACTIVE);
        List<AppOfferMappingData> appOfferMappingDataList = query.getResultList();
        return appOfferMappingDataList;
    }

    @Override
    public List<AppOfferApplicabilityData> getAppOfferApplicabilityData(Integer appOfferId) {
        Query query = manager.createQuery("select X FROM AppOfferApplicabilityData X where X.appOfferDetailData.id = :appOfferId");
        query.setParameter("appOfferId", appOfferId);
        List<AppOfferApplicabilityData> appOfferMappingDataList = query.getResultList();
        return appOfferMappingDataList;
    }

    @Override
    public Boolean setStatusForAppOffer(IdCodeName idCodeName) {
        String status;
        if (idCodeName.getStatus().equalsIgnoreCase("ACTIVE")) {
            status = AppConstants.ACTIVE;
        } else {
            status = AppConstants.IN_ACTIVE;
        }
        AppOfferDetailData appOfferDetailData = manager.find(AppOfferDetailData.class, idCodeName.getId());
        if (appOfferDetailData != null) {
            appOfferDetailData.setStatus(idCodeName.getStatus());
            appOfferDetailData.setUpdatedBy((idCodeName.getCode()));
            appOfferDetailData.setUpdateTime((AppUtils.getCurrentTimestamp()));
            manager.flush();
            return true;
        }
        return false;
    }

    @Override
    public Boolean setOrderingForAppOffer(List<IdIndex> lists) {
        for (IdIndex list : lists) {
            updateIndex(list);
        }
        return true;
    }

    private void updateIndex(IdIndex idIndex) {
        AppOfferDetailData appOfferDetailData = manager.find(AppOfferDetailData.class, idIndex.getId());
        if (appOfferDetailData != null) {
            appOfferDetailData.setOfferIndex(idIndex.getIndex());
            appOfferDetailData.setUpdatedBy((idIndex.getUserId()));
            appOfferDetailData.setUpdateTime((AppUtils.getCurrentTimestamp()));
            manager.flush();
        }
    }

	@Override
	public String getOfferBudgerCategory(String couponCode) {
		Query query = manager.createQuery(
				"select X.offerDetail.accountsCategory.budgetCategory FROM CouponDetailData X where X.couponCode = :couponCode");
		query.setParameter("couponCode", couponCode);
		List<String> list = query.getResultList();
		return list == null || list.size() == 0 ? null : list.get(0);

	}

    @Override
    public OfferDetailData getOfferDetailDataFromCoupon(String offerCode){
        try {
            CouponDetailData couponDetailData = getCoupon(offerCode);
            OfferDetailData offerDetailData = getOfferDetailData(couponDetailData.getOfferDetail().getOfferDetailId());
            return offerDetailData;
        }catch (Exception e){
            LOG.error("Exception Caught While Retrieving Offer With Coupon Code :: {}",offerCode);
            return null;
        }
    }

	@Override
	public void setCouponCodeAsInactive(Integer couponDetailId) {
		CouponDetailData data = manager.find(CouponDetailData.class, couponDetailId);
		data.setCouponStatus(AppConstants.IN_ACTIVE);
	}

    @Override
    public CampaignDetail addNewCampaign(CampaignDetail campaignDetail) {
        CampaignDetailData campaignDetailData = new CampaignDetailData();
        parseCampaignDetailData(campaignDetail, campaignDetailData);
        campaignDetailData = add(campaignDetailData);
        addOrUpdateCampaignMappingList(campaignDetail, campaignDetailData);
        return MasterDataConverter.convert(campaignDetailData);
    }

	private void addOrUpdateCampaignMappingList(CampaignDetail campaignDetail, CampaignDetailData campaignDetailData) {
		List<CampaignCouponMapping> existingList = campaignDetailData.getCouponMapping();
		List<CampaignCouponMapping> newList = new ArrayList<>();
		for (Map.Entry<String, Map<Integer, CampaignMapping>> entry : campaignDetail.getMappings().entrySet()) {
			for (Map.Entry<Integer, CampaignMapping> innerEntry : entry.getValue().entrySet()) {
				CampaignCouponMapping mapping = null;
				if (existingList != null && innerEntry.getValue().getCampaignCouponMappingId() != null) {
					for (CampaignCouponMapping existing : existingList) {
						if (Objects.nonNull(existing.getCampaignCouponMappingId()) && existing.getCampaignCouponMappingId().equals(innerEntry.getValue()
								.getCampaignCouponMappingId())) {
							mapping = existing;
							mapping.setCustomerType(entry.getKey());
							mapping.setCloneCode(innerEntry.getValue().getCode());
							mapping.setCloneCodeDesc(innerEntry.getValue().getDesc());
							mapping.setJourneyNumber(innerEntry.getKey());
							mapping.setValidityInDays(innerEntry.getValue().getValidityInDays());
							mapping.setReminderDays(innerEntry.getValue().getReminderDays());
							manager.flush();
							break;
						}
					}
					mapping.setCampaignCouponMappingId(innerEntry.getValue().getCampaignCouponMappingId());
				} else {
					mapping = new CampaignCouponMapping();
					mapping.setCampaignDetailData(campaignDetailData);
					mapping.setCustomerType(entry.getKey());
					mapping.setCloneCode(innerEntry.getValue().getCode());
					mapping.setCloneCodeDesc(innerEntry.getValue().getDesc());
					mapping.setJourneyNumber(innerEntry.getKey());
					mapping.setValidityInDays(innerEntry.getValue().getValidityInDays());
					mapping.setReminderDays(innerEntry.getValue().getReminderDays());
					manager.persist(mapping);
					newList.add(mapping);
				}
			}
		}
		if(!newList.isEmpty()){
		    campaignDetailData.setCouponMapping(newList);
        }
	}


    @Override
    public CampaignDetail addCampaignToken(CampaignDetail campaignDetail) {
        return updateCampaignDetail(campaignDetail);
    }

    private void parseCampaignDetailData(CampaignDetail campaignDetail, CampaignDetailData campaignDetailData) {
        campaignDetailData.setPrimaryUrl(campaignDetail.getPrimaryUrl());
        campaignDetailData.setCampaignStrategy(campaignDetail.getCampaignStrategy());
        campaignDetailData.setCampaignSource(campaignDetail.getCampaignSource());
        campaignDetailData.setCampaignMedium(campaignDetail.getCampaignMedium());
        campaignDetailData.setCampaignName(campaignDetail.getCampaignName());
        campaignDetailData.setCampaignCategory(campaignDetail.getCampaignCategory());
        campaignDetailData.setCampaignDesc(campaignDetail.getCampaignDesc());
        campaignDetailData.setCouponCode(campaignDetail.getCouponCode());
        campaignDetailData.setCouponCodeDesc(campaignDetail.getCouponCodeDesc());
        campaignDetailData.setIsCouponClone(AppConstants.getValue(campaignDetail.isCouponClone()));
        campaignDetailData.setRegion(campaignDetail.getRegion());
        campaignDetailData.setCity(campaignDetail.getCity());
        campaignDetailData.setUnitIds(campaignDetail.getUnitIds());
        campaignDetailData.setUsageLimit(campaignDetail.getUsageLimit());
        campaignDetailData.setStartDate(AppUtils.getDate(campaignDetail.getStartDate()));
        campaignDetailData.setEndDate(AppUtils.getDate(campaignDetail.getEndDate()));
        campaignDetailData.setValidity(campaignDetail.getValidity());
        campaignDetailData.setHeroBannerMobile(campaignDetail.getHeroBannerMobile());
        campaignDetailData.setHeroBannerDesktop(campaignDetail.getHeroBannerDesktop());
        campaignDetailData.setLandingPageDesc(campaignDetail.getLandingPageDesc());
        campaignDetailData.setSmsTemplate(campaignDetail.getSmsTemplate());
        campaignDetailData.setSmsReminder(campaignDetail.getSmsReminder());
        campaignDetailData.setWhatsappTemplate(campaignDetail.getWhatsappTemplate());
        campaignDetailData.setWhatsappReminder(campaignDetail.getWhatsappReminder());
        campaignDetailData.setReminderDayGap(campaignDetail.getReminderDayGap());
        campaignDetailData.setUtmHeading(campaignDetail.getUtmHeading());
        campaignDetailData.setUtmDesc(campaignDetail.getUtmDesc());
        campaignDetailData.setUtmImageUrl(campaignDetail.getUtmImageUrl());
        campaignDetailData.setRedirectionUrl(campaignDetail.getRedirectionUrl());
        campaignDetailData.setCampaignStatus(campaignDetail.getCampaignStatus());
        campaignDetailData.setImage1(campaignDetail.getImage1());
        campaignDetailData.setImage2(campaignDetail.getImage2());
        campaignDetailData.setImage3(campaignDetail.getImage3());
        campaignDetailData.setLongUrl(campaignDetail.getLongUrl());
        campaignDetailData.setShortUrl(campaignDetail.getShortUrl());
        campaignDetailData.setNewCustomerOnly(campaignDetail.getNewCustomerOnly());
        campaignDetailData.setCampaignReach(campaignDetail.getCampaignReach());
        campaignDetailData.setCouponPrefix(campaignDetail.getCouponPrefix());
        campaignDetailData.setLinkedCampaignId(campaignDetail.getLinkedCampaignId());
        campaignDetailData.setCouponApplicableAfter(campaignDetail.getCouponApplicableAfter());
        campaignDetailData.setBrandId(campaignDetail.getBrandId());
        campaignDetailData.setApplicableForOrder(AppConstants.getValue(campaignDetail.getApplicableForOrder()));
        campaignDetailData.setParentCampaignStrategy(campaignDetail.getParentCampaignStrategy());
        campaignDetailData.setLaunchUnitId(campaignDetail.getLaunchUnitId());
        campaignDetailData.setCafeLaunchDate(campaignDetail.getCafeLaunchDate());
        campaignDetailData.setCrmAppBannerUrl(campaignDetail.getCrmAppBannerUrl());
    }

    @Override
    public List<CampaignDetail> getAllCampaigns() {
        List<CampaignDetail> campaignDetailList = new ArrayList<>();
        List<CampaignDetailData> campaignDetailDataList = null;

        try {
            Query query = manager.createQuery("select C FROM CampaignDetailData C WHERE C.campaignStatus <> :archived");
            query.setParameter("archived", AppConstants.ARCHIVED);
            campaignDetailDataList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting All Campaign Detail", e);
        }

        if (campaignDetailDataList != null) {
            campaignDetailDataList.forEach(data -> campaignDetailList.add(MasterDataConverter.convert(data)));
        }

        return campaignDetailList;
    }

    @Override
    public CampaignDetail getCampaignById(Integer campaignId) {
        CampaignDetail campaignDetail = null;
        CampaignDetailData campaignDetailData = null;
        try {
            Query query = manager.createQuery("select C FROM CampaignDetailData C WHERE C.campaignId = :campaignId AND C.campaignStatus = :status");
            query.setParameter("campaignId", campaignId);
            query.setParameter("status", AppConstants.ACTIVE);
            Object obj = query.getSingleResult();
            if (obj != null) {
                campaignDetailData = (CampaignDetailData) obj;
            }
        } catch (Exception e) {
            LOG.error("Error while fetching campaign detail: {}", e.getMessage());
        }
        if (campaignDetailData != null) {
            campaignDetail = MasterDataConverter.convert(campaignDetailData);
        }
        return campaignDetail;
    }

    @Override
    public CampaignDetail getCampaignByTokenAndStatus(String campaignToken, String campaignStatus) {
        CampaignDetail campaignDetail = null;
        CampaignDetailData campaignDetailData = null;
        try {
            Query query = manager.createQuery("select C FROM CampaignDetailData C WHERE C.campaignToken = :campaignToken AND C.campaignStatus = :campaignStatus");
            query.setParameter("campaignToken", campaignToken);
            query.setParameter("campaignStatus", campaignStatus);
            Object obj = query.getSingleResult();
            if (obj != null) {
                campaignDetailData = (CampaignDetailData) obj;
            }
        } catch (Exception e) {
            LOG.error("Error while fetching campaign detail: {}", e.getMessage());
        }
        if (campaignDetailData != null) {
            campaignDetail = MasterDataConverter.convert(campaignDetailData);
        }
        return campaignDetail;
    }

    @Override
    public List<CampaignDetail> getCampaignsByCampaignNameOrDesc(String campaignDesc, Boolean fetchAll) {
        List<CampaignDetail> campaignDetailList = new ArrayList<>();
        List<CampaignDetailData> campaignDetailDataList = null;
        campaignDesc = "%" + campaignDesc + "%";
        try {
            String queryString = "FROM CampaignDetailData C WHERE (C.campaignDesc LIKE :campaignDesc OR C.campaignName LIKE :campaignDesc) AND C.campaignStatus <> :archived";
            if (!fetchAll) {
                queryString += " AND campaignStatus = :active";
            }
            Query query = manager.createQuery(queryString);
            query.setParameter("campaignDesc", campaignDesc);
            query.setParameter("archived", AppConstants.ARCHIVED);
            if (!fetchAll) {
                query.setParameter("active", AppConstants.ACTIVE);
            }
            campaignDetailDataList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting All Campaign Detail", e);
        }

        if (campaignDetailDataList != null) {
            campaignDetailDataList.forEach(data -> campaignDetailList.add(MasterDataConverter.convert(data)));
        }

        return campaignDetailList;
    }

    @Override
    public List<CampaignCouponMapping> getActiveCampaignsByJourney(String repeatType, Integer journey) {
        try {
            String queryString = "select C FROM CampaignCouponMapping C WHERE C.journeyNumber = :journey AND C.customerType = :repeatType AND C.campaignDetailData.campaignStatus = :active";
            Query query = manager.createQuery(queryString);
            query.setParameter("repeatType", repeatType);
            query.setParameter("journey", journey);
            query.setParameter("active", AppConstants.ACTIVE);
            return query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting All Campaign Detail", e);
        }
        return null;
    }

    @Override
    public DeliveryCouponDetailData getMasterCoupon(String code, Integer brandId) {
        try {
            Date currentDate = AppUtils.getCurrentTimestamp();
            Query query = manager.createQuery("select DC FROM DeliveryCouponDetailData DC WHERE " +
                    " DC.masterCoupon = :masterCoupon AND DC.startDate <= :startDate AND DC.endDate >= :endDate " +
                    "AND DC.brandId = :brandId AND DC.channelPartnerId = :channelPartnerId AND DC.deliveryCouponStatus = :status ");
            query.setParameter("masterCoupon", code);
            query.setParameter("startDate", currentDate);
            query.setParameter("endDate", currentDate);
            query.setParameter("brandId", brandId);
            query.setParameter("channelPartnerId", 3);
            query.setParameter("status", DeliveryCouponStatus.AVAILABLE.name());
            query.setMaxResults(1);
            DeliveryCouponDetailData detailData = (DeliveryCouponDetailData) query.getSingleResult();
            if(Objects.nonNull(detailData)){
                LOG.info("Found valid Coupon : {} for master coupon : {} ",detailData.getCouponCode(),detailData.getMasterCoupon());
                return detailData;
            }else {
                LOG.info("No valid Coupon found for masterCoupon : {}, startDate : {}, endDate :{}, brandId :{}, " +
                        "channelPartnerId : {}, isExhausted : {}",code,currentDate,currentDate,brandId,3,AppConstants.getValue(false));
            }
        } catch (Exception e) {
            LOG.error("Error while getting Coupon Code: {} , {}", code, e.getMessage());
        }
        return null;
    }

    @Override
    public DeliveryCouponDetailData getDeliveryMasterCoupon(String code, Integer brandId,Integer channelPartnerId){
        try {
            Date currentDate = AppUtils.getCurrentTimestamp();
            Query query = manager.createQuery("select DC FROM DeliveryCouponDetailData DC WHERE " +
                    " DC.masterCoupon = :masterCoupon AND DC.startDate <= :startDate AND DC.endDate >= :endDate " +
                    "AND DC.brandId = :brandId AND DC.channelPartnerId = :channelPartnerId AND DC.deliveryCouponStatus = :status ");
            query.setParameter("masterCoupon", code);
            query.setParameter("startDate", currentDate);
            query.setParameter("endDate", currentDate);
            query.setParameter("brandId", brandId);
            query.setParameter("channelPartnerId", channelPartnerId);
            query.setParameter("status", DeliveryCouponStatus.AVAILABLE.name());
            query.setMaxResults(1);
            DeliveryCouponDetailData detailData = (DeliveryCouponDetailData) query.getSingleResult();
            if(Objects.nonNull(detailData)){
                LOG.info("Found valid Coupon : {} for master coupon : {} ",detailData.getCouponCode(),detailData.getMasterCoupon());
                return detailData;
            }else {
                LOG.info("No valid Coupon found for masterCoupon : {}, startDate : {}, endDate :{}, brandId :{}, " +
                        "channelPartnerId : {}, isExhausted : {}",code,currentDate,currentDate,brandId,3,AppConstants.getValue(false));
            }
        } catch (Exception e) {
            LOG.error("Error while getting Coupon Code: {} , {}", code, e.getMessage());
        }
        return null;
    }


    @Override
    public CampaignDetail updateCampaignStatus(CampaignDetail campaignDetail) {
        return updateCampaignDetail(campaignDetail);
    }

    @Override
	public CampaignDetail updateCampaignDetail(CampaignDetail campaignDetail) {
		CampaignDetailData campaignDetailData = manager.find(CampaignDetailData.class, campaignDetail.getCampaignId());
		campaignDetailData.setCampaignId(campaignDetail.getCampaignId());
		parseCampaignDetailData(campaignDetail, campaignDetailData);
		addOrUpdateCampaignMappingList(campaignDetail, campaignDetailData);
		campaignDetailData.setCampaignToken(campaignDetail.getCampaignToken());
		campaignDetailData = update(campaignDetailData);
		return MasterDataConverter.convert(campaignDetailData);
	}

	@Override
	public CampaignDetail getActiveCampaignByUnitId(Integer unitId, String strategy) {
		//TODO Ankit to implement this
        LOG.info("Fetching Active campaign for unit id : {} and strategy : {}",unitId,strategy);
        try{
            Query query = manager.createQuery("select cdd FROM CampaignDetailData cdd WHERE cdd.unitIds LIKE :unitId " +
                    "AND cdd.campaignStatus = :status AND cdd.campaignStrategy = :strategy AND cdd.applicableForOrder = :applicableForOrder ORDER BY cdd.campaignId DESC");
            query.setParameter("unitId","%"+unitId+"%");
            query.setParameter("status","ACTIVE");
            query.setParameter("strategy",strategy);
            query.setParameter("applicableForOrder",AppConstants.YES);
            List<CampaignDetailData> details =  query.getResultList();
            if(details.size() > 0){
                CampaignDetail campaignDetail = MasterDataConverter.convert(details.get(0));
                LOG.info("Found campaign for unit Id ::: {}",unitId);
                LOG.info("CampaignDetail found with campaign id : {}",campaignDetail.getCampaignId());
                return campaignDetail;
            }else{
                LOG.info("Found campaign for unit Id ::: {}",unitId);
            }
        }catch (NoResultException e){
            LOG.error("No campaign found for particular unit ::: {}",unitId);
        }catch (Exception e){
            LOG.error("No campaign found for particular unit ::: {}",unitId,e);
        }

		return null;
	}

	@Override
	public CampaignDetail getActiveCampaignByUnitRegion(String region, String strategy) {
		//TODO Ankit to implement this
        LOG.info("Fetching Active campaign for region : {} and strategy : {}",region,strategy);
        try{
            Query query = manager.createQuery("select cdd FROM CampaignDetailData cdd WHERE cdd.region LIKE :region  AND" +
                    " cdd.campaignReach = :reachType AND cdd.campaignStatus = :status AND cdd.applicableForOrder = :applicableForOrder " +
                    "AND cdd.campaignStrategy = :strategy ORDER BY cdd.campaignId DESC");
            query.setParameter("region","%"+region+"%");
            query.setParameter("status","ACTIVE");
            query.setParameter("reachType", CampaignReachType.REGION_SPECIFIC.name());
            query.setParameter("strategy",strategy);
            query.setParameter("applicableForOrder",AppConstants.YES);
            List<CampaignDetailData> details = query.getResultList();
            if(details.size() > 0){
                CampaignDetail campaignDetail = MasterDataConverter.convert(details.get(0));
                LOG.info("Found campaign for region ::: {}",region);
                return campaignDetail;
            }else {
                LOG.info("No campaign found for particular region ::: {}",region);
            }
        }catch (NoResultException e){
            LOG.error("No campaign found for particular region ::: {}",region);
        }catch (Exception e){
            LOG.error("No campaign found for particular region ::: {}",region,e);
        }
        return null;
	}

	@Override
	public CampaignDetail getActiveCampaignBySystem(String strategy) {
		//TODO Ankit to implement this
        LOG.info("Fetching Active campaign for system specific and strategy : {}",strategy);
        try{
            Query query = manager.createQuery("select cdd FROM CampaignDetailData cdd WHERE " +
                    " cdd.campaignReach = :reachType AND cdd.campaignStatus = :status AND cdd.applicableForOrder = :applicableForOrder " +
                    "AND cdd.campaignStrategy = :strategy ORDER BY cdd.campaignId DESC");
            query.setParameter("status","ACTIVE");
            query.setParameter("reachType", CampaignReachType.SYSTEM_SPECIFIC.name());
            query.setParameter("strategy",strategy);
            query.setParameter("applicableForOrder",AppConstants.YES);
            List<CampaignDetailData> details = query.getResultList();
            if(details.size() > 0){
                CampaignDetail campaignDetail = MasterDataConverter.convert(details.get(0));
                LOG.info("Found campaign for with system specific reach");
                return campaignDetail;
            }else{
                LOG.info("No campaign found for with system specific reach");
            }
        }catch (NoResultException e){
            LOG.error("No campaign found for with system specific reach");
        }catch (Exception e){
            LOG.error("No campaign found for with system specific reach",e);
        }
        return null;
	}

    @Override
    public Long findValidDeliveryCouponCount(String masterCoupon, Date currentTimestamp) {
        try {
            Query query = manager.createQuery("SELECT SUM(dccd.maxNoOfDistributions) - SUM(dccd.noOfAllocations) FROM DeliveryCouponDetailData dccd " +
                    "WHERE dccd.masterCoupon = :masterCoupon AND dccd.endDate >= :currentTimeStamp AND dccd.isExhausted = :noFlag");
            query.setParameter("masterCoupon", masterCoupon);
            query.setParameter("currentTimeStamp", currentTimestamp);
            query.setParameter("noFlag", AppConstants.NO);
            Long validCouponCount = (Long) query.getSingleResult();
            LOG.info("Valid Delivery Coupon Count {}", validCouponCount);
            return validCouponCount;
        } catch (Exception e) {
            LOG.error("Error while finding valid delivery coupons", e);
        }
        return 0L;
    }

    @Override
    public List<CampaignDetail> getActiveCampaignListByValidDate() {
        List<CampaignDetail> campaignDetailList = new ArrayList<>();
        List<CampaignDetailData> campaignDetailDataList = null;
        Date today = AppUtils.getCurrentDate();
        try {
            String queryString = "select C FROM CampaignDetailData C WHERE C.endDate >= :today AND C.campaignStatus = :active";
            Query query = manager.createQuery(queryString);
            query.setParameter("today", today);
            query.setParameter("active", AppConstants.ACTIVE);
            campaignDetailDataList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting Campaign Detail List", e);
        }

        if (campaignDetailDataList != null) {
            campaignDetailDataList.forEach(data -> campaignDetailList.add(MasterDataConverter.convert(data)));
        }

        return campaignDetailList;
    }

    @Override
    public List<Integer> getLinkedCampaignIds(Integer campaignId) {
        Date currentDate = AppUtils.getCurrentTimestamp();
         try {
             Query query = manager.createQuery("SELECT cd.campaignId FROM CampaignDetailData cd WHERE " +
                     "cd.linkedCampaignId = :campaignId AND cd.campaignStatus = :status AND cd.startDate <= :startDate " +
                     "AND cd.endDate >= :endDate AND cd.campaignId <> :campaignId");
             query.setParameter("campaignId",campaignId);
             query.setParameter("status",AppConstants.ACTIVE);
             query.setParameter("startDate",currentDate);
             query.setParameter("endDate",currentDate);
             List<Integer> ids = query.getResultList();
             if(ids.isEmpty()){
                 LOG.info("No linked id found for campaign id : {}",campaignId);
             }else {
                 LOG.info("Found {} linked campaign ids : {}",ids.size(), JSONSerializer.toJSON(ids));
             }
             return ids;
         } catch (Exception e){
             LOG.error("Error while fetching linked campaign ids for campaign Id : {}",campaignId,e);
         }
         return new ArrayList<>();
    }

    @Override
    public CouponDetail getAutoApplicableOfferForUnit(Integer unitId) {
        Query query = manager.createQuery("select O FROM OfferDetailMappingData O where O.mappingType = :mappingType and " +
                "O.status = :status and O.mappingValue= :unitId");
        query.setParameter("mappingType", CouponMappingType.UNIT.name());
        query.setParameter("status", AppConstants.ACTIVE);
        query.setParameter("unitId", unitId.toString());
        List<OfferDetailMappingData> offerDetailMappingData = query.getResultList();
        if (Objects.nonNull(offerDetailMappingData)) {
            for(OfferDetailMappingData offerDetail :offerDetailMappingData){
                OfferDetailData offerDetailData = getOfferDetailData(offerDetail.getOfferDetail().getOfferDetailId());
                if(Objects.nonNull(offerDetailData) && AppConstants.ACTIVE.equals(offerDetailData.getOfferStatus()) &&
                        Objects.nonNull(offerDetailData.getAutoApplicableforUnit()) && offerDetailData.getAutoApplicableforUnit().equals(AppConstants.YES)) {
                    Query q = manager.createQuery("select c From CouponDetailData c WHERE c.offerDetail = :offerDetailData " +
                            "and c.couponStatus = :couponStatus");
                    q.setParameter("offerDetailData", offerDetailData);
                    q.setParameter("couponStatus", AppConstants.ACTIVE);
                    q.setMaxResults(1);
                    List<CouponDetailData> couponDetails = q.getResultList();
                    if (Objects.nonNull(couponDetails) && couponDetails.size() == 1) {
                        return (MasterDataConverter.convert(couponDetails.get(0)));
                    }
                    return null;
                }
            }
        }
        return null;
    }

    @Override
    public void addDeliveryCoupons(List<DeliveryCouponDetailData> couponDetailDataList) {
        int batchSize = 500;
        for (int i = 0; i < couponDetailDataList.size(); i++) {
            if (i > 0 && i % batchSize == 0) {
                manager.flush();
            }
            manager.persist(couponDetailDataList.get(i));
        }
    }

    @Override
    public Integer updateAllCouponWithContact(String oldContactNumber, String newContactNumber) {
        try {
            Query query = manager.createQuery("UPDATE CouponDetailMappingData E SET E.mappingValue =:newContactNumber where E.mappingValue =:oldContactNumber");
            query.setParameter("oldContactNumber", oldContactNumber);
            query.setParameter("newContactNumber", newContactNumber);
            return query.executeUpdate();
        } catch (Exception e) {
            return -1;
        }
    }

    @Override
    public List<String> getValidCoupon(List<String> coupons) {
        try{
            Query query = manager.createQuery("select cd FROM CouponDetailData cd WHERE cd.couponCode IN (:code)");
            query.setParameter("code",coupons);
            List<CouponDetailData> couponDetailData = query.getResultList();
            if(couponDetailData.size() == 0){
                return Arrays.asList(coupons.get(0));
            }
            for(CouponDetailData data : couponDetailData){
                for(String code : coupons){
                    if(!code.equals(data.getCouponCode())){
                        return Arrays.asList(code);
                    }
                }
            }
            return new ArrayList<>();
        }catch (Exception e){
            LOG.error("Error while fetching generated coupon codes that are already present for coupon code : {}", JSONSerializer.toJSON(coupons));
            return null;
        }
    }

    @Override
    public Integer getCouponOfferId( String couponCode) {
        Query query = manager.createQuery("Select od.offerDetailId from CouponDetailData cdd INNER JOIN OfferDetailData od on cdd.offerDetail.offerDetailId = od.offerDetailId where cdd.couponCode = :couponCode");
        query.setParameter("couponCode", couponCode);
        try {
            return (Integer) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.info("Error while getting offer Id for coupon code :::::::{}", couponCode, e);
            return null;
        }
    }

    @Override
    public boolean checkOfferIdForCoupon(List<String> couponCodes,Integer offerId){
        Query query = manager.createQuery("Select cdd from CouponDetailData cdd INNER JOIN OfferDetailData od on cdd.offerDetail.offerDetailId = od.offerDetailId where cdd.couponCode IN (:codes) And cdd.offerDetail.offerDetailId = :offerId");
        query.setParameter("codes",couponCodes);
        query.setParameter("offerId",offerId);
        try {
            List<CouponDetailData> couponDetailData = query.getResultList();
            if(!CollectionUtils.isEmpty(couponDetailData))
                return false;
            return true;
        }catch (NoResultException e) {
            LOG.info("Error while getting coupon data for offer id {} and coupon codes :::::::{}", offerId, e);
            return false;
        }
    }

    @Override
    public DeliveryCouponAllocationDetailData getDeliveryCouponId(String contactNumber){
        Query query = manager.createQuery("FROM DeliveryCouponAllocationDetailData X where" +
                " X.contactNumber= :contactNumber order by allotmentTime desc");
        query.setParameter("contactNumber", contactNumber);
        query.setMaxResults(1);
        try {
            return (DeliveryCouponAllocationDetailData) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.info("Error in fetching data from Delivery Coupon Allocation Detail Data : {}",e);
            return null;
        }
    }

    @Override
    public DeliveryCouponDetailData getDeliveryCouponCode(Integer deliveryCouponId){
        Query query = manager.createQuery("FROM DeliveryCouponDetailData X where" +
                " X.deliveryCouponId= :deliveryCouponId");
        query.setParameter("deliveryCouponId", deliveryCouponId);
        try {
            return (DeliveryCouponDetailData) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.info("Error in etching data from Delivery Coupon Detail Data : {}",e);
            return null;
        }
    }

    @Override
    public List<CouponDetailMappingData> getCouponMappingsForContactNumber(String contactNumber){
        try {
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from CouponDetailMappingData data where data.mappingType = :mappingType and data.mappingValue = :contactNumber and data.status = :status");
            query.setParameter("mappingType", CouponMappingType.CONTACT_NUMBER.name());
            query.setParameter("contactNumber", contactNumber);
            query.setParameter("status",AppConstants.ACTIVE);
            List<CouponDetailMappingData> mappings = query.getResultList();
            LOG.info("COUPON_APPLY Fetching coupon mappings for contact number {} ends in {}ms", contactNumber, System.currentTimeMillis()-startTime);
            return mappings;
        } catch (Exception e) {
            LOG.info("Error while getting Coupon mappings for contact number: {} , {}", contactNumber, e.getMessage());
        }
        return new ArrayList<>();
    }

    @Override
    public CouponDetailData getCouponByCouponIdAndOfferId(List<Integer> couponDetailIds,Integer offerId){
        try {
            Date date = AppUtils.getCurrentBusinessDate();
            Query query = manager.createQuery("select c from  CouponDetailData c where c.offerDetail.offerDetailId = :offerId and c.couponDetailId in (:couponDetailIds) " +
                    "and c.startDate <= :date and c.endDate >= :date and c.couponStatus = :couponStatus");
            query.setParameter("offerId",offerId);
            query.setParameter("couponDetailIds",couponDetailIds);
            query.setParameter("date", date);
            query.setParameter("couponStatus", AppConstants.ACTIVE);
            query.setMaxResults(1);
            CouponDetailData resultData = (CouponDetailData) query.getSingleResult();
            return resultData;
        }catch (Exception e){
            LOG.info("Error in fetching CouponDetailData for OfferId : {} with exception : {}",offerId,e.getMessage());
        }
        return null;
    }

    @Override
    public List<Integer> getOfferAccountCatorybyBudgetCategory(List<String> budgetCategory){
        try {
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data.id from OfferAccountCategory data where data.budgetCategory in :budgetCategory and data.status = :status");
            query.setParameter("budgetCategory", budgetCategory);
            query.setParameter("status", AppConstants.ACTIVE);
            List<Integer> categories = query.getResultList();
            LOG.info("Fetching Offer acount category for Budget category : {} took : {} ms",
                    budgetCategory, System.currentTimeMillis() - startTime);
            return categories;
        }catch (Exception e){
            LOG.info("Error in fetching Offer acount category for budget category : {} with exception : {}",
                    budgetCategory,e);
            return null;
        }
    }

    @Override
    public List<OfferDetailData> getOfferDetailDataByAccountCategory(List<Integer> ids){
        try {
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from OfferDetailData data where data.accountsCategory.id in (:ids) and data.offerStatus = :status");
            query.setParameter("ids", ids);
            query.setParameter("status", AppConstants.ACTIVE);
            List<OfferDetailData> offerDetail = query.getResultList();
            LOG.info("Fetching Offer detail data took : {} ms",System.currentTimeMillis()-startTime);
            return offerDetail;
        }catch (Exception e){
            LOG.info("Error in fetching Offer detail with error : {}",
                    e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DeliveryOfferDetailData> getDeliveryOfferDetailDataForCategory(String category){
        try {
            long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data from DeliveryOfferDetailData data where data.offerCategory = :category and data.offerStatus = :status");
            query.setParameter("category", category);
            query.setParameter("status", AppConstants.ACTIVE);
            List<DeliveryOfferDetailData> offerDetail = query.getResultList();
            LOG.info("Fetching Delivery Offer detail for category : {} data took : {} ms", category,System.currentTimeMillis() - startTime);
            return offerDetail;
        }catch (Exception e){
            LOG.info("Error in Fetching Delivery offer detail data for category : {} and error is : {}",category,e);
        }
        return null;
    }

    @Override
    public CouponDetailData getCouponDetailByOfferId(Integer offerId){
        try{
            Query query = manager.createQuery("SELECT data From CouponDetailData data where data.offerDetail.offerDetailId = :offerDetailId");
            query.setParameter("offerDetailId",offerId);
            query.setMaxResults(1);
            CouponDetailData result = (CouponDetailData) query.getSingleResult();
            if(Objects.nonNull(result)){
                return result;
            }
            else{
                LOG.info("No coupon exist for this offerId : {}",offerId);
                return null;
            }
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public CustomerWinbackOfferInfo getCustomerWinbackOfferInfoById(Integer id){
        try{
            Query query = manager.createQuery("SELECT data From CustomerWinbackOfferInfo data where data.id = :id");
            query.setParameter("id",id);
            CustomerWinbackOfferInfo result = (CustomerWinbackOfferInfo) query.getSingleResult();
            if(Objects.nonNull(result)){
                return result;
            }
            else{
                LOG.info("No customer winback info exist for this id : {}",id);
                return null;
            }
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<CustomerWinbackOfferInfo> getWinbackInfo(){
        try {
            Query query = manager.createQuery("SELECT data From CustomerWinbackOfferInfo data");
            List<CustomerWinbackOfferInfo> list = query.getResultList();
            return list;
        }catch (Exception e){
            LOG.info("Error in Fetching winback offers ",e);
        }
        return null;
    }

    @Override
    public List<CustomerWinbackOfferInfo> getWinbackInfo(Date startDate,Date endDate){
        try {
            Query query = manager.createQuery("SELECT data From CustomerWinbackOfferInfo data where data.updatedAt >= :startDate and data.updatedAt<=:endDate");
            query.setParameter("startDate",startDate);
            query.setParameter("endDate",endDate);
            List<CustomerWinbackOfferInfo> list = query.getResultList();
            return list;
        }catch (Exception e){
            LOG.info("Error in Fetching winback offers ",e);
        }
        return null;
    }

    @Override
    public Map<String, List<CouponDetail>> getCouponDetailByAccountCategory(List<Integer> categoryIds, boolean onlyActive) {
        List<Object[]> dataList = null;
        Map<String, List<CouponDetail>> finalCategoryCouponMap = new HashMap<>();
        try {
            Query query = manager.createQuery("SELECT distinct D, oc.budgetCategory FROM CouponDetailData D LEFT JOIN FETCH " +
                    "D.offerDetail INNER JOIN OfferAccountCategory oc ON oc.id = D.offerDetail.accountsCategory.id WHERE  D.offerDetail.accountsCategory.id IN (:categoryIds) " +
                    "AND D.offerDetail.offerStatus IN (:status)");
            query.setParameter("categoryIds", categoryIds);
            query.setParameter("status", onlyActive ? List.of((AppConstants.ACTIVE)) :
                    List.of(AppConstants.IN_ACTIVE, AppConstants.ACTIVE));
            dataList = query.getResultList();
        } catch (Exception e) {
            LOG.error("Error while getting Coupons", e.getMessage());
        }
        if (!CollectionUtils.isEmpty(dataList)) {
            for (Object[] data : dataList) {
                if (data.length > 0) {
                    List<CouponDetailMappingData> mappings = getCouponMappings(((CouponDetailData) data[0]).getCouponDetailId());
                    getFinalCategoryCouponMap(finalCategoryCouponMap, (String) data[1],
                            (MasterDataConverter.convert((CouponDetailData) data[0], mappings, true, true)));
                }
            }
        }
        return finalCategoryCouponMap;
    }

    private void getFinalCategoryCouponMap(Map<String, List<CouponDetail>> finalCategoryCouponMap,
                                           String key, CouponDetail couponDetail) {
        if (!finalCategoryCouponMap.containsKey(key)) {
            List<CouponDetail> details = new ArrayList<>();
            details.add(couponDetail);
            finalCategoryCouponMap.put(key, details);
        } else if (finalCategoryCouponMap.containsKey(key)) {
            finalCategoryCouponMap.get(key).add(couponDetail);
        }
    }

    public CouponDetailData getCouponCustomerMapping(String couponCode, String contactNumber) {
        try {
            Query query = manager.createQuery("SELECT C FROM CouponDetailData C INNER JOIN CouponDetailMappingData CM \n" +
                    "ON C.couponDetailId = CM.couponDetail\n" +
                    "WHERE CM.mappingValue = :contactNumber AND C.couponCode = :couponCode");
            query.setParameter("contactNumber",contactNumber);
            query.setParameter("couponCode",couponCode);
            return (CouponDetailData) query.getSingleResult();
        } catch (Exception e) {
            LOG.info("Error while getting customer coupon mapping ", e);
        }
        return null;
    }

    public void updateCustomerCouponMapping(CouponDetailData couponDetailData) {
        try {
            Query query = manager.createQuery("UPDATE CouponDetailData SET usageCount = :usageCount , " +
                    "couponStatus = :status WHERE couponDetailId = :couponDetailId");
            query.setParameter("usageCount", couponDetailData.getUsageCount() - 1);
            query.setParameter("status", AppConstants.ACTIVE);
            query.setParameter("couponDetailId", couponDetailData.getCouponDetailId());
            query.executeUpdate();
        } catch (Exception e) {
            LOG.info("Error while updating customer coupon mapping ", e);
        }
    }

    @Override
    public CouponDetailData getCouponWithoutMappings(String couponCode) {
        try {
            Query query = manager.createQuery("SELECT data FROM CouponDetailData data where data.couponCode = :couponCode");
            query.setParameter("couponCode", couponCode);
            Object o = query.getSingleResult();
            CouponDetailData couponDetailData = (CouponDetailData) o;
            return Objects.isNull(o) ? null : (CouponDetailData) o;
        } catch (Exception e) {
            LOG.info("Error while getting Coupon Code: {} , {}", couponCode, e.getMessage());
        }
        return null;
    }

    @Override
    public List<OfferDetailData> getOfferDetailByScope(String offerScope){
        List<OfferDetailData> offerDetails = new ArrayList<>();
        Date currentDate = AppUtils.getBusinessDate();
        try {

//            LOG.info("Fetching offer detail for offerScope {} and {} date",offerScope,currentDate);
            Query query = manager.createQuery(""+"select data from OfferDetailData data "+
                    "where data.offerStatus = :offerStatus " +
                    "and data.offerScope = :offerScope "+
                    "and data.startDate <= :date and data.endDate >= :date"
            );
            query.setParameter("offerStatus", AppConstants.ACTIVE);
            query.setParameter("offerScope", offerScope);
            query.setParameter("date", currentDate);
            offerDetails = query.getResultList();
        }catch (Exception e){
            LOG.info("Error Fetching offer detail for offerScope {} and {} date",offerScope,currentDate,e);
        }

        return  offerDetails;
    }

    @Override
    public List<OfferDetailMappingData> getOfferDetailMappingDataByMappingTypeAndMappingValueAndOfferdetailIds(String mappingType, String mappingValue,List<Integer> ids){
        List<OfferDetailMappingData>  offerDetailMappingData  = new ArrayList<>();
        try {
//            LOG.info("Fetching offer detail Mapping Data  for mappingType {} ,mappingValues {} and ids {}",mappingType,mappingValue,ids);
            Query query = manager.createQuery("select c from OfferDetailMappingData c where c.offerDetail.offerDetailId in (:offerDetailIds) " +
                    "and c.mappingType = :mappingType and c.mappingValue = :mappingValue and c.status = :status");
            query.setParameter("offerDetailIds", ids);
            query.setParameter("mappingType", mappingType);
            query.setParameter("mappingValue", mappingValue);
            query.setParameter("status", AppConstants.ACTIVE);
            offerDetailMappingData =  query.getResultList();
        }catch (Exception e){
            LOG.info("Error offer detail Mapping Data  for mappingType {} ,mappingValues {} and ids {}",mappingType,mappingValue,ids);
        }
        return offerDetailMappingData;
    }

    @Override
    public List<CouponDetailData> getCouponDetailByResuseAndStatusAndOfferdetailIds(String couponsResuse, String status, List<Integer> ids){
        List<CouponDetailData> couponDetailDataList = new ArrayList<>();
        try{
//            LOG.info("Fetching coupon detail Data for offerIds {} ,resuse {} and status {}",ids,couponsResuse,status);
            Query query = manager.createQuery("select c from CouponDetailData c where c.offerDetail.offerDetailId in (:offerDetailIds) " +
                    "and c.couponReuse = :couponReuse and c.couponStatus = :couponStatus order by 1 desc");
            query.setParameter("offerDetailIds", ids);
            query.setParameter("couponReuse", couponsResuse);
            query.setParameter("couponStatus", status);
            couponDetailDataList = query.getResultList();
        }catch (Exception e){
            LOG.info("error Fetching coupon detail Data for offerIds {} ,resuse {} and status {}",ids,couponsResuse,status);
        }

        return couponDetailDataList;
    }

    @Override
    public CouponDetailData getCouponByCouponDetailId(List<Integer> couponDetailId){
        try{
            Date endDate = AppUtils.getCurrentDate();
            Query query = manager.createQuery("From CouponDetailData where couponDetailId in :couponDetailId and endDate >= :endDate " +
                    "and usageCount < maxUsage");
            query.setParameter("couponDetailId",couponDetailId);
            query.setParameter("endDate",endDate);
            query.setMaxResults(1);
            CouponDetailData result = (CouponDetailData) query.getSingleResult();
            if(Objects.nonNull(result)){
                return result;
            }
            else{
                return null;
            }
        }catch (Exception e){
            return null;
        }
    }


}
