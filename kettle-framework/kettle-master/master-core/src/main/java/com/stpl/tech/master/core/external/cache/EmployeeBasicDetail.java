/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.cache;

import java.io.Serializable;

import com.stpl.tech.master.domain.model.EmploymentStatus;

public class EmployeeBasicDetail implements Serializable {

    private static final long serialVersionUID = -1854003503992144900L;
    private int id;
    private String name;
    private String emailId;
    private EmploymentStatus status;
    private String departmentName;
    private Integer designationId;
    private String designation;
    private String employeeCode;
    private String contactNumber;
    private String mappingStatus;
    private String sdpContact;
    private Integer mealAllowanceLimit;
    private int maxAllocatedUnits = -1;
    private Integer reportingManagerId;
    private String slackChannel;
    private Integer departmentId;
    private Integer userPolicyId;
    private String gender;
    private Integer companyId;
    private String imagekey;
    private Integer locCode;

    public EmployeeBasicDetail() {

    }

    public EmployeeBasicDetail(int id, String name, EmploymentStatus status, String departmentName,
                               String designation, String employeeCode, String emailId, String sdpContact, int maxAllocatedUnits, Integer departmentId, Integer userPolicyId ) {
        super();
        this.id = id;
        this.name = name;
        this.status = status;
        this.departmentName = departmentName;
        this.designation = designation;
        this.employeeCode = employeeCode;
        this.emailId = emailId;
        this.sdpContact = sdpContact;
        this.maxAllocatedUnits = maxAllocatedUnits;
        this.departmentId = departmentId;
        this.userPolicyId = userPolicyId;
    }



    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public EmploymentStatus getStatus() {
        return status;
    }

    public void setStatus(EmploymentStatus status) {
        this.status = status;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Integer getDesignationId() {
        return designationId;
    }

    public void setDesignationId(Integer designationId) {
        this.designationId = designationId;
    }

    public String getDesignation() {
        return designation;
    }

    public void setDesignation(String designation) {
        this.designation = designation;
    }

	public String getEmployeeCode() {
		return employeeCode;
	}

	public void setEmployeeCode(String employeeCode) {
		this.employeeCode = employeeCode;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getMappingStatus() {
		return mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

    public String getSdpContact() {
        return sdpContact;
    }

    public void setSdpContact(String sdpContact) {
        this.sdpContact = sdpContact;
    }

	public Integer getMealAllowanceLimit() {
		return mealAllowanceLimit;
	}

	public void setMealAllowanceLimit(Integer mealAllowanceLimit) {
		this.mealAllowanceLimit = mealAllowanceLimit;
	}

    public Integer getReportingManagerId() {
        return reportingManagerId;
    }

    public void setReportingManagerId(Integer reportingManagerId) {
        this.reportingManagerId = reportingManagerId;
    }

    public String getSlackChannel() {
        return slackChannel;
    }

    public void setSlackChannel(String slackChannel) {
        this.slackChannel = slackChannel;
    }

	public int getMaxAllocatedUnits() {
		return maxAllocatedUnits;
	}

	public void setMaxAllocatedUnits(int maxAllocatedUnits) {
		this.maxAllocatedUnits = maxAllocatedUnits;
	}

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getUserPolicyId() {
        return userPolicyId;
    }

    public void setUserPolicyId(Integer userPolicyId) {
        this.userPolicyId = userPolicyId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getImagekey() {
        return imagekey;
    }

    public void setImagekey(String imagekey) {
        this.imagekey = imagekey;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getLocCode() {
        return locCode;
    }

    public void setLocCode(Integer locCode) {
        this.locCode = locCode;
    }
}
