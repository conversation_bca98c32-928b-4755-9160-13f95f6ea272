/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core;

public class OfferCategoryHelper {

    private static enum Category {
        BILL, ITEM;
    }

    public static String getCategory(OfferCategoryType type) {
        switch (type) {
            case PERCENTAGE_BILL_STRATEGY:

                return Category.BILL.name();

            case PERCENTAGE_ITEM_BOGO_STRATEGY:

                return Category.ITEM.name();

            case SLICE_ITEM_PRICE_STRATEGY:

                return Category.ITEM.name();

            case FLAT_BILL_STRATEGY:

                return Category.BILL.name();

            case PERCENTAGE_ITEM_STRATEGY:

                return Category.ITEM.name();

            case FLAT_ITEM_STRATEGY:
            case COMBO_STRATEGY:

                return Category.ITEM.name();

            case OFFER_WITH_FREE_ITEM_STRATEGY:

                return Category.BILL.name();

            case FIXED_VALUE_BILL_STRATEGY:
            case PERCENTAGE_BILL_MAX_CAP_STRATEGY:

                return Category.BILL.name();
            case PERCENTAGE_ITEM_MAX_CAP_STRATEGY:

                return Category.ITEM.name();
            case PERCENTAGE_ITEM_BOGO_SLICE_STRATEGY:

                return Category.ITEM.name();
            case FREEBIE_STRATEGY:

                return Category.ITEM.name();
            case PERCENTAGE_ITEM_MULTIPLE_STRATEGY:

                return Category.ITEM.name();
            case PERCENTAGE_ITEM_MULTIX_MULTIY_STRATEGY:

                return Category.ITEM.name();
            case PRICE_DESCENDING_PERCENTAGE_ITEM_STRATEGY:
                return Category.ITEM.name();

            default:
                break;
        }
        return null;
    }
}
