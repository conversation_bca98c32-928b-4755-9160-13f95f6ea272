package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "UNIT_CLOSURE_STATE")
public class UnitClosureState implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "STATE_ID", nullable = false)
    private Long stateId;
    @Column(name = "STATE_NAME", nullable = false)
    private String stateName;
    @Column(name = "STATE_DESCRIPTION", nullable = false)
    private String stateDescription;
    @Column(name = "STATE_STATUS", nullable = false)
    private String stateStatus;
    @Column(name = "QUERY", columnDefinition="TEXT")
    private String query;
    @Column(name = "PARAMETER")
    private String parameter;
    @Column(name = "DESTINATION_SOURCE", nullable = false)
    private String destinationSource;
    @Column(name = "STATE_OWNER", nullable = false)
    private String stateOwner;

}