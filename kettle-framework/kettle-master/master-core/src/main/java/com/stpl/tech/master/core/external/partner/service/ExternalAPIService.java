/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.partner.service;

import java.util.List;

import com.hazelcast.map.IMap;
import com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIToken;
import com.stpl.tech.master.data.model.ExternalPartnerInfo;

public interface ExternalAPIService {

    public void addPartner(String partnerName, String envType);

    public List<ExternalPartnerInfo> getAllActivePartners();

    public void addAccessAPISToToken(IMap<String, ExternalAPIToken> tokenMap);

    public void refreshExternalAPICache();

}
