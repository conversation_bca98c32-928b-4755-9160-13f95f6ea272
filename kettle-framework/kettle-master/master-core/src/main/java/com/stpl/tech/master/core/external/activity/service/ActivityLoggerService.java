/**
 *
 */
package com.stpl.tech.master.core.external.activity.service;

import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EmailGenerationException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public interface ActivityLoggerService {

	public void addActivity(Object oldObject, Object newObject, int updatedById);

	public Boolean sendUnitChangeMail(Unit oldObject, Unit newObject, String updatedBy, String className, Integer id,
							   List<String> toEmails, String placeHolder) throws IllegalAccessException, EmailGenerationException;

	public Map<String, Pair<Object,Object>> findDiff(Object oldObject , Object newObject) throws IllegalAccessException;

	public Boolean sendDiffEmail(Object oldObject , Object newObject , String updatedBy , String className , Integer id ,
						  List<String> toEmails, String placeHolder) throws IllegalAccessException, EmailGenerationException;

}
