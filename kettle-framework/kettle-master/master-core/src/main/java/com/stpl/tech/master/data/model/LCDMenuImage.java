package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Generated;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

@Entity
@Table(name = "LCD_MENU_IMAGES")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LCDMenuImage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "NAME", nullable = false)
    private String name;

    @Column(name = "PATH", nullable = false)
    private String path;

    @Column(name = "URL", nullable = false)
    private String url;

    @Column(name = "TYPE", nullable = false)
    private String type;


    @Column(name = "SIZE", nullable = false)
    private String size;



    @Column(name = "VERSION", nullable = false)
    private String version;

    @Column(name = "REGION", nullable = false)
    private String region;

    @Column(name = "PRICE_PROFILE", nullable = false)
    private String priceProfile;

    @Column(name = "ORIENTATION", nullable = false)
    private String orientation;

    @Column(name = "SLOT", nullable = false)
    private String slot;

    @Column(name = "LCD_TYPE", nullable = false)
    private String lcdType;

    @Column(name = "UPLOAD_TIME", nullable = false)
    private Date uploadTime;

    @Column(name = "UPLOADED_BY", nullable = false)
    private Integer uploadedBy;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

    @Column(name = "STATUS", nullable = false)
    private String status;

    @OneToMany(mappedBy = "lcdMenuImage", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LCDMenuImageVariant> variants;

    @OneToMany(mappedBy = "lcdMenuImage", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LCDMenuImageParameter> parameters;

    /**
     * Helper method to get parameters as a Map
     * @return Map of step name to step value
     */
    public Map<String, String> getParametersMap() {
        if (parameters == null) {
            return new HashMap<>();
        }
        return parameters.stream()
                .collect(Collectors.toMap(LCDMenuImageParameter::getStepName, LCDMenuImageParameter::getStepValue));
    }

    /**
     * Helper method to set parameters from a Map
     * @param paramMap Map of step name to step value
     */
    public void setParametersFromMap(Map<String, String> paramMap) {
        if (paramMap == null) {
            return;
        }

        if (parameters == null) {
            parameters = new java.util.ArrayList<>();
        } else {
            parameters.clear();
        }

        paramMap.forEach((key, value) -> {
            parameters.add(LCDMenuImageParameter.builder()
                    .lcdMenuImage(this)
                    .stepName(key)
                    .stepValue(value)
                    .build());
        });
    }
}