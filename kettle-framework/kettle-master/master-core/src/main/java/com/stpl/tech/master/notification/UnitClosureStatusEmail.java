package com.stpl.tech.master.notification;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class UnitClosureStatusEmail extends EmailNotification {

  private UnitClosureStatusTemplate template;
    private MasterProperties props;
    private  String unitName;
    private String status;
    public UnitClosureStatusEmail(UnitClosureStatusTemplate template, String unitName, MasterProperties props, String status){
        this.template = template;
        this.props = props;
        this.unitName = unitName;
        this.status = status;
    }

    @Override
    public String[] getToEmails() {
        return new String[]{"<EMAIL>"};
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        return "Unit Closure "+status+" for Unit : "+unitName;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return props.getEnvironmentType();
    }
}
