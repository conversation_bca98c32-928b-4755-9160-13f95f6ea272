package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Created by Mohit
 */
@Entity
@Table(name = "LOCATION_DETAIL")
public class LocationDetail {

	private Integer id;
	private String city;
	private String cityCode;
	private StateDetail state;
	private String countryCode;
	private String status;
	private String functionalFlag;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "LOCATION_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "CITY", nullable = false, length = 100)
	public String getCity() {
		return city;
	}

	public void setCity(String state) {
		this.city = state;
	}

	@Column(name = "CITY_CODE", nullable = false, length = 10)
	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String countryCode) {
		this.cityCode = countryCode;
	}

	@Column(name = "COUNTRY_CODE", nullable = false, length = 10)
	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "STATE_DETAIL_ID", nullable = false)
	public StateDetail getState() {
		return state;
	}

	public void setState(StateDetail state) {
		this.state = state;
	}

	@Column(name = "LOCATION_STATUS", nullable = false, length = 15)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	/**
	 * @return
	 */
	@Column(name = "FUNCTIONAL_FLAG", nullable = false, length = 1)
	public String getFunctionalFlag() {
		return functionalFlag;
	}

	public void setFunctionalFlag(String functionalFlag) {
		this.functionalFlag = functionalFlag;
	}
}
