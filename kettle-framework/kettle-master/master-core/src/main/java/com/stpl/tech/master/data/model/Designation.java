/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.HashSet;
import java.util.Set;

/**
 * Designation generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "DESIGNATION")
public class Designation implements java.io.Serializable {

	private int designationId;
	private String designationName;
	private String designationDesc;
	protected String transactionSystemAccess;
	protected String scmSystemAccess;
	protected String adminSystemAccess;
	protected String clmSystemAccess;
	protected String analyticsSystemAccess;
	protected String crmSystemAccess;
	protected String formsSystemAccess;
	protected String appInstallerAccess;
	protected String channelPartnerAccess;
	protected Integer maxAllocatedUnits = -1;
	protected String attendanceAccess;
	protected String knockApplicationAccess;
	private Set<EmployeeDetail> employeeDetails = new HashSet<EmployeeDetail>(0);
	private Set<Department> departments = new HashSet<Department>(0);

	public Designation() {
	}

	public Designation(int designationId, String designationName, String designationDesc) {
		this.designationId = designationId;
		this.designationName = designationName;
		this.designationDesc = designationDesc;
	}

	public Designation(int designationId, String designationName, String designationDesc,
			Set<EmployeeDetail> employeeDetails, Set<Department> departments) {
		this.designationId = designationId;
		this.designationName = designationName;
		this.designationDesc = designationDesc;
		this.employeeDetails = employeeDetails;
		this.departments = departments;
	}

	@Id
	@Column(name = "DESIGNATION_ID", unique = true, nullable = false)
	public int getDesignationId() {
		return this.designationId;
	}

	public void setDesignationId(int designationId) {
		this.designationId = designationId;
	}

	@Column(name = "DESIGNATION_NAME", nullable = false)
	public String getDesignationName() {
		return this.designationName;
	}

	public void setDesignationName(String designationName) {
		this.designationName = designationName;
	}

	@Column(name = "DESIGNATION_DESC", nullable = false, length = 500)
	public String getDesignationDesc() {
		return this.designationDesc;
	}

	public void setDesignationDesc(String designationDesc) {
		this.designationDesc = designationDesc;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "designation")
	public Set<EmployeeDetail> getEmployeeDetails() {
		return this.employeeDetails;
	}

	public void setEmployeeDetails(Set<EmployeeDetail> employeeDetails) {
		this.employeeDetails = employeeDetails;
	}

	@ManyToMany(fetch = FetchType.LAZY, mappedBy = "designations")
	public Set<Department> getDepartments() {
		return this.departments;
	}

	public void setDepartments(Set<Department> departments) {
		this.departments = departments;
	}

	@Column(name = "TRANSACTION_SYSTEM_ACCESS", nullable = false, length = 1)
	public String getTransactionSystemAccess() {
		return transactionSystemAccess;
	}

	public void setTransactionSystemAccess(String transactionSystemAccess) {
		this.transactionSystemAccess = transactionSystemAccess;
	}

	@Column(name = "SCM_SYSTEM_ACCESS", nullable = false, length = 1)
	public String getScmSystemAccess() {
		return scmSystemAccess;
	}

	public void setScmSystemAccess(String scmSystemAccess) {
		this.scmSystemAccess = scmSystemAccess;
	}

	@Column(name = "ADMIN_SYSTEM_ACCESS", nullable = false, length = 1)
	public String getAdminSystemAccess() {
		return adminSystemAccess;
	}

	public void setAdminSystemAccess(String adminSystemAccess) {
		this.adminSystemAccess = adminSystemAccess;
	}
	@Column(name = "CLM_SYSTEM_ACCESS", nullable = false, length = 1)
	public String getClmSystemAccess() {
		return clmSystemAccess;
	}

	public void setClmSystemAccess(String clmSystemAccess) {
		this.clmSystemAccess = clmSystemAccess;
	}

	@Column(name = "ANALYTICS_SYSTEM_ACCESS", nullable = false, length = 1)
	public String getAnalyticsSystemAccess() {
		return analyticsSystemAccess;
	}

	public void setAnalyticsSystemAccess(String analyticsSystemAccess) {
		this.analyticsSystemAccess = analyticsSystemAccess;
	}

	@Column(name = "CRM_SYSTEM_ACCESS", nullable = false, length = 1)
	public String getCrmSystemAccess() {
		return crmSystemAccess;
	}

	public void setCrmSystemAccess(String crmSystemAccess) {
		this.crmSystemAccess = crmSystemAccess;
	}

	@Column(name = "FORMS_SYSTEM_ACCESS", nullable = false, length = 1)
	public String getFormsSystemAccess() {
		return formsSystemAccess;
	}

	public void setFormsSystemAccess(String formsSystemAccess) {
		this.formsSystemAccess = formsSystemAccess;
	}

	@Column(name = "CHANNEL_PARTNER_ACCESS", nullable = false, length = 1)
	public String getChannelPartnerAccess() {
		return channelPartnerAccess;
	}

	public void setChannelPartnerAccess(String channelPartnerAccess) {
		this.channelPartnerAccess = channelPartnerAccess;
	}

	@Column(name = "APP_INSTALLER_ACCESS", nullable = false, length = 1)
	public String getAppInstallerAccess() {
		return appInstallerAccess;
	}

	public void setAppInstallerAccess(String appInstallerAccess) {
		this.appInstallerAccess = appInstallerAccess;
	}

	@Column(name = "MAX_ALLOCATED_UNIT", nullable = true)
	public Integer getMaxAllocatedUnits() {
		return maxAllocatedUnits;
	}

	public void setMaxAllocatedUnits(Integer maxAllocatedUnits) {
		this.maxAllocatedUnits = maxAllocatedUnits;
	}

	@Column(name = "ATTENDANCE_ACCESS", nullable = false)
	public String getAttendanceAccess() {
		return attendanceAccess;
	}

	public void setAttendanceAccess(String attendanceAccess) {
		this.attendanceAccess = attendanceAccess;
	}

	@Column(name = "KNOCK_APPLICATION_ACCESS",nullable = false)
	public String getKnockApplicationAccess() {
		return knockApplicationAccess;
	}

	public void setKnockApplicationAccess(String knockApplicationAccess) {
		this.knockApplicationAccess = knockApplicationAccess;
	}
}
