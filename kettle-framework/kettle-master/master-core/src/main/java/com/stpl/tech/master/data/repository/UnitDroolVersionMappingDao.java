package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.UnitDroolVersionMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitDroolVersionMappingDao extends JpaRepository<UnitDroolVersionMapping, Integer> {

    public List<UnitDroolVersionMapping> findByMappingStatus(String status);

    public List<UnitDroolVersionMapping> findByDroolTypeAndUnitIdIn(String droolFile, List<Integer> unitIds);
}
