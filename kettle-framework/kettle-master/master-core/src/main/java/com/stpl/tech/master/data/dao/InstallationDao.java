package com.stpl.tech.master.data.dao;

import java.util.List;

import com.stpl.tech.master.data.model.ApplicationInstallationData;
import com.stpl.tech.master.data.model.UnitRestrictedApplicationData;

public interface InstallationDao extends AbstractDao {

	public List<ApplicationInstallationData> getInstalledMachines(String applicationName, Integer unitId, String terminal,
			String screenType, String status);

	public List<UnitRestrictedApplicationData> getUnitRestrictedApplication(Integer unitId, String application, String status);

}
