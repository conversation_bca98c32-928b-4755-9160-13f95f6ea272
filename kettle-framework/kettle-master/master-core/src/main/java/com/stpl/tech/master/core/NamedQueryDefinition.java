package com.stpl.tech.master.core;

public enum NamedQueryDefinition {

    UNIT_PRODUCT_PRICE_DATA("SELECT          distinct\n" +
            "                ud.UNIT_ID unitId,\n" +
            "                ud.UNIT_NAME unitName,\n" +
            "                pd.PRODUCT_ID productId,\n" +
            "                pd.PRODUCT_NAME productName,\n" +
            "                upp.DIMENSION_CODE dimensionCode,\n" +
            "                rl.RL_CODE dimension,\n" +
            "                ud.UNIT_CATEGORY unitCategory,\n" +
            "                ud.PRICING_PROFILE pricingProfile,\n" +
            "                pd.BRAND_ID brandId,\n" +
            "                ud.UNIT_REGION unitRegion,\n" +
            "                ptype.RTL_CODE rtlCode,\n" +
            "                psubtype.RL_CODE rlCode,\n" +
            "                pd.PRODUCT_STATUS productStatus,\n" +
            "                upm.UNIT_PROD_REF_ID unitProductMappingId,\n" +
            "                upm.PRODUCT_STATUS unitProductMappingStatus,\n" +
            "                upp.UNIT_PROD_PRICE_ID unitProductPriceId,\n" +
            "                upp.PRICING_STATUS unitProductPricingStatus,\n" +
            "                upp.PRICE price \n" +
            "            FROM \n" +
            "                UNIT_PRODUCT_MAPPING upm\n" +
            "                inner join \n" +
            "                UNIT_PRODUCT_PRICING upp \n" +
            "                on  upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
            "                inner join \n" +
            "                UNIT_DETAIL ud\n" +
            "                on upm.UNIT_ID = ud.UNIT_ID \n" +
            "                inner join \n" +
            "                PRODUCT_DETAIL pd\n" +
            "                on upm.PRODUCT_ID = pd.PRODUCT_ID\n" +
            "                inner join\n" +
            "                REF_LOOKUP rl\n" +
            "                on upp.DIMENSION_CODE = rl.RL_ID\n" +
            "                inner join\n" +
            "                REF_LOOKUP_TYPE ptype\n" +
            "                on pd.PRODUCT_TYPE = ptype.RTL_ID \n" +
            "                inner join\n" +
            "                REF_LOOKUP psubtype\n" +
            "                on pd.PRODUCT_SUB_TYPE = psubtype.RL_ID\n" +
            "                inner join\n" +
            "                REF_LOOKUP rlup \n" +
            "            WHERE \n" +
            "                upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID \n" +
            "                    AND upp.DIMENSION_CODE = rl.RL_ID \n" +
            "                    AND pd.PRODUCT_STATUS = 'ACTIVE' \n" +
            "                    AND ud.UNIT_STATUS = 'ACTIVE' \n" +
            "                    AND ud.UNIT_CATEGORY = :unitCategory \n" +
            "                    AND pd.BRAND_ID = :brandId \n" +
            "                    AND upm.PRODUCT_STATUS = 'ACTIVE' \n" +
            "                    AND upp.PRICING_STATUS = 'ACTIVE' \n"
    ),

    UNITS_SPECIFIC_PRODUCTS_PRICE_DATA("SELECT DISTINCT ud.UNIT_ID AS unitId, ud.UNIT_NAME AS unitName,pd.PRODUCT_ID AS productId, "+
            "pd.PRODUCT_NAME AS productName, "+
            "upp.DIMENSION_CODE AS dimensionCode, "+
            "rl.RL_CODE AS dimension, "+
            "ud.UNIT_CATEGORY AS unitCategory, "+
            "ud.PRICING_PROFILE pricingProfile, " +
            "pd.BRAND_ID AS brandId, "+
            "ud.UNIT_REGION AS unitRegion, "+
            "ptype.RTL_CODE AS rtlCode, "+
            "psubtype.RL_CODE AS rlCode, "+
            "pd.PRODUCT_STATUS AS productStatus, "+
            "upm.UNIT_PROD_REF_ID AS unitProductMappingId, "+
            "upm.PRODUCT_STATUS AS unitProductMappingStatus, "+
            "upp.UNIT_PROD_PRICE_ID AS unitProductPriceId, "+
            "upp.PRICING_STATUS AS unitProductPricingStatus, "+
            "upp.PRICE as price, "+
            "upp.ALIAS_PRODUCT_NAME as aliasProductName, "+
            "uam.ATTRIBUTE_VALUE as pricingCategory "+
            "FROM "+
            "UNIT_PRODUCT_MAPPING upm "+
            "INNER JOIN UNIT_PRODUCT_PRICING upp "+
            "ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID "+
            "AND upp.PRICING_STATUS = 'ACTIVE' "+
            "INNER JOIN UNIT_DETAIL ud "+
            "ON upm.UNIT_ID = ud.UNIT_ID "+
            "AND ud.UNIT_STATUS = 'ACTIVE' "+
            "INNER JOIN PRODUCT_DETAIL pd "+
            "ON upm.PRODUCT_ID = pd.PRODUCT_ID "+
            "AND pd.PRODUCT_STATUS = 'ACTIVE' "+
            "INNER JOIN REF_LOOKUP rl "+
            "ON upp.DIMENSION_CODE = rl.RL_ID "+
            "AND rl.RL_CODE in :dimensions "+
            "INNER JOIN REF_LOOKUP_TYPE ptype "+
            "ON pd.PRODUCT_TYPE = ptype.RTL_ID "+
            "INNER JOIN REF_LOOKUP psubtype "+
            "ON pd.PRODUCT_SUB_TYPE = psubtype.RL_ID "+
            "LEFT JOIN UNIT_ATTRIBUTE_MAPPING uam "+
            "ON uam.UNIT_ID= ud.UNIT_ID "+
            "and uam.ATTRIBUTE_CODE = 'PRICING_CATEGORY' "+
            "WHERE "+
            "upm.PRODUCT_STATUS = 'ACTIVE'; "
    );
    private final String query;


    private NamedQueryDefinition(String query) {
        this.query = query;
    }


    public String getQuery() {
        return query;
    }


}
