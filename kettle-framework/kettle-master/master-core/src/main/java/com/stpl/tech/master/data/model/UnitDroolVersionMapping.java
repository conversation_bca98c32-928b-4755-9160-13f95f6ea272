package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UNIT_DROOL_VERSION_MAPPING")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnitDroolVersionMapping implements Serializable {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "MAPPING_ID", unique = true, nullable = false)
    private Integer mappingId;

    @Column(name = "DROOL_TYPE", nullable = false)
    private String droolType;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "MAPPING_STATUS", nullable = false)
    private String mappingStatus;

    @Column(name = "VERSION", nullable = false)
    private String version;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATION_TIME")
    private Date lastUpdationTime;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

    @Column(name = "REMARKS", columnDefinition = "TEXT")
    private String remarks;
}
