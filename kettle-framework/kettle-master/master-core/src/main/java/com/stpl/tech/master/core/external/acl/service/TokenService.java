package com.stpl.tech.master.core.external.acl.service;

import io.jsonwebtoken.Claims;

import java.util.Map;

/**
 * Created by <PERSON><PERSON> on 24-05-2016.
 */
public interface TokenService<T extends TokenDao> {
	public String createToken(Map<String, Object> claims);
	public String createToken(T object, long ttlMillis);
	
	public String createToken(T object, long ttlMillis, String passPhraseKey);

	public void parseToken(T object, String text);

	public void parseToken(T object, String jwt, String key);


	public Claims parseToken(String token);

	public String createRefreshToken(String sessionKey,int unitId, int userId, String application,
									 String macAddress, String geoLocation , int terminalId);

	public Claims parseRefreshToken(String token);

	public boolean isTokenExpired(String token);

	public boolean isRefreshTokenExpired(String token);
}
