package com.stpl.tech.master.core.external.notification;

import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.master.core.external.notification.service.impl.GoogleChatServiceImpl;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class SlackNotificationService implements ExternalNotificationService {

	private final GoogleChatServiceImpl googleChatService = new GoogleChatServiceImpl();

	private static final Logger LOG = LoggerFactory.getLogger(SlackNotificationService.class);
	private static final SlackNotificationService INSTANCE = new SlackNotificationService();
	private BlockingQueue<SlackMessage> queue = new DelayQueue<SlackMessage>();

	public static SlackNotificationService getInstance() {
		return INSTANCE;
	}

	public SlackNotificationService() {
		ScheduledExecutorService service = Executors.newScheduledThreadPool(1);
		service.scheduleAtFixedRate(new SlackMessageThread(queue), 0, 1, TimeUnit.MINUTES);
	}

	public synchronized SlackMessage addToQueue(SlackMessage data) {
		try {
			queue.put(data);
		} catch (InterruptedException e) {
			LOG.error(String.format("Error while putting Slack message %s for channel %s  to queue", data.getText(),
					data.getChannel()), e);
			return null;
		}
		return data;
	}

	public BlockingQueue<SlackMessage> getQueue() {
		return queue;
	}

	// to a slack channel
	public void sendNotification(EnvType env, String user, SlackNotification channel, String text){
		sendNotification(env, user, null, channel.getChannel(env), text);
	}

	// to a direct user
	public void sendNotification(EnvType env, String user, String directUser, String text){
			sendNotification(env, user, directUser, null, text);
	}
	
	// to a direct user
	public void sendNotification(EnvType env, String user, String directUser, Notification notification){
			sendNotification(env, user, directUser, null, notification.getNotificationMessage());
	}

	// to a channel using notification
	public void sendNotification(EnvType env, String user, SlackNotification channel, Notification notification) {
		sendNotification(env, user, channel, notification.getNotificationMessage());
	}
	
	// to a channel using notification 
	public void sendNotification(EnvType env, String user, SlackNotification channel,String managerId, Notification notification) throws IOException {
		sendNotification(env, user, null, channel.getChannel(env,managerId), notification.getNotificationMessage());
	}

	// common method
	public void sendNotification(EnvType env, String user, String directUser, String channel, String text){
		if (text == null) {
			return;
		}
		String channelName = channel;
		if (AppUtils.isProd(env)) {
			if(channel != null){
				channel = googleChatService.getSpaceId(channel,env);
			}

		}else{
			if(channel != null) {
				channel = googleChatService.getSpaceId(SlackNotification.DEV_NOTIFICATION_TESTING.getChannel(env), env);
				text = env.name() + " : Space : *" + channelName + "* : " + text;
			}
		}

		// different message as user overrides channel if both are present
		if (directUser != null) {
			addToQueue(new SlackMessage(env, null, directUser, user, text));
		}
		if (channel != null) {
			List<String> channelNameAndId = Arrays.asList(channel.split(","));
			if(channelNameAndId.get(0).equals(SlackNotification.COMMON_NOTIFICATION_CHANNEL.getChannel(env))){
				text = "Space : *"+ channelName + "* does exists or not associated with Chaayos google chat App \n*follow this video to create the space* \n https://drive.google.com/file/d/1EpBcVvneBUuBmwOSSpecLYuSo1ZC5u28/view?usp=share_link \n" + text;
			}
			channel = channelNameAndId.get(1);
			addToQueue(new SlackMessage(env, channel, null, user, text));
		}
	}

}
