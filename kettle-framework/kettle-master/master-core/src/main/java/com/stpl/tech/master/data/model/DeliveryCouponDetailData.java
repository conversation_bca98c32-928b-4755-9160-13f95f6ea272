package com.stpl.tech.master.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;


@ExcelSheet(value = "Delivery Coupon Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Entity
@Table(name = "DELIVERY_COUPON_DETAIL_DATA")
public class DeliveryCouponDetailData {

    private Integer deliveryCouponId;
    @ExcelField
    private Integer brandId;
    @ExcelField
    private Integer channelPartnerId;
    @ExcelField
    private String couponStrategy;
    @ExcelField
    private String masterCoupon;
    @ExcelField
    private String couponCode;
    @ExcelField(headerName = "START_DATE(YYYY-MM-DD)")
    private Date startDate;
    @ExcelField(headerName = "END_DATE(YYYY-MM-DD)")
    private Date endDate;
    @ExcelField
    private Integer validityInDays;
    @ExcelField
    private Integer maxNoOfDistributions;
    private Date creationTime;
    private Integer noOfAllocations;
    private String isExhausted;
    private Date lastAllocationTime;
    private String deliveryCouponStatus;
    @ExcelField
    private Integer maxUsage;
    private String isRedeemed;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "DELIVERY_COUPON_ID", unique = true, nullable = false)
    public Integer getDeliveryCouponId() {
        return deliveryCouponId;
    }

    public void setDeliveryCouponId(Integer deliveryCouponId) {
        this.deliveryCouponId = deliveryCouponId;
    }

    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "CHANNEL_PARTNER_ID")
    public Integer getChannelPartnerId() {
        return channelPartnerId;
    }

    public void setChannelPartnerId(Integer channelPartnerId) {
        this.channelPartnerId = channelPartnerId;
    }

    @Column(name = "COUPON_STRATEGY")
    public String getCouponStrategy() {
        return couponStrategy;
    }

    public void setCouponStrategy(String couponStrategy) {
        this.couponStrategy = couponStrategy;
    }

    @Column(name = "MASTER_COUPON")
    public String getMasterCoupon() {
        return masterCoupon;
    }

    public void setMasterCoupon(String masterCoupon) {
        this.masterCoupon = masterCoupon;
    }

    @Column(name = "COUPON_CODE")
    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @Column(name = "START_DATE")
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Column(name = "END_DATE")
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Column(name = "VALIDITY_IN_DAYS")
    public Integer getValidityInDays() {
        return validityInDays;
    }

    public void setValidityInDays(Integer validityInDays) {
        this.validityInDays = validityInDays;
    }

    @Column(name = "MAX_NO_OF_DISTRIBUTIONS")
    public Integer getMaxNoOfDistributions() {
        return maxNoOfDistributions;
    }

    public void setMaxNoOfDistributions(Integer maxNoOfDistributions) {
        this.maxNoOfDistributions = maxNoOfDistributions;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME")
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "NO_OF_ALLOCATIONS")
    public Integer getNoOfAllocations() {
        return noOfAllocations;
    }

    public void setNoOfAllocations(Integer noOfAllocations) {
        this.noOfAllocations = noOfAllocations;
    }

    @Column(name = "IS_EXHAUSTED", nullable = false)
    public String getIsExhausted() {
        return isExhausted;
    }

    public void setIsExhausted(String isExhausted) {
        this.isExhausted = isExhausted;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_ALLOCATION_TIME")
    public Date getLastAllocationTime() {
        return lastAllocationTime;
    }

    public void setLastAllocationTime(Date lastAllocationTime) {
        this.lastAllocationTime = lastAllocationTime;
    }

    @Column(name = "DELIVERY_COUPON_STATUS")
    public String getDeliveryCouponStatus() {
        return deliveryCouponStatus;
    }

    public void setDeliveryCouponStatus(String deliveryCouponStatus) {
        this.deliveryCouponStatus = deliveryCouponStatus;
    }

    @Column(name = "MAX_USAGE")
    public Integer getMaxUsage() {
        return maxUsage;
    }

    public void setMaxUsage(Integer maxUsage) {
        this.maxUsage = maxUsage;
    }
    @Column(name = "IS_REDEEMED")
    public String getIsRedeemed(){
        return isRedeemed;
    }
    public void setIsRedeemed(String isRedeemed){
        this.isRedeemed = isRedeemed;
    }

    @Override
    public String toString() {
        return "DeliveryCouponDetailData{" +
                "deliveryCouponId=" + deliveryCouponId +
                ", brandId=" + brandId +
                ", channelPartnerId=" + channelPartnerId +
                ", couponStrategy='" + couponStrategy + '\'' +
                ", masterCoupon='" + masterCoupon + '\'' +
                ", couponCode='" + couponCode + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", validityInDays=" + validityInDays +
                ", maxNoOfDistributions=" + maxNoOfDistributions +
                ", creationTime=" + creationTime +
                ", noOfAllocations=" + noOfAllocations +
                ", isExhausted='" + isExhausted + '\'' +
                ", lastAllocationTime=" + lastAllocationTime +
                ", deliveryCouponStatus='" + deliveryCouponStatus + '\'' +
                ", maxUsage=" + maxUsage +
                '}';
    }
}
