package com.stpl.tech.master.core.dao.impl;

import com.stpl.tech.master.core.dao.UnitContactDataDao;
import com.stpl.tech.master.data.model.UnitContactDataEntity;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class UnitContactDataDaoImpl implements UnitContactDataDao {

    private static final Logger LOG = LoggerFactory.getLogger(UnitContactDataDaoImpl.class);

    @Autowired(required = true)
    @Qualifier("masterSessionFactory")
    private SessionFactory sessionFactory;

    private Session getCurrentSession() {
        return sessionFactory.getCurrentSession();
    }

    @Override
    public List<UnitContactDataEntity> getContactsByUnitId(Integer unitId) {
        LOG.debug("Fetching all contacts for unit: {}", unitId);
        
        String hql = "FROM UnitContactDataEntity WHERE unitId = :unitId ORDER BY createdOn DESC";
        Query<UnitContactDataEntity> query = getCurrentSession().createQuery(hql, UnitContactDataEntity.class);
        query.setParameter("unitId", unitId);
        
        List<UnitContactDataEntity> contacts = query.getResultList();
        LOG.debug("Found {} contacts for unit: {}", contacts.size(), unitId);
        
        return contacts;
    }

    @Override
    public List<UnitContactDataEntity> getActiveContactsByUnitId(Integer unitId) {
        LOG.debug("Fetching active contacts for unit: {}", unitId);
        
        String hql = "FROM UnitContactDataEntity WHERE unitId = :unitId AND status = 'ACTIVE' ORDER BY createdOn DESC";
        Query<UnitContactDataEntity> query = getCurrentSession().createQuery(hql, UnitContactDataEntity.class);
        query.setParameter("unitId", unitId);
        
        List<UnitContactDataEntity> contacts = query.getResultList();
        LOG.debug("Found {} active contacts for unit: {}", contacts.size(), unitId);
        
        return contacts;
    }

    @Override
    public boolean saveOrUpdateContacts(List<UnitContactDataEntity> contacts) {
        LOG.debug("Bulk saving/updating {} contacts", contacts.size());
        
        try {
            Session session = getCurrentSession();
            
            for (UnitContactDataEntity contact : contacts) {
                if (contact.getId() == null) {
                    // New contact - save
                    session.save(contact);
                    LOG.debug("Saved new contact: {}", contact.getReferenceName());
                } else {
                    // Existing contact - update
                    session.update(contact);
                    LOG.debug("Updated contact ID: {} - {}", contact.getId(), contact.getReferenceName());
                }
            }
            
            session.flush();
            LOG.info("Successfully saved/updated {} contacts", contacts.size());
            return true;
            
        } catch (Exception e) {
            LOG.error("Error saving/updating contacts", e);
            return false;
        }
    }

    @Override
    public UnitContactDataEntity saveOrUpdateContact(UnitContactDataEntity contact) {
        LOG.debug("Saving/updating single contact: {}", contact.getReferenceName());
        
        try {
            Session session = getCurrentSession();
            
            if (contact.getId() == null) {
                session.save(contact);
                LOG.debug("Saved new contact with ID: {}", contact.getId());
            } else {
                session.update(contact);
                LOG.debug("Updated contact ID: {}", contact.getId());
            }
            
            session.flush();
            return contact;
            
        } catch (Exception e) {
            LOG.error("Error saving/updating contact: {}", contact.getReferenceName(), e);
            throw new RuntimeException("Failed to save contact", e);
        }
    }

    @Override
    public int deactivateContacts(List<Integer> contactIds, String updatedBy) {
        LOG.debug("Deactivating {} contacts", contactIds.size());
        
        if (contactIds.isEmpty()) {
            return 0;
        }
        
        String hql = "UPDATE UnitContactDataEntity SET status = 'INACTIVE', updatedBy = :updatedBy, updatedOn = :updatedOn WHERE id IN (:contactIds)";
        Query query = getCurrentSession().createQuery(hql);
        query.setParameter("updatedBy", updatedBy);
        query.setParameter("updatedOn", new Date());
        query.setParameterList("contactIds", contactIds);
        
        int updatedCount = query.executeUpdate();
        LOG.info("Deactivated {} contacts", updatedCount);
        
        return updatedCount;
    }

    @Override
    public UnitContactDataEntity getContactById(Integer contactId) {
        LOG.debug("Fetching contact by ID: {}", contactId);
        
        try {
            UnitContactDataEntity contact = getCurrentSession().get(UnitContactDataEntity.class, contactId);
            if (contact != null) {
                LOG.debug("Found contact: {}", contact.getReferenceName());
            } else {
                LOG.debug("No contact found with ID: {}", contactId);
            }
            return contact;
            
        } catch (Exception e) {
            LOG.error("Error fetching contact by ID: {}", contactId, e);
            return null;
        }
    }

    @Override
    public boolean isContactNumberExists(Integer unitId, Long contactNumber, Integer excludeContactId) {
        LOG.debug("Checking if contact number {} exists for unit: {} (excluding ID: {})", 
                 contactNumber, unitId, excludeContactId);
        
        StringBuilder hql = new StringBuilder("SELECT COUNT(*) FROM UnitContactDataEntity WHERE unitId = :unitId AND contactNumber = :contactNumber AND status = 'ACTIVE'");
        
        if (excludeContactId != null) {
            hql.append(" AND id != :excludeContactId");
        }
        
        Query<Long> query = getCurrentSession().createQuery(hql.toString(), Long.class);
        query.setParameter("unitId", unitId);
        query.setParameter("contactNumber", contactNumber);
        
        if (excludeContactId != null) {
            query.setParameter("excludeContactId", excludeContactId);
        }
        
        Long count = query.uniqueResult();
        boolean exists = count != null && count > 0;
        
        LOG.debug("Contact number {} exists for unit {}: {}", contactNumber, unitId, exists);
        return exists;
    }
}
