/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 3 Aug, 2015 5:36:58 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * TaxCategoryData generated by hbm2java
 */
@Entity
@Table(name = "TAX_CATEGORY_DATA", uniqueConstraints = @UniqueConstraint(columnNames = { "CATEGORY_CODE" }))
public class TaxCategoryData implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8419169198886167569L;
	private Integer taxCategoryDataId;
	private String categoryCode;
	private String categoryDescription;
	private String categoryInternalDescription;
	private String categoryStatus;
	private String exempted;

	public TaxCategoryData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TAX_CATEGORY_DATA_ID", unique = true, nullable = false)
	public Integer getTaxCategoryDataId() {
		return this.taxCategoryDataId;
	}

	public void setTaxCategoryDataId(Integer productCategoryDataId) {
		this.taxCategoryDataId = productCategoryDataId;
	}

	@Column(name = "CATEGORY_CODE", nullable = false, length = 20)
	public String getCategoryCode() {
		return this.categoryCode;
	}

	public void setCategoryCode(String categoryCode) {
		this.categoryCode = categoryCode;
	}

	@Column(name = "CATEGORY_STATUS", nullable = false, length = 15)
	public String getCategoryStatus() {
		return categoryStatus;
	}

	public void setCategoryStatus(String categoryStatus) {
		this.categoryStatus = categoryStatus;
	}

	@Column(name = "CATEGORY_DESCRIPTION", nullable = false, length = 1000)
	public String getCategoryDescription() {
		return categoryDescription;
	}

	public void setCategoryDescription(String categoryDescription) {
		this.categoryDescription = categoryDescription;
	}

	@Column(name = "CATEGORY_INTERNAL_DESCRIPTION", nullable = false, length = 1000)
	public String getCategoryInternalDescription() {
		return categoryInternalDescription;
	}

	public void setCategoryInternalDescription(String categoryInternalDescription) {
		this.categoryInternalDescription = categoryInternalDescription;
	}

	@Column(name = "IS_EXEMPTED", nullable = false, length = 1)
	public String getExempted() {
		return exempted;
	}

	public void setExempted(String exempted) {
		this.exempted = exempted;
	}

}
