package com.stpl.tech.master.notification;

import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class UnitClosureTaskDoneEmail extends EmailNotification {

  private   UnitClosureTaskDoneTemplate template;
  private  UnitClosureEvent unitClosureEvent;
    private MasterProperties props;
    public UnitClosureTaskDoneEmail(UnitClosureTaskDoneTemplate template, UnitClosureEvent unitClosureEvent,MasterProperties props){
        this.template = template;
        this.unitClosureEvent = unitClosureEvent;
        this.props = props;
    }

    @Override
    public String[] getToEmails() {
        return new String[]{"<EMAIL>"};
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        return "Unit Closure Task Done, Unit Id : "+unitClosureEvent.getUnitId();
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return props.getEnvironmentType();
    }
}
