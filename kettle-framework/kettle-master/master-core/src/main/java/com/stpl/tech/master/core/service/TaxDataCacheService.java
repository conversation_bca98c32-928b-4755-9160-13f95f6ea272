/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service;

import java.util.List;
import java.util.Map;

import com.stpl.tech.master.tax.model.CategoryAdditionalTax;
import com.stpl.tech.master.tax.model.CategoryTax;
import com.stpl.tech.master.tax.model.TaxCategory;

public interface TaxDataCacheService {

	public void loadCache();

	public void refreshTaxCategoryMap();
	
	public void refreshCategoryTaxMap();

	public void refreshHsnStateTaxDataMap();

	public void updateTaxCache(List<CategoryTax> categories, Map<String, CategoryAdditionalTax> additionalMap);

	public void addTaxCategoryToCache(TaxCategory taxCategory);
}
