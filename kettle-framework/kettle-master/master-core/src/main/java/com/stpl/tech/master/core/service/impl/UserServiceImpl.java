/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.master.core.service.impl;

import com.google.gson.Gson;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.PdfWriter;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.MasterException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.mapper.DtoDataMapper;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.UserService;
import com.stpl.tech.master.core.service.model.AclData;
import com.stpl.tech.master.core.service.model.ActionType;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.MasterUserDataDao;
import com.stpl.tech.master.data.model.BusinessDivision;
import com.stpl.tech.master.data.model.Department;
import com.stpl.tech.master.data.model.Designation;
import com.stpl.tech.master.data.model.DocumentDetailData;
import com.stpl.tech.master.data.model.EmployeeApplicationMappingDetail;
import com.stpl.tech.master.data.model.EmployeeDetail;
import com.stpl.tech.master.data.model.EmployeeUserPolicyResetLog;
import com.stpl.tech.master.data.model.RoleChangeAuditLogData;
import com.stpl.tech.master.data.model.UserPolicyData;
import com.stpl.tech.master.data.model.UserPolicyRoleMapping;
import com.stpl.tech.master.domain.model.ACLRequest;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.DocUploadTypeDTO;
import com.stpl.tech.master.domain.model.DocumentDetailDTO;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.EmployeeApplicationMapping;
import com.stpl.tech.master.domain.model.EmployeeOnboarding;
import com.stpl.tech.master.domain.model.EmployeeRole;
import com.stpl.tech.master.domain.model.EmployeeRoleUpdateRequest;
import com.stpl.tech.master.domain.model.EmploymentStatus;
import com.stpl.tech.master.domain.model.EmploymentType;
import com.stpl.tech.master.domain.model.FileTypeDTO;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.RoleActionDTO;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UserPolicyDataDTO;
import com.stpl.tech.master.domain.model.UserPolicyRoleMappingDTO;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;
import com.stpl.tech.master.notification.EmployeeCredentialsEmailNotification;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.util.EnvType;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Service
public class UserServiceImpl implements UserService {

    private static final Logger LOG = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private MasterUserDataDao dao;

    @Autowired
    private MasterDataCacheService cacheService;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private Environment env;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private MasterProperties masterProperties;
    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.UserService#authenticate(int,
     * java.lang.String, int)
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String authenticateUser(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent,
                                   boolean isAdmin) throws AuthenticationFailureException {
        return dao.authenticateUser(userSession, ipAddress, macAddress, userAgent, isAdmin);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.UserService#changePasscode(int,
     * java.lang.String, java.lang.String)
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean changePasscode(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent)
            throws AuthenticationFailureException {
        return dao.changePasscode(userSession, ipAddress, macAddress, userAgent);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.UserService#logout(int, int,
     * java.lang.String)
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean logout(int unitId, int userId, String sessionKey)
            throws AuthenticationFailureException {
        return dao.logout(unitId, userId, sessionKey);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.UserService#getEmployees(int)
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Employee> getEmployees(int unitId) throws DataNotFoundException {
        return dao.getEmployees(unitId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Employee getEmployee(int userId) {
        return dao.getEmployee(userId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Employee getEmployee(String empCode) {
        return dao.getEmployee(empCode);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public EmployeeBasicDetail getActiveEmployee(int userId) {
        return dao.getActiveEmployee(userId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Employee addEmployee(Employee employee,Department department,Designation designation) throws DataUpdationException {
        Employee emp = dao.addEmployee(employee, department,designation);
        cacheService.addEmployee(emp);
        cacheService.addEmployeeBasicDetail(MasterDataConverter.convert(emp));
        return emp;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Employee updateEmployee(Employee employee) throws DataUpdationException {
        Employee emp = dao.updateEmployee(employee);
        cacheService.addEmployee(emp);
        cacheService.addEmployeeBasicDetail(MasterDataConverter.convert(emp));
        return emp;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateStatus(int employeeId, EmploymentStatus status) throws DataUpdationException {
        return dao.updateStatus(employeeId, status);
    }

    @Transactional(rollbackFor = Exception.class , value="MasterDataSourceTM",readOnly = false, propagation = Propagation.REQUIRED)
    public List<EmployeeBasicDetail> getAllEmployees(){
        return dao.getAllEmployees();
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean authorizeMac(String macAddress) {
        return dao.authorizeMac(macAddress);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Set<String> getAuthorizedMacs() {
        @SuppressWarnings({"rawtypes", "unchecked"})
        Set<String> authorizedMacs = new HashSet();
        authorizedMacs.addAll(dao.getAuthorizedMacs());
        return authorizedMacs;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Designation getDesignation(int designationId) {
        return dao.getDesignation(designationId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Designation getDesignation(String designationName) {
        return dao.getDesignation(designationName);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Department getDepartment(int deptId) {
        return dao.getDepartment(deptId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Department getDepartment(String deptName) {
        return dao.getDepartment(deptName);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addEmployeeUnitMapping(int employeeId, List<Integer> unitIds) throws DataUpdationException {
        return dao.addEmployeeUnitMapping(employeeId, unitIds);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitBasicDetail> getUnitsForEmployee(int employeeId, boolean onlyActive) throws DataNotFoundException {
        return dao.getUnitsForEmployee(employeeId, onlyActive);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> getActiveEmpUnitIds(int employeeId) {
        return dao.getActiveEmpUnitIds(employeeId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean createPasscode(int userId, String newPasscode) throws AuthenticationFailureException {
        return dao.createPasscode(userId, newPasscode);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EmployeeBasicDetail> getEmployeesForUnit(Integer unitId) {
        List<Employee> employeeList = dao.getEmployeesForUnit(unitId);
        List<EmployeeBasicDetail> employeeBasicDetails = new ArrayList<EmployeeBasicDetail>();
        for (Employee employee : employeeList) {
            employeeBasicDetails.add(MasterDataConverter.convert(employee));
        }
        return employeeBasicDetails;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> getEmployeesForUnitTrimmed(Integer unitId) {
        List<Employee> employeeList = dao.getEmployeesForUnit(unitId);
        List<IdCodeName> employeeBasicDetails = new ArrayList<IdCodeName>();
        for (Employee employee : employeeList) {
            employeeBasicDetails.add(new IdCodeName(employee.getId(), employee.getName(), employee.getEmployeeCode()));
        }
        return employeeBasicDetails;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<EmployeeBasicDetail> getEmployeesForEmployeeMealForUnit(int unitId) {
        return dao.getEmployeesForEmployeeMealForUnit(unitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EmployeeBasicDetail> getActiveEmployees(String designation, String department) {
        return dao.getActiveEmployees(designation, department);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EmployeeBasicDetail> getAllEmployees(String designation) {
        return dao.getAllEmployees(designation);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean resetPasscode(int userId, String newPasscode, int updatedBy) throws AuthenticationFailureException {
        return dao.resetPasscode(userId, newPasscode, updatedBy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean verifyUser(UserSessionDetail userSession, String ipAddress, String macAddress, String userAgent)
            throws AuthenticationFailureException {
        return dao.verifyUser(userSession, ipAddress, macAddress, userAgent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public EmployeeBasicDetail getEmployeeBasicDetail(int userId) {
        return dao.getEmployeeBasicDetail(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public EmployeeBasicDetail getEmployeeBasicDetail(String empCode) {
        return dao.getEmployeeBasicDetail(empCode);
    }

    @Override
    public List<IdName> getActiveEmployeeIdName() {
        return dao.getActiveEmployeeDetail();
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Employee getEmployeeBySdpContact(String sdpContact) {
        return dao.getEmployeeBySdpContact(sdpContact);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.UserService#getAcl(com.stpl.tech.master
     * .domain.model.ApplicationName, int)
     */
    @Override
    public Map<String, Map<String, Boolean>> getAcl(ApplicationName appName, int employeeId, Integer companyId, Integer brandId) {
        List<AclData> list = dao.getAcl(appName, employeeId, companyId, brandId);
        Map<String, Map<String, Boolean>> acl = new HashMap<>();
        for (ActionType type : ActionType.values()) {
            acl.put(type.getDesc(), new HashMap<>());
        }
        if (list != null) {
            for (AclData data : list) {
                acl.get(ActionType.valueOf(data.getType().toUpperCase()).getDesc()).put(data.getAction(), true);
            }
        }
        return acl;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.master.core.service.UserService#updateRoles(com.stpl.tech.
     * master.domain.model.EmployeeRole)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateRoles(EmployeeRole roles) {
        boolean result = dao.deactivateRoles(roles);
        if (result) {
            for (Integer roleId : roles.getRoles()) {
                result = dao.addRole(roles.getEmployeeId(), roleId, roles.getUpdatedBy());
            }
            if (Objects.nonNull(roles.getUserPolicyId())) {
                EmployeeDetail employeeDetail = dao.find(EmployeeDetail.class, roles.getEmployeeId());
                employeeDetail.setUserPolicyData(dao.find(UserPolicyData.class, roles.getUserPolicyId()));
                EmployeeBasicDetail employeeBasicDetail = masterCache.getEmployeeBasicDetail(roles.getEmployeeId());
                employeeBasicDetail.setUserPolicyId(roles.getUserPolicyId());
                masterCache.addEmployeeDetail(employeeBasicDetail);
                dao.update(employeeDetail);
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateRolesV2(EmployeeRoleUpdateRequest request) {
        Boolean result = dao.updateEmployeeRoleBrandMappings(request);
        if (result) {
            if (Objects.nonNull(request.getUserPolicyId())) {
                EmployeeDetail employeeDetail = dao.find(EmployeeDetail.class, request.getEmployeeId());
                employeeDetail.setUserPolicyData(dao.find(UserPolicyData.class, request.getUserPolicyId()));
                EmployeeBasicDetail employeeBasicDetail = masterCache.getEmployeeBasicDetail(request.getEmployeeId());
                employeeBasicDetail.setUserPolicyId(request.getUserPolicyId());
                masterCache.addEmployeeDetail(employeeBasicDetail);
                dao.update(employeeDetail);
            }
        }
        return result;
    }

    @Override
    public Map<String, List<IdName>> getEmpListByAcl(ACLRequest aclRequest) {
        return dao.getEmpListByAcl(aclRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Employee getEmployeeByContact(String contact) {
        try {
            if (StringUtils.isEmpty(contact)) {
                return null;
            }
            return dao.getEmployeeByContact(contact);
        } catch (Exception e) {
            LOG.error("Error getting employee by contact: {}", contact, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean createUpdateUserRolesForPolicy(EmployeeRole employeeRole, Integer uploadedDocId, String departmentDesignation) throws DataUpdationException {
        if (employeeRole.getUpdatePolicy()) {
            boolean result = dao.deactivateRolesInPolicy(employeeRole);
            if (result) {
                for (Integer roleId : employeeRole.getRoles()) {
                    result = dao.addUserPolicyRole(employeeRole.getUserPolicyId(), roleId);
                }
                UserPolicyData policyData = dao.find(UserPolicyData.class, employeeRole.getUserPolicyId());
                policyData.setUpdatedAt(AppUtils.getCurrentTimestamp());
                policyData.setUpdatedBy(AppUtils.getCreatedBy(masterCache.getEmployee(employeeRole.getUpdatedBy()), employeeRole.getUpdatedBy()));
                dao.update(policyData);
            }
            return result;
        } else {
            List<UserPolicyData> userPolicyDataList = dao.checkUserPolicyExist(departmentDesignation);
            if (Objects.nonNull(userPolicyDataList) && !userPolicyDataList.isEmpty()) {
                throw new DataUpdationException("User Policy with name " + employeeRole.getPolicyName() + " already exists..!");
            } else {
                UserPolicyData userPolicyData = new UserPolicyData();
                userPolicyData.setPolicyName(employeeRole.getPolicyName());
                userPolicyData.setPolicyDescription(employeeRole.getPolicyDescription());
                userPolicyData.setDepartmentDesignation(departmentDesignation);
                userPolicyData.setUploadedDocumentId(uploadedDocId);
                userPolicyData.setCreatedBy(AppUtils.getCreatedBy(masterCache.getEmployee(employeeRole.getUpdatedBy()), employeeRole.getUpdatedBy()));
                userPolicyData.setCreatedAt(AppUtils.getCurrentTimestamp());
                userPolicyData = dao.add(userPolicyData);
                for (Integer role : employeeRole.getRoles()) {
                    UserPolicyRoleMapping userPolicyRoleMapping = new UserPolicyRoleMapping();
                    userPolicyRoleMapping.setUserPolicyId(userPolicyData.getUserPolicyId());
                    userPolicyRoleMapping.setRoleId(role);
                    userPolicyRoleMapping.setMappingStatus(AppConstants.ACTIVE);
                    dao.add(userPolicyRoleMapping);
                }
                return true;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void logRoleChangeAudit(Integer userPolicyEmployeeId, String type, Integer updatedBy, Integer uploadedDocId) {
        List<RoleChangeAuditLogData> roleChangeAuditLogDataList = new ArrayList<>();
        if (type.equalsIgnoreCase("USER_POLICY_ROLE_EDIT")) {
            UserPolicyData userPolicyData = dao.find(UserPolicyData.class, userPolicyEmployeeId);
            for (UserPolicyRoleMapping userPolicyRoleMapping : userPolicyData.getUserPolicyRoleMappingList()) {
                if (userPolicyRoleMapping.getMappingStatus().equalsIgnoreCase(AppConstants.ACTIVE)) {
                    addRoleChangeAudit(userPolicyEmployeeId, type, updatedBy, roleChangeAuditLogDataList, userPolicyRoleMapping.getRoleId(), uploadedDocId);
                }
            }
        } else {
            List<IdCodeName> employeeActiveRoles = dao.getActiveRoles(userPolicyEmployeeId);
            for (IdCodeName employeeActiveRole : employeeActiveRoles) {
                addRoleChangeAudit(userPolicyEmployeeId, type, updatedBy, roleChangeAuditLogDataList, employeeActiveRole.getId(), uploadedDocId);
            }
        }
        dao.addAll(roleChangeAuditLogDataList);
    }

    private void addRoleChangeAudit(Integer userPolicyId, String type, Integer updatedBy, List<RoleChangeAuditLogData> roleChangeAuditLogDataList, Integer roleId, Integer uploadedDocId) {
        RoleChangeAuditLogData roleChangeAuditLogData = new RoleChangeAuditLogData();
        roleChangeAuditLogData.setKeyId(userPolicyId);
        roleChangeAuditLogData.setKeyType(type);
        roleChangeAuditLogData.setRoleId(roleId);
        roleChangeAuditLogData.setUploadedDocumentId(uploadedDocId);
        roleChangeAuditLogData.setLoggedBy(AppUtils.getCreatedBy(masterCache.getEmployee(updatedBy), updatedBy));
        roleChangeAuditLogData.setLoggedAt(AppUtils.getCurrentTimestamp());
        roleChangeAuditLogDataList.add(roleChangeAuditLogData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EmployeeBasicDetail> getEmployeesWithDepartmentDesignation(Integer departmentId, Integer designationId) {
        List<EmployeeBasicDetail> result = new ArrayList<>();
        try {
            result = masterCache.getEmployeeBasicDetails().values().stream().filter(e -> Objects.nonNull(e.getDepartmentId()) && e.getDepartmentId().equals(departmentId)
                    && Objects.nonNull(e.getDesignationId()) && e.getDesignationId().equals(designationId)).collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Error getting employees with department designation :: ",e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean resetUpdateEmployeeUserPolicy(Integer employeeId, Integer policyId, Integer updatedBy, Integer uploadedDocId) {
        try {
            EmployeeDetail employeeDetail = dao.find(EmployeeDetail.class, employeeId);
            if (Objects.nonNull(employeeDetail)) {
                UserPolicyData previousPolicy = employeeDetail.getUserPolicyData();
                UserPolicyData policyData = dao.find(UserPolicyData.class, policyId);
                employeeDetail.setUserPolicyData(policyData);
                dao.update(employeeDetail);
                logEmployeeUserPolicyResetLog(previousPolicy, policyData, employeeDetail, updatedBy, uploadedDocId);
                EmployeeBasicDetail employeeBasicDetail = masterCache.getEmployeeBasicDetail(employeeId);
                employeeBasicDetail.setUserPolicyId(policyData.getUserPolicyId());
                masterCache.addEmployeeDetail(employeeBasicDetail);
                return true;
            }
        } catch (Exception e) {
            LOG.error("Error reset Updating the Employee User Policy Data :: ",e);
        }
        return false;
    }

    @Override
    public Integer getUnitCafeManager(Integer unitId) {
        return masterCache.getUnit(unitId).getUnitCafeManager();
    }

    @Override
    public Date getLastHandoverDate(Integer unitId) {
        return masterCache.getUnit(unitId).getLastHandoverDate();
    }

    @Override
    public String getLastHandoverFrom(Integer unitId) {
        return masterCache.getUnit(unitId).getLastHandoverFrom();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateEmployeeRoles(Integer employeeId, Integer updatedBy, Integer policyId, List<Integer> overrideRoles) {
        List<Integer> currentRoles = new ArrayList<>();
        UserPolicyData policyData = dao.find(UserPolicyData.class, policyId);
        for (UserPolicyRoleMapping userPolicyRoleMapping : policyData.getUserPolicyRoleMappingList()) {
            if (userPolicyRoleMapping.getMappingStatus().equalsIgnoreCase(AppConstants.ACTIVE) && !currentRoles.contains(userPolicyRoleMapping.getRoleId())) {
                currentRoles.add(userPolicyRoleMapping.getRoleId());
            }
        }
        currentRoles.addAll(overrideRoles);
        EmployeeRole employeeRole = new EmployeeRole();
        employeeRole.setEmployeeId(employeeId);
        employeeRole.setUpdatedBy(updatedBy);
        employeeRole.setRoles(currentRoles);
        updateRoles(employeeRole);
    }

    private void logEmployeeUserPolicyResetLog(UserPolicyData previousPolicy, UserPolicyData policyData, EmployeeDetail employeeDetail, Integer updatedBy, Integer uploadedDocId) {
        EmployeeUserPolicyResetLog policyResetLog = new EmployeeUserPolicyResetLog();
        policyResetLog.setEmployeeId(employeeDetail.getEmpId());
        if (Objects.nonNull(previousPolicy)) {
            policyResetLog.setPreviousUserPolicyId(previousPolicy.getUserPolicyId());
        }
        policyResetLog.setNewUserPolicyId(policyData.getUserPolicyId());
        policyResetLog.setUploadedDocumentId(uploadedDocId);
        policyResetLog.setLoggedBy(AppUtils.getCreatedBy(masterCache.getEmployee(updatedBy), updatedBy));
        policyResetLog.setLoggedAt(AppUtils.getCurrentTimestamp());
        dao.add(policyResetLog);
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.service.UserService#getActiveRoles()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> getActiveRoles() {
        List<IdCodeName> result = dao.getActiveRoles();
        Set<Integer> roleIds = result.stream().mapToInt(value -> value.getId()).boxed().collect(Collectors.toSet());
        Map<Integer, List<RoleActionDTO>> roleActionsMap = getRoleActionForRoleIds(roleIds);
        for (IdCodeName idCodeName : result) {
            if (roleActionsMap.containsKey(idCodeName.getId())) {
                RoleActionDTO roleActionDTO = roleActionsMap.get(idCodeName.getId()).get(0);
                idCodeName.setApplicationName(roleActionDTO.getApplicationName());
                idCodeName.setRoleActions(roleActionsMap.get(idCodeName.getId()));
            }
        }
        return result;
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.service.UserService#getActiveRoles(int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> getActiveRoles(int employeeId) {
        return dao.getActiveRoles(employeeId);
    }

    /* (non-Javadoc)
     * @see com.stpl.tech.master.core.service.UserService#getEmplyeePassCode(int)
     */
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getEmplyeePassCode(int userId) {
        return dao.getEmplyeePassCode(userId);
    }

    /*
     * @Override
     *
     * @Transactional(rollbackFor=Exception.class, value = "MasterDataSourceTM",
     * readOnly = true, propagation = Propagation.REQUIRED) public Map<String,
     * Map<String, Integer>> setPermissionCache(int employeeId) { Map<String,
     * Integer> permissions = dao.getEmployeePermissions(employeeId); ACLCache
     * aclCache = ACLCache.getInstance();
     * aclCache.addPermissions(Integer.valueOf(employeeId).toString(),
     * permissions); return
     * aclCache.getPermissions(Integer.valueOf(employeeId).toString()); }
     */


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> getPurchaseRoles(int empId) {
        return dao.getActiveRoles(empId).stream()
                .filter(role -> role.getCode().contains("_PURCHASER")
                        || role.getCode().contains("_APPROVER_"))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, String> getEmployeeWithRole(String role) {
        return dao.getEmployeesWithRole(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getEmployeeOnboardingView() {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "EmployeeeOnboardingSheet.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<EmployeeOnboarding> all = new ArrayList<EmployeeOnboarding>();
                writer.writeSheet(all, EmployeeOnboarding.class);
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean uploadEmployeeOnboardingSheet(MultipartFile file) throws Exception {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        List<EmployeeOnboarding> employeeList = parser.createEntity(workbook.getSheetAt(0),
                EmployeeOnboarding.class, errors::add);
        if (errors.isEmpty()) {
            LOG.info(new Gson().toJson(employeeList));
            Integer count = 0;
            for (EmployeeOnboarding onboardingData : employeeList) {
                if (onboardingData != null && !onboardingData.getName().isEmpty()) {
                    try {
                        Employee employee;
                        try {
                            employee = convertToEmployee(onboardingData);
                            employee.setEmploymentStatus(EmploymentStatus.IN_ACTIVE);
                        }catch(Exception e){
                            LOG.error("Error while converting data for employee {} at row number {} - {}", onboardingData.getName(), count, e.getMessage());
                            throw new Exception("Error while converting data for employee "+onboardingData.getName()+" at row number "+ String.valueOf(count)+" - "+e.getMessage());
                        }
                        Employee e ;
                        try {
                             e = addEmployee(employee, null, null);
                        }catch (Exception exception){
                            LOG.error("Error while adding data for employee {}  - {}", onboardingData.getName(), exception.getMessage());
                            throw new Exception("Error while adding data for employee {} ::: "+onboardingData.getName() + "There might be duplicate rows or the entry already exists ");
                        }

                        LOG.info("Employee {} successfully onboraded with employee id {} ", onboardingData.getName(), e.getId());
                    } catch (Exception e) {
                        LOG.info("Error while converting data for employee {} at row number {} - {}", onboardingData.getName(), count, e.getMessage());
                        throw new Exception(e.getMessage());
                    }
                    count += 1;
                }
            }
        } else {
            LOG.info("Error Parsing Workbook for Manpower Expenses, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        workbook.close();
        return true;
    }

    @Override
    public Integer getEmployeeIdByEmail(String userEmail) {
        return dao.getEmployeeIdByEmail(userEmail);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    Employee convertToEmployee(EmployeeOnboarding onboardingData) throws DataNotFoundException {
        Employee employee = new Employee();
        if (onboardingData.getName() != null) {
            employee.setName(onboardingData.getName());
        } else {
            throw new DataNotFoundException("Required field name missing");
        }

        if(onboardingData.getDob()!=null){
            employee.setDob(onboardingData.getDob());
        }
        if(onboardingData.getReasonForTermination()!=null){
            employee.setReasonForTermination(onboardingData.getReasonForTermination());
        }
        if(onboardingData.getHrExecutive()!=null){
            employee.setHrExecutive(onboardingData.getHrExecutive());
        }
        if(onboardingData.getLeaveApprovalAuthority()!=null){
            employee.setLeaveApprovalAuthority(onboardingData.getLeaveApprovalAuthority());
        }
        if(onboardingData.getLocCode()!=null){
            employee.setLocCode(onboardingData.getLocCode());
        } else {
            throw new DataNotFoundException("Required field Location code missing");
        }

        if (onboardingData.getGender() != null) {
            if(onboardingData.getGender().equals("M")){
                employee.setGender("M");
            }else if(onboardingData.getGender().equals("F")){
                employee.setGender("F");
            }else{
                throw new DataNotFoundException("Required data not found in gender field");
            }
        } else {
            throw new DataNotFoundException("Required field gender missing");
        }
        if (onboardingData.getEmployeeCode() != null) {
            employee.setEmployeeCode(onboardingData.getEmployeeCode());
        } else {
            throw new DataNotFoundException("Required field employee code missing");
        }
        if (onboardingData.getEmployeeEmail() != null) {
            employee.setEmployeeEmail(onboardingData.getEmployeeEmail());
        }
        if (onboardingData.getEmployeeMealEligible() != null) {
            if (onboardingData.getEmployeeMealEligible().equals("YES")) {
                employee.setEmployeeMealEligible(Boolean.TRUE);
            } else if(onboardingData.getEmployeeMealEligible().equals("NO")){
                employee.setEmployeeMealEligible(Boolean.FALSE);
            }else{
                throw new DataNotFoundException("Required data invalid for employee meal");
            }
        } else {
            throw new DataNotFoundException("Required field employee meal eligible missing");
        }
        if (Objects.nonNull(onboardingData.getBiometricId())) {
            employee.setBiometricId(onboardingData.getBiometricId());
        }
        if (onboardingData.getSlackId() != null && !onboardingData.getSlackId().isEmpty()) {
            employee.setCommunicationChannel(onboardingData.getSlackId());
        }
        if (onboardingData.getJoiningDate() != null) {
            employee.setJoiningDate(new Date(onboardingData.getJoiningDate()));
        } else {
            throw new DataNotFoundException("Required field joining date missing");
        }
        if (onboardingData.getDepartment() != null) {
            List<com.stpl.tech.master.domain.model.Department> allDepartment = masterCache.getDepartmentsValues();
            boolean found = false;
            for (com.stpl.tech.master.domain.model.Department department : allDepartment) {
                if (department.getName().toLowerCase(Locale.ROOT).equals(onboardingData.getDepartment().toLowerCase(Locale.ROOT))) {
                    employee.setDepartment(department);
                    found = true;
                    break;
                }
            }
            if (!found) {
                throw new DataNotFoundException("The value of department is not valid.");
            }
        } else {
            throw new DataNotFoundException("Required field department missing");
        }
        if (onboardingData.getDesignation() != null) {
            List<com.stpl.tech.master.domain.model.Designation> allDesignation = masterCache.getDesignationsValues();
            boolean found = false;
            for (com.stpl.tech.master.domain.model.Designation designation : allDesignation) {
                if (designation.getName().toLowerCase(Locale.ROOT).equals(onboardingData.getDesignation().toLowerCase(Locale.ROOT))) {
                    employee.setDesignation(designation);
                    found = true;
                    break;
                }
            }
            if (!found) {
                throw new DataNotFoundException("Designation value is not valid");
            }
        } else {
            throw new DataNotFoundException("Required field designation missing");
        }
        if (onboardingData.getEmploymentType()!= null) {
            if (onboardingData.getEmploymentType().trim().equals(String.valueOf(EmploymentType.FULL_TIME))) {
                employee.setEmploymentType(EmploymentType.FULL_TIME);
            } else if (onboardingData.getEmploymentType().trim().equals(String.valueOf(EmploymentType.PART_TIME))) {
                employee.setEmploymentType(EmploymentType.PART_TIME);
            } else if (onboardingData.getEmploymentType().trim().equals(String.valueOf(EmploymentType.CONSULTANT))) {
                employee.setEmploymentType(EmploymentType.CONSULTANT);
            } else {
                throw new DataNotFoundException("Employment type is not valid");
            }
        } else {
            throw new DataNotFoundException("Required field employment type missing");
        }
        if (onboardingData.getReportingManager() != null) {
            Employee reportingManager = getEmployee(onboardingData.getReportingManager());
            if (reportingManager != null) {
                employee.setReportingManager(reportingManager);
            } else {
                throw new DataNotFoundException("No emnployee found with this id of reporting manager");
            }
        } else {
            throw new DataNotFoundException("Required field reporting manager missing");
        }
        // Setting address for employee
        Address address = new Address();
        if (onboardingData.getAddressLine1() != null) {
            address.setLine1(onboardingData.getAddressLine1());
        } else {
            throw new DataNotFoundException("Required field line 1 missing");
        }
        if (onboardingData.getAddressLine2() != null) {
            address.setLine2(onboardingData.getAddressLine2());
        }
        if (onboardingData.getAddressLine3() != null) {
            address.setLine3(onboardingData.getAddressLine3());
        }
        if (onboardingData.getCity() != null) {
            address.setCity(onboardingData.getCity());
        } else {
            throw new DataNotFoundException("Required field city missing");
        }
        if (onboardingData.getState() != null) {
            address.setState(onboardingData.getState());
        } else {
            throw new DataNotFoundException("Required field state missing");
        }
        if (onboardingData.getZipCode() != null) {
            address.setZipCode(onboardingData.getZipCode());
        } else {
            throw new DataNotFoundException("Required field zip code missing");
        }
        if (onboardingData.getContact1() != null) {
            address.setContact1(onboardingData.getContact1());
        } else {
            throw new DataNotFoundException("Required field contact 1 missing");
        }
        if (onboardingData.getContact2() != null) {
            address.setContact2(onboardingData.getContact2());
        }
        if(onboardingData.getCountry()!=null) {
            address.setCountry(onboardingData.getCountry());
        }else{
            throw new DataNotFoundException("Required field Country missing");
        }
        address.setAddressType("OFFICIAL");
        employee.setCurrentAddress(address);
        employee.setEmploymentStatus(EmploymentStatus.ACTIVE);
        return employee;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean addEmployeeAppMapping(EmployeeApplicationMapping employeeMapping) {
        List<EmployeeApplicationMappingDetail> employeeApplicationMapping = new ArrayList<>();
        for(String mappingValue: employeeMapping.getMappingValue()){
            EmployeeApplicationMappingDetail mappingDetail = new EmployeeApplicationMappingDetail();
            mappingDetail.setEmployeeId(employeeMapping.getEmployeeId());
            mappingDetail.setMappingValue(mappingValue.toUpperCase());
            mappingDetail.setMappingType(employeeMapping.getMappingType());
            mappingDetail.setRecordStatus(employeeMapping.getRecordStatus());
            employeeApplicationMapping.add(mappingDetail);
        }
        List<EmployeeApplicationMappingDetail> data = dao.addAll(employeeApplicationMapping);
        if(data.isEmpty()){
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateEmployeeAppMapping(EmployeeApplicationMapping employeeMapping) {
       return dao.updateEmployeeAppMapping(employeeMapping);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public EmployeeApplicationMapping getEmpAppMapping(int employeeId) {
        Map<String, List<Pair<String, String>>> data = dao.getEmpAppMapping(employeeId);
        EmployeeApplicationMapping emp = new EmployeeApplicationMapping();
        if (Objects.nonNull(data)) {
            List<String> values = new ArrayList<>();
            for (Map.Entry<String, List<Pair<String, String>>> e : data.entrySet()) {
                String type = e.getKey();
                for (Pair<String, String> p : e.getValue()) {
                    if (p.getValue().equals(AppConstants.ACTIVE)) {
                        values.add(p.getKey());
                    }
                }
                if (!values.isEmpty()) {
                    emp.setMappingType(type);
                    emp.setMappingValue(values);
                    emp.setEmployeeId(employeeId);
                    emp.setRecordStatus(AppConstants.ACTIVE);
                    break;
                }
            }
        }
        return emp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public  Boolean verifyUserForOrderCancellation(Integer empId, String passcode){
        return dao.verifyUserForCancellation(empId,passcode);
    }


    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public String getLastActivityTime(String system1, String system2){
        String date =  dao.getLastActivityTime(system1,system2);
        return date;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Boolean addLastActivityTime(String system1,String system2,String date){
        return dao.addLastActivityTime(system1,system2,date);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Boolean updateEmpData(Employee employeeDetail,Department department,Designation designation){
        try{

            Employee  updateEmpData = dao.updateEmpData(employeeDetail,department,designation);
            Boolean val = Objects.nonNull(updateEmpData);
            if(val){
                employeeDetail.setId(updateEmpData.getId());
                cacheService.addEmployee(employeeDetail);
                cacheService.addEmployeeBasicDetail(MasterDataConverter.convert(employeeDetail));
            }

            return val;
        }catch(Exception e){
            LOG.error("Error in updating employee detail ",e);
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void setEmployeeInActive(String empId,String empContact){
        try{
           dao.setEmployeeInactive(empId,empContact);
        }catch(Exception e){
            LOG.error("Error in setting employee inactive ",e);
        }
    }


    //    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public Integer lookUpDepartmentDesignation(com.stpl.tech.master.domain.model.Department department, String designationName){
        List<com.stpl.tech.master.domain.model.Designation> designations =  department.getDesignations();
        if(Objects.nonNull(designations) && !designations.isEmpty()){
            for(com.stpl.tech.master.domain.model.Designation designation : designations){
               if(designation.getName().equalsIgnoreCase(designationName))
                  return designation.getId();
            }
        }
        return 0;
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Boolean addDepartmentDesignationMapping(Integer deptId , Integer designationId){
        return dao.addDepartmentDesignationMapping(deptId,designationId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public BusinessDivision getBusinessDivision(Integer businessDivId){
        return dao.getBusinessDivision(businessDivId);
    }

    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void inValidateSessionCache(List<Integer> unitId){
        dao.inValidateSessionCache(unitId);
    }

    @Override
    public Integer getPosVersionEnabledForUnit(Integer unitId){
        return masterCache.getUnit(unitId).getPosVersion();
    }

    @Override
    public String getFaDaycloseEnabledForUnit(Integer unitId) {
        return masterCache.getUnit(unitId).getFaDaycloseEnabled();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetailDTO uploadDocument(FileTypeDTO type, MimeType mimeType, DocUploadTypeDTO docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException {
        String fileName = docName + "_" + AppUtils.getCurrentTimeISTStringWithNoColons()  + "."
                + mimeType.name().toLowerCase();
        String baseDir = "FILE" + File.separator + AppUtils.getCurrentYear() + File.separator
                + AppUtils.getCurrentMonthName() + File.separator + AppUtils.getCurrentDayofMonth();
        return uploadDocument(type, mimeType, docType, userId, file, fileName, baseDir);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UserPolicyDataDTO> getUserPolicies() {
        List<UserPolicyDataDTO> result = new ArrayList<>();
        Set<Integer> roleIds = new HashSet<>();
        try {
            List<UserPolicyData> userPolicyData = dao.findAll(UserPolicyData.class);
            if (Objects.nonNull(userPolicyData) && !userPolicyData.isEmpty()) {
                for (UserPolicyData policyData : userPolicyData) {
                    UserPolicyDataDTO userPolicyDataDTO = DtoDataMapper.INSTANCE.toUserPolicyDataDTO(policyData);
                    for (UserPolicyRoleMapping userPolicyRoleMapping : policyData.getUserPolicyRoleMappingList()) {
                        if (userPolicyRoleMapping.getMappingStatus().equalsIgnoreCase(AppConstants.ACTIVE)) {
                            UserPolicyRoleMappingDTO userPolicyRoleMappingDTO = DtoDataMapper.INSTANCE.toUserPolicyRoleMappingDTO(userPolicyRoleMapping);
                            roleIds.add(userPolicyRoleMappingDTO.getRoleId());
                            if (Objects.isNull(userPolicyDataDTO.getUserPolicyRoleMappings())) {
                                userPolicyDataDTO.setUserPolicyRoleMappings(new ArrayList<>());
                            }
                            userPolicyDataDTO.getUserPolicyRoleMappings().add(userPolicyRoleMappingDTO);
                        }
                    }
                    result.add(userPolicyDataDTO);
                }
                Map<Integer, List<RoleActionDTO>> roleActionsMap = getRoleActionForRoleIds(roleIds);
                for (UserPolicyDataDTO  userPolicyDataDTO :  result) {
                    if (Objects.nonNull(userPolicyDataDTO.getUserPolicyRoleMappings())) {
                        for (UserPolicyRoleMappingDTO mappingDTO : userPolicyDataDTO.getUserPolicyRoleMappings()) {
                            if (roleActionsMap.containsKey(mappingDTO.getRoleId())) {
                                RoleActionDTO roleActionDTO = roleActionsMap.get(mappingDTO.getRoleId()).get(0);
                                mappingDTO.setRoleName(roleActionDTO.getRoleName());
                                mappingDTO.setApplicationName(roleActionDTO.getApplicationName());
                                mappingDTO.setRoleActions(roleActionsMap.get(mappingDTO.getRoleId()));
                                mappingDTO.setRoleDescription(roleActionDTO.getRoleDescription());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting user policies :: ", e);
        }
        return result;
    }

    private Map<Integer, List<RoleActionDTO>> getRoleActionForRoleIds(Set<Integer> roleIds) {
        List<Object[]> roleActionObjectList = dao.getRoleActionMappingsForRoles(roleIds);
        Map<Integer, List<RoleActionDTO>> roleActionsMap = new HashMap<>();
        roleActionObjectList.forEach(roleAction -> {
            RoleActionDTO roleActionDTO = new RoleActionDTO();
            roleActionDTO.setRoleId((Integer) roleAction[0]);
            roleActionDTO.setRoleName((String) roleAction[1]);
            roleActionDTO.setRoleDescription((String) roleAction[2]);
            roleActionDTO.setApplicationId((Integer) roleAction[3]);
            roleActionDTO.setActionDetailId((Integer) roleAction[4]);
            roleActionDTO.setActionCode((String) roleAction[5]);
            roleActionDTO.setActionType((String) roleAction[6]);
            roleActionDTO.setActionCategory((String) roleAction[7]);
            roleActionDTO.setActionDescription((String) roleAction[8]);
            roleActionDTO.setApplicationName((String) roleAction[9]);
            List<RoleActionDTO> list;
            if (roleActionsMap.containsKey(roleActionDTO.getRoleId())) {
                list = roleActionsMap.get(roleActionDTO.getRoleId());
            } else {
                list = new ArrayList<>();
            }
            list.add(roleActionDTO);
            roleActionsMap.put(roleActionDTO.getRoleId(), list);
        });
        return roleActionsMap;
    }

    public DocumentDetailDTO uploadDocument(FileTypeDTO type, MimeType mimeType, DocUploadTypeDTO docType, Integer userId, MultipartFile file, String fileName, String baseDir) throws IOException, DocumentException {
        File destFile = null;
        MultipartFile compressedFile = null;
        if(mimeType.equals(MimeType.PDF)) {
            LOG.info("#########Uploaded FIle is PDF , Trying To Compress ###### ");
            //Compression of PDF
            try{
                compressedFile = compressPdf(destFile, file,compressedFile);
                LOG.info("###### size before compression : {}", file.getSize());
                LOG.info("###### Size After Compression : {}", compressedFile.getSize());
            }catch (MasterException e){
                LOG.info("######Error While Compressing File , Uploading Without Compression");
                compressedFile = null;
            }
        }else if(mimeType.equals(MimeType.PNG)){
            LOG.info("#######Uploaded File Is Of PNG Format. Uploading Without Compression .");
        }else{
            LOG.info("#######Uploaded File is Of {} Format, Trying To Compress", mimeType.extension());
            try{
                byte[] imageByte = compressImage(file, mimeType.extension());
                compressedFile = new MockMultipartFile(file.getName(),
                        file.getOriginalFilename(), file.getContentType(), imageByte);
                LOG.info("###### size before compression : {}", file.getSize());
                LOG.info("###### Size After Compression : {}", compressedFile.getSize());
            }catch (Exception e){
                LOG.info("#######Error While Compressing Image , Uploading Without Compression");
                compressedFile = null;
            }
        }


        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(env.getProperty("amazon.s3.bucket", "chaayosdevtest"), baseDir, fileName, Objects.isNull(compressedFile) ? file : compressedFile);
            DocumentDetailDTO documentDetailDTO = new DocumentDetailDTO();
            documentDetailDTO.setMimeType(mimeType);
            documentDetailDTO.setUploadType(docType);
            documentDetailDTO.setFileType(type);
            documentDetailDTO.setDocumentLink(fileName);
            documentDetailDTO.setS3Key(s3File.getKey());
            documentDetailDTO.setFileUrl(s3File.getUrl());
            documentDetailDTO.setS3Bucket(s3File.getBucket());
            documentDetailDTO.setUpdatedBy(MasterDataConverter.generateIdCodeName(userId, "", masterCache.getEmployee(userId)));
            documentDetailDTO.setUpdateTime(AppUtils.getCurrentTimestamp());
            DocumentDetailData data = dao.add(MasterDataConverter.convert(documentDetailDTO));
            if(Objects.nonNull(destFile)){
                destFile.delete();
            }
            if (data.getDocumentId() != null) {
                return MasterDataConverter.convert(data);
            }

        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }

    public MultipartFile compressPdf(File destFile , MultipartFile file , MultipartFile compressedFile) throws IOException, DocumentException, MasterException {
        try {
            String dest = env.getProperty("server.base.dir") + file.getOriginalFilename();
            destFile = new File(dest);
            PdfReader reader = new PdfReader(file.getInputStream());
            PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(dest), PdfWriter.VERSION_1_5);
            stamper.getWriter().setCompressionLevel(9);
            int total = reader.getNumberOfPages() + 1;
            for (int i = 1; i < total; i++) {
                reader.setPageContent(i, reader.getPageContent(i));
            }
            stamper.setFullCompression();
            stamper.close();
            reader.close();
            FileInputStream input = new FileInputStream(destFile);
            compressedFile = new MockMultipartFile(file.getName(),
                    file.getOriginalFilename(), file.getContentType(), IOUtils.toByteArray(input));
            input.close();
        }catch (Exception e) {
            LOG.info("Error While Compressing PDF : {} " , file.getOriginalFilename());
            LOG.info(e.getMessage());
            throw new MasterException("Error While Compressing PDF",e.getMessage());
        }
        return  compressedFile;
    }

    public byte[] compressImage(MultipartFile image , String mimeType) throws IOException
    {

        InputStream inputStream = image.getInputStream();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        float imageQuality = 0.6f;

        // Create the buffered image
        BufferedImage bufferedImage = ImageIO.read(inputStream);

        // Get image writers
        Iterator<ImageWriter> imageWriters = ImageIO.getImageWritersByFormatName("jpeg"); // Input your Format Name here

        if (!imageWriters.hasNext())
            throw new IllegalStateException("Writers Not Found!!");

        ImageWriter imageWriter = imageWriters.next();
        ImageOutputStream imageOutputStream = ImageIO.createImageOutputStream(outputStream);
        imageWriter.setOutput(imageOutputStream);

        ImageWriteParam imageWriteParam = imageWriter.getDefaultWriteParam();

        // Set the compress quality metrics
        imageWriteParam.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        imageWriteParam.setCompressionQuality(imageQuality);

        // Compress and insert the image into the byte array.
        imageWriter.write(null, new IIOImage(bufferedImage, null, null), imageWriteParam);

        byte[] imageBytes = outputStream.toByteArray();

        // close all streams
        inputStream.close();
        outputStream.close();
        imageOutputStream.close();
        imageWriter.dispose();


        return imageBytes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer sendEmployeeCredentialsEmail(List<Integer> employeeIds) {
        LOG.info("Starting to send employee credentials email to employees without passwords");

        try {
            // Step 1: Fetch all employees who don't have entries in EmployeePassCode table
            List<Employee> employeesWithoutPasscodes = dao.getEmployeesWithoutPasscodes(employeeIds);
            if (employeesWithoutPasscodes == null || employeesWithoutPasscodes.isEmpty()) {
                LOG.info("No employees found without passcodes");
                return 0;
            }

            LOG.info("Found {} employees without passcodes", employeesWithoutPasscodes.size());

            // Step 2: Generate passwords for each employee and maintain a HashMap
            Map<Integer, String> employeePasscodeMap = new HashMap<>();
            int passwordGenerationCount = 0;

            for (Employee employee : employeesWithoutPasscodes) {
                try {
                    Integer employeeId = employee.getId();
                    String employeeName = employee.getName();
                    
                    // Generate new password
                    String newPassword = PasswordImpl.generateRandomPassword(employeeId, employeeName);
                    
                    // Set the password using resetPasscode function
                    boolean passwordSet = this.resetPasscode(employeeId, newPassword, AppConstants.SYSTEM_EMPLOYEE_ID);
                    
                    if (passwordSet) {
                        // Store in HashMap for later use
                        employeePasscodeMap.put(employeeId, newPassword);
                        passwordGenerationCount++;
                        LOG.info("Generated new password for employee ID: {}, Name: {}", employeeId, employeeName);
                    } else {
                        LOG.error("Failed to generate password for employee ID: {}", employeeId);
                    }
                } catch (Exception e) {
                    LOG.error("Error generating password for employee ID: {}, Error: {}", employee.getId(), e.getMessage(), e);
                }
            }

            LOG.info("Generated passwords for {} employees", passwordGenerationCount);

            // Step 3: Send emails to employees whose passwords were just created
            int successCount = 0;
            int failureCount = 0;

            // Determine environment type and from email
            EnvType envType = masterProperties.getEnvironmentType();
            String fromEmail = AppConstants.REPORTING_EMAIL;

            for (Employee employee : employeesWithoutPasscodes) {
                try {
                    Integer employeeId = employee.getId();
                    String employeeName = employee.getName();
                    String employeeEmail = employee.getEmployeeEmail();

                    // Skip if employee doesn't have email or password wasn't generated
                    if (employeeEmail == null || employeeEmail.trim().isEmpty()) {
                        LOG.warn("Employee ID: {} doesn't have email address, skipping", employeeId);
                        continue;
                    }

                    if (!employeePasscodeMap.containsKey(employeeId)) {
                        LOG.warn("Employee ID: {} password wasn't generated, skipping email", employeeId);
                        continue;
                    }

                    String decryptedPasscode = employeePasscodeMap.get(employeeId);

                    LOG.info("Sending credentials email to employee ID: {}, Email: {}", employeeId, employeeEmail);

                    // Create and send email notification
                    EmployeeCredentialsEmailNotification emailNotification =
                            new EmployeeCredentialsEmailNotification(
                                    employeeEmail,
                                    employeeName,
                                    employeeId,
                                    decryptedPasscode,
                                    envType,
                                    fromEmail
                            );

                    emailNotification.sendEmail();
                    successCount++;

                    LOG.info("Successfully sent credentials email to employee ID: {}", employeeId);

                } catch (Exception e) {
                    failureCount++;
                    LOG.error("Failed to send credentials email to employee ID: {}, Error: {}",
                            employee.getId(), e.getMessage(), e);
                }
            }

            LOG.info("Employee credentials email sending completed. Success: {}, Failures: {}, Passwords Generated: {}",
                    successCount, failureCount, passwordGenerationCount);

            return successCount;

        } catch (Exception e) {
            LOG.error("Error in sendEmployeeCredentialsEmail", e);
            throw new RuntimeException("Failed to send employee credentials emails", e);
        }
    }

}
