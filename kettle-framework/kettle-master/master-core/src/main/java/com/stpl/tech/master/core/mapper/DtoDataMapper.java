/*
 * Created By Shanmukh
 */

package com.stpl.tech.master.core.mapper;

import com.stpl.tech.master.data.model.LCDMenuImage;
import com.stpl.tech.master.data.model.LCDMenuImageVariant;
import com.stpl.tech.master.data.model.LCMMenuVariantMetadataGroup;
import com.stpl.tech.master.data.model.LCMMenuVariantMetadataItem;
import com.stpl.tech.master.data.model.UnitClosureEvent;
import com.stpl.tech.master.data.model.UnitClosureState;
import com.stpl.tech.master.data.model.UnitClosureStateEvent;
import com.stpl.tech.master.data.model.UserPolicyData;
import com.stpl.tech.master.data.model.UserPolicyRoleMapping;
import com.stpl.tech.master.domain.model.LCDMenuImageDomain;
import com.stpl.tech.master.domain.model.LCDMenuImageVariantDomain;
import com.stpl.tech.master.domain.model.LCMMenuVariantMetadataGroupDomain;
import com.stpl.tech.master.domain.model.LCMMenuVariantMetadataItemDomain;
import com.stpl.tech.master.domain.model.UnitClosureEventDomain;
import com.stpl.tech.master.domain.model.UnitClosureStateDomain;
import com.stpl.tech.master.domain.model.UnitClosureStateEventDomain;
import com.stpl.tech.master.domain.model.UserPolicyDataDTO;
import com.stpl.tech.master.domain.model.UserPolicyRoleMappingDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DtoDataMapper {

    DtoDataMapper INSTANCE = Mappers.getMapper(DtoDataMapper.class);

    UserPolicyDataDTO toUserPolicyDataDTO(UserPolicyData userPolicyData);

    UserPolicyRoleMappingDTO toUserPolicyRoleMappingDTO(UserPolicyRoleMapping userPolicyRoleMapping);

    List<UnitClosureEventDomain> toUnitClosureEventDtoList(List<UnitClosureEvent> unitClosureEvents);

    List<UnitClosureStateEventDomain> toUnitClosureStateEventsDtoList(List<UnitClosureStateEvent> unitClosureStateEvents);

    UnitClosureStateDomain toUnitClosureStateDto(UnitClosureState stateEvent);


}
