package com.stpl.tech.master.data.dao;

import java.util.List;

import com.stpl.tech.master.data.model.RiderUnitMapping;

public interface RiderMappingDao {

	boolean addMapping(int employeeId, int unitId);

	boolean deleteMapping(int employeeId, int unitId);

	boolean disableMapping(int employeeId, int unitId);

	boolean enableMapping(int employeeId, int unitId);

	List<RiderUnitMapping> getMapping(int unitId, List<String> mappingStatus);

	List<RiderUnitMapping> getMapping(int employeeId);

}