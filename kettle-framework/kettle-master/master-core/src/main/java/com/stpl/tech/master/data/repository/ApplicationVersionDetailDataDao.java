package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.ApplicationVersionDetailData;
import com.stpl.tech.util.AppConstants;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ApplicationVersionDetailDataDao extends JpaRepository<ApplicationVersionDetailData,Integer> {
     List<ApplicationVersionDetailData> findByUnitIdAndApplicationNameAndVersionStatus(Integer unitId, String applicationName, String status);

     List<ApplicationVersionDetailData> findByUnitIdAndTerminalAndApplicationNameInAndVersionStatus(Integer unitId,Integer terminalId,List<String> applicationName, String status);

     @Modifying
     @Query(value = "UPDATE APPLICATION_VERSION_DETAIL_DATA ave SET ave.VERSION_STATUS = ?4 WHERE ave.UNIT_ID IN (?1) AND ave.TERMINAL IN (?2) AND ave.APPLICATION_NAME = ?3", nativeQuery = true)
     public void updateUnitVersionStatus(Integer unitId,Integer terminalId,String applicationName, String status);
}
