/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 3 Aug, 2015 5:36:58 PM by Hibernate Tools 4.0.0

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * UnitTaxMapping generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_TAX_MAPPING")
public class UnitTaxMapping implements java.io.Serializable {

	private Integer unitTaxMappingId;
	private TaxProfile taxProfile;
	private UnitDetail unitDetail;
	private BigDecimal taxPercentage;
	private String profileStatus;
	private String state;

	public UnitTaxMapping() {
	}

	public UnitTaxMapping(TaxProfile taxProfile, UnitDetail unitDetail, BigDecimal taxPercentage, String profileStatus,
			String state) {
		this.taxProfile = taxProfile;
		this.unitDetail = unitDetail;
		this.taxPercentage = taxPercentage;
		this.profileStatus = profileStatus;
		this.state = state;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_TAX_MAPPING_ID", unique = true, nullable = false)
	public Integer getUnitTaxMappingId() {
		return this.unitTaxMappingId;
	}

	public void setUnitTaxMappingId(Integer unitTaxMappingId) {
		this.unitTaxMappingId = unitTaxMappingId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TAX_PROFILE_ID", nullable = false)
	public TaxProfile getTaxProfile() {
		return this.taxProfile;
	}

	public void setTaxProfile(TaxProfile taxProfile) {
		this.taxProfile = taxProfile;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_ID", nullable = false)
	public UnitDetail getUnitDetail() {
		return this.unitDetail;
	}

	public void setUnitDetail(UnitDetail unitDetail) {
		this.unitDetail = unitDetail;
	}

	@Column(name = "TAX_PERCENTAGE", nullable = false, precision = 10)
	public BigDecimal getTaxPercentage() {
		return this.taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

	@Column(name = "PROFILE_STATUS", nullable = false, length = 20)
	public String getProfileStatus() {
		return this.profileStatus;
	}

	public void setProfileStatus(String profileStatus) {
		this.profileStatus = profileStatus;
	}

	@Column(name = "STATE", nullable = false, length = 20)
	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}
}
