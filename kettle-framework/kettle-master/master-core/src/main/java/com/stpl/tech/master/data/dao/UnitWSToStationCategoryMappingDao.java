/*
 * Created By <PERSON><PERSON><PERSON>
 */

package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.UnitWSToStationCategoryMappingData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitWSToStationCategoryMappingDao extends JpaRepository<UnitWSToStationCategoryMappingData, Integer> {

    List<UnitWSToStationCategoryMappingData> findByUnitIdAndMappingStatus(Integer unitId, String mappingStatus);
}
