package com.stpl.tech.master.core.apps.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.monk.configuration.model.AppBuildVersionData;
import com.stpl.tech.master.monk.configuration.model.AppsVersionMetadata;
import com.stpl.tech.master.monk.configuration.model.ArduinoBuildData;
import com.stpl.tech.master.monk.configuration.model.ArduinoBuildForUnit;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-05-2018.
 */

public interface AppsManagementService {

    public AppsVersionMetadata findLatestVersion();

    public List<AppsVersionMetadata> findAllActiveVersions();

    public AppsVersionMetadata uploadNewBuild(AppsVersionMetadata version);

    public AppsVersionMetadata activateVersion(AppsVersionMetadata version);

    public AppBuildVersionData checkAndReturnVersion(AppBuildVersionData apkVersion);

    public ArduinoBuildData initiateBuild(Integer userId);

    public ArduinoBuildData getInitiatedArduinoBuild(String versionName) throws DataNotFoundException;

    public ArduinoBuildData uploadArduinoBuild(MultipartFile file, Integer userId, String monk, String bucket, Integer unitId, String versionName) throws DataNotFoundException;

    public boolean activateArduinoBuild(ArduinoBuildData buildData);

    public ArduinoBuildForUnit getActiveArduinoBuild(Integer unit) throws DataNotFoundException;

    public ArduinoBuildData getActiveArduinoBuild() throws DataNotFoundException;

    public ArduinoBuildData getInitiatedArduinoBuild() throws DataNotFoundException;

}
