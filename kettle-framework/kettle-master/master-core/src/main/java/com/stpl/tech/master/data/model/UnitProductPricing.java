/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

// Generated 21 Jul, 2015 8:03:01 PM by Hibernate Tools 4.0.0

import com.stpl.tech.master.core.service.batchProcess.batchAnnotation.BatchProcess;
import com.stpl.tech.master.domain.model.UnitProductPriceSheetDetail;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * UnitProductPricing generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_PRODUCT_PRICING", uniqueConstraints = @UniqueConstraint(columnNames = {"UNIT_PROD_REF_ID",
        "DIMENSION_CODE"}))
@SqlResultSetMapping(name = "UnitProductPriceSheetDetail", classes = @ConstructorResult(targetClass = UnitProductPriceSheetDetail.class, columns = {
        @ColumnResult(name = "unitId", type = Integer.class),
        @ColumnResult(name = "unitName", type = String.class),
        @ColumnResult(name = "productId", type = Integer.class),
        @ColumnResult(name = "productName", type = String.class),
        @ColumnResult(name = "dimensionCode", type = Integer.class),
        @ColumnResult(name = "dimension", type = String.class),
        @ColumnResult(name = "unitCategory", type = String.class),
        @ColumnResult(name = "pricingProfile", type = Integer.class),
        @ColumnResult(name = "brandId", type = Integer.class),
        @ColumnResult(name = "unitRegion", type = String.class),
        @ColumnResult(name = "rtlCode", type = String.class),
        @ColumnResult(name = "rlCode", type = String.class),
        @ColumnResult(name = "productStatus", type = String.class),
        @ColumnResult(name = "unitProductMappingId", type = Integer.class),
        @ColumnResult(name = "unitProductMappingStatus", type = String.class),
        @ColumnResult(name = "unitProductPriceId", type = Integer.class),
        @ColumnResult(name = "unitProductPricingStatus", type = String.class),
        @ColumnResult(name = "price", type = BigDecimal.class),}))

@SqlResultSetMapping(name = "UnitProductPriceCategoryDetail", classes = @ConstructorResult(targetClass = UnitProductPriceCategoryDetail.class, columns = {
        @ColumnResult(name = "unitId", type = Integer.class),
        @ColumnResult(name = "unitName", type = String.class),
        @ColumnResult(name = "productId", type = Integer.class),
        @ColumnResult(name = "productName", type = String.class),
        @ColumnResult(name = "dimensionCode", type = Integer.class),
        @ColumnResult(name = "dimension", type = String.class),
        @ColumnResult(name = "unitCategory", type = String.class),
        @ColumnResult(name = "pricingProfile", type = Integer.class),
        @ColumnResult(name = "brandId", type = Integer.class),
        @ColumnResult(name = "unitRegion", type = String.class),
        @ColumnResult(name = "rtlCode", type = String.class),
        @ColumnResult(name = "rlCode", type = String.class),
        @ColumnResult(name = "productStatus", type = String.class),
        @ColumnResult(name = "unitProductMappingId", type = Integer.class),
        @ColumnResult(name = "unitProductMappingStatus", type = String.class),
        @ColumnResult(name = "unitProductPriceId", type = Integer.class),
        @ColumnResult(name = "unitProductPricingStatus", type = String.class),
        @ColumnResult(name = "price", type = BigDecimal.class),
        @ColumnResult(name = "aliasProductName", type = String.class),
        @ColumnResult(name = "pricingCategory", type = String.class),
}))
@BatchProcess(batchSize = 300)
public class UnitProductPricing implements java.io.Serializable {

    private Integer unitProdPriceId;
    private RefLookup refLookup;
    private UnitProductMapping unitProductMapping;
    private Date lastUpdateTmstmp;
    private BigDecimal price;
    private BigDecimal cost;
    private BigDecimal codCost;
    private Integer buffer;
    private Integer threshold;
    private String recipeProfile;
    private String status = "ACTIVE";
    private Integer updatedBy;
    private String aliasProductName;
    private String dimensionDescriptor;
    private String isDeliveryOnlyProduct;
    private String pickDineInConsumables;

    public UnitProductPricing() {
    }

    public UnitProductPricing(RefLookup refLookup, UnitProductMapping unitProductMapping, Date lastUpdateTmstmp) {
        this.refLookup = refLookup;
        this.unitProductMapping = unitProductMapping;
        this.lastUpdateTmstmp = lastUpdateTmstmp;
    }

    public UnitProductPricing(RefLookup refLookup, UnitProductMapping unitProductMapping, Date lastUpdateTmstmp,
                              BigDecimal price, String recipeProfile) {
        this.refLookup = refLookup;
        this.unitProductMapping = unitProductMapping;
        this.lastUpdateTmstmp = lastUpdateTmstmp;
        this.price = price;
        this.recipeProfile = recipeProfile;
    }

    public UnitProductPricing(RefLookup refLookup, UnitProductMapping unitProductMapping, Date lastUpdateTmstmp,
                              BigDecimal price, String recipeProfile, String status) {
        this.refLookup = refLookup;
        this.unitProductMapping = unitProductMapping;
        this.lastUpdateTmstmp = lastUpdateTmstmp;
        this.price = price;
        this.recipeProfile = recipeProfile;
        this.status = status;
    }



    public UnitProductPricing(RefLookup refLookup, UnitProductMapping unitProductMapping, Date lastUpdateTmstmp,
                              BigDecimal price, String recipeProfile, String status,String aliasProductName,String isdeliveryOnly,
                              String pickDineInConsumables,String dimensionDescriptor) {
        this.refLookup = refLookup;
        this.unitProductMapping = unitProductMapping;
        this.lastUpdateTmstmp = lastUpdateTmstmp;
        this.price = price;
        this.recipeProfile = recipeProfile;
        this.status = status;
        this.aliasProductName  = aliasProductName;
        this.isDeliveryOnlyProduct = isdeliveryOnly;
        this.pickDineInConsumables = pickDineInConsumables;
        this.dimensionDescriptor = dimensionDescriptor;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "UNIT_PROD_PRICE_ID", unique = true, nullable = false)
    public Integer getUnitProdPriceId() {
        return this.unitProdPriceId;
    }

    public void setUnitProdPriceId(Integer unitProdPriceId) {
        this.unitProdPriceId = unitProdPriceId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DIMENSION_CODE", nullable = false)
    public RefLookup getRefLookup() {
        return this.refLookup;
    }

    public void setRefLookup(RefLookup refLookup) {
        this.refLookup = refLookup;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "UNIT_PROD_REF_ID", nullable = false)
    public UnitProductMapping getUnitProductMapping() {
        return this.unitProductMapping;
    }

    public void setUnitProductMapping(UnitProductMapping unitProductMapping) {
        this.unitProductMapping = unitProductMapping;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
    public Date getLastUpdateTmstmp() {
        return this.lastUpdateTmstmp;
    }

    public void setLastUpdateTmstmp(Date lastUpdateTmstmp) {
        this.lastUpdateTmstmp = lastUpdateTmstmp;
    }

    @Column(name = "PRICE", precision = 10)
    public BigDecimal getPrice() {
        return this.price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "COST", precision = 10)
    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    @Column(name = "COD_COST", precision = 10)
    public BigDecimal getCodCost() {
        return codCost;
    }

    public void setCodCost(BigDecimal codCost) {
        this.codCost = codCost;
    }

    @Column(name = "BUFFER_QUANTITY")
    public Integer getBuffer() {
        return buffer;
    }

    public void setBuffer(Integer buffer) {
        this.buffer = buffer;
    }

    @Column(name = "THRESHOLD_QUANTITY")
    public Integer getThreshold() {
        return threshold;
    }

    public void setThreshold(Integer threshold) {
        this.threshold = threshold;
    }

    @Column(name = "RECIPE_PROFILE", nullable = false)
    public String getRecipeProfile() {
        return recipeProfile;
    }

    public void setRecipeProfile(String recipeProfile) {
        this.recipeProfile = recipeProfile;
    }

    @Column(name = "PRICING_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "UPDATED_BY")
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "ALIAS_PRODUCT_NAME")
    public String getAliasProductName() {
        return aliasProductName;
    }

    public void setAliasProductName(String aliasProductName) {
        this.aliasProductName = aliasProductName;
    }

    @Column(name = "DIMENSION_DESCRIPTOR")
    public String getDimensionDescriptor() {
        return dimensionDescriptor;
    }
    public void setDimensionDescriptor(String dimensionDescriptor) {
        this.dimensionDescriptor = dimensionDescriptor;
    }

    @Column(name = "IS_DELIVERY_ONLY_PRODUCT")
    public String getIsDeliveryOnlyProduct() {
        return isDeliveryOnlyProduct;
    }

    public void setIsDeliveryOnlyProduct(String isDeliveryOnlyProduct) {
        this.isDeliveryOnlyProduct = isDeliveryOnlyProduct;
    }

    @Column(name = "PICK_DINE_IN_CONSUMABLES")
    public String getPickDineInConsumables() {
        return pickDineInConsumables;
    }

    public void setPickDineInConsumables(String pickDineInConsumables) {
        this.pickDineInConsumables = pickDineInConsumables;
    }

}
