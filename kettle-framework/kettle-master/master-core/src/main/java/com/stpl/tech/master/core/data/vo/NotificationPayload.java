package com.stpl.tech.master.core.data.vo;

import java.util.Date;
import java.util.Map;

public class NotificationPayload {

    private String messageType;
    private Integer orderId;
    private Integer customerId;
    private String contactNumber;
    private boolean whatsappOptIn;
    private boolean sendWhatsapp;
    private Date requestTime;
    private Map<String,String> payload;

    public NotificationPayload() {
    }

    public NotificationPayload(String messageType, Integer orderId, Integer customerId,
                               boolean whatsappOptIn, boolean sendWhatsapp, Date requestTime) {
        this.messageType = messageType;
        this.orderId = orderId;
        this.customerId = customerId;
        this.whatsappOptIn = whatsappOptIn;
        this.sendWhatsapp = sendWhatsapp;
        this.requestTime = requestTime;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public boolean isWhatsappOptIn() {
        return whatsappOptIn;
    }

    public void setWhatsappOptIn(boolean whatsappOptIn) {
        this.whatsappOptIn = whatsappOptIn;
    }

    public boolean isSendWhatsapp() {
        return sendWhatsapp;
    }

    public void setSendWhatsapp(boolean sendWhatsapp) {
        this.sendWhatsapp = sendWhatsapp;
    }

    public Date getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Date requestTime) {
        this.requestTime = requestTime;
    }

    public Map<String, String> getPayload() {
        return payload;
    }

    public void setPayload(Map<String, String> payload) {
        this.payload = payload;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }
}
