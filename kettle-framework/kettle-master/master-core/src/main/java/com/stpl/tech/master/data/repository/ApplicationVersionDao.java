package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.ApplicationVersionDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApplicationVersionDao extends JpaRepository<ApplicationVersionDetail,Integer> {

    ApplicationVersionDetail findByApplicationVersionAndApplicationName(String applicationVersion,String applicationName);

    List<ApplicationVersionDetail> findByStatus(String status);

}
