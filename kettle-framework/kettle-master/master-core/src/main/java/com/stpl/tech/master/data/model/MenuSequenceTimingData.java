package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "MENU_SEQUENCE_TIMING_DATA")
public class MenuSequenceTimingData {

    private Integer id;
    private Integer menuSequenceId;
    private Integer productGroupParentId;
    private String service;
    private String time1From;
    private String time1To;
    private String time2From;
    private String time2To;
    private String time3From;
    private String time3To;
    private boolean dayMonday;
    private boolean dayTuesday;
    private boolean dayWednesday;
    private boolean dayThursday;
    private boolean dayFriday;
    private boolean daySaturday;
    private boolean daySunday;
    private String startDate;
    private String endDate;

    private String daySlots;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MENU_SEQUENCE_TIMING_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "MENU_SEQUENCE_ID", nullable = false)
    public Integer getMenuSequenceId() {
        return menuSequenceId;
    }

    public void setMenuSequenceId(Integer menuSequenceId) {
        this.menuSequenceId = menuSequenceId;
    }
    @Column(name = "PRODUCT_GROUP_PARENT_ID", nullable = false)
    public Integer getProductGroupParentId() {
        return productGroupParentId;
    }

    public void setProductGroupParentId(Integer productGroupParentId) {
        this.productGroupParentId = productGroupParentId;
    }
    @Column(name = "SERVICE", nullable = true)
    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    @Column(name = "TIME_1_FROM", nullable = true)
    public String getTime1From() {
        return time1From;
    }

    public void setTime1From(String time1From) {
        this.time1From = time1From;
    }

    @Column(name = "TIME_1_TO", nullable = true)
    public String getTime1To() {
        return time1To;
    }

    public void setTime1To(String time1To) {
        this.time1To = time1To;
    }

    @Column(name = "TIME_2_FROM", nullable = true)
    public String getTime2From() {
        return time2From;
    }

    public void setTime2From(String time2From) {
        this.time2From = time2From;
    }

    @Column(name = "TIME_2_TO", nullable = true)
    public String getTime2To() {
        return time2To;
    }

    public void setTime2To(String time2To) {
        this.time2To = time2To;
    }

    @Column(name = "TIME_3_FROM", nullable = true)
    public String getTime3From() {
        return time3From;
    }

    public void setTime3From(String time3From) {
        this.time3From = time3From;
    }

    @Column(name = "TIME_3_TO", nullable = true)
    public String getTime3To() {
        return time3To;
    }

    public void setTime3To(String time3To) {
        this.time3To = time3To;
    }

    @Column(name = "DAY_MONDAY", nullable = true)
    public boolean isDayMonday() {
        return dayMonday;
    }

    public void setDayMonday(boolean dayMonday) {
        this.dayMonday = dayMonday;
    }

    @Column(name = "DAY_TUESDAY", nullable = true)
    public boolean isDayTuesday() {
        return dayTuesday;
    }

    public void setDayTuesday(boolean dayTuesday) {
        this.dayTuesday = dayTuesday;
    }

    @Column(name = "DAY_WEDNESDAY", nullable = true)
    public boolean isDayWednesday() {
        return dayWednesday;
    }

    public void setDayWednesday(boolean dayWednesday) {
        this.dayWednesday = dayWednesday;
    }

    @Column(name = "DAY_THURSDAY", nullable = true)
    public boolean isDayThursday() {
        return dayThursday;
    }

    public void setDayThursday(boolean dayThursday) {
        this.dayThursday = dayThursday;
    }

    @Column(name = "DAY_FRIDAY", nullable = true)
    public boolean isDayFriday() {
        return dayFriday;
    }

    public void setDayFriday(boolean dayFriday) {
        this.dayFriday = dayFriday;
    }

    @Column(name = "DAY_SATURDAY", nullable = true)
    public boolean isDaySaturday() {
        return daySaturday;
    }

    public void setDaySaturday(boolean daySaturday) {
        this.daySaturday = daySaturday;
    }

    @Column(name = "DAY_SUNDAY", nullable = true)
    public boolean isDaySunday() {
        return daySunday;
    }

    public void setDaySunday(boolean daySunday) {
        this.daySunday = daySunday;
    }

    @Column(name = "START_DATE", nullable = true)
    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @Column(name = "END_DATE", nullable = true)
    public String  getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }


    @Column(name = "DAY_SLOTS")
    public String getDaySlots() {
        return daySlots;
    }

    public void setDaySlots(String daySlots) {
        this.daySlots = daySlots;
    }
}
