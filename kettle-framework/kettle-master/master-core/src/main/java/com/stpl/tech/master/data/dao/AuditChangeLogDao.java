package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.mongo.AuditChangeLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;


@Repository

public interface AuditChangeLogDao extends MongoRepository<AuditChangeLog, String> {
    @Query("{'keyId' : {'$eq' : ?0} , 'keyType' : {'$eq' : ?1 }}")
    public AuditChangeLog findByKeyId(Integer keyId , String keyType);

}
