package com.stpl.tech.master.core.external.notification.service.impl;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.gson.Gson;
import com.stpl.tech.master.core.data.vo.GoogleChatSpace;
import com.stpl.tech.master.core.data.vo.GoogleChatSpaces;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.service.GoogleChatService;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class GoogleChatServiceImpl implements GoogleChatService {


    private static final Logger LOG = LoggerFactory.getLogger(GoogleChatServiceImpl.class);
    private static final String BASE_URL = "https://chat.googleapis.com";
    private static final String[] SCOPES = {"https://www.googleapis.com/auth/chat.bot"};
    private static final Gson gson = new Gson();
    private static Map<String,String> spacesCache;

    private static URL GET_SPACES;
    static {
        try {
            GET_SPACES = new URL( BASE_URL + "/v1/spaces?pageSize=1000");
        } catch (MalformedURLException e) {

        }
    }

    @PostConstruct
    public void createSpacesCache() {
        LOG.info("POST-CONSTRUCT GoogleChatServiceImpl - STARTED");
        spacesCache = new HashMap<>();
        initSpacesCache();
    }

    private void initSpacesCache(){
        GoogleChatSpaces googleChatSpaces= getSpaces();
        for(GoogleChatSpace space:googleChatSpaces.getSpaces()){
            spacesCache.put(space.getDisplayName(),space.getName());
        }
    }

    private static GoogleCredential credentials = new GoogleCredential();

    public GoogleCredential getGoogleCredential() {
        if(Objects.isNull(credentials.getAccessToken())){
            credentials = addGoogleCredentialToMap();
        }
        return credentials;
    }

    public String getAccessToken(GoogleCredential googleCredential) throws IOException {
        googleCredential.refreshToken();
        return googleCredential.getAccessToken();
    }

    private HttpURLConnection getConnection(URL url, String accessToken) throws IOException {
        HttpURLConnection conn = null;
        conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Authorization", "Bearer " + accessToken);
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        return conn;
    }

    private GoogleCredential addGoogleCredentialToMap(){
        String keyFile = "google-chatapi.json";
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(keyFile);
        GoogleCredential googleCredential = null;
        try {
            googleCredential = GoogleCredential.fromStream(inputStream)
                    .createScoped(Arrays.asList(SCOPES));
            return googleCredential;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public GoogleChatSpaces getSpaces(){
        try {
            String accessToken = getAccessToken(getGoogleCredential());
            HttpURLConnection connection = getConnection(GET_SPACES, accessToken);
            connection.connect();
            BufferedReader rd = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            StringBuffer response = new StringBuffer();
            while ((line = rd.readLine()) != null) {
                response.append(line).append("\n");
            }
            rd.close();
            connection.disconnect();
            return gson.fromJson(response.toString(),GoogleChatSpaces.class);
        }catch (Exception e){
            LOG.error("Exception occurred while getting google chat spaces ", e);
        }
        return null;
    }

    public String getSpaceId(String spaceName, EnvType env){
        if(Objects.isNull(spacesCache)){
            spacesCache = new HashMap<>();
            initSpacesCache();
            if(Objects.isNull(spacesCache)){
                return null;
            }
        }
        if(spacesCache.containsKey(spaceName)){
            return spaceName +","+spacesCache.get(spaceName);
        }else{
            for(int i=0;i<3;i++){
                spacesCache = new HashMap<>();
                initSpacesCache();
                if(spacesCache.containsKey(spaceName)){
                    return spaceName +","+spacesCache.get(spaceName);
                }
            }
            return SlackNotification.COMMON_NOTIFICATION_CHANNEL.getChannel(env)+","+spacesCache.get(SlackNotification.COMMON_NOTIFICATION_CHANNEL.getChannel(env));
        }
    }


}
