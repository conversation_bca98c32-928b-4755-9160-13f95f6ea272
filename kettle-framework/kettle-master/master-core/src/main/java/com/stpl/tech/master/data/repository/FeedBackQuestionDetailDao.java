package com.stpl.tech.master.data.repository;

import com.stpl.tech.master.data.model.FeedbackQuestionsDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Repository
public interface FeedBackQuestionDetailDao extends JpaRepository<FeedbackQuestionsDetail,Integer> {

    List<FeedbackQuestionsDetail> findByQuestionType(String questionType);

    List<FeedbackQuestionsDetail> findByIdInAndQuestionTypeAndQuestionStatus(List<Integer> id,String questionType,String questionStatus);

}
