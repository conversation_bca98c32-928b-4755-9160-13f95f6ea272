/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

/**
 * UnitDetail generated by hbm2java
 */
@Entity
@Table(name = "BRAND_DETAIL")
public class BrandDetail implements java.io.Serializable {

    private static final long serialVersionUID = -9133952863692186923L;
    private Integer brandId;
    private String brandName;
    private String brandCode;
    private String tagLine;
    private String domain;
    private String billTag;
    private String websiteLink;
    private String status;
    private String supportContact;
    private String supportEmail;
    private String verbiage;
    private String brandContactCode;

    public BrandDetail() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "BRAND_ID", unique = true, nullable = false)
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "BRAND_NAME", nullable = false)
    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    @Column(name = "BRAND_CODE", nullable = false)
    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    @Column(name = "BRAND_TAGLINE", nullable = false)
    public String getTagLine() {
        return tagLine;
    }

    public void setTagLine(String tagLine) {
        this.tagLine = tagLine;
    }

    @Column(name = "BRAND_DOMAIN", nullable = false)
    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    @Column(name = "BRAND_BILL_TAGLINE", nullable = false)
    public String getBillTag() {
        return billTag;
    }

    public void setBillTag(String billTag) {
        this.billTag = billTag;
    }

    @Column(name = "BRAND_WEBSITE_LINK", nullable = false)
    public String getWebsiteLink() {
        return websiteLink;
    }

    public void setWebsiteLink(String websiteLink) {
        this.websiteLink = websiteLink;
    }

    @Column(name = "BRAND_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "BRAND_SUPPORT_CONTACT", nullable = false)
    public String getSupportContact() {
        return supportContact;
    }

    public void setSupportContact(String supportContact) {
        this.supportContact = supportContact;
    }

    @Column(name = "BRAND_SUPPORT_EMAIL", nullable = false)
    public String getSupportEmail() {
        return supportEmail;
    }

    public void setSupportEmail(String supportEmail) {
        this.supportEmail = supportEmail;
    }

    @Column(name = "BRAND_VERBIAGE", nullable = false)
    public String getVerbiage() {
        return verbiage;
    }

    public void setVerbiage(String verbiage) {
        this.verbiage = verbiage;
    }

    @Column(name = "BRAND_CONTACT_CODE")
    public String getBrandContactCode() {
        return brandContactCode;
    }

    public void setBrandContactCode(String brandContactCode) {
        this.brandContactCode = brandContactCode;
    }
}