/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core;

import com.stpl.tech.master.domain.model.CouponMapping;

import java.util.ArrayList;
import java.util.List;

public enum CouponMappingType {

	NUMBER_OF_DAYS, CUSTOMER, CONTACT_NUMBER, UNIT, UNIT_REGION,CITY, ORDER_SOURCE, CHANNEL_PARTNER, PAYMENT_MODE, PRODUCT, PRODUCT_CATEGORY, PRODUCT_SUB_CATEGORY, NEW_CUSTOMER, FIRST_ORDER,FREEBIE_PRODUCT,ACQUISITION_SOURCE,UNIT_SUB_CATEGORY ;

	private List<CouponMapping> values;

	protected List<CouponMapping> getValues() {
		return this.values;
	}

	protected void refreshValues() {
		this.values = new ArrayList<>();
	}

}
