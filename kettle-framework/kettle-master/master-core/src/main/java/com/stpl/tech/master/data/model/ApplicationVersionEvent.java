package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "APPLICATION_VERSION_EVENT")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationVersionEvent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID", unique = true, nullable = false)
    private Integer eventId;
    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;
    @Column(name = "APPLICATION_NAME", nullable = false)
    private String applicationName;
    @Column(name = "APPLICATION_VERSION", nullable = false)
    private String applicationVersion;
    @Column(name = "UNIT_REGION", nullable = false)
    private String unitRegion;
    @Column(name = "UPDATED_BY", nullable = false)
    private String updatedBy;
    @Column(name = "UPDATION_TIME", nullable = false)
    private Date updatedTime;
    @Column(name = "TERMINAL_ID")
    private Integer terminalId;
    @Column(name = "EVENT_STATUS", nullable = false)
    private String status;
}
