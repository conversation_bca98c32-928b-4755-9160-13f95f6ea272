package com.stpl.tech.master.data.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "UNIT_TO_PARTNER_DQR_MAPPING")
public class UnitToPartnerDqrMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MAPPING_ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "PARTNER_NAME", nullable = false)
    private String partnerName;

    @Column(name = "STATUS", nullable = false)
    private String status;

    @Column(name = "MERCHANT_ID", nullable = false)
    private String merchantId;

    @Column(name = "MERCHANT_KEY", nullable = false)
    private  String merchantKey;
}
