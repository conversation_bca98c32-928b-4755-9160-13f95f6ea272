package com.stpl.tech.master.util;

import com.stpl.tech.master.core.service.RecipeService;
import com.stpl.tech.master.data.dao.MonkRecipeVersioningDao;
import com.stpl.tech.master.data.dao.ProductCheckListDao;
import com.stpl.tech.master.data.dao.RecipeDao;
import com.stpl.tech.master.data.model.CriticalProductMenuToSCMMapData;
import com.stpl.tech.master.data.model.ProductCheckListEvent;
import com.stpl.tech.master.data.model.ProductImageMapping;
import com.stpl.tech.master.data.model.UnitProductMapping;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MenuType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.PriceProfileKey;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductCheckListExcelSheet;
import com.stpl.tech.master.domain.model.ProductDimensionKey;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeData;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeVersionData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.master.core.service.impl.RecipeServiceImpl.getCriticalProducts;

@Service
@Log4j2
public class ProductCheckListUtil {

    @Autowired
    private ProductCheckListDao productCheckListDao;

    @Autowired
    private RecipeDao recipeDao;

    @Autowired
    private MonkRecipeVersioningDao monkRecipeVersioningDao;

    private final Logger LOG = LoggerFactory.getLogger(ProductCheckListUtil.class);

    public String checkForProductStatus(Product product) {
        return AppConstants.ACTIVE.equals(productCheckListDao.getProductStatus(product)) ?
                AppConstants.YES : AppConstants.NO;
    }

    public List<UnitProductMapping> getUnitProductMapping(Product product, List<Integer> unitIds) {
        return productCheckListDao.getUnitProductMapping(product, unitIds);
    }

    public String checkForUnitProductMapping(String status) {
        return AppConstants.ACTIVE.equals(status) ? AppConstants.YES : AppConstants.NO;
    }

    public Map<Integer, Pair<String, String>> checkForMenuProductMapping(Map<Integer, Map<MenuType, Integer>> menuSequenceIdsByPartnerAndSlot,
                                                                         Integer productId, List<Integer> menuSequenceIds) {
        Map<Integer, Pair<String, String>> statusReasonMapByPartner = new HashMap<>();

        List<Integer> menuSequenceIdsFromDao = productCheckListDao.getMenuProductMappings(productId, menuSequenceIds);

        for (Integer partnerId : menuSequenceIdsByPartnerAndSlot.keySet()) {
            Pair<String, String> statusReasonPair = new Pair<>();
            Map<MenuType, Integer> menuTypeSequenceMap = menuSequenceIdsByPartnerAndSlot.get(partnerId);
            String status = AppConstants.YES;
            List<String> reason = new ArrayList<>();
            for (Map.Entry<MenuType, Integer> entry : menuTypeSequenceMap.entrySet()) {
                if(AppConstants.CHANNEL_PARTNER_DINE_IN_APP == partnerId && MenuType.DEFAULT.equals(entry.getKey()) ){
                    continue;
                }
                if (!menuSequenceIdsFromDao.contains(entry.getValue())) {
                    status = AppConstants.NO;
                    reason.add(String.valueOf(entry.getKey()));
                }
            }
            statusReasonPair.setKey(status);
            if (AppConstants.YES.equals(status)) {
                statusReasonPair.setValue("-");
            } else {
                statusReasonPair.setValue(String.join(", ", reason));
            }
            statusReasonMapByPartner.put(partnerId, statusReasonPair);
        }

        return statusReasonMapByPartner;
    }

    public Pair<String, String> checkForImageMappings(Integer productId, List<String> imageTypes) {
        List<ProductImageMapping> imageMappings = productCheckListDao.getProductImageMapping(productId);
        Pair<String, String> statusReasonPair = new Pair<>();
        String status = AppConstants.YES;
        List<String> reason = new ArrayList<>();

        Map<String, String> imageTypeStatusMap = new HashMap<>();
        for (ProductImageMapping imageMapping : imageMappings) {
            imageTypeStatusMap.put(imageMapping.getImageType(), imageMapping.getStatus());
        }
        for (String type : imageTypes) {
            if (!imageTypeStatusMap.containsKey(type)) {
                status = AppConstants.NO;
                reason.add(type);
            }
        }
        statusReasonPair.setKey(status);

        if (AppConstants.YES.equals(status)) {
            statusReasonPair.setValue("-");
        } else {
            statusReasonPair.setValue(String.join(", ", reason));
        }

        return statusReasonPair;
    }

    public String checkForProductRecipeStatus(Integer productId, List<String> statusList, Integer dimensionInfoId,
                                              String profile, Date liveDate) {
        List<RecipeDetail> recipeDetails = recipeDao.findRecipeByStatusesOfProduct(productId, statusList, dimensionInfoId, profile);

        if (Objects.nonNull(profile)) {
            // First Check For "IN_PROGRESS"
            for (RecipeDetail recipeDetail : recipeDetails) {
                if (AppConstants.IN_PROGRESS.equals(recipeDetail.getStatus()) && liveDate.compareTo(recipeDetail.getStartDate()) >= AppConstants.ZERO) {
                    return AppConstants.YES;
                }
            }
            // If not "IN_PROGRESS", check for "ACTIVE"
            for (RecipeDetail recipeDetail : recipeDetails) {
                if (AppConstants.ACTIVE.equals(recipeDetail.getStatus()) && liveDate.compareTo(recipeDetail.getStartDate()) >= AppConstants.ZERO) {
                    return AppConstants.YES;
                }
            }
        }
        return AppConstants.NO;
    }

    public void checkForProductRecipeMisMatch(Product product, List<String> statusList, Integer dimensionInfoId,
                                                String profile, Date liveDate , ProductCheckListExcelSheet productCheckListExcelSheet) {
        try{
            List<RecipeDetail> recipeDetails = recipeDao.findRecipeByStatusesOfProduct(product.getId(), statusList, dimensionInfoId, profile);
            RecipeDetail mappedRecipe = null;
            if (Objects.nonNull(profile)) {
                // First Check For "IN_PROGRESS"
                for (RecipeDetail recipeDetail : recipeDetails) {
                    if (AppConstants.IN_PROGRESS.equals(recipeDetail.getStatus()) && liveDate.compareTo(recipeDetail.getStartDate()) >= AppConstants.ZERO) {
                        mappedRecipe = recipeDetail;
                        break;
                    }
                }
                // If not "IN_PROGRESS", check for "ACTIVE"
                if(Objects.isNull(mappedRecipe)){
                    for (RecipeDetail recipeDetail : recipeDetails) {
                        if (AppConstants.ACTIVE.equals(recipeDetail.getStatus()) && liveDate.compareTo(recipeDetail.getStartDate()) >= AppConstants.ZERO) {
                            mappedRecipe = recipeDetail;
                            break;
                        }
                    }
                }
            }
            if(Objects.nonNull(mappedRecipe)) {
                Map<RecipeDetail, List<CriticalProductMenuToSCMMapData>> criticalProductKeys = new HashMap<>();
                getCriticalProducts(mappedRecipe, criticalProductKeys,new HashMap<>());
                if(!CollectionUtils.isEmpty(criticalProductKeys) && Boolean.FALSE.equals(product.isInventoryTracked())){
                    productCheckListExcelSheet.setInventoryTrackValid(AppConstants.NO);
                    productCheckListExcelSheet.setInventoryTrackInValidReason("Critical Products in Recipe But Product Inventory is not Tracked");
                }else if(CollectionUtils.isEmpty(criticalProductKeys) && Boolean.TRUE.equals(product.isInventoryTracked())){
                    productCheckListExcelSheet.setInventoryTrackValid(AppConstants.NO);
                    productCheckListExcelSheet.setInventoryTrackInValidReason("Product Inventory is Tracked But No Critical Products in Recipe");
                }else{
                    productCheckListExcelSheet.setInventoryTrackValid(AppConstants.YES);
                    productCheckListExcelSheet.setInventoryTrackInValidReason("");
                }
            }else{
                productCheckListExcelSheet.setInventoryTrackValid(AppConstants.NO);
                productCheckListExcelSheet.setInventoryTrackInValidReason("No Recipe Mapped with Product");
            }
        }catch (Exception e){
            log.error("Error While Setting Inventory Track Mismatch Status : {} ",e);
        }


    }


    public String checkForPriceProfileMapping(Map<Integer, PriceProfileKey> unitPriceProfileMap,
                                              Integer unitId , ProductDimensionKey productDimensionKey,
                                              Map<PriceProfileKey, Map<ProductDimensionKey, BigDecimal>> priceProfileProductPriceMap) {
        PriceProfileKey priceProfileKey = unitPriceProfileMap.get(unitId);
        if (Objects.nonNull(priceProfileKey)) {
          Map<ProductDimensionKey,BigDecimal> productPriceMap = priceProfileProductPriceMap.get(priceProfileKey);
          if (!CollectionUtils.isEmpty(productPriceMap)) {
              if (productPriceMap.containsKey(productDimensionKey)) {
                  return AppConstants.YES;
              }
          }
        }
        return AppConstants.NO;
    }

    public String checkForHotBeverageMonkRecipe(Product product, IdCodeName dimension, String status, String region) {
        if (!AppConstants.HOT_BEVERAGE_PRODUCT_TYPE.equals(product.getType()) || !(Objects.isNull(product.getStationCategoryName())
                || AppConstants.HOT_BEVERAGE_STATION_CATEGORY_NAME.equals(product.getStationCategoryName()))) {
            return AppConstants.NOT_APPLICABLE;
        }
        List<MonkRecipeVersionData> monkRecipeVersionDataList = monkRecipeVersioningDao.findByStatusAndRegion(status, region);
        for (MonkRecipeVersionData monkRecipeVersionData : monkRecipeVersionDataList) {
            List<MonkRecipeData> content = monkRecipeVersionData.getContent();
            for (MonkRecipeData item : content) {
                if (item.getProductId() == product.getId() && item.getDimension().equals(dimension.getCode())) {
                    if (AppConstants.HOT_BEVERAGE_PRODUCT_TYPE.equals(product.getType()) &&
                            (Objects.isNull(product.getStationCategoryName()) || AppConstants.HOT_BEVERAGE_STATION_CATEGORY_NAME.
                                    equals(product.getStationCategoryName()))) {
                        return AppConstants.YES;
                    }
                }
            }
        }
        return AppConstants.NO;
    }

    public List<UnitPartnerBrandKey> convert(List<Integer> codMappingsForCheckList, Integer unitId, Integer brandId) {
        List<UnitPartnerBrandKey> formattedCodMappings = new ArrayList<>();
        for (Integer id : codMappingsForCheckList) {
            formattedCodMappings.add(new UnitPartnerBrandKey(unitId, brandId, id));
        }
        return formattedCodMappings;
    }

    public ProductCheckListEvent getProductCheckListEventById(Integer eventId) {
        return productCheckListDao.getProductCheckListEventById(eventId);
    }

}
