package com.stpl.tech.master.core.external.notification.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.external.notification.dao.TemperatureLoggerDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.TemperatureLogDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

/**
 * Created by Chaayos on 21-09-2016.
 */
@Repository
public class TemperatureLoggerDaoImpl extends AbstractMasterDaoImpl implements TemperatureLoggerDao {

	@Override
	public List<TemperatureLogDetail> getTemperatureDetail(String locationId, String deviceName, Date logTime) {
		Query query = manager.createQuery(
				"FROM TemperatureLogDetail t where t.locationId = :locationId and t.deviceName = :deviceName and t.logTime >= :logTime order by t.deviceLocation asc, t.logTime desc");
		query.setParameter("locationId", locationId);
		query.setParameter("deviceName", deviceName);
		query.setParameter("logTime", logTime);
		return query.getResultList();
	}

	@Override
	public void markAsNotified(Integer id, boolean notified) {
		TemperatureLogDetail log = manager.find(TemperatureLogDetail.class, id);
		log.setIsNotified(AppConstants.getValue(notified));
		if(notified) {
			log.setNotificationTime(AppUtils.getCurrentTimestamp());	
		}
		manager.flush();
	}

	@Override
	public List<TemperatureLogDetail> findAll(Date startTime, Date endTime) {
		Query query = manager.createQuery(
			"FROM TemperatureLogDetail t where t.logTime  BETWEEN :startTime AND :endTime  order by logTime desc");
		query.setParameter("startTime",startTime );
		query.setParameter("endTime",endTime );
		return query.getResultList();

	}
}
