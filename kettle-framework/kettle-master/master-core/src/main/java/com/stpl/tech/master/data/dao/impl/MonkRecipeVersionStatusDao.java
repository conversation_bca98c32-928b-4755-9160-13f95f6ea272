package com.stpl.tech.master.data.dao.impl;

import com.stpl.tech.master.data.model.MonkRecipeVersionDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MonkRecipeVersionStatusDao extends JpaRepository<MonkRecipeVersionDetail, Integer> {

    public MonkRecipeVersionDetail findByUnitId(Integer unitId);
}
