package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "UNIT_BRAND_MAPPING")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnitBrandMapping implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UNIT_BRAND_MAPPING_ID")
    private Integer id;

    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;

    @Column(name = "BRAND_ID", nullable = false)
    private Integer brandId;

    @Column(name = "MAPPING_STATUS", nullable = false)
    private String status;

    @Column(name = "CREATION_TIMESTAMP", nullable = false)
    @Temporal(value = TemporalType.TIMESTAMP)
    private Date creationTimestamp;

    @Column(name = "CREATED_BY", nullable = false)
    private Integer createdBy;

    @Column(name = "UPDATION_TIMESTAMP")
    @Temporal(value = TemporalType.TIMESTAMP)
    private Date updationTimestamp;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

}
