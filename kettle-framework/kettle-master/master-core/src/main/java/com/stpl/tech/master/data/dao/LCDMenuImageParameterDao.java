package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.LCDMenuImage;
import com.stpl.tech.master.data.model.LCDMenuImageParameter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LCDMenuImageParameterDao extends JpaRepository<LCDMenuImageParameter, Long> {
    
    List<LCDMenuImageParameter> findByLcdMenuImage(LCDMenuImage lcdMenuImage);
    
    List<LCDMenuImageParameter> findByStepNameAndStepValue(String stepName, String stepValue);
    
    void deleteByLcdMenuImage(LCDMenuImage lcdMenuImage);
}
