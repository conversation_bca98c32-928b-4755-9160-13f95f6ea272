package com.stpl.tech.master.data.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "FICO_DETAIL")
public class FicoDetailData implements Serializable {

	private static final long serialVersionUID = 8646014408370991769L;

	private int ficodetailId;
	private int unitId;
	private String name;
	private String companyName;
	private int registeredAddressId;
	private String gstin;
	private String emailId;
	private String contact1;
	private String contact2;
	private String reportingEmailId;
	private String offerCode;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "FICO_DETAIL_ID", unique = true, nullable = false)
	public int getFicodetailId() {
		return ficodetailId;
	}

	public void setFicodetailId(int ficodetailId) {
		this.ficodetailId = ficodetailId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "FICO_NAME", nullable = false)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "COMPANY_NAME", nullable = false)
	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	@Column(name = "REGISTERED_ADDRESS_ID", nullable = false)
	public int getRegisteredAddressId() {
		return registeredAddressId;
	}

	public void setRegisteredAddressId(int registeredAddressId) {
		this.registeredAddressId = registeredAddressId;
	}

	@Column(name = "GSTIN", nullable = false)
	public String getGstin() {
		return gstin;
	}

	public void setGstin(String gstin) {
		this.gstin = gstin;
	}

	@Column(name = "EMAIL_ID", nullable = false)
	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	@Column(name = "CONTACT_1", nullable = false)
	public String getContact1() {
		return contact1;
	}

	public void setContact1(String contact1) {
		this.contact1 = contact1;
	}

	@Column(name = "CONTACT_2", nullable = true)
	public String getContact2() {
		return contact2;
	}

	public void setContact2(String contact2) {
		this.contact2 = contact2;
	}

	@Column(name = "REPORTING_EMAIL_ID", nullable = false)
	public String getReportingEmailId() {
		return reportingEmailId;
	}

	public void setReportingEmailId(String reportingEmailId) {
		this.reportingEmailId = reportingEmailId;
	}

	@Column(name = "OFFER_CODE", nullable = true)
	public String getOfferCode() {
		return offerCode;
	}

	public void setOfferCode(String offerCode) {
		this.offerCode = offerCode;
	}

	@Override
	public String toString() {
		return "FicoDetailData [ficodetailId=" + ficodetailId + ", unitId=" + unitId + ", name=" + name
				+ ", companyName=" + companyName + ", registeredAddressId=" + registeredAddressId + ", gstin=" + gstin
				+ ", emailId=" + emailId + ", contact1=" + contact1 + ", contact2=" + contact2 + ", reportingEmailId="
				+ reportingEmailId + ", offerCode=" + offerCode + "]";
	}

}
