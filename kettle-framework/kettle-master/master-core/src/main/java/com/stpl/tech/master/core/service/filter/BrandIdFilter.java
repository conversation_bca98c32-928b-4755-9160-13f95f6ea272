package com.stpl.tech.master.core.service.filter;

import com.stpl.tech.util.domain.RequestContext;
import com.stpl.tech.util.domain.RequestHolder;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Objects;

@Component
public class BrandIdFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {
        if (request instanceof HttpServletRequest httpRequest) {
            RequestHolder requestHolder = RequestContext.getContext();
            String brandId = httpRequest.getHeader("brandId");
            if (Objects.nonNull(brandId) && brandId.matches("\\d+")) {
                Integer brandIdInt = Integer.parseInt(brandId);
                requestHolder.setBrandId(brandIdInt);
            }
            String companyId = httpRequest.getHeader("companyId");
            if (Objects.nonNull(companyId) && companyId.matches("\\d+")) {
                Integer companyIdInt = Integer.parseInt(companyId);
                requestHolder.setCompanyId(companyIdInt);
            }
            RequestContext.setContext(requestHolder);
        }

        try {
            chain.doFilter(request, response);
        } finally {
            RequestContext.clearContext();
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }

}
