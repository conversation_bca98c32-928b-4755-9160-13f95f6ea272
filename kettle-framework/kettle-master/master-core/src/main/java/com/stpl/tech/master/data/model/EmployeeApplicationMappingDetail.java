package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;


@Entity
@Table(name = "EMPLOYEE_APPLICATION_MAPPING_DETAIL")
public class EmployeeApplicationMappingDetail {

    Integer mappingId;
    Integer employeeId;
    String mappingType;
    String mappingValue;
    String recordStatus;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "MAPPING_ID", unique = true, nullable = false)
    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    @Column(name = "EMPLOYEE_ID")
    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    @Column(name = "MAPPING_TYPE")
    public String getMappingType() {
        return mappingType;
    }

    public void setMappingType(String mappingType) {
        this.mappingType = mappingType;
    }

    @Column(name = "MAPPING_VALUE")
    public String getMappingValue() {return mappingValue;}

    public void setMappingValue(String mappingValue) {this.mappingValue = mappingValue;}

    @Column(name = "RECORD_STATUS")
    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }
}
