package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "GROUP_UPSELLING_MAPPING", schema = "KETTLE_MASTER_STAGE")
public class GroupUpsellingMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GROUP_UPSELLING_MAPPING_ID", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "GROUP_ID", nullable = false)
    private UnitGroupMappingData groupId;

    @Column(name = "UPSELLING_ID")
    private Integer upsellingId;

    @Column(name = "DAY_SLOT", length = 100)
    private String daySlot;

    @Column(name = "PARTNER_ID", length = 100)
    private Integer partnerId;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "STATUS", length = 45)
    private String status;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "CREATION_TIME")
    private Date creationTime;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

    @Column(name = "UPDATION_TIME")
    private Date updationTime;
}