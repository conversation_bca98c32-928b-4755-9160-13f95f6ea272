package com.stpl.tech.master.data.model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "UNIT_CLOSURE_FORM_DATA")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UnitClosureFormData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UNIT_CLOSURE_FORM_DATA_ID")
    Integer unitClosureFormDataId;

    @ManyToOne
    @JoinColumn(name="UNIT_CLOSURE_EVENT_ID")
    UnitClosureEvent unitClosureEvent;

    @ManyToOne
    @JoinColumn(name="UNIT_CLOSURE_FORM_METADATA_ID")
    UnitClosureFormMetaData unitClosureMetaData;

    @Column(name = "DATE")
    Date date;
    @Column(name = "COMMENT")
    String comment;
    @Column(name = "ATTACHMENT_ID")
    Integer attachmentId;


}
