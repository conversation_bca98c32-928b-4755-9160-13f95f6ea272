/*
 * Created By Shanmukh
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "USER_POLICY_DATA")
public class UserPolicyData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "USER_POLICY_ID", unique = true, nullable = false)
    private Integer userPolicyId;

    @Column(name = "POLICY_NAME")
    private String policyName;

    @Column(name = "POLICY_DESCRIPTION")
    private String policyDescription;

    @Column(name = "DEPARTMENT_DESIGNATION")
    private String departmentDesignation;

    @Column(name = "UPLOADED_DOCUMENT_ID")
    private Integer uploadedDocumentId;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Column(name = "UPDATED_BY")
    private String updatedBy;

    @Column(name = "UPDATED_AT")
    private Date updatedAt;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "userPolicyId")
    private List<UserPolicyRoleMapping> userPolicyRoleMappingList;

    public Integer getUserPolicyId() {
        return this.userPolicyId;
    }

    public void setUserPolicyId(Integer userPolicyId) {
        this.userPolicyId = userPolicyId;
    }

    public String getPolicyName() {
        return this.policyName;
    }

    public void setPolicyName(String policyName) {
        this.policyName = policyName;
    }

    public String getPolicyDescription() {
        return policyDescription;
    }

    public void setPolicyDescription(String policyDescription) {
        this.policyDescription = policyDescription;
    }

    public String getDepartmentDesignation() {
        return departmentDesignation;
    }

    public void setDepartmentDesignation(String departmentDesignation) {
        this.departmentDesignation = departmentDesignation;
    }

    public Integer getUploadedDocumentId() {
        return uploadedDocumentId;
    }

    public void setUploadedDocumentId(Integer uploadedDocumentId) {
        this.uploadedDocumentId = uploadedDocumentId;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<UserPolicyRoleMapping> getUserPolicyRoleMappingList() {
        return userPolicyRoleMappingList;
    }

    public void setUserPolicyRoleMappingList(List<UserPolicyRoleMapping> userPolicyRoleMappingList) {
        this.userPolicyRoleMappingList = userPolicyRoleMappingList;
    }
}
