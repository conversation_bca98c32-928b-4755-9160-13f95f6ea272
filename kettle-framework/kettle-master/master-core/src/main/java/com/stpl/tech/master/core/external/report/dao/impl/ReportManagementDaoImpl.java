package com.stpl.tech.master.core.external.report.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.report.metadata.model.ReportSummary;
import com.stpl.tech.kettle.report.metadata.model.ReportVersionDetail;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.report.dao.ReportManagementDao;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.XMLReportDefinitionData;
import com.stpl.tech.master.data.model.XMLReportVersionData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Repository
public class ReportManagementDaoImpl extends AbstractMasterDaoImpl implements ReportManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(ReportManagementDaoImpl.class);

	@Override
	public List<ReportSummary> getAllReportVersionHistoryById(Integer reportId) {
		List<ReportSummary> resultList = new ArrayList<>();
		XMLReportDefinitionData reportDefinitionData = manager.find(XMLReportDefinitionData.class, reportId);
		List<XMLReportVersionData> versionDataList = getXMLReportVersionData(reportId);
		if (versionDataList != null && versionDataList.size() > 0) {
			for (XMLReportVersionData xmlReportVersionData : versionDataList) {
				resultList.add(MasterDataConverter.convert(reportDefinitionData, xmlReportVersionData));
			}
		}
		return resultList;
	}

    @Override
    public List<XMLReportDefinitionData> getAllReportCategories(String executionEnvironment) {
        Query query = manager.createQuery("FROM XMLReportDefinitionData X where X.executionEnvironment = :environment");
        query.setParameter("environment", executionEnvironment);
        List<XMLReportDefinitionData> list = query.getResultList();
        return list;
    }

    @Override
    public List<XMLReportDefinitionData> getAllReportCategoryList(String executionEnvironment) {
        Query query = manager.createQuery("FROM XMLReportDefinitionData X where X.executionEnvironment = :environment AND X.reportStatus = :reportStatus");
        query.setParameter("environment", executionEnvironment);
        query.setParameter("reportStatus", "ACTIVE");
        return query.getResultList();
    }

    @Override
    public List<XMLReportVersionData> getXMLReportVersionData(Integer reportId) {
        Query query = manager.createQuery("FROM XMLReportVersionData X where X.xmlReportDefinitionData.reportId = :reportId");
        query.setParameter("reportId", reportId);
        List<XMLReportVersionData> list = query.getResultList();
        return list;
    }

	@Override
	public int addNewReport(ReportSummary report) throws DataUpdationException {
        XMLReportDefinitionData xml = new XMLReportDefinitionData();
        xml.setDepartmentId(report.getDepartmentId());
        xml.setDepartmentName(report.getDepartmentName());
        xml.setExecutionEnvironment(report.getExecutionEnvironment());
        xml.setReportName(report.getReportName());
        xml.setReportStatus(AppConstants.ACTIVE);
        xml.setReportType(report.getReportType());
        xml.setLastUpdated(AppUtils.getCurrentTimestamp());
        xml = add(xml);
        return xml.getReportId();
	}

	@Override
	public int updateStatus(int reportId, String status) {
		XMLReportDefinitionData xml = manager.find(XMLReportDefinitionData.class, reportId);
		xml.setReportStatus(status);
		manager.flush();
		return xml.getReportId();
	}

    @Override
    public int addNewReportVersion(ReportVersionDetail report) {
        XMLReportVersionData xml = null;
        try {
            XMLReportDefinitionData def = manager.find(XMLReportDefinitionData.class, report.getReportId());
            List<XMLReportVersionData> versionDataList = getXMLReportVersionData(report.getReportId());
			int versionNo = versionDataList == null || versionDataList.size() == 0 ? 1 : versionDataList.size() + 1;
            xml = new XMLReportVersionData();
            xml.setXmlReportDefinitionData(def);
            xml.setS3Link(report.getS3Link());
            xml.setFileName(report.getFileName());
            xml.setMimeType(report.getMimeType());
            xml.setS3Bucket(report.getS3Bucket());
            xml.setS3Key(report.getS3Key());
            xml.setComment(report.getComments());
            xml.setLastUpdated(AppUtils.getCurrentTimestamp());
            xml.setVersionNo(versionNo);
            xml.setDefaultV(AppConstants.NO);
            manager.persist(xml);
            manager.flush();
        } catch (Exception e) {
            LOG.error("Error adding " + xml.getClass().getName() + " {}", e.getMessage(), e);
        }
        return xml.getVersionId();
    }

    @Override
    public int setDefaultReportVersion(int reportId, int versionId) {
        XMLReportVersionData defaultVersion = manager.find(XMLReportVersionData.class, versionId);
        List<XMLReportVersionData> versionDataList = getXMLReportVersionData(reportId);
        XMLReportDefinitionData def = manager.find(XMLReportDefinitionData.class, reportId);
        def.setVersion(defaultVersion.getVersionNo());
        def.setXmlFilePath(defaultVersion.getS3Link());
        def.setLastUpdated(AppUtils.getCurrentTimestamp());
        def = update(def);

        for (XMLReportVersionData version: versionDataList) {
            version.setDefaultV(AppConstants.NO);
            version.setLastUpdated(AppUtils.getCurrentTimestamp());
            version.setXmlReportDefinitionData(def);
            version = update(version);
            LOG.info("version ", version.getVersionNo());
        }
        defaultVersion = manager.find(XMLReportVersionData.class, versionId);
        defaultVersion.setDefaultV(AppConstants.YES);
        defaultVersion.setLastUpdated(AppUtils.getCurrentTimestamp());
        defaultVersion.setXmlReportDefinitionData(def);
        defaultVersion = update(defaultVersion);
        return defaultVersion.getVersionId();
    }

    @Override
    public XMLReportVersionData getActiveReportVersion(Integer reportId, Integer versionNo) {
        Query query = manager.createQuery("FROM XMLReportVersionData X where" +
                " X.xmlReportDefinitionData.reportId = :reportId AND X.versionNo = :versionNo");
        query.setParameter("reportId", reportId);
        query.setParameter("versionNo", versionNo);
        try {
            return (XMLReportVersionData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }


}
