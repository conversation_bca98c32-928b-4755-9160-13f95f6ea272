package com.stpl.tech.master.core.external.notification.service.impl;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.master.core.external.notification.dao.TemperatureLoggerDao;
import com.stpl.tech.master.core.external.notification.service.TemperatureLoggerService;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.TemperatureLogDetail;

/**
 * Created by Chaayos on 21-09-2016.
 */
@Service
public class TemperatureLoggerServiceImpl extends AbstractMasterDaoImpl implements TemperatureLoggerService {

	private static final Logger LOG = LoggerFactory.getLogger(TemperatureLoggerServiceImpl.class);
	@Autowired
	private TemperatureLoggerDao dao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public TemperatureLogDetail addLog(TemperatureLogDetail log) throws IOException {
		LOG.info("Logging Temperature : {} ", log);
		return dao.add(log);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<TemperatureLogDetail> getTemperatureDetail(String locationId, String deviceName, Date logTime) {
		LOG.info("Getting  list with device name and locationId based on time ");
		return dao.getTemperatureDetail(locationId, deviceName, logTime);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markAsNotified(Integer id, boolean notified) {
		LOG.info("updating  notification ");
		dao.markAsNotified(id, notified);
	}

	@Override
	public List<TemperatureLogDetail> findAll(Date startTime, Date endTime) {
		return dao.findAll(startTime,endTime);
	}

}