package com.stpl.tech.master.core.external.notification;

import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.util.EnvType;

public interface ExternalNotificationService {
	
	public void sendNotification(EnvType env, String user, SlackNotification channel, String text);

	public void sendNotification(EnvType env, String user, SlackNotification channel, Notification notification);
}
