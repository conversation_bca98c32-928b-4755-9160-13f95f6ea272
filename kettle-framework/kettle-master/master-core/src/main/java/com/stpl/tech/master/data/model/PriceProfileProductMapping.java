package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "PRICE_PROFILE_PRODUCT_MAPPING")
public class PriceProfileProductMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PRICE_PROFILE_PRODUCT_MAPPING_ID")
    private Integer id;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PRICE_PROFILE_ID", referencedColumnName = "PRICE_PROFILE_DATA_ID")
    private PriceProfileData priceProfileData;



    @Column(name = "VERSION", nullable = false)
    private Integer version;

    @Column(name = "PRODUCT_ID", nullable = false)
    private Integer productId;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DIMENSION_CODE", nullable = false)
    private RefLookup dimensionCode;

    @Column(name = "PRICE", precision = 16, scale = 6, nullable = false)
    private BigDecimal price;

    @Column(name = "STATUS", length = 45)
    private String status;

    @Column(name = "CREATED_BY", nullable = false)
    private Integer createdBy;

    @Column(name = "CREATION_TIME", nullable = false)
    private Date creationTime;

    @Column(name = "LAST_UPDATED_BY")
    private Integer lastUpdatedBy;

    @Column(name = "LAST_UPDATION_TIME")
    private Date lastUpdationTime;
}
