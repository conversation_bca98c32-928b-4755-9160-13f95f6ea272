package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "LCD_MENU_STEPS")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LCDMenuStep {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "STEP_NAME", unique = true, nullable = false)
    private String stepName;
    
    @Column(name = "STEP_ORDER", nullable = false)
    private Integer stepOrder;
    
    @Column(name = "STATUS")
    private String status;
    
    @Column(name = "CREATED_AT")
    private Date createdAt;
    
    @Column(name = "UPDATED_AT")
    private Date updatedAt;
} 