package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.data.model.ProductTagMappingDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductTagMappingDetailDao extends JpaRepository<ProductTagMappingDetail,Integer> {

    List<ProductTagMappingDetail> findByProductIdAndMappingStatus(Integer productId, String mappingStatus);
}
