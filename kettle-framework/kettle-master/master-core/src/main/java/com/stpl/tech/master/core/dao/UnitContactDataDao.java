package com.stpl.tech.master.core.dao;

import com.stpl.tech.master.data.model.UnitContactDataEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Spring Data JPA Repository for unit contact data operations
 */
@Repository
public interface UnitContactDataDao extends JpaRepository<UnitContactDataEntity, Integer> {

    /**
     * Retrieves all contacts for a specific unit ordered by creation date (most recent first)
     *
     * @param unitId the unit ID
     * @return list of contact entities
     */
    List<UnitContactDataEntity> findByUnitIdOrderByCreatedOnDesc(Integer unitId);

    /**
     * Retrieves only active contacts for a specific unit
     *
     * @param unitId the unit ID
     * @param status the status to filter by (e.g., "ACTIVE")
     * @return list of active contact entities
     */
    List<UnitContactDataEntity> findByUnitIdAndStatusOrderByCreatedOnDesc(Integer unitId, String status);

    /**
     * Soft deletes contacts by setting status to INACTIVE
     *
     * @param contactIds list of contact IDs to deactivate
     * @param status the new status (e.g., "INACTIVE")
     * @param updatedBy user performing the operation
     * @param updatedOn timestamp of the update
     * @return number of contacts updated
     */
    @Modifying
    @Query("UPDATE UnitContactDataEntity u SET u.status = :status, u.updatedBy = :updatedBy, u.updatedOn = :updatedOn WHERE u.id IN :contactIds")
    int updateStatusByIds(@Param("contactIds") List<Integer> contactIds,
                         @Param("status") String status,
                         @Param("updatedBy") Integer updatedBy,
                         @Param("updatedOn") Date updatedOn);

    /**
     * Checks if a contact number already exists for a unit (excluding specific contact ID)
     *
     * @param unitId the unit ID
     * @param contactNumber the contact number to check
     * @param excludeContactId contact ID to exclude from check (for updates)
     * @return true if contact number exists, false otherwise
     */
    @Query("SELECT COUNT(u) > 0 FROM UnitContactDataEntity u WHERE u.unitId = :unitId AND u.contactNumber = :contactNumber AND (:excludeContactId IS NULL OR u.id != :excludeContactId)")
    boolean existsByUnitIdAndContactNumberExcludingId(@Param("unitId") Integer unitId,
                                                     @Param("contactNumber") Long contactNumber,
                                                     @Param("excludeContactId") Integer excludeContactId);
}
