package com.stpl.tech.master.core.dao;

import com.stpl.tech.master.data.model.UnitContactDataEntity;

import java.util.List;

/**
 * DAO interface for unit contact data operations
 */
public interface UnitContactDataDao {

    /**
     * Retrieves all contacts for a specific unit (both active and inactive)
     * 
     * @param unitId the unit ID
     * @return list of contact entities
     */
    List<UnitContactDataEntity> getContactsByUnitId(Integer unitId);

    /**
     * Retrieves only active contacts for a specific unit
     * 
     * @param unitId the unit ID
     * @return list of active contact entities
     */
    List<UnitContactDataEntity> getActiveContactsByUnitId(Integer unitId);

    /**
     * Saves or updates a list of contacts in bulk
     * 
     * @param contacts list of contact entities to save/update
     * @return true if operation was successful, false otherwise
     */
    boolean saveOrUpdateContacts(List<UnitContactDataEntity> contacts);

    /**
     * Saves or updates a single contact
     * 
     * @param contact the contact entity to save/update
     * @return the saved contact entity with generated ID if new
     */
    UnitContactDataEntity saveOrUpdateContact(UnitContactDataEntity contact);

    /**
     * Soft deletes contacts by setting status to INACTIVE
     * 
     * @param contactIds list of contact IDs to deactivate
     * @param updatedBy user performing the operation
     * @return number of contacts updated
     */
    int deactivateContacts(List<Integer> contactIds, String updatedBy);

    /**
     * Gets contact by ID
     * 
     * @param contactId the contact ID
     * @return contact entity or null if not found
     */
    UnitContactDataEntity getContactById(Integer contactId);

    /**
     * Checks if a contact number already exists for a unit (excluding specific contact ID)
     * 
     * @param unitId the unit ID
     * @param contactNumber the contact number to check
     * @param excludeContactId contact ID to exclude from check (for updates)
     * @return true if contact number exists, false otherwise
     */
    boolean isContactNumberExists(Integer unitId, Long contactNumber, Integer excludeContactId);
}
