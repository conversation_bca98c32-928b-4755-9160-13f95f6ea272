package com.stpl.tech.master.core.external.price.service.impl;

import com.stpl.tech.master.core.external.price.dao.ProductPriceDao;
import com.stpl.tech.master.core.external.price.service.ProductPriceService;
import com.stpl.tech.master.data.model.UnitProductPricingBulkUpdateEvent;
import com.stpl.tech.master.domain.model.UnitProductPriceBulkRequest;
import com.stpl.tech.master.domain.model.UnitProductPriceSheetDetail;
import com.stpl.tech.master.domain.model.UnitProductPricingDetail;
import com.stpl.tech.master.notification.ProductProfileBulkDataChange;
import com.stpl.tech.master.notification.ProductProfileDataBulkUpdateTemplate;
import com.stpl.tech.spring.exception.FileArchiveServiceException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class ProductPriceServiceImpl implements ProductPriceService {

    private static final Logger LOG = LoggerFactory.getLogger(ProductPriceServiceImpl.class);

    @Autowired
    private ProductPriceDao dao;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getUnitProductPriceSheet(UnitProductPriceBulkRequest request) {
        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest httpServletRequest, HttpServletResponse response) throws Exception {
                String fileName = "UnitProductPriceSheet.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitProductPriceSheetDetail> detailList = dao.getUnitProductPriceDetail(
                        request.getUnitCategory(), request.getBrandId(),
                        Arrays.asList(request.getRegions().split(",")), request.getPricingProfile(), request.getProductIds());
                if (Objects.nonNull(detailList) && !detailList.isEmpty()) {
                    writer.writeSheet(detailList, UnitProductPriceSheetDetail.class);
                }
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<BigDecimal, List<UnitProductPricingDetail>> parseAndCreateBulkUnitProductPriceUpdateEvent(
            MultipartFile file, Integer updatedBy, UnitProductPricingBulkUpdateEvent updateEvent) throws IOException {
        Workbook workbook;
        if (file.getName().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();

        int sheetCount = workbook.getNumberOfSheets();
        List<UnitProductPriceSheetDetail> entityList = new ArrayList<>();
        for (int i = 0; i < sheetCount; i++) {
            LOG.info("Parsing Bulk Product Price Sheet {}", i + 1);
            List<UnitProductPriceSheetDetail> sheetDetailList = parser.createEntity(workbook.getSheetAt(i),
                    UnitProductPriceSheetDetail.class, errors::add);
            entityList.addAll(sheetDetailList);
        }
        if (errors.isEmpty()) {
            List<Integer> productPriceIdList = new ArrayList<>();
            Map<Integer, BigDecimal> productPriceMap = new HashMap<>();
            Map<BigDecimal, List<UnitProductPricingDetail>> priceToUnitProductMap = new HashMap<>();
            BigDecimal bigDecimalZero = BigDecimal.ZERO;
            int totalRecordCount = 0;
            int errorRecordCount = 0;
            int changedRecordCount = 0;
            for (UnitProductPriceSheetDetail productPriceDetail : entityList) {
                if (Objects.nonNull(productPriceDetail) && Objects.nonNull(productPriceDetail.getNewPrice())
                        && productPriceDetail.getNewPrice().compareTo(bigDecimalZero) >= 1
                        && Objects.nonNull(productPriceDetail.getUnitProductPriceId())) {
                    productPriceIdList.add(productPriceDetail.getUnitProductPriceId());
                    productPriceMap.put(productPriceDetail.getUnitProductPriceId(), productPriceDetail.getNewPrice());
                    totalRecordCount += 1;
                } else if (Objects.nonNull(productPriceDetail)) {
                    totalRecordCount += 1;
                    errorRecordCount += 1;
                }
            }
            if (!productPriceIdList.isEmpty()) {
                List<UnitProductPricingDetail> oldPricingList = dao.getUnitProductPriceByMappingIds(productPriceIdList);
                errorRecordCount += productPriceIdList.size() - oldPricingList.size();
                for (UnitProductPricingDetail oldPricing : oldPricingList) {
                    BigDecimal newPrice = (productPriceMap.get(oldPricing.getUnitProdPriceId())).setScale(2,
                            BigDecimal.ROUND_HALF_EVEN);
                    BigDecimal difference = (oldPricing.getPrice().subtract(newPrice)).abs();
                    if (difference.compareTo(bigDecimalZero) >= 1 && difference.compareTo(
                            (oldPricing.getPrice().multiply(AppConstants.UNIT_PRODUCT_PRICE_THRESHOLD))) <= -1) {
                        if (priceToUnitProductMap.containsKey(newPrice)) {
                            List<UnitProductPricingDetail> list = priceToUnitProductMap.get(newPrice);
                            list.add(oldPricing);
                        } else {
                            List<UnitProductPricingDetail> list = new ArrayList<>();
                            list.add(oldPricing);
                            priceToUnitProductMap.put(newPrice, list);
                        }
                        changedRecordCount += 1;
                    } else {
                        errorRecordCount += 1;
                    }
                }
            }
            updateEvent.setUpdatedBy(updatedBy);
            updateEvent.setUpdateTime(AppUtils.getCurrentTimestamp());
            updateEvent.setUnitCategory(entityList.get(0).getUnitCategory());
            updateEvent.setBrandId(entityList.get(0).getBrandId());
            updateEvent.setTotalRecords(totalRecordCount);
            updateEvent.setTotalErrorRecords(errorRecordCount);
            updateEvent.setTotalRecordsChanged(changedRecordCount);
            dao.add(updateEvent);
            workbook.close();
            return priceToUnitProductMap;
        } else {
            LOG.info("Error Parsing Workbook for Unit Product Bulk Update, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void bulkUpdateUnitProductPrice(BigDecimal price, List<UnitProductPricingDetail> productPricingList,
                                           Integer updatedBy, UnitProductPricingBulkUpdateEvent updateEvent) {
        List<Integer> productPriceIds = new ArrayList<>();
        for (UnitProductPricingDetail pricing : productPricingList) {
            productPriceIds.add(pricing.getUnitProdPriceId());
        }
        dao.bulkUpdateUnitProductPrice(price, productPriceIds, updatedBy);
        dao.addBulkUpdateUnitProductPriceLog(price, productPricingList, updateEvent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateBulkUpdateUnitProductPriceEvent(UnitProductPricingBulkUpdateEvent updateEvent) {
        dao.update(updateEvent);
    }

    @Override
    public String saveBulkProductPriceSheet(String s3ProductBucket, MultipartFile file) {
        try {
            String baseDir = "bulk_product_price_sheets";
            String fileName = file.getOriginalFilename();
            String fileExtension = fileName.substring(fileName.lastIndexOf('.'));
            fileName = fileName.substring(0, fileName.lastIndexOf('.')) + "_" + Instant.now().getEpochSecond() + fileExtension;
            fileName = fileName.replaceAll(" ", "_").toLowerCase();
            FileDetail s3File = fileArchiveService.saveFileToS3(s3ProductBucket, baseDir, fileName, file,
                    true);
            if (Objects.nonNull(s3File)) {
                LOG.info("Saved Bulk Product Price sheet by name {}", fileName);
                return s3File.getName();
            }
        } catch (FileArchiveServiceException e) {
            LOG.error("Encountered error while uploading Bulk Product Price sheet to S3", e);
        }
        return null;
    }

    public void sendProductProfileBulkUpdateMail(UnitProductPricingBulkUpdateEvent updateEvent, String basePath, EnvType envType) {
        List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>"));
        try {
            if (Objects.nonNull(updateEvent)) {
                ProductProfileDataBulkUpdateTemplate template = new ProductProfileDataBulkUpdateTemplate(basePath, AppUtils.getCurrentTimeISTStringWithNoColons(), updateEvent);
                ProductProfileBulkDataChange productProfilebulkDataChange = new ProductProfileBulkDataChange(envType, toEmails, template);
                productProfilebulkDataChange.sendEmail();
            }
        } catch (Exception e) {
            LOG.info("Error while Sending Email For bulk product price Updation :::::");
            LOG.info("EXCEPTION : {}", e);
        }
    }
}
