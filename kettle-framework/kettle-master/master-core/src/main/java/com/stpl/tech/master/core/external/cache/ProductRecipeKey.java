package com.stpl.tech.master.core.external.cache;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.io.Serializable;

public class ProductRecipeKey implements Serializable{

	
	/**
	 * 
	 */
	private static final long serialVersionUID = 6205916282577327518L;
	
	private int productId;
	private String dimension;
	private String profile;
	
	public ProductRecipeKey(){
		
	}

	public ProductRecipeKey(int productId, String dimension, String profile) {
		super();
		this.productId = productId;
		this.dimension = dimension;
		this.profile =  profile;
	}

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		ProductRecipeKey that = (ProductRecipeKey) o;

		return new EqualsBuilder()
				.append(productId, that.productId)
				.append(dimension, that.dimension)
				.append(profile, that.profile)
				.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
				.append(productId)
				.append(dimension)
				.append(profile)
				.toHashCode();
	}

	@Override
	public String toString() {
		return "ProductRecipeKey{" +
			"productId=" + productId +
			", dimension='" + dimension + '\'' +
			", profile='" + profile + '\'' +
			'}';
	}
}
