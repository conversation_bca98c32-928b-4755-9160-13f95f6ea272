package com.stpl.tech.master.notification;

import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AbstractTemplate;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BCXOrderDetailTemplate extends AbstractVelocityTemplate {

    private EnvType envType;
    private Map<String,Object> request;
    private String sourceOrderId;
    private String basePath;
    private String type;
    public  BCXOrderDetailTemplate(Map<String,Object> request,String sourceOrderId,String type,EnvType envType,String basePath){
        this.request = request;
        this.sourceOrderId = sourceOrderId;
        this.envType = envType;
        this.basePath= basePath;
        this.type = type;
    }

    @Override
    public String getTemplatePath() {
        return "template/OrderDetailEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + sourceOrderId + request.get("UNIT_NAME")+".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String,Object> data = new HashMap<>();
        String emailSartingString = "Hi " + request.get("UNIT_NAME") + "\n" +
                "We have received a ORS from "+request.get("PARTNER_CODE")+" for "+request.get("BRAND_NAME") +"\n" +
                "Below are the details"+ "\n";
        String emailEndingString = "Please Look Into This asap \n Thanks \n BCX Management Team \n";
        request.put("PartnerSourceId",sourceOrderId);
        request.put("Heading",emailSartingString);
        request.put("Ending",emailEndingString);
        return request;
    }
}
