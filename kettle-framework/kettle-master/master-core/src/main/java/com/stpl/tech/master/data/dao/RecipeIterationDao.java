package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.recipe.calculator.model.RecipeIterationDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RecipeIterationDao extends MongoRepository<RecipeIterationDetail, String> {
	
	@Query("{'linkedProductId' : {'$eq' : ?0},'status' : {'$eq' : ?1}}")
	public List<RecipeIterationDetail> getApprovedIterationForProduct(int productId,String status);

	@Query("{'linkedProductId' : {'$in' : ?0},'status' : {'$eq' : ?1}}")
	public List<RecipeIterationDetail> getAllApprovedIterationForProducts(List<Integer> productIds,String status);
	
	@Query("{'linkedProductId' : {'$eq' : ?0},'status' : {'$ne' : ?1}}")
	public List<RecipeIterationDetail> getIterationForProduct(int productId,String status);

	@Query("{'linkedConstructName' : {'$eq' : ?0},'status' : {'$ne' : ?1}}")
	public List<RecipeIterationDetail> getIterationForConstruct(String constructName, String status);

	@Query("{'linkedConstructName' : {'$eq' : ?0}}")
	public List<RecipeIterationDetail> getAllConstruct(String constructName);
	
	@Query("{'iterationName' : {'$eq' : ?0}}")
	public List<RecipeIterationDetail> getAllIteration(String iterationName);

	@Query("{'linkedConstructName' : {'$eq' : ?0},'status' : {'$eq' : ?1}}")
	public List<RecipeIterationDetail> getArchivedIterationForConstruct(String constructName, String status);
	
	@Query("{'linkedProductId' : {'$eq' : ?0},'status' : {'$eq' : ?1}}")
	public List<RecipeIterationDetail> getArchivedIterationForProduct(int productId, String status);
	
	@Query("{'linkedConstructName' : {'$ne' : ?0}}")
	public List<RecipeIterationDetail> getAllSCMConstruct(String linkedConstrstatusuctName);

	@Query("{'linkedProductId' : {'$eq' : ?0}}")
	public List<RecipeIterationDetail> getSCMIterationForProduct(int productId);
}