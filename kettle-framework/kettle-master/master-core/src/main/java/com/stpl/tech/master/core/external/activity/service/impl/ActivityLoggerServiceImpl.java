package com.stpl.tech.master.core.external.activity.service.impl;

import com.stpl.tech.kettle.report.metadata.model.comparator.HasId;
import com.stpl.tech.kettle.report.metadata.model.comparator.HasStatus;
import com.stpl.tech.master.core.ActivityType;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.external.activity.dao.ActivityLoggerDao;
import com.stpl.tech.master.core.external.activity.service.ActivityLoggerService;
import com.stpl.tech.master.data.model.MetadataActivityLogger;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.notification.ChangeObjectDiff;
import com.stpl.tech.master.notification.ChangesDiffNotificationTemplate;
import com.stpl.tech.master.notification.UnitDetailChange;
import com.stpl.tech.master.notification.UnitDetailChangeNotificationTemplate;
import com.stpl.tech.master.notification.ZomatoBusinessHourChangeEmailTemplate;
import com.stpl.tech.master.notification.ZomatoBussinessHourChange;
import com.stpl.tech.master.recipe.calculator.model.IterationIngredientDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import de.danielbechler.diff.ObjectDifferBuilder;
import de.danielbechler.diff.node.DiffNode;
import de.danielbechler.diff.node.Visit;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * - Created by malimo on 01-08-2018
 */
@Service
public class ActivityLoggerServiceImpl implements ActivityLoggerService {

	private static final Logger LOG = LoggerFactory.getLogger(ActivityLoggerServiceImpl.class);
	@Autowired
	private ActivityLoggerDao loggerDao;

	@Autowired
	private MasterProperties props;

    @Override
	public Boolean sendDiffEmail(Object oldObject , Object newObject , String updatedBy , String className , Integer id ,
								 List<String> toEmails, String placeHolder) throws IllegalAccessException, EmailGenerationException {
		try {
			Map<String,Pair<Object,Object>> diffs = findDiff(oldObject,newObject);
			ChangesDiffNotificationTemplate template = new ChangesDiffNotificationTemplate(diffs,updatedBy,
					className, id,props.getBasePath());
			ChangeObjectDiff changeObjectDiff = new ChangeObjectDiff(className,props.getEnvironmentType(),
					updatedBy,toEmails,template ,placeHolder );
			changeObjectDiff.sendEmail();
		}catch (Exception e){
			LOG.info("Error While Finding Diff And Sending Diff Email For {} with ID : {}",className,id,e);
			return false;
		}
		return true;

	}

	@Override
	public Boolean sendUnitChangeMail(Unit oldObject, Unit newObject, String updatedBy, String className, Integer id,
									  List<String> toEmails, String placeHolder) throws IllegalAccessException, EmailGenerationException {
		try {
			Map<String,Pair<Object,Object>> diffs = findDiff(oldObject,newObject);
			UnitDetailChangeNotificationTemplate template1 = new UnitDetailChangeNotificationTemplate(diffs,updatedBy,
					className, id,props.getBasePath(),newObject);
			UnitDetailChange unitDetailChange = new UnitDetailChange(className,props.getEnvironmentType(),
					updatedBy,toEmails,template1 ,placeHolder,newObject );
			unitDetailChange.sendEmail();
		}catch (Exception e){
			LOG.info("Error While Finding Diff And Sending Diff Email For {} with ID : {}",className,id,e);
			return false;
		}
		return true;

	}

	private  Pair<String,String> findArrayDiff(Object[] oldArray , Object[] newArray) throws IllegalAccessException {
		String arrayDiffOld = "[<br>";
		String arrayDiffNew = "[<br>";
		Integer oldSize = oldArray.length;
		Integer newSize = newArray.length;
		Integer i =0;
		while(i < newSize && i < oldSize){
			Object oldEle = oldArray[i];
			Object newEle = newArray[i];
			if(!traverseDepthFirst(oldEle,"").equals(traverseDepthFirst(newEle,""))){
				Pair<String,String> nestedDiffs = getNestedDiff(oldEle,newEle);
				String oldMsg = "{<br>" +nestedDiffs.getKey() + "}" + ",<br>";
				String newMsg = "{<br>" + nestedDiffs.getValue() + "}" +",<br>";
				if(oldEle.getClass().equals(IterationIngredientDetail.class)){
					oldMsg ="<b>" + ((IterationIngredientDetail) oldEle).getProductName() + "</b> : " + oldMsg;
					newMsg = "<b>" + ((IterationIngredientDetail) newEle).getProductName() + "</b> : " + newMsg;
				}
				arrayDiffOld = arrayDiffOld.concat(oldMsg);
				arrayDiffNew = arrayDiffNew.concat(newMsg);
			}
			i++;
		}
		while(i < oldSize){
			String tempMsg = "";
			tempMsg = traverseDepthFirst(oldArray[i],tempMsg);
			arrayDiffOld = arrayDiffOld.concat("{<br>" +tempMsg + "}" +",<br>");
			i++;
		}
		while (i<newSize){
			String tempMsg = "";
			tempMsg = traverseDepthFirst(newArray[i],tempMsg);
			arrayDiffNew= arrayDiffNew.concat("{<br>" + tempMsg + "}" + ",<br>");
			i++;
		}
		arrayDiffOld =arrayDiffOld.concat("<br>]");
		arrayDiffNew =arrayDiffNew.concat("<br>]");
		return new Pair<>(arrayDiffOld.trim(),arrayDiffNew.trim());
	}



	@Override
	public Map<String,Pair<Object,Object>>  findDiff(Object oldObject , Object newObject) throws IllegalAccessException {
		//Map Key Is Field Name , Pair Key is Old Field Value , Pair Value is New Field Value
		Map<String,Pair<Object,Object>> objectDiffs = new HashMap<>();
		try{
			LOG.info("####Trying To Find Diffs On Object Of Class : {}",oldObject.getClass());
			if(!oldObject.getClass().equals(newObject.getClass())){
				LOG.info("Couldn't Find DIff Old And New Object Is not Of same Type");
				return objectDiffs;
			}
			Field[] fields = oldObject.getClass().getDeclaredFields();
			for(Field field : fields){
				try {
					if (field.getName().equals("serialVersionUID")) {
						continue;
					}
					field.setAccessible(true);
					if (field.getType().isArray() || field.getType().isAssignableFrom(Collection.class) ||
							field.getType().equals(List.class)) {
						LOG.info("######Field Is OF Array Type");
						if(Objects.isNull(field.get(oldObject))){
							if(Objects.isNull(field.get(newObject))){
								continue;
							}
							LOG.info("Old Array Is Null");
							field.set(oldObject,new ArrayList<Object>());
						}
						Object[] oldArray = ((Collection<Object>) field.get(oldObject)).toArray();
						Object[] newArray = ((Collection<Object>) field.get(newObject)).toArray();

						if (!Arrays.equals(oldArray, newArray)) {
							LOG.info("Field : {} is changed For Class : {}", field.getName(), oldObject.getClass());
							Pair<String, String> arrayDiffs = findArrayDiff(oldArray, newArray);
							objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(arrayDiffs.getKey().trim(),
									arrayDiffs.getValue().trim()));
						} else {
							LOG.info("#####Both Arrays Are Same");
							continue;
						}
					} else if (Objects.isNull(field.get(oldObject))) {
						if (Objects.nonNull(field.get(newObject))) {
							String msg = "";
							LOG.info("Field : {} is changed For Class : {}", field.getName(), oldObject.getClass());
							if (field.get(newObject).getClass().getPackage().getName().startsWith("com.stpl.tech") &&
									!field.get(newObject).getClass().isEnum()) {
								String oldObjMsg = "";
								String newObjMsg = "";
								oldObjMsg = traverseDepthFirst(field.get(oldObject), oldObjMsg);
								newObjMsg = traverseDepthFirst(field.get(newObject), newObjMsg);
								objectDiffs.put(splitCamelCase(field.getName()), new Pair<>("", newObjMsg));
							} else {
								if (field.getType().equals(Date.class)) {
									String oldDate = "";
									String newDate = "";
									if (Objects.nonNull(field.get(newObject))) {
										newDate = AppUtils.dateInddthMMMFormat((Date) field.get(newObject));
									}
									if (!oldDate.equals(newDate)) {
										objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(oldDate, newDate));
									}
								} else {
									objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(Objects.nonNull(field.get(oldObject)) ? field.get(oldObject).toString() : "",
											Objects.nonNull(field.get(newObject)) ? field.get(newObject).toString() : ""));
								}
							}
						} else {
							continue;
						}

					} else if (!field.get(oldObject).equals(field.get(newObject))) {
						LOG.info("Field : {} is changed For Class : {}", field.getName(), oldObject.getClass());
						String msg = "";
						if (field.get(oldObject).getClass().getPackage().getName().startsWith("com.stpl.tech") &&
								!field.get(oldObject).getClass().isEnum()) {
							String oldObjectMsg = "";
							String newObjMsg = "";
							oldObjectMsg = traverseDepthFirst(field.get(oldObject), oldObjectMsg);
							newObjMsg = traverseDepthFirst(field.get(newObject), newObjMsg);
							if (Objects.isNull(field.get(newObject))) {
								objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(oldObjectMsg, ""));
							} else if (!oldObjectMsg.equals(newObjMsg)) {
								Pair<String, String> nestedDiffs = getNestedDiff(field.get(oldObject), field.get(newObject));
								objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(nestedDiffs.getKey(), nestedDiffs.getValue()));
							}
						} else {
							if (field.getType().equals(Date.class)) {
								String oldDate = AppUtils.dateInddthMMMFormat((Date) field.get(oldObject));
								String newDate = "";
								if (Objects.nonNull(field.get(newObject))) {
									newDate = AppUtils.dateInddthMMMFormat((Date) field.get(newObject));
								}
								if (!oldDate.equals(newDate)) {
									objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(oldDate, newDate));
								}
							} else {
								objectDiffs.put(splitCamelCase(field.getName()), new Pair<>(Objects.nonNull(field.get(oldObject)) ? field.get(oldObject).toString() : "",
										Objects.nonNull(field.get(newObject)) ? field.get(newObject).toString() : ""));
							}
						}

					}
				}catch (Exception e){
					LOG.info("Error While Finding Diff For Field : {}",field.getName(), e);
					objectDiffs.put(splitCamelCase(field.getName()),new Pair<>("Error While Finding Diff",
							"Error While Finding Diff"));
				}
			}
		}catch (Exception e){
			LOG.info("Error While Finding Diffs On Objects" , e);
		}

		return objectDiffs;
	}

	private Pair<String , String> getNestedDiff(Object oldObj , Object newObj) throws IllegalAccessException {
		Map<String,Pair<Object,Object>> nestedDiffs = findDiff(oldObj,newObj);
		String oldObjectDiff = "";
		String newObjectDiff = "";
		for(String field :  nestedDiffs.keySet()){
			oldObjectDiff =  oldObjectDiff.concat( splitCamelCase(field) + " : " + nestedDiffs.get(field).getKey() + "<br>");
			newObjectDiff =  newObjectDiff.concat( splitCamelCase(field) + " : " + nestedDiffs.get(field).getValue() + "<br>");
		}
		return new Pair<>(oldObjectDiff,newObjectDiff);

	}

	private String traverseDepthFirst(Object obj ,String msg) throws IllegalAccessException {
		if (obj == null) {
			return msg;
		}
		for (Field field : obj.getClass().getDeclaredFields()) {
			field.setAccessible(true);
			if(field.getName().equals("serialVersionUID")){
				continue;
			}
			String tempMsg = "";
			if(Objects.nonNull(field.get(obj))  && field.get(obj).getClass().getPackage().getName().startsWith("com.stpl.tech") &&
					!field.get(obj).getClass().isEnum()){
				tempMsg =  splitCamelCase(field.getName()) + ": " + traverseDepthFirst(field.get(obj),"") + "<br>";
			}else{
				tempMsg = splitCamelCase(field.getName()) + ": " + field.get(obj) + "<br>";
			}
			LOG.info("nested Value : {}",tempMsg);
			msg = msg.concat(tempMsg);



		}
		return msg;
	}


	public String splitCamelCase(String s) {
		s =  s.replaceAll(
				String.format("%s|%s|%s",
						"(?<=[A-Z])(?=[A-Z][a-z])",
						"(?<=[^A-Z])(?=[A-Z])",
						"(?<=[A-Za-z])(?=[^A-Za-z])"
				),
				" "
		);
		return s.substring(0, 1).toUpperCase() + s.substring(1);
	}




	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public void addActivity(Object oldObject, Object newObject, int updatedById) {
		MetadataActivityLogger log = get(oldObject, newObject, updatedById);
		if (log != null) {
			LOG.info(String.format("Activity : %s[%s], Updated By : %d, Type : %s, \nChange : %s", log.getKeyType(),
					log.getKeyId(), log.getActivityUserId(), log.getActivityType(), log.getActivityLog()));
			loggerDao.add(log);
		}
	}

	private MetadataActivityLogger get(Object oldObject, Object newObject, int updatedById) {
		MetadataActivityLogger log = null;
		try {
			ActivityType type = null;
			String id = null;
			Class<?> className = null;
			ActivityType activationStatusChanged = null;
			boolean calculateDiff = false;
			if (oldObject == null && newObject == null) {
				return null;
			}
			if (oldObject == null && newObject != null && newObject instanceof HasId) {
				id = ((HasId) newObject).objectId().toString();
				className = newObject.getClass();
				type = ActivityType.ADD;
			} else if (oldObject != null && newObject == null && oldObject instanceof HasId) {
				className = oldObject.getClass();
				id = ((HasId) oldObject).objectId().toString();
				type = ActivityType.DELETE;
			} else if (oldObject != null && newObject != null && oldObject instanceof HasId
					&& newObject instanceof HasId) {
				className = newObject.getClass();
				id = ((HasId) newObject).objectId().toString();
				type = ActivityType.UPDATE;
				calculateDiff = true;
			} else {
				return null;
			}
			if ((oldObject != null && oldObject instanceof HasStatus)
					|| (newObject != null && newObject instanceof HasStatus)) {
				String oldStatus = oldObject == null ? null : ((HasStatus) oldObject).currentStatus();
				String newStatus = newObject == null ? null : ((HasStatus) newObject).currentStatus();
				if ((oldStatus != null && (oldStatus.equalsIgnoreCase(AppConstants.ACTIVE)
						|| oldStatus.equalsIgnoreCase(AppConstants.IN_ACTIVE)))
						|| (newStatus != null && (newStatus.equalsIgnoreCase(AppConstants.ACTIVE)
								|| newStatus.equalsIgnoreCase(AppConstants.IN_ACTIVE)))) {
					if ((oldStatus == null && newStatus.equalsIgnoreCase(AppConstants.ACTIVE))
							|| (oldStatus != null && newStatus != null && !oldStatus.equalsIgnoreCase(oldStatus)
									&& newStatus.equalsIgnoreCase(AppConstants.ACTIVE))) {
						activationStatusChanged = ActivityType.ACTIVATE;
					} else if ((oldStatus == null && newStatus.equalsIgnoreCase(AppConstants.IN_ACTIVE))
							|| (oldStatus != null && newStatus != null && !oldStatus.equalsIgnoreCase(oldStatus)
									&& newStatus.equalsIgnoreCase(AppConstants.IN_ACTIVE))) {
						activationStatusChanged = ActivityType.DEACTIVATE;
					}

				}
				if (activationStatusChanged == null && ((oldStatus == null && newStatus != null)
						|| (oldStatus != null && newStatus == null)
						|| (oldStatus != null && newStatus != null && !oldStatus.equalsIgnoreCase(newStatus)))) {
					activationStatusChanged = ActivityType.STATUS_CHANGED;
				}

			}
			final String finalClassName = className.getSimpleName();
			List<String> changeSet = new ArrayList<>();
			if (calculateDiff) {
				DiffNode diff = ObjectDifferBuilder.buildDefault().compare(oldObject, newObject);
				diff.visit(new DiffNode.Visitor() {
					public void node(DiffNode node, Visit visit) {
						if (node.hasChildren()) {
							return;
						}
						final Object baseValue = node.canonicalGet(oldObject);
						final Object workingValue = node.canonicalGet(newObject);
						changeSet.add(finalClassName + ":"
								+ node.getPath().toString().replaceFirst("/", "").replaceAll("/", "->") + " ### '"
								+ baseValue + "' ===> '" + workingValue + "'");
					}
				});
			} else if (ActivityType.ADD.equals(type) || ActivityType.DELETE.equals(type)) {
				changeSet.add(finalClassName + ": Action -> '" + type.name() + "'");
			}

			log = new MetadataActivityLogger();
			// log.setActivityLog();
			log.setActivityTime(AppUtils.getCurrentTimestamp());
			log.setActivityType(
					type.name() + (activationStatusChanged == null ? "" : ", " + activationStatusChanged.name()));
			log.setActivityUserId(updatedById);
			log.setKeyId(id);
			log.setKeyType(className.getName());
			log.setActivityLog(StringUtils.join(changeSet, " || "));
		} catch (Exception e) {
			LOG.error(String.format("Error while logging activity by user %d\nOld Object : %s\n\nNew Object : %s",
					updatedById, oldObject, newObject));
		}

		return log;
	}

	public static void main(String[] args) {
		ActivityLoggerServiceImpl impl = new ActivityLoggerServiceImpl();
		Address a = new Address();
		a.setId(1);
		a.setAddressType("HOME");
		a.setCompany("STPL");
		Unit u = new Unit();
		u.setId(10000);
		u.setName("Good Earth");
		u.setAddress(a);
		u.setStatus(UnitStatus.IN_ACTIVE);
		u.getPaymentModes().addAll(Arrays.asList(3, 2, 1));
		Address a1 = new Address();
		a1.setId(2);
		a1.setAddressType("HOME");
		a1.setCompany("DKC");
		Unit u1 = new Unit();
		u1.setId(10000);
		u1.setName("Good Earth City Center");
		u1.setAddress(a1);
		u1.setStatus(UnitStatus.ACTIVE);
		u1.getPaymentModes().addAll(Arrays.asList(1, 2, 4));
		System.out.println(impl.get(u, u1, 100000));

	}


}
