package com.stpl.tech.master.data.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "MONK_X_TWO_URLS")
public class MonkXTwoUrls {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "URL_ID")
    private Integer urlId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MONK_ID", referencedColumnName = "id")
    @JsonIgnore
    private MonkXTwoMetadata monkMetadata;
    
    @Column(name = "URL")
    private String monkUrl;
    
    @Column(name = "MACHINE_INDEX")
    private Integer machineIndex;
    
    
    @Column(name = "IS_ACTIVE")
    private Boolean isActive;
    
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    
    @Column(name = "LAST_UPDATED_TIMESTAMP")
    private Date lastUpdatedTimestamp;
}