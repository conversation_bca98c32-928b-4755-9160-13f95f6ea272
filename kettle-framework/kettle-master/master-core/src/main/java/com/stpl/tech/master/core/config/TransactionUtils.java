package com.stpl.tech.master.core.config;

import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import javax.sql.DataSource;

public class TransactionUtils extends TransactionInterceptor {

    public static PlatformTransactionManager currentTransactionManager() {
        TransactionInfo transactionInfo = TransactionAspectSupport.currentTransactionInfo();
        if (transactionInfo != null) {
            return transactionInfo.getTransactionManager();
        }
        throw new IllegalStateException("No current transaction found");
    }

    public static DataSource currentDataSource() {
        PlatformTransactionManager txManager = currentTransactionManager();
        if(txManager instanceof JpaTransactionManager) {
            return ((JpaTransactionManager) txManager).getDataSource();
        }
        throw new IllegalStateException("Current transaction manager is not a JpaTransactionManager");
    }

}
