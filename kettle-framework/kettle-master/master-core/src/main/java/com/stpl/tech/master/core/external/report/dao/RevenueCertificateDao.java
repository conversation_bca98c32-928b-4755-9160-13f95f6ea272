package com.stpl.tech.master.core.external.report.dao;

import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.data.model.UnitAttributeMapping;
import com.stpl.tech.master.data.model.UnitDetail;

import java.util.List;

public interface RevenueCertificateDao extends AbstractDao {
    boolean saveEmailId(Integer unitId, String toBeGenerated, String emailId);

    public List<UnitAttributeMapping> getEmailIdByUnitIdAndAttributeIds(Integer unitId, List<Integer> attributeIds);
}
