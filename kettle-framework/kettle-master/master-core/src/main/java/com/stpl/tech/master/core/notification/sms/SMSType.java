package com.stpl.tech.master.core.notification.sms;

public enum SMSType {

	OTP("OTP", "sms.otp.client"), TRANSACTIONAL("TRANSACTIONAL", "sms.transactional.client"), PROMOTIONAL("PROMOTIONAL",
			"sms.promotional.client"), OPT_VIA_IVR("OPT_VIA_IVR","sms.otp.client"),DOHFUL_OTP("OTP", "sms.otp.client");

	private final String key;
	private final String value;

	SMSType(String key, String value) {
		this.key = key;
		this.value = value;
	}

	public String getKey() {
		return key;
	}

	public String getValue() {
		return value;
	}
}
