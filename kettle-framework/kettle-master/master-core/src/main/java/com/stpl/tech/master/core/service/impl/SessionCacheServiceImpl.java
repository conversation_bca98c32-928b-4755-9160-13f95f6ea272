/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.cache.SessionCache;
import com.stpl.tech.master.core.service.SessionCacheService;
import com.stpl.tech.master.data.dao.MasterUserDataDao;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SessionCacheServiceImpl implements SessionCacheService {

    Logger LOG = LoggerFactory.getLogger(SessionCacheServiceImpl.class);

    @Autowired
    private SessionCache sessionCache;

    @Autowired
    private MasterUserDataDao masterUserDataDao;

    public SessionCacheServiceImpl() {
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void clearAllPosSession() {
        for (String sessionKey : sessionCache.getSessionKeyToDetailMap().keySet()) {
            try {
                if (sessionCache.isPosSession(sessionKey)) {
                    sessionCache.removeFromCache(sessionCache.getSessionKeyToDetailMap().get(sessionKey).getUserId(), sessionKey);
                }
            } catch (AuthenticationFailureException e) {
                LOG.error("Error decrypting session key: " + sessionKey);
            }
        }
        try{
            masterUserDataDao.getActiveEmployeeSessionDetail(AppUtils.getDateAfterDays(AppUtils.getBusinessDate(),-1));
        }catch (Exception e){
            LOG.error("Error Fetching Past 24 Hour Active Employee Session Details ", e);
        }

    }

    @Override
    public void clearUnitPosSession(Integer unitId) {
        for (Integer key : sessionCache.getUnitToSessionMap().keySet()) {
            try {
                if (key.equals(unitId)) {
                    String sessionKey = sessionCache.getUnitToSessionMap().get(unitId).getSessionKey();
                    if (sessionCache.isPosSession(sessionKey)) {
                        sessionCache.removeFromCache(sessionCache.getSessionKeyToDetailMap().get(sessionKey).getUserId(), sessionKey);
                    }
                }
            } catch (AuthenticationFailureException e) {
                LOG.error("Error clearing unit pos session: ", e);
            }
        }
    }

    @Override
    public void clearUnitSession(Integer unitId) {
        for (Integer key : sessionCache.getUnitToSessionMap().keySet()) {
            if (key.equals(unitId)) {
                String sessionKey = sessionCache.getUnitToSessionMap().get(unitId).getSessionKey();
                sessionCache.removeFromCache(sessionCache.getSessionKeyToDetailMap().get(sessionKey).getUserId(), sessionKey);
            }
        }
    }

    @Override
    public void clearUserPosSession(Integer userId) {
        for (String sessionKey : sessionCache.getSessionKeyToDetailMap().keySet()) {
            try {
                if ((sessionCache.getSessionKeyToDetailMap().get(sessionKey).getUserId() == userId) && sessionCache.isPosSession(sessionKey)) {
                    sessionCache.removeFromCache(sessionCache.getSessionKeyToDetailMap().get(sessionKey).getUserId(), sessionKey);
                }
            } catch (AuthenticationFailureException e) {
                LOG.error("Error decrypting session key: " + sessionKey);
            }
        }
    }

    @Override
    public void clearUserSession(Integer userId) {
        for (String sessionKey : sessionCache.getSessionKeyToDetailMap().keySet()) {
            if ((sessionCache.getSessionKeyToDetailMap().get(sessionKey).getUserId() == userId)) {
                sessionCache.removeFromCache(sessionCache.getSessionKeyToDetailMap().get(sessionKey).getUserId(), sessionKey);
            }
        }
    }

}
