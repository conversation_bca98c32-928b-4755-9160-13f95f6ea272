package com.stpl.tech.master.data.dao;

import com.stpl.tech.master.recipe.calculator.model.IterationIngredientInstructions;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IterationIngredientInstructionsDao extends MongoRepository<IterationIngredientInstructions, String> {

    //@Query("{'instruction' : {'$eq' : ?0}")
    public IterationIngredientInstructions findByInstruction(String instruction);
}
