package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.MenuExcelUploadEvent;
import com.stpl.tech.master.domain.model.MenuExcelUploadResponse;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.util.List;

public interface PartnerMenuMetadataService {
    MenuExcelUploadResponse processExcelUpload(MultipartFile file , Integer uploadedBy , List<String> selectedSheets , String menuApp)
            throws DataUpdationException, IOException;

    View downloadExcelForMenuMappings(String menuApp , Boolean isTemplate);

    public MenuExcelUploadEvent getLatestMenuMappingUploadEvent(String menuApp);
}
