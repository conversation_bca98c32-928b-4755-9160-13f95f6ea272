package com.stpl.tech.master.core.external.partner.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;

import com.stpl.tech.master.data.model.MenuSequenceData;
import com.stpl.tech.master.data.model.MenuSequenceTimingData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.partner.dao.ChannelPartnerDao;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.ChannelPartner;
import com.stpl.tech.master.data.model.ChannelPartnerCommission;
import com.stpl.tech.master.data.model.MenuRecommendationMappingData;
import com.stpl.tech.master.data.model.MenuSequenceMappingData;
import com.stpl.tech.master.data.model.PriceProfile;
import com.stpl.tech.master.data.model.PriceProfileRangeValues;
import com.stpl.tech.master.data.model.ProductGroupData;
import com.stpl.tech.master.data.model.ProductSequenceData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MenuApp;
import com.stpl.tech.master.domain.model.PriceProfileDetail;
import com.stpl.tech.master.domain.model.PriceProfileRangeValueDetail;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Repository
public class ChannelPartnerDaoImpl extends AbstractMasterDaoImpl implements ChannelPartnerDao {

    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerDaoImpl.class);

    @Override
    public List<ChannelPartnerDetail> getAllChannelPartner(Date businessDate) throws DataNotFoundException {
        List<ChannelPartnerDetail> results = new ArrayList<>();
        Query query = manager.createQuery("FROM ChannelPartner E order by E.partnerId");
        @SuppressWarnings("unchecked")
        List<ChannelPartner> list = query.getResultList();
        for (ChannelPartner partner : list) {
            results.add(MasterDataConverter.convert(partner, getChannelPartnerCommission(partner.getPartnerId(), businessDate)));
        }
        return results;
    }

    @Override
    public ChannelPartnerCommission getChannelPartnerCommission(int partnerId, Date businessDate) throws DataNotFoundException {
        Query query = manager.createQuery("FROM ChannelPartnerCommission E where E.partnerId = :partnerId and E.startDate <= :businessDate and E.endDate >= :businessDate order by partnerCommissionId desc");
        query.setParameter("partnerId", partnerId);
        query.setParameter("businessDate", businessDate);
        List<ChannelPartnerCommission> list = query.getResultList();
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<UnitChannelPartnerMappingData> getUnitChannelPartnerMappings() {
        Query query = manager.createQuery(
            "FROM UnitChannelPartnerMappingData E");
//        query.setParameter("status", "ACTIVE");
        @SuppressWarnings("unchecked")
        List<UnitChannelPartnerMappingData> resultList = query.getResultList();
        return resultList;
    }

    @Override
    public UnitChannelPartnerMappingData findMappingByUnitAndPartnerId(Integer unitId, Integer partnerId) {
        Query query = manager.createQuery("FROM UnitChannelPartnerMappingData u WHERE u.unitId = :unitId AND u.channelPartnerId = :partnerId");
        query.setParameter("unitId", unitId);
        query.setParameter("partnerId", partnerId);
        try {
            return (UnitChannelPartnerMappingData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }


    @Override
    public List<UnitChannelPartnerMenuMappingData>  getUnitPartnerMenuMappings() {
        Query query = manager.createQuery(
            "FROM UnitChannelPartnerMenuMappingData E");
        @SuppressWarnings("unchecked")
        List<UnitChannelPartnerMenuMappingData> resultList = query.getResultList();
        return resultList;
    }

    @Override
    public List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappings(Integer unitChannelPartnerMappingId,Integer brandId) {
        try{
            Query query = manager.createQuery(
                    "FROM UnitChannelPartnerMenuMappingData E where unitPartnerMappingId = :unitChannelPartnerMappingId and  brandId = :brandId");
            query.setParameter("unitChannelPartnerMappingId",unitChannelPartnerMappingId);
            query.setParameter("brandId",brandId);
            List<UnitChannelPartnerMenuMappingData> resultList = query.getResultList();
            return resultList;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<UnitChannelPartnerMenuMappingData> getUnitPartnerMenuMappingsByChannelPartnerMappingId(List<Integer> unitChannelPartnerMappingIds,Integer brandId) {
        try{
            Query query = manager.createQuery(
                    "FROM UnitChannelPartnerMenuMappingData E where unitPartnerMappingId in :unitChannelPartnerMappingIds and  brandId = :brandId");
            query.setParameter("unitChannelPartnerMappingIds",unitChannelPartnerMappingIds);
            query.setParameter("brandId",brandId);
            List<UnitChannelPartnerMenuMappingData> resultList = query.getResultList();
            return resultList;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public void inActiveUnitChannelPartnerMenuMapping(UnitChannelPartnerMenuMappingData data){
        try{
            Query query = manager.createQuery("FROM UnitChannelPartnerMenuMappingData E WHERE unitPartnerMappingId = :unitPartnerMappingId and menuType = :menuType and " +
                    "menuApp = :menuApp and brandId =:brandId and id <> :id and status= :status");
            query.setParameter("id",data.getId());
            query.setParameter("brandId",data.getBrandId());
            query.setParameter("menuApp",data.getMenuApp());
            query.setParameter("menuType",data.getMenuType());
            query.setParameter("unitPartnerMappingId",data.getUnitPartnerMappingId());
            query.setParameter("status",AppConstants.ACTIVE);
            List<UnitChannelPartnerMenuMappingData> result= query.getResultList();
            for(UnitChannelPartnerMenuMappingData val:result){
                val.setStatus(AppConstants.IN_ACTIVE);
                manager.persist(val);
            }
            manager.flush();
        }catch (Exception e){
            LOG.error("Exception Caught::::",e);
        }
    }


    @Override
    public List<UnitChannelPartnerMenuMappingData> findMenuMappingByUnitPartnerMappingId(Integer unitChannelPartnerMappingId) {
        Query query = manager.createQuery("FROM UnitChannelPartnerMenuMappingData u WHERE u.unitPartnerMappingId = :unitChannelPartnerMappingId");
        query.setParameter("unitChannelPartnerMappingId", unitChannelPartnerMappingId);
        return query.getResultList();
    }


    @Override
    public List<UnitChannelPartnerMenuMappingData> findMenuMappingByUnitPartnerBrandMappingId(Integer unitChannelPartnerMappingId, Integer brandId) {
        Query query = manager.createQuery("FROM UnitChannelPartnerMenuMappingData u WHERE u.unitPartnerMappingId = :unitChannelPartnerMappingId AND u.brandId = :brandId");
        query.setParameter("unitChannelPartnerMappingId", unitChannelPartnerMappingId);
        query.setParameter("brandId", brandId);
        return query.getResultList();
    }

    @Override
    public List<UnitChannelPartnerMenuMappingData> findMenuMappingByMenuSequenceId(Integer menuSeqId) {
        Query query = manager.createQuery("FROM UnitChannelPartnerMenuMappingData u WHERE u.menuSequenceId = :menuSeqId AND u.status = :status");
        query.setParameter("menuSeqId", menuSeqId);
        query.setParameter("status", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public List<MenuSequenceData> findAllActiveMenuSequenceData() {
        Query query = manager.createQuery("FROM MenuSequenceData m WHERE m.menuStatus = :status");
        query.setParameter("status", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public List<String> findAllNotActiveMenuSequenceData(List<Integer> menuSequenceIds) {
        Query query = manager.createQuery("SELECT m.menuSequenceName FROM  MenuSequenceData m WHERE m.menuSequenceId IN :list AND m.menuStatus <> :status");
        query.setParameter("list", menuSequenceIds);
        query.setParameter("status", AppConstants.ACTIVE);
        return query.getResultList();
    }

    @Override
    public List<MenuSequenceMappingData> getAllMenuSequenceMappingByMenuSequenceId(Integer menuSequenceId) {

        Query query = manager.createQuery(
            "FROM MenuSequenceMappingData m where m.menuSequenceId = :menuSequenceId");
        query.setParameter("menuSequenceId", menuSequenceId);
        @SuppressWarnings("unchecked")
        List<MenuSequenceMappingData> list = query.getResultList();
        return list;
    }

    @Override
    public List<MenuSequenceTimingData> getAllMenuSequenceTimingByMenuSequenceId(Integer menuSequenceId){
        Query query = manager.createQuery(
                "FROM MenuSequenceTimingData m where m.menuSequenceId = :menuSequenceId");
        query.setParameter("menuSequenceId", menuSequenceId);
        @SuppressWarnings("unchecked")
        List<MenuSequenceTimingData> list = query.getResultList();
        return list;
    }


    @Override
    public MenuSequenceTimingData getMenuSequenceTimingByMenuSequenceIdAndProductGroupId(Integer menuSequenceId,Integer productGroupId){
        Query query = manager.createQuery(
                "FROM MenuSequenceTimingData m where m.productGroupParentId = :productGroupParentId AND m.menuSequenceId = :menuSequenceId");
        query.setParameter("productGroupParentId", productGroupId);
        query.setParameter("menuSequenceId",menuSequenceId);
        @SuppressWarnings("unchecked")
        MenuSequenceTimingData result = (MenuSequenceTimingData) query.getSingleResult();
        return result;
    }

    @Override
    public MenuSequenceTimingData getMenuSequenceTimingByProductGroupId(Integer productGroupId){
        Query query = manager.createQuery(
                "FROM MenuSequenceTimingData m where m.productGroupParentId = :productGroupParentId");
        query.setParameter("productGroupParentId", productGroupId);
        @SuppressWarnings("unchecked")
        MenuSequenceTimingData result = (MenuSequenceTimingData) query.getSingleResult();
        return result;
    }

    @Override
    public List<ProductGroupData> getAllProductGroupByGroupIds(List<Integer> groupIds) {

        Query query = manager.createQuery(
            "FROM ProductGroupData p where p.groupId IN :groupIds");
        query.setParameter("groupIds", groupIds);
        @SuppressWarnings("unchecked")
        List<ProductGroupData> list = query.getResultList();
        return list;
    }

    @Override
    public List<ProductSequenceData> getAllSequencedProductInGroupForCloning(Integer cloneId) {
        Query query = manager.createQuery(
            "FROM ProductSequenceData p where p.productGroupId = :cloneId AND p.status = :status order by p.productIndex ");
        query.setParameter("cloneId", cloneId);
        query.setParameter("status", UnitStatus.ACTIVE.value());
        @SuppressWarnings("unchecked")
        List<ProductSequenceData> list = query.getResultList();
        return list;
    }

    @Override
    public List<MenuSequenceMappingData> getAllSequencedProductInMenuForCloning(Integer cloneId) {
        Query query = manager.createQuery(
            "FROM MenuSequenceMappingData p where p.menuSequenceId = :cloneId AND p.status = :status order by p.productGroupParentId");
        query.setParameter("cloneId", cloneId);
        query.setParameter("status", UnitStatus.ACTIVE.value());
        @SuppressWarnings("unchecked")
        List<MenuSequenceMappingData> list = query.getResultList();
        return list;
    }


    @Override
    public List<ProductSequenceData> getProductSequenceByGroupIds(List<Integer> groupIds) {
        Query query = manager.createQuery(
            "FROM ProductSequenceData p where p.productGroupId IN :groupIds AND p.status = :status");
        query.setParameter("groupIds", groupIds);
        query.setParameter("status", UnitStatus.ACTIVE.value());
        @SuppressWarnings("unchecked")
        List<ProductSequenceData> list = query.getResultList();
        return list;
    }

    @Override
    public List<ProductGroupData> findAllProductGroupDataWithFilter(String groupType, String menuAppType) {
        try {
            StringBuilder queryString = new StringBuilder("From ProductGroupData p where p.menuApp = :menuAppType");
            if(groupType.length() != 0) {
                queryString.append(" and p.groupType = :groupType");
            }
            Query query = manager.createQuery(queryString.toString());
            if(groupType.length() != 0) {
                query.setParameter("groupType", groupType);
            }
            query.setParameter("menuAppType", menuAppType);
            @SuppressWarnings("unchecked")
            List<ProductGroupData> list = query.getResultList();
            return list;
        }
        catch (Exception exp) {
            LOG.info("Exception while getting product group data with filters : {} " ,exp.getMessage());
            return new ArrayList<ProductGroupData>();
        }
    }

    @Override
    public List<ProductGroupData> getProductGroupByTagAndType(String tag, String type) {

        Query query = manager.createQuery(
            "FROM ProductGroupData p where p.groupTag = :tag AND p.groupType = :type");
        query.setParameter("tag", tag);
        query.setParameter("type", type);
        @SuppressWarnings("unchecked")
        List<ProductGroupData> list = query.getResultList();
        return list;
    }

    @Override
    public List<ProductGroupData> getProductGroupByNameAndType(String name, String type, String tag) {
        Query query = manager.createQuery(
            "FROM ProductGroupData p where p.groupName = :name AND p.groupType = :type AND p.groupTag = :tag");
        query.setParameter("name", name);
        query.setParameter("type", type);
        query.setParameter("tag", tag);
        @SuppressWarnings("unchecked")
        List<ProductGroupData> list = query.getResultList();
        return list;
    }

    @Override
    public List<ProductSequenceData> getProductSequenceByGroupId(Integer groupId) {

        Query query = manager.createQuery(
            "FROM ProductSequenceData p where p.productGroupId = :groupId AND p.status = :status");
        query.setParameter("groupId", groupId);
        query.setParameter("status", UnitStatus.ACTIVE.value());
        @SuppressWarnings("unchecked")
        List<ProductSequenceData> list = query.getResultList();
        return list;
    }

    @Override
    public List<ProductSequenceData> getAllProductSequenceByGroupId(Integer groupId) {

        Query query = manager.createQuery(
            "FROM ProductSequenceData p where p.productGroupId = :groupId");
        query.setParameter("groupId", groupId);
        @SuppressWarnings("unchecked")
        List<ProductSequenceData> list = query.getResultList();
        return list;
    }

    @Override
    public ProductSequenceData getProductSequenceByGroupIdAndProductId(Integer groupId, Integer productId) {

        Query query = manager.createQuery(
            "FROM ProductSequenceData p where p.productGroupId = :groupId AND p.productId = :productId");
        query.setParameter("groupId", groupId);
        query.setParameter("productId", productId);
        try {
            return (ProductSequenceData) query.getSingleResult();
        } catch (NoResultException | NonUniqueResultException e) {
            return null;
        }
    }


    @Override
    public List<MenuRecommendationMappingData> getAllRecommendationMapping(Integer recommendationId,String status) {
        List<String> statuses = Objects.isNull(status) ? new ArrayList<>(Arrays.asList(AppConstants.ACTIVE ,AppConstants.IN_ACTIVE)) :
                new ArrayList<>(List.of(status));
        Query query = manager.createQuery(
            "FROM MenuRecommendationMappingData p where p.menuRecommendationId = :menuRecommendationId and p.status in (:status) ");
        query.setParameter("menuRecommendationId", recommendationId);
        query.setParameter("status",statuses);
        @SuppressWarnings("unchecked")
        List<MenuRecommendationMappingData> list = query.getResultList();
        return list;
    }

    @Override
    public List<Integer> getChannelPartnerIdsForMenuSequence(Integer menuSequenceId) {
        Query query = manager.createQuery(
            "FROM UnitChannelPartnerMenuMappingData p where p.menuSequenceId = :menuSequenceId AND p.status = :status");
        query.setParameter("menuSequenceId", menuSequenceId);
        query.setParameter("status", UnitStatus.ACTIVE.value());
        @SuppressWarnings("unchecked")
        List<UnitChannelPartnerMenuMappingData> unitChannelPartnerMenuMappingData = query.getResultList();
        List<Integer> channelPartnerIds = new ArrayList<Integer>();
        for (UnitChannelPartnerMenuMappingData unitChannelPartnerMenuMapping : unitChannelPartnerMenuMappingData) {
            channelPartnerIds.add(unitChannelPartnerMenuMapping.getUnitPartnerMappingId());
        }
        return channelPartnerIds;
    }

    @Override
    public List<Integer> getUnitIdsForMenu(List<Integer> ids, Integer kettlePartnerId) {
        Query query = manager.createQuery(
            "FROM UnitChannelPartnerMappingData p where p.id IN :ids AND p.channelPartnerId = :channelPartnerId");
        query.setParameter("ids", ids);
        query.setParameter("channelPartnerId", kettlePartnerId);
        List<UnitChannelPartnerMappingData> unitChannelPartnerMappingData = query.getResultList();
        List<Integer> unitIds = new ArrayList<Integer>();
        for (UnitChannelPartnerMappingData unitChannelPartnerMapping : unitChannelPartnerMappingData) {
            unitIds.add(unitChannelPartnerMapping.getUnitId());
        }
        return unitIds;
    }

    @Override
    public List<Integer> getMenuSequenceIdsAsPerMenuApp(MenuApp menuApp) {
        Query query = manager.createQuery(
            " select  p.menuSequenceId FROM MenuSequenceData p where p.menuApp =:menuApp ");
        query.setParameter("menuApp", menuApp.toString());
        return query.getResultList();
    }

    @Override
    public List<Integer> getCategoryIdsMenuSequenceMapping(List<Integer> menuSequenceIds) {
        Query query = manager.createQuery(
            " select DISTINCT p.productGroupParentId FROM MenuSequenceMappingData p where p.menuSequenceId IN :ids AND p.status = :status and p.productGroupParentId IS NOT NULL ");
        query.setParameter("ids", menuSequenceIds);
        query.setParameter("status", UnitStatus.ACTIVE.value());
        return query.getResultList();
    }

    @Override
    public List<ProductGroupData> getProductGroupDataAsPerIdAndMenuApp(List<Integer> ids, MenuApp menuApp) {
        Query query = manager.createQuery(
            " FROM ProductGroupData p where p.menuApp =:menuApp AND  p.groupId IN :groupIds and p.status=:status");
        query.setParameter("menuApp", menuApp.toString());
        query.setParameter("groupIds", ids);
        query.setParameter("status", UnitStatus.ACTIVE.value());
        return query.getResultList();
    }

    @Override
    public PriceProfileDetail getUnitPartnerProfilePrice(Integer priceProfileId) {
        PriceProfile priceProfile = manager.find(PriceProfile.class, priceProfileId);
        List<PriceProfileRangeValues> priceProfileRangeValues = new ArrayList<>();
        if (priceProfile != null && priceProfile.getProfileStatus().equals(AppConstants.ACTIVE)) {
            Query query = manager.createQuery(" FROM PriceProfileRangeValues WHERE priceProfileId= :priceProfileId and rangeValuesStatus= :rangeValuesStatus");
            query.setParameter("priceProfileId", priceProfileId);
            query.setParameter("rangeValuesStatus", AppConstants.ACTIVE);
            priceProfileRangeValues = query.getResultList();
        }
        return MasterDataConverter.convert(priceProfile, priceProfileRangeValues);
    }

    @Override
    public List<PriceProfileDetail> getUnitPartnerProfilePrice() {
        Query priceQuery=manager.createQuery("FROM PriceProfile ");
        List<PriceProfile> priceProfiles= priceQuery.getResultList();

        List<PriceProfileRangeValues> priceProfileRangeValues = new ArrayList<>();
        List<PriceProfileDetail> priceProfileDetail = new ArrayList<>();

        for(PriceProfile value : priceProfiles){
            if (value != null && value.getProfileStatus().equals(AppConstants.ACTIVE)) {
                Query query = manager.createQuery(" FROM PriceProfileRangeValues WHERE priceProfileId= :priceProfileId and rangeValuesStatus= :rangeValuesStatus");
                query.setParameter("priceProfileId", value.getPriceProfileId());
                query.setParameter("rangeValuesStatus", AppConstants.ACTIVE);
                priceProfileRangeValues = query.getResultList();
            }
            priceProfileDetail.add(MasterDataConverter.convert(value, priceProfileRangeValues));
        }
        return priceProfileDetail;
    }

    @Override
    public boolean addProfilePriceMapping(PriceProfileDetail detail) {
        try {
                PriceProfile priceProfile = new PriceProfile();
                priceProfile.setPriceProfileId(detail.getPriceProfileId());
                priceProfile.setLastUpdatedBy(detail.getLastUpdatedBy());
                priceProfile.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                priceProfile.setProfileCreationTime(AppUtils.getCurrentTimestamp());
                priceProfile.setThresholdPercentage(detail.getThresholdPercentage());
                priceProfile.setProfileDescription(detail.getProfileDescription());
                priceProfile.setProfileStatus(detail.getProfileStatus());
                priceProfile.setProfileType(detail.getProfileType().name());
                priceProfile.setThresholdPercentage(detail.getThresholdPercentage());
                manager.persist(priceProfile);
                for (PriceProfileRangeValueDetail rangeValues : detail.getProfileRangeValueDetails()) {
                    PriceProfileRangeValues values = new PriceProfileRangeValues();
                    values.setProfileProfileRangeValuesId(rangeValues.getProfileProfileRangeValuesId());
                    values.setPriceProfileId(priceProfile.getPriceProfileId());
                    values.setDeltaPrice(rangeValues.getDeltaPrice());
                    values.setEndPrice(rangeValues.getEndPrice());
                    values.setLastUpdatedBy(rangeValues.getLastUpdatedBy());
                    values.setStartPrice(rangeValues.getStartPrice());
                    values.setRangeValuesStatus(rangeValues.getRangeValuesStatus());
                    values.setStartPrice(rangeValues.getStartPrice());
                    values.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                    values.setRangeValuesActivationTime(AppUtils.getCurrentTimestamp());
                    manager.persist(values);
                }
                manager.flush();
                return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public  List<PriceProfileDetail> getProfilePrice(String priceProfileStrategy){
        Query query= manager.createQuery("From PriceProfile where profileType = :profileType");
        query.setParameter("profileType",priceProfileStrategy);
        List<PriceProfile> profileDetails= query.getResultList();
        List<PriceProfileDetail> result= new ArrayList<>();
        for(PriceProfile detail: profileDetails){
            Query rangeQuery=manager.createQuery("FROM PriceProfileRangeValues where priceProfileId= :priceProfileId");
            rangeQuery.setParameter("priceProfileId",detail.getPriceProfileId());
            List<PriceProfileRangeValues> profileRangeValues=rangeQuery.getResultList();
            result.add(MasterDataConverter.convert(detail,profileRangeValues));
        }
        return result;
    }

    @Override
    public boolean updateProfilePriceMapping(IdCodeName detail){
        try{
            PriceProfile profile = manager.find(PriceProfile.class, detail.getId());
            profile.setProfileStatus(detail.getStatus());
            profile.setThresholdPercentage(new BigDecimal(Integer.parseInt(detail.getCode())));
            profile.setProfileDescription(detail.getName());
            profile.setLastUpdateTime(AppUtils.getCurrentTimestamp());
            profile.setLastUpdatedBy(detail.getShortCode());
            manager.persist(profile);
            return true;
        }catch (Exception e){
            return false;
        }
    }

	@Override
	public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles() {
		Map<Integer, PriceProfileDetail> map = new HashMap<Integer, PriceProfileDetail>();
		Query query = manager.createQuery("From PriceProfile where profileStatus = :profileStatus");
		query.setParameter("profileStatus", AppConstants.ACTIVE);
		List<PriceProfile> profileDetails = query.getResultList();
		for (PriceProfile detail : profileDetails) {
			Query rangeQuery = manager
					.createQuery("FROM PriceProfileRangeValues where priceProfileId= :priceProfileId");
			rangeQuery.setParameter("priceProfileId", detail.getPriceProfileId());
			List<PriceProfileRangeValues> profileRangeValues = rangeQuery.getResultList();
			map.put(detail.getPriceProfileId(), MasterDataConverter.convert(detail, profileRangeValues));
		}
		return map;
	}

    @Override
	public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles(Integer priceProfileId) {
		Map<Integer, PriceProfileDetail> map = new HashMap<>();
		Query query = manager.createQuery("From PriceProfile where profileStatus = :profileStatus and priceProfileId= :priceProfileId ");
		query.setParameter("profileStatus", AppConstants.ACTIVE);
		query.setParameter("priceProfileId", priceProfileId);
		List<PriceProfile> profileDetails = query.getResultList();
		for (PriceProfile detail : profileDetails) {
			Query rangeQuery = manager
					.createQuery("FROM PriceProfileRangeValues where priceProfileId= :priceProfileId");
			rangeQuery.setParameter("priceProfileId", detail.getPriceProfileId());
			List<PriceProfileRangeValues> profileRangeValues = rangeQuery.getResultList();
			map.put(detail.getPriceProfileId(), MasterDataConverter.convert(detail, profileRangeValues));
		}
		return map;
	}

    @Override
    public Map<Integer, PriceProfileDetail> getAllActivePriceProfiles(List<Integer> priceProfileIds) {
        Map<Integer, PriceProfileDetail> map = new HashMap<>();
        Query query = manager.createQuery("From PriceProfile where profileStatus = :profileStatus and priceProfileId in :priceProfileIds ");
        query.setParameter("profileStatus", AppConstants.ACTIVE);
        query.setParameter("priceProfileIds", priceProfileIds);
        List<PriceProfile> profileDetails = query.getResultList();
        for (PriceProfile detail : profileDetails) {
            Query rangeQuery = manager
                    .createQuery("FROM PriceProfileRangeValues where priceProfileId= :priceProfileId");
            rangeQuery.setParameter("priceProfileId", detail.getPriceProfileId());
            List<PriceProfileRangeValues> profileRangeValues = rangeQuery.getResultList();
            map.put(detail.getPriceProfileId(), MasterDataConverter.convert(detail, profileRangeValues));
        }
        return map;
    }
}
