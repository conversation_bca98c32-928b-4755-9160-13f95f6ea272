package com.stpl.tech.master.core.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.util.HSSFColor.HSSFColorPredefined;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import com.stpl.tech.master.Counter;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.SCMServiceEndpoints;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.MetaInfo;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.recipe.model.CompositeIngredientData;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeCategoryCost;
import com.stpl.tech.master.recipe.model.RecipeCost;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.model.RecipeIngredientCost;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeData;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Service
public class RecipeHelper {

	private static final Logger LOG = LoggerFactory.getLogger(RecipeHelper.class);

	@Autowired
	private MasterProperties env;

	@Autowired
	private MasterDataCache cache;

	public View getRecipeView(List<RecipeCost> costs, List<RecipeDetail> recipes, String fileName) {
		return new AbstractXlsxView() {
			@Override
			protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
					HttpServletRequest request, HttpServletResponse response) throws Exception {

				response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
				MetaInfo scmProducts = getSCMProductData();
				MetaInfo menuProducts = getMenuProductData();
				createRecipeDetailSheet(recipes, workbook, scmProducts, menuProducts);
				createRecipeCostSheet(costs, workbook);
			}

		};
	}

	public View getRecipeDetailView(List<RecipeDetail> recipes, String fileName) {
		return new AbstractXlsxView() {
			@Override
			protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
					HttpServletRequest request, HttpServletResponse response) throws Exception {

				response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
				MetaInfo scmProducts = getSCMProductData();
				MetaInfo menuProducts = getMenuProductData();
				createRecipeDetailSheet(recipes, workbook, scmProducts, menuProducts);
			}

		};
	}

	public View getRecipeInstructionDetailView(List<RecipeDetail> recipes, String fileName) {
		return new AbstractXlsxView() {
			@Override
			protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
					HttpServletRequest request, HttpServletResponse response) throws Exception {

				response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
				MetaInfo scmProducts = getSCMProductData();
				createRecipeInstructionDetailSheet(recipes, workbook, scmProducts);
			}

		};
	}


	public View getMonkRecipeDetailView(int mode, List<MonkRecipeDetail> recipes, String fileName) {
		return new AbstractXlsxView() {
			@Override
			protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
					HttpServletRequest request, HttpServletResponse response) throws Exception {

				response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
				createMonkRecipeDetailSheet(mode, recipes, workbook);
			}

		};
	}

	public View getRecipeCostView(List<RecipeCost> costs, String fileName) {
		return new AbstractXlsxView() {
			@Override
			protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
					HttpServletRequest request, HttpServletResponse response) throws Exception {

				response.addHeader("Content-Disposition", "attachment; filename=" + fileName);
				createRecipeCostSheet(costs, workbook);

			}

		};
	}

	private MetaInfo getSCMProductData() {
		LOG.info("Fetching Product Information from SCM");
		String endPoint = env.getSCMEndpoint() + SCMServiceEndpoints.PRODUCT_DEFINITION;
		try {
			return WebServiceHelper.getRequestWithParam(endPoint, env.getSCMAuthInternal(), new HashMap<>(),
					MetaInfo.class);
		} catch (Exception e) {
			LOG.error("Error while creating web request to {}", endPoint, e);
		}
		return new MetaInfo("NOT_FOUND", new HashMap<>());

	}

	private MetaInfo getMenuProductData() {
		LOG.info("Fetching Product Information from master");
		Map<Integer, ProductBasicDetail> defs = cache.getProductBasicDetails();
		MetaInfo infor = new MetaInfo("MENU_PRODUCT", new HashMap<>());
		for (Integer key : defs.keySet()) {
			ProductBasicDetail p = defs.get(key);
			infor.getData().put(key, new IdCodeName(p.getDetail().getId(), p.getDetail().getName(),
					p.getDetail().getCode(), null, p.getClassification().name(), p.getStatus().name()));
		}
		return infor;

	}

	private void createRecipeCostSheet(List<RecipeCost> costs, Workbook workbook) {
		Sheet sheet = workbook.createSheet("Recipe Cost Details");
		generateRecipeSheet(workbook, sheet);
		Counter rowCount = new Counter(0);
		if (costs.size() > 0) {
			for (RecipeCost cost : costs) {
				if (cost != null) {
					writeRecipe(sheet, rowCount, cost);
				}
			}
		}
	}

	private void createRecipeInstructionDetailSheet(List<RecipeDetail> recipes, Workbook workbook, MetaInfo scmProducts) {
		Sheet sheet = workbook.createSheet("Recipe Instruction Details");
		generateInstructionSheet(workbook, sheet);
		Counter rowCount = new Counter(0);
		if (recipes.size() > 0) {
			for (RecipeDetail recipe : recipes) {
				writeInstructions(sheet, rowCount, recipe, scmProducts);
			}
		}
	}

	private void createRecipeDetailSheet(List<RecipeDetail> recipes, Workbook workbook, MetaInfo scmProducts, MetaInfo menuProducts) {
		Sheet sheet = workbook.createSheet("Recipe Details");
		generateSheet(workbook, sheet);
		Counter rowCount = new Counter(0);
		if (recipes.size() > 0) {
			for (RecipeDetail recipe : recipes) {
				writeRecipe(sheet, rowCount, recipe, scmProducts, menuProducts);
			}
		}
	}

	private void createMonkRecipeDetailSheet(int mode, List<MonkRecipeDetail> recipes, Workbook workbook) {
		Sheet sheet = workbook.createSheet("Recipe Details");
		generateMonkRecipeSheet(workbook, sheet);
		Counter rowCount = new Counter(0);
		if (recipes.size() > 0) {
			for (MonkRecipeDetail recipe : recipes) {
				writeRecipe(sheet, rowCount, recipe, mode);
			}
		}
	}

	public void notify(String text) {
		SlackNotificationService.getInstance().sendNotification(env.getEnvironmentType(), AppConstants.KETTLE,
				SlackNotification.PRODUCT_NOTIFICATIONS, text);
	}

	public String getRecipeUpdateTemplate(RecipeDetail detail) {
		return "Recipe Updated" + getBasicRecipeDetails(detail);
	}

	private String getBasicRecipeDetails(RecipeDetail detail) {
		return "\nRecipe Name: " + detail.getName() + "\nProduct: " + detail.getProduct().getName() + "\nDimension: "
				+ detail.getDimension().getName() + "\nUpdated By: " + detail.getLastUpdatedByName();
	}

	private String getBasicRecipeDetails(MonkRecipeDetail detail) {
		return "\nRecipe Name: " + detail.getName() + "\nProduct: " + detail.getProduct().getName() + "\nDimension: "
				+ detail.getDimension().getName() + "\nUpdated By: " + detail.getLastUpdatedByName();
	}
	
	public String getRecipeAddTemplate(RecipeDetail detail) {
		return "Recipe Added" + getBasicRecipeDetails(detail);
	}

	public String getRecipeRemoveTemplate(RecipeDetail detail) {
		return "Recipe Removed" + getBasicRecipeDetails(detail);
	}

	private void writeRecipe(Sheet sheet, Counter rowCount, RecipeCost detail) {
		List<String> errors = new ArrayList<>();
		if (detail.getErrorCodes() != null && detail.getErrorCodes().size() > 0) {
			errors.addAll(detail.getErrorCodes());
		}
		for (RecipeCategoryCost category : detail.getCategoryCost()) {
			createRow(sheet, rowCount, detail, category);
			if (category.getErrorCodes() != null && category.getErrorCodes().size() > 0) {
				errors.addAll(category.getErrorCodes());
			}
		}
		for (String error : errors) {
			if (error == null || error.trim().length() == 0) {
				continue;
			}
			rowCount.increment();
			Row aRow = sheet.createRow(rowCount.getC());
			aRow.createCell(1).setCellValue(error);
		}

	}

	private void writeRecipe(Sheet sheet, Counter rowCount, MonkRecipeDetail detail, int mode) {
		Row aRow = sheet.createRow(rowCount.increment().getC());
		for (MonkRecipeData data : detail.getDatas()) {
			aRow.createCell(1).setCellValue(detail.getProduct().getProductId());
			aRow.createCell(2).setCellValue(detail.getProduct().getName());
			aRow.createCell(3).setCellValue(detail.getDimension().getCode());
			aRow.createCell(4).setCellValue(detail.getName());
			aRow.createCell(5).setCellValue(detail.getPreparation());
			aRow.createCell(6).setCellValue(mode);
			aRow.createCell(7).setCellValue(data.getQuantity());
			aRow.createCell(8).setCellValue(data.getWater());
			aRow.createCell(9).setCellValue(data.getMilk());
			aRow.createCell(10).setCellValue(data.getBoilSettle());
			aRow.createCell(11).setCellValue(data.getNoOfBoils());
			aRow.createCell(12).setCellValue(data.getHeatingTimeMins());
			aRow.createCell(13).setCellValue(data.getHeatingTimeSecs());
		}
	}

	private void writeRecipe(Sheet sheet, Counter rowCount, RecipeDetail detail, MetaInfo scmProducts, MetaInfo menuProducts) {

		if (detail.getIngredient().getVariants() != null && detail.getIngredient().getVariants().size() > 0) {
			for (IngredientVariant variant : detail.getIngredient().getVariants()) {
				for (IngredientVariantDetail item : variant.getDetails()) {
					createRow(sheet, rowCount, detail, "VARIANTS", variant, item, scmProducts, menuProducts);
				}
			}
		}

		if (detail.getIngredient().getProducts() != null && detail.getIngredient().getProducts().size() > 0) {
			for (IngredientProduct variant : detail.getIngredient().getProducts()) {
				for (IngredientProductDetail item : variant.getDetails()) {
					createRow(sheet, rowCount, detail, "INGREDIENT_PRODUCTS", item, "SCM", scmProducts, menuProducts);
				}
			}
		}

		if (detail.getIngredient().getCompositeProduct() != null
				&& detail.getIngredient().getCompositeProduct().getDetails() != null
				&& detail.getIngredient().getCompositeProduct().getDetails().size() > 0) {
			for (CompositeIngredientData variant : detail.getIngredient().getCompositeProduct().getDetails()) {
				for (IngredientProductDetail item : variant.getMenuProducts()) {
					createRow(sheet, rowCount, detail, "COMPOSITE_PRODUCTS", variant, item, scmProducts, menuProducts);
				}
			}
		}

		if (detail.getIngredient().getComponents() != null && detail.getIngredient().getComponents().size() > 0) {
			for (IngredientProductDetail item : detail.getIngredient().getComponents()) {
				createRow(sheet, rowCount, detail, "INGREDIENT_COMPONENTS", item, "SCM", scmProducts, menuProducts);
			}
		}

		if (detail.getAddons() != null && detail.getAddons().size() > 0) {
			for (IngredientProductDetail item : detail.getAddons()) {
				createRow(sheet, rowCount, detail, "ADDONS", item, "MENU", scmProducts, menuProducts);
			}
		}
		if (detail.getDineInConsumables() != null && detail.getDineInConsumables().size() > 0) {
			for (IngredientProductDetail item : detail.getDineInConsumables()) {
				createRow(sheet, rowCount, detail, "DINE_IN_CONSUMABLES", item, "SCM", scmProducts, menuProducts);
			}
		}
		if (detail.getDeliveryConsumables() != null && detail.getDeliveryConsumables().size() > 0) {
			for (IngredientProductDetail item : detail.getDeliveryConsumables()) {
				createRow(sheet, rowCount, detail, "DELIVERY_CONSUMABLES", item, "SCM", scmProducts, menuProducts);
			}
		}
		if (detail.getTakeawayConsumables() != null && detail.getTakeawayConsumables().size() > 0) {
			for (IngredientProductDetail item : detail.getTakeawayConsumables()) {
				createRow(sheet, rowCount, detail, "TAKEAWAY_CONSUMABLES", item, "SCM", scmProducts, menuProducts);
			}
		}
	}

	private void writeInstructions(Sheet sheet, Counter rowCount, RecipeDetail detail,
			MetaInfo scmProducts) {
		Row aRow = sheet.createRow(rowCount.increment().getC());
		aRow.createCell(0).setCellValue(detail.getRecipeId());
		aRow.createCell(1).setCellValue(detail.getName());
		Integer productId = detail.getProduct().getProductId();
		aRow.createCell(2).setCellValue(productId);
		IdCodeName def = scmProducts.getData().get(productId);
		if (def != null) {
			aRow.createCell(3).setCellValue(def.getName());
			aRow.createCell(4).setCellValue(def.getStatus());
		} else {
			aRow.createCell(3).setCellValue(detail.getName());
			aRow.createCell(4).setCellValue("UNKNOWN");
		}

		aRow.createCell(5).setCellValue(detail.getDimension().getCode());
		aRow.createCell(6).setCellValue(detail.getNotes());
	}

	private void createRow(Sheet sheet, Counter rowCount, RecipeCost detail, RecipeCategoryCost category) {
		for (RecipeIngredientCost common : detail.getCommonIngredient()) {
			createRow(sheet, rowCount, category.getCostType().name(), "COMMON", detail, common);
		}
		for (RecipeIngredientCost common : category.getIngredients()) {
			createRow(sheet, rowCount, category.getCostType().name(), category.getCostType().name(), detail,
					common);
		}
		Row aRow = createCommon(sheet, rowCount, detail, category.getCostType().name(),
				category.getCostType().name());
		aRow.createCell(12).setCellValue("Total");
		aRow.createCell(13).setCellValue(category.getCost().floatValue());
	}

	private void createRow(Sheet sheet, Counter rowCount, String type, String ingredientType, RecipeCost detail,
			RecipeIngredientCost ingredient) {
		Row aRow = createCommon(sheet, rowCount.increment(), detail, type, ingredientType);
		aRow.createCell(7).setCellValue(ingredient.getType().name());
		aRow.createCell(8).setCellValue(ingredient.getProductId());
		aRow.createCell(9).setCellValue(ingredient.getProductName());
		aRow.createCell(10).setCellValue(ingredient.getUom() != null ? ingredient.getUom() : "NA");
		aRow.createCell(11).setCellValue(ingredient.getPrice() != null ? ingredient.getPrice().floatValue() : -1F);
		aRow.createCell(12).setCellValue(ingredient.getQuantity() != null ? ingredient.getQuantity().floatValue() : -1F);
		aRow.createCell(13).setCellValue(ingredient.getYield() != null ? ingredient.getYield().floatValue() : -1F);
		aRow.createCell(14).setCellValue(ingredient.getCost() != null ? ingredient.getCost().floatValue() : -1F);
	}

	private void createRow(Sheet sheet, Counter rowCount, RecipeDetail detail, String type,
			IngredientVariant variant, IngredientVariantDetail item, MetaInfo scmProducts, MetaInfo menuProducts) {
		Row aRow = createCommon(sheet, rowCount.increment(), detail, type, "SCM", scmProducts, menuProducts);
		Integer productId = variant.getProduct().getProductId();
		if (scmProducts.getData().containsKey(productId)) {
			IdCodeName product = scmProducts.getData().get(productId);
			aRow.createCell(8).setCellValue(product.getType());
			aRow.createCell(9).setCellValue(product.getId());
			aRow.createCell(10).setCellValue(product.getName());
			aRow.createCell(11).setCellValue(product.getStatus());

		} else {
			aRow.createCell(8).setCellValue("ERROR");
			aRow.createCell(9).setCellValue(variant.getProduct().getProductId());
			aRow.createCell(10).setCellValue(variant.getProduct().getName());
			aRow.createCell(11).setCellValue("ERROR");
		}
		aRow.createCell(12).setCellValue(item.getAlias());
		aRow.createCell(13).setCellValue("NA");
		aRow.createCell(14).setCellValue(item.getQuantity().floatValue());
		aRow.createCell(15).setCellValue(item.getYield().floatValue());
		aRow.createCell(16).setCellValue(item.getUom() != null ? item.getUom().name() : "NA");
		aRow.createCell(17).setCellValue(variant.isCritical());
		aRow.createCell(18).setCellValue(variant.isCustomize());
		aRow.createCell(19).setCellValue(detail.getProfile());
		aRow.createCell(20).setCellValue(detail.getCreationDate() != null ? AppUtils.dateToString(detail.getCreationDate()): "");
		aRow.createCell(21).setCellValue(detail.getStatus());
		aRow.createCell(22).setCellValue(detail.getDimension().getDesc()!=null ? detail.getDimension().getDesc():"NA");
		aRow.createCell(23).setCellValue(detail.isDeliverable());
		aRow.createCell(24).setCellValue(item.getTag() !=null ? item.getTag():"NA");
		aRow.createCell(25).setCellValue(detail.getDispensed()!=null ? "TRUE":"FALSE" );
		aRow.createCell(26).setCellValue(
				detail.getStartDate() != null ? AppUtils.getDateString(detail.getStartDate()) : "9999-12-01");

	}

	private Row createCommon(Sheet sheet, Counter rowCount, RecipeCost detail, String type,
			String ingredientType) {
		Row aRow = sheet.createRow(rowCount.getC());
		aRow.createCell(0).setCellValue(type);
		aRow.createCell(1).setCellValue(detail.getRecipeId());
		aRow.createCell(2).setCellValue(detail.getRecipeName());
		aRow.createCell(3).setCellValue(detail.getProductId());
		aRow.createCell(4).setCellValue(detail.getProductName());
		aRow.createCell(5).setCellValue(detail.getDimension());
		aRow.createCell(6).setCellValue(ingredientType);
		return aRow;
	}

	private Row createCommon(Sheet sheet, Counter rowCount, RecipeDetail detail, String type, String source, MetaInfo scmProducts, MetaInfo menuProducts) {
		Row aRow = sheet.createRow(rowCount.getC());
		aRow.createCell(0).setCellValue(detail.getRecipeId());
		aRow.createCell(1).setCellValue(detail.getName());
		Integer productId = detail.getProduct().getProductId();
		aRow.createCell(2).setCellValue(productId);
		if(ProductClassification.SCM_PRODUCT.equals(detail.getProduct().getClassification())){
			IdCodeName def = scmProducts.getData().get(productId);
			if(def != null ) {
				aRow.createCell(3).setCellValue(def.getName());
				aRow.createCell(4).setCellValue(def.getStatus());
			}else {
				aRow.createCell(3).setCellValue(detail.getName());
				aRow.createCell(4).setCellValue("UNKNOWN");
			}

		}else {
			IdCodeName def = menuProducts.getData().get(productId);
			if(def != null ) {
				aRow.createCell(3).setCellValue(def.getName());
				aRow.createCell(4).setCellValue(def.getStatus());

			}else {
				aRow.createCell(3).setCellValue(detail.getName());
				aRow.createCell(4).setCellValue("UNKNOWN");
			}
		}
		aRow.createCell(5).setCellValue(detail.getDimension().getCode());
		aRow.createCell(6).setCellValue(type);
		aRow.createCell(7).setCellValue(source);
		return aRow;
	}

	private void createRow(Sheet sheet, Counter rowCount, RecipeDetail detail, String type,
			IngredientProductDetail item, String source, MetaInfo scmProducts, MetaInfo menuProducts) {
		Row aRow = createCommon(sheet, rowCount.increment(), detail, type, source, scmProducts, menuProducts);
		Integer productId = item.getProduct().getProductId();
		if ("SCM".equals(source)) {
			if (scmProducts.getData().containsKey(productId)) {
				IdCodeName product = scmProducts.getData().get(productId);
				aRow.createCell(8).setCellValue(product.getType());
				aRow.createCell(9).setCellValue(product.getId());
				aRow.createCell(10).setCellValue(product.getName());
				aRow.createCell(11).setCellValue(product.getStatus());

			} else {
				aRow.createCell(8).setCellValue("ERROR");
				aRow.createCell(9).setCellValue(item.getProduct().getProductId());
				aRow.createCell(10).setCellValue(item.getProduct().getName());
				aRow.createCell(11).setCellValue("ERROR");
			}
		} else {
			if (menuProducts.getData().containsKey(productId)) {
				IdCodeName product = menuProducts.getData().get(productId);
				aRow.createCell(8).setCellValue(product.getType());
				aRow.createCell(9).setCellValue(product.getId());
				aRow.createCell(10).setCellValue(product.getName());
				aRow.createCell(11).setCellValue(product.getStatus());

			} else {
				aRow.createCell(8).setCellValue("ERROR");
				aRow.createCell(9).setCellValue(item.getProduct().getProductId());
				aRow.createCell(10).setCellValue(item.getProduct().getName());
				aRow.createCell(11).setCellValue("ERROR");
			}

		}
		aRow.createCell(12).setCellValue("NA");
		aRow.createCell(13).setCellValue(item.getDimension() != null ? item.getDimension().getCode() : "NA");
		aRow.createCell(14).setCellValue(item.getQuantity().floatValue());
		aRow.createCell(15).setCellValue(item.getYield().floatValue());
		aRow.createCell(16).setCellValue(item.getUom() != null ? item.getUom().name() : "NA");
		aRow.createCell(17).setCellValue(item.isCritical());
		aRow.createCell(18).setCellValue(item.isCustomize());
		aRow.createCell(19).setCellValue(detail.getProfile());
		aRow.createCell(20)
				.setCellValue(detail.getCreationDate() != null ? AppUtils.dateToString(detail.getCreationDate()) : "");
		aRow.createCell(21).setCellValue(detail.getStatus());
		aRow.createCell(22)
				.setCellValue(detail.getDimension().getDesc() != null ? detail.getDimension().getDesc() : "NA");
		aRow.createCell(23).setCellValue(detail.isDeliverable());
//		aRow.createCell(20).setCellValue(item.getTag());
//		aRow.createCell(21).setCellValue(detail.getDispensed());

		aRow.createCell(24).setCellValue(item.getTag() != null ? item.getTag() : "NA");
		aRow.createCell(25).setCellValue(detail.getDispensed() != null ? "TRUE" : "FALSE");
		aRow.createCell(26).setCellValue(
				detail.getStartDate() != null ? AppUtils.getDateString(detail.getStartDate()) : "9999-12-01");

	}

	private void createRow(Sheet sheet, Counter rowCount, RecipeDetail detail, String type,
			CompositeIngredientData variant, IngredientProductDetail item, MetaInfo scmProducts, MetaInfo menuProducts) {
		Row aRow = createCommon(sheet, rowCount.increment(), detail, type, "MENU", scmProducts, menuProducts);
		IdCodeName def = menuProducts.getData().get(item.getProduct().getProductId());
		if(def != null ) {
			aRow.createCell(8).setCellValue(def.getType());
			aRow.createCell(9).setCellValue(def.getId());
			aRow.createCell(10).setCellValue(def.getName());
			aRow.createCell(11).setCellValue(def.getStatus());


		}else {
			aRow.createCell(8).setCellValue("MENU");
			aRow.createCell(9).setCellValue(item.getProduct().getProductId());
			aRow.createCell(10).setCellValue(item.getProduct().getName());
			aRow.createCell(11).setCellValue("UNKNOWN");

		}


		aRow.createCell(12).setCellValue(variant.getName());
		aRow.createCell(13).setCellValue(item.getDimension().getCode() != null ? item.getDimension().getCode() : "NA");
		aRow.createCell(14).setCellValue(item.getQuantity().floatValue());
		aRow.createCell(15).setCellValue(item.getYield().floatValue());
		aRow.createCell(16).setCellValue(item.getUom() != null ? item.getUom().name() : "NA");
		aRow.createCell(17).setCellValue(item.isCritical());
		aRow.createCell(18).setCellValue(item.isCustomize());
		aRow.createCell(19).setCellValue(detail.getProfile());
		aRow.createCell(20).setCellValue(detail.getCreationDate() != null ? AppUtils.dateToString(detail.getCreationDate()): "");
		aRow.createCell(21).setCellValue(detail.getStatus());
		aRow.createCell(22).setCellValue(detail.getDimension().getDesc()!=null ? detail.getDimension().getDesc():"NA");
		aRow.createCell(23).setCellValue(detail.isDeliverable());
//		aRow.createCell(20).setCellValue(item.getTag());
//		aRow.createCell(21).setCellValue(detail.getDispensed());
		aRow.createCell(24).setCellValue(item.getTag() !=null ? item.getTag():"NA");
		aRow.createCell(25).setCellValue(detail.getDispensed()!=null ?"TRUE":"FALSE" );
		aRow.createCell(26).setCellValue(
				detail.getStartDate() != null ? AppUtils.getDateString(detail.getStartDate()) : "9999-12-01");

	}

	private Sheet generateRecipeSheet(Workbook workbook, Sheet sheet) {
		sheet.setDefaultColumnWidth(20);

		CellStyle style = generateHeaderStyle(workbook);
		Row header = sheet.createRow(0);
		createHeader(header, 0, "Type", style);
		createHeader(header, 1, "Recipe Id", style);
		createHeader(header, 2, "Recipe Name", style);
		createHeader(header, 3, "Product Id", style);
		createHeader(header, 4, "Product Name", style);
		createHeader(header, 5, "Dimension", style);
		createHeader(header, 6, "Component Type", style);
		createHeader(header, 7, "Ingredient Type", style);
		createHeader(header, 8, "Product Id", style);
		createHeader(header, 9, "Product Name", style);
		createHeader(header, 10, "UoM", style);
		createHeader(header, 11, "Price", style);
		createHeader(header, 12, "Quanity", style);
		createHeader(header, 13, "Yield", style);
		createHeader(header, 14, "Cost", style);
		return sheet;
	}

	private Sheet generateInstructionSheet(Workbook workbook, Sheet sheet) {
		sheet.setDefaultColumnWidth(22);

		CellStyle style = generateHeaderStyle(workbook);
		Row header = sheet.createRow(0);
		createHeader(header, 0, "Recipe Id", style);
		createHeader(header, 1, "Recipe Name", style);
		createHeader(header, 2, "Parent Product Id", style);
		createHeader(header, 3, "Parent Product Name", style);
		createHeader(header, 4, "Parent Product Status", style);
		createHeader(header, 5, "Dimension", style);
		createHeader(header, 6, "Instructions", style);
		return sheet;
	}

	private Sheet generateSheet(Workbook workbook, Sheet sheet) {
		sheet.setDefaultColumnWidth(22);

		CellStyle style = generateHeaderStyle(workbook);
		Row header = sheet.createRow(0);
		createHeader(header, 0, "Recipe Id", style);
		createHeader(header, 1, "Recipe Name", style);
		createHeader(header, 2, "Parent Product Id", style);
		createHeader(header, 3, "Parent Product Name", style);
		createHeader(header, 4, "Parent Product Status", style);
		createHeader(header, 5, "Dimension", style);
		createHeader(header, 6, "Ingredient Type", style);
		createHeader(header, 7, "Product Source", style);
		createHeader(header, 8, "Product Category", style);
		createHeader(header, 9, "Product Id", style);
		createHeader(header, 10, "Product Name", style);
		createHeader(header, 11, "Product Status", style);
		createHeader(header, 12, "Alias Name", style);
		createHeader(header, 13, "Dimension", style);
		createHeader(header, 14, "Quanity", style);
		createHeader(header, 15, "Yield", style);
		createHeader(header, 16, "UoM", style);
		createHeader(header, 17, "Critical", style);
		createHeader(header, 18, "Customizable", style);
		createHeader(header, 19, "Profile", style);
		createHeader(header, 20, "Creation Date", style);
		createHeader(header, 21, "Recipe Status", style);
		createHeader(header,22,"Description",style);
		createHeader(header,23,"IsDeliverable",style);
		createHeader(header,24,"Tag",style);
		createHeader(header,25,"Dispensed",style);
		createHeader(header,26,"Start Date",style);
		return sheet;
	}


	private Sheet generateMonkRecipeSheet(Workbook workbook, Sheet sheet) {
		sheet.setDefaultColumnWidth(22);

		CellStyle style = generateHeaderStyle(workbook);
		Row header = sheet.createRow(0);
		createHeader(header, 0, "Recipe Id", style);
		createHeader(header, 1, "Recipe Name", style);
		createHeader(header, 2, "Parent Product Id", style);
		createHeader(header, 3, "Dimension", style);
		createHeader(header, 4, "Ingredient Type", style);
		createHeader(header, 5, "Product Source", style);
		createHeader(header, 6, "Product Id", style);
		createHeader(header, 7, "Product Name", style);
		createHeader(header, 8, "Alias Name", style);
		createHeader(header, 9, "Dimension", style);
		createHeader(header, 10, "Quanity", style);
		createHeader(header, 11, "Yield", style);
		createHeader(header, 12, "UoM", style);
		createHeader(header, 13, "Critical", style);
		createHeader(header, 14, "Customizable", style);
		createHeader(header, 15, "Profile", style);
		createHeader(header, 16, "Creation Date", style);
		createHeader(header, 17, "Recipe Status", style);
		createHeader(header,18,"Description",style);
		createHeader(header,19,"IsDeliverable",style);
		createHeader(header,20,"Tag",style);
		createHeader(header,21,"Dispensed",style);
		return sheet;
	}

	private void createHeader(Row header, int number, String name, CellStyle style) {
		header.createCell(number).setCellValue(name);
		header.getCell(number).setCellStyle(style);
	}

	private static CellStyle generateHeaderStyle(Workbook workbook) {
		CellStyle style = workbook.createCellStyle();
		style.setFillForegroundColor(HSSFColorPredefined.BLACK.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		Font font = workbook.createFont();
		font.setFontName("Arial");
		font.setBold(true);
		font.setColor(HSSFColorPredefined.WHITE.getIndex());
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setFont(font);
		return style;
	}

	/**
	 * @param detail
	 * @return
	 */
	public String getMonkRecipeAddTemplate(MonkRecipeDetail detail) {
		return "Monk Recipe Added" + getBasicRecipeDetails(detail);
	}

	/**
	 * @param detail
	 * @return
	 */
	public String getMonkRecipeUpdateTemplate(MonkRecipeDetail detail) {
		return "Monk Recipe Updated" + getBasicRecipeDetails(detail);
	}

	/**
	 * @param detail
	 * @return
	 */
	public String getMonkRecipeRemoveTemplate(MonkRecipeDetail detail) {
		return "Monk Recipe Removed" + getBasicRecipeDetails(detail);
	}
	
	public static int getDimensionCode(String dimension) {
		if (dimension.equals("Regular")) {
			return 1;
		}
		if (dimension.equals("Full")) {
			return 2;
		}
		if (dimension.equals("ChotiKetli")) {
			return 3;
		}
		if (dimension.equals("BadiKetli")) {
			return 4;
		}
		if (dimension.equals("None")) {
			return 5;
		}

		return -1;
	}

	public String getRecipeApproveTemplate(RecipeDetail recipeDetail, Boolean sentForApproval) {
		if (sentForApproval) {
			return "Recipe Sent For Approval" + getBasicRecipeDetails(recipeDetail);
		} else {
			return "Recipe Approved" + getBasicRecipeDetails(recipeDetail);
		}
	}

	public String getRecipeRejectTemplate(RecipeDetail recipeDetail, Boolean sentForRejection) {
		if (sentForRejection) {
			return "Recipe Rejected" + getBasicRecipeDetails(recipeDetail);
		} else {
			return "Cancelled Request For Approval" + getBasicRecipeDetails(recipeDetail);
		}
	}
}
