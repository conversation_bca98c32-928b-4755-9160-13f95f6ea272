/*
 * Created By Shanmukh
 */

package com.stpl.tech.master.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "USER_POLICY_ROLE_MAPPING")
public class UserPolicyRoleMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "USER_POLICY_ROLE_MAPPING_ID", unique = true, nullable = false)
    private Integer userPolicyRoleMappingId;

    @Column(name = "USER_POLICY_ID")
    private Integer userPolicyId;

    @Column(name = "ROLE_ID")
    private Integer roleId;

    @Column(name = "MAPPING_STATUS")
    private String mappingStatus;

    public Integer getUserPolicyRoleMappingId() {
        return this.userPolicyRoleMappingId;
    }

    public void setUserPolicyRoleMappingId(Integer userPolicyRoleMappingId) {
        this.userPolicyRoleMappingId = userPolicyRoleMappingId;
    }

    public Integer getUserPolicyId() {
        return this.userPolicyId;
    }

    public void setUserPolicyId(Integer userPolicyId) {
        this.userPolicyId = userPolicyId;
    }

    public Integer getRoleId() {
        return this.roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getMappingStatus() {
        return mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }
}
