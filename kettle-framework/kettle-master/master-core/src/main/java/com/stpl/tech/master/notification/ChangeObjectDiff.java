package com.stpl.tech.master.notification;

import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Objects;

public class ChangeObjectDiff extends EmailNotification {
    private String className;
    private EnvType envType;
    private String employeeName;
    private List<String> toEmails;
    private String placeHolder;
    private ChangesDiffNotificationTemplate template;

    public ChangeObjectDiff(String className, EnvType envType, String employeeName, List<String> toEmails,
                            ChangesDiffNotificationTemplate template , String placeHolder) {
        this.className = className;
        this.envType = envType;
        this.employeeName = employeeName;
        this.toEmails = toEmails;
        this.template = template;
        this.placeHolder = placeHolder;
    }

    @Override
    public String[] getToEmails() {
        if (AppUtils.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            if(Objects.nonNull(toEmails) && toEmails.size()>0){
                toEmails.add("<EMAIL>");
            }
            return this.toEmails.toArray(new String[0]);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = placeHolder + " has Been Updated By " + employeeName  + " on " +
                AppUtils.getTimeISTString(AppUtils.getCurrentTimestamp());
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
