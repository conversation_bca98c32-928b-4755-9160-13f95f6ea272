package com.stpl.tech.master.core.service;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.sql.DataSource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.kettle.report.metadata.model.ReportCategory;
import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.kettle.report.metadata.model.ReportNotification;
import com.stpl.tech.kettle.report.metadata.model.ReportNotificationType;
import com.stpl.tech.kettle.report.metadata.model.ReportOutput;
import com.stpl.tech.kettle.report.metadata.model.ReportParam;
import com.stpl.tech.kettle.report.metadata.model.UserType;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.notification.GenericNotification;
import com.stpl.tech.master.notification.GenericNotificationTemplate;
import com.stpl.tech.master.notification.GenericReportEmail;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.master.util.QueryExecutorForList;
import com.stpl.tech.master.util.ServiceUtil;
import com.stpl.tech.master.util.UpdateQueryExecutor;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.ExecutionEnvironment;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.util.notification.ReportDetailData;

public abstract class AbstractAutomatedReports extends AbstractResources {

	private final int THRESHOLD_FOR_MESSAGING = 75;
	private static final Logger LOG = LoggerFactory.getLogger(AbstractAutomatedReports.class);

	public void sendGenericNotification(GenericNotification mailNotification)
			throws IOException, EmailGenerationException {
		GenericNotificationTemplate template = new GenericNotificationTemplate(mailNotification);
		GenericReportEmail email = new GenericReportEmail(template, getEnvironmentType());
		if (mailNotification.isAttachFile()) {
			List<AttachmentData> attachments = null;
			if (mailNotification.isNeedsCompression()) {
				attachments = new ArrayList<>();
				attachments.add(MasterUtil.compress(mailNotification.getSubject(), getBasePath() + "/tmp/",
						mailNotification.getAttachmentData()));
			} else {
				attachments = mailNotification.getAttachmentData();
			}
			email.sendRawMail(attachments);
		} else {
			email.sendEmail();
		}
	}

	public List<GenericNotification> executeReports(ReportCategories reportCategories, String executionTime)
			throws ParseException, EmailGenerationException, IOException {
		return executeReports(reportCategories, executionTime, false);
	}

	public List<GenericNotification> executeReports(ReportCategories reportCategories, String executionTime,
			boolean skipHeader) throws ParseException, EmailGenerationException, IOException {
		return executeReports(null, reportCategories, executionTime, skipHeader, null, null);
	}

	public List<GenericNotification> executeReports(String subject, ReportCategories reportCategories,
			String executionTime, boolean skipHeader, String[] emails, Map<String, List<Object>> params)
			throws ParseException, EmailGenerationException, IOException {

		return executeReports(subject, reportCategories, executionTime, skipHeader, emails, params, false);
	}

	public List<GenericNotification> executeReports(String subject, ReportCategories reportCategories,
			String executionTime, boolean skipHeader, String[] emails, Map<String, List<Object>> params, Boolean write)
			throws ParseException, EmailGenerationException, IOException {
		List<GenericNotification> notifications = new ArrayList<>();
		for (ReportCategory category : reportCategories.getCategory()) {
			LOG.info("Running automated report: {}", category.getName());
			GenericNotification mailNotification = createNotification(subject, category, executionTime, emails);
			for (ReportData report : category.getReport()) {
                if(report.getEnvironment() == null || report.getEnvironment().trim().length() == 0) {
					report.setEnvironment(getEnvironmentType().getExecutionEnvironment().name());
				}
				setParams(report, params);
				DataSource dataSource = ServiceUtil
						.getDataSourceBean(ExecutionEnvironment.valueOf(report.getEnvironment()), write);
				if (report.getUpdate() == null || !report.getUpdate()) {
					QueryExecutorForList executor = new QueryExecutorForList();
					executor.execute(report, dataSource);
					List<List<String>> data = executor.getData();
					Set<Integer> skipColumns = sendNotifications(mailNotification.getSubject(), report, data, category,
							executionTime, skipHeader);
					LOG.info("returned size: " + data.size() + "returned data::::::: ");
					mailNotification.addReportData(new ReportDetailData(report.getName(), data,
							report.isSkipInline() == null || !report.isSkipInline(), skipColumns), skipHeader);
				} else {
					UpdateQueryExecutor executor = new UpdateQueryExecutor();
					int updateCount = executor.execute(report, dataSource);
					LOG.info(report.getName() + " : update count: " + updateCount);
					List<List<String>> data = new ArrayList<>();
					data.add(Arrays.asList("Update Count"));
					data.add(Arrays.asList(updateCount + ""));
					mailNotification.addReportData(new ReportDetailData(report.getName(), data, true, new HashSet<>()),
							skipHeader);
				}

			}
			sendGenericNotification(mailNotification);
			notifications.add(mailNotification);
		}
		return notifications;
	}

	/**
	 * @param report
	 * @param params
	 */
	protected void setParams(ReportData report, Map<String, List<Object>> params) {
		StringBuffer error = new StringBuffer();
		if (report.getParam() != null && report.getParam().size() > 0) {
			for (ReportParam param : report.getParam()) {
				if (params != null && params.containsKey(param.getName())) {
					List<Object> values = params.get(param.getName());
					StringBuffer buffer = new StringBuffer();
					for (int i = 0; i < values.size(); i++) {
						buffer.append(values.get(i));
						if (i < values.size() - 1) {
							buffer.append(",");
						}
					}
					param.setValue(buffer.toString());
					param.setDelimiter(",");
					param.setMultiValued(values.size() > 1 ? true : false);
				} else {
					error.append("Did Not Find Param : " + param.getName() + " in the jobs Param");
				}
			}
		}
	}

	private Set<Integer> sendNotifications(String subject, ReportData report, List<List<String>> data,
			ReportCategory category, String executionTime, boolean skipHeader)
			throws IOException, EmailGenerationException {
		Set<Integer> skipColumns = new HashSet<>();
		if (report.getNotification() == null || report.getNotification().size() == 0) {
			LOG.info("No Notifications to be sent for report " + report.getName());
			return skipColumns;
		}
		if (data.size() <= 1 || data.size() > THRESHOLD_FOR_MESSAGING) {
			return skipColumns;
		}
		for (ReportNotification notification : report.getNotification()) {
			if (notification.getNotificationIdIndex() != null && notification.getNotificationIdIndex() >= 0) {
				skipColumns.add(notification.getNotificationIdIndex());
			}
			if (notification.getType().equals(ReportNotificationType.SLACK)) {
				sendSlackMessages(subject, notification, data);
			} else if (notification.getType().equals(ReportNotificationType.EMAIL)) {
				sendEmailMessages(subject, category, report, notification, data, skipColumns, executionTime,
						skipHeader);
			}
		}
		return skipColumns;
	}

	private void sendSlackMessages(String subject, ReportNotification notification, List<List<String>> data) {

		List<String> headers = data.get(0);
		List<String> messages = new ArrayList<>();
		for (int i = 1; i < data.size(); i++) {
			List<String> detail = data.get(i);
			StringBuffer buffer = new StringBuffer();
			buffer.append(subject).append("\n");
			for (int index : notification.getMessageIndex()) {
				buffer.append(headers.get(index)).append(" : ").append(detail.get(index)).append("\n");
			}
			String message = buffer.toString();
			messages.add(message);
			if (notification.getNotificationIdIndex() != null && notification.getNotificationIdIndex() >= 0) {
				sendSlackNotifications(notification.getNotificationType(),
						detail.get(notification.getNotificationIdIndex()), message);
			}
		}
		if (notification.getChannel() != null && !notification.getChannel().trim().equals("")) {
			String[] channels = notification.getChannel().split(",");
			String message = StringUtils.join(messages, ",");
			for (String channel : channels) {
				sendSlackNotifications(UserType.CHANNEL, channel, message);
			}
		}
	}

	private void sendEmailMessages(String subject, ReportCategory category, ReportData report,
			ReportNotification notification, List<List<String>> data, Set<Integer> skipColumns, String executionTime,
			boolean skipHeader) throws IOException, EmailGenerationException {

		List<String> headers = data.get(0);
		List<String> finalHeader = new ArrayList<>();
		List<GenericNotification> notifications = new ArrayList<>();
		for (int index : notification.getMessageIndex()) {
			finalHeader.add(headers.get(index));
		}
		for (int i = 1; i < data.size(); i++) {
			List<List<String>> inputs = new ArrayList<>();
			inputs.add(finalHeader);
			List<String> detail = data.get(i);
			List<String> finalData = new ArrayList<>();
			String toEmails = null;
			String subjectPrefix = null;
			if (notification.getNotificationIdIndex() != null && notification.getNotificationIdIndex() >= 0) {
				toEmails = detail.get(notification.getNotificationIdIndex());
			}
			if (notification.getSubjectPrefixId() != null && notification.getSubjectPrefixId() >= 0) {
				subjectPrefix = detail.get(notification.getSubjectPrefixId());
			}
			for (int index : notification.getMessageIndex()) {
				finalData.add(detail.get(index));
			}
			inputs.add(finalData);
			GenericNotification mailNotification = createNotification(category, report, executionTime, subjectPrefix,
					toEmails);
			mailNotification.addReportData(new ReportDetailData(report.getName(), inputs,
					report.isSkipInline() == null || !report.isSkipInline(), skipColumns), skipHeader);
			notifications.add(mailNotification);
		}
		for (GenericNotification notify : notifications) {
			sendGenericNotification(notify);
		}
	}

	private void sendSlackNotifications(UserType type, String channel, String text) {
		if (type == null || type.equals(UserType.CHANNEL)) {
			SlackNotificationService.getInstance().sendNotification(getEnvironmentType(), "Kettle", null,
					channel.trim(), text);
		} else if (type.equals(UserType.DIRECT)) {
			SlackNotificationService.getInstance().sendNotification(getEnvironmentType(), "Kettle", channel.trim(),
					text);
		}
	}

	private GenericNotification createNotification(ReportCategory category, ReportData report, String executionTime,
			String subjectPrefix, String toEmail) {
		GenericNotification notification = new GenericNotification();
		notification.setSubject(subjectPrefix + " " + category.getName() + " for " + executionTime);
		notification.setSchedule(category.getSchedule());
		notification.setFromEmail("<EMAIL>");
		ArrayList<String> emails = new ArrayList<>();
		String[] to = toEmail.split(",");
		for (String email : to) {
			emails.add(email);
		}
		to = category.getToEmails().split(",");
		for (String email : to) {
			emails.add(email);
		}
		String[] toEmails = new String[emails.size()];
		notification.setToEmails(emails.toArray(toEmails));
		notification.setOutputType(ReportOutput.INLINE);
		notification.setNeedsCompression(false);
		notification.setAttachFile(false);
		return notification;
	}

	protected GenericNotification createNotification(String subject, ReportCategory category, String executionTime,
			String[] emails) {
		GenericNotification notification = new GenericNotification();
		notification.setSubject(
				subject == null ? category.getName() + " for " + executionTime : subject + " for " + executionTime);
		notification.setSchedule(category.getSchedule());
		notification.setFromEmail(category.getFromEmail());
		List<String> toEmails = new ArrayList<>();
		if (emails != null && emails.length > 0) {
			for (String email : emails) {
				toEmails.add(email);
			}
		}
		if (category.getToEmails() != null && category.getToEmails().trim().length() > 0) {
			String[] additionals = category.getToEmails().split(",");
			for (String email : additionals) {
				toEmails.add(email);
			}

		}
		notification.setToEmails(toEmails.toArray(new String[toEmails.size()]));
		notification.setOutputType(
				category.getAttachmentType() == null ? ReportOutput.INLINE : category.getAttachmentType());
		notification.setNeedsCompression(category.isCompress() == null ? false : category.isCompress());
		notification.setAttachFile(category.getAttachmentType() != null);
		notification.setForceEmail(category.getForceEmail());
		return notification;
	}

	public abstract EnvType getEnvironmentType();

	public abstract String getBasePath();

}
