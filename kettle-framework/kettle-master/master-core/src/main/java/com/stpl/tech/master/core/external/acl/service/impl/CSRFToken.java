//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.25 at 12:25:34 AM IST 
//

package com.stpl.tech.master.core.external.acl.service.impl;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.core.external.acl.service.TokenDao;

import io.jsonwebtoken.Claims;

public class CSRFToken implements TokenDao {

	protected String url;
	protected String timestamp;

	public CSRFToken() {

	}

	public CSRFToken(String url, String timestamp) {
		super();
		this.url = url;
		this.timestamp = timestamp;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public Map<String, Object> createClaims() {
		// Setting JWT Claims
		Map<String, Object> authClaims = new HashMap<String, Object>();
		authClaims.put("url", this.getUrl());
		authClaims.put("timestamp", this.getTimestamp());
		return authClaims;
	}

	public void parseClaims(Claims claims) {
		this.url = claims.get("url", String.class);
		this.timestamp = claims.get("timestamp", String.class);
	}

}
