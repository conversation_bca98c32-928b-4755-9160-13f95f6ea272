package com.stpl.tech.master.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "ROLE_BRAND_MAPPING")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class RoleBrandMapping implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ROLE_BRAND_MAPPING_ID")
    private Integer roleBrandMappingId;

    @Column(name = "ROLE_ID", nullable = false)
    private Integer roleId;

    @Column(name = "BRAND_ID", nullable = false)
    private Integer brandId;

    @Column(name = "STATUS", nullable = false)
    private String status;

}
