/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.external.cache;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.recipe.model.CondimentsData;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Service
public class RecipeCache {

	private static final Logger LOG = LoggerFactory.getLogger(RecipeCache.class);

	@Autowired
	@Qualifier(value = "MasterHazelCastInstance")
	private HazelcastInstance instance;

	private IMap<ProductRecipeKey, RecipeDetail> recipes;

	private MultiMap<ProductRecipeKey, Integer> criticalProducts;

	private MultiMap<ProductRecipeKey, IngredientProductDetail> mandatoryAddons;

//	private IMap<Integer, RecipeDetail> scmRecipes;

	private IMap<ProductRecipeKey, RecipeDetail> scmRecipes;

	private IMap<Integer, RecipeDetail> recipeMap;

	private IMap<Integer, Map<Integer, Map<String, Pair<String, Integer>>>> unitProductProfileDetails;

	private  IMap<Integer,Map<String,CondimentsData>> recipeToSrcGroupCondiment;

	private IMap<Integer,Pair<String, String>> unitMonkRecipeProfileVersion;

	@PostConstruct
	public void createCache() {
    	LOG.info("POST-CONSTRUCT RecipeCache - STARTED");
		LOG.info("$$$$$$$$$$$$$$$Creating Recipe Cache$$$$$$$$$$$$$$$");
		recipes = instance.getMap("MasterDataCache:recipes");
		criticalProducts = instance.getMultiMap("MasterDataCache:criticalProducts");
		mandatoryAddons = instance.getMultiMap("MasterDataCache:mandatoryAddons");
		scmRecipes = instance.getMap("MasterDataCache:scmRecipes");
		recipeMap = instance.getMap("MasterDataCache:recipeMap");
		unitProductProfileDetails = instance.getMap("MasterDataCache:unitProductProfileDetails");
		recipeToSrcGroupCondiment = instance.getMap("MasterDataCache:recipeToSrcGroupCondiment");
		unitMonkRecipeProfileVersion = instance.getMap("MasterDataCache:unitMonkRecipeProfileVersion");
	}

	public void clearCache() {
		LOG.info("$$$$$$$$$$$$$$$Clearing Recipe Cache$$$$$$$$$$$$$$$");
		instance.getMap("MasterDataCache:recipes").clear();
		instance.getMultiMap("MasterDataCache:criticalProducts").clear();
		instance.getMultiMap("MasterDataCache:mandatoryAddons").clear();
		instance.getMap("MasterDataCache:scmRecipes").clear();
		instance.getMap("MasterDataCache:recipeMap").clear();
		instance.getMap("MasterDataCache:unitProductProfileDetails").clear();
		instance.getMap("MasterDataCache:recipeToSrcGroupCondiment").clear();
		instance.getMap("MasterDataCache:unitMonkRecipeProfileVersion").clear();
	}

	public void removeRecipeCache() {
		LOG.info("$$$$$$$$$$$$$$$Removing Recipe Cache$$$$$$$$$$$$$$$");
		instance.getMap("MasterDataCache:recipes").clear();
		instance.getMultiMap("MasterDataCache:criticalProducts").clear();
		instance.getMap("MasterDataCache:scmRecipes").clear();
		instance.getMap("MasterDataCache:recipeMap").clear();
		instance.getMap("MasterDataCache:recipeToSrcGroupCondiment").clear();
		instance.getMap("MasterDataCache:unitMonkRecipeProfileVersion").clear();
	}

	public RecipeCache() {

	}

	public RecipeDetail getRecipe(int productId, String dimension, String profile) {
		return recipes.get(new ProductRecipeKey(productId, dimension, profile));
	}

	public RecipeDetail getRecipe(ProductRecipeKey recipe) {
		return recipes.get(recipe);
	}

//	public RecipeDetail getScmRecipe(Integer productId) {
//		return scmRecipes.get(productId);
//	}

	public RecipeDetail getScmRecipe(ProductRecipeKey recipe) {
		return scmRecipes.get(recipe);
	}

	public Map<ProductRecipeKey, RecipeDetail> getRecipes() {
		return recipes;
	}

	public Set<Integer> getCriticalProducts(int productId, String dimension, String profile) {
		Collection<Integer> list = criticalProducts.get(new ProductRecipeKey(productId, dimension, profile));
		return list == null ? new HashSet<>() : new HashSet<>(list);
	}


	public Collection<IngredientProductDetail> getMandatoryAddons(int productId, String dimension, String profile) {
		return mandatoryAddons.get(new ProductRecipeKey(productId, dimension, profile));
	}

	public MultiMap<ProductRecipeKey,IngredientProductDetail> getMandatoryAddons() {
		return mandatoryAddons;
	}
	public MultiMap<ProductRecipeKey, Integer> getCriticalProducts() {
		return criticalProducts;
	}

//	public Map<Integer, RecipeDetail> getScmRecipes() {
//		return scmRecipes;
//	}

	public Map<ProductRecipeKey, RecipeDetail> getScmRecipes() {
		return scmRecipes;
	}

	public RecipeDetail getRecipe(Integer recipeId) {
		return recipeMap.get(recipeId);
	}

	public Map<Integer, RecipeDetail> getRecipeMap() {
		return recipeMap;
	}

	public IMap<Integer, Map<Integer, Map<String, Pair<String, Integer>>>> getAllProductProfileDetails() {
		return unitProductProfileDetails;
	}

	public Map<Integer, Map<String, Pair<String, Integer>>> getUnitProductProfileDetails(int unitId) {
		return unitProductProfileDetails.get(unitId);
	}

	public IMap<Integer, Map<String, CondimentsData>> getRecipeToSrcGroupCondiment() {
		return recipeToSrcGroupCondiment;
	}

	public Map<String, CondimentsData> getRecipeToSrcGroupCondiment(Integer id){
		return recipeToSrcGroupCondiment.get(id) !=null? recipeToSrcGroupCondiment.get(id):null;
	}

	public String getUnitProductProfile(int unitId, int productId, String dimension) {
		/*
		 * LOG.info("Unit Id : " + unitId + " Product Id :" + productId + " Dimension "
		 * + dimension); LOG.info("Product Status " +
		 * unitProductProfileDetails.get(unitId).get(productId));
		 * LOG.info("Dimension Status " +
		 * unitProductProfileDetails.get(unitId).get(productId).get(dimension));
		 */
		try {
			return unitProductProfileDetails.get(unitId).get(productId).get(dimension).getKey();
		} catch (Exception e) {
			return AppConstants.DEFAULT_RECIPE_PROFILE;
		}
	}

	public Integer getUnitProductRecipeId(int unitId, int productId, String dimension) {
		return unitProductProfileDetails.get(unitId) != null
				&& unitProductProfileDetails.get(unitId).get(productId) != null
				&& unitProductProfileDetails.get(unitId).get(productId).get(dimension) != null
						? unitProductProfileDetails.get(unitId).get(productId).get(dimension).getValue()
						: null;
	}

	public void clearUnitProductProfileDetails() {
		instance.getMultiMap("MasterDataCache:unitProductProfileDetails").clear();
	}

	public void clearUnitMonkRecipeProfileVersion() { instance.getMap("MasterDataCache:unitMonkRecipeProfileVersion").clear(); }

	public IMap<Integer, Pair<String, String>> getUnitMonkRecipeProfileVersion() {
		return unitMonkRecipeProfileVersion;
	}

	public Pair<String, String> getMonkRecipeProfileVersionOfUnit(int unitId) {
		if (Objects.nonNull(unitMonkRecipeProfileVersion) && unitMonkRecipeProfileVersion.containsKey(unitId)) {
			return unitMonkRecipeProfileVersion.get(unitId);
		}
		return null;
	}

	@Override
	public String toString() {
		return "RecipeCache{" +
				"recipes=" + recipes.size() +
				", criticalProducts=" + criticalProducts.size() +
				", scmRecipes=" + scmRecipes.size() +
				", recipeMap=" + recipeMap.size() +
				", unitProductProfileDetails=" + unitProductProfileDetails.size() +
				'}';
	}
}
