package com.stpl.tech.master.core.service;

import java.util.List;

import com.stpl.tech.master.domain.model.ApplicationInstallationDetail;
import com.stpl.tech.master.domain.model.ApplicationInstallationRequest;
import com.stpl.tech.master.domain.model.UnitRestrictedApplicationDetail;

public interface InstallationService {

	public List<ApplicationInstallationDetail> getInstalledMachines(String applicationName, Integer unintId, String terminal,
			String screenType, String status);

	public ApplicationInstallationDetail addInstallationMachine(ApplicationInstallationDetail installationDetail);

	public Boolean updateInstallationMachineStatus(ApplicationInstallationRequest request);

	public Boolean updateUnitRestrictedApplication(List<UnitRestrictedApplicationDetail> detail);

	public List<UnitRestrictedApplicationDetail> getApplicationList(Integer unitId, String status);

}
