package com.stpl.tech.master.core.external.partnerReport.dao.impl;

import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.external.partnerReport.dao.ExternalPartnerReportDao;
import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.PaymentMode;
import com.stpl.tech.master.data.model.ProductDetail;

@Repository
public class ExternalPartnerReportDaoImpl extends AbstractMasterDaoImpl implements ExternalPartnerReportDao {

	@Override
	public  ProductDetail getProductDetail(int productId){
		return find(ProductDetail.class, productId);
	}
	
	@Override
	public  PaymentMode getPaymentMode(int modeId){
		return find(PaymentMode.class, modeId);
	}
}
