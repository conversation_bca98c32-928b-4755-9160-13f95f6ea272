<!--<#setting locale="en_US">-->
<!--<#setting date_format="MM/dd/yyyy HH:mm:ss">-->
<!--<#setting number_format="0.##">-->
<!--<#setting boolean_format="Yes,No">-->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd"><html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Order Detail Email Notification For BCX</title>
</head>
<body>
    <h3>Hi $data.UNIT_NAME</h3>
    <h3>We have received a $data.COMPLAINT_TYPE from $data.PARTNER_CODE for $data.BRAND_NAME</h3>
    <h3>Below are the details</h3>

<table border="1" cellspacing="0" cellpadding="5">
    <tbody>
    <tr>
        <td><b>Issue Type</b></td>
        <!--        <td>$data.ISSUE_TYPE</td>-->
        <td> #if($data.ISSUE_TYPE)
            $data.ISSUE_TYPE
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Order Id</b></td>
        <!--        <td>$data.GENERATED_ORDER_ID</td>-->
        <td> #if($data.GENERATED_ORDER_ID)
            $data.GENERATED_ORDER_ID
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Partner Order Id</b></td>
        <!--        <td>$data.PartnerSourceId</td>-->
        <td> #if($data.PartnerSourceId)
            $data.PartnerSourceId
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Customer Name</b></td>
        <!--        <td>$data.CUSTOMER_NAME</td>-->
        <td> #if($data.CUSTOMER_NAME)
            $data.CUSTOMER_NAME
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Order Time</b></td>
        <!--        <td>$data.BILLING_SERVER_TIME</td>-->
        <td> #if($data.BILLING_SERVER_TIME)
            $data.BILLING_SERVER_TIME
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Processing Time</b></td>
        <!--        <td>$data.PREPERATION_TIME</td>-->
        <td> #if(($data.READY_TO_DISPATCH) && ($data.PREPERATION_TIME))
            $data.READY_TO_DISPATCH ($data.PREPERATION_TIME mins)
            #elseif ($data.READY_TO_DISPATCH)
            $data.READY_TO_DISPATCH
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Rider Assigned At</b></td>
        <!--        <td>$data.RIDER_ASSIGNED_TIME</td>-->
        <td> #if(($data.RIDER_ASSIGNED_AT) && ($data.RIDER_ASSIGNED_TIME))
            $data.RIDER_ASSIGNED_AT ($data.RIDER_ASSIGNED_TIME mins)
            #elseif($data.RIDER_ASSIGNED_AT)
            $data.RIDER_ASSIGNED_AT
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Rider Arrived At</b></td>
        <!--        <td>$data.RIDER_ARRIVED_AT</td>-->
        <td> #if(($data.RIDER_ARRIVED_AT) && ($data.RIDER_ARRIVED_TIME))
            $data.RIDER_ARRIVED_AT ($data.RIDER_ARRIVED_TIME mins)
            #elseif($data.RIDER_ARRIVED_AT)
            $data.RIDER_ARRIVED_AT
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Pickup Time</b></td>
        <!--        <td>$data.ORDER_PICKUP_TIME</td>-->
        <td> #if(($data.ORDER_PICKUP_TIME) && ($data.RIDER_PICKED_TIME))
            $data.ORDER_PICKUP_TIME ($data.RIDER_PICKED_TIME mins)
            #elseif($data.ORDER_PICKUP_TIME)
            $data.ORDER_PICKUP_TIME
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Delivery Time</b></td>
        <!--        <td>$data.ORDER_DELIVERY_TIME</td>-->
        <td> #if(($data.ORDER_DELIVERY_TIME) && ($data.RIDER_DELIVERY_TIME))
            $data.ORDER_DELIVERY_TIME ($data.RIDER_DELIVERY_TIME mins)
            #elseif($data.ORDER_DELIVERY_TIME)
            $data.ORDER_DELIVERY_TIME
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Rider Details</b></td>
        <!--        <td>$data.RIDER_NAME + "( " + $data.RIDER_CONTACT +" )"</td>-->
        <td> #if(($data.RIDER_NAME) && ($data.RIDER_CONTACT))
            $data.RIDER_NAME ($data.RIDER_CONTACT)
            #elseif($data.RIDER_NAME)
            $data.RIDER_NAME
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Customer Complaint Time</b></td>
        <!--        <td>$data.ISSUE_TIME</td>-->
        <td> #if($data.COMPLAINT_TIME)
            $data.COMPLAINT_TIME
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Complaint Items</b></td>
        <!--        <td>$data.ITEMS</td>-->
        <td> #if($data.COMPLAINT_ITEMS)
            $data.COMPLAINT_ITEMS
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Previous Items</b></td>
        <!--        <td>$data.ITEMS</td>-->
        <td> #if($data.PREVIOUS_ITEMS)
            $data.PREVIOUS_ITEMS
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Modified Items</b></td>
        <!--        <td>$data.ITEMS</td>-->
        <td> #if($data.MODIFIED_ITEMS)
            $data.MODIFIED_ITEMS
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>FallBack Errors</b></td>
        <td>
            #if($data.FALLBACK_ERRORS)
            $data.FALLBACK_ERRORS
            #else
            NA
            #end
        </td>
    </tr>
    <tr>
        <td><b>All Items</b></td>
        <!--        <td>$data.ITEMS</td>-->
        <td> #if($data.ITEMS)
            $data.ITEMS
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Order Amount</b></td>
        <!--        <td>$data.TOTAL_AMOUNT</td>-->
        <td> #if($data.TOTAL_AMOUNT)
            $data.TOTAL_AMOUNT
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>DAM</b></td>
        <!--        <td>$data.CAFE_MANAGER + "( " + $data."CAFE_MANAGER_CONTACT"+ " )"</td>-->
        <td> #if(($data.CAFE_MANAGER) && ($data.CAFE_MANAGER_CONTACT))
            $data.CAFE_MANAGER ($data.CAFE_MANAGER_CONTACT)
            #elseif($data.CAFE_MANAGER)
            $data.CAFE_MANAGER
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>AM</b></td>
        <!--        <td>$data.UNIT_MANAGER + "( " + $data."UNIT_MANAGER_CONTACT"+ " )"</td>-->
        <td> #if(($data.UNIT_MANAGER) && ($data.UNIT_MANAGER_CONTACT))
            $data.UNIT_MANAGER ($data.UNIT_MANAGER_CONTACT)
            #elseif($data.UNIT_MANAGER)
            $data.UNIT_MANAGER
            #else
            NA
            #end</td>
    </tr>
    <tr>
        <td><b>Cafe Manager</b></td>
        <!--        <td>$data.UNIT_CAFE_MANAGER</td>-->
        <td> #if($data.UNIT_CAFE_MANAGER)
            $data.UNIT_CAFE_MANAGER
            #else
            NA
            #end</td>
    </tr>
    </tbody>
</table>
    <h3>Please Look Into This asap</h3>
    <h3>Thanks</h3>
    <h3>BCX Management Team</h3>



</body>
</html>

