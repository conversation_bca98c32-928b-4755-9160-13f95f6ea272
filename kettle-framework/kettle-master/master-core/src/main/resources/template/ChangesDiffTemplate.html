
<#setting locale="en_US">
<#setting date_format="MM/dd/yyyy HH:mm:ss">
<#setting number_format="0.##">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Changes Overview</title>
</head>
<body>


<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
    <div style="padding: 15px 5px;">
        <b>
            ${Object} Id : ${Id}
        </b>
    </div>

    <div  style="margin: 5px; padding: 5px;">
        <table style="width: 100%;">
            <thead>
            <tr style="background: #c90; font-weight: bolder;">
                <th>Field Name</th>
                <th>Old Value</th>
                <th>New Value</th>
            </tr>
            </thead>
            <tbody>
            <#list keys>
              <#items as key>
               <tr style="background-color: #e6e6e6; margin-top: 10px; padding:15px;">
                   <td style="padding-left: 5px; color: black; font-weight: bold;">${key}</td>
                   <td style="padding-left:5px;vertical-align : top ;color: black ; "><#if (diffs[key].key)?? > ${diffs[key].key} </#if></td>
                   <td style="padding-left : 5px; vertical-align: top; color: black ; "><#if (diffs[key].value)?? > ${diffs[key].value} </#if></td>
               </tr>
              </#items>
            </#list>
            </tbody>
        </table>

    </div>
</div>
</body>
