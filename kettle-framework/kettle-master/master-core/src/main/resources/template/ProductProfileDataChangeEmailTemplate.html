<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Product Price Profile Change Notification</title>
    <style type="text/css">
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding-bottom: 20px;
            border-bottom: 1px solid #dddddd;
        }
        .product-section {
            margin-top: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eeeeee;
        }
        .product-section:last-child {
            border-bottom: none;
        }
        .product-title {
            font-size: 20px;
            color: #333333;
        }
        .updated-by {
            font-size: 14px;
            color: #777777;
        }
        .mapping-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .mapping-table th, .mapping-table td {
            border: 1px solid #dddddd;
            padding: 8px;
            text-align: center;
            font-size: 14px;
        }
        .mapping-table th {
            background-color: #f9f9f9;
            color: #555555;
        }
        .mapping-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .error {
            text-align: center;
            color: #ff0000;
            font-size: 18px;
            padding: 20px;
        }
    </style>
</head>
<body>
#if((!$data.mappingData || $data.mappingData.isEmpty()) && (!$data.mappingDataForBulkUpload || $data.mappingDataForBulkUpload.isEmpty()))
<div class="error">No mapping data found.</div>
#else
<div class="container">
    <div class="header">
        <h2>Product Price Profile Change Notification</h2>
    </div>

    ## Process mappingData if it exists and is not empty
    #if($data.mappingData && !$data.mappingData.isEmpty())
    #foreach($entry in $data.mappingData.entrySet())
    #set($product = $entry.getKey())
    #set($productMappings = $entry.getValue())

    <div class="product-section">
        <div class="product-title">Product: $product.productName</div>
        <div class="updated-by">Updated By: $data.updatedBy</div>

        <table class="mapping-table">
            <thead>
            <tr>
                <th>UNIT ID</th>
                <th>UNIT NAME</th>
                <th>DIMENSION</th>
                #if($data.isProfileChange)
                <th>PROFILE (Current)</th>
                <th>PROFILE (New)</th>
                <th>STATUS (Current)</th>
                <th>STATUS (New)</th>
                <th>DELIVERY ONLY PRODUCT</th>
                <th>PICK DINE IN CONSUMABLES</th>
                #else
                <th>PRODUCT ALIAS (Current)</th>
                <th>PRODUCT ALIAS (New)</th>
                <th>DIMENSION DESC (Current)</th>
                <th>DIMENSION DESC (New)</th>
                #end
            </tr>
            </thead>
            <tbody>
            #foreach($item in $productMappings)
            <tr>
                <td>#if($item.unit.id)$item.unit.id#else N/A#end</td>
                <td>#if($item.unit.name)$item.unit.name#else N/A#end</td>
                <td>#if($item.dimension.name)$item.dimension.name#else N/A#end</td>
                #if($data.isProfileChange)
                <td>#if($item.price.currentProfile)$item.price.currentProfile#else N/A#end</td>
                <td>#if($item.price.profile)$item.price.profile#else N/A#end</td>
                <td>#if($item.price.currentStatus)$item.price.currentStatus#else N/A#end</td>
                <td>#if($item.price.status)$item.price.status#else N/A#end</td>
                <td>#if($item.price.isDeliveryOnlyProduct)Yes#else No#end</td>
                <td>#if($item.price.pickDineInConsumables == true)Yes#elseif($item.price.pickDineInConsumables == false)No#else N/A#end</td>
                #else
                <td>#if($item.price.currentAliasProductName)$item.price.currentAliasProductName#else N/A#end</td>
                <td>#if($item.price.aliasProductName)$item.price.aliasProductName#else N/A#end</td>
                <td>#if($item.price.currentDimensionDescriptor)$item.price.currentDimensionDescriptor#else N/A#end</td>
                <td>#if($item.price.dimensionDescriptor)$item.price.dimensionDescriptor#else N/A#end</td>
                #end
            </tr>
            #end
            </tbody>
        </table>
    </div>
    #end
    #elseif($data.mappingDataForBulkUpload && !$data.mappingDataForBulkUpload.isEmpty())
    #foreach($entry in $data.mappingDataForBulkUpload.entrySet())
    #set($product = $entry.getKey())
    #set($productMappings = $entry.getValue())

    <div class="product-section">
        <div class="product-title">Product: $product.productName</div>
        <div class="updated-by">Updated By: $data.updatedBy</div>

        <table class="mapping-table">
            <thead>
            <tr>
                <th>UNIT ID</th>
                <th>UNIT NAME</th>
                <th>DIMENSION</th>
                #if($data.isProfileChange)
                <th>PROFILE (Current)</th>
                <th>PROFILE (New)</th>
                <th>STATUS (Current)</th>
                <th>STATUS (New)</th>
                <th>DELIVERY ONLY PRODUCT</th>
                <th>PICK DINE IN CONSUMABLES</th>
                #else
                <th>PRODUCT ALIAS (Current)</th>
                <th>PRODUCT ALIAS (New)</th>
                <th>DIMENSION DESC (Current)</th>
                <th>DIMENSION DESC (New)</th>
                #end
            </tr>
            </thead>
            <tbody>
            #foreach($item in $productMappings)
            <tr>
                <td>#if($item.unitId)$item.unitId#else N/A#end</td>
                <td>#if($item.unitName)$item.unitName#else N/A#end</td>
                <td>#if($item.dimensionCode)$item.dimensionCode#else N/A#end</td>
                #if($data.isProfileChange)
                <td>#if($item.oldRecipeProfile)$item.oldRecipeProfile#else N/A#end</td>
                <td>#if($item.newRecipeProfile)$item.newRecipeProfile#else N/A#end</td>
                <td>#if($item.oldStatus)$item.oldStatus#else N/A#end</td>
                <td>#if($item.newStatus)$item.newStatus#else N/A#end</td>
                <td>#if($item.newDeliveryOnly)$item.newDeliveryOnly#else N/A#end</td>
                <td>#if($item.newPickDineInConsumables)$item.newPickDineInConsumables #else N/A#end</td>
                #else
                <td>#if($item.oldProductAlias)$item.oldProductAlias#else N/A#end</td>
                <td>#if($item.newProductAlias)$item.newProductAlias#else N/A#end</td>
                <td>#if($item.oldDimensionDesc)$item.oldDimensionDesc#else N/A#end</td>
                <td>#if($item.newDimensionDesc)$item.newDimensionDesc#else N/A#end</td>
                #end
            </tr>
            #end
            </tbody>
        </table>
    </div>
    #end
    #end
</div>
#end
</body>
</html>