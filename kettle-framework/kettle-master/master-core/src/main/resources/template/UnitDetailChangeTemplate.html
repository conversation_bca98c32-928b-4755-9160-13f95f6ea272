<#setting locale="en_US">
<#setting date_format="MM/dd/yyyy HH:mm:ss">
<#setting number_format="0.##">
<#setting boolean_format="Yes,No">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">

<body>

<div  style="margin: 5px; padding: 5px;">
    <h1>Changes Overview</h1>
    <table style="width: 100%;">
        <thead>
        <tr style="background: #c90; font-weight: bolder;">
            <th>Field Name</th>
            <th>Old Value</th>
            <th>New Value</th>
        </tr>
        </thead>
        <tbody>
        <#list keys>
        <#items as key>
        <tr style="background-color: #e6e6e6; margin-top: 10px; padding:15px;">
            <td style="padding-left: 5px; color: black; font-weight: bold;">${key}</td>
            <td style="padding-left:5px;vertical-align : top ;color: black ; "><#if (diffs[key].key)?? > ${diffs[key].key} </#if></td>
            <td style="padding-left : 5px; vertical-align: top; color: black ; "><#if (diffs[key].value)?? > ${diffs[key].value} </#if></td>
        </tr>
        </#items>
    </#list>
    </tbody>
    </table>

</div>

<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">

    <h1>UNIT INFO</h1>
    <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Unit Name : </b> <#if NewObject.name ??>${NewObject.name}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Unit EMail : </b><#if NewObject.unitEmail ??>${NewObject.unitEmail}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Unit Family : </b><#if NewObject.family.value() ??>${NewObject.family.value()}<#else>...</#if> </td>
            <td style="padding:5pt;text-align:left;"><b>Unit Region : </b><#if NewObject.region ??>${NewObject.region}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>No of Take Away Terminal Count : </b><#if NewObject.noOfTakeawayTerminals ??>${NewObject.noOfTakeawayTerminals}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Work Station Enabled : </b><#if NewObject.workstationEnabled ??>${NewObject.workstationEnabled}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Free Internet Access : </b><#if NewObject.freeInternetAccess ??>${NewObject.freeInternetAccess}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Table Service Enabled : </b><#if NewObject.tableService ??>${NewObject.tableService}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Unit Terminals count : </b><#if NewObject.noOfTerminals ??>${NewObject.noOfTerminals}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b> Unit GSTIN number : </b> <#if NewObject.tin  ??>${NewObject.tin}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>FSSAI : </b><#if NewObject.fssai ??>${NewObject.fssai}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Otp Via Email Allowed : </b><#if NewObject.isOtpAllowedViaEmail ??>${NewObject.isOtpAllowedViaEmail}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Unit Reference Name : </b><#if NewObject.referenceName ??>${NewObject.referenceName}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Communication Channel : </b><#if NewObject.channel ??>${NewObject.channel}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>No of Electricity Meter : </b><#if NewObject.noOfMeter ??>${NewObject.noOfMeter}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>DG Meter Available : </b><#if NewObject.dGAvailable ??>${NewObject.dGAvailable}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Business Type : </b><#if NewObject.unitBusinessType.value() ??>${NewObject.unitBusinessType.value()}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Company : </b><#if NewObject.company.name ??>${NewObject.company.name}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Revenue Certificate Generation Enable : </b><#if NewObject.revenueCertificateGenerationEnable ??>${NewObject.revenueCertificateGenerationEnable}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Revenue Certificate Emails : </b><#if NewObject.revenueCertificateEmail ??>${NewObject.revenueCertificateEmail}<#else>...</#if></td>
        </tr>
    </table>

    <h1>UNIT ADDRESS DETAILS</h1>
    <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Address Type : </b><#if NewObject.address.addressType ??>${NewObject.address.addressType}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Line 1 : </b><#if NewObject.address.line1 ??>${NewObject.address.line1}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Line 2 : </b><#if NewObject.address.line2 ??>${NewObject.address.line2}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Line 3 : </b><#if NewObject.address.line3 ??>${NewObject.address.line3}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>City : </b><#if NewObject.address.city ??>${NewObject.address.city}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>State : </b><#if NewObject.address.state ??>${NewObject.address.state}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>ZipCode : </b><#if NewObject.address.zipCode ??>${NewObject.address.zipCode}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Country : </b><#if NewObject.address.country ??>${NewObject.address.country}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Contact 1 : </b> <#if NewObject.address.contact1 ??>${NewObject.address.contact1}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Contact 2 : </b> <#if NewObject.address.contact2 ??>${NewObject.address.contact2}<#else>...</#if></td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Latitude : </b><#if NewObject.address.latitude ??>${NewObject.address.latitude}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Longitude : </b><#if NewObject.address.longitude ??>${NewObject.address.longitude}<#else>...</#if> </td>
        </tr>
    </table>

    <h1>DELIVERY PARTNERS</h1>
    <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
        <thead>
        <tr style="background: #c90;">
            <th>Partner Name</th>
            <th>Priority</th>
        </tr>
        </thead>
        <tbody>
        <#list NewObject.deliveryPartners>
        <#items as key>
        <tr style="background-color: #e6e6e6; margin-top: 10px; padding:15px;">
            <td style="padding-left:5px;vertical-align : top ;color: black ; "><#if key.detail.name ?? > key.detail.name </#if></td>
            <td style="padding-left : 5px; vertical-align: top; color: black ; "><#if key.priority ?? > key.priority </#if></td>
        </tr>
        </#items>
    </#list>
    </tbody>
    </table>

    <h1>MANAGER</h1>
    <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Manager name : </b><#if NewObject.managerName ??>${NewObject.managerName}<#else>...</#if></td>
        </tr>
    </table>

    <h1>LOCATION</h1>
    <table border="1" style="width:100%;border-spacing: 0;border-color: black;">
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Country : </b><#if NewObject.location.country ??>${NewObject.location.country}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>State : </b><#if NewObject.address.state ??>${NewObject.address.state}<#else>...</#if> </td>
        </tr>
        <tr>
            <td style="padding:5pt;text-align:left;"><b>Location : </b><#if NewObject.location.name ??>${NewObject.location.name}<#else>...</#if></td>
            <td style="padding:5pt;text-align:left;"><b>Code : </b><#if NewObject.location.code ??>${NewObject.location.code}<#else>...</#if> </td>
        </tr>
    </table>


</div>