<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Product Price Profile Bulk Update Email</title>
</head>
<style>
    a:hover {text-decoration: underline !important;}

</style>
<body marginheight="0" topmargin="0" marginwidth="0" style="margin: 0px; background-color: #E5F3C71;" leftmargin="0">
<table cellspacing="0" border="0" cellpadding="0" width="100%" bgcolor="#E5F3C7"
       style="@import url(https://fonts.googleapis.com/css?family=Rubik:300,400,500,700|Open+Sans:300,400,600,700); font-family: 'Open Sans', sans-serif;">
    <tr>
        <td>
            <table style="background-color: #E5F3C7; max-width:670px; margin:0 auto;" width="100%" border="0"
                   align="center" cellpadding="0" cellspacing="0">
                <tr>
                    <td style="height:80px;">&nbsp;</td>
                </tr>
                <tr>
                    <td style="height:20px;"></td>
                </tr>
                <!-- Email Content -->
                <tr>
                    <td>
                        <table width="95%" border="0" align="center" cellpadding="0" cellspacing="0"
                               style="max-width:670px; background:#fff; border-radius:3px;-webkit-box-shadow:0 6px 18px 0 rgba(0,0,0,.06);-moz-box-shadow:0 6px 18px 0 rgba(0,0,0,.06);box-shadow:0 6px 18px 0 rgba(0,0,0,.06);padding:0 40px;">
                            <tr>
                                <td style="height:40px;"></td>
                            </tr>
                            <!-- Title -->
                            <tr>
                                <td style="padding:0 15px; text-align:center;">
                                    <h1 style="color:#1e1e2d; font-weight:400; margin:0;font-size:32px;font-family:'Rubik',sans-serif;">
                                        Product Price Profile Bulk Update report</h1>
                                    <span style="display:inline-block; vertical-align:middle; margin:29px 0 26px; border-bottom:1px solid #cecece;
                                        width:100px;"></span>
                                </td>
                            </tr>
                            <!-- Details Table -->
                            <tr>
                                <td>
                                    <table cellpadding="0" cellspacing="0"
                                           style="width: 100%; border: 1px solid #ededed">
                                        <tbody>
                                        <tr>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; border-right: 1px solid #ededed; width: 35%; font-weight:500; color:rgba(0,0,0,.64)">
                                                Unit Category:
                                            </td>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; color: #455056;">
                                                $data.unitCategory
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; border-right: 1px solid #ededed; width: 35%; font-weight:500; color:rgba(0,0,0,.64)">
                                                Brand Id:
                                            </td>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; color: #455056;">
                                                $data.brandId
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; border-right: 1px solid #ededed; width: 35%; font-weight:500; color:rgba(0,0,0,.64)">
                                                Updated By:
                                            </td>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; color: #455056;">
                                                $data.updatedBy
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed;border-right: 1px solid #ededed; width: 35%; font-weight:500; color:rgba(0,0,0,.64)">
                                                Total Records:
                                            </td>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; color: #455056;">
                                                #if($data.totalRecords)
                                                $data.totalRecords
                                                #else
                                                NA
                                                #end
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                    style="padding: 10px;  border-bottom: 1px solid #ededed; border-right: 1px solid #ededed; width: 35%;font-weight:500; color:rgba(0,0,0,.64)">
                                                Total Records Changed
                                            </td>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; color: #455056;">
                                                #if($data.totalRecordsChanged)
                                                $data.totalRecordsChanged
                                                #else
                                                NA
                                                #end
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; border-right: 1px solid #ededed; width: 35%;font-weight:500; color:rgba(0,0,0,.64)">
                                                Total Records Updated Successfully:
                                            </td>
                                            <td
                                                    style="padding: 10px; border-bottom: 1px solid #ededed; color: #455056; ">
                                                #if($data.totalRecordsUpdatedSuccessfully)
                                                $data.totalRecordsUpdatedSuccessfully
                                                #else
                                                NA
                                                #end
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                    style="padding: 10px; border-right: 1px solid #ededed; width: 35%;font-weight:500; color:rgba(0,0,0,.64)">
                                                Total Unchanged Records:
                                            </td>
                                            <td style="padding: 10px; color: #455056;">
                                                #if($data.totalErrorRecords)
                                                $data.totalErrorRecords
                                                #else
                                                NA
                                                #end
                                            </td>
                                        </tr>
                                        <tr>
                                            <td
                                                    style="padding: 10px; border-right: 1px solid #ededed; width: 35%;font-weight:500; color:rgba(0,0,0,.64)">
                                                Total Failure Records:
                                            </td>
                                            <td style="padding: 10px; color: #455056;">
                                                #if($data.totalFailureRecords)
                                                $data.totalFailureRecords
                                                #else
                                                NA
                                                #end
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td style="height:40px;">&nbsp;</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td style="height:20px;">&nbsp;</td>
                </tr>
            </table>
        </td>
    </tr>
</table>
</body>
</html>