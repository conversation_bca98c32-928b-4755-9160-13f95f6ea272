<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<#setting locale="en_US"> <#setting date_format="MM/dd/yyyy HH:mm:ss">
<#setting number_format="0.##">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Employee Activation Email</title>
</head>
<body style="font-family: Verdana, sans-serif;">
	<div
		style="box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); margin: 20px 0; background-color: #f1f1f1; padding: 0.01em 16px;">
		<table align="left" border="1" cellpadding="1" cellspacing="1"
			style="width: 500px">
			<thead>
				<tr>
					<th scope="col">Details</th>
					<th scope="col">Value</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>Employee Id</td>
					<td>${employee.id}</td>
				</tr>
				<tr>
					<td>Name</td>
					<td>${employee.name}</td>
				</tr>
				<tr>
					<td>Gender</td>
					<td>${employee.gender}</td>
				</tr>
				<tr>
					<td>Contact 1</td>
					<td>${employee.primaryContact}</td>
				</tr>
				<tr>
					<td>Contact 2</td>
					<td><#if employee.secondaryContact??>${employee.secondaryContact}<#else>Not available</#if></td>
				</tr>
				<tr>
					<td>Department</td>
					<td>${employee.department.name}</td>
				</tr>
				<tr>
					<td>Designation</td>
					<td>${employee.designation.name}</td>
				</tr>
				<tr>
					<td>Biometric Identifier</td>
					<td><#if employee.biometricId??>${employee.biometricId}</#if></td>
				</tr>
				<tr>
					<td>Employment Type</td>
					<td>${employee.employmentType}</td>
				</tr>
				<tr>
					<td>Employment Status</td>
					<td>${employee.employmentStatus}</td>
				</tr>
				<tr>
					<td>Joining Date</td>
					<td><#if employee.joiningDate??>${employee.joiningDate}</#if></td>
				</tr>

				<tr>
					<td>Reporting Manager</td>
					<td><#if
						(employee.reportingManager)??>${employee.reportingManager.name}</#if></td>
				</tr>
				<tr>
					<td>Current Address</td>
					<td>${currentAddress}</td>
				</tr>
				<tr>
					<td>Permanent Address</td>
					<td>${permanentAddress}</td>
				</tr>
				<tr>
					<td><strong>Password</strong></td>
					<td>${password}</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div
		style="box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12); margin: 20px 0; background-color: #f1f1f1; padding: 0.01em 16px;">

		<table align="left" border="1" cellpadding="1" cellspacing="1"
			style="width: 500px">
			<thead>
				<tr>
					<th scope="col">Unit Assignments</th>
				</tr>
			</thead>
			<tbody>
				<#list mappedUnits> <#items as unitName>
				<tr>
					<td>${unitName}</td>
				</tr>
				</#items> </#list>
			</tbody>
		</table>

		<p>&nbsp;</p>

	</div>
</body>
</html>


