<!--
  ~ Created By Shan<PERSON><PERSON>
  -->
<#setting locale="en_US">
<#setting date_format="MM/dd/yyyy HH:mm:ss">
<#setting number_format="0.##">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Recipes Pending For Approval</title>
</head>
<body>


<div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
    <div style="padding: 15px 5px; text-align: center; align-items: center">
        <h3>
            <#if emailType == "MARKED_IN_ACTIVE">
                Recipes Marked As IN_ACTIVE
            </#if>
            <#if emailType == "PENDING_FOR_APPROVAL">
                Recipes Pending For Approval
            </#if>
            <#if emailType == "MARKED_AS_ACTIVE">
                Recipes Marked As ACTIVE
            </#if>
        </h3>
    </div>

    <div  style="margin: 5px; padding: 5px;">
        <table style="width: 100%;">
            <thead>
            <tr style="background: #c90; font-weight: bolder;">
                <th>Recipe Id</th>
                <th>Recipe Name</th>
                <th>Recipe Dimension</th>
                <th>Recipe Profile</th>
                <th>Recipe Last Updated By</th>
            </tr>
            </thead>
            <tbody>
            <#list recipes>
            <#items as key>
            <tr style="background-color: #e6e6e6; margin-top: 10px; padding:15px;">
                <td style="padding-left: 5px; color: black; font-weight: bold;">${key.recipeId}</td>
                <td style="padding-left:5px;vertical-align : top ;color: black ; ">${key.recipeName}</td>
                <td style="padding-left:5px;vertical-align : top ;color: black ; ">${key.dimension}</td>
                <td style="padding-left:5px;vertical-align : top ;color: black ; ">${key.recipeProfile}</td>
                <td style="padding-left:5px;vertical-align : top ;color: black ; ">${key.recipeLastUpdatedByName} (${key.recipeLastUpdatedBy})</td>
            </tr>
            </#items>
        </#list>
        </tbody>
        </table>
    </div>
    <div style="padding: 15px 5px; text-align: center; align-items: center">
        <p style="background-color: red;color: white">
        <#if emailType == "MARKED_IN_ACTIVE">
            Note : The Above Recipes Are Marked As IN_ACTIVE You Can Re-Activate It through the Find Recipe Menu. If You are Facing any issue Kindly Contact Tech team.
        </#if>
        <#if emailType == "PENDING_FOR_APPROVAL">
            Note : If Recipe is Not Approved Before 04:00 AM Of Next Day, Recipe Will be Marked As IN_ACTIVE.
        </#if>
        <#if emailType == "MARKED_AS_ACTIVE">
            Note : The Above Recipes Are Marked As ACTIVE You Can Map the Recipe Profile to the Cafes From Now. If You are Facing any issue Kindly Contact Tech team.
        </#if>
        </p>
    </div>
</div>
</body>
