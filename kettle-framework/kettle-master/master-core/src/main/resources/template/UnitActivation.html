<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<#setting locale="en_US">
	<#setting date_format="MM/dd/yyyy HH:mm:ss">
		<#setting number_format="0.##">
			<#setting boolean_format="Yes,No">
				<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
			<html lang="en">
				<head>
					<title>Unit Activation Email</title>
				</head>
				<body>
					<br/>
					<p>
						<strong>Unit Details</strong>
					</p>
					<table border="1" cellspacing="0" cellpadding="5">
						<tbody>
							<tr>
								<th>ID</th>
								<th>Name</th>
								<th>Reference Name</th>
								<th>Region</th>
								<th>Family</th>
								<th>Status</th>
								<th>Email</th>
								<th>TIN</th>
								<th>Terminals</th>
								<th>TakeAway</th>
								<th>WorkStation</th>
								<th>Free Internet</th>
								<th>Table Service</th>
								<th>Communication Channel</th>
							</tr>
							<tr>
								<td>${unit.id}</td>
								<td>${unit.name}</td>
								<td>
									<#if (unit.referenceName)?? >${unit.referenceName} </#if>
								</td>
								<td>${unit.region}</td>
								<td>${unit.family}</td>
								<td>${unit.status}</td>
								<td>
									<#if (unit.unitEmail)?? >${unit.unitEmail} </#if>
								</td>
								<td>${unit.tin}</td>
								<td>${unit.noOfTerminals}</td>
								<td>${unit.noOfTakeawayTerminals}</td>
								<td>${unit.workstationEnabled}</td>
								<td>${unit.freeInternetAccess}</td>
								<td>${unit.tableService}</td>
								<td>
									<#if (unit.channel)?? >${unit.channel} </#if>
								</td>
							</tr>
						</tbody>
					</table>
					<p>
						<strong>Division</strong>
					</p>              
					<table border="1" cellspacing="0" cellpadding="5">
						<tr>
							<th>Name</th>
							<th>Description</th>
							<th>Category</th>
							<th>Sub Category</th>
						</tr>
						<tr>
							<td>${unit.division.name}</td>
							<td>${unit.division.description}</td>
							<td>${unit.division.category}</td>
							<td>${unit.subCategory}</td>
						</tr>                
					</table>
					<p>
						<strong>Address</strong>
					</p>              
					<table border="1" cellspacing="0" cellpadding="5">
						<tr>
							<th>Line 1</th>
							<th>Line 2</th>
							<th>Line 3</th>
							<th>City</th>
							<th>State</th>
							<th>Country</th>
							<th>Zip code</th>
							<th>Contact 1</th>
							<th>Contact 2</th>
							<th>Latitude</th>
							<th>Longitude</th>
							<th>Address Type</th>
						</tr>
						<tr>
							<td>
								<#if (unit.address.line1)?? >${unit.address.line1} </#if>
							</td>
							<td>
								<#if (unit.address.line2)?? >${unit.address.line2} </#if>
							</td>
							<td>
								<#if (unit.address.line3)?? >${unit.address.line3}</#if>
							</td>
							<td>
								<#if (unit.address.city)?? >${unit.address.city} </#if>
							</td>
							<td>
								<#if (unit.address.state)?? >${unit.address.state} </#if>
							</td>
							<td>
								<#if (unit.address.country)?? >${unit.address.country} </#if>
							</td>
							<td>
								<#if (unit.address.zipCode)?? >${unit.address.zipCode} </#if>
							</td>
							<td>
								<#if (unit.address.contact1)?? >${unit.address.contact1} </#if>
							</td>
							<td>
								<#if (unit.address.contact2)?? >${unit.address.contact2}</#if>
							</td>
							<td>
								<#if (unit.address.latitude)?? >${unit.address.latitude} </#if>
							</td>
							<td>
								<#if (unit.address.longitude)?? >${unit.address.longitude} </#if>
							</td>
							<td>
								<#if (unit.address.addressType)?? >${unit.address.addressType} </#if>
							</td>
						</tr>                
					</table>
					<p>
						<strong>Tax Profile</strong>
					</p>              
					<table border="1" cellspacing="0" cellpadding="5">
					<tr>
						<td>TAX Profile</td>
						<td>Percentage</td>
						<td>Status</td>
					</tr>
					<#list unit.taxProfiles>
						<#items as profile>
						<tr>
							<td>${profile.name}</td>
							<td>${profile.percentage}</td>
							<td>${profile.status}</td>
						</tr>   
						</#items>
					</#list>             
					</table>
					<p>
						<strong>Business Hours </strong>
					</p> 
					<table border="1" cellspacing="0" cellpadding="5">
						<tr>
							<td>Shift</td>
							<td>Days</td>
							<td>Dine In</td>
							<td>Cod</td>
							<td>Take Away</td>
							<td>Hand Over</td>
						</tr>
						<#list unit.operationalHours>
							<#items as operationHour>
								<tr>
									<td>${operationHour.noOfShifts}</td>
									<td>${operationHour.dayOfTheWeek}</td>
									<td>
										<b>Start Time :</b>
										<#if (operationHour.dineInOpeningTime)?? > ${operationHour.dineInOpeningTime} </#if> 
										<b>End Time :</b>
										<#if (operationHour.dineInClosingTime)?? > ${operationHour.dineInClosingTime} </#if> 
									</td>
									<td>
										<b>Start Time :</b>
										<#if (operationHour.deliveryOpeningTime)?? > ${operationHour.deliveryOpeningTime} </#if>
										<b>End Time : </b>
										<#if (operationHour.deliveryClosingTime)?? > ${operationHour.deliveryClosingTime} </#if>
									</td>
									<td>
										<b>Start Time :</b>
										<#if (operationHour.takeAwayOpeningTime)?? > ${operationHour.takeAwayOpeningTime}  </#if>
										<b>End Time : </b>
										<#if (operationHour.takeAwayClosingTime)?? > ${operationHour.takeAwayClosingTime} </#if>
									</td>
									<td>
										<#if (operationHour.shiftOneHandoverTime)?? > ${operationHour.shiftOneHandoverTime} </#if>
									</td>
								</tr>
							</#items>
						</#list>
					</table>
					<p>
						<strong>Products</strong>
					</p>              
					<table border="1" cellspacing="0" cellpadding="5">
						<tr>
							<th>ID</th>
							<th>Name</th>
							<th>Profile Size</th>
							<th>Addons</th>
							<th>Attribute</th>
							<th>SKU Code</th>
							<th>Bill Type</th>
							<th>Addon Profile</th>
							<th>Status</th>
						</tr>
						<#list unit.products>
							<#items as product>
								<tr>
									<td>${product.id}</td>
									<td>${product.name}</td>
									<td>${product.hasSizeProfile}</td>
									<td>${product.hasAddons}</td>
									<td>
										<#if (product.attribute)?? > ${product.attribute} </#if>
									</td>
									<td>
										<#if (product.skuCode)?? >${product.skuCode} </#if>
									</td>
									<td>${product.billType}</td>
									<td>
										<#if (product.addOnProfile)?? > ${product.addOnProfile} </#if>
									</td>
									<td>${product.status}</td>
								</tr>
							</#items>
						</#list>			
					</table>
				</body>
			</html>