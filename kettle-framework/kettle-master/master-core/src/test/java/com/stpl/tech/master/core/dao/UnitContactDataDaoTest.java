package com.stpl.tech.master.core.dao;

import com.stpl.tech.master.data.model.UnitContactDataEntity;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test class for UnitContactDataDao Spring Data JPA Repository
 */
@DataJpaTest
@ActiveProfiles("test")
public class UnitContactDataDaoTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private UnitContactDataDao unitContactDataDao;

    @Test
    public void testFindByUnitIdOrderByCreatedOnDesc() {
        // Given
        Date now = new Date();
        
        UnitContactDataEntity contact1 = createTestContact(1, "Manager", 9876543210L, "ACTIVE", now);
        UnitContactDataEntity contact2 = createTestContact(1, "Assistant", 9876543211L, "ACTIVE", new Date(now.getTime() + 1000));
        
        entityManager.persistAndFlush(contact1);
        entityManager.persistAndFlush(contact2);

        // When
        List<UnitContactDataEntity> contacts = unitContactDataDao.findByUnitIdOrderByCreatedOnDesc(1);

        // Then
        assertThat(contacts).hasSize(2);
        assertThat(contacts.get(0).getReferenceName()).isEqualTo("Assistant"); // Most recent first
        assertThat(contacts.get(1).getReferenceName()).isEqualTo("Manager");
    }

    @Test
    public void testFindByUnitIdAndStatusOrderByCreatedOnDesc() {
        // Given
        Date now = new Date();
        
        UnitContactDataEntity activeContact = createTestContact(1, "Manager", 9876543210L, "ACTIVE", now);
        UnitContactDataEntity inactiveContact = createTestContact(1, "Assistant", 9876543211L, "INACTIVE", now);
        
        entityManager.persistAndFlush(activeContact);
        entityManager.persistAndFlush(inactiveContact);

        // When
        List<UnitContactDataEntity> activeContacts = unitContactDataDao.findByUnitIdAndStatusOrderByCreatedOnDesc(1, "ACTIVE");

        // Then
        assertThat(activeContacts).hasSize(1);
        assertThat(activeContacts.get(0).getReferenceName()).isEqualTo("Manager");
        assertThat(activeContacts.get(0).getStatus()).isEqualTo("ACTIVE");
    }

    @Test
    public void testUpdateStatusByIds() {
        // Given
        UnitContactDataEntity contact1 = createTestContact(1, "Manager", 9876543210L, "ACTIVE", new Date());
        UnitContactDataEntity contact2 = createTestContact(1, "Assistant", 9876543211L, "ACTIVE", new Date());
        
        contact1 = entityManager.persistAndFlush(contact1);
        contact2 = entityManager.persistAndFlush(contact2);

        // When
        Date updateTime = new Date();
        int updatedCount = unitContactDataDao.updateStatusByIds(
            Arrays.asList(contact1.getId(), contact2.getId()), 
            "INACTIVE", 
            999, 
            updateTime
        );

        // Then
        assertThat(updatedCount).isEqualTo(2);
        
        // Verify the updates
        entityManager.clear(); // Clear persistence context to force fresh fetch
        UnitContactDataEntity updatedContact1 = entityManager.find(UnitContactDataEntity.class, contact1.getId());
        UnitContactDataEntity updatedContact2 = entityManager.find(UnitContactDataEntity.class, contact2.getId());
        
        assertThat(updatedContact1.getStatus()).isEqualTo("INACTIVE");
        assertThat(updatedContact1.getUpdatedBy()).isEqualTo(999);
        assertThat(updatedContact2.getStatus()).isEqualTo("INACTIVE");
        assertThat(updatedContact2.getUpdatedBy()).isEqualTo(999);
    }

    @Test
    public void testExistsByUnitIdAndContactNumberExcludingId() {
        // Given
        UnitContactDataEntity contact = createTestContact(1, "Manager", 9876543210L, "ACTIVE", new Date());
        contact = entityManager.persistAndFlush(contact);

        // When & Then
        // Should return true when checking same contact number for same unit (excluding different ID)
        boolean exists1 = unitContactDataDao.existsByUnitIdAndContactNumberExcludingId(1, 9876543210L, 999);
        assertThat(exists1).isTrue();

        // Should return false when excluding the same contact ID
        boolean exists2 = unitContactDataDao.existsByUnitIdAndContactNumberExcludingId(1, 9876543210L, contact.getId());
        assertThat(exists2).isFalse();

        // Should return false for different unit
        boolean exists3 = unitContactDataDao.existsByUnitIdAndContactNumberExcludingId(2, 9876543210L, null);
        assertThat(exists3).isFalse();

        // Should return false for different contact number
        boolean exists4 = unitContactDataDao.existsByUnitIdAndContactNumberExcludingId(1, 1234567890L, null);
        assertThat(exists4).isFalse();
    }

    @Test
    public void testSaveAndFindById() {
        // Given
        UnitContactDataEntity contact = createTestContact(1, "Manager", 9876543210L, "ACTIVE", new Date());

        // When
        UnitContactDataEntity savedContact = unitContactDataDao.save(contact);

        // Then
        assertThat(savedContact.getId()).isNotNull();
        
        UnitContactDataEntity foundContact = unitContactDataDao.findById(savedContact.getId()).orElse(null);
        assertThat(foundContact).isNotNull();
        assertThat(foundContact.getReferenceName()).isEqualTo("Manager");
        assertThat(foundContact.getContactNumber()).isEqualTo(9876543210L);
    }

    private UnitContactDataEntity createTestContact(Integer unitId, String referenceName, Long contactNumber, String status, Date createdOn) {
        UnitContactDataEntity contact = new UnitContactDataEntity();
        contact.setUnitId(unitId);
        contact.setReferenceName(referenceName);
        contact.setContactNumber(contactNumber);
        contact.setStatus(status);
        contact.setCreatedBy(1);
        contact.setCreatedOn(createdOn);
        contact.setUpdatedBy(1);
        contact.setUpdatedOn(createdOn);
        return contact;
    }
}
