<?xml version="1.0"?>
<!-- ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL ~ __________________ 
	~ ~ [2015] - [2017] Sunshine Teahouse Private Limited ~ All Rights Reserved. 
	~ ~ NOTICE: All information contained herein is, and remains ~ the property 
	of Sunshine Teahouse Private Limited and its suppliers, ~ if any. The intellectual 
	and technical concepts contained ~ herein are proprietary to Sunshine Teahouse 
	Private Limited ~ and its suppliers, and are protected by trade secret or 
	copyright law. ~ Dissemination of this information or reproduction of this 
	material ~ is strictly forbidden unless prior written permission is obtained 
	~ from Sunshine Teahouse Private Limited. -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:element name="Unit">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="id" type="xs:int" />
				<xs:element name="creditAccount" type="xs:int" nillable="true"/>
				<xs:element name="name" type="xs:string" />
				<xs:element name="region" type="UnitRegion" />
				<xs:element name="family" type="UnitCategory" />
				<xs:element name="subCategory" type="UnitSubCategory" />
				<xs:element name="unitBusinessType" type="UnitBusinessType" />
				<xs:element name="status" type="UnitStatus" />
				<xs:element name="unitEmail" type="xs:string" />
				<xs:element name="startDate" type="xs:date" nillable="true" />
				<xs:element name="lastBusinessDate" type="xs:date"
					nillable="true" />
				<xs:element name="tin" type="xs:string" />
				<xs:element name="division" type="Division" />
				<xs:element name="address" type="Address" />
				<xs:element name="cloneUnitId" type="xs:int" nillable="true" />
				<xs:element name="inventoryCloneUnitId" type="xs:int"
					nillable="true" />
				<xs:element name="noOfTerminals" type="xs:int" default="1"
					nillable="false" />
				<xs:element name="noOfTakeawayTerminals" type="xs:int"
					default="0" nillable="false" />
				<xs:element name="workstationEnabled" type="xs:boolean" />
				<xs:element name="noOfTables" type="xs:int" default="1"
					nillable="false" />
				<xs:element name="unitManager" type="Employee" nillable="true"/>
				<xs:element name="products" type="Product" minOccurs="1"
					maxOccurs="unbounded" />
				<xs:element name="deliveryPartners" type="PartnerDetail"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="taxProfiles" type="TaxProfile"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="referenceName" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="TransactionMetadata">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="categories" type="ListData" minOccurs="0"
					maxOccurs="unbounded" />
				<xs:element name="discountCodes" type="ListData" />
				<xs:element name="complimentaryCodes" type="ListData" />
				<xs:element name="addOns" type="AddonList" minOccurs="1"
					maxOccurs="unbounded" />
				<xs:element name="paymentModes" type="PaymentMode"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="channelPartner" type="IdCodeName"
					minOccurs="1" maxOccurs="unbounded" />
				<xs:element name="deliveryPartner" type="IdCodeName"
					minOccurs="1" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="OfferDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="category" type="xs:string" />
			<xs:element name="type" type="xs:string" />
			<xs:element name="text" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="startDate" type="xs:date" />
			<xs:element name="endDate" type="xs:date" />
			<xs:element name="minValue" type="xs:int" />
			<xs:element name="includeTaxes" type="xs:boolean" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="validateCustomer" type="xs:boolean" />
			<xs:element name="minQuantity" type="xs:int" />
			<xs:element name="minLoyalty" type="xs:int" />
			<xs:element name="offerValue" type="xs:int" />
			<xs:element name="minItemCount" type="xs:int" />
			<xs:element name="offerScope" type="xs:string" />
			<xs:element name="priority" type="xs:int" />
			<xs:element name="partners" type="IdCodeName" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="metaDataMappings" type="IdCodeName"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CouponDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="usage" type="xs:int" />
			<xs:element name="startDate" type="xs:date" />
			<xs:element name="endDate" type="xs:date" />
			<xs:element name="reusable" type="xs:boolean" />
			<xs:element name="reusableByCustomer" type="xs:boolean" />
			<xs:element name="maxUsage" type="xs:int" nillable="true" />
			<xs:element name="offer" type="OfferDetail" />
			<xs:element name="manualOverride" type="xs:boolean" />
			<xs:element name="couponMappingList" type="CouponMapping"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CouponMapping">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="type" type="xs:string" />
			<xs:element name="value" type="xs:string" />
			<xs:element name="dimension" type="xs:string" nillable="true" />
			<xs:element name="dataType" type="xs:string" />
			<xs:element name="minValue" type="xs:string" />
			<xs:element name="group" type="xs:int" />
			<xs:element name="status" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Employee">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="gender" type="xs:string" />
			<xs:element name="primaryContact" type="xs:string" />
			<xs:element name="secondaryContact" type="xs:string" />
			<xs:element name="biometricId" type="xs:string" nillable="true" />
			<xs:element name="department" type="Department" />
			<xs:element name="designation" type="Designation" />
			<xs:element name="currentAddress" type="Address" />
			<xs:element name="permanentAddress" type="Address" />
			<xs:element name="employmentType" type="EmploymentType" />
			<xs:element name="employmentStatus" type="EmploymentStatus" />
			<xs:element name="joiningDate" type="xs:date" />
			<xs:element name="reportingManager" type="Employee" />
			<xs:element name="employeeEmail" type="xs:string" nillable="true" />
			<xs:element name="employeeCode" type="xs:string" nillable="true" />
			<xs:element name="communicationChannel" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DenominationDetail">
		<xs:sequence>
			<xs:element name="denominationId" type="xs:int" />
			<xs:element name="denominationCode" type="xs:string" />
			<xs:element name="denominationText" type="xs:string" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="displayOrder" type="xs:int" />
			<xs:element name="denominationValue" type="xs:int" />
			<xs:element name="bundleSize" type="xs:int" />
			<xs:element name="paymentMode" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ListData">
		<xs:sequence>
			<xs:element name="detail" type="IdCodeName" />
			<xs:element name="content" type="IdCodeName" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddonList">
		<xs:sequence>
			<xs:element name="detail" type="IdCodeName" />
			<xs:element name="content" type="AddonData" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PaymentMode">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="type" type="xs:string" />
			<xs:element name="settlementType" type="xs:string" />
			<xs:element name="generatePull" type="xs:boolean" />
			<xs:element name="denominations" type="DenominationDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Department">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="division" type="Division" />
			<xs:element name="designations" type="Designation"
				minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Designation">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Product">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="hasSizeProfile" type="xs:boolean" />
			<xs:element name="hasAddons" type="xs:boolean" />
			<xs:element name="type" type="xs:int" />
			<xs:element name="subType" type="xs:int" />
			<xs:element name="attribute" type="xs:string" />
			<xs:element name="skuCode" type="xs:string" />
			<xs:element name="billType" type="BillType" />
			<xs:element name="inventoryTracked" type="xs:boolean" />
			<xs:element name="addOnProfile" type="xs:string" nillable="true" />
			<xs:element name="dimensionProfileId" type="xs:int" />
			<xs:element name="status" type="ProductStatus" />
			<xs:element name="startDate" type="xs:date" />
			<xs:element name="endDate" type="xs:date" />
			<xs:element name="supportsVariantLevelOrdering" type="xs:boolean" />
			<xs:sequence>
				<xs:element name="prices" type="ProductPrice" minOccurs="1"
					maxOccurs="unbounded" />
			</xs:sequence>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Address">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="line1" type="xs:string" />
			<xs:element name="line2" type="xs:string" nillable="true" />
			<xs:element name="line3" type="xs:string" nillable="true" />
			<xs:element name="locality" type="xs:string" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="state" type="xs:string" />
			<xs:element name="country" type="xs:string" />
			<xs:element name="zipCode" type="xs:string" nillable="true" />
			<xs:element name="contact1" type="xs:string" />
			<xs:element name="contact2" type="xs:string" nillable="true" />
			<xs:element name="addressType" type="xs:string" />
			<xs:element name="company" type="xs:string" nillable="true" />
			<xs:element name="latitude" type="xs:string" nillable="true" />
			<xs:element name="longitude" type="xs:string" nillable="true" />
			<xs:element name="preferredAddress" type="xs:boolean"
				nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Division">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="category" type="xs:string" />
			<xs:element name="company" type="Company" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Company">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="cin" type="xs:string" />
			<xs:element name="serviceTaxNumber" type="xs:string" />
			<xs:element name="websiteAddress" type="xs:string" />
			<xs:element name="registeredAddress" type="Address" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="BillType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MRP" />
			<xs:enumeration value="NET_PRICE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ProductPrice">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="dimension" type="xs:string" />
			<xs:element name="price" type="xs:decimal" />
			<xs:element name="cost" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ProductBasicDetail">
		<xs:sequence>
			<xs:element name="detail" type="IdCodeName" />
			<xs:element name="type" type="xs:int" />
			<xs:element name="subType" type="xs:int" />
			<xs:element name="isInventoryTracked" type="xs:boolean" />
			<xs:element name="variantLevelOrdering" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitBasicDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="referenceName" type="xs:string" />
			<xs:element name="status" type="UnitStatus" />
			<xs:element name="noOfTerminal" type="xs:int" />
			<xs:element name="noOfTakeawayTerminals" type="xs:int" />
			<xs:element name="category" type="UnitCategory" />
			<xs:element name="subCategory" type="UnitSubCategory" />
			<xs:element name="region" type="UnitRegion" />
			<xs:element name="city" type="xs:string" />
			<xs:element name="contact" type="xs:string" nillable="true" />
			<xs:element name="email" type="xs:string" nillable="true" />
			<xs:element name="tin" type="xs:string" nillable="true" />
			<xs:element name="address" type="xs:string" nillable="true" />
			<xs:element name="latitude" type="xs:string" nillable="true" />
			<xs:element name="longitude" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IdCodeName">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" nillable="true" />
			<xs:element name="type" type="xs:string" nillable="true" />
			<xs:element name="status" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AddonData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="code" type="xs:string" />
			<xs:element name="shortCode" type="xs:string" nillable="true" />
			<xs:element name="linkedProductId" type="xs:int" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PartnerDetail">
		<xs:sequence>
			<xs:element name="mappingId" type="xs:int" />
			<xs:element name="detail" type="IdCodeName" />
			<xs:element name="priority" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxProfile">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="profileId" type="xs:int" />
			<xs:element name="percentage" type="xs:decimal" />
			<xs:element name="type" type="TaxType" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="status" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UnitProductMappingData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="unit" type="IdCodeName" />
			<xs:element name="product" type="IdCodeName" />
			<xs:element name="price" type="ProductPrice" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PreAuthApi">
		<xs:sequence>
			<xs:element name="id" type="xs:int" nillable="true" />
			<xs:element name="api" type="xs:string" />
			<xs:element name="status" type="SwitchStatus" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="JWTToken">
		<xs:sequence>
			<xs:element name="sessionKey" type="xs:string" />
			<xs:element name="issuedAt" type="xs:date" />
			<xs:element name="unitId" type="xs:string" />
			<xs:element name="issuer" type="xs:string" />
			<xs:element name="userId" type="xs:string" />
			<xs:element name="terminalId" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="EmploymentStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="EmploymentType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FULL_TIME" />
			<xs:enumeration value="PART_TIME" />
			<xs:enumeration value="CONSULTANT" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="RecipeDetail">
		<xs:sequence>
			<xs:element name="recipeId" type="xs:int" />
			<xs:element name="product" type="ProductBasicDetail" />
			<xs:element name="dimension" type="IdCodeName" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="creationDate" type="xs:date" />
			<xs:element name="modificationDate" type="xs:date" />
			<xs:element name="startDate" type="xs:date" />
			<xs:element name="endDate" type="xs:date" />
			<xs:element name="deliverable" type="xs:boolean" />
			<xs:element name="ingridient" type="IngridientDetail" />
			<xs:element name="addons" type="IngridientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="dineInConsumbales" type="IngridientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="deliveryConsumables" type="IngridientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="takeawayConsumables" type="IngridientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngridientDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="scmProducts" type="IngridientProduct"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="menuProducts" type="IngridientProduct"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="variants" type="IngridientVaraint"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="compnents" type="IngridientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngridientProduct">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="category" type="IdCodeName" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="details" type="IngridientProductDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngridientProductDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="product" type="ProductBasicDetail" />
			<xs:element name="dimension" type="IdCodeName" />
			<xs:element name="uom" type="UnitOfMeasure" />
			<xs:element name="quantity" type="xs:decimal" />
			<xs:element name="default" type="xs:boolean" />
			<xs:element name="status" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngridientVaraint">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="product" type="ProductBasicDetail" />
			<xs:element name="dimension" type="IdCodeName" />
			<xs:element name="uom" type="UnitOfMeasure" />
			<xs:element name="status" type="xs:string" />
			<xs:element name="details" type="IngridientVariantDetail"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IngridientVariantDetail">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="alias" type="xs:string" />
			<xs:element name="quantity" type="xs:decimal" />
			<xs:element name="default" type="xs:boolean" />
			<xs:element name="status" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Consumable">
		<xs:sequence>
			<xs:element name="productId" type="xs:int" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="quantity" type="xs:decimal" />
			<xs:element name="uom" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ConsumptionData">
		<xs:sequence>
			<xs:element name="closureId" type="xs:int" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="businessDate" type="xs:date" />
			<xs:element name="startOrderId" type="xs:int" />
			<xs:element name="endOrderId" type="xs:int" />
			<xs:element name="consumables" type="Consumable"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AccessControlList">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="module" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="applicationName" type="ApplicationName" />
			<xs:element name="status" type="SwitchStatus" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GiftCardActivationRequest">
		<xs:sequence>
			<xs:element name="cardNumber" type="xs:string" />
			<xs:element name="cardSerial" type="xs:string" />
			<xs:element name="requestingUnit" type="xs:int" />
			<xs:element name="requestingEmployee" type="xs:int" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="IngridientType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NONE" />
			<xs:enumeration value="PRODUCT" />
			<xs:enumeration value="VARIENT" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TaxType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NET_PRICE_VAT" />
			<xs:enumeration value="MRP_VAT" />
			<xs:enumeration value="SURCHARGE" />
			<xs:enumeration value="SERVICE_TAX" />
			<xs:enumeration value="SB_CESS" />
			<xs:enumeration value="KK_CESS" />
			<xs:enumeration value="GST" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitCategory" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CAFE" />
			<xs:enumeration value="DELIVERY" />
			<xs:enumeration value="TAKE_AWAY" />
			<xs:enumeration value="COD" />
			<xs:enumeration value="KITCHEN" />
			<xs:enumeration value="WAREHOUSE" />
			<xs:enumeration value="OFFICE" />
			<xs:enumeration value="CHAI_MONK" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitSubCategory" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="BPC" />
			<xs:enumeration value="COD" />
			<xs:enumeration value="HSC" />
			<xs:enumeration value="MALL" />
			<xs:enumeration value="TG" />
			<xs:enumeration value="INTL" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="SwitchStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitRegion" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NCR" />
			<xs:enumeration value="MUMBAI" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ProductStatus" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACTIVE" />
			<xs:enumeration value="IN_ACTIVE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="OfferMetaDataType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="PRODUCT" />
			<xs:enumeration value="PRODUCT_SUB_CATEGORY" />
			<xs:enumeration value="PRODUCT_CATEGORY" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="PartnerType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INTERNAL" />
			<xs:enumeration value="EXTERNAL" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UnitOfMeasure" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="KG" />
			<xs:enumeration value="GM" />
			<xs:enumeration value="ML" />
			<xs:enumeration value="L" />
			<xs:enumeration value="PC" />
			<xs:enumeration value="PACKET" />
			<xs:enumeration value="SACHET" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ApplicationName" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="KETTLE_SERVICE" />
			<xs:enumeration value="MASTER_SERVICE" />
			<xs:enumeration value="KETTLE_ADMIN" />
			<xs:enumeration value="KETTLE_CRM" />
			<xs:enumeration value="SCM_SERVICE" />
			<xs:enumeration value="KETTLE_ANALYTICS" />
			<xs:enumeration value="KETTLE_CHECKLIST" />
			<xs:enumeration value="WORKSTATION" />
		</xs:restriction>
	</xs:simpleType>


	<!-- ChaiMonk related models -->

	<xs:complexType name="KioskCompanyDetails">
		<xs:sequence>
			<xs:element name="companyId" type="xs:int" nillable="true" />
			<xs:element name="companyName" type="xs:string" />
			<xs:element name="companyDomains" type="xs:string" minOccurs="1" maxOccurs="unbounded" />
			<xs:element name="contactDetails" type="IdCodeName" />
			<xs:element name="country" type="xs:string" />
			<xs:element name="companyEmail" type="xs:string" />
			<xs:element name="companyStatus" type="SwitchStatus" />
			<xs:element name="paymentMode" type="KioskPaymentMode" />
			<xs:element name="kioskSubDomain" type="xs:string"/>
			<xs:element name="officeList" type="KioskOfficeDetails" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="KioskOfficeDetails">
		<xs:sequence>
			<xs:element name="officeId" type="xs:int" nillable="true" />
			<xs:element name="officeName" type="xs:string"/>
			<xs:element name="officeShortCode" type="xs:string"/>
			<xs:element name="officeContact" type="IdCodeName"/>
			<xs:element name="companyDetails" type="KioskCompanyDetails" />
			<xs:element name="paymentMode" type="KioskPaymentMode"/>
			<xs:element name="region" type="UnitRegion"/>
			<xs:element name="tin" type="xs:string"/>
			<xs:element name="officeAddress" type="Address" />
			<xs:element name="officeStatus" type="SwitchStatus" />
			<xs:element name="locationList" type="KioskLocationDetails" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="KioskLocationDetails">
		<xs:sequence>
			<xs:element name="locationId" type="xs:int" nillable="true" />
			<xs:element name="locationName" type="xs:string" />
			<xs:element name="locationShortCode" type="xs:string"/>
			<xs:element name="locationAddress" type="xs:string"/>
			<xs:element name="officeDetails" type="KioskOfficeDetails" />
			<xs:element name="assigned" type="xs:boolean" default="false"/>
			<xs:element name="locationStatus" type="SwitchStatus" />
			<xs:element name="assignedUnit" type="IdCodeName" nillable="true" />
			<xs:element name="kioskMachines" type="KioskMachine" minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>


	<xs:complexType name="KioskMachine">
		<xs:sequence>
			<xs:element name="machineId" type="xs:int" nillable="true" />
			<xs:element name="uuid" type="xs:string" />
			<xs:element name="batchNo" type="xs:string"/>
			<xs:element name="installationUnit" type="IdCodeName" />
			<xs:element name="machineStatus" type="SwitchStatus" />
			<xs:element name="installationDate" type="xs:date"/>
			<xs:element name="deactivationDate" type="xs:date" />
			<xs:element name="manufacturingDate" type="xs:date" />
		</xs:sequence>
	</xs:complexType>

	<xs:simpleType name="KioskPaymentMode" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="COMPANY_PAID" />
			<xs:enumeration value="EMPLOYEE_PAID" />
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="LocalityMapping">
		<xs:sequence>
			<xs:element name="objectId" type="xs:string" />
			<xs:element name="primaryCOD" type="xs:string" />
			<xs:element name="secondaryCOD" type="xs:string" />
			<xs:element name="tertiaryCOD" type="xs:string"/>
			<xs:element name="city" type="xs:string" />
			<xs:element name="state" type="xs:string" />
			<xs:element name="country" type="xs:string" default="India"/>
			<xs:element name="locality" type="xs:string"/>
			<xs:element name="createdAt" type="xs:date" />
			<xs:element name="primaryUnitDeliveryTime" type="xs:string" />
			<xs:element name="primaryUnitId" type="xs:string" />
			<xs:element name="secUnitDeliveryTime" type="xs:string" />
			<xs:element name="secondaryUnitId" type="xs:string" />
			<xs:element name="tertiaryUnitDeliveryTime" type="xs:string" />
			<xs:element name="tertiaryUnitId" type="xs:string" />
			<xs:element name="updatedAt" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ManualBillBook">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="generatedForUnitId" type="IdCodeName"
				nillable="true" />
			<xs:element name="startNo" type="xs:int" />
			<xs:element name="transferOrderId" type="xs:int" />
			<xs:element name="endNo" type="xs:int" />
			<xs:element name="creationTime" type="xs:date" />
			<xs:element name="activationTime" type="xs:date" />
			<xs:element name="status" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	
		<xs:complexType name="FicoDetail">
		<xs:sequence>
			<xs:element name="ficoDetailId" type="xs:int" />
			<xs:element name="unitId" type="IdCodeName"
				nillable="true" />
			<xs:element name="name" type="xs:string" />
			<xs:element name="companyName" type="xs:string" />
			<xs:element name="addresssId" type="xs:int" />
			<xs:element name="gstin" type="xs:string" />
			<xs:element name="emailId" type="xs:string" />
			<xs:element name="contact1" type="xs:string" />
			<xs:element name="contact2" type="xs:string" nillable="true" />
			<xs:element name="reportingEmailId" type="xs:string" />
			<xs:element name="offerCode" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="TokenizedApi">
		<xs:sequence>
			<xs:element name="apiId" type="xs:int" nillable="true" />
			<xs:element name="apiName" type="xs:string" nillable="false" />
			<xs:element name="apiMethod" type="xs:string" nillable="false" />
			<xs:element name="application" type="ApplicationName" />
			<xs:element name="status" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>

    <xs:complexType name="MonkConfiguration">
        <xs:sequence>
            <xs:element name="unitId" type="xs:int"/>
            <xs:element name="status" type="SwitchStatus" />
            <xs:element name="updatedBy" type="IdCodeName" />
            <xs:element name="updatedAt" type="xs:date" />
            <xs:element name="configurationList" type="MonkConfigurationValue" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MonkConfigurationValue">
        <xs:sequence>
            <xs:element name="confId" type="xs:int" nillable="true" />
            <xs:element name="scope" type="MonkConfigurationScope" />
            <xs:element name="type" type="MonkConfigurationType" nillable="true" />
            <xs:element name="value" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="MonkAttribute">
        <xs:sequence>
            <xs:element name="id" type="xs:int" nillable="true" />
            <xs:element name="attr" type="xs:string" />
            <xs:element name="scope" type="xs:string" />
            <xs:element name="label" type="xs:string" />
            <xs:element name="type" type="xs:string" />
            <xs:element name="status" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="MonkConfigurationScope" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ALL" />
            <xs:enumeration value="MONK1" />
            <xs:enumeration value="MONK2" />
            <xs:enumeration value="MONK3" />
            <xs:enumeration value="MONK4" />
            <xs:enumeration value="MONK5" />
            <xs:enumeration value="MONK6" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="MonkConfigurationType" final="restriction">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MONK_ENABLED" />
            <xs:enumeration value="HOT_COLD_MERGED" />
            <xs:enumeration value="NUMBER_OF_MONKS" />
            <xs:enumeration value="MAC_ADDRESS" />
            <xs:enumeration value="WEIGHT_COEFFICIENT" />
            <xs:enumeration value="WATER_DENSITY" />
            <xs:enumeration value="MILK_DENSITY" />
            <xs:enumeration value="SMALL_PAN_WEIGHT" />
            <xs:enumeration value="BIG_PAN_WEIGHT" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="UnitBusinessType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="COCO" />
			<xs:enumeration value="FICO" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:complexType name="ConfigAttributeDefinition">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="attributeType" type="xs:string" />
			<xs:element name="attributeCode" type="xs:string" />
			<xs:element name="attributeName" type="xs:string" />
			<xs:element name="defaultValue" type="xs:string" nillable="true" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ConfigAttributeValue">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="attributeDef" type="ConfigAttributeDefinition" />
			<xs:element name="attributeValue" type="xs:string" />
			<xs:element name="applicationName" type="xs:string" />
		</xs:sequence>
	</xs:complexType>

    <xs:complexType name="AppBuildVersionData">
        <xs:sequence>
            <xs:element name="appName" type="xs:string"/>
            <xs:element name="unitId" type="xs:int"/>
            <xs:element name="version" type="xs:string"/>
            <xs:element name="url" type="xs:string" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AppsVersionMetadata">
        <xs:sequence>
            <xs:element name="id" type="xs:int" />
            <xs:element name="major" type="xs:string" />
            <xs:element name="minor" type="xs:string" />
            <xs:element name="patch" type="xs:string"/>
            <xs:element name="status" type="SwitchStatus"/>
            <xs:element name="apps" type="AppBuildData" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="AppBuildData">
        <xs:sequence>
            <xs:element name="appName" type="xs:string" />
            <xs:element name="uploadUrl" type="xs:string" />
            <xs:element name="units" type="IdCodeName" maxOccurs="unbounded" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>