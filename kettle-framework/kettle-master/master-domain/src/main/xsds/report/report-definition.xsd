<?xml version="1.0"?>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:element name="ReportCategories">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="category" type="ReportCategory"
					minOccurs="1" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="ReportCategory">
		<xs:sequence>
			<xs:element name="report" type="ReportData" minOccurs="1"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" />
		<xs:attribute name="name" type="xs:string" />
		<xs:attribute name="type" type="xs:string" />
		<xs:attribute name="accessCode" type="xs:string" />
		<xs:attribute name="schedule" type="xs:string" use="optional"/>
		<xs:attribute name="toEmails" type="xs:string" use="optional" />
		<xs:attribute name="fromEmail" type="xs:string" use="optional" />
		<xs:attribute name="attachmentType" type="ReportOutput" use="optional" />
		<xs:attribute name="compress" type="xs:boolean" use="optional" />
	</xs:complexType>
	<xs:complexType name="ReportData">
		<xs:sequence>
			<xs:element name="content" type="xs:string" />
			<xs:element name="param" type="ReportParam" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="environment" type="ExecutionEnvironment"
				nillable="true" default="UAT" />
			<xs:element name="notification" type="ReportNotification" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" />
		<xs:attribute name="name" type="xs:string" />
		<xs:attribute name="executionType" type="ExecutionType" />
		<xs:attribute name="returnType" type="xs:string" />
	</xs:complexType>
	<xs:complexType name="ReportParam">
		<xs:sequence>
			<xs:element name="value" type="xs:string" />
		</xs:sequence>
		<xs:attribute name="name" type="xs:string" />
		<xs:attribute name="displayName" type="xs:string" />
		<xs:attribute name="dataType" type="DataType" />
		<xs:attribute name="multiValued" type="xs:boolean" use="optional" />
		<xs:attribute name="optional" type="xs:boolean" default="false"
			use="optional" />
		<xs:attribute name="delimiter" type="xs:string" default=","
			use="optional" />
		<xs:attribute name="format" type="xs:string" use="optional" />
	</xs:complexType>
	<xs:complexType name="ReportNotification">
		<xs:sequence>
			<xs:element name="type" type="ReportNotificationType" />
			<xs:element name="notificationIdIndex" type="xs:int" nillable="true"/>
			<xs:element name="notificationType" type="UserType" nillable="true"/>
			<xs:element name="channel" type="xs:string" nillable="true"/>
			<xs:element name="messageIndex" type="xs:int" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="DataType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LONG" />
			<xs:enumeration value="STRING" />
			<xs:enumeration value="INTEGER" />
			<xs:enumeration value="DOUBLE" />
			<xs:enumeration value="BOOLEAN" />
			<xs:enumeration value="DATE" />
			<xs:enumeration value="TIMESTAMP" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ReportNotificationType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SMS" />
			<xs:enumeration value="SLACK" />
			<xs:enumeration value="EMAIL" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExecutionType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SQL" />
			<xs:enumeration value="JAVA" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ExecutionEnvironment" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UAT" />
			<xs:enumeration value="DEV" />
			<xs:enumeration value="PROD" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="ReportOutput" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="INLINE" />
			<xs:enumeration value="CSV" />
			<xs:enumeration value="EXCEL" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="UserType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="DIRECT" />
			<xs:enumeration value="CHANNEL" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
