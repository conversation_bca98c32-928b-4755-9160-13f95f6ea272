package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 10-03-2017.
 */
public class ChannelPartnerDetail extends IdCodeName implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -3476713812722830735L;
	private Integer creditAccount;
	private BigDecimal commission;
	private BigDecimal taxRate;
	private Boolean apiIntegrated;
    private String partnerName;


    public void setCreditAccount(Integer creditAccount) {
        this.creditAccount = creditAccount;
    }

    public Integer getCreditAccount() {
        return creditAccount;
    }

    public BigDecimal getCommission() {
		return commission;
	}

	public void setCommission(BigDecimal commission) {
		this.commission = commission;
	}
	
    public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public Boolean getApiIntegrated() {
        return apiIntegrated;
    }

    public void setApiIntegrated(Boolean apiIntegrated) {
        this.apiIntegrated = apiIntegrated;
    }

    public String getPartnerName() {
        return partnerName;
    }
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((code == null) ? 0 : code.hashCode());
        result = prime * result + id;
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        IdCodeName other = (IdCodeName) obj;
        if (code == null) {
            if (other.code != null)
                return false;
        } else if (!code.equals(other.code))
            return false;
        if (id != other.id)
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        } else if (!name.equals(other.name))
            return false;
        return true;
    }

    @Override
    public String toString() {
        return "ChannelPartner [id=" + id + ", name=" + name + "]";
    }


}
