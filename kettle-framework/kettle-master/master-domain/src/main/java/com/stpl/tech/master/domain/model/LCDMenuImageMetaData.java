package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LCDMenuImageMetaData {



    @Builder.Default
    private List<String> regions  = new ArrayList<>();

    @Builder.Default
    private List<String> priceProfiles = new ArrayList<>();


    @Builder.Default
    private List<String> slots = new ArrayList<>();

    @Builder.Default
    private List<String> lcdTypes = new ArrayList<>();

    @Builder.Default
    private List<LCMMenuVariantMetadataGroupDomain> variants = new ArrayList<>();


}
