/**
 * 
 */
package com.stpl.tech.master.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

/**
 * <AUTHOR>
 *
 */
public class OfferWithFreeItemData {

	private int productId;
	private String dimension;
	private int quantity;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	private BigDecimal value;
	private OfferValueType type;

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getValue() {
		return value;
	}

	public void setValue(BigDecimal value) {
		this.value = value;
	}

	public OfferValueType getType() {
		return type;
	}

	public void setType(OfferValueType type) {
		this.type = type;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	
}
