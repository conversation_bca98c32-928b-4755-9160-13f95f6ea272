package com.stpl.tech.master.domain.model;

import java.io.Serializable;

public class UnitPaymentModeMappingDetail implements Serializable {

    private static final long serialVersionUID = -121850921144456678L;
    private Integer unitPaymentModeMappingId;
    private PaymentMode paymentMode;
    private UnitBasicDetail unitDetail;
    private String mappingStatus;

    public Integer getUnitPaymentModeMappingId() {
        return unitPaymentModeMappingId;
    }

    public void setUnitPaymentModeMappingId(Integer unitPaymentModeMappingId) {
        this.unitPaymentModeMappingId = unitPaymentModeMappingId;
    }

    public PaymentMode getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(PaymentMode paymentMode) {
        this.paymentMode = paymentMode;
    }

    public UnitBasicDetail getUnitDetail() {
        return unitDetail;
    }

    public void setUnitDetail(UnitBasicDetail unitDetail) {
        this.unitDetail = unitDetail;
    }

    public String getMappingStatus() {
        return mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }
}
