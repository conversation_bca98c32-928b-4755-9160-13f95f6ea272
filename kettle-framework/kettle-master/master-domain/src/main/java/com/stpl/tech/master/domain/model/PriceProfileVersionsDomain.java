package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class PriceProfileVersionsDomain {
    private Integer priceProfileVersionsId;
    private Integer priceProfileId;
    private Integer versionNo;
    private String status;
    private Date creationTime;
    private Integer createdBy;
    private Integer updatedBy;
    private Date updationTime;
}
