//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.15 at 03:51:51 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SMSClientName.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="SMSClientName"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="SOLUTIONS_INFINI"/&gt;
 *     &lt;enumeration value="SMS_GUPSHUP"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "SMSClientName")
@XmlEnum
public enum SMSClientName {

    @XmlEnumValue("SOLUTIONS_INFINI")
    SOLUTION_INFINI("SOLUTIONS_INFINI"),
    @XmlEnumValue("SMS_GUPSHUP")
    SMS_GUPSHUP("SMS_GUPSHUP");
    private final String value;

    SMSClientName(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static SMSClientName fromValue(String v) {
        for (SMSClientName c: SMSClientName.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
