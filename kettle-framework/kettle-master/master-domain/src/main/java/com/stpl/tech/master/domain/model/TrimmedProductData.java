package com.stpl.tech.master.domain.model;

import java.util.List;

public class TrimmedProductData {
    private Integer id;
    private String name;
    private List<String> dimensions;

    public TrimmedProductData() {
    }

    public TrimmedProductData(Integer id, String name, List<String> dimensions) {
        this.id = id;
        this.name = name;
        this.dimensions = dimensions;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getDimensions() {
        return dimensions;
    }

    public void setDimensions(List<String> dimensions) {
        this.dimensions = dimensions;
    }
}
