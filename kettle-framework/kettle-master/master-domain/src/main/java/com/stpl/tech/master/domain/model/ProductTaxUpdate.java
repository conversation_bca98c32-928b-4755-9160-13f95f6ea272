package com.stpl.tech.master.domain.model;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductTaxUpdate", propOrder = { "employeeName", "employeeId", "details" })
public class ProductTaxUpdate {

	@XmlElement(required = true)
	protected String employeeName;
	protected int employeeId;
	List<ProductTaxDetail> details;

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public List<ProductTaxDetail> getDetails() {
		if (details == null) {
			details = new ArrayList<ProductTaxDetail>();
		}
		return details;
	}

}
