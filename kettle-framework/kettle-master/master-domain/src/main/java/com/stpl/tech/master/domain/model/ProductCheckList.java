package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductCheckList {

    Product product;

    IdCodeName dimension;

    List<IdCodeName> units;

    Integer brandId;

    Date liveDate;

}
