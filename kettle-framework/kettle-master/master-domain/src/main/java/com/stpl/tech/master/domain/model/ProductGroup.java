package com.stpl.tech.master.domain.model;



import java.util.Date;
import java.util.List;


public class ProductGroup {

    private Integer groupId;
    private String groupName;
    private String groupTag;
    private String groupType;
    private MenuType menuType;
    private String groupDescription;
    private List<ProductSequence> productSequenceList;
    private IdName createdBy;
    private IdName updatedBy;
    private Date creationTime;
    private Integer cloneId;
    private MenuApp menuApp;
    private ProductGroupImage icon;
    private String categoryTag;
    private String menuCategoryImage;
    private String menuCategoryDetails;

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupTag() {
        return groupTag;
    }

    public void setGroupTag(String groupTag) {
        this.groupTag = groupTag;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public String getGroupDescription() {
        return groupDescription;
    }

    public void setGroupDescription(String groupDescription) {
        this.groupDescription = groupDescription;
    }

    public List<ProductSequence> getProductSequenceList() {
        return productSequenceList;
    }

    public void setProductSequenceList(List<ProductSequence> productSequenceList) {
        this.productSequenceList = productSequenceList;
    }

    public IdName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdName createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public IdName getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(IdName updatedBy) {
        this.updatedBy = updatedBy;
    }

    public MenuType getMenuType() {
        return menuType;
    }

    public void setMenuType(MenuType menuType) {
        this.menuType = menuType;
    }

    public Integer getCloneId() {
        return cloneId;
    }

    public void setCloneId(Integer cloneId) {
        this.cloneId = cloneId;


    }

    public ProductGroupImage getIcon() {
        return icon;
    }

    public void setIcon(ProductGroupImage icon) {
        this.icon = icon;
    }

    public MenuApp getMenuApp() {
        return menuApp;
    }

    public void setMenuApp(MenuApp menuApp) {
        this.menuApp = menuApp;
    }

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public String getMenuCategoryImage() {
        return menuCategoryImage;
    }

    public void setMenuCategoryImage(String menuCategoryImage) {
        this.menuCategoryImage = menuCategoryImage;
    }

    public String getMenuCategoryDetails() {
        return menuCategoryDetails;
    }

    public void setMenuCategoryDetails(String menuCategoryDetails) {
        this.menuCategoryDetails = menuCategoryDetails;
    }
}


