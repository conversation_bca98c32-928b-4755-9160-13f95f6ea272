/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.02.16 at 04:36:27 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for OfferDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OfferDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="category" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="text" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="startDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="endDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="minValue" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="includeTaxes" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="validateCustomer" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="minQuantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="minLoyalty" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="offerValue" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="minItemCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="offerScope" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="priority" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="partners" type="{http://www.w3schools.com}IdCodeName" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="metaDataMappings" type="{http://www.w3schools.com}IdCodeName" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OfferDetail", propOrder = {
    "id",
    "category",
    "type",
    "text",
    "description",
    "startDate",
    "endDate",
    "minValue",
    "includeTaxes",
    "status",
    "validateCustomer",
    "minQuantity",
    "minLoyalty",
    "offerValue",
    "minItemCount",
    "offerScope",
    "emailDomain",
    "priority",
    "partners",
    "metaDataMappings",
    "offerWithFreeItem"
})
public class OfferDetail implements Serializable {

    private static final long serialVersionUID = -6917393831012207579L;
    protected int id;
    @XmlElement(required = true)
    protected String category;
    @XmlElement(required = true)
    protected String type;
    @XmlElement(required = true)
    protected String text;
    @XmlElement(required = true)
    protected String description;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date startDate;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date endDate;
    protected int minValue;
    protected boolean includeTaxes;
    @XmlElement(required = true)
    protected String status;
    protected boolean validateCustomer;
    protected Boolean removeLoyalty;
    protected int minQuantity;
    protected int minLoyalty;
    protected int offerValue;
    protected int minItemCount;
    @XmlElement(required = true)
    protected String offerScope;
    @XmlElement(required = true)
    protected String emailDomain;
    protected int priority;
    @XmlElement(required = true)
    protected IdName accountsCategory;
    protected OfferWithFreeItemData offerWithFreeItem;
    protected boolean isPrepaid;
    protected BigDecimal prepaidAmount;
    protected BigDecimal maxBillValue;
    protected List<MappingIdCodeName> partners;
    protected List<IdCodeName> metaDataMappings;
    protected boolean otpRequired;
    protected BigDecimal maxDiscountAmount;
    protected List<CouponMapping> couponMappingList;
    protected Boolean frequencyApplicable;
    protected String frequencyStrategy;
    protected Integer frequencyCount;
    protected Integer dailyFrequencyCount;
    protected Integer applicableHour;
    protected Boolean autoApplicableforUnit;
    protected String termsAndConditions;
    protected Integer maxQuantity;

    protected Integer loyaltyBurnPoints;

    protected String budgetCategory;

    protected boolean signupOfferApplicable;

    @XmlElement(required = true)
    protected Integer brandId;

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the category property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategory() {
        return category;
    }

    /**
     * Sets the value of the category property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategory(String value) {
        this.category = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the text property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getText() {
        return text;
    }

    /**
     * Sets the value of the text property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setText(String value) {
        this.text = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the startDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * Sets the value of the startDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStartDate(Date value) {
        this.startDate = value;
    }

    /**
     * Gets the value of the endDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * Sets the value of the endDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEndDate(Date value) {
        this.endDate = value;
    }

    /**
     * Gets the value of the minValue property.
     * 
     */
    public int getMinValue() {
        return minValue;
    }

    /**
     * Sets the value of the minValue property.
     * 
     */
    public void setMinValue(int value) {
        this.minValue = value;
    }

    /**
     * Gets the value of the includeTaxes property.
     * 
     */
    public boolean isIncludeTaxes() {
        return includeTaxes;
    }

    /**
     * Sets the value of the includeTaxes property.
     * 
     */
    public void setIncludeTaxes(boolean value) {
        this.includeTaxes = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the validateCustomer property.
     * 
     */
    public boolean isValidateCustomer() {
        return validateCustomer;
    }

    /**
     * Sets the value of the validateCustomer property.
     * 
     */
    public void setValidateCustomer(boolean value) {
        this.validateCustomer = value;
    }

    /**
     * Gets the value of the minQuantity property.
     * 
     */
    public int getMinQuantity() {
        return minQuantity;
    }

    /**
     * Sets the value of the minQuantity property.
     * 
     */
    public void setMinQuantity(int value) {
        this.minQuantity = value;
    }

    /**
     * Gets the value of the minLoyalty property.
     * 
     */
    public int getMinLoyalty() {
        return minLoyalty;
    }

    /**
     * Sets the value of the minLoyalty property.
     * 
     */
    public void setMinLoyalty(int value) {
        this.minLoyalty = value;
    }

    /**
     * Gets the value of the offerValue property.
     * 
     */
    public int getOfferValue() {
        return offerValue;
    }

    /**
     * Sets the value of the offerValue property.
     * 
     */
    public void setOfferValue(int value) {
        this.offerValue = value;
    }

    /**
     * Gets the value of the minItemCount property.
     * 
     */
    public int getMinItemCount() {
        return minItemCount;
    }

    /**
     * Sets the value of the minItemCount property.
     * 
     */
    public void setMinItemCount(int value) {
        this.minItemCount = value;
    }

    /**
     * Gets the value of the offerScope property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOfferScope() {
        return offerScope;
    }

    /**
     * Sets the value of the offerScope property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOfferScope(String value) {
        this.offerScope = value;
    }

    /**
     * Gets the value of the priority property.
     * 
     */
    public int getPriority() {
        return priority;
    }

    /**
     * Sets the value of the priority property.
     * 
     */
    public void setPriority(int value) {
        this.priority = value;
    }

    /**
     * Gets the value of the partners property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the partners property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPartners().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IdCodeName }
     * 
     * 
     */
    public List<MappingIdCodeName> getPartners() {
        if (partners == null) {
            partners = new ArrayList<MappingIdCodeName>();
        }
        return this.partners;
    }

    /**
     * Gets the value of the metaDataMappings property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the metaDataMappings property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMetaDataMappings().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IdCodeName }
     * 
     * 
     */
    public List<IdCodeName> getMetaDataMappings() {
        if (metaDataMappings == null) {
            metaDataMappings = new ArrayList<IdCodeName>();
        }
        return this.metaDataMappings;
    }

	public String getEmailDomain() {
		return emailDomain;
	}

	public void setEmailDomain(String emailDomain) {
		this.emailDomain = emailDomain;
	}

	public IdName getAccountsCategory() {
		return accountsCategory;
	}

	public void setAccountsCategory(IdName accountsCategory) {
		this.accountsCategory = accountsCategory;
	}

	public OfferWithFreeItemData getOfferWithFreeItem() {
		return offerWithFreeItem;
	}

	public void setOfferWithFreeItem(OfferWithFreeItemData offerWithFreeItem) {
		this.offerWithFreeItem = offerWithFreeItem;
	}

	public Boolean getRemoveLoyalty() {
		return removeLoyalty;
	}

	public void setRemoveLoyalty(Boolean removeLoyalty) {
		this.removeLoyalty = removeLoyalty;
	}

    public boolean isPrepaid() {
        return isPrepaid;
    }

    public void setPrepaid(boolean prepaid) {
        isPrepaid = prepaid;
    }

    public BigDecimal getPrepaidAmount() {
        return prepaidAmount;
    }

    public void setPrepaidAmount(BigDecimal prepaidAmount) {
        this.prepaidAmount = prepaidAmount;
    }

	public BigDecimal getMaxBillValue() {
		return maxBillValue;
	}

	public void setMaxBillValue(BigDecimal maxBillValue) {
		this.maxBillValue = maxBillValue;
	}

    public boolean getOtpRequired() {
        return otpRequired;
    }

    public void setOtpRequired(boolean otpRequired) {
        this.otpRequired = otpRequired;
    }

    public BigDecimal getMaxDiscountAmount() {
        return maxDiscountAmount;
    }

    public void setMaxDiscountAmount(BigDecimal maxDiscountAmount) {
        this.maxDiscountAmount = maxDiscountAmount;
    }

	public List<CouponMapping> getCouponMappingList() {
		if(couponMappingList == null) {
			couponMappingList = new ArrayList<>();
		}
		return couponMappingList;
	}

	public void setCouponMappingList(List<CouponMapping> couponMappingList) {
		this.couponMappingList = couponMappingList;
	}

    public Boolean isFrequencyApplicable() {
        return frequencyApplicable;
    }

    public void setFrequencyApplicable(Boolean frequencyApplicable) {
        this.frequencyApplicable = frequencyApplicable;
    }
    
    public String getFrequencyStrategy() {
		return frequencyStrategy;
	}

	public void setFrequencyStrategy(String frequencyStrategy) {
		this.frequencyStrategy = frequencyStrategy;
	}

	public Integer getFrequencyCount() {
        return frequencyCount;
    }

    public void setFrequencyCount(Integer frequencyCount) {
        this.frequencyCount = frequencyCount;
    }

    public Integer getDailyFrequencyCount() {
        return dailyFrequencyCount;
    }

    public void setDailyFrequencyCount(Integer dailyFrequencyCount) {
        this.dailyFrequencyCount = dailyFrequencyCount;
    }

    public Integer getApplicableHour() {
        return applicableHour;
    }

    public void setApplicableHour(Integer applicableHour) {
        this.applicableHour = applicableHour;
    }


    public Boolean getAutoApplicableforUnit() {
        return autoApplicableforUnit;
    }

    public void setAutoApplicableforUnit(Boolean autoApplicableforUnit) {
        this.autoApplicableforUnit = autoApplicableforUnit;
    }

    public String getTermsAndConditions() {
        return termsAndConditions;
    }

    public void setTermsAndConditions(String termsAndConditions) {
        this.termsAndConditions = termsAndConditions;
    }

    public Boolean getFrequencyApplicable() {
        return frequencyApplicable;
    }

    public Integer getMaxQuantity() {
        return maxQuantity;
    }

    public void setMaxQuantity(Integer maxQuantity) {
        this.maxQuantity = maxQuantity;
    }

    public Integer getLoyaltyBurnPoints() {
        return loyaltyBurnPoints;
    }

    public void setLoyaltyBurnPoints(Integer loyaltyBurnPoints) {
        this.loyaltyBurnPoints = loyaltyBurnPoints;
    }

    public String getBudgetCategory() {
        return budgetCategory;
    }

    public void setBudgetCategory(String budgetCategory) {
        this.budgetCategory = budgetCategory;
    }

    public boolean isSignupOfferApplicable() {
        return signupOfferApplicable;
    }

    public void setSignupOfferApplicable(boolean signupOfferApplicable) {
        this.signupOfferApplicable = signupOfferApplicable;
    }

    public Integer getBrandId() { return this.brandId; }

    public void setBrandId(Integer brandId) { this.brandId = brandId; }

}
