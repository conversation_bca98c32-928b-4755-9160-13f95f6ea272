package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class PriceProfileRangeValueDetail  implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 3964913613805058969L;
	private Integer profileProfileRangeValuesId;
    private Integer priceProfileId;
    private BigDecimal startPrice;
    private BigDecimal endPrice;
    private BigDecimal deltaPrice;
    private String rangeValuesStatus;
    private Date lastUpdateTime;
    private String lastUpdatedBy;
    private Date rangeValuesActivationTime;

    public Integer getProfileProfileRangeValuesId() {
        return profileProfileRangeValuesId;
    }

    public void setProfileProfileRangeValuesId(Integer profileProfileRangeValuesId) {
        this.profileProfileRangeValuesId = profileProfileRangeValuesId;
    }

    public Integer getPriceProfileId() {
        return priceProfileId;
    }

    public void setPriceProfileId(Integer priceProfileId) {
        this.priceProfileId = priceProfileId;
    }

    public BigDecimal getStartPrice() {
        return startPrice;
    }

    public void setStartPrice(BigDecimal startPrice) {
        this.startPrice = startPrice;
    }

    public BigDecimal getEndPrice() {
        return endPrice;
    }

    public void setEndPrice(BigDecimal endPrice) {
        this.endPrice = endPrice;
    }

    public BigDecimal getDeltaPrice() {
        return deltaPrice;
    }

    public void setDeltaPrice(BigDecimal deltaPrice) {
        this.deltaPrice = deltaPrice;
    }

    public String getRangeValuesStatus() {
        return rangeValuesStatus;
    }

    public void setRangeValuesStatus(String rangeValuesStatus) {
        this.rangeValuesStatus = rangeValuesStatus;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getRangeValuesActivationTime() {
        return rangeValuesActivationTime;
    }

    public void setRangeValuesActivationTime(Date rangeValuesActivationTime) {
        this.rangeValuesActivationTime = rangeValuesActivationTime;
    }
}
