package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CondimentItemData implements Serializable {

     private static final long serialVersionUID = 2296196800288376411L;

     private  Integer itemId;
     private String name;
     private Integer quantity;

}
