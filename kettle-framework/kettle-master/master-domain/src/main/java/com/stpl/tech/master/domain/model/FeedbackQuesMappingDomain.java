package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FeedbackQuesMappingDomain {

    private Integer questionId;
    private String questionType;
    private List<String> unitName;
    private Integer updateBy;
    private List<Integer> mappingIds;
}
