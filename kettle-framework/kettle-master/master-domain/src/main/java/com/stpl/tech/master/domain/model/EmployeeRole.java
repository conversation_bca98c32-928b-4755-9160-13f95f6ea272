/**
 * 
 */
package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class EmployeeRole implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6508239324249259766L;

	private int employeeId;
	private List<Integer> roles;
	private Integer updatedBy;
	private Integer userPolicyId;
	private String policyName;
	private String policyDescription;
	private Boolean updatePolicy;

	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	public List<Integer> getRoles() {
		if (roles == null) {
			roles = new ArrayList<>();
		}
		return roles;
	}

	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	public void setRoles(List<Integer> roles) {
		this.roles = roles;
	}

	public Integer getUserPolicyId() {
		return userPolicyId;
	}

	public void setUserPolicyId(Integer userPolicyId) {
		this.userPolicyId = userPolicyId;
	}

	public String getPolicyName() {
		return policyName;
	}

	public void setPolicyName(String policyName) {
		this.policyName = policyName;
	}

	public String getPolicyDescription() {
		return policyDescription;
	}

	public void setPolicyDescription(String policyDescription) {
		this.policyDescription = policyDescription;
	}

	public Boolean getUpdatePolicy() {
		return updatePolicy;
	}

	public void setUpdatePolicy(Boolean updatePolicy) {
		this.updatePolicy = updatePolicy;
	}
}
