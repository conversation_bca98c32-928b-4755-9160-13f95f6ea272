package com.stpl.tech.master.domain.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@ExcelSheet(value = "Unit Price Profile Mapping")
public class UnitPriceProfileMappingSheet {
    private Integer id;


    @ExcelField
    private Integer unitId;
    @ExcelField
    private String unitName;

    @ExcelField
    private String priceProfile;

    @ExcelField
    private Integer priceProfileVersion;

    @ExcelField
    private Integer channelPartnerId;

    @ExcelField
    private Integer brandId;

    @ExcelField
    private String mappingStatus;


    private Integer createdBy;


    private Date creationTime;


    private Date updationTime;


    private Integer updatedBy;
}
