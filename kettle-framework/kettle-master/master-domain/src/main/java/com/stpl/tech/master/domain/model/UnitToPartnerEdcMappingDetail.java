package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UnitToPartnerEdcMappingDetail implements Serializable {

        @Id
        private Integer id;
        private UnitBasicDetail unitBasicDetail;
        private String edcMappingStatus;
        private String partnerName;
        private String merchantId;
        private String terminalId;
        private String merchantKey;
        private String version;
        private String tid;
}
