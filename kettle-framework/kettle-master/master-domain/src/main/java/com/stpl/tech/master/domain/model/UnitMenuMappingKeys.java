/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.04.04 at 12:22:00 PM IST
//

package com.stpl.tech.master.domain.model;

import org.apache.commons.lang.builder.HashCodeBuilder;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.List;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "")
@XmlRootElement(name = "UnitMenuMappingKeys")
public class UnitMenuMappingKeys implements Serializable {

    private static final long serialVersionUID = -93045147658127L;

    private List<Integer> mappingIds;

    private Boolean status;

    public UnitMenuMappingKeys() {
    }

    public UnitMenuMappingKeys(List<Integer> mappingIds, Boolean status) {
        this.mappingIds = mappingIds;
        this.status = status;
    }

    @Override
    public String toString() {
        return "UnitMenuMappingKeys{" +
                "mappingIds='" + mappingIds + '\'' +
                ", status='" + status + '\'' +
                '}';
    }

    public List<Integer> getMappingIds() {
        return mappingIds;
    }

    public void setMappingIds(List<Integer> mappingIds) {
        this.mappingIds = mappingIds;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }
}
