/**
 * 
 */
package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CouponCloneRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4243538110844615472L;

	private List<String> identifier;
	private IdentifierType identifierType;
	private String startDay; // yyyy-MM-dd
	private Integer validDays;
	private Integer usageCount;
	private String code;
	private String prefix;
	private Integer applicableUnitId;
	private String applicableRegion;
	private Integer maxCustomerUsage;

	public List<String> getIdentifier() {
		if (identifier == null) {
			identifier = new ArrayList<String>();
		}
		return identifier;
	}

	public void setIdentifier(List<String> identifier) {
		this.identifier = identifier;
	}

	public IdentifierType getIdentifierType() {
		return identifierType;
	}

	public void setIdentifierType(IdentifierType identifierType) {
		this.identifierType = identifierType;
	}

	public String getStartDay() {
		return startDay;
	}

	public void setStartDay(String startDay) {
		this.startDay = startDay;
	}

	public Integer getValidDays() {
		return validDays;
	}

	public void setValidDays(Integer validDays) {
		this.validDays = validDays;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Integer getUsageCount() {
		return usageCount;
	}

	public void setUsageCount(Integer usageCount) {
		this.usageCount = usageCount;
	}

	public String getPrefix() {
		return prefix;
	}

	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}

	
	public Integer getApplicableUnitId() {
		return applicableUnitId;
	}

	public void setApplicableUnitId(Integer applicableUnitId) {
		this.applicableUnitId = applicableUnitId;
	}

	public String getApplicableRegion() {
		return applicableRegion;
	}

	public void setApplicableRegion(String applicableRegion) {
		this.applicableRegion = applicableRegion;
	}

	public Integer getMaxCustomerUsage() {
		return maxCustomerUsage;
	}

	public void setMaxCustomerUsage(Integer maxCustomerUsage) {
		this.maxCustomerUsage = maxCustomerUsage;
	}

	@Override
	public String toString() {
		return "CouponCloneRequest [identifier=" + identifier + ", identifierType=" + identifierType + ", startDay="
				+ startDay + ", validDays=" + validDays + ", usageCount=" + usageCount + ", code=" + code + ", prefix="
				+ prefix + ", applicableUnitId=" + applicableUnitId + ", applicableRegion=" + applicableRegion + "]";
	}

}