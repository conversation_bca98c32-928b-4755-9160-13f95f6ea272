package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.List;

public class UnitProductPriceBulkRequest implements Serializable {

    private static final long serialVersionUID = -3647314545268145392L;
    private String unitCategory;
    private Integer brandId;
    private String regions;
    private List<String> pricingProfile;
    private List<String> productIds;

    public String getUnitCategory() {
        return unitCategory;
    }

    public void setUnitCategory(String unitCategory) {
        this.unitCategory = unitCategory;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getRegions() {
        return regions;
    }

    public void setRegions(String regions) {
        this.regions = regions;
    }

    public List<String> getPricingProfile() {
        return pricingProfile;
    }

    public void setPricingProfile(List<String> pricingProfile) {
        this.pricingProfile = pricingProfile;
    }

    public List<String> getProductIds() {
        return productIds;
    }

    public void setProductIds(List<String> productIds) {
        this.productIds = productIds;
    }

    @Override
    public String toString() {
        return "UnitProductPriceBulkRequest{" +
                "unitCategory='" + unitCategory + '\'' +
                ", brandId=" + brandId +
                ", regions='" + regions + '\'' +
                ", pricingProfile='" + pricingProfile + '\'' +
                '}';
    }
}
