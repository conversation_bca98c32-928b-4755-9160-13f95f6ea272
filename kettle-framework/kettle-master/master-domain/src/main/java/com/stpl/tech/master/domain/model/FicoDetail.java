//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.08.22 at 02:16:08 PM IST 
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for FicoDetail complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="FicoDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ficoDetailId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="companyName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="addresssId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="gstin" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="emailId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contact1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contact2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="reportingEmailId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="offerCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FicoDetail", propOrder = { "ficoDetailId", "unitId", "name", "companyName", "addresssId", "gstin",
		"emailId", "contact1", "contact2", "reportingEmailId", "offerCode" })
public class FicoDetail {

	protected int ficoDetailId;
	@XmlElement(required = true, nillable = true)
	protected IdCodeName unitId;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected String companyName;
	protected int addresssId;
	@XmlElement(required = true)
	protected String gstin;
	@XmlElement(required = true)
	protected String emailId;
	@XmlElement(required = true)
	protected String contact1;
	@XmlElement(required = true, nillable = true)
	protected String contact2;
	@XmlElement(required = true)
	protected String reportingEmailId;
	@XmlElement(required = true, nillable = true)
	protected String offerCode;

	/**
	 * Gets the value of the ficoDetailId property.
	 * 
	 */
	public int getFicoDetailId() {
		return ficoDetailId;
	}

	/**
	 * Sets the value of the ficoDetailId property.
	 * 
	 */
	public void setFicoDetailId(int value) {
		this.ficoDetailId = value;
	}

	/**
	 * Gets the value of the unitId property.
	 * 
	 * @return possible object is {@link IdCodeName }
	 * 
	 */
	public IdCodeName getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the unitId property.
	 * 
	 * @param value
	 *            allowed object is {@link IdCodeName }
	 * 
	 */
	public void setUnitId(IdCodeName value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the companyName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCompanyName() {
		return companyName;
	}

	/**
	 * Sets the value of the companyName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCompanyName(String value) {
		this.companyName = value;
	}

	/**
	 * Gets the value of the addresssId property.
	 * 
	 */
	public int getAddresssId() {
		return addresssId;
	}

	/**
	 * Sets the value of the addresssId property.
	 * 
	 */
	public void setAddresssId(int value) {
		this.addresssId = value;
	}

	/**
	 * Gets the value of the gstin property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getGstin() {
		return gstin;
	}

	/**
	 * Sets the value of the gstin property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setGstin(String value) {
		this.gstin = value;
	}

	/**
	 * Gets the value of the emailId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEmailId() {
		return emailId;
	}

	/**
	 * Sets the value of the emailId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEmailId(String value) {
		this.emailId = value;
	}

	/**
	 * Gets the value of the contact1 property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getContact1() {
		return contact1;
	}

	/**
	 * Sets the value of the contact1 property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setContact1(String value) {
		this.contact1 = value;
	}

	/**
	 * Gets the value of the contact2 property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getContact2() {
		return contact2;
	}

	/**
	 * Sets the value of the contact2 property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setContact2(String value) {
		this.contact2 = value;
	}

	/**
	 * Gets the value of the reportingEmailId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getReportingEmailId() {
		return reportingEmailId;
	}

	/**
	 * Sets the value of the reportingEmailId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setReportingEmailId(String value) {
		this.reportingEmailId = value;
	}

	/**
	 * Gets the value of the offerCode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOfferCode() {
		return offerCode;
	}

	/**
	 * Sets the value of the offerCode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOfferCode(String value) {
		this.offerCode = value;
	}

}
