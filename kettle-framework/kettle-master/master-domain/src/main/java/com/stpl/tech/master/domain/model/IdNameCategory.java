/**
 * 
 */
package com.stpl.tech.master.domain.model;

/**
 * <AUTHOR>
 *
 */
public class IdNameCategory {

	private int id;
	private String name;
	private String category;
	private int noOfTakeawayTerminals;
	private int noOfTerminal;
    private boolean workStationEnabled;
    private boolean live;
	private String unitZone;

	public String getUnitZone() {
		return unitZone;
	}

	public void setUnitZone(String unitZone) {
		this.unitZone = unitZone;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public int getNoOfTakeawayTerminals() {
		return noOfTakeawayTerminals;
	}

	public void setNoOfTakeawayTerminals(int noOfTakeawayTerminals) {
		this.noOfTakeawayTerminals = noOfTakeawayTerminals;
	}

	public int getNoOfTerminal() {
		return noOfTerminal;
	}

	public void setNoOfTerminal(int noOfTerminal) {
		this.noOfTerminal = noOfTerminal;
	}

    public boolean isWorkStationEnabled() {
        return workStationEnabled;
    }

    public void setWorkStationEnabled(boolean workStationEnabled) {
        this.workStationEnabled = workStationEnabled;
    }

	public boolean isLive() {
		return live;
	}

	public void setLive(boolean live) {
		this.live = live;
	}
    
    
}

