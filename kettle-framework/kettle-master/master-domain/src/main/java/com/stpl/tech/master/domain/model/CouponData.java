package com.stpl.tech.master.domain.model;

import java.io.Serializable;

public class CouponData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9026179482246649706L;

	private String coupon;
	private String startDate;
	private String endDate;
	private Integer usageCount;
	private Integer couponDetailId;
	private Integer offerDetailId;
	

	public CouponData(String coupon, String startDate, String endDate, Integer usageCount, Integer couponDetailId, Integer offerDetailId) {
		super();
		this.coupon = coupon;
		this.startDate = startDate;
		this.endDate = endDate;
		this.usageCount = usageCount;
		this.couponDetailId = couponDetailId;
		this.offerDetailId = offerDetailId;
	}

	public String getCoupon() {
		return coupon;
	}

	public void setCoupon(String coupon) {
		this.coupon = coupon;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public Integer getUsageCount() {
		return usageCount;
	}

	public void setUsageCount(Integer usageCount) {
		this.usageCount = usageCount;
	}

	public Integer getCouponDetailId() {
		return couponDetailId;
	}

	public void setCouponDetailId(Integer couponDetailId) {
		this.couponDetailId = couponDetailId;
	}

	public Integer getOfferDetailId() {
		return offerDetailId;
	}

	public void setOfferDetailId(Integer offerDetailId) {
		this.offerDetailId = offerDetailId;
	}

}
