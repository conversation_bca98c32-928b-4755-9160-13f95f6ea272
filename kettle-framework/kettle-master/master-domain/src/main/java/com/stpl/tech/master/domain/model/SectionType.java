/*
 * SUNS<PERSON>INE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


package com.stpl.tech.master.domain.model;

public enum SectionType {

    TRENDING_TODAY("TRENDING_TODAY","TT"),
    SPECIAL_PRODUCTS ("SPECIAL_PRODUCTS","SP"),
    REGULAR("REGULAR","REG"),
    APP_BANNERS("APP_BANNERS","AB"),
    CATEGORY_GRID ("CATEGORY_GRID","CG"),
    UNIT_OFFERS("UNIT_OFFERS","UO");

    private String fullName;
    private String shortName;

    SectionType(String fullName, String shortName) {
        this.fullName = fullName;
        this.shortName = shortName;
    }

    public String getFullName() {
        return fullName;
    }

    public String getShortName() {
        return shortName;
    }
}
