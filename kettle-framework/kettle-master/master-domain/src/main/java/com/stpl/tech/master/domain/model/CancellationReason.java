//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.11.16 at 08:32:55 PM IST 
//

package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CancellationReason", propOrder = { "id", "code", "desc", "source", "noWastage", "partialWastage",
		"completeWastage" })
public class CancellationReason implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5765417020036940292L;
	
	protected int id;
	protected String code;
	protected String desc;
	protected String status;
	protected UnitCategory source;
	protected boolean noWastage;
	protected boolean partialWastage;
	protected boolean completeWastage;

	public CancellationReason() {

	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public UnitCategory getSource() {
		return source;
	}

	public void setSource(UnitCategory source) {
		this.source = source;
	}

	public boolean isNoWastage() {
		return noWastage;
	}

	public void setNoWastage(boolean noWastage) {
		this.noWastage = noWastage;
	}

	public boolean isPartialWastage() {
		return partialWastage;
	}

	public void setPartialWastage(boolean partialWastage) {
		this.partialWastage = partialWastage;
	}

	public boolean isCompleteWastage() {
		return completeWastage;
	}

	public void setCompleteWastage(boolean completeWastage) {
		this.completeWastage = completeWastage;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
