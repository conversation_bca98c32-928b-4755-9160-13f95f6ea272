package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

public class UnitPartnerMenuMapping implements Serializable {

    private Integer id;
    private IdCodeName unit;
    private IdCodeName channelPartner;
    private IdCodeName deliveryPartner;
    private Integer unitChannelPartnerMappingId;
    private IdCodeName menuSequence;
    private MenuType menuType;
    private MenuApp menuApp;
    private String startTime;
    private String endTime;
    private String day;
    private Integer dayNumber;
    private IdCodeName createdBy;
    private IdCodeName updatedBy;
    private Date createdAt;
    private Date updatedAt;
    private String status;
    private IdCodeName brand;
    private Integer cartRecommendationSequenceId;
    private Integer menuRecommendationSequenceId;
    private Integer priceProfileId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public IdCodeName getUnit() {
        return unit;
    }

    public void setUnit(IdCodeName unit) {
        this.unit = unit;
    }

    public IdCodeName getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(IdCodeName channelPartner) {
        this.channelPartner = channelPartner;
    }

    public IdCodeName getDeliveryPartner() {
        return deliveryPartner;
    }

    public void setDeliveryPartner(IdCodeName deliveryPartner) {
        this.deliveryPartner = deliveryPartner;
    }

    public Integer getUnitChannelPartnerMappingId() {
        return unitChannelPartnerMappingId;
    }

    public void setUnitChannelPartnerMappingId(Integer unitChannelPartnerMappingId) {
        this.unitChannelPartnerMappingId = unitChannelPartnerMappingId;
    }

    public IdCodeName getMenuSequence() {
        return menuSequence;
    }

    public void setMenuSequence(IdCodeName menuSequence) {
        this.menuSequence = menuSequence;
    }

    public MenuType getMenuType() {
        return menuType;
    }

    public void setMenuType(MenuType menuType) {
        this.menuType = menuType;
    }

    public MenuApp getMenuApp() {
        return menuApp;
    }

    public void setMenuApp(MenuApp menuApp) {
        this.menuApp = menuApp;
    }

    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdCodeName createdBy) {
        this.createdBy = createdBy;
    }

    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(IdCodeName updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public Integer getDayNumber() {
        return dayNumber;
    }

    public void setDayNumber(Integer dayNumber) {
        this.dayNumber = dayNumber;
    }

    public IdCodeName getBrand() {
        return brand;
    }

    public void setBrand(IdCodeName brand) {
        this.brand = brand;
    }

    public Integer getCartRecommendationSequenceId() {
		return cartRecommendationSequenceId;
	}

	public void setCartRecommendationSequenceId(Integer cartRecommendationSequenceId) {
		this.cartRecommendationSequenceId = cartRecommendationSequenceId;
	}

	public Integer getMenuRecommendationSequenceId() {
		return menuRecommendationSequenceId;
	}

	public void setMenuRecommendationSequenceId(Integer menuRecommendationSequenceId) {
		this.menuRecommendationSequenceId = menuRecommendationSequenceId;
	}

    public Integer getPriceProfileId() {
        return priceProfileId;
    }

    public void setPriceProfileId(Integer priceProfileId) {
        this.priceProfileId = priceProfileId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnitPartnerMenuMapping that = (UnitPartnerMenuMapping) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(unit, that.unit) &&
                Objects.equals(channelPartner, that.channelPartner) &&
                Objects.equals(deliveryPartner, that.deliveryPartner) &&
                Objects.equals(unitChannelPartnerMappingId, that.unitChannelPartnerMappingId) &&
                Objects.equals(menuSequence, that.menuSequence) &&
                menuType == that.menuType &&
                menuApp == that.menuApp &&
                Objects.equals(startTime, that.startTime) &&
                Objects.equals(endTime, that.endTime) &&
                Objects.equals(day, that.day) &&
                Objects.equals(dayNumber, that.dayNumber) &&
                Objects.equals(createdBy, that.createdBy) &&
                Objects.equals(updatedBy, that.updatedBy) &&
                Objects.equals(createdAt, that.createdAt) &&
                Objects.equals(updatedAt, that.updatedAt) &&
                Objects.equals(status, that.status) &&
                Objects.equals(cartRecommendationSequenceId, that.cartRecommendationSequenceId) &&
                Objects.equals(menuRecommendationSequenceId, that.menuRecommendationSequenceId) &&
                Objects.equals(brand, that.brand);
    }

    @Override
    public int hashCode() {

        return Objects.hash(id, unit, channelPartner, deliveryPartner, unitChannelPartnerMappingId, menuSequence, menuType, menuApp, startTime, endTime, day, dayNumber, createdBy, updatedBy, createdAt, updatedAt, status, brand, cartRecommendationSequenceId, menuRecommendationSequenceId);
    }
}
