package com.stpl.tech.master.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class EmployeeData implements Serializable {

    private static final long serialVersionUID = 1695676756485780114L;

    @JsonProperty("Employee Status")
    private String employeeStatus;

    @JsonProperty("Date of Joining")
    private String  DOJ;

    @JsonProperty("Department")
    private String department;

    @JsonProperty("Designation")
    private String designation;

    @JsonProperty("Primary Phone")
    private String contact;

    @JsonProperty("Store Code")
    private String location;

    @JsonProperty("Employment Type")
    private String employeeType;

    @JsonProperty("Reporting Manager")
    private String reportingManager;

    @JsonProperty("HR Executive")
    private String hrExecutive;

    @JsonProperty("Leave Approval Authority")
    private String leaveApprovalAuthority;

    @JsonProperty("Employee Id")
    private String empId;

    @JsonProperty( "First Name")
    private String firstName;

    @JsonProperty("Middle Name")
    private String middleName;

    @JsonProperty("Last Name")
    private String lastName;

    @JsonProperty("Primary Email")
    private String email;

    @JsonProperty("State")
    private String state;

    @JsonProperty("Location(On Joining)")
    private String joiningLocation;

    @JsonProperty("Reporting Manager(Emp Id)")
    private String reportingManagerEmpId;

    @JsonProperty("Date Of Birth")
    private String DOB;

    @JsonProperty("Employee Status(As String)")
    private String EmployeeStatusString;

    @JsonProperty("Profile Pic Link")
    private String profilePic;

    @JsonProperty("Gender")
    private String gender;

    public String getEmployeeStatus() {
        return employeeStatus;
    }

    public String getDOB() {
        return DOB;
    }

    public void setDOB(String DOB) {
        this.DOB = DOB;
    }

    public String getDOJ() {
        return DOJ;
    }

    public String getDepartment() {
        return department.split(",")[0];
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDesignation() {
        return designation;
    }

    public void setDesignation(String designation) {
        this.designation = designation;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getReportingManager() {
        return reportingManager;
    }

    public void setReportingManager(String reportingManager) {
        this.reportingManager = reportingManager;
    }

    public String getHrExecutive() {
        return hrExecutive;
    }

    public void setHrExecutive(String hrExecutive) {
        this.hrExecutive = hrExecutive;
    }

    public String getLeaveApprovalAuthority() {
        return leaveApprovalAuthority;
    }

    public void setLeaveApprovalAuthority(String leaveApprovalAuthority) {
        this.leaveApprovalAuthority = leaveApprovalAuthority;
    }

    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getJoiningLocation() {
        return joiningLocation;
    }

    public void setJoiningLocation(String joiningLocation) {
        this.joiningLocation = joiningLocation;
    }

    public String getReportingManagerEmpId() {
        return reportingManagerEmpId;
    }

    public void setReportingManagerEmpId(String reportingManagerEmpId) {
        this.reportingManagerEmpId = reportingManagerEmpId;
    }

    public void setDOJ(String DOJ) {
        this.DOJ = DOJ;
    }

    public void setEmployeeStatus(String employeeStatus) {
        this.employeeStatus = employeeStatus;
    }

    public String getEmployeeStatusString() {
        return EmployeeStatusString;
    }

    public void setEmployeeStatusString(String employeeStatusString) {
        EmployeeStatusString = employeeStatusString;
    }

    public String getProfilePic() {
        return profilePic;
    }

    public void setProfilePic(String profilePic) {
        this.profilePic = profilePic;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }
}
