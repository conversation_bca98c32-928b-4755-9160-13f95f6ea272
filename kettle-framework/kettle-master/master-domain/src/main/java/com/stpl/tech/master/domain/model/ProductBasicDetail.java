/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 12:10:26 PM IST 
//

package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * <p>
 * Java class for ProductBasicDetail complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="ProductBasicDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="detail" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="subType" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="isInventoryTracked" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductBasicDetail", propOrder = { "detail", "type", "subType", "isInventoryTracked" })
public class ProductBasicDetail implements Serializable, Comparable<ProductBasicDetail> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -926969452764262475L;
	@Id
	private String objectId;

	@Version
	@JsonIgnore
	private Long version;

	/**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 */
	private String detachAll;
	@XmlElement(required = true)
	protected IdCodeName detail;
	protected int type;
	protected int subType;
	protected int stationCategory;
	protected String stationCategoryName;
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	protected ProductClassification classification;
	@XmlElement(required = false)
	protected Integer webType;
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	protected ProductStatus status;
	protected String code;
	protected boolean isInventoryTracked;
    protected boolean employeeMealComponent;
	protected Set<IdCodeNameValue> regularTags = new HashSet<IdCodeNameValue>();
	protected Set<IdCodeNameValue> nutritionTags = new HashSet<IdCodeNameValue>();

	/**
	 * @return the objectId
	 */
	public String getObjectId() {
		return objectId;
	}

	/**
	 * @param objectId
	 *            the objectId to set
	 */
	public void setObjectId(String _id) {
		this.objectId = _id;
	}

	/**
	 * @return the version
	 */
	public Long getVersion() {
		return version;
	}

	/**
	 * @param version
	 *            the version to set
	 */
	public void setVersion(Long version) {
		this.version = version;
	}

	/**
	 * @return the detachAll
	 */
	public String getDetachAll() {
		return detachAll;
	}

	/**
	 * @param detachAll
	 *            the detachAll to set
	 */
	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}

	/**
	 * Gets the value of the detail property.
	 * 
	 * @return possible object is {@link IdCodeName }
	 * 
	 */
	public IdCodeName getDetail() {
		return detail;
	}

	/**
	 * Sets the value of the detail property.
	 * 
	 * @param value
	 *            allowed object is {@link IdCodeName }
	 * 
	 */
	public void setDetail(IdCodeName value) {
		this.detail = value;
	}

	/**
	 * Gets the value of the type property.
	 * 
	 */
	public int getType() {
		return type;
	}

	/**
	 * Sets the value of the type property.
	 * 
	 */
	public void setType(int value) {
		this.type = value;
	}

	/**
	 * Gets the value of the subType property.
	 * 
	 */
	public int getSubType() {
		return subType;
	}

	/**
	 * Sets the value of the subType property.
	 * 
	 */
	public void setSubType(int value) {
		this.subType = value;
	}

	public int getStationCategory() {
		return stationCategory;
	}

	public void setStationCategory(int stationCategory) {
		this.stationCategory = stationCategory;
	}

	public String getStationCategoryName() {
		return stationCategoryName;
	}

	public void setStationCategoryName(String stationCategoryName) {
		this.stationCategoryName = stationCategoryName;
	}

	@Override
	public int compareTo(ProductBasicDetail o) {
		return Integer.compare(this.getDetail().getId(), o.getDetail().getId());
	}

	public ProductClassification getClassification() {
		return classification;
	}

	public void setClassification(ProductClassification classification) {
		this.classification = classification;
	}

	public boolean isInventoryTracked() {
		return isInventoryTracked;
	}

	public void setInventoryTracked(boolean isInventoryTracked) {
		this.isInventoryTracked = isInventoryTracked;
	}

	public boolean isEmployeeMealComponent() {
		return employeeMealComponent;
	}

	public void setEmployeeMealComponent(boolean employeeMealComponent) {
		this.employeeMealComponent = employeeMealComponent;
	}

	/**
	 * @return the webType
	 */
	public Integer getWebType() {
		return webType;
	}

	/**
	 * @param webType the webType to set
	 */
	public void setWebType(Integer webType) {
		this.webType = webType;
	}

	/**
	 * @return the status
	 */
	public ProductStatus getStatus() {
		return status;
	}

	/**
	 * @param status the status to set
	 */
	public void setStatus(ProductStatus status) {
		this.status = status;
	}

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @param code the code to set
	 */
	public void setCode(String code) {
		this.code = code;
	}

	public Set<IdCodeNameValue> getRegularTags() {
		return regularTags;
	}

	public void setRegularTags(Set<IdCodeNameValue> regularTags) {
		this.regularTags = regularTags;
	}

	public Set<IdCodeNameValue> getNutritionTags() {
		return nutritionTags;
	}

	public void setNutritionTags(Set<IdCodeNameValue> nutritionTags) {
		this.nutritionTags = nutritionTags;
	}
}
