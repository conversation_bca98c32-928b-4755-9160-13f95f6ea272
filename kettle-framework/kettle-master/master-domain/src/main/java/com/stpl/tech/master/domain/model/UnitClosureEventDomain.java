package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UnitClosureEventDomain {


    private Long requestId;
    @NotNull
    private Integer unitId;
    private String unitName;
    @NotNull
    private Date operationStopDate;
    @NotNull
    private String message;
    private Date closureDate;
    private String closureStatus;
    private Integer createdBy;
    private Date creationTIme;
    private Integer updatedBy;
    private Date lastUpdationTime;

}