package com.stpl.tech.master.domain.model;

import java.io.Serializable;

public class AddLocationRequest implements Serializable {
    private Integer id;
    private String city;
    private String cityCode;
    private Integer stateId;
    private Integer countryId;
    private String status;
    private Boolean functionalFlag;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public Integer getStateId() {
        return stateId;
    }

    public void setStateId(Integer stateId) {
        this.stateId = stateId;
    }

    public Integer getCountryId() {
        return countryId;
    }

    public void setCountryId(Integer countryId) {
        this.countryId = countryId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getFunctionalFlag() {
        return functionalFlag;
    }

    public void setFunctionalFlag(Boolean functionalFlag) {
        this.functionalFlag = functionalFlag;
    }
}
