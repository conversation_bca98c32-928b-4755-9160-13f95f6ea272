package com.stpl.tech.master.domain.model;

import java.util.Date;

public class DeliveryCouponDetail {

    private Integer deliveryCouponId;
    private Integer brandId;
    private Integer channelPartnerId;
    private String couponStrategy;
    private String masterCoupon;
    private String couponCode;
    private Date startDate;
    private Date endDate;
    private Integer validityInDays;
    private Integer maxNoOfDistributions;
    private Date creationTime;
    private Integer noOfAllocations;
    private String isExhausted;
    private Date lastAllocationTime;
    private Integer maxUsage;

    public Integer getDeliveryCouponId() {
        return deliveryCouponId;
    }

    public void setDeliveryCouponId(Integer deliveryCouponId) {
        this.deliveryCouponId = deliveryCouponId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getChannelPartnerId() {
        return channelPartnerId;
    }

    public void setChannelPartnerId(Integer channelPartnerId) {
        this.channelPartnerId = channelPartnerId;
    }

    public String getCouponStrategy() {
        return couponStrategy;
    }

    public void setCouponStrategy(String couponStrategy) {
        this.couponStrategy = couponStrategy;
    }

    public String getMasterCoupon() {
        return masterCoupon;
    }

    public void setMasterCoupon(String masterCoupon) {
        this.masterCoupon = masterCoupon;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getValidityInDays() {
        return validityInDays;
    }

    public void setValidityInDays(Integer validityInDays) {
        this.validityInDays = validityInDays;
    }

    public Integer getMaxNoOfDistributions() {
        return maxNoOfDistributions;
    }

    public void setMaxNoOfDistributions(Integer maxNoOfDistributions) {
        this.maxNoOfDistributions = maxNoOfDistributions;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Integer getNoOfAllocations() {
        return noOfAllocations;
    }

    public void setNoOfAllocations(Integer noOfAllocations) {
        this.noOfAllocations = noOfAllocations;
    }

    public String getIsExhausted() {
        return isExhausted;
    }

    public void setIsExhausted(String isExhausted) {
        this.isExhausted = isExhausted;
    }

    public Date getLastAllocationTime() {
        return lastAllocationTime;
    }

    public void setLastAllocationTime(Date lastAllocationTime) {
        this.lastAllocationTime = lastAllocationTime;
    }

    public Integer getMaxUsage() {
        return maxUsage;
    }

    public void setMaxUsage(Integer maxUsage) {
        this.maxUsage = maxUsage;
    }
}
