//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.21 at 05:03:06 PM IST 
//


package com.stpl.tech.master.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for ConsumptionData complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ConsumptionData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="closureId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="businessDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="startOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="endOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="consumables" type="{http://www.w3schools.com}Consumable" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="cafeConsumables" type="{http://www.w3schools.com}Consumable" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="codConsumable" type="{http://www.w3schools.com}Consumable" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="employeeMealConsumable" type="{http://www.w3schools.com}Consumable" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ConsumptionData", propOrder = {
    "closureId",
    "unitId",
    "businessDate",
    "startOrderId",
    "endOrderId",
    "consumables",
    "cafeConsumables",
    "codConsumable",
    "employeeMealConsumable"
})
public class ConsumptionData {

    protected int closureId;
    protected int unitId;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date businessDate;
    protected int startOrderId;
    protected int endOrderId;
    protected List<Consumable> consumables;
    protected List<Consumable> cafeConsumables;
    protected List<Consumable> codConsumable;
    protected List<Consumable> employeeMealConsumable;

    /**
     * Gets the value of the closureId property.
     * 
     */
    public int getClosureId() {
        return closureId;
    }

    /**
     * Sets the value of the closureId property.
     * 
     */
    public void setClosureId(int value) {
        this.closureId = value;
    }

    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the businessDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getBusinessDate() {
        return businessDate;
    }

    /**
     * Sets the value of the businessDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBusinessDate(Date value) {
        this.businessDate = value;
    }

    /**
     * Gets the value of the startOrderId property.
     * 
     */
    public int getStartOrderId() {
        return startOrderId;
    }

    /**
     * Sets the value of the startOrderId property.
     * 
     */
    public void setStartOrderId(int value) {
        this.startOrderId = value;
    }

    /**
     * Gets the value of the endOrderId property.
     * 
     */
    public int getEndOrderId() {
        return endOrderId;
    }

    /**
     * Sets the value of the endOrderId property.
     * 
     */
    public void setEndOrderId(int value) {
        this.endOrderId = value;
    }

    /**
     * Gets the value of the consumables property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the consumables property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getConsumables().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Consumable }
     * 
     * 
     */
    public List<Consumable> getConsumables() {
        if (consumables == null) {
            consumables = new ArrayList<Consumable>();
        }
        return this.consumables;
    }

	public List<Consumable> getCafeConsumables() {
        if (cafeConsumables == null) {
        	cafeConsumables = new ArrayList<Consumable>();
        }
		return cafeConsumables;
	}

	public List<Consumable> getCodConsumable() {
        if (codConsumable == null) {
        	codConsumable = new ArrayList<Consumable>();
        }
		return codConsumable;
	}

	public List<Consumable> getEmployeeMealConsumable() {
        if (employeeMealConsumable == null) {
        	employeeMealConsumable = new ArrayList<Consumable>();
        }
		return employeeMealConsumable;
	}

}
