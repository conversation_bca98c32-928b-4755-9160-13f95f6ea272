/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.04 at 12:22:00 PM IST 
//

package com.stpl.tech.master.domain.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import org.apache.commons.lang.builder.HashCodeBuilder;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "")
@XmlRootElement(name = "UnitPartnerBrandKey")
public class UnitPartnerBrandKey implements Serializable {

    private static final long serialVersionUID = -93045147658127L;

    private Integer unitId;

    private Integer brandId;

    private Integer partnerId;

    public UnitPartnerBrandKey(int unitId, int brandId, int partnerId) {
        this.unitId = unitId;
        this.brandId = brandId;
        this.partnerId = partnerId;
    }

    public UnitPartnerBrandKey() {
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnitPartnerBrandKey that = (UnitPartnerBrandKey) o;
        return unitId != null && brandId != null && partnerId != null &&
                that.unitId != null && that.brandId != null && that.partnerId != null &&
                unitId.intValue() == that.unitId.intValue() &&
                brandId.intValue() == that.brandId.intValue() &&
                partnerId.intValue() == that.partnerId.intValue();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(unitId)
                .append(brandId)
                .append(partnerId)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "UnitPartnerBrandKey{" +
                "unitId=" + unitId +
                ", brandId=" + brandId +
                ", partnerId=" + partnerId +
                '}';
    }
    
    
	public String getKey() {
		return unitId + "_" + brandId + "_" + partnerId;
	}
}
