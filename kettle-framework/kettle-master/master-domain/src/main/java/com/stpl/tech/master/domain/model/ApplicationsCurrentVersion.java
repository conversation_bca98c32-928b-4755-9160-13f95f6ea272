package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationsCurrentVersion {
    private Integer unitId;

    private Integer terminalId;
    private String unitRegion;
    private List<ApplicationVersionDetailDomainData> applicationVersionDetailDomainDataList;


}
