/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.domain.model;

import com.stpl.tech.util.AppUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ProductImageMappingDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7542735268463811173L;
	private Integer productId;
	private Integer dimensionCode;
	private String trendLow;
	private String trendHigh;
	private String specialLow;
	private String specialHigh;
//	private String gridLow;
//	private List<String> gridLows;
	private IndexUrl gridLow;
	private List<IndexUrl> gridLows;
	private IndexUrl gridLowWebp;
	private List<IndexUrl> gridLowsWebp;
	private List<IndexUrl> gridHighs;
	private IndexUrl gridHighV1;
	private String gridHigh;
	private String listLow;
	private String listHigh;
	private String comboLow;
	private String comboHigh;
	private String showcaseVideo;
	private IndexUrl marketingImage;
	private IndexUrl marketingImageWebView;
	private List<IndexUrl> marketingImages;
	private List<IndexUrl> marketingImageWebViews;

	private IndexUrl grid100X100;

	private List<IndexUrl> grids100X100;

	private IndexUrl grid400X400;

	private List<IndexUrl> grids400X400;

	private IndexUrl recommendationImage1200X1200;
	private List<IndexUrl> recommendationImages1200X1200;

	public ProductImageMappingDetail() {
	}

	public ProductImageMappingDetail(Integer productId) {
		this.productId = productId;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getTrendLow() {
		return trendLow;
	}

	public void setTrendLow(String trendLow) {
		this.trendLow = trendLow;
	}

	public String getTrendHigh() {
		return trendHigh;
	}

	public void setTrendHigh(String trendHigh) {
		this.trendHigh = trendHigh;
	}

	public String getSpecialLow() {
		return specialLow;
	}

	public void setSpecialLow(String specialLow) {
		this.specialLow = specialLow;
	}

	public String getSpecialHigh() {
		return specialHigh;
	}

	public void setSpecialHigh(String specialHigh) {
		this.specialHigh = specialHigh;
	}

//	public String getGridLow() {
//		return gridLow;
//	}
//
//	public void setGridLow(String gridLow) {
//		this.gridLow = gridLow;
//	}

	public String getGridHigh() {
		return gridHigh;
	}

	public void setGridHigh(String gridHigh) {
		this.gridHigh = gridHigh;
	}

	public String getListLow() {
		return listLow;
	}

	public void setListLow(String listLow) {
		this.listLow = listLow;
	}

	public String getListHigh() {
		return listHigh;
	}

	public void setListHigh(String listHigh) {
		this.listHigh = listHigh;
	}

	public String getComboLow() {
		return comboLow;
	}

	public void setComboLow(String comboLow) {
		this.comboLow = comboLow;
	}

	public String getComboHigh() {
		return comboHigh;
	}

	public void setComboHigh(String comboHigh) {
		this.comboHigh = comboHigh;
	}

//	public List<String> getGridLows() {
//		if (gridLows == null) {
//			gridLows = new ArrayList<String>();
//		}
//		return gridLows;
//	}
//
//	public void setGridLows(List<String> gridLows) {
//		this.gridLows = gridLows;
//	}

	public String getShowcaseVideo() {
		return showcaseVideo;
	}

	public void setShowcaseVideo(String showcaseVideo) {
		this.showcaseVideo = showcaseVideo;
	}

	public IndexUrl getGridLow() {
		return gridLow;
	}

	public void setGridLow(IndexUrl gridLow) {
		this.gridLow = gridLow;
	}

	public List<IndexUrl> getGridLows() {
		if(gridLows == null){
			gridLows = new ArrayList<IndexUrl>();
		}
		return gridLows;
	}

	public IndexUrl getGridLowWebp() {
		return gridLowWebp;
	}

	public void setGridLowWebp(IndexUrl gridLowWebp) {
		this.gridLowWebp = gridLowWebp;
	}

	public List<IndexUrl> getGridLowsWebp() {
		if(gridLowsWebp == null){
			gridLowsWebp = new ArrayList<IndexUrl>();
		}
		return gridLowsWebp;
	}

	public void setGridLowsWebp(List<IndexUrl> gridLowsWebp) {
		this.gridLowsWebp = gridLowsWebp;
	}

	public IndexUrl getMarketingImage() {
		return marketingImage;
	}

	public void setMarketingImage(IndexUrl marketingImage) {
		this.marketingImage = marketingImage;
	}

	public IndexUrl getMarketingImageWebView() {
		return marketingImageWebView;
	}

	public void setMarketingImageWebView(IndexUrl marketingImageWebView) {
		this.marketingImageWebView = marketingImageWebView;
	}

	public List<IndexUrl> getMarketingImages() {
		if (marketingImages == null) {
			marketingImages = new ArrayList<IndexUrl>();
		}
		return marketingImages;
	}

	public void setMarketingImages(List<IndexUrl> marketingImages) {
		this.marketingImages = marketingImages;
	}

	public List<IndexUrl> getMarketingImageWebViews() {
		if (marketingImageWebViews == null) {
			marketingImageWebViews = new ArrayList<IndexUrl>();
		}
		return marketingImageWebViews;
	}

	public void setMarketingImageWebViews(List<IndexUrl> marketingImageWebViews) {
		this.marketingImageWebViews = marketingImageWebViews;
	}

	public IndexUrl getGrid100X100() {
		return grid100X100;
	}

	public void setGrid100X100(IndexUrl grid100X100) {
		this.grid100X100 = grid100X100;
	}

	public IndexUrl getGrid400X400() {
		return grid400X400;
	}

	public void setGrid400X400(IndexUrl grid400X400) {
		this.grid400X400 = grid400X400;
	}

	public List<IndexUrl> getGrids100X100() {
		if(grids100X100 == null){
			grids100X100 = new ArrayList<IndexUrl>();
		}
		return grids100X100;
	}

	public void setGrids100X100(List<IndexUrl> grids100X100) {
		this.grids100X100 = grids100X100;
	}

	public List<IndexUrl> getGrids400X400() {
		if(grids400X400 == null){
			grids400X400 = new ArrayList<IndexUrl>();
		}
		return grids400X400;
	}

	public void setGrids400X400(List<IndexUrl> grids400X400) {
		this.grids400X400 = grids400X400;
	}

	public IndexUrl getRecommendationImage1200X1200() {
		return recommendationImage1200X1200;
	}

	public void setRecommendationImage1200X1200(IndexUrl recommendationImage1200X1200) {
		this.recommendationImage1200X1200 = recommendationImage1200X1200;
	}

	public List<IndexUrl> getRecommendationImages1200X1200() {
		if(recommendationImages1200X1200 == null){
			recommendationImages1200X1200 = new ArrayList<IndexUrl>();
		}
		return recommendationImages1200X1200;
	}

	public void setRecommendationImages1200X1200(List<IndexUrl> recommendationImages1200X1200) {
		this.recommendationImages1200X1200 = recommendationImages1200X1200;
	}

	public Integer getDimensionCode() {
		return dimensionCode;
	}

	public void setDimensionCode(Integer dimensionCode) {
		this.dimensionCode = dimensionCode;
	}
}
