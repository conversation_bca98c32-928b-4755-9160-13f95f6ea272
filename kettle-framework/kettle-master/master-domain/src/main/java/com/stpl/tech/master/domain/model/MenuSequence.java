package com.stpl.tech.master.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import javax.xml.bind.annotation.XmlSchemaType;
import java.util.Date;
import java.util.Set;

public class MenuSequence {

    private Integer menuSequenceId;
    private Integer dynamicPriceProfileId;
    private String menuSequenceName;
    private String menuSequenceDescription;
    private Set<ProductGroupSequence> productGroupSequences;
    private IdName createdBy;
    @JsonProperty
    @XmlSchemaType(name = "date")
    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date creationTime;
    private Date lastUpdateTime;
    private UnitStatus status;
    private MenuType menuType;
    private MenuStatus menuStatus;
    private Integer cloneId;
    private MenuApp menuApp;
    private Integer menuRecommendationSequenceId;

    public Integer getMenuSequenceId() {
        return menuSequenceId;
    }

    public void setMenuSequenceId(Integer menuSequenceId) {
        this.menuSequenceId = menuSequenceId;
    }

    public String getMenuSequenceName() {
        return menuSequenceName;
    }

    public void setMenuSequenceName(String menuSequenceName) {
        this.menuSequenceName = menuSequenceName;
    }

    public String getMenuSequenceDescription() {
        return menuSequenceDescription;
    }

    public void setMenuSequenceDescription(String menuSequenceDescription) {
        this.menuSequenceDescription = menuSequenceDescription;
    }

    public Set<ProductGroupSequence> getProductGroupSequences() {
        return productGroupSequences;
    }

    public void setProductGroupSequences(Set<ProductGroupSequence> productGroupSequences) {
        this.productGroupSequences = productGroupSequences;
    }

    public IdName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdName createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public UnitStatus getStatus() {
        return status;
    }

    public void setStatus(UnitStatus status) {
        this.status = status;
    }

    public MenuType getMenuType() {
        return menuType;
    }

    public void setMenuType(MenuType menuType) {
        this.menuType = menuType;
    }

    public MenuStatus getMenuStatus() { return this.menuStatus; }

    public void setMenuStatus(MenuStatus menuStatus) { this.menuStatus = menuStatus; }

    public Integer getCloneId() {
        return cloneId;
    }

    public void setCloneId(Integer cloneId) {
        this.cloneId = cloneId;
    }

    public MenuApp getMenuApp() {
        return menuApp;
    }

    public void setMenuApp(MenuApp menuApp) {
        this.menuApp = menuApp;
    }

    public Integer getMenuRecommendationSequenceId() {
		return menuRecommendationSequenceId;
	}

	public void setMenuRecommendationSequenceId(Integer menuRecommendationSequenceId) {
		this.menuRecommendationSequenceId = menuRecommendationSequenceId;
	}

    public Integer getDynamicPriceProfileId() {
        return dynamicPriceProfileId;
    }

    public void setDynamicPriceProfileId(Integer dynamicPriceProfileId) {
        this.dynamicPriceProfileId = dynamicPriceProfileId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        MenuSequence that = (MenuSequence) o;

        return new EqualsBuilder()
                .append(menuSequenceId, that.menuSequenceId)
                .append(menuSequenceName, that.menuSequenceName)
                .append(menuSequenceDescription, that.menuSequenceDescription)
                .append(productGroupSequences, that.productGroupSequences)
                .append(createdBy, that.createdBy)
                .append(creationTime, that.creationTime)
                .append(lastUpdateTime, that.lastUpdateTime)
                .append(status, that.status)
                .append(menuRecommendationSequenceId, that.menuRecommendationSequenceId)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(menuSequenceId)
                .append(menuSequenceName)
                .append(menuSequenceDescription)
                .append(productGroupSequences)
                .append(createdBy)
                .append(creationTime)
                .append(lastUpdateTime)
                .append(status)
                .append(menuRecommendationSequenceId)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "MenuSequence{" +
                "menuSequenceId=" + menuSequenceId +
                ", menuSequenceName='" + menuSequenceName + '\'' +
                ", menuSequenceDescription='" + menuSequenceDescription + '\'' +
                ", productGroupSequences=" + productGroupSequences +
                ", createdBy=" + createdBy +
                ", creationTime=" + creationTime +
                ", lastUpdateTime=" + lastUpdateTime +
                ", status=" + status +
                ", menuRecommendationSequenceId=" + menuRecommendationSequenceId +
                '}';
    }
}
