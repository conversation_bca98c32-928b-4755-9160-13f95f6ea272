package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VersionCompatibilityDetails {
    private Integer unitId;
    private String applicationName;
    private boolean isCompatible;
    private Integer terminalId;
    private String currentApplicationVersion;
    private String compatibleApplicationVersion;
}
