/**
 * 
 */
package com.stpl.tech.master.domain.model;

/**
 * <AUTHOR>
 *
 */
public class ComplimentaryReason extends IdCodeName {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7489228605277975480L;

	public ComplimentaryReason() {

	}

	public ComplimentaryReason(int id, String name, String code, String shortCode, String type, String status,
			String category) {
		super(id, name, code, shortCode, type, status);
		this.category = category;
	}

	protected String category;

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

}
