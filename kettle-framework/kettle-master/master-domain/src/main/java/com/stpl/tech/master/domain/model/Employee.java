/*

 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.09 at 05:35:16 PM IST 
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * <p>
 * Java class for Employee complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="Employee"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="gender" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="primaryContact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="secondaryContact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="biometricId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="department" type="{http://www.w3schools.com}Department"/&gt;
 *         &lt;element name="designation" type="{http://www.w3schools.com}Designation"/&gt;
 *         &lt;element name="currentAddress" type="{http://www.w3schools.com}Address"/&gt;
 *         &lt;element name="permanentAddress" type="{http://www.w3schools.com}Address"/&gt;
 *         &lt;element name="employmentType" type="{http://www.w3schools.com}EmploymentType"/&gt;
 *         &lt;element name="employmentStatus" type="{http://www.w3schools.com}EmploymentStatus"/&gt;
 *         &lt;element name="joiningDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="reportingManager" type="{http://www.w3schools.com}Employee"/&gt;
 *         &lt;element name="employeeEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="employeeCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="communicationChannel" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Employee", propOrder = {
    "id",
    "name",
    "gender",
    "primaryContact",
    "secondaryContact",
    "biometricId",
    "department",
    "designation",
    "currentAddress",
    "permanentAddress",
    "employmentType",
    "employmentStatus",
    "joiningDate",
    "reportingManager",
    "employeeEmail",
    "employeeCode",
    "communicationChannel",
	"appMappingType"
})
public class Employee implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -710693116012443391L;
	protected int id;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected String gender;
	@XmlElement(required = true)
	protected String primaryContact;
	@XmlElement(required = true)
	protected String secondaryContact;
	@XmlElement(required = true, nillable = true)
	protected String biometricId;
	@XmlElement(required = true)
	protected Department department;
	@XmlElement(required = true)
	protected Designation designation;
	@XmlElement(required = true)
	protected Address currentAddress;
	@XmlElement(required = true)
	protected Address permanentAddress;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected EmploymentType employmentType;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected EmploymentStatus employmentStatus;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	protected Date joiningDate;
	@XmlSchemaType(name = "date")
	protected Date dob;
	@XmlElement(required = true)
	protected Employee reportingManager;
	@XmlElement(required = true, nillable = true)
	protected String employeeEmail;
	@XmlElement(required = true, nillable = true)
    protected String employeeCode;
    @XmlElement(required = true, nillable = true)
    protected String communicationChannel;
	@XmlElement(required = true)
	protected Set<ApplicationName> applications;
	@XmlElement(required = true)
	protected Set<Integer> units;
	@XmlElement(required = true)
	protected String sdpContact;
	@XmlElement(required = true)
	protected boolean employeeMealEligible;
	@XmlElement(required = true)
	protected String reasonForTermination;
	@XmlElement(required = true)
	protected String hrExecutive;
	@XmlElement(required = true)
	protected String leaveApprovalAuthority;
	@XmlElement(required = true)
	protected Integer locCode;


	public String getAppMappingType() {
		return appMappingType;
	}

	public void setAppMappingType(String appMappingType) {
		this.appMappingType = appMappingType;
	}

	@XmlElement(required = true)
	protected String appMappingType;
	protected Integer userPolicyId;
	protected Company company;
	protected String imageKey;


	/**
	 * Gets the value of the id property.
	 * 
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the gender property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getGender() {
		return gender;
	}

	/**
	 * Sets the value of the gender property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setGender(String value) {
		this.gender = value;
	}

	/**
	 * Gets the value of the primaryContact property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPrimaryContact() {
		return primaryContact;
	}

	/**
	 * Sets the value of the primaryContact property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPrimaryContact(String value) {
		this.primaryContact = value;
	}

	/**
	 * Gets the value of the secondaryContact property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSecondaryContact() {
		return secondaryContact;
	}

	/**
	 * Sets the value of the secondaryContact property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSecondaryContact(String value) {
		this.secondaryContact = value;
	}

	/**
	 * Gets the value of the biometricId property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getBiometricId() {
		return biometricId;
	}

	/**
	 * Sets the value of the biometricId property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setBiometricId(String value) {
		this.biometricId = value;
	}

	/**
	 * Gets the value of the department property.
	 * 
	 * @return possible object is {@link Department }
	 * 
	 */
	public Department getDepartment() {
		return department;
	}

	/**
	 * Sets the value of the department property.
	 * 
	 * @param value
	 *            allowed object is {@link Department }
	 * 
	 */
	public void setDepartment(Department value) {
		this.department = value;
	}

	/**
	 * Gets the value of the designation property.
	 * 
	 * @return possible object is {@link Designation }
	 * 
	 */
	public Designation getDesignation() {
		return designation;
	}

	/**
	 * Sets the value of the designation property.
	 * 
	 * @param value
	 *            allowed object is {@link Designation }
	 * 
	 */
	public void setDesignation(Designation value) {
		this.designation = value;
	}

	/**
	 * Gets the value of the currentAddress property.
	 * 
	 * @return possible object is {@link Address }
	 * 
	 */
	public Address getCurrentAddress() {
		return currentAddress;
	}

	/**
	 * Sets the value of the currentAddress property.
	 * 
	 * @param value
	 *            allowed object is {@link Address }
	 * 
	 */
	public void setCurrentAddress(Address value) {
		this.currentAddress = value;
	}

	/**
	 * Gets the value of the permanentAddress property.
	 * 
	 * @return possible object is {@link Address }
	 * 
	 */
	public Address getPermanentAddress() {
		return permanentAddress;
	}

	/**
	 * Sets the value of the permanentAddress property.
	 * 
	 * @param value
	 *            allowed object is {@link Address }
	 * 
	 */
	public void setPermanentAddress(Address value) {
		this.permanentAddress = value;
	}

	/**
	 * Gets the value of the employmentType property.
	 * 
	 * @return possible object is {@link EmploymentType }
	 * 
	 */
	public EmploymentType getEmploymentType() {
		return employmentType;
	}

	/**
	 * Sets the value of the employmentType property.
	 * 
	 * @param value
	 *            allowed object is {@link EmploymentType }
	 * 
	 */
	public void setEmploymentType(EmploymentType value) {
		this.employmentType = value;
	}

	/**
	 * Gets the value of the employmentStatus property.
	 * 
	 * @return possible object is {@link EmploymentStatus }
	 * 
	 */
	public EmploymentStatus getEmploymentStatus() {
		return employmentStatus;
	}

	/**
	 * Sets the value of the employmentStatus property.
	 * 
	 * @param value
	 *            allowed object is {@link EmploymentStatus }
	 * 
	 */
	public void setEmploymentStatus(EmploymentStatus value) {
		this.employmentStatus = value;
	}

	/**
	 * Gets the value of the joiningDate property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getJoiningDate() {
		return joiningDate;
	}

	/**
	 * Sets the value of the joiningDate property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setJoiningDate(Date value) {
		this.joiningDate = value;
	}

	/**
	 * Gets the value of the reportingManager property.
	 * 
	 * @return possible object is {@link Employee }
	 * 
	 */
	public Employee getReportingManager() {
		return reportingManager;
	}

	/**
	 * Sets the value of the reportingManager property.
	 * 
	 * @param value
	 *            allowed object is {@link Employee }
	 * 
	 */
	public void setReportingManager(Employee value) {
		this.reportingManager = value;
	}

	/**
	 * Gets the value of the employeeEmail property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEmployeeEmail() {
		return employeeEmail;
	}

	/**
	 * Sets the value of the employeeEmail property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEmployeeEmail(String value) {
		this.employeeEmail = value;
	}

	public Set<ApplicationName> getApplications() {
		return applications;
	}

	public void setApplications(Set<ApplicationName> applications) {
		this.applications = applications;
	}
	
	public boolean hasAccess(ApplicationName application) {
		return applications.contains(application);
	}
	
    /**
     * Gets the value of the employeeCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEmployeeCode() {
        return employeeCode;
    }

    /**
     * Sets the value of the employeeCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEmployeeCode(String value) {
        this.employeeCode = value;
    }

    /**
     * Gets the value of the communicationChannel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCommunicationChannel() {
        return communicationChannel;
    }

    /**
     * Sets the value of the communicationChannel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCommunicationChannel(String value) {
        this.communicationChannel = value;
    }

	public Set<Integer> getUnits() {
		return units;
	}

	public void setUnits(Set<Integer> units) {
		this.units = units;
	}

	public String getSdpContact() {
		return sdpContact;
	}

	public void setSdpContact(String sdpContact) {
		this.sdpContact = sdpContact;
	}

	public boolean isEmployeeMealEligible() {
		return employeeMealEligible;
	}

	public void setEmployeeMealEligible(boolean employeeMealEligible) {
		this.employeeMealEligible = employeeMealEligible;
	}

	public Date getDob() {
		return dob;
	}

	public void setDob(Date dob) {
		this.dob = dob;
	}

	public String getReasonForTermination() {
		return reasonForTermination;
	}

	public void setReasonForTermination(String reasonForTermination) {
		this.reasonForTermination = reasonForTermination;
	}

	public String getHrExecutive() {
		return hrExecutive;
	}

	public void setHrExecutive(String hrExecutive) {
		this.hrExecutive = hrExecutive;
	}

	public String getLeaveApprovalAuthority() {
		return leaveApprovalAuthority;
	}

	public void setLeaveApprovalAuthority(String leaveApprovalAuthority) {
		this.leaveApprovalAuthority = leaveApprovalAuthority;
	}

	public Integer getLocCode() {
		return locCode;
	}

	public void setLocCode(Integer locCode) {
		this.locCode = locCode;
	}

	public Integer getUserPolicyId() {
		return userPolicyId;
	}

	public void setUserPolicyId(Integer userPolicyId) {
		this.userPolicyId = userPolicyId;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public String getImageKey() {
		return imageKey;
	}

	public void setImageKey(String imageKey) {
		this.imageKey = imageKey;
	}
}
