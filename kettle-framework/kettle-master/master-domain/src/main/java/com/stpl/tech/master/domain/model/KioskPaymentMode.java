//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.23 at 01:37:36 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for KioskPaymentMode.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="KioskPaymentMode"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="COMPANY_PAID"/&gt;
 *     &lt;enumeration value="EMPLOYEE_PAID"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "KioskPaymentMode")
@XmlEnum
public enum KioskPaymentMode {

    @XmlEnumValue("COMPANY_PAID")
    COMPANY_PAID("COMPANY_PAID"),
    @XmlEnumValue("EMPLOYEE_PAID")
    EMPLOYEE_PAID("EMPLOYEE_PAID");
    private final String value;

    KioskPaymentMode(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static KioskPaymentMode fromValue(String v) {
        for (KioskPaymentMode c: KioskPaymentMode.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
