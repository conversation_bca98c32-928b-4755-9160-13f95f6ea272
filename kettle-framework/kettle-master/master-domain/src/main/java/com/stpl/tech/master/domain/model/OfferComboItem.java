package com.stpl.tech.master.domain.model;

import java.util.Objects;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 09-04-2018.
 */
public class OfferComboItem {

    private int id;
    private String name;
    private int q;
    private String d;

    public OfferComboItem() { }

    public OfferComboItem(int productId, int quantity, String dimension, String name) {
        this.id = productId;
        this.q=quantity;
        this.d=dimension;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getQ() {
        return q;
    }

    public void setQ(int q) {
        this.q = q;
    }

    public String getD() {
        return d;
    }

    public void setD(String d) {
        this.d = d;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof OfferComboItem)) return false;
        OfferComboItem that = (OfferComboItem) o;
        return id == that.id &&
                q == that.q &&
                Objects.equals(d, that.d);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, q, d);
    }

    @Override
    public String toString() {
        return "[ name=" + name + ", q=" + q + ", d=" + d + " ]";
    }
}
