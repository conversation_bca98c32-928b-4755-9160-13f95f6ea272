/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


package com.stpl.tech.master.domain.model;

public enum BannerActionType {

    HOME("home", "Will open Home Grid"),
    HOME_LIST("home_list", "Will open Home List"),
    CART("cart", "Will open Cart"),
    STORE_LOCATOR("store_locator", "Will open Cafe search screen"),
    MY_ORDERS("my_orders", "Will open My Orders Screen"),
    PRODUCT_SEARCH("product_search", "Will open Product Search"),
    TAP_TO_COPY_CODE("tap_to_copy_code", ""),
    PROFILE_MAIN("profile_main", "Will open Profile screen"),
    PROFILE_OFFERS("profile_offers", "Will open Profile Screen and expand offers card to top"),
    PROFILE_ALLIANCES("profile_alliances", "Will open Profile Screen and expand alliances to top if exists"),
    PROFILE_MONEY_WALLET("profile_money_wallet", "Will open Profile Screen and expand money wallet card to top"),
    PROFILE_MONEY_CASH("profile_money_cash", "Will open Profile Screen and expand money cash card to top"),
    PROFILE_REFERRAL("profile_referral", "Will open Profile Screen and expand referral card to top"),
    REFERRAL("referral","will open referral screen"),
    PROFILE_LOYALTY("profile_loyalty", "Will open Profile Screen and expand loyalty card to top");


    BannerActionType(String actionType, String detail) {
        this.actionType = actionType;
        this.detail = detail;
    }

    private String actionType;
    private String detail;


    public String getActionType() {
        return actionType;
    }

    public String getDetail() {
        return detail;
    }
}
