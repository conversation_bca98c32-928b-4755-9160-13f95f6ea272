package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.xml.crypto.Data;
import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PriceProfileProductMappingDomain {
    private Integer id;
    private IdCodeName priceProfileId;

    private Integer version;
    private Product productId;
    private IdCodeName dimensionCode;
    private BigDecimal price;

    private BigDecimal newPrice;

    private String status;

    private String newStatus;
    private Integer createdBy;
    private Date creationTime;
    private Integer lastUpdatedBy;
    private Date lastUpdationTime;

    private Boolean isNew;
}
