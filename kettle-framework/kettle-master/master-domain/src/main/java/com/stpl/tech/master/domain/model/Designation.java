/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2015.12.19 at 12:10:26 PM IST
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <p>
 * Java class for Designation complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="Designation"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Designation", propOrder = { "id", "name", "description" })
public class Designation implements Serializable, Comparable<Designation> {

	/**
	 *
	 */
	private static final long serialVersionUID = 1056127804090070686L;
	protected int id;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected String description;
	@XmlElement(required = true)
	protected boolean transactionSystemAccess;
	@XmlElement(required = true)
	protected boolean scmSystemAccess;
	@XmlElement(required = true)
	protected boolean adminSystemAccess;
	@XmlElement(required = true)
	protected boolean clmSystemAccess;
	@XmlElement(required = true)
	protected boolean analyticsSystemAccess;
	@XmlElement(required = true)
	protected boolean crmSystemAccess;
	@XmlElement(required = true)
	protected boolean formsSystemAccess;
	@XmlElement(required = true)
	protected boolean channelPartnerSystemAccess;
	@XmlElement(required = true)
	protected boolean appInstallerAccess;
	@XmlElement(required = true)
	protected int maxAllocatedUnits = -1;
	@XmlElement(required = true)
	protected  boolean attendanceAccess;
	@XmlElement(required = true)
	protected  boolean knockApplicationAccess;

	/**
	 * Gets the value of the id property.
	 *
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the description property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Sets the value of the description property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setDescription(String value) {
		this.description = value;
	}

	@Override
	public int compareTo(Designation o) {
		return Integer.compare(this.getId(), o.getId());
	}

	public boolean isTransactionSystemAccess() {
		return transactionSystemAccess;
	}

	public void setTransactionSystemAccess(boolean transactionSystemAccess) {
		this.transactionSystemAccess = transactionSystemAccess;
	}

	public boolean isScmSystemAccess() {
		return scmSystemAccess;
	}

	public void setScmSystemAccess(boolean scmSystemAccess) {
		this.scmSystemAccess = scmSystemAccess;
	}

	public boolean isAdminSystemAccess() {
		return adminSystemAccess;
	}

	public void setAdminSystemAccess(boolean adminSystemAccess) {
		this.adminSystemAccess = adminSystemAccess;
	}

	public boolean isClmSystemAccess() {
		return clmSystemAccess;
	}

	public void setClmSystemAccess(boolean clmSystemAccess) {
		this.clmSystemAccess = clmSystemAccess;
	}

	public boolean isAnalyticsSystemAccess() {
		return analyticsSystemAccess;
	}

	public void setAnalyticsSystemAccess(boolean analyticsSystemAccess) {
		this.analyticsSystemAccess = analyticsSystemAccess;
	}

	public boolean isCrmSystemAccess() {
		return crmSystemAccess;
	}

	public void setCrmSystemAccess(boolean crmSystemAccess) {
		this.crmSystemAccess = crmSystemAccess;
	}

	public boolean isFormsSystemAccess() {
		return formsSystemAccess;
	}

	public void setFormsSystemAccess(boolean formsSystemAccess) {
		this.formsSystemAccess = formsSystemAccess;
	}

	public boolean isChannelPartnerSystemAccess() {
		return channelPartnerSystemAccess;
	}

	public void setChannelPartnerSystemAccess(boolean channelPartnerSystemAccess) {
		this.channelPartnerSystemAccess = channelPartnerSystemAccess;
	}

	public boolean isAppInstallerAccess() {
		return appInstallerAccess;
	}

	public void setAppInstallerAccess(boolean appInstallerAccess) {
		this.appInstallerAccess = appInstallerAccess;
	}

	public int getMaxAllocatedUnits() {
		return maxAllocatedUnits;
	}

	public void setMaxAllocatedUnits(int maxAllocatedUnits) {
		this.maxAllocatedUnits = maxAllocatedUnits;
	}

	public boolean isAttendanceAccess() {
		return attendanceAccess;
	}

	public void setAttendanceAccess(boolean attendanceAccess) {
		this.attendanceAccess = attendanceAccess;
	}

	public boolean isKnockApplicationAccess() {
		return knockApplicationAccess;
	}

	public void setKnockApplicationAccess(boolean knockApplicationAccess) {
		this.knockApplicationAccess = knockApplicationAccess;
	}
}
