//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.12.07 at 12:13:11 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for TokenizedApi complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="TokenizedApi"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="apiId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="apiName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="application" type="{http://www.w3schools.com}ApplicationName"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TokenizedApi", propOrder = {
    "apiId",
    "apiName",
    "application",
    "status"
})
public class TokenizedApi {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer apiId;
    @XmlElement(required = true)
    protected String apiName;
    @XmlElement(required = true)
    protected String apiMethod;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected ApplicationName application;
    protected boolean status;

    /**
     * Gets the value of the apiId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getApiId() {
        return apiId;
    }

    /**
     * Sets the value of the apiId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setApiId(Integer value) {
        this.apiId = value;
    }

    /**
     * Gets the value of the apiName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApiName() {
        return apiName;
    }

    /**
     * Sets the value of the apiName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApiName(String value) {
        this.apiName = value;
    }

    /**
     * Gets the value of the apiMethod property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getApiMethod() {
        return apiMethod;
    }

    /**
     * Sets the value of the apiMethod property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setApiMethod(String value) {
        this.apiMethod = value;
    }

    /**
     * Gets the value of the application property.
     * 
     * @return
     *     possible object is
     *     {@link ApplicationName }
     *     
     */
    public ApplicationName getApplication() {
        return application;
    }

    /**
     * Sets the value of the application property.
     * 
     * @param value
     *     allowed object is
     *     {@link ApplicationName }
     *     
     */
    public void setApplication(ApplicationName value) {
        this.application = value;
    }

    /**
     * Gets the value of the status property.
     * 
     */
    public boolean isStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     */
    public void setStatus(boolean value) {
        this.status = value;
    }

}
