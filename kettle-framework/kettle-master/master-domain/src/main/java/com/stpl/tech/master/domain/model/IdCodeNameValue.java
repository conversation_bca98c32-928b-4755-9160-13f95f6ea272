package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.math.BigDecimal;

public class IdCodeNameValue implements Serializable {

    protected int id;
    @XmlElement(required = true)
    protected String name;
    @XmlElement(required = true)
    protected String code;
    protected BigDecimal value = BigDecimal.ZERO;

    public IdCodeNameValue() {

    }

    public IdCodeNameValue(int id, String name, String code) {
        super();
        this.id = id;
        this.name = name;
        this.code = code;
    }

    public IdCodeNameValue(int id, String name, String code, int value) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.value = BigDecimal.valueOf(value);
    }

    public IdCodeNameValue(int id, String name, String code, BigDecimal value) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.value = value;
    }

    /**
     * Gets the value of the id property.
     *
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is {@link String }
     *
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value
     *            allowed object is {@link String }
     *
     */
    public void setName(String value) {
        this.name = value;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((code == null) ? 0 : code.hashCode());
        result = prime * result + id;
        result = prime * result + ((name == null) ? 0 : name.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        IdCodeNameValue other = (IdCodeNameValue) obj;
        if (code == null) {
            if (other.code != null)
                return false;
        } else if (!code.equals(other.code))
            return false;
        if (id != other.id)
            return false;
        if (name == null) {
            if (other.name != null)
                return false;
        } else if (!name.equals(other.name))
            return false;
        return true;
    }

    @Override
    public String toString() {
        return "IdCodeValueName [id=" + id + ", name=" + name + ", code" + code +"]";
    }

}
