package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PriceProfileProductMappingRequest {
    List<Integer> productIds;
    List<Integer> priceProfileIds;
    List<Integer> versionIds;

    List<Integer> dimensionIds;
}
