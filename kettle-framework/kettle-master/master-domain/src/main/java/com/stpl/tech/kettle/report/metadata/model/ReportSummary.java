package com.stpl.tech.kettle.report.metadata.model;

public class ReportSummary {

    private int reportId;

    private int versionId;

    private String reportName;

    private String reportVersion;

    private String xmlFilePath;

    private String status;

    private int departmentId;

    private String departmentName;

    private String reportType;

    private String executionEnvironment;

    private String defaultV;
    
    public ReportSummary(int reportId, String reportVersion, String xmlFilePath, String status,
                         String departmentName, String reportType) {
        this.reportId = reportId;
        this.reportVersion = reportVersion;
        this.xmlFilePath = xmlFilePath;
        this.status = status;
        this.departmentName = departmentName;
        this.reportType = reportType;
    }

    public ReportSummary() {
    }

    public int getReportId() {
        return reportId;
    }

    public void setReportId(int reportId) {
        this.reportId = reportId;
    }

    public String getReportVersion() {
        return reportVersion;
    }

    public void setReportVersion(String reportVersion) {
        this.reportVersion = reportVersion;
    }

    public String getXmlFilePath() {
        return xmlFilePath;
    }

    public void setXmlFilePath(String xmlFilePath) {
        this.xmlFilePath = xmlFilePath;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

	public String getReportName() {
		return reportName;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	public int getDepartmentId() {
		return departmentId;
	}

	public void setDepartmentId(int departmentId) {
		this.departmentId = departmentId;
	}

	public String getExecutionEnvironment() {
		return executionEnvironment;
	}

	public void setExecutionEnvironment(String executionEnvironment) {
		this.executionEnvironment = executionEnvironment;
	}

    public int getVersionId() {
        return versionId;
    }

    public void setVersionId(int versionId) {
        this.versionId = versionId;
    }

    public String getDefaultV() {
        return defaultV;
    }

    public void setDefaultV(String defaultV) {
        this.defaultV = defaultV;
    }
}
