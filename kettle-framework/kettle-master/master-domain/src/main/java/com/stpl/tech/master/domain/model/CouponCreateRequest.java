/**
 * 
 */
package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class CouponCreateRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3262531589090757861L;
	private String contactNumber;
	private Boolean startsFromToday;
	private Integer validDays;
	private String url;
	private Boolean compressUrl;
	private String couponPrefix;
	private String sampleCode;
	private Date startDate;

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Boolean getCompressUrl() {
		return compressUrl;
	}

	public void setCompressUrl(Boolean compressUrl) {
		this.compressUrl = compressUrl;
	}

	public Boolean getStartsFromToday() {
		return startsFromToday;
	}

	public void setStartsFromToday(Boolean startsFromToday) {
		this.startsFromToday = startsFromToday;
	}

	public Integer getValidDays() {
		return validDays;
	}

	public void setValidDays(Integer validDays) {
		this.validDays = validDays;
	}

	public String getCouponPrefix() {
		return couponPrefix;
	}

	public void setCouponPrefix(String couponPrefix) {
		this.couponPrefix = couponPrefix;
	}

	public String getSampleCode() {
		return sampleCode;
	}

	public void setSampleCode(String sampleCode) {
		this.sampleCode = sampleCode;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	
}
