/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.04.04 at 12:22:00 PM IST
//

package com.stpl.tech.master.domain.model;

import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import com.stpl.tech.kettle.report.metadata.model.comparator.HasId;
import com.stpl.tech.kettle.report.metadata.model.comparator.HasStatus;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Java class for anonymous complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="region" type="{http://www.w3schools.com}String"/&gt;
 *         &lt;element name="family" type="{http://www.w3schools.com}UnitCategory"/&gt;
 *         &lt;element name="subCategory" type="{http://www.w3schools.com}UnitSubCategory"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}UnitStatus"/&gt;
 *         &lt;element name="unitEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="startDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastBusinessDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="tin" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="division" type="{http://www.w3schools.com}Division"/&gt;
 *         &lt;element name="address" type="{http://www.w3schools.com}Address"/&gt;
 *         &lt;element name="cloneUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="inventoryCloneUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="menuSequenceCloneUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="noOfTerminals" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="noOfTakeawayTerminals" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="workstationEnabled" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="tokenEnabled" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="tokenLimit" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="noOfTables" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitManager" type="{http://www.w3schools.com}Employee"/&gt;
 *         &lt;element name="products" type="{http://www.w3schools.com}Product" maxOccurs="unbounded"/&gt;
 *         &lt;element name="deliveryPartners" type="{http://www.w3schools.com}PartnerDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="taxProfiles" type="{http://www.w3schools.com}TaxProfile" maxOccurs="unbounded"/&gt;
 *         &lt;element name="operationalHours" type="{http://www.w3schools.com}UnitHours" maxOccurs="7"/&gt;
 *         &lt;element name="referenceName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="channel" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "id", "name", "region", "family", "subCategory", "status", "unitEmail", "startDate",
		"lastBusinessDate", "tin","fssai", "division", "address", "cloneUnitId", "inventoryCloneUnitId","menuSequenceCloneUnitId", "noOfTerminals",
		"noOfTakeawayTerminals", "workstationEnabled", "tokenEnabled", "tokenLimit", "tableService","tableServiceType", "noOfTables",
		"unitManager", "products", "deliveryPartners", "paymentModes", "taxProfiles", "referenceName", "channel" ,"shortCode"})
@XmlRootElement(name = "Unit")
public class Unit implements Serializable, Comparable<Unit>, HasId, HasStatus {

	/**
	 *
	 */
	private static final long serialVersionUID = 1536706683006809232L;
	protected int id;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected String region;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected UnitCategory family;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected UnitSubCategory subCategory;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected UnitStatus status;
	@XmlElement(required = true)
	protected String unitEmail;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	protected Date startDate;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	protected Date lastBusinessDate;
	@XmlElement(required = true)
	protected String tin;
	protected String fssai;
	@XmlElement(required = true)
	protected Division division;
	@XmlElement(required = true)
	protected Address address;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer cloneUnitId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer inventoryCloneUnitId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer menuSequenceCloneUnitId;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer creditAccount;
	@XmlElement(defaultValue = "1")
	protected int noOfTerminals;
	@XmlElement(defaultValue = "0")
	protected int noOfTakeawayTerminals;
	protected boolean workstationEnabled;
	protected boolean tokenEnabled;
	protected boolean freeInternetAccess;
	protected boolean tableService;
	protected int tableServiceType;
	@XmlElement(defaultValue = "1")
	protected int noOfTables;
	@XmlElement(defaultValue = "1")
	protected int tokenLimit;
	@XmlElement(required = true, nillable = true)
	protected Employee unitManager;
	@XmlElement(required = true)
	protected List<Product> products;
	protected List<PartnerDetail> deliveryPartners;
	@XmlElement(required = true)
	protected List<TaxProfile> taxProfiles;
	@XmlElement(required = true)
	protected List<Integer> paymentModes;
	@XmlElement(required = false)
	protected List<UnitHours> operationalHours;
	@XmlElement(required = false)
	protected List<UnitHours> gntOperationalHours = new ArrayList<>();
	@XmlElement(required = true)
	protected String referenceName;
	@XmlElement(required = true)
	protected String channel;
	@XmlElement(required = true)
	protected Integer managerId;
	@XmlElement(required = true)
	protected String managerName;
	@XmlElement(required = true)
	protected String managerEmail;
	@XmlElement(required = true)
	protected String managerChannel;
	@XmlElement(required = true)
	protected String managerContact;
	@XmlElement(required = true)
	protected Location location;
	@XmlElement(required = true)
	protected boolean isPartnerPriced;
	@XmlSchemaType(name = "string")
	protected UnitBusinessType unitBusinessType;
	@XmlElement(required = true)
	protected Integer noOfMeter;
	protected boolean dGAvailable;
	protected Company company;
	protected CafeType cafeType;
	protected IdCodeName cafeManager;
	protected TrueCallerSettings trueCallerEnabled;
	protected boolean live;
	protected boolean hotAndColdMerged;
	protected boolean liveInventoryEnabled;
	protected String packagingType;
	protected BigDecimal packagingValue;
	protected  Boolean isOtpViaEmail;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	protected Date handoverDate;
	private String cafeAppStatus;
	private String cafeNeoStatus;
	private String googleMerchantId;
	private String shortName;
	private String costCenterName;
	protected String shortCode;
	protected Integer salesClonedFrom;
	protected Date probableOpeningDate;
	private Integer pricingProfile;
	private String unitZone;
	private String f9Enabled;
	private String revenueCertificateEmail;
	private boolean revenueCertificateGenerationEnable;
	private String varianceAcknowledgementEmployees;
	private boolean isHotspotEnabled;

	private Boolean isClosed;

	private Integer posVersion;
	private String faDaycloseEnabled;
	private Integer unitCafeManager;
	private Date lastHandoverDate;
	private String lastHandoverFrom;
	private String varianceAcknowledgementRequired;
	private boolean publishRealTimeSale;
	private String partnerToPublishSale;

	private String loyalTeaRedemptionAllowed;
	private String autoEdcPayment;

	private String monkRecipeProfile;
	private Integer noOfMonksNeeded;

	private String loyalTeaBurnSwiggyAllowed;

	private Boolean assemblyStrictMode;
	private Boolean milkTrackingEnabled;

	private String closure;

	private String showLoyalteaScreen;
	private Boolean assemblyOtpMode;
	private CafeTimingTrimmedData cafeTimingTrimmedData;

	private List<String> sweetnerAddons = new ArrayList<>();

	private String isSuperUEnabled;

	private String checkBigPanThreshold;

	private Boolean customerLogin = false;

	private Boolean autoTokenEnabled = false;

	private BigDecimal serviceCharge;
	private String serviceChargeDesc;
	private Boolean serviceChargePosEnabled = false;
	private Boolean serviceChargeAppEnabled = false;
	private Boolean isTestingUnit = false;
	private boolean dineIn;
	private boolean takeAway;
	private boolean table;
	private boolean tableSelection;
	private boolean tokenSelection;
	private String orderingType;

	private boolean token;
	
	private List<Brand> brands;

	private Integer customAddonsLimit;
	
	private Boolean feedbackFormEnabledForPos;
	
	private String dreamfolksOutletId;

	public boolean isTableSelection() {
		return tableSelection;
	}

	public void setTableSelection(boolean tableSelection) {
		this.tableSelection = tableSelection;
	}

	public boolean isTokenSelection() {
		return tokenSelection;
	}

	public void setTokenSelection(boolean tokenSelection) {
		this.tokenSelection = tokenSelection;
	}

	public String getOrderingType() {
		return orderingType;
	}

	public void setOrderingType(String orderingType) {
		this.orderingType = orderingType;
	}

	/**
	 * Gets the value of the id property.
	 *
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the region property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getRegion() {
		return region;
	}

	/**
	 * Sets the value of the region property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setRegion(String value) {
		this.region = value;
	}

	/**
	 * Gets the value of the family property.
	 *
	 * @return possible object is {@link UnitCategory }
	 *
	 */
	public UnitCategory getFamily() {
		return family;
	}

	/**
	 * Sets the value of the family property.
	 *
	 * @param value
	 *            allowed object is {@link UnitCategory }
	 *
	 */
	public void setFamily(UnitCategory value) {
		this.family = value;
	}



	/**
	 * Gets the value of the subCategory property.
	 *
	 * @return possible object is {@link UnitSubCategory }
	 *
	 */
	public UnitSubCategory getSubCategory() {
		return subCategory;
	}

	/**
	 * Sets the value of the subCategory property.
	 *
	 * @param value
	 *            allowed object is {@link UnitSubCategory }
	 *
	 */
	public void setSubCategory(UnitSubCategory value) {
		this.subCategory = value;
	}

	/**
	 * Gets the value of the status property.
	 *
	 * @return possible object is {@link UnitStatus }
	 *
	 */
	public UnitStatus getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 *
	 * @param value
	 *            allowed object is {@link UnitStatus }
	 *
	 */
	public void setStatus(UnitStatus value) {
		this.status = value;
	}

	/**
	 * Gets the value of the unitEmail property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getUnitEmail() {
		return unitEmail;
	}

	/**
	 * Sets the value of the unitEmail property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setUnitEmail(String value) {
		this.unitEmail = value;
	}

	/**
	 * Gets the value of the startDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * Sets the value of the startDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setStartDate(Date value) {
		this.startDate = value;
	}

	/**
	 * Gets the value of the lastBusinessDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getLastBusinessDate() {
		return lastBusinessDate;
	}

	/**
	 * Sets the value of the lastBusinessDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setLastBusinessDate(Date value) {
		this.lastBusinessDate = value;
	}

	/**
	 * Gets the value of the tin property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getTin() {
		return tin;
	}

	/**
	 * Sets the value of the tin property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setTin(String value) {
		this.tin = value;
	}

	/**
	 * Gets the value of the division property.
	 *
	 * @return possible object is {@link Division }
	 *
	 */
	public Division getDivision() {
		return division;
	}

	/**
	 * Sets the value of the division property.
	 *
	 * @param value
	 *            allowed object is {@link Division }
	 *
	 */
	public void setDivision(Division value) {
		this.division = value;
	}

	/**
	 * Gets the value of the address property.
	 *
	 * @return possible object is {@link Address }
	 *
	 */
	public Address getAddress() {
		return address;
	}

	/**
	 * Sets the value of the address property.
	 *
	 * @param value
	 *            allowed object is {@link Address }
	 *
	 */
	public void setAddress(Address value) {
		this.address = value;
	}

	/**
	 * Gets the value of the cloneUnitId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getCloneUnitId() {
		return cloneUnitId;
	}

	/**
	 * Sets the value of the cloneUnitId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setCloneUnitId(Integer value) {
		this.cloneUnitId = value;
	}

	/**
	 * Gets the value of the creditAccount property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getCreditAccount() {
		return creditAccount;
	}

	/**
	 * Sets the value of the creditAccount property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setCreditAccount(Integer value) {
		this.creditAccount = value;
	}

	/**
	 * Gets the value of the inventoryCloneUnitId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getInventoryCloneUnitId() {
		return inventoryCloneUnitId;
	}

	/**
	 * Sets the value of the inventoryCloneUnitId property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setInventoryCloneUnitId(Integer value) {
		this.inventoryCloneUnitId = value;
	}

	/**
	 * Gets the value of the menuSequenceCloneUnitId property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getMenuSequenceCloneUnitId() {
		return menuSequenceCloneUnitId;
	}

	/**
	 * Sets the value of the menuSequenceCloneUnitId property.
	 *
	 * @param menuSequenceCloneUnitId
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setMenuSequenceCloneUnitId(Integer menuSequenceCloneUnitId) {
		this.menuSequenceCloneUnitId = menuSequenceCloneUnitId;
	}

	/**
	 * Gets the value of the noOfTerminals property.
	 *
	 */
	public int getNoOfTerminals() {
		return noOfTerminals;
	}

	/**
	 * Sets the value of the noOfTerminals property.
	 *
	 */
	public void setNoOfTerminals(int value) {
		this.noOfTerminals = value;
	}

	/**
	 * Gets the value of the noOfTakeawayTerminals property.
	 *
	 */
	public int getNoOfTakeawayTerminals() {
		return noOfTakeawayTerminals;
	}

	/**
	 * Sets the value of the noOfTakeawayTerminals property.
	 *
	 */
	public void setNoOfTakeawayTerminals(int value) {
		this.noOfTakeawayTerminals = value;
	}

	/**
	 * Gets the value of the workstationEnabled property.
	 *
	 */
	public boolean isWorkstationEnabled() {
		return workstationEnabled;
	}

	/**
	 * Sets the value of the workstationEnabled property.
	 *
	 */
	public void setWorkstationEnabled(boolean value) {
		this.workstationEnabled = value;
	}

	public boolean isTableService() {
		return tableService;
	}


	public void setTableService(boolean tableService) {
		this.tableService = tableService;
	}

	public Integer getTableServiceType() {
		return tableServiceType;
	}

	public void setTableServiceType(Integer tableServiceType) {
		this.tableServiceType = tableServiceType;
	}

	/**
	 * Gets the value of the noOfTables property.
	 *
	 */
	public int getNoOfTables() {
		return noOfTables;
	}

	/**
	 * Sets the value of the noOfTables property.
	 *
	 */
	public void setNoOfTables(int value) {
		this.noOfTables = value;
	}

	/**
	 * Gets the value of the unitManager property.
	 *
	 * @return possible object is {@link Employee }
	 *
	 */
	public Employee getUnitManager() {
		return unitManager;
	}

	/**
	 * Sets the value of the unitManager property.
	 *
	 * @param value
	 *            allowed object is {@link Employee }
	 *
	 */
	public void setUnitManager(Employee value) {
		this.unitManager = value;
	}

	/**
	 * Gets the value of the products property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the products property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getProducts().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link Product }
	 *
	 *
	 */
	public List<Product> getProducts() {
		if (products == null) {
			products = new ArrayList<Product>();
		}
		return this.products;
	}

	/**
	 * Gets the value of the deliveryPartners property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the deliveryPartners property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getDeliveryPartners().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link PartnerDetail }
	 *
	 *
	 */
	public List<PartnerDetail> getDeliveryPartners() {
		if (deliveryPartners == null) {
			deliveryPartners = new ArrayList<PartnerDetail>();
		}
		return this.deliveryPartners;
	}

	/**
	 * Gets the value of the taxProfiles property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the taxProfiles property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getTaxProfiles().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link TaxProfile }
	 *
	 *
	 */
	public List<TaxProfile> getTaxProfiles() {
		if (taxProfiles == null) {
			taxProfiles = new ArrayList<TaxProfile>();
		}
		return this.taxProfiles;
	}

	public List<Integer> getPaymentModes() {
		if (paymentModes == null) {
			paymentModes = new ArrayList<Integer>();
		}
		return this.paymentModes;
	}

	@Override
	public int compareTo(Unit o) {
		return Integer.compare(this.getId(), o.getId());
	}

	/**
	 * Gets the value of the operationalHours property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the operationalHours property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getOperationalHours().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link UnitHours
	 * }
	 *
	 *
	 */
	public List<UnitHours> getOperationalHours() {
		if (operationalHours == null) {
			operationalHours = new ArrayList<UnitHours>();
		}
		return this.operationalHours;
	}

	public void setOperationalHours(List<UnitHours> operationalHours) {
		if (Objects.nonNull(operationalHours)) {
			this.operationalHours = operationalHours;
		}else {
			this.operationalHours = new ArrayList<UnitHours>();
		}
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + id;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Unit other = (Unit) obj;
		if (id != other.id)
			return false;
		return true;
	}

	public boolean isFreeInternetAccess() {
		return freeInternetAccess;
	}

	public void setFreeInternetAccess(boolean freeInternetAccess) {
		this.freeInternetAccess = freeInternetAccess;
	}

	/**
	 * Gets the value of the referenceName property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getReferenceName() {
		return referenceName;
	}

	/**
	 * Sets the value of the referenceName property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setReferenceName(String value) {
		this.referenceName = value;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	/**
	 * @return the managerId
	 */
	public Integer getManagerId() {
		return managerId;
	}

	/**
	 * @param managerId
	 *            the managerId to set
	 */
	public void setManagerId(Integer managerId) {
		this.managerId = managerId;
	}

	public String getManagerEmail() {
		return managerEmail;
	}

	public void setManagerEmail(String managerEmail) {
		this.managerEmail = managerEmail;
	}

	/**
	 * @return the managerSlack
	 */
	public String getManagerChannel() {
		return managerChannel;
	}

	/**
	 * @param managerSlack
	 *            the managerSlack to set
	 */
	public void setManagerChannel(String managerSlack) {
		this.managerChannel = managerSlack;
	}

	/**
	 * @return the managerName
	 */
	public String getManagerName() {
		return managerName;
	}

	/**
	 * @param managerName
	 *            the managerName to set
	 */
	public void setManagerName(String managerName) {
		this.managerName = managerName;
	}

	/**
	 * @return the managerContact
	 */
	public String getManagerContact() {
		return managerContact;
	}

	/**
	 * @param managerContact
	 *            the managerContact to set
	 */
	public void setManagerContact(String managerContact) {
		this.managerContact = managerContact;
	}

	public Location getLocation() {
		return location;
	}

	public void setLocation(Location location) {
		this.location = location;
	}

	public boolean isPartnerPriced() {
		return isPartnerPriced;
	}

	public void setPartnerPriced(boolean isPartnerPriced) {
		this.isPartnerPriced = isPartnerPriced;
	}

	public boolean isTokenEnabled() {
		return tokenEnabled;
	}

	public void setTokenEnabled(boolean tokenEnabled) {
		this.tokenEnabled = tokenEnabled;
	}

	public int getTokenLimit() {
		return tokenLimit;
	}

	public void setTokenLimit(int tokenLimit) {
		this.tokenLimit = tokenLimit;
	}

	public UnitBusinessType getUnitBusinessType() {
		return unitBusinessType;
	}

	public void setUnitBusinessType(UnitBusinessType unitBusinessType) {
		this.unitBusinessType = unitBusinessType;
	}

	public Integer getNoOfMeter() {
		return noOfMeter;
	}

	public void setNoOfMeter(Integer noOfMeter) {
		this.noOfMeter = noOfMeter;
	}

	public boolean isdGAvailable() {
		return dGAvailable;
	}

	public void setdGAvailable(boolean dGAvailable) {
		this.dGAvailable = dGAvailable;
	}

	public Company getCompany() {
		return company;
	}

	public void setCompany(Company company) {
		this.company = company;
	}

	public IdCodeName getCafeManager() {
		return cafeManager;
	}

	public void setCafeManager(IdCodeName cafeManager) {
		this.cafeManager = cafeManager;
	}

    public TrueCallerSettings getTrueCallerEnabled() {
        return trueCallerEnabled;
    }

    public void setTrueCallerEnabled(TrueCallerSettings trueCallerEnabled) {
        this.trueCallerEnabled = trueCallerEnabled;
    }

	public boolean isLive() {
		return live;
	}

	public void setLive(boolean live) {
		this.live = live;
	}

    public boolean isHotAndColdMerged() {
        return hotAndColdMerged;
    }

    public void setHotAndColdMerged(boolean hotAndColdMerged) {
        this.hotAndColdMerged = hotAndColdMerged;
    }

	public boolean isLiveInventoryEnabled() {
		return liveInventoryEnabled;
	}

	public void setLiveInventoryEnabled(boolean liveInventoryEnabled) {
		this.liveInventoryEnabled = liveInventoryEnabled;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.kettle.report.metadata.model.comparator.HasId#getObjectId()
	 */
	@Override
	public Object objectId() {
		return this.id;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.kettle.report.metadata.model.comparator.HasStatus#getCurrentStatus()
	 */
	@Override
	public String currentStatus() {
		return this.status == null ? UnitStatus.IN_ACTIVE.name() : this.status.name();
	}

	public String getPackagingType() {
		return packagingType;
	}

	public void setPackagingType(String packagingType) {
		this.packagingType = packagingType;
	}

	public BigDecimal getPackagingValue() {
		return packagingValue;
	}

	public void setPackagingValue(BigDecimal packagingValue) {
		this.packagingValue = packagingValue;
	}

	public Date getHandoverDate() {
		return handoverDate;
	}

	public void setHandoverDate(Date handoverDate) {
		this.handoverDate = handoverDate;
	}

	public String getCafeAppStatus() {
		return cafeAppStatus;
	}

	public void setCafeAppStatus(String cafeAppStatus) {
		this.cafeAppStatus = cafeAppStatus;
	}

	public String getCafeNeoStatus() {
		return cafeNeoStatus;
	}

	public void setCafeNeoStatus(String cafeNeoStatus) {
		this.cafeNeoStatus = cafeNeoStatus;
	}

	public String getGoogleMerchantId() {
		return googleMerchantId;
	}

	public void setGoogleMerchantId(String googleMerchantId) {
		this.googleMerchantId = googleMerchantId;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getCostCenterName() {
		return costCenterName;
	}

	public void setCostCenterName(String costCenterName) {
		this.costCenterName = costCenterName;
	}

	public String getFssai() {
		return fssai;
	}

	public void setFssai(String fssai) {
		this.fssai = fssai;
	}

	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	public Integer getSalesClonedFrom() {
		return salesClonedFrom;
	}

	public void setSalesClonedFrom(Integer salesClonedFrom) {
		this.salesClonedFrom = salesClonedFrom;
	}

	public Date getProbableOpeningDate() {
		return probableOpeningDate;
	}

	public void setProbableOpeningDate(Date probableOpeningDate) {
		this.probableOpeningDate = probableOpeningDate;
	}

	public Integer getPricingProfile() {
		return pricingProfile;
	}

	public void setPricingProfile(Integer pricingProfile) {
		this.pricingProfile = pricingProfile;
	}

	public String getUnitZone() {
		return unitZone;
	}

	public void setUnitZone(String unitZone) {
		this.unitZone = unitZone;
	}

	public String getF9Enabled() {
		return f9Enabled;
	}

	public void setF9Enabled(String f9Enabled) {
		this.f9Enabled = f9Enabled;
	}

	public String getRevenueCertificateEmail() {
		return revenueCertificateEmail;
	}

	public void setRevenueCertificateEmail(String revenueCertificateEmail) {
		this.revenueCertificateEmail = revenueCertificateEmail;
	}

	public boolean isRevenueCertificateGenerationEnable() {
		return revenueCertificateGenerationEnable;
	}

	public void setRevenueCertificateGenerationEnable(boolean revenueCertificateGenerationEnable) {
		this.revenueCertificateGenerationEnable = revenueCertificateGenerationEnable;
	}


	public Boolean getClosed() {
		return isClosed;
	}

	public void setClosed(Boolean closed) {
		isClosed = closed;
	}

	public boolean isHotspotEnabled() {
		return isHotspotEnabled;
	}

	public void setHotspotEnabled(boolean hotspotEnabled) {
		this.isHotspotEnabled = hotspotEnabled;
	}

	public Integer getPosVersion() {
		return posVersion;
	}

	public void setPosVersion(Integer posVersion) {
		this.posVersion = posVersion;
	}

	public String getFaDaycloseEnabled() {
		return faDaycloseEnabled;
	}

	public void setFaDaycloseEnabled(String faDaycloseEnabled) {
		this.faDaycloseEnabled = faDaycloseEnabled;
	}

	public Integer getUnitCafeManager() {
		return unitCafeManager;
	}

	public void setUnitCafeManager(Integer unitCafeManager) {
		this.unitCafeManager = unitCafeManager;
	}

	public Date getLastHandoverDate() {
		return lastHandoverDate;
	}

	public void setLastHandoverDate(Date lastHandoverDate) {
		this.lastHandoverDate = lastHandoverDate;
	}

	public String getLastHandoverFrom() {
		return lastHandoverFrom;
	}

	public void setLastHandoverFrom(String lastHandoverFrom) {
		this.lastHandoverFrom = lastHandoverFrom;
	}

	public String getVarianceAcknowledgementRequired() {
		return varianceAcknowledgementRequired;
	}

	public void setVarianceAcknowledgementRequired(String varianceAcknowledgementRequired) {
		this.varianceAcknowledgementRequired = varianceAcknowledgementRequired;
	}

	public String getVarianceAcknowledgementEmployees() {
		return varianceAcknowledgementEmployees;
	}

	public void setVarianceAcknowledgementEmployees(String varianceAcknowledgementEmployees) {
		this.varianceAcknowledgementEmployees = varianceAcknowledgementEmployees;
	}

	public List<UnitHours> getGntOperationalHours() {
		return gntOperationalHours;
	}

	public void setGntOperationalHours(List<UnitHours> gntOperationalHours) {
		this.gntOperationalHours = gntOperationalHours;
	}



	public boolean isPublishRealTimeSale() {
		return publishRealTimeSale;
	}

	public void setPublishRealTimeSale(boolean publishRealTimeSale) {
		this.publishRealTimeSale = publishRealTimeSale;
	}

	public String getPartnerToPublishSale() {
		return partnerToPublishSale;
	}

	public void setPartnerToPublishSale(String partnerToPublishSale) {
		this.partnerToPublishSale = partnerToPublishSale;
	}

	public String getLoyalTeaRedemptionAllowed() {
		return loyalTeaRedemptionAllowed;
	}

	public void setLoyalTeaRedemptionAllowed(String loyalTeaRedemptionAllowed) {
		this.loyalTeaRedemptionAllowed = loyalTeaRedemptionAllowed;
	}

	public String getAutoEdcPayment() {
		return autoEdcPayment;
	}

	public void setAutoEdcPayment(String autoEdcPayment) {
		this.autoEdcPayment = autoEdcPayment;
	}

	public String getMonkRecipeProfile() {
		return monkRecipeProfile;
	}

	public void setMonkRecipeProfile(String monkRecipeProfile) {
		this.monkRecipeProfile = monkRecipeProfile;
	}

	public Integer getNoOfMonksNeeded() {
		return noOfMonksNeeded;
	}

	public void setNoOfMonksNeeded(Integer noOfMonksNeeded) {
		this.noOfMonksNeeded = noOfMonksNeeded;
	}

	public void setLoyalTeaBurnSwiggyAllowed(String loyalTeaBurnSwiggyAllowed){
		this.loyalTeaBurnSwiggyAllowed = loyalTeaBurnSwiggyAllowed;
	}

	public String getLoyalTeaBurnSwiggyAllowed(){
		return loyalTeaBurnSwiggyAllowed;
	}

	public Boolean getAssemblyStrictMode() {
		return assemblyStrictMode;
	}

	public void setAssemblyStrictMode(Boolean assemblyStrictMode) {
		this.assemblyStrictMode = assemblyStrictMode;
	}

	public Boolean getMilkTrackingEnabled() {
		return milkTrackingEnabled;
	}

	public void setMilkTrackingEnabled(Boolean milkTrackingEnabled) {
		this.milkTrackingEnabled = milkTrackingEnabled;
	}

	public String getClosure() {
		return closure;
	}

	public void setClosure(String closure) {
		this.closure = closure;
	}

	public void setShowLoyalteaScreen(String showLoyalteaScreen) {
		this.showLoyalteaScreen = showLoyalteaScreen;	}

	public String getShowLoyalteaScreen() {
		return showLoyalteaScreen;
	}

	public Boolean getIsOtpViaEmail() {
		return isOtpViaEmail;
	}

	public void setIsOtpViaEmail(Boolean isOtpViaEmail) {
		this.isOtpViaEmail = isOtpViaEmail;
	}

	public Boolean getAssemblyOtpMode() {
		return assemblyOtpMode;
	}

	public void setAssemblyOtpMode(Boolean assemblyOtpMode) {
		this.assemblyOtpMode = assemblyOtpMode;
	}

	public CafeTimingTrimmedData getCafeTimingTrimmedData() {
		return cafeTimingTrimmedData;
	}
	public void setCafeTimingTrimmedData(CafeTimingTrimmedData cafeTimingTrimmedData){
		this.cafeTimingTrimmedData = cafeTimingTrimmedData;
	}

	public List<String> getSweetnerAddons() {
		return sweetnerAddons;
	}

	public void setSweetnerAddons(List<String> sweetnerAddons) {
		this.sweetnerAddons = sweetnerAddons;
	}

	public String getIsSuperUEnabled() {
		return isSuperUEnabled;
	}

	public void setIsSuperUEnabled(String isSuperUEnabled) {
		this.isSuperUEnabled = isSuperUEnabled;
	}

	public String getCheckBigPanThreshold() {
		return checkBigPanThreshold;
	}

	public void setCheckBigPanThreshold(String checkBigPanThreshold) {
		this.checkBigPanThreshold = checkBigPanThreshold;
	}

	public Boolean getCustomerLogin() {
		return customerLogin;
	}

	public void setCustomerLogin(Boolean customerLogin) {
		this.customerLogin = customerLogin;
	}

	public Boolean getAutoTokenEnabled() {
		return autoTokenEnabled;
	}

	public void setAutoTokenEnabled(Boolean autoTokenEnabled) {
		this.autoTokenEnabled = autoTokenEnabled;
	}

	public BigDecimal getServiceCharge() {
		return serviceCharge;
	}

	public void setServiceCharge(BigDecimal serviceCharge) {
		this.serviceCharge = serviceCharge;
	}

	public Boolean getServiceChargePosEnabled() {
		return serviceChargePosEnabled;
	}

	public void setServiceChargePosEnabled(Boolean serviceChargePosEnabled) {
		this.serviceChargePosEnabled = serviceChargePosEnabled;
	}

	public Boolean getServiceChargeAppEnabled() {
		return serviceChargeAppEnabled;
	}

	public void setServiceChargeAppEnabled(Boolean serviceChargeAppEnabled) {
		this.serviceChargeAppEnabled = serviceChargeAppEnabled;
	}

	public Boolean getIsTestingUnit() {
		return isTestingUnit;
	}
	public void setIsTestingUnit(Boolean isTestingUnit) {
		this.isTestingUnit = isTestingUnit;
	}

    public boolean isDineIn() {
        return dineIn;
    }

    public void setDineIn(boolean dineIn) {
        this.dineIn = dineIn;
    }

    public boolean isTakeAway() {
        return takeAway;
    }

    public void setTakeAway(boolean takeAway) {
        this.takeAway = takeAway;
    }

    public boolean isTable() {
        return table;
    }

    public void setTable(boolean table) {
        this.table = table;
    }


	public CafeType getCafeType() {
		return cafeType;
	}

	public void setCafeType(CafeType cafeType) {
		this.cafeType = cafeType;
	}

	public List<Brand> getBrands() { return this.brands; }

	public void setBrands(List<Brand> brands) { this.brands = brands; }

	public String getServiceChargeDesc() {
		return serviceChargeDesc;
	}

	public void setServiceChargeDesc(String serviceChargeDesc) {
		this.serviceChargeDesc = serviceChargeDesc;
	}
	public Integer getCustomAddonsLimit() {
		return customAddonsLimit;
	}

	public void setCustomAddonsLimit(Integer customAddonsLimit) {
		this.customAddonsLimit = customAddonsLimit;
	}
	
	public Boolean isFeedbackFormEnabledForPos() {
		return feedbackFormEnabledForPos;
	}
	
	public void setFeedbackFormEnabledForPos(Boolean feedbackFormAvailableForPos) {
		feedbackFormEnabledForPos = feedbackFormAvailableForPos;
	}
	
	public String getDreamfolksOutletId() {
		return dreamfolksOutletId;
	}
	
	public void setDreamfolksOutletId(String dreamfolksOutletId) {
		this.dreamfolksOutletId = dreamfolksOutletId;
	}
	
}
