/*

 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 12:10:26 PM IST 
//


package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="categories" type="{http://www.w3schools.com}ListData" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="discountCodes" type="{http://www.w3schools.com}ListData"/&gt;
 *         &lt;element name="complimentaryCodes" type="{http://www.w3schools.com}ListData"/&gt;
 *         &lt;element name="addOns" type="{http://www.w3schools.com}AddonList" maxOccurs="unbounded"/&gt;
 *         &lt;element name="paymentModes" type="{http://www.w3schools.com}PaymentMode" maxOccurs="unbounded"/&gt;
 *         &lt;element name="channelPartner" type="{http://www.w3schools.com}IdCodeName" maxOccurs="unbounded"/&gt;
 *         &lt;element name="deliveryPartner" type="{http://www.w3schools.com}IdCodeName" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "categories",
    "discountCodes",
    "complimentaryCodes",
    "addOns",
    "paymentModes",
    "channelPartner",
    "deliveryPartner",
    "cancellationReasons"
})
@XmlRootElement(name = "TransactionMetadata")
public class TransactionMetadata  implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = 3744429701415659319L;
	protected List<ListData> categories;
    @XmlElement(required = true)
    protected ListData discountCodes;
    @XmlElement(required = true)
    protected ListData complimentaryCodes;
    @XmlElement(required = true)
    protected List<AddonList> addOns;
    @XmlElement(required = true)
    protected List<PaymentMode> paymentModes;
    @XmlElement(required = true)
    protected List<CancellationReason> cancellationReasons;
    @XmlElement(required = true)
    protected List<IdCodeName> channelPartner;
    @XmlElement(required = true)
    protected List<IdCodeName> deliveryPartner;
    @XmlElement(required = false)
    protected Boolean serviceTaxExemptRule;
    protected List<Brand> brandList;
    @XmlElement(required = true)
    protected ListData coreProducts;
    @XmlElement(required = true)
    protected ListData categoryProducts;
    protected Integer subscriptionProductId;
    protected boolean disableCafeToOperate;

    /**
     * Gets the value of the categories property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the categories property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCategories().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ListData }
     * 
     * 
     */
    public List<ListData> getCategories() {
        if (categories == null) {
            categories = new ArrayList<ListData>();
        }
        return this.categories;
    }

    /**
     * Gets the value of the discountCodes property.
     * 
     * @return
     *     possible object is
     *     {@link ListData }
     *     
     */
    public ListData getDiscountCodes() {
        return discountCodes;
    }

    /**
     * Sets the value of the discountCodes property.
     * 
     * @param value
     *     allowed object is
     *     {@link ListData }
     *     
     */
    public void setDiscountCodes(ListData value) {
        this.discountCodes = value;
    }

    /**
     * Gets the value of the complimentaryCodes property.
     * 
     * @return
     *     possible object is
     *     {@link ListData }
     *     
     */
    public ListData getComplimentaryCodes() {
        return complimentaryCodes;
    }

    /**
     * Sets the value of the complimentaryCodes property.
     * 
     * @param value
     *     allowed object is
     *     {@link ListData }
     *     
     */
    public void setComplimentaryCodes(ListData value) {
        this.complimentaryCodes = value;
    }

    /**
     * Gets the value of the addOns property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the addOns property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAddOns().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AddonList }
     * 
     * 
     */
    public List<AddonList> getAddOns() {
        if (addOns == null) {
            addOns = new ArrayList<AddonList>();
        }
        return this.addOns;
    }

    /**
     * Gets the value of the paymentModes property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the paymentModes property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPaymentModes().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentMode }
     * 
     * 
     */
    public List<PaymentMode> getPaymentModes() {
        if (paymentModes == null) {
            paymentModes = new ArrayList<PaymentMode>();
        }
        return this.paymentModes;
    }

    
    public List<CancellationReason> getCancellationReasons() {
        if (cancellationReasons == null) {
        	cancellationReasons = new ArrayList<CancellationReason>();
        }
		return cancellationReasons;
	}

	/**
     * Gets the value of the channelPartner property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the channelPartner property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getChannelPartner().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IdCodeName }
     * 
     * 
     */
    public List<IdCodeName> getChannelPartner() {
        if (channelPartner == null) {
            channelPartner = new ArrayList<IdCodeName>();
        }
        return this.channelPartner;
    }

    /**
     * Gets the value of the deliveryPartner property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the deliveryPartner property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDeliveryPartner().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link IdCodeName }
     * 
     * 
     */
    public List<IdCodeName> getDeliveryPartner() {
        if (deliveryPartner == null) {
            deliveryPartner = new ArrayList<IdCodeName>();
        }
        return this.deliveryPartner;
    }

	public Boolean getServiceTaxExemptRule() {
		return serviceTaxExemptRule;
	}

	public void setServiceTaxExemptRule(Boolean serviceTaxExemptRule) {
		this.serviceTaxExemptRule = serviceTaxExemptRule;
	}

    public List<Brand> getBrandList() {
        if (brandList == null) {
            brandList = new ArrayList<>();
        }
        return brandList;
    }

    public void setBrandList(List<Brand> brandList) {
        this.brandList = brandList;

    }

	public ListData getCoreProducts() {
		return coreProducts;
	}

	public void setCoreProducts(ListData coreProducts) {
		this.coreProducts = coreProducts;
	}

	public ListData getCategoryProducts() {
		return categoryProducts;
	}

	public void setCategoryProducts(ListData categoryProducts) {
		this.categoryProducts = categoryProducts;
	}

	public Integer getSubscriptionProductId() {
		return subscriptionProductId;
	}

	public void setSubscriptionProductId(Integer subscriptionProductId) {
		this.subscriptionProductId = subscriptionProductId;
	}

    public boolean isDisableCafeToOperate() {
        return disableCafeToOperate;
    }

    public void setDisableCafeToOperate(boolean disableCafeToOperate) {
        this.disableCafeToOperate = disableCafeToOperate;
    }
}
