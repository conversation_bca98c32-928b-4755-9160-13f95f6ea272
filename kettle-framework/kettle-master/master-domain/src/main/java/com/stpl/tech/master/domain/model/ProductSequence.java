package com.stpl.tech.master.domain.model;

import java.util.Date;
import java.util.Map;

public class ProductSequence {


    private Integer id;
    private IdName product;
    private Integer productIndex;
    private IdName productGroup;
    private IdName createdBy;
    private IdName updatedBy;
    private Date creationTime;
    private Date lastUpdateTime;
    private String status;
    private boolean recommended;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public IdName getProduct() {
        return product;
    }

    public void setProduct(IdName product) {
        this.product = product;
    }

    public Integer getProductIndex() {
        return productIndex;
    }

    public void setProductIndex(Integer productIndex) {
        this.productIndex = productIndex;
    }

    public IdName getProductGroup() {
        return productGroup;
    }

    public void setProductGroup(IdName productGroup) {
        this.productGroup = productGroup;
    }

    public IdName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdName createdBy) {
        this.createdBy = createdBy;
    }

    public IdName getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(IdName updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isRecommended() {
        return recommended;
    }

    public void setRecommended(boolean recommended) {
        this.recommended = recommended;
    }
}
