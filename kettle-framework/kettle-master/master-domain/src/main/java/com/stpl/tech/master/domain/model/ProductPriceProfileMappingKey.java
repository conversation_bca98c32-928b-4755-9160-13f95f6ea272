package com.stpl.tech.master.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
public class ProductPriceProfileMappingKey {
    Integer priceProfileId;

    Integer versionNumber;


    Integer productId;

    Integer dimensionId;
}
