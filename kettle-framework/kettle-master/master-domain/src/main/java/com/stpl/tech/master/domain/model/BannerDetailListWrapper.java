package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.List;

public class BannerDetailListWrapper implements Serializable {

    private static final long serialVersionUID = -4156774318267158509L;

    List<BannerDetail> list;

    public BannerDetailListWrapper() {
    }

    public BannerDetailListWrapper(List<BannerDetail> list) {
        this.list = list;
    }

    public List<BannerDetail> getList() {
        return list;
    }

    public void setList(List<BannerDetail> list) {
        this.list = list;
    }
}
