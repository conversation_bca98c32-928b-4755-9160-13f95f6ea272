/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.03.01 at 03:29:06 PM IST 
//


package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for CouponDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CouponDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="code" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="usage" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="startDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="endDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="reusable" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="reusableByCustomer" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="maxUsage" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="offer" type="{http://www.w3schools.com}OfferDetail"/&gt;
 *         &lt;element name="manualOverride" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="couponMappingList" type="{http://www.w3schools.com}CouponMapping" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CouponDetail", propOrder = {
    "id",
    "code",
    "status",
    "usage",
    "startDate",
    "endDate",
    "reusable",
    "reusableByCustomer",
    "couponApplicability",
    "maxUsage",
    "offer",
    "manualOverride",
    "couponMappingList"
})
public class CouponDetail implements Serializable {

    private static final long serialVersionUID = 3156411541062422067L;
    protected int id;
    @XmlElement(required = true)
    protected String code;
    @XmlElement(required = true)
    protected String status;
    protected int usage;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date startDate;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date endDate;
    protected boolean reusable;
    protected boolean reusableByCustomer;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer couponApplicability;

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer maxUsage;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer maxCustomerUsage;
    @XmlElement(required = true)
    protected OfferDetail offer;
    protected boolean manualOverride;
    protected List<CouponMapping> couponMappingList;
    protected Map<String,Set<CouponMapping>> mappings;

    @XmlElement
    protected Boolean customerVisibility;

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the code property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCode() {
        return code;
    }

    /**
     * Sets the value of the code property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCode(String value) {
        this.code = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the usage property.
     * 
     */
    public int getUsage() {
        return usage;
    }

    /**
     * Sets the value of the usage property.
     * 
     */
    public void setUsage(int value) {
        this.usage = value;
    }

    /**
     * Gets the value of the startDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * Sets the value of the startDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStartDate(Date value) {
        this.startDate = value;
    }

    /**
     * Gets the value of the endDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     * Sets the value of the endDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEndDate(Date value) {
        this.endDate = value;
    }

    /**
     * Gets the value of the reusable property.
     * 
     */
    public boolean isReusable() {
        return reusable;
    }

    /**
     * Sets the value of the reusable property.
     * 
     */
    public void setReusable(boolean value) {
        this.reusable = value;
    }

    /**
     * Gets the value of the reusableByCustomer property.
     * 
     */
    public boolean isReusableByCustomer() {
        return reusableByCustomer;
    }

    /**
     * Sets the value of the reusableByCustomer property.
     * 
     */
    public void setReusableByCustomer(boolean value) {
        this.reusableByCustomer = value;
    }

    /**
     * Gets the value of the maxUsage property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getMaxUsage() {
        return maxUsage;
    }

    /**
     * Sets the value of the maxUsage property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setMaxUsage(Integer value) {
        this.maxUsage = value;
    }

    /**
     * Gets the value of the offer property.
     * 
     * @return
     *     possible object is
     *     {@link OfferDetail }
     *     
     */
    public OfferDetail getOffer() {
        return offer;
    }

    /**
     * Sets the value of the offer property.
     * 
     * @param value
     *     allowed object is
     *     {@link OfferDetail }
     *     
     */
    public void setOffer(OfferDetail value) {
        this.offer = value;
    }

    /**
     * Gets the value of the manualOverride property.
     * 
     */
    public boolean isManualOverride() {
        return manualOverride;
    }

    /**
     * Sets the value of the manualOverride property.
     * 
     */
    public void setManualOverride(boolean value) {
        this.manualOverride = value;
    }

    /**
     * Gets the value of the couponMappingList property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the couponMappingList property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCouponMappingList().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link CouponMapping }
     * 
     * 
     */
    public List<CouponMapping> getCouponMappingList() {
        if (couponMappingList == null) {
            couponMappingList = new ArrayList<CouponMapping>();
        }
        return this.couponMappingList;
    }

    public void setCouponMappingList(List<CouponMapping> couponMappingList) {
        this.couponMappingList = couponMappingList;
    }

    public Map<String, Set<CouponMapping>> getMappings() {
        if (mappings == null) {
        	mappings = new HashMap<String,Set<CouponMapping>>();
        }
		return this.mappings;
	}

	public Integer getMaxCustomerUsage() {
		return maxCustomerUsage;
	}

	public void setMaxCustomerUsage(Integer maxCustomerUsage) {
		this.maxCustomerUsage = maxCustomerUsage;
	}

    public Integer getCouponApplicability() {
        return couponApplicability;
    }

    public void setCouponApplicability(Integer couponApplicability) {
        this.couponApplicability = couponApplicability;
    }

    public Boolean getCustomerVisibility() { return this.customerVisibility; }

    public void setCustomerVisibility(Boolean customerVisibility) { this.customerVisibility = customerVisibility; }

}
