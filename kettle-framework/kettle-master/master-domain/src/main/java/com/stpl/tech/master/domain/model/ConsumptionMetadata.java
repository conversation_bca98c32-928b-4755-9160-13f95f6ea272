package com.stpl.tech.master.domain.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@ToString
public class ConsumptionMetadata implements Serializable {

    private static final long serialVersionUID = -4015080891513242493L;

    private Map<String, ConsumptionCodeData> patti = new HashMap<>();
    private Map<String, ConsumptionCodeData> sugar = new HashMap<>();
    private Map<String, ConsumptionCodeData> gur = new HashMap<>();
    private Map<String, ConsumptionCodeData> honey = new HashMap<>();
    private Map<String, ConsumptionCodeData> sugarFree = new HashMap<>();
}