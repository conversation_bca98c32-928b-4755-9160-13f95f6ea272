package com.stpl.tech.master.domain.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.io.Serializable;

public class CityLocalityKey implements Serializable {

    private static final long serialVersionUID = -5157651671917508852L;

    private String city;
    private String locality;

    public CityLocalityKey() {
    }

    public CityLocalityKey(String city, String locality) {

        this.city = city;
        this.locality = locality;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        CityLocalityKey that = (CityLocalityKey) o;

        return new EqualsBuilder()
                .append(city, that.city)
                .append(locality, that.locality)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(city)
                .append(locality)
                .toHashCode();
    }
}
