package com.stpl.tech.master.domain.model;


import com.stpl.tech.util.AppConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.File;
import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CategoryAttributesDomain implements Serializable {

    private Integer attributeId;
    private Integer rlId;
    private String rlName;
    private String defaultVisibility = AppConstants.NO;
    private Integer sequenceNumber;
    private String tagImageFile;
}
