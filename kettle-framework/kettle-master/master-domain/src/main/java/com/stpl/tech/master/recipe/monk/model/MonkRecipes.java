package com.stpl.tech.master.recipe.monk.model;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 26-04-2018.
 */
public class MonkRecipes {

    private String version;
    Map<String,String> recipes;
    Map<String,MonkVersionMetadata> monkVersionMap;
    String recipeRegion;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Map<String, String> getRecipes() {
        return recipes;
    }

    public void setRecipes(Map<String, String> recipes) {
        this.recipes = recipes;
    }

    public Map<String, MonkVersionMetadata> getMonkVersionMap() {
        return monkVersionMap;
    }

    public void setMonkVersionMap(Map<String, MonkVersionMetadata> monkVersionMap) {
        this.monkVersionMap = monkVersionMap;
    }

    public String getRecipeRegion() {
        return recipeRegion;
    }

    public void setRecipeRegion(String recipeRegion) {
        this.recipeRegion = recipeRegion;
    }

}
