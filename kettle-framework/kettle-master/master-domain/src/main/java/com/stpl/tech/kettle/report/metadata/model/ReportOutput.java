//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.07.28 at 05:13:07 PM IST 
//


package com.stpl.tech.kettle.report.metadata.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ReportOutput.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="ReportOutput"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="INLINE"/&gt;
 *     &lt;enumeration value="CSV"/&gt;
 *     &lt;enumeration value="EXCEL"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "ReportOutput")
@XmlEnum
public enum ReportOutput {

    INLINE,
    CSV,
    EXCEL;

    public String value() {
        return name();
    }

    public static ReportOutput fromValue(String v) {
        return valueOf(v);
    }

}
