package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CondimentGroupData implements Serializable {

    private static final long serialVersionUID = 3454843370201884691L;

    private Integer groupId;
    private String groupName;
    private String source;
    private Integer quantity;

}
