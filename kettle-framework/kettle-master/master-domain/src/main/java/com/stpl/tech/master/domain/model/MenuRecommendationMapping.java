package com.stpl.tech.master.domain.model;

import java.math.BigDecimal;
import java.util.List;

public class MenuRecommendationMapping {


	private Integer productId;
	private String dimension;
	private List<MenuRecommendationMapping> recommendation;
	private Integer index;
	private String recommendationTitle;
	private BigDecimal discountAmount;
	private String discountType;

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public List<MenuRecommendationMapping> getRecommendation() {
		return recommendation;
	}

	public void setRecommendation(List<MenuRecommendationMapping> recommendation) {
		this.recommendation = recommendation;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getRecommendationTitle() {
		return recommendationTitle;
	}

	public void setRecommendationTitle(String recommendationTitle) {
		this.recommendationTitle = recommendationTitle;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public String getDiscountType() {
		return discountType;
	}

	public void setDiscountType(String discountType) {
		this.discountType = discountType;
	}
}
