package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;


@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ProductDimensionKey implements Serializable {

    @Serial
    private static final long serialVersionUID = -3928756498780441916L;
    Integer productId;
    String dimension;
}
