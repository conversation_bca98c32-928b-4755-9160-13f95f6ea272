package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ProductCityImageMappingDomain implements Serializable {
    private Integer productImgMappingId;
    private Integer productId;
    private String imageType;
    private String imageUrl;
    private Integer lastUpdatedBy;
    private Date lastUpdationTime;
    private String status;
    private String cityName;

}
