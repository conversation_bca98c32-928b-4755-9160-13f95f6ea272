/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.15 at 05:28:10 PM IST 
//


package com.stpl.tech.kettle.report.metadata.model;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.kettle.report.metadata.model package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.kettle.report.metadata.model
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ReportCategories }
     * 
     */
    public ReportCategories createReportCategories() {
        return new ReportCategories();
    }

    /**
     * Create an instance of {@link ReportCategory }
     * 
     */
    public ReportCategory createReportCategory() {
        return new ReportCategory();
    }

    /**
     * Create an instance of {@link ReportData }
     * 
     */
    public ReportData createReportData() {
        return new ReportData();
    }

    /**
     * Create an instance of {@link ReportParam }
     * 
     */
    public ReportParam createReportParam() {
        return new ReportParam();
    }

    /**
     * Create an instance of {@link ReportNotification }
     * 
     */
    public ReportNotification createReportNotification() {
        return new ReportNotification();
    }

}
