package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LCDMenuImageDomain {

    private Integer id;
    private String name;
    private String path;
    private String url;

    private String type;

    private String size;
    private String version;
    private String region;
    private String priceProfile;
    private String orientation;
    private String slot;
    private String lcdType;

    private Date uploadTime;
    private Integer uploadedBy;
    private Date updationTime;
    private Integer updatedBy;
    private List<LCDMenuImageVariantDomain> variants;
}
