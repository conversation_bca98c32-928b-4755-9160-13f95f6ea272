package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitContactData implements Serializable {

    private static final long serialVersionUID = -441674439916804767L;

    private Integer unitId;
    private List<ContactInfo> contacts;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfo implements Serializable {
        private static final long serialVersionUID = -441674439916804768L;

        private Integer id;
        private String referenceName;
        private Long contactNumber;
        private String status;
    }
}
