package com.stpl.tech.master.domain.model;


import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class UnitPriceProfileMappingDomain {
    private Integer id;
    private IdCodeName unit;
    private IdCodeName priceProfile;
    private Integer priceProfileVersion;
    private Integer channelPartnerId;
    private Integer brandId;
    private String mappingStatus;
    private Integer createdBy;
    private Date creationTime;
    private Date updationTime;
    private Integer updatedBy;
}
