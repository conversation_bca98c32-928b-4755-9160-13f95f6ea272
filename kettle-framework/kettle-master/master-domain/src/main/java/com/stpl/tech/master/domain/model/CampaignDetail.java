package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class CampaignDetail implements Serializable {

    private static final long serialVersionUID = 547050883711532872L;
    private Integer campaignId;
    private String primaryUrl;
    private String campaignStrategy;
    private String campaignSource;
    private String campaignMedium;
    private String campaignName;
    private String campaignCategory;
    private String campaignDesc;
    private String couponCode;
    private String couponCodeDesc;
    private boolean couponClone;
    private String region;
    private String city;
    private String unitIds;
    private Integer usageLimit;
    private Date startDate;
    private Date endDate;
    private Integer validity;
    private String heroBannerMobile;
    private String heroBannerDesktop;
    private String landingPageDesc;
    private String smsTemplate;
    private String smsReminder;
    private String whatsappTemplate;
    private String whatsappReminder;
    private Integer reminderDayGap;
    private String utmHeading;
    private String utmDesc;
    private String utmImageUrl;
    private String redirectionUrl;
    private String campaignStatus;
    private String campaignToken;
    private String image1;
    private String image2;
    private String image3;
    private String longUrl;
    private String shortUrl;
    private String newCustomerOnly;
    private String campaignReach;
    private Map<String, Map<Integer, CampaignMapping>> mappings;
    private String couponPrefix;
    private Integer linkedCampaignId;
    private Integer couponApplicableAfter;
    private Integer brandId;
    private Boolean applicableForOrder;
    private String parentCampaignStrategy;
    private Integer launchUnitId;

    private Date cafeLaunchDate;
    private String crmAppBannerUrl;

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public String getPrimaryUrl() {
        return primaryUrl;
    }

    public void setPrimaryUrl(String primaryUrl) {
        this.primaryUrl = primaryUrl;
    }

    public String getCampaignSource() {
        return campaignSource;
    }

    public String getCampaignStrategy() {
        return campaignStrategy;
    }

    public void setCampaignStrategy(String campaignStrategy) {
        this.campaignStrategy = campaignStrategy;
    }

    public void setCampaignSource(String campaignSource) {
        this.campaignSource = campaignSource;
    }

    public String getCampaignMedium() {
        return campaignMedium;
    }

    public void setCampaignMedium(String campaignMedium) {
        this.campaignMedium = campaignMedium;
    }

    public String getCampaignName() {
        return campaignName;
    }

    public void setCampaignName(String campaignName) {
        this.campaignName = campaignName;
    }

    public String getCampaignCategory() {
        return campaignCategory;
    }

    public void setCampaignCategory(String campaignCategory) {
        this.campaignCategory = campaignCategory;
    }

    public String getCampaignDesc() {
        return campaignDesc;
    }

    public void setCampaignDesc(String campaignDesc) {
        this.campaignDesc = campaignDesc;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getCouponCodeDesc() {
        return couponCodeDesc;
    }

    public void setCouponCodeDesc(String couponCodeDesc) {
        this.couponCodeDesc = couponCodeDesc;
    }


    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(String unitIds) {
        this.unitIds = unitIds;
    }

    public Integer getUsageLimit() {
        return usageLimit;
    }

    public void setUsageLimit(Integer usageLimit) {
        this.usageLimit = usageLimit;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getValidity() {
        return validity;
    }

    public void setValidity(Integer validity) {
        this.validity = validity;
    }

    public String getHeroBannerMobile() {
        return heroBannerMobile;
    }

    public void setHeroBannerMobile(String heroBannerMobile) {
        this.heroBannerMobile = heroBannerMobile;
    }

    public String getHeroBannerDesktop() {
        return heroBannerDesktop;
    }

    public void setHeroBannerDesktop(String heroBannerDesktop) {
        this.heroBannerDesktop = heroBannerDesktop;
    }

    public String getLandingPageDesc() {
        return landingPageDesc;
    }

    public void setLandingPageDesc(String landingPageDesc) {
        this.landingPageDesc = landingPageDesc;
    }

    public String getSmsTemplate() {
        return smsTemplate;
    }

    public void setSmsTemplate(String smsTemplate) {
        this.smsTemplate = smsTemplate;
    }

    public String getSmsReminder() {
        return smsReminder;
    }

    public void setSmsReminder(String smsReminder) {
        this.smsReminder = smsReminder;
    }

    public String getWhatsappTemplate() {
        return whatsappTemplate;
    }

    public void setWhatsappTemplate(String whatsappTemplate) {
        this.whatsappTemplate = whatsappTemplate;
    }

    public String getWhatsappReminder() {
        return whatsappReminder;
    }

    public void setWhatsappReminder(String whatsappReminder) {
        this.whatsappReminder = whatsappReminder;
    }

    public Integer getReminderDayGap() {
        return reminderDayGap;
    }

    public void setReminderDayGap(Integer reminderDayGap) {
        this.reminderDayGap = reminderDayGap;
    }

    public String getUtmHeading() {
        return utmHeading;
    }

    public void setUtmHeading(String utmHeading) {
        this.utmHeading = utmHeading;
    }

    public String getUtmDesc() {
        return utmDesc;
    }

    public void setUtmDesc(String utmDesc) {
        this.utmDesc = utmDesc;
    }

    public String getUtmImageUrl() {
        return utmImageUrl;
    }

    public void setUtmImageUrl(String utmImageUrl) {
        this.utmImageUrl = utmImageUrl;
    }

    public String getRedirectionUrl() {
        return redirectionUrl;
    }

    public void setRedirectionUrl(String redirectionUrl) {
        this.redirectionUrl = redirectionUrl;
    }

    public String getCampaignStatus() {
        return campaignStatus;
    }

    public void setCampaignStatus(String campaignStatus) {
        this.campaignStatus = campaignStatus;
    }

    public String getCampaignToken() {
        return campaignToken;
    }

    public void setCampaignToken(String campaignToken) {
        this.campaignToken = campaignToken;
    }

    public String getImage1() {
        return image1;
    }

    public void setImage1(String image1) {
        this.image1 = image1;
    }

    public String getImage2() {
        return image2;
    }

    public void setImage2(String image2) {
        this.image2 = image2;
    }

    public String getImage3() {
        return image3;
    }

    public void setImage3(String image3) {
        this.image3 = image3;
    }

    public String getLongUrl() {
        return longUrl;
    }

    public void setLongUrl(String longUrl) {
        this.longUrl = longUrl;
    }

    public String getShortUrl() {
        return shortUrl;
    }

    public void setShortUrl(String shortUrl) {
        this.shortUrl = shortUrl;
    }

    public String getNewCustomerOnly() {
        return newCustomerOnly;
    }

    public void setNewCustomerOnly(String newCustomerOnly) {
        this.newCustomerOnly = newCustomerOnly;
    }

    public String getCampaignReach() {
        return campaignReach;
    }

    public void setCampaignReach(String campaignReach) {
        this.campaignReach = campaignReach;
    }

    public boolean isCouponClone() {
        return couponClone;
    }

    public void setCouponClone(boolean couponClone) {
        this.couponClone = couponClone;
    }

    public Map<String, Map<Integer, CampaignMapping>> getMappings() {
        return mappings;
    }

    public void setMappings(Map<String, Map<Integer, CampaignMapping>> mappings) {
        this.mappings = mappings;
    }


    public String getCouponPrefix() {
        return couponPrefix;
    }

    public void setCouponPrefix(String couponPrefix) {
        this.couponPrefix = couponPrefix;
    }

    public Integer getLinkedCampaignId() {
        return linkedCampaignId;
    }

    public void setLinkedCampaignId(Integer linkedCampaignId) {
        this.linkedCampaignId = linkedCampaignId;
    }

    public Integer getCouponApplicableAfter() {
        return couponApplicableAfter;
    }

    public void setCouponApplicableAfter(Integer couponApplicableAfter) {
        this.couponApplicableAfter = couponApplicableAfter;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Boolean getApplicableForOrder() {
        return applicableForOrder;
    }

    public void setApplicableForOrder(Boolean applicableForOrder) {
        this.applicableForOrder = applicableForOrder;
    }

    public String getParentCampaignStrategy() {
        return parentCampaignStrategy;
    }

    public void setParentCampaignStrategy(String parentCampaignStrategy) {
        this.parentCampaignStrategy = parentCampaignStrategy;
    }

    public Integer getLaunchUnitId() {
        return launchUnitId;
    }

    public void setLaunchUnitId(Integer launchUnitId) {
        this.launchUnitId = launchUnitId;
    }

    public Date getCafeLaunchDate() {
        return cafeLaunchDate;
    }

    public void setCafeLaunchDate(Date cafeLaunchDate) {
        this.cafeLaunchDate = cafeLaunchDate;
    }

    public String getCrmAppBannerUrl() {
        return crmAppBannerUrl;
    }

    public void setCrmAppBannerUrl(String crmAppBannerUrl) {
        this.crmAppBannerUrl = crmAppBannerUrl;
    }

    @Override
    public String toString() {
        return "CampaignDetail{" +
                "campaignId=" + campaignId +
                ", primaryUrl='" + primaryUrl + '\'' +
                ", campaignStrategy='" + campaignStrategy + '\'' +
                ", campaignSource='" + campaignSource + '\'' +
                ", campaignMedium='" + campaignMedium + '\'' +
                ", campaignName='" + campaignName + '\'' +
                ", campaignCategory='" + campaignCategory + '\'' +
                ", campaignDesc='" + campaignDesc + '\'' +
                ", couponCode='" + couponCode + '\'' +
                ", couponCodeDesc='" + couponCodeDesc + '\'' +
                ", couponClone=" + couponClone +
                ", region='" + region + '\'' +
                ", city='" + city + '\'' +
                ", unitIds='" + unitIds + '\'' +
                ", usageLimit=" + usageLimit +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", validity=" + validity +
                ", heroBannerMobile='" + heroBannerMobile + '\'' +
                ", heroBannerDesktop='" + heroBannerDesktop + '\'' +
                ", landingPageDesc='" + landingPageDesc + '\'' +
                ", smsTemplate='" + smsTemplate + '\'' +
                ", smsReminder='" + smsReminder + '\'' +
                ", whatsappTemplate='" + whatsappTemplate + '\'' +
                ", whatsappReminder='" + whatsappReminder + '\'' +
                ", reminderDayGap=" + reminderDayGap +
                ", utmHeading='" + utmHeading + '\'' +
                ", utmDesc='" + utmDesc + '\'' +
                ", utmImageUrl='" + utmImageUrl + '\'' +
                ", redirectionUrl='" + redirectionUrl + '\'' +
                ", campaignStatus='" + campaignStatus + '\'' +
                ", campaignToken='" + campaignToken + '\'' +
                ", image1='" + image1 + '\'' +
                ", image2='" + image2 + '\'' +
                ", image3='" + image3 + '\'' +
                ", longUrl='" + longUrl + '\'' +
                ", shortUrl='" + shortUrl + '\'' +
                ", newCustomerOnly='" + newCustomerOnly + '\'' +
                ", campaignReach='" + campaignReach + '\'' +
                ", mappings=" + mappings +
                ", couponPrefix='" + couponPrefix + '\'' +
                ", linkedCampaignId=" + linkedCampaignId +
                ", couponApplicableAfter=" + couponApplicableAfter +
                ", brandId=" + brandId +
                ", applicableForOrder=" + applicableForOrder +
                ", parentCampaignStrategy='" + parentCampaignStrategy + '\'' +
                ", launchUnitId=" + launchUnitId +
                '}';
    }
}
