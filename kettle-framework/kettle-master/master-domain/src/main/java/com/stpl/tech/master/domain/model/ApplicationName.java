//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.19 at 03:52:43 PM IST
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;
import java.util.HashSet;
import java.util.Set;

/**
 * <p>
 * Java class for ApplicationName.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * <p>
 *
 * <pre>
 * &lt;simpleType name="ApplicationName"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="KETTLE_SERVICE"/&gt;
 *     &lt;enumeration value="NEO_SERVICE"/&gt;
 *     &lt;enumeration value="NOAH_SERVICE"/&gt;
 *     &lt;enumeration value="MASTER_SERVICE"/&gt;
 *     &lt;enumeration value="MASTER_SERVICE"/&gt;
 *     &lt;enumeration value="KETTLE_ADMIN"/&gt;
 *     &lt;enumeration value="KETTLE_CRM"/&gt;
 *     &lt;enumeration value="SCM_SERVICE"/&gt;
 *     &lt;enumeration value="KETTLE_ANALYTICS"/&gt;
 *     &lt;enumeration value="KETTLE_CHECKLIST"/&gt;
 *     &lt;enumeration value="WORKSTATION"/&gt;
 *     &lt;enumeration value="FORMS_SERVICE"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 *
 */
@XmlType(name = "ApplicationName")
@XmlEnum
public enum ApplicationName {

	@XmlEnumValue("KETTLE_SERVICE") KETTLE_SERVICE("KETTLE_SERVICE",1,false),
	@XmlEnumValue("NEO_SERVICE") NEO_SERVICE("NEO_SERVICE",3,false),
	@XmlEnumValue("DINE_IN") DINE_IN("DINE_IN",15,false),
	@XmlEnumValue("NOAH_SERVICE") NOAH_SERVICE("NOAH_SERVICE",2,false),
	@XmlEnumValue("MASTER_SERVICE") MASTER_SERVICE("MASTER_SERVICE",4,false),
	@XmlEnumValue("KETTLE_ADMIN") KETTLE_ADMIN("KETTLE_ADMIN",5,false),
	@XmlEnumValue("KETTLE_CRM") KETTLE_CRM("KETTLE_CRM",6,false),
	@XmlEnumValue("SCM_SERVICE") SCM_SERVICE("SCM_SERVICE",7,false),
	@XmlEnumValue("KETTLE_ANALYTICS") KETTLE_ANALYTICS("KETTLE_ANALYTICS",8,false),
	@XmlEnumValue("KETTLE_CHECKLIST") KETTLE_CHECKLIST("KETTLE_CHECKLIST",9,false),
	@XmlEnumValue("WORKSTATION") WORKSTATION("WORKSTATION",10,false),
	@XmlEnumValue("FORMS_SERVICE") FORMS_SERVICE("FORMS_SERVICE",11,false),
	@XmlEnumValue("SERVICE_ORDER") SERVICE_ORDER("SERVICE_ORDER",12,false),
	@XmlEnumValue("APP_INSTALLER") APP_INSTALLER("APP_INSTALLER", 14,false),
	@XmlEnumValue("CHANNEL_PARTNER") CHANNEL_PARTNER("CHANNEL_PARTNER",13,false),
	@XmlEnumValue("KIOSK_SERVICE") KIOSK_SERVICE("KIOSK_SERVICE",16,false),
	@XmlEnumValue("REKOGNITION_SERVICE") REKOGNITION_SERVICE("REKOGNITION_SERVICE",17,false),
	@XmlEnumValue("OFFER_SERVICE") OFFER_SERVICE("OFFER_SERVICE",18,false),
	@XmlEnumValue("ATTENDANCE_SERVICE") ATTENDANCE_SERVICE("ATTENDANCE_SERVICE",19,true),
	@XmlEnumValue("KNOCK_SERVICE") KNOCK_SERVICE("KNOCK_SERVICE",20,false)
	,@XmlEnumValue("KETTLE_OPS") KETTLE_OPS("KETTLE_OPS",21,true);
	private final String value;
	private final int id;
	private final Boolean refreshTokenEnabled;

	ApplicationName(String v, int id , Boolean refreshTokenEnabled) {
		value = v;
		this.id = id;
		this.refreshTokenEnabled = refreshTokenEnabled;
	}

	public String value() {
		return value;
	}

	public int id() {
		return id;
	}

	public Boolean getRefreshTokenEnabled(){return refreshTokenEnabled;}

	public static ApplicationName fromValue(String v) {
		for (ApplicationName c : ApplicationName.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

	public static Set<ApplicationName> accessFor(Designation designation) {
		Set<ApplicationName> names = new HashSet<>();
		if (designation.adminSystemAccess) {
			names.add(KETTLE_ADMIN);
		}
		if (designation.analyticsSystemAccess) {
			names.add(KETTLE_ANALYTICS);
		}
		if (designation.scmSystemAccess) {
			names.add(SCM_SERVICE);
			names.add(SERVICE_ORDER);
		}
		if (designation.crmSystemAccess) {
			names.add(KETTLE_CRM);
		}
		if (designation.clmSystemAccess) {
			names.add(KETTLE_CRM);
		}
		if (designation.clmSystemAccess) {
			names.add(KETTLE_CRM);
		}
		if (designation.formsSystemAccess) {
			names.add(FORMS_SERVICE);
		}
		if (designation.channelPartnerSystemAccess) {
			names.add(CHANNEL_PARTNER);
		}
		if (designation.appInstallerAccess) {
			names.add(APP_INSTALLER);
		}
		if (designation.transactionSystemAccess) {
			names.add(KETTLE_SERVICE);
			names.add(MASTER_SERVICE);
			names.add(KETTLE_CHECKLIST);
			names.add(WORKSTATION);
			names.add(CHANNEL_PARTNER);
			//names.add(OFFER_SERVICE);
		}
		if(designation.attendanceAccess){
			names.add(ATTENDANCE_SERVICE);
		}
		if(designation.knockApplicationAccess){
			names.add(KNOCK_SERVICE);
		}
		/*names.add(ATTENDANCE_EMPLOYEE_SERVICE);*/
		names.add(KETTLE_OPS);
		return names;
	}
}
