package com.stpl.tech.master.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class EmployeeRoleUpdateRequest implements Serializable {

    @JsonProperty(value = "employeeId")
    private Integer employeeId;

    @JsonProperty(value = "updatedBy")
    private Integer updatedBy;

    @JsonProperty(value = "userPolicyId")
    private Integer userPolicyId;

    @JsonProperty(value = "roleBrandMappings")
    private List<IdCodeName> mappings;

}
