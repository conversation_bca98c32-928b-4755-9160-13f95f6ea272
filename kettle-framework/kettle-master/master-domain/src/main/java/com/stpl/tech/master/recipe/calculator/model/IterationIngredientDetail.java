//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.calculator.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientProductDetail", propOrder = { "_id", "productId", "productName", "uom", "quantity",
		"yieldPercentage", "quantityPerSubUom", "yieldQuantity", "yieldReason" })
@Document
public class IterationIngredientDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2724125928939936517L;

	@Id
	private String _id;
	/*
	 * @Version
	 * 
	 * @JsonIgnore private Long version;
	 * 
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */
	@Field
	protected Integer productId;
	@Field
	protected String productName;
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	@Field
	protected SubUnitOfMeasure uom;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal quantity;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal oldQuantity;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal yieldPercentage = new BigDecimal("100.00");
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal oldYieldPercentage;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal quantityPerSubUom;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal oldQuantityPerSubUom;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal yieldQuantity;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal oldYieldQuantity;
	protected String yieldReason;
	protected List<IterationIngredientInstructions> instructions;
	protected Boolean autoProduction;
	protected Boolean showRecipe;
	
	@Field
	protected int type;
	@Field
	protected int subType;


	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getSubType() {
		return subType;
	}

	public void setSubType(int subType) {
		this.subType = subType;
	}

	public String get_id() {
		return _id;
	}

	public void set_id(String objectId) {
		this._id = objectId;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public SubUnitOfMeasure getUom() {
		return uom;
	}

	public void setUom(SubUnitOfMeasure uom) {
		this.uom = uom;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getOldQuantity() {
		return oldQuantity;
	}

	public void setOldQuantity(BigDecimal oldQuantity) {
		this.oldQuantity = oldQuantity;
	}

	public BigDecimal getYieldPercentage() {
		return yieldPercentage;
	}

	public void setYieldPercentage(BigDecimal yieldPercentage) {
		this.yieldPercentage = yieldPercentage;
	}
	
	public BigDecimal getOldYieldPercentage() {
		return oldYieldPercentage;
	}

	public void setOldYieldPercentage(BigDecimal oldYieldPercentage) {
		this.oldYieldPercentage = oldYieldPercentage;
	}

	public BigDecimal getQuantityPerSubUom() {
		return quantityPerSubUom;
	}

	public void setQuantityPerSubUom(BigDecimal quantityPerSubUom) {
		this.quantityPerSubUom = quantityPerSubUom;
	}

	public BigDecimal getOldQuantityPerSubUom() {
		return oldQuantityPerSubUom;
	}

	public void setOldQuantityPerSubUom(BigDecimal oldQuantityPerSubUom) {
		this.oldQuantityPerSubUom = oldQuantityPerSubUom;
	}

	public BigDecimal getYieldQuantity() {
		return yieldQuantity;
	}

	public void setYieldQuantity(BigDecimal yieldQuantity) {
		this.yieldQuantity = yieldQuantity;
	}

	public BigDecimal getOldYieldQuantity() {
		return oldYieldQuantity;
	}

	public void setOldYieldQuantity(BigDecimal oldYieldQuantity) {
		this.oldYieldQuantity = oldYieldQuantity;
	}

	public String getYieldReason() {
		return yieldReason;
	}

	public void setYieldReason(String yieldReason) {
		this.yieldReason = yieldReason;
	}

	public List<IterationIngredientInstructions> getInstructions() {
		return instructions;
	}

	public void setInstructions(List<IterationIngredientInstructions> instructions) {
		this.instructions = instructions;
	}

	public Boolean getAutoProduction() {
		return autoProduction;
	}

	public void setAutoProduction(Boolean autoProduction) {
		this.autoProduction = autoProduction;
	}

	public Boolean getShowRecipe() {
		return showRecipe;
	}

	public void setShowRecipe(Boolean showRecipe) {
		this.showRecipe = showRecipe;
	}
}