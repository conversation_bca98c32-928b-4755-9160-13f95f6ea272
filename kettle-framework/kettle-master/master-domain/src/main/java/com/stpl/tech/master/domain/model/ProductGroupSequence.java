package com.stpl.tech.master.domain.model;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

public class ProductGroupSequence {

    private Integer id;
    private Integer groupId;
    private String groupName;
    private String groupTag;
    private String groupType;
    private MenuType menuType;
    private String groupDescription;
    private List<ProductSequence> productSequenceList;
    private IdName createdBy;
    private IdName updatedBy;
    private Date creationTime;
    private Date lastUpdateTime;
    private List<ProductGroupSequence> subGroups;
    private Integer groupIndex;
    private String listingType;
    private String categoryTag;
    private String menuCategoryImage;
    private String menuCategoryDetails;
    private List<MenuSequenceTiming> timings;

    public List<MenuSequenceTiming> getTimings() {
        return timings;
    }
    public void setTimings(List<MenuSequenceTiming> timings) {
        this.timings = timings;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupTag() {
        return groupTag;
    }

    public void setGroupTag(String groupTag) {
        this.groupTag = groupTag;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public String getGroupDescription() {
        return groupDescription;
    }

    public void setGroupDescription(String groupDescription) {
        this.groupDescription = groupDescription;
    }

    public List<ProductSequence> getProductSequenceList() {
        return productSequenceList;
    }

    public void setProductSequenceList(List<ProductSequence> productSequenceList) {
        this.productSequenceList = productSequenceList;
    }

    public IdName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdName createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public List<ProductGroupSequence> getSubGroups() {
        return subGroups;
    }

    public void setSubGroups(List<ProductGroupSequence> subGroups) {
        this.subGroups = subGroups;
    }

    public Integer getGroupIndex() {
        return groupIndex;
    }

    public void setGroupIndex(Integer groupIndex) {
        this.groupIndex = groupIndex;
    }

    public IdName getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(IdName updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public MenuType getMenuType() {
        return menuType;
    }

    public void setMenuType(MenuType menuType) {
        this.menuType = menuType;
    }

    public String getListingType() {
		return listingType;
	}

	public void setListingType(String listingType) {
		this.listingType = listingType;
	}

    public String getCategoryTag() {
        return categoryTag;
    }

    public void setCategoryTag(String categoryTag) {
        this.categoryTag = categoryTag;
    }

    public String getMenuCategoryImage() {
        return menuCategoryImage;
    }

    public void setMenuCategoryImage(String menuCategoryImage) {
        this.menuCategoryImage = menuCategoryImage;
    }

    public String getMenuCategoryDetails() {
        return menuCategoryDetails;
    }

    public void setMenuCategoryDetails(String menuCategoryDetails) {
        this.menuCategoryDetails = menuCategoryDetails;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ProductGroupSequence that = (ProductGroupSequence) o;

        return new EqualsBuilder()
                .append(id, that.id)
                .append(groupId, that.groupId)
                .append(groupName, that.groupName)
                .append(groupTag, that.groupTag)
                .append(groupType, that.groupType)
                .append(menuType, that.menuType)
                .append(groupDescription, that.groupDescription)
                .append(productSequenceList, that.productSequenceList)
                .append(createdBy, that.createdBy)
                .append(updatedBy, that.updatedBy)
                .append(creationTime, that.creationTime)
                .append(lastUpdateTime, that.lastUpdateTime)
                .append(subGroups, that.subGroups)
                .append(groupIndex, that.groupIndex)
                .append(listingType, that.listingType)
                .append(categoryTag, that.categoryTag)
                .append(menuCategoryImage, that.menuCategoryImage)
                .append(menuCategoryDetails, that.menuCategoryDetails)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(id)
                .append(groupId)
                .append(groupName)
                .append(groupTag)
                .append(groupType)
                .append(menuType)
                .append(groupDescription)
                .append(productSequenceList)
                .append(createdBy)
                .append(updatedBy)
                .append(creationTime)
                .append(lastUpdateTime)
                .append(subGroups)
                .append(groupIndex)
                .append(listingType)
                .append(categoryTag)
                .append(menuCategoryImage)
                .append(menuCategoryDetails)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "ProductGroupSequence{" +
                "id=" + id +
                ", groupId=" + groupId +
                ", groupName='" + groupName + '\'' +
                ", groupTag='" + groupTag + '\'' +
                ", groupType='" + groupType + '\'' +
                ", menuType=" + menuType +
                ", groupDescription='" + groupDescription + '\'' +
                ", productSequenceList=" + productSequenceList +
                ", createdBy=" + createdBy +
                ", updatedBy=" + updatedBy +
                ", creationTime=" + creationTime +
                ", lastUpdateTime=" + lastUpdateTime +
                ", subGroups=" + subGroups +
                ", groupIndex=" + groupIndex +
                ", listingType='" + listingType + '\'' +
                ", categoryTag='" + categoryTag + '\'' +
                ", menuCategoryImage='" + menuCategoryImage + '\'' +
                ", menuCategoryDetails='" + menuCategoryDetails + '\'' +
                '}';
    }

    public static class SortByIndex implements Comparator<ProductGroupSequence>
    {
        public int compare(ProductGroupSequence a, ProductGroupSequence b)
        {
            return a.getGroupIndex() - b.getGroupIndex();
        }
    }
}
