/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.04 at 12:22:00 PM IST 
//

package com.stpl.tech.master.domain.model;

import com.stpl.tech.kettle.report.metadata.model.comparator.HasId;
import com.stpl.tech.kettle.report.metadata.model.comparator.HasStatus;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "brandId",
        "brandName",
        "brandCode",
        "tagLine",
        "domain",
        "billTag",
        "websiteLink",
        "status",
        "supportContact",
        "supportEmail",
        "verbiage"})
@XmlRootElement(name = "BrandBasic")
public class BrandBasic implements Serializable, Comparable<BrandBasic>, HasId, HasStatus {

    private static final long serialVersionUID = -93045179307038127L;

    private Integer brandId;
    private String brandName;
    private String brandCode;
    private String tagLine;
    private String domain;
    private String billTag;
    private String websiteLink;
    private String status;
    private String supportContact;
    private String supportEmail;
    private String verbiage;

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public String getTagLine() {
        return tagLine;
    }

    public void setTagLine(String tagLine) {
        this.tagLine = tagLine;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getBillTag() {
        return billTag;
    }

    public void setBillTag(String billTag) {
        this.billTag = billTag;
    }

    public String getWebsiteLink() {
        return websiteLink;
    }

    public void setWebsiteLink(String websiteLink) {
        this.websiteLink = websiteLink;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSupportContact() {
        return supportContact;
    }

    public void setSupportContact(String supportContact) {
        this.supportContact = supportContact;
    }

    public String getSupportEmail() {
        return supportEmail;
    }

    public void setSupportEmail(String supportEmail) {
        this.supportEmail = supportEmail;
    }

    public String getVerbiage() {
        return verbiage;
    }

    public void setVerbiage(String verbiage) {
        this.verbiage = verbiage;
    }

    @Override
    public Object objectId() {
        return brandId;
    }

    @Override
    public String currentStatus() {
        return status;
    }

    @Override
    public int compareTo(BrandBasic o) {
        return o.brandId.compareTo(brandId);
    }
}
