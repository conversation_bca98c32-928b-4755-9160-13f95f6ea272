/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.21 at 05:42:08 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for UnitCategory.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="UnitCategory"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="CAFE"/&gt;
 *     &lt;enumeration value="DELIVERY"/&gt;
 *     &lt;enumeration value="TAKE_AWAY"/&gt;
 *     &lt;enumeration value="COD"/&gt;
 *     &lt;enumeration value="KITCHEN"/&gt;
 *     &lt;enumeration value="WAREHOUSE"/&gt;
 *     &lt;enumeration value="OFFICE"/&gt;
 *     &lt;enumeration value="CHAI_MONK"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "UnitCategory")
@XmlEnum
public enum UnitCategory {

    CAFE("CAFE"),
    DELIVERY("DELIVERY"),
    @XmlEnumValue("TAKE_AWAY")
    TAKE_AWAY("TAKE_AWAY"),
    COD("COD"),
    KITCHEN("KITCHEN"),
    WAREHOUSE("WAREHOUSE"),
    OFFICE("OFFICE"),
    @XmlEnumValue("CHAI_MONK")
    CHAI_MONK("CHAI_MONK"),
    @XmlEnumValue("EMPLOYEE_MEAL")
    EMPLOYEE_MEAL("EMPLOYEE_MEAL");
    private final String value;

    UnitCategory(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static UnitCategory fromValue(String v) {
        for (UnitCategory c: UnitCategory.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
