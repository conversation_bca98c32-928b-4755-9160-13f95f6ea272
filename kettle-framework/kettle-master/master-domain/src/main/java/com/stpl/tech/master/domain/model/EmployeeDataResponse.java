package com.stpl.tech.master.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

public class EmployeeDataResponse implements Serializable {

    private static final long serialVersionUID = -7228997733293828715L;

    @JsonProperty("result")
    private String result;

    @JsonProperty("data")
    private List<EmployeeData> data;

    public List<EmployeeData> getData() {
        return data;
    }
    public void setData(List<EmployeeData> data) {
        this.data = data;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

}
