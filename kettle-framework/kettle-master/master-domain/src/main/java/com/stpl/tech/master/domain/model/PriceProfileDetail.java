package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class PriceProfileDetail implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 3078071560848072115L;
	private Integer priceProfileId;
    private String profileDescription;
    private PriceProfileStrategy profileType;
    private Date profileCreationTime;
    private Date lastUpdateTime;
    private String lastUpdatedBy;
    private String profileStatus;
    private BigDecimal thresholdPercentage;
    private List<PriceProfileRangeValueDetail> profileRangeValueDetails;

    public Integer getPriceProfileId() {
        return priceProfileId;
    }

    public void setPriceProfileId(Integer priceProfileId) {
        this.priceProfileId = priceProfileId;
    }

    public String getProfileDescription() {
        return profileDescription;
    }

    public void setProfileDescription(String profileDescription) {
        this.profileDescription = profileDescription;
    }

    public PriceProfileStrategy getProfileType() {
        return profileType;
    }

    public void setProfileType(PriceProfileStrategy profileType) {
        this.profileType = profileType;
    }

    public Date getProfileCreationTime() {
        return profileCreationTime;
    }

    public void setProfileCreationTime(Date profileCreationTime) {
        this.profileCreationTime = profileCreationTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public String getProfileStatus() {
        return profileStatus;
    }

    public void setProfileStatus(String profileStatus) {
        this.profileStatus = profileStatus;
    }

    public List<PriceProfileRangeValueDetail> getProfileRangeValueDetails() {
        return profileRangeValueDetails;
    }

    public void setProfileRangeValueDetails(List<PriceProfileRangeValueDetail> profileRangeValueDetails) {
        this.profileRangeValueDetails = profileRangeValueDetails;
    }

    public BigDecimal getThresholdPercentage() {
        return thresholdPercentage;
    }

    public void setThresholdPercentage(BigDecimal thresholdPercentage) {
        this.thresholdPercentage = thresholdPercentage;
    }
}
