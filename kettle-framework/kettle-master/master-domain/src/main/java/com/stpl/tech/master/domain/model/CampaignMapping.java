package com.stpl.tech.master.domain.model;

import java.io.Serializable;

public class CampaignMapping implements Serializable {

	private static final long serialVersionUID = 4117932758277012007L;
	private Integer campaignCouponMappingId;
	private int campaignId;
	private String code;
	private int validityInDays;
	private int journey;
	private String desc;
	private Integer reminderDays;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public int getValidityInDays() {
		return validityInDays;
	}

	public void setValidityInDays(int validityInDays) {
		this.validityInDays = validityInDays;
	}

	public int getJourney() {
		return journey;
	}

	public void setJourney(int journey) {
		this.journey = journey;
	}

	public int getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(int campaignId) {
		this.campaignId = campaignId;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public Integer getCampaignCouponMappingId() {
		return campaignCouponMappingId;
	}

	public void setCampaignCouponMappingId(Integer campaignCouponMappingId) {
		this.campaignCouponMappingId = campaignCouponMappingId;
	}

	public Integer getReminderDays() {
		return reminderDays;
	}

	public void setReminderDays(Integer reminderDays) {
		this.reminderDays = reminderDays;
	}

	@Override
	public String toString() {
		return "CampaignMapping{" +
				"campaignCouponMappingId=" + campaignCouponMappingId +
				", campaignId=" + campaignId +
				", code='" + code + '\'' +
				", validityInDays=" + validityInDays +
				", journey=" + journey +
				", desc='" + desc + '\'' +
				", reminderDays=" + reminderDays +
				'}';
	}
}
