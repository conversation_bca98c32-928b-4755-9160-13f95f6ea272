package com.stpl.tech.master.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.context.annotation.Bean;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EmailInfoVO {

    private Integer orderId;

    private Integer customerId;

    private String orderType;

    private boolean resend;

    private String email;
}
