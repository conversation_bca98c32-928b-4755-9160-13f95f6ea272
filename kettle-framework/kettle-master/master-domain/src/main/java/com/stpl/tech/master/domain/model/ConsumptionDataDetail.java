package com.stpl.tech.master.domain.model;

import java.math.BigDecimal;

public class ConsumptionDataDetail {
    private BigDecimal price;
    private BigDecimal taxPercentage;

    public ConsumptionDataDetail(BigDecimal price, BigDecimal taxPercentage) {
        this.price = price;
        this.taxPercentage = taxPercentage;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTaxPercentage() {
        return taxPercentage;
    }

    public void setTaxPercentage(BigDecimal taxPercentage) {
        this.taxPercentage = taxPercentage;
    }
}
