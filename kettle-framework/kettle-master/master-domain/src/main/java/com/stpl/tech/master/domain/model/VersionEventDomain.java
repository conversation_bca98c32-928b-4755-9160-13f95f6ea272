package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class VersionEventDomain {
    private Integer unitId;
    private String applicationName;
    private String applicationVersion;
    private String unitRegion;
    private String updatedBy;
    private Date updatedTime;
    private String status;
}
