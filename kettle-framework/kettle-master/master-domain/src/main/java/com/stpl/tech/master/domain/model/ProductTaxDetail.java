package com.stpl.tech.master.domain.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductTaxDetail", propOrder = { "keyId", "product", "taxType", "country", "state", "startDate",
		"taxRate", "status" })
public class ProductTaxDetail {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer keyId;
	protected IdCodeName product;
	protected IdCodeName taxType;
	protected IdCodeName country;
	protected IdCodeName state;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal taxRate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date startDate;
	@XmlElement(required = true)
	protected String status;

	public Integer getKeyId() {
		return keyId;
	}

	public void setKeyId(Integer keyId) {
		this.keyId = keyId;
	}

	public IdCodeName getProduct() {
		return product;
	}

	public void setProduct(IdCodeName product) {
		this.product = product;
	}

	public IdCodeName getTaxType() {
		return taxType;
	}

	public void setTaxType(IdCodeName taxType) {
		this.taxType = taxType;
	}

	public IdCodeName getCountry() {
		return country;
	}

	public void setCountry(IdCodeName country) {
		this.country = country;
	}

	public IdCodeName getState() {
		return state;
	}

	public void setState(IdCodeName state) {
		this.state = state;
	}

	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}