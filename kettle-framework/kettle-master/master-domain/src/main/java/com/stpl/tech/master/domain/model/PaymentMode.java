
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2015.12.19 at 12:10:26 PM IST
//

package com.stpl.tech.master.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Java class for PaymentMode complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="PaymentMode"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="settlementType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="generatePull" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="denominations" type="{http://www.w3schools.com}DenominationDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentMode", propOrder = { "id", "name", "description", "type", "settlementType", "generatePull",
		"denominations","attributes" })
@Document
public class PaymentMode implements Serializable, Comparable<PaymentMode> {

	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;
	*//**
	 *
	 */
	private static final long serialVersionUID = -8269062611081355533L;
	@Field
	protected int id;
	@XmlElement(required = true)
	@Field
	protected String name;
	@XmlElement(required = true)
	@Field
	protected String description;
	@XmlElement(required = true)
	@Field
	protected String type;
	@XmlElement(required = false)
	@XmlSchemaType(name = "string")
	@Field
	protected PaymentCategory category;
	@XmlElement(required = true)
	@Field
	protected String settlementType;
	@Field
	protected boolean generatePull;
	@Field
	protected boolean autoPullValidate;
	@Field
	protected boolean autoTransfer;
	@Field
	protected boolean autoCloseTransfer;
	@Field
	protected boolean editable;
	@Field
	protected boolean applicableOnDiscountedOrders;
	@Field
	protected String validationSource;
	@Field
	protected boolean needsSettlementSlip;
	@Field
	protected List<DenominationDetail> denominations;


	@Field
	protected String status;

	@Field
	protected String ledgerName;
	@Field
	protected List<Pair<String, String>> attributes;
	private BigDecimal commissionRate;
	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	/**
	 * Gets the value of the id property.
	 *
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the description property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Sets the value of the description property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setDescription(String value) {
		this.description = value;
	}

	/**
	 * Gets the value of the type property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getType() {
		return type;
	}

	/**
	 * Sets the value of the type property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setType(String value) {
		this.type = value;
	}

	/**
	 * Gets the value of the settlementType property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getSettlementType() {
		return settlementType;
	}

	/**
	 * Sets the value of the settlementType property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setSettlementType(String value) {
		this.settlementType = value;
	}

	/**
	 * Gets the value of the generatePull property.
	 *
	 */
	public boolean isGeneratePull() {
		return generatePull;
	}

	/**
	 * Sets the value of the generatePull property.
	 *
	 */
	public void setGeneratePull(boolean value) {
		this.generatePull = value;
	}

	/**
	 * Gets the value of the denominations property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the denominations property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getDenominations().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link DenominationDetail }
	 *
	 *
	 */
	public List<DenominationDetail> getDenominations() {
		if (denominations == null) {
			denominations = new ArrayList<DenominationDetail>();
		}
		return this.denominations;
	}

	public List<Pair<String, String>> getAttributes() {
		if (attributes == null) {
			attributes = new ArrayList<Pair<String, String>>();
		}
		return this.attributes;
	}

	@Override
	public int compareTo(PaymentMode o) {
		return Integer.compare(this.getId(), o.getId());
	}

	public PaymentCategory getCategory() {
		return category;
	}

	public void setCategory(PaymentCategory category) {
		this.category = category;
	}

	public boolean isAutoPullValidate() {
		return autoPullValidate;
	}

	public void setAutoPullValidate(boolean autoPullValidate) {
		this.autoPullValidate = autoPullValidate;
	}

	public boolean isAutoTransfer() {
		return autoTransfer;
	}

	public void setAutoTransfer(boolean autoTransfer) {
		this.autoTransfer = autoTransfer;
	}

	public boolean isAutoCloseTransfer() {
		return autoCloseTransfer;
	}

	public void setAutoCloseTransfer(boolean autoCloseTransfer) {
		this.autoCloseTransfer = autoCloseTransfer;
	}

	public boolean isEditable() {
		return editable;
	}

	public void setEditable(boolean editable) {
		this.editable = editable;
	}

	public boolean isApplicableOnDiscountedOrders() {
		return applicableOnDiscountedOrders;
	}

	public void setApplicableOnDiscountedOrders(boolean applicableOnDiscountedOrders) {
		this.applicableOnDiscountedOrders = applicableOnDiscountedOrders;
	}

	public boolean isNeedsSettlementSlip() {
		return needsSettlementSlip;
	}

	public void setNeedsSettlementSlip(boolean needsSettlementSlip) {
		this.needsSettlementSlip = needsSettlementSlip;
	}

	public String getValidationSource() {
		return validationSource;
	}

	public void setValidationSource(String validationSource) {
		this.validationSource = validationSource;
	}

	public BigDecimal getCommissionRate() {
		return commissionRate;
	}

	public void setCommissionRate(BigDecimal commissionRate) {
		this.commissionRate = commissionRate;
	}
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	public String getLedgerName() {
		return ledgerName;
	}

	public void setLedgerName(String ledgerName) {
		this.ledgerName = ledgerName;
	}


}
