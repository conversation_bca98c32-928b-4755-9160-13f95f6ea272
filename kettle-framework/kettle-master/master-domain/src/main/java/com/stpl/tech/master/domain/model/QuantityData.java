package com.stpl.tech.master.domain.model;

import java.util.Map;

public class QuantityData {

    private Integer quantity;
    private Integer taxableQuantity;
    private Map<Integer, Map<String, Pair<Integer, Integer>>> brandwiseQuanity;


    public QuantityData() {
    }

    public QuantityData(Integer quantity,Integer taxableQuantity,  Map<Integer, Map<String, Pair<Integer, Integer>>> brandwiseQuanity) {
        this.quantity = quantity;
        this.taxableQuantity = taxableQuantity;
        this.brandwiseQuanity = brandwiseQuanity;
    }

    // Quantity BrandId OrderType Quantity
    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Map<Integer, Map<String, Pair<Integer, Integer>>> getBrandwiseQuantity() {
        return brandwiseQuanity;
    }

    public void setBrandwiseQuantity(Map<Integer, Map<String, Pair<Integer, Integer>>> brandwiseQuanity) {
        this.brandwiseQuanity = brandwiseQuanity;
    }

	public Integer getTaxableQuantity() {
		return taxableQuantity;
	}

	public void setTaxableQuantity(Integer taxableQuantity) {
		this.taxableQuantity = taxableQuantity;
	}

    
}
