package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Date;

public class CafeTimingChangeRequest implements Serializable {

    private static final long serialVersionUID = -5053065110066994722L;

    private Integer id;

    private Integer unitId;

    private Integer brandId;

    private String unitName;

    private String dineInOpenTime;

    private String dineInCloseTime;

    private String deliveryOpeningTime;

    private String deliveryClosingTime;

    private String dayOfWeekText;

    private Integer empId;

    private Integer acceptedByEmpId;

    private Date actionTimestamp;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getDineInOpenTime() {
        return dineInOpenTime;
    }

    public void setDineInOpenTime(String dineInOpenTime) {
        this.dineInOpenTime = dineInOpenTime;
    }

    public String getDineInCloseTime() {
        return dineInCloseTime;
    }

    public void setDineInCloseTime(String dineInCloseTime) {
        this.dineInCloseTime = dineInCloseTime;
    }

    public String getDeliveryOpeningTime() {
        return deliveryOpeningTime;
    }

    public void setDeliveryOpeningTime(String deliveryOpeningTime) {
        this.deliveryOpeningTime = deliveryOpeningTime;
    }

    public String getDeliveryClosingTime() {
        return deliveryClosingTime;
    }

    public void setDeliveryClosingTime(String deliveryClosingTime) {
        this.deliveryClosingTime = deliveryClosingTime;
    }

    public String getDayOfWeekText() {
        return dayOfWeekText;
    }

    public void setDayOfWeekText(String dayOfWeekText) {
        this.dayOfWeekText = dayOfWeekText;
    }

    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    public Integer getAcceptedByEmpId() {
        return acceptedByEmpId;
    }

    public void setAcceptedByEmpId(Integer acceptedByEmpId) {
        this.acceptedByEmpId = acceptedByEmpId;
    }

    public Date getActionTimestamp() {
        return actionTimestamp;
    }

    public void setActionTimestamp(Date actionTimestamp) {
        this.actionTimestamp = actionTimestamp;
    }
}
