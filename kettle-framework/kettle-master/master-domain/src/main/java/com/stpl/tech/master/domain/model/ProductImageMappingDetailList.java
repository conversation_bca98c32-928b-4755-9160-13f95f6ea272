/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.domain.model;


import java.util.List;

public class ProductImageMappingDetailList implements java.io.Serializable {

	private static final long serialVersionUID = 2315987124494130719L;

	List<Integer> pids;

	List<ProductImageMappingDetail> list;

	public ProductImageMappingDetailList() {
	}

	public ProductImageMappingDetailList(List<ProductImageMappingDetail> list) {
		this.list = list;
	}

	public List<ProductImageMappingDetail> getList() {
		return list;
	}

	public List<Integer> getPids() {
		return pids;
	}

	public void setPids(List<Integer> pids) {
		this.pids = pids;
	}

	public void setList(List<ProductImageMappingDetail> list) {
		this.list = list;
	}
}
