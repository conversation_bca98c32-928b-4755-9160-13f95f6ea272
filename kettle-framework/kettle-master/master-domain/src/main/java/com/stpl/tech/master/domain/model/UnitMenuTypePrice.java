package com.stpl.tech.master.domain.model;

import java.math.BigDecimal;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Unit Menu Type Price Details")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class UnitMenuTypePrice {
    @ExcelField
    Integer unitId;
    @ExcelField
    String unitName;
    @ExcelField
    Integer productCategory;
    @ExcelField
    Integer productSubCategory;
    @ExcelField
    String productName;
    @ExcelField
    String dimension;
    @ExcelField
    Integer productId;
    @ExcelField
    BigDecimal originalPrice;
    @ExcelField
    BigDecimal defaultvalue;
    @ExcelField
    BigDecimal daySlot;
    @ExcelField
    BigDecimal daySlotBreakfast;
    @ExcelField
    BigDecimal daySlotLunch;
    @ExcelField
    BigDecimal daySlotEvening;
    @ExcelField
    BigDecimal daySlotDinner;
    @ExcelField
    BigDecimal daySlotPostDinner;
    @ExcelField
    BigDecimal daySlotOvernight;
    @ExcelField
    BigDecimal singleServe;

    public UnitMenuTypePrice(Integer unitId, String unitName, Integer productCategory, Integer productSubCategory, String productName, String dimension,
                             Integer productId, BigDecimal originalPrice) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.productCategory = productCategory;
        this.productSubCategory = productSubCategory;
        this.productName = productName;
        this.dimension = dimension;
        this.productId = productId;
        this.originalPrice = originalPrice;
    }

    public UnitMenuTypePrice() {
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
            this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(Integer productCategory) {
        this.productCategory = productCategory;
    }

    public Integer getProductSubCategory() {
        return productSubCategory;
    }

    public void setProductSubCategory(Integer productSubCategory) {
        this.productSubCategory = productSubCategory;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getDaySlot() {
        return daySlot;
    }

    public void setDaySlot(BigDecimal daySlot) {
        this.daySlot = daySlot;
    }

    public BigDecimal getDaySlotBreakfast() {
        return daySlotBreakfast;
    }

    public void setDaySlotBreakfast(BigDecimal daySlotBreakfast) {
        this.daySlotBreakfast = daySlotBreakfast;
    }

    public BigDecimal getDaySlotLunch() {
        return daySlotLunch;
    }

    public void setDaySlotLunch(BigDecimal daySlotLunch) {
        this.daySlotLunch = daySlotLunch;
    }

    public BigDecimal getDaySlotEvening() {
        return daySlotEvening;
    }

    public void setDaySlotEvening(BigDecimal daySlotEvening) {
        this.daySlotEvening = daySlotEvening;
    }

    public BigDecimal getDaySlotDinner() {
        return daySlotDinner;
    }

    public void setDaySlotDinner(BigDecimal daySlotDinner) {
        this.daySlotDinner = daySlotDinner;
    }

    public BigDecimal getDaySlotPostDinner() {
        return daySlotPostDinner;
    }

    public void setDaySlotPostDinner(BigDecimal daySlotPostDinner) {
        this.daySlotPostDinner = daySlotPostDinner;
    }

    public BigDecimal getDaySlotOvernight() {
        return daySlotOvernight;
    }

    public void setDaySlotOvernight(BigDecimal daySlotOvernight) {
        this.daySlotOvernight = daySlotOvernight;
    }

    public BigDecimal getSingleServe() {
        return singleServe;
    }

    public void setSingleServe(BigDecimal singleServe) {
        this.singleServe = singleServe;
    }

    public BigDecimal getDefaultvalue() {
        return defaultvalue;
    }

    public void setDefaultvalue(BigDecimal defaultvalue) {
        this.defaultvalue = defaultvalue;
    }
}
