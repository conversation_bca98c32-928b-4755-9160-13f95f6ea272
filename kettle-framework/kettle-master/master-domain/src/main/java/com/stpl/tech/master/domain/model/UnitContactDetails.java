package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitContactDetails implements Serializable {

    private static final long serialVersionUID = -441674439916804766L;

    private Integer unitId;
    private String unitName;
    private List<IdCodeName> contactDetails;

}
