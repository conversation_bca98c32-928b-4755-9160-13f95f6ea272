package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DayCloseRequestStatus {
    private Integer unitId;
    private Date businessDate;
    private boolean dayCloseStatus;
    private Integer kettleStartOrderId;
    private Integer kettleEndOrderId;
    private boolean dayCloseExclusion = false;
}
