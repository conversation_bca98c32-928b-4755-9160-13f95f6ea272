//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.10.22 at 02:17:13 PM IST 
//


package com.stpl.tech.kettle.report.metadata.model;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ReportNotification complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ReportNotification"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}ReportNotificationType"/&gt;
 *         &lt;element name="notificationIdIndex" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="notificationType" type="{http://www.w3schools.com}UserType"/&gt;
 *         &lt;element name="channel" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="messageIndex" type="{http://www.w3.org/2001/XMLSchema}int" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ReportNotification", propOrder = {
    "type",
    "notificationIdIndex",
    "subjectPrefixId",
    "notificationType",
    "channel",
    "messageIndex"
})
public class ReportNotification {

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected ReportNotificationType type;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer notificationIdIndex;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer subjectPrefixId;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "string")
    protected UserType notificationType;
    @XmlElement(required = true, nillable = true)
    protected String channel;
    @XmlElementWrapper(name = "indexes")
    @XmlElement(type = Integer.class, nillable =true)
    protected List<Integer> messageIndex;

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link ReportNotificationType }
     *     
     */
    public ReportNotificationType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link ReportNotificationType }
     *     
     */
    public void setType(ReportNotificationType value) {
        this.type = value;
    }

    /**
     * Gets the value of the notificationIdIndex property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getNotificationIdIndex() {
        return notificationIdIndex;
    }

    /**
     * Sets the value of the notificationIdIndex property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setNotificationIdIndex(Integer value) {
        this.notificationIdIndex = value;
    }
    
    public Integer getSubjectPrefixId() {
		return subjectPrefixId;
	}

	public void setSubjectPrefixId(Integer subjectPrefixId) {
		this.subjectPrefixId = subjectPrefixId;
	}

	/**
     * Gets the value of the notificationType property.
     * 
     * @return
     *     possible object is
     *     {@link UserType }
     *     
     */
    public UserType getNotificationType() {
        return notificationType;
    }

    /**
     * Sets the value of the notificationType property.
     * 
     * @param value
     *     allowed object is
     *     {@link UserType }
     *     
     */
    public void setNotificationType(UserType value) {
        this.notificationType = value;
    }

    /**
     * Gets the value of the channel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChannel() {
        return channel;
    }

    /**
     * Sets the value of the channel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChannel(String value) {
        this.channel = value;
    }

    /**
     * Gets the value of the messageIndex property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the messageIndex property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMessageIndex().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Integer }
     * 
     * 
     */
    public List<Integer> getMessageIndex() {
        if (messageIndex == null) {
            messageIndex = new ArrayList<Integer>();
        }
        return this.messageIndex;
    }

}
