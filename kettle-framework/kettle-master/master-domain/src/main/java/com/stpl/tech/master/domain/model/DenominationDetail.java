/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.02.21 at 10:38:24 AM IST 
//

package com.stpl.tech.master.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <p>
 * Java class for DenominationDetail complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="DenominationDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="denominationId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="denominationCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="denominationText" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="displayOrder" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="denominationValue" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="bundleSize" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="paymentMode" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DenominationDetail", propOrder = { "denominationId", "denominationCode", "denominationText", "status",
		"displayOrder", "denominationValue", "bundleSize", "paymentMode" })
@Document
public class DenominationDetail implements Serializable, Comparable<DenominationDetail> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2576849608609905519L;
	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/
	@Field
	protected int denominationId;
	@XmlElement(required = true)
	@Field
	protected String denominationCode;
	@XmlElement(required = true)
	@Field
	protected String denominationText;
	@XmlElement(required = true)
	@Field
	protected String status;
	@Field
	protected int displayOrder;
	@Field
	protected int denominationValue;
	@Field
	protected int bundleSize;
	@Field
	protected int paymentMode;

	
	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

/*	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	/**
	 * Gets the value of the denominationId property.
	 * 
	 */
	public int getDenominationId() {
		return denominationId;
	}

	/**
	 * Sets the value of the denominationId property.
	 * 
	 */
	public void setDenominationId(int value) {
		this.denominationId = value;
	}

	/**
	 * Gets the value of the denominationCode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDenominationCode() {
		return denominationCode;
	}

	/**
	 * Sets the value of the denominationCode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDenominationCode(String value) {
		this.denominationCode = value;
	}

	/**
	 * Gets the value of the denominationText property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDenominationText() {
		return denominationText;
	}

	/**
	 * Sets the value of the denominationText property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDenominationText(String value) {
		this.denominationText = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	/**
	 * Gets the value of the displayOrder property.
	 * 
	 */
	public int getDisplayOrder() {
		return displayOrder;
	}

	/**
	 * Sets the value of the displayOrder property.
	 * 
	 */
	public void setDisplayOrder(int value) {
		this.displayOrder = value;
	}

	/**
	 * Gets the value of the denominationValue property.
	 * 
	 */
	public int getDenominationValue() {
		return denominationValue;
	}

	/**
	 * Sets the value of the denominationValue property.
	 * 
	 */
	public void setDenominationValue(int value) {
		this.denominationValue = value;
	}

	/**
	 * Gets the value of the bundleSize property.
	 * 
	 */
	public int getBundleSize() {
		return bundleSize;
	}

	/**
	 * Sets the value of the bundleSize property.
	 * 
	 */
	public void setBundleSize(int value) {
		this.bundleSize = value;
	}

	/**
	 * Gets the value of the paymentMode property.
	 * 
	 */
	public int getPaymentMode() {
		return paymentMode;
	}

	/**
	 * Sets the value of the paymentMode property.
	 * 
	 */
	public void setPaymentMode(int value) {
		this.paymentMode = value;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + denominationId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DenominationDetail other = (DenominationDetail) obj;
		if (denominationId != other.denominationId)
			return false;
		return true;
	}

	@Override
	public int compareTo(DenominationDetail o) {
		return Integer.compare(this.getDenominationId(), o.getDenominationId());
	}
}
