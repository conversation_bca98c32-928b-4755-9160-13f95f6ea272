package com.stpl.tech.master.domain.model;

import java.util.Date;
import java.util.List;


public class MenuRecommendation {

    private Integer menuRecommendationId;
    private String menuRecommendationName;
    private String menuRecommendationDescription;
    private List<MenuRecommendationMapping> recommendationProduct;
    private Integer createdBy;
    private Date creationTime;
    private Date lastUpdateTime;
    private String recommendationType;
    private Integer cloneId;
    private String Status;


    public Integer getMenuRecommendationId() {
        return menuRecommendationId;
    }

    public void setMenuRecommendationId(Integer menuSequenceId) {
        this.menuRecommendationId = menuSequenceId;
    }


    public String getMenuRecommendationName() {
        return menuRecommendationName;
    }

    public void setMenuRecommendationName(String menuSequenceName) {
        this.menuRecommendationName = menuSequenceName;
    }

    public String getMenuRecommendationDescription() {
        return menuRecommendationDescription;
    }

    public void setMenuRecommendationDescription(String menuRecommendationDescription) {
        this.menuRecommendationDescription = menuRecommendationDescription;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public String getRecommendationType() {
        return recommendationType;
    }

    public void setRecommendationType(String menuType) {
        this.recommendationType = menuType;
    }

    public Integer getCloneId() {
        return cloneId;
    }

    public void setCloneId(Integer cloneId) {
        this.cloneId = cloneId;
    }

    public List<MenuRecommendationMapping> getRecommendationProduct() {
        return recommendationProduct;
    }

    public void setRecommendationProduct(List<MenuRecommendationMapping> recommendationProduct) {
        this.recommendationProduct = recommendationProduct;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String status) {
        Status = status;
    }
}
