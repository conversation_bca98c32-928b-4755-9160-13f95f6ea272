package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ConsumptionCodeData implements Serializable {

    private static final long serialVersionUID = 2211069212658175335L;

    private String color;
    private String name;
    private Integer id;
    private BigDecimal qty;
    private String dimension;

}