//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.20 at 07:12:44 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


@XmlType(name = "FileTypeDTO")
@XmlEnum
public enum FileTypeDTO {

    OTHERS;

    public String value() {
        return name();
    }

    public static FileTypeDTO fromValue(String v) {
        return valueOf(v);
    }

}
