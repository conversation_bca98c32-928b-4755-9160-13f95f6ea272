//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.20 at 07:12:44 PM IST 
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "ImageCategoryType")
@XmlEnum
public enum ImageCategoryType {

	TRENDING_LOW("_trending_low", "600px", ImageCategoryUploadType.UPLOAD, false, 1),
	TRENDING_HIGH("_trending_high", "1200px", ImageCategoryUploadType.UPLOAD, false, 1),
	SPECIAL_LOW("_special_low", "600px", ImageCategoryUploadType.UPLOAD, false, 1),
	SPECIAL_HIGH("_special_high", "1200px", ImageCategoryUploadType.UPLOAD, false, 1),
	GRID_MENU_LOW("_grid_menu_low", "600px", ImageCategoryUploadType.UPLOAD, true, 10, ImageType.JPEG),
	GRID_MENU_LOW_WEBP("_grid_menu_low_webp", "600px", ImageCategoryUploadType.UPLOAD, true, 10, ImageType.WEBP),
	GRID_MENU_HIGH("_grid_menu_high", "1200px", ImageCategoryUploadType.UPLOAD, true, 1),
	LIST_MENU_HIGH("_list_menu_high", "280px", ImageCategoryUploadType.UPLOAD, true, 1),
	LIST_MENU_LOW("_list_menu_low", "70px", ImageCategoryUploadType.UPLOAD, false, 1),
	COMBO_GRID_HIGH("_combo_grid_high", "1200px", ImageCategoryUploadType.UPLOAD, false, 1),
	COMBO_GRID_LOW("_combo_grid_low", "600px", ImageCategoryUploadType.UPLOAD, false, 1),
	SHOWCASE_VIDEO("video", "600px", ImageCategoryUploadType.LINK, true, 1),
	MARKETING_IMAGE_WEB_VIEW("_marketing_image_webp","1200px",ImageCategoryUploadType.UPLOAD,true,10,ImageType.JPEG),
	MARKETING_IMAGE_MOB_VIEW("_marketing_image_mob_view","1200px",ImageCategoryUploadType.UPLOAD,true,10,ImageType.JPEG),
	GRID_MENU_100X100("_grid_menu_100x100","100px",ImageCategoryUploadType.UPLOAD,true,10,ImageType.WEBP),
	GRID_MENU_400X400("_grid_menu_400x400","400px",ImageCategoryUploadType.UPLOAD,true,10,ImageType.WEBP),
	RECOMMENDATION_IMAGE_1200X1200("_recommendation_image_1200x1200","1200px",ImageCategoryUploadType.UPLOAD,true,10,ImageType.WEBP);

	//	TODO FOR WEB AND MOBILE VIEW ->MARKETING IMAGE


	private final String value;
	private final String size;
	private final ImageCategoryUploadType type;
	private final Boolean status;
	private final Integer limit;
	private ImageType imageType;

	ImageCategoryType(String s, String size, ImageCategoryUploadType type, Boolean status, Integer limit) {
		value = s;
		this.size = size;
		this.type = type;
		this.status = status;
		this.limit = limit;
	}

	ImageCategoryType(String s, String size, ImageCategoryUploadType type, Boolean status, Integer limit, ImageType imageType) {
		value = s;
		this.size = size;
		this.type = type;
		this.status = status;
		this.limit = limit;
		this.imageType = imageType;
	}

	public String value() {
		return value;
	}

	public String size() {
		return size;
	}

	public ImageCategoryUploadType type() {
		return type;
	}

	public ImageCategoryUploadType getType() {
		return type;
	}

	public Boolean getStatus() {
		return status;
	}

	public Integer getLimit() {
		return limit;
	}

	@Override
	public String toString() {
		return "ImageCategoryType{" +
			"value='" + value + '\'' +
			", size='" + size + '\'' +
			", type=" + type +
			", status=" + status +
			", limit=" + limit +
			'}';
	}
}
