package com.stpl.tech.master.domain.model;

import java.util.Date;

public class UnitRestrictedApplicationDetail {
	private int id;
	private IdCodeName unit;
	private ApplicationName applicationName;
	private IdCodeName lastUpdatedBy;
	private Date lastUpdatedOn;
	private ApplicationInstallationStatus status;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public IdCodeName getUnit() {
		return unit;
	}

	public void setUnit(IdCodeName unit) {
		this.unit = unit;
	}

	public ApplicationName getApplicationName() {
		return applicationName;
	}

	public void setApplicationName(ApplicationName applicationName) {
		this.applicationName = applicationName;
	}

	public IdCodeName getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(IdCodeName lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	public Date getLastUpdatedOn() {
		return lastUpdatedOn;
	}

	public void setLastUpdatedOn(Date lastUpdatedOn) {
		this.lastUpdatedOn = lastUpdatedOn;
	}

	public ApplicationInstallationStatus getStatus() {
		return status;
	}

	public void setStatus(ApplicationInstallationStatus status) {
		this.status = status;
	}

}
