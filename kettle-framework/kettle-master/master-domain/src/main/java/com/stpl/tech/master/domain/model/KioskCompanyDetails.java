//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.23 at 01:37:36 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for KioskCompanyDetails complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="KioskCompanyDetails"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="companyId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="companyName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="companyDomains" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded"/&gt;
 *         &lt;element name="contactDetails" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="companyEmail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="companyStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="paymentMode" type="{http://www.w3schools.com}KioskPaymentMode"/&gt;
 *         &lt;element name="kioskSubDomain" type="{http://www.w3schools.com}string"/&gt;
 *         &lt;element name="officeList" type="{http://www.w3schools.com}KioskOfficeDetails" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "KioskCompanyDetails", propOrder = {
        "companyId",
        "companyName",
        "companyDomains",
        "contactDetails",
        "country",
        "companyEmail",
        "companyStatus",
        "paymentMode",
        "kioskSubDomain",
        "officeList"
})
public class KioskCompanyDetails implements Serializable {

    private static final long serialVersionUID = 3528863602200041895L;

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer companyId;
    @XmlElement(required = true)
    protected String companyName;
    @XmlElement(required = true)
    protected List<IdCodeName> companyDomains;
    @XmlElement(required = true)
    protected IdCodeName contactDetails;
    @XmlElement(required = true)
    protected String country;
    @XmlElement(required = true)
    protected String companyEmail;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus companyStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected KioskPaymentMode paymentMode;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    private String kioskSubDomain;
    @XmlElement(required = true)
    protected List<KioskOfficeDetails> officeList;


    /**
     * Gets the value of the companyId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * Sets the value of the companyId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCompanyId(Integer value) {
        this.companyId = value;
    }

    /**
     * Gets the value of the companyName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     * Sets the value of the companyName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCompanyName(String value) {
        this.companyName = value;
    }

    /**
     * Gets the value of the companyDomains property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the companyDomains property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getCompanyDomains().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<IdCodeName> getCompanyDomains() {
        if (companyDomains == null) {
            companyDomains = new ArrayList<IdCodeName>();
        }
        return this.companyDomains;
    }

    /**
     * Gets the value of the contactDetails property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getContactDetails() {
        return contactDetails;
    }

    /**
     * Sets the value of the contactDetails property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setContactDetails(IdCodeName value) {
        this.contactDetails = value;
    }

    /**
     * Gets the value of the country property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCountry() {
        return country;
    }

    /**
     * Sets the value of the country property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCountry(String value) {
        this.country = value;
    }

    /**
     * Gets the value of the companyEmail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCompanyEmail() {
        return companyEmail;
    }

    /**
     * Sets the value of the companyEmail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCompanyEmail(String value) {
        this.companyEmail = value;
    }

    /**
     * Gets the value of the companyStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getCompanyStatus() {
        return companyStatus;
    }

    /**
     * Sets the value of the companyStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setCompanyStatus(SwitchStatus value) {
        this.companyStatus = value;
    }

    /**
     * Gets the value of the paymentMode property.
     * 
     * @return
     *     possible object is
     *     {@link KioskPaymentMode }
     *     
     */
    public KioskPaymentMode getPaymentMode() {
        return paymentMode;
    }

    /**
     * Sets the value of the paymentMode property.
     * 
     * @param value
     *     allowed object is
     *     {@link KioskPaymentMode }
     *     
     */
    public void setPaymentMode(KioskPaymentMode value) {
        this.paymentMode = value;
    }

    /**
     * Gets the value of the officeList property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the officeList property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOfficeList().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link KioskOfficeDetails }
     * 
     * 
     */
    public List<KioskOfficeDetails> getOfficeList() {
        if (officeList == null) {
            officeList = new ArrayList<>();
        }
        return this.officeList;
    }

    /**
     * Sets the value of the kioskSubDomain property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setKioskSubDomain(String value) {
        this.kioskSubDomain = value;
    }

    /**
     * Gets the value of the kioskSubDomain property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getKioskSubDomain() {
        return kioskSubDomain;
    }
}
