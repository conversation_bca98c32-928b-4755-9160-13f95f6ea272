//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.08.14 at 03:44:51 PM IST 
//


package com.stpl.tech.master.domain.model;

import java.util.Date;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for ManualBillBook complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ManualBillBook"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generatedForUnitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="startNo" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="transferOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="endNo" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="creationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="activationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ManualBillBook", propOrder = {
    "id",
    "generatedForUnitId",
    "startNo",
    "transferOrderId",
    "usedBillCount",
    "endNo",
    "creationTime",
    "activationTime",
    "status"
})
public class ManualBillBook {

    protected int id;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName generatedForUnitId;
    protected int startNo;
    protected int transferOrderId;
    protected int usedBillCount;
    protected int endNo;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date creationTime;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date activationTime;
    @XmlElement(required = true)
    protected String status;

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the generatedForUnitId property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getGeneratedForUnitId() {
        return generatedForUnitId;
    }

    /**
     * Sets the value of the generatedForUnitId property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setGeneratedForUnitId(IdCodeName value) {
        this.generatedForUnitId = value;
    }

    /**
     * Gets the value of the startNo property.
     * 
     */
    public int getStartNo() {
        return startNo;
    }

    /**
     * Sets the value of the startNo property.
     * 
     */
    public void setStartNo(int value) {
        this.startNo = value;
    }

    /**
     * Gets the value of the transferOrderId property.
     * 
     */
    public int getTransferOrderId() {
        return transferOrderId;
    }

    /**
     * Sets the value of the transferOrderId property.
     * 
     */
    public void setTransferOrderId(int value) {
        this.transferOrderId = value;
    }
    
  
	public int getUsedBillCount() {
		return usedBillCount;
	}

	public void setUsedBillCount(int usedBillCount) {
		this.usedBillCount = usedBillCount;
	}

	/**
     * Gets the value of the endNo property.
     * 
     */
    public int getEndNo() {
        return endNo;
    }

    /**
     * Sets the value of the endNo property.
     * 
     */
    public void setEndNo(int value) {
        this.endNo = value;
    }

    /**
     * Gets the value of the creationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getCreationTime() {
        return creationTime;
    }

    /**
     * Sets the value of the creationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreationTime(Date value) {
        this.creationTime = value;
    }

    /**
     * Gets the value of the activationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getActivationTime() {
        return activationTime;
    }

    /**
     * Sets the value of the activationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActivationTime(Date value) {
        this.activationTime = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

}
