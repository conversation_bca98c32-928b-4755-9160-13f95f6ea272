package com.stpl.tech.master.domain.model;

import java.math.BigDecimal;

public class UnitProductPricingDetail {

    private Integer unitProdPriceId;
    private BigDecimal price;

    public UnitProductPricingDetail() {
    }

    public UnitProductPricingDetail(Integer unitProdPriceId, BigDecimal price) {
        this.unitProdPriceId = unitProdPriceId;
        this.price = price;
    }

    public Integer getUnitProdPriceId() {
        return unitProdPriceId;
    }

    public void setUnitProdPriceId(Integer unitProdPriceId) {
        this.unitProdPriceId = unitProdPriceId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public String toString() {
        return "UnitProductPricingDetail{" +
                "unitProdPriceId=" + unitProdPriceId +
                ", price=" + price +
                '}';
    }
}
