package com.stpl.tech.master.domain.model;

import java.util.Date;

public class ApplicationInstallationDetail {

	private int id;
	private ApplicationName applicationName;
	private String screenType;
	private IdCodeName unit;
	private Integer terminal;
	private String machineId;
	private String machineDetails;
	private IdCodeName createdBy;
	private Date createdOn;
	private IdCodeName lastUpdatedBy;
	private Date lastUpdatedOn;
	private ApplicationInstallationStatus status;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public ApplicationName getApplicationName() {
		return applicationName;
	}

	public void setApplicationName(ApplicationName applicationName) {
		this.applicationName = applicationName;
	}

	public String getScreenType() {
		return screenType;
	}

	public void setScreenType(String screenType) {
		this.screenType = screenType;
	}

	public IdCodeName getUnit() {
		return unit;
	}

	public void setUnit(IdCodeName unit) {
		this.unit = unit;
	}

	public Integer getTerminal() {
		return terminal;
	}

	public void setTerminal(Integer terminal) {
		this.terminal = terminal;
	}

	public String getMachineId() {
		return machineId;
	}

	public void setMachineId(String machineId) {
		this.machineId = machineId;
	}

	public String getMachineDetails() {
		return machineDetails;
	}

	public void setMachineDetails(String machineDetails) {
		this.machineDetails = machineDetails;
	}

	public IdCodeName getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(IdCodeName createdBy) {
		this.createdBy = createdBy;
	}

	public Date getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(Date createdOn) {
		this.createdOn = createdOn;
	}

	public IdCodeName getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(IdCodeName lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	public Date getLastUpdatedOn() {
		return lastUpdatedOn;
	}

	public void setLastUpdatedOn(Date lastUpdatedOn) {
		this.lastUpdatedOn = lastUpdatedOn;
	}

	public ApplicationInstallationStatus getStatus() {
		return status;
	}

	public void setStatus(ApplicationInstallationStatus status) {
		this.status = status;
	}

}
