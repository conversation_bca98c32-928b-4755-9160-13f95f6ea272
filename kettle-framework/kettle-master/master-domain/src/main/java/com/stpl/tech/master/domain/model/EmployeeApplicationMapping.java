package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class EmployeeApplicationMapping implements Serializable {

    private static final long serialVersionUID = -8765362801294545689L;
    private Integer mappingId;
    private Integer employeeId;
    private String mappingType;
    private List<String> mappingValue;
    private String recordStatus;

    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {this.employeeId = employeeId;}

    public String getMappingType() {
        return mappingType;
    }

    public void setMappingType(String mappingType) {
        this.mappingType = mappingType;
    }

    public String getRecordStatus() {
        return recordStatus;
    }

    public List<String> getMappingValue() {return mappingValue;}

    public void setMappingValue(List<String> mappingValue) {this.mappingValue = mappingValue;}

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    @Override
    public String toString() {
        return "EmployeeApplicationMapping{" +
                "mappingId=" + mappingId +
                ", employeeId=" + employeeId +
                ", mappingType='" + mappingType + '\'' +
                ", mappingValue=" + mappingValue +
                ", recordStatus='" + recordStatus + '\'' +
                '}';
    }
}
