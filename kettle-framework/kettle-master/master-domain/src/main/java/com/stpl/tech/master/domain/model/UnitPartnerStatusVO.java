package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UnitPartnerStatusVO {

    private List<Integer> unitIds = new ArrayList<>();
    private List<Integer> partnerIds = new ArrayList<>();
    private Integer brandId;
    private Date startDate;
    private Date endDate;
    private Boolean status;
}
