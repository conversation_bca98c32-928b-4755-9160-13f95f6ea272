package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitVersionDomain {

    private Integer unitId;
    private List<String> applicationName;
    private Integer terminalId;
    private String status;
    private String posVersion;
    private String cafeAppVersion;
    private String requestSource;
    private String applicationVersion ;
}
