package com.stpl.tech.master.domain.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

import java.util.Date;

@ExcelSheet(value = "Employee Onboarding Details")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class EmployeeOnboarding {

    @ExcelField
    protected String name;
    @ExcelField
    protected String gender;
    @ExcelField
    protected String biometricId;
    @ExcelField
    protected String department;
    @ExcelField
    protected String designation;
    @ExcelField
    protected String addressLine1;
    @ExcelField
    protected String addressLine2;
    @ExcelField
    protected String addressLine3;
    @ExcelField
    protected String city;
    @ExcelField
    protected String state;
    @ExcelField
    protected String country;
    @ExcelField
    protected String zipCode;
    @ExcelField
    protected String contact1;
    @ExcelField
    protected String contact2;
    @ExcelField
    protected String employmentType;
    @ExcelField
    protected String joiningDate;
    @ExcelField
    protected Integer reportingManager;
    @ExcelField
    protected String employeeEmail;
    @ExcelField
    protected String employeeCode;
    @ExcelField
    protected String slackId;
    @ExcelField
    protected String employeeMealEligible;
    @ExcelField
    protected String reasonForTermination;
    @ExcelField
    protected String hrExecutive;
    @ExcelField
    protected String leaveApprovalAuthority;
    @ExcelField
    protected Date dob;
    @ExcelField
    protected Integer locCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getBiometricId() {
        return biometricId;
    }

    public void setBiometricId(String biometricId) {
        this.biometricId = biometricId;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDesignation() {
        return designation;
    }

    public void setDesignation(String designation) {
        this.designation = designation;
    }

    public String getAddressLine1() {
        return addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    public String getAddressLine2() {
        return addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    public String getAddressLine3() {
        return addressLine3;
    }

    public void setAddressLine3(String addressLine3) {
        this.addressLine3 = addressLine3;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getContact1() {
        return contact1;
    }

    public void setContact1(String contact1) {
        this.contact1 = contact1;
    }

    public String getContact2() {
        return contact2;
    }

    public void setContact2(String contact2) {
        this.contact2 = contact2;
    }

    public String getEmploymentType() {
        return employmentType;
    }

    public void setEmploymentType(String employmentType) {
        this.employmentType = employmentType;
    }

    public String getJoiningDate() {
        return joiningDate;
    }

    public void setJoiningDate(String joiningDate) {
        this.joiningDate = joiningDate;
    }

    public Integer getReportingManager() {
        return reportingManager;
    }

    public void setReportingManager(Integer reportingManager) {
        this.reportingManager = reportingManager;
    }

    public String getEmployeeEmail() {
        return employeeEmail;
    }

    public void setEmployeeEmail(String employeeEmail) {
        this.employeeEmail = employeeEmail;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getSlackId() {
        return slackId;
    }

    public void setSlackId(String slackId) {
        this.slackId = slackId;
    }

    public String getEmployeeMealEligible() {
        return employeeMealEligible;
    }

    public void setEmployeeMealEligible(String employeeMealEligible) {
        this.employeeMealEligible = employeeMealEligible;
    }

    public String getReasonForTermination() {
        return reasonForTermination;
    }

    public void setReasonForTermination(String reasonForTermination) {
        this.reasonForTermination = reasonForTermination;
    }

    public String getHrExecutive() {
        return hrExecutive;
    }

    public void setHrExecutive(String hrExecutive) {
        this.hrExecutive = hrExecutive;
    }

    public String getLeaveApprovalAuthority() {
        return leaveApprovalAuthority;
    }

    public void setLeaveApprovalAuthority(String leaveApprovalAuthority) {
        this.leaveApprovalAuthority = leaveApprovalAuthority;
    }

    public Date getDob() {
        return dob;
    }

    public void setDob(Date dob) {
        this.dob = dob;
    }

    public Integer getLocCode() {
        return locCode;
    }

    public void setLocCode(Integer locCode) {
        this.locCode = locCode;
    }
}
