/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 12:10:26 PM IST 
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MappingIdCodeName", propOrder = { "mappingId", "id", "name", "code", "shortCode", "type", "status" })
@Document
public class MappingIdCodeName extends IdCodeName {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7142383159221231193L;
	protected Integer mappingId;

	public MappingIdCodeName() {

	}

	public MappingIdCodeName(int mappingId, int id, String name, String code) {
		super(id, name, code);
		this.mappingId = mappingId;
	}

	public MappingIdCodeName(int mappingId, int id, String name, String code, String shortCode, String type,
			String status) {
		super(id, name, code, shortCode, type, status);
		this.mappingId = mappingId;
	}

	public Integer getMappingId() {
		return mappingId;
	}

	public void setMappingId(Integer mappingId) {
		this.mappingId = mappingId;
	}

	@Override
	public String toString() {
		return "MappingIdCodeName [mappingId=" + mappingId + ", id=" + id + ", name=" + name + ", code=" + code
				+ ", status=" + status + "]";
	}

}
