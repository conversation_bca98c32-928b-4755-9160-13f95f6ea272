package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CustomerWinbackOfferInfoDomain {
    private String contactNumber;
    private String customerName;
    private Integer offerId;
    private String offerCode;
    private String offerDescription;
    private Integer validityInDays;
    private String compensationReason;
    private String complainSource;
    private String comment;
    private String orderId;
    private String orderSource;
    private Integer channelPartnerId;
    private String isNotified;
    private Integer updatedBy;

}
