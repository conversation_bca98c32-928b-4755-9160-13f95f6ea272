/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


package com.stpl.tech.master.domain.model;

public enum OfferType {

    OFFER("OFFER", true), 
    CUSTOMER_OFFER_BANNER("CUSTOMER_OFFER_BANNER", true),
    OFFER_BANNER("OFFER_BANNER", false),
    OFFER_APP_BLOCKER("OFFER_APP_BLOCKER", true),
    ALLIANCE("ALLIANCE", false),
    BANNER("BANNER", false),
    APP_BLOCKER("APP_BLOCKER", false),
    WALLET_BANNER("WALLET_BANNER", false),
    BANNER_ACTION("BANNER_ACTION", false);

    private String offerType;
    private Boolean validateCoupon;


    OfferType(String offerType, Boolean validateCoupon) {
        this.offerType = offerType;
        this.validateCoupon = validateCoupon;
    }

    public Boolean getValidateCoupon() {
        return validateCoupon;
    }

    public String getOfferType() {
        return offerType;
    }


}
