package com.stpl.tech.master.domain.model;

import java.util.Date;
import java.util.List;

public class AppOfferDetail {

    private Integer appOfferId;
    private String offerType;
    private String description;
    private String title;
    private Integer offerIndex;
    private Date startDate;
    private Date endDate;
    private String couponCode;
    private Integer offerId;
    private Integer couponId;
    private String listImage;
    private String listImageUrl;
    private String gridImage;
    private String gridImageUrl;
    private String actionType;
    private String status;
    private List<AppOfferUnitDetail> unitDetailList;
    private String createdBy;
    private String updatedBy;
    private String redirectionLink;
    private String actionCategory;
    private Integer menuCategoryId;
    private Integer partnerId;
    private AppOfferType appOfferType;
    private Integer mov;
    private Integer offerValue;
    private Integer maxDiscount;
    private String offerCategory;
    private String offerOrderType;
    private  List<AppOfferApplicability> appOfferApplicabilityList;
    private Integer brandId;

    private List<String> appofferApplicabilityFlags;
    public AppOfferDetail() {
    }

    public Integer getAppOfferId() {
        return appOfferId;
    }

    public void setAppOfferId(Integer appOfferId) {
        this.appOfferId = appOfferId;
    }

    public String getOfferType() {
        return offerType;
    }

    public void setOfferType(String offerType) {
        this.offerType = offerType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getOfferIndex() {
        return offerIndex;
    }

    public void setOfferIndex(Integer offerIndex) {
        this.offerIndex = offerIndex;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public Integer getOfferId() {
        return offerId;
    }

    public void setOfferId(Integer offerId) {
        this.offerId = offerId;
    }

    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }

    public String getListImage() {
        return listImage;
    }

    public void setListImage(String listImage) {
        this.listImage = listImage;
    }

    public String getListImageUrl() {
        return listImageUrl;
    }

    public void setListImageUrl(String listImageUrl) {
        this.listImageUrl = listImageUrl;
    }

    public String getGridImage() {
        return gridImage;
    }

    public void setGridImage(String gridImage) {
        this.gridImage = gridImage;
    }

    public String getGridImageUrl() {
        return gridImageUrl;
    }

    public void setGridImageUrl(String gridImageUrl) {
        this.gridImageUrl = gridImageUrl;
    }

    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<AppOfferUnitDetail> getUnitDetailList() {
        return unitDetailList;
    }

    public void setUnitDetailList(List<AppOfferUnitDetail> unitDetailList) {
        this.unitDetailList = unitDetailList;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getRedirectionLink() {
        return redirectionLink;
    }

    public void setRedirectionLink(String redirectionLink) {
        this.redirectionLink = redirectionLink;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getActionCategory() {
        return actionCategory;
    }

    public void setActionCategory(String actionCategory) {
        this.actionCategory = actionCategory;
    }

    public Integer getMenuCategoryId() {
        return menuCategoryId;
    }

    public void setMenuCategoryId(Integer menuCategoryId) {
        this.menuCategoryId = menuCategoryId;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public AppOfferType getAppOfferType() {
        return appOfferType;
    }

    public void setAppOfferType(AppOfferType appOfferType) {
        this.appOfferType = appOfferType;
    }


    public Integer getMov() {
        return mov;
    }

    public void setMov(Integer mov) {
        this.mov = mov;
    }

    public Integer getOfferValue() {
        return offerValue;
    }

    public void setOfferValue(Integer offerValue) {
        this.offerValue = offerValue;
    }

    public Integer getMaxDiscount() {
        return maxDiscount;
    }

    public void setMaxDiscount(Integer maxDiscount) {
        this.maxDiscount = maxDiscount;
    }

    public String getOfferCategory() {
        return offerCategory;
    }

    public void setOfferCategory(String offerCategory) {
        this.offerCategory = offerCategory;
    }

    public String getOfferOrderType() {
        return offerOrderType;
    }

    public void setOfferOrderType(String offerOrderType) {
        this.offerOrderType = offerOrderType;
    }

    public List<AppOfferApplicability> getAppOfferApplicabilityList() {
        return appOfferApplicabilityList;
    }

    public void setAppOfferApplicabilityList(List<AppOfferApplicability> appOfferApplicabilityList) {
        this.appOfferApplicabilityList = appOfferApplicabilityList;
    }

    public List<String> getAppofferApplicabilityFlags() {
        return appofferApplicabilityFlags;
    }

    public void setAppofferApplicabilityFlags(List<String> appofferApplicabilityFlags) {
        this.appofferApplicabilityFlags = appofferApplicabilityFlags;
    }

    public Integer getBrandId() { return this.brandId; }

    public void setBrandId(Integer brandId) { this.brandId = brandId; }

}


