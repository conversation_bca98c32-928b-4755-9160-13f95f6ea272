/*
 * Created By <PERSON><PERSON><PERSON>
 */

package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecipeApprovalDTO {

    private Integer recipeId;
    private String recipeName;
    private String dimension;
    private String recipeProfile;
    private Integer recipeLastUpdatedBy;
    private String recipeLastUpdatedByName;
}
