/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.03.23 at 05:22:11 PM IST
//

package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * Java class for UnitBasicDetail complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="UnitBasicDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}UnitStatus"/&gt;
 *         &lt;element name="noOfTerminal" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="noOfTakeawayTerminals" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="category" type="{http://www.w3schools.com}UnitCategory"/&gt;
 *         &lt;element name="region" type="{http://www.w3schools.com}String"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contact" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="email" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="tin" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="address" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="latitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="longitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subCategory" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitBasicDetail", propOrder = { "id", "name", "status", "noOfTerminal", "noOfTakeawayTerminals",
		"category", "region", "city", "contact", "email", "tin","fssai", "address", "latitude", "longitude", "subCategory" })
@Document
public class UnitBasicDetail implements Serializable, Comparable<UnitBasicDetail> {

	/**
	 *
	 */
	private static final long serialVersionUID = -2153356827093573640L;
	@Id
	private String _id;
	@Field
	protected int id;
	@XmlElement(required = true)
	@Field
	protected String name;
	@XmlElement(required = true)
	@Field
	protected String referenceName;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	@Field
	protected UnitStatus status;
	@Field
	protected int noOfTerminal;
	@Field
	protected int noOfTakeawayTerminals;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	@Field
	protected UnitCategory category;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	@Field
	protected String region;
	@XmlElement(required = true)
	@Field
	protected String city;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String contact;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String email;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String tin;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String address;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String latitude;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String longitude;
	@XmlSchemaType(name = "string")
	@Field
	protected UnitSubCategory subCategory;
	@XmlElement(required = true)
	@Field
	protected String locationCode;
	protected String state;
	protected String stateCode;
	protected boolean partnerPriced;
	protected boolean tokenEnabled;
	protected int companyId;
	protected TrueCallerSettings trueCallerEnabled;
	private boolean workStationEnabled;
	protected boolean live;
	private boolean hotAndColdMerged;
	private boolean liveInventoryEnabled;
	private IdCodeName location;
	private Integer unitManagerId;
	private Integer cafeManagerId;
	private String packagingType;
	private BigDecimal packagingValue;
	private String cafeAppStatus;
	private String cafeNeoStatus;
	private String googleMerchantId;
	private String shortName;
	private String costCenterName;
	private String fssai;
	private Date handOverDate;
	protected Integer salesClonedFrom;
	protected Date probableOpeningDate;
	private Integer pricingProfile;
	private String unitZone;
	private String f9Enabled;

	private Boolean isClosed;

	private Integer posVersion;
	private String faDaycloseEnabled;

	private Integer unitCafeManager;
	private Date lastHandoverDate;
	private String lastHandoverFrom;
	private String varianceAcknowledgementRequired;

	private String loyalTeaRedemptionAllowed;

	private String autoEdcPayment;

	private String monkRecipeProfile;
	private Integer noOfMonksNeeded;
	private String closure;

	private boolean isHotSpotEnabled;
	private String loyalTeaBurnSwiggyAllowed;

	private Boolean  assemblyStrictMode;

	private String showLoyalteaScreen;
	private Boolean  assemblyOtpMode;

	private Boolean IsOtpViaEmail;
	private Boolean customerLogin;
	private Boolean autoTokenEnabled;

	private String isSuperUEnabled = "N";
	private boolean isTestingUnit = false;



	private BigDecimal serviceCharge;
	private boolean serviceChargePosEnabled = false;
	private boolean serviceChargeAppEnabled = false;

	private CafeType cafeType;
	private Integer customAddonsLimit;
	
	private Boolean FeedbackFormEnabledForPos;
	
	private String dreamfolksOutletId;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/**
	 * Gets the value of the id property.
	 *
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getReferenceName() {
		return referenceName;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setReferenceName(String value) {
		this.referenceName = value;
	}

	/**
	 * Gets the value of the status property.
	 *
	 * @return possible object is {@link UnitStatus }
	 *
	 */
	public UnitStatus getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 *
	 * @param value allowed object is {@link UnitStatus }
	 *
	 */
	public void setStatus(UnitStatus value) {
		this.status = value;
	}

	/**
	 * Gets the value of the noOfTerminal property.
	 *
	 */
	public int getNoOfTerminal() {
		return noOfTerminal;
	}

	/**
	 * Sets the value of the noOfTerminal property.
	 *
	 */
	public void setNoOfTerminal(int value) {
		this.noOfTerminal = value;
	}

	/**
	 * Gets the value of the noOfTakeawayTerminals property.
	 *
	 */
	public int getNoOfTakeawayTerminals() {
		return noOfTakeawayTerminals;
	}

	/**
	 * Sets the value of the noOfTakeawayTerminals property.
	 *
	 */
	public void setNoOfTakeawayTerminals(int value) {
		this.noOfTakeawayTerminals = value;
	}

	/**
	 * Gets the value of the category property.
	 *
	 * @return possible object is {@link UnitCategory }
	 *
	 */
	public UnitCategory getCategory() {
		return category;
	}

	/**
	 * Sets the value of the category property.
	 *
	 * @param value allowed object is {@link UnitCategory }
	 *
	 */
	public void setCategory(UnitCategory value) {
		this.category = value;
	}

	/**
	 * Gets the value of the region property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getRegion() {
		return region;
	}

	/**
	 * Sets the value of the region property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setRegion(String value) {
		this.region = value;
	}

	/**
	 * Gets the value of the city property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getCity() {
		return city;
	}

	/**
	 * Sets the value of the city property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setCity(String value) {
		this.city = value;
	}

	/**
	 * Gets the value of the contact property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getContact() {
		return contact;
	}

	/**
	 * Sets the value of the contact property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setContact(String value) {
		this.contact = value;
	}

	/**
	 * Gets the value of the email property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getEmail() {
		return email;
	}

	/**
	 * Sets the value of the email property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setEmail(String value) {
		this.email = value;
	}

	/**
	 * Gets the value of the tin property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getTin() {
		return tin;
	}

	/**
	 * Sets the value of the tin property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setTin(String value) {
		this.tin = value;
	}

	/**
	 * Gets the value of the address property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getAddress() {
		return address;
	}

	/**
	 * Sets the value of the address property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setAddress(String value) {
		this.address = value;
	}

	/**
	 * Gets the value of the latitude property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getLatitude() {
		return latitude;
	}

	/**
	 * Sets the value of the latitude property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setLatitude(String value) {
		this.latitude = value;
	}

	/**
	 * Gets the value of the longitude property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getLongitude() {
		return longitude;
	}

	/**
	 * Sets the value of the longitude property.
	 *
	 * @param value allowed object is {@link String }
	 *
	 */
	public void setLongitude(String value) {
		this.longitude = value;
	}

	public UnitSubCategory getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(UnitSubCategory subCategory) {
		this.subCategory = subCategory;
	}

	public String getLocationCode() {
		return locationCode;
	}

	public void setLocationCode(String locationCode) {
		this.locationCode = locationCode;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see java.lang.Object#hashCode()
	 */
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + id;
		return result;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see java.lang.Object#equals(java.lang.Object)
	 */
	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null) {
			return false;
		}
		if (!(obj instanceof UnitBasicDetail)) {
			return false;
		}
		UnitBasicDetail other = (UnitBasicDetail) obj;
		if (id != other.id) {
			return false;
		}
		return true;
	}

	@Override
	public int compareTo(UnitBasicDetail o) {
		return Integer.compare(this.getId(), o.getId());
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getStateCode() {
		return stateCode;
	}

	public void setStateCode(String stateCode) {
		this.stateCode = stateCode;
	}

	public boolean isPartnerPriced() {
		return partnerPriced;
	}

	public void setPartnerPriced(boolean partnerPriced) {
		this.partnerPriced = partnerPriced;
	}

	public boolean isTokenEnabled() {
		return tokenEnabled;
	}

	public void setTokenEnabled(boolean tokenEnabled) {
		this.tokenEnabled = tokenEnabled;
	}

	public int getCompanyId() {
		return companyId;
	}

	public void setCompanyId(int companyId) {
		this.companyId = companyId;
	}

	public TrueCallerSettings getTrueCallerEnabled() {
		return trueCallerEnabled;
	}

	public void setTrueCallerEnabled(TrueCallerSettings trueCallerEnabled) {
		this.trueCallerEnabled = trueCallerEnabled;
	}

	public boolean isWorkStationEnabled() {
		return workStationEnabled;
	}

	public void setWorkStationEnabled(boolean workStationEnabled) {
		this.workStationEnabled = workStationEnabled;
	}

	public boolean isLive() {
		return live;
	}

	public void setLive(boolean live) {
		this.live = live;
	}

	public void setHotAndColdMerged(boolean hotAndColdMerged) {
		this.hotAndColdMerged = hotAndColdMerged;
	}

	public boolean getHotAndColdMerged() {
		return hotAndColdMerged;
	}

	public boolean isLiveInventoryEnabled() {
		return liveInventoryEnabled;
	}

	public void setLiveInventoryEnabled(boolean liveInventoryEnabled) {
		this.liveInventoryEnabled = liveInventoryEnabled;
	}

	public void setLocation(IdCodeName location) {
		this.location = location;
	}

	public IdCodeName getLocation() {
		return location;
	}

	public Integer getUnitManagerId() {
		return unitManagerId;
	}

	public void setUnitManagerId(Integer unitManagerId) {
		this.unitManagerId = unitManagerId;
	}

	public Integer getCafeManagerId() {
		return cafeManagerId;
	}

	public void setCafeManagerId(Integer cafeManagerId) {
		this.cafeManagerId = cafeManagerId;
	}

	public String getPackagingType() {
		return packagingType;
	}

	public void setPackagingType(String packagingType) {
		this.packagingType = packagingType;
	}

	public BigDecimal getPackagingValue() {
		return packagingValue;
	}

	public void setPackagingValue(BigDecimal packagingValue) {
		this.packagingValue = packagingValue;
	}

	public String getCafeAppStatus() {
		return cafeAppStatus;
	}

	public void setCafeAppStatus(String cafeAppStatus) {
		this.cafeAppStatus = cafeAppStatus;
	}

	public String getCafeNeoStatus() {
		return cafeNeoStatus;
	}

	public void setCafeNeoStatus(String cafeNeoStatus) {
		this.cafeNeoStatus = cafeNeoStatus;
	}

	public String getGoogleMerchantId() {
		return googleMerchantId;
	}

	public void setGoogleMerchantId(String googleMerchantId) {
		this.googleMerchantId = googleMerchantId;
	}

	public String getShortName() {
		return shortName;
	}

	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getCostCenterName() {
		return costCenterName;
	}

	public void setCostCenterName(String costCenterName) {
		this.costCenterName = costCenterName;
	}

	public String getFssai() {
		return fssai;
	}

	public void setFssai(String fssai) {
		this.fssai = fssai;
	}

	public Date getHandOverDate() {
		return handOverDate;
	}

	public void setHandOverDate(Date handOverDate) {
		this.handOverDate = handOverDate;
	}

	public Integer getSalesClonedFrom() {
		return salesClonedFrom;
	}

	public void setSalesClonedFrom(Integer salesClonedFrom) {
		this.salesClonedFrom = salesClonedFrom;
	}

	public Date getProbableOpeningDate() {
		return probableOpeningDate;
	}

	public void setProbableOpeningDate(Date probableOpeningDate) {
		this.probableOpeningDate = probableOpeningDate;
	}

	public Integer getPricingProfile() {
		return pricingProfile;
	}

	public void setPricingProfile(Integer pricingProfile) {
		this.pricingProfile = pricingProfile;
	}

	public String getUnitZone() {
		return unitZone;
	}

	public void setUnitZone(String unitZone) {
		this.unitZone = unitZone;
	}

	public String getF9Enabled() {
		return f9Enabled;
	}

	public void setF9Enabled(String f9Enabled) {
		this.f9Enabled = f9Enabled;
	}

	public Boolean getClosed() {
		return isClosed;
	}

	public void setClosed(Boolean closed) {
		isClosed = closed;
	}

	public Integer getPosVersion() {
		return posVersion;
	}

	public void setPosVersion(Integer posVersion) {
		this.posVersion = posVersion;
	}

	public String getFaDaycloseEnabled() {
		return faDaycloseEnabled;
	}

	public void setFaDaycloseEnabled(String faDaycloseEnabled) {
		this.faDaycloseEnabled = faDaycloseEnabled;
	}

	public Integer getUnitCafeManager() {
		return unitCafeManager;
	}

	public void setUnitCafeManager(Integer unitCafeManager) {
		this.unitCafeManager = unitCafeManager;
	}

	public Date getLastHandoverDate() {
		return lastHandoverDate;
	}

	public void setLastHandoverDate(Date lastHandoverDate) {
		this.lastHandoverDate = lastHandoverDate;
	}

	public String getLastHandoverFrom() {
		return lastHandoverFrom;
	}

	public void setLastHandoverFrom(String lastHandoverFrom) {
		this.lastHandoverFrom = lastHandoverFrom;
	}

	public boolean isHotSpotEnabled() {
		return isHotSpotEnabled;
	}

	public void setHotSpotEnabled(boolean hotSpotEnabled) {
		isHotSpotEnabled = hotSpotEnabled;
	}

	public String getVarianceAcknowledgementRequired() {return varianceAcknowledgementRequired;}

	public void setVarianceAcknowledgementRequired(String varianceAcknowledgementRequired) {this.varianceAcknowledgementRequired = varianceAcknowledgementRequired;}

	public String getLoyalTeaRedemptionAllowed() {
		return loyalTeaRedemptionAllowed;
	}

	public void setLoyalTeaRedemptionAllowed(String loyalTeaRedemptionAllowed) {
		this.loyalTeaRedemptionAllowed = loyalTeaRedemptionAllowed;
	}

	public String getAutoEdcPayment() {
		return autoEdcPayment;
	}

	public void setAutoEdcPayment(String autoEdcPayment) {
		this.autoEdcPayment = autoEdcPayment;
	}

	public String getMonkRecipeProfile() {
		return monkRecipeProfile;
	}

	public void setMonkRecipeProfile(String monkRecipeProfile) {
		this.monkRecipeProfile = monkRecipeProfile;
	}

	public Integer getNoOfMonksNeeded() {
		return noOfMonksNeeded;
	}

	public void setNoOfMonksNeeded(Integer noOfMonksNeeded) {
		this.noOfMonksNeeded = noOfMonksNeeded;
	}

	public void setLoyalTeaBurnSwiggyAllowed(String loyalTeaBurnSwiggyAllowed){
		this.loyalTeaBurnSwiggyAllowed = loyalTeaBurnSwiggyAllowed;
	}

	public String getLoyalTeaBurnSwiggyAllowed(){
		return this.loyalTeaBurnSwiggyAllowed;
	}


	public Boolean getAssemblyStrictMode() {
		return assemblyStrictMode;
	}

	public void setAssemblyStrictMode(Boolean assemblyStrictMode) {
		this.assemblyStrictMode = assemblyStrictMode;
	}

	public String getClosure() {
		return closure;
	}

	public void setClosure(String closure) {
		this.closure = closure;
	}

	public void setShowLoyalteaScreen(String showLoyalteaScreen) {
		this.showLoyalteaScreen = showLoyalteaScreen;
	}

	public String getShowLoyalteaScreen() {
		return showLoyalteaScreen;
	}

	public Boolean getIsOtpViaEmail() {
		return IsOtpViaEmail;
	}

	public void setIsOtpViaEmail(Boolean isOtpViaEmail) {
		IsOtpViaEmail = isOtpViaEmail;
	}

	public Boolean getAssemblyOtpMode() {
		return assemblyOtpMode;
	}

	public void setAssemblyOtpMode(Boolean assemblyOtpMode) {
		this.assemblyOtpMode = assemblyOtpMode;
	}

	public Boolean getCustomerLogin() {
		return customerLogin;
	}

	public void setCustomerLogin(Boolean customerLogin) {
		this.customerLogin = customerLogin;
	}

	public Boolean getAutoTokenEnabled() {
		return autoTokenEnabled;
	}

	public void setAutoTokenEnabled(Boolean autoTokenEnabled) {
		this.autoTokenEnabled = autoTokenEnabled;
	}

	public String getIsSuperUEnabled() {
		return isSuperUEnabled;
	}

	public void setIsSuperUEnabled(String isSuperUEnabled) {
		this.isSuperUEnabled = isSuperUEnabled;
	}

	public Boolean getIsTestingUnit() {
		return isTestingUnit;
	}

	public void setIsTestingUnit(Boolean isTestingUnit) {
		this.isTestingUnit = !Objects.isNull(isTestingUnit) && isTestingUnit;
	}
	public BigDecimal getServiceCharge() {
		return serviceCharge;
	}

	public void setServiceCharge(BigDecimal serviceCharge) {
		this.serviceCharge = serviceCharge;
	}

	public boolean getServiceChargePosEnabled() {
		return serviceChargePosEnabled;
	}

	public void setServiceChargePosEnabled(boolean serviceChargePosEnabled) {
		this.serviceChargePosEnabled = serviceChargePosEnabled;
	}

	public boolean getServiceChargeAppEnabled() {
		return serviceChargeAppEnabled;
	}

	public void setServiceChargeAppEnabled(boolean serviceChargeAppEnabled) {
		this.serviceChargeAppEnabled = serviceChargeAppEnabled;
	}

	public void setCafeType(CafeType cafeType) {
		this.cafeType = cafeType;
	}

	public CafeType getCafeType() {
		return cafeType;
	}

	public Integer getCustomAddonsLimit() {
		return customAddonsLimit;
	}
	public void setCustomAddonsLimit(Integer customAddonsLimit) {
		this.customAddonsLimit = customAddonsLimit;
	}
	
	public Boolean getFeedbackFormEnabledForPos() {
		return FeedbackFormEnabledForPos;
	}
	
	public void setFeedbackFormEnabledForPos(Boolean feedbackFormEnabledForPos) {
		FeedbackFormEnabledForPos = feedbackFormEnabledForPos;
	}
	
	public String getDreamfolksOutletId() {
		return dreamfolksOutletId;
	}
	
	public void setDreamfolksOutletId(String dreamfolksOutletId) {
		this.dreamfolksOutletId = dreamfolksOutletId;
	}
	
	
}
