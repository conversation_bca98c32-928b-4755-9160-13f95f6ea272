/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 12:10:26 PM IST 
//

package com.stpl.tech.master.domain.model;


import java.util.HashMap;
import java.util.List;

public class DispenserBulkRequest {

    private List<Integer> products;

    private HashMap<String,String> variants;
    private HashMap<String,String> addons;
    private HashMap<String,String> mandatoryAddons;


    public List<Integer> getProducts() {
        return products;
    }

    public void setProducts(List<Integer> products) {
        this.products = products;
    }

    public HashMap<String, String> getVariants() {
        return variants;
    }

    public void setVariants(HashMap<String, String> variants) {
        this.variants = variants;
    }

    public HashMap<String, String> getAddons() {
        return addons;
    }

    public void setAddons(HashMap<String, String> addons) {
        this.addons = addons;
    }

    public HashMap<String, String> getMandatoryAddons() {
        return mandatoryAddons;
    }

    public void setMandatoryAddons(HashMap<String, String> mandatoryAddons) {
        this.mandatoryAddons = mandatoryAddons;
    }
}
