/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.21 at 02:44:39 PM IST 
//

package com.stpl.tech.master.domain.model;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for OfferMetaDataType.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="OfferMetaDataType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="PRODUCT"/&gt;
 *     &lt;enumeration value="PRODUCT_SUB_CATEGORY"/&gt;
 *     &lt;enumeration value="PRODUCT_CATEGORY"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "OfferMetaDataType")
@XmlEnum
public enum OfferMetaDataType {

	PRODUCT("PRODUCT") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			Set<Integer> productCategories = productDetails.stream()
					.mapToInt(productDetail -> productDetail.getDetail().getId()).boxed().collect(Collectors.toSet());
			List<Integer> categories = productCategories.stream().filter(p -> convert(mappingValues).contains(p))
					.collect(Collectors.toList());
			return categories;
		}
	},

	@XmlEnumValue("PRODUCT_SUB_CATEGORY") PRODUCT_SUB_CATEGORY("PRODUCT_SUB_CATEGORY") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			return productDetails.stream().filter(p -> convert(mappingValues).contains(p.getSubType()))
					.mapToInt(p -> p.getDetail().getId()).boxed().collect(Collectors.toList());
		}
	},

	@XmlEnumValue("PRODUCT_CATEGORY") PRODUCT_CATEGORY("PRODUCT_CATEGORY") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			Set<Integer> intMappings = convert(mappingValues);
		    return productDetails.stream().filter(p -> intMappings.contains(p.getType()))
					.mapToInt(p -> p.getDetail().getId()).boxed().collect(Collectors.toList());
		}
	},

	@XmlEnumValue("PRODUCT_QUANTITY") PRODUCT_QUANTITY("PRODUCT_QUANTITY") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			/*
			 * Set<Integer> productCategories = productDetails.stream()
			 * .mapToInt(productDetail -> productDetail.get())
			 * .boxed().collect(Collectors.toSet()); return
			 * productCategories.stream().filter(mappingValues::contains).
			 * collect(Collectors.toList());
			 */
			return null;
		}
	},


    @XmlEnumValue("COMBO_ITEM_GROUP") COMBO_ITEM_GROUP("COMBO_ITEM_GROUP") {
        @Override
        public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {

            return null;
        }
    },

	DAYS("DAYS") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			return null;
		}
	},

	TIME_RANGE("TIME_RANGE") {
		@Override
		public List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails) {
			return null;
		}
	};

	private static Set<Integer> convert(Set<String> mappingValues){
	    return mappingValues.stream().mapToInt(Integer::parseInt).boxed().collect(Collectors.toSet());
    }


	private final String value;

	OfferMetaDataType(String v) {
		value = v;
	}

	public String value() {
		return value;
	}

	public static OfferMetaDataType fromValue(String v) {
		for (OfferMetaDataType c : OfferMetaDataType.values()) {
			if (c.value.equals(v)) {
				return c;
			}
		}
		throw new IllegalArgumentException(v);
	}

	public abstract List<Integer> getCommonElements(Set<String> mappingValues, Set<ProductBasicDetail> productDetails);

}
