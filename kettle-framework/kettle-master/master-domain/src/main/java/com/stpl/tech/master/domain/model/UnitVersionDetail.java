package com.stpl.tech.master.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stpl.tech.util.AppConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UnitVersionDetail {

    private String currentVersion;
    private String expectedVersion;
    @JsonFormat(timezone = AppConstants.DEFAULT_TIME_ZONE, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedVersionDate;
    private String expectedVersionDescription;
    private String expectedVersionReleaseType;
    private String expectedVersionBuildName;
}
