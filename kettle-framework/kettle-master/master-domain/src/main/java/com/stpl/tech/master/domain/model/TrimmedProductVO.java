package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Map;

public class TrimmedProductVO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -2605990215255787849L;
	private Integer id;
	private String name;
	private ProductStatus status;
	private Boolean inventoryTracked;
	private Integer type;
	private Map<String, TrimmedProductPrice> prices;
	private Integer brandId;

	public TrimmedProductVO() {
		
	}
	
	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public TrimmedProductVO(Integer id, String name, ProductStatus status, Boolean inventoryTracked, Integer type,
							Map<String, TrimmedProductPrice> prices, Integer brandId) {
		super();
		this.id = id;
		this.name = name;
		this.status = status;
		this.inventoryTracked = inventoryTracked;
		this.type = type;
		this.prices = prices;
		this.brandId=brandId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public ProductStatus getStatus() {
		return status;
	}

	public void setStatus(ProductStatus status) {
		this.status = status;
	}

	public Boolean getInventoryTracked() {
		return inventoryTracked;
	}

	public void setInventoryTracked(Boolean inventoryTracked) {
		this.inventoryTracked = inventoryTracked;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Map<String, TrimmedProductPrice> getPrices() {
		return prices;
	}

	public void setPrices(Map<String, TrimmedProductPrice> prices) {
		this.prices = prices;
	}

	@Override
	public String toString() {
		return "TrimmedProductVO [id=" + id + ", name=" + name + ", status=" + status + ", inventoryTracked="
				+ inventoryTracked + ", type=" + type + ", prices=" + prices + ", brandId=" + brandId + "]";
	}

	
}
