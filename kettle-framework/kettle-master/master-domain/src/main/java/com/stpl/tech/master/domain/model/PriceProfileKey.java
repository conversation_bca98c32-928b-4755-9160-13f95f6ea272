package com.stpl.tech.master.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public class PriceProfileKey implements Serializable {
    @Serial
    private static final long serialVersionUID = -6616046842052099280L;
    Integer priceProfileId;

    Integer priceProfileVersion;
}
