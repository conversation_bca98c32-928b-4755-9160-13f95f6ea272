//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.20 at 07:12:44 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


@XmlType(name = "OfferActionType")
@XmlEnum
public enum OfferActionType {

    TapToCopy("Tap to Copy", "TTC"),
    HOME("Will open Home Grid", "home"),
    HOME_LIST("Will open Home List", "home_list"),
    CART("Will open Cart", "cart"),
    STORE_LOCATOR("Will open Cafe search screen", "store_locator"),
    <PERSON><PERSON>_<PERSON>DERS("Will open My Orders Screen", "my_orders"),
    PRODUCT_SEARCH("Will open Product Search", "product_search"),
    PROFILE_MAIN("Will open Profile screen", "profile_main"),
    PROFILE_OFFERS("Will open Profile Screen and expand offers card to top", "profile_offers"),
    PROFILE_ALLIANCES("Will open Profile Screen and expand alliances to top if exists", "profile_alliances"),
    PROFILE_MONEY_WALLET("Will open Profile Screen and expand money wallet card to top", "profile_money_wallet"),
    PROFILE_MONEY_CASH("Will open Profile Screen and expand money cash card to top", "profile_money_cash"),
    PROFILE_REFERRAL("Will open Profile Screen and expand referral card to top", "profile_referral"),
    REFERRAL("will open referral screen","referral"),
    PROFILE_LOYALTY("Will open Profile Screen and expand loyalty card to top", "profile_loyalty");

    private String offerName;
    private String value;

    OfferActionType(String name, String value) {
        this.offerName = name;
        this.value = value;
    }

    public String value() {
        return value;
    }

    public String offerName() {
        return offerName;
    }
}
