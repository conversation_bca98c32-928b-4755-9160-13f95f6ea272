package com.stpl.tech.master.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LCMMenuVariantMetadataGroupDomain {

    private Long id;
    private String groupName;

    private String status;

    @Builder.Default
    private List<LCMMenuVariantMetadataItemDomain> items  =new ArrayList<>();

}
