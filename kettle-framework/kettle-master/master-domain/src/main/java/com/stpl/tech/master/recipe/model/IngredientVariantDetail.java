//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.19 at 03:23:59 PM IST 
//

package com.stpl.tech.master.recipe.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IngredientVariantDetail", propOrder = { "_id", "alias", "quantity", "defaultSetting", "status",
		"captured" })
@Document
public class IngredientVariantDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2198505699784866775L;

	@Id
	private String _id;
	/*
	 * @Version
	 * 
	 * @JsonIgnore private Long version;
	 * 
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */
	@Field
	protected int productId;
	@Field
	protected String alias;
	@Field
	protected UnitOfMeasure uom;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal quantity;
	@Field
	protected boolean defaultSetting;
	@Field
	protected String status = "ACTIVE";
	@Field
	protected boolean captured = true;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal yield = new BigDecimal(100d);
	@Field
	protected String desc;
	@Field
	protected String tag;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*
	 * public Long getVersion() { return version; }
	 * 
	 * public void setVersion(Long version) { this.version = version; }
	 * 
	 * public String getDetachAll() { return detachAll; }
	 * 
	 * public void setDetachAll(String detachAll) { this.detachAll = detachAll;
	 * }
	 */

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public UnitOfMeasure getUom() {
		return uom;
	}

	public void setUom(UnitOfMeasure uom) {
		this.uom = uom;
	}

	/**
	 * Gets the value of the alias property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAlias() {
		return alias;
	}

	/**
	 * Sets the value of the alias property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAlias(String value) {
		this.alias = value;
	}

	/**
	 * Gets the value of the quantity property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getQuantity() {
		return quantity;
	}

	/**
	 * Sets the value of the quantity property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setQuantity(BigDecimal value) {
		this.quantity = value;
	}

	/**
	 * Gets the value of the default property.
	 * 
	 */
	public boolean isDefaultSetting() {
		return defaultSetting;
	}

	/**
	 * Sets the value of the default property.
	 * 
	 */
	public void setDefaultSetting(boolean value) {
		this.defaultSetting = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	/**
	 * @return the captured
	 */
	public boolean isCaptured() {
		return captured;
	}

	/**
	 * @param captured
	 *            the captured to set
	 */
	public void setCaptured(boolean captured) {
		this.captured = captured;
	}

	public BigDecimal getYield() {
		return yield;
	}

	public void setYield(BigDecimal yield) {
		this.yield = yield;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}
}
