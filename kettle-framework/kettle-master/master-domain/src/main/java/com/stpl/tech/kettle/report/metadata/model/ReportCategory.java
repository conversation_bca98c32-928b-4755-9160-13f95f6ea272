/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2015.12.15 at 07:47:57 PM IST
//

package com.stpl.tech.kettle.report.metadata.model;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for ReportCategory complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="ReportCategory"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="report" type="{http://www.w3schools.com}ReportData" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;attribute name="id" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *       &lt;attribute name="name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="type" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="accessCode" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="schedule" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="toEmails" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="fromEmail" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="attachmentType" type="{http://www.w3schools.com}ReportOutput" /&gt;
 *       &lt;attribute name="compress" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ReportCategory", propOrder = { "report" })
public class ReportCategory {
	@XmlElementWrapper(name = "reports")
	@XmlElement(required = true)
    protected List<ReportData> report;
    @XmlAttribute(name = "id")
    protected Integer id;
    @XmlAttribute(name = "name")
    protected String name;
    @XmlAttribute(name = "type")
    protected String type;
    @XmlAttribute(name = "accessCode")
    protected String accessCode;
    @XmlAttribute(name = "schedule")
    protected String schedule;
    @XmlAttribute(name = "toEmails")
    protected String toEmails;
    @XmlAttribute(name = "fromEmail")
    protected String fromEmail;
    @XmlAttribute(name = "attachmentType")
    protected ReportOutput attachmentType;
    @XmlAttribute(name = "compress")
    protected Boolean compress;
    @XmlAttribute(name = "forceEmail")
    protected Boolean forceEmail = false;

    /**
     * Gets the value of the report property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the report property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getReport().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ReportData }
     *
     *
     */
    public List<ReportData> getReport() {
        if (report == null) {
            report = new ArrayList<ReportData>();
        }
        return this.report;
    }

    /**
     * Gets the value of the id property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the name property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the accessCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getAccessCode() {
        return accessCode;
    }

    /**
     * Sets the value of the accessCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setAccessCode(String value) {
        this.accessCode = value;
    }

    /**
     * Gets the value of the schedule property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSchedule() {
        return schedule;
    }

    /**
     * Sets the value of the schedule property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSchedule(String value) {
        this.schedule = value;
    }

    /**
     * Gets the value of the toEmails property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getToEmails() {
        return toEmails;
    }

    /**
     * Sets the value of the toEmails property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setToEmails(String value) {
        this.toEmails = value;
    }

    /**
     * Gets the value of the fromEmail property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getFromEmail() {
        return fromEmail;
    }

    /**
     * Sets the value of the fromEmail property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setFromEmail(String value) {
        this.fromEmail = value;
    }

    /**
     * Gets the value of the attachmentType property.
     *
     * @return
     *     possible object is
     *     {@link ReportOutput }
     *
     */
    public ReportOutput getAttachmentType() {
        return attachmentType;
    }

    /**
     * Sets the value of the attachmentType property.
     *
     * @param value
     *     allowed object is
     *     {@link ReportOutput }
     *
     */
    public void setAttachmentType(ReportOutput value) {
        this.attachmentType = value;
    }

    /**
     * Gets the value of the compress property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean isCompress() {
        return compress;
    }

    /**
     * Sets the value of the compress property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setCompress(Boolean value) {
        this.compress = value;
    }

    public Boolean getForceEmail() {
        return forceEmail;
    }

    public void setForceEmail(Boolean forceEmail) {
        this.forceEmail = forceEmail;
    }
}
