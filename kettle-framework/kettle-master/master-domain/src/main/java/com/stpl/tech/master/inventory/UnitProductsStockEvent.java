package com.stpl.tech.master.inventory;

import com.stpl.tech.master.domain.model.IdName;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class UnitProductsStockEvent implements Serializable {

    private static final long serialVersionUID = 7180419820751887692L;
    private Integer unitId;
    private List<String> productIds = new ArrayList<>();
    private List<IdName> productDimensions = new ArrayList<>();
    private Integer partnerId;
    private StockStatus status;

    private Integer brandId;

    private Boolean forceStockOut;
    private Integer retryCount;

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public UnitProductsStockEvent() {
    }

    public UnitProductsStockEvent(Integer unitId, StockStatus status) {
        this.unitId = unitId;
        this.status = status;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public List<String> getProductIds() {
        if(productIds == null){
            productIds = new ArrayList<>();
        }
        return productIds;
    }

    public void setProductIds(List<String> productIds) {
        this.productIds = productIds;
    }

    public StockStatus getStatus() {
        return status;
    }

    public void setStatus(StockStatus status) {
        this.status = status;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public List<IdName> getProductDimensions() {
        return productDimensions;
    }

    public void setProductDimensions(List<IdName> productDimensions) {
        this.productDimensions = productDimensions;
    }

    public Boolean getForceStockOut() { return this.forceStockOut; }

    public void setForceStockOut(Boolean forceStockOut) { this.forceStockOut = forceStockOut; }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        UnitProductsStockEvent that = (UnitProductsStockEvent) o;

        return new EqualsBuilder()
                .append(unitId, that.unitId)
                .append(productIds, that.productIds)
                .append(productDimensions, that.productDimensions)
                .append(partnerId, that.partnerId)
                .append(status, that.status)
                .append(forceStockOut, that.forceStockOut)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(unitId)
                .append(productIds)
                .append(productDimensions)
                .append(partnerId)
                .append(status)
                .append(forceStockOut)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "UnitProductsStockEvent{" +
                "unitId=" + unitId +
                ", productIds=" + productIds +
                ", productDimensions=" + productDimensions +
                ", partnerId=" + partnerId +
                ", status=" + status +
                ", forceStockOut=" + forceStockOut +
                '}';
    }
}
