//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.23 at 01:37:36 PM IST 
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for KioskLocationDetails complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="KioskLocationDetails"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="locationId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="locationName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="locationShortCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="locationAddress" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="officeDetails" type="{http://www.w3schools.com}KioskOfficeDetails"/&gt;
 *         &lt;element name="assigned" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="locationStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="assignedUnit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="kioskMachines" type="{http://www.w3schools.com}KioskMachine" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "KioskLocationDetails", propOrder = {
    "locationId",
    "locationName",
    "locationShortCode",
    "locationAddress",
    "officeDetails",
    "assigned",
    "locationStatus",
    "assignedUnit",
    "kioskMachines"
})
public class KioskLocationDetails implements Serializable{

    private static final long serialVersionUID = 6252415121943411745L;

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer locationId;
    @XmlElement(required = true)
    protected String locationName;
    @XmlElement(required = true)
    protected String locationShortCode;
    @XmlElement(required = true)
    protected String locationAddress;
    @XmlElement(required = true)
    protected KioskOfficeDetails officeDetails;
    @XmlElement(defaultValue = "false")
    protected boolean assigned;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus locationStatus;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName assignedUnit;
    @XmlElement(required = true)
    protected List<KioskMachine> kioskMachines;

    /**
     * Gets the value of the locationId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getLocationId() {
        return locationId;
    }

    /**
     * Sets the value of the locationId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setLocationId(Integer value) {
        this.locationId = value;
    }

    /**
     * Gets the value of the locationName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     * Sets the value of the locationName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLocationName(String value) {
        this.locationName = value;
    }

    /**
     * Gets the value of the locationShortCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocationShortCode() {
        return locationShortCode;
    }

    /**
     * Sets the value of the locationShortCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLocationShortCode(String value) {
        this.locationShortCode = value;
    }

    /**
     * Gets the value of the locationAddress property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocationAddress() {
        return locationAddress;
    }

    /**
     * Sets the value of the locationAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLocationAddress(String value) {
        this.locationAddress = value;
    }

    /**
     * Gets the value of the officeDetails property.
     * 
     * @return
     *     possible object is
     *     {@link KioskOfficeDetails }
     *     
     */
    public KioskOfficeDetails getOfficeDetails() {
        return officeDetails;
    }

    /**
     * Sets the value of the officeDetails property.
     * 
     * @param value
     *     allowed object is
     *     {@link KioskOfficeDetails }
     *     
     */
    public void setOfficeDetails(KioskOfficeDetails value) {
        this.officeDetails = value;
    }

    /**
     * Gets the value of the assigned property.
     * 
     */
    public boolean isAssigned() {
        return assigned;
    }

    /**
     * Sets the value of the assigned property.
     * 
     */
    public void setAssigned(boolean value) {
        this.assigned = value;
    }

    /**
     * Gets the value of the locationStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getLocationStatus() {
        return locationStatus;
    }

    /**
     * Sets the value of the locationStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setLocationStatus(SwitchStatus value) {
        this.locationStatus = value;
    }

    /**
     * Gets the value of the assignedUnit property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getAssignedUnit() {
        return assignedUnit;
    }

    /**
     * Sets the value of the assignedUnit property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setAssignedUnit(IdCodeName value) {
        this.assignedUnit = value;
    }

    /**
     * Gets the value of the kioskMachines property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the kioskMachines property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getKioskMachines().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link KioskMachine }
     * 
     * 
     */
    public List<KioskMachine> getKioskMachines() {
        if (kioskMachines == null) {
            kioskMachines = new ArrayList<KioskMachine>();
        }
        return this.kioskMachines;
    }

}
