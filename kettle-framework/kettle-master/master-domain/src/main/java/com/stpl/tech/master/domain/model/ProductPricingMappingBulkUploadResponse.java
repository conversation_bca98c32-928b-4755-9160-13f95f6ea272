package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductPricingMappingBulkUploadResponse {
    List<String> errors;
    Integer recordsUpdated;

    Integer recordsAdded;

    Integer recordsWithErrors;


}
