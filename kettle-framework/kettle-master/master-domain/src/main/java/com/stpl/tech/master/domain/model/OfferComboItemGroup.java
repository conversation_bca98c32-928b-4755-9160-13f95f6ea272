package com.stpl.tech.master.domain.model;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.util.JSONSerializer;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by s<PERSON>khar on 09-04-2018.
 */
public class OfferComboItemGroup {

    private List<OfferComboItem> items;

    public List<OfferComboItem> getItems() {
        if(items==null){
            items = new ArrayList<>();
        }
        return items;
    }

    @Override
    public String toString() {
        return items.toString();
    }


    public static void main(String[] args) {
        OfferComboItemGroup itemGroup = new OfferComboItemGroup();

        OfferComboItem item = new OfferComboItem();
        item.setName("Vada Pav");
        item.setId(670);
        item.setQ(1);
        item.setD("None");
        itemGroup.getItems().add(item);

        System.out.println(JSONSerializer.toJSON(itemGroup));
    }
}
