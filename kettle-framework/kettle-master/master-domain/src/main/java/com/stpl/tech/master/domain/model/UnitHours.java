/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.30 at 03:35:50 PM IST 
//

package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.sql.Time;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>
 * Java class for UnitHours complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="UnitHours"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dayOfTheWeekNumber" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dayOfTheWeek" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="noOfShifts" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="isOperational" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="hasDelivery" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="hasDineIn" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="hasTakeAway" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="dineInOpeningTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="dineInClosingTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="deliveryOpeningTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="deliveryClosingTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="takeAwayOpeningTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="takeAwayClosingTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="shiftOneHandoverTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="shiftTwoHandoverTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitHours", propOrder = { "id", "unitId", "dayOfTheWeekNumber", "dayOfTheWeek", "noOfShifts",
		"isOperational", "hasDelivery", "hasDineIn", "hasTakeAway", "dineInOpeningTime", "dineInClosingTime",
		"deliveryOpeningTime", "deliveryClosingTime", "takeAwayOpeningTime", "takeAwayClosingTime",
		"shiftOneHandoverTime", "shiftTwoHandoverTime", "status" })
public class UnitHours implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -620287446994104940L;
	protected int id;
	protected int unitId;
	protected int dayOfTheWeekNumber;
	@XmlElement(required = true)
	protected String dayOfTheWeek;
	protected int noOfShifts;
	protected boolean isOperational;
	protected boolean hasDelivery;
	protected boolean hasDineIn;
	protected boolean hasTakeAway;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "time")
	protected Time dineInOpeningTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "time")
	protected Time dineInClosingTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "time")
	protected Time deliveryOpeningTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "time")
	protected Time deliveryClosingTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "time")
	protected Time takeAwayOpeningTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "time")
	protected Time takeAwayClosingTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "time")
	protected Time shiftOneHandoverTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter3.class)
	@XmlSchemaType(name = "time")
	protected Time shiftTwoHandoverTime;
	@XmlElement(required = true)
	protected String status;

	/**
	 * Gets the value of the id property.
	 * 
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the unitId property.
	 * 
	 */
	public int getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the unitId property.
	 * 
	 */
	public void setUnitId(int value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the dayOfTheWeekNumber property.
	 * 
	 */
	public int getDayOfTheWeekNumber() {
		return dayOfTheWeekNumber;
	}

	/**
	 * Sets the value of the dayOfTheWeekNumber property.
	 * 
	 */
	public void setDayOfTheWeekNumber(int value) {
		this.dayOfTheWeekNumber = value;
	}

	/**
	 * Gets the value of the dayOfTheWeek property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDayOfTheWeek() {
		return dayOfTheWeek;
	}

	/**
	 * Sets the value of the dayOfTheWeek property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDayOfTheWeek(String value) {
		this.dayOfTheWeek = value;
	}

	/**
	 * Gets the value of the noOfShifts property.
	 * 
	 */
	public int getNoOfShifts() {
		return noOfShifts;
	}

	/**
	 * Sets the value of the noOfShifts property.
	 * 
	 */
	public void setNoOfShifts(int value) {
		this.noOfShifts = value;
	}

	/**
	 * Gets the value of the isOperational property.
	 * 
	 */
	public boolean isIsOperational() {
		return isOperational;
	}

	/**
	 * Sets the value of the isOperational property.
	 * 
	 */
	public void setIsOperational(boolean value) {
		this.isOperational = value;
	}

	/**
	 * Gets the value of the hasDelivery property.
	 * 
	 */
	public boolean isHasDelivery() {
		return hasDelivery;
	}

	/**
	 * Sets the value of the hasDelivery property.
	 * 
	 */
	public void setHasDelivery(boolean value) {
		this.hasDelivery = value;
	}

	/**
	 * Gets the value of the hasDineIn property.
	 * 
	 */
	public boolean isHasDineIn() {
		return hasDineIn;
	}

	/**
	 * Sets the value of the hasDineIn property.
	 * 
	 */
	public void setHasDineIn(boolean value) {
		this.hasDineIn = value;
	}

	/**
	 * Gets the value of the hasTakeAway property.
	 * 
	 */
	public boolean isHasTakeAway() {
		return hasTakeAway;
	}

	/**
	 * Sets the value of the hasTakeAway property.
	 * 
	 */
	public void setHasTakeAway(boolean value) {
		this.hasTakeAway = value;
	}

	/**
	 * Gets the value of the dineInOpeningTime property.
	 * 
	 * @return possible object is {@link Time }
	 * 
	 */
	public Time getDineInOpeningTime() {
		return dineInOpeningTime;
	}

	/**
	 * Sets the value of the dineInOpeningTime property.
	 * 
	 * @param value
	 *            allowed object is {@link Time }
	 * 
	 */
	public void setDineInOpeningTime(Time value) {
		this.dineInOpeningTime = value;
	}

	/**
	 * Gets the value of the dineInClosingTime property.
	 * 
	 * @return possible object is {@link Time }
	 * 
	 */
	public Time getDineInClosingTime() {
		return dineInClosingTime;
	}

	/**
	 * Sets the value of the dineInClosingTime property.
	 * 
	 * @param value
	 *            allowed object is {@link Time }
	 * 
	 */
	public void setDineInClosingTime(Time value) {
		this.dineInClosingTime = value;
	}

	/**
	 * Gets the value of the deliveryOpeningTime property.
	 * 
	 * @return possible object is {@link Time }
	 * 
	 */
	public Time getDeliveryOpeningTime() {
		return deliveryOpeningTime;
	}

	/**
	 * Sets the value of the deliveryOpeningTime property.
	 * 
	 * @param value
	 *            allowed object is {@link Time }
	 * 
	 */
	public void setDeliveryOpeningTime(Time value) {
		this.deliveryOpeningTime = value;
	}

	/**
	 * Gets the value of the deliveryClosingTime property.
	 * 
	 * @return possible object is {@link Time }
	 * 
	 */
	public Time getDeliveryClosingTime() {
		return deliveryClosingTime;
	}

	/**
	 * Sets the value of the deliveryClosingTime property.
	 * 
	 * @param value
	 *            allowed object is {@link Time }
	 * 
	 */
	public void setDeliveryClosingTime(Time value) {
		this.deliveryClosingTime = value;
	}

	/**
	 * Gets the value of the takeAwayOpeningTime property.
	 * 
	 * @return possible object is {@link Time }
	 * 
	 */
	public Time getTakeAwayOpeningTime() {
		return takeAwayOpeningTime;
	}

	/**
	 * Sets the value of the takeAwayOpeningTime property.
	 * 
	 * @param value
	 *            allowed object is {@link Time }
	 * 
	 */
	public void setTakeAwayOpeningTime(Time value) {
		this.takeAwayOpeningTime = value;
	}

	/**
	 * Gets the value of the takeAwayClosingTime property.
	 * 
	 * @return possible object is {@link Time }
	 * 
	 */
	public Time getTakeAwayClosingTime() {
		return takeAwayClosingTime;
	}

	/**
	 * Sets the value of the takeAwayClosingTime property.
	 * 
	 * @param value
	 *            allowed object is {@link Time }
	 * 
	 */
	public void setTakeAwayClosingTime(Time value) {
		this.takeAwayClosingTime = value;
	}

	/**
	 * Gets the value of the shiftOneHandoverTime property.
	 * 
	 * @return possible object is {@link Time }
	 * 
	 */
	public Time getShiftOneHandoverTime() {
		return shiftOneHandoverTime;
	}

	/**
	 * Sets the value of the shiftOneHandoverTime property.
	 * 
	 * @param value
	 *            allowed object is {@link Time }
	 * 
	 */
	public void setShiftOneHandoverTime(Time value) {
		this.shiftOneHandoverTime = value;
	}

	/**
	 * Gets the value of the shiftTwoHandoverTime property.
	 * 
	 * @return possible object is {@link Time }
	 * 
	 */
	public Time getShiftTwoHandoverTime() {
		return shiftTwoHandoverTime;
	}

	/**
	 * Sets the value of the shiftTwoHandoverTime property.
	 * 
	 * @param value
	 *            allowed object is {@link Time }
	 * 
	 */
	public void setShiftTwoHandoverTime(Time value) {
		this.shiftTwoHandoverTime = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dayOfTheWeek == null) ? 0 : dayOfTheWeek.hashCode());
		result = prime * result + dayOfTheWeekNumber;
		result = prime * result + unitId;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UnitHours other = (UnitHours) obj;
		if (dayOfTheWeek == null) {
			if (other.dayOfTheWeek != null)
				return false;
		} else if (!dayOfTheWeek.equals(other.dayOfTheWeek))
			return false;
		if (dayOfTheWeekNumber != other.dayOfTheWeekNumber)
			return false;
		if (unitId != other.unitId)
			return false;
		return true;
	}

	
}
