package com.stpl.tech.master.domain.model;

import java.io.Serializable;
import java.util.Objects;

public class UnitProductKey implements Serializable {

    private static final long serialVersionUID = 3658145231822140481L;

    Integer unitId;
    Integer productId;
    String dimension;

    public UnitProductKey(Integer unitId, Integer productId, String dimension) {
        this.unitId = unitId;
        this.productId = productId;
        this.dimension = dimension;
    }

    public UnitProductKey() {
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnitProductKey that = (UnitProductKey) o;
        return Objects.equals(unitId, that.unitId) && Objects.equals(productId, that.productId) && Objects.equals(dimension, that.dimension);
    }

    @Override
    public int hashCode() {
        return Objects.hash(unitId, productId, dimension);
    }
}
