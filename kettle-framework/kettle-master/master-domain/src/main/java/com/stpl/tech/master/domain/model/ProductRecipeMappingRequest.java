package com.stpl.tech.master.domain.model;

import lombok.Data;

import java.util.List;

@Data
public class ProductRecipeMappingRequest {
    private String unitType;
    private Integer companyId;
    private Integer brandId;
    private List<Integer> productIds;
    private List<Integer> categoryIds;
    private List<Integer> dimensionIds;
    private List<String> regions;
    private List<Integer> cityIds;
    private Boolean isDimensionsSelected = true;
    private List<Integer> unitIds;
}
