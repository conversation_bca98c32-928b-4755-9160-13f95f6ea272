package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnitClosureStateDomain {
    private Long stateId;
    private String stateName;
    private String stateDescription;
    private String stateStatus;
    private String destinationSource;
    private String stateOwner;


}