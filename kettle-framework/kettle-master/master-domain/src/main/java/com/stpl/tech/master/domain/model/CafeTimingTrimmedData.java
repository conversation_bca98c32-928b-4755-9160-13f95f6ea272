package com.stpl.tech.master.domain.model;

import com.stpl.tech.master.domain.model.IdCodeName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CafeTimingTrimmedData implements Serializable {

    private boolean iscafe24x7;
    private Map<Integer, CafeTimings> dineInTimings;
}
