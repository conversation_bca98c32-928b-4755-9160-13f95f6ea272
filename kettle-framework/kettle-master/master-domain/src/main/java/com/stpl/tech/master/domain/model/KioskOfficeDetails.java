//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.09.23 at 01:37:36 PM IST
//


package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for KioskOfficeDetails complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="KioskOfficeDetails"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="officeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="officeName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="officeShortCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="officeContact" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="companyDetails" type="{http://www.w3schools.com}KioskCompanyDetails"/&gt;
 *         &lt;element name="paymentMode" type="{http://www.w3schools.com}KioskPaymentMode"/&gt;
 *         &lt;element name="region" type="{http://www.w3schools.com}String"/&gt;
 *         &lt;element name="tin" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="officeAddress" type="{http://www.w3schools.com}Address"/&gt;
 *         &lt;element name="officeStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="locationList" type="{http://www.w3schools.com}KioskLocationDetails" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "KioskOfficeDetails", propOrder = {
    "officeId",
    "officeName",
    "officeShortCode",
    "officeContact",
    "companyDetails",
    "paymentMode",
    "region",
    "tin",
    "officeAddress",
    "officeStatus",
    "locationList"
})
public class KioskOfficeDetails implements Serializable{

    private static final long serialVersionUID = 6760213651059113927L;

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer officeId;
    @XmlElement(required = true)
    protected String officeName;
    @XmlElement(required = true)
    protected String officeShortCode;
    @XmlElement(required = true)
    protected IdCodeName officeContact;
    @XmlElement(required = true)
    protected KioskCompanyDetails companyDetails;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected KioskPaymentMode paymentMode;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected String region;
    @XmlElement(required = true)
    protected String tin;
    @XmlElement(required = true)
    protected Address officeAddress;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus officeStatus;
    @XmlElement(required = true)
    protected List<KioskLocationDetails> locationList;

    /**
     * Gets the value of the officeId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getOfficeId() {
        return officeId;
    }

    /**
     * Sets the value of the officeId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setOfficeId(Integer value) {
        this.officeId = value;
    }

    /**
     * Gets the value of the officeName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOfficeName() {
        return officeName;
    }

    /**
     * Sets the value of the officeName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOfficeName(String value) {
        this.officeName = value;
    }

    /**
     * Gets the value of the officeShortCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOfficeShortCode() {
        return officeShortCode;
    }

    /**
     * Sets the value of the officeShortCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOfficeShortCode(String value) {
        this.officeShortCode = value;
    }

    /**
     * Gets the value of the officeContact property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getOfficeContact() {
        return officeContact;
    }

    /**
     * Sets the value of the officeContact property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setOfficeContact(IdCodeName value) {
        this.officeContact = value;
    }

    /**
     * Gets the value of the companyDetails property.
     *
     * @return
     *     possible object is
     *     {@link KioskCompanyDetails }
     *
     */
    public KioskCompanyDetails getCompanyDetails() {
        return companyDetails;
    }

    /**
     * Sets the value of the companyDetails property.
     *
     * @param value
     *     allowed object is
     *     {@link KioskCompanyDetails }
     *
     */
    public void setCompanyDetails(KioskCompanyDetails value) {
        this.companyDetails = value;
    }

    /**
     * Gets the value of the paymentMode property.
     *
     * @return
     *     possible object is
     *     {@link KioskPaymentMode }
     *
     */
    public KioskPaymentMode getPaymentMode() {
        return paymentMode;
    }

    /**
     * Sets the value of the paymentMode property.
     *
     * @param value
     *     allowed object is
     *     {@link KioskPaymentMode }
     *
     */
    public void setPaymentMode(KioskPaymentMode value) {
        this.paymentMode = value;
    }

    /**
     * Gets the value of the region property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getRegion() {
        return region;
    }

    /**
     * Sets the value of the region property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setRegion(String value) {
        this.region = value;
    }

    /**
     * Gets the value of the tin property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getTin() {
        return tin;
    }

    /**
     * Sets the value of the tin property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setTin(String value) {
        this.tin = value;
    }

    /**
     * Gets the value of the officeAddress property.
     *
     * @return
     *     possible object is
     *     {@link Address }
     *
     */
    public Address getOfficeAddress() {
        return officeAddress;
    }

    /**
     * Sets the value of the officeAddress property.
     *
     * @param value
     *     allowed object is
     *     {@link Address }
     *
     */
    public void setOfficeAddress(Address value) {
        this.officeAddress = value;
    }

    /**
     * Gets the value of the officeStatus property.
     *
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *
     */
    public SwitchStatus getOfficeStatus() {
        return officeStatus;
    }

    /**
     * Sets the value of the officeStatus property.
     *
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *
     */
    public void setOfficeStatus(SwitchStatus value) {
        this.officeStatus = value;
    }

    /**
     * Gets the value of the locationList property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the locationList property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getLocationList().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link KioskLocationDetails }
     *
     *
     */
    public List<KioskLocationDetails> getLocationList() {
        if (locationList == null) {
            locationList = new ArrayList<KioskLocationDetails>();
        }
        return this.locationList;
    }

}
