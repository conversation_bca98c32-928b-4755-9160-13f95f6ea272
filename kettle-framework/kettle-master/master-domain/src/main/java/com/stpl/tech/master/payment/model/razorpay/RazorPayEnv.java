package com.stpl.tech.master.payment.model.razorpay;

import com.stpl.tech.master.payment.model.PaymentModeValidation;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EnvType;

import java.util.Objects;

public enum RazorPayEnv implements PaymentModeValidation {

	//DEV("rzp_test_jKfnQU1Dj5XhuW", "pYXbTzZzKcb0BKHTFObb4ZjX"),
    DEV_CHAAYOS("rzp_test_75dNZ49tWJvann","sOBMFceEfKtPw3HI10rO8FAH"),
    PROD_CHAAYOS("***********************", "KElkbA5snbTOlA5OcCWowhpu"),
	DEV_DOHFUL("rzp_test_aKG0mx8x5qsQnK","h46y5WGYSlFll4N13Bsnb1TP"),
	PROD_DOHFUL("***********************", "4jCPBiZqi5LbFIebApLxrGVE");

	private final String key;
	private final String authKey;

	private RazorPayEnv(String key, String authKey) {
		this.key = key;
		this.authKey = authKey;
	}

	public String getKey() {
		return key;
	}

	@Override
	public boolean validate(String authKey) {
		return this.authKey.equals(authKey);
	}

	public static RazorPayEnv get(EnvType env, Integer brandId) {
		if (Objects.nonNull(brandId) && brandId.equals(AppConstants.DOHFUL_BRAND_ID)) {
			switch (env) {
				case DEV:
				case STAGE:
					return DEV_DOHFUL;
				case PROD:
				case SPROD:
					return PROD_DOHFUL;
			}
		}
		switch (env) {
			case DEV:
			case STAGE:
				return DEV_CHAAYOS;
			case PROD:
			case SPROD:
				return PROD_CHAAYOS;
		}
		return null;
	}

	@Override
	public String getAuthKey() {
		return authKey;
	}

}