package com.stpl.tech.master.domain.model;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ShopifyAddress implements Serializable {

    @Id
    private String _id;

    private String name;

    @SerializedName("address1")
    private String line1;

    @SerializedName("address2")
    private String line2;

    private String city;

    @SerializedName("province")
    private String state;

    @SerializedName("zip")
    private String zipCode;


    private String country;

    @SerializedName("phone")
    private String contact1;

    private String company;
    @SerializedName("default")
    private Boolean preferredAddress;

    private String addressType="Shopify";

//    @SerializedName("city")
    private String locality="NA";

    @SerializedName("id")
    private String sourceId;

    private String source="SHOPIFY";

}
