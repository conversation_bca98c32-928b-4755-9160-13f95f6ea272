package com.stpl.tech.master.domain.model;

import java.util.Date;

public class DeliveryCouponAllocationDetail {

    private Integer allocationId;
    private Integer deliveryCouponId;
    private Integer customerId;
    private Integer contactNumber;
    private Integer campaignId;
    private Date allotmentTime;
    private Integer allotmentOrderId;

    public Integer getAllocationId() {
        return allocationId;
    }

    public void setAllocationId(Integer allocationId) {
        this.allocationId = allocationId;
    }

    public Integer getDeliveryCouponId() {
        return deliveryCouponId;
    }

    public void setDeliveryCouponId(Integer deliveryCouponId) {
        this.deliveryCouponId = deliveryCouponId;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Integer getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(Integer contactNumber) {
        this.contactNumber = contactNumber;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public Date getAllotmentTime() {
        return allotmentTime;
    }

    public void setAllotmentTime(Date allotmentTime) {
        this.allotmentTime = allotmentTime;
    }

    public Integer getAllotmentOrderId() {
        return allotmentOrderId;
    }

    public void setAllotmentOrderId(Integer allotmentOrderId) {
        this.allotmentOrderId = allotmentOrderId;
    }

    @Override
    public String toString() {
        return "DeliveryCouponAllocationDetail{" +
                "allocationId=" + allocationId +
                ", deliveryCouponId=" + deliveryCouponId +
                ", customerId=" + customerId +
                ", contactNumber=" + contactNumber +
                ", campaignId=" + campaignId +
                ", allotmentTime=" + allotmentTime +
                ", allotmentOrderId=" + allotmentOrderId +
                '}';
    }
}
