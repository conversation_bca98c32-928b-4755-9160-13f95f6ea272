/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.19 at 12:10:26 PM IST 
//

package com.stpl.tech.master.domain.model;


import java.util.List;

public class DispenserRecipeRequest {

    private IdName product;

    private IdCodeName dimension;

    private String profile;
    private List<DispenserTagAndRevolution> variants;
    private List<DispenserTagAndRevolution> addOns;
    private List<DispenserTagAndRevolution> mandatoryAddons;


    public IdName getProduct() {
        return product;
    }

    public void setProduct(IdName product) {
        this.product = product;
    }

    public IdCodeName getDimension() {
        return dimension;
    }

    public void setDimension(IdCodeName dimension) {
        this.dimension = dimension;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public List<DispenserTagAndRevolution> getVariants() {
        return variants;
    }

    public void setVariants(List<DispenserTagAndRevolution> variants) {
        this.variants = variants;
    }

    public List<DispenserTagAndRevolution> getAddOns() {
        return addOns;
    }

    public void setAddOns(List<DispenserTagAndRevolution> addOns) {
        this.addOns = addOns;
    }

    public List<DispenserTagAndRevolution> getMandatoryAddons() {
        return mandatoryAddons;
    }

    public void setMandatoryAddons(List<DispenserTagAndRevolution> mandatoryAddons) {
        this.mandatoryAddons = mandatoryAddons;
    }
}
