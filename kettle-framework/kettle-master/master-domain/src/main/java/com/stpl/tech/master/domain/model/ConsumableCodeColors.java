package com.stpl.tech.master.domain.model;

public enum ConsumableCodeColors {
    WH<PERSON>E("White"),
    YELLOW("Yellow"),
    GREEN("Green"),
    BLUE("Blue"),
    PINK("Pink"),
    ORANGE("Orange"),
    NO_COLOR("No Color"),
    BROWN_GUR("Brown Gur"),
    BROWN("<PERSON>"),
    HONEY("Honey");

    private final String name;

    ConsumableCodeColors(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
