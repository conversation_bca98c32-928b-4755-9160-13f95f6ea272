package com.stpl.tech.master.domain.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

import java.io.Serializable;
import java.math.BigDecimal;

@ExcelSheet(value = "Product Price Sheet Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class UnitProductPriceSheetDetail implements Serializable {

    private static final long serialVersionUID = 2295422929550338128L;
    @ExcelField
    private Integer unitId;
    @ExcelField
    private String unitName;
    @ExcelField
    private Integer productId;
    @ExcelField
    private String productName;
    @ExcelField
    private Integer dimensionCode;
    @ExcelField
    private String dimension;
    @ExcelField
    private String unitCategory;
    @ExcelField
    private Integer pricingProfile;
    @ExcelField
    private Integer brandId;
    @ExcelField
    private String unitRegion;
    @ExcelField
    private String rtlCode;
    @ExcelField
    private String rlCode;
    @ExcelField
    private String productStatus;
    @ExcelField
    private Integer unitProductMappingId;
    @ExcelField
    private String unitProductMappingStatus;
    @ExcelField
    private Integer unitProductPriceId;
    @ExcelField
    private String unitProductPricingStatus;
    @ExcelField
    private BigDecimal price;
    @ExcelField
    private BigDecimal newPrice;

    public UnitProductPriceSheetDetail() {
    }

    public UnitProductPriceSheetDetail(Integer unitId, String unitName, Integer productId, String productName, Integer dimensionCode, String dimension,
                                       String unitCategory, Integer pricingProfile, Integer brandId, String unitRegion, String rtlCode, String rlCode, String productStatus, Integer unitProductMappingId,
                                       String unitProductMappingStatus, Integer unitProductPriceId, String unitProductPricingStatus, BigDecimal price) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.unitCategory = unitCategory;
        this.pricingProfile = pricingProfile;
        this.brandId = brandId;
        this.unitRegion = unitRegion;
        this.productId = productId;
        this.rtlCode = rtlCode;
        this.rlCode = rlCode;
        this.productName = productName;
        this.productStatus = productStatus;
        this.unitProductMappingId = unitProductMappingId;
        this.unitProductMappingStatus = unitProductMappingStatus;
        this.dimensionCode = dimensionCode;
        this.unitProductPriceId = unitProductPriceId;
        this.dimension = dimension;
        this.unitProductPricingStatus = unitProductPricingStatus;
        this.price = price;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getDimensionCode() {
        return dimensionCode;
    }

    public void setDimensionCode(Integer dimensionCode) {
        this.dimensionCode = dimensionCode;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getUnitCategory() {
        return unitCategory;
    }

    public void setUnitCategory(String unitCategory) {
        this.unitCategory = unitCategory;
    }

    public Integer getPricingProfile() {
        return pricingProfile;
    }

    public void setPricingProfile(Integer pricingProfile) {
        this.pricingProfile = pricingProfile;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getUnitRegion() {
        return unitRegion;
    }

    public void setUnitRegion(String unitRegion) {
        this.unitRegion = unitRegion;
    }

    public String getRtlCode() {
        return rtlCode;
    }

    public void setRtlCode(String rtlCode) {
        this.rtlCode = rtlCode;
    }

    public String getRlCode() {
        return rlCode;
    }

    public void setRlCode(String rlCode) {
        this.rlCode = rlCode;
    }

    public String getProductStatus() {
        return productStatus;
    }

    public void setProductStatus(String productStatus) {
        this.productStatus = productStatus;
    }

    public Integer getUnitProductMappingId() {
        return unitProductMappingId;
    }

    public void setUnitProductMappingId(Integer unitProductMappingId) {
        this.unitProductMappingId = unitProductMappingId;
    }

    public String getUnitProductMappingStatus() {
        return unitProductMappingStatus;
    }

    public void setUnitProductMappingStatus(String unitProductMappingStatus) {
        this.unitProductMappingStatus = unitProductMappingStatus;
    }

    public Integer getUnitProductPriceId() {
        return unitProductPriceId;
    }

    public void setUnitProductPriceId(Integer unitProductPriceId) {
        this.unitProductPriceId = unitProductPriceId;
    }

    public String getUnitProductPricingStatus() {
        return unitProductPricingStatus;
    }

    public void setUnitProductPricingStatus(String unitProductPricingStatus) {
        this.unitProductPricingStatus = unitProductPricingStatus;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(BigDecimal newPrice) {
        this.newPrice = newPrice;
    }

    @Override
    public String toString() {
        return "UnitProductPriceSheetDetail{" +
                ", unitId=" + unitId +
                ", unitName='" + unitName + '\'' +
                ", productId=" + productId +
                ", productName='" + productName + '\'' +
                ", dimensionCode=" + dimensionCode +
                ", dimension='" + dimension + '\'' +
                ", unitCategory='" + unitCategory + '\'' +
                ", pricingProfile=" + pricingProfile +
                ", brandId=" + brandId +
                ", unitRegion='" + unitRegion + '\'' +
                ", rtlCode='" + rtlCode + '\'' +
                ", rlCode='" + rlCode + '\'' +
                ", productStatus='" + productStatus + '\'' +
                ", unitProductMappingId=" + unitProductMappingId +
                ", unitProductMappingStatus='" + unitProductMappingStatus + '\'' +
                ", unitProductPriceId=" + unitProductPriceId +
                ", unitProductPricingStatus='" + unitProductPricingStatus + '\'' +
                ", price=" + price +
                ", newPrice=" + newPrice +
                '}';
    }
}
