/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.09 at 06:52:19 PM IST 
//

package com.stpl.tech.master.domain.model;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each Java content interface and Java
 * element interface generated in the com.stpl.tech.kettle.domain.model package.
 * <p>
 * An ObjectFactory allows you to programatically construct new instances of the
 * Java representation for XML content. The Java representation of XML content
 * can consist of schema derived interfaces and classes representing the binding
 * of schema type definitions, element declarations and model groups. Factory
 * methods for each of these are provided in this class.
 */
@XmlRegistry
public class MasterObjectFactory {

    /**
     * Create a new ObjectFactory that can be used to create new instances of
     * schema derived classes for package: com.stpl.tech.kettle.domain.model
     */
    public MasterObjectFactory() {
    }

    /**
     * Create an instance of {@link Unit }
     */
    public Unit createUnit() {
        return new Unit();
    }

    /**
     * Create an instance of {@link Division }
     */
    public Division createDivision() {
        return new Division();
    }

    /**
     * Create an instance of {@link Address }
     */
    public Address createAddress() {
        return new Address();
    }

    /**
     * Create an instance of {@link Product }
     */
    public Product createProduct() {
        return new Product();
    }

    /**
     * Create an instance of {@link TaxProfile }
     */
    public TaxProfile createTaxProfile() {
        return new TaxProfile();
    }

    /**
     * Create an instance of {@link TransactionMetadata }
     */
    public TransactionMetadata createTransactionMetadata() {
        return new TransactionMetadata();
    }

    /**
     * Create an instance of {@link ListData }
     */
    public ListData createListData() {
        return new ListData();
    }

    /**
     * Create an instance of {@link PaymentMode }
     */
    public PaymentMode createPaymentMode() {
        return new PaymentMode();
    }

    /**
     * Create an instance of {@link IdCodeName }
     */
    public IdCodeName createIdCodeName() {
        return new IdCodeName();
    }

    /**
     * Create an instance of {@link OfferDetail }
     */
    public OfferDetail createOfferDetail() {
        return new OfferDetail();
    }

    /**
     * Create an instance of {@link CouponDetail }
     */
    public CouponDetail createCouponDetail() {
        return new CouponDetail();
    }

    /**
     * Create an instance of {@link CouponMapping }
     */
    public CouponMapping createCouponMapping() {
        return new CouponMapping();
    }

    /**
     * Create an instance of {@link Employee }
     */
    public Employee createEmployee() {
        return new Employee();
    }

    /**
     * Create an instance of {@link Department }
     */
    public Department createDepartment() {
        return new Department();
    }

    /**
     * Create an instance of {@link Designation }
     */
    public Designation createDesignation() {
        return new Designation();
    }

    /**
     * Create an instance of {@link Company }
     */
    public Company createCompany() {
        return new Company();
    }

    /**
     * Create an instance of {@link ProductPrice }
     */
    public ProductPrice createProductPrice() {
        return new ProductPrice();
    }

    /**
     * Create an instance of {@link ProductBasicDetail }
     */
    public ProductBasicDetail createProductBasicDetail() {
        return new ProductBasicDetail();
    }

    /**
     * Create an instance of {@link UnitBasicDetail }
     */
    public UnitBasicDetail createUnitBasicDetail() {
        return new UnitBasicDetail();
    }

    /**
     * Create an instance of {@link UnitProductMappingData }
     */
    public UnitProductMappingData createUnitProductMappingData() {
        return new UnitProductMappingData();
    }


    /**
     * Create an instance of {@link PartnerDetail }
     */
    public PartnerDetail createPartnerDetail() {
        return new PartnerDetail();
    }

    /**
     * Create an instance of {@link AddonList }
     */
    public AddonList createAddonList() {
        return new AddonList();
    }

    /**
     * Create an instance of {@link AddonData }
     */
    public AddonData createAddonData() {
        return new AddonData();
    }

    public ChannelPartnerDetail createChannelPartnerDetail() {
        return new ChannelPartnerDetail();
    }

    public Brand createBrand() {
        return new Brand();
    }

    public UnitPartnerBrandKey createUnitPartnerData() {
        return new UnitPartnerBrandKey();
    }

    public UnitPartnerBrandRestaurantKey createUnitPartnerBrandRestaurantData(){
        return new UnitPartnerBrandRestaurantKey();
    }

    public RestaurantPartnerKey createRestaurantPartnerKey() { return new RestaurantPartnerKey(); }

    public EntityAliasKey createEntityAlias() {
        return new EntityAliasKey();
    }

    public ProductImageMappingDetail createProductImageMappingDetail() {
        return new ProductImageMappingDetail();
    }

    public BannerDetail createBannerDetail() {
        return new BannerDetail();
    }
}
