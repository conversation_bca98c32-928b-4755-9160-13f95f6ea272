package com.stpl.tech.master.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MenuExcelUploadResponse {

    String response;

    List<String> errorsList;

    Map<String,Map<String,Map<String,Set<String>>>> changeLog;

    List<String> parsedSheets;
}
