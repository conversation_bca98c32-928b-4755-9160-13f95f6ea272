package com.stpl.tech.master.domain.model;

import java.util.ArrayList;
import java.util.List;

public enum AppOfferType {

    GENERIC_OFFER,
    CUSTOMER_OFFER;

    public String value() {
        return name();
    }

    public static AppOfferType fromValue(String v) {
        return valueOf(v);
    }

    public static List<String> getAppOfferTypes() {
        List<String> appOfferTypes = new ArrayList<>();
        for(AppOfferType type : AppOfferType.values()) {
            appOfferTypes.add(type.value());
        }
        return appOfferTypes;
    }
}