package com.stpl.tech.master.domain.model;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@ExcelSheet(value = "ProductCheckList")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class ProductCheckListExcelSheet {

    private static final Logger LOG = LoggerFactory.getLogger(ProductCheckListExcelSheet.class);

    @ExcelField
    public String productId;

    @ExcelField
    public String productName;

    @ExcelField
    public String dimension;

    @ExcelField
    public String unitName;

    @ExcelField
    public String brandId;

    @ExcelField
    public String liveDate;

    @ExcelField(headerName = "OVERALL_MAPPING_STATUS_COD")
    public String overallMappingStatusForCOD;

    @ExcelField(headerName = "OVERALL_MAPPING_STATUS_DINE_IN")
    public String overallMappingStatusForDineIn;

    @ExcelField
    public String productStatus;

    @ExcelField
    public String unitProductMapping = AppConstants.NO;

    @ExcelField(headerName = "CAFE_PRODUCT_RECIPE_MAPPING")
    public String unitProductPricing = AppConstants.NO;

    @ExcelField
    public String codProductMappingZomato = AppConstants.NO;

    @ExcelField
    public String codProductMappingSwiggy = AppConstants.NO;

    @ExcelField
    public String codProductMappingMagicPin = AppConstants.NO;

    @ExcelField(headerName = "ZOMATO_PRODUCT_RECIPE_MAPPING")
    public String codProductPriceMappingZomato = AppConstants.NO;

    @ExcelField(headerName = "SWIGGY_PRODUCT_RECIPE_MAPPING")
    public String codProductPriceMappingSwiggy = AppConstants.NO;

    @ExcelField(headerName = "MAGICPIN_PRODUCT_RECIPE_MAPPING")
    public String codProductPriceMappingMagicPin = AppConstants.NO;

    @ExcelField
    public String menuMappingStatusChaayosDineIn;

    @ExcelField
    public String menuMappingReasonChaayosDineIn;

    @ExcelField
    public String menuMappingStatusDineInApp;

    @ExcelField
    public String menuMappingReasonDineInApp;

    @ExcelField
    public String menuMappingStatusZomato;

    @ExcelField
    public String menuMappingReasonZomato;

    @ExcelField
    public String menuMappingStatusSwiggy;

    @ExcelField
    public String menuMappingReasonSwiggy;

    @ExcelField
    public String menuMappingStatusMagicPin;

    @ExcelField
    public String menuMappingReasonMagicPin;

    @ExcelField
    public String recipeLive;

    @ExcelField
    public String priceProfileMappingDineIn;

    @ExcelField
    public String priceProfileMappingZomato;

    @ExcelField
    public String priceProfileMappingSwiggy;

    @ExcelField
    public String priceProfileMappingMagicPin;

    @ExcelField
    public String hotBeverageMonkRecipe;

    @ExcelField
    public String imageMappingStatusDineIn;

    @ExcelField
    public String imageMappingReasonDineIn;

    @ExcelField(headerName = "IMAGE_MAPPING_STATUS_COD")
    public String imageMappingStatusCOD;

    @ExcelField(headerName = "IMAGE_MAPPING_REASON_COD")
    public String imageMappingReasonCOD;

    @ExcelField
    public String employeeMealMapping;


    @ExcelField
    public String inventoryTrackValid;

    @ExcelField
    public String inventoryTrackInValidReason;

    public void updateOverAllMappingStatus(List<String> mappingNames, String status) {
        try {
            Field[] fields = this.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (mappingNames.contains(field.getName()) && field.getType().equals(String.class)) {
                    field.setAccessible(Boolean.TRUE);
                    Object value = field.get(this);
                    if (AppConstants.NO.equals((String) value)) {
                        if ("overallMappingStatusForCOD".equals(status)) {
                            this.overallMappingStatusForCOD = AppConstants.NO;
                        } else if ("overallMappingStatusForDineIn".equals(status)) {
                            this.overallMappingStatusForDineIn = AppConstants.NO;
                        }
                        return;
                    }
                }
            }
            if ("overallMappingStatusForCOD".equals(status)) {
                this.overallMappingStatusForCOD = AppConstants.YES;
            } else if ("overallMappingStatusForDineIn".equals(status)) {
                this.overallMappingStatusForDineIn = AppConstants.YES;
            }
        } catch (Exception e) {
            LOG.info("Error occured while updating Overall Mapping Status {}", e);
        }
    }

}
