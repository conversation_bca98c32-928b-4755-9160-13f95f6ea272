/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.04 at 12:22:00 PM IST 
//


package com.stpl.tech.master.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <p>Java class for ProductPrice complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ProductPrice"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dimension" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="price" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="cost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductPrice", propOrder = {
    "id",
    "dimension",
    "price",
    "cost",
    "recipe",
    "aliasProductName",
    "dimensionDescriptor"
})
public class ProductPrice implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = -7808048538719390946L;
	protected int id;
    @XmlElement(required = true)
    protected String dimension;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal price;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal cost;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal codCost;
    @XmlElement(required = true)
    protected RecipeDetail recipe;
    @XmlElement(required = true)
    protected Integer buffer;
    @XmlElement(required = true)
    protected Integer  threshold;
    @XmlElement(required = true)
    protected Boolean  customize;
    @XmlElement(required = true)
    protected String profile;
    @XmlElement(required = true)
    protected String status = "ACTIVE";
    @XmlElement(required = true)
    protected Integer recipeId;
    @XmlElement(required = true)
    protected String aliasProductName;
    @XmlElement(required = true)
    protected String dimensionDescriptor;
    @XmlElement(required = true)
    @JsonProperty("isDeliveryOnlyProduct")
    protected Boolean isDeliveryOnlyProduct;
    @XmlElement(required = true)
    @JsonProperty("pickDineInConsumables")
    protected Boolean pickDineInConsumables;
    @JsonDeserialize(using=BigDecimalDeserializer.class)
    protected BigDecimal currentPrice;
    protected String currentAliasProductName;
    protected String currentDimensionDescriptor;
    protected String currentProfile;
    protected String currentStatus;

    public ProductPrice() {}

    public ProductPrice(String status) {
        this.status = status;
    }

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the dimension property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDimension() {
        return dimension;
    }

    /**
     * Sets the value of the dimension property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDimension(String value) {
        this.dimension = value;
    }

    /**
     * Gets the value of the price property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * Sets the value of the price property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrice(BigDecimal value) {
        this.price = value;
    }

    /**
     * Gets the value of the cost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getCost() {
        return cost;
    }

    /**
     * Sets the value of the cost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCost(BigDecimal value) {
        this.cost = value;
    }

	public RecipeDetail getRecipe() {
		return recipe;
	}

	public void setRecipe(RecipeDetail recipe) {
		this.recipe = recipe;
	}

	public BigDecimal getCodCost() {
		return codCost;
	}

	public void setCodCost(BigDecimal codCost) {
		this.codCost = codCost;
	}

	public Integer getBuffer() {
		return buffer;
	}

	public void setBuffer(Integer buffer) {
		this.buffer = buffer;
	}

	public Integer getThreshold() {
		return threshold;
	}

	public void setThreshold(Integer threshold) {
		this.threshold = threshold;
	}

	public Boolean isCustomize() {
		return customize;
	}

	public void setCustomize(Boolean customize) {
		this.customize = customize;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

	public Integer getRecipeId() {
		return recipeId;
	}

	public void setRecipeId(Integer recipeId) {
		this.recipeId = recipeId;
	}

    public String getAliasProductName() {
        return aliasProductName;
    }

    public void setAliasProductName(String aliasProductName) {
        this.aliasProductName = aliasProductName;
    }

    public String getDimensionDescriptor() {
        return dimensionDescriptor;
    }

    public void setDimensionDescriptor(String dimensionDescriptor) {
        this.dimensionDescriptor = dimensionDescriptor;
    }

    public Boolean getIsDeliveryOnlyProduct() {
        return isDeliveryOnlyProduct;
    }

    public void setIsDeliveryOnlyProduct(Boolean isDeliveryOnlyProduct) {
        this.isDeliveryOnlyProduct = isDeliveryOnlyProduct;
    }

    public Boolean getPickDineInConsumables() {
        return pickDineInConsumables;
    }

    public void setPickDineInConsumables(Boolean pickDineInConsumables) {
        this.pickDineInConsumables = pickDineInConsumables;
    }

    public BigDecimal getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(BigDecimal currentPrice) {
        this.currentPrice = currentPrice;
    }

    public String getCurrentAliasProductName() {
        return currentAliasProductName;
    }

    public void setCurrentAliasProductName(String currentAliasProductName) {
        this.currentAliasProductName = currentAliasProductName;
    }

    public String getCurrentDimensionDescriptor() {
        return currentDimensionDescriptor;
    }

    public void setCurrentDimensionDescriptor(String currentDimensionDescriptor) {
        this.currentDimensionDescriptor = currentDimensionDescriptor;
    }

    public String getCurrentProfile() {
        return currentProfile;
    }

    public void setCurrentProfile(String currentProfile) {
        this.currentProfile = currentProfile;
    }

    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }
}
