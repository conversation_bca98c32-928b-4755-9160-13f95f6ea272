package com.stpl.tech.master.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UnitClosureStateEventDomain {
    private Long eventId;
    private Long requestId;
    private Long stateId;
    private String eventStatus;
    private Integer updatedBy;
    private Date lastUpdationTime;
    private UnitClosureStateDomain unitClosureStateDomain;

}