<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.stpl.tech</groupId>
		<artifactId>chaayos</artifactId>
		<version>6.2.41</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
    <groupId>com.stpl.tech.kettle</groupId>
    <artifactId>kettle-framework</artifactId>
    <packaging>pom</packaging>

    <name>kettle-framework</name>
    <url>http://maven.apache.org</url>

    <modules>
        <module>kettle-master</module>
        <module>kettle-transaction</module>
         <!--  <module>kettle-scripts</module>-->
        <module>kettle-crm</module>
<!--        <module>kettle-scm</module>-->
        <module>kettle-logging</module>
        <module>kettle-analytics</module>
        <!--<module>kettle-checklist</module>
        <module>kettle-clm</module>-->
<!--        <module>kettle-inventory</module>-->
        <module>kettle-dashboard</module>
        <module>truecaller-service</module>
<!--        <module>kettle-etl</module>-->
<!--        <module>channel-partner</module>-->
        <module>rekognition-service</module>
<!--        <module>inventory-ui</module>-->
    </modules>
</project>
