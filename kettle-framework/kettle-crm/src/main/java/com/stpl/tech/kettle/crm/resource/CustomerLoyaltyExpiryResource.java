package com.stpl.tech.kettle.crm.resource;

import com.stpl.tech.kettle.crm.model.LoyaltyExpiryBody;
import com.stpl.tech.kettle.crm.service.CustomerLoyaltyCLMService;
import com.stpl.tech.kettle.crm.service.CustomerLoyaltyExpiryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
public class CustomerLoyaltyExpiryResource {


    @Autowired
    CustomerLoyaltyExpiryService customerLoyaltyExpiryService;

    @Autowired
    CustomerLoyaltyCLMService customerLoyaltyCLMService;

    @PutMapping("/generate-historic-data")
    void generateHistoricData(@RequestParam("startDate") @DateTimeFormat(pattern = "dd-MM-yyyy HH:mm:ss") Date startbusinessDate,
                              @RequestParam("endDate") @DateTimeFormat(pattern = "dd-MM-yyyy HH:mm:ss") Date endbusinessDate
            , @RequestParam("batchSize") Integer batchSize, @RequestParam("customerIds") List<Integer> customerIds) throws Exception {
         customerLoyaltyExpiryService.updateCustomersLoyaltyExpiryData(startbusinessDate,endbusinessDate,batchSize,customerIds);
    }

    @PutMapping("/generate-historic-data-from-customer")
    void generateHistoricData(@RequestBody LoyaltyExpiryBody loyaltyExpiryBody) throws Exception {
        customerLoyaltyExpiryService.updateCustomersLoyaltyExpiryData(null,null,5000,loyaltyExpiryBody.getCustomersId());
    }

    @PutMapping("/fix-by-acquire-points")
    void fixByAcquirePoints(@RequestBody LoyaltyExpiryBody loyaltyExpiryBody) throws Exception {
        customerLoyaltyExpiryService.fixLoyaltyUsingAcquirePoints(loyaltyExpiryBody.getCustomersId());
    }

    @GetMapping("/expiry-loyalty")
    void expireLoyalty() throws Exception {
        customerLoyaltyExpiryService.expireLoyaltyPoints();
    }

    @PutMapping("/clm-top-up")
    void updateCustomerLoyaltyWithClmTopUp(@RequestParam("startDate") @DateTimeFormat(pattern = "dd-MM-yyyy HH:mm:ss") Date startbusinessDate,
                                           @RequestParam("endDate") @DateTimeFormat(pattern = "dd-MM-yyyy HH:mm:ss") Date endbusinessDate){
        customerLoyaltyCLMService.customerLoyaltyCLMTopUp(startbusinessDate,endbusinessDate);
    }

    @GetMapping("/push-expiry-event")
    void pushExpiryEventToCleverTap(){
        customerLoyaltyCLMService.pushExpiryEventsToCleverTapForThisMonths();
    }

    @PostMapping("/extend-loyalty")
    void extendLoyalty(@RequestParam("startDate") @DateTimeFormat(pattern = "dd-MM-yyyy HH:mm:ss") Date startbusinessDate,
                       @RequestParam("endDate") @DateTimeFormat(pattern = "dd-MM-yyyy HH:mm:ss") Date endbusinessDate,
                       @RequestParam Integer batchSize){
        customerLoyaltyExpiryService.extendExpiryLoyaltyPoints(batchSize,startbusinessDate,endbusinessDate);
    }




}
