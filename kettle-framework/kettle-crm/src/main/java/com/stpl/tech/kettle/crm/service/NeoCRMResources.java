/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import com.google.gson.Gson;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.crm.model.CustomerAddress;
import com.stpl.tech.kettle.crm.model.CustomerCurrentAddresses;
import com.stpl.tech.kettle.crm.model.CustomerLoginInfo;
import com.stpl.tech.kettle.crm.model.NeoCustomerResponse;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.TokenServiceImpl;
import com.stpl.tech.master.core.external.interceptor.ExternalAPITokenCache;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.master.core.external.notification.service.ExternalUserManagementService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIToken;
import com.stpl.tech.master.core.external.partner.service.impl.PartnerRequest;
import com.stpl.tech.master.core.external.partner.service.impl.ZeonReqestData;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import io.jsonwebtoken.SignatureException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.jms.JMSException;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.NEO_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + NEO_SERVICES_ROOT_CONTEXT) // v1/neo
public class NeoCRMResources extends NotificationResource {

    private static final Logger LOG = LoggerFactory.getLogger(NeoCRMResources.class);

    @Autowired
    private ExternalUserManagementService externalUserService;

    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    @Autowired
    protected CustomerService customerService;
    @Autowired
    protected AuthorizationService authorizationDao;
    @Autowired
    protected EnvironmentProperties environment;
    @Autowired
    protected TokenService<FeedbackTokenInfo> tokenService;
    @Autowired
    private NotificationService notificationService;
    @Autowired(required = false)
    private TokenService<PartnerRequest> jwtService;
    @Autowired
    private ExternalAPITokenCache externalAPICache;

    @Autowired
    private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

    @RequestMapping(method = RequestMethod.POST, value = "customer/lookup", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Customer lookup(@RequestBody String contact) throws DataUpdationException {
        contact = AppUtils.getValidContactNUmber(contact);
        LOG.info("Got lookup request from neo-service with contact number call = customer/lookup: {}" ,contact);
        Customer customer = customerService.getCustomer(contact);
        if (customer != null && customer.isInternal()) {
            throw new DataUpdationException(
                "Customer With Contact Number " + customer.getContactNumber() + " is an Internal Employee");
        }
        return customer;
    }

    @RequestMapping(method = RequestMethod.POST, value = "old-customer/lookup", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean oldCustomerLookUp(@RequestBody String contact) throws DataUpdationException {
        contact = AppUtils.getValidContactNUmber(contact);
        LOG.info("Got lookup request from neo-service with contact number call = old-customer/lookup: {}", contact);
        return customerService.isCustomerOld(contact, 2021);
    }

    @RequestMapping(method = RequestMethod.POST, value = "has-orders/cafe", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean hasOrdersForCafe(@RequestBody String contact) throws DataUpdationException {
        contact = AppUtils.getValidContactNUmber(contact);
        LOG.info("Got has-orders/cafe request from neo-service with contactNumber" + contact);
        return customerService.hasOrdersForOrderSource(contact, "CAFE");
    }

    @RequestMapping(method = RequestMethod.POST, value = "has-orders/delivery", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean hasOrdersForDelivery(@RequestBody String contact) throws DataUpdationException {
        contact = AppUtils.getValidContactNUmber(contact);
        LOG.info("Got has-orders/delivery request from neo-service with contactNumber" + contact);
        return customerService.hasOrdersForOrderSource(contact, "COD");
    }

    @RequestMapping(method = RequestMethod.POST, value = "customer/lookup/app", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Customer lookupForApp(@RequestBody String contact, @RequestParam(required = false) Boolean verifyInternal,
                                 @RequestParam(required = false) Integer brandId) throws DataUpdationException, JMSException {
        LOG.info("Got lookup request from neo-service with contact number call = customer/lookup/app: {} , and for brandId :: {}", contact, brandId);
        if(Objects.nonNull(brandId)){
            contact = AppUtils.getValidContactNumberForBrand(contact,customerService.getBrandContactCode(brandId));
        }else {
            contact = AppUtils.getValidContactNUmber(contact);
        }
        Customer customer = customerService.getCustomer(contact);
//        removed lookup whatsapp update call for dine in customer
//        if(Objects.nonNull(customer) && Objects.isNull(customer.getOptWhatsapp())){
//            LOG.info("Enabling Whatsapp notification for customer :: {}",contact);
//            customer.setOptWhatsapp(AppConstants.YES);
//            customerService.updateCustomer(customer);
//            sendWhatsappOptInRequest(customer);
//        }
        if (verifyInternal != null && verifyInternal && customer != null && customer.isInternal()) {
            throw new DataUpdationException(
                "Customer With Contact Number " + customer.getContactNumber() + " is an Internal Employee");
        }
        return customer;
    }

    private void sendWhatsappOptInRequest(Customer customerResponse) throws JMSException {
        NotificationPayload payload = new NotificationPayload();
        payload.setCustomerId(customerResponse.getId());
        payload.setOrderId(0);
        payload.setMessageType(AppConstants.WA_OPT_IN);
        payload.setContactNumber(customerResponse.getContactNumber());
        payload.setRequestTime(AppUtils.getCurrentTimestamp());
        Map<String,String> map = new HashMap<>();
        map.put("firstName",customerResponse.getFirstName());
        map.put("sourceDynamicText",customerResponse.getAcquisitionSource());
        payload.setPayload(map);
        customerCommunicationEventPublisher.publishCustomerCommunicationEvent(getEnvironmentProperties().getEnvironmentType().name(),payload);
    }

    @RequestMapping(method = RequestMethod.POST, value = "customer/signup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Customer signup(HttpServletRequest httpServletRequest,@RequestBody Customer request) throws DataUpdationException {
        LOG.info("Got request from neo-service to signup customer with contact number", request.getContactNumber());
        String contactNumber = AppUtils.getValidContactNUmber(request.getContactNumber());
        Customer customer = customerService.getCustomer(contactNumber);
        if (customer != null && customer.isInternal()) {
            throw new DataUpdationException(
                "Customer With Contact Number " + customer.getContactNumber() + " is an Internal Employee");
        }
        return signup(request, customer, ApplicationName.NEO_SERVICE,getBrandIdFromHeaders(httpServletRequest));
    }

    @RequestMapping(method = RequestMethod.POST, value = "customer/signup/app", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Customer signupForApp(@RequestBody final Customer request,
                                 @RequestParam(required = false) Boolean verifyInternal, @RequestParam(required = false) Boolean viaTruecaller,
                                 @RequestParam(required = false) Integer brandId) throws DataUpdationException {
        LOG.info("Got request from neo-service to signup customer with contact number", request.getContactNumber());
        String contactNumber = (Objects.isNull(brandId)) ? AppUtils.getValidContactNUmber(request.getContactNumber()) :
                AppUtils.getValidContactNumberForBrand(request.getContactNumber(),customerService.getBrandContactCode(brandId));
        Customer customer = customerService.getCustomer(contactNumber);
        if (verifyInternal != null && verifyInternal && customer != null && customer.isInternal()) {
            throw new DataUpdationException(
                "Customer With Contact Number " + customer.getContactNumber() + " is an Internal Employee");
        }
        return signup(request, customer, ApplicationName.DINE_IN, brandId);
    }

    private Customer signup(@RequestBody Customer request, Customer customer, ApplicationName appName, Integer brandId) throws DataUpdationException {
        if (customer == null) {
            customer = getCustomerObject(request, appName);
            LOG.info(" CUSTOMER BRANCH CHECK customer/signup -------- " + customer.getAcquisitionBrandId()
                + customer.isChaayosCustomer());
            if(Objects.isNull(customer.getOptWhatsapp()) ){
                customer.setOptWhatsapp(AppConstants.YES);
            }else if(Objects.nonNull(customer.getOptWhatsapp()) ){
                customer.setOptWhatsapp(customer.getOptWhatsapp());
            }
            customer = customerService.addCustomerUnchecked(customer);
        } else {
            if (!customer.isContactNumberVerified()) {
                customerService.verifyContactNumber(customer.getContactNumber());
                customer.setContactNumberVerified(true);
            }
            if (request.getEmailId() != null) {
                customer.setEmailId(request.getEmailId());
                customer.setEmailVerified(false);
            }
            if (request.getFirstName() != null) {
                customer.setFirstName(request.getFirstName());
            }
            if (request.getSignUpRefCode() != null) {
                customer.setSignUpRefCode(request.getSignUpRefCode());
            }
            if (request.getGender() != null) {
                customer.setGender(request.getGender());
            }
            if (request.getDateOfBirth() != null) {
                customer.setDateOfBirth(request.getDateOfBirth());
            }
            if (request.getAnniversary() != null) {
                customer.setAnniversary(request.getAnniversary());
            }
            if(Objects.isNull(customer.getOptWhatsapp()) && appName.equals(AppConstants.DINE_IN)){
                customer.setOptWhatsapp(AppConstants.YES);
            }else if(Objects.nonNull(customer.getOptWhatsapp()) && appName.equals(AppConstants.DINE_IN)){
                customer.setOptWhatsapp(customer.getOptWhatsapp());
            }
            customerService.updateBasicCustomerInfo(customer);
            return customer;
        }
        if (customer.getFirstName() != null && !customer.getFirstName().trim().equals("") && (Objects.isNull(brandId) || AppConstants.CHAAYOS_BRAND_ID == brandId)) {
            sendWelcomeMessage(customer);
        }
        return customer;
    }

    @RequestMapping(method = RequestMethod.POST, value = "customer/update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean update(@RequestBody final Customer request) throws DataUpdationException {
        LOG.info("Got update request from neo-service with contact number: {}", request.getContactNumber());
        if (request.getContactNumber() != null) {
            // TODO this is making 2 calls make it 1
            String contactNumber = AppUtils.getValidContactNUmber(request.getContactNumber());
            Customer customer = customerService.getCustomer(contactNumber);
            customer.setFirstName(request.getFirstName());
            customer.setEmailId(request.getEmailId());
            return customerService.updateBasicCustomerInfo(customer);
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "customer/update/app", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void syncDineInAppCustomerProfile(@RequestBody Customer request, @RequestParam(required = false) Integer brandId) throws DataUpdationException {
        LOG.info("Got request from dine in app to update customer profile {}", new Gson().toJson(request));
        String contactNumber = (Objects.isNull(brandId)) ? AppUtils.getValidContactNUmber(request.getContactNumber()) :
                AppUtils.getValidContactNumberForBrand(request.getContactNumber(),customerService.getBrandContactCode(brandId));
        request.setContactNumber(contactNumber);
        customerService.syncCustomerProfileDineInApp(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "otp/generate", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean generateOtp(@RequestBody String contact, @RequestParam(name = "chp", required = false) Integer partnerId,
                               @RequestParam(required = false) Integer brandId)
        throws DataUpdationException, DuplicateRequestException {
        LOG.info("Request from neo-service to generate otp for customer with contact number: {}", contact);
        if(Objects.nonNull(brandId)){
            contact = AppUtils.getValidContactNumberForBrand(contact, customerService.getBrandContactCode(brandId));
            return sendCustomerAuthorizationOTPBrandWise(environment.getSendOTPLastFourDigits(), contact, partnerId,brandId);
        }
        else {
            contact = AppUtils.getValidContactNUmber(contact);
            return sendCustomerAuthorizationOTP(environment.getSendOTPLastFourDigits(), contact, partnerId);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "async/success/message", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Deprecated
    public boolean sendAsyncOrderSuccessMessage(@RequestParam String contact, @RequestParam String customerName)
        throws DataUpdationException, DuplicateRequestException {
        LOG.info("Request from neo-service to generate otp for customer with contact number: {}", contact);
//        return sendAsyncSuccessMessage(customerName, contact);
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "async/failure/message", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean sendAsyncOrderFailureMessage(@RequestParam String contact,
                                                @RequestParam final String customerName) throws DataUpdationException, DuplicateRequestException {
        LOG.info("Request from neo-service to generate otp for customer with contact number: {}", contact);
        contact = AppUtils.getValidContactNUmber(contact);
        return sendAsyncFailureMessage(customerName, contact);
    }

    @RequestMapping(method = RequestMethod.POST, value = "otp/verify", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean verifyOTP(@RequestBody CustomerResponse customerResponse) throws DataUpdationException {
        LOG.info("Request from neo-service to verify OTP for customer with contact: {} and otp: {}",
            customerResponse.getContact(), customerResponse.getOtp());
        String contactNumber = AppUtils.getValidContactNUmber(customerResponse.getContact());
        return verifyOTPInCurrentSession(customerResponse.getOtp(),contactNumber);
    }

    @RequestMapping(method = RequestMethod.POST, value = "otp/verify/app", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean verifyOTP(@RequestBody final CustomerLoginInfo info) throws DataUpdationException {
        LOG.info("Request from dine-in-app to verify OTP for customer with contact: {} and otp: {}", info.getContact(),
            info.getOtp());
        String contactNumber;
        if(Objects.nonNull(info.getBrandId()) && AppConstants.CHAAYOS_BRAND_ID != info.getBrandId()){
            contactNumber = AppUtils.getValidContactNumberForBrand(info.getContact(),customerService.getBrandContactCode(info.getBrandId()));
        }
        else {
            contactNumber = AppUtils.getValidContactNUmber(info.getContact());
        }
        return verifyOTPInCurrentSession(info.getOtp(), contactNumber);
    }

    @RequestMapping(method = RequestMethod.POST, value = "otp/resend", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean resendCustomerAuthorizationOTP(@RequestBody final CustomerResponse customerResponse,
                                                  @RequestParam(name = "chp", required = false) Integer partnerId,
                                                  @RequestParam(required = false) Integer brandId)
            throws DataUpdationException, DuplicateRequestException {
        LOG.info("Request from neo-service to resend OTP for contact: {}", customerResponse.getContact());
        if(Objects.nonNull(brandId)){
            String contact = AppUtils.getValidContactNumberForBrand(customerResponse.getContact(),customerService.getBrandContactCode(brandId));
            return resendCustomerAuthorizationOTPBrandWise(environment.getSendOTPLastFourDigits(), contact, false,
                    partnerId,brandId);
        }
        String contactNumber = AppUtils.getValidContactNUmber(customerResponse.getContact());
        return resendCustomerAuthorizationOTP(environment.getSendOTPLastFourDigits(), contactNumber, false,
                partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "address/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Address addAddress(@RequestBody CustomerAddress request)
        throws DataNotFoundException, DataUpdationException {
        LOG.info("Request to add address for contact {} with data {}", request.getContact(),
            JSONSerializer.toJSON(request.getAddress()));
        String contactNumber = AppUtils.getValidContactNUmber(request.getContact());
        return customerService.addAddress(contactNumber, request.getAddress());
    }

    @RequestMapping(method = RequestMethod.POST, value = "address/new", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Address> getNewAddresses(@RequestBody final CustomerCurrentAddresses request)
        throws DataNotFoundException, DataUpdationException {
        LOG.info("Request to get new address for contact {} with data {}", request.getId(),
            JSONSerializer.toJSON(request));
        return customerService.getNewAddress(request.getId(), request.getAddresses());
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-internal-customers", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<String> getInternalCustomers() {
        try {
            return externalUserService.getAllInternalOpsEmployeeContactNumbers();
        } catch (Exception e) {
            LOG.error("Unable to get internal customers", e);
        }
        return null;
    }

    @RequestMapping(method = RequestMethod.GET, value = "partner/customer/login", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public NeoCustomerResponse partnerCustomerSignup(@RequestParam String accessKey, @RequestParam String token)
        throws DataUpdationException, AuthenticationFailureException {
        try {
            ZeonReqestData data = (ZeonReqestData) validate(accessKey, token, ZeonReqestData.class);
            LOG.info("Got request from neo-service to sign in partner {} customer with contact number {}",
                data.getPartnerId(), data.getContact());
            Customer customer = customerService.getCustomer(data.getContact());
            if (customer == null) {
                customer = getCustomerObject(data.getContact(), data.getName(), data.getEmail(), data.getPartnerId(),
                    data.getOutletId());
                LOG.info(" CUSTOMER BRANCH CHECK partner/customer/login -------- " + customer.getAcquisitionBrandId()
                    + customer.isChaayosCustomer());
                customer = customerService.addCustomerUnchecked(customer);
            }
            NeoCustomerResponse r = new NeoCustomerResponse();
            r.setCustomer(customer);
            r.setTerminal(data.getTerminal());
            r.setUnitId(data.getOutletId());
            return r;
        } catch (Exception e) {
            LOG.error("Error while partner request", e);
            throw e;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "signup/coupon", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean sendSignUpCouponCall(@RequestBody final CustomerResponse customerResponse) {
        LOG.info("Request from neo-service to send sign up coupon code for contact: {}", customerResponse.getContact());
        String contactNumber = AppUtils.getValidContactNUmber(customerResponse.getContact());
        customerResponse.setContact(contactNumber);
        return sendSignUpCoupon(customerResponse);
    }

    public static void main(String[] args) {
        PartnerRequest request = new PartnerRequest();
        request.setPartnerId(13);
        String key = "X26QK5T7VP7Y3JD";
        ZeonReqestData data = new ZeonReqestData();
        data.setContact("8055852367");
        data.setEmail("<EMAIL>");
        data.setName("Vivek");
        data.setOutletId(10001);
        data.setPartnerId(13);
        data.setTerminal(1);
        request.setData(JSONSerializer.toJSON(data));
        TokenServiceImpl<PartnerRequest> service = new TokenServiceImpl<PartnerRequest>();
        System.out.println(service.createToken(request, -1L, key));

    }

    private <T> Object validate(String accessKey, String token, Class<T> clazz) throws AuthenticationFailureException {
        ExternalAPIToken partnerToken = externalAPICache.getTokenMap().get(accessKey);

        if (externalAPICache.getTokenMap() == null) {
            LOG.info("TOKEN MAP IS NULL");
        }
        PartnerRequest request = new PartnerRequest();
        try {
            jwtService.parseToken(request, token, partnerToken.getPassCode());
        } catch (SignatureException se) {
            throw new AuthenticationFailureException(se.getMessage());
        }
        if (partnerToken == null || partnerToken.getPartnerId() == null || request.getPartnerId() == null
            || !partnerToken.getPartnerId().equals(request.getPartnerId())) {
            LOG.info("Partner Does not match partner partner {}, request {}", JSONSerializer.toJSON(partnerToken),
                JSONSerializer.toJSON(request));
            throw new AuthenticationFailureException("Partner does not match");
        }
        return JSONSerializer.toJSON(request.getData(), clazz);
    }

    @Override
    public AuthorizationService getAuthorizationService() {
        return authorizationDao;
    }

    @Override
    public EnvironmentProperties getEnvironmentProperties() {
        return environment;
    }

    @Override
    public CustomerService getCustomerService() {
        return customerService;
    }

    public NotificationService getNotificationService() {
        return notificationService;
    }

    private final Customer getCustomerObject(Customer request, ApplicationName appName) {
        Customer customer = new Customer();
        customer.setFirstName(request.getFirstName());
        customer.setMiddleName(request.getMiddleName());
        customer.setLastName(request.getLastName());
        customer.setEmailId(request.getEmailId());
        customer.setEmailVerified(false);
        customer.setContactNumber(request.getContactNumber());
        customer.setSignUpRefCode(request.getSignUpRefCode());
        customer.setIsRefSubscriber(request.getIsRefSubscriber());
        customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
        if (request.getRegistrationUnitId() == null) {
            customer.setRegistrationUnitId(AppConstants.CHAI_ON_DEMAND_UNIT_ID);
        } else {
            customer.setRegistrationUnitId(request.getRegistrationUnitId());
        }
        if (request.getAcquisitionSource() == null) {
            customer.setAcquisitionSource(appName.name());
        } else {
            customer.setAcquisitionSource(request.getAcquisitionSource());
        }
        if (request.getAcquisitionToken() == null) {
            customer.setAcquisitionToken(UnitCategory.COD.name());
        } else {
            customer.setAcquisitionToken(request.getAcquisitionToken());
        }
        if (request.getGender() != null) {
            customer.setGender(request.getGender());
        }
        if (request.getDateOfBirth() != null) {
            customer.setDateOfBirth(request.getDateOfBirth());
        }
        if (request.getAnniversary() != null) {
            customer.setAnniversary(request.getAnniversary());
        }
        if(request.getOptWhatsapp() != null){
            customer.setOptWhatsapp(request.getOptWhatsapp());
        }
        customer.setOrderCount(0);
        customer.setAvailedSignupOffer(false);
        customer.setContactNumberVerified(true);
        customer.setAcquisitionBrandId(request.getAcquisitionBrandId() == null ? AppConstants.CHAAYOS_BRAND_ID
            : request.getAcquisitionBrandId());
        customer.setChaayosCustomer(customer.getAcquisitionBrandId() == AppConstants.CHAAYOS_BRAND_ID);
        return customer;
    }

    private final Customer getCustomerObject(String contact, String name, String email, Integer partnerId,
                                             Integer outletId) {
        Customer customer = new Customer();
        customer.setFirstName(name);
        customer.setEmailId(email);
        customer.setContactNumber(contact);
        customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
        customer.setRegistrationUnitId(outletId);
        customer.setAcquisitionSource(ApplicationName.NEO_SERVICE.name());
        customer.setAcquisitionToken(String.valueOf(partnerId));
        customer.setOrderCount(0);
        customer.setAvailedSignupOffer(false);
        customer.setContactNumberVerified(true);
        customer.setAcquisitionBrandId(AppConstants.CHAAYOS_BRAND_ID);
        customer.setChaayosCustomer(true);
        return customer;
    }

}
