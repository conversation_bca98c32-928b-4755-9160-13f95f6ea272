package com.stpl.tech.kettle.crm.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Referral Link Data")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class ReferralLinkData {
	@ExcelField
	private String contactNumber;
	@ExcelField
	private String contactName;
	@ExcelField
	private String referralCode;
	@ExcelField
	private String customerId;
	@ExcelField
	private String referralAvailable;
	@ExcelField
	private String referralLink;
	@ExcelField
	private String referralOriginalLink;
	@ExcelField
	private String campaign;
	@ExcelField
	private String source;

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getReferralCode() {
		return referralCode;
	}

	public void setReferralCode(String referralCode) {
		this.referralCode = referralCode;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public String getReferralAvailable() {
		return referralAvailable;
	}

	public void setReferralAvailable(String referralAvailable) {
		this.referralAvailable = referralAvailable;
	}

	public String getReferralLink() {
		return referralLink;
	}

	public void setReferralLink(String referralLink) {
		this.referralLink = referralLink;
	}

	public String getReferralOriginalLink() {
		return referralOriginalLink;
	}

	public void setReferralOriginalLink(String referralOriginalLink) {
		this.referralOriginalLink = referralOriginalLink;
	}

	public String getCampaign() {
		return campaign;
	}

	public void setCampaign(String campaign) {
		this.campaign = campaign;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

}
