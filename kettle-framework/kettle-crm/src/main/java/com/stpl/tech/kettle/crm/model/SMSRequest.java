package com.stpl.tech.kettle.crm.model;

public class SMSRequest {

	private String contact;
	private String campaign;
	private String message;
	private String smsType;
	private String application;
	private String token;
	private boolean sendNotification;

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getCampaign() {
		return campaign;
	}

	public void setCampaign(String campaign) {
		this.campaign = campaign;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getSmsType() {
		return smsType;
	}

	public void setSmsType(String smsType) {
		this.smsType = smsType;
	}

	public String getApplication() {
		return application;
	}

	public void setApplication(String application) {
		this.application = application;
	}

	public boolean isSendNotification() {
		return sendNotification;
	}

	public void setSendNotification(boolean sendNotification) {
		this.sendNotification = sendNotification;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	@Override
	public String toString() {
		return "SMSRequest [contact=" + contact + ", campaign=" + campaign + ", message=" + message + ", smsType="
				+ smsType + ", application=" + application + ", token=" + token + ", sendNotification="
				+ sendNotification + "]";
	}

}
