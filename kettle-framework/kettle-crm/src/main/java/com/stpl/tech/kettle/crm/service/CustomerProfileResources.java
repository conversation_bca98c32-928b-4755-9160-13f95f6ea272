/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

import java.io.IOException;
import java.util.List;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.kettle.core.notification.sms.CustomerReferralData;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.crm.model.CustomerProfileData;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.notification.service.ExternalUserManagementService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.OtpType;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT)
public class CustomerProfileResources extends CustomerManagementResources {

	@Autowired
	protected CustomerService customerdao;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private ExternalUserManagementService externalUserService;

	@Autowired
	protected AuthorizationService authorizatonDao;

	@Autowired
	protected EnvironmentProperties enviournment;

	@Autowired
	private SMSClientProviderService providerService;

	private static final Logger LOG = LoggerFactory.getLogger(CustomerProfileResources.class);

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to the
	 * client as "text/plain" media type.
	 *
	 * @return String that will be returned as a text/plain response.
	 * @throws DataUpdationException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "lookup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerProfileData search(@RequestBody final CustomerProfileData customerSession)
			throws DataUpdationException {
		Customer currentData = customerSession.getCustomer();
		LOG.info(String.format("Got request for a customer with contact number ## %s from source %s and token %s ",
				currentData.getContactNumber(), currentData.getAcquisitionSource(), currentData.getAcquisitionToken()));
		// validate phone number here
		boolean sentOtp = false;
		Customer customer = customerdao.getCustomer(currentData.getContactNumber());
		if (customer == null) {
			customer = currentData;
			sentOtp = sendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(), currentData.getContactNumber());
			customerSession.setNewCustomer(true);
		} else {
			if (customer.isInternal()) {
				throw new DataUpdationException("Customer With Contact Number "
						+ customerSession.getCustomer().getContactNumber() + " is an Internal Employee");
			}
			if (!customer.isContactNumberVerified()) {
				sentOtp = sendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(),currentData.getContactNumber());
			} else {
				// TODO Fix this
				// sentOtp =
				// sendCustomerLoginOTP(currentData.getContactNumber());
				sentOtp = sendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(),currentData.getContactNumber());
			}
			customerSession.setNewCustomer(false);
		}
		customerSession.setCustomer(customer);
		customerSession.setOtpSent(sentOtp);
		return customerSession;
	}

	@RequestMapping(method = RequestMethod.GET, value = "mark-as-internal-customers", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean markCustomersAsInternal() {
		try {
			List<String> contacts = externalUserService.getAllInternalOpsEmployeeContactNumbers();
			customerdao.markAsInternalCustomers(contacts);
			return true;
		} catch (Exception e) {
			LOG.error("Unable to execute marking of internal customers", e);
		}

		return false;
	}

	protected boolean sendCustomerLoginOTP(String contactNumber) {
		try {
			String token = getNotificationService().getOTPMapperInstance()
					.generateOTP(enviournment.getSendAutomatedOTPSMS(), OtpType.KETTLE_CRM, contactNumber);
			String message = CustomerSMSNotificationType.CUSTOMER_LOGIN_MESSENGER.getMessage(getToken(token));
			boolean sentOtp = providerService
					.getSMSClient(CustomerSMSNotificationType.CUSTOMER_LOGIN_MESSENGER.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM)
					.sendMessage(message, contactNumber);
			authorizatonDao.createSMSAuthorizationRequest(contactNumber, token, message);
			return sentOtp;
		} catch (IOException | DuplicateRequestException e) {
			LOG.error("Error while sending the OTP message to " + contactNumber, e);
		}
		return false;
	}

	

	@RequestMapping(method = RequestMethod.POST, value = "verify/signup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerProfileData verifyContactNumber(@RequestBody final CustomerProfileData userSession)
			throws DataUpdationException {
		Customer currentData = userSession.getCustomer();
		LOG.info(String.format(
				"Got request for a verify OTP for customer with contact number ## %s from source %s and token %s ",
				currentData.getContactNumber(), currentData.getAcquisitionSource(), currentData.getAcquisitionToken()));

		boolean result = verifyOTPInCurrentSession(userSession.getOtpText(), currentData.getContactNumber());
		if (result) {
			if (userSession.isNewCustomer()) {
				currentData = customerdao.addCustomer(currentData);
			}
			if (!currentData.isContactNumberVerified()) {
				customerdao.verifyContactNumber(currentData.getContactNumber());
			}

		}
		currentData = customerdao.getCustomer(currentData.getContactNumber());
		userSession.setOtpVerificationStatus(result);
		userSession.setCustomer(currentData);
		return userSession;
	}

	@RequestMapping(method = RequestMethod.POST, value = "refer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerReferralData referFriend(@RequestBody final CustomerReferralData customerReferralData)
			throws DataUpdationException, IOException, AuthenticationFailureException {
		LOG.info(String.format("Got a referral request from %s for %s [%s]",
				customerReferralData.getReffererContactNumber(), customerReferralData.getContactNumber(),
				customerReferralData.getName()));
		
		customerReferralData.setOtpSent(sendCustomerAuthorizationOTP(false, customerReferralData.getContactNumber()));
		return customerReferralData;
	}

	@RequestMapping(method = RequestMethod.POST, value = "update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerProfileData update(@RequestBody final CustomerProfileData userSession) throws DataNotFoundException,DataUpdationException {
		Customer currentData = userSession.getCustomer();
		LOG.info(String.format(
				"Got request for an update basic information for customer with contact number ## %s from source %s and token %s ",
				currentData.getContactNumber(), currentData.getAcquisitionSource(), currentData.getAcquisitionToken()));
		customerdao.updateBasicCustomerInfo(currentData);
		Customer updatedCustomer = customerdao.getCustomer(userSession.getCustomer().getId());
		userSession.setCustomer(updatedCustomer);
		return userSession;
	}

	@Override
	public AuthorizationService getAuthorizationService() {
		return authorizatonDao;
	}

	@Override
	public EnvironmentProperties getEnvironmentProperties() {
		return enviournment;
	}

	@Override
	public CustomerService getCustomerService() {
		return customerdao;
	}

	@Override
	public NotificationService getNotificationService() {
		return notificationService;
	}

}
