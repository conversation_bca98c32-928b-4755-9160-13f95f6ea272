package com.stpl.tech.kettle.crm.adhoc;

import java.util.List;
import java.util.stream.Collectors;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;

import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.model.BouncedRecipient;
import com.stpl.tech.util.notification.model.EmailBounceMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EmailMessageListener implements MessageListener {

    private static final Logger LOG = LoggerFactory.getLogger(EmailMessageListener.class);

    private EnvType envType;

    private CustomerService customerService;

    public EmailMessageListener(EnvType envType, CustomerService customerService) {
        this.envType = envType;
        this.customerService = customerService;
    }

    @Override
    public void onMessage(Message message) {
        try {
            LOG.info("On Message " + message.getJMSMessageID());
            if(message instanceof SQSTextMessage){
                SQSTextMessage object = (SQSTextMessage) message;
                if (object.getText() != null) {
                    try {
                        JsonParser parser = new JsonParser();
                        JsonObject o = parser.parse(object.getText()).getAsJsonObject();
                        LOG.info("Bounced recipients message :: {}", o);
                        //DO NOT CHANGE THIS
                        JsonPrimitive emailBounceMessage = o.getAsJsonPrimitive("Message");
                        EmailBounceMessage bounced = JSONSerializer.toJSON(emailBounceMessage.getAsString(),
                                EmailBounceMessage.class);
                        List<String> recipients = bounced.getBounce().getBouncedRecipients().stream()
                                .map(BouncedRecipient::getEmailAddress)
                                .collect(Collectors.toList());
                        LOG.info("Bounced recipients message :: {}", recipients);
                        customerService.removeInvalidEmails(recipients);
                        message.acknowledge();
                    }catch (DataUpdationException e1){
                        LOG.error(":::::: Error while updating customer details ::::::",e1);
                    }catch (Exception e){
                        LOG.error(":::::::::: Exception while reading message ::::::::",e);
                    }
                }
            }
        } catch (JMSException e) {
            LOG.error("Error during reading message ::: ",e);
        }
    }
}
