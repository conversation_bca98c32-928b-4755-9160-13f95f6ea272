/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.core;

public class CustomerServiceConstants {

	public static final String API_VERSION = "v1";

	public static final String SEPARATOR = "/";

	public static final String CRM_SERVICES_ROOT_CONTEXT = "crm";
	
	public static final String SHOPIFY_SERVICES_ROOT_CONTEXT = "shopify-resource";

	public static final String FORM_SERVICES_ROOT_CONTEXT = "form";

	public static final String CUSTOMER_SERVICES_ROOT_CONTEXT = "customer";

	public static final String CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT = "customer-profile";

	public static final String COD_CUSTOMER_SERVICES_ROOT_CONTEXT = "cod-customer";

	public static final String EXTERNAL_SERVICES_ROOT_CONTEXT = "external";

	public static final String NEO_SERVICES_ROOT_CONTEXT = "neo";

	public static final String AUTOMATED_CLM_ROOT_CONTEXT = "automated-clm";

	public static final String REFERRAL_SERVICE_ROOT_CONTEXT = "ref";
	public static final String LOYALITY_TRANSFER_SERVICE_ROOT_CONTEXT = "loyalty-transfer";

}
