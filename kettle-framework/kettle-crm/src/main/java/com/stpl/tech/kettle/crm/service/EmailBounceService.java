package com.stpl.tech.kettle.crm.service;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.Session;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.crm.adhoc.EmailMessageListener;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-11-2017.
 */

@Service
public class EmailBounceService {

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    protected CustomerService customerService;

    @PostConstruct
    public void init() throws JMSException {
    	Regions region = AppUtils.getRegion(props.getEnvironmentType());
        SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
        EmailMessageListener listener = new EmailMessageListener(props.getEnvironmentType(), customerService);
        MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvironmentType().name(),
                "_EMAIL_BOUNCES");
        consumer.setMessageListener(listener);
        SQSNotification.getInstance().getSqsConnection(region).start();
    }
}