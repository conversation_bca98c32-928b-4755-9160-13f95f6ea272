/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.crm.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.crm.dao.CustomerAdhocDao;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.offer.data.model.CustomerOfferData;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;

/**
 * <AUTHOR>
 */
@Repository
public class CustomerAdhocDaoImpl extends AbstractDaoImpl implements CustomerAdhocDao {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerAdhocDaoImpl.class);

	@Autowired
	private MasterDataCache cache;

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.kettle.customer.dao.CustomerDao#getAllCustomerWithOrders(
	 * java.util.Date, java.util.Date)
	 */
	@Override
	public List<CustomerOfferData> getAllCustomerWithOrders(Date startTime, Date endTime) {
		List<CustomerOfferData> customerOfferList = new ArrayList<>();
		try {
			LOG.info(String.format("Looking up for Orders between %s and %s", startTime, endTime));
			Query query = manager.createQuery(
					"select distinct od.customerId, od.unitId,ci.firstName, ci.contactNumber, od.settledAmount, ci.numberVerificationTime FROM OrderDetail od, CustomerInfo ci where od.customerId = ci.customerId and od.billingServerTime > :startTime and od.billingServerTime <= :endTime and od.orderStatus <> :orderStatus and od.orderType = :orderType and od.customerId > 5 and od.orderSource = :cafeSource "
					+ "and (ci.isBlacklisted is null or ci.isBlacklisted = :isBlacklisted) "
					+ "and (ci.emailId is null or ci.emailId not like :emailIdFilter) "
					+ "and ci.isNumberVerified = :isNumberVerified");
			query.setParameter("startTime", startTime);
			query.setParameter("endTime", endTime);
			query.setParameter("cafeSource", UnitCategory.CAFE.name());
			query.setParameter("isBlacklisted", AppConstants.NO);
			query.setParameter("isNumberVerified", AppConstants.YES);
			query.setParameter("emailIdFilter", "%chaayos.com");
			query.setParameter("orderStatus", OrderStatus.CANCELLED.name());
			query.setParameter("orderType", "order");
			@SuppressWarnings("unchecked")
			List<Object[]> list = query.getResultList();
			if (list != null) {
				for (Object[] detail : list) {
					int unitId = (Integer) detail[1];
					UnitBasicDetail unit = cache.getUnitBasicDetail(unitId);
					CustomerOfferData d = new CustomerOfferData((String) detail[3], (String) detail[2],
							(Integer) detail[0], unitId, unit.getName(), (BigDecimal) detail[4], unit.getRegion(),(Date)detail[5]);
					customerOfferList.add(d);
				}
			}
		} catch (NoResultException e) {
			LOG.error(String.format("Did not find any order in between %s and %s", startTime, endTime));
		}
		return customerOfferList;
	}

	@Override
	public List<CustomerOfferData> getAllCustomerWithOrders(Date startTime, Date endTime, List<Integer> unitIds) {
		List<CustomerOfferData> customerOfferList = new ArrayList<>();
		try {
			LOG.info(String.format("Looking up for Orders between %s and %s", startTime, endTime));
			Query query = manager.createQuery(
					"select distinct od.customerId, od.unitId,ci.firstName, ci.contactNumber, od.settledAmount, ci.numberVerificationTime FROM OrderDetail od, CustomerInfo ci where od.customerId = ci.customerId and od.billingServerTime > :startTime and od.billingServerTime <= :endTime and od.orderStatus <> :orderStatus and od.orderType = :orderType and od.customerId > 5 and od.orderSource = :cafeSource and od.unitId in (:unitIds) "
					+ "and (ci.isBlacklisted is null or ci.isBlacklisted = :isBlacklisted) "
					+ "and (ci.emailId is null or ci.emailId not like :emailIdFilter) "
					+ "and ci.isNumberVerified = :isNumberVerified");
			query.setParameter("startTime", startTime);
			query.setParameter("endTime", endTime);
			query.setParameter("cafeSource", UnitCategory.CAFE.name());
			query.setParameter("isBlacklisted", AppConstants.NO);
			query.setParameter("unitIds", unitIds);
			query.setParameter("isNumberVerified", AppConstants.YES);
			query.setParameter("emailIdFilter", "%chaayos.com");
			query.setParameter("orderStatus", OrderStatus.CANCELLED.name());
			query.setParameter("orderType", "order");
			@SuppressWarnings("unchecked")
			List<Object[]> list = query.getResultList();
			if (list != null) {
				for (Object[] detail : list) {
					int unitId = (Integer) detail[1];
					UnitBasicDetail unit = cache.getUnitBasicDetail(unitId);
					CustomerOfferData d = new CustomerOfferData((String) detail[3], (String) detail[2],
							(Integer) detail[0], unitId, unit.getName(), (BigDecimal) detail[4], unit.getRegion(),(Date)detail[5]);
					customerOfferList.add(d);
				}
			}
		} catch (NoResultException e) {
			LOG.error(String.format("Did not find any order in between %s and %s", startTime, endTime));
		}
		return customerOfferList;
	}

}
