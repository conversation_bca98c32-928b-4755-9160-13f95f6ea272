/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.core.data.vo.AuditTokenInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackRatingData;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.OrderDetailForFeedback;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.external.EventDetail;
import com.stpl.tech.kettle.domain.model.webengage.survey.WebEngageSurveyForm;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.acl.service.CSRFTokenService;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.FORM_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + FORM_SERVICES_ROOT_CONTEXT)
public class FormResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(FormResource.class);

    @Autowired
    private FeedbackManagementService feedbackService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    protected TokenService<FeedbackTokenInfo> feedbackTokenService;

    @Autowired
    protected TokenService<AuditTokenInfo> auditTokenService;

    @Autowired
    private CSRFTokenService csrftokenService;


    @RequestMapping(method = RequestMethod.POST, value = "external/type-form/submit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer submitFeedbackTypeForm(@RequestBody final Object formEvent) {
        String json = JSONSerializer.toJSON(formEvent);
        LOG.info("External form submission : {}", json);
        EventDetail detail = WebServiceHelper.convert(formEvent, EventDetail.class);
        try {
			if (detail != null && detail.getFormResponse() != null && detail.getFormResponse().getHidden() != null) {
				String token = detail.getFormResponse().getHidden().getToken();
				if (token != null) {
					FeedbackTokenInfo tokenInfo = new FeedbackTokenInfo();
					feedbackTokenService.parseToken(tokenInfo, token);
					FeedbackRatingData rating = feedbackService.addFeedback(detail, tokenInfo);
					return rating.getRating();
				}
			}
        } catch (Exception e) {
            LOG.error("Error while processing the json " + json, e);
            new ErrorNotification("Error while persisting feedback data", "Json : " + json, e,
                props.getEnvironmentType()).sendEmail();
            throw e;
        }
        return -1;
    }

    @RequestMapping(method = RequestMethod.POST, value = "external/type-form/order/submit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer submitFeedbackTypeFormWithOnlyOrder(@RequestBody final Object formEvent) throws Exception {
        String json = JSONSerializer.toJSON(formEvent);
        LOG.info("External Order form submission : {}", json);
        EventDetail detail = WebServiceHelper.convert(formEvent, EventDetail.class);
        try {
			if (detail != null && detail.getFormResponse() != null && detail.getFormResponse().getHidden() != null) {
				String token = detail.getFormResponse().getHidden().getToken();
				if (token != null) {
					FeedbackTokenInfo tokenInfo = new FeedbackTokenInfo();
					feedbackTokenService.parseToken(tokenInfo, token);
					FeedbackRatingData rating = feedbackService.addFeedback(detail, tokenInfo);
					System.out.println("FeedbackRatingData for Swiggy : "+ rating);
					if(rating.getAwardLoyalty() != null && rating.getAwardLoyalty() && rating.getOrderId() > 0) {
						String contact = rating.getContactNumber().substring(rating.getContactNumber().length() - 10, rating.getContactNumber().length());
						Customer customer = customerService.createCustomerAndAwardLoyalty(contact, rating.getName(), rating.getOrderId(), "QR_SCAN", "NPS");
						feedbackService.updateCustomerInfoInFeedbackData(rating.getFeedbackId(), customer.getId(), customer.getEmailId());
					}
					return rating.getRating();
				}
			}
        } catch (Exception e) {
            LOG.error("Error while processing the json " + json, e);
            new ErrorNotification("Error while persisting feedback data", "Json : " + json, e,
                props.getEnvironmentType()).sendEmail();
            throw e;
        }
        return -1;
    }
    
    
    
    @RequestMapping(method = RequestMethod.POST, value = "external/type-form/submit/audit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer submitAuditTypeForm(@RequestBody final Object formEvent) {
        String json = JSONSerializer.toJSON(formEvent);
        LOG.info("External form submission : {}", json);
        EventDetail detail = WebServiceHelper.convert(formEvent, EventDetail.class);
        try {
            String token = detail.getFormResponse().getHidden().getToken();
            if (token != null) {
                AuditTokenInfo tokenInfo = new AuditTokenInfo();
                auditTokenService.parseToken(tokenInfo, token);
                return feedbackService.addAudit(detail, tokenInfo);
            }
        } catch (Exception e) {
            LOG.error("Error while processing the json " + json, e);
            new ErrorNotification("Error while persisting feedback data", "Json : " + json, e,
                props.getEnvironmentType()).sendEmail();;
            throw e;
        }
        return -1;
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "web-engage/survey/submit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer webEngageSurveyData(@RequestBody final Object formEvent) throws Exception {
        LOG.info("External form submission : {}", WebServiceHelper.convertToString(formEvent));
        WebEngageSurveyForm detail = WebServiceHelper.convert(formEvent, WebEngageSurveyForm.class);
        try {
			if(detail.getResponse().getData().getTitle().equalsIgnoreCase("TNPS")){
				int id = feedbackService.addOrderNPSDetail(detail);
				if(id == -1){
					throw new Exception("Error while processing the form for NPS Feedback");
				}
				return id;
			}
			
        } catch (Exception e) {
            String json = JSONSerializer.toJSON(formEvent);
            LOG.error("Error while processing the json " + json, e);
            new ErrorNotification("Error while persisting nps data", "Json : " + json, e,
                props.getEnvironmentType()).sendEmail();;
            throw e;
        }
        return -1;
    }
    @RequestMapping(method = RequestMethod.POST, value = "order-detail-feedback", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getOrderDetailForFeedback(@RequestBody String dataToken){
        if(dataToken == null || dataToken.length() == 0){
            LOG.info("Empty data token found");
            return null;
        }
        try{
            FeedbackTokenInfo tokenInfo = new FeedbackTokenInfo();
            feedbackTokenService.parseToken(tokenInfo, dataToken);
            LOG.info("Got token info after parsing {}",tokenInfo.toString());
            if(tokenInfo.getOrderId() == null){
                LOG.info("fetching order id for feedback id == {}", tokenInfo.getFeedbackId());
                tokenInfo.setOrderId(feedbackService.getOrderID(tokenInfo.getFeedbackId()));
                LOG.info("Got token info after fetching order id {}",tokenInfo.toString());
            }
            return feedbackService.getOrderDetailForFeedback(tokenInfo);
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }


    @RequestMapping(method = RequestMethod.POST, value = "submit-feedback", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean submitOrderFeedback(@RequestBody OrderDetailForFeedback feedbackData) throws AuthenticationFailureException {
        validateRequest(feedbackData.getT(), true);
        LOG.info("Request to save response for feedback id == "+feedbackData.getFid());
        return feedbackService.saveFeedback(feedbackData);
    }

    private boolean validateRequest(String token, boolean removeToken)
            throws AuthenticationFailureException {
        if (csrftokenService.contains(token)) {
            LOG.info("Found Token - " + token);
            if (removeToken) {
                removeToken(token);
            }
            return true;
        }else {
            LOG.info("Not Found Token - " + token);
        }
        throw new AuthenticationFailureException("Invalid request");
    }
    public void removeToken(String token) {
        try {
            if (token != null) {
                csrftokenService.remove(token);
            }
        } catch (Exception e) {
            LOG.error("Error Removing Token", e);
        }
    }


}
