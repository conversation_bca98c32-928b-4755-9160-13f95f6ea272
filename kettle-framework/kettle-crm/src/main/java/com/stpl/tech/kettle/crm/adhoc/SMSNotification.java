package com.stpl.tech.kettle.crm.adhoc;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.NotImplementedException;
import org.apache.commons.lang.WordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.core.file.load.AbstractRowMapper;
import com.stpl.tech.kettle.core.file.load.CSVParser;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;

public class SMSNotification {

	private static final Logger LOG = LoggerFactory.getLogger(SMSNotification.class);

	public static void main(String[] args) {
		SMSNotification notification = new SMSNotification();
		notification.sendMessages(true, "E:/notification/BANDRA_DATA_CSV.csv");
	}

	public void sendMessages(boolean hasShortUrl, String fileName) {
		try {
			LOG.info("Parsing file {} ", fileName);
			List<MessageData> messages = getMessages(fileName);
			for (MessageData data : messages) {
				if (hasShortUrl) {
					String longUrl = data.getUrl();
					ShortUrlData shortUrl = SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(longUrl);
					data.shortUrl = shortUrl.getUrl();
				}
				SolsInfiniWebServiceClient.getTransactionalClient().sendMessage(data.getText(), data.contact);
			}
		} catch (IOException e) {
			LOG.error("Failed to download the file", e);
		}
	}

	private List<MessageData> getMessages(String outputFile) {
		CSVParser<MessageData> parser = new CSVParser<MessageData>(new MessageDataMapper());
		List<MessageData> messages = new ArrayList<>();
		try {
			messages = parser.parseCsv(outputFile, 0, -1);
			if (parser.hasErrors()) {
				LOG.error("Errors in excel sheet " + parser.getErrors());
				throw new RuntimeException("Errors in excel sheet " + parser.getErrors());
			}
		} catch (IOException e) {
			LOG.error("Failed to load the stored excel file ", e);
			throw new RuntimeException("Failed to load the stored excel file ", e);
		}
		return messages;
	}

	public static class MessageData {
		private String contact;
		private String name;
		private String shortUrl;

		public String getUrl() {
			return String.format("https://stpltd.typeform.com/to/O2NgDW?contact=%s&name=%s", contact,
					WordUtils.capitalizeFully(name));
		}

		public String getText() {
			return String.format(
					"Hi %s, does absence of Valet Parking prevent you from coming to Chaayos at Chapel Road Bandra? Click here  %s & let us know",
					WordUtils.capitalizeFully(name), shortUrl);
		}
	}

	public class MessageDataMapper extends AbstractRowMapper<MessageData, String> {

		@Override
		public void setData(MessageData message, String nextCell) {
			throw new NotImplementedException();
		}

		@Override
		public void setData(MessageData message, String nextCell, int index) {
			try {
				switch (index) {
				case 0:
					message.contact = nextCell;
					break;
				case 1:
					message.name = nextCell == null ? "Chai Lover":nextCell.trim();
					break;
				default:
					break;
				}
			} catch (Exception e) {
				addError(index, e.getMessage());
			}
		}

		@Override
		public MessageData createNewInstance() {
			return new MessageData();
		}
	}
}
