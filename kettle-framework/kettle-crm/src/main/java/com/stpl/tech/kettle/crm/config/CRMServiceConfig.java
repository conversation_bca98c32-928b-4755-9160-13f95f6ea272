/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.config;

import com.google.common.base.Preconditions;
import com.stpl.tech.kettle.core.config.CLMConfig;
import com.stpl.tech.kettle.core.config.DataLakeExternalConfig;
import com.stpl.tech.kettle.core.config.TransactionExternalConfig;

import com.stpl.tech.master.core.config.KettleInterceptorConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;
import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import javax.sql.DataSource;
import java.util.TimeZone;

@SpringBootApplication
@Configuration
@EnableScheduling
@EnableWebMvc
@ComponentScan({ "com.stpl.tech.kettle.crm.automated", "com.stpl.tech.kettle.crm.dao","com.stpl.tech.kettle.crm.shopifyProperties",
		"com.stpl.tech.kettle.crm.service" ,"com.stpl.tech.kettle.clm" ,"com.stpl.tech.spring.crypto","com.stpl.tech.kettle.crm.resource"})
@Import(value = { MasterExternalConfig.class, TransactionExternalConfig.class,
		DataLakeExternalConfig.class, MasterSecurityConfiguration.class, CLMConfig.class, KettleInterceptorConfig.class})

public class CRMServiceConfig extends SpringBootServletInitializer {

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	@Autowired
	private Environment env;

	public static void main(String[] args) {
		SpringApplication.run(CRMServiceConfig.class, args);
	}

	@Bean(name = "kettleDataSource")
	public DataSource dumpDataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(Preconditions.checkNotNull(env.getProperty("jdbc.driverClassName")));
		dataSource.setUrl(Preconditions.checkNotNull(env.getProperty("kettle.db")));
		dataSource.setUsername(Preconditions.checkNotNull(env.getProperty("jdbc.user")));
		dataSource.setPassword(Preconditions.checkNotNull(env.getProperty("jdbc.pass")));
		return dataSource;
	}


	public CRMServiceConfig() {
		super();
	}
}
