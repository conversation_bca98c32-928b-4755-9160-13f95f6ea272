/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.data.model.CancelOrderInfo;
import com.stpl.tech.kettle.crm.model.CustomerLoginData;
import com.stpl.tech.kettle.crm.model.EmployeeLoginData;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.OtpType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.jms.JMSException;
import java.io.IOException;
import java.util.Objects;

public abstract class CustomerManagementResources extends NotificationResource {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerManagementResources.class);

	@Autowired
	private SMSClientProviderService providerService;

	protected boolean sendCustomerAuthorizationOTP(boolean lastFourDigits, String contactNumber) {
		try {
			Customer customer = customerService.getCustomer(contactNumber);
			String token = getNotificationService().getOTPMapperInstance().generateOTP(lastFourDigits,
					OtpType.KETTLE_CRM, contactNumber);
			if(Objects.nonNull(customer) && !AppConstants.DEFAULT_COUNTRY_CODE.equalsIgnoreCase(customer.getCountryCode())
					&& customer.isEmailVerified()) {
				customerService.sendOTPEmail(customer, token);
				return true;
			}
			String message = CustomerSMSNotificationType.OTP_MESSENGER.getMessage(getToken(token));
			boolean sentOtp = getNotificationService().sendNotification(
					CustomerSMSNotificationType.OTP_MESSENGER.name(), message, contactNumber,
					providerService.getSMSClient(CustomerSMSNotificationType.OTP_MESSENGER.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					getEnvironmentProperties().getSendAutomatedOTPSMS(),null);
			getAuthorizationService().createSMSAuthorizationRequest(contactNumber, token, message);
			return sentOtp;
		} catch (IOException | DuplicateRequestException | JMSException e) {
			LOG.error("Error while sending the OTP message to " + contactNumber, e);
		}
		return false;
	}

	protected boolean sendRedemptionOTP(CustomerLoginData userSession) {
		boolean status = false;
		try {
			Customer customer = customerService.getCustomer(userSession.getContactNumber());
			String token = getNotificationService().getOTPMapperInstance().generateOTP(props.getSendOTPLastFourDigits(),
					OtpType.KETTLE_CRM, userSession.getContactNumber());
			if(Objects.nonNull(customer) && !AppConstants.DEFAULT_COUNTRY_CODE.equalsIgnoreCase(customer.getCountryCode())
					&& customer.isEmailVerified()) {
				customerService.sendOTPEmail(customer, token);
				return true;
			}
			String message = CustomerSMSNotificationType.OTP_MESSENGER.getMessage(getToken(token));
			return getNotificationService().sendNotification(CustomerSMSNotificationType.OTP_MESSENGER.name(), message,
					userSession.getContactNumber(),
					providerService.getSMSClient(CustomerSMSNotificationType.OTP_MESSENGER.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					getEnvironmentProperties().getSendAutomatedOTPSMS(),null);
		} catch (IOException | DuplicateRequestException | JMSException e) {
			LOG.error("Error while sending the OTP message to " + userSession.getContactNumber(), e);
		}
		return status;
	}

	protected boolean sendComplimentaryOrderRedemptionOTP(EmployeeLoginData userSession) {
		boolean status = false;
		try {
			Customer customer = customerService.getCustomer(userSession.getAmContactNumber());
			String token = getNotificationService().getOTPMapperInstance().generateOTP(props.getSendOTPLastFourDigits(),
					OtpType.KETTLE_CRM, userSession.getAmContactNumber());
			if (Objects.nonNull(customer) && !AppConstants.DEFAULT_COUNTRY_CODE.equalsIgnoreCase(customer.getCountryCode())
					&& customer.isEmailVerified()) {
				customerService.sendOTPEmail(customer, token);
				return true;
			}
			String message = CustomerSMSNotificationType.OTP_MESSENGER.getMessage(getToken(token));
			boolean amNotification = false;
			if (Objects.nonNull(userSession.getAmContactNumber()) && userSession.getAmContactNumber().length()>9) {
				amNotification = getNotificationService().sendNotification(CustomerSMSNotificationType.OTP_MESSENGER.name(), message,
						userSession.getAmContactNumber(),
						providerService.getSMSClient(CustomerSMSNotificationType.OTP_MESSENGER.getTemplate().getSMSType(),
								ApplicationName.KETTLE_CRM), getEnvironmentProperties().getSendAutomatedOTPSMS(), null);

				//Send Charity Order Notification To Abhinav Agarwal
				getNotificationService().sendNotification(CustomerSMSNotificationType.OTP_MESSENGER.name(), message,
						AppConstants.CHARITY_ORDER_NOTIFICATION_NUMBER,
						providerService.getSMSClient(CustomerSMSNotificationType.OTP_MESSENGER.getTemplate().getSMSType(),
								ApplicationName.KETTLE_CRM), getEnvironmentProperties().getSendAutomatedOTPSMS(), null);
			}
			boolean damNotification = false;
//			if (Objects.nonNull(userSession.getDamContactNumber()) && Objects.nonNull(userSession.getAmContactNumber()) && userSession.getDamContactNumber().length()>9 && !userSession.getAmContactNumber().equals(userSession.getDamContactNumber()) ) {
//				damNotification = getNotificationService().sendNotification(CustomerSMSNotificationType.OTP_MESSENGER.name(), message,
//						userSession.getDamContactNumber(),
//						providerService.getSMSClient(CustomerSMSNotificationType.OTP_MESSENGER.getTemplate().getSMSType(),
//								ApplicationName.KETTLE_CRM), getEnvironmentProperties().getSendAutomatedOTPSMS(), null);
//			}
			return (amNotification || damNotification);
		} catch (IOException | DuplicateRequestException | JMSException e) {
			LOG.error("Error while sending the OTP message to " + userSession.getAmContactNumber(), e);
		}
		return status;
	}

	protected boolean sendCancelOrderOTP(CancelOrderInfo cancelOrderInfo) {
		boolean status = false;
		try {
			String token = getNotificationService().getOTPMapperInstance().generateOTP(props.getSendOTPLastFourDigits(),
					OtpType.KETTLE_CRM, cancelOrderInfo.getContactNumber());
			cancelOrderInfo.setOtpPin(token);
			String contact=AppUtils.getValidContactNUmber(cancelOrderInfo.getContactNumber());
			boolean sendNotification = false;
			String message = "";
			if(AppConstants.DOHFUL_BRAND_ID.equals(cancelOrderInfo.getBrandId())){
				message=CustomerSMSNotificationType.DOHFUL_CANCEL_ORDER_OTP_MESSENGER.getMessage(cancelOrderInfo);
			}
			else{
				message=CustomerSMSNotificationType.CANCEL_ORDER_OTP_MESSENGER.getMessage(cancelOrderInfo);
			}
			if (Objects.nonNull(cancelOrderInfo.getContactNumber()) && Objects.nonNull(contact) && Objects.nonNull(message)) {
				sendNotification = getNotificationService().sendNotification(CustomerSMSNotificationType.CANCEL_ORDER_OTP_MESSENGER.name(), message,
						cancelOrderInfo.getContactNumber(),
						providerService.getSMSClient(CustomerSMSNotificationType.CANCEL_ORDER_OTP_MESSENGER.getTemplate().getSMSType(),
								ApplicationName.KETTLE_CRM), getEnvironmentProperties().getSendAutomatedOTPSMS(), null);
			}

			return (sendNotification);
		} catch (IOException | DuplicateRequestException | JMSException e) {
			LOG.error("Error while sending the OTP message to " + cancelOrderInfo.getContactNumber(), e);
		}
		return status;
	}

	protected boolean verifyOTPInCurrentSession(String otpPin, String contactNumber) {
		boolean result = otpPin
				.equals(getNotificationService().getOTPMapperInstance().getOTP(OtpType.KETTLE_CRM, contactNumber));
		if (result) {
			getNotificationService().getOTPMapperInstance().removeOTP(OtpType.KETTLE_CRM, contactNumber);
		}
		return result;
	}

	public abstract AuthorizationService getAuthorizationService();

	public abstract EnvironmentProperties getEnvironmentProperties();

}
