package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.crm.automated.AutomatedCLMResource;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.EmailGenerationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.text.ParseException;

import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.AUTOMATED_CLM_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

/**
 * Created by shikhar on 7/5/19.
 */

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + AUTOMATED_CLM_ROOT_CONTEXT)
public class AutomatedCLMHelperResource extends AbstractResources{

    @Autowired
    private AutomatedCLMResource clmResource;

    @RequestMapping(method = RequestMethod.GET, value = "process-hourly-coupons", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean triggerHourlyCoupons() throws ParseException, IOException, EmailGenerationException {
        clmResource.triggerCoupons();
        return true;
    }
    
	@RequestMapping(method = RequestMethod.GET, value = "process-last-day-nps", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public boolean runLastDayNPS() throws ParseException, IOException, EmailGenerationException {
		clmResource.reSendNPSNotificationForCODCustomers();
		return true;
	}

	
	@RequestMapping(method = RequestMethod.GET, value = "expire-signup-offer", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public boolean expireSignupOfer() throws ParseException, IOException, EmailGenerationException {
		clmResource.expireSignupOffer();
		return true;
	}


	@RequestMapping(method = RequestMethod.GET, value = "loyalty-reminder-5-days", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public boolean loyaltyReminder5Days() throws ParseException, IOException, EmailGenerationException {
		clmResource.autoLoyaltyReminderBefore5Days();
		return true;
	}


	@RequestMapping(method = RequestMethod.GET, value = "loyalty-reminder-15-days", produces = MediaType.APPLICATION_JSON)
	@ResponseBody
	public boolean loyaltyReminder15Days() throws ParseException, IOException, EmailGenerationException {
		clmResource.autoLoyaltyReminderBefore15Days();
		return true;
	}

}
