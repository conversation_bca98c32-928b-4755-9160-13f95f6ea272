package com.stpl.tech.kettle.crm.cache;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

@Component
public class CRMCache {

	private final int MAX_COUNT = 10;

	private final Map<String, Integer> smsCounts = new HashMap<String, Integer>();

	public CRMCache() {
		super();
	}

	public Integer getSmsCountForContact(String contact) {
		return smsCounts.get(contact);
	}

	// TODO Atomic transactions?
	public Integer incrementCount(String contact) {
		Integer count = smsCounts.get(contact);
		if (count != null) {
			count++;
		} else {
			smsCounts.put(contact, 1);
		}
		return count;
	}

	public boolean isUnderSMSlimit(String contact) {
		Integer count = smsCounts.get(contact);
		return count == null || count < MAX_COUNT;
	}

	// TODO require customer count refresh service in case we are stuck for a
	// customer
	public void resetCounter(String contact) {
		smsCounts.remove(contact);
	}

	public void resetCounterForAllContacts() {
		smsCounts.clear();
	}
}
