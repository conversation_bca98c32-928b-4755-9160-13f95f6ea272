/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.model;

import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.domain.model.Address;

public class CODCustomerLoginData {

    private UserSessionDetail session;

    private String contactNumber;

    private boolean newCustomer;

    private Customer customer;

    private Address newAddress;

    private Integer acquisitionBrandId;

    private boolean chaayosCustomer;

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public boolean isNewCustomer() {
        return newCustomer;
    }

    public void setNewCustomer(boolean newCustomer) {
        this.newCustomer = newCustomer;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public UserSessionDetail getSession() {
        return session;
    }

    public void setSession(UserSessionDetail session) {
        this.session = session;
    }

    public Address getNewAddress() {
        return newAddress;
    }

    public void setNewAddress(Address newAddress) {
        this.newAddress = newAddress;
    }

    public Integer getAcquisitionBrandId() {
        return acquisitionBrandId;
    }

    public void setAcquisitionBrandId(Integer acquisitionBrandId) {
        this.acquisitionBrandId = acquisitionBrandId;
    }

    public boolean isChaayosCustomer() {
        return chaayosCustomer;
    }

    public void setChaayosCustomer(boolean chaayosCustomer) {
        this.chaayosCustomer = chaayosCustomer;
    }
}
