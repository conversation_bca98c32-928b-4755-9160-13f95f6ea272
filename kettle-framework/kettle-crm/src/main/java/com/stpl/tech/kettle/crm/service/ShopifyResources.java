package com.stpl.tech.kettle.crm.service;

import com.google.gson.Gson;
import com.stpl.tech.kettle.core.data.vo.ShopifyCustomerResponse;
import com.stpl.tech.kettle.crm.shopifyProperties.ShopifyConfig;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.ShopifyConverter;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import javax.jms.JMSException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SHOPIFY_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;


@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + SHOPIFY_SERVICES_ROOT_CONTEXT)
public class ShopifyResources {

    @Autowired
    private ShopifyAuthenticationService shopifyAuthenticationService;

    @Autowired
    private ShopifyConfig shopifyConfig;
    @Autowired
    ShopifyConverter shopifyCustomerConverter;


    @Autowired
    protected CustomerService customerService;
    public static final String HMAC_HEADER = "X-Shopify-Hmac-Sha256";
    private static final Logger LOG = LoggerFactory.getLogger(CRMResources.class);

    // Api generation for testing Shopify webhook Integration //
    @RequestMapping(method = RequestMethod.POST, value = "signup-new-shopify-customer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ShopifyCustomerResponse getShopifyCustomerCardInfo(HttpServletRequest req)
            throws DataUpdationException, DataNotFoundException, JMSException, IOException, ServletException, NoSuchAlgorithmException, InvalidKeyException {

        String secret = shopifyConfig.getSecretKey();
        Gson shopifyResponseJSON = new Gson();
        String webhookHmac = req.getHeader(HMAC_HEADER);
        String content = req.getReader().lines().collect(Collectors.joining());
        String hmac = null;
        ShopifyCustomerResponse shopifyCustomerResponse = shopifyResponseJSON.fromJson(content, ShopifyCustomerResponse.class);
        LOG.info("Request for Shopify user: {}", shopifyCustomerResponse.getFirstName());
        if (!shopifyAuthenticationService.checkHmac(content, webhookHmac, secret) || (shopifyCustomerResponse.getPhone()==null && shopifyCustomerResponse.getNote()==null)) {
            return null;
        } else {
            LOG.info("Received a webhook request from Shopify");
        }
        shopifyCustomerResponse.setPhone(shopifyCustomerResponse.getPhone() != null ? shopifyCustomerResponse.getPhone() : "+91" + shopifyCustomerResponse.getNote());
        LOG.info("Got request to signup and verify up for a Shopify customer with contact number ## {}  for  {}",
                shopifyCustomerResponse.getPhone(), shopifyCustomerResponse.getFirstName());
        Customer customer = customerService.getCustomer(shopifyCustomerResponse.getPhone());
        if (Objects.isNull(customer)) {
            customer = shopifyCustomerConverter.getCustomerFromShopify(shopifyCustomerResponse);
            customer.setContactNumberVerified(false);
            customer.setOptWhatsapp(AppConstants.NO);
            customer = customerService.addCustomerUnchecked(customer);
            shopifyCustomerConverter.updateShopifyCustomerResponse(customer, shopifyCustomerResponse);

            // send verification email to new customer
            if (AppUtils.checkBlank(shopifyCustomerResponse.getEmail()) != null && Objects.nonNull(customer.getId())
                    && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customer.getId())) {
                customerService.sendVerificationEmail(shopifyCustomerResponse.getEmail(),
                        null, customer.getContactNumber(), shopifyCustomerResponse.getFirstName(),customer.getAcquisitionBrandId());
            }
        } else {
            LOG.info(" Existing Customer  -------- {}  with Contact: {}", customer.getId(), customer.getContactNumber());
            List<Address> newAddresses = shopifyCustomerConverter.updateAddressFromShopifyAddress(customer.getAddresses(), shopifyCustomerResponse.getAddresses());
            Customer updatedCustomer = shopifyCustomerConverter.getCustomerFromShopify(shopifyCustomerResponse);
            updatedCustomer.setAddresses(newAddresses);
            updatedCustomer.setId(customer.getId());
            customerService.updateShopifyCustomer(updatedCustomer);
        }
        return shopifyCustomerResponse;
    }
}
