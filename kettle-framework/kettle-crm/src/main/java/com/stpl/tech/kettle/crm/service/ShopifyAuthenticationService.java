package com.stpl.tech.kettle.crm.service;


import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Service;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import static java.nio.charset.StandardCharsets.UTF_8;

@Service
public class ShopifyAuthenticationService {

    public static final String HMAC_ALGORITHM = "HmacSHA256";

    public static String calculateHmac(String message, String secret) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac hmac = Mac.getInstance(HMAC_ALGORITHM);
        SecretKeySpec key = new SecretKeySpec(secret.getBytes(UTF_8), HMAC_ALGORITHM);
        hmac.init(key);

        return Base64.encodeBase64String(hmac.doFinal(message.getBytes(UTF_8)));
    }


    public static boolean checkHmac(String message, String hmac, String secret) throws InvalidKeyException, NoSuchAlgorithmException {
        return hmac.equals(calculateHmac(message, secret));
    }
}
