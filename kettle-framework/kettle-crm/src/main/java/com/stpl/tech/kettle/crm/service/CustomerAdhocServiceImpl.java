/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.crm.dao.CustomerAdhocDao;
import com.stpl.tech.kettle.offer.data.model.CustomerOfferData;

@Service
public class CustomerAdhocServiceImpl implements CustomerAdhocService {

	@Autowired
	private CustomerAdhocDao dao;

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.kettle.customer.service.CustomerAdhocService#
	 * getAllCustomerWithOrders(java.util.Date, java.util.Date)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CustomerOfferData> getAllCustomerWithOrders(Date startTime, Date endTime) {
		return dao.getAllCustomerWithOrders(startTime, endTime);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CustomerOfferData> getAllCustomerWithOrders(Date startTime, Date endTime, List<Integer> unitIds) {
		return dao.getAllCustomerWithOrders(startTime, endTime, unitIds);
	}

}