/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.crm.model.CODCustomerLoginData;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.ObjectFactory;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.COD_CUSTOMER_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + COD_CUSTOMER_SERVICES_ROOT_CONTEXT)
public class CODCustomerResources extends AbstractResources {

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    ObjectFactory objectFactory = new ObjectFactory();

    private static final Logger LOG = LoggerFactory.getLogger(CODCustomerResources.class);

    /**
     * This Method searches for Customer using his contact number
     *
     * @return Customer wrapped in CODCustomerLoginData if customer is present
     * else adds customer if customer is sent in request.
     * @throws DataUpdationException
     */
    @RequestMapping(method = RequestMethod.POST, value = "lookup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CODCustomerLoginData search(@RequestBody final CODCustomerLoginData customerData)
        throws DataUpdationException {
        LOG.info(String.format("Got request for a customer with contact number ## %s from COD ",
            customerData.getContactNumber()));
        // TODO validate phone number
        Customer customer = customerService.getCustomer(customerData.getContactNumber());
/*        if(customer != null && customer.isInternal()) {
    		throw new DataUpdationException("Customer With Contact Number " + customer.getContactNumber() + " is an Internal Employee");
       	
        }
*/
        if (customer == null && customerData.getCustomer() != null) {
            customer = customerService.addCustomer(getCustomer(customerData.getSession().getUnitId(),
                customerData.getSession().getUserId(), customerData.getContactNumber()
                , customerData.getAcquisitionBrandId()));
            customerData.setNewCustomer(true);
            cleverTapDataPushService.pushUserToCleverTap(customer.getId());
        }
        customerData.setCustomer(customer);
        return customerData;
    }

    private final Customer getCustomer(int unitId, int userId, String contactNumber, Integer brandId) {
        Customer customer = objectFactory.createCustomer();
        customer.setContactNumber(contactNumber);
        customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
        customer.setRegistrationUnitId(unitId);
        customer.setAcquisitionSource(AppConstants.CHAAYOS_COD);
        customer.setAcquisitionToken(Integer.toString(userId));
        customer.setAcquisitionBrandId(brandId);
        customer.setChaayosCustomer(brandId.equals(AppConstants.CHAAYOS_BRAND_ID));
        customer.setSmsSubscriber(true);
        customer.setOptWhatsapp(AppConstants.YES);
        return customer;
    }

    /**
     * This Method updates basic info. i.e. emailId, first name of a Customer.
     *
     * @return Customer wrapped in CODCustomerLoginData
     * @throws DataUpdationException
     */
    @RequestMapping(method = RequestMethod.POST, value = "update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CODCustomerLoginData update(@RequestBody final CODCustomerLoginData userSession)
        throws DataNotFoundException, DataUpdationException {
        LOG.info(String.format(
            "Got request for updating customer information with contact number ## %s from unit %d with emailId %s and name %s and customer productId %d",
            userSession.getCustomer().getContactNumber(), userSession.getSession().getUnitId(),
            userSession.getCustomer().getEmailId(), userSession.getCustomer().getFirstName(),
            userSession.getCustomer().getId()));
        if (userSession.getCustomer().getId() > 5) {
            customerService.updateBasicCustomerInfo(userSession.getCustomer());
            Customer updatedCustomer = customerService.getCustomer(userSession.getCustomer().getId());
            userSession.setCustomer(updatedCustomer);
                    // updating customer data on clevertap
            LOG.info("Commented clevertap push request for customer {}",updatedCustomer.getId());
            cleverTapDataPushService.pushUserToCleverTap(updatedCustomer.getId());
        }
        return userSession;
    }

    /**
     * This Method searches for Customer using his contact number
     *
     * @return Customer wrapped in CODCustomerLoginData if customer is present
     * else adds customer if customer is sent in request.
     * @throws DataUpdationException
     */
    @RequestMapping(method = RequestMethod.POST, value = "lookup-update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CODCustomerLoginData lookupUpdate(@RequestBody final CODCustomerLoginData customerData)
        throws DataUpdationException, DataNotFoundException {
        LOG.info(String.format("Got request for a customer with contact number ## %s from COD ",
            customerData.getContactNumber()));
        // TODO validate phone number
        Customer customer = customerService.getCustomer(customerData.getContactNumber());
        if (customer == null && customerData.getCustomer() != null) {
            customer = customerService.addCustomer(getCustomer(customerData.getSession().getUnitId(),
                customerData.getSession().getUserId(), customerData.getContactNumber(),
                customerData.getAcquisitionBrandId()));
            customerData.setNewCustomer(true);
            String name = customerData.getCustomer().getFirstName();
            if (customerData.getCustomer().getLastName() != null) {
                name = name + customerData.getCustomer().getLastName();
            }
            customer.setFirstName(name);
            if (customerData.getCustomer().getEmailId() != null) {
                customer.setEmailId(customerData.getCustomer().getEmailId());
            }
            customerService.updateBasicCustomerInfo(customer);
            cleverTapDataPushService.pushUserToCleverTap(customer.getId());
            customer = customerService.getCustomer(customer.getId());
        }
        customerData.setCustomer(customer);
        return customerData;
    }

    /**
     * This Method Adds new Address to a customer requires customer object in
     * the request.
     *
     * @return Address Id
     * @throws DataUpdationException
     */
    @RequestMapping(method = RequestMethod.POST, value = "add/address", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer addAddress(@RequestBody final CODCustomerLoginData userSession)
        throws DataNotFoundException, DataUpdationException {
        LOG.info(String.format(
            "Got request for adding customer address information with Id# %d contact number ## %s with emailId %s",
            userSession.getCustomer().getId(), userSession.getCustomer().getContactNumber(),
            userSession.getCustomer().getEmailId()));

        Integer addressId = null;
        if (userSession.getCustomer().getId() > 5 && userSession.getNewAddress() != null) {
            addressId = customerService.addAddress(userSession.getCustomer().getId(), userSession.getNewAddress());
        }
        return addressId;
    }

}
