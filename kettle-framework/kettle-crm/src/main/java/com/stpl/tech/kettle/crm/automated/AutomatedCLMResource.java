/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.automated;

import com.stpl.tech.kettle.core.FeedbackEventStatus;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.FeedbackStatus;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.crm.service.CustomerAdhocService;
import com.stpl.tech.kettle.crm.service.NotificationResource;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.LoyaltyTransferService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.offer.data.model.CustomerOfferData;
import com.stpl.tech.kettle.report.metadata.model.ReportOutput;
import com.stpl.tech.master.core.LaunchOfferStrategy;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.data.vo.HourlyOfferUnitMapping;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.ExternalUserManagementService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CustomerOfferMappingData;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.notification.GenericNotification;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.ReportDetailData;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

@Component
public class AutomatedCLMResource extends NotificationResource {

    private static final Logger LOG = LoggerFactory.getLogger(AutomatedCLMResource.class);

    // Skip units that are not eligible for Mother's Day Campaign, need to fix this
    // to make it configuration driven
    private final HashSet<Integer> SKIP_UNITS = new HashSet<Integer>(Arrays.asList(26039, 26022, 26043));

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CustomerAdhocService customerAdhocService;

    @Autowired
    private OfferManagementExternalService offerService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ExternalUserManagementService externalUserService;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private TokenService<FeedbackTokenInfo> tokenService;

    @Autowired
    private FeedbackManagementService feedbackService;

    @Autowired
    private LoyaltyTransferService loyaltyTransferService;

    public void autoLoyaltyReminderBeforeNDays(CustomerSMSNotificationType type, int noOfDays) throws ParseException, EmailGenerationException, IOException {
        if (!getEnvironmentProperties().getAutomatedLoyaltySMS()) {
            LOG.info("Skipping send automated loyalty reminders sms");
            return;
        }
        Date currentDate = AppUtils.getCurrentDate();
        Date startDate = AppUtils.getStartOfDayIST(AppUtils.getOldDate(currentDate, noOfDays));
        Date endDate = AppUtils.getEndOfDayIST(startDate);
        List<CustomerInfo> customers = customerService.getCustomersWithPendingLoyalty(startDate, endDate);
        for (CustomerInfo info : customers) {
            sendLoyaltyReminderMessage(type, getEnvironmentProperties().getAutomatedLoyaltySMS(), info, noOfDays==25 ? 5 : 10);
        }
        GenericNotification notification = createGenericNotification(
                "Loyalty Reminder To Customers for " + AppUtils.getFormattedDate(startDate), startDate,
                new String[]{"<EMAIL>", "<EMAIL>"});
        notification.addReportData(new ReportDetailData("Summary of Loyalty Reminder To Customers",
                toContent(customers), true, new HashSet<>()), false);
        sendGenericNotification(notification);
    }

    @Scheduled(cron = "0 0 * * * *", zone = "GMT+05:30")
//    @Scheduled(fixedRate = 120000)
    public void expireSignupOffer() throws ParseException, EmailGenerationException, IOException {
        if (!getEnvironmentProperties().getExpireSignupOffer()) {
            LOG.info("Skipping expiring signup offer");
            return;
        }
        customerService.expireSignupOffer(getEnvironmentProperties().getExpireSignupOfferDays());
    }

    @Scheduled(cron = "0 0 6 * * *", zone = "GMT+05:30")
    public void orderStatusUpdatefromKettleToDineinEvery24Hours() throws Exception {
        LOG.info("Entered in Order Status Update From Kettle To Dine Every Day 6am");
        try {
            LOG.info("Running cron job");
            String dineInEndPoint = props.getDineInCRMBaseUrl()  + "v2/cart/update-unsettled-orders-status";
            WebServiceHelper.postWithAuth(dineInEndPoint, props.getDineInToken(), null, Object.class);
        } catch (Exception e) {
            LOG.info("Exception: ", e.toString());
        }
    }

    //Turned off as these are now handled via cartesian
    // @Scheduled(fixedRate = 120000)
    //@Scheduled(cron = "0 30 10 * * *", zone = "GMT+05:30")
    public void autoLoyaltyReminderBefore15Days() throws ParseException, EmailGenerationException, IOException {
        autoLoyaltyReminderBeforeNDays(CustomerSMSNotificationType.LOYALTY_REMINDER_2, 15);

    }

    //Turned off as these are now handled via cartesian
    //@Scheduled(cron = "0 45 10 * * *", zone = "GMT+05:30")
    public void autoLoyaltyReminderBefore5Days() throws ParseException, EmailGenerationException, IOException {
        autoLoyaltyReminderBeforeNDays(CustomerSMSNotificationType.LOYALTY_REMINDER, 25);
    }

    // @Scheduled(fixedRate = 120000)
    @Scheduled(cron = "0 30 10 * * *", zone = "GMT+05:30")
    public void autoReminder() throws ParseException, EmailGenerationException, IOException {
        if (!getEnvironmentProperties().getAutomatedReminderSMS()) {
            LOG.info("Skipping send automated promotional sms");
            return;
        }
        Date currentDate = AppUtils.getCurrentDate();
        Date startDate = AppUtils.getStartOfDayIST(AppUtils.getOldDate(currentDate, 10));
        Date endDate = AppUtils.getEndOfDayIST(startDate);
        GenericNotification notification = createGenericNotification(
                "Delivery and Cafe To Customers for " + AppUtils.getFormattedDate(startDate), startDate,
                new String[]{"<EMAIL>", "<EMAIL>"});
        List<CustomerInfo> customers = customerService.getNewCustomers(UnitCategory.CAFE.name(), startDate, endDate);
        for (CustomerInfo info : customers) {
            sendDeliveryPromotionalMessage(getEnvironmentProperties().getAutomatedReminderSMS(), info);
        }
        notification.addReportData(new ReportDetailData("Summary of Delivery Reminders To Customers",
                toContent(customers), true, new HashSet<>()), false);

        List<CustomerInfo> customers1 = customerService.getNewCustomers(UnitCategory.COD.name(), startDate, endDate);
        for (CustomerInfo info : customers1) {
            sendCafePromotionalMessage(getEnvironmentProperties().getAutomatedReminderSMS(), info);
        }
        notification.addReportData(new ReportDetailData("Summary of Cafe Reminders To Customers", toContent(customers1),
                true, new HashSet<>()), false);

        sendGenericNotification(notification);
    }

    @Scheduled(cron = "0 */2 7-22 * * *", zone = "GMT+05:30")
    public void feedbackNotification() throws ParseException, EmailGenerationException, IOException {
        if (!getEnvironmentProperties().getAutomatedFeedbackSMS()) {
            // LOG.info("Skipping send automated feedback sms");
            return;
        }
        Date currentDate = AppUtils.getCurrentTimestamp();
        Date startDate = AppUtils.getStartOfDayIST(AppUtils.getCurrentBusinessDate());
        List<FeedbackEventInfo> events = new ArrayList<>();
        events.addAll(feedbackService.getPendingFeedbackEvents(FeedbackSource.SMS, startDate, currentDate));
        events.addAll(feedbackService.getPendingElaboratedFeedbackEvents(FeedbackSource.SMS, startDate, currentDate));
        if (events != null && events.size() > 0) {
            for (FeedbackEventInfo event : events) {
                try {
                    String unitName = masterCache.getUnitBasicDetail(event.getUnitId()).getName();
                    FeedbackTokenInfo token = new FeedbackTokenInfo(event.getContactNumber(), unitName,
                            event.getCustomerName(), event.getOrderId(), event.getOrderSource(), event.getEventSource(),
                            event.getFeedbackId(), event.getFeedbackEventId());
                    String jwtToken = tokenService.createToken(token, -1L);
                    String feedbackUrl = null;
                    ShortUrlData shortUrl = null;
                    boolean result = false;
                    if (event.getType() == null || FeedbackEventType.REGULAR.equals(event.getType())) {
                        feedbackUrl = (UnitCategory.COD.name().equals(event.getOrderSource())
                                ? event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointDelivery()
                                : event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointDinein())
                                + String.format("name=%s&number=%s&unit=%s&token=%s", event.getCustomerName(),
                                event.getContactNumber(), unitName, jwtToken);
                        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(event.getBrand());
                        shortUrl = smsWebServiceClient.getShortUrl(feedbackUrl);
                        event.setEventLongUrl(feedbackUrl);
                        event.setEventShortUrl(shortUrl.getUrl());
                        result = sendFeedbackMessage(getEnvironmentProperties().getAutomatedFeedbackSMS(),
                                event.getContactNumber(), event);
                    } else {
                        feedbackUrl = (UnitCategory.COD.name().equals(event.getOrderSource())
                                // feedback.endpoint.low.rating.delivery
                                ? event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointLowRatingDelivery()
                                : event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointLowRatingDinein())
                                + String.format("name=%s&number=%s&unit=%s&token=%s&rating=%s", event.getCustomerName(),
                                event.getContactNumber(), unitName, jwtToken, event.getRating());
                        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(event.getBrand());
                        shortUrl = smsWebServiceClient.getShortUrl(feedbackUrl);
                        event.setEventLongUrl(feedbackUrl);
                        event.setEventShortUrl(shortUrl.getUrl());
                        result = sendElaboratedFeedbackMessage(
                                getEnvironmentProperties().getAutomatedLowRatingFeedbackSMS(), event.getContactNumber(),
                                event);
                    }

                    if (result) {
                        feedbackService.updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(),
                                shortUrl, feedbackUrl, FeedbackEventStatus.NOTIFIED, FeedbackStatus.NOTIFIED);

                    } else {
                        feedbackService.updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(),
                                shortUrl, feedbackUrl, FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);

                    }
                } catch (IOException e) {
                    LOG.error("Error while generating the feedback message to " + event.getContactNumber(), e);
                    feedbackService.updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(), null,
                            null, FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);
                }
            }
        }

    }

    /**
     * @throws ParseException
     * @throws EmailGenerationException
     * @throws IOException
     */
   // @Scheduled(cron = "0 0 9 * * *", zone = "GMT+05:30")
    // @Scheduled(fixedRate = 10000)
    public void reSendNPSNotificationForCODCustomers() throws ParseException, EmailGenerationException, IOException {
        if (!getEnvironmentProperties().getAutomatedNPSSMS()) {
            LOG.info("Skipping send automated NPS sms");
            return;
        }
        List<FeedbackEventInfo> events = new ArrayList<>();
        events.addAll(feedbackService.getNotifiedNPSEventsForLastDay(FeedbackSource.SMS));
        LOG.info("Found {} SMS for NPS - Resend", events.size());
        Set<Integer> customers = new HashSet<>();
        if (events != null && events.size() > 0) {
            for (FeedbackEventInfo event : events) {
                if (customers.contains(event.getCustomerId())) {
                    // just to skip duplicates
                    continue;
                }
                customers.add(event.getCustomerId());
                SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(event.getBrand());
                sendNPSMessage(getEnvironmentProperties().getAutomatedNPSSMS(), event.getContactNumber(), event, smsWebServiceClient);
            }
        }
    }

    public static void main(String[] args) {
        try {
            String feedbackUrl1 = "https://stpltd.typeform.com/to/oJgh2w?"
                    + String.format("name=%s&number=%s&unit=%s&token=%s", "Vivek", "8055852367", "Good+Earth", "");
            String feedbackUrl2 = "https://stpltd.typeform.com/to/oJgh2w?"
                    + String.format("name=%s&number=%s&unit=%s&token=%s", "Vivek", "8055852367", "Good Earth", "");
            String feedbackUrl3 = "https://stpltd.typeform.com/to/oJgh2w?"
                    + String.format("name=%s&number=%s&unit=%s&token=%s", "Vivek", "8055852367", "Good%20Earth", "");
            URIBuilder builder = new URIBuilder("https://stpltd.typeform.com/to/oJgh2w?abc=kjdshashkjd");
            builder.addParameter("name", "Vivek Rawat");
            builder.addParameter("unit", "Good Earth");
            builder.addParameter("token", "ascsacasc.scsacascCQWECW.CWECWECEW_SA");
            builder.addParameter("contact", "8055852367");

            //SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(feedbackUrl1);
            //SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(feedbackUrl2);
            String s = builder.build().toURL().toString();
            System.out.println(s);
            SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(s);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Cron to Run every 2 minutes for NPS SMS SMS should not be sent between 9PM to
     * 9AM as per TRAI Rules
     */
    @Scheduled(cron = "0 */2 9-20 * * *", zone = "GMT+05:30")
    public void sendNPSNotification() throws ParseException, EmailGenerationException, IOException {
        if (!getEnvironmentProperties().getAutomatedNPSSMS()) {
            LOG.info("Skipping send automated NPS sms");
            return;
        }
        List<FeedbackEventInfo> events = new ArrayList<>();
        events.addAll(feedbackService.getPendingNPSEvents(FeedbackSource.SMS));
        LOG.info("Found {} SMS for NPS", events.size());
        for (FeedbackEventInfo event : events) {
            System.out.println(event.getFeedbackEventId());
        }
        Set<Integer> customers = new HashSet<>();
        if (events != null && events.size() > 0) {
            for (FeedbackEventInfo event : events) {
                try {
                    if (customers.contains(event.getCustomerId()) && !props.getOrderFeedbackType().equals("internal")) {
                        feedbackService.updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(),
                                null, null, FeedbackEventStatus.CANCELLED, FeedbackStatus.CANCELLED);
                        LOG.info("CANCELLED NPS SMS as Multiple Data present for customer Id {}",
                                event.getCustomerId());
                        continue;
                    }
                    customers.add(event.getCustomerId());
                    String unitName = masterCache.getUnitBasicDetail(event.getUnitId()).getName();
                    FeedbackTokenInfo token = new FeedbackTokenInfo(event.getContactNumber(), unitName,
                            event.getCustomerName(), event.getOrderId(), event.getOrderSource(), event.getEventSource(),
                            event.getFeedbackId(), event.getFeedbackEventId());
                    String jwtToken = tokenService.createToken(token, -1L);
                    String feedbackUrl = null;
                    ShortUrlData shortUrl = null;
                    boolean result = false;
                    if(props.getOrderFeedbackType().equals("internal")){
                        feedbackUrl = event.getBrand().getInternalOrderFeedbackUrl();
                    } else {
                        feedbackUrl = AppConstants.COD.equals(event.getOrderSource()) ?
                                // feedback.endpoint.nps.delivery
                                event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointNPSDelivery()
                                // feedback.endpoint.nps.cafe
                                : event.getBrand().getFeedBackUrl() + event.getBrand().getFeedbackEndpointNPSCafe();
                    }

                    URIBuilder builder = new URIBuilder(feedbackUrl);
                    builder.addParameter("name", event.getCustomerName());
                    builder.addParameter("unit", unitName);
                    builder.addParameter("token", jwtToken);
                    SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(event.getBrand());
                    shortUrl = smsWebServiceClient.getShortUrl(builder.build().toURL().toString());
                    event.setEventLongUrl(feedbackUrl);
                    event.setEventShortUrl(shortUrl.getUrl());
                    result = sendNPSMessage(getEnvironmentProperties().getAutomatedNPSSMS(), event.getContactNumber(),
                            event, smsWebServiceClient);
                    feedbackUrl = builder.build().toURL().toString();
                    if (result) {
                        Date updateTime = feedbackService.updateFeedbackEventStatus(event.getFeedbackId(),
                                event.getFeedbackEventId(), shortUrl, feedbackUrl, FeedbackEventStatus.NOTIFIED,
                                FeedbackStatus.NOTIFIED);
                        feedbackService.updateLastNPSTime(updateTime, event.getCustomerId());
                    } else {
                        feedbackService.updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(),
                                shortUrl, feedbackUrl, FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);
                    }
                } catch (IOException | URISyntaxException e) {
                    LOG.error("Error while generating the feedback message to " + event.getContactNumber(), e);
                    feedbackService.updateFeedbackEventStatus(event.getFeedbackId(), event.getFeedbackEventId(), null,
                            null, FeedbackEventStatus.FAILED, FeedbackStatus.FAILED);
                }
            }
        }

    }

    // @Scheduled(fixedRate = 120000)
    @Scheduled(cron = "0 1 * 10,11,12 * *", zone = "GMT+05:30")
    public void sendHourlyOffer() throws ParseException, EmailGenerationException, IOException {
        LOG.info("running hourly offer cron::::::::::::::::::::::::::::::::::::");
        if (!getEnvironmentProperties().getHourlyOffer() || AppUtils.isDev(props.getEnvironmentType())) {
            LOG.info("Skipping launching hourly offers");
            return;
        }
        triggerCoupons();
    }

    // @Scheduled(fixedRate = 120000)
    @Scheduled(cron = "0 0 10 10,11,12 * *", zone = "GMT+05:30")
    public void sendMassOffers() throws ParseException, EmailGenerationException, IOException {
        LOG.info("running hourly mass offer cron::::::::::::::::::::::::::::::::::::");
        if (!getEnvironmentProperties().getHourlyOffer() || AppUtils.isDev(props.getEnvironmentType())) {
            LOG.info("Skipping launching hourly offers");
            return;
        }
        List<OfferDetailData> offers = offerService
                .getAllValidOffersWithLaunchStrategy(Arrays.asList(LaunchOfferStrategy.HOURLY_FOR_EACH_UNIT.name(), LaunchOfferStrategy.HOURLY_FOR_SPECIFIC_UNITS.name()));
        if (offers != null && offers.size() > 0) {
            for (OfferDetailData offer : offers) {
                LOG.info("Sending Mass Offer Notfications for " + offer.getOfferText());
                Date endDate = AppUtils.getDayBeforeOrAfterDay(offer.getEndDate(), 2);
                if ((AppUtils.getCurrentBusinessDate().equals(offer.getStartDate())
                        || AppUtils.getCurrentBusinessDate().after(offer.getStartDate()))
                        && AppUtils.getCurrentBusinessDate().before(endDate)) {
                    Date endTime = AppUtils.getCurrentBusinessDate();
                    List<CustomerOfferMappingData> mapping = offerService
                            .getAllPendingNotificationMassOffers(offer.getOfferDetailId(), endTime);
                    if (mapping != null && mapping.size() > 0) {
                        ShortUrlData shortUrl = SolsInfiniWebServiceClient.getTransactionalClient()
                                .getShortUrl(props.getMassOfferURL());
                        for (CustomerOfferMappingData m : mapping) {
                            UnitBasicDetail unit = masterCache.getUnitBasicDetail(m.getUnitId());
                            CustomerOfferData o = new CustomerOfferData(m.getContactNumber(), m.getCustomerName(), -1,
                                    m.getUnitId(), unit.getName(), BigDecimal.ZERO, unit.getRegion(), null);
                            o.setUrl(shortUrl.getUrl());
                            o.setCouponCode(m.getCouponCode());
                            o.setValidUntil(AppUtils.getFormattedDate(offer.getEndDate()));
                            sendMassOfferMessage(true, o.getContactNumber(), o,
                                    CustomerSMSNotificationType.MASS_OFFER_WINNER_MESSAGE_REMINDER);
                            offerService.setCustomerOfferMappingDataNotified(m.getCustomerOfferMappingDataId());
                        }
                    }

                }
            }
        }
    }

    // @Scheduled(fixedRate = 600000)
    @Scheduled(cron = "0 0 12,22 * * *", zone = "GMT+05:30")
    public void markAsInternalCustomers() {
        try {
            List<String> contacts = externalUserService.getAllInternalOpsEmployeeContactNumbers();
            customerService.markAsInternalCustomers(contacts);
        } catch (Exception e) {
            LOG.error("Unable to execute marking of internal customers", e);
        }
    }

    // @Scheduled(fixedRate = 600000)
    @Scheduled(cron = "59 59 23 * * *", zone = "GMT+05:30")
    public void resetMaxUsageCount() throws ParseException, EmailGenerationException, IOException {
        LOG.info("Cron to Reset Usage Count of customer offers");
        if (!getEnvironmentProperties().getHourlyOffer()) {
            LOG.info("Skipping launching hourly offers");
            return;
        }
        List<OfferDetailData> offers = offerService.getAllOffersWithLaunchStrategy(Arrays
                .asList(LaunchOfferStrategy.HOURLY_FOR_EACH_UNIT.name(), LaunchOfferStrategy.HOURLY_FOR_SPECIFIC_UNITS.name(), LaunchOfferStrategy.N_DAY_FREE_ITEM.name()));
        if (offers != null && offers.size() > 0) {
            for (OfferDetailData offer : offers) {
                LOG.info("Reseting Usage Count of customer offers for " + offer.getOfferText());
                offerService.setUsageCountOfAllCouponsForAnOffer(offer.getOfferDetailId(), 1);
            }
        }
    }

    @Scheduled(cron = "0 0 8 * * *", zone = "GMT+05:30")
    public void markLoyaltyTransferExpired(){
        LOG.info("Running cron to mark loyalty transfers expired");
        loyaltyTransferService.markExpired();
    }



    @Override
    public AuthorizationService getAuthorizationService() {
        return null;
    }

    @Override
    public EnvironmentProperties getEnvironmentProperties() {
        return props;
    }

    @Override
    public CustomerService getCustomerService() {
        return customerService;
    }

    @Override
    public NotificationService getNotificationService() {
        return notificationService;
    }

    private void sendHourlyOffersToCustomers(OfferDetailData offer, Date startTime, Date endTime, List<Integer> unitIds, int days) {
        Map<Integer, CustomerOfferData> result = new HashMap<>();
        Map<Integer, CustomerOfferData> failedOffers = new HashMap<>();
        List<CustomerOfferData> orderDetails = null;
        if (unitIds == null || unitIds.size() == 0) {
            orderDetails = customerAdhocService.getAllCustomerWithOrders(startTime, endTime);
        } else {
            orderDetails = customerAdhocService.getAllCustomerWithOrders(startTime, endTime, unitIds);
        }
        if (orderDetails == null || orderDetails.size() == 0) {
            return;
        }
        LOG.info("Found " + orderDetails.size() + " orders during this hour");
        orderDetails.forEach(item -> LOG.info(item.toString()));
        Set<String> customerWithOffer = offerService.getAllCustomerWithOffer(offer.getOfferDetailId());

        // Code to exclude all the current cycle winners
        Set<String> currentCycleWinners = offerService.getCurrentCycleWinners(
                Arrays.asList(LaunchOfferStrategy.HOURLY_FOR_EACH_UNIT, LaunchOfferStrategy.HOURLY_FOR_SPECIFIC_UNITS));
        if (currentCycleWinners != null && !currentCycleWinners.isEmpty()) {
            LOG.info("Adding {} contacts into exclusion list who have already won yearly coupon",
                    currentCycleWinners.size());
            customerWithOffer.addAll(currentCycleWinners);

        }

        LOG.info("Found Customers that have already got offers");
        LOG.info(Arrays.toString(customerWithOffer.toArray()));
        Map<Integer, List<CustomerOfferData>> unitToCustomerDetails = map(orderDetails);
        List<CouponDetailData> couponCodes = offerService.getAllInactiveCoupons(offer.getOfferDetailId());
        int count = 0;
        for (Integer unit : unitToCustomerDetails.keySet()) {
            List<CustomerOfferData> customers = unitToCustomerDetails.get(unit);
            CustomerOfferData customerContactNumber = selectRandom(customers, customerWithOffer);
            if (customerContactNumber != null) {
                customerWithOffer.add(customerContactNumber.getContactNumber());
                if (count < couponCodes.size()) {
                    CouponDetailData coupon = couponCodes.get(count);
                    customerContactNumber.setCouponCode(coupon.getCouponCode());
                    customerContactNumber.setValidUntil(AppUtils.getFormattedDate(offer.getEndDate()));
                    customerContactNumber.setCouponDetailId(coupon.getCouponDetailId());
                    customerContactNumber.setOfferDetailId(offer.getOfferDetailId());
                    count++;
                    result.put(unit, customerContactNumber);
                } else {
                    failedOffers.put(unit, customerContactNumber);
                }
            }
            /*
             * if (failedOffers != null && failedOffers.size() > 0) {
             * offerService.deactiveOffer(offer.getOfferDetailId()); }
             */
        }

        try {
            sendLaunchOffer(offer, unitToCustomerDetails, result, failedOffers, endTime, days);
        } catch (IOException | EmailGenerationException e) {
            new ErrorNotification("Failed to send Launch Offer Messages", "Error while sending Launch Offer Messages",
                    e, props.getEnvironmentType()).sendEmail();
        }
    }

    public void triggerCoupons() {
        List<OfferDetailData> offers = offerService.getAllValidOffersWithLaunchStrategy(Arrays.asList(
                LaunchOfferStrategy.HOURLY_FOR_EACH_UNIT.name(), LaunchOfferStrategy.HOURLY_FOR_SPECIFIC_UNITS.name()));
        if (offers != null && offers.size() > 0) {
            for (OfferDetailData offer : offers) {
				/*int days = 365;
				List<Integer> unitIds = new ArrayList<>();
				for (OfferMetadata metadata : offer.getMetaDataMappings()) {
					if (CouponMappingType.UNIT.name().equals(metadata.getMappingType())) {
						unitIds.add(Integer.valueOf(metadata.getMappingValue()));
					}
					if (CouponMappingType.NUMBER_OF_DAYS.name().equals(metadata.getMappingType())) {
						days = Integer.valueOf(metadata.getMappingValue());
					}
				}*/
                HourlyOfferUnitMapping hourlyOfferUnitMapping = offerService.getHourlyOfferUnitMapping(offer.getOfferDetailId());
                if (LaunchOfferStrategy.HOURLY_FOR_EACH_UNIT.name().equals(offer.getLaunchStrategy())
                        || LaunchOfferStrategy.HOURLY_FOR_SPECIFIC_UNITS.name().equals(offer.getLaunchStrategy())) {
                    Date startTime = AppUtils.getPreviousDateHourIST();
                    Date endTime = AppUtils.getCurrentDateHourIST();
                    sendHourlyOffersToCustomers(offer, startTime, endTime, hourlyOfferUnitMapping.getUnitIds(), hourlyOfferUnitMapping.getNumberOfDays());
                }
            }
        }
    }

    /**
     * @param orderDetails
     * @return
     */
    private Map<Integer, List<CustomerOfferData>> map(List<CustomerOfferData> orderDetails) {
        Map<Integer, List<CustomerOfferData>> map = new HashMap<>();
        for (CustomerOfferData c : orderDetails) {
            if (this.SKIP_UNITS.contains(c.getUnitId()) || c.getAmount().compareTo(new BigDecimal("199.00")) < 0) {
                continue;
            }
            if (!map.containsKey(c.getUnitId())) {
                map.put(c.getUnitId(), new ArrayList<>());
            }
            map.get(c.getUnitId()).add(c);
        }
        for (Integer key : map.keySet()) {
            List<CustomerOfferData> data = map.get(key);
            LOG.info("Unit Id " + key + " Order Size " + data.size());
            LOG.info("Orders \n" + Arrays.toString(data.toArray()));
        }
        return map;
    }

    /**
     * @param offer
     * @param result
     * @throws IOException
     * @throws EmailGenerationException
     */
    private void sendLaunchOffer(OfferDetailData offer, Map<Integer, List<CustomerOfferData>> unitToCustomerDetails,
                                 Map<Integer, CustomerOfferData> result, Map<Integer, CustomerOfferData> failedOffers, Date endTime, int days)
            throws IOException, EmailGenerationException {
        if (result != null && result.size() > 0) {
            for (CustomerOfferData customer : result.values()) {
                offerService.setContactNumber(customer.getContactNumber(), customer.getCouponCode());
                CustomerOfferMappingData mapping = new CustomerOfferMappingData();
                mapping.setContactNumber(customer.getContactNumber());
                mapping.setCouponCode(customer.getCouponCode());
                mapping.setCouponDetailId(customer.getCouponDetailId());
                mapping.setCreationTime(AppUtils.getCurrentTimestamp());
                mapping.setCustomerName(customer.getCustomerName());
                mapping.setOfferDetailId(customer.getOfferDetailId());
                mapping.setUnitId(customer.getUnitId());
                mapping.setNotified(AppConstants.NO);
                mapping.setAcquisitionSource("CAFE_VISIT");
                mapping.setOfferCodeUsed("Y");
                mapping.setOfferStrategy(offer.getLaunchStrategy());
                mapping.setOfferDayCount(days);
                offerService.add(mapping);
                String feedbackUrl = props.getMassOfferURL() + "?couponCode=" + customer.getCouponCode();
                ShortUrlData shortUrl = SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(feedbackUrl);
                customer.setUrl(shortUrl != null ? shortUrl.getUrl() : feedbackUrl);
                sendMassOfferMessage(true, customer.getContactNumber(), customer,
                        CustomerSMSNotificationType.MASS_OFFER_WINNER_MESSAGE);
            }
            GenericNotification notification = createGenericNotification(
                    (failedOffers.size() > 0 ? "FAILURE :" : "") + "Launch Offer To Customers for "
                            + offer.getOfferText() + " " + AppUtils.getFormattedTimeWithHours(endTime),
                    AppUtils.getCurrentTimestamp(), new String[]{"<EMAIL>", "<EMAIL>",
                            "<EMAIL>", "<EMAIL>"});
            notification.addReportData(new ReportDetailData("Summary of Launch Offer To Customers",
                    toContent(result, failedOffers, unitToCustomerDetails), true, new HashSet<>()), false);
            sendGenericNotification(notification);
        }

    }

    private CustomerOfferData selectRandom(List<CustomerOfferData> numbers, Set<String> filterList) {
        if (numbers == null || numbers.size() == 0) {
            return null;
        }
        Random rand = new Random();
        int index = rand.nextInt(numbers.size());
        CustomerOfferData randomElement = numbers.get(index);
        if (filterList.contains(randomElement.getContactNumber())) {
            LOG.info("Found {} in contact filterList, removing it and re-calculating a new contact",
                    randomElement.getContactNumber());
            numbers.remove(randomElement);
            return selectRandom(numbers, filterList);
        }
        return randomElement;
    }

    private List<List<String>> toContent(List<CustomerInfo> customers) {
        List<List<String>> list = new ArrayList<>();
        List<String> header = new ArrayList<>();
        header.add("Customer Id");
        header.add("Customer Name");
        // header.add("Contact Number");
        list.add(header);
        for (CustomerInfo info : customers) {
            List<String> data = new ArrayList<>();
            data.add("" + info.getCustomerId());
            data.add(info.getFirstName());
            // data.add(info.getContactNumber());
            list.add(data);
        }
        return list;
    }

    private List<List<String>> toContent(Map<Integer, CustomerOfferData> results,
                                         Map<Integer, CustomerOfferData> failedOffers, Map<Integer, List<CustomerOfferData>> totals) {

        List<List<String>> list = new ArrayList<>();
        List<String> header = new ArrayList<>();
        header.add("Unit Id");
        header.add("Unit Name");
        header.add("Total Qualified Customers");
        header.add("Customer Name");
        header.add("Contact No.");
        // header.add("Contact Number");
        list.add(header);
        if (failedOffers != null && failedOffers.size() > 0) {
            for (CustomerOfferData info : failedOffers.values()) {
                list.add(getList(info, 0));
            }
        }
        if (results != null && results.size() > 0) {
            for (CustomerOfferData info : results.values()) {
                List<CustomerOfferData> data = totals.get(info.getUnitId());
                LOG.info("Printing for unit " + info.getUnitId() + " size is " + data.size());
                list.add(getList(info, data.size()));
            }
        }
        return list;
    }

    private List<String> getList(CustomerOfferData info, int total) {
        List<String> data = new ArrayList<>();
        data.add("" + info.getUnitId());
        data.add(info.getUnitName());
        data.add("" + total);
        data.add(info.getCustomerName());
        data.add(info.getContactNumber() == null ? "FAILED" : "XXXXXX" + info.getContactNumber().substring(6));
        return data;
    }

    private GenericNotification createGenericNotification(String subject, Date startDate, String[] toEmails) {
        GenericNotification notification = new GenericNotification();
        notification.setAttachFile(true);
        notification.setFromEmail("<EMAIL>");
        notification.setNeedsCompression(true);
        notification.setOutputType(ReportOutput.EXCEL);
        notification.setSubject(subject);
        notification.setToEmails(toEmails);
        return notification;
    }

    @Scheduled(cron = "0 0 6 * * *", zone = "GMT+05:30")
    public void deactivateOldSubscriptions() {
        LOG.info("Updating Old Subscriptions Plan and Events as IN_ACTIVE at 6AM every day");
        customerService.deactivateOldSubscriptionPlans();
        customerService.deactivateOldSubscriptionPlanEvents();
    }
}