package com.stpl.tech.kettle.crm.service;

import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.REFERRAL_SERVICE_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.ws.rs.core.MediaType;

import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.google.api.services.drive.model.File;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.file.management.GoogleSheetLoader;
import com.stpl.tech.kettle.crm.model.ReferralLinkData;
import com.stpl.tech.kettle.crm.model.SMSRequest;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.ReferralService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.referral.model.AllotChaayosCashRequestBody;
import com.stpl.tech.kettle.referral.model.ReferralNotificationRequest;
import com.stpl.tech.kettle.referral.model.ReferralRequest;
import com.stpl.tech.kettle.referral.model.ReferralResponse;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.acl.service.CSRFTokenService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * All Referral Request URL should created and handled here
 *
 */
@Controller
@RequestMapping(value = API_VERSION + SEPARATOR
		+ REFERRAL_SERVICE_ROOT_CONTEXT, produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
public class ReferralResource extends NotificationResource {

	private static final int BATCH_SIZE_OF_EXPIRAY = 1000;

	private static final Logger LOG = LoggerFactory.getLogger(ReferralResource.class);

	@Autowired
	protected ReferralService referralService;
	@Autowired
	protected CustomerService customerService;
	@Autowired
	protected AuthorizationService authorizationDao;
	@Autowired
	protected EnvironmentProperties environment;
	@Autowired
	private NotificationService notificationService;
	@Autowired
	private CSRFTokenService csrftokenService;

	// TODO SIROHI
	/**
	 * Submit Referral by Referrer Code
	 *
	 * @param request
	 * @return
	 * @throws DataUpdationException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "submit/code")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ReferralResponse submit(@RequestBody ReferralRequest request)
			throws DataUpdationException, AuthenticationFailureException {
		validateRequest(request.getToken(), true);
		return submitCode(request, false);
	}

	@RequestMapping(method = RequestMethod.POST, value = "submit/code/dinein")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ReferralResponse submitForDineIn(@RequestBody ReferralRequest request)
			throws DataUpdationException, AuthenticationFailureException {
		return submitCode(request, false);
	}



	/**
	 * Submit Referral by Referrer Code
	 *
	 * @param request
	 * @return
	 * @throws DataUpdationException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "validate")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ReferralResponse validate(@RequestBody ReferralRequest request)
			throws DataUpdationException, AuthenticationFailureException {
		validateRequest(request.getToken(), false);
		ReferralResponse response;
		try {
			response = getResponse(request, true, false);
			if (!response.isStatus()) {
				removeToken(request.getToken());
			}
		} catch (Exception e) {
			removeToken(request.getToken());
			LOG.error("Error in validation", e);
			throw e;
		}
		return response;
	}



	@RequestMapping(method = RequestMethod.POST, value = "otp/resend")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean resendOTP(@RequestParam String contact) throws DataUpdationException, DuplicateRequestException {
		LOG.info("Request from referral-service to resend OTP for contact: {}", contact);
		return resendCustomerAuthorizationOTP(false, contact, false, null);
	}

	@RequestMapping(method = RequestMethod.POST, value = "generate-token", produces = MediaType.TEXT_PLAIN)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String getCSRFToken(@RequestBody String url) throws DataNotFoundException {
		return csrftokenService.getToken(url);
	}

	@RequestMapping(method = RequestMethod.POST, value = "cash/activate")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean activateCashPacketsAPI() {
		LOG.info("Request from referral-service to activate Cash packets");
		activateCashPackets();
		return true;
	}

	@RequestMapping(method = RequestMethod.POST, value = "cash/expire")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean expireCashPacketsAPI() {
		LOG.info("Request from referral-service to expire Cash packets");
		while(referralService.countExpiringPackets() > 0) {
			referralService.expireCashPackets(BATCH_SIZE_OF_EXPIRAY);
		}
		return true;
	}

	@RequestMapping(method = RequestMethod.POST, value = "generate/refcode")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean generateRefCodeForCustomers() {
		LOG.info("Request from referral-service to generate refcodes");
		referralService.generateRefCodes();
		return true;
	}

	@RequestMapping(method = RequestMethod.POST, value = "sms/send")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean sendSMSToCustomer(@RequestBody ReferralNotificationRequest request) {
		LOG.info("Request from referral-service to generate refcodes");
		referralService.sendReferralSMS(request.getContacts(), request.getCampaign(), request.getSource());
		return true;
	}

	@RequestMapping(method = RequestMethod.GET, value = "generate/link")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean generateLink(@RequestParam(name = "fileName") final String fileName,
			@RequestParam(name = "campaign") final String campaign, @RequestParam(name = "source") final String source,
			@RequestParam(name = "email") final String email) throws EmailGenerationException {
		// String outputFile = environment.getBasePath() + "/referrals/" + fileName;
		String outputFile = environment.getBasePath() + "/referrals/" + fileName
				+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
		GoogleSheetLoader loader = new GoogleSheetLoader();
		try {
			LOG.info("Downloading file {} ", fileName);
			File file = loader.getFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
					TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
			if (file == null) {
				throw new RuntimeException("File Not Found " + fileName);
			}
			loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
					AppConstants.EXCEL_MIME_TYPE, outputFile);
			FileInputStream stream = new FileInputStream(new java.io.File(outputFile));
			Workbook workbook;
			if (file.getName().endsWith("xls")) {
				workbook = new HSSFWorkbook(stream);
			} else {
				workbook = new XSSFWorkbook(stream);
			}
			SheetParser parser = new SheetParser();
			List<ExcelParsingException> errors = new ArrayList<>();
			List<ReferralLinkData> entityList = parser.createEntity(workbook.getSheetAt(0), ReferralLinkData.class,
					errors::add);
			workbook.close();
			LOG.info("ReferralContacts Found " + entityList.size());

			List<String> contacts = entityList.stream().map(ReferralLinkData::getContactNumber)
					.collect(Collectors.toList());
			List<CustomerInfo> customers = customerService.getCustomerWithReferralCode(contacts);
			List<ReferralLinkData> output = new ArrayList<>();
			for (CustomerInfo c : customers) {
				output.add(getReferralLinkData(c, campaign, source));
			}

			String outputWorkBookName = environment.getBasePath() + "/referrals/" + fileName
					+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
			Workbook outputWorkBook = new XSSFWorkbook();
			ExcelWriter writer = new ExcelWriter(outputWorkBook);
			writer.writeSheet(output, ReferralLinkData.class);
			outputWorkBook.write(new FileOutputStream(outputWorkBookName));
			outputWorkBook.close();
			sendEmail(outputWorkBookName, email, environment.getEnvironmentType());
			return true;
		} catch (IOException e) {
			LOG.error("Failed to download the file", e);
			return false;
		}

	}

	public static void main(String[] args) {
		ReferralResource r = new ReferralResource();
		try {
			r.generateLink("referral_contacts", "ABC", "XYX", "<EMAIL>,<EMAIL>");
		} catch (EmailGenerationException e) {
			LOG.error("Error", e);
		}
	}

	private void sendEmail(String outputWorkBookFileName, final String email, final EnvType env)
			throws FileNotFoundException, IOException, EmailGenerationException {
		List<AttachmentData> attachments = new ArrayList<>();
		AttachmentData data = new AttachmentData(
				IOUtils.toByteArray(new FileInputStream(new java.io.File(outputWorkBookFileName))),
				outputWorkBookFileName, AppConstants.EXCEL_MIME_TYPE);
		attachments.add(data);
		new EmailNotification() {

			@Override
			public String subject() {
				return "Referral Link File " + AppUtils.getCurrentDateISTFormatted();
			}

			@Override
			public String[] getToEmails() {
				return email.split(",");
			}

			@Override
			public String getFromEmail() {
				return "<EMAIL>";
			}

			@Override
			public EnvType getEnvironmentType() {
				return env;
			}

			@Override
			public String body() throws EmailGenerationException {
				return "File Attached";
			}
		}.sendRawMail(attachments);
	}

	private ReferralLinkData getReferralLinkData(CustomerInfo c, String campaign, String source) throws IOException {
		String url = createReferralUrl(c.getRefCode(), c.getFirstName(), campaign, source);
		ShortUrlData shortUrl = SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(url);
		ReferralLinkData data = new ReferralLinkData();
		data.setReferralLink(shortUrl.getUrl());
		data.setReferralOriginalLink(url);
		data.setContactName(c.getFirstName());
		data.setContactNumber(c.getContactNumber());
		data.setReferralCode(c.getRefCode());
		data.setCustomerId(c.getCustomerId() + "");
		data.setReferralAvailable(c.getIsRefSubscriber());
		data.setCampaign(campaign);
		data.setSource(source);
		return data;
	}

	private String createReferralUrl(String refCode, String name, String campaign, String source) {

		String s = "https://cafes.chaayos.com/refer?";

		if (refCode != null && refCode.trim().length() > 0) {
			s = s + "r=" + refCode.trim();
		}
		if (name != null && name.trim().length() > 0) {
			s = s + "&n=" + name;
		}
		if (campaign != null && campaign.length() > 0) {
			s = s + "&c=" + campaign;
		}
		if (source != null && source.length() > 0) {
			s = s + "&s=" + source;
		}

		return s;
	}

	@Scheduled(cron = "0 15 05 * * *", zone = "GMT+05:30")
	public void refreshCache() throws DataNotFoundException, IOException, EmailGenerationException {
		LOG.info("CRON to refresh CSRF Tokens");
		try {
			csrftokenService.clearAll();
			LOG.info("Refresh CSRF Tokens :: SUCCESSFUL");
		} catch (Exception e) {
			LOG.error("Refresh CSRF Tokens :: FAILED", e);
		}
	}

	@Scheduled(cron = "0 30 09 * * *", zone = "GMT+05:30")
	public void activateCashPacketsCron() throws DataNotFoundException, IOException, EmailGenerationException {
		LOG.info("CRON to activate cash packets");
		activateCashPackets();
	}

	@RequestMapping(method = RequestMethod.POST, value = "generic/sms/send", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean sendGenericSms(@RequestBody final SMSRequest request) throws AuthenticationFailureException {
		LOG.info("Generic SMS Request \n"+request);
		validateRequest(request.getToken(), true);
		return sendSMS(request.isSendNotification(), request);
	}

	private void activateCashPackets() {
		try {
			Map<Integer, org.springframework.data.util.Pair<Date,BigDecimal>> customerMap = referralService.activateCashPackets();
			LOG.info("Cash packets activation :: SUCCESSFUL");
			referralService.notifyRefSuccess(customerMap);
		} catch (Exception e) {
			LOG.error("Cash packets activation :: FAILED", e);
		}
	}

	@Scheduled(cron = "0 30 05 * * *", zone = "GMT+05:30")
	public void expireCashPacketsCron() throws DataNotFoundException, IOException, EmailGenerationException {
		LOG.info("CRON to expire cash packets");
		try {
			while(referralService.countExpiringPackets() > 0) {
				referralService.expireCashPackets(BATCH_SIZE_OF_EXPIRAY);
			}
			LOG.info("CRON to expire cash packets :: SUCCESSFUL");
		} catch (Exception e) {
			LOG.error("CRON to expire cash packets :: FAILED", e);
		}
	}

	private boolean validateRequest(String token, boolean removeToken)
			throws AuthenticationFailureException {
		if (csrftokenService.contains(token)) {
			LOG.info("Found Token - " + token);
			if (removeToken) {
				removeToken(token);
			}
			return true;
		}else {
			LOG.info("Not Found Token - " + token);
		}
		throw new AuthenticationFailureException("Invalid request");
	}

	@RequestMapping(method = RequestMethod.POST, value="allot-chaayos-cash",produces = MediaType.APPLICATION_JSON,consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<Integer> allotChaayosCash(@RequestBody AllotChaayosCashRequestBody a){
		try {
			List<Integer> cashPacketDataListForFailedCustomers = new ArrayList<>();
			Date creationDate = AppUtils.getCurrentTimestamp();
			Date expirationDate = AppUtils.addDays(creationDate, a.getValidityInDays() + a.getLagInDays());
			for (Integer customerId : a.getCustomerIds()) {
				boolean status = referralService.allotChaayosCash(customerId, a.getValidityInDays(), a.getLagInDays(), a.getAmount(),
						creationDate, expirationDate, a.getCashMetadataType(), a.getCashTransactionCode(),
						a.getSendNotification(), a.getPublishEvent(), a.getComment());
				if (!status) {
					cashPacketDataListForFailedCustomers.add(customerId);
				}
			}

			return cashPacketDataListForFailedCustomers;
		}
		catch (Exception e){
			LOG.error("ERROR: ", e);
			return null;
		}
	}




	@Override
	public AuthorizationService getAuthorizationService() {
		return authorizationDao;
	}

	@Override
	public EnvironmentProperties getEnvironmentProperties() {
		return environment;
	}

	@Override
	public CustomerService getCustomerService() {
		return customerService;
	}

	@Override
	public NotificationService getNotificationService() {
		return notificationService;
	}
}
