/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.crm.model.SMSRequest;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.ReferralService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.SMSToken;
import com.stpl.tech.kettle.offer.data.model.CustomerOfferData;
import com.stpl.tech.kettle.referral.model.ReferralRequest;
import com.stpl.tech.kettle.referral.model.ReferralResponse;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.acl.service.CSRFTokenService;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.service.AbstractAutomatedReports;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.OtpType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.jms.JMSException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public abstract class NotificationResource extends AbstractAutomatedReports {

	private static final Logger LOG = LoggerFactory.getLogger(NotificationResource.class);

	@Autowired
	private SMSClientProviderService providerService;
	@Autowired
	private CSRFTokenService csrftokenService;
	@Autowired
	protected ReferralService referralService;
	@Autowired
	protected CustomerService customerService;
	@Autowired
	protected EnvironmentProperties props;
	@Autowired
	private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;
	@Autowired
	private CleverTapDataPushService cleverTapDataPushService;
	@Autowired
	private ApplicationContext applicationContext;

	protected boolean sendCustomerAuthorizationOTP(boolean lastFourDigits, String contactNumber, Integer partnerId) {
		/*
		 * Otp check for blacklisted contact
		 */
//		if (customerService.isBlackListed(contactNumber)) {
//			return false;
//		}
		try {
			Customer customer = customerService.getCustomer(contactNumber);
			if(Objects.nonNull(customer) && !AppConstants.DEFAULT_COUNTRY_CODE.equalsIgnoreCase(customer.getCountryCode())
					&& customer.isEmailVerified()) {
				customerService.sendOTPEmail(customer, null);
				return true;
			}
			String token = getNotificationService().getOTPMapperInstance().generateOTP(lastFourDigits,
					OtpType.KETTLE_CRM, contactNumber, getEnvironmentType());
			return sendCustomerAuthorizationMessage(contactNumber, token, partnerId,null);
		} catch (DuplicateRequestException e) {
			LOG.error("Error in send Customer Authorization OTP", e);
			return false;
		}
	}

	protected boolean sendCustomerAuthorizationOTPBrandWise(boolean lastFourDigits, String contactNumber,
															Integer partnerId,Integer brandId) {

		try {
			Customer customer = customerService.getCustomer(contactNumber);
			if(Objects.nonNull(customer) && !AppConstants.DEFAULT_COUNTRY_CODE.equalsIgnoreCase(customer.getCountryCode())
					&& customer.isEmailVerified()) {
				customerService.sendOTPEmailBrandWise(customer, null,brandId);
				return true;
			}
			String token = getNotificationService().getOTPMapperInstance().generateOTP(lastFourDigits,
					OtpType.KETTLE_CRM, contactNumber, getEnvironmentType());
			return sendCustomerAuthorizationMessage(contactNumber, token, partnerId,brandId);
		} catch (DuplicateRequestException e) {
			LOG.error("Error in send Customer Authorization OTP", e);
			return false;
		}
	}

	public void removeToken(String token) {
		try {
			if (token != null) {
				csrftokenService.remove(token);
			}
		} catch (Exception e) {
			LOG.error("Error Removing Token", e);
		}
	}

	public ReferralResponse submitCode(ReferralRequest request, boolean skipOtpVerification) {
		ReferralResponse response;
		try {
			response = getResponse(request, false, skipOtpVerification);
			if (!response.isStatus()) {
				removeToken(request.getToken());
			}
		} catch (Exception e) {
			removeToken(request.getToken());
			LOG.error("Error in validation", e);
			throw e;
		}
		return response;
	}

	public ReferralResponse getResponse(ReferralRequest request, boolean isValidationOnly,
			boolean skipOtpVerification) {
		LOG.info("Referral request for {}, {}", JSONSerializer.toJSON(request),
				isValidationOnly ? "Validation" : "Submit");

		if (AppUtils.isBlank(request.getSignUpRefCode())) {
			return new ReferralResponse("Referral Code not Provided", false);
		}

		if (isValidationOnly) {
			ReferralResponse response = referralService.submitReferralByCode(request, true);
			if (response.isStatus()) {
				// send OTP to the customer
				LOG.info("Request from referral-service to generate otp for customer with contact number: {}",
						request.getContact());
				boolean sentOtp = sendCustomerAuthorizationOTP(false, request.getContact(), null);
				response.setOtpSent(sentOtp);
			}
			return response;
		} else {
			boolean otpVerified;
			if (skipOtpVerification) {
				otpVerified = true;
			} else {
				otpVerified = verifyOTPInCurrentSession(request.getOtp(), request.getContact());
			}
			if (otpVerified) {
				return referralService.submitReferralByCode(request, false);
			} else {
				return new ReferralResponse("Incorrect OTP!", false);
			}
		}
	}

	protected boolean resendCustomerAuthorizationOTP(boolean lastFourDigits, String contactNumber,
													 boolean isEmailAllowed, Integer partnerId) {
		/*
		 * Otp check for blacklisted contact
		 */
//		if (customerService.isBlackListed(contactNumber)) {
//			return false;
//		}
		try {
			Customer customer = customerService.getCustomer(contactNumber);
			if(Objects.nonNull(customer) && ((!AppConstants.DEFAULT_COUNTRY_CODE.equalsIgnoreCase(customer.getCountryCode()) || isEmailAllowed))
					&& customer.isEmailVerified()) {
				customerService.sendOTPEmail(customer, null);
				return true;
			}
			String token = getNotificationService().getOTPMapperInstance().getOTP(OtpType.KETTLE_CRM, contactNumber);
			if (token == null) {
				token = getNotificationService().getOTPMapperInstance().generateOTP(lastFourDigits, OtpType.KETTLE_CRM,
						contactNumber, getEnvironmentType());
			}
			return sendCustomerAuthorizationMessage(contactNumber, token, partnerId,null);
		} catch (DuplicateRequestException e) {
			LOG.error("Error in resend send Customer Authorization OTP", e);
			return false;
		}
	}

	protected boolean resendCustomerAuthorizationOTPBrandWise(boolean lastFourDigits, String contactNumber,
													 boolean isEmailAllowed, Integer partnerId, Integer brandId) {

		try {
			Customer customer = customerService.getCustomer(contactNumber);
			if(Objects.nonNull(customer) && ((!AppConstants.DEFAULT_COUNTRY_CODE.equalsIgnoreCase(customer.getCountryCode()) || isEmailAllowed))
					&& customer.isEmailVerified()) {
				customerService.sendOTPEmailBrandWise(customer, null,brandId);
				return true;
			}
			String token = getNotificationService().getOTPMapperInstance().getOTP(OtpType.KETTLE_CRM, contactNumber);
			if (token == null) {
				token = getNotificationService().getOTPMapperInstance().generateOTP(lastFourDigits, OtpType.KETTLE_CRM,
						contactNumber, getEnvironmentType());
			}
			return sendCustomerAuthorizationMessage(contactNumber, token, partnerId,brandId);
		} catch (DuplicateRequestException e) {
			LOG.error("Error in resend send Customer Authorization OTP", e);
			return false;
		}
	}

	protected  boolean resendCustomerAuthorizationOTPViaIVR(boolean lastFourDigits, String contactNumber){
		try {
			String token = getNotificationService().getOTPMapperInstance().getOTP(OtpType.KETTLE_CRM, contactNumber);
			if (token == null) {
				token = getNotificationService().getOTPMapperInstance().generateOTP(lastFourDigits, OtpType.KETTLE_CRM,
						contactNumber, getEnvironmentType());
			}
			return sendCustomerAuthorizationMessageViaIVR(contactNumber, token);
		} catch (DuplicateRequestException e) {
			LOG.error("Error in resend send Customer Authorization OTP ", e);
			return false;
		}
	}

	protected boolean sendCustomerAuthorizationMessageViaIVR(String contactNumber, String token) {
		if(Objects.nonNull(token) && Objects.nonNull(contactNumber)){
			return getNotificationService().sendOTPRequestViaIVR(CustomerSMSNotificationType.OTP_VIA_IVR.name(),contactNumber,providerService.getSMSClient(CustomerSMSNotificationType.OTP_VIA_IVR.getTemplate().getSMSType(),ApplicationName.KETTLE_CRM),getEnvironmentProperties().getSendAutomzatedOTPViaIVR(),null, token,getEnvironmentProperties().getIVRId());
		}
		return false ;
	}

	protected boolean sendCustomerAuthorizationMessage(String contactNumber, String token, Integer partnerId, Integer brandId) {
		try {
			Customer customer = customerService.getCustomer(contactNumber);
			String contact = AppUtils.extractContactFromBrandContactNumber(contactNumber,customerService.getBrandContactCode(brandId));
			if(Objects.nonNull(customer) && !AppConstants.DEFAULT_COUNTRY_CODE.equalsIgnoreCase(customer.getCountryCode())
					&& customer.isEmailVerified()) {
				if(Objects.nonNull(brandId) && brandId == AppConstants.DOHFUL_BRAND_ID){
					customerService.sendOTPEmailBrandWise(customer, token,brandId);
				}
				else{
					customerService.sendOTPEmail(customer, token);
				}
				return true;
			}
			CustomerSMSNotificationType notificationType = CustomerSMSNotificationType.OTP_MESSENGER;
			if (Objects.nonNull(brandId) && brandId.equals(AppConstants.DOHFUL_BRAND_ID)) {
				notificationType = CustomerSMSNotificationType.DOHFUL_OTP_MESSENGER;
			}
			else if (Objects.nonNull(partnerId) && partnerId == AppConstants.WEB_APP_CHANNEL_PARTNER_CODE) {
				LOG.info("[OTP] Channel partner {}", partnerId);
				notificationType = CustomerSMSNotificationType.OTP_MESSENGER_SNP;
			}
			String message = notificationType.getMessage(getToken(token));
			boolean sentOtp = getNotificationService().sendNotification(
					notificationType.name(), message, contact,
					providerService.getSMSClient(notificationType.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					getEnvironmentProperties().getSendAutomatedOTPSMS(), null);
			getAuthorizationService().createSMSAuthorizationRequest(contactNumber, token, message);
			return sentOtp;
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the OTP message to " + contactNumber, e);
		}
		return false;
	}

	protected boolean sendWelcomeMessage(Customer customer) {
		try {
			if(Objects.nonNull(customer) && customer.getOptWhatsapp().equals(AppConstants.YES)){
				try {
					customerCommunicationEventPublisher.publishCustomerCommunicationEvent(props.getEnvironmentType().name(),
							getNotificationPayload(CustomerSMSNotificationType.WELCOME_MESSAGE, customer));
				} catch (Exception e){
					LOG.error("Exception Faced While Publishing Whatsapp Optin for Customer ::: {}",customer.getContactNumber());
				}
			}
			String message = CustomerSMSNotificationType.WELCOME_MESSAGE.getMessage(customer.getFirstName());
			/*return getNotificationService().sendNotification(CustomerSMSNotificationType.WELCOME_MESSAGE.name(),
					message, customer.getContactNumber(),
					providerService.getSMSClient(CustomerSMSNotificationType.WELCOME_MESSAGE.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					getEnvironmentProperties().getSendAutomatedWelcomeSMS(),getNotificationPayload(CustomerSMSNotificationType.WELCOME_MESSAGE,customer));*/
		} catch (Exception e) {
			LOG.error("Error while sending the welcome message to " + customer.getContactNumber(), e);
		}
		return false;
	}

	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, Customer customerResponse) {
		NotificationPayload payload = new NotificationPayload();
		payload.setCustomerId(customerResponse.getId());
		payload.setOrderId(0);
		payload.setMessageType(type.name());
		payload.setContactNumber(customerResponse.getContactNumber());
		payload.setWhatsappOptIn(customerResponse.getOptWhatsapp().equals(AppConstants.YES));
		payload.setRequestTime(AppUtils.getCurrentTimestamp());
		payload.setSendWhatsapp(type.isWhatsapp());
		Map<String,String> map = new HashMap<>();
		map.put("firstName",customerResponse.getFirstName());
		payload.setPayload(map);
		return payload;
	}

	protected boolean sendAsyncSuccessMessage(String customerName, String contactNumber) {
		try {
			String message = CustomerSMSNotificationType.ASYNC_ORDER_SUCCESS_MESSAGE.getMessage(customerName);
			return getNotificationService().sendNotification(
					CustomerSMSNotificationType.ASYNC_ORDER_SUCCESS_MESSAGE.name(), message, contactNumber,
					providerService.getSMSClient(
							CustomerSMSNotificationType.ASYNC_ORDER_SUCCESS_MESSAGE.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					getEnvironmentProperties().getSendAutomatedWelcomeSMS(),null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending async order success message to " + contactNumber, e);
		}
		return false;
	}

	protected boolean sendAsyncFailureMessage(String customerName, String contactNumber) {
		try {
			String message = CustomerSMSNotificationType.ASYNC_ORDER_FAILURE_MESSAGE.getMessage(customerName);
			return getNotificationService().sendNotification(
					CustomerSMSNotificationType.ASYNC_ORDER_FAILURE_MESSAGE.name(), message, contactNumber,
					providerService.getSMSClient(
							CustomerSMSNotificationType.ASYNC_ORDER_FAILURE_MESSAGE.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					getEnvironmentProperties().getSendAutomatedWelcomeSMS(),null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending async order failure message to " + contactNumber, e);
		}
		return false;
	}

	protected boolean sendFeedbackMessage(boolean sendNotification, String contactNumber, FeedbackEventInfo token) {
		try {
			String message = CustomerSMSNotificationType.FEEDBACK_MESSAGE.getMessage(token);
			return getNotificationService().sendNotification(CustomerSMSNotificationType.FEEDBACK_MESSAGE.name(),
					message, contactNumber,
					providerService.getSMSClient(
							CustomerSMSNotificationType.FEEDBACK_MESSAGE.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					sendNotification,null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the feedback message to " + contactNumber, e);
		}

		return false;
	}

	protected boolean sendNPSMessage(boolean sendNotification, String contactNumber, FeedbackEventInfo token,
			SMSWebServiceClient smsWebServiceClient) {
		try {
			if (token.getType().equals(FeedbackEventType.NPS)) {
				String message = CustomerSMSNotificationType.NPS_MESSAGE.getMessage(token);
				return getNotificationService().sendNotification(CustomerSMSNotificationType.NPS_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			} else if (token.getType().equals(FeedbackEventType.NPS_OFFER)) {
				String message = CustomerSMSNotificationType.NPS_OFFER_MESSAGE.getMessage(token);
				return getNotificationService().sendNotification(CustomerSMSNotificationType.NPS_OFFER_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			}else if(token.getType().equals(FeedbackEventType.ORDER_FEEDBACK)){
				String message = CustomerSMSNotificationType.ORDER_FEEDBACK_MESSAGE.getMessage(token);
				return getNotificationService().sendNotification(CustomerSMSNotificationType.ORDER_FEEDBACK_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			}
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the NPS message to " + contactNumber, e);
		}

		return false;
	}

	protected boolean sendElaboratedFeedbackMessage(boolean sendNotification, String contactNumber,
			FeedbackEventInfo token) {
		try {
			String message = CustomerSMSNotificationType.ELABORATED_FEEDBACK_MESSAGE.getMessage(token);
			return getNotificationService().sendNotification(
					CustomerSMSNotificationType.ELABORATED_FEEDBACK_MESSAGE.name(), message, contactNumber,
					providerService.getSMSClient(
							CustomerSMSNotificationType.ELABORATED_FEEDBACK_MESSAGE.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					sendNotification,null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the elaborated feedback message to " + contactNumber, e);
		}

		return false;
	}

	protected boolean sendMassOfferMessage(boolean sendNotification, String contactNumber, CustomerOfferData data,
			CustomerSMSNotificationType notification) {
		try {
			String message = notification.getMessage(data);
			return getNotificationService().sendNotification(notification.name(), message, contactNumber,
					providerService.getSMSClient(notification.getTemplate().getSMSType(), ApplicationName.KETTLE_CRM),
					sendNotification,null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while mass offer message " + contactNumber, e);
		}

		return false;
	}

	protected boolean sendLoyaltyReminderMessage(CustomerSMSNotificationType type, boolean sendNotification,
												 CustomerInfo info, int daysValidity) {
		try {
			Map<String,String> payload = new HashMap<>();
			payload.put("firstName",info.getFirstName());
			payload.put("daysValidity",String.valueOf(daysValidity));
			payload.put("expiryDate",AppUtils.getDateInMonth(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(),daysValidity)));
			String message = type.getMessage(info.getFirstName());
			return getNotificationService().sendNotification(type.name(), message, info.getContactNumber(),
					providerService.getSMSClient(type.getTemplate().getSMSType(), ApplicationName.KETTLE_CRM),
					AppUtils.isProd(getEnvironmentProperties().getEnvironmentType()) ? sendNotification : false,
					getNotificationPayload(type,info,payload));
		} catch (IOException | JMSException e) {
			LOG.error("WHATSAPP_NOTIFICATOIN ::: Error while sending the loyalty message to " + info.getContactNumber(), e);
		}
		return false;
	}

	protected boolean sendOptOutMessage(boolean sendNotification, String customerName, String contactNumber) {
		try {
			String message = CustomerSMSNotificationType.OPT_OUT.getMessage(customerName);
			return getNotificationService().sendNotification(CustomerSMSNotificationType.OPT_OUT.name(), message,
					contactNumber,
					providerService.getSMSClient(CustomerSMSNotificationType.OPT_OUT.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					sendNotification,null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the Opt Out message to " + contactNumber, e);
		}
		return false;
	}

	protected boolean sendSkippingFaceITMessage(boolean sendNotification, String customerName, String contactNumber) {
		try {
			String message = CustomerSMSNotificationType.SKIPPING_FACE_IT.getMessage(customerName);
			return getNotificationService().sendNotification(CustomerSMSNotificationType.SKIPPING_FACE_IT.name(),
					message, contactNumber,
					providerService.getSMSClient(
							CustomerSMSNotificationType.SKIPPING_FACE_IT.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					sendNotification,null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the Skipping Face IT message to " + contactNumber, e);
		}
		return false;
	}

	protected boolean sendCafePromotionalMessage(boolean sendNotification, CustomerInfo info) {
		try {
			String message = CustomerSMSNotificationType.CAFE_PROMOTION.getMessage(info.getFirstName());
			return getNotificationService().sendNotification(CustomerSMSNotificationType.CAFE_PROMOTION.name(), message,
					info.getContactNumber(),
					providerService.getSMSClient(CustomerSMSNotificationType.CAFE_PROMOTION.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					sendNotification,null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the cafe promotional message to " + info.getContactNumber(), e);
		}
		return false;
	}

	protected boolean sendSMS(boolean sendNotification, SMSRequest request) {
		try {
			String message = request.getMessage();
			return getNotificationService().sendNotification(request.getCampaign(), message, request.getContact(),
					providerService.getSMSClient(SMSType.valueOf(request.getSmsType().toUpperCase()),
							ApplicationName.valueOf(request.getApplication().toUpperCase())),
					sendNotification,null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the generic sms message to " + request.getContact(), e);
		}
		return false;
	}

	protected boolean sendDeliveryPromotionalMessage(boolean sendNotification, CustomerInfo info) {
		try {
			String message = CustomerSMSNotificationType.DELIVERY_PROMOTION.getMessage(info.getFirstName());
			return getNotificationService().sendNotification(CustomerSMSNotificationType.DELIVERY_PROMOTION.name(),
					message, info.getContactNumber(),
					providerService.getSMSClient(
							CustomerSMSNotificationType.DELIVERY_PROMOTION.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					sendNotification,null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the delivery promotional message to " + info.getContactNumber(), e);
		}
		return false;
	}

	protected boolean sendRedemptionOTP(CustomerResponse customer,boolean isEmailAllowed) {
		try {
			String token = getNotificationService().getOTPMapperInstance().generateOTP(props.getSendOTPLastFourDigits(), OtpType.KETTLE_CRM,
					customer.getContact(), getEnvironmentType());
			return sendRedemptionMessage(customer, token,isEmailAllowed);
		} catch (DuplicateRequestException e) {
			LOG.error("Error in send Customer Redemption OTP", e);
			return false;
		}
	}

	protected boolean resendRedemptionOTP(CustomerResponse customer) {
		try {
			String token = getNotificationService().getOTPMapperInstance().getOTP(OtpType.KETTLE_CRM,
					customer.getContact());
			if (token == null) {
				token = getNotificationService().getOTPMapperInstance().generateOTP(props.getSendOTPLastFourDigits(), OtpType.KETTLE_CRM,
						customer.getContact(), getEnvironmentType());
			}
			final String otp = token;
			boolean isEmailAllowed = true;
			boolean isOtpSent = sendRedemptionMessage(customer, token,isEmailAllowed);
			if(isOtpSent){
				ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
						.getBean("taskExecutor");
				executor.execute(()->{
					try {
						cleverTapDataPushService.generateOtpEventPayloadForCleverTap(customer, otp);
					}catch (Exception e){
						LOG.info("Error in pushing Event to cleverTap : {}",e);
					}
				});
			}
			return isOtpSent;
		} catch (DuplicateRequestException e) {
			LOG.error("Error in resend Customer Redemption OTP", e);
			return false;
		}
	}

	protected boolean sendRedemptionMessage(CustomerResponse customerResponse, String token ,boolean isEmailAllowed) {
		try {
			Customer customer = customerService.getCustomer(customerResponse.getContact());
			boolean isOtpSent;
			if(Objects.nonNull(customer) && (!customer.getCountryCode().equalsIgnoreCase(AppConstants.DEFAULT_COUNTRY_CODE)|| isEmailAllowed ) && customer.isEmailVerified()) {
				customerService.sendOTPEmail(customer, token);
				isOtpSent= true;
			}
			String message = CustomerSMSNotificationType.OTP_MESSENGER.getMessage(getToken(token));
			isOtpSent= getNotificationService().sendNotification(CustomerSMSNotificationType.OTP_MESSENGER.name(), message,
					customerResponse.getContact(),
					providerService.getSMSClient(CustomerSMSNotificationType.OTP_MESSENGER.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),
					getEnvironmentProperties().getSendAutomatedOTPSMS(),null);
			return isOtpSent;
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the OTP message to " + customerResponse.getContact(), e);
		}
		return false;
	}

	protected boolean verifyOTPInCurrentSession(String otpPin, String contactNumber) {
		boolean result = otpPin
				.equals(getNotificationService().getOTPMapperInstance().getOTP(OtpType.KETTLE_CRM, contactNumber));
		if (result) {
			getNotificationService().getOTPMapperInstance().removeOTP(OtpType.KETTLE_CRM, contactNumber);
		}
		return result;
	}

	public abstract AuthorizationService getAuthorizationService();

	public abstract EnvironmentProperties getEnvironmentProperties();

	public abstract CustomerService getCustomerService();

	public abstract NotificationService getNotificationService();

	public SMSToken getToken(String token) {
		return new SMSToken(token, getEnvironmentProperties().getOTPHashKey());
	}

	@Override
	public EnvType getEnvironmentType() {
		return getEnvironmentProperties().getEnvironmentType();
	}

	@Override
	public String getBasePath() {
		return getEnvironmentProperties().getBasePath();
	}

	protected boolean sendSignUpCoupon(CustomerResponse customerResponse) {
		try {
			String message = CustomerSMSNotificationType.SIGN_UP_COUPON_NOTIFICATION.getMessage(customerResponse);
			return getNotificationService().sendNotification(
					CustomerSMSNotificationType.SIGN_UP_COUPON_NOTIFICATION.name(), message, customerResponse.getContact(),
					providerService.getSMSClient(CustomerSMSNotificationType.SIGN_UP_COUPON_NOTIFICATION.getTemplate().getSMSType(),
							ApplicationName.KETTLE_CRM),true, null);
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the Sign up coupon message to " + customerResponse.getContact(), e);
		}
		return false;
	}

	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, CustomerInfo customerInfo,Map<String,String> payload) {
		try {
			NotificationPayload load = new NotificationPayload();
			load.setCustomerId(customerInfo.getCustomerId());
			load.setOrderId(null);
			load.setMessageType(type.name());
			load.setContactNumber(customerInfo.getContactNumber());
			load.setSendWhatsapp(type.isWhatsapp());
			if (Objects.nonNull(customerInfo.getOptWhatsapp())) {
				load.setWhatsappOptIn(customerInfo.getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}
			load.setRequestTime(AppUtils.getCurrentTimestamp());
			load.setPayload(payload);
			return load;
		} catch (Exception e){
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",customerInfo.getCustomerId());
			return null;
		}
	}
}
