package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.customer.service.CustomerInfoService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.LoyaltyTransferService;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.LoyaltyTransfer;
import com.stpl.tech.kettle.domain.model.webengage.survey.LoyaltyGiftingSMSToken;
import com.stpl.tech.kettle.loyaltyTransfer.model.LoyaltyTransferRequestBody;
import com.stpl.tech.kettle.loyaltyTransfer.model.LoyaltyTransferStatus;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.LOYALITY_TRANSFER_SERVICE_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + LOYALITY_TRANSFER_SERVICE_ROOT_CONTEXT)
public class LoyaltyTransferResource {
    @Autowired
    protected CustomerService customerService;

    @Autowired
    protected CustomerInfoService customerInfoService;

    @Autowired
    protected LoyaltyTransferService loyaltyTransferService;

    //parameter required  ReceiverContactNumber , SenderId
    @RequestMapping(method = RequestMethod.POST, value = "lookup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public LoyaltyTransfer lookUp(@RequestBody final LoyaltyTransferRequestBody loyaltyTransferRequestBody) {
        CustomerInfo customerInfo=loyaltyTransferService.getCustomerInfoByContactNumber(loyaltyTransferRequestBody.getContactNumber());
        LoyaltyTransfer loyaltyTransfer = loyaltyTransferService.initiateLoyaltyTransfer(loyaltyTransferRequestBody, customerInfo);
        loyaltyTransferService.addLoyaltyTransferStatusLog(null, LoyaltyTransferStatus.INITIATED.name(), loyaltyTransfer.getEventId());
        return loyaltyTransfer;
    }

    //parameter required  EventId , NoOfChai , TransferMessage , ReceiverName
    @RequestMapping(method = RequestMethod.POST, value = "create-gifting", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public LoyaltyTransfer createEvent(@RequestBody LoyaltyTransferRequestBody loyaltyTransferRequestBody) throws DataNotFoundException {
        LoyaltyTransfer loyaltyTransfer = loyaltyTransferService.getLoyalityTransferByEventId(loyaltyTransferRequestBody.getEventId());
        LoyaltyScore loyaltyScore = loyaltyTransferService.getLoyalityScore(loyaltyTransfer.getSenderId());
        loyaltyTransfer = loyaltyTransferService.createLoyaltyTransfer(loyaltyTransferRequestBody, loyaltyScore, loyaltyTransfer);
        if(loyaltyTransfer!=null){
            loyaltyTransferService.addLoyaltyTransferStatusLog(LoyaltyTransferStatus.INITIATED.name(), LoyaltyTransferStatus.CREATED.name(), loyaltyTransferRequestBody.getEventId());
            LoyaltyGiftingSMSToken loyaltyGiftingSMSToken=new LoyaltyGiftingSMSToken(loyaltyTransfer.getSenderName(),loyaltyTransfer.getReceiverName(),loyaltyTransfer.getTotalTransferred());
            loyaltyTransferService.sendLoyaltyGiftingReminderSMS(loyaltyGiftingSMSToken,loyaltyTransfer.getReceiverContactNumber());
        }
        return loyaltyTransfer;
    }

    //parameter required SenderId
    @RequestMapping(method = RequestMethod.GET, value = "sent-gift/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<LoyaltyTransfer> getAllSentGift(@RequestParam int senderId) {
        return loyaltyTransferService.getAllSentGift(senderId);
    }

    //parameter required SenderId
    @RequestMapping(method = RequestMethod.GET, value = "sent-gift/not-claimed", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<LoyaltyTransfer> getNotClaimedGift(@RequestParam int senderId) {
        return loyaltyTransferService.getNotClaimedGift(senderId);
    }

    //parameter required SenderId
    @RequestMapping(method = RequestMethod.GET, value = "sent-gift/claimed", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<LoyaltyTransfer> getClaimedGift(@RequestParam int senderId) {

        return loyaltyTransferService.getClaimedGift(senderId);
    }

    //parameter required contactNumber
    @RequestMapping(method = RequestMethod.GET, value = "received-gift/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<LoyaltyTransfer> getAllReceivedGift(@RequestParam String contactNumber) {
        return loyaltyTransferService.getAllReceivedGift(contactNumber);
    }

    //parameter required contactNumber
    @RequestMapping(method = RequestMethod.GET, value = "received-gift/not-claimed", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<LoyaltyTransfer> getNotClaimedReceivedGift(@RequestParam String contactNumber) {
        return loyaltyTransferService.getNotClaimedReceivedGift(contactNumber);
    }

    //parameter required contactNumber
    @RequestMapping(method = RequestMethod.GET, value = "received-gift/claimed", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<LoyaltyTransfer> getClaimedReceivedGift(@RequestParam String contactNumber) {

        return loyaltyTransferService.getClaimedReceivedGift(contactNumber);
    }





    //parameter required SenderId , EventId
    @RequestMapping(method = RequestMethod.POST, value = "cancel-gifting", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public LoyaltyTransfer cancleGift(@RequestBody LoyaltyTransferRequestBody loyaltyTransferRequestBody) {
        LoyaltyScore loyaltyScore=loyaltyTransferService.getLoyalityScore(loyaltyTransferRequestBody.getSenderId());
        return loyaltyTransferService.cancelGift(loyaltyTransferRequestBody.getEventId(),loyaltyScore);
    }

    //parameter required EventId
    @RequestMapping(method = RequestMethod.POST, value = "claim-gifting", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public LoyaltyTransfer claimGift(@RequestBody LoyaltyTransferRequestBody loyaltyTransferRequestBody) {
        LoyaltyTransfer loyaltyTransfer=loyaltyTransferService.getLoyalityTransferByEventId(loyaltyTransferRequestBody.getEventId());
        LoyaltyScore loyaltyScoreSender=loyaltyTransferService.getLoyalityScore(loyaltyTransfer.getSenderId());
        loyaltyTransferService.addLoyaltyTransferStatusLog(LoyaltyTransferStatus.CREATED.name(),LoyaltyTransferStatus.CLAIMED.name(),loyaltyTransferRequestBody.getEventId());
        return loyaltyTransferService.claimGift(loyaltyTransfer,loyaltyScoreSender);
    }

    //parameter required EventId
    @RequestMapping(method = RequestMethod.POST, value = "send-reminder", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean sendLoyaltyGiftingReminder(@RequestBody LoyaltyTransferRequestBody loyaltyTransferRequestBody) throws DataNotFoundException{
        LoyaltyTransfer loyaltyTransfer=loyaltyTransferService.getLoyalityTransferByEventId(loyaltyTransferRequestBody.getEventId());
        CustomerInfo customerInfo=customerInfoService.getCustomerInfoById(loyaltyTransfer.getSenderId());
        LoyaltyGiftingSMSToken loyaltyGiftingSMSToken=new LoyaltyGiftingSMSToken(loyaltyTransferService.getCustomerFullName(customerInfo),loyaltyTransfer.getReceiverName(),loyaltyTransfer.getTotalTransferred());
        return  loyaltyTransferService.sendLoyaltyGiftingReminderSMS(loyaltyGiftingSMSToken,loyaltyTransfer.getReceiverContactNumber());
    }
}

