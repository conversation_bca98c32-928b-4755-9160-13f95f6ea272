package com.stpl.tech.kettle.crm.shopifyProperties;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Configuration
public class ShopifyConfig {

    @Value("${shopify.secretKey}")
     String secretKey;

}
