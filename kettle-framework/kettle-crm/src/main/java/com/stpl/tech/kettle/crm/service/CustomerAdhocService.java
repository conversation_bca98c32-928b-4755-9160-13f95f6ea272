/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.offer.data.model.CustomerOfferData;

public interface CustomerAdhocService {

	/**
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	List<CustomerOfferData> getAllCustomerWithOrders(Date startTime, Date endTime);

	List<CustomerOfferData> getAllCustomerWithOrders(Date startTime, Date endTime, List<Integer> unitIds);

}