
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.FeedbackRatingData;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.crm.model.CustomerFaceIdRemoveData;
import com.stpl.tech.kettle.customer.dao.CashCardDao;
import com.stpl.tech.kettle.customer.dao.SubscriptionPlanDao;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.CustomerInfoService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.LoyaltyService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.CustomerAdditionalDetail;
import com.stpl.tech.kettle.data.model.CustomerDetailType;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.ObjectFactory;
import com.stpl.tech.kettle.domain.model.external.EventDetail;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerPostRequest;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.kettle.report.metadata.model.TrueCallerSettings;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.service.UnitManagementService;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.OtpType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.jms.JMSException;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.CRM_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.kettle.crm.core.CustomerServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CRM_SERVICES_ROOT_CONTEXT)
public class CRMResources extends NotificationResource {

    private static final Logger LOG = LoggerFactory.getLogger(CRMResources.class);
    private static final ObjectFactory objectFactory = new ObjectFactory();

    @Autowired
    protected CustomerService customerService;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    protected AuthorizationService authorizatonDao;



    @Autowired
    protected EnvironmentProperties enviournment;

    @Autowired
    protected TokenService<FeedbackTokenInfo> tokenService;

    @Autowired
    private FeedbackManagementService feedbackService;

    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    @Autowired
    private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    SubscriptionPlanDao subscriptionPlanDao;

    @Autowired
    CardService cardService;

    @Autowired
    LoyaltyService loyaltyService;

    @Autowired
    CashCardDao cashCardDao;

    @Autowired
    MasterDataCache masterDataCache;



    @RequestMapping(method = RequestMethod.POST, value = "signin", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse search(@RequestBody final CustomerResponse customerResponse) throws DataUpdationException, JMSException {
        LOG.info("Got request to Sign-in for a customer with contact number ## {} at unit {}",
                customerResponse.getContact(), customerResponse.getUnitId());

        Customer customer = customerService.getCustomer(customerResponse.getContact());
//        Long customerCount = orderManagementService.checkCustomerAdditionalDetail(customer.getId(),CustomerDetailType.SOURCE_ACKNOWLEDGEMENT.value());
//         if(customerCount==0){
//             customerResponse.setSourceAcknowledged(false);
//         }else{
//             customerResponse.setSourceAcknowledged(true);
//         }
//        if (Objects.nonNull(customer) && Objects.isNull(customer.getOptWhatsapp()) && Objects.nonNull(customerResponse.isOptWhatsapp())){
//            customer.setOptWhatsapp(Boolean.TRUE.equals(customerResponse.isOptWhatsapp()) ? AppConstants.YES : AppConstants.NO);
//            customerService.updateCustomer(customer);
//            if(customer.getOptWhatsapp().equals(AppConstants.YES)) {
//                sendWhatsappOptInRequest(customer);
//            }
//        }
        return getCustomerResponse(customer, customerResponse);
    }

    private CustomerResponse getCustomerResponse(Customer customer, CustomerResponse customerResponse)
            throws DataUpdationException, JMSException {
        if (customer == null) {
            customerResponse.setNewCustomer(true);
            customerResponse.setEligibleForSignupOffer(false);
            if (customerResponse.getSignInMode() == null
                    || customerResponse.getSignInMode().equals(TrueCallerSettings.DEFAULT)) {
                sendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(),
                        customerResponse.getContact(),
                        null);
            }
        } else {
            if (customer.isInternal()) {
                throw new DataUpdationException(
                        "Customer With Contact Number " + customerResponse.getContact() + " is an Internal Employee");
            }
            if (!customer.isContactNumberVerified()) {
                if (customerResponse.getSignInMode() == null
                        || customerResponse.getSignInMode().equals(TrueCallerSettings.DEFAULT)) {
                    sendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(),
                            customerResponse.getContact(),
                            null);
                }
                // assuming contact not verified means new customer.
                customerResponse.setNewCustomer(true);
                customerResponse.setEligibleForSignupOffer(false);
            } else {
                customerResponse.setNewCustomer(false);
            }
            /*
             * customerResponse.setFeedbackRequired(feedbackService.getFeedbackStatus(
             * customer.getId())); if (customerResponse.isFeedbackRequired()) {
             * FeedbackOrderMetadata data =
             * feedbackService.getFeedbackData(customer.getId());
             * customerResponse.setFeedbackOrderMetadata(data); }
             */
            updateCustomerResponse(customer, customerResponse);
        }
        if (customer == null) {
            customerResponse.setSourceAcknowledged(false);
        } else {
            customerResponse.setSourceAcknowledged(getCustomerSourceAcknowledgeStatus(customer.getId()));
        }
        if (Objects.nonNull(customer) && Objects.nonNull(customer.getSubscriptionInfoDetail())) {
            customerResponse.setSubscriptionInfoDetail(customer.getSubscriptionInfoDetail());
        }
        return customerResponse;
    }


    private boolean getCustomerSourceAcknowledgeStatus(int customerId) {
        Long count = customerService.checkCustomerAdditionalDetail(customerId, CustomerDetailType.SOURCE_ACKNOWLEDGEMENT.value());
        if (count == 0) {
            return false;
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "signin-by-faceId", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse faceRecognition(@RequestBody final String faceId) throws DataUpdationException, JMSException {
        LOG.info("Got request to Sign-in for a customer with faceId {}", faceId);
        Customer customer = customerService.getCustomerByFaceId(faceId);
        if (customer == null) {
            return null;
        }
        return getCustomerResponse(customer, new CustomerResponse());
    }

    @RequestMapping(method = RequestMethod.POST, value = "customer-by-contact", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Customer getCustomerByContact(@RequestBody final String contactNumber) throws DataUpdationException {
        LOG.info("Get Customer By Contact {}", contactNumber);
        return customerService.getCustomer(contactNumber);
    }

    @RequestMapping(method = RequestMethod.POST, value = "face-it/opt-out-customer/customer-id", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String optOutOfFaceItById(@RequestBody final String customerId) throws DataUpdationException, DataNotFoundException {
        LOG.info("Opt Out of FACE IT Request for Customer Id {}", customerId);
        Integer id = Integer.valueOf(customerId);
        String faceId = customerService.optOutOfFaceIt(id);
        Customer customer = customerService.getCustomer(id);
        sendOptOutMessage(true, customer.getFirstName(), customer.getContactNumber());
        return faceId;
    }

    @RequestMapping(method = RequestMethod.POST, value = "face-it/opt-out-customer/contact-number", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerFaceIdRemoveData optOutOfFaceItByNumber(@RequestBody final String contactNumber, @RequestParam Boolean flag) throws DataUpdationException {
        LOG.info("Opt Out of FACE IT Request for Contact Number {}", contactNumber);
        CustomerFaceIdRemoveData data = new CustomerFaceIdRemoveData();
        String faceId = customerService.optOutOfFaceIt(contactNumber, flag);
        Customer customer = customerService.getCustomer(contactNumber);
        if (!flag) {
            if (faceId != null) {
                sendOptOutMessage(true, customer.getFirstName(), customer.getContactNumber());
                data.setContactNumber(contactNumber);
                data.setFaceIds(faceId);
                return data;
            } else {
                return null;
            }
        } else {
            if (faceId != null) {
                List<Integer> customerIds = customerService.removeAllFaceIdsWithGivenFaceId(faceId);
                data.setCustomerIds(customerIds);
                data.setFaceIds(faceId);
                data.setContactNumber(contactNumber);
                if (customer != null) {
                    data.setCustomerId(customer.getId());
                    data.setCustomerName(customer.getFirstName());
                }
                return data;
            } else {
                return null;
            }
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "face-it/skipped-face-it-notify", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean notifyCustomerOfSkippedFaceIt(@RequestBody final String contactNumber) throws DataUpdationException {
        LOG.info("Sending Skipped from FR Message to Opt Out Message for Contact Number {}", contactNumber);
        Customer customer = customerService.getCustomer(contactNumber);
        sendSkippingFaceITMessage(true, customer.getFirstName(), customer.getContactNumber());
        return true;
    }


    @RequestMapping(method = RequestMethod.POST, value = "map-by-faceId", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean faceRecognition(@RequestBody final Pair<String, String> data) throws DataUpdationException {
        LOG.info("Got request to face Sign-up for a customer with contact {} , faceId {}", data.getKey(), data.getValue());
        return customerService.mapCustomerByFaceId(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "trueCallerSignin", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse trueCallerSignin(@RequestBody final CustomerResponse customerResponse) throws DataUpdationException {
        LOG.info("Got request to Sign-in for a customer with contact number via truecaller ## {} at unit {} with email {}",
                customerResponse.getContact(), customerResponse.getUnitId(), customerResponse.getEmail());

        Customer customer = customerService.getCustomer(customerResponse.getContact());
        customerResponse.setOtpVerified(true);
        if (customer == null) {
            customerResponse.setNewCustomer(true);
            customerResponse.setEligibleForSignupOffer(false);
            customerResponse.setName(null);
            if (customerResponse.getSignInMode() == null ||
                    customerResponse.getSignInMode().equals(TrueCallerSettings.DEFAULT)) {
                sendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(),
                        customerResponse.getContact(),
                        null);
            }
        } else {
            if (customer.isInternal()) {
                throw new DataUpdationException("Customer With Contact Number " + customerResponse.getContact() + " is an Internal Employee");
            }

            if (customerResponse.getEmail() != null && customer.isContactNumberVerified() && (customer.getEmailId() == null || customer.getEmailId() == "" || !customer.isEmailVerified())) {
                if (customer.getEmailId() == null || (customer.getEmailId() != null && customer.getEmailId().indexOf("chaayos.com") == -1)) {
                    LOG.info("Updating email via truecalller");
                    customer.setEmailId(customerResponse.getEmail());
                }
            }

            if (customerResponse.getName() != null && !customer.isContactNumberVerified()) {
                LOG.info("Updating name via truecalller");
                customer.setFirstName(customerResponse.getName());
            }

            customerResponse.setContactVerified(true);
            customer.setContactNumberVerified(true);
            customerService.updateBasicCustomerInfo(customer);


            cleverTapDataPushService.pushUserToCleverTap(customer.getId());


            updateCustomerResponse(customer, customerResponse);
        }
        return customerResponse;
    }

    @RequestMapping(method = RequestMethod.POST, value = "signup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse signupAndVerify(@RequestBody final CustomerResponse customerResponse)
            throws DataUpdationException, DataNotFoundException, JMSException {
        LOG.info("Got request to signup and verify up for a customer with contact number ## {} at unit {}",
                customerResponse.getContact(), customerResponse.getUnitId());

        boolean result = customerResponse.getTcId() != null || verifyOTPInCurrentSession(customerResponse.getOtp(), customerResponse.getContact());
        if (result) {
            Customer customer = customerService.getCustomer(customerResponse.getContact());
            if (customer == null) {
                customer = getCustomer(customerResponse);
                if (customerResponse.getOtp().equals(customerResponse.getContact())) {
                    customer.setContactNumberVerified(false);
                } else {
                    customer.setContactNumberVerified(true);
                }
                LOG.info(" CUSTOMER BRANCH CHECK signup -------- " + customer.getAcquisitionBrandId() + customer.isChaayosCustomer());
                if (Objects.nonNull(customerResponse.isOptWhatsapp())) {
                    customer.setOptWhatsapp(customerResponse.isOptWhatsapp() ? AppConstants.YES : AppConstants.NO);
                }else {
                	customer.setOptWhatsapp(AppConstants.YES);
                }
                customer = customerService.addCustomerUnchecked(customer);
                updateCustomerResponse(customer, customerResponse);
                customerResponse.setNewCustomer(true);

                // send verification email to new customer
                if (AppUtils.checkBlank(customerResponse.getEmail()) != null && Objects.nonNull(customer.getId())
                        && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customer.getId())) {
                    customerService.sendVerificationEmail(customerResponse.getEmail(),
                            null, customer.getContactNumber(), customerResponse.getName(),customer.getAcquisitionBrandId());
                }
            } else {
                if (customer.isInternal()) {
                    throw new DataUpdationException("Customer With Contact Number " + customerResponse.getContact() + " is an Internal Employee");
                }
                if (!customer.isContactNumberVerified() && !customerResponse.getOtp().equals(customerResponse.getContact())) {
                    customerService.verifyContactNumber(customerResponse.getContact());
                    customer.setContactNumberVerified(true);
                }
                if (customerResponse.getEmail() != null) {
                    customer.setEmailId(customerResponse.getEmail());
                }
                if (customerResponse.getName() != null) {
                    customer.setFirstName(customerResponse.getName());
                }
                if (Objects.nonNull(customerResponse.isOptWhatsapp())) {
                    customer.setOptWhatsapp(AppUtils.YES);
                } else {
                    customer.setOptWhatsapp(AppUtils.NO);
                }
                customerService.updateBasicCustomerInfo(customer);
                updateCustomerResponse(customer, customerResponse);
                customerResponse.setNewCustomer(false);
            }
            if (customer.getFirstName() != null && !customer.getFirstName().trim().equals("")) {
                sendWelcomeMessage(customer);
                cleverTapDataPushService.pushUserToCleverTap(customer.getId());
            }
            customerResponse.setOtpVerified(true);
//            if (Objects.nonNull(customer.getOptWhatsapp()) && customer.getOptWhatsapp().equals(AppConstants.YES)){
//                customerCommunicationEventPublisher.publishCustomerCommunicationEvent(enviournment.getEnvironmentType().name(),getNotificationPayload(customerResponse,true));
//            }
        } else {
            customerResponse.setContactVerified(false);
            customerResponse.setOtpVerified(false);
        }

        return customerResponse;
    }

    @RequestMapping(method = RequestMethod.POST, value = "trueCallerSignup", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse trueCallerSignup(@RequestBody final CustomerResponse customerResponse)
            throws DataUpdationException {
        LOG.info("Got request to signup and verify up for a customer with contact number via truecaller ## {} at unit {}",
                customerResponse.getContact(), customerResponse.getUnitId());

        Customer customer = customerService.getCustomer(customerResponse.getContact());
        if (customer == null) {
            customer = getCustomer(customerResponse);
            customer.setContactNumberVerified(true);
            customer.setAcquisitionToken(AppConstants.TRUE_CALLER);
            customer.setAcquisitionBrandId(AppConstants.CHAAYOS_BRAND_ID);
            customer.setChaayosCustomer(true);
            LOG.info(" CUSTOMER BRANCH CHECK trueCallerSignup -------- " + customer.getAcquisitionBrandId() + customer.isChaayosCustomer());
            customer = customerService.addCustomerUnchecked(customer);
            updateCustomerResponse(customer, customerResponse);
            customerResponse.setNewCustomer(true);
            customerResponse.setContactVerified(true);

            // send verification email to new customer
            if (AppUtils.checkBlank(customerResponse.getEmail()) != null && Objects.nonNull(customer.getId())
                    && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customer.getId())) {
                customerService.sendVerificationEmail(customerResponse.getEmail(),
                        null, customer.getContactNumber(), customerResponse.getName(),customer.getAcquisitionBrandId());
            }
        } else {
            if (customer.isInternal()) {
                throw new DataUpdationException("Customer With Contact Number " + customerResponse.getContact() + " is an Internal Employee");
            }

            if (customerResponse.getEmail() != null) {
                customer.setEmailId(customerResponse.getEmail());
            }
            if (customerResponse.getName() != null) {
                customer.setFirstName(customerResponse.getName());
            }
            customerService.updateBasicCustomerInfo(customer);
            updateCustomerResponse(customer, customerResponse);
            customerResponse.setNewCustomer(false);
        }
        if (customer.getFirstName() != null && !customer.getFirstName().trim().equals("")) {
            sendWelcomeMessage(customer);
            cleverTapDataPushService.pushUserToCleverTap(customer.getId());

        }
        customerResponse.setOtpVerified(true);
        return customerResponse;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-name", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse updateName(@RequestBody final CustomerResponse customerResponse)
            throws DataUpdationException {
        LOG.info("Got update request for name {} with contact number# {} at unit {}", customerResponse.getName(),
                customerResponse.getContact(), customerResponse.getUnitId());

        Customer customer = customerService.getCustomer(customerResponse.getContact());
        if (customer != null) {
            customer.setFirstName(customerResponse.getName());
            customer.setEmailId(customerResponse.getEmail());
            customerService.updateBasicCustomerInfo(customer);
            if (customerResponse.getEmail() == null || customerResponse.getEmail().trim().equals("")) {
                sendWelcomeMessage(customer);
            }
        } else {
            LOG.error("Customer Not Found for Contact #{}", customerResponse.getContact());
        }
        updateCustomerResponse(customer, customerResponse);

        // updating customer data on clevertap

        cleverTapDataPushService.pushUserToCleverTap(customer.getId());


        return customerResponse;
    }

    @RequestMapping(method = RequestMethod.POST, value = "overrideContactVerification", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse overrideContactVerificationStatus(@RequestBody final CustomerResponse customerResponse)
            throws DataUpdationException {
        LOG.info("Got override request for verification status of contact number# {} at unit {}",
                customerResponse.getContact(), customerResponse.getUnitId());
        try {
            customerService.overrideContactVerificationStatus(customerResponse.getContact(),
                    customerResponse.isContactVerified());
        } catch (Exception e) {
            LOG.error("Error While updating contact verification status for contact #{}", customerResponse.getContact(),
                    e);
        }
        return customerResponse;
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean generateOtp(@RequestBody final CustomerResponse customerResponse) throws DataUpdationException, EmailGenerationException {
        LOG.info(String.format("Got generate otp request for a customer with contact number ## %s from unit %d",
                customerResponse.getContact(), customerResponse.getUnitId()));
        try {
            LOG.info(JSONSerializer.toJSON(customerResponse));
        } catch (Exception e) {
            LOG.error("Error While deserializing", e);
        }
        boolean isAllowed = false;
        if(customerResponse.getIsResendOtp()){
            isAllowed = true;
        }else {
            Unit unit = masterDataCache.getUnit(customerResponse.getUnitId());
            if (Objects.nonNull(unit)) {
                isAllowed = Objects.nonNull(unit.getIsOtpViaEmail()) ? unit.getIsOtpViaEmail() : false;
            }
        }
        return sendRedemptionOTP(customerResponse,isAllowed);
    }

    @RequestMapping(method = RequestMethod.POST, value = "switch/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean switcOtp(@RequestBody final CustomerResponse customerResponse) throws DataUpdationException {
        LOG.info(String.format("Got switch otp request for a customer with contact number ## %s from unit %d",
                customerResponse.getContact(), customerResponse.getUnitId()));
        try {
            LOG.info(JSONSerializer.toJSON(customerResponse));
        } catch (Exception e) {

            LOG.error("Error While deserializing", e);
            return false;
        }
        getNotificationService().getOTPMapperInstance().setOTP(OtpType.KETTLE_CRM, customerResponse.getContact(),
                getEnvironmentType(), customerResponse.getContact());
        return true;
    }


    @RequestMapping(method = RequestMethod.POST, value = "verify/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean verifyOTP(@RequestBody final CustomerResponse customerResponse) throws DataUpdationException {
        LOG.info("OTP verification request for customer with contact number # {} from unit {} and otp {}",
                customerResponse.getContact(), customerResponse.getUnitId(), customerResponse.getOtp());
        return verifyOTPInCurrentSession(customerResponse.getOtp(), customerResponse.getContact());
    }

    @RequestMapping(method = RequestMethod.POST, value = "resendCustomerAuthorizationOTPViaIVR/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean resendCustomerAuthorizationOTPViaIvr(@RequestBody final CustomerResponse customerResponse)
            throws DataUpdationException {
        LOG.info("OTP resend request for customer with contact number # {} from unit {}", customerResponse.getContact(),
                customerResponse.getUnitId());
        try {
            return resendCustomerAuthorizationOTPViaIVR(enviournment.getSendOTPLastFourDigits(), customerResponse.getContact());
        } catch (Exception e) {
            LOG.error("Exception while getting Otp via IVR :", e);
            return false;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "resendCustomerAuthorizationOTP/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean resendCustomerAuthorizationOTP(@RequestBody final CustomerResponse customerResponse)
            throws DataUpdationException {
        LOG.info("OTP resend request for customer with contact number # {} from unit {}", customerResponse.getContact(),
                customerResponse.getUnitId());
        return resendCustomerAuthorizationOTP(enviournment.getSendOTPLastFourDigits(), customerResponse.getContact(),
                true, null);
    }

    @RequestMapping(method = RequestMethod.POST, value = "resendRedemptionOTP/otp", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean resendRedemptionOTP(@RequestBody final CustomerResponse customerResponse) {
        LOG.info("OTP resend request for customer with contact number # {} from unit {}", customerResponse.getContact(),
                customerResponse.getUnitId());
        return super.resendRedemptionOTP(customerResponse);
    }

    @RequestMapping(method = RequestMethod.POST, value = "feedback/submit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer submitFeedback(@RequestBody final Object eventDetail) {
        LOG.info("customer screen feedback submission : {}", eventDetail);
        EventDetail detail = WebServiceHelper.convert(eventDetail, EventDetail.class);
        try {
            String token = detail.getFormResponse().getHidden().getToken();
            if (token != null) {
                FeedbackTokenInfo tokenInfo = new FeedbackTokenInfo();
                tokenService.parseToken(tokenInfo, token);
                FeedbackRatingData rating = feedbackService.addFeedback(detail, tokenInfo);
                if (tokenInfo.getFeedbackSource().equals(FeedbackSource.POS.name()) && rating != null && rating.getRating() > 0
                        && rating.getRating() <= 3) {
                    int brandId = customerService.getOrderDetail(tokenInfo.getOrderId()).getBrandId();
                    feedbackService.createFeedbackEvent(FeedbackSource.SMS, tokenInfo.getOrderId(),
                            tokenInfo.getOrderSource(), tokenInfo.getFeedbackId(), AppUtils.getCurrentTimestamp(),
                            FeedbackEventType.ELABORATED, rating.getRating(), brandId);
                }
            }

        } catch (Exception e) {
            String json = JSONSerializer.toJSON(eventDetail);
            LOG.error("Error while processing the json " + json, e);
            new ErrorNotification("Error while persisting feedback data", "Json : " + json, e,
                    enviournment.getEnvironmentType()).sendEmail();
            ;
            throw e;
        }
        return -1;
    }

    @RequestMapping(method = RequestMethod.POST, value = "feedback/cancel", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean cancelFeedback(@RequestBody final String feedbackToken) {
        LOG.info("customer screen feedback cancel : {}", feedbackToken);
        return feedbackService.cancelFeedback(feedbackToken);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-truecaller-profile")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse getTrueCallerProfile(@RequestParam String requestId) throws IOException {
        TrueCallerVerifiedProfile verifiedProfile = customerService.getTrueCallerVerifiedProfile(requestId);
        if (verifiedProfile != null) {
            Customer customer = customerService.updateTrueCallerInfo(verifiedProfile);
            CustomerResponse customerResponse = new CustomerResponse();
            updateCustomerResponse(customer, customerResponse);
            return customerResponse;
        }
        return null;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-truecaller-profile")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse updateTrueCallerProfile(@RequestBody TrueCallerVerifiedProfile profile) throws IOException {
        if (profile != null) {
            Customer customer = customerService.updateTrueCallerInfo(profile);
            CustomerResponse customerResponse = new CustomerResponse();
            updateCustomerResponse(customer, customerResponse);
            customerResponse.setTcId(profile.getTcId());
            cleverTapDataPushService.pushUserToCleverTap(customer.getId());
            return customerResponse;
        }
        return null;
    }


    @RequestMapping(method = RequestMethod.POST, value = "send-truecaller-request")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public TrueCallerPostRequest sendTrueCallerRequest(@RequestBody TrueCallerPostRequest contact) throws IOException {
        String requestId = customerService.sendTrueCallerRequest(contact);
        contact.setRequestId(requestId);
        return contact;
    }


    @RequestMapping(method = RequestMethod.POST, value = "save-customer-source-acknowledgement", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerAdditionalDetail saveCustomerSourceAcknowledge(@RequestBody IdCodeName idCodeName) {
        LOG.info("saving customer additional detail ");
        return customerService.saveCustomerAdditionalDetail(idCodeName);
    }

    @Override
    public AuthorizationService getAuthorizationService() {
        return authorizatonDao;
    }

    @Override
    public EnvironmentProperties getEnvironmentProperties() {
        return enviournment;
    }

    private final Customer getCustomer(CustomerResponse customerResponse) {
        Customer customer = objectFactory.createCustomer();
        customer.setFirstName(customerResponse.getName());
        customer.setContactNumber(customerResponse.getContact());
        customer.setEmailId(customerResponse.getEmail());
        customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
        customer.setRegistrationUnitId(customerResponse.getUnitId());
        customer.setAcquisitionSource(UnitCategory.CAFE.name());
        customer.setAcquisitionToken("CHAAYOS_LOYALTY");
        customer.setOrderCount(0);
        if (customerResponse.getTcId() != null) {
            customer.setTrueCallerProfile(new TrueCallerVerifiedProfile(customerResponse.getTcId()));
            customer.setContactNumberVerified(true);
            customer.setEmailVerified(customerResponse.getEmail() != null);
        }
        customer.setAvailedSignupOffer(false);
        customer.setSignUpRefCode(customerResponse.getSignUpRefCode());
        customer.setReferrerAwarded(false);
        customer.setAcquisitionBrandId(customerResponse.getBrandId());
        if (customerResponse.getBrandId() == null) {
            customerResponse.setBrandId(AppConstants.CHAAYOS_BRAND_ID);
            customer.setAcquisitionBrandId(customerResponse.getBrandId());
        }
        customer.setChaayosCustomer(customerResponse.getBrandId().equals(AppConstants.CHAAYOS_BRAND_ID));
        if (customerResponse.getGender() != null) {
            customer.setGender(customerResponse.getGender());
        }
        if (customerResponse.getDateOfBirth() != null) {
            customer.setDateOfBirth(customerResponse.getDateOfBirth());
        }
        if (customerResponse.getAnniversary() != null) {
            customer.setAnniversary(customerResponse.getAnniversary());
        }
        customer.setSmsSubscriber(true);
        return customer;
    }

    private void updateCustomerResponse(Customer customer, CustomerResponse customerResponse) {
        customerResponse.setId(customer.getId());
        customerResponse.setName(customer.getFirstName());
        customerResponse.setContact(customer.getContactNumber());
        customerResponse.setOptOutOfFaceIt(customer.isOptOutOfFaceIt());
        customerResponse.setOptOutTime(customer.getOptOutTime());
        if (customerResponse.getOtp() != null && customerResponse.getOtp().equals(customerResponse.getContact())) {
            customerResponse.setContactVerified(true);
        } else {
            customerResponse.setContactVerified(customer.isContactNumberVerified());
        }
        customerResponse.setEmail(customer.getEmailId());
        customerResponse.setEmailVerified(customer.isEmailVerified());
        customerResponse.setLoyalityPoints(customer.getLoyaltyPoints());
        customerResponse.setLastVisitTime(customer.getLastOrderTime());
        customerResponse.setChaayosCash(customer.getChaayosCash());
        customerResponse.setEligibleForSignupOffer(TransactionUtils.eligibleForSignupOffer(customerService, customer,masterDataCache));
        if (customer.getTrueCallerProfile() != null) {
            customerResponse.setTcId(customer.getTrueCallerProfile().getTcId());
        }
        if (customer.getFaceId() != null) {
            customerResponse.setFaceId(customer.getFaceId());
        }
        if (customer.getOptWhatsapp() != null) {
            customerResponse.setOptWhatsapp(customer.getOptWhatsapp().equals(AppConstants.YES));
        }
        customerResponse.setOrderCount(Objects.nonNull(customer.getOrderCount()) ? customer.getOrderCount() : null);
        // unitId
        // newCustomer
        // OTP
    }

    @Override
    public CustomerService getCustomerService() {
        return customerService;
    }

    public NotificationService getNotificationService() {
        return notificationService;
    }

    @RequestMapping(method = RequestMethod.POST, value = "send-app-download-link", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerAdditionalDetail sendDownloadAppLinkToCustomer(@RequestBody IdCodeName request) throws DataNotFoundException {
        Customer customerInfo = customerService.getCustomer(request.getId());
        Boolean status = customerService.sendDownloadAppLinkToCustomer(customerInfo.getContactNumber(), customerInfo, request.getCode());
        return customerService.saveAppDownloadLinkData(request.getId(), CustomerSMSNotificationType.valueOf(request.getCode()).name(), status);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-crm-screen-url", consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateCrmScreenUrl(@RequestBody CrmAppScreenDetail detail) throws DataNotFoundException {
        return customerService.updateCrmScreenUrl(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-crm-screen-status", consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateCrmScreenStatus(@RequestBody List<CrmAppScreenDetail> details) {
        return customerService.updateCrmScreenStatus(details);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-whatsapp-status", consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerResponse updateWhatsappOptInOut(@RequestBody CustomerResponse customerResponse) throws JMSException {
        boolean result = customerService.updateWhatsappOptInOut(customerResponse);
        if (result && Boolean.TRUE.equals(customerResponse.isOptWhatsapp())) {
            customerCommunicationEventPublisher.publishCustomerCommunicationEvent(enviournment.getEnvironmentType().name(), getNotificationPayload(customerResponse));
        } else {
            customerResponse.setOptWhatsapp(Boolean.FALSE);
        }
        return customerResponse;
    }

    private NotificationPayload getNotificationPayload(CustomerResponse customerResponse) {
        NotificationPayload payload = new NotificationPayload();
        payload.setCustomerId(customerResponse.getId());
        payload.setOrderId(0);
        payload.setMessageType(AppConstants.WA_OPT_IN);
        payload.setContactNumber(customerResponse.getContact());
        payload.setRequestTime(AppUtils.getCurrentTimestamp());
        payload.setSendWhatsapp(true);
        payload.setWhatsappOptIn(customerResponse.isOptWhatsapp());
        Map<String, String> map = new HashMap<>();
        map.put("firstName", customerResponse.getName());
        payload.setPayload(map);
        return payload;
    }



}
