<?xml version="1.0" encoding="UTF-8"?>
<svg width="373px" height="71px" viewBox="0 0 373 71" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 46.2 (44496) - http://www.bohemiancoding.com/sketch -->
    <title>LOGO WHITE</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <polygon id="path-1" points="79.976 47.94 0.817 47.94 0.817 0.266 79.976 0.266 79.976 47.94"></polygon>
        <polygon id="path-3" points="0 70.94 372.815 70.94 372.815 0.122 0 0.122"></polygon>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="LOGO-WHITE">
            <g id="Truecaller_logo_black_rgb">
                <g id="Group-4" transform="translate(171.000000, 23.000000)">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <g id="Clip-2"></g>
                    <path d="M24.501,0.266 C30.1,0.266 34.782,2.377 37.811,5.498 L31.661,13.852 C29.458,12.016 27.438,11.19 25.327,11.19 C18.809,11.19 14.678,16.239 14.678,24.133 C14.678,32.028 18.993,37.076 24.868,37.076 C28.172,37.076 30.835,35.516 33.038,33.772 L38.454,42.309 C34.047,46.164 28.264,48 23.307,48 C10.639,48 0.817,39.28 0.817,24.133 C0.817,8.987 11.924,0.266 24.501,0.266" id="Fill-1" fill="#FFFFFF" mask="url(#mask-2)"></path>
                    <path d="M58.955,37.535 C62.076,37.535 64.095,36.067 66.482,33.68 L66.482,25.694 C56.843,26.979 53.722,29.733 53.722,33.221 C53.722,36.158 55.742,37.535 58.955,37.535 Z M66.482,17.615 C66.207,13.576 64.187,11.006 59.322,11.006 C55.466,11.006 51.611,12.567 47.205,15.137 L42.431,6.233 C48.214,2.744 54.732,0.266 61.892,0.266 C73.55,0.266 79.976,6.875 79.976,20.828 L79.976,46.899 L68.961,46.899 L67.951,42.217 L67.675,42.217 C63.82,45.614 59.505,48 54.365,48 C46.103,48 40.871,41.942 40.871,34.231 C40.871,24.684 48.49,19.451 66.482,17.615 L66.482,17.615 Z" id="Fill-3" fill="#FFFFFF" mask="url(#mask-2)"></path>
                </g>
                <polygon id="Fill-5" fill="#FFFFFF" points="257.345 69.898 270.839 69.898 270.839 5.549 257.345 5.549"></polygon>
                <g id="Group-11">
                    <mask id="mask-4" fill="white">
                        <use xlink:href="#path-3"></use>
                    </mask>
                    <g id="Clip-7"></g>
                    <polygon id="Fill-6" fill="#FFFFFF" mask="url(#mask-4)" points="277.659 69.898 291.153 69.898 291.153 5.549 277.659 5.549"></polygon>
                    <path d="M325.12,42.451 C325.12,37.219 322.917,33.547 317.501,33.547 C313.278,33.547 309.606,36.393 308.688,42.451 L325.12,42.451 Z M317.225,23.266 C330.169,23.266 336.594,32.721 336.594,45.114 C336.594,47.5 336.319,49.703 336.044,50.805 L308.78,50.805 C309.973,57.69 314.563,60.719 320.622,60.719 C324.018,60.719 327.139,59.709 330.444,57.69 L334.942,65.86 C330.26,69.072 324.202,71 318.786,71 C305.751,71 295.745,62.188 295.745,47.133 C295.745,32.354 306.301,23.266 317.225,23.266 L317.225,23.266 Z" id="Fill-8" fill="#FFFFFF" mask="url(#mask-4)"></path>
                    <path d="M342.155,24.367 L353.171,24.367 L354.089,32.354 L354.456,32.354 C357.76,26.203 362.717,23.266 367.399,23.266 C369.969,23.266 371.53,23.633 372.815,24.184 L370.612,35.842 C368.96,35.383 367.583,35.108 365.655,35.108 C362.258,35.108 358.036,37.311 355.649,43.461 L355.649,69.899 L342.155,69.899 L342.155,24.367" id="Fill-9" fill="#FFFFFF" mask="url(#mask-4)"></path>
                    <path d="M145.99,35.843 C149.65,34.007 154.202,34.097 154.69,36.288 C155.717,40.89 149.071,44.765 140.212,46.09 C140.235,44.031 140.761,38.544 145.99,35.843 Z M127.487,51.233 C126.952,56.285 126.589,60.042 121.261,59.702 C118.436,59.468 116.998,57.445 117.429,53.467 L121.084,28.814 C121.13,28.39 122.089,23.408 118.521,23.255 C114.758,23.094 109.826,24.834 108.983,30.161 C108.156,35.384 107.809,38.361 107.143,42.601 C106.844,44.606 106.538,46.601 106.289,48.56 C105.393,54.959 105.045,58.293 100.188,57.993 C95.83,57.543 96.123,53.767 96.581,50.562 L99.614,28.868 C99.659,28.444 100.619,23.413 97.051,23.26 C93.289,23.098 88.356,24.888 87.513,30.216 C87.303,31.537 87.125,32.715 86.965,33.806 L86.962,33.772 C86.07,39.982 82.551,42.186 79.803,42.012 C76.391,41.43 77.252,38.116 77.754,34.714 C77.771,34.598 78.151,32.039 78.167,31.923 C79.317,24.204 76.552,21.488 69.815,24.358 C66.165,25.914 62.346,30.01 59.929,32.958 L60.541,28.817 C60.586,28.392 61.546,23.41 57.977,23.258 C54.215,23.096 49.283,24.836 48.44,30.164 C48.35,30.729 48.266,31.267 48.187,31.784 C47.046,36.721 39.836,59.904 28.83,59.904 C20.876,59.904 23.601,44.823 26.157,35.176 C30.438,35.646 34.416,36.026 35.841,36.151 C40.542,36.565 41.548,32.525 41.874,30.609 C42.792,25.359 42.065,23.248 37.734,23.248 C37.734,23.248 34.597,23.249 30.203,23.249 C31.671,19.929 33.708,15.748 36.227,11.179 C40.406,3.598 33.519,1.092 33.519,1.092 C33.519,1.092 25.653,-2.984 20.658,5.571 C17.14,11.596 14.913,18.075 13.371,23.25 C8.259,23.25 4.065,23.25 2.959,23.25 C-0.295,23.25 0.007,26.205 0.007,26.205 C0.007,28.918 6.384,31.369 10.878,32.703 C8.435,44.974 6.635,70.932 26.299,70.907 C35.74,70.897 41.26,64.532 44.486,57.318 C44.027,62.531 44.238,67.166 46.293,69.242 C49.02,71.998 54.274,71.367 54.826,67.115 C55.209,64.394 55.984,59.408 56.597,55.416 C57.435,50.543 58.493,47.811 60.189,43.975 C62.097,39.658 67.517,36.098 67.419,41.104 C67.13,55.804 78.208,56.104 84.606,49.567 C83.692,56.008 83.405,62.081 86.84,66.217 C93.519,74.26 103.54,70.33 108.767,66.811 C114.4,74.211 127.61,70.231 130.953,65.393 L130.956,65.404 C134.774,70.6 141.869,71.099 147.148,70.886 C158.298,70.339 166.08,60.979 166.992,56.075 C167.375,54.018 166.269,51.889 163.457,53.526 C161.059,54.837 157.177,59.84 148.881,60.5 C143.014,60.963 140.329,56.768 140.329,54.035 C158.41,51.498 165.346,44.472 165.346,35.544 C165.346,29.542 160.155,23.248 151.622,23.248 C134.061,23.248 128.588,38.994 127.487,51.233 L127.487,51.233 Z" id="Fill-10" fill="#FFFFFF" mask="url(#mask-4)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>