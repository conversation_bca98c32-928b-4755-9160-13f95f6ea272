.numeric-input-overlay-wrapper {
  position: relative;
  display: inline-block;
}

.numeric-input-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
  font-size: 12px;
  color: #1E1E1E;
}

.numeric-input-overlay-value {
  position: absolute;
  display: inline-block;
  text-align: right;
  height: 70%;
  top: 15%;
  right: 6px;
}

.numeric-input {
  text-align: right;
  padding-right: 3px;
}

@-webkit-keyframes blinker {
  50% {
    opacity: 0.0;
  }
}

@keyframes blinker {
  50% {
    opacity: 0.0;
  }
}
.cursor {
  position: absolute;
  -webkit-animation: blinker .9s linear infinite;
          animation: blinker .9s linear infinite;
  border-right: 1px solid black;
  height: 70%;
  top: 15%;
  width: 1px;
  right: 0px;
  margin-right: 3px;
  display: none;
}

.numeric-input-overlay-active .cursor {
  display: block;
}

.numeric-keyboard {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  outline: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  background: white;
  /* font-family: Arial, "Helvetica Neue", Helvetica, sans-serif; */
  font-size: 35px;
  color: #1E1E1E;
  display: none;
  width: 100%;
  -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
}
.numeric-keyboard * {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}
.numeric-keyboard .panel-left {
  display: table-cell;
  vertical-align: top;
  height: 216px;
  width: 75%;
}
.numeric-keyboard .panel-right {
  display: table-cell;
  vertical-align: top;
  height: 216px;
  width: 25%;
  background-color: #ffffff;
}
.numeric-keyboard .panel-row {
  display: table;
  width: 100%;
}
.numeric-keyboard .button-container, .numeric-keyboard .button-container-0, .numeric-keyboard .button-container-clear, .numeric-keyboard .button-container-confirm {
  display: table-cell;
  height: 90px;
  vertical-align: middle;
  text-align: center;
  border: 2px dashed #BFBFBF;
  border-left: 1px solid #BFBFBF;
}
.numeric-keyboard .button-container:active, .numeric-keyboard .button-container-0:active, .numeric-keyboard .button-container-clear:active, .numeric-keyboard .button-container-confirm:active {
  background-color: #CCCCCC;
}
.numeric-keyboard .panel-col1 {
  border-left: none;
}
.numeric-keyboard .button-container-clear, .numeric-keyboard .button-container-confirm {
  height: 108px;
  font-size: 20px;
  width: 33%;
  font-weight: bold;
}
.numeric-keyboard .button-container-clear .keyboard, .numeric-keyboard .button-container-confirm .keyboard {
  display: inline-block;
}
.numeric-keyboard .button-container {
  width: 33%;
}
.numeric-keyboard .button-container-0 {
  width: 66%;
  border-left: none;
}

.numeric-keyboard-open {
  display: table;
}

.keyboard {
  background-image: url("../img/keyboard.svg");
  -webkit-background-size: 31px 26px;
          background-size: 31px 26px;
  background-repeat: no-repeat;
}
.no-svg .keyboard {
  background-image: url("../images/keyboard.png");
}
@media only screen and (-webkit-min-device-pixel-ratio: 2), not all, only screen and (min-resolution: 2dppx), only screen and (min-resolution: 192dpi) {
  .no-svg .keyboard {
    background-image: url("../images/<EMAIL>");
  }
}
@media only screen and (-webkit-min-device-pixel-ratio: 3), not all, only screen and (min-resolution: 3dppx), only screen and (min-resolution: 288dpi) {
  .no-svg .keyboard {
    background-image: url("../images/<EMAIL>");
  }
}

.keyboard {
  width: 21px;
  height: 16px;
  background-position: -5px -5px;
}
