/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {

    'use strict';

    customerApp.controller("LoginController", function ($rootScope, $scope, $http, $location, APIJson, AuthService, socketUtils, $timeout, AppUtil) {
        $scope.init = function () {
            $scope.cafeList = [];
            $scope.takeAwayList = [];
            $scope.outlets = [];
            $scope.selectedOutlet = null;
            $scope.unitCat = "CAFE";
            $scope.terminals = [];
            $scope.selectedTerminal = null;
            $scope.user = null;
            $scope.passcode = null;
            $rootScope.dataLoading = false;
            $scope.showLoginBox = true;
            $scope.autoConfig = false;
            $timeout(function () {
                $("#user").focus();
            }, 500);
            $scope.setAutoConfig();
        };

        $scope.setAutoConfig = function () {
            $scope.autoConfigData = AppUtil.getAutoConfigData();
            if($scope.autoConfigData!=null && !AppUtil.isEmptyObject($scope.autoConfigData)){
                $scope.selectedOutlet = {};
                $scope.selectedTerminal = {};
                $scope.selectedOutlet.id = $scope.autoConfigData.unitId;
                $scope.selectedTerminal.id = $scope.autoConfigData.selectedTerminalId;
                $scope.unitName = $scope.autoConfigData.unitName;
                $scope.autoConfig = true;
            }else{
                fetchOutlets("CAFE");
               // fetchTakeawayOutlets();
            }
        }

        $scope.resetLogin = function () {
            AppUtil.removeAutoConfigData();
            window.location.reload();
        }

        $scope.selectCat = function (cat) {
            //console.log(cat);
            $scope.unitCat = cat;
            if ($scope.unitCat == "CAFE") {
                $scope.outlets = $scope.cafeList;
                $scope.selectOutlet($scope.outlets[0]);
            }/* else if ($scope.unitCat == "TAKE_AWAY") {
                $scope.outlets = $scope.takeAwayList;
                $scope.selectOutlet($scope.outlets[0]);
            }*/
        };

        $scope.selectOutlet = function (outlet) {
            $scope.selectedOutlet = outlet;
            $scope.showTerminals();
        };

        $scope.showTerminals = function () {
            $scope.terminals = [];
            if ($scope.unitCat == "CAFE") {
                var terminalCount = $scope.selectedOutlet.noOfTerminal;
                for (var i = 1; i <= terminalCount; i++) {
                    $scope.terminals.push({id: i, name: "T" + i});
                }
            } else if ($scope.unitCat == "TAKE_AWAY") {
                var terminalCount = $scope.selectedOutlet.noOfTakeawayTerminals;
                for (var i = 1; i <= terminalCount; i++) {
                    var seedValue = (100 + i);
                    $scope.terminals.push({id: seedValue, name: "T" + seedValue});
                }
            }
            $scope.selectedTerminal = $scope.terminals[0];
        };

        $scope.hideKeyboard = function(){
            console.log(typeof Android);
            if(typeof Android == 'undefined'){
                return;
            }
            Android.hideKeyboard();
        }

        $scope.userNameInputFocus = function () {
            if(typeof Android == 'undefined'){
                return;
            }
            Android.clickedNameField();
        }

        $scope.userNameInputBlur = function () {
            if(typeof Android == 'undefined'){
                return;
            }
            Android.hideKeyboard();
        }
        
        $scope.userNumberInputFocus = function () {
            if(typeof Android == 'undefined'){
                return;
            }
            Android.clickedNumberField();
        };

        $scope.login = function () {
            if ($scope.user == null || $scope.user == "") {
                Materialize.toast('Please type user id!', 4000);
                return false;
            }
            if ($scope.passcode == null || $scope.passcode == "") {
                Materialize.toast('Please type passcode!', 4000);
                return false;
            }
            var reqObj = {
                unitId: $scope.selectedOutlet.id,
                userId: $scope.user,
                password: $scope.passcode,
                terminalId: $scope.selectedTerminal.id,
                screenType: "CUSTOMER",
                macAddress: $location.search().mac,
                application: "KETTLE_CRM"
            };
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.users.login,
                data: reqObj
            }).then(function success(response) {
                //console.log(response);
                if (response.data.sessionKeyId == null) {
                    Materialize.toast('User id & Passcode don\'t match!', 4000);
                } else {
                    //setting autoconfig
                    if(!$scope.autoConfig){
                        AppUtil.setAutoConfigData({unitId:reqObj.unitId,selectedTerminalId: reqObj.terminalId, unitName:$scope.selectedOutlet.name, region:$scope.selectedOutlet.region});
                    }
                    //registering socket on nginx server
                    var subscriberObj = {unit: reqObj.unitId, terminal: reqObj.terminalId, type: reqObj.screenType};

                    $http({
                        method: 'POST',
                        url: APIJson.urls.unitMetaData.unitData,
                        data: reqObj.unitId
                    }).then(function (unitDataObj) {
                        if(unitDataObj != null && unitDataObj.data != null && unitDataObj.data.hotspotEnabled === true) {
                            subscriberObj.isHotspotEnabled = unitDataObj.data.hotspotEnabled;
                        }
                        socketUtils.register(subscriberObj);
                        $rootScope.subscriberDetails = subscriberObj;
                        AuthService.setAuthorization(response.data.jwtToken);
                        $rootScope.loginObj = response.data;
                        $rootScope.region = $scope.autoConfig ? $scope.autoConfigData.region : $scope.selectedOutlet.region;
                        $location.path("dashboard-new");
                    }, function (response) {
                        console.log("error:" + response);
                    });                    
                }
                $rootScope.dataLoading = false;
            }, function error(response) {
                console.log("error:" + response);
                $rootScope.dataLoading = false;
            });
            $scope.goFullScreen();
        };

        $scope.changePasscode = function () {
            if ($scope.fpuser == null || $scope.fpuser == "") {
                Materialize.toast('Please fill user id!', 4000);
            } else if ($scope.fpunit == null || $scope.fpunit == "") {
                Materialize.toast('Please fill unit id!', 4000);
            } else if ($scope.fppass == null || $scope.fppass == "") {
                Materialize.toast('Please fill old passcode!', 4000);
            } else if ($scope.fpnpass == null || $scope.fpnpass == "") {
                Materialize.toast('Please fill new passcode!', 4000);
            } else {
                var reqObj = {
                    userId: $scope.fpuser,
                    unitId: $scope.fpunit,
                    password: $scope.fppass,
                    newPassword: $scope.fpnpass
                };
                $http({
                    method: 'POST',
                    url: APIJson.urls.users.changePassCode,
                    data: reqObj
                }).then(function success(response) {
                    //console.log(response);
                    if (response.data == true) {
                        Materialize.toast('Passcode changed successfully!', 4000);
                        $scope.showLoginBox = true;
                    } else {
                        Materialize.toast('Please provide correct old passcode!', 4000);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }
        };

        function fetchOutlets(code) {
            $http({
                method: 'GET',
                url: APIJson.urls.posMetaData.allUnits + '?category=' + code
            }).then(function success(response) {
                if (code === "CAFE") {
                    $scope.cafeList = response.data;
                    $scope.outlets = $scope.cafeList;
                    $scope.selectedOutlet = $scope.outlets[0];
                    $scope.showTerminals();
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }


        /*function fetchTakeawayOutlets() {
            $http({
                method: 'GET',
                url: APIJson.urls.posMetaData.takeawayUnits
            }).then(function success(response) {
                $scope.takeAwayList = response.data;
            }, function error(response) {
                console.log("error:" + response);
            });
        }*/


        $scope.goFullScreen = function () {
            var docElm = document.documentElement;
            if (docElm.requestFullscreen) {
                docElm.requestFullscreen();
            }
            else if (docElm.mozRequestFullScreen) {
                docElm.mozRequestFullScreen();
            }
            else if (docElm.webkitRequestFullScreen) {
                docElm.webkitRequestFullScreen();
            }
            else if (docElm.msRequestFullscreen) {
                docElm.msRequestFullscreen();
            }
        }

    });

})();
