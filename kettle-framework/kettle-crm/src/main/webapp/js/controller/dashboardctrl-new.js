/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';
    var regex = /^\d+$/;
    customerApp.controller("NewDashboardController", function ($rootScope, $scope, $http, $location, APIJson,
                                                               $timeout,$interval, socketUtils, AppUtil) {

        $scope.onlyNumbers = /^[0-9]*$/;
        var skipEmailInput = false;

        resetToOtp();
        
        $scope.$on("$destroy", function () {
            socketUtils.removeAllListeners();
        });

        socketUtils.receiveMessage(function (message) {
            //console.log("Received message :::: ", message);
            // console.log("#### ####CALLBACK CALLED CTRL NEW #### ####");
            processSocketMessage(message);
        },'dashboardctrlnew');

        socketUtils.pairingDone(function (message) {
            //console.log("Received message :::: ", message);
        });

        socketUtils.pairingFailed(function (message) {
            //console.log("Received message :::: ", message);
        });

        function processSocketMessage(message) {
            var key = Object.keys(message)[0];
            var emittedObj = message[key];
            switch (key) {
                case "ORDER_START":
                    if(!$scope.started){
                    	resetToOtp();
                        $scope.started = true;
                        $scope.userObj = {};
                        $scope.createUserObj();
                        $scope.startFreshScreenStrategy();
                        //createFeedbackFormObj();
                    }
                    socketUtils.emitMessage({"SCREEN_OPENED": $scope.userObj});
                    break;
                case "ORDER_PLACED":
                    /*if(!angular.isUndefined($scope.processStarted) && $scope.processStarted){
                        break;
                    }else{*/
                        if($scope.started){
                            $scope.started = false;
                        }
                        $scope.userObj = {};
                        $scope.createUserObj();
                        showToast('Order placed successfully. Thanks for visiting Chaayos!', 4000);
                        $timeout(function () {
                            $("#userFlowView").focus();
                        }, 100);
                        $timeout(function () {
                            $scope.resetCustomerScreen();
                        }, 500);
                    //}
                    break;

                case "ORDER_IN_PROGRESS":
                    if(emittedObj.otpNotReceivedClicked != undefined && emittedObj.otpNotReceivedClicked != null){
                        $scope.otpNotReceivedClicked = emittedObj.otpNotReceivedClicked;
                        document.getElementById("OTPScreen").style.opacity = "0";
                    	document.getElementById("OTPScreen").style.zIndex = "0";
                    	document.getElementById("thankYouScreen").style.opacity = "1";
                    	document.getElementById("thankYouScreen").style.zIndex = "9";
                    }
                    if (emittedObj.name != null) {
                        showToast('We are placing your order. Thanks for visiting Chaayos!', 4000);
                        //$scope.resetCustomerScreen();
                        if($scope.lastRedemptionSource != null && !$scope.reachedFinalThankuScreen){
                    		$scope.redemptionStrategy($scope.lastRedemptionSource);
                        }else{
                    		$scope.loadThankYouView();
                        }
                    } else {
                        $scope.fetchNameStrategy();
                    }
                    break;
                case "SWITCH_OTP":
                	$scope.switchOTP();
                	break;
                case "CUSTOMER_NOT_INTERESTED" :
                    $scope.fetchNameStrategy();
                    break;
/*                case "SKIP_FEEDBACK" :
                    $scope.skipFeedbackStrategy();
                    break;
*/               /* case "RECOMMENDATION" :
                    console.log('Recommendation', emittedObj)
                    $scope.userObj.recommendationDetail = emittedObj.recommendationDetail;
                    $scope.recommendationDetail = emittedObj.recommendationDetail;
                    $scope.loadRecommendationView();
                    break;
                case "RECOMMENDATION_RESULT_POS" :
                    if(emittedObj.recommendationDetail != null && emittedObj.recommendationDetail.availed){
                	 showToast('Added '+ emittedObj.recommendationDetail.name +' to your order', 4000);
                    }
                    $scope.recommendationDetail = emittedObj.recommendationDetail;
                    document.getElementById("recommendationScreen").style.opacity = "0";
                    document.getElementById("recommendationScreen").style.zIndex = "0";
                    $scope.offerImageTransition = false;
                    if($scope.lastRedemptionSource != null && !$scope.reachedFinalThankuScreen){
                	    $scope.redemptionStrategy($scope.lastRedemptionSource);
                    }else{
                	    $scope.loadFinalThankYou();
                    }
                    $scope.isSkipRecomendation = false;
                    break;
                case "SKIP_RECOMMENDATION" :
                    $scope.isSkipRecomendation = true;
                    $scope.loadFinalThankYou();
                    break;*/
                case "ORDER_CANCELLED":
                    if($scope.started){
                        $scope.started = false;
                    }
                    if (emittedObj.newCustomer) {
                        //contactVerified is set to false in the object from posCtrl.js itself
                        //TODO add a call to update contact verified equals to false
                        $scope.overrideContactVerification();
                    }
                    $scope.userObj = {};
                    $scope.createUserObj();
                    $timeout(function () {$("#userFlowView").focus();}, 100);
                    $scope.resetCustomerScreen();
                    $timeout.cancel($scope.thankYouTransition);
                    $timeout.cancel($scope.trueCallerInterval);
                    break;
                case "GIFT_CARD_ADDED":
                    if(emittedObj!=null){
                        $scope.giftCardList[emittedObj.itemId] = emittedObj;
                    }
                    $scope.loadGiftCardView();
                    break;
                case "GIFT_CARD_REMOVED":
                    if(emittedObj!=null){
                        //$scope.removeGiftCard(emittedObj.itemId);
                        //$scope.giftCardList.delete(emittedObj.itemId);
                        delete $scope.giftCardList[emittedObj.itemId];
                        if(Object.keys($scope.giftCardList).length<=0 || !$scope.isPendingGiftCard()){
                            $scope.loadThankYouView();
                        }
                    }
                    break;
                case "GIFT_CARD_REMOVED_ALL":
                    if(emittedObj!=null){
                    	if($scope.giftCardList!=null && Object.keys($scope.giftCardList).length>0){
                             $scope.giftCardList = {};
                             $scope.loadThankYouView();
                        }
                    }
                    break;
                case "GIFT_CARDS_REVALIDATE":
                    if(emittedObj!=null){
                        emittedObj.map(function (card) {
                            $scope.giftCardList[card.itemId] = card;
                        });
                    }
                    $scope.loadGiftCardView();
                    break;
                case "GIFT_CARDS_OTP_VERIFY":
                    //if($rootScope.loginObj.trueCaller=="TRUE_CALLER_FIRST"){
                      //  sendTrueCallerRequest($scope.userObj.contact.toString(),"GIFT_CARD");
                    //}else{
                        $scope.generateOTP("GIFT_CARD_VERIFICATION");
                        $scope.otpHeadline = "One time password to approve your gift card payment";
                    //}
                    break;
        		case "GIFT_CARDS_OTP_CHECK":
					$scope.resendOTPStatus();
					break;
                case "GIFT_CARDS_OTP_VERIFY_CANCEL":
                	document.getElementById("OTPScreen").style.opacity = "0";
                	document.getElementById("OTPScreen").style.zIndex = "0";
                	document.getElementById("thankYouScreen").style.opacity = "1";
                	document.getElementById("thankYouScreen").style.zIndex = "9";
                	break;
                case "EMAIL_INPUT_SKIPPED_BY_CRE":
                	 $scope.showEmailInput = false;
                	 skipEmailInput = true;
                	 $scope.updateUsernameStrategy ();
                	break;
                case "EMAIL_FORM_SKIPPED_BY_CRE":
                	$scope.skipEmail('bycre');
                	skipEmailInput = true;
                	break;
                case "TRUE_CALLER_PROFILE":
                    if(!AppUtil.isEmptyObject(emittedObj)
                        && !AppUtil.isEmptyObject(emittedObj.contact)
                        && !AppUtil.isEmptyObject(emittedObj.tcId)){
                        $scope.userObj.contact = emittedObj.contact;
                        $scope.userObj.contactVerified = true;
                        $scope.userObj.name = emittedObj.name;
                        $scope.userObj.email = emittedObj.email;
                        $scope.userObj.tcId = emittedObj.tcId;
                        $scope.userObj.emailVerified = false;
                        $scope.trueCallerProfileReceived();
                    }else{
                        $timeout.cancel($scope.trueCallerInterval);
                        if($scope.OtpSentCount < 1){
                            $scope.generateOTP("REGISTRATION", function () {
                                $scope.trueCallerStrategy(false);
                            });
                        }
                    }
                    break;
                case "TC_GIFT_CARD_VERIFY":
                case "TC_REDEMPTION_VERIFY":
                    handleTrueCallerVerification(emittedObj);
                    break;
                case "TABLE_ORDER":
                	$scope.otpSwitched = false;
                    $scope.otpText = "One time password to verify your number";
                    $scope.otpLength = 4;
                    $scope.started = true;
                    $scope.userObj = {};
                    $scope.createUserObj();
                    $scope.startFreshScreenStrategy(true);
                    $scope.userObj.contact = emittedObj.contact;
                    socketUtils.emitMessage({"SCREEN_OPENED": $scope.userObj});
                    $scope.contactLookUpStrategy();
                	break;
                case "OFFER_SIGNUP_REQUIRED":
                    $scope.generateOTP("OFFER");
                    break;
            }

        }
        
        function resetToOtp(){
            $scope.otpSwitched = false;
            $scope.otpText = "One time password to verify your number";
            $scope.otpLength = 4;
            $scope.otpPlaceHoler = "Enter OTP Here";

        }
        
        function handleTrueCallerVerification(emittedObj) {
            $scope.OTPType = $scope.trueCallerRequestType=="REDEMPTION" ? $scope.trueCallerRequestType : "GIFT_CARD_VERIFICATION";
            if(emittedObj!=null){
                $scope.userObj.otpVerified = true;
                if(emittedObj.accessToken!=null){
                    sendRedemptionMessage(function () {
                        $scope.trueCallerStrategy(false);
                    });
                }else{
                    $scope.generateOTP($scope.OTPType, function(){
                        $scope.trueCallerStrategy(false);
                    });
                }
                if(emittedObj.trueCallerProfile!=undefined && emittedObj.trueCallerProfile.tcId!=null){
                    updateTrueCallerInfo(emittedObj.trueCallerProfile);
                }
            }else{
                $scope.generateOTP($scope.OTPType, function () {
                    $scope.trueCallerStrategy(false);
                });
            }
        }

        $scope.init = function () {
            $("#contact,#otp").characterCounter();
            $scope.userObj = {};
            $scope.createUserObj();
            //$scope.recommendationDetail = null;
            $scope.lastRedemptionSource = null;
            $scope.reachedFinalThankuScreen = false;
            $rootScope.dataLoading = false;
            $scope.showEmailView = false;
            $scope.showEmailForm = true;
            $(".shadowBox").css("display", "none");
            $scope.giftCardList = {};
            $scope.isSkipRecomendation = false;
            $scope.skippedRedemption = false;
            $scope.chaiRedeemed = 0;
            $scope.OTPType="REDEMPTION";
            $scope.otpSent = false;
            //$scope.recommendationDetail = null;
        };

        $scope.startFreshScreenStrategy = function (skipContactScreen) {
        	if(skipContactScreen == null){
        		skipContactScreen = false;
        	}
        	skipEmailInput = false;
            $scope.processStarted = true;
            $scope.userObj.name = null;
            $scope.userObj.contact = null;
            $scope.OTPDisabled = true;
            $scope.userNameDisabled = true;
            $scope.showOTP = true;
            $scope.showContact = true;
            $scope.showEditContact = true;
            $scope.showUpdateUsername = false;
            $scope.showEmailForm = true;
            $scope.OtpSentCount = 0;
            $scope.showPromotionHead = false;
            $scope.exisitingUserNameUpdate = false;
            //$scope.showFeedbackScreen = false;
            //$scope.feedbackOrderMetadata = null;
            $scope.giftCardList = {};
            $scope.isSkipRecomendation = false;
            $scope.skippedRedemption = false;
            $scope.chaiRedeemed = 0;
            $scope.OTPType="REDEMPTION";
            //$scope.recommendationDetail = null;
            $scope.showEmailInput = false;
            $scope.otpHeadline = null;
            $scope.showTrueCallerContact = false;
            $scope.tc = undefined;
            if(!skipContactScreen){
            	document.getElementById("userContactScreen").style.opacity = "1";
            	document.getElementById("userContactScreen").style.zIndex = "9";
            }
            document.getElementById("thankYouScreen").style.opacity = "0";
            document.getElementById("thankYouScreen").style.zIndex = "0";
            document.getElementById("redemptionScreen").style.opacity = "0";
            document.getElementById("redemptionScreen").style.zIndex = "0";
            document.getElementById("OTPScreen").style.opacity = "0";
            document.getElementById("OTPScreen").style.zIndex = "0";
            document.getElementById("userFlowView").style.opacity = "1";
            document.getElementById("userFlowView").style.zIndex = "3";
            //document.getElementById("feedbackScreen").style.opacity = "0";
            //document.getElementById("feedbackScreen").style.zIndex = "0";
            //document.getElementById("recommendationScreen").style.opacity = "0";
            //document.getElementById("recommendationScreen").style.zIndex = "0";
            document.getElementById("giftCardScreen").style.opacity = "0";
            document.getElementById("giftCardScreen").style.zIndex = "0";
            //$scope.offerImageTransition = false;
            $timeout(function () {$("#contact").focus();}, 500);
        };

        $scope.resetTC = function (value) {
            $scope.showTrueCallerContact = value!=undefined ? value : false;
            if($scope.tc==undefined){
                $scope.tc = {contact:null};
            }
            $scope.tc.contact = null;
        };

        $scope.sendTrueCallerRequest = function(contact, type){
            if (contact == null || (contact != null && contact.toString().length <= 1
                    && (contact > 9 || contact < 6))) {
                $scope.resetTC();
            } else if (contact.toString().length == 10) {
                $scope.showTrueCallerContact = false;
                showToast('Checking your profile...', 4000);
                sendSignInRequest(contact,$rootScope.loginObj.trueCaller, function () {
                    if(AppUtil.isEmptyObject($scope.userObj.id) || !$scope.userObj.contactVerified){
                        sendTrueCallerRequest(contact, type);
                    }else{
                        $scope.detailsEntered();
                    }
                });
            }
        };

        function sendTrueCallerRequest(contact, type) {
            $scope.trueCallerRequestType = type;
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.customer.verifyTrueCaller,
                data: {
                    contact:contact,
                    unitId:$rootScope.loginObj.unitId,
                    terminal:$rootScope.loginObj.terminalId,
                    type: type
                },
            }).then(function(response) {
                $rootScope.dataLoading = false;
                var data = response.data;
                if (data!=undefined){
                    $scope.resetTC();
                    $scope.trueCallerRequestType = data.type;
                    $scope.userObj.contact = contact;
                    $scope.trueCallerStrategy(true);
                    socketUtils.emitMessage({TC_VERIFICATION: $scope.userObj});
                    $scope.truecallerRequestId = data.requestId;
                    if($scope.truecallerRequestId!=null && $scope.truecallerRequestId.trim().length>0){
                        if($scope.trueCallerRequestType=="REGISTRATION"){
                            $scope.trueCallerInterval = $timeout(initTrueCallerPoller, 20000);
                        }
                    }else {
                        $scope.resetTC();
                        showToast('Your profile is not verified with TrueCaller. One Time Password sent successfully', 5000);
                        $scope.generateOTP($scope.trueCallerRequestType, function () {
                            $scope.trueCallerStrategy(false);
                        });
                    }
                }
            }, function(response) {
                $scope.resetTC();
                $rootScope.dataLoading = false;
                $scope.generateOTP($scope.trueCallerRequestType, function () {
                    $scope.trueCallerStrategy(false);
                });
                console.log("error:", response);
            });
        }

        function initTrueCallerPoller() {
            if($scope.truecallerRequestId!=undefined && $scope.truecallerRequestId!=null
                && $scope.truecallerRequestId.trim().length>0 && $scope.userObj.tcId==undefined){
                $http({
                    method: 'GET',
                    url: APIJson.urls.customer.getTrueCallerProfile,
                    params: {requestId:$scope.truecallerRequestId}
                }).then(function success(response) {
                    if(!AppUtil.isEmptyObject(response.data) && !AppUtil.isEmptyObject(response.data.contact)){
                        $scope.userObj = response.data;
                        $scope.trueCallerProfileReceived();
                    }
                    /*else{
                        if($scope.OtpSentCount<=0){
                            $scope.generateOTP($scope.trueCallerRequestType,function () {
                                $scope.trueCallerStrategy(false);
                            });
                        }
                    }*/
                }, function error(response) {
                    console.log("error:" + response);
                });
            }
        }

        function updateTrueCallerInfo(trueCallerProfile) {
            $http({
                method: 'POST',
                url: APIJson.urls.customer.updateTrueCallerProfile,
                data: trueCallerProfile
            }).then(function success(response) {
                if(response.data!=undefined && response.data!=null){
                    $scope.userObj['name'] = response.data['name'];
                    $scope.userObj['email'] = response.data['email'];
                    $scope.userObj['contact'] = response.data['contact'];
                    $scope.userObj['tcId'] = response.data['tcId'];
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }

        function sendSignInRequest(contact, mode, callback) {
            var request = angular.copy($scope.userObj);
            if(contact != undefined && contact != null){
                request.contact = contact;
                request.signInMode = mode;
            }
            $http({
                method: 'POST',
                url: APIJson.urls.customer.signin,
                data: request
            }).then(function success(response) {
                if(response.data!=null && !angular.isUndefined(response.data.contact)){
                    $scope.userObj = response.data;
                    //$scope.userObj.recommendationDetail = $scope.recommendationDetail;
                    if(callback && typeof callback=='function'){
                        callback();
                    }
                }else{
                    $scope.startFreshScreenStrategy();
                    if(response!= null && response.data!= null && response.data.errorMessage != null && response.data.errorMessage.indexOf('Customer') >= 0){
                        showToast(response.data.errorMessage, 4000);
                    	
                    }else{
                        showToast('Please enter contact number again!', 4000);
                    	
                    }
                }
                $rootScope.dataLoading = false;
            }, function error(response) {
                //console.log("error:" + response);
                $rootScope.dataLoading = false;
            });
        }

        $scope.trueCallerProfileReceived = function () {
            $scope.trueCallerStrategy(false);
            $scope.userObj.otpVerified = true;
            socketUtils.emitMessage({DETAILS_ENTERED: $scope.userObj});
            if ($scope.userObj.id==null || $scope.userObj.id==0) {
                $scope.userObj.newCustomer = true;
                $scope.newCustomerWithTrueCallerStrategy();
                $scope.showEmailInput = $scope.userObj.email==null || $scope.userObj.email.trim().length > 0;
            } else if ($scope.userObj.name == null || $scope.userObj.name == "") {
                $scope.updateNameStrategy();
            }/* else if($scope.userObj.feedbackRequired) {
                $scope.feedbackStrategy();
            } */else if ($scope.userObj.eligibleForSignupOffer) {
                $scope.signUpOfferStrategy(true);
            } else {
                $scope.redemptionStrategy('fromNewUser');
            }

            // update true caller profile only if not a new customer and contact is verified
            if (!AppUtil.isEmptyObject($scope.userObj.tcId)) {
                var tcProfile = {
                    name:$scope.userObj.name,
                    contact:$scope.userObj.contact,
                    email:$scope.userObj.email,
                    tcId: $scope.userObj.tcId
                };
                updateTrueCallerInfo(tcProfile);
            }

            if ($scope.userObj.contactVerified) {
                if ($scope.userObj.eligibleForSignupOffer) {
                    $scope.signUpOfferStrategy(true);
                } else {
                    $scope.redemptionStrategy('fromNewUser');
                }

            }
        };


        $scope.contactLookUpStrategy = function () {
            if ($scope.userObj.contact == null ||
                ($scope.userObj.contact != null && $scope.userObj.contact.toString().length <= 1
                    && ($scope.userObj.contact > 9 || $scope.userObj.contact < 6))) {
                $scope.userObj.contact = null;
            } else if ($scope.userObj.contact.toString().length == 10) {
                $rootScope.dataLoading = true;
                socketUtils.emitMessage({PROCESS_STARTED: $scope.userObj});
                var mode = ($rootScope.loginObj.trueCaller == "TRUE_CALLER_FIRST")
                    ?  $rootScope.loginObj.trueCaller : "DEFAULT";
                sendSignInRequest($scope.userObj.contact, mode, function () {
                    $scope.detailsEntered();
                });
            }
        };


        $scope.detailsEntered = function(){
            socketUtils.emitMessage({DETAILS_ENTERED: $scope.userObj});
            if (!$scope.userObj.contactVerified) {
                $scope.newCustomerStrategy();
                $scope.showEmailInput = true;
            } else if ($scope.userObj.name == null || $scope.userObj.name == "") {
                $scope.updateNameStrategy();
            } /*else if($scope.userObj.feedbackRequired) {
                $scope.feedbackStrategy();
            } */
            else if ($scope.userObj.contactVerified) {                    
            	if ($scope.userObj.eligibleForSignupOffer) {
            		$scope.signUpOfferStrategy(true);
            	} else {
            		$scope.redemptionStrategy('fromNewUser');
            	}
            }
        };

        $scope.newCustomerStrategy = function(){
            $scope.showEditContact = false;
            if($rootScope.loginObj.trueCaller == "TRUE_CALLER_FIRST"){
                sendTrueCallerRequest($scope.userObj.contact.toString(),"REGISTRATION");
            }
            socketUtils.emitMessage({NEW_CUSTOMER: $scope.userObj});
            $scope.OTPDisabled = false;
            $scope.userNameDisabled = false;
            $scope.OtpSentCount += 1;
            $timeout(function () {
                $("#user").focus();
            }, 100);
        };


        $scope.newCustomerWithTrueCallerStrategy = function(){
            $scope.showEditContact = false;
            $scope.userNameDisabled = true;
            $scope.userObj.contactVerified = true;
            sendSignUpRequest();
            socketUtils.emitMessage({NEW_CUSTOMER: $scope.userObj});
        };


        $scope.trueCallerStrategy = function (show) {
            $scope.showEditContact = false;
            $scope.resetTC();
            $scope.OTPDisabled = show;
            $scope.userNameDisabled = show;
            $timeout(function () {
                var $element = $("#trueCallerLoader");
                if(show){
                    $element.addClass("show");
                }else{
                    $element.removeClass("show");
                }
            }, 100);
        };

        $scope.updateNameStrategy = function () {
            $scope.showOTP = false;
            $scope.showUpdateUsername = true;
            $scope.showEditContact = false;
            $scope.userNameDisabled = false;
            $scope.exisitingUserNameUpdate = true;
           	//skipEmailInputByCRE(true);
            $timeout(function () {
                $("#user").focus();
            }, 100);
        }

        $scope.fetchNameStrategy = function () {
            $scope.showContact = false;
            $scope.userNameDisabled = false;
            $scope.showOTP = false;
            $scope.showEditContact = false;
            $scope.showUpdateUsername = false;
            $timeout(function () {
                $("#user").focus();
            }, 100);
            $scope.loadFinalThankYou();
        };

        $scope.signUpOfferStrategy = function (verification) {
        	if($scope.userObj.otpVerified){
        		$scope.showRedemptionVerify = false;
        	}else{
        		$scope.showRedemptionVerify = verification;
        	}
            $scope.redeemChai(1);
            socketUtils.emitMessage({ELIGIBLE_FOR_SIGNUP_OFFER: $scope.userObj});
            if(verification){
                $scope.otpHeadline = "One time password to get a free desi chai on your 2nd visit";
                //showToast('Welcome back ' + $scope.userObj.name + ', You are eligible for a free chai. Please enter One Time Password received on your mobile to avail offer!', 4000);
            }
        };

        $scope.verifyCustomer = function () {
            if($scope.userObj.name!=null && $scope.userObj.name.trim()!='' && !validName($scope.userObj.name)){
                showToast('Please enter your real name.', 3000);
                $scope.userObj.otp = "";
            }else if($scope.userObj.email != null && $scope.userObj.email.trim() != '' && !validEmail($scope.userObj.email)){
                showToast('Please fill valid email address.', 3000);
                document.getElementById("otp").value = '';
            }else{
                if (document.getElementById("otp").value.length == $scope.otpLength) {
                    $scope.verifyNumber();
                } else if ($scope.userObj.otp.toString().length > $scope.otpLength) {
                    showToast('One Time Password should not be more than '+ $scope.otpLength + ' digits!', 4000);
                }
            }
        };

        $scope.updateUsernameStrategy = function () {
            if ($scope.userObj.name == null || $scope.userObj.name == "") {
                showToast('Please enter your name', 4000);
            } else if (!validName($scope.userObj.name)){
                showToast('Please enter your real name.', 4000);
            } else if ($scope.showEmailInput && ($scope.userObj.email == null || $scope.userObj.email.trim() == "")) {
                showToast('Please enter your email address', 4000);
            } else if ($scope.showEmailInput && !validEmail($scope.userObj.email)) {
                showToast('Please enter valid email address. All your receipts will be send to this address only!', 4000);
            } else {
                if (!$scope.showContact) {
                    document.getElementById("thankYouScreen").style.opacity = "1";
                    document.getElementById("thankYouScreen").style.zIndex = "9";
                    socketUtils.emitMessage({DETAILS_ENTERED: $scope.userObj});
                    $scope.loadThankYouView();
                    $scope.showUpdateUsername = false;
                    $scope.hideEmailView();
                    //showToast('Thanks for visiting Chaayos. We are placing your order!', 4000);
                } else {
                    $scope.userObj.name = toTitleCase($scope.userObj.name);
                    $rootScope.dataLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.customer.updateName,
                        data: $scope.userObj
                    }).then(function success(response) {
                        $rootScope.dataLoading = false;
                        $scope.userObj = response.data;
                        //$scope.userObj.recommendationDetail = $scope.recommendationDetail;
                        if ($scope.userObj.name == null || $scope.userObj.name == "") {
                            showToast('Connection Problem. Please try again!', 4000);
                        } /*else if ($scope.userObj.feedbackRequired) {
                            $scope.feedbackStrategy();
                        } */else if ($scope.userObj.eligibleForSignupOffer) {
                            $scope.signUpOfferStrategy(true);
                        } else if ($scope.exisitingUserNameUpdate) {
                            $scope.redemptionStrategy('fromNewUser');
                        } else {
                            $scope.redemptionStrategy('fromOTP');
                        }
                        socketUtils.emitMessage({DETAILS_ENTERED: $scope.userObj});
                    }, function error(response) {
                        $rootScope.dataLoading = false;
                    });
                }
            }
        };

        $scope.redemptionStrategy = function (source) {
            $scope.lastRedemptionSource = source;
            socketUtils.emitMessage({REDEMPTION_AVAILABLE: $scope.userObj});
            document.getElementById("userContactScreen").style.opacity = "0";
            document.getElementById("userContactScreen").style.zIndex = "0";
/*            document.getElementById("feedbackScreen").style.opacity = "0";
            document.getElementById("feedbackScreen").style.zIndex = "0";
*/            if (document.getElementById("OTPScreen").style.opacity == "1") {
                document.getElementById("OTPScreen").style.opacity = "0";
                document.getElementById("OTPScreen").style.zIndex = "0";
            }
            document.getElementById("redemptionScreen").style.opacity = "1";
            document.getElementById("redemptionScreen").style.zIndex = "9";
            if(!skipEmailInput){
            	 $scope.showEmailForm = ($scope.userObj.email==null || $scope.userObj.email.trim()=='');
            }else{
            	$scope.showEmailForm = false;
            	skipEmailInput = false;
            }
            if($scope.showEmailForm){
            	skipEmailFormByCRE();
            }
            var count = parseInt($scope.userObj.loyalityPoints / 60);
            $scope.chaiCount = count > 5 ? 5 : count;
            if (count > 0) {
                $scope.chaiArray = [];
                for (var i = 1; i <= $scope.chaiCount; i++) {
                    $scope.chaiArray.push(i);
                }
            }
            $("#userFlowView").focus();
            if (source == "fromOTP") {
            	if($scope.chaiCount > 0){
                    $scope.newUser = false;
                    $scope.showRedemptionVerify = true;
            	}else{
                	$scope.newUser = true;
                    $scope.showRedemptionVerify = false;
                    $scope.thankYouTransition = $timeout(function () {
                        //showToast('Thanks for visiting Chaayos. We are placing your order!', 4000);
                        $scope.loadThankYouView();
                    }, 15000);
            	}
            } else if (source == "fromNewUser") {
                $scope.newUser = false;
                if ($scope.chaiCount > 0) {
                    $scope.showRedemptionVerify = true;
                } else {
                    if($scope.userObj.email != null && $scope.userObj.email.trim() != ''){
                    $scope.thankYouTransition = $timeout(function () {
                        //showToast('Thanks for visiting Chaayos. We are placing your order!', 4000);
                        $scope.loadThankYouView();
                    }, 10000);
                }
            }
        }
        };

        $scope.skipEmail = function (byUser) {
            $scope.showEmailForm = false;
            if(byUser != 'bycre'){
            	socketUtils.emitMessage({EMAIL_FORM_SKIPPED_BY_CUSTOMER: $scope.userObj});
            }
            if($scope.chaiCount<=0){
                $scope.thankYouTransition = $timeout(function () {
                    //showToast('Thanks for visiting Chaayos. We are placing your order!', 4000);
                    $scope.loadThankYouView();
                }, 10000);
            }
        };

        $scope.loadOTPView = function (otpType) {
            $scope.hideEmailView();
            $scope.OTPType = otpType;
            socketUtils.emitMessage({OTP_SENT: $scope.userObj});
            $scope.showUserName = false;
            $scope.userObj.otp = null;
            document.getElementById("userContactScreen").style.opacity = "0";
            document.getElementById("userContactScreen").style.zIndex = "0";
            document.getElementById("thankYouScreen").style.opacity = "0";
            document.getElementById("thankYouScreen").style.zIndex = "0";
            document.getElementById("OTPScreen").style.opacity = "1";
            document.getElementById("OTPScreen").style.zIndex = "9";
            $scope.OtpSentCount = 1;
            $timeout(function () {
                $("#redemptionotp").focus();
            }, 500);
        };

        $scope.editContact = function () {
            $scope.hideKeyboard();
            $scope.createUserObj();
            $scope.showEditContact = true;
        };

        $scope.hideKeyboard = function(){
            if(typeof Android == 'undefined'){
                return;
            }
            Android.hideKeyboard();
        };

        $scope.resendOTPStrategy = function (type) {
            if ($scope.OtpSentCount == 2) {
                showToast('2nd last attempt!', 4000);
            }
            if ($scope.OtpSentCount == 3) {
                showToast('Last attempt!', 4000);
            }
            var url = APIJson.urls.customer.resendRedemptionOTP;
            if (type == "resendAuthorizationOTP") {
                url = APIJson.urls.customer.resendAuthorizationOTP;
            }
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: url,
                data: $scope.userObj
            }).then(function success(response) {
                socketUtils.emitMessage({OTP_RESENT: $scope.userObj});
                $rootScope.dataLoading = false;
                if (response.data == true) {
                    showToast('One Time Password resent successfully. Please check your mobile!', 4000);
                    $scope.OtpSentCount += 1;
                    $timeout(function () {
                        $("#otp").focus();
                    }, 100);
                } else {
                    showToast('Error sending One Time Password. Please try again!', 4000);
                }
            }, function error(response) {
                $rootScope.dataLoading = false;
            });
        };

        function sendSignUpRequest() {
            $scope.userObj.brandId = 1;
            $http({
                method: 'POST',
                url: APIJson.urls.customer.signup,
                data: $scope.userObj
            }).then(function success(response) {

                $rootScope.dataLoading = false;
                $scope.userObj = response.data;
                //$scope.userObj.recommendationDetail = $scope.recommendationDetail;
                socketUtils.emitMessage({OTP_STATUS: $scope.userObj});
                if ($scope.userObj.contactVerified) {

                    $scope.userObj.otpVerified = true;
                    skipEmailInputByCRE(false);
                    if ($scope.userObj.name == null || $scope.userObj.name == ""
                        || $scope.userObj.email == null || $scope.userObj.email.trim() == "") {
                        $scope.showOTP = false;
                        $scope.showUpdateUsername = true;
                        $scope.showEmailInput = true;
                        $timeout(function () {$("#user").focus();}, 100);
                    } /*else if ($scope.userObj.feedbackRequired) {
                        $scope.feedbackStrategy();
                    } */else if ($scope.userObj.eligibleForSignupOffer) {
                        $scope.signUpOfferStrategy(false);
                    } else {
                        $scope.redemptionStrategy("fromOTP");
                    }
                } else {
                    showToast('Incorrect One Time Password. Please enter again!', 4000);
                    $scope.userObj.otp = null;
                }
            }, function error(response) {
                $rootScope.dataLoading = false;
            });
        }

        $scope.verifyNumber = function () {
            $rootScope.dataLoading = true;
            $scope.userObj.otp = document.getElementById("otp").value;
            if ($scope.userObj.name != null && $scope.userObj.name != "") {
                $scope.userObj.name = toTitleCase($scope.userObj.name);
            }
            socketUtils.emitMessage({OTP_SUBMITTED: $scope.userObj});
            sendSignUpRequest();
        };

        function sendRedemptionMessage(callback) {
            $scope.userObj.otpVerified = true;
            if($scope.OTPType=="REDEMPTION"){
                if ($scope.chaiRedeemed > 0) {
                    $scope.userObj.chaiRedeemed = $scope.chaiRedeemed;
                    socketUtils.emitMessage({REDEMPTION: $scope.userObj});
                    /*var cup = $scope.chaiRedeemed > 1 ? "cups" : "cup";
                     showToast('We have added ' + $scope.chaiRedeemed + ' ' + cup + ' of Meri Wali Desi Chai to your order!', 10000);*/
                } else {
                    //showToast('Thanks for visiting Chaayos. We are placing your order!', 10000);
                }
            }else{
                socketUtils.emitMessage({OTP_VERIFIED: $scope.userObj});
            }
            if (document.getElementById("OTPScreen").style.opacity == "1") {
                document.getElementById("OTPScreen").style.opacity = "0";
                document.getElementById("OTPScreen").style.zIndex = "0";
                document.getElementById("thankYouScreen").style.opacity = "1";
                document.getElementById("thankYouScreen").style.zIndex = "9";
            }
            $scope.loadThankYouView();
            $timeout(function () {
                $("#userFlowView").focus();
            }, 100);
            if(callback!=undefined && typeof callback=="function"){
                callback();
            }
        }

        $scope.resendOTPStatus = function (){
			if($scope.userObj != null && $scope.userObj.otpVerified){
				$scope.releaseOTPVerified();
			}
		};
		
		$scope.releaseOTPVerified = function () {
			socketUtils.emitMessage({
				OTP_VERIFIED : $scope.userObj
			});	
		};
		
        $scope.verifyOTP = function (type) {
            if (document.getElementById("redemptionotp").value.length == $scope.otpLength) {
                $rootScope.dataLoading = true;
                $scope.userObj.otp = document.getElementById("redemptionotp").value;
                $http({
                    method: 'POST',
                    url: APIJson.urls.customer.verifyOTP,
                    data: $scope.userObj
                }).then(function success(response) {
                    if (response.data) {
                        sendRedemptionMessage();
                    } else {
                        showToast('Incorrect One Time Password. Please enter again!', 4000);
                        $scope.userObj.otp = null;
                    }
                    $rootScope.dataLoading = false;
                }, function error(response) {
                    //console.log("error:" + response);
                    $rootScope.dataLoading = false;
                });
            }
        };

        $scope.redeemChai = function (count) {
            $scope.chaiRedeemed = count;
            $scope.userObj.chaiRedeemed = $scope.chaiRedeemed;
            $scope.userObj.productId = 10;
            if ($scope.showRedemptionVerify && $scope.chaiRedeemed > 0) {
                /*if($rootScope.loginObj.trueCaller=="TRUE_CALLER_FIRST"){
                    sendTrueCallerRequest($scope.userObj.contact.toString(),"REDEMPTION");
                }else{*/
                    $scope.generateOTP("REDEMPTION");
                    $scope.otpHeadline = "One time password to redeem your free chai";
                //}
            } else {
                if ($scope.chaiRedeemed > 0) {
                    socketUtils.emitMessage({REDEMPTION: $scope.userObj});
                    var cup = $scope.chaiRedeemed > 1 ? "cups" : "cup";
                    //showToast('Welcome back ' + $scope.userObj.name + ', We have added ' + $scope.chaiRedeemed + ' ' + cup + ' of Meri Wali Desi Chai to your order!', 3000);
                } else {
                    socketUtils.emitMessage({REDEEM_LATER: $scope.userObj});
                    $scope.skippedRedemption = true;
                    //showToast('Thanks for visiting Chaayos. We are placing your order!', 10000);
                }
                $scope.loadThankYouView();
            }
        };

        $scope.skipToOTP = function(otpType){
            $scope.showTrueCallerContact = false;
            $timeout.cancel($scope.trueCallerInterval);
            $scope.generateOTP(otpType,function(){
               $scope.trueCallerStrategy(false);
            });
        };

        $scope.generateOTP = function (otpType, callback) {
        	resetToOtp();
        	$http({
                method: 'POST',
                url: APIJson.urls.customer.generateOTP,
                data: $scope.userObj
            }).then(function success(response) {
                if (response.data) {
                    document.getElementById("redemptionScreen").style.opacity = "0";
                    document.getElementById("redemptionScreen").style.zIndex = "0";
/*                    document.getElementById("feedbackScreen").style.opacity = "0";
                    document.getElementById("feedbackScreen").style.zIndex = "0";
*/                    if(otpType!="REGISTRATION"){
                        $scope.loadOTPView(otpType);
                    }
                    if (callback!=undefined && typeof callback=="function"){
                        callback();
                    }
                } else {
                    showToast('Network problem. Please try again!', 4000);
                }
                $rootScope.dataLoading = false;
            }, function error(response) {
                $rootScope.dataLoading = false;
                if (callback!=undefined && typeof callback=="function"){
                    callback();
                }
            });
        };

        $scope.switchOTP = function () {
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.customer.switchOTP,
                data: $scope.userObj
            }).then(function success(response) {
                if (response.data) {
                	$scope.otpSwitched = true;
                	$scope.otpText = "Please enter your Contact Number Again";
                    $scope.otpLength = 10;
                    $scope.otpPlaceHoler = "Enter Contact Number Again";
                } else {
                    showToast('Network problem. Please try again!', 4000);
                }
                $rootScope.dataLoading = false;
            }, function error(response) {
                $rootScope.dataLoading = false;
            });
        };

        
        /*$scope.feedbackStrategy = function () {
            $scope.hideKeyboard();
            socketUtils.emitMessage({FEEDBACK_PENDING: $scope.userObj});
            document.getElementById("userContactScreen").style.opacity = "0";
            document.getElementById("userContactScreen").style.zIndex = "0";
            document.getElementById("feedbackScreen").style.opacity = "1";
            document.getElementById("feedbackScreen").style.zIndex = "9";
            $timeout(function () {
                $("#feedbackScreen").focus();
            }, 500);
            createFeedbackFormObj();
        };*/
        
/*        $scope.skipFeedbackStrategy = function () {
            if ($scope.userObj.eligibleForSignupOffer) {
                $scope.signUpOfferStrategy(true);
            } else {
                $scope.redemptionStrategy('fromNewUser');
            }
        };
*/
/*        $scope.submitFeedback = function () {
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.customer.submitFeedback,
                data: $scope.feedbackFormObj
            }).then(function success(response) {
                $rootScope.dataLoading = false;
                if(response.data){
                    //showToast('Feedback submitted successfully!', 4000);
                    if ($scope.userObj.eligibleForSignupOffer) {
                        $scope.signUpOfferStrategy(true);
                    } else {
                        $scope.redemptionStrategy('fromNewUser');
                    }
                    socketUtils.emitMessage({FEEDBACK_SUBMITTED: $scope.userObj});
                }else {
                    showToast('Error submitting feedback!', 4000);
                }
            }, function error(response) {
                //console.log("error:" + response);
                $rootScope.dataLoading = false;
            });
        };
        
        $scope.setFeedbackMetadata = function () {
            if($scope.userObj.feedbackOrderMetadata!=null){
                $scope.userObj.feedbackOrderMetadata.products.forEach(function (product) {
                   product.style = {'background-image':"url('img/products/"+product.id+".png')"};
                });
            }
        };
*/
	
        $scope.setEmail = function () {
            if ($scope.userObj.email == null || $scope.userObj.email.trim() == "" || !validEmail($scope.userObj.email)) {
                showToast('Please enter valid email address.', 4000);
            } else {
                $rootScope.dataLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.customer.updateName,
                    data: $scope.userObj
                }).then(function success(response) {
                    $rootScope.dataLoading = false;
                    $scope.userObj = response.data;
                    //$scope.userObj.recommendationDetail = $scope.recommendationDetail;
                    if ($scope.userObj.email == null || $scope.userObj.email.trim() == "") {
                        showToast('Connection Problem. Please try again.', 4000);
                    } else {
                        socketUtils.emitMessage({EMAIL_ENTERED: $scope.userObj});
                        $scope.showEmailForm = false;
                        $timeout(function () {
                            $scope.hideEmailView();
                        }, 10000);
                        showToast("Please don't forget to verify your email using the verification link we will send with your e-bill receipt.", 4000);
                    }
                }, function error(response) {
                    //console.log("error:" + response);
                    $rootScope.dataLoading = false;
                });
            }
        };
        
        function skipEmailFormByCRE (){
        	socketUtils.emitMessage({SKIP_EMAIL_FORM: $scope.userObj});
        };
        
        function skipEmailInputByCRE (flag){
        	if(flag){
        		$scope.showEmailInput = true;
        	}
        	socketUtils.emitMessage({SKIP_EMAIL_INPUT: $scope.userObj});
        };

        $scope.loadThankYouView = function () {
            if($scope.isPendingGiftCard()){
                $scope.loadGiftCardView();
            }/*else if(!$scope.isSkipRecomendation && $scope.recommendationDetail != null && !$scope.recommendationDetail.availed){
        	    $scope.loadRecommendationView();
            }*/else{
        	    $scope.loadFinalThankYou();
            }
        };
        /*$scope.addRecommendation = function(){
            $scope.userObj.recommendationDetail.availed = true;
            socketUtils.emitMessage({RECOMMENDATION_RESULT: $scope.userObj});
            showToast('Added '+ $scope.userObj.recommendationDetail.name +' to your order', 4000);
            $scope.loadFinalThankYou();
        };*/
        /*$scope.skipRecommendation = function(){
            $scope.userObj.recommendationDetail.availed = false;
            socketUtils.emitMessage({RECOMMENDATION_RESULT: $scope.userObj});
            $scope.loadFinalThankYou();
        };*/
        
        $scope.loadFinalThankYou = function(){
            if (document.getElementById("OTPScreen").style.opacity == "1") {
        	return;
            }
            $scope.reachedFinalThankuScreen = true;
            $scope.loadEmailView();
            if (document.getElementById("redemptionScreen").style.opacity == "1") {
                document.getElementById("redemptionScreen").style.opacity = "0";
                document.getElementById("redemptionScreen").style.zIndex = "0";
            }
            if (document.getElementById("giftCardScreen").style.opacity == "1") {
                document.getElementById("giftCardScreen").style.opacity = "0";
                document.getElementById("giftCardScreen").style.zIndex = "0";
            }
            document.getElementById("thankYouScreen").style.opacity = "1";
            document.getElementById("thankYouScreen").style.zIndex = "9";
        };
        
        /*$scope.loadRecommendationView = function () {
            if ($scope.userObj.recommendationDetail != undefined && !$scope.userObj.recommendationDetail.availed 
        	    && (($scope.otpNotReceivedClicked != undefined && $scope.otpNotReceivedClicked != null && $scope.otpNotReceivedClicked) 
        		    || document.getElementById("OTPScreen").style.opacity != 1)
        	    && (document.getElementById("redemptionScreen").style.opacity == "1" || document.getElementById("thankYouScreen").style.opacity == "1"
                || document.getElementById("giftCardScreen").style.opacity == "1")){
                document.getElementById("redemptionScreen").style.opacity = "0";
                document.getElementById("redemptionScreen").style.zIndex = "0";
                document.getElementById("thankYouScreen").style.opacity = "0";
                document.getElementById("thankYouScreen").style.zIndex = "0";
                document.getElementById("giftCardScreen").style.opacity = "0";
                document.getElementById("giftCardScreen").style.zIndex = "0";
                document.getElementById("recommendationScreen").style.opacity = "1";
                document.getElementById("recommendationScreen").style.zIndex = "9";
                $scope.hideEmailView();
                setInterval(function(){ $("#oiTrans").addClass('active'); }, 50000);
            }
        };*/

        $scope.loadGiftCardView = function () {
            if (document.getElementById("thankYouScreen").style.opacity == "1" ||
               // document.getElementById("recommendationScreen").style.opacity == "1" ||
                (document.getElementById("redemptionScreen").style.opacity == "1" && ($scope.skippedRedemption || $scope.chaiRedeemed>0 || $scope.userObj.loyalityPoints<60))){
                document.getElementById("redemptionScreen").style.opacity = "0";
                document.getElementById("redemptionScreen").style.zIndex = "0";
               /* document.getElementById("recommendationScreen").style.opacity = "0";
                document.getElementById("recommendationScreen").style.zIndex = "0";*/
                document.getElementById("thankYouScreen").style.opacity = "0";
                document.getElementById("thankYouScreen").style.zIndex = "0";
                document.getElementById("giftCardScreen").style.opacity = "1";
                document.getElementById("giftCardScreen").style.zIndex = "9";
                $scope.hideEmailView();
            }
        };

        $scope.updateGiftCardCode = function (card) {
            card.cardNumber = card.cardNumber.replace(/[^a-zA-Z0-9]/g, "");
            if(card.cardNumber.length==6){
                /*card.codeAdded = true;
                socketUtils.emitMessage({GIFT_CARD_CODE_ADDED:card});*/
                if(!$scope.isDuplicateCard(card)){
                    $scope.validateGiftCard(card);
                }else{
                    card.cardNumber = "";
                    card.error = "Card already added!"
                }
            }else{
                card.error = "";
            }
        };

        $scope.isDuplicateCard = function (card) {
            var found = false;
            var cardNumbers = [];
            for(var i in $scope.giftCardList){
                if($scope.giftCardList[i].cardNumber.length>0){
                    if(!found && cardNumbers.indexOf(card.cardNumber.toUpperCase())>=0){
                        found = true;
                    }else{
                        cardNumbers.push($scope.giftCardList[i].cardNumber.toUpperCase());
                    }
                }
            }
            return found;
        };

        $scope.isPendingGiftCard = function () {
            var ret = false;
            for(var i in $scope.giftCardList){
                if(!$scope.giftCardList[i].isValid){
                    ret = true;
                }
            }
            return ret;
        };

        $scope.validateGiftCard = function (card) {
            var configData = AppUtil.getAutoConfigData();
            card.buyerId = $scope.userObj.id;
            card.empId = $rootScope.loginObj.userId;
            card.unitId = configData.unitId;
            card.terminalId  = configData.selectedTerminalId;
            card.validating = true;
            $http({
                method: 'POST',
                url: APIJson.urls.orderManagement.validateGiftCardsInOrder,
                data: [card]
            }).then(function success(response) {
                card.validating = false;
                response.data.map(function (card) {
                    $scope.giftCardList[card.itemId] = card;
                    if(card.isValid){
                        socketUtils.emitMessage({GIFT_CARD_CODE_ADDED:card});
                    }
                });
                if(!$scope.isPendingGiftCard()){
                    $scope.loadThankYouView();
                }
            }, function error(response) {
                card.validating = false;
            });
        };

        $scope.loadEmailView = function () {
            if ($scope.userObj.contactVerified !=null && $scope.userObj.contactVerified && ($scope.userObj.email == undefined || $scope.userObj.email == null || $scope.userObj.email.trim() == "")) {
        	$scope.showPromotionHead = false;
                $scope.bgImage = {"background-image": "url('img/emailBg.jpg')"};
                $scope.showEmailView = true;
            } else {
                $scope.showPromotionHead = true;
            }
        };

        $scope.hideEmailView = function () {
            $scope.showEmailView = false;
            $scope.showPromotionHead = true;
        };

        $scope.resetCustomerScreen = function () {
            $scope.trueCallerStrategy(false);
            $scope.hideEmailView();
            $scope.createUserObj();
           // $scope.recommendationDetail = null;
            $scope.otpNotReceivedClicked = null;
            $scope.lastRedemptionSource = null;
            //$scope.offerImageTransition = false;
            $scope.reachedFinalThankuScreen = false;
            document.getElementById("userFlowView").style.opacity = "0";
            document.getElementById("userFlowView").style.zIndex = "0";
            $(".shadowBox").css("display", "none");
            $("#toast-container").html("");
        };

        $scope.overrideContactVerification = function () {
            $scope.userObj.name = $scope.userObj.name != null ? toTitleCase($scope.userObj.name) : "";
            $scope.userObj.contactVerified = false;
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.customer.overrideContactVerification,
                data: $scope.userObj
            }).then(function success(response) {
                $rootScope.dataLoading = false;
                $scope.userObj = response.data;
                //$scope.userObj.recommendationDetail = $scope.recommendationDetail;
            }, function error(response) {
                //console.log("error:" + response);
                $rootScope.dataLoading = false;
            });
        };

        $scope.createUserObj = function () {
            $scope.userObj = {
                id: null,
                name: null,
                contact: null,
                email: null,
                loyalityPoints: null,
                contactVerified: null,
                emailVerified: null,
                unitId: $rootScope.loginObj.unitId,
                newCustomer: null,
                otp: null,
                chaiRedeemed: 0,
                productId: 10,
                //recommendation : false,
                //recommendationDetail : null,
                otpVerified:false
            };
        };

        $scope.userNameInputFocus = function () {
            if(typeof Android == 'undefined'){
        	return;
            }
            Android.clickedNameField();
        };
        
        $scope.userEmailInputFocus = function () {
            if(typeof Android == 'undefined'){
                return;
            }
            Android.clickedEmailField();
        };
        
        $scope.userNumberInputFocus = function () {
            if(typeof Android == 'undefined'){
                return;
            }
            Android.clickedNumberField();
        };

        $scope.userNameInputBlur = function () {
            if(typeof Android == 'undefined'){
        	return;
            }
            Android.hideKeyboard();
        };
        
        ////////////////////////// feedback form code //////////////////////////
        
       /* $scope.setRating = function (rating) {
            var title, options, contains;
            $("#ratingBox").find("span").each(function(index, val){
                if(index<rating){
                    $(val).addClass("selected");
                }else{
                    $(val).removeClass("selected");
                }
            });
            if(rating<4){
                title = "We are sorry for an unpleasant experience. Tell us what went wrong?";
                options = [
                    {name:"Chai/ Beverages",selected:false},
                    {name:"Food",selected:false},
                    {name:"Service",selected:false}
                ];
                if($scope.userObj.feedbackOrderMetadata.orderSource!='COD'){
                    options.push({name:"Ambience",selected:false});
                }
            }else if(rating == 4){
                title = "Uh ho! Can you tell us what can we improve upon?";
                options = [
                    {name:"Chai/ Beverages",selected:false},
                    {name:"Food",selected:false},
                    {name:"Service",selected:false}
                ];
                if($scope.userObj.feedbackOrderMetadata.orderSource!='COD'){
                    options.push({name:"Ambience",selected:false});
                }
            }else if(rating == 5){
                title = "Awesome! What did we do really well?";
                options = [
                    {name:"Chai/ Beverages",selected:false},
                    {name:"Food",selected:false},
                    {name:"Service",selected:false}
                ];
                if($scope.userObj.feedbackOrderMetadata.orderSource!='COD'){
                    options.push({name:"Ambience",selected:false});
                }
            }
            contains = false;
            $scope.feedbackFormObj.form_response.definition.fields.forEach(function (item) {
                if(item.type=="list"){
                    item.id = rating,
                    item.title = title;
                    item.options = options;
                    contains = true;
                }
            });
            if(!contains){
                $scope.feedbackFormObj.form_response.definition.fields.push({
                    id : rating,
                    title: title,
                    type: "list",
                    options: options
                });
            }
            $scope.feedbackFormObj.form_response.answers.forEach(function (item) {
                if(item.field.type=="rating"){
                    item.number = rating;
                }
                if(item.field.type=="multiple_choice"){
                    item.field.id = rating;
                    item.choices.labels = [];
                }
            });
        };
        
        $scope.setFeedbackOption = function (option) {
            if(option.selected){
                $scope.feedbackFormObj.form_response.answers.forEach(function (item) {
                    if(item.field.type=="multiple_choice"){
                        item.choices.labels.forEach(function (val, index) {
                            if(val==option.name){
                                item.choices.labels.splice(index,1);
                            }
                        });
                    }
                });
            }else{
                $scope.feedbackFormObj.form_response.answers.forEach(function (item) {
                    if(item.field.type=="multiple_choice"){
                        item.choices.labels.push(option.name);
                    }
                });
            }
            option.selected = !option.selected
        };*/

        /*$scope.removeGiftCard = function (itemId) {
            $scope.giftCardList.map(function (card, index) {
                if(card.itemId==itemId){
                    $scope.giftCardList.splice(index,1);
                }
            });
        };*/
        
        ////////////////////////// feedback form code //////////////////////////

        $scope.hideShadow = function () {
            $(".shadowBox").css("display", "none");
            $("#toast-container").html("");
        };

        function validEmail(email) {
            var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(email);
        }

        function validName(name) {
            var re = /^[0-9]*$/;
            return !re.test(name);
        }

        function toTitleCase(str) {
            return str.replace(/\w\S*/g, function (txt) {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            });
        }

        function showToast(msg, time) {
            $("#toast-container").html("");
            $(".shadowBox").css("display", "block");
            Materialize.toast(msg, time, '', function () {
                $(".shadowBox").css("display", "none");
            });
        }

        /*function createFeedbackFormObj() {
            var obj = {
                event_id: $scope.userObj.feedbackOrderMetadata!=null?$scope.userObj.feedbackOrderMetadata.feedbackEventId:null,  //generate random
                event_type: "form_response",
                form_response: {
                    form_id:"asdafbaf",
                    token: null,
                    submitted_at: null,
                    hidden: {
                        name: $scope.userObj.name,
                        feedbackUnit: $rootScope.loginObj.unitId,
                        unit : $scope.userObj.feedbackOrderMetadata!=null?$scope.userObj.feedbackOrderMetadata.unitName:null,
                        token: $scope.userObj.feedbackOrderMetadata!=null?$scope.userObj.feedbackOrderMetadata.feedbackToken:null,  //get token from backend in customer obj
                        contact: $scope.userObj.contact
                    },
                    definition: {
                        id: "xRILQe",
                        title: "Customer Screen Feedback",
                        fields: [
                            {
                                id: 28103526,
                                title: "Rate your experience",
                                type: "rating"
                            },{
                                id: 28103527,
                                title: null,
                                type: "list",
                                options: null
                            }
                        ]
                    },
                    answers: [
                        {
                            type: "number",
                            number: null,
                            field: {
                                id: 28103526,
                                type: "rating"
                            }
                        },
                        {
                            type: "choices",
                            choices: {
                                labels: []  // fill answered choices here
                            },
                            field: {
                                id: 28103527,
                                type: "multiple_choice"
                            }
                        }
                    ]
                }
            }
            $scope.feedbackFormObj = angular.copy(obj);
        }*/
    })/*.directive('fallbackSrc', function () {
        var fallbackSrc = {
            link: function postLink(scope, iElement, iAttrs) {
                iElement.bind('error', function () {
                    angular.element(this).attr("src", iAttrs.fallbackSrc);
                });
            }
        };
        return fallbackSrc;
    })*/.directive('restrictInput', [function () {
        return {
            restrict: 'A',
            link: function (scope, element, attrs) {
                var ele = element[0];
                var value = ele.value;
                ele.addEventListener('keyup', function (e) {
                    if (ele.value == null || ele.value == '' || regex.test(ele.value)) {
                        value = ele.value;
                    } else {
                        ele.value = value;
                    }
                });
            }
        };
    }]);
})();