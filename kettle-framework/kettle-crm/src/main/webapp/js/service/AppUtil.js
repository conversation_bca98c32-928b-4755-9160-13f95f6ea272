/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';

    angular.module('customerApp').factory('AppUtil', AppUtil);

    AppUtil.$inject = [];
    function AppUtil() {

        var service = {};

        service.autoConfigData = {};
        service.getAutoConfigData = getAutoConfigData;
        service.setAutoConfigData = setAutoConfigData;
        service.removeAutoConfigData = removeAutoConfigData;
        service.isEmptyObject = isEmptyObject;
        service.coverContact = coverContact;

        function getAutoConfigData(){
            if(this.autoConfigData == null || service.isEmptyObject(this.autoConfigData)){
                this.autoConfigData = JSON.parse(localStorage.getItem("crmConfigData"));
            }
            return this.autoConfigData;
        }

        function setAutoConfigData(autoConfigData){
            this.autoConfigData = autoConfigData;
            localStorage.setItem("crmConfigData", JSON.stringify(autoConfigData));
        }

        function removeAutoConfigData(){
            this.autoConfigData = null;
            localStorage.removeItem("crmConfigData");
        }

        function isEmptyObject(obj) {
            if (obj != undefined && obj != null) {
                if (typeof obj == 'string' || typeof obj == 'number')
                    return obj.toString().length == 0;
                else
                    return Object.keys(obj).length == 0;
            }
            return true;
        }

        function coverContact(contact){
            if(contact.length>7){
                return "*******"+contact.substring(7,contact.length);
            }else{
                var str = "";
                for(var i=0; i<contact.length;i++){
                    str+="*";
                }
                return str;
            }
        }

        return service;

    }



})();
