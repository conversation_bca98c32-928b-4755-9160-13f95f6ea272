/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('customerApp').service('socketUtils', function ($rootScope, $window, $location, Pubnub) {
        var socket = null; //getting object from window context
        var pairingStatus = false;
        var isMqttBased = false;
        $rootScope.subscriberDetails = null;

    var isMqttTestUnit = false;
    var client = null;
    //ctrl vs callback fn map
    var callbackMap = {};

    function initMqtt(clientId, brokerPort) {
        client = mqtt.connect(`ws://localhost:${brokerPort}`, {
            clientId: clientId
        });
        $rootScope.mqttClient = client;
        console.log(":: Initialising Mqtt Client ::", client);
        client.on('connect', function () {
            console.log(":: Connected to Mqtt Broker ::");
        });
        client.on('message', function (topic, message) {
            // message is Buffer
            // console.log(`[${topic}] : ${message.toString()}`);
            var stringMsg = message.toString(); 
            onMessageGlobal(topic, JSON.parse(stringMsg));
        });
        client.on('disconnect', function (packet) {
            // message is Buffer
            console.log(`Disconnect ${packet} : ${message.toString()}`);
        });
        client.on('close', function () {
            // message is Buffer
            console.log(`## ## Closed client`);
        });
    }

    function sendMessageToTopic(topic, message) {
        client.publish(topic, message);
    }
    
    function subscribeToTopic(topic) {
        console.log("Subscribing to topic name", topic);
        client.subscribe(topic);
    }

    function unsubscribeFromTopic(topic) {
        // console.log("Unsubscribing to topic name", topic);
        client.unsubscribe(topic);
    }
    
    function onMessageGlobal(topic, message) {
        //need to delete all unused one when on close modal is called
        var keys = Object.keys(callbackMap);
        // console.log("All callbacks should now be executed - Callback Map", callbackMap);
        keys.forEach((fn) => {
            //even if the callback is called we check the message received
            callbackMap[fn](message);
        });
    }
    
    function setUpMqttCallback(callback, key) {
        console.log(`setting callback @ [${key}]`);
        callbackMap[key] = callback;
    }        

        function emitOnSocket(eventName, data, callback) {
            data = JSON.stringify(data);
            if (socket != null) {
                socket.emit(eventName, data, function () {
                    //console.log("sending information....");
                    var args = arguments;
                    $rootScope.$apply(function () {
                        if (callback) {
                            callback.apply(socket, args);
                        }
                    });
                });
            }

        }

        function receiveOnSocket(eventName, callback) {
            if (socket != null) {
                socket.on(eventName, function () {
                    //console.log("waiting for message from nginx server....");
                    var args = arguments;
                    $rootScope.$apply(function () {
                        callback.apply(socket, args);
                    });
                });
            }

        }

        function getChannelName() {
            var suffix = getEnvType() + '_' + 'CustomerScreenChannel_' + $rootScope.subscriberDetails.unit + '_' + $rootScope.subscriberDetails.terminal;
            //console.log(suffix);
            return suffix;
        }

        function getEnvType(){
            var envType = "DEV";
            if($location.host().indexOf("internal")!=-1){
                envType = "SPROD";
            }else if($location.host().indexOf("prod")!=-1){
                envType = "PROD";
            }
            return envType;
        }

		function reSubscribe(){
			Pubnub.unsubscribe({
                channels: [getChannelName()]
            });
			Pubnub.subscribe({channels: [getChannelName()],triggerEvents: ['message', 'status', 'presence'],withPresence: true});
		}

        return {
            getPairingStatus: function () {
                return pairingStatus;
            },
            setPairingStatus: function (status) {
                pairingStatus = status;
            },
            getSocket: function (subscriber) {
                //console.log("Getting object from window object :: ",socket.io.engine.id);
                return socket;
            },
            register : function(subscriber) {
                $rootScope.subscriberDetails = subscriber;
                //console.log("pubnubs : register function called");
                var publisherKey = null;
                var subscribeKey = null;

                if(subscriber.isHotspotEnabled === true) {
                    initMqtt('KETTLE_CRM_WEB', '8689');
                    isMqttTestUnit = true;
                    return;
                }

                if (getEnvType().toLowerCase().indexOf( "prod")!=-1) {
                    publisherKey = '******************************************';
                    subscribeKey = '******************************************';
                }else{
                    publisherKey = '******************************************';
                    subscribeKey = '******************************************';

                }
                if (!$rootScope.initialized) {
                    // Initialize the PubNub service
                    Pubnub.init({
                        publishKey: publisherKey,
                        subscribeKey: subscribeKey
                    });
                    $rootScope.initialized = true;
                }

                /*
                ////console.log("socket : register function called");
                var host = "uat.kettle.chaayos.com";
                if ($location.host().indexOf("prod") != -1) {
                    host = "backup.kettle.chaayos.com";
                }
                if ($location.host().indexOf("dev") != -1) {
                    host = "uat.kettle.chaayos.com";
                }

                var url = $location.protocol() + "://" + host + ":3000";
                ////console.log("node process url :: ",url);
                var options = {
                    "force new connection": true,
                    "reconnection": true,
                    "reconnectionDelay": 2000,                  //starts with 2 secs delay, then 4, 6, 8, until 60 where it stays forever until it reconnects
                    "reconnectionDelayMax": 5000,             //1 minute maximum delay between connections
                    "reconnectionAttempts": "Infinity",         //to prevent dead clients, having the user to having to manually reconnect after a server restart.
                    "timeout": 10000,                           //before connect_error and connect_timeout are emitted.
                    "transports": ["websocket", "polling"]
                };
                socket = io.connect(url, options);
                socket.on('connect', function () {
                    ////console.log("socket connect called"+socket.io.engine.id);
                    ////console.log("registering client with id on nginx server :::::: "+ socket.io.engine.id);
                    emitOnSocket("register", subscriber);
                });

                socket.on('reconnect', function () {
                    if ($rootScope.subscriberDetails != undefined) {
                        emitOnSocket("register", $rootScope.subscriberDetails);
                    }
                });
                */
            },
            emitMessage : function(data) {
                try {
                    if(isMqttTestUnit) {
                        sendMessageToTopic(getChannelName(), JSON.stringify(data));
                        return;
                    }
                } catch (e) {
                    console.error("Error at emitting message :: ", data, e);
                    return;
                }
               
                Pubnub.publish({
                        channel: getChannelName(),
                        message: data
                    },
                    function (status, response) {
                    	if (status.error) {
                            // handle error
                            console.log("Emit Message Status : ", status);
                        } else {
                         //   console.log("Message Published w/ timetoken", Object.keys(data)[0]);
                        }
                    });
            },
            receiveMessage : function(callback, key){
                try {
                    if(isMqttTestUnit) {
                        subscribeToTopic(getChannelName());
                        setUpMqttCallback(callback, key);
                        return;
                    }
                } catch (e) {
                    console.error('Error while setting on message callback', e);
                    return;
                }
                
                Pubnub.addListener({
                    status: function (statusEvent) {
                       // console.log("Receive Message Status : ", statusEvent);
                    	if(statusEvent.category == 'PNNetworkUpCategory'){
                            reSubscribe();
                    	}
                    },
                    message: function (m) {
                        $rootScope.$apply(function () {
                            if (callback) {
                                callback(m.message);
                            }
                        });
                    },
                    presence: function (m) {
                        console.log(m)
                    },
                });
                Pubnub.subscribe({
                	channels: [getChannelName()],
                	triggerEvents: ['message', 'status', 'presence'],
                	withPresence: true
                });
            },
            pairingDone: function (callback) {
                //receiveOnSocket("partnerOnline", callback);
            },
            pairingFailed: function (callback) {
                //receiveOnSocket("partnerOffline", callback);
            },
            removeAllListeners: function (callback) {
                if (isMqttTestUnit) {
                    callbackMap = {};
                    if(client !== null && client !== undefined) {
                        try {
                            unsubscribeFromTopic(getChannelName());
                        } catch (e) {
                            console.log("Error while unsubbing from channel");
                        }
                    }
                    return;
                }
                try {
                    if ($rootScope.initialized) {
                        Pubnub.unsubscribe({
                            channels: [getChannelName()]
                        });
                    }
                } catch (e) {
                    //console.log(e);
                }

                /*
                    if (socket != null) {
                        socket.off("messageReceive");
                    }
                */
            }
        };
    });


