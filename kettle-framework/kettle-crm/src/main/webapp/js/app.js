/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

var customerApp = angular.module("customerApp",['ngRoute','pubnub.angular.service','ngTouch']);

customerApp.config(['$routeProvider',function($routeProvider){
	$routeProvider.
    when('/login', {
      templateUrl: 'views/login.html',
      controller: 'LoginController'
    }).when('/dashboard', {
        templateUrl: 'views/dashboard.html',
        controller: 'DashboardController'
    }).when('/dashboard-new', {
        templateUrl: 'views/dashboard-new.html',
        controller: 'NewDashboardController'
    }).
    otherwise({
      redirectTo: '/login'
    });
}]).service('AuthService', function() {
    var service = this;
    service.authorization = null;
    service.getAuthorization = getAuthorization;
    service.setAuthorization = function(authorization){
    	service.authorization = authorization;
    };
    function getAuthorization(){
    	return service.authorization;
    }
    return service;
})
.service('authInterceptor', function($rootScope, AuthService, $location, $timeout, $q) {
    var service = this;
    service.request = function(config) {
        config.headers.auth = AuthService.getAuthorization();
        config.timeout = $timeout(function(){ config.timedOut = true },10000,false);
        if(config.method=="POST" && config.data == undefined){
            config["data"] = {};
        }
        return config;
    };
    service.responseError = function(response) {
        if (response.status === 401) {
        	console.log("Response Error 401",response);
			AuthService.setAuthorization(null);
            $location.path('/login');
        }

        if(response.config.timedOut){
            return $q.reject(response);
        }
        return response;
    };
})
.config(['$httpProvider',function($httpProvider) {
    $httpProvider.interceptors.push('authInterceptor');
}])
.run(function($rootScope, $location, AuthService){
	$rootScope.$on('$locationChangeStart', function (event, next, current) {
        // redirect to login page if not logged in and trying to access a restricted page
        var restrictedPage = $.inArray($location.path(), ['/login']) === -1;
        var loggedIn = AuthService.getAuthorization()!=null;
        if (restrictedPage && !loggedIn) {
			$location.path("/login");
        } 
		
    });
})
.directive('stringToNumber', function() {
  return {
		require: 'ngModel',
	link: function(scope, element, attrs, ngModel) {
	  ngModel.$parsers.push(function(value) {
		return '' + value;
	  });
	  ngModel.$formatters.push(function(value) {
		return parseFloat(value, 10);
	  });
	}
  };
});

