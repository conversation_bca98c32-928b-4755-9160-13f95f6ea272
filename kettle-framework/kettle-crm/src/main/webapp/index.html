<!DOCTYPE html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"/>
    <title>Chaayos - Experiments With Chai </title>
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link type="text/css" rel="stylesheet" href="css/materialize.min.css" media="screen,projection"/>
    <link rel="stylesheet" type="text/css" href="css/style.css?v0.1">
    <!--<link rel="stylesheet" type="text/css" href="css/numeric-keyboard.css">-->
</head>

<body data-ng-app="customerApp">
<div data-ng-view></div>


<div class="loaderView valign-wrapper" data-ng-show="dataLoading">
    <div class="preloader-wrapper big active" style="margin:auto;margin-top: 250px;">
        <div class="spinner-layer spinner-blue">
            <div class="circle-clipper left">
                <div class="circle"></div>
            </div>
            <div class="gap-patch">
                <div class="circle"></div>
            </div>
            <div class="circle-clipper right">
                <div class="circle"></div>
            </div>
        </div>

        <div class="spinner-layer spinner-red">
            <div class="circle-clipper left">
                <div class="circle"></div>
            </div>
            <div class="gap-patch">
                <div class="circle"></div>
            </div>
            <div class="circle-clipper right">
                <div class="circle"></div>
            </div>
        </div>

        <div class="spinner-layer spinner-yellow">
            <div class="circle-clipper left">
                <div class="circle"></div>
            </div>
            <div class="gap-patch">
                <div class="circle"></div>
            </div>
            <div class="circle-clipper right">
                <div class="circle"></div>
            </div>
        </div>

        <div class="spinner-layer spinner-green">
            <div class="circle-clipper left">
                <div class="circle"></div>
            </div>
            <div class="gap-patch">
                <div class="circle"></div>
            </div>
            <div class="circle-clipper right">
                <div class="circle"></div>
            </div>
        </div>

    </div>
</div>


<script type="text/javascript" src="js/jquery-2.2.2.min.js"></script>
<script type="text/javascript" src="js/materialize.min.js"></script>
<script type="text/javascript" src="js/angular.min.js"></script>
<script type="text/javascript" src="js/angular-route.min.js"></script>
<script type="text/javascript" src="./js/pubnub.4.4.0.min.js"></script>
<script type="text/javascript" src="./js/pubnub-angular-4.1.0.min.js"></script>
<script type="text/javascript" src="./js/mqtt.min.js"></script>

<script type="text/javascript" src="./js/angular-mocks.js"></script>
<script type="text/javascript" src="./js/touch.js"></script>
<!-- <script type="text/javascript" src="./js/numeric-keyboard-module.js"></script> -->
<script type="text/javascript" src="./js/socket.io-min.js"></script>
<script type="text/javascript">
    jQuery.extend(jQuery.expr[':'], {
        focusable: function (el, index, selector) {
            return $(el).is('a, button, :input, [tabindex]');
        }
    });
</script>


<!-- Angular controllers & services start here -->
<script src="js/app.js?v=0.1"></script>

<script src="js/controller/loginctrl.js?v=0.1"></script>
<!-- <script src="js/controller/dashboardctrl.js"></script> -->
<script src="js/controller/dashboardctrl-new.js?v=0.1"></script>
<script src="js/service/socketUtils.js?v=0.1"></script>

<script src="js/service/APIJson.js?v=0.1"></script>
<script src="js/service/AppUtil.js?v=0.1"></script>
<!-- Angular controllers & services end here -->
</body>
</html>