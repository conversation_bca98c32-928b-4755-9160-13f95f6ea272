
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-ng-init="init()"></div>
<div class="card cardContainer">
    <!-- <div class="card-image newUser">
        <img class="activator" src="img/newUser.jpg">
    </div> -->
    <div class="card-image newUser {{region}}" style="background-size:cover;">
    </div>
</div>

<div id="userFlowView" tabindex="100">
    <!--<div id="emailView" data-ng-show="showEmailView">
        <div class="row">
            <div class="col s12" data-ng-show="showEmailForm">
                <p class="mailText">
                    Tell us your Email to get a cup of
                    <span style="font-weight:bold;">Desi Chai Free</span> - and yeah
                    <span style="font-weight:bold;">We Don't Spam</span>
                </p>

                <form>
                    <div class="input-field col s9">
                        <input tabindex="1" id="email" type="email" data-ng-model="userObj.email" autocomplete="off"
                               data-ng-blur="userNameInputBlur()" data-ng-focus="userNameInputFocus()" placeholder="Email"/>
                    </div>
                    <div class="input-field col s3">
                        <button type="submit" class="waves-effect waves-light btn white black-text emailBtn"
                                data-ng-click="setEmail()">
                            Submit
                        </button>
                    </div>
                </form>
            </div>
            <div class="col s10 offset-s1" data-ng-hide="showEmailForm">
                <p style="font-size:24px;color:#fff;">
                    Please verify your email using verification link sent on your email!
                </p>
            </div>
        </div>
    </div>-->

    <div id="userContactScreen">
        <!-- for units having otp_first flow-->
        <div class="row formBox" data-ng-if="loginObj.trueCaller=='OTP_FIRST'">  <!-- data-ng-change="contactLookUpStrategy()"   -->
            <div data-ng-show="!showTrueCallerContact">
                <div class="col s12">
                    <div class="input-field col s12" data-ng-show="showEditContact && showContact">
                        <input tabindex="1" id="contact" type="number" data-ng-model="userObj.contact" autocomplete="off"
                               data-ng-change="contactLookUpStrategy()" length="10" maxlength="10" string-to-number restrict-input placeholder="Enter Contact No Here"
                               data-ng-focus="userNumberInputFocus()" data-ng-blur="userNameInputBlur()"/>
                        <span data-ng-if="userObj.contact!=null" data-ng-click="userObj.contact=null" class="clearEmail">&times</span>
                        <label class="active customerInfoLabel" for="contact">Contact</label>
                    </div>
                </div>
                <div class="col s12" data-ng-hide="showEditContact">
                    <div class="col s12">
                        <p style="font-weight:300;font-size:28px;">
                            Contact: <span style="font-size:44px;">{{userObj.contact}}</span>
                            <span class="contactVerified" data-ng-show="userObj.contactVerified==true">Verified</span>
                            <span class="editContactLink" data-ng-click="editContact()"
                                          data-ng-show="!showEditContact && userObj.contactVerified!=true">Edit</span>
                        </p>
                    </div>
                </div>
                <div class="col s12">
                    <div class="input-field col s12">
                        <input tabindex="2" id="user" type="text" data-ng-model="userObj.name"
                               data-ng-disabled="userNameDisabled" data-ng-focus="userNameInputFocus()"
                               style="text-transform:capitalize;" data-ng-class="{'disabled':userNameDisabled}"
                               data-ng-blur="userNameInputBlur()" autocomplete="off" placeholder="Enter Your Name Here" id="realName"/>
                        <label class="active customerInfoLabel" for="user">Name</label>
                    </div>
                </div>
                <div class="col s12" data-ng-if="showEmailInput">
                    <div class="input-field col s12">
                        <input tabindex="3" id="uemail" type="email" data-ng-model="userObj.email"
                               data-ng-disabled="emailDisabled" data-ng-focus="userNameInputFocus()"
                               data-ng-class="{'disabled':emailDisabled}"
                               data-ng-blur="userNameInputBlur()" autocomplete="off" placeholder="Enter Your Email Here"/>
                        <label class="active customerInfoLabel" for="user">Email</label>
                    </div>
                </div>
                <div class="col s12" data-ng-show="showOTP">
                    <div class="input-field col s12">
                        <input tabindex="4" id="otp" type="number" data-ng-model="userObj.otp" data-ng-disabled="OTPDisabled"
                               length="{{otpLength}}" maxlength="{{otpLength}}" string-to-number data-ng-change="verifyCustomer()"
                               autocomplete="off" restrict-input placeholder="Enter OTP Here" data-ng-focus="userNumberInputFocus()" data-ng-blur="userNameInputBlur()"/>
                        <label class="active customerInfoLabel" for="otp">{{otpText}}</label>
                    </div>
                </div>
                <div class="col s12">
                    <button class="waves-effect waves-light btn col s12 chaiBtn" data-ng-click="updateUsernameStrategy()"
                            data-ng-show="showUpdateUsername">
                        Submit
                    </button>
                </div>
                <div class="col s12 right-align">
                    <button class="waves-effect waves-light btn-flat" data-ng-show="showOTP && OtpSentCount<4"
                            data-ng-click="resendOTPStrategy('resendAuthorizationOTP')"
                            data-ng-disabled="OTPDisabled" data-ng-class="{disabled:OTPDisabled}">
                        Resend OTP
                    </button>
                </div>
            </div>
            <div class="col s12 center-align" data-ng-show="!showTrueCallerContact"> ----- OR ----- </div>
            <div class="col s12">
                <div class="input-field col s12" data-ng-show="showTrueCallerContact">
                    <input tabindex="1" id="tc.contact" type="number" data-ng-model="tc.contact" autocomplete="off"
                           data-ng-change="sendTrueCallerRequest(tc.contact,'REGISTRATION')" length="10" maxlength="10"
                           string-to-number restrict-input placeholder="Enter Contact No Here"
                           data-ng-focus="userNumberInputFocus()" data-ng-blur="userNameInputBlur()"/>
                    <span data-ng-if="tc.contact!=null" data-ng-click="tc.contact=null" class="clearEmail">&times</span>
                    <label class="active customerInfoLabel" for="tc.contact">Contact</label>
                </div>
                <div class="col s12 center-align" style="margin-bottom: 35px;" data-ng-show="!showTrueCallerContact">
                    <button class="waves-effect waves-light btn" style="background-color: #0087FF;height:60px;"
                            data-ng-click="resetTC(true);">
                        <span class="white-text" style="font-family: sans-serif;font-weight: 550;">Register via
                            <img style="height:20px;" src="./img/tc-logos/LOGO_WHITE.svg" />
                        </span>
                    </button>
                </div>
                <div class="col s12 right-align" data-ng-show="showTrueCallerContact">
                    <button class="waves-effect waves-light btn-flat" style="height: 60px;"
                            data-ng-click="resetTC(false);">
                        Back to OTP
                    </button>
                </div>
            </div>
        </div>

        <!-- for units not having otp_first flow-->
        <div class="row formBox" data-ng-if="loginObj.trueCaller!='OTP_FIRST'">  <!-- data-ng-change="contactLookUpStrategy()"   -->
            <div class="col s12">
                <div class="input-field col s12" data-ng-show="showEditContact && showContact">
                    <input tabindex="1" id="contact" type="number" data-ng-model="userObj.contact" autocomplete="off"
                           data-ng-change="contactLookUpStrategy()" length="10" maxlength="10" string-to-number restrict-input placeholder="Enter Contact No Here"
                           data-ng-focus="userNumberInputFocus()" data-ng-blur="userNameInputBlur()"/>
                    <span data-ng-if="userObj.contact!=null" data-ng-click="userObj.contact=null" class="clearEmail">&times</span>
                    <label class="active customerInfoLabel" for="contact">Contact</label>
                </div>
            </div>
            <div class="col s12" data-ng-hide="showEditContact">
                <div class="col s12">
                    <p style="font-weight:300;font-size:28px;">
                        Contact: <span style="font-size:44px;">{{userObj.contact}}</span>
                        <span class="contactVerified" data-ng-show="userObj.contactVerified==true">Verified</span>
                        <span class="editContactLink" data-ng-click="editContact()"
                              data-ng-show="!showEditContact && userObj.contactVerified!=true">Edit</span>
                    </p>
                </div>
            </div>
            <div class="col s12">
                <div class="input-field col s12">
                    <input tabindex="2" id="user" type="text" data-ng-model="userObj.name"
                           data-ng-disabled="userNameDisabled" data-ng-focus="userNameInputFocus()"
                           style="text-transform:capitalize;" data-ng-class="{'disabled':userNameDisabled}"
                           data-ng-blur="userNameInputBlur()" autocomplete="off" placeholder="Enter Your Name Here" id="realName"/>
                    <label class="active customerInfoLabel" for="user">Name</label>
                </div>
            </div>
            <div class="col s12" data-ng-if="showEmailInput">
                <div class="input-field col s12">
                    <input tabindex="3" id="uemail" type="email" data-ng-model="userObj.email"
                           data-ng-disabled="emailDisabled" data-ng-focus="userNameInputFocus()"
                           data-ng-class="{'disabled':emailDisabled}"
                           data-ng-blur="userNameInputBlur()" autocomplete="off" placeholder="Enter Your Email Here"/>
                    <label class="active customerInfoLabel" for="user">Email</label>
                </div>
            </div>
            <div class="col s12" data-ng-show="showOTP">
                <div class="input-field col s12">
                    <input tabindex="4" id="otp" type="number" data-ng-model="userObj.otp" data-ng-disabled="OTPDisabled"
                           length="{{otpLength}}" maxlength="{{otpLength}}" string-to-number data-ng-change="verifyCustomer()"
                           autocomplete="off" restrict-input placeholder="Enter OTP Here" data-ng-focus="userNumberInputFocus()" data-ng-blur="userNameInputBlur()"/>
                    <label class="active customerInfoLabel" for="otp">{{otpText}}</label>
                </div>
            </div>
            <div class="col s12">
                <button class="waves-effect waves-light btn col s12 chaiBtn" data-ng-click="updateUsernameStrategy()"
                        data-ng-show="showUpdateUsername">
                    Submit
                </button>
            </div>
            <div class="col s12 right-align">
                <button class="waves-effect waves-light btn-flat"  style="margin-bottom:90px" data-ng-show="showOTP && OtpSentCount<4"
                        data-ng-click="resendOTPStrategy('resendAuthorizationOTP')"
                        data-ng-disabled="OTPDisabled" data-ng-class="{disabled:OTPDisabled}">
                    Resend OTP
                </button>
            </div>
        </div>
    </div>

<!--     <div id="feedbackScreen">
        <div class="feedbackContainer">
            <h4 class="center-align" data-ng-if="userObj.feedbackOrderMetadata.orderSource!='COD'">How was your last visit at<br />{{userObj.feedbackOrderMetadata.unitName}}</h4>
            <h4 class="center-align" data-ng-if="userObj.feedbackOrderMetadata.orderSource=='COD'">How was your last delivery order?</h4>
            <div class="row" style="margin-bottom: 0px;">
                <div class="col s12" style="margin-top: 0px;">
                    <p class="center-align" style="font-size: 18px;margin: 0;">{{userObj.feedbackOrderMetadata.orderDay}}, {{userObj.feedbackOrderMetadata.orderTime}}</p>
                </div>
            </div>
            <div class="row">
                <div class="col s12">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s12 center-align" style="font-size: 18px; margin: 0px;">
                            <div class="productBox" data-ng-repeat="product in userObj.feedbackOrderMetadata.products">
                                <div class="productImg">  ng-style="product.style"
                                    <img data-ng-src="img/products/{{product.id}}.png" fallback-src="img/products/fallback/{{product.type}}.png" />
                                </div>
                                {{product.name}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <form name="feedbackForm">
                <div class="feedbackQuestionBox" data-ng-repeat="item in feedbackFormObj.form_response.definition.fields">
                    <p class="feedbackQuestion center-align">{{item.title}}</p>
                    <p id="ratingBox" class="center-align" data-ng-if="item.type=='rating'">
                        <span data-ng-click="setRating(1)"></span>
                        <span data-ng-click="setRating(2)"></span>
                        <span data-ng-click="setRating(3)"></span>
                        <span data-ng-click="setRating(4)"></span>
                        <span data-ng-click="setRating(5)"></span>
                    </p>
                    <div  data-ng-if="item.type=='list'" class="center-align">
                        <button class="feedbackOptionBtn" data-ng-repeat="option in item.options"
                             data-ng-click="setFeedbackOption(option)" data-ng-class="{'selected':option.selected}">{{option.name}}</button>
                    </div>
                </div>
                <div class="feedbackQuestionBox center-align">
                    <button class="btn feedbackSubmitBtn" data-ng-click="submitFeedback()" data-ng-show="feedbackFormObj.form_response.answers[0].number!=null">Submit</button>
                </div>
            </form>
        </div>
    </div>
 -->    
    <div id="trueCallerLoader">
       <div id="trueCallerBg">
           <h3><img src="img/skipOtp.png" id="skipToOtpBtn" ng-click="skipToOTP(trueCallerRequestType)"></h3>
       </div>
    </div>

    <div id="redemptionScreen">
        <div class="row center-align" style="margin-top:220px; font-size:28px;">
            <div class="col s12" data-ng-if="!showEmailForm">
                <p class="welcomeText">Hi <span>{{userObj.name}}</span>!</p>
                <p data-ng-if="newUser && eligibleForSignupOffer">Welcome to Chaayos LoyalTea<!--, get a FREE Desi Chai on
                    your next order-->. You are eligible for a free Desi Chai.</p>
                <div data-ng-if="newUser && !eligibleForSignupOffer">
                    <p>Welcome to Chaayos LoyalTea.</p>
                    <p>You have a Desi Chai Free on your next visit.</p>
                    <img src="img/6thvisit.png" />
                </div>
                <p data-ng-if="!newUser">Your Registered Number: {{userObj.contact}}</p>
                <p data-ng-if="!newUser">Current Points: {{userObj.loyalityPoints | number}} <span
                        data-ng-if="chaiCount>0">({{chaiCount}} Free Chai)</span></p>
            </div>
            <div class="col s12" data-ng-if="!newUser && chaiCount>0  && !showEmailForm">
                <button class="waves-effect waves-light btn-large redeemBtn" data-ng-repeat="chai in chaiArray"
                        data-ng-click="redeemChai(chai)">{{chai}} <span data-ng-if="chai==1">Chai</span><span data-ng-if="chai>1">Chais</span> Free
                </button>
                <br/><br/>
                <button class="waves-effect waves-light btn-large" data-ng-click="redeemChai(0)">Have it Later</button>
            </div>
            <div class="col s12" data-ng-if="!newUser && chaiCount==0  && !showEmailForm">
                <p style="font-size:31px;">
                    You are <span style="font-size:51px;">{{(60-userObj.loyalityPoints)/10}}</span>
                    <span data-ng-if="((60-userObj.loyalityPoints)/10)>1">visits</span>
                    <span data-ng-if="((60-userObj.loyalityPoints)/10)==1">visit</span>
                    away from a Free Chai
                </p>
            </div>
            <div data-ng-if="showEmailForm" style="position:fixed;top:0px;right:0px;bottom:0px;left:0px;background:#fff;">
                <!--<div style="background:#375125;padding: 40px 20px;text-align:center;color:#fff;font-size: 40px;">
                    Chaayos is Going Green
                </div>-->
                <div style="padding:20px 50px;">
                    <p style="text-align: left;">Register your email today to get your next Desi Chai FREE!</p>
                    <form style="margin: 45px 0;">
                        <div class="input-field col s12">
                            <input tabindex="1" id="email" type="email" data-ng-model="userObj.email" autocomplete="off"
                                   data-ng-blur="userNameInputBlur()" data-ng-focus="userNameInputFocus()" placeholder="Email"/>
                        </div>
                        <div class="input-field col s12 right-align">
                            <button type="button" class="waves-effect waves-light btn btn-large green white-text"
                                    style="background: #375125 !important;" data-ng-click="setEmail()">
                                Submit
                            </button>
                        </div>
                        <div class="clearfix"></div>
                    </form>
                    <div style="margin-top: 100px;">
                        <img src="img/gogreen.png" />
                        <p style="text-align:center;">Register for e-bill &<br />
                            <span style="color:#426c24;font-size:33px;">SAVE PAPER</span></p>
                    </div>
                    <div style="text-align: center;margin-top: 70px;">
                        <img src="img/skip.png" style="cursor: pointer" data-ng-click="skipEmail()">
                        <!--<button type="button" class="waves-effect waves-light btn btn-large green white-text"
                                style="background: #375125 !important; margin-right: 40px;" data-ng-click="skipEmail()">
                            Skip
                        </button>-->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="giftCardScreen">
        <div style="background: url('img/giftCardsBottom.png') center bottom no-repeat;position: absolute;top:0;right:0;bottom:0;left:0;">
            <div class="giftCardsHeader">
                <h1>Enter Gift Card Code</h1>
            </div>
            <div style="margin:245px 10% 0 10%; text-align: left;font-size: 28px; max-height: 905px; overflow-y: auto;">
                <div style="margin: 0 0 20px 0;" data-ng-repeat="(key, value) in giftCardList track by key">
                    <div style="display: inline-block;width:40%;">{{value.productName}}</div>
                    <div style="display: inline-block;width:40%;">
                        <input type="text" maxlength="6" data-ng-if="!value.isValid" style="text-transform: uppercase; font-size: 28px; box-shadow: none;"
                               data-ng-model="value.cardNumber" data-ng-change="updateGiftCardCode(value)" data-ng-focus="userNameInputFocus()" />
                        <span data-ng-if="value.isValid" style="text-transform: uppercase">{{value.cardNumber}}</span>
                    </div>
                    <div style="display: inline-block;width:15%; text-align: center;">
                        <span data-ng-if="value.validating"><img src="img/loader.gif" style="width:35px;margin-bottom: -8px;"></span>
                        <span data-ng-if="value.isValid"><img src="img/checked.png" style="width:35px;margin-bottom: -8px;"></span>
                    </div>
                    <p class="giftCardError">{{value.error}}</p>
                </div>
            </div>
        </div>
    </div>

    <div id="OTPScreen">
        <div class="row formBox">
            <div class="col s12">
                <div class="input-field col s12">
                    <input tabindex="-1" id="redemptionotp" type="number" data-ng-model="userObj.otp" autocomplete="off"
                           length="{{otpLength}}" maxlength="{{otpLength}}" string-to-number data-ng-change="verifyOTP()" placeholder="{{otpPlaceHoler}}" data-ng-focus="userNumberInputFocus()" data-ng-blur="userNameInputBlur()" />
                    <label class="active otpLabel" for="otp" data-ng-if="otpHeadline!=null && !otpSwitched">{{otpHeadline}}</label>
                    <label class="active otpLabel" for="otp" data-ng-if="otpHeadline!=null && otpSwitched">Please enter your Contact Number Again</label>
                    <label class="active otpLabel" for="otp" data-ng-if="otpHeadline==null && !otpSwitched">One Time Password</label>
                    <label class="active otpLabel" for="otp" data-ng-if="otpHeadline==null && otpSwitched">Please enter your Contact Number Again</label>
                </div>
            </div>
            <div class="col s12 right-align">
                <button class="waves-effect waves-light btn-flat" data-ng-if="OtpSentCount<4"
                        data-ng-click="resendOTPStrategy('resendRedemptionOTP')">
                    Resend OTP
                </button>
            </div>
        </div>
    </div>

    <div id="thankYouScreen" class="{{region}}">
        <!--<p class="promotionHead" data-ng-show="showPromotionHead">Loyaltea</p>-->
    </div>

</div>
<div class="shadowBox" data-ng-click="hideShadow()"></div>
