<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-ng-init="init()"></div>
<div class="card cardContainer">
    <div class="card-image newUser">
      <img class="activator" src="img/newUser.jpg">
    </div>
</div>

<div id="userFlowView" tabindex="100">
        <div id="emailView" data-ng-show="showEmailView">
                <div class="row">
                        <div class="col s10 offset-s1" style="margin-top:5px;" data-ng-show="showEmailForm">
                                <p style="margin:20px 0 -10px 10px; text-align:left;font-size:19px;line-height:28px;color:white;">
                                        Tell us your Email to get a cup of
                                        <span style="font-weight:bold;">Desi Chai Free</span> - and yeah<br />
                                        <span style="font-weight:bold;">We Don't Spam</span>
                                </p>
                                <form>
                                        <div class="input-field col s10">
                                      <input tabindex="1" id="email" type="email" data-ng-model="userObj.email" autocomplete="off" placeholder="Email" />
                                    </div>
                                    <div class="input-field col s2">
                                        <button type="submit" class="waves-effect waves-light btn white black-text" style="height:45px;" data-ng-click="setEmail()">
                                                Submit
                                        </button>
                                    </div>
                                </form>
                        </div>
                        <div class="col s10 offset-s1" data-ng-hide="showEmailForm">
                                <p style="font-size:24px;color:#fff;">
                                        Please verify your email using verification link sent on your email!
                                </p>
                        </div>
                </div>
        </div>

        <div id="userContactScreen">
                <div class="row formBox" style="margin-top:220px;">  <!-- data-ng-change="lookUpContact()"   -->
                        <div class="col s10 offset-s1">
                                <div class="input-field col s12" data-ng-show="showEditContact && showContact">
                              <input tabindex="1" id="contact" type="number" data-ng-model="userObj.contact" autocomplete="off"
                               data-ng-change="lookUpContact()" length="10" maxlength="10" string-to-number restrict-input />
                              <span data-ng-if="userObj.contact!=null" data-ng-click="userObj.contact=null" class="clearEmail">&times</span>
                              <label class="active" for="contact">Contact</label>
                            </div>
                        </div>
                        <div class="col s10 offset-s1" data-ng-hide="showEditContact">
                                <div class="col s12">
                              <p style="font-weight:300;font-size:28px;">
                                Contact: <span style="font-size:44px;">{{userObj.contact}}</span>
                                <span class="contactVerified" data-ng-show="userObj.contactVerified==true">Verified</span>
                                <span class="editContactLink" data-ng-click="editContact()"
                                 data-ng-show="!showEditContact && userObj.contactVerified!=true">Edit</span>
                              </p>
                            </div>
                        </div>
                        <div class="col s10 offset-s1">
                                <div class="input-field col s12">
                              <input tabindex="2" id="user" type="text" data-ng-model="userObj.name" data-ng-disabled="userNameDisabled"
                               style="text-transform:capitalize;" data-ng-class="{'disabled':userNameDisabled}" autocomplete="off" />
                              <label class="active" for="user">Name</label>
                            </div>
                        </div>
                        <div class="col s10 offset-s1" data-ng-show="showOTP">
                                <div class="input-field col s12">
                              <input tabindex="3" id="otp" type="number" data-ng-model="userObj.otp" data-ng-disabled="OTPDisabled"
                               length="4" maxlength="4" string-to-number data-ng-change="verifyCustomer()" autocomplete="off" restrict-input />
                              <label class="active" for="otp">OTP</label>
                            </div>
                        </div>
                        <div class="col s10 offset-s1">
                                <button class="waves-effect waves-light btn col s12 chaiBtn" data-ng-click="updateUsername()"
                                 data-ng-show="showUpdateUsername">
                                Submit
                            </button>
                        </div>
                        <div class="col s10 offset-s1 right-align">
                                <button class="waves-effect waves-light btn-flat" data-ng-show="showOTP && OtpSentCount<4" data-ng-click="resendAuthorizationOTP()"
                                 data-ng-disabled="OTPDisabled" data-ng-class="{disabled:OTPDisabled}">
                                Resend OTP
                            </button>
                        </div>
                </div>
        </div>

        <div id="redemptionScreen">
                <div class="row center-align" style="margin-top:220px; font-size:28px;">
                        <div class="col s12">
                                <p class="welcomeText">Welcome <span>{{userObj.name}}</span>!</p>
                                <p data-ng-if="newUser">We have added a cup of Meri Wali Desi Chai to your order.</p>
                                <p data-ng-if="!newUser">Your Registered Number: {{userObj.contact}}</p>
                                <p data-ng-if="!newUser">Current Points: {{userObj.loyalityPoints | number}} <span data-ng-if="chaiCount>0">({{chaiCount}} Free Chai)</span></p>
                        </div>
                        <div class="col s12" data-ng-if="!newUser && chaiCount>0">
                                <button class="waves-effect waves-light btn-large redeemBtn" data-ng-repeat="chai in chaiArray"
                                 data-ng-click="redeemChai(chai)">{{chai}} Chai</button><br /><br />
                                <button class="waves-effect waves-light btn-large" data-ng-click="redeemChai(0)">Have it Later</button>
                        </div>
                        <div class="col s12" data-ng-if="!newUser && chaiCount==0">
                                <p style="font-size:31px;">
                                        You are <span style="font-size:51px;">{{(60-userObj.loyalityPoints)/10}}</span>
                                        <span data-ng-if="((60-userObj.loyalityPoints)/10)>1">visits</span>
                                        <span data-ng-if="((60-userObj.loyalityPoints)/10)==1">visit</span>
                                         away from a Free Chai
                                </p>
                        </div>
                </div>
        </div>

        <div id="OTPScreen">
                <div class="row formBox" style="margin-top:220px;">
                        <div class="col s12">
                                <div class="input-field col s12">
                              <input tabindex="-1" id="redemptionotp" type="number" data-ng-model="userObj.otp" autocomplete="off"
                               length="4" maxlength="4" string-to-number data-ng-change="verifyRedemption()" />
                              <label class="active" for="otp">OTP</label>
                            </div>
                        </div>
                        <div class="col s12 right-align">
                                <button class="waves-effect waves-light btn-flat" data-ng-if="OtpSentCount<4" data-ng-click="resendRedemptionOTP()">
                                Resend OTP
                            </button>
                        </div>
                </div>
        </div>

        <div id="thankYouScreen" class="{{uniDetail.region}}">
                <p class="promotionHead" data-ng-show="showPromotionHead">Loyaltea</p>
        </div>

</div>
<div class="shadowBox" data-ng-click="hideShadow()"></div>
