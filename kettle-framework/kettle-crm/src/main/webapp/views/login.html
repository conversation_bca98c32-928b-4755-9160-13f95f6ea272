<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="container" data-ng-init="init()">
    <div class="row">
        <div class="col s12 m10 offset-m1 l6 offset-l3">
            <div class="card z-depth-1">
                <!-- <div class="card-content green darken-3 white-text">
                    <div class="card-title">Login</div>
                </div> -->
                <div class="card-content" style="position:relative;overflow:hidden;">
                    <div data-ng-show="showLoginBox">
                        <div class="row" data-ng-show="autoConfig" style="padding-bottom: 20px;">
                            <div class="col-xs-12">
                                <p class="alert alert-info center-align">{{unitName}}
                                    (T-{{selectedTerminal.id}})</p>
                            </div>
                        </div>
                        <!--<div class="row" data-ng-hide="autoConfig">
                            <div class="col s12" style="margin-top:0; margin-bottom:0;">
                                <input class="with-gap col s6" type="radio" data-ng-model="unitCat"
                                       value="CAFE" name="category" id="cafe"/>
                                <label for="cafe" class="black-text col s6"
                                       data-ng-click="selectCat('CAFE')">Cafe</label>
                              &lt;!&ndash;   <input class="with-gap col s6" type="radio" data-ng-model="unitCat"
                                       value="TAKE_AWAY" name="category" id="takeAway"/>
                                <label for="takeAway" class="black-text col s6" data-ng-click="selectCat('TAKE_AWAY')">Take
                                    Away</label> &ndash;&gt;
                            </div>
                        </div>-->
                        <div class="row">
                            <div class="input-field col s12">
                                <input id="user" type="number" class="validate" data-ng-model="user" data-ng-blur="userNameInputBlur()" data-ng-focus="userNumberInputFocus()">
                                <label class="active" for="user">User Id</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s12">
                                <input id="passcode" type="password" class="validate" data-ng-model="passcode"  data-ng-blur="userNameInputBlur()" data-ng-focus="userNumberInputFocus()">
                                <label class="active" for="passcode">Passcode</label>
                            </div>
                        </div>
                        <div class="row" data-ng-hide="autoConfig">
                            <div class="input-field col s12">
                                <select data-ng-model="selectedOutlet" class="" name="unit" id="unit"
                                        data-ng-change="showTerminals()"
                                        data-ng-options="outlet as outlet.name for outlet in outlets track by outlet.id"></select>
                                <label class="active" class="active" for="unit">Unit</label>
                            </div>
                        </div>
                        <div class="row" data-ng-hide="autoConfig">
                            <div class="input-field col s12">
                                <select data-ng-model="selectedTerminal" class="" name="terminal" id="terminal"
                                        data-ng-options="terminal as terminal.name for terminal in terminals track by terminal.id"></select>
                                <label class="active" for="terminal">Terminal</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s12">
                                <button class="btn-flat waves-effect waves-light" type="button"
                                        data-ng-click="showLoginBox=false">Change Passcode
                                </button>
                                <button class="btn waves-effect waves-light green darken-2 right" type="button"
                                        data-ng-click="login()">Login
                                </button>
                            </div>
                        </div>
                        <div class="row" data-ng-show="autoConfig">
                            <div class="input-field col s12">
                                <button class="btn waves-effect waves-light green darken-2 right" type="button"
                                        data-ng-click="resetLogin()">Reset Login
                                </button>
                            </div>
                        </div>
                    </div>
                    <div data-ng-hide="showLoginBox">
                        <span id="changePassClose" data-ng-click="showLoginBox=true">&times;</span>

                        <div class="row">
                            <div class="input-field col s12">
                                <input id="fpuser" type="number" class="validate" data-ng-model="fpuser">
                                <label for="fpuser">User Id</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s12">
                                <input id="fpunit" type="number" data-ng-model="fpunit">
                                <label for="fpunit">Unit Id</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s12">
                                <input id="fppass" type="password" data-ng-model="fppass" data-ng-blur="userNameInputBlur()" data-ng-focus="userNameInputFocus()">
                                <label for="fppass">Old Passcode</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s12">
                                <input id="fpnpass" type="password" data-ng-model="fpnpass" data-ng-blur="userNameInputBlur()" data-ng-focus="userNameInputFocus()">
                                <label for="fpnpass">New Passcode</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="input-field col s12">
                                <button class="btn waves-effect waves-light green darken-2 right"
                                        type="button" data-ng-click="changePasscode()">Change Passcode
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>