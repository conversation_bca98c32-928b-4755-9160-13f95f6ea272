
ALTER TABLE KETTLE_REKOGNITION_DEV.<PERSON><PERSON><PERSON><PERSON>_IMAGE_MAPPING_DATA ADD COLUMN  THRESHOLD_VALUE decimal(10,2) DEFAULT NULL ;
ALTER TABLE KETTLE_REKOGNITION_DEV.LOOKUP_IMAGE_MAPPING_DATA ADD COLUMN  SIMILARITY decimal(10,2) DEFAULT NULL ;
ALTER TABLE KETTLE_REKOGNITION_DEV.LOOKUP_IMAGE_MAPPING_DATA ADD COLUMN  CONFIDENCE decimal(10,2) DEFAULT NULL ;


CREATE TABLE KETTLE_REKOGNITION_DEV.CUSTOMER_FACE_ID_MATCH_METADATA
(
    ID                 int(11) NOT NULL AUTO_INCREMENT,
    CUSTOMER_ID        int(11) DEFAULT NULL,
    CUSTOMER_NAME      varchar(150) DEFAULT NULL,
    CONTACT_NUMBER     varchar(15) DEFAULT NULL,
    FACE_ID            varchar(100) DEFAULT NULL,
    <PERSON><PERSON><PERSON><PERSON>_CUSTOMER_ID int(11) DEFAULT NULL,
    <PERSON><PERSON><PERSON><PERSON> KEY (ID)
);

CREATE TABLE KETTLE_REKOGNITION_DEV.CUSTOMER_HEURISTIC_DATA (
                                                                    `CUSTOMER_HEURISTIC_ID` INT(5) NOT NULL AUTO_INCREMENT,
                                                                    `CUSTOMER_ID` INT(11) NOT NULL,
                                                                    `SUBJECT_NAME` VARCHAR(100) NULL,
                                                                    `CONTACT_NUMBER` VARCHAR(15) NULL DEFAULT NULL,
                                                                    `FACE_ID` VARCHAR(100) NULL DEFAULT NULL,
                                                                    `AGE_LOW` INT(5) NULL DEFAULT NULL,
                                                                    `AGE_HIGH` INT(5) NULL DEFAULT NULL,
                                                                    `GENDER` VARCHAR(45) NULL DEFAULT NULL,
                                                                    `GENDER_CONFIDENCE` DECIMAL(10,2) NULL DEFAULT NULL,
                                                                    `BEARD` VARCHAR(1) NULL DEFAULT NULL,
                                                                    `MUSTACHE` VARCHAR(1) NULL DEFAULT NULL,
                                                                    `IMAGE_CONFIDENCE` DECIMAL(10,2) NULL DEFAULT NULL,
                                                                    `FACE_METADATA_ID` VARCHAR(100) NULL DEFAULT NULL,
                                                                    `CREATION_TIME` TIMESTAMP NULL DEFAULT NULL,
                                                                    `SESSION_ID` VARCHAR(50) NULL DEFAULT NULL,
                                                                    `THRESHOLD_VALUE` DECIMAL(10,2) NULL DEFAULT NULL,
                                                                    PRIMARY KEY (`CUSTOMER_HEURISTIC_ID`));

CREATE TABLE KETTLE_REKOGNITION_DEV.CUSTOMER_FACE_SUCCESS_METRIC_DATA (
					`FACE_SUCCESS_METRIC_ID` INT(11) NOT NULL AUTO_INCREMENT,
					`CUSTOMER_ID` INT(11) NOT NULL,
					`FACE_ID` VARCHAR(100) NULL DEFAULT NULL,
				    `CONTACT_NUMBER` VARCHAR(15) NULL DEFAULT NULL,
					`EULER_ANGLE_X` DECIMAL(10,2) NULL DEFAULT NULL,
					`EULER_ANGLE_Y` DECIMAL(10,2) NULL DEFAULT NULL,
					`EULER_ANGLE_Z` DECIMAL(10,2) NULL DEFAULT NULL,
					`RIGHT_EYE_OPEN_PROBABILITY` DECIMAL(10,2) NULL DEFAULT NULL,
					`LEFT_EYE_OPEN_PROBABILITY` DECIMAL(10,2) NULL DEFAULT NULL,
					`IMAGE_CONFIDENCE` DECIMAL(10,2) NULL DEFAULT NULL,
					`THRESHOLD_VALUE` DECIMAL(10,2) NULL DEFAULT NULL,
                    `CREATION_TIME` TIMESTAMP NULL DEFAULT NULL,
				PRIMARY KEY (`FACE_SUCCESS_METRIC_ID`));


ALTER TABLE kettle_rekognition_dev.customer_heuristic_data
    ADD COLUMN EMOTION VARCHAR(100) NULL DEFAULT NULL AFTER MUSTACHE,
ADD COLUMN EMOTION_CONFIDENCE DECIMAL(10,2) NULL DEFAULT NULL AFTER EMOTION;

CREATE INDEX CUSTOMER_ID_CUSTOMER_HEURISTIC_DATA ON  KETTLE_REKOGNITION_DEV.CUSTOMER_HEURISTIC_DATA(CUSTOMER_ID) USING BTREE;
CREATE INDEX FACE_ID_CUSTOMER_HEURISTIC_DATA ON  KETTLE_REKOGNITION_DEV.CUSTOMER_HEURISTIC_DATA(FACE_ID) USING BTREE;

CREATE INDEX CUSTOMER_ID_CUSTOMER_FACE_SUCCESS_METRIC_DATA ON  KETTLE_REKOGNITION_DEV.CUSTOMER_FACE_SUCCESS_METRIC_DATA(CUSTOMER_ID) USING BTREE;
CREATE INDEX FACE_ID_CUSTOMER_FACE_SUCCESS_METRIC_DATA ON  KETTLE_REKOGNITION_DEV.CUSTOMER_FACE_SUCCESS_METRIC_DATA(FACE_ID) USING BTREE;


DROP TABLE IF EXISTS KETTLE_REKOGNITION_DEV.UPDATE_IMAGE_MAPPING_DATA;
CREATE TABLE KETTLE_REKOGNITION_DEV.UPDATE_IMAGE_MAPPING_DATA (
  `IMAGE_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `SUBJECT_NAME` varchar(100) NOT NULL,
  `CONTACT_NUMBER` varchar(15) NOT NULL,
  `FACE_ID` varchar(100) DEFAULT NULL,
  `EULER_ANGLE_X` DECIMAL(10,2) NULL DEFAULT NULL,
  `EULER_ANGLE_Y` DECIMAL(10,2) NULL DEFAULT NULL,
  `EULER_ANGLE_Z` DECIMAL(10,2) NULL DEFAULT NULL,
  `RIGHT_EYE_OPEN_PROBABILITY` DECIMAL(10,2) NULL DEFAULT NULL,
  `LEFT_EYE_OPEN_PROBABILITY` DECIMAL(10,2) NULL DEFAULT NULL,
  `QUALITY` decimal(10,2) DEFAULT NULL,
  `BATCH_ID` varchar(100) DEFAULT NULL,
  `CREATION_TIME` timestamp NULL DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `SESSION_ID` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`IMAGE_MAPPING_ID`)
);


CREATE INDEX CONTACT_NUMBER_LOOKUP_IMAGE_MAPPING_DATA ON  KETTLE_REKOGNITION_STAGE.LOOKUP_IMAGE_MAPPING_DATA(CONTACT_NUMBER) USING BTREE;
CREATE INDEX FACE_ID_LOOKUP_IMAGE_MAPPING_DATA ON  KETTLE_REKOGNITION_STAGE.LOOKUP_IMAGE_MAPPING_DATA(FACE_ID) USING BTREE;

CREATE INDEX CONTACT_NUMBER_SIGNUP_IMAGE_MAPPING_DATA ON  KETTLE_REKOGNITION_STAGE.SIGNUP_IMAGE_MAPPING_DATA(CONTACT_NUMBER) USING BTREE;
CREATE INDEX FACE_ID_LOOKUP_SIGNUP_MAPPING_DATA ON  KETTLE_REKOGNITION_STAGE.SIGNUP_IMAGE_MAPPING_DATA(FACE_ID) USING BTREE;

CREATE INDEX CONTACT_NUMBER_UPDATE_IMAGE_MAPPING_DATA ON  KETTLE_REKOGNITION_STAGE.UPDATE_IMAGE_MAPPING_DATA(CONTACT_NUMBER) USING BTREE;
CREATE INDEX FACE_ID_LOOKUP_UPDATE_MAPPING_DATA ON  KETTLE_REKOGNITION_STAGE.UPDATE_IMAGE_MAPPING_DATA(FACE_ID) USING BTREE;