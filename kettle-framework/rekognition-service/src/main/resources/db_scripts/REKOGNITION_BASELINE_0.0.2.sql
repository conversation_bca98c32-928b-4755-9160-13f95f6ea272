ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN DEVICE_UNIQUE_ID VARCHAR(100) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN MANUFACTURER VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN DEVICE_MODEL VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN DEVICE_ID VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN SYSTEM_NAME VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN DEVICE_VERSION VARCHAR(20) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN BUNDLE_ID VARCHAR(150) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN BUILD_NUMBER VARCHAR(20) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN DEVICE_NAME VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN USER_AGENT VARCHAR(150) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN DEVICE_LOCALE VARCHAR(20) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN DEVICE_COUNTRY VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_MAPPING_DATA ADD COLUMN MAC_ADDRESS VARCHAR(100) NULL;



ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN DEVICE_UNIQUE_ID VARCHAR(100) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN MANUFACTURER VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN DEVICE_MODEL VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN DEVICE_ID VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN SYSTEM_NAME VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN DEVICE_VERSION VARCHAR(20) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN BUNDLE_ID VARCHAR(150) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN BUILD_NUMBER VARCHAR(20) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN DEVICE_NAME VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN USER_AGENT VARCHAR(150) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN DEVICE_LOCALE VARCHAR(20) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN DEVICE_COUNTRY VARCHAR(50) NULL;
ALTER TABLE KETTLE_REKOGNITION_DEV.APP_VERSION_ACTIVITY_LOG_DATA ADD COLUMN MAC_ADDRESS VARCHAR(100) NULL;
