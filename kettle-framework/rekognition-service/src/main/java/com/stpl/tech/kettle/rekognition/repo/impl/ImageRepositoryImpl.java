package com.stpl.tech.kettle.rekognition.repo.impl;

import com.stpl.tech.kettle.rekognition.entity.*;
import com.stpl.tech.kettle.rekognition.repo.ImageRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
public class ImageRepositoryImpl extends AbstractDaoImpl implements ImageRepository {

	private static final Logger LOG = LoggerFactory.getLogger(ImageRepositoryImpl.class);

	@Override
	public List<SignUpImageMappingData> findByFaceId(String faceId) {
		Query query = manager.createQuery("FROM  SignUpImageMappingData where faceId = :faceId");
		query.setParameter("faceId", faceId);
		try {
			return query.getResultList();
		} catch (NoResultException e) {
			return null;
		}
	}

	@Override
	public SignUpImageMappingData findBySessionId(String sessionId) {
		Query query = manager.createQuery("FROM  SignUpImageMappingData where sessionId = :sessionId");
		query.setParameter("sessionId", sessionId);
		try {
			List l = query.getResultList();
			if (l != null && !l.isEmpty()) {
				return (SignUpImageMappingData) l.get(0);
			}
		} catch (Exception e) {
			LOG.error("Error while searching sign up for session {}", sessionId);
		}
		return null;
	}

	@Override
	public List<SignUpImageMappingData> lookUpExistingMapping(String phoneNumber) {
		Query query = manager.createQuery(
				"FROM  SignUpImageMappingData where contactNumber = :contactNumber and faceId is not null ");
		query.setParameter("contactNumber", phoneNumber);
		return (List<SignUpImageMappingData>) query.getResultList();
	}

	@Override
	public List<SignUpImageMappingData> getUnIndexedMappings() {
		Query query = manager.createQuery("FROM  SignUpImageMappingData where faceId is null and mappedFaceId is null");
		try {
			return query.getResultList();
		} catch (NoResultException e) {
			return new ArrayList<>();
		}
	}

	@Override
	public boolean updateMappedFaceId(int imageMetadataId, String faceId) {
		Query query = manager.createQuery(
				"update SignUpImageMappingData set mappedFaceId =:faceId where imageMappingId =:imageMetadataId");
		try {
			query.setParameter("faceId", faceId);
			query.setParameter("imageMetadataId", imageMetadataId);

			query.executeUpdate();
			return true;
		} catch (NoResultException e) {
			return false;
		}
	}

	@Override
	public LookUpImageMappingData findMetaDataWithContactNumber(String customerNumber) {
		Query query = manager.createQuery("FROM LookUpImageMappingData md where md.contactNumber=:customerNumber " +
			"ORDER BY md.imageMappingId DESC ");
		query.setParameter("customerNumber", customerNumber);
		try {
			LookUpImageMappingData data = (LookUpImageMappingData) query.getResultList().get(0);
			if (data != null) {
				return data;
			} else {
				LOG.info("no data found with customer number {} ", customerNumber);
			}
		} catch (Exception e) {
				LOG.info(" error is {} ", e);
		}
		return null;
	}

	@Override
	public CustomerHeuristicData findCustomerData(Integer customerId){
		Query query = manager.createQuery("FROM CustomerHeuristicData md where md.customerId=:customerId");
		query.setParameter("customerId", customerId);
		try {
			CustomerHeuristicData data = (CustomerHeuristicData) query.getSingleResult();
			if (Objects.nonNull(data)) {
				return data;
			} else {
				LOG.info("no data found with customer id {} ", customerId);
			}
		} catch (Exception e) {
			LOG.info(" error is {} ", e);
		}
		return null;
	}
	@Override
	public CustomerFaceSuccessMetricData findFaceSuccessMetricData(Integer customerId){
		Query query = manager.createQuery("FROM CustomerFaceSuccessMetricData md where md.customerId=:customerId");
		query.setParameter("customerId", customerId);
		try {
			Object result = query.getSingleResult();
			if (Objects.nonNull(result)) {
				CustomerFaceSuccessMetricData data = (CustomerFaceSuccessMetricData) result;
				return data;
			} else {
				LOG.info("no data found with customer id {} ", customerId);
			}
		} catch (Exception e) {
			LOG.info(" error is {} ", e);
		}
		return null;
	}

	@Override
	public List<CustomerFaceSuccessMetricData> deleteFaceSuccessMatricData(String contactNumber)  {
			Query query = manager.createQuery("FROM CustomerFaceSuccessMetricData md where md.contactNumber=:contactNumber ");
			query.setParameter("contactNumber",contactNumber);
		try {
			List<CustomerFaceSuccessMetricData> data = query.getResultList() ;
			if (Objects.nonNull(data)) {
				return data;
			} else {
				LOG.info("no data found with customer id {} ", contactNumber);
			}
		} catch (Exception e) {
			LOG.info(" error is {} ", e);
		}

		return new ArrayList<>();

	}

	@Override
	public List<String> lookUpExistingFaceIdMapping(String contactNumber) {
		try {
			Query query = manager.createQuery(
					"SELECT distinct md.faceId FROM LookUpImageMappingData md where md.contactNumber=:contactNumber and faceId is not null");
			query.setParameter("contactNumber", contactNumber);
			List<String> data =  query.getResultList();
			if (Objects.nonNull(data)) {
				return data;
			} else {
				LOG.info("no data found with customer id {} ", contactNumber);
			}
		} catch (Exception e) {
			LOG.info(" error is {} ", e);
		}

		return new ArrayList<>();

	}

	@Override
	public List<UpdateImageMappingData> findUpdateImageMappingDateByFaceId(String faceId){
		try {
			Query query = manager.createQuery(
					"FROM UpdateImageMappingData md where md.faceId = :faceId"
			);
			query.setParameter("faceId",faceId);

			List<UpdateImageMappingData> data = query.getResultList();
			if (Objects.nonNull(data)) {
				return data;
			} else {
				LOG.info("no data found with customer id {} ", faceId);
			}
		} catch (Exception e) {
			LOG.info(" error is {} ", e);
		}

		return new ArrayList<>();

	}
}
