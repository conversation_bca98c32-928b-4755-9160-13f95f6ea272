package com.stpl.tech.kettle.rekognition.service;

import java.nio.charset.CharacterCodingException;
import java.util.List;
import java.util.Optional;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.rekognition.model.FaceMatch;
import com.amazonaws.services.rekognition.model.FaceRecord;
import com.stpl.tech.kettle.rekognition.modal.FaceIdMetaData;

public interface RekognitionService {

	/**
	 * Implementation for AWS rekognition to add faces in an s3 image.
	 * <p>
	 * s3 stores files in flat structure hence filename is just he name of the file
	 * in the s3 bucket
	 * 
	 * @param region
	 * @param bucket
	 * @param collection
	 * @param fileName
	 * @return
	 */
	public FaceRecord addFaces(Regions region, String bucket, String collection, String fileName);

	/**
	 * Face lookup call in rekognition collection this should lookup for the
	 * Prominent face and return the face match. this is used to get face id for the
	 * face having maximum similarity
	 * <p>
	 * should be used when we have image in s3
	 * 
	 * @param region
	 * @param bucket
	 * @param collection
	 * @param fileName
	 * @return
	 */
	public FaceIdMetaData searchFacesByImage(Regions region, String bucket, String collection, String fileName);

	/**
	 * Face lookup call in rekognition collection this should lookup for the
	 * Prominent face and return the face match. this is used to get face id for the
	 * face having maximum similarity
	 * <p>
	 * this is recommended search option and should be used when we have byte array
	 * for the image
	 * 
	 * <p>
	 * <b>NOTE:</b> Calling an Amazon Rekognition Image operation with image bytes
	 * is faster than uploading the image to an Amazon S3 bucket and then
	 * referencing the uploaded image in an Amazon Rekognition Image operation.
	 * Consider this approach if you are uploading images to Amazon Rekognition
	 * Image for near real-time processing. For example, images uploaded from an IP
	 * camera or images uploaded through a web portal.
	 * 
	 * @param region
	 * @param collection
	 * @param bytes
	 * @return
	 */
	public FaceIdMetaData searchFacesByImageByteArray(Regions region, String collection, byte[] bytes);

	/**
	 * Create collection in AWS rekognition service in provided region
	 * 
	 * @param region
	 * @param collectionId
	 */
	public void createCollection(Regions region, String collectionId);

	/**
	 * Delete collection in AWS rekognition service in provided region
	 * 
	 * @param region
	 * @param collectionId
	 */
	public void deleteCollection(Regions region, String collectionId);

	/**
	 * This will delete all the faces in face list for the given in the collection.
	 * <p>
	 * <b>NOTE:</b> Be cautious using this method as face Id is unique and different
	 * face Id is generated if we index the same image multiple times
	 * 
	 * @param region
	 * @param faces
	 * @param collectionId
	 */
	public void deleteFace(Regions region, List<String> faces, String collectionId);

	FaceRecord addFacesWithBytes(Regions region, String collection, String faceData) throws CharacterCodingException;

}
