package com.stpl.tech.kettle.rekognition.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import javax.imageio.ImageIO;
import javax.xml.bind.DatatypeConverter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.rekognition.service.ImageManagementService;
import com.stpl.tech.util.AppUtils;

public class ImageUtils {

	private static final Logger LOG = LoggerFactory.getLogger(ImageUtils.class);

	public static BufferedImage decodeBase64Image(String imageString) {
		BufferedImage image = null;
		byte[] imageByte;
		try {
			imageByte = decodeBase64TobyteArray(imageString);
			ByteArrayInputStream bis = new ByteArrayInputStream(imageByte);
			image = ImageIO.read(new ByteArrayInputStream(imageByte));
			bis.close();

		} catch (Exception e) {
			e.printStackTrace();
		}
		return image;
	}

	public static byte[] decodeBase64TobyteArray(String imageString) {
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		byte[] data = DatatypeConverter.parseBase64Binary(imageString);
		LOG.info("########## decoded Base64 ---------- {}", watch.stop().elapsed(TimeUnit.MILLISECONDS));
		return data;
	}

	public static String createImageName(String phoneNumber, String batchId, String metadataId, String imageExtention) {
		return phoneNumber + RekognitionConstants.HYPHEN + batchId + RekognitionConstants.HYPHEN + metadataId
				+ RekognitionConstants.HYPHEN + AppUtils.getDateString(new Date()) + RekognitionConstants.DOT
				+ imageExtention;
	}
}
