package com.stpl.tech.kettle.rekognition.entity.mongo;

import javax.persistence.Id;

import org.springframework.data.mongodb.core.mapping.Document;

@Document
public class FaceMetadata {

	@Id
	private String metadataId;

	private String faceDescription;

	private String environmentConditions;

	private String faceRecognitionData;

	public String getMetadataId() {
		return metadataId;
	}

	public void setMetadataId(String id) {
		this.metadataId = id;
	}

	public String getFaceDescription() {
		return faceDescription;
	}

	public void setFaceDescription(String faceDescription) {
		this.faceDescription = faceDescription;
	}

	public String getEnvironmentConditions() {
		return environmentConditions;
	}

	public void setEnvironmentConditions(String environmentConditions) {
		this.environmentConditions = environmentConditions;
	}

	public String getFaceRecognitionData() {
		return faceRecognitionData;
	}

	public void setFaceRecognitionData(String faceRecognitionData) {
		this.faceRecognitionData = faceRecognitionData;
	}
}
