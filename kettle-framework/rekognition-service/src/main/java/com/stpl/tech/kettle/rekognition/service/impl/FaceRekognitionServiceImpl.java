package com.stpl.tech.kettle.rekognition.service.impl;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.rekognition.model.FaceMatch;
import com.amazonaws.services.rekognition.model.FaceRecord;
import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.CustomerResponseTrimmed;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.entity.LookUpImageMappingData;
import com.stpl.tech.kettle.rekognition.entity.SignUpImageMappingData;
import com.stpl.tech.kettle.rekognition.modal.CustomerFaceIdRemoveData;
import com.stpl.tech.kettle.rekognition.modal.FaceIdMetaData;
import com.stpl.tech.kettle.rekognition.modal.FaceRequest;
import com.stpl.tech.kettle.rekognition.modal.LookUpMetaData;
import com.stpl.tech.kettle.rekognition.service.CustomerConnectionService;
import com.stpl.tech.kettle.rekognition.service.CustomerDemographicsService;
import com.stpl.tech.kettle.rekognition.service.FaceMetadataService;
import com.stpl.tech.kettle.rekognition.service.FaceRekognitionService;
import com.stpl.tech.kettle.rekognition.service.FileService;
import com.stpl.tech.kettle.rekognition.service.ImageManagementService;
import com.stpl.tech.kettle.rekognition.service.RekognitionService;
import com.stpl.tech.kettle.rekognition.util.DataConverter;
import com.stpl.tech.kettle.rekognition.util.ImageUtils;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.CharacterCodingException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Service
public class FaceRekognitionServiceImpl extends AbstractServiceImpl implements FaceRekognitionService {

	private static final Logger LOG = LoggerFactory.getLogger(FaceRekognitionServiceImpl.class);

	@Autowired
	private RekognitionProperties props;

	@Autowired
	private FileService fileService;

	@Autowired
	private RekognitionService rekognitionService;

	@Autowired
	private CustomerConnectionService customerService;

	@Autowired
	private ImageManagementService imageManager;

	@Autowired
	private FaceMetadataService faceMetadataService;

	@Autowired
	private CustomerDemographicsService customerDemographicsService;


	// executor for the thread processing in sign-up and lookup
	private ExecutorService executorService;

	@PostConstruct
	public void initateExecutorService() {
		if (executorService == null) {
			executorService = Executors.newFixedThreadPool(200);
		}
	}

	@PreDestroy
	public void shutDownExecutorService() {
		if (executorService != null) {
			try {
				executorService.shutdownNow();
				// wait 5 second for closing all threads
				executorService.awaitTermination(5, TimeUnit.SECONDS);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
		}
	}

	public CustomerResponseTrimmed lookupTrimmed(FaceRequest inputRequest , String apiVersion){
		CustomerResponse customerResponse = lookup(inputRequest,apiVersion);
		if(Objects.nonNull(customerResponse)){
			customerResponse.setUnitId(inputRequest.getUnitId());
		}
		return DataConverter.trimCustomerResponse(customerResponse);
	}

	@Override
	public CustomerResponse lookup(FaceRequest inputRequest,String apiVersion) {
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		CustomerResponse response = null;
		Optional<FaceMatch> face = Optional.empty();
		FaceIdMetaData faceIdMetaData=new FaceIdMetaData();
		try {
			Regions region = props.getRegion();
			// Search Face
			faceIdMetaData = searchImageBase64EncodedImage(region, inputRequest.getFaceImage());
			if ( faceIdMetaData.getFaceMatch() != null && faceIdMetaData.getFaceMatch().isPresent()) {
				// Success
				face=faceIdMetaData.getFaceMatch();
				response = customerService.getCustomerByFaceId(face.get().getFace().getFaceId(),apiVersion);

				if (response == null) {
					LOG.info("No customer found");
				}else {
					LOG.info(" customer contact number  is {}",response.getContact());
				}
				LOG.info("face id is {}", face.get().getFace().getFaceId());
				LOG.info(" we got similarity as {} ",face.get().getSimilarity());
				LOG.info(" we got Confidence as {} ",face.get().getFace().getConfidence());
			}
		} catch (Exception e) {
			LOG.error("Error in face lookup", e);
		}
		// this will spawn another thread to work
		saveLookUpMappings(inputRequest, face, response,faceIdMetaData.getThresholdValue());
		LOG.info("########## Total Time for face lookup  {}---------- {}", inputRequest.getBatchId(),
				watch.stop().elapsed(TimeUnit.MILLISECONDS));
		return response;
	}

	private void saveLookUpMappings(FaceRequest inputRequest, Optional<FaceMatch> face, CustomerResponse response,Float threshold) {
		executorService.execute(() -> {
			try {
				String faceMetadataId = UUID.randomUUID().toString();
				FileDetail s3FileDetail = null;
				if(props.isImageSaveEnabled()) {
					s3FileDetail = saveFileToS3(inputRequest.getFaceImage(), inputRequest.getBatchId(),
						response != null ? response.getContact() : "UNKNOWN", faceMetadataId);
				}
				imageManager.processLookUpImageMappingData(inputRequest, face, response, s3FileDetail, faceMetadataId, threshold);
				if(Objects.nonNull(response) &&Objects.nonNull(response.getName()) && Objects.nonNull(response.getContact())){
					imageManager.processLookUpCustomerHeuristicData(inputRequest, face, response.getId(),response.getName(), response.getContact(), s3FileDetail, faceMetadataId, threshold);
					if(Objects.nonNull(inputRequest.getFaceSuccessMetric())){
						imageManager.processFaceSuccessMetricData(inputRequest,face,response.getId(),response.getContact(),threshold);
					}
				}else if(face.isPresent() && Objects.nonNull(face.get().getFace()) && Objects.nonNull(face.get().getFace().getFaceId())){
					//customerDemographicsService.deleteCustomerDemographicsMetadata(face.get().getFace().getFaceId());
					customerDemographicsService.updateDemographicsMetadataStatus(face.get().getFace().getFaceId(), AppConstants.IN_ACTIVE,null);
				}

				faceMetadataService.saveFaceMetadata(faceMetadataId, inputRequest.getFaceDescription(),
						inputRequest.getEnvironmentConditions(), JSONSerializer.toJSON(face));
				LOG.info("Saved Data for look up request");
			} catch (Exception e) {
				LOG.error("Error while saving LookUp Mapping", e);
			}
		});
	}

	@Override
	public void signUp(FaceRequest faceRequest ,String apiVersion) {
		executorService.execute(() -> {
			try {
				processSignUp(faceRequest,apiVersion);
			} catch (Exception e) {
				LOG.error("Error while saving signup Mapping", e);
			}
		});
	}

	@Override
	public boolean optOutOfFaceIt(String contactNumber) {
		LOG.info("Opting out Customer with Contact Number " + contactNumber);
		CustomerFaceIdRemoveData faceIdData= customerService.optOutCustomer(contactNumber);
		if (faceIdData != null) {
			LOG.info("Opting out Customer with Contact Number " + contactNumber + " deleting the Face Id " + faceIdData.getFaceIds());
			saveDataBeforeDeletion(contactNumber,faceIdData);
			deleteFace(faceIdData.getFaceIds());
			return true;
		} else {
			LOG.info("Opting out Customer with Contact Number " + contactNumber + " with no registered face");
			return false;
		}
	}
	private void saveDataBeforeDeletion(String contactNumber, CustomerFaceIdRemoveData faceIdData) {
		LOG.info("saving customer info before deleting ");
		imageManager.addCustomerDataBeforeDeletion(faceIdData);
	}

	@Override
	public boolean optOutOfFaceIt(int customerId) {
		LOG.info("Opting out Customer with Customer Id " + customerId);
		String faceId = customerService.optOutCustomer(customerId);
		if (faceId != null) {
			LOG.info("Opting out Customer with Customer Id " + customerId + " deleting the Face Id " + faceId);
			deleteFace(faceId);

		} else {
			LOG.info("Opting out Customer with Customer Id " + customerId + " with no registered face");
		}
		return true;
	}

	private void processSignUp(FaceRequest faceRequest , String apiVersion) {
		FaceRecord face = null;
		try {
			Customer customer = customerService.getCustomerByContact(faceRequest.getContact(),apiVersion);
			if (customer != null && customer.isOptOutOfFaceIt()) {
				LOG.info("Skipping Face Metadata Storage For Customer With Contact Number " + faceRequest.getContact()
						+ " as they have Opted Out Of Face IT");
				try {
					customerService.notifyCustomerOfSkippedFR(faceRequest.getContact(),apiVersion);
				} catch (Exception e) {
					LOG.error("Error while sending Skipping Face Metadata Storage For Customer With Contact Number "
							+ faceRequest.getContact() + " as they have Opted Out Of Face IT");
				}
				return;
			}
			String faceMetadataId = UUID.randomUUID().toString();
			Regions region = props.getRegion();
			FileDetail s3FileDetail = null;
			if (props.isImageSaveEnabled()) {
				s3FileDetail = saveFileToS3(faceRequest.getFaceImage(), faceRequest.getBatchId(),
						faceRequest.getContact(), faceMetadataId);
			}
			List<SignUpImageMappingData> mappedDataList = imageManager.lookUpExistingMapping(faceRequest.getContact());
			for (SignUpImageMappingData e : mappedDataList) {
				deleteFace(e.getFaceId());
			}
			List<String> lookUpMappedFaceIdList = imageManager.lookUpExistingFaceIdMapping(faceRequest.getContact());
			for (String faceId : lookUpMappedFaceIdList) {
				deleteFace(faceId);
			}
			if (props.isImageSaveEnabled()) {
				face = createIndex(region, s3FileDetail.getName());
			} else {
				face = createIndexWithBytes(region, faceRequest.getFaceImage());
			}
			FaceIdMetaData faceIdMetaData=new FaceIdMetaData();
			Optional<FaceMatch> faceMatch = Optional.empty();
			Customer response = null;

			try {
				// Search Face
				faceIdMetaData = searchImageBase64EncodedImage(region,faceRequest.getFaceImage());
				faceMatch=faceIdMetaData.getFaceMatch();
			}catch (Exception ex){
				LOG.error("Error in searching face while signup", ex);
			}

			imageManager.processSignUpImageMappingData(faceRequest, Optional.of(face), s3FileDetail, faceMetadataId);
			faceMetadataService.saveFaceMetadata(faceMetadataId, faceRequest.getFaceDescription(),
					faceRequest.getEnvironmentConditions(), JSONSerializer.toJSON(face));

			customerService.mapCustomerToFace(face.getFace().getFaceId(), faceRequest.getContact(),apiVersion);
			response = customerService.getCustomerByContact(faceRequest.getContact(),apiVersion);
			if(Objects.nonNull(response) && Objects.nonNull(response.getFirstName()) && Objects.nonNull(response.getContactNumber())){
				imageManager.processSignupCustomerHeuristicData(faceRequest, faceMatch, response.getId(),response.getFirstName(), response.getContactNumber(), s3FileDetail, faceMetadataId, faceIdMetaData.getThresholdValue());
				if(Objects.nonNull(faceRequest.getFaceSuccessMetric())){
					imageManager.processFaceSuccessMetricData(faceRequest,faceMatch,response.getId(),response.getContactNumber(),faceIdMetaData.getThresholdValue());
				}
			}
			//imageManager.updateSignupCustomerHeuristicData(faceRequest, Optional.of(face), s3FileDetail, faceMetadataId,response);
		} catch (Exception ex) {
			LOG.error("Exception Occurred in face/detection ", ex);
			deleteFaceRecord(face);
		}
	}

	private void deleteFaceRecord(FaceRecord faceRecord) {
		if (faceRecord != null) {
			deleteFace(faceRecord.getFace().getFaceId());
		}
	}

	private void deleteFace(String faceId) {
		rekognitionService.deleteFace(Regions.AP_SOUTH_1, Arrays.asList(faceId), props.getRekognitionCollectionId());
	}

	/**
	 * generates AWS face index (face Id) for the image
	 * <p>
	 * requires image to be uploaded in s3 bucket
	 *
	 * @param region
	 * @param fileName
	 * @return
	 */
	private FaceRecord createIndex(Regions region, String fileName) {
		return rekognitionService.addFaces(region, props.getS3BucketName(), props.getRekognitionCollectionId(),
				fileName);
	}

	private FaceRecord createIndexWithBytes(Regions region, String faceImage) throws CharacterCodingException {
		return rekognitionService.addFacesWithBytes(region, props.getRekognitionCollectionId(), faceImage);
	}

	/**
	 * Used when we have image already uploaded in s3 bucket
	 *
	 * @param region
	 * @param fileName
	 * @return
	 */
	private FaceIdMetaData searchImage(Regions region, String fileName) {
		return rekognitionService.searchFacesByImage(region, props.getS3BucketName(),
				props.getRekognitionCollectionId(), fileName);
	}

	/**
	 * this will search face in AWS rekognition
	 * <p>
	 * will convert data to byte array first as image is encrypted in base64 string
	 *
	 * @param region
	 * @param base64String
	 * @return
	 */
	private FaceIdMetaData searchImageBase64EncodedImage(Regions region, String base64String) {
		return rekognitionService.searchFacesByImageByteArray(region, props.getRekognitionCollectionId(),
				ImageUtils.decodeBase64TobyteArray(base64String));
	}

	/**
	 * Abstract class method implementation
	 *
	 */
	@Override
	protected FileService getFileService() {
		return fileService;
	}

	@Override
	protected RekognitionProperties getEnviournmentProperties() {
		return props;
	}

	@Override
	public LookUpMetaData findMetaDataWithContactNumber(String contactNumber) {
		return imageManager.findMetaDataWithContactNumber(contactNumber);
	}

	@Override
	public void update(FaceRequest inputRequest , String apiVersion) {
		Customer customer = customerService.getCustomerByContact(inputRequest.getContact(),apiVersion);
		try{
		if(imageManager.checkUpdateFaceSuccessMetricData(inputRequest,customer.getId(),customer.getFaceId())){
			FaceRecord face = null;
				if (customer != null && customer.isOptOutOfFaceIt()) {
					LOG.info("Skipping Face Metadata Storage For Customer With Contact Number " + inputRequest.getContact()
							+ " as they have Opted Out Of Face IT");
					try {
						customerService.notifyCustomerOfSkippedFR(inputRequest.getContact(),apiVersion);
					} catch (Exception e) {
						LOG.error("Error while sending Skipping Face Metadata Storage For Customer With Contact Number "
								+ inputRequest.getContact() + " as they have Opted Out Of Face IT");
					}
					return;
				}
				String faceMetadataId = UUID.randomUUID().toString();
				Regions region = props.getRegion();
				FileDetail s3FileDetail = null;
				if (props.isImageSaveEnabled()) {
					s3FileDetail = saveFileToS3(inputRequest.getFaceImage(), inputRequest.getBatchId(),
							inputRequest.getContact(), faceMetadataId);
				}
				List<SignUpImageMappingData> mappedDataList = imageManager.lookUpExistingMapping(inputRequest.getContact());
				for (SignUpImageMappingData e : mappedDataList) {
					deleteFace(e.getFaceId());
				}
				deleteFace(customer.getFaceId());
				List<String> lookUpMappedFaceIdList = imageManager.lookUpExistingFaceIdMapping(inputRequest.getContact());
				for (String faceId : lookUpMappedFaceIdList) {
					deleteFace(faceId);
				}
				if (props.isImageSaveEnabled()) {
					face = createIndex(region, s3FileDetail.getName());
				} else {
					face = createIndexWithBytes(region, inputRequest.getFaceImage());
				}
				FaceIdMetaData faceIdMetaData = new FaceIdMetaData();
				Optional<FaceMatch> faceMatch = Optional.empty();

				try {
					// Search Face
					faceIdMetaData = searchImageBase64EncodedImage(region, inputRequest.getFaceImage());
					faceMatch = faceIdMetaData.getFaceMatch();
				} catch (Exception ex) {
					LOG.error("Error in searching face while signup", ex);
				}
				imageManager.processUpdateImageMappingData(inputRequest, face.getFace().getFaceId());
				faceMetadataService.saveFaceMetadata(faceMetadataId, inputRequest.getFaceDescription(),
						inputRequest.getEnvironmentConditions(), JSONSerializer.toJSON(face));
				resolveConflict(inputRequest, Optional.of(face), customer,faceIdMetaData.getThresholdValue(),apiVersion);
		}else{
				imageManager.processUpdateImageMappingData(inputRequest,customer.getFaceId());
		}
	} catch (Exception exception) {
			LOG.error("Exception Occurred in face/detection ", exception);
	}

	}

	public void resolveConflict(FaceRequest inputRequest, Optional<FaceRecord> face, Customer response,Float threshold,
								String apiVersion){
		try {
			String faceMetadataId = UUID.randomUUID().toString();
			FileDetail s3FileDetail = null;
			if(props.isImageSaveEnabled()) {
				s3FileDetail = saveFileToS3(inputRequest.getFaceImage(), inputRequest.getBatchId(),
						response != null ? response.getContactNumber() : "UNKNOWN", faceMetadataId);
			}
			if(Objects.nonNull(response) &&Objects.nonNull(response.getFirstName()) && Objects.nonNull(response.getContactNumber())) {
				imageManager.updateFaceSuccessMetricData(inputRequest, face, response.getId(), response.getFirstName(),response.getContactNumber(),
						s3FileDetail,faceMetadataId,threshold,apiVersion);
			}
		}catch (Exception e){
			LOG.error("Error in resolving face conflict", e);
		}

	}

	@Override
	public void deleteFaceSuccessMetric(String contactNumber) throws DataUpdationException {
	imageManager.deleteSuccessMetricData(contactNumber);
	}
}
