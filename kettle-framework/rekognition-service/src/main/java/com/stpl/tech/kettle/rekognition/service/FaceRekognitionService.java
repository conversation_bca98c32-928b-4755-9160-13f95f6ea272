package com.stpl.tech.kettle.rekognition.service;

import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.CustomerResponseTrimmed;
import com.stpl.tech.kettle.rekognition.modal.FaceRequest;
import com.stpl.tech.kettle.rekognition.modal.LookUpMetaData;
import com.stpl.tech.master.core.exception.DataUpdationException;

public interface FaceRekognitionService {

	public CustomerResponse lookup(FaceRequest inputRequest,String apiVersion);

	public CustomerResponseTrimmed lookupTrimmed(FaceRequest inputRequest , String apiVersion);

	public void signUp(FaceRequest faceRequest,String apiVersion);

	boolean optOutOfFaceIt(String contactNumber);

	boolean optOutOfFaceIt(int customerId);

	public LookUpMetaData findMetaDataWithContactNumber(String contactNumber);

	public void update(FaceRequest faceRequest, String apiVersion);

	public void deleteFaceSuccessMetric(String contactNumber) throws DataUpdationException;
}
