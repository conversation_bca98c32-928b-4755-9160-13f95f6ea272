package com.stpl.tech.kettle.rekognition.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.rekognition.entity.mongo.FaceMetadata;
import com.stpl.tech.kettle.rekognition.repo.mongo.FaceMetadataRepository;
import com.stpl.tech.kettle.rekognition.service.FaceMetadataService;

@Service
public class FaceMetadataServiceImpl implements FaceMetadataService {

	private static final Logger LOG = LoggerFactory.getLogger(FaceMetadataServiceImpl.class);

	@Autowired
	private FaceMetadataRepository faceMetadataRepository;

	@Override
	public boolean save(FaceMetadata faceMetadata) {
		try {
			faceMetadataRepository.save(faceMetadata);
		} catch (Exception e) {
			LOG.info("Exception occurred while saving face metadata", e);
			return false;
		}
		return true;
	}

	@Override
	public boolean saveFaceMetadata(String id, String faceDescription, String environmentConditions,
			String faceRecognitionData) {
		FaceMetadata faceMetadata = new FaceMetadata();
		faceMetadata.setMetadataId(id);
		faceMetadata.setFaceDescription(faceDescription);
		faceMetadata.setEnvironmentConditions(environmentConditions);
		faceMetadata.setFaceRecognitionData(faceRecognitionData);
		return save(faceMetadata);
	}

}
