package com.stpl.tech.kettle.rekognition.service.impl;

import com.amazonaws.services.rekognition.model.Face;
import com.amazonaws.services.rekognition.model.FaceDetail;
import com.amazonaws.services.rekognition.model.FaceMatch;
import com.amazonaws.services.rekognition.model.FaceRecord;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.rekognition.entity.*;
import com.stpl.tech.kettle.rekognition.entity.mongo.DemographicsMetadata;
import com.stpl.tech.kettle.rekognition.entity.mongo.FaceMetadata;
import com.stpl.tech.kettle.rekognition.modal.*;
import com.stpl.tech.kettle.rekognition.repo.ImageRepository;
import com.stpl.tech.kettle.rekognition.repo.mongo.CustomerDemographicsRepository;
import com.stpl.tech.kettle.rekognition.repo.mongo.FaceMetadataRepository;
import com.stpl.tech.kettle.rekognition.service.CustomerConnectionService;
import com.stpl.tech.kettle.rekognition.service.ImageManagementService;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class ImageMangementServiceImpl implements ImageManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(ImageManagementService.class);

	@Autowired
	private ImageRepository imageRepository;

	@Autowired
	private FaceMetadataRepository faceMetadataRepository;

	@Autowired
	private CustomerDemographicsRepository customerDemographicsRepository;

	@Autowired
	private CustomerConnectionService customerService;



	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void processLookUpImageMappingData(FaceRequest inputRequest, Optional<FaceMatch> face,
			CustomerResponse response, FileDetail s3FileDetail, String faceMetadataId, Float threshold) throws IOException {
		try {
			LookUpImageMappingData entity = new LookUpImageMappingData();
			entity.setSubjectName(response != null ? response.getName() : null);
			entity.setContactNumber(response != null ? response.getContact() : null);
			if(face != null && face.isPresent() ){
				entity.setFaceId(face.get().getFace().getFaceId());
				entity.setConfidence(BigDecimal.valueOf(face.get().getFace().getConfidence()));
				entity.setSimilarity(BigDecimal.valueOf(face.get().getSimilarity()));
			}else {
				entity.setFaceId(null);
				entity.setConfidence(null);
				entity.setSimilarity(null);
			}
			if(s3FileDetail != null) {
				entity.setFileName(s3FileDetail.getName());
				entity.setFilePath(s3FileDetail.getUrl());
			}
			entity.setFaceMetadataId(faceMetadataId);
			entity.setQuality(inputRequest.getQuality());
			entity.setBatchId(inputRequest.getBatchId());
			entity.setCreationTime(AppUtils.getCurrentTimestamp());
			entity.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			entity.setSessionId(inputRequest.getSessionId());
			entity.setThreshold(threshold!=null?BigDecimal.valueOf(threshold):BigDecimal.ZERO);
			imageRepository.add(entity);
		} catch (Exception ex) {
			LOG.error("Error", ex);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void processLookUpCustomerHeuristicData(FaceRequest inputRequest, Optional<FaceMatch> face,Integer customerId,String customerName,String customerContact, FileDetail s3FileDetail, String faceMetadataId, Float threshold) throws IOException {
		try {
			if (face.isPresent()) {
				DemographicsMetadata demographicsMetadata = customerDemographicsRepository.findByFaceId(face.get().getFace().getFaceId());
				CustomerHeuristicData data = imageRepository.findCustomerData(customerId);

				if (Objects.nonNull(demographicsMetadata) && Objects.nonNull(demographicsMetadata.getFaceDetail()) && Objects.nonNull(face.get().getFace())) {
					if (Objects.isNull(data)) {
						saveNewCustomerHeuristicData(inputRequest, face.get().getFace(), customerId, customerName, customerContact, faceMetadataId, demographicsMetadata.getFaceDetail(),threshold);
					} else {
						updateCustomerHeuristicData(demographicsMetadata, data, inputRequest, faceMetadataId,threshold);
					}
				}
			}
		} catch (Exception ex) {
			LOG.error("Error Processing Customer Lookup Heuristic Data", ex);
		}
	}

	public void saveNewCustomerHeuristicData(FaceRequest inputRequest, Face face, int customerId, String customerName, String customerContact, String faceMetadataId, FaceDetail faceDetail,float threshold) {
		CustomerHeuristicData customerHeuristicData = new CustomerHeuristicData();
		customerHeuristicData.setCustomerId(customerId);
		customerHeuristicData.setSubjectName(customerName);
		customerHeuristicData.setContactNumber(customerContact);

		customerHeuristicData.setFaceId(Objects.nonNull(face) ? face.getFaceId():null);

		customerHeuristicData.setFaceMetadataId(faceMetadataId);
		customerHeuristicData.setCreationTime(AppUtils.getCurrentTimestamp());
		customerHeuristicData.setSessionId(inputRequest.getSessionId());
		customerHeuristicData.setThreshold(Objects.nonNull(threshold) ? BigDecimal.valueOf(threshold) : BigDecimal.ZERO);

//		BigDecimal emotionThreshold = new BigDecimal(AppConstants.EMOTION_CONFIDENCE_THRESHOLD);
//
//		if(Objects.nonNull(faceDetail.getEmotions()) && faceDetail.getEmotions().size() >0 && Objects.nonNull(faceDetail.getEmotions().get(0).getConfidence()) && emotionThreshold.compareTo(BigDecimal.valueOf(faceDetail.getEmotions().get(0).getConfidence())) <=0 ){
//			customerHeuristicData.setEmotion(faceDetail.getEmotions().get(0).getType());
//			customerHeuristicData.setEmotionConfidence(BigDecimal.valueOf(faceDetail.getEmotions().get(0).getConfidence()));
//		}else{
//			customerHeuristicData.setEmotion(null);
//			customerHeuristicData.setEmotionConfidence(null);
//		}

		if(Objects.nonNull(faceDetail.getEmotions()) && faceDetail.getEmotions().size() >0 && Objects.nonNull(faceDetail.getEmotions().get(0).getConfidence()) ){
			customerHeuristicData.setEmotion(faceDetail.getEmotions().get(0).getType());
			customerHeuristicData.setEmotionConfidence(BigDecimal.valueOf(faceDetail.getEmotions().get(0).getConfidence()));
		}

		//Setting Demographics Value For Customer
		customerHeuristicData.setAgeLow(Objects.nonNull(faceDetail.getAgeRange().getLow())?faceDetail.getAgeRange().getLow():null);
		customerHeuristicData.setAgeHigh(Objects.nonNull(faceDetail.getAgeRange().getHigh())? faceDetail.getAgeRange().getHigh():null);
		customerHeuristicData.setImageConfidence(Objects.nonNull(faceDetail.getConfidence()) ? BigDecimal.valueOf(faceDetail.getConfidence()) : BigDecimal.ZERO);
		customerHeuristicData.setMustache(Objects.nonNull(faceDetail.getMustache().getValue())?(faceDetail.getMustache().getValue() ? AppConstants.YES : AppConstants.NO):null);
		customerHeuristicData.setBeard(Objects.nonNull(faceDetail.getBeard().getValue())?(faceDetail.getBeard().getValue() ? AppConstants.YES : AppConstants.NO):null);
		customerHeuristicData.setGender(Objects.nonNull(faceDetail.getGender().getValue())?faceDetail.getGender().getValue():null);
		customerHeuristicData.setGenderConfidence(Objects.nonNull(faceDetail.getGender().getConfidence()) ? BigDecimal.valueOf(faceDetail.getGender().getConfidence()) : BigDecimal.ZERO);
		imageRepository.add(customerHeuristicData);
	}

	public void updateCustomerHeuristicData(DemographicsMetadata demographicsMetadata, CustomerHeuristicData customerHeuristicData, FaceRequest inputRequest, String faceMetadataId,float threshold) {

		BigDecimal thresh = Objects.nonNull(threshold) ? BigDecimal.valueOf(threshold).setScale(2, RoundingMode.DOWN) : BigDecimal.ZERO;
		BigDecimal imageConfidence = Objects.nonNull( demographicsMetadata.getFaceDetail().getConfidence()) ? BigDecimal.valueOf(demographicsMetadata.getFaceDetail().getConfidence()).setScale(2, RoundingMode.DOWN) : BigDecimal.ZERO;
		BigDecimal genderConfidence = Objects.nonNull(demographicsMetadata.getFaceDetail().getGender().getConfidence()) ? BigDecimal.valueOf(demographicsMetadata.getFaceDetail().getGender().getConfidence()).setScale(2, RoundingMode.DOWN) : BigDecimal.ZERO;


//		if( Objects.nonNull(demographicsMetadata.getFaceId()) && !customerHeuristicData.getFaceId().equals(demographicsMetadata.getFaceId())){
//			customerHeuristicData.setFaceId(demographicsMetadata.getFaceId());
//			updateData(demographicsMetadata, customerHeuristicData, inputRequest, faceMetadataId,thresh);
//		}
//		else if (thresh.compareTo(customerHeuristicData.getThreshold()) > 0 || imageConfidence.compareTo(customerHeuristicData.getImageConfidence()) > 0  ||
//						genderConfidence.compareTo(customerHeuristicData.getGenderConfidence()) > 0) {
//			updateData(demographicsMetadata, customerHeuristicData, inputRequest, faceMetadataId,thresh);
//		}
		if (thresh.compareTo(customerHeuristicData.getThreshold()) > 0 || imageConfidence.compareTo(customerHeuristicData.getImageConfidence()) > 0  ||
						genderConfidence.compareTo(customerHeuristicData.getGenderConfidence()) > 0) {
			updateData(demographicsMetadata, customerHeuristicData, inputRequest, faceMetadataId,thresh);
		}
	}

	public void updateData(DemographicsMetadata demographicsMetadata, CustomerHeuristicData customerHeuristicData, FaceRequest inputRequest, String faceMetadataId,BigDecimal thresh){
		customerHeuristicData.setFaceMetadataId(faceMetadataId);
		customerHeuristicData.setCreationTime(AppUtils.getCurrentTimestamp());
		customerHeuristicData.setSessionId(inputRequest.getSessionId());
		customerHeuristicData.setThreshold(thresh);

//		BigDecimal emotionThreshold = new BigDecimal(AppConstants.EMOTION_CONFIDENCE_THRESHOLD);
//
//		if(Objects.nonNull(demographicsMetadata.getFaceDetail().getEmotions()) && demographicsMetadata.getFaceDetail().getEmotions().size() >0 && Objects.nonNull(demographicsMetadata.getFaceDetail().getEmotions().get(0).getConfidence()) && emotionThreshold.compareTo(BigDecimal.valueOf(demographicsMetadata.getFaceDetail().getEmotions().get(0).getConfidence())) <=0 ){
//			customerHeuristicData.setEmotion(demographicsMetadata.getFaceDetail().getEmotions().get(0).getType());
//			customerHeuristicData.setEmotionConfidence(BigDecimal.valueOf(demographicsMetadata.getFaceDetail().getEmotions().get(0).getConfidence()));
//		}else{
//			customerHeuristicData.setEmotion(null);
//			customerHeuristicData.setEmotionConfidence(null);
//		}
		if(Objects.nonNull(demographicsMetadata.getFaceDetail().getEmotions()) && demographicsMetadata.getFaceDetail().getEmotions().size() >0 && Objects.nonNull(demographicsMetadata.getFaceDetail().getEmotions().get(0).getConfidence())){
			customerHeuristicData.setEmotion(demographicsMetadata.getFaceDetail().getEmotions().get(0).getType());
			customerHeuristicData.setEmotionConfidence(BigDecimal.valueOf(demographicsMetadata.getFaceDetail().getEmotions().get(0).getConfidence()));
		}

		//Setting Demographics Value For Customer
		customerHeuristicData.setAgeLow(demographicsMetadata.getFaceDetail().getAgeRange().getLow());
		customerHeuristicData.setAgeHigh(demographicsMetadata.getFaceDetail().getAgeRange().getHigh());
		customerHeuristicData.setImageConfidence(Objects.nonNull(demographicsMetadata.getFaceDetail().getConfidence()) ? BigDecimal.valueOf(demographicsMetadata.getFaceDetail().getConfidence()) : BigDecimal.ZERO);
		customerHeuristicData.setMustache(demographicsMetadata.getFaceDetail().getMustache().getValue() ? AppConstants.YES : AppConstants.NO);
		customerHeuristicData.setBeard(demographicsMetadata.getFaceDetail().getBeard().getValue() ? AppConstants.YES : AppConstants.NO);
		customerHeuristicData.setGender(demographicsMetadata.getFaceDetail().getGender().getValue());
		customerHeuristicData.setGenderConfidence(Objects.nonNull(demographicsMetadata.getFaceDetail().getGender().getConfidence()) ? BigDecimal.valueOf(demographicsMetadata.getFaceDetail().getGender().getConfidence()) : BigDecimal.ZERO);
		imageRepository.update(customerHeuristicData);
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void processSignupCustomerHeuristicData(FaceRequest inputRequest, Optional<FaceMatch> face, Integer customerId,String customerName,String customerContact, FileDetail s3FileDetail, String faceMetadataId, Float threshold){
		try {
			if (face.isPresent()) {
				DemographicsMetadata demographicsMetadata = customerDemographicsRepository.findByFaceId(face.get().getFace().getFaceId());
				//CustomerHeuristicData data = imageRepository.findCustomerData(customerId);

				if (Objects.nonNull(demographicsMetadata) && Objects.nonNull(demographicsMetadata.getFaceDetail()) && Objects.nonNull(face.get().getFace())) {
					saveNewCustomerHeuristicData(inputRequest, face.get().getFace(), customerId, customerName, customerContact, faceMetadataId, demographicsMetadata.getFaceDetail(),threshold);
//					if (Objects.isNull(data)) {
//						saveNewCustomerHeuristicData(inputRequest, face.get().getFace(), customerId, customerName, customerContact, faceMetadataId, demographicsMetadata.getFaceDetail(),threshold);
//					}
				}
			}
		} catch (Exception ex) {
			LOG.error("Error Processing Customer Sign Up Heuristic Data", ex);
		}
	}

//	public void updateDemographicsData(FaceRequest faceRequest,Optional<FaceRecord> faceRecord){
//		DemographicsMetadata demographicsMetadata = customerDemographicsRepository.findByFaceId(faceRecord.get().getFace().getFaceId());
//		if(Objects.nonNull(demographicsMetadata)){
//			customerDemographicsRepository.deleteById(faceRecord.get().getFace().getFaceId());
//		}
//		DemographicsMetadata entity = new DemographicsMetadata();
//		try {
//			entity.setFaceId(faceRecord.get().getFace().getFaceId());
//			entity.setFaceDetail(faceRecord.get().getFaceDetail());
//			customerDemographicsRepository.save(entity);
//		} catch (Exception e) {
//			LOG.info("Exception occurred while saving demographics metadata", e);
//		}
//	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void processSignUpImageMappingData(FaceRequest faceRequest, Optional<FaceRecord> face,
			FileDetail s3FileDetail, String faceMetadataId) throws IOException {
		try {
			SignUpImageMappingData entity = new SignUpImageMappingData();
			entity.setSubjectName(faceRequest.getCustomerName());
			entity.setContactNumber(faceRequest.getContact());
			entity.setFaceId(face.get().getFace().getFaceId());
			if(s3FileDetail != null) {
				entity.setFileName(s3FileDetail.getName());
				entity.setFilePath(s3FileDetail.getUrl());
			}
			entity.setFaceMetadataId(faceMetadataId);
			entity.setQuality(faceRequest.getQuality());
			entity.setBatchId(faceRequest.getBatchId());
			entity.setCreationTime(AppUtils.getCurrentTimestamp());
			entity.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			entity.setSessionId(faceRequest.getSessionId());
			imageRepository.add(entity);
		} catch (Exception ex) {
			LOG.error("Error", ex);
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<SignUpImageMappingData> lookUpExistingMapping(String phoneNumber) {
		return imageRepository.lookUpExistingMapping(phoneNumber);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public SignUpImageMappingData getSignUpDataBySessionId(String sessionId) {
		return imageRepository.findBySessionId(sessionId);
	}

	@Override
	public void addOriginalImageMapping(ImageSyncRequest syncRequest, FileDetail s3FileDetail, String faceMetadataId) {
		try {
			OriginalImageMappingData entity = new OriginalImageMappingData();
			entity.setFileName(s3FileDetail.getName());
			entity.setFilePath(s3FileDetail.getUrl());
			entity.setCreationTime(AppUtils.getCurrentTimestamp());
			entity.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			entity.setSessionId(syncRequest.getSessionId());
			entity.setFaceMetadataId(faceMetadataId);
			imageRepository.add(entity);
		} catch (Exception ex) {
			LOG.error("Error", ex);
		}

	}

	@Override
	public LookUpMetaData findMetaDataWithContactNumber(String contactNumber) {
		LookUpImageMappingData mappingData = imageRepository.findMetaDataWithContactNumber(contactNumber);
		if (mappingData != null) {
			FaceMetadata lookUpFaceMetadata = faceMetadataRepository.findByMetadataId(mappingData.getFaceMetadataId());
			List<SignUpImageMappingData> signUpImageMappingData = imageRepository.lookUpExistingMapping(contactNumber);
			SignUpImageMappingData singleResultOfSignUpData = signUpImageMappingData.get(signUpImageMappingData.size() - 1);
			FaceMetadata signUpFaceMetadata = faceMetadataRepository.findByMetadataId(singleResultOfSignUpData.getFaceMetadataId());
			LookUpMetaData lookUpMetaData=new LookUpMetaData();
			lookUpMetaData.setContactNumber(contactNumber);
			lookUpMetaData.setLookUpImageMappingData(mappingData);
			lookUpMetaData.setLookUpFaceMetadata(lookUpFaceMetadata);
			lookUpMetaData.setSignUpImageMappingData(singleResultOfSignUpData);
			lookUpMetaData.setSignUpFaceMetaData(signUpFaceMetadata);
			return lookUpMetaData;
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void addCustomerDataBeforeDeletion(CustomerFaceIdRemoveData faceIdData) {
		try {
			if (!faceIdData.getCustomerIds().isEmpty() && faceIdData.getCustomerIds().size() > 0) {
				for (Integer customerId : faceIdData.getCustomerIds()) {
					CustomerFaceIdMatchData customerFaceIdMatchData = new CustomerFaceIdMatchData();
					customerFaceIdMatchData.setCustomerId(faceIdData.getCustomerId());
					customerFaceIdMatchData.setCustomerName(faceIdData.getCustomerName());
					customerFaceIdMatchData.setContactNumber(faceIdData.getContactNumber());
					customerFaceIdMatchData.setFaceId(faceIdData.getFaceIds());
					customerFaceIdMatchData.setLinkedCustomerId(customerId);
					imageRepository.add(customerFaceIdMatchData);
				}
			} else {
				CustomerFaceIdMatchData customerFaceIdMatchData = new CustomerFaceIdMatchData();
				customerFaceIdMatchData.setCustomerId(faceIdData.getCustomerId());
				customerFaceIdMatchData.setCustomerName(faceIdData.getCustomerName());
				customerFaceIdMatchData.setContactNumber(faceIdData.getContactNumber());
				customerFaceIdMatchData.setFaceId(faceIdData.getFaceIds());
				customerFaceIdMatchData.setLinkedCustomerId(null);
				imageRepository.add(customerFaceIdMatchData);
			}
		}catch (Exception e){
			LOG.info("error while adding to data base {} ",e);
		}
	}
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void processFaceSuccessMetricData(FaceRequest inputRequest,Optional<FaceMatch> face,Integer customerId,String customerContact,float threshold){
		try{
			CustomerFaceSuccessMetricData successMetricData = imageRepository.findFaceSuccessMetricData(customerId);
			if(face.isPresent()){
				if(Objects.isNull(successMetricData)){
					saveFaceSuccessMetricData(inputRequest,face.get().getFace(), customerId,customerContact,threshold);
				}
			}
		}catch (Exception e){
			LOG.info("error while adding Face Success Metric data to data base {} ",e);
		}

	}
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void saveFaceSuccessMetricData(FaceRequest inputRequest, Face face, int customerId, String customerContact,float threshold){
		CustomerFaceSuccessMetricData customerFaceSuccessMetric = new CustomerFaceSuccessMetricData();
		customerFaceSuccessMetric.setCustomerId(customerId);
		customerFaceSuccessMetric.setContactNumber(customerContact);
		customerFaceSuccessMetric.setFaceId(face.getFaceId());
		customerFaceSuccessMetric.setEulerX(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerX()));
		customerFaceSuccessMetric.setEulerY(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerY()));
		customerFaceSuccessMetric.setEulerZ(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerZ()));
		customerFaceSuccessMetric.setRightEyeOpenProbability(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getRightEyeOpenProbability()));
		customerFaceSuccessMetric.setLeftEyeOpenProbability(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getLeftEyeOpenProbability()));
		customerFaceSuccessMetric.setImageConfidence(BigDecimal.valueOf(face.getConfidence()));
		customerFaceSuccessMetric.setThreshold(BigDecimal.valueOf(threshold));
		customerFaceSuccessMetric.setCreationTime(AppUtils.getCurrentTimestamp());
		imageRepository.add(customerFaceSuccessMetric);
	}
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateFaceSuccessMetricData(FaceRequest inputRequest, Optional<FaceRecord> faceRecord,Integer customerId,String customerName,String customerContact,
											FileDetail s3FileDetail, String faceMetadataId,float threshold,String apiVersion){
		try{
			CustomerFaceSuccessMetricData successMetricData = imageRepository.findFaceSuccessMetricData(customerId);
			if(Objects.nonNull(faceRecord.get().getFace())){
				if(Objects.nonNull(successMetricData)){
					updateSuccessMetricData(inputRequest,faceRecord.get().getFace(), customerId,customerName,customerContact,threshold,s3FileDetail,faceMetadataId,
							successMetricData,apiVersion);
				}else{
					saveFaceSuccessMetricData(inputRequest,faceRecord.get().getFace(),customerId,customerContact,threshold);
					customerService.mapCustomerToFace(faceRecord.get().getFace().getFaceId(),customerContact,apiVersion);
				}
			}
		}catch (Exception e){
			LOG.info("error while adding Face Success Metric data to data base {} ",e);
		}

	}
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void updateSuccessMetricData(FaceRequest inputRequest, Face face, int customerId,String customerName, String customerContact,
										float threshold,FileDetail s3FileDetail, String faceMetadataId,CustomerFaceSuccessMetricData existingData , String apiVersion)
			throws IOException {
		existingData.setFaceId(face.getFaceId());
		existingData.setEulerX(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerX()));
		existingData.setEulerY(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerY()));
		existingData.setEulerZ(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerZ()));
		existingData.setRightEyeOpenProbability(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getRightEyeOpenProbability()));
		existingData.setLeftEyeOpenProbability(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getLeftEyeOpenProbability()));
		existingData.setImageConfidence(BigDecimal.valueOf(face.getConfidence()));
		existingData.setThreshold(BigDecimal.valueOf(threshold));
		existingData.setCreationTime(AppUtils.getCurrentTimestamp());
		imageRepository.update(existingData);
		customerService.mapCustomerToFace(face.getFaceId(),customerContact,apiVersion);
	}
	public Boolean findSuccessMetricComparisonFactor(CustomerFaceSuccessMetricData newMetrics , CustomerFaceSuccessMetricData oldMetrics){
			BigDecimal newEulerXWeight = AppUtils.multiply(newMetrics.getEulerX(), BigDecimal.valueOf(0.25)).abs();
			BigDecimal newEulerYWeight = AppUtils.multiply(newMetrics.getEulerY(), BigDecimal.valueOf(0.25)).abs();
			BigDecimal newEulerZWeight = AppUtils.multiply(newMetrics.getEulerX(), BigDecimal.valueOf(0.25)).abs();
			BigDecimal newLeftEyeOpenWeight = AppUtils.multiply(newMetrics.getLeftEyeOpenProbability(), BigDecimal.valueOf(0.125)).abs();
			BigDecimal newRightEyeOpenWeight = AppUtils.multiply(newMetrics.getRightEyeOpenProbability(), BigDecimal.valueOf(0.125)).abs();
			BigDecimal newAngleMetric = AppUtils.add(AppUtils.add(newEulerXWeight,newEulerYWeight),newEulerZWeight);
			BigDecimal newEyeOpenMetric = AppUtils.add(newLeftEyeOpenWeight,newRightEyeOpenWeight);
			BigDecimal newWeightedMetric = AppUtils.subtract(newAngleMetric,newEyeOpenMetric);
			BigDecimal oldEulerXWeight = AppUtils.multiply(oldMetrics.getEulerX(), BigDecimal.valueOf(0.25)).abs();
			BigDecimal oldEulerYWeight = AppUtils.multiply(oldMetrics.getEulerY(), BigDecimal.valueOf(0.25)).abs();
			BigDecimal oldEulerZWeight = AppUtils.multiply(oldMetrics.getEulerX(), BigDecimal.valueOf(0.25)).abs();
			BigDecimal oldLeftEyeOpenWeight = AppUtils.multiply(oldMetrics.getLeftEyeOpenProbability(), BigDecimal.valueOf(0.125)).abs();
			BigDecimal oldRightEyeOpenWeight = AppUtils.multiply(oldMetrics.getRightEyeOpenProbability(), BigDecimal.valueOf(0.125)).abs();
			BigDecimal oldAngleMetric = AppUtils.add(AppUtils.add(oldEulerXWeight,oldEulerYWeight),oldEulerZWeight);
			BigDecimal oldEyeOpenMetric = AppUtils.add(oldLeftEyeOpenWeight,oldRightEyeOpenWeight);
			BigDecimal oldWeightedMetric = AppUtils.subtract(oldAngleMetric,oldEyeOpenMetric);
			return newWeightedMetric.compareTo(oldWeightedMetric) < 0;

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void processUpdateImageMappingData(FaceRequest faceRequest, String faceId){
		try {
			FaceSuccessMetric successMetricData = faceRequest.getFaceSuccessMetric();
			UpdateImageMappingData entity = new UpdateImageMappingData();
			entity.setSubjectName(faceRequest.getCustomerName());
			entity.setContactNumber(faceRequest.getContact());
			entity.setFaceId(faceId);
			entity.setQuality(faceRequest.getQuality());
			entity.setBatchId(faceRequest.getBatchId());
			entity.setCreationTime(AppUtils.getCurrentTimestamp());
			entity.setLastUpdateTime(AppUtils.getCurrentTimestamp());
			entity.setSessionId(faceRequest.getSessionId());
			entity.setEulerX(BigDecimal.valueOf(successMetricData.getEulerX()));
			entity.setEulerY(BigDecimal.valueOf(successMetricData.getEulerY()));
			entity.setEulerZ(BigDecimal.valueOf(successMetricData.getEulerZ()));
			entity.setLeftEyeOpenProbability(BigDecimal.valueOf(successMetricData.getLeftEyeOpenProbability()));
			entity.setRightEyeOpenProbability(BigDecimal.valueOf(successMetricData.getRightEyeOpenProbability()));
			imageRepository.add(entity);
		} catch (Exception ex) {
			LOG.error("Error", ex);
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean checkUpdateFaceSuccessMetricData(FaceRequest inputRequest,Integer customerId,String faceId){
		try{
			List<UpdateImageMappingData> data = imageRepository.findUpdateImageMappingDateByFaceId(faceId);
			if(!data.isEmpty()){
				return true;
			}
			CustomerFaceSuccessMetricData successMetricData = imageRepository.findFaceSuccessMetricData(customerId);
			if(Objects.nonNull(successMetricData)){
				CustomerFaceSuccessMetricData customerFaceSuccessMetricData = new CustomerFaceSuccessMetricData();
				customerFaceSuccessMetricData.setEulerX(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerX()));
				customerFaceSuccessMetricData.setEulerY(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerY()));
				customerFaceSuccessMetricData.setEulerZ(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getEulerZ()));
				customerFaceSuccessMetricData.setLeftEyeOpenProbability(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getLeftEyeOpenProbability()));
				customerFaceSuccessMetricData.setRightEyeOpenProbability(BigDecimal.valueOf(inputRequest.getFaceSuccessMetric().getRightEyeOpenProbability()));
				return findSuccessMetricComparisonFactor(customerFaceSuccessMetricData,successMetricData);
			}else{
				return true;
			}
		}catch (Exception e){
			LOG.info("error while adding Face Success Metric data to data base {} ",e);
		}
		return false;

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void deleteSuccessMetricData(String contactNumber) {
		try{
			List<CustomerFaceSuccessMetricData> successMetricData =  imageRepository.deleteFaceSuccessMatricData(contactNumber);
			for(CustomerFaceSuccessMetricData scmd : successMetricData){
				imageRepository.delete(scmd);
			}
		}catch (Exception e){
			LOG.info("Error in deleteing success metric",e);
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<String> lookUpExistingFaceIdMapping(String phoneNumber) {
		return imageRepository.lookUpExistingFaceIdMapping(phoneNumber);
	}
}
