package com.stpl.tech.kettle.rekognition.service.impl;

import java.io.File;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.service.FileService;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;

@Service
public class FileServiceImpl implements FileService {
	private static final Logger LOG = LoggerFactory.getLogger(FileServiceImpl.class);

	@Autowired
	private FileArchiveService fileService;

	@Autowired
	private RekognitionProperties props;

	@Override
	public FileDetail saveFile(MultipartFile file, String fileName, String bucket, String basePath) {
		LOG.info("Saving file to S3");
		return fileService.saveFileToS3(bucket, basePath, fileName, file);
	}

	@Override
	public FileDetail saveFile(File sourceImage) {
		LOG.info("Uploading image");
		return fileService.saveFileToS3(props.getS3BucketName(), null, sourceImage, true);
	}

}
