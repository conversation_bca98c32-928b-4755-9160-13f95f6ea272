package com.stpl.tech.kettle.rekognition.service;

import com.amazonaws.services.rekognition.model.FaceMatch;
import com.amazonaws.services.rekognition.model.FaceRecord;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.rekognition.entity.LookUpImageMappingData;
import com.stpl.tech.kettle.rekognition.entity.SignUpImageMappingData;
import com.stpl.tech.kettle.rekognition.modal.CustomerFaceIdRemoveData;
import com.stpl.tech.kettle.rekognition.modal.FaceRequest;
import com.stpl.tech.kettle.rekognition.modal.ImageSyncRequest;
import com.stpl.tech.kettle.rekognition.modal.LookUpMetaData;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.spring.model.FileDetail;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

public interface ImageManagementService {

	/**
	 * saves file to s3 and logs image mapping data w.r.t. customer
	 *
	 * @param inputRequest
	 *
	 * @param face
	 * @param response
	 * @param s3FileDetail
	 * @throws IOException
	 */
	public void processLookUpImageMappingData(FaceRequest inputRequest, Optional<FaceMatch> face,
			CustomerResponse response, FileDetail s3FileDetail, String faceMetadataId, Float threshold) throws IOException;

	/**
	 * saves file to s3 and logs image mapping data w.r.t. customer
	 *
	 * @param inputRequest
	 *
	 * @param optional
	 * @param response
	 * @throws IOException
	 */
	public void processSignUpImageMappingData(FaceRequest faceRequest, Optional<FaceRecord> faceRecord,
			FileDetail s3FileDetail, String faceMetadataId) throws IOException;

	public List<SignUpImageMappingData> lookUpExistingMapping(String contact);

	/**
	 * Search SignUp data based on session Id
	 *
	 * @param sessionId
	 * @return
	 */
	public SignUpImageMappingData getSignUpDataBySessionId(String sessionId);

	/**
	 * Adds original image mapping via a separate sync call
	 * @param syncRequest
	 * @param s3FileDetail
	 * @param faceMetadataId
	 */
	public void addOriginalImageMapping(ImageSyncRequest syncRequest, FileDetail s3FileDetail, String faceMetadataId);

	public LookUpMetaData findMetaDataWithContactNumber(String contactNumber);

	void addCustomerDataBeforeDeletion(CustomerFaceIdRemoveData faceIdData);

	public void processLookUpCustomerHeuristicData(FaceRequest inputRequest, Optional<FaceMatch> face, Integer customerId,String customerName,String customerContact, FileDetail s3FileDetail, String faceMetadataId, Float threshold) throws IOException;

	public void processSignupCustomerHeuristicData(FaceRequest inputRequest, Optional<FaceMatch> face, Integer customerId,String customerName,String customerContact, FileDetail s3FileDetail, String faceMetadataId, Float threshold);

	public void processFaceSuccessMetricData(FaceRequest inputRequest,Optional<FaceMatch> face,Integer customerId,String customerContact,float threshold);

	public void updateFaceSuccessMetricData(FaceRequest inputRequest, Optional<FaceRecord> faceRecord,Integer customerId,
											String customerName,String customerContact,FileDetail s3FileDetail, String faceMetadataId,float threshold ,String apiVersion);

	public void processUpdateImageMappingData(FaceRequest faceRequest, String faceId);

	public boolean checkUpdateFaceSuccessMetricData(FaceRequest inputRequest,Integer customerId,String faceId);

	public void deleteSuccessMetricData(String contactNumber) throws DataUpdationException;

	public List<String> lookUpExistingFaceIdMapping(String contact);
}
