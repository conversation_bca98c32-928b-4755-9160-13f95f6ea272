package com.stpl.tech.kettle.rekognition.modal;

import com.amazonaws.services.rekognition.model.FaceMatch;

import java.util.Optional;

public class FaceIdMetaData  {

    private Optional<FaceMatch> faceMatch;

    private Float thresholdValue;

    public Optional<FaceMatch> getFaceMatch() {
        return faceMatch;
    }

    public void setFaceMatch(Optional<FaceMatch> faceMatch) {
        this.faceMatch = faceMatch;
    }

    public Float getThresholdValue() {
        return thresholdValue;
    }

    public void setThresholdValue(Float thresholdValue) {
        this.thresholdValue = thresholdValue;
    }
}
