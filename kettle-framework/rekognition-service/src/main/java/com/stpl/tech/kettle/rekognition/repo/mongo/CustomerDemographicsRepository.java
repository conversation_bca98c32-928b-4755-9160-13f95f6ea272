package com.stpl.tech.kettle.rekognition.repo.mongo;

import com.stpl.tech.kettle.rekognition.entity.mongo.DemographicsMetadata;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerDemographicsRepository extends MongoRepository<DemographicsMetadata, String> {

    public DemographicsMetadata findByFaceId(String faceId);

    public void deleteByFaceId(String id);
}
