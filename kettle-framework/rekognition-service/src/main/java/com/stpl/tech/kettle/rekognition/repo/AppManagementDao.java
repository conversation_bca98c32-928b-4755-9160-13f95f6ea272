package com.stpl.tech.kettle.rekognition.repo;

import com.stpl.tech.kettle.rekognition.entity.AppVersionMappingData;
import com.stpl.tech.kettle.rekognition.modal.AppMappingInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationType;

public interface AppManagementDao extends AbstractDao {

	public String getCurrentAppVersion(ApplicationType appType);

	public ApplicationInfo getApplicationInfo(String version, ApplicationType appType);

    AppVersionMappingData getAppVersionMapping(Integer unitId, Integer terminalId, String appType);

    public boolean updateAppVersionMapping(AppMappingInfo appVersionInfo);

}
