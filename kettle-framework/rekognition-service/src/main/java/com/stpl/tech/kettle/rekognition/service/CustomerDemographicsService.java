package com.stpl.tech.kettle.rekognition.service;

import com.amazonaws.services.rekognition.model.FaceDetail;
import com.stpl.tech.kettle.rekognition.entity.mongo.DemographicsMetadata;
import org.springframework.stereotype.Service;

@Service
public interface CustomerDemographicsService {

    public boolean save(DemographicsMetadata demographicsMetadata) ;

    public boolean saveDemographicsMetadata(String id, FaceDetail faceDetail) ;

    public boolean updateDemographicsMetadataStatus(String id,String status,FaceDetail faceDetail);

    public DemographicsMetadata getCustomerDemographicsMetadata(String id);

    public  void deleteCustomerDemographicsMetadata(String id);
}
