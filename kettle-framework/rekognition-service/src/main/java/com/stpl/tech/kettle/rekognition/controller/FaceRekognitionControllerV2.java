package com.stpl.tech.kettle.rekognition.controller;

import com.stpl.tech.kettle.core.data.vo.CustomerResponseTrimmed;
import com.stpl.tech.kettle.rekognition.modal.FaceRequest;
import com.stpl.tech.kettle.rekognition.service.FaceRekognitionService;
import com.stpl.tech.kettle.rekognition.util.RekognitionConstants;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@Log4j2
@RequestMapping(value = RekognitionConstants.API_VERSION_V2 + RekognitionConstants.SEPARATOR
        + RekognitionConstants.REKOGNITION_ROOT_CONTEXT)
public class FaceRekognitionControllerV2 {

    @Autowired
    private FaceRekognitionService faceRekognitionService;


    @PostMapping(value = "look-up")
    public CustomerResponseTrimmed lookup(@RequestBody @Valid FaceRequest faceRequest) {
        log.info("Look-up request for batchId : {}", faceRequest.getBatchId());
        return faceRekognitionService.lookupTrimmed(faceRequest,RekognitionConstants.API_VERSION_V2);
    }

    @PostMapping(value = "sign-up")
    public void signUp(@RequestBody @Valid FaceRequest faceRequest) {
        log.info("Request for Signup for contact {} , customerName {} , batchId {}", faceRequest.getContact(),
                faceRequest.getCustomerName(), faceRequest.getBatchId());
        faceRekognitionService.signUp(faceRequest,RekognitionConstants.API_VERSION_V2);
    }

    @PostMapping(value = "update-face-id")
    public void update(@RequestBody @Valid FaceRequest faceRequest) {
        log.info("update-face-id request for batchId : {}", faceRequest.getBatchId());
        faceRekognitionService.update(faceRequest,RekognitionConstants.API_VERSION_V2);
    }
}
