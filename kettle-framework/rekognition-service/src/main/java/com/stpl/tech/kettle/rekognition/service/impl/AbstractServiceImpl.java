package com.stpl.tech.kettle.rekognition.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.service.FileService;
import com.stpl.tech.kettle.rekognition.util.ImageUtils;
import com.stpl.tech.spring.model.FileDetail;

public abstract class AbstractServiceImpl {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractServiceImpl.class);

	protected abstract FileService getFileService();

	protected abstract RekognitionProperties getEnviournmentProperties();

	protected FileDetail saveFileToS3(String base64Image, String batchId, String contact, String metadataId)
			throws IOException {
		LOG.info("Creating Image form String Base64");
		String imageName = ImageUtils.createImageName(contact, batchId, metadataId,
				getEnviournmentProperties().getImageExtension());
		File sourceImage = createFaceImage(base64Image, imageName);
		FileDetail fileDetail = null;
		if (sourceImage.exists()) {
			fileDetail = getFileService().saveFile(sourceImage);
			LOG.info("File uploaded to path " + fileDetail.getKey());
		}
		return fileDetail;
	}

	private File createFaceImage(String faceImageString, String imageName) throws IOException {
		BufferedImage bufferedImage = ImageUtils.decodeBase64Image(faceImageString);
		File imageBasePath = new File(getImagePath());
		if (!imageBasePath.exists()) {
			imageBasePath.mkdirs();
		}
		File image = new File(imageBasePath + File.separator + imageName);
		ImageIO.write(bufferedImage, getEnviournmentProperties().getImageExtension(), image);
		return image;
	}

	private String getImagePath() {
		String imageFilePath = getEnviournmentProperties().getTmpLocation() + File.separator
				+ getEnviournmentProperties().getImageBasePath();
		LOG.info("Image file path is :: " + imageFilePath);
		return imageFilePath;
	}

}
