package com.stpl.tech.kettle.rekognition.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "UPDATE_IMAGE_MAPPING_DATA")
public class UpdateImageMappingData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "IMAGE_MAPPING_ID")
	private int imageMappingId;

	@Column(name = "SUBJECT_NAME", nullable = true)
	private String subjectName;

	@Column(name = "CONTACT_NUMBER", nullable = false)
	private String contactNumber;

	@Column(name = "FACE_ID", nullable = true)
	private String faceId;
	@Column(name = "EULER_ANGLE_X")
	private BigDecimal eulerX;
	@Column(name = "EULER_ANGLE_Y")
	private BigDecimal eulerY;
	@Column(name = "EULER_ANGLE_Z")
	private BigDecimal eulerZ;
	@Column(name = "RIGHT_EYE_OPEN_PROBABILITY")
	private BigDecimal rightEyeOpenProbability;
	@Column(name = "LEFT_EYE_OPEN_PROBABILITY")
	private BigDecimal leftEyeOpenProbability;

	@Column(name = "QUALITY", nullable = false, precision = 10)
	private BigDecimal quality;

	@Column(name = "BATCH_ID", nullable = false)
	private String batchId;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = true)
	private Date creationTime;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = true)
	private Date lastUpdateTime;

	@Column(name = "SESSION_ID")
	private String sessionId;

	public int getImageMappingId() {
		return imageMappingId;
	}

	public void setImageMappingId(int id) {
		this.imageMappingId = id;
	}

	public String getSubjectName() {
		return subjectName;
	}

	public void setSubjectName(String name) {
		this.subjectName = name;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String phoneNumber) {
		this.contactNumber = phoneNumber;
	}

	public String getFaceId() {
		return faceId;
	}

	public void setFaceId(String faceId) {
		this.faceId = faceId;
	}

	public BigDecimal getQuality() {
		return quality;
	}

	public void setQuality(BigDecimal quality) {
		this.quality = quality;
	}

	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date createdDate) {
		this.creationTime = createdDate;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date updatedDate) {
		this.lastUpdateTime = updatedDate;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public void setEulerX(BigDecimal eulerX) {
		this.eulerX = eulerX;
	}

	public void setEulerY(BigDecimal eulerY) {
		this.eulerY = eulerY;
	}

	public void setEulerZ(BigDecimal eulerZ) {
		this.eulerZ = eulerZ;
	}

	public void setRightEyeOpenProbability(BigDecimal rightEyeOpenProbability) {
		this.rightEyeOpenProbability = rightEyeOpenProbability;
	}

	public void setLeftEyeOpenProbability(BigDecimal leftEyeOpenProbability) {
		this.leftEyeOpenProbability = leftEyeOpenProbability;
	}

	public BigDecimal getEulerX() {
		return eulerX;
	}

	public BigDecimal getEulerY() {
		return eulerY;
	}

	public BigDecimal getEulerZ() {
		return eulerZ;
	}

	public BigDecimal getRightEyeOpenProbability() {
		return rightEyeOpenProbability;
	}

	public BigDecimal getLeftEyeOpenProbability() {
		return leftEyeOpenProbability;
	}
}
