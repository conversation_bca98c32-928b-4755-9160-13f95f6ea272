package com.stpl.tech.kettle.rekognition.util;

import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.CustomerResponseTrimmed;
import com.stpl.tech.kettle.rekognition.entity.AppVersionData;
import com.stpl.tech.kettle.rekognition.modal.ApplicationInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationType;

import java.util.Objects;

public class DataConverter {

	public static ApplicationInfo convert(AppVersionData data) {
		return new ApplicationInfo(data.getAppVersion(), ApplicationType.valueOf(data.getAppType()), data.getFileName(),
				data.getFilePath());
	}

	public static CustomerResponseTrimmed trimCustomerResponse(CustomerResponse customerResponse){
		if ( customerResponse == null ) {
			return null;
		}

		CustomerResponseTrimmed customerResponseTrimmed = new CustomerResponseTrimmed();

		customerResponseTrimmed.setId( customerResponse.getId() );
		customerResponseTrimmed.setName( customerResponse.getName() );
		customerResponseTrimmed.setContact( customerResponse.getContact() );
		customerResponseTrimmed.setEmail( customerResponse.getEmail() );
		customerResponseTrimmed.setUnitId( customerResponse.getUnitId() );
		customerResponseTrimmed.setContactVerified( customerResponse.isContactVerified() );
		customerResponseTrimmed.setSignInMode( customerResponse.getSignInMode() );
		customerResponseTrimmed.setEmailVerified( customerResponse.isEmailVerified() );
		customerResponseTrimmed.setEligibleForSignupOffer( customerResponse.isEligibleForSignupOffer() );
		customerResponseTrimmed.setNewCustomer( customerResponse.isNewCustomer() );
		customerResponseTrimmed.setOtp( customerResponse.getOtp() );
		customerResponseTrimmed.setOtpVerified( customerResponse.isOtpVerified() );
		customerResponseTrimmed.setLastVisitTime( customerResponse.getLastVisitTime() );
		customerResponseTrimmed.setOptOutOfFaceIt( customerResponse.isOptOutOfFaceIt() );
		customerResponseTrimmed.setFaceId( customerResponse.getFaceId() );
		customerResponseTrimmed.setGender( customerResponse.getGender() );
		customerResponseTrimmed.setDateOfBirth( customerResponse.getDateOfBirth() );
		customerResponseTrimmed.setAnniversary( customerResponse.getAnniversary() );
		customerResponseTrimmed.setLoyalityPoints( customerResponse.getLoyalityPoints() );
		customerResponseTrimmed.setOptWhatsapp( customerResponse.getOptWhatsapp() );
		customerResponseTrimmed.setSubscriptionInfoDetail(customerResponse.getSubscriptionInfoDetail());
		customerResponseTrimmed.setWalletBalance(customerResponse.getWalletBalance());
		customerResponseTrimmed.setOrderCount(Objects.nonNull(customerResponse.getOrderCount()) ? customerResponse.getOrderCount() : null);
        customerResponseTrimmed.setAddTime(customerResponse.getAddTime());
		customerResponseTrimmed.setHasSubscription(Objects.nonNull(customerResponse.getSubscriptionInfoDetail()));
		customerResponseTrimmed.setProfileCompleted(customerResponse.isProfileCompleted());

		return customerResponseTrimmed;
	}

}
