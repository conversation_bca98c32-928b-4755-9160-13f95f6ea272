package com.stpl.tech.kettle.rekognition.service;

import com.stpl.tech.kettle.rekognition.modal.AppMappingInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationType;
import com.stpl.tech.master.core.exception.DataUpdationException;

public interface AppManagementService {

	public String getCurrentAppVersion(ApplicationType appType);

	public ApplicationInfo downloadAppVersion(String version, ApplicationType appType);

	public boolean updateAppVersionMapping(AppMappingInfo appVersionInfo);

	public ApplicationInfo saveFile(ApplicationInfo appData) throws DataUpdationException;

}
