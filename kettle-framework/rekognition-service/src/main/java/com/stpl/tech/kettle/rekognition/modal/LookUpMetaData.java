package com.stpl.tech.kettle.rekognition.modal;


import com.stpl.tech.kettle.rekognition.entity.LookUpImageMappingData;
import com.stpl.tech.kettle.rekognition.entity.SignUpImageMappingData;
import com.stpl.tech.kettle.rekognition.entity.mongo.FaceMetadata;

public class LookUpMetaData {
    private String contactNumber;

    private LookUpImageMappingData lookUpImageMappingData;
    private FaceMetadata lookUpFaceMetadata;

    private SignUpImageMappingData signUpImageMappingData;
    private FaceMetadata signUpFaceMetaData;

    public LookUpImageMappingData getLookUpImageMappingData() {
        return lookUpImageMappingData;
    }

    public void setLookUpImageMappingData(LookUpImageMappingData lookUpImageMappingData) {
        this.lookUpImageMappingData = lookUpImageMappingData;
    }

    public FaceMetadata getLookUpFaceMetadata() {
        return lookUpFaceMetadata;
    }

    public void setLookUpFaceMetadata(FaceMetadata lookUpFaceMetadata) {
        this.lookUpFaceMetadata = lookUpFaceMetadata;
    }

    public SignUpImageMappingData getSignUpImageMappingData() {
        return signUpImageMappingData;
    }

    public void setSignUpImageMappingData(SignUpImageMappingData signUpImageMappingData) {
        this.signUpImageMappingData = signUpImageMappingData;
    }

    public FaceMetadata getSignUpFaceMetaData() {
        return signUpFaceMetaData;
    }

    public void setSignUpFaceMetaData(FaceMetadata signUpFaceMetaData) {
        this.signUpFaceMetaData = signUpFaceMetaData;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }
}
