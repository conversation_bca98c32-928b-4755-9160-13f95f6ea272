package com.stpl.tech.kettle.rekognition.service.impl;

import com.stpl.tech.kettle.rekognition.entity.AppVersionActivityLogData;
import com.stpl.tech.kettle.rekognition.entity.AppVersionMappingData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.entity.AppVersionData;
import com.stpl.tech.kettle.rekognition.modal.AppMappingInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationType;
import com.stpl.tech.kettle.rekognition.repo.AppManagementDao;
import com.stpl.tech.kettle.rekognition.service.AppManagementService;
import com.stpl.tech.kettle.rekognition.service.FileService;
import com.stpl.tech.kettle.rekognition.util.DataConverter;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.util.AppUtils;

@Service
@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
public class AppManagementServiceImpl implements AppManagementService {

    @Autowired
    private AppManagementDao appDao;

    @Autowired
    private FileService fileService;

    @Autowired
    private RekognitionProperties props;

    @Override
    @Transactional(readOnly = true)
    public String getCurrentAppVersion(ApplicationType appType) {
        return appDao.getCurrentAppVersion(appType);
    }

    @Override
    public ApplicationInfo downloadAppVersion(String version, ApplicationType appType) {
        return appDao.getApplicationInfo(version, appType);
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public boolean updateAppVersionMapping(AppMappingInfo appVersionInfo) {
        AppVersionMappingData appVersionMappingData = appDao.getAppVersionMapping(appVersionInfo.getUnitId(), appVersionInfo.getTerminal(), appVersionInfo.getAppType());
        if (appVersionMappingData != null) {
            appVersionMappingData.setAppVersion(appVersionInfo.getVersion());
            appVersionMappingData.setBuildNumber(appVersionInfo.getBuildNumber());
            appVersionMappingData.setBundleId(appVersionInfo.getBundleId());
            appVersionMappingData.setDeviceCountry(appVersionInfo.getDeviceCountry());
            appVersionMappingData.setDeviceId(appVersionInfo.getDeviceId());
            appVersionMappingData.setDeviceLocale(appVersionInfo.getDeviceLocale());
            appVersionMappingData.setDeviceModel(appVersionInfo.getDeviceModel());
            appVersionMappingData.setDeviceName(appVersionInfo.getDeviceName());
            appVersionMappingData.setDeviceUniqueId(appVersionInfo.getDeviceUniqueId());
            appVersionMappingData.setDeviceVersion(appVersionInfo.getDeviceVersion());
            appVersionMappingData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
            appVersionMappingData.setMacAddress(appVersionInfo.getMacAddress());
            appVersionMappingData.setManufacturer(appVersionInfo.getManufacturer());
            appVersionMappingData.setSystemName(appVersionInfo.getSystemName());
            appVersionMappingData.setUserAgent(appVersionInfo.getUserAgent());
            appVersionMappingData = (AppVersionMappingData) appDao.update(appVersionMappingData);
            if(appVersionMappingData != null) {
                return createLogData(appVersionInfo, appVersionMappingData.getAppVersionMappingId());
            }
            return false;
        } else {
            appVersionMappingData = new AppVersionMappingData();
            appVersionMappingData.setAppType(appVersionInfo.getAppType());
            appVersionMappingData.setAppVersion(appVersionInfo.getVersion());
            appVersionMappingData.setBuildNumber(appVersionInfo.getBuildNumber());
            appVersionMappingData.setBundleId(appVersionInfo.getBundleId());
            appVersionMappingData.setDeviceCountry(appVersionInfo.getDeviceCountry());
            appVersionMappingData.setDeviceId(appVersionInfo.getDeviceId());
            appVersionMappingData.setDeviceLocale(appVersionInfo.getDeviceLocale());
            appVersionMappingData.setDeviceModel(appVersionInfo.getDeviceModel());
            appVersionMappingData.setDeviceName(appVersionInfo.getDeviceName());
            appVersionMappingData.setDeviceUniqueId(appVersionInfo.getDeviceUniqueId());
            appVersionMappingData.setDeviceVersion(appVersionInfo.getDeviceVersion());
            appVersionMappingData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
            appVersionMappingData.setMacAddress(appVersionInfo.getMacAddress());
            appVersionMappingData.setManufacturer(appVersionInfo.getManufacturer());
            appVersionMappingData.setSystemName(appVersionInfo.getSystemName());
            appVersionMappingData.setTerminalId(appVersionInfo.getTerminal());
            appVersionMappingData.setUnitId(appVersionInfo.getUnitId());
            appVersionMappingData.setUserAgent(appVersionInfo.getUserAgent());
            appVersionMappingData = (AppVersionMappingData) appDao.add(appVersionMappingData);
            if(appVersionMappingData != null) {
                return createLogData(appVersionInfo, appVersionMappingData.getAppVersionMappingId());
            }
            return false;
        }
    }

    @Override
    public ApplicationInfo saveFile(ApplicationInfo appData) throws DataUpdationException {
        ApplicationInfo oldApp = appDao.getApplicationInfo(appData.getVersion(), appData.getAppType());
        if (oldApp != null) {
            throw new DataUpdationException("App with this version already exists");
        }
        FileDetail fileDetail = fileService.saveFile(appData.getFile(), appData.getFileName(),
                props.getApplicationS3Bucket(), props.getApplicationS3BasePath());
        AppVersionData appVersionData = createAppData(appData, fileDetail);
        appDao.add(appVersionData);
        return DataConverter.convert(appVersionData);
    }

    private AppVersionData createAppData(ApplicationInfo appData, FileDetail fileDetail) {
        AppVersionData data = new AppVersionData();
        data.setAppType(appData.getAppType().name());
        data.setAppVersion(appData.getVersion());
        data.setCreationTime(AppUtils.getCurrentTimestamp());
        data.setFileName(fileDetail.getName());
        data.setFilePath(fileDetail.getUrl());
        return data;
    }

    private boolean createLogData(AppMappingInfo appMappingInfo, Integer appVersionMappingId) {
        AppVersionActivityLogData appVersionActivityLogData = new AppVersionActivityLogData();
        appVersionActivityLogData.setAppVersionMappingId(appVersionMappingId);
        appVersionActivityLogData.setAppType(appMappingInfo.getAppType());
        appVersionActivityLogData.setAppVersion(appMappingInfo.getVersion());
        appVersionActivityLogData.setBuildNumber(appMappingInfo.getBuildNumber());
        appVersionActivityLogData.setBundleId(appMappingInfo.getBundleId());
        appVersionActivityLogData.setDeviceCountry(appMappingInfo.getDeviceCountry());
        appVersionActivityLogData.setDeviceId(appMappingInfo.getDeviceId());
        appVersionActivityLogData.setDeviceLocale(appMappingInfo.getDeviceLocale());
        appVersionActivityLogData.setDeviceModel(appMappingInfo.getDeviceModel());
        appVersionActivityLogData.setDeviceName(appMappingInfo.getDeviceName());
        appVersionActivityLogData.setDeviceUniqueId(appMappingInfo.getDeviceUniqueId());
        appVersionActivityLogData.setDeviceVersion(appMappingInfo.getDeviceVersion());
        appVersionActivityLogData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
        appVersionActivityLogData.setMacAddress(appMappingInfo.getMacAddress());
        appVersionActivityLogData.setManufacturer(appMappingInfo.getManufacturer());
        appVersionActivityLogData.setSystemName(appMappingInfo.getSystemName());
        appVersionActivityLogData.setTerminalId(appMappingInfo.getTerminal());
        appVersionActivityLogData.setUnitId(appMappingInfo.getUnitId());
        appVersionActivityLogData.setUserAgent(appMappingInfo.getUserAgent());
        appVersionActivityLogData = (AppVersionActivityLogData) appDao.add(appVersionActivityLogData);
        return appVersionActivityLogData != null;
    }

}
