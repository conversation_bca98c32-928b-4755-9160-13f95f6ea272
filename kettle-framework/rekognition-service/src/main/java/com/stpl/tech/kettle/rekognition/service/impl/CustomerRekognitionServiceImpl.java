package com.stpl.tech.kettle.rekognition.service.impl;

import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.kettle.rekognition.modal.CustomerFaceIdRemoveData;
import com.stpl.tech.kettle.rekognition.util.RekognitionConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.service.CustomerConnectionService;
import com.stpl.tech.master.core.WebServiceHelperExtended;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.endpoint.Endpoints;

@Service
public class CustomerRekognitionServiceImpl implements CustomerConnectionService {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerRekognitionServiceImpl.class);

	@Autowired
	private RekognitionProperties props;

	@Override
	public CustomerResponse getCustomerByFaceId(String faceId,String apiVersion) {
		LOG.info("Searching Customer with FaceId {}", faceId);
		String token = props.getRekognitionAuthToken();
		try {
			String url = apiVersion.equalsIgnoreCase(RekognitionConstants.API_VERSION_V2) ?
					getCustomerRequestEndPointV2() : getCustomerRequestEndPoint();
			return WebServiceHelperExtended.exchangeWithAuth(url, token, HttpMethod.POST,
					CustomerResponse.class, faceId, null);
		} catch (URISyntaxException e) {
			LOG.error("Error While getting customer by Face Id", e);
		}
		return null;
	}
	
	
	@Override
	public Customer getCustomerByContact(String contactNumber,String apiVersion) {
		LOG.info("Searching Customer with Contact Number {}", contactNumber);
		String token = props.getRekognitionAuthToken();
		try {
			String url = RekognitionConstants.API_VERSION_V2.equalsIgnoreCase(apiVersion) ? getCustomerByContactRequestEndPointV2()
					: getCustomerByContactRequestEndPoint();
			return WebServiceHelperExtended.exchangeWithAuth(url, token,
					HttpMethod.POST, Customer.class, contactNumber, null);
		} catch (URISyntaxException e) {
			LOG.error("Error While getting customer by contact number " + contactNumber, e);
		}
		return null;
	}
	
	@Override
	public CustomerFaceIdRemoveData optOutCustomer(String contactNumber) {
		LOG.info("Opt Out Customer of Facial Rekognition {} ", contactNumber);
		String token = props.getRekognitionAuthToken();
		try {
			Map<String,Boolean> uriVariable=new HashMap<>();
			uriVariable.put("flag",true);
			return WebServiceHelperExtended.exchangeWithAuth(getOptOutByContactRequestEndPoint(), token,
				HttpMethod.POST, CustomerFaceIdRemoveData.class, contactNumber, uriVariable);
		} catch (URISyntaxException e) {
			LOG.error("Error While Opting Out Customer " + contactNumber + " of Face Detection", e);
		}
		return null;
	}

	@Override
	public String optOutCustomer(int customerId) {
		LOG.info("Opt Out Customer of Facial Rekognition {} ", customerId);
		String token = props.getRekognitionAuthToken();
		try {
			return WebServiceHelperExtended.exchangeWithAuth(getOptOutByIdRequestEndPoint(), token,
					HttpMethod.POST, String.class, customerId+"", null);
		} catch (URISyntaxException e) {
			LOG.error("Error While Opting Out Customer " + customerId + " of Face Detection", e);
		}
		return null;
	}

	private String getCustomerRequestEndPoint() {
		return props.getCRMServiceBasePath() + Endpoints.GET_CUSTOMER_BY_FACE_ID;
	}

	private String getCustomerRequestEndPointV2() {
		return props.getCRMV2ServiceBasePath() + Endpoints.GET_CUSTOMER_BY_FACE_ID_V2;
	}

	private String getSkipFRMessageRequestEndPoint() {
		return props.getCRMServiceBasePath() + Endpoints.SKIPPED_FACE_IT_NOTIFICATION;
	}

	private String getSkipFRMessageRequestEndPointV2() {
		return props.getCRMV2ServiceBasePath() + Endpoints.SKIPPED_FACE_IT_NOTIFICATION_V2;
	}

	private String getCustomerByContactRequestEndPoint() {
		return props.getCRMServiceBasePath() + Endpoints.GET_CUSTOMER_BY_CONTACT;
	}

	private String getCustomerByContactRequestEndPointV2() {
		return props.getCRMV2ServiceBasePath() + Endpoints.GET_CUSTOMER_BY_CONTACT_V2;
	}

	private String getOptOutByIdRequestEndPoint() {
		return props.getCRMServiceBasePath() + Endpoints.OPT_OUT_CUSTOMER_OF_FACE_IT_BY_ID;
	}


	private String getOptOutByContactRequestEndPoint() {
		return props.getCRMServiceBasePath() + Endpoints.OPT_OUT_CUSTOMER_OF_FACE_IT_BY_CONTACT;
	}

	@Override
	public boolean mapCustomerToFace(String faceId, String contactNumber , String apiVersion) {
		LOG.info("Mapping customer {} with FaceId {}", contactNumber, faceId);
		String token = props.getRekognitionAuthToken();
		Pair<String, String> payload = new Pair<String, String>(contactNumber, faceId);
		try {
			String url = RekognitionConstants.API_VERSION_V2.equalsIgnoreCase(apiVersion) ? getCustomerFaceMappingEndPointV2() :
					getCustomerFaceMappingEndPoint();
			return WebServiceHelperExtended.exchangeWithAuth(url, token, HttpMethod.POST,
					Boolean.class, JSONSerializer.toJSON(payload), null);
		} catch (URISyntaxException e) {
			LOG.error("Error While getting customer by Face Id", e);
		}
		return false;
	}

	private String getCustomerFaceMappingEndPoint() {
		return props.getCRMServiceBasePath() + Endpoints.MAP_CUSTOMER_BY_FACE_ID;
	}

	private String getCustomerFaceMappingEndPointV2() {
		return props.getCRMV2ServiceBasePath() + Endpoints.MAP_CUSTOMER_BY_FACE_ID_V2;
	}


	@Override
	public boolean notifyCustomerOfSkippedFR(String contactNumber ,String apiVersion) {
		LOG.info("Sending Skip FR Message to customer  {}", contactNumber);
		String token = props.getRekognitionAuthToken();
		try {
			String url = RekognitionConstants.API_VERSION_V2.equalsIgnoreCase(apiVersion) ? getSkipFRMessageRequestEndPointV2()
					: getSkipFRMessageRequestEndPoint();
			return WebServiceHelperExtended.exchangeWithAuth(url, token, HttpMethod.POST,
					Boolean.class, contactNumber, null);
		} catch (URISyntaxException e) {
			LOG.error("Error While Sending Skip FR Message to customer " + contactNumber, e);
		}
		return false;
	}

}
