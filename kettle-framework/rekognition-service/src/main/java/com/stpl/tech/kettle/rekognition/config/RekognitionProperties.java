/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.rekognition.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.amazonaws.regions.Regions;
import com.stpl.tech.util.EnvType;

@Service
public class RekognitionProperties {

	@Autowired
	private Environment environment;

	public EnvType getEnvType() {
		return EnvType.valueOf(environment.getProperty("environment.type", "DEV"));
	}

	public String getMongoURI() {
		return environment.getProperty("argos.mongo.uri");
	}

	public String getUploadSystem() {
		return environment.getProperty("upload.system");
	}

	public String getS3BucketName() {
		return environment.getProperty("s3.bucketName");
	}

	public String getAccessKey() {
		return environment.getProperty("s3.accesskey");
	}

	public String getSecretKey() {
		return environment.getProperty("s3.secretKey");
	}

	public String getS3Folder() {
		return environment.getProperty("s3.folder");
	}

	public String getS3Protocol() {
		return environment.getProperty("s3.protocol");
	}

	public String getTmpLocation() {
		return getBasePath() + "/" + environment.getProperty("tmp.location");
	}

	public String getImageBasePath() {
		return environment.getProperty("image.base.path");
	}

	public Boolean isImageSaveEnabled() {
		return Boolean.valueOf(environment.getProperty("rekog.save.image", "false"));
	}
	
	public String getImageExtension() {
		return environment.getProperty("image.extension");
	}

	public Regions getRegion() {
		return Regions.valueOf(environment.getProperty("aws.region"));

	}

	public String getMongoHost() {
		return environment.getProperty("argos.mongo.host", "localhost");
	}

	public int getMongoPort() {
		return Integer.valueOf(environment.getProperty("argos.mongo.port", "27017"));
	}

	public String getMongoSchema() {
		return environment.getProperty("argos.mongo.schema", "kettle_rekognition");
	}

	public String getMongoUser() {
		return environment.getProperty("argos.mongo.user", "root");
	}

	public String getMongoPass() {
		return environment.getProperty("argos.mongo.pass", "root");
	}

	public String getBasePath() {
		return environment.getProperty("server.base.dir");
	}

	public EnvType getEnvironmentType() {
		return EnvType.valueOf(environment.getProperty("environment.type"));
	}

	public String getRekognitionCollectionId() {
		return environment.getProperty("aws.rekognition.collectionId");
	}

	public String getRekognitionAuthToken() {
		return environment.getProperty("rekognition.token");
	}

	public String getKettleServiceBasePath() {
		return environment.getProperty("base.path.kettle.service");
	}

	public String getMasterServiceBasePath() {
		return environment.getProperty("base.path.master.service");
	}

	public String getCRMServiceBasePath() {
		return environment.getProperty("base.path.kettle.crm");
	}

	public String getCRMV2ServiceBasePath(){
		return environment.getProperty("base.path.kettle.crm.v2");
	}

	public String getApplicationS3Bucket() {
		return environment.getProperty("s3.application.bucketName");
	}

	public String getApplicationS3BasePath() {
		return environment.getProperty("s3.application.folder");
	}
	


}
