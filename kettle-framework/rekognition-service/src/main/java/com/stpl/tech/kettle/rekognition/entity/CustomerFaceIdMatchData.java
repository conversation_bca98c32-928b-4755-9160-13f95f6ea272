package com.stpl.tech.kettle.rekognition.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "CUSTOMER_FACE_ID_MATCH_METADATA")
public class CustomerFaceIdMatchData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="ID")
    private Integer id;

    @Column(name="CUSTOMER_ID")
    private Integer customerId;

    @Column(name="CUSTOMER_NAME")
    private String customerName;

    @Column(name="CONTACT_NUMBER")
    private String contactNumber;

    @Column(name="FACE_ID")
    private String faceId;

    @Column(name="LINKED_CUSTOMER_ID")
    private Integer LinkedCustomerId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getFaceId() {
        return faceId;
    }

    public void setFaceId(String faceId) {
        this.faceId = faceId;
    }

    public Integer getLinkedCustomerId() {
        return LinkedCustomerId;
    }

    public void setLinkedCustomerId(Integer linkedCustomerId) {
        LinkedCustomerId = linkedCustomerId;
    }
}
