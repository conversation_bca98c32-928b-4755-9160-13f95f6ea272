package com.stpl.tech.kettle.rekognition.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.service.RekognitionService;
import com.stpl.tech.kettle.rekognition.util.RekognitionConstants;
import com.stpl.tech.util.AppUtils;

@RestController
@RequestMapping(value = RekognitionConstants.API_VERSION + RekognitionConstants.SEPARATOR
		+ RekognitionConstants.ADMIN_ROOT_CONTEXT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST) // 'v1/c'
public class FaceRekognitionAdminController {

	private static final Logger LOG = LoggerFactory.getLogger(FaceRekognitionAdminController.class);

	@Autowired
	private RekognitionService rekogService;

	@Autowired
	private RekognitionProperties props;

	@RequestMapping(value = "delete-collection")
	public boolean deleteCollection(@RequestBody String collectionId) {
		LOG.info("Delete Rekog Collection " + collectionId);
		if (AppUtils.isProd(props.getEnvironmentType())) {
			LOG.info("Skipping Request For Prod of Delete Rekog Collection " + collectionId);
			return false;
		}
		rekogService.deleteCollection(props.getRegion(), collectionId);
		return true;
	}

	@RequestMapping(value = "create-collection")
	public boolean createCollection(@RequestBody String collectionId) {
		LOG.info("Create Rekog Collection " + collectionId);
		if (AppUtils.isProd(props.getEnvironmentType())) {
			LOG.info("Skipping Request For Prod of Create Rekog Collection " + collectionId);
			return false;
		}
		rekogService.createCollection(props.getRegion(), collectionId);
		return true;
	}

}
