package com.stpl.tech.kettle.rekognition.modal;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

public class FaceRequest {

	private String customerName;
	private String contact;
	private String faceDescription;
	private String environmentConditions;
	@NotNull
	private String faceImage;
	private BigDecimal quality;
	@NotNull
	private String batchId;
	@NotNull
	private Integer unitId;
	@NotNull
	private Integer terminalId;
	@NotNull
	private String sessionId;

	private FaceSuccessMetric faceSuccessMetric;

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getFaceDescription() {
		return faceDescription;
	}

	public void setFaceDescription(String faceDescription) {
		this.faceDescription = faceDescription;
	}

	public String getEnvironmentConditions() {
		return environmentConditions;
	}

	public void setEnvironmentConditions(String environmentConditions) {
		this.environmentConditions = environmentConditions;
	}

	public String getFaceImage() {
		return faceImage;
	}

	public void setFaceImage(String faceImage) {
		this.faceImage = faceImage;
	}

	public BigDecimal getQuality() {
		return quality;
	}

	public void setQuality(BigDecimal quality) {
		this.quality = quality;
	}

	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public Integer getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(Integer terminalId) {
		this.terminalId = terminalId;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public FaceSuccessMetric getFaceSuccessMetric() {
		return faceSuccessMetric;
	}
	public void setFaceSuccessMetric(FaceSuccessMetric faceSuccessMetric) {
		this.faceSuccessMetric = faceSuccessMetric;
	}
}
