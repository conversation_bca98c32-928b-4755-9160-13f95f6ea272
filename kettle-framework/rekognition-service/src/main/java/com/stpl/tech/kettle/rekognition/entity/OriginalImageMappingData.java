package com.stpl.tech.kettle.rekognition.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "ORIGINAL_IMAGE_MAPPING_DATA")
public class OriginalImageMappingData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "IMAGE_MAPPING_ID")
	private int imageMappingId;

	@Column(name = "FILE_NAME", nullable = true)
	private String fileName;

	@Column(name = "FILE_PATH", nullable = true)
	private String filePath;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = true)
	private Date creationTime;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = true)
	private Date lastUpdateTime;

	@Column(name = "SESSION_ID")
	private String sessionId;
	
	@Column(name = "FACE_METADATA_ID", nullable = true)
	private String faceMetadataId;

	public int getImageMappingId() {
		return imageMappingId;
	}

	public void setImageMappingId(int imageMappingId) {
		this.imageMappingId = imageMappingId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getFaceMetadataId() {
		return faceMetadataId;
	}

	public void setFaceMetadataId(String faceMetadataId) {
		this.faceMetadataId = faceMetadataId;
	}

}
