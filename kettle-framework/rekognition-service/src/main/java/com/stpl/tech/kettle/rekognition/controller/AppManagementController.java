package com.stpl.tech.kettle.rekognition.controller;

import java.util.Map;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.modal.AppManagePushNotification;
import com.stpl.tech.kettle.rekognition.modal.AppMappingInfo;
import com.stpl.tech.kettle.rekognition.modal.AppNotificationRequest;
import com.stpl.tech.kettle.rekognition.modal.ApplicationInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationType;
import com.stpl.tech.kettle.rekognition.service.AppManagementService;
import com.stpl.tech.kettle.rekognition.util.RekognitionConstants;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.notification.pubnub.PubnubService;
import com.stpl.tech.util.notification.pubnub.PushNotification;

@RestController
@RequestMapping(value = RekognitionConstants.API_VERSION + RekognitionConstants.SEPARATOR
        + RekognitionConstants.APP_MANAGEMENT_ROOT_CONTEXT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST)
// 'v1/c'
public class AppManagementController {
    private static final Logger LOG = LoggerFactory.getLogger(AppManagementController.class);

    @Autowired
    private AppManagementService appService;

    @Autowired
    private RekognitionProperties props;

    @Autowired
    private PubnubService pubnubService;

    /**
     * API to get current available APP version for specific application
     *
     * @param appType
     * @return
     */
    @RequestMapping(value = "version/current", method = RequestMethod.GET)
    public String getCurrentAppVersion(@RequestParam String appType) {
        LOG.info("Request to get current app version for app", appType);
        return appService.getCurrentAppVersion(ApplicationType.valueOf(appType));
    }

    /**
     * API to download Application file for version
     *
     * @param version
     * @param appType
     * @return
     */
    @RequestMapping(value = "download", method = RequestMethod.GET)
    public ApplicationInfo download(@RequestParam String version, @RequestParam String appType) {
        LOG.info("Request to download app version", version);
        return appService.downloadAppVersion(version, ApplicationType.valueOf(appType));
    }

    /**
     * Update Application version mapping with units
     *
     * @param appVersionInfo
     * @return
     */
    @RequestMapping(value = "version/update")
    public boolean updateAppVersionMapping(@RequestBody @Valid AppMappingInfo appVersionInfo) {
        LOG.info("Request for update app version for {}", appVersionInfo);
        return appService.updateAppVersionMapping(appVersionInfo);
    }

    @RequestMapping(value = "upload")
    public ApplicationInfo upload(@RequestBody ApplicationInfo appData) throws DataUpdationException {
        LOG.info("Request to uolpad app version for {}", appData);
        return appService.saveFile(appData);
    }

    @RequestMapping(value = "pushNotification")
    public Boolean pushNotification(@RequestBody AppNotificationRequest request) {
        LOG.info("Request to push pubnub message for {}", request);
        PushNotification<Map<String, String>> notification = new AppManagePushNotification(props.getEnvironmentType(), request);
        pubnubService.sendNotification(notification);
        return true;
    }
}
