package com.stpl.tech.kettle.rekognition.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CUSTOMER_FACE_SUCCESS_METRIC_DATA")
@Getter
@Setter
public class CustomerFaceSuccessMetricData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "FACE_SUCCESS_METRIC_ID")
    private Integer id;
    @Column(name = "CUSTOMER_ID")
    private Integer customerId;
    @Column(name = "CONTACT_NUMBER")
    private String contactNumber;
    @Column(name = "FACE_ID")
    private String faceId;
    @Column(name = "EULER_ANGLE_X")
    private BigDecimal eulerX;
    @Column(name = "EULER_ANGLE_Y")
    private BigDecimal eulerY;
    @Column(name = "EULER_ANGLE_Z")
    private BigDecimal eulerZ;
    @Column(name = "RIGHT_EYE_OPEN_PROBABILITY")
    private BigDecimal rightEyeOpenProbability;
    @Column(name = "LEFT_EYE_OPEN_PROBABILITY")
    private BigDecimal leftEyeOpenProbability;
    @Column(name = "IMAGE_CONFIDENCE")
    private BigDecimal imageConfidence;
    @Column(name="THRESHOLD_VALUE")
    private BigDecimal threshold;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false)
    private Date creationTime;


}
