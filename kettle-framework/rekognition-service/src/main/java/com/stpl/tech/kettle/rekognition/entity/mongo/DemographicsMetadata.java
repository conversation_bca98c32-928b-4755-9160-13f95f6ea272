package com.stpl.tech.kettle.rekognition.entity.mongo;

import com.amazonaws.services.rekognition.model.FaceDetail;
import org.springframework.data.mongodb.core.mapping.Document;
import javax.persistence.Id;

@Document
public class DemographicsMetadata {

    @Id
    private String id;

    private String faceId;

    private  String status;
    private FaceDetail faceDetail;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFaceId() {
        return faceId;
    }

    public void setFaceId(String faceId) {
        this.faceId = faceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public FaceDetail getFaceDetail() {
        return faceDetail;
    }

    public void setFaceDetail(FaceDetail faceDetail) {
        this.faceDetail = faceDetail;
    }
}
