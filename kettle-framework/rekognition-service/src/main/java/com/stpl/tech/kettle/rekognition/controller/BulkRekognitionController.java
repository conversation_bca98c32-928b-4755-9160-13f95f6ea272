package com.stpl.tech.kettle.rekognition.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.kettle.rekognition.util.RekognitionConstants;

@RestController
@RequestMapping(value = RekognitionConstants.API_VERSION + RekognitionConstants.SEPARATOR
		+ RekognitionConstants.BULK_ROOT_CONTEXT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST) // 'v1/c'
public class BulkRekognitionController {
	/*
	 * 
	 * private static final Logger LOG =
	 * LoggerFactory.getLogger(BulkRekognitionController.class);
	 * 
	 * @Autowired private RekognitionService rekognitionService;
	 * 
	 * 
	 * @Autowired private CustomerRekognitionService customerService;
	 * 
	 * 
	 * @Autowired private FileService fileService;
	 * 
	 * @Autowired private RekognitionProperties props;
	 * 
	 * @Autowired private ImageManagementService imageManager;
	 * 
	 * @Autowired private FaceMetadataService faceMetadataService;
	 * 
	 * @RequestMapping(value = "sign-up/bulk", consumes = MediaType.ALL_VALUE)
	 * public boolean detectFaceInBulk(@RequestParam("file") final MultipartFile
	 * zipFile) throws Exception { try { LOG.info("Detect face in bulk"); byte[] buf
	 * = new byte[1024]; String zipFileName = props.getTmpLocation() +
	 * File.separator + zipFile.getOriginalFilename().substring(0,
	 * zipFile.getOriginalFilename().lastIndexOf(".")) + ".zip"; File file = new
	 * File(zipFileName); if (!file.exists()) { file.getParentFile().mkdirs();
	 * file.createNewFile(); } try (InputStream inputStream =
	 * zipFile.getInputStream(); FileOutputStream fileOutputStream = new
	 * FileOutputStream(file)) { int numRead = 0; while ((numRead =
	 * inputStream.read(buf)) >= 0) { fileOutputStream.write(buf, 0, numRead); } }
	 * ZipFile unzipFile = new ZipFile(file); String extractedZipFolder =
	 * zipFileName.substring(0, zipFileName.lastIndexOf("."));
	 * unzipFile.extractAll(extractedZipFolder); file.delete();
	 * 
	 * if (StringUtils.isNotBlank(extractedZipFolder)) { List<FaceRequest>
	 * faceRequests = getFaceRequests(extractedZipFolder); if
	 * (CollectionUtils.isNotEmpty(faceRequests)) { for (FaceRequest faceRequest :
	 * faceRequests) { detectFace(faceRequest); } } } } catch (Exception ex) {
	 * LOG.error("Exception Occurred in face/detection ", ex); return false; }
	 * return true; }
	 * 
	 * @RequestMapping(value = "map-unindexed/bulk", consumes =
	 * MediaType.APPLICATION_JSON_VALUE) public boolean mapUnindexedBulk() throws
	 * Exception { try { LOG.info("Map Unindexed Records with face Id in bulk");
	 * Regions region = props.getRegion(); List<ImageMappingData> list =
	 * imageManager.getUnIndexedMappings(); LOG.info("Found " + list.size() +
	 * " unmapped faces from image mapping data"); if (list != null && list.size() >
	 * 0) { for (ImageMappingData data : list) { FaceMatch face =
	 * rekognitionService.searchFacesByImage(region, props.getS3BucketName(),
	 * props.getRekognitionCollectionId(), data.getFileName()); if (face != null) {
	 * imageManager.updateMappedFaceId(data.getImageMappingId(),
	 * face.getFace().getFaceId()); } } }
	 * 
	 * } catch (Exception ex) {
	 * LOG.error("Exception Occurred in mapping unindexed faces ", ex); return
	 * false; } return true; }
	 * 
	 * private List<FaceRequest> getFaceRequests(String extractedFolderPath) throws
	 * Exception { List<FaceRequest> faceRequests = new ArrayList<FaceRequest>();
	 * try { File extractedFolder = new File(extractedFolderPath); if
	 * (extractedFolder.exists()) { for (File jsonFile :
	 * extractedFolder.listFiles()) { if (jsonFile != null) { JSONObject
	 * exportBookJSON = JSONObject .fromObject(FileUtils.readFileToString(jsonFile,
	 * RekognitionConstants.ENCODING_UTF_8)); if (exportBookJSON == null) {
	 * LOG.error("ERROR! Invalid json file" + jsonFile.getAbsolutePath()); return
	 * null; } FaceRequest faceRequest =
	 * JSONSerializer.toJSON(exportBookJSON.toString(), FaceRequest.class); if
	 * (faceRequest != null) { faceRequests.add(faceRequest); } } } } } catch
	 * (Exception ex) { LOG.error("Exception occurred while in getFaceRequest", ex);
	 * } return faceRequests; }
	 * 
	 * private FaceRecord createIndex(Regions region, String fileName) { FaceRecord
	 * faceRecord = rekognitionService.addFaces(region, props.getS3BucketName(),
	 * props.getRekognitionCollectionId(), fileName); return faceRecord; }
	 * 
	 * public boolean detectFace(@RequestBody FaceRequest faceRequest) throws
	 * Exception { try { Regions region = props.getRegion();
	 * LOG.info("Creating Image form String Base64"); String imageName =
	 * ImageUtils.createImageName(faceRequest.getContact(),
	 * faceRequest.getBatchId(), props.getImageExtension()); File sourceImage =
	 * imageManager.createFaceImage(faceRequest.getFaceImage(), imageName);
	 * FileDetail fileDetail = null; if (sourceImage.exists()) { fileDetail =
	 * fileService.saveFile(sourceImage); LOG.info("File uploaded to path " +
	 * fileDetail.getKey()); } if (fileDetail != null &&
	 * StringUtils.isNotBlank(fileDetail.getUrl())) { String faceMetadataId =
	 * UUID.randomUUID().toString();
	 * faceMetadataService.saveFaceMetadata(faceMetadataId,
	 * faceRequest.getFaceDescription(), faceRequest.getEnvironmentConditions(),
	 * StringUtils.EMPTY); boolean exists =
	 * imageManager.lookUpExistingMapping(faceRequest.getContact()); FaceRecord
	 * faceRecord = null; if (!exists) { faceRecord = createIndex(region,
	 * fileDetail.getName()); }
	 * imageManager.saveFaceDetectionDetails(faceRequest.getCustomerName(),
	 * faceRequest.getContact(), faceRecord == null ? null :
	 * faceRecord.getFace().getFaceId(), imageName, fileDetail.getUrl(),
	 * faceMetadataId, faceRequest.getQuality(), faceRequest.getBatchId()); } }
	 * catch (Exception ex) { LOG.error("Exception Occurred in face/detection ",
	 * ex); return false; } return true; }
	 */}
