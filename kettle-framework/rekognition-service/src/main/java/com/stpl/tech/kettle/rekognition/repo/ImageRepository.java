package com.stpl.tech.kettle.rekognition.repo;

import com.stpl.tech.kettle.rekognition.entity.*;
import com.stpl.tech.master.core.exception.DataUpdationException;

import java.util.List;

public interface ImageRepository extends AbstractDao {

	public List<SignUpImageMappingData> findByFaceId(String faceId);

	public List<SignUpImageMappingData> lookUpExistingMapping(String phoneNumber);

	public List<SignUpImageMappingData> getUnIndexedMappings();

	public boolean updateMappedFaceId(int imageMetadataId, String faceId);

	public SignUpImageMappingData findBySessionId(String sessionId);

	public LookUpImageMappingData findMetaDataWithContactNumber(String contactNumber);

	public CustomerHeuristicData findCustomerData(Integer customerId);

	public CustomerFaceSuccessMetricData findFaceSuccessMetricData(Integer customerId);

	public List<CustomerFaceSuccessMetricData> deleteFaceSuccessMatricData(String contactNumber);

	public List<String> lookUpExistingFaceIdMapping(String contactNumber);

	public List<UpdateImageMappingData> findUpdateImageMappingDateByFaceId(String faceId);

}
