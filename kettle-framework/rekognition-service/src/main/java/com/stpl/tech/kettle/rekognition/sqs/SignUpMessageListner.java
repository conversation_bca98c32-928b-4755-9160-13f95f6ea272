package com.stpl.tech.kettle.rekognition.sqs;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.rekognition.service.FaceRekognitionService;

public class SignUpMessageListner implements MessageListener {

	private static final Logger LOG = LoggerFactory.getLogger(SignUpMessageListner.class);

	private MessageProducer errorQueue;

	private FaceRekognitionService service;

	public SignUpMessageListner(MessageProducer errorQueue, FaceRekognitionService service) {
		this.errorQueue = errorQueue;
		this.service = service;
	}

	@Override
	public void onMessage(Message message) {
		if (message != null) {
			try {
				message.acknowledge();
			} catch (JMSException e) {
				LOG.error("Error in acknowledging message for SignUpMessageListner");
			}
		}
	}

}
