package com.stpl.tech.kettle.rekognition.util;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.rekognition.AmazonRekognition;
import com.amazonaws.services.rekognition.AmazonRekognitionClientBuilder;

public class ClientFactory {

	public static AmazonRekognition rekognition;

	public static AmazonRekognition getClient(Regions region) {
		if (rekognition == null) {
			ClientConfiguration clientConfig = new ClientConfiguration();
			clientConfig.setConnectionTimeout(30000);
			clientConfig.setRequestTimeout(60000);
			clientConfig.setProtocol(Protocol.HTTPS);

			rekognition = AmazonRekognitionClientBuilder.standard().withClientConfiguration(clientConfig)
					.withCredentials(new AWSCredentialsProvider() {

						private AWSCredentials credentials = new RekognitionCredentials();

						@Override
						public void refresh() {

						}

						@Override
						public AWSCredentials getCredentials() {
							return credentials;
						}
					}).withRegion(Region.getRegion(region).getName()).build();
		}
		return rekognition;
	}
}