package com.stpl.tech.kettle.rekognition.controller;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.modal.ImageSyncRequest;
import com.stpl.tech.kettle.rekognition.service.ImageSyncService;
import com.stpl.tech.kettle.rekognition.util.RekognitionConstants;

@RestController
@RequestMapping(value = RekognitionConstants.API_VERSION + RekognitionConstants.SEPARATOR
		+ RekognitionConstants.IMAGE_SYNC_ROOT_CONTEXT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST) // 'v1/c'
public class ImageSyncController {

	private static final Logger LOG = LoggerFactory.getLogger(ImageSyncController.class);

	@Autowired
	private ImageSyncService imageSyncService;

	@Autowired
	private RekognitionProperties props;

	/**
	 * Sync Original Images to save in s3
	 * 
	 * @param syncRequest
	 */
	@RequestMapping(value = "original-image")
	public boolean saveOriginalImage(@RequestBody @Valid ImageSyncRequest syncRequest) {
		LOG.info("Request for Sync for session Id {}", syncRequest.getSessionId());
		if (props.isImageSaveEnabled()) {
			return imageSyncService.saveOriginalImage(syncRequest);
		} else {
			LOG.info("Skipping Request for Sync for session Id {}", syncRequest.getSessionId());
		}
		return false;
	}
}
