package com.stpl.tech.kettle.rekognition.repo.impl;

import java.util.List;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.rekognition.entity.AppVersionActivityLogData;
import com.stpl.tech.kettle.rekognition.entity.AppVersionData;
import com.stpl.tech.kettle.rekognition.entity.AppVersionMappingData;
import com.stpl.tech.kettle.rekognition.modal.AppMappingInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationInfo;
import com.stpl.tech.kettle.rekognition.modal.ApplicationType;
import com.stpl.tech.kettle.rekognition.repo.AppManagementDao;
import com.stpl.tech.kettle.rekognition.util.DataConverter;
import com.stpl.tech.util.AppUtils;

@Repository
public class AppManagementDaoImpl extends AbstractDaoImpl implements AppManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(AppManagementDaoImpl.class);

    @Override
    public String getCurrentAppVersion(ApplicationType appType) {
        Query query = manager
                .createQuery("FROM AppVersionData E WHERE E.appType = :appType ORDER BY E.versionDataId DESC");
        query.setParameter("appType", appType.name());
        query.setMaxResults(1);
        try {
            List<?> resultList = query.getResultList();
            if (resultList != null && !resultList.isEmpty()) {
                return ((AppVersionData) resultList.get(0)).getAppVersion();
            }
        } catch (Exception e) {
            LOG.error("Error while getting app version", e);
        }
        return null;
    }

    @Override
    public ApplicationInfo getApplicationInfo(String appVersion, ApplicationType appType) {
        Query query = manager
                .createQuery("FROM AppVersionData E WHERE E.appType = :appType AND E.appVersion = :appVersion");
        query.setParameter("appType", appType.name());
        query.setParameter("appVersion", appVersion);
        try {
            AppVersionData data = (AppVersionData) query.getSingleResult();
            return DataConverter.convert(data);
        } catch (Exception e) {
            LOG.error("Error while getting app version", e);
        }
        return null;
    }

    @Override
    public AppVersionMappingData getAppVersionMapping(Integer unitId, Integer terminalId, String appType) {
        Query query = manager.createQuery("FROM AppVersionMappingData E WHERE E.unitId = :unitId "
                + " AND E.terminalId = :terminalId AND E.appType = :appType");
        query.setParameter("appType", appType);
        query.setParameter("terminalId", terminalId);
        query.setParameter("unitId", unitId);
        try {
            return (AppVersionMappingData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        } catch (NonUniqueResultException e) {
            return (AppVersionMappingData) query.getResultList().get(0);
        }
    }

    @Override
    public boolean updateAppVersionMapping(AppMappingInfo appVersionInfo) {
        Query query = manager.createQuery("FROM AppVersionMappingData E WHERE E.unitId = :unitId "
                + " AND E.terminalId = :terminalId AND E.appType = :appType");
        query.setParameter("appType", appVersionInfo.getAppType());
        query.setParameter("terminalId", appVersionInfo.getTerminal());
        query.setParameter("unitId", appVersionInfo.getUnitId());
        try {
            AppVersionMappingData data = (AppVersionMappingData) query.getSingleResult();
            if (!data.getAppVersion().equals(appVersionInfo.getVersion())) {
                // update version
                data.setAppVersion(appVersionInfo.getVersion());
                data.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                update(data);
                // make version update entry
                AppVersionActivityLogData log = createLogData(data, appVersionInfo);
                add(log);
            }
        } catch (NoResultException e) {
            AppVersionMappingData appVersionMappingData = new AppVersionMappingData();
            appVersionMappingData.setAppType(appVersionInfo.getAppType());
            appVersionMappingData.setAppVersion(appVersionInfo.getVersion());
            appVersionMappingData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
            appVersionMappingData.setTerminalId(appVersionInfo.getTerminal());
            appVersionMappingData.setUnitId(appVersionInfo.getUnitId());
            appVersionMappingData = add(appVersionMappingData);
            if (appVersionMappingData != null) {
                AppVersionActivityLogData log = createLogData(appVersionMappingData, appVersionInfo);
                add(log);
            }
            return appVersionMappingData != null;
        } catch (Exception e) {
            LOG.error("Error while getting app version", e);
            return false;
        }
        return true;
    }

    private AppVersionActivityLogData createLogData(AppVersionMappingData data, AppMappingInfo appVersionInfo) {
        AppVersionActivityLogData log = new AppVersionActivityLogData();
        log.setAppType(data.getAppType());
        log.setLastUpdateTime(AppUtils.getCurrentTimestamp());
        log.setTerminalId(data.getTerminalId());
        log.setAppVersionMappingId(data.getAppVersionMappingId());
        log.setUnitId(data.getUnitId());
        log.setAppVersion(appVersionInfo.getVersion());
        return log;
    }

}
