package com.stpl.tech.kettle.rekognition.service.impl;

import com.amazonaws.services.rekognition.model.FaceDetail;
import com.stpl.tech.kettle.rekognition.entity.mongo.DemographicsMetadata;
import com.stpl.tech.kettle.rekognition.repo.mongo.CustomerDemographicsRepository;
import com.stpl.tech.kettle.rekognition.service.CustomerDemographicsService;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class CustomerDemographicsServiceImpl implements CustomerDemographicsService {

    private static final Logger LOG = LoggerFactory.getLogger(CustomerDemographicsServiceImpl.class);

    @Autowired
    private CustomerDemographicsRepository customerDemographicsRepository;

    @Override
    public boolean save(DemographicsMetadata demographicsMetadata) {
        try {
            customerDemographicsRepository.save(demographicsMetadata);
        } catch (Exception e) {
            LOG.info("Exception occurred while saving demographics metadata", e);
            return false;
        }
        return true;
    }

    @Override
    public boolean saveDemographicsMetadata(String id, FaceDetail faceDetail) {
        DemographicsMetadata demographicsMetadata = new DemographicsMetadata();
        demographicsMetadata.setFaceId(id);
        demographicsMetadata.setStatus(AppConstants.ACTIVE);
        demographicsMetadata.setFaceDetail(faceDetail);
        return save(demographicsMetadata);
    }

    @Override
    public boolean updateDemographicsMetadataStatus(String id,String status,FaceDetail faceDetail){
        DemographicsMetadata demographicsMetadata = getCustomerDemographicsMetadata(id);
        demographicsMetadata.setStatus(status);
        if(Objects.nonNull(faceDetail)){
            demographicsMetadata.setFaceDetail(faceDetail);
        }
        return save(demographicsMetadata);
    }

    public DemographicsMetadata getCustomerDemographicsMetadata(String id){
        return customerDemographicsRepository.findByFaceId(id);
    }

    public  void deleteCustomerDemographicsMetadata(String id){
        customerDemographicsRepository.deleteByFaceId(id);
    }
}
