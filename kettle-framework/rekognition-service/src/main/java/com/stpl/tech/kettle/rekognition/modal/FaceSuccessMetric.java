package com.stpl.tech.kettle.rekognition.modal;

public class FaceSuccessMetric {

    private float eulerX;
    private float eulerY;
    private float eulerZ;
    private float rightEyeOpenProbability;
    private float leftEyeOpenProbability;

    public float getEulerX() {
        return eulerX;
    }

    public void setEulerX(float eulerX) {
        this.eulerX = eulerX;
    }

    public float getEulerY() {
        return eulerY;
    }

    public void setEulerY(float eulerY) {
        this.eulerY = eulerY;
    }

    public float getEulerZ() {
        return eulerZ;
    }

    public void setEulerZ(float eulerZ) {
        this.eulerZ = eulerZ;
    }

    public float getRightEyeOpenProbability() {
        return rightEyeOpenProbability;
    }

    public void setRightEyeOpenProbability(float rightEyeOpenProbability) {
        this.rightEyeOpenProbability = rightEyeOpenProbability;
    }

    public float getLeftEyeOpenProbability() {
        return leftEyeOpenProbability;
    }

    public void setLeftEyeOpenProbability(float leftEyeOpenProbability) {
        this.leftEyeOpenProbability = leftEyeOpenProbability;
    }
}
