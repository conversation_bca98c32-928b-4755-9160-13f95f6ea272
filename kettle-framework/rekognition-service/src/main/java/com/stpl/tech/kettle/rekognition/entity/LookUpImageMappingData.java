package com.stpl.tech.kettle.rekognition.entity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "LOOKUP_IMAGE_MAPPING_DATA")
public class LookUpImageMappingData {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "IMAGE_MAPPING_ID")
	private int imageMappingId;

	@Column(name = "SUBJECT_NAME")
	private String subjectName;

	@Column(name = "CONTACT_NUMBER")
	private String contactNumber;

	@Column(name = "FACE_ID")
	private String faceId;

	@Column(name = "FILE_NAME")
	private String fileName;

	@Column(name = "FILE_PATH")
	private String filePath;

	@Column(name = "FACE_METADATA_ID")
	private String faceMetadataId;

	@Column(name = "MAPPED_FACE_ID")
	private String mappedFaceId;

	@Column(name = "QUALITY", nullable = false, precision = 10)
	private BigDecimal quality;

	@Column(name = "BATCH_ID", nullable = false)
	private String batchId;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = false)
	private Date creationTime;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	private Date lastUpdateTime;

	@Column(name = "SESSION_ID")
	private String sessionId;

	@Column(name="THRESHOLD_VALUE")
	private BigDecimal threshold;

	@Column(name="SIMILARITY")
	private BigDecimal similarity;

	@Column(name="CONFIDENCE")
	private BigDecimal confidence;

	public int getImageMappingId() {
		return imageMappingId;
	}

	public void setImageMappingId(int id) {
		this.imageMappingId = id;
	}

	public String getSubjectName() {
		return subjectName;
	}

	public void setSubjectName(String name) {
		this.subjectName = name;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String phoneNumber) {
		this.contactNumber = phoneNumber;
	}

	public String getFaceId() {
		return faceId;
	}

	public void setFaceId(String faceId) {
		this.faceId = faceId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String faceImageName) {
		this.fileName = faceImageName;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String faceImagePath) {
		this.filePath = faceImagePath;
	}

	public String getFaceMetadataId() {
		return faceMetadataId;
	}

	public void setFaceMetadataId(String faceMetadataId) {
		this.faceMetadataId = faceMetadataId;
	}

	public BigDecimal getQuality() {
		return quality;
	}

	public void setQuality(BigDecimal quality) {
		this.quality = quality;
	}

	public String getBatchId() {
		return batchId;
	}

	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}

	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date createdDate) {
		this.creationTime = createdDate;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date updatedDate) {
		this.lastUpdateTime = updatedDate;
	}

	public String getMappedFaceId() {
		return mappedFaceId;
	}

	public void setMappedFaceId(String mappedFaceId) {
		this.mappedFaceId = mappedFaceId;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public BigDecimal getThreshold() {
		return threshold;
	}

	public void setThreshold(BigDecimal threshold) {
		this.threshold = threshold;
	}

	public BigDecimal getSimilarity() {
		return similarity;
	}

	public void setSimilarity(BigDecimal similarity) {
		this.similarity = similarity;
	}

	public BigDecimal getConfidence() {
		return confidence;
	}

	public void setConfidence(BigDecimal confidence) {
		this.confidence = confidence;
	}
}
