package com.stpl.tech.kettle.rekognition.controller;

import javax.validation.Valid;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.endpoint.Endpoints;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.rekognition.modal.FaceRequest;
import com.stpl.tech.kettle.rekognition.service.FaceRekognitionService;
import com.stpl.tech.kettle.rekognition.util.RekognitionConstants;
import com.stpl.tech.kettle.rekognition.modal.LookUpMetaData;

@RestController
@RequestMapping(value = RekognitionConstants.API_VERSION + RekognitionConstants.SEPARATOR
		+ RekognitionConstants.REKOGNITION_ROOT_CONTEXT, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST) // 'v1/c'
public class FaceRekognitionController {
	private static final Logger LOG = LoggerFactory.getLogger(FaceRekognitionController.class);

	@Autowired
	private FaceRekognitionService faceRekognitionService;

	/**
	 * Look Up request to be sent via customer screen for customer search.
	 * <p>
	 * this will return the same object returned by Kettle CRM login call to
	 * facilitate login using face recognition with out breaking the existing flow.
	 * <p>
	 * this call connects to CRM service to get customer info based on the face Id
	 * if found, else returns null
	 * 
	 * @param @FaceRequest
	 * @return
	 */
	@RequestMapping(value = "look-up")
	public CustomerResponse lookup(@RequestBody @Valid FaceRequest faceRequest) {
		LOG.info("Look-up request for batchId : {}", faceRequest.getBatchId());
		return faceRekognitionService.lookup(faceRequest, RekognitionConstants.API_VERSION);
	}

	/**
	 * Sign Up request for a customer image.
	 * <p>
	 * this image will be send via customer screen or any other source. a base64
	 * image format is received for AWS indexing and face Id is then mapped to the
	 * customer.
	 * <p>
	 * case 1: face id does not exist -> assign new faceId
	 * <p>
	 * case 2: face id exists -> then add new face index and mark for anomaly
	 * resolution
	 * 
	 * @param @FaceRequest
	 * @throws Exception
	 */
	@RequestMapping(value = "sign-up")
	public void signUp(@RequestBody @Valid FaceRequest faceRequest) {
		LOG.info("Request for Signup for contact {} , customerName {} , batchId {}", faceRequest.getContact(),
				faceRequest.getCustomerName(), faceRequest.getBatchId());
		faceRekognitionService.signUp(faceRequest,RekognitionConstants.API_VERSION);
	}
	
	
	@RequestMapping(value = "face-it/opt-out-customer/customer-id")
	public boolean optOutOfFaceItById(@RequestBody String customerId) {
		LOG.info("Opt Out Of Face Id By customer Id : {}", customerId);
		return faceRekognitionService.optOutOfFaceIt(Integer.valueOf(customerId));
	}

	@RequestMapping(value = "face-it/opt-out-customer/contact-number")
	public boolean optOutOfFaceItByContact(@RequestBody String contactNumber) {
		LOG.info("Opt Out Of Face Id By contact number : {}", contactNumber);
		return faceRekognitionService.optOutOfFaceIt(contactNumber);
	}

	//api for debug - meta data
	@RequestMapping(  value="face-it/metadata-details")
	public LookUpMetaData faceIdMetaDataDetails(@RequestBody String contactNumber){
		return faceRekognitionService.findMetaDataWithContactNumber(contactNumber);
	}

	@RequestMapping(value = "update-face-id")
	public void update(@RequestBody @Valid FaceRequest faceRequest) {
		LOG.info("update-face-id request for batchId : {}", faceRequest.getBatchId());
		faceRekognitionService.update(faceRequest,RekognitionConstants.API_VERSION);
	}

	@RequestMapping(value = "delete-face-success-metric")
	public void update(@RequestBody String contactNumber){
		try {
			LOG.info("delete-face-success-metric request for contactNumber : {}", contactNumber);
			faceRekognitionService.deleteFaceSuccessMetric(contactNumber);
		}catch (DataUpdationException e){
			LOG.info("delete-face-success-metric error - ", e);
		}

	}
}