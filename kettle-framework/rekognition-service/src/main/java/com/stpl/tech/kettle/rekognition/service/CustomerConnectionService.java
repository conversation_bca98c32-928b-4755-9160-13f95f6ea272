package com.stpl.tech.kettle.rekognition.service;

import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.rekognition.modal.CustomerFaceIdRemoveData;

public interface CustomerConnectionService {

	public CustomerResponse getCustomerByFaceId(String faceId,String apiVersion);

	public boolean mapCustomerToFace(String faceId, String id , String apiVersion);

	CustomerFaceIdRemoveData optOutCustomer(String contactNumber);

	String optOutCustomer(int customerId);


	Customer getCustomerByContact(String contactNumber,String apiVersion);

	boolean notifyCustomerOfSkippedFR(String contactNumber , String apiVersion);

}
