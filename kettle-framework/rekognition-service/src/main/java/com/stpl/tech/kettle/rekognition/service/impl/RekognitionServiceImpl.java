package com.stpl.tech.kettle.rekognition.service.impl;

import com.amazonaws.regions.Regions;
import com.amazonaws.services.rekognition.AmazonRekognition;
import com.amazonaws.services.rekognition.model.*;
import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.rekognition.entity.mongo.DemographicsMetadata;
import com.stpl.tech.kettle.rekognition.modal.FaceIdMetaData;
import com.stpl.tech.kettle.rekognition.service.CustomerDemographicsService;
import com.stpl.tech.kettle.rekognition.service.RekognitionService;
import com.stpl.tech.kettle.rekognition.util.ClientFactory;
import com.stpl.tech.kettle.rekognition.util.ImageUtils;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.nio.charset.CharacterCodingException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
public class RekognitionServiceImpl implements RekognitionService {

	private static final Logger LOG = LoggerFactory.getLogger(RekognitionServiceImpl.class);


	@Autowired
	private CustomerDemographicsService customerDemographicsService;
	/**
	 * this will add face to the collection and return a face Id which uniquely
	 * identifies this indexing
	 * <p>
	 * files are stored in flat structure hence file name is just the name of the
	 * file in the bucket
	 *
	 */
	@Override
	public FaceRecord addFaces(Regions region, String bucket, String collection, String fileName) {
		Image image = new Image().withS3Object(new S3Object().withBucket(bucket).withName(fileName));
		IndexFacesRequest indexFacesRequest = new IndexFacesRequest().withImage(image)
				.withQualityFilter(QualityFilter.AUTO).withMaxFaces(1).withCollectionId(collection)
				.withExternalImageId(fileName).withDetectionAttributes("DEFAULT");
		AmazonRekognition rekognitionClient = ClientFactory.getClient(region);
		IndexFacesResult indexFacesResult = rekognitionClient.indexFaces(indexFacesRequest);
		List<FaceRecord> faceRecords = indexFacesResult.getFaceRecords();
		for (FaceRecord faceRecord : faceRecords) {
			LOG.info("Face Indexed : {}", JSONSerializer.toJSON(faceRecord));
		}
		List<UnindexedFace> unindexedFaces = indexFacesResult.getUnindexedFaces();
		for (UnindexedFace unindexedFace : unindexedFaces) {
			LOG.info("Unindexed Face: {}", JSONSerializer.toJSON(unindexedFace));
		}
		if (faceRecords == null || faceRecords.isEmpty()) {
			return null;
		}
		// we have only 1 face as Max Faces is set to 1
		return faceRecords.get(0);
	}

	@Override
	public FaceRecord addFacesWithBytes(Regions region, String collection, String faceData) throws CharacterCodingException {

		Image image = new Image().withBytes(getByteBuffer(faceData));
		IndexFacesRequest indexFacesRequest = new IndexFacesRequest().withImage(image)
				.withQualityFilter(QualityFilter.AUTO).withMaxFaces(1).withCollectionId(collection).withDetectionAttributes("DEFAULT");
		AmazonRekognition rekognitionClient = ClientFactory.getClient(region);
		IndexFacesResult indexFacesResult = rekognitionClient.indexFaces(indexFacesRequest);
		List<FaceRecord> faceRecords = indexFacesResult.getFaceRecords();
		for (FaceRecord faceRecord : faceRecords) {
			LOG.info("Face Indexed : {}", JSONSerializer.toJSON(faceRecord));
		}
		List<UnindexedFace> unindexedFaces = indexFacesResult.getUnindexedFaces();
		for (UnindexedFace unindexedFace : unindexedFaces) {
			LOG.info("Unindexed Face: {}", JSONSerializer.toJSON(unindexedFace));
		}
		if (faceRecords == null || faceRecords.isEmpty()) {
			return null;
		}
		// we have only 1 face as Max Faces is set to 1
		return faceRecords.get(0);
	}

	private ByteBuffer getByteBuffer(String faceData) throws CharacterCodingException {
		return ByteBuffer.wrap(ImageUtils.decodeBase64TobyteArray(faceData));
	}

	@Override
	public FaceIdMetaData searchFacesByImage(Regions region, String bucket, String collection, String fileName) {
		Image image = new Image().withS3Object(new S3Object().withBucket(bucket).withName(fileName));
		return lookUp(region, collection, image);
	}

	@Override
	public FaceIdMetaData searchFacesByImageByteArray(Regions region, String collection, byte[] bytes) {
		Image image = new Image().withBytes(ByteBuffer.wrap(bytes));
		return lookUp(region, collection, image);
	}

	private FaceIdMetaData lookUp(Regions region, String collection, Image image) {
		Float threshold1 = 99.0F;
		Float threshold2 = 98.0F;
		FaceIdMetaData faceIdMetaData=new FaceIdMetaData();
		LOG.info("finding face with threshold value is {} ",threshold1);
		Optional<FaceMatch> faceMatch1 = findLookUp(region, collection, image, threshold1);
		if ( faceMatch1==null || !faceMatch1.isPresent()) {
			LOG.info("finding face with threshold value is {} ",threshold2);
			Optional<FaceMatch> faceMatch2 = findLookUp(region, collection, image, threshold2);
			faceIdMetaData.setThresholdValue(threshold2);
			faceIdMetaData.setFaceMatch(faceMatch2);
			return faceIdMetaData;
		}
		faceIdMetaData.setThresholdValue(threshold1);
		faceIdMetaData.setFaceMatch(faceMatch1);
		return faceIdMetaData;
	}

	private Optional<FaceMatch> findLookUp(Regions region, String collection, Image image, Float threshold) {
		Stopwatch totalWatch = Stopwatch.createUnstarted();
		totalWatch.start();
		Stopwatch watch = Stopwatch.createUnstarted();

		watch.start();
		AmazonRekognition rekognitionClient = ClientFactory.getClient(region);
		LOG.info("########## client factory acquired ---------- {}", watch.stop().elapsed(TimeUnit.MILLISECONDS));

		watch.start();
		SearchFacesByImageRequest searchFacesByImageRequest = new SearchFacesByImageRequest()
				.withCollectionId(collection).withImage(image).withFaceMatchThreshold(threshold).withMaxFaces(1);

		DetectFacesRequest detectFacesRequest = new DetectFacesRequest().withImage(image).withAttributes(Attribute.ALL);

		LOG.info("########## face search request created ---------- {}", watch.stop().elapsed(TimeUnit.MILLISECONDS));

		watch.start();
		SearchFacesByImageResult searchFacesByImageResult = rekognitionClient
				.searchFacesByImage(searchFacesByImageRequest);
		LOG.info("########## search completed ---------- {}", watch.stop().elapsed(TimeUnit.MILLISECONDS));

		watch.start();
		List<FaceMatch> faceImageMatches = searchFacesByImageResult.getFaceMatches();
		DetectFacesResult detectFacesResult = rekognitionClient.detectFaces(detectFacesRequest);

		List<FaceDetail> faceDetails = detectFacesResult.getFaceDetails();

		for (FaceDetail detectedFace : faceDetails) {
			if (detectFacesRequest.getAttributes().contains("ALL")) {
				AgeRange ageRange = detectedFace.getAgeRange();
				System.out.println("The detected face is estimated to be between "
						+ ageRange.getLow().toString() + " and " + ageRange.getHigh().toString()
						+ " years old.");
				System.out.println("\nBeard - " + detectedFace.getBeard().toString() + "\nEmotions - " + detectedFace.getEmotions().toString() + "\nEyeglasses - " + detectedFace.getEyeglasses().toString() +
						"\nGender - " + detectedFace.getGender().toString());
				System.out.println("Here's the complete set of attributes:");
			} else { // non-default attributes have null values.
				System.out.println("Here's the default set of attributes:");
			}
		}

			LOG.info("########## face match collected ---------- {}", watch.stop().elapsed(TimeUnit.MILLISECONDS));
			LOG.info("Size of faceImageMatches is {}", faceImageMatches.size());
			watch.start();
			if (faceImageMatches.isEmpty()) {
				LOG.info("No face match found");
				return Optional.empty();
			}
			FaceMatch f = null;
			for (FaceMatch face : faceImageMatches) {
				// LOG.info("Face Match : {}", JSONSerializer.toJSON(face));
				if (f == null) {
					f = face;
				} else if (f.getSimilarity().compareTo(face.getSimilarity()) < 0) {
					f = face;
				}
			}
			LOG.info("########## metadata comparison done  ---------- {}", watch.stop().elapsed(TimeUnit.MILLISECONDS));
			LOG.info("########## total time for rekognition method  ---------- {}",
					totalWatch.stop().elapsed(TimeUnit.MILLISECONDS));
			if (f != null) {
				LOG.info("face id is {}", f.getFace().getFaceId());
				LOG.info(" we got similarity as {} ", f.getSimilarity());
				LOG.info(" we got Confidence as {} ", f.getFace().getConfidence());}

		for (FaceDetail detectedFace : faceDetails) {
			if (detectFacesRequest.getAttributes().contains("ALL")) {
				AgeRange ageRange = detectedFace.getAgeRange();
				System.out.println("The detected face is estimated to be between "
						+ ageRange.getLow().toString() + " and " + ageRange.getHigh().toString()
						+ " years old.");
				System.out.println("\nBeard - " + detectedFace.getBeard().toString() + "\nEmotions - " + detectedFace.getEmotions().toString() + "\nEyeglasses - " + detectedFace.getEyeglasses().toString() +
						"\nGender - " + detectedFace.getGender().toString());
				System.out.println("Here's the complete set of attributes:");
				if(Objects.nonNull(f) && Objects.nonNull(f.getFace()) && Objects.nonNull(f.getFace().getFaceId())){
					saveCustomerHeuristicData(f.getFace().getFaceId(),detectedFace);
				}
			} else { // non-default attributes have null values.
				System.out.println("Here's the default set of attributes:");
			}
			}
			return Optional.ofNullable(f);
		}

	public void saveCustomerHeuristicData(String id,FaceDetail detectedFace){
		DemographicsMetadata demographicsMetadata = customerDemographicsService.getCustomerDemographicsMetadata(id);
		if(Objects.isNull(demographicsMetadata)){
			customerDemographicsService.saveDemographicsMetadata(id,detectedFace);
		}else if(Objects.nonNull(demographicsMetadata.getFaceId())&& demographicsMetadata.getFaceId().equals(id) && Objects.nonNull(detectedFace.getConfidence())  && Objects.nonNull(demographicsMetadata.getFaceDetail()) && Objects.nonNull(demographicsMetadata.getFaceDetail().getConfidence()) && detectedFace.getConfidence() > demographicsMetadata.getFaceDetail().getConfidence()){
			customerDemographicsService.updateDemographicsMetadataStatus(id, AppConstants.ACTIVE,detectedFace);
//			customerDemographicsService.deleteCustomerDemographicsMetadata(demographicsMetadata.getFaceId());
//			customerDemographicsService.saveDemographicsMetadata(id,detectedFace);
		}
	}

	public static void main(String[] args) {
		RekognitionServiceImpl impl = new RekognitionServiceImpl();
		impl.createCollection(Regions.AP_SOUTH_1, "uat-rekognition-chaayos");
	}

	@Override
	public void createCollection(Regions region, String collectionId) {
		CreateCollectionRequest request = new CreateCollectionRequest().withCollectionId(collectionId);
		AmazonRekognition rekognition = ClientFactory.getClient(region);
		CreateCollectionResult result = rekognition.createCollection(request);
		LOG.info("Collection created : {}", JSONSerializer.toJSON(result));
	}

	@Override
	public void deleteCollection(Regions region, String collectionId) {
		DeleteCollectionRequest request = new DeleteCollectionRequest().withCollectionId(collectionId);
		AmazonRekognition rekognition = ClientFactory.getClient(region);
		DeleteCollectionResult result = rekognition.deleteCollection(request);
		LOG.info("Collection deleted : {}", JSONSerializer.toJSON(result));
	}

	/**
	 * Please use cautiously as this will delete the data which cannot be
	 * regenerated as indexing same image multiple times gives different face Id
	 *
	 */
	@Override
	public void deleteFace(Regions region, List<String> faces, String collectionId) {
		try{
			AmazonRekognition rekognitionClient = ClientFactory.getClient(region);
			DeleteFacesRequest deleteFacesRequest = new DeleteFacesRequest().withCollectionId(collectionId)
					.withFaceIds(faces);
			DeleteFacesResult deleteFacesResult = rekognitionClient.deleteFaces(deleteFacesRequest);
			List<String> faceRecords = deleteFacesResult.getDeletedFaces();
			for (String face : faceRecords) {
				LOG.info("Deleted FaceID: {}", face);
			}
		}catch (Exception exception){
			LOG.info(" error is {} ", exception);
		}

	}

}
