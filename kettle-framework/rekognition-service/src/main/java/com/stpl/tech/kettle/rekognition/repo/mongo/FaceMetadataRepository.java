package com.stpl.tech.kettle.rekognition.repo.mongo;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.rekognition.entity.mongo.FaceMetadata;

@Repository
public interface FaceMetadataRepository extends MongoRepository<FaceMetadata, String> {

    public FaceMetadata findByMetadataId(String metadataId);
}
