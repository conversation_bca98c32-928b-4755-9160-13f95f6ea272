package com.stpl.tech.kettle.rekognition.sqs;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.service.FaceRekognitionService;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;

//@Service
public class RekognitionSQSMessageService {

	private static final Logger LOG = LoggerFactory.getLogger(RekognitionSQSMessageService.class);
	@Autowired
	private RekognitionProperties props;
	@Autowired
	private FaceRekognitionService service;

	@PostConstruct
	public void init() throws JMSException {
		Regions region = AppUtils.getRegion(props.getEnvironmentType());
		SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
		MessageProducer producer = SQSNotification.getInstance().getProducer(session, props.getEnvironmentType().name(),
				"_SIGN_UP");
		SignUpMessageListner listener = new SignUpMessageListner(producer, service);
		MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvironmentType().name(),
				"_SIGN_UP");
		consumer.setMessageListener(listener);
		startQueueProcessing(region);
	}

	/**
	 * Method to stop Inventory queue listener.
	 * <p>
	 * To be used in pair with startQueueProcessing
	 * 
	 * @throws JMSException
	 */
	public void stopQueueProcessing(Regions region) throws JMSException {
		LOG.info("Stopping Queueing Service");
		SQSNotification.getInstance().getSqsConnection(region).stop();
		LOG.info("Stopped Queueing Service");
	}

	/**
	 * Method to start Inventory queue listener.
	 * <p>
	 * To be used in pair with stopQueueProcessing
	 *
	 * @throws JMSException
	 */
	public void startQueueProcessing(Regions region) throws JMSException {
		LOG.info("Starting Queueing Service");
		SQSNotification.getInstance().getSqsConnection(region).start();
		LOG.info("Started Queueing Service");
	}

}
