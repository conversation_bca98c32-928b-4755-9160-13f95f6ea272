package com.stpl.tech.kettle.rekognition.modal;

import javax.validation.constraints.NotNull;

import org.springframework.web.multipart.MultipartFile;

public class ApplicationInfo {

	@NotNull
	private String version;
	@NotNull
	private ApplicationType appType;
	@NotNull
	private String fileName;
	private String filepath;
	private MultipartFile file;

	public ApplicationInfo() {
		// DEFAULT
	}

	public ApplicationInfo(String version, ApplicationType appType, String fileName, String filepath) {
		super();
		this.version = version;
		this.appType = appType;
		this.fileName = fileName;
		this.filepath = filepath;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public MultipartFile getFile() {
		return file;
	}

	public void setFile(MultipartFile file) {
		this.file = file;
	}

	public ApplicationType getAppType() {
		return appType;
	}

	public void setAppType(ApplicationType appType) {
		this.appType = appType;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getFilepath() {
		return filepath;
	}

	public void setFilepath(String filepath) {
		this.filepath = filepath;
	}

	@Override
	public String toString() {
		return "ApplicationInfo [version=" + version + ", appType=" + appType + ", fileName=" + fileName + "]";
	}

}
