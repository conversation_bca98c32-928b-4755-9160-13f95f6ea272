package com.stpl.tech.kettle.rekognition.service.impl;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.rekognition.config.RekognitionProperties;
import com.stpl.tech.kettle.rekognition.entity.SignUpImageMappingData;
import com.stpl.tech.kettle.rekognition.modal.ImageSyncRequest;
import com.stpl.tech.kettle.rekognition.service.FileService;
import com.stpl.tech.kettle.rekognition.service.ImageManagementService;
import com.stpl.tech.kettle.rekognition.service.ImageSyncService;
import com.stpl.tech.spring.model.FileDetail;

@Service
public class ImageSyncServiceImpl extends AbstractServiceImpl implements ImageSyncService {

	private static final Logger LOG = LoggerFactory.getLogger(ImageSyncServiceImpl.class);

	@Autowired
	private RekognitionProperties props;

	@Autowired
	private FileService fileService;

	@Autowired
	private ImageManagementService imageManager;

	@Override
	public boolean saveOriginalImage(ImageSyncRequest syncRequest) {
		try {
			String faceMetadataId = UUID.randomUUID().toString();
			SignUpImageMappingData signUpData = imageManager.getSignUpDataBySessionId(syncRequest.getSessionId());
			if (signUpData != null) {
				FileDetail s3FileDetail = saveFileToS3(syncRequest.getFaceImage(), signUpData.getBatchId(),
						signUpData.getContactNumber(), faceMetadataId);
				imageManager.addOriginalImageMapping(syncRequest, s3FileDetail, faceMetadataId);
			}
		} catch (Exception ex) {
			LOG.error("Exception Occurred in face/detection ", ex);
			return false;
		}
		return true;
	}

	/**
	 * Abstract class method implementation
	 * 
	 */
	@Override
	protected FileService getFileService() {
		return fileService;
	}

	@Override
	protected RekognitionProperties getEnviournmentProperties() {
		return props;
	}

}
