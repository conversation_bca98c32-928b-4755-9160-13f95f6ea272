package com.stpl.tech.kettle.rekognition.config;

import com.stpl.tech.master.core.config.MasterCacheClientConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;
import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import com.stpl.tech.spring.config.SpringUtilityServiceConfig;
import com.stpl.tech.util.notification.pubnub.PubnubService;
import com.stpl.tech.util.notification.pubnub.impl.PubnubServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@EnableTransactionManagement
@ComponentScan(basePackages = { "com.stpl.tech.kettle.rekognition", "com.stpl.tech.spring.service" })
@EnableMongoRepositories({ "com.stpl.tech.kettle.rekognition.repo.mongo" })
@EnableJpaRepositories(basePackages = {
		"com.stpl.tech.kettle.rekognition.repo" }, entityManagerFactoryRef = "TransactionDataSourceEMFactory", transactionManagerRef = "TransactionDataSourceTM")
@Import(value = { SpringUtilityServiceConfig.class, MasterCacheClientConfig.class, MasterSecurityConfiguration.class })
public class RekognitionConfig {

	@Autowired
	private Environment env;

	public RekognitionConfig() {
		super();
	}

	@Bean(name = "TransactionDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean transactionDataSourceEMFactory() {
		final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(rekognitionDataSource());
		em.setPackagesToScan("com.stpl.tech.kettle.rekognition");
		final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		em.setJpaProperties(masterAdditionalProperties());
		em.setPersistenceUnitName("TransactionDataSourcePUName");
		return em;
	}

	@Bean(name = "rekognitionDataSource")
	public DataSource rekognitionDataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(env.getProperty("jdbc.driverClassName"));
		dataSource.setUrl(env.getProperty("jdbc.url"));
		dataSource.setUsername(env.getProperty("jdbc.user"));
		dataSource.setPassword(env.getProperty("jdbc.pass"));
		return dataSource;
	}

	@Bean(name = "TransactionDataSourceTM")
	public PlatformTransactionManager transactionDataSourceTM() {
		final JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(transactionDataSourceEMFactory().getObject());
		return transactionManager;
	}

	@Bean(name = "rekognitionDataSourceET")
	public PersistenceExceptionTranslationPostProcessor rekognitionDataSourceET() {
		return new PersistenceExceptionTranslationPostProcessor();
	}

	final Properties masterAdditionalProperties() {
		final Properties hibernateProperties = new Properties();
		hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
		hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
		hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
		return hibernateProperties;
	}

//	@Bean
//	public MongoClient factory() throws UnknownHostException {
//		MongoClientURI uri = new MongoClientURI(env.getProperty("spring.data.mongodb.uri"));
//		return new MongoClient(uri);
//	}
//
//	@Bean
//	public MongoDbFactory getMongoDbFactory() throws UnknownHostException {
//		return new SimpleMongoDbFactory(factory(), env.getProperty("spring.data.mongodb.databasebase", "kettle_rekognition"));
//	}
//
//	@Bean(name = "mongoTemplate")
//	public MongoTemplate getMongoTemplate() throws UnknownHostException {
//		return new MongoTemplate(getMongoDbFactory());
//	}

	@Bean
	public ThreadPoolTaskExecutor taskExecutor() {
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		pool.setCorePoolSize(15);
		pool.setMaxPoolSize(30);
		pool.setWaitForTasksToCompleteOnShutdown(true);
		return pool;
	}

	@Bean(name = "multipartResolver")
	public CommonsMultipartResolver commonsMultipartResolver() {
		CommonsMultipartResolver resolver = new CommonsMultipartResolver();
		resolver.setDefaultEncoding("utf-8");
		// Set the maximum allowed size (in bytes) for each individual file.
		resolver.setMaxUploadSizePerFile(55242880);// 5MB
		resolver.setMaxUploadSize(8388608); // 8MB
		return resolver;
	}

	@Bean(name = "pubnubService")
	public PubnubService pubnubNotificationService(){
		return new PubnubServiceImpl(env.getProperty("environment.type"),
				env.getProperty("pubnub.subscribe.key"),
				env.getProperty("pubnub.publish.key"));
	}
}
