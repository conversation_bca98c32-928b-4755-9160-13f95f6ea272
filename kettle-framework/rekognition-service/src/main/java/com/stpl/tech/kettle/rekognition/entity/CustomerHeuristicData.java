package com.stpl.tech.kettle.rekognition.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CUSTOMER_HEURISTIC_DATA")
public class CustomerHeuristicData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CUSTOMER_HEURISTIC_ID")
    private Integer id;

    @Column(name = "CUSTOMER_ID")
    private Integer customerId;

    @Column(name = "FACE_ID")
    private String faceId;

    @Column(name = "AGE_LOW")
    private Integer ageLow;

    @Column(name = "AGE_HIGH")
    private Integer ageHigh;

    @Column(name = "GENDER")
    private String gender;

    @Column(name = "GENDER_CONFIDENCE")
    private BigDecimal genderConfidence;

    @Column(name = "BEARD")
    private String beard;

    @Column(name = "MUSTACHE")
    private String mustache;

    @Column(name = "IMAGE_CONFIDENCE")
    private BigDecimal imageConfidence;

    @Column(name = "SUBJECT_NAME")
    private String subjectName;

    @Column(name = "CONTACT_NUMBER")
    private String contactNumber;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false)
    private Date creationTime;

    @Column(name = "FACE_METADATA_ID")
    private String faceMetadataId;


    @Column(name = "SESSION_ID")
    private String sessionId;

    @Column(name="THRESHOLD_VALUE")
    private BigDecimal threshold;

    @Column(name="EMOTION")
    private String emotion;

    @Column(name="EMOTION_CONFIDENCE")
    private BigDecimal emotionConfidence;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getFaceId() {
        return faceId;
    }

    public void setFaceId(String faceId) {
        this.faceId = faceId;
    }

    public Integer getAgeLow() {
        return ageLow;
    }

    public void setAgeLow(Integer ageLow) {
        this.ageLow = ageLow;
    }

    public Integer getAgeHigh() {
        return ageHigh;
    }

    public void setAgeHigh(Integer ageHigh) {
        this.ageHigh = ageHigh;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public BigDecimal getGenderConfidence() {
        return genderConfidence;
    }

    public void setGenderConfidence(BigDecimal genderConfidence) {
        this.genderConfidence = genderConfidence;
    }

    public String getBeard() {
        return beard;
    }

    public void setBeard(String beard) {
        this.beard = beard;
    }

    public String getMustache() {
        return mustache;
    }

    public void setMustache(String mustache) {
        this.mustache = mustache;
    }

    public BigDecimal getImageConfidence() {
        return imageConfidence;
    }

    public void setImageConfidence(BigDecimal imageConfidence) {
        this.imageConfidence = imageConfidence;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getFaceMetadataId() {
        return faceMetadataId;
    }

    public void setFaceMetadataId(String faceMetadataId) {
        this.faceMetadataId = faceMetadataId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public BigDecimal getThreshold() {
        return threshold;
    }

    public void setThreshold(BigDecimal threshold) {
        this.threshold = threshold;
    }

    public String getEmotion() {
        return emotion;
    }

    public void setEmotion(String emotion) {
        this.emotion = emotion;
    }

    public BigDecimal getEmotionConfidence() {
        return emotionConfidence;
    }

    public void setEmotionConfidence(BigDecimal emotionConfidence) {
        this.emotionConfidence = emotionConfidence;
    }
}
