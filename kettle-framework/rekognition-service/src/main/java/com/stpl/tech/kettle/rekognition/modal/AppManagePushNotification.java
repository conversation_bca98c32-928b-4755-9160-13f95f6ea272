package com.stpl.tech.kettle.rekognition.modal;

import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.pubnub.PushNotification;

import java.util.HashMap;
import java.util.Map;

public class AppManagePushNotification implements PushNotification<Map<String, String>> {


    private String channelName;
    private int retries;
    private Map<String, String> message;

    public AppManagePushNotification(EnvType env, AppNotificationRequest request) {
        this.channelName = initChannel(env, request.getApplicationType(), request.getUnitId(), request.getTerminalId(), request.getChannel());
        setMessage(request.getMessage());
    }

    private String initChannel(EnvType env, ApplicationType applicationType, Integer unitId, Integer terminalId, String channelName) {
        if (ApplicationType.CUSTOMER_SCREEN.equals(applicationType)) {
            return env + "_CustomerScreenChannel_" + unitId + "_" + terminalId;
        }
        return channelName;
    }

    @Override
    public Map<String, String> getMessage() {
        return this.message;
    }

    @Override
    public void setMessage(Map<String, String> message) {
        this.message = message;
    }

    @Override
    public String getChannelName() {
        return this.channelName;
    }

    @Override
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    @Override
    public int getRetries() {
        return retries;
    }

    @Override
    public void setRetries(int value) {
        this.retries = value;
    }
}
