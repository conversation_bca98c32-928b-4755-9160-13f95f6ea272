<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.stpl.tech.kettle</groupId>
		<artifactId>kettle-framework</artifactId>
		<version>6.2.41</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<groupId>com.stpl.tech.kettle.rekognition</groupId>
	<artifactId>rekognition-service</artifactId>
	<name>rekognition-service</name>
	<url>http://maven.apache.org</url>
	<packaging>war</packaging>
<!--	<build>-->
<!--		<plugins>-->
<!--			<plugin>-->
<!--				<groupId>org.apache.maven.plugins</groupId>-->
<!--				<artifactId>maven-war-plugin</artifactId>-->
<!--				<version>2.6</version>-->
<!--				<configuration>-->
<!--					<webXml>WebContent/WEB-INF/web.xml</webXml>-->
<!--				</configuration>-->
<!--			</plugin>-->
<!--		</plugins>-->
<!--		<finalName>rekognition-service</finalName>-->
<!--	</build>-->
	<build>
		<finalName>${project.artifactId}</finalName>
	</build>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>com.stpl.tech.kettle.crm</groupId>
			<artifactId>kettle-crm</artifactId>
			<version>4.1.0-SNAPSHOT</version>
		</dependency>-->
		<dependency>
			<groupId>com.stpl.tech.kettle.master</groupId>
			<artifactId>master-core</artifactId>
			<version>6.2.41</version>
		</dependency>
		<dependency>
			<groupId>com.stpl.tech.kettle.transaction</groupId>
			<artifactId>data-model</artifactId>
			<version>6.2.41</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-rekognition</artifactId>
			<version>1.12.228</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-core</artifactId>
			<version>1.12.228</version>
		</dependency>
		<dependency>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
		    <groupId>javax.servlet</groupId>
		    <artifactId>javax.servlet-api</artifactId>
		    <scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>net.bull.javamelody</groupId>
			<artifactId>javamelody-spring-boot-starter</artifactId>
			<version>1.90.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
			<scope>runtime</scope>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-config</artifactId>
			<version>3.1.8</version>
		</dependency>
	</dependencies>
</project>
