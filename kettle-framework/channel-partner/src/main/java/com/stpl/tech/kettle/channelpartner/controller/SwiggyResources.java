package com.stpl.tech.kettle.channelpartner.controller;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyCancelRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyCancelResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyRiderStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.SwiggyRiderStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.OrderRejectionData;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderReject;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.ConnectCustomerObject;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyCustomerConnect;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyHolidaySlotData;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.net.URISyntaxException;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SWIGGY_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + SWIGGY_ROOT_CONTEXT) // 'v1/swiggy'
public class SwiggyResources extends SwiggyOrderAbstractResource {

    private static final Logger LOG = LoggerFactory.getLogger(SwiggyResources.class);

    @Autowired
    private SwiggyService swiggyService;

    @Autowired
    private MasterDataCache masterDataCache ;

    @RequestMapping(method = RequestMethod.POST, value = "order/add", produces = MediaType.APPLICATION_JSON)
    public SwiggyOrderResponse addSwiggyOrder(@RequestBody Object request) throws URISyntaxException, ChannelPartnerException {
        LOG.info("Request to add swiggy order : " + new Gson().toJson(request));
        String data = new Gson().toJson(request);
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SwiggyOrderRequest order = null;
		try {
			order = mapper.readValue(data, SwiggyOrderRequest.class);
            return addSwiggyOrder(order);
			// Use below line for camel case request parsing while debugging
			// order = new Gson().fromJson(data, SwiggyOrderRequest.class);
//           Moved this code to an abstract class --------->
			/*if (order.getOutletId().equalsIgnoreCase(ChannelPartnerUtils.getSwiggyTestOutletId())) {
				return swiggyService.addSwiggyOrderInDev(request);
			} else {
				SwiggyOrderResponse response = swiggyService.addSwiggyOrder(order, false);
				LOG.info("Request to add swiggy order {}: {}", order.getOrderId(), new Gson().toJson(response));
				return response;
			}*/
		} catch (Exception e) {
            e.printStackTrace();
        }
        order = new SwiggyOrderRequest();
        SwiggyOrderResponse response = new SwiggyOrderResponse();
        response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
        response.setSwiggyOrderId(Long.valueOf(order.getOrderId()));
        response.setExternalOrderId(order.getOrderId());
        response.setStatusMessage("Failed");
        response.setStatusCode(101);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/cancel", produces = MediaType.APPLICATION_JSON)
    public SwiggyCancelResponse cancelSwiggyOrder(@RequestBody SwiggyCancelRequest request) throws ChannelPartnerException {
        LOG.info("Request to cancel swiggy order : " + new Gson().toJson(request));
        return swiggyService.cancelSwiggyOrder(request, false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/rider/status", produces = MediaType.APPLICATION_JSON)
    public SwiggyRiderStatusResponse updateSwiggyOrderDeliveryStatus(@RequestBody SwiggyRiderStatusRequest request) {
        LOG.info("Request to get swiggy order rider status : " + new Gson().toJson(request));
        return swiggyService.updateSwiggyOrderDeliveryStatus(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "holiday/slot", produces = MediaType.APPLICATION_JSON)
    public SwiggyCancelResponse addSwiggyHolidaySlot(@RequestBody SwiggyHolidaySlotData request) {
        LOG.info("Request to add swiggy holiday slot : " + new Gson().toJson(request));
        return swiggyService.addSwiggyHolidaySlot(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/add/test", produces = MediaType.APPLICATION_JSON)
    public SwiggyOrderResponse addSwiggyTestOrder(@RequestBody Object request)
        throws URISyntaxException, ChannelPartnerException {
        LOG.info("Request to add swiggy test order : " + new Gson().toJson(request));
        String data = new Gson().toJson(request);
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SwiggyOrderRequest order = null;
        try {
            order = mapper.readValue(data, SwiggyOrderRequest.class);
            return swiggyService.addSwiggyOrder(order, false);
        } catch (IOException e) {
            e.printStackTrace();
        }
        order = new SwiggyOrderRequest();
        SwiggyOrderResponse response = new SwiggyOrderResponse();
        response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
        response.setSwiggyOrderId(Long.valueOf(order.getOrderId()));
        response.setExternalOrderId(order.getOrderId());
        response.setStatusMessage("Failed");
        response.setStatusCode(101);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/add/dev/test", produces = MediaType.APPLICATION_JSON)
    public SwiggyOrderResponse addSwiggyDevTestingOrder(@RequestBody Object request) throws URISyntaxException, ChannelPartnerException {
        LOG.info("Request to add dev testing swiggy order : " + new Gson().toJson(request));
        String data = new Gson().toJson(request);
        SwiggyOrderRequest order = null;
        try {
            order = new Gson().fromJson(data, SwiggyOrderRequest.class);
            return swiggyService.addSwiggyOrder(order, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        order = new SwiggyOrderRequest();
        SwiggyOrderResponse response = new SwiggyOrderResponse();
        response.setTimestamp(ChannelPartnerUtils.getCurrentTimestamp());
        response.setSwiggyOrderId(Long.valueOf(order.getOrderId()));
        response.setExternalOrderId(order.getOrderId());
        response.setStatusMessage("Failed");
        response.setStatusCode(101);
        return response;
    }
    @RequestMapping(method = RequestMethod.POST, value = "update/swiggy/delivery/timing", produces = MediaType.APPLICATION_JSON)
    public void updateCafeDeliveryTimingOnSwiggy(@RequestBody CafeTimingChangeRequest cafeTimingChangeRequest){
        LOG.info("Changing Cafe Delivery Time");
        swiggyService.updateCafeDeliveryTimeSwiggy(cafeTimingChangeRequest);
    }

    @PostMapping(value = "order-reject",consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    public SwiggyOrderReject orderReject(@RequestBody OrderRejectionData orderRejectionData) {
        LOG.info("Getting Rejection Reason");
        return swiggyService.orderReject(orderRejectionData.getSwiggyRejectOrderDetail(), orderRejectionData.getRejectionMetadata(), orderRejectionData.getReasonMetaData());
    }

    @PostMapping(value = "customer-connect",consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    public SwiggyCustomerConnect customerConnect(@RequestBody ConnectCustomerObject connectCustomerObject){
        LOG.info("Connecting to customer");
        return swiggyService.connectWithCustomer(connectCustomerObject);

    }
}

