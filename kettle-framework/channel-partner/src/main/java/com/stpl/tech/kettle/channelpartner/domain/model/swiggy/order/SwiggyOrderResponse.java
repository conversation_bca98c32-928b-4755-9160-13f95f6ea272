
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.Date;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "timestamp",
    "status_code",
    "status_message",
    "external_order_id",
    "swiggy_order_id",
    "description"
})
public class SwiggyOrderResponse {

    @JsonProperty("timestamp")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date timestamp;
    @JsonProperty("status_code")
    private Integer statusCode;
    @JsonProperty("status_message")
    private String statusMessage;
    @JsonProperty("external_order_id")
    private String externalOrderId;
    @JsonProperty("swiggy_order_id")
    private Long swiggyOrderId;
    @JsonProperty("description")
    private String description;

    @JsonProperty("timestamp")
    public Date getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    @JsonProperty("status_code")
    public Integer getStatusCode() {
        return statusCode;
    }

    @JsonProperty("status_code")
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @JsonProperty("status_message")
    public String getStatusMessage() {
        return statusMessage;
    }

    @JsonProperty("status_message")
    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    @JsonProperty("external_order_id")
    public String getExternalOrderId() {
        return externalOrderId;
    }

    @JsonProperty("external_order_id")
    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    @JsonProperty("swiggy_order_id")
    public Long getSwiggyOrderId() {
        return swiggyOrderId;
    }

    @JsonProperty("swiggy_order_id")
    public void setSwiggyOrderId(Long swiggyOrderId) {
        this.swiggyOrderId = swiggyOrderId;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("timestamp", timestamp).append("statusCode", statusCode).append("statusMessage", statusMessage).append("externalOrderId", externalOrderId).append("swiggyOrderId", swiggyOrderId).append("description", description).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(statusCode).append(timestamp).append(description).append(externalOrderId).append(swiggyOrderId).append(statusMessage).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof SwiggyOrderResponse) == false) {
            return false;
        }
        SwiggyOrderResponse rhs = ((SwiggyOrderResponse) other);
        return new EqualsBuilder().append(statusCode, rhs.statusCode).append(timestamp, rhs.timestamp).append(description, rhs.description).append(externalOrderId, rhs.externalOrderId).append(swiggyOrderId, rhs.swiggyOrderId).append(statusMessage, rhs.statusMessage).isEquals();
    }

}
