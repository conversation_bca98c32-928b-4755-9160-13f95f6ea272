package com.stpl.tech.kettle.channelpartner.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMetadataFetchService;
import com.stpl.tech.kettle.channelpartner.domain.model.BrandProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.FilteredProductsVO;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerMetadataDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMetadata;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMetadataKey;

@Service
public class PartnerMetadataFetchServiceImpl implements PartnerMetadataFetchService {

    @Autowired
    private PartnerMetadataDao partnerMetadataDao;

    @Override
    public List<Integer> getPartnerProductFilter(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productFilters = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_FILTER_LIST);
            JsonArray tagsArray = new Gson().fromJson(productFilters, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    try {
                        FilteredProductsVO mapping = new Gson().fromJson(item, FilteredProductsVO.class);
                        if (mapping.getBrandId().equals(brandId)) {
                            return mapping.getProductIds();
                        }
                    } catch (Exception e) {
                        return null;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public List<String> getPartnerBogoProductsforOffer(Integer partnerId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productList = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.BOGO_PRODUCT_LIST);
            JsonArray productIds = new Gson().fromJson(productList, JsonArray.class);
            if (productIds != null) {
                List<String> products = new ArrayList<>();
                for (JsonElement item : productIds) {
                    products.add(String.valueOf(item.getAsInt()));
                }
                return products;
            }
        }
        return null;
    }

    @Override
    public List<ProductTagsMappings> getPartnerProductTagsMappings(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_TAGS_MAPPINGS);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    BrandProductTagsMappings mappings = new Gson().fromJson(item.toString(), BrandProductTagsMappings.class);
                    if (mappings.getBrandId().equals(brandId)) {
                        return mappings.getMappings();
                    }
                }
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<ProductTagsMappings> getPartnerProductMeatTagsMappings(Integer partnerId, Integer brandId) {
        List<PartnerMetadata> partnerMetadataList = partnerMetadataDao.findAllByKettlePartnerId(partnerId);
        if (partnerMetadataList != null && !partnerMetadataList.isEmpty()) {
            String productTags = partnerMetadataList.get(0).getMetadata().get(PartnerMetadataKey.PRODUCT_MEAT_TAGS_MAPPINGS);
            JsonArray tagsArray = new Gson().fromJson(productTags, JsonArray.class);
            if (tagsArray != null) {
                for (JsonElement item : tagsArray) {
                    BrandProductTagsMappings mappings = new Gson().fromJson(item.toString(), BrandProductTagsMappings.class);
                    if (mappings.getBrandId().equals(brandId)) {
                        return mappings.getMappings();
                    }
                }
            }
        }
        return new ArrayList<>();
    }


}
