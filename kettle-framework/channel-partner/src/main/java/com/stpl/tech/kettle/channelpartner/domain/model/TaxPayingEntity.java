package com.stpl.tech.kettle.channelpartner.domain.model;

public enum TaxPayingEntity {
    SELF("DEFAULT", "RESTAURANT", "VENDOR"),
    PARTNER("SOURCE_TAX", "SWIGGY", "SWIGGY");

    private final String zomato;
    private final String swiggy;
    private final String swiggyNew;

    TaxPayingEntity(String zomato, String swiggy, String swiggyNew) {
        this.zomato = zomato;
        this.swiggy = swiggy;
        this.swiggyNew = swiggyNew;
    }

    public String getZomato() {
        return zomato;
    }

    public String getSwiggy() {
        return swiggy;
    }

	public String getSwiggyNew() {
		return swiggyNew;
	}


    
}
