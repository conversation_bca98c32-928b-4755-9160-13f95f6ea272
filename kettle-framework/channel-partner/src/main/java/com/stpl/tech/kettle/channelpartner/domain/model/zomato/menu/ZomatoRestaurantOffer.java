
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "offer_id",
    "start_date",
    "end_date",
    "timings",
    "offer_type",
    "discount_type",
    "min_order_amount",
    "discount_value",
    "is_active"
})
public class ZomatoRestaurantOffer {

    @JsonProperty("offer_id")
    private String offerId;
    @JsonProperty("start_date")
    private String startDate;
    @JsonProperty("end_date")
    private String endDate;
    @JsonProperty("timings")
    private List<Timing> timings = null;
    @JsonProperty("offer_type")
    private String offerType;
    @JsonProperty("discount_type")
    private String discountType;
    @JsonProperty("min_order_amount")
    private Integer minOrderAmount;
    @JsonProperty("discount_value")
    private Integer discountValue;
    @JsonProperty("is_active")
    private Integer isActive;

    @JsonProperty("offer_id")
    public String getOfferId() {
        return offerId;
    }

    @JsonProperty("offer_id")
    public void setOfferId(String offerId) {
        this.offerId = offerId;
    }

    @JsonProperty("start_date")
    public String getStartDate() {
        return startDate;
    }

    @JsonProperty("start_date")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @JsonProperty("end_date")
    public String getEndDate() {
        return endDate;
    }

    @JsonProperty("end_date")
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @JsonProperty("timings")
    public List<Timing> getTimings() {
        return timings;
    }

    @JsonProperty("timings")
    public void setTimings(List<Timing> timings) {
        this.timings = timings;
    }

    @JsonProperty("offer_type")
    public String getOfferType() {
        return offerType;
    }

    @JsonProperty("offer_type")
    public void setOfferType(String offerType) {
        this.offerType = offerType;
    }

    @JsonProperty("discount_type")
    public String getDiscountType() {
        return discountType;
    }

    @JsonProperty("discount_type")
    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    @JsonProperty("min_order_amount")
    public Integer getMinOrderAmount() {
        return minOrderAmount;
    }

    @JsonProperty("min_order_amount")
    public void setMinOrderAmount(Integer minOrderAmount) {
        this.minOrderAmount = minOrderAmount;
    }

    @JsonProperty("discount_value")
    public Integer getDiscountValue() {
        return discountValue;
    }

    @JsonProperty("discount_value")
    public void setDiscountValue(Integer discountValue) {
        this.discountValue = discountValue;
    }

    @JsonProperty("is_active")
    public Integer getIsActive() {
        return isActive;
    }

    @JsonProperty("is_active")
    public void setIsActive(Integer isActive) {
        this.isActive = isActive;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("offerId", offerId).append("startDate", startDate).append("endDate", endDate).append("timings", timings).append("offerType", offerType).append("discountType", discountType).append("minOrderAmount", minOrderAmount).append("discountValue", discountValue).append("isActive", isActive).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(isActive).append(startDate).append(offerType).append(discountType).append(timings).append(endDate).append(discountValue).append(minOrderAmount).append(offerId).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoRestaurantOffer) == false) {
            return false;
        }
        ZomatoRestaurantOffer rhs = ((ZomatoRestaurantOffer) other);
        return new EqualsBuilder().append(isActive, rhs.isActive).append(startDate, rhs.startDate).append(offerType, rhs.offerType).append(discountType, rhs.discountType).append(timings, rhs.timings).append(endDate, rhs.endDate).append(discountValue, rhs.discountValue).append(minOrderAmount, rhs.minOrderAmount).append(offerId, rhs.offerId).isEquals();
    }

}
