package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="MONTHLY_AOV_METADATA")
public class MonthlyAOVMetadata implements Serializable {
    private static final long serialVersionUID = 3785961348862031906L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AOV_MAPPING_ID")
    private Long aovMappingId;
    @Column(name="YEAR")
    private Integer year;
    @Column(name="MONTH")
    private Integer month;
    @Column(name="BRAND_ID")
    private Integer brandId;
    @Column(name="AOV")
    private BigDecimal aov;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="CALCULATION_START_TIME")
    private Date calculationStartTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="CALCULATED_AT")
    private Date calculatedAt;
}
