package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "vendorEntityId",
        "title",
        "maxSelections",
        "minSelections",
        "maxSelectionsPerItem",
        "discountValue",
        "selectionEntities"
})
public class ZomatoComboSelection {

    @JsonProperty("vendorEntityId")
    private String vendorEntityId;
    @JsonProperty("title")
    private String title;
    @JsonProperty("maxSelections")
    private int maxSelections;
    @JsonProperty("minSelections")
    private int minSelections;
    @JsonProperty("maxSelectionsPerItem")
    private int maxSelectionsPerItem;
    @JsonProperty("discountValue")
    private float discountValue;
    @JsonProperty("selectionEntities")
    private List<ZomatoComboSelectionEntities> selectionEntities;

    @JsonProperty("vendorEntityId")
    public String getVendorEntityId() {
        return vendorEntityId;
    }

    @JsonProperty("vendorEntityId")
    public void setVendorEntityId(String vendorEntityId) {
        this.vendorEntityId = vendorEntityId;
    }

    @JsonProperty("title")
    public String getTitle() {
        return title;
    }

    @JsonProperty("title")
    public void setTitle(String title) {
        this.title = title;
    }

    @JsonProperty("maxSelections")
    public int getMaxSelections() {
        return maxSelections;
    }

    @JsonProperty("maxSelections")
    public void setMaxSelections(int maxSelections) {
        this.maxSelections = maxSelections;
    }

    @JsonProperty("minSelections")
    public int getMinSelections() {
        return minSelections;
    }

    @JsonProperty("minSelections")
    public void setMinSelections(int minSelections) {
        this.minSelections = minSelections;
    }

    @JsonProperty("maxSelectionsPerItem")
    public int getMaxSelectionsPerItem() {
        return maxSelectionsPerItem;
    }

    @JsonProperty("maxSelectionsPerItem")
    public void setMaxSelectionsPerItem(int maxSelectionsPerItem) {
        this.maxSelectionsPerItem = maxSelectionsPerItem;
    }

    @JsonProperty("discountValue")
    public float getDiscountValue() {
        return discountValue;
    }

    @JsonProperty("discountValue")
    public void setDiscountValue(float discountValue) {
        this.discountValue = discountValue;
    }

    @JsonProperty("selectionEntities")
    public List<ZomatoComboSelectionEntities> getSelectionEntities() {
        return selectionEntities;
    }

    @JsonProperty("selectionEntities")
    public void setSelectionEntities(List<ZomatoComboSelectionEntities> selectionEntities) {
        this.selectionEntities = selectionEntities;
    }
}