package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "day",
        "slots",
        "active"
})
public class ZomatoTimings {

    @JsonProperty("day")
    String day;

    @JsonProperty("slots")
    List<ZomatoSlots> slots;

    @JsonProperty("active")
    boolean active;

    @JsonProperty("day")
    public String getDay() {
        return day;
    }

    @JsonProperty("day")
    public void setDay(String day) {
        this.day = day;
    }

    @JsonProperty("slots")
    public List<ZomatoSlots> getSlots() {
        return slots;
    }

    @JsonProperty("slots")
    public void setSlots(List<ZomatoSlots> slots) {
        this.slots = slots;
    }

    @JsonProperty("active")
    public boolean isActive() {
        return active;
    }

    @JsonProperty("active")
    public void setActive(boolean active) {
        this.active = active;
    }
}
