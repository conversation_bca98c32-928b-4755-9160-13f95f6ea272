package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
	"vendorEntityId",
	"name",
	"max",
	"displayName",
	"min",
	"variants"
})
public class ZomatoModifierGroups {
	
	@JsonProperty("vendorEntityId")
    private String vendorEntityId;
	@JsonProperty("name")
    private String name;
	@JsonProperty("max")
    private Integer max;
	@JsonProperty("displayName")
    private String displayName;
	@JsonProperty("min")
    private Integer min;
	@JsonProperty("variants")
    private List<ZomatoModifierVariants> variants;
	@JsonProperty("kind")
	private String kind;
	
	@JsonProperty("name")
	public String getName() {
		return name;
	}
	
	@JsonProperty("name")
	public void setName(String name) {
		this.name = name;
	}
	
	@JsonProperty("max")
	public Integer getMax() {
		return max;
	}
	
	@JsonProperty("max")
	public void setMax(Integer max) {
		this.max = max;
	}
	
	@JsonProperty("displayName")
	public String getDisplayName() {
		return displayName;
	}
	
	@JsonProperty("displayName")
	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}
	
	@JsonProperty("min")
	public Integer getMin() {
		return min;
	}
	
	@JsonProperty("min")
	public void setMin(Integer min) {
		this.min = min;
	}
	
	@JsonProperty("vendorEntityId")
	public String getVendorEntityId() {
		return vendorEntityId;
	}
	
	@JsonProperty("vendorEntityId")
	public void setVendorEntityId(String vendorEntityId) {
		this.vendorEntityId = vendorEntityId;
	}
	
	@JsonProperty("variants")
	public List<ZomatoModifierVariants> getVariants() {
		return variants;
	}
	
	@JsonProperty("variants")
	public void setVariants(List<ZomatoModifierVariants> variants) {
		this.variants = variants;
	}

	@JsonProperty("kind")
	public String getKind() {
		return kind;
	}

	@JsonProperty("kind")
	public void setKind(String kind) {
		this.kind = kind;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		ZomatoModifierGroups that = (ZomatoModifierGroups) o;

		return new EqualsBuilder()
			.append(vendorEntityId, that.vendorEntityId)
			.append(name, that.name)
			.append(max, that.max)
			.append(displayName, that.displayName)
			.append(min, that.min)
			.append(variants, that.variants)
			.append(kind, that.kind)
			.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
			.append(vendorEntityId)
			.append(name)
			.append(max)
			.append(displayName)
			.append(min)
			.append(variants)
			.append(kind)
			.toHashCode();
	}

	@Override
	public String toString() {
		return "ZomatoModifierGroups{" +
			"vendorEntityId='" + vendorEntityId + '\'' +
			", name='" + name + '\'' +
			", max=" + max +
			", displayName='" + displayName + '\'' +
			", min=" + min +
			", variants=" + variants +
			", kind='" + kind + '\'' +
			'}';
	}
}
