package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.kettle.domain.model.OrderMetadata;

public class TransactionDifferenceMetadata extends OrderMetadata {

    //type - item, cart
    private String t;
    //id - product id in case of order, nul in case of cart
    private Integer i;
    //parameter which has difference e.g. PRICE
    private String p;
    // expected value
    private Float e;
    //actual value
    private Float a;

    public TransactionDifferenceMetadata(String t, String p, Integer i, Float e, Float a) {
        this.t = t;
        this.p = p;
        this.i = i;
        this.e = e;
        this.a = a;
    }

    public String getT() {
        return t;
    }

    public void setT(String t) {
        this.t = t;
    }

    public Integer getI() {
        return i;
    }

    public void setI(Integer i) {
        this.i = i;
    }

    public String getP() {
        return p;
    }

    public void setP(String p) {
        this.p = p;
    }

    public Float getE() {
        return e;
    }

    public void setE(Float e) {
        this.e = e;
    }

    public Float getA() {
        return a;
    }

    public void setA(Float a) {
        this.a = a;
    }
}
