package com.stpl.tech.kettle.channelpartner.domain.model.zomato;


import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.builder.HashCodeBuilder;

@Getter
@Setter
@Builder
public class BrandMonthYearKey {
    Integer month;
    Integer year;
    Integer brandId;

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(month)
                .append(year)
                .append(brandId)
                .toHashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        BrandMonthYearKey that = (BrandMonthYearKey) obj;
        return month != null && year != null && brandId != null &&
                that.month != null && that.brandId != null && that.year != null &&
                month.intValue() == that.month.intValue() &&
                brandId.intValue() == that.brandId.intValue() &&
                year.intValue() == that.year.intValue();
    }

    @Override
    public String toString() {
        return "BrandMonthYearKey{" +
                "month=" + month +
                ", year=" + year +
                ", brandId=" + brandId +
                '}';
    }
}
