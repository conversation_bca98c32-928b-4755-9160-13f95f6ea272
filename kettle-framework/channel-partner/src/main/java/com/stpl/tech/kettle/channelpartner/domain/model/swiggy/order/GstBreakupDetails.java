package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

public class GstBreakupDetails {

    @JsonProperty("total")
    private float total;
    @JsonProperty("packaging_charge_gst")
    private float packagingChargeGst;
    @JsonProperty("item_charge_gst")
    private float itemChargeGst;
    @JsonProperty("service_charge_gst")
    private float serviceChargeGst;

    @JsonProperty("total")
    public float getTotal() {
        return total;
    }

    @JsonProperty("total")
    public void setTotal(float total) {
        this.total = total;
    }

    @JsonProperty("packaging_charge_gst")
    public float getPackagingChargeGst() {
        return packagingChargeGst;
    }

    @JsonProperty("packaging_charge_gst")
    public void setPackagingChargeGst(float packagingChargeGst) {
        this.packagingChargeGst = packagingChargeGst;
    }

    @JsonProperty("item_charge_gst")
    public float getItemChargeGst() {
        return itemChargeGst;
    }

    @JsonProperty("item_charge_gst")
    public void setItemChargeGst(float itemChargeGst) {
        this.itemChargeGst = itemChargeGst;
    }

    @JsonProperty("service_charge_gst")
    public float getServiceChargeGst() {
        return serviceChargeGst;
    }

    @JsonProperty("service_charge_gst")
    public void setServiceChargeGst(float serviceChargeGst) {
        this.serviceChargeGst = serviceChargeGst;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        GstBreakupDetails that = (GstBreakupDetails) o;

        return new EqualsBuilder()
            .append(total, that.total)
            .append(packagingChargeGst, that.packagingChargeGst)
            .append(itemChargeGst, that.itemChargeGst)
            .append(serviceChargeGst, that.serviceChargeGst)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(total)
            .append(packagingChargeGst)
            .append(itemChargeGst)
            .append(serviceChargeGst)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "GstBreakupDetails{" +
            "total=" + total +
            ", packagingChargeGst=" + packagingChargeGst +
            ", itemChargeGst=" + itemChargeGst +
            ", serviceChargeGst=" + serviceChargeGst +
            '}';
    }
}
