package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.stpl.tech.kettle.domain.model.PartnerOrderStates;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "rider_name",
        "rider_phone_number"
})
public class ZomatoOrderStateData extends ZomatoOrderRiderData {

    private PartnerOrderStates state;

    public PartnerOrderStates getState() {
        return state;
    }

    public void setState(PartnerOrderStates state) {
        this.state = state;
    }
}
