package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.service.PartnerLocalityService;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerLocalityDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.UnitPartnerLocalityMappingDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerLocalityDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.UnitPartnerLocalityMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PartnerLocalityServiceImpl implements PartnerLocalityService {

    @Autowired
    private PartnerLocalityDao partnerLocalityDao;

    @Autowired
    private UnitPartnerLocalityMappingDao unitPartnerLocalityMappingDao;

    @Override
    public PartnerLocalityDetail getLocality(Integer kettlePartnerId, String locality, String city, String state, String country, String pinCode){
        return partnerLocalityDao.getPartnerLocalityDetailByKettlePartnerIdAndLocalityAndCityAndStateAndCountryAndPinCode(kettlePartnerId, locality, city, state, country, pinCode);
    }

    @Override
    public PartnerLocalityDetail addLocality(PartnerLocalityDetail partnerLocalityDetail){
        return partnerLocalityDao.save(partnerLocalityDetail);
    }

    @Override
    public UnitPartnerLocalityMapping getPartnerLocalityMapping(PartnerLocalityDetail partnerLocalityDetail){
        return unitPartnerLocalityMappingDao.getUnitPartnerLocalityMappingByPartnerLocalityDetail(partnerLocalityDetail);
    }

    @Override
    public UnitPartnerLocalityMapping addMapping(UnitPartnerLocalityMapping mapping){
        return unitPartnerLocalityMappingDao.save(mapping);
    }
}
