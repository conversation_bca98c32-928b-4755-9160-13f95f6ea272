package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import java.util.List;
import java.util.Map;

public class PartnerUpsellingSuperCombosProdIdDetail {

    private List<String> productsIds;
    private List<String> upsellingProdIds;
    private List<String> superCombosProdIds;
    private List<String> addOnProductIds;
    private Map<String, List<String>> productVariantsMap;

    public List<String> getProductsIds() {
        return productsIds;
    }

    public void setProductsIds(List<String> productsIds) {
        this.productsIds = productsIds;
    }

    public List<String> getUpsellingProdIds() {
        return upsellingProdIds;
    }

    public void setUpsellingProdIds(List<String> upsellingProdIds) {
        this.upsellingProdIds = upsellingProdIds;
    }

    public List<String> getSuperCombosProdIds() {
        return superCombosProdIds;
    }

    public void setSuperCombosProdIds(List<String> superCombosProdIds) {
        this.superCombosProdIds = superCombosProdIds;
    }

    public Map<String, List<String>> getProductVariantsMap() {
        return productVariantsMap;
    }

    public void setProductVariantsMap(Map<String, List<String>> productVariantsMap) {
        this.productVariantsMap = productVariantsMap;
    }

    public List<String> getAddOnProductIds() {
        return addOnProductIds;
    }

    public void setAddOnProductIds(List<String> addOnProductIds) {
        this.addOnProductIds = addOnProductIds;
    }
}
