package com.stpl.tech.kettle.channelpartner.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerUnitListVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuVersionData;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoUnitProductStockV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCatalogues;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoMenu;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.CafeMenuAutoPushDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerMenuAuditDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerMenuUpdateDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOfferDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerSingleServeMenuDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitMenuDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitMenuVersionMappingDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitProductMappingDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerUnitProductPricingDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuAuditHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerSingleServeMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuVersionMapping;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductMappingDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductPricingDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderReconciliationData;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.PartnerOrderReconciliationErrorData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.util.AppConstants;

@Service
public class PartnerMenuServiceImpl implements PartnerMenuService {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerMenuServiceImpl.class);

    @Autowired
    private PartnerMenuUpdateDao partnerMenuUpdateDao;

    @Autowired
    private PartnerOfferDao partnerOfferDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private PartnerUnitMenuDao partnerUnitMenuDao;

    @Autowired
    private RedisPublisher redisPublisher;

    @Autowired
    PartnerSingleServeMenuDao partnerSingleServeMenuDao;

    @Autowired
    private PartnerUnitProductMappingDao partnerUnitProductMappingDao;

    @Autowired
    private PartnerUnitMenuVersionMappingDao partnerUnitMenuVersionMappingDao;

    @Autowired
    private PartnerMenuAuditDao partnerMenuAuditDao;

    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @Autowired
    private PartnerUnitProductPricingDao partnerUnitProductPricingDao;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    CafeMenuAutoPushDao cafeMenuAutoPushDao;


    @Override
    public List<PartnerMenuAuditHistory> getMenuAuditHistory(Integer unitId, Integer partnerId,
                                                             Integer brandId, String menuType) {


        return partnerMenuAuditDao.findTop15ByUnitIdAndPartnerIdAndBrandIdAndMenuTypeOrderByAddTimeISTDesc(unitId, partnerId, brandId
            , menuType);

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PartnerMenuDetail getPartnerMenuDetail(UnitMenuAddVO event) {
        String region = null;
        if (event.getRegion() != null) {
            region = event.getRegion();
        } else {
            region = masterDataCache.getUnit(event.getUnitId()).getRegion();
        }
        List<PartnerMenuDetail> partnerMenuDetails = partnerMenuUpdateDao.findAllByActiveAndPartnerIdAndRegion(true, event.getKettlePartnerId(),
            region);
        if (partnerMenuDetails != null && !partnerMenuDetails.isEmpty()) {
            return partnerMenuDetails.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addPartnerMenuDetail(UnitMenuAddVO event) {
        String region = masterDataCache.getUnit(event.getUnitId()).getRegion();
        List<PartnerMenuDetail> partnerMenuUpdateDetails = partnerMenuUpdateDao.findAllByActiveAndPartnerIdAndRegion(true, event.getKettlePartnerId(),
            region);
        for (PartnerMenuDetail detail : partnerMenuUpdateDetails) {
            detail.setActive(false);
            partnerMenuUpdateDao.save(detail);
        }
        PartnerMenuDetail detail = new PartnerMenuDetail();
        detail.setActive(true);
        detail.setMenuData(event.getMenuRequest());
        detail.setPartnerId(event.getKettlePartnerId());
        detail.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(event.getKettlePartnerId()).getPartnerName());
        detail.setRegion(region);
        detail.setUpdateTime(ChannelPartnerUtils.getCurrentTimestamp());
        detail.setUpdateTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        partnerMenuUpdateDao.save(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PartnerUnitMenuDetail getActivePartnerUnitMenuDetail(Integer partnerId, Integer unitId, Integer brandId) {
        List<PartnerUnitMenuDetail> partnerUnitMenuDetails = partnerUnitMenuDao.findAllByActiveAndPartnerIdAndUnitId(true, partnerId,
            unitId);
        if (partnerUnitMenuDetails != null && !partnerUnitMenuDetails.isEmpty()) {
            for (PartnerUnitMenuDetail partnerUnitMenuDetail : partnerUnitMenuDetails) {
                if (partnerUnitMenuDetail.getBrandId() == null) {
                    partnerUnitMenuDetail.setBrandId(1); //default for chaayos
                }
                if (partnerUnitMenuDetail.getBrandId().equals(brandId)) {
                    return partnerUnitMenuDetail;
                }
            }
            return partnerUnitMenuDetails.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PartnerUnitMenuDetail getActivePartnerUnitMenuDetail(Integer partnerId, Integer unitId, Boolean isNew, Integer brandId) {
        List<PartnerUnitMenuDetail> partnerUnitMenuDetails = partnerUnitMenuDao.findAllByActiveAndPartnerIdAndUnitIdAndIsNew(true, partnerId,
            unitId, isNew);
        if (partnerUnitMenuDetails != null && !partnerUnitMenuDetails.isEmpty()) {
            for (PartnerUnitMenuDetail partnerUnitMenuDetail : partnerUnitMenuDetails) {
                if (partnerUnitMenuDetail.getBrandId() == null) {
                    partnerUnitMenuDetail.setBrandId(1); //default for chaayos
                }
                if (partnerUnitMenuDetail.getBrandId().equals(brandId)) {
                    return partnerUnitMenuDetail;
                }
            }
            return partnerUnitMenuDetails.get(0);
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addPartnerUnitMenuDetail(UnitMenuAddVO event) {
        String region = masterDataCache.getUnit(event.getUnitId()).getRegion();
        List<PartnerUnitMenuDetail> partnerMenuUpdateDetails = partnerUnitMenuDao.findAllByActiveAndPartnerIdAndUnitId(true, event.getKettlePartnerId(),
            event.getUnitId());
        for (PartnerUnitMenuDetail detail : partnerMenuUpdateDetails) {
            if (detail.getBrandId() == null) {
                detail.setBrandId(1); //default brand id is 1 from Chaayos
            }
            if (detail.getBrandId().equals(event.getBrandId())) {
                detail.setActive(false);
                partnerUnitMenuDao.save(detail);
            }
        }
        PartnerUnitMenuDetail detail = new PartnerUnitMenuDetail();
        detail.setActive(true);
        detail.setMenuData(event.getMenuRequest());
        detail.setCharges(event.getCharges());
        detail.setPartnerId(event.getKettlePartnerId());
        detail.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(event.getKettlePartnerId()).getPartnerName());
        detail.setRegion(region);
        detail.setUnitId(event.getUnitId());
        detail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        detail.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        detail.setEmployeeId(event.getEmployeeId());
        detail.setEmployeeName(masterDataCache.getEmployee(event.getEmployeeId()));
        detail.setNew(event.getNew());
        detail.setMenuType(event.getMenuType());
        detail.setBrandId(event.getBrandId());
        partnerUnitMenuDao.save(detail);
        /*if (detail.getPartnerName().equals("SWIGGY")) { //for SWIGGY
            if (!event.getNew()) {
                clearUnitProductMappingOfNewMenu(event);
                channelPartnerDataCache.loadSwiggyUnitProductMappings();
            }
            channelPartnerDataCache.clearSwiggyUnitProductMap(event.getUnitId());
        }*/
    }

    /*private void clearUnitProductMappingOfNewMenu(UnitMenuAddVO event) {
        List<PartnerUnitProductMappingDetail> partnerUnitProductMap = partnerUnitProductMappingDao.findAllByActiveAndPartnerIdAndUnitId(true,
            event.getKettlePartnerId(), event.getUnitId());
        for (PartnerUnitProductMappingDetail detail : partnerUnitProductMap) {
            if (detail.getBrandId() == null) {
                detail.setBrandId(1); //default brand id is 1 from Chaayos
            }
            if (detail.getBrandId().equals(event.getBrandId())) {
                detail.setActive(false);
                partnerUnitProductMappingDao.save(detail);
            }
        }

    }*/

    @Override
    public List<PartnerOfferDetail> getAllActiveOffersForPartnerAndUnit(Integer partnerId, Integer unitId) throws ChannelPartnerException {
        if (partnerId != null && channelPartnerDataCache.getPartnerCacheById().containsKey(partnerId)) {
            Date time = ChannelPartnerUtils.getCurrentTimestamp();
            return partnerOfferDao.getAllActiveOffersForPartnerAndUnit(partnerId, unitId, time);
        } else {
            throw new ChannelPartnerException("Partner id is invalid.");
        }
    }

    @Override
    public List<PartnerOfferDetail> getAllActiveOffersForPartnerAndUnits(PartnerUnitListVO request) throws ChannelPartnerException {
        Integer partnerId = request.getPartnerId();
        List<Integer> unitIds = request.getUnitIds();
        if (partnerId != null && channelPartnerDataCache.getPartnerCacheById().containsKey(partnerId)) {
            Date time = ChannelPartnerUtils.getCurrentTimestamp();
            return partnerOfferDao.getAllActiveOffersForPartnerAndUnits(partnerId, unitIds, time);
        } else {
            throw new ChannelPartnerException("Partner id is invalid.");
        }
    }

    @Override
    public List<PartnerOfferDetail> getActiveOffersForPartnerAndUnits(Integer unitId) throws ChannelPartnerException {
        Integer partnerId = 123;
        List<Integer> unitIds = new ArrayList<Integer>();
        unitIds.add(unitId);
        if (partnerId != null && channelPartnerDataCache.getPartnerCacheById().containsKey(partnerId)) {
            Date time = ChannelPartnerUtils.getCurrentTimestamp();
            return partnerOfferDao.getAllActiveOffersForPartnerAndUnits(partnerId, unitIds, time);
        } else {
            throw new ChannelPartnerException("Partner id is invalid.");
        }
    }

    @Override
    public PartnerOfferDetail addPartnerOfferForZomato(PartnerUnitListVO request) throws ChannelPartnerException {
        PartnerOfferDetail partnerOfferDetail = request.getOfferDetail();
        if (partnerOfferDetail != null) {
            Integer partnerId = partnerOfferDetail.getPartnerId();
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
            Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
            if (partnerId == null || partnerDetail == null) {
                throw new ChannelPartnerException("Partner id is invalid.");
            }
            if (request.getUnitIds().size() == 0) {
                throw new ChannelPartnerException("No unit selected.");
            }
            if (partnerOfferDetail.getStartDate() == null) {
                partnerOfferDetail.setStartDate(ChannelPartnerUtils.getCurrentTimestamp());
            }
            if (partnerOfferDetail.getEndDate() == null) {
                partnerOfferDetail.setEndDate(ChannelPartnerUtils.getNextDate(ChannelPartnerUtils.getCurrentTimestamp()));
            }
            if (partnerOfferDetail.getActive() == null) {
                partnerOfferDetail.setActive(false);
            }
            if (partnerOfferDetail.getOfferData() == null) {
                throw new ChannelPartnerException("Offer data is not valid.");
            }
            partnerOfferDetail.setActive(false);
            partnerOfferDetail.setPartnerName(partnerDetail.getPartnerName());
            partnerOfferDetail.setAddTime(currentTime);
            partnerOfferDetail.setUpdateTime(currentTime);
           partnerOfferDetail.setAddTimeIST(ChannelPartnerUtils.getTimeISTString(currentTime));
            partnerOfferDetail.setUpdateTimeIST(ChannelPartnerUtils.getTimeISTString(currentTime));// List<PartnerOfferDetail> partnerOfferDetails = getAllActiveOffersForPartnerAndUnits(request);
            for (Integer unitId : request.getUnitIds()) {
                partnerOfferDetail.setUnitId(unitId);
                partnerOfferDetail = partnerOfferDao.save(partnerOfferDetail);
               /* if (masterDataCache.getUnits().containsKey(unitId)) {
                    if(channelPartnerDataCache.getUnitToPartnerMap(unitId).contains(request.getBrandId())){
                        PartnerOfferDetail finalPartnerOfferDetail = partnerOfferDetail;
                        List<PartnerOfferDetail> unitOffers = partnerOfferDetails.stream().filter(partnerOfferDetail1 -> {
                            return partnerOfferDetail1.getUnitId().equals(unitId) &&
                                    partnerOfferDetail1.getStartDate().compareTo(finalPartnerOfferDetail.getStartDate()) <= 0;
                        }).collect(Collectors.toList());
                        if (unitOffers.isEmpty()) {
                            partnerOfferDetail.setUnitId(unitId);
                            partnerOfferDetail = partnerOfferDao.save(partnerOfferDetail);
                            if (partnerOfferDetail != null) {
                                //Menu update will be pushed manually
                                //Setting update event to queue
                                /*PartnerActionEvent event = new PartnerActionEvent();
                                event.setEventType(PartnerActionEventType.UPDATE_UNIT_MENU);
                                List<Integer> partnerIds = new ArrayList<>(1);
                                partnerIds.add(partnerId);
                                event.setPartnerIds(partnerIds);
                                event.setEventData(partnerOfferDetail.getUnitId());
                                try {
                                    partnerActionsQueueListener.add(event);
                                } catch (InterruptedException e) {
                                    LOG.error("error adding menu update because of offer added to partner action queue", e);
                                }
                            } else {
                                throw new ChannelPartnerException("Error adding offer detail to unit");
                            }
                        } else {
                            throw new ChannelPartnerException("There are other offers already running on "
                                    + masterDataCache.getUnit(unitId).getName() + " from " + partnerOfferDetail.getStartDate()
                                    + " to " + partnerOfferDetail.getEndDate());
                        }
                    }
                } */
            }
            return partnerOfferDetail;
        } else {
            throw new ChannelPartnerException("Request is invalid.");
        }
    }

    @Override
    public PartnerOfferDetail addPartnerOffer(PartnerUnitListVO request) throws ChannelPartnerException {
        PartnerOfferDetail partnerOfferDetail = request.getOfferDetail();
        if (partnerOfferDetail != null) {
            Integer partnerId = partnerOfferDetail.getPartnerId();
            PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
            Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
            if (partnerId == null || partnerDetail == null) {
                throw new ChannelPartnerException("Partner id is invalid.");
            }
            if (request.getUnitIds().size() == 0) {
                throw new ChannelPartnerException("No unit selected.");
            }
            if (partnerOfferDetail.getStartDate() == null) {
                partnerOfferDetail.setStartDate(ChannelPartnerUtils.getCurrentTimestamp());
            }
            if (partnerOfferDetail.getEndDate() == null) {
                partnerOfferDetail.setEndDate(ChannelPartnerUtils.getNextDate(ChannelPartnerUtils.getCurrentTimestamp()));
            }
            if (partnerOfferDetail.getActive() == null) {
                partnerOfferDetail.setActive(false);
            }
            if (partnerOfferDetail.getOfferData() == null) {
                throw new ChannelPartnerException("Offer data is not valid.");
            }
            partnerOfferDetail.setActive(false);
            partnerOfferDetail.setPartnerName(partnerDetail.getPartnerName());
            partnerOfferDetail.setAddTime(currentTime);
            partnerOfferDetail.setUpdateTime(currentTime);
            partnerOfferDetail.setAddTimeIST(ChannelPartnerUtils.getTimeISTString(currentTime));
            partnerOfferDetail.setUpdateTimeIST(ChannelPartnerUtils.getTimeISTString(currentTime));
            List<PartnerOfferDetail> partnerOfferDetails = getAllActiveOffersForPartnerAndUnits(request);
            for (Integer unitId : request.getUnitIds()) {
                partnerOfferDetail.setUnitId(unitId);
                partnerOfferDetail = partnerOfferDao.save(partnerOfferDetail);
                if (masterDataCache.getUnits().containsKey(unitId)) {
                    boolean mappingValid = masterDataCache.getActiveUnitChannelPartnerMapping().stream().anyMatch(unitChannelPartnerMapping ->
                        unitChannelPartnerMapping.getChannelPartner().getId() == request.getPartnerId() && unitChannelPartnerMapping.getUnit().getId() == unitId);
                    if (mappingValid) {
                        PartnerOfferDetail finalPartnerOfferDetail = partnerOfferDetail;
                        List<PartnerOfferDetail> unitOffers = partnerOfferDetails.stream().filter(partnerOfferDetail1 -> {
                            return partnerOfferDetail1.getUnitId().equals(unitId) &&
                                partnerOfferDetail1.getStartDate().compareTo(finalPartnerOfferDetail.getStartDate()) <= 0;
                        }).collect(Collectors.toList());
                        if (unitOffers.isEmpty()) {
                            partnerOfferDetail.setUnitId(unitId);
                            partnerOfferDetail = partnerOfferDao.save(partnerOfferDetail);
                            if (partnerOfferDetail != null) {
                                //Menu update will be pushed manually
                                //Setting update event to queue
                                /*PartnerActionEvent event = new PartnerActionEvent();
                                event.setEventType(PartnerActionEventType.UPDATE_UNIT_MENU);
                                List<Integer> partnerIds = new ArrayList<>(1);
                                partnerIds.add(partnerId);
                                event.setPartnerIds(partnerIds);
                                event.setEventData(partnerOfferDetail.getUnitId());
                                try {
                                    partnerActionsQueueListener.add(event);
                                } catch (InterruptedException e) {
                                    LOG.error("error adding menu update because of offer added to partner action queue", e);
                                }*/
                            } else {
                                throw new ChannelPartnerException("Error adding offer detail to unit");
                            }
                        } else {
                            throw new ChannelPartnerException("There are other offers already running on "
                                + masterDataCache.getUnit(unitId).getName() + " from " + partnerOfferDetail.getStartDate()
                                + " to " + partnerOfferDetail.getEndDate());
                        }
                    }
                }
            }
            return partnerOfferDetail;
        } else {
            throw new ChannelPartnerException("Request is invalid.");
        }
    }


    @Override
    public Boolean deactivatePartnerOffer(String partnerOfferId) throws ChannelPartnerException {
        return updatePartnerOfferStatus(partnerOfferId, false);
    }

    @Override
    public Boolean activatePartnerOffer(String partnerOfferId) throws ChannelPartnerException {
        return updatePartnerOfferStatus(partnerOfferId, true);
    }

    private Boolean updatePartnerOfferStatus(String partnerOfferId, Boolean isActivate) throws ChannelPartnerException {
        if (partnerOfferId != null) {
            Optional<PartnerOfferDetail> detailData = partnerOfferDao.findById(partnerOfferId);
            if (!detailData.isPresent()) {
                throw new ChannelPartnerException("Partner detail is invalid.");
            }
            PartnerOfferDetail detail = detailData.get();
            detail.setActive(isActivate);
            detail = partnerOfferDao.save(detail);
            if (detail != null) {
                //Setting update event to queue
                PartnerActionEvent event = new PartnerActionEvent();
                event.setEventType(PartnerActionEventType.UPDATE_UNIT_MENU);
                List<Integer> partnerIds = new ArrayList<>(1);
                partnerIds.add(detail.getPartnerId());
                event.setPartnerIds(partnerIds);
                event.setEventData(detail.getUnitId());
                if (channelPartnerDataCache.getPartnerCacheById().containsKey(detail.getPartnerId())) {
                    redisPublisher.publish(channelPartnerDataCache.getPartnerCacheById().get(detail.getPartnerId()).getPartnerName(), new Gson().toJson(event));
                } else {
                    LOG.error("Error publishing UPDATE_UNIT_MENU event: partner id " + detail.getPartnerId() + " is not valid.");
                }
                return true;
            } else {
                throw new ChannelPartnerException("Error updating offer status.");
            }
        } else {
            throw new ChannelPartnerException("Request is invalid.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addSingleServeMenuDetail(UnitMenuAddVO event) {
        List<PartnerSingleServeMenuDetail> partnerSingleServeMenu = partnerSingleServeMenuDao
            .findAllByPartnerIdAndUnitId(event.getKettlePartnerId(), event.getUnitId());
        for (PartnerSingleServeMenuDetail detail : partnerSingleServeMenu) {
            if (detail.getBrandId() == null) {
                detail.setBrandId(1); // default brand id is 1 from Chaayos
            }
            if (detail.getBrandId().equals(event.getBrandId())) {
                detail.setActive(false);
                partnerSingleServeMenuDao.save(detail);
            }
        }

        String region = masterDataCache.getUnit(event.getUnitId()).getRegion();
        PartnerSingleServeMenuDetail detail = new PartnerSingleServeMenuDetail();
        detail.setMenuData(event.getMenuRequest());
        detail.setPartnerId(event.getKettlePartnerId());
        detail.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(event.getKettlePartnerId()).getPartnerName());
        detail.setRegion(region);
        detail.setActive(true);
        detail.setUnitId(event.getUnitId());
        detail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        detail.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        detail.setEmployeeId(event.getEmployeeId());
        detail.setEmployeeName(masterDataCache.getEmployee(event.getEmployeeId()));
        detail.setBrandId(event.getBrandId());
        partnerSingleServeMenuDao.save(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addPartnerUnitMenuMappingForUpsellingProds(List<String> productIds, UnitMenuAddVO request, List<String> upsellingProductIds,
                                                           List<String> superComboproductIds, Map<String, List<String>> productVariantsMap, List<String> addOnProductIds) {
        List<PartnerUnitProductMappingDetail> partnerUnitProductMap = partnerUnitProductMappingDao.findAllByActiveAndPartnerIdAndUnitId(true,
            request.getKettlePartnerId(), request.getUnitId());
        for (PartnerUnitProductMappingDetail detail : partnerUnitProductMap) {
            if (detail.getBrandId() == null) {
                detail.setBrandId(1); //default brand id is 1 from Chaayos
            }
            if (detail.getBrandId().equals(request.getBrandId())) {
                detail.setActive(false);
                partnerUnitProductMappingDao.save(detail);
            }
        }
        PartnerUnitProductMappingDetail partnerUnitProductMappingDetail = new PartnerUnitProductMappingDetail();
        partnerUnitProductMappingDetail.setActive(true);
        partnerUnitProductMappingDetail.setPartnerId(request.getKettlePartnerId());
        partnerUnitProductMappingDetail.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId()).getPartnerName());
        partnerUnitProductMappingDetail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        partnerUnitProductMappingDetail.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        partnerUnitProductMappingDetail.setUnitId(request.getUnitId());
        partnerUnitProductMappingDetail.setBrandId(request.getBrandId());
        partnerUnitProductMappingDetail.setEmployeeId(request.getEmployeeId());
        partnerUnitProductMappingDetail.setEmployeeName(masterDataCache.getEmployee(request.getEmployeeId()));
        partnerUnitProductMappingDetail.setProductIds(productIds);
        partnerUnitProductMappingDetail.setUpsellingProductIds(upsellingProductIds);
        partnerUnitProductMappingDetail.setSuperCombosProductIds(superComboproductIds);
        partnerUnitProductMappingDetail.setProductVariantsMap(productVariantsMap);
        partnerUnitProductMappingDetail.setAddOnProductIds(addOnProductIds);
        partnerUnitProductMappingDao.save(partnerUnitProductMappingDetail);

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addPartnerUnitProductMappings(List<String> productIds, UnitMenuAddVO request, List<String> upsellingProdIds, List<String> comboProdIds) {
        List<PartnerUnitProductMappingDetail> partnerUnitProductMap = partnerUnitProductMappingDao.findAllByActiveAndPartnerIdAndUnitId(true,
                request.getKettlePartnerId(), request.getUnitId());
        for (PartnerUnitProductMappingDetail detail : partnerUnitProductMap) {
            if (detail.getBrandId() == null) {
                detail.setBrandId(1); //default brand id is 1 from Chaayos
            }
            if (detail.getBrandId().equals(request.getBrandId())) {
                detail.setActive(false);
                partnerUnitProductMappingDao.save(detail);
            }
        }
        PartnerUnitProductMappingDetail partnerUnitProductMappingDetail = new PartnerUnitProductMappingDetail();
        partnerUnitProductMappingDetail.setActive(true);
        partnerUnitProductMappingDetail.setPartnerId(request.getKettlePartnerId());
        partnerUnitProductMappingDetail.setPartnerName(channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId()).getPartnerName());
        partnerUnitProductMappingDetail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        partnerUnitProductMappingDetail.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        partnerUnitProductMappingDetail.setUnitId(request.getUnitId());
        partnerUnitProductMappingDetail.setBrandId(request.getBrandId());
        partnerUnitProductMappingDetail.setEmployeeId(request.getEmployeeId());
        partnerUnitProductMappingDetail.setEmployeeName(masterDataCache.getEmployee(request.getEmployeeId()));
        partnerUnitProductMappingDetail.setProductIds(productIds);
        partnerUnitProductMappingDetail.setUpsellingProductIds(upsellingProdIds);
        partnerUnitProductMappingDetail.setSuperCombosProductIds(comboProdIds);
        partnerUnitProductMappingDao.save(partnerUnitProductMappingDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<String> getSingleServeProductItems(ZomatoUnitProductStockV3 zomatoUnitProductStock, UnitPartnerBrandKey key) {
        Set<String> ssProductsIds = new HashSet<String>();
        List<PartnerSingleServeMenuDetail> partnerSingleServeMenu = partnerSingleServeMenuDao
            .findAllByActiveAndPartnerIdAndUnitIdAndBrandId(true, key.getPartnerId(), key.getUnitId(), key.getBrandId());
        if (partnerSingleServeMenu != null && !partnerSingleServeMenu.isEmpty()) {
            partnerSingleServeMenu.forEach(partnerMenuDetail -> {
                Object menuData = partnerMenuDetail.getMenuData();
                if (menuData != null) {
                    ZomatoMenu menuRequest = ChannelPartnerUtils.deserializeObject(new Gson().toJson(menuData), ZomatoMenu.class);
                    if (menuRequest != null) {
                        for (String product : zomatoUnitProductStock.getCatalogueVendorEntityIds()) {
                            for (ZomatoCatalogues catalogues : menuRequest.getCatalogues()) {
                                if (catalogues.getVendorEntityId().startsWith("ss_")) {
                                    String[] ids = catalogues.getVendorEntityId().split("_");
                                    if (product.equalsIgnoreCase(ids[1])) {
                                        ssProductsIds.add(catalogues.getVendorEntityId());
                                    }
                                }
                            }
                        }
                    }
                }
            });
        }
        return new ArrayList<>(ssProductsIds);
    }

    @Override
    public String getCurrentUnitMenuVersion(UnitMenuAddVO request, Integer unitId) {
        /*
         * List<PartnerUnitMenuVersionMapping> partnerUnitMenuMapping =
         * partnerUnitMenuVersionMappingDao
         * .findAllByUnitIdAndPartnerIdAndBrandIdAndMenuType(unitId,
         * request.getKettlePartnerId(), request.getBrandId(), request.getMenuType());
         */
        List<PartnerUnitMenuVersionMapping> partnerUnitMenuMapping = partnerUnitMenuVersionMappingDao.findAllByUnitIdAndPartnerIdAndBrandIdAndMenuType(unitId,
            request.getKettlePartnerId(),
            request.getBrandId(), request.getMenuType(), PageRequest.of(0, 1, Sort.by(Direction.DESC, "id"))).getContent();
        if (partnerUnitMenuMapping != null && partnerUnitMenuMapping.size() > 0) {
            return partnerUnitMenuMapping.get(0).getVersion();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String addUnitMenuMapping(String currentVersion, UnitMenuAddVO request, Integer unitId) throws ChannelPartnerException {
        String region = masterDataCache.getUnit(unitId).getRegion();
        String newVersion;
        if (currentVersion != null) {
            Integer version = Integer.parseInt(currentVersion.substring(1, currentVersion.length())) + 1;
            newVersion = "v" + version.toString();
        } else {
            newVersion = "v1";
        }
        List<PartnerUnitMenuVersionMapping> mappings = partnerUnitMenuVersionMappingDao.findAllByUnitIdAndPartnerIdAndBrandIdAndMenuTypeAndVersion(
            request.getUnitId(), request.getKettlePartnerId(), request.getBrandId(), request.getMenuType(), newVersion);
        if (mappings == null || !mappings.isEmpty()) {
            throw new ChannelPartnerException("Version already exists. Please check.");
        }
        PartnerUnitMenuVersionMapping partnerUnitMenuVersionMapping = new PartnerUnitMenuVersionMapping();
        partnerUnitMenuVersionMapping.setBrandId(request.getBrandId());
        partnerUnitMenuVersionMapping.setPartnerId(request.getKettlePartnerId());
        partnerUnitMenuVersionMapping.setPartnerName(
            channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId()).getPartnerName());
        partnerUnitMenuVersionMapping.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        partnerUnitMenuVersionMapping.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        partnerUnitMenuVersionMapping.setUnitId(unitId);
        partnerUnitMenuVersionMapping.setBrandId(request.getBrandId());
        partnerUnitMenuVersionMapping.setEmployeeId(request.getEmployeeId());
        partnerUnitMenuVersionMapping.setEmployeeName(masterDataCache.getEmployee(request.getEmployeeId()));
        partnerUnitMenuVersionMapping.setVersion(newVersion);
        partnerUnitMenuVersionMapping.setStatus(request.getStatus());
        partnerUnitMenuVersionMapping.setMenuType(request.getMenuType());
        partnerUnitMenuVersionMapping.setMenuSequenceId(request.getMenuSequenceId());
        partnerUnitMenuVersionMapping.setMenuSequenceName(request.getMenuSequenceName());
        partnerUnitMenuVersionMappingDao.save(partnerUnitMenuVersionMapping);

        PartnerUnitMenuDetail existing = partnerUnitMenuDao.findAllByUnitIdAndPartnerIdAndBrandIdAndVersionAndMenuType(request.getUnitId(), request.getKettlePartnerId(),
            request.getBrandId(), request.getVersion(), request.getMenuType());
        if (existing != null) {
            throw new ChannelPartnerException("Unit menu with same version already exists");
        }
        PartnerUnitMenuDetail detail = new PartnerUnitMenuDetail();
        detail.setActive(false);
        detail.setMenuData(request.getMenuRequest());
        detail.setCharges(request.getCharges());
        detail.setPartnerId(request.getKettlePartnerId());
        detail.setPartnerName(
            channelPartnerDataCache.getPartnerCacheById().get(request.getKettlePartnerId()).getPartnerName());
        detail.setRegion(region);
        detail.setUnitId(unitId);
        detail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        detail.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        detail.setEmployeeId(request.getEmployeeId());
        detail.setEmployeeName(masterDataCache.getEmployee(request.getEmployeeId()));
        detail.setNew(request.getNew());
        detail.setBrandId(request.getBrandId());
        detail.setVersion(newVersion);
        detail.setMenuType(request.getMenuType());
        detail.setMenuSequenceId(request.getMenuSequenceId());
        detail.setMenuSequenceName(request.getMenuSequenceName());
        partnerUnitMenuDao.save(detail);
        return newVersion;
    }

    @Override
    public List<UnitMenuVersionData> getUnitMenuVersionList(Integer unitId, Integer kettlePartnerId,
                                                            Integer brandId, String menuType) {
        Pageable pageable = PageRequest.of(0, 5);
        Page<PartnerUnitMenuVersionMapping> partnerUnitMenuMapping = partnerUnitMenuVersionMappingDao
            .findAllByUnitIdAndPartnerIdAndBrandIdAndMenuTypeOrderByIdDesc(unitId, kettlePartnerId,
                brandId, MenuType.fromValue(menuType), pageable);
        List<UnitMenuVersionData> unitMenuVersionData = new ArrayList<>();
        for (PartnerUnitMenuVersionMapping partnerUnitMenuVersion : partnerUnitMenuMapping) {
            UnitMenuVersionData unitMenuData = new UnitMenuVersionData();
            unitMenuData.setUnitId(partnerUnitMenuVersion.getUnitId());
            unitMenuData.setKettlePartnerId(partnerUnitMenuVersion.getPartnerId());
            unitMenuData.setBrandId(partnerUnitMenuVersion.getBrandId());
            unitMenuData.setVersion(partnerUnitMenuVersion.getVersion());
            unitMenuData.setStatus(partnerUnitMenuVersion.getStatus());
            unitMenuData.setMenuType(partnerUnitMenuVersion.getMenuType());
            unitMenuData.setMenuSequenceId(partnerUnitMenuVersion.getMenuSequenceId());
            unitMenuData.setMenuSequenceName(partnerUnitMenuVersion.getMenuSequenceName());
            unitMenuData.setAddTime(partnerUnitMenuVersion.getAddTime());
            if (partnerUnitMenuVersion.getAddTimeIST() != null) {
                unitMenuData.setAddTimeIST(partnerUnitMenuVersion.getAddTimeIST());
            }
            unitMenuVersionData.add(unitMenuData);
        }
        return unitMenuVersionData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void markUnitMenuVersionData(Integer unitId, Integer kettlePartnerId, Integer brandId,
                                        String version, String status, String menuType) {
        List<PartnerUnitMenuVersionMapping> partnerUnitMenuMapping = partnerUnitMenuVersionMappingDao
            .findAllByUnitIdAndPartnerIdAndBrandIdAndMenuType(unitId, kettlePartnerId,
                brandId, MenuType.fromValue(menuType));
        if (partnerUnitMenuMapping != null && !partnerUnitMenuMapping.isEmpty()) {
            for (PartnerUnitMenuVersionMapping partnerUnitMenu : partnerUnitMenuMapping) {
                if (partnerUnitMenu.getStatus().equalsIgnoreCase(status)) {
                    partnerUnitMenu.setStatus("IN_ACTIVE");
                    partnerUnitMenuVersionMappingDao.save(partnerUnitMenu);
                } else if (partnerUnitMenu.getVersion().equalsIgnoreCase(version)) {
                    partnerUnitMenu.setStatus(status);
                    partnerUnitMenuVersionMappingDao.save(partnerUnitMenu);
                }
            }
        }
    }

    @Override
    public PartnerUnitMenuDetail getUnitVersionMenu(Integer unitId, Integer kettlePartnerId, Integer brandId,
                                                    String version, String menuType) {
        return partnerUnitMenuDao.findAllByUnitIdAndPartnerIdAndBrandIdAndVersionAndMenuType(unitId, kettlePartnerId,
            brandId, version, MenuType.valueOf(menuType));
    }

    @Override
    public PartnerUnitMenuVersionMapping getPartnerUnitVersionData(Integer partnerId, Integer unitId, Integer brandId,
                                                                   String menuType) {
        return partnerUnitMenuVersionMappingDao.findAllByUnitIdAndPartnerIdAndBrandIdAndMenuTypeAndStatus(unitId, partnerId,
            brandId, MenuType.fromValue(menuType), "ACTIVE");
    }

    @Override
    public PartnerUnitMenuDetail getPartnerUnitVersionMenuDetail(Integer partnerId, Integer unitId, Integer brandId,
                                                                 String menuType, String version) {
        return partnerUnitMenuDao
            .findAllByUnitIdAndPartnerIdAndBrandIdAndVersionAndMenuType(unitId, partnerId, brandId, version,
                MenuType.valueOf(menuType));
    }

    @Override
    public List<PartnerMenuAuditHistory> getPartnerMenuHistoryData(Integer partnerId, Integer unitId, Integer brandId,
                                                                   String menuType) {
        List<PartnerMenuAuditHistory> partnerMenuAuditHistory = partnerMenuAuditDao.findAllByUnitIdAndPartnerIdAndBrandIdAndMenuType(unitId,
            partnerId, brandId, menuType);
        if (partnerMenuAuditHistory != null && !partnerMenuAuditHistory.isEmpty()) {
            return partnerMenuAuditHistory;
        }
        return null;
    }

    @Override
    public List<PartnerMenuAuditHistory> getPartnerMenuHistoryByStatus(Integer partnerId, Integer unitId, Integer brandId,
                                                                       String status) {
        return partnerMenuAuditDao.findAllByUnitIdAndPartnerIdAndBrandIdAndStatus(unitId, partnerId, brandId, status);
    }

    @Override
    public void fetchOrderReconciliationData() {
        Date currentDate = ChannelPartnerUtils.getPreviousDate();
        List<PartnerOrderDetail> partnerOrderData = partnerOrderDao.getOrdersByTime(ChannelPartnerUtils.getStartOfBusinessDay(currentDate), ChannelPartnerUtils.getEndOfBusinessDay(currentDate));
        if (partnerOrderData != null) {
            for (PartnerOrderDetail partnerOrderDetail : partnerOrderData) {
                PartnerOrderReconciliationData partnerOrderReconciliationData = new PartnerOrderReconciliationData();
                partnerOrderReconciliationData.setPartnerName(partnerOrderDetail.getPartnerName());
                partnerOrderReconciliationData.setPartnerOrderId(partnerOrderDetail.getPartnerOrderId());
                partnerOrderReconciliationData.setUnitId(partnerOrderDetail.getUnitId().toString());
                partnerOrderReconciliationData.setBrandId(partnerOrderDetail.getBrandId());
                if (partnerOrderDetail.getPartnerName().equalsIgnoreCase("ZOMATO")) {
                    ZomatoOrderRequestV3 partnerOrder = ChannelPartnerUtils.deserializeObject(new Gson().toJson(partnerOrderDetail.getPartnerOrder()),
                        ZomatoOrderRequestV3.class);
                    if (partnerOrder != null) {
                        partnerOrderReconciliationData.setPartnerGrossAmount(new BigDecimal(Float.toString(partnerOrder.getGrossAmount())));
                        partnerOrderReconciliationData.setPartnerId(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
                    }
                } else {
                    SwiggyOrderRequest partnerOrder = ChannelPartnerUtils.deserializeObject(new Gson().toJson(partnerOrderDetail.getPartnerOrder()),
                        SwiggyOrderRequest.class);
                    if (partnerOrder != null) {
                        partnerOrderReconciliationData.setPartnerGrossAmount(new BigDecimal(Float.toString(partnerOrder.getRestaurantGrossBill())));
                        partnerOrderReconciliationData.setPartnerId(channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId());
                    }
                }
                Order order = ChannelPartnerUtils.deserializeObject(new Gson().toJson(partnerOrderDetail.getKettleOrder()), Order.class);
                if (order != null) {
                    partnerOrderReconciliationData.setKettleTotalAmount(order.getTransactionDetail().getPaidAmount());
                    partnerOrderReconciliationData.setAmountDifference(partnerOrderReconciliationData.getPartnerGrossAmount()
                        .subtract(partnerOrderReconciliationData.getKettleTotalAmount()));
                }
                List<PartnerOrderReconciliationErrorData> partnerOrderReconciliationErrorData = new ArrayList<PartnerOrderReconciliationErrorData>();
                for (PartnerOrderError partnerOrderError : partnerOrderDetail.getOrderErrors()) {
                    PartnerOrderReconciliationErrorData errorData = new PartnerOrderReconciliationErrorData();
                    errorData.setPartnerOrderId(partnerOrderReconciliationData.getPartnerOrderId());
                    errorData.setKettleOrderId(partnerOrderReconciliationData.getKettleOrderId());
                    errorData.setErrorReason(partnerOrderError.getErrorCode().toString());
                    errorData.setErrorDescription(partnerOrderError.getErrorDescription());
                    partnerOrderReconciliationErrorData.add(errorData);
                }
                partnerOrderReconciliationData.setPartnerOrderError(partnerOrderReconciliationErrorData);
                //partnerOrderReconciliationDao.add(partnerOrderReconciliationData);
            }
        }
    }

    @Override
    public boolean checkVersionMenuAuditData(UnitMenuAddVO request, String partnerName) {
        List<PartnerMenuAuditHistory> partnerAuditHistory = getPartnerMenuHistoryByStatus(request.getKettlePartnerId(),
            request.getUnitId(), request.getBrandId(), AppConstants.ACTIVE);
        if (partnerAuditHistory != null) {
            for (PartnerMenuAuditHistory partnerMenuAuditHistory : partnerAuditHistory) {
                if (partnerMenuAuditHistory.getVersion().equalsIgnoreCase(request.getVersion()) &&
                    partnerMenuAuditHistory.getMenuType().equalsIgnoreCase(request.getMenuType().name())) {
                    String logData1= ChannelPartnerUtils.getMessage("Same version Menu i.e. " + request.getVersion() + " for day slot ",request.getMenuType().name()
                            + " is already Live on unit: " + request.getUnitId() + " for brandId: " + request.getBrandId() + " for partner "
                            + partnerName);
                    LOG.info(logData1);
                    SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                        ApplicationName.KETTLE_SERVICE.name(), SlackNotification.PARTNER_INTEGRATION, logData1);
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void updatePartnerMenuAuditData(Integer partnerId, Integer unitId, Integer brandId, int employeeId, String menuType,
                                           String version, String partnerName, Object response) {
        List<PartnerMenuAuditHistory> partnerAuditHistory = getPartnerMenuHistoryByStatus(partnerId, unitId, brandId, AppConstants.ACTIVE);
        if (partnerAuditHistory != null) {
            for (PartnerMenuAuditHistory detail : partnerAuditHistory) {
                detail.setStatus("IN_ACTIVE");
                partnerMenuAuditDao.save(detail);
            }
        }
        PartnerMenuAuditHistory partnerMenuAuditHistory = new PartnerMenuAuditHistory();
        partnerMenuAuditHistory.setUnitId(unitId);
        partnerMenuAuditHistory.setPartnerId(partnerId);
        partnerMenuAuditHistory.setPartnerName(partnerName);
        partnerMenuAuditHistory.setBrandId(brandId);
        partnerMenuAuditHistory.setEmployeeId(employeeId);
        partnerMenuAuditHistory.setEmployeeName(masterDataCache.getEmployee(employeeId));
        partnerMenuAuditHistory.setVersion(version);
        partnerMenuAuditHistory.setStatus("ACTIVE");
        partnerMenuAuditHistory.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        partnerMenuAuditHistory.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        partnerMenuAuditHistory.setMenuType(menuType);
        partnerMenuAuditHistory.setMenuResponse(response);
        partnerMenuAuditDao.save(partnerMenuAuditHistory);
    }

    @Override
    public PartnerUnitProductPricingDetail updatePartnerUnitProductPricing(Integer partnerId, Integer unitId, Integer brandId, int employeeId,
                                                                           Map<Integer, Map<String, BigDecimal>> pricing, String partnerName) {
        List<PartnerUnitProductPricingDetail> pricingDetails = partnerUnitProductPricingDao.findAllByUnitIdAndPartnerIdAndBrandIdAndActive(unitId,
            partnerId, brandId, true);
        if (pricingDetails != null) {
            for (PartnerUnitProductPricingDetail detail : pricingDetails) {
                detail.setActive(false);
                partnerUnitProductPricingDao.save(detail);
            }
        }
        PartnerUnitProductPricingDetail pricingDetail = new PartnerUnitProductPricingDetail();
        pricingDetail.setActive(true);
        pricingDetail.setAddTime(ChannelPartnerUtils.getCurrentTimestamp());
        pricingDetail.setAddTimeIST(ChannelPartnerUtils.getCurrentTimeISTString());
        pricingDetail.setBrandId(brandId);
        pricingDetail.setEmployeeId(employeeId);
        pricingDetail.setEmployeeName(masterDataCache.getEmployee(employeeId));
        pricingDetail.setPartnerId(partnerId);
        pricingDetail.setPartnerName(partnerName);
        pricingDetail.setPricing(pricing);
        pricingDetail.setUnitId(unitId);
        pricingDetail = partnerUnitProductPricingDao.save(pricingDetail);
        return pricingDetail;
    }

    @Override
    public Map<Integer, Map<Integer, CafeMenuAutoPush>> getMapValue(List<Integer> unitIdsForMenu) {
        List<CafeMenuAutoPush> menuData = cafeMenuAutoPushDao.findAllByUnitIdIn(unitIdsForMenu);
        return getMapOfUnitAndBrand(menuData, unitIdsForMenu);
    }

    private Map<Integer, Map<Integer, CafeMenuAutoPush>> getMapOfUnitAndBrand(List<CafeMenuAutoPush> menuData, List<Integer> unitIdsForMenu) {
        Map<Integer, Map<Integer, CafeMenuAutoPush>> mapOfUnitAndBrand = new HashMap<>();
        for (Integer unit : unitIdsForMenu) {
            Map<Integer, CafeMenuAutoPush> mapOfBrand = new HashMap<>();
            menuData.stream().filter(cafeMenuAutoPush -> cafeMenuAutoPush.getUnitId().equals(unit)).forEach(cafeMenuAutoPush -> {
                mapOfBrand.put(cafeMenuAutoPush.getBrandId(), cafeMenuAutoPush);
            });
            if (!mapOfBrand.isEmpty()) {
                mapOfUnitAndBrand.put(unit, mapOfBrand);
            }
            /*boolean flag = false;
            for (CafeMenuAutoPush menuAutoPush : menuData) {
                if (unit.equals(menuAutoPush.getUnitId())) {
                    mapOfBrand.put(menuAutoPush.getBrandId(), menuAutoPush);
                    flag = true;
                }
            }
            if (flag) {
                mapOfUnitAndBrand.put(unit, mapOfBrand);
            }*/
        }
        return mapOfUnitAndBrand;
    }
}
