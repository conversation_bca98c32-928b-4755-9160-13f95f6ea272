package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "outlet_id",
        "zomato_online_order_status",
        "reason",
        "editable",
        "timestamp"
})
public class ZomatoCafeStatusRequest {

    @JsonProperty("outlet_id")
    private String outletId;

    @JsonProperty("zomato_online_order_status")
    private Boolean zomatoOnlineOrderStatus;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("editable")
    private String editable;

    @JsonProperty("timestamp")
    private String timestamp;

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("zomato_online_order_status")
    public Boolean getZomatoOnlineOrderStatus() {
        return zomatoOnlineOrderStatus;
    }

    @JsonProperty("zomato_online_order_status")
    public void setZomatoOnlineOrderStatus(Boolean zomatoOnlineOrderStatus) {
        this.zomatoOnlineOrderStatus = zomatoOnlineOrderStatus;
    }

    @JsonProperty("reason")
    public String getReason() {
        return reason;
    }

    @JsonProperty("reason")
    public void setReason(String reason) {
        this.reason = reason;
    }

    @JsonProperty("editable")
    public String getEditable() {
        return editable;
    }

    @JsonProperty("editable")
    public void setEditable(String editable) {
        this.editable = editable;
    }

    @JsonProperty("timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoCafeStatusRequest that = (ZomatoCafeStatusRequest) o;

        return new EqualsBuilder()
            .append(outletId, that.outletId)
            .append(zomatoOnlineOrderStatus, that.zomatoOnlineOrderStatus)
            .append(reason, that.reason)
            .append(editable, that.editable)
            .append(timestamp, that.timestamp)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(outletId)
            .append(zomatoOnlineOrderStatus)
            .append(reason)
            .append(editable)
            .append(timestamp)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "ZomatoCafeStatusRequest{" +
            "outletId='" + outletId + '\'' +
            ", zomatoOnlineOrderStatus=" + zomatoOnlineOrderStatus +
            ", reason='" + reason + '\'' +
            ", editable='" + editable + '\'' +
            ", timestamp='" + timestamp + '\'' +
            '}';
    }
}
