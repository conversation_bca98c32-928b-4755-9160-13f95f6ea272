package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

//@Entity
//@Table(name = "PARTNER_ORDER_RECONCILIATION_ERROR_DATA")
public class PartnerOrderReconciliationErrorData {

    private int id;
    private int partnerOrderReconciliationId;
    private String partnerOrderId;
    private String kettleOrderId;
    private String errorDescription;
    private String errorReason;
    private PartnerOrderReconciliationData partnerOrderReconciliations;

//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    @Column(name = "ID", unique = true, nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

//    @Column(name = "PARTNER_RECONCILIATION_ID")
    public int getPartnerOrderReconciliationId() {
        return partnerOrderReconciliationId;
    }

    public void setPartnerOrderReconciliationId(int partnerOrderReconciliationId) {
        this.partnerOrderReconciliationId = partnerOrderReconciliationId;
    }

//    @Column(name = "PARTNER_ORDER_ID")
    public String getPartnerOrderId() {
        return partnerOrderId;
    }

    public void setPartnerOrderId(String partnerOrderId) {
        this.partnerOrderId = partnerOrderId;
    }

//    @Column(name = "KETTLE_ORDER_ID")
    public String getKettleOrderId() {
        return kettleOrderId;
    }

    public void setKettleOrderId(String kettleOrderId) {
        this.kettleOrderId = kettleOrderId;
    }

//    @Column(name = "ERROR_REASON")
    public String getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(String errorReason) {
        this.errorReason = errorReason;
    }

//    @Column(name = "ERROR_DESCRIPTION")
    public String getErrorDescription() {
        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }

//    @ManyToOne(fetch = FetchType.LAZY)
//    @JoinColumn(name = "PARTNER_ORDER_RECONCILIATION_ID", nullable = false)
    public PartnerOrderReconciliationData getPartnerOrderReconciliations() {
        return partnerOrderReconciliations;
    }

    public void setPartnerOrderReconciliations(PartnerOrderReconciliationData partnerOrderReconciliations) {
        this.partnerOrderReconciliations = partnerOrderReconciliations;
    }





}
