package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;

@Document
@XmlRootElement(name = "PartnerLocalityDetail")
public class PartnerLocalityDetail {

    @Id
    private String id;
    private String locality;
    private String city;
    private String state;
    private String country;
    private String pinCode;
    private Integer kettlePartnerId;
    private String partnerName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPinCode() {
        return pinCode;
    }

    public void setPinCode(String pinCode) {
        this.pinCode = pinCode;
    }

    public Integer getKettlePartnerId() {
        return kettlePartnerId;
    }

    public void setKettlePartnerId(Integer kettlePartnerId) {
        this.kettlePartnerId = kettlePartnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }
}
