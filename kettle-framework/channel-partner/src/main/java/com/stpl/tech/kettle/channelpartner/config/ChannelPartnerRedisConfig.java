package com.stpl.tech.kettle.channelpartner.config;

import com.stpl.tech.kettle.channelpartner.core.redis.impl.RedisPublisherImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableScheduling
@ComponentScan(basePackages = {"com.stpl.tech.kettle.channelpartner.core.redis"})
public class ChannelPartnerRedisConfig {

    @Autowired
    private Environment env;

    public ChannelPartnerRedisConfig() {
        super();
    }


    @Bean
    public JedisConnectionFactory jedisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration(env.getProperty("redis.host"), Integer.valueOf(env.getProperty("redis.port")));
        config.setDatabase(Integer.parseInt(env.getProperty("redis.db.index")));
        return new JedisConnectionFactory(config);
    }

    @Bean
    public CacheManager cacheManager() {
        return RedisCacheManager.RedisCacheManagerBuilder.fromConnectionFactory(jedisConnectionFactory())
                .build();
    }

//    @Bean
//    JedisConnectionFactory jedisConnectionFactory() {
//        JedisConnectionFactory jedisConFactory = new JedisConnectionFactory();
//        jedisConFactory.setHostName(env.getProperty("redis.host"));
//        jedisConFactory.setPort(Integer.parseInt(env.getProperty("redis.port")));
//        jedisConFactory.setUsePool(true);
//        jedisConFactory.setTimeout(100000);
//        jedisConFactory.setDatabase(Integer.parseInt(env.getProperty("redis.db.index")));
//        //jedisConFactory.setPassword(env.getProperty("redis.pass"));
//        return jedisConFactory;
//    }
//
    @Bean
    RedisTemplate<String, Object> redisTemplate() {
        final RedisTemplate<String, Object> template = new RedisTemplate<String, Object>();
        template.setConnectionFactory(jedisConnectionFactory());
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericToStringSerializer<>(Object.class));
        template.setValueSerializer(new GenericToStringSerializer<Object>(Object.class));
        return template;
    }
//
//    @Bean
//    MessageListenerAdapter messageListener() {
//        return new MessageListenerAdapter(new RedisMessageListener());
//    }
//
    @Bean
    RedisMessageListenerContainer redisContainer() {
        final RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(jedisConnectionFactory());
        //container.addMessageListener(messageListener(), topic());
        return container;
    }
//
    @Bean
    RedisPublisherImpl redisPublisher() {
        return new RedisPublisherImpl(redisTemplate());
    }

    /*@Bean
    ChannelTopic topic() {
        return new ChannelTopic("pubsub:queue");
    }*/

}
