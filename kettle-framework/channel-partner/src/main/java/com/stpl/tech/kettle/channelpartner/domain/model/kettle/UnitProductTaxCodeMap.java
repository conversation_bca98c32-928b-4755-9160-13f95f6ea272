package com.stpl.tech.kettle.channelpartner.domain.model.kettle;

import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;

import java.util.Map;

public class UnitProductTaxCodeMap {

    private Map<Integer, ProductVO> productVOMap;
    private Map<String, TaxDataVO> taxMap;

    public Map<Integer, ProductVO> getProductVOMap() {
        return productVOMap;
    }

    public void setProductVOMap(Map<Integer, ProductVO> productVOMap) {
        this.productVOMap = productVOMap;
    }

    public Map<String, TaxDataVO> getTaxMap() {
        return taxMap;
    }

    public void setTaxMap(Map<String, TaxDataVO> taxMap) {
        this.taxMap = taxMap;
    }
}
