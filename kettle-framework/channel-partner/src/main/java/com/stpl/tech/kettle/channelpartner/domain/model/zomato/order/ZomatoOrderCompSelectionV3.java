package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "selection_id", "selection_title", "selection_entities" })

public class ZomatoOrderCompSelectionV3 {
	
	@JsonProperty("selection_id")
	private String selectionId;
	@JsonProperty("selection_title")
	private String selectionTitle;
	@JsonProperty("selection_entities")
	private List<ZomatoOrderCompSelectionEntityV3> selectionEntities;
	
	public String getSelectionId() {
		return selectionId;
	}
	public void setSelectionId(String selectionId) {
		this.selectionId = selectionId;
	}
	public String getSelectionTitle() {
		return selectionTitle;
	}
	public void setSelectionTitle(String selectionTitle) {
		this.selectionTitle = selectionTitle;
	}
	public List<ZomatoOrderCompSelectionEntityV3> getSelectionEntities() {
		return selectionEntities;
	}
	public void setSelectionEntities(List<ZomatoOrderCompSelectionEntityV3> selectionEntities) {
		this.selectionEntities = selectionEntities;
	}
	@Override
	public String toString() {
		return "ZomatoOrderCompSelectionV3 [selectionId=" + selectionId + ", selectionTitle=" + selectionTitle
				+ ", selectionEntities=" + selectionEntities + "]";
	}
	
	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(selectionId).append(selectionTitle).append(selectionEntities).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof ZomatoOrderCompSelectionV3) == false) {
			return false;
		}
		ZomatoOrderCompSelectionV3 rhs = ((ZomatoOrderCompSelectionV3) other);
		return new EqualsBuilder().append(selectionId, rhs.selectionId).append(selectionTitle, rhs.selectionTitle)
				.append(selectionEntities, rhs.selectionEntities).isEquals();
	}
	
}
