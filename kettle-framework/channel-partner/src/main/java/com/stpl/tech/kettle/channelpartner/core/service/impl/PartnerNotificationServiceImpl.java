package com.stpl.tech.kettle.channelpartner.core.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerNotificationService;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.slack.Slack;

@Service
public class PartnerNotificationServiceImpl implements PartnerNotificationService{

    private static final Logger LOG = LoggerFactory.getLogger(PartnerNotificationServiceImpl.class);

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private MasterDataCache masterDataCache;

   

    @Override
    public void sendOrderNotPunchedNotification(PartnerOrderDetail detail) {
        try {
            UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(detail.getUnitId());
            String message = "Order cannot be processed for partner order Id: " + detail.getPartnerOrderId() + " for partner ID: " + detail.getPartnerId();

            SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
                "Channel Partner", SlackNotification.ORDER_REJECTED, message);
            if (ubd.getUnitManagerId() != null) {
                SlackNotificationService.getInstance()
                    .sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
                        !AppUtils.isProd(environmentProperties.getEnvType())
                            ? environmentProperties.getEnvType().name().toLowerCase() + "_"
                            + ubd.getUnitManagerId() + "_notify"
                            : ubd.getUnitManagerId() + "_notify",
                        message);
            }
            if (ubd.getCafeManagerId() != null) {
                SlackNotificationService.getInstance()
                    .sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
                        !AppUtils.isProd(environmentProperties.getEnvType())
                            ? environmentProperties.getEnvType().name().toLowerCase() + "_"
                            + ubd.getCafeManagerId() + "_notify"
                            : ubd.getCafeManagerId() + "_notify",
                        message);
            }
        } catch (Exception e) {
            LOG.error("Error while publishing slack for inventory down and order punched", e);
        }
    }
}