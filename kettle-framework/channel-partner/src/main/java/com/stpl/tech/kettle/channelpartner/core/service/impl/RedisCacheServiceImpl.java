package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.service.RedisCacheService;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderOutOfStockData;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderOutOfStockDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.SwiggyOrderOutOfStockDataDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class RedisCacheServiceImpl implements RedisCacheService {
    @Autowired
    private SwiggyOrderOutOfStockDataDao swiggyOrderOutOfStockDataDao;
    @Override
    public Optional<SwiggyOrderOutOfStockData> getSwiggyOrderOutOfStockData(String partnerOrderId) {
         return swiggyOrderOutOfStockDataDao.findById(partnerOrderId);
    }

    @Override
    public void saveSwiggyOrderOutOfStockData(SwiggyOrderOutOfStockDetail swiggyOrderOutOfStockDetail) {
        Optional<SwiggyOrderOutOfStockData> data = swiggyOrderOutOfStockDataDao.findById(swiggyOrderOutOfStockDetail.getPartnerOrderId());
        try{
            if(data.isPresent()){
                SwiggyOrderOutOfStockData swiggyOrderOutOfStockData = data.get();
                swiggyOrderOutOfStockData.getItemIds().clear();
                swiggyOrderOutOfStockData.setItemIds(swiggyOrderOutOfStockDetail.getItemIds());
                swiggyOrderOutOfStockData.getAddOnIds().clear();
                swiggyOrderOutOfStockData.setAddOnIds(swiggyOrderOutOfStockDetail.getAddOnIds());
                swiggyOrderOutOfStockData.getVariantIds().clear();
                swiggyOrderOutOfStockData.setVariantIds(swiggyOrderOutOfStockDetail.getVariantIds());
                swiggyOrderOutOfStockDataDao.save(swiggyOrderOutOfStockData);
            }else{
                SwiggyOrderOutOfStockData swiggyOrderOutOfStockData = SwiggyOrderOutOfStockData.builder()
                        .partnerOrderId(swiggyOrderOutOfStockDetail.getPartnerOrderId())
                        .itemIds(swiggyOrderOutOfStockDetail.getItemIds())
                        .addOnIds(swiggyOrderOutOfStockDetail.getAddOnIds())
                        .variantIds(swiggyOrderOutOfStockDetail.getVariantIds()).build();
                swiggyOrderOutOfStockDataDao.save(swiggyOrderOutOfStockData);
            }
        }catch (Exception e){
            log.error("Exception while saving swiggy order out of stock data ::::::::{}", swiggyOrderOutOfStockDetail);
        }
    }
}
