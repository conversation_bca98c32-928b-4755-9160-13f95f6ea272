package com.stpl.tech.kettle.channelpartner.domain.model;

import java.util.Date;

import com.stpl.tech.master.domain.model.MimeType;

public enum MenuType {
	DEFAULT, DAY_SLOT , DAY_SLOT_BREAKFAST, DAY_SLOT_LUNCH, DAY_SLOT_EVENING, DAY_SLOT_DINNER, DAY_SLOT_POST_DINNER,
	DAY_SLOT_OVERNIGHT, SINGLE_SERVE;

	public static MenuType getCurrentMenuType(Date currentTime) {
		int hour = currentTime.getHours();
		if ((hour >= 5 && hour <= 11)) {
			return DAY_SLOT_BREAKFAST;
		} else if (hour >= 12 && hour <= 14) {
			return DAY_SLOT_LUNCH;
		} else if (hour >= 15 && hour <= 19) {
			return DAY_SLOT_EVENING;
		} else if (hour >= 20 && hour <= 21) {
			return DAY_SLOT_DINNER;
		} else if ((hour >= 22 && hour <= 23)) {
			return DAY_SLOT_POST_DINNER;
		} else if (hour >= 0 && hour <= 4) {
			return DAY_SLOT_OVERNIGHT;
		}
		return DEFAULT;

	}
	
	public static MenuType fromValue(String v) {
        for (MenuType c: MenuType.values()) {
            if (c.toString().equalsIgnoreCase(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }
}
