
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "taxes",
    "charges",
    "order_additional_charges",
    "categories"
})
public class OldZomatoMenu {

    @JsonProperty("taxes")
    private List<OldZomatoMenuTax> taxes = null;
    @JsonProperty("charges")
    private List<OldZomatoMenuCharge> charges = null;
    @JsonProperty("order_additional_charges")
    private List<ZomatoMenuItemCharge> orderAdditionalCharges = null;
    @JsonProperty("categories")
    private List<OldZomatoCategory> categories = null;

    @JsonProperty("taxes")
    public List<OldZomatoMenuTax> getTaxes() {
        return taxes;
    }

    @JsonProperty("taxes")
    public void setTaxes(List<OldZomatoMenuTax> taxes) {
        this.taxes = taxes;
    }

    @JsonProperty("charges")
    public List<OldZomatoMenuCharge> getCharges() {
        return charges;
    }

    @JsonProperty("charges")
    public void setCharges(List<OldZomatoMenuCharge> charges) {
        this.charges = charges;
    }

    @JsonProperty("order_additional_charges")
    public List<ZomatoMenuItemCharge> getOrderAdditionalCharges() {
        return orderAdditionalCharges;
    }

    @JsonProperty("order_additional_charges")
    public void setOrderAdditionalCharges(List<ZomatoMenuItemCharge> orderAdditionalCharges) {
        this.orderAdditionalCharges = orderAdditionalCharges;
    }

    @JsonProperty("categories")
    public List<OldZomatoCategory> getCategories() {
        return categories;
    }

    @JsonProperty("categories")
    public void setCategories(List<OldZomatoCategory> categories) {
        this.categories = categories;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        OldZomatoMenu that = (OldZomatoMenu) o;

        return new EqualsBuilder()
                .append(taxes, that.taxes)
                .append(charges, that.charges)
                .append(orderAdditionalCharges, that.orderAdditionalCharges)
                .append(categories, that.categories)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(taxes)
                .append(charges)
                .append(orderAdditionalCharges)
                .append(categories)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "ZomatoMenu{" +
                "taxes=" + taxes +
                ", charges=" + charges +
                ", orderAdditionalCharges=" + orderAdditionalCharges +
                ", categories=" + categories +
                '}';
    }
}
