package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

public class GstBreakup {

    @JsonProperty("total")
    private float total;
    @JsonProperty("vendor")
    private GstBreakupDetails vendor;
    @JsonProperty("swiggy")
    private GstBreakupDetails swiggy;

    @JsonProperty("total")
    public float getTotal() {
        return total;
    }

    @JsonProperty("total")
    public void setTotal(float total) {
        this.total = total;
    }

    @JsonProperty("vendor")
    public GstBreakupDetails getVendor() {
        return vendor;
    }

    @JsonProperty("vendor")
    public void setVendor(GstBreakupDetails vendor) {
        this.vendor = vendor;
    }

    @JsonProperty("swiggy")
    public GstBreakupDetails getSwiggy() {
        return swiggy;
    }

    @JsonProperty("swiggy")
    public void setSwiggy(GstBreakupDetails swiggy) {
        this.swiggy = swiggy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        GstBreakup that = (GstBreakup) o;

        return new EqualsBuilder()
            .append(total, that.total)
            .append(vendor, that.vendor)
            .append(swiggy, that.swiggy)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(total)
            .append(vendor)
            .append(swiggy)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "GstBreakup{" +
            "total=" + total +
            ", vendor=" + vendor +
            ", swiggy=" + swiggy +
            '}';
    }
}
