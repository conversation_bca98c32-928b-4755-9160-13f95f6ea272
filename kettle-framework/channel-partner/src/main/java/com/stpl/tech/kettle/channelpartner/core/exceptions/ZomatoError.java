package com.stpl.tech.kettle.channelpartner.core.exceptions;


import com.fasterxml.jackson.annotation.JsonProperty;

public class ZomatoError {

    private Integer code;
    private String status;
    private String message;
    private String error;
    @JsonProperty("validation_errors")
    private Object validationErrors;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    @JsonProperty("validation_errors")
    public Object getValidationErrors() {
        return validationErrors;
    }

    @JsonProperty("validation_errors")
    public void setValidationErrors(Object validationErrors) {
        this.validationErrors = validationErrors;
    }
}
