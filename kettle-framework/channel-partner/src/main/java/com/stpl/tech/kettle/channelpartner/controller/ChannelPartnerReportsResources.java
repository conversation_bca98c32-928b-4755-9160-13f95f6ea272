package com.stpl.tech.kettle.channelpartner.controller;

import com.stpl.tech.kettle.channelpartner.core.service.PartnerMetadataManagementService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.domain.model.ChannelPartnerReportRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductStockSnapshot;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.REPORTS_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + REPORTS_ROOT_CONTEXT) // 'v1/reports'
public class ChannelPartnerReportsResources {

    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerReportsResources.class);

    @Autowired
    private PartnerOrderService partnerOrderService;

    @Autowired
    private PartnerMetadataManagementService partnerMetadataManagementService;

    @RequestMapping(method = RequestMethod.POST, value = "orders-by-time", produces = MediaType.APPLICATION_JSON)
    public List<PartnerOrderDetail> getOrdersBetweenTime(@RequestBody ChannelPartnerReportRequest request) {
        LOG.info("Request to get partner orders between time", request.getStartTime(), request.getEndTime());
        return partnerOrderService.getOrdersByTime(request.getStartTime(), request.getEndTime());
    }

    @RequestMapping(method = RequestMethod.POST, value = "stock/snapshot", produces = MediaType.APPLICATION_JSON)
    public PartnerUnitProductStockSnapshot gtUnitProductStockSnapshot(@RequestBody UnitPartnerBrandKey request, @RequestParam Integer productId) {
        LOG.info("Request to get unit product stock for unit {} partner {} brand {}", request.getUnitId(),
            request.getPartnerId(), request.getBrandId());
        return partnerMetadataManagementService.getUnitProductStockSnapshot(request, productId);
    }
}
