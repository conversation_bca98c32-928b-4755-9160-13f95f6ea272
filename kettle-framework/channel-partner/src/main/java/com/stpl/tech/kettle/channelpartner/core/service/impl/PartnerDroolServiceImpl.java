package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.config.PartnerDroolConfig;
import com.stpl.tech.kettle.channelpartner.core.PartnerDroolFileType;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerDroolService;
import com.stpl.tech.spring.service.FileArchiveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class PartnerDroolServiceImpl implements PartnerDroolService {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerDroolService.class);

    private static final String RESOURCE_PATH_OF_COMMISSION_DROOL_FILE= "drools/commission_matrix.xls";

    private static final String COMMISSION_FILE_DESTINATION_DIRECTORY = "/drools/commission_matrix";

    @Autowired
    private PartnerDroolConfig partnerDroolConfig;

    @Autowired
    private EnvironmentProperties env;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Override
    public void initailizeDroolContainer(String droolFileType){
        try{
            processAndDownloadDroolFile(droolFileType);
            if(PartnerDroolFileType.COMMISSION_MATRIX.getFileName().equals(droolFileType)) {
                partnerDroolConfig.initDroolConfigForCommissionMatrix();
            }
        }catch (Exception e){
            LOG.info("Error in initialize drool container for offerDecision : {}",e.getMessage());
        }
    }

    public void processAndDownloadDroolFile(String droolFileType) throws Exception {
        String envType = env.getEnvType().name().toLowerCase();
        String destinationDirectory = env.getBasePath()  + COMMISSION_FILE_DESTINATION_DIRECTORY;
        try {
            fileArchiveService.saveFileToDestinationPath(RESOURCE_PATH_OF_COMMISSION_DROOL_FILE, destinationDirectory,PartnerDroolFileType.COMMISSION_MATRIX.getFileName());
        }catch (Exception e){
            LOG.info("Error in saving commission matrix drool file into data folder : {}",e.getMessage());
        }

    }

    @Override
    public boolean isDroolContainerInitializeForCommissionMatrix(){
        return Objects.nonNull(partnerDroolConfig.getKieContainerForCommissionMatrix());
    }


}
