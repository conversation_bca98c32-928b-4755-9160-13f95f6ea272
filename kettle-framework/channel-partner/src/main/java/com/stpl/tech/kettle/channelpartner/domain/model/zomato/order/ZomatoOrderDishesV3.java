package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "dish_type", "dish_id", "composition", "quantity", "total_cost", "total_taxed_discount",
		"dish_discounts", "taxes", "charges", "final_cost", })
public class ZomatoOrderDishesV3 {

	@JsonProperty("dish_type")
	private String dishType;
	@JsonProperty("dish_id")
	private String dishId;
	@JsonProperty("composition")
	private ZomatoOrderCompositionV3 composition;
	@JsonProperty("quantity")
	private Integer quantity;
	@JsonProperty("total_cost")
	private Float totalCost;
	@JsonProperty("total_discount")
	private Float totalDiscount;
	@JsonProperty("total_taxed_discount")
	private String totalTaxedDiscount;
	@JsonProperty("dish_discounts")
	private List<ZomatoOrderDishDiscountV3> dishDiscounts = new ArrayList<>();
	@JsonProperty("taxes")
	private List<ZomatoOrderTaxDetailsV3> taxes = new ArrayList<>();
	@JsonProperty("charges")
	private List<ZomatoOrderDishChargesV3> itemCharges = new ArrayList<>();
	@JsonProperty("final_cost")
	private Float finalCost = null;

	public String getDishType() {
		return dishType;
	}

	public void setDishType(String dishType) {
		this.dishType = dishType;
	}

	public String getDishId() {
		return dishId;
	}

	public void setDishId(String dishId) {
		this.dishId = dishId;
	}

	public ZomatoOrderCompositionV3 getComposition() {
		return composition;
	}

	public void setComposition(ZomatoOrderCompositionV3 composition) {
		this.composition = composition;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public Float getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(Float totalCost) {
		this.totalCost = totalCost;
	}

	public String getTotalTaxedDiscount() {
		return totalTaxedDiscount;
	}

	public void setTotalTaxedDiscount(String totalTaxedDiscount) {
		this.totalTaxedDiscount = totalTaxedDiscount;
	}

	public List<ZomatoOrderDishDiscountV3> getDishDiscounts() {
		return dishDiscounts;
	}

	public void setDishDiscounts(List<ZomatoOrderDishDiscountV3> dishDiscounts) {
		this.dishDiscounts = dishDiscounts;
	}

	public List<ZomatoOrderTaxDetailsV3> getTaxes() {
		return taxes;
	}

	public void setTaxes(List<ZomatoOrderTaxDetailsV3> taxes) {
		this.taxes = taxes;
	}

	public List<ZomatoOrderDishChargesV3> getItemCharges() {
		return itemCharges;
	}

	public void setItemCharges(List<ZomatoOrderDishChargesV3> itemCharges) {
		this.itemCharges = itemCharges;
	}

	public Float getFinalCost() {
		return finalCost;
	}

	public void setFinalCost(Float finalCost) {
		this.finalCost = finalCost;
	}

	@JsonProperty("total_discount")
	public Float getTotalDiscount() {
		return totalDiscount;
	}

	@JsonProperty("total_discount")
	public void setTotalDiscount(Float totalDiscount) {
		this.totalDiscount = totalDiscount;
	}

	@Override
	public String toString() {
		return "ZomatoOrderDishesV3 [dishType=" + dishType + ", dishId=" + dishId + ", composition=" + composition
				+ ", quantity=" + quantity + ", totalCost=" + totalCost + ", totalTaxedDiscount=" + totalTaxedDiscount
				+ ", dishDiscounts=" + dishDiscounts + ", taxes=" + taxes + ", itemCharges=" + itemCharges
				+ ", finalCost=" + finalCost + "]";
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(dishType).append(dishId).append(composition).append(quantity)
				.append(totalCost).append(totalTaxedDiscount).append(itemCharges).append(dishDiscounts).append(taxes)
				.append(itemCharges).append(finalCost).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof ZomatoOrderDishesV3) == false) {
			return false;
		}
		ZomatoOrderDishesV3 rhs = ((ZomatoOrderDishesV3) other);
		return new EqualsBuilder().append(dishType, rhs.dishType).append(dishId, rhs.dishId)
				.append(composition, rhs.composition).append(quantity, rhs.quantity).append(totalCost, rhs.totalCost)
				.append(totalTaxedDiscount, rhs.totalTaxedDiscount).append(itemCharges, rhs.itemCharges)
				.append(dishDiscounts, rhs.dishDiscounts).append(taxes, rhs.taxes).append(itemCharges, rhs.itemCharges)
				.append(finalCost, rhs.finalCost).isEquals();
	}

}
