package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

public enum ZomatoRejectionCodes {
    ITEMS_OUT_OF_STOCK(1,"Items out of stock"),
    NO_DELIVERY_BOYS(2,"No delivery boys available"),
    NEARING_CLOSING_TIME(3,"Nearing closing time"),
    OUT_OF_SUBZONE_AREA(4,"Out of Subzone/Area"),
    KITCHEN_IS_FULL(5,"Kitchen is Full");

    int id;
    String message;

    ZomatoRejectionCodes(int id, String message) {
    }

    public int getId() {
        return id;
    }

    public String getMessage() {
        return message;
    }
}
