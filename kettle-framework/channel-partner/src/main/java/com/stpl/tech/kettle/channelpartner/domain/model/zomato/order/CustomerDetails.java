package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "name",
        "phone_number",
        "email",
        "address",
        "delivery_area_latitude",
        "delivery_area_longitude",
        "delivery_area",
        "city",
        "state",
        "country",
        "pincode",
        "address_instructions",
        "order_instructions"
})
public class CustomerDetails {

    @JsonProperty("name")
    private String name;
    @JsonProperty("phone_number")
    private String phoneNumber;
    @JsonProperty("email")
    private String email;
    @JsonProperty("address")
    private String address;
    @JsonProperty("delivery_area_latitude")
    private Float deliveryAreaLatitude;
    @JsonProperty("delivery_area_longitude")
    private Float deliveryAreaLongitude;
    @JsonProperty("delivery_area")
    private String deliveryArea;
    @JsonProperty("city")
    private String city;
    @JsonProperty("state")
    private String state;
    @JsonProperty("country")
    private String country;
    @JsonProperty("pincode")
    private String pincode;
    @JsonProperty("address_instructions")
    private String addressInstructions;
    @JsonProperty("address_type")
    private String addressType;
    @JsonProperty("order_instructions")
    private String orderInstructions;
    @JsonProperty("delivery_coordinates_type")
    private String deliveryCoordinatesType;

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("phone_number")
    public String getPhoneNumber() {
        return phoneNumber;
    }

    @JsonProperty("phone_number")
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @JsonProperty("email")
    public String getEmail() {
        return email;
    }

    @JsonProperty("email")
    public void setEmail(String email) {
        this.email = email;
    }

    @JsonProperty("address")
    public String getAddress() {
        return address;
    }

    @JsonProperty("address")
    public void setAddress(String address) {
        this.address = address;
    }

    @JsonProperty("delivery_area_latitude")
    public Float getDeliveryAreaLatitude() {
        return deliveryAreaLatitude;
    }

    @JsonProperty("delivery_area_latitude")
    public void setDeliveryAreaLatitude(Float deliveryAreaLatitude) {
        this.deliveryAreaLatitude = deliveryAreaLatitude;
    }

    @JsonProperty("delivery_area_longitude")
    public Float getDeliveryAreaLongitude() {
        return deliveryAreaLongitude;
    }

    @JsonProperty("delivery_area_longitude")
    public void setDeliveryAreaLongitude(Float deliveryAreaLongitude) {
        this.deliveryAreaLongitude = deliveryAreaLongitude;
    }

    @JsonProperty("delivery_area")
    public String getDeliveryArea() {
        return deliveryArea;
    }

    @JsonProperty("delivery_area")
    public void setDeliveryArea(String deliveryArea) {
        this.deliveryArea = deliveryArea;
    }

    @JsonProperty("city")
    public String getCity() {
        return city;
    }

    @JsonProperty("city")
    public void setCity(String city) {
        this.city = city;
    }

    @JsonProperty("state")
    public String getState() {
        return state;
    }

    @JsonProperty("state")
    public void setState(String state) {
        this.state = state;
    }

    @JsonProperty("country")
    public String getCountry() {
        return country;
    }

    @JsonProperty("country")
    public void setCountry(String country) {
        this.country = country;
    }

    @JsonProperty("pincode")
    public String getPincode() {
        return pincode;
    }

    @JsonProperty("pincode")
    public void setPincode(String pincode) {
        this.pincode = pincode;
    }

    @JsonProperty("address_instructions")
    public String getAddressInstructions() {
        return addressInstructions;
    }

    @JsonProperty("address_instructions")
    public void setAddressInstructions(String addressInstructions) {
        this.addressInstructions = addressInstructions;
    }

    @JsonProperty("address_type")
    public String getAddressType() {
        return addressType;
    }

    @JsonProperty("address_type")
    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }

    @JsonProperty("order_instructions")
    public String getOrderInstructions() {
        return orderInstructions;
    }

    @JsonProperty("order_instructions")
    public void setOrderInstructions(String orderInstructions) {
        this.orderInstructions = orderInstructions;
    }

    @JsonProperty("delivery_coordinates_type")
    public String getDeliveryCoordinatesType() {
        return deliveryCoordinatesType;
    }

    @JsonProperty("delivery_coordinates_type")
    public void setDeliveryCoordinatesType(String deliveryCoordinatesType) {
        this.deliveryCoordinatesType = deliveryCoordinatesType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("name", name).append("phoneNumber", phoneNumber).append("email", email).append("address", address).append("deliveryAreaLatitude", deliveryAreaLatitude).append("deliveryAreaLongitude", deliveryAreaLongitude).append("deliveryArea", deliveryArea).append("city", city).append("state", state).append("country", country).append("pincode", pincode).append("addressInstructions", addressInstructions).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(deliveryAreaLatitude).append(state).append(addressInstructions).append(country).append(city).append(pincode).append(phoneNumber).append(address).append(email).append(deliveryAreaLongitude).append(name).append(deliveryArea).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof CustomerDetails) == false) {
            return false;
        }
        CustomerDetails rhs = ((CustomerDetails) other);
        return new EqualsBuilder().append(deliveryAreaLatitude, rhs.deliveryAreaLatitude).append(state, rhs.state).append(addressInstructions, rhs.addressInstructions).append(country, rhs.country).append(city, rhs.city).append(pincode, rhs.pincode).append(phoneNumber, rhs.phoneNumber).append(address, rhs.address).append(email, rhs.email).append(deliveryAreaLongitude, rhs.deliveryAreaLongitude).append(name, rhs.name).append(deliveryArea, rhs.deliveryArea).isEquals();
    }

}