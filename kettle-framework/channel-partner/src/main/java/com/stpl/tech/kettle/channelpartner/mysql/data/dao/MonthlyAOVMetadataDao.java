package com.stpl.tech.kettle.channelpartner.mysql.data.dao;

import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MonthlyAOVDetail;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.MonthlyAOVMetadata;
import com.stpl.tech.master.core.exception.DataUpdationException;

import java.util.List;
import java.util.Optional;

public interface MonthlyAOVMetadataDao extends AbstractMasterDao{
    MonthlyAOVMetadata getPreviousMonthAov(int year, int month, int brandId);
    List<MonthlyAOVMetadata> getBrandWisePreviousMonthAov(int year , int month , List<Integer> brandId);

    void updateAovValues(MonthlyAOVMetadata monthlyAOVMetadata, Optional<MonthlyAOVDetail> obj) throws DataUpdationException;
}
