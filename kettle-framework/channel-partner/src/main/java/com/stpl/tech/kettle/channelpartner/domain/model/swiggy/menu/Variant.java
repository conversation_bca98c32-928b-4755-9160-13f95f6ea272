
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "id",
    "name",
    "price",
    "default",
    "order",
    "in_stock",
    "is_veg",
    "gst_details",
    "default_dependent_variant_id",
    "default_dependent_variant_group_id"
})
public class Variant {

    @JsonProperty("id")
    private String id;
    @JsonProperty("name")
    private String name;
    @JsonProperty("price")
    private Double price = null;
    @JsonProperty("default")
    private Boolean _default;
    @JsonProperty("order")
    private Integer order;
    @JsonProperty("in_stock")
    private Integer inStock;
    @JsonProperty("is_veg")
    private Integer isVeg;
    @JsonProperty("gst_details")
    private GstDetails gstDetails;
    @JsonProperty("default_dependent_variant_id")
    private String defaultDependentVariantId;
    @JsonProperty("default_dependent_variant_group_id")
    private String defaultDependentVariantGroupId;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("price")
    public Double getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Double price) {
        this.price = price;
    }

    @JsonProperty("default")
    public Boolean getDefault() {
        return _default;
    }

    @JsonProperty("default")
    public void setDefault(Boolean _default) {
        this._default = _default;
    }

    @JsonProperty("order")
    public Integer getOrder() {
        return order;
    }

    @JsonProperty("order")
    public void setOrder(Integer order) {
        this.order = order;
    }

    @JsonProperty("in_stock")
    public Integer getInStock() {
        return inStock;
    }

    @JsonProperty("in_stock")
    public void setInStock(Integer inStock) {
        this.inStock = inStock;
    }

    @JsonProperty("is_veg")
    public Integer getIsVeg() {
        return isVeg;
    }

    @JsonProperty("is_veg")
    public void setIsVeg(Integer isVeg) {
        this.isVeg = isVeg;
    }

    @JsonProperty("gst_details")
    public GstDetails getGstDetails() {
        return gstDetails;
    }

    @JsonProperty("gst_details")
    public void setGstDetails(GstDetails gstDetails) {
        this.gstDetails = gstDetails;
    }

    @JsonProperty("default_dependent_variant_id")
    public String getDefaultDependentVariantId() {
        return defaultDependentVariantId;
    }

    @JsonProperty("default_dependent_variant_id")
    public void setDefaultDependentVariantId(String defaultDependentVariantId) {
        this.defaultDependentVariantId = defaultDependentVariantId;
    }

    @JsonProperty("default_dependent_variant_group_id")
    public String getDefaultDependentVariantGroupId() {
        return defaultDependentVariantGroupId;
    }

    @JsonProperty("default_dependent_variant_group_id")
    public void setDefaultDependentVariantGroupId(String defaultDependentVariantGroupId) {
        this.defaultDependentVariantGroupId = defaultDependentVariantGroupId;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("id", id).append("name", name).append("price", price).append("_default", _default).append("order", order).append("inStock", inStock).append("isVeg", isVeg).append("gstDetails", gstDetails).append("defaultDependentVariantId", defaultDependentVariantId).append("defaultDependentVariantGroupId", defaultDependentVariantGroupId).append("additionalProperties", additionalProperties).toString();
    }

}
