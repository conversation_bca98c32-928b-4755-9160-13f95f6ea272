
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "item_id",
        "item_name",
        "item_unit_price",
        "item_final_price",
        "item_short_description",
        "item_long_description",
        "item_is_active",
        "item_is_default",
        "item_image_url",
        "item_in_stock",
        "item_order",
        "item_is_recommended",
        "item_is_treats_active",
        "combo_reduced_price",
        "item_is_bogo_active",
        "item_image_url",
        "item_tags",
        "groups",
        "item_taxes",
        "item_charges",
        "item_discounts"
})
public class ZomatoMenuItem {

    @JsonProperty("item_id")
    private String itemId;
    @JsonProperty("item_name")
    private String itemName;
    @JsonProperty("item_unit_price")
    private Float itemUnitPrice;
    @JsonProperty("item_final_price")
    private Float itemFinalPrice;
    @JsonProperty("item_short_description")
    private String itemShortDescription;
    @JsonProperty("item_long_description")
    private String itemLongDescription;
    @JsonProperty("item_is_active")
    private Integer itemIsActive;
    @JsonProperty("item_is_default")
    private Integer itemIsDefault;
    @JsonProperty("item_image_url")
    private String itemImageUrl;
    @JsonProperty("item_in_stock")
    private Integer itemInStock;
    @JsonProperty("item_order")
    private Integer itemOrder;
    @JsonProperty("item_is_recommended")
    private Integer itemIsRecommended;
    @JsonProperty("item_is_treats_active")
    private Integer itemIsTreatsActive;
    @JsonProperty("combo_reduced_price")
    private Float comboReducedPrice;
    @JsonProperty("item_is_bogo_active")
    private Integer itemIsBogoActive;
    @JsonProperty("item_tags")
    private List<Integer> itemTags = new ArrayList<>();
    @JsonProperty("groups")
    private List<Group> groups = new ArrayList<>();
    @JsonProperty("item_taxes")
    private List<ZomatoMenuItemTax> itemTaxes = new ArrayList<>();
    @JsonProperty("item_charges")
    private List<ZomatoMenuItemCharge> itemCharges = new ArrayList<>();
    @JsonProperty("item_discounts")
    private List<Object> itemDiscounts = new ArrayList<>();

    @JsonProperty("item_id")
    public String getItemId() {
        return itemId;
    }

    @JsonProperty("item_id")
    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    @JsonProperty("item_name")
    public String getItemName() {
        return itemName;
    }

    @JsonProperty("item_name")
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @JsonProperty("item_unit_price")
    public Float getItemUnitPrice() {
        return itemUnitPrice;
    }

    @JsonProperty("item_unit_price")
    public void setItemUnitPrice(Float itemUnitPrice) {
        this.itemUnitPrice = itemUnitPrice;
    }

    @JsonProperty("item_final_price")
    public Float getItemFinalPrice() {
        return itemFinalPrice;
    }

    @JsonProperty("item_final_price")
    public void setItemFinalPrice(Float itemFinalPrice) {
        this.itemFinalPrice = itemFinalPrice;
    }

    @JsonProperty("item_short_description")
    public String getItemShortDescription() {
        return itemShortDescription;
    }

    @JsonProperty("item_short_description")
    public void setItemShortDescription(String itemShortDescription) {
        this.itemShortDescription = itemShortDescription;
    }

    @JsonProperty("item_long_description")
    public String getItemLongDescription() {
        return itemLongDescription;
    }

    @JsonProperty("item_long_description")
    public void setItemLongDescription(String itemLongDescription) {
        this.itemLongDescription = itemLongDescription;
    }

    @JsonProperty("item_is_active")
    public Integer getItemIsActive() {
        return itemIsActive;
    }

    @JsonProperty("item_is_active")
    public void setItemIsActive(Integer itemIsActive) {
        this.itemIsActive = itemIsActive;
    }

    @JsonProperty("item_is_default")
    public Integer getItemIsDefault() {
        return itemIsDefault;
    }

    @JsonProperty("item_is_default")
    public void setItemIsDefault(Integer itemIsDefault) {
        this.itemIsDefault = itemIsDefault;
    }

    @JsonProperty("item_image_url")
    public String getItemImageUrl() {
        return itemImageUrl;
    }

    @JsonProperty("item_image_url")
    public void setItemImageUrl(String itemImageUrl) {
        this.itemImageUrl = itemImageUrl;
    }

    @JsonProperty("item_in_stock")
    public Integer getItemInStock() {
        return itemInStock;
    }

    @JsonProperty("item_in_stock")
    public void setItemInStock(Integer itemInStock) {
        this.itemInStock = itemInStock;
    }

    @JsonProperty("item_order")
    public Integer getItemOrder() {
        return itemOrder;
    }

    @JsonProperty("item_order")
    public void setItemOrder(Integer itemOrder) {
        this.itemOrder = itemOrder;
    }

    @JsonProperty("item_is_recommended")
    public Integer getItemIsRecommended() {
        return itemIsRecommended;
    }

    @JsonProperty("item_is_recommended")
    public void setItemIsRecommended(Integer itemIsRecommended) {
        this.itemIsRecommended = itemIsRecommended;
    }

    @JsonProperty("item_is_treats_active")
    public Integer getItemIsTreatsActive() {
        return itemIsTreatsActive;
    }

    @JsonProperty("item_is_treats_active")
    public void setItemIsTreatsActive(Integer itemIsTreatsActive) {
        this.itemIsTreatsActive = itemIsTreatsActive;
    }

    @JsonProperty("combo_reduced_price")
    public Float getComboReducedPrice() {
        return comboReducedPrice;
    }

    @JsonProperty("combo_reduced_price")
    public void setComboReducedPrice(Float comboReducedPrice) {
        this.comboReducedPrice = comboReducedPrice;
    }

    @JsonProperty("item_is_bogo_active")
    public Integer getItemIsBogoActive() {
        return itemIsBogoActive;
    }

    @JsonProperty("item_is_bogo_active")
    public void setItemIsBogoActive(Integer itemIsBogoActive) {
        this.itemIsBogoActive = itemIsBogoActive;
    }

    @JsonProperty("item_tags")
    public List<Integer> getItemTags() {
        return itemTags;
    }

    @JsonProperty("item_tags")
    public void setItemTags(List<Integer> itemTags) {
        this.itemTags = itemTags;
    }

    @JsonProperty("groups")
    public List<Group> getGroups() {
        if(groups == null){
            groups = new ArrayList<>();
        }
        return groups;
    }

    @JsonProperty("groups")
    public void setGroups(List<Group> groups) {
        this.groups = groups;
    }

    @JsonProperty("item_taxes")
    public List<ZomatoMenuItemTax> getItemTaxes() {
        return itemTaxes;
    }

    @JsonProperty("item_taxes")
    public void setItemTaxes(List<ZomatoMenuItemTax> itemTaxes) {
        this.itemTaxes = itemTaxes;
    }

    @JsonProperty("item_charges")
    public List<ZomatoMenuItemCharge> getItemCharges() {
        return itemCharges;
    }

    @JsonProperty("item_charges")
    public void setItemCharges(List<ZomatoMenuItemCharge> itemCharges) {
        this.itemCharges = itemCharges;
    }

    @JsonProperty("item_discounts")
    public List<Object> getItemDiscounts() {
        return itemDiscounts;
    }

    @JsonProperty("item_discounts")
    public void setItemDiscounts(List<Object> itemDiscounts) {
        this.itemDiscounts = itemDiscounts;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("itemId", itemId).append("itemName", itemName).append("itemUnitPrice", itemUnitPrice).append("itemFinalPrice", itemFinalPrice).append("itemShortDescription", itemShortDescription).append("itemLongDescription", itemLongDescription).append("itemIsActive", itemIsActive).append("itemIsDefault", itemIsDefault).append("itemImageUrl", itemImageUrl).append("itemTags", itemTags).append("groups", groups).append("itemTaxes", itemTaxes).append("itemCharges", itemCharges).append("itemDiscounts", itemDiscounts).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(itemId)
                .append(itemName)
                .append(itemUnitPrice)
                .append(itemFinalPrice)
                .append(itemShortDescription)
                .append(itemLongDescription)
                .append(itemIsActive)
                .append(itemIsDefault)
                .append(itemImageUrl)
                .append(itemInStock)
                .append(itemOrder)
                .append(itemIsRecommended)
                .append(itemIsTreatsActive)
                .append(itemIsBogoActive)
                .append(itemTags)
                .append(groups)
                .append(itemTaxes)
                .append(itemCharges)
                .append(itemDiscounts)
                .toHashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoMenuItem that = (ZomatoMenuItem) o;

        return new EqualsBuilder()
                .append(itemId, that.itemId)
                .append(itemName, that.itemName)
                .append(itemUnitPrice, that.itemUnitPrice)
                .append(itemFinalPrice, that.itemFinalPrice)
                .append(itemShortDescription, that.itemShortDescription)
                .append(itemLongDescription, that.itemLongDescription)
                .append(itemIsActive, that.itemIsActive)
                .append(itemIsDefault, that.itemIsDefault)
                .append(itemImageUrl, that.itemImageUrl)
                .append(itemInStock, that.itemInStock)
                .append(itemOrder, that.itemOrder)
                .append(itemIsRecommended, that.itemIsRecommended)
                .append(itemIsTreatsActive, that.itemIsTreatsActive)
                .append(itemIsBogoActive, that.itemIsBogoActive)
                .append(itemTags, that.itemTags)
                .append(groups, that.groups)
                .append(itemTaxes, that.itemTaxes)
                .append(itemCharges, that.itemCharges)
                .append(itemDiscounts, that.itemDiscounts)
                .isEquals();
    }
}
