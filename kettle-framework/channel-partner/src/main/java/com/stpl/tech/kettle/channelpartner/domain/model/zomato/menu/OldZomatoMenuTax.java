package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "tax_id",
        "tax_name",
        "tax_type",
        "tax_value",
        "tax_is_active"
})
public class OldZomatoMenuTax {

    @JsonProperty("tax_id")
    private String taxId;
    @JsonProperty("tax_name")
    private String taxName;
    @JsonProperty("tax_type")
    private String taxType;
    @JsonProperty("tax_value")
    private Float taxValue;
    @JsonProperty("tax_is_active")
    private Integer taxIsActive;

    @JsonProperty("tax_id")
    public String getTaxId() {
        return taxId;
    }

    @JsonProperty("tax_id")
    public void setTaxId(String taxId) {
        this.taxId = taxId;
    }

    @JsonProperty("tax_name")
    public String getTaxName() {
        return taxName;
    }

    @JsonProperty("tax_name")
    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    @JsonProperty("tax_type")
    public String getTaxType() {
        return taxType;
    }

    @JsonProperty("tax_type")
    public void setTaxType(String taxType) {
        this.taxType = taxType;
    }

    @JsonProperty("tax_value")
    public Float getTaxValue() {
        return taxValue;
    }

    @JsonProperty("tax_value")
    public void setTaxValue(Float taxValue) {
        this.taxValue = taxValue;
    }

    @JsonProperty("tax_is_active")
    public Integer getTaxIsActive() {
        return taxIsActive;
    }

    @JsonProperty("tax_is_active")
    public void setTaxIsActive(Integer taxIsActive) {
        this.taxIsActive = taxIsActive;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("taxId", taxId).append("taxName", taxName).append("taxType", taxType).append("taxValue", taxValue).append("taxIsActive", taxIsActive).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(taxId).append(taxName).append(taxType).append(taxValue).append(taxIsActive).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof OldZomatoMenuTax) == false) {
            return false;
        }
        OldZomatoMenuTax rhs = ((OldZomatoMenuTax) other);
        return new EqualsBuilder().append(taxId, rhs.taxId).append(taxName, rhs.taxName).append(taxType, rhs.taxType).append(taxValue, rhs.taxValue).append(taxIsActive, rhs.taxIsActive).isEquals();
    }

}