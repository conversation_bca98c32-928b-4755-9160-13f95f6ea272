package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.kettle.channelpartner.domain.model.PartnerStatus;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;

@Document
@XmlRootElement(name = "PartnerDetail")
public class PartnerDetail {

    @Id
    protected String partnerId;

    protected Integer kettlePartnerId;

    protected String partnerName;

    protected String partnerCode;

    protected String partnerAuthKey;

    protected PartnerStatus partnerStatus;

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerAuthKey() {
        return partnerAuthKey;
    }

    public void setPartnerAuthKey(String partnerAuthKey) {
        this.partnerAuthKey = partnerAuthKey;
    }

    public PartnerStatus getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(PartnerStatus partnerStatus) {
        this.partnerStatus = partnerStatus;
    }

    public String getPartnerCode() {
        return partnerCode;
    }

    public void setPartnerCode(String partnerCode) {
        this.partnerCode = partnerCode;
    }

    public Integer getKettlePartnerId() {
        return kettlePartnerId;
    }

    public void setKettlePartnerId(Integer kettlePartnerId) {
        this.kettlePartnerId = kettlePartnerId;
    }
}
