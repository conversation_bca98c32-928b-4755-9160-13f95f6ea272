package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "item_id",
        "item_in_stock"
})
public class ZomatoProductStock {

    @JsonProperty("item_id")
    private String itemId;

    @JsonProperty("item_in_stock")
    private Integer itemInStock;

    @JsonProperty("item_id")
    public String getItemId() {
        return itemId;
    }

    @JsonProperty("item_id")
    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    @JsonProperty("item_in_stock")
    public Integer getItemInStock() {
        return itemInStock;
    }

    @JsonProperty("item_in_stock")
    public void setItemInStock(Integer itemInStock) {
        this.itemInStock = itemInStock;
    }
}
