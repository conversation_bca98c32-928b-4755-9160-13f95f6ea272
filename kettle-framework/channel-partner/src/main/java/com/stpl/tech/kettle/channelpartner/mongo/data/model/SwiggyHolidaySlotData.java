package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlRootElement;

@Document
@XmlRootElement(name = "SwiggyHolidaySlotData")
public class SwiggyHolidaySlotData {

    @Id
    private String id;
    @JsonProperty("eventId")
    private String eventId;
    @JsonProperty("eventType")
    private String eventType;
    @JsonProperty("timestamp")
    private String timestamp;
    @JsonProperty("restaurantId")
    private String restaurantId;
    @JsonProperty("partnerType")
    private Integer partnerType;
    @JsonProperty("partnerId")
    private String partnerId;
    @JsonProperty("isPenalisation")
    private Boolean isPenalisation;
    @JsonProperty("fromTime")
    private String fromTime;
    @JsonProperty("toTime")
    private String toTime;
    @JsonProperty("updatedOn")
    private String updatedOn;
    @JsonProperty("dispositionName")
    private String dispositionName;
    @JsonProperty("dispositionDescription")
    private String dispositionDescription;
    @JsonProperty("restaurantName")
    private String restaurantName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("eventId")
    public String getEventId() {
        return eventId;
    }

    @JsonProperty("eventId")
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    @JsonProperty("eventType")
    public String getEventType() {
        return eventType;
    }

    @JsonProperty("eventType")
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    @JsonProperty("timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @JsonProperty("restaurantId")
    public String getRestaurantId() {
        return restaurantId;
    }

    @JsonProperty("restaurantId")
    public void setRestaurantId(String restaurantId) {
        this.restaurantId = restaurantId;
    }

    @JsonProperty("partnerType")
    public Integer getPartnerType() {
        return partnerType;
    }

    @JsonProperty("partnerType")
    public void setPartnerType(Integer partnerType) {
        this.partnerType = partnerType;
    }

    @JsonProperty("partnerId")
    public String getPartnerId() {
        return partnerId;
    }

    @JsonProperty("partnerId")
    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    @JsonProperty("isPenalisation")
    public Boolean getIsPenalisation() {
        return isPenalisation;
    }

    @JsonProperty("isPenalisation")
    public void setIsPenalisation(Boolean isPenalisation) {
        this.isPenalisation = isPenalisation;
    }

    @JsonProperty("fromTime")
    public String getFromTime() {
        return fromTime;
    }

    @JsonProperty("fromTime")
    public void setFromTime(String fromTime) {
        this.fromTime = fromTime;
    }

    @JsonProperty("toTime")
    public String getToTime() {
        return toTime;
    }

    @JsonProperty("toTime")
    public void setToTime(String toTime) {
        this.toTime = toTime;
    }

    @JsonProperty("updatedOn")
    public String getUpdatedOn() {
        return updatedOn;
    }

    @JsonProperty("updatedOn")
    public void setUpdatedOn(String updatedOn) {
        this.updatedOn = updatedOn;
    }

    @JsonProperty("dispositionName")
    public String getDispositionName() {
        return dispositionName;
    }

    @JsonProperty("dispositionName")
    public void setDispositionName(String dispositionName) {
        this.dispositionName = dispositionName;
    }

    @JsonProperty("dispositionDescription")
    public String getDispositionDescription() {
        return dispositionDescription;
    }

    @JsonProperty("dispositionDescription")
    public void setDispositionDescription(String dispositionDescription) {
        this.dispositionDescription = dispositionDescription;
    }

    @JsonProperty("restaurantName")
    public String getRestaurantName() {
        return restaurantName;
    }

    @JsonProperty("restaurantName")
    public void setRestaurantName(String restaurantName) {
        this.restaurantName = restaurantName;
    }
}
