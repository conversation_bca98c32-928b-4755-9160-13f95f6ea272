package com.stpl.tech.kettle.channelpartner.core.queue.listener;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;

public class PartnerOrderStateListener implements MessageListener {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerOrderStateListener.class);

    private MessageProducer errorQueue;

    private RedisPublisher redisPublisher;

    public PartnerOrderStateListener(MessageProducer errorQueue, RedisPublisher redisPublisher) {
        this.errorQueue = errorQueue;
        this.redisPublisher = redisPublisher;
    }

    @Override
    public void onMessage(Message message) {
        try {
            LOG.info("On Message " + message.getJMSMessageID());
            if (message instanceof SQSObjectMessage) {
                SQSObjectMessage object = (SQSObjectMessage) message;
                if (object.getObject() instanceof PartnerOrderStateUpdate) {
                    message.acknowledge();
                    PartnerOrderStateUpdate response = (PartnerOrderStateUpdate) object.getObject();
                    LOG.info("ORDER STATUS EVENT RECEIVED OBJECT::::", new Gson().toJson(response));
                    if (processMessage(response)) {
                        message.acknowledge();
                    }
                }
            }
            if (message instanceof SQSTextMessage) {
                SQSTextMessage object = (SQSTextMessage) message;
                if (object.getText() != null) {
                    message.acknowledge();
                    String response = object.getText();
                    PartnerOrderStateUpdate event = ChannelPartnerUtils.deserializeObject(response, PartnerOrderStateUpdate.class);
                    LOG.info("ORDER STATUS EVENT RECEIVED TEXT::::", new Gson().toJson(event));
                    if (processMessage(event)) {
                        message.acknowledge();
                    }
                }
            }
        } catch (JMSException e) {
            LOG.error("Error while saving the message", e);
            try {
                LOG.info("Publishing Error Message to Error Queue " + message.getJMSMessageID());
                errorQueue.send(message);
            } catch (JMSException e1) {
                LOG.error("Error while saving the message to error queue", e);
            }
        }
    }

    private boolean processMessage(PartnerOrderStateUpdate event) {
        LOG.info("Got Message " + new Gson().toJson(event));
        PartnerActionEvent partnerActionEvent = new PartnerActionEvent();
        partnerActionEvent.setEventData(event);
        partnerActionEvent.setBrandId(event.getBrandId());
        partnerActionEvent.setEventType(PartnerActionEventType.ORDER_STATUS_UPDATE);
        partnerActionEvent.setPartner(false);
        redisPublisher.publish("COMMON", new Gson().toJson(partnerActionEvent));
        return true;
    }
}
