package com.stpl.tech.kettle.channelpartner.core.redis.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class ZomatoListenerImpl implements MessageListener {

    private static final Logger LOG = LoggerFactory.getLogger(ZomatoListenerImpl.class);

    @Autowired
    private RedisMessageListenerContainer redisMessageListenerContainer;

    @Autowired
    private ZomatoService zomatoService;

    private ChannelTopic channelTopic = new ChannelTopic("ZOMATO");

    @PostConstruct
    public void subscribe() {
        redisMessageListenerContainer.addMessageListener(this, channelTopic);
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            PartnerActionEvent event = new Gson().fromJson(message.toString(), PartnerActionEvent.class);
            if (event != null) {
                LOG.info("Processing Zomato action: " + new Gson().toJson(event));
                try {
                    Integer unitId;
                    ObjectMapper mapper = new ObjectMapper();
                    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    switch (event.getEventType()) {
                        case UNIT_PRODUCT_STOCK:
                            LOG.info("Processing ZOMATO UNIT_PRODUCT_STOCK EVENT");
                            UnitProductsStockEvent unitProductsStockEvent = mapper.readValue(new Gson().toJson(event.getEventData()), UnitProductsStockEvent.class);
                            if (unitProductsStockEvent.getProductIds() != null && unitProductsStockEvent.getProductIds().size() > 0) {
                                zomatoService.updateZomatoStock(unitProductsStockEvent);
                            }
                            break;
                        case UPDATE_ALL_UNIT_MENU:
                        	LOG.info("Processing Zomato UPDATE_UNIT_MENU EVENT");
                            UnitMenuAddVO eventD = mapper.readValue(new Gson().toJson(event.getEventData()), UnitMenuAddVO.class);
                            zomatoService.pushMenuToUnits(eventD.getUnitIdsForMenu(), eventD.getBrandId(), eventD.getEmployeeId(), eventD.getKettlePartnerId(), eventD.getMenuType());
                            break;
                        case UPDATE_MENU:
                            //TODO Remove this as it is obsolete
                            LOG.info("Processing Zomato UPDATE_MENU EVENT");
                            unitId = (Integer) event.getEventData();
                            //TODO fix this for region level menu although it is not used anywhere
                            zomatoService.updateZomatoMenu(unitId, false, event.getEventType(), 1, null);
                            break;
                        case UPDATE_UNIT_MENU:
                            //TODO Remove this as it is obsolete
                            LOG.info("Processing Zomato UPDATE_UNIT_MENU EVENT");
                            UnitMenuAddVO eventData = mapper.readValue(new Gson().toJson(event.getEventData()), UnitMenuAddVO.class);
                            zomatoService.updateZomatoMenu(eventData.getUnitId(), eventData.getNew(), event.getEventType(), eventData.getBrandId(), eventData.getEmployeeId());
                            break;
                        case INVENTORY_UPDATE:
                            LOG.info("Processing Zomato INVENTORY_UPDATE EVENT");
                            List data = (List) event.getEventData();
                            List<Integer> unitIds = new ArrayList<>();
                            for (Object o : data) {
                                unitIds.add((int) Double.parseDouble(o.toString()));
                            }
                            if (Objects.nonNull((Integer)event.getBrandId())) {
                                zomatoService.refreshUnitInventory(unitIds,(Integer)event.getBrandId());
                            } else {
                                zomatoService.refreshUnitInventory(unitIds);
                            }
                            break;
                        case TAKEAWAY_STATUS_UPDATE:
                            LOG.info("Processing Zomato TAKEAWAY_STATUS_UPDATE EVENT");
                            UnitPartnerStatusVO unitPartnerStatusVO = mapper.readValue(new Gson().toJson(event.getEventData()), UnitPartnerStatusVO.class);
                            zomatoService.updateOutletTakeawayStatus(unitPartnerStatusVO);
                            break;
                        case SCHEDULED_MENU_PUSH:
                        	LOG.info("Processing Scheduled Zomato UPDATE_UNIT_MENU EVENT");
                            UnitMenuAddVO unitMenuAddVO = mapper.readValue(new Gson().toJson(event.getEventData()), UnitMenuAddVO.class);
                            zomatoService.scheduledMenuPush(unitMenuAddVO.getUnitIdsForMenu(), unitMenuAddVO.getMenuType());
                            break;
                        default:
                            LOG.info(new Gson().toJson(event.getEventData()));
                    }
                } catch (Exception e) {
                    LOG.error("Error processing zomato redis event::::::::::::::: " + event.toString(), e);
                }
            }
        } catch (Exception e) {
            LOG.error("error: ", e);
        }
    }
}
