package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

/**
 * Created by Chaayos on 01-10-2016.
 */
@Service
public class EnvironmentProperties {

	@Autowired
	Environment environment;

	public String getRedisHost() {
		return environment.getProperty("redis.host", "localhost");
	}

	public int getRedisPort() {
		return Integer.valueOf(environment.getProperty("redis.port", "6379"));
	}

	public EnvType getEnvType() {
		return EnvType.valueOf(environment.getProperty("environment.type", "DEV"));
	}

	public String getChannelPartnerClientToken() {
		return environment.getProperty("cp.client.token");
	}

	public String getMongoURI() {
		return environment.getProperty("cp.mongo.uri");
	}

	public String getMongoHost() {
		return environment.getProperty("cp.mongo.host", "localhost");
	}

	public int getMongoPort() {
		return Integer.valueOf(environment.getProperty("cp.mongo.port", "27017"));
	}

	public String getMongoSchema() {
		return environment.getProperty("cp.mongo.schema", "neo");
	}

	public String getMongoUser() {
		return environment.getProperty("cp.mongo.user", "root");
	}

	public String getMongoPass() {
		return environment.getProperty("cp.mongo.pass", "root");
	}

	public String getKettleCRMBasePath() {
		return environment.getProperty("base.path.kettle.crm");
	}

	public String getKettleServiceBasePath() {
		return environment.getProperty("base.path.kettle.service");
	}

	public String getMasterServiceBasePath() {
		return environment.getProperty("base.path.master.service");
	}

	public String getCRMServiceBasePath() {
		return environment.getProperty("base.path.kettle.crm");
	}

	public int getDefaultCustomerId() {
		return Integer.parseInt(environment.getProperty("default.customer.id"));
	}

	public int getSwiggyConfirmationDelayInSeconds() {
		return Integer.parseInt(environment.getProperty("swiggy.confirmation.delay","5"));
	}

    public int getZomatoConfirmationDelayInSeconds() {
        return Integer.parseInt(environment.getProperty("zomato.confirmation.delay","5"));
    }

	public boolean getSendOtp() {
		return Boolean.valueOf(environment.getProperty("send.otp", "true"));
	}

	public String getSwiggyCustomerContact() {
		return environment.getProperty("swiggy.customer.contact","1234567890");
	}

	public String getZomatoCustomerContact(){
		return environment.getProperty("zomato.customer.contact", "1234567890");
	}

	public String getSwiggyAPIKey() {
		return environment.getProperty("swiggy.api.key","Dssd2NfASDr34DTDAa9HlP8MPURVGN5bI0edwedsuay5sada");
	}

	public String getSwiggyMenuAuthorizationKey() {
		return environment.getProperty("swiggy.menu.authorization.key","dXNlcjpjaGVjaw==");
	}

	public String getSwiggyMenuTokenId() {
		return environment.getProperty("swiggy.menu.token.id","1002651b75492a31");
	}

    public String getZomatoAPIKey(int brandId) {
		if(brandId == 0) {
			brandId = 1; //Chaayos brand id
		}
        return environment.getProperty("zomato.api.key"+brandId,"90a38a546af580c03cfd2cc39146daf1");
    }

	public String getBasePath() {
		return environment.getProperty("server.base.dir");
	}

	public boolean slackDuplicateOrders(){
		return Boolean.valueOf(environment.getProperty("slack.duplicate.orders", "false"));
	}

	public String getEmailFilterDomains() {
		return environment.getProperty("email.filter.domains","");
	}

	public String getAwsQueueRegion() {
		return environment.getProperty("aws.queue.region","EU_WEST_1");
	}
	
	public String getDevUrl() {
		return environment.getProperty("base.path.dev.url");
	}

	public String getChannelPartnerRedirectToken() {
		return environment.getProperty("cp.redirect.client.token");
	}
	
	public boolean getCpCondimentFlag() {
		return  Boolean.valueOf(environment.getProperty("channelpartner.condiment.flag"));
	}

	public boolean isScheduledMenuPush() {
		return  Boolean.valueOf(environment.getProperty("scheduled.menu.push", "false"));
	}

    public String getChannelPartnerBasePath() {
		return environment.getProperty("base.path.channel.partner");
    }

    public String getKnockNotificationUrl() {
		return  environment.getProperty("knock.notification.url");
    }

	public String getKnockMasterToken() {
		return environment.getProperty("knock.master.token");
	}

	public String getKnockBaseUrl() {
		return  environment.getProperty("knock.base.url");
	}

	public boolean isTestingModeforNotification(){
		return  Boolean.valueOf(environment.getProperty("notification.testingMode.flag","false"));
	}

	public boolean isTestingModeEnabled(){
		return Boolean.valueOf(environment.getProperty("cp.test.mode", "false"));
	}
	public String getLiveOrderBaseUrl() {
		return environment.getProperty("live.orders.base.url","http://dev.kettle.chaayos.com:8080");
	}

	public String getLiveOrdersToken(){
		return  environment.getProperty("live.orders.token");
	}

	public boolean isDefaultZomatoCustomerFlow(){
		return Boolean.valueOf(environment.getProperty("default.zomato.customer.flow", "true"));
	}

	public boolean getEditOrderCompositionBasisRemark(){
		return Boolean.valueOf(environment.getProperty("edit.order.composition.basis.delivery.remark", "false"));
	}

	public Integer getSwiggyCustomerId() {
		return Integer.parseInt(environment.getProperty("default.swiggy.customer.id", "67456"));
	}

	public Integer getZomatoCustomerId() {
		return Integer.parseInt(environment.getProperty("default.zomato.customer.id", "3527255"));
	}

	public boolean isDefaultSwiggyCustomerFlow(){
		return Boolean.valueOf(environment.getProperty("default.swiggy.customer.flow", "false"));
	}

	public String getInventoryBaseUrl(){
		return environment.getProperty("inventory.base.url", "https://relax.chaayos.com");
	}
	public Boolean callInventoryForWebOrders(){
		return Boolean.valueOf(environment.getProperty("call.inventory.for.web.orders", "true"));
	}
}
