package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
	"vendorEntityId"
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZomatoCatVarPropertyValues {

	@JsonProperty("vendorEntityId")
	private String vendorEntityId;
	
	public String getVendorEntityId() {
		return vendorEntityId;
	}

	public void setVendorEntityId(String vendorEntityId) {
		this.vendorEntityId = vendorEntityId;
	}
	
	@Override
	public String toString() {
		return "ZomatoCatVarPropertyValues [vendorEntityId=" + vendorEntityId + "]";
	}

	@Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoCatVarPropertyValues that = (ZomatoCatVarPropertyValues) o;

        return new EqualsBuilder()
                .append(vendorEntityId, that.vendorEntityId)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(vendorEntityId)
                .toHashCode();
    }
	
	
}
