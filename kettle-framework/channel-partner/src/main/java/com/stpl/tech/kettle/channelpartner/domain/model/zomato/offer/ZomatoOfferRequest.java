package com.stpl.tech.kettle.channelpartner.domain.model.zomato.offer;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "outletId", "restaurantOffers" })
public class ZomatoOfferRequest {

	@JsonProperty("outletId")
	private String outletId;
	@JsonProperty("restaurantOffers")
	private List<ZomatoRestaurantOffers> restaurantOffers;

	@JsonProperty("outletId")
	public String getOutletId() {
		return outletId;
	}

	@JsonProperty("outletId")
	public void setOutletId(String outletId) {
		this.outletId = outletId;
	}

	@JsonProperty("restaurantOffers")
	public List<ZomatoRestaurantOffers> getRestaurantOffers() {
		return restaurantOffers;
	}

	@JsonProperty("restaurantOffers")
	public void setRestaurantOffers(List<ZomatoRestaurantOffers> restaurantOffers) {
		this.restaurantOffers = restaurantOffers;
	}

	@Override
	public String toString() {
		return "ZomatoOfferRequest [outletId=" + outletId + ", restaurantOffers=" + restaurantOffers + "]";
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(outletId).append(restaurantOffers).toHashCode();
	}

	@Override
	public boolean equals(Object other) {
		if (other == this) {
			return true;
		}
		if ((other instanceof ZomatoOfferRequest) == false) {
			return false;
		}
		ZomatoOfferRequest rhs = ((ZomatoOfferRequest) other);
		return new EqualsBuilder().append(outletId, rhs.outletId).append(restaurantOffers, rhs.restaurantOffers)
				.isEquals();
	}

}
