package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "day","slots"})
public class DayUpdate {

    @JsonProperty("day")
    public String day;

    @JsonProperty("slots")
    public List<Slot> slots;

    @JsonProperty("day")
    public String getDay() {
        return day;
    }

    @JsonProperty("day")
    public void setDay(String day) {
        this.day = day;
    }

    @JsonProperty("slots")
    public List<Slot> getSlots() {
        return slots;
    }

    @JsonProperty("slots")
    public void setSlots(List<Slot> slots) {
        this.slots = slots;
    }
}
