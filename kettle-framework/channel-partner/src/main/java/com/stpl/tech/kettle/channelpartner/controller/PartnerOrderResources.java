package com.stpl.tech.kettle.channelpartner.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPendingOrderRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.PARTNER_ORDER_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PARTNER_ORDER_ROOT_CONTEXT) // 'v1/partner-order'
public class PartnerOrderResources extends ChannelPartnerAbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerOrderResources.class);

    @Autowired
    private PartnerOrderService partnerOrderService;
    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @RequestMapping(method = RequestMethod.POST, value = "get", produces = MediaType.APPLICATION_JSON)
    public List<PartnerOrderDetail> getPartnerOrder(@RequestBody PartnerOrderDetail request) throws ChannelPartnerException {
        LOG.info("Request to get partner order : " + new Gson().toJson(request));
        return partnerOrderService.getPartnerOrder(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-pending-orders", produces = MediaType.APPLICATION_JSON)
    public Map<String, List<PartnerOrderDetail>> getPartnerOrder(@RequestBody PartnerPendingOrderRequest request) throws ChannelPartnerException {
        if(request.getHours() == null){
            request.setHours(2);
        }
        return partnerOrderService.getPartnerPendingOrders(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "call-swiggy-support", produces = MediaType.APPLICATION_JSON)
    public boolean callSwiggyPartnerSupport(@RequestParam String orderId) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to call swiggy partner support for order id: " + orderId);
        return partnerOrderService.callSwiggyPartnerSupport(orderId);
        //Call Swiggy Support
    }

    @RequestMapping(method = RequestMethod.POST, value = "mark-resolved", produces = MediaType.APPLICATION_JSON)
    public boolean markOrderIssuesResolved(@RequestParam String orderId) throws ChannelPartnerException {
        LOG.info("Request to mark order resolved for order id: " + orderId);
        return partnerOrderService.markOrderResolved(orderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-kettle-order", produces = MediaType.APPLICATION_JSON)
    public boolean addKettleOrderForCheckedOrder(@RequestParam String orderId , @RequestParam String kettleOrderId ,@RequestParam Integer unitId ) throws ChannelPartnerException {
        LOG.info("Request to Add Kettle Order :: {} To Partner Order :: {}  " ,kettleOrderId, orderId);
        return partnerOrderService.addKettleOrderId(kettleOrderId,orderId,unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "manual-process", produces = MediaType.APPLICATION_JSON)
    public boolean manualProcess(@RequestParam String orderId) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to manual process order id: " + orderId);
        return partnerOrderService.manualProcess(orderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "mark-cancelled", produces = MediaType.APPLICATION_JSON)
    public boolean markCancelled(@RequestParam String kettleOrderId) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to mark cancel order id: " + kettleOrderId);
        return partnerOrderService.markCancelled(kettleOrderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "force-process", produces = MediaType.APPLICATION_JSON)
    public boolean skipInventoryCheck(@RequestParam String orderId) throws ChannelPartnerException, URISyntaxException {
        LOG.info("Request to place order without inventory order id: " + orderId);
        return partnerOrderService.manualProcessWithSkipInventory(orderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "swiggy-rider-time", produces = MediaType.APPLICATION_JSON)
    public Integer getSwiggyRiderTimeOfArrival(@RequestBody String orderId) throws ChannelPartnerException {
        LOG.info("Request to get Swiggy rider time of arrival: " + orderId);
        return partnerOrderService.getSwiggyRiderTimeOfArrival(orderId);
    }

    @RequestMapping(method = RequestMethod.GET,value = "update-cafe-timing",produces = MediaType.APPLICATION_JSON)
    public void updateCafeBuisnessTiming(){

    }

    @RequestMapping(method = RequestMethod.POST, value = "get-product-detail-by-name-orderId", produces = MediaType.APPLICATION_JSON)
    public PartnerOrderDetail getProductDetailByNameAndOrderId(@RequestBody PartnerRequest partnerRequest) {
        return partnerOrderDao.getProductDetailByNameAndOrderId(partnerRequest.getPartnerName(),partnerRequest.getPartnerOrderId());
    }

}
