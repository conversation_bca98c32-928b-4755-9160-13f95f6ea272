package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;


public class ZomatoDeliveryStatusData {

    @JsonProperty("outlet_delivery_status")
    private Integer outletDeliveryStatus;
    @JsonProperty("zomato_online_order_status")
    private Integer zomatoOnlineOrderStatus;

    @JsonProperty("outlet_delivery_status")
    public Integer getOutletDeliveryStatus() {
        return outletDeliveryStatus;
    }

    @JsonProperty("outlet_delivery_status")
    public void setOutletDeliveryStatus(Integer outletDeliveryStatus) {
        this.outletDeliveryStatus = outletDeliveryStatus;
    }

    @JsonProperty("zomato_online_order_status")
    public Integer getZomatoOnlineOrderStatus() {
        return zomatoOnlineOrderStatus;
    }

    @JsonProperty("zomato_online_order_status")
    public void setZomatoOnlineOrderStatus(Integer zomatoOnlineOrderStatus) {
        this.zomatoOnlineOrderStatus = zomatoOnlineOrderStatus;
    }
}
