package com.stpl.tech.kettle.channelpartner.core.task;

import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarkStockInOOSItemsTask implements Runnable{

    private static final Logger LOG = LoggerFactory.getLogger(MarkStockInOOSItemsTask.class);

    private String partnerOrderId;

    private UnitPartnerBrandKey key;

    private SwiggyService swiggyService;


    @Override
    public void run() {
        try {
            MDC.put("request.id",partnerOrderId );
            swiggyService.markOOSItemsStockIn(partnerOrderId,key);
            MDC.clear();
        } catch (Exception e) {
            LOG.error("Error While Stock In OOS Marked Items For  Swiggy  Order  :::: {} " ,this.partnerOrderId, e);
        }
    }
}
