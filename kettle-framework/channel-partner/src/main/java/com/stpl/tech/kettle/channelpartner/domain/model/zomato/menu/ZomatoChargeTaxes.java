package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_type",
        "taxes"
})
public class ZomatoChargeTaxes {


    @JsonProperty("order_type")
    private String orderType;
    @JsonProperty("taxes")
    private List<String> taxes;

    @JsonProperty("order_type")
    public String getOrderType() {
        return orderType;
    }

    @JsonProperty("order_type")
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    @JsonProperty("taxes")
    public List<String> getTaxes() {
        return taxes;
    }

    @JsonProperty("taxes")
    public void setTaxes(List<String> taxes) {
        this.taxes = taxes;
    }
}