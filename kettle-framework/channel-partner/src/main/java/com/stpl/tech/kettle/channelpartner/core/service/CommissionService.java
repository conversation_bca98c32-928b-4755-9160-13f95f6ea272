package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.domain.model.PartnerAOVRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.Order;

import java.io.IOException;

public interface CommissionService {
   void commissionCalculation(Order order, PartnerOrderDetail partnerOrderDetail) throws IOException;

    void calculateMonthWiseAOV(PartnerAOVRequest partnerAOVRequest);
}
