package com.stpl.tech.kettle.channelpartner.core.converters;

import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxDetailKey;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxPayingEntity;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.Item;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderable;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class AbstractConverters {
    private static final Logger LOG = LoggerFactory.getLogger(AbstractConverters.class);

    public static void calculateSaving(TransactionDetail transactionDetail, Order order) {

        BigDecimal saving;
        BigDecimal taxWithoutDiscount = BigDecimal.ZERO;
        BigDecimal totalAmount = transactionDetail.getTaxes().get(0).getTotal();
        for (TaxDetail taxDetail : transactionDetail.getTaxes()) {
            taxWithoutDiscount = ChannelPartnerUtils.add(taxWithoutDiscount,
                    ChannelPartnerUtils.percentOfWithScale10(taxDetail.getPercentage(), taxDetail.getTotal()));
        }
        BigDecimal payableWithoutDiscount = ChannelPartnerUtils.add(taxWithoutDiscount, totalAmount);
        BigDecimal taxable = transactionDetail.getTaxes().get(0).getTaxable();
        BigDecimal tax = transactionDetail.getTax();
        BigDecimal payableWithDiscount = ChannelPartnerUtils.add(taxable, tax);
        saving = ChannelPartnerUtils.subtract(payableWithoutDiscount, payableWithDiscount).setScale(0, RoundingMode.HALF_UP);
        if (saving.compareTo(BigDecimal.ZERO) < 0) {
            transactionDetail.setSavings(BigDecimal.ZERO);
        } else {
            transactionDetail.setSavings(saving);
        }
    }

    public static OrderItem getDefaultOrderItemFromRecipe(Product product, int quantity, MasterDataCache masterDataCache, boolean isInterState,
                                                          Map<String, TaxDataVO> taxMap, boolean withRecipe, BigDecimal discountPercent,
                                                          String discountReason, String dimension, Integer unitId, BigDecimal cartValue,
                                                          boolean taxDeductedByPartner, String partnerTaxType) {
        OrderItem orderItem = new OrderItem();
        orderItem.setProductId(product.getId());
        orderItem.setPartnerTaxType(partnerTaxType);
        orderItem.setProductName(product.getName());
        orderItem.setProductAliasName(Objects.nonNull(orderItem.getProductAliasName()) ? orderItem.getProductAliasName() : product.getName());
        orderItem.setProductCategory(new IdCodeName(product.getType(), "", ""));
        orderItem.setProductSubCategory(new IdCodeName(product.getSubType(), "", ""));
        orderItem.setQuantity(quantity);
        ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
        complimentaryDetail.setIsComplimentary(false);
        orderItem.setComplimentaryDetail(complimentaryDetail);
        orderItem.setBillType(product.getBillType());
        //orderItem.setItemCode();
        orderItem.setCode(product.getTaxCode());
        //orderItem.setReasonId();
        orderItem.setBookedWastage(false);
        //orderItem.setCardType();
        orderItem.setTakeAway(false);
        orderItem.setTaxDeductedByPartner(taxDeductedByPartner);

        // Finding dimension for product
        ProductPrice productPrice = setOrderItemPrice(dimension, product, orderItem);

        if (productPrice == null) {
            productPrice = product.getPrices().get(0);
        }
        if (product.getId() == 1043) {
            if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("PERCENTAGE")) {
                BigDecimal price = ChannelPartnerUtils.multiply(ChannelPartnerUtils.divide(masterDataCache.getUnit(unitId)
                        .getPackagingValue(), new BigDecimal(100)), cartValue);
                productPrice.setPrice(price);
            } else if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("FIXED")) {
                productPrice.setPrice(masterDataCache.getUnit(unitId).getPackagingValue());
            }
        }
        orderItem.setPrice(productPrice.getPrice().setScale(2, RoundingMode.HALF_UP));
        RecipeDetail recipeDetail = productPrice.getRecipe();
        if (recipeDetail != null) {
            orderItem.setRecipeId(recipeDetail.getRecipeId());
        }
        OrderItemComposition orderItemComposition = new OrderItemComposition();
        if (withRecipe) {
            if (recipeDetail != null) {
                //setting ingredient products
                recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
                    for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                        if (ingredientProductDetail.isDefaultSetting()) {
                            orderItemComposition.getProducts().add(ingredientProductDetail);
                        }
                    }
                });
                //Setting Ingredient Variants
                recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                    for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                        if (ingredientVariantDetail.isDefaultSetting()) {
                            orderItemComposition.getVariants().add(ingredientVariantDetail);
                        }
                    }
                });
            }
        }
        orderItem.setComposition(orderItemComposition);

        orderItem.setTotalAmount(ChannelPartnerUtils.multiply(orderItem.getPrice(), new BigDecimal(orderItem.getQuantity())));
        //Setting discount data
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setTotalDiscount(BigDecimal.ZERO);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        discountDetail.setDiscountCode(0);
        //discountDetail.setDiscountReason();
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(discountPercent);
        percentageDetail.setValue(BigDecimal.ZERO);
        if (discountPercent.compareTo(BigDecimal.ZERO) > 0) {
            percentageDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(discountPercent, orderItem.getTotalAmount()));
            discountDetail.setDiscountReason(discountReason.equalsIgnoreCase("") ? "ZOMATO" : discountReason);
            discountDetail.setDiscountCode(2004);
        }
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setTotalDiscount(ChannelPartnerUtils.add(discountDetail.getPromotionalOffer(), percentageDetail.getValue())
                .setScale(2, RoundingMode.HALF_UP));
        orderItem.setDiscountDetail(discountDetail);

        orderItem.setAmount(ChannelPartnerUtils.subtract(orderItem.getTotalAmount(),
                discountDetail.getTotalDiscount()).setScale(2, RoundingMode.HALF_UP));

        //Setting taxes
        BigDecimal totalItemTax = BigDecimal.ZERO;
        TaxDataVO taxDataVO = taxMap.get(product.getTaxCode());
        if (!isInterState) {
            if (taxDataVO.getState().getCgst().compareTo(BigDecimal.ZERO) > 0) {
                TaxDetail taxDetail = createTaxDetail("CGST", "GST", taxDataVO.getState().getCgst(),
                        orderItem.getPrice(), orderItem.getQuantity(), orderItem.getDiscountDetail().getTotalDiscount());
                orderItem.getTaxes().add(taxDetail);
                totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
            }
            if (taxDataVO.getState().getSgst().compareTo(BigDecimal.ZERO) > 0) {
                TaxDetail taxDetail = createTaxDetail("SGST/UTGST", "GST", taxDataVO.getState().getSgst(),
                        orderItem.getPrice(), orderItem.getQuantity(), orderItem.getDiscountDetail().getTotalDiscount());
                orderItem.getTaxes().add(taxDetail);
                totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
            }
        } else {
            if (taxDataVO.getState().getIgst().compareTo(BigDecimal.ZERO) > 0) {
                TaxDetail taxDetail = createTaxDetail("IGST", "GST", taxDataVO.getState().getIgst(),
                        orderItem.getPrice(), orderItem.getQuantity(), orderItem.getDiscountDetail().getTotalDiscount());
                orderItem.getTaxes().add(taxDetail);
                totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
            }
        }
        orderItem.setTax(totalItemTax);
        return orderItem;
    }

    public static ProductPrice setOrderItemPrice(String dimension, Product product, OrderItem orderItem) {
        ProductPrice productPrice = null;
        if (dimension == null) {
            //dimension = masterDataCache.getDimensionProfile(product.getDimensionProfileId()).getContent().get(0).getCode();
            dimension = product.getPrices().get(0).getDimension();
            orderItem.setDimension(dimension);
            productPrice = product.getPrices().get(0);
        } else {
            orderItem.setDimension(dimension);
            for (ProductPrice productPrice1 : product.getPrices()) {
                if (productPrice1.getDimension().equalsIgnoreCase(dimension)) {
                    productPrice = productPrice1;
                }
            }
        }
        return productPrice;
    }

    public static String getProductAbsoluteName(String name) {
        if (name.contains("(")) {
            return name.substring(0, name.indexOf("(")).trim();
        }
        return name;
    }

    public static void setOrderItemTaxes(Product product, OrderItem orderItem, Map<String, TaxDataVO> taxMap, DiscountDetail discountDetail, Integer parentQuantity) {
        TaxDataVO taxDataVO = taxMap.get(product.getTaxCode());
        setTaxDetailsToOrderItem(orderItem, discountDetail, taxDataVO, parentQuantity);
    }

    public static BigDecimal aggregateTaxesFromOrderItems(List<OrderItem> orderItems, Map<TaxDetailKey, TaxDetail> taxMap) {
        BigDecimal totalTax = BigDecimal.ZERO;
        for (OrderItem item : orderItems) {
            if (item.getCode().equalsIgnoreCase("COMBO")) {
                calculateTaxesForComboItems(item,taxMap);
                /*Map<TaxDetailKey, TaxDetail> taxMapCombo = aggregateTaxesForComboItem(item.getComposition().getMenuProducts(), item.getTotalAmount());
                for (TaxDetailKey key : taxMapCombo.keySet()) {
                    TaxDetail tdc = taxMapCombo.get(key);
                    TaxDetail td = taxMap.get(key);
                    if (td == null) {
                        td = (TaxDetail) ChannelPartnerUtils.deepClone(tdc);
                    } else {
                        td.setTotal(td.getTotal().add(tdc.getTotal()));
                        td.setTaxable(td.getTaxable().add(tdc.getTaxable()));
                    }
                    taxMap.put(key, td);
                }*/
            }
            calculateTaxesForNonComboItems(item,taxMap);
            /*for (TaxDetail taxDetail : item.getTaxes()) {
                TaxDetailKey key = new TaxDetailKey(taxDetail.getType(), taxDetail.getCode(), taxDetail.getPercentage());
                TaxDetail taxd = taxMap.get(key);
                if (taxd == null) {
                    taxd = (TaxDetail) ChannelPartnerUtils.deepClone(taxDetail);
                } else {
                    taxd.setTotal(taxd.getTotal().add(taxDetail.getTotal()));
                    taxd.setTaxable(taxd.getTaxable().add(taxDetail.getTaxable()));
                }
                taxMap.put(key, taxd);
            }*/
        }
        totalTax =calculateTotalTaxFromTaxMap(taxMap);
        /*for (TaxDetailKey key : taxMap.keySet()) {
            TaxDetail td = taxMap.get(key);
            td.setValue(ChannelPartnerUtils.percentageOfWithScale10(td.getPercentage(), td.getTaxable()));
            totalTax = totalTax.add(td.getValue());
            taxMap.put(key, td);
        }*/
        return totalTax;
    }

    public static BigDecimal calculateTotalTaxFromTaxMap(Map<TaxDetailKey, TaxDetail> taxMap) {
        BigDecimal totalTax = BigDecimal.ZERO;
        for (TaxDetailKey key : taxMap.keySet()) {
            TaxDetail td = taxMap.get(key);
            td.setValue(ChannelPartnerUtils.percentageOfWithScale10(td.getPercentage(), td.getTaxable()));
            totalTax = totalTax.add(td.getValue());
            taxMap.put(key, td);
        }
        return totalTax;
    }

    public static void calculateTaxesForNonComboItems(OrderItem item, Map<TaxDetailKey, TaxDetail> taxMap) {
        for (TaxDetail taxDetail : item.getTaxes()) {
            TaxDetailKey key = new TaxDetailKey(taxDetail.getType(), taxDetail.getCode(), taxDetail.getPercentage());
            TaxDetail taxd = taxMap.get(key);
            if (taxd == null) {
                taxd = (TaxDetail) ChannelPartnerUtils.deepClone(taxDetail);
            } else {
                taxd.setTotal(taxd.getTotal().add(taxDetail.getTotal()));
                taxd.setTaxable(taxd.getTaxable().add(taxDetail.getTaxable()));
            }
            taxMap.put(key, taxd);
        }
    }

    public static void calculateTaxesForComboItems(OrderItem item, Map<TaxDetailKey, TaxDetail> taxMap) {
        Map<TaxDetailKey, TaxDetail> taxMapCombo = aggregateTaxesForComboItem(item.getComposition().getMenuProducts(), item.getTotalAmount());
        for (TaxDetailKey key : taxMapCombo.keySet()) {
            TaxDetail tdc = taxMapCombo.get(key);
            TaxDetail td = taxMap.get(key);
            if (td == null) {
                td = (TaxDetail) ChannelPartnerUtils.deepClone(tdc);
            } else {
                td.setTotal(td.getTotal().add(tdc.getTotal()));
                td.setTaxable(td.getTaxable().add(tdc.getTaxable()));
            }
            taxMap.put(key, td);
        }
    }

    public static BigDecimal aggregateCollectionTaxesFromOrderItems(List<OrderItem> orderItems, Map<TaxDetailKey, TaxDetail> taxMap) {
        BigDecimal totalTax = BigDecimal.ZERO;
        for (OrderItem item : orderItems) {
            boolean sourceTaxflag = true;
            if(Objects.nonNull(item.getPartnerTaxType())) {
                if (item.getPartnerTaxType().equals(TaxPayingEntity.PARTNER.getZomato())) {
                    sourceTaxflag = false;
                } else if (item.getPartnerTaxType().equals(TaxPayingEntity.PARTNER.getSwiggyNew())) {
                    sourceTaxflag = false;
                }
            }
            if (sourceTaxflag) {
                if (item.getCode().equalsIgnoreCase("COMBO")) {
                    Map<TaxDetailKey, TaxDetail> taxMapCombo = aggregateCollectionTaxesForComboItem(item.getComposition().getMenuProducts(), item.getTotalAmount());
                    for (TaxDetailKey key : taxMapCombo.keySet()) {
                        TaxDetail tdc = taxMapCombo.get(key);
                        TaxDetail td = taxMap.get(key);
                        if (td == null) {
                            td = (TaxDetail) ChannelPartnerUtils.deepClone(tdc);
                        } else {
                            td.setTotal(td.getTotal().add(tdc.getTotal()));
                            td.setTaxable(td.getTaxable().add(tdc.getTaxable()));
                        }
                        taxMap.put(key, td);
                    }
                }
                for (TaxDetail taxDetail : item.getTaxes()) {
                    TaxDetailKey key = new TaxDetailKey(taxDetail.getType(), taxDetail.getCode(), taxDetail.getPercentage());
                    TaxDetail taxd = taxMap.get(key);
                    if (taxd == null) {
                        taxd = (TaxDetail) ChannelPartnerUtils.deepClone(taxDetail);
                    } else {
                        taxd.setTotal(taxd.getTotal().add(taxDetail.getTotal()));
                        taxd.setTaxable(taxd.getTaxable().add(taxDetail.getTaxable()));
                    }
                    taxMap.put(key, taxd);
                }
            }
        }
        for (TaxDetailKey key : taxMap.keySet()) {
            TaxDetail td = taxMap.get(key);
            td.setValue(ChannelPartnerUtils.percentageOfWithScale10(td.getPercentage(), td.getTaxable()));
            totalTax = totalTax.add(td.getValue());
            taxMap.put(key, td);
        }
        return totalTax;
    }

    private static Map<TaxDetailKey, TaxDetail> aggregateTaxesForComboItem(List<OrderItem> orderItems, BigDecimal parentTotalAmount) {
        Map<TaxDetailKey, TaxDetail> taxMapCombo = new HashMap<>();
        for (OrderItem item : orderItems) {
            for (TaxDetail taxDetail : item.getTaxes()) {
                TaxDetailKey key = new TaxDetailKey(taxDetail.getType(), taxDetail.getCode(), taxDetail.getPercentage());
                TaxDetail taxd = taxMapCombo.get(key);
                if (taxd == null) {
                    taxd = (TaxDetail) ChannelPartnerUtils.deepClone(taxDetail);
                } else {
                    taxd.setTotal(taxd.getTotal().add(taxDetail.getTotal()));
                    taxd.setTaxable(taxd.getTaxable().add(taxDetail.getTaxable()));
                }
                taxMapCombo.put(key, taxd);
            }
        }
        for (TaxDetailKey key : taxMapCombo.keySet()) {
            TaxDetail td = taxMapCombo.get(key);
            td.setTotal(parentTotalAmount);
            taxMapCombo.put(key, td);
        }
        return taxMapCombo;
    }

    private static Map<TaxDetailKey, TaxDetail> aggregateCollectionTaxesForComboItem(List<OrderItem> orderItems, BigDecimal parentTotalAmount) {
        Map<TaxDetailKey, TaxDetail> taxMapCombo = new HashMap<>();
        for (OrderItem item : orderItems) {
            boolean sourceTaxflag = true;
            if (item.getPartnerTaxType().equals(TaxPayingEntity.PARTNER.getZomato())) {
                sourceTaxflag = false;
            }else if(item.getPartnerTaxType().equals(TaxPayingEntity.PARTNER.getSwiggyNew())) {
                sourceTaxflag = false;
            }
            if (sourceTaxflag) {
                for (TaxDetail taxDetail : item.getTaxes()) {
                    TaxDetailKey key = new TaxDetailKey(taxDetail.getType(), taxDetail.getCode(), taxDetail.getPercentage());
                    TaxDetail taxd = taxMapCombo.get(key);
                    if (taxd == null) {
                        taxd = (TaxDetail) ChannelPartnerUtils.deepClone(taxDetail);
                    } else {
                        taxd.setTotal(taxd.getTotal().add(taxDetail.getTotal()));
                        taxd.setTaxable(taxd.getTaxable().add(taxDetail.getTaxable()));
                    }
                    taxMapCombo.put(key, taxd);
                }
            }
        }
        for (TaxDetailKey key : taxMapCombo.keySet()) {
            TaxDetail td = taxMapCombo.get(key);
            td.setTotal(parentTotalAmount);
            taxMapCombo.put(key, td);
        }
        return taxMapCombo;
    }

    private static TaxDetail createTaxDetail(String taxCode, String taxType, BigDecimal percentage, BigDecimal price, Integer quantity,
                                             BigDecimal discount) {
        TaxDetail taxDetail = new TaxDetail();
        taxDetail.setCode(taxCode);
        taxDetail.setPercentage(percentage);
        taxDetail.setTaxable(ChannelPartnerUtils.subtract(ChannelPartnerUtils.multiply(price, new BigDecimal(quantity)), discount));
        taxDetail.setTotal(ChannelPartnerUtils.multiply(price, new BigDecimal(quantity)));
        taxDetail.setType(taxType);
        taxDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(taxDetail.getPercentage(), taxDetail.getTaxable()));
        return taxDetail;
    }

    private static void addTaxAndTotalAmount(OrderItem orderItem, Map<String, BigDecimal> map) {
        BigDecimal total = orderItem.getPrice().multiply(BigDecimal.valueOf(orderItem.getQuantity()));
        map.put("totalAmount", map.get("totalAmount").add(total));
        for (TaxDetail taxDetail : orderItem.getTaxes()) {
            map.put("taxWithoutDiscount", map.get("taxWithoutDiscount").add(ChannelPartnerUtils.percentageOfWithScale10(taxDetail.getPercentage(), total)));
        }
    }

    private static void setTaxDetailsToOrderItem(OrderItem orderItem, DiscountDetail discountDetail, TaxDataVO taxDataVO, Integer parentQuantity) {
        BigDecimal totalItemTax = BigDecimal.ZERO;
        Integer qty = orderItem.getQuantity() * parentQuantity;
        if (false) { //interstate is not supported
            if (taxDataVO.getState().getIgst().compareTo(BigDecimal.ZERO) > 0) {
                TaxDetail taxDetail = AbstractConverters.createTaxDetail("IGST", "GST", taxDataVO.getState().getIgst(),
                        orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                orderItem.getTaxes().add(taxDetail);
                totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
            }
        } else {
            if (taxDataVO.getState().getCgst().compareTo(BigDecimal.ZERO) > 0) {
                TaxDetail taxDetail = AbstractConverters.createTaxDetail("CGST", "GST", taxDataVO.getState().getCgst(),
                        orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                orderItem.getTaxes().add(taxDetail);
                totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
            }
            if (taxDataVO.getState().getSgst().compareTo(BigDecimal.ZERO) > 0) {
                TaxDetail taxDetail = AbstractConverters.createTaxDetail("SGST/UTGST", "GST", taxDataVO.getState().getSgst(),
                        orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                orderItem.getTaxes().add(taxDetail);
                totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
            }
        }
        orderItem.setTax(totalItemTax);
    }

    public static BigDecimal getComboTotalPrice(Map<Integer, Product> products,
                                                IngredientProductDetail ingredientProductDetail, Integer comboQuantity) {
        BigDecimal comboTotalPrice = BigDecimal.ZERO;
        boolean dimensionMatched = false;
        if (products.containsKey(ingredientProductDetail.getProduct().getProductId())) {
            Product comboConstituent = products.get(ingredientProductDetail.getProduct().getProductId());
            String comboConstituentDimension = ingredientProductDetail.getDimension().getCode();
            BigDecimal quantity = ingredientProductDetail.getQuantity().multiply(BigDecimal.valueOf(comboQuantity));
            for (ProductPrice comboConstituentPrice : comboConstituent.getPrices()) {
                if (comboConstituentPrice.getDimension().equalsIgnoreCase(comboConstituentDimension)) {
                    dimensionMatched = true;
                    comboTotalPrice = comboTotalPrice.add(comboConstituentPrice.getPrice().multiply(quantity));
                }
            }
            if (!dimensionMatched) {
                comboTotalPrice = comboTotalPrice
                        .add(comboConstituent.getPrices().get(0).getPrice().multiply(quantity));
            }
        }
        return comboTotalPrice;
    }

    public static void addSettlementToOrder(Order order, ZomatoOrderable request, MasterDataCache masterDataCache) {
        Settlement settlement = new Settlement();
        settlement.setAmount(order.getTransactionDetail().getPaidAmount());
        if (request.getCashToBeCollected() > 0) {
            for (Integer key : masterDataCache.getPaymentModes().keySet()) {
                if (masterDataCache.getPaymentModes().get(key).getName()
                        .equalsIgnoreCase(request.getPaymentMode().trim())) {
                    settlement.setMode(key);
                }
            }
        } else {
            settlement.setMode(6); // FOR CREDIT
        }
        if (settlement.getMode() == 6) {
            ExternalSettlement externalSettlement = new ExternalSettlement();
            externalSettlement.setAmount(order.getTransactionDetail().getPaidAmount());
            externalSettlement.setExternalTransactionId(Integer.valueOf(1).toString()); // Zomato credit account id
            settlement.getExternalSettlements().add(externalSettlement);
        }
        order.getSettlements().add(settlement);
    }

    public static TaxDetail createTaxDetailV3(String taxCode, String taxType, BigDecimal totalTaxValue, BigDecimal taxPercent,BigDecimal price, Integer quantity,
                                               BigDecimal discount) {
        TaxDetail taxDetail = new TaxDetail();
        taxDetail.setCode(taxCode);
        taxDetail.setTaxable(ChannelPartnerUtils.subtract(ChannelPartnerUtils.multiply(price, new BigDecimal(quantity)), discount));
        taxDetail.setType(taxType);
        taxDetail.setValue(totalTaxValue);
        taxDetail.setTotal(ChannelPartnerUtils.multiply(price, new BigDecimal(quantity)));
        taxDetail.setPercentage(taxPercent);
        return taxDetail;
    }

    public static void setOrderItemTaxesMappedfromSwiggy(Product product, OrderItem orderItem, DiscountDetail discountDetail, Integer parentQuantity, Item item) {
        BigDecimal totalItemTax = BigDecimal.ZERO;
        if (!Objects.isNull(item)) {
            try {
                Integer qty = orderItem.getQuantity() * parentQuantity;
                if (Objects.nonNull(item.getIgstPercent()) && item.getIgstPercent() > 0 && Objects.nonNull(item.getIgst())) {
                    if (false) { //interstate is not supported
                        TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("IGST", "GST", BigDecimal.valueOf(item.getIgst()), new BigDecimal(Float.toString(item.getIgstPercent())),
                                orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                        orderItem.getTaxes().add(taxDetail);
                        totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                    }
                } else {
                    if (Objects.nonNull(item.getCgstPercent()) && item.getCgstPercent() > 0 && Objects.nonNull(item.getCgst())) {
                        TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("CGST", "GST", BigDecimal.valueOf(item.getCgst()),BigDecimal.valueOf(item.getCgstPercent()),
                                orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                        orderItem.getTaxes().add(taxDetail);
                        totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                    }
                    if (Objects.nonNull(item.getSgstPercent()) && item.getSgstPercent() > 0 && Objects.nonNull(item.getSgst())) {
                        TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("SGST/UTGST", "GST", BigDecimal.valueOf(item.getSgst()), BigDecimal.valueOf(item.getSgstPercent()),
                                orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                        orderItem.getTaxes().add(taxDetail);
                        totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                    }
                }
            } catch (Exception e) {
                LOG.error("No taxes mapped for orderItem with id:{}", orderItem.getProductId(), e.toString());
            }
        }
        orderItem.setTax(totalItemTax);
    }

    public static Float aggregateTaxesFromOrderItemsForSwiggy(List<OrderItem> orders, SwiggyOrderRequest request) {
        Float cartCgstTotalTax = Objects.nonNull(request.getCartCgst()) && request.getCartCgst() > 0 ? request.getCartCgst() : Float.valueOf(0);
        Float cartSgstTotalTax = Objects.nonNull(request.getCartSgst()) && request.getCartSgst() > 0 ? request.getCartSgst() : Float.valueOf(0);
        Float totalTaxPerOrder = Float.valueOf(0);
        if (cartSgstTotalTax > 0) {
            totalTaxPerOrder = Float.sum(totalTaxPerOrder, cartSgstTotalTax);
        }
        if (cartCgstTotalTax > 0) {
            totalTaxPerOrder = Float.sum(totalTaxPerOrder, cartCgstTotalTax);
        }
        return request.getCartGst();
    }
    public static TaxDetail setTaxDetail(String taxCode, String taxType, BigDecimal percentage, BigDecimal price, Integer quantity,
                                         BigDecimal discount) {
        TaxDetail taxDetail = new TaxDetail();
        taxDetail.setCode(taxCode);
        taxDetail.setPercentage(percentage);
        taxDetail.setTaxable(ChannelPartnerUtils.subtract(ChannelPartnerUtils.multiply(price, new BigDecimal(quantity)), discount));
        taxDetail.setTotal(ChannelPartnerUtils.multiply(price, new BigDecimal(quantity)));
        taxDetail.setType(taxType);
        taxDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(taxDetail.getPercentage(), taxDetail.getTaxable()));
        return taxDetail;
    }
}

