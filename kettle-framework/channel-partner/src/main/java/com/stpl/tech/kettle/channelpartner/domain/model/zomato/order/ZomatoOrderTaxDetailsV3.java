package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"slug", "name", "amount"})
public class ZomatoOrderTaxDetailsV3 implements Serializable {

    private static final long serialVersionUID = 4838179992999056754L;

    @JsonProperty("slug")
    private String slug;
    @JsonProperty("name")
    private String taxName;
    @JsonProperty("amount")
    private Float taxAmount;
    @JsonProperty("type")
    private String type;

    @JsonProperty("slug")
    public String getSlug() {
        return slug;
    }

    @JsonProperty("slug")
    public void setSlug(String slug) {
        this.slug = slug;
    }

    @JsonProperty("name")
    public String getTaxName() {
        return taxName;
    }

    @JsonProperty("name")
    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    @JsonProperty("tax_amount")
    public Float getTaxAmount() {
        return taxAmount;
    }

    @JsonProperty("tax_amount")
    public void setTaxAmount(Float taxAmount) {
        this.taxAmount = taxAmount;
    }

    @JsonProperty("type")
    public String getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "ZomatoOrderTaxDetailsV3 [slug=" + slug + ", taxName=" + taxName + ", taxAmount=" + taxAmount + "]";
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(taxAmount).append(slug).append(taxName).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if (!(other instanceof ZomatoOrderTaxDetailsV3)) {
            return false;
        }
        ZomatoOrderTaxDetailsV3 rhs = ((ZomatoOrderTaxDetailsV3) other);
        return new EqualsBuilder().append(taxAmount, rhs.taxAmount).append(slug, rhs.slug).append(taxName, rhs.taxName)
            .isEquals();
    }

}