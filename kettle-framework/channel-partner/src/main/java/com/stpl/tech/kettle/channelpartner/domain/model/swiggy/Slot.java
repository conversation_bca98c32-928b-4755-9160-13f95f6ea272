package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({ "open_time","close_time"})
public class Slot {

    @JsonProperty("open_time")
    private String open_time;

    @JsonProperty("close_time")
    private String close_time;

    @JsonProperty("open_time")
    public String getOpen_time() {
        return open_time;
    }

    @JsonProperty("open_time")
    public void setOpen_time(String open_time) {
        this.open_time = open_time;
    }

    @JsonProperty("close_time")
    public String getClose_time() {
        return close_time;
    }

    @JsonProperty("close_time")
    public void setClose_time(String close_time) {
        this.close_time = close_time;
    }
}
