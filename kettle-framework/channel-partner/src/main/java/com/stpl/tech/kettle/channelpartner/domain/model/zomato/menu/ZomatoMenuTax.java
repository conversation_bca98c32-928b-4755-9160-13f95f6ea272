package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "tax_id",
        "display_name",
        "calculation_type",
        "value",
        "name",
        "service",
        "slug"
})
public class ZomatoMenuTax {
	

    @JsonProperty("tax_id")
    private String taxId;
    @JsonProperty("display_name")
    private String displayName;
    @JsonProperty("calculation_type")
    private String calculationType;
    @JsonProperty("value")
    private Float value;
    @JsonProperty("name")
    private String name;
    @JsonProperty("service")
    private List<String> service = null;
    @JsonProperty("slug")
    private String slug;
    
    @JsonProperty("tax_id")
    public String getTaxId() {
		return taxId;
	}

    @JsonProperty("tax_id")
	public void setTaxId(String taxId) {
		this.taxId = taxId;
	}

    @JsonProperty("display_name")
	public String getDisplayName() {
		return displayName;
	}

    @JsonProperty("display_name")
	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

    @JsonProperty("calculation_type")
	public String getCalculationType() {
		return calculationType;
	}

    @JsonProperty("calculation_type")
	public void setCalculationType(String calculationType) {
		this.calculationType = calculationType;
	}

    @JsonProperty("value")
	public Float getValue() {
		return value;
	}

    @JsonProperty("value")
	public void setValue(Float value) {
		this.value = value;
	}

    @JsonProperty("name")
	public String getName() {
		return name;
	}

    @JsonProperty("name")
	public void setName(String name) {
		this.name = name;
	}

    @JsonProperty("slug")
    public String getSlug() {
		return slug;
	}

    @JsonProperty("slug")
	public void setSlug(String slug) {
		this.slug = slug;
	}

	@JsonProperty("service")
	public List<String> getService() {
		return service;
	}

    @JsonProperty("service")
	public void setService(List<String> service) {
		this.service = service;
	}

	

    @Override
	public String toString() {
		return "ZomatoMenuTax [taxId=" + taxId + ", displayName=" + displayName + ", calculationType=" + calculationType
				+ ", value=" + value + ", name=" + name + ", service=" + service + ", slug=" + slug + "]";
	}

	@Override
    public int hashCode() {
        return new HashCodeBuilder().append(taxId).append(displayName).append(calculationType).append(value).append(name).append(service).append(slug).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoMenuTax) == false) {
            return false;
        }
        ZomatoMenuTax rhs = ((ZomatoMenuTax) other);
        return new EqualsBuilder().append(taxId, rhs.taxId).append(displayName, rhs.displayName).append(slug, rhs.slug).append(calculationType, rhs.calculationType).append(value, rhs.value).append(name, rhs.name).append(service, rhs.service).isEquals();
    }

}