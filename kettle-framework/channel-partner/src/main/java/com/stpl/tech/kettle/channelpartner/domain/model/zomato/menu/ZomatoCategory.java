
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
	"vendorEntityId",
	"name",
	"order",
	"subCategories" 
})
public class ZomatoCategory {

    @JsonProperty("vendorEntityId")
    private String vendorEntityId;
	@JsonProperty("name")
    private String name;
    @JsonProperty("order")
    private Integer order;
    @JsonProperty("subCategories")
    private List<ZomatoSubcategory> subCategories = null;
	@JsonProperty("timings")
	private List<ZomatoMenuTiming> timings = null;

    @JsonProperty("vendorEntityId")
    public String getVendorEntityId() {
		return vendorEntityId;
	}

    @JsonProperty("vendorEntityId")
	public void setVendorEntityId(String vendorEntityId) {
		this.vendorEntityId = vendorEntityId;
	}

    @JsonProperty("name")
    public String getName() {
		return name;
	}

    @JsonProperty("name")
	public void setName(String name) {
		this.name = name;
	}

	@JsonProperty("order")
	public Integer getOrder() {
		return order;
	}

    @JsonProperty("order")
	public void setOrder(Integer order) {
		this.order = order;
	}

    @JsonProperty("subCategories")
	public List<ZomatoSubcategory> getSubCategories() {
		return subCategories;
	}

    @JsonProperty("subCategories")
	public void setSubCategories(List<ZomatoSubcategory> subCategories) {
		this.subCategories = subCategories;
	}

	public List<ZomatoMenuTiming> getTimings() {
		return timings;
	}

	public void setTimings(List<ZomatoMenuTiming> timings) {
		this.timings = timings;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;

		if (o == null || getClass() != o.getClass()) return false;

		ZomatoCategory that = (ZomatoCategory) o;

		return new EqualsBuilder()
			.append(vendorEntityId, that.vendorEntityId)
			.append(name, that.name)
			.append(order, that.order)
			.append(subCategories, that.subCategories)
			.append(timings, that.timings)
			.isEquals();
	}

	@Override
	public int hashCode() {
		return new HashCodeBuilder(17, 37)
			.append(vendorEntityId)
			.append(name)
			.append(order)
			.append(subCategories)
			.append(timings)
			.toHashCode();
	}

	@Override
	public String toString() {
		return "ZomatoCategory{" +
			"vendorEntityId='" + vendorEntityId + '\'' +
			", name='" + name + '\'' +
			", order=" + order +
			", subCategories=" + subCategories +
			", timings=" + timings +
			'}';
	}
}
