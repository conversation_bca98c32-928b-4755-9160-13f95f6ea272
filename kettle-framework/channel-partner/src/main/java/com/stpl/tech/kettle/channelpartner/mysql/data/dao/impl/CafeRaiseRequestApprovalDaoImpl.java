package com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl;

import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeRaiseRequestApprovalDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeRaiseRequestApproval;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Repository
public class CafeRaiseRequestApprovalDaoImpl extends AbstractMasterDaoImpl implements CafeRaiseRequestApprovalDao {

    private static final Logger LOG = LoggerFactory.getLogger(CafeRaiseRequestApprovalDaoImpl.class);

    @Override
    public List<CafeRaiseRequestApproval> getAllPendingCafeSwitchOffRequests(String actionTaken){
        Query query = manager.createQuery("FROM CafeRaiseRequestApproval c WHERE c.actionTaken= :actionTaken");
        query.setParameter("actionTaken",actionTaken);
        return query.getResultList();
    }

    @Override
    public boolean updateActionTakenOnRequest(Integer id,String actionTaken){
        Query query = manager.createQuery("UPDATE CafeRaiseRequestApproval c SET c.actionTaken = :actionTaken WHERE c.id = :id");
        query.setParameter("actionTaken", actionTaken);
        query.setParameter("id", id);
        int count = query.executeUpdate();
        return count > 0;
    }
}
