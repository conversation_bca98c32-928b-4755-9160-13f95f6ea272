package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuTrackResponse;

import java.util.List;
import java.util.Map;

public class SwiggyMenuTrackResponse implements MenuTrackResponse {


    @JsonProperty("statusCode")
    private Integer statusCode;
    @JsonProperty("data")
    private Map<String, List<Object>> data;
    @JsonProperty("code")
    private String code;

    @JsonProperty("statusCode")
    public Integer getStatusCode() {
        return statusCode;
    }

    @JsonProperty("statusCode")
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @JsonProperty("data")
    public Map<String, List<Object>> getData() {
        return data;
    }

    @JsonProperty("data")
    public void setData(Map<String, List<Object>> data) {
        this.data = data;
    }

    @JsonProperty("code")
    public String getCode() {
        return code;
    }

    @JsonProperty("code")
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "SwiggyMenuTrackResponse{" +
                "statusCode=" + statusCode +
                ", data=" + data +
                ", code='" + code + '\'' +
                '}';
    }
}
