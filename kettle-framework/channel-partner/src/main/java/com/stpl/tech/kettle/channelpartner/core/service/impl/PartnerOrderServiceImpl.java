package com.stpl.tech.kettle.channelpartner.core.service.impl;

import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.stpl.tech.kettle.channelpartner.core.util.WebServiceHelper;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeStatusChannelPartnerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.StockRefreshAuditData;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.notification.email.OrderMismatchNotification;
import com.stpl.tech.kettle.channelpartner.core.notification.email.templates.OrderMismatchNotificationTemplate;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerOrderService;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.service.TrackService;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPendingOrderRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.dao.PartnerOrderDao;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.util.EmailGenerationException;

@Service
public class PartnerOrderServiceImpl implements PartnerOrderService {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerOrderServiceImpl.class);
    public static final String ORDER_REJECTED = "Order Rejected";

    @Autowired
    private SwiggyService swiggyService;

    @Autowired
    private TrackService trackService;

    @Autowired
    private PartnerOrderDao partnerOrderDao;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private ZomatoService zomatoService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private WebServiceHelper webServiceHelper;

    @Autowired
    private CafeStatusChannelPartnerDao cafeStatusChannelPartnerDao;

    @Override
    public List<PartnerOrderDetail> getPartnerOrder(PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException {
        if (partnerOrderDetail.getPartnerId() != null && partnerOrderDetail.getPartnerOrderId() != null) {
            return partnerOrderDao.searchByPartnerIdAndOrderId(partnerOrderDetail.getPartnerId(), partnerOrderDetail.getPartnerOrderId());
        } else {
            throw new ChannelPartnerException("Order not found!");
        }
    }

    @Override
    public PartnerOrderDetail getPartnerOrder(String kettleOrderId) throws ChannelPartnerException {
        if (kettleOrderId != null) {
            return partnerOrderDao.searchByKettleOrderId(kettleOrderId);
        } else {
            throw new ChannelPartnerException("Order not found!");
        }
    }

    @Override
    public Map<String, List<PartnerOrderDetail>> getPartnerPendingOrders(PartnerPendingOrderRequest request) throws ChannelPartnerException {
        List<PartnerOrderDetail> newOrders = new ArrayList<>();
        List<PartnerOrderDetail> changedOrders = new ArrayList<>();
        List<PartnerOrderDetail> finishedOrders = new ArrayList<>();
        Date time = ChannelPartnerUtils.getDateBeforeOrAfterInSeconds(ChannelPartnerUtils.getCurrentTimestamp(),
            -request.getHours() * 60 * 60);
        List<PartnerOrderDetail> orders ;
        if (request.getUnitId() != null) {
            LOG.info("Getting Pending  PartnerOrderDetail for Cafe and unitId  " + request.getUnitId());
            orders = partnerOrderDao.getCafePendingOrders(request.getUnitId(),time);
        } else {
//            LOG.info("Getting Pending  PartnerOrderDetail for callCenter ");
            orders = partnerOrderDao.getPartnerPendingOrders(time);
        }
        if (request.getPartnerOrderDetails() == null || request.getPartnerOrderDetails().keySet().size() == 0) {
            newOrders.addAll(orders);
        } else {
            orders.forEach(order -> {
                if (request.getPartnerOrderDetails().containsKey(order.getOrderId())) {
                    if (!request.getPartnerOrderDetails().get(order.getOrderId()).equals(order.getPartnerOrderStatus().name())) {
                        changedOrders.add(order);
                    }
                    request.getPartnerOrderDetails().remove(order.getOrderId());
                } else {
                    newOrders.add(order);
                }
            });
            request.getPartnerOrderDetails().keySet().forEach(key -> {
                PartnerOrderDetail partnerOrderDetail = new PartnerOrderDetail();
                partnerOrderDetail.setOrderId(key);
                finishedOrders.add(partnerOrderDetail);
            });
        }
        Map<String, List<PartnerOrderDetail>> stringListMap = new HashMap<>();
        stringListMap.put("NEW", newOrders);
        stringListMap.put("CHANGED", changedOrders);
        stringListMap.put("FINISHED", finishedOrders);
        return stringListMap;
    }

    @Override
    public List<PartnerOrderDetail> getOrdersByTime(Date startTime, Date endTime) {
        return partnerOrderDao.getOrdersByTime(startTime, endTime);
    }

    @Override
    public List<PartnerOrderDetail> getPartnerOrder(String partnerId, String orderStatus) throws ChannelPartnerException {
        if (!StringUtils.isEmpty(partnerId) && !StringUtils.isEmpty(orderStatus)) {
            return partnerOrderDao.searchByPartnerIdAndOrderStatus(partnerId, orderStatus);
        } else {
            throw new ChannelPartnerException("Order not found!");
        }
    }

    @Override
    public boolean callSwiggyPartnerSupport(String orderId) throws ChannelPartnerException {
        Optional<PartnerOrderDetail> partnerOrderDetail = partnerOrderDao.findById(orderId);
        if (partnerOrderDetail.isPresent()) {
            return swiggyService.callSwiggyPartnerSupport(partnerOrderDetail.get());
        }
        throw new ChannelPartnerException("Partner order id not found!");
    }

    @Override
    public boolean markOrderResolved(String orderId) throws ChannelPartnerException {
    	Optional<PartnerOrderDetail> partnerOrderDetailData = partnerOrderDao.findById(orderId);
        if (partnerOrderDetailData.isPresent()) {
        	PartnerOrderDetail partnerOrderDetail = partnerOrderDetailData.get();
            PartnerOrderStatus currentStatus = partnerOrderDetail.getPartnerOrderStatus();
            if (currentStatus == PartnerOrderStatus.CHECKED) {
                partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.RESOLVED);
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(currentStatus,
                        partnerOrderDetail.getPartnerOrderStatus(), true, null));
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.MARK_RESOLVED, "Order marked resolved", null));
                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                if (partnerOrderDetail != null) {
                    return true;
                }
            }
            throw new ChannelPartnerException("Partner order cannot be marked as RESOLVED!");
        }
        throw new ChannelPartnerException("Partner order id not found!");
    }

    @Override
    public boolean addKettleOrderId(String generatedOrderId,String partnerOrderId, Integer unitId){
        Optional<PartnerOrderDetail> partnerOrderDetailData = partnerOrderDao.findById(partnerOrderId);
        if (partnerOrderDetailData.isPresent()) {
            PartnerOrderDetail partnerOrderDetail = partnerOrderDetailData.get();
            if(Objects.isNull(partnerOrderDetail.getKettleOrderId())){
                Object[] kettleOrderDetail = cafeStatusChannelPartnerDao.getOrderDetailByGeneratedOrderId(generatedOrderId);
                if(Objects.nonNull(kettleOrderDetail)){
                    Integer kettleOrderId = (Integer) kettleOrderDetail[0];
                    if(partnerOrderDetail.getPartnerOrderId().equals(kettleOrderDetail[2])){
                        if(Objects.nonNull(kettleOrderId)){
                            partnerOrderDetail.setKettleOrderId(String.valueOf(kettleOrderId));
                            trackService.updatePartnerOrder(partnerOrderDetail);
                            return  true;
                        }
                    }
                }
            }
        }
        return  false;
    }

    @Override
    public boolean manualProcess(String orderId) throws ChannelPartnerException, URISyntaxException {
        Optional<PartnerOrderDetail> partnerOrderDetail = partnerOrderDao.findById(orderId);
        if (partnerOrderDetail != null) {
            switch (partnerOrderDetail.get().getPartnerName()) {
                case "SWIGGY":
                    swiggyService.manualProcessOrder(partnerOrderDetail.get(), false);
                    break;
                case "ZOMATO":
                    zomatoService.manualProcessOrder(partnerOrderDetail.get(), false);
                    break;
                default:
                    return false;
            }

            return true;
        }
        throw new ChannelPartnerException("Partner order id not found!");
    }

    @Override
    public boolean markCancelled(String kettleOrderId) throws ChannelPartnerException {
        PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByKettleOrderId(kettleOrderId);
        //PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCache().get(partnerOrderDetail.getPartnerId());
        if (partnerOrderDetail != null) {
            PartnerOrderStatus partnerOrderStatus = partnerOrderDetail.getPartnerOrderStatus();
            if (partnerOrderStatus.equals(PartnerOrderStatus.EDIT_CANCEL_REQUESTED) || partnerOrderStatus.equals(PartnerOrderStatus.CANCEL_REQUESTED)) {
                if (partnerOrderStatus.equals(PartnerOrderStatus.EDIT_CANCEL_REQUESTED)) {
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.EDIT_CANCELLED);
                    partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.EDIT_CANCELLED, "Partner order cancelled", null));
                } else if (partnerOrderStatus.equals(PartnerOrderStatus.CANCEL_REQUESTED)) {
                    partnerOrderDetail.setPartnerOrderStatus(PartnerOrderStatus.CANCELLED);
                    partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.CANCELLED, "Partner order cancelled", null));
                }
                partnerOrderDetail.getOrderStateLogs().add(trackService.generatePartnerOrderStatusLog(partnerOrderStatus, partnerOrderDetail.getPartnerOrderStatus(), true, null));
                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                if (partnerOrderDetail != null) {
                    return true;
                }
                throw new ChannelPartnerException("Error updating order status.");
            } else {
                throw new ChannelPartnerException("Partner order id not valid for cancellation!");
            }
        }
        throw new ChannelPartnerException("Partner order id not valid!");
    }

    @Override
    public boolean manualProcessWithSkipInventory(String orderId) throws ChannelPartnerException, URISyntaxException {
        Optional<PartnerOrderDetail> partnerOrderDetailData = partnerOrderDao.findById(orderId);
        if (partnerOrderDetailData.isPresent()) {
        	PartnerOrderDetail partnerOrderDetail = partnerOrderDetailData.get();
            boolean processOrder = true;
            for (PartnerOrderError partnerOrderError : partnerOrderDetail.getOrderErrors()) {
                if (!partnerOrderError.getErrorCode().equals(PartnerOrderErrorCode.STOCK_NOT_SUFFICIENT)
                		&& !partnerOrderError.getErrorCode().equals(PartnerOrderErrorCode.STOCK_NOT_AVAILABLE)) {
                    if (processOrder) {
                        processOrder = partnerOrderError.getErrorCode().isCanBeProcessed();
                    }
                }
            }
            if (processOrder && partnerOrderDetail.getPartnerOrderStatus().equals(PartnerOrderStatus.CHECKED)) {
                partnerOrderDetail.getOrderActions().add(trackService.generatePartnerOrderAction(PartnerActionCode.FORCE_PROCESSED,
                        "Force process skipping inventory check!", null));
                partnerOrderDetail = trackService.updatePartnerOrder(partnerOrderDetail);
                if (partnerOrderDetail != null) {
                    switch (partnerOrderDetail.getPartnerName()) {
                        case "SWIGGY":
                            swiggyService.manualProcessOrder(partnerOrderDetail, true);
                            break;
                        case "ZOMATO":
                            zomatoService.manualProcessOrder(partnerOrderDetail, true);
                            break;
                        default:
                            throw new ChannelPartnerException("Error in force processing order. Partner is not valid!");
                    }
                    return true;
                } else {
                    throw new ChannelPartnerException("Error in force processing order. Please try again!");
                }
            }
            throw new ChannelPartnerException("Order not applicable for force manual process!");
        }
        throw new ChannelPartnerException("Partner order id not found!");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void sendOrderNotification(PartnerOrderDetail order) {
        OrderMismatchNotificationTemplate template = new OrderMismatchNotificationTemplate(order,
                order.getPartnerOrderId(), environmentProperties.getBasePath());
        OrderMismatchNotification notification = new OrderMismatchNotification(template, environmentProperties.getEnvType());
        try {
            notification.sendEmail();
        } catch (EmailGenerationException e) {
            e.printStackTrace();
        } catch (Exception e) {
            LOG.error("Error sending mismatch notification email:", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void sendOrderStatusUpdate(PartnerOrderStateUpdate request) {
        try {
            PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByKettleOrderId(request.getOrderId());
            if (partnerOrderDetail != null) {
                switch (partnerOrderDetail.getPartnerName()) {
                    case "SWIGGY":
                        swiggyService.sendOrderStatusUpdate(request, partnerOrderDetail);
                        break;
                    case "ZOMATO":
                        zomatoService.sendOrderStatusUpdate(request, partnerOrderDetail);
                        break;
                    default:
                }
            }
        } catch (ChannelPartnerException ex) {
            LOG.error("Error updating partner order state to partner");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Integer getSwiggyRiderTimeOfArrival(String orderId) throws ChannelPartnerException {
        PartnerOrderDetail partnerOrderDetail = partnerOrderDao.searchByKettleOrderId(orderId);
        if (partnerOrderDetail != null) {
            return swiggyService.getRiderTimeOfArrival(orderId, partnerOrderDetail);
        }
        throw new ChannelPartnerException("Invalid request", "Order id is not valid.");
    }

    @Override
	public void sendOrderNotPunchedNotification(PartnerOrderDetail detail) {
		try {
			UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(detail.getUnitId());
			String brandName = detail.getBrandId() != null
					? masterDataCache.getBrandMetaData().get(detail.getBrandId()).getBrandName()
					: "Unknown";
			String message = String.format(
					"Order Rejected from : %s for %s and cafe %s \n Partner Order Id : %s, Time : %s \nErrors : %s",
					detail.getPartnerName(), brandName, ubd.getName(), detail.getPartnerOrderId(),
					detail.getAddTimeIST(), StringUtils.join(detail.getOrderErrors(), ",\n"));
            String message1 = ChannelPartnerUtils.getMessage("Order Rejections", message);
			SlackNotificationService.getInstance().sendNotification(environmentProperties.getEnvType(),
					"Channel Partner", SlackNotification.ORDER_REJECTED, message1);
			if (ubd.getUnitManagerId() != null) {
                SlackNotificationService.getInstance()
						.sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
								!AppUtils.isProd(environmentProperties.getEnvType())
										? environmentProperties.getEnvType().name().toLowerCase() + "_"
												+ ubd.getUnitManagerId() + "_notify"
										: ubd.getUnitManagerId() + "_notify",
								message1);
			}
			if (ubd.getCafeManagerId() != null) {
                SlackNotificationService.getInstance()
						.sendNotification(environmentProperties.getEnvType(), "Channel Partner", null,
								!AppUtils.isProd(environmentProperties.getEnvType())
										? environmentProperties.getEnvType().name().toLowerCase() + "_"
												+ ubd.getCafeManagerId() + "_notify"
										: ubd.getCafeManagerId() + "_notify",
								message1);
			}
		} catch (Exception e) {
			LOG.error("Error while publishing slack for inventory down and order punched", e);
		}
	}

    @Override
    public void sendOrderNotPunchedNotificationToKnock(PartnerOrderDetail detail) {
        try {
            UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(detail.getUnitId());
            String brandName = detail.getBrandId() != null
                    ? masterDataCache.getBrandMetaData().get(detail.getBrandId()).getBrandName()
                    : "Unknown";
            String message = String.format(
                    "Order Rejected from : %s for %s and cafe %s \n Partner Order Id : %s, Time : %s \nErrors : %s",
                    detail.getPartnerName(), brandName, ubd.getName(), detail.getPartnerOrderId(),
                    detail.getAddTimeIST(), StringUtils.join(detail.getOrderErrors(), ",\n"));
            Map<String,String> params = new HashMap<>();
            params.put("message",message);
            params.put("title", ORDER_REJECTED);

            try {
                if (ubd.getUnitManagerId() != null) {
                    params.put("userId", ubd.getUnitManagerId() + "");
                    webServiceHelper.postWithAuthentication(environmentProperties.getKnockBaseUrl() +  AppConstants.KNOCK_NOTIFICATION_ENDPOINT + "send-knock-notification", environmentProperties.getKnockMasterToken(),params , null, Boolean.class);
                }
            }catch(Exception e){
                LOG.error("Error while publishing order rejection notification on knock ",e);
            }
            try {
                if (ubd.getCafeManagerId() != null) {
                    params.put("userId", ubd.getCafeManagerId() + "");
                    webServiceHelper.postWithAuthentication(environmentProperties.getKnockBaseUrl() + AppConstants.KNOCK_NOTIFICATION_ENDPOINT + "send-knock-notification", environmentProperties.getKnockMasterToken(),params , null, Boolean.class);
                }
            } catch (Exception e) {
                LOG.error("Error while publishing order rejection notification on knock ",e);
            }
        } catch (Exception e) {
            LOG.error("Error while publishing slack for inventory down and order punched", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void logStockRefreshEvent(Integer unitId, Integer partnerId, Integer brandId,String logType, String status) {
        try{
            StockRefreshAuditData stockRefreshAuditData = StockRefreshAuditData.builder().unitId(unitId).channelPartnerId(partnerId)
                    .brandId(brandId).timestamp(AppUtils.getCurrentTimestamp()).logType(logType).status(status).build();
            cafeStatusChannelPartnerDao.add(stockRefreshAuditData);
        }catch (Exception e){
            LOG.error("Error While Logging Stock Refresh Event For  Unit Id :::: {} , Partner Id :::: {} ," +
                    " Brand Id :::: {}  ",unitId,partnerId,brandId);
        }

    }
}
