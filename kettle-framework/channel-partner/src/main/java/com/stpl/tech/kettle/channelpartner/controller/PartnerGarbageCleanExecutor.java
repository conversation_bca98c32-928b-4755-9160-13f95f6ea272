package com.stpl.tech.kettle.channelpartner.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class PartnerGarbageCleanExecutor {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerGarbageCleanExecutor.class);

    @Scheduled(cron = "0 5 0,4,7,11,15,19 * * *", zone = "GMT+05:30")
//    @Scheduled(fixedRate = 10000)
    public void garbageCleanExecutor(){
        try {
            LOG.info("CHANNEL_PARTNER_GARBAGE_COLLECTOR :::::: Executing Garbage Clean up");
            System.gc();
        }catch (Exception e){
            LOG.error("CHANNEL_PARTNER_GARBAGE_COLLECTOR :::::: Exception Faced While Executing Garbage Clean up");
        }
    }
}
