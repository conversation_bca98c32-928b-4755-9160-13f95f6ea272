package com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl;

import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeStatusChannelPartnerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Repository
public class CafeStatusChannelPartnerDaoImpl extends AbstractMasterDaoImpl implements CafeStatusChannelPartnerDao {

    private static final Logger LOG = LoggerFactory.getLogger(CafeStatusChannelPartnerDaoImpl.class);

    @Override
    public List<CafeStatusChannelPartner> getInActiveCafe(String statusUpdate){
        Query query = manager.createQuery("FROM CafeStatusChannelPartner c WHERE c.statusUpdate= :statusUpdate ");
        query.setParameter("statusUpdate",statusUpdate);
        return query.getResultList();
    }

    @Override
    public Object[] getOrderDetailByGeneratedOrderId(String generatedOrderId) {
        Query query = manager.createNativeQuery("Select ORDER_ID , UNIT_ID , ORDER_SOURCE_ID FROM  ORDER_DETAIL E where E.GENERATED_ORDER_ID =:generatedOrderId ");
        query.setParameter("generatedOrderId", generatedOrderId);
        try {
            return (Object[]) query.getSingleResult();
        } catch (Exception e) {
            LOG.info("Error while fetching Kettle Order Id  for Generated Order id :::: {}  :::: {} ",generatedOrderId,e);
        }
        return null;
    }





}
