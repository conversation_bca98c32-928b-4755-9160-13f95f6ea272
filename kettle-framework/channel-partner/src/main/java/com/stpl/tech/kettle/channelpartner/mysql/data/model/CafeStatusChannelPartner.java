package com.stpl.tech.kettle.channelpartner.mysql.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

import static javax.persistence.GenerationType.IDENTITY;


@Entity
@Table(name="CAFE_STATUS_CHANNEL_PARTNER")
public class CafeStatusChannelPartner implements  Serializable{

    private static final long serialVersionUID = -3595800761182952875L;

    private Integer id;

    private Integer unitId;

    private String unitName;

    private  String unitRegion;

    private String unitCity;

    private String isCafeLive;

    private String lastUpdatedTime;

    private String updationRequest;

    private String channelPartner;

    private String brandName;

    private Integer statusCode;

    private Integer brandId;

    private Integer empId;

    private String applicationSource;

    private String channelRestaurantId;

    private String statusMessage;

    private String statusUpdate;

    private  Integer partnerId;

    @Id
    @Column(name = "ID",unique = true,nullable = false)
    @GeneratedValue(strategy = IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name="UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "UNIT_REGION")
    public String getUnitRegion() {
        return unitRegion;
    }

    public void setUnitRegion(String unitRegion) {
        this.unitRegion = unitRegion;
    }

    @Column(name = "UNIT_CITY")
    public String getUnitCity() {
        return unitCity;
    }

    public void setUnitCity(String unitCity) {
        this.unitCity = unitCity;
    }

    @Column(name = "IS_CAFE_LIVE")
    public String isCafeLive() {
        return isCafeLive;
    }

    public void setCafeLive(String cafeLive) {
        isCafeLive = cafeLive;
    }

    @Column(name = "LAST_UPDATED_TIME")
    public String getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(String lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    @Column(name = "UPDATION_REQUEST")
    public String isUpdationRequest() {
        return updationRequest;
    }

    public void setUpdationRequest(String updationRequest) {
        this.updationRequest = updationRequest;
    }

    @Column(name = "CHANNEL_PARTNER")
    public String getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(String channelPartner) {
        this.channelPartner = channelPartner;
    }

    @Column(name = "BRAND_NAME")
    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    @Column(name = "STATUS_CODE")
    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "STATUS_MESSAGE")
    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    @Column(name = "EMPLOYEE_ID")
    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    @Column(name = "APPLICATION_SOURCE")
    public String getApplicationSource() {
        return applicationSource;
    }

    public void setApplicationSource(String applicationSource) {
        this.applicationSource = applicationSource;
    }

    @Column(name = "CHANNEL_RESTAURANT_ID")
    public String getChannelRestaurantId() {
        return channelRestaurantId;
    }

    public void setChannelRestaurantId(String channelRestaurantId) {
        this.channelRestaurantId = channelRestaurantId;
    }

    @Column(name = "STATUS_UPDATE")
    public String getStatusUpdate() {
        return statusUpdate;
    }

    public void setStatusUpdate(String statusUpdate) {
        this.statusUpdate = statusUpdate;
    }

    @Column(name = "PARTNER_ID")
    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }







}
