package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ZomatoTakeawayStatusData {

    @JsonProperty("outlet_takeaway_status")
    private Integer outletTakeawayStatus;
    @JsonProperty("zomato_takeaway_status")
    private Integer zomatoTakeawayStatus;

    @JsonProperty("outlet_takeaway_status")
    public Integer getOutletTakeawayStatus() {
        return outletTakeawayStatus;
    }

    @JsonProperty("outlet_takeaway_status")
    public void setOutletTakeawayStatus(Integer outletTakeawayStatus) {
        this.outletTakeawayStatus = outletTakeawayStatus;
    }

    @JsonProperty("zomato_takeaway_status")
    public Integer getZomatoTakeawayStatus() {
        return zomatoTakeawayStatus;
    }

    @JsonProperty("zomato_takeaway_status")
    public void setZomatoTakeawayStatus(Integer zomatoTakeawayStatus) {
        this.zomatoTakeawayStatus = zomatoTakeawayStatus;
    }
}
