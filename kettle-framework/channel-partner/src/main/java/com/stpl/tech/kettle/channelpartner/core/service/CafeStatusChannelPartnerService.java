package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeRaiseRequestApproval;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;

import java.util.List;

public interface CafeStatusChannelPartnerService {

    List<CafeStatusChannelPartner> getInActiveCafe(String statusUpdate);

    void saveCafeStatus(CafeStatusChannelPartner cafeStatus);

    CafeStatusChannelPartner deleteActivatedCafe(Integer id);

    List<CafeRaiseRequestApproval> getUnitSwitchOffRequests(String actionTaken);

    boolean updatedRequestForUnit(CafeRaiseRequestApproval request);
}
