package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "tax_id",
        "tax_name",
        "tax_type",
        "tax_value",
        "tax_amount",
        "tax_applied_on",
        "tax_is_active"
})
public class ZomatoTaxDetails {

    @JsonProperty("tax_id")
    private String taxId;
    @JsonProperty("tax_name")
    private String taxName;
    @JsonProperty("tax_type")
    private String taxType;
    @JsonProperty("tax_value")
    private Float taxValue;
    @JsonProperty("tax_amount")
    private Float taxAmount;
    @JsonProperty("tax_applied_on")
    private String taxAppliedOn;
    @JsonProperty("tax_is_active")
    private Boolean taxIsActive;

    @JsonProperty("tax_id")
    public String getTaxId() {
        return taxId;
    }

    @JsonProperty("tax_id")
    public void setTaxId(String taxId) {
        this.taxId = taxId;
    }

    @JsonProperty("tax_name")
    public String getTaxName() {
        return taxName;
    }

    @JsonProperty("tax_name")
    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    @JsonProperty("tax_type")
    public String getTaxType() {
        return taxType;
    }

    @JsonProperty("tax_type")
    public void setTaxType(String taxType) {
        this.taxType = taxType;
    }

    @JsonProperty("tax_value")
    public Float getTaxValue() {
        return taxValue;
    }

    @JsonProperty("tax_value")
    public void setTaxValue(Float taxValue) {
        this.taxValue = taxValue;
    }

    @JsonProperty("tax_amount")
    public Float getTaxAmount() {
        return taxAmount;
    }

    @JsonProperty("tax_amount")
    public void setTaxAmount(Float taxAmount) {
        this.taxAmount = taxAmount;
    }

    public String getTaxAppliedOn() {
        return taxAppliedOn;
    }

    public void setTaxAppliedOn(String taxAppliedOn) {
        this.taxAppliedOn = taxAppliedOn;
    }

    @JsonProperty("tax_is_active")
    public Boolean getTaxIsActive() {
        return taxIsActive;
    }

    @JsonProperty("tax_is_active")
    public void setTaxIsActive(Boolean taxIsActive) {
        this.taxIsActive = taxIsActive;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("taxId", taxId).append("taxName", taxName).append("taxType", taxType).append("taxValue", taxValue).append("taxAmount", taxAmount).append("taxIsActive", taxIsActive).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(taxAmount).append(taxId).append(taxName).append(taxType).append(taxValue).append(taxIsActive).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoTaxDetails) == false) {
            return false;
        }
        ZomatoTaxDetails rhs = ((ZomatoTaxDetails) other);
        return new EqualsBuilder().append(taxAmount, rhs.taxAmount).append(taxId, rhs.taxId).append(taxName, rhs.taxName).append(taxType, rhs.taxType).append(taxValue, rhs.taxValue).append(taxIsActive, rhs.taxIsActive).isEquals();
    }

}