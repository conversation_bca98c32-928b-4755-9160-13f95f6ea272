package com.stpl.tech.kettle.test.service;

import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.test.mongo.model.PartnerOrderDetail;

import java.util.List;
import java.util.Map;

public interface TestZomatoService {
    List<PartnerOrderDetail> getZomatoLiveOrders(String partnerId , Integer startOrderId , Integer endOrderId);
    PartnerOrderDetail getPartnerOrder(String kettleOrderId);

    void updateTestCustomerDetails(ZomatoOrderRequestV3 zomatoOrderRequest);

    List<PartnerOrderDetail> sendRequestToGetLivePartnerOrderData(String partnerName, Integer startOrderId, Integer endOrderId);

    boolean addBatchedOrders(List<PartnerOrderDetail> partnerOrderDetailList, Integer startOrderId, Integer endOrderId, String partnerName, Map<String, Object> failedOrderMap);

    boolean addOrderRequest(ZomatoOrderRequestV3 zomatoOrderRequestV3, Map<String, Object> map);

//    void addBatchedOrdersRequest(List<PartnerOrderDetail> partnerOrderDetailList, Integer startOrderId, Integer endOrderId, String partnerName, boolean b);
}
