package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SwiggyOrderOutOfStockDetail implements Serializable {

    private static final long serialVersionUID = 2440499613532696721L;
    private String partnerOrderId;
    @Builder.Default
    private List<String> itemIds =new ArrayList<>();
    @Builder.Default
    private List<String> addOnIds =new ArrayList<>();

    @Builder.Default
    private List<String> variantIds =new ArrayList<>();

}
