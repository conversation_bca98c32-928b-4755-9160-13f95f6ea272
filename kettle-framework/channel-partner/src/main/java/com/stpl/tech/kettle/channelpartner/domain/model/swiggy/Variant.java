
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "cgst",
    "cgst_percent",
    "id",
    "igst",
    "igst_percent",
    "name",
    "price",
    "sgst",
    "sgst_percent"
})
public class Variant {

    @JsonProperty("cgst")
    private float cgst;
    @JsonProperty("cgst_percent")
    private float cgstPercent;
    @JsonProperty("id")
    private String id;
    @JsonProperty("igst")
    private float igst;
    @JsonProperty("igst_percent")
    private float igstPercent;
    @JsonProperty("name")
    private String name;
    @JsonProperty("price")
    private float price;
    @JsonProperty("sgst")
    private float sgst;
    @JsonProperty("sgst_percent")
    private float sgstPercent;

    @JsonProperty("cgst")
    public float getCgst() {
        return cgst;
    }

    @JsonProperty("cgst")
    public void setCgst(float cgst) {
        this.cgst = cgst;
    }

    @JsonProperty("cgst_percent")
    public float getCgstPercent() {
        return cgstPercent;
    }

    @JsonProperty("cgst_percent")
    public void setCgstPercent(float cgstPercent) {
        this.cgstPercent = cgstPercent;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("igst")
    public float getIgst() {
        return igst;
    }

    @JsonProperty("igst")
    public void setIgst(float igst) {
        this.igst = igst;
    }

    @JsonProperty("igst_percent")
    public float getIgstPercent() {
        return igstPercent;
    }

    @JsonProperty("igst_percent")
    public void setIgstPercent(float igstPercent) {
        this.igstPercent = igstPercent;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("price")
    public float getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(float price) {
        this.price = price;
    }

    @JsonProperty("sgst")
    public float getSgst() {
        return sgst;
    }

    @JsonProperty("sgst")
    public void setSgst(float sgst) {
        this.sgst = sgst;
    }

    @JsonProperty("sgst_percent")
    public float getSgstPercent() {
        return sgstPercent;
    }

    @JsonProperty("sgst_percent")
    public void setSgstPercent(float sgstPercent) {
        this.sgstPercent = sgstPercent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("cgst", cgst).append("cgstPercent", cgstPercent).append("id", id).append("igst", igst).append("igstPercent", igstPercent).append("name", name).append("price", price).append("sgst", sgst).append("sgstPercent", sgstPercent).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(id).append(price).append(sgstPercent).append(cgstPercent).append(name).append(igst).append(cgst).append(sgst).append(igstPercent).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof Variant) == false) {
            return false;
        }
        Variant rhs = ((Variant) other);
        return new EqualsBuilder().append(id, rhs.id).append(price, rhs.price).append(sgstPercent, rhs.sgstPercent).append(cgstPercent, rhs.cgstPercent).append(name, rhs.name).append(igst, rhs.igst).append(cgst, rhs.cgst).append(sgst, rhs.sgst).append(igstPercent, rhs.igstPercent).isEquals();
    }

}
