package com.stpl.tech.kettle.channelpartner.core.service;

import java.util.List;

import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappings;

public interface PartnerMetadataFetchService {
	//Yes
    List<Integer> getPartnerProductFilter(Integer partnerId, Integer brandId);
    //Yes
    public List<String> getPartnerBogoProductsforOffer(Integer kettlePartnerId);
    //Yes
    List<ProductTagsMappings> getPartnerProductTagsMappings(Integer partnerId, Integer brandId);
    //Yes
    List<ProductTagsMappings> getPartnerProductMeatTagsMappings(Integer partnerId, Integer brandId);

}
