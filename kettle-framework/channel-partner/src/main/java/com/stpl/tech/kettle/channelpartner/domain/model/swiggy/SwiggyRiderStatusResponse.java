package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "status_message",
        "status_code",
        "timestamp",
        "swiggy_order_id"
})
public class SwiggyRiderStatusResponse {

    @JsonProperty("status_message")
    private String statusMessage;
    @JsonProperty("status_code")
    private int statusCode;
    @JsonProperty("timestamp")
    private String timestamp;
    @JsonProperty("swiggy_order_id")
    private Long swiggyOrderId;

    @JsonProperty("status_message")
    public String getStatusMessage() {
        return statusMessage;
    }

    @JsonProperty("status_message")
    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }

    @JsonProperty("status_code")
    public int getStatusCode() {
        return statusCode;
    }

    @JsonProperty("status_code")
    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    @JsonProperty("timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @JsonProperty("swiggy_order_id")
    public Long getSwiggyOrderId() {
        return swiggyOrderId;
    }

    @JsonProperty("swiggy_order_id")
    public void setSwiggyOrderId(Long swiggyOrderId) {
        this.swiggyOrderId = swiggyOrderId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        SwiggyRiderStatusResponse that = (SwiggyRiderStatusResponse) o;

        return new EqualsBuilder()
                .append(statusCode, that.statusCode)
                .append(statusMessage, that.statusMessage)
                .append(timestamp, that.timestamp)
                .append(swiggyOrderId, that.swiggyOrderId)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(statusMessage)
                .append(statusCode)
                .append(timestamp)
                .append(swiggyOrderId)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "SwiggyRiderStatusResponse{" +
                "statusMessage='" + statusMessage + '\'' +
                ", statusCode=" + statusCode +
                ", timestamp='" + timestamp + '\'' +
                ", swiggyOrderId=" + swiggyOrderId +
                '}';
    }
}