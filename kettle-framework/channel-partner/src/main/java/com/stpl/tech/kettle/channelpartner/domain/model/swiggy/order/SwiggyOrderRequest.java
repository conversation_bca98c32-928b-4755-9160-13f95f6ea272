
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"callback_url", "cart_cgst", "cart_cgst_percent", "cart_gst", "cart_gst_percent", "cart_igst",
        "cart_igst_percent", "cart_sgst", "cart_sgst_percent", "customer_address", "customer_area", "customer_city",
        "customer_name", "customer_phone", "delivery_type", "instructions", "items", "order_date_time", "order_edit",
        "order_edit_reason", "order_id", "order_packing_charges", "outlet_id", "payment_type", "restaurant_discount",
        "restaurant_gross_bill", "restaurant_service_charges", "reward_type", "order_packing_charges_cgst",
        "order_packing_charges_sgst", "order_packing_charges_igst", "order_packing_charges_gst",
        "order_packing_charges_cgst_percent", "order_packing_charges_sgst_percent",
        "order_packing_charges_igst_percent", "restaurant_name", "customer_id", "delivery_fee_coupon_restaurant_discount",
        "offer_data", "is_thirty_mof", "meals", "payment_qr_url", "transaction_id", "gst_breakup", "prep_time_details"})
public class SwiggyOrderRequest {

    @JsonProperty("callback_url")
    private String callbackUrl;
    @JsonProperty("cart_cgst")
    private float cartCgst;
    @JsonProperty("cart_cgst_percent")
    private float cartCgstPercent;
    @JsonProperty("cart_gst")
    private float cartGst;
    @JsonProperty("cart_gst_percent")
    private float cartGstPercent;
    @JsonProperty("cart_igst")
    private float cartIgst;
    @JsonProperty("cart_igst_percent")
    private float cartIgstPercent;
    @JsonProperty("cart_sgst")
    private float cartSgst;
    @JsonProperty("cart_sgst_percent")
    private float cartSgstPercent;
    //deprecated
    @JsonProperty("customer_address")
    private String customerAddress;
    //deprecated
    @JsonProperty("customer_area")
    private String customerArea;
    //deprecated
    @JsonProperty("customer_city")
    private String customerCity;
    @JsonProperty("customer_name")
    private String customerName;
    //deprecated
    @JsonProperty("customer_phone")
    private String customerPhone;
    @JsonProperty("delivery_type")
    private String deliveryType;
    @JsonProperty("instructions")
    private String instructions;
    @JsonProperty("items")
    private List<Item> items = null;
    @JsonProperty("order_date_time")
    private String orderDateTime;
    @JsonProperty("order_edit")
    private boolean orderEdit;
    @JsonProperty("order_edit_reason")
    private Object orderEditReason;
    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("order_packing_charges")
    private float orderPackingCharges;
    @JsonProperty("outlet_id")
    private String outletId;
    @JsonProperty("payment_type")
    private String paymentType;
    @JsonProperty("restaurant_discount")
    private float restaurantDiscount;
    @JsonProperty("restaurant_gross_bill")
    private float restaurantGrossBill;
    @JsonProperty("restaurant_service_charges")
    private float restaurantServiceCharges;
    @JsonProperty("order_type")
    private String orderType;
    @JsonProperty("reward_type")
    private String rewardType;
    @JsonProperty("order_packing_charges_cgst")
    private float orderPackingChargesCgst;
    @JsonProperty("order_packing_charges_sgst")
    private float orderPackingChargesSgst;
    @JsonProperty("order_packing_charges_igst")
    private float orderPackingChargesIgst;
    @JsonProperty("order_packing_charges_gst")
    private float orderPackingChargesGst;
    @JsonProperty("order_packing_charges_cgst_percent")
    private float orderPackingChargesCgstPercent;
    @JsonProperty("order_packing_charges_sgst_percent")
    private float orderPackingChargesSgstPercent;
    @JsonProperty("order_packing_charges_igst_percent")
    private float orderPackingChargesIgstPercent;
    @JsonProperty("restaurant_name")
    private String restaurantName;
    @JsonProperty("customer_id")
    private String customerId;
    @JsonProperty("delivery_fee_coupon_restaurant_discount")
    private float deliveryFeeCouponRestaurantDiscount;
    /*@JsonProperty("order_cess_charges")
    private float orderCessCharges;*/
	/*@JsonProperty("order_cess_expressions")
	private float orderCessExpressions;*/
    @JsonProperty("offer_data")
    private List<SwiggyOrderOfferData> offerDataList;
    @JsonProperty("is_thirty_mof")
    private boolean isThirtyMof;
	@JsonProperty("meals")
	private List<String> meals;
    @JsonProperty("payment_qr_url")
    private String paymentQrUrl;
    @JsonProperty("transaction_id")
    private String transactionId;
    @JsonProperty("gst_breakup")
    private GstBreakup gstBreakup;
    @JsonProperty("prep_time_details")
    private PrepTimeDetails prepTimeDetails;

    @JsonProperty("callback_url")
    public String getCallbackUrl() {
        return callbackUrl;
    }

    @JsonProperty("callback_url")
    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    @JsonProperty("cart_cgst")
    public float getCartCgst() {
        return cartCgst;
    }

    @JsonProperty("cart_cgst")
    public void setCartCgst(float cartCgst) {
        this.cartCgst = cartCgst;
    }

    @JsonProperty("cart_cgst_percent")
    public float getCartCgstPercent() {
        return cartCgstPercent;
    }

    @JsonProperty("cart_cgst_percent")
    public void setCartCgstPercent(float cartCgstPercent) {
        this.cartCgstPercent = cartCgstPercent;
    }

    @JsonProperty("cart_gst")
    public float getCartGst() {
        return cartGst;
    }

    @JsonProperty("cart_gst")
    public void setCartGst(float cartGst) {
        this.cartGst = cartGst;
    }

    @JsonProperty("cart_gst_percent")
    public float getCartGstPercent() {
        return cartGstPercent;
    }

    @JsonProperty("cart_gst_percent")
    public void setCartGstPercent(float cartGstPercent) {
        this.cartGstPercent = cartGstPercent;
    }

    @JsonProperty("cart_igst")
    public float getCartIgst() {
        return cartIgst;
    }

    @JsonProperty("cart_igst")
    public void setCartIgst(float cartIgst) {
        this.cartIgst = cartIgst;
    }

    @JsonProperty("cart_igst_percent")
    public float getCartIgstPercent() {
        return cartIgstPercent;
    }

    @JsonProperty("cart_igst_percent")
    public void setCartIgstPercent(float cartIgstPercent) {
        this.cartIgstPercent = cartIgstPercent;
    }

    @JsonProperty("cart_sgst")
    public float getCartSgst() {
        return cartSgst;
    }

    @JsonProperty("cart_sgst")
    public void setCartSgst(float cartSgst) {
        this.cartSgst = cartSgst;
    }

    @JsonProperty("cart_sgst_percent")
    public float getCartSgstPercent() {
        return cartSgstPercent;
    }

    @JsonProperty("cart_sgst_percent")
    public void setCartSgstPercent(float cartSgstPercent) {
        this.cartSgstPercent = cartSgstPercent;
    }

    @JsonProperty("customer_address")
    public String getCustomerAddress() {
        return customerAddress;
    }

    @JsonProperty("customer_address")
    public void setCustomerAddress(String customerAddress) {
        this.customerAddress = customerAddress;
    }

    @JsonProperty("customer_area")
    public String getCustomerArea() {
        return customerArea;
    }

    @JsonProperty("customer_area")
    public void setCustomerArea(String customerArea) {
        this.customerArea = customerArea;
    }

    @JsonProperty("customer_city")
    public String getCustomerCity() {
        return customerCity;
    }

    @JsonProperty("customer_city")
    public void setCustomerCity(String customerCity) {
        this.customerCity = customerCity;
    }

    @JsonProperty("customer_name")
    public String getCustomerName() {
        return customerName;
    }

    @JsonProperty("customer_name")
    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    @JsonProperty("customer_phone")
    public String getCustomerPhone() {
        return customerPhone;
    }

    @JsonProperty("customer_phone")
    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    @JsonProperty("delivery_type")
    public String getDeliveryType() {
        return deliveryType;
    }

    @JsonProperty("delivery_type")
    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    @JsonProperty("instructions")
    public String getInstructions() {
        return instructions;
    }

    @JsonProperty("instructions")
    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }

    @JsonProperty("items")
    public List<Item> getItems() {
        return items;
    }

    @JsonProperty("items")
    public void setItems(List<Item> items) {
        this.items = items;
    }

    @JsonProperty("order_date_time")
    public String getOrderDateTime() {
        return orderDateTime;
    }

    @JsonProperty("order_date_time")
    public void setOrderDateTime(String orderDateTime) {
        this.orderDateTime = orderDateTime;
    }

    @JsonProperty("order_edit")
    public boolean isOrderEdit() {
        return orderEdit;
    }

    @JsonProperty("order_edit")
    public void setOrderEdit(boolean orderEdit) {
        this.orderEdit = orderEdit;
    }

    @JsonProperty("order_edit_reason")
    public Object getOrderEditReason() {
        return orderEditReason;
    }

    @JsonProperty("order_edit_reason")
    public void setOrderEditReason(Object orderEditReason) {
        this.orderEditReason = orderEditReason;
    }

    @JsonProperty("order_id")
    public String getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("order_packing_charges")
    public float getOrderPackingCharges() {
        return orderPackingCharges;
    }

    @JsonProperty("order_packing_charges")
    public void setOrderPackingCharges(float orderPackingCharges) {
        this.orderPackingCharges = orderPackingCharges;
    }

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("payment_type")
    public String getPaymentType() {
        return paymentType;
    }

    @JsonProperty("payment_type")
    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    @JsonProperty("restaurant_discount")
    public float getRestaurantDiscount() {
        return restaurantDiscount;
    }

    @JsonProperty("restaurant_discount")
    public void setRestaurantDiscount(float restaurantDiscount) {
        this.restaurantDiscount = restaurantDiscount;
    }

    @JsonProperty("restaurant_gross_bill")
    public float getRestaurantGrossBill() {
        return restaurantGrossBill;
    }

    @JsonProperty("restaurant_gross_bill")
    public void setRestaurantGrossBill(float restaurantGrossBill) {
        this.restaurantGrossBill = restaurantGrossBill;
    }

    @JsonProperty("restaurant_service_charges")
    public float getRestaurantServiceCharges() {
        return restaurantServiceCharges;
    }

    @JsonProperty("restaurant_service_charges")
    public void setRestaurantServiceCharges(float restaurantServiceCharges) {
        this.restaurantServiceCharges = restaurantServiceCharges;
    }

    @JsonProperty("order_type")
    public String getOrderType() {
        return orderType;
    }

    @JsonProperty("order_type")
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    @JsonProperty("reward_type")
    public String getRewardType() {
        return rewardType;
    }

    @JsonProperty("reward_type")
    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    @JsonProperty("order_packing_charges_cgst")
    public float getOrderPackingChargesCgst() {
        return orderPackingChargesCgst;
    }

    public void setOrderPackingChargesCgst(float orderPackingChargesCgst) {
        this.orderPackingChargesCgst = orderPackingChargesCgst;
    }

    @JsonProperty("order_packing_charges_sgst")
    public float getOrderPackingChargesSgst() {
        return orderPackingChargesSgst;
    }

    public void setOrderPackingChargesSgst(float orderPackingChargesSgst) {
        this.orderPackingChargesSgst = orderPackingChargesSgst;
    }

    @JsonProperty("order_packing_charges_igst")
    public float getOrderPackingChargesIgst() {
        return orderPackingChargesIgst;
    }

    public void setOrderPackingChargesIgst(float orderPackingChargesIgst) {
        this.orderPackingChargesIgst = orderPackingChargesIgst;
    }

    @JsonProperty("order_packing_charges_gst")
    public float getOrderPackingChargesGst() {
        return orderPackingChargesGst;
    }

    public void setOrderPackingChargesGst(float orderPackingChargesGst) {
        this.orderPackingChargesGst = orderPackingChargesGst;
    }

    @JsonProperty("order_packing_charges_cgst_percent")
    public float getOrderPackingChargesCgstPercent() {
        return orderPackingChargesCgstPercent;
    }

    public void setOrderPackingChargesCgstPercent(float orderPackingChargesCgstPercent) {
        this.orderPackingChargesCgstPercent = orderPackingChargesCgstPercent;
    }

    @JsonProperty("order_packing_charges_sgst_percent")
    public float getOrderPackingChargesSgstPercent() {
        return orderPackingChargesSgstPercent;
    }

    public void setOrderPackingChargesSgstPercent(float orderPackingChargesSgstPercent) {
        this.orderPackingChargesSgstPercent = orderPackingChargesSgstPercent;
    }

    @JsonProperty("order_packing_charges_igst_percent")
    public float getOrderPackingChargesIgstPercent() {
        return orderPackingChargesIgstPercent;
    }

    public void setOrderPackingChargesIgstPercent(float orderPackingChargesIgstPercent) {
        this.orderPackingChargesIgstPercent = orderPackingChargesIgstPercent;
    }

    @JsonProperty("restaurant_name")
    public String getRestaurantName() {
        return restaurantName;
    }

    @JsonProperty("restaurant_name")
    public void setRestaurantName(String restaurantName) {
        this.restaurantName = restaurantName;
    }

    @JsonProperty("customer_id")
    public String getCustomerId() {
        return customerId;
    }

    @JsonProperty("customer_id")
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    @JsonProperty("delivery_fee_coupon_restaurant_discount")
    public float getDeliveryFeeCouponRestaurantDiscount() {
        return deliveryFeeCouponRestaurantDiscount;
    }

    @JsonProperty("delivery_fee_coupon_restaurant_discount")
    public void setDeliveryFeeCouponRestaurantDiscount(float deliveryFeeCouponRestaurantDiscount) {
        this.deliveryFeeCouponRestaurantDiscount = deliveryFeeCouponRestaurantDiscount;
    }

    @JsonProperty("offer_data")
    public List<SwiggyOrderOfferData> getOfferDataList() {
        return offerDataList;
    }

    @JsonProperty("offer_data")
    public void setOfferDataList(List<SwiggyOrderOfferData> offerDataList) {
        this.offerDataList = offerDataList;
    }

    @JsonProperty("is_thirty_mof")
    public boolean isThirtyMof() {
        return isThirtyMof;
    }

    @JsonProperty("is_thirty_mof")
    public void setThirtyMof(boolean thirtyMof) {
        isThirtyMof = thirtyMof;
    }

    @JsonProperty("meals")
    public List<String> getMeals() {
        return meals;
    }

    @JsonProperty("meals")
    public void setMeals(List<String> meals) {
        this.meals = meals;
    }

    @JsonProperty("payment_qr_url")
    public String getPaymentQrUrl() {
        return paymentQrUrl;
    }

    @JsonProperty("payment_qr_url")
    public void setPaymentQrUrl(String paymentQrUrl) {
        this.paymentQrUrl = paymentQrUrl;
    }

    @JsonProperty("transaction_id")
    public String getTransactionId() {
        return transactionId;
    }

    @JsonProperty("transaction_id")
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @JsonProperty("gst_breakup")
    public GstBreakup getGstBreakup() {
        return gstBreakup;
    }

    @JsonProperty("gst_breakup")
    public void setGstBreakup(GstBreakup gstBreakup) {
        this.gstBreakup = gstBreakup;
    }

    @JsonProperty("prep_time_details")
    public PrepTimeDetails getPrepTimeDetails() {
        return prepTimeDetails;
    }

    @JsonProperty("prep_time_details")
    public void setPrepTimeDetails(PrepTimeDetails prepTimeDetails) {
        this.prepTimeDetails = prepTimeDetails;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        SwiggyOrderRequest that = (SwiggyOrderRequest) o;

        return new EqualsBuilder()
                .append(cartCgst, that.cartCgst)
                .append(cartCgstPercent, that.cartCgstPercent)
                .append(cartGst, that.cartGst)
                .append(cartGstPercent, that.cartGstPercent)
                .append(cartIgst, that.cartIgst)
                .append(cartIgstPercent, that.cartIgstPercent)
                .append(cartSgst, that.cartSgst)
                .append(cartSgstPercent, that.cartSgstPercent)
                .append(orderEdit, that.orderEdit)
                .append(orderPackingCharges, that.orderPackingCharges)
                .append(restaurantDiscount, that.restaurantDiscount)
                .append(restaurantGrossBill, that.restaurantGrossBill)
                .append(restaurantServiceCharges, that.restaurantServiceCharges)
                .append(orderPackingChargesCgst, that.orderPackingChargesCgst)
                .append(orderPackingChargesSgst, that.orderPackingChargesSgst)
                .append(orderPackingChargesIgst, that.orderPackingChargesIgst)
                .append(orderPackingChargesGst, that.orderPackingChargesGst)
                .append(orderPackingChargesCgstPercent, that.orderPackingChargesCgstPercent)
                .append(orderPackingChargesSgstPercent, that.orderPackingChargesSgstPercent)
                .append(orderPackingChargesIgstPercent, that.orderPackingChargesIgstPercent)
                .append(deliveryFeeCouponRestaurantDiscount, that.deliveryFeeCouponRestaurantDiscount)
                .append(isThirtyMof, that.isThirtyMof)
                .append(callbackUrl, that.callbackUrl)
                .append(customerAddress, that.customerAddress)
                .append(customerArea, that.customerArea)
                .append(customerCity, that.customerCity)
                .append(customerName, that.customerName)
                .append(customerPhone, that.customerPhone)
                .append(deliveryType, that.deliveryType)
                .append(instructions, that.instructions)
                .append(items, that.items)
                .append(orderDateTime, that.orderDateTime)
                .append(orderEditReason, that.orderEditReason)
                .append(orderId, that.orderId)
                .append(outletId, that.outletId)
                .append(paymentType, that.paymentType)
                .append(orderType, that.orderType)
                .append(rewardType, that.rewardType)
                .append(restaurantName, that.restaurantName)
                .append(customerId, that.customerId)
                .append(offerDataList, that.offerDataList)
                .append(isThirtyMof, that.isThirtyMof)
                .append(meals, that.meals)
                .append(paymentQrUrl, that.paymentQrUrl)
                .append(transactionId, that.transactionId)
                .append(gstBreakup, that.gstBreakup)
                .append(prepTimeDetails, that.prepTimeDetails)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(callbackUrl)
                .append(cartCgst)
                .append(cartCgstPercent)
                .append(cartGst)
                .append(cartGstPercent)
                .append(cartIgst)
                .append(cartIgstPercent)
                .append(cartSgst)
                .append(cartSgstPercent)
                .append(customerAddress)
                .append(customerArea)
                .append(customerCity)
                .append(customerName)
                .append(customerPhone)
                .append(deliveryType)
                .append(instructions)
                .append(items)
                .append(orderDateTime)
                .append(orderEdit)
                .append(orderEditReason)
                .append(orderId)
                .append(orderPackingCharges)
                .append(outletId)
                .append(paymentType)
                .append(restaurantDiscount)
                .append(restaurantGrossBill)
                .append(restaurantServiceCharges)
                .append(orderType)
                .append(rewardType)
                .append(orderPackingChargesCgst)
                .append(orderPackingChargesSgst)
                .append(orderPackingChargesIgst)
                .append(orderPackingChargesGst)
                .append(orderPackingChargesCgstPercent)
                .append(orderPackingChargesSgstPercent)
                .append(orderPackingChargesIgstPercent)
                .append(restaurantName)
                .append(customerId)
                .append(deliveryFeeCouponRestaurantDiscount)
                .append(offerDataList)
                .append(isThirtyMof)
                .append(meals)
                .append(paymentQrUrl)
                .append(transactionId)
                .append(gstBreakup)
                .append(prepTimeDetails)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "SwiggyOrderRequest{" +
                "callbackUrl='" + callbackUrl + '\'' +
                ", cartCgst=" + cartCgst +
                ", cartCgstPercent=" + cartCgstPercent +
                ", cartGst=" + cartGst +
                ", cartGstPercent=" + cartGstPercent +
                ", cartIgst=" + cartIgst +
                ", cartIgstPercent=" + cartIgstPercent +
                ", cartSgst=" + cartSgst +
                ", cartSgstPercent=" + cartSgstPercent +
                ", customerAddress='" + customerAddress + '\'' +
                ", customerArea='" + customerArea + '\'' +
                ", customerCity='" + customerCity + '\'' +
                ", customerName='" + customerName + '\'' +
                ", customerPhone='" + customerPhone + '\'' +
                ", deliveryType='" + deliveryType + '\'' +
                ", instructions='" + instructions + '\'' +
                ", items=" + items +
                ", orderDateTime='" + orderDateTime + '\'' +
                ", orderEdit=" + orderEdit +
                ", orderEditReason=" + orderEditReason +
                ", orderId='" + orderId + '\'' +
                ", orderPackingCharges=" + orderPackingCharges +
                ", outletId='" + outletId + '\'' +
                ", paymentType='" + paymentType + '\'' +
                ", restaurantDiscount=" + restaurantDiscount +
                ", restaurantGrossBill=" + restaurantGrossBill +
                ", restaurantServiceCharges=" + restaurantServiceCharges +
                ", orderType='" + orderType + '\'' +
                ", rewardType='" + rewardType + '\'' +
                ", orderPackingChargesCgst=" + orderPackingChargesCgst +
                ", orderPackingChargesSgst=" + orderPackingChargesSgst +
                ", orderPackingChargesIgst=" + orderPackingChargesIgst +
                ", orderPackingChargesGst=" + orderPackingChargesGst +
                ", orderPackingChargesCgstPercent=" + orderPackingChargesCgstPercent +
                ", orderPackingChargesSgstPercent=" + orderPackingChargesSgstPercent +
                ", orderPackingChargesIgstPercent=" + orderPackingChargesIgstPercent +
                ", restaurantName='" + restaurantName + '\'' +
                ", customerId='" + customerId + '\'' +
                ", deliveryFeeCouponRestaurantDiscount=" + deliveryFeeCouponRestaurantDiscount +
                ", offerDataList=" + offerDataList +
                ", isThirtyMof=" + isThirtyMof +
                '}';
    }
}
