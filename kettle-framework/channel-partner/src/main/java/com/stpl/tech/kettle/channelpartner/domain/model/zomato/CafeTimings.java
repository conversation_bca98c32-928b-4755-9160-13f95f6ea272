package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CafeTimings implements Serializable {

    private String outlet_id;

    private List<Timings> timings;

    private boolean isActive;

}
