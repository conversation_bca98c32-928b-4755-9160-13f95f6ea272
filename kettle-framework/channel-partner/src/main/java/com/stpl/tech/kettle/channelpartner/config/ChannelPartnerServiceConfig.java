package com.stpl.tech.kettle.channelpartner.config;

import java.util.TimeZone;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.stpl.tech.master.core.config.MasterCacheClientConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;

@Configuration
@EnableScheduling
@Import(value = { ChannelPartnerConfig.class, MasterExternalConfig.class, MasterCacheClientConfig.class })
public class ChannelPartnerServiceConfig {

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}
}