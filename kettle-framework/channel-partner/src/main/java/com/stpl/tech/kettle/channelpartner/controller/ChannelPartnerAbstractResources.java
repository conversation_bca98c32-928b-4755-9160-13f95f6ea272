package com.stpl.tech.kettle.channelpartner.controller;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerError;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;

public abstract class ChannelPartnerAbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerAbstractResources.class);

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(ChannelPartnerException.class)
    @ResponseBody
    public ChannelPartnerError handleException(HttpServletRequest req, ChannelPartnerException ex) {
        LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
        return ex.getCode();
    }
}
