
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "category_id",
        "category_name",
        "category_description",
        "category_is_active",
        "category_image_url",
        "category_tags",
        "category_order",
        "category_schedules",
        "has_subcategory",
        "items",
        "subcategories"
})
public class OldZomatoCategory {

    @JsonProperty("category_id")
    private String categoryId;
    @JsonProperty("category_name")
    private String categoryName;
    @JsonProperty("category_description")
    private String categoryDescription;
    @JsonProperty("category_is_active")
    private Integer categoryIsActive;
    @JsonProperty("category_image_url")
    private String categoryImageUrl;
    @JsonProperty("category_tags")
    private List<Integer> categoryTags = null;
    @JsonProperty("category_order")
    private Integer categoryOrder;
    @JsonProperty("category_schedules")
    private List<CategorySchedule> categorySchedules = null;
    @JsonProperty("has_subcategory")
    private Integer hasSubcategory;
    @JsonProperty("items")
    private List<ZomatoMenuItem> items = null;
    @JsonProperty("subcategories")
    private List<OldZomatoSubcategory> subcategories = null;

    @JsonProperty("category_id")
    public String getCategoryId() {
        return categoryId;
    }

    @JsonProperty("category_id")
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    @JsonProperty("category_name")
    public String getCategoryName() {
        return categoryName;
    }

    @JsonProperty("category_name")
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    @JsonProperty("category_description")
    public String getCategoryDescription() {
        return categoryDescription;
    }

    @JsonProperty("category_description")
    public void setCategoryDescription(String categoryDescription) {
        this.categoryDescription = categoryDescription;
    }

    @JsonProperty("category_is_active")
    public Integer getCategoryIsActive() {
        return categoryIsActive;
    }

    @JsonProperty("category_is_active")
    public void setCategoryIsActive(Integer categoryIsActive) {
        this.categoryIsActive = categoryIsActive;
    }

    @JsonProperty("category_image_url")
    public String getCategoryImageUrl() {
        return categoryImageUrl;
    }

    @JsonProperty("category_image_url")
    public void setCategoryImageUrl(String categoryImageUrl) {
        this.categoryImageUrl = categoryImageUrl;
    }

    @JsonProperty("category_tags")
    public List<Integer> getCategoryTags() {
        return categoryTags;
    }

    @JsonProperty("category_tags")
    public void setCategoryTags(List<Integer> categoryTags) {
        this.categoryTags = categoryTags;
    }

    @JsonProperty("category_order")
    public Integer getCategoryOrder() {
        return categoryOrder;
    }

    @JsonProperty("category_order")
    public void setCategoryOrder(Integer categoryOrder) {
        this.categoryOrder = categoryOrder;
    }

    @JsonProperty("category_schedules")
    public List<CategorySchedule> getCategorySchedules() {
        return categorySchedules;
    }

    @JsonProperty("category_schedules")
    public void setCategorySchedules(List<CategorySchedule> categorySchedules) {
        this.categorySchedules = categorySchedules;
    }

    @JsonProperty("has_subcategory")
    public Integer getHasSubcategory() {
        return hasSubcategory;
    }

    @JsonProperty("has_subcategory")
    public void setHasSubcategory(Integer hasSubcategory) {
        this.hasSubcategory = hasSubcategory;
    }

    @JsonProperty("items")
    public List<ZomatoMenuItem> getItems() {
        return items;
    }

    @JsonProperty("items")
    public void setItems(List<ZomatoMenuItem> items) {
        this.items = items;
    }

    @JsonProperty("subcategories")
    public List<OldZomatoSubcategory> getSubcategories() {
        return subcategories;
    }

    @JsonProperty("subcategories")
    public void setSubcategories(List<OldZomatoSubcategory> subcategories) {
        this.subcategories = subcategories;
    }

    @Override
    public String toString() {
        return "ZomatoCategory{" +
                "categoryId='" + categoryId + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", categoryDescription='" + categoryDescription + '\'' +
                ", categoryIsActive=" + categoryIsActive +
                ", categoryImageUrl='" + categoryImageUrl + '\'' +
                ", categoryTags=" + categoryTags +
                ", categoryOrder=" + categoryOrder +
                ", categorySchedules=" + categorySchedules +
                ", hasSubcategory=" + hasSubcategory +
                ", items=" + items +
                ", subcategories=" + subcategories +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        OldZomatoCategory that = (OldZomatoCategory) o;

        return new EqualsBuilder()
                .append(categoryId, that.categoryId)
                .append(categoryName, that.categoryName)
                .append(categoryDescription, that.categoryDescription)
                .append(categoryIsActive, that.categoryIsActive)
                .append(categoryImageUrl, that.categoryImageUrl)
                .append(categoryTags, that.categoryTags)
                .append(categoryOrder, that.categoryOrder)
                .append(categorySchedules, that.categorySchedules)
                .append(hasSubcategory, that.hasSubcategory)
                .append(items, that.items)
                .append(subcategories, that.subcategories)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(categoryId)
                .append(categoryName)
                .append(categoryDescription)
                .append(categoryIsActive)
                .append(categoryImageUrl)
                .append(categoryTags)
                .append(categoryOrder)
                .append(categorySchedules)
                .append(hasSubcategory)
                .append(items)
                .append(subcategories)
                .toHashCode();
    }
}
