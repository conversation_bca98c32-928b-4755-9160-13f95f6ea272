package com.stpl.tech.kettle.test.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.controller.ZomatoOrderAbstractResource;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderResponse;
import com.stpl.tech.kettle.test.domain.BulkAddOrderResponse;
import com.stpl.tech.kettle.test.mongo.model.PartnerOrderDetail;
import com.stpl.tech.kettle.test.service.TestZomatoService;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.ZOMATO_TEST_ROOT_CONTEXT;

@Slf4j
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + ZOMATO_TEST_ROOT_CONTEXT) // 'v1/zomato-test'
public class ZomatoTestResource extends ZomatoOrderAbstractResource {

    @Autowired
    private TestZomatoService testZomatoService;

    @Autowired
    private EnvironmentProperties environmentProperties;


    @RequestMapping(method = RequestMethod.GET, value = "get-live-orders", produces = MediaType.APPLICATION_JSON)
    public List<PartnerOrderDetail> getLiveZomatoOrders(@RequestParam String partnerName, @RequestParam Integer startOrderId, @RequestParam Integer endOrderId) {
        log.error("&&&&&&&&&&&&&&&&& GETTING ZOMATO LIVE ORDERS with startOrderId {} and endOrderId {} --------------------------", startOrderId, endOrderId);
        try {
            return testZomatoService.getZomatoLiveOrders(partnerName, startOrderId, endOrderId);
        } catch (Exception e) {
            log.error("Exception faced while fetching zomato live orders ::::::::::::::::::::::::", e);
            return null;
        }
    }


    @RequestMapping(method = RequestMethod.GET, value = "get-live-order", produces = MediaType.APPLICATION_JSON)
    public ZomatoOrderRequestV3 getLiveZomatoOrders(@RequestParam String partnerName, @RequestParam Integer orderId) {
        log.error("&&&&&&&&&&&&&&&&& GETTING Partner Order detail for LIVE ORDER  with orderId:: {} --------------------------", orderId);
        try {
            PartnerOrderDetail partnerOrderDetail =testZomatoService.getPartnerOrder(orderId.toString());
            return (ZomatoOrderRequestV3) partnerOrderDetail.getPartnerOrder();
        } catch (Exception e) {
            log.error("Exception faced while fetching zomato live orders ::::::::::::::::::::::::", e);
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "raw-order-add", produces = MediaType.APPLICATION_JSON)
    public boolean addRawOrder(@RequestBody ZomatoOrderRequestV3 zomatoOrderRequestV3) {
        log.error("&&&&&&&&&&&&&&&&& Add Raw Order Request for external order Id :: {} --------------------------", zomatoOrderRequestV3.getOrderId());
        if(!AppUtils.isProd(environmentProperties.getEnvType())){
            testZomatoService.updateTestCustomerDetails(zomatoOrderRequestV3);
            try {
                return testZomatoService.addOrderRequest(zomatoOrderRequestV3, null);
            } catch (Exception e) {
                log.error("Exception faced while fetching zomato live orders ::::::::::::::::::::::::", e);
                return false;
            }
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "raw-order-add-in-range", produces = MediaType.APPLICATION_JSON)
    public boolean addRawOrderInRange(@RequestParam String partnerName, @RequestParam Integer startOrderId, @RequestParam Integer endOrderId) {
        log.error("&&&&&&&&&&&&&&&&& PROCESSING REQUEST TO CREATE ZOMATO LIVE ORDER FOR ENV :::{} --------------------------", environmentProperties.getEnvType().toString());
        ExecutorService taskExecutor = Executors.newFixedThreadPool(100);// or default 100
        List<PartnerOrderDetail> partnerOrderDetailList = testZomatoService.getZomatoLiveOrders(partnerName,startOrderId,endOrderId);
        List<Integer> responseStatus = new ArrayList<>();
        if (Objects.nonNull(partnerOrderDetailList) && !partnerOrderDetailList.isEmpty()) {
            for (PartnerOrderDetail partnerOrderDetail : partnerOrderDetailList) {
                taskExecutor.execute(() -> {
                    ZomatoOrderRequestV3 zomatoOrderRequest = (ZomatoOrderRequestV3) partnerOrderDetail.getPartnerOrder();
                    testZomatoService.updateTestCustomerDetails(zomatoOrderRequest);
                    try{
                        ZomatoOrderResponse orderResponse = addZomatoOrder(zomatoOrderRequest);
                        if (Objects.nonNull(orderResponse)) {
                            log.info("Response on processing  zomato add order request for partnerOrderId ::{} {}::::::::::::::::", zomatoOrderRequest.getOrderId(), new Gson().toJson(orderResponse));
                            if(orderResponse.getCode()!=200){
                                responseStatus.add(orderResponse.getCode());
                            }
                        }
                    }catch (Exception e ){
                        log.error("Exception while processing create order request for order id :{}::::::::::::",zomatoOrderRequest.getOrderId(),e);
                    }
                });
            }
            taskExecutor.shutdown();
            try {
                taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
            } catch (InterruptedException e) {
                log.error("--------------------INTERUPTION !!!!-------------Error in completion of order threads", e);
                return false;
            }
        }
        if(!responseStatus.isEmpty()){
            log.info("&&&&&&&&&&&&&&& Failed while sending raw add order request for partnerName :{} ::::::::",partnerName);
            return false ;
        }
        return  true ;
    }

    /*@RequestMapping(method = RequestMethod.POST, value = "bulk-order-add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean addRawOrders( @RequestBody TestZomatoOrderRequest testZomatoOrderRequest) {
        log.info("&&&&&&&&&&&&&&&&& PROCESSING BULK REQUEST FOR ZOMATO ORDER CREATE IN  ENV :::{} --------------------------", environmentProperties.getEnvType().toString());
        ExecutorService taskExecutor = Executors.newFixedThreadPool(100);// or default 100
        List<PartnerOrderDetail> partnerOrderDetailList =testZomatoOrderRequest.getZomatoOrderRequestList();
        AtomicBoolean isSuccess = new AtomicBoolean(true);
        if(!AppUtils.isProd(environmentProperties.getEnvType())){
            if (Objects.nonNull(partnerOrderDetailList) && !partnerOrderDetailList.isEmpty()) {
                for (PartnerOrderDetail partnerOrderDetail : partnerOrderDetailList) {
                    taskExecutor.execute(() -> {
                        ObjectMapper mapper = new ObjectMapper();
                        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                        String jsonString = new Gson().toJson(partnerOrderDetail.getPartnerOrder());
                        ZomatoOrderRequestV3 zomatoOrderRequest = null;
                        try {
                            zomatoOrderRequest = mapper.readValue(jsonString, ZomatoOrderRequestV3.class);
                        } catch(JsonProcessingException e) {
                            log.error("Exception while parsing zomato order request for partnerOrderId :{}::::::::::::::", partnerOrderDetail.getPartnerOrderId(),e );
                        }
                        testZomatoService.updateTestCustomerDetails(zomatoOrderRequest);
                        isSuccess.set(addOrderRequest(zomatoOrderRequest));
                    });
                }
                taskExecutor.shutdown();
                try {
                    taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
                } catch (InterruptedException e) {
                    log.error("--------------------INTERUPTION !!!!-------------Error in completion of order threads", e);
                    return false;
                }
            }
            return isSuccess.get();
        }
        log.info("&&&&&&&&&&&&&&&&&&  Cannot process bulk create order request for this env ::::{}:::::::::::::::::", environmentProperties.getEnvType().toString());
        return  false ;
    }*/

    @RequestMapping(method = RequestMethod.POST, value = "bulk-order-add", produces = MediaType.APPLICATION_JSON)
    public BulkAddOrderResponse addRawOrders(@RequestParam String partnerName, @RequestParam Integer startOrderId , @RequestParam Integer endOrderId) {
        List<PartnerOrderDetail> partnerOrderDetailList = new ArrayList<>();
        Map<String,Object> failedOrderMap = new HashMap<>();
        if(!AppUtils.isProd(environmentProperties.getEnvType())){
            try{
                partnerOrderDetailList =testZomatoService.sendRequestToGetLivePartnerOrderData(partnerName,startOrderId,endOrderId);
                testZomatoService.addBatchedOrders(partnerOrderDetailList,startOrderId,endOrderId,partnerName,failedOrderMap );
            }catch (Exception e ){
                log.error("Exception while processing bulk get order request ::::::::",e );
            }
            if(!failedOrderMap.isEmpty()){
                return BulkAddOrderResponse.builder().isSuccess(false).failedOrdersMap(failedOrderMap).build();
            }else{
                return BulkAddOrderResponse.builder().isSuccess(true).failedOrdersMap(null).build();
            }
        }
        failedOrderMap.put("Cannot add order for this env", null);
        return BulkAddOrderResponse.builder().isSuccess(false).failedOrdersMap(failedOrderMap).build();
    }

}

