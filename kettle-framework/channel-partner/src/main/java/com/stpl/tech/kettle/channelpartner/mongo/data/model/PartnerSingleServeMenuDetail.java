package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "PartnerSingleServeUnitMenuDetail")
public class PartnerSingleServeMenuDetail {

    private String id;
    private String partnerName;
    private Integer partnerId;
    private Integer unitId;
    private String region;
    private Object menuData;
    private Date addTime;
    private String addTimeIST;
    private Boolean active = false;
    private Integer employeeId;
    private String employeeName;
    private Integer brandId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Object getMenuData() {
        return menuData;
    }

    public void setMenuData(Object menuData) {
        this.menuData = menuData;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public String getAddTimeIST() {
        return addTimeIST;
    }

    public void setAddTimeIST(String addTimeIST) {
        this.addTimeIST = addTimeIST;
    }
}
