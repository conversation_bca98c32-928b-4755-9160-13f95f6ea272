package com.stpl.tech.kettle.channelpartner.config;

import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import com.stpl.tech.spring.config.SpringUtilityServiceConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.data.redis.repository.configuration.EnableRedisRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Properties;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.TimeZone;

@SpringBootApplication
@Configuration
@EnableScheduling
@EnableTransactionManagement
@Import(value = {SpringUtilityServiceConfig.class, MasterSecurityConfiguration.class})
@ComponentScan(basePackages = {"com.stpl.tech.kettle.channelpartner", "com.stpl.tech.kettle.test"})
@EnableRedisRepositories(basePackages = {"com.stpl.tech.kettle.channelpartner.mysql.data.dao"})
@EnableMongoRepositories(basePackages = {"com.stpl.tech.kettle.channelpartner.mongo.data.dao","com.stpl.tech.kettle.test.mongo.dao"})
@EnableJpaRepositories(basePackages = {"com.stpl.tech.kettle.channelpartner.mysql.data.dao.impl"}, entityManagerFactoryRef = "CPDataSourceEMFactory", transactionManagerRef = "CPDataSourceTM")
//@EntityScan("com.stpl.tech.kettle.channelpartner.mysql.data.model")
public class ChannelPartnerConfig extends SpringBootServletInitializer {

    @Autowired
    private Environment env;

    public ChannelPartnerConfig() {
        super();
    }

    static {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
    }
    // beans

    public static void main(String[] args) {
        SpringApplication.run(ChannelPartnerConfig.class);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(ChannelPartnerConfig.class);
    }

    @Bean(name = "CPDataSourceEMFactory")
    public LocalContainerEntityManagerFactoryBean cpEntityManagerFactory() {
        final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(cpDataSource());
        em.setPackagesToScan("com.stpl.tech.kettle.channelpartner.mysql.data.model");
       final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        em.setJpaProperties(masterAdditionalProperties());
        em.setPersistenceUnitName("CPDataSourcePUName");
        return em;
    }

    @Bean(name = "CPDataSource")
    public DataSource cpDataSource() {
        final DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(env.getProperty("jdbc.driverClassName"));
        dataSource.setUrl(env.getProperty("jdbc.url"));
        dataSource.setUsername(env.getProperty("jdbc.user"));
        dataSource.setPassword(env.getProperty("jdbc.pass"));
        return dataSource;
    }

    @Bean(name = "CPDataSourceTM")
    public PlatformTransactionManager cpTransactionManager() {
        final JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(cpEntityManagerFactory().getObject());
        return transactionManager;
    }

    @Bean(name = "CPDataSourceET")
    public PersistenceExceptionTranslationPostProcessor cpExceptionTranslation() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

    private Properties masterAdditionalProperties() {
        final Properties hibernateProperties = new Properties();
        hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
        hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
        hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
        return hibernateProperties;
    }

//    @Bean
//    public MongoClient factory() throws UnknownHostException {
//        MongoClientURI uri = new MongoClientURI(env.getProperty("cp.mongo.uri"));
//        return new MongoClient(uri);
//    }
//
//    @Bean
//    public MongoDbFactory getMongoDbFactory() throws Exception {
//        return new SimpleMongoDbFactory(factory(), env.getProperty("cp.mongo.schema", "channelpartner"));
//    }
//
//    @Bean(name = "mongoTemplate")
//    public MongoTemplate getMongoTemplate() throws Exception {
//        return new MongoTemplate(getMongoDbFactory());
//    }

    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
        pool.setCorePoolSize(50);
        pool.setMaxPoolSize(75);
        pool.setWaitForTasksToCompleteOnShutdown(true);
        return pool;
    }

    @Bean
    public ScheduledThreadPoolExecutor scheduledThreadPoolExecutor(){
        ScheduledThreadPoolExecutor pool = new ScheduledThreadPoolExecutor(5);
        return pool;
    }






}
