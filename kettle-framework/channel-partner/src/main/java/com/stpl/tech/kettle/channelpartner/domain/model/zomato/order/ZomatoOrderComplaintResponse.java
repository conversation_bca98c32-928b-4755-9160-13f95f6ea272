package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "action",
        "order_id",
        "reference_id",
        "refund_amount",
        "refund_type",
        "no_refund_reason_id",
        "no_refund_reason"
})
public class ZomatoOrderComplaintResponse {

    @JsonProperty("order_id")
    private Long orderId;

    @JsonProperty("reference_id")
    private String referenceId;

    @JsonProperty("action")
    private String action;

    @JsonProperty("refund_type")
    private String refundType;

    @JsonProperty("refund_amount")
    private float refundAmount;

    @JsonProperty("no_refund_reason_id")
    private Integer noRefundReasonId;

    @JsonProperty("no_refund_reason")
    private String noRefundReason;

    @JsonProperty("order_id")
    public Long getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("reference_id")
    public String getReferenceId() {
        return referenceId;
    }

    @JsonProperty("reference_id")
    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    @JsonProperty("action")
    public String getAction() {
        return action;
    }
    @JsonProperty("action")
    public void setAction(String action) {
        this.action = action;
    }

    @JsonProperty("refund_type")
    public String getRefundType() {
        return refundType;
    }

    @JsonProperty("refund_type")
    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    @JsonProperty("refund_amount")
    public float getRefundAmount() {
        return refundAmount;
    }

    @JsonProperty("refund_amount")
    public void setRefundAmount(float refundAmount) {
        this.refundAmount = refundAmount;
    }

    @JsonProperty("no_refund_reason_id")
    public Integer getNoRefundReasonId() {
        return noRefundReasonId;
    }

    @JsonProperty("no_refund_reason_id")
    public void setNoRefundReasonId(Integer noRefundReasonId) {
        this.noRefundReasonId = noRefundReasonId;
    }

    @JsonProperty("no_refund_reason")
    public String getNoRefundReason() {
        return noRefundReason;
    }

    @JsonProperty("no_refund_reason")
    public void setNoRefundReason(String noRefundReason) {
        this.noRefundReason = noRefundReason;
    }
}
