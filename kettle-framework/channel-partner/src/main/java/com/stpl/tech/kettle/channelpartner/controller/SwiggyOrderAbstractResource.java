package com.stpl.tech.kettle.channelpartner.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.service.SwiggyService;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.net.URISyntaxException;
import java.util.Objects;

@Slf4j
public class SwiggyOrderAbstractResource extends ChannelPartnerAbstractResources{

    @Autowired
    private SwiggyService swiggyService;

    public SwiggyOrderResponse addSwiggyOrder(SwiggyOrderRequest order) throws ChannelPartnerException, URISyntaxException {
        if(Objects.nonNull(order)){
            if (order.getOutletId().equalsIgnoreCase(ChannelPartnerUtils.getSwiggyTestOutletId())) {
                return swiggyService.addSwiggyOrderInDev(order);
            } else {
                SwiggyOrderResponse response = swiggyService.addSwiggyOrder(order, false);
                log.info("Request to add swiggy order {}: {}", order.getOrderId(), new Gson().toJson(response));
                return response;
            }
        }
        return null;
    }

}
