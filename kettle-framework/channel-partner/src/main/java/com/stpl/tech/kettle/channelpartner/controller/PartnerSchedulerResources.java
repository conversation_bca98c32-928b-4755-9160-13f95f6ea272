package com.stpl.tech.kettle.channelpartner.controller;

import com.stpl.tech.kettle.channelpartner.core.scheduler.PartnerMenuPushScheduler;
import com.stpl.tech.kettle.channelpartner.core.scheduler.PartnerOrderCacheScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.CHANNEL_PARTNER_SCHEDULER_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + CHANNEL_PARTNER_SCHEDULER_ROOT_CONTEXT)
// 'v1/channel-partner-scheduler'
public class PartnerSchedulerResources extends ChannelPartnerAbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerSchedulerResources.class);

    @Autowired
    private PartnerOrderCacheScheduler partnerOrderCacheScheduler;

    @Autowired
    private PartnerMenuPushScheduler partnerMenuPushScheduler;

    @RequestMapping(method = RequestMethod.GET, value = "clear-unit-inventory-queue", produces = MediaType.APPLICATION_JSON)
    public void clearUnitInventoryRefreshDelayQueue() {
        PartnerOrderCacheScheduler.getDelayQueue().clear();
        LOG.info("PartnerOrderCacheScheduler delayQueue cleared");
    }

    @RequestMapping(method = RequestMethod.GET, value = "start-unit-stock-refresh-cron", produces = MediaType.APPLICATION_JSON)
    public void manualStockRefresh() {
        LOG.info("Calling PartnerOrderCacheScheduler manualStockRefresh");
        partnerOrderCacheScheduler.refreshAllUnitsStock();
        partnerOrderCacheScheduler.refreshAllUnitsStockForGNT();
    }

    @RequestMapping(method = RequestMethod.GET, value = "start-menu-push-cron", produces = MediaType.APPLICATION_JSON)
    public void manualMenuPushCronStart() {
        LOG.info("Calling partnerOrderCacheScheduler pushMenuForDaySlot");
        partnerMenuPushScheduler.pushMenuForDaySlot();
    }
}
