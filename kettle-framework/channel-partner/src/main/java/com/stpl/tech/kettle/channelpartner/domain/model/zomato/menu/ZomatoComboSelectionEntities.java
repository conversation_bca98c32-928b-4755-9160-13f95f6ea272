package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "variantVendorEntityId",
        "catalogueVendorEntityId"
})
public class ZomatoComboSelectionEntities {

    @JsonProperty("variantVendorEntityId")
    private String variantVendorEntityId;
    @JsonProperty("catalogueVendorEntityId")
    private String catalogueVendorEntityId;

    @JsonProperty("variantVendorEntityId")
    public String getVariantVendorEntityId() {
        return variantVendorEntityId;
    }

    @JsonProperty("variantVendorEntityId")
    public void setVariantVendorEntityId(String variantVendorEntityId) {
        this.variantVendorEntityId = variantVendorEntityId;
    }

    @JsonProperty("catalogueVendorEntityId")
    public String getCatalogueVendorEntityId() {
        return catalogueVendorEntityId;
    }

    @JsonProperty("catalogueVendorEntityId")
    public void setCatalogueVendorEntityId(String catalogueVendorEntityId) {
        this.catalogueVendorEntityId = catalogueVendorEntityId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ZomatoComboSelectionEntities that = (ZomatoComboSelectionEntities) o;
        return Objects.equals(variantVendorEntityId, that.variantVendorEntityId) &&
                Objects.equals(catalogueVendorEntityId, that.catalogueVendorEntityId);
    }

    @Override
    public int hashCode() {

        return Objects.hash(variantVendorEntityId, catalogueVendorEntityId);
    }

    @Override
    public String toString() {
        return "ZomatoComboSelectionEntities{" +
                "variantVendorEntityId='" + variantVendorEntityId + '\'' +
                ", catalogueVendorEntityId='" + catalogueVendorEntityId + '\'' +
                '}';
    }
}