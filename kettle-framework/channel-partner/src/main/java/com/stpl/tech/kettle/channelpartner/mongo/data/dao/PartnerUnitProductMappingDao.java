package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitProductMappingDetail;

@Repository
public interface PartnerUnitProductMappingDao extends MongoRepository<PartnerUnitProductMappingDetail, String> {

	public List<PartnerUnitProductMappingDetail> findAllByActiveAndPartnerIdAndUnitId(Boolean active, Integer partnerId,
			Integer unitId);

	public List<PartnerUnitProductMappingDetail> findAllByActiveAndPartnerIdAndUnitIdAndBrandId(Boolean active, Integer partnerId,
																					  Integer unitId, Integer brandId);

	public List<PartnerUnitProductMappingDetail> findAllByActiveAndPartnerId(Boolean active, Integer partnerId);

}
