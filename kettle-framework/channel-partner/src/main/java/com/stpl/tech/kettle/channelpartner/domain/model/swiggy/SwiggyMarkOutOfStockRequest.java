package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "external_restaurant_id",
        "edit_allowed",
        "oos_data"
})

public class SwiggyMarkOutOfStockRequest {

    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("external_restaurant_id")
    private String externalRestaurantId;

    @JsonProperty("edit_allowed")
    private String editAllowed;

    @JsonProperty("oos_data")
    private List<SwiggyMarkOutOfStockOosData> oosData;

    @JsonProperty("order_id")
    public String getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    @JsonProperty("external_restaurant_id")
    public String getExternalRestaurantId() {
        return externalRestaurantId;
    }
    @JsonProperty("external_restaurant_id")
    public void setExternalRestaurantId(String externalRestaurantId) {
        this.externalRestaurantId = externalRestaurantId;
    }
    @JsonProperty("edit_allowed")
    public String getEditAllowed() {
        return editAllowed;
    }
    @JsonProperty("edit_allowed")
    public void setEditAllowed(String editAllowed) {
        this.editAllowed = editAllowed;
    }
    @JsonProperty("oos_data")
    public List<SwiggyMarkOutOfStockOosData> getOosData() {
        return oosData;
    }
    @JsonProperty("oos_data")
    public void setOosData(List<SwiggyMarkOutOfStockOosData> oosData) {
        this.oosData = oosData;
    }
}
