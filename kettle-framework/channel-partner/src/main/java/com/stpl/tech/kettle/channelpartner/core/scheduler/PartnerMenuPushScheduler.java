package com.stpl.tech.kettle.channelpartner.core.scheduler;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEvent;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.redis.RedisPublisher;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;

@Component
public class PartnerMenuPushScheduler {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerMenuPushScheduler.class);

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private RedisPublisher redisPublisher;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvironmentProperties properties;

    @Scheduled(cron = "0 5 0,6,12,15,20,22 * * *", zone = "GMT+05:30")
    public void pushMenuForDaySlot() {
        LOG.info("CRON JOB: pushMenuForDaySlot : STARTED");
        Date date = ChannelPartnerUtils.getCurrentTimestamp();
        MenuType type = MenuType.getCurrentMenuType(date);
        if (properties.isScheduledMenuPush()) {
            List<Integer> unitIdsForMenu = getUnitIdsForMenuPush();
            List<Integer> partnerIds = new ArrayList<>();
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("ZOMATO").getKettlePartnerId());
            partnerIds.add(channelPartnerDataCache.getPartnerCache().get("SWIGGY").getKettlePartnerId());
            UnitMenuAddVO unitMenu = new UnitMenuAddVO();
            unitMenu.setMenuType(type);
            unitMenu.setUnitIdsForMenu(unitIdsForMenu);
            PartnerActionEvent event = new PartnerActionEvent();
            event.setEventType(PartnerActionEventType.SCHEDULED_MENU_PUSH);
            event.setEventData(unitMenu);
            for (Integer partnerId : partnerIds) {
                PartnerDetail partnerDetail = channelPartnerDataCache.getPartnerCacheById().get(partnerId);
                if (partnerDetail != null) {
                    LOG.info("PUBLISHING REDIS EVENT " + event.getEventType().name() + " topic: "
                        + partnerDetail.getPartnerName());
                    String message = new Gson().toJson(event);
                    redisPublisher.publish(partnerDetail.getPartnerName(), message);
                } else {
                    LOG.error("Error publishing SCHEDULED_MENU_PUSH event: partner id " + partnerId + " is not valid.");
                }
            }
        }
        LOG.info("CRON JOB: pushMenuForDaySlot : FINISHED");
    }

    private List<Integer> getUnitIdsForMenuPush() {
        List<Integer> unit = new ArrayList<>();
        if (EnvType.PROD.equals(properties.getEnvType()) || EnvType.SPROD.equals(properties.getEnvType())) {
            List<UnitBasicDetail> units = masterDataCache.getAllUnits();
            for (UnitBasicDetail unitBasicDetail : units) {
                if (unitBasicDetail.isLive() && UnitStatus.ACTIVE.equals(unitBasicDetail.getStatus())) {
                    unit.add(unitBasicDetail.getId());
                }
            }
        } else {
            unit.add(10000);
        }
        return unit;
    }
}
