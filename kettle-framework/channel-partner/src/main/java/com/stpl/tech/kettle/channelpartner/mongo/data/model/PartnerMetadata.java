package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Map;

@Document
@XmlRootElement(name = "PartnerMetadata")
public class PartnerMetadata {

    @Id
    private String metadataId;
    private Integer kettlePartnerId;
    private String partnerName;
    private Map<PartnerMetadataKey, String> metadata;

    public String getMetadataId() {
        return metadataId;
    }

    public void setMetadataId(String metadataId) {
        this.metadataId = metadataId;
    }

    public Integer getKettlePartnerId() {
        return kettlePartnerId;
    }

    public void setKettlePartnerId(Integer kettlePartnerId) {
        this.kettlePartnerId = kettlePartnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Map<PartnerMetadataKey, String> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<PartnerMetadataKey, String> metadata) {
        this.metadata = metadata;
    }
}
