package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"vendorEntityId", "description", "name", "inStock", "reducedPrice", "taxGroups",
        "charges", "type", "subtitle", "media", "selections", "services"})
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZomatoCombo {

    @JsonProperty("vendorEntityId")
    private String vendorEntityId;
    @JsonProperty("description")
    private String description;
    @JsonProperty("name")
    private String name;
    @JsonProperty("inStock")
    private boolean inStock;
    @JsonProperty("reducedPrice")
    private float reducedPrice;
    @JsonProperty("taxGroups")
    private List<ZomatoTaxGroup> taxGroups = null;
    @JsonProperty("charges")
    private List<ZomatoCatalogueCharges> charges = null;
    @JsonProperty("type")
    private String type;
    @JsonProperty("subtitle")
    private String subtitle;
    @JsonProperty("media")
    private List<ZomatoComboMedia> media;
    @JsonProperty("selections")
    private List<ZomatoComboSelection> selections;
    @JsonProperty("services")
    private List<ZomatoComboService> services;


    @JsonProperty("vendorEntityId")
    public String getVendorEntityId() {
        return vendorEntityId;
    }

    @JsonProperty("vendorEntityId")
    public void setVendorEntityId(String vendorEntityId) {
        this.vendorEntityId = vendorEntityId;
    }

    @JsonProperty("description")
    public String getDescription() {
        return description;
    }

    @JsonProperty("description")
    public void setDescription(String description) {
        this.description = description;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("inStock")
    public boolean getInStock() {
        return inStock;
    }

    @JsonProperty("inStock")
    public void setInStock(boolean inStock) {
        this.inStock = inStock;
    }

    @JsonProperty("reducedPrice")
    public float getReducedPrice() {
        return reducedPrice;
    }

    @JsonProperty("reducedPrice")
    public void setReducedPrice(float reducedPrice) {
        this.reducedPrice = reducedPrice;
    }

    @JsonProperty("taxGroups")
    public List<ZomatoTaxGroup> getTaxGroups() {
        return taxGroups;
    }

    @JsonProperty("taxGroups")
    public void setTaxGroups(List<ZomatoTaxGroup> taxGroups) {
        this.taxGroups = taxGroups;
    }

    @JsonProperty("charges")
    public List<ZomatoCatalogueCharges> getCharges() {
        return charges;
    }

    @JsonProperty("charges")
    public void setCharges(List<ZomatoCatalogueCharges> charges) {
        this.charges = charges;
    }

    @JsonProperty("type")
    public String getType() {
        return type;
    }

    @JsonProperty("type")
    public void setType(String type) {
        this.type = type;
    }

    @JsonProperty("subtitle")
    public String getSubtitle() {
        return subtitle;
    }

    @JsonProperty("subtitle")
    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    @JsonProperty("media")
    public List<ZomatoComboMedia> getMedia() {
        return media;
    }

    @JsonProperty("media")
    public void setMedia(List<ZomatoComboMedia> media) {
        this.media = media;
    }

    @JsonProperty("selections")
    public List<ZomatoComboSelection> getSelections() {
        return selections;
    }

    @JsonProperty("selections")
    public void setSelections(List<ZomatoComboSelection> selections) {
        this.selections = selections;
    }

    @JsonProperty("services")
    public List<ZomatoComboService> getServices() {
        return services;
    }

    @JsonProperty("services")
    public void setServices(List<ZomatoComboService> services) {
        this.services = services;
    }
}
