package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "timestamp",
        "swiggy_order_id",
        "external_order_id"
})
public class SwiggyMarkFoodReadyRequest {

    @JsonProperty("timestamp")
    private String timestamp;
    @JsonProperty("swiggy_order_id")
    private long swiggyOrderId;
    @JsonProperty("external_order_id")
    private String externalOrderId;

    @JsonProperty("timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    @JsonProperty("timestamp")
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @JsonProperty("swiggy_order_id")
    public long getSwiggyOrderId() {
        return swiggyOrderId;
    }

    @JsonProperty("swiggy_order_id")
    public void setSwiggyOrderId(long swiggyOrderId) {
        this.swiggyOrderId = swiggyOrderId;
    }

    @JsonProperty("external_order_id")
    public String getExternalOrderId() {
        return externalOrderId;
    }

    @JsonProperty("external_order_id")
    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("timestamp", timestamp).append("swiggyOrderId", swiggyOrderId).append("externalOrderId", externalOrderId).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(timestamp).append(externalOrderId).append(swiggyOrderId).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof SwiggyMarkFoodReadyRequest) == false) {
            return false;
        }
        SwiggyMarkFoodReadyRequest rhs = ((SwiggyMarkFoodReadyRequest) other);
        return new EqualsBuilder().append(timestamp, rhs.timestamp).append(externalOrderId, rhs.externalOrderId).append(swiggyOrderId, rhs.swiggyOrderId).isEquals();
    }

}