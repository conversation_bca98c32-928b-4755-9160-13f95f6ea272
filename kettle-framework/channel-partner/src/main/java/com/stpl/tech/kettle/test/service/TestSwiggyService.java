package com.stpl.tech.kettle.test.service;

import com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order.SwiggyOrderRequest;
import com.stpl.tech.kettle.test.domain.TestSwiggyOrderRequest;
import com.stpl.tech.kettle.test.mongo.model.PartnerOrderDetail;

import java.util.List;
import java.util.Map;

public interface TestSwiggyService {
    List<PartnerOrderDetail> getSwiggyLiveOrders(String partnerNamee , Integer startOrderId , Integer endOrderId);

    PartnerOrderDetail getPartnerOrder(String kettleOrderId);

   TestSwiggyOrderRequest sendRequestToGetLiveSwiggyOrderRequestData(String partnerName, Integer startOrderId, Integer endOrderId);

    boolean addSwiggyOrderRequest(SwiggyOrderRequest swiggyOrderRequest, Map<String, Object> failedOrderMap);

    boolean addSwiggyBatchedOrders(TestSwiggyOrderRequest bulkSwiggyOrderRequest, Integer startOrderId, Integer endOrderId, String partnerName, Map<String, Object> failedOrderMap);
}
