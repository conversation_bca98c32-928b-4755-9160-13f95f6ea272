package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "partnerOrderRiderDetails")
public class PartnerOrderRiderDetails {

    @Id
    protected String orderId;
    @Indexed
    protected String partnerOrderId;
    protected String partnerName;
    @Indexed
    private String riderName;
    private String riderContact;
    private Date addTime;
    private String addTimeIST;
    private String rbt;
    private String isHighTemp;
    private String isWearingMask;
    private PartnerMaskTempResponse partnerMaskTempResponse;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPartnerOrderId() {
        return partnerOrderId;
    }

    public void setPartnerOrderId(String partnerOrderId) {
        this.partnerOrderId = partnerOrderId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getRiderName() {
        return riderName;
    }

    public void setRiderName(String riderName) {
        this.riderName = riderName;
    }

    public String getRiderContact() {
        return riderContact;
    }

    public void setRiderContact(String riderContact) {
        this.riderContact = riderContact;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public String getRbt() {
        return rbt;
    }

    public void setRbt(String rbt) {
        this.rbt = rbt;
    }

    public String getIsHighTemp() {
        return isHighTemp;
    }

    public void setIsHighTemp(String isHighTemp) {
        this.isHighTemp = isHighTemp;
    }

    public String getIsWearingMask() {
        return isWearingMask;
    }

    public void setIsWearingMask(String isWearingMask) {
        this.isWearingMask = isWearingMask;
    }

    public PartnerMaskTempResponse getPartnerMaskTempResponse() {
        return partnerMaskTempResponse;
    }

    public void setPartnerMaskTempResponse(PartnerMaskTempResponse partnerMaskTempResponse) {
        this.partnerMaskTempResponse = partnerMaskTempResponse;
    }

    public String getAddTimeIST() {
        return addTimeIST;
    }

    public void setAddTimeIST(String addTimeIST) {
        this.addTimeIST = addTimeIST;
    }
}
