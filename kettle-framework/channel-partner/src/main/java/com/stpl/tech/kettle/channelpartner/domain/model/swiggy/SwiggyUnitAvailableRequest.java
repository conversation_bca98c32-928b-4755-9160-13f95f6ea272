package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "partnerid",
        "isRequestedToOpen",
        "fromTime",
        "toTime"
})
public class SwiggyUnitAvailableRequest {

    @JsonProperty("partnerid")
    private String partnerId;
    @JsonProperty("isRequestedToOpen")
    private boolean isRequestedToOpen;
    @JsonProperty("fromTime")
    private String fromTime;
    @JsonProperty("toTime")
    private String toTime;

    @JsonProperty("partnerid")
    public String getPartnerId() {
        return partnerId;
    }

    @JsonProperty("partnerid")
    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    @JsonProperty("isRequestedToOpen")
    public boolean isRequestedToOpen() {
        return isRequestedToOpen;
    }

    @JsonProperty("isRequestedToOpen")
    public void setRequestedToOpen(boolean requestedToOpen) {
        isRequestedToOpen = requestedToOpen;
    }

    @JsonProperty("fromTime")
    public String getFromTime() {
        return fromTime;
    }

    @JsonProperty("fromTime")
    public void setFromTime(String fromTime) {
        this.fromTime = fromTime;
    }

    @JsonProperty("toTime")
    public String getToTime() {
        return toTime;
    }

    @JsonProperty("toTime")
    public void setToTime(String toTime) {
        this.toTime = toTime;
    }
}
