package com.stpl.tech.kettle.channelpartner.mongo.data.model;


import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@Getter
@Setter
@Document
@XmlRootElement(name = "DotdProduct")
public class DotdProduct {

    @Id
    protected int productId;

    protected String productName;

    protected List<Integer> productsList;

    protected List<DotdProductDimensions> dotdProductDimensionsList;


}
