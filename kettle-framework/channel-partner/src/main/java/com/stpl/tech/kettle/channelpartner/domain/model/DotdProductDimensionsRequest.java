package com.stpl.tech.kettle.channelpartner.domain.model;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "dimensionId",
        "dimensionName",
        "dimensionCode"
})
public class DotdProductDimensionsRequest {

    @JsonProperty("dimensionId")
    private Integer dimensionId;

    @JsonProperty("dimensionName")
    private String dimensionName;

    @JsonProperty("dimensionCode")
    private String dimensionCode;
}
