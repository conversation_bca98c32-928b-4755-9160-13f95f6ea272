package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "charge_id",
        "charge_name",
        "charge_value",
        "charge_amount",
        "charge_type",
        "charge_taxes",
        "charge_taxes_total"
})
public class ZomatoChargeDetails {

    @JsonProperty("charge_id")
    private Integer chargeId;
    @JsonProperty("charge_name")
    private String chargeName;
    @JsonProperty("charge_value")
    private Float chargeValue;
    @JsonProperty("charge_amount")
    private Float chargeAmount;
    @JsonProperty("charge_type")
    private String chargeType;
    @JsonProperty("charge_taxes")
    private List<ZomatoTaxDetails> chargeTaxes = null;
    @JsonProperty("charge_taxes_total")
    private Float chargeTaxesTotal;
    @JsonProperty("is_delivery_charge")
    private Integer isDeliveryCharge;

    @JsonProperty("charge_id")
    public Integer getChargeId() {
        return chargeId;
    }

    @JsonProperty("charge_id")
    public void setChargeId(Integer chargeId) {
        this.chargeId = chargeId;
    }

    @JsonProperty("charge_name")
    public String getChargeName() {
        return chargeName;
    }

    @JsonProperty("charge_name")
    public void setChargeName(String chargeName) {
        this.chargeName = chargeName;
    }

    @JsonProperty("charge_value")
    public Float getChargeValue() {
        return chargeValue;
    }

    @JsonProperty("charge_value")
    public void setChargeValue(Float chargeValue) {
        this.chargeValue = chargeValue;
    }

    @JsonProperty("charge_amount")
    public Float getChargeAmount() {
        return chargeAmount;
    }

    @JsonProperty("charge_amount")
    public void setChargeAmount(Float chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    @JsonProperty("charge_type")
    public String getChargeType() {
        return chargeType;
    }

    @JsonProperty("charge_type")
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    @JsonProperty("charge_taxes")
    public List<ZomatoTaxDetails> getChargeTaxes() {
        return chargeTaxes;
    }

    @JsonProperty("charge_taxes")
    public void setChargeTaxes(List<ZomatoTaxDetails> chargeTaxes) {
        this.chargeTaxes = chargeTaxes;
    }

    @JsonProperty("charge_taxes_total")
    public Float getChargeTaxesTotal() {
        return chargeTaxesTotal;
    }

    @JsonProperty("charge_taxes_total")
    public void setChargeTaxesTotal(Float chargeTaxesTotal) {
        this.chargeTaxesTotal = chargeTaxesTotal;
    }

    @JsonProperty("is_delivery_charge")
    public Integer getIsDeliveryCharge() {
        return isDeliveryCharge;
    }

    @JsonProperty("is_delivery_charge")
    public void setIsDeliveryCharge(Integer isDeliveryCharge) {
        this.isDeliveryCharge = isDeliveryCharge;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("chargeId", chargeId).append("chargeName", chargeName).append("chargeValue", chargeValue).append("chargeAmount", chargeAmount).append("chargeType", chargeType).append("chargeTaxes", chargeTaxes).append("chargeTaxesTotal", chargeTaxesTotal).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(chargeAmount).append(chargeValue).append(chargeTaxes).append(chargeId).append(chargeTaxesTotal).append(chargeName).append(chargeType).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoChargeDetails) == false) {
            return false;
        }
        ZomatoChargeDetails rhs = ((ZomatoChargeDetails) other);
        return new EqualsBuilder().append(chargeAmount, rhs.chargeAmount).append(chargeValue, rhs.chargeValue).append(chargeTaxes, rhs.chargeTaxes).append(chargeId, rhs.chargeId).append(chargeTaxesTotal, rhs.chargeTaxesTotal).append(chargeName, rhs.chargeName).append(chargeType, rhs.chargeType).isEquals();
    }

}