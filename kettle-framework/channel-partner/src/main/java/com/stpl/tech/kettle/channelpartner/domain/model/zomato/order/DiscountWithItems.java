package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_item_id",
        "quantity",
        "zomato_item_id"
})
public class DiscountWithItems {

    @JsonProperty("order_item_id")
    private Integer orderItemId;
    @JsonProperty("quantity")
    private Integer quantity;
    @JsonProperty("zomato_item_id")
    private Integer zomatoItemId;

    @JsonProperty("order_item_id")
    public Integer getOrderItemId() {
        return orderItemId;
    }

    @JsonProperty("order_item_id")
    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    @JsonProperty("quantity")
    public Integer getQuantity() {
        return quantity;
    }

    @JsonProperty("quantity")
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @JsonProperty("zomato_item_id")
    public Integer getZomatoItemId() {
        return zomatoItemId;
    }

    @JsonProperty("zomato_item_id")
    public void setZomatoItemId(Integer zomatoItemId) {
        this.zomatoItemId = zomatoItemId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("orderItemId", orderItemId).append("quantity", quantity).append("zomatoItemId", zomatoItemId).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(orderItemId).append(zomatoItemId).append(quantity).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof DiscountWithItems) == false) {
            return false;
        }
        DiscountWithItems rhs = ((DiscountWithItems) other);
        return new EqualsBuilder().append(orderItemId, rhs.orderItemId).append(zomatoItemId, rhs.zomatoItemId).append(quantity, rhs.quantity).isEquals();
    }

}