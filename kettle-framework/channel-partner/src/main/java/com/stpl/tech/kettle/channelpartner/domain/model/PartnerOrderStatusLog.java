package com.stpl.tech.kettle.channelpartner.domain.model;

import java.util.Date;

public class PartnerOrderStatusLog {

    private PartnerOrderStatus fromStatus;
    private PartnerOrderStatus toStatus;
    private Date updateTime;
    private String updateTimeIST;
    private boolean manual;

    public PartnerOrderStatus getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(PartnerOrderStatus fromStatus) {
        this.fromStatus = fromStatus;
    }

    public PartnerOrderStatus getToStatus() {
        return toStatus;
    }

    public void setToStatus(PartnerOrderStatus toStatus) {
        this.toStatus = toStatus;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isManual() {
        return manual;
    }

    public void setManual(boolean manual) {
        this.manual = manual;
    }

    public String getUpdateTimeIST() {
        return updateTimeIST;
    }

    public void setUpdateTimeIST(String updateTimeIST) {
        this.updateTimeIST = updateTimeIST;
    }
}
