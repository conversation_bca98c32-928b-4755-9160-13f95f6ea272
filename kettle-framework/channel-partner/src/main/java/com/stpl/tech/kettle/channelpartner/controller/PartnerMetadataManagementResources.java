package com.stpl.tech.kettle.channelpartner.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.core.queue.model.PartnerActionEventType;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMenuService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMetadataFetchService;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerMetadataManagementService;
import com.stpl.tech.kettle.channelpartner.domain.model.BrandProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.CafeActivationTokenInfo;
import com.stpl.tech.kettle.channelpartner.domain.model.DealOfTheDayRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.MenuTrackResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductAliasVO;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductTagsVO;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerProductsTagsVU;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerUnitListVO;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductAlias;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappingVU;
import com.stpl.tech.kettle.channelpartner.domain.model.ProductTagsMappings;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitAutoSwitchOff;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuAddVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitMenuVersionData;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.UnitProductPartnerStatusVO;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsChangeRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoLogisticsStatusResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoNotificationResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoTakeawayStatusResponse;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPush;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.CafeMenuAutoPushLog;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DealOfTheDay;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfileMappings;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.DesiChaiCustomProfiles;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDeliveryStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuAuditHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerUnitMenuDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.UnitPartnerLocalityMapping;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdName;
import com.stpl.tech.master.domain.model.IdNameGroup;
import com.stpl.tech.master.domain.model.IdValueUnit;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.PARTNER_METADATA_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PARTNER_METADATA_MANAGEMENT_ROOT_CONTEXT)
// 'v1/partner-metadata-management'
public class PartnerMetadataManagementResources extends ChannelPartnerAbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerMetadataManagementResources.class);

    @Autowired
    private PartnerMetadataManagementService partnerMetadataManagementService;
    @Autowired
    private PartnerMetadataFetchService partnerMetadataFetchService;

    @Autowired
    private PartnerMenuService partnerMenuService;

    @Autowired
    private TokenService<CafeActivationTokenInfo> jwtService;

    @RequestMapping(method = RequestMethod.POST, value = "unit-toggle", produces = MediaType.APPLICATION_JSON)
    public boolean setUnitAvailability(@RequestBody UnitPartnerStatusVO request) throws ChannelPartnerException {
        LOG.info("Request to set unit availability status:" + new Gson().toJson(request));
        // set Activate Button Calling this ApI
        return partnerMetadataManagementService.setUnitAvailability(request);
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit-toggle-knockapp", produces = MediaType.APPLICATION_JSON)
    public boolean setOffUnitAvailibilityFromKnockApp(@RequestBody UnitAutoSwitchOff request) {
        LOG.info("Request to set unit availability status:" + new Gson().toJson(request));
        return partnerMetadataManagementService.setOffUnitAvailibilityFromKnockApp(request,false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-toggle-unit-off-dashboard", produces = MediaType.APPLICATION_JSON)
    public boolean setOnUnitFromUnitOffDashboard(@RequestBody UnitAutoSwitchOff request) {
        LOG.info("Request to set unit availability status:" + new Gson().toJson(request));
        return partnerMetadataManagementService.setOffUnitAvailibilityFromKnockApp(request,true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-toggle-cafedashboard", produces = MediaType.APPLICATION_JSON)
    public boolean setCafeUnitAvailability(@RequestParam Integer id,@RequestParam Integer partnerId, @RequestParam Integer unitId,@RequestParam Integer brandId){
        boolean response = partnerMetadataManagementService.activateCafeUnitDashboard(id,partnerId,unitId,brandId);
        LOG.info("Value of the::{}",response);
        return response;

    }

    @RequestMapping(method = RequestMethod.POST, value = "item-stock-update", produces = MediaType.APPLICATION_JSON)
    public boolean setUnitAvailability(@RequestBody UnitProductPartnerStatusVO request) throws ChannelPartnerException {
        LOG.info("Request to set product availability status: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setUnitProductStock(request);
    }

    ////////////////////////////// PARTNER RIDER APIS ///////////////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "get-delivery-status", produces = MediaType.APPLICATION_JSON)
    public List<PartnerDeliveryStatus> getDeliveryStatus(@RequestBody String kettleOrderId) throws ChannelPartnerException {
        LOG.info("Request to get rider data for order: " + kettleOrderId);
        return partnerMetadataManagementService.getDeliveryStatus(kettleOrderId);
    }

    ////////////////////////////// PARTNER MENU PRODUCT FILTER //////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "set-partner-product-filter", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerProductFilter(@RequestBody UnitProductPartnerStatusVO request) throws ChannelPartnerException {
        LOG.info("Request to set partner product filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerProductFilter(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-filter", produces = MediaType.APPLICATION_JSON)
    public List<Integer> getPartnerProductFilter(@RequestParam Integer partnerId, @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product filter for partnerId: " + partnerId + " and brand Id: " + brandId);
        return partnerMetadataFetchService.getPartnerProductFilter(partnerId, brandId);
    }

    ////////////////////////////// PARTNER BOGO PRODUCTS //////////////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "set-partner-bogo-product", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerBogoProducts(@RequestBody UnitProductPartnerStatusVO request) throws ChannelPartnerException {
        LOG.info("Request to set partner bogo product : " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerBogoProducts(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-bogo-product", produces = MediaType.APPLICATION_JSON)
    public List<Integer> getPartnerBogoProducts(@RequestBody Integer partnerId) throws ChannelPartnerException {
        LOG.info("Request to get partner bogo product for partnerId: " + partnerId);
        return partnerMetadataManagementService.getPartnerBogoProducts(partnerId);
    }

    ////////////////////////////// PARTNER PRODUCT TAGS //////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "set-partner-product-tags", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerProductTags(@RequestBody PartnerProductTagsVO request) throws ChannelPartnerException {
        LOG.info("Request to set partner product filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerProductTags(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-tags", produces = MediaType.APPLICATION_JSON)
    public List<IdNameGroup> getPartnerProductTags(@RequestBody Integer partnerId) throws ChannelPartnerException {
        LOG.info("Request to get partner product tags for partnerId: " + partnerId);
        return partnerMetadataManagementService.getPartnerProductTags(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-partner-product-tags-mappings", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerProductTagsMappings(@RequestBody PartnerProductTagsMappings request) throws ChannelPartnerException {
        LOG.info("Request to set partner product filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerProductTagsMappings(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-tags-mappings", produces = MediaType.APPLICATION_JSON)
    public List<ProductTagsMappings> getPartnerProductTagsMappings(@RequestParam Integer partnerId, @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product tags for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataFetchService.getPartnerProductTagsMappings(partnerId, brandId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-tags-mappings-detail", produces = MediaType.APPLICATION_JSON)
    public List<BrandProductTagsMappings> getPartnerProductTagsMappingsDetail(@RequestBody Integer partnerId,
                                                                              @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product tags with productId for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataManagementService.getPartnerProductTagsMappingsDetail(partnerId, brandId);
    }


    ////////////////////////////// PARTNER MEAT TAGS //////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "set-meat-tags", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerMeatTags(@RequestBody PartnerProductTagsVO request) throws ChannelPartnerException {
        LOG.info("Request to set partner  meat filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerMeatTags(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-meat-tags", produces = MediaType.APPLICATION_JSON)
    public List<IdName> getPartnerMeatTags(@RequestBody Integer partnerId) throws ChannelPartnerException {
        LOG.info("Request to get partner meat tags for partnerId: " + partnerId);
        return partnerMetadataManagementService.getPartnerMeatTags(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-partner-product-meat-tags-mappings", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerProductMeatTagsMappings(@RequestBody PartnerProductTagsMappings request) throws ChannelPartnerException {
        LOG.info("Request to set partner product meat filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerProductMeatTagsMappings(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-meat-tags-mappings", produces = MediaType.APPLICATION_JSON)
    public List<ProductTagsMappings> getPartnerProductMeatTagsMappings(@RequestParam Integer partnerId,
                                                                       @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product meat tags for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataFetchService.getPartnerProductMeatTagsMappings(partnerId, brandId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-meat-tags-mappings-detail", produces = MediaType.APPLICATION_JSON)
    public List<BrandProductTagsMappings> getPartnerProductMeatTagsMappingsDetail(@RequestBody Integer partnerId,
                                                                                  @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product tags with productId for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataManagementService.getPartnerProductMeatTagsMappingsDetail(partnerId, brandId);
    }
    ////////////////////////////PARTNER ALLERGEN TYPES /////////////////////////////////////////
    @RequestMapping(method = RequestMethod.POST, value = "set-allergen-tags", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerAllergenTags(@RequestBody PartnerProductTagsVO request) throws ChannelPartnerException {
        LOG.info("Request to set partner  allergen filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerAllergenTags(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-allergen-tags", produces = MediaType.APPLICATION_JSON)
    public List<IdName> getPartnerAllergenTags(@RequestBody Integer partnerId) throws ChannelPartnerException {
        LOG.info("Request to get partner allergen tags for partnerId: " + partnerId);
        return partnerMetadataManagementService.getPartnerAllergenTags(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-partner-product-allergen-tags-mappings", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerProductAllergenTagsMappings(@RequestBody PartnerProductTagsMappings request) throws ChannelPartnerException {
        LOG.info("Request to set partner product allergen filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerProductAllergenTagsMapping(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-allergen-tags-mappings", produces = MediaType.APPLICATION_JSON)
    public List<ProductTagsMappings> getPartnerProductAllergenTagsMappings(@RequestParam Integer partnerId,
                                                                           @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product allergen tags for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataManagementService.getPartnerProductAllergenTagsMappings(partnerId, brandId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-allergen-tags-mappings-detail", produces = MediaType.APPLICATION_JSON)
    public List<BrandProductTagsMappings> getPartnerProductAllergenTagsMappingsDetail(@RequestBody Integer partnerId,
                                                                                      @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product tags with productId for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataManagementService.getPartnerProductAllergenTagsMappingsDetail(partnerId, brandId);
    }
    ////////////////////////////PARTNER SERVING INFO APIS /////////////////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "set-serving-info", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerServingInfoTags(@RequestBody PartnerProductTagsVO request) throws ChannelPartnerException {
        LOG.info("Request to set partner  serving info filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerServingInfoTags(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-serving-info", produces = MediaType.APPLICATION_JSON)
    public List<IdName> getPartnerServingInfoTags(@RequestBody Integer partnerId) throws ChannelPartnerException {
        LOG.info("Request to get partner serving info for partnerId: " + partnerId);
        return partnerMetadataManagementService.getPartnerServingInfoTags(partnerId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "set-partner-product-serving-info-tags-mappings", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerProductServingInfoTagsMappings(@RequestBody PartnerProductTagsMappings request) throws ChannelPartnerException {
        LOG.info("Request to set partner product serving info filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerProductServingInfoTagsMapping(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-serving-info-tags-mappings", produces = MediaType.APPLICATION_JSON)
    public List<ProductTagsMappings> getPartnerProductServingInfoTagsMappings(@RequestParam Integer partnerId,
                                                                              @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product serving info tags for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataManagementService.getPartnerProductServingInfoTagsMappings(partnerId, brandId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-serving-info-tags-mappings-detail", produces = MediaType.APPLICATION_JSON)
    public List<BrandProductTagsMappings> getPartnerProductServingInfoTagsMappingsDetail(@RequestBody Integer partnerId,
                                                                                         @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product tags with productId for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataManagementService.getPartnerProductServingInfoTagsMappingsDetail(partnerId, brandId);
    }
    ////////////////////////////PARTNER SERVING SIZE APIS /////////////////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "get-serving-size", produces = MediaType.APPLICATION_JSON)
    public List<IdValueUnit> getPartnerServingSize(@RequestBody Integer partnerId) throws ChannelPartnerException {
        LOG.info("Request to get partner serving size for partner id: " + partnerId);
        return partnerMetadataManagementService.getPartnerServingSize(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-serving-size", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerServingSize(@RequestBody PartnerProductsTagsVU request) throws ChannelPartnerException {
        LOG.info("Request to set partner  serving size filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerServingSize(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-serving-size-mapping", produces = MediaType.APPLICATION_JSON)
    public List<ProductTagsMappingVU> getPartnerProductServingSizeMappings(@RequestParam Integer partnerId, @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product serving size for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataManagementService.getPartnerProductServingSizeMappings(partnerId, brandId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-partner-product-serving-size-mapping", produces = MediaType.APPLICATION_JSON)
    public boolean setPartnerProductServingSizeMappings(@RequestBody PartnerProductTagsMappings request) throws ChannelPartnerException {
        LOG.info("Request to set partner product serving size filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerProductServingSizeMappings(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-serving-size-mapping-detail", produces = MediaType.APPLICATION_JSON)
    public List<BrandProductTagsMappings> getPartnerProductServingSizeMappingsDetail(@RequestParam Integer partnerId, @RequestParam Integer brandId) throws ChannelPartnerException {
        LOG.info("Request to get partner product tags with productId for partnerId: " + partnerId + " and brandId: " + brandId);
        return partnerMetadataManagementService.getPartnerProductServingSizeMappingsDetail(partnerId, brandId);
    }

    ////////////////////////////PARTNER MENU APIS /////////////////////////////////////////

    /*@RequestMapping(method = RequestMethod.POST, value = "menu-sequence-get", produces = MediaType.APPLICATION_JSON, consumes =  MediaType.APPLICATION_JSON)
    public MenuSequence getMenuSequence(@RequestBody UnitMenuAddVO request) throws ChannelPartnerException {
        LOG.info("Request to get partner menu sequence: " + new Gson().toJson(request));
        return partnerMetadataManagementService.getMenuSequence(request);
    }*/

    @RequestMapping(method = RequestMethod.POST, value = "menu-get", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public UnitMenuAddVO getMenu(@RequestBody UnitMenuAddVO request) throws ChannelPartnerException {
        LOG.info("Request to get partner unit menu: " + new Gson().toJson(request));
        return partnerMetadataManagementService.getPartnerUnitMenu(request, PartnerActionEventType.UPDATE_UNIT_MENU);
    }

    @Deprecated
    @RequestMapping(method = RequestMethod.POST, value = "menu-add", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean addMenu(@RequestBody UnitMenuAddVO request) throws ChannelPartnerException {
        LOG.info("Request to add partner unit menu: " + new Gson().toJson(request));
        return partnerMetadataManagementService.addPartnerRegionMenu(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "menu-add-per-unit", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean addMenuPerUnit(@RequestBody UnitMenuAddVO request) throws ChannelPartnerException {
        LOG.info("Request to add partner unit menu for unit: " + new Gson().toJson(request));
        return partnerMetadataManagementService.addPartnerUnitMenu(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-active-menu-for-units", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public List<PartnerUnitMenuDetail> getActiveMenuForPartnerUnit(@RequestBody PartnerUnitListVO request) throws ChannelPartnerException {
        LOG.info("Request to get active menu for partner unit: " + new Gson().toJson(request));
        return partnerMetadataManagementService.getActiveMenuForPartnerUnits(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "menu-refresh", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean refreshMenu(@RequestBody UnitMenuAddVO request) throws ChannelPartnerException {
        LOG.info("Request to refresh partner unit menu: " + new Gson().toJson(request));
        return partnerMetadataManagementService.refreshPartnerUnitMenu(request);
    }

    /*@RequestMapping(method = RequestMethod.POST, value = "single-serve-menu-add-per-unit", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean addSingleServeMenuPerUnit(@RequestBody UnitMenuAddVO request) throws ChannelPartnerException {
        LOG.info("Request to add partner unit menu for unit: " + new Gson().toJson(request));
        return partnerMetadataManagementService.addPartnerUnitSingleServeMenu(request);
    }*/

    //////////////////////////// PARTNER OFFER APIS //////////////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "get-active-offers", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public List<PartnerOfferDetail> getPartnerOffersForUnits(@RequestBody PartnerUnitListVO request) throws ChannelPartnerException {
        LOG.info("Request to get partner offers for partner: " + request.getPartnerId() + " and unit id" + request.getUnitIds());
        return partnerMetadataManagementService.getAllActiveOffersForPartnerAndUnits(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-offer", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public PartnerOfferDetail addPartnerOffer(@RequestBody PartnerUnitListVO request) throws ChannelPartnerException {
        LOG.info("Request to add partner offer");
        return partnerMetadataManagementService.addPartnerOffer(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "deactivate-offer", produces = MediaType.APPLICATION_JSON)
    public Boolean deactivatePartnerOffer(@RequestParam String partnerOfferId) throws ChannelPartnerException {
        LOG.info("Request to remove Zomato treats item");
        return partnerMetadataManagementService.deactivatePartnerOffer(partnerOfferId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "activate-offer", produces = MediaType.APPLICATION_JSON)
    public Boolean activatePartnerOffer(@RequestParam String partnerOfferId) throws ChannelPartnerException {
        LOG.info("Request to remove Zomato treats item");
        return partnerMetadataManagementService.activatePartnerOffer(partnerOfferId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "send-partner-offer", produces = MediaType.APPLICATION_JSON)
    public Boolean activatePartnerOffer(@RequestBody PartnerOfferDetail partnerOfferDetail) throws ChannelPartnerException,
        IllegalArgumentException, IllegalAccessException {
        LOG.info("Request to send Zomato offers.");
        return partnerMetadataManagementService.sendPartnerOffer(partnerOfferDetail);
    }


    ////////////////////// PARTNER QVM APIS //////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "get-qvm-tags", produces = MediaType.APPLICATION_JSON)
    public List<IdName> getPartnerProductQVMTags(@RequestBody Integer partnerId) {
        LOG.info("Request to get QVM tags for partnerId: " + partnerId);
        return partnerMetadataManagementService.getPartnerProductQVMTags(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-qvm-tags", produces = MediaType.APPLICATION_JSON)
    public boolean setPartnerProductQVMTags(@RequestBody PartnerProductTagsVO qvmTags) {
        LOG.info("Request to set QVM tags: " + new Gson().toJson(qvmTags));
        return partnerMetadataManagementService.setPartnerQVMtTags(qvmTags);
    }


    ////////////////////// PARTNER INVENTORY UPDATE APIS /////////////////

    @RequestMapping(method = RequestMethod.POST, value = "inventory/publish", produces = MediaType.APPLICATION_JSON)
    public void publishInventoryToPartner(@RequestBody UnitPartnerStatusVO request) {
        LOG.info("Request to update Inventory");
        partnerMetadataManagementService.publishInventoryToPartner(request);
    }

    ////////////////////// PARTNER LOCALITY MAPPING APIS /////////////////

    @RequestMapping(method = RequestMethod.POST, value = "get-pending-localities", produces = MediaType.APPLICATION_JSON)
    public List<UnitPartnerLocalityMapping> getPendingLocalityMappings() {
        LOG.info("Request to get localities pending for mapping");
        return partnerMetadataManagementService.getPendingLocalityMappings();
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-pending-localities", produces = MediaType.APPLICATION_JSON)
    public boolean updateLocalityMappings(@RequestBody List<UnitPartnerLocalityMapping> mappings) {
        LOG.info("Request to get localities pending for mapping");
        return partnerMetadataManagementService.updateLocalityMappings(mappings);
    }

    ////////////////////// ZOMATO SPECIFIC APIS //////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "get-zomato-treats-item", produces = MediaType.APPLICATION_JSON)
    public IdCodeName getZomatoTreatsItem() {
        LOG.info("Request to get zomato treats item: ");
        return partnerMetadataManagementService.getZomatoTreatsItem();
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-zomato-treats-item", produces = MediaType.APPLICATION_JSON)
    public Boolean setZomatoTreatsItem(@RequestBody Integer productId) throws ChannelPartnerException {
        LOG.info("Request to set zomato treats item: " + productId);
        return partnerMetadataManagementService.setZomatoTreatsItem(productId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "remove-zomato-treats-item", produces = MediaType.APPLICATION_JSON)
    public Boolean removeZomatoTreatsItem() {
        LOG.info("Request to remove Zomato treats item");
        return partnerMetadataManagementService.removeZomatoTreatsItem();
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/logistics-status/get", produces = MediaType.APPLICATION_JSON)
    public ZomatoLogisticsStatusResponse getOutletLogisticsStatus(@RequestBody UnitPartnerBrandKey request) throws ChannelPartnerException {
        LOG.info("Request to get logistics status from Zomato for unit: " + new Gson().toJson(request));
        return partnerMetadataManagementService.getOutletLogisticsStatus(request.getUnitId(), request.getBrandId());
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/logistics-status/update", produces = MediaType.APPLICATION_JSON)
    public ZomatoNotificationResponse updateOutletLogisticsStatus(@RequestBody ZomatoLogisticsChangeRequest request) throws ChannelPartnerException {
        LOG.info("Request to change logistics status on Zomato: " + new Gson().toJson(request));
        return partnerMetadataManagementService.updateOutletLogisticsStatus(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/delivery-status/get", produces = MediaType.APPLICATION_JSON)
    public ZomatoDeliveryStatusResponse getOutletDeliveryStatus(@RequestBody UnitPartnerBrandKey request) throws ChannelPartnerException {
        LOG.info("Request to get delivery status from Zomato for unit: " + new Gson().toJson(request));
        return partnerMetadataManagementService.getOutletDeliveryStatus(request.getUnitId(), request.getBrandId());
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/delivery-status/update", produces = MediaType.APPLICATION_JSON)
    public ZomatoNotificationResponse updateOutletDeliveryStatus(@RequestBody ZomatoDeliveryChangeRequest request) throws ChannelPartnerException {
        LOG.info("Request to change delivery status on Zomato: " + new Gson().toJson(request));
        return partnerMetadataManagementService.updateOutletDeliveryStatus(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/takeaway-status/get", produces = MediaType.APPLICATION_JSON)
    public ZomatoTakeawayStatusResponse getOutletTakeawayStatus(@RequestBody UnitPartnerBrandKey request) throws ChannelPartnerException {
        LOG.info("Request to get delivery status from Zomato for unit: " + new Gson().toJson(request));
        return partnerMetadataManagementService.getOutletTakeawayStatus(request.getUnitId(), request.getBrandId());
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/takeaway-status/update", produces = MediaType.APPLICATION_JSON)
    public boolean updateOutletTakeawayStatus(@RequestBody UnitPartnerStatusVO request) throws ChannelPartnerException {
        LOG.info("Request to change delivery status on Zomato: " + new Gson().toJson(request));
        return partnerMetadataManagementService.updateOutletTakeawayStatus(request);
    }

    //Updating Cafe Delivery Time On Zomato
    @RequestMapping(method = RequestMethod.POST, value = "update-zomato-delivery-timing", produces = MediaType.APPLICATION_JSON)
    public boolean updateCafeDeliveryTimingOnZomato(@RequestBody CafeTimingChangeRequest cafeTimingChangeRequest){
        LOG.info("Changing Cafe Delivery Time on Zomato");
        return partnerMetadataManagementService.updateCafeDeliveryTimeZomato(cafeTimingChangeRequest);
    }

    ////////////////////// SWIGGY SPECIFIC APIS //////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "get-swiggy-super-item", produces = MediaType.APPLICATION_JSON)
    public IdCodeName getSwiggySuperItem() {
        LOG.info("Request to get swiggy super item: ");
        return partnerMetadataManagementService.getSwiggySuperItem();
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-swiggy-super-item", produces = MediaType.APPLICATION_JSON)
    public Boolean setSwiggySuperItem(@RequestBody Integer productId) throws ChannelPartnerException {
        LOG.info("Request to set swiggy super item: " + productId);
        return partnerMetadataManagementService.setSwiggySuperItem(productId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "remove-swiggy-super-item", produces = MediaType.APPLICATION_JSON)
    public Boolean removeSwiggySuperItem() {
        LOG.info("Request to swiggy super treats item");
        return partnerMetadataManagementService.removeSwiggySuperItem();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-swiggy-recommended-products", produces = MediaType.APPLICATION_JSON)
    public List<Integer> getSwiggyRecommendedProducts() {
        LOG.info("Request to get swiggy recommended products");
        return partnerMetadataManagementService.getSwiggyRecommendedProducts();
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-swiggy-recommended-products", produces = MediaType.APPLICATION_JSON)
    public boolean setSwiggySupperProducts(List<Integer> productIds) {
        LOG.info("Request to set swiggy recommended products");
        return partnerMetadataManagementService.setSwiggyRecommendedProducts(productIds);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-swiggy-menu-status", produces = MediaType.APPLICATION_JSON)
    public MenuTrackResponse getSwiggyMenuStatus(@RequestParam Integer unitId, @RequestParam Integer partnerId,
                                                 @RequestParam Integer brandId) {
        LOG.info("Request to get swiggy menu push status ");
        return partnerMetadataManagementService.getSwiggyMenuStatus(unitId, partnerId, brandId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-swiggy-delivery-timing", produces = MediaType.APPLICATION_JSON)
    public boolean updateCafeDeliveryTimingOnSwiggy(@RequestBody CafeTimingChangeRequest cafeTimingChangeRequest){
        LOG.info("Changing Cafe Delivery Time on Swiggy");
        return  partnerMetadataManagementService.updateCafeDeliveryTimeSwiggy(cafeTimingChangeRequest);
    }

    ////////////////////////////// PARTNER MENU PRODUCT ALIASES //////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "set-partner-product-aliases", produces = MediaType.APPLICATION_JSON)
    public Boolean setPartnerProductAliases(@RequestBody PartnerProductAliasVO request) throws ChannelPartnerException {
        LOG.info("Request to set partner product filter: " + new Gson().toJson(request));
        return partnerMetadataManagementService.setPartnerProductAliases(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-product-aliases", produces = MediaType.APPLICATION_JSON)
    public List<ProductAlias> getPartnerProductAliases(@RequestBody Integer partnerId) throws ChannelPartnerException {
        LOG.info("Request to get partner product aliases for partnerId: " + partnerId);
        return partnerMetadataManagementService.getPartnerProductAliases(partnerId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-swiggy-stock-version-status", produces = MediaType.APPLICATION_JSON)
    public boolean getSwiggyStockVersionStatus() {
        return partnerMetadataManagementService.getSwiggyStockVersionStatus();
    }

    @RequestMapping(method = RequestMethod.GET, value = "update-swiggy-stock-version-status", produces = MediaType.APPLICATION_JSON)
    public boolean updateSwiggyStockVersionStatus(@RequestParam boolean status) {
        return partnerMetadataManagementService.updateSwiggyStockVersionStatus(status);
    }

    ///////////////////////////// DYNAMIC MENU APIS /////////////////////////////////////

    @RequestMapping(method = RequestMethod.POST, value = "add-menu-version", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public String addMenuVersionForPartner(@RequestBody UnitMenuAddVO request) throws ChannelPartnerException {
        LOG.info("Request to add partner unit menu for unit: " + new Gson().toJson(request));
        return partnerMetadataManagementService.addMenuVersionForPartner(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-unit-menu-data", produces = MediaType.APPLICATION_JSON)
    public List<UnitMenuVersionData> getUnitMenuData(@RequestParam Integer unitId, @RequestParam Integer kettlePartnerId,
                                                     @RequestParam Integer brandId, @RequestParam String menuType) {
        return partnerMetadataManagementService.getUnitMenuVersionData(unitId, kettlePartnerId, brandId, menuType);
    }

    @RequestMapping(method = RequestMethod.GET, value = "mark-unit-version-default", produces = MediaType.APPLICATION_JSON)
    public List<UnitMenuVersionData> markUnitVersionDefault(@RequestParam Integer unitId, @RequestParam Integer kettlePartnerId,
                                                            @RequestParam Integer brandId, @RequestParam String version,
                                                            @RequestParam String status, @RequestParam String menuType) {
        return partnerMetadataManagementService.markUnitMenuVersionDef(unitId, kettlePartnerId, brandId, version, status, menuType);
    }

    @RequestMapping(method = RequestMethod.GET, value = "show-version-menu", produces = MediaType.APPLICATION_JSON)
    public PartnerUnitMenuDetail showVersionedMenu(@RequestParam Integer unitId, @RequestParam Integer kettlePartnerId,
                                                   @RequestParam Integer brandId, @RequestParam String version, @RequestParam String menuType) {
        return partnerMetadataManagementService.showVersionMenu(unitId, kettlePartnerId, brandId, version, menuType);
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-menu-to-units", produces = MediaType.APPLICATION_JSON)
    public boolean pushMenuToUnits(@RequestBody UnitMenuAddVO request) throws ChannelPartnerException {
        return partnerMetadataManagementService.pushMenuToUnits(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-menu-auto-flag", produces = MediaType.APPLICATION_JSON)
    public List<CafeMenuAutoPush> menuAutoPush(@RequestBody List<CafeMenuAutoPush> data) {
        return partnerMetadataManagementService.menuAutoPush(data);
    }

    @RequestMapping(method = RequestMethod.GET, value = "menu-auto-push-data", produces = MediaType.APPLICATION_JSON)
    public List<CafeMenuAutoPush> showMenuData(@RequestParam Integer brandId) {
        LOG.info("Request for menu auto push mapping with brandID is " + brandId);
        return partnerMetadataManagementService.showMenuData(brandId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-menu-auto-push-history", produces = MediaType.APPLICATION_JSON)
    public List<CafeMenuAutoPushLog> menuAutoPushHistory(@RequestParam Integer brandId, @RequestParam Integer unitId) {
        LOG.info("Request for menu auto Push History for brand is " + brandId + " and unitID " + unitId);
        return partnerMetadataManagementService.menuAutoPushHistory(brandId, unitId);
    }

    // MenuAuditHistory API
    @RequestMapping(method = RequestMethod.GET, value = "menu-audit-history", produces = MediaType.APPLICATION_JSON)
    public List<PartnerMenuAuditHistory> showMenuAuditHistory(@RequestParam Integer unitId, @RequestParam Integer partnerId,
                                                              @RequestParam Integer brandId, @RequestParam String menuType) {
        return partnerMenuService.getMenuAuditHistory(unitId, partnerId, brandId, menuType);
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////// Desi Chai Custom Profiles APIS ////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////////////


    @RequestMapping(method = RequestMethod.GET, value = "get-dc-profiles", produces = MediaType.APPLICATION_JSON)
    public List<DesiChaiCustomProfiles> getDesiChaiCustomProfile() {
        LOG.info("Request to get desi chai custom profiles");
        return partnerMetadataManagementService.getDesiChaiCustomProfiles();
    }


    @RequestMapping(method = RequestMethod.POST, value = "add-dc-profile", produces = MediaType.APPLICATION_JSON)
    public DesiChaiCustomProfiles addDesiChaiCustomProfile(@RequestBody DesiChaiCustomProfiles profile) throws ChannelPartnerException {
        LOG.info("Request to add desi chai custom profile {}", profile != null ? new Gson().toJson(profile) : "");
        return partnerMetadataManagementService.addDesiChaiCustomProfile(profile);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-dc-profile-mappings", produces = MediaType.APPLICATION_JSON)
    public List<DesiChaiCustomProfileMappings> getDesiChaiCustomProfileMappings(@RequestBody PartnerUnitListVO request) {
        LOG.info("Request to get desi chai custom profile mappings for request {}", new Gson().toJson(request));
        return partnerMetadataManagementService.getDesiChaiCustomProfileMappings(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-dc-profile-mappings", produces = MediaType.APPLICATION_JSON)
    public List<DesiChaiCustomProfileMappings> addDesiChaiCustomProfileMappings(@RequestBody List<DesiChaiCustomProfileMappings> mappings) {
        LOG.info("Request to add desi chai custom profile mappings {}", mappings != null ? new Gson().toJson(mappings) : "");
        return partnerMetadataManagementService.addDesiChaiCustomProfileMappings(mappings);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-dc-profile-mapping-status", produces = MediaType.APPLICATION_JSON)
    public DesiChaiCustomProfileMappings updateDesiChaiCustomProfileMappingStatus(@RequestBody DesiChaiCustomProfileMappings mapping)
        throws ChannelPartnerException {
        LOG.info("Request to update desi chai custom profile mapping status {}", mapping != null ? new Gson().toJson(mapping) : "");
        return partnerMetadataManagementService.updateDesiChaiCustomProfileMappingStatus(mapping);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-dc-profiles-for-unit", produces = MediaType.APPLICATION_JSON)
    public List<DesiChaiCustomProfiles> getDesiChaiProfilesForUnit(@RequestBody Integer unitId,@RequestParam Integer partnerId)
        throws ChannelPartnerException {
        LOG.info("Request to get desi chai custom profiles for unit {}", unitId);
        return partnerMetadataManagementService.getDesiChaiCustomProfilesForUnit(unitId,partnerId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "activate-cafe-for-unit/{token:.+}", produces = MediaType.APPLICATION_JSON)
    public ResponseEntity<String> activateCafeOnChannelPartner(@PathVariable("token") String token, @RequestParam(required = false) Boolean forceUpdate) {
        CafeActivationTokenInfo jwtToken = new CafeActivationTokenInfo();
        jwtService.parseToken(jwtToken, token);
        if (forceUpdate == null) {
            forceUpdate = false;
        }
        partnerMetadataManagementService.setUnitAvailability(jwtToken.getRequestId(), jwtToken.getPartnerId(), forceUpdate);
        return new ResponseEntity<>("Successfully Submitted Request to activate cafe",HttpStatus.OK);
    }


    ///////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////// DOTD ////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////////////


    @RequestMapping(method = RequestMethod.GET, value = "get-dotd-products-list", produces = MediaType.APPLICATION_JSON)
    public DealOfTheDay getDotdProducts(@RequestParam Integer partnerId){
        LOG.info("Request to get DOTD Products for partner id {}",partnerId);
        return partnerMetadataManagementService.getDotdProducts(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST,value = "set-dotd-products-list",produces = MediaType.APPLICATION_JSON ,consumes = MediaType.APPLICATION_JSON)
    public Boolean setProductsForDOTD(@RequestBody DealOfTheDayRequest dealOfTheDayRequest){
        LOG.info("Set DOTD Products for partner id {}",dealOfTheDayRequest.getKettlePartnerId());
        return partnerMetadataManagementService.setProductsForDOTD(dealOfTheDayRequest);
    }


}
