package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerLocalityDetail;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.UnitPartnerLocalityMapping;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitPartnerLocalityMappingDao extends MongoRepository<UnitPartnerLocalityMapping, String> {

    public UnitPartnerLocalityMapping getUnitPartnerLocalityMappingByPartnerLocalityDetail(PartnerLocalityDetail partnerLocalityDetail);

    @Query("{'pending' : true}")
    public List<UnitPartnerLocalityMapping> getPendingLocalityMappings();
}
