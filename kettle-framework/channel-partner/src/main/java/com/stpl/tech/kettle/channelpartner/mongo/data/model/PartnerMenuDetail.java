package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "PartnerMenuDetail")
public class PartnerMenuDetail {

    private String id;
    private String partnerName;
    private Integer partnerId;
    private String region;
    private Object menuData;
    private Boolean active = false;
    private Date updateTime;
    private String updateTimeIST;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Object getMenuData() {
        return menuData;
    }

    public void setMenuData(Object menuData) {
        this.menuData = menuData;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateTimeIST() {
        return updateTimeIST;
    }

    public void setUpdateTimeIST(String updateTimeIST) {
        this.updateTimeIST = updateTimeIST;
    }
}
