package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoMenu;

import java.util.Date;

public class UnitMenuVersionData {

    private Integer unitId;
    private Integer kettlePartnerId;
    private Integer brandId;
    private String version;
    private ZomatoMenu menuRequest;
    private String status;
    private MenuType menuType;
    private Integer menuSequenceId;
    private String menuSequenceName;
    private Date addTime;
    private String addTimeIST;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getKettlePartnerId() {
        return kettlePartnerId;
    }

    public void setKettlePartnerId(Integer kettlePartnerId) {
        this.kettlePartnerId = kettlePartnerId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public ZomatoMenu getMenuRequest() {
        return menuRequest;
    }

    public void setMenuRequest(ZomatoMenu menuRequest) {
        this.menuRequest = menuRequest;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public MenuType getMenuType() {
        return menuType;
    }

    public void setMenuType(MenuType menuType) {
        this.menuType = menuType;
    }

    public Integer getMenuSequenceId() {
        return menuSequenceId;
    }

    public void setMenuSequenceId(Integer menuSequenceId) {
        this.menuSequenceId = menuSequenceId;
    }

    public String getMenuSequenceName() {
        return menuSequenceName;
    }

    public void setMenuSequenceName(String menuSequenceName) {
        this.menuSequenceName = menuSequenceName;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public String getAddTimeIST() {
        return addTimeIST;
    }

    public void setAddTimeIST(String addTimeIST) {
        this.addTimeIST = addTimeIST;
    }
}
