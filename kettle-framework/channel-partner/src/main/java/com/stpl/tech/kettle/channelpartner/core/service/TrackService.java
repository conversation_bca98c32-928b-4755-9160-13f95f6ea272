package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.domain.model.PartnerActionCode;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderActions;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatusLog;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerMenuStatus;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;

import java.util.Date;
import java.util.List;

public interface TrackService {

    PartnerOrderDetail trackPartnerOrder(String orderId, String partnerId, String partnerName, String partnerCode, Object order,
                                         String linkedOrderId, boolean isManual, String partnerOrderVersion);

    PartnerOrderStatusLog generatePartnerOrderStatusLog(PartnerOrderStatus from, PartnerOrderStatus to, boolean isManual, Date time);

    public PartnerOrderActions generatePartnerOrderAction(PartnerActionCode partnerActionCode, String actionDetail, Date time);

    void addPartnerOrderToCache(String partnerOrderId, String partnerName, Integer unitId);

    List<PartnerOrderDetail> getPartnerOrderByPartnerOrderId(String orderId);

    PartnerOrderDetail updatePartnerOrder(PartnerOrderDetail partnerOrderDetail);

    PartnerMenuStatus updatePartnerMenuStatus(PartnerMenuStatus partnerMenuStatus);

    PartnerOrderDetail getPartnerData(String orderId);
}
