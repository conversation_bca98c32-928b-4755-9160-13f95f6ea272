package com.stpl.tech.kettle.channelpartner.domain.model;

import java.math.BigDecimal;
import java.util.Objects;

public class TaxDetailKey {

    private String type;
    private String code;
    private BigDecimal percentage;

    public TaxDetailKey() {
    }

    public TaxDetailKey(String type, String code, BigDecimal percentage) {
        this.type = type;
        this.code = code;
        this.percentage = percentage;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public BigDecimal getPercentage() {
        return percentage;
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TaxDetailKey that = (TaxDetailKey) o;
        return Objects.equals(type, that.type) &&
                Objects.equals(code, that.code) &&
                percentage.compareTo(that.percentage) == 0;
    }

    @Override
    public int hashCode() {

        return Objects.hash(type, code, percentage);
    }
}
