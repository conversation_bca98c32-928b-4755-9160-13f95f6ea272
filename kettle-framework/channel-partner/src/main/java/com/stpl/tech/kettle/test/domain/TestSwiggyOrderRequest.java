package com.stpl.tech.kettle.test.domain;

import com.stpl.tech.kettle.test.mongo.model.PartnerOrderDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TestSwiggyOrderRequest implements Serializable {

    private static final long serialVersionUID = -3849991372858963040L;
    List<PartnerOrderDetail> swiggyOrderRequestList;
}
