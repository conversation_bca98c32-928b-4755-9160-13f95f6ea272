package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "outlet_id",
        "order_id",
        "date"
})
public class ZomatoRatingRequest {
    @JsonProperty("outlet_id")
    private String outletId;

    @JsonProperty("order_id")
    private String orderId;

    @JsonProperty("date")
    private String date;

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("order_id")
    public String getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ZomatoRatingRequest)) return false;
        ZomatoRatingRequest that = (ZomatoRatingRequest) o;
        return Objects.equals(getOutletId(), that.getOutletId()) && Objects.equals(getOrderId(), that.getOrderId()) && Objects.equals(getDate(), that.getDate());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getOutletId(), getOrderId(), getDate());
    }

    @Override
    public String toString() {
        return "ZomatoRatingRequest{" +
                "outletId='" + outletId + '\'' +
                ", orderId='" + orderId + '\'' +
                ", date='" + date + '\'' +
                '}';
    }
}
