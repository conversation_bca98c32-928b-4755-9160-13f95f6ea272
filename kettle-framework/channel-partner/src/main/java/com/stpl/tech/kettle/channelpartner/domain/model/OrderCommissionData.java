package com.stpl.tech.kettle.channelpartner.domain.model;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;


@Getter
@Setter
@ToString
public class OrderCommissionData {
    private String partnerOrderId;
    private int kettleOrderId;
    private int unitId;
    private BigDecimal subTotal;
    private BigDecimal packagingCharges;
    private BigDecimal discount;
    private BigDecimal netAmount;
    private BigDecimal aov;
    private Double commissionRate;
    private Double extraCommissionRate;
    private BigDecimal commissionAmount;
    private Double gstRate;
    private BigDecimal finalCommissionAmount;

    private  String partnerName;

    private int brand;
    private String brandName;
    private long unitAge;
    private Date unitLiveDate;
  private String swiggyCloudKitchen;

    private String ruleParams;
    private Date orderDate;
    private String status;
    private BigDecimal restaurantGrossBill;
}
