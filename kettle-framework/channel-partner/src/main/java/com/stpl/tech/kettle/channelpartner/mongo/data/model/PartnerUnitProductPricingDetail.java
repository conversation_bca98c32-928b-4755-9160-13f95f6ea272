package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Document
@XmlRootElement(name = "PartnerUnitProductPricingDetail")
public class PartnerUnitProductPricingDetail {

    private String id;
    private String partnerName;
    private Integer partnerId;
    private Integer unitId;
    private Boolean active = false;
    private Date addTime;
    private String addTimeIST;
    //Map<productId, Map<dimension, price>>
    private Map<Integer, Map<String, BigDecimal>> pricing;
    private Integer employeeId;
    private String employeeName;
    private Integer brandId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Map<Integer, Map<String, BigDecimal>> getPricing() {
        return pricing;
    }

    public String getAddTimeIST() {
        return addTimeIST;
    }

    public void setAddTimeIST(String addTimeIST) {
        this.addTimeIST = addTimeIST;
    }

    public void setPricing(Map<Integer, Map<String, BigDecimal>> pricing) {
        this.pricing = pricing;
    }
}
