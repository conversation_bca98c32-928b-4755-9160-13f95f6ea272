package com.stpl.tech.kettle.channelpartner.core.service;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerCafeStatusHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;

import java.util.List;

public interface PartnerService {

    List<PartnerDetail> getPartners();

    List<PartnerDetail> getAllPartners();

    List<PartnerDetail> getActivePartners();

    PartnerDetail addPartner(PartnerDetail partnerDetail);

    PartnerDetail activatePartner(String partnerId);

    PartnerDetail deactivatePartner(String partnerId);

    List<PartnerCafeStatusHistory> getPartnerCafeStatusHistory(Integer brandId, Integer unitId, String partnerName);
}
