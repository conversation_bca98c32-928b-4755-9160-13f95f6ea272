package com.stpl.tech.kettle.channelpartner.core.converters;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.util.ChannelPartnerUtils;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderErrorCode;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxDetailKey;
import com.stpl.tech.kettle.channelpartner.domain.model.TaxPayingEntity;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderChargesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCompSelectionEntityV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCompSelectionV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCompositionPropV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderCompositionV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDiscountV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishChargesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishDiscountV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderDishesV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderModifierGroupsV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderRequestV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderTaxDetailsV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderType;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderVariantsV3;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDiscountData;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.readonly.domain.model.TaxDataVO;
import com.stpl.tech.master.recipe.model.CompositeIngredientData;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.OptionData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

public class ZomatoConvertersV3 {

    private static final Logger LOG = LoggerFactory.getLogger(ZomatoConvertersV3.class);

    private static final String REGULAR_SUGAR = "Regular Sugar";

    public static Order convertOrderV3(ZomatoOrderRequestV3 request, Customer customer, Address address,
                                       MasterDataCache masterDataCache, UnitPartnerBrandMappingData data, EnvironmentProperties environmentProperties) {
        Date currentTime = ChannelPartnerUtils.getCurrentTimestamp();
        Order order = new Order();
        order.setGenerateOrderId(ChannelPartnerUtils.generateRandomOrderId());
        order.setCustomerId(customer.getId());
        order.setEmployeeId(ChannelPartnerServiceConstants.SYSTEM_EMPLOYEE_ID);
        order.setPointsRedeemed(0);
        if (ZomatoOrderType.TAKEAWAY.equals(request.getOrderType())) {
            order.setSource("TAKE_AWAY");
        } else {
            order.setSource("COD");
        }
        order.setSourceId(request.getOrderId().toString());
        order.setHasParcel(true);
        order.setStatus(OrderStatus.CREATED);
        order.setSettlementType(SettlementType.DEBIT);
        order.setUnitId(data.getUnitId());
        order.setUnitName(masterDataCache.getUnit(data.getUnitId()).getName());
        order.setTerminalId(1);
        order.setBillStartTime(currentTime);
        order.setBillCreationTime(currentTime);
        order.setBillCreationSeconds(0);
        order.setBillingServerTime(currentTime);
        order.setChannelPartner(3);
        if(environmentProperties.isDefaultZomatoCustomerFlow()){
            order.setDeliveryPartner(5);
        }else {
            if (!ZomatoOrderType.TAKEAWAY.equals(request.getOrderType()) && request.getEnableDelivery() == 1) {
                order.setDeliveryPartner(8); // PARTNER ID 8 FOR CHAAYOS DELIVERY
            } else {
                order.setDeliveryPartner(5); // for PICKUP
            }
        }
        if (request.getCutleryInstructions() != null) {
            order.setOrderRemark(request.getCutleryInstructions() + " ");
        }
        if (request.getOrderInstructions() != null) {
            String finalOrderRemark = (order.getOrderRemark() != null ? order.getOrderRemark() : "") + request.getOrderInstructions();
            order.setOrderRemark(finalOrderRemark);
        }
        if (address != null) {
            order.setDeliveryAddress(address.getId());
        } else {
            order.setDeliveryAddress(0);
        }
//        String name = customer.getFirstName();
        String name= (request.getCustomerDetails()!=null && request.getCustomerDetails().getName() !=null )? request.getCustomerDetails().getName():customer.getFirstName();
        if (name != null) {
            name = name.replaceAll("[^a-zA-Z0-9 ]", "");
            if (name.length() > 20) {
                name = name.substring(0, 20);
            }
        }
        order.setCustomerName(name);
        order.setContainsSignupOffer(false);
        order.setNewCustomer(false);
        order.setOrderType("order");
        order.setBrandId(data.getBrandId());
        order.setOrderDiscountData(getOrderDiscountData(request));
        // order.setOrderId();
        // order.setExternalOrderId(externalOrderId);
        // order.setOptionResultEventId();
        // order.setUnitOrderId();
        // order.setCampaignId();
        // order.setApplication();
        // order.setEnquiryItems();
        // order.setTableNumber();
        // setTransactionDetail(order, request);
        // order.setPrintCount(1);
        // order.setSubscriptionDetail();
        // order.setOfferCode();
        // order.setReprints();
        // order.setCancellationDetails();
        // order.setEmployeeMeal();
        // order.setEmployeeIdForMeal();
        // order.setTempCode();
        // order.setMetadataList();
        // order.setPendingCash();
        // order.setTokenNumber();
        // order.setLinkedOrderId();
        // order.setPaymentDetailId();
        // order.setAwardLoyalty();
        // order.setBillBookNo();
        order.setPartnerCustomerId(request.getOrderId()+"_"+request.getCustomerDetails().getPhoneNumber());
        return order;
    }

    private static List<OrderDiscountData> getOrderDiscountData(ZomatoOrderRequestV3 request) {
        List<OrderDiscountData> orderDiscountData = new ArrayList<OrderDiscountData>();
        for (ZomatoOrderDiscountV3 zomatoOrderDiscount : request.getOrderDiscounts()) {
            if (zomatoOrderDiscount.getIsZomatoDiscount() == 0) {
                OrderDiscountData orderDiscount = new OrderDiscountData();
                orderDiscount.setPartnerName("ZOMATO");
                orderDiscount.setDiscountName(zomatoOrderDiscount.getDiscountName());
                orderDiscount.setDiscountCategory(zomatoOrderDiscount.getDiscountCategory());
                orderDiscount.setDiscountType(zomatoOrderDiscount.getDiscountType());
                orderDiscount.setDiscountValue(zomatoOrderDiscount.getDiscountValue());
                orderDiscount.setDiscountAmount(zomatoOrderDiscount.getDiscountAmount());
                orderDiscount.setDiscountAppliedOn(zomatoOrderDiscount.getDiscountAppliedOn());
                orderDiscount.setDiscountIsTaxed(zomatoOrderDiscount.getDiscountIsTaxed());
                orderDiscount.setIsPartnerDiscount(zomatoOrderDiscount.getIsZomatoDiscount() == 1 ? "YES" : "NO");
                if (zomatoOrderDiscount.getVoucherCode().equalsIgnoreCase("")) {
                    orderDiscount.setVoucherCode("PARTNER");
                } else {
                    orderDiscount.setVoucherCode(zomatoOrderDiscount.getVoucherCode());
                }
                orderDiscountData.add(orderDiscount);
            }
        }
        return orderDiscountData;
    }

    public static void setTransactionDetailV3(Order order, ZomatoOrderRequestV3 request, MasterDataCache masterDataCache) {
        TransactionDetail transactionDetail = new TransactionDetail();
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal taxable = BigDecimal.ZERO;
        for (OrderItem item : order.getOrders()) {
            total = ChannelPartnerUtils.add(total, item.getPrice().multiply(BigDecimal.valueOf(item.getQuantity())));
            taxable = ChannelPartnerUtils.add(taxable, item.getAmount());
        }
        transactionDetail.setTotalAmount(total);
        transactionDetail.setTaxableAmount(taxable);
        BigDecimal discountValue = BigDecimal.ZERO; //totalDiscountValue
        BigDecimal discountPercent = BigDecimal.ZERO;
        BigDecimal discountPromotional = BigDecimal.ZERO;
        DiscountDetail discountDetail = new DiscountDetail();

        //        AGGREGATING DISOUNTS AT ITEM LEVEL AND SETTING THE FINAL TOTAL DISCOUNT VALUE TO TRANSACTIONALDETAIL
        LOG.info("Aggregating Discpunt Vales from orderItem Level to get finalDiscountValue for order with orderId :{}", order.getOrderId());
        for (OrderItem orderItem : order.getOrders()) {
            BigDecimal orderItemDiscountValue = BigDecimal.ZERO;
            BigDecimal percentage = BigDecimal.ZERO;
            BigDecimal promotionalOffer = BigDecimal.ZERO;
            if (Objects.nonNull(orderItem.getDiscountDetail())) {
                orderItemDiscountValue = orderItem.getDiscountDetail().getDiscount().getValue();
                percentage = orderItem.getDiscountDetail().getDiscount().getPercentage();
                promotionalOffer = orderItem.getDiscountDetail().getPromotionalOffer();
            }
//           BigDecimal percentage = orderItemDiscountValue.divide(orderItem.getTotalAmount(), 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            discountValue = discountValue.add(orderItemDiscountValue);
            discountPercent = discountPercent.add(percentage);
            discountPromotional = discountPromotional.add(promotionalOffer);
        }
//        PercentageDetail percentageDetail = getDiscountValuesV3(request);
//        discountValue = percentageDetail.getValue();
//        discountPercent = percentageDetail.getPercentage();
        discountDetail.setDiscountReason(getDiscountNameV3(request).equalsIgnoreCase("") ? "ZOMATO" : getDiscountNameV3(request));
        discountDetail.setDiscountCode(2004);
        discountDetail.setDiscount(new PercentageDetail());
        discountDetail.getDiscount().setValue(discountValue);
        discountDetail.getDiscount().setPercentage(discountPercent);
        discountDetail.setPromotionalOffer(discountPromotional);
        discountDetail.setTotalDiscount(
                ChannelPartnerUtils.add(discountDetail.getDiscount().getValue(), discountDetail.getPromotionalOffer()));
        transactionDetail.setDiscountDetail(discountDetail);

        LOG.info("Aggregating Tax Vales from orderItem Level to get finalTaxValue for order with orderId :{}", order.getOrderId());
        Map<TaxDetailKey, TaxDetail> taxMap = new HashMap<>();
        BigDecimal totalTax = AbstractConverters.aggregateTaxesFromOrderItems(order.getOrders(), taxMap);
        transactionDetail.getTaxes().addAll(taxMap.values());
        transactionDetail.setTax(totalTax);

        Map<TaxDetailKey, TaxDetail> collectionTaxMap = new HashMap<>();
        BigDecimal collectionTotalTax = AbstractConverters.aggregateCollectionTaxesFromOrderItems(order.getOrders(), collectionTaxMap);
        transactionDetail.setCollectionTax(collectionTotalTax);
        BigDecimal collectionTotalAmount = ChannelPartnerUtils.add(transactionDetail.getTaxableAmount(), transactionDetail.getCollectionTax());
        BigDecimal paidAmount = ChannelPartnerUtils.add(transactionDetail.getTaxableAmount(),
                transactionDetail.getTax());
        transactionDetail.setPaidAmount(paidAmount.setScale(0, RoundingMode.HALF_UP));
        transactionDetail.setCollectionAmount(collectionTotalAmount.setScale(5, RoundingMode.HALF_UP));
        transactionDetail.setRoundOffValue(ChannelPartnerUtils.subtract(transactionDetail.getPaidAmount(), paidAmount));
        AbstractConverters.calculateSaving(transactionDetail, order);
        order.setTransactionDetail(transactionDetail);

        AbstractConverters.addSettlementToOrder(order, request, masterDataCache);
    }

    private static PercentageDetail getDiscountValuesV3(ZomatoOrderRequestV3 request) {
        BigDecimal totalDiscount = BigDecimal.ZERO;
        for (ZomatoOrderDiscountV3 zomatoOrderDiscount : request.getOrderDiscounts()) {
            if (zomatoOrderDiscount.getIsZomatoDiscount() == 0) {
                totalDiscount = totalDiscount.add(BigDecimal.valueOf(zomatoOrderDiscount.getDiscountAmount()));
            }
        }
        for (ZomatoOrderDishesV3 item : request.getDishes()) {
            for (ZomatoOrderDishDiscountV3 zomatoItemDiscount : item.getDishDiscounts()) {
                totalDiscount = totalDiscount.add(BigDecimal.valueOf(zomatoItemDiscount.getDiscountValue()));
            }
        }
        BigDecimal subTotal = BigDecimal.ZERO;
        for (ZomatoOrderDishesV3 item : request.getDishes()) {
            BigDecimal price = BigDecimal.valueOf(item.getComposition().getUnitCost());
            subTotal = ChannelPartnerUtils.add(subTotal, ChannelPartnerUtils.multiply(price, BigDecimal.valueOf(item.getQuantity())));
        }
        if (request.getOrderAdditionalCharges() != null) {
            for (ZomatoOrderChargesV3 zomatoChargeDetails : request.getOrderAdditionalCharges()) {
                subTotal = ChannelPartnerUtils.add(subTotal, BigDecimal.valueOf(zomatoChargeDetails.getChargeAmount()))
                        .setScale(10, RoundingMode.HALF_UP);
            }
        }
        for (ZomatoOrderDishesV3 item : request.getDishes()) {
            for (ZomatoOrderDishChargesV3 zomatoOrderDishChargesV3 : item.getItemCharges()) {
                subTotal = ChannelPartnerUtils.add(subTotal, BigDecimal.valueOf(zomatoOrderDishChargesV3.getChargeAmount()))
                        .setScale(10, RoundingMode.HALF_UP);
            }
        }
        BigDecimal percentage = BigDecimal.ZERO;
        if (totalDiscount.compareTo(BigDecimal.ZERO) > 0) {
            percentage = totalDiscount.divide(subTotal, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(percentage);
        percentageDetail.setValue(totalDiscount);
        return percentageDetail;
    }

    public static void addItemsToOrderV3(Order order, ZomatoOrderRequestV3 request, MasterDataCache masterDataCache, PartnerOrderDetail partnerOrderDetail,
                                         Map<String, TaxDataVO> taxMap, Map<Integer, Product> products, EnvironmentProperties environmentProperties,
                                         int pricingUnitId, Map<Integer, Map<String, BigDecimal>> pricingMap, Map<Integer, Product> cafeProducts) {
        boolean isInterState = false;
        LOG.info("Enter into addItemsToOrderV3 Method");
        updateDesiChaiProductIdsV3(request);
        PercentageDetail percentageDetail = getDiscountValuesV3(request);
        BigDecimal discountPercent = getDiscountValuesV3(request).getPercentage();
        String discountName = "";
        if (discountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountName = getDiscountNameV3(request);
        }
        List<ZomatoOrderDishesV3> recommendedOrderDishes = new ArrayList<>();
        for (ZomatoOrderDishesV3 item : request.getDishes()) {
            Integer productId = null;
            if (item.getDishType().equalsIgnoreCase("combo")) {
                productId = Integer.parseInt(item.getDishId());
            } else {
                productId = Integer.parseInt(item.getComposition().getCatalogueId().replaceAll("(_[A-Za-z0-9]*)", ""));
            }
            if (products.containsKey(productId)) {
                Product product = products.get(productId);
                List<OrderItem> paidAddonItems = new ArrayList<>();
                LOG.info("Adding Product " + product.getName() + " to Order");
                OrderItem orderItem = convertOrderItemV3(product, item, taxMap, products,
                    discountPercent, null, BigDecimal.ZERO, 1, false, masterDataCache, partnerOrderDetail.getBrandId(),
                        recommendedOrderDishes, discountName, pricingMap, percentageDetail, paidAddonItems,false, cafeProducts, request.getOrderInstructions(),environmentProperties);
                order.getOrders().add(orderItem);
                order.getOrders().addAll(paidAddonItems);
            } else {
                PartnerOrderError partnerOrderError = new PartnerOrderError();
                partnerOrderError.setErrorCode(PartnerOrderErrorCode.PRODUCT_MISMATCH);
                if (item.getComposition().getCatalogueId() != null) {
                    partnerOrderError.setErrorDescription(
                            "Product " + item.getComposition().getCatalogueName() + " with id " + item.getComposition().getCatalogueId() + " not found.");
                } else {
                    partnerOrderError.setErrorDescription(
                            "Product " + item.getComposition().getComboName() + " with id " + item.getComposition().getComboId() + " not found.");
                }
                partnerOrderDetail.getOrderErrors().add(partnerOrderError);
            }
        }
        LOG.info("All Products are added to Order Detail");
        if (!recommendedOrderDishes.isEmpty()) {
            createRecommendedProductsDataV3(order, recommendedOrderDishes, products, partnerOrderDetail, taxMap, discountPercent,
                    masterDataCache, discountName, pricingMap, percentageDetail, cafeProducts, request.getOrderInstructions(),environmentProperties);
        }
        /*BigDecimal cartValueSourceTax = BigDecimal.ZERO;
        BigDecimal cartValueDefaultTax = BigDecimal.ZERO;
        for (OrderItem item : order.getOrders()) {
            if (Boolean.TRUE.equals(item.getTaxDeductedByPartner())) {
                cartValueSourceTax = cartValueSourceTax.add(item.getPrice().multiply(new BigDecimal(item.getQuantity())))
                        .subtract(item.getDiscountDetail().getTotalDiscount());
            } else {
                cartValueDefaultTax = cartValueDefaultTax.add(item.getPrice().multiply(new BigDecimal(item.getQuantity())))
                        .subtract(item.getDiscountDetail().getTotalDiscount());
            }
        }
        boolean isPackagingChargesSet = false;
        if (cartValueSourceTax.compareTo(BigDecimal.ZERO) > 0) {
            isPackagingChargesSet = true;
            addChargeItemsToOrder(order, request, masterDataCache, isInterState, cartValueSourceTax, taxMap, products,
                    pricingUnitId, discountName, discountPercent, TaxPayingEntity.PARTNER.getZomato());
        }
        if (cartValueDefaultTax.compareTo(BigDecimal.ZERO) > 0 && !isPackagingChargesSet) {
            addChargeItemsToOrder(order, request, masterDataCache, isInterState, cartValueDefaultTax, taxMap, products,
                    pricingUnitId, discountName, discountPercent, TaxPayingEntity.SELF.getZomato());
        }*/
        BigDecimal sourcePackaging = BigDecimal.ZERO;
        BigDecimal defaultPackaging = BigDecimal.ZERO;
        Map<String, BigDecimal> sourcePackagingMap = new HashMap<>();
        Map<String, BigDecimal> defaultPackagingMap = new HashMap<>();
        Map<String, ZomatoOrderTaxDetailsV3> sourceTaxMap = new HashMap<>();
        Map<String, ZomatoOrderTaxDetailsV3> defaultTaxMap = new HashMap<>();
        for (ZomatoOrderDishesV3 dish : request.getDishes()) {
            for(ZomatoOrderDishChargesV3 charge : dish.getItemCharges()) {
                if(charge.getChargeId().contains(ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID.toString())) {
                    if(!charge.getChargeTaxes().isEmpty() && charge.getChargeTaxes().get(0).getType().equals(TaxPayingEntity.PARTNER.getZomato())) {
                        sourcePackaging = calculatePackagingAmountAndTaxes(sourcePackaging, sourcePackagingMap, sourceTaxMap, charge);
                    } else {
                        defaultPackaging = calculatePackagingAmountAndTaxes(defaultPackaging, defaultPackagingMap, defaultTaxMap, charge);
                    }
                }
            }
        }
        if(sourcePackaging.compareTo(BigDecimal.ZERO) > 0) {
            addChargeItemsToOrder(order, masterDataCache, isInterState, taxMap, products,
                    pricingUnitId, discountName, BigDecimal.ZERO, true, sourceTaxMap, sourcePackaging, sourcePackagingMap, TaxPayingEntity.PARTNER.getZomato());
        }
        if(defaultPackaging.compareTo(BigDecimal.ZERO) > 0) {
            addChargeItemsToOrder(order, masterDataCache, isInterState, taxMap, products,
                    pricingUnitId, discountName, BigDecimal.ZERO, false, defaultTaxMap, defaultPackaging, defaultPackagingMap, TaxPayingEntity.SELF.getZomato());
        }

        addCondimentsForGheeAndTurmeric(order, masterDataCache, products, environmentProperties, pricingUnitId);
        //setOrderItemInstructions(order,null,null,request );
        LOG.info("Exiting addItemsToOrderV3 Method");
    }

    private static BigDecimal calculatePackagingAmountAndTaxes(BigDecimal packaging, Map<String, BigDecimal> packagingMap, Map<String, ZomatoOrderTaxDetailsV3> taxMap, ZomatoOrderDishChargesV3 charge) {
        packaging = ChannelPartnerUtils.add(packaging, BigDecimal.valueOf(charge.getChargeAmount()));
        for(ZomatoOrderTaxDetailsV3 tax : charge.getChargeTaxes()) {
            if(!taxMap.containsKey(tax.getSlug())) {
                ZomatoOrderTaxDetailsV3 taxCopy = (ZomatoOrderTaxDetailsV3) ChannelPartnerUtils.deepClone(tax);
                if(Objects.nonNull(taxCopy)) {
                    taxMap.put(tax.getSlug(), taxCopy);
                }
            } else {
                ZomatoOrderTaxDetailsV3 taxData = taxMap.get(tax.getSlug());
                taxData.setTaxAmount(taxData.getTaxAmount() + tax.getTaxAmount());
            }
            if(!packagingMap.containsKey(tax.getSlug())) {
                packagingMap.put(tax.getSlug(), BigDecimal.valueOf(charge.getChargeAmount()));
            } else {
                packagingMap.put(tax.getSlug(), ChannelPartnerUtils.add(packagingMap.get(tax.getSlug()), BigDecimal.valueOf(charge.getChargeAmount())));
            }
        }
        return packaging;
    }

    private static void addChargeItemsToOrder(Order order, MasterDataCache masterDataCache, boolean isInterState,
                                              Map<String, TaxDataVO> taxMap, Map<Integer, Product> products,
                                              int pricingUnitId, String discountName, BigDecimal discountPercent,
                                              boolean taxDeductedByPartner, Map<String, ZomatoOrderTaxDetailsV3> itemTaxMap,
                                              BigDecimal price, Map<String, BigDecimal> priceMap, String partnerTaxType) {
        Product product = products.get(ChannelPartnerServiceConstants.PACKAGING_PRODUCT_ID);
        OrderItem orderItem = AbstractConverters.getDefaultOrderItemFromRecipe(product, 1,
                masterDataCache, isInterState, taxMap, false, discountPercent, discountName,
                product.getPrices().get(0).getDimension(), pricingUnitId, BigDecimal.ZERO, taxDeductedByPartner, partnerTaxType);
        orderItem.getTaxes().clear();
        orderItem.setPrice(price);
        orderItem.setAmount(price);
        orderItem.setTotalAmount(price);
        BigDecimal totalItemTax = BigDecimal.ZERO;
        for(ZomatoOrderTaxDetailsV3 zomatoOrderItemTax : itemTaxMap.values()) {
            if (zomatoOrderItemTax.getTaxName().equalsIgnoreCase("CGST") && zomatoOrderItemTax.getTaxAmount() > 0) {
                TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("CGST", "GST", BigDecimal.valueOf(zomatoOrderItemTax.getTaxAmount()).setScale(2,RoundingMode.HALF_UP),new BigDecimal(zomatoOrderItemTax.getSlug().split("_")[3]),
                        priceMap.get(zomatoOrderItemTax.getSlug()), 1, BigDecimal.ZERO);
                orderItem.getTaxes().add(taxDetail);
                totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
            }
            if ((zomatoOrderItemTax.getTaxName().equalsIgnoreCase("SGST") || zomatoOrderItemTax.getTaxName().equalsIgnoreCase("UTGST")) && zomatoOrderItemTax.getTaxAmount() > 0) {
                TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("SGST/UTGST", "GST", BigDecimal.valueOf(zomatoOrderItemTax.getTaxAmount()).setScale(2,RoundingMode.HALF_UP),new BigDecimal(zomatoOrderItemTax.getSlug().split("_")[3]),
                        priceMap.get(zomatoOrderItemTax.getSlug()), 1, BigDecimal.ZERO);
                orderItem.getTaxes().add(taxDetail);
                totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
            }
        }
        orderItem.setTax(totalItemTax);
        order.getOrders().add(orderItem);
    }

    private static void addCondimentsForGheeAndTurmeric(Order order, MasterDataCache masterDataCache, Map<Integer, Product> products,
                                                        EnvironmentProperties environmentProperties, int pricingUnitId) {
        LOG.info("Brand Id of Order is : " + order.getBrandId());
        LOG.info("Channel Partner Condiment flag is " + environmentProperties.getCpCondimentFlag());
        // Ghee and turmeric Product Addition
        if (ChannelPartnerServiceConstants.GHEE_TURMERIC_BRAND_ID.equals(order.getBrandId()) && environmentProperties.getCpCondimentFlag()) {
            BigDecimal gntCartValue = BigDecimal.ZERO;
            for (OrderItem item : order.getOrders()) {
                Integer prodCatId = item.getProductCategory().getId();
                boolean catValid = Arrays.stream(ChannelPartnerServiceConstants.CHUTNEY_BOX_PRODUCT_CATEGORIES).anyMatch(value -> value == prodCatId);
                if (catValid && item.getProductId() != 1043) {
                    gntCartValue = ChannelPartnerUtils.subtract(ChannelPartnerUtils.add(ChannelPartnerUtils.multiply(item.getPrice(),
                            new BigDecimal(item.getQuantity())), gntCartValue), item.getDiscountDetail().getTotalDiscount());
                }
            }
            LOG.info("Brand Id is matched with ghee turmeric brand");
            if (gntCartValue.compareTo(BigDecimal.ZERO) > 0) {
                gntCartValue = gntCartValue.add(getPackaginPriceForCondimentOrderV3(gntCartValue, masterDataCache, pricingUnitId));
                Map<TaxDetailKey, TaxDetail> mapTax = new HashMap<>();
                gntCartValue = gntCartValue.add(AbstractConverters.aggregateTaxesFromOrderItems(order.getOrders(), mapTax));
                if (products.containsKey(ChannelPartnerServiceConstants.CHUTNEY_BOX_PRODUCT_ID)) {
                    Product product = products.get(ChannelPartnerServiceConstants.CHUTNEY_BOX_PRODUCT_ID);
                    LOG.info("Adding Product " + product.getId() + " to Order");
                    OrderItem orderItem = getCondimentProductV3(product, product.getPrices().get(0).getDimension(), gntCartValue);
                    order.getOrders().add(orderItem);
                    LOG.info("Product Chutney Box to Order is added successfully");
                }
            }
        }
    }

    private static BigDecimal getPackaginPriceForCondimentOrderV3(BigDecimal gntCartValue, MasterDataCache masterDataCache, int unitId) {
        BigDecimal totalPrice = BigDecimal.ZERO;
        if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("PERCENTAGE")) {
            totalPrice = ChannelPartnerUtils.multiply(ChannelPartnerUtils.divide(masterDataCache.getUnit(unitId).getPackagingValue(), new BigDecimal(100)), gntCartValue);
        } else if (masterDataCache.getUnit(unitId).getPackagingType().equalsIgnoreCase("FIXED")) {
            totalPrice = masterDataCache.getUnit(unitId).getPackagingValue();
        }
        return totalPrice;
    }

    private static OrderItem getCondimentProductV3(Product product, String dimension, BigDecimal cartValue) {
        LOG.info("Entering getCondimentProduct Method :");
        OrderItem orderItem = new OrderItem();
        orderItem.setProductId(product.getId());
        orderItem.setProductName(product.getName());
        orderItem.setProductCategory(new IdCodeName(product.getType(), "", ""));
        orderItem.setProductSubCategory(new IdCodeName(product.getSubType(), "", ""));
        orderItem.setQuantity(getCondimentQuantityV3(cartValue));
        ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
        complimentaryDetail.setIsComplimentary(false);
        orderItem.setComplimentaryDetail(complimentaryDetail);
        orderItem.setBillType(product.getBillType());
        orderItem.setCode(product.getTaxCode());
        orderItem.setBookedWastage(false);
        orderItem.setTakeAway(false);

        // Finding dimension for product
        ProductPrice productPrice = AbstractConverters.setOrderItemPrice(dimension, product, orderItem);
        // Adding Product Price
        if (productPrice == null) {
            productPrice = product.getPrices().get(0);
        }
        orderItem.setPrice(BigDecimal.ZERO);
        RecipeDetail recipeDetail = productPrice.getRecipe();
        if (recipeDetail != null) {
            orderItem.setRecipeId(recipeDetail.getRecipeId());
        }
        orderItem.setTotalAmount(BigDecimal.ZERO);
        orderItem.setTax(BigDecimal.ZERO);
        LOG.info("Exiting getCondimentProduct Method :");
        //orderItem.setItemCode();
        //orderItem.setReasonId();
        //orderItem.setCardType();
        return orderItem;
    }

    private static int getCondimentQuantityV3(BigDecimal cartValue) {
        BigDecimal quantity = (cartValue.setScale(0, BigDecimal.ROUND_DOWN)).divide(ChannelPartnerServiceConstants.CHUTNEY_BOX_THRESHOLD_PRICE, 0);
        return (quantity.setScale(0, BigDecimal.ROUND_DOWN)).intValue();
    }

    private static String getDiscountNameV3(ZomatoOrderRequestV3 request) {
        StringBuilder result = new StringBuilder();
        String prefix = "";
        String delimiter = ",";
        for (ZomatoOrderDiscountV3 zomatoOrderDiscount : request.getOrderDiscounts()) {
            result.append(prefix).append(zomatoOrderDiscount.getVoucherCode().equalsIgnoreCase("") ? "PARTNER" : zomatoOrderDiscount.getVoucherCode());
            prefix = delimiter;
        }
        return result.toString();
    }

    private static void createRecommendedProductsDataV3(Order order, List<ZomatoOrderDishesV3> recommendedOrderDishes, Map<Integer, Product> products,
                                                        PartnerOrderDetail partnerOrderDetail, Map<String, TaxDataVO> taxMap, BigDecimal discountPercent,
                                                        MasterDataCache masterDataCache, String discountName, Map<Integer, Map<String, BigDecimal>> pricingMap, PercentageDetail percentageDetail, Map<Integer, Product> cafeProducts, String orderInstructions,EnvironmentProperties environmentProperties) {
        for (ZomatoOrderDishesV3 item : recommendedOrderDishes) {
            boolean productMatched = false;
            Integer productId = null;
            if (item.getDishType().equalsIgnoreCase("combo")) {
                productId = Integer.parseInt(item.getDishId());
            } else {
                productId = Integer.parseInt(item.getComposition().getCatalogueId().replaceAll("(_[A-Za-z0-9]*)", ""));
            }
            if (products.containsKey(productId)) {
                Product product = products.get(productId);
                productMatched = true;
                OrderItem orderItem = convertOrderItemV3(product, item, taxMap, products, discountPercent, null, BigDecimal.ZERO, 1,
                        false, masterDataCache, partnerOrderDetail.getBrandId(), recommendedOrderDishes, discountName, pricingMap, percentageDetail, new ArrayList<>(),true, cafeProducts, orderInstructions,environmentProperties);
                order.getOrders().add(orderItem);
            }
            if (!productMatched) {
                PartnerOrderError partnerOrderError = new PartnerOrderError();
                partnerOrderError.setErrorCode(PartnerOrderErrorCode.PRODUCT_MISMATCH);
                partnerOrderError.setErrorDescription(
                        "Product " + item.getComposition().getCatalogueName() + " with id " + item.getComposition().getCatalogueId() + " not found.");
                partnerOrderDetail.getOrderErrors().add(partnerOrderError);
            }
        }
    }

    private static void updateDesiChaiProductIdsV3(ZomatoOrderRequestV3 request) {
        request.getDishes().forEach(item -> {
            if (!item.getDishType().equalsIgnoreCase("combo")) {
                if (Integer.valueOf(item.getComposition().getCatalogueId().replaceAll("(_[A-Za-z0-9]*)", "")).equals(10)) {
                    updateDesiChaiV3(item);
                } else if (Integer.valueOf(item.getComposition().getCatalogueId().replaceAll("(_[A-Za-z0-9]*)", "")).equals(1282)) {
                    updateBaarishWaliChaiV3(item);
                }
            }
        });
    }

    private static void updateDesiChaiV3(ZomatoOrderDishesV3 item) {
        item.getComposition().getProperties().forEach(properties -> {
            if (ChannelPartnerUtils.getDesiChaiMilkMap().get(properties.getPropertyValue().trim()) != null) {
                item.getComposition().setCatalogueId(ChannelPartnerUtils.getDesiChaiMilkMap()
                        .get(properties.getPropertyValue().trim()).toString());
                String name = item.getComposition().getCatalogueName();
                if (item.getComposition().getCatalogueName().contains("(")) {
                    name = item.getComposition().getCatalogueName().split("\\(")[0];
                }
                name += " " + properties.getPropertyValue().trim();
                item.getComposition().setCatalogueName(name);
            }
        });
    }

    private static void updateBaarishWaliChaiV3(ZomatoOrderDishesV3 item) {
        item.getComposition().getProperties().forEach(properties -> {
            if (ChannelPartnerUtils.getBaarishWaliChaiMilkMap().get(properties.getPropertyValue().trim()) != null) {
                item.getComposition().setCatalogueId(ChannelPartnerUtils.getBaarishWaliChaiMilkMap()
                        .get(properties.getPropertyValue().trim()).toString());
                String name = item.getComposition().getCatalogueName();
                if (item.getComposition().getCatalogueName().contains("(")) {
                    name = item.getComposition().getCatalogueName().split("\\(")[0];
                }
                name += " " + properties.getPropertyValue().trim();
                item.getComposition().setCatalogueName(name);
            }
        });
    }

    private static OrderItem convertOrderItemV3(Product product, ZomatoOrderDishesV3 item, Map<String, TaxDataVO> taxMap, Map<Integer, Product> products,
                                                BigDecimal discountPercent, String dimension, BigDecimal comboDiscountPercent, Integer parentQuantity, boolean isConstituent,
                                                MasterDataCache masterDataCache, Integer brandId, List<ZomatoOrderDishesV3> recommendedOrderDishes, String discountName,
                                                Map<Integer, Map<String, BigDecimal>> pricingMap, PercentageDetail discountPercentageDetail, List<OrderItem> paidAddonsItems, boolean isRecommendedProduct, Map<Integer, Product> cafeProducts, String orderInstructions, EnvironmentProperties environmentProperties) {
        OrderItem orderItem = new OrderItem();
        orderItem.setProductId(product.getId());
        orderItem.setProductName(product.getName());
        boolean superCombo = product.getSubType() == ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID;
        orderItem.setProductCategory(new IdCodeName(product.getType(), "", ""));
        orderItem.setProductSubCategory(new IdCodeName(product.getSubType(), "", ""));
        orderItem.setQuantity(item.getQuantity());

        //Assigning category and subCategory to OrderItem
        orderItem.setSourceCategory(item.getComposition().getCategoryName());
        orderItem.setSourceSubCategory(item.getComposition().getSubCategoryName());

        BigDecimal unitCost;
        AtomicBoolean isRecommendedDish= new AtomicBoolean(false);
        DiscountDetail recomDiscountDetail = null;
        if (!superCombo) {
            recomDiscountDetail = new DiscountDetail();
            unitCost = getRecommendedPriceAndDiscountExtractionV3(item, recommendedOrderDishes, isRecommendedDish, recomDiscountDetail);
        } else {
            unitCost = BigDecimal.valueOf(item.getComposition().getUnitCost()).setScale(2, RoundingMode.HALF_UP);
        }
        orderItem.setPrice(unitCost);
        ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
        complimentaryDetail.setIsComplimentary(false);
        orderItem.setComplimentaryDetail(complimentaryDetail);
        orderItem.setBillType(product.getBillType());
        orderItem.setCode(product.getTaxCode());
        orderItem.setBookedWastage(false);
        orderItem.setTakeAway(false);
        orderItem.setTaxDeductedByPartner(TaxPayingEntity.PARTNER.getZomato().equalsIgnoreCase(item.getTaxes().get(0).getType()));
        orderItem.setPartnerTaxType(item.getTaxes().get(0).getType());
        ProductPrice productPrice = null;

        if (dimension == null) {
            //for products with only single dimension like food category items
            if (product.getPrices().size() == 1) {
                productPrice = product.getPrices().get(0);
            } else if (product.getPrices().size() > 1) {
                //for products with multiple dimensions where size property is present
                String dimensionCode = item.getComposition().getVariantId().split("_")[1];
                List<ProductPrice> productPrices = product.getPrices().stream().filter(productPrice1 ->
                        productPrice1.getDimension().equalsIgnoreCase(dimensionCode)).collect(Collectors.toList());
                if (!productPrices.isEmpty()) {
                    productPrice = productPrices.get(0);
                }
            }
            if (productPrice == null) {
                BigDecimal price = BigDecimal.ZERO;
                if (item.getComposition().getUnitCost() > 0) {
                    price = unitCost;
                }
                TreeMap<BigDecimal, ProductPrice> priceDifference = new TreeMap<>();
                for (ProductPrice pPrice : product.getPrices()) {
                    priceDifference.put(ChannelPartnerUtils.subtract(price, pPrice.getPrice()).abs(), pPrice);
                }
                productPrice = priceDifference.get(priceDifference.firstKey());
            }
        } else {
            for (ProductPrice pPrice : product.getPrices()) {
                if (pPrice.getDimension().equalsIgnoreCase(dimension)) {
                    productPrice = pPrice;
                }
            }
            if (productPrice == null) {
                productPrice = product.getPrices().get(0);
            }
        }
        // Original price of product from pricing unit this can be different from partner price
        // because of dynamic price which is reduced from original price
        if (product.getSubType() != ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID &&
                product.getSubType() != ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID) {
            orderItem.setOriginalPrice(productPrice.getPrice());
        }
        int productId = product.getId();
        if (ChannelPartnerUtils.getDesiChaiVariantMap().containsKey(productId)) {
            productId = ChannelPartnerUtils.getDesiChaiVariantMap().get(productId);
        }
        if (pricingMap != null && pricingMap.containsKey(productId) && pricingMap.get(productId).containsKey(productPrice.getDimension())) {
            productPrice.setPrice(pricingMap.get(productId).get(productPrice.getDimension()));
        }
        if (product.getSubType() != ChannelPartnerServiceConstants.HERO_COMBO_SUBCATEGORY_ID &&
                product.getSubType() != ChannelPartnerServiceConstants.SUPER_COMBO_SUBCATEGORY_ID && Objects.nonNull(recommendedOrderDishes) && recommendedOrderDishes.isEmpty()) {
            // orderItem.setPrice(productPrice.getPrice().setScale(2, RoundingMode.HALF_UP));
            if (item.getComposition().getUnitCost() != null && item.getComposition().getUnitCost() > 0) {
                orderItem.setPrice(BigDecimal.valueOf(item.getComposition().getUnitCost()).setScale(2, RoundingMode.HALF_UP));
            } else {
                orderItem.setPrice(productPrice.getPrice().setScale(2, RoundingMode.HALF_UP));
            }
        }
        orderItem.setDimension(productPrice.getDimension());

        RecipeDetail recipeDetail = productPrice.getRecipe();
        //LOG.info(new Gson().toJson(recipeDetail));
        orderItem.setRecipeId(recipeDetail.getRecipeId());

//        orderItem.setTotalAmount(ChannelPartnerUtils.multiply(orderItem.getPrice(), new BigDecimal(orderItem.getQuantity() * parentQuantity)));
        if(Objects.nonNull(recommendedOrderDishes)  && recommendedOrderDishes.isEmpty() && !isRecommendedProduct){
            orderItem.setTotalAmount(BigDecimal.valueOf(item.getTotalCost()));
        }else if (Objects.nonNull(recommendedOrderDishes) && !recommendedOrderDishes.isEmpty() && !isRecommendedProduct){
            orderItem.setTotalAmount(ChannelPartnerUtils.multiply(orderItem.getPrice(), BigDecimal.valueOf(orderItem.getQuantity())));
        }else if (Objects.nonNull(recommendedOrderDishes)  && !recommendedOrderDishes.isEmpty() && isRecommendedProduct){
            orderItem.setTotalAmount(BigDecimal.valueOf(item.getTotalCost()));
        }

        //Setting discount data

        //SETTING DISCOUNT DATA AT ORDER ITEM LEVEL (I.E DISCOUNT OFFERENT FOR EACH ORDER ITEM )
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setTotalDiscount(BigDecimal.ZERO);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        BigDecimal orderItemLevelDiscountValue = BigDecimal.ZERO;
        if(Objects.nonNull(item.getTotalDiscount()) && item.getTotalDiscount() > 0) {
            orderItemLevelDiscountValue = BigDecimal.valueOf(item.getTotalDiscount());
        }
        /*if(Objects.nonNull(item.getDishDiscounts()) && !item.getDishDiscounts().isEmpty()){
            for(ZomatoOrderDishDiscountV3 dishDiscountV3 :item.getDishDiscounts()){
                orderItemLevelDiscountValue= AppUtils.add(orderItemLevelDiscountValue, BigDecimal.valueOf(dishDiscountV3.getDiscountValue()));
            }
        }*/
        /*try {
            orderItemLevelDiscountValue = BigDecimal.valueOf(item.getTotalDiscount()).setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            LOG.error("No discount value mapped with item :{}", e.getMessage());
        }*/
        PercentageDetail pDetail = new PercentageDetail();
        pDetail.setPercentage(BigDecimal.ZERO);
        pDetail.setValue(BigDecimal.ZERO);
        if(!isRecommendedDish.get()){
            if (isConstituent) {
                pDetail.setPercentage(comboDiscountPercent);
                pDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(pDetail.getPercentage(),
                        orderItem.getTotalAmount()));
            } else {
                if (orderItemLevelDiscountValue.compareTo(BigDecimal.ZERO) > 0) {
                    discountDetail.setPromotionalOffer(orderItemLevelDiscountValue);
                }
            }
        }else{
            if(Objects.nonNull(recomDiscountDetail)){
                orderItemLevelDiscountValue = ChannelPartnerUtils.percentageOfWithScale10(recomDiscountDetail.getDiscount().getPercentage(),orderItem.getTotalAmount());
                discountDetail.setPromotionalOffer(orderItemLevelDiscountValue);
            }
        }
        discountDetail.setDiscount(pDetail);
        discountDetail.setTotalDiscount(ChannelPartnerUtils.add(discountDetail.getPromotionalOffer(), discountDetail.getDiscount().getValue()));

        if (discountPercent.compareTo(BigDecimal.ZERO) > 0 && comboDiscountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason(discountName.equalsIgnoreCase("") ? "ZOMATO+COMBO" : discountName + "+COMBO");
        } else if (discountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason(discountName.equalsIgnoreCase("") ? "ZOMATO" : discountName);
        } else if (comboDiscountPercent != null && comboDiscountPercent.compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountReason("COMBO");
        }
        if (pDetail.getValue().compareTo(BigDecimal.ZERO) > 0) {
            discountDetail.setDiscountCode(2004);
        }
//        if (percentageDetail.getPercentage().compareTo(BigDecimal.ZERO) > 0) {
//            discountDetail.setDiscountCode(2004);
//            percentageDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(percentageDetail.getPercentage(),
//                    orderItem.getTotalAmount()));
//        }
//        discountDetail.setDiscount(percentageDetail);
//        discountDetail.setTotalDiscount(
//                ChannelPartnerUtils.add(discountDetail.getPromotionalOffer(), percentageDetail.getValue()));

//        Setting discount data for orderItem and caculating net amount(amount for order Item= totalAmount -dicountAmount)

        orderItem.setDiscountDetail(discountDetail);
//        orderItem.setAmount(ChannelPartnerUtils.subtract(orderItem.getTotalAmount(), discountDetail.getTotalDiscount()));
        BigDecimal itemFinalAmt=BigDecimal.ZERO;
        if(Objects.nonNull(recommendedOrderDishes) && recommendedOrderDishes.isEmpty() && !isRecommendedProduct){
        if(Objects.nonNull(item.getFinalCost()) && BigDecimal.ZERO.compareTo(orderItemLevelDiscountValue)<0){
            itemFinalAmt = AppUtils.subtract(BigDecimal.valueOf(item.getTotalCost()), orderItemLevelDiscountValue);
        }else if(Objects.nonNull(item.getFinalCost())) {
            itemFinalAmt = BigDecimal.valueOf(item.getFinalCost());
        }
        }else if (Objects.nonNull(recommendedOrderDishes) && !recommendedOrderDishes.isEmpty() ){
            itemFinalAmt=AppUtils.subtract(orderItem.getTotalAmount(),orderItemLevelDiscountValue);
        }/*else if (Objects.nonNull(recommendedOrderDishes) && !recommendedOrderDishes.isEmpty() && isRecommendedProduct){
            itemFinalAmt= orderItem.getTotalAmount();
        }*/

        orderItem.setAmount(itemFinalAmt);
        // Zomato does not provide tax bifurcations

        //Setting addons
        OrderItemComposition orderItemComposition = new OrderItemComposition();
        setRecipeAddons(superCombo, item, orderItemComposition, recipeDetail);
        setRecipePaidAddons(superCombo, item, orderItemComposition, productPrice, taxMap, products, paidAddonsItems, cafeProducts, product);
        setRecipeIngredientVariants(superCombo, item, orderItemComposition, recipeDetail);
        setRecipeIngredientProducts(superCombo, item, orderItemComposition, recipeDetail);
        // Setting default ingredient variants for missing ones
        setMissingRecipeIngredientVariants(item, orderItemComposition, recipeDetail);
        // Setting default ingredient products for missing ones
        setMissingRecipeIngredientProducts(item, orderItemComposition, recipeDetail);
        if(environmentProperties.getEditOrderCompositionBasisRemark() && product.getType()==5){
            editSugarRecipeIngredientVarientsBasisRemark(item, orderItemComposition, recipeDetail,orderInstructions);
        }
        if (recipeDetail.getIngredient().getCompositeProduct() != null) {
            Set<String> catalogues = new HashSet<>();
            if (!superCombo) {
                for (ZomatoOrderCompositionPropV3 prop : item.getComposition().getProperties()) {
                    String[] arr = prop.getPropertyValueId().trim().split("_");
                    catalogues.add(arr[0]);
                }
                if (product.getSubType() == 3675) {
                    for (ZomatoOrderModifierGroupsV3 mod : item.getComposition().getModifierGroups()) {
                        for (ZomatoOrderVariantsV3 var : mod.getVariants()) {
                            if (var.getCatalogueId().endsWith(ChannelPartnerServiceConstants.HERO_ITEM_IDENTIFIER)) {
                                String[] arr = var.getCatalogueId().trim().split("_");
                                catalogues.add(arr[0]);
                            }
                        }
                    }
                }
            }
            BigDecimal comboTotalPrice = BigDecimal.ZERO;
            for (CompositeIngredientData compositeIngredientData : recipeDetail.getIngredient().getCompositeProduct().getDetails()) {
                boolean found = false;
                IngredientProductDetail defaultMenuProduct = null;
                for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        defaultMenuProduct = ingredientProductDetail;
                    }
                    if (!superCombo) {
                        for (String itemId : catalogues) {
                            if (Integer.parseInt(itemId) == ingredientProductDetail.getProduct().getProductId()) {
                                comboTotalPrice = comboTotalPrice.add(
                                        AbstractConverters.getComboTotalPrice(products, ingredientProductDetail, orderItem.getQuantity()));
                                found = true;
                            }
                        }
                    } else {
                        for (ZomatoOrderCompSelectionV3 selection : item.getComposition().getSelections()) {
                            String itemId = selection.getSelectionEntities().get(0).getCatalogueId().replaceAll("(_[A-Za-z0-9]*)", "");
                            if (Integer.parseInt(itemId) == ingredientProductDetail.getProduct().getProductId()) {
//                                comboTotalPrice = comboTotalPrice.add(
//                                        AbstractConverters.getComboTotalPrice(products, ingredientProductDetail, orderItem.getQuantity()));
//                                AbstractConverters.getComboTotalPriceForSelectionEntities();
                                //TODO Rahul Verify this
                                comboTotalPrice = comboTotalPrice.add(ChannelPartnerUtils.add(comboTotalPrice, BigDecimal.valueOf(selection.getSelectionEntities().get(0).getUnitCost())));
                                found = true;
                            }
                        }
                    }
                }
                if (!found) {
                    if (defaultMenuProduct == null) {
                        defaultMenuProduct = compositeIngredientData.getMenuProducts().get(0);
                    }
                    comboTotalPrice = comboTotalPrice.add(AbstractConverters.getComboTotalPrice(products,
                            defaultMenuProduct, orderItem.getQuantity()));
                }
            }

            BigDecimal comboDiscount = ChannelPartnerUtils.subtract(comboTotalPrice, ChannelPartnerUtils.multiply(orderItem.getPrice(), BigDecimal.valueOf(orderItem.getQuantity())));
            if (discountDetail.getDiscount().getValue().compareTo(BigDecimal.ZERO) > 0) {
                comboDiscount = comboDiscount.add(discountDetail.getDiscount().getValue());
            }
            BigDecimal comboDiscountPercentage = ChannelPartnerUtils.percentageWithScale10(comboDiscount, comboTotalPrice);

            // setting menu items
            for (CompositeIngredientData compositeIngredientData : recipeDetail.getIngredient().getCompositeProduct()
                    .getDetails()) {
                boolean found = false;
                IngredientProductDetail defaultMenuProduct = null;
                for (IngredientProductDetail ingredientProductDetail : compositeIngredientData.getMenuProducts()) {
                    if (ingredientProductDetail.isDefaultSetting()) {
                        defaultMenuProduct = ingredientProductDetail;
                    }
                    if (!superCombo) {
                        for (String itemId : catalogues) {
                            if (Integer.parseInt(itemId) == ingredientProductDetail.getProduct().getProductId()) {
                                // updating desi chai product Id
                                if (Integer.parseInt(itemId) == 10) {
                                    for (ZomatoOrderCompositionPropV3 properties : item.getComposition().getProperties()) {
                                        if (ChannelPartnerUtils.getDesiChaiMilkMap().get(properties.getPropertyValue().trim()) != null) {
                                            itemId = ChannelPartnerUtils.getDesiChaiMilkMap().get(properties.getPropertyValue().trim()).toString();
                                        }
                                    }
                                }
                                if (products.containsKey(Integer.parseInt(itemId))) {
                                    Product comboConstituent = products.get(Integer.parseInt(itemId));
                                    String comboConstituentDimension = ingredientProductDetail.getDimension().getCode();
                                    ZomatoOrderDishesV3 dishes = createItemOrder(ingredientProductDetail.getQuantity().intValue(), item,
                                            Integer.valueOf(ingredientProductDetail.getProduct().getProductId()).toString());
                                    OrderItem comboConstituentOrderItem = convertOrderItemV3(comboConstituent, dishes, taxMap, products, discountPercent,
                                            comboConstituentDimension, comboDiscountPercentage, orderItem.getQuantity(), true, masterDataCache, brandId,
                                            null, discountName, pricingMap, discountPercentageDetail, paidAddonsItems,false, cafeProducts,orderInstructions, environmentProperties);
                                    orderItemComposition.getMenuProducts().add(comboConstituentOrderItem);
                                    found = true;
                                }
                            }
                        }
                    } else {
                        for (ZomatoOrderCompSelectionV3 selection : item.getComposition().getSelections()) {
                            String itemId = selection.getSelectionEntities().get(0).getCatalogueId().replaceAll("(_[A-Za-z0-9]*)", "");
                            if (Integer.parseInt(itemId) == ingredientProductDetail.getProduct().getProductId() &&
                                    compositeIngredientData.getName().equalsIgnoreCase(selection.getSelectionTitle())) {
                                //LOG.info("item matched " + compositeIngredientData.getName()+ ":"+itemId);
                                // updating desi chai product Id
                                if (Integer.parseInt(itemId) == 10) {
                                    for (ZomatoOrderCompositionPropV3 properties : selection.getSelectionEntities()
                                            .get(0).getProperties()) {
                                        if (ChannelPartnerUtils.getDesiChaiMilkMap().get(properties.getPropertyValue().trim()) != null) {
                                            itemId = ChannelPartnerUtils.getDesiChaiMilkMap().get(properties.getPropertyValue().trim()).toString();
                                        }
                                    }
                                }
                                if (products.containsKey(Integer.parseInt(itemId))) {
                                    Product comboConstituent = products.get(Integer.parseInt(itemId));
                                    String comboConstituentDimension = ingredientProductDetail.getDimension().getCode();
                                    ZomatoOrderDishesV3 dishes = createItemOrderOfSuperComboV3(ingredientProductDetail.getQuantity().intValue(),
                                            selection.getSelectionEntities().get(0), item.getTaxes().get(0).getType());
                                    OrderItem comboConstituentOrderItem = convertOrderItemV3(comboConstituent, dishes, taxMap, products, discountPercent,
                                            comboConstituentDimension, comboDiscountPercentage, orderItem.getQuantity(), true, masterDataCache, brandId,
                                            null, discountName, pricingMap, discountPercentageDetail, paidAddonsItems,false, cafeProducts, orderInstructions,environmentProperties);
                                    orderItemComposition.getMenuProducts().add(comboConstituentOrderItem);
                                    found = true;
                                }
                            }
                        }
                    }
                }
                if (!found) {
                    //LOG.info("product not found in zomato order customization so punching default one from recipe::::::");
                    if (defaultMenuProduct == null) {
                        defaultMenuProduct = compositeIngredientData.getMenuProducts().get(0);
                    }
                    Product comboConstituent = products.get(defaultMenuProduct.getProduct().getProductId());
                    String comboConstituentDimension = defaultMenuProduct.getDimension().getCode();
                    // creating default item without customizations
                    ZomatoOrderDishesV3 zomatoOrderItem = new ZomatoOrderDishesV3();
                    zomatoOrderItem.setQuantity(defaultMenuProduct.getQuantity().intValue());
                    ZomatoOrderCompositionV3 itemComp = new ZomatoOrderCompositionV3();
                    itemComp.setModifierGroups(new ArrayList<>());
                    zomatoOrderItem.setTotalCost(BigDecimal.ZERO.floatValue());
                    itemComp.setUnitCost(BigDecimal.ZERO.floatValue());
                    itemComp.setProperties(new ArrayList<>());
                    if (defaultMenuProduct.getIngredient() != null) {
                        for (IngredientVariant ingredientVariant : defaultMenuProduct.getIngredient().getVariants()) {
                            for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                                if (ingredientVariantDetail.isDefaultSetting()) {
                                    List<ZomatoOrderCompositionPropV3> propComp = new ArrayList<>();
                                    ZomatoOrderCompositionPropV3 defaultProp = new ZomatoOrderCompositionPropV3();
                                    defaultProp.setPropertyValue(ingredientVariantDetail.getAlias());
                                    propComp.add(defaultProp);
                                    itemComp.setProperties(propComp);
                                }
                            }
                        }
                        for (IngredientProduct ingredientProduct : defaultMenuProduct.getIngredient().getProducts()) {
                            for (IngredientProductDetail ingredientProductDetail1 : ingredientProduct.getDetails()) {
                                if (ingredientProductDetail1.isDefaultSetting()) {
                                    List<ZomatoOrderCompositionPropV3> propComp = new ArrayList<>();
                                    ZomatoOrderCompositionPropV3 defaultProp = new ZomatoOrderCompositionPropV3();
                                    defaultProp.setPropertyValue(ingredientProductDetail1.getProduct().getName());
                                    propComp.add(defaultProp);
                                    itemComp.setProperties(propComp);
                                }
                            }
                        }
                    }
                    if (defaultMenuProduct.getAddons() != null) {
                        for (IngredientProductDetail productDetail : defaultMenuProduct.getAddons()) {
                            if (productDetail.isDefaultSetting()) {
                                List<ZomatoOrderModifierGroupsV3> modGroup = new ArrayList<ZomatoOrderModifierGroupsV3>();
                                List<ZomatoOrderVariantsV3> modVariant = new ArrayList<ZomatoOrderVariantsV3>();
                                ZomatoOrderVariantsV3 mVarient = new ZomatoOrderVariantsV3();
                                mVarient.setCatalogueName(productDetail.getProduct().getName());
                                modVariant.add(mVarient);
                                ZomatoOrderModifierGroupsV3 modGroupVar = new ZomatoOrderModifierGroupsV3();
                                modGroupVar.setVariants(modVariant);
                                modGroup.add(modGroupVar);
                                itemComp.setModifierGroups(modGroup);
                            }
                        }
                    }
                    zomatoOrderItem.setComposition(itemComp);
                    List<ZomatoOrderTaxDetailsV3> zomatoOrderTaxDetailsV3List = new ArrayList<>();
                    //TODO Rahul verify this with Ishman as it does not make any sense because we created new object so taxes list will be empty
                    for (ZomatoOrderTaxDetailsV3 zomatoOrderTaxDetailsV3 : zomatoOrderItem.getTaxes()) {
                        zomatoOrderTaxDetailsV3List.add(zomatoOrderTaxDetailsV3);
                    }
                    zomatoOrderItem.setTaxes(zomatoOrderTaxDetailsV3List);
                    ZomatoOrderTaxDetailsV3 tax = new ZomatoOrderTaxDetailsV3();
                    tax.setType(item.getTaxes().get(0).getType());
                    zomatoOrderItem.getTaxes().add(tax);
                    OrderItem comboConstituentOrderItem = convertOrderItemV3(comboConstituent, zomatoOrderItem,
                            taxMap, products, discountPercent, comboConstituentDimension,
                            comboDiscountPercentage, orderItem.getQuantity(), true, masterDataCache, brandId,
                            null, discountName, pricingMap, discountPercentageDetail, paidAddonsItems,false, cafeProducts, orderInstructions,environmentProperties);
                    orderItemComposition.getMenuProducts().add(comboConstituentOrderItem);
                }
            }
        }
        orderItem.setComposition(orderItemComposition);

        //Setting taxes
//        AbstractConverters.setOrderItemTaxes(product, orderItem, taxMap, discountDetail, parentQuantity);
        setOrderItemTaxesMappedFromZomato(product, orderItem, taxMap, discountDetail, parentQuantity, item,isRecommendedProduct,isRecommendedDish);
        //setOrderItemInstructions(null, orderItem,item.getComposition().getInstructions(),null);
        //orderItem.setReasonId();
        //orderItem.setComposition();
        //orderItem.setItemCode();
        //orderItem.setCardType();
        //discountDetail.setDiscountReason("ZOMATO");
        //discountDetail.setDiscountCode(2004);
        LOG.error("OrderItem with id :{} has price", orderItem.getProductId());
        return orderItem;
    }

    private static void editSugarRecipeIngredientVarientsBasisRemark(ZomatoOrderDishesV3 item, OrderItemComposition orderItemComposition, RecipeDetail recipeDetail, String orderInstructions) {
        Map<String, String> orderItemRemarkMap = ChannelPartnerUtils.getOrderItemRemarkMap();
        AtomicReference<String> key = new AtomicReference<>("");
        try{
            recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                boolean editApplicable = false;
//                for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
//                    for (ZomatoOrderCompositionPropV3 itemVariants : item.getComposition().getProperties()) {
                        if (!orderItemRemarkMap.isEmpty() && orderInstructions != null && orderInstructions.length() > 0) {
                            for(Map.Entry<String,String> entry : orderItemRemarkMap.entrySet()){
                                String instructions = orderInstructions.trim().toLowerCase();
                                String matcher = entry.getKey().trim().toLowerCase();
                                if(instructions.contains(matcher)) {
                                    editApplicable = true;
                                    key.set(orderItemRemarkMap.get(matcher));
                                    break;
                                }
                            }
                        }
//                    }
//                }
                if (editApplicable) {
                    ingredientVariant.getDetails().forEach(ingredientVariantDetail -> {
                        if (ingredientVariantDetail.getAlias().equalsIgnoreCase(key.get())) {
                            replaceExistingSugarVariant(ingredientVariantDetail,orderItemComposition,key);
                        }
                    });
                }
            });
        }catch(Exception e ){
            LOG.error("Exception while editing order item composition for sugar", e);
        }

    }

    private static void replaceExistingSugarVariant(IngredientVariantDetail ingredientVariantDetail, OrderItemComposition orderItemComposition, AtomicReference<String> key) {
        if(Objects.nonNull(orderItemComposition) && Objects.nonNull(orderItemComposition.getVariants()) && !orderItemComposition.getVariants().isEmpty()){
            for (int i = 0 ; i<orderItemComposition.getVariants().size(); i++){
                if(orderItemComposition.getVariants().get(i).getAlias().equalsIgnoreCase(REGULAR_SUGAR)){
                    orderItemComposition.getVariants().set(i,ingredientVariantDetail);
                }
            }
        }
    }

    /*private static void setOrderItemInstructions(Order order , OrderItem orderItem, List<ZomatoOrderItemInstructions> instructions, ZomatoOrderRequestV3 requestV3) {
        try{
            Map<String, String> orderItemRemarkMap = ChannelPartnerUtils.getOrderItemRemarkMap();
            if(!orderItemRemarkMap.isEmpty()){
                if(Objects.isNull(order) && Objects.nonNull(orderItem) && Objects.nonNull(instructions) && !instructions.isEmpty()){
                    if(orderItemRemarkMap.containsKey(instructions.get(0).getValue().trim().toLowerCase())){
                        orderItem.setOrderItemRemark(orderItemRemarkMap.get(instructions.get(0).getValue().trim()));
                    }
                }else{
                    if(Objects.nonNull(order) && Objects.nonNull(requestV3) && orderItemRemarkMap.containsKey(requestV3.getOrderInstructions().trim().toLowerCase())){
                        order.setOrderRemark(orderItemRemarkMap.get(requestV3.getOrderInstructions().trim().toLowerCase()));
                    }
                }
            }
        }catch(Exception e ){
            LOG.error("Exception ehile setting order remarks ::::::::::", e);
        }

    }*/

    public static void setOrderItemTaxesMappedFromZomato(Product product, OrderItem orderItem, Map<String, TaxDataVO> taxMap, DiscountDetail discountDetail, Integer parentQuantity, ZomatoOrderDishesV3 item, boolean isRecommendedProduct, AtomicBoolean isRecommendedDish) {
        if (!Objects.isNull(item)) {
            BigDecimal totalItemTax= BigDecimal.ZERO;
            if ( Objects.nonNull(item.getTaxes()) &&!item.getTaxes().isEmpty() && item.getTaxes().size() > 0 && !isRecommendedProduct && !isRecommendedDish.get()) {
                for (ZomatoOrderTaxDetailsV3 zomatoOrderItemTax : item.getTaxes()) {
                    totalItemTax= totalItemTax.add(setTaxForOrderItemV3(product, orderItem, discountDetail, parentQuantity, item, zomatoOrderItemTax));
                }
                orderItem.setTax(totalItemTax);
            }else if (isRecommendedProduct || isRecommendedDish.get()){
                AbstractConverters.setOrderItemTaxes(product,orderItem,taxMap,discountDetail,1);
            }
        }
    }

    private static BigDecimal setTaxForOrderItemV3(Product product, OrderItem orderItem, DiscountDetail discountDetail, Integer parentQuantity, ZomatoOrderDishesV3 item, ZomatoOrderTaxDetailsV3 zomatoOrderItemTax) {
        BigDecimal totalItemTax = BigDecimal.ZERO;
        Integer qty = orderItem.getQuantity() * parentQuantity;
        if(Objects.nonNull(zomatoOrderItemTax.getTaxAmount()) && Objects.nonNull(zomatoOrderItemTax.getTaxName()) ){
            if (false) { //interstate is not supported
                if (zomatoOrderItemTax.getTaxName().equalsIgnoreCase("IGST") && zomatoOrderItemTax.getTaxAmount() > 0) {
                    TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("IGST", "GST", BigDecimal.valueOf(zomatoOrderItemTax.getTaxAmount()).setScale(2,RoundingMode.HALF_UP),new BigDecimal(zomatoOrderItemTax.getSlug().split("_")[3]),
                            orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                    orderItem.getTaxes().add(taxDetail);
                    totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                }
            } else {
                if (zomatoOrderItemTax.getTaxName().equalsIgnoreCase("CGST") && zomatoOrderItemTax.getTaxAmount() > 0) {
                    TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("CGST", "GST", BigDecimal.valueOf(zomatoOrderItemTax.getTaxAmount()).setScale(2,RoundingMode.HALF_UP),new BigDecimal(zomatoOrderItemTax.getSlug().split("_")[3]),
                            orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                    orderItem.getTaxes().add(taxDetail);
                    totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                }
                if ((zomatoOrderItemTax.getTaxName().equalsIgnoreCase("SGST") || zomatoOrderItemTax.getTaxName().equalsIgnoreCase("UTGST")) && zomatoOrderItemTax.getTaxAmount() > 0) {
                    TaxDetail taxDetail = AbstractConverters.createTaxDetailV3("SGST/UTGST", "GST", BigDecimal.valueOf(zomatoOrderItemTax.getTaxAmount()).setScale(2,RoundingMode.HALF_UP),new BigDecimal(zomatoOrderItemTax.getSlug().split("_")[3]),
                            orderItem.getPrice(), qty, discountDetail.getTotalDiscount());
                    orderItem.getTaxes().add(taxDetail);
                    totalItemTax = ChannelPartnerUtils.add(totalItemTax, taxDetail.getValue());
                }
            }
        }
        return totalItemTax;
    }

    private static void setRecipeIngredientVariants(boolean superCombo, ZomatoOrderDishesV3 item, OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        if (!superCombo) {
            for (ZomatoOrderCompositionPropV3 itemVariants : item.getComposition().getProperties()) {
                recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
                    for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                        if (ingredientVariantDetail.getAlias()
                                .equalsIgnoreCase(itemVariants.getPropertyValue().trim())) {
                            orderItemComposition.getVariants().add(ingredientVariantDetail);
                        }
                    }
                });
            }
        }
    }

    private static void setRecipeAddons(boolean superCombo, ZomatoOrderDishesV3 item, OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        if (!superCombo) {
            for (ZomatoOrderModifierGroupsV3 addOnsItem : item.getComposition().getModifierGroups()) {
                for (ZomatoOrderVariantsV3 addOnVariants : addOnsItem.getVariants()) {
                    for (IngredientProductDetail ingredientProductDetail : recipeDetail.getAddons()) {
                        Integer catalogueId = 0;
                        try {
                            catalogueId = Integer.valueOf(addOnVariants.getCatalogueId());
                        } catch (Exception e) {

                        }
                        if ((catalogueId > 0 && ingredientProductDetail.getProduct().getProductId() == catalogueId)
                                || ingredientProductDetail.getProduct().getName()
                                .equalsIgnoreCase(addOnVariants.getCatalogueName().trim())) {
                            orderItemComposition.getAddons().add(ingredientProductDetail);
                        }
                    }
                }
            }
        }
    }

    private static void setRecipePaidAddons(boolean superCombo, ZomatoOrderDishesV3 item, OrderItemComposition orderItemComposition, ProductPrice productPrice,
                                            Map<String, TaxDataVO> taxMap, Map<Integer, Product> products, List<OrderItem> paidAddonItems, Map<Integer, Product> cafeProducts, Product product) {
        List<ProductPrice> matchedPriceObj = cafeProducts.get(product.getId()).getPrices().stream().filter(price -> price.getDimension().equals(productPrice.getDimension())).collect(Collectors.toList());
        if (!superCombo && !matchedPriceObj.isEmpty() && Objects.nonNull(matchedPriceObj.get(0).getRecipe().getOptions())) {
            for (ZomatoOrderModifierGroupsV3 addOnsItem : item.getComposition().getModifierGroups()) {
                for (ZomatoOrderVariantsV3 addOnVariants : addOnsItem.getVariants()) {
                    for (OptionData optionData : matchedPriceObj.get(0).getRecipe().getOptions()) {
                        int catalogueId = 0;
                        try {
                            catalogueId = Integer.parseInt(addOnVariants.getCatalogueId());
                        } catch(Exception e) {
                            LOG.error("error parsing paid addon product id:" + addOnVariants.getCatalogueId(), e);
                        }
                        if ((catalogueId > 0 && optionData.getId() == catalogueId)
                                || optionData.getName().equalsIgnoreCase(addOnVariants.getCatalogueName().trim())) {

                            Product addonProduct = products.get(catalogueId);

                            if(Objects.nonNull(addonProduct) && Objects.nonNull(addonProduct.getPrices()) && !addonProduct.getPrices().isEmpty()) {
                                orderItemComposition.getOptions().add(optionData.getName());
                                OrderItem orderItem = new OrderItem();
                                orderItem.setProductId(addonProduct.getId());
                                orderItem.setProductName(addonProduct.getName());
                                orderItem.setProductCategory(new IdCodeName(addonProduct.getType(), "", ""));
                                orderItem.setProductSubCategory(new IdCodeName(addonProduct.getSubType(), "", ""));
                                orderItem.setQuantity(item.getQuantity());
                                BigDecimal unitCost = BigDecimal.valueOf(Float.parseFloat(addOnVariants.getUnitCost()));
                                orderItem.setPrice(unitCost);
                                orderItem.setTotalAmount(AppUtils.multiply(unitCost, BigDecimal.valueOf(item.getQuantity())));
                                ComplimentaryDetail complimentaryDetail = new ComplimentaryDetail();
                                complimentaryDetail.setIsComplimentary(false);
                                orderItem.setComplimentaryDetail(complimentaryDetail);
                                orderItem.setBillType(addonProduct.getBillType());
                                orderItem.setCode(addonProduct.getTaxCode());
                                orderItem.setBookedWastage(false);
                                orderItem.setTakeAway(false);
                                orderItem.setTaxDeductedByPartner(TaxPayingEntity.PARTNER.getZomato().equalsIgnoreCase(item.getTaxes().get(0).getType()));
                                // orderItem.setPartnerTaxType(item.getTaxes().get(0).getType());
                                ProductPrice addonProductPrice = addonProduct.getPrices().get(0);
                                orderItem.setOriginalPrice(addonProductPrice.getPrice());
                                orderItem.setDimension(addonProductPrice.getDimension());
                                RecipeDetail recipe = addonProductPrice.getRecipe();
                                orderItem.setRecipeId(recipe.getRecipeId());
                                // orderItem.setTotalAmount(BigDecimal.valueOf(item.getTotalCost()));
                                DiscountDetail discountDetail = new DiscountDetail();
                                discountDetail.setTotalDiscount(BigDecimal.ZERO);
                                discountDetail.setPromotionalOffer(BigDecimal.ZERO);
                                PercentageDetail pDetail = new PercentageDetail();
                                pDetail.setPercentage(BigDecimal.ZERO);
                                pDetail.setValue(BigDecimal.ZERO);
                                discountDetail.setDiscount(pDetail);
                                discountDetail.setPromotionalOffer(BigDecimal.ZERO);
                                orderItem.setDiscountDetail(discountDetail);
                                orderItem.setAmount(AppUtils.subtract(orderItem.getTotalAmount(), discountDetail.getTotalDiscount()));
                                AbstractConverters.setOrderItemTaxes(addonProduct, orderItem, taxMap, discountDetail, item.getQuantity());
                                paidAddonItems.add(orderItem);
                            }

                        }
                    }
                }
            }
        }
    }

    private static void setRecipeIngredientProducts(boolean superCombo, ZomatoOrderDishesV3 item, OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        if (!superCombo) {
            for (ZomatoOrderCompositionPropV3 itemVariants : item.getComposition().getProperties()) {
                recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
                    for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                        if (ingredientProductDetail.getProduct().getName()
                                .equalsIgnoreCase(itemVariants.getPropertyValue().trim())) {
                            orderItemComposition.getProducts().add(ingredientProductDetail);
                        }
                    }
                });
            }
        }
    }

    private static void setMissingRecipeIngredientVariants(ZomatoOrderDishesV3 item, OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        recipeDetail.getIngredient().getVariants().forEach(ingredientVariant -> {
            boolean found = false;
            for (IngredientVariantDetail ingredientVariantDetail : ingredientVariant.getDetails()) {
                for (ZomatoOrderCompositionPropV3 itemVariants : item.getComposition().getProperties()) {
                    if (ingredientVariantDetail.getAlias().equalsIgnoreCase(itemVariants.getPropertyValue().trim())) {
                        found = true;
                        break;
                    }
                }
            }
            if (!found) {
                ingredientVariant.getDetails().forEach(ingredientVariantDetail -> {
                    if (ingredientVariantDetail.isDefaultSetting()) {
                        orderItemComposition.getVariants().add(ingredientVariantDetail);
                    }
                });
            }
        });
    }

    private static void setMissingRecipeIngredientProducts(ZomatoOrderDishesV3 item, OrderItemComposition orderItemComposition, RecipeDetail recipeDetail) {
        try {
            recipeDetail.getIngredient().getProducts().forEach(ingredientProduct -> {
                if (Objects.nonNull(ingredientProduct) && ingredientProduct.getDetails().isEmpty() && ingredientProduct.getDetails().size() > 0) {
                    boolean found = false;
                    for (IngredientProductDetail ingredientProductDetail : ingredientProduct.getDetails()) {
                        for (ZomatoOrderCompositionPropV3 itemVariants : item.getComposition().getProperties()) {
                            if (ingredientProductDetail.getProduct().getName()
                                    .equalsIgnoreCase(itemVariants.getPropertyValue().trim())) {
                                found = true;
                                break;
                            }
                        }
                    }
                    if (!found) {
                        ingredientProduct.getDetails().forEach(ingredientProductDetail -> {
                            if (ingredientProductDetail.isDefaultSetting()) {
                                orderItemComposition.getProducts().add(ingredientProductDetail);
                            }
                        });
                    }
                }
            });
        } catch (Exception e) {
            LOG.error("RecipeDetail does not contain any products :{}", e.toString());
        }
        ;
    }

    private static BigDecimal getRecommendedPriceAndDiscountExtractionV3(ZomatoOrderDishesV3 item, List<ZomatoOrderDishesV3> newOrderDishesList, AtomicBoolean isParentProductRecommended, DiscountDetail discountDetail) {
        BigDecimal recommendedPrice = BigDecimal.ZERO;
        for (ZomatoOrderModifierGroupsV3 addOnsItem : item.getComposition().getModifierGroups()) {
            for (ZomatoOrderVariantsV3 addOnVariants : addOnsItem.getVariants()) {
                if (addOnVariants.getCatalogueId() != null) {
                    String[] addonIds = addOnVariants.getCatalogueId().split("_");
                    String recommendationConstant = ChannelPartnerServiceConstants.RECOMMENDATION_ITEM_IDENTIFIER.substring(1);
                    Boolean isRecommendedAddon = Arrays.asList(addonIds).contains(recommendationConstant);
                    if (Boolean.TRUE.equals(isRecommendedAddon)) {
                        isParentProductRecommended.set(true);
                        ZomatoOrderDishesV3 newProduct = createOrderDishV3(addOnVariants, item.getQuantity(), item.getTaxes().get(0).getType(),item);
                        //calculate recom disount percentage and distribute evenly to item
                        calculateRecomDiscountPercent(item, discountDetail, newProduct);
                        newProduct.setTotalDiscount(discountDetail.getTotalDiscount().floatValue());
                        newOrderDishesList.add(newProduct);
                        recommendedPrice = ChannelPartnerUtils.add(recommendedPrice, new BigDecimal(addOnVariants.getUnitCost()));
                    }
                }
            }
        }
        if (recommendedPrice.compareTo(BigDecimal.ZERO) > 0) {
            recommendedPrice = ChannelPartnerUtils.subtract(new BigDecimal(item.getComposition().getUnitCost()), recommendedPrice).setScale(2, RoundingMode.HALF_UP);
        } else {
            recommendedPrice = new BigDecimal(item.getComposition().getUnitCost()).setScale(2, RoundingMode.HALF_UP);
        }
        return recommendedPrice;
    }

    private static void calculateRecomDiscountPercent(ZomatoOrderDishesV3 item, DiscountDetail discountDetail, ZomatoOrderDishesV3 newProduct) {
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(AppUtils.percentageWithScale10(BigDecimal.valueOf(item.getTotalDiscount()), BigDecimal.valueOf(item.getTotalCost())));
        percentageDetail.setValue(ChannelPartnerUtils.percentageOfWithScale10(percentageDetail.getPercentage(), BigDecimal.valueOf(newProduct.getTotalCost())));
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        discountDetail.setTotalDiscount(ChannelPartnerUtils.add(discountDetail.getPromotionalOffer(), discountDetail.getDiscount().getValue()));
        LOG.info("Recom Discount Percentage Detail :::::::{}", new Gson().toJson(discountDetail));
    }


    private static ZomatoOrderDishesV3 createOrderDishV3(ZomatoOrderVariantsV3 addOnVariants, Integer quantity, String taxType, ZomatoOrderDishesV3 item) {
        ZomatoOrderDishesV3 dishes = new ZomatoOrderDishesV3();
        dishes.setDishType("variant");
        String[] data = addOnVariants.getCatalogueId().split("_");
        dishes.setQuantity(quantity);
        String catalogueId = data[0];
        String propertyValue = data[1];
        ZomatoOrderCompositionV3 dishComposition = new ZomatoOrderCompositionV3();
        dishComposition.setVariantId(addOnVariants.getVariantId());
        dishComposition.setUnitCost(Float.valueOf(addOnVariants.getUnitCost()));
        dishComposition.setCatalogueId(catalogueId);
        dishComposition.setCatalogueName(addOnVariants.getCatalogueName());
        List<ZomatoOrderCompositionPropV3> propertyGroupList = new ArrayList<>();
        ZomatoOrderCompositionPropV3 propertyGroup = new ZomatoOrderCompositionPropV3();
        propertyGroup.setPropertyName("Size");
        propertyGroup.setPropertyValue(propertyValue);
        propertyGroupList.add(propertyGroup);
        dishComposition.setProperties(propertyGroupList);
        dishes.setDishDiscounts(new ArrayList<>());
        dishComposition.setModifierGroups(new ArrayList<>());
        // Adding Charges
        List<ZomatoOrderDishChargesV3> dishCharges = new ArrayList<>();
        dishes.setItemCharges(dishCharges);
        // Adding taxes
        List<ZomatoOrderTaxDetailsV3> taxes = new ArrayList<>();
        ZomatoOrderTaxDetailsV3 tax = new ZomatoOrderTaxDetailsV3();
        tax.setType(taxType);
        taxes.add(tax);
        dishes.setTaxes(taxes);
        dishes.setComposition(dishComposition);
        dishes.setQuantity(item.getQuantity());
        dishes.setTotalCost(ChannelPartnerUtils.multiply(dishComposition.getUnitCost(), Float.valueOf(dishes.getQuantity())));
        return dishes;
    }

    private static ZomatoOrderDishesV3 createItemOrder(Integer quantity, ZomatoOrderDishesV3 item, String catalogueId) {
        ZomatoOrderDishesV3 dishes = new ZomatoOrderDishesV3();
        dishes.setQuantity(quantity);
        dishes.setTotalCost(BigDecimal.ZERO.floatValue());
        List<ZomatoOrderModifierGroupsV3> modifierGroup = new ArrayList<>();
        for (ZomatoOrderModifierGroupsV3 modGroup : item.getComposition().getModifierGroups()) {
            if (catalogueId.equalsIgnoreCase("10")) {
                List<ZomatoOrderVariantsV3> modVar = new ArrayList<>();
                modVar.add(modGroup.getVariants().get(0));
                ZomatoOrderModifierGroupsV3 modifierGrp = new ZomatoOrderModifierGroupsV3();
                modifierGrp.setVariants(modVar);
                 modifierGroup.add(modifierGrp);
            }
        }
        ZomatoOrderCompositionV3 dishComposition = new ZomatoOrderCompositionV3();
        dishComposition.setModifierGroups(modifierGroup);
        List<ZomatoOrderCompositionPropV3> propertyGroup = new ArrayList<>();
        for (ZomatoOrderCompositionPropV3 prop : item.getComposition().getProperties()) {
            if (prop.getPropertyValueId().startsWith(catalogueId)) {
                propertyGroup.add(prop);
            }
        }
        dishComposition.setUnitCost(BigDecimal.ZERO.floatValue());
        dishComposition.setProperties(propertyGroup);
        dishes.setComposition(dishComposition);
        ZomatoOrderTaxDetailsV3 tax = new ZomatoOrderTaxDetailsV3();
        tax.setType(item.getTaxes().get(0).getType());
        dishes.getTaxes().add(tax);
        return dishes;
    }

    private static ZomatoOrderDishesV3 createItemOrderOfSuperComboV3(int quantity,
                                                                     ZomatoOrderCompSelectionEntityV3 selectionEntity, String taxType) {
        ZomatoOrderDishesV3 dishes = new ZomatoOrderDishesV3();
        /*if (selectionEntity.getCatalogueId().contains("_")) {
            String[] id = selectionEntity.getCatalogueId().split("_");
            dishes.setQuantity(Integer.parseInt(id[1]));
        } else {
            dishes.setQuantity(1);
        }*/
        dishes.setQuantity(quantity);
        dishes.setTotalCost(BigDecimal.ZERO.floatValue());
        List<ZomatoOrderModifierGroupsV3> modifierGroup = new ArrayList<>();
        ZomatoOrderCompositionV3 dishComposition = new ZomatoOrderCompositionV3();
        dishComposition.setModifierGroups(modifierGroup);
        dishComposition.setUnitCost(selectionEntity.getUnitCost());
        dishComposition.setProperties(selectionEntity.getProperties());
        dishes.setComposition(dishComposition);
        ZomatoOrderTaxDetailsV3 tax = new ZomatoOrderTaxDetailsV3();
        tax.setType(taxType);
        dishes.getTaxes().add(tax);
        return dishes;

    }

}
