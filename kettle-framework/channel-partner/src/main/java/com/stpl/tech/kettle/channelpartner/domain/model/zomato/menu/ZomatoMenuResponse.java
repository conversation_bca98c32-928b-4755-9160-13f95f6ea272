package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "menu_response",
        "request_id"
})
public class ZomatoMenuResponse {

    @JsonProperty("menu_response")
    private MenuResponse menuResponse;
    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("menu_response")
    public MenuResponse getMenuResponse() {
        return menuResponse;
    }

    @JsonProperty("menu_response")
    public void setMenuResponse(MenuResponse menuResponse) {
        this.menuResponse = menuResponse;
    }


    @JsonProperty("request_id")
    public String getRequestId() {
        return requestId;
    }

    @JsonProperty("request_id")
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
}
