package com.stpl.tech.kettle.channelpartner.core.service;

import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.channelpartner.core.exceptions.ChannelPartnerException;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerPendingOrderRequest;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOrderDetail;
import com.stpl.tech.kettle.domain.model.PartnerOrderStateUpdate;

public interface PartnerOrderService {

    List<PartnerOrderDetail> getPartnerOrder(PartnerOrderDetail partnerOrderDetail) throws ChannelPartnerException;

    PartnerOrderDetail getPartnerOrder(String kettleOrderId) throws ChannelPartnerException;

    Map<String, List<PartnerOrderDetail>> getPartnerPendingOrders(PartnerPendingOrderRequest request) throws ChannelPartnerException;

    List<PartnerOrderDetail> getOrdersByTime(Date startTime, Date endTime);

    List<PartnerOrderDetail> getPartnerOrder(String partnerId, String orderStatus) throws ChannelPartnerException;

    boolean callSwiggyPartnerSupport(String orderId) throws ChannelPartnerException, URISyntaxException;

    boolean markOrderResolved(String orderId) throws ChannelPartnerException;

    boolean manualProcess(String orderId) throws ChannelPartnerException, URISyntaxException;

    boolean markCancelled(String orderId) throws ChannelPartnerException, URISyntaxException;

    boolean manualProcessWithSkipInventory(String orderId) throws ChannelPartnerException, URISyntaxException;

    void sendOrderNotification(PartnerOrderDetail order);

    void sendOrderStatusUpdate(PartnerOrderStateUpdate request);

    Integer getSwiggyRiderTimeOfArrival(String orderId) throws ChannelPartnerException;

    void sendOrderNotPunchedNotification(PartnerOrderDetail detail);

    void sendOrderNotPunchedNotificationToKnock(PartnerOrderDetail detail);

    boolean fallbackProcessedBy(String orderId, String fallbackProcessedBy) throws ChannelPartnerException;

    void logStockRefreshEvent(Integer unitId , Integer partnerId , Integer brandId,String logType, String status);

    public boolean addKettleOrderId(String kettleOrderId,String partnerOrderId , Integer unitId);
}
