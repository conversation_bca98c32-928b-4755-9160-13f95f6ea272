
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "day_of_week",
    "open_time",
    "close_time"
})
public class ItemSlot {

    @JsonProperty("day_of_week")
    private Integer dayOfWeek;
    @JsonProperty("open_time")
    private Integer openTime;
    @JsonProperty("close_time")
    private Integer closeTime;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("day_of_week")
    public Integer getDayOfWeek() {
        return dayOfWeek;
    }

    @JsonProperty("day_of_week")
    public void setDayOfWeek(Integer dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    @JsonProperty("open_time")
    public Integer getOpenTime() {
        return openTime;
    }

    @JsonProperty("open_time")
    public void setOpenTime(Integer openTime) {
        this.openTime = openTime;
    }

    @JsonProperty("close_time")
    public Integer getCloseTime() {
        return closeTime;
    }

    @JsonProperty("close_time")
    public void setCloseTime(Integer closeTime) {
        this.closeTime = closeTime;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("dayOfWeek", dayOfWeek).append("openTime", openTime).append("closeTime", closeTime).append("additionalProperties", additionalProperties).toString();
    }

}
