package com.stpl.tech.kettle.channelpartner.core.service.impl;

import com.stpl.tech.kettle.channelpartner.core.service.CafeStatusChannelPartnerService;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeRaiseRequestApprovalDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.dao.CafeStatusChannelPartnerDao;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeRaiseRequestApproval;
import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;



@Service
public class CafeStatusChannelPartnerServiceImpl implements CafeStatusChannelPartnerService {

    @Autowired
    private CafeStatusChannelPartnerDao cafeStatusChannelPartnerDao;

    @Autowired
    private CafeRaiseRequestApprovalDao cafeRaiseRequestApprovalDao;

    private static final Logger LOG = LoggerFactory.getLogger(CafeStatusChannelPartnerServiceImpl.class);

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CafeStatusChannelPartner> getInActiveCafe(String statusUpdate){
      return  cafeStatusChannelPartnerDao.getInActiveCafe(statusUpdate);
      //return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CafeRaiseRequestApproval> getUnitSwitchOffRequests(String actionTaken){
        return cafeRaiseRequestApprovalDao.getAllPendingCafeSwitchOffRequests(actionTaken);

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatedRequestForUnit(CafeRaiseRequestApproval request){

        try {
            return cafeRaiseRequestApprovalDao.updateActionTakenOnRequest(request.getId(), AppConstants.COMPLETED_REQUEST);
        } catch (Exception e){
            LOG.info("Error while updating  updatedRequestForUnit request",e);
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveCafeStatus(CafeStatusChannelPartner cafeStatusChannelPartner){
        cafeStatusChannelPartnerDao.add(cafeStatusChannelPartner);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CPDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CafeStatusChannelPartner deleteActivatedCafe(Integer id){
        CafeStatusChannelPartner obj =cafeStatusChannelPartnerDao.find(CafeStatusChannelPartner.class,id);
        cafeStatusChannelPartnerDao.delete(obj);
        return  obj;
    }



}
