package com.stpl.tech.kettle.channelpartner.controller;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.service.CafeLookUpService;
import com.stpl.tech.kettle.channelpartner.core.service.CommissionService;
import com.stpl.tech.kettle.channelpartner.core.service.EnvironmentProperties;
import com.stpl.tech.kettle.channelpartner.core.service.ZomatoService;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerAOVRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MACRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.MACRespose;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoDeliveryStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoMenuRequestStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderRiderDataV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderRiderMaskV3;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.ZomatoOrderStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCafeStatusRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoRatingRequest;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderResponse;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.zomato.order.ZomatoOrderV3;
import com.stpl.tech.master.domain.model.BusinessHourEvent;
import com.stpl.tech.master.domain.model.BusinessHourObject;
import com.stpl.tech.master.domain.model.CafeTimingChangeRequest;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nullable;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.text.ParseException;
import java.util.Objects;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.ZOMATO_ROOT_CONTEXT;

@Log4j2
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + ZOMATO_ROOT_CONTEXT) // 'v1/zomato'
public class ZomatoResources extends ZomatoOrderAbstractResource {

    private static final Logger LOG = LoggerFactory.getLogger(ZomatoResources.class);

    @Autowired
    private ZomatoService zomatoService;

    @Autowired
    private CafeLookUpService cafeLookUpService;

    @Autowired
    private CommissionService commissionService;

    @Autowired
    private EnvironmentProperties properties;


    @RequestMapping(method = RequestMethod.POST, value = "order/add", produces = MediaType.APPLICATION_JSON)
    public ZomatoOrderResponse addOrder(@RequestBody Object request) {
        String requestJson = new Gson().toJson(request);
        LOG.info("Request to add zomato order : {}", requestJson);
        String externalOrderId = null;
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            ZomatoOrderV3 order = mapper.readValue(requestJson, ZomatoOrderV3.class);
            ZomatoOrderResponse orderResponse =  addZomatoOrder(order.getOrder()); // MOVED THIS CODE TO AN ABSTRACT CLASS TO AVOID CODE REDUNDANCY(@MOHIT_MALIK)
            /*externalOrderId = order.getOrder().getOrderId().toString();
            //return zomatoService.addZomatoOrderV3(order.getOrder(), false);
            ZomatoOrderResponse response =  zomatoService.addZomatoOrderV3(order.getOrder(), false);
            String responseJson = new Gson().toJson(response);
            LOG.info("Request to add zomato order {}: {}",order.getOrder().getOrderId(), responseJson);*/
            return orderResponse;
        } catch (IOException e) {
            LOG.error("error parsing zomato order request:", e);
        }
        /*if(isV3) {

        } else {
            try {
                ZomatoOrder order = mapper.readValue(data, ZomatoOrder.class);
                externalOrderId = order.getOrder().getOrderId().toString();
                return zomatoService.addZomatoOrder(order.getOrder(), false);
            } catch (IOException ex) {
                LOG.error("error parsing zomato order request:", ex);
            }
        }*/
        ZomatoOrderResponse response = new ZomatoOrderResponse();
        response.setCode(500);
        response.setExternalOrderId(externalOrderId);
        response.setMessage("Failed");
        response.setStatus(ZomatoOrderStatus.FAILED);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/addV3", produces = MediaType.APPLICATION_JSON)
    public ZomatoOrderResponse addOrderV3(@RequestBody ZomatoOrderV3 request) {
        String requestJson = new Gson().toJson(request);
        LOG.info("Request to add zomato order {}: {}",request.getOrder().getOrderId(), requestJson);
        ZomatoOrderResponse response =  zomatoService.addZomatoOrderV3(request.getOrder(), false);
        String responseJson = new Gson().toJson(response);
        LOG.info("Request to add zomato order {}: {}",request.getOrder().getOrderId(), responseJson);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/rider/status", produces = MediaType.APPLICATION_JSON)
    public void updateOrderDeliveryStatus(@RequestBody ZomatoDeliveryStatusRequest request) {
        String requestJson = new Gson().toJson(request);
        LOG.info("Request to update delivery status of Zomato order : {}", requestJson);
        zomatoService.updateZomatoOrderDeliveryStatus(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/status/update", produces = MediaType.APPLICATION_JSON)
    public void updateOrderStatus(@RequestBody ZomatoOrderStatusRequest request) {
        String requestJson = new Gson().toJson(request);
        LOG.info("Request to update zomato order status : {}", requestJson);
        zomatoService.updateZomatoOrderStatus(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/menu/status", produces = MediaType.APPLICATION_JSON)
    public void updateMenuStatus(@RequestBody ZomatoMenuRequestStatus request) {
        String requestJson = new Gson().toJson(request);
        LOG.info("Request of updated menu status : {}", requestJson);
        zomatoService.updateMenuStatus(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/rider/rider_details", produces = MediaType.APPLICATION_JSON)
    public boolean updateRiderDetails(@RequestBody ZomatoOrderRiderDataV3 request) {
        String requestJson = new Gson().toJson(request);
        LOG.info("Request of updated rider details status : {}", requestJson);
        return zomatoService.updateRiderDetails(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/rider/rider_mask", consumes = MediaType.APPLICATION_JSON,
        produces = MediaType.APPLICATION_JSON)
    public boolean updateRiderMask(@RequestBody ZomatoOrderRiderMaskV3 request) {
        String requestJson = new Gson().toJson(request);
        LOG.info("Request of updated mask status : {}", requestJson);
        return zomatoService.updateRiderMask(request);
    }

    // this is the new zomato push api to set cafe status
    @RequestMapping(method = RequestMethod.POST, value = "order/zomato-cafe-status", produces = MediaType.APPLICATION_JSON)
    public Boolean setZomatoCafeStatusData(@RequestBody ZomatoCafeStatusRequest request) throws ParseException, IOException {
        LOG.info("Request to change zomato cafe status on unit {} : {}", request.getOutletId(), new Gson().toJson(request));
        return cafeLookUpService.setZomatoCafeStatusData(request);
    }

    //MAC will creat here.
    @RequestMapping(method = RequestMethod.POST, value = "/order/macrelay", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public MACRespose macRelay(@RequestBody MACRequest request) {
        LOG.info("Merchant Agreed Cancellation was initiated by the  customer " + new Gson().toJson(request));
        return zomatoService.macRelay(request);
    }

    //Updating Cafe Delivery Time On Zomato
    @RequestMapping(method = RequestMethod.POST, value = "update/zomato/delivery/timing", produces = MediaType.APPLICATION_JSON)
    public void updateCafeDeliveryTimingOnZomato(@RequestBody CafeTimingChangeRequest cafeTimingChangeRequest){
        LOG.info("Changing Cafe Delivery Time");
        zomatoService.updateCafeDeliveryTimeZomato(cafeTimingChangeRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get/zomato/order/ratings", produces = MediaType.APPLICATION_JSON)
    public  boolean getZomatoOrderRatings(@RequestBody ZomatoRatingRequest zomatoRatingRequest){
         LOG.info("Get Zomato Order Ratings");
             return zomatoService.getZomatoOutletOrderRating(zomatoRatingRequest);
    }

    @Scheduled(cron = "0 0 6 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "upload/zomato/order/ratings", produces = MediaType.APPLICATION_JSON)
    public void uploadZomatoOrderRatings() {
        if(AppUtils.isProd(properties.getEnvType())) {
            LOG.info("Get Zomato Order Ratings");
            zomatoService.uploadZomatoOrderRating();
        }
    }

    @Scheduled(cron = "0 0 0 1 * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "calculate/monthly/aov", produces = MediaType.APPLICATION_JSON)
    public void calculateAOV() {
        try {
            commissionService.calculateMonthWiseAOV(null);
        } catch (Exception e) {
            LOG.error("Exception got while monthly aov calculation ::", e);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "calculate/aov", produces = MediaType.APPLICATION_JSON)
    public void calculateAOV(@RequestBody(required = false) PartnerAOVRequest partnerAOVRequest){
        try{
            commissionService.calculateMonthWiseAOV(partnerAOVRequest);
        }catch(Exception e ){
            LOG.error("Exception got while monthly aov calculation ::", e);
        }
    }

    @PostMapping(value = "update/zomato/cafe/timings",produces = MediaType.APPLICATION_JSON)
    public void updateZomatoCafeTimings(@Nullable @RequestBody Integer outletId){
        if(Objects.nonNull(outletId)) {
            log.info("Updating cafe timings for zomato for unitid", outletId);
            zomatoService.updateZomatoCafeTimings(outletId, BusinessHourEvent.ENABLE_BUSINESS_HOURS.value(), null);
        }
        else{
            zomatoService.updateAllZomatoCafeTimings();
        }
    }

    @PostMapping(value = "update/zomato/business/hour",produces = MediaType.APPLICATION_JSON)
    public void updateZomatoBusinessHours(@RequestBody BusinessHourObject businessHourObject){
        zomatoService.updateZomatoCafeTimings(businessHourObject.getOutletId(), businessHourObject.getEvent(),businessHourObject.getOldBusinessHours());
    }

}
