
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "schedule_name",
    "schedule_day",
    "schedule_time_slots"
})
public class CategorySchedule {

    @JsonProperty("schedule_name")
    private String scheduleName;
    @JsonProperty("schedule_day")
    private List<Integer> scheduleDay = null;
    @JsonProperty("schedule_time_slots")
    private List<ScheduleTimeSlot> scheduleTimeSlots = null;

    @JsonProperty(" schedule_name")
    public String getScheduleName() {
        return scheduleName;
    }

    @JsonProperty(" schedule_name")
    public void setScheduleName(String scheduleName) {
        this.scheduleName = scheduleName;
    }

    @JsonProperty("schedule_day")
    public List<Integer> getScheduleDay() {
        return scheduleDay;
    }

    @JsonProperty("schedule_day")
    public void setScheduleDay(List<Integer> scheduleDay) {
        this.scheduleDay = scheduleDay;
    }

    @JsonProperty("schedule_time_slots")
    public List<ScheduleTimeSlot> getScheduleTimeSlots() {
        return scheduleTimeSlots;
    }

    @JsonProperty("schedule_time_slots")
    public void setScheduleTimeSlots(List<ScheduleTimeSlot> scheduleTimeSlots) {
        this.scheduleTimeSlots = scheduleTimeSlots;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("scheduleName", scheduleName).append("scheduleDay", scheduleDay).append("scheduleTimeSlots", scheduleTimeSlots).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(scheduleName).append(scheduleDay).append(scheduleTimeSlots).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof CategorySchedule) == false) {
            return false;
        }
        CategorySchedule rhs = ((CategorySchedule) other);
        return new EqualsBuilder().append(scheduleName, rhs.scheduleName).append(scheduleDay, rhs.scheduleDay).append(scheduleTimeSlots, rhs.scheduleTimeSlots).isEquals();
    }

}
