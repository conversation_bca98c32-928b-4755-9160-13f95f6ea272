
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "addons",
    "cgst",
    "cgst_percent",
    "id",
    "igst",
    "igst_percent",
    "name",
    "packing_charges",
    "price",
    "quantity",
    "reward_type",
    "sgst",
    "sgst_percent",
    "subtotal",
    "variants"
})
public class Item {

    @JsonProperty("addons")
    private List<Addon> addons = null;
    @JsonProperty("cgst")
    private float cgst;
    @JsonProperty("cgst_percent")
    private float cgstPercent;
    @JsonProperty("id")
    private String id;
    @JsonProperty("igst")
    private float igst;
    @JsonProperty("igst_percent")
    private float igstPercent;
    @JsonProperty("name")
    private String name;
    @JsonProperty("packing_charges")
    private float packingCharges;
    @JsonProperty("price")
    private float price;
    @JsonProperty("quantity")
    private int quantity;
    @JsonProperty("reward_type")
    private Object rewardType;
    @JsonProperty("sgst")
    private float sgst;
    @JsonProperty("sgst_percent")
    private float sgstPercent;
    @JsonProperty("subtotal")
    private float subtotal;
    @JsonProperty("variants")
    private List<Variant> variants = null;

    @JsonProperty("addons")
    public List<Addon> getAddons() {
        return addons;
    }

    @JsonProperty("addons")
    public void setAddons(List<Addon> addons) {
        this.addons = addons;
    }

    @JsonProperty("cgst")
    public float getCgst() {
        return cgst;
    }

    @JsonProperty("cgst")
    public void setCgst(float cgst) {
        this.cgst = cgst;
    }

    @JsonProperty("cgst_percent")
    public float getCgstPercent() {
        return cgstPercent;
    }

    @JsonProperty("cgst_percent")
    public void setCgstPercent(float cgstPercent) {
        this.cgstPercent = cgstPercent;
    }

    @JsonProperty("id")
    public String getId() {
        return id;
    }

    @JsonProperty("id")
    public void setId(String id) {
        this.id = id;
    }

    @JsonProperty("igst")
    public float getIgst() {
        return igst;
    }

    @JsonProperty("igst")
    public void setIgst(float igst) {
        this.igst = igst;
    }

    @JsonProperty("igst_percent")
    public float getIgstPercent() {
        return igstPercent;
    }

    @JsonProperty("igst_percent")
    public void setIgstPercent(float igstPercent) {
        this.igstPercent = igstPercent;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("packing_charges")
    public float getPackingCharges() {
        return packingCharges;
    }

    @JsonProperty("packing_charges")
    public void setPackingCharges(float packingCharges) {
        this.packingCharges = packingCharges;
    }

    @JsonProperty("price")
    public float getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(float price) {
        this.price = price;
    }

    @JsonProperty("quantity")
    public int getQuantity() {
        return quantity;
    }

    @JsonProperty("quantity")
    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    @JsonProperty("reward_type")
    public Object getRewardType() {
        return rewardType;
    }

    @JsonProperty("reward_type")
    public void setRewardType(Object rewardType) {
        this.rewardType = rewardType;
    }

    @JsonProperty("sgst")
    public float getSgst() {
        return sgst;
    }

    @JsonProperty("sgst")
    public void setSgst(float sgst) {
        this.sgst = sgst;
    }

    @JsonProperty("sgst_percent")
    public float getSgstPercent() {
        return sgstPercent;
    }

    @JsonProperty("sgst_percent")
    public void setSgstPercent(float sgstPercent) {
        this.sgstPercent = sgstPercent;
    }

    @JsonProperty("subtotal")
    public float getSubtotal() {
        return subtotal;
    }

    @JsonProperty("subtotal")
    public void setSubtotal(float subtotal) {
        this.subtotal = subtotal;
    }

    @JsonProperty("variants")
    public List<Variant> getVariants() {
        return variants;
    }

    @JsonProperty("variants")
    public void setVariants(List<Variant> variants) {
        this.variants = variants;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("addons", addons).append("cgst", cgst).append("cgstPercent", cgstPercent).append("id", id).append("igst", igst).append("igstPercent", igstPercent).append("name", name).append("packingCharges", packingCharges).append("price", price).append("quantity", quantity).append("rewardType", rewardType).append("sgst", sgst).append("sgstPercent", sgstPercent).append("subtotal", subtotal).append("variants", variants).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(sgstPercent).append(addons).append(subtotal).append(sgst).append(rewardType).append(packingCharges).append(id).append(price).append(cgstPercent).append(name).append(igst).append(variants).append(quantity).append(cgst).append(igstPercent).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof Item) == false) {
            return false;
        }
        Item rhs = ((Item) other);
        return new EqualsBuilder().append(sgstPercent, rhs.sgstPercent).append(addons, rhs.addons).append(subtotal, rhs.subtotal).append(sgst, rhs.sgst).append(rewardType, rhs.rewardType).append(packingCharges, rhs.packingCharges).append(id, rhs.id).append(price, rhs.price).append(cgstPercent, rhs.cgstPercent).append(name, rhs.name).append(igst, rhs.igst).append(variants, rhs.variants).append(quantity, rhs.quantity).append(cgst, rhs.cgst).append(igstPercent, rhs.igstPercent).isEquals();
    }

}
