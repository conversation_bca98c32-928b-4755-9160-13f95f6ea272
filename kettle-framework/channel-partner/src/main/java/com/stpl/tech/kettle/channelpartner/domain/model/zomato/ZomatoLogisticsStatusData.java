package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

public class ZomatoLogisticsStatusData {

    @JsonProperty("zomato_logistics_enabled")
    private Boolean zomatoLogisticsEnabled;
    @JsonProperty("zomato_logistics_serviceability_status")
    private Boolean zomatoLogisticsServiceabilityStatus;
    @JsonProperty("self_delivery_enabled")
    private Boolean selfDeliveryEnabled;
    @JsonProperty("self_delivery_serviceability_status")
    private Boolean selfDeliveryServiceabilityStatus;

    @JsonProperty("zomato_logistics_enabled")
    public Boolean getZomatoLogisticsEnabled() {
        return zomatoLogisticsEnabled;
    }

    @JsonProperty("zomato_logistics_enabled")
    public void setZomatoLogisticsEnabled(Boolean zomatoLogisticsEnabled) {
        this.zomatoLogisticsEnabled = zomatoLogisticsEnabled;
    }

    @JsonProperty("zomato_logistics_serviceability_status")
    public Boolean getZomatoLogisticsServiceabilityStatus() {
        return zomatoLogisticsServiceabilityStatus;
    }

    @JsonProperty("zomato_logistics_serviceability_status")
    public void setZomatoLogisticsServiceabilityStatus(Boolean zomatoLogisticsServiceabilityStatus) {
        this.zomatoLogisticsServiceabilityStatus = zomatoLogisticsServiceabilityStatus;
    }

    @JsonProperty("self_delivery_enabled")
    public Boolean getSelfDeliveryEnabled() {
        return selfDeliveryEnabled;
    }

    @JsonProperty("self_delivery_enabled")
    public void setSelfDeliveryEnabled(Boolean selfDeliveryEnabled) {
        this.selfDeliveryEnabled = selfDeliveryEnabled;
    }

    @JsonProperty("self_delivery_serviceability_status")
    public Boolean getSelfDeliveryServiceabilityStatus() {
        return selfDeliveryServiceabilityStatus;
    }

    @JsonProperty("self_delivery_serviceability_status")
    public void setSelfDeliveryServiceabilityStatus(Boolean selfDeliveryServiceabilityStatus) {
        this.selfDeliveryServiceabilityStatus = selfDeliveryServiceabilityStatus;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoLogisticsStatusData that = (ZomatoLogisticsStatusData) o;

        return new EqualsBuilder()
                .append(zomatoLogisticsEnabled, that.zomatoLogisticsEnabled)
                .append(zomatoLogisticsServiceabilityStatus, that.zomatoLogisticsServiceabilityStatus)
                .append(selfDeliveryEnabled, that.selfDeliveryEnabled)
                .append(selfDeliveryServiceabilityStatus, that.selfDeliveryServiceabilityStatus)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(zomatoLogisticsEnabled)
                .append(zomatoLogisticsServiceabilityStatus)
                .append(selfDeliveryEnabled)
                .append(selfDeliveryServiceabilityStatus)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "ZomatoLogisticsStatusData{" +
                "zomatoLogisticsEnabled=" + zomatoLogisticsEnabled +
                ", zomatoLogisticsServiceabilityStatus=" + zomatoLogisticsServiceabilityStatus +
                ", selfDeliveryEnabled=" + selfDeliveryEnabled +
                ", selfDeliveryServiceabilityStatus=" + selfDeliveryServiceabilityStatus +
                '}';
    }
}
