
package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
	"name",
	"order",
	"entities",
	"vendorEntityId"
})
public class ZomatoSubcategory {

	@JsonProperty("vendorEntityId")
    private String vendorEntityId;
    @JsonProperty("name")
    private String name;
    @JsonProperty("order")
    private Integer order;
    @JsonProperty("entities")
    private List<ZomatoEntities> entities = null;

    @JsonProperty("order")
	public Integer getOrder() {
		return order;
	}

	@JsonProperty("order")
	public void setOrder(Integer order) {
		this.order = order;
	}

    @JsonProperty("name")
    public String getName() {
		return name;
	}

    @JsonProperty("name")
	public void setName(String name) {
		this.name = name;
	}

    @JsonProperty("vendorEntityId")
	public String getVendorEntityId() {
		return vendorEntityId;
	}

    @JsonProperty("vendorEntityId")
	public void setVendorEntityId(String vendorEntityId) {
		this.vendorEntityId = vendorEntityId;
	}

    @JsonProperty("entities")
	public List<ZomatoEntities> getEntities() {
		return entities;
	}

    @JsonProperty("entities")
	public void setEntities(List<ZomatoEntities> entities) {
		this.entities = entities;
	}

	

	@Override
	public String toString() {
		return "ZomatoSubcategory [vendorEntityId=" + vendorEntityId + ", name=" + name
				+ ", order=" + order + ", entities=" + entities + "]";
	}

	@Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoSubcategory that = (ZomatoSubcategory) o;

        return new EqualsBuilder()
                .append(name, that.name)
                .append(order, that.order)
                .append(vendorEntityId, that.vendorEntityId)
                .append(entities, that.entities)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(name)
                .append(order)
                .append(vendorEntityId)
                .append(entities)
                .toHashCode();
    }
}
