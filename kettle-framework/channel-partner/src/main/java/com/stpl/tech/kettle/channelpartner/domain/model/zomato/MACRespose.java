package com.stpl.tech.kettle.channelpartner.domain.model.zomato;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "code"
        ,"status"
})
public class MACRespose {

    @JsonProperty("code")
    private  Integer code;

    @JsonProperty("status")
    private String status;

    public Integer getCode() {
        return code;
    }

    public String getStatus() {
        return status;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Override
    public String toString() {
        return "MACRespose{" +
                "code=" + code +
                ", status='" + status + '\'' +
                '}';
    }


}
