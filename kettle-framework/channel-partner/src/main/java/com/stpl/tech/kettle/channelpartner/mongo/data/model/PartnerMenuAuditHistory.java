package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlRootElement;

import com.stpl.tech.kettle.channelpartner.domain.model.MenuType;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@XmlRootElement(name = "PartnerMenuAuditHistory")
public class PartnerMenuAuditHistory {

	private String id;
	private String version;
	private String partnerName;
	private Integer partnerId;
	private Integer unitId;
	private Date addTime;
	private String addTimeIST;
	private Integer employeeId;
	private String employeeName;
	private Integer brandId;
	private String menuType;
	private String status;
	private Object menuResponse;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getPartnerName() {
		return partnerName;
	}

	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}

	public Integer getPartnerId() {
		return partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	public Integer getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(Integer employeeId) {
		this.employeeId = employeeId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public String getMenuType() {
		return menuType;
	}

	public void setMenuType(String menuType) {
		this.menuType = menuType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAddTimeIST() {
		return addTimeIST;
	}

	public void setAddTimeIST(String addTimeIST) {
		this.addTimeIST = addTimeIST;
	}

	public Object getMenuResponse() {
		return menuResponse;
	}

	public void setMenuResponse(Object menuResponse) {
		this.menuResponse = menuResponse;
	}
}
