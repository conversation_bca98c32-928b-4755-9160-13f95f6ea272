package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import java.util.List;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "propertyValues",
    "prices",
    "vendorEntityId",
    "modifierGroups"
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class ZomatoCatalogueVariant {

    @JsonProperty("propertyValues")
    private List<ZomatoCatVarPropertyValues> propertyValues = null;
    @JsonProperty("prices")
    private List<ZomatoCatVarPrice> prices = null;
    @JsonProperty("vendorEntityId")
    private String vendorEntityId;
    @JsonProperty("modifierGroups")
    private List<ZomatoCatVarModifierG> modifierGroups = null;
    @JsonProperty("inStock")
    private boolean inStock = true;

    @JsonProperty("propertyValues")
    public List<ZomatoCatVarPropertyValues> getPropertyValues() {
        return propertyValues;
    }

    @JsonProperty("propertyValues")
    public void setPropertyValues(List<ZomatoCatVarPropertyValues> propertyValues) {
        this.propertyValues = propertyValues;
    }

    @JsonProperty("prices")
    public List<ZomatoCatVarPrice> getPrices() {
        return prices;
    }

    @JsonProperty("prices")
    public void setPrices(List<ZomatoCatVarPrice> prices) {
        this.prices = prices;
    }

    @JsonProperty("vendorEntityId")
    public String getVendorEntityId() {
        return vendorEntityId;
    }

    @JsonProperty("vendorEntityId")
    public void setVendorEntityId(String vendorEntityId) {
        this.vendorEntityId = vendorEntityId;
    }

    @JsonProperty("modifierGroups")
    public List<ZomatoCatVarModifierG> getModifierGroups() {
        return modifierGroups;
    }

    @JsonProperty("modifierGroups")
    public void setModifierGroups(List<ZomatoCatVarModifierG> modifierGroups) {
        this.modifierGroups = modifierGroups;
    }

    public boolean isInStock() {
        return inStock;
    }

    public void setInStock(boolean inStock) {
        this.inStock = inStock;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoCatalogueVariant that = (ZomatoCatalogueVariant) o;

        return new EqualsBuilder()
            .append(inStock, that.inStock)
            .append(propertyValues, that.propertyValues)
            .append(prices, that.prices)
            .append(vendorEntityId, that.vendorEntityId)
            .append(modifierGroups, that.modifierGroups)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(propertyValues)
            .append(prices)
            .append(vendorEntityId)
            .append(modifierGroups)
            .append(inStock)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "ZomatoCatalogueVariant{" +
            "propertyValues=" + propertyValues +
            ", prices=" + prices +
            ", vendorEntityId='" + vendorEntityId + '\'' +
            ", modifierGroups=" + modifierGroups +
            ", inStock=" + inStock +
            '}';
    }
}
