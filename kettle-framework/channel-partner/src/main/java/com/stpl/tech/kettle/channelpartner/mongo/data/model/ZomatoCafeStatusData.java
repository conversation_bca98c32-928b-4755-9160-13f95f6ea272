package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu.ZomatoCafeStatusRequest;
import com.stpl.tech.master.domain.model.UnitStatus;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "ZomatoCafeStatusData")
public class ZomatoCafeStatusData {

    @Id
    private String id;
    private String name;
    private Integer unitId;
    private Integer brandId;
    private Boolean partnerStatus;
    private String city;
    private Boolean isCafeLive;
    private String region;
    private UnitStatus unitStatus;
    private ZomatoCafeStatusRequest zomatoResponse;
    private Date lastUpdatedTime;
    private String lastUpdatedTimeIST;
    private boolean zomatoStatusFromGetApi;
    private boolean updationRequest=false;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Boolean getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(Boolean partnerStatus) {
        this.partnerStatus = partnerStatus;
    }

    public Boolean getCafeLive() {
        return isCafeLive;
    }

    public void setCafeLive(Boolean cafeLive) {
        isCafeLive = cafeLive;
    }

    public void setUnitStatus(UnitStatus unitStatus) {
        this.unitStatus = unitStatus;
    }

    public UnitStatus getUnitStatus() {
        return unitStatus;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public ZomatoCafeStatusRequest getZomatoResponse() {
        return zomatoResponse;
    }

    public void setZomatoResponse(ZomatoCafeStatusRequest zomatoResponse) {
        this.zomatoResponse = zomatoResponse;
    }

    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public String getLastUpdatedTimeIST() {
        return lastUpdatedTimeIST;
    }

    public void setLastUpdatedTimeIST(String lastUpdatedTimeIST) {
        this.lastUpdatedTimeIST = lastUpdatedTimeIST;
    }

    public boolean isZomatoStatusFromGetApi() {
        return zomatoStatusFromGetApi;
    }

    public void setZomatoStatusFromGetApi(boolean zomatoStatusFromGetApi) {
        this.zomatoStatusFromGetApi = zomatoStatusFromGetApi;
    }

    public boolean isUpdationRequest() {
        return updationRequest;
    }

    public void setUpdationRequest(boolean updationRequest) {
        this.updationRequest = updationRequest;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoCafeStatusData that = (ZomatoCafeStatusData) o;

        return new EqualsBuilder()
            .append(id, that.id)
            .append(name, that.name)
            .append(unitId, that.unitId)
            .append(brandId, that.brandId)
            .append(partnerStatus, that.partnerStatus)
            .append(city, that.city)
            .append(isCafeLive, that.isCafeLive)
            .append(region, that.region)
            .append(unitStatus, that.unitStatus)
            .append(zomatoResponse, that.zomatoResponse)
            .append(lastUpdatedTime, that.lastUpdatedTime)
            .append(lastUpdatedTimeIST, that.lastUpdatedTimeIST)
            .append(zomatoStatusFromGetApi, that.zomatoStatusFromGetApi)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(id)
            .append(name)
            .append(unitId)
            .append(brandId)
            .append(partnerStatus)
            .append(city)
            .append(isCafeLive)
            .append(region)
            .append(unitStatus)
            .append(zomatoResponse)
            .append(lastUpdatedTime)
            .append(lastUpdatedTimeIST)
            .append(zomatoStatusFromGetApi)
            .toHashCode();
    }

    @Override
    public String toString() {
        return "ZomatoCafeStatusData{" +
            "id='" + id + '\'' +
            ", name='" + name + '\'' +
            ", unitId=" + unitId +
            ", brandId=" + brandId +
            ", partnerStatus=" + partnerStatus +
            ", city='" + city + '\'' +
            ", isCafeLive=" + isCafeLive +
            ", region=" + region +
            ", unitStatus=" + unitStatus +
            ", zomatoResponse=" + zomatoResponse +
            ", lastUpdatedTime=" + lastUpdatedTime +
            ", lastUpdatedTimeIST='" + lastUpdatedTimeIST + '\'' +
            '}';
    }
}

