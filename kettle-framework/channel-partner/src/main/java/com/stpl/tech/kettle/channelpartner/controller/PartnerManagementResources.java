package com.stpl.tech.kettle.channelpartner.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.channelpartner.core.service.PartnerService;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerCafeStatusHistory;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.PARTNER_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PARTNER_MANAGEMENT_ROOT_CONTEXT) // 'v1/partner-management'
public class PartnerManagementResources extends ChannelPartnerAbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(PartnerManagementResources.class);

    @Autowired
    private PartnerService partnerService;

    @RequestMapping(method = RequestMethod.POST, value = "add", produces = MediaType.APPLICATION_JSON)
    public PartnerDetail addNewPartner(@RequestBody PartnerDetail request) {
        LOG.info("Request to add partner : {}", new Gson().toJson(request));
        return partnerService.addPartner(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get", produces = MediaType.APPLICATION_JSON)
    public List<PartnerDetail> getAllPartners() {
        LOG.info("Request to get all partners : ");
        return partnerService.getAllPartners();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-active", produces = MediaType.APPLICATION_JSON)
    public List<PartnerDetail> getActivePartners() {
        LOG.info("Request to get active partners : ");
        return partnerService.getActivePartners();
    }

    @RequestMapping(method = RequestMethod.POST, value = "activate", produces = MediaType.APPLICATION_JSON, consumes = MediaType.TEXT_PLAIN)
    public PartnerDetail activatePartner(@RequestBody String partnerId) {
        LOG.info("Request to get active partners : ");
        return partnerService.activatePartner(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "deactivate", produces = MediaType.APPLICATION_JSON, consumes = MediaType.TEXT_PLAIN)
    public PartnerDetail deactivatePartner(@RequestBody String partnerId) {
        LOG.info("Request to deactivate partner id : {}", partnerId);
        return partnerService.deactivatePartner(partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-partner-cafe-status-history", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<PartnerCafeStatusHistory> getPartnerCafeStatusHistory(@RequestBody IdCodeName detail) {
        LOG.info("Request to get cafe status history for : {}", detail.getId());
        return partnerService.getPartnerCafeStatusHistory(Integer.parseInt(detail.getCode()), detail.getId(), detail.getName());
    }
}
