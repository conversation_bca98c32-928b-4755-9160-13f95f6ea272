package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "restaurantId",
        "externalItemIds",
        "enable",
        "fromTime",
        "toTime"
})
public class SwiggyStockRequest {

    @JsonProperty("restaurantId")
    private String restaurantId;
    @JsonProperty("externalItemIds")
    private List<String> externalItemIds = null;
    @JsonProperty("enable")
    private boolean enable;
    @JsonProperty("fromTime")
    private String fromTime;
    @JsonProperty("toTime")
    private String toTime;

    @JsonProperty("restaurantId")
    public String getRestaurantId() {
        return restaurantId;
    }

    @JsonProperty("restaurantId")
    public void setRestaurantId(String restaurantId) {
        this.restaurantId = restaurantId;
    }

    @JsonProperty("externalItemIds")
    public List<String> getExternalItemIds() {
        if(externalItemIds == null){
            externalItemIds = new ArrayList<>();
        }
        return externalItemIds;
    }

    @JsonProperty("externalItemIds")
    public void setExternalItemIds(List<String> externalItemIds) {
        this.externalItemIds = externalItemIds;
    }

    @JsonProperty("enable")
    public boolean isEnable() {
        return enable;
    }

    @JsonProperty("enable")
    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    @JsonProperty("fromTime")
    public String getFromTime() {
        return fromTime;
    }

    @JsonProperty("fromTime")
    public void setFromTime(String fromTime) {
        this.fromTime = fromTime;
    }

    @JsonProperty("toTime")
    public String getToTime() {
        return toTime;
    }

    @JsonProperty("toTime")
    public void setToTime(String toTime) {
        this.toTime = toTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("restaurantId", restaurantId).append("externalItemIds", externalItemIds).append("enable", enable).append("fromTime", fromTime).append("toTime", toTime).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(externalItemIds).append(enable).append(fromTime).append(toTime).append(restaurantId).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof SwiggyStockRequest) == false) {
            return false;
        }
        SwiggyStockRequest rhs = ((SwiggyStockRequest) other);
        return new EqualsBuilder().append(externalItemIds, rhs.externalItemIds).append(enable, rhs.enable).append(fromTime, rhs.fromTime).append(toTime, rhs.toTime).append(restaurantId, rhs.restaurantId).isEquals();
    }

}