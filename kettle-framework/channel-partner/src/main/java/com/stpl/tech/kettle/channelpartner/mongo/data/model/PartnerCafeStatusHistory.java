package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.master.domain.model.UnitStatus;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "PartnerCafeStatusHistory")
public class PartnerCafeStatusHistory {

    @Id
    private String id;
    private String name;
    private Integer unitId;
    private Integer brandId;
    private String partnerName;
    private Boolean partnerStatus;
    private Boolean isCafeLive;
    private UnitStatus unitStatus;
    private Date lastUpdatedTime;
    private String lastUpdatedTimeIST;
    private boolean getAPiStatusResponse;
    private String reason;

    public PartnerCafeStatusHistory(String name, Integer unitId, Integer brandId, String partnerName, Boolean partnerStatus,
                                    Boolean isCafeLive, UnitStatus unitStatus, Date lastUpdatedTime, String lastUpdatedTimeIST,
                                    boolean getAPiStatusResponse, String reason) {
        this.name = name;
        this.unitId = unitId;
        this.brandId = brandId;
        this.partnerName = partnerName;
        this.partnerStatus = partnerStatus;
        this.isCafeLive = isCafeLive;
        this.unitStatus = unitStatus;
        this.lastUpdatedTime = lastUpdatedTime;
        this.lastUpdatedTimeIST = lastUpdatedTimeIST;
        this.getAPiStatusResponse=getAPiStatusResponse;
        this.reason = reason;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Boolean getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(Boolean partnerStatus) {
        this.partnerStatus = partnerStatus;
    }

    public Boolean getCafeLive() {
        return isCafeLive;
    }

    public void setCafeLive(Boolean cafeLive) {
        isCafeLive = cafeLive;
    }

    public UnitStatus getUnitStatus() {
        return unitStatus;
    }

    public void setUnitStatus(UnitStatus unitStatus) {
        this.unitStatus = unitStatus;
    }

    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public String getLastUpdatedTimeIST() {
        return lastUpdatedTimeIST;
    }

    public void setLastUpdatedTimeIST(String lastUpdatedTimeIST) {
        this.lastUpdatedTimeIST = lastUpdatedTimeIST;
    }

    public boolean isGetAPiStatusResponse() {
        return getAPiStatusResponse;
    }

    public void setGetAPiStatusResponse(boolean getAPiStatusResponse) {
        this.getAPiStatusResponse = getAPiStatusResponse;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}

