package com.stpl.tech.kettle.channelpartner.core.exceptions;

import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.util.EnvType;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;

import java.lang.reflect.Method;

public class CustomAsyncExceptionHandler implements AsyncUncaughtExceptionHandler {

    @Override
    public void handleUncaughtException(Throwable throwable, Method method, Object... obj) {

        StringBuilder message = new StringBuilder("Exception message - " + throwable.getMessage()+"\n");
        message.append("Method name - " + method.getName()+"\n");
        for (Object param : obj) {
            message.append("Parameter value - " + param+"\n");
        }
        System.out.println(message);
        SlackNotificationService.getInstance().sendNotification(EnvType.DEV,
                ApplicationName.KETTLE_SERVICE.name(), SlackNotification.SYSTEM_ERRORS,
                "Async Call Exception::::::::::::::::\n"+message);
    }
}
