package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({"predicted_prep_time", "max_increase_threshold", "max_decrease_threshold"})
public class PrepTimeDetails {

    @JsonProperty("predicted_prep_time")
    private float predictedPrepTime;

    @JsonProperty("max_increase_threshold")
    private float maxIncreaseThreshold;

    @JsonProperty("max_decrease_threshold")
    private float maxDecreaseThreshold;


    @JsonProperty("predicted_prep_time")
    public float getPredictedPrepTime() {
        return predictedPrepTime;
    }

    @JsonProperty("predicted_prep_time")
    public void setPredictedPrepTime(float predictedPrepTime) {
        this.predictedPrepTime = predictedPrepTime;
    }

    @JsonProperty("max_increase_threshold")
    public float getMaxIncreaseThreshold() {
        return maxIncreaseThreshold;
    }

    @JsonProperty("max_increase_threshold")
    public void setMaxIncreaseThreshold(float maxIncreaseThreshold) {
        this.maxIncreaseThreshold = maxIncreaseThreshold;
    }

    @JsonProperty("max_decrease_threshold")
    public float getMaxDecreaseThreshold() {
        return maxDecreaseThreshold;
    }

    @JsonProperty("max_decrease_threshold")
    public void setMaxDecreaseThreshold(float maxDecreaseThreshold) {
        this.maxDecreaseThreshold = maxDecreaseThreshold;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PrepTimeDetails that = (PrepTimeDetails) o;
        return Float.compare(that.predictedPrepTime, predictedPrepTime) == 0 && Float.compare(that.maxIncreaseThreshold, maxIncreaseThreshold) == 0 && Float.compare(that.maxDecreaseThreshold, maxDecreaseThreshold) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(predictedPrepTime, maxIncreaseThreshold, maxDecreaseThreshold);
    }

    @Override
    public String toString() {
        return "PrepTimeDetails{" +
                "predictedPrepTime=" + predictedPrepTime +
                ", maxIncreaseThreshold=" + maxIncreaseThreshold +
                ", maxDecreaseThreshold=" + maxDecreaseThreshold +
                '}';
    }
}
