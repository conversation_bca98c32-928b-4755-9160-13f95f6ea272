package com.stpl.tech.kettle.channelpartner.domain.model.zomato.order;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.apache.commons.lang.builder.ToStringBuilder;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "zomato_item_id",
        "item_id",
        "item_name",
        "order_item_id",
        "item_unit_price",
        "item_final_price",
        "item_quantity",
        "groups",
        "item_taxes",
        "item_charges",
        "item_discounts"
})
public class ZomatoOrderItem {

    @JsonProperty("zomato_item_id")
    private Long zomatoItemId;
    @JsonProperty("item_id")
    private String itemId;
    @JsonProperty("item_name")
    private String itemName;
    @JsonProperty("order_item_id")
    private Integer orderItemId;
    @JsonProperty("item_unit_price")
    private Float itemUnitPrice;
    @JsonProperty("combo_reduced_price")
    private Float comboReducedPrice;
    @JsonProperty("item_final_price")
    private Float itemFinalPrice;
    @JsonProperty("item_quantity")
    private Integer itemQuantity;
    @JsonProperty("groups")
    private List<ZomatoGroup> groups = null;
    @JsonProperty("item_taxes")
    private List<ZomatoTaxDetails> itemTaxes = null;
    @JsonProperty("item_charges")
    private List<ZomatoChargeDetails> itemCharges = null;
    @JsonProperty("item_discounts")
    private List<ZomatoItemDiscount> itemDiscounts = null;

    @JsonProperty("zomato_item_id")
    public Long getZomatoItemId() {
        return zomatoItemId;
    }

    @JsonProperty("zomato_item_id")
    public void setZomatoItemId(Long zomatoItemId) {
        this.zomatoItemId = zomatoItemId;
    }

    @JsonProperty("item_id")
    public String getItemId() {
        return itemId;
    }

    @JsonProperty("item_id")
    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    @JsonProperty("item_name")
    public String getItemName() {
        return itemName;
    }

    @JsonProperty("item_name")
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    @JsonProperty("order_item_id")
    public Integer getOrderItemId() {
        return orderItemId;
    }

    @JsonProperty("order_item_id")
    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    @JsonProperty("item_unit_price")
    public Float getItemUnitPrice() {
        return itemUnitPrice;
    }

    @JsonProperty("item_unit_price")
    public void setItemUnitPrice(Float itemUnitPrice) {
        this.itemUnitPrice = itemUnitPrice;
    }

    @JsonProperty("combo_reduced_price")
    public Float getComboReducedPrice() {
        return comboReducedPrice;
    }

    @JsonProperty("combo_reduced_price")
    public void setComboReducedPrice(Float comboReducedPrice) {
        this.comboReducedPrice = comboReducedPrice;
    }

    @JsonProperty("item_final_price")
    public Float getItemFinalPrice() {
        return itemFinalPrice;
    }

    @JsonProperty("item_final_price")
    public void setItemFinalPrice(Float itemFinalPrice) {
        this.itemFinalPrice = itemFinalPrice;
    }

    @JsonProperty("item_quantity")
    public Integer getItemQuantity() {
        return itemQuantity;
    }

    @JsonProperty("item_quantity")
    public void setItemQuantity(Integer itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    @JsonProperty("groups")
    public List<ZomatoGroup> getGroups() {
        if(groups == null){
            groups = new ArrayList<>();
        }
        return groups;
    }

    @JsonProperty("groups")
    public void setGroups(List<ZomatoGroup> groups) {
        this.groups = groups;
    }

    @JsonProperty("item_taxes")
    public List<ZomatoTaxDetails> getItemTaxes() {
        if(itemTaxes == null){
            itemTaxes = new ArrayList<>();
        }
        return itemTaxes;
    }

    @JsonProperty("item_taxes")
    public void setItemTaxes(List<ZomatoTaxDetails> itemTaxes) {
        this.itemTaxes = itemTaxes;
    }

    @JsonProperty("item_charges")
    public List<ZomatoChargeDetails> getItemCharges() {
        if(itemCharges == null){
            itemCharges = new ArrayList<>();
        }
        return itemCharges;
    }

    @JsonProperty("item_charges")
    public void setItemCharges(List<ZomatoChargeDetails> itemCharges) {
        this.itemCharges = itemCharges;
    }

    @JsonProperty("item_discounts")
    public List<ZomatoItemDiscount> getItemDiscounts() {
        if(itemDiscounts == null){
            itemDiscounts = new ArrayList<>();
        }
        return itemDiscounts;
    }

    @JsonProperty("item_discounts")
    public void setItemDiscounts(List<ZomatoItemDiscount> itemDiscounts) {
        this.itemDiscounts = itemDiscounts;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("zomatoItemId", zomatoItemId).append("itemId", itemId).append("itemName", itemName).append("orderItemId", orderItemId).append("itemUnitPrice", itemUnitPrice).append("itemFinalPrice", itemFinalPrice).append("itemQuantity", itemQuantity).append("groups", groups).append("itemTaxes", itemTaxes).append("itemCharges", itemCharges).append("itemDiscounts", itemDiscounts).toString();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(itemFinalPrice).append(itemUnitPrice).append(itemName).append(itemQuantity).append(orderItemId).append(zomatoItemId).append(itemCharges).append(itemId).append(itemTaxes).append(groups).append(itemDiscounts).toHashCode();
    }

    @Override
    public boolean equals(Object other) {
        if (other == this) {
            return true;
        }
        if ((other instanceof ZomatoOrderItem) == false) {
            return false;
        }
        ZomatoOrderItem rhs = ((ZomatoOrderItem) other);
        return new EqualsBuilder().append(itemFinalPrice, rhs.itemFinalPrice).append(itemUnitPrice, rhs.itemUnitPrice).append(itemName, rhs.itemName).append(itemQuantity, rhs.itemQuantity).append(orderItemId, rhs.orderItemId).append(zomatoItemId, rhs.zomatoItemId).append(itemCharges, rhs.itemCharges).append(itemId, rhs.itemId).append(itemTaxes, rhs.itemTaxes).append(groups, rhs.groups).append(itemDiscounts, rhs.itemDiscounts).isEquals();
    }

}