package com.stpl.tech.kettle.channelpartner.domain.model.swiggy;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "orderId",
        "timeToArrive",
})
public class OrderTime {

    @JsonProperty("orderId")
    private String orderId;
    @JsonProperty("timeToArrive")
    private Integer timeToArrive;

    @JsonProperty("orderId")
    public String getOrderId() {
        return orderId;
    }

    @JsonProperty("orderId")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("timeToArrive")
    public Integer getTimeToArrive() {
        return timeToArrive;
    }

    @JsonProperty("timeToArrive")
    public void setTimeToArrive(Integer timeToArrive) {
        this.timeToArrive = timeToArrive;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        OrderTime orderTime = (OrderTime) o;

        return new EqualsBuilder()
                .append(orderId, orderTime.orderId)
                .append(timeToArrive, orderTime.timeToArrive)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(orderId)
                .append(timeToArrive)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "OrderTime{" +
                "orderId='" + orderId + '\'' +
                ", timeToArrive=" + timeToArrive +
                '}';
    }
}