package com.stpl.tech.kettle.channelpartner.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;

import java.util.List;

public class PartnerUnitListVO {

    private Integer partnerId;
    private List<Integer> unitIds;
    private PartnerOfferDetail offerDetail;
    private Boolean isNew;
    private Integer brandId;

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public List<Integer> getUnitIds() {
        return unitIds;
    }

    public void setUnitIds(List<Integer> unitIds) {
        this.unitIds = unitIds;
    }

    public PartnerOfferDetail getOfferDetail() {
        return offerDetail;
    }

    public void setOfferDetail(PartnerOfferDetail offerDetail) {
        this.offerDetail = offerDetail;
    }

    public Boolean getNew() {
        return isNew;
    }

    public void setNew(Boolean isNew) {
        this.isNew = isNew;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }
}
