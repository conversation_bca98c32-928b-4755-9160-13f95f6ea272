package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "service",
    "price",
    "originalPrice"
})
public class ZomatoCatVarPrice {

    @JsonProperty("service")
    private String service;
    @JsonProperty("price")
    private Float price;
    @JsonProperty("originalPrice")
    private Float originalPrice;

    @JsonProperty("service")
    public String getService() {
        return service;
    }

    @JsonProperty("service")
    public void setService(String service) {
        this.service = service;
    }

    @JsonProperty("price")
    public Float getPrice() {
        return price;
    }

    @JsonProperty("price")
    public void setPrice(Float price) {
        this.price = price;
    }

    @JsonProperty("originalPrice")
    public Float getOriginalPrice() {
        return originalPrice;
    }

    @JsonProperty("originalPrice")
    public void setOriginalPrice(Float originalPrice) {
        this.originalPrice = originalPrice;
    }


    @Override
    public String toString() {
        return "ZomatoCatVarPrice [service=" + service + ", price=" + price + ", originalPrice=" + originalPrice + "]";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoCatVarPrice that = (ZomatoCatVarPrice) o;

        return new EqualsBuilder()
            .append(service, that.service)
            .append(price, that.price)
            .append(originalPrice, that.originalPrice)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(service)
            .append(price)
            .append(originalPrice)
            .toHashCode();
    }


}
