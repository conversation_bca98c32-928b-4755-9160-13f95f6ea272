package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "outlet_id",
        "items"
})
public class ZomatoUnitProductStock {

    @JsonProperty("outlet_id")
    private String outletId;

    @JsonProperty("items")
    private List<ZomatoProductStock> items;

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("items")
    public List<ZomatoProductStock> getItems() {
        if(items == null){
            items = new ArrayList<>();
        }
        return items;
    }

    @JsonProperty("items")
    public void setItems(List<ZomatoProductStock> items) {
        this.items = items;
    }
}
