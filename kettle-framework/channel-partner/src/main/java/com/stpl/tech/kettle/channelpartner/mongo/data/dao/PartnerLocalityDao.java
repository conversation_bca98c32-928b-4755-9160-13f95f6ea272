package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerLocalityDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PartnerLocalityDao extends MongoRepository<PartnerLocalityDetail, String> {

    public PartnerLocalityDetail getPartnerLocalityDetailByKettlePartnerIdAndLocalityAndCityAndStateAndCountryAndPinCode(Integer kettlePartnerId,
                                                                                                                         String locality, String city, String state, String country, String pinCode);
}
