package com.stpl.tech.kettle.channelpartner.controller;

import com.stpl.tech.kettle.channelpartner.core.cache.ChannelPartnerDataCache;
import com.stpl.tech.kettle.channelpartner.core.scheduler.PartnerOrderCacheScheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;

import java.util.Map;
import java.util.Set;

import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.CHANNEL_PARTNER_CACHE_ROOT_CONTEXT;
import static com.stpl.tech.kettle.channelpartner.core.ChannelPartnerServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + CHANNEL_PARTNER_CACHE_ROOT_CONTEXT) // 'v1/channel-partner-cache'
public class ChannelPartnerCacheResources {
    private static final Logger LOG = LoggerFactory.getLogger(ChannelPartnerCacheResources.class);

    @Autowired
    private ChannelPartnerDataCache channelPartnerDataCache;

    @Autowired
    private PartnerOrderCacheScheduler partnerOrderCacheScheduler;

    @Scheduled(cron = "0 30 7 * * *", zone = "GMT+05:30")
    public void reloadPartnersCacheCron() {
        LOG.info("Cron to Reload CHANNEL PARTNER CACHE");
        channelPartnerDataCache.initPartnerCache();
    }

    @Scheduled(cron = "0 0/5 * * * *", zone = "GMT+05:30")
    public void notifyPendingOrders() {
        LOG.info("Cron to confirm pending orders");
        channelPartnerDataCache.addPendingOrdersForNotification(5);
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-partner-cache", produces = MediaType.APPLICATION_JSON)
    public boolean reloadPartnersCache() {
        LOG.info("Request to reload CHANNEL PARTNER CACHE");
        channelPartnerDataCache.initPartnerCache();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-unit-product-combo-mapping", produces = MediaType.APPLICATION_JSON)
    public boolean reloadProductComboMapping() {
        LOG.info("Request to reload UNIT PRODUCT COMBO MAPPING CACHE");
        channelPartnerDataCache.loadUnitProductComboMappings();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-partner-unit-product-mapping", produces = MediaType.APPLICATION_JSON)
    public boolean reloadPartnerUnitProductMapping() {
        LOG.info("Request to reload PARTNER UNIT PRODUCT MAPPING CACHE");
        channelPartnerDataCache.loadUnitUpsellingSuperComboMapping();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-partner-unit-product-pricing", produces = MediaType.APPLICATION_JSON)
    public boolean reloadPartnerUnitProductPricing() {
        LOG.info("Request to reload PARTNER UNIT PRODUCT PRICING CACHE");
        channelPartnerDataCache.loadPartnerUnitProductPricingMap();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-stock", produces = MediaType.APPLICATION_JSON)
    public boolean refreshStock() {
        LOG.info("Request to reload UNIT STOCK");
        partnerOrderCacheScheduler.refreshAllUnitsStock();
        partnerOrderCacheScheduler.refreshAllUnitsStockForGNT();
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "reload-stock/unit", produces = MediaType.APPLICATION_JSON)
	public boolean refreshStock(@RequestParam final int unitId) {
		LOG.info("Request to reload UNIT STOCK for unitId {}", unitId);
		partnerOrderCacheScheduler.refreshStock(unitId);
		return true;
	}

    @RequestMapping(method = RequestMethod.GET, value = "product-combo/unit", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, Set<Integer>> getUnitProductComboMappings(@RequestParam final int unitId) {
        LOG.info("Request to reload UNIT STOCK for unitId {}", unitId);
        return channelPartnerDataCache.getUnitProductComboMappings(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "combo-product/unit", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, Set<Integer>> getUnitComboProductMappings(@RequestParam final int unitId) {
        LOG.info("Request to reload UNIT STOCK for unitId {}", unitId);
        return channelPartnerDataCache.getUnitComboProductMappings(unitId);
    }

    @PostMapping(value = "update-timing-cache/unit",produces = MediaType.APPLICATION_JSON)
    public Boolean updateTimingCache(){
        channelPartnerDataCache.updateCacheOfTimings();
        return true;
    }


}
