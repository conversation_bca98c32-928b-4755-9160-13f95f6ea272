
package com.stpl.tech.kettle.channelpartner.domain.model.swiggy.menu;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.ToStringBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
    "igst",
    "sgst",
    "cgst",
    "inclusive"
})
public class GstDetails {

    @JsonProperty("igst")
    private BigDecimal igst;
    @JsonProperty("sgst")
    private BigDecimal sgst;
    @JsonProperty("cgst")
    private BigDecimal cgst;
    @JsonProperty("inclusive")
    private Boolean inclusive;
    @JsonProperty("gst_liability")
    private String gstLiability;
    @JsonIgnore
    private Map<String, Object> additionalProperties = new HashMap<String, Object>();

    @JsonProperty("igst")
    public BigDecimal getIgst() {
        return igst;
    }

    @JsonProperty("igst")
    public void setIgst(BigDecimal igst) {
        this.igst = igst;
    }

    @JsonProperty("sgst")
    public BigDecimal getSgst() {
        return sgst;
    }

    @JsonProperty("sgst")
    public void setSgst(BigDecimal sgst) {
        this.sgst = sgst;
    }

    @JsonProperty("cgst")
    public BigDecimal getCgst() {
        return cgst;
    }

    @JsonProperty("cgst")
    public void setCgst(BigDecimal cgst) {
        this.cgst = cgst;
    }

    @JsonProperty("inclusive")
    public Boolean getInclusive() {
        return inclusive;
    }

    @JsonProperty("inclusive")
    public void setInclusive(Boolean inclusive) {
        this.inclusive = inclusive;
    }

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return this.additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        this.additionalProperties.put(name, value);
    }

    @JsonProperty("gst_liability")
       public String getGstLiability() {
		return gstLiability;
	}

    @JsonProperty("gst_liability")
    	public void setGstLiability(String gstLiability) {
		this.gstLiability = gstLiability;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this).append("igst", igst).append("sgst", sgst).append("cgst", cgst).append("inclusive", inclusive).append("additionalProperties", additionalProperties).append("gstLiability", gstLiability).toString();
    }

}
