package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderActions;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderError;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatus;
import com.stpl.tech.kettle.channelpartner.domain.model.PartnerOrderStatusLog;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlRootElement;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Document
@XmlRootElement(name = "PartnerOrderDetail")
public class PartnerOrderDetail {

    @Id
    protected String orderId;
    @Indexed
    protected String partnerId;
    protected String partnerName;
    @Indexed
    protected String partnerOrderId;
    protected Object partnerOrder;
    protected Object kettleOrder;
    protected String externalOrderId;
    @Indexed
    protected String kettleOrderId;
    @Indexed
    protected PartnerOrderStatus partnerOrderStatus;
    protected List<PartnerOrderActions> orderActions = new ArrayList<>();
    protected List<PartnerOrderError> orderErrors = new ArrayList<>();
    protected Boolean orderValid;
    protected List<PartnerOrderStatusLog> orderStateLogs = new ArrayList<>();
    @Indexed
    protected Date addTime;
    protected String addTimeIST;
    protected String linkedOrderId;
    protected BigDecimal billPercentageDifference;
    protected BigDecimal billDifference;
    protected Boolean toBeProcessed;
    protected Boolean toBeRejected;
    
    @Indexed
    protected Boolean beingProcessed;
    protected List<PartnerDeliveryStatus> deliveryStatuses;
    protected List<PartnerOrderStatusUpdate> partnerOrderStatusUpdates;
    protected String unitName;
    protected String partnerOrderVersion;
    protected String restaurantId;
    protected Integer brandId;
    protected Integer unitId;
    protected boolean isNotified;
    private String fallbackProcessedBy;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerOrderId() {
        return partnerOrderId;
    }

    public void setPartnerOrderId(String partnerOrderId) {
        this.partnerOrderId = partnerOrderId;
    }

    public Object getPartnerOrder() {
        return partnerOrder;
    }

    public void setPartnerOrder(Object partnerOrder) {
        this.partnerOrder = partnerOrder;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public PartnerOrderStatus getPartnerOrderStatus() {
        return partnerOrderStatus;
    }

    public void setPartnerOrderStatus(PartnerOrderStatus partnerOrderStatus) {
        this.partnerOrderStatus = partnerOrderStatus;
    }

    public List<PartnerOrderActions> getOrderActions() {
        return orderActions;
    }

    public void setOrderActions(List<PartnerOrderActions> orderActions) {
        this.orderActions = orderActions;
    }

    public List<PartnerOrderError> getOrderErrors() {
        return orderErrors;
    }

    public void setOrderErrors(List<PartnerOrderError> orderErrors) {
        this.orderErrors = orderErrors;
    }

    public Boolean getOrderValid() {
        return orderValid;
    }

    public void setOrderValid(Boolean orderValid) {
        this.orderValid = orderValid;
    }

    public List<PartnerOrderStatusLog> getOrderStateLogs() {
        return orderStateLogs;
    }

    public void setOrderStateLogs(List<PartnerOrderStatusLog> orderStateLogs) {
        this.orderStateLogs = orderStateLogs;
    }

    public String getLinkedOrderId() {
        return linkedOrderId;
    }

    public void setLinkedOrderId(String linkedOrderId) {
        this.linkedOrderId = linkedOrderId;
    }

    public String getExternalOrderId() {
        return externalOrderId;
    }

    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    public BigDecimal getBillPercentageDifference() {
        return billPercentageDifference;
    }

    public void setBillPercentageDifference(BigDecimal billPercentageDifference) {
        this.billPercentageDifference = billPercentageDifference;
    }

    public BigDecimal getBillDifference() {
        return billDifference;
    }

    public void setBillDifference(BigDecimal billDifference) {
        this.billDifference = billDifference;
    }

    public String getKettleOrderId() {
        return kettleOrderId;
    }

    public void setKettleOrderId(String kettleOrderId) {
        this.kettleOrderId = kettleOrderId;
    }

    public Object getKettleOrder() {
        return kettleOrder;
    }

    public void setKettleOrder(Object kettleOrder) {
        this.kettleOrder = kettleOrder;
    }

    public Boolean getToBeProcessed() {
        return toBeProcessed;
    }

    public void setToBeProcessed(Boolean toBeProcessed) {
        this.toBeProcessed = toBeProcessed;
    }

    public Boolean getBeingProcessed() {
        return beingProcessed;
    }

    public void setBeingProcessed(Boolean beingProcessed) {
        this.beingProcessed = beingProcessed;
    }

    public List<PartnerDeliveryStatus> getDeliveryStatuses() {
        if(deliveryStatuses == null){
            deliveryStatuses = new ArrayList<>();
        }
        return deliveryStatuses;
    }

    public void setDeliveryStatuses(List<PartnerDeliveryStatus> deliveryStatuses) {
        this.deliveryStatuses = deliveryStatuses;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public List<PartnerOrderStatusUpdate> getPartnerOrderStatusUpdates() {
        if(partnerOrderStatusUpdates == null){
            partnerOrderStatusUpdates = new ArrayList<>();
        }
        return partnerOrderStatusUpdates;
    }

    public void setPartnerOrderStatusUpdates(List<PartnerOrderStatusUpdate> partnerOrderStatusUpdates) {
        this.partnerOrderStatusUpdates = partnerOrderStatusUpdates;
    }

    public String getPartnerOrderVersion() {
        return partnerOrderVersion;
    }

    public void setPartnerOrderVersion(String partnerOrderVersion) {
        this.partnerOrderVersion = partnerOrderVersion;
    }

    public String getRestaurantId() {
        return restaurantId;
    }

    public void setRestaurantId(String restaurantId) {
        this.restaurantId = restaurantId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getAddTimeIST() {
        return addTimeIST;
    }

    public void setAddTimeIST(String addTimeIST) {
        this.addTimeIST = addTimeIST;
    }

    public boolean isNotified() {
        return isNotified;
    }

    public void setNotified(boolean notified) {
        isNotified = notified;
    }

    public String getFallbackProcessedBy() {
        return fallbackProcessedBy;
    }

    public void setFallbackProcessedBy(String fallbackProcessedBy) {
        this.fallbackProcessedBy = fallbackProcessedBy;
    }

    public String getPickedByStatus() {
        return fallbackProcessedBy == null || fallbackProcessedBy.trim().isEmpty() ? 
            "Picked By: Not Picked" : 
            "Picked By: " + fallbackProcessedBy;
    }

    public Boolean getToBeRejected() {
		return toBeRejected == null ? Boolean.FALSE : toBeRejected;
	}

	public void setToBeRejected(Boolean toBeRejected) {
		this.toBeRejected = toBeRejected;
	}

	@Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        PartnerOrderDetail that = (PartnerOrderDetail) o;

        return new EqualsBuilder()
            .append(orderId, that.orderId)
            .append(partnerId, that.partnerId)
            .append(partnerName, that.partnerName)
            .append(partnerOrderId, that.partnerOrderId)
            .append(partnerOrder, that.partnerOrder)
            .append(kettleOrder, that.kettleOrder)
            .append(externalOrderId, that.externalOrderId)
            .append(kettleOrderId, that.kettleOrderId)
            .append(partnerOrderStatus, that.partnerOrderStatus)
            .append(orderActions, that.orderActions)
            .append(orderErrors, that.orderErrors)
            .append(orderValid, that.orderValid)
            .append(orderStateLogs, that.orderStateLogs)
            .append(addTime, that.addTime)
            .append(addTimeIST, that.addTimeIST)
            .append(linkedOrderId, that.linkedOrderId)
            .append(billPercentageDifference, that.billPercentageDifference)
            .append(billDifference, that.billDifference)
            .append(toBeProcessed, that.toBeProcessed)
            .append(beingProcessed, that.beingProcessed)
            .append(deliveryStatuses, that.deliveryStatuses)
            .append(partnerOrderStatusUpdates, that.partnerOrderStatusUpdates)
            .append(unitName, that.unitName)
            .append(partnerOrderVersion, that.partnerOrderVersion)
            .append(restaurantId, that.restaurantId)
            .append(brandId, that.brandId)
            .append(unitId, that.unitId)
            .append(isNotified, that.isNotified)
            .append(fallbackProcessedBy, that.fallbackProcessedBy)
            .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
            .append(orderId)
            .append(partnerId)
            .append(partnerName)
            .append(partnerOrderId)
            .append(partnerOrder)
            .append(kettleOrder)
            .append(externalOrderId)
            .append(kettleOrderId)
            .append(partnerOrderStatus)
            .append(orderActions)
            .append(orderErrors)
            .append(orderValid)
            .append(orderStateLogs)
            .append(addTime)
            .append(addTimeIST)
            .append(linkedOrderId)
            .append(billPercentageDifference)
            .append(billDifference)
            .append(toBeProcessed)
            .append(beingProcessed)
            .append(deliveryStatuses)
            .append(partnerOrderStatusUpdates)
            .append(unitName)
            .append(partnerOrderVersion)
            .append(restaurantId)
            .append(brandId)
            .append(unitId)
            .append(isNotified)
            .append(fallbackProcessedBy)
            .toHashCode();
    }
}
