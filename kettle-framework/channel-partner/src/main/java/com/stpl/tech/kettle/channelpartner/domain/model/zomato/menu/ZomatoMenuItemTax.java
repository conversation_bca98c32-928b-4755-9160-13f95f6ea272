package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_type",
        "taxes"
})
public class ZomatoMenuItemTax {

    @JsonProperty("order_type")
    private String orderType;
    @JsonProperty("taxes")
    private List<String> taxes;

    @JsonProperty("order_type")
    public String getOrderType() {
        return orderType;
    }

    @JsonProperty("order_type")
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    @JsonProperty("taxes")
    public List<String> getTaxes() {
        return taxes;
    }

    @JsonProperty("taxes")
    public void setTaxes(List<String> taxes) {
        this.taxes = taxes;
    }

    @Override
    public String toString() {
        return "ZomatoMenuItemTax{" +
                "orderType='" + orderType + '\'' +
                ", taxes=" + taxes +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoMenuItemTax that = (ZomatoMenuItemTax) o;

        return new EqualsBuilder()
                .append(orderType, that.orderType)
                .append(taxes, that.taxes)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(orderType)
                .append(taxes)
                .toHashCode();
    }
}