package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "rider_name",
        "rider_phone_number"
})
public class ZomatoRiderData {

    @JsonProperty("rider_name")
    private String riderName;

    @JsonProperty("rider_phone_number")
    private String riderPhoneNumber;

    @JsonProperty("rider_name")
    public String getRiderName() {
        return riderName;
    }

    @JsonProperty("rider_name")
    public void setRiderName(String riderName) {
        this.riderName = riderName;
    }

    @JsonProperty("rider_phone_number")
    public String getRiderPhoneNumber() {
        return riderPhoneNumber;
    }

    @JsonProperty("rider_phone_number")
    public void setRiderPhoneNumber(String riderPhoneNumber) {
        this.riderPhoneNumber = riderPhoneNumber;
    }
}
