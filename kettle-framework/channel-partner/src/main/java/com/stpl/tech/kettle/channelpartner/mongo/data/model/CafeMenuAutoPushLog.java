package com.stpl.tech.kettle.channelpartner.mongo.data.model;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Date;

@Document
@XmlRootElement(name = "cafeMenuAutoPushLog")
public class CafeMenuAutoPushLog {
    @Id
    private String id;

    private Integer unitId;
    private Integer brandId;
    private String unitName;
    private Boolean zomatoMenuFlag;
    private Boolean swiggyMenuFlag;
    private Date lastUpdatedTime;
    private String lastUpdatedTimeString;
    private Integer employeeId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Boolean getZomatoMenuFlag() {
        return zomatoMenuFlag;
    }

    public void setZomatoMenuFlag(Boolean zomatoMenuFlag) {
        this.zomatoMenuFlag = zomatoMenuFlag;
    }

    public Boolean getSwiggyMenuFlag() {
        return swiggyMenuFlag;
    }

    public void setSwiggyMenuFlag(Boolean swiggyMenuFlag) {
        this.swiggyMenuFlag = swiggyMenuFlag;
    }

    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) { this.lastUpdatedTime = lastUpdatedTime; }

    public String getLastUpdatedTimeString() {
        return lastUpdatedTimeString;
    }

    public void setLastUpdatedTimeString(String lastUpdatedTimeString) { this.lastUpdatedTimeString = lastUpdatedTimeString; }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }
}
