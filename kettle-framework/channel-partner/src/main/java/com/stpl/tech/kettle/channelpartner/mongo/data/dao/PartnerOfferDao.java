package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.PartnerOfferDetail;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface PartnerOfferDao extends MongoRepository<PartnerOfferDetail, String> {

    @Query("{'partnerId' : ?0, 'unitId' : ?1, 'endDate' : {$gte : ?2 }}")
    public List<PartnerOfferDetail> getAllActiveOffersForPartnerAndUnit(Integer partnerId, Integer unitId, Date endDate);

    @Query("{'partnerId' : ?0, 'unitId' : { $in : ?1 }, 'endDate' : {$gte : ?2 }}")
    public List<PartnerOfferDetail> getAllActiveOffersForPartnerAndUnits(Integer partnerId, List<Integer> unitIds, Date endDate);

}
