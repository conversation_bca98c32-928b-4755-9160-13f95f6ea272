package com.stpl.tech.kettle.channelpartner.mysql.data.dao;

import com.stpl.tech.kettle.channelpartner.mysql.data.model.CafeStatusChannelPartner;
import com.stpl.tech.master.data.dao.AbstractMasterDao;

import java.util.List;

public interface CafeStatusChannelPartnerDao extends AbstractMasterDao {


     List<CafeStatusChannelPartner> getInActiveCafe(String statusUpdate);

     public  Object[]  getOrderDetailByGeneratedOrderId(String generatedOrderId);

}
