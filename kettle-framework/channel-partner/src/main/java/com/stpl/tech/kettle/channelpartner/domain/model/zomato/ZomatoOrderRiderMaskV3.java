package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "is_rider_wearing_mask"
})
public class ZomatoOrderRiderMaskV3 {
	
	@JsonProperty("order_id")
	private String orderId;

	@JsonProperty("is_rider_wearing_mask")
	private String isRiderWearingMask;

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getIsRiderWearingMask() {
		return isRiderWearingMask;
	}

	public void setIsRiderWearingMask(String isRiderWearingMask) {
		this.isRiderWearingMask = isRiderWearingMask;
	}

}
