package com.stpl.tech.kettle.channelpartner.mongo.data.model;


import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@Document
@XmlRootElement(name = "DotdProductDimensions")
public class DotdProductDimensions {

    @Id
    protected int dimensionId;

    protected String dimensionName;

    protected String dimensionCode;
}
