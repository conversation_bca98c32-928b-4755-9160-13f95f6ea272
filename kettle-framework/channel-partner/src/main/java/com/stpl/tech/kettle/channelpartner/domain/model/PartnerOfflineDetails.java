package com.stpl.tech.kettle.channelpartner.domain.model;

import com.stpl.tech.master.domain.model.IdCodeName;

import java.util.Date;
import java.util.List;
import java.util.Objects;

public class PartnerOfflineDetails {
    private Integer partnerId;
    private String partnerName;
    private Boolean partnerStatus;
    private String reason;
    private String offlineTime;

    public PartnerOfflineDetails() {
    }

    public PartnerOfflineDetails(Integer partnerId, String partnerName, Boolean partnerStatus, String reason, String offlineTime) {
        this.partnerId = partnerId;
        this.partnerName = partnerName;
        this.partnerStatus = partnerStatus;
        this.reason = reason;
        this.offlineTime = offlineTime;
    }

    public Integer getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Integer partnerId) {
        this.partnerId = partnerId;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public Boolean getPartnerStatus() {
        return partnerStatus;
    }

    public void setPartnerStatus(Boolean partnerStatus) {
        this.partnerStatus = partnerStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getOfflineTime() {
        return offlineTime;
    }

    public void setOfflineTime(String offlineTime) {
        this.offlineTime = offlineTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PartnerOfflineDetails that = (PartnerOfflineDetails) o;
        return partnerId.equals(that.partnerId) && Objects.equals(partnerName, that.partnerName) && Objects.equals(partnerStatus, that.partnerStatus) && Objects.equals(reason, that.reason) && Objects.equals(offlineTime, that.offlineTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(partnerId, partnerName, partnerStatus, reason, offlineTime);
    }
}
