package com.stpl.tech.kettle.channelpartner.mongo.data.dao;

import com.stpl.tech.kettle.channelpartner.mongo.data.model.SwiggyCafeStatusData;
import com.stpl.tech.kettle.channelpartner.mongo.data.model.ZomatoCafeStatusData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ZomatoCafeStatusDao extends MongoRepository<ZomatoCafeStatusData, String> {

    ZomatoCafeStatusData findByUnitId(Integer unitId);

    ZomatoCafeStatusData findByUnitIdAndBrandId(Integer unitId, Integer brandId);

    List<ZomatoCafeStatusData> findAllByUnitIdInAndPartnerStatusAndBrandIdExists(Set<Integer> unitIds, boolean status, boolean brandExists);

}
