package com.stpl.tech.kettle.channelpartner.core.exceptions;

/**
 * Created by Chaayos on 27-07-2017.
 */
public class ChannelPartnerError {

    private Integer errorCode;
    private String errorMsg;
    private String errorTitle;

    public ChannelPartnerError(String errorTitle, String errorMsg, Integer errorCode) {
        this.errorMsg = errorMsg;
        this.errorTitle = errorTitle;
        this.errorCode = errorCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public String getErrorTitle() {
        return errorTitle;
    }

    public Integer getErrorCode() {
        return errorCode;
    }
}
