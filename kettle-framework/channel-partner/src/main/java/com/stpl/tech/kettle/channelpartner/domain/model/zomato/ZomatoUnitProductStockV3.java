package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "outletId",
        "inStock",
        "catalogueVendorEntityIds",
        "comboVendorEntityIds",
        "variantVendorEntityIds",
        "autoTurnOnTime",
        "customTurnOnTime"
})
public class ZomatoUnitProductStockV3 {

    @JsonProperty("outletId")
    private String outletId;

    @JsonProperty("inStock")
    private Boolean inStock;

    @JsonProperty("autoTurnOnTime")
    private String autoTurnOnTime;

    @JsonProperty("customTurnOnTime")
    private String customTurnOnTime;

    
    @JsonProperty("catalogueVendorEntityIds")
    private List<String> catalogueVendorEntityIds = new ArrayList<>();
    
    @JsonProperty("comboVendorEntityIds")
    private List<String> comboVendorEntityIds = new ArrayList<>();

    @JsonProperty("variantVendorEntityIds")
    private List<String> variantVendorEntityIds = new ArrayList<>();

    @JsonProperty("outletId")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outletId")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("inStock")
    public Boolean getInStock() {
        return inStock;
    }

    @JsonProperty("inStock")
    public void setInStock(Boolean inStock) {
        this.inStock = inStock;
    }

    @JsonProperty("autoTurnOnTime")
    public String getAutoTurnOnTime() {
        return autoTurnOnTime;
    }

    @JsonProperty("autoTurnOnTime")
    public void setAutoTurnOnTime(String autoTurnOnTime) {
        this.autoTurnOnTime = autoTurnOnTime;
    }
    @JsonProperty("customTurnOnTime")
    public String getCustomTurnOnTime() {
		return customTurnOnTime;
	}
    @JsonProperty("customTurnOnTime")
	public void setCustomTurnOnTime(String customTurnOnTime) {
		this.customTurnOnTime = customTurnOnTime;
	}

	@JsonProperty("catalogueVendorEntityIds")
	public List<String> getCatalogueVendorEntityIds() {
		return catalogueVendorEntityIds;
	}

    @JsonProperty("catalogueVendorEntityIds")
	public void setCatalogueVendorEntityIds(List<String> catalogueVendorEntityIds) {
		this.catalogueVendorEntityIds = catalogueVendorEntityIds;
	}

    @JsonProperty("comboVendorEntityIds")
	public List<String> getComboVendorEntityIds() {
		return comboVendorEntityIds;
	}

    @JsonProperty("comboVendorEntityIds")
	public void setComboVendorEntityIds(List<String> comboVendorEntityIds) {
		this.comboVendorEntityIds = comboVendorEntityIds;
	}

    @JsonProperty("variantVendorEntityIds")
    public List<String> getVariantVendorEntityIds() {
        return variantVendorEntityIds;
    }

    @JsonProperty("variantVendorEntityIds")
    public void setVariantVendorEntityIds(List<String> variantVendorEntityIds) {
        this.variantVendorEntityIds = variantVendorEntityIds;
    }
}
