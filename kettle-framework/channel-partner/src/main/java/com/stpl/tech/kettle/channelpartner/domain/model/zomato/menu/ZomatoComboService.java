package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "service"
})
public class ZomatoComboService {

    @JsonProperty("service")
    private String service;

    @JsonProperty("service")
    public String getService() {
        return service;
    }

    @JsonProperty("service")
    public void setService(String service) {
        this.service = service;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ZomatoComboService that = (ZomatoComboService) o;
        return Objects.equals(service, that.service);
    }

    @Override
    public int hashCode() {

        return Objects.hash(service);
    }

    @Override
    public String toString() {
        return "ZomatoComboService{" +
                "service='" + service + '\'' +
                '}';
    }
}