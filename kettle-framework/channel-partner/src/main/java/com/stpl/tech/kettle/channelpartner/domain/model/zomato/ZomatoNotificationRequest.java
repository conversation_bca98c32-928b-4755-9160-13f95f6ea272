package com.stpl.tech.kettle.channelpartner.domain.model.zomato;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "order_id",
        "external_order_id",
        "delivery_time",
        "prep_time"
})
public class ZomatoNotificationRequest {
    @JsonProperty("order_id")
    private String orderId;
    @JsonProperty("external_order_id")
    private String externalOrderId;
    @JsonProperty("delivery_time")
    private Integer deliveryTime;
    @JsonProperty("prep_time")
    private Integer prepTime;

    @JsonProperty("order_id")
    public String getOrderId() {
        return orderId;
    }

    @JsonProperty("order_id")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @JsonProperty("external_order_id")
    public String getExternalOrderId() {
        return externalOrderId;
    }

    @JsonProperty("external_order_id")
    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    @JsonProperty("delivery_time")
    public Integer getDeliveryTime() {
        return deliveryTime;
    }

    @JsonProperty("delivery_time")
    public void setDeliveryTime(Integer deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    @JsonProperty("prep_time")
    public Integer getPrepTime() {
        return prepTime;
    }

    @JsonProperty("prep_time")
    public void setPrepTime(Integer prepTime) {
        this.prepTime = prepTime;
    }

    @Override
    public String toString() {
        return "ZomatoNotificationRequest{" +
                "orderId='" + orderId + '\'' +
                ", externalOrderId='" + externalOrderId + '\'' +
                ", deliveryTime=" + deliveryTime +
                ", prepTime=" + prepTime +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        ZomatoNotificationRequest that = (ZomatoNotificationRequest) o;

        return new EqualsBuilder()
                .append(orderId, that.orderId)
                .append(externalOrderId, that.externalOrderId)
                .append(deliveryTime, that.deliveryTime)
                .append(prepTime, that.prepTime)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(orderId)
                .append(externalOrderId)
                .append(deliveryTime)
                .append(prepTime)
                .toHashCode();
    }
}