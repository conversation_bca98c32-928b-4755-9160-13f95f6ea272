#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#
log.base.dir=/usr/share/tomcat8/logs/
spring.application.name=channel-partner
monitoring.user-name=admin
monitoring.user-password=Chaayos12345

# Enable JavaMelody auto-configuration (optional, default: true)
javamelody.enabled=false
# Data source names to exclude from monitoring (optional, comma-separated)
#javamelody.excluded-datasources=secretSource,topSecretSource
# Enable monitoring of Spring services and controllers (optional, default: true)
javamelody.spring-monitoring-enabled=false
# Initialization parameters for JavaMelody (optional)
# See: https://github.com/javamelody/javamelody/wiki/UserGuide#6-optional-parameters
#    log http requests:
javamelody.init-parameters.log=true
#    to exclude images, css, fonts and js urls from the monitoring:
javamelody.init-parameters.url-exclude-pattern=(/webjars/.*|/css/.*|/images/.*|/fonts/.*|/js/.*)
#    to aggregate digits in http requests:
# javamelody.init-parameters.http-transform-pattern: \d+
#    to add basic auth:
javamelody.init-parameters.authorized-users=${monitoring.user-name}:${monitoring.user-password}
#    to change the default storage directory:
javamelody.init-parameters.storage-directory==${log.base.dir}/${spring.application.name}/javamelody
#    to change the default "/monitoring" path:
# javamelody.init-parameters.monitoring-path=/admin/performance

management.endpoints.web.exposure.include=*
javamelody.management-endpoint-monitoring-enabled=true
spring.main.allow-bean-definition-overriding=true

# Enable response compression
server.compression.enabled=true
# The comma-separated list of mime types that should be compressed
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
# Compress the response only if the response size is at least 1KB
server.compression.min-response-size=1024

#management.server.servlet.contextPath=/${spring.application.name}-management
log.location=${log.base.dir}/${spring.application.name}/log
server.servlet.context-path=/${spring.application.name}
spring.mvc.servlet.path=/rest

server.port=9692

environment.type=DEV
mail.receipt.email=<EMAIL>
mail.undelivered.email=<EMAIL>
mail.dummy.customer.id=5
mail.to.email=<EMAIL>
mail.retry.count=2
mail.thread.sleep.time=60000
commission.matrix.path=file:/data/app/kettle/dev/drools/commission_matrix/commission_matrix.xls
server.base.dir=/data/app/kettle/dev
account.verify.email.path=http://dev.accounts.chaayos.com/accounts/verifyemail.php

# jdbc.X
#Transaction Data Source
jdbc.driverClassName=com.mysql.cj.jdbc.Driver
jdbc.url=********************************************************
jdbc.user=root
jdbc.pass=Chaayos123#@!

#Master Data Source
master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.url=***************************************************************
master.jdbc.user=root
master.jdbc.pass=Chaayos123#@!

# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate

#spring.data.mongodb.uri=mongodb://********:27017/admin?authSource=admin&connectTimeoutMS=300000
spring.data.mongodb.uri=**********************************************************************************************
spring.data.mongodb.database=channel_partner_dev
spring.data.mongodb.auto-connect-retry=true


run.validate.filter=true
run.aclInterceptor=false

master.cache.host.details=********
master.cache.host.ports=5701,5702,5703,5704

slack.client.id=23671280437.80816882181
slack.client.secret.key=81d9fc3b94eb18701b15b5ae26f5057e

env.host.ip=*************
socket.io.port=9092


cp.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IkNIQU5ORUwtUEFSVE5FUiIsImVudlR5cGUiOiJQUk9EIiwicGFzc0NvZGUiOiJNM0EyTCIsImlhdCI6MTUyODg0OTkzOH0.RbuNjXkk7fc2xs7KwgJQTQGY6milAkKKDl8M8CSKAzU
swiggy.customer.contact=9599598307
zomato.customer.contact=89475893476

cp.redirect.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImtldHRsZSIsImVudlR5cGUiOiJTUFJPRCIsInBhc3NDb2RlIjoiQlMyMzkiLCJpYXQiOjE1NDAyNzMxMTV9.EslEr7vSXZjKa3OrtHvXNN05pWXG7nhlowrHPIokzLc

base.path.kettle.service=http://********:8080/
base.path.master.service=http://********:8080/
base.path.kettle.crm=http://********:8080/

base.path.dev.url=http://********:8080/

swiggy.api.key=E0Ag0dUKAaVZLSlhP09VQJjq2ys61fUfwL6jy589lKAiaKhkrOS9mWUfOD9Q9aoy
swiggy.confirmation.delay=5
swiggy.menu.authorization.key=dXNlcjpjaGVjaw==
swiggy.menu.token.id=1002651b75492a31
zomato.api.key1=90a38a546af580c03cfd2cc39146daf1
zomato.api.key2=207032352a7689e47c0da5a418dc237a
zomato.confirmation.delay=5

slack.duplicate.orders=true
email.filter.domains=example.com
aws.queue.region=EU_WEST_1
scheduled.menu.push=true

channelpartner.condiment.flag=true

#Inventory Redis Source
redis.host=localhost
redis.port=6379
redis.db.index=2
#redis.pass=R3d15D3V
redis.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImludmVudG9yeS1zZXJ2aWNlIiwiZW52VHlwZSI6IlBST0QiLCJwYXNzQ29kZSI6IkU3MjU0IiwiaWF0IjoxNTIxMDIzMzkwfQ.LbBQlZofH0m1vDu4gmdKjZXwY4f48plQ0DJnZR36zVo

base.path.channel.partner=http://localhost:8080/

notification.testingMode.flag = false

knock.notification.url=http://********:9696/knock-service/rest/notification/send-notification-to-topic
knock.master.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IktOT0NLIiwiZW52VHlwZSI6IlNQUk9EIiwicGFydG5lcklkIjoyMiwicGFzc0NvZGUiOiI3QTMyNjNaRTY1WTVCQzgiLCJpYXQiOjE2NjA4ODYyMDd9.JL01lF-JHfLMKcrQ8r8VOqtOx7hGcAXEnNx4XmBgTYs
#  eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiTDNGVnVCV0haVVNYMDdGSlVRS3FaSnZhZ25Od0RIem9oUnQvalVBeC9zbDNHb2RUTjc2S1RCdTFFbDU4SHlOZzFxdVQ5cG9qcVhWZkRTK2w4ZGRjdnc9PSIsInVuaXRJZCI6MCwidGVybWluYWxJZCI6MCwidXNlcklkIjoxMDAwMjYsImlhdCI6MTY3NDM4Njc5MiwiaXNzdWVyIjoiS05PQ0tfU0VSVklDRSJ9.bC9YzdRMWtwvwYYoZkqfUclExsFbTw7awKwWGyU9v9Q
knock.base.url=http://********:9696/knock-service/

spring.main.allow-circular-references=true
cp.test.mode =true
live.orders.base.url=https://relax.chaayos.com

live.orders.token =eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6ImtldHRsZSIsImVudlR5cGUiOiJTUFJPRCIsInBhc3NDb2RlIjoiQlMyMzkiLCJpYXQiOjE1NDAyNzMxMTV9.EslEr7vSXZjKa3OrtHvXNN05pWXG7nhlowrHPIokzLc
default.zomato.customer.flow=true

is.client.node = true
client.node.ip.details=********:5701,*********:5701,*********:5701,********:5701
edit.order.composition.basis.delivery.remark=true
default.swiggy.customer.id=67456
default.zomato.customer.id=3527255
default.swiggy.customer.flow=true
call.inventory.for.web.orders=true
inventory.base.url=http://stage.kettle.chaayos.com:8787

