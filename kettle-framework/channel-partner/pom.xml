<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kettle-framework</artifactId>
        <groupId>com.stpl.tech.kettle</groupId>
        <version>5.0.31</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>com.stpl.tech.kettle.partner</groupId>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>channel-partner</artifactId>

    <name>channel-partner</name>
    <!-- FIXME change it to the project's website -->
    <url>http://dev.kettle.chaayos.com</url>
    <packaging>war</packaging>
    <properties>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
    </properties>

    <!--    <build>-->
    <!--        <pluginManagement>&lt;!&ndash; lock down plugins versions to avoid using Maven defaults (may be moved to parent pom) &ndash;&gt;-->
    <!--            <plugins>-->
    <!--                <plugin>-->
    <!--                    <groupId>org.apache.maven.plugins</groupId>-->
    <!--                    <artifactId>maven-war-plugin</artifactId>-->
    <!--                    <version>2.6</version>-->
    <!--                    <configuration>-->
    <!--                        <webXml>WebContent/WEB-INF/web.xml</webXml>-->
    <!--                    </configuration>-->
    <!--                </plugin>-->
    <!--                &lt;!&ndash;<plugin>-->
    <!--                    <groupId>org.jvnet.jaxb2.maven2</groupId>-->
    <!--                    <artifactId>maven-jaxb2-plugin</artifactId>-->
    <!--                    <version>0.11.0</version>-->
    <!--                    <executions>-->
    <!--                        <execution>-->
    <!--                            <id>cp-domain-mongo-schema</id>-->
    <!--                            <phase>generate-sources</phase>-->
    <!--                            <goals>-->
    <!--                                <goal>generate</goal>-->
    <!--                            </goals>-->
    <!--                            <configuration>-->
    <!--                                <schemaDirectory>src/main/xsds/mongo</schemaDirectory>-->
    <!--                                <generatePackage>com.stpl.tech.kettle.channelpartner.mongo.data.model</generatePackage>-->
    <!--                                <generateDirectory>${project.build.directory}/generated-sources/xjc-mongo</generateDirectory>-->
    <!--                            </configuration>-->
    <!--                        </execution>-->
    <!--                    </executions>-->
    <!--                </plugin>&ndash;&gt;-->
    <!--            </plugins>-->
    <!--        </pluginManagement>-->
    <!--        <finalName>channel-partner</finalName>-->
    <!--    </build>-->
    <build>
        <finalName>${project.artifactId}</finalName>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-core</artifactId>
            <version>7.59.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-compiler</artifactId>
            <version>7.59.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.kie</groupId>
            <artifactId>kie-spring</artifactId>
            <version>7.59.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>net.bull.javamelody</groupId>
            <artifactId>javamelody-spring-boot-starter</artifactId>
            <version>1.90.0</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.master</groupId>
            <artifactId>master-core</artifactId>
            <version>5.0.31</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.transaction</groupId>
            <artifactId>domain-model</artifactId>
            <version>5.0.31</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.spring</groupId>
            <artifactId>spring-service</artifactId>
            <version>5.0.31</version>
        </dependency>
        <!--<dependency>
            <groupId>com.stpl.tech.kettle</groupId>
            <artifactId>kettle-crm</artifactId>
            <version>3.1.0-SNAPSHOT</version>
        </dependency>-->
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
            <version>1.11.883</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.10</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>2.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.apache.tomcat.embed</groupId>-->
        <!--            <artifactId>tomcat-embed-core</artifactId>-->
        <!--            <version>8.0.32</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>7.7.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy-xml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>json-path</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>xml-path</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>json-schema-validator</artifactId>
            <version>5.3.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
    </dependencies>
</project>
