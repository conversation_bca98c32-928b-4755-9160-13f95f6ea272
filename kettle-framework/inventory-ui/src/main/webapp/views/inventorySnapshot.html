<div class="row" data-ng-init="init()">
    <div class="col-xs-12">
        <h1 class="page-header">
            Inventory Snapshot
        </h1>
    </div>
</div>

<div class="col">

    <div class="row-xs-6 form-group">
        <label>Select Zone</label>
        <select  style="width: 40%"
                 class="form-control"
                 name="unitZone" id="unitZone"
                 ng-change="selectUnitsForZone(selectedZone)"
                 ng-options="option as option for option in unitZones"
                 ng-model="selectedZone">{{option}}
        </select>
        <h4>{{selectedUnits.length}} units selected</h4>

    </div>
    <div class="row-xs-6 form-group">
        <label>Select Unit</label>
        <select
                style="width: 70%"
                class="form-control"
                ui-select2="multiSelectOptions"
                id="inventoryUnit"
                name="inventoryUnit"
                multiple
                data-placeholder="{{unitPlaceHolder}}"
                data-ng-model="selectedUnits"
                data-ng-options="unit as unit.name for unit in filteredUnits track by unit.id"
                ></select>
        <button
        data-ng-click="selectAllUnit()"
        >{{selectAllButtonText}}</button>
<!--        <div ng-dropdown-multiselect="" extra-settings="multiSelectSettings"-->
<!--             options="units" selected-model="selectedUnits" class="region-card"-->
<!--             data-ng-change="setSelectedUnit(selectedUnit)">-->
<!--        </div>-->
    </div>
    <div class="row-xs-6 form-group">
        <label>Product</label>
        <select
                class="form-control"
                ui-select2="selectedUnit"
                id="inventoryProduct"
                name="productUnit"
                multiple
                data-placeholder="Select Product"
                data-ng-model="selectedProducts"
                data-ng-change="addProduct()"
                data-ng-options="trimmedProduct as trimmedProduct.name for trimmedProduct in trimmedProducts track by trimmedProduct.id"
                >
        </select>
    </div>
    <div class="form-group">
        <button style="margin: 10px" class="btn btn-primary pull-right"
                ng-click="getInventoryForProducts()">Download Inventory Sheet
        </button>
    </div>
</div>
