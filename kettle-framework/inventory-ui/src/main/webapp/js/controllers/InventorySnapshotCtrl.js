inventoryApp.controller("InventorySnapshotCtrl",
    ['$rootScope', '$scope', '$state', '$http', '$location', 'APIJson', 'AuthService', 'fileService','$cookieStore', 'Popeye', 'AppUtil',
        function ($rootScope, $scope, $state, $http, $location, APIJson, AuthService,fileService, $cookieStore, Popeye, AppUtil) {

            $scope.isAdmin = false;
            $scope.plot=false;
            $scope.prods=[];
            $scope.prodsNames=[];
            $scope.selectedProducts=null;
            $scope.selectedUnits=[];
            $scope.selectedZone="";
            $scope.inventoryData=null;
            $scope.trimmedProducts=[]
            $scope.selectAllButtonText="Select All Unit"
            $scope.unitPlaceHolder="Select Unit"
            $scope.unitZones=['NORTH','SOUTH','EAST','WEST'];
            $scope.filteredUnits=[];
            $scope.unitZonePlaceholder="Select Zone"
            $scope.isSelectAllButton=true
            $scope.init = function () {
                if (AppUtil.getCurrentUserId() == 120458 || AppUtil.getCurrentDesignation() == "Admin") {
                    $scope.isAdmin = true;
                }
                $scope.getUnits(AppUtil.getCurrentUserId());
                $scope.getProductsData();
                $scope.productMap = {};
                // $scope.getProductBasicDetail();
                $scope.trimmedProducts = [];
                $scope.selectedProducts = [];
            };
            $scope.getUnits = function (amId) {
                $rootScope.showFullScreenLoader = true;
                if ($scope.isAdmin) {
                    $http({
                        method: 'GET',
                        url: APIJson.urls.unitMetaData.activeUnits,
                        headers: "Content-type: application/json",
                        params: {
                            'category': 'ALL'
                        }
                    }).then(function success(response) {
                        $scope.units = response.data;
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    $http({
                        method: 'POST',
                        url: APIJson.urls.userManagement.activeUnitsForUser,
                        headers: "Content-type: application/json",
                        data: {
                            employeeId: amId,
                            onlyActive: true
                        }
                    }).then(function success(response) {
                        $scope.units = response.data;
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.getProductsData = function (unit){
                $http({
                    method: 'GET',
                    url: APIJson.urls.unitMetaData.trimmedProduct,
                    headers: "Content-type: application/json"
                }).then(function success(response) {
                    $scope.trimmedProducts = response.data;
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            }
            $scope.getInventoryForProducts=function (){
                console.log($scope.selectedProducts);
                $rootScope.showFullScreenLoader = true;
                var productList = []
                for(var i in $scope.selectedProducts){
                    var dimensions = $scope.selectedProducts[i].dimensions;
                    for(var j in dimensions){
                        productList.push({
                            'id':$scope.selectedProducts[i].id,
                            'name':$scope.selectedProducts[i].name,
                            'dimension':$scope.selectedProducts[i].dimensions[j]
                        })
                    }
                }
                var unitList=[]
                $scope.selectedUnits?.map(function (unit){
                    unitList.push({id:unit.id,name:unit.name});
                })
                var data = {
                    "units":unitList,
                    "products":productList
                }
                var zone = $scope.selectedZone;
                var InventoryUpdateAPI = APIJson.urls.inventory;
                var zoneAPI;
                if(zone!=null){
                    zoneAPI = InventoryUpdateAPI.baseUrl + "/" + zone.toLowerCase() + InventoryUpdateAPI.postfix + InventoryUpdateAPI.getProductWiseInventory;
                }else{
                    zoneAPI = InventoryUpdateAPI.baseUrl + "/north" + InventoryUpdateAPI.postfix + InventoryUpdateAPI.getProductWiseInventory;
                }
                console.log(zoneAPI);

                $http({
                    method: 'POST',
                    url: zoneAPI,
                    data: data,
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).then(function success(response) {
                    if(response.status === 200 && response.data){
                        var fileName = "INVENTORY_DATA" + " - " + Date.now() + ".xls";
                        var blob = new Blob(
                            [response.data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                        $scope.inventoryData=response.data;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                    alert("Something went wrong");
                });
            }

            $scope.selectUnitsForZone = function(value){
                var units=$scope.units;
                console.log(units);
                var zone=value;
                var filteredUnits=units.filter(function (unit){
                    return unit.unitZone==zone;
                });
                console.log(filteredUnits);
                console.log(zone);
                $scope.filteredUnits=filteredUnits;
            }

            $scope.addProduct = function (){
                if($scope.selectedProducts.length > 5){
                    var dummy = []
                    for(var i in $scope.selectedProducts){
                        if (i>=5) break;
                        dummy.push($scope.selectedProducts[i])
                    }
                    $scope.selectedProducts=null;
                    $scope.selectedProducts=dummy;
                }
            }

            $scope.selectAllUnit = function () {
                if ($scope.isSelectAllButton) {
                    $scope.selectAllButtonText = "Deselect All Unit";
                    var data = [];
                    $scope.units.map(function (unit) {
                        data.push(unit);
                    });
                    $scope.selectedUnits = data;
                    $scope.unitPlaceHolder = data.length + " units selected";
                } else {
                    $scope.selectAllButtonText = "Select All Unit";
                    $scope.unitPlaceHolder = "Select Unit";
                    $scope.selectedUnits = [];
                }
                $scope.isSelectAllButton = !$scope.isSelectAllButton;
            }
        }]);
