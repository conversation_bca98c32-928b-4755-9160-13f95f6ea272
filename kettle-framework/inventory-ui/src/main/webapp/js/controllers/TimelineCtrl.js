inventoryApp.controller("TimelineCtrl",
    ['$rootScope', '$scope', '$state', '$http', '$location', 'APIJson', 'AuthService', '$cookieStore', 'Popeye', 'AppUtil',
        function ($rootScope, $scope, $state, $http, $location, APIJson, AuthService, $cookieStore, Popeye, AppUtil) {

            $scope.isAdmin = false;
            $scope.plot=false;
            var myChart=null;
            var removeTotal = 0;
            var addTotal = 0;
            var addTotalDate = 0;
            var removeTotalDate = 0;
            var i=0;
            $scope.prods=[];
            $scope.prodsNames=[];
            var unit=null;
            $scope.init = function () {
                if (AppUtil.getCurrentUserId() == 120458 || AppUtil.getCurrentDesignation() == "Admin") {
                    $scope.isAdmin = true;
                }
                $scope.getUnits(AppUtil.getCurrentUserId());
                $scope.productMap = {};
                // $scope.getProductBasicDetail();
                $scope.reset();
                $scope.selectedUnit = null;
                $scope.selectedProduct = null;
                $scope.prodsNames=[];
            };

            $scope.reset = function () {
                $scope.timelineData = null;
            };

            $scope.getUnits = function (amId) {
                $rootScope.showFullScreenLoader = true;
                if ($scope.isAdmin) {
                    $http({
                        method: 'GET',
                        url: APIJson.urls.unitMetaData.activeUnits,
                        headers: "Content-type: application/json",
                        params: {
                            'category': 'ALL'
                        }
                    }).then(function success(response) {
                        $scope.units = response.data;
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    $http({
                        method: 'POST',
                        url: APIJson.urls.userManagement.activeUnitsForUser,
                        headers: "Content-type: application/json",
                        data: {
                            employeeId: amId,
                            onlyActive: true
                        }
                    }).then(function success(response) {
                        $scope.units = response.data;
                        $rootScope.showFullScreenLoader = false;
                    }, function error(response) {
                        console.log("error:" + response);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };

            $scope.setSelectedUnit = function (selectedUnit) {
                $rootScope.showFullScreenLoader = true;
                $scope.selectedUnit = selectedUnit;
                $scope.prods = [];
                $scope.prodsNames = [];
                $scope.getProductBasicDetail();
                $scope.inventory = [];
            };

            $scope.setSelectedProduct = function (selectedProduct) {
                $scope.selectedProduct = selectedProduct;
                $scope.destroyChart();
            };

            $scope.setSelectedDate = function (selectedDate){
                $scope.selectedDate = selectedDate;
                $scope.destroyChart();
            };

            $scope.destroyChart = function (){
                if(myChart != null){
                    myChart.destroy();
                }
            };

            $scope.removeDate = function(){
                $scope.setSelectedDate(null);
            };

            $scope.getUnitTimeLine = function () {
                $rootScope.showFullScreenLoader = true;
                var zone = $scope.selectedUnit.unitZone;
                var InventoryAPI = APIJson.urls.inventory;
                var zoneAPI;
                if(zone!=null){
                    zoneAPI = InventoryAPI.baseUrl + "/" + zone.toLowerCase() + InventoryAPI.postfix + InventoryAPI.timeline;
                }else{
                    zoneAPI = InventoryAPI.baseUrl + InventoryAPI.postfix + InventoryAPI.timeline;
                }
                console.log(zoneAPI);
                $http({
                    method: 'GET',
                    url: zoneAPI,
                    params: {
                        'unitId': $scope.selectedUnit.id,
                    }
                }).then(function success(response) {
                    $scope.reset();
                    $scope.timelineData = response.data;
                    $scope.timelineData.map(function (data) {
                        if(data.details === null){
                            return false;
                        }
                        else{
                        data.details.map(function (detail) {
                            detail.name = $scope.productMap[detail.id];
                            if($scope.prods.indexOf(detail.id) === -1){
                                 if(detail.name != undefined){
                                     $scope.prods.push(detail.id);
                                     $scope.prodsNames.push(detail.name);}
                            }
                        });}
                    });
                    $rootScope.showFullScreenLoader = false;
                }, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.getProductBasicDetail = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: APIJson.urls.scmProductManagement.getProductBasicDetail,
                    params: {
                        'unitId': $scope.selectedUnit.id,
                    }
                }).then(function success(response) {
                    if(response.status === 200){
                        $scope.products = response.data;
                        $scope.productMap = {};
                        $scope.products.map(function (product) {
                            $scope.productMap[product.productId] = product.productName;
                            });
                        $scope.getUnitTimeLine();
                        $rootScope.showFullScreenLoader = false;
                }}, function error(response) {
                    console.log("error:" + response);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.plotChart = function () {
                if ($scope.selectedUnit == null || $scope.selectedProduct == null) {
                    if ($scope.selectedUnit == null){
                        alert("Please select Unit");
                        return false;
                    }
                    else if ($scope.selectedProduct == null) {
                        alert("Please select Product");
                        return false;
                    }
                }
                var labelsRem = [];
                var labelsAdd = [];
                var stockDataRemove = [];
                var stockDataAdd = [];
                var stockDataDateRemove = [];
                var stockDataDateAdd = [];
                var labelsDate = [];
                addTotalDate = 0;
                addTotal = 0;
                removeTotal = 0;
                removeTotalDate = 0;
                    $scope.timelineData.map(function (data) {
                        if (data.details === null) {
                            return false;
                        }else {
                            data.details.map(function (detail) {
                                var idx = $scope.prodsNames.indexOf($scope.selectedProduct);
                                var prodId = $scope.prods[idx];
                                if (detail.id === prodId) {
                                    var dateOfEvent = new Date(data.eventTime);
                                    var finDateOfEvent = new Date(dateOfEvent.setMinutes(dateOfEvent.getMinutes()-330)).toLocaleDateString();
                                    if (data.action === 'REMOVE') {
                                        removeTotal += detail.q;
                                        unit = detail.u;
                                        if ($scope.selectedDate != null) {
                                            var selectedDate = $scope.selectedDate.toLocaleDateString();
                                            if (angular.equals(selectedDate, finDateOfEvent)) {
                                                removeTotalDate += detail.q;
                                                stockDataDateRemove.push({
                                                    id: detail.id,
                                                    time: data.eventTime,
                                                    source: data.source,
                                                    orderId: data.orderId,
                                                    qty: detail.q
                                                });
                                                labelsDate.push(new Date(data.eventTime).toGMTString().slice(17,25));
                                                unit = detail.u;
                                            }
                                        }
                                        stockDataRemove.push({
                                            id: detail.id,
                                            time: data.eventTime,
                                            source: data.source,
                                            orderId: data.orderId,
                                            qty: detail.q
                                        });
                                        labelsRem.push(finDateOfEvent);
                                    }
                                    else if (data.action === 'ADD') {
                                        addTotal += detail.q;
                                        unit = detail.u;
                                        if ($scope.selectedDate != null) {
                                            var selectedDate = $scope.selectedDate.toLocaleDateString();
                                            if (angular.equals(selectedDate, finDateOfEvent)) {
                                                addTotalDate += detail.q;
                                                stockDataDateAdd.push({
                                                    id: detail.id,
                                                    time: data.eventTime,
                                                    source: data.source,
                                                    orderId: data.orderId,
                                                    qty: detail.q
                                                });
                                                labelsDate.push(new Date(data.eventTime).toGMTString().slice(17,25));
                                                unit = detail.u;
                                            }
                                        }
                                        stockDataAdd.push({
                                            id: detail.id,
                                            time: data.eventTime,
                                            source: data.source,
                                            orderId: data.orderId,
                                            qty: detail.q
                                        });
                                        labelsAdd.push(finDateOfEvent);
                                    }
                                    // labels.push(new Date(data.eventTime).toLocaleDateString());
                                }
                            })
                        }
                    });

                    var finAdd=[];
                    var j=0;
                    for(var i=0;i<stockDataRemove.length+stockDataAdd.length;i++){
                        if(i<stockDataRemove.length){
                            finAdd[i]={};
                        }else{
                            finAdd[i]=stockDataAdd[j];
                            j++;
                        }
                    }

                    var finDateAdd = [];
                    var k=0;
                    for(var i=0;i<stockDataDateRemove.length+stockDataDateAdd.length;i++){
                        if(i<stockDataDateRemove.length){
                            finDateAdd[i]={};
                        }else{
                            finDateAdd[i]=stockDataDateAdd[k];
                            k++;
                        }
                    }

                    var labels =angular.copy(labelsRem);
                    for(var i=0;i<labelsAdd.length;i++){
                        labels.push(labelsAdd[i]);
                    }

                var data = {
                        labels: $scope.selectedDate != null ? labelsDate.length === 0 ? "No Data Present" : labelsDate : labels,
                        datasets: [{
                            label: $scope.selectedProduct + " - " + $scope.selectedUnit.name + " REMOVE" ,
                            backgroundColor: 'rgb(255, 10, 10)',
                            borderColor: 'rgb(255, 99, 132)',
                            data: $scope.selectedDate != null ?(stockDataDateRemove.length === 0 ? [] : stockDataDateRemove): stockDataRemove,
                            tension: 0.1
                        },
                            {
                                label: $scope.selectedProduct + " - " + $scope.selectedUnit.name + " ADD",
                                backgroundColor: 'rgb(10, 255, 50)',
                                borderColor: 'rgb(255, 99, 132)',
                                data: $scope.selectedDate != null ? (stockDataDateAdd.length === 0 ? [] : finDateAdd) : finAdd,
                                tension: 0.1
                            }
                        ]
                    };
                    var config = {
                        type: 'bar',
                        data: data,
                        options: {
                            parsing: {
                                xAxisKey: 'time',
                                yAxisKey: 'qty',
                            },

                            plugins: {
                                title: {
                                    display: true,
                                    text: $scope.selectedDate != null ? (labelsDate.length > 0 ?
                                        "Chart of " + $scope.selectedDate.toLocaleDateString() + " || Quantity Removed : " + removeTotalDate.toFixed(5) + unit + " || Quantity Added : " + addTotalDate.toFixed(5) + unit :
                                        "Chart " + $scope.selectedDate.toLocaleDateString() + " No Data Present") : "Chart || Quantity Removed : " + removeTotal.toFixed(5) + unit + " || Quantity Added : " + addTotal.toFixed(5) + unit,
                                    font: {
                                        size: 20,
                                        weight: 'bold',
                                        lineHeight: 1.2,
                                    },
                                    padding: {
                                        top: 10,
                                        bottom: 30
                                    }
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function (tooltipItem, data) {
                                            var label=myChart.data.datasets[tooltipItem.datasetIndex].label;
                                            var source=myChart.data.datasets[tooltipItem.datasetIndex].data[tooltipItem.dataIndex].source;
                                            var quantity = tooltipItem.formattedValue;
                                            var res = label+"- Source : "+source+" : "+quantity+unit;
                                            if(source === undefined){
                                                return null;
                                            }else {
                                                return res;
                                            }
                                        }
                                    }
                                }

                            },
                            scales: {
                                x: {
                                    display: true,
                                    title: {
                                        display: true,
                                        text: labelsDate.length>0 ? 'Time' : 'Date',
                                        color: '#911',
                                        font: {
                                            family: 'Comic Sans MS',
                                            size: 20,
                                            weight: 'bold',
                                            lineHeight: 1.2,
                                        },
                                        padding: {top: 10, left: 0, right: 0, bottom: 0}
                                    }
                                },
                                y: {
                                    display: true,
                                    title: {
                                        display: true,
                                        text: 'Quantity',
                                        color: '#191',
                                        font: {
                                            family: 'Comic Sans MS',
                                            size: 20,
                                            weight: 'bold',
                                            lineHeight: 1.2,
                                        },
                                        padding: {top: 20, left: 0, right: 10, bottom: 10}
                                    }
                                }
                            }
                        }
                    };
                    myChart = new Chart(
                        document.getElementById('myChart'),
                        config
                    );
                    labels = [];
                    stockDataRemove = [];
                    stockDataAdd = [];
                    stockDataDateRemove = [];
                    stockDataDateAdd = [];
                    labelsDate = [];
                }
        }]);
