/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
inventoryApp.controller("LoginController", function ($state, $scope, $location, $http, $cookieStore, APIJson, AuthService, $rootScope, AppUtil) {

    $scope.init = function () {
        $scope.userId = null;
        $scope.passcode = null;
        $scope.showMessage = false;
        $scope.loginText = "Login";
    };

    $scope.login = function () {

        var userObj = {
            userId: $scope.userId,
            password: $scope.passcode,
            unitId: 0,
            terminalId: 0,
            macAddress: $location.search().mac,
            application: "KETTLE_ANALYTICS"
        };

        $scope.loginText = "Logging in...";
        $scope.loginDisabled = true;

        $http({
            method: 'post',
            url: APIJson.urls.users.login,
            // url: AppUtil.restUrls.users.adminLogin,
            data: userObj
        }).then(function success(response) {
            if (response.data.sessionKeyId == null) {
                $scope.authSuccess = false;
                $scope.authMessage = 'Credentials are not correct!';
                $scope.showMessage = true;
                $scope.loginText = "Login";
                $scope.loginDisabled = false;
            } else {
                $scope.authSuccess = true;
                $scope.authMessage = 'Authentication successful. Redirecting to dashboard!';
                $scope.showMessage = true;

                AppUtil.setUserData(response.data.user);
                $rootScope.userData = AppUtil.getUserData();
                $rootScope.aclData = response.data.acl;
                AppUtil.setAcl(response.data.acl);

                response.data.permissions = null;
                $cookieStore.put('inventoryGlobals', response.data);
                AuthService.setAuthorization(response.data.jwtToken);
                $state.go("dashboard");

            }
        }, function error(response) {
            console.log("error:" + response);
            $scope.authSuccess = false;
            $scope.authMessage = 'Error in verifying credentials. Try again!';
            $scope.showMessage = true;
            $scope.loginText = "Login";
            $scope.loginDisabled = false;
        });
    };

    $scope.removeAlert = function () {
        $scope.showMessage = false;
    }
});