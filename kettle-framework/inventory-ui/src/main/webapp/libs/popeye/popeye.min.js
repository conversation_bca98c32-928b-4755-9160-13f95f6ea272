/**
* angular-popeye
* A simple modal library for AngularJS applications

* <AUTHOR> <<EMAIL>>
* @copyright Pathgather 2015
* @license MIT
* @link https://github.com/Pathgather/popeye
* @version 1.0.5
*/

(function(){var a,b,c=function(a,b){return function(){return a.apply(b,arguments)}};if(b=function(a){"use strict";var b;return b=a.module("pathgather.popeye",[]),b.provider("Popeye",function(){var b;return b={defaults:{containerTemplate:'<div class="popeye-modal-container">\n  <div class="popeye-modal">\n    <a class="popeye-close-modal" href ng-click="$close()"></a>\n  </div>\n</div>',containerTemplateUrl:null,bodyClass:"popeye-modal-open",containerClass:null,modalClass:null,locals:null,resolve:null,scope:null,controller:null,keyboard:!0,click:!0},$get:["$q","$animate","$rootScope","$document","$http","$templateCache","$compile","$controller","$injector",function(d,e,f,g,h,i,j,k,l){var m,n,o;return n=null,o=null,g.on("keydown",function(a){return 27===a.which&&null!=n&&n.options.keyboard?n.close({reason:"keyboard"}):void 0}),m=function(){function m(e){if(null==e&&(e={}),this.handleError=c(this.handleError,this),this.close=c(this.close,this),this.open=c(this.open,this),this.resolve=c(this.resolve,this),null==e.template&&null==e.templateUrl)throw new Error("template or templateUrl must be provided");this.options=a.extend(a.copy(b.defaults),e),this.resolvedDeferred=d.defer(),this.resolved=this.resolvedDeferred.promise,this.openedDeferred=d.defer(),this.opened=this.openedDeferred.promise,this.closedDeferred=d.defer(),this.closed=this.closedDeferred.promise}return m.prototype.resolve=function(){return this.resolving?this.resolved:(this.resolving=!0,d.when({}).then(function(b){return function(){var c,e;return c=a.extend({modal:b},b.options.locals),e=a.extend({},b.options.resolve),a.forEach(e,function(b,d){return c[d]=a.isString(b)?l.get(b):l.invoke(b,null,c)}),d.all(c)}}(this)).then(function(b){return function(c){return b.scope=null!=b.options.scope?b.options.scope:f.$new(),b.scope.$close=function(){return b.close.apply(b,arguments)},b.options.controller&&(b.controller=k(b.options.controller,a.extend({$scope:b.scope},c))),b.resolvedDeferred.resolve(b)}}(this),function(a){return function(b){return a.handleError(b)}}(this)),this.resolved)},m.prototype.open=function(){var b;return this.opening?this.opened:(this.opening=!0,b=null!=o?o=o.then(function(a){return function(b){return b.close().then(function(){return a})}}(this)):null!=n?o=n.close().then(function(a){return function(){return a}}(this)):(o=this.opened,d.when()),b.then(function(b){return function(){return b.resolve().then(function(){var c;if(null==b.scope)throw new Error("@scope is undefined");return c=null!=b.options.containerTemplate?d.when({data:b.options.containerTemplate}):null!=b.options.containerTemplateUrl?h.get(b.options.containerTemplateUrl,{cache:i}):d.reject("Missing containerTemplate or containerTemplateUrl"),c.then(function(c){var f,k;return f=a.element(c.data),b.options.containerClass&&f.addClass(b.options.containerClass),k=null!=b.options.template?d.when({data:b.options.template}):null!=b.options.templateUrl?h.get(b.options.templateUrl,{cache:i}):d.reject("Missing containerTemplate or containerTemplateUrl"),k.then(function(c){var d,h;return a.element(f[0].querySelector(".popeye-modal")).append(c.data),b.options.click&&f.on("click",function(a){return a.target===a.currentTarget?b.close():void 0}),b.container=j(f)(b.scope),b.element=a.element(b.container[0].querySelector(".popeye-modal")),b.options.modalClass&&b.element.addClass(b.options.modalClass),d=g.find("body"),d[0].lastChild&&(h=a.element(d[0].lastChild)),b.options.bodyClass&&d.addClass(b.options.bodyClass),e.enter(b.container,d,h).then(function(){return n=b,b.openedDeferred.resolve(b)})})})})}}(this))["catch"](function(a){return function(b){return a.handleError(b)}}(this))["finally"](function(){return o=null}),this.opened)},m.prototype.close=function(a){return this.closing?this.closed:(this.closing=!0,this.opened.then(function(b){return function(){if(null==b.container)throw new Error("@container is undefined");return e.leave(b.container).then(function(){return n=null,b.options.scope||b.scope.$destroy(),g.find("body").removeClass(b.options.bodyClass),b.closedDeferred.resolve(a)},function(a){return b.handleError(a)})}}(this)),this.closed)},m.prototype.handleError=function(a){return this.resolvedDeferred.reject(a),this.openedDeferred.reject(a),this.closedDeferred.reject(a)},m}(),{openModal:function(a){var b;return null==a&&(a={}),b=new m(a),b.open(),b},closeCurrentModal:function(a){return null!=n&&n.close(a),n},isModalOpen:function(){return!!n}}}]}})},null!=("undefined"!=typeof window&&null!==window?window.angular:void 0))b(window.angular);else{if("function"!=typeof require)throw new Error("Could not find angular on window nor via require()");a=require("angular"),b(a)}"undefined"!=typeof module&&null!==module&&(module.exports="pathgather.popeye")}).call(this);