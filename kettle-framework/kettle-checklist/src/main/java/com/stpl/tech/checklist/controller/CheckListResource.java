/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.checklist.controller;

import static com.stpl.tech.checklist.core.CheckListConstants.API_VERSION;
import static com.stpl.tech.checklist.core.CheckListConstants.CHECKLIST_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.checklist.core.CheckListConstants.SEPARATOR;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.checklist.core.CheckListUtils;
import com.stpl.tech.checklist.dao.CheckListInstanceRepository;
import com.stpl.tech.checklist.dao.CheckListItemRepository;
import com.stpl.tech.checklist.dao.CheckListScheduleRepository;
import com.stpl.tech.checklist.model.CheckListInstance;
import com.stpl.tech.checklist.model.CheckListItem;
import com.stpl.tech.checklist.model.CheckListSchedule;
import com.stpl.tech.master.core.exception.DataUpdationException;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CHECKLIST_SERVICES_ROOT_CONTEXT)
public class CheckListResource extends AbstractController {

	private static final Logger LOG = LoggerFactory.getLogger(CheckListResource.class);

	@Autowired
	private CheckListInstanceRepository instanceService;

	@Autowired
	private CheckListScheduleRepository scheduleService;

	@Autowired
	private CheckListItemRepository checkListItemService;

	@Autowired
	private CheckListGenerator generator;

	@RequestMapping(method = RequestMethod.GET, value = "checklists", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CheckListInstance> getAllChecklistForUnit(@RequestParam(name = "unitId") final int unitId) {
		LOG.info("Request to get checklists for unitId: {}", unitId);
		Date businessDate = CheckListUtils.getCurrentDateIST();
		return instanceService.findByUnitIdAndBusinessDate(unitId, CheckListUtils.getBusinessDateString(businessDate));
	}

	@RequestMapping(method = RequestMethod.POST, value = "checklists/submit", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CheckListInstance updateCheckListInstance(@RequestBody final CheckListInstance instance)
			throws DataUpdationException {
		LOG.info("Request to update checklist instance");

		Optional<CheckListInstance> result = null;
		Optional<CheckListInstance> originalInstance = instanceService.findById(instance.get_id());

		if (originalInstance.isPresent() && originalInstance.get().getSubmissionTime() == null) {
			instance.setSubmissionTime(CheckListUtils.getCurrentTimestamp());
		}

		Optional<CheckListSchedule> schedule = scheduleService.findById(instance.getCheckList().getSchedule().get_id());
		int editTimeLimit=0;
		if(schedule.isPresent()){
			editTimeLimit = schedule.get().getEditableBeforeMinutes();
		}
		if (originalInstance.isPresent() && CheckListUtils.getCurrentTimestamp()
				.compareTo(CheckListUtils.addMinutes(originalInstance.get().getStartTime(), editTimeLimit)) < 0) {
			instance.setLastUpdateTime(CheckListUtils.getCurrentTimestamp());
			instanceService.save(instance);
			checkListItemService.saveAll(instance.getCheckList().getItems());
			result = instanceService.findById(instance.get_id());
		} else {
			throw new DataUpdationException("Exceeded Editing/Submission Time Limit");
		}
		return result.orElse(null);
	}

	@RequestMapping(method = RequestMethod.POST, value = "checklists/date", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CheckListInstance> getAllChecklistForBusinessDate(@RequestParam(name = "unitId") final int unitId,
			@RequestParam(name = "unitId") final Date businessDate) {
		LOG.info("Request to get checklists for unitId: {} for Date {}", unitId, businessDate);
		return instanceService.findByUnitIdAndBusinessDate(unitId,
				new SimpleDateFormat("YYYY-MM_DD").format(businessDate));
	}

	@RequestMapping(method = RequestMethod.GET, value = "generate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean createByGenerator() {
		LOG.info("Generating checklists using generator");
		try {
			generator.checkListInstanceGenerator();
			return true;
		} catch (Exception e) {
			LOG.error("Error while generating checklists", e);
		}
		return false;
	}

	// @RequestMapping(method = RequestMethod.GET, value = "createDummy",
	// produces = MediaType.APPLICATION_JSON)
	// @ResponseStatus(HttpStatus.OK)
	// @ResponseBody
	// public CheckListInstance createDummy() {
	// LOG.info("Creating dummy");
	// return instanceService.save(createDummyData(new Random().nextInt(100)));
	// }

	// @RequestMapping(method = RequestMethod.GET, value = "create", produces =
	// MediaType.APPLICATION_JSON)
	// @ResponseStatus(HttpStatus.OK)
	// @ResponseBody
	// public boolean createCheckListInstance() {
	// LOG.info("Creating Dummy form existing checklists");
	// generator.checkListInstanceGenerator();
	// // createDummyFromDB();
	// return true;
	// }

	// private void createDummyFromDB() {
	//
	// List<CheckList> templateList =
	// checkListService.findByTemplateAndStatus(true, AppConstants.ACTIVE);
	// CheckListInstance instance;
	// for (CheckList checkList : templateList) {
	//
	// int count = new Random().nextInt(100);
	//
	// instance = new CheckListInstance();
	// instance.setBusinessDate(CheckListUtils.getCurrentDateIST());
	// instance.setUnitId(10000);
	// instance.setUnitName("DummyUnit-" + count);
	// instance.setUserId(123456);
	// instance.setUserName("DummyUser-" + count);
	// instance.setCheckList(createNewCheckListObject(checkList));
	// instance.setStatus(AppConstants.ACTIVE);
	// instance.setUserRole("MANAGER");
	// instance.setSubmissionTime(null);
	// instance.setLastUpdateTime(null);
	// instance.setStartTime(AppUtils.getCurrentTimestamp());
	// instance.setEndTime(CheckListUtils.addMinutes(AppUtils.getCurrentTimestamp(),
	// checkList.getSchedule().getEditableBeforeMinutes()));
	//
	// instanceService.save(instance);
	// }
	// }
	//
	// private CheckList createNewCheckListObject(CheckList checkList) {
	// CheckList checkListClone = new CheckList();
	// checkListClone.setCategory(checkList.getCategory());
	// checkListClone.setDescription(checkList.getDescription());
	// checkListClone.setName(checkList.getName());
	// checkList.getSchedule().setEditableBeforeMinutes(200000);
	// checkListClone.setSchedule(checkList.getSchedule());
	// checkListClone.setStation(checkList.getStation());
	// checkListClone.setStatus(checkList.getStatus());
	// checkListClone.setTemplate(false);
	// checkListClone.setType(checkList.getType());
	// // replicate Items
	// for (CheckListItem item : checkList.getItems()) {
	// CheckListItem itemClone = new CheckListItem();
	// itemClone.setAction(item.getAction());
	// itemClone.setDescription(item.getDescription());
	// itemClone.setMandatory(item.isMandatory());
	// itemClone.setMultiValued(item.isMultiValued());
	// itemClone.setOrdering(item.getOrdering());
	// itemClone.setProbableValues(item.getProbableValues());
	// itemClone.setResponse(item.getResponse());
	// itemClone.setResponseType(item.getResponseType());
	// itemClone.setStatus(item.getStatus());
	// itemClone.setStep(item.getStep());
	// checkListClone.getItems().add(itemClone);
	// }
	// return checkListClone;
	// }

	// private CheckListInstance createDummyData(int count) {
	//
	// ClosingSchedule closingSchedule = new ClosingSchedule();
	// closingSchedule.setMinutesDifference(10);
	// closingSchedule.setAfterClosing(true);
	//
	// OpeningSchedule openingSchedule = new OpeningSchedule();
	// openingSchedule.setBeforeOpening(true);
	// openingSchedule.setMinutesDifference(10);
	//
	// CheckListSchedule schedule = new CheckListSchedule();
	// schedule.setEditableBeforeMinutes(15);
	// schedule.setClosing(closingSchedule);
	//
	// // schedule.setOpening(openingSchedule);
	// schedule.setFrequency(CheckListFrequency.WEEKLY);
	// MonthlySchedule monthlySchedule = new MonthlySchedule();
	// DayOfMonthSchedule dayOfMonthSchedule = new DayOfMonthSchedule();
	// dayOfMonthSchedule.setMonthEnd(true);
	// dayOfMonthSchedule.setDayOfMonth(3);
	// dayOfMonthSchedule.getHoursOfTheDay().add(14);
	// dayOfMonthSchedule.getHoursOfTheDay().add(16);
	// dayOfMonthSchedule.getHoursOfTheDay().add(17);
	// dayOfMonthSchedule.getHoursOfTheDay().add(18);
	// monthlySchedule.getDaysOfMonth().add(dayOfMonthSchedule);
	// DayOfMonthSchedule dayOfMonthSchedule2 = new DayOfMonthSchedule();
	// dayOfMonthSchedule2.setMonthEnd(false);
	// dayOfMonthSchedule2.setDayOfMonth(15);
	// dayOfMonthSchedule2.getHoursOfTheDay().add(24);
	// dayOfMonthSchedule2.getHoursOfTheDay().add(25);
	// dayOfMonthSchedule2.getHoursOfTheDay().add(26);
	// monthlySchedule.getDaysOfMonth().add(dayOfMonthSchedule2);
	// schedule.setMonthly(monthlySchedule);
	// schedule.setNotificationRequired(true);
	// schedule.setNotifyBeforeMinutes(10);
	// ShiftHandoverSchedule shiftHandOver = new ShiftHandoverSchedule();
	// shiftHandOver.setMinutesDifference(10);
	// shiftHandOver.setAfterHandover(true);
	// shiftHandOver.setBeforeHandover(false);
	// schedule.setShiftHandover(shiftHandOver);
	// WeeklySchedule weeklySchedule = new WeeklySchedule();
	// DayOfWeekSchedule dayOfTheWeek = new DayOfWeekSchedule();
	// dayOfTheWeek.setDayOfWeek(2);
	// weeklySchedule.getDaysOfWeek().add(dayOfTheWeek);
	// schedule.setWeekly(weeklySchedule);
	//
	// IntradaySchedule intradaySchedule = new IntradaySchedule();
	// intradaySchedule.getHoursOfTheDay().add(21);
	// intradaySchedule.getHoursOfTheDay().add(22);
	// intradaySchedule.getHoursOfTheDay().add(23);
	// intradaySchedule.getHoursOfTheDay().add(24);
	// intradaySchedule.getHoursOfTheDay().add(25);
	//
	// schedule.setIntraDay(intradaySchedule);
	// schedule.setOpening(openingSchedule);
	//
	// CheckList checkList = new CheckList();
	// checkList.setDescription("Dummy Checklist description");
	// checkList.setName("Dummy Checklist-" + count);
	// checkList.setSchedule(schedule);
	//
	// checkList.setStatus(AppConstants.ACTIVE);
	// checkList.setType(CheckListType.PERIODIC);
	// checkList.setCategory(CheckListCategory.OPERATIONAL);
	// checkList.setTemplate(true);
	// checkList.setStation(CheckListStation.values()[new Random().nextInt(5)]);
	// checkList.getUnitCategory().add(UnitCategory.CAFE.name());
	// checkList.getUnitCategory().add(UnitCategory.DELIVERY.name());
	//
	// CheckListItem item = new CheckListItem();
	// item.setAction("Some Item Action to do something");
	// item.setDescription("Item Description");
	// item.setOrdering(1);
	// item.setStep("Item Step");
	// item.setResponse("false");
	// item.setResponseType(Boolean.class.getName());
	// item.setMandatory(true);
	// item.setMultiValued(true);
	// item.getProbableValues().add(AppConstants.YES);
	// item.getProbableValues().add(AppConstants.NO);
	// item.setStatus(AppConstants.ACTIVE);
	// checkList.getItems().add(item);
	//
	// CheckListInstance instance = new CheckListInstance();
	// instance.setBusinessDate(CheckListUtils.getCurrentDateIST());
	// instance.setUnitId(10000);
	// instance.setUnitName("DummyUnit-" + count);
	// instance.setUserId(123456);
	// instance.setUserName("DummyUser-" + count);
	// instance.setCheckList(checkList);
	// instance.setStatus(AppConstants.ACTIVE);
	// instance.setUserRole("MANAGER");
	// instance.setSubmissionTime(AppUtils.getCurrentTimestamp());
	// instance.setLastUpdateTime(AppUtils.getCurrentTimestamp());
	// instance.setStartTime(AppUtils.getCurrentTimestamp());
	// instance.setEndTime(AppUtils.getCurrentTimestamp());
	//
	// return instance;
	// }

}
