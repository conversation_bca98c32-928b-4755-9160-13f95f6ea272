package com.stpl.tech.checklist.config;

import com.hazelcast.config.Config;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.master.core.config.MasterCacheClientConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

@Configuration
@EnableScheduling
@PropertySource({ "classpath:props/jdbc-${env.type}.properties" })
@ComponentScan(basePackages = { "com.stpl.tech.checklist" })
@Import(value = { MasterCacheClientConfig.class })
@EnableMongoRepositories(basePackages = "com.stpl.tech.checklist.dao")
public class CheckListConfig {

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	@Autowired
	private Environment env;

	public CheckListConfig() {
		super();
	}

//	@Bean(name = "factory")
//	public MongoClient factory() throws UnknownHostException {
//		return new MongoClient(env.getProperty("master.mongo.url"),
//				Integer.parseInt(env.getProperty("master.mongo.port")));
//	}
//
//	@Bean(name = "getMongoDbFactory")
//	public MongoDbFactory getMongoDbFactory() throws Exception {
//		return new SimpleMongoDbFactory(factory(), env.getProperty("spring.data.mongodb.database"));
//	}

//	@Bean(name = "mongoTemplate")
//	public MongoTemplate getMongoTemplate() throws Exception {
//		MongoTemplate mongoTemplate = new MongoTemplate(getMongoDbFactory());
//		return mongoTemplate;
//	}

	@Bean(name = "CheckListHazelCastSessionConfig")
	public Config hazelcastSessionConfig() {
		return new Config().setInstanceName("CheckListHazelCastSessionConfig");
	}

	@Bean(name = "CheckListHazelCastInstance")
	public HazelcastInstance hazelcastInstance() {
		return Hazelcast.getOrCreateHazelcastInstance(hazelcastSessionConfig());
	}

}
