package com.stpl.tech.checklist.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.checklist.dao.CheckListItemRepository;
import com.stpl.tech.checklist.dao.CheckListRepository;
import com.stpl.tech.checklist.dao.CheckListScheduleRepository;
import com.stpl.tech.checklist.model.CheckList;
import com.stpl.tech.checklist.service.CheckListService;

@Service
public class CheckListServiceImpl implements CheckListService {

	@Autowired
	private CheckListRepository checkListDao;

	@Autowired
	private CheckListItemRepository checkListItemDao;

	@Autowired
	private CheckListScheduleRepository checkListScheduleDao;

	@Override
	@Transactional(rollbackFor=Exception.class, readOnly = true, propagation = Propagation.REQUIRED)
	public List<CheckList> findByTemplate(boolean isTemplate) {
		return checkListDao.findByTemplate(isTemplate);
	}

	@Override
	@Transactional(rollbackFor=Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	public CheckList addCheckList(CheckList checklist) {
		checklist.setItems(checkListItemDao.saveAll(checklist.getItems()));
		checklist.setSchedule(checkListScheduleDao.save(checklist.getSchedule()));
		return checkListDao.save(checklist);
	}

	@Override
	@Transactional(rollbackFor=Exception.class, readOnly = true, propagation = Propagation.REQUIRED)
	public CheckList findOne(String id) {
		return checkListDao.findById(id).get();
	}

}
