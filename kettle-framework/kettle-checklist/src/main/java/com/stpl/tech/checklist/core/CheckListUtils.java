package com.stpl.tech.checklist.core;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;

public class CheckListUtils {

	public static final String BUSINESS_DATE_FORMAT = "yyyy-MM-dd";
	
	public static DateTime getCurrentTimeIST() {
		return new DateTime(DateTimeZone.forID("Asia/Kolkata"));
	}

	public static Calendar getCalendar() {
		return new GregorianCalendar(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	public static Calendar getCalendar(Date date) {
		Calendar cal = new GregorianCalendar(TimeZone.getTimeZone("Asia/Kolkata"));
		cal.setTime(date);
		return cal;
	}

	public static Date getCurrentTimestamp() {
		return new Date(getCurrentTimeIST().getMillis());
	}

	public static Date getCurrentDateIST() {
		Calendar c = getCalendar();
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		return c.getTime();
	}

	public static int getDayOfWeek(Date date) {
		return getCalendar(date).get(Calendar.DAY_OF_WEEK);
	}

	public static int getLastDayOfMonth(Date date) {
		Calendar cal = getCalendar();
		cal.setTime(date);
		cal.add(Calendar.MONTH, 1);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.add(Calendar.DATE, -1);
		return cal.get(Calendar.DAY_OF_MONTH);
	}

	public static int getDayOfMonth(Date date) {
		return getCalendar(date).get(Calendar.DAY_OF_MONTH);
	}

	public static boolean isLastDayOfMonth(Date businessDate) {
		return CheckListUtils.getDayOfMonth(businessDate) == CheckListUtils.getLastDayOfMonth(businessDate);
	}

	public static Date addMinutes(Date date, int minutes) {
		Calendar cal = getCalendar(date);
		cal.add(Calendar.MINUTE, minutes);
		return cal.getTime();
	}

	public static Date setTimeToDate(Date date, Date time) {
		Calendar cal = getCalendar(date);
		Calendar calTime = getCalendar(time);
		cal.set(Calendar.HOUR, calTime.get(Calendar.HOUR));
		cal.set(Calendar.MINUTE, calTime.get(Calendar.MINUTE));
		cal.set(Calendar.SECOND, calTime.get(Calendar.SECOND));
		return cal.getTime();
	}

	public static Date convertIntegerToTime(Date date, Integer timeCounter) {

		int totalMinutes = timeCounter * 15;
		int hourHand = totalMinutes / 60;
		int minuteHand = totalMinutes % 60;

		Calendar c = getCalendar(date);
		c.set(Calendar.HOUR_OF_DAY, hourHand);
		c.set(Calendar.MINUTE, minuteHand);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		return c.getTime();
	}
	
	public static String getBusinessDateString(Date date) {
		return new SimpleDateFormat(BUSINESS_DATE_FORMAT).format(date);
	}
	
}
