package com.stpl.tech.checklist.dao;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.stpl.tech.checklist.model.CheckList;

@Repository
public interface CheckListRepository extends MongoRepository<CheckList, String> {

	List<CheckList> findByTemplateAndStatus(boolean isTemplate, String status);

	List<CheckList> findByTemplate(boolean isTemplate);

}
