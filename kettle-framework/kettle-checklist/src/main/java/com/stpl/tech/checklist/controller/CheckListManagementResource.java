/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.checklist.controller;

import com.stpl.tech.checklist.dao.CheckListItemRepository;
import com.stpl.tech.checklist.model.CheckList;
import com.stpl.tech.checklist.model.CheckListItem;
import com.stpl.tech.checklist.service.CheckListService;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Optional;

import static com.stpl.tech.checklist.core.CheckListConstants.*;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CHECKLIST_MANAGEMENT_ROOT_CONTEXT)
public class CheckListManagementResource {

	private static final Logger LOG = LoggerFactory.getLogger(CheckListManagementResource.class);

	@Autowired
	private CheckListService checkListService;

	@Autowired
	private CheckListItemRepository checkListItemService;

	@RequestMapping(method = RequestMethod.GET, value = "checklists", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CheckList> getAllChecklists() {
		LOG.info("Request to get all checklist templates");
		return checkListService.findByTemplate(true);
	}

	@RequestMapping(method = RequestMethod.POST, value = "checklist/add", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CheckList addChecklist(@RequestBody final CheckList checklist) {
		LOG.info("Request to add checklist");
		return checkListService.addCheckList(checklist);
	}

	@RequestMapping(method = RequestMethod.POST, value = "checklist/update", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CheckList updateChecklist(@RequestBody final CheckList checklist) {
		LOG.info("Request to save checklist");
		return checkListService.addCheckList(checklist);
	}

	@RequestMapping(method = RequestMethod.POST, value = "checklist/activate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean activateChecklist(@RequestBody final String graphId) {
		LOG.info("Request to activate checklist");
		CheckList checklist = checkListService.findOne(graphId);
		if (checklist != null) {
			checklist.setStatus(AppConstants.ACTIVE);
			checkListService.addCheckList(checklist);
			return true;
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.POST, value = "checklist/deactivate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean deactivateChecklist(@RequestBody final String graphId) {
		LOG.info("Request to deactivate checklist");
		CheckList checklist = checkListService.findOne(graphId);
		if (checklist != null) {
			checklist.setStatus(AppConstants.IN_ACTIVE);
			checkListService.addCheckList(checklist);
			return true;
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.POST, value = "checklistitem/activate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean activateCheckListItem(@RequestBody final String graphId) {
		LOG.info("Request to activate checkList Item");
		Optional<CheckListItem> checkListItem = checkListItemService.findById(graphId);
		if (checkListItem.isPresent()) {
			checkListItem.get().setStatus(AppConstants.ACTIVE);
			checkListItemService.save(checkListItem.get());
			return true;
		}
		return false;
	}

	@RequestMapping(method = RequestMethod.POST, value = "checklistitem/deactivate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean deactivateCheckListItem(@RequestBody final String graphId) {
		LOG.info("Request to deactivate checkList Item");
		Optional<CheckListItem> checkListItem = checkListItemService.findById(graphId);
		if (checkListItem.isPresent()) {
			checkListItem.get().setStatus(AppConstants.IN_ACTIVE);
			checkListItemService.save(checkListItem.get());
			///deactivate in checklist
			return true;
		}
		return false;
	}

}
