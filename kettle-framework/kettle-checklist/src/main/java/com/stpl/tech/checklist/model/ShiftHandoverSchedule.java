//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.21 at 04:26:33 PM IST 
//


package com.stpl.tech.checklist.model;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
 * <p>Java class for ShiftHandoverSchedule complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ShiftHandoverSchedule"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="beforeHandover" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="minutesDifference" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="afterHandover" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ShiftHandoverSchedule", propOrder = {
    "beforeHandover",
    "minutesDifference",
    "afterHandover"
})
@JsonIgnoreProperties(value = {"handler"})
@Document
public class ShiftHandoverSchedule {

	@Id
	private String _id;
    protected boolean beforeHandover;
    protected int minutesDifference;
    protected boolean afterHandover;


	public String get_id() {
		return _id;
	}

	public void set_id(String id) {
		this._id = id;
	}

	/**
     * Gets the value of the beforeHandover property.
     * 
     */
    public boolean isBeforeHandover() {
        return beforeHandover;
    }

	/**
     * Sets the value of the beforeHandover property.
     * 
     */
    public void setBeforeHandover(boolean value) {
        this.beforeHandover = value;
    }

    /**
     * Gets the value of the minutesDifference property.
     * 
     */
    public int getMinutesDifference() {
        return minutesDifference;
    }

    /**
     * Sets the value of the minutesDifference property.
     * 
     */
    public void setMinutesDifference(int value) {
        this.minutesDifference = value;
    }

    /**
     * Gets the value of the afterHandover property.
     * 
     */
    public boolean isAfterHandover() {
        return afterHandover;
    }

    /**
     * Sets the value of the afterHandover property.
     * 
     */
    public void setAfterHandover(boolean value) {
        this.afterHandover = value;
    }

}
