//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.21 at 04:26:33 PM IST 
//


package com.stpl.tech.checklist.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CheckListMappingType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="CheckListMappingType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="UNIT_CATEGORY"/&gt;
 *     &lt;enumeration value="STATION"/&gt;
 *     &lt;enumeration value="ROLE"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "CheckListMappingType")
@XmlEnum
public enum CheckListMappingType {

    @XmlEnumValue("UNIT_CATEGORY")
    UNIT_CATEGORY("UNIT_CATEGORY"),
    STATION("STATION"),
    ROLE("ROLE");
    private final String value;

    CheckListMappingType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static CheckListMappingType fromValue(String v) {
        for (CheckListMappingType c: CheckListMappingType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
