package com.stpl.tech.checklist.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.FileDownloadException;
import com.stpl.tech.master.core.service.model.ErrorInfo;

public class AbstractController {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractController.class);
	
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(DataUpdationException.class)
	@ResponseBody
	public ErrorInfo handleDataUpdationException(Exception ex) {
		LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(),ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	@ResponseStatus(HttpStatus.NOT_FOUND)
	@ExceptionHandler(DataNotFoundException.class)
	@ResponseBody
	public ErrorInfo handleDataNotFoundException(Exception ex) {
		LOG.error(HttpStatus.NOT_FOUND.name(),ex);
		return new ErrorInfo(HttpStatus.NOT_FOUND.name(), ex);
	}

	@ResponseStatus(HttpStatus.NO_CONTENT)
	@ExceptionHandler(FileDownloadException.class)
	@ResponseBody
	public ErrorInfo handleFileDownloadException(Exception ex) {
		LOG.error(HttpStatus.NO_CONTENT.name(),ex);
		return new ErrorInfo(HttpStatus.NO_CONTENT.name(), ex);
	}

	@ResponseStatus(HttpStatus.UNAUTHORIZED)
	@ExceptionHandler(AuthenticationFailureException.class)
	@ResponseBody
	public ErrorInfo handleAuthenticationFailureException(Exception ex) {
		LOG.info(HttpStatus.UNAUTHORIZED.name(),ex);
		return new ErrorInfo(HttpStatus.UNAUTHORIZED.name(), ex);
	}
	
}
