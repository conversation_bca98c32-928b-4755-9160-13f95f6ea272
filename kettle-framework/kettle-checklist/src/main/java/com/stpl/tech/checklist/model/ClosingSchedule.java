//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.01 at 05:08:22 PM IST 
//


package com.stpl.tech.checklist.model;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
 * <p>Java class for ClosingSchedule complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ClosingSchedule"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="beforeClosing" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="minutesDifference" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="afterClosing" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ClosingSchedule", propOrder = {
    "beforeClosing",
    "minutesDifference",
    "afterClosing"
})
@JsonIgnoreProperties(value = {"handler"})
@Document
public class ClosingSchedule {

	@Id
	private String _id;
    protected boolean beforeClosing;
    protected int minutesDifference;
    protected boolean afterClosing;

    
	public String get_id() {
		return _id;
	}

	public void set_id(String id) {
		this._id = id;
	}

    /**
     * Gets the value of the beforeClosing property.
     * 
     */
    public boolean isBeforeClosing() {
        return beforeClosing;
    }

    /**
     * Sets the value of the beforeClosing property.
     * 
     */
    public void setBeforeClosing(boolean value) {
        this.beforeClosing = value;
    }

    /**
     * Gets the value of the minutesDifference property.
     * 
     */
    public int getMinutesDifference() {
        return minutesDifference;
    }

    /**
     * Sets the value of the minutesDifference property.
     * 
     */
    public void setMinutesDifference(int value) {
        this.minutesDifference = value;
    }

    /**
     * Gets the value of the afterClosing property.
     * 
     */
    public boolean isAfterClosing() {
        return afterClosing;
    }

    /**
     * Sets the value of the afterClosing property.
     * 
     */
    public void setAfterClosing(boolean value) {
        this.afterClosing = value;
    }

}
