//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.01 at 07:10:07 PM IST 
//

package com.stpl.tech.checklist.model;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <p>
 * Java class for CheckList complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="CheckList"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}CheckListType"/&gt;
 *         &lt;element name="category" type="{http://www.w3schools.com}CheckListCategory"/&gt;
 *         &lt;element name="schedule" type="{http://www.w3schools.com}CheckListSchedule"/&gt;
 *         &lt;element name="station" type="{http://www.w3schools.com}CheckListStation"/&gt;
 *         &lt;element name="items" type="{http://www.w3schools.com}CheckListItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="unitCategory" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="template" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CheckList", propOrder = { "name", "description", "type", "category", "schedule", "station", "items",
		"unitCategory", "template", "status" })
@Document
public class CheckList {

	@Id
	private String _id;
	@XmlElement(required = true)
	@Field
	protected String name;
	@XmlElement(required = true)
	@Field
	protected String description;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	@Field
	protected CheckListType type;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	@Field
	protected CheckListCategory category;
	@XmlElement(required = true)
	protected CheckListSchedule schedule;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	@Field
	protected CheckListStation station;
	protected List<CheckListItem> items;
	protected List<String> unitCategory;
	@Field
	protected boolean template;
	@XmlElement(required = true, defaultValue = "IN_ACTIVE")
	@Field
	protected String status;

	public String get_id() {
		return _id;
	}

	public void set_id(String id) {
		this._id = id;
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the description property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDescription() {
		return description;
	}

	/**
	 * Sets the value of the description property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDescription(String value) {
		this.description = value;
	}

	/**
	 * Gets the value of the type property.
	 * 
	 * @return possible object is {@link CheckListType }
	 * 
	 */
	public CheckListType getType() {
		return type;
	}

	/**
	 * Sets the value of the type property.
	 * 
	 * @param value
	 *            allowed object is {@link CheckListType }
	 * 
	 */
	public void setType(CheckListType value) {
		this.type = value;
	}

	/**
	 * Gets the value of the category property.
	 * 
	 * @return possible object is {@link CheckListCategory }
	 * 
	 */
	public CheckListCategory getCategory() {
		return category;
	}

	/**
	 * Sets the value of the category property.
	 * 
	 * @param value
	 *            allowed object is {@link CheckListCategory }
	 * 
	 */
	public void setCategory(CheckListCategory value) {
		this.category = value;
	}

	/**
	 * Gets the value of the schedule property.
	 * 
	 * @return possible object is {@link CheckListSchedule }
	 * 
	 */
	public CheckListSchedule getSchedule() {
		return schedule;
	}

	/**
	 * Sets the value of the schedule property.
	 * 
	 * @param value
	 *            allowed object is {@link CheckListSchedule }
	 * 
	 */
	public void setSchedule(CheckListSchedule value) {
		this.schedule = value;
	}

	/**
	 * Gets the value of the station property.
	 * 
	 * @return possible object is {@link CheckListStation }
	 * 
	 */
	public CheckListStation getStation() {
		return station;
	}

	/**
	 * Sets the value of the station property.
	 * 
	 * @param value
	 *            allowed object is {@link CheckListStation }
	 * 
	 */
	public void setStation(CheckListStation value) {
		this.station = value;
	}

	/**
	 * Gets the value of the items property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the items property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getItems().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link CheckListItem }
	 * 
	 * 
	 */
	public List<CheckListItem> getItems() {
		if (items == null) {
			items = new ArrayList<CheckListItem>();
		}
		return this.items;
	}

	public void setItems(List<CheckListItem> items) {
		this.items = items;
	}

	/**
	 * Gets the value of the unitCategory property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the unitCategory property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getUnitCategory().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link String }
	 * 
	 * 
	 */
	public List<String> getUnitCategory() {
		if (unitCategory == null) {
			unitCategory = new ArrayList<String>();
		}
		return this.unitCategory;
	}

	public void setUnitCategory(List<String> unitCategory) {
		this.unitCategory = unitCategory;
	}

	/**
	 * Gets the value of the template property.
	 * 
	 */
	public boolean isTemplate() {
		return template;
	}

	/**
	 * Sets the value of the template property.
	 * 
	 */
	public void setTemplate(boolean value) {
		this.template = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

}
