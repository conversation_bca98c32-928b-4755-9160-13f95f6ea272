package com.stpl.tech.checklist.controller;

import com.stpl.tech.checklist.core.CheckListUtils;
import com.stpl.tech.checklist.dao.CheckListInstanceRepository;
import com.stpl.tech.checklist.dao.CheckListRepository;
import com.stpl.tech.checklist.model.*;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.sql.Time;
import java.util.Date;
import java.util.List;

@Component
public class CheckListGenerator {

	private static final Logger LOG = LoggerFactory.getLogger(CheckListGenerator.class);

	@Autowired
	private CheckListRepository checkListRepository;

	@Autowired
	private CheckListInstanceRepository checkListInstanceRepository;

	@Autowired
	private MasterDataCache masterCache;

	ObjectFactory factory = new ObjectFactory();

	/**
	 * Scheduled Generator for Check List Instances
	 * 
	 * Collects Templates of checklist and then generates the checklist instance
	 * for the current business date
	 * 
	 * Scheduled at 05:15 everyday on Indian time zone
	 * 
	 */
	@Scheduled(cron = "0 15 05 * * *", zone = "GMT+05:30")
	// @Scheduled(fixedRate= 10000)
	public void checkListInstanceGenerator() {

		Date businessDate = CheckListUtils.getCurrentDateIST();
		LOG.info("Generating Checklists for business date: {}", businessDate);

		List<CheckList> templateList = checkListRepository.findByTemplateAndStatus(true, AppConstants.ACTIVE);
		for (UnitBasicDetail unit : masterCache.getAllUnits()) {
			try {
				generateCheckLists(unit, templateList, businessDate);
			} catch (Exception e) {
				LOG.error("ERROR while generating checklist for unit {} : {}", unit.getId(), unit.getName(), e);
			}
		}
	}

	private void generateCheckLists(UnitBasicDetail unit, List<CheckList> templateList, Date businessDate) {
		if (isWorkingDay(masterCache.getOperationalHoursForUnit(unit.getId()), businessDate)) {
			for (CheckList checkList : templateList) {
				if (checkList.getUnitCategory().contains(unit.getCategory().name())) {
					generateCheckList(unit, businessDate, createNewCheckListObject(checkList));
				}
			}
		}
	}

	private void generateCheckList(UnitBasicDetail unit, Date businessDate, CheckList checkList) {
		CheckListSchedule schedule = checkList.getSchedule();
		CheckListFrequency frequency = schedule.getFrequency();
		switch (frequency) {
		case CLOSING:
			// generate daily
			createClosingInstance(unit, businessDate, checkList, schedule);
			break;
		case OPENING:
			// generate daily
			createOpeningInstance(unit, businessDate, checkList, schedule);
			break;
		case INTRA_DAY:
			// generate daily
			createIntradayInstance(unit, businessDate, checkList, schedule);
			break;
		case SHIFT_HANDOVER:
			// generate daily
			createShiftHandoverInstance(unit, businessDate, checkList, schedule);
			break;
		case MONTHLY:
			// check today is the day
			createMonthlyInstance(unit, businessDate, checkList, schedule);
			break;
		case WEEKLY:
			// check today is the day
			createWeeklyInstance(unit, businessDate, checkList, schedule);
			break;
		default:
			break;
		}
	}

	private void createWeeklyInstance(UnitBasicDetail unit, Date businessDate, CheckList checkList,
			CheckListSchedule schedule) {
		WeeklySchedule weeklySchedule = schedule.getWeekly();
		CheckListInstance instance;
		Date date;
		for (DayOfWeekSchedule dayOfWeekSchedule : weeklySchedule.getDaysOfWeek()) {
			if (dayOfWeekSchedule.getDayOfWeek() == CheckListUtils.getDayOfWeek(businessDate)) {
				for (Integer hour : dayOfWeekSchedule.getHoursOfTheDay()) {
					instance = createCheckListInstance(unit, businessDate, checkList);
					date = CheckListUtils.convertIntegerToTime(businessDate, hour);
					instance.setStartTime(date);
					checkListInstanceRepository.save(instance);
				}
			}
		}
	}

	private void createMonthlyInstance(UnitBasicDetail unit, Date businessDate, CheckList checkList,
			CheckListSchedule schedule) {
		MonthlySchedule monthlySchedule = schedule.getMonthly();
		CheckListInstance instance;
		Date date;
		for (DayOfMonthSchedule dayOfMonthSchedule : monthlySchedule.getDaysOfMonth()) {
			if (dayOfMonthSchedule.getDayOfMonth() == CheckListUtils.getDayOfMonth(businessDate)) {
				for (Integer hour : dayOfMonthSchedule.getHoursOfTheDay()) {
					instance = createCheckListInstance(unit, businessDate, checkList);
					date = CheckListUtils.convertIntegerToTime(businessDate, hour);
					instance.setStartTime(date);
					checkListInstanceRepository.save(instance);
				}
			}

			if (dayOfMonthSchedule.isMonthEnd() && CheckListUtils.isLastDayOfMonth(businessDate)) {
				for (Integer hour : dayOfMonthSchedule.getHoursOfTheDay()) {
					instance = createCheckListInstance(unit, businessDate, checkList);
					date = CheckListUtils.convertIntegerToTime(businessDate, hour);
					instance.setStartTime(date);
					checkListInstanceRepository.save(instance);
				}
			}
		}
	}

	private void createShiftHandoverInstance(UnitBasicDetail unit, Date businessDate, CheckList checkList,
			CheckListSchedule schedule) {
		ShiftHandoverSchedule shiftShedule = schedule.getShiftHandover();
		CheckListInstance instance = createCheckListInstance(unit, businessDate, checkList);
		int minutes = 0;
		// TODO for multiple shifts
		UnitHours unitHour = masterCache.getOperationalHoursForUnit(unit.getId())
				.get(CheckListUtils.getDayOfWeek(businessDate) - 1);
		Date shiftHandOverTime = unitHour.getShiftOneHandoverTime();
		shiftHandOverTime = CheckListUtils.setTimeToDate(businessDate, shiftHandOverTime);
		if (shiftShedule.isAfterHandover()) {
			minutes = shiftShedule.getMinutesDifference();
		} else if (shiftShedule.isBeforeHandover()) {
			minutes = -1 * shiftShedule.getMinutesDifference();
		} else {
			minutes = 0;
		}
		shiftHandOverTime = CheckListUtils.addMinutes(shiftHandOverTime, minutes);
		instance.setStartTime(shiftHandOverTime);
		checkListInstanceRepository.save(instance);
	}

	private void createIntradayInstance(UnitBasicDetail unit, Date businessDate, CheckList checkList,
			CheckListSchedule schedule) {
		IntradaySchedule intraDaySchedule = schedule.getIntraDay();
		CheckListInstance instance;
		Date date;
		for (Integer hour : intraDaySchedule.getHoursOfTheDay()) {
			instance = createCheckListInstance(unit, businessDate, checkList);
			date = CheckListUtils.convertIntegerToTime(businessDate, hour);
			instance.setStartTime(date);
			checkListInstanceRepository.save(instance);
		}
	}

	private void createOpeningInstance(UnitBasicDetail unit, Date businessDate, CheckList checkList,
			CheckListSchedule schedule) {
		OpeningSchedule openingSchedule = schedule.getOpening();
		CheckListInstance instance = createCheckListInstance(unit, businessDate, checkList);
		UnitHours unitHours = masterCache.getOperationalHoursForUnit(unit.getId())
				.get(CheckListUtils.getDayOfWeek(businessDate) - 1);
		Date openingTime = getOpeningTimeOnUnitCategory(unitHours, unit.getCategory());
		openingTime = CheckListUtils.setTimeToDate(businessDate, openingTime);
		int minutes = 0;
		if (openingSchedule.isAfterOpening()) {
			minutes = openingSchedule.getMinutesDifference();
		} else if (openingSchedule.isBeforeOpening()) {
			minutes = -1 * openingSchedule.getMinutesDifference();
		} else {
			minutes = 0;
		}
		openingTime = CheckListUtils.addMinutes(openingTime, minutes);
		instance.setStartTime(openingTime);
		checkListInstanceRepository.save(instance);
	}

	private void createClosingInstance(UnitBasicDetail unit, Date businessDate, CheckList checkList,
			CheckListSchedule schedule) {
		ClosingSchedule closingSchedule = schedule.getClosing();
		CheckListInstance instance = createCheckListInstance(unit, businessDate, checkList);
		UnitHours unitHours = masterCache.getOperationalHoursForUnit(unit.getId())
				.get(CheckListUtils.getDayOfWeek(businessDate) - 1);
		Date closingTime = getClosingTimeOnUnitCategory(unitHours, unit.getCategory());
		closingTime = CheckListUtils.setTimeToDate(businessDate, closingTime);
		int minutes = 0;
		if (closingSchedule.isAfterClosing()) {
			minutes = closingSchedule.getMinutesDifference();
		} else if (closingSchedule.isBeforeClosing()) {
			minutes = -1 * closingSchedule.getMinutesDifference();
		} else {
			minutes = 0;
		}
		closingTime = CheckListUtils.addMinutes(closingTime, minutes);
		instance.setStartTime(closingTime);
		checkListInstanceRepository.save(instance);
	}

	private Date getClosingTimeOnUnitCategory(UnitHours unitHours, UnitCategory category) {
		Time time = null;
		switch (category) {
		case CAFE:
			time = unitHours.getDineInClosingTime();
			break;
		case COD:
			break;
		case DELIVERY:
			time = unitHours.getDeliveryClosingTime();
			break;
		case TAKE_AWAY:
			time = unitHours.getTakeAwayClosingTime();
			break;
		default:
			break;
		}
		return time;
	}

	private Date getOpeningTimeOnUnitCategory(UnitHours unitHours, UnitCategory category) {
		Time time = null;
		switch (category) {
		case CAFE:
			time = unitHours.getDineInOpeningTime();
			break;
		case COD:
			break;
		case DELIVERY:
			time = unitHours.getDeliveryOpeningTime();
			break;
		case TAKE_AWAY:
			time = unitHours.getTakeAwayOpeningTime();
			break;
		default:
			break;
		}
		return time;
	}

	private CheckListInstance createCheckListInstance(UnitBasicDetail unit, Date businessDate, CheckList checkList) {
		CheckListInstance instance = factory.createCheckListInstance();
		instance.setCheckList(checkList);
		instance.setBusinessDate(CheckListUtils.getBusinessDateString(businessDate));
		instance.setUnitId(unit.getId());
		instance.setUnitName(unit.getName());
		instance.setStatus(AppConstants.ACTIVE);
		return instance;
	}

	private boolean isWorkingDay(List<UnitHours> unitHours, Date date) {
		for (UnitHours unitHour : unitHours) {
			if (CheckListUtils.getDayOfWeek(date) == unitHour.getDayOfTheWeekNumber()) {
				return unitHour.isIsOperational();
			}
		}
		return false;
	}

	private CheckList createNewCheckListObject(CheckList checkList) {
		CheckList checkListClone = new CheckList();
		checkListClone.setCategory(checkList.getCategory());
		checkListClone.setDescription(checkList.getDescription());
		checkListClone.setName(checkList.getName());
		checkListClone.setSchedule(checkList.getSchedule());
		checkListClone.setStation(checkList.getStation());
		checkListClone.setStatus(checkList.getStatus());
		checkListClone.setTemplate(false);
		checkListClone.setType(checkList.getType());
		// replicate Items
		for (CheckListItem item : checkList.getItems()) {
			if (AppConstants.ACTIVE.equals(item.getStatus())) {
				CheckListItem itemClone = new CheckListItem();
				itemClone.setAction(item.getAction());
				itemClone.setDescription(item.getDescription());
				itemClone.setMandatory(item.isMandatory());
				itemClone.setMultiValued(item.isMultiValued());
				itemClone.setOrdering(item.getOrdering());
				itemClone.setProbableValues(item.getProbableValues());
				itemClone.setResponse(item.getResponse());
				itemClone.setResponseType(item.getResponseType());
				itemClone.setStatus(item.getStatus());
				itemClone.setStep(item.getStep());
				itemClone.setComment(item.getComment());
				checkListClone.getItems().add(itemClone);
			}
		}
		return checkListClone;
	}

}
