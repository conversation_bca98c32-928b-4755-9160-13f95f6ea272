//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.21 at 04:26:33 PM IST 
//


package com.stpl.tech.checklist.model;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
 * <p>Java class for CheckListSchedule complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CheckListSchedule"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="notificationRequired" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="notifyBeforeMinutes" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="editableBeforeMinutes" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="frequency" type="{http://www.w3schools.com}CheckListFrequency"/&gt;
 *         &lt;element name="opening" type="{http://www.w3schools.com}OpeningSchedule"/&gt;
 *         &lt;element name="closing" type="{http://www.w3schools.com}ClosingSchedule"/&gt;
 *         &lt;element name="intraDay" type="{http://www.w3schools.com}IntradaySchedule"/&gt;
 *         &lt;element name="shiftHandover" type="{http://www.w3schools.com}ShiftHandoverSchedule"/&gt;
 *         &lt;element name="weekly" type="{http://www.w3schools.com}WeeklySchedule"/&gt;
 *         &lt;element name="monthly" type="{http://www.w3schools.com}MonthlySchedule"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CheckListSchedule", propOrder = {
    "notificationRequired",
    "notifyBeforeMinutes",
    "editableBeforeMinutes",
    "frequency",
    "opening",
    "closing",
    "intraDay",
    "shiftHandover",
    "weekly",
    "monthly"
})
@JsonIgnoreProperties(value = {"handler"})
@Document
public class CheckListSchedule {

	@Id
	private String _id;
	@Field
    protected boolean notificationRequired;
	@Field
    protected int notifyBeforeMinutes;
	@Field
    protected int editableBeforeMinutes;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    @Field
    protected CheckListFrequency frequency;
    @XmlElement(required = true)
    protected OpeningSchedule opening;
    @XmlElement(required = true)
    protected ClosingSchedule closing;
    @XmlElement(required = true)
    protected IntradaySchedule intraDay;
    @XmlElement(required = true)
    protected ShiftHandoverSchedule shiftHandover;
    @XmlElement(required = true)
    protected WeeklySchedule weekly;
    @XmlElement(required = true)
    protected MonthlySchedule monthly;

    
	public String get_id() {
		return _id;
	}

	public void set_id(String id) {
		this._id = id;
	}

    /**
     * Gets the value of the notificationRequired property.
     * 
     */
    public boolean isNotificationRequired() {
        return notificationRequired;
    }

    /**
     * Sets the value of the notificationRequired property.
     * 
     */
    public void setNotificationRequired(boolean value) {
        this.notificationRequired = value;
    }

    /**
     * Gets the value of the notifyBeforeMinutes property.
     * 
     */
    public int getNotifyBeforeMinutes() {
        return notifyBeforeMinutes;
    }

    /**
     * Sets the value of the notifyBeforeMinutes property.
     * 
     */
    public void setNotifyBeforeMinutes(int value) {
        this.notifyBeforeMinutes = value;
    }

    /**
     * Gets the value of the editableBeforeMinutes property.
     * 
     */
    public int getEditableBeforeMinutes() {
        return editableBeforeMinutes;
    }

    /**
     * Sets the value of the editableBeforeMinutes property.
     * 
     */
    public void setEditableBeforeMinutes(int value) {
        this.editableBeforeMinutes = value;
    }

    /**
     * Gets the value of the frequency property.
     * 
     * @return
     *     possible object is
     *     {@link CheckListFrequency }
     *     
     */
    public CheckListFrequency getFrequency() {
        return frequency;
    }

    /**
     * Sets the value of the frequency property.
     * 
     * @param value
     *     allowed object is
     *     {@link CheckListFrequency }
     *     
     */
    public void setFrequency(CheckListFrequency value) {
        this.frequency = value;
    }

    /**
     * Gets the value of the opening property.
     * 
     * @return
     *     possible object is
     *     {@link OpeningSchedule }
     *     
     */
    public OpeningSchedule getOpening() {
        return opening;
    }

    /**
     * Sets the value of the opening property.
     * 
     * @param value
     *     allowed object is
     *     {@link OpeningSchedule }
     *     
     */
    public void setOpening(OpeningSchedule value) {
        this.opening = value;
    }

    /**
     * Gets the value of the closing property.
     * 
     * @return
     *     possible object is
     *     {@link ClosingSchedule }
     *     
     */
    public ClosingSchedule getClosing() {
        return closing;
    }

    /**
     * Sets the value of the closing property.
     * 
     * @param value
     *     allowed object is
     *     {@link ClosingSchedule }
     *     
     */
    public void setClosing(ClosingSchedule value) {
        this.closing = value;
    }

    /**
     * Gets the value of the intraDay property.
     * 
     * @return
     *     possible object is
     *     {@link IntradaySchedule }
     *     
     */
    public IntradaySchedule getIntraDay() {
        return intraDay;
    }

    /**
     * Sets the value of the intraDay property.
     * 
     * @param value
     *     allowed object is
     *     {@link IntradaySchedule }
     *     
     */
    public void setIntraDay(IntradaySchedule value) {
        this.intraDay = value;
    }

    /**
     * Gets the value of the shiftHandover property.
     * 
     * @return
     *     possible object is
     *     {@link ShiftHandoverSchedule }
     *     
     */
    public ShiftHandoverSchedule getShiftHandover() {
        return shiftHandover;
    }

    /**
     * Sets the value of the shiftHandover property.
     * 
     * @param value
     *     allowed object is
     *     {@link ShiftHandoverSchedule }
     *     
     */
    public void setShiftHandover(ShiftHandoverSchedule value) {
        this.shiftHandover = value;
    }

    /**
     * Gets the value of the weekly property.
     * 
     * @return
     *     possible object is
     *     {@link WeeklySchedule }
     *     
     */
    public WeeklySchedule getWeekly() {
        return weekly;
    }

    /**
     * Sets the value of the weekly property.
     * 
     * @param value
     *     allowed object is
     *     {@link WeeklySchedule }
     *     
     */
    public void setWeekly(WeeklySchedule value) {
        this.weekly = value;
    }

    /**
     * Gets the value of the monthly property.
     * 
     * @return
     *     possible object is
     *     {@link MonthlySchedule }
     *     
     */
    public MonthlySchedule getMonthly() {
        return monthly;
    }

    /**
     * Sets the value of the monthly property.
     * 
     * @param value
     *     allowed object is
     *     {@link MonthlySchedule }
     *     
     */
    public void setMonthly(MonthlySchedule value) {
        this.monthly = value;
    }

}
