package com.stpl.tech.checklist.controller;

import static com.stpl.tech.checklist.core.CheckListConstants.API_VERSION;
import static com.stpl.tech.checklist.core.CheckListConstants.CHECKLIST_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.checklist.core.CheckListConstants.SEPARATOR;

import javax.ws.rs.core.MediaType;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.checklist.model.CheckListCategory;
import com.stpl.tech.checklist.model.CheckListFrequency;
import com.stpl.tech.checklist.model.CheckListStation;
import com.stpl.tech.checklist.model.CheckListType;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CHECKLIST_METADATA_ROOT_CONTEXT)
public class CheckListMetadataResource {

	@RequestMapping(method = RequestMethod.GET, value = "listTypes", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CheckListType[] getAllChecklistType() {
		return CheckListType.values();
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "categories", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CheckListCategory[] getAllChecklistCategory() {
		return CheckListCategory.values();
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "frequency", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CheckListFrequency[] getAllChecklistFrequency() {
		return CheckListFrequency.values();
	}
	
	@RequestMapping(method = RequestMethod.GET, value = "station", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CheckListStation[] getAllChecklistStation() {
		return CheckListStation.values();
	}

}
