package com.stpl.tech.checklist.model;

import javax.xml.bind.annotation.adapters.XmlAdapter;
import java.math.BigDecimal;

public class Adapter1 extends XmlAdapter<String, BigDecimal> {

	public BigDecimal unmarshal(String value) {
		return (com.stpl.tech.util.domain.adapter.BigDecimalAdapter.parseBigDecimal(value));
	}

	public String marshal(BigDecimal value) {
		return (com.stpl.tech.util.domain.adapter.BigDecimalAdapter.printBigDecimal(value));
	}

}
