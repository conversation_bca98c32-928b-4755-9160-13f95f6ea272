//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.21 at 04:26:33 PM IST 
//


package com.stpl.tech.checklist.model;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
 * <p>Java class for MonthlySchedule complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="MonthlySchedule"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="daysOfMonth" type="{http://www.w3schools.com}DayOfMonthSchedule" maxOccurs="31" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MonthlySchedule", propOrder = {
    "daysOfMonth"
})
@JsonIgnoreProperties(value = {"handler"})
@Document
public class MonthlySchedule {

	@Id
	private String _id;
    protected List<DayOfMonthSchedule> daysOfMonth;

    
	public String get_id() {
		return _id;
	}

	public void set_id(String id) {
		this._id = id;
	}

    /**
     * Gets the value of the daysOfMonth property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the daysOfMonth property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDaysOfMonth().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DayOfMonthSchedule }
     * 
     * 
     */
    public List<DayOfMonthSchedule> getDaysOfMonth() {
        if (daysOfMonth == null) {
            daysOfMonth = new ArrayList<DayOfMonthSchedule>();
        }
        return this.daysOfMonth;
    }

	public void setDaysOfMonth(List<DayOfMonthSchedule> daysOfMonth) {
		this.daysOfMonth = daysOfMonth;
	}

}
