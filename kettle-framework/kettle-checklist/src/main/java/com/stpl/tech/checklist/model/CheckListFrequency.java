//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.21 at 04:26:33 PM IST 
//


package com.stpl.tech.checklist.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CheckListFrequency.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="CheckListFrequency"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="OPENING"/&gt;
 *     &lt;enumeration value="CLOSING"/&gt;
 *     &lt;enumeration value="INTRA_DAY"/&gt;
 *     &lt;enumeration value="SHIFT_HANDOVER"/&gt;
 *     &lt;enumeration value="WEEKLY"/&gt;
 *     &lt;enumeration value="MONTHLY"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "CheckListFrequency")
@XmlEnum
public enum CheckListFrequency {

    OPENING("OPENING"),
    CLOSING("CLOSING"),
    @XmlEnumValue("INTRA_DAY")
    INTRA_DAY("INTRA_DAY"),
    @XmlEnumValue("SHIFT_HANDOVER")
    SHIFT_HANDOVER("SHIFT_HANDOVER"),
    WEEKLY("WEEKLY"),
    MONTHLY("MONTHLY");
    private final String value;

    CheckListFrequency(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static CheckListFrequency fromValue(String v) {
        for (CheckListFrequency c: CheckListFrequency.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
