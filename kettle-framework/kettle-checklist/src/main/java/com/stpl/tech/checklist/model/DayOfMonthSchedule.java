//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.21 at 04:26:33 PM IST 
//


package com.stpl.tech.checklist.model;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Id;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


/**
 * <p>Java class for DayOfMonthSchedule complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DayOfMonthSchedule"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="monthEnd" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="dayOfMonth" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="hoursOfTheDay" type="{http://www.w3.org/2001/XMLSchema}int" maxOccurs="96" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DayOfMonthSchedule", propOrder = {
    "monthEnd",
    "dayOfMonth",
    "hoursOfTheDay"
})
@JsonIgnoreProperties(value = {"handler"})
@Document
public class DayOfMonthSchedule {

	@Id
	private String _id;
    protected boolean monthEnd;
    protected int dayOfMonth;
    @XmlElement(type = Integer.class)
    protected List<Integer> hoursOfTheDay;

    
	public String get_id() {
		return _id;
	}

	public void set_id(String id) {
		this._id = id;
	}
	
    /**
     * Gets the value of the monthEnd property.
     * 
     */
    public boolean isMonthEnd() {
        return monthEnd;
    }

    /**
     * Sets the value of the monthEnd property.
     * 
     */
    public void setMonthEnd(boolean value) {
        this.monthEnd = value;
    }

    /**
     * Gets the value of the dayOfMonth property.
     * 
     */
    public int getDayOfMonth() {
        return dayOfMonth;
    }

    /**
     * Sets the value of the dayOfMonth property.
     * 
     */
    public void setDayOfMonth(int value) {
        this.dayOfMonth = value;
    }

    /**
     * Gets the value of the hoursOfTheDay property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the hoursOfTheDay property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getHoursOfTheDay().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Integer }
     * 
     * 
     */
    public List<Integer> getHoursOfTheDay() {
        if (hoursOfTheDay == null) {
            hoursOfTheDay = new ArrayList<Integer>();
        }
        return this.hoursOfTheDay;
    }

	public void setHoursOfTheDay(List<Integer> hoursOfTheDay) {
		this.hoursOfTheDay = hoursOfTheDay;
	}

}
