//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.21 at 04:26:33 PM IST 
//


package com.stpl.tech.checklist.model;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.checklist.model package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.checklist.model
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link CheckListMapping }
     * 
     */
    public CheckListMapping createCheckListMapping() {
        return new CheckListMapping();
    }

    /**
     * Create an instance of {@link CheckList }
     * 
     */
    public CheckList createCheckList() {
        return new CheckList();
    }

    /**
     * Create an instance of {@link CheckListInstance }
     * 
     */
    public CheckListInstance createCheckListInstance() {
        return new CheckListInstance();
    }

    /**
     * Create an instance of {@link CheckListSchedule }
     * 
     */
    public CheckListSchedule createCheckListSchedule() {
        return new CheckListSchedule();
    }

    /**
     * Create an instance of {@link OpeningSchedule }
     * 
     */
    public OpeningSchedule createOpeningSchedule() {
        return new OpeningSchedule();
    }

    /**
     * Create an instance of {@link ClosingSchedule }
     * 
     */
    public ClosingSchedule createClosingSchedule() {
        return new ClosingSchedule();
    }

    /**
     * Create an instance of {@link IntradaySchedule }
     * 
     */
    public IntradaySchedule createIntradaySchedule() {
        return new IntradaySchedule();
    }

    /**
     * Create an instance of {@link ShiftHandoverSchedule }
     * 
     */
    public ShiftHandoverSchedule createShiftHandoverSchedule() {
        return new ShiftHandoverSchedule();
    }

    /**
     * Create an instance of {@link MonthlySchedule }
     * 
     */
    public MonthlySchedule createMonthlySchedule() {
        return new MonthlySchedule();
    }

    /**
     * Create an instance of {@link WeeklySchedule }
     * 
     */
    public WeeklySchedule createWeeklySchedule() {
        return new WeeklySchedule();
    }

    /**
     * Create an instance of {@link DayOfWeekSchedule }
     * 
     */
    public DayOfWeekSchedule createDayOfWeekSchedule() {
        return new DayOfWeekSchedule();
    }

    /**
     * Create an instance of {@link DayOfMonthSchedule }
     * 
     */
    public DayOfMonthSchedule createDayOfMonthSchedule() {
        return new DayOfMonthSchedule();
    }

    /**
     * Create an instance of {@link CheckListItem }
     * 
     */
    public CheckListItem createCheckListItem() {
        return new CheckListItem();
    }

}
