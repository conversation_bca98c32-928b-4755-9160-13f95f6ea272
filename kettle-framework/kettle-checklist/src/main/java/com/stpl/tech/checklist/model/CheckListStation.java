//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.01 at 07:10:07 PM IST 
//


package com.stpl.tech.checklist.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CheckListStation.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="CheckListStation"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="HOT"/&gt;
 *     &lt;enumeration value="COLD"/&gt;
 *     &lt;enumeration value="FOOD"/&gt;
 *     &lt;enumeration value="FLOOR"/&gt;
 *     &lt;enumeration value="POS"/&gt;
 *     &lt;enumeration value="ASSEMBLY"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "CheckListStation")
@XmlEnum
public enum CheckListStation {

    HOT,
    COLD,
    FOOD,
    FLOOR,
    POS,
    ASSEMBLY;

    public String value() {
        return name();
    }

    public static CheckListStation fromValue(String v) {
        return valueOf(v);
    }

}
