<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<bindings xmlns="http://java.sun.com/xml/ns/jaxb" version="2.0"
	xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<globalBindings underscoreBinding="asCharInWord">
		<javaType name="java.util.Date" xmlType="xs:date"
			parseMethod="com.stpl.tech.util.domain.adapter.DateAdapter.parseDate"
			printMethod="com.stpl.tech.util.domain.adapter.DateAdapter.printDate" />
		<javaType name="java.math.BigDecimal" xmlType="xs:decimal"
			parseMethod="com.stpl.tech.util.domain.adapter.BigDecimalAdapter.parseBigDecimal"
			printMethod="com.stpl.tech.util.domain.adapter.BigDecimalAdapter.printBigDecimal" />
	</globalBindings>
</bindings>