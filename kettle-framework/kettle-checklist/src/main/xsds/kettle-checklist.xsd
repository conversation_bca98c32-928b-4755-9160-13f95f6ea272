<?xml version="1.0"?>
<!-- ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL ~ __________________ 
	~ ~ [2015] - [2017] Sunshine Teahouse Private Limited ~ All Rights Reserved. 
	~ ~ NOTICE: All information contained herein is, and remains ~ the property 
	of Sunshine Teahouse Private Limited and its suppliers, ~ if any. The intellectual 
	and technical concepts contained ~ herein are proprietary to Sunshine Teahouse 
	Private Limited ~ and its suppliers, and are protected by trade secret or 
	copyright law. ~ Dissemination of this information or reproduction of this 
	material ~ is strictly forbidden unless prior written permission is obtained 
	~ from Sunshine Teahouse Private Limited. -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:element name="CheckListMapping">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="mappingType" type="CheckListMappingType" />
				<xs:element name="mappingValue" type="xs:string" />
				<xs:element name="status" type="xs:string" />
				<xs:element name="checkLists" type="CheckList" minOccurs="0"
					maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="CheckList">
		<xs:sequence>
			<xs:element name="name" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="type" type="CheckListType" />
			<xs:element name="category" type="CheckListCategory" />
			<xs:element name="schedule" type="CheckListSchedule" />
			<xs:element name="station" type="CheckListStation" />
			<xs:element name="items" type="CheckListItem" minOccurs="0"
				maxOccurs="unbounded" />
			<xs:element name="unitCategory" type="xs:string" minOccurs="0"
					maxOccurs="unbounded" />
			<xs:element name="template" type="xs:boolean" />
			<xs:element name="status" type="xs:string" default="IN_ACTIVE" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CheckListSchedule">
		<xs:sequence>
			<xs:element name="notificationRequired" type="xs:boolean" />
			<xs:element name="notifyBeforeMinutes" type="xs:int" />
			<xs:element name="editableBeforeMinutes" type="xs:int" />
			<xs:element name="frequency" type="CheckListFrequency" />
			<xs:element name="opening" type="OpeningSchedule" />
			<xs:element name="closing" type="ClosingSchedule" />
			<xs:element name="intraDay" type="IntradaySchedule" />
			<xs:element name="shiftHandover" type="ShiftHandoverSchedule" />
			<xs:element name="weekly" type="WeeklySchedule" />
			<xs:element name="monthly" type="MonthlySchedule" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OpeningSchedule">
		<xs:sequence>
			<xs:element name="beforeOpening" type="xs:boolean" />
			<xs:element name="minutesDifference" type="xs:int" />
			<xs:element name="afterOpening" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ClosingSchedule">
		<xs:sequence>
			<xs:element name="beforeClosing" type="xs:boolean" />
			<xs:element name="minutesDifference" type="xs:int" />
			<xs:element name="afterClosing" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="IntradaySchedule">
		<xs:sequence>
			<xs:element name="hoursOfTheDay" type="xs:int" minOccurs="0"
				maxOccurs="96" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ShiftHandoverSchedule">
		<xs:sequence>
			<xs:element name="beforeHandover" type="xs:boolean" />
			<xs:element name="minutesDifference" type="xs:int" />
			<xs:element name="afterHandover" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MonthlySchedule">
		<xs:sequence>
			<xs:element name="daysOfMonth" type="DayOfMonthSchedule"
				minOccurs="0" maxOccurs="31" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="WeeklySchedule">
		<xs:sequence>
			<xs:element name="daysOfWeek" type="DayOfWeekSchedule"
				minOccurs="0" maxOccurs="7" />

		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DayOfWeekSchedule">
		<xs:sequence>
			<xs:element name="dayOfWeek" type="xs:int" />
			<xs:element name="hoursOfTheDay" type="xs:int" minOccurs="0"
				maxOccurs="96" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="DayOfMonthSchedule">
		<xs:sequence>
			<xs:element name="monthEnd" type="xs:boolean" />
			<xs:element name="dayOfMonth" type="xs:int" />
			<xs:element name="hoursOfTheDay" type="xs:int" minOccurs="0"
				maxOccurs="96" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CheckListType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ADHOC" />
			<xs:enumeration value="PERIODIC" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CheckListRole" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="MANAGER" />
			<xs:enumeration value="TRAINER" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CheckListStation" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="HOT" />
			<xs:enumeration value="COLD" />
			<xs:enumeration value="FOOD" />
			<xs:enumeration value="FLOOR" />
			<xs:enumeration value="POS" />
			<xs:enumeration value="ASSEMBLY" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CheckListCategory" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OPERATIONAL" />
			<xs:enumeration value="PLANNER" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CheckListMappingType" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UNIT_CATEGORY" />
			<xs:enumeration value="STATION" />
			<xs:enumeration value="ROLE" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CheckListFrequency" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OPENING" />
			<xs:enumeration value="CLOSING" />
			<xs:enumeration value="INTRA_DAY" />
			<xs:enumeration value="SHIFT_HANDOVER" />
			<xs:enumeration value="WEEKLY" />
			<xs:enumeration value="MONTHLY" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CheckListItem">
		<xs:sequence>
			<xs:element name="ordering" type="xs:int" />
			<xs:element name="step" type="xs:string" />
			<xs:element name="action" type="xs:string" />
			<xs:element name="description" type="xs:string" />
			<xs:element name="response" type="xs:string" />
			<xs:element name="mandatory" type="xs:boolean" />
			<xs:element name="multiValued" type="xs:boolean" />
			<xs:element name="responseType" type="xs:string" />
			<xs:element name="probableValues" type="xs:string"
				minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="status" type="xs:string" default="ACTIVE" />
		</xs:sequence>
	</xs:complexType>
	<xs:element name="CheckListInstance">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="event" type="CheckList" />
				<xs:element name="checkList" type="CheckList" />
				<xs:element name="userName" type="xs:string" />
				<xs:element name="userRole" type="xs:string" />
				<xs:element name="userId" type="xs:int" />
				<xs:element name="businessDate" type="xs:date" />
				<xs:element name="startTime" type="xs:date" />
				<xs:element name="endTime" type="xs:date" />
				<xs:element name="unitName" type="xs:string" />
				<xs:element name="unitId" type="xs:int" />
				<xs:element name="submissionTime" type="xs:date" />
				<xs:element name="lastUpdateTime" type="xs:date" />
				<xs:element name="status" type="xs:string" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
