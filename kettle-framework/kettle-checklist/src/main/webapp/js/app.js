/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('listapp',['ui.router','ngCookies']).config(['$stateProvider', '$urlRouterProvider',
  function($stateProvider,$urlRouterProvider) {
	  
	  $stateProvider
                .state('login', {
                    url: '/login',
                    templateUrl: 'views/login.html',
                    controller: 'LoginController'
                })
                .state('dashboard', {
                    url: '/dashboard',
                    templateUrl: 'views/dashboard.html',
                    controller: 'MainController'
                })
                .state('dashboard.checkListView', {
                    url: '/checkListView',
                    templateUrl: 'views/checkListView.html',
                    controller: 'checkListViewCtrl'
                }) 
                .state('dashboard.checkListStation', {
                    url: '/checkListStation',
                    templateUrl: 'views/checkListStation.html',
                    controller: 'checkListStationCtrl'
                })
                
            $urlRouterProvider.otherwise('/dashboard');
  }])
  .service('AuthService', function() {
            var service = this;
            service.authorization = null;
    	    service.getAuthorization = getAuthorization;
    	    service.setAuthorization = function(authorization){
    	    	service.authorization = authorization;
    	    };
    	    function getAuthorization(){
    	    	return service.authorization;
    	    }
    	    return service;
        }).service('authInterceptor', function($rootScope, AuthService, $location,$cookieStore) {
            var service = this;
            service.request = function(config) {
                config.headers.auth = AuthService.getAuthorization();
                return config;
            };
            service.responseError = function(response) {
                if (response.status === 401) {
                	console.log("Response Error 401",response);
					$cookieStore.remove('checklistlobals');
					AuthService.setAuthorization(null);
                    $location.path('/login');
                }
                return response;
            };
        }).config(['$httpProvider',function($httpProvider) {
        	//$httpProvider.defaults.headers.post['auth'] = AuthService.getAuthorization(); 
            $httpProvider.interceptors.push('authInterceptor');
        }]).run(function($rootScope, $location, AuthService,$cookieStore){
        	
			$rootScope.$on('$locationChangeStart', function (event, next, current) {
				  $cookieStore.get('checklistglobals')!=null?AuthService.setAuthorization($cookieStore.get('checklistglobals').jwtToken):AuthService.setAuthorization(null);
                // redirect to login page if not logged in and trying to access a restricted page
                var restrictedPage = $.inArray($location.path(), ['/login']) === -1;
                var loggedIn = AuthService.getAuthorization()!=null;
                if (restrictedPage && !loggedIn) {
					$location.path("/login");
                } 	
            });
			$rootScope.showFullScreenLoader = false;
		}).directive('stringToNumber', function() {
		  return {
			require: 'ngModel',
			link: function(scope, element, attrs, ngModel) {
			  ngModel.$parsers.push(function(value) {
				return '' + value;
			  });
			  ngModel.$formatters.push(function(value) {
				return parseFloat(value, 10);
			  });
			}
		  };
		});