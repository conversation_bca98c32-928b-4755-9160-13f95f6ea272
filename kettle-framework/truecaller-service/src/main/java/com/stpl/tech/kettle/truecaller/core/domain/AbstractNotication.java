package com.stpl.tech.kettle.truecaller.core.domain;

import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerRequestType;
import com.stpl.tech.kettle.truecaller.util.TrueCallerUtils;
import com.stpl.tech.util.EnvType;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 22-03-2018.
 */
public abstract class AbstractNotication {
    private static final String PREFIX = "TCChannel";
    private static final String SEPARATOR = "_";

    public String initChannel(EnvType env, Integer unitId , Integer terminal, boolean isKiosk){
        return env.name() + SEPARATOR + (isKiosk ? "Kiosk" : "") + PREFIX + SEPARATOR + unitId + SEPARATOR + terminal;
    }

    public String initChannel(EnvType env, Integer unitId , Integer terminal, String application){
        return env.name() + SEPARATOR + application + PREFIX + SEPARATOR + unitId + SEPARATOR + terminal;
    }

    public String getMessageKey(String requestType) {
        if(requestType!=null){
            TrueCallerRequestType type = TrueCallerRequestType.valueOf(requestType);
            return type.getMessageKey();
        }
        return TrueCallerRequestType.REGISTRATION.getMessageKey();
    }

    public String getName(String firstName, String lastName) {
        return TrueCallerUtils.makeName(firstName, lastName);
    }
}
