package com.stpl.tech.kettle.truecaller.core.service;

import com.stpl.tech.kettle.core.exception.RequestValidatonException;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerPostRequest;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerProfileDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerRequestDetail;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerProfileDineIn;

import java.io.IOException;

/**
 * Copyright (C) 2018,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-03-2018.
 */
public interface TrueCallerService {


    public String verifyProfile(TrueCallerPostRequest contact) throws RequestValidatonException;

    public void verifyAndSaveAccessToken(String requestId, String accessToken, String state) throws RequestValidatonException;

    public void getProfileAndSave(TrueCallerRequestDetail requestDetail) throws RequestValidatonException;

    void getProfileFromAccessToken(String requestId, String accessToken, String endpoint, String application) throws RequestValidatonException, IOException;

    public TrueCallerVerifiedProfile getTrueCallerProfileForRequest(String requestID);

    public TrueCallerRequestDetail getTrueCallerRequestForContact(String contact);

    void saveProfile(TrueCallerProfileDetail profile);

    void getProfileForTokenAndPutToCache(String requestId, String accessToken, String endpoint) throws IOException;

    TrueCallerProfileDineIn getProfileFromCache(String requestId);

    TrueCallerProfileDineIn putProfileToCache(String requestId, TrueCallerProfileDineIn trueCallerProfileDineIn);
}
