package com.stpl.tech.kettle.truecaller.controller;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.ws.rs.core.MediaType;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerProfileDineIn;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.google.gson.Gson;
import com.stpl.tech.kettle.core.exception.RequestValidatonException;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerPostRequest;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.kettle.truecaller.core.TrueCallerConstants;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerProfileDetail;
import com.stpl.tech.kettle.truecaller.core.service.TrueCallerService;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-03-2018.
 */

@RestController
@RequestMapping(value = "v1/tc")
public class TrueCallerResource {

    private static final Logger LOG = LoggerFactory.getLogger(TrueCallerResource.class);

    @Autowired
    private TrueCallerService service;

    // verify contact with True Caller Service
    @RequestMapping(value = "verify", method = RequestMethod.POST, consumes = {MediaType.APPLICATION_JSON,MediaType.TEXT_PLAIN})
    public String verifyProfile(@RequestBody TrueCallerPostRequest request) throws Exception {
        LOG.info("Request to verify profile for contact {} {} {}", request.getUnitId(), request.getTerminal(), request.getContact());
        return service.verifyProfile(request);
    }


    // verify contact with True Caller Service
    @RequestMapping(value = "get-profile", method = RequestMethod.GET)
    public TrueCallerVerifiedProfile getVerifiedProfile(@RequestParam String requestId) throws Exception {
        LOG.info("Request to get profile for request {} ", requestId);
        return service.getTrueCallerProfileForRequest(requestId);
    }

    //TODO fix this for using contact number feature
    //callback for True Caller Service
    @RequestMapping(value = "callback", method = RequestMethod.POST)
    public void postAccessToken(@RequestBody Map<String, String> requestMap)
            throws RequestValidatonException {
        LOG.info("Request to process callback with params :::::: {}", new Gson().toJson(requestMap));
        String requestId = requestMap.get(TrueCallerConstants.REQUEST_ID_PARAM);
        String accessToken = requestMap.get(TrueCallerConstants.ACCESS_TOKEN_PARAM);
        String state = requestMap.get(TrueCallerConstants.STATE_PARAM);
        LOG.info("Request to process callback with params :::::: {} {} {}", requestId, accessToken, state);
        service.verifyAndSaveAccessToken(requestId, accessToken, state);
    }


    //callback for True Caller QR code Service
    @RequestMapping(value = "qr-callback", method = RequestMethod.POST)
    public void postAccessTokenQR(@RequestBody Map<String, String> requestMap)
            throws RequestValidatonException, IOException {
        LOG.info("Request to process callback qr with params :::::: {}", new Gson().toJson(requestMap));
        String requestId = requestMap.get(TrueCallerConstants.REQUEST_ID_PARAM);
        String accessToken = requestMap.get(TrueCallerConstants.ACCESS_TOKEN_PARAM);
        String endpoint = requestMap.get(TrueCallerConstants.END_POINT);
        LOG.info("Request to process callback with params :::::: {} {} {}", requestId, accessToken, endpoint);
        service.getProfileFromAccessToken(requestId, accessToken, endpoint, "Kiosk");
    }


    //callback for True Caller QR code Service on customer screen
    @RequestMapping(value = "qr-cs-callback", method = RequestMethod.POST)
    public void postAccessTokenQRCustomerScreen(@RequestBody Map<String, String> requestMap)
            throws RequestValidatonException, IOException {
        LOG.info("Request to process callback qr with params :::::: {}", new Gson().toJson(requestMap));
        String requestId = requestMap.get(TrueCallerConstants.REQUEST_ID_PARAM);
        String accessToken = requestMap.get(TrueCallerConstants.ACCESS_TOKEN_PARAM);
        String endpoint = requestMap.get(TrueCallerConstants.END_POINT);
        LOG.info("Request to process callback with params :::::: {} {} {}", requestId, accessToken, endpoint);
        service.getProfileFromAccessToken(requestId, accessToken, endpoint, "CustomerScreen");
    }


    //updating profile from dine in app
    @RequestMapping(value = "updateProfile", method = RequestMethod.POST)
    public void updateTruecallerProfile(@RequestBody TrueCallerProfileDetail profile)
            throws RequestValidatonException, IOException {
        LOG.info("Request to update truecaller profile :::::: {}", new Gson().toJson(profile));
        service.saveProfile(profile);
    }


    @RequestMapping(value = "/webProfile",method = RequestMethod.POST)
    public boolean getProductList(@RequestBody Map<String, String> requestMap) throws RequestValidatonException, IOException {
        if (requestMap.get(TrueCallerConstants.STATUS) != null) {
            return false;
        }
        LOG.info("Request to process callback qr with params :::::: {}", new Gson().toJson(requestMap));
        String requestId = requestMap.get(TrueCallerConstants.REQUEST_ID_PARAM);
        String accessToken = requestMap.get(TrueCallerConstants.ACCESS_TOKEN_PARAM);
        String endpoint = requestMap.get(TrueCallerConstants.END_POINT);

        service.getProfileFromAccessToken(requestId, accessToken, endpoint, "TRUE_CALLER_WEB");
        return true;
    }

    /**
     * Gets data from truecaller, and adds profile data to cache
     * @param requestMap
     * @return
     * @throws RequestValidatonException
     * @throws IOException
     */
    @RequestMapping(value = "/tcProfile", method = RequestMethod.POST)
    public void dineInTrueCallerWebhook(@RequestBody Map<String, String> requestMap) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        try {
            LOG.info("[Dine In] Truecaller Callback to process request with params :::::: {}",
                    new Gson().toJson(requestMap));
            String requestId = requestMap.get(TrueCallerConstants.REQUEST_ID_PARAM);
            if (Objects.nonNull(requestMap.get(TrueCallerConstants.STATUS))) {
                //status is flow_invoked or user_rejected
                if (TrueCallerConstants.USER_REJECTED.equalsIgnoreCase(requestMap.get(TrueCallerConstants.STATUS))) {
                    service.putProfileToCache(requestId,
                            TrueCallerProfileDineIn
                                    .builder()
                                    .consentStatus(TrueCallerConstants.USER_REJECTED)
                                    .build()
                    );
                }
                return;
            }

            String accessToken = requestMap.get(TrueCallerConstants.ACCESS_TOKEN_PARAM);
            String endpoint = requestMap.get(TrueCallerConstants.END_POINT);

            if (Objects.nonNull(accessToken) && Objects.nonNull(endpoint)) {
                // profile found
                service.getProfileForTokenAndPutToCache(requestId, accessToken, endpoint);
            }
            LOG.info("#### TC Webhook took {} for processing ####", watch.stop().elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            LOG.error("Error while processing truecaller data", e);
        }
    }

    @GetMapping(value = "get-tc-pr")
    public TrueCallerProfileDineIn getProfileData(@RequestParam String requestId) {
        return service.getProfileFromCache(requestId);
    }

}
