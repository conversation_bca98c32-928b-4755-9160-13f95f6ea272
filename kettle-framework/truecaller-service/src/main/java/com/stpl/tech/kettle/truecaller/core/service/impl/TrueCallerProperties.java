package com.stpl.tech.kettle.truecaller.core.service.impl;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 07-03-2018.
 */
@Service
public class TrueCallerProperties {

    @Autowired
    private Environment env;

    public EnvType getEnvironmentType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public String getBasePath() {
        return env.getProperty("server.base.dir");
    }

    public String getVerifyEmailPath() {
        return env.getProperty("account.verify.email.path");
    }

    public String getTrueCallerKey() {
        return env.getProperty("truecaller.api.secret.key");
    }

    public String getPubnubPublishKey() {
        return env.getProperty("pubnub.publish.key", "******************************************");
    }

    public String getPubnubSubscribeKey() {
        return env.getProperty("pubnub.subscribe.key", "******************************************");
    }
}
