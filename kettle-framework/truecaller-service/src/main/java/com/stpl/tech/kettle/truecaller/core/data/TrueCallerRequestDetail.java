package com.stpl.tech.kettle.truecaller.core.data;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 05-03-2018.
 */

@Entity
@Table(name = "TRUE_CALLER_REQUEST_DETAIL")
public class TrueCallerRequestDetail {

    private Integer id;
    private Integer unitId;
    private Integer terminal;
    private String contact;
    private String requestId;
    private String accessToken;
    private TrueCallerProfileDetail profile;
    private Date requestedAt;
    private Date tokenReceivedAt;
    private Date profileFetchedAt;
    private String sessionKey;
    private String requestType;

    private String status=null;

    private String endpoint;



    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "TRUE_CALLER_REQUEST_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }


    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }


    @Column(name = "END_POINT")
    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    @Column(name = "TERMINAL", nullable = false)
    public Integer getTerminal() {
        return terminal;
    }

    public void setTerminal(Integer terminal) {
        this.terminal = terminal;
    }

    @Column(name = "CONTACT", nullable = false)
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Column(name = "REQUEST_ID", unique = true, nullable = false)
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Column(name = "ACCESS_TOKEN")
    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TRUE_CALLER_PROFILE_ID")
    public TrueCallerProfileDetail getProfile() {
        return profile;
    }

    public void setProfile(TrueCallerProfileDetail profile) {
        this.profile = profile;
    }

    @Column(name = "REQUESTED_AT", nullable = false)
    public Date getRequestedAt() {
        return requestedAt;
    }

    public void setRequestedAt(Date requestedAt) {
        this.requestedAt = requestedAt;
    }

    @Column(name = "TOKEN_RECEIVED_AT")
    public Date getTokenReceivedAt() {
        return tokenReceivedAt;
    }

    public void setTokenReceivedAt(Date tokenReceivedAt) {
        this.tokenReceivedAt = tokenReceivedAt;
    }

    @Column(name = "PROFILE_FETCHED_AT")
    public Date getProfileFetchedAt() {
        return profileFetchedAt;
    }

    public void setProfileFetchedAt(Date profileFetchedAt) {
        this.profileFetchedAt = profileFetchedAt;
    }

    @Column(name = "SESSION_KEY")
    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    @Column(name = "REQUEST_TYPE")
    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }
}
