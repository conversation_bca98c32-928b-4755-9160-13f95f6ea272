package com.stpl.tech.kettle.truecaller.core;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-03-2018.
 */
public class TrueCallerConstants {
    public static final String VERIFY_ENDPOINT = "https://api4.truecaller.com/v1/apps/requests";
    public static final String FETCH_USER_ENDPOINT = "https://profile4.truecaller.com/v1/default";
    public static final String REQUEST_ID_PARAM = "requestId";
    public static final String ACCESS_TOKEN_PARAM = "accessToken";
    public static final String STATE_PARAM = "state";
    public static final String APP_KEY_PARAM = "appKey";
    public static final String PHONE_NUMBER_PARAM = "phoneNumber";
    public static final String END_POINT = "endpoint";
    public static final String STATUS = "status";
    public static final String USER_REJECTED = "user_rejected";
    public static final String USER_INVOKED = "flow_invoked";

}
