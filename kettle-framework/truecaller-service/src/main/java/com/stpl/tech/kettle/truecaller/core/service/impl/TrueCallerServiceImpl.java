package com.stpl.tech.kettle.truecaller.core.service.impl;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerProfileDineIn;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.stpl.tech.kettle.core.exception.RequestValidatonException;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerPostRequest;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerRequestType;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.kettle.truecaller.core.TrueCallerConstants;
import com.stpl.tech.kettle.truecaller.core.dao.TrueCallerDao;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerAddressDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerProfileDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerRequestDetail;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerNotification;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerProfile;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerRequest;
import com.stpl.tech.kettle.truecaller.core.domain.VerificationNotification;
import com.stpl.tech.kettle.truecaller.core.service.TrueCallerService;
import com.stpl.tech.kettle.truecaller.util.TrueCallerAdapter;
import com.stpl.tech.kettle.truecaller.util.TrueCallerUtils;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.pubnub.PubnubService;
import com.stpl.tech.util.notification.pubnub.PushNotification;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;


/**
 * Copyright (C) 2018,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-03-2018.
 */

@Service
public class TrueCallerServiceImpl implements TrueCallerService {

    private static final Logger LOG = LoggerFactory.getLogger(TrueCallerServiceImpl.class);

    @Autowired
    private TrueCallerProperties env;

    @Autowired
    private TrueCallerDao trueCallerDao;

    @Autowired
    private PubnubService pubnubService;

    private static final HashMap<String, TrueCallerProfileDineIn> reqIdToProfileMap =  new HashMap<>();
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * evicting logic run every 'time period' minutes, remove all entries having diff
     * from current timestamp greater than time period minutes
     */
    @PostConstruct
    private void clearTrueCallerProfileCache() {
        LOG.info("Starting tc cache #####");
        scheduler.scheduleAtFixedRate(TrueCallerServiceImpl::removeExpiredEntries, 0, 30, TimeUnit.MINUTES);
    }

    @PreDestroy
    private void shutdownScheduler() {
        LOG.info("Stopping scheduler for cache #####");
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(60, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public String verifyProfile(TrueCallerPostRequest request) throws RequestValidatonException {
        /*TrueCallerRequestDetail requestDetail = getTrueCallerRequestForContact(request.getContact());
        if (requestDetail != null && request.getType().equals(TrueCallerRequestType.REGISTRATION)) {
            TrueCallerProfileDetail profileDetail = requestDetail.getProfile();
            TrueCallerNotification notification = new TrueCallerNotification(env.getEnvironmentType(), profileDetail, requestDetail);
            pubnubService.sendNotification(notification);
            return requestDetail.getRequestId();
        } else {*/
        HttpPost requestObject = new HttpPost(TrueCallerConstants.VERIFY_ENDPOINT);
        try {
            PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
            JSONObject requestMap = new JSONObject();
            Phonenumber.PhoneNumber number = phoneUtil.parse("+91" + request.getContact(), "");
            String phoneNumber = phoneUtil.format(number, PhoneNumberUtil.PhoneNumberFormat.E164);
            String sessionKey = AppUtils.generateRandomAlphaNumericCode(10);
            requestMap.put(TrueCallerConstants.PHONE_NUMBER_PARAM, phoneNumber);
            requestMap.put(TrueCallerConstants.STATE_PARAM, sessionKey);
            String body = requestMap.toString();
            System.out.println(body);
            org.apache.http.HttpEntity httpEntity = new StringEntity(body, AppConstants.CHARSET);
            requestObject.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
            requestObject.setHeader(TrueCallerConstants.APP_KEY_PARAM, env.getTrueCallerKey());
            requestObject.setEntity(httpEntity);
            HttpResponse response = null;

            response = WebServiceHelper.postRequest(requestObject);
            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                Map<String, String> responseMap = WebServiceHelper.convertResponse(response, Map.class);
                String requestId = responseMap.get(TrueCallerConstants.REQUEST_ID_PARAM);
                if (responseMap != null && requestId != null) {
                    LOG.info("RequestId received from TrueCaller for verify customer call :::::: {}", requestId);
                    TrueCallerRequest trueCallerRequest = new TrueCallerRequest();
                    trueCallerRequest.setRequestId(requestId);
                    trueCallerRequest.setContact(request.getContact());
                    trueCallerRequest.setSessionKey(sessionKey);
                    trueCallerRequest.setUnitId(request.getUnitId());
                    trueCallerRequest.setTerminal(request.getTerminal());
                    trueCallerRequest.setRequestedAt(AppUtils.getCurrentTimestamp());
                    trueCallerRequest.setType(request.getType());
                    trueCallerRequest = trueCallerDao.saveTrueCallerRequest(trueCallerRequest);
                    return trueCallerRequest.getRequestId();
                }
            }
        } catch (Exception e) {
            LOG.error("Error while verify true caller request", e);
            throw new RequestValidatonException(e);
        } finally {
            requestObject.releaseConnection();
            requestObject.abort();
        }
        //}
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public TrueCallerRequestDetail getTrueCallerRequestForContact(String contact) {
        TrueCallerRequestDetail requestDetail = trueCallerDao.getByContact(contact);
        return requestDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public void verifyAndSaveAccessToken(String requestId, String accessToken, String state) throws RequestValidatonException {
        LOG.info("Received callback from True caller with accessToken ::: {}", accessToken);
        List<TrueCallerRequestDetail> requestDetailList = trueCallerDao.getByRequestId(requestId);
        Optional<TrueCallerRequestDetail> requestDetail = requestDetailList.stream()
            .filter(request -> request.getSessionKey().equals(state)).findAny();
        if (requestDetail.isPresent()) {
            TrueCallerRequestDetail request = requestDetail.get();
            if (request != null && request.getRequestId().equalsIgnoreCase(requestId)
                && request.getSessionKey().equalsIgnoreCase(state)
                && request.getTokenReceivedAt() == null) { // to prevent multiple callbacks
                request.setAccessToken(accessToken);
                request.setTokenReceivedAt(AppUtils.getCurrentTimestamp());
                getProfileAndSave(request);
            } else {
                PushNotification<Map<String, TrueCallerVerifiedProfile>> notification = new TrueCallerNotification(env.getEnvironmentType(), null, request);
                pubnubService.sendNotification(notification);
                StringBuilder message = new StringBuilder("No request found with the given request id :: " + requestId + "\n");
                message.append("and access token ::::: " + accessToken + "\n");
                LOG.info(message.toString());
                TrueCallerUtils.sendSlackMessage(env.getEnvironmentType(), message, SlackNotification.SYSTEM_ERRORS);
            }
        } else {
            PushNotification<Map<String, TrueCallerVerifiedProfile>> notification = new TrueCallerNotification(env.getEnvironmentType(), null, requestDetailList.get(0));
            pubnubService.sendNotification(notification);
            StringBuilder message = new StringBuilder("No request found with the given request id :: " + requestId + "\n");
            message.append("and access token ::::: " + accessToken + "\n");
            message.append("and session key ::::: " + state + "\n");
            LOG.info(message.toString());
            TrueCallerUtils.sendSlackMessage(env.getEnvironmentType(), message, SlackNotification.SYSTEM_ERRORS);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public void getProfileFromAccessToken(String requestId, String accessToken, String endpoint, String application) throws RequestValidatonException, IOException {
        LOG.info("Received callback from True caller with accessToken ::: {}", accessToken);
        TrueCallerProfile profile = fetchProfileFromTrueCaller(accessToken, endpoint);
        if (application == "TRUE_CALLER_WEB") {
            if (profile.getPhoneNumbers() == null) {
                return;
            }

            TrueCallerRequestDetail existingTrueCallerRequestDetail = getTrueCallerRequestForContact(profile.getPhoneNumbers().get(0).substring(2));

            if (existingTrueCallerRequestDetail == null) {
                TrueCallerRequestDetail trueCallerRequestDetail = new TrueCallerRequestDetail();

                trueCallerRequestDetail.setRequestId(requestId);
                trueCallerRequestDetail.setAccessToken(accessToken);
                trueCallerRequestDetail.setEndpoint(endpoint);
                trueCallerRequestDetail.setContact(profile.getPhoneNumbers().get(0).substring(2));
                trueCallerRequestDetail.setUnitId(1111);
                trueCallerRequestDetail.setTerminal(1111);
                trueCallerRequestDetail.setRequestType("TRUECALLER_WEB");
//                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm");
//                LocalDateTime now = LocalDateTime.now();
//                Date date = new Date(dtf.format(now));
                trueCallerRequestDetail.setRequestedAt(AppUtils.getCurrentDateIST());


                TrueCallerProfileDetail profileDetail = TrueCallerAdapter.convert(profile, profile.getPhoneNumbers().get(0).substring(2));
                trueCallerRequestDetail.setAccessToken(accessToken);
                trueCallerRequestDetail.setRequestId(requestId);
                trueCallerRequestDetail.setEndpoint(endpoint);

//                trueCallerRequestDetail=trueCallerDao.saveTrueCallerWebRequest(trueCallerRequestDetail);
                saveProfile(profile, trueCallerRequestDetail);
//                saveProfile(profileDetail);

            } else {


                TrueCallerProfileDetail profileDetail = TrueCallerAdapter.convert(profile, profile.getPhoneNumbers().get(0).substring(2));
                existingTrueCallerRequestDetail.setAccessToken(accessToken);
                existingTrueCallerRequestDetail.setRequestId(requestId);
                existingTrueCallerRequestDetail.setEndpoint(endpoint);
//              existingTrueCallerRequestDetail.setProfile(profileDetail);
                TrueCallerProfileDetail existingTrueCallerProfileDetail = trueCallerDao.getProfileByContact(profile.getPhoneNumbers().get(0).substring(2));

                existingTrueCallerRequestDetail.setProfile(existingTrueCallerProfileDetail);
//                getProfileAndSave(existingTrueCallerRequestDetail);
                saveProfile(profileDetail);
            }


        } else {
            //        TrueCallerProfileDetail profileDetail = saveProfile(profile,requestDetail);
            PushNotification<Map<String, TrueCallerVerifiedProfile>> notification = new TrueCallerNotification(env.getEnvironmentType(), profile, requestId, application);
            pubnubService.sendNotification(notification);

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public TrueCallerVerifiedProfile getTrueCallerProfileForRequest(String requestID) {
        List<TrueCallerRequestDetail> requestDetailList = trueCallerDao.getByRequestId(requestID);
        if (requestDetailList != null && requestDetailList.size() > 0 && requestDetailList.get(0).getStatus()==null) {
            TrueCallerRequestDetail requestDetail = requestDetailList.get(0);
            if (requestDetail != null && requestDetail.getProfile() != null) {
                TrueCallerProfileDetail profile = requestDetail.getProfile();
                return getProfile(profile);
            }
        }
        return null;
    }


    private TrueCallerVerifiedProfile getProfile(TrueCallerProfileDetail profileDetail) {
        TrueCallerVerifiedProfile profile = new TrueCallerVerifiedProfile();
        profile.setContact(profileDetail.getPrimaryNumber());
        profile.setName(TrueCallerUtils.makeName(profileDetail.getFirstName(), profileDetail.getLastName()));
        profile.setEmail(profileDetail.getEmailId());
        profile.setTcId(profileDetail.getProfileId());
        return profile;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public void getProfileAndSave(TrueCallerRequestDetail requestDetail) throws RequestValidatonException {
        try {
            LOG.info("Access Token being sent to GET call :: {}", requestDetail.getAccessToken());
            if (requestDetail.getAccessToken() != null) { //ACCEPTED BY CUSTOMER
                TrueCallerRequestType type = TrueCallerRequestType.valueOf(requestDetail.getRequestType());
                String accessToken = "Bearer " + requestDetail.getAccessToken();
                TrueCallerProfile profile = fetchProfileFromTrueCaller(accessToken);
                if (type == null || type.equals(TrueCallerRequestType.REGISTRATION)) {
                    TrueCallerProfileDetail profileDetail = saveProfile(profile, requestDetail);
                    PushNotification<Map<String, TrueCallerVerifiedProfile>> notification = new TrueCallerNotification(env.getEnvironmentType(), profileDetail, requestDetail);
                    pubnubService.sendNotification(notification);
                } else if (type.equals(TrueCallerRequestType.GIFT_CARD) || type.equals(TrueCallerRequestType.REDEMPTION)) {
                    TrueCallerRequestDetail previouslyFetched = trueCallerDao.getByContact(requestDetail.getContact());
                    if (previouslyFetched == null || !compare(profile, previouslyFetched.getProfile())) {
                        TrueCallerProfileDetail profileDetail = saveProfile(profile, requestDetail);
                        requestDetail.setProfile(profileDetail);
                    }
                    PushNotification<Map<String, TrueCallerRequest>> notification =
                        new VerificationNotification(env.getEnvironmentType(), TrueCallerAdapter.convert(requestDetail));
                    pubnubService.sendNotification(notification);
                } else if(type.equals(TrueCallerRequestType.TRUECALLER_WEB) )
                {
                    TrueCallerProfileDetail profileDetail = saveProfile(profile, requestDetail);
                }
                else {
                    StringBuilder message = new StringBuilder("INVALID REQUEST TYPE FOR TRUECALLER FOR CONTACT :: ")
                        .append(requestDetail.getContact()).append("\n")
                        .append("SESSION KEY :: ").append(requestDetail.getSessionKey());
                    TrueCallerUtils.sendSlackMessage(env.getEnvironmentType(), message, SlackNotification.SYSTEM_ERRORS);
                }
            } else { // REJECTED BY CUSTOMER
                requestDetail.setProfile(null);
                requestDetail.setProfileFetchedAt(AppUtils.getCurrentTimestamp());
                trueCallerDao.updateRequest(requestDetail);
                PushNotification<Map<String, TrueCallerVerifiedProfile>> notification = new TrueCallerNotification(env.getEnvironmentType(), null, requestDetail);
                pubnubService.sendNotification(notification);
            }

        } catch (Exception e) {
            StringBuilder message = new StringBuilder("Error while requesting true caller profile :: ")
                .append(requestDetail.getContact()).append("\n")
                .append("SESSION KEY :: ").append(requestDetail.getSessionKey()).append("\n")
                .append("ERROR ::: \n").append(e.getMessage());
            LOG.error(message.toString(), e);
            TrueCallerUtils.sendSlackMessage(env.getEnvironmentType(), message, SlackNotification.SYSTEM_ERRORS);
            throw new RequestValidatonException("Error while fetching profile details for contact :: " + requestDetail.getContact());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public void saveProfile(TrueCallerProfileDetail profile) {
        if (profile != null) {
            TrueCallerProfileDetail profileDetail = trueCallerDao.getProfileByContact(profile.getPrimaryNumber());
            List<TrueCallerAddressDetail> newAddresses = new ArrayList<>();
            if (profileDetail == null) {
                profileDetail = profile;
                newAddresses.addAll(profile.getAddressList());
            } else {
                if (profile.getAvatarUrl() != null) {
                    profileDetail.setAvatarUrl(profile.getAvatarUrl());
                }
                if (profile.getCompanyName() != null) {
                    profileDetail.setCompanyName(profile.getCompanyName());
                }
                if (profile.getEmailId() != null) {
                    profileDetail.setEmailId(profile.getEmailId());
                }
                if (profile.getFacebookId() != null) {
                    profileDetail.setFacebookId(profile.getFacebookId());
                }
                profileDetail.setFirstName(profile.getFirstName());
                if (profile.getGender() != null) {
                    profileDetail.setGender(profile.getGender());
                }
                if (profile.getJobTitle() != null) {
                    profileDetail.setJobTitle(profile.getJobTitle());
                }
                profileDetail.setLastName(profile.getLastName());
                profileDetail.setPrimaryNumber(profile.getPrimaryNumber());
                if (profile.getTwitterId() != null) {
                    profileDetail.setTwitterId(profile.getTwitterId());
                }
                for (TrueCallerAddressDetail detail : profile.getAddressList()) {
                    boolean found = false;
                    if(((detail.getCity()==null) &&  (detail.getStreet()==null)) && (detail.getZipcode()==null))
                    {
                        found=true;
                    }
                    for (TrueCallerAddressDetail address : profileDetail.getAddressList()) {
                        if(address.getCity() != null && address.getCity().equalsIgnoreCase(detail.getCity()) && address.getStreet()!=null &&
                            address.getStreet().equalsIgnoreCase(detail.getStreet()) && address.getCountryCode() != null &&
                            address.getCountryCode().equalsIgnoreCase(detail.getCountryCode()) && address.getZipcode() != null &&
                            address.getZipcode().equalsIgnoreCase(detail.getZipcode())){
                            found = true;
                        }

                    }
                    if (!found) {
                        newAddresses.add(detail);
                    }
                }
            }
            profileDetail = trueCallerDao.saveProfile(profileDetail);
            if (profileDetail != null) {
                trueCallerDao.saveAddresses(newAddresses, profileDetail);
                CustomerInfo customerInfo = trueCallerDao.getCustomerInfo(profileDetail);
                if(customerInfo != null) {
                    customerInfo.setTrueCallerProfileId(profileDetail.getProfileId());
                    customerInfo = trueCallerDao.saveCustomerInfo(customerInfo);
                    if(customerInfo != null) {
                        for(TrueCallerAddressDetail addressDetail : newAddresses) {
                            if(addressDetail.getStreet() != null) {
                                CustomerAddressInfo customerAddressInfo = new CustomerAddressInfo();
                                customerAddressInfo.setAddressLine1(addressDetail.getStreet());
                                customerAddressInfo.setAddressType("HOME");
                                customerAddressInfo.setCity(addressDetail.getCity());
                                customerAddressInfo.setCompany(profile.getCompanyName());
                                customerAddressInfo.setCountry("India");
                                customerAddressInfo.setCustomerInfo(customerInfo);
                                customerAddressInfo.setZipcode(addressDetail.getZipcode());
                                customerAddressInfo.setState("");
                                customerAddressInfo.setName(customerInfo.getFirstName());
                                customerAddressInfo.setEmail(customerInfo.getEmailId());
                                customerAddressInfo.setLocality("");
                                trueCallerDao.saveCustomerAddress(customerAddressInfo);
                            }
                        }
                    }
                }
            }
        }
    }

    private boolean compare(TrueCallerProfile profile, TrueCallerProfileDetail profileDetail) {
        boolean flag = profileDetail != null ? TrueCallerAdapter.convertProfile(profileDetail).equals(profile) : false;
        LOG.info(":::::::::::::::::::Profile fetched from previous profile is null {}:::::::::::::::::::::::::::", profileDetail == null);
        LOG.info(":::::::::::::::::::Equals flag for profile equality {}:::::::::::::::::::::::::::", flag);
        return flag;
    }

    private TrueCallerProfileDetail saveProfile(TrueCallerProfile profile, TrueCallerRequestDetail requestDetail) {
        if (profile != null) {
            TrueCallerProfileDetail profileDetail = trueCallerDao.saveProfile(profile, requestDetail.getContact());
            if (profileDetail.getProfileId() != null) {
                requestDetail.setProfile(profileDetail);
                requestDetail.setProfileFetchedAt(AppUtils.getCurrentTimestamp());
                trueCallerDao.updateRequest(requestDetail);
            }
            return profileDetail;
        }
        return null;

    }


    private TrueCallerProfile fetchProfileFromTrueCaller(String accessToken) throws IOException {
        return fetchProfileFromTrueCaller(accessToken, TrueCallerConstants.FETCH_USER_ENDPOINT);
    }


    private TrueCallerProfile fetchProfileFromTrueCaller(String accessToken, String endpoint) throws IOException {
        HttpGet reqObj = new HttpGet(endpoint);
        reqObj.setHeader("Authorization", "Bearer " + accessToken);
        reqObj.setHeader("Content-type", "application/json");
        HttpResponse response = WebServiceHelper.getRequest(reqObj);
        TrueCallerProfile profile = WebServiceHelper.convertResponse(response, TrueCallerProfile.class);
        LOG.info("::::::::::::::::::::: Received the user profile :::::::::::::::::");
        LOG.info(JSONSerializer.toJSON(profile));
        reqObj.releaseConnection();
        reqObj.abort();
        return profile;
    }

    @Override
    public void getProfileForTokenAndPutToCache(String requestId, String accessToken, String endpoint) throws IOException {
        TrueCallerProfile profile = fetchProfileFromTrueCaller(accessToken, endpoint);
        // convert profile to dine format
        TrueCallerProfileDineIn trueCallerProfileDineIn = TrueCallerAdapter.convertProfile(profile);
        trueCallerProfileDineIn.setRequestNonce(requestId);
        trueCallerProfileDineIn.setProfileReceiveTime(AppUtils.getCurrentTimestamp());
        // add to cache
        reqIdToProfileMap.put(requestId, trueCallerProfileDineIn);
    }

    private static void removeExpiredEntries() {
        try {
            Date currentTime = AppUtils.getCurrentTimestamp();
            LOG.info("Removing entries tc cache ##### {}", currentTime);

            // Remove entries whose duration exceeds the expiration time
            reqIdToProfileMap.entrySet().removeIf(entry -> {
                TrueCallerProfileDineIn profile = entry.getValue();
                Date timeAdded = profile.getProfileReceiveTime();
                // Check if the entry's time exceeds the expiry duration
                return AppUtils.getTimeDiffernceInMinutes(timeAdded, currentTime) > 20;
            });
        } catch (Exception e) {
            LOG.error("Error while clearing entries", e);
        }
    }

    @Override
    public TrueCallerProfileDineIn getProfileFromCache(String requestId) {
        return reqIdToProfileMap.get(requestId);
    }

    @Override
    public TrueCallerProfileDineIn putProfileToCache(String requestId, TrueCallerProfileDineIn trueCallerProfileDineIn) {
        return reqIdToProfileMap.put(requestId, trueCallerProfileDineIn);
    }

}
