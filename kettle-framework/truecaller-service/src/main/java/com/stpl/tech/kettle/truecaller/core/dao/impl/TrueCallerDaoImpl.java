package com.stpl.tech.kettle.truecaller.core.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.truecaller.core.dao.TrueCallerDao;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerAddressDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerProfileDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerRequestDetail;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerProfile;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerRequest;
import com.stpl.tech.kettle.truecaller.util.TrueCallerAdapter;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 05-03-2018.
 */
@Repository
public class TrueCallerDaoImpl implements TrueCallerDao {

    private static final Logger LOG = LoggerFactory.getLogger(TrueCallerDaoImpl.class);

    @PersistenceContext(unitName = "TransactionDataSourcePUName")
    @Qualifier(value = "TransactionDataSourceEMFactory")
    private EntityManager manager;

    @Override
    public TrueCallerRequest saveTrueCallerRequest(TrueCallerRequest request) {
        try {
            TrueCallerRequestDetail detail = TrueCallerAdapter.convert(request);
            manager.persist(detail);
            manager.flush();
            return TrueCallerAdapter.convert(detail);
        } catch (Exception e) {
            LOG.error("Error while persisting request data ", e);
            throw e;
        }
    }

    @Override
    public TrueCallerRequestDetail saveTrueCallerWebRequest(TrueCallerRequestDetail detail) {
        try {
            manager.persist(detail);
            manager.flush();
            return (detail);
        } catch (Exception e) {
            LOG.error("Error while persisting request data ", e);
            throw e;
        }
    }


    @Override
    public List<TrueCallerRequestDetail> getByRequestId(String requestId) {
        Query query = manager.createQuery("SELECT E FROM TrueCallerRequestDetail E " +
                "WHERE E.requestId=:requestId ORDER BY E.id DESC");
        query.setParameter("requestId", requestId).setMaxResults(1);
        List<TrueCallerRequestDetail> requestDetails = (List<TrueCallerRequestDetail>) query.getResultList();
        if (requestDetails != null && requestDetails.size() > 0) {
            return requestDetails;
        }
        return null;
    }

    @Override
    public TrueCallerRequestDetail getByContact(String contact) {
        Query query = manager.createQuery("SELECT E FROM TrueCallerRequestDetail E " +
                "WHERE E.contact=:contact AND E.accessToken IS NOT NULL AND E.profileFetchedAt IS NOT NULL ORDER BY E.id DESC");
        query.setParameter("contact", contact).setMaxResults(1);
        List<TrueCallerRequestDetail> requestDetails = (List<TrueCallerRequestDetail>) query.getResultList();
        if (requestDetails != null && requestDetails.size() > 0) {
            return requestDetails.get(0);
        }
        return null;
    }

    @Override
    public List<TrueCallerRequestDetail> getAllByContact(String contact) {
        Query query = manager.createQuery("SELECT E FROM TrueCallerRequestDetail E " +
                "WHERE E.contact=:contact AND E.profileFetchedAt IS NOT NULL ORDER BY E.id DESC");
        query.setParameter("contact", contact).setMaxResults(1);
        List<TrueCallerRequestDetail> requestDetails = (List<TrueCallerRequestDetail>) query.getResultList();
        return requestDetails;
    }

    @Override
    public TrueCallerProfileDetail getProfileByContact(String contact) {
        try {
            Query query = manager.createQuery("FROM TrueCallerProfileDetail t WHERE t.primaryNumber = :contact");
            query.setParameter("contact", contact);
            List<TrueCallerProfileDetail> profileDetail = query.getResultList();
            if(profileDetail != null && !profileDetail.isEmpty()) {
                return profileDetail.get(0);
            }
            return null;
        } catch (Exception e) {
            LOG.error("Error while getting truecaller profile data ", e);
            throw e;
        }
    }

    @Override
    public TrueCallerProfileDetail saveProfile(TrueCallerProfile trueCallerProfile, String contact) {
        try {
            TrueCallerProfileDetail profileDetail = TrueCallerAdapter.convert(trueCallerProfile, contact);
            manager.persist(profileDetail);
            manager.flush();
            saveAddresses(profileDetail.getAddressList(), profileDetail);
            return profileDetail;
        } catch (Exception e) {
            LOG.error("Error while persisting request data ", e);
            throw e;
        }
    }

    @Override
    public TrueCallerProfileDetail saveProfile(TrueCallerProfileDetail trueCallerProfile) {
        try {
            manager.persist(trueCallerProfile);
            manager.flush();
            return trueCallerProfile;
        } catch (Exception e) {
            LOG.error("Error while persisting request data ", e);
            throw e;
        }
    }

    @Override
    public void saveAddresses(List<TrueCallerAddressDetail> addressDetails, TrueCallerProfileDetail profileDetail) {

        for (TrueCallerAddressDetail addressDetail : addressDetails) {
            addressDetail.setProfileData(profileDetail);
            if(addressDetail.getCity()==null && addressDetail.getStreet()==null && addressDetail.getZipcode()==null)
            {
                continue;
            }
            manager.persist(addressDetail);
        }
        manager.flush();
    }

    @Override
    public TrueCallerRequest updateRequest(TrueCallerRequestDetail requestDetail) {
        try {
            manager.merge(requestDetail);
            manager.flush();
            return TrueCallerAdapter.convert(requestDetail);
        } catch (Exception e) {
            LOG.error("Error while merging request data ", e);
            throw e;
        }
    }

    @Override
    public CustomerInfo getCustomerInfo(TrueCallerProfileDetail profile) {
        try {
            Query query = manager.createQuery("FROM CustomerInfo c WHERE c.contactNumber = :contact");
            query.setParameter("contact", profile.getPrimaryNumber());
            List<CustomerInfo> profileDetail = query.getResultList();
            if(profileDetail != null && !profileDetail.isEmpty()) {
                return profileDetail.get(0);
            }
            return null;
        } catch (Exception e) {
            LOG.error("Error while getting truecaller profile data ", e);
            throw e;
        }
    }

    @Override
    public CustomerInfo saveCustomerInfo(CustomerInfo profile) {
        try {
            profile = manager.merge(profile);
            manager.flush();
            return profile;
        } catch (Exception e) {
            LOG.error("Error while updating truecaller profile id in customer info ", e);
            throw e;
        }
    }

    @Override
    public CustomerAddressInfo saveCustomerAddress(CustomerAddressInfo address) {
        try {
            address = manager.merge(address);
            manager.flush();
            return address;
        } catch (Exception e) {
            LOG.error("Error while updating truecaller profile id in customer info ", e);
            throw e;
        }
    }
}
