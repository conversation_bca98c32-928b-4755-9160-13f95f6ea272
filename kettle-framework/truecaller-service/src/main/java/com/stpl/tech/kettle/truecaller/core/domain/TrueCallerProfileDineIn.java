package com.stpl.tech.kettle.truecaller.core.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.mongodb.core.index.Indexed;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TrueCallerProfileDineIn implements Serializable {

    private String id;

    private boolean successful;

    private String firstName;

    private String lastName;

    private String phoneNumber;

    private String gender;

    private String street;

    private String city;

    private String zipcode;

    private String countryCode;

    private String facebookId;

    private String twitterId;

    private String email;

    private String url;

    private String avatarUrl;

    private boolean isTrueName;

    private boolean isAmbassador;

    private String companyName;

    private String jobTitle;

    private String payload;

    private String signature;

    private String signatureAlgorithm;

    private String requestNonce;

    private Date lastUpdated;

    private String countryShortCode;

    private String verificationMode;

    private boolean isSimChanged;

    private Boolean optWhatsapp;

    private Date profileReceiveTime;

    private String consentStatus;
}
