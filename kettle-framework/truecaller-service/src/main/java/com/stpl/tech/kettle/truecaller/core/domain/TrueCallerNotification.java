package com.stpl.tech.kettle.truecaller.core.domain;

import java.util.HashMap;
import java.util.Map;

import com.google.gson.Gson;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerProfileDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerRequestDetail;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.pubnub.PushNotification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 26-11-2016.
 */
public class TrueCallerNotification extends AbstractNotication implements PushNotification<Map<String,TrueCallerVerifiedProfile>> {

    private static final Logger LOG = LoggerFactory.getLogger(TrueCallerNotification.class);

    private String channelName;
    private int retries;
    private Map<String,TrueCallerVerifiedProfile> message;


    public TrueCallerNotification(EnvType env, TrueCallerProfileDetail profileDetail, TrueCallerRequestDetail requestDetail) {
        TrueCallerVerifiedProfile profile = new TrueCallerVerifiedProfile();
        if(profileDetail!=null) {
            profile.setContact(profileDetail.getPrimaryNumber());
            profile.setEmail(profileDetail.getEmailId());
            profile.setName(getName(profileDetail.getFirstName(), profileDetail.getLastName()));
            profile.setTcId(profileDetail.getProfileId());
        }
        this.channelName = initChannel(env, requestDetail.getUnitId(), requestDetail.getTerminal(), "");
        Map<String,TrueCallerVerifiedProfile> message = new HashMap<>();
        message.put(getMessageKey(requestDetail.getRequestType()), profile);
        setMessage(message);
    }


    public TrueCallerNotification(EnvType env, TrueCallerProfile profileDetail, String requestId, String application) {
        TrueCallerVerifiedProfile profile = new TrueCallerVerifiedProfile();
        if(profileDetail!=null && !profileDetail.getPhoneNumbers().isEmpty()) {
            profile.setContact(profileDetail.getPhoneNumbers().get(0));
            profile.setEmail(profileDetail.getOnlineIdentities().getEmail());
            profile.setName(getName(profileDetail.getName().getFirst(), profileDetail.getName().getLast()));
            profile.setTcId(profileDetail.getProfileId());
        }
        String[] data = requestId.split("\\*");
        LOG.info(new Gson().toJson(data));
        String[] unitData = data[0].split("#");
        this.channelName = initChannel(env, Integer.parseInt(unitData[0]), Integer.parseInt(unitData[1]), application);
        Map<String,TrueCallerVerifiedProfile> message = new HashMap<>();
        message.put(getMessageKey(null), profile);
        setMessage(message);
    }


    @Override
    public Map<String,TrueCallerVerifiedProfile> getMessage() {
        return this.message;
    }

    @Override
    public void setMessage(Map<String,TrueCallerVerifiedProfile> message) {
        this.message = message;
    }

    @Override
    public String getChannelName() {
        return this.channelName;
    }

    @Override
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    @Override
    public int getRetries(){
        return retries;
    }

    @Override
    public void setRetries(int value) {
        this.retries = value;
    }

    @Override
    public int hashCode() {
        int result = channelName.hashCode();
        result = 31 * result + message.hashCode();
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }

        if (!(o instanceof TrueCallerNotification)){
            return false;
        }

        TrueCallerNotification that = (TrueCallerNotification) o;
        if (!channelName.equals(that.channelName)){
            return false;
        }

        if(this.message==null || that.message==null){
            return false;
        }

        String[] keySet1 = (String[]) message.keySet().toArray();
        String[] keySet2 = (String[]) that.message.keySet().toArray();
        if(this.message.get(keySet1[0])==null || that.message.get(keySet2[0])==null){
            return false;
        }
        return this.message.get(keySet1[0]).getContact().equals(that.message.get(keySet2[0]).getContact());
    }
}
