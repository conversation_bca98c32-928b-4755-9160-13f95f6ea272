package com.stpl.tech.kettle.truecaller.core.domain;

import java.util.Objects;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 05-03-2018.
 */
public class TrueCallerName {

    private String first;
    private String last;

    public TrueCallerName() {
    }

    public TrueCallerName(String firstName, String lastName) {
        this.first = firstName;
        this.last = lastName;
    }

    public String getFirst() {
        return first;
    }

    public void setFirst(String first) {
        this.first = first;
    }

    public String getLast() {
        return last;
    }

    public void setLast(String last) {
        this.last = last;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TrueCallerName)) return false;
        TrueCallerName that = (TrueCallerName) o;
        return Objects.equals(first, that.first) && Objects.equals(last, that.last);
    }

    @Override
    public int hashCode() {
        return Objects.hash(first, last);
    }
}
