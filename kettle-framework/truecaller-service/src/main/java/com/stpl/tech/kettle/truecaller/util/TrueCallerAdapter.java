package com.stpl.tech.kettle.truecaller.util;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.base.Joiner;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerRequestType;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerAddressDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerProfileDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerRequestDetail;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerAddress;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerName;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerOnlineIdentity;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerProfile;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerProfileDineIn;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerRequest;
import com.stpl.tech.util.AppConstants;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 24-03-2018.
 */

public class TrueCallerAdapter {

    public static TrueCallerRequest convert(TrueCallerRequestDetail detail) {
        TrueCallerRequest request = new TrueCallerRequest();
        request.setId(detail.getId());
        request.setContact(detail.getContact());
        request.setRequestId(detail.getRequestId());
        request.setAccessToken(detail.getAccessToken());
        request.setRequestedAt(detail.getRequestedAt());
        request.setTokenReceivedAt(detail.getTokenReceivedAt());
        request.setProfileFetchedAt(detail.getProfileFetchedAt());
        request.setTerminal(detail.getTerminal());
        request.setUnitId(detail.getUnitId());
        if (detail.getProfile() != null) {
            request.setTrueCallerProfile(convert(detail.getProfile()));
        }
        request.setType(TrueCallerRequestType.valueOf(detail.getRequestType()));
        return request;
    }

    public static TrueCallerVerifiedProfile convert(TrueCallerProfileDetail data) {
        TrueCallerVerifiedProfile profile = new TrueCallerVerifiedProfile();
        profile.setTcId(data.getProfileId());
        profile.setContact(data.getPrimaryNumber());
        profile.setName(TrueCallerUtils.makeName(data.getFirstName(), data.getLastName()));
        profile.setEmail(data.getEmailId());
        return profile;
    }

    public static TrueCallerRequestDetail convert(TrueCallerRequest request) {
        TrueCallerRequestDetail detail = new TrueCallerRequestDetail();
        detail.setContact(request.getContact());
        detail.setRequestId(request.getRequestId());
        detail.setAccessToken(request.getAccessToken());
        detail.setSessionKey(request.getSessionKey());
        detail.setRequestedAt(request.getRequestedAt());
        detail.setTokenReceivedAt(request.getTokenReceivedAt());
        detail.setProfileFetchedAt(request.getProfileFetchedAt());
        detail.setUnitId(request.getUnitId());
        detail.setTerminal(request.getTerminal());
        detail.setRequestType(request.getType().name());
        return detail;
    }

    public static TrueCallerProfileDetail convert(TrueCallerProfile trueCallerProfile, String contact) {
        TrueCallerProfileDetail profileDetail = new TrueCallerProfileDetail();
        profileDetail.setPrimaryNumber(contact);
        profileDetail.setOtherNumbers(Joiner.on(",").skipNulls().join(trueCallerProfile.getPhoneNumbers()));
        profileDetail.setProfileId(trueCallerProfile.getProfileId());
        profileDetail.setFirstName(trueCallerProfile.getName().getFirst());
        profileDetail.setLastName(trueCallerProfile.getName().getLast());
        profileDetail.setAvatarUrl(trueCallerProfile.getAvatarUrl());
        profileDetail.setGender(trueCallerProfile.getGender());
        boolean isActive = Boolean.valueOf(trueCallerProfile.getIsActive());
        profileDetail.setActiveProfile(isActive ? AppConstants.YES : AppConstants.NO);
        profileDetail.setJobTitle(trueCallerProfile.getJobTitle());
        profileDetail.setCompanyName(trueCallerProfile.getCompanyName());
        profileDetail.setPrivacyMode(trueCallerProfile.getPrivacy());
        profileDetail.setProfileType(trueCallerProfile.getType());
        TrueCallerOnlineIdentity onlineIdentity = trueCallerProfile.getOnlineIdentities();
        if (onlineIdentity != null) {
            profileDetail.setFacebookId(onlineIdentity.getFacebookId());
            profileDetail.setTwitterId(onlineIdentity.getTwitterId());
            profileDetail.setEmailId(onlineIdentity.getEmail());
            profileDetail.setWebsite(onlineIdentity.getUrl());
        }
        profileDetail.setAddressList(convert(trueCallerProfile.getAddresses()));
        return profileDetail;
    }

    public static List<TrueCallerAddressDetail> convert(List<TrueCallerAddress> addresses) {
        return addresses.stream().map(TrueCallerAdapter::convert).collect(Collectors.toList());
    }

    public static TrueCallerAddressDetail convert(TrueCallerAddress trueCallerAddress) {
        TrueCallerAddressDetail address = new TrueCallerAddressDetail();
        address.setAddressId(trueCallerAddress.getAddressId());
        address.setCity(trueCallerAddress.getCity());
        address.setCountryCode(trueCallerAddress.getCountryCode());
        address.setStreet(trueCallerAddress.getStreet());
        address.setZipcode(trueCallerAddress.getZipcode());
        return address;
    }

    public static TrueCallerProfile convertProfile(TrueCallerProfileDetail profileDetail) {
        TrueCallerProfile profile = new TrueCallerProfile();
        profile.setProfileId(profileDetail.getProfileId());
        profile.setAvatarUrl(profileDetail.getAvatarUrl());
        profile.setName(new TrueCallerName(profileDetail.getFirstName(),profileDetail.getLastName()));
        profile.setOnlineIdentities(new TrueCallerOnlineIdentity(profileDetail));
        profile.setCompanyName(profileDetail.getCompanyName());
        profile.setGender(profileDetail.getGender());
        profile.setIsActive(profileDetail.getActiveProfile());
        profile.setJobTitle(profileDetail.getJobTitle());
        profile.setPrivacy(profileDetail.getPrivacyMode());
        profile.setType(profileDetail.getProfileType());
        List<String> phoneNumbers = Arrays.asList(profileDetail.getPrimaryNumber());
        profile.setPhoneNumbers(phoneNumbers);
        return profile;
    }

    public static TrueCallerProfileDineIn convertProfile(TrueCallerProfile trueCallerProfile) {
        return TrueCallerProfileDineIn.builder()
                .phoneNumber(trueCallerProfile.getPhoneNumbers() != null && !trueCallerProfile.getPhoneNumbers().isEmpty() ? "+" + trueCallerProfile.getPhoneNumbers().get(0) : null)
                .countryCode(trueCallerProfile.getAddresses() != null && !trueCallerProfile.getAddresses().isEmpty() ? trueCallerProfile.getAddresses().get(0).getCountryCode() : null)
                .firstName(trueCallerProfile.getName() != null ? trueCallerProfile.getName().getFirst() : null)
                .lastName(trueCallerProfile.getName() != null ? trueCallerProfile.getName().getLast() : null)
                .email(trueCallerProfile.getOnlineIdentities() != null ? trueCallerProfile.getOnlineIdentities().getEmail() : null)
                .gender(trueCallerProfile.getGender() != null && !trueCallerProfile.getGender().equalsIgnoreCase("NonSet") ? trueCallerProfile.getGender() : null)
                .avatarUrl(trueCallerProfile.getAvatarUrl())
                .facebookId(trueCallerProfile.getOnlineIdentities() != null ? trueCallerProfile.getOnlineIdentities().getFacebookId() : null)
                .jobTitle(trueCallerProfile.getJobTitle())
                .twitterId(trueCallerProfile.getOnlineIdentities() != null ? trueCallerProfile.getOnlineIdentities().getTwitterId() : null)
                .city(trueCallerProfile.getAddresses() != null && !trueCallerProfile.getAddresses().isEmpty() ? trueCallerProfile.getAddresses().get(0).getCity() : null)
                .street(trueCallerProfile.getAddresses() != null && !trueCallerProfile.getAddresses().isEmpty() ? trueCallerProfile.getAddresses().get(0).getStreet() : null)
                .zipcode(trueCallerProfile.getAddresses() != null && !trueCallerProfile.getAddresses().isEmpty() ? trueCallerProfile.getAddresses().get(0).getZipcode() : null)
                .build();
    }
}

