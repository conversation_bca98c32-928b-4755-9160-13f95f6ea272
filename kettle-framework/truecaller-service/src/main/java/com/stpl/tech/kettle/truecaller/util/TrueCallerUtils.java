package com.stpl.tech.kettle.truecaller.util;

import java.util.Arrays;
import java.util.List;

import com.google.common.base.Joiner;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.EnvType;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 24-03-2018.
 */
public class TrueCallerUtils {

    public static String makeName(String... objs){
        List<String> nameList = Arrays.asList(objs);
        String fullName = Joiner.on(" ").skipNulls().join(nameList);
        return fullName;
    }

    public static void sendSlackMessage(EnvType envType, StringBuilder message, SlackNotification notification) {
        message.append("--------------------------------------------------------");
        SlackNotificationService.getInstance().sendNotification(envType, "TRUECALLER",
                notification, message.toString());
    }
}
