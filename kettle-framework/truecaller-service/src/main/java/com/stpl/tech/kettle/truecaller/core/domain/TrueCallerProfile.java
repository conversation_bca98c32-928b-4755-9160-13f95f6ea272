package com.stpl.tech.kettle.truecaller.core.domain;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 05-03-2018.
 */
public class TrueCallerProfile {

    private Integer profileId;
    private TrueCallerName name;
    private String aboutMe;
    private List<String> phoneNumbers;
    private TrueCallerOnlineIdentity onlineIdentities;
    private String companyName;
    private String jobTitle;
    private List<TrueCallerAddress> addresses;
    private String gender;
    private String type;
    private String avatarUrl;
    private String isActive;
    private String privacy;

    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    public TrueCallerName getName() {
        return name;
    }

    public void setName(TrueCallerName name) {
        this.name = name;
    }

    public String getAboutMe() {
        return aboutMe;
    }

    public void setAboutMe(String aboutMe) {
        this.aboutMe = aboutMe;
    }

    public List<String> getPhoneNumbers() {
        return phoneNumbers;
    }

    public void setPhoneNumbers(List<String> phoneNumbers) {
        this.phoneNumbers = phoneNumbers;
    }

    public TrueCallerOnlineIdentity getOnlineIdentities() {
        return onlineIdentities;
    }

    public void setOnlineIdentities(TrueCallerOnlineIdentity onlineIdentities) {
        this.onlineIdentities = onlineIdentities;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public List<TrueCallerAddress> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<TrueCallerAddress> addresses) {
        this.addresses = addresses;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TrueCallerProfile)) return false;
        TrueCallerProfile profile = (TrueCallerProfile) o;
        boolean numberFlag = CollectionUtils.isEqualCollection(profile.getPhoneNumbers(), this.getPhoneNumbers());
        return Objects.equals(name, profile.name) && numberFlag &&
                Objects.equals(onlineIdentities, profile.onlineIdentities);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, phoneNumbers, onlineIdentities);
    }

    public String getPrivacy() {
        return privacy;
    }

    public void setPrivacy(String privacy) {
        this.privacy = privacy;
    }


}
