package com.stpl.tech.kettle.truecaller.core.domain;

import java.util.Objects;

import com.stpl.tech.kettle.truecaller.core.data.TrueCallerProfileDetail;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 05-03-2018.
 */
public class TrueCallerOnlineIdentity {
    private String facebookId;
    private String twitterId;
    private String email;
    private String url;

    public TrueCallerOnlineIdentity() {
    }

    public TrueCallerOnlineIdentity(TrueCallerProfileDetail profileDetail) {
        this.facebookId = profileDetail.getFacebookId();
        this.twitterId = profileDetail.getTwitterId();
        this.email = profileDetail.getEmailId();
        this.url = profileDetail.getWebsite();
    }

    public String getFacebookId() {
        return facebookId;
    }

    public void setFacebookId(String facebookId) {
        this.facebookId = facebookId;
    }

    public String getTwitterId() {
        return twitterId;
    }

    public void setTwitterId(String twitterId) {
        this.twitterId = twitterId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TrueCallerOnlineIdentity)) return false;
        TrueCallerOnlineIdentity that = (TrueCallerOnlineIdentity) o;
        return Objects.equals(facebookId, that.facebookId) &&
                Objects.equals(twitterId, that.twitterId) &&
                Objects.equals(email, that.email);
    }

    @Override
    public int hashCode() {
        return Objects.hash(facebookId, twitterId, email);
    }
}
