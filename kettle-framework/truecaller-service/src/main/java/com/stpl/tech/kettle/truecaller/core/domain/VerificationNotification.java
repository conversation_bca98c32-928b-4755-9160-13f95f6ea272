package com.stpl.tech.kettle.truecaller.core.domain;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.pubnub.PushNotification;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by s<PERSON><PERSON> on 22-03-2018.
 */
public class VerificationNotification extends AbstractNotication implements PushNotification<Map<String,TrueCallerRequest>> {

    private String channelName;
    private int retries;
    private Map<String,TrueCallerRequest> message;


    public VerificationNotification(EnvType env, TrueCallerRequest request) {
        this.channelName = initChannel(env,request.getUnitId(),request.getTerminal(), "");
        Map<String, TrueCallerRequest> requestMap = new HashMap<>();
        requestMap.put(getMessageKey(request.getType().name()),request);
        setMessage(requestMap);
    }

    @Override
    public Map<String, TrueCallerRequest> getMessage() {
        return this.message;
    }

    @Override
    public void setMessage(Map<String, TrueCallerRequest> message) {
        this.message = message;
    }


    @Override
    public String getChannelName() {
        return this.channelName;
    }

    @Override
    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    @Override
    public int getRetries() {
        return this.retries;
    }

    @Override
    public void setRetries(int value) {
        this.retries = value;
    }
}
