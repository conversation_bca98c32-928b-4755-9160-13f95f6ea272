package com.stpl.tech.kettle.truecaller.core.domain;

import java.util.Date;

import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerRequestType;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 05-03-2018.
 */
public class TrueCallerRequest {
    private Integer id;
    private Integer unitId;
    private Integer terminal;
    private String contact;
    private String requestId;
    private String accessToken;
    private String sessionKey;
    private TrueCallerVerifiedProfile trueCallerProfile;
    private Date requestedAt;
    private Date tokenReceivedAt;
    private Date profileFetchedAt;
    private int retryCount;
    private TrueCallerRequestType type;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getTerminal() {
        return terminal;
    }

    public void setTerminal(Integer terminal) {
        this.terminal = terminal;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public TrueCallerVerifiedProfile getTrueCallerProfile() {
        return trueCallerProfile;
    }

    public void setTrueCallerProfile(TrueCallerVerifiedProfile trueCallerProfile) {
        this.trueCallerProfile = trueCallerProfile;
    }

    public Date getRequestedAt() {
        return requestedAt;
    }

    public void setRequestedAt(Date requestedAt) {
        this.requestedAt = requestedAt;
    }

    public Date getTokenReceivedAt() {
        return tokenReceivedAt;
    }

    public void setTokenReceivedAt(Date tokenReceivedAt) {
        this.tokenReceivedAt = tokenReceivedAt;
    }

    public Date getProfileFetchedAt() {
        return profileFetchedAt;
    }

    public void setProfileFetchedAt(Date profileFetchedAt) {
        this.profileFetchedAt = profileFetchedAt;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public TrueCallerRequestType getType() {
        return type;
    }

    public void setType(TrueCallerRequestType type) {
        this.type = type;
    }
}
