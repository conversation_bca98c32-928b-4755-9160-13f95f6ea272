package com.stpl.tech.kettle.truecaller.core.dao;

import java.util.List;

import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerAddressDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerProfileDetail;
import com.stpl.tech.kettle.truecaller.core.data.TrueCallerRequestDetail;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerProfile;
import com.stpl.tech.kettle.truecaller.core.domain.TrueCallerRequest;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 05-03-2018.
 */
public interface TrueCallerDao {
    public TrueCallerRequest saveTrueCallerRequest(TrueCallerRequest request);

    public TrueCallerRequestDetail saveTrueCallerWebRequest(TrueCallerRequestDetail detail);

    public List<TrueCallerRequestDetail> getByRequestId(String requestId);

    TrueCallerProfileDetail getProfileByContact(String contact);

    public TrueCallerProfileDetail saveProfile(TrueCallerProfile trueCallerProfile, String contact);

    TrueCallerProfileDetail saveProfile(TrueCallerProfileDetail trueCallerProfile);

    void saveAddresses(List<TrueCallerAddressDetail> addressDetails, TrueCallerProfileDetail profileDetail);

    public TrueCallerRequest updateRequest(TrueCallerRequestDetail requestDetail);

    public TrueCallerRequestDetail getByContact(String contact);

    public List<TrueCallerRequestDetail> getAllByContact(String contact);

    CustomerInfo getCustomerInfo(TrueCallerProfileDetail profile);

    CustomerInfo saveCustomerInfo(CustomerInfo profile);

    CustomerAddressInfo saveCustomerAddress(CustomerAddressInfo address);
}
