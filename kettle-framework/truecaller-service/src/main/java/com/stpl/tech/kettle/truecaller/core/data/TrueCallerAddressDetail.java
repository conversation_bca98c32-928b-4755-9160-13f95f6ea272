package com.stpl.tech.kettle.truecaller.core.data;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 06-03-2018.
 */
@Entity
@Table(name = "TRUE_CALLER_ADDRESS_DETAIL")
public class TrueCallerAddressDetail {

    private Integer addressId;
    private String countryCode;
    private String city;
    private String street;
    private String zipcode;
    private TrueCallerProfileDetail profileData;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ADDRESS_ID", unique = true, nullable = false)
    public Integer getAddressId() {
        return addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    @Column(name = "COUNTRY_CODE")
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    @Column(name = "CITY")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name = "STREET")
    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    @Column(name = "ZIPCODE")
    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PROFILE_ID", nullable = false)
    public TrueCallerProfileDetail getProfileData() {
        return profileData;
    }

    public void setProfileData(TrueCallerProfileDetail profileData) {
        this.profileData = profileData;
    }
}
