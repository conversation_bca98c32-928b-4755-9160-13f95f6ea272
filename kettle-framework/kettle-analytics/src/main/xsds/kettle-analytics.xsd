<?xml version="1.0"?>
<!-- ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL ~ __________________ 
	~ ~ [2015] - [2017] Sunshine Teahouse Private Limited ~ All Rights Reserved. 
	~ ~ NOTICE: All information contained herein is, and remains ~ the property 
	of Sunshine Teahouse Private Limited and its suppliers, ~ if any. The intellectual 
	and technical concepts contained ~ herein are proprietary to Sunshine Teahouse 
	Private Limited ~ and its suppliers, and are protected by trade secret or 
	copyright law. ~ Dissemination of this information or reproduction of this 
	material ~ is strictly forbidden unless prior written permission is obtained 
	~ from Sunshine Teahouse Private Limited. -->

<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	targetNamespace="http://www.w3schools.com" xmlns="http://www.w3schools.com"
	elementFormDefault="qualified">
	<xs:element name="CategoryReportData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="name" type="xs:string" />
				<xs:element name="businessDate" type="xs:date" />
				<xs:element name="data" type="AggregateSaleData" />
				<xs:element name="drillDown" type="UnitReportData"
					minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="TargetReportData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="year" type="xs:int" />
				<xs:element name="month" type="xs:string" />
				<xs:element name="weekDay" type="UnitReportData"
					minOccurs="0" maxOccurs="unbounded" />
				<xs:element name="weekEnd" type="UnitReportData"
					minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="ProductCategoryReportData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="name" type="xs:string" />
				<xs:element name="businessDate" type="xs:date" />
				<xs:element name="data" type="AggregateQuantityData" />
				<xs:element name="drillDown" type="ProductSubCategoryReportData"
					minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="ProductSubCategoryReportData">
		<xs:sequence>
			<xs:element name="name" type="xs:string" />
			<xs:element name="businessDate" type="xs:date" />
			<xs:element name="data" type="AggregateQuantityData" />
			<xs:element name="drillDown" type="ProductReportData"
				minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:element name="SubCategoryReportData">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="name" type="xs:string" />
				<xs:element name="businessDate" type="xs:date" />
				<xs:element name="data" type="AggregateSaleData" />
				<xs:element name="drillDown" type="UnitReportData"
					minOccurs="0" maxOccurs="unbounded" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="UnitReportData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="businessDate" type="xs:date" />
			<xs:element name="data" type="AggregateSaleData" />
			<xs:element name="drillDown" type="ProductReportData" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AggregateSaleData">
		<xs:sequence>
			<xs:element name="netSale" type="DecimalData" />
			<xs:element name="deliveryNetSale" type="DecimalData" />
			<xs:element name="gmv" type="DecimalData" />
			<xs:element name="deliveryGmv" type="DecimalData" />
			<xs:element name="apc" type="DecimalData" />
			<xs:element name="foodCapture" type="DecimalData" />
			<xs:element name="customerCapture" type="DecimalData" />
			<xs:element name="deliveryApc" type="DecimalData" />
			<xs:element name="tickets" type="NumericData" />
			<xs:element name="deliveryTickets" type="NumericData" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ProductReportData">
		<xs:sequence>
			<xs:element name="id" type="xs:int" />
			<xs:element name="dimension" type="xs:string" />
			<xs:element name="businessDate" type="xs:date" />
			<xs:element name="data" type="AggregateQuantityData" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="AggregateQuantityData">
		<xs:sequence>
			<xs:element name="total" type="NumericData" />
			<xs:element name="accountableComplimentary" type="NumericData" />
			<xs:element name="nonAccountableComplimentary" type="NumericData" />
			<xs:element name="deliveryTotal" type="NumericData" />
			<xs:element name="deliveryAccountableComplimentary" type="NumericData" />
			<xs:element name="deliveryNonAccountableComplimentary"
				type="NumericData" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="NumericData">
		<xs:sequence>
			<xs:element name="min" type="xs:int" />
			<xs:element name="avg" type="xs:int" />
			<xs:element name="max" type="xs:int" />
			<xs:element name="current" type="xs:int" />
			<xs:element name="minDate" type="xs:date" />
			<xs:element name="maxDate" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DecimalData">
		<xs:sequence>
			<xs:element name="min" type="xs:decimal" />
			<xs:element name="avg" type="xs:decimal" />
			<xs:element name="max" type="xs:decimal" />
			<xs:element name="current" type="xs:decimal" />
			<xs:element name="minDate" type="xs:date" />
			<xs:element name="maxDate" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AssemblyEventLog">
		<xs:sequence>
			<xs:element name="eventType" type="xs:string" />
			<xs:element name="unitId" type="xs:int" />
			<xs:element name="wsHotState" type="xs:string" />
			<xs:element name="wsColdState" type="xs:string" />
			<xs:element name="wsFoodState" type="xs:string" />
			<xs:element name="comment" type="xs:string" />
			<xs:element name="eventTimeStamp" type="xs:date" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MonkLogData">
		<xs:sequence>
			<xs:element name="chaiMonkName" type="xs:string" />
			<xs:element name="monkEvent" type="xs:string" />
			<xs:element name="timeStamp" type="xs:date" />
			<xs:element name="milkQuantity" type="xs:decimal" />
			<xs:element name="payload" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
</xs:schema>
