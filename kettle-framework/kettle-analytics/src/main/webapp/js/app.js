/**
 * http://usejsdoc.org/
 */
var analyticapp = angular.module('analyticapp', ['ui.router', 'ui.materialize', 'ngCookies', 'ui.select2']).config(function ($stateProvider, $urlRouterProvider) {

    $urlRouterProvider.otherwise("/login");

    $stateProvider.state('login', {
        url: "/login",
        templateUrl: 'views/login.html',
        params: {accessDenied: false},
        controller: 'LoginController'
    }).state('menu', {
        url: '/menu',
        templateUrl: 'views/menu.html',
        controller: 'MainController'
    }).state('menu.dashboard', {
        url: "/dashboard",
        templateUrl: "views/dashboard.html",
        controller: 'DashboardController'

    }).state('menu.aggregated', {
        url: "/aggregated",
        templateUrl: "views/aggregated.html",
        controller: 'AggregatedDashboardController'
    }).state('menu.product', {
        url: "/product",
        templateUrl: "views/product.html",
        controller: 'ProductDashboardController'
    }).state('menu.apcLever', {
        url: "/apcLever",
        templateUrl: "views/apcLever.html",
        controller: 'ApcLeverController'
    })
}).service('AuthService', function () {
    var service = this;
    service.authorization = null;
    service.getAuthorization = getAuthorization;
    service.setAuthorization = function (authorization) {
        service.authorization = authorization;
    };

    function getAuthorization() {
        return service.authorization;
    }

    return service;
}).factory('httpAuthInterceptor', function ($q, $rootScope, AuthService, $location) {
    return {
        request: function (config) {
            $rootScope.showSpinner = true;
            config.headers.auth = AuthService.getAuthorization();
            if(config.method=="POST" && config.data == undefined){
                config["data"] = {};
            }
            return config;
        },
        requestError: function (rejection) {
            $rootScope.showSpinner = false;
            return $q.reject(rejection);
        },
        response: function (response) {
            $rootScope.showSpinner = false;
            return response || $q.when(response);
        },

        responseError: function (response) {
            $rootScope.showSpinner = false;
            if (response.status === 401) {
                $location.path('/login');
            }
            return $q.reject(response);
        }
    };
}).config(['$httpProvider', function ($httpProvider) {
    $httpProvider.interceptors.push('httpAuthInterceptor');
}]);


analyticapp.filter('getPercentageReport', function () {
    return function(num1, num2, num3) {
        if(num3){
            if(isNaN(num1) || isNaN(num2) || num2==0) {
                return "0 %";
            } else {
                return parseFloat((parseInt(num1)/parseInt(num2))*100).toFixed(0)+" %";
            }
        } else {
            return "NA";
        }
    }
});

analyticapp.filter('getPercentageReport', function () {
    return function(num1, num2, num3) {
        if(num3){
            if(isNaN(num1) || isNaN(num2) || num2==0) {
                return "0 %";
            } else {
                return parseFloat((parseInt(num1)/parseInt(num2))*100).toFixed(0)+" %";
            }
        } else {
            return "NA";
        }
    }
});

analyticapp.filter('getPercentageValue', function () {
    return function(num1, num2, num3) {
        if(num3){
            if(isNaN(num1) || isNaN(num2) || num2===0) {
                return "0 %";
            } else {
                return Math.round(num1) + " %";
            }
        } else {
            return "NA";
        }
    }
});