/**
 * http://usejsdoc.org/
 */
analyticapp
		.controller(
				"ProductDashboardController",
				function($rootScope, $scope, $http, $location, APIJson,AuthService ) {
					$scope.init = function() {
						$scope.envType = null;
						$scope.pusher = null;
						$scope.channel = null;
						$scope.productReportData = null;
						//subscribeChannel();
						fetchAllSalesData();
					}
					function createBinding(productData) {
						// var channel = $scope.pusher.subscribe('sales_data');
						$scope.channel
								.bind(
									productData.id,
										function(data) {
											//console.log(data);
											$scope
													.$apply(function() {
														for ( var i in $scope.productReportData) {
															if ($scope.productReportData[i].id == data.productData.id) {
																$scope.productReportData[i] = data.productData;
																break;
															}
														}
													});
										});

					}

					function fetchAllSalesData() {
						$http({
							method : 'GET',
							url : APIJson.urls.analyticsData.allProductSales
						}).then(function success(response) {
							$scope.productReportData = response.data;
							$scope.productReportData.forEach(function(productData) {
								createBinding(productData);
							});
						}, function error(response) {
							console.log("error:" + response);
						});
					}

					function subscribeChannel() {
						$scope.envType = "dev";
						if ($location.host().indexOf("orient.chaayos.com") != -1 || $location.host().indexOf("chaudhary.chaayos.com") != -1) {
							$scope.envType = "prod";
						}
						console.log("initializing...");
						Pusher.logToConsole = true;
						$scope.pusher = new Pusher('668c61c6259750d8ab74', {
							cluster : 'eu',
							encrypted : true
						});

						$scope.channel = $scope.pusher.subscribe($scope.envType
								+ '_product_sales_data');

					}
				});
