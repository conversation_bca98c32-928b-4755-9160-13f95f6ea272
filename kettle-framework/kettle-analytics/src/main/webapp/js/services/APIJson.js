/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by mohit on 12-05-2016.
 */
(function () {
    'use strict';

    analyticapp.factory('APIJson', APIJson);

    function APIJson() {

        var baseUrl = window.location.protocol+"//"+window.location.host;
        var masterUrl = window.location.protocol+"//"+window.location.host;


        var SEPARATOR = "/";

        var MASTER_SERVICE = masterUrl + SEPARATOR + "master-service/rest/v1" + SEPARATOR;
        var ANALYTICS_SERVICE = baseUrl + SEPARATOR + "kettle-analytics/rest/v1" + SEPARATOR;

        var ANALYTICS_SERVICES_ROOT_CONTEXT = "analytics" + SEPARATOR;
        var USER_SERVICES_ROOT_CONTEXT = MASTER_SERVICE + "users";
        var UNIT_METADATA_ROOT_CONTEXT = MASTER_SERVICE + "unit-metadata";

        var service = {};

        service.urls = {
            analyticsData: {
                unitSales: ANALYTICS_SERVICE + ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/sales",
                unitSalesNew: ANALYTICS_SERVICE + ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/sales/new",
                targetAllData: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/target-all-data",
                currentAllData: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/current-all-data",
                allUnitsSales: ANALYTICS_SERVICE + ANALYTICS_SERVICES_ROOT_CONTEXT + "units/sales",
                allAggregatedSales: ANALYTICS_SERVICE + ANALYTICS_SERVICES_ROOT_CONTEXT + "aggregated/sales",
                allProductSales: ANALYTICS_SERVICE + ANALYTICS_SERVICES_ROOT_CONTEXT + "products/sales",
                unitPenetrationTarget: ANALYTICS_SERVICE + ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/target",
                unitPenetrationCurrent: ANALYTICS_SERVICE + ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/current",
                unitPenetrationLastMonth: ANALYTICS_SERVICE + ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/previous-month"
            },
            users: {
                login: USER_SERVICES_ROOT_CONTEXT + "/login",
                logout: USER_SERVICES_ROOT_CONTEXT + "/logout"
            },
            unitMetaData: {
                activeUnits: UNIT_METADATA_ROOT_CONTEXT + "/all-active-units"
            }
        };

        return service;
    }

})();
