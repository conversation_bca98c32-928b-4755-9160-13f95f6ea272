<div id="RokdaModal" data-ng-init="init()">
    <div class="row">
        <div class="col s12 m12 l12 purple lighten-5 navigation-header">
            <p>APC Levers</p>
        </div>
    </div>

    <div class="row">
        <div class="col s6">
            <label for="inputCreated">Select unit</label>
            <select class="form-control" data-ng-options="unit as unit.name for unit in unitList track by unit.id" data-ng-model="selectedUnit" data-ng-change="getApcLeverData()"></select>
            <!--<input type="button" class="btn" style="margin-top: 10px;" value="Submit" data-ng-click="">-->
        </div>
        <div class="col s6">
            <label for="inputCreated">Select date</label>
            <input input-date type="text" name="created" id="inputCreated" ng-model="selectedDate" data-ng-change="reset()"
                    container="" format="yyyy-mm-dd" select-years="1" max="{{maxDate}}" />
            <input type="button" class="btn" value="Add date" data-ng-if="dates.length<maxDatesLength" data-ng-click="getApcLeverData(selectedDate)" />
        </div>
    </div>
    <div class="row">
        <div class="col s12">
            <span style="display: inline-block; padding: 5px; border:#ccc 1px solid; border-radius: 3px; margin:5px; cursor:pointer;"
                  data-ng-repeat="date in dates track by $index" data-ng-click="removeDate($index)">{{date.date}} &times;</span>
        </div>
    </div>
    <div class="row" style="margin-bottom: 0px;">
        <div class="col s12">
            <div>
                <table class="table bordered striped">
                    <tr>
                        <th class="yellow">Category</th>
                        <th class="brown">Target</th>
                        <th class="teal">Achievement M-1</th>
                        <th class="orange">Achievement MTD</th>
                        <td data-ng-repeat="date in dates">MTD - {{date.date}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable">New Product Penetration</td>
                        <td>{{unitsPenetrationTarget.seasonal | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.seasonal | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.seasonal | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.seasonal | getPercentageReport : date.unitsPenetrationCurrent.dineIn : date.unitsPenetrationCurrent}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable">Cakes Penetration</td>
                        <td>{{unitsPenetrationTarget.cakes | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.cakes | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.cakes | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.cakes | getPercentageReport : date.unitsPenetrationCurrent.dineIn : date.unitsPenetrationCurrent}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable">Cold Penetration</td>
                        <td>{{unitsPenetrationTarget.cold | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.cold | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.cold | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.cold | getPercentageReport : date.unitsPenetrationCurrent.dineIn : date.unitsPenetrationCurrent}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable">Regular to Full Ratio</td>
                        <td>{{unitsPenetrationTarget.full | getPercentageReport : (unitsPenetrationTarget.full+unitsPenetrationTarget.regular) : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.full | getPercentageReport : (unitsPenetrationLM.full+unitsPenetrationLM.regular) : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.full | getPercentageReport : (unitsPenetrationCurrent.full+unitsPenetrationCurrent.regular) : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.full | getPercentageReport : (date.unitsPenetrationCurrent.full+date.unitsPenetrationCurrent.regular) : date.unitsPenetrationCurrent}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable">Meals Penetration</td>
                        <td>{{unitsPenetrationTarget.meals | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.meals | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.meals | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.meals | getPercentageReport : date.unitsPenetrationCurrent.dineIn : date.unitsPenetrationCurrent}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable">Merchandise Penetration</td>
                        <td>{{unitsPenetrationTarget.merchandise | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.merchandise | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.merchandise | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.merchandise | getPercentageReport : date.unitsPenetrationCurrent.dineIn : date.unitsPenetrationCurrent}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable">Beverage Only Tickets</td>
                        <td>{{unitsPenetrationTarget.beverage | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.beverage | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.beverage | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.beverage | getPercentageReport : date.unitsPenetrationCurrent.dineIn : date.unitsPenetrationCurrent}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable" style="max-width: 170px;">New Customers with No Desi Chai</td>
                        <td>{{unitsPenetrationTarget.newCustWithNoPriPrd | getPercentageReport : unitsPenetrationTarget.newCustomer : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.newCustWithNoPriPrd | getPercentageReport : unitsPenetrationLM.newCustomer : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.newCustWithNoPriPrd | getPercentageReport : unitsPenetrationCurrent.newCustomer : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.newCustWithNoPriPrd | getPercentageReport : date.unitsPenetrationCurrent.newCustomer : date.unitsPenetrationCurrent}}</td>
                    </tr>
                    <tr>
                        <td class="firstColumnTable">Gift cards Penetration</td>
                        <td>{{unitsPenetrationTarget.giftCards | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                        <td>{{unitsPenetrationLM.giftCards | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        <td>{{unitsPenetrationCurrent.giftCards | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                        <td data-ng-repeat="date in dates">{{date.unitsPenetrationCurrent.giftCards | getPercentageReport : date.unitsPenetrationCurrent.dineIn : date.unitsPenetrationCurrent}}</td>
                    </tr>
                </table>
            </div>
            <!--<div style="margin-top: 10px;">
                <table class="table bordered striped">
                    <tr>
                        <th class="yellow">Category</th>
                        <th class="orange">Today</th>
                        <th class="teal">LWSD</th>
                    </tr>
                    <tr data-ng-repeat=" item in unitsReportData.data">
                        <td class="firstColumnTable">{{item.name}}</td>
                        <td>{{item.current | number : 0}}</td>
                        <td>{{item.lw | number : 0}}</td>
                    </tr>
                </table>
            </div>-->
        </div>
    </div>
</div>