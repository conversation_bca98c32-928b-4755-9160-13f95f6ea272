<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<nav style="position: fixed; top:0;z-index: 9;" ng-init="init()">
    <div class="row" style="margin-bottom:0px;font-size:16px;">
        <div class="col s8 m10 l10">
            <a data-activates="nav-mobile" class="button-collapse top-nav full hide-on-large-only">
                <i class="material-icons">reorder</i>
            </a> &nbsp;
            <!-- <img src="img/logo.png" style="height: 50px; float: left;" />
             -->
             <span class="page-title white-text" style="font-size: x-large; font-family: monospace,fantasy;">Chaudhary</span>
        </div>
        <div class="col s4 m2 l2">
            <a data-ng-click="logout()" class="white-text btn">
                <i class="material-icons" style="line-height: inherit;">power_settings_new</i>
            </a>
        </div>
    </div>
</nav>

<div class="row" data-ng-init="init()">
    <div class="col s12 m4 l3">
        <div id="nav-mobile" class="side-nav fixed" style="top:65px;">
            <ul class="collapsible" id="menu" data-collapsible="accordion" style="transform: translateX(0%);">
                <li>
                    <div class="collapsible-header waves-effect waves-teal">Sales</div>
                    <div class="collapsible-body">
                        <ul class="sub-menu">
                            <li class="bold" data-ng-click="activeMenu = 'aggregated'" data-ng-class="{active:activeMenu == 'aggregated'}">
                                <a ui-sref=".aggregated" class="waves-effect waves-teal">Aggregated Sales</a>
                            </li>
                            <li class="bold" data-ng-click="activeMenu = 'dashboard'" data-ng-class="{active:activeMenu == 'dashboard'}">
                                <a ui-sref=".dashboard" class="waves-effect waves-teal">Unit Wise Sales</a>
                            </li>
                            <li class="bold" data-ng-click="activeMenu = 'product'" data-ng-class="{active:activeMenu == 'product'}">
                                <a ui-sref=".product" class="waves-effect waves-teal">Product Wise Sales</a>
                            </li>
                            <li class="bold" data-ng-click="activeMenu = 'apcLevers'" data-ng-class="{active:activeMenu == 'apcLever'}">
                                <a ui-sref=".apcLever" class="waves-effect waves-teal">APC Levers</a>
                            </li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    </div>
    <div id="content" class="col s12 m12 l12" ui-view autoscroll="true"></div>

