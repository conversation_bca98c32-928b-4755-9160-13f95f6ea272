<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHAAYOS - DASHBOARD</title>
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <!-- CSS -->
    <link rel="stylesheet" href="libs/materialize/dist/css/materialize.min.css">
    <!-- custom styles -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="libs/select2/select2.min.css">
    <link rel="stylesheet" href="css/base.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dnd.css">

    <!-- custom method to disable back button -->
    <script type = "text/javascript" >
        history.pushState(null, null, '');
        window.addEventListener('popstate', function(event) {
            history.pushState(null, null, '');
        });
    </script>
    <!-- custom method to disable back button -->
</head>
<body data-ng-app="analyticapp">
    <!-- Spinner code starts here-->
    <div class="overlay" data-ng-show="showSpinner">
        <div id="scm-spinner-wrapper">
            <div id="scm-spinner" class="preloader-wrapper big active">
            <div class="spinner-layer spinner-blue-only">
                <div class="circle-clipper left">
                    <div class="circle"></div>
                </div>
                <div class="gap-patch">
                    <div class="circle"></div>
                </div>
                <div class="circle-clipper right">
                    <div class="circle"></div>
                </div>
            </div>
        </div>
         </div>
    </div>
    <!-- Spinner code ends here-->
    <div class="overlay" data-ng-show="showOverlay"></div>


    <div class="row" ui-view></div>

    <!-- JS -->
    <!--<script src="libs/jquery/dist/jquery.min.js"></script>-->
    <script type="text/javascript" src="libs/jquery/dist/jquery.min.js"></script>
    <script src="libs/materialize/dist/js/materialize.min.js"></script>
    <script src="libs/angular/angular.min.js"></script>
    <script src="libs/angular/angular-cookies.min.js"></script>
    <script src="libs/angular-ui-router/release/angular-ui-router.min.js"></script>
    <script src="libs/angular-materialize/angular-materialize.js"></script>
    <script src="libs/select2/select2.min.js"></script>
    <script src="libs/select2/ui-select2.js"></script>
     <script src="https://js.pusher.com/3.1/pusher.min.js"></script>

    <!-- ANGULAR CUSTOM SERVICES-->
    <script src="js/app.js"></script>
    <script src="js/services/APIJson.js"></script>

    <!-- ANGULAR CUSTOM CONTROLLERS-->
    <script src="js/controllers/LoginController.js"></script>
    <script src="js/controllers/MainController.js"></script>
    <script src="js/controllers/DashboardController.js"></script>
    <script src="js/controllers/AggregatedDashboardController.js"></script>
    <script src="js/controllers/ProductDashboardController.js"></script>
    <script src="js/controllers/ApcLeverController.js"></script>
</body>
</html>
