<?xml version="1.0" encoding="UTF-8"?>

<!-- For assistance related to logback-translator or configuration  -->
<!-- files in general, please contact the logback user mailing list -->
<!-- at http://www.qos.ch/mailman/listinfo/logback-user             -->
<!--                                                                -->
<!-- For professional support please see                            -->
<!--    http://www.qos.ch/shop/products/professionalSupport         -->
<!--                                                                -->
<configuration>
    <appender name="ANALYTICS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- Tests run on modern PCs show that buffering related property -->
        <!-- "ImmediateFlush" has negligible impact and will be ignored.  -->
        <!--See http://logback.qos.ch/manual/appenders.html#RollingFileAppender-->
        <!--and http://logback.qos.ch/manual/appenders.html#TimeBasedRollingPolicy-->
        <!--for further documentation-->
        <Append>true</Append>
        <File>./logs/kettle-analytics.log</File>
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %X{request.id} %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./logs/kettle-analytics.%d{yyyy-MM-dd}.log</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{"yyyy-MM-dd:HH:mm:ss.SSS",IST} %t %-5p %X{request.id} %10c: %m%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ANALYTICS"/>
    </root>
    <logger name="org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver" level="OFF"/>
</configuration>