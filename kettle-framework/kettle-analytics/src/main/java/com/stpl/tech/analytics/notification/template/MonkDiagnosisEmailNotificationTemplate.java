/*
 * Created By Shanmukh
 */

package com.stpl.tech.analytics.notification.template;

import com.stpl.tech.loggingservice.model.chaimonk.log.MonkDiagnosisLogData;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

public class MonkDiagnosisEmailNotificationTemplate extends AbstractVelocityTemplate {

    private MonkDiagnosisLogData monkDiagnosisLogData;

    private String basePath;

    public MonkDiagnosisEmailNotificationTemplate(MonkDiagnosisLogData monkDiagnosisLogData, String basePath) {
        this.monkDiagnosisLogData = monkDiagnosisLogData;
        this.basePath = basePath;
    }

    public MonkDiagnosisLogData getMonkDiagnosisLogData() {
        return monkDiagnosisLogData;
    }

    @Override
    public String getTemplatePath() {
        return "templates/MonkDiagnosisEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/monkDiagnosis/" + monkDiagnosisLogData.getMonkDiagnosisEventId() +"/"+ AppUtils.getDateString(AppUtils.getCurrentDateIST())  + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("logData", monkDiagnosisLogData);
        return stringObjectMap;
    }
}
