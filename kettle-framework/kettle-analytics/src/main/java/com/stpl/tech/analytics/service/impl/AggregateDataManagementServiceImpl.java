/**
 *
 */
package com.stpl.tech.analytics.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.stpl.tech.analytics.dao.UnitReportDataObjectDao;
import com.stpl.tech.analytics.model.UnitReportData;
import com.stpl.tech.analytics.model.UnitReportDataObject;
import com.stpl.tech.analytics.service.AggregateDataManagementService;
import com.stpl.tech.analytics.service.IdGeneratorService;
import com.stpl.tech.util.AppUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class AggregateDataManagementServiceImpl implements AggregateDataManagementService {

	@Autowired
	private UnitReportDataObjectDao dao;

	@Autowired
	@Qualifier("analyticsIdGeneratorService")
	private IdGeneratorService idService;

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.analytics.service.UnitReportDataManagementService#save(com.
	 * stpl.tech.analytics.model.UnitReportDataObject)
	 */
	@Override
	public void save(UnitReportDataObject data) {
		data.setId(idService.getNextId(UnitReportDataObject.class));
		dao.save(data);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.analytics.service.AggregateDataManagementService#
	 * findLastWeekCurrenTimeSale(int)
	 */
	@Override
	public UnitReportData findLastWeekCurrentTimeSale(int unitId) {
		List<UnitReportDataObject> list = dao.findLatestRecord(unitId,
				AppUtils.getOldDate(AppUtils.getCurrentBusinessDate(), 7),AppUtils.getOldDate( AppUtils.getCurrentTimestamp(), 7),
				Sort.by(Sort.Direction.DESC, "time"));
		if (list == null || list.size() == 0) {
			return null;

		} else {
			return list.get(0).getInstance();
		}
	}

}
