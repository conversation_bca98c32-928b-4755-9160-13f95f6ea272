/*
 * Created By <PERSON><PERSON><PERSON>
 */

package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.kettle.data.model.AssemblyNotificationUnitWiseData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface AssemblyNotificationUnitWiseDataDao extends JpaRepository<AssemblyNotificationUnitWiseData, Integer> {

    List<AssemblyNotificationUnitWiseData> findByUnitIdInAndNotificationTypeIn(Collection<Integer> unitIds, Collection<String> notificationTypes);

}
