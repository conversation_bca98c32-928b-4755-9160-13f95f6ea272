package com.stpl.tech.analytics.service.impl;

import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

@Service
public class AnalyticsProperties {

	@Autowired
	private Environment env;

	public EnvType getEnvironmentType() {
		return EnvType.valueOf(env.getProperty("environment.type"));
	}

	public String getBasePath() {
		return env.getProperty("server.base.dir");
	}

	public boolean publishToPusher() {
		return Boolean.valueOf(env.getProperty("publish.to.pusher"));
	}

	public String getArduinoBuildBucket() {
		return env.getProperty("amazon.arduino.s3.bucket","DEV_ARDUINO_BUILDS");
	}

	/**
	 * @return
	 */
	public Set<Integer> getTrackedProducts() {
		Set<Integer>  items = new HashSet<>();
		String productList = env.getProperty("tracked.product.ids");
		if(productList != null && productList.length()   > 0){
			String [] products = productList.split(",");
			for(String product : products){
				items.add(Integer.valueOf(product.trim()));
			}
		}
		return items;
	}
	
	
	/**
	 * @return
	 */
	public BigDecimal getPercentageThreasholdForReporting() {
		return new BigDecimal(env.getProperty("percentage.value.for.threshold"));
	}

	public String getGiftCardUnitsFetchURL(){
		return env.getProperty("gift.card.offer.units");
	}

	public int getSubscriptionProductSubType() {
		return Integer.valueOf(env.getProperty("subscription.product.type", "3810"));
	}

	public int getChaayosOrderDelayThrshld() {
		return Integer.valueOf(env.getProperty("chaayos.order.delay.threshold", "10"));
	}

	public int getGNTOrderDelayThrshld() {
		return Integer.valueOf(env.getProperty("gnt.order.delay.threshold", "15"));
	}

	public int getChaayosTATThrshld() {
		return Integer.valueOf(env.getProperty("chaayos.tat.threshold", "10"));
	}

	public int getGNTTATThrshld() {
		return Integer.valueOf(env.getProperty("gnt.tat.threshold", "15"));
	}

	public Boolean isAssemblyFirestoreEnabledForAll() {
		return Boolean.valueOf(env.getProperty("assembly.firestore.enabled.all", "false"));
	}
	public String getAssemblyFirestoreUnits() {
		return env.getProperty("units.for.order.delivery.through.fire.store", "");
	}

}
	
