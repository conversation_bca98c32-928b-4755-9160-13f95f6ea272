package com.stpl.tech.analytics.service.impl;

import com.amazonaws.regions.Regions;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.stpl.tech.util.domain.adapter.FlexibleDateDeserializer;
import com.stpl.tech.analytics.data.dao.AssemblyNotificationDao;
import com.stpl.tech.analytics.data.dao.AssemblyNotificationUnitWiseDataDao;
import com.stpl.tech.analytics.data.dao.MonkDiagnosisEventDao;
import com.stpl.tech.analytics.data.dao.MonkDiagnosisTroubleShootDao;
import com.stpl.tech.analytics.data.dao.MonkManualTaskAddonDao;
import com.stpl.tech.analytics.data.dao.MonkOrderDetailDao;
import com.stpl.tech.analytics.data.dao.MonkRecipeAddonsDataDao;
import com.stpl.tech.analytics.data.dao.MonkRemakeErrorDao;
import com.stpl.tech.analytics.data.dao.MonkTaskCompletionStatsDao;
import com.stpl.tech.analytics.data.dao.MonkXTwoLogDataDao;
import com.stpl.tech.analytics.data.dao.OrderDetailDao;
import com.stpl.tech.analytics.data.dao.WeightCalibrationDetailDao;
import com.stpl.tech.analytics.data.dao.WorkStationLogDao;
import com.stpl.tech.analytics.mapper.MonkManualTaskMapper;
import com.stpl.tech.analytics.mapper.MonkTaskCompletionStatsMapper;
import com.stpl.tech.analytics.model.MonkDashboardData;
import com.stpl.tech.analytics.model.MonkPayloadDashboardData;
import com.stpl.tech.analytics.model.WeightCalibrationDetail;
import com.stpl.tech.analytics.model.chaimonk.log.MonkFileUploadType;
import com.stpl.tech.analytics.notification.MonkDiagnosisEmailNotification;
import com.stpl.tech.analytics.notification.MonkWtCalibrationEmailNotification;
import com.stpl.tech.analytics.notification.template.MonkDiagnosisEmailNotificationTemplate;
import com.stpl.tech.analytics.notification.template.MonkWtCalibrationEmailNotificationTemplate;
import com.stpl.tech.analytics.service.LogUploadService;
import com.stpl.tech.analytics.service.LoggingService;
import com.stpl.tech.kettle.data.model.AssemblyNotificationData;
import com.stpl.tech.kettle.data.model.AssemblyNotificationUnitWiseData;
import com.stpl.tech.kettle.data.model.MonkDiagnosisEvent;
import com.stpl.tech.kettle.data.model.MonkDiagnosisTroubleShoot;
import com.stpl.tech.kettle.data.model.MonkLogData;
import com.stpl.tech.kettle.data.model.MonkOrderDetail;
import com.stpl.tech.kettle.data.model.MonkRecipeAddonsData;
import com.stpl.tech.kettle.data.model.MonkRemakeError;
import com.stpl.tech.kettle.data.model.MonkTaskCompletionStats;
import com.stpl.tech.kettle.data.model.MonkXTwoLogData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.data.model.OrderItemAddon;
import com.stpl.tech.kettle.data.model.SCodeReasons;
import com.stpl.tech.kettle.data.model.WeightCalibrationDetailData;
import com.stpl.tech.kettle.data.model.WorkstationLog;
import com.stpl.tech.kettle.domain.model.ChaiMonkXTwoRecipe;
import com.stpl.tech.kettle.domain.model.ChaiMonkXTwoRecipeStep;
import com.stpl.tech.kettle.domain.model.MonkRecipeAddonsDataDto;
import com.stpl.tech.kettle.domain.model.MonkTaskCompletionStatsDto;
import com.stpl.tech.kettle.domain.model.WorkTask;
import com.stpl.tech.kettle.domain.model.XTwoCommandType;
import com.stpl.tech.kettle.domain.model.XTwoDispenseTypeEnum;
import com.stpl.tech.kettle.domain.model.XTwoLiquidIndexEnum;
import com.stpl.tech.kettle.domain.model.XTwoVesselTypeEnum;
import com.stpl.tech.kettle.service.notification.AndroidPushNotification;
import com.stpl.tech.util.domain.GenericLogData;
import com.stpl.tech.master.core.external.notification.FireStoreNotificationType;
import com.stpl.tech.loggingservice.dao.chaimonk.log.AssemblyLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkAssemblyTaskLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkConsumptionLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkDiagnosisLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkRemakeDataDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkStatusLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkTaskStatusDetailDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkTaskStatusDetailNewDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkTaskStatusLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.MonkXTwoLogDao;
import com.stpl.tech.loggingservice.dao.chaimonk.log.WsCrashLogDao;
import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyEventLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyOrderLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkConsumptionLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkDiagnosisLogData;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkNotAllocatedReason;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkPreAllocationLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLogPayloadDetails;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusDetailLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusDetailLogNew;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkXTwoLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.NotificationData;
import com.stpl.tech.loggingservice.model.chaimonk.log.RemakeData;
import com.stpl.tech.loggingservice.model.chaimonk.log.TroubleShoot;
import com.stpl.tech.loggingservice.model.chaimonk.log.WsCrashLog;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.service.FirebaseNotificationService;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.WsType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.BasicQuery;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.stpl.tech.kettle.data.model.MonkManualTask;
import com.stpl.tech.analytics.data.dao.MonkManualTaskDao;
import com.google.gson.Gson;
import com.stpl.tech.analytics.dto.MonkManualTaskDTO;

@Service
public class LoggingServiceImpl implements LoggingService {

    private static final Logger LOG = LoggerFactory.getLogger(LoggingServiceImpl.class);

    private static final int NONE_QUANTITY = 100;
    private static final int REGUALR_QUANTITY = 200;
    private static final int FULL_QUANTITY = 300;
    private static final int CHOTI_KETLI_QUANTITY = 400;
    private static final int BADI_KETLI_QUANTITY = 1000;
    private static final int MINI_KETLI_QUANTITY = 250;

    protected static final String NONE = "None";
    protected static final String BADI_KETLI = "BadiKetli";
    protected static final String CHOTI_KETLI = "ChotiKetli";
    protected static final String MINI_KETLI = "MiniKetli";
    protected static final String FULL = "Full";
    public static final String REGULAR = "Regular";

    private static final String MONK_TASK_STATUS_DETAIL_LOG = MonkTaskStatusDetailLog.class.getSimpleName();
    private static final Gson gson = new GsonBuilder().registerTypeAdapter(Date.class, new FlexibleDateDeserializer()).create();



    @Autowired
    private AssemblyLogDao assemblyLogDao;

    @Autowired
    private MonkLogDao monkLogDao;

    @Autowired
    private MonkConsumptionLogDao consumptionLogDao;

    @Autowired
    private MonkTaskStatusLogDao taskStatusLogDao;

    @Autowired
    private MonkAssemblyTaskLogDao monkAssemblyTaskLogDao;

    @Autowired
    private MonkTaskStatusDetailDao taskStatusDetailDao;
    
    @Autowired
    private MonkTaskStatusDetailNewDao taskStatusDetailNewDao;

    @Autowired
    private MonkStatusLogDao statusLogDao;

    @Autowired
    private WsCrashLogDao wsCrashLogDao;

    @Autowired
    private WeightCalibrationDetailDao weightCalibrationDetailDao;

    @Autowired
    private AnalyticsProperties props;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private MonkDiagnosisLogDao monkDiagnosisLogDao;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private MonkOrderDetailDao monkOrderDetailDao;

   @Autowired
   WorkStationLogDao workStationLogDao;

   @Autowired
    SQSNotificationService sqsNotificationService;

   @Autowired
    Environment env;

   @Autowired
    MonkRemakeErrorDao monkRemakeErrorDao;

   @Autowired
   MonkTaskCompletionStatsDao monkTaskCompletionStatsDao;

    @Autowired
    MonkXTwoLogDataDao monkXTwoLogDataDao;

   @Autowired
   FirebaseNotificationService firebaseNotificationService;

   @Autowired
   AssemblyNotificationDao assemblyNotificationDao;

   @Autowired
   private RecipeCache recipeCache;

   @Autowired
    MonkDiagnosisEventDao monkDiagnosisEventDao;

   @Autowired
   MonkDiagnosisTroubleShootDao monkDiagnosisTroubleShootDao;

   @Autowired
    AssemblyNotificationUnitWiseDataDao assemblyNotificationUnitWiseDataDao;

   @Autowired
    MonkXTwoLogDao monkXTwoLogDao;

    @Autowired
    private MonkManualTaskDao monkManualTaskDao;

    @Autowired
    private MonkManualTaskAddonDao monkManualTaskAddonDao;

    @Autowired
    private MonkRemakeDataDao monkRemakeDataDao;

    @Autowired
    private MonkRecipeAddonsDataDao monkRecipeAddonsDataDao;

    @Override
    public boolean addAssemblyLog(AssemblyEventLog data) {
        assemblyLogDao.save(data);
        return true;
    }

    @Override
    public boolean addMonkLogData(MonkLogData logData) {
        monkLogDao.save(logData);
        return true;
    }

    @Override
    public boolean addMonkConsumptionLogData(List<MonkConsumptionLog> consumptionLogList) {
        for (MonkConsumptionLog consumptionLog : consumptionLogList) {
            try {
                consumptionLogDao.save(consumptionLog);
            } catch (Exception e) {
                LOG.error("Error while saving consumption log from monk", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean addMonkConsumptionLogData(MonkConsumptionLog consumptionLog) {
            try {
                consumptionLogDao.save(consumptionLog);
            } catch (Exception e) {
                LOG.error("Error while saving consumption log from monk", e);
                return false;
            }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addMonkStatusLogData(List<MonkStatusLog> statusLogList) {
        for (MonkStatusLog log : statusLogList) {
            try {
                statusLogDao.save(log);
                saveRealTimeDashboardDetails(log);
            } catch (Exception e) {
                LOG.error("Error while saving monk log status log from monk", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean addMonkStatusLogData(MonkStatusLog statusLog) {
            try {
                statusLogDao.save(statusLog);
            } catch (Exception e) {
                LOG.error("Error while saving status log from monk", e);
                return false;
            }
        return true;
    }

    @Override
    public boolean addMonkTaskStatusLogData(List<MonkTaskStatusLog> taskStatusLogList) {
        for (MonkTaskStatusLog log : taskStatusLogList) {
            try {
                taskStatusLogDao.save(log);
            } catch (Exception e) {
                LOG.error("Error while saving consumption log from monk", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean addMonkTaskStatusLogData(MonkTaskStatusLog taskStatusLog) {
            try {
                taskStatusLogDao.save(taskStatusLog);
            } catch (Exception e) {
                LOG.error("Error while saving consumption log from monk", e);
                return false;
            }
        return true;
    }


    @Override
    public boolean addMonkTaskStatusDetailLogData(List<MonkTaskStatusDetailLog> detailLogs) {
        for (MonkTaskStatusDetailLog log : detailLogs) {
            try {
                taskStatusDetailDao.save(log);
            } catch (Exception e) {
                LOG.error("Error while saving task detail log from monk", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean addMonkTaskStatusDetailLogData(MonkTaskStatusDetailLog detailLog) {
            try {
                MonkTaskStatusDetailLog  rawLog = AppUtils.clone(detailLog, MonkTaskStatusDetailLog.class);
                rawLog.setSkId("republished");
                rawLog.setClassName(MONK_TASK_STATUS_DETAIL_LOG);
                boolean isWorkStationLogsAvailable = isWorkStationLogs(detailLog);
                if(isWorkStationLogsAvailable) {
                    if ((Objects.nonNull(taskStatusDetailDao.findMonkTaskStatusDetailByItemId(detailLog.getOrderItemId())) &&
                            !taskStatusDetailDao.findMonkTaskStatusDetailByItemId(detailLog.getOrderItemId()).isEmpty())) {
                        return true;
                    }else {
                        taskStatusDetailDao.save(detailLog);
                    }
                }else {
                    if (!(Objects.nonNull(taskStatusDetailDao.findMonkTaskStatusDetailByItemId(detailLog.getOrderItemId())) &&
                            !taskStatusDetailDao.findMonkTaskStatusDetailByItemId(detailLog.getOrderItemId()).isEmpty())) {
                        taskStatusDetailDao.save(detailLog);
                    }
                    if(AppUtils.isProd(props.getEnvironmentType())) {
                        EnvType envType = EnvType.valueOf(env.getProperty("environment.type", "LOCAL"));
                        Regions region = AppUtils.getRegion(envType);
                        sqsNotificationService.publishToSQS(envType.name(), rawLog, "_HOT_STATION_LOGS", region);
                    }
                    }
                    return true;
            } catch (Exception e) {
                LOG.error("Error while saving task detail log from monk", e);
                return false;
            }
    }

    private boolean isWorkStationLogs(MonkTaskStatusDetailLog detailLog){
        List<WorkstationLog> workstationLogs = workStationLogDao.findByItemId(detailLog.getOrderItemId());
        if(!CollectionUtils.isEmpty(workstationLogs)){
            WorkstationLog workstationLog = workstationLogs.get(0);
            return saveAllocationDelayReason(workstationLog,detailLog);
        }
        return false;
    }

    private boolean saveAllocationDelayReason(WorkstationLog log,MonkTaskStatusDetailLog detailLog){
        Map<String,Long> delayReason = new HashMap<>();
        if(detailLog.getMonkPreAllocationLogs().isEmpty()){
            LOG.error("No monkPreAllocationLogs found for itemId :{}",detailLog.getOrderItemId());
            return false;
        }else {
            Long queuingTime = detailLog.getMonkPreAllocationLogs().get(0).getAllocationLogTime() - detailLog.getTaskCreationTime();
            if (queuingTime > 3600000 || queuingTime < 0) {
                log.setValid(AppConstants.NO);
            }
            log.setQuequingTime(queuingTime);
            List<MonkPreAllocationLog> monkPreAllocationLogs = detailLog.getMonkPreAllocationLogs();
            long prevLog = 0;
            long currentLog = 0;
            String prevReason = null;
            String currentReason = null;
            for (MonkPreAllocationLog monkPreAllocationLog : monkPreAllocationLogs) {
                if (monkPreAllocationLog.getAttemptNo() == 0 && monkPreAllocationLog.isAllocated()) {
                    workStationLogDao.save(log);
                    return true;
                }
                if (monkPreAllocationLog.getAttemptNo() == 0) {
                    prevLog = monkPreAllocationLog.getAllocationLogTime();
                    prevReason = monkPreAllocationLog.getNotAllocatedReason();
                } else {
                    currentLog = monkPreAllocationLog.getAllocationLogTime();
                    long timeTaken = currentLog - prevLog;
                    if (timeTaken > 3600000 || timeTaken < 0) {
                        log.setValid(AppConstants.NO);
                    }
                    if (prevReason.equals(MonkNotAllocatedReason.INACTIVE.toString())) {
                        if (monkPreAllocationLog.getActiveMonks() > 0) {
                            prevReason = String.valueOf(MonkNotAllocatedReason.BUSY);
                        } else {
                            prevReason = String.valueOf(MonkNotAllocatedReason.OTHER);
                        }
                    }
                    if (delayReason.containsKey(prevReason)) {
                        Long addTime = delayReason.get(prevReason);
                        Long resultTime = addTime + timeTaken;
                        delayReason.put(prevReason, resultTime);
                    } else {
                        delayReason.put(prevReason, timeTaken);
                    }
                    currentReason = monkPreAllocationLog.getNotAllocatedReason();
                    prevLog = currentLog;
                    prevReason = currentReason;
                }
            }
            if (delayReason.containsKey(MonkNotAllocatedReason.BUSY.name())) {
                queuingTime = queuingTime + delayReason.get(MonkNotAllocatedReason.BUSY.name());
                if (queuingTime > 3600000 || queuingTime < 0) {
                    log.setValid(AppConstants.NO);
                }
                log.setQuequingTime(queuingTime);
            }

            delayReason.keySet().forEach(key -> {
                MonkNotAllocatedReason data = MonkNotAllocatedReason.valueOf(key);
                switch (data) {
                    case PAN_NOT_PLACED:
                        if (delayReason.get(data.name()) > 3600000 || delayReason.get(data.name()) < 0) {
                            log.setValid(AppConstants.NO);
                        }
                        log.setPanPlaced(AppConstants.YES);
                        log.setPanNotPlacedTime(delayReason.get(data.name()));
                        break;
                    case INSUFFICIENT_WATER:
                        if (delayReason.get(data.name()) > 3600000 || delayReason.get(data.name()) < 0) {
                            log.setValid(AppConstants.NO);
                        }
                        log.setSufficientWater(AppConstants.YES);
                        log.setInSufficientWater(delayReason.get(data.name()));
                        break;
                    case INSUFFICIENT_MILK:
                        if (delayReason.get(data.name()) > 3600000 || delayReason.get(data.name()) < 0) {
                            log.setValid(AppConstants.NO);
                        }
                        log.setSufficientMilk(AppConstants.YES);
                        log.setInSufficientMilk(delayReason.get(data.name()));
                        break;
                    case DIRTY_SENSOR:
                        if (delayReason.get(data.name()) > 3600000 || delayReason.get(data.name()) < 0) {
                            log.setValid(AppConstants.NO);
                        }
                        log.setCleanSensor(AppConstants.YES);
                        log.setSenorNotCleanedTime(delayReason.get(data.name()));
                        break;
                    case OTHER:
                        if (delayReason.get(data.name()) > 3600000 || delayReason.get(data.name()) < 0) {
                            log.setValid(AppConstants.NO);
                        }
                        log.setOthers(AppConstants.YES);
                        log.setOthersTimeTaken(delayReason.get(data.name()));
                        break;
                }
            });

            try {
                workStationLogDao.save(log);
                return true;
            } catch (Exception e) {
                LOG.error("Unable to save data for itemId {}", log.getItemId());
            }
        }
      return false;
    }

    @Override
    public boolean addMonkTaskStatusDetailLogNewData(List<MonkTaskStatusDetailLogNew> detailLogs) {
        for (MonkTaskStatusDetailLogNew log : detailLogs) {
            try {
            		taskStatusDetailNewDao.save(log);
            } catch (Exception e) {
                LOG.error("Error while saving task detail log from monk", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean addAssemblyOrderLog(List<AssemblyOrderLog> orderLogList) {
        for (AssemblyOrderLog log : orderLogList) {
            try {
                monkAssemblyTaskLogDao.save(log);
            } catch (Exception e) {
                LOG.error("Error while saving order log from assembly screen", e);
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean uploadMonkBrokerLogs(MultipartFile file, Integer unitId) {
        String baseDir = unitId + File.separator + "brokerLogs";
        FileDetail fileUploaded = fileArchiveService.saveFileToS3("monklogs", baseDir, file.getName(), file, false);
        StringBuffer message = new StringBuffer(" ::::: New Broker Logs uploaded from Unit :::::")
                .append(masterDataCache.getUnitBasicDetail(unitId).getName()).append("\n")
                .append("URL :::::: " + fileUploaded.getUrl());
        SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                "ANALYTICS", SlackNotification.MONK_SYNC_ERRORS, message.toString());
        return fileUploaded != null && fileUploaded.getUrl() != null;
    }

    @Override
    public void uploadLogFile(MultipartFile file, Integer unitId, MonkFileUploadType fileType) {
        try{
            uploadFileToPath(file, unitId, fileType);
            String fileContent = new String(file.getBytes());
            String[] objects = fileContent.split("#END");
            LOG.info("Objects to be uploaded ::::::: {}", objects.length);
            LogUploadService uploadService = getUploader(unitId, objects, fileType);
            taskExecutor.execute(uploadService);
        }catch (Exception e){
            UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
            StringBuilder message = new StringBuilder(":::: MONK FILE UPLOAD FAILURE ALARM NOTIFICATION :::: \n");
            message.append("UNIT ID :: ").append(unitBasicDetail.getId()).append("\n");
            message.append("UNIT NAME :: ").append(unitBasicDetail.getName()).append("\n");
            message.append("ERROR MESSAGE :::::::\n").append(e.getMessage());
            message.append(":::::::::::::::::::::::::::::::::\n");
            sendSlackMessage(message,SlackNotification.MONK_SYNC_ERRORS);
        }
    }


    private LogUploadService getUploader(Integer unitId, String[] objects, MonkFileUploadType fileType){
        UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitId);
        EnvType env = props.getEnvironmentType();
        LogUploadService uploadService = null;
        switch (fileType){
            case CONSUMPTION_LOG:
                uploadService = new LogUploadService<MonkConsumptionLogDao,MonkConsumptionLog>(unit,env,objects,consumptionLogDao,MonkConsumptionLog.class);
                break;
            case STATUS_LOG:
                uploadService = new LogUploadService<MonkStatusLogDao, MonkStatusLog>(unit,env,objects,statusLogDao,MonkStatusLog.class);
                break;
            case TASK_STATUS_DETAIL_LOG:
                uploadService = new LogUploadService<MonkTaskStatusDetailDao, MonkTaskStatusDetailLog>(unit,env,objects,taskStatusDetailDao, MonkTaskStatusDetailLog.class);
                break;
            case TASK_STATUS_DETAIL_LOG_NEW:
                uploadService = new LogUploadService<MonkTaskStatusDetailNewDao, MonkTaskStatusDetailLogNew>(unit,env,objects,taskStatusDetailNewDao, MonkTaskStatusDetailLogNew.class);
                break;
            case TASK_STATUS_LOG:
                uploadService = new LogUploadService<MonkTaskStatusLogDao, MonkTaskStatusLog>(unit,env,objects,taskStatusLogDao,MonkTaskStatusLog.class);
                break;
            case WS_CRASH_LOG:
                uploadService = new LogUploadService<WsCrashLogDao, WsCrashLog>(unit,env,objects,wsCrashLogDao,WsCrashLog.class);
                break;
            default:
                uploadService = new LogUploadService<MonkStatusLogDao, MonkStatusLog>(unit,env,objects,statusLogDao,MonkStatusLog.class);
                break;
        }
        return uploadService;
    }

    private String uploadFileToPath(MultipartFile file, Integer unitId, MonkFileUploadType type) throws IOException {
        byte[] bytes = file.getBytes();
        // Creating the directory to store file
        String rootPath = props.getBasePath();
        String parentDir = "monkLogFiles" + File.separator + unitId + File.separator + type;
        String name = "MONK_LOG_FILE_" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".log";
        return AppUtils.write(bytes, rootPath, parentDir, name, LOG);
    }

    private void sendSlackMessage(StringBuilder message,SlackNotification notification) {
        message.append("--------------------------------------------------------");
        SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "ANALYTICS",
                notification, message.toString());
    }

    @Override
    public List<Object> queryGeneric(String queryString, int limit,int page, Class cls){
        Query query = new BasicQuery(queryString).with(Pageable.ofSize(limit).withPage(page));
        List<Object> result = mongoTemplate.find(query, cls);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addMonkDiagnosisLogData(MonkDiagnosisLogData monkDiagnosisLogData) {
        try {
            addMonkDiagnosisLogToSql(monkDiagnosisLogData);
            monkDiagnosisLogDao.save(monkDiagnosisLogData);
        } catch (Exception e) {
            LOG.error("Error while saving status log from monk", e);
            return false;
        }
        return true;
    }

    private void addMonkDiagnosisLogToSql(MonkDiagnosisLogData monkDiagnosisLogData) {
        try {
            MonkDiagnosisEvent monkDiagnosisEvent = new MonkDiagnosisEvent();
            monkDiagnosisEvent.setUnitId(monkDiagnosisLogData.getUnitId());
            monkDiagnosisEvent.setUserId(monkDiagnosisLogData.getUserId());
            monkDiagnosisEvent.setCreatedAt(monkDiagnosisLogData.getCreatedAt());
            monkDiagnosisEvent.setLoggedAtServer(AppUtils.getCurrentTimestamp());
            monkDiagnosisEvent.setEnteredUserName(monkDiagnosisLogData.getUserName());
            monkDiagnosisEvent.setMonkName(monkDiagnosisLogData.getMonkName());
            monkDiagnosisEvent = monkDiagnosisEventDao.save(monkDiagnosisEvent);
            monkDiagnosisLogData.setMonkDiagnosisEventId(monkDiagnosisEvent.getMonkDiagnosisEventId());
            UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(monkDiagnosisLogData.getUnitId());
            monkDiagnosisLogData.setUnitName(Objects.nonNull(unitBasicDetail) ? unitBasicDetail.getName() : null);
            List<String> unitEmails = new ArrayList<>();
            if (Objects.nonNull(unitBasicDetail)) {
                if (Objects.nonNull(unitBasicDetail.getEmail())) {
                    unitEmails.add(unitBasicDetail.getEmail());
                }
                if (Objects.nonNull(unitBasicDetail.getCafeManagerId())) {
                    EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(unitBasicDetail.getCafeManagerId());
                    if (Objects.nonNull(employeeBasicDetail) && Objects.nonNull(employeeBasicDetail.getEmailId())) {
                        unitEmails.add(employeeBasicDetail.getEmailId());
                    }
                }
            }
            monkDiagnosisLogData.setUserIdsName(masterDataCache.getEmployee(monkDiagnosisLogData.getUserId()));
            Map<Date, Map<String, List<TroubleShoot>>> sortedMap = new TreeMap<>(monkDiagnosisLogData.getMonkDiagnosisData());
            monkDiagnosisLogData.setMonkDiagnosisData(sortedMap);
            List<MonkDiagnosisTroubleShoot> monkDiagnosisTroubleShoots = new ArrayList<>();
            for (Map.Entry<Date, Map<String, List<TroubleShoot>>> entry : sortedMap.entrySet()) {
                for (Map.Entry<String, List<TroubleShoot>> innerEntry : entry.getValue().entrySet()) {
                    if (innerEntry.getKey().equalsIgnoreCase("DIAGNOSIS")) {
                        for (TroubleShoot ts : innerEntry.getValue()) {
                            MonkDiagnosisTroubleShoot monkDiagnosisTroubleShoot = new MonkDiagnosisTroubleShoot();
                            monkDiagnosisTroubleShoot.setDiagnosisAt(entry.getKey());
                            monkDiagnosisTroubleShoot.setMonkDiagnosisEventId(monkDiagnosisEvent.getMonkDiagnosisEventId());
                            monkDiagnosisTroubleShoot.setCode(ts.getCode());
                            monkDiagnosisTroubleShoot.setCodeMeaning(ts.getCodeMeaning());
                            monkDiagnosisTroubleShoot.setAcknowledged(AppUtils.setStatus(ts.isAcknowleged()));
                            monkDiagnosisTroubleShoots.add(monkDiagnosisTroubleShoot);
                        }
                    }
                }
            }
            if (!monkDiagnosisTroubleShoots.isEmpty()) {
                monkDiagnosisTroubleShootDao.saveAll(monkDiagnosisTroubleShoots);
            }
            try {
                MonkDiagnosisEmailNotificationTemplate monkDiagnosisEmailNotificationTemplate = new MonkDiagnosisEmailNotificationTemplate(monkDiagnosisLogData, props.getBasePath());
                MonkDiagnosisEmailNotification monkDiagnosisEmailNotification = new MonkDiagnosisEmailNotification(monkDiagnosisEmailNotificationTemplate, props.getEnvironmentType(), unitEmails);
                monkDiagnosisEmailNotification.sendEmail();
            } catch (Exception e) {
                LOG.error("Error while Sending Email  :: ", e);
            }
        } catch (Exception e) {
            LOG.error("Error while addMonkDiagnosisLogToSql :: ", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addWeightCalibrationDetail(WeightCalibrationDetail weightCalibrationDetail){
        try{

            WeightCalibrationDetailData weightCalibrationDetailData = new WeightCalibrationDetailData();

            weightCalibrationDetailData.setUnitId(weightCalibrationDetail.getUnitId());
            weightCalibrationDetailData.setUserId(weightCalibrationDetail.getUserId());
            weightCalibrationDetailData.setUserName(weightCalibrationDetail.getStartedby());
            weightCalibrationDetailData.setMonkName(weightCalibrationDetail.getMonkName());
            weightCalibrationDetailData.setKnownWeight(weightCalibrationDetail.getKnownWeight());
            weightCalibrationDetailData.setMeasuredWeight(weightCalibrationDetail.getMeasuredWeight());
            if(Objects.nonNull(weightCalibrationDetail.getMeasurementTime()))
            {
                Date measurementTime = AppUtils.getDate(weightCalibrationDetail.getMeasurementTime(),"yyyy-MM-dd HH:mm:ss");
                weightCalibrationDetailData.setMeasurementTime(measurementTime);
            }
            else
            {
                weightCalibrationDetailData.setMeasurementTime(AppUtils.getCurrentTimestamp());
            }

            weightCalibrationDetailData =  weightCalibrationDetailDao.save(weightCalibrationDetailData);



            try {

                weightCalibrationDetail.setWtCalibrationEventId(weightCalibrationDetailData.getId());
                UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(weightCalibrationDetail.getUnitId());
                weightCalibrationDetail.setUnitName(Objects.nonNull(unitBasicDetail) ? unitBasicDetail.getName() : null);

                Map<String, WeightCalibrationDetail> weightCalibrationDetailMap = getWtCalibrationDetail(weightCalibrationDetail.getUnitId());
                if(Objects.nonNull(weightCalibrationDetailMap) && weightCalibrationDetailMap.containsKey(weightCalibrationDetail.getMonkName())){
                    weightCalibrationDetail.setPrevWtCalValue(weightCalibrationDetailMap.get(weightCalibrationDetail.getMonkName()).getMeasuredWeight());
                    weightCalibrationDetail.setPrevWtCalTime(weightCalibrationDetailMap.get(weightCalibrationDetail.getMonkName()).getMeasurementTime());
                }

                List<String> unitEmails = new ArrayList<>();
                if (Objects.nonNull(unitBasicDetail)) {
                    if (Objects.nonNull(unitBasicDetail.getEmail())) {
                        unitEmails.add(unitBasicDetail.getEmail());
                    }
                    if (Objects.nonNull(unitBasicDetail.getCafeManagerId())) {
                        EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(unitBasicDetail.getCafeManagerId());
                        if (Objects.nonNull(employeeBasicDetail) && Objects.nonNull(employeeBasicDetail.getEmailId())) {
                            unitEmails.add(employeeBasicDetail.getEmailId());
                        }
                    }
                }

                weightCalibrationDetail.setUserIdName(masterDataCache.getEmployee(weightCalibrationDetail.getUserId()));

                MonkWtCalibrationEmailNotificationTemplate wtCalibrationEmailNotificationTemplate = new MonkWtCalibrationEmailNotificationTemplate(weightCalibrationDetail, props.getBasePath());
                MonkWtCalibrationEmailNotification monkWtCalibrationEmailNotification = new MonkWtCalibrationEmailNotification(wtCalibrationEmailNotificationTemplate, props.getEnvironmentType(), unitEmails);
                monkWtCalibrationEmailNotification.sendEmail();
            } catch (Exception e) {
                LOG.error("Error while Sending Wt Calibration Email  :: ", e);
            }
            return true;
        }
        catch (Exception e){
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, WeightCalibrationDetail> getWtCalibrationDetail(Integer unitId){
        try{
            Map<String,WeightCalibrationDetail> weightCalibrationDetailHashMap = new HashMap<String,WeightCalibrationDetail>();
            List<WeightCalibrationDetailData> weightCalibrationDetailDataList = weightCalibrationDetailDao.getWtCalibrationValue(unitId);
            for(WeightCalibrationDetailData weightCalibrationDetailData:weightCalibrationDetailDataList)
            {
                WeightCalibrationDetail weightCalibrationDetail1 = new WeightCalibrationDetail();

                weightCalibrationDetail1.setUnitId(weightCalibrationDetailData.getUnitId());
                weightCalibrationDetail1.setUserId(weightCalibrationDetailData.getUserId());
                weightCalibrationDetail1.setStartedby(weightCalibrationDetailData.getMonkName());
                weightCalibrationDetail1.setMonkName(weightCalibrationDetailData.getMonkName());
                weightCalibrationDetail1.setKnownWeight(weightCalibrationDetailData.getKnownWeight());
                weightCalibrationDetail1.setMeasuredWeight(weightCalibrationDetailData.getMeasuredWeight());
                weightCalibrationDetail1.setMeasurementTime(AppUtils.getDateString(weightCalibrationDetailData.getMeasurementTime()));

                weightCalibrationDetailHashMap.put(weightCalibrationDetailData.getMonkName(),weightCalibrationDetail1);
            }
            return weightCalibrationDetailHashMap;
        }
        catch(Exception e){
            return  new HashMap<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, MonkDashboardData> getRealTimeDashboardDetails(Integer orderId){
        List<OrderDetail> orders = orderDetailDao.getRemakeChaiOrders(orderId, "unsatisfied-customer-order");
        return  getMonkDashboardDataMap(orders);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveRealTimeDashboardDetails(MonkStatusLog log){
           List<OrderDetail> orders = orderDetailDao.getOrder(log.getOrderId());
            Map<Integer, MonkOrderDetail> monkOrderDetailMap =  constructMonkDashboardDataMap(orders,new HashMap<>());
            monkOrderDetailMap.forEach((k,v)->{
                if(v.getItemId() == log.getTaskId()){
                    v.setForcefullyRemoved(AppUtils.setStatus(log.isForcefullyRemoved()));
                    monkOrderDetailDao.save(v);
                    LOG.info("Monk Status Log saved for orderItemId {} of orderId {}",log.getTaskId(),log.getOrderId());
                }
            });
    }


    private  Map<Integer, MonkDashboardData> getMonkDashboardDataMap(List<OrderDetail> orders){
        List<MonkOrderDetail> monkOrderDetails = new ArrayList<>();
        Map<Integer, MonkDashboardData> monkDashboardDataMap = new HashMap<>();
        for(OrderDetail orderDetail : orders){
            for(OrderItem item : orderDetail.getOrderItems()){
                MonkOrderDetail monkOrderDetail = monkOrderDetailDao.findByItemId(item.getOrderItemId());
                MonkDashboardData monkDashboardData = convertMonkOrderDetail(item,monkOrderDetail);
                monkDashboardDataMap.put(item.getOrderItemId(),monkDashboardData);
            }
        }
        return  monkDashboardDataMap;
    }

    private  Map<Integer, MonkOrderDetail> constructMonkDashboardDataMap(List<OrderDetail> orders,Map <Integer, MonkOrderDetail> monkDashboardDataMap){
        if(Objects.isNull(monkDashboardDataMap)){
            monkDashboardDataMap = new HashMap<>();
        }
        for(OrderDetail orderDetail : orders){
            for(OrderItem item : orderDetail.getOrderItems()){
                Product product = masterDataCache.getProduct(item.getProductId());
                LOG.info("Item :{},of type :{} , brandId :{}",item.getOrderItemId(),product.getType(),product.getBrandId());
                boolean isHot = false;
                if(AppUtils.getTypeForProductType(product.getType(), product.getBrandId()).equals(WsType.HOT)){
                    isHot = true;
                    MonkOrderDetail monkDashboardData =  monkDashboardDataMap.get(item.getOrderItemId());
                    if(Objects.isNull(monkDashboardData)){
                        monkDashboardData = new MonkOrderDetail();
                        monkDashboardDataMap.put(item.getOrderItemId(),monkDashboardData);
                    }
                    addOrderItemDetailsToMonkDashboardData(item,monkDashboardData,product);
                    addOrderDetailsToMonkDashboardData(orderDetail,monkDashboardData);
                }
                LOG.info("isHot : {}",isHot);
            }
        }
        return  monkDashboardDataMap;
    }

    private MonkDashboardData convertMonkOrderDetail(OrderItem item, MonkOrderDetail monkOrderDetail) {
        MonkDashboardData monkDashboardData = getMonkDashboardData(monkOrderDetail);
        addOrderItemAddons(item.getOrderItemAddons(), monkDashboardData);
        monkDashboardData.setStatusPayloadList(new HashSet<>());
        List<MonkStatusLog> logs = statusLogDao.findMonkStatusLogsByItemId(item.getOrderItemId());
        if (Objects.nonNull(logs) && logs.size() > 0) {
            MonkStatusLog log = logs.get(0);
            List<MonkStatusLogPayloadDetails> logPayloadDetails = log.getMonkMessages();
            if (Objects.nonNull(logPayloadDetails) && logPayloadDetails.size() > 0) {
                for (MonkStatusLogPayloadDetails logPayloadDetail : logPayloadDetails) {
                    MonkPayloadDashboardData payloadDashboardData = new MonkPayloadDashboardData();
                    if (StringUtils.isNotBlank(logPayloadDetail.getPayload())) {
                        String payload = logPayloadDetail.getPayload().trim();
                        payload.replaceAll("\\[", "").replaceAll("\\]", "");
                        payloadDashboardData.setPayLoad(payload);
                    }
                    payloadDashboardData.setTime(AppUtils.getFormattedTime(logPayloadDetail.getCurrDate()));
                    monkDashboardData.getStatusPayloadList().add(payloadDashboardData);
                }
            }
        }
        return monkDashboardData;
    }

    private  MonkDashboardData getMonkDashboardData(MonkOrderDetail monkOrderDetail){
        MonkDashboardData monkDashboardData = new MonkDashboardData();
        monkDashboardData.setRemakeOrder(AppUtils.getStatus(monkOrderDetail.getRemakeOrder()));
        monkDashboardData.setRemakeReason(monkOrderDetail.getRemakeReason());
        monkDashboardData.setOrderId(monkOrderDetail.getOrderId());
        monkDashboardData.setItemId(monkOrderDetail.getItemId());
        monkDashboardData.setNoOfBoils(monkOrderDetail.getNoOfBoils());
        monkDashboardData.setExpectedNoOfBoils(monkOrderDetail.getExpectedNoOfBoils());
        monkDashboardData.setSteepingTime(monkOrderDetail.getSteepingTime());
        monkDashboardData.setSteewingTime(monkOrderDetail.getSteewingTime());
        monkDashboardData.setExpectedMilkQuatity(monkOrderDetail.getExpectedMilkQuatity());
        monkDashboardData.setExpectedWaterQuatity(monkOrderDetail.getExpectedWaterQuatity());
        monkDashboardData.setActualMilkQuatity(monkOrderDetail.getActualMilkQuatity());
        monkDashboardData.setActualWaterQuatity(monkOrderDetail.getActualWaterQuatity());
        monkDashboardData.setMonkName(monkOrderDetail.getMonkName());
        monkDashboardData.setCalibrationCoef(monkOrderDetail.getCalibrationCoef());
        monkDashboardData.setRecipeVersion(monkOrderDetail.getRecipeVersion());
        monkDashboardData.setRecipeString(monkOrderDetail.getRecipeString());
        monkDashboardData.setFinalQuantity(monkOrderDetail.getFinalQuantity());
        monkDashboardData.setExpectedFinalQuantity(monkOrderDetail.getExpectedFinalQuantity());
        monkDashboardData.setProductQuantity(monkOrderDetail.getProductQuantity());
        monkDashboardData.setProductId(monkOrderDetail.getProductId());
        monkDashboardData.setProductName(monkOrderDetail.getProductName());
        monkDashboardData.setProductDimension(monkOrderDetail.getProductDimension());
        monkDashboardData.setUnitName(monkOrderDetail.getUnitName());
        monkDashboardData.setUnitId(monkOrderDetail.getUnitId());
        monkDashboardData.setBillingServerTime(AppUtils.getFormattedTime(monkOrderDetail.getBillingServerTime()));
        monkDashboardData.setTaskStartTime(AppUtils.getFormattedTime(monkOrderDetail.getTaskStartTime()));
        monkDashboardData.setTaskCompletionTime(AppUtils.getFormattedTime(monkOrderDetail.getTaskCompletionTime()));
        monkDashboardData.setTaskCompletedIn(monkOrderDetail.getTaskCompletedIn());
        monkDashboardData.setAssemblyOrderCreationTime(AppUtils.getFormattedTime(monkOrderDetail.getAssemblyOrderCreationTime()));
        monkDashboardData.setPreperationTime(monkOrderDetail.getPreperationTime());
        return  monkDashboardData;
    }


    private  void addOrderDetailsToMonkDashboardData(OrderDetail order, MonkOrderDetail monkDashboardData){
        monkDashboardData.setOrderId(order.getOrderId());
        boolean remakeOrder = order.getOrderType().equalsIgnoreCase(AppConstants.UNSATISFIED_CUSTOMER_ORDER) && Objects.nonNull(order.getLinkedOrderId());
        monkDashboardData.setRemakeOrder(AppUtils.setStatus(remakeOrder));
        int unitId = order.getUnitId();
        monkDashboardData.setUnitId(unitId);
        Unit unit = masterDataCache.getUnit(unitId);
        monkDashboardData.setUnitName(unit.getName());
        monkDashboardData.setBillingServerTime(order.getBillingServerTime());
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    private void addOrderItemAddons(List<OrderItemAddon> orderItemAddons, MonkDashboardData monkDashboardData){
        if(Objects.isNull(monkDashboardData.getOrderItemAddons())){
            monkDashboardData.setOrderItemAddons(new ArrayList<>());
        }
        for(OrderItemAddon itemAddon : orderItemAddons){
            OrderItemAddon addon = new OrderItemAddon();
            addon.setOrderItemAddonId(itemAddon.getOrderItemAddonId());
            addon.setDimension(itemAddon.getDimension());
            addon.setName(itemAddon.getName());
            addon.setProductId(itemAddon.getProductId());
            addon.setQuantity(itemAddon.getQuantity());
            addon.setDefaultSetting(itemAddon.getDefaultSetting());
            addon.setSource(itemAddon.getSource());
            addon.setType(itemAddon.getType());
            addon.setUom(itemAddon.getUom());
            monkDashboardData.getOrderItemAddons().add(addon);
        }
    }

    private  void addOrderItemDetailsToMonkDashboardData(OrderItem item, MonkOrderDetail monkDashboardData,Product product){
        monkDashboardData.setProductId(product.getId());
        monkDashboardData.setProductName(product.getName());
        monkDashboardData.setProductDimension(item.getDimension());
        monkDashboardData.setPreperationTime(product.getPrepTime().multiply(BigDecimal.valueOf(Math.sqrt(item.getQuantity()))).setScale(2, RoundingMode.HALF_UP).toString());

        //mongo query based on item id
        switch (item.getDimension()){
            case NONE:
                monkDashboardData.setExpectedFinalQuantity(NONE_QUANTITY * item.getQuantity() );
                break;
            case REGULAR:
                monkDashboardData.setExpectedFinalQuantity(REGUALR_QUANTITY * item.getQuantity());
                break;
            case CHOTI_KETLI:
                monkDashboardData.setExpectedFinalQuantity(CHOTI_KETLI_QUANTITY * item.getQuantity());
                break;
            case MINI_KETLI:
                monkDashboardData.setExpectedFinalQuantity(MINI_KETLI_QUANTITY * item.getQuantity());
                break;
            case BADI_KETLI:
                monkDashboardData.setExpectedFinalQuantity(BADI_KETLI_QUANTITY * item.getQuantity());
                break;
            case FULL:
                monkDashboardData.setExpectedFinalQuantity(FULL_QUANTITY * item.getQuantity());
                break;
        }
        monkDashboardData.setItemId(item.getOrderItemId());
        monkDashboardData.setProductQuantity(item.getQuantity());
        List<MonkTaskStatusDetailLog> detailLogs = taskStatusDetailDao.findMonkTaskStatusDetailByItemId(item.getOrderItemId());
        if(Objects.nonNull(detailLogs) && detailLogs.size()>0){
            MonkTaskStatusDetailLog log = detailLogs.get(0);
           monkDashboardData.setAssemblyOrderCreationTime(log.getOrderCreationTime());
            monkDashboardData.setMonkName(log.getMonkName().trim());
        }
        List<MonkStatusLog> statusLogList = statusLogDao.findMonkStatusLogsByItemId(item.getOrderItemId());
        Date chaiStartTime = null;
        Date chaiEndTime = null;
        int lastStatusCode=-230;
        int noOfBoils=0;
        int milkPourLogCount = 0;
        int pattiWindowLogCount = 0;
        int monkTaskCompleteLogCount = 0;

        for(MonkStatusLog statusLog : statusLogList){
            for(MonkStatusLogPayloadDetails logPayloadDetail : statusLog.getMonkMessages()){
                int sCode = logPayloadDetail.getsCode();
                if(Math.abs(sCode)>100){
                    MonkRemakeError monkRemakeError = new MonkRemakeError();
                    monkRemakeError.setUnitId(statusLog.getUid());
                    monkRemakeError.setMonkName(statusLog.getName());
                    monkRemakeError.setOrderId(statusLog.getOrderId());
                    monkRemakeError.setTaskId(statusLog.getTaskId());
                    monkRemakeError.setSCode(sCode);
                    monkRemakeError.setCodeReason(SCodeReasons.getNameBySCode(sCode));
                    monkRemakeErrorDao.save(monkRemakeError);
                }
            }
        }

        if(Objects.nonNull(statusLogList) && statusLogList.size()>0){
            MonkStatusLog log = statusLogList.get(0);
            for(MonkStatusLogPayloadDetails statusLog : log.getMonkMessages()){
                int sCode = statusLog.getsCode();
                if(sCode == 6 || sCode == 5 || sCode == 4 || sCode ==3 || sCode==2 ||sCode==1){
                    if(lastStatusCode == -230 ){
                        lastStatusCode = sCode;
                    }else if(lastStatusCode == sCode + 1){
                        noOfBoils++;
                        lastStatusCode = sCode;
                    }
                }
                if(Math.abs(sCode)>100){
                    monkDashboardData.setRemakeReason(SCodeReasons.getNameBySCode(sCode));
                    monkDashboardData.setRemakeOrder(AppUtils.YES);
                }

                if(StringUtils.isNotBlank(statusLog.getPayload())){
                    String payload = statusLog.getPayload().trim();
                    payload.replaceAll("\\[", "").replaceAll("\\]","");
                    if(sCode==8 && Objects.isNull(chaiStartTime)){
                        chaiStartTime = statusLog.getCurrDate();
                        monkDashboardData.setTaskStartTime(chaiStartTime);
                    }
                    if (sCode == 7 && milkPourLogCount == 0) {
                        milkPourLogCount += 1;
                        String waterQuantity = payload.split(",")[1];
                        monkDashboardData.setActualWaterQuatity(waterQuantity);
                    }
                    if (sCode == 16 && pattiWindowLogCount == 0) {
                        pattiWindowLogCount += 1;
                        String milkAndWaterQuantity = payload.split(",")[5];
                        try {
                            double actualMilkQ = Double.parseDouble(milkAndWaterQuantity)
                                    - Double.parseDouble(monkDashboardData.getActualWaterQuatity());
                            monkDashboardData.setActualMilkQuatity(actualMilkQ + "");
                        } catch (Exception e) {
                            LOG.info("Unable to calculate actual milk quantity");
                        }
                    }

                    if(StringUtils.isNotBlank(statusLog.getType())) {
                        if (statusLog.getType().equals("Recipe")) {
                            monkDashboardData.setRecipeVersion(payload);
                            monkDashboardData.setRecipeString(payload);
                            monkDashboardData.setExpectedWaterQuatity(payload.substring(2,6));
                            monkDashboardData.setExpectedMilkQuatity(payload.substring(6,10));
                            monkDashboardData.setExpectedNoOfBoils(payload.substring(12,13));
                            String steewTime = payload.substring(13, 17);
                            String steepTime = payload.substring(17, 21);
                            monkDashboardData.setSteepingTime(steepTime.substring(0,2)+" min "+steepTime.substring(2,4)+" sec ");
                            monkDashboardData.setSteewingTime(steewTime.substring(0,2)+" min "+steewTime.substring(2,4)+" sec ");
                            try{
                                int minutes = Integer.parseInt(steepTime.substring(0, 2));
                                int seconds = Integer.parseInt(steepTime.substring(2, 4));
                                Long totalSeconds = minutes * 60L + seconds;
                                monkDashboardData.setSteepingTimeInSeconds(totalSeconds);
                                int minutes1 = Integer.parseInt(steewTime.substring(0, 2));
                                int seconds1 = Integer.parseInt(steewTime.substring(2, 4));
                                Long totalSeconds1 = minutes1 * 60L + seconds1;
                                monkDashboardData.setSteewingTimeInSeconds(totalSeconds1);
                            } catch (NumberFormatException e) {
                                LOG.error("Unable to convert steeping and stewing time in seconds in monkDashboardData for monkOrderId: {}",monkDashboardData.getMonkOrderId());
                            }
                        }
                        if (statusLog.getType().equals("RecipeVersion")) {
                            monkDashboardData.setRecipeVersion(payload);
                        }
                    }
                    if (sCode == 1 && monkTaskCompleteLogCount == 0) {
                        monkTaskCompleteLogCount += 1;
                        monkDashboardData.setFinalQuantity(Double.valueOf(payload.split(",")[5]));
                    }
                }
                if((sCode==1 ) && Objects.isNull(chaiEndTime)){
                    chaiEndTime = statusLog.getCurrDate();
                }
                //calculate all extra
            }
        }
        monkDashboardData.setNoOfBoils(noOfBoils);
        if(Objects.nonNull(chaiStartTime) && Objects.nonNull(chaiEndTime)){
            monkDashboardData.setTaskCompletionTime(chaiEndTime);
            monkDashboardData.setTaskCompletedIn(Math.abs(AppUtils.getTimeDiffernceInMinutes(chaiStartTime,chaiEndTime)));
            double timeTakenInTaskCompletion = monkDashboardData.getTaskCompletedIn();
            int fullMinutes = (int) timeTakenInTaskCompletion;
            double partialMinutes = timeTakenInTaskCompletion - fullMinutes;
            long seconds = fullMinutes * 60 + (long)(partialMinutes * 60);
            monkDashboardData.setTaskCompletedInSeconds(seconds);
        }
    }

    private String getReceipeStringToSearch(Integer productId, Integer quantity, String dimension) {
        if (productId.equals(20) || productId.equals(30))
            dimension = "None";
        return productId + "#" + dimension + "#" + quantity + "$";
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveMonkTaskCompletionStats(MonkStatusLog log) {
        try {
            LOG.info("Saving task completion stats for Order Id : {} and Task Id : {}", log.getOrderId(), log.getTaskId());
            if (Objects.nonNull(log.getMonkMessages()) && !log.getMonkMessages().isEmpty()) {
                Optional<MonkStatusLogPayloadDetails> recipeString = log.getMonkMessages().stream().
                        filter(e -> Objects.nonNull(e.getType()) && e.getType().equalsIgnoreCase("Recipe")).findFirst();
                List<MonkTaskCompletionStats> monkTaskCompletionStatsList = new ArrayList<>();
                if (recipeString.isPresent()) {
                    List<MonkStatusLogPayloadDetails> monkMessages = log.getMonkMessages();
                    for (int i = 0; i < monkMessages.size(); i++) {
                        if (monkMessages.get(i).getsCode() == AppConstants.CHAI_COMPLETION_STATUS_CODE && !monkMessages.get(i).isLastStateCodeLog()) {
                            String[] messageArray = monkMessages.get(i).getPayload().split(",");
                            Integer actualBoilCount = Integer.parseInt(messageArray[1]);
                            Integer expectedBoilCount = Integer.parseInt(messageArray[2]);
                            Integer totalTimeForPouring = Integer.parseInt(messageArray[3]);
                            Integer totalTimeForAddPatti = Integer.parseInt(messageArray[4]);
                            Integer totalTimeForBrewing = Integer.parseInt(messageArray[5]);
                            Integer totalTimeForTaskCompletion = Integer.parseInt(messageArray[6]);
                            Integer actualWaterDispensed = Integer.parseInt(messageArray[7]);
                            Integer expectedWaterToDispense = Integer.parseInt(recipeString.get().getPayload().substring(2, 6));
                            Integer actualMilkDispensed = Integer.parseInt(messageArray[8]);
                            Integer expectedMilkToDispense = Integer.parseInt(recipeString.get().getPayload().substring(6, 10));
                            Integer finalQuantityWithIngredients = Integer.parseInt(messageArray[9]);
                            Integer boilsDetectedBeforeAddPatti = Integer.parseInt(messageArray[10]);
                            Integer minimumTargetWeight = Integer.parseInt(messageArray[11]);
                            BigDecimal firmwareVersion = new BigDecimal(messageArray[12]);
                            Integer expectedWaterToDispenseAfterAddons = Integer.parseInt(messageArray[13]);
                            Integer expectedMilkToDispenseAfterAddons = Integer.parseInt(messageArray[14]);
                            String recipeAfterAddons = messageArray[15];

                            MonkTaskCompletionStats monkTaskCompletionStats = new MonkTaskCompletionStats(log.getOrderId(), log.getTaskId(), log.getUid(),
                                    actualBoilCount, expectedBoilCount, totalTimeForPouring, totalTimeForAddPatti, totalTimeForBrewing, totalTimeForTaskCompletion, actualWaterDispensed,
                                    expectedWaterToDispense, actualMilkDispensed, expectedMilkToDispense, finalQuantityWithIngredients,boilsDetectedBeforeAddPatti, minimumTargetWeight,
                                    firmwareVersion, AppUtils.getCurrentTimestamp(),expectedWaterToDispenseAfterAddons,expectedMilkToDispenseAfterAddons,recipeAfterAddons);
                            // check for remake or not
                            if (monkMessages.get(i - 1).getsCode() == AppConstants.RESPONSE_CHAI_COMPLETE) {
                                monkTaskCompletionStats.setIsActualCompletionLog(AppConstants.YES);
                            } else {
                                monkTaskCompletionStats.setIsActualCompletionLog(AppConstants.NO);
                            }
                            monkTaskCompletionStatsList.add(monkTaskCompletionStats);
                        }
                    }
                    monkTaskCompletionStatsDao.saveAll(monkTaskCompletionStatsList);
                } else {
                    LOG.info("No Recipe Log found for Order Id : {} and Task Id : {}", log.getOrderId(), log.getTaskId());
                }
            } else {
                LOG.info("No Monk Messages found for Order Id : {} and Task Id : {}", log.getOrderId(), log.getTaskId());
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While saveMonkTaskCompletionStats :: ", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveMonkTaskCompletionStatLogs(MonkTaskCompletionStatsDto log) {
        try {
            LOG.info("Saving task completion stats for Order Id : {} and Task Id : {}", log.getOrderId(), log.getTaskId());
            MonkTaskCompletionStats monkTaskCompletionStats = MonkTaskCompletionStatsMapper.INSTANCE.toMonkTaskCompletionStats(log);
            monkTaskCompletionStats.setMonkTaskCompletionStatsId(null);
            monkTaskCompletionStats.setFinalQuantityWithIngredients(log.getFinalQuantityWithIngredients());
            monkTaskCompletionStats.setLogAddTimeAtServer(AppUtils.getCurrentTimestamp());
            Pair<String, String> monkRecipeProfileVersionOfUnit = recipeCache.getMonkRecipeProfileVersionOfUnit(log.getUnitId());
            if (Objects.nonNull(monkRecipeProfileVersionOfUnit)) {
                monkTaskCompletionStats.setActualRecipeRegionOfUnit(monkRecipeProfileVersionOfUnit.getKey());
                monkTaskCompletionStats.setActualRecipeVersionOfUnit(monkRecipeProfileVersionOfUnit.getValue());
            }
            monkTaskCompletionStats.setIsSplit(log.getIsSplit());
            monkTaskCompletionStats.setLinkedTaskId(log.getLinkedTaskId());
            monkTaskCompletionStats.setMonkName(log.getMonkName());
            monkTaskCompletionStats.setHotStationVersion(log.getHotStationVersion());
            monkTaskCompletionStats.setAssemblyVersion(log.getAssemblyVersion());
            monkTaskCompletionStats.setClubbedWithTask(log.getClubbedWithTask());
            monkTaskCompletionStats.setWeightCalibrationPoint(log.getWeightCalibrationPoint());
            monkTaskCompletionStats.setForcefullyRemoved(log.getForcefullyRemoved());
            monkTaskCompletionStats.setExpectedWaterToDispenseAfterAddons(log.getExpectedWaterToDispenseAfterAddons());
            monkTaskCompletionStats.setExpectedMilkToDispenseAfterAddons(log.getExpectedMilkToDispenseAfterAddons());
            monkTaskCompletionStats.setRecipeAfterAddons(log.getRecipeAfterAddons());
            final MonkTaskCompletionStats monkTaskCompletion = monkTaskCompletionStatsDao.save(monkTaskCompletionStats);
            if (Objects.nonNull(log.getRecipeLevelAddons()) && !log.getRecipeLevelAddons().isEmpty()) {
                List<MonkRecipeAddonsDataDto> monkRecipeAddonsDataDtoList = log.getRecipeLevelAddons().entrySet().stream()
                        .map(entry -> {
                            MonkRecipeAddonsDataDto dto = new MonkRecipeAddonsDataDto();
                            dto.setMonkTaskCompletionStatsId(monkTaskCompletion.getMonkTaskCompletionStatsId());
                            dto.setAddonName(entry.getKey());
                            dto.setQuantity(entry.getValue());
                            return dto;
                        })
                        .collect(Collectors.toList());
                List<MonkRecipeAddonsData> monkRecipeAddonsDataList = monkRecipeAddonsDataDtoList.stream()
                        .map(dto -> {
                            MonkRecipeAddonsData entity = new MonkRecipeAddonsData();
                            entity.setMonkTaskCompletionStatsId(dto.getMonkTaskCompletionStatsId());
                            entity.setAddonName(dto.getAddonName());
                            entity.setQuantity(dto.getQuantity());
                            return entity;
                        })
                        .collect(Collectors.toList());
                monkRecipeAddonsDataDao.saveAll(monkRecipeAddonsDataList);
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While saveMonkTaskCompletionStats :: ", e);
        }
    }

    @Override
    public void sendGChatMessageOfError(String data) {
        try {
            LOG.info("Sending sendGChatMessageOfError : {}", data);
            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                    "ANALYTICS", SlackNotification.ASSEMBLY_ERRORS, data);
        } catch (Exception e) {
            LOG.error("Exception Occurred While sendGChatMessageOfError :: ", e);
        }
    }

    @Override
    public boolean savePreAllocationLogs(Integer unitId, Date businessDate) {
        try {
            Optional<List<MonkTaskStatusDetailLog>> monkTaskStatusDetailLogs = taskStatusDetailDao.findByUnitIdAndOrderCreationTimeGreaterThanEqual(unitId, businessDate);
            Map<Integer, Long> queuingTimeMap = new HashMap<>();
            for (MonkTaskStatusDetailLog monkTaskStatusDetailLog : monkTaskStatusDetailLogs.get()) {
                Long quequingTime = monkTaskStatusDetailLog.getMonkPreAllocationLogs().get(0).getAllocationLogTime()-monkTaskStatusDetailLog.getTaskCreationTime();
                queuingTimeMap.put(monkTaskStatusDetailLog.getOrderItemId(), quequingTime);
            }
            queuingTimeMap.keySet().forEach(key -> {
                List<WorkstationLog> workstationLogList = workStationLogDao.findByItemId(key);
                if(!workstationLogList.isEmpty()) {
                    WorkstationLog workstationLog = workstationLogList.get(0);
                    workstationLog.setQuequingTime(queuingTimeMap.get(key));
                    workStationLogDao.save(workstationLog);
                }
                else {
                    LOG.error("Unable to find workstation logs for orderItemId : {}",key);
                }
            });
            return true;
        } catch (Exception e) {
            LOG.error("Error saving data for workstationLogs for unitId : {}",unitId, e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean sendNotificationToAndroid(NotificationData notificationData) {
        List<AssemblyNotificationUnitWiseData> storedUnitWiseNotifications = assemblyNotificationUnitWiseDataDao.findByUnitIdInAndNotificationTypeIn(notificationData.getUnitIds(), notificationData.getNotificationType());
        Map<String, AssemblyNotificationUnitWiseData> uniNotificationTypeMap = storedUnitWiseNotifications
                .stream().collect(Collectors.toMap(this::getUnitNotificationKey, Function.identity(), (existing, replacement) -> existing));
        Map<Integer, AssemblyNotificationUnitWiseData> unitWiseNotificationMapById = storedUnitWiseNotifications
                .stream().collect(Collectors.toMap(AssemblyNotificationUnitWiseData::getAssemblyNotificationUnitWiseDataId, Function.identity(), (existing, replacement) -> existing));
        List<AssemblyNotificationUnitWiseData> assemblyNotificationUnitWiseDataList = new ArrayList<>();
        List<AssemblyNotificationData> assemblyNotificationDataList = new ArrayList<>();

        for (Integer unit : notificationData.getUnitIds()) {
            for (String notificationType : notificationData.getNotificationType()) {
                String key = unit + "_" + notificationType;
                if (!uniNotificationTypeMap.containsKey(key)) {
                    AssemblyNotificationUnitWiseData notificationUnitWiseData = new AssemblyNotificationUnitWiseData();
                    notificationUnitWiseData.setUnitId(unit);
                    notificationUnitWiseData.setNotificationType(notificationType);
                    assemblyNotificationUnitWiseDataList.add(notificationUnitWiseData);
                }
            }
        }
        if (!assemblyNotificationUnitWiseDataList.isEmpty()){
            assemblyNotificationUnitWiseDataDao.saveAll(assemblyNotificationUnitWiseDataList);
            storedUnitWiseNotifications = assemblyNotificationUnitWiseDataDao.findByUnitIdInAndNotificationTypeIn(notificationData.getUnitIds(), notificationData.getNotificationType());
            uniNotificationTypeMap = storedUnitWiseNotifications
                    .stream().collect(Collectors.toMap(this::getUnitNotificationKey, Function.identity(), (existing, replacement) -> existing));
            unitWiseNotificationMapById = storedUnitWiseNotifications
                    .stream().collect(Collectors.toMap(AssemblyNotificationUnitWiseData::getAssemblyNotificationUnitWiseDataId, Function.identity(), (existing, replacement) -> existing));
        }
        for (Integer unit : notificationData.getUnitIds()) {
            for (String notificationType : notificationData.getNotificationType()) {
                String key = unit + "_" + notificationType;
                AssemblyNotificationData data = new AssemblyNotificationData();
                data.setAssemblyNotificationUnitWiseDataId(uniNotificationTypeMap.get(key).getAssemblyNotificationUnitWiseDataId());
                data.setNotificationStartTime(AppUtils.getCurrentTimestamp());
                data.setNotificationStatus(AppConstants.INITIATED);
                data.setNotificationTriggeredBy(notificationData.getNotificationTriggeredBy());
                assemblyNotificationDataList.add(data);
            }
        }
        List<AssemblyNotificationData> notificationDataList = assemblyNotificationDao.saveAll(assemblyNotificationDataList);

        EnvType envType = props.getEnvironmentType();
        for (AssemblyNotificationData data : notificationDataList) {
            AssemblyNotificationUnitWiseData notificationUnitWiseData = unitWiseNotificationMapById.get(data.getAssemblyNotificationUnitWiseDataId());
            notificationUnitWiseData.setLastAssemblyNotificationId(data.getKeyId());
            unitWiseNotificationMapById.put(data.getAssemblyNotificationUnitWiseDataId(), notificationUnitWiseData);
            Integer unitId = notificationUnitWiseData.getUnitId();
            String notificationType = notificationUnitWiseData.getNotificationType();
            AndroidPushNotification androidPushNotification = new AndroidPushNotification(data.getKeyId(), unitId, notificationType, props.getAssemblyFirestoreUnits(), String.valueOf(props.isAssemblyFirestoreEnabledForAll()), FireStoreNotificationType.ANDROID_NOTIFICATION);
            androidPushNotification.setSendToAndroid(AppConstants.YES);
            androidPushNotification.setTopic(AppUtils.getAndroidNotificationChannel(envType.name(), unitId));
            try {
                boolean isNotificationSend = firebaseNotificationService.sendNotification(envType, androidPushNotification);
                if (isNotificationSend) {
                    LOG.info("Notification send successfully for unitId {} and notificationType {}", unitId, notificationType);
                }
            } catch (Exception e) {
                LOG.info("Can not send notification for unitId {} and notificationType {}", unitId, notificationType);
            }
        }
        assemblyNotificationUnitWiseDataDao.saveAll(unitWiseNotificationMapById.values());
        return true;
    }

    private String getUnitNotificationKey(AssemblyNotificationUnitWiseData unitWiseData) {
        return unitWiseData.getUnitId() + "_" + unitWiseData.getNotificationType();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveNotificationToAndroid(Integer keyId, String responseMessage) {
        Optional<AssemblyNotificationData> assemblyRecipeVersion1 = assemblyNotificationDao.findById(keyId);
        if (assemblyRecipeVersion1.isPresent()) {
            AssemblyNotificationData version = assemblyRecipeVersion1.get();
            version.setNotificationEndTime(AppUtils.getCurrentTimestamp());
            version.setNotificationStatus(responseMessage.split("-")[0]);
            version.setResponseMessage(responseMessage.split("-")[1]);
            try {
                assemblyNotificationDao.save(version);
                return true;
            } catch (Exception e) {
                LOG.error("Unable to save for keyId :{} and reason : {} as {}", keyId, responseMessage, e);
            }
        } else {
            LOG.error("KeyId {} not found", keyId);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void markAssemblyNotificationsAsNotDelivered() {
        try {
            Date before15MinutesTime = AppUtils.addMinutesToDate(AppUtils.getCurrentTimestamp(), -15);
            List<AssemblyNotificationData> notificationsList = assemblyNotificationDao.findByNotificationStatusEqualsIgnoreCaseAndNotificationStartTimeLessThan(AppConstants.INITIATED, before15MinutesTime);
            if (Objects.nonNull(notificationsList) && !notificationsList.isEmpty()) {
                for (AssemblyNotificationData notificationData : notificationsList) {
                    LOG.info("Updating Notification as Not Delivered for notification id : {}", notificationData.getKeyId());
                    notificationData.setNotificationStatus(AppConstants.NOT_DELIVERED);
                    notificationData.setNotificationEndTime(AppUtils.getCurrentTimestamp());
                }
            }
            assemblyNotificationDao.saveAll(notificationsList);
        } catch (Exception e) {
            LOG.error("Error while markAssemblyNotificationsAsNotDelivered :: ", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addGenericLogData(GenericLogData log, Gson gson) {
        // now we received generic log based on the json data class name we will take out the exact class
        if (log.getJsonDataClassName().equalsIgnoreCase(MonkTaskCompletionStatsDto.class.getSimpleName())) {
            MonkTaskCompletionStatsDto taskCompletionStats = gson.fromJson(log.getJsonData(), MonkTaskCompletionStatsDto.class);
            saveMonkTaskCompletionStatLogs(taskCompletionStats);
        }
        else if (log.getJsonDataClassName().equalsIgnoreCase(MonkManualTask.class.getSimpleName())) {
            handleMonkManualTask(log, LOG);
        } else if (log.getJsonDataClassName().equalsIgnoreCase(RemakeData.class.getSimpleName())) {
            monkRemakeDataDao.save(gson.fromJson(log.getJsonData(), RemakeData.class));
        }
    }

    private void handleMonkManualTask(GenericLogData log, Logger logger) {
        try {
            List<MonkManualTaskDTO> monkManualTaskDTOList = gson.fromJson(log.getJsonData(), new TypeToken<List<MonkManualTaskDTO>>() {}.getType());

            MonkManualTaskMapper monkManualTaskMapper = MonkManualTaskMapper.INSTANCE;
            List<MonkManualTask> monkManualTasks = monkManualTaskMapper.toEntityWithLink(monkManualTaskDTOList);
             monkManualTaskDao.saveAll(monkManualTasks);
            logger.info("Successfully saved {} monk manual tasks with their addons", monkManualTasks.size());

        } catch (Exception e) {
            logger.error("Error processing monk manual tasks: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to process monk manual tasks", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addMonkXTwoLog(MonkXTwoLog log) {
        try {
            monkXTwoLogDao.save(log);
            saveMonkXTwoLogToSql(log);
        } catch (Exception e) {
            LOG.info("Unable to save Monk XTwo log ", e);
        }
    }

    private void saveMonkXTwoLogToSql(MonkXTwoLog log) {
        try {
            if (Objects.nonNull(log.getChaiMonkMap()) && Objects.isNull(log.getChaiMonkMap().getResetReason())
                && Objects.nonNull(log.getChaiMonkMap().getCurrentTaskRecipe()) && XTwoCommandType.AUTOCOOK_START.equals(log.getChaiMonkMap().getCurrentTaskRecipe().getCMD())) {
                // converting the data to Monk Task Completion log
                ChaiMonkXTwoRecipe currentTaskRecipe = log.getChaiMonkMap().getCurrentTaskRecipe();

                Integer waterPouringTime = getPouringTimeOfLiquid(currentTaskRecipe, XTwoLiquidIndexEnum.WATER);
                Integer milkPouringTime = getPouringTimeOfLiquid(currentTaskRecipe, XTwoLiquidIndexEnum.MILK);
                Integer totalPouringTime = getTotalPouringTime(currentTaskRecipe);
                Integer totalTimeForAddPatti = getTotalTimeForPatti(currentTaskRecipe);
                Integer totalTimeForBrewing = getTotalTimeForBrewing(currentTaskRecipe);
                Integer totalTimeForTaskCompletion = AppUtils.getSecondsDiff(currentTaskRecipe.getTaskAcceptedTime(), currentTaskRecipe.getTaskCompletedTime());
                Integer timeToDetectVessel = getVesselDetectionTime(currentTaskRecipe);
                Integer vesselOffPositionTime = getVesselOffPositionTime(currentTaskRecipe);
                Integer totalTimeToCompleteCooking = getTaskCookingCompletionTime(currentTaskRecipe);

                List<MonkTaskCompletionStats> monkTaskCompletionStatsList = new ArrayList<>();
                MonkTaskCompletionStats monkTaskCompletionStats = new MonkTaskCompletionStats();
                monkTaskCompletionStats.setOrderId(log.getWorkTask().getOrderId());
                monkTaskCompletionStats.setUnitId(log.getUnitId());
                monkTaskCompletionStats.setTaskId(log.getWorkTask().getTaskId());
                monkTaskCompletionStats.setActualBoilCount(log.getWorkTask().getRequiredNumberOfBoils());
                monkTaskCompletionStats.setExpectedBoilCount(log.getWorkTask().getRequiredNumberOfBoils());
                monkTaskCompletionStats.setTotalTimeForPouring(totalPouringTime);
                monkTaskCompletionStats.setTotalTimeForAddPatti(totalTimeForAddPatti);
                monkTaskCompletionStats.setTotalTimeForBrewing(totalTimeForBrewing);
                monkTaskCompletionStats.setTotalTimeForTaskCompletion(totalTimeForTaskCompletion);
                monkTaskCompletionStats.setActualWaterDispensed(log.getWorkTask().getWaterQuantity());
                monkTaskCompletionStats.setExpectedWaterToDispense(log.getWorkTask().getWaterQuantity());
                monkTaskCompletionStats.setExpectedMilkToDispense(log.getWorkTask().getOriginalMilkQuantity());
                monkTaskCompletionStats.setActualMilkDispensed(log.getWorkTask().getOriginalMilkQuantity());
                monkTaskCompletionStats.setLogAddTime(log.getLogAddTime());
                monkTaskCompletionStats.setLogAddTimeAtServer(AppUtils.getCurrentTimestamp());
                monkTaskCompletionStats.setRecipe(log.getWorkTask().getRecipeString());
                monkTaskCompletionStats.setIsManualTask(AppUtils.setStatus(log.getWorkTask().isManualCreated()));
                monkTaskCompletionStats.setTaskQuantity(BigDecimal.valueOf(log.getWorkTask().getOrderItem().getQuantity()));
                monkTaskCompletionStats.setRecipeVersionAtMonk(log.getRecipeVersionAtMonk());
                monkTaskCompletionStats.setRecipeRegionAtMonk(log.getRecipeRegionAtMonk());
                monkTaskCompletionStats.setIsClubbed(AppUtils.setStatus(log.getWorkTask().isClubbed()));
                monkTaskCompletionStats.setIsSplit(AppUtils.setStatus(log.getWorkTask().isSplittedOrder()));
                monkTaskCompletionStats.setLinkedTaskId(log.getWorkTask().getLinkedTaskId());
                monkTaskCompletionStats.setHotStationVersion(log.getWebAppVersion());
                monkTaskCompletionStats.setMonkName(log.getChaiMonkMap().getChaiMonkId());
                monkTaskCompletionStats.setFirmwareVersion(new BigDecimal(log.getRecipeStepVersion()));
                if(Objects.nonNull(log.getWorkTask().getExpectedMilkToDispenseAfterAddons())){
                    monkTaskCompletionStats.setExpectedMilkToDispenseAfterAddons(log.getWorkTask().getExpectedMilkToDispenseAfterAddons());
                }
                if(Objects.nonNull(log.getWorkTask().getExpectedWaterToDispenseAfterAddons())){
                    monkTaskCompletionStats.setExpectedWaterToDispenseAfterAddons(log.getWorkTask().getExpectedWaterToDispenseAfterAddons());
                }
                if(Objects.nonNull(log.getWorkTask().getRecipeAfterAddons())){
                    monkTaskCompletionStats.setRecipeAfterAddons(log.getWorkTask().getRecipeAfterAddons());
                }
                Pair<String, String> monkRecipeProfileVersionOfUnit = recipeCache.getMonkRecipeProfileVersionOfUnit(log.getUnitId());
                if (Objects.nonNull(monkRecipeProfileVersionOfUnit)) {
                    monkTaskCompletionStats.setActualRecipeRegionOfUnit(monkRecipeProfileVersionOfUnit.getKey());
                    monkTaskCompletionStats.setActualRecipeVersionOfUnit(monkRecipeProfileVersionOfUnit.getValue());
                }
                monkTaskCompletionStatsList.add(monkTaskCompletionStats);
                if (!CollectionUtils.isEmpty(log.getWorkTask().getClubbedTaskList())) {
                    for (WorkTask workTask : log.getWorkTask().getClubbedTaskList()) {
                        MonkTaskCompletionStats clonedStats = monkTaskCompletionStats.clone();
                        clonedStats.setOrderId(workTask.getOrderId());
                        clonedStats.setTaskId(workTask.getTaskId());
                        clonedStats.setClubbedWithTask(monkTaskCompletionStats.getTaskId());
                        monkTaskCompletionStatsList.add(monkTaskCompletionStats);
                    }
                }
                monkTaskCompletionStatsList = monkTaskCompletionStatsDao.saveAll(monkTaskCompletionStatsList);

                MonkXTwoLogData monkXTwoLogData = new MonkXTwoLogData();
                monkXTwoLogData.setTaskSentToMonkTime(currentTaskRecipe.getTaskSentToMonkTime());
                monkXTwoLogData.setTaskAcceptedTime(currentTaskRecipe.getTaskAcceptedTime());
                monkXTwoLogData.setTaskCompletedTime(currentTaskRecipe.getTaskCompletedTime());
                monkXTwoLogData.setTaskStoppedTime(currentTaskRecipe.getTaskStoppedTime());
                monkXTwoLogData.setTaskStopAcceptedTime(currentTaskRecipe.getTaskStopAcceptedTime());
                monkXTwoLogData.setTaskStopRejectedTime(currentTaskRecipe.getTaskStopRejectedTime());
                monkXTwoLogData.setTaskStopRejectedTime(currentTaskRecipe.getTaskStopRejectedTime());
                monkXTwoLogData.setTaskCookingCompletedTime(currentTaskRecipe.getTaskCookingCompletedTime());
                monkXTwoLogData.setTimeToDetectVessel(timeToDetectVessel);
                monkXTwoLogData.setVesselOffPositionTime(vesselOffPositionTime);
                monkXTwoLogData.setWaterQuantity(monkTaskCompletionStats.getActualWaterDispensed());
                monkXTwoLogData.setMilkQuantity(monkTaskCompletionStats.getActualMilkDispensed());
                monkXTwoLogData.setWaterPouringTime(waterPouringTime);
                monkXTwoLogData.setMilkPouringTime(milkPouringTime);
                monkXTwoLogData.setTotalTimeForPouring(totalPouringTime);
                monkXTwoLogData.setTotalTimeForAddPatti(totalTimeForAddPatti);
                monkXTwoLogData.setTotalTimeForBrewing(totalTimeForBrewing);
                monkXTwoLogData.setTotalTimeForTaskCompletion(totalTimeForTaskCompletion);
                monkXTwoLogData.setWebAppVersion(log.getWebAppVersion());
                monkXTwoLogData.setTotalTimeToCompleteCooking(totalTimeToCompleteCooking);
                if (Objects.nonNull(totalTimeToCompleteCooking)) {
                    monkXTwoLogData.setTimeToRemovePanAfterCompletion(Math.abs(totalTimeForTaskCompletion - totalTimeToCompleteCooking));
                }
                monkXTwoLogData = monkXTwoLogDataDao.save(monkXTwoLogData);
                for (MonkTaskCompletionStats stats : monkTaskCompletionStatsList) {
                    stats.setMonkXTwoLogDataId(monkXTwoLogData);
                }
                monkTaskCompletionStatsDao.saveAll(monkTaskCompletionStatsList);
            }
        } catch (Exception e) {
            LOG.error("Error while saving monkXTwoLogData for taskId: {} and orderId: {} : ", log.getWorkTask().getTaskId(), log.getWorkTask().getOrderId(), e);
        }
    }

    private Integer getTaskCookingCompletionTime(ChaiMonkXTwoRecipe currentTaskRecipe) {
        if (Objects.nonNull(currentTaskRecipe.getTaskAcceptedTime()) && Objects.nonNull(currentTaskRecipe.getTaskCookingCompletedTime())) {
            return AppUtils.getSecondsDiff(currentTaskRecipe.getTaskAcceptedTime(), currentTaskRecipe.getTaskCookingCompletedTime());
        }
        return null;
    }

    private Integer getVesselOffPositionTime(ChaiMonkXTwoRecipe currentTaskRecipe) {
        Optional<ChaiMonkXTwoRecipeStep> vesselDetectionStep = currentTaskRecipe.getATTR().getSTEPS().stream().filter(e -> Objects.nonNull(e.getVESSEL_TYPE())
                        && !XTwoVesselTypeEnum.NO_PAN.getVesselType().equals(e.getVESSEL_TYPE()) && Objects.nonNull(e.getVesselOffPosition()))
                .findFirst();
        if (vesselDetectionStep.isPresent()) {
            return AppUtils.getSecondsDiff(vesselDetectionStep.get().getVesselOffPosition(), vesselDetectionStep.get().getCompletedTime());
        }
        return null;
    }

    private Integer getVesselDetectionTime(ChaiMonkXTwoRecipe currentTaskRecipe) {
        Optional<ChaiMonkXTwoRecipeStep> vesselDetectionStep = currentTaskRecipe.getATTR().getSTEPS().stream().filter(e -> Objects.nonNull(e.getVESSEL_TYPE()) && !XTwoVesselTypeEnum.NO_PAN.getVesselType().equals(e.getVESSEL_TYPE()))
                .findFirst();
        if (vesselDetectionStep.isPresent()) {
            return AppUtils.getSecondsDiff(vesselDetectionStep.get().getStartTime(), vesselDetectionStep.get().getCompletedTime());
        }
        return null;
    }

    private Integer getTotalTimeForBrewing(ChaiMonkXTwoRecipe currentTaskRecipe) {
        Integer result = 0;
        List<ChaiMonkXTwoRecipeStep> boilSteps = currentTaskRecipe.getATTR().getSTEPS().stream().filter(e -> Objects.nonNull(e.getBOIL_COUNT()))
                .collect(Collectors.toList());
        return getMinimumAndMaxTimeDifference(result, boilSteps);
    }

    private Integer getMinimumAndMaxTimeDifference(Integer result, List<ChaiMonkXTwoRecipeStep> steps) {
        Date minimumStartTime = steps.stream().map(ChaiMonkXTwoRecipeStep::getStartTime).min(Date::compareTo).orElse(null);
        Date maximumCompletedTime = steps.stream().map(ChaiMonkXTwoRecipeStep::getCompletedTime).max(Date::compareTo).orElse(null);
        if (Objects.nonNull(minimumStartTime) && Objects.nonNull(maximumCompletedTime)) {
            return AppUtils.getSecondsDiff(minimumStartTime, maximumCompletedTime);
        }
        return result;
    }

    private Integer getTotalTimeForPatti(ChaiMonkXTwoRecipe currentTaskRecipe) {
        Optional<ChaiMonkXTwoRecipeStep> pattiStep = currentTaskRecipe.getATTR().getSTEPS().stream()
                .filter(e -> Objects.nonNull(e.getDISPENSE_TYPE()) &&
                (XTwoDispenseTypeEnum.SPICE.getDispenseType().equals(e.getDISPENSE_TYPE()))).findFirst();
        if (pattiStep.isPresent() && Objects.nonNull(pattiStep.get().getStartTime())
                && Objects.nonNull(pattiStep.get().getCompletedTime())) {
            return AppUtils.getSecondsDiff(pattiStep.get().getStartTime(), pattiStep.get().getCompletedTime());
        }
        return null;
    }

    private Integer getTotalPouringTime(ChaiMonkXTwoRecipe currentTaskRecipe) {
        Integer result = 0;
        List<ChaiMonkXTwoRecipeStep> pouringSteps = currentTaskRecipe.getATTR().getSTEPS().stream().filter(e -> Objects.nonNull(e.getLIQUID_INDEX()))
                .collect(Collectors.toList());
        return getMinimumAndMaxTimeDifference(result, pouringSteps);
    }

    private Integer getPouringTimeOfLiquid(ChaiMonkXTwoRecipe currentTaskRecipe, XTwoLiquidIndexEnum liquidIndexEnum) {
        Optional<ChaiMonkXTwoRecipeStep> waterStep = currentTaskRecipe.getATTR().getSTEPS().stream().filter(e -> Objects.nonNull(e.getLIQUID_INDEX()) &&
                (liquidIndexEnum.getLiquidIndex() == e.getLIQUID_INDEX())).findFirst();
        if (waterStep.isPresent() && Objects.nonNull(waterStep.get().getStartTime())
                && Objects.nonNull(waterStep.get().getCompletedTime())) {
            return AppUtils.getSecondsDiff(waterStep.get().getStartTime(), waterStep.get().getCompletedTime());
        }
        return null;
    }

    @Override
    public void sendGChatMessageOfChaiMonk2Error(String data) {
        try {
            LOG.info("Sending sendGChatMessageOfError : {}", data);
            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                    "ANALYTICS", SlackNotification.CHAI_MONK_2_ERRORS, data);
        } catch (Exception e) {
            LOG.error("Exception Occurred While sendGChatMessageOfError :: ", e);
        }
    }
}

