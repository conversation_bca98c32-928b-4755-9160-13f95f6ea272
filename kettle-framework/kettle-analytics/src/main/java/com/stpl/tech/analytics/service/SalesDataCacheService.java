package com.stpl.tech.analytics.service;

import java.util.Date;
import java.util.Set;

import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.AggregatedReportData;
import com.stpl.tech.analytics.model.UnitReportData;
import com.stpl.tech.kettle.domain.model.OrderResponse;

public interface SalesDataCacheService {

	public UnitReportData addOrder(OrderResponse order);
	
	public UnitReportData getSalesData(int unitId);
	
	public Set<UnitReportData> getSalesData();
	
	public Set<AggregatedReportData> getAggregatedSalesData();
	
	public void refreshTargets(Date businessDate);

	public void refreshTargets(Date businessDate, boolean sendNotification);

	public void loadDataForTheDay(Date starteTime, Date endTime);

	public void reloadCache(Date businessDate, Date starteTime, Date endTime, boolean sendNotification);

	public AggregatedReportData addStatsForAll(OrderResponse order);
	
	public AggregatedReportData addStatsForCategory(OrderResponse order);
	
	public AggregatedReportData addStatsForSubCategory(OrderResponse order);

	/**
	 * @return
	 */
	public AggregatedReportData getAllAggregatedSalesData();

	/**
	 * 
	 */
	void saveDaily(Date businessDate);

	/**
	 * @param unitId
	 * @return
	 */
	public AggregatePenetrationData getMTDPenetration(int unitId);

	/**
	 * @param unitId
	 * @return
	 */
	AggregatePenetrationData getPreviousMonthPenetration(int unitId);

	UnitReportData getSystemSalesData();
}
