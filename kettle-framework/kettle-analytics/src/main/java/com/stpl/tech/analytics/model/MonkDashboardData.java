package com.stpl.tech.analytics.model;

import com.stpl.tech.kettle.data.model.OrderItemAddon;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Set;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class MonkDashboardData {

    boolean remakeOrder;

    String remakeReason;

    int orderId;

    int itemId;

    int taskId;

    int noOfBoils;//monkStatusLog recipe string item level

    String expectedNoOfBoils;//monkStatusLog recipe string item level

    String steepingTime;

    String steewingTime;

    String expectedMilkQuatity;//monkStatusLog recipe string item level

    String expectedWaterQuatity;//monkStatusLog recipe string item level

    String actualMilkQuatity;//monkStatusLog status code item level

    String actualWaterQuatity;//monkStatusLog status code item level

    String monkName;

    int calibrationCoef;

    String recipeVersion;//monkStatusLog recipe version item level

    String recipeString;//monkStatusLog recipe string item level

    double finalQuantity;

    double expectedFinalQuantity;

    int productQuantity;

    int productId;
    String productName;

    String productDimension;

    String unitName;
    int unitId;

    List<OrderItemAddon> orderItemAddons;

    String billingServerTime;

    String taskStartTime;

    String taskCompletionTime;

    double taskCompletedIn;

    String assemblyOrderCreationTime;


    String preperationTime;
    Set<MonkPayloadDashboardData> statusPayloadList;

}
