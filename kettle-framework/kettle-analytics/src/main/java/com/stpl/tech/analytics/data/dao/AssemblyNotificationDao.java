package com.stpl.tech.analytics.data.dao;


import com.stpl.tech.kettle.data.model.AssemblyNotificationData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface AssemblyNotificationDao extends JpaRepository<AssemblyNotificationData,Integer> {

    List<AssemblyNotificationData> findByNotificationStatusEqualsIgnoreCaseAndNotificationStartTimeLessThan(String notificationStatus, Date notificationStartTime);

}
