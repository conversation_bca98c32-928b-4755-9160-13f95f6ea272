package com.stpl.tech.analytics.dao;

import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.stpl.tech.analytics.model.UnitReportDataObject;

public interface UnitReportDataObjectDao extends MongoRepository<UnitReportDataObject, String> {

	@Query("{'instance.detail.id' : {'$eq' : ?0}, 'date' : {'$eq' : ?1}, 'time' : {'$lt' : ?2}  }")
	public List<UnitReportDataObject> findLatestRecord(final int unitId, final Date businessDate, final Date time, Sort sort);
}
