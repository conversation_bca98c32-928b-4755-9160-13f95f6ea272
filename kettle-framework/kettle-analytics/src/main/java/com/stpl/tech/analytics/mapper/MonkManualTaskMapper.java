package com.stpl.tech.analytics.mapper;

import com.stpl.tech.analytics.dto.MonkManualTaskAddonDTO;
import com.stpl.tech.analytics.dto.MonkManualTaskDTO;
import com.stpl.tech.kettle.data.model.MonkManualTask;
import com.stpl.tech.kettle.data.model.MonkManualTaskAddon;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

@Mapper
public interface MonkManualTaskMapper {
    MonkManualTaskMapper INSTANCE = Mappers.getMapper(MonkManualTaskMapper.class);
    MonkManualTask toMonkManualTask(MonkManualTaskDTO monkManualTaskDTO);
    List<MonkManualTask> toMonkManualTaskList(List<MonkManualTaskDTO> monkManualTaskDTOs);

    Set<MonkManualTaskAddon> toMonkManualTaskAddonList(List<MonkManualTaskAddonDTO> monkManualTaskAddonDTOs);

    default void linkAddons(MonkManualTask task) {
        if (task.getAddons() != null) {
            for (MonkManualTaskAddon addon : task.getAddons()) {
                addon.setMonkManualTask(task);
            }
        }
    }
    default List<MonkManualTask> toEntityWithLink(List<MonkManualTaskDTO> dto) {
        List<MonkManualTask> task = toMonkManualTaskList(dto);
        for( MonkManualTask taskItem : task) {
            linkAddons(taskItem);
        }
        return task;
    }
}
