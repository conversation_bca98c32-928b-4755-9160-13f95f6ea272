package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.kettle.data.model.MonkOrderDetail;
import com.stpl.tech.kettle.data.model.OrderDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MonkOrderDetailDao extends JpaRepository<MonkOrderDetail, Integer> {

    @Query("SELECT o FROM MonkOrderDetail  o  WHERE o.itemId = ?1  ")
    public MonkOrderDetail findByItemId(Integer itemId);

}
