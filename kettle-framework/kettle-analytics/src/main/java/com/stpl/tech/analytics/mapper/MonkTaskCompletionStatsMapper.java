/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.analytics.mapper;

import com.stpl.tech.kettle.data.model.MonkTaskCompletionStats;
import com.stpl.tech.kettle.domain.model.MonkTaskCompletionStatsDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MonkTaskCompletionStatsMapper {

    MonkTaskCompletionStatsMapper INSTANCE = Mappers.getMapper(MonkTaskCompletionStatsMapper.class);

    MonkTaskCompletionStats toMonkTaskCompletionStats(MonkTaskCompletionStatsDto monkTaskCompletionStatsDto);

}
