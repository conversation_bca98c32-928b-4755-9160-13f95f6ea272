package com.stpl.tech.analytics.notification;

import com.stpl.tech.analytics.notification.template.MonkDiagnosisEmailNotificationTemplate;
import com.stpl.tech.analytics.notification.template.MonkWtCalibrationEmailNotificationTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Objects;

public class MonkWtCalibrationEmailNotification extends EmailNotification {
    MonkWtCalibrationEmailNotificationTemplate template;
    EnvType envType;

    List<String> unitEmails;

    public MonkWtCalibrationEmailNotification(MonkWtCalibrationEmailNotificationTemplate template, EnvType envType, List<String> unitEmails) {
        this.template = template;
        this.envType = envType;
        this.unitEmails = unitEmails;
    }

    @Override
    public String[] getToEmails() {
        if (AppUtils.isDev(envType)) {
            return new String[]{"<EMAIL>"};
        } else {
            unitEmails.add("<EMAIL>");
            unitEmails.add("<EMAIL>");
            unitEmails.add("<EMAIL>");
            unitEmails.add("<EMAIL>");
            String[] simpleArray = new String[unitEmails.size()];
            return unitEmails.toArray(simpleArray);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Monk Weight Calibration " + AppUtils.getFormattedDate(AppUtils.getCurrentDateIST());
        if (Objects.nonNull(template.getWeightCalibrationDetail().getUnitName())) {
            subject = subject + " Of " + template.getWeightCalibrationDetail().getUnitName() + " [ " + template.getWeightCalibrationDetail().getUnitId() + " ] ";
        }
        if (Objects.nonNull(template.getWeightCalibrationDetail().getWtCalibrationEventId())) {
            subject = subject + " # " + template.getWeightCalibrationDetail().getWtCalibrationEventId();
        }
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] : " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
