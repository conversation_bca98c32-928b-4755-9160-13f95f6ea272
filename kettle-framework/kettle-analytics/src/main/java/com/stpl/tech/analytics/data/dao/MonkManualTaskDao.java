package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.kettle.data.model.MonkManualTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DAO interface for MonkManualTask MySQL operations
 */
@Repository
public interface MonkManualTaskDao extends JpaRepository<MonkManualTask, Integer> {
    List<MonkManualTask> findByUnitId(Integer unitId);
} 