/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.controller;

import com.stpl.tech.analytics.model.chaimonk.ChaiMonkOrder;
import com.stpl.tech.analytics.service.OrderProcessingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;
import static com.stpl.tech.analytics.core.AnalyticsConstants.CHAI_MONK_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + CHAI_MONK_SERVICES_ROOT_CONTEXT)
public class ChaiMonkResources {

    @Autowired
    OrderProcessingService orderService;

    @RequestMapping(method = RequestMethod.POST, value = "sync-orders", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer syncOrders(@RequestBody  List<ChaiMonkOrder> orderDetails) {
        return orderService.syncChaiMonkOrders(orderDetails);
    }

}
