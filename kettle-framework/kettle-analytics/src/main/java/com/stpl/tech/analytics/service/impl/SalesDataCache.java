/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import com.hazelcast.collection.ISet;
import com.hazelcast.collection.ISet;
import com.hazelcast.map.IMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.analytics.core.AnalyticsUtil;
import com.stpl.tech.analytics.core.PenetrationDataHelper;
import com.stpl.tech.analytics.dao.DailyUnitReportDataObjectDao;
import com.stpl.tech.analytics.dao.OrderProcessingDao;
import com.stpl.tech.analytics.dao.SalesTargetRepository;
import com.stpl.tech.analytics.listener.Notifier;
import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.AggregatedReportData;
import com.stpl.tech.analytics.model.DailyUnitReportDataObject;
import com.stpl.tech.analytics.model.ProductReportData;
import com.stpl.tech.analytics.model.SalesTarget;
import com.stpl.tech.analytics.model.UnitReportData;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.domain.model.UnitSubCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;


@Service
public class SalesDataCache {

	private static final Logger LOG = LoggerFactory.getLogger(SalesDataCache.class);

	public static final UnitBasicDetail SYSTEM_UNIT;

	static {
		SYSTEM_UNIT = new UnitBasicDetail();
		SYSTEM_UNIT.setId(AppConstants.SYSTEM_UNIT_ID);
		SYSTEM_UNIT.setName("Chaayos");
		SYSTEM_UNIT.setCategory(UnitCategory.CAFE);
		SYSTEM_UNIT.setStatus(UnitStatus.ACTIVE);
		SYSTEM_UNIT.setCompanyId(1);
	}

	@Autowired
	@Qualifier(value = "AnalyticsHazelCastInstance")
	private HazelcastInstance instance;

	@Autowired
	private SalesTargetRepository targetDao;

	@Autowired
	private OrderProcessingDao orderDao;

	@Autowired
	private DailyUnitReportDataObjectDao dailyDataDao;

	@Autowired
	private AnalyticsProperties props;

	private boolean isLoaded = false;

	@Autowired
	private MasterDataCache masterCache;

	private IMap<Integer, UnitReportData> unitSalesData;

	private ISet<Integer> giftCardOffer;

	private IMap<Integer, AggregatePenetrationData> mtdPenetrationData;

	private IMap<Integer, AggregatePenetrationData> previousMonthPenetrationData;

	private IMap<Integer, ProductReportData> productSalesData;

	private IMap<String, AggregatedReportData> aggregatedSalesData;

	public void createCache(Date businessDate, Date starteTime, Date endTime, boolean sendNotification) {

		LOG.info("$$$$$$$$$$$$$$$Creating Sales Data Cache$$$$$$$$$$$$$$$");
		unitSalesData = instance.getMap("MasterDataCache:unitSalesData");
		mtdPenetrationData = instance.getMap("MasterDataCache:mtdPenetrationData");
		previousMonthPenetrationData = instance.getMap("MasterDataCache:previousMonthPenetrationData");
		productSalesData = instance.getMap("MasterDataCache:productSalesData");
		aggregatedSalesData = instance.getMap("MasterDataCache:aggregatedSalesData");
		giftCardOffer = instance.getSet("MasterDataCache:giftCardOffer");
		if (masterCache.getAllUnits() != null && masterCache.getAllUnits().size() > 0 && !isLoaded) {

			LOG.info("$$$$$$$$$$$$$$$Loading Sales Data Cache for Units$$$$$$$$$$$$$$$");
			isLoaded = true;
			Map<Integer, List<OrderResponse>> orders = getOrders(starteTime, endTime);
			getAggregatedReportData("ALL");
			for (UnitCategory category : UnitCategory.values()) {
				getAggregatedReportData(category);
			}
			for (UnitSubCategory category : UnitSubCategory.values()) {
				getAggregatedReportData(category);
			}
			for (UnitBasicDetail detail : masterCache.getAllUnits()) {
				getUnitReportData(detail);
			}
			getSystemReportData();
			for (ProductBasicDetail product : masterCache.getAllProductsBasicDetail()) {
				getProductReportData(product);
			}
			refreshTargets(businessDate, sendNotification);
			loadGiftCardOffer();
			loadDataForTheDay(orders);
			loadAggregatedPenetrationMTD();
			loadAggregatedPenetrationPreviousMonth();

		}
	}

	/**
	 * @param
	 */
	private void loadGiftCardOffer() {
		List<?> data = AnalyticsUtil.callWebService(List.class, props.getGiftCardUnitsFetchURL(), null);
		if (data != null) {
			for (Object o : data) {
				if (o != null) {
					BigDecimal decimal = new BigDecimal((double) o);
					giftCardOffer.add(decimal.intValue());
				}
			}
		}
	}

	/**
	 *
	 */
	private void loadAggregatedPenetrationPreviousMonth() {
		Date currentDate = AppUtils.getCurrentBusinessDate();
		Date startOfMonth = AppUtils.getStartOfMonth(currentDate.getYear(), currentDate.getMonth());
		Date endOfPreviousMonth = AppUtils.getDayBeforeOrAfterDay(startOfMonth, -1);
		Date startOfPreviousMonth = AppUtils.getStartOfMonth(endOfPreviousMonth.getYear(),
				endOfPreviousMonth.getMonth());
		List<DailyUnitReportDataObject> data = dailyDataDao.findByMonthAndYear(startOfPreviousMonth,
				endOfPreviousMonth);
		Map<Integer, AggregatePenetrationData> penetration = convert(data);
		previousMonthPenetrationData.putAll(penetration);
	}

	/**
	 *
	 */
	private void loadAggregatedPenetrationMTD() {
		Date currentDate = AppUtils.getCurrentBusinessDate();
		Date startOfMonth = AppUtils.getStartOfMonth(currentDate.getYear(), currentDate.getMonth());
		List<DailyUnitReportDataObject> data = dailyDataDao.findByMonthAndYear(startOfMonth, currentDate);
		Map<Integer, AggregatePenetrationData> penetration = convert(data);
		mtdPenetrationData.putAll(penetration);
	}

	/**
	 * @param data
	 * @return
	 */
	private Map<Integer, AggregatePenetrationData> convert(List<DailyUnitReportDataObject> data) {
		Map<Integer, AggregatePenetrationData> map = new HashMap<>();
		for (DailyUnitReportDataObject d : data) {
			int unitId = d.getInstance().getDetail().getId();
			if (!map.containsKey(unitId)) {
				map.put(unitId, d.getInstance().getPenetration());
			} else {
				map.get(unitId).add(d.getInstance().getPenetration());
			}
		}
		return map;
	}

	public void clearCache() {
		LOG.info("$$$$$$$$$$$$$$$Clearing Sales Data  Cache$$$$$$$$$$$$$$$");
		instance.getMap("MasterDataCache:unitSalesData").clear();
		instance.getMap("MasterDataCache:mtdPenetrationData").clear();
		instance.getMap("MasterDataCache:previousMonthPenetrationData").clear();
		instance.getMap("MasterDataCache:productSalesData").clear();
		instance.getMap("MasterDataCache:aggregatedSalesData").clear();
		instance.getMap("MasterDataCache:giftCardOffer").clear();
		isLoaded = false;
	}

	public SalesDataCache() {

	}
	public UnitReportData getSystemReportData() {
		if (!unitSalesData.containsKey(AppConstants.SYSTEM_UNIT_ID)) {
			clearUnitReportData(SYSTEM_UNIT);
		}
		return unitSalesData.get(SYSTEM_UNIT.getId());
	}

	public UnitReportData getUnitReportData(UnitBasicDetail detail) {
		if (detail == null) {
			return null;
		}
		if (!unitSalesData.containsKey(detail.getId())) {
			clearUnitReportData(detail);
		}
		return unitSalesData.get(detail.getId());
	}

	public ProductReportData getProductReportData(ProductBasicDetail detail) {
		if (detail == null) {
			return null;
		}
		if (!productSalesData.containsKey(detail.getDetail().getId())) {
			clearProductReportData(detail);
		}
		return productSalesData.get(detail.getDetail().getId());
	}

	public AggregatedReportData getAggregatedReportData(UnitSubCategory category) {
		if (category == null) {
			return null;
		}
		String key = key(category);
		if (!aggregatedSalesData.containsKey(key)) {
			clearAggregatedReportData(category);
		}
		return aggregatedSalesData.get(key);
	}

	public AggregatedReportData getAggregatedReportData(String category) {
		if (category == null) {
			return null;
		}
		if (!aggregatedSalesData.containsKey(category)) {
			clearAggregatedReportData(category);
		}
		return aggregatedSalesData.get(category);
	}

	public AggregatedReportData getAggregatedReportData(UnitCategory category) {
		if (category == null) {
			return null;
		}
		String key = key(category);
		if (!aggregatedSalesData.containsKey(key)) {
			clearAggregatedReportData(category);
		}
		return aggregatedSalesData.get(key);
	}

	public UnitReportData getUnitReportData(int unitId) {
		UnitReportData data = unitSalesData.get(unitId);
		if (data == null) {
			return getUnitReportData(masterCache.getUnitBasicDetail(unitId));
		}
		return data;
	}

	public void loadCache() {
		if (!isLoaded) {
			reloadCache(AppUtils.getCurrentBusinessDate(), null, null, true);
		}
	}

	public UnitReportData addUnitReportData(UnitReportData detail) {
		unitSalesData.put(detail.getDetail().getId(), detail);
		return unitSalesData.get(detail.getDetail().getId());
	}

	public ProductReportData addProductReportData(ProductReportData detail) {
		productSalesData.put(detail.getDetail().getDetail().getId(), detail);
		return productSalesData.get(detail.getDetail().getDetail().getId());
	}

	public AggregatedReportData addAggregatedReportData(AggregatedReportData detail) {
		aggregatedSalesData.put(detail.getId(), detail);
		return aggregatedSalesData.get(detail.getId());
	}

	public UnitReportData clearUnitReportData(UnitBasicDetail detail) {
		unitSalesData.put(detail.getId(), createUnitReportData(detail));
		return unitSalesData.get(detail.getId());
	}

	public ProductReportData clearProductReportData(ProductBasicDetail detail) {
		productSalesData.put(detail.getDetail().getId(), createProductReportData(detail));
		return productSalesData.get(detail.getDetail().getId());
	}

	public AggregatedReportData clearAggregatedReportData(String detail) {
		aggregatedSalesData.put(detail, createAggregatedReportData(detail));
		return aggregatedSalesData.get(detail);
	}

	public AggregatedReportData clearAggregatedReportData(UnitCategory detail) {
		String key = key(detail);
		aggregatedSalesData.put(key, createAggregatedReportData(key, detail));
		return aggregatedSalesData.get(key);
	}

	public AggregatedReportData clearAggregatedReportData(UnitSubCategory detail) {
		String key = key(detail);
		aggregatedSalesData.put(key, createAggregatedReportData(key, detail));
		return aggregatedSalesData.get(key);
	}

	private String key(UnitSubCategory category) {
		return "UnitSubCategory_" + category.name();
	}

	private String key(UnitCategory category) {
		return "UnitCategory_" + category.name();
	}

	private UnitReportData createUnitReportData(UnitBasicDetail detail) {
		UnitReportData data = new UnitReportData();
		data.setId(detail.getId());
		data.setDetail(clone(detail));
		return data;
	}

	private ProductReportData createProductReportData(ProductBasicDetail detail) {
		ProductReportData data = new ProductReportData();
		data.setId(detail.getDetail().getId());
		data.setDetail(cloneProduct(detail));
		return data;
	}

	private AggregatedReportData createAggregatedReportData(String key, UnitSubCategory category) {
		AggregatedReportData data = new AggregatedReportData();
		data.setId(key);
		data.setDetail(create(category));
		return data;
	}

	private AggregatedReportData createAggregatedReportData(String key) {
		AggregatedReportData data = new AggregatedReportData();
		data.setId(key);
		data.setDetail(create(key));
		return data;
	}

	private IdCodeName create(UnitSubCategory category) {
		IdCodeName idCodeName = new IdCodeName();
		idCodeName.setCode(category.name());
		idCodeName.setName(category.name());
		idCodeName.setId(category.ordinal());
		return idCodeName;
	}

	private IdCodeName create(String category) {
		IdCodeName idCodeName = new IdCodeName();
		idCodeName.setCode(category);
		idCodeName.setName(category);
		idCodeName.setId(-1);
		return idCodeName;
	}

	private AggregatedReportData createAggregatedReportData(String key, UnitCategory category) {
		AggregatedReportData data = new AggregatedReportData();
		data.setId(key);
		data.setDetail(create(category));
		return data;
	}

	private IdCodeName create(UnitCategory category) {
		IdCodeName idCodeName = new IdCodeName();
		idCodeName.setCode(category.name());
		idCodeName.setName(category.name());
		idCodeName.setId(category.ordinal());
		return idCodeName;
	}

	private UnitBasicDetail clone(UnitBasicDetail detail) {
		UnitBasicDetail clone = new UnitBasicDetail();
		clone.setCategory(detail.getCategory());
		clone.setSubCategory(detail.getSubCategory());
		clone.setCity(detail.getCity());
		clone.setId(detail.getId());
		clone.setLatitude(detail.getLatitude());
		clone.setLongitude(detail.getLongitude());
		clone.setName(detail.getName());
		clone.setNoOfTakeawayTerminals(detail.getNoOfTakeawayTerminals());
		clone.setNoOfTerminal(detail.getNoOfTerminal());
		clone.setRegion(detail.getRegion());
		clone.setStatus(detail.getStatus());
		clone.setLive(detail.isLive());
		clone.setWorkStationEnabled(detail.isWorkStationEnabled());
		clone.setCompanyId(detail.getCompanyId());
		return clone;
	}

	private ProductBasicDetail cloneProduct(ProductBasicDetail detail) {
		ProductBasicDetail clone = new ProductBasicDetail();
		clone.setClassification(detail.getClassification());
		clone.setCode(detail.getCode());
		IdCodeName data = new IdCodeName(detail.getDetail().getId(), detail.getDetail().getName(),
				detail.getDetail().getCode(), detail.getDetail().getShortCode(), detail.getDetail().getType(),
				detail.getDetail().getStatus());
		clone.setDetail(data);
		clone.setEmployeeMealComponent(detail.isEmployeeMealComponent());
		clone.setInventoryTracked(detail.isInventoryTracked());
		clone.setStatus(detail.getStatus());
		clone.setSubType(detail.getSubType());
		clone.setType(detail.getType());
		clone.setWebType(detail.getWebType());
		return clone;
	}

	public Collection<UnitReportData> getUnitsReportData() {
		loadCache();
		return unitSalesData.values();
	}

	public Collection<AggregatedReportData> getAggregatedSalesData() {
		loadCache();
		return aggregatedSalesData.values();
	}

	public void reloadCache(Date businessDate, Date startTime, Date endTime, boolean sendNotification) {
		clearCache();
		createCache(businessDate, startTime, endTime, sendNotification);
	}

	public Map<Integer, List<OrderResponse>> getOrders(Date startTime, Date endTime) {
		Date date = startTime == null ? AnalyticsUtil.getStartOfDay() : startTime;
		LOG.info("Processing order since time {}, {} and {} : for all Units ", startTime, date, endTime);

		List<OrderResponse> orders = endTime == null ? orderDao.findByOrderTime(date, OrderStatus.CANCELLED.name())
				: orderDao.findByOrderTimeForARange(date, endTime, OrderStatus.CANCELLED.name());

		Map<Integer, List<OrderResponse>> map = new HashMap<>();
		for (OrderResponse order : orders) {
			if (!map.containsKey(order.getOrder().getUnitId())) {
				map.put(order.getOrder().getUnitId(), new ArrayList<>());
			}
			map.get(order.getOrder().getUnitId()).add(order);
		}
		return map;
	}

	public void refreshTargets(Date businessDate, boolean sendNotification) {
		Notifier notifier = new Notifier(props.publishToPusher());
		List<SalesTarget> oldTargets = targetDao.findByBusinessDate(businessDate);
		if (oldTargets != null && oldTargets.size() > 0) {
			for (SalesTarget target : oldTargets) {
				UnitReportData cacheData = getUnitReportData(target.getUnitId());
				if (cacheData != null) {
					cacheData.getData().updateTargets(target.getData());
					cacheData = addUnitReportData(cacheData);
					LOG.info("Updated Targets for Unit " + cacheData.getDetail().getName() + "\n" + cacheData);
					if (sendNotification) {
						notifier.addNotification(props.getEnvironmentType(), cacheData);
					}
				}
			}
		}
	}

	public void loadDataForTheDay(Map<Integer, List<OrderResponse>> map) {
		for (UnitBasicDetail ubd : masterCache.getAllUnits()) {
			loadDataForTheDay(ubd.getId(),  map.get(ubd.getId()));
		}
	}

	public void loadDataForTheDay(int unitId, List<OrderResponse> orders) {
		if (orders != null && orders.size() > 0) {
			LOG.info("Processing order with total count {}", orders.size());
			for (OrderResponse order : orders) {
				LOG.info("Processing order for the day {} : {}", order.getOrder().getOrderId(),
						order.getOrder().getStatus().name());
				if (!OrderStatus.CANCELLED.equals(order.getOrder().getStatus())
						&& !OrderStatus.CANCELLED_REQUESTED.equals(order.getOrder().getStatus())
						&& !TransactionUtils.isSpecialOrder(order.getOrder())) {
					try {
						addStatsForAll(order);
						addStatsForCategory(order);
						addStatsForSubCategory(order);
						addOrder(order);
						addOrderForProduct(order);
					} catch (Exception e) {
						LOG.error("Error while loading data {}", JSONSerializer.toJSON(order), e);
					}
				}
			}
		}
	}

	public void refreshTargets(Date businessDate) {
		refreshTargets(businessDate, true);
	}

	public AggregatedReportData addStatsForAll(OrderResponse order) {
		if (!isLoaded) {
			loadCache();
		}
		AggregatedReportData data = getAggregatedReportData("ALL");
		data = data.addOrder(order.getOrder());
		return addAggregatedReportData(data);
	}

	public AggregatedReportData addStatsForCategory(OrderResponse order) {
		if (!isLoaded) {
			loadCache();
		}
		AggregatedReportData data = getAggregatedReportData(order.getUnit().getCategory());
		data = data.addOrder(order.getOrder());
		return addAggregatedReportData(data);
	}

	public AggregatedReportData addStatsForSubCategory(OrderResponse order) {
		if (!isLoaded) {
			loadCache();
		}
		AggregatedReportData data2 = getAggregatedReportData(order.getUnit().getSubCategory());
		data2 = data2.addOrder(order.getOrder());
		return addAggregatedReportData(data2);
	}

	public UnitReportData addOrder(OrderResponse order) {
		AggregatePenetrationData p = getPenetrationData(order.getOrder());
		UnitReportData data = getUnitReportData(order.getUnit());
		UnitReportData unitResponse = addOrder(data, order.getOrder(), p);
		UnitReportData system = getUnitReportData(SYSTEM_UNIT);
//		UnitReportData system = getSystemReportData(SYSTEM_UNIT);
		UnitReportData systemResponse = addOrder(system, order.getOrder(), p);
		addUnitReportData(systemResponse);
		return addUnitReportData(unitResponse);
	}


	public AggregatePenetrationData getPenetrationData(Order order) {
		PenetrationDataHelper helper = new PenetrationDataHelper();
		AggregatePenetrationData p = helper.getPenetrations(hasGiftCardOffer(order.getUnitId()), masterCache, order, props);
		return p;
	}

	public UnitReportData addOrder(UnitReportData current, Order order, AggregatePenetrationData p) {
		current.getData().addOrder(order);
		current.getPenetration().add(p);
		return current;
	}

	public List<ProductReportData> addOrderForProduct(OrderResponse order) {
		List<ProductReportData> items = new ArrayList<>();
		if (order.getOrder().getOrderType().equals("order")) {
			for (OrderItem item : order.getOrder().getOrders()) {
				ProductReportData data = getProductReportData(masterCache.getProductBasicDetail(item.getProductId()));
				if (data == null) {
					continue;
				}
				ProductReportData response = data.addOrderItem(UnitCategory.valueOf(order.getOrder().getSource()),
						order.getOrder().getStatus(), item);
				items.add(addProductReportData(response));
			}
		}
		return items;
	}

	public static void main(String[] args) {
		Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("Asia/Kolkata"));
		String currentMonth = AnalyticsUtil.getMonthForInt(cal.get(Calendar.MONTH)).toLowerCase();
		int year = cal.get(Calendar.YEAR);
		System.out.println("Month  : " + currentMonth);
		System.out.println("Year : " + year);
	}

	/**
	 * @param productId
	 * @return
	 */
	public ProductReportData getProductReportData(int productId) {
		return productSalesData.get(productId);
	}

	public IMap<Integer, UnitReportData> getUnitSalesData() {
		return unitSalesData;
	}

	/**
	 * @return
	 */
	public Collection<ProductReportData> getProductsReportData() {
		loadCache();
		return productSalesData.values();
	}

	/**
	 * @param unitId
	 * @return
	 */
	public AggregatePenetrationData getMTDPenetration(int unitId) {
		return mtdPenetrationData.get(unitId);
	}

	/**
	 * @param unitId
	 * @return
	 */
	public AggregatePenetrationData getPreviousMonthPenetration(int unitId) {
		return previousMonthPenetrationData.get(unitId);
	}

	/**
	 * @param unitId
	 * @return
	 */
	public boolean hasGiftCardOffer(int unitId) {
		return giftCardOffer.contains(unitId);
	}
}
