package com.stpl.tech.analytics.dao;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.sales.domain.model.SalesRecord;

@Repository
public interface SalesRecordDao extends MongoRepository<SalesRecord, String> {

	/*
	 * @Query(
	 * "select from OrderResponse where order.billingServerTime >= :billingTime and order.status <> :status"
	 * )
	 */
	@Query("{'billingServerTime' : {'$gte' : ?0}, 'status' : {'$ne' : ?1}  }")
	public List<SalesRecord> findByOrderTime(final Date billingTime, final String status);

	/* @Query("select from OrderResponse where order.orderId = :orderId") */
	@Query("{'orderId' : ?0}")
	public List<SalesRecord> findByOrderId(final int orderId);
}
