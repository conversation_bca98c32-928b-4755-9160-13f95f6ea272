package com.stpl.tech.analytics.cache.impl;

import com.stpl.tech.analytics.cache.AnalyticsCache;
import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.DailyUnitReportDataObject;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class AnalyticsCacheImpl implements AnalyticsCache {


    Map<Integer, AggregatePenetrationData> dailyUnitWiseAggregatePenetrationData;
    Map<Integer, AggregatePenetrationData> monthlyUnitWiseAggregatePenetrationData;

    Map<Integer, List<DailyUnitReportDataObject>> dailyUnitReportData;


    @Override
    public void refreshCache() {
        dailyUnitWiseAggregatePenetrationData = new HashMap<>();
        monthlyUnitWiseAggregatePenetrationData = new HashMap<>();
        dailyUnitReportData = new HashMap<>();
    }

    @Override
    public AggregatePenetrationData getCurrentDailyTargetsFromCache(Integer unitId) {

        if (Objects.nonNull(dailyUnitWiseAggregatePenetrationData) && dailyUnitWiseAggregatePenetrationData.containsKey(unitId)) {
            return dailyUnitWiseAggregatePenetrationData.get(unitId);
        }
        return null;
    }

    @Override
    public void setCurrentDailyTargetsFromCache(Integer unitId, AggregatePenetrationData data) {
        if (Objects.isNull(dailyUnitWiseAggregatePenetrationData)) {
            dailyUnitWiseAggregatePenetrationData = new HashMap<>();
        }
        dailyUnitWiseAggregatePenetrationData.put(unitId, data);
    }

    public AggregatePenetrationData getCurrentMonthlyTargetsFromCache(Integer unitId) {

        if (Objects.nonNull(monthlyUnitWiseAggregatePenetrationData) && monthlyUnitWiseAggregatePenetrationData.containsKey(unitId)) {
            return monthlyUnitWiseAggregatePenetrationData.get(unitId);
        }
        return null;
    }

    @Override
    public void setCurrentMonthlyTargetsFromCache(Integer unitId, AggregatePenetrationData data) {
        if (Objects.isNull(monthlyUnitWiseAggregatePenetrationData)) {
            monthlyUnitWiseAggregatePenetrationData = new HashMap<>();
        }
        monthlyUnitWiseAggregatePenetrationData.put(unitId, data);
    }

    @Override
    public List<DailyUnitReportDataObject> getDailyUnitReportDataObjectFromCache(Integer unitId) {
        if (Objects.nonNull(dailyUnitReportData) && dailyUnitReportData.containsKey(unitId)) {
            return dailyUnitReportData.get(unitId);
        }
        return null;
    }

    @Override
    public void setDailyUnitReportDataObjectFromCache(Integer unitId, List<DailyUnitReportDataObject> data) {
        if (Objects.isNull(dailyUnitReportData)) {
            dailyUnitReportData = new HashMap<>();
        }
        dailyUnitReportData.put(unitId, data);
    }

}
