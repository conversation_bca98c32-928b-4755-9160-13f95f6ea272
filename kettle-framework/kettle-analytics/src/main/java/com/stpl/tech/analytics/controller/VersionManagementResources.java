package com.stpl.tech.analytics.controller;

import com.stpl.tech.analytics.dao.UnitAppVersionMetadata;
import com.stpl.tech.analytics.data.dao.UnitAppVersionMetadataRepository;
import com.stpl.tech.analytics.dto.BuildVersionRequest;
import com.stpl.tech.analytics.dto.UnitDeviceWhitelistDto;
import com.stpl.tech.analytics.service.VersionManagementService;
import com.stpl.tech.kettle.data.model.UnitDeviceWhitelist;
import com.stpl.tech.master.core.service.model.ScreenType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;
import static com.stpl.tech.analytics.core.AnalyticsConstants.SEPARATOR;
import static com.stpl.tech.analytics.core.AnalyticsConstants.VERSION_METADATA_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + VERSION_METADATA_ROOT_CONTEXT)
public class VersionManagementResources {
    @Autowired
    private VersionManagementService versionService;

    // POST API to save version data
    @PostMapping("/save")
    public ResponseEntity<String> saveBuildVersion(@RequestBody BuildVersionRequest request) {
        return ResponseEntity.ok(versionService.saveBuildVersion(request));
    }

    @GetMapping("/{unitId}")
    public ResponseEntity<List<UnitAppVersionMetadata>> getVersionHistory(@PathVariable String unitId) {
        List<UnitAppVersionMetadata> versionList = versionService.getVersionHistory(unitId);
        if (versionList.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(versionList);
    }

    @GetMapping("/latest/{unitId}")
    public ResponseEntity<BuildVersionRequest> getLatestVersions(@PathVariable String unitId) {
        BuildVersionRequest latestVersions = versionService.getLatestVersions(unitId);
        if (latestVersions == null) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(latestVersions);
    }

    // POST API to save whitelist entry
    @PostMapping("/whitelist/save")
    public ResponseEntity<UnitDeviceWhitelistDto> saveWhitelist(@RequestBody UnitDeviceWhitelistDto whitelistDto) {
        return ResponseEntity.ok(versionService.saveUnitDeviceWhitelist(whitelistDto));
    }

    // GET API to get whitelist entries by unitId
    @GetMapping("/whitelist/unit/{unitId}")
    public ResponseEntity<List<UnitDeviceWhitelistDto>> getWhitelistByUnitId(@PathVariable String unitId) {
        List<UnitDeviceWhitelistDto> list = versionService.getUnitDeviceWhitelistByUnitId(unitId);
        if (list == null || list.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(list);
    }

    // GET API to get whitelist entry by mac address
    @GetMapping("/whitelist/mac/{macAddress}")
    public ResponseEntity<UnitDeviceWhitelistDto> getWhitelistByMac(@PathVariable String macAddress) {
        UnitDeviceWhitelistDto entry = versionService.getUnitDeviceWhitelistByMac(macAddress);
        if (entry == null) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.ok(entry);
    }
}
