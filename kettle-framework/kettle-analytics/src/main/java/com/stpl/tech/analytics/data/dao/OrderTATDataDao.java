package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.kettle.data.model.MonkOrderDetail;
import com.stpl.tech.kettle.data.model.UnitOrderTATDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface OrderTATDataDao extends JpaRepository<UnitOrderTATDetail, Integer> {

    public List<UnitOrderTATDetail> findByUnitIdAndBusinessDate(Integer unitId, Date businessDate);

    public List<UnitOrderTATDetail> findByUnitIdAndBrandIdAndBusinessDate(Integer unitId, Integer brandId,Date businessDate);

    public UnitOrderTATDetail findByUnitIdAndBrandIdAndTypeAndBusinessDate(Integer unitId, Integer brandId, String type,Date businessDate);

}
