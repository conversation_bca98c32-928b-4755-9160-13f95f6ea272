package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.kettle.data.model.UnitDeviceWhitelist;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UnitDeviceWhitelistRepository extends JpaRepository<UnitDeviceWhitelist, Integer> {
    List<UnitDeviceWhitelist> findByUnitId(String unitId);
    UnitDeviceWhitelist findByMacAddress(String macAddress);
} 