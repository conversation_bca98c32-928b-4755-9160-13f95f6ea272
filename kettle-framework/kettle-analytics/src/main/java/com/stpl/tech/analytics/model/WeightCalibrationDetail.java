package com.stpl.tech.analytics.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stpl.tech.util.AppConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class WeightCalibrationDetail {
    Integer wtCalibrationEventId;
    int unitId;
    String unitName;
    int userId;
    String startedby;
    String userIdName;
    String monkName;
    String knownWeight;
    String prevWtCalValue;
    String prevWtCalTime;
    String measuredWeight;
    String measurementTime;
    String className;

}
