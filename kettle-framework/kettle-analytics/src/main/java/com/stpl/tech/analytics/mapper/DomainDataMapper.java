package com.stpl.tech.analytics.mapper;

import com.stpl.tech.analytics.dto.UnitDeviceWhitelistDto;
import com.stpl.tech.kettle.data.model.UnitDeviceWhitelist;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DomainDataMapper {
    DomainDataMapper INSTANCE = Mappers.getMapper(DomainDataMapper.class);

    UnitDeviceWhitelist toUnitDeviceWhiteList(UnitDeviceWhitelistDto unitDeviceWhiteListDto);
    UnitDeviceWhitelistDto toUnitDeviceWhiteListDto(UnitDeviceWhitelist unitDeviceWhiteList);
    List<UnitDeviceWhitelist> toUnitDeviceWhiteListList(List<UnitDeviceWhitelistDto> unitDeviceWhiteListDtos);
    List<UnitDeviceWhitelistDto> toUnitDeviceWhiteListDtoList(List<UnitDeviceWhitelist> unitDeviceWhiteLists);
}
