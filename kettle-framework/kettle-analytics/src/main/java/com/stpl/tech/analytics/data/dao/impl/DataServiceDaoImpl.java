/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.analytics.data.dao.DataServiceDao;
import com.stpl.tech.analytics.model.PercentageIntegerData;
import com.stpl.tech.analytics.model.SalesSplitData;
import com.stpl.tech.analytics.model.UnitData;
import com.stpl.tech.analytics.model.UnitSaleData;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.UploadedTargetData;
import com.stpl.tech.kettle.data.model.UnitTargetDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Repository
public class DataServiceDaoImpl extends AbstractDaoImpl implements DataServiceDao {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.kettle.data.dao.DataServiceDao#changeStatus(java.util.Date,
	 * java.util.Date, java.lang.String, java.util.Collection)
	 */
	@Override
	public void markTargetAsInactive(Date fromDate, Date toDate, Collection<Integer> unitIds) {
		Query query = manager.createQuery("update UnitTargetDetail set status = :status"
				+ " where businessDate >= :fromDate and businessDate <=  :toDate and unitId IN (:unitIds)"
				+ " and status = :active");
		query.setParameter("unitIds", new ArrayList<>(unitIds));
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("fromDate", fromDate);
		query.setParameter("toDate", toDate);
		query.setParameter("active", AppConstants.ACTIVE);
		query.executeUpdate();
		manager.flush();
	}

	@Override
	public void markAllTargetForMonthAsInactive(int month, int year) {
		Query query = manager.createQuery("update UploadedTargetData set status = :status"
				+ " where month = :month and year = :year and status = :active");
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("month", month);
		query.setParameter("year", year);
		query.setParameter("active", AppConstants.ACTIVE);
		query.executeUpdate();
		manager.flush();
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.kettle.data.dao.DataServiceDao#addAll(java.util.List)
	 */
	@Override
	public void addAllUnitTargets(List<UnitTargetDetail> details) {
		for (UnitTargetDetail detail : details) {
			manager.persist(detail);
		}
		manager.flush();
	}

	@Override
	public void addMonthlyTargets(List<UploadedTargetData> entityList) {
		for (UploadedTargetData entity : entityList) {
			entity.setStatus(AppConstants.ACTIVE);
			manager.persist(entity);
		}
		manager.flush();
	}

	@Override
	public UploadedTargetData getCurrentMonthTargetsForUnit(int unitId, int month, int year) {
		Query query = manager.createQuery(" FROM UploadedTargetData WHERE unitId = :unitId and month = :month "
				+ "  and year = :year  and status = :active and targetType = :targetType");
		query.setParameter("unitId", unitId);
		query.setParameter("month", month);
		query.setParameter("year", year);
		query.setParameter("targetType", "MONTHLY");
		query.setParameter("active", AppConstants.ACTIVE);
		List<UploadedTargetData> targets = query.getResultList();
		if (targets != null && targets.size() == 1) {
			return targets.get(0);
		}
		return null;
	}

	@Override
	public UploadedTargetData getCurrentDailyTargetsForUnit(int unitId, Date businessDate) {
		Query query = manager.createQuery(" FROM UploadedTargetData WHERE unitId = :unitId and businessDate = :businessDate "
				+ "  and status = :active  and targetType = :targetType");
		query.setParameter("unitId", unitId);
		query.setParameter("businessDate", businessDate);
		query.setParameter("targetType", "DAILY");
		query.setParameter("active", AppConstants.ACTIVE);
		List<UploadedTargetData> targets = query.getResultList();
		if (targets != null && targets.size() == 1) {
			return targets.get(0);
		}
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.analytics.data.dao.DataServiceDao#setTargetData(com.stpl.
	 * tech.analytics.model.CumulativeData, java.util.Date, java.util.Date)
	 */
	@Override
	public SalesSplitData getTargetData(Date startOfMonth, Date businessDate) {
		Query query = manager.createQuery("select sum(netSales),sum(netDeliverySales),sum(netDineInSales),"
				+ "sum(netTickets),sum(netDeliveryTickets),sum(netDineInTickets) , count(distinct unitId) from"
				+ " UnitTargetDetail " + " where businessDate >= :fromDate and businessDate <= :toDate "
				+ " and status = :active");
		query.setParameter("fromDate", startOfMonth);
		query.setParameter("toDate", businessDate);
		query.setParameter("active", AppConstants.ACTIVE);
		List<Object[]> targets = query.getResultList();
		if (targets != null && targets.size() == 1) {
			Object[] target = targets.get(0);
			return convert(target);
		}
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.analytics.data.dao.DataServiceDao#setTargetData(com.stpl.
	 * tech.analytics.model.CumulativeData, java.util.Date, java.util.Date)
	 */
	@Override
	public List<UnitData> getAllUnitsTargetData(Date startOfMonth, Date businessDate) {
		Query query = manager.createQuery("select sum(netSales),sum(netDeliverySales),sum(netDineInSales),"
				+ "sum(netTickets),sum(netDeliveryTickets),sum(netDineInTickets) , unitId from" + " UnitTargetDetail "
				+ " where businessDate >= :fromDate and businessDate <= :toDate "
				+ " and status = :active group by unitId");
		query.setParameter("fromDate", startOfMonth);
		query.setParameter("toDate", businessDate);
		query.setParameter("active", AppConstants.ACTIVE);
		List<UnitData> unitDatas = new ArrayList<>();
		List<Object[]> targets = query.getResultList();
		if (targets != null && targets.size() > 0) {
			for (Object[] target : targets) {
				unitDatas.add(convertToUnitData(target));
			}
		}
		return unitDatas;
	}

	@Override
	public PercentageIntegerData getPercentageAchievement(BigDecimal percentage, Date startOfMonth,
			Date lastDayOfMonth) {
		PercentageIntegerData data = new PercentageIntegerData();
		data.setPercentage(percentage);

		Query query = manager.createQuery("select sd.unitId, sum(sd.netSales), sum(td.netSales) from "
				+ " UnitSaleDetail sd, UnitTargetDetail td " + " where td.businessDate = sd.businessDate and "
				+ "td.unitId = sd.unitId and sd.businessDate >= :fromDate and sd.businessDate <= :toDate"
				+ " and td.businessDate >= :fromDate and td.businessDate <= :toDate and td.status = :active and td.netSales > 0"
				+ " group by sd.unitId");
		query.setParameter("fromDate", startOfMonth);
		query.setParameter("active", AppConstants.ACTIVE);
		query.setParameter("toDate", lastDayOfMonth);
		List<Object[]> targets = query.getResultList();
		if (targets != null && targets.size() > 0) {
			for (Object[] target : targets) {
				BigDecimal sale = convertToBigDecimal(target[1]);
				BigDecimal t = convertToBigDecimal(target[2]);
				if (sale.compareTo(t) >= 0) {
					data.setBeyond(data.getBeyond() + 1);
				} else if (sale.compareTo(t) < 0 && sale.compareTo(AppUtils.multiply(t, percentage)) >= 0) {
					data.setAbove(data.getAbove() + 1);
				} else if (sale.compareTo(t) < 0 && sale.compareTo(AppUtils.multiply(t, percentage)) < 0) {
					data.setBelow(data.getBelow() + 1);
				}
			}
		}
		return data;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.analytics.data.dao.DataServiceDao#getSalesData(java.util.
	 * Date, java.util.Date)
	 */
	@Override
	public SalesSplitData getSalesData(Date startOfMonth, Date businessDate) {

		Query query1 = manager.createQuery("select distinct unitId from" + " UnitTargetDetail "
				+ " where businessDate >= :fromDate and businessDate <= :toDate " + " and status = :active");
		query1.setParameter("fromDate", startOfMonth);
		query1.setParameter("toDate", businessDate);
		query1.setParameter("active", AppConstants.ACTIVE);
		List<Integer> unitIds = query1.getResultList();
		if (unitIds != null && unitIds.size() > 0) {
			Query query = manager.createQuery("select sum(netSales),sum(netDeliverySales),sum(netDineInSales),"
					+ "sum(netTickets),sum(netDeliveryTickets),sum(netDineInTickets), count(distinct unitId) from"
					+ " UnitSaleDetail "
					+ " where businessDate >= :fromDate and businessDate <= :toDate and (unitName like :odc or unitId IN (:unitIds))");
			query.setParameter("fromDate", startOfMonth);
			query.setParameter("toDate", businessDate);
			query.setParameter("unitIds", unitIds);
			query.setParameter("odc", "%ODC%");
			List<Object[]> targets = query.getResultList();
			if (targets != null && targets.size() == 1) {
				Object[] target = targets.get(0);
				return convert(target);
			}
		}
		return null;
	}

	private SalesSplitData convert(Object[] target) {
		SalesSplitData sales = new SalesSplitData();
		UnitSaleData total = new UnitSaleData();
		UnitSaleData dineIn = new UnitSaleData();
		UnitSaleData delivery = new UnitSaleData();
		total.setSales(convertToBigDecimal(target[0]));
		delivery.setSales(convertToBigDecimal(target[1]));
		dineIn.setSales(convertToBigDecimal(target[2]));
		total.setTicket(convertToInteger(target[3]));
		delivery.setTicket(convertToInteger(target[4]));
		dineIn.setTicket(convertToInteger(target[5]));
		total.setApc(AppUtils.divide(total.getSales(), convertToBigDecimal(total.getTicket())));
		dineIn.setApc(AppUtils.divide(dineIn.getSales(), convertToBigDecimal(dineIn.getTicket())));
		delivery.setApc(AppUtils.divide(delivery.getSales(), convertToBigDecimal(delivery.getTicket())));
		sales.setCount(convertToInteger(target[6]));
		sales.setDelivery(delivery);
		sales.setDineIn(dineIn);
		sales.setTotal(total);
		return sales;
	}

	private BigDecimal convertToBigDecimal(Object target) {
		return target == null ? BigDecimal.ZERO : new BigDecimal(target.toString());
	}

	private Integer convertToInteger(Object target) {
		return target == null ? 0 : new Integer(target.toString());
	}

	private UnitData convertToUnitData(Object[] target) {
		SalesSplitData sales = new SalesSplitData();
		UnitSaleData total = new UnitSaleData();
		UnitSaleData dineIn = new UnitSaleData();
		UnitSaleData delivery = new UnitSaleData();
		total.setSales(convertToBigDecimal(target[0]));
		delivery.setSales(convertToBigDecimal(target[1]));
		dineIn.setSales(convertToBigDecimal(target[2]));
		total.setTicket(convertToInteger(target[3]));
		delivery.setTicket(convertToInteger(target[4]));
		dineIn.setTicket(convertToInteger(target[5]));
		total.setApc(AppUtils.divide(total.getSales(), convertToBigDecimal(total.getTicket())));
		dineIn.setApc(AppUtils.divide(dineIn.getSales(), convertToBigDecimal(dineIn.getTicket())));
		delivery.setApc(AppUtils.divide(delivery.getSales(), convertToBigDecimal(delivery.getTicket())));
		sales.setDelivery(delivery);
		sales.setDineIn(dineIn);
		sales.setTotal(total);
		UnitData unitData = new UnitData();
		unitData.setSales(sales);
		unitData.setUnitId(convertToInteger(target[6]));
		return unitData;
	}

}