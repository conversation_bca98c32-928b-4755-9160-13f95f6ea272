package com.stpl.tech.analytics.model;

import java.util.List;

public class TotalPenetrationData {
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public TotalPenetrationData() {
    }

    public TotalPenetrationData(String type, List<PosAnalytics> metrics, List<PosAnalytics> penetration) {

        this.type = type;
        this.metrics = metrics;
        this.penetration = penetration;
    }

    public List<PosAnalytics> getMetrics() {
        return metrics;
    }

    public void setMetrics(List<PosAnalytics> metrics) {
        this.metrics = metrics;
    }

    public List<PosAnalytics> getPenetration() {
        return penetration;
    }

    public void setPenetration(List<PosAnalytics> penetration) {
        this.penetration = penetration;
    }

    public String type;
    public List<PosAnalytics> metrics;
    public List<PosAnalytics> penetration;

}
