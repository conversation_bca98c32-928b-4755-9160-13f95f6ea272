package com.stpl.tech.analytics.model;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

public enum CategoryType {
	MERCHANDISE_SALES("Merchandise Sales", true, true, 1), 
	BAKERY_PER_TICKET("Bakery Per Ticket", true, false, 2),
	COLD_PER_TICKET("Cold Per Ticket", true, false, 3), 
	FOOD_PER_TICKET("Food Per Ticket", true, false, 4),
	REGULAR_TO_FULL_RATIO("Regular to Full Ratio", false, false, 5),
	MEALS_PER_TICKET("Meals Per Ticket", true, false, 6), 
	BEVERAGE_ONLY_TICKETS("Beverage Only Tickets", true, true, 7),
	SEASONAL_PRODUCT_ONE("Seasonal Product One", true, true, 8),
	SEASONAL_PRODUCT_TWO("Seasonal Product Two", true, true, 9),
	SEASONAL_PRODUCT_THREE("Seasonal Product Three", true, true, 10);

	public static void main(String[] args) {
		for(CategoryType type: visibleCategory) {
			System.out.println(type.name());
		}
	}
	private final String category;
	private final Boolean trend;
	private final Boolean visible;
	private final Integer index;
	
	private static final Collection<CategoryType> visibleCategory;
	
	static {
		Map<Integer, CategoryType> map = new TreeMap<>();
		for (CategoryType type : values()) {
			if (type.visible) {
				map.put(type.index, type);
			}
		}
		visibleCategory = map.values();
	}

	CategoryType(String category, boolean trend, Boolean visible, Integer index) {
		this.category = category;
		this.trend = trend;
		this.visible = visible;
		this.index = index;
	}

	public String getCategory() {
		return category;
	}

	public Boolean getTrend() {
		return trend;
	}

	public Boolean getVisible() {
		return visible;
	}

	public Integer getIndex() {
		return index;
	}

	public static Collection<CategoryType> getVisibleCategory() {
		return visibleCategory;
	}

	public static BigDecimal getCategorySpecificResult(AggregatePenetrationData sales, CategoryType categoryType) {
		switch (categoryType) {
		case MERCHANDISE_SALES:
			return (calculatePercentage(sales.getMerchandiseSales(), sales.getNetSale()));
		case BAKERY_PER_TICKET:
			return (calculatePercentage(new BigDecimal(sales.getCakes()), new BigDecimal(sales.getDineIn())));
		case COLD_PER_TICKET:
			return (calculatePercentage(new BigDecimal(sales.getCold()), new BigDecimal(sales.getDineIn())));
		case FOOD_PER_TICKET:
			return (calculatePercentage(new BigDecimal(sales.getFood()), new BigDecimal(sales.getDineIn())));
		case REGULAR_TO_FULL_RATIO:
			return (calculatePercentage(new BigDecimal(sales.getFull()), new BigDecimal(sales.getRegular())));
		case MEALS_PER_TICKET:
			return (calculatePercentage(new BigDecimal(sales.getMeals()), new BigDecimal(sales.getDineIn())));
		case BEVERAGE_ONLY_TICKETS:
			return (calculatePercentage(new BigDecimal(sales.getBeverage()), new BigDecimal(sales.getDineIn())));
		case SEASONAL_PRODUCT_ONE:
			return (calculatePercentage(new BigDecimal(sales.getSeasonalProductOne()),
					new BigDecimal(sales.getDineIn())));
		case SEASONAL_PRODUCT_TWO:
			return (calculatePercentage(new BigDecimal(sales.getSeasonalProductTwo()),
					new BigDecimal(sales.getDineIn())));
		case SEASONAL_PRODUCT_THREE:
			return (calculatePercentage(new BigDecimal(sales.getSeasonalProductThree()),
					new BigDecimal(sales.getDineIn())));
		}
		return null;
	}

	private static BigDecimal calculatePercentage(BigDecimal num1, BigDecimal num2) {
		if ((num1 == null) || (num2 == null) || num2.compareTo(BigDecimal.ZERO) == 0  || num1.compareTo(BigDecimal.ZERO) == 0) {
			BigDecimal bd = new BigDecimal(0);
			return bd;
		} else {
			BigDecimal value = new BigDecimal(
					Float.toString((Float.parseFloat(num1.toString()) / Float.parseFloat(num2.toString()) * 100)));
			return value;
		}
	}
}
