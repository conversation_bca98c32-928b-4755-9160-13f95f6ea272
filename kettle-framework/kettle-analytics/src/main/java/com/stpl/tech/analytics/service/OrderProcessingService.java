package com.stpl.tech.analytics.service;

import com.stpl.tech.analytics.model.chaimonk.ChaiMonkOrder;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import org.springframework.stereotype.Service;

import java.util.List;

public interface OrderProcessingService {
	
	public OrderResponse addOrder(OrderResponse order);

	public List<OrderResponse> findByOrderId(int orderId);

	public void delete(OrderResponse order);

	public Integer syncChaiMonkOrders(List<ChaiMonkOrder> orderDetails);
}
