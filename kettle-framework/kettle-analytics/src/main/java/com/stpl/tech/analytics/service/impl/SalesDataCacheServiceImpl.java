package com.stpl.tech.analytics.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.analytics.dao.DailyUnitReportDataObjectDao;
import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.AggregatedReportData;
import com.stpl.tech.analytics.model.DailyUnitReportDataObject;
import com.stpl.tech.analytics.model.UnitReportData;
import com.stpl.tech.analytics.service.SalesDataCacheService;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import com.stpl.tech.util.AppUtils;

@Service
public class SalesDataCacheServiceImpl implements SalesDataCacheService {

	@Autowired
	private SalesDataCache salesCache;

	@Autowired
	private DailyUnitReportDataObjectDao dailyDao;

	@PostConstruct
	public void init() {
		reloadCache(AppUtils.getBusinessDate(), null, null, false);
	}

	@Override
	public void reloadCache(Date businessDate, Date starteTime, Date endTime, boolean sendNotification) {
		salesCache.reloadCache(businessDate, starteTime, endTime, sendNotification);
	}

	@Override
	public void saveDaily(Date businessDate) {
		Map<Integer, UnitReportData> all = salesCache.getUnitSalesData();
		List<DailyUnitReportDataObject> allExisting = dailyDao.findAllByBusinessDate(businessDate);
		if (allExisting != null && allExisting.size() > 0) {
			for (DailyUnitReportDataObject entity : allExisting) {
				dailyDao.delete(entity);
			}
		}
		if (all != null) {
			for (Integer key : all.keySet()) {
				UnitReportData data = all.get(key);
				data.set_id(null);
				DailyUnitReportDataObject obj = new DailyUnitReportDataObject();
				obj.setInstance(data);
				obj.setTime(AppUtils.getCurrentTimestamp());
				obj.setDate(businessDate);
				dailyDao.save(obj);
			}
		}

	}

	@Override
	public UnitReportData addOrder(OrderResponse order) {
		return salesCache.addOrder(order);
	}

	@Override
	public AggregatedReportData addStatsForAll(OrderResponse order) {
		return salesCache.addStatsForAll(order);
	}

	@Override
	public AggregatedReportData addStatsForCategory(OrderResponse order) {
		return salesCache.addStatsForCategory(order);
	}

	@Override
	public AggregatedReportData addStatsForSubCategory(OrderResponse order) {
		return salesCache.addStatsForSubCategory(order);
	}

	@Override
	public UnitReportData getSalesData(int unitId) {
		return salesCache.getUnitReportData(unitId);
	}

	@Override
	public Set<UnitReportData> getSalesData() {
		return new TreeSet<>(salesCache.getUnitsReportData());
	}

	@Override
	public void refreshTargets(Date businessDate, boolean sendNotification) {
		salesCache.refreshTargets(businessDate);
	}

	@Override
	public void loadDataForTheDay(Date starteTime, Date endTime) {
		Map<Integer, List<OrderResponse>> orders = salesCache.getOrders(starteTime, endTime);
		salesCache.loadDataForTheDay(orders);
	}

	@Override
	public void refreshTargets(Date businessDate) {
		refreshTargets(businessDate, true);
	}

	@Override
	public Set<AggregatedReportData> getAggregatedSalesData() {
		return new TreeSet<>(salesCache.getAggregatedSalesData());
	}

	@Override
	public AggregatedReportData getAllAggregatedSalesData() {
		return salesCache.getAggregatedReportData("ALL");
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.analytics.service.SalesDataCacheService#getMTDPenetration(
	 * int)
	 */
	@Override
	public AggregatePenetrationData getMTDPenetration(int unitId) {
		return salesCache.getMTDPenetration(unitId);
	}

	@Override
	public AggregatePenetrationData getPreviousMonthPenetration(int unitId) {
		return salesCache.getPreviousMonthPenetration(unitId);
	}

	@Override
	public UnitReportData getSystemSalesData() {
		return salesCache.getSystemReportData();
	}
}
