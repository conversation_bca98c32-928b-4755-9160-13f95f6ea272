/**
 * 
 */
package com.stpl.tech.analytics.service;

import java.sql.SQLException;
import java.util.List;

import com.stpl.tech.kettle.sales.domain.model.CustomerData;
import com.stpl.tech.util.ExecutionEnvironment;

/**
 * <AUTHOR>
 *
 */
public interface CustomerDataLoaderService {
	public List<CustomerData> loadCustomerData(ExecutionEnvironment env, int startCustomerId, int endCustomerId)
			throws SQLException;
}
