/*
 * Created By Shanmukh
 */

package com.stpl.tech.analytics.notification;

import com.stpl.tech.analytics.notification.template.MonkDiagnosisEmailNotificationTemplate;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class MonkDiagnosisEmailNotification extends EmailNotification {

    MonkDiagnosisEmailNotificationTemplate template;

    EnvType envType;

    List<String> unitEmails;

    public MonkDiagnosisEmailNotification(MonkDiagnosisEmailNotificationTemplate template, EnvType envType, List<String> unitEmails) {
        this.template = template;
        this.envType = envType;
        this.unitEmails = unitEmails;
    }

    @Override
    public String[] getToEmails() {
        if (AppUtils.isDev(envType)) {
            return new String[]{"<EMAIL>"};
        } else {
            unitEmails.add("<EMAIL>");
            unitEmails.add("<EMAIL>");
            unitEmails.add("<EMAIL>");
            unitEmails.add("<EMAIL>");
            String[] simpleArray = new String[unitEmails.size()];
            return unitEmails.toArray(simpleArray);
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Monk Diagnosis " + AppUtils.getFormattedDate(AppUtils.getCurrentDateIST());
        if (Objects.nonNull(template.getMonkDiagnosisLogData().getUnitName())) {
            subject = subject + " Of " + template.getMonkDiagnosisLogData().getUnitName() + " [ " + template.getMonkDiagnosisLogData().getUnitId() + " ] ";
        }
        if (Objects.nonNull(template.getMonkDiagnosisLogData().getMonkDiagnosisEventId())) {
            subject = subject + " # " + template.getMonkDiagnosisLogData().getMonkDiagnosisEventId();
        }
        if (AppUtils.isDev(envType)) {
            subject = "[Dev] : " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
