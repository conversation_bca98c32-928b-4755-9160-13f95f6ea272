/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.mapper;

import java.math.BigDecimal;

import org.apache.poi.ss.usermodel.Cell;

import com.stpl.tech.analytics.model.UnitData;
import com.stpl.tech.kettle.core.file.load.AbstractRowMapper;
import com.stpl.tech.util.AppUtils;

public class SalesProjectionDataMapper extends AbstractRowMapper<UnitData, Cell> {

	@Override
	public void setData(UnitData expense, Cell nextCell) {
		try {
			int columnIndex = nextCell.getColumnIndex();
			switch (columnIndex) {
			// 1 Unit Id
			case 0:
				expense.setUnitId(((Double) getCellValue(nextCell)).intValue());
				break;
			// 3 Target Ticket
			case 1:
				expense.setUnitName((String) getCellValue(nextCell));
				break;
			case 2:
				expense.getSales().getTotal().setTicket(((Double) getCellValue(nextCell)).intValue());
				break;
			case 3:
				expense.getSales().getTotal().setSales(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			case 4:
				expense.getSales().getTotal()
						.setApc(new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			case 5:
				expense.getSales().getDelivery().setTicket(((Double) getCellValue(nextCell)).intValue());
				break;
			case 6:
				expense.getSales().getDelivery().setSales(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			case 7:
				expense.getSales().getDelivery()
						.setApc(new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;

			default:
				break;
			}

			expense.getSales().getDineIn().setTicket(
					expense.getSales().getTotal().getTicket() - expense.getSales().getDelivery().getTicket());
			expense.getSales().getDineIn().setSales(AppUtils.subtract(expense.getSales().getTotal().getSales(),
					expense.getSales().getDelivery().getSales()));
			expense.getSales().getDineIn().setApc(AppUtils.divide(expense.getSales().getDineIn().getSales(),
					new BigDecimal(expense.getSales().getDineIn().getTicket())));
		} catch (Exception e) {
			addError(nextCell, e.getMessage());
		}
	}

	@Override
	public UnitData createNewInstance() {
		return new UnitData();
	}

}
