package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.kettle.data.model.WeightCalibrationDetailData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public interface WeightCalibrationDetailDao extends JpaRepository<WeightCalibrationDetailData,Integer> {

    @Query(value = "SELECT w1.WT_CALIBRATION_ID, w1.UNIT_ID, w1.USER_ID, w1.USER_NAME, w1.MONK_NAME, w1.KNOWN_WEIGHT, w1.MEASURED_WEIGHT, w1.MEASUREMENT_TIME FROM WEIGHT_CALIBRATION_DETAIL w1 JOIN ( SELECT UNIT_ID, MONK_NAME, MAX(MEASUREMENT_TIME) AS MAX_MEASUREMENT_TIME FROM WEIGHT_CALIBRATION_DETAIL WHERE UNIT_ID = :unitId GROUP BY UNIT_ID, MONK_NAME ) w2 ON w1.UNIT_ID = w2.UNIT_ID AND w1.MONK_NAME = w2.MONK_NAME AND w1.MEASUREMENT_TIME = w2.MAX_MEASUREMENT_TIME",nativeQuery = true)
    List<WeightCalibrationDetailData> getWtCalibrationValue(@Param("unitId") Integer unitId);

}
