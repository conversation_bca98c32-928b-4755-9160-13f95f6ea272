package com.stpl.tech.analytics.cache;

import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.DailyUnitReportDataObject;

import java.util.List;

public interface AnalyticsCache {

    public void refreshCache();

    public AggregatePenetrationData getCurrentDailyTargetsFromCache(Integer unitId);

    public void setCurrentDailyTargetsFromCache(Integer unitId, AggregatePenetrationData data);

    public AggregatePenetrationData getCurrentMonthlyTargetsFromCache(Integer unitId);

    public void setCurrentMonthlyTargetsFromCache(Integer unitId, AggregatePenetrationData data);

    public List<DailyUnitReportDataObject> getDailyUnitReportDataObjectFromCache(Integer unitId);

    public void setDailyUnitReportDataObjectFromCache(Integer unitId, List<DailyUnitReportDataObject> data);
}
