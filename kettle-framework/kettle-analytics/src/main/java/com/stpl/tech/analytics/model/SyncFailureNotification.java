/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.model;

import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.util.AppUtils;

import java.text.SimpleDateFormat;

public class SyncFailureNotification implements Notification {

    private String subject;
    private String message;

    public SyncFailureNotification(String subject, String message) {
        this.subject = subject;
        this.message = message;
    }

    @Override
    public String getNotificationMessage() {
        StringBuffer body = new StringBuffer("Sync Error Notification : " + subject + "\nError Generation Timestamp: "
                + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(AppUtils.getCurrentTimestamp()));
        body.append("\nError Summary: " + message);
        return body.toString();
    }
}
