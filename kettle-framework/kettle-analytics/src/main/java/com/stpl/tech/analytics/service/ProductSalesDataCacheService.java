package com.stpl.tech.analytics.service;

import java.util.List;
import java.util.Set;

import com.stpl.tech.analytics.model.ProductReportData;
import com.stpl.tech.kettle.domain.model.OrderResponse;

public interface ProductSalesDataCacheService {

	public List<ProductReportData> addOrder(OrderResponse order);
	
	public ProductReportData getSalesData(int productId);
	
	public Set<ProductReportData> getSalesData();

}
