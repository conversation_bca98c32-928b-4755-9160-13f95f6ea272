package com.stpl.tech.analytics.listener;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.MessageProducer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.google.common.base.Stopwatch;
import com.stpl.tech.analytics.model.AggregatedReportData;
import com.stpl.tech.analytics.model.ProductReportData;
import com.stpl.tech.analytics.model.UnitReportData;
import com.stpl.tech.analytics.model.UnitReportDataObject;
import com.stpl.tech.analytics.service.AggregateDataManagementService;
import com.stpl.tech.analytics.service.OrderProcessingService;
import com.stpl.tech.analytics.service.ProductSalesDataCacheService;
import com.stpl.tech.analytics.service.SalesDataCacheService;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.util.EnvType;

public class OrderMessageListener implements MessageListener {

	private static final Logger LOG = LoggerFactory.getLogger(OrderMessageListener.class);

	private OrderProcessingService service;

	private SalesDataCacheService cacheService;

	private ProductSalesDataCacheService productCacheService;

	private MessageProducer errorQueue;

	private AggregateDataManagementService aggregateService;

	private Notifier notifier;

	private EnvType env;

	private Set<Integer> trackedProducts;

	private Map<Integer, OrderResponse> cancelledOrder = new HashMap<>();

	public OrderMessageListener(EnvType env, MessageProducer errorQueue, OrderProcessingService service,
			SalesDataCacheService cacheService, ProductSalesDataCacheService productCacheService,
			Set<Integer> trackedProducts, boolean sendNotification, AggregateDataManagementService aggregateService) {
		this.env = env;
		this.errorQueue = errorQueue;
		this.service = service;
		this.cacheService = cacheService;
		this.productCacheService = productCacheService;
		this.aggregateService = aggregateService;
		this.trackedProducts = trackedProducts;
		this.notifier = new Notifier(sendNotification);
	}

	@Override
	public void onMessage(Message message) {
		try {
			LOG.info("On Message " + message.getJMSMessageID());
			if (message instanceof SQSObjectMessage) {
				SQSObjectMessage object = (SQSObjectMessage) message;
				if (object.getObject() instanceof OrderResponse) {
					message.acknowledge();
					OrderResponse response = (OrderResponse) object.getObject();
					processMessage(response);
					message.acknowledge();
				}
			}
		} catch (JMSException e) {
			LOG.error("Error while saving the message", e);
			try {
				LOG.info("Publishing Error Message to Error Queue " + message.getJMSMessageID());
				errorQueue.send(message);
			} catch (JMSException e1) {
				LOG.error("Error while saving the message to error queue", e);
			}
			return;
		}
	}

	private void processMessage(OrderResponse response) {
		LOG.info("Got Message " + response.getOrder().getOrderId() + " status : " + response.getOrder().getStatus());
		Stopwatch watch = Stopwatch.createUnstarted();
		List<OrderResponse> orders = service.findByOrderId(response.getOrder().getOrderId());
		if (orders != null && orders.size() > 0) {
			OrderResponse order = orders.get(0);
			if (order.getOrder().getStatus().equals(response.getOrder().getStatus())) {
				LOG.error("Order has already been processed " + response.getOrder().getOrderId());
			} else {
				LOG.error("Order status is changed " + response.getOrder().getOrderId());
				watch.start();
				service.delete(order);
				saveAndNotify(response);
				System.out.println("########## , STEP 0, - ,Changing order status ----------,"
						+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
			}
		} else {
			// Incase an order with cancelled status comes before
			if (OrderStatus.CANCELLED.equals(response.getOrder().getStatus())) {
				cancelledOrder.put(response.getOrder().getOrderId(), response);
			} else {
				watch.start();
				saveAndNotify(response);
				if (cancelledOrder.containsKey(response.getOrder().getOrderId())) {
					System.out.println("########## , STEP 1, - ,Inside cancelled order status ----------");
					processMessage(cancelledOrder.get(response.getOrder().getOrderId()));
					cancelledOrder.remove(response.getOrder().getOrderId());
				}
				System.out.println("########## , STEP 2, - ,finally time ----------,"
						+ watch.stop().elapsed(TimeUnit.MILLISECONDS));
			}
		}
	}

	private void saveAndNotify(OrderResponse order) {
		service.addOrder(order);
		if (!TransactionUtils.isSpecialOrder(order.getOrder())) {
			AggregatedReportData data1 = cacheService.addStatsForAll(order);
			notifier.addNotification(env, data1);
			AggregatedReportData data2 = cacheService.addStatsForCategory(order);
			notifier.addNotification(env, data2);
			AggregatedReportData data3 = cacheService.addStatsForSubCategory(order);
			notifier.addNotification(env, data3);
			UnitReportData data = cacheService.addOrder(order);
			notifier.addNotification(env, data);
			List<ProductReportData> products = productCacheService.addOrder(order);
			for (ProductReportData product : products) {
				if (trackedProducts.contains(product.getDetail().getDetail().getId())) {
					notifier.addNotification(env, product);
				}
			}
			data.set_id(null);
			UnitReportDataObject object = new UnitReportDataObject(data);
			aggregateService.save(object);
		}
	}
}
