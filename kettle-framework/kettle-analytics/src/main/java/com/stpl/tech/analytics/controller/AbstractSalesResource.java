/**
 * 
 */
package com.stpl.tech.analytics.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.jms.JMSException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.api.services.drive.model.File;
import com.stpl.tech.analytics.mapper.SalesProjectionDataMapper;
import com.stpl.tech.analytics.model.UnitData;
import com.stpl.tech.analytics.service.SalesDataCacheService;
import com.stpl.tech.analytics.service.impl.AnalyticsProperties;
import com.stpl.tech.analytics.service.impl.ProjectionsDataCache;
import com.stpl.tech.analytics.service.impl.SQSMessageService;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.file.load.ExcelParser;
import com.stpl.tech.kettle.core.file.management.GoogleSheetLoader;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

/**
 * <AUTHOR>
 *
 */
public abstract class AbstractSalesResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractSalesResource.class);

	public void projectionsDataCacheRefresh() {
		String fileName = "current_month_projection";
		String outputFile = getProps().getBasePath() + "/targets/" + fileName;
		GoogleSheetLoader loader = new GoogleSheetLoader();
		try {
			LOG.info("Downloading file {} ", fileName);
			File file = loader.getFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
					TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
			if (file == null) {
				throw new RuntimeException("File Not Found " + fileName);
			}
			loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
					AppConstants.EXCEL_MIME_TYPE, outputFile);
			List<UnitData> targets = getSalesProjections(0, outputFile);
			getProjections().addProjections(targets);

		} catch (IOException e) {
			LOG.error("Failed to download the file", e);
		}

	}

	private List<UnitData> getSalesProjections(int sheetNo, String outputFile) {
		ExcelParser<UnitData> parser = new ExcelParser<>(new SalesProjectionDataMapper());
		List<UnitData> expenses = new ArrayList<>();
		try {
			expenses = parser.parseExcel(outputFile, sheetNo, 2, 0, 1);
			if (parser.hasErrors()) {
				LOG.error("Errors in excel sheet " + parser.getErrors());
				throw new RuntimeException("Errors in excel sheet " + parser.getErrors());
			}
		} catch (IOException e) {
			LOG.error("Failed to load the stored excel file ", e);
			throw new RuntimeException("Failed to load the stored excel file ", e);
		}
		return expenses;
	}

	public abstract ProjectionsDataCache getProjections();

	public abstract AnalyticsProperties getProps();

	public abstract SQSMessageService getSQSService();

	public abstract SalesDataCacheService getSalesDataCacheService();

	protected void salesDataCacheRefresh(Date businessDate, Date startTime, Date endTime) throws JMSException {

		getSQSService().stopQueueProcessing();
		LOG.info("Recalculating Old Sales Data");
		LOG.info("Recalculating Old Sales Data from " + startTime + " to " + endTime);
		getSalesDataCacheService().reloadCache(businessDate, startTime, endTime, true);
		LOG.info("Saving daily sales data for " + businessDate);
		getSalesDataCacheService().saveDaily(businessDate);
		LOG.info("Running Cache Refreshes for Sales Data");
		getSalesDataCacheService().reloadCache(AppUtils.getCurrentBusinessDate(), null, null, true);
		getSQSService().startQueueProcessing();

	}

}
