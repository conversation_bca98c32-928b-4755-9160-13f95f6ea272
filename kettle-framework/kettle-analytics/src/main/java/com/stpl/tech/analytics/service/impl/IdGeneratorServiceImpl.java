package com.stpl.tech.analytics.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.analytics.dao.IdGeneratorDao;
import com.stpl.tech.analytics.model.IdGenerator;
import com.stpl.tech.analytics.service.IdGeneratorService;


@Service("analyticsIdGeneratorService")
public class IdGeneratorServiceImpl implements IdGeneratorService {

	@Autowired
	private IdGeneratorDao dao;

	@Override
	public <T> int getNextId(Class<T> context) {
		IdGenerator data = null;
		List<IdGenerator> list = dao.findByName(context.getName());
		if (list == null || list.size() == 0) {
			IdGenerator idGenerator = new IdGenerator();
			idGenerator.setName(context.getName());
			idGenerator.setNextId(1);
			data = dao.save(idGenerator);
		} else {
			data = list.get(0);
		}
		int id = data.getNextId();
		data.setNextId(data.getNextId() + 1);
		dao.save(data);
		return id;
	}

}
