/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.dao.chaimonk;

import com.stpl.tech.analytics.model.chaimonk.ChaiMonkOrder;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ChaiMonkOrderProcessingDao extends MongoRepository<ChaiMonkOrder,String>{


    @Query("{'billGenerationTime' : { $gte : ?0 }, 'orderStatus' : { $ne : ?1 }}")
    public List<ChaiMonkOrder> findByOrderTime(final Date billGenerationTime,final String status);

    @Query("{'orderId': ?0}")
    public List<ChaiMonkOrder> findByOrderId(final int orderId);

}
