//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.10 at 07:11:28 PM IST 
//

package com.stpl.tech.analytics.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter2;

public class SalesData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4092911385931520565L;
	protected int unitId;
	protected String unitName;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	protected Date businessDate;
	protected List<AggregatedData> data;

	public SalesData() {

	}

	public SalesData(UnitReportData data, UnitReportData lwData) {
		if (data == null) {
			return;
		}
		this.unitId = data.getDetail().getId();
		this.unitName = data.getDetail().getName();
		this.businessDate = data.getBusinessDate();
		this.data = new ArrayList<AggregatedData>();
		if (lwData == null) {
			lwData = new UnitReportData();
			lwData.setData(new AggregateSaleData());
		}
		this.data.add(create("SALES", "Total Sales", data.getData().getNetSale(), lwData.getData().getNetSale()));
		this.data.add(create("TICKETS", "Total Tickets", data.getData().getTickets(), lwData.getData().getTickets()));
		this.data.add(create("APC", "Total APC", data.getData().getApc(), lwData.getData().getApc()));
		this.data.add(create("DINE_IN_SALES", "Dine In Sales", data.getData().getDineInNetSale(),
				lwData.getData().getDineInNetSale()));
		this.data.add(create("DINE_IN_TICKETS", "Dine In Tickets", data.getData().getDineInTickets(),
				lwData.getData().getDineInTickets()));
		this.data.add(
				create("DINE_IN_APC", "Dine In APC", data.getData().getDineInApc(), lwData.getData().getDineInApc()));


		this.data.add(create("DELIVERY_SALES", "Delivery Sales", data.getData().getDeliveryNetSale(),
				lwData.getData().getDeliveryNetSale()));
		this.data.add(create("DELIVERY_TICKETS", "Delivery Tickets", data.getData().getDeliveryTickets(),
				lwData.getData().getDeliveryTickets()));
		this.data.add(create("DELIVERY_APC", "Delivery APC", data.getData().getDeliveryApc(),
				lwData.getData().getDeliveryApc()));

		this.data.add(create("DELIVERY_SALES_CHAAYOS", "Delivery Sales(Chaayos)", data.getData().getDeliveryNetSaleChaayos(),
				lwData.getData().getDeliveryNetSaleChaayos()));
		this.data.add(create("DELIVERY_TICKETS_CHAAYOS", "Delivery Tickets(Chaayos)", data.getData().getDeliveryTicketsChaayos(),
				lwData.getData().getDeliveryTicketsChaayos()));
		this.data.add(create("DELIVERY_APC_CHAAYOS", "Delivery APC(Chaayos)", data.getData().getDeliveryApcChaayos(),
				lwData.getData().getDeliveryApcChaayos()));
		this.data.add(create("DELIVERY_SALES_GNT", "Delivery Sales(GnT)", data.getData().getDeliveryNetSaleGnT(),
				lwData.getData().getDeliveryNetSaleGnT()));
		this.data.add(create("DELIVERY_TICKETS_GNT", "Delivery Tickets(GnT)", data.getData().getDeliveryTicketsGnT(),
				lwData.getData().getDeliveryTicketsGnT()));
		this.data.add(create("DELIVERY_APC_GNT", "Delivery APC(GnT)", data.getData().getDeliveryApcGnT(),
				lwData.getData().getDeliveryApcGnT()));
	}

	private AggregatedData create(String code, String name, NumericData data, NumericData lwData) {
		AggregatedData d = new AggregatedData();
		d.setCode(code);
		d.setName(name);
		d.setCurrent(new BigDecimal(data.getCurrent()));
		d.setTarget(new BigDecimal(data.getTarget()));
		if (lwData != null) {
			d.setLw(new BigDecimal(lwData.getCurrent()));
		}
		return d;

	}

	private AggregatedData create(String code, String name, DecimalData data, DecimalData lwData) {
		AggregatedData d = new AggregatedData();
		d.setCode(code);
		d.setName(name);
		d.setCurrent(data.getCurrent());
		d.setTarget(data.getTarget());
		if (lwData != null) {
			d.setLw(lwData.getCurrent());
		}
		return d;

	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public List<AggregatedData> getData() {
		return data;
	}

	public void setData(List<AggregatedData> data) {
		this.data = data;
	}

}
