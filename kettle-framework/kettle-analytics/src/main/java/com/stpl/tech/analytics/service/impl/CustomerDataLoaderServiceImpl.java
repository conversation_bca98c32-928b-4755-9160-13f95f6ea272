/**
 * 
 */
package com.stpl.tech.analytics.service.impl;

import java.sql.SQLException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.analytics.data.dao.DataLoaderDao;
import com.stpl.tech.analytics.service.CustomerDataLoaderService;
import com.stpl.tech.kettle.sales.domain.model.CustomerData;
import com.stpl.tech.util.ExecutionEnvironment;

/**
 * <AUTHOR>
 *
 */
@Service
public class CustomerDataLoaderServiceImpl implements CustomerDataLoaderService {

	@Autowired
	private DataLoaderDao customerDao;
	/* (non-Javadoc)
	 * @see com.stpl.tech.analytics.service.CustomerDataLoaderService#loadCustomerData(com.stpl.tech.util.ExecutionEnvironment, int, int)
	 */
	@Override
	public List<CustomerData> loadCustomerData(ExecutionEnvironment env, int startCustomerId, int endCustomerId)
			throws SQLException {
		return customerDao.loadCustomerData(env, startCustomerId, endCustomerId);
	}

}
