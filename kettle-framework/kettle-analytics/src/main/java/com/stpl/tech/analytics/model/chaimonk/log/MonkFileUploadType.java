package com.stpl.tech.analytics.model.chaimonk.log;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 20-02-2018.
 */
public enum MonkFileUploadType {
    STATUS_LOG,
    CONSUMPTION_LOG,
    TASK_STATUS_LOG,
    TASK_STATUS_DETAIL_LOG,
    TASK_STATUS_DETAIL_LOG_NEW,
    WS_CRASH_LOG;

    public String value() {
        return name();
    }

    public static MonkFileUploadType fromValue(String v) {
        return valueOf(v);
    }

}
