package com.stpl.tech.analytics.service.impl;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.analytics.listener.OrderMessageListener;
import com.stpl.tech.analytics.service.AggregateDataManagementService;
import com.stpl.tech.analytics.service.OrderProcessingService;
import com.stpl.tech.analytics.service.ProductSalesDataCacheService;
import com.stpl.tech.analytics.service.SalesDataCacheService;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;

@Service
public class SQSMessageService {

	private static final Logger LOG = LoggerFactory.getLogger(SQSMessageService.class);
	@Autowired
	private AnalyticsProperties props;

	@Autowired
	private OrderProcessingService service;

	@Autowired
	private AggregateDataManagementService aggregateService;

	@Autowired
	private SalesDataCacheService cacheService;

	@Autowired
	private ProductSalesDataCacheService productCacheService;

	//@PostConstruct
	public void init() throws JMSException {
		Regions region = AppUtils.getRegion(props.getEnvironmentType());
		SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
		MessageProducer producer = SQSNotification.getInstance().getProducer(session, props.getEnvironmentType().name(),
				"_ORDERS_ERRORS");
		OrderMessageListener listener = new OrderMessageListener(props.getEnvironmentType(), producer, service,
				cacheService, productCacheService, props.getTrackedProducts(), props.publishToPusher(),
				aggregateService);
		MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvironmentType().name(),
				"_ORDERS");
		consumer.setMessageListener(listener);
		startQueueProcessing();
	}

	public void stopQueueProcessing() throws JMSException {
		Regions region = AppUtils.getRegion(props.getEnvironmentType());
		LOG.info("##########Stopping Queueing Service##########");
		SQSNotification.getInstance().getSqsConnection(region).stop();
		LOG.info("##########Stopped Queueing Service##########");

	}

	public void startQueueProcessing() throws JMSException {
		Regions region = AppUtils.getRegion(props.getEnvironmentType());
		LOG.info("##########Starting Queueing Service##########");
		SQSNotification.getInstance().getSqsConnection(region).start();
		LOG.info("##########Started Queueing Service##########");

	}
}
