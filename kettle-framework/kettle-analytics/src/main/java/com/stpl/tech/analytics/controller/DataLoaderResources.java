/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.controller;

import static com.stpl.tech.analytics.core.AnalyticsConstants.ANALYTICS_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;
import static com.stpl.tech.analytics.core.AnalyticsConstants.LOADER_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.SEPARATOR;

import java.sql.SQLException;
import java.util.List;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.analytics.dao.CustomerDataDao;
import com.stpl.tech.analytics.service.CustomerDataLoaderService;
import com.stpl.tech.kettle.sales.domain.model.CustomerData;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.ExecutionEnvironment;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + ANALYTICS_SERVICES_ROOT_CONTEXT + SEPARATOR
		+ LOADER_SERVICES_ROOT_CONTEXT) // v1/analytics/log
public class DataLoaderResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(DataLoaderResources.class);

	@Autowired
	private CustomerDataLoaderService customerService;

	@Autowired
	private CustomerDataDao customerDao;

	@RequestMapping(method = RequestMethod.GET, value = "load/customer", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean addAssemblyLogData(@RequestParam("start") final Integer start,
			@RequestParam("end") final Integer end) throws SQLException {
		List<CustomerData> datas = customerService.loadCustomerData(ExecutionEnvironment.UAT, start, end);
		customerDao.saveAll(datas);
		return true;
	}

}
