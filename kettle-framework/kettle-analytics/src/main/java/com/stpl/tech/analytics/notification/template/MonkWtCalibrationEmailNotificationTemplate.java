package com.stpl.tech.analytics.notification.template;

import com.stpl.tech.analytics.model.WeightCalibrationDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public class MonkWtCalibrationEmailNotificationTemplate extends AbstractVelocityTemplate {
    private WeightCalibrationDetail weightCalibrationDetail;

    private String basePath;

    public MonkWtCalibrationEmailNotificationTemplate(WeightCalibrationDetail weightCalibrationDetail, String basePath) {
        this.weightCalibrationDetail = weightCalibrationDetail;
        this.basePath = basePath;
    }

    @Override
    public String getTemplatePath() {
        return "templates/MonkWtCalibrationEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/monkDiagnosis/" + weightCalibrationDetail.getWtCalibrationEventId() +"/"+ AppUtils.getDateString(AppUtils.getCurrentDateIST())  + ".html";

    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("logData", weightCalibrationDetail);
        return stringObjectMap;
    }


}
