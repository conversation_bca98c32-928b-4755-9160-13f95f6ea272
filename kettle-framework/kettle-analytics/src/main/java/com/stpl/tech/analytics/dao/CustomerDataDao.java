package com.stpl.tech.analytics.dao;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.sales.domain.model.CustomerData;

@Repository
public interface CustomerDataDao extends MongoRepository<CustomerData, String> {


	@Query("{'customerId' : ?0}")
	public List<CustomerData> findByCustomerId(final int customerId);
}
