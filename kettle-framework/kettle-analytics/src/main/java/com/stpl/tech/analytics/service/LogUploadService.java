package com.stpl.tech.analytics.service;

import com.google.gson.Gson;
import com.stpl.tech.analytics.core.AnalyticsUtil;
import com.stpl.tech.loggingservice.model.chaimonk.log.BasicLogDetail;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 19-02-2019.
 */
public class LogUploadService<T extends MongoRepository, R extends BasicLogDetail> implements Runnable {

    private static final Gson GSON = AnalyticsUtil.getCustomSerializer();

    private static final Logger LOG = LoggerFactory.getLogger(LogUploadService.class);

    private String[] objects;

    private UnitBasicDetail unitBasicDetail;

    private EnvType envType;

    private T dao;

    private Class<R> objectType;

    public LogUploadService(UnitBasicDetail unit,EnvType env, String[] objects, T dao, Class<R> objectType) {
        this.objects = objects;
        this.objectType = objectType;
        this.dao = dao;
        this.unitBasicDetail = unit;
        this.envType = env;
    }

    @Override
    public void run() {
        LOG.info(":::::::::::::::::::::::: Initializing upload service of type {} for {} at {} ::::::::::::::::::::::::",
                objectType.getName(),unitBasicDetail.getName(), AppUtils.getCurrentTimestamp());
        List<R> allObjects = new ArrayList<R>();
        for (String object : this.objects) {
            R obj = GSON.fromJson(object, objectType);
            obj.setUploadTime(AppUtils.getCurrentTimestamp());
            allObjects.add(obj);
        }
        dao.save(allObjects);
        LOG.info(":::::::::::::::: On Completion Total Objects uploaded :::::::::::::::: {} of Type {} for Unit {} at {}",
                objects.length, objectType.getName(), unitBasicDetail.getName(), AppUtils.getCurrentTimestamp());
        StringBuilder message = new StringBuilder(":::: MONK FILE UPLOAD SUCCESS ALARM NOTIFICATION :::: \n");
        message.append("UNIT ID :: ").append(unitBasicDetail.getId()).append("\n");
        message.append("UNIT NAME :: ").append(unitBasicDetail.getName()).append("\n");
        message.append(":::::::::::::::::::::::::::::::::\n");
        sendSlackMessage(message, SlackNotification.MONK_SYNC_ERRORS);
    }

    private void sendSlackMessage(StringBuilder message,SlackNotification notification) {
        message.append("--------------------------------------------------------");
        SlackNotificationService.getInstance().sendNotification(this.envType, "ANALYTICS",
                notification, message.toString());
    }
}
