package com.stpl.tech.analytics.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.common.base.Preconditions;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.hazelcast.config.Config;
import com.hazelcast.config.JoinConfig;
import com.hazelcast.config.NetworkConfig;
import com.hazelcast.core.Hazelcast;
import com.hazelcast.core.HazelcastInstance;
import com.stpl.tech.master.core.config.MasterCacheClientConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;
import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import com.stpl.tech.spring.config.SpringUtilityServiceConfig;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import com.stpl.tech.util.EnvType;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.io.File;
import java.io.FileInputStream;
import java.net.URL;
import java.util.Properties;
import java.util.TimeZone;


@SpringBootApplication
@Configuration
@EnableScheduling
@EnableTransactionManagement
@ComponentScan(basePackages = { "com.stpl.tech.analytics","com.stpl.tech.master.core.apps.service" })
@EnableMongoRepositories(basePackages = {"com.stpl.tech.analytics.dao","com.stpl.tech.master.core.apps.dao","com.stpl.tech.loggingservice.dao.chaimonk.log"})
@EnableJpaRepositories(basePackages = "com.stpl.tech.analytics.data.dao", entityManagerFactoryRef = "TransactionDataSourceEMFactory", transactionManagerRef = "TransactionDataSourceTM")
@Log4j2
@Import(value = {MasterExternalConfig.class, SpringUtilityServiceConfig.class, MasterCacheClientConfig.class, MasterSecurityConfiguration.class})
public class KettleAnalyticsConfig  extends SpringBootServletInitializer{

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	@Autowired
	private Environment env;

	public KettleAnalyticsConfig() {
		super();
		System.out.println("config read...................................................");
	}

	public static void main(String[] args) {
		SpringApplication.run(KettleAnalyticsConfig.class);
	}

//	@Bean
//	public MongoClient factory() throws UnknownHostException {
//		MongoClientURI uri = new MongoClientURI(env.getProperty("spring.data.mongodb.uri"));
//		return new MongoClient(uri);
//	}

	@Bean
	public ThreadPoolTaskExecutor taskExecutor() {
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		pool.setCorePoolSize(10);
		pool.setMaxPoolSize(20);
		pool.setWaitForTasksToCompleteOnShutdown(true);
		return pool;
	}

	/*
	 * @Bean public OrientTransactionManager transactionManager() { return new
	 * OrientTransactionManager(factory()); }
	 *
	 * @Bean public OrientObjectTemplate objectTemplate() { return new
	 * OrientObjectTemplate(factory()); }
	 */

//	@Bean
//	public MongoDbFactory getMongoDbFactory() throws Exception {
//		return new SimpleMongoDbFactory(factory(), env.getProperty("spring.data.mongodb.database"));
//	}

//	@Bean(name = "mongoTemplate")
//	public MongoTemplate getMongoTemplate() throws Exception {
//		MongoTemplate mongoTemplate = new MongoTemplate(getMongoDbFactory());
//		return mongoTemplate;
//	}

	/*
	 * @PostConstruct public void registerEntities() throws
	 * UnknownHostException{
	 * factory().db().getEntityManager().registerEntityClasses(
	 * "com.stpl.tech.kettle.domain.model");
	 * factory().db().getEntityManager().registerEntityClasses(
	 * "com.stpl.tech.master.recipe.model");
	 * factory().db().getEntityManager().registerEntityClasses(
	 * "com.stpl.tech.master.domain.model");
	 * factory().db().getEntityManager().registerEntityClasses(
	 * "com.stpl.tech.kettle.delivery.model");
	 * factory().db().getEntityManager().registerEntityClasses(
	 * "com.stpl.tech.analytics.model"); }
	 */

	@Bean(name = "AnalyticsHazelCastSessionConfig")
	public Config hazelcastSessionConfig() {
		Config c = new Config();
		if (env.getProperty("environment.type", "LOCAL").equalsIgnoreCase(EnvType.LOCAL.name())) {
			NetworkConfig network = c.getNetworkConfig();
			//network.addOutboundPort(45734);
			JoinConfig join = network.getJoin();
			join.getMulticastConfig().setEnabled(false);
			join.getTcpIpConfig().addMember(env.getProperty("master.cache.host.details", "localhost")).setEnabled(true);
			c.setInstanceName("AnalyticsHazelCastSessionConfig");
		} else {
			c = new Config().setInstanceName("AnalyticsHazelCastSessionConfig");
		}
		return c;
	}

	@Bean(name = "AnalyticsHazelCastInstance")
	public HazelcastInstance hazelcastInstance() {
		return Hazelcast.getOrCreateHazelcastInstance(hazelcastSessionConfig());
	}

	@Bean(name = "TransactionDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean entityManagerFactory() {
		final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(dataSource());
		em.setPackagesToScan(
				new String[] { "com.stpl.tech.kettle.data.model","com.stpl.tech.analytics.dao"});

		final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		em.setJpaProperties(additionalProperties());
		em.setPersistenceUnitName("TransactionDataSourcePUName");
		return em;
	}

	@Bean(name = "TransactionDataSource")
	public DataSource dataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(Preconditions.checkNotNull(env.getProperty("jdbc.driverClassName")));
		dataSource.setUrl(Preconditions.checkNotNull(env.getProperty("jdbc.url")));
		dataSource.setUsername(Preconditions.checkNotNull(env.getProperty("jdbc.user")));
		dataSource.setPassword(Preconditions.checkNotNull(env.getProperty("jdbc.pass")));

		return dataSource;
	}

	@Bean(name = "TransactionDataSourceTM")
	public PlatformTransactionManager transactionManager() {
		final JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(entityManagerFactory().getObject());
		return transactionManager;
	}

	@Bean(name = "TransactionDataSourceET")
	public PersistenceExceptionTranslationPostProcessor exceptionTranslation() {
		return new PersistenceExceptionTranslationPostProcessor();
	}

	final Properties additionalProperties() {
		final Properties hibernateProperties = new Properties();
		hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
		hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
		hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
		return hibernateProperties;
	}

    @Bean(name = "multipartResolver")
    public CommonsMultipartResolver commonsMultipartResolver(){
        CommonsMultipartResolver commonsMultipartResolver = new CommonsMultipartResolver();
        commonsMultipartResolver.setDefaultEncoding("utf-8");
        //commonsMultipartResolver.setMaxUploadSize(8388608); // equivalent to 8 MB
		commonsMultipartResolver.setMaxUploadSize(********);
        return commonsMultipartResolver;
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(KettleAnalyticsConfig.class);
    }

	@PostConstruct
	public void initializeFirebaseApp() {
		log.info("POST CONSTRUCT FIRESTORE - STARTED");
		FileInputStream serviceAccount = null;
		try {
			try {
				URL resource = getClass().getClassLoader().getResource("firebase-" +
						EnvType.valueOf(env.getProperty("environment.type")).name().toLowerCase()+".json");
				if (resource != null) {
					File file = new File(resource.getFile());
					serviceAccount = new FileInputStream(file);
				} else {
					log.info("No File Found ..!");
					return;
				}
			} catch (Exception e) {
				log.info("Error Occurred while loading resources ..!" , e);
				return;
			}
			FirebaseOptions options = FirebaseOptions.builder()
					.setCredentials(GoogleCredentials.fromStream(serviceAccount))
					.build();
			FirebaseApp.initializeApp(options);
		} catch (Exception e) {
			log.error("File not found exception :",e);
		}
	}
}
