/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.controller;

import static com.stpl.tech.analytics.core.AnalyticsConstants.ANALYTICS_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;
import static com.stpl.tech.analytics.core.AnalyticsConstants.SEPARATOR;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import javax.jms.JMSException;
import javax.ws.rs.core.MediaType;

import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.google.api.services.drive.model.File;
import com.stpl.tech.analytics.cache.AnalyticsCache;
import com.stpl.tech.analytics.dao.DailyUnitReportDataObjectDao;
import com.stpl.tech.analytics.dao.SalesTargetRepository;
import com.stpl.tech.analytics.mapper.SalesTargetDataMapper;
import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.AggregatedReportData;
import com.stpl.tech.analytics.model.CategoryType;
import com.stpl.tech.analytics.model.CumulativeData;
import com.stpl.tech.analytics.model.DailyUnitReportDataObject;
import com.stpl.tech.analytics.model.PercentageIntegerData;
import com.stpl.tech.analytics.model.PosAnalytics;
import com.stpl.tech.analytics.model.ProductReportData;
import com.stpl.tech.analytics.model.SalesData;
import com.stpl.tech.analytics.model.SalesTarget;
import com.stpl.tech.analytics.model.TotalPenetrationData;
import com.stpl.tech.analytics.model.UnitData;
import com.stpl.tech.analytics.model.UnitReportData;
import com.stpl.tech.analytics.service.AggregateDataManagementService;
import com.stpl.tech.analytics.service.DataService;
import com.stpl.tech.analytics.service.ProductSalesDataCacheService;
import com.stpl.tech.analytics.service.SalesDataCacheService;
import com.stpl.tech.analytics.service.impl.AnalyticsProperties;
import com.stpl.tech.analytics.service.impl.ProjectionsDataCache;
import com.stpl.tech.analytics.service.impl.SQSMessageService;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.file.load.ExcelParser;
import com.stpl.tech.kettle.core.file.management.GoogleSheetLoader;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.jms.JMSException;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

import static com.stpl.tech.analytics.core.AnalyticsConstants.ANALYTICS_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;
import static com.stpl.tech.analytics.core.AnalyticsConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + ANALYTICS_SERVICES_ROOT_CONTEXT)
public class SalesDataResources extends AbstractSalesResource {

    private static final Logger LOG = LoggerFactory.getLogger(SalesDataResources.class);

    @Autowired
    private SalesDataCacheService cache;
    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private ProductSalesDataCacheService productCache;

    @Autowired
    private SalesTargetRepository targetDao;

    @Autowired
    private AggregateDataManagementService aggregationService;

    @Autowired
    private DataService dataService;

    @Autowired
    private ProjectionsDataCache projections;

    @Autowired
    private AnalyticsProperties props;

    @Autowired
    private SQSMessageService sqsService;

    @Autowired
    private DailyUnitReportDataObjectDao dailyDao;

    @Autowired
    private AnalyticsCache analyticsCache;

    @RequestMapping(method = RequestMethod.GET, value = "unit/sales", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitReportData salesDate(@RequestParam(name = "unitId") final int unitId) {
        return cache.getSalesData(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "overall/system/sales", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitReportData systemSalesData() {
        return cache.getSystemSalesData();
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/sales/new", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SalesData compressedSalesData(@RequestParam(name = "unitId") final int unitId) {
        return new SalesData(cache.getSalesData(unitId), aggregationService.findLastWeekCurrentTimeSale(unitId));
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/penetration/target", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AggregatePenetrationData compressedPenetrationTargetData(@RequestParam(name = "unitId") final int unitId,
                                                                    @RequestParam(name = "businessDate", required = false) final String businessDate, @RequestParam(name = "loggedIn", required = false) final String loggedIn) {
        if (businessDate != null) {
            return dataService.getCurrentMonthTargets(unitId, AppUtils.getDate(AppUtils.parseDate(businessDate)), loggedIn);
        } else {
            return dataService.getCurrentMonthTargets(unitId, null, loggedIn);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-analytics-cache", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void refreshAnalyticsCacheData() {
        LOG.info("Refreshing Analytics Cache");
        analyticsCache.refreshCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/penetration/target-for-day", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AggregatePenetrationData compressedPenetrationDailyTargetData(
            @RequestParam(name = "unitId") final int unitId,
            @RequestParam(name = "businessDate", required = false) final String businessDate, @RequestParam(name = "loggedIn", required = false) final String loggedIn) {
        if (businessDate != null) {
            return dataService.getCurrentDailyTargets(unitId, AppUtils.getDate(AppUtils.parseDate(businessDate)), loggedIn);
        } else {
            return dataService.getCurrentDailyTargets(unitId, null, loggedIn);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/penetration/target-all-data", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public TotalPenetrationData compressedPenetrationTotalTargetData(@RequestParam(name = "unitId") final int unitId, @RequestParam(name = "businessDate", required = false) final String businessDate, @RequestParam(name = "loggedIn", required = false) final String loggedIn) {
        AggregatePenetrationData targetDaily = new AggregatePenetrationData();
        AggregatePenetrationData targetMonthly = new AggregatePenetrationData();
        if (businessDate != null) {
            targetDaily = dataService.getCurrentDailyTargets(unitId, AppUtils.getDate(AppUtils.parseDate(businessDate)), loggedIn);
            targetMonthly = dataService.getCurrentMonthTargets(unitId, AppUtils.getDate(AppUtils.parseDate(businessDate)), loggedIn);
        } else {
            targetDaily = dataService.getCurrentDailyTargets(unitId, null, loggedIn);
            targetMonthly = dataService.getCurrentMonthTargets(unitId, null, loggedIn);
        }
        TotalPenetrationData totalPenetrationData = getAllAggregatedPenetrationData(targetDaily, targetMonthly);
        return totalPenetrationData;
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/penetration/current-all-data", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public TotalPenetrationData compressedPenetrationTotalCurrentData(@RequestParam(name = "unitId") final int unitId, @RequestParam(name = "businessDate", required = false) final String businessDate, @RequestParam(name = "loggedIn", required = false) final String loggedIn)
            throws CloneNotSupportedException {
        UnitReportData specificUnitReportData = cache.getSalesData(unitId);
        UnitReportData systemWideReportData = cache.getSystemSalesData();
        AggregatePenetrationData currentDaily = specificUnitReportData == null ? new AggregatePenetrationData() : specificUnitReportData.getPenetration();

        AggregatePenetrationData currentMonthly = null;
        if (businessDate != null) {
            Date currentDate = AppUtils.getDate(AppUtils.parseDate(businessDate));
            Date startOfMonth = AppUtils.getStartOfMonth(currentDate.getYear(), currentDate.getMonth());
            List<DailyUnitReportDataObject> data = null;
            if (currentDate.equals(startOfMonth)) {
                currentDate = AppUtils.getNextDate(currentDate);
            }
            if (!Objects.equals(loggedIn, "true") && analyticsCache.getDailyUnitReportDataObjectFromCache(unitId) != null && Objects.equals(currentDaily, AppUtils.getBusinessDate())) {
                data = analyticsCache.getDailyUnitReportDataObjectFromCache(unitId);
            } else {
                data = dailyDao.findByUnitIdMonthAndYear(unitId, startOfMonth, currentDate);
                analyticsCache.setDailyUnitReportDataObjectFromCache(unitId, data);
            }
            if (data != null && data.size() > 0) {
                currentMonthly = (AggregatePenetrationData) data.get(0).getInstance().getPenetration().clone();

            }
            for (int i = 1; i < data.size(); i++) {
                currentMonthly.add(data.get(i).getInstance().getPenetration());
            }
            if (Objects.nonNull(currentMonthly)) {
                currentMonthly.setOverallSystemWideReportData(systemWideReportData);
                currentMonthly.setSpecificUnitReportData(specificUnitReportData);
            }
        } else {

            AggregatePenetrationData mtd = cache.getMTDPenetration(unitId);
            if (mtd != null) {
                currentMonthly = (AggregatePenetrationData) mtd.clone();
            }
            UnitReportData unitData = cache.getSalesData(unitId);
            if (currentMonthly == null) {
                if (unitData != null) {
                    currentMonthly = unitData.getPenetration();
                }

            } else {
                if (unitData != null) {
                    currentMonthly.add(unitData.getPenetration());
                }
            }

            if (Objects.nonNull(currentMonthly)) {
                currentMonthly.setSpecificUnitReportData(specificUnitReportData);
                currentMonthly.setOverallSystemWideReportData(systemWideReportData);
            }
        }
        return getAllAggregatedPenetrationData(currentMonthly, currentDaily);
    }

    private TotalPenetrationData getAllAggregatedPenetrationData(AggregatePenetrationData currentDaily, AggregatePenetrationData currentMonthly) {
        TotalPenetrationData totalPenetrationData = new TotalPenetrationData();
        for (CategoryType category : CategoryType.getVisibleCategory()) {
            PosAnalytics posAnalytics = new PosAnalytics();
            posAnalytics.setLabel(category.getCategory());
            posAnalytics.setAggregate(CategoryType.getCategorySpecificResult(currentDaily, category));
            posAnalytics.setCurrentOrTarget(CategoryType.getCategorySpecificResult(currentMonthly, category));
            posAnalytics.setTrend(category.getTrend());
            List<PosAnalytics> list = totalPenetrationData.getMetrics();
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(posAnalytics);
            totalPenetrationData.setMetrics(list);
        }

        totalPenetrationData.setType("PENETRATION TARGET");
        return totalPenetrationData;
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/penetration/current", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AggregatePenetrationData compressedPenetrationData(@RequestParam(name = "unitId") final int unitId,
                                                              @RequestParam(name = "businessDate", required = false) final String businessDate, @RequestParam(name = "loggedIn", required = false) final String loggedIn)
            throws CloneNotSupportedException {
        UnitReportData specificUnitReportData = cache.getSalesData(unitId);
        UnitReportData systemWideReportData = cache.getSystemSalesData();
        if (businessDate != null) {
            Date currentDate = AppUtils.getDate(AppUtils.parseDate(businessDate));
            Date startOfMonth = AppUtils.getStartOfMonth(currentDate.getYear(), currentDate.getMonth());
            List<DailyUnitReportDataObject> data = null;
            if (currentDate.equals(startOfMonth)) {
                currentDate = AppUtils.getNextDate(currentDate);
            }
            if (!Objects.equals(loggedIn, "true") && analyticsCache.getDailyUnitReportDataObjectFromCache(unitId) != null && Objects.equals(currentDate, AppUtils.getBusinessDate())) {
                data = analyticsCache.getDailyUnitReportDataObjectFromCache(unitId);
            } else {
                data = dailyDao.findByUnitIdMonthAndYear(unitId, startOfMonth, currentDate);
                analyticsCache.setDailyUnitReportDataObjectFromCache(unitId, data);
            }
            AggregatePenetrationData penetration = null;
            if (data != null && data.size() > 0) {
                penetration = (AggregatePenetrationData) data.get(0).getInstance().getPenetration().clone();

            }
            for (int i = 1; i < data.size(); i++) {
                penetration.add(data.get(i).getInstance().getPenetration());
            }
            if (Objects.nonNull(penetration)) {
                penetration.setOverallSystemWideReportData(systemWideReportData);
                penetration.setSpecificUnitReportData(specificUnitReportData);
            }
            return penetration;
        } else {

            AggregatePenetrationData result = null;
            AggregatePenetrationData mtd = cache.getMTDPenetration(unitId);
            if (mtd != null) {
                result = (AggregatePenetrationData) mtd.clone();
            }
            UnitReportData unitData = cache.getSalesData(unitId);
            if (result == null) {
                if (unitData != null) {
                    return unitData.getPenetration();
                }

            } else {
                if (unitData != null) {
                    result.add(unitData.getPenetration());
                }
            }

            if (Objects.nonNull(result)) {
                result.setSpecificUnitReportData(specificUnitReportData);
                result.setOverallSystemWideReportData(systemWideReportData);
            }
            return result;
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/penetration/current-for-day", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AggregatePenetrationData compressedPenetrationDailyData(@RequestParam(name = "unitId") final int unitId)
            throws CloneNotSupportedException {
        UnitReportData data = cache.getSalesData(unitId);
        return data == null ? new AggregatePenetrationData() : data.getPenetration();
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/penetration/previous-month", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AggregatePenetrationData compressedPenetrationPreviousMonthData(
            @RequestParam(name = "unitId") final int unitId) {
        return cache.getPreviousMonthPenetration(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "product/sales", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ProductReportData productSalesDate(@RequestParam(name = "productId") final int unitId) {
        return productCache.getSalesData(unitId);

    }

    @RequestMapping(method = RequestMethod.GET, value = "products/tracked", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductBasicDetail> getTrackedProducts() {
        List<ProductBasicDetail> products = new ArrayList<>();
        for (Integer p : props.getTrackedProducts()) {
            products.add(masterCache.getProductBasicDetail(p));
        }
        return products;

    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/sales/lw", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitReportData lwSalesData(@RequestParam(name = "unitId") final int unitId) {
        return aggregationService.findLastWeekCurrentTimeSale(unitId);

    }

    /**
     * token=q5feeDhJvs0CTjRPEyidUu3x team_id=T0001 team_domain=example
     * channel_id=C2147483705 channel_name=test user_id=U2147483697 user_name=Steve
     * command=/weather text=94070
     * response_url=https://hooks.slack.com/commands/1234/5678
     *
     * @param
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "system/sales", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_FORM_URLENCODED)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String aggregatedSales(@ModelAttribute("token") String token, @ModelAttribute("team_id") String teamId,
                                  @ModelAttribute("team_domain") String teamDomain, @ModelAttribute("channel_id") String channelId,
                                  @ModelAttribute("channel_name") String channelName, @ModelAttribute("user_id") String userId,
                                  @ModelAttribute("user_name") String userName, @ModelAttribute("command") String command,
                                  @ModelAttribute("text") String text, @ModelAttribute("response_url") String responseUrl) throws JSONException {
        LOG.info("Request Received : token: " + token + ", teamId: " + teamId + ", teamDomain: " + teamDomain
                + ", channelId: " + channelId + ", channelName: " + channelName + ", userId: " + userId + ", userName: "
                + userName + ", command: " + command + ", text: " + text + ", responseUrl: " + responseUrl);
        JSONObject result = new JSONObject();
        if (token.equals("q5feeDhJvs0CTjRPEyidUu3x")) {
            result.put("sales", cache.getAllAggregatedSalesData().getData().getNetSale().getCurrent());
            result.put("target", cache.getAllAggregatedSalesData().getData().getNetSale().getTarget());
        } else {
            result.put("error", "Not Allowed");
        }
        return result.toString(2);
    }

    @RequestMapping(method = RequestMethod.GET, value = "units/sales", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Set<UnitReportData> allSalesData() {
        return cache.getSalesData();

    }

    @RequestMapping(method = RequestMethod.GET, value = "products/sales", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Set<ProductReportData> allProductsSalesData() {
        Set<ProductReportData> set = new TreeSet<>();
        for (Integer id : props.getTrackedProducts()) {
            set.add(productCache.getSalesData(id));
        }
        return set;

    }

    @RequestMapping(method = RequestMethod.GET, value = "aggregated/sales", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Set<AggregatedReportData> allAggregatedSalesData() {
        return cache.getAggregatedSalesData();

    }

    @RequestMapping(method = RequestMethod.GET, value = "aggregated/sales/all", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AggregatedReportData getAllAggregatedSalesData() {
        return cache.getAllAggregatedSalesData();

    }

    @RequestMapping(method = RequestMethod.GET, value = "cumulative/sales", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CumulativeData getCumulativeData() {
        CumulativeData data = new CumulativeData();

        dataService.setCafeCountMTD(data);
        dataService.setMonthlyTarget(data);
        dataService.setMTD(data);
        dataService.setMTDTarget(data);
        dataService.setTodayTarget(data);
        if (!projections.isLoaded()) {
            projectionsDataCacheRefresh();
        }
        data.setMonthlyProjected(projections.getAggregatedProjections());
        List<UnitData> allUnitsData = dataService.getAllUnitsTargetData();
        Map<Integer, UnitData> projection = projections.getSalesProjection();
        PercentageIntegerData cafeCountProjected = new PercentageIntegerData();
        BigDecimal percentage = props.getPercentageThreasholdForReporting();
        for (UnitData unit : allUnitsData) {
            UnitData p = projection.get(unit.getUnitId());
            if (p != null) {
                BigDecimal sale = p.getSales().getTotal().getSales();
                BigDecimal t = unit.getSales().getTotal().getSales();
                if (sale.compareTo(t) >= 0) {
                    cafeCountProjected.setBeyond(cafeCountProjected.getBeyond() + 1);
                } else if (sale.compareTo(t) < 0 && sale.compareTo(AppUtils.multiply(t, percentage)) >= 0) {
                    cafeCountProjected.setAbove(cafeCountProjected.getAbove() + 1);
                } else if (sale.compareTo(t) < 0 && sale.compareTo(AppUtils.multiply(t, percentage)) < 0) {
                    cafeCountProjected.setBelow(cafeCountProjected.getBelow() + 1);
                }

            }
        }
        data.setCafeCountProjected(cafeCountProjected);
        return data;

    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh-projections", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void refreshProjections() {
        projectionsDataCacheRefresh();
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void salesDataCacheRefresh() {
        LOG.info("Running Cache Refreshes for Sales Data");
        cache.reloadCache(AppUtils.getCurrentBusinessDate(), null, null, true);
    }

    @RequestMapping(method = RequestMethod.GET, value = "refresh/day-close", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void dayClose(@RequestParam String businessDateStr) throws JMSException {
        Date businessDate = AppUtils.getDate(AppUtils.parseDate(businessDateStr));
        super.salesDataCacheRefresh(businessDate, AppUtils.getStartOfBusinessDay(businessDate),
                AppUtils.getStartOfBusinessDay(AppUtils.getDayBeforeOrAfterDay(businessDate, 1)));
    }

    @RequestMapping(method = RequestMethod.GET, value = "units/sales/target/update", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void updateSalesTargetData(@RequestParam(name = "fileName") final String fileName) {
        // String fileName = "sales_target_h1_2017.xlsx";
        String outputFile = props.getBasePath() + "/targets/" + fileName;
        Date minDate = new Date();
        GoogleSheetLoader loader = new GoogleSheetLoader();
        try {
            LOG.info("Downloading file {} ", fileName);
            File file = loader.getFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
                    TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
            if (file == null) {
                throw new RuntimeException("File Not Found " + fileName);
            }
            loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
                    AppConstants.EXCEL_MIME_TYPE, outputFile);
            List<SalesTarget> targets = getSalesTargets(0, outputFile);
            for (SalesTarget target : targets) {
                if (target.getBusinessDate().before(minDate)) {
                    minDate = target.getBusinessDate();
                }
            }
            List<SalesTarget> oldTargets = targetDao.findAllByBusinessDate(minDate);
            if (oldTargets != null && oldTargets.size() > 0) {
                LOG.info("Found {} old targets", oldTargets.size());
                targetDao.deleteAll(oldTargets);
            }
            targetDao.saveAll(targets);
            dataService.addTargets(targets);
            cache.refreshTargets(AppUtils.getCurrentBusinessDate());
        } catch (IOException e) {
            LOG.error("Failed to download the file", e);
        }

    }

    @RequestMapping(method = RequestMethod.GET, value = "units/sales/target/update/transactional", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void updateSalesTargetDataInTransaction(@RequestParam(name = "fileName") final String fileName) {
        // String fileName = "sales_target_h1_2017.xlsx";
        String outputFile = props.getBasePath() + "/targets/" + fileName;
        GoogleSheetLoader loader = new GoogleSheetLoader();
        try {
            LOG.info("Downloading file {} ", fileName);
            File file = loader.getFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
                    TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
            if (file == null) {
                throw new RuntimeException("File Not Found " + fileName);
            }
            loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
                    AppConstants.EXCEL_MIME_TYPE, outputFile);
            List<SalesTarget> targets = getSalesTargets(0, outputFile);
            dataService.addTargets(targets);
            cache.refreshTargets(AppUtils.getCurrentBusinessDate());
        } catch (IOException e) {
            LOG.error("Failed to download the file", e);
        }

    }

    private List<SalesTarget> getSalesTargets(int sheetNo, String outputFile) {
        ExcelParser<SalesTarget> parser = new ExcelParser<>(new SalesTargetDataMapper());
        List<SalesTarget> expenses = new ArrayList<>();
        try {
            expenses = parser.parseExcel(outputFile, sheetNo, 1, 0, 1);
            if (parser.hasErrors()) {
                LOG.error("Errors in excel sheet " + parser.getErrors());
                throw new RuntimeException("Errors in excel sheet " + parser.getErrors());
            }
        } catch (IOException e) {
            LOG.error("Failed to load the stored excel file ", e);
            throw new RuntimeException("Failed to load the stored excel file ", e);
        }
        return expenses;
    }

    @RequestMapping(method = RequestMethod.GET, value = "units/sales/target/update/monthly", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void updateMonthlySalesTargetData(@RequestParam(name = "fileName") final String fileName) {
        // String fileName = "sales_target_h1_2017.xlsx";
        String outputFile = props.getBasePath() + "/targets/monthly/" + fileName;
        GoogleSheetLoader loader = new GoogleSheetLoader();
        try {
            LOG.info("Downloading file {} ", fileName);
            File file = loader.getFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
                    TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
            if (file == null) {
                throw new RuntimeException("File Not Found " + fileName);
            }
            loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
                    AppConstants.EXCEL_MIME_TYPE, outputFile);
            dataService.addMonthlyTargets(outputFile);
            cache.refreshTargets(AppUtils.getBusinessDate());
        } catch (IOException e) {
            LOG.error("Failed to download the file", e);
        }

    }

    public ProjectionsDataCache getProjections() {
        return projections;
    }

    public AnalyticsProperties getProps() {
        return props;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.analytics.controller.AbstractSalesResource#getSQSService()
     */
    @Override
    public SQSMessageService getSQSService() {
        return sqsService;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.analytics.controller.AbstractSalesResource#
     * getSalesDataCacheService()
     */
    @Override
    public SalesDataCacheService getSalesDataCacheService() {
        return cache;
    }

}
