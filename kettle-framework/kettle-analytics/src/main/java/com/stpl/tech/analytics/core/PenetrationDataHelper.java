/**
 * 
 */
package com.stpl.tech.analytics.core;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.service.impl.AnalyticsProperties;
import com.stpl.tech.kettle.data.dao.impl.PosMetadataDaoImpl;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

/**
 * <AUTHOR>
 *
 */
public class PenetrationDataHelper {

	private static final Set<Integer> primaryProductIds;
	private static final Logger LOG = LoggerFactory.getLogger(PosMetadataDaoImpl.class);

	static {
		primaryProductIds = Stream.of(10, 11, 12, 14, 15, 50).collect(Collectors.toCollection(HashSet::new));
	}

	public AggregatePenetrationData getPenetrations(boolean hasGiftCardOffer, MasterDataCache masterCache,
													Order order, AnalyticsProperties props) {
		AggregatePenetrationData a = new AggregatePenetrationData();
		if (order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
			return a;
		}
		int ticketMultipler = 1;
		BigDecimal multiplier = new BigDecimal(1.0d);
		boolean isPrimaryProduct = false;
		if (OrderStatus.CANCELLED.equals(order.getStatus())
				|| OrderStatus.CANCELLED_REQUESTED.equals(order.getStatus())) {
			ticketMultipler = -1;
			multiplier = new BigDecimal(-1.0d);
		}
		Set<Integer> seasonalProductIds = getSeasonalProducts(masterCache);
		boolean isCOD = UnitCategory.COD.name().equals(order.getSource());
		boolean onlyGiftCard = true;
		boolean onlyBeverage = true;
		boolean onlySubscriptionProduct = true;
		int count = 1 * ticketMultipler;
		BigDecimal merchandiseSales = BigDecimal.ZERO;
		for (OrderItem item : order.getOrders()) {
			Product p = masterCache.getProduct(item.getProductId());
			try {
				if (p.getSubType() == props.getSubscriptionProductSubType()) {
					a.setUnitSubscriptionCount(a.getUnitSubscriptionCount() + 1);
				} else {
					onlySubscriptionProduct = false;
				}
			} catch (Exception e) {
				LOG.info("Error setting subscription Count:{} ", e);
			}
			if (!isGiftCard(p)) {
				onlyGiftCard = false;
			}
			if (!isBeverage(p)) {
				onlyBeverage = false;
			}
			if (!isCOD) {
				a.setFood(isFood(p) ? a.getFood() + (item.getQuantity() * ticketMultipler) : a.getFood());
				a.setCakes(isCakes(p) ? a.getCakes() + (item.getQuantity() * ticketMultipler) : a.getCakes());
				a.setCold(isCold(p) ? a.getCold() + (item.getQuantity() * ticketMultipler) : a.getCold());
				a.setHot(isHot(p) ? a.getHot() + (item.getQuantity() * ticketMultipler) : a.getHot());
				a.setNonVeg(isNonVeg(p) ? a.getNonVeg() + (item.getQuantity() * ticketMultipler) : a.getNonVeg());
				a.setMeals(isMeals(p) ? a.getMeals() + (item.getQuantity() * ticketMultipler) : a.getMeals());
				a.setMerchandise(isMerchandise(p) ? a.getMerchandise() + (item.getQuantity() * ticketMultipler)
						: a.getMerchandise());
				a.setRegular(a.getRegular()
						+ (regularCount(item.getDimension(), p, masterCache) ? count * item.getQuantity() : 0));
				a.setFull(a.getFull()
						+ (fullCount(item.getDimension(), p, masterCache) ? count * item.getQuantity() : 0));
				a.setSeasonal(
						isSeasonal(seasonalProductIds, p) ? a.getSeasonal() + (item.getQuantity() * ticketMultipler)
								: a.getSeasonal());
				a.setSeasonalProductOne(isSeasonal(AppConstants.SEASONAL_PRODUCT_IDS_1,p)?a.getSeasonalProductOne()+(item.getQuantity() * ticketMultipler)
						: a.getSeasonalProductOne());
				a.setSeasonalProductTwo(isSeasonal(AppConstants.SEASONAL_PRODUCT_IDS_2,p)?a.getSeasonalProductTwo()+(item.getQuantity() * ticketMultipler)
						: a.getSeasonalProductTwo());

				a.setSeasonalProductThree(isSeasonal(AppConstants.SEASONAL_PRODUCT_IDS_3,p)?a.getSeasonalProductThree()+(item.getQuantity() * ticketMultipler)
						: a.getSeasonalProductThree());

				if (isMerchandise(p)) {
					merchandiseSales = AppUtils.add(merchandiseSales, item.getAmount());
				}
			}

			if (hasGiftCardOffer) {
				a.setGiftCards(a.getGiftCards() == count || isGiftCard(p) ? count : 0);
			}
			if (isPrimaryProduct(p)) {
				isPrimaryProduct = true;
			}
			if (isCombo(p)) {
				if (item.getComposition() != null && item.getComposition().getMenuProducts() != null) {
					for (OrderItem cItem : item.getComposition().getMenuProducts()) {
						Product cp = masterCache.getProduct(cItem.getProductId());

						if (!isBeverage(cp)) {
							onlyBeverage = false;
						}
						if (!isCOD) {
							a.setCakes(isCakes(cp) ? a.getCakes() + (cItem.getQuantity() * ticketMultipler)
									: a.getCakes());
							a.setCold(isCold(cp) ? a.getCold() + (cItem.getQuantity() * ticketMultipler) : a.getCold());
							a.setHot(isHot(cp) ? a.getHot() + (cItem.getQuantity() * ticketMultipler) : a.getHot());
							a.setFood(isFood(cp) ? a.getFood() + (cItem.getQuantity() * ticketMultipler) : a.getFood());
							a.setNonVeg(isNonVeg(cp) ? a.getNonVeg() + (cItem.getQuantity() * ticketMultipler)
									: a.getNonVeg());
							a.setMeals(isMeals(cp) ? a.getMeals() + (cItem.getQuantity() * ticketMultipler)
									: a.getMeals());
							a.setMerchandise(
									isMerchandise(cp) ? a.getMerchandise() + (cItem.getQuantity() * ticketMultipler)
											: a.getMerchandise());
							a.setSeasonal(isSeasonal(seasonalProductIds, cp)
									? a.getSeasonal() + (cItem.getQuantity() * ticketMultipler)
									: a.getSeasonal());
						}
						if (hasGiftCardOffer) {
							a.setGiftCards(a.getGiftCards() == count || isGiftCard(cp) ? count : 0);
						}

						if (isPrimaryProduct(cp)) {
							isPrimaryProduct = true;
						}
					}
				}
			}

		}

//		a.setOverallSystemSubscriptionCount(a.getOverallSystemSubscriptionCount()+1);
		a.setMerchandiseSales(AnalyticsUtil.multiply(multiplier, merchandiseSales));
		if (!isCOD) {
			a.setNetSale(AnalyticsUtil.multiply(multiplier, order.getTransactionDetail().getTaxableAmount()));
		}
		a.setBeverage(onlyBeverage ? count : 0);
		a.setNewCustomer(!isCOD && order.isNewCustomer() ? count : 0);
		a.setNewCustWithNoPriPrd(!isCOD && order.isNewCustomer() && !isPrimaryProduct ? count : 0);
		a.setDineIn(
				!isCOD && order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard && !onlySubscriptionProduct
						? count
						: 0);
		a.setDelivery(
				isCOD && order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard && !onlySubscriptionProduct
						? count
						: 0);
		a.setCustomer(order.getCustomerId() > 5 && !isCOD
				&& order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard && !onlySubscriptionProduct
						? count
						: 0);
		a.setTotal(
				order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard && !onlySubscriptionProduct
						? count
						: 0);
		if (hasGiftCardOffer) {
			a.setTotalTicketForGC(
					order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard && !onlySubscriptionProduct
							? count
							: 0);

		}
		return a;
	}

	/**
	 * @param p
	 * @return
	 */
	private boolean isSeasonal(Set<Integer> seasonalProductIds, Product p) {
		return seasonalProductIds.contains(p.getId());
	}

	private Set<Integer> getSeasonalProducts(MasterDataCache masterCache) {
		Set<Integer> seasonalProductIds = new HashSet<>();
		if (seasonalProductIds.size() == 0) {
			ListData seasonal = masterCache.getItemPerTicket().get("SEASONAL");
			if (seasonal != null) {
				for (IdCodeName idCodeName : seasonal.getContent()) {
					seasonalProductIds.add(Integer.valueOf(idCodeName.getCode()));
				}
			}
		}
		return seasonalProductIds;
	}

	/**
	 * @param p
	 * @return
	 */
	private boolean isPrimaryProduct(Product p) {
		return primaryProductIds.contains(p.getId());
	}

	/**
	 * @param p
	 * @return
	 */

	private boolean isMerchandise(Product p) {
		return (9 == p.getType()) && !isGiftCard(p);
	}

	/**
	 * @param p
	 * @return
	 */
	private boolean isMeals(Product p) {
		return isFood(p) && 704 == p.getSubType();
	}

	/**
	 * @param p
	 * @return
	 */
	private boolean isCakes(Product p) {
		return 10 == p.getType() && (1001 == p.getSubType() || 1002 == p.getSubType());
	}

	private boolean isCold(Product p) {
		return 6 == p.getType();
	}

	private boolean isFood(Product p) {
		return 7 == p.getType();
	}

	private boolean isBeverage(Product p) {
		return isHot(p) || isCold(p);
	}

	private boolean isHot(Product p) {
		return 5 == p.getType();
	}

	private boolean isCombo(Product p) {
		return AppConstants.COMBO_TAX_CODE.equals(p.getTaxCode());
	}

	private boolean isGiftCard(Product p) {
//		LOG.info("Product's tax code :{}", p.getTaxCode());
		boolean ans =  AppConstants.GIFT_CARD_TAX_CODE.equals(p.getTaxCode());
		return ans ;
	}

	private boolean isNonVeg(Product p) {
		return isFood(p) && p.getAttribute() != null && AppConstants.PRODUCT_ATTRIBUTE_NON_VEG.equals(p.getAttribute());
	}

	private boolean regularCount(String dimension, Product p, MasterDataCache masterCache) {
		return (isHot(p) || isCold(p)) && AppConstants.DIMESION_REGULAR_STRING.equals(dimension)
				&& hasRegularAndFullBoth(p, masterCache);
	}

	/**
	 * @param p
	 * @return
	 */
	private boolean hasRegularAndFullBoth(Product p, MasterDataCache masterCache) {
		boolean hasRegular = false;
		boolean hasFull = false;
		ListData profile = masterCache.getDimensionProfile(p.getDimensionProfileId());
		if (profile != null && profile.getContent() != null && profile.getContent().size() > 0) {
			for (IdCodeName price : profile.getContent()) {
				if (AppConstants.DIMESION_REGULAR_STRING.equals(price.getCode())) {
					hasRegular = true;
					continue;
				}
				if (AppConstants.DIMESION_FULL_STRING.equals(price.getCode())) {
					hasFull = true;
					continue;
				}
			}
		}
		return hasRegular && hasFull;
	}

	private boolean fullCount(String dimension, Product p, MasterDataCache masterCache) {
		return (isHot(p) || isCold(p)) && AppConstants.DIMESION_FULL_STRING.equals(dimension)
				&& hasRegularAndFullBoth(p, masterCache);
	}
}
