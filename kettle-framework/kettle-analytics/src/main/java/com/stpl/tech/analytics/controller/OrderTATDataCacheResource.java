
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.controller;

import com.stpl.tech.analytics.model.UnitOrderTATDomain;
import com.stpl.tech.analytics.service.OrderTATCacheService;
import com.stpl.tech.analytics.service.impl.OrderTATCacheServiceImpl;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import static com.stpl.tech.analytics.core.AnalyticsConstants.ANALYTICS_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;
import static com.stpl.tech.analytics.core.AnalyticsConstants.ORDER_TAT_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.SEPARATOR;

@RestController
@RequestMapping(API_VERSION  + SEPARATOR + ORDER_TAT_ROOT_CONTEXT)
public class OrderTATDataCacheResource {

    private static final Logger LOG = LoggerFactory.getLogger(OrderTATDataCacheResource.class);


    private static boolean SCHEDULE_STORED_PROC = true;

    @Autowired
    private OrderTATCacheService orderTATCacheService;

    @GetMapping("refresh/all")
    public void refreshOrderTATCache() {
        orderTATCacheService.refreshOrderTATData();
    }

    @GetMapping("refresh/unit")
    public void refreshOrderTATCache(@RequestParam int unitId) {
        orderTATCacheService.refreshOrderTATData(unitId);
    }
    @GetMapping("all")
    public Map<String, UnitOrderTATDomain> getOrderTATData() {
        return orderTATCacheService.getOrderTATData();
    }

    @GetMapping("unit")
    public Map<String, UnitOrderTATDomain> getOrderTATData(@RequestParam int unitId) {
        return orderTATCacheService.getOrderTATData(unitId);
    }

    @GetMapping("unit/brand")
    public Map<String, UnitOrderTATDomain> getOrderTATData(@RequestParam int unitId,@RequestParam int brandId) {
        return orderTATCacheService.getOrderTATData(unitId, brandId);
    }

    @GetMapping("unit/brand/type")
    public UnitOrderTATDomain getOrderTATData(@RequestParam int unitId,@RequestParam int brandId,@RequestParam String type) {
        return orderTATCacheService.getOrderTATData(unitId, brandId, type);
    }

    @Scheduled(cron = "0 0,15,30,45 * * * *", zone = "GMT+05:30")
    public void updateByScheduledStoredProc() {
        if (SCHEDULE_STORED_PROC) {
            LOG.info("$$$$$$$$$$$$$$$ Order TAT Agg. Scheduled Stored Proc called $$$$$$$$$$$$$$$");
            orderTATCacheService.callOrderTATProc(AppUtils.getCurrentDate());
        }
    }

    @Scheduled(cron = "0 45 05 * * *", zone = "GMT+05:30")
    public void updateByStoredProcEOD() {
        if (SCHEDULE_STORED_PROC) {
            LOG.info("$$$$$$$$$$$$$$$ Order TAT Agg. EOD Stored Proc called $$$$$$$$$$$$$$$");
            orderTATCacheService.callOrderTATProc(AppUtils.getPreviousBusinessDate());
        }
    }

    @PostMapping("set-status")
    public void setStoredProcScheduleStatus(@RequestParam Boolean status) {
        SCHEDULE_STORED_PROC = status;
    }

}