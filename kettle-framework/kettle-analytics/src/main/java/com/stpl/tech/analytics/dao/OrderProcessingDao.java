package com.stpl.tech.analytics.dao;

import com.stpl.tech.kettle.domain.model.OrderResponse;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface OrderProcessingDao extends MongoRepository<OrderResponse,String> {

	/*@Query("select from OrderResponse where order.billingServerTime >= :billingTime and order.status <> :status")*/
	@Query("{'order.billingServerTime' : {'$gte' : ?0}, 'order.status' : {'$ne' : ?1}  }")
	public List<OrderResponse> findByOrderTime(final Date billingTime,final String status);
	
	/*@Query("select from OrderResponse where order.billingServerTime > :billingStartTime and order.billingServerTime <= :billingEndTime and order.status <> :status")*/
	@Query("{'order.billingServerTime' : {'$gt' : ?0, '$lte' : ?1}, 'order.status' : {'$ne' : ?2}  }")
	public List<OrderResponse> findByOrderTimeForARange(final Date billingStartTime,final Date billingEndTime,final String status);
	
	/*@Query("select from OrderResponse where order.orderId = :orderId")*/
	@Query("{'order.orderId' : ?0}")
	public List<OrderResponse> findByOrderId(final int orderId);
	
	@Query("{'order.billingServerTime' : {'$gte' : ?0}, 'order.status' : {'$ne' : ?1} , 'order.unitId' : {'$eq' : ?2} }")
	public List<OrderResponse> findByOrderTimeAndUnit(final Date billingTime,final String status, int unitId);

	@Query("{'order.billingServerTime' : {'$gt' : ?0, '$lte' : ?1}, 'order.status' : {'$ne' : ?2}  , 'order.unitId' : {'$eq' : ?3} }}")
	public List<OrderResponse> findByOrderTimeForARangeAndUnit(final Date billingStartTime,final Date billingEndTime,final String status, int unitId);
	
}
