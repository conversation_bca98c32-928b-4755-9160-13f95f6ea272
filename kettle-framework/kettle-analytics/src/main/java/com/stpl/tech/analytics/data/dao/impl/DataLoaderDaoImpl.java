/**
 * 
 */
package com.stpl.tech.analytics.data.dao.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.stereotype.Repository;

import com.stpl.tech.analytics.data.dao.DataLoaderDao;
import com.stpl.tech.kettle.sales.domain.model.CustomerData;
import com.stpl.tech.kettle.sales.domain.model.SalesRecord;
import com.stpl.tech.master.util.ServiceUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.ExecutionEnvironment;

/**
 * <AUTHOR>
 *
 */
@Repository
public class DataLoaderDaoImpl implements DataLoaderDao {

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.analytics.data.dao.CustomerDataLoaderDao#loadCustomerData(
	 * int, int)
	 */
	@Override
	public List<CustomerData> loadCustomerData(ExecutionEnvironment env, int startCustomerId, int endCustomerId)
			throws SQLException {
		List<CustomerData> customers = new ArrayList<>();
		DataSource dataSource = ServiceUtil.getDataSourceBean(env);
		Connection connection = dataSource.getConnection();
		PreparedStatement statement = connection.prepareStatement("SELECT \n"+
    "ci.CUSTOMER_ID, \n"+
    "ci.FIRST_NAME, \n"+
    "ci.CONTACT_NUMBER, \n"+
    "ci.EMAIL_ID, \n"+
    "ci.IS_NUMBER_VERIFIED, \n"+
    "ci.IS_EMAIL_VERIFIED, \n"+
    "ci.NUMBER_VERIFICATION_TIME, \n"+
    "ci.EMAIL_VERIFICATION_TIME, \n"+
    "ci.ACQUISITION_SOURCE, \n"+
    "ci.ACQUISITION_TOKEN, \n"+
    "ci.REGISTRATION_UNIT_ID, \n"+
    "ls.ACQUIRED_POINTS, \n"+
    "ls.CUMULATIVE_POINTS, \n"+
    "ls.AVAILED_SIGNUP_OFFER, \n"+
    "TOTAL_UNITS_VISITED , \n"+
    "FIRST_ORDER_ID , \n"+
    "c.LAST_ORDER_ID , \n"+
    "FIRST_ORDER_DATE , \n"+
    "LAST_ORDER_DATE , \n"+
    "TICKET_COUNT , \n"+
    "ZERO_AMOUNT_TICKET_COUNT , \n"+
    "ZERO_AMOUNT_DINE_IN_TICKET_COUNT , \n"+
    "ZERO_AMOUNT_DELIVERY_TICKET_COUNT , \n"+
    "CANCELLED_TICKET_COUNT , \n"+
    "TICKET_WITH_OFFER , \n"+
    "TICKET_WITH_REDEMPTION , \n"+
    "DINE_IN_TICKET , \n"+
    "DELIVERY_TICKET , \n"+
    "TAKE_AWAY_TICKET , \n"+
    "ZOMATO_TICKET , \n"+
    "SWIGGY_TICKET , \n"+
    "FOOD_PANDA_TICKET , \n"+
    "UBER_EATS_TICKET , \n"+
    "OLD_APP_TICKET , \n"+
    "WEB_APP_TICKET , \n"+
    "CALL_CENTER_TICKET , \n"+
    "OTHER_PARTNER_TICKET , \n"+
    "TICKET_ON_MONDAY , \n"+
    "TICKET_ON_TUESDAY , \n"+
    "TICKET_ON_WEDNESDAY , \n"+
    "TICKET_ON_THURSDAY , \n"+
    "TICKET_ON_FRIDAY , \n"+
    "TICKET_ON_SATURDAY , \n"+
    "TICKET_ON_SUNDAY , \n"+
    "TICKET_ON_WEEKDAY , \n"+
    "TICKET_ON_WEEKEND , \n"+
    "TICKET_IN_BREAKFAST , \n"+
    "TICKET_IN_LUNCH , \n"+
    "TICKET_IN_EVENING , \n"+
    "TICKET_IN_DINNER , \n"+
    "TICKET_IN_POST_DINNER , \n"+
    "TICKET_IN_NIGHT , \n"+
    "ONE_NPS_TICKET , \n"+
    "TWO_NPS_TICKET , \n"+
    "THREE_NPS_TICKET , \n"+
    "FOUR_NPS_TICKET , \n"+
    "FIVE_NPS_TICKET , \n"+
    "SIX_NPS_TICKET , \n"+
    "SEVEN_NPS_TICKET , \n"+
    "EIGHT_NPS_TICKET , \n"+
    "NINE_NPS_TICKET , \n"+
    "TEN_NPS_TICKET , \n"+
    "LAST_NPS_SCORE , \n"+
    "NEGATIVE_NPS_TICKET , \n"+
    "POSITIVE_NPS_TICKET , \n"+
    "NEUTRAL_NPS_TICKET , \n"+
    "TOTAL_SPEND , \n"+
    "TOTAL_DISCOUNT , \n"+
    "MINIMUM_APC , \n"+
    "MAXIMUM_APC , \n"+
    "DELIVERY_SPEND , \n"+
    "DELIVERY_DISCOUNT , \n"+
    "DELIVERY_MINIMUM_APC , \n"+
    "DELIVERY_MAXIMUM_APC , \n"+
    "DINE_IN_SPEND , \n"+
    "DINE_IN_DISCOUNT , \n"+
    "DINE_IN_MINIMUM_APC , \n"+
    "DINE_IN_MAXIMUM_APC , \n"+
    "CASH_TICKET , \n"+
    "CARD_TICKET , \n"+
    "AMEX_TICKET , \n"+
    "PAYTM_TICKET , \n"+
    "GIFT_CARD_TICKET , \n"+
    "MOBIKWIK_TICKET , \n"+
    "ONLINE_PAYMENT_TICKET , \n"+
    "OTHER_PAYMENT_TICKET , \n"+
    "CASH_SPEND , \n"+
    "CARD_SPEND , \n"+
    "AMEX_SPEND , \n"+
    "PAYTM_SPEND , \n"+
    "GIFT_CARD_SPEND , \n"+
    "MOBIKWIK_SPEND , \n"+
    "ONLINE_SPEND , \n"+
    "OTHER_PAYMENT_SPEND , \n"+
    "ONE_FEEDBACK_TICKET , \n"+
    "TWO_FEEDBACK_TICKET , \n"+
    "THREE_FEEDBACK_TICKET , \n"+
    "FOUR_FEEDBACK_TICKET , \n"+
    "FIVE_FEEDBACK_TICKET , \n"+
    "TICKET_WITH_FOOD , \n"+
    "TICKET_WITH_VEG , \n"+
    "TICKET_WITH_NON_VEG , \n"+
    "TICKET_WITH_HOT , \n"+
    "TICKET_WITH_COLD , \n"+
    "TICKET_WITH_BAKERY , \n"+
    "TICKET_WITH_COMBO , \n"+
    "TICKET_WITH_MERCHANDISE , \n"+
    "TICKET_WITH_OTHER , \n"+
    "TICKET_WITH_GIFT_CARD , \n"+
    "PEOPLE_PER_TICKET , \n"+
    "MINIMUM_PEOPLE_PER_ORDER , \n"+
    "MAXIMUM_PEOPLE_PER_ORDER , \n"+
    "SPLIT_PAYMENT_TICKET , \n"+
    "LAST_PAYMENT_MODE , \n"+
    "FIRST_UNIT_ID , \n"+
    "FIRST_UNIT_NAME , \n"+
    "LAST_UNIT_ID , \n"+
    "LAST_UNIT_NAME , \n"+
    "LAST_FEEDBACK_SCORE , \n"+
    "DAY_GAP_SINCE_LAST_ORDER , \n"+
    "TOTAL_APC , \n"+
    "DELIVERY_APC , \n"+
    "DINE_IN_APC \n"+
"FROM \n"+
    "KETTLE_DUMP.CUSTOMER_INFO ci, \n"+
    "KETTLE_DUMP.LOYALTY_SCORE ls, \n"+
    "CLM_ANALYTICS.CUSTOMER_DATA c \n"+
"WHERE \n"+
    "ci.CUSTOMER_ID = ls.CUSTOMER_ID \n"+
    "AND c.CUSTOMER_ID = ci.CUSTOMER_ID \n"+
        "AND ci.CUSTOMER_ID >= ? \n"+
        "AND ci.CUSTOMER_ID < ?");
		statement.setInt(1, startCustomerId);
		statement.setInt(2, endCustomerId);

		ResultSet rs = statement.executeQuery();
		while (rs.next()) {
			CustomerData c = new CustomerData();
			c.setCustomerId(rs.getInt("CUSTOMER_ID"));
			c.setCustomerName(rs.getString("FIRST_NAME"));
			c.setContactNumber(rs.getString("CONTACT_NUMBER"));
			c.setEmailId(rs.getString("EMAIL_ID"));
			c.setNumberVerified(AppConstants.getValue(rs.getString("IS_NUMBER_VERIFIED")));
			c.setEmailVerified(AppConstants.getValue(rs.getString("IS_EMAIL_VERIFIED")));
			c.setNumberVerificationTime(rs.getTimestamp("NUMBER_VERIFICATION_TIME"));
			c.setEmailVerificationTime(rs.getTimestamp("EMAIL_VERIFICATION_TIME"));
			c.setAquisitionSource(rs.getString("ACQUISITION_SOURCE"));
			c.setAquisitionToken(rs.getString("ACQUISITION_TOKEN"));
			c.setRegistrationUnitId(rs.getInt("REGISTRATION_UNIT_ID"));
			c.setCurrentPoints(rs.getInt("ACQUIRED_POINTS"));
			c.setTotalPoints(rs.getInt("CUMULATIVE_POINTS"));
			c.setRedeemedPoints(c.getTotalPoints() - c.getCurrentPoints());
			c.setAvailedSignupOffer(AppConstants.getValue(rs.getString("AVAILED_SIGNUP_OFFER")));
			c.setUnitsVisited(rs.getInt("TOTAL_UNITS_VISITED"));
			c.setFirstOrderId(rs.getInt("FIRST_ORDER_ID"));
			c.setLastOrderId(rs.getInt("LAST_ORDER_ID"));
			c.setFirstOrderDate(rs.getTimestamp("FIRST_ORDER_DATE"));
			c.setLastOrderDate(rs.getTimestamp("LAST_ORDER_DATE"));
			c.setTicketCount(rs.getInt("TICKET_COUNT"));
			c.setZeroAmountTicketCount(rs.getInt("ZERO_AMOUNT_TICKET_COUNT"));
			c.setZeroAmountDineInTicketCount(rs.getInt("ZERO_AMOUNT_DINE_IN_TICKET_COUNT"));
			c.setZeroAmountDeliveryTicketCount(rs.getInt("ZERO_AMOUNT_DELIVERY_TICKET_COUNT"));
			c.setCancelledOrderCount(rs.getInt("CANCELLED_TICKET_COUNT"));
			c.setTicketWithOffer(rs.getInt("TICKET_WITH_OFFER"));
			c.setTicketWithRedemption(rs.getInt("TICKET_WITH_REDEMPTION"));
			c.setDineInTicket(rs.getInt("DINE_IN_TICKET"));
			c.setDeliveryTicket(rs.getInt("DELIVERY_TICKET"));
			c.setTakeawayTicket(rs.getInt("TAKE_AWAY_TICKET"));
			c.setZomatoTicket(rs.getInt("ZOMATO_TICKET"));
			c.setSwiggyTicket(rs.getInt("SWIGGY_TICKET"));
			c.setFoodPandaTicket(rs.getInt("FOOD_PANDA_TICKET"));
			c.setUberEatsTicket(rs.getInt("UBER_EATS_TICKET"));
			c.setOldAppTicket(rs.getInt("OLD_APP_TICKET"));
			c.setWebAppTicket(rs.getInt("WEB_APP_TICKET"));
			c.setCallCenterTicket(rs.getInt("CALL_CENTER_TICKET"));
			c.setOtherPartnerTicket(rs.getInt("OTHER_PARTNER_TICKET"));
			c.setTicketOnMonday(rs.getInt("TICKET_ON_MONDAY"));
			c.setTicketOnTuesday(rs.getInt("TICKET_ON_TUESDAY"));
			c.setTicketOnWednesday(rs.getInt("TICKET_ON_WEDNESDAY"));
			c.setTicketOnThursday(rs.getInt("TICKET_ON_THURSDAY"));
			c.setTicketOnFriday(rs.getInt("TICKET_ON_FRIDAY"));
			c.setTicketOnSaturday(rs.getInt("TICKET_ON_SATURDAY"));
			c.setTicketOnSunday(rs.getInt("TICKET_ON_SUNDAY"));
			c.setTicketOnWeekday(rs.getInt("TICKET_ON_WEEKDAY"));
			c.setTicketOnWeekend(rs.getInt("TICKET_ON_WEEKEND"));
			c.setTicketInBreakfast(rs.getInt("TICKET_IN_BREAKFAST"));
			c.setTicketInLunch(rs.getInt("TICKET_IN_LUNCH"));
			c.setTicketInEvening(rs.getInt("TICKET_IN_EVENING"));
			c.setTicketInDinner(rs.getInt("TICKET_IN_DINNER"));
			c.setTicketInPostDinner(rs.getInt("TICKET_IN_POST_DINNER"));
			c.setTicketInNight(rs.getInt("TICKET_IN_NIGHT"));
			c.setOneNPSTicket(rs.getInt("ONE_NPS_TICKET"));
			c.setTwoNPSTicket(rs.getInt("TWO_NPS_TICKET"));
			c.setThreeNPSTicket(rs.getInt("THREE_NPS_TICKET"));
			c.setFourNPSTicket(rs.getInt("FOUR_NPS_TICKET"));
			c.setFiveNPSTicket(rs.getInt("FIVE_NPS_TICKET"));
			c.setSixNPSTicket(rs.getInt("SIX_NPS_TICKET"));
			c.setSevenNPSTicket(rs.getInt("SEVEN_NPS_TICKET"));
			c.setEightNPSTicket(rs.getInt("EIGHT_NPS_TICKET"));
			c.setNineNPSTicket(rs.getInt("NINE_NPS_TICKET"));
			c.setTenNPSTicket(rs.getInt("TEN_NPS_TICKET"));
			c.setLastNPSScore(rs.getInt("LAST_NPS_SCORE"));
			c.setNegativeNPSTicket(rs.getInt("NEGATIVE_NPS_TICKET"));
			c.setPositiveNPSTicket(rs.getInt("POSITIVE_NPS_TICKET"));
			c.setNeutralNPSTicket(rs.getInt("NEUTRAL_NPS_TICKET"));
			c.setSpend(rs.getBigDecimal("TOTAL_SPEND"));
			c.setDiscount(rs.getBigDecimal("TOTAL_DISCOUNT"));
			c.setMinApc(rs.getBigDecimal("MINIMUM_APC"));
			c.setMaxApc(rs.getBigDecimal("MAXIMUM_APC"));
			c.setDeliverySpend(rs.getBigDecimal("DELIVERY_SPEND"));
			c.setDeliveryDiscount(rs.getBigDecimal("DELIVERY_DISCOUNT"));
			c.setMinDeliveryApc(rs.getBigDecimal("DELIVERY_MINIMUM_APC"));
			c.setMaxDeliveryApc(rs.getBigDecimal("DELIVERY_MAXIMUM_APC"));
			c.setDineInSpend(rs.getBigDecimal("DINE_IN_SPEND"));
			c.setDineInDiscount(rs.getBigDecimal("DINE_IN_DISCOUNT"));
			c.setMinDineInApc(rs.getBigDecimal("DINE_IN_MINIMUM_APC"));
			c.setMaxDineInApc(rs.getBigDecimal("DINE_IN_MAXIMUM_APC"));
			c.setCashTicket(rs.getInt("CASH_TICKET"));
			c.setCardTicket(rs.getInt("CARD_TICKET"));
			c.setAmexTicket(rs.getInt("AMEX_TICKET"));
			c.setPaytmTicket(rs.getInt("PAYTM_TICKET"));
			c.setGiftCardTicket(rs.getInt("GIFT_CARD_TICKET"));
			c.setMobikwikTicket(rs.getInt("MOBIKWIK_TICKET"));
			c.setOnlineTicket(rs.getInt("ONLINE_PAYMENT_TICKET"));
			c.setOtherPaymentTicket(rs.getInt("OTHER_PAYMENT_TICKET"));
			c.setCashSpend(rs.getBigDecimal("CASH_SPEND"));
			c.setCardSpend(rs.getBigDecimal("CARD_SPEND"));
			c.setAmexSpend(rs.getBigDecimal("AMEX_SPEND"));
			c.setPaytmSpend(rs.getBigDecimal("PAYTM_SPEND"));
			c.setGiftCardSpend(rs.getBigDecimal("GIFT_CARD_SPEND"));
			c.setMobikwikSpend(rs.getBigDecimal("MOBIKWIK_SPEND"));
			c.setOnlineSpend(rs.getBigDecimal("ONLINE_SPEND"));
			c.setOtherSpend(rs.getBigDecimal("OTHER_PAYMENT_SPEND"));
			c.setOneFeedbackTicket(rs.getInt("ONE_FEEDBACK_TICKET"));
			c.setTwoFeedbackTicket(rs.getInt("TWO_FEEDBACK_TICKET"));
			c.setThreeFeedbackTicket(rs.getInt("THREE_FEEDBACK_TICKET"));
			c.setFourFeedbackTicket(rs.getInt("FOUR_FEEDBACK_TICKET"));
			c.setFiveFeedbackTicket(rs.getInt("FIVE_FEEDBACK_TICKET"));
			c.setTicketWithFood(rs.getInt("TICKET_WITH_FOOD"));
			c.setTicketWithVeg(rs.getInt("TICKET_WITH_VEG"));
			c.setTicketWithNonVeg(rs.getInt("TICKET_WITH_NON_VEG"));
			c.setTicketWithHot(rs.getInt("TICKET_WITH_HOT"));
			c.setTicketWithCold(rs.getInt("TICKET_WITH_COLD"));
			c.setTicketWithBakery(rs.getInt("TICKET_WITH_BAKERY"));
			c.setTicketWithCombo(rs.getInt("TICKET_WITH_COMBO"));
			c.setTicketWithMerchandise(rs.getInt("TICKET_WITH_MERCHANDISE"));
			c.setTicketWithOthers(rs.getInt("TICKET_WITH_OTHER"));
			c.setTicketWithGiftCard(rs.getInt("TICKET_WITH_GIFT_CARD"));
			c.setPeoplePerTicket(rs.getInt("PEOPLE_PER_TICKET"));
			c.setMinPeoplePerOrder(rs.getInt("MINIMUM_PEOPLE_PER_ORDER"));
			c.setMaxPeoplePerOrder(rs.getInt("MAXIMUM_PEOPLE_PER_ORDER"));
			c.setSplitPaymentTicket(rs.getInt("SPLIT_PAYMENT_TICKET"));
			c.setLastPaymentMode(rs.getInt("LAST_PAYMENT_MODE"));
			c.setFirstUnitId(rs.getInt("FIRST_UNIT_ID"));
			c.setFirstUnitName(rs.getString("FIRST_UNIT_NAME"));
			c.setLastUnitId(rs.getInt("LAST_UNIT_ID"));
			c.setLastUnitName(rs.getString("LAST_UNIT_NAME"));
			c.setLastFeedbackScore(rs.getInt("LAST_FEEDBACK_SCORE"));
			c.setDaysGapSinceLastOrder(rs.getInt("DAY_GAP_SINCE_LAST_ORDER"));
			c.setApc(rs.getBigDecimal("TOTAL_APC"));
			c.setDeliveryApc(rs.getBigDecimal("DELIVERY_APC"));
			c.setDineInApc(rs.getBigDecimal("DINE_IN_APC")); 
			c.setOnlyGiftCardTicket(rs.getInt("ONLY_GIFT_CARD_TICKET"));
			
			customers.add(c);
		}

		return customers;
	}

	
	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.stpl.tech.analytics.data.dao.CustomerDataLoaderDao#loadCustomerData(
	 * int, int)
	 */
	@Override
	public List<SalesRecord> loadSaleData(ExecutionEnvironment env, int startOrderId, int endOrderId)
			throws SQLException {
		List<SalesRecord> customers = new ArrayList<>();
		DataSource dataSource = ServiceUtil.getDataSourceBean(env);
		Connection connection = dataSource.getConnection();
		PreparedStatement statement = connection.prepareStatement("SELECT \n" + "ci.CUSTOMER_ID, \n"
				+ "ci.FIRST_NAME, \n" + "ci.CONTACT_NUMBER, \n" + "ci.EMAIL_ID, \n" + "ci.IS_NUMBER_VERIFIED, \n"
				+ "ci.IS_EMAIL_VERIFIED, \n" + "ci.NUMBER_VERIFICATION_TIME, \n" + "ci.EMAIL_VERIFICATION_TIME, \n"
				+ "ci.ACQUISITION_SOURCE, \n" + "ci.ACQUISITION_TOKEN, \n" + "ci.REGISTRATION_UNIT_ID, \n"
				+ "ls.ACQUIRED_POINTS, \n" + "ls.CUMULATIVE_POINTS, \n" + "ls.AVAILED_SIGNUP_OFFER \n" + "FROM \n"
				+ "KETTLE_DUMP.CUSTOMER_INFO ci, \n" + "KETTLE_DUMP.LOYALTY_SCORE ls \n" + "WHERE \n"
				+ "ci.CUSTOMER_ID = ls.CUSTOMER_ID \n" + "AND ci.CUSTOMER_ID >= ? \n" + "AND ci.CUSTOMER_ID < ?");
		statement.setInt(1, startOrderId);
		statement.setInt(2, endOrderId);

		ResultSet rs = statement.executeQuery();
		while (rs.next()) {
			CustomerData c = new CustomerData();
			c.setCustomerId(rs.getInt(1));
			c.setCustomerName(rs.getString(2));
			c.setContactNumber(rs.getString(3));
			c.setEmailId(rs.getString(4));
			c.setNumberVerified(AppConstants.getValue(rs.getString(5)));
			c.setEmailVerified(AppConstants.getValue(rs.getString(6)));
			c.setNumberVerificationTime(rs.getTimestamp(7));
			c.setEmailVerificationTime(rs.getTimestamp(8));
			c.setAquisitionSource(rs.getString(9));
			c.setAquisitionToken(rs.getString(10));
			c.setRegistrationUnitId(rs.getInt(11));
			c.setCurrentPoints(rs.getInt(12));
			c.setTotalPoints(rs.getInt(13));
			c.setAvailedSignupOffer(AppConstants.getValue(rs.getString(14)));
			c.setRedeemedPoints(c.getTotalPoints() - c.getCurrentPoints());
			//customers.add(c);
		}

		return customers;
	}

}
