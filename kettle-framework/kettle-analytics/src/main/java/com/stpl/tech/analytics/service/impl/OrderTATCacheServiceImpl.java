package com.stpl.tech.analytics.service.impl;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.analytics.core.AnalyticsConstants;
import com.stpl.tech.analytics.data.dao.OrderTATDataDao;
import com.stpl.tech.analytics.model.UnitOrderTATDomain;
import com.stpl.tech.analytics.service.OrderTATCacheService;
import com.stpl.tech.analytics.converters.UnitOrderTATConverter;
import com.stpl.tech.kettle.data.model.UnitOrderTATDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Service
public class OrderTATCacheServiceImpl implements OrderTATCacheService {

    private static final String ORDER_TAT_STORED_PROC = "CALL CALCULATE_ORDER_PREP_TIME_AGGREGATE_FOR_DATE(:in_business_date)";

    private static final Logger LOG = LoggerFactory.getLogger(OrderTATCacheServiceImpl.class);

    private static final String SEPARATOR = "#";

    private static final String BUSINESS_DATE = "in_business_date";

    private static final List<String> orderTypes = Arrays.asList(AnalyticsConstants.DINE_IN, AnalyticsConstants.COD, AnalyticsConstants.GNT, AnalyticsConstants.GNT_DINE_IN,AnalyticsConstants.PRIORITY_ORDER);

    @PersistenceContext(unitName = "TransactionDataSourcePUName")
    @Qualifier(value = "TransactionDataSourceEMFactory")
    private EntityManager entityManager;

    @Autowired
    @Qualifier(value = "AnalyticsHazelCastInstance")
    private HazelcastInstance instance;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private OrderTATDataDao orderTATDataDao;

    @Autowired
    private AnalyticsProperties properties;

    private IMap<String, UnitOrderTATDomain> unitOrderTATData;

    @PostConstruct
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void init() {
        refreshOrderTATData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, UnitOrderTATDomain> getOrderTATData() {
        return unitOrderTATData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, UnitOrderTATDomain> getOrderTATData(int unitId) {
        return unitOrderTATData.getAll(buildKey(unitId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, UnitOrderTATDomain> getOrderTATData(int unitId, int brandId) {
        return unitOrderTATData.getAll(buildKey(unitId, brandId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public UnitOrderTATDomain getOrderTATData(int unitId, int brandId, String type) {
        return unitOrderTATData.get(buildKey(unitId, brandId, type));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshOrderTATData() {
        unitOrderTATData = instance.getMap("MasterDataCache:unitOrderTATData");
        clearCache();
        createCache();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshOrderTATData(int unitId) {
        clearCache(unitId);
        createCache(unitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshOrderTATData(int unitId, int brandId) {
        clearCache(unitId, brandId);
        createCache(unitId, brandId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void refreshOrderTATData(int unitId, int brandId, String type) {
        clearCache(unitId, brandId, type);
        createCache(unitId, brandId, type);
    }

    @Override
    @Scheduled(cron = "0 2,17,32,47 * * * *", zone = "GMT+05:30")
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void syncOrderTATData() {
        LOG.info("$$$$$$$$$$$$$$$ Order TAT Cache Scheduled Refresh $$$$$$$$$$$$$$$");
        refreshOrderTATData();
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void callOrderTATProc(Date businessDate) {
        try {
           Query query = entityManager.createNativeQuery(ORDER_TAT_STORED_PROC);
           query.setParameter(BUSINESS_DATE,businessDate);
           query.executeUpdate();
        } catch (Exception e) {
            LOG.info("Unable to Call Stored Proc ", e);
        }
    }

    private void clearCache() {
        try {
            LOG.info("$$$$$$$$$$$$$$$ Clearing Order TAT Cache $$$$$$$$$$$$$$$");
            unitOrderTATData.clear();
        } catch (Exception e) {
            LOG.info("Unable to Clear Order TAT Cache");
        }
    }

    private void clearCache(Integer unitId) {
        try {
            LOG.info("$$$$$$$$$$$$$$$ Clearing Order TAT Cache for unit {} $$$$$$$$$$$$$$$", unitId);
            Set<String> keys = buildKey(unitId);
            synchronized (keys) {
                unitOrderTATData.getAll(keys).clear();
            }
        } catch (Exception e) {
            LOG.info("Unable to Clear Order TAT Cache for unit {}", unitId);
        }
    }

    private void clearCache(int unitId, int brandId) {
        try {
            LOG.info("$$$$$$$$$$$$$$$ Clearing Order TAT Cache for unit {} brand {} $$$$$$$$$$$$$$$", unitId, brandId);
            Set<String> keys = buildKey(unitId, brandId);
            synchronized (keys) {
                unitOrderTATData.getAll(keys).clear();
            }
        } catch (Exception e) {
            LOG.info("Unable to Clear Order TAT Cache for unit {} brand {}", unitId, brandId);
        }
    }

    private void clearCache(int unitId, int brandId, String type) {
        try {
            LOG.info("$$$$$$$$$$$$$$$ Clearing Order TAT Cache for unit {} brand {} type {} $$$$$$$$$$$$$$$", unitId, brandId, type);
            String key = buildKey(unitId, brandId, type);
            synchronized (key) {
                unitOrderTATData.remove(key);
            }
        } catch (Exception e) {
            LOG.info("Unable to Clear Order TAT Cache for unit {} brand {} type {}", unitId, brandId, type);
        }
    }

    private void createCache() {
        LOG.info("$$$$$$$$$$$$$$$ Creating Order TAT Cache $$$$$$$$$$$$$$$");
        List<UnitBasicDetail> units = masterCache.getAllUnits();
        if (!CollectionUtils.isEmpty(units)) {
            for (UnitBasicDetail unitBasicDetail : units) {
                createCache(unitBasicDetail.getId());
            }
        }
    }

    private void createCache(int unitId) {
       // LOG.info("$$$$$$$$$$$$$$$ Creating Order TAT Cache for {} $$$$$$$$$$$$$$$", unitId);
        addToCache(orderTATDataDao.findByUnitIdAndBusinessDate(unitId, AppUtils.getBusinessDate()));
    }

    private void createCache(int unitId, int brandId) {
        LOG.info("$$$$$$$$$$$$$$$ Creating Order TAT Cache for {}#{} $$$$$$$$$$$$$$$", unitId, brandId);
        addToCache(orderTATDataDao.findByUnitIdAndBrandIdAndBusinessDate(unitId, brandId, AppUtils.getBusinessDate()));
    }

    private void createCache(int unitId, int brandId, String type) {
        LOG.info("$$$$$$$$$$$$$$$ Creating Order TAT Cache for {}#{}#{} $$$$$$$$$$$$$$$", unitId, brandId, type);
        addToCache(orderTATDataDao.findByUnitIdAndBrandIdAndTypeAndBusinessDate(unitId, brandId, type, AppUtils.getBusinessDate()));
    }

    private String buildKey(int unitId, int brandId, String type) {
        return unitId + SEPARATOR + brandId + SEPARATOR + type;
    }

    private Set<String> buildKey(int unitId, int brandId) {
        Set<String> keys = new HashSet<>();
        orderTypes.forEach(type -> keys.add(buildKey(unitId, brandId, type)));
        return keys;
    }

    private Set<String> buildKey(int unitId) {
        Set<String> keys = new HashSet<>();
        masterCache.getBrandMetaData().forEach((k, v) -> keys.addAll(buildKey(unitId, k)));
        return keys;
    }

    private void addToCache(List<UnitOrderTATDetail> unitOrderTATDetailList) {
        if (!CollectionUtils.isEmpty(unitOrderTATDetailList)) {
            for (UnitOrderTATDetail orderTATDetail : unitOrderTATDetailList) {
                addToCache(orderTATDetail);
            }
        }
    }

    private void addToCache(UnitOrderTATDetail orderTATDetail) {
        if (Objects.nonNull(orderTATDetail)) {
            String key = buildKey(orderTATDetail.getUnitId(), orderTATDetail.getBrandId(), orderTATDetail.getType());
            synchronized (key) {
                unitOrderTATData.put(key, UnitOrderTATConverter.convert(orderTATDetail,properties));
            }
        }
    }
}
