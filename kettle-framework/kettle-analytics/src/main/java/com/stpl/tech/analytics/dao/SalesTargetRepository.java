package com.stpl.tech.analytics.dao;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.stpl.tech.analytics.model.SalesTarget;

public interface SalesTargetRepository extends MongoRepository<SalesTarget, String> {

	public List<SalesTarget> findByBusinessDate(Date businessDate);
	
	@Query("{'businessDate' : {'$gte' : ?0}}")
	public List<SalesTarget> findAllByBusinessDate(final Date minDate);
}
