/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.model.chaimonk;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.domain.model.Adapter2;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.persistence.Entity;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Document
public class ChaiMonkOrder {

    @Id
    private String _id;

    @Field
    private Integer orderId;
    @Field
    private int generatedOrderId;
    @Field
    private int token;
    @Field
    private Integer customerId;
    @Field
    private String customerName;
    @Field
    private int empId;
    @Field
    private OrderStatus orderStatus;
    @Field
    private String settlementType;
    @Field
    private int unitId;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @Field
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date businessDate;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @Field
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date billGenerationTime;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @Field
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date billCancellationTime;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @Field
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date billSettlementTime;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @Field
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date billCompletionTime;
    @Field
    private double totalAmount;
    @Field
    private double netPriceVatPercent;
    @Field
    private double netPriceVatAmount;
    @Field
    private double mrpVatPercent;
    @Field
    private double mrpVatAmount;
    @Field
    private double serviceTaxPercent;
    @Field
    private double serviceTaxAmount;
    @Field
    private double sbCessPercent;
    @Field
    private double sbCessAmount;
    @Field
    private double kkCessPercent;
    @Field
    private double kkCessAmount;
    @Field
    private double surchargeTaxPercent;
    @Field
    private double surchargeTaxAmount;
    @Field
    private double roundOffAmount;
    @Field
    private double settledAmount;
    @Field
    private List<ChaiMonkOrderItem> orderItems = new ArrayList<ChaiMonkOrderItem>();
    @Field
    private List<ChaiMonkSettlement> orderSettlements = new ArrayList<ChaiMonkSettlement>();
    @Field
    private List<ChaiMonkPrintDetail> orderReprints = new ArrayList<ChaiMonkPrintDetail>();



    public Integer getOrderId() {
        return this.orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }


    public int getGeneratedOrderId() {
        return this.generatedOrderId;
    }

    public void setGeneratedOrderId(int generatedOrderId) {
        this.generatedOrderId = generatedOrderId;
    }

    public Integer getCustomerId() {
        return this.customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public int getEmpId() {
        return this.empId;
    }

    public void setEmpId(int empId) {
        this.empId = empId;
    }

    public String getSettlementType() {
        return this.settlementType;
    }

    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    public int getUnitId() {
        return this.unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }


    public double getTotalAmount() {
        return this.totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public double getNetPriceVatPercent() {
        return this.netPriceVatPercent;
    }

    public void setNetPriceVatPercent(double netPriceVatPercent) {
        this.netPriceVatPercent = netPriceVatPercent;
    }

    public double getNetPriceVatAmount() {
        return this.netPriceVatAmount;
    }

    public void setNetPriceVatAmount(double netPriceVatAmount) {
        this.netPriceVatAmount = netPriceVatAmount;
    }

    public double getMrpVatPercent() {
        return this.mrpVatPercent;
    }

    public void setMrpVatPercent(double mrpVatPercent) {
        this.mrpVatPercent = mrpVatPercent;
    }

    public double getMrpVatAmount() {
        return this.mrpVatAmount;
    }

    public void setMrpVatAmount(double mrpVatAmount) {
        this.mrpVatAmount = mrpVatAmount;
    }

    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

    public double getServiceTaxPercent() {
        return this.serviceTaxPercent;

    }

    public void setServiceTaxPercent(double serviceTaxPercent) {
        this.serviceTaxPercent = serviceTaxPercent;
    }

    public double getServiceTaxAmount() {
        return this.serviceTaxAmount;
    }

    public void setServiceTaxAmount(double serviceTaxAmount) {
        this.serviceTaxAmount = serviceTaxAmount;
    }
    public double getSbCessPercent() {
        return this.sbCessPercent;
    }

    public void setSbCessPercent(double sbCessPercent) {
        this.sbCessPercent = sbCessPercent;
    }

    public double getKkCessAmount() {
        return this.kkCessAmount;
    }

    public void setKkCessAmount(double kkCessAmount) {
        this.kkCessAmount = kkCessAmount;
    }

    public double getKkCessPercent() {
        return this.kkCessPercent;
    }

    public void setKkCessPercent(double kkCessPercent) {
        this.kkCessPercent = kkCessPercent;
    }

    public double getSbCessAmount() {
        return this.sbCessAmount;
    }

    public void setSbCessAmount(double sbCessAmount) {
        this.sbCessAmount = sbCessAmount;
    }

    public double getSurchargeTaxPercent() {
        return this.surchargeTaxPercent;
    }



    public void setSurchargeTaxPercent(double surchargeTaxPercent) {
        this.surchargeTaxPercent = surchargeTaxPercent;
    }

    public double getSurchargeTaxAmount() {
        return this.surchargeTaxAmount;
    }

    public void setSurchargeTaxAmount(double surchargeTaxAmount) {
        this.surchargeTaxAmount = surchargeTaxAmount;
    }

    public double getRoundOffAmount() {
        return this.roundOffAmount;
    }

    public void setRoundOffAmount(double roundOffAmount) {
        this.roundOffAmount = roundOffAmount;
    }

    public double getSettledAmount() {
        return this.settledAmount;
    }

    public void setSettledAmount(double settledAmount) {
        this.settledAmount = settledAmount;
    }

    public List<ChaiMonkOrderItem> getOrderItems() {
        return this.orderItems;
    }

    public void setOrderItems(List<ChaiMonkOrderItem> orderItems) {
        this.orderItems = orderItems;
    }

    public List<ChaiMonkSettlement> getOrderSettlements() {
        return this.orderSettlements;
    }

    public void setOrderSettlements(List<ChaiMonkSettlement> orderSettlements) {
        this.orderSettlements = orderSettlements;
    }

    public List<ChaiMonkPrintDetail> getOrderReprints() {
        return orderReprints;
    }

    public void setOrderReprints(List<ChaiMonkPrintDetail> orderReprints) {
        this.orderReprints = orderReprints;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public OrderStatus getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(OrderStatus orderStatus) {
        this.orderStatus = orderStatus;
    }

    public int getToken() {
        return token;
    }

    public void setToken(int token) {
        this.token = token;
    }

    public Date getBillGenerationTime() {
        return billGenerationTime;
    }

    public void setBillGenerationTime(Date billGenerationTime) {
        this.billGenerationTime = billGenerationTime;
    }

    public Date getBillCancellationTime() {
        return billCancellationTime;
    }

    public void setBillCancellationTime(Date billCancellationTime) {
        this.billCancellationTime = billCancellationTime;
    }

    public Date getBillSettlementTime() {
        return billSettlementTime;
    }

    public void setBillSettlementTime(Date billSettlementTime) {
        this.billSettlementTime = billSettlementTime;
    }

    public Date getBillCompletionTime() {
        return billCompletionTime;
    }

    public void setBillCompletionTime(Date billCompletionTime) {
        this.billCompletionTime = billCompletionTime;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }
}
