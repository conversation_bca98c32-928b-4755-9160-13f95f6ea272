package com.stpl.tech.analytics.controller;

import com.stpl.tech.master.core.apps.service.AppsManagementService;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.monk.configuration.model.AppBuildVersionData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;
import static com.stpl.tech.analytics.core.AnalyticsConstants.SEPARATOR;
import static com.stpl.tech.analytics.core.AnalyticsConstants.UNIT_METADATA_ROOT_CONTEXT;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 13-11-2018.
 */
@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + UNIT_METADATA_ROOT_CONTEXT)
public class UnitManagementResources extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(UnitManagementResources.class);

    @Autowired
    private AppsManagementService service;

    @RequestMapping(method = RequestMethod.POST, value = "apps/check-version")
    @ResponseBody
    public AppBuildVersionData getUnitMonkApkVersion(@RequestBody AppBuildVersionData apkVersion)
            throws DataNotFoundException, AuthenticationFailureException {
        LOG.info(":::: GET request for getting monk conf data of unit id  :::: " + apkVersion.getUnitId());
        return service.checkAndReturnVersion(apkVersion);
    }
}
