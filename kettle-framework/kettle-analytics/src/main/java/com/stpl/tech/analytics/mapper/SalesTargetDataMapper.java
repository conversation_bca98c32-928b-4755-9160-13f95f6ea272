/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.mapper;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.poi.ss.usermodel.Cell;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import com.stpl.tech.analytics.model.SalesTarget;
import com.stpl.tech.kettle.core.file.load.AbstractRowMapper;
import com.stpl.tech.util.AppUtils;

public class SalesTargetDataMapper extends AbstractRowMapper<SalesTarget, Cell> {

	/**
	 * Date related
	 */
	public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern("dd-MM-yyyy");

	@Override
	public void setData(SalesTarget expense, Cell nextCell) {
		try {
			int columnIndex = nextCell.getColumnIndex();
			switch (columnIndex) {
			// 1 Unit Id
			case 0:
				String date = (String) getCellValue(nextCell);
				expense.setBusinessDate(AppUtils.getDate(new Date(DATE_FORMATTER.parseDateTime(date).getMillis())));
				break;
			// 2 Unit Name - not required
			case 1:
				expense.setUnitId(((Double) getCellValue(nextCell)).intValue());
				break;
			// 3 Target Ticket
			case 2:
				break;
			case 3:
				expense.getData().getDineInTickets().setTarget(((Double) getCellValue(nextCell)).intValue());
				break;
			case 4:
				expense.getData().getDineInNetSale().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			case 5:
				expense.getData().getDineInApc().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			case 6:
				expense.getData().getDeliveryTickets().setTarget(((Double) getCellValue(nextCell)).intValue());
				break;
			case 7:
				expense.getData().getDeliveryNetSale().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;

			case 8:
				expense.getData().getDeliveryApc().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			case 9:
				expense.getData().getTickets().setTarget(((Double) getCellValue(nextCell)).intValue());
				break;
			case 10:

				expense.getData().getNetSale().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			case 11:
				expense.getData().getApc().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			default:
				break;
			}
		} catch (Exception e) {
			addError(nextCell, e.getMessage());
		}
	}

	@Override
	public SalesTarget createNewInstance() {
		return new SalesTarget();
	}

}
