/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.mapper;

import java.math.BigDecimal;

import org.apache.poi.ss.usermodel.Cell;

import com.stpl.tech.analytics.model.UnitReportData;
import com.stpl.tech.kettle.core.file.load.AbstractRowMapper;

public class UnitReportDataMapper extends AbstractRowMapper<UnitReportData, Cell> {

	@Override
	public void setData(UnitReportData expense, Cell nextCell) {
		try {
			int columnIndex = nextCell.getColumnIndex();
			switch (columnIndex) {
			// 1 Unit Id
			case 0:
				expense.setId(((Double) getCellValue(nextCell)).intValue());
				break;
			// 2 Unit Name - not required
			case 1:
				break;
			// 3 Target Ticket
			case 2:
				expense.getData().getTickets().setTarget(((Double) getCellValue(nextCell)).intValue());
				break;
			// 4 Target Sales
			case 3:
				expense.getData().getNetSale().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 5 Target Delivery Tickets
			case 4:
				expense.getData().getDeliveryTickets().setTarget(((Double) getCellValue(nextCell)).intValue());
				break;
			// 6 Target Delivery Sales
			case 5:
				expense.getData().getDeliveryNetSale().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 7 Target APC
			case 6:
				expense.getData().getApc().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			// 8 Target Delivery APC
			case 7:
				expense.getData().getDeliveryApc().setTarget(
						new BigDecimal((Double) getCellValue(nextCell)).setScale(2, BigDecimal.ROUND_HALF_UP));
				break;
			default:
				break;
			}
		} catch (Exception e) {
			addError(nextCell, e.getMessage());
		}
	}

	@Override
	public UnitReportData createNewInstance() {
		return new UnitReportData();
	}

}
