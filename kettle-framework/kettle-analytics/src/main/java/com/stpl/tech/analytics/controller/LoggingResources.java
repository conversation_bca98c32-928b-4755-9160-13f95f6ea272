/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.analytics.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.data.model.WeightCalibrationDetailData;
import com.stpl.tech.analytics.model.MonkDashboardData;
import com.stpl.tech.analytics.model.chaimonk.log.MonkFileUploadType;
import com.stpl.tech.analytics.service.LoggingService;
import com.stpl.tech.kettle.data.model.MonkLogData;
import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyEventLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.AssemblyOrderLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkConsumptionLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkDiagnosisLogData;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusDetailLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusDetailLogNew;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.NotificationData;
import com.stpl.tech.analytics.model.WeightCalibrationDetail;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.lang.StringEscapeUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;

import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.analytics.core.AnalyticsConstants.ANALYTICS_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;
import static com.stpl.tech.analytics.core.AnalyticsConstants.LOGGING_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.analytics.core.AnalyticsConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + ANALYTICS_SERVICES_ROOT_CONTEXT + SEPARATOR
        + LOGGING_SERVICES_ROOT_CONTEXT) // v1/analytics/log
public class LoggingResources extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(LoggingResources.class);
    private static final String MONK_CONSUMPTION_LOG = MonkConsumptionLog.class.getSimpleName();
    private static final String MONK_TASK_STATUS_LOG = MonkTaskStatusLog.class.getSimpleName();
    private static final String MONK_TASK_STATUS_DETAIL_LOG = MonkTaskStatusDetailLog.class.getSimpleName();
    private static final String MONK_STATUS_LOG = MonkStatusLog.class.getSimpleName();
    private static final String WEIGHT_CALIBRATION_DETAIL = WeightCalibrationDetail.class.getSimpleName();

    private static final String MONK_DIAGNOSIS_LOG = MonkDiagnosisLogData.class.getSimpleName();


    @Autowired
    private LoggingService service;

    @RequestMapping(method = RequestMethod.POST, value = "assembly", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addAssemblyLogData(@RequestBody final AssemblyEventLog logData) {
        LOG.info("Adding Assembly Log - {}", JSONSerializer.toJSON(logData));
        return service.addAssemblyLog(logData);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkLogData(@RequestBody final MonkLogData logData) {
        LOG.info("Adding Monk Log - {}", JSONSerializer.toJSON(logData));
        return service.addMonkLogData(logData);
    }


    @RequestMapping(method = RequestMethod.POST, value = "monk-consumption", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkConsumption(@RequestBody final List<MonkConsumptionLog> consumptionLogList) {
        LOG.info("Adding Monk Consumption Log - {}", JSONSerializer.toJSON(consumptionLogList));
        return service.addMonkConsumptionLogData(consumptionLogList);
    }


    @RequestMapping(method = RequestMethod.POST, value = "monk-status-log", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkStatusLog(@RequestBody final List<MonkStatusLog> statusLogList) {
        LOG.info("Adding Monk Status Log - {}", JSONSerializer.toJSON(statusLogList));
        return service.addMonkStatusLogData(statusLogList);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk-task-log", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkTaskLog(@RequestBody final List<MonkTaskStatusLog> taskLogList) {
        LOG.info("Adding Monk Task Status Log - {}", JSONSerializer.toJSON(taskLogList));
        return service.addMonkTaskStatusLogData(taskLogList);
    }

    @RequestMapping(method = RequestMethod.POST, value = "monk-task-detail-log", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkTaskDetailLog(@RequestBody final List<MonkTaskStatusDetailLog> detailLogs) {
        LOG.info("Adding Monk Task Status Log - {}", JSONSerializer.toJSON(detailLogs));
        return service.addMonkTaskStatusDetailLogData(detailLogs);
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "monk-task-detail-log-new", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addMonkTaskDetailLogNew(@RequestBody final List<MonkTaskStatusDetailLogNew> detailLogs) {
        LOG.info("Adding Monk Task Status Log - {}", JSONSerializer.toJSON(detailLogs));
        return service.addMonkTaskStatusDetailLogNewData(detailLogs);
    }

    @RequestMapping(method = RequestMethod.POST, value = "save-assembly-order-log", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addAssemblyOrderTaskLog(@RequestBody final List<AssemblyOrderLog> taskLogList) {
        LOG.info("Adding Assembly Order Status Log - {}", JSONSerializer.toJSON(taskLogList));
        return service.addAssemblyOrderLog(taskLogList);
    }


    @RequestMapping(method = RequestMethod.POST, value = "save-monk-log-file", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean uploadMonkLogFile(@RequestParam(value = "unitId") final Integer unitId,
                                     @RequestParam(value = "file") final MultipartFile file,
                                     @RequestParam(value = "type") final String fileType) {
        LOG.info("Request to upload monk log file {} {} {}", unitId, file.getName(), fileType);
        LOG.info("Enum constant found :::: {}", MonkFileUploadType.valueOf(fileType));
        service.uploadLogFile(file, unitId, MonkFileUploadType.valueOf(fileType));
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "save-monk-broker-file", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean uploadMonkBrokerFile(@RequestParam(value = "unitId") final Integer unitId,
                                     @RequestParam(value = "file") final MultipartFile file) {
        LOG.info("Request to upload monk log file {} {}", unitId, file.getName());
        return service.uploadMonkBrokerLogs(file, unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "assembly/generic", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addGenericAssemblyLogData(@RequestBody String data) {
        data =StringEscapeUtils.unescapeJava(data);
        data =data.substring(1, data.length() - 1);
        JSONObject obj = new JSONObject(data);
        try{
            String className = obj.getString("className");
            Gson gson = new Gson();
            LOG.info("Adding Assembly Log - {}", className);
           if(className.equals(MONK_CONSUMPTION_LOG)){
               MonkConsumptionLog log =   gson.fromJson(data,MonkConsumptionLog.class);
               return service.addMonkConsumptionLogData(log);
           } else if (className.equals(MONK_TASK_STATUS_LOG)){
               MonkTaskStatusLog log =   gson.fromJson(data,MonkTaskStatusLog.class);
               return service.addMonkTaskStatusLogData(log);
           }else if (className.equals(MONK_TASK_STATUS_DETAIL_LOG)){
               MonkTaskStatusDetailLog log =   gson.fromJson(data,MonkTaskStatusDetailLog.class);
               return service.addMonkTaskStatusDetailLogData(log);
           } else if (className.equals(MONK_STATUS_LOG)) {
               MonkStatusLog log =   gson.fromJson(data,MonkStatusLog.class);
               return service.addMonkStatusLogData(log);
           }
           else if (className.equals(MONK_DIAGNOSIS_LOG)) {
               MonkDiagnosisLogData log =   gson.fromJson(data,MonkDiagnosisLogData.class);
               return service.addMonkDiagnosisLogData(log);
           }
           else if(className.equals(WEIGHT_CALIBRATION_DETAIL)) {
               WeightCalibrationDetail log = gson.fromJson(data,WeightCalibrationDetail.class);
               return service.addWeightCalibrationDetail(log);
           }
        }catch (Exception e){
            LOG.info("Exception occured during saving generic assembly log ", e);
            return false;
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "assembly/generic/query", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Object> getGenericAssemblyLogData(@RequestBody String queryString,@RequestParam int limit ,@RequestParam int page , @RequestParam String className) {
       if(limit<=0 || limit>500){
           limit = 500;
       }
        try{
            if(className.equals(MONK_CONSUMPTION_LOG)){
                return service.queryGeneric(queryString,limit,page,MonkConsumptionLog.class);
            } else if (className.equals(MONK_TASK_STATUS_LOG)){
                return service.queryGeneric(queryString,limit,page,MonkTaskStatusLog.class);
            }else if (className.equals(MONK_TASK_STATUS_DETAIL_LOG)){
                return service.queryGeneric(queryString,limit,page,MonkTaskStatusDetailLog.class);
            } else if (className.equals(MONK_STATUS_LOG)) {
                return service.queryGeneric(queryString,limit,page,MonkStatusLog.class);
            }else if (className.equals(MONK_DIAGNOSIS_LOG)) {
                return service.queryGeneric(queryString,limit,page,MonkDiagnosisLogData.class);
            }
            else{
                LOG.info("Data {} is not supported ", className);
            }
        }catch (Exception e){
            LOG.info("Exception occured during querying generic assembly log ", e);
        }
        return null;
    }

    @RequestMapping(method = RequestMethod.GET, value = "assembly/wtCalibrationDetail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, WeightCalibrationDetail> getWtCalibrationDetail(@RequestParam  Integer unitId)
    {
        return service.getWtCalibrationDetail(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "assembly/dashboard/rt", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer, MonkDashboardData> getRealTimeDashboard(@RequestBody Integer orderId) {
        return service.getRealTimeDashboardDetails(orderId);
    }

    @RequestMapping(method = RequestMethod.POST,value = "save/preallocation-time",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean savePreallocationLogs(@RequestParam(required = false) Integer unitId,
                                         @RequestParam(required = false) String businessDate){
        return service.savePreAllocationLogs(unitId, AppUtils.getDate(businessDate, "yyyy-MM-dd"));
    }

    @RequestMapping(method = RequestMethod.POST, value="send/assembly-notification",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean sendNotificationToAndroid(@RequestBody NotificationData notificationData){
        LOG.info("Requesting to send notification");
        return service.sendNotificationToAndroid(notificationData);
    }

    @RequestMapping(method = RequestMethod.POST, value="save/assembly-notification",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean saveNotificationToAndroid(@RequestParam Integer keyId, @RequestParam String responseMessage){
        LOG.info("Requesting to send notification");
        return service.saveNotificationToAndroid(keyId, responseMessage);
    }

    @Scheduled(cron = "0 */15 * * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value="mark-assembly-notifications-as-not-delivered",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void markAssemblyNotificationsAsNotDelivered() {
        LOG.info("Running markNotificationsAsNotDelivered :::");
        service.markAssemblyNotificationsAsNotDelivered();
    }

}