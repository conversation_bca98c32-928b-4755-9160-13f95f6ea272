package com.stpl.tech.analytics.service;

import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.AggregatedReportData;
import com.stpl.tech.analytics.model.UnitOrderTATDomain;
import com.stpl.tech.analytics.model.UnitReportData;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import com.stpl.tech.util.WsType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;
import java.util.Set;

public interface OrderTATCacheService {

	public Map<String , UnitOrderTATDomain> getOrderTATData();

	public Map<String , UnitOrderTATDomain> getOrderTATData(int unitId);

	public Map<String , UnitOrderTATDomain> getOrderTATData(int unitId, int brandId);

	public UnitOrderTATDomain getOrderTATData(int unitId, int brandId , String type);

	public void refreshOrderTATData();

	public void refreshOrderTATData(int unitId);

	public void refreshOrderTATData(int unitId, int brandId);

	public void refreshOrderTATData(int unitId, int brandId , String type);


	public void syncOrderTATData();

    void callOrderTATProc(Date businessDate);
}
