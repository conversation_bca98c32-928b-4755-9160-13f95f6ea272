package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.kettle.data.model.MonkManualTaskAddon;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * DAO interface for MonkManualTaskAddon MySQL operations
 */
@Repository
public interface MonkManualTaskAddonDao extends JpaRepository<MonkManualTaskAddon, Integer> {
    List<MonkManualTaskAddon> findByMonkManualTask_MonkManualTaskId(Integer monkManualTaskId);
} 