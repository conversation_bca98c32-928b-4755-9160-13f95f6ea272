/**
 *
 */
package com.stpl.tech.analytics.service;

import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.CumulativeData;
import com.stpl.tech.analytics.model.SalesTarget;
import com.stpl.tech.analytics.model.UnitData;

import java.io.IOException;
import java.util.Date;
import java.util.List;

public interface DataService {

    public void addTargets(List<SalesTarget> targets);

    public void setMTD(CumulativeData data);

    public void setMTDTarget(CumulativeData data);

    public void setTodayTarget(CumulativeData data);

    public void setMonthlyTarget(CumulativeData data);

    public void setCafeCountMTD(CumulativeData data);

    public List<UnitData> getAllUnitsTargetData();

    public void addMonthlyTargets(String outputFile) throws IOException;

    public AggregatePenetrationData getCurrentMonthTargets(int unitId, Date businessDate, String loggedIn);

    public AggregatePenetrationData getCurrentDailyTargets(int unitId, Date date, String loggedIn);

}
