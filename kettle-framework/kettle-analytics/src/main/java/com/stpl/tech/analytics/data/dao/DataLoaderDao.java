/**
 * 
 */
package com.stpl.tech.analytics.data.dao;

import java.sql.SQLException;
import java.util.List;

import com.stpl.tech.kettle.sales.domain.model.CustomerData;
import com.stpl.tech.kettle.sales.domain.model.SalesRecord;
import com.stpl.tech.util.ExecutionEnvironment;

/**
 * <AUTHOR>
 *
 */
public interface DataLoaderDao {

	public List<CustomerData> loadCustomerData(ExecutionEnvironment env, int startCustomerId, int endCustomerId) throws SQLException;

	/**
	 * @param env
	 * @param startOrderId
	 * @param endOrderId
	 * @return
	 * @throws SQLException
	 */
	List<SalesRecord> loadSaleData(ExecutionEnvironment env, int startOrderId, int endOrderId) throws SQLException;
}
