package com.stpl.tech.analytics.core;

import java.io.IOException;
import java.text.DateFormatSymbols;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonParseException;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

public class AnalyticsUtil extends AppUtils {

	private static final Logger LOG = LoggerFactory.getLogger(AnalyticsUtil.class);

	private static Gson GSON = null;

	private static final String[] DATE_FORMATS = new String[] { "MMM dd, yyyy HH:mm:ss", "MMM dd, yyyy hh:mm:ss a" };

	public static String getMonthForInt(int num) {
		String month = "wrong";
		DateFormatSymbols dfs = new DateFormatSymbols();
		String[] months = dfs.getMonths();
		if (num >= 0 && num <= 11) {
			month = months[num];
		}
		return month;
	}

	public static boolean isWeekDay() {
		Calendar cal = Calendar.getInstance();
		return cal.get(Calendar.DAY_OF_WEEK) > 1 && cal.get(Calendar.DAY_OF_WEEK) < 7;

	}

	public static Date getStartOfDay() {
		Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Kolkata"));
		int hour = calendar.get(Calendar.HOUR_OF_DAY);
		// hour should be always less then 5, not equal to 5
		if (hour < 5) {
			calendar.add(Calendar.DATE, -1);
		}
		calendar.set(Calendar.HOUR_OF_DAY, 5);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	public static void main(String[] args) {
		System.out.println(getStartOfDay());
	}

	public static <T> T callWebService(Class<T> clazz, String endPoint, Object object) {
		try {
			HttpResponse response = createPostRequest(endPoint, object);
			return WebServiceHelper.convertResponse(response, clazz, true);
		} catch (Exception e) {
			LOG.error("Error while creating web request to {}", endPoint, e);
		}
		return null;
	}

	private static HttpResponse createPostRequest(String url, Object object)
			throws ClientProtocolException, IOException {
		HttpPost requestObject = new HttpPost(url);
		String consumptionDataJson = WebServiceHelper.convertToString(object);
		HttpEntity httpEntity = new StringEntity(consumptionDataJson, AppConstants.CHARSET);
		requestObject.setHeader("Content-type", MediaType.APPLICATION_JSON.toString());
		requestObject.setEntity(httpEntity);
		return WebServiceHelper.postRequestWithNoTimeout(requestObject);
	}

	public static Gson getCustomSerializer() {
		if (GSON == null) {
			return new GsonBuilder().registerTypeAdapter(Date.class,
					(JsonDeserializer<Date>) (jsonElement, type, jsonDeserializationContext) -> {
						for (String format : DATE_FORMATS) {
							try {
								return new SimpleDateFormat(format, Locale.US).parse(jsonElement.getAsString());
							} catch (ParseException e) {
								LOG.error("Parse Exception while reading date from object", e);
							}
						}
						throw new JsonParseException("Date not parsable: \"" + jsonElement.getAsString()
								+ "\". Supported formats: " + Arrays.toString(DATE_FORMATS));
					}).create();
		} else {
			return GSON;
		}

	}
}
