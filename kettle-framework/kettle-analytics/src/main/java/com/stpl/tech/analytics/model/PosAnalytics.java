package com.stpl.tech.analytics.model;

import java.math.BigDecimal;

public class PosAnalytics {
    public String label;
    public BigDecimal aggregate;

    public PosAnalytics() {
    }

    public PosAnalytics(String label, BigDecimal aggregate, BigDecimal currentOrTarget, boolean trend) {

        this.label = label;
        this.aggregate = aggregate;
        this.currentOrTarget = currentOrTarget;
        this.trend = trend;
    }

    public String getLabel() {

        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public BigDecimal getAggregate() {
        return aggregate;
    }

    public void setAggregate(BigDecimal aggregate) {
        this.aggregate = aggregate;
    }

    public BigDecimal getCurrentOrTarget() {
        return currentOrTarget;
    }

    public void setCurrentOrTarget(BigDecimal currentOrTarget) {
        this.currentOrTarget = currentOrTarget;
    }

    public boolean isTrend() {
        return trend;
    }

    public void setTrend(boolean trend) {
        this.trend = trend;
    }

    public BigDecimal currentOrTarget;
    public boolean trend;
}
