package com.stpl.tech.analytics.dto;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class MonkManualTaskAddonDTO {
    private Integer monkManualTaskAddonId;
    private Integer monkManualTaskId;
    private Integer addonId;
    private String productSourceSystem;
    private String dimension;
    private BigDecimal quantity;
    private String addonName;
    private String addonType;
    private String unitOfMeasure;
    private String defaultSetting;
} 