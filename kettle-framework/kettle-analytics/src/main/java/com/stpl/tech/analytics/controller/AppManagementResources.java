package com.stpl.tech.analytics.controller;

import com.stpl.tech.analytics.core.AnalyticsConstants;
import com.stpl.tech.analytics.service.impl.AnalyticsProperties;
import com.stpl.tech.master.core.apps.service.AppsManagementService;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.monk.configuration.model.AppsVersionMetadata;
import com.stpl.tech.master.monk.configuration.model.ArduinoBuildData;
import com.stpl.tech.master.monk.configuration.model.ArduinoBuildForUnit;
import com.stpl.tech.spring.service.FileArchiveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import static com.stpl.tech.analytics.core.AnalyticsConstants.API_VERSION;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-05-2018.
 */

@RestController
@RequestMapping(value = API_VERSION + AnalyticsConstants.SEPARATOR + AnalyticsConstants.APPS_MANAGEMENT_ROOT_CONTEXT) // 'v1/app-management'
public class AppManagementResources extends AbstractResources{
    private static final Logger LOG = LoggerFactory.getLogger(AppManagementResources.class);

    @Autowired
    private AppsManagementService service;

    @Autowired
    private AnalyticsProperties props;

    @Autowired
    private FileArchiveService fileArchiveService;


    @RequestMapping(method = RequestMethod.GET, value = "latest", produces = MediaType.APPLICATION_JSON_VALUE)
    public AppsVersionMetadata getLatestBuilds(){
        LOG.info("Request to get the latest build version for Android Apps");
        return service.findLatestVersion();
    }


    @RequestMapping(method = RequestMethod.POST, value = "new-version", produces = MediaType.APPLICATION_JSON_VALUE)
    public AppsVersionMetadata uploadNewBuild(@RequestBody final AppsVersionMetadata version){
        LOG.info("Request to get the latest build version for Android Apps");
        return service.uploadNewBuild(version);
    }


    @RequestMapping(method = RequestMethod.POST, value = "activate-version", produces = MediaType.APPLICATION_JSON_VALUE)
    public AppsVersionMetadata activateVersion(@RequestBody final AppsVersionMetadata version){
        LOG.info("Request to get the latest build version for Android Apps");
        return service.activateVersion(version);
    }


    /***************************    ARDUINO BUILD RELATED APIs    *****************************/

    @RequestMapping(method = RequestMethod.POST, value = "upload-arduino-build",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ArduinoBuildData uploadArduinoBuild(@RequestParam(value = "userId") Integer userId,
                                               @RequestParam(value = "unitId") Integer unitId,
                                               @RequestParam(value = "monk") String monk,
                                               @RequestParam(value = "version") String versionName,
                                         @RequestParam(value = "file") final MultipartFile file)
            throws DataNotFoundException {
        return service.uploadArduinoBuild(file, userId, monk, props.getArduinoBuildBucket(), unitId, versionName);
    }

    @RequestMapping(method = RequestMethod.POST, value = "initiate-arduino-build/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ArduinoBuildData initiateArduinoBuild(@PathVariable Integer userId) {
        return service.initiateBuild(userId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "initiated-arduino-build", produces = MediaType.APPLICATION_JSON_VALUE)
    public ArduinoBuildData initiatedArduinoBuild() throws DataNotFoundException {
        return service.getInitiatedArduinoBuild();
    }

    @RequestMapping(method = RequestMethod.POST, value = "activate-arduino-build", produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean activateArduinoBuild(@RequestBody ArduinoBuildData buildData) {
        return service.activateArduinoBuild(buildData);
    }

    @RequestMapping(method = RequestMethod.GET, value = "active-arduino-version", produces = MediaType.APPLICATION_JSON_VALUE)
    public ArduinoBuildData activeArduinoBuild() throws DataNotFoundException {
        return service.getActiveArduinoBuild();
    }

    @RequestMapping(method = RequestMethod.GET, value = "check-arduino-version", produces = MediaType.APPLICATION_JSON_VALUE)
    public ArduinoBuildForUnit checkArduinoVersion(@RequestParam Integer unitId) throws DataNotFoundException {
        return service.getActiveArduinoBuild(unitId);
    }

}
