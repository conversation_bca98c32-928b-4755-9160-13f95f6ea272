package com.stpl.tech.analytics.sqs;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.analytics.service.LoggingService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.Session;
import java.util.Properties;

@Service
public class AssemblySQSMessageService {

	private static final Logger LOG = LoggerFactory.getLogger(AssemblySQSMessageService.class);
	@Autowired
	private Environment env;

	@Autowired
	private LoggingService loggingService;

	@PostConstruct
	public void init() throws JMSException {
		EnvType envType = EnvType.valueOf(env.getProperty("environment.type", "LOCAL"));
		Regions region = AppUtils.getRegion(envType);
		SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
		AssemblyLogListner listener = new AssemblyLogListner(loggingService);
		MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, envType.name(),
				"_HOT_STATION_LOGS");
		consumer.setMessageListener(listener);
		startQueueProcessing(region);
	}

	/**
	 * Method to stop Inventory queue listener.
	 * <p>
	 * To be used in pair with startQueueProcessing
	 * 
	 * @throws JMSException
	 */
	public void stopQueueProcessing(Regions region) throws JMSException {
		LOG.info("Stopping Queueing Service");
		SQSNotification.getInstance().getSqsConnection(region).stop();
		LOG.info("Stopped Queueing Service");
	}

	/**
	 * Method to start Inventory queue listener.
	 * <p>
	 * To be used in pair with stopQueueProcessing
	 *
	 * @throws JMSException
	 */
	public void startQueueProcessing(Regions region) throws JMSException {
		LOG.info("Starting Queueing Service");
		SQSNotification.getInstance().getSqsConnection(region).start();
		LOG.info("Started Queueing Service");
	}

}
