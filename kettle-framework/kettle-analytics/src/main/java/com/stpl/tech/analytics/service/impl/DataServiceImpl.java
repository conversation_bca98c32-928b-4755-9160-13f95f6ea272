/**
 *
 */
package com.stpl.tech.analytics.service.impl;

import com.stpl.tech.analytics.cache.AnalyticsCache;
import com.stpl.tech.analytics.data.dao.DataServiceDao;
import com.stpl.tech.analytics.model.AggregatePenetrationData;
import com.stpl.tech.analytics.model.CumulativeData;
import com.stpl.tech.analytics.model.SalesTarget;
import com.stpl.tech.analytics.model.UnitData;
import com.stpl.tech.analytics.service.DataService;
import com.stpl.tech.kettle.data.model.UnitTargetDetail;
import com.stpl.tech.kettle.data.model.UploadedTargetData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
@Service
public class DataServiceImpl implements DataService {

    private static final Logger LOG = LoggerFactory.getLogger(DataServiceImpl.class);

    @Autowired
    private DataServiceDao dao;

    @Autowired
    private AnalyticsProperties props;

    @Autowired
    AnalyticsCache analyticsCache;

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addTargets(List<SalesTarget> targets) {
        List<UnitTargetDetail> list = new ArrayList<>();
        Set<Integer> unitIds = new HashSet<>();
        Date minDate = targets.get(0).getBusinessDate();
        Date maxDate = targets.get(0).getBusinessDate();
        for (SalesTarget target : targets) {
            if (target.getBusinessDate().before(minDate)) {
                minDate = target.getBusinessDate();
            }
            if (target.getBusinessDate().after(maxDate)) {
                maxDate = target.getBusinessDate();
            }
            unitIds.add(target.getUnitId());
            list.add(convert(target));
        }
        dao.markTargetAsInactive(minDate, maxDate, unitIds);
        dao.addAllUnitTargets(list);
    }

    /**
     * @param d
     * @return
     */
    private UnitTargetDetail convert(SalesTarget target) {
        UnitTargetDetail d = new UnitTargetDetail();
        d.setBusinessDate(target.getBusinessDate());
        d.setNetDeliveryApc(target.getData().getDeliveryApc().getTarget());
        d.setNetDeliverySales(target.getData().getDeliveryNetSale().getTarget());
        d.setNetDeliveryTickets(target.getData().getDeliveryTickets().getTarget());
        d.setNetDineInTickets(
                target.getData().getTickets().getTarget() - target.getData().getDeliveryTickets().getTarget());
        d.setNetDineInSales(AppUtils.subtract(target.getData().getNetSale().getTarget(),
                target.getData().getDeliveryNetSale().getTarget()));
        d.setNetDineInApc(AppUtils.divide(d.getNetDineInSales(), new BigDecimal(d.getNetDineInTickets())));
        d.setNetSales(target.getData().getNetSale().getTarget());
        d.setNetTickets(target.getData().getTickets().getTarget());
        d.setNetApc(target.getData().getApc().getTarget());
        d.setUnitId(target.getUnitId());
        d.setStatus(AppConstants.ACTIVE);
        return d;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.analytics.service.DataService#setMTD(com.stpl.tech.
     * analytics.model.CumulativeData, java.util.Date)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void setMTD(CumulativeData data) {
        Date businessDate = AppUtils.getCurrentBusinessDate();
        data.setMtd(dao.getSalesData(AppUtils.getStartOfMonth(businessDate.getYear(), businessDate.getMonth()),
                businessDate));
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.analytics.service.DataService#setMTDTarget(com.stpl.tech.
     * analytics.model.CumulativeData)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void setMTDTarget(CumulativeData data) {
        Date businessDate = AppUtils.getOldDate(AppUtils.getCurrentBusinessDate(), 1);
        data.setMtdTarget(dao.getTargetData(AppUtils.getStartOfMonth(businessDate.getYear(), businessDate.getMonth()),
                businessDate));
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.analytics.service.DataService#setTarget(com.stpl.tech.
     * analytics.model.CumulativeData, java.util.Date)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void setMonthlyTarget(CumulativeData data) {
        Date businessDate = AppUtils.getCurrentBusinessDate();
        Date lastDayOfMonth = AppUtils.getLastDayOfMonth(businessDate);
        data.setMonthlyTarget(dao.getTargetData(
                AppUtils.getStartOfMonth(businessDate.getYear(), businessDate.getMonth()), lastDayOfMonth));

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitData> getAllUnitsTargetData() {
        Date businessDate = AppUtils.getCurrentBusinessDate();
        Date lastDayOfMonth = AppUtils.getLastDayOfMonth(businessDate);
        return dao.getAllUnitsTargetData(AppUtils.getStartOfMonth(businessDate.getYear(), businessDate.getMonth()),
                lastDayOfMonth);

    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.analytics.service.DataService#setProjected(com.stpl.tech.
     * analytics.model.CumulativeData, java.util.Date)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void setCafeCountMTD(CumulativeData data) {
        Date businessDate = AppUtils.getCurrentBusinessDate();
        data.setCafeCountMTD(dao.getPercentageAchievement(props.getPercentageThreasholdForReporting(),
                AppUtils.getStartOfMonth(businessDate.getYear(), businessDate.getMonth()), businessDate));
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.analytics.service.DataService#setTodayTarget(com.stpl.tech.
     * analytics.model.CumulativeData)
     */
    @Override
    public void setTodayTarget(CumulativeData data) {
        Date businessDate = AppUtils.getCurrentBusinessDate();
        data.setTodayTarget(dao.getTargetData(businessDate, businessDate));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addMonthlyTargets(String filePath) throws IOException {
        FileInputStream inputStream = new FileInputStream(new File(filePath));
        Workbook workbook = new XSSFWorkbook(inputStream);
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();
        List<UploadedTargetData> entityList = parser.createEntity(workbook.getSheetAt(0), UploadedTargetData.class,
                errors::add);
        if (errors.isEmpty()) {
            int year = entityList.get(0).getYear();
            int month = entityList.get(0).getMonth();
            dao.markAllTargetForMonthAsInactive(month, year);
            dao.addMonthlyTargets(entityList);
        } else {
            LOG.info("Error Parsing Workbook for Expense, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
        }
        workbook.close();
        LOG.info("Monthly Targets Uploaded ", entityList != null ? entityList.size() : 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public AggregatePenetrationData getCurrentMonthTargets(int unitId, Date businessDate, String loggedIn) {
        Date date = (businessDate == null) ? AppUtils.getBusinessDate() : businessDate;
        int month = AppUtils.getMonth(date);
        int year = AppUtils.getYear(date);
        if (!Objects.equals(loggedIn, "true") && analyticsCache.getCurrentMonthlyTargetsFromCache(unitId) != null && Objects.equals(date, AppUtils.getBusinessDate())) {
            return analyticsCache.getCurrentMonthlyTargetsFromCache(unitId);
        }

        UploadedTargetData data = dao.getCurrentMonthTargetsForUnit(unitId, month, year);

        AggregatePenetrationData pData = new AggregatePenetrationData();
        if (data != null) {
            convert(data, pData);
        } else {
            LOG.info("Monthly Target Data Not Available for unit {}, month {}, year {}", unitId, month, year);
        }
        analyticsCache.setCurrentMonthlyTargetsFromCache(unitId, pData);
        return pData;
    }

    private void convert(UploadedTargetData data, AggregatePenetrationData pData) {

        pData.setBeverage(data.getBeverage());
        pData.setCakes(data.getCakes());
        pData.setCold(data.getCold());
        pData.setCustomer(data.getCustomer());
        pData.setDelivery(data.getDeliveryTickets());
        pData.setDineIn(data.getDineInTickets());
        pData.setFood(data.getFood());
        pData.setGiftCards(data.getGiftCards());
        pData.setHot(data.getHot());
        pData.setMeals(data.getMeals());
        pData.setMerchandise(data.getMerchandise());
        pData.setNewCustomer(data.getNewCustomer());
        pData.setNewCustWithNoPriPrd(data.getNewCustWithNoPriPrd());
        pData.setNonVeg(data.getNonVeg());
        pData.setSeasonal(data.getSeasonal());
        pData.setTotal(data.getTickets());
        pData.setFull(data.getFull());
        pData.setRegular(data.getRegular());
        pData.setMerchandiseSales(data.getMerchandiseSales());
        pData.setNetSale(data.getNetSale());
        pData.setSeasonalProductOne(data.getSeasonalProductOne());
        pData.setSeasonalProductTwo(data.getSeasonalProductTwo());
        pData.setSeasonalProductThree(data.getSeasonalProductThree());

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public AggregatePenetrationData getCurrentDailyTargets(int unitId, Date businessDate, String loggedIn) {
        Date date = (businessDate == null) ? AppUtils.getBusinessDate() : businessDate;

        if (!Objects.equals(loggedIn, "true") && analyticsCache.getCurrentDailyTargetsFromCache(unitId) != null && Objects.equals(date, AppUtils.getBusinessDate())) {
            return analyticsCache.getCurrentDailyTargetsFromCache(unitId);
        }

        UploadedTargetData data = dao.getCurrentDailyTargetsForUnit(unitId, date);

        AggregatePenetrationData pData = new AggregatePenetrationData();
        if (data != null) {
            convert(data, pData);
        } else {
            LOG.info("Daily Target Data Not Available for unit {}, date {}", unitId, businessDate);
        }
        analyticsCache.setCurrentDailyTargetsFromCache(unitId, pData);
        return pData;
    }

}
