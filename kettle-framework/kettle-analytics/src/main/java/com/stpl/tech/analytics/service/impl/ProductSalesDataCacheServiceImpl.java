package com.stpl.tech.analytics.service.impl;

import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.stpl.tech.analytics.model.ProductReportData;
import com.stpl.tech.analytics.service.ProductSalesDataCacheService;
import com.stpl.tech.kettle.domain.model.OrderResponse;

@Service
public class ProductSalesDataCacheServiceImpl implements ProductSalesDataCacheService {

	@Autowired
	private SalesDataCache salesCache;

	@Override
	public List<ProductReportData> addOrder(OrderResponse order) {
		return salesCache.addOrderForProduct(order);
	}

	@Override
	public ProductReportData getSalesData(int productId) {
		return salesCache.getProductReportData(productId);
	}

	@Override
	public Set<ProductReportData> getSalesData() {
		return new TreeSet<>(salesCache.getProductsReportData());
	}

}
