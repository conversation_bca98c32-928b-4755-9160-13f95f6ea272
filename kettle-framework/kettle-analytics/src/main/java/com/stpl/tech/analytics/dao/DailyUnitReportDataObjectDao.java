package com.stpl.tech.analytics.dao;

import java.util.Date;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.stpl.tech.analytics.model.DailyUnitReportDataObject;

public interface DailyUnitReportDataObjectDao extends MongoRepository<DailyUnitReportDataObject, String> {

	@Query("{'instance.detail.id' : {'$eq' : ?0}, 'date' : {'$eq' : ?1}}")
	public List<DailyUnitReportDataObject> findByBusinessDate(final int unitId, final Date businessDate);

	@Query("{'date' : {'$eq' : ?0}}")
	public List<DailyUnitReportDataObject> findAllByBusinessDate(final Date businessDate);

	/**
	 * @param startOfMonth
	 * @param currentDate
	 * @return
	 */
	@Query("{'date' : {'$gte' : ?0, '$lte' : ?1}}")
	public List<DailyUnitReportDataObject> findByMonthAndYear(Date startOfMonth, Date currentDate);

	@Query("{'instance.detail.id' : {'$eq' : ?0}, 'date' : {'$gte' : ?1, '$lte' : ?2}}")
	public List<DailyUnitReportDataObject> findByUnitIdMonthAndYear(int unitId, Date startOfMonth, Date currentDate);

}
