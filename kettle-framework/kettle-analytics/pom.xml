<?xml version="1.0"?>
<!-- ~ Copyright (C) 2016, Sunshine Teahouse Private Limited - All Rights
	Reserved ~ Unauthorized copying of this file, via any medium is strictly
	prohibited ~ Proprietary and confidential ~ Written by Chaayos Technology
	Team, 2015 -->

<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.stpl.tech.kettle</groupId>
        <artifactId>kettle-framework</artifactId>
        <version>6.2.41</version>
		<relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>com.stpl.tech.kettle.analytics</groupId>
    <artifactId>kettle-analytics</artifactId>
    <name>kettle-analytics</name>
    <packaging>war</packaging>
    <url>http://maven.apache.org</url>
<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-war-plugin</artifactId>-->
<!--                <version>2.6</version>-->
<!--                <configuration>-->
<!--                    <webXml>WebContent/WEB-INF/web.xml</webXml>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--        <finalName>kettle-analytics</finalName>-->
<!--    </build>-->
    <build>
        <finalName>${project.artifactId}</finalName>
    </build>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>net.bull.javamelody</groupId>
            <artifactId>javamelody-spring-boot-starter</artifactId>
            <version>1.90.0</version>
        </dependency>
        <dependency>
            <groupId>com.hazelcast</groupId>
            <artifactId>hazelcast</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
            <version>1.11.251</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.log</groupId>
            <artifactId>logging-core</artifactId>
            <version>6.2.41</version>
        </dependency>
        <dependency>
            <groupId>com.pusher</groupId>
            <artifactId>pusher-http-java</artifactId>
            <version>0.9.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.spring</groupId>
            <artifactId>spring-service</artifactId>
            <version>6.2.41</version>
        </dependency>
        <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
		    <groupId>javax.servlet</groupId>
		    <artifactId>javax.servlet-api</artifactId>
		    <scope>provided</scope>
		</dependency>
		<dependency>
		    <groupId>org.springframework.boot</groupId>
		    <artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>        <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <exclusions>
            <exclusion>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
            </exclusion>
        </exclusions>
    </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
            <version>3.1.8</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
