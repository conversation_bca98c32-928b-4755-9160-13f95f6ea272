<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <url>http://maven.apache.org</url>
    <parent>
        <groupId>com.stpl.tech.kettle</groupId>
        <artifactId>kettle-framework</artifactId>
        <version>5.0.15</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <groupId>com.stpl.tech.kettle.inventory</groupId>
    <artifactId>kettle-inventory</artifactId>
    <name>kettle-inventory</name>
    <packaging>war</packaging>

    <!--    <build>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.apache.maven.plugins</groupId>-->
    <!--                <artifactId>maven-war-plugin</artifactId>-->
    <!--                <version>2.6</version>-->
    <!--                <configuration>-->
    <!--                    <webXml>WebContent/WEB-INF/web.xml</webXml>-->
    <!--                </configuration>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--        <finalName>kettle-inventory</finalName>-->
    <!--    </build>-->
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>net.bull.javamelody</groupId>
            <artifactId>javamelody-spring-boot-starter</artifactId>
            <version>1.90.0</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.master</groupId>
            <artifactId>master-core</artifactId>
            <version>5.0.15</version>
            <!--            <exclusions>-->
            <!--            	<exclusion>-->
            <!--            		<groupId>commons-io</groupId>-->
            <!--            		<artifactId>commons-io</artifactId>-->
            <!--            	</exclusion>-->
            <!--            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.scm</groupId>
            <artifactId>scm-domain</artifactId>
            <version>5.0.15</version>
            <!--            <exclusions>-->
            <!--            	<exclusion>-->
            <!--            		<groupId>commons-io</groupId>-->
            <!--            		<artifactId>commons-io</artifactId>-->
            <!--            	</exclusion>-->
            <!--            </exclusions>-->
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.stpl.tech.redis</groupId>-->
        <!--            <artifactId>redis-core</artifactId>-->
        <!--            <version>4.1.0-SNAPSHOT</version>-->
        <!--        </dependency>-->
        <!--    </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-security</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.redis</groupId>
            <artifactId>redis-domain</artifactId>
            <version>5.0.15</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.redis</groupId>
            <artifactId>redis-core</artifactId>
            <version>5.0.15</version>
            <scope>compile</scope>
        </dependency>        <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <exclusions>
            <exclusion>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-logging</artifactId>
            </exclusion>
        </exclusions>
    </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--        &lt;!&ndash; https://mvnrepository.com/artifact/org.apache.poi/poi &ndash;&gt;-->
        <!--		<dependency>-->
        <!--		    <groupId>org.apache.poi</groupId>-->
        <!--		    <artifactId>poi-ooxml</artifactId>-->
        <!--		    <version>5.2.2</version>-->
        <!--		</dependency>-->

        <!-- https://mvnrepository.com/artifact/org.subtlelib/poi.builder -->
        <!--		<dependency>-->
        <!--		    <groupId>org.subtlelib</groupId>-->
        <!--		    <artifactId>poi.builder</artifactId>-->
        <!--		    <version>1.0.4</version>-->
        <!--		</dependency>-->
    </dependencies>
</project>
