package com.stpl.tech.kettle.inventory.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import org.apache.http.protocol.HTTP;

import com.stpl.tech.kettle.inventory.domain.mongo.CostDetail;
import com.stpl.tech.kettle.inventory.domain.redis.InventoryData;
import com.stpl.tech.kettle.inventory.vo.RecipeInventory;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.slack.SlackUtils;

public class InventoryUtils extends AppUtils {

	// keep this private to disable unwanted key collision
	private static String unitProductKey(int unitId, String keyType, int keyId, String dimension, String system) {
		return system + unitId + keyType + keyId + dimension;
	}

	public static String unitSCMSKUKey(int unitId, int productId, String dimension) {
		return unitProductKey(unitId, PriceUpdateEntryType.SKU.name(), productId, dimension,
				InventoryServiceConstants.SCM);
	}

	public static String unitSCMProductKey(int unitId, int productId, String dimension) {
		return unitProductKey(unitId, PriceUpdateEntryType.PRODUCT.name(), productId, dimension,
				InventoryServiceConstants.SCM);
	}

	public static String unitCafeProductKey(int unitId, int productId, String dimension) {
		return unitProductKey(unitId, PriceUpdateEntryType.PRODUCT.name(), productId, dimension,
				InventoryServiceConstants.KETTLE);
	}

	public static String unitSCMProductKey(int unitId, PriceUpdateEntryType keyType, int keyId, String uom) {
		return PriceUpdateEntryType.SKU.equals(keyType) ? unitSCMSKUKey(unitId, keyId, uom)
				: unitSCMProductKey(unitId, keyId, uom);
	}

	private static String unitKeyData(int unitId, String string) {
		return unitId + string;
	}

	public static String unitSCMProductRepoKey(int unitId) {
		return unitKeyData(unitId, InventoryServiceConstants.SCM);
	}

	public static String unitCafeProductRepoKey(int unitId) {
		return unitKeyData(unitId, InventoryServiceConstants.KETTLE);
	}

	public static String getUserAgent(HttpServletRequest req) {
		String userAgent = req.getHeader(HTTP.USER_AGENT);
		if (userAgent == null) {
			userAgent = req.getHeader(HTTP.USER_AGENT.toLowerCase());
		}
		return userAgent;
	}

	public static void slackIt(String userAgent, String url, EnvType envType, Exception ex) {
		String message = SlackUtils.slackIt(userAgent, url, envType, "Inventory", ex);
		SlackNotificationService.getInstance().sendNotification(envType,"Inventory", SlackNotification.SYSTEM_ERRORS,message);
	}

	public static String getCriticalProductKey(int id, int unitId) {
		return id + "@@" + unitId;
	}

	public static void calculateProductInventory(int unitId, RecipeDetail recipe,
			Map<String, BigDecimal> criticalProductKeys, Map<String, BigDecimal> productKeys) {

		if (recipe == null || recipe.getIngredient() == null) {
			return;
		}

		if(Objects.isNull(recipe.getIngredient().getProducts())){
			recipe.getIngredient().setProducts(new ArrayList<>());
		}
		if(Objects.isNull(recipe.getIngredient().getVariants())){
			recipe.getIngredient().setVariants(new ArrayList<>());
		}
		if(Objects.isNull(recipe.getIngredient().getComponents())){
			recipe.getIngredient().setComponents(new ArrayList<>());
		}

		// handle for Garden Fresh
		if (recipe.getProduct().getProductId() == 868) {
			for (IngredientProduct id : recipe.getIngredient().getProducts()) {
				for (IngredientProductDetail ipd : id.getDetails()) {
					criticalProductKeys.put(InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(),
							ipd.getUom().name()), ipd.getQuantity());
					productKeys.put(InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(),
							ipd.getUom().name()), ipd.getQuantity());
				}
			}
		}

		for (IngredientProduct id : recipe.getIngredient().getProducts()) {
			// critical
			if (id.isCritical()) {
				for (IngredientProductDetail ipd : id.getDetails()) {
					if (ipd.isCritical()) {
						criticalProductKeys.put(InventoryUtils.unitSCMProductKey(unitId,
								ipd.getProduct().getProductId(), ipd.getUom().name()), ipd.getQuantity());
					}
				}
			}
			// all
			for (IngredientProductDetail ipd : id.getDetails()) {
				productKeys.put(
						InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(), ipd.getUom().name()),
						ipd.getQuantity());
			}
		}

		for (IngredientVariant iv : recipe.getIngredient().getVariants()) {
			// critical
			if (iv.isCritical()) {
				for (IngredientVariantDetail ivd : iv.getDetails()) {
					if (ivd.isDefaultSetting()) {
						criticalProductKeys.put(
								InventoryUtils.unitSCMProductKey(unitId, ivd.getProductId(), ivd.getUom().name()),
								ivd.getQuantity());
					}
				}
			}
			// all
			for (IngredientVariantDetail ivd : iv.getDetails()) {
				if (ivd.isDefaultSetting()) {
					productKeys.put(InventoryUtils.unitSCMProductKey(unitId, ivd.getProductId(), ivd.getUom().name()),
							ivd.getQuantity());
				}
			}
		}

		for (IngredientProductDetail ipd : recipe.getIngredient().getComponents()) {
			// critical
			if (ipd.isCritical()) {
				criticalProductKeys.put(
						InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(), ipd.getUom().name()),
						ipd.getQuantity());
			}
			// all
			productKeys.put(
					InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(), ipd.getUom().name()),
					ipd.getQuantity());
		}

	}

	public static void getCriticalProductId(int unitId, RecipeDetail recipe, Set<Integer> criticalProductKeys) {
		if (recipe == null || recipe.getIngredient() == null) {
			return;
		}

		// handle for Garden Fresh
		if (recipe.getProduct().getProductId() == 868) {
			for (IngredientProduct id : recipe.getIngredient().getProducts()) {
				for (IngredientProductDetail ipd : id.getDetails()) {
					criticalProductKeys.add(ipd.getProduct().getProductId());
				}
			}
		}

		for (IngredientProduct id : recipe.getIngredient().getProducts()) {
			if (id.isCritical()) {
				for (IngredientProductDetail ipd : id.getDetails()) {
					if (ipd.isCritical()) {
						criticalProductKeys.add(ipd.getProduct().getProductId());
					}
				}
			}
		}
		for (IngredientVariant iv : recipe.getIngredient().getVariants()) {
			if (iv.isCritical()) {
				for (IngredientVariantDetail ivd : iv.getDetails()) {
					if (ivd.isDefaultSetting()) {
						criticalProductKeys.add(ivd.getProductId());
					}
				}
			}
		}

		for (IngredientProductDetail ipd : recipe.getIngredient().getComponents()) {
			if (ipd.isCritical()) {
				criticalProductKeys.add(ipd.getProduct().getProductId());
			}
		}
	}

	public static void getAllRecipeProductInventory(int unitId, RecipeDetail recipe,
			Map<String, RecipeInventory> criticalProductKeys, Map<String, RecipeInventory> productKeys) {
		if (recipe == null || recipe.getIngredient() == null) {
			return;
		}
		for (IngredientProduct id : recipe.getIngredient().getProducts()) {
			if (id.isCritical()) {
				for (IngredientProductDetail ipd : id.getDetails()) {
					if (ipd.isCritical()) {
						criticalProductKeys.put(
								InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(),
										ipd.getUom().name()),
								new RecipeInventory(ipd.getProduct().getProductId(), ipd.getProduct().getName(),
										ipd.getQuantity(), BigDecimal.ZERO, ipd.getUom().name(), ipd.isCritical()));
					}
				}
			}
			for (IngredientProductDetail ipd : id.getDetails()) {
				productKeys.put(
						InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(), ipd.getUom().name()),
						new RecipeInventory(ipd.getProduct().getProductId(), ipd.getProduct().getName(),
								ipd.getQuantity(), BigDecimal.ZERO, ipd.getUom().name(), id.isCritical()));
			}
		}
		for (IngredientVariant iv : recipe.getIngredient().getVariants()) {
			if (iv.isCritical()) {
				for (IngredientVariantDetail ivd : iv.getDetails()) {
					if (ivd.isDefaultSetting()) {
						criticalProductKeys.put(
								InventoryUtils.unitSCMProductKey(unitId, ivd.getProductId(), ivd.getUom().name()),
								new RecipeInventory(ivd.getProductId(), ivd.getAlias(), ivd.getQuantity(),
										BigDecimal.ZERO, ivd.getUom().name(), iv.isCritical()));
					}
				}
			}
			for (IngredientVariantDetail ivd : iv.getDetails()) {
				if (ivd.isDefaultSetting()) {
					productKeys.put(InventoryUtils.unitSCMProductKey(unitId, ivd.getProductId(), ivd.getUom().name()),
							new RecipeInventory(ivd.getProductId(), ivd.getAlias(), ivd.getQuantity(), BigDecimal.ZERO,
									ivd.getUom().name(), iv.isCritical()));
				}
			}
		}
		for (IngredientProductDetail ipd : recipe.getIngredient().getComponents()) {
			if (ipd.isCritical()) {
				criticalProductKeys.put(
						InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(), ipd.getUom().name()),
						new RecipeInventory(ipd.getProduct().getProductId(), ipd.getProduct().getName(),
								ipd.getQuantity(), BigDecimal.ZERO, ipd.getUom().name(), ipd.isCritical()));
			}
			productKeys.put(
					InventoryUtils.unitSCMProductKey(unitId, ipd.getProduct().getProductId(), ipd.getUom().name()),
					new RecipeInventory(ipd.getProduct().getProductId(), ipd.getProduct().getName(), ipd.getQuantity(),
							BigDecimal.ZERO, ipd.getUom().name(), ipd.isCritical()));
		}
	}

	public static String getUnitProductKey(int unitId, int productId) {
		return unitId + "@@" + productId;
	}

	public static boolean isShortExpire(Date systemExpireTime, Date productExpiryDate) {
		if (productExpiryDate == null) {
			return false;
		}
		return productExpiryDate.before(systemExpireTime);
	}

	public static Date getNextExpireTimeLimit() {
		Calendar calendar = getCalender();
		int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
		if (hour < 5) {
			calendar.add(Calendar.DATE, -1);
		}
		calendar.add(Calendar.DATE, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 5);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();

	}

	/*------- Inventory calculations ----------------*/

	public static void addToMap(Map<String, InventoryData> map, CostDetail c, Date expireTime) {
		String key = InventoryUtils.unitSCMProductKey(c.getUnitId(), c.getKeyType(), c.getKeyId(), c.getUom());
		InventoryData d = map.get(key);
		if (d == null) {
			d = new InventoryData(key, c.getKeyId(), c.getName(), BigDecimal.ZERO, c.getUom(), BigDecimal.ZERO);
		}
		// d.setQty(d.getQty().add(getQuantity(c.getQuantity())));
		updateInventoryData(d, c.getQuantity(), expireTime, c.getExpiryDate(), c.getPrice(), InventoryAction.ADD);
		map.put(key, d);
	}

	public static void updateInventoryData(InventoryData i, BigDecimal quantity, Date systemExpireTime,
			Date productExpireTime, BigDecimal price, InventoryAction action) {

		boolean shortExpire = InventoryUtils.isShortExpire(systemExpireTime, productExpireTime);
		if (InventoryAction.ADD.equals(action)) {
			i.setQty(AppUtils.add(i.getQty(), quantity));
			if (shortExpire) {
				i.setExQty(i.getExQty().add(quantity));
			}
			if (BigDecimal.ZERO.compareTo(i.getPrice()) == 0) {
				i.setPrice(price);
			}
			if (BigDecimal.ZERO.compareTo(quantity) != 0) {
				i.setPrice(AppUtils.divideWithScale10(AppUtils.multiplyWithScale10(i.getQty(), i.getPrice())
						.add(AppUtils.multiplyWithScale10(quantity, price)), AppUtils.add(i.getQty(), quantity)));
			}
		} else if (InventoryAction.REMOVE.equals(action)) {
			i.setQty(AppUtils.subtract(i.getQty(), quantity));
			if (i.getExQty() != null && BigDecimal.ZERO.compareTo(i.getExQty()) != 0) {
				if (i.getExQty().compareTo(quantity) > 0) {
					i.setExQty(AppUtils.subtract(i.getExQty(), quantity));
				} else {
					i.setExQty(BigDecimal.ZERO);
				}
			}
		} else if (InventoryAction.REFRESH.equals(action)) {
			// Check this if quantity is incorrect
			i.setQty(quantity);
			if (shortExpire) {
				i.setExQty(quantity);
			}
			i.setPrice(i.getPrice());
		}
	}

	public static BigDecimal getQuantity(BigDecimal q) {
		return q != null && BigDecimal.ZERO.compareTo(q) == -1 ? q : BigDecimal.ZERO;
	}
}
