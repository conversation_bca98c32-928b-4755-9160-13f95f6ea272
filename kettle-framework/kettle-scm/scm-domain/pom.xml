<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kettle-scm</artifactId>
        <groupId>com.stpl.tech.kettle.scm</groupId>
        <version>5.0.22</version>
		<relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>scm-domain</artifactId>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.stpl.tech.util</groupId>
            <artifactId>utility-service</artifactId>
            <version>5.0.22</version>
        </dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.master</groupId>
            <artifactId>master-domain</artifactId>
            <version>5.0.22</version>
        </dependency>
        <!-- <dependency>
            <groupId>com.stpl.tech.kettle.transaction</groupId>
            <artifactId>data-model</artifactId>
            <version>4.1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>-->
    </dependencies>
    <build>
        <!--<plugins>
            <plugin>
                <groupId>org.jvnet.jaxb2.maven2</groupId>
                <artifactId>maven-jaxb2-plugin</artifactId>
                <version>0.11.0</version>
                <executions>
                    <execution>
                        <id>scm-schema</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <schemaDirectory>src/main/xsds/</schemaDirectory>
                            <generatePackage>com.stpl.tech.scm.domain.model</generatePackage>
                            <generateDirectory>${project.build.directory}/generated-sources/xjc-scm</generateDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>-->
    </build>
</project>
