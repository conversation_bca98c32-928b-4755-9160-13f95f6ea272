package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class VendorAdvancePaymentAuditLog {
    private Integer advancePaymentAuditLogId;
    private BigDecimal amount;
    private BigDecimal fromAmount;
    private BigDecimal toAmount;
    private Integer prId;
    private Integer advancePaymentId;
    private String status;
    private String loggedBy;
    private Date loggedAt;
}
