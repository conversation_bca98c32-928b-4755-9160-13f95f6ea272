/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.09 at 05:35:16 PM IST 
//

package com.stpl.tech.scm.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TaxDetail", propOrder = { "type", "code", "percentage", "value" })
public class TaxDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5577464341032140963L;

	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal percentage;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal value;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal total;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal taxable;

	protected String type;

	protected String code;

	/**
	 * Gets the value of the percentage property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getPercentage() {
		return percentage;
	}

	/**
	 * Sets the value of the percentage property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setPercentage(BigDecimal value) {
		this.percentage = value;
	}

	/**
	 * Gets the value of the value property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getValue() {
		return value;
	}

	/**
	 * Sets the value of the value property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setValue(BigDecimal value) {
		this.value = value;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public BigDecimal getTotal() {
		return total;
	}

	public void setTotal(BigDecimal total) {
		this.total = total;
	}

	public BigDecimal getTaxable() {
		return taxable;
	}

	public void setTaxable(BigDecimal taxable) {
		this.taxable = taxable;
	}

}
