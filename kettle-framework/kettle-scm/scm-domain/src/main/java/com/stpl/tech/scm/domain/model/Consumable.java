//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.07.04 at 04:41:46 PM IST 
//

package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import org.apache.poi.ss.formula.eval.NotImplementedException;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for Consumable complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="Consumable"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="uom" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Consumable", propOrder = { "productId", "name", "quantity", "uom" })
public class Consumable implements InventoryItemVO {

	protected Integer id;
	protected int productId;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal quantity;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal price;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal cost = BigDecimal.ZERO;
	@XmlElement(required = true)
	protected String uom;
	protected List<InventoryItemDrilldown> drillDowns;
	protected  BigDecimal taxableQuantity = BigDecimal.ZERO;
	protected  BigDecimal taxPercentage = BigDecimal.ZERO;
	/**
	 * Gets the value of the productId property.
	 * 
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * Sets the value of the productId property.
	 * 
	 */
	public void setProductId(int value) {
		this.productId = value;
	}

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the quantity property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getQuantity() {
		return quantity;
	}

	/**
	 * Sets the value of the quantity property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setQuantity(BigDecimal value) {
		this.quantity = value;
	}

	/**
	 * Gets the value of the uom property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUom() {
		return uom;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * Sets the value of the uom property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUom(String value) {
		this.uom = value;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyId()
	 */
	@Override
	public int getKeyId() {
		return this.productId;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyType()
	 */
	@Override
	public PriceUpdateEntryType getKeyType() {
		return PriceUpdateEntryType.PRODUCT;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyId()
	 */
	@Override
	public int getItemKeyId() {
		return this.id;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyType()
	 */
	@Override
	public StockEventType getItemKeyType() {
		return StockEventType.CONSUMPTION;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getDrillDowns()
	 */
	@Override
	public List<InventoryItemDrilldown> getDrillDowns() {
		if (drillDowns == null) {
			drillDowns = new ArrayList<>();
		}
		return drillDowns;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getPrice()
	 */
	@Override
	public BigDecimal getPrice() {
		return this.price;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#setPrice(java.math.
	 * BigDecimal)
	 */
	@Override
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getCost() {
		return cost;
	}

	public void setCost(BigDecimal cost) {
		this.cost = cost;
	}

	@Override
	public void setKeyType(PriceUpdateEntryType type) {
		throw new NotImplementedException("This should not be called for Consumable");
	}

	@Override
	public Date getExpiryDate() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setExpiryDate(Date expiryDate) {
		// TODO Auto-generated method stub
	}

	@Override
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	public BigDecimal getTaxableQuantity() {
		return taxableQuantity;
	}

	public void setTaxableQuantity(BigDecimal taxableQuantity) {
		this.taxableQuantity = taxableQuantity;
	}

	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}
}
