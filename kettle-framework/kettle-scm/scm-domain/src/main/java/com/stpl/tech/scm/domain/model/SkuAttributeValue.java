//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SkuAttributeValue complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SkuAttributeValue"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="skuAttributeValueId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attributeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attributeValueId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="mappingStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SkuAttributeValue", propOrder = {
    "skuAttributeValueId",
    "skuId",
    "attributeId",
    "attributeValueId",
    "mappingStatus"
})
public class SkuAttributeValue {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer skuAttributeValueId;
    protected int skuId;
    protected int attributeId;
    protected int attributeValueId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus mappingStatus;

    /**
     * Gets the value of the skuAttributeValueId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getSkuAttributeValueId() {
        return skuAttributeValueId;
    }

    /**
     * Sets the value of the skuAttributeValueId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setSkuAttributeValueId(Integer value) {
        this.skuAttributeValueId = value;
    }

    /**
     * Gets the value of the skuId property.
     * 
     */
    public int getSkuId() {
        return skuId;
    }

    /**
     * Sets the value of the skuId property.
     * 
     */
    public void setSkuId(int value) {
        this.skuId = value;
    }

    /**
     * Gets the value of the attributeId property.
     * 
     */
    public int getAttributeId() {
        return attributeId;
    }

    /**
     * Sets the value of the attributeId property.
     * 
     */
    public void setAttributeId(int value) {
        this.attributeId = value;
    }

    /**
     * Gets the value of the attributeValueId property.
     * 
     */
    public int getAttributeValueId() {
        return attributeValueId;
    }

    /**
     * Sets the value of the attributeValueId property.
     * 
     */
    public void setAttributeValueId(int value) {
        this.attributeValueId = value;
    }

    /**
     * Gets the value of the mappingStatus property.
     *
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getMappingStatus() {
        return mappingStatus;
    }

    /**
     * Sets the value of the mappingStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setMappingStatus(SwitchStatus value) {
        this.mappingStatus = value;
    }

}
