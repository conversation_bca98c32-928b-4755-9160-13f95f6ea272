//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.08 at 03:20:20 PM IST 
//

package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import org.apache.poi.ss.formula.eval.NotImplementedException;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for BookingConsumption complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="BookingConsumption"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="calculatedQuantity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="availableSkuList" type="{http://www.w3schools.com}IdCodeName" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "BookingConsumption", propOrder = { "id", "productId", "productName", "skuId", "skuName",
		"unitOfMeasure", "calculatedQuantity", "unitPrice", "totalCost", "availableSkuList" })
public class ReverseBookingConsumption implements InventoryItemVO {

	protected int id;
	protected int productId;
	@XmlElement(required = true)
	protected String productName;
	protected int skuId;
	@XmlElement(required = true)
	protected String skuName;
	@XmlElement(required = true)
	protected String unitOfMeasure;
	@XmlElement(required = true)
	protected BigDecimal calculatedQuantity;
	@XmlElement(required = true)
	protected BigDecimal unitPrice;
	@XmlElement(required = true)
	protected BigDecimal totalCost;
	protected List<IdCodeName> availableSkuList;
	List<InventoryItemDrilldown> drillDowns;
	@XmlElement(required = false, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date expiryDate;
	protected List<ReverseBookingConsumption> bookingConsumption;

	@XmlElement(required = false, type = String.class, nillable = true)
	@XmlSchemaType(name = "profile")
	protected String profile;
	protected boolean autoProduction;
	protected boolean recipeRequire;
	protected boolean mappedAutoProduction;
	protected BigDecimal wastageQuantity;



	/**
	 * Gets the value of the id property.
	 * 
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the productId property.
	 * 
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * Sets the value of the productId property.
	 * 
	 */
	public void setProductId(int value) {
		this.productId = value;
	}

	/**
	 * Gets the value of the productName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * Sets the value of the productName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setProductName(String value) {
		this.productName = value;
	}

	/**
	 * Gets the value of the skuId property.
	 * 
	 */
	public int getSkuId() {
		return skuId;
	}

	/**
	 * Sets the value of the skuId property.
	 * 
	 */
	public void setSkuId(int value) {
		this.skuId = value;
	}

	/**
	 * Gets the value of the skuName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSkuName() {
		return skuName;
	}

	/**
	 * Sets the value of the skuName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSkuName(String value) {
		this.skuName = value;
	}

	/**
	 * Gets the value of the unitOfMeasure property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	/**
	 * Sets the value of the unitOfMeasure property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUnitOfMeasure(String value) {
		this.unitOfMeasure = value;
	}

	/**
	 * Gets the value of the calculatedQuantity property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getCalculatedQuantity() {
		return calculatedQuantity;
	}

	/**
	 * Sets the value of the calculatedQuantity property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setCalculatedQuantity(BigDecimal value) {
		this.calculatedQuantity = value;
	}

	/**
	 * Gets the value of the unitPrice property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	/**
	 * Sets the value of the unitPrice property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setUnitPrice(BigDecimal value) {
		this.unitPrice = value;
	}

	/**
	 * Gets the value of the totalCost property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	/**
	 * Sets the value of the totalCost property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setTotalCost(BigDecimal value) {
		this.totalCost = value;
	}

	/**
	 * Gets the value of the availableSkuList property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a snapshot.
	 * Therefore any modification you make to the returned list will be present
	 * inside the JAXB object. This is why there is not a <CODE>set</CODE> method
	 * for the availableSkuList property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getAvailableSkuList().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link IdCodeName }
	 * 
	 * 
	 */
	public List<IdCodeName> getAvailableSkuList() {
		if (availableSkuList == null) {
			availableSkuList = new ArrayList<IdCodeName>();
		}
		return this.availableSkuList;
	}

	@Override
	@JsonIgnore
	public int getKeyId() {
		return this.skuId;
	}

	@Override
	@JsonIgnore
	public PriceUpdateEntryType getKeyType() {
		return PriceUpdateEntryType.SKU;
	}

	@Override
	@JsonIgnore
	public int getItemKeyId() {
		return this.getId();
	}

	@Override
	@JsonIgnore
	public StockEventType getItemKeyType() {
		return StockEventType.BOOKING_CONSUMPTION;
	}

	@Override
	@JsonIgnore
	public List<InventoryItemDrilldown> getDrillDowns() {
		if(drillDowns == null){
			this.drillDowns = new ArrayList<>();
		}
		return this.drillDowns;
	}
	
	@Override
	@JsonIgnore
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	@Override
	@JsonIgnore
	public BigDecimal getQuantity() {
		return this.calculatedQuantity;
	}

	@Override
	@JsonIgnore
	public BigDecimal getPrice() {
		return this.unitPrice;
	}

	@Override
	public void setPrice(BigDecimal value) {
		this.unitPrice = value;
	}

	@Override
	public void setQuantity(BigDecimal value) {
		this.calculatedQuantity = value;
	}

	@Override
	@JsonIgnore
	public String getUom() {
		return this.unitOfMeasure;
	}

	@Override
	public void setKeyType(PriceUpdateEntryType type) {
		// TODO document why this method is empty
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public List<ReverseBookingConsumption> getBookingConsumption() {
		if (bookingConsumption == null) {
			bookingConsumption = new ArrayList<ReverseBookingConsumption>();
		}
		return bookingConsumption;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	public boolean isAutoProduction() {
		return autoProduction;
	}

	public void setAutoProduction(boolean autoProduction) {
		this.autoProduction = autoProduction;
	}

	public boolean isRecipeRequire() {
		return recipeRequire;
	}

	public void setRecipeRequire(boolean recipeRequire) {
		this.recipeRequire = recipeRequire;
	}

	public boolean isMappedAutoProduction() {
		return mappedAutoProduction;
	}

	public void setMappedAutoProduction(boolean mappedAutoProduction) {
		this.mappedAutoProduction = mappedAutoProduction;
	}

	public BigDecimal getWastageQuantity() {
		return wastageQuantity;
	}

	public void setWastageQuantity(BigDecimal wastageQuantity) {
		this.wastageQuantity = wastageQuantity;
	}
}
