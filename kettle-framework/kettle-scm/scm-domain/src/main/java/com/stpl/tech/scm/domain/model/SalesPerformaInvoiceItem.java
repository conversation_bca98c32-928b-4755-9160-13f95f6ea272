//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.07.03 at 02:06:35 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;


/**
 * <p>Java class for SalesPerformaInvoiceItem complex type.
 * <p>
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;complexType name="SalesPerformaInvoiceItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="sku" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="uom" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pkg" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="ratio" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="pkgQty" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="qty" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="currPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="pkgPrice" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="sellPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="currAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="pkgAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="sellAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalTax" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="taxes" type="{http://www.w3schools.com}SalesPerformaItemTax" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SalesPerformaInvoiceItem", propOrder = {
        "id",
        "sku",
        "uom",
        "pkg",
        "ratio",
        "pkgQty",
        "qty",
        "currPrice",
        "pkgPrice",
        "sellPrice",
        "currAmount",
        "pkgAmount",
        "sellAmount",
        "totalTax",
        "taxes"
})
public class SalesPerformaInvoiceItem implements InventoryItemVO{

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true)
    protected IdCodeName sku;
    @XmlElement(required = true)
    protected String uom;
    @XmlElement(required = true)
    protected IdCodeName pkg;
    @XmlElement(required = true)
    protected BigDecimal ratio;
    @XmlElement(required = true)
    protected BigDecimal pkgQty;
    @XmlElement(required = true)
    protected BigDecimal qty;
    @XmlElement(required = true)
    protected BigDecimal currPrice;
    @XmlElement(required = true)
    protected BigDecimal pkgPrice;
    @XmlElement(required = true)
    protected BigDecimal sellPrice;
    @XmlElement(required = true)
    protected BigDecimal currAmount;
    @XmlElement(required = true)
    protected BigDecimal pkgAmount;
    @XmlElement(required = true)
    protected BigDecimal sellAmount;
    @XmlElement(required = true)
    protected BigDecimal totalTax;
    protected List<SalesPerformaItemTax> taxes;

    protected List<InventoryItemDrilldown> drillDowns;
    protected Date expiryDate;
    protected List<EntityAssetMapping> associatedAssetMappings;
    @XmlElement(required = true)
    protected  String alias;
    protected BigDecimal correctedSellPrice;
    protected BigDecimal correctedTax;
    protected BigDecimal correctedSellAmount;

    /**
     * Gets the value of the id property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the sku property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getSku() {
        return sku;
    }

    /**
     * Sets the value of the sku property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setSku(IdCodeName value) {
        this.sku = value;
    }

    /**
     * Gets the value of the uom property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getUom() {
        return uom;
    }

    @Override
    public Date getExpiryDate() {
        return null;
    }

    @Override
    public void setExpiryDate(Date expiryDate) {

    }

    /**
     * Sets the value of the uom property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUom(String value) {
        this.uom = value;
    }

    /**
     * Gets the value of the pkg property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getPkg() {
        return pkg;
    }

    /**
     * Sets the value of the pkg property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setPkg(IdCodeName value) {
        this.pkg = value;
    }

    /**
     * Gets the value of the ratio property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getRatio() {
        return ratio;
    }

    /**
     * Sets the value of the ratio property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setRatio(BigDecimal value) {
        this.ratio = value;
    }

    /**
     * Gets the value of the pkgQty property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getPkgQty() {
        return pkgQty;
    }

    /**
     * Sets the value of the pkgQty property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setPkgQty(BigDecimal value) {
        this.pkgQty = value;
    }

    /**
     * Gets the value of the qty property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getQty() {
        return qty;
    }

    /**
     * Sets the value of the qty property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setQty(BigDecimal value) {
        this.qty = value;
    }

    /**
     * Gets the value of the currPrice property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getCurrPrice() {
        return currPrice;
    }

    /**
     * Sets the value of the currPrice property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setCurrPrice(BigDecimal value) {
        this.currPrice = value;
    }

    /**
     * Gets the value of the pkgPrice property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getPkgPrice() {
        return pkgPrice;
    }

    /**
     * Sets the value of the pkgPrice property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setPkgPrice(BigDecimal value) {
        this.pkgPrice = value;
    }

    /**
     * Gets the value of the sellPrice property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getSellPrice() {
        return sellPrice;
    }

    /**
     * Sets the value of the sellPrice property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setSellPrice(BigDecimal value) {
        this.sellPrice = value;
    }

    /**
     * Gets the value of the currAmount property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getCurrAmount() {
        return currAmount;
    }

    /**
     * Sets the value of the currAmount property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setCurrAmount(BigDecimal value) {
        this.currAmount = value;
    }

    /**
     * Gets the value of the pkgAmount property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getPkgAmount() {
        return pkgAmount;
    }

    /**
     * Sets the value of the pkgAmount property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setPkgAmount(BigDecimal value) {
        this.pkgAmount = value;
    }

    /**
     * Gets the value of the sellAmount property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getSellAmount() {
        return sellAmount;
    }

    /**
     * Sets the value of the sellAmount property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setSellAmount(BigDecimal value) {
        this.sellAmount = value;
    }

    /**
     * Gets the value of the totalTax property.
     *
     * @return possible object is
     * {@link BigDecimal }
     */
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    /**
     * Sets the value of the totalTax property.
     *
     * @param value allowed object is
     *              {@link BigDecimal }
     */
    public void setTotalTax(BigDecimal value) {
        this.totalTax = value;
    }

    /**
     * Gets the value of the taxes property.
     * <p>
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the taxes property.
     * <p>
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTaxes().add(newItem);
     * </pre>
     * <p>
     * <p>
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SalesPerformaItemTax }
     */
    public List<SalesPerformaItemTax> getTaxes() {
        if (taxes == null) {
            taxes = new ArrayList<SalesPerformaItemTax>();
        }
        return this.taxes;
    }

    /***********************************  Over ridden methods for Inventory  ******************************************/

    /*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyId()
	 */
    @Override
    @JsonIgnore
    public int getKeyId() {
        return this.sku.getId();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyType()
     */
    @Override
    @JsonIgnore
    public PriceUpdateEntryType getKeyType() {
        return PriceUpdateEntryType.SKU;
    }

    @Override
    public void setKeyType(PriceUpdateEntryType type) {}


    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyId()
     */
    @Override
    @JsonIgnore
    public int getItemKeyId() {
        return this.id;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyType()
     */
    @Override
    @JsonIgnore
    public StockEventType getItemKeyType() {
        return StockEventType.TRANSFER_OUT;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getDrillDowns()
     */
    @Override
    @JsonIgnore
    public List<InventoryItemDrilldown> getDrillDowns() {
        if (this.drillDowns == null) {
            this.drillDowns = new ArrayList<>();
        }
        return this.drillDowns;
    }

    @Override
    public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
        this.drillDowns = drillDowns;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getQuantity()
     */
    @Override
    @JsonIgnore
    public BigDecimal getQuantity() {
        return this.qty;
    }

    @Override
    @JsonIgnore
    public BigDecimal getPrice() {
        return this.currPrice;
    }

    @Override
    public void setPrice(BigDecimal price) {
        this.currPrice = price;
    }


    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#setQuantity(java.math.
     * BigDecimal)
     */
    @Override
    public void setQuantity(BigDecimal qty) {
        this.qty = qty;
    }

    public List<EntityAssetMapping> getAssociatedAssetMappings() {
        return associatedAssetMappings;
    }

    public void setAssociatedAssetMappings(List<EntityAssetMapping> associatedAssetMappings) {
        this.associatedAssetMappings = associatedAssetMappings;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public BigDecimal getCorrectedSellPrice() {
        return correctedSellPrice;
    }

    public void setCorrectedSellPrice(BigDecimal correctedSellPrice) {
        this.correctedSellPrice = correctedSellPrice;
    }

    public BigDecimal getCorrectedTax() {
        return correctedTax;
    }

    public void setCorrectedTax(BigDecimal correctedTax) {
        this.correctedTax = correctedTax;
    }

    public BigDecimal getCorrectedSellAmount() {
        return correctedSellAmount;
    }

    public void setCorrectedSellAmount(BigDecimal correctedSellAmount) {
        this.correctedSellAmount = correctedSellAmount;
    }
}
