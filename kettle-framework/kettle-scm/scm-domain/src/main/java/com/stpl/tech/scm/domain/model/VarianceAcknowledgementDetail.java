package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VarianceAcknowledgementDetail {

    private Integer id;
    private Date businessDate;
    private Date generationTime;
    private Integer unitId;
    private BigDecimal inventoryCost;
    private BigDecimal varianceCost;
    private BigDecimal variancePercentage;
    private String frequency;
    private String acknowledged;
    private Integer acknowledgedBy;
    private Date acknowledgedTime;
    private Integer scmDayCloseEventId;
    private String acknowledgementRequired;
    private String acknowledgementType;
    private String comment;

}
