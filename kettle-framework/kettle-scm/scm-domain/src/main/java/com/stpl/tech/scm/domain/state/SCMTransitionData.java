//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.06 at 03:23:09 PM IST 
//


package com.stpl.tech.scm.domain.state;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="fromStateCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="toStateCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="event" type="{http://www.w3schools.com}SCMTransitionEvent"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}SCMTransitionStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "fromStateCode",
    "toStateCode",
    "event",
    "status"
})
@XmlRootElement(name = "SCMTransitionData")
public class SCMTransitionData {

    @XmlElement(required = true)
    protected String fromStateCode;
    @XmlElement(required = true)
    protected String toStateCode;
    @XmlElement(required = true)
    protected SCMTransitionEvent event;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SCMTransitionStatus status;

    /**
     * Gets the value of the fromStateCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getFromStateCode() {
        return fromStateCode;
    }

    /**
     * Sets the value of the fromStateCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setFromStateCode(String value) {
        this.fromStateCode = value;
    }

    /**
     * Gets the value of the toStateCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getToStateCode() {
        return toStateCode;
    }

    /**
     * Sets the value of the toStateCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setToStateCode(String value) {
        this.toStateCode = value;
    }

    /**
     * Gets the value of the event property.
     *
     * @return
     *     possible object is
     *     {@link SCMTransitionEvent }
     *
     */
    public SCMTransitionEvent getEvent() {
        return event;
    }

    /**
     * Sets the value of the event property.
     *
     * @param value
     *     allowed object is
     *     {@link SCMTransitionEvent }
     *
     */
    public void setEvent(SCMTransitionEvent value) {
        this.event = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link SCMTransitionStatus }
     *
     */
    public SCMTransitionStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link SCMTransitionStatus }
     *     
     */
    public void setStatus(SCMTransitionStatus value) {
        this.status = value;
    }

}
