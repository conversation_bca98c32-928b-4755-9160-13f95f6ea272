package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AssetRecovery {

    private Integer recoveryId;
    private Integer assetId;
    private AssetDefinition assetDefinition;
    private Integer eventId;
    private StockEventDefinition stockEventDefinition;
    private IdCodeName unit;
    private List<AssetRecoveryDetail> assetRecoveryDetail;
    private BigDecimal amountToRecover;
    private String recoveryStatus;
    private BigDecimal amountRecovered;
    private String employeeRecoveryStatus;
    private BigDecimal employeeRecoveryAmount;
    private String insuranceRecoveryStatus;
    private BigDecimal insuranceRecoveryAmount;
    private BigDecimal writeOffAmount;
    private IdCodeName createdBy;
    private IdCodeName approvedBy;
    private Date creationDate;
}
