package com.stpl.tech.scm.domain.model;

import java.util.Date;

public class GatepassStatusDetail {
	private Integer id;
	private Integer gatepassId;
	private String toStatus;
	private String fromStatus;
	private String transitionStatus;
	private String reason;
	private IdCodeName updatedBy;
	private Date updatedAt;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getGatepassId() {
		return gatepassId;
	}

	public void setGatepassId(Integer gatepassId) {
		this.gatepassId = gatepassId;
	}

	public String getToStatus() {
		return toStatus;
	}

	public void setToStatus(String toStatus) {
		this.toStatus = toStatus;
	}

	public String getFromStatus() {
		return fromStatus;
	}

	public void setFromStatus(String fromStatus) {
		this.fromStatus = fromStatus;
	}

	public String getTransitionStatus() {
		return transitionStatus;
	}

	public void setTransitionStatus(String transitionStatus) {
		this.transitionStatus = transitionStatus;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public IdCodeName getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(IdCodeName updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

}
