package com.stpl.tech.scm.domain.model;

/**
 * Created by Chaayos on 28-07-2017.
 */
public class SkuPackagingKey {

    private int skuId;
    private int packagingId;

    public SkuPackagingKey(){

    }

    public SkuPackagingKey(int skuId, int packagingId) {
        this.skuId = skuId;
        this.packagingId = packagingId;
    }

    public int getSkuId() {
        return skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    public int getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(int packagingId) {
        this.packagingId = packagingId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SkuPackagingKey that = (SkuPackagingKey) o;

        if (skuId != that.skuId) return false;
        return packagingId == that.packagingId;

    }

    @Override
    public int hashCode() {
        int result = skuId;
        result = 31 * result + packagingId;
        return result;
    }
}
