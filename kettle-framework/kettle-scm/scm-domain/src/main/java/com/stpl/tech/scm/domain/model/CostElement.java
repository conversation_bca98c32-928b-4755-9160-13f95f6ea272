package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;

public class CostElement {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	@XmlElement(required = true, nillable = true)
	protected String code;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected String description;
	@XmlElement(required = true)
	protected String status;
	@XmlElement(required = true)
	protected String ascCode;
	@XmlElement(required = true)
	protected BigDecimal taxRate;
	@XmlElement(required = true)
	protected BigDecimal tdsRate;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected ListDetail category;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected ListDetail department;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected ListDetail division;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected ListType subCategory;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected ListData subSubCategory;
	@XmlElement(required = true)
	protected String capex;
	@XmlElement(required = true)
	protected String uom;
	@XmlElement(required = true)
	protected List<String> uoms;
	@XmlElement(required = true)
	protected String isPriceUpdate;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public BigDecimal getTdsRate() {
		return tdsRate;
	}

	public void setTdsRate(BigDecimal tdsRate) {
		this.tdsRate = tdsRate;
	}
	
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public ListDetail getCategory() {
		return category;
	}

	public void setCategory(ListDetail category) {
		this.category = category;
	}

	public ListDetail getDepartment() {
		return department;
	}

	public void setDepartment(ListDetail department) {
		this.department = department;
	}

	public ListDetail getDivision() {
		return division;
	}

	public void setDivision(ListDetail division) {
		this.division = division;
	}

	public ListType getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(ListType subCategory) {
		this.subCategory = subCategory;
	}

	public ListData getSubSubCategory() {
		return subSubCategory;
	}

	public void setSubSubCategory(ListData subSubCategory) {
		this.subSubCategory = subSubCategory;
	}

	public String getCapex() {
		return capex;
	}

	public void setCapex(String capex) {
		this.capex = capex;
	}

	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	public String getAscCode() {
		return ascCode;
	}

	public void setAscCode(String ascCode) {
		this.ascCode = ascCode;
	}

	public List<String> getUoms() {
		return uoms;
	}

	public void setUoms(List<String> uoms) {
		this.uoms = uoms;
	}

	public String getIsPriceUpdate() {
		return isPriceUpdate;
	}

	public void setIsPriceUpdate(String isPriceUpdate) {
		this.isPriceUpdate = isPriceUpdate;
	}
	
}
