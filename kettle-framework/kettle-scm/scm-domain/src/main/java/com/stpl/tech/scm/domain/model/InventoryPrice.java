/**
 * 
 */
package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
public class InventoryPrice implements Comparable<InventoryPrice>{

	private PriceUpdateEntryType type;
	private int id;
	private BigDecimal price;
	private Date expiryDate;
	private String dateString;

	public InventoryPrice(PriceUpdateEntryType type, int id, BigDecimal price) {
		super();
		this.type = type;
		this.id = id;
		this.price = price.setScale(6, BigDecimal.ROUND_HALF_UP);
	}

	public InventoryPrice(PriceUpdateEntryType type, int id, BigDecimal price, Date expiryDate, String dateString) {
		super();
		this.type = type;
		this.id = id;
		this.price = price;
		this.expiryDate = expiryDate;
		this.dateString = dateString;
	}

	public PriceUpdateEntryType getType() {
		return type;
	}

	public void setType(PriceUpdateEntryType type) {
		this.type = type;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public String getDateString() {
		return dateString;
	}

	public void setDateString(String dateString) {
		this.dateString = dateString;
	}

	@Override
	public int hashCode() {

		final int prime = 31;
		int result = 1;
		result = prime * result + id;
		// commented out to generate similar hash code
		// result = prime * result + ((price == null) ? 0 : price.hashCode());
		result = prime * result + ((type == null) ? 0 : type.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		InventoryPrice other = (InventoryPrice) obj;
		if (id != other.id)
			return false;
		if (dateString == null || other.dateString == null || !dateString.equals(other.dateString)) {
			return false;
		}
		if (price == null) {
			if (other.price != null)
				return false;
		} else if (!price.setScale(6, BigDecimal.ROUND_HALF_UP)
				.equals(other.price.setScale(6, BigDecimal.ROUND_HALF_UP)))
			return false;
		if (!type.name().equals(other.type.name()))
			return false;
		return true;
	}

	@Override
	public int compareTo(InventoryPrice o) {
		//future to past
		return o.getExpiryDate().compareTo(this.getExpiryDate());
	}

}
