package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalDetail {
    private Integer approvalRequestId;
    private Integer eventId;
    private Integer assetId;
    private Integer skuId;
    private Integer unitId;
    private String unitName;
    private String type;
    private String status;
    private String skuName;
    private String skuImage;
    private BigDecimal cost;
    private Integer requestedBy;
    private String requestedByName;
    private Integer requestedTo;
    private String requestDate;
    private Integer approvedBy;
    private String approvalDate;
}
