package com.stpl.tech.scm.domain.model;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class TrimmedCostDetail implements Serializable{
    private static final long serialVersionUID = -3740402749449237758L;
    private int unitId;
    private String name;
    private String uom;
    private int keyId;
    private BigDecimal prices;
    private BigDecimal quantity;
    private Date expiryDate;
    private PriceUpdateEntryType keyType;

    public TrimmedCostDetail() {
    }

    public TrimmedCostDetail(int unitId, String name, String uom, int keyId, BigDecimal quantity, Date expiryDate, PriceUpdateEntryType keyType, BigDecimal prices) {
        this.unitId = unitId;
        this.name = name;
        this.uom = uom;
        this.keyId = keyId;
        this.quantity = quantity;
        this.expiryDate = expiryDate;
        this.keyType = keyType;
        this.prices = prices;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public int getKeyId() {
        return keyId;
    }

    public void setKeyId(int keyId) {
        this.keyId = keyId;
    }

    public PriceUpdateEntryType getKeyType() {
        return keyType;
    }

    public void setKeyType(PriceUpdateEntryType keyType) {
        this.keyType = keyType;
    }

    public BigDecimal getPrices() {
        return prices;
    }

    public void setPrices(BigDecimal prices) {
        this.prices = prices;
    }
}
