package com.stpl.tech.scm.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;

public class CapexRequestDetail {

	 @XmlElement(required = true, type = Integer.class, nillable = true)
	    protected Integer id;
	    @XmlSchemaType(name = "date")
	    protected Date generationTime;
	    @XmlSchemaType(name = "date")
	    protected Date lastUpdateTime;
	    @XmlElement(required = true)
	    protected Integer generatedBy;
	    @XmlElement(required = true)
	    protected Integer lastUpdatedBy;
	    @XmlElement(required = true)
	    protected Integer unitId;
	    @XmlElement(required = true)
	    protected String unitName;
	    @XmlElement(required = true)
	    protected String fileName;
	    @XmlElement(required = true)
	    protected String type;
	    @XmlElement(required = true)
	    protected String templateVersion;
	    @XmlElement(required = true)
	    protected String accessKey;
	    @XmlElement(required = true)
	    protected String status;
		public Integer getId() {
			return id;
		}
		public void setId(Integer id) {
			this.id = id;
		}
		public Date getGenerationTime() {
			return generationTime;
		}
		public void setGenerationTime(Date generationTime) {
			this.generationTime = generationTime;
		}
		public Date getLastUpdateTime() {
			return lastUpdateTime;
		}
		public void setLastUpdateTime(Date lastUpdateTime) {
			this.lastUpdateTime = lastUpdateTime;
		}
		public Integer getGeneratedBy() {
			return generatedBy;
		}
		public void setGeneratedBy(Integer generatedBy) {
			this.generatedBy = generatedBy;
		}
		public Integer getLastUpdatedBy() {
			return lastUpdatedBy;
		}
		public void setLastUpdatedBy(Integer lastUpdatedBy) {
			this.lastUpdatedBy = lastUpdatedBy;
		}
		public Integer getUnitId() {
			return unitId;
		}
		public void setUnitId(Integer unitId) {
			this.unitId = unitId;
		}
		public String getUnitName() {
			return unitName;
		}
		public void setUnitName(String unitName) {
			this.unitName = unitName;
		}
		public String getFileName() {
			return fileName;
		}
		public void setFileName(String fileName) {
			this.fileName = fileName;
		}
		public String getType() {
			return type;
		}
		public void setType(String type) {
			this.type = type;
		}
		public String getTemplateVersion() {
			return templateVersion;
		}
		public void setTemplateVersion(String templateVersion) {
			this.templateVersion = templateVersion;
		}
		public String getAccessKey() {
			return accessKey;
		}
		public void setAccessKey(String accessKey) {
			this.accessKey = accessKey;
		}
		public String getStatus() {
			return status;
		}
		public void setStatus(String status) {
			this.status = status;
		}
	    
	    
}
