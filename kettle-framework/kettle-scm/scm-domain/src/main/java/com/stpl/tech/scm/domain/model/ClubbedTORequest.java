package com.stpl.tech.scm.domain.model;



import java.util.Date;
import java.util.List;
import java.util.Map;

public class ClubbedTORequest {
    private List<Integer> clubRoIds;
    private Date fulfilmentDate;
    private IdCodeName generationUnit;
    private IdCodeName generatedBy;
    private IdCodeName lastUpdatedBy;
    private Map<Integer, Map<Integer, GoodsReceivedItem>> skuToPackagingMapping;

    public List<Integer> getClubRoIds() {
        return clubRoIds;
    }

    public void setClubRoIds(List<Integer> clubRoIds) {
        this.clubRoIds = clubRoIds;
    }

    public Date getFulfilmentDate() {
        return fulfilmentDate;
    }

    public void setFulfilmentDate(Date fulfilmentDate) {
        this.fulfilmentDate = fulfilmentDate;
    }


    public IdCodeName getGenerationUnit() {
        return generationUnit;
    }

    public void setGenerationUnit(IdCodeName generationUnit) {
        this.generationUnit = generationUnit;
    }

    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(IdCodeName generatedBy) {
        this.generatedBy = generatedBy;
    }

    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(IdCodeName lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }


    public Map<Integer, Map<Integer, GoodsReceivedItem>> getSkuToPackagingMapping() {
        return skuToPackagingMapping;
    }

    public void setSkuToPackagingMapping(Map<Integer, Map<Integer, GoodsReceivedItem>> skuToPackagingMapping) {
        this.skuToPackagingMapping = skuToPackagingMapping;
    }
}
