package com.stpl.tech.scm.domain.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CreditDebitNoteDetail {

    private Integer Id;
    private String invoiceId;
    private Integer vendorId;
    private String status;
    private Integer createdBy;
    private Date generationTime;
    private String approvalRequired;
    private Integer approvedBy;
    private Date updatedAt;
    private String creditNoteId;
    private Integer creditNoteDocId;
    private String creditNoteDocUrl;
    private BigDecimal totalTax;
    private BigDecimal netAmount;
    private BigDecimal totalAmount;
    private String invoiceDocUrl;
    private String invoiceDate;
    private String vendorInvoiceNumber;
    private List<CreditDebitNoteItemDetail> itemDetails;

}
