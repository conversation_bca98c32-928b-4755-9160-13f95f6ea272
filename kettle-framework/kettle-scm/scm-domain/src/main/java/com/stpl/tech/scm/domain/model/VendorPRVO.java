package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class VendorPRVO {

    private List<DebitBalanceVO> debitBalances;
    private BigDecimal totalDebitBalance;
    private Integer paymentSentCount;
    private BigDecimal paymentSentAmount;

    public List<DebitBalanceVO> getDebitBalances() {
        if(debitBalances == null){
            debitBalances = new ArrayList<>();
        }
        return debitBalances;
    }

    public Integer getPaymentSentCount() {
        return paymentSentCount;
    }

    public void setPaymentSentCount(Integer paymentSentCount) {
        this.paymentSentCount = paymentSentCount;
    }

    public BigDecimal getPaymentSentAmount() {
        return paymentSentAmount;
    }

    public void setPaymentSentAmount(BigDecimal paymentSentAmount) {
        this.paymentSentAmount = paymentSentAmount;
    }

    public BigDecimal getTotalDebitBalance() {
        return totalDebitBalance;
    }

    public void setTotalDebitBalance(BigDecimal totalDebitBalance) {
        this.totalDebitBalance = totalDebitBalance;
    }
}
