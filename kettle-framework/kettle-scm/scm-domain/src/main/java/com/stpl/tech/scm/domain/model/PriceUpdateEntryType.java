//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.02.22 at 07:12:15 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PriceUpdateEntryType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="PriceUpdateEntryType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="SKU"/&gt;
 *     &lt;enumeration value="PRODUCT"/&gt;
 *     &lt;enumeration value="RECIPE"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "PriceUpdateEntryType")
@XmlEnum
public enum PriceUpdateEntryType {

    SKU,
    PRODUCT,
    RECIPE_SCM,
    RECIPE_CAFE,
    RECIPE_COD,
    RECIPE_TAKE_AWAY,
    RECIPE_DELIVERY;

    public String value() {
        return name();
    }

    public static PriceUpdateEntryType fromValue(String v) {
        return valueOf(v);
    }

}
