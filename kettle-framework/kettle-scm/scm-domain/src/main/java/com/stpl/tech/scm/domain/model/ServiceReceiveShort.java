package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ServiceReceiveShort {
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, nillable = true)
    protected VendorBasicDetail vendor;
    @XmlSchemaType(name = "string")
    protected ServiceOrderStatus status;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal totalAmount;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal price;
    @XmlElement(required = true)
    protected BigDecimal totalTaxes;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal extraCharges;
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date creationTime;
    @XmlElement(required = true)
    protected List<ServiceOrder> serviceOrderList;
    protected DocumentDetail serviceReceiveDocument;
    protected IdCodeName company;
    protected IdCodeName location;
    protected Integer paymentRequestId;
    private IdCodeName deliveryState;
    private String type;

    public IdCodeName getDeliveryState() {
        return deliveryState;
    }

    public void setDeliveryState(IdCodeName deliveryState) {
        this.deliveryState = deliveryState;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public VendorBasicDetail getVendor() {
        return vendor;
    }

    public void setVendor(VendorBasicDetail vendor) {
        this.vendor = vendor;
    }

    public ServiceOrderStatus getStatus() {
        return status;
    }

    public void setStatus(ServiceOrderStatus status) {
        this.status = status;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotalTaxes() {
        return totalTaxes;
    }

    public void setTotalTaxes(BigDecimal totalTaxes) {
        this.totalTaxes = totalTaxes;
    }

    public BigDecimal getExtraCharges() {
        return extraCharges;
    }

    public void setExtraCharges(BigDecimal extraCharges) {
        this.extraCharges = extraCharges;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public List<ServiceOrder> getServiceOrderList() {
        return serviceOrderList;
    }

    public void setServiceOrderList(List<ServiceOrder> serviceOrderList) {
        this.serviceOrderList = serviceOrderList;
    }

    public DocumentDetail getServiceReceiveDocument() {
        return serviceReceiveDocument;
    }

    public void setServiceReceiveDocument(DocumentDetail serviceReceiveDocument) {
        this.serviceReceiveDocument = serviceReceiveDocument;
    }

    public IdCodeName getCompany() {
        return company;
    }

    public void setCompany(IdCodeName company) {
        this.company = company;
    }

    public IdCodeName getLocation() {
        return location;
    }

    public void setLocation(IdCodeName location) {
        this.location = location;
    }

    public Integer getPaymentRequestId() {
        return paymentRequestId;
    }

    public void setPaymentRequestId(Integer paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
