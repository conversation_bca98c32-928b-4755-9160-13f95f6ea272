package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class WastageAggregatedData {
    private String skuName;
    private String productName;
    private String subCategory;
    private String uom;
    private BigDecimal totalQuantity;
    private BigDecimal totalCost;
    private Integer wastageEvents;

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public Integer getWastageEvents() {
        return wastageEvents;
    }

    public void setWastageEvents(Integer wastageEvents) {
        this.wastageEvents = wastageEvents;
    }
}
