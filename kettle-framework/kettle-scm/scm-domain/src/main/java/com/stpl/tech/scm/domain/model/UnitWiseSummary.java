package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.domain.model.UnitBasicDetail;

import java.util.List;

public class UnitWiseSummary {
    UnitBasicDetail unitBasicDetails;
    Boolean gntFlag;
    Boolean chaayosFlag;

    public UnitBasicDetail getUnitBasicDetails() {
        return unitBasicDetails;
    }

    public void setUnitBasicDetails(UnitBasicDetail unitBasicDetails) {
        this.unitBasicDetails = unitBasicDetails;
    }

    public Boolean getGntFlag() {
        return gntFlag;
    }

    public void setGntFlag(Boolean gntFlag) {
        this.gntFlag = gntFlag;
    }

    public Boolean getChaayosFlag() {
        return chaayosFlag;
    }

    public void setChaayosFlag(Boolean chaayosFlag) {
        this.chaayosFlag = chaayosFlag;
    }
}
