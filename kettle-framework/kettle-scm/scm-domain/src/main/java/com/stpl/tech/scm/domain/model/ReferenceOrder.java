//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.11 at 12:23:12 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for ReferenceOrder complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ReferenceOrder"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="initiationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="requestUnit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="fulfillmentUnit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="fulfillmentDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}SCMOrderStatus"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="referenceOrderMenuItems" type="{http://www.w3schools.com}ReferenceOrderMenuItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="referenceOrderScmItems" type="{http://www.w3schools.com}ReferenceOrderScmItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="requestOrderIds" type="{http://www.w3.org/2001/XMLSchema}int" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ReferenceOrder", propOrder = {
    "id",
    "generationTime",
    "initiationTime",
    "lastUpdateTime",
    "requestUnit",
    "generatedBy",
    "fulfillmentUnit",
    "fulfillmentDate",
    "status",
    "comment",
    "referenceOrderMenuItems",
    "referenceOrderScmItems",
    "requestOrderIds",
    "numberOfDays",
    "raiseBy", "refOrderSource"
})
public class ReferenceOrder {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date initiationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(required = true)
    protected IdCodeName requestUnit;
    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName fulfillmentUnit;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date fulfillmentDate;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SCMOrderStatus status;
    @XmlElement(required = true)
    protected String comment;
    protected List<ReferenceOrderMenuItem> referenceOrderMenuItems;
    protected List<ReferenceOrderScmItem> referenceOrderScmItems;
    @XmlElement(type = Integer.class)
    protected List<Integer> requestOrderIds;
    protected Integer numberOfDays;
    protected Boolean raiseBy;
    protected String refOrderSource;
    protected RegularOrderEvent orderEvent;
    protected String refreshDate;
    protected String missedF9Products;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the generationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getGenerationTime() {
        return generationTime;
    }

    /**
     * Sets the value of the generationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGenerationTime(Date value) {
        this.generationTime = value;
    }

    /**
     * Gets the value of the initiationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getInitiationTime() {
        return initiationTime;
    }

    /**
     * Sets the value of the initiationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setInitiationTime(Date value) {
        this.initiationTime = value;
    }

    /**
     * Gets the value of the lastUpdateTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * Sets the value of the lastUpdateTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastUpdateTime(Date value) {
        this.lastUpdateTime = value;
    }

    /**
     * Gets the value of the requestUnit property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getRequestUnit() {
        return requestUnit;
    }

    /**
     * Sets the value of the requestUnit property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setRequestUnit(IdCodeName value) {
        this.requestUnit = value;
    }

    /**
     * Gets the value of the generatedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    /**
     * Sets the value of the generatedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedBy(IdCodeName value) {
        this.generatedBy = value;
    }

    /**
     * Gets the value of the fulfillmentUnit property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getFulfillmentUnit() {
        return fulfillmentUnit;
    }

    /**
     * Sets the value of the fulfillmentUnit property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setFulfillmentUnit(IdCodeName value) {
        this.fulfillmentUnit = value;
    }

    /**
     * Gets the value of the fulfillmentDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getFulfillmentDate() {
        return fulfillmentDate;
    }

    /**
     * Sets the value of the fulfillmentDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFulfillmentDate(Date value) {
        this.fulfillmentDate = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link SCMOrderStatus }
     *
     */
    public SCMOrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link SCMOrderStatus }
     *
     */
    public void setStatus(SCMOrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the comment property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the referenceOrderMenuItems property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the referenceOrderMenuItems property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getReferenceOrderMenuItems().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ReferenceOrderMenuItem }
     * 
     * 
     */
    public List<ReferenceOrderMenuItem> getReferenceOrderMenuItems() {
        if (referenceOrderMenuItems == null) {
            referenceOrderMenuItems = new ArrayList<ReferenceOrderMenuItem>();
        }
        return this.referenceOrderMenuItems;
    }

    /**
     * Gets the value of the referenceOrderScmItems property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the referenceOrderScmItems property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getReferenceOrderScmItems().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ReferenceOrderScmItem }
     * 
     * 
     */
    public List<ReferenceOrderScmItem> getReferenceOrderScmItems() {
        if (referenceOrderScmItems == null) {
            referenceOrderScmItems = new ArrayList<ReferenceOrderScmItem>();
        }
        return this.referenceOrderScmItems;
    }

    /**
     * Gets the value of the requestOrderIds property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the requestOrderIds property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRequestOrderIds().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Integer }
     *
     *
     */
    public List<Integer> getRequestOrderIds() {
        if (requestOrderIds == null) {
            requestOrderIds = new ArrayList<Integer>();
        }
        return this.requestOrderIds;
    }

    public Integer getNumberOfDays() {
        return numberOfDays;
    }

    public void setNumberOfDays(Integer numberOfDays) {
        this.numberOfDays = numberOfDays;
    }

    public Boolean getRaiseBy() {
        return raiseBy;
    }

    public void setRaiseBy(Boolean raiseBy) {
        this.raiseBy = raiseBy;
    }

    public String getRefOrderSource() {
        return refOrderSource;
    }

    public void setRefOrderSource(String refOrderSource) {
        this.refOrderSource = refOrderSource;
    }

    public RegularOrderEvent getOrderEvent() {
        return orderEvent;
    }

    public void setOrderEvent(RegularOrderEvent orderEvent) {
        this.orderEvent = orderEvent;
    }

    public String getRefreshDate() {
        return refreshDate;
    }

    public void setRefreshDate(String refreshDate) {
        this.refreshDate = refreshDate;
    }

    public String getMissedF9Products() {
        return missedF9Products;
    }

    public void setMissedF9Products(String missedF9Products) {
        this.missedF9Products = missedF9Products;
    }
}