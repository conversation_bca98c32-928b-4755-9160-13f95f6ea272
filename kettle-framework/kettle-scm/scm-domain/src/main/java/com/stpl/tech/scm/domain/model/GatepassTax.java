package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class GatepassTax {
	private Integer id;
	private GatepassItem gatepassItem;
	private String taxType;
	private String taxCode;
	private BigDecimal taxPercentage;
	private BigDecimal taxAmount;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public GatepassItem getGatepassItem() {
		return gatepassItem;
	}

	public void setGatepassItem(GatepassItem gatepassItem) {
		this.gatepassItem = gatepassItem;
	}

	public String getTaxType() {
		return taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

}
