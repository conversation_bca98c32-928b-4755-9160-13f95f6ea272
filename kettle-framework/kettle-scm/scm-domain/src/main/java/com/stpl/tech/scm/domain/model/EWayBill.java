package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class EWayBill {

	private Integer id;
	private String ewayBillNumber;
	private TransferOrder transferOrder;
	private String status;
	private BigDecimal distance;
	private Vehicle vehicle;
	private boolean ewayRequired;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getEwayBillNumber() {
		return ewayBillNumber;
	}

	public void setEwayBillNumber(String ewayBillNumber) {
		this.ewayBillNumber = ewayBillNumber;
	}

	public TransferOrder getTransferOrder() {
		return transferOrder;
	}

	public void setTransferOrder(TransferOrder transferOrder) {
		this.transferOrder = transferOrder;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public BigDecimal getDistance() {
		return distance;
	}

	public void setDistance(BigDecimal distance) {
		this.distance = distance;
	}

	public Vehicle getVehicle() {
		return vehicle;
	}

	public void setVehicle(Vehicle vehicle) {
		this.vehicle = vehicle;
	}

	public boolean isEwayRequired() {
		return ewayRequired;
	}

	public void setEwayRequired(boolean ewayRequired) {
		this.ewayRequired = ewayRequired;
	}

}
