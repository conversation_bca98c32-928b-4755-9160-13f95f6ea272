package com.stpl.tech.scm.domain.model;

import java.util.Date;

public class HolidayActivateDeactivate {
    private Date holidayDate;
    private String updatedBy;
    private Boolean activateOrDeactivate;

    public Date getHolidayDate() {
        return holidayDate;
    }

    public void setHolidayDate(Date holidayDate) {
        this.holidayDate = holidayDate;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Boolean getActivateOrDeactivate() {
        return activateOrDeactivate;
    }

    public void setActivateOrDeactivate(Boolean activateOrDeactivate) {
        this.activateOrDeactivate = activateOrDeactivate;
    }
}
