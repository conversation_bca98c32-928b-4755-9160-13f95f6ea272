package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CorrectedSalesInvoiceDetails {

    private Integer invoiceId;
    private BigDecimal additionalCharges;
    private BigDecimal totalAmount;
    private BigDecimal totalCorrectedAmount;
    private BigDecimal totalSellingCost;
    private BigDecimal totalCorrectedSellingCost;
    private BigDecimal totalTax;
    private BigDecimal totalCorrectedTax;
    private List<CorrectedSalesInvoiceItemDetails> correctedInvoiceItems;
}
