//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.07.03 at 02:06:35 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SalesPerformaStatus.
 * <p>
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="SalesPerformaStatus"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="INITIATED"/&gt;
 *     &lt;enumeration value="PERFORMA_GENERATED"/&gt;
 *     &lt;enumeration value="PENDING_DISPATCH"/&gt;
 *     &lt;enumeration value="CLOSED"/&gt;
 *     &lt;enumeration value="CANCELLED"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 */
@XmlType(name = "SalesPerformaStatus")
@XmlEnum
public enum SalesPerformaStatus {

    INITIATED,
    PERFORMA_GENERATED,
    APPROVED,
    PENDING_DISPATCH,
    CLOSED,
    DELIVERED,
    CANCELLED,
    REJECTED,
    PENDING_CANCEL_APPROVAL,
    PENDING_APPROVAL_L1,
    PENDING_APPROVAL_L2,
    DELIVERED_WITH_CORRECTION,
    CORRECTION_APPROVAL,
    CREDIT_NOTE_APPROVAL,
    DISAPPROVED;

    public String value() {
        return name();
    }

    public static SalesPerformaStatus fromValue(String v) {
        return valueOf(v);
    }

}
