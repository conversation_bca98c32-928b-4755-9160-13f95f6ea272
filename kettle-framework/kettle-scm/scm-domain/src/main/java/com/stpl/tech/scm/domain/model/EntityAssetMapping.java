/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 03:52:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EntityAssetMapping", propOrder = {
        "entityAssetMappingId",
        "entityId",
        "entityType",
        "entityCategory",
        "entitySubId",
        "assetId",
        "assetTagValue"
})
public class EntityAssetMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer entityAssetMappingId;
    @XmlElement(required = true)
    protected Integer entityId;
    @XmlElement(required = true)
    protected String entityType;
    @XmlElement(required = true)
    protected String entityCategory;
    @XmlElement(required = true)
    protected Integer entitySubId;
    @XmlElement(required = false)
    protected Integer assetId;
    @XmlElement(required = true)
    protected String assetTagValue;

    public Integer getEntityAssetMappingId() {
        return entityAssetMappingId;
    }

    public void setEntityAssetMappingId(Integer entityAssetMappingId) {
        this.entityAssetMappingId = entityAssetMappingId;
    }

    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    public String getEntityCategory() {
        return entityCategory;
    }

    public void setEntityCategory(String entityCategory) {
        this.entityCategory = entityCategory;
    }

    public Integer getEntitySubId() {
        return entitySubId;
    }

    public void setEntitySubId(Integer entitySubId) {
        this.entitySubId = entitySubId;
    }

    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public String getAssetTagValue() {
        return assetTagValue;
    }

    public void setAssetTagValue(String assetTagValue) {
        this.assetTagValue = assetTagValue;
    }
}
