package com.stpl.tech.scm.domain.model;


import java.math.BigDecimal;

public class ServiceReceivedItemDrilldown {


    protected Integer serviceReceivedItemDrilldownId;
    protected Integer serviceReceivedItemId;
    protected String description;
    protected BigDecimal length;
    protected BigDecimal width;
    protected BigDecimal height;
    protected Integer nos;
    protected BigDecimal multiplier;
    protected BigDecimal receivedQuantity;
    private String sourceUom;
    private String isExclusionEntry;

    public String getIsExclusionEntry() {
        return isExclusionEntry;
    }

    public void setIsExclusionEntry(String isExclusionEntry) {
        this.isExclusionEntry = isExclusionEntry;
    }

    public Integer getServiceReceivedItemId() {
        return serviceReceivedItemId;
    }

    public void setServiceReceivedItemId(Integer serviceReceivedItemId) {
        this.serviceReceivedItemId = serviceReceivedItemId;
    }

    public Integer getServiceReceivedItemDrilldownId() {
        return serviceReceivedItemDrilldownId;
    }

    public void setServiceReceivedItemDrilldownId(Integer serviceReceivedItemDrilldownId) {
        this.serviceReceivedItemDrilldownId = serviceReceivedItemDrilldownId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public Integer getNos() {
        return nos;
    }

    public void setNos(Integer nos) {
        this.nos = nos;
    }

    public BigDecimal getMultiplier() {
        return multiplier;
    }

    public void setMultiplier(BigDecimal multiplier) {
        this.multiplier = multiplier;
    }

    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    public String getSourceUom() {
        return sourceUom;
    }

    public void setSourceUom(String sourceUom) {
        this.sourceUom = sourceUom;
    }
}
