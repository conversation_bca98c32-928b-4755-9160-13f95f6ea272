//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.06 at 03:23:09 PM IST 
//


package com.stpl.tech.scm.domain.state;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SCMStateType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="SCMStateType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="START"/&gt;
 *     &lt;enumeration value="INTERMEDIATE"/&gt;
 *     &lt;enumeration value="TRANSIENT"/&gt;
 *     &lt;enumeration value="END"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "SCMStateType")
@XmlEnum
public enum SCMStateType {

    START,
    INTERMEDIATE,
    TRANSIENT,
    END;

    public String value() {
        return name();
    }

    public static SCMStateType fromValue(String v) {
        return valueOf(v);
    }

}
