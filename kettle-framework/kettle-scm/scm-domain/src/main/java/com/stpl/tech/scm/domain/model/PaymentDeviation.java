//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.09.11 at 11:20:21 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PaymentDeviation complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PaymentDeviation"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="paymentDeviationId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="deviationCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deviationType" type="{http://www.w3schools.com}PaymentDeviationType"/&gt;
 *         &lt;element name="deviationLevel" type="{http://www.w3schools.com}PaymentDeviationLevel"/&gt;
 *         &lt;element name="deviationDetail" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentDeviation", propOrder = {
    "paymentDeviationId",
    "deviationCode",
    "deviationType",
    "deviationLevel",
    "deviationDetail"
})
public class PaymentDeviation {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer paymentDeviationId;
    @XmlElement(required = true)
    protected String deviationCode;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentDeviationType deviationType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentDeviationLevel deviationLevel;
    @XmlElement(required = true)
    protected String deviationDetail;

    /**
     * Gets the value of the paymentDeviationId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPaymentDeviationId() {
        return paymentDeviationId;
    }

    /**
     * Sets the value of the paymentDeviationId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPaymentDeviationId(Integer value) {
        this.paymentDeviationId = value;
    }

    /**
     * Gets the value of the deviationCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeviationCode() {
        return deviationCode;
    }

    /**
     * Sets the value of the deviationCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeviationCode(String value) {
        this.deviationCode = value;
    }

    /**
     * Gets the value of the deviationType property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentDeviationType }
     *     
     */
    public PaymentDeviationType getDeviationType() {
        return deviationType;
    }

    /**
     * Sets the value of the deviationType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentDeviationType }
     *     
     */
    public void setDeviationType(PaymentDeviationType value) {
        this.deviationType = value;
    }

    /**
     * Gets the value of the deviationLevel property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentDeviationLevel }
     *     
     */
    public PaymentDeviationLevel getDeviationLevel() {
        return deviationLevel;
    }

    /**
     * Sets the value of the deviationLevel property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentDeviationLevel }
     *     
     */
    public void setDeviationLevel(PaymentDeviationLevel value) {
        this.deviationLevel = value;
    }

    /**
     * Gets the value of the deviationDetail property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeviationDetail() {
        return deviationDetail;
    }

    /**
     * Sets the value of the deviationDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeviationDetail(String value) {
        this.deviationDetail = value;
    }

}