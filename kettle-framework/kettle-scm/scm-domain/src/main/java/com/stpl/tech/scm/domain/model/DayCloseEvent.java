//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.06.13 at 06:17:07 PM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for DayCloseEvent complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="DayCloseEvent"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}StockEventType"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}StockEventStatus"/&gt;
 *         &lt;element name="unit" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="businessDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="eventLogs" type="{http://www.w3schools.com}DayCloseEventLog" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DayCloseEvent", propOrder = {
    "id",
    "type",
    "status",
    "unit",
    "businessDate",
    "eventLogs"
})
public class DayCloseEvent {

    protected int id;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected StockEventType type;
    protected String stockTakeType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected StockEventStatus status;
    @XmlElement(required = true)
    protected IdCodeName unit;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date businessDate;
    @XmlElement(required = true)
    protected List<DayCloseEventLog> eventLogs;
    protected Date generationTime;
    protected String subCategories;

    /**
     * Gets the value of the id property.
     *
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the type property.
     *
     * @return
     *     possible object is
     *     {@link StockEventType }
     *
     */
    public StockEventType getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     *
     * @param value
     *     allowed object is
     *     {@link StockEventType }
     *
     */
    public void setType(StockEventType value) {
        this.type = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link StockEventStatus }
     *
     */
    public StockEventStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link StockEventStatus }
     *
     */
    public void setStatus(StockEventStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the unit property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getUnit() {
        return unit;
    }

    /**
     * Sets the value of the unit property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setUnit(IdCodeName value) {
        this.unit = value;
    }

    /**
     * Gets the value of the businessDate property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getBusinessDate() {
        return businessDate;
    }

    /**
     * Sets the value of the businessDate property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setBusinessDate(Date value) {
        this.businessDate = value;
    }

    /**
     * Gets the value of the eventLogs property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the eventLogs property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEventLogs().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DayCloseEventLog }
     *
     *
     */
    public List<DayCloseEventLog> getEventLogs() {
        if (eventLogs == null) {
            eventLogs = new ArrayList<DayCloseEventLog>();
        }
        return this.eventLogs;
    }

	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}

    public String getStockTakeType() {
        return stockTakeType;
    }

    public void setStockTakeType(String stockTakeType) {
        this.stockTakeType = stockTakeType;
    }

    public void setEventLogs(List<DayCloseEventLog> eventLogs) {
        this.eventLogs = eventLogs;
    }

    public String getSubCategories() {
        return subCategories;
    }

    public void setSubCategories(String subCategories) {
        this.subCategories = subCategories;
    }
}
