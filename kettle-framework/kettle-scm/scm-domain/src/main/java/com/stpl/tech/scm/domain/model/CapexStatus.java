package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "CapexStatus")
@XmlEnum
public enum CapexStatus {

    CREATED,
    APPROVED,
    PENDING_APPROVAL,
    PENDING_APPROVAL_L1,
    PENDING_APPROVAL_L2,
    PENDING_APPROVAL_L3,
    REJECTED_L1,
    REJECTED_L2,
    REJECTED_L3,
    CANCELLED,
    EDITED,
    ACTIVE,
    IN_ACTIVE,
    ARCHIVED,
    INITIATE_CLOSURE,
    CLOSED,
    CLOSED_L1,
    CLOSED_L2,
    CLOSED_L3,
    DOCUMENT_UPLOADED,
    OPEX,
    CAPEX;

    public String value() {
        return name();
    }

    public static CapexStatus fromValue(String v) {
        return valueOf(v);
    }

}
