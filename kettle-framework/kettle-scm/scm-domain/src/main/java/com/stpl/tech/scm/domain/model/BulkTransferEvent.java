package com.stpl.tech.scm.domain.model;

import java.util.Date;
import java.util.List;

public class BulkTransferEvent {
    private Integer bulkTransferEventId;
    private Integer roCount;
    private Integer toCount;
    private IdCodeName generatedBy;
    private IdCodeName sourceCompany;
    private IdCodeName generationUnit;
    private Date initiationTime;
    private Date completionTime;
    private String status;
    private Boolean isInvoiceSet;
    private List<TransferOrder> transferOrderList;
    private String type;

    public Integer getBulkTransferEventId() {
        return this.bulkTransferEventId;
    }

    public void setBulkTransferEventId(Integer bulkTransferEventId) {
        this.bulkTransferEventId = bulkTransferEventId;
    }

    public Integer getRoCount() {
        return this.roCount;
    }

    public void setRoCount(Integer roCount) {
        this.roCount = roCount;
    }

    public Integer getToCount() {
        return this.toCount;
    }

    public void setToCount(Integer toCount) {
        this.toCount = toCount;
    }

    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(IdCodeName generatedBy) {
        this.generatedBy = generatedBy;
    }

    public IdCodeName getSourceCompany() {
        return sourceCompany;
    }

    public void setSourceCompany(IdCodeName sourceCompany) {
        this.sourceCompany = sourceCompany;
    }

    public IdCodeName getGenerationUnit() {
        return generationUnit;
    }

    public void setGenerationUnit(IdCodeName generationUnit) {
        this.generationUnit = generationUnit;
    }

    public Date getInitiationTime() {
        return this.initiationTime;
    }

    public void setInitiationTime(Date initiationTime) {
        this.initiationTime = initiationTime;
    }

    public Date getCompletionTime() {
        return this.completionTime;
    }

    public void setCompletionTime(Date completionTime) {
        this.completionTime = completionTime;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getInvoiceSet() {
        return isInvoiceSet;
    }

    public void setInvoiceSet(Boolean invoiceSet) {
        isInvoiceSet = invoiceSet;
    }

    public List<TransferOrder> getTransferOrderList() {
        return transferOrderList;
    }

    public void setTransferOrderList(List<TransferOrder> transferOrderList) {
        this.transferOrderList = transferOrderList;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
