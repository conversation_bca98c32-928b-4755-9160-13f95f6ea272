package com.stpl.tech.scm.domain.model;

import java.util.Date;

public class PurchaseOrderExtendedStatusLog {

    private Integer purchaseOrderExtendedStatusId;
    private String fromStatus;
    private String toStatus;
    private Integer updatedBy;
    private Date updateTime;
    private String description;
    private Integer purchaseOrderId;
    private String extensionReason;
    private Integer vendorId;
    private Date updatedExpiryDate;


    public Integer getPurchaseOrderExtendedStatusId() {
        return purchaseOrderExtendedStatusId;
    }

    public void setPurchaseOrderExtendedStatusId(Integer purchaseOrderExtendedStatusId) {
        this.purchaseOrderExtendedStatusId = purchaseOrderExtendedStatusId;
    }

    public String getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(String fromStatus) {
        this.fromStatus = fromStatus;
    }

    public String getToStatus() {
        return toStatus;
    }

    public void setToStatus(String toStatus) {
        this.toStatus = toStatus;
    }

    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getPurchaseOrderId() {
        return purchaseOrderId;
    }

    public void setPurchaseOrderId(Integer purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
    }

    public String getExtensionReason() {
        return extensionReason;
    }

    public void setExtensionReason(String extensionReason) {
        this.extensionReason = extensionReason;
    }

    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public Date getUpdatedExpiryDate() {
        return updatedExpiryDate;
    }

    public void setUpdatedExpiryDate(Date updatedExpiryDate) {
        this.updatedExpiryDate = updatedExpiryDate;
    }
}
