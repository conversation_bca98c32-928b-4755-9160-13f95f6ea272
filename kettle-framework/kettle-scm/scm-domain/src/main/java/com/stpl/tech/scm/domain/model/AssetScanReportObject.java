package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.sql.Timestamp;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AssetScanReportObject {

    private Integer assetId;

    private String assetName;

    private String assetTag;

    private String assetStatus;

    private String subCategory;

    private Integer procurementCost;

    private Integer productId;

    private Integer skuId;

    private Date createdDate;

    private Date lastTransferDate;

    private Timestamp scanTime;

    private Timestamp lastStockTake;

}
