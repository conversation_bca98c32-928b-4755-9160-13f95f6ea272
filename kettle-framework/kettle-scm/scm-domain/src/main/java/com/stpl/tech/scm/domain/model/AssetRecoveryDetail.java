package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AssetRecoveryDetail {

    private Integer recoveryDetailId;
    private Integer recoveryId;
    private String recoveryType;
    private String recoveryStatus;
    private BigDecimal amountRecovered;
    private IdCodeName recoveredFrom;
}
