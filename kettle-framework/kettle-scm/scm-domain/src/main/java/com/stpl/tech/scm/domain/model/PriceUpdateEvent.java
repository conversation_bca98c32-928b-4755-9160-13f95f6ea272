//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.02.22 at 07:12:15 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for PriceUpdateEvent complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PriceUpdateEvent"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="eventId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="createdByName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="creationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="finalizedBy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="finalizationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="finalizedByName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="eventType" type="{http://www.w3schools.com}PriceUpdateEventType"/&gt;
 *         &lt;element name="eventActionType" type="{http://www.w3schools.com}PriceUpdateEventActionType"/&gt;
 *         &lt;element name="eventStatus" type="{http://www.w3schools.com}PriceUpdateEventStatus"/&gt;
 *         &lt;element name="noOfRecords" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="noOfErrors" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dataFilePath" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="entries" type="{http://www.w3schools.com}PriceUpdateEntry" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PriceUpdateEvent", propOrder = {
    "eventId",
    "createdBy",
    "createdByName",
    "creationTime",
    "finalizedBy",
    "finalizationTime",
    "finalizedByName",
    "eventType",
    "eventActionType",
    "eventStatus",
    "noOfRecords",
    "noOfErrors",
    "dataFilePath",
    "entries"
})
public class PriceUpdateEvent {

    protected int eventId;
    protected int createdBy;
    @XmlElement(required = true)
    protected String createdByName;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date creationTime;
    protected int finalizedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date finalizationTime;
    @XmlElement(required = true)
    protected String finalizedByName;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PriceUpdateEventType eventType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PriceUpdateEventActionType eventActionType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PriceUpdateEventStatus eventStatus;
    protected int noOfRecords;
    protected int noOfErrors;
    @XmlElement(required = true)
    protected String dataFilePath;
    protected List<PriceUpdateEntry> entries;

    /**
     * Gets the value of the eventId property.
     * 
     */
    public int getEventId() {
        return eventId;
    }

    /**
     * Sets the value of the eventId property.
     * 
     */
    public void setEventId(int value) {
        this.eventId = value;
    }

    /**
     * Gets the value of the createdBy property.
     * 
     */
    public int getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     * 
     */
    public void setCreatedBy(int value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the createdByName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCreatedByName() {
        return createdByName;
    }

    /**
     * Sets the value of the createdByName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreatedByName(String value) {
        this.createdByName = value;
    }

    /**
     * Gets the value of the creationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getCreationTime() {
        return creationTime;
    }

    /**
     * Sets the value of the creationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreationTime(Date value) {
        this.creationTime = value;
    }

    /**
     * Gets the value of the finalizedBy property.
     * 
     */
    public int getFinalizedBy() {
        return finalizedBy;
    }

    /**
     * Sets the value of the finalizedBy property.
     * 
     */
    public void setFinalizedBy(int value) {
        this.finalizedBy = value;
    }

    /**
     * Gets the value of the finalizationTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getFinalizationTime() {
        return finalizationTime;
    }

    /**
     * Sets the value of the finalizationTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFinalizationTime(Date value) {
        this.finalizationTime = value;
    }

    /**
     * Gets the value of the finalizedByName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFinalizedByName() {
        return finalizedByName;
    }

    /**
     * Sets the value of the finalizedByName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFinalizedByName(String value) {
        this.finalizedByName = value;
    }

    /**
     * Gets the value of the eventType property.
     * 
     * @return
     *     possible object is
     *     {@link PriceUpdateEventType }
     *     
     */
    public PriceUpdateEventType getEventType() {
        return eventType;
    }

    /**
     * Sets the value of the eventType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PriceUpdateEventType }
     *     
     */
    public void setEventType(PriceUpdateEventType value) {
        this.eventType = value;
    }

    /**
     * Gets the value of the eventActionType property.
     * 
     * @return
     *     possible object is
     *     {@link PriceUpdateEventActionType }
     *     
     */
    public PriceUpdateEventActionType getEventActionType() {
        return eventActionType;
    }

    /**
     * Sets the value of the eventActionType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PriceUpdateEventActionType }
     *     
     */
    public void setEventActionType(PriceUpdateEventActionType value) {
        this.eventActionType = value;
    }

    /**
     * Gets the value of the eventStatus property.
     * 
     * @return
     *     possible object is
     *     {@link PriceUpdateEventStatus }
     *     
     */
    public PriceUpdateEventStatus getEventStatus() {
        return eventStatus;
    }

    /**
     * Sets the value of the eventStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link PriceUpdateEventStatus }
     *     
     */
    public void setEventStatus(PriceUpdateEventStatus value) {
        this.eventStatus = value;
    }

    /**
     * Gets the value of the noOfRecords property.
     * 
     */
    public int getNoOfRecords() {
        return noOfRecords;
    }

    /**
     * Sets the value of the noOfRecords property.
     * 
     */
    public void setNoOfRecords(int value) {
        this.noOfRecords = value;
    }

    /**
     * Gets the value of the noOfErrors property.
     * 
     */
    public int getNoOfErrors() {
        return noOfErrors;
    }

    /**
     * Sets the value of the noOfErrors property.
     * 
     */
    public void setNoOfErrors(int value) {
        this.noOfErrors = value;
    }

    /**
     * Gets the value of the dataFilePath property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDataFilePath() {
        return dataFilePath;
    }

    /**
     * Sets the value of the dataFilePath property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDataFilePath(String value) {
        this.dataFilePath = value;
    }

    /**
     * Gets the value of the entries property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the entries property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEntries().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PriceUpdateEntry }
     * 
     * 
     */
    public List<PriceUpdateEntry> getEntries() {
        if (entries == null) {
            entries = new ArrayList<PriceUpdateEntry>();
        }
        return this.entries;
    }

}
