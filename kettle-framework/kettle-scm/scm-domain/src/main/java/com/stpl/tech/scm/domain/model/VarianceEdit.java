/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VarianceEdit {
    private Integer dayCloseEventId;
    private Date dayCloseSubmittedOn;
    private Date canEditVarianceTill;
    private Boolean canEditVariance;
    private String varianceEditStatus;
    private Integer varianceUpdatedBy;
    private String varianceUpdatedByName;
    private List<VarianceEditItem> varianceEditItems = new ArrayList<>();
}
