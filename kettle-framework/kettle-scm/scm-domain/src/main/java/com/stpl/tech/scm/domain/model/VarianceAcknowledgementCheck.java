package com.stpl.tech.scm.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VarianceAcknowledgementCheck {

    private boolean varianceWarning;
    private boolean varianceBlocking;
    private boolean varianceWeeklyWarning;
    private boolean varianceWeeklyBlocking;
    private Integer daysLeftToBlock;
    private Integer daysLeftToBlockWeekly;

}
