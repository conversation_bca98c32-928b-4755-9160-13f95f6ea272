//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.09.11 at 11:20:21 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for PaymentInvoice complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PaymentInvoice"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="paymentInvoiceId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="invoiceNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="invoiceDocumentHandle" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="calculatedInvoiceAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="invoiceAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="paymentAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="extraCharges" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="invoiceDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="paymentInvoiceItems" type="{http://www.w3schools.com}PaymentInvoiceItem" maxOccurs="unbounded"/&gt;
 *         &lt;element name="deviations" type="{http://www.w3schools.com}InvoiceDeviationMapping" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="rejections" type="{http://www.w3schools.com}InvoiceDeviationMapping" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentInvoice", propOrder = {
    "paymentInvoiceId",
    "invoiceNumber",
    "invoiceDocumentHandle",
    "calculatedInvoiceAmount",
    "invoiceAmount",
    "paymentAmount",
    "extraCharges",
    "invoiceDate",
    "paymentInvoiceItems",
    "deviations",
    "rejections"
})
public class PaymentInvoice {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer paymentInvoiceId;
    @XmlElement(required = true)
    protected String invoiceNumber;
    protected int invoiceDocumentHandle;
    @XmlElement(required = true)
    protected BigDecimal calculatedInvoiceAmount;
    @XmlElement(required = true)
    protected BigDecimal invoiceAmount;
    @XmlElement(required = true)
    protected BigDecimal paymentAmount;
    @XmlElement(required = true)
    protected BigDecimal extraCharges;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date invoiceDate;
    @XmlElement(required = true)
    protected List<PaymentInvoiceItem> paymentInvoiceItems;
    protected List<InvoiceDeviationMapping> deviations;
    protected List<InvoiceDeviationMapping> rejections;

    /**
     * Gets the value of the paymentInvoiceId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getPaymentInvoiceId() {
        return paymentInvoiceId;
    }

    /**
     * Sets the value of the paymentInvoiceId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setPaymentInvoiceId(Integer value) {
        this.paymentInvoiceId = value;
    }

    /**
     * Gets the value of the invoiceNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    /**
     * Sets the value of the invoiceNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInvoiceNumber(String value) {
        this.invoiceNumber = value;
    }

    /**
     * Gets the value of the invoiceDocumentHandle property.
     *
     */
    public int getInvoiceDocumentHandle() {
        return invoiceDocumentHandle;
    }

    /**
     * Sets the value of the invoiceDocumentHandle property.
     *
     */
    public void setInvoiceDocumentHandle(int value) {
        this.invoiceDocumentHandle = value;
    }

    /**
     * Gets the value of the calculatedInvoiceAmount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getCalculatedInvoiceAmount() {
        return calculatedInvoiceAmount;
    }

    /**
     * Sets the value of the calculatedInvoiceAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setCalculatedInvoiceAmount(BigDecimal value) {
        this.calculatedInvoiceAmount = value;
    }

    /**
     * Gets the value of the invoiceAmount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    /**
     * Sets the value of the invoiceAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setInvoiceAmount(BigDecimal value) {
        this.invoiceAmount = value;
    }

    /**
     * Gets the value of the paymentAmount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    /**
     * Sets the value of the paymentAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPaymentAmount(BigDecimal value) {
        this.paymentAmount = value;
    }

    /**
     * Gets the value of the extraCharges property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getExtraCharges() {
        return extraCharges;
    }

    /**
     * Sets the value of the extraCharges property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setExtraCharges(BigDecimal value) {
        this.extraCharges = value;
    }

    /**
     * Gets the value of the invoiceDate property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getInvoiceDate() {
        return invoiceDate;
    }

    /**
     * Sets the value of the invoiceDate property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setInvoiceDate(Date value) {
        this.invoiceDate = value;
    }

    /**
     * Gets the value of the paymentInvoiceItems property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the paymentInvoiceItems property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPaymentInvoiceItems().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PaymentInvoiceItem }
     * 
     * 
     */
    public List<PaymentInvoiceItem> getPaymentInvoiceItems() {
        if (paymentInvoiceItems == null) {
            paymentInvoiceItems = new ArrayList<PaymentInvoiceItem>();
        }
        return this.paymentInvoiceItems;
    }

    /**
     * Gets the value of the deviations property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the deviations property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDeviations().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link InvoiceDeviationMapping }
     *
     *
     */
    public List<InvoiceDeviationMapping> getDeviations() {
        if (deviations == null) {
            deviations = new ArrayList<InvoiceDeviationMapping>();
        }
        return this.deviations;
    }

    /**
     * Gets the value of the rejections property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the rejections property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getRejections().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link InvoiceDeviationMapping }
     *
     *
     */
    public List<InvoiceDeviationMapping> getRejections() {
        if (rejections == null) {
            rejections = new ArrayList<InvoiceDeviationMapping>();
        }
        return this.rejections;
    }

}
