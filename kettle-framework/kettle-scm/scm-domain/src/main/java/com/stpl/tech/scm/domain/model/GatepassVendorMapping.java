package com.stpl.tech.scm.domain.model;

import java.util.Date;

public class GatepassVendorMapping {
	private Integer id;
	private VendorDetail vendor;
	private String operationType;
	private String status;
	private IdCodeName createdBy;
	private Date createdAt;
	private IdCodeName unit;
	private Integer vendorId;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public VendorDetail getVendor() {
		return vendor;
	}

	public void setVendor(VendorDetail vendor) {
		this.vendor = vendor;
	}

	public String getOperationType() {
		return operationType;
	}

	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public IdCodeName getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(IdCodeName createdBy) {
		this.createdBy = createdBy;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public IdCodeName getUnit() {
		return unit;
	}

	public void setUnit(IdCodeName unit) {
		this.unit = unit;
	}

	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

}
