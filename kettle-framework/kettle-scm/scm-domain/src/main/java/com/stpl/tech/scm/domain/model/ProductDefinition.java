/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 03:52:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for ProductDefinition complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="ProductDefinition"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="productDescription" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="categoryDefinition" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="subCategoryDefinition" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="supportsLooseOrdering" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="creationDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="hasInner" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="hasCase" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="BulkGRAllowed" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="stockKeepingFrequency" type="{http://www.w3schools.com}TimeFrequency"/&gt;
 *         &lt;element name="productCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="shelfLifeInDays" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="negotiatedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="availableAtCafe" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="availableForCafeInventory" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="fulfillmentType" type="{http://www.w3schools.com}FulfillmentType"/&gt;
 *         &lt;element name="defaultFulfillmentType" type="{http://www.w3schools.com}FulfillmentType"/&gt;
 *         &lt;element name="derivedMappings" type="{http://www.w3schools.com}DerivedMapping" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="participatesInRecipe" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="variantLevelOrdering" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="productImage" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="supportsSpecialOrdering" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="varianceType" type="{http://www.w3schools.com}VarianceType"/&gt;
 *         &lt;element name="autoProduction" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="profileId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductDefinition", propOrder = {
    "productId",
    "productName",
    "productDescription",
    "categoryDefinition",
    "subCategoryDefinition",
    "supportsLooseOrdering",
    "creationDate",
    "createdBy",
    "hasInner",
    "hasCase",
        "bulkGRAllowed",
    "stockKeepingFrequency",
    "productCode",
    "shelfLifeInDays",
    "productStatus",
    "unitOfMeasure",
    "unitPrice",
    "negotiatedUnitPrice",
    "availableAtCafe",
    "interCafeTransfer",
    "availableForCafeInventory",
    "fulfillmentType",
    "defaultFulfillmentType",
    "derivedMappings",
    "participatesInRecipe",
    "variantLevelOrdering",
    "productImage",
    "supportsSpecialOrdering",
    "varianceType",
    "autoProduction",
        "profileId",
    "recipeRequired"
})
public class ProductDefinition {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer productId;
    @XmlElement(required = true)
    protected String productName;
    @XmlElement(required = true)
    protected String productDescription;
    @XmlElement(required = true)
    protected IdCodeName categoryDefinition;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName subCategoryDefinition;
    protected boolean supportsLooseOrdering;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date creationDate;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    protected boolean hasInner;
    protected boolean recipeRequired;
    protected boolean hasCase;
    protected boolean bulkGRAllowed;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected StockTakeType stockKeepingFrequency;
    @XmlElement(required = true, nillable = true)
    protected String productCode;
    @XmlElement(defaultValue = "1")
    protected int shelfLifeInDays;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected ProductStatus productStatus;
    @XmlElement(required = true)
    protected String unitOfMeasure;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float unitPrice;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected Float negotiatedUnitPrice;
    //protected List<ProductFulfillmentType> productFulfillmentType;
    protected boolean participatesInRecipe;
    protected boolean participatesInCafeRecipe;
    protected boolean variantLevelOrdering;
    @XmlElement(required = true, nillable = true)
    protected String productImage;
    @XmlElement(required = true, type = Boolean.class, nillable = true)
    protected Boolean supportsSpecialOrdering;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected VarianceType varianceType;
    protected VarianceType kitchenVarianceType;
    protected boolean autoProduction;
    protected boolean participatesInPnl;
    protected String taxCode;
	private Boolean assetOrdering;
    protected boolean availableAtCafe;
    protected boolean interCafeTransfer;
    protected boolean availableForCafeInventory;
    protected FulfillmentType fulfillmentType;
    protected FulfillmentType defaultFulfillmentType;
    protected List<DerivedMapping> derivedMappings;
    protected Integer profileId;
    protected Integer divisionId;
    protected Integer departmentId;
    protected Integer classificationId;
    protected Integer subClassificationId;
    /**
     * Gets the value of the productId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setProductId(Integer value) {
        this.productId = value;
    }

    /**
     * Gets the value of the productName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getProductName() {
        return productName;
    }

    /**
     * Sets the value of the productName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setProductName(String value) {
        this.productName = value;
    }

    /**
     * Gets the value of the productDescription property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getProductDescription() {
        return productDescription;
    }

    /**
     * Sets the value of the productDescription property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setProductDescription(String value) {
        this.productDescription = value;
    }

    /**
     * Gets the value of the categoryDefinition property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getCategoryDefinition() {
        return categoryDefinition;
    }

    /**
     * Sets the value of the categoryDefinition property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setCategoryDefinition(IdCodeName value) {
        this.categoryDefinition = value;
    }

    /**
     * Gets the value of the subCategoryDefinition property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getSubCategoryDefinition() {
        return subCategoryDefinition;
    }

    /**
     * Sets the value of the subCategoryDefinition property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setSubCategoryDefinition(IdCodeName value) {
        this.subCategoryDefinition = value;
    }

    /**
     * Gets the value of the supportsLooseOrdering property.
     *
     */
    public boolean isSupportsLooseOrdering() {
        return supportsLooseOrdering;
    }

    /**
     * Sets the value of the supportsLooseOrdering property.
     *
     */
    public void setSupportsLooseOrdering(boolean value) {
        this.supportsLooseOrdering = value;
    }

    /**
     * Gets the value of the creationDate property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * Sets the value of the creationDate property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setCreationDate(Date value) {
        this.creationDate = value;
    }

    /**
     * Gets the value of the createdBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the hasInner property.
     *
     */
    public boolean isHasInner() {
        return hasInner;
    }

    /**
     * Sets the value of the hasInner property.
     *
     */
    public void setHasInner(boolean value) {
        this.hasInner = value;
    }

    /**
     * Gets the value of the hasCase property.
     *
     */
    public boolean isHasCase() {
        return hasCase;
    }

    /**
     * Sets the value of the hasCase property.
     *
     */
    public void setHasCase(boolean value) {
        this.hasCase = value;
    }

    /**
     * Gets the value of the stockKeepingFrequency property.
     *
     * @return
     *     possible object is
     *     {@link StockTakeType }
     *
     */
    public StockTakeType getStockKeepingFrequency() {
        return stockKeepingFrequency;
    }

    /**
     * Sets the value of the stockKeepingFrequency property.
     *
     * @param value
     *     allowed object is
     *     {@link StockTakeType }
     *
     */
    public void setStockKeepingFrequency(StockTakeType value) {
        this.stockKeepingFrequency = value;
    }

    /**
     * Gets the value of the productCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     * Sets the value of the productCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setProductCode(String value) {
        this.productCode = value;
    }

    /**
     * Gets the value of the shelfLifeInDays property.
     *
     */
    public int getShelfLifeInDays() {
        return shelfLifeInDays;
    }

    /**
     * Sets the value of the shelfLifeInDays property.
     *
     */
    public void setShelfLifeInDays(int value) {
        this.shelfLifeInDays = value;
    }

    /**
     * Gets the value of the productStatus property.
     *
     * @return
     *     possible object is
     *     {@link ProductStatus }
     *
     */
    public ProductStatus getProductStatus() {
        return productStatus;
    }

    /**
     * Sets the value of the productStatus property.
     *
     * @param value
     *     allowed object is
     *     {@link ProductStatus }
     *
     */
    public void setProductStatus(ProductStatus value) {
        this.productStatus = value;
    }

    /**
     * Gets the value of the unitOfMeasure property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    /**
     * Sets the value of the unitOfMeasure property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUnitOfMeasure(String value) {
        this.unitOfMeasure = value;
    }

    /**
     * Gets the value of the unitPrice property.
     *
     * @return
     *     possible object is
     *     {@link Float }
     *
     */
    public Float getUnitPrice() {
        return unitPrice;
    }

    /**
     * Sets the value of the unitPrice property.
     *
     * @param value
     *     allowed object is
     *     {@link Float }
     *
     */
    public void setUnitPrice(Float value) {
        this.unitPrice = value;
    }

    /**
     * Gets the value of the negotiatedUnitPrice property.
     *
     * @return
     *     possible object is
     *     {@link Float }
     *
     */
    public Float getNegotiatedUnitPrice() {
        return negotiatedUnitPrice;
    }

    /**
     * Sets the value of the negotiatedUnitPrice property.
     *
     * @param value
     *     allowed object is
     *     {@link Float }
     *
     */
    public void setNegotiatedUnitPrice(Float value) {
        this.negotiatedUnitPrice = value;
    }


    /**
     * Gets the value of the participatesInRecipe property.
     *
     */
    public boolean isParticipatesInRecipe() {
        return participatesInRecipe;
    }

    /**
     * Sets the value of the participatesInRecipe property.
     *
     */
    public void setParticipatesInRecipe(boolean value) {
        this.participatesInRecipe = value;
    }

    public boolean isParticipatesInCafeRecipe() {
        return participatesInCafeRecipe;
    }

    public void setParticipatesInCafeRecipe(boolean participatesInCafeRecipe) {
        this.participatesInCafeRecipe = participatesInCafeRecipe;
    }

    /**
     * Gets the value of the variantLevelOrdering property.
     *
     */
    public boolean isVariantLevelOrdering() {
        return variantLevelOrdering;
    }

    /**
     * Sets the value of the variantLevelOrdering property.
     *
     */
    public void setVariantLevelOrdering(boolean value) {
        this.variantLevelOrdering = value;
    }

    /**
     * Gets the value of the productImage property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getProductImage() {
        return productImage;
    }

    /**
     * Sets the value of the productImage property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setProductImage(String value) {
        this.productImage = value;
    }

    /**
     * Gets the value of the supportsSpecialOrdering property.
     *
     * @return
     *     possible object is
     *     {@link Boolean }
     *
     */
    public Boolean isSupportsSpecialOrdering() {
        return supportsSpecialOrdering;
    }

    /**
     * Sets the value of the supportsSpecialOrdering property.
     *
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *
     */
    public void setSupportsSpecialOrdering(Boolean value) {
        this.supportsSpecialOrdering = value;
    }

	/**
	 * @return the taxCode
	 */
	public String getTaxCode() {
		return taxCode;
	}

	/**
	 * @param taxCode the taxCode to set
	 */
	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	/**
	 * @param status
	 */
	public void setAssetOrdering(boolean assetOrdering) {
		this.assetOrdering = assetOrdering;
	}

	public Boolean getAssetOrdering() {
		return assetOrdering;
	}

    public FulfillmentType getDefaultFulfillmentType() {
        return defaultFulfillmentType;
    }

    public void setDefaultFulfillmentType(FulfillmentType defaultFulfillmentType) {
        this.defaultFulfillmentType = defaultFulfillmentType;
    }

    public FulfillmentType getFulfillmentType() {
        return fulfillmentType;
    }

    public void setFulfillmentType(FulfillmentType defaultFulfillmentType) {
        this.fulfillmentType = defaultFulfillmentType;
    }

    public boolean isAvailableAtCafe() {
        return availableAtCafe;
    }

    public void setAvailableAtCafe(boolean availableAtCafe) {
        this.availableAtCafe = availableAtCafe;
    }

    public boolean isAvailableForCafeInventory() {
        return availableForCafeInventory;
    }

    public void setAvailableForCafeInventory(boolean availableForCafeInventory) {
        this.availableForCafeInventory = availableForCafeInventory;
    }

    /**
     * Gets the value of the derivedMappings property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the derivedMappings property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDerivedMappings().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DerivedMapping }
     *
     *
     */
    public List<DerivedMapping> getDerivedMappings() {
        if (derivedMappings == null) {
            derivedMappings = new ArrayList<DerivedMapping>();
        }
        return this.derivedMappings;
    }


    /**
     * Gets the value of the varianceType property.
     *
     * @return
     *     possible object is
     *     {@link VarianceType }
     *
     */
    public VarianceType getVarianceType() {
        return varianceType;
    }

    /**
     * Sets the value of the varianceType property.
     *
     * @param value
     *     allowed object is
     *     {@link VarianceType }
     *
     */
    public void setVarianceType(VarianceType value) {
        this.varianceType = value;
    }

    public VarianceType getKitchenVarianceType() {
        return kitchenVarianceType;
    }

    public void setKitchenVarianceType(VarianceType kitchenVarianceType) {
        this.kitchenVarianceType = kitchenVarianceType;
    }

    /**
     * Gets the value of the autoProduction property.
     *
     */
    public boolean isAutoProduction() {
        return autoProduction;
    }

    /**
     * Sets the value of the autoProduction property.
     *
     */
    public void setAutoProduction(boolean value) {
        this.autoProduction = value;
    }

    /**
     * Gets the value of the participatesInPnl property.
     *
     */
	public boolean isParticipatesInPnl() {
		return participatesInPnl;
	}

	/**
     * Sets the value of the participatesInPnl property.
     *
     */
	public void setParticipatesInPnl(boolean participatesInPnl) {
		this.participatesInPnl = participatesInPnl;
	}

    public boolean isBulkGRAllowed() {
        return bulkGRAllowed;
    }

    public void setBulkGRAllowed(boolean bulkGRAllowed) {
        this.bulkGRAllowed = bulkGRAllowed;
    }

    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    public Integer getDivisionId() {
        return divisionId;
    }

    public void setDivisionId(Integer divisionId) {
        this.divisionId = divisionId;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getClassificationId() {
        return classificationId;
    }

    public void setClassificationId(Integer classificationId) {
        this.classificationId = classificationId;
    }

    public Integer getSubClassificationId() {
        return subClassificationId;
    }

    public void setSubClassificationId(Integer subClassificationId) {
        this.subClassificationId = subClassificationId;
    }

    public boolean isRecipeRequired() {
        return recipeRequired;
    }

    public void setRecipeRequired(boolean recipeRequired) {
        this.recipeRequired = recipeRequired;
    }

    public boolean isInterCafeTransfer() {
        return interCafeTransfer;
    }

    public void setInterCafeTransfer(boolean interCafeTransfer) {
        this.interCafeTransfer = interCafeTransfer;
    }
}

