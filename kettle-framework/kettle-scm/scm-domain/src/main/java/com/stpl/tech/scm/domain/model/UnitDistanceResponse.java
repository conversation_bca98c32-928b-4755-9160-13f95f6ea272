package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class UnitDistanceResponse {
    protected BigDecimal distance;
    protected Integer sourceUnitId;
    protected String sourcePinCode;
    protected Integer destinationUnitId;
    protected String destinationPinCode;


    public BigDecimal getDistance() {
        return distance;
    }

    public void setDistance(BigDecimal distance) {
        this.distance = distance;
    }

    public String getSourcePinCode() {
        return sourcePinCode;
    }

    public void setSourcePinCode(String sourcePinCode) {
        this.sourcePinCode = sourcePinCode;
    }

    public String getDestinationPinCode() {
        return destinationPinCode;
    }

    public void setDestinationPinCode(String destinationPinCode) {
        this.destinationPinCode = destinationPinCode;
    }

    public Integer getSourceUnitId() {
        return sourceUnitId;
    }

    public void setSourceUnitId(Integer sourceUnitId) {
        this.sourceUnitId = sourceUnitId;
    }

    public Integer getDestinationUnitId() {
        return destinationUnitId;
    }

    public void setDestinationUnitId(Integer destinationUnitId) {
        this.destinationUnitId = destinationUnitId;
    }
}
