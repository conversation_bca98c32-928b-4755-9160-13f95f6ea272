package com.stpl.tech.scm.domain.model;

import java.util.Date;

public class TransferredAsset {
    private Integer toId;
    private Integer toItemId;
    private Integer skuId;
    private String skuName;
    private Integer assetId;
    private Integer updatedAssetId;
    private String assetTag;
    private String assetStatus;
    private Integer productId;
    private String productName;
    private String updatedAssetTag;
    private String updatedBy;
    private Date lastUpdatedTime;
    private Integer generatedUnitId;
    private Integer generatedForUnitId;
    private Boolean hasChild;
    private Boolean inventoryCheck;
    private Integer grId;
    private Integer grItemId;
    private String previousAssetTag;

    public Integer getToId() {
        return toId;
    }

    public void setToId(Integer toId) {
        this.toId = toId;
    }

    public Integer getToItemId() {
        return toItemId;
    }

    public void setToItemId(Integer toItemId) {
        this.toItemId = toItemId;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public Integer getUpdatedAssetId() {
        return updatedAssetId;
    }

    public void setUpdatedAssetId(Integer updatedAssetId) {
        this.updatedAssetId = updatedAssetId;
    }

    public String getAssetTag() {
        return assetTag;
    }

    public void setAssetTag(String assetTag) {
        this.assetTag = assetTag;
    }

    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getUpdatedAssetTag() {
        return updatedAssetTag;
    }

    public void setUpdatedAssetTag(String updatedAssetTag) {
        this.updatedAssetTag = updatedAssetTag;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    public Integer getGeneratedUnitId() {
        return generatedUnitId;
    }

    public void setGeneratedUnitId(Integer generatedUnitId) {
        this.generatedUnitId = generatedUnitId;
    }

    public Integer getGeneratedForUnitId() {
        return generatedForUnitId;
    }

    public void setGeneratedForUnitId(Integer generatedForUnitId) {
        this.generatedForUnitId = generatedForUnitId;
    }

    public Boolean getHasChild() {
        return hasChild;
    }

    public void setHasChild(Boolean hasChild) {
        this.hasChild = hasChild;
    }

    public Boolean getInventoryCheck() {
        return inventoryCheck;
    }

    public void setInventoryCheck(Boolean inventoryCheck) {
        this.inventoryCheck = inventoryCheck;
    }

    public Integer getGrId() {
        return grId;
    }

    public void setGrId(Integer grId) {
        this.grId = grId;
    }

    public Integer getGrItemId() {
        return grItemId;
    }

    public void setGrItemId(Integer grItemId) {
        this.grItemId = grItemId;
    }

    public String getPreviousAssetTag() {
        return previousAssetTag;
    }

    public void setPreviousAssetTag(String previousAssetTag) {
        this.previousAssetTag = previousAssetTag;
    }
}
