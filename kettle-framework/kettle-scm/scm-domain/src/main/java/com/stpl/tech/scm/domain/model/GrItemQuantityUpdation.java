package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "grItemQuantityUpdation", propOrder = {
        "grItemDeviationId",
        "deviationReason"
})
public class GrItemQuantityUpdation {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    private Integer grItemDeviationId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    private String deviationReason;

    public Integer getGrItemDeviationId() {
        return grItemDeviationId;
    }

    public void setGrItemDeviationId(Integer grItemDeviationId) {
        this.grItemDeviationId = grItemDeviationId;
    }

    public String getDeviationReason() {
        return deviationReason;
    }

    public void setDeviationReason(String deviationReason) {
        this.deviationReason = deviationReason;
    }
}
