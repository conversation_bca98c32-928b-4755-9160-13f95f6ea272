/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.12 at 05:40:48 PM IST 
//


package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for ProductStockForUnit complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ProductStockForUnit"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="inventoryId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="opening" type="{http://www.w3.org/2001/XMLSchema}BigDecimal"/&gt;
 *         &lt;element name="stockValue" type="{http://www.w3.org/2001/XMLSchema}BigDecimal"/&gt;
 *         &lt;element name="received" type="{http://www.w3.org/2001/XMLSchema}BigDecimal"/&gt;
 *         &lt;element name="transferred" type="{http://www.w3.org/2001/XMLSchema}BigDecimal"/&gt;
 *         &lt;element name="wasted" type="{http://www.w3.org/2001/XMLSchema}BigDecimal"/&gt;
 *         &lt;element name="consumption" type="{http://www.w3.org/2001/XMLSchema}BigDecimal"/&gt;
 *         &lt;element name="expectedValue" type="{http://www.w3.org/2001/XMLSchema}BigDecimal"/&gt;
 *         &lt;element name="variance" type="{http://www.w3.org/2001/XMLSchema}BigDecimal"/&gt;
 *         &lt;element name="eventType" type="{http://www.w3schools.com}StockTakeType"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductStockForUnit", propOrder = {
    "inventoryId",
    "productId",
    "opening",
    "stockValue",
    "received",
    "transferred",
    "wasted",
    "consumption",
    "expectedValue",
    "variance"
})
public class ProductStockForUnit implements InventoryItemVO{

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer inventoryId;
    protected int productId;
    protected int skuId;
    protected BigDecimal unitPrice;
    protected BigDecimal opening;
    @XmlElement(required = true, type = BigDecimal.class, nillable = true)
    protected BigDecimal stockValue;
    @XmlElement(required = true, type = BigDecimal.class, nillable = true)
    protected BigDecimal received;
    @XmlElement(required = true, type = BigDecimal.class, nillable = true)
    protected BigDecimal receivedWithInitiatedGr;
    @XmlElement(required = true, type = BigDecimal.class, nillable = true)
    protected BigDecimal transferred;
    @XmlElement(required = true, type = BigDecimal.class, nillable = true)
    protected BigDecimal wasted;
    @XmlElement(required = true, type = BigDecimal.class, nillable = true)
    protected BigDecimal consumption;
    @XmlElement(required = true, type = BigDecimal.class, nillable = true)
    protected BigDecimal expectedValue;
    @XmlElement(required = true, type = BigDecimal.class, nillable = true)
    protected BigDecimal variance;
    @XmlElement(required = true)
    protected BigDecimal varianceCost;
    protected String uom;
    protected BigDecimal booked;
    protected BigDecimal reverseBooked;
    protected String categoryDef;
    protected  String subCategoryDef;
    protected String varianceReason;
    private PriceUpdateEntryType keyType;
    private List<InventoryItemDrilldown> drillDowns;
    private BigDecimal originalClosing;
    private BigDecimal originalVariance;
    private BigDecimal originalVarianceCost;
    private BigDecimal originalVarianceTax;
    protected BigDecimal reverseConsumption;

    /**
     * Gets the value of the inventoryId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getInventoryId() {
        return inventoryId;
    }

    /**
     * Sets the value of the inventoryId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setInventoryId(Integer value) {
        this.inventoryId = value;
    }

    /**
     * Gets the value of the productId property.
     * 
     */
    public int getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     * 
     */
    public void setProductId(int value) {
        this.productId = value;
    }


    /**
     * Gets the value of the skuId property.
     *
     */
    public int getSkuId() {
        return skuId;
    }

    /**
     * Sets the value of the skuId property.
     *
     */
    public void setSkuId(int value) {
        this.skuId = value;
    }





    /**
     * Gets the value of the opening property.
     * 
     */
    public BigDecimal getOpening() {
        return opening;
    }

    /**
     * Sets the value of the opening property.
     * 
     */
    public void setOpening(BigDecimal value) {
        this.opening = value;
    }

    
    
    /**
     * Gets the value of the stockValue property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getStockValue() {
        return stockValue;
    }

    /**
     * Sets the value of the stockValue property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setStockValue(BigDecimal value) {
        this.stockValue = value;
    }

    /**
     * Gets the value of the received property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getReceived() {
        return received;
    }

    /**
     * Sets the value of the received property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setReceived(BigDecimal value) {
        this.received = value;
    }

    /**
     * Gets the value of the transferred property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTransferred() {
        return transferred;
    }

    /**
     * Sets the value of the transferred property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTransferred(BigDecimal value) {
        this.transferred = value;
    }

    /**
     * Gets the value of the wasted property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getWasted() {
        return wasted;
    }

    /**
     * Sets the value of the wasted property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setWasted(BigDecimal value) {
        this.wasted = value;
    }

    /**
     * Gets the value of the consumption property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getConsumption() {
        return consumption;
    }

    /**
     * Sets the value of the consumption property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setConsumption(BigDecimal value) {
        this.consumption = value;
    }

    /**
     * Gets the value of the expectedValue property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getExpectedValue() {
        return expectedValue;
    }

    /**
     * Sets the value of the expectedValue property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setExpectedValue(BigDecimal value) {
        this.expectedValue = value;
    }

    /**
     * Gets the value of the variance property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getVariance() {
        return variance;
    }

    /**
     * Sets the value of the variance property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setVariance(BigDecimal value) {
        this.variance = value;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal price) {
        this.unitPrice = price;
    }

    public BigDecimal getVarianceCost() {
        return varianceCost;
    }

    public void setVarianceCost(BigDecimal varianceCost) {
        this.varianceCost = varianceCost;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    public BigDecimal getBooked() {
        return booked;
    }

    public void setBooked(BigDecimal booked) {
        this.booked = booked;
    }

    public BigDecimal getReverseBooked() {
        return reverseBooked;
    }

    public void setReverseBooked(BigDecimal reverseBooked) {
        this.reverseBooked = reverseBooked;
    }

    public String getCategoryDef() {
        return categoryDef;
    }

    public void setCategoryDef(String categoryDef) {
        this.categoryDef = categoryDef;
    }

    public String getSubCategoryDef() {
        return subCategoryDef;
    }

    public void setSubCategoryDef(String subCategoryDef) {
        this.subCategoryDef = subCategoryDef;
    }

    public String getVarianceReason() {
        return varianceReason;
    }

    public void setVarianceReason(String varianceReason) {
        this.varianceReason = varianceReason;
    }

    public BigDecimal getReceivedWithInitiatedGr() {
        return receivedWithInitiatedGr;
    }

    public void setReceivedWithInitiatedGr(BigDecimal receivedWithInitiatedGr) {
        this.receivedWithInitiatedGr = receivedWithInitiatedGr;
    }

    /*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyId()
	 */
    @Override
    public int getKeyId() {
        return PriceUpdateEntryType.SKU.equals(this.keyType) ? this.skuId : this.productId;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyType()
     */
    @Override
    public PriceUpdateEntryType getKeyType() {
        return this.keyType;
    }

    public void setKeyType(PriceUpdateEntryType keyType) {
        this.keyType = keyType;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyId()
     */
    @Override
    public int getItemKeyId() {
        return PriceUpdateEntryType.SKU.equals(this.keyType) ? this.skuId : this.productId;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyType()
     */
    @Override
    @JsonIgnore
    public StockEventType getItemKeyType() {
        return StockEventType.STOCK_TAKE;
    }

    @Override
    @JsonIgnore
    public List<InventoryItemDrilldown> getDrillDowns() {
        if(this.drillDowns==null){
            this.drillDowns = new ArrayList<>();
        }
        return this.drillDowns;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getQuantity()
     */
    @Override
    @JsonIgnore
    public BigDecimal getQuantity() {
        return this.variance.abs();
    }

    @Override
    @JsonIgnore
    public BigDecimal getPrice() {
        return unitPrice;
    }

    @Override
    public void setPrice(BigDecimal price) {
        this.unitPrice = price.abs();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#setQuantity(java.math.
     * BigDecimal)
     */
    @Override
    public void setQuantity(BigDecimal quantity) {
        this.variance = quantity;
    }

	@Override
	public Date getExpiryDate() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setExpiryDate(Date expiryDate) {
		// TODO Auto-generated method stub
	}

	@Override
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}


    @Override
    public String toString() {
        return "[PRODUCT_ID: " + this.productId + " SKU_ID: "+ this.skuId + " ACTUAL_STOCK_UPDATED: "+ this.stockValue + "]";
    }

    public BigDecimal getOriginalClosing() {
        return originalClosing;
    }

    public void setOriginalClosing(BigDecimal originalClosing) {
        this.originalClosing = originalClosing;
    }

    public BigDecimal getOriginalVariance() {
        return originalVariance;
    }

    public void setOriginalVariance(BigDecimal originalVariance) {
        this.originalVariance = originalVariance;
    }

    public BigDecimal getOriginalVarianceCost() {
        return originalVarianceCost;
    }

    public void setOriginalVarianceCost(BigDecimal originalVarianceCost) {
        this.originalVarianceCost = originalVarianceCost;
    }

    public BigDecimal getOriginalVarianceTax() {
        return originalVarianceTax;
    }

    public void setOriginalVarianceTax(BigDecimal originalVarianceTax) {
        this.originalVarianceTax = originalVarianceTax;
    }

    public BigDecimal getReverseConsumption() {
        return reverseConsumption;
    }

    public void setReverseConsumption(BigDecimal reverseConsumption) {
        this.reverseConsumption = reverseConsumption;
    }
}
