package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FixedAssetDayCloseResponseObject {
    protected List<FixedAssetCompactDefinition> availableAssetsDaily;
    protected List<FixedAssetCompactDefinition> availableAssetsWeekly;
    protected List<FixedAssetCompactDefinition> availableAssetsMonthly;
    protected Date lastDailyDayClose;
    protected Date lastWeeklyDayClose;
    protected Date lastMonthlyDayClose;
    protected Boolean blockDaily;
    protected Boolean blockWeekly;
    protected Boolean blockMonthly;
}
