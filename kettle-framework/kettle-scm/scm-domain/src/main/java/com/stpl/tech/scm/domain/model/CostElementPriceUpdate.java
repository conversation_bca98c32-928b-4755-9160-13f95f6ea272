package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CostElementPriceUpdate", propOrder = { "id", "name", "currentPrice", "updatedPrice", "status" })
public class CostElementPriceUpdate {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	@XmlElement(required = true, nillable = true)
	protected String status;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected BigDecimal currentPrice;
	@XmlElement(required = true)
	protected BigDecimal updatedPrice;
	@XmlElement(required = true)
	protected Integer costElementPriceId;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public BigDecimal getCurrentPrice() {
		return currentPrice;
	}
	public void setCurrentPrice(BigDecimal currentPrice) {
		this.currentPrice = currentPrice;
	}
	public BigDecimal getUpdatedPrice() {
		return updatedPrice;
	}
	public void setUpdatedPrice(BigDecimal updatedPrice) {
		this.updatedPrice = updatedPrice;
	}
	public Integer getCostElementPriceId() {
		return costElementPriceId;
	}
	public void setCostElementPriceId(Integer costElementPriceId) {
		this.costElementPriceId = costElementPriceId;
	}
	
	
	
	
}
