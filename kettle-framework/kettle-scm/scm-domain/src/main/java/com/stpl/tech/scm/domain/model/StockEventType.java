//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.26 at 02:20:27 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for StockEventType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="StockEventType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="CONSUMPTION"/&gt;
 *     &lt;enumeration value="TRANSFER_OUT"/&gt;
 *     &lt;enumeration value="WASTAGE"/&gt;
 *     &lt;enumeration value="RECEIVED"/&gt;
 *     &lt;enumeration value="OPENING"/&gt;
 *     &lt;enumeration value="CLOSING"/&gt;
 *     &lt;enumeration value="STOCK_TAKE"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "StockEventType")
@XmlEnum
public enum StockEventType {

    CONSUMPTION,
    TRANSFER_OUT,
    ASSET_RECEIVING,
    WASTAGE,
    RECEIVED,
    VENDOR_RECEIVED,
    OPENING,
    CLOSING,
    WH_CLOSING,
    WH_OPENING,
    STOCK_TAKE,
    BOOKING_RECEIVED,
    BOOKING_CONSUMPTION,
    MANUAL_ENTRY;

    public String value() {
        return name();
    }

    public static StockEventType fromValue(String v) {
        return valueOf(v);
    }

}
