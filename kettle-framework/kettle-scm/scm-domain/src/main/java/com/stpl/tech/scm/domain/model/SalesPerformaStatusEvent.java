//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.07.03 at 02:06:35 PM IST 
//


package com.stpl.tech.scm.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


/**
 * <p>Java class for SalesPerformaStatusEvent complex type.
 * <p>
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;complexType name="SalesPerformaStatusEvent"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="fromStatus" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="toStatus" type="{http://www.w3schools.com}TaxCategory"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}TransitionStatus"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="updatedAt" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SalesPerformaStatusEvent", propOrder = {
        "id",
        "fromStatus",
        "toStatus",
        "status",
        "updatedBy",
        "updatedAt"
})
public class SalesPerformaStatusEvent {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true)
    protected SalesPerformaStatus fromStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SalesPerformaStatus toStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected TransitionStatus status;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date updatedAt;

    /**
     * Gets the value of the id property.
     *
     * @return possible object is
     * {@link Integer }
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value allowed object is
     *              {@link Integer }
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the fromStatus property.
     *
     * @return possible object is
     * {@link SalesPerformaStatus }
     */
    public SalesPerformaStatus getFromStatus() {
        return fromStatus;
    }

    /**
     * Sets the value of the fromStatus property.
     *
     * @param value allowed object is
     *              {@link SalesPerformaStatus }
     */
    public void setFromStatus(SalesPerformaStatus value) {
        this.fromStatus = value;
    }

    /**
     * Gets the value of the toStatus property.
     *
     * @return possible object is
     * {@link SalesPerformaStatus }
     */
    public SalesPerformaStatus getToStatus() {
        return toStatus;
    }

    /**
     * Sets the value of the toStatus property.
     *
     * @param value allowed object is
     *              {@link SalesPerformaStatus }
     */
    public void setToStatus(SalesPerformaStatus value) {
        this.toStatus = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return possible object is
     * {@link TransitionStatus }
     */
    public TransitionStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value allowed object is
     *              {@link TransitionStatus }
     */
    public void setStatus(TransitionStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the updatedBy property.
     *
     * @return possible object is
     * {@link IdCodeName }
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     *
     * @param value allowed object is
     *              {@link IdCodeName }
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }

    /**
     * Gets the value of the updatedAt property.
     *
     * @return possible object is
     * {@link String }
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * Sets the value of the updatedAt property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setUpdatedAt(Date value) {
        this.updatedAt = value;
    }

}
