//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.10.17 at 03:09:10 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for GRProcessingStatus.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="GRProcessingStatus"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="PENDING"/&gt;
 *     &lt;enumeration value="IN_PROCESS"/&gt;
 *     &lt;enumeration value="PROCESSED"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "GRProcessingStatus")
@XmlEnum
public enum GRProcessingStatus {

    PENDING,
    IN_PROCESS,
    PROCESSED;

    public String value() {
        return name();
    }

    public static GRProcessingStatus fromValue(String v) {
        return valueOf(v);
    }

}
