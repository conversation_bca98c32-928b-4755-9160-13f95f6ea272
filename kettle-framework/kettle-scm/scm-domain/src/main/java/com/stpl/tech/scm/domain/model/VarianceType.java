//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.07.21 at 07:43:53 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for VarianceType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="VarianceType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="ZERO_VARIANCE"/&gt;
 *     &lt;enumeration value="COGS_CONTROLLABLES"/&gt;
 *     &lt;enumeration value="CONSUMABLES_CONTROLLABLES"/&gt;
 *     &lt;enumeration value="TBD"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "VarianceType")
@XmlEnum
public enum VarianceType {

    ZERO_VARIANCE,
    COGS_CONTROLLABLES,
    CONSUMABLES_CONTROLLABLES,
    CONSUMABLES_SMALLWARE,
    FIXED_ASSETS,
    PCC,
    YIELD_CONTROLLABLES,
    TBD;

    public String value() {
        return name();
    }

    public static VarianceType fromValue(String v) {
        return valueOf(v);
    }

}
