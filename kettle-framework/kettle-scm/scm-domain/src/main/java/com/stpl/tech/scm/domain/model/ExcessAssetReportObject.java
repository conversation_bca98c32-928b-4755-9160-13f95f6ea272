package com.stpl.tech.scm.domain.model;

import com.google.api.client.util.DateTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ExcessAssetReportObject {

    private String excessType;

    private Integer assetId;

    private String assetName;

    private String assetTag;

    private String assetStatus;

    private String subCategory;

    private Integer procurementCost;

    private Integer productId;

    private Integer skuId;

    private Integer unitId;

    private String unitName;

    private Date createdDate;

    private Date lastTransferDate;

    private DateTime scanTime;

    private String comment;

    private String imgURL;
}
