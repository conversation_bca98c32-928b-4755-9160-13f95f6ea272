//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.20 at 05:17:20 PM IST 
//

package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class ServiceOrderItem {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	protected int costElementId;
	@XmlElement(required = true)
	protected String ascCode;
	@XmlElement(required = true)
	protected String costElementName;
	@XmlElement(required = true)
	protected String serviceDescription;
	@XmlElement(required = true)
	protected BigDecimal requestedQuantity;
	@XmlElement(required = true)
	protected BigDecimal receivedQuantity;
	@XmlElement(required = true)
	protected BigDecimal pendingQuantity;
	@XmlElement(required = true)
	protected String unitOfMeasure;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal unitPrice;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal totalCost;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal amountPaid;
	protected BigDecimal totalTax;
	protected BigDecimal taxRate;
	protected BigDecimal tdsRate;
	protected boolean exemptItem;
	protected Integer businessCostCenterId;
	protected String businessCostCenterName;
	protected String type;
	protected String stateId;
	protected Integer serviceOrderId;
	protected Integer locationId;
	protected Integer received;
	protected String unitId;
	protected String departmentName;
	protected Integer departmentId;
	protected BigDecimal totalCostDup;
	protected BigDecimal totalTaxDup;
	protected BigDecimal totalAmountDup;
	protected BigDecimal budgetAmount;
	protected BigDecimal remainingAmount;
	protected BigDecimal receivingAmount;
	protected BigDecimal paidAmount;
	protected BigDecimal runningAmount;
	protected BigDecimal originalAmount;
	protected Date costElementDate;
	protected Date costElementToDate;
	protected List<ServiceOrderShort> serviceOrderShortList;
	protected String vendorName;
	protected ServiceOrderStatus status;

	public String getVendorName() {
		return vendorName;
	}

	public void setVendorName(String vendorName) {
		this.vendorName = vendorName;
	}

	public ServiceOrderStatus getStatus() {
		return status;
	}

	public ServiceOrderStatus setStatus(ServiceOrderStatus status) {
		this.status = status;
		return status;
	}

	public Integer getDepartmentId() {
		return departmentId;
	}

	public void setDepartmentId(Integer departmentId) {
		this.departmentId = departmentId;
	}

	public List<Integer> getCostElementIds() {
		return costElementIds;
	}

	public void setCostElementIds(List<Integer> costElementIds) {
		this.costElementIds = costElementIds;
	}

	protected List<Integer> costElementIds;
	
	protected List<ServiceReceiveTaxDetail> taxes;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public int getCostElementId() {
		return costElementId;
	}

	public void setCostElementId(int costElementId) {
		this.costElementId = costElementId;
	}

	public String getAscCode() {
		return ascCode;
	}

	public void setAscCode(String hsnCode) {
		this.ascCode = hsnCode;
	}

	public String getCostElementName() {
		return costElementName;
	}

	public void setCostElementName(String costElementName) {
		this.costElementName = costElementName;
	}

	public String getServiceDescription() {
		return serviceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		this.serviceDescription = serviceDescription;
	}

	public BigDecimal getRequestedQuantity() {
		return requestedQuantity;
	}

	public void setRequestedQuantity(BigDecimal requestedQuantity) {
		this.requestedQuantity = requestedQuantity;
	}

	public BigDecimal getReceivedQuantity() {
		return receivedQuantity;
	}

	public void setReceivedQuantity(BigDecimal receivedQuantity) {
		this.receivedQuantity = receivedQuantity;
	}

	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	public BigDecimal getAmountPaid() {
		return amountPaid;
	}

	public void setAmountPaid(BigDecimal amountPaid) {
		this.amountPaid = amountPaid;
	}

	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}
	
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	public BigDecimal getTdsRate() {
		return tdsRate;
	}

	public void setTdsRate(BigDecimal tdsRate) {
		this.tdsRate = tdsRate;
	}

	public boolean isExemptItem() {
		return exemptItem;
	}

	public void setExemptItem(boolean exemptItem) {
		this.exemptItem = exemptItem;
	}

	public List<ServiceReceiveTaxDetail> getTaxes() {
		return taxes;
	}

	public void setTaxes(List<ServiceReceiveTaxDetail> taxes) {
		this.taxes = taxes;
	}

	public Integer getBusinessCostCenterId() {
		return businessCostCenterId;
	}

	public void setBusinessCostCenterId(Integer businessCostCenterId) {
		this.businessCostCenterId = businessCostCenterId;
	}

	public String getBusinessCostCenterName() {
		return businessCostCenterName;
	}

	public void setBusinessCostCenterName(String businessCostCenterName) {
		this.businessCostCenterName = businessCostCenterName;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getStateId() {
		return stateId;
	}

	public void setStateId(String stateId) {
		this.stateId = stateId;
	}

	public Integer getServiceOrderId() {
		return serviceOrderId;
	}

	public void setServiceOrderId(Integer serviceOrderId) {
		this.serviceOrderId = serviceOrderId;
	}

	public Integer getLocationId() {
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}

	public BigDecimal getPendingQuantity() {
		return pendingQuantity;
	}

	public void setPendingQuantity(BigDecimal pendingQuantity) {
		this.pendingQuantity = pendingQuantity;
	}

	public Integer getReceived() {
		return received;
	}

	public void setReceived(Integer received) {
		this.received = received;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public BigDecimal getTotalCostDup() {
		return totalCostDup;
	}

	public void setTotalCostDup(BigDecimal totalCostDup) {
		this.totalCostDup = totalCostDup;
	}

	public BigDecimal getTotalTaxDup() {
		return totalTaxDup;
	}

	public void setTotalTaxDup(BigDecimal totalTaxDup) {
		this.totalTaxDup = totalTaxDup;
	}

	public BigDecimal getTotalAmountDup() {
		return totalAmountDup;
	}

	public void setTotalAmountDup(BigDecimal totalAmountDup) {
		this.totalAmountDup = totalAmountDup;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public BigDecimal getRemainingAmount() {
		return remainingAmount;
	}

	public void setRemainingAmount(BigDecimal remainingAmount) {
		this.remainingAmount = remainingAmount;
	}

	public BigDecimal getReceivingAmount() {
		return receivingAmount;
	}

	public void setReceivingAmount(BigDecimal receivingAmount) {
		this.receivingAmount = receivingAmount;
	}

	public BigDecimal getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(BigDecimal paidAmount) {
		this.paidAmount = paidAmount;
	}

	public BigDecimal getRunningAmount() {
		return runningAmount;
	}

	public void setRunningAmount(BigDecimal runningAmount) {
		this.runningAmount = runningAmount;
	}

	public BigDecimal getBudgetAmount() {
		return budgetAmount;
	}

	public void setBudgetAmount(BigDecimal budgetAmount) {
		this.budgetAmount = budgetAmount;
	}

	public BigDecimal getOriginalAmount() {
		return originalAmount;
	}

	public void setOriginalAmount(BigDecimal originalAmount) {
		this.originalAmount = originalAmount;
	}

	public Date getCostElementDate() {
		return costElementDate;
	}

	public void setCostElementDate(Date costElementDate) {
		this.costElementDate = costElementDate;
	}

	public Date getCostElementToDate() {
		return costElementToDate;
	}

	public void setCostElementToDate(Date costElementToDate) {
		this.costElementToDate = costElementToDate;
	}

	public List<ServiceOrderShort> getServiceOrderShortList() {
		return serviceOrderShortList;
	}

	public void setServiceOrderShortList(List<ServiceOrderShort> serviceOrderShortList) {
		this.serviceOrderShortList = serviceOrderShortList;
	}
}
