package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

public class ServiceOrder {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date generationTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date initiationTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date lastUpdateTime;
	@XmlElement(required = true, nillable = true)
	protected IdCodeName vendor;
	@XmlElement(required = true)
	protected IdCodeName generatedBy;
	@XmlElement(required = true, nillable = true)
	protected IdCodeName lastUpdatedBy;
	@XmlElement(required = true, nillable = true)
	protected IdCodeName approvedBy;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date fulfillmentDate;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal totalCost;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal totalAmount;
	@XmlElement(required = true, nillable = true)
	protected String receiptNumber;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected ServiceOrderStatus status;
	@XmlElement(required = true)
	protected String comment;
	@XmlElement(required = true)
	protected BigDecimal totalTaxes;
	protected Integer costCenterId;
	protected boolean vendorNotified;
	protected boolean forceClosed;
	@XmlElement(required = true)
	protected VendorDispatchLocation dispatchLocation;
	protected List<ServiceOrderItem> orderItems = new ArrayList<>();
	protected DocumentDetail soInvoiceDocument;
	protected String type;
	@XmlElement(required = true)
	protected String tagName;
	protected Boolean accountedForInPnl;
	protected Integer uploadedDocumentId;
	protected VendorAdvancePayment vendorAdvancePayment;
	protected List<VendorAdvancePayment> vendorAdvancePayments;
	protected Integer approvalOfHodDocumentId;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}

	public Date getInitiationTime() {
		return initiationTime;
	}

	public void setInitiationTime(Date initiationTime) {
		this.initiationTime = initiationTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public IdCodeName getVendor() {
		return vendor;
	}

	public void setVendor(IdCodeName vendor) {
		this.vendor = vendor;
	}

	public IdCodeName getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(IdCodeName generatedBy) {
		this.generatedBy = generatedBy;
	}

	public IdCodeName getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(IdCodeName lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	public IdCodeName getApprovedBy() {
		return approvedBy;
	}

	public void setApprovedBy(IdCodeName approvedBy) {
		this.approvedBy = approvedBy;
	}

	public Date getFulfillmentDate() {
		return fulfillmentDate;
	}

	public void setFulfillmentDate(Date fulfillmentDate) {
		this.fulfillmentDate = fulfillmentDate;
	}

	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public String getReceiptNumber() {
		return receiptNumber;
	}

	public void setReceiptNumber(String receiptNumber) {
		this.receiptNumber = receiptNumber;
	}

	public ServiceOrderStatus getStatus() {
		return status;
	}

	public void setStatus(ServiceOrderStatus status) {
		this.status = status;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public BigDecimal getTotalTaxes() {
		return totalTaxes;
	}

	public void setTotalTaxes(BigDecimal totalTaxes) {
		this.totalTaxes = totalTaxes;
	}

	public boolean isVendorNotified() {
		return vendorNotified;
	}

	public void setVendorNotified(boolean vendorNotified) {
		this.vendorNotified = vendorNotified;
	}

	public boolean isForceClosed() {
		return forceClosed;
	}

	public void setForceClosed(boolean forceClosed) {
		this.forceClosed = forceClosed;
	}

	public VendorDispatchLocation getDispatchLocation() {
		return dispatchLocation;
	}

	public void setDispatchLocation(VendorDispatchLocation dispatchLocation) {
		this.dispatchLocation = dispatchLocation;
	}

	public List<ServiceOrderItem> getOrderItems() {
		return orderItems;
	}

	public void setOrderItems(List<ServiceOrderItem> orderItems) {
		this.orderItems = orderItems;
	}

	public DocumentDetail getSoInvoiceDocument() {
		return soInvoiceDocument;
	}

	public void setSoInvoiceDocument(DocumentDetail soInvoiceDocument) {
		this.soInvoiceDocument = soInvoiceDocument;
	}

	public Integer getCostCenterId() {
		return costCenterId;
	}

	public void setCostCenterId(Integer costCenterId) {
		this.costCenterId = costCenterId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public Boolean getAccountedForInPnl() {
		return accountedForInPnl;
	}

	public void setAccountedForInPnl(Boolean accountedForInPnl) {
		this.accountedForInPnl = accountedForInPnl;
	}

	public Integer getUploadedDocumentId() {
		return uploadedDocumentId;
	}

	public void setUploadedDocumentId(Integer uploadedDocumentId) {
		this.uploadedDocumentId = uploadedDocumentId;
	}

	public VendorAdvancePayment getVendorAdvancePayment() {
		return vendorAdvancePayment;
	}

	public void setVendorAdvancePayment(VendorAdvancePayment vendorAdvancePayment) {
		this.vendorAdvancePayment = vendorAdvancePayment;
	}

	public List<VendorAdvancePayment> getVendorAdvancePayments() {
		return vendorAdvancePayments;
	}

	public void setVendorAdvancePayments(List<VendorAdvancePayment> vendorAdvancePayments) {
		this.vendorAdvancePayments = vendorAdvancePayments;
	}

	public Integer getApprovalOfHodDocumentId() {
		return approvalOfHodDocumentId;
	}

	public void setApprovalOfHodDocumentId(Integer approvalOfHodDocumentId) {
		this.approvalOfHodDocumentId = approvalOfHodDocumentId;
	}
}
