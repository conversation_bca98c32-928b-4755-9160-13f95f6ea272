
package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ServiceReceive {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	@XmlElement(required = true, nillable = true)
	protected VendorBasicDetail vendor;

	@XmlElement(required = true)
	protected IdCodeName createdBy;
	@XmlElement(required = true)
	protected IdCodeName updatedBy;

	@XmlElement(required = true, nillable = true)
	@XmlSchemaType(name = "string")
	protected InvoiceDocType docType;
	@XmlElement(required = true, nillable = true)
	protected String docNumber;

	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected ServiceOrderStatus status;

	@XmlElement(required = true)
	protected String receiveCreationType;

	@XmlElement(required = true, nillable = true)
	protected BigDecimal totalAmount;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal price;
	@XmlElement(required = true)
	protected BigDecimal totalTaxes;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal extraCharges;

	@XmlElement(required = true)
	protected List<ServiceOrder> serviceOrderList;
	protected List<ServiceReceiveItem> serviceReceiveItems;

	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date creationTime;
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date updationTime;

	protected boolean amountMatched;
	protected DocumentDetail serviceReceiveDocument;
	protected IdCodeName company;
	protected IdCodeName location;
	protected Integer paymentRequestId;
	protected Boolean toBePaid;
	private IdCodeName deliveryLocation;

	private AddressDetail dispatchAddress;
	private IdCodeName deliveryState;
	private String type;
	private List<Integer> advanceSrs;
	private VendorAdvancePayment vendorAdvancePayment;
	private List<VendorAdvancePayment> vendorAdvancePayments;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public VendorBasicDetail getVendor() {
		return vendor;
	}

	public void setVendor(VendorBasicDetail vendor) {
		this.vendor = vendor;
	}

	public IdCodeName getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(IdCodeName createdBy) {
		this.createdBy = createdBy;
	}

	public IdCodeName getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(IdCodeName updatedBy) {
		this.updatedBy = updatedBy;
	}

	public InvoiceDocType getDocType() {
		return docType;
	}

	public void setDocType(InvoiceDocType docType) {
		this.docType = docType;
	}

	public String getDocNumber() {
		return docNumber;
	}

	public void setDocNumber(String docNumber) {
		this.docNumber = docNumber;
	}

	public ServiceOrderStatus getStatus() {
		return status;
	}

	public void setStatus(ServiceOrderStatus status) {
		this.status = status;
	}

	public String getReceiveCreationType() {
		return receiveCreationType;
	}

	public void setReceiveCreationType(String receiveCreationType) {
		this.receiveCreationType = receiveCreationType;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getTotalTaxes() {
		return totalTaxes;
	}

	public void setTotalTaxes(BigDecimal totalTaxes) {
		this.totalTaxes = totalTaxes;
	}

	public BigDecimal getExtraCharges() {
		return extraCharges;
	}

	public void setExtraCharges(BigDecimal extraCharges) {
		this.extraCharges = extraCharges;
	}

	public List<ServiceOrder> getServiceOrderList() {
		return serviceOrderList;
	}

	public void setServiceOrderList(List<ServiceOrder> serviceOrderList) {
		this.serviceOrderList = serviceOrderList;
	}

	public List<ServiceReceiveItem> getServiceReceiveItems() {
		if(serviceReceiveItems == null) {
			serviceReceiveItems = new ArrayList<>();
		}
		return serviceReceiveItems;
	}

	public void setServiceReceiveItems(List<ServiceReceiveItem> serviceReceiveItems) {
		this.serviceReceiveItems = serviceReceiveItems;
	}

	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	public Date getUpdationTime() {
		return updationTime;
	}

	public void setUpdationTime(Date updationTime) {
		this.updationTime = updationTime;
	}

	public boolean isAmountMatched() {
		return amountMatched;
	}

	public void setAmountMatched(boolean amountMatched) {
		this.amountMatched = amountMatched;
	}

	public DocumentDetail getServiceReceiveDocument() {
		return serviceReceiveDocument;
	}

	public void setServiceReceiveDocument(DocumentDetail serviceReceiveDocument) {
		this.serviceReceiveDocument = serviceReceiveDocument;
	}

	public IdCodeName getCompany() {
		return company;
	}

	public void setCompany(IdCodeName company) {
		this.company = company;
	}

	public IdCodeName getLocation() {
		return location;
	}

	public void setLocation(IdCodeName location) {
		this.location = location;
	}

	public Integer getPaymentRequestId() {
		return paymentRequestId;
	}

	public void setPaymentRequestId(Integer paymentRequestId) {
		this.paymentRequestId = paymentRequestId;
	}

	public Boolean getToBePaid() {
		return toBePaid;
	}

	public void setToBePaid(Boolean toBePaid) {
		this.toBePaid = toBePaid;
	}

	public void setDeliveryLocation(IdCodeName deliveryLocation) {
		this.deliveryLocation = deliveryLocation;
	}

	public IdCodeName getDeliveryLocation() {
		return deliveryLocation;
	}

	public AddressDetail getDispatchAddress() {
		return dispatchAddress;
	}

	public void setDispatchAddress(AddressDetail dispatchAddress) {
		this.dispatchAddress = dispatchAddress;
	}

	public IdCodeName getDeliveryState() {
		return deliveryState;
	}

	public void setDeliveryState(IdCodeName deliveryState) {
		this.deliveryState = deliveryState;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public List<Integer> getAdvanceSrs() {
		return advanceSrs;
	}

	public void setAdvanceSrs(List<Integer> advanceSrs) {
		this.advanceSrs = advanceSrs;
	}

	public VendorAdvancePayment getVendorAdvancePayment() {
		return vendorAdvancePayment;
	}

	public void setVendorAdvancePayment(VendorAdvancePayment vendorAdvancePayment) {
		this.vendorAdvancePayment = vendorAdvancePayment;
	}

	public List<VendorAdvancePayment> getVendorAdvancePayments() {
		return vendorAdvancePayments;
	}

	public void setVendorAdvancePayments(List<VendorAdvancePayment> vendorAdvancePayments) {
		this.vendorAdvancePayments = vendorAdvancePayments;
	}
}
