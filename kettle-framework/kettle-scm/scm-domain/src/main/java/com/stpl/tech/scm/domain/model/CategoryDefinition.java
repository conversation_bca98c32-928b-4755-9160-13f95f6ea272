//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST
//


package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CategoryDefinition complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="CategoryDefinition"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="categoryId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="categoryName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="categoryCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="categoryDescription" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="categoryStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *         &lt;element name="subCategories" type="{http://www.w3schools.com}SubCategoryDefinition" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CategoryDefinition", propOrder = {
    "categoryId",
    "categoryName",
    "categoryCode",
    "categoryDescription",
    "categoryStatus",
    "subCategories"
})
public class CategoryDefinition implements Comparable<CategoryDefinition>{

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer categoryId;
    @XmlElement(required = true)
    protected String categoryName;
    @XmlElement(required = true)
    protected String categoryCode;
    @XmlElement(required = true)
    protected String categoryDescription;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus categoryStatus;
    protected List<SubCategoryDefinition> subCategories;

    /**
     * Gets the value of the categoryId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * Sets the value of the categoryId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setCategoryId(Integer value) {
        this.categoryId = value;
    }

    /**
     * Gets the value of the categoryName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * Sets the value of the categoryName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategoryName(String value) {
        this.categoryName = value;
    }

    /**
     * Gets the value of the categoryCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategoryCode() {
        return categoryCode;
    }

    /**
     * Sets the value of the categoryCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategoryCode(String value) {
        this.categoryCode = value;
    }

    /**
     * Gets the value of the categoryDescription property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategoryDescription() {
        return categoryDescription;
    }

    /**
     * Sets the value of the categoryDescription property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategoryDescription(String value) {
        this.categoryDescription = value;
    }

    /**
     * Gets the value of the categoryStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getCategoryStatus() {
        return categoryStatus;
    }

    /**
     * Sets the value of the categoryStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setCategoryStatus(SwitchStatus value) {
        this.categoryStatus = value;
    }

    /**
     * Gets the value of the subCategories property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the subCategories property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSubCategories().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SubCategoryDefinition }
     *
     *
     */
    public List<SubCategoryDefinition> getSubCategories() {
        if (subCategories == null) {
            subCategories = new ArrayList<SubCategoryDefinition>();
        }
        return this.subCategories;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CategoryDefinition that = (CategoryDefinition) o;

        if (!categoryId.equals(that.categoryId)) return false;
        if (!categoryName.equals(that.categoryName)) return false;
        if (!categoryCode.equals(that.categoryCode)) return false;
        if (!categoryDescription.equals(that.categoryDescription)) return false;
        return categoryStatus == that.categoryStatus;

    }

    @Override
    public int hashCode() {
        int result = categoryId.hashCode();
        result = 31 * result + categoryName.hashCode();
        result = 31 * result + categoryCode.hashCode();
        result = 31 * result + categoryDescription.hashCode();
        result = 31 * result + categoryStatus.hashCode();
        return result;
    }

    @Override
    public int compareTo(CategoryDefinition categoryDefinition) {
        return (categoryDefinition.getCategoryId().compareTo(this.getCategoryId()));
    }
}
