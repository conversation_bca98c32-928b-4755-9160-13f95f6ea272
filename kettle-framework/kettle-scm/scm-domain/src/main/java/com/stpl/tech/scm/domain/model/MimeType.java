//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.20 at 07:12:44 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for MimeType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="MimeType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="PDF"/&gt;
 *     &lt;enumeration value="XLS/XLSX"/&gt;
 *     &lt;enumeration value="IMG"/&gt;
 *     &lt;enumeration value="CSV"/&gt;
 *     &lt;enumeration value="TEXT"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "MimeType")
@XmlEnum
public enum MimeType {

    PDF("application/pdf","pdf"),
    XLSX("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","xls"),
    XLS("application/vnd.ms-excel","xlsx"),
    JPG("image/jpg","jpg"),
    JPEG("image/jpeg","jpeg"),
    PNG("image/png","png"),
    CSV("text/plain","csv"),
    TXT("text/plain", "txt"),
    JSON("application/json","json");
    private final String value;
    private final String extension;

    MimeType(String v, String e) {
        value = v;
        extension = e;
    }

    public String value() {
        return value;
    }

    public String extension() {
        return extension;
    }


    public static MimeType fromValue(String v) {
        for (MimeType c: MimeType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public static String mimeValue(String e){
        for (MimeType c : MimeType.values()) {
            if (c.extension.equalsIgnoreCase(e)) {
                return c.value;
            }
        }
        return null;
    }
}
