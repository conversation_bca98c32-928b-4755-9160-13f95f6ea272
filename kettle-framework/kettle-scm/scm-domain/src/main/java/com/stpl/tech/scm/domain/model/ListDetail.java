package com.stpl.tech.scm.domain.model;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListDetail", propOrder = { "listDetailId", "type", "code", "name", "description", "status", "alias",
		"listType", "accountable" ,"email"})
public class ListDetail {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer listDetailId;

	@XmlElement(required = true)
	protected String type;

	@XmlElement(required = true)
	protected String code;

	@XmlElement(required = true)
	protected String name;

	@XmlElement(required = true)
	protected String description;

	@XmlElement(required = true)
	protected String status;

	@XmlElement(required = true)
	protected String alias;

	@XmlElement(required = true)
	protected String baseType;
	
	@XmlElement(required = true)
    @XmlSchemaType(name = "string")
	protected List<ListType> listType;


	protected boolean accountable;

	protected String email ;

	public Integer getListDetailId() {
		return listDetailId;
	}

	public void setListDetailId(Integer listDetailId) {
		this.listDetailId = listDetailId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public String getBaseType() {
		return baseType;
	}

	public void setBaseType(String baseType) {
		this.baseType = baseType;
	}

	public List<ListType> getListType() {
		return listType;
	}

	public void setListType(List<ListType> listType) {
		this.listType = listType;
	}


	public boolean isAccountable() {
		return accountable;
	}

	public void setAccountable(boolean accountable) {
		this.accountable = accountable;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

}
