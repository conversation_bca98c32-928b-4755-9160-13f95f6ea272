package com.stpl.tech.scm.domain.model;


import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "VendorEditedType")
@XmlEnum
public enum VendorEditedType {
    PERSONAL_DETAIL,
    ACCOUNT_DETAIL,
    COMPANY_DETAIL,
    DISPATCH_LOCATION;
    public String value() {
        return name();
    }
    public static VendorEditedType fromValue(String v) {
        return valueOf(v);
    }
}
