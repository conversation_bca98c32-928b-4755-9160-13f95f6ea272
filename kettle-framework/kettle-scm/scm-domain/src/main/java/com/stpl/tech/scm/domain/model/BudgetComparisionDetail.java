package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class BudgetComparisionDetail {
    protected String departmentName;
    protected BigDecimal lastApprovedOriginalAmount;
    protected BigDecimal currentOriginalAmount;
    protected BigDecimal lastApprovedBudgetAmount;
    protected Integer departmentId;

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public BigDecimal getLastApprovedOriginalAmount() {
        return lastApprovedOriginalAmount;
    }

    public void setLastApprovedOriginalAmount(BigDecimal lastApprovedOriginalAmount) {
        this.lastApprovedOriginalAmount = lastApprovedOriginalAmount;
    }

    public BigDecimal getCurrentOriginalAmount() {
        return currentOriginalAmount;
    }

    public void setCurrentOriginalAmount(BigDecimal currentOriginalAmount) {
        this.currentOriginalAmount = currentOriginalAmount;
    }

    public BigDecimal getLastApprovedBudgetAmount() {
        return lastApprovedBudgetAmount;
    }

    public void setLastApprovedBudgetAmount(BigDecimal lastApprovedBudgetAmount) {
        this.lastApprovedBudgetAmount = lastApprovedBudgetAmount;
    }

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }
}
