/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MilkBreadBypass {
    private Integer milkBreadBypassId;
    private Integer unitId;
    private String bypassReason;
    private String comment;
    private String roIds;
    private String bypassedBy;
    private Date bypassedTime;
}
