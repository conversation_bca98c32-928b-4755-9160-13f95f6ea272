package com.stpl.tech.scm.domain.model;

import java.util.List;

public enum AdvancePaymentStatus {
    INITIATED,
    CREATED,
    CANCELLED,
    REJECTED,
    BLOCKED,
    UN_BLOCKED,
    ADJUST_INITIATED,
    ADJUST_REJECTED,
    ADJUST_CANCELLED,
    ADJUSTED,
    REFUND_INITIATED,
    REFUND_APPROVED,
    REFUNDED,
    COMPLETED;

    public String value() {
        return name();
    }

    public static AdvancePaymentStatus fromValue(String v) {
        return valueOf(v);
    }

    public static List<String> getAllStatus() {
        List<String> result = new java.util.ArrayList<String>();
        for (AdvancePaymentStatus c: AdvancePaymentStatus.values()) {
            result.add(c.value());
        }
        return result;
    }
}
