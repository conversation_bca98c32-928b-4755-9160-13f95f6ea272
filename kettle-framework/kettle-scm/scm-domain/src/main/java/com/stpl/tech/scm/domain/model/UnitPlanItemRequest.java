package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class UnitPlanItemRequest {
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer unitId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer roId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer roItemId;
    protected Integer roProductId;
    protected BigDecimal requestedQuantity;
    protected BigDecimal requestedAbsoluteQuantity;
    protected BigDecimal totalQuantity;
    protected BigDecimal averageSales;
    protected String unitName;
    protected BigDecimal adjustedQuantity;
    protected String adjustedReason;
    protected String productionBookingCompleted;
    protected Boolean isTransferred;
    protected String status;
    protected List<MultiPackagingAdjustments> multiPackagingAdjustments;
    protected String uom;


    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getRoId() {
        return roId;
    }

    public void setRoId(Integer roId) {
        this.roId = roId;
    }

    public Integer getRoItemId() {
        return roItemId;
    }

    public void setRoItemId(Integer roItemId) {
        this.roItemId = roItemId;
    }

    public Integer getRoProductId() {
        return roProductId;
    }

    public void setRoProductId(Integer roProductId) {
        this.roProductId = roProductId;
    }

    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    public void setRequestedQuantity(BigDecimal requestedQuantity) {
        this.requestedQuantity = requestedQuantity;
    }

    public BigDecimal getRequestedAbsoluteQuantity() {
        return requestedAbsoluteQuantity;
    }

    public void setRequestedAbsoluteQuantity(BigDecimal requestedAbsoluteQuantity) {
        this.requestedAbsoluteQuantity = requestedAbsoluteQuantity;
    }

    public BigDecimal getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(BigDecimal totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public BigDecimal getAverageSales() {
        return averageSales;
    }

    public void setAverageSales(BigDecimal averageSales) {
        this.averageSales = averageSales;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getAdjustedQuantity() {
        return adjustedQuantity;
    }

    public void setAdjustedQuantity(BigDecimal adjustedQuantity) {
        this.adjustedQuantity = adjustedQuantity;
    }

    public String getAdjustedReason() {
        return adjustedReason;
    }

    public void setAdjustedReason(String adjustedReason) {
        this.adjustedReason = adjustedReason;
    }

    public String getProductionBookingCompleted() {
        return productionBookingCompleted;
    }

    public void setProductionBookingCompleted(String productionBookingCompleted) {
        this.productionBookingCompleted = productionBookingCompleted;
    }

    public Boolean getTransferred() {
        return isTransferred;
    }

    public void setTransferred(Boolean transferred) {
        isTransferred = transferred;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<MultiPackagingAdjustments> getMultiPackagingAdjustments() {
        if (Objects.isNull(multiPackagingAdjustments)) {
            return new ArrayList<>();
        }
        return multiPackagingAdjustments;
    }

    public void setMultiPackagingAdjustments(List<MultiPackagingAdjustments> multiPackagingAdjustments) {
        this.multiPackagingAdjustments = multiPackagingAdjustments;
    }

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }
}
