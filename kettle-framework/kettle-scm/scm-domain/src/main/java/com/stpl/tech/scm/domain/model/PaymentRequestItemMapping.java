//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.09.22 at 02:16:00 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PaymentRequestItemMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PaymentRequestItemMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="paymentRequestId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="paymentRequestType" type="{http://www.w3schools.com}PaymentRequestType"/&gt;
 *         &lt;element name="paymentRequestItemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="linkedPaymentRequestId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PaymentRequestItemMapping", propOrder = {
    "id",
    "paymentRequestId",
    "paymentRequestType",
    "paymentRequestItemId",
    "linkedPaymentRequestId",
    "status"
})
public class PaymentRequestItemMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    protected int paymentRequestId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentRequestType paymentRequestType;
    protected int paymentRequestItemId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer linkedPaymentRequestId;
    @XmlElement(required = true, nillable = true)
    protected String status;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the paymentRequestId property.
     *
     */
    public int getPaymentRequestId() {
        return paymentRequestId;
    }

    /**
     * Sets the value of the paymentRequestId property.
     *
     */
    public void setPaymentRequestId(int value) {
        this.paymentRequestId = value;
    }

    /**
     * Gets the value of the paymentRequestType property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentRequestType }
     *     
     */
    public PaymentRequestType getPaymentRequestType() {
        return paymentRequestType;
    }

    /**
     * Sets the value of the paymentRequestType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentRequestType }
     *     
     */
    public void setPaymentRequestType(PaymentRequestType value) {
        this.paymentRequestType = value;
    }

    /**
     * Gets the value of the paymentRequestItemId property.
     *
     */
    public int getPaymentRequestItemId() {
        return paymentRequestItemId;
    }

    /**
     * Sets the value of the paymentRequestItemId property.
     *
     */
    public void setPaymentRequestItemId(int value) {
        this.paymentRequestItemId = value;
    }

    /**
     * Gets the value of the linkedPaymentRequestId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getLinkedPaymentRequestId() {
        return linkedPaymentRequestId;
    }

    /**
     * Sets the value of the linkedPaymentRequestId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setLinkedPaymentRequestId(Integer value) {
        this.linkedPaymentRequestId = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setStatus(String value) {
        this.status = value;
    }

}
