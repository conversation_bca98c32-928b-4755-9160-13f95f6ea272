//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.20 at 05:17:20 PM IST
//


package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;


/**
 * <p>Java class for TransferOrder complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="TransferOrder"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="initiationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="generationUnitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedForUnitId" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="lastUpdatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}SCMOrderStatus"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="purchaseOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="goodsReceivedId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="requestOrderTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="transferOrderItems" type="{http://www.w3schools.com}TransferOrderItem" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransferOrder", propOrder = {
    "id",
    "generationTime",
    "initiationTime",
    "lastUpdateTime",
    "generationUnitId",
    "generatedForUnitId",
    "generatedBy",
    "lastUpdatedBy",
    "status",
    "comment",
    "totalAmount",
    "requestOrderId",
    "purchaseOrderId",
    "goodsReceivedId",
    "requestOrderTime",
    "transferOrderItems",
        "toType",
        "bulkTransferEventId"
})
public class TransferOrder extends AbstractInventoryVO implements ConsumptionVO, ReceivingVO{

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date initiationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(required = true)
    protected IdCodeName generationUnitId;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName generatedForUnitId;
    @XmlElement(required = true)
    protected IdCodeName sourceCompany;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName receivingCompany;
    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName lastUpdatedBy;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SCMOrderStatus status;
    @XmlElement(required = true)
    protected String comment;
    protected float totalAmount;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer requestOrderId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer purchaseOrderId;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer goodsReceivedId;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date requestOrderTime;
    protected List<TransferOrderItem> transferOrderItems;
	protected BigDecimal tax;
	protected String invoiceId;
	protected String type;
	protected List<TaxDetail> taxes;
	protected State fromState;
	protected State toState;
    @XmlElement(required = true, nillable = true)
	protected PriceUpdateEntryType inventoryType;
    protected boolean external;
    protected int vendorId;
    protected int dispatchId;
    protected ExternalTransferDetail externalTransferDetail;
    protected boolean ewayApplicable;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected TransferOrderType toType;
    @XmlElement(required = false, type = Double.class, nillable = true)
    protected Double totalCost;
    protected String budgetType;
    @XmlElement(nillable = true)
    protected Integer bulkTransferEventId;
    protected Boolean eInvoiceGenerated;
    private String docIdsPorImages;
    /**
     * Gets the value of the id property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the generationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getGenerationTime() {
        return generationTime;
    }

    /**
     * Sets the value of the generationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setGenerationTime(Date value) {
        this.generationTime = value;
    }

    /**
     * Gets the value of the initiationTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getInitiationTime() {
        return initiationTime;
    }

    /**
     * Sets the value of the initiationTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setInitiationTime(Date value) {
        this.initiationTime = value;
    }

    /**
     * Gets the value of the lastUpdateTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * Sets the value of the lastUpdateTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setLastUpdateTime(Date value) {
        this.lastUpdateTime = value;
    }

    /**
     * Gets the value of the generationUnitId property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGenerationUnitId() {
        return generationUnitId;
    }

    /**
     * Sets the value of the generationUnitId property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGenerationUnitId(IdCodeName value) {
        this.generationUnitId = value;
    }

    /**
     * Gets the value of the generatedForUnitId property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGeneratedForUnitId() {
        return generatedForUnitId;
    }

    /**
     * Sets the value of the generatedForUnitId property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedForUnitId(IdCodeName value) {
        this.generatedForUnitId = value;
    }

    /**
     * Gets the value of the generatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    /**
     * Sets the value of the generatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setGeneratedBy(IdCodeName value) {
        this.generatedBy = value;
    }

    /**
     * Gets the value of the lastUpdatedBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    /**
     * Sets the value of the lastUpdatedBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setLastUpdatedBy(IdCodeName value) {
        this.lastUpdatedBy = value;
    }

    /**
     * Gets the value of the status property.
     *
     * @return
     *     possible object is
     *     {@link SCMOrderStatus }
     *
     */
    public SCMOrderStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     * @param value
     *     allowed object is
     *     {@link SCMOrderStatus }
     *
     */
    public void setStatus(SCMOrderStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the comment property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getComment() {
        return comment;
    }

    /**
     * Sets the value of the comment property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setComment(String value) {
        this.comment = value;
    }

    /**
     * Gets the value of the totalAmount property.
     *
     */
    public float getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     *
     */
    public void setTotalAmount(float value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the requestOrderId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getRequestOrderId() {
        return requestOrderId;
    }

    /**
     * Sets the value of the requestOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setRequestOrderId(Integer value) {
        this.requestOrderId = value;
    }

    /**
     * Gets the value of the purchaseOrderId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getPurchaseOrderId() {
        return purchaseOrderId;
    }

    /**
     * Sets the value of the purchaseOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setPurchaseOrderId(Integer value) {
        this.purchaseOrderId = value;
    }

    /**
     * Gets the value of the goodsReceivedId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getGoodsReceivedId() {
        return goodsReceivedId;
    }

    /**
     * Sets the value of the goodsReceivedId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setGoodsReceivedId(Integer value) {
        this.goodsReceivedId = value;
    }

    /**
     * Gets the value of the requestOrderTime property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public Date getRequestOrderTime() {
        return requestOrderTime;
    }

    /**
     * Sets the value of the requestOrderTime property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setRequestOrderTime(Date value) {
        this.requestOrderTime = value;
    }

    /**
     * Gets the value of the transferOrderItems property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the transferOrderItems property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTransferOrderItems().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link TransferOrderItem }
     *
     *
     */
    public List<TransferOrderItem> getTransferOrderItems() {
        if (transferOrderItems == null) {
            transferOrderItems = new ArrayList<TransferOrderItem>();
        }
        return this.transferOrderItems;
    }

	/**
	 * @return the tax
	 */
	public BigDecimal getTax() {
		return tax;
	}

	/**
	 * @param tax the tax to set
	 */
	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	/**
	 * @return the taxes
	 */
	public List<TaxDetail> getTaxes() {
		if(taxes == null){
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	/**
	 * @return the invoiceId
	 */
	public String getInvoiceId() {
		return invoiceId;
	}

	/**
	 * @param invoiceId the invoiceId to set
	 */
	public void setInvoiceId(String invoiceId) {
		this.invoiceId = invoiceId;
	}

	/**
	 * @return the fromState
	 */
	public State getFromState() {
		return fromState;
	}

	/**
	 * @param fromState the fromState to set
	 */
	public void setFromState(State fromState) {
		this.fromState = fromState;
	}

	/**
	 * @return the toState
	 */
	public State getToState() {
		return toState;
	}

	/**
	 * @param toState the toState to set
	 */
	public void setToState(State toState) {
		this.toState = toState;
	}

	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * @param type the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getKeyId()
	 */
	@Override
    @JsonIgnore
	public int getKeyId() {
		return this.id;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getKeyType()
	 */
	@Override
    @JsonIgnore
	public StockEventType getKeyType() {
		return StockEventType.TRANSFER_OUT;
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getUnitId()
	 */
	@Override
    @JsonIgnore
	public int getUnitId() {
		return this.getGenerationUnitId().getId();
	}

	/* (non-Javadoc)
	 * @see com.stpl.tech.scm.domain.vo.InventoryVO#getInventoryType()
	 */
	@Override
	public PriceUpdateEntryType getInventoryType() {
		return this.inventoryType;
	}


	public void setInventoryType(PriceUpdateEntryType inventoryType) {
		this.inventoryType = inventoryType;
	}

	@Override
    @JsonIgnore
	public List<InventoryItemVO> getInventoryItems() {
		return new ArrayList<>(this.transferOrderItems);
	}


    public boolean isExternal() {
        return external;
    }

    public void setExternal(boolean external) {
        this.external = external;
    }

    public int getVendorId() {
        return vendorId;
    }

    public void setVendorId(int vendorId) {
        this.vendorId = vendorId;
    }

    public int getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(int dispatchId) {
        this.dispatchId = dispatchId;
    }

    public ExternalTransferDetail getExternalTransferDetail() {
        return externalTransferDetail;
    }

    public void setExternalTransferDetail(ExternalTransferDetail externalTransferDetail) {
        this.externalTransferDetail = externalTransferDetail;
    }

	public IdCodeName getSourceCompany() {
		return sourceCompany;
	}

	public void setSourceCompany(IdCodeName sourceCompany) {
		this.sourceCompany = sourceCompany;
	}

	public IdCodeName getReceivingCompany() {
		return receivingCompany;
	}

	public void setReceivingCompany(IdCodeName receivingCompany) {
		this.receivingCompany = receivingCompany;
	}

	public boolean isEwayApplicable() {
		return ewayApplicable;
	}

	public void setEwayApplicable(boolean ewayApplicable) {
		this.ewayApplicable = ewayApplicable;
	}

    public TransferOrderType getToType() {
        return toType;
    }

    public void setToType(TransferOrderType toType) {
        this.toType = toType;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public String getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(String budgetType) {
        this.budgetType = budgetType;
    }

    @Override
	public boolean isAssetOrder() {
		return toType.isAsset();
	}

    public void setTransferOrderItems(List<TransferOrderItem> transferOrderItems) {
        this.transferOrderItems = transferOrderItems;
    }

    public Integer getBulkTransferEventId() {
        return bulkTransferEventId;
    }

    public void setBulkTransferEventId(Integer bulkTransferEventId) {
        this.bulkTransferEventId = bulkTransferEventId;
    }


    public Boolean geteInvoiceGenerated() {
        return eInvoiceGenerated;
    }

    public void seteInvoiceGenerated(Boolean eInvoiceGenerated) {
        this.eInvoiceGenerated = eInvoiceGenerated;
    }

    public String getDocIdsPorImages() {
        return docIdsPorImages;
    }

    public void setDocIdsPorImages(String docIdsPorImages) {
        this.docIdsPorImages = docIdsPorImages;
    }

}
