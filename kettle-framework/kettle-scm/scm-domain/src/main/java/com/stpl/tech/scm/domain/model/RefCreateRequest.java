package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class RefCreateRequest {

    private String start_date;
    private String end_date;
    private RefCreateRequestFilters filters;
    private String metric;
    private String time_granularity;
    private Boolean calculate_safety_stock;
    private String level;
    private String max_capping_safety_stock_multiplier;
    private List<String> dimensions_to_be_aggregated_on;
    private List<String> columns;
    private Boolean bom_only;
    private Integer number_of_days;

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public RefCreateRequestFilters getFilters() {
        return filters;
    }

    public void setFilters(RefCreateRequestFilters filters) {
        this.filters = filters;
    }

    public String getMetric() {
        return metric;
    }

    public void setMetric(String metric) {
        this.metric = metric;
    }

    public String getTime_granularity() {
        return time_granularity;
    }

    public void setTime_granularity(String time_granularity) {
        this.time_granularity = time_granularity;
    }

    public Boolean getCalculate_safety_stock() {
        return calculate_safety_stock;
    }

    public void setCalculate_safety_stock(Boolean calculate_safety_stock) {
        this.calculate_safety_stock = calculate_safety_stock;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public boolean isCalculate_safety_stock() {
        return calculate_safety_stock;
    }

    public String getMax_capping_safety_stock_multiplier() {
        return max_capping_safety_stock_multiplier;
    }

    public void setMax_capping_safety_stock_multiplier(String max_capping_safety_stock_multiplier) {
        this.max_capping_safety_stock_multiplier = max_capping_safety_stock_multiplier;
    }

    public List<String> getDimensions_to_be_aggregated_on() {
        return dimensions_to_be_aggregated_on;
    }

    public void setDimensions_to_be_aggregated_on(List<String> dimensions_to_be_aggregated_on) {
        this.dimensions_to_be_aggregated_on = dimensions_to_be_aggregated_on;
    }

    public List<String> getColumns() {
        return columns;
    }

    public void setColumns(List<String> columns) {
        this.columns = columns;
    }

    public Boolean getBom_only() {
        return bom_only;
    }

    public void setBom_only(Boolean bom_only) {
        this.bom_only = bom_only;
    }

    public Integer getNumber_of_days() {
        return number_of_days;
    }

    public void setNumber_of_days(Integer number_of_days) {
        this.number_of_days = number_of_days;
    }
}
