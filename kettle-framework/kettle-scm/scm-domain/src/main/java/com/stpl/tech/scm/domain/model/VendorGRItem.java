//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.04 at 10:58:30 PM IST 
//

package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import org.apache.poi.ss.formula.eval.NotImplementedException;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;

/**
 * <p>
 * Java class for VendorGRItem complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="VendorGRItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="hsn" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="requestedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="receivedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="totalCost" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="amountPaid" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="packagingId" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
 *         &lt;element name="packagingName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="conversionRatio" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="exemptItem" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="packagingQuantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="totalTax" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="taxes" type="{http://www.w3schools.com}TaxDetail" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VendorGRItem", propOrder = { "id", "skuId", "skuName", "hsn", "receivedQuantity", "unitOfMeasure",
		"unitPrice", "totalCost", "amountPaid", "packagingId", "packagingName", "conversionRatio", "exemptItem",
		"packagingQuantity", "totalTax", "taxes" })
public class VendorGRItem implements InventoryItemVO {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	protected int skuId;
	@XmlElement(required = true)
	protected String skuName;
	protected String subCategory;
	protected String category;
	@XmlElement(required = true)
	protected String hsn;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected BigDecimal receivedQuantity;
	@XmlElement(required = true)
	protected String unitOfMeasure;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal unitPrice;
	@JsonIgnore
	@XmlElement(required = true, nillable = true)
	protected BigDecimal price;
	@JsonIgnore
	@XmlElement(required = true, nillable = true)
	protected BigDecimal quantity;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal totalCost;
	@XmlElement(required = true, nillable = true)
	protected BigDecimal amountPaid;
	@XmlElement(required = true)
	protected Integer packagingId;
	@XmlElement(required = true)
	protected String packagingName;
	@XmlElement(required = true)
	protected BigDecimal conversionRatio;
	protected boolean exemptItem;
	protected BigDecimal packagingQty;
	@XmlElement(required = true)
	protected BigDecimal totalTax;
	protected List<OtherTaxDetail> taxes;
	protected Map<Integer, BigDecimal> usedPOItems;
	protected List<InventoryItemDrilldown> drillDowns;
	@XmlElement(required = false, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date expiryDate;

	/**
	 * Gets the value of the id property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 * @param value
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setId(Integer value) {
		this.id = value;
	}

	/**
	 * Gets the value of the skuId property.
	 * 
	 */
	public int getSkuId() {
		return skuId;
	}

	/**
	 * Sets the value of the skuId property.
	 * 
	 */
	public void setSkuId(int value) {
		this.skuId = value;
	}

	/**
	 * Gets the value of the skuName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSkuName() {
		return skuName;
	}

	/**
	 * Sets the value of the skuName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSkuName(String value) {
		this.skuName = value;
	}

	/**
	 * Gets the value of the hsn property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getHsn() {
		return hsn;
	}

	/**
	 * Sets the value of the hsn property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setHsn(String value) {
		this.hsn = value;
	}

	/**
	 * Gets the value of the receivedQuantity property.
	 * 
	 * @return possible object is {@link Float }
	 * 
	 */
	public BigDecimal getReceivedQuantity() {
		return receivedQuantity;
	}

	/**
	 * Sets the value of the receivedQuantity property.
	 * 
	 * @param value
	 *            allowed object is {@link Float }
	 * 
	 */
	public void setReceivedQuantity(BigDecimal value) {
		this.receivedQuantity = value;
	}

	/**
	 * Gets the value of the unitOfMeasure property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	/**
	 * Sets the value of the unitOfMeasure property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUnitOfMeasure(String value) {
		this.unitOfMeasure = value;
	}

	/**
	 * Gets the value of the unitPrice property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	/**
	 * Sets the value of the unitPrice property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUnitPrice(BigDecimal value) {
		this.unitPrice = value;
	}

	/**
	 * Gets the value of the totalCost property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	/**
	 * Sets the value of the totalCost property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTotalCost(BigDecimal value) {
		this.totalCost = value;
	}

	/**
	 * Gets the value of the amountPaid property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getAmountPaid() {
		return amountPaid;
	}

	/**
	 * Sets the value of the amountPaid property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAmountPaid(BigDecimal value) {
		this.amountPaid = value;
	}

	/**
	 * Gets the value of the packagingId property.
	 * 
	 * @return possible object is {@link BigInteger }
	 * 
	 */
	public Integer getPackagingId() {
		return packagingId;
	}

	/**
	 * Sets the value of the packagingId property.
	 * 
	 * @param value
	 *            allowed object is {@link BigInteger }
	 * 
	 */
	public void setPackagingId(Integer value) {
		this.packagingId = value;
	}

	/**
	 * Gets the value of the packagingName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPackagingName() {
		return packagingName;
	}

	/**
	 * Sets the value of the packagingName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPackagingName(String value) {
		this.packagingName = value;
	}

	/**
	 * Gets the value of the conversionRatio property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getConversionRatio() {
		return conversionRatio;
	}

	/**
	 * Sets the value of the conversionRatio property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setConversionRatio(BigDecimal value) {
		this.conversionRatio = value;
	}

	/**
	 * Gets the value of the exemptItem property.
	 * 
	 */
	public boolean isExemptItem() {
		return exemptItem;
	}

	/**
	 * Sets the value of the exemptItem property.
	 * 
	 */
	public void setExemptItem(boolean value) {
		this.exemptItem = value;
	}

	/**
	 * Gets the value of the packagingQuantity property.
	 * 
	 */
	public BigDecimal getPackagingQty() {
		return packagingQty;
	}

	/**
	 * Sets the value of the packagingQuantity property.
	 * 
	 */
	public void setPackagingQty(BigDecimal value) {
		this.packagingQty = value;
	}

	/**
	 * Gets the value of the totalTax property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getTotalTax() {
		return totalTax;
	}

	/**
	 * Sets the value of the totalTax property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setTotalTax(BigDecimal value) {
		this.totalTax = value;
	}

	/**
	 * Gets the value of the taxes property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the taxes property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getTaxes().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link TaxDetail
	 * }
	 * 
	 * 
	 */
	public List<OtherTaxDetail> getTaxes() {
		if (this.taxes == null) {
			this.taxes = new ArrayList<>();
		}
		return this.taxes;
	}

	public Map<Integer, BigDecimal> getUsedPOItems() {
		if (this.usedPOItems == null) {
			this.usedPOItems = new HashMap<>();
		}
		return this.usedPOItems;
	}

	public void setUsedPOItems(Map<Integer, BigDecimal> usedPOItems) {
		this.usedPOItems = usedPOItems;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyId()
	 */
	@Override
	@JsonIgnore
	public int getKeyId() {
		return this.skuId;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyType()
	 */
	@Override
	@JsonIgnore
	public PriceUpdateEntryType getKeyType() {
		return PriceUpdateEntryType.SKU;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyId()
	 */
	@Override
	@JsonIgnore
	public int getItemKeyId() {
		return this.id;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyType()
	 */
	@Override
	@JsonIgnore
	public StockEventType getItemKeyType() {
		return StockEventType.VENDOR_RECEIVED;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getDrillDowns()
	 */
	@Override
	@JsonIgnore
	public List<InventoryItemDrilldown> getDrillDowns() {
		if (drillDowns == null) {
			drillDowns = new ArrayList<>();
		}
		return drillDowns;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getQuantity()
	 */
	@Override
	@JsonIgnore
	public BigDecimal getQuantity() {
		if (this.quantity == null) {
			this.quantity = new BigDecimal(this.receivedQuantity.doubleValue());
		}
		return this.quantity;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getPrice()
	 */
	@Override
	@JsonIgnore
	public BigDecimal getPrice() {
		if (this.price == null) {
			this.price = this.unitPrice.divide(this.conversionRatio, 10, BigDecimal.ROUND_HALF_UP);
		}
		return this.price;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#setPrice(java.math.
	 * BigDecimal)
	 */
	@Override
	@JsonIgnore
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#setQuantity(java.math.
	 * BigDecimal)
	 */
	@Override
	@JsonIgnore
	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getUom()
	 */
	@Override
	@JsonIgnore
	public String getUom() {
		return this.unitOfMeasure;
	}
	
	@Override
	public void setKeyType(PriceUpdateEntryType type) {
		throw new NotImplementedException("This should not be called for VendorGR");
    }

    public String getSubCategory() {
        return subCategory;
    }

    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public void setTaxes(List<OtherTaxDetail> taxes) {
		this.taxes = taxes;
	}

	@Override
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	
}
