package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

@XmlType(name = "VendorEditedFieldType")
@XmlEnum
public enum VendorEditedFieldType {
    ENTITY_NAME,
    FIRST_NAME,
    LAST_NAME,
    PRIMARY_CONTACT,
    SECONDARY_CONTACT,
    PRIMARY_EMAIL,
    SECONDARY_EMAIL,
    MSME_REGISTERED,
    CIN,
    CIN_DOCUMENT,
    COMPANY_NAME,
    PAN,
    MSME_DOCUMENT,
    VAT_DOCUMENT,
    PAN_DOCUMENT,
    REGISTERED_NAME,
    COMPANRY_ADDRESS,
    ACCOUNT_CONTACT_NAME,
    ACCOUNT_CONTACT_EMAIL,
    ACCOUNT_NUMBAR,
    IFSC_CODE,
    ACCOUNT_KIND,
    CONTACT_NUMBER,
    CANCELED_CHEQUE,
    <PERSON><PERSON><PERSON>,
    G<PERSON>IN_DOCUMENT,
    TIN,
    NOTIFICATION,
    <PERSON>IS<PERSON><PERSON>H_ADDRESS,
    APPLY_TAX,
    MICRE_CODE,
    ACCOUNT_TYPE,
    CST,
    CST_DOCUMENT,
    ENTITY_TYPE,
    BUSINESS_TYPE,
    COMPANY_ADDRESS,
    COMPANY_TYPE,
    ARC,
    ARC_DOCUMENT,
    EMAIL_ID,
    LOCATION_NAME,
    GST_STATUS,
    VENDOR_ADDRESS;
    public String value() {
        return name();
    }
    public static VendorEditedFieldType fromValue(String v) {
        return valueOf(v);
    }
}
