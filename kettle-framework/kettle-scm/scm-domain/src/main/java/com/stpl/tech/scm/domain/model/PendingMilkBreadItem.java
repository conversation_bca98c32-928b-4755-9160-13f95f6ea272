/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PendingMilkBreadItem {

    private Integer requestUnitId;
    private List<String> vendorIds;
    private List<Integer> roIds;
}
