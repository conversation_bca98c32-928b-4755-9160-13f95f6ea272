package com.stpl.tech.scm.domain.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CreditDebitNoteItemDetail {

    protected Integer itemId;
    protected Integer referenceId;
    protected String itemDesc;
    protected BigDecimal qty;
    protected BigDecimal price;
    protected BigDecimal netAmount;
    protected BigDecimal taxPercent;
    protected BigDecimal taxAmount;
    protected BigDecimal totalAmount;
}
