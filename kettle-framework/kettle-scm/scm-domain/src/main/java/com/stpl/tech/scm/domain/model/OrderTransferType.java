package com.stpl.tech.scm.domain.model;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 20-08-2018.
 */


public enum OrderTransferType {

    TRANSFER,
    INVOICE;

    public String value() {
        return name();
    }

    public static OrderTransferType fromValue(String v) {
        return valueOf(v);
    }

}