//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for UnitProductMapping complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitProductMapping"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="unitProductMappingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendorId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="mappingStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitProductMapping", propOrder = {
    "unitProductMappingId",
    "unitId",
    "productId",
    "vendorId",
    "mappingStatus"
})
public class UnitProductMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer unitProductMappingId;
    protected int unitId;
    protected int productId;
    protected int vendorId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus mappingStatus;

    /**
     * Gets the value of the unitProductMappingId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getUnitProductMappingId() {
        return unitProductMappingId;
    }

    /**
     * Sets the value of the unitProductMappingId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setUnitProductMappingId(Integer value) {
        this.unitProductMappingId = value;
    }

    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the productId property.
     * 
     */
    public int getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     * 
     */
    public void setProductId(int value) {
        this.productId = value;
    }

    /**
     * Gets the value of the vendorId property.
     * 
     */
    public int getVendorId() {
        return vendorId;
    }

    /**
     * Sets the value of the vendorId property.
     * 
     */
    public void setVendorId(int value) {
        this.vendorId = value;
    }

    /**
     * Gets the value of the mappingStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getMappingStatus() {
        return mappingStatus;
    }

    /**
     * Sets the value of the mappingStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setMappingStatus(SwitchStatus value) {
        this.mappingStatus = value;
    }

}
