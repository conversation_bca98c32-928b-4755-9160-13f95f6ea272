package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlElement;

public class CostCenter {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	@XmlElement(required = true, nillable = true)
	protected String code;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true)
	protected String description;
	@XmlElement(required = true)
	protected String costCenterEmail;
	@XmlElement(required = true)
	protected IdCodeName owner;
	protected List<CostElement> elements = new ArrayList<CostElement>();

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public List<CostElement> getElements() {
		return elements;
	}

	public void setElements(List<CostElement> elements) {
		this.elements = elements;
	}

	public IdCodeName getOwner() {
		return owner;
	}

	public void setOwner(IdCodeName owner) {
		this.owner = owner;
	}

	public String getCostCenterEmail() {
		return costCenterEmail;
	}

	public void setCostCenterEmail(String costCenterEmail) {
		this.costCenterEmail = costCenterEmail;
	}
	
	

}
