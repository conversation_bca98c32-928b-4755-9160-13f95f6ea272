package com.stpl.tech.scm.domain.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "EwayResponse")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class EWayResponse {

	@ExcelField
	private String slNo;
	@ExcelField
	private String supplyType;
	@ExcelField
	private String docNo;
	@ExcelField
	private String docDate;
	@ExcelField
	private String otherPartyGstin;
	@ExcelField
	private String supplyState;
	@ExcelField
	private String vehicleNo;
	@ExcelField
	private String noOfItems;
	@ExcelField
	private String ewbNo;
	@ExcelField
	private String ewdDate;
	@ExcelField
	private String validTillDate;
	@ExcelField
	private String ewbStatus;
	@ExcelField
	private String errors;
	private boolean error;
	private int toId;

	public EWayResponse() {
		// TODO Auto-generated constructor stub
	}

	public EWayResponse(String docNo, String errors, boolean error) {
		this.docNo = docNo;
		this.errors = errors;
		this.error = error;
	}

	public String getSlNo() {
		return slNo;
	}

	public void setSlNo(String slNo) {
		this.slNo = slNo;
	}

	public String getSupplyType() {
		return supplyType;
	}

	public void setSupplyType(String supplyType) {
		this.supplyType = supplyType;
	}

	public String getDocNo() {
		return docNo;
	}

	public void setDocNo(String docNo) {
		this.docNo = docNo;
	}

	public String getDocDate() {
		return docDate;
	}

	public void setDocDate(String docDate) {
		this.docDate = docDate;
	}

	public String getOtherPartyGstin() {
		return otherPartyGstin;
	}

	public void setOtherPartyGstin(String otherPartyGstin) {
		this.otherPartyGstin = otherPartyGstin;
	}

	public String getSupplyState() {
		return supplyState;
	}

	public void setSupplyState(String supplyState) {
		this.supplyState = supplyState;
	}

	public String getVehicleNo() {
		return vehicleNo;
	}

	public void setVehicleNo(String vehicleNo) {
		this.vehicleNo = vehicleNo;
	}

	public String getNoOfItems() {
		return noOfItems;
	}

	public void setNoOfItems(String noOfItems) {
		this.noOfItems = noOfItems;
	}

	public String getEwbNo() {
		return ewbNo;
	}

	public void setEwbNo(String ewbNo) {
		this.ewbNo = ewbNo;
	}

	public String getEwdDate() {
		return ewdDate;
	}

	public void setEwdDate(String ewdDate) {
		this.ewdDate = ewdDate;
	}

	public String getValidTillDate() {
		return validTillDate;
	}

	public void setValidTillDate(String validTillDate) {
		this.validTillDate = validTillDate;
	}

	public String getEwbStatus() {
		return ewbStatus;
	}

	public void setEwbStatus(String ewbStatus) {
		this.ewbStatus = ewbStatus;
	}

	public String getErrors() {
		return errors;
	}

	public void setErrors(String errors) {
		this.errors = errors;
	}

	public boolean isError() {
		return error;
	}

	public boolean getError() {
		return error;
	}

	public void setError(boolean error) {
		this.error = error;
	}

	public int getToId() {
		return toId;
	}

	public void setToId(int toId) {
		this.toId = toId;
	}

	
}
