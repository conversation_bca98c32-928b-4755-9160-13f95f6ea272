package com.stpl.tech.scm.domain.model;

import java.util.Date;

public class RegularOrderEvent {

    private Integer eventId;
    private Integer unitId;
    private String brand;
    private Date fulfilmentDate;
    private Integer orderingDays;
    private String status;

    public RegularOrderEvent() {
    }

    public RegularOrderEvent(Integer eventId, Integer unitId, String brand, Date fulfilmentDate, Integer orderingDays, String status) {
        this.eventId = eventId;
        this.unitId = unitId;
        this.brand = brand;
        this.fulfilmentDate = fulfilmentDate;
        this.orderingDays = orderingDays;
        this.status = status;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public Date getFulfilmentDate() {
        return fulfilmentDate;
    }

    public void setFulfilmentDate(Date fulfilmentDate) {
        this.fulfilmentDate = fulfilmentDate;
    }

    public Integer getOrderingDays() {
        return orderingDays;
    }

    public void setOrderingDays(Integer orderingDays) {
        this.orderingDays = orderingDays;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
