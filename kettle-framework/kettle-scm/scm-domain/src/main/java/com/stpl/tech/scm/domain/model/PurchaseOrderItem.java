//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.20 at 05:17:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.domain.model.Pair;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for PurchaseOrderItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PurchaseOrderItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="skuName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="requestedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestedAbsoluteQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="transferredQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="receivedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="negotiatedUnitPrice" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="totalCost" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="amountPaid" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PurchaseOrderItem", propOrder = {
        "id",
        "skuId",
        "hsn",
        "skuName",
        "requestedQuantity",
        "requestedAbsoluteQuantity",
        "transferredQuantity",
        "receivedQuantity",
        "unitOfMeasure",
        "unitPrice",
        "negotiatedUnitPrice",
        "totalCost",
        "amountPaid",
        "packagingId",
        "packagingName",
        "packagingQuantity",
        "conversionRatio",
        "exemptItem",
        "igst",
        "cgst",
        "sgst",
        "otherTaxes"
})
public class PurchaseOrderItem {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    protected int skuId;
    @XmlElement(required = true)
    protected String hsn;
    @XmlElement(required = true)
    protected String skuName;
    protected BigDecimal requestedQuantity;
    protected BigDecimal requestedAbsoluteQuantity;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected BigDecimal transferredQuantity;
    @XmlElement(required = true, type = Float.class, nillable = true)
    protected BigDecimal receivedQuantity;
    @XmlElement(required = true)
    protected String unitOfMeasure;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal unitPrice;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal negotiatedUnitPrice;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal totalCost;
    @XmlElement(required = true, nillable = true)
    protected BigDecimal amountPaid;
    protected BigDecimal totalTax;
    protected int packagingId;
    protected String packagingName;
    protected BigDecimal packagingQty;
    protected BigDecimal conversionRatio;
    protected boolean exemptItem;
    protected PercentageDetail igst;
    protected PercentageDetail cgst;
    protected PercentageDetail sgst;
    protected List<OtherTaxDetail> otherTaxes;
    protected Integer leadTime;
    protected String type;
    protected BigDecimal otherTax;
    protected List<Pair<BigDecimal, String>> priceHistory;

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the skuId property.
     * 
     */
    public int getSkuId() {
        return skuId;
    }

    /**
     * Sets the value of the skuId property.
     * 
     */
    public void setSkuId(int value) {
        this.skuId = value;
    }


    public String getHsn() {
        return hsn;
    }

    public void setHsn(String hsn) {
        this.hsn = hsn;
    }

    /**
     * Gets the value of the skuName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     * Sets the value of the skuName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkuName(String value) {
        this.skuName = value;
    }

    /**
     * Gets the value of the requestedQuantity property.
     * 
     */
    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    /**
     * Sets the value of the requestedQuantity property.
     * 
     */
    public void setRequestedQuantity(BigDecimal value) {
        this.requestedQuantity = value;
    }

    /**
     * Gets the value of the requestedAbsoluteQuantity property.
     * 
     */
    public BigDecimal getRequestedAbsoluteQuantity() {
        return requestedAbsoluteQuantity;
    }

    /**
     * Sets the value of the requestedAbsoluteQuantity property.
     * 
     */
    public void setRequestedAbsoluteQuantity(BigDecimal value) {
        this.requestedAbsoluteQuantity = value;
    }

    /**
     * Gets the value of the transferredQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public BigDecimal getTransferredQuantity() {
        return transferredQuantity;
    }

    /**
     * Sets the value of the transferredQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setTransferredQuantity(BigDecimal value) {
        this.transferredQuantity = value;
    }

    /**
     * Gets the value of the receivedQuantity property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    /**
     * Sets the value of the receivedQuantity property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setReceivedQuantity(BigDecimal value) {
        this.receivedQuantity = value;
    }

    /**
     * Gets the value of the unitOfMeasure property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    /**
     * Sets the value of the unitOfMeasure property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitOfMeasure(String value) {
        this.unitOfMeasure = value;
    }

    /**
     * Gets the value of the unitPrice property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /**
     * Sets the value of the unitPrice property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnitPrice(BigDecimal value) {
        this.unitPrice = value;
    }

    /**
     * Gets the value of the negotiatedUnitPrice property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getNegotiatedUnitPrice() {
        return negotiatedUnitPrice;
    }

    /**
     * Sets the value of the negotiatedUnitPrice property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNegotiatedUnitPrice(BigDecimal value) {
        this.negotiatedUnitPrice = value;
    }

    /**
     * Gets the value of the totalCost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getTotalCost() {
        return totalCost;
    }

    /**
     * Sets the value of the totalCost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalCost(BigDecimal value) {
        this.totalCost = value;
    }

    /**
     * Gets the value of the amountPaid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getAmountPaid() {
        return amountPaid;
    }

    /**
     * Sets the value of the amountPaid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAmountPaid(BigDecimal value) {
        this.amountPaid = value;
    }

    public int getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(int packagingId) {
        this.packagingId = packagingId;
    }

    public String getPackagingName() {
        return packagingName;
    }

    public void setPackagingName(String packagingName) {
        this.packagingName = packagingName;
    }

    public BigDecimal getPackagingQty() {
        return packagingQty;
    }

    public void setPackagingQty(BigDecimal packagingQty) {
        this.packagingQty = packagingQty;
    }

    public BigDecimal getConversionRatio() {
        return conversionRatio;
    }

    public void setConversionRatio(BigDecimal conversionRatio) {
        this.conversionRatio = conversionRatio;
    }

    public boolean isExemptItem() {
        return exemptItem;
    }

    public void setExemptItem(boolean exemptItem) {
        this.exemptItem = exemptItem;
    }

    public PercentageDetail getIgst() {
        return igst;
    }

    public void setIgst(PercentageDetail igst) {
        this.igst = igst;
    }

    public PercentageDetail getCgst() {
        return cgst;
    }

    public void setCgst(PercentageDetail cgst) {
        this.cgst = cgst;
    }

    public PercentageDetail getSgst() {
        return sgst;
    }

    public void setSgst(PercentageDetail sgst) {
        this.sgst = sgst;
    }

    public List<OtherTaxDetail> getOtherTaxes() {
        return otherTaxes;
    }

    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    public void setOtherTaxes(List<OtherTaxDetail> otherTaxes) {
        this.otherTaxes = otherTaxes;
    }

    public Integer getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime) {
        this.leadTime = leadTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getOtherTax() {
        return otherTax;
    }

    public void setOtherTax(BigDecimal otherTax) {
        this.otherTax = otherTax;
    }

    @Override
	public String toString() {
		return "PurchaseOrderItem [id=" + id + ", skuId=" + skuId + ", hsn=" + hsn + ", skuName=" + skuName
				+ ", requestedQuantity=" + requestedQuantity + ", requestedAbsoluteQuantity="
				+ requestedAbsoluteQuantity + ", transferredQuantity=" + transferredQuantity + ", receivedQuantity="
				+ receivedQuantity + ", unitOfMeasure=" + unitOfMeasure + ", unitPrice=" + unitPrice
				+ ", negotiatedUnitPrice=" + negotiatedUnitPrice + ", totalCost=" + totalCost + ", amountPaid="
				+ amountPaid + ", totalTax=" + totalTax + ", packagingId=" + packagingId + ", packagingName="
				+ packagingName + ", packagingQty=" + packagingQty + ", conversionRatio=" + conversionRatio
				+ ", exemptItem=" + exemptItem + ", igst=" + igst + ", cgst=" + cgst + ", sgst=" + sgst
				+ ", otherTaxes=" + otherTaxes + "]";
	}


    public List<Pair<BigDecimal, String>> getPriceHistory() {
        return priceHistory;
    }

    public void setPriceHistory(List<Pair<BigDecimal, String>> priceHistory) {
        this.priceHistory = priceHistory;
    }
    
}
