//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for AttributeDefinition complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AttributeDefinition"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="attributeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attributeName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attributeType" type="{http://www.w3schools.com}AttributeType"/&gt;
 *         &lt;element name="attributeCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attributeShortCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attributeDescription" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="attributeStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AttributeDefinition", propOrder = {
    "attributeId",
    "attributeName",
    "attributeType",
    "attributeCode",
    "attributeShortCode",
    "attributeDescription",
    "attributeStatus"
})
public class AttributeDefinition implements Comparable {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer attributeId;
    @XmlElement(required = true)
    protected String attributeName;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected AttributeType attributeType;
    @XmlElement(required = true)
    protected String attributeCode;
    @XmlElement(required = true)
    protected String attributeShortCode;
    @XmlElement(required = true)
    protected String attributeDescription;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus attributeStatus;

    /**
     * Gets the value of the attributeId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getAttributeId() {
        return attributeId;
    }

    /**
     * Sets the value of the attributeId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setAttributeId(Integer value) {
        this.attributeId = value;
    }

    /**
     * Gets the value of the attributeName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttributeName() {
        return attributeName;
    }

    /**
     * Sets the value of the attributeName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttributeName(String value) {
        this.attributeName = value;
    }

    /**
     * Gets the value of the attributeType property.
     * 
     * @return
     *     possible object is
     *     {@link AttributeType }
     *     
     */
    public AttributeType getAttributeType() {
        return attributeType;
    }

    /**
     * Sets the value of the attributeType property.
     * 
     * @param value
     *     allowed object is
     *     {@link AttributeType }
     *     
     */
    public void setAttributeType(AttributeType value) {
        this.attributeType = value;
    }

    /**
     * Gets the value of the attributeCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttributeCode() {
        return attributeCode;
    }

    /**
     * Sets the value of the attributeCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttributeCode(String value) {
        this.attributeCode = value;
    }

    /**
     * Gets the value of the attributeShortCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttributeShortCode() {
        return attributeShortCode;
    }

    /**
     * Sets the value of the attributeShortCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttributeShortCode(String value) {
        this.attributeShortCode = value;
    }

    /**
     * Gets the value of the attributeDescription property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAttributeDescription() {
        return attributeDescription;
    }

    /**
     * Sets the value of the attributeDescription property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttributeDescription(String value) {
        this.attributeDescription = value;
    }

    /**
     * Gets the value of the attributeStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *     
     */
    public SwitchStatus getAttributeStatus() {
        return attributeStatus;
    }

    /**
     * Sets the value of the attributeStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *     
     */
    public void setAttributeStatus(SwitchStatus value) {
        this.attributeStatus = value;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AttributeDefinition that = (AttributeDefinition) o;

        if (!attributeId.equals(that.attributeId)) return false;
        if (!attributeName.equals(that.attributeName)) return false;
        if (attributeType != that.attributeType) return false;
        if (!attributeCode.equals(that.attributeCode)) return false;
        if (!attributeShortCode.equals(that.attributeShortCode)) return false;
        if (!attributeDescription.equals(that.attributeDescription)) return false;
        return attributeStatus == that.attributeStatus;

    }

    @Override
    public int hashCode() {
        int result = attributeId.hashCode();
        result = 31 * result + attributeName.hashCode();
        result = 31 * result + attributeType.hashCode();
        result = 31 * result + attributeCode.hashCode();
        result = 31 * result + attributeShortCode.hashCode();
        result = 31 * result + attributeDescription.hashCode();
        result = 31 * result + attributeStatus.hashCode();
        return result;
    }

    @Override
    public int compareTo(Object o) {
        AttributeDefinition ad = (AttributeDefinition) o;
        return this.getAttributeId().compareTo(ad.getAttributeId());
    }
}
