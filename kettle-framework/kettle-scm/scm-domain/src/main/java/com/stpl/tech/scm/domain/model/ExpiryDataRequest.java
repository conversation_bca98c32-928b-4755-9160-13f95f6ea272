package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;

public class ExpiryDataRequest extends AbstractInventoryVO implements ConsumptionVO {

	protected Integer unitId;
	protected PriceUpdateEntryType inventoryType;
	protected List<ItemExpiryData> inventoryItems;

	public ExpiryDataRequest() {
		// TODO Auto-generated constructor stub
	}

	public void setInventoryItems(List<ItemExpiryData> inventoryItems) {
		this.inventoryItems = inventoryItems;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public void setInventoryType(PriceUpdateEntryType inventoryType) {
		this.inventoryType = inventoryType;
	}

	@Override
	public int getKeyId() {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public StockEventType getKeyType() {
		return StockEventType.TRANSFER_OUT;
	}

	@Override
	public int getUnitId() {
		return this.unitId;
	}

	@Override
	public PriceUpdateEntryType getInventoryType() {
		return this.inventoryType;
	}

	@Override
	public List<InventoryItemVO> getInventoryItems() {
		return new ArrayList<>(this.inventoryItems);
	}

	@Override
	public boolean isAssetOrder() {
		return false;
	}

}
