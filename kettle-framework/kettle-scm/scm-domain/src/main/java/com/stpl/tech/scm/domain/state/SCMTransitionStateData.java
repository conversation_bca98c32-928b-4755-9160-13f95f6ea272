//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.06 at 03:23:09 PM IST 
//


package com.stpl.tech.scm.domain.state;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="states" type="{http://www.w3schools.com}SCMStateData" maxOccurs="unbounded"/&gt;
 *         &lt;element name="transitions" type="{http://www.w3schools.com}SCMTransitionState" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "states",
    "transitions"
})
@XmlRootElement(name = "SCMTransitionStateData")
public class SCMTransitionStateData {

    @XmlElement(required = true)
    protected List<SCMStateData> states;
    @XmlElement(required = true)
    protected List<SCMTransitionState> transitions;

    /**
     * Gets the value of the states property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the states property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getStates().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SCMStateData }
     *
     *
     */
    public List<SCMStateData> getStates() {
        if (states == null) {
            states = new ArrayList<SCMStateData>();
        }
        return this.states;
    }

    /**
     * Gets the value of the transitions property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the transitions property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTransitions().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SCMTransitionState }
     *
     *
     */
    public List<SCMTransitionState> getTransitions() {
        if (transitions == null) {
            transitions = new ArrayList<SCMTransitionState>();
        }
        return this.transitions;
    }

}
