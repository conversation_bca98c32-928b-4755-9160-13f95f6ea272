package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;

import java.util.Date;


public class PendingTransferOrder {

    protected Integer id;
    protected IdCodeName generationUnitId;
    protected IdCodeName generatedForUnitId;
    protected Integer requestOrderId;
    protected IdCodeName generatedBy;
    protected Date generationTime;
    protected SCMOrderStatus status;


    public PendingTransferOrder(Integer id, Integer generationUnitId, Integer generatedForUnitId, Integer requestOrder, Integer generatedBy, Date generationTime, String status) {
        this.id = id;
        this.generationUnitId = new IdCodeName(generationUnitId,null,null);
        this.generatedForUnitId = new IdCodeName(generatedForUnitId,null,null);
        this.requestOrderId = requestOrder;
        this.generatedBy = new IdCodeName(generatedBy,null,null);
        this.generationTime = generationTime;
        this.status = SCMOrderStatus.fromValue(status);
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public IdCodeName getGeneratedForUnitId() {
        return generatedForUnitId;
    }

    public void setGeneratedForUnitId(IdCodeName generatedForUnitId) {
        this.generatedForUnitId = generatedForUnitId;
    }

    public Integer getRequestOrderId() {
        return requestOrderId;
    }

    public void setRequestOrderId(Integer requestOrderId) {
        this.requestOrderId = requestOrderId;
    }

    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(IdCodeName generatedBy) {
        this.generatedBy = generatedBy;
    }

    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    public SCMOrderStatus getStatus() {
        return status;
    }

    public void setStatus(SCMOrderStatus status) {
        this.status = status;
    }

    public IdCodeName getGenerationUnitId() {
        return generationUnitId;
    }

    public void setGenerationUnitId(IdCodeName generationUnitId) {
        this.generationUnitId = generationUnitId;
    }
}
