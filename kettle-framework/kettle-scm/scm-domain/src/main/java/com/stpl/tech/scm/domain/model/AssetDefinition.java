/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.12 at 03:52:20 PM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AssetDefinition", propOrder = {
        "assetId",
        "assetName",
        "assetImageUrl",
        "unitId",
        "unitType",
        "assetStatus",
        "skuId",
        "productId",
        "profileId",
        "grId",
        "grItemId",
        "vendorId",
        "vendorName",
        "ownerType",
        "ownerId",
        "firstOwnerType",
        "firstOwnerId",
        "tagType",
        "tagValue",
        "tagPrintCount",
        "lastTagPrintDate",
        "lastTagPrintedBy",
        "price",
        "tax",
        "taxPercentage",
        "procurementCost",
        "quantity",
        "lifeTimeType",
        "lifeTimeValue",
        "lifeTimeInDays",
        "inventoryDate",
        "startDate",
        "expectedEndDate",
        "actualEndDate",
        "depreciationStrategy",
        "depreciationRatePa",
        "dailyDepreciationRate",
        "depreciationResidue",
        "realizedDepreciation",
        "realizedDepreciationDate",
        "lastTransferType",
        "lastTransferId",
        "lastTransferDate",
        "lastTransferedBy",
        "hasWarranty",
        "warrantyLastDate",
        "hasAMC",
        "amcLastDate",
        "hasInsurance",
        "insuranceLastDate",
        "creationDate",
        "createdBy",
        "grossBlock",
        "fixedValue",
        "recoveryType",
        "recoveryAmount",
        "recoveryStatus",
        "writeOffType",
        "writeOffAmount",
        "writtenOff",
        "lifeTimeCategoryMonths"

})
public class AssetDefinition {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer assetId;

    @XmlElement(required = false)
    protected String assetName;

    protected String assetImageUrl;

    /*
        UnitId signifies Location of Asset
     */
    @XmlElement(required = true)
    protected Integer unitId;

    @XmlElement(required = true)
    protected String unitType;

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected AssetStatusType assetStatus;

    @XmlElement(required = true)
    protected Integer skuId;

    @XmlElement(required = true)
    protected Integer productId;

    @XmlElement(required = true)
    protected Integer profileId;

    @XmlElement(required = true)
    protected Integer grId;
    @XmlElement(required = true)
    protected Integer grItemId;

    @XmlElement(required = true)
    protected Integer vendorId;
    @XmlElement(required = true)
    protected String vendorName;

    @XmlElement(required = true)
    protected String ownerType;
    @XmlElement(required = true)
    protected Integer ownerId;

    @XmlElement(required = true)
    protected String firstOwnerType;
    @XmlElement(required = true)
    protected Integer firstOwnerId;

    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected TagType tagType;
    @XmlElement(required = true)
    protected String tagValue;
    protected Integer tagPrintCount;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date lastTagPrintDate;
    @XmlElement(required = false)
    protected IdCodeName lastTagPrintedBy;

    protected Float price;
    protected Float tax;
    protected Float taxPercentage;
    protected Float procurementCost;
    @XmlElement(defaultValue = "1")
    protected Integer quantity;

    @XmlSchemaType(name = "string")
    protected LifeTimeType lifeTimeType;
    protected Float lifeTimeValue;
    protected Integer lifeTimeInDays;
    protected Integer lifeTimeCategoryMonths;

    @XmlElement(required = false, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date inventoryDate;

    @XmlElement(required = false, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date startDate;

    @XmlElement(required = false, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date expectedEndDate;

    @XmlElement(required = false, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date actualEndDate;

    protected String depreciationStrategy;
    protected Float depreciationRatePa;
    protected Float dailyDepreciationRate;
    protected Float depreciationResidue;
    protected Float realizedDepreciation;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date realizedDepreciationDate;

    protected String lastTransferType;
    protected Integer lastTransferId;
    @XmlElement(required = false, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date lastTransferDate;
    @XmlElement(required = false)
    protected IdCodeName lastTransferedBy;

    protected boolean hasWarranty;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date warrantyLastDate;

    protected boolean hasAMC;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date amcLastDate;

    protected boolean hasInsurance;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date insuranceLastDate;

    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "date")
    protected Date creationDate;
    @XmlElement(required = true)
    protected IdCodeName createdBy;

    private Float grossBlock;

    private Float fixedValue;

    private String recoveryType;

    private Float recoveryAmount;

    private String recoveryStatus;

    private String writeOffType;

    private Float writeOffAmount;

    private boolean writtenOff;


    protected List<ProfileAttributeMapping> profileAttributeMappingList;

    protected List<EntityAttributeValueMapping> entityAttributeValueMappings;

    protected Float currentValue;

    protected Float currentValueWithoutTax;

    protected String uniqueFieldName;

    protected String uniqueFieldValue;

    protected IdCodeName subCategoryDefinition;

    protected  Boolean isInTransit;

    protected Boolean isScanned;

    protected BigDecimal totalRecoverAmount;

    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    public String getAssetName() {
        return assetName;
    }

    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }

    public String getAssetImageUrl() {
        return assetImageUrl;
    }

    public void setAssetImageUrl(String assetImageUrl) {
        this.assetImageUrl = assetImageUrl;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public AssetStatusType getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(AssetStatusType assetStatus) {
        this.assetStatus = assetStatus;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    public Integer getGrId() {
        return grId;
    }

    public void setGrId(Integer grId) {
        this.grId = grId;
    }

    public Integer getGrItemId() {
        return grItemId;
    }

    public void setGrItemId(Integer grItemId) {
        this.grItemId = grItemId;
    }

    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public String getFirstOwnerType() {
        return firstOwnerType;
    }

    public void setFirstOwnerType(String firstOwnerType) {
        this.firstOwnerType = firstOwnerType;
    }

    public Integer getFirstOwnerId() {
        return firstOwnerId;
    }

    public void setFirstOwnerId(Integer firstOwnerId) {
        this.firstOwnerId = firstOwnerId;
    }

    public TagType getTagType() {
        return tagType;
    }

    public void setTagType(TagType tagType) {
        this.tagType = tagType;
    }

    public String getTagValue() {
        return tagValue;
    }

    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    public Integer getTagPrintCount() {
        return tagPrintCount;
    }

    public void setTagPrintCount(Integer tagPrintCount) {
        this.tagPrintCount = tagPrintCount;
    }

    public Date getLastTagPrintDate() {
        return lastTagPrintDate;
    }

    public void setLastTagPrintDate(Date lastTagPrintDate) {
        this.lastTagPrintDate = lastTagPrintDate;
    }

    public IdCodeName getLastTagPrintedBy() {
        return lastTagPrintedBy;
    }

    public void setLastTagPrintedBy(IdCodeName lastTagPrintedBy) {
        this.lastTagPrintedBy = lastTagPrintedBy;
    }

    public Float getPrice() {
        return price;
    }

    public void setPrice(Float price) {
        this.price = price;
    }

    public Float getTax() {
        return tax;
    }

    public void setTax(Float tax) {
        this.tax = tax;
    }

    public Float getProcurementCost() {
        return procurementCost;
    }

    public void setProcurementCost(Float procurementCost) {
        this.procurementCost = procurementCost;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public LifeTimeType getLifeTimeType() {
        return lifeTimeType;
    }

    public void setLifeTimeType(LifeTimeType lifeTimeType) {
        this.lifeTimeType = lifeTimeType;
    }

    public Float getLifeTimeValue() {
        return lifeTimeValue;
    }

    public void setLifeTimeValue(Float lifeTimeValue) {
        this.lifeTimeValue = lifeTimeValue;
    }

    public Integer getLifeTimeInDays() {
        return lifeTimeInDays;
    }

    public void setLifeTimeInDays(Integer lifeTimeInDays) {
        this.lifeTimeInDays = lifeTimeInDays;
    }

    public Date getInventoryDate() {
        return inventoryDate;
    }

    public void setInventoryDate(Date inventoryDate) {
        this.inventoryDate = inventoryDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getExpectedEndDate() {
        return expectedEndDate;
    }

    public void setExpectedEndDate(Date expectedEndDate) {
        this.expectedEndDate = expectedEndDate;
    }

    public Date getActualEndDate() {
        return actualEndDate;
    }

    public void setActualEndDate(Date actualEndDate) {
        this.actualEndDate = actualEndDate;
    }

    public String getDepreciationStrategy() {
        return depreciationStrategy;
    }

    public void setDepreciationStrategy(String depreciationStrategy) {
        this.depreciationStrategy = depreciationStrategy;
    }

    public Float getDepreciationRatePa() {
        return depreciationRatePa;
    }

    public void setDepreciationRatePa(Float depreciationRatePa) {
        this.depreciationRatePa = depreciationRatePa;
    }

    public Float getDailyDepreciationRate() {
        return dailyDepreciationRate;
    }

    public void setDailyDepreciationRate(Float dailyDepreciationRate) {
        this.dailyDepreciationRate = dailyDepreciationRate;
    }

    public Float getDepreciationResidue() {
        return depreciationResidue;
    }

    public void setDepreciationResidue(Float depreciationResidue) {
        this.depreciationResidue = depreciationResidue;
    }

    public Float getRealizedDepreciation() {
        return realizedDepreciation;
    }

    public void setRealizedDepreciation(Float realizedDepreciation) {
        this.realizedDepreciation = realizedDepreciation;
    }

    public Date getRealizedDepreciationDate() {
        return realizedDepreciationDate;
    }

    public void setRealizedDepreciationDate(Date realizedDepreciationDate) {
        this.realizedDepreciationDate = realizedDepreciationDate;
    }

    public String getLastTransferType() {
        return lastTransferType;
    }

    public void setLastTransferType(String lastTransferType) {
        this.lastTransferType = lastTransferType;
    }

    public Integer getLastTransferId() {
        return lastTransferId;
    }

    public void setLastTransferId(Integer lastTransferId) {
        this.lastTransferId = lastTransferId;
    }

    public Date getLastTransferDate() {
        return lastTransferDate;
    }

    public void setLastTransferDate(Date lastTransferDate) {
        this.lastTransferDate = lastTransferDate;
    }

    public IdCodeName getLastTransferedBy() {
        return lastTransferedBy;
    }

    public void setLastTransferedBy(IdCodeName lastTransferedBy) {
        this.lastTransferedBy = lastTransferedBy;
    }

    public boolean isHasWarranty() {
        return hasWarranty;
    }

    public void setHasWarranty(boolean hasWarranty) {
        this.hasWarranty = hasWarranty;
    }

    public Date getWarrantyLastDate() {
        return warrantyLastDate;
    }

    public void setWarrantyLastDate(Date warrantyLastDate) {
        this.warrantyLastDate = warrantyLastDate;
    }

    public boolean isHasAMC() {
        return hasAMC;
    }

    public void setHasAMC(boolean hasAMC) {
        this.hasAMC = hasAMC;
    }

    public Date getAmcLastDate() {
        return amcLastDate;
    }

    public void setAmcLastDate(Date amcLastDate) {
        this.amcLastDate = amcLastDate;
    }

    public boolean isHasInsurance() {
        return hasInsurance;
    }

    public void setHasInsurance(boolean hasInsurance) {
        this.hasInsurance = hasInsurance;
    }

    public Date getInsuranceLastDate() {
        return insuranceLastDate;
    }

    public void setInsuranceLastDate(Date insuranceLastDate) {
        this.insuranceLastDate = insuranceLastDate;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdCodeName createdBy) {
        this.createdBy = createdBy;
    }

    public List<ProfileAttributeMapping> getProfileAttributeMappingList() {
        return profileAttributeMappingList;
    }

    public void setProfileAttributeMappingList(List<ProfileAttributeMapping> profileAttributeMappingList) {
        this.profileAttributeMappingList = profileAttributeMappingList;
    }

    public List<EntityAttributeValueMapping> getEntityAttributeValueMappings() {
        return entityAttributeValueMappings;
    }

    public void setEntityAttributeValueMappings(List<EntityAttributeValueMapping> entityAttributeValueMappings) {
        this.entityAttributeValueMappings = entityAttributeValueMappings;
    }

    public Float getGrossBlock() {
        return grossBlock;
    }

    public void setGrossBlock(Float grossBlock) {
        this.grossBlock = grossBlock;
    }

    public Float getFixedValue() {
        return fixedValue;
    }

    public void setFixedValue(Float fixedValue) {
        this.fixedValue = fixedValue;
    }

    public String getRecoveryType() {
        return recoveryType;
    }

    public void setRecoveryType(String recoveryType) {
        this.recoveryType = recoveryType;
    }

    public Float getRecoveryAmount() {
        return recoveryAmount;
    }

    public void setRecoveryAmount(Float recoveryAmount) {
        this.recoveryAmount = recoveryAmount;
    }

    public String getRecoveryStatus() {
        return recoveryStatus;
    }

    public void setRecoveryStatus(String recoveryStatus) {
        this.recoveryStatus = recoveryStatus;
    }

    public String getWriteOffType() {
        return writeOffType;
    }

    public void setWriteOffType(String writeOffType) {
        this.writeOffType = writeOffType;
    }

    public Float getWriteOffAmount() {
        return writeOffAmount;
    }

    public void setWriteOffAmount(Float writeOffAmount) {
        this.writeOffAmount = writeOffAmount;
    }

    public boolean isWrittenOff() {
        return writtenOff;
    }

    public void setWrittenOff(boolean writtenOff) {
        this.writtenOff = writtenOff;
    }

    public Float getTaxPercentage() {
        return taxPercentage;
    }

    public void setTaxPercentage(Float taxPercentage) {
        this.taxPercentage = taxPercentage;
    }

    public Float getCurrentValue() {
        return currentValue;
    }

    public void setCurrentValue(Float currentValue) {
        this.currentValue = currentValue;
    }

    public Float getCurrentValueWithoutTax() {
        return currentValueWithoutTax;
    }

    public void setCurrentValueWithoutTax(Float currentValueWithoutTax) {
        this.currentValueWithoutTax = currentValueWithoutTax;
    }

    public String getUniqueFieldName() {
        return uniqueFieldName;
    }

    public void setUniqueFieldName(String uniqueFieldName) {
        this.uniqueFieldName = uniqueFieldName;
    }

    public String getUniqueFieldValue() {
        return uniqueFieldValue;
    }

    public void setUniqueFieldValue(String uniqueFieldValue) {
        this.uniqueFieldValue = uniqueFieldValue;
    }

    public IdCodeName getSubCategoryDefinition() {
        return subCategoryDefinition;
    }

    public void setSubCategoryDefinition(IdCodeName subCategoryDefinition) {
        this.subCategoryDefinition = subCategoryDefinition;
    }

    public Integer getLifeTimeCategoryMonths() {
        return lifeTimeCategoryMonths;
    }

    public void setLifeTimeCategoryMonths(Integer lifeTimeCategoryMonths) {
        this.lifeTimeCategoryMonths = lifeTimeCategoryMonths;
    }

    public Boolean getInTransit() {
        return isInTransit;
    }

    public void setInTransit(Boolean inTransit) {
        isInTransit = inTransit;
    }

    public Boolean getScanned() {
        return isScanned;
    }

    public void setScanned(Boolean scanned) {
        isScanned = scanned;
    }

    public BigDecimal getTotalRecoverAmount() {
        return totalRecoverAmount;
    }

    public void setTotalRecoverAmount(BigDecimal totalRecoverAmount) {
        this.totalRecoverAmount = totalRecoverAmount;
    }
}
