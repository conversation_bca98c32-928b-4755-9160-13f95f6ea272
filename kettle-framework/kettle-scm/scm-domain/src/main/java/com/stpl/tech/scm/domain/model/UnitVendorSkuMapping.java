//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.11.19 at 11:43:30 AM IST 
//

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitVendorSkuMapping", propOrder = { "unitSkuMappingId", "sku", "vendor", "keyId", "status" })
public class UnitVendorSkuMapping {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer unitSkuMappingId;
	@XmlElement(required = true)
	protected IdCodeName sku;
	@XmlElement(required = true)
	protected IdCodeName vendor;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer keyId;
	@XmlSchemaType(name = "string")
	protected SwitchStatus status;

	/**
	 * Gets the value of the unitSkuMappingId property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getUnitSkuMappingId() {
		return unitSkuMappingId;
	}

	/**
	 * Sets the value of the unitSkuMappingId property.
	 * 
	 * @param value
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setUnitSkuMappingId(Integer value) {
		this.unitSkuMappingId = value;
	}

	/**
	 * Gets the value of the vendor property.
	 *
	 * @return possible object is {@link IdCodeName }
	 * 
	 */
	public IdCodeName getVendor() {
		return vendor;
	}

	/**
	 * Sets the value of the vendor property.
	 *
	 * @param value
	 *            allowed object is {@link IdCodeName }
	 * 
	 */
	public void setVendor(IdCodeName value) {
		this.vendor = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link SwitchStatus }
	 * 
	 */
	public SwitchStatus getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link SwitchStatus }
	 * 
	 */
	public void setStatus(SwitchStatus value) {
		this.status = value;
	}

	public IdCodeName getSku() {
		return sku;
	}

	public void setSku(IdCodeName sku) {
		this.sku = sku;
	}

	public Integer getKeyId() {
		return keyId;
	}

	public void setKeyId(Integer unitSkuVendorMappingId) {
		this.keyId = unitSkuVendorMappingId;
	}

}
