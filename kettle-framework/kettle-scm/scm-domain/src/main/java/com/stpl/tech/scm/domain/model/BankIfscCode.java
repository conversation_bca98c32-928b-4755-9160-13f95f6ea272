package com.stpl.tech.scm.domain.model;

public enum BankIfscCode {

    ABHY("Abhyudaya Co-op Bank Ltd"),
    ADCB("Abu Dhabi Commercial Bank Ltd"),
    NBAD("National Bank of Abu Dhabi PJSC"),
    ABBL("AB Bank Ltd."),
    AMCB("Ahmedabad Mercantile Co-op Bank"),
    ALLA("Allahabad Bank"),
    ANDB("Andhra Bank"),
    ADIA("Antwerp Diamond Bank Mumbai"),
    ANZB("Australia and New Zealand Banking Group Limited"),
    UTIB("Axis Bank"),
    BOFA("Bank Of America"),
    BBKM("Bank Of Bahrain And Kuwait"),
    BARB("Bank of Baroda"),
    BCEY("Bank Of Ceylon"),
    BKID("Bank of India"),
    MAHB("Bank Of Maharashtra"),
    NOSC("Bank Of Nova Scotia"),
    BOTM("Bank Of Tokyo-Mitsubishi Ufj Ltd"),
    IBBK("Bank Internasional Indonesia"),
    BARC("Barclays Bank Plc"),
    BACB("Bassein Catholic Co-op Bank Ltd"),
    BCBM("Bharat Co-op Bank (Mumbai) Ltd"),
    BNPA("BNP Paribas"),
    CNRB("Canara Bank"),
    CSBK("Catholic Syrian Bank Ltd"),
    CBIN("Central Bank of India"),
    CTCB("Chinatrust Commercial Bank"),
    CITI("Citibank India"),
    CCBL("Citizencredit Co-op Bank Ltd"),
    CIUB("City Union Bank Ltd"),
    CORP("Corporation Bank"),
    COSB("Cosmos Co-op Bank Ltd"),
    CRLY("Credit Agricole Corp and Investment Bank"),
    CTBA("Commonwealth Bank of Australia"),
    DBSS("DBS Bank"),
    BKDN("Dena Bank"),
    DEUT("Deutsche Bank Ag"),
    DCBL("Development Credit Bank"),
    DLXB("Dhanlaxmi Bank Ltd"),
    DICG("Dicgc"),
    DOHB("Doha Bank"),
    DNSB("Dombivli Nagari Sahakari Bank Ltd"),
    EIBI("Export-Import Bank of India"),
    FDRL("Federal Bank Ltd"),
    FIRN("Firstrand Bank Ltd"),
    GBCB("Greater Bombay Co-op Bank Ltd"),
    HDFC("HDFC Bank"),
    HSBC("HSBC"),
    ICIC("ICICI Bank"),
    IBKL("IDBI Bank"),
    IDFB("IDFC Bank Limited"),
    IDIB("Indian Bank"),
    IOBA("Indian Overseas Bank"),
    INDB("IndusInd Bank Ltd."),
    ICBK("Industrial and Commercial Bank of China Ltd."),
    VYSA("ING Vysya Bank"),
    IBKO("Industrial Bank of  Korea"),
    JAKA("Jammu & Kashmir Bank Ltd."),
    JSBL("Janakalyan Sahakari Bank Ltd"),
    JSBP("Janata Sahkari Bank Ltd Pune"),
    CHAS("Jpmorgan Chase Bank"),
    KCCB("Kalupur Commercial Co-op Bank Ltd"),
    KJSB("Kalyan Janata Sahakari Bank Ltd"),
    KCBL("Kapole Co-op Bank"),
    KARB("Karnataka Bank Ltd."),
    KSCB("Karnataka State Co-op Apex Bank"),
    KVBL("Karur Vysya Bank"),
    KKBK("Kotak Mahindra Bank"),
    KOEX("KEB  Hana Bank"),
    KRTH("Krung Thai Bank PCL Mumbai"),
    LAVB("Lakshmi Vilas Bank Ltd"),
    MCBL("Mahanagar Co-op Bank Ltd"),
    MSCI("Maharashtra State Co-operative Bank Limited"),
    MSHQ("Mashreq Bank Psc"),
    MSNU("Mehsana Urban Co-op Bank Ltd"),
    MHCB("Mizuho Corporate Bank Ltd"),
    NTBL("Nainital Bank Ltd"),
    NATA("National Australia Bank"),
    NICB("New India Co-op Bank Ltd"),
    NKGS("Nkgsb Co-op Bank Ltd"),
    NNSB("Nutan Nagarik Sahakari Bank Ltd"),
    OIBA("Oman International Bank Saog"),
    ORBC("Oriental Bank of Commerce"),
    PJSB("Parsik Janata Sahakari Bank Ltd"),
    PMCB("Punjab and Maharashtra Bank Ltd."),
    PUNB("Punjab National Bank"),
    PSIB("Punjab & Sind Bank"),
    QNBA("Qatar National Bank(QPSC)"),
    RNSB("Rajkot Nagarik Sahakari Bank Ltd"),
    RATN("Ratnakar Bank Ltd"),
    RBIS("Reserve Bank Of India"),
    ABNA("Royal bank of scotland"),
    RABO("Rabobank International"),
    SABR("Sberbank"),
    SRCB("Saraswat Co-operative Bank Ltd."),
    SVCB("Shamrao Vithal Co-op Bank Ltd"),
    SHBK("Shinhan Bank"),
    SOGE("Societe Generale"),
    BSON("Sonali Bank Ltd."),
    SIBL("South Indian Bank"),
    SCBL("Standard Chartered Bank"),
    SBBJ("State Bank Of Bikaner And Jaipur"),
    SBHY("State Bank of Hyderabad"),
    SBIN("State Bank of India"),
    NBFC("SBI Global Factors Ltd(SBIGFL) factoring Unit"),
    BMCB("Bombay Mercantile Co-operative bank Limited"),
    STCB("State Bank Of Mauritius Ltd"),
    SBMY("State Bank of Mysore"),
    STBP("State Bank Of Patiala"),
    SBTR("State Bank Of Travancore"),
    SSAU("State Bank of Saurashtra"),
    SMBC("Sumitomo Mitsui Banking Corporation"),
    SPCB("Surat Peoples Co-op Bank Ltd"),
    SYNB("Syndicate Bank"),
    TMBL("Tamilnad Mercantile Bank Ltd"),
    TJSB("Thane Janta Sahakari Bank, Ltd."),
    KUCB("The Karad Urban Co-op Bank Ltd"),
    NMCB("The Nasik Merchants Co-op Bank Ltd"),
    UBSW("UBS AG"),
    UCBA("UCO Bank"),
    UBIN("Union Bank of India"),
    UTBI("United Bank of India"),
    UOVB("United Overseas Bank"),
    VIJB("Vijaya Bank"),
    WBSC("West Bengal State Co-op Bank Ltd"),
    WPAC("Westpac Banking Corporation"),
    HVBK("Woori Bank"),
    YESB("Yes Bank"),
    EBIL("Emirates NBD Bank  PJSC");


    private final String bankName;

    BankIfscCode(String bankName) {
        this.bankName = bankName;
    }

    public String getBankName() {
        return this.bankName;
    }

    public static BankIfscCode fromValue(String v) {
        return valueOf(v);
    }

    public static String getBankNameFromIfsc(String ifsc) {
        for (BankIfscCode code : values()) {
            if (code.name().equalsIgnoreCase(ifsc)) {
                return code.getBankName();
            }
        }
        return null;
    }
}
