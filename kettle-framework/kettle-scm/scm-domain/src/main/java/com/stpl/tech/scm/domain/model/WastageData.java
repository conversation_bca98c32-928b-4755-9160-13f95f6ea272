//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.26 at 02:20:27 AM IST 
//

package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.util.domain.adapter.BigDecimalSixPrecisionDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.formula.eval.NotImplementedException;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WastageData", propOrder = { "id", "quantity", "productId", "product", "skuId", "price", "cost" })
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WastageData implements InventoryItemVO {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal quantity;
	protected Integer productId;
	protected ProductBasicDetail product;
	protected Integer skuId;
	protected SkuBasicDetail sku;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal price;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal cost;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal tax;
	@JsonDeserialize(using = BigDecimalSixPrecisionDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter4.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal totalAmount;
	@XmlElement(required = true, nillable = true)
	protected String comment;
	protected Integer reasonCode;
	List<InventoryItemDrilldown> drillDowns;
	@XmlElement(required = false, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date expiryDate;
	protected List<MonkWastageDetail> wasteDrillDown;
	protected Integer productionId;
	protected String enteredComment;
	protected String subCategory;
	protected Integer wastageEventId;
	protected String employeeName;
	protected String status;
	protected Date generationTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public Integer getSkuId() {
		return skuId;
	}

	public void setSkuId(Integer skuId) {
		this.skuId = skuId;
	}

	public SkuBasicDetail getSku() {
		return sku;
	}

	public void setSku(SkuBasicDetail sku) {
		this.sku = sku;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getCost() {
		return cost;
	}

	public void setCost(BigDecimal cost) {
		this.cost = cost;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public ProductBasicDetail getProduct() {
		return product;
	}

	public void setProduct(ProductBasicDetail product) {
		this.product = product;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Integer getReasonCode() {
		return reasonCode;
	}

	public void setReasonCode(Integer reasonCode) {
		this.reasonCode = reasonCode;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyId()
	 */
	@Override
	@JsonIgnore
	public int getItemKeyId() {
		return this.id!=null ? this.id : 0;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getItemKeyType()
	 */
	@Override
	@JsonIgnore
	public StockEventType getItemKeyType() {
		return StockEventType.WASTAGE;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getDrillDowns()
	 */
	@Override
	@JsonIgnore
	public List<InventoryItemDrilldown> getDrillDowns() {
		if(drillDowns == null){
			this.drillDowns = new ArrayList<>();
		}
		return this.drillDowns;
	}
	
	@Override
	@JsonIgnore
	public void setDrillDowns(List<InventoryItemDrilldown> drilldowns) {
		this.drillDowns = drilldowns;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyId()
	 */
	@Override
	@JsonIgnore
	public int getKeyId() {
		return this.skuId == null ? (this.productId==null ? 0 : this.productId) : this.skuId;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getKeyType()
	 */
	@Override
	@JsonIgnore
	public PriceUpdateEntryType getKeyType() {
		return this.skuId == null ? PriceUpdateEntryType.PRODUCT : PriceUpdateEntryType.SKU;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.scm.domain.vo.InventoryItemVO#getUom()
	 */
	@Override
	@JsonIgnore
	public String getUom() {
		return this.product != null ? this.product.unitOfMeasure : (this.sku != null ? this.sku.unitOfMeasure : null);
	}
	
	@Override
	public void setKeyType(PriceUpdateEntryType type) {
		throw new NotImplementedException("This should not be called for WastageData");
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public List<MonkWastageDetail> getWasteDrillDown() {
		return wasteDrillDown;
	}

	public void setWasteDrillDown(List<MonkWastageDetail> wasteDrillDown) {
		this.wasteDrillDown = wasteDrillDown;
	}

	public Integer getProductionId() {
		return productionId;
	}

	public void setProductionId(Integer productionId) {
		this.productionId = productionId;
	}

	public String getEnteredComment() {
		return enteredComment;
	}

	public void setEnteredComment(String enteredComment) {
		this.enteredComment = enteredComment;
	}

	public String getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(String subCategory) {
		this.subCategory = subCategory;
	}

	public Integer getWastageEventId() {
		return wastageEventId;
	}

	public void setWastageEventId(Integer wastageEventId) {
		this.wastageEventId = wastageEventId;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date generationTime) {
		this.generationTime = generationTime;
	}
}
