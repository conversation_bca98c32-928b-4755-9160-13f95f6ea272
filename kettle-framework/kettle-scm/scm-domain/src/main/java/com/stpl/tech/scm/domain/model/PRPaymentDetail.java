//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.10.04 at 11:12:48 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>Java class for PRPaymentDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PRPaymentDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendorId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendorName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="beneficiaryAccountNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="beneficiaryIfscCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="debitAccount" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="debitBank" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="paymentType" type="{http://www.w3schools.com}PaymentType"/&gt;
 *         &lt;element name="paidAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="paymentDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="remarks" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="proposedAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="utrNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="paymentRequests" type="{http://www.w3schools.com}PaymentRequest" maxOccurs="unbounded"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PRPaymentDetail", propOrder = {
    "id",
    "vendorId",
    "vendorName",
    "beneficiaryAccountNumber",
    "beneficiaryIfscCode",
    "debitAccount",
    "debitBank",
    "paymentType",
    "paidAmount",
    "paymentDate",
    "remarks",
    "proposedAmount",
    "createdBy",
    "utrNumber",
    "paymentRequests"
})
public class PRPaymentDetail {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer id;
    protected int vendorId;
    @XmlElement(required = true)
    protected String vendorName;
    @XmlElement(required = true)
    protected String beneficiaryAccountNumber;
    @XmlElement(required = true)
    protected String beneficiaryIfscCode;
    @XmlElement(required = true)
    protected String debitAccount;
    @XmlElement(required = true)
    protected String debitBank;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected PaymentType paymentType;
    @XmlElement(required = true)
    protected BigDecimal paidAmount;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date paymentDate;
    @XmlElement(required = true)
    protected Date actualDate;
    @XmlElement(required = true)
    protected String remarks;
    @XmlElement(required = true)
    protected BigDecimal proposedAmount;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true)
    protected String utrNumber;
    @XmlElement(required = true)
    protected List<PaymentRequest> paymentRequests;
    protected Date vendorPaymentDate;

    protected  BigDecimal tdsAmount;

    public void setPaymentRequests(List<PaymentRequest> paymentRequests) {
        this.paymentRequests = paymentRequests;
    }

    public BigDecimal getTdsAmount() {

        return tdsAmount;
    }

    public void setTdsAmount(BigDecimal tdsAmount) {
        this.tdsAmount = tdsAmount;
    }

    public Date getActualDate() {
        return actualDate;
    }

    public void setActualDate(Date actualDate) {
        this.actualDate = actualDate;
    }

    /**
     * Gets the value of the id property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */


    public Integer getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setId(Integer value) {
        this.id = value;
    }

    /**
     * Gets the value of the vendorId property.
     * 
     */
    public int getVendorId() {
        return vendorId;
    }

    /**
     * Sets the value of the vendorId property.
     * 
     */
    public void setVendorId(int value) {
        this.vendorId = value;
    }

    /**
     * Gets the value of the vendorName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVendorName() {
        return vendorName;
    }

    /**
     * Sets the value of the vendorName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVendorName(String value) {
        this.vendorName = value;
    }

    /**
     * Gets the value of the beneficiaryAccountNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBeneficiaryAccountNumber() {
        return beneficiaryAccountNumber;
    }

    /**
     * Sets the value of the beneficiaryAccountNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBeneficiaryAccountNumber(String value) {
        this.beneficiaryAccountNumber = value;
    }

    /**
     * Gets the value of the beneficiaryIfscCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBeneficiaryIfscCode() {
        return beneficiaryIfscCode;
    }

    /**
     * Sets the value of the beneficiaryIfscCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBeneficiaryIfscCode(String value) {
        this.beneficiaryIfscCode = value;
    }

    /**
     * Gets the value of the debitAccount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDebitAccount() {
        return debitAccount;
    }

    /**
     * Sets the value of the debitAccount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDebitAccount(String value) {
        this.debitAccount = value;
    }

    /**
     * Gets the value of the debitBank property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDebitBank() {
        return debitBank;
    }

    /**
     * Sets the value of the debitBank property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDebitBank(String value) {
        this.debitBank = value;
    }

    /**
     * Gets the value of the paymentType property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentType }
     *     
     */
    public PaymentType getPaymentType() {
        return paymentType;
    }

    /**
     * Sets the value of the paymentType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentType }
     *     
     */
    public void setPaymentType(PaymentType value) {
        this.paymentType = value;
    }

    /**
     * Gets the value of the paidAmount property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    /**
     * Sets the value of the paidAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPaidAmount(BigDecimal value) {
        this.paidAmount = value;
    }

    /**
     * Gets the value of the paymentDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getPaymentDate() {
        return paymentDate;
    }

    /**
     * Sets the value of the paymentDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPaymentDate(Date value) {
        this.paymentDate = value;
    }

    /**
     * Gets the value of the remarks property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * Sets the value of the remarks property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRemarks(String value) {
        this.remarks = value;
    }

    /**
     * Gets the value of the proposedAmount property.
     *
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *
     */
    public BigDecimal getProposedAmount() {
        return proposedAmount;
    }

    /**
     * Sets the value of the proposedAmount property.
     *
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *
     */
    public void setProposedAmount(BigDecimal value) {
        this.proposedAmount = value;
    }

    /**
     * Gets the value of the createdBy property.
     *
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     *
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the utrNumber property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getUtrNumber() {
        return utrNumber;
    }

    /**
     * Sets the value of the utrNumber property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setUtrNumber(String value) {
        this.utrNumber = value;
    }

    /**
     * Gets the value of the paymentRequests property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the paymentRequests property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPaymentRequests().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     *     {@link PaymentRequest }
     *     
     *
     */
    public List<PaymentRequest> getPaymentRequests() {
        if (paymentRequests == null) {
            paymentRequests = new ArrayList<PaymentRequest>();
    }
        return this.paymentRequests;
    }

    public Date getVendorPaymentDate() {
        return vendorPaymentDate;
    }

    public void setVendorPaymentDate(Date vendorPaymentDate) {
        this.vendorPaymentDate = vendorPaymentDate;
    }
}
