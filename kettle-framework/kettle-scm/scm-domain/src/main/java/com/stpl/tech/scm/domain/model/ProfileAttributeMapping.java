/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 03:52:20 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProfileAttributeMapping", propOrder = {
        "profileAttributeMappingId",
        "profileId",
        "attributeId",
        "creationDate",
        "createdBy",
        "definedAtProduct",
        "definedAtSKU",
        "definedAtAsset",
        "mandatoryAtProduct",
        "mandatoryAtSKU",
        "mandatoryAtAsset",
        "overridableAtProduct",
        "overridableAtSKU",
        "participateInName",
        "updationDate",
        "updatedBy",
        "status"
})
public class ProfileAttributeMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer profileAttributeMappingId;
    @XmlElement(required = true)
    protected Integer profileId;
    @XmlElement(required = true)
    protected Integer attributeId;
    @XmlElement(required = false)
    protected String attributeName;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date creationDate;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true)
    protected boolean definedAtProduct;
    @XmlElement(required = true)
    protected boolean definedAtSKU;
    @XmlElement(required = true)
    protected boolean definedAtAsset;
    @XmlElement(required = true)
    protected boolean mandatoryAtProduct;
    @XmlElement(required = true)
    protected boolean mandatoryAtSKU;
    @XmlElement(required = true)
    protected boolean mandatoryAtAsset;
    @XmlElement(required = true)
    protected boolean overridableAtProduct;
    @XmlElement(required = true)
    protected boolean overridableAtSKU;
    @XmlElement(required = true)
    protected boolean participateInName;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updationDate;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true)
    protected String associatedImage;
    protected boolean standAlone;


    public Integer getProfileAttributeMappingId() {
        return profileAttributeMappingId;
    }

    public void setProfileAttributeMappingId(Integer profileAttributeMappingId) {
        this.profileAttributeMappingId = profileAttributeMappingId;
    }

    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    public Integer getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(IdCodeName createdBy) {
        this.createdBy = createdBy;
    }

    public boolean isDefinedAtProduct() {
        return definedAtProduct;
    }

    public void setDefinedAtProduct(boolean definedAtProduct) {
        this.definedAtProduct = definedAtProduct;
    }

    public boolean isDefinedAtSKU() {
        return definedAtSKU;
    }

    public void setDefinedAtSKU(boolean definedAtSKU) {
        this.definedAtSKU = definedAtSKU;
    }

    public boolean isDefinedAtAsset() {
        return definedAtAsset;
    }

    public void setDefinedAtAsset(boolean definedAtAsset) {
        this.definedAtAsset = definedAtAsset;
    }

    public boolean isMandatoryAtProduct() {
        return mandatoryAtProduct;
    }

    public void setMandatoryAtProduct(boolean mandatoryAtProduct) {
        this.mandatoryAtProduct = mandatoryAtProduct;
    }

    public boolean isMandatoryAtSKU() {
        return mandatoryAtSKU;
    }

    public void setMandatoryAtSKU(boolean mandatoryAtSKU) {
        this.mandatoryAtSKU = mandatoryAtSKU;
    }

    public boolean isMandatoryAtAsset() {
        return mandatoryAtAsset;
    }

    public void setMandatoryAtAsset(boolean mandatoryAtAsset) {
        this.mandatoryAtAsset = mandatoryAtAsset;
    }

    public boolean isOverridableAtProduct() {
        return overridableAtProduct;
    }

    public void setOverridableAtProduct(boolean overridableAtProduct) {
        this.overridableAtProduct = overridableAtProduct;
    }

    public boolean isOverridableAtSKU() {
        return overridableAtSKU;
    }

    public void setOverridableAtSKU(boolean overridableAtSKU) {
        this.overridableAtSKU = overridableAtSKU;
    }

    public Date getUpdationDate() {
        return updationDate;
    }

    public void setUpdationDate(Date updationDate) {
        this.updationDate = updationDate;
    }

    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(IdCodeName updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public boolean isParticipateInName() {
        return participateInName;
    }

    public void setParticipateInName(boolean participateInName) {
        this.participateInName = participateInName;
    }

    public String getAssociatedImage() {
        return associatedImage;
    }

    public void setAssociatedImage(String associatedImage) {
        this.associatedImage = associatedImage;
    }

    public String getAttributeName() {
        return attributeName;
    }

    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    public boolean isStandAlone() {
        return standAlone;
    }

    public void setStandAlone(boolean standAlone) {
        this.standAlone = standAlone;
    }
}
