//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.07.03 at 02:06:35 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SalesPerformaType.
 * <p>
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="SalesPerformaType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="RETURN_TO_VENDOR"/&gt;
 *     &lt;enumeration value="B2B_SALES"/&gt;
 *     &lt;enumeration value="SCRAP"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 */
@XmlType(name = "SalesPerformaType")
@XmlEnum
public enum SalesPerformaType {

    RETURN_TO_VENDOR("RETURN_TO_VENDOR"),
    @XmlEnumValue("B2B_SALES")
    B2B_SALES("B2B_SALES"),
    B2B_RETURN("B2B_RETURN"),
    SCRAP("SCRAP"),
    ECOM("ECOM");
    private final String value;

    SalesPerformaType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static SalesPerformaType fromValue(String v) {
        for (SalesPerformaType c : SalesPerformaType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
