//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.06.08 at 03:11:36 PM IST 
//

package com.stpl.tech.scm.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.domain.model.Adapter4;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

/**
 * <p>
 * Java class for ProductionBooking complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="ProductionBooking"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="bookingId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="skuId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitPrice" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalCost" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cancellationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="closureTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="cancelledBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="bookingStatus" type="{http://www.w3schools.com}BookingStatus"/&gt;
 *         &lt;element name="bookingConsumption" type="{http://www.w3schools.com}BookingConsumption" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductionBooking", propOrder = { "bookingId", "productId", "productName", "skuId", "unitOfMeasure",
		"quantity", "unitId", "unitPrice", "totalCost", "generationTime", "cancellationTime", "closureTime",
		"generatedBy", "cancelledBy", "bookingStatus", "bookingConsumption" })
public class ReverseProductionBooking extends AbstractInventoryVO implements ConsumptionVO, ReceivingVO {

	protected int bookingId;
	protected int productId;
	@XmlElement(required = true)
	protected String productName;
	protected int skuId;
	@XmlElement(required = true)
	protected String unitOfMeasure;
	@XmlElement(required = true)
	protected BigDecimal quantity;
	protected int unitId;
	@XmlElement(required = true)
	@XmlJavaTypeAdapter(Adapter4.class)
	protected BigDecimal unitPrice;
	@XmlElement(required = true)
	@XmlJavaTypeAdapter(Adapter4.class)
	protected BigDecimal totalCost;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date generationTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date cancellationTime;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date closureTime;
	@XmlElement(required = true)
	protected IdCodeName generatedBy;
	@XmlElement(required = true, nillable = true)
	protected IdCodeName cancelledBy;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	protected BookingStatus bookingStatus;
	protected List<ReverseBookingConsumption> bookingConsumption = new ArrayList<>();
	@XmlElement(required = false, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date expiryDate;
	@XmlElement(required = false, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date currentDate;
	protected Set<Date> availableDates = new TreeSet<>();
	protected String profile;
	protected Integer vendorId;
	protected Integer dispatchId;
	protected String autoBooking;
	protected boolean reverseBooking;

	/**
	 * Gets the value of the bookingId property.
	 * 
	 */
	public int getBookingId() {
		return bookingId;
	}

	/**
	 * Sets the value of the bookingId property.
	 * 
	 */
	public void setBookingId(int value) {
		this.bookingId = value;
	}

	/**
	 * Gets the value of the productId property.
	 * 
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * Sets the value of the productId property.
	 * 
	 */
	public void setProductId(int value) {
		this.productId = value;
	}

	/**
	 * Gets the value of the productName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * Sets the value of the productName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setProductName(String value) {
		this.productName = value;
	}

	/**
	 * Gets the value of the skuId property.
	 * 
	 */
	public int getSkuId() {
		return skuId;
	}

	/**
	 * Sets the value of the skuId property.
	 * 
	 */
	public void setSkuId(int value) {
		this.skuId = value;
	}

	/**
	 * Gets the value of the unitOfMeasure property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	/**
	 * Sets the value of the unitOfMeasure property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setUnitOfMeasure(String value) {
		this.unitOfMeasure = value;
	}

	/**
	 * Gets the value of the quantity property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getQuantity() {
		return quantity;
	}

	/**
	 * Sets the value of the quantity property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setQuantity(BigDecimal value) {
		this.quantity = value;
	}

	/**
	 * Gets the value of the unitId property.
	 * 
	 */
	public int getUnitId() {
		return unitId;
	}

	/**
	 * Sets the value of the unitId property.
	 * 
	 */
	public void setUnitId(int value) {
		this.unitId = value;
	}

	/**
	 * Gets the value of the unitPrice property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	/**
	 * Sets the value of the unitPrice property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setUnitPrice(BigDecimal value) {
		this.unitPrice = value;
	}

	/**
	 * Gets the value of the totalCost property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	/**
	 * Sets the value of the totalCost property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setTotalCost(BigDecimal value) {
		this.totalCost = value;
	}

	/**
	 * Gets the value of the generationTime property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getGenerationTime() {
		return generationTime;
	}

	/**
	 * Sets the value of the generationTime property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setGenerationTime(Date value) {
		this.generationTime = value;
	}

	/**
	 * Gets the value of the cancellationTime property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getCancellationTime() {
		return cancellationTime;
	}

	/**
	 * Sets the value of the cancellationTime property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCancellationTime(Date value) {
		this.cancellationTime = value;
	}

	/**
	 * Gets the value of the closureTime property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public Date getClosureTime() {
		return closureTime;
	}

	/**
	 * Sets the value of the closureTime property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setClosureTime(Date value) {
		this.closureTime = value;
	}

	/**
	 * Gets the value of the generatedBy property.
	 * 
	 * @return possible object is {@link IdCodeName }
	 * 
	 */
	public IdCodeName getGeneratedBy() {
		return generatedBy;
	}

	/**
	 * Sets the value of the generatedBy property.
	 * 
	 * @param value
	 *            allowed object is {@link IdCodeName }
	 * 
	 */
	public void setGeneratedBy(IdCodeName value) {
		this.generatedBy = value;
	}

	/**
	 * Gets the value of the cancelledBy property.
	 * 
	 * @return possible object is {@link IdCodeName }
	 * 
	 */
	public IdCodeName getCancelledBy() {
		return cancelledBy;
	}

	/**
	 * Sets the value of the cancelledBy property.
	 * 
	 * @param value
	 *            allowed object is {@link IdCodeName }
	 * 
	 */
	public void setCancelledBy(IdCodeName value) {
		this.cancelledBy = value;
	}

	/**
	 * Gets the value of the bookingStatus property.
	 * 
	 * @return possible object is {@link BookingStatus }
	 * 
	 */
	public BookingStatus getBookingStatus() {
		return bookingStatus;
	}

	/**
	 * Sets the value of the bookingStatus property.
	 * 
	 * @param value
	 *            allowed object is {@link BookingStatus }
	 * 
	 */
	public void setBookingStatus(BookingStatus value) {
		this.bookingStatus = value;
	}

	/**
	 * Gets the value of the bookingConsumption property.
	 * 
	 * <p>
	 * This accessor method returns a reference to the live list, not a snapshot.
	 * Therefore any modification you make to the returned list will be present
	 * inside the JAXB object. This is why there is not a <CODE>set</CODE> method
	 * for the bookingConsumption property.
	 * 
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getConsumption().add(newItem);
	 * </pre>
	 * 
	 * 
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link BookingConsumption }
	 * 
	 * 
	 */
	public List<ReverseBookingConsumption> getBookingConsumption() {
		if (bookingConsumption == null) {
			bookingConsumption = new ArrayList<>();
		}
		return this.bookingConsumption;
	}

	@Override
	@JsonIgnore
	public int getKeyId() {
		return bookingId;
	}

	@Override
	@JsonIgnore
	public StockEventType getKeyType() {
		return StockEventType.BOOKING_CONSUMPTION;
	}

	@Override
	@JsonIgnore
	public PriceUpdateEntryType getInventoryType() {
		return PriceUpdateEntryType.SKU;
	}

	@Override
	@JsonIgnore
	public List<InventoryItemVO> getInventoryItems() {
		return new ArrayList<>(this.bookingConsumption);
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public Date getCurrentDate() {
		return currentDate;
	}

	public void setCurrentDate(Date currentDate) {
		this.currentDate = currentDate;
	}

	public Set<Date> getAvailableDates() {
		if (availableDates == null) {
			availableDates = new TreeSet<Date>();
		}
		return availableDates;
	}

	public void setAvailableDates(TreeSet<Date> availableDates) {
		this.availableDates = availableDates;
	}

	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

	public Integer getDispatchId() {
		return dispatchId;
	}

	public void setDispatchId(Integer dispatchId) {
		this.dispatchId = dispatchId;
	}

	@Override
	public boolean isAssetOrder() {
		return false;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	public String getAutoBooking() {
		return autoBooking;
	}

	public void setAutoBooking(String autoBooking) {
		this.autoBooking = autoBooking;
	}

	public boolean isReverseBooking() {
		return reverseBooking;
	}

	public void setReverseBooking(boolean reverseBooking) {
		this.reverseBooking = reverseBooking;
	}

	public void setBookingConsumption(List<ReverseBookingConsumption> bookingConsumption) {
		this.bookingConsumption = bookingConsumption;
	}
}
