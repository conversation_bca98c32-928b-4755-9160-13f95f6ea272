//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.09.11 at 11:20:21 AM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PaymentCycle.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="PaymentCycle"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="DAILY"/&gt;
 *     &lt;enumeration value="WEEKLY"/&gt;
 *     &lt;enumeration value="FORTNIGHTLY"/&gt;
 *     &lt;enumeration value="MONTHLY"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "PaymentCycle")
@XmlEnum
public enum PaymentCycle {

    DAILY,
    WEEKLY,
    FORTNIGHTLY,
    MONTHLY;

    public String value() {
        return name();
    }

    public static PaymentCycle fromValue(String v) {
        return valueOf(v);
    }

}
