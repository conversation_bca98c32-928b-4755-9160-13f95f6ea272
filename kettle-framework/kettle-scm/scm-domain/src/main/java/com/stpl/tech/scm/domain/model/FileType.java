//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.20 at 07:12:44 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for FileType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="FileType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="PAN"/&gt;
 *     &lt;enumeration value="CIN"/&gt;
 *     &lt;enumeration value="SERVICE_TAX"/&gt;
 *     &lt;enumeration value="CST"/&gt;
 *     &lt;enumeration value="VAT"/&gt;
 *     &lt;enumeration value="CHEQUE"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "FileType")
@XmlEnum
public enum FileType {

    ARC,
    PAN,
    CIN,
    SERVICE_TAX,
    CST,
    VAT,
    OTHERS,
    CHEQUE,
    SALES_INVOICE,
    GSTIN,
    TDS,
    ASSET_IMAGE,
    CONTRACT,
    MSME,
    CREDIT_NOTE;

    public String value() {
        return name();
    }

    public static FileType fromValue(String v) {
        return valueOf(v);
    }

}
