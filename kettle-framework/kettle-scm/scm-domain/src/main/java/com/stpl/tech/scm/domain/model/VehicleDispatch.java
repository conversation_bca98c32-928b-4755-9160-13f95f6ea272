package com.stpl.tech.scm.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VehicleDispatch {

	private Integer dispatchId;
	private Vehicle vehicle;
	private Date dispatchDate;
	private DispatchStatus status;
	private List<Consignment> consignmentList;
    private String transporterGstin;
    private String docketNumber;
	private String vehicleNumber;

	private SCMOrderStatus eInvoiceDone;

    public Integer getDispatchId() {
		return dispatchId;
	}

	public void setDispatchId(Integer dispatchId) {
		this.dispatchId = dispatchId;
	}

	public Vehicle getVehicle() {
		return vehicle;
	}

	public void setVehicle(Vehicle vehicle) {
		this.vehicle = vehicle;
	}

	public Date getDispatchDate() {
		return dispatchDate;
	}

	public void setDispatchDate(Date dispatchDate) {
		this.dispatchDate = dispatchDate;
	}

	public DispatchStatus getStatus() {
		return status;
	}

	public void setStatus(DispatchStatus status) {
		this.status = status;
	}

	public List<Consignment> getConsignmentList() {
		if (consignmentList == null) {
			consignmentList = new ArrayList<>();
		}
		return consignmentList;
	}

	public void setConsignmentList(List<Consignment> consignmentList) {
		this.consignmentList = consignmentList;
	}

    public void setTransporterGstin(String transporterGstin) {
        this.transporterGstin = transporterGstin;
    }

    public String getTransporterGstin() {
        return transporterGstin;
    }

    public void setDocketNumber(String docketNumber) {
        this.docketNumber = docketNumber;
    }

    public String getDocketNumber() {
        return docketNumber;
    }

	public String getVehicleNumber() {
		return vehicleNumber;
	}

	public void setVehicleNumber(String vehicleNumber) {
		this.vehicleNumber = vehicleNumber;
	}

	public SCMOrderStatus geteInvoiceDone() {
		return eInvoiceDone;
	}

	public void seteInvoiceDone(SCMOrderStatus eInvoiceDone) {
		this.eInvoiceDone = eInvoiceDone;
	}
}
