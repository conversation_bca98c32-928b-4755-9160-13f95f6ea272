package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class GatepassItemDrilldown{
	private Integer id;
	private GatepassItem gatepassItem;
	private BigDecimal quantity;
	private BigDecimal price;
	private Date addTime;
	private Date expiryDate;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public GatepassItem getGatepassItem() {
		return gatepassItem;
	}

	public void setGatepassItem(GatepassItem gatepassItem) {
		this.gatepassItem = gatepassItem;
	}

	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}
	
}
