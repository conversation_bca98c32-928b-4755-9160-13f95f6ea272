package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;

public class RejectedGrItem {
    private Integer grId;
    private Integer originalGrId;
    private Integer toId;
    private Integer roId;
    private Integer grItemId;
    private Integer originalGrItemId;
    private Integer toItemId;
    private Integer roItemId;
    private BigDecimal rejectedQuantity;
    private String rejectedBy;
    private Integer rejectedUnitId;
    private Integer skuId;
    private String skuName;

    public Integer getGrId() {
        return grId;
    }

    public void setGrId(Integer grId) {
        this.grId = grId;
    }

    public Integer getOriginalGrId() {
        return originalGrId;
    }

    public void setOriginalGrId(Integer originalGrId) {
        this.originalGrId = originalGrId;
    }

    public Integer getToId() {
        return toId;
    }

    public void setToId(Integer toId) {
        this.toId = toId;
    }

    public Integer getRoId() {
        return roId;
    }

    public void setRoId(Integer roId) {
        this.roId = roId;
    }

    public Integer getGrItemId() {
        return grItemId;
    }

    public void setGrItemId(Integer grItemId) {
        this.grItemId = grItemId;
    }

    public Integer getOriginalGrItemId() {
        return originalGrItemId;
    }

    public void setOriginalGrItemId(Integer originalGrItemId) {
        this.originalGrItemId = originalGrItemId;
    }

    public Integer getToItemId() {
        return toItemId;
    }

    public void setToItemId(Integer toItemId) {
        this.toItemId = toItemId;
    }

    public Integer getRoItemId() {
        return roItemId;
    }

    public void setRoItemId(Integer roItemId) {
        this.roItemId = roItemId;
    }

    public BigDecimal getRejectedQuantity() {
        return rejectedQuantity;
    }

    public void setRejectedQuantity(BigDecimal rejectedQuantity) {
        this.rejectedQuantity = rejectedQuantity;
    }

    public String getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(String rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    public Integer getRejectedUnitId() {
        return rejectedUnitId;
    }

    public void setRejectedUnitId(Integer rejectedUnitId) {
        this.rejectedUnitId = rejectedUnitId;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }
}
