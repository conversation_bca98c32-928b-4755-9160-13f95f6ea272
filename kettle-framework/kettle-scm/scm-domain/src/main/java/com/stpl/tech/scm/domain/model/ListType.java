package com.stpl.tech.scm.domain.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListType", propOrder = { "listTypeId", "type", "code", "name", "description", "status", "alias",
		"listData", "listDetail","budgetCategory" })
public class ListType {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer listTypeId;

	@XmlElement(required = true)
	protected String type;

	@XmlElement(required = true)
	protected String code;

	@XmlElement(required = true)
	protected String name;

	@XmlElement(required = true)
	protected String description;

	@XmlElement(required = true)
	protected String status;

	@XmlElement(required = true)
	protected String alias;
	
	@XmlElement(required = true)
    @XmlSchemaType(name = "string")
	protected List<ListData> listData;
	
	@XmlElement(required = true)
    @XmlSchemaType(name = "string")
	protected ListDetail listDetail;

	@XmlElement(required = true)
	protected  String budgetCategory;


	public Integer getListTypeId() {
		return listTypeId;
	}

	public void setListTypeId(Integer listTypeId) {
		this.listTypeId = listTypeId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public List<ListData> getListData() {
		return listData;
	}

	public void setListData(List<ListData> listData) {
		this.listData = listData;
	}

	public ListDetail getListDetail() {
		return listDetail;
	}

	public void setListDetail(ListDetail listDetail) {
		this.listDetail = listDetail;
	}

	public String getBudgetCategory() {
		return budgetCategory;
	}

	public void setBudgetCategory(String budgetCategory) {
		this.budgetCategory = budgetCategory;
	}
}
