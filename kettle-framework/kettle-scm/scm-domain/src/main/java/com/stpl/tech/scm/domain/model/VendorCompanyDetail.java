//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.04.20 at 07:12:44 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;


import java.util.Date;


/**
 * <p>Java class for VendorCompanyDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="VendorCompanyDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="companyId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="vendorDetail" type="{http://www.w3schools.com}VendorDetail"/&gt;
 *         &lt;element name="companyName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="registeredName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="cin" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pan" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}VendorStatus"/&gt;
 *         &lt;element name="companyAddress" type="{http://www.w3schools.com}AddressDetail"/&gt;
 *         &lt;element name="updateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="updatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "VendorCompanyDetail", propOrder = {
    "companyId",
    "vendorDetail",
    "companyName",
    "registeredName",
    "cin",
    "pan",
    "status",
    "companyAddress",
    "updateTime",
    "updatedBy",
    "exemptSupplier"
})
public class VendorCompanyDetail {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer companyId;
    @XmlElement(required = true)
    protected IdCodeName vendorDetail;
    @XmlElement(required = true)
    protected String companyName;
    @XmlElement(required = true)
    protected String registeredName;
    @XmlElement(required = true)
    protected String cin;
    @XmlElement(required = true)
    protected String pan;
    @XmlElement(required = true)
    protected AddressDetail companyAddress;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date updateTime;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    protected String arc;
    protected String cst;
    protected BusinessType businessType;
    protected ConstitutionalEntityType entityType;
    protected DocumentDetail panDocument;
    protected DocumentDetail arcDocument;
    protected DocumentDetail cinDocument;
    protected DocumentDetail cstDocument;
    protected DocumentDetail serviceTaxDocument;
    protected DocumentDetail vatDocument;
    protected DocumentDetail msmeDocument;
    protected Date msmeExpirationDate;
    private int creditDays;
    private boolean exemptSupplier;
    private boolean msmeRegistered;

    /**
     * Gets the value of the companyId property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * Sets the value of the companyId property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setCompanyId(Integer value) {
        this.companyId = value;
    }

    /**
     * Gets the value of the vendorDetail property.
     * 
     * @return
     *     possible object is
     *     {@link VendorDetail }
     *     
     */
    public IdCodeName getVendorDetail() {
        return vendorDetail;
    }

    /**
     * Sets the value of the vendorDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link VendorDetail }
     *     
     */
    public void setVendorDetail(IdCodeName value) {
        this.vendorDetail = value;
    }

    /**
     * Gets the value of the companyName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     * Sets the value of the companyName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCompanyName(String value) {
        this.companyName = value;
    }

    /**
     * Gets the value of the registeredName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRegisteredName() {
        return registeredName;
    }

    /**
     * Sets the value of the registeredName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRegisteredName(String value) {
        this.registeredName = value;
    }

    /**
     * Gets the value of the cin property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCin() {
        return cin;
    }

    /**
     * Sets the value of the cin property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCin(String value) {
        this.cin = value;
    }

    /**
     * Gets the value of the pan property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPan() {
        return pan;
    }

    /**
     * Sets the value of the pan property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPan(String value) {
        this.pan = value;
    }

    /**
     * Gets the value of the companyAddress property.
     * 
     * @return
     *     possible object is
     *     {@link AddressDetail }
     *     
     */
    public AddressDetail getCompanyAddress() {
        return companyAddress;
    }

    /**
     * Sets the value of the companyAddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link AddressDetail }
     *     
     */
    public void setCompanyAddress(AddressDetail value) {
        this.companyAddress = value;
    }

    /**
     * Gets the value of the updateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets the value of the updateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateTime(Date value) {
        this.updateTime = value;
    }

    /**
     * Gets the value of the updatedBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getUpdatedBy() {
        return updatedBy;
    }

    /**
     * Sets the value of the updatedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setUpdatedBy(IdCodeName value) {
        this.updatedBy = value;
    }


    public String getArc() {
        return arc;
    }

    public void setArc(String arc) {
        this.arc = arc;
    }

    public String getCst() {
        return cst;
    }

    public void setCst(String cst) {
        this.cst = cst;
    }

    public BusinessType getBusinessType() {
        return businessType;
    }

    public void setBusinessType(BusinessType businessType) {
        this.businessType = businessType;
    }

    public ConstitutionalEntityType getEntityType() {
        return entityType;
    }

    public void setEntityType(ConstitutionalEntityType entityType) {
        this.entityType = entityType;
    }

    public DocumentDetail getPanDocument() {
        return panDocument;
    }

    public void setPanDocument(DocumentDetail panDocument) {
        this.panDocument = panDocument;
    }

    public DocumentDetail getArcDocument() {
        return arcDocument;
    }

    public void setArcDocument(DocumentDetail arcDocument) {
        this.arcDocument = arcDocument;
    }

    public DocumentDetail getCinDocument() {
        return cinDocument;
    }

    public void setCinDocument(DocumentDetail cinDocument) {
        this.cinDocument = cinDocument;
    }

    public DocumentDetail getCstDocument() {
        return cstDocument;
    }

    public void setCstDocument(DocumentDetail cstDocument) {
        this.cstDocument = cstDocument;
    }

    public DocumentDetail getServiceTaxDocument() {
        return serviceTaxDocument;
    }

    public void setServiceTaxDocument(DocumentDetail serviceTaxDocument) {
        this.serviceTaxDocument = serviceTaxDocument;
    }

    public DocumentDetail getVatDocument() {
        return vatDocument;
    }

    public void setVatDocument(DocumentDetail vatDocument) {
        this.vatDocument = vatDocument;
    }

    public void setCreditDays(int creditDays) {
        this.creditDays = creditDays;
    }

    public int getCreditDays() {
        return creditDays;
    }

    public boolean isExemptSupplier() {
        return exemptSupplier;
    }

    public void setExemptSupplier(boolean exemptSupplier) {
        this.exemptSupplier = exemptSupplier;
    }

	public DocumentDetail getMsmeDocument() {
		return msmeDocument;
	}

	public void setMsmeDocument(DocumentDetail msmeDocument) {
		this.msmeDocument = msmeDocument;
	}

	public boolean isMsmeRegistered() {
		return msmeRegistered;
	}

	public void setMsmeRegistered(boolean msmeRegistered) {
		this.msmeRegistered = msmeRegistered;
	}

    public Date getMsmeExpirationDate() {
        return msmeExpirationDate;
    }

    public void setMsmeExpirationDate(Date msmeExpirationDate) {
        this.msmeExpirationDate = msmeExpirationDate;
    }
}
