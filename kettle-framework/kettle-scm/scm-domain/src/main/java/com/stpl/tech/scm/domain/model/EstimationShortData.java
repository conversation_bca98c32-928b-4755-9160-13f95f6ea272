package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class EstimationShortData {

    private Integer productId; // SKU_ID
    private String category; // L2_CATEGORY
    private Integer categoryId; // cache
    private String dimension; // SKU_ID
    private Integer quantity; // Predicted_final
    private Integer unitId; // cost_center
    private BigDecimal sales; // value
    private String date;
    private BigDecimal originalQuantity;

    public EstimationShortData() {
    }

    public EstimationShortData(Integer productId, String dimension, Integer categoryId, String category, Integer quantity, Integer unitId, BigDecimal sales) {
        this.productId = productId;
        this.dimension = dimension;
        this.categoryId = categoryId;
        this.category = category;
        this.quantity = quantity;
        this.unitId= unitId;
        this.sales=sales;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public BigDecimal getSales() {
        return sales;
    }

    public void setSales(BigDecimal sales) {
        this.sales = sales;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public BigDecimal getOriginalQuantity() {
        return originalQuantity;
    }

    public void setOriginalQuantity(BigDecimal originalQuantity) {
        this.originalQuantity = originalQuantity;
    }
}
