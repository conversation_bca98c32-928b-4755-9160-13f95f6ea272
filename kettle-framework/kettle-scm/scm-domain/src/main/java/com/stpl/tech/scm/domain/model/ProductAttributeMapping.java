package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.*;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;
import java.util.List;

/**
 * <p>Java class for RequestOrder complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="RequestOrder"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="attributeList" type="{http://www.w3schools.com}AttributeProperties" maxOccurs="unbounded" minOccurs="0"/&gt;
 *         &lt;element name="generationTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="lastUpdatedBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductAttributeMapping", propOrder = {
        "productId",
        "attributeList",
        "generationTime",
        "lastUpdateTime",
        "generatedBy",
        "lastUpdatedBy"
})
public class ProductAttributeMapping {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer productId;

    protected List<AttributeProperties> attributeList;

    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date generationTime;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;


    @XmlElement(required = true)
    protected IdCodeName generatedBy;
    @XmlElement(required = true, nillable = true)
    protected IdCodeName lastUpdatedBy;

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public List<AttributeProperties> getAttributeList() {
        return attributeList;
    }

    public void setAttributeList(List<AttributeProperties> attributeList) {
        this.attributeList = attributeList;
    }

    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    public IdCodeName getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(IdCodeName generatedBy) {
        this.generatedBy = generatedBy;
    }

    public IdCodeName getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(IdCodeName lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }
}
