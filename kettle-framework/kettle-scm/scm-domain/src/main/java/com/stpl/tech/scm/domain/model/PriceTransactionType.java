//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.08.19 at 02:51:21 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PriceTransactionType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="PriceTransactionType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="MANUAL_ADDITION"/&gt;
 *     &lt;enumeration value="MANUAL_OVERRIDE"/&gt;
 *     &lt;enumeration value="RECEIVING"/&gt;
 *     &lt;enumeration value="CONSUMPTION"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "PriceTransactionType")
@XmlEnum
public enum PriceTransactionType {

    INITIAL_LOAD,
    MANUAL_ADDITION,
    MANUAL_OVERRIDE,
    STOCK_CORRECTION,
    RECEIVING,
    CONSUMPTION;

    public String value() {
        return name();
    }

    public static PriceTransactionType fromValue(String v) {
        return valueOf(v);
    }

}
