package com.stpl.tech.scm.domain.model;

import com.stpl.tech.master.domain.model.Address;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LostAssetEmailObject {

    private String assetName;
    private Integer assetId;
    private String assetTag;
    private BigDecimal procurementCost;
    private BigDecimal expectedRecoveryAmount;
    private Integer poId;
    private Integer grId;
    private Integer prId;
    private String invoiceId;
    private Date creationDate;
    private Date stockTakeTime;
    private String stockTakingUser;
    private Integer unitId;
    private String unitName;
    private Address unitAddress;
    private String areaManager;
    private String deputyAreaManager;
    private String lossApprovedBy;
    private Integer eventId;
    private Date lastStockTakeTime;
    private String lastStockTakingUnit;
}
