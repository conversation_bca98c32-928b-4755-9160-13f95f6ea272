package com.stpl.tech.scm.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;

public class GatepassItem implements InventoryItemVO {

	private Integer id;
	private Gatepass gatepass;
	private IdCodeName sku;
	private BigDecimal price;
	private String uom;
	private GatepassTransType transType;
	private BigDecimal quantity;
	private BigDecimal amount;
	private BigDecimal tax;
	private BigDecimal cost;
	private Integer closureId;
	private Date expiryDate;
	private List<GatepassTax> taxes = new ArrayList<GatepassTax>();
	private List<GatepassItemDrilldown> itemDrillDowns = new ArrayList<GatepassItemDrilldown>();
	private List<InventoryItemDrilldown> drillDowns;
	private String reason;
	private IdCodeName createdBy;
	private Date createdAt;
	private List<GatepassItemAssetMapping> gatepassItemAssetMappings;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Gatepass getGatepass() {
		return gatepass;
	}

	public void setGatepass(Gatepass gatepass) {
		this.gatepass = gatepass;
	}

	public IdCodeName getSku() {
		return sku;
	}

	public void setSku(IdCodeName sku) {
		this.sku = sku;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	public GatepassTransType getTransType() {
		return transType;
	}

	public void setTransType(GatepassTransType transType) {
		this.transType = transType;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public BigDecimal getCost() {
		return cost;
	}

	public void setCost(BigDecimal cost) {
		this.cost = cost;
	}

	public Integer getClosureId() {
		return closureId;
	}

	public void setClosureId(Integer closureId) {
		this.closureId = closureId;
	}

	public List<GatepassTax> getTaxes() {
		return taxes;
	}

	public void setTaxes(List<GatepassTax> taxes) {
		this.taxes = taxes;
	}

	public List<GatepassItemDrilldown> getItemDrillDowns() {
		return itemDrillDowns;
	}

	public void setItemDrillDowns(List<GatepassItemDrilldown> itemDrillDowns) {
		this.itemDrillDowns = itemDrillDowns;
	}

	@Override
	@JsonIgnore
	public int getKeyId() {
		return this.sku.getId();
	}

	@Override
	@JsonIgnore
	public PriceUpdateEntryType getKeyType() {
		return PriceUpdateEntryType.SKU;
	}

	@Override
	public void setKeyType(PriceUpdateEntryType type) {
		// TODO Auto-generated method stub

	}

	@Override
	@JsonIgnore
	public int getItemKeyId() {
		return this.id;
	}

	@Override
	public StockEventType getItemKeyType() {
		switch (this.transType) {
		case RETURN:
			return StockEventType.RECEIVED;
		case TRANSFER:
			return StockEventType.TRANSFER_OUT;
		case LOST:
			return StockEventType.WASTAGE;
		}
		return StockEventType.TRANSFER_OUT;
	}

	@Override
	public List<InventoryItemDrilldown> getDrillDowns() {
		if (drillDowns == null) {
			drillDowns = new ArrayList<>();
		}
		return drillDowns;
	}

	@Override
	public Date getExpiryDate() {
		return this.expiryDate;
	}

	@Override
	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	@Override
	public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
		this.drillDowns = drillDowns;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public IdCodeName getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(IdCodeName createdBy) {
		this.createdBy = createdBy;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public List<GatepassItemAssetMapping> getGatepassItemAssetMappings() {
		return gatepassItemAssetMappings;
	}

	public void setGatepassItemAssetMappings(List<GatepassItemAssetMapping> gatepassItemAssetMappings) {
		this.gatepassItemAssetMappings = gatepassItemAssetMappings;
	}
}
