//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.12 at 01:35:23 PM IST 
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

/*
    Created By : A<PERSON><PERSON><PERSON><PERSON>
 */

@XmlType(name = "StockTakeSubType")
@XmlEnum
public enum StockTakeSubType {

    REGULAR(true),
    NSO(true),
    RENOVATION(true),
    MANAGER_HANDOVER(true),
    AUDIT(true),
    DAILY_DAYCLOSE (false),
    WEEKLY_DAYCLOSE (false),
    MONTHLY_DAYCLOSE (false);


    private final boolean isAuditorCompulsory;

	private StockTakeSubType(boolean isAuditorCompulsory) {
		this.isAuditorCompulsory = isAuditorCompulsory;
	}

	public String value() {
        return name();
    }

    public static StockTakeSubType fromValue(String v) {
        return valueOf(v);
    }

    public boolean isAuditorCompulsory() {
        return isAuditorCompulsory;
    }
}
