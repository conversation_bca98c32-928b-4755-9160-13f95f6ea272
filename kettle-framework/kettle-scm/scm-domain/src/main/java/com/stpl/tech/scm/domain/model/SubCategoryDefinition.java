//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.06.12 at 11:22:51 AM IST
//


package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SubCategoryDefinition complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="SubCategoryDefinition"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="subCategoryId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="subCategoryName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subCategoryCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subCategoryDescription" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="subCategoryStatus" type="{http://www.w3schools.com}SwitchStatus"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SubCategoryDefinition", propOrder = {
    "subCategoryId",
    "subCategoryName",
    "subCategoryCode",
    "subCategoryDescription",
    "subCategoryStatus"
})
public class SubCategoryDefinition {

    @XmlElement(required = true, type = Integer.class, nillable = true)
    protected Integer subCategoryId;
    @XmlElement(required = true)
    protected String subCategoryName;
    @XmlElement(required = true)
    protected String subCategoryCode;
    @XmlElement(required = true)
    protected String subCategoryDescription;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SwitchStatus subCategoryStatus;
    protected Integer shelfLifeInDays;
    protected String shelfLifeRange;

    /**
     * Gets the value of the subCategoryId property.
     *
     * @return
     *     possible object is
     *     {@link Integer }
     *
     */
    public Integer getSubCategoryId() {
        return subCategoryId;
    }

    /**
     * Sets the value of the subCategoryId property.
     *
     * @param value
     *     allowed object is
     *     {@link Integer }
     *
     */
    public void setSubCategoryId(Integer value) {
        this.subCategoryId = value;
    }

    /**
     * Gets the value of the subCategoryName property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSubCategoryName() {
        return subCategoryName;
    }

    /**
     * Sets the value of the subCategoryName property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSubCategoryName(String value) {
        this.subCategoryName = value;
    }

    /**
     * Gets the value of the subCategoryCode property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSubCategoryCode() {
        return subCategoryCode;
    }

    /**
     * Sets the value of the subCategoryCode property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSubCategoryCode(String value) {
        this.subCategoryCode = value;
    }

    /**
     * Gets the value of the subCategoryDescription property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getSubCategoryDescription() {
        return subCategoryDescription;
    }

    /**
     * Sets the value of the subCategoryDescription property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setSubCategoryDescription(String value) {
        this.subCategoryDescription = value;
    }

    /**
     * Gets the value of the subCategoryStatus property.
     *
     * @return
     *     possible object is
     *     {@link SwitchStatus }
     *
     */
    public SwitchStatus getSubCategoryStatus() {
        return subCategoryStatus;
    }

    /**
     * Sets the value of the subCategoryStatus property.
     *
     * @param value
     *     allowed object is
     *     {@link SwitchStatus }
     *
     */
    public void setSubCategoryStatus(SwitchStatus value) {
        this.subCategoryStatus = value;
    }

    public Integer getShelfLifeInDays() {
        return shelfLifeInDays;
    }

    public void setShelfLifeInDays(Integer shelfLifeInDays) {
        this.shelfLifeInDays = shelfLifeInDays;
    }

    public String getShelfLifeRange() {
        return shelfLifeRange;
    }

    public void setShelfLifeRange(String shelfLifeRange) {
        this.shelfLifeRange = shelfLifeRange;
    }
}
