DROP TABLE IF EXISTS KETTLE_SCM.DOCUMENT_DETAIL_DATA;
CREATE TABLE KETTLE_SCM.DOCUMENT_DETAIL_DATA (
  DOCUMENT_ID INT NOT NULL AUTO_INCREMENT ,
  FILE_TYPE VARCHAR(45) NOT NULL,
  DOCUMENT_LINK VARCHAR(200) NOT NULL,
  UPDATE_TIME DATETIME NOT NULL,
  UPDATED_BY INT NULL,
  DOCUMENT_MIME_TYPE VARCHAR(45) NOT NULL,
  DOCUMENT_UPLOAD_TYPE VARCHAR(45) NOT NULL,
  DOCUMENT_UPLOAD_TYPE_ID INT NULL,
  DOCUMENT_S3_KEY VARCHAR(180) NULL,
  DOCUMENT_S3_BUCKET VARCHAR(180) NULL,
  PRIMARY KEY (DOCUMENT_ID),
  UNIQUE INDEX DOCUMENT_ID_UNIQUE (DOCUMENT_ID ASC)
);


DROP TABLE IF EXISTS KETTLE_SCM.ADDRESS_DETAIL_DATA;
CREATE TABLE KETTLE_SCM.ADDRESS_DETAIL_DATA (
  ADDRESS_ID INT NOT NULL AUTO_INCREMENT,
  LINE_1 VARCHAR(150) NOT NULL,
  LINE_2 VARCHAR(150) NULL,
  CITY VARCHAR(70) NOT NULL,
  STATE VARCHAR(70) NOT NULL,
  COUNTRY VARCHAR(50) NOT NULL,
  LINE_3 VARCHAR(45) NULL,
  ZIPCODE VARCHAR(15) NOT NULL,
  ADDRESS_CONTACT VARCHAR(20) NOT NULL,
  ADDRESS_TYPE VARCHAR(45) NOT NULL,
  PRIMARY KEY (ADDRESS_ID),
  UNIQUE INDEX ADDRESS_ID_UNIQUE (ADDRESS_ID ASC)
);

ALTER TABLE ADDRESS_DETAIL_DATA ADD COLUMN LOCATION_ID VARCHAR(15) NULL;

DROP TABLE IF EXISTS KETTLE_SCM.VENDOR_DETAIL_DATA;
CREATE TABLE KETTLE_SCM.VENDOR_DETAIL_DATA (
  VENDOR_ID INT NOT NULL AUTO_INCREMENT,
  ENTITY_NAME VARCHAR(150) NOT NULL,
  VENDOR_FIRST_NAME VARCHAR(70) NOT NULL,
  VENDOR_LAST_NAME VARCHAR(70) NOT NULL,
  VENDOR_PRIMARY_CONTACT VARCHAR(20) NOT NULL,
  VENDOR_SECONDARY_CONTACT VARCHAR(20) NULL,
  VENDOR_PRIMARY_EMAIL VARCHAR(70) NOT NULL,
  VENDOR_SECONDARY_EMAIL VARCHAR(70) NULL,
  VENDOR_STATUS VARCHAR(45) NOT NULL,
  VENDOR_TYPE VARCHAR(45) NOT NULL,
  VENDOR_REQUESTED_BY INT NOT NULL,
  VENDOR_UPDATED_AT DATETIME NOT NULL,
  VENDOR_ADDRESS_ID INT NOT NULL,
  VENDOR_UPDATED_BY INT NULL,
  PRIMARY KEY (VENDOR_ID) ,
  UNIQUE INDEX VENDOR_ID_UNIQUE (VENDOR_ID ASC),
  INDEX (VENDOR_ADDRESS_ID ASC),
  CONSTRAINT
    FOREIGN KEY (VENDOR_ADDRESS_ID)
    REFERENCES ADDRESS_DETAIL_DATA (ADDRESS_ID)
  );

DROP TABLE IF EXISTS KETTLE_SCM.VENDOR_ACCOUNT_DETAILS;
CREATE TABLE KETTLE_SCM.VENDOR_ACCOUNT_DETAILS (
  ACCOUNT_DETAIL_ID INT NOT NULL AUTO_INCREMENT,
  VENDOR_ID INT NOT NULL,
  ACCOUNT_NUMBER VARCHAR(45) NOT NULL,
  ACCOUNT_CONTACT_EMAIL VARCHAR(45) NOT NULL,
  ACCOUNT_CONTACT_NAME VARCHAR(85) NOT NULL,
  ACCOUNT_CONTACT_NUMBER VARCHAR(25) NOT NULL,
  IFSC_CODE VARCHAR(45) NOT NULL,
  ACCOUNT_TYPE VARCHAR(45) NOT NULL,
  MICR_CODE VARCHAR(45) NOT NULL,
  KIND_OF_ACCOUNT VARCHAR(45) NOT NULL,
  CHEQUE_DOCUMENT INT NULL,
  UPDATED_BY INT NOT NULL,
  UPDATED_AT DATETIME NOT NULL,
  PRIMARY KEY (ACCOUNT_DETAIL_ID) ,
  UNIQUE INDEX ACCOUNT_DETAIL_ID_UNIQUE (ACCOUNT_DETAIL_ID ASC) ,
  INDEX IFBK_ACCOUNT_VENDOR_KEY_IDX (CHEQUE_DOCUMENT ASC) ,
  CONSTRAINT
    FOREIGN KEY (CHEQUE_DOCUMENT)
    REFERENCES DOCUMENT_DETAIL_DATA (DOCUMENT_ID),
  CONSTRAINT
    FOREIGN KEY (VENDOR_ID)
    REFERENCES VENDOR_DETAIL_DATA (VENDOR_ID)
);


DROP TABLE IF EXISTS KETTLE_SCM.VENDOR_DISPATCH_LOCATIONS;
CREATE TABLE KETTLE_SCM.VENDOR_DISPATCH_LOCATIONS (
  DISPATCH_LOCATION_ID INT NOT NULL AUTO_INCREMENT ,
  VENDOR_ID INT NOT NULL ,
  APPLY_TAX VARCHAR(1) NOT NULL ,
  LOCATION_NAME VARCHAR(45) NOT NULL ,
  NOTIFICATION_TYPE VARCHAR(15) NOT NULL ,
  LOCATION_ADDRESS_ID INT NOT NULL ,
  TIN VARCHAR(45) NULL,
  GSTIN VARCHAR(45) NULL,
  UPDATED_BY INT NOT NULL,
  UPDATED_AT DATETIME NOT NULL,
  GST_STATUS VARCHAR (30) NOT NULL,
  PRIMARY KEY (DISPATCH_LOCATION_ID)  ,
  UNIQUE INDEX DISPATCH_LOCATION_ID_UNIQUE (DISPATCH_LOCATION_ID ASC)  ,
  INDEX IFBK_LOCATION_ADDRESS_KEY_IDX (LOCATION_ADDRESS_ID ASC)  ,
  INDEX IFBK_VENDOR_LOCATION_KEY_IDX (VENDOR_ID ASC)  ,
  UNIQUE INDEX TIN_UNIQUE (TIN ASC),
  UNIQUE INDEX GSTIN_UNIQUE (GSTIN ASC),
  CONSTRAINT IFBK_LOCATION_ADDRESS_KEY
    FOREIGN KEY (LOCATION_ADDRESS_ID)
    REFERENCES ADDRESS_DETAIL_DATA (ADDRESS_ID),
  CONSTRAINT IFBK_VENDOR_LOCATION_KEY
    FOREIGN KEY (VENDOR_ID)
    REFERENCES VENDOR_DETAIL_DATA (VENDOR_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM.VENDOR_COMPANY_DETAIL;
CREATE TABLE KETTLE_SCM.VENDOR_COMPANY_DETAIL (
  VENDOR_COMPANY_ID INT(11) NOT NULL AUTO_INCREMENT,
  VENDOR_ID INT(11) NOT NULL,
  REGISTERED_NAME VARCHAR(150) NOT NULL,
  NAME VARCHAR(150) NOT NULL,
  CIN VARCHAR(45) DEFAULT NULL,
  CST VARCHAR(45) DEFAULT NULL,
  VENDOR_COMPANY_ADDRESS_ID INT(11) NOT NULL,
  PAN VARCHAR(45) NOT NULL,
  UPDATED_AT DATETIME NOT NULL,
  UPDATED_BY INT(11) NOT NULL,
  ARC VARCHAR(45) DEFAULT NULL,
  CREDIT_CYCLE INT(11) NOT NULL DEFAULT 0,
  COMPANY_TYPE VARCHAR(45) NOT NULL,
  BUSINESS_TYPE VARCHAR(45) NOT NULL,
  CIN_DOCUMENT INT(11) DEFAULT NULL,
  ARC_DOCUMENT INT(11) DEFAULT NULL,
  CST_DOCUMENT INT(11) DEFAULT NULL,
  PAN_DOCUMENT INT(11) DEFAULT NULL,
  VAT_DOCUMENT INT(11) DEFAULT NULL,
  SERVICE_TAX_DOCUMENT INT(11) DEFAULT NULL,
  PRIMARY KEY (VENDOR_COMPANY_ID),
  UNIQUE KEY VENDOR_COMPANY_ID_UNIQUE (VENDOR_COMPANY_ID),
  UNIQUE KEY PAN_UNIQUE (PAN),
  UNIQUE KEY CIN_UNIQUE (CIN),
  UNIQUE KEY ARC_UNIQUE (ARC),
  KEY IFBK_COMPANY_ADDRESS_KEY_IDX (VENDOR_COMPANY_ADDRESS_ID),
  KEY IFBK_COMPANY_VENDOR_KEY_IDX (VENDOR_ID),
  CONSTRAINT FOREIGN KEY (VENDOR_COMPANY_ADDRESS_ID) REFERENCES ADDRESS_DETAIL_DATA (ADDRESS_ID),
  CONSTRAINT FOREIGN KEY (VENDOR_ID) REFERENCES VENDOR_DETAIL_DATA (VENDOR_ID)
) ENGINE=INNODB DEFAULT CHARSET=UTF8;


ALTER TABLE KETTLE_SCM.VENDOR_COMPANY_DETAIL ADD COLUMN EXEMPT_SUPPLIER VARCHAR (1) NULL;



/**
    Added for mapping Access control to external vendor management after executing
    http://dev.kettle.chaayos.com:9595/master-service/rest/v1/external-partner/add/partner?partnerName=vendor-client
    from POSTMAN client
 **/
 
INSERT INTO KETTLE_MASTER_DEV.EXTERNAL_PARTNER_INFO (EXTERNAL_PARTNER_INFO_ID, PARTNER_NAME, PARTNER_CODE, PASS_CODE, API_KEY, CREATION_DATE, PARTNER_STATUS)
VALUES ('6', 'vendor-client', 'vendor-client', '76709', 'eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6InZlbmRvci1jbGllbnQiLCJlbnZUeXBlIjoiREVWIiwicGFzc0NvZGUiOiI3NjcwOSIsImlhdCI6MTQ5MzEwOTA0OH0.qVE5TCpygSVL4BFtPqhrRtFcuWrWYn4Mg6dQPLK_qrs', '2017-04-25', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ACCESS_CONTROL_LIST_DATA (ACL_ID,ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES ('127','scm-service.vendor-registration-management.*', 'Vendor Management Resource for External Services', 'ACTIVE', 'SCM_SERVICE');

INSERT INTO KETTLE_MASTER_DEV.PARTNER_PERMISSION_MAPPING (PARTNER_ID, PERMISSION, ACL_ID, PPM_STATUS)
VALUES ('6', '1111', '127', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.PRE_AUTHENTICATED_API (API, STATUS)
VALUES ('scm-service.vendor-registration-management.get-auth', 'ACTIVE');



/********** Vendor Detail Data Migration from OLD to NEW schema *************/
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('1', 'Polka', 'Polka', 'Polka', '9215012309', 'NULL', '<EMAIL>', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '1', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('2', 'SANGWAN SALE CORPORATION', 'SANGWAN SALE CORPORATION', 'SANGWAN SALE CORPORATION', '9540018406', '9540018403', '<EMAIL>', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '2', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('3', 'harpal milk agency', 'Harpal milk agency', 'Harpal milk agency', '9953575421', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '3', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('4', 'Amul', 'Amul', 'Amul', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '4', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('5', 'Om Dairy', 'Om Dairy', 'Om Dairy', '9911117510', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '5', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('6', 'Aditya juneja', 'Aditya juneja', 'Aditya juneja', '9891800573', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '6', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('7', 'Neelkanth enterprises', 'Neelkanth enterprises', 'Neelkanth enterprises', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '7', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('8', 'Fresh & Fresh', 'Fresh & Fresh', 'Fresh & Fresh', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '8', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('9', 'Awasthi', 'Awasthi', 'Awasthi', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '9', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('10', 'Naveen Pawar Milk', 'Naveen Pawar Milk', 'Naveen Pawar Milk', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '10', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('11', 'Anmol Milk Distributors', 'Anmol Milk Distributors', 'Anmol Milk Distributors', '7498309448', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '11', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('12', 'Shiva sales corporation', 'Shiva sales corporation', 'Shiva sales corporation', '9718518803', 'NULL', '<EMAIL>', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '12', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('13', 'Salim Milk Center', 'Salim Milk Center', 'Salim Milk Center', '9820791661', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '13', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('14', 'Gautam Jee Dairy', 'Gautam Jee Dairy', 'Gautam Jee Dairy', '9310268194', 'NULL', '<EMAIL>', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '14', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('15', 'Malik', 'Malik', 'Malik', '9990006786', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '15', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('16', 'Satish', 'Satish', 'Satish', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '16', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('17', 'RAM GEN. STORE', 'RAM GEN. STORE', 'RAM GEN. STORE', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '17', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('18', 'ARADHY MILK AGENCY', 'ARADHY MILK AGENCY', 'ARADHY MILK AGENCY', '8692004555', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '18', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('19', 'Raja Ice', 'Raja Ice', 'Raja Ice', 'NULL', 'NULL', 'NULL', 'NULL', 'IN_ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '19', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('20', 'Sonu dairy', 'Sonu dairy', 'Sonu dairy', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '20', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('21', 'Party Ice', 'Party Ice', 'Party Ice', 'NULL', 'NULL', 'NULL', 'NULL', 'IN_ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '21', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('22', 'Cash purchase', 'Cash purchase', 'Cash purchase', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '22', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('23', 'Deepak', 'Deepak', 'Deepak', '9873454473', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '23', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('24', 'S.K. Lohia Milk Supplier', 'S.K. Lohia Milk Supplier', 'S.K. Lohia Milk Supplier', '9873331005', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '24', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('25', 'Dipesh', 'Dipesh', 'Dipesh', '9212339907', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '25', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('26', 'More Mega Store', 'More Mega Store', 'More Mega Store', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '26', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('27', 'Shri Sai', 'Shri Sai', 'Shri Sai', '9820885732', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '27', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('28', 'Anup Mukherjee', 'Anup Mukherjee', 'Anup Mukherjee', '8826829736', '9899630693', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '28', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('29', 'Rakesh Diary', 'Rakesh Diary', 'Rakesh Diary', '7838506244', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '29', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('30', 'Roshini Dairy', 'Roshini Dairy', 'Roshini Dairy', '7498382851', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '30', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('31', 'Vikash Transport', 'Vikash Transport', 'Vikash Transport', '9324554634', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '31', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('32', 'Ankur Agencies', 'Ankur Agencies', 'Ankur Agencies', '9891129287', 'NULL', '<EMAIL>', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '32', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('33', 'NULL', 'Om Dairy Noida', 'Om Dairy Noida', '9310268193', '', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '33', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('34', 'Lite Bite foods pvt. ltd', 'Lite Bite foods pvt. ltd', 'Lite Bite foods pvt. ltd', 'NULL', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '34', '120056');
INSERT INTO KETTLE_SCM.VENDOR_DETAIL_DATA (VENDOR_ID, ENTITY_NAME, VENDOR_FIRST_NAME, VENDOR_LAST_NAME, VENDOR_PRIMARY_CONTACT, VENDOR_SECONDARY_CONTACT, VENDOR_PRIMARY_EMAIL, VENDOR_SECONDARY_EMAIL, VENDOR_STATUS, VENDOR_TYPE, VENDOR_REQUESTED_BY, VENDOR_UPDATED_AT, VENDOR_ADDRESS_ID, VENDOR_UPDATED_BY) VALUES ('35', 'GunGun distributor', 'GunGun distributor', 'GunGun distributor', '8860446667', 'NULL', 'NULL', 'NULL', 'ACTIVE', 'EXTERNAL', '120056', '2017-04-24', '35', '120056');

/**************Dummy Vendor Address Detail Entry  for Data population*****************/

INSERT INTO KETTLE_SCM.ADDRESS_DETAIL_DATA(ADDRESS_ID,LINE_1,LINE_2,LINE_3,CITY,STATE,COUNTRY,ZIPCODE,ADDRESS_CONTACT,ADDRESS_TYPE)
VALUES(1,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(2,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(3,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(4,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(5,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(6,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(7,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(8,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(9,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(10,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(11,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(12,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(13,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(14,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(15,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(16,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(17,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(18,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(19,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(20,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(21,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(22,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(23,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(24,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(25,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(26,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(27,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(28,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(29,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(30,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(31,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(32,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(33,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(34,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS"),
(35,"Plot 382, first floor","100 feet road","Ghitorni","Delhi","New Delhi","India","110030","7742052349","VENDOR_ADDRESS");