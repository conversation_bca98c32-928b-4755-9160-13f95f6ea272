CREATE TABLE `KETTLE_SCM_DEV`.`AGREEMENT_DETAIL` (
  `AGREEMENT_ID` INT NOT NULL AUTO_INCREMENT,
  `AGREEMENT_TYPE` VARCHAR(45) NOT NULL,
  `AGREEMENT_VERSION` INT NOT NULL,
  `AGREEMENT_VALUE` VARCHAR(1000) NOT NULL,
  PRIMARY KEY (`AGREEMENT_ID`),
  UNIQUE INDEX `AGREEMENT_TYPE_UNIQUE` (`AGREEMENT_TYPE` ASC),
  UNIQUE INDEX `AGREEMENT_VERSION_UNIQUE` (`AGREEMENT_VERSION` ASC),
  UNIQUE INDEX `AGREEMENT_VALUE_UNIQUE` (`AGREEMENT_VALUE` ASC));


ALTER TABLE `KETTLE_SCM_DEV`.`AGREEMENT_DETAIL`
ADD COLUMN `AGREEMENT_STATUS` VARCHAR(10) NOT NULL AFTER `AGREEMENT_VALUE`;

CREATE TABLE `KETTLE_SCM_DEV`.`AGREEMENT_SIGN_RECORD` (
  `RECORD_ID` INT NOT NULL AUTO_INCREMENT,
  `AGREEMENT_ID` INT NOT NULL,
  `AGREEMENT_SIGN_DATE` DATETIME NOT NULL,
  `REQUESTER_USER_ID` INT NOT NULL,
  `RECORD_REASON` VARCHAR(10) NOT NULL,
  PRIMARY KEY (`RECORD_ID`));

ALTER TABLE `KETTLE_SCM_DEV`.`AGREEMENT_SIGN_RECORD`
ADD COLUMN `VENDOR_ID` INT NOT NULL AFTER `RECORD_ID`,
CHANGE COLUMN `RECORD_REASON` `RECORD_REASON` VARCHAR(10) NOT NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`AGREEMENT_DETAIL`
DROP INDEX `AGREEMENT_VALUE_UNIQUE` ,
DROP INDEX `AGREEMENT_VERSION_UNIQUE` ,
DROP INDEX `AGREEMENT_TYPE_UNIQUE` ;
;

ALTER TABLE `KETTLE_SCM_DEV`.`AGREEMENT_DETAIL`
ADD COLUMN `AGREEMENT_SUBTYPE` VARCHAR(100) NOT NULL AFTER `AGREEMENT_TYPE`;

ALTER TABLE `KETTLE_SCM_DEV`.`AGREEMENT_DETAIL`
CHANGE COLUMN `AGREEMENT_VERSION` `AGREEMENT_VERSION` VARCHAR(10) NOT NULL ;

INSERT INTO `KETTLE_SCM_DEV`.`AGREEMENT_DETAIL` (`AGREEMENT_TYPE`, `AGREEMENT_SUBTYPE`, `AGREEMENT_VERSION`, `AGREEMENT_VALUE`, `AGREEMENT_STATUS`) VALUES ('VENDOR', 'LEGAL_DISCLAIMER', 'v1', 'We hereby accept that we are registering with Chaayos i.e. Sunshine Teahouse Private Limited . We will issue invoice in name of company for which we will receive Purchase Order/ Service Order.', 'ACTIVE');
INSERT INTO `KETTLE_SCM_DEV`.`AGREEMENT_DETAIL` (`AGREEMENT_TYPE`, `AGREEMENT_SUBTYPE`, `AGREEMENT_VERSION`, `AGREEMENT_VALUE`, `AGREEMENT_STATUS`) VALUES ('VENDOR', 'VENDOR_AGREEMENT', 'v1', 'I/we completely understand and accept our responsibilities and obligations as a “data controller/ data fiduciary” as defined under the Sensitive Personal Data or Information (SPDI) Rules and/or any relevant applicable law of the land, and I/we shall comply with it unconditionally.', 'ACTIVE');

ALTER TABLE `kettle_scm_dev`.`vendor_company_detail`
ADD COLUMN `MSME_EXPIRE_DATE` DATETIME NULL AFTER `MSME_DOCUMENT`;

CREATE TABLE `KETTLE_SCM_DEV`.`ADDITIONAL_DOCUMENTS_MASTER`(
 `ID` INT NOT NULL AUTO_INCREMENT,
 `DOCUMENT_NAME` VARCHAR(255) NOT NULL,
 `DOCUMENT_CODE` VARCHAR(255) NOT NULL,
 `CREATED_DATE` DATETIME NOT NULL,
 `LAST_UPDATED_DATE` DATETIME NULL DEFAULT NULL,
 `CREATED_BY` VARCHAR(255) NOT NULL,
 `LAST_UPDATED_BY` INT NULL DEFAULT NULL,
 `IS_ACTIVE` VARCHAR(255) NULL DEFAULT NULL,
 PRIMARY KEY(`ID`));


create table COST_DETAIL_DATA_WH
(
    COST_DETAIL_DATA_ID int auto_increment
        primary key,
    KEY_ID              int            not null,
    KEY_TYPE            varchar(25)    not null,
    UNIT_ID             int            not null,
    PRICE               decimal(16, 6) not null,
    QUANTITY            decimal(16, 6) not null,
    UNIT_OF_MEASURE     varchar(10)    not null,
    IS_LATEST           varchar(1)     not null,
    LAST_UPDATE_TMSTMP  timestamp      null,
    EXPIRY_DATE         datetime       null,
    CREATION_REASON     varchar(20)    null,
    CREATION_ITEM_ID    int(20)        null
);

create index COST_DETAIL_DATA_WH_EXPIRY_DATE
    on COST_DETAIL_DATA_WH (EXPIRY_DATE);

create index COST_DETAIL_DATA_WH_LAST_UPDATE_TMSTMP
    on COST_DETAIL_DATA_WH (LAST_UPDATE_TMSTMP);

create index COST_DETAIL_DATA_WH_UNIT_OF_MEASURE
    on COST_DETAIL_DATA_WH (UNIT_OF_MEASURE);

create index IS_LATEST_COST_DETAIL_DATA_WH
    on COST_DETAIL_DATA_WH (IS_LATEST);

create index KEY_ID_COST_DETAIL_DATA_WH
    on COST_DETAIL_DATA_WH (KEY_ID);

create index KEY_TYPE_COST_DETAIL_DATA_WH
    on COST_DETAIL_DATA_WH (KEY_TYPE);

create index UNIT_ID_COST_DETAIL_DATA_WH
    on COST_DETAIL_DATA_WH (UNIT_ID);

create table COST_DETAIL_WH_AUDIT_DATA
(
    COST_DETAIL_DATA_AUDIT_ID int auto_increment
        primary key,
    COST_DETAIL_DATA_ID       int                    not null,
    KEY_ID                    int                    not null,
    KEY_TYPE                  varchar(25)            not null,
    UNIT_ID                   int                    not null,
    PRICE                     decimal(16, 6)         not null,
    QUANTITY                  decimal(16, 6)         not null,
    UNIT_OF_MEASURE           varchar(10)            not null,
    TRANSACTTION_TYPE         varchar(25)            null,
    ADD_TIME                  timestamp              null,
    IS_CANCELLATION           varchar(1) default 'N' not null,
    OLD_PRICE                 decimal(16, 6)         null,
    EXPIRY_DATE               datetime               null
);

create index COST_DETAIL_WH_AUDIT_ID_KEY
    on COST_DETAIL_WH_AUDIT_DATA (KEY_ID);

create index COST_DETAIL_WH_AUDIT_KEY
    on COST_DETAIL_WH_AUDIT_DATA (COST_DETAIL_DATA_AUDIT_ID);

create index COST_DETAIL_WH_AUDIT_TYPE_KEY
    on COST_DETAIL_WH_AUDIT_DATA (KEY_TYPE);

create index COST_DETAIL_WH_AUDIT_UNIT_KEY
    on COST_DETAIL_WH_AUDIT_DATA (UNIT_ID);


    create table COST_DETAIL_CAFE_AUDIT_DATA
    (
        COST_DETAIL_DATA_AUDIT_ID int auto_increment
            primary key,
        COST_DETAIL_DATA_ID       int                    not null,
        KEY_ID                    int                    not null,
        KEY_TYPE                  varchar(25)            not null,
        UNIT_ID                   int                    not null,
        PRICE                     decimal(16, 6)         not null,
        QUANTITY                  decimal(16, 6)         not null,
        UNIT_OF_MEASURE           varchar(10)            not null,
        TRANSACTTION_TYPE         varchar(25)            null,
        ADD_TIME                  timestamp              null,
        IS_CANCELLATION           varchar(1) default 'N' not null,
        OLD_PRICE                 decimal(16, 6)         null,
        EXPIRY_DATE               datetime               null
    );

    create index COST_DETAIL_CAFE_AUDIT_ID_KEY
        on COST_DETAIL_CAFE_AUDIT_DATA (KEY_ID);

    create index COST_DETAIL_CAFE_AUDIT_KEY
        on COST_DETAIL_CAFE_AUDIT_DATA (COST_DETAIL_DATA_AUDIT_ID);

    create index COST_DETAIL_CAFE_AUDIT_TYPE_KEY
        on COST_DETAIL_CAFE_AUDIT_DATA (KEY_TYPE);

    create index COST_DETAIL__CAFEAUDIT_UNIT_KEY
        on COST_DETAIL_CAFE_AUDIT_DATA (UNIT_ID);


create table COST_DETAIL_DATA_CAFE
(
    COST_DETAIL_DATA_ID int auto_increment
        primary key,
    KEY_ID              int            not null,
    KEY_TYPE            varchar(25)    not null,
    UNIT_ID             int            not null,
    PRICE               decimal(16, 6) not null,
    QUANTITY            decimal(16, 6) not null,
    UNIT_OF_MEASURE     varchar(10)    not null,
    IS_LATEST           varchar(1)     not null,
    LAST_UPDATE_TMSTMP  timestamp      null,
    EXPIRY_DATE         datetime       null,
    CREATION_REASON     varchar(20)    null,
    CREATION_ITEM_ID    int(20)        null
);

create index COST_DETAIL_DATA_CAFE_EXPIRY_DATE
    on COST_DETAIL_DATA_CAFE (EXPIRY_DATE);

create index COST_DETAIL_DATA_CAFE_LAST_UPDATE_TMSTMP
    on COST_DETAIL_DATA_CAFE (LAST_UPDATE_TMSTMP);

create index COST_DETAIL_DATA_CAFE_UNIT_OF_MEASURE
    on COST_DETAIL_DATA_CAFE (UNIT_OF_MEASURE);

create index IS_LATEST_COST_DETAIL_DATA_CAFE
    on COST_DETAIL_DATA_CAFE (IS_LATEST);

create index KEY_ID_COST_DETAIL_DATA_CAFE
    on COST_DETAIL_DATA_CAFE (KEY_ID);

create index KEY_TYPE_COST_DETAIL_DATA_CAFE
    on COST_DETAIL_DATA_CAFE (KEY_TYPE);

create index UNIT_ID_COST_DETAIL_DATA_CAFE
    on COST_DETAIL_DATA_CAFE (UNIT_ID);

create table COST_DETAIL_CAFE_AUDIT_DRILLDOWN_DATA
(
    COST_DETAIL_DATA_AUDIT_DRILLDOWN_ID int auto_increment
        primary key,
    COST_DETAIL_DATA_AUDIT_ID           int                    not null,
    KEY_ID                              int                    not null,
    KEY_TYPE                            varchar(25)            not null,
    PRICE                               decimal(16, 6)         not null,
    QUANTITY                            decimal(16, 6)         not null,
    UNIT_OF_MEASURE                     varchar(10)            not null,
    ADD_TIME                            timestamp              null,
    IS_CANCELLATION                     varchar(1) default 'N' not null,
    EXPIRY_DATE                         datetime               null
);

create index COST_DETAIL_CAFE_DRILLDOWN_KEY
    on COST_DETAIL_CAFE_AUDIT_DRILLDOWN_DATA (COST_DETAIL_DATA_AUDIT_ID);

    create table COST_DETAIL_WH_AUDIT_DRILLDOWN_DATA
    (
       COST_DETAIL_DATA_AUDIT_DRILLDOWN_ID int auto_increment
            primary key,
        COST_DETAIL_DATA_AUDIT_ID           int                    not null,
        KEY_ID                              int                    not null,
        KEY_TYPE                            varchar(25)            not null,
        PRICE                               decimal(16, 6)         not null,
        QUANTITY                            decimal(16, 6)         not null,
        UNIT_OF_MEASURE                     varchar(10)            not null,
        ADD_TIME                            timestamp              null,
        IS_CANCELLATION                     varchar(1) default 'N' not null,
        EXPIRY_DATE                         datetime               null
    );

    create index COST_DETAIL_WH_DRILLDOWN_KEY
        on COST_DETAIL_WH_AUDIT_DRILLDOWN_DATA (COST_DETAIL_DATA_AUDIT_ID);


CREATE TABLE `KETTLE_SCM_DEV`.`COSTELEMENT_DOCUMENT_MAPPING`(
`COSTELEMENT_DOCUMENT_MAPPING_ID` INT NOT NULL AUTO_INCREMENT,
`COSTELEMENT_ID` INT NOT NULL,
`DOCUMENT_ID` INT NOT NULL,
`CREATED_BY` VARCHAR(255) NOT NULL,
`CREATED_AT` TIMESTAMP NOT NULL,
PRIMARY KEY(`COSTELEMENT_DOCUMENT_MAPPING_ID`));

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_EVENT_DEFINITION`
ADD COLUMN `PARENT_ID` INT(11) NULL DEFAULT NULL AFTER `EVENT_ID`,
ADD COLUMN `SUB_CATEGORY` VARCHAR(45) NULL DEFAULT NULL AFTER `SUB_TYPE`;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_EVENT_DEFINITION`
ADD COLUMN `SPLIT` VARCHAR(45) NULL DEFAULT NULL AFTER `TO_ID`;

CREATE TABLE `KETTLE_SCM_DEV`.`APPROVAL_DETAIL_DATA` (
  `APPROVAL_REQUEST_ID` INT NOT NULL AUTO_INCREMENT,
  `EVENT_ID` INT NOT NULL,
  `ASSET_ID` INT NOT NULL,
  `SKU_ID` INT NOT NULL,
  `UNIT_ID` INT NOT NULL,
  `TYPE` VARCHAR(45) NOT NULL,
  `STATUS` VARCHAR(45) NOT NULL,
  `SKU_NAME` VARCHAR(45) NOT NULL,
  `PROCUREMENT_COST` DECIMAL NOT NULL,
  `REQUESTED_BY` INT NOT NULL,
  `REQUESTED_TO` INT NOT NULL,
  `REQUEST_DATE` DATE NOT NULL,
  `APPROVED_BY` INT NULL,
  `APPROVAL_DATE` DATE NULL,
  PRIMARY KEY (`APPROVAL_REQUEST_ID`));

  CREATE TABLE `CAFE_SKU_MAPPING` (
  `UNIT_SKU_MAPPING_ID` int(11) NOT NULL AUTO_INCREMENT,
  `UNIT_ID` int(11) NOT NULL,
  `SKU_ID` int(11) NOT NULL,
  `MAPPING_STATUS` varchar(15) NOT NULL,
  `CREATED_AT` timestamp NULL DEFAULT NULL,
  `CREATED_BY` varchar(50) DEFAULT NULL,
  `UPDATED_AT` timestamp NULL DEFAULT NULL,
  `UPDATED_BY` varchar(50) DEFAULT NULL,
  `PROFILE` varchar(32) DEFAULT 'P0',
  `INVENTORY_LIST_ID` int(11) NOT NULL,
  `PRODUCTION_UNIT` int(11) DEFAULT NULL,
  `PACKAGING_ID` int(11) DEFAULT NULL,
  `TAX_CODE` varchar(50) DEFAULT NULL,
  `VO_DISCONTINUED_FROM` timestamp NULL DEFAULT NULL,
  `RO_DISCONTINUED_FROM` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`UNIT_SKU_MAPPING_ID`),
  UNIQUE KEY `UNIT_SKU_MAPPING_KEY` (`UNIT_ID`,`SKU_ID`),
  KEY `UNIT_SKU_MAPPING_MAPPING_STATUS` (`MAPPING_STATUS`) USING BTREE,
  KEY `UNIT_SKU_MAPPING_UNIT_ID` (`UNIT_ID`) USING BTREE,
  KEY `UNIT_SKU_MAPPING_SKU_ID` (`SKU_ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=118855 DEFAULT CHARSET=latin1;

ALTER TABLE `kettle_master_dev`.`unit_detail`
ADD COLUMN `FA_DAYCLOSE_ENABLED` VARCHAR(1) NOT NULL DEFAULT 'N' AFTER `POS_VERSION`;

ALTER TABLE `KETTLE_SCM_DEV`.`ASSET_DEFINITION`
ADD COLUMN `TOTAL_RECOVER_AMOUNT` DECIMAL(16,6) NULL DEFAULT NULL AFTER `IS_IN_TRANSIT`;

ALTER TABLE `kettle_master_dev`.`unit_detail`
ADD COLUMN `UNIT_CAFE_MANAGER` INT(11) NULL DEFAULT NULL AFTER `UNIT_SUB_CATEGORY`,
ADD COLUMN `LAST_HANDOVER_TIME` DATE NULL AFTER `IS_HOTSPOT_LIVE`,
ADD COLUMN `LAST_HANDOVER_FROM` VARCHAR(45) NULL AFTER `LAST_HANDOVER_TIME`;

CREATE TABLE KETTLE_SCM.ASSET_RECOVERY_NEW (
RECOVERY_ID int(11) NOT NULL AUTO_INCREMENT,
ASSET_ID int(11) NOT NULL,
EVENT_ID int(11) NOT NULL,
UNIT_ID int(11) NOT NULL,
AMOUNT_TO_RECOVER decimal(16,6) NOT NULL,
RECOVERY_STATUS varchar(45) NOT NULL,
AMOUNT_RECOVERED decimal(16,6) DEFAULT NULL,
EMPLOYEE_RECOVERY_STATUS varchar(45) NOT NULL,
EMPLOYEE_RECOVERY_AMOUNT decimal(16,6) DEFAULT NULL,
INSURANCE_RECOVERY_STATUS varchar(45) NOT NULL,
INSURANCE_RECOVERY_AMOUNT decimal(16,6) DEFAULT NULL,
WRITE_OFF_AMOUNT decimal(16,6) DEFAULT NULL,
CREATED_BY int(11) DEFAULT NULL,
CREATION_DATE date DEFAULT NULL,
PRIMARY KEY (RECOVERY_ID)
);

CREATE TABLE KETTLE_SCM.ASSET_RECOVERY_DETAIL (
RECOVERY_DETAIL_ID int(11) NOT NULL AUTO_INCREMENT,
RECOVERY_ID int(11) DEFAULT NULL,
RECOVERY_TYPE varchar(45) DEFAULT NULL,
RECOVERY_STATUS varchar(45) DEFAULT NULL,
AMOUNT_RECOVERED decimal(16,6) DEFAULT NULL,
RECOVERED_FROM varchar(45) DEFAULT NULL,
PRIMARY KEY (RECOVERY_DETAIL_ID)
);

CREATE TABLE `kettle_scm_dev`.`stocktake_frequency` (
  `STOCKTAKE_FREQUENCY_ID` INT NOT NULL AUTO_INCREMENT,
  `UNIT_CATEGORY` VARCHAR(45) NOT NULL,
  `TYPE` VARCHAR(45) NULL,
  `FREQUENCY` VARCHAR(45) NOT NULL,
  `CLASSIFICATION_ID` INT NOT NULL,
  `INVENTORY_LIST_ID` INT NULL,
  PRIMARY KEY (`STOCKTAKE_FREQUENCY_ID`)
);

CREATE TABLE `KETTLE_SCM_STAGE`.`INVALID_TAG_SCANNED` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `EVENT_ID` INT NOT NULL,
  `TAG_VALUE` VARCHAR(200) NOT NULL,
  `SCAN_TIME` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`));


ALTER TABLE `KETTLE_SCM_DEV`.`TRANSFER_ORDER_E_INVOICE`
ADD COLUMN `STATUS` VARCHAR(45) NULL AFTER `IRN_NO`;

CREATE INDEX TRANSFER_ORDER_E_INVOICE_STATUS_IDX ON KETTLE_SCM_DUMP.TRANSFER_ORDER_E_INVOICE(STATUS)  USING BTREE;

CREATE TABLE `kettle_scm_dev`.`category_conversion` (
  `CONVERSION_ID` int(11) NOT NULL AUTO_INCREMENT,
  `OLD_CATEGORY` int(11) NOT NULL,
  `NEW_CATEGORY` int(11) NOT NULL,
  `TYPE` varchar(45) NOT NULL,
  `CONVERSION_QUANTITY` int(11) NOT NULL,
  `CONVERSION_DATE` datetime NOT NULL,
  `CONVERTED_BY` int(11) NOT NULL,
  PRIMARY KEY (`CONVERSION_ID`)
)

CREATE TABLE `kettle_scm_dev`.`category_conversion_item` (
  `CONVERSION_ITEM_ID` INT NOT NULL AUTO_INCREMENT,
  `CONVERSION_ID` INT NOT NULL,
  `PRODUCT_ID` INT NOT NULL,
  `ASSET_ID` INT NULL,
  `AMOUNT` DECIMAL(16,6) NOT NULL,
  `GR_ID` INT NULL,
  PRIMARY KEY (`CONVERSION_ITEM_ID`));


