ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM
ADD COLUMN TAX_CODE VARCHAR(40) NULL,
ADD COLUMN TOTAL_TAX DECIMAL (10,2)  NULL;

CREATE TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM_TAX_DETAIL(
ORDER_ITEM_TAX_DETAIL_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
ORDER_ITEM_ID INTEGER NOT NULL,
TAX_CODE VARCHAR(20) NOT NULL,
TAX_TYPE VARCHAR(20) NOT NULL,
TOTAL_AMOUNT DECIMAL(10,2) NULL,
TAXABLE_AMOUNT DECIMAL(10,2) NULL,
TAX_PERCENTAGE DECIMAL (10,2) NOT NULL,
TOTAL_TAX DECIMAL (10,2) NOT NULL
);