ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES
ADD COLUMN CONSUMPTION_PRICE DECIMAL(16,6) NULL,
ADD COLUMN CONSUMPTION_COST DECIMAL(16,6) NULL,
ADD COLUMN TRANSFER_OUT_PRICE DECIMAL(16,6) NULL,
ADD COLUMN TRANSFER_OUT_COST DECIMAL(16,6) NULL,
ADD COLUMN WASTAGE_PRICE DECIMAL(16,6) NULL,
ADD COLUMN WASTAGE_COST DECIMAL(16,6) NULL,
ADD COLUMN RECEIVED_PRICE DECIMAL(16,6) NULL,
ADD COLUMN RECEIVED_COST DECIMAL(16,6) NULL;

INSERT INTO `KETTLE_MASTER_DEV`.`PRE_AUTHENTICATED_API` (`API`, `STATUS`) VALUES ('scm-service.stock-management.check-opening-for-cafe', 'ACTIVE');

ALTER TABLE KETTLE_SCM_DEV.STOCK_INVENTORY
ADD COLUMN VARIANCE_COST DECIMAL(16,6) NULL,
ADD COLUMN VARIANCE_PRICE DECIMAL(16,6) NULL;

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA
ADD COLUMN INVALID_GR VARCHAR(1) NOT NULL DEFAULT 'N';

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('GRPHSH', '7', 'ACTION', 'SHOW', 'SuMo -> Production History -> Get Recipe', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('GRPHRP', '7', 'ACTION', 'SHOW', 'SuMo -> Production History -> Get Recipe -> Re-print', 'ACTIVE');
-- place ids of above inserted actions in following query
INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('3', '150', 'ACTIVE', '120103', '2017-07-01 00:00:00');
INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`) VALUES ('3', '151', 'ACTIVE', '120103', '2017-07-01 00:00:00');


#ACL Mapping or Current Pricing
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('CPR', '7', 'MENU', 'SHOW', 'SUMO -> CURRENT PRICING FOR ALL UNITS', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADDPRI', '7', 'ACTION', 'ADD', 'SUMO -> CURRENT PRICING FOR ALL UNITS -> ADDPRI -> ADD', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('UPDPRI', '7', 'ACTION', 'UPDATE', 'SUMO -> CURRENT PRICING FOR ALL UNITS -> UPDPRI -> UPDATE', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
SELECT '1', ACTION_DETAIL_ID, 'ACTIVE', '100000', '2017-07-01 00:00:00' FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE IN ("CPR","ADDPRI","UPDPRI");
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
SELECT '2', ACTION_DETAIL_ID, 'ACTIVE', '100000', '2017-07-01 00:00:00' FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = "CPR";

DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRODUCT_RECIPE_COST_DATA;
CREATE TABLE KETTLE_SCM_DEV.PRODUCT_RECIPE_COST_DATA(
PRODUCT_RECIPE_COST_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
UNIT_ID INTEGER NOT NULL,
RECIPE_ID INTEGER NOT NULL,
RECIPE_NAME VARCHAR(200) NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
DIMENSION VARCHAR(20) NOT NULL,
COST_TYPE VARCHAR(20) NOT NULL,
COST DECIMAL(20,6) NULL,
LAST_UPDATE_TMSTMP TIMESTAMP NOT NULL,
PRODUCT_SOURCE VARCHAR(20) NULL
);


ALTER TABLE BOOKING_CONSUMPTION
 MODIFY COLUMN CALCULATED_QUANTITY decimal(20,6),
 MODIFY COLUMN UNIT_PRICE decimal(20,6),
 MODIFY COLUMN TOTAL_COST decimal(20,6);


INSERT INTO KETTLE_MASTER_DEV.UNIT_DETAIL (UNIT_NAME, UNIT_REGION, UNIT_CATEGORY, UNIT_EMAIL, START_DATE, UNIT_STATUS, TIN, UNIT_ADDR_ID, BUSINESS_DIV_ID, NO_OF_TERMINALS, NO_OF_TA_TERMINALS, WORKSTATION_ENABLED, NO_OF_TABLES, UNIT_SUB_CATEGORY, HAS_TABLE_SERVICE, FREE_INTERNET_ACCESS, UNIT_REFERENCE_NAME, GSTIN, LOCATION_DETAIL_ID, IS_PARTNER_PRICED, IS_TOKEN_ENABLED, TOKEN_LIMIT)
VALUES ('SCRAP DELHI', 'NCR', 'WAREHOUSE', '<EMAIL>', '2017-08-31 11:00:00', 'IN_ACTIVE', '***********', '12199', '1000', '0', '1', 'N', '0', 'INTL', 'N', 'N', 'SCRAP DELHI', '07AARCS3853M1ZL', '185', 'N', 'N', '0');
INSERT INTO KETTLE_MASTER_DEV.UNIT_DETAIL (UNIT_NAME, UNIT_REGION, UNIT_CATEGORY, UNIT_EMAIL, START_DATE, UNIT_STATUS, TIN, UNIT_ADDR_ID, BUSINESS_DIV_ID, NO_OF_TERMINALS, NO_OF_TA_TERMINALS, WORKSTATION_ENABLED, NO_OF_TABLES, UNIT_SUB_CATEGORY, HAS_TABLE_SERVICE, FREE_INTERNET_ACCESS, UNIT_REFERENCE_NAME, GSTIN, LOCATION_DETAIL_ID, IS_PARTNER_PRICED, IS_TOKEN_ENABLED, TOKEN_LIMIT)
VALUES ('SCRAP MUMBAI', 'MUMBAI', 'WAREHOUSE', '<EMAIL>', '2017-08-31 11:00:00', 'IN_ACTIVE', '27141149418V', '12198', '1000', '0', '1', 'N', '0', 'INTL', 'N', 'N', 'SCRAP MUMBAI', '27AARCS3853M1ZJ', '1', 'N', 'N', '0');

INSERT INTO KETTLE_SCM_DEV.UNIT_DETAIL (UNIT_ID, UNIT_NAME, UNIT_EMAIL, UNIT_STATUS, CATEGORY_ID, TIN_NUMBER)
SELECT UNIT_ID,UNIT_NAME,UNIT_EMAIL,UNIT_STATUS,3,GSTIN from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_NAME="SCRAP DELHI";
INSERT INTO KETTLE_SCM_DEV.UNIT_DETAIL (`UNIT_ID`, `UNIT_NAME`, `UNIT_EMAIL`, `UNIT_STATUS`, `CATEGORY_ID`, `TIN_NUMBER`)
SELECT UNIT_ID,UNIT_NAME,UNIT_EMAIL,UNIT_STATUS,3,GSTIN from KETTLE_MASTER_DEV.UNIT_DETAIL where UNIT_NAME="SCRAP MUMBAI";


ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT_LOG ADD COLUMN INVENTORY_LIST VARCHAR (20) NULL;
