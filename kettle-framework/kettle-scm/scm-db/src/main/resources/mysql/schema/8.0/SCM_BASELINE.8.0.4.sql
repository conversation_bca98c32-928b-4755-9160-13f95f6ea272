CREATE INDEX ASSET_DEFINITION_ASSET_NAME ON KETTLE_SCM_DEV.ASSET_DEFINITION(ASSET_NAME) USING BTREE;
CREATE INDEX ASSET_DEFINITION_UNIT_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(UNIT_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_UNIT_TYPE ON KETTLE_SCM_DEV.ASSET_DEFINITION(UNIT_TYPE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_ASSET_STATUS ON KETTLE_SCM_DEV.ASSET_DEFINITION(ASSET_STATUS) USING BTREE;
CREATE INDEX ASSET_DEFINITION_SKU_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(SKU_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_PRODUCT_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(PRODUCT_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_PROFILE_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(PROFILE_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_VENDOR_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(VENDOR_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_OWNER_TYPE ON KETTLE_SCM_DEV.ASSET_DEFINITION(OWNER_TYPE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_OWNER_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(OWNER_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_FIRST_OWNER_TYPE ON KETTLE_SCM_DEV.ASSET_DEFINITION(FIRST_OWNER_TYPE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_FIRST_OWNER_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(FIRST_OWNER_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_TAG_TYPE ON KETTLE_SCM_DEV.ASSET_DEFINITION(TAG_TYPE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_LIFE_TIME_TYPE ON KETTLE_SCM_DEV.ASSET_DEFINITION(LIFE_TIME_TYPE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_HAS_WARRANTY ON KETTLE_SCM_DEV.ASSET_DEFINITION(HAS_WARRANTY) USING BTREE;
CREATE INDEX ASSET_DEFINITION_RECOVERY_STATUS ON KETTLE_SCM_DEV.ASSET_DEFINITION(RECOVERY_STATUS) USING BTREE;
CREATE INDEX ASSET_DEFINITION_IS_WRITE_OFF ON KETTLE_SCM_DEV.ASSET_DEFINITION(IS_WRITE_OFF) USING BTREE;
CREATE INDEX ASSET_DEFINITION_GR_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(GR_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_GR_ITEM_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION(GR_ITEM_ID) USING BTREE;
CREATE INDEX ASSET_DEFINITION_TAG_VALUE ON KETTLE_SCM_DEV.ASSET_DEFINITION(TAG_VALUE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_START_DATE ON KETTLE_SCM_DEV.ASSET_DEFINITION(START_DATE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_EXPECTED_END_DATE ON KETTLE_SCM_DEV.ASSET_DEFINITION(EXPECTED_END_DATE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_ACTUAL_END_DATE ON KETTLE_SCM_DEV.ASSET_DEFINITION(ACTUAL_END_DATE) USING BTREE;
CREATE INDEX ASSET_DEFINITION_DEPRECIATION_STRATEGY ON KETTLE_SCM_DEV.ASSET_DEFINITION(DEPRECIATION_STRATEGY) USING BTREE;


CREATE INDEX ASSET_DEPRECIATION_MAPPING_ASSET_ID ON KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING(ASSET_ID) USING BTREE;
CREATE INDEX ASSET_DEPRECIATION_MAPPING_OWNER_ID ON KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING(OWNER_ID) USING BTREE;
CREATE INDEX ASSET_DEPRECIATION_MAPPING_ASSET_STATUS ON KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING(ASSET_STATUS) USING BTREE;
CREATE INDEX ASSET_DEPRECIATION_MAPPING_UNIT_ID ON KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING(UNIT_ID) USING BTREE;
CREATE INDEX ASSET_DEPRECIATION_MAPPING_START_DATE ON KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING(START_DATE) USING BTREE;
CREATE INDEX ASSET_DEPRECIATION_MAPPING_END_DATE ON KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING(END_DATE) USING BTREE;

CREATE INDEX PROFILE_ATTRIBUTE_MAPPING_PROFILE_ID ON KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING(PROFILE_ID) USING BTREE;
CREATE INDEX PROFILE_ATTRIBUTE_MAPPING_ATTRIBUTE_ID ON KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING(ATTRIBUTE_ID) USING BTREE;
CREATE INDEX PROFILE_ATTRIBUTE_MAPPING_STATUS ON KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING(STATUS) USING BTREE;
CREATE INDEX PROFILE_ATTRIBUTE_MAPPING_IS_STANDALONE ON KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING(IS_STANDALONE) USING BTREE;

CREATE INDEX ASSET_RECOVERY_ASSET_ID ON KETTLE_SCM_DEV.ASSET_RECOVERY(ASSET_ID) USING BTREE;
CREATE INDEX ASSET_RECOVERY_UNIT_ID ON KETTLE_SCM_DEV.ASSET_RECOVERY(UNIT_ID) USING BTREE;
CREATE INDEX ASSET_RECOVERY_ASSET_STATUS ON KETTLE_SCM_DEV.ASSET_RECOVERY(ASSET_STATUS) USING BTREE;
CREATE INDEX ASSET_RECOVERY_RECOVERY_TYPE ON KETTLE_SCM_DEV.ASSET_RECOVERY(RECOVERY_TYPE) USING BTREE;
CREATE INDEX ASSET_RECOVERY_RECOVERY_UNIT ON KETTLE_SCM_DEV.ASSET_RECOVERY(RECOVERY_UNIT) USING BTREE;
CREATE INDEX ASSET_RECOVERY_RECOVERY_UNIT_TYPE ON KETTLE_SCM_DEV.ASSET_RECOVERY(RECOVERY_UNIT_TYPE) USING BTREE;
CREATE INDEX ASSET_RECOVERY_RECOVERY_STATUS ON KETTLE_SCM_DEV.ASSET_RECOVERY(RECOVERY_STATUS) USING BTREE;

CREATE INDEX ASSET_SCRAPPED_MAPPING_ASSET_ID ON KETTLE_SCM_DEV.ASSET_SCRAPPED_MAPPING(ASSET_ID) USING BTREE;
CREATE INDEX ASSET_SCRAPPED_MAPPING_UNIT_ID ON KETTLE_SCM_DEV.ASSET_SCRAPPED_MAPPING(UNIT_ID) USING BTREE;
CREATE INDEX ASSET_SCRAPPED_MAPPING_SCRAPPED_DATE ON KETTLE_SCM_DEV.ASSET_SCRAPPED_MAPPING(SCRAPPED_DATE) USING BTREE;

CREATE INDEX ASSET_TRANSFER_MAPPING_UNIT_ID ON KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING(UNIT_ID) USING BTREE;
CREATE INDEX ASSET_TRANSFER_MAPPING_ASSET_ID ON KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING(ASSET_ID) USING BTREE;
CREATE INDEX ASSET_TRANSFER_MAPPING_ASSET_STATUS ON KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING(ASSET_STATUS) USING BTREE;
CREATE INDEX ASSET_TRANSFER_MAPPING_OWNER_ID ON KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING(OWNER_ID) USING BTREE;
CREATE INDEX ASSET_TRANSFER_MAPPING_START_DATE ON KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING(START_DATE) USING BTREE;
CREATE INDEX ASSET_TRANSFER_MAPPING_END_DATE ON KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING(END_DATE) USING BTREE;
CREATE INDEX ASSET_TRANSFER_MAPPING_IS_WRITE_OFF ON KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING(IS_WRITE_OFF) USING BTREE;

CREATE INDEX TRANSFER_ORDER_NOTE_ASSET_ID ON KETTLE_SCM_DEV.TRANSFER_ORDER_NOTE(ASSET_ID) USING BTREE;
CREATE INDEX TRANSFER_ORDER_NOTE_UNIT_ID ON KETTLE_SCM_DEV.TRANSFER_ORDER_NOTE(UNIT_ID) USING BTREE;


CREATE INDEX STOCK_EVENT_DEFINITION_UNIT_ID ON KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION(UNIT_ID) USING BTREE;
CREATE INDEX STOCK_EVENT_DEFINITION_UNIT_TYPE ON KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION(UNIT_TYPE) USING BTREE;
CREATE INDEX STOCK_EVENT_DEFINITION_EVENT_TYPE ON KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION(EVENT_TYPE) USING BTREE;
CREATE INDEX STOCK_EVENT_DEFINITION_EVENT_STATUS ON KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION(EVENT_STATUS) USING BTREE;
CREATE INDEX STOCK_EVENT_DEFINITION_EVENT_INITIATOR_ID ON KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION(EVENT_INITIATOR_ID) USING BTREE;
CREATE INDEX STOCK_EVENT_DEFINITION_EVENT_CREATION_DATE ON KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION(EVENT_CREATION_DATE) USING BTREE;
CREATE INDEX STOCK_EVENT_DEFINITION_AUDITOR_ID ON KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION(AUDITOR_ID) USING BTREE;


CREATE INDEX ASSET_DEFINITION_LOG_ASSET_ID ON KETTLE_SCM_DEV.ASSET_DEFINITION_LOG(ASSET_ID) USING BTREE;

