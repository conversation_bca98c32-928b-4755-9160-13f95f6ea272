ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN FULFILLMENT_TYPE VARCHAR(30) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN DEFAULT_FULFILLMENT_TYPE VARCHAR(30) NULL;
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN AVAILABLE_AT_CAFE VARCHAR(1) NOT NULL DEFAULT 'Y';
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN AVAILABLE_FOR_CAFE_INVENTORY VARCHAR(1) NULL DEFAULT 'Y';

DROP TABLE IF EXISTS KETTLE_SCM_DEV.DERIVED_FULFILMENT_TYPE_MAPPING;
CREATE TABLE KETTLE_SCM_DEV.DERIVED_FULFILMENT_TYPE_MAPPING(
  DERIVED_MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
  FULFILMENT_TYPE VARCHAR(30) NOT NULL,
  PRODUCT_ID INTEGER NOT NULL,
  DELIVERY_UNIT_ID INTEGER NOT NULL,
  CONSTRAINT
    FOREIGN KEY (PRODUCT_ID)
    REFERENCES PRODUCT_DEFINITION (PRODUCT_ID)
);


#Migration scripts for Product fulfillment type data to product definition table

UPDATE PRODUCT_DEFINITION set FULFILLMENT_TYPE='EXTERNAL' where PRODUCT_ID in
(
	SELECT PFD.PRODUCT_DEFINITION_ID FROM PRODUCT_FULFILLMENT_TYPE_DATA PFD
	INNER JOIN (SELECT PRODUCT_DEFINITION_ID,COUNT(FULFILLMENT_TYPE) AS FUL_TYPES
	FROM PRODUCT_FULFILLMENT_TYPE_DATA GROUP BY PRODUCT_DEFINITION_ID) AS T ON PFD.PRODUCT_DEFINITION_ID=T.PRODUCT_DEFINITION_ID
	WHERE T.FUL_TYPES=1 and PFD.FULFILLMENT_TYPE='EXTERNAL'
);

UPDATE PRODUCT_DEFINITION set FULFILLMENT_TYPE='KITCHEN' where PRODUCT_ID in
(
	SELECT PFD.PRODUCT_DEFINITION_ID FROM PRODUCT_FULFILLMENT_TYPE_DATA PFD
	INNER JOIN (SELECT PRODUCT_DEFINITION_ID,COUNT(FULFILLMENT_TYPE) AS FUL_TYPES
	FROM PRODUCT_FULFILLMENT_TYPE_DATA GROUP BY PRODUCT_DEFINITION_ID) AS T ON PFD.PRODUCT_DEFINITION_ID=T.PRODUCT_DEFINITION_ID
	WHERE T.FUL_TYPES=1 and PFD.FULFILLMENT_TYPE='KITCHEN'
);

UPDATE PRODUCT_DEFINITION set FULFILLMENT_TYPE='WAREHOUSE' where PRODUCT_ID in
(
	SELECT PFD.PRODUCT_DEFINITION_ID FROM PRODUCT_FULFILLMENT_TYPE_DATA PFD
	INNER JOIN (SELECT PRODUCT_DEFINITION_ID,COUNT(FULFILLMENT_TYPE) AS FUL_TYPES
	FROM PRODUCT_FULFILLMENT_TYPE_DATA GROUP BY PRODUCT_DEFINITION_ID) AS T ON PFD.PRODUCT_DEFINITION_ID=T.PRODUCT_DEFINITION_ID
	WHERE T.FUL_TYPES=1 and PFD.FULFILLMENT_TYPE='WAREHOUSE'
);

UPDATE PRODUCT_DEFINITION set FULFILLMENT_TYPE='DERIVED' where PRODUCT_ID in (100018,100395,100396,100218,100371,100705,100752,100862,100908,100755,100909);
UPDATE PRODUCT_DEFINITION set DEFAULT_FULFILLMENT_TYPE='EXTERNAL' WHERE PRODUCT_ID in (100018,100395,100396);
UPDATE PRODUCT_DEFINITION set DEFAULT_FULFILLMENT_TYPE='WAREHOUSE' WHERE PRODUCT_ID in (100218,100371,100705,100752,100862,100908);
UPDATE PRODUCT_DEFINITION set DEFAULT_FULFILLMENT_TYPE='KITCHEN' WHERE PRODUCT_ID in (100755,100909);