ALTER TABLE KETTLE_SCM_DEV.ITEM_TAX_DETAIL_DATA MODIFY COLUMN PO_ITEM_ID INTEGER NULL;
ALTER TABLE KETTLE_SCM_DEV.ITEM_TAX_DETAIL_DATA ADD COLUMN GR_ITEM_ID INTEGER NULL;

ALTER TABLE KETTLE_SCM_DEV.ITEM_TAX_DETAIL_DATA
ADD CONSTRAINT ITEM_TAX_DETAIL_DATA_IBFK_2
  FOREIGN KEY (GR_ITEM_ID)
  REFERENCES KETTLE_SCM_DEV.VENDOR_GR_ITEM_DETAIL (GOODS_RECEIVED_ITEM_ID);


CREATE TABLE KETTLE_SCM_DEV.PURCHASE_ORDER_TO_GOODS_RECEIVED_MAPPING(
 MAPPING_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
 PURCHASE_ORDER_ID INT NOT NULL,
 VENDOR_GOODS_RECEIVED_ID INT NOT NULL,
 CONS<PERSON>AINT
		FOREIGN KEY (VENDOR_GOODS_RECEIVED_ID)
		REFERENCES KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA (GOODS_RECEIVED_ID),
 CONSTRAINT
		FOREIGN KEY (PURCHASE_ORDER_ID)
		REFERENCES KETTLE_SCM_DEV.PURCHASE_ORDER (PURCHASE_ORDER_ID)
		
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA;
CREATE TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA (
    GOODS_RECEIVED_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    GOODS_RECEIVED_STATUS VARCHAR(15) NOT NULL,
    UPDATED_BY INTEGER NULL,
    CREATED_BY INTEGER NOT NULL,
    CREATED_AT DATETIME NOT NULL,
    UPDATED_AT DATETIME NULL,
    DELIVERY_UNIT_ID INTEGER NOT NULL,
    VENDOR_ID INTEGER NOT NULL,
	DISPATCH_ID INTEGER NOT NULL,
	DOCUMENT_UPLOADED VARCHAR(30) NOT NULL,
	DOCUMENT_NUMBER VARCHAR(100) NOT NULL,
	TOTAL_PRICE DECIMAL(16,4) NOT NULL,
	TOTAL_TAX DECIMAL(16,4) NOT NULL,
	TOTAL_AMOUNT DECIMAL(16,4) NOT NULL,
	EXTRA_CHARGES DECIMAL(16,4) NULL,
	CREATION_TYPE VARCHAR(30) NOT NULL
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.VENDOR_GR_ITEM_DETAIL;
CREATE TABLE KETTLE_SCM_DEV.VENDOR_GR_ITEM_DETAIL (
    GOODS_RECEIVED_ITEM_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    SKU_ID INTEGER NOT NULL,
	HSN_CODE VARCHAR(50) NOT NULL,
	SKU_NAME VARCHAR(100) NOT NULL,
	PACKAGING_ID INTEGER NOT NULL,
	PACKAGING_NAME VARCHAR(50) NOT NULL,
	PACKAGING_QUANTITY INTEGER NOT NULL,
	CONVERSION_RATIO DECIMAL(10,5) NOT NULL,
	RECEIVED_QUANTITY DECIMAL(16,6) NOT NULL,
	UNIT_OF_MEASURE VARCHAR(5) NOT NULL,
	UNIT_PRICE DECIMAL(10,4) NOT NULL,
	TOTAL_PRICE DECIMAL(10,4) NOT NULL,
	TOTAL_TAX DECIMAL(10,4) NOT NULL,
	TOTAL_AMOUNT DECIMAL(10,4) NOT NULL,
	VENDOR_GR_ID INTEGER NOT NULL,
	CONSTRAINT
		FOREIGN KEY (VENDOR_GR_ID)
		REFERENCES KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA (GOODS_RECEIVED_ID)
);


#STOCK INVENTORY RELATED CHANGES HERE ONWARDS

ALTER TABLE KETTLE_SCM.WASTAGE_EVENT ADD COLUMN COST DECIMAL(16,6) NULL;
ALTER TABLE KETTLE_SCM.WASTAGE_EVENT ADD COLUMN PRICE DECIMAL(16,6) NULL;
ALTER TABLE KETTLE_SCM.WASTAGE_EVENT ADD COLUMN SKU_ID INTEGER NULL;