update KETTLE_SCM_DEV.VENDOR_DETAIL_DATA
set VENDOR_TYPE  = 'VENDOR'
where VENDOR_TYPE = 'EXTERNAL';


update KETTLE_SCM_DEV.VENDOR_REGISTRATION_REQUEST_DETAIL
set VENDOR_TYPE  = 'VENDOR'
where VENDOR_TYPE = 'EXTERNAL';

ALTER TABLE `KETTLE_SCM_DEV`.`UNIT_VENDOR_MAPPING` 
ADD COLUMN `FULFILMENT_TYPE` VARCHAR(30) NOT NULL AFTER `MAPPING_STATUS`,
ADD COLUMN `SMS_NOTIFICATION` VARCHAR(1) NULL AFTER `FULFILMENT_TYPE`,
ADD COLUMN `EMAIL_NOTIFICATION` VARCHAR(1) NULL AFTER `SMS_NOTIFICATION`,
ADD COLUMN `NOTIFY_BEFORE_DAYS` INT(11) NULL AFTER `EMAIL_NOTIFICATION`,
ADD COLUMN `NOTIFICATION_TIME` VARCHAR(45) NOT NULL AFTER `NOTIFY_BEFORE_DAYS`,
ADD COLUMN `CREATION_TIME` TIMESTAMP NOT NULL AFTER `NOTIFICATION_TIME`,
ADD COLUMN `LAST_UPDATED_TIME` TIMESTAMP NULL AFTER `CREATION_TIME`,
ADD COLUMN `CREATED_BY` INT(11) NOT NULL AFTER `LAST_UPDATED_TIME`,
ADD COLUMN `LAST_UPDATED_BY` INT(11) NULL AFTER `CREATED_BY`;



CREATE TABLE KETTLE_SCM_DEV.PRODUCT_CONSUMPTION_DRILLDOWN (
CONSUMPTION_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
CONSUMPTION_TYPE VARCHAR(20) NOT NULL,
EVENT_ID INTEGER NOT NULL,
PRODUCT_ID INTEGER NOT NULL,
UNIT_ID INTEGER NOT NULL,
UOM VARCHAR(10) NOT NULL,
CONSUMPTION DECIMAL(16,6)
);

