
DELIMITER $$
CREATE  PROCEDURE KETTLE_SCM_DEV.`CLONE_UNIT_DAY_CLOSE_ESTIMATE_DATA`(IN IN_NEW_UNIT_ID INTEGER,IN IN_CLONING_UNIT_ID INTEGER,IN REQUIRED_BUSINESS_DATE  DATE)
BEGIN


INSERT INTO KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA (
UNIT_ID ,
BRAND_ID ,
PRODUCT_ID ,
DIM<PERSON><PERSON><PERSON> ,
QUANTITY ,
BUSINESS_DATE ,
DAY_OF_WEEK ,
UPT ,
NET_SALES ,
GMV ,
DATA_TYPE,
CALCULATED_ORDER_SOURCE
)

SELECT IN_NEW_UNIT_ID, BRAND_ID, PRODUCT_ID, <PERSON><PERSON>ENSION, QUANTITY, BUSINESS_DATE, DAY_OF_WEEK, UPT, NET_SALES, GMV, DATA_TYPE, CALCULATED_ORDER_SOURCE 
FROM KETTLE_DEV.DAY_CLOSE_ESTIMATE_DATA WHERE UNIT_ID=IN_CLONING_UNIT_ID AND BUSINESS_DATE>=REQUIRED_BUSINESS_DATE;



INSERT INTO KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA (
UNIT_ID ,
BRAND_ID,
BUSINESS_DATE,
NET_SALES,
SALE_TYPE,
SALES_PERCENTAGE
)
SELECT  IN_NEW_UNIT_ID, BRAND_ID, BUSINESS_DATE, NET_SALES, SALE_TYPE, SALES_PERCENTAGE
FROM
   KETTLE_SCM_DEV.DAY_CLOSE_SALES_DATA 
WHERE
    UNIT_ID = IN_CLONING_UNIT_ID
	AND BUSINESS_DATE >= REQUIRED_BUSINESS_DATE;
END$$
DELIMITER ;

CREATE TABLE KETTLE_SCM_DEV.`HOLIDAYS_LIST_DATA`(
	`HOLIDAY_LIST_ID` INTEGER(11) NOT NULL AUTO_INCREMENT,
    `HOLIDAY_TYPE` varchar(20) NOT NULL,
    `HOLIDAY_DATE` DATE NOT NULL,
    `HOLIDAY_YEAR` INTEGER(4) NOT NULL,
    `HOLIDAY_MONTH` INTEGER(2) NOT NULL,
    `STATUS` varchar(15) NOT NULL,
    `CREATED_BY` varchar(50) NOT NULL,
    `CREATED_TIME` TIMESTAMP NULL,
    `UPDATED_BY` VARCHAR(50) NULL,
    `UPDATED_AT` TIMESTAMP NULL,
    PRIMARY KEY(`HOLIDAY_LIST_ID`)
);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN `VENDOR_PAYMENT_DATE` DATE;

ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL ADD COLUMN `VENDOR_PAYMENT_DATE` DATE NULL;

ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL ADD COLUMN `UTR_UPLOADED_BY` varchar(50) NULL;

ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL ADD COLUMN `UTR_UPLOADED_TIME` timestamp NULL;

ALTER TABLE KETTLE_SCM_DEV.PR_PAYMENT_DETAIL ADD COLUMN `IS_FORCED_UTR_UPLOAD` varchar(1) NULL;


CREATE TABLE KETTLE_SCM_DEV.REGULAR_ORDERING_FORECAST_DATA(
	FORECAST_ID INTEGER(20) PRIMARY KEY AUTO_INCREMENT,
	DATE TIMESTAMP,
    BRAND VARCHAR(30),
    CAFE_ID VARCHAR(50),
    CITY VARCHAR(30),
    L1_CATEGORY VARCHAR(30),
    L2_CATEGORY VARCHAR(30),
    L3_CATEGORY VARCHAR(30),
    L4_CATEGORY VARCHAR(30),
    MOTHER_WAREHOUSE VARCHAR(30),
    ORDER_SOURCE VARCHAR(30),
    SKU_ID VARCHAR(70),
    SKU_NAME VARCHAR(255),
    PREDICTED_LPI DECIMAL(16,6),
    PREDICTED_BASELINE DECIMAL(16,6),
    PREDICTED_UPI DECIMAL(16,6),
    PREDICTED_FINAL DECIMAL(16,6),
    PRICE DECIMAL(16,6),
    ACTUAL_VALUE DECIMAL(16,6),
    PREDICTED_BASELINE_VALUE DECIMAL(16,6),
    VALUE DECIMAL(16,6),
    REFRESH_DATE TIMESTAMP
);

ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER ADD COLUMN REF_ORDER_SOURCE VARCHAR(200);
ALTER TABLE KETTLE_SCM_DEV.REGULAR_ORDERING_FORECAST_DATA ADD COLUMN (ACTUAL DECIMAL(16,6));

ALTER TABLE KETTLE_SCM_DEV.UNIT_SKU_MAPPING ADD COLUMN PACKAGING_ID INT(11) NULL;