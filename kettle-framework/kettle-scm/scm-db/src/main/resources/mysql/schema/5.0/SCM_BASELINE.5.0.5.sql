ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_DATA ADD COLUMN CREATION_REASON VARCHAR (20);
ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_DATA ADD COLUMN CREATION_ITEM_ID INTEGER (20);

INSERT INTO KETTLE_SCM_ARCHIVE.COST_DETAIL_DATA_REASON
SELECT cdd.COST_DETAIL_DATA_ID,cdd.KEY_TYPE,cdd.KEY_ID,cdd.UNIT_ID, cadd.KEY_TYPE as REASON,cadd.KEY_ID as REASON_ITEM_ID
from KETTLE_SCM_DEV.COST_DETAIL_DATA cdd
INNER JOIN KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA cad
on cad.COST_DETAIL_DATA_ID = cdd.COST_DETAIL_DATA_ID
INNER JOIN KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DRILLDOWN_DATA cadd
on cadd.COST_DETAIL_DATA_AUDIT_ID = cad.COST_DETAIL_DATA_AUDIT_ID and cadd.KEY_TYPE IN ("RECEIVED","VENDOR_RECEIVED");

SET SQL_SAFE_UPDATES=0;
UPDATE KETTLE_SCM_DEV.COST_DETAIL_DATA CDD INNER JOIN
KETTLE_SCM_DEV.COST_DETAIL_DATA_REASON CR ON CR.COST_DETAIL_DATA_ID = CDD.COST_DETAIL_DATA_ID
SET CDD.CREATION_REASON = CR.REASON, CDD.CREATION_ITEM_ID = CR.REASON_ITEM_ID;

CREATE TABLE KETTLE_SCM_DEV.CONSUMPTION_AUDIT_DATA(
  AUDIT_DETAIL_ID INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  KEY_ID INTEGER NOT NULL,
  KEY_TYPE VARCHAR(10) NOT NULL,
  UOM VARCHAR(20) NOT NULL,
  COST_DETAIL_ID INTEGER NOT NULL,
  TRANSACTION_ITEM_ID INT NOT NULL,
  TRANSACTION_TYPE VARCHAR (30) NOT NULL,
  COST_DETAIL_QUANTITY DECIMAL(16,6) NOT NULL,
  QUANTITY DECIMAL(16,6) NOT NULL,
  PRICE DECIMAL(16,6) NOT NULL,
  LAST_UPDATE_TIME TIMESTAMP ,
  EXPIRY_DATE DATETIME,
  CREATION_REASON VARCHAR(40),
  CREATION_ITEM_ID INT,
  EXCEPTION_REASON VARCHAR(30)
);

CREATE INDEX COST_DETAIL_AUDIT_TYPE_KEY ON KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA(KEY_TYPE) USING BTREE;
CREATE INDEX COST_DETAIL_AUDIT_ID_KEY ON KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA(KEY_ID) USING BTREE;
CREATE INDEX COST_DETAIL_AUDIT_UNIT_KEY ON KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA(UNIT_ID) USING BTREE;
CREATE INDEX COST_DETAIL_AUDIT_KEY ON KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA(COST_DETAIL_DATA_AUDIT_ID) USING BTREE;
CREATE INDEX COST_DETAIL_DRILLDOWN_KEY ON KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DRILLDOWN_DATA(COST_DETAIL_DATA_AUDIT_ID) USING BTREE;

DROP TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DUMP_ASC;
CREATE TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DUMP_ASC 
SELECT * FROM (SELECT 
  CAD.COST_DETAIL_DATA_ID,
  CAD.COST_DETAIL_DATA_AUDIT_ID,
  CAD.KEY_TYPE,
  CAD.KEY_ID,
  CAD.UNIT_ID,
  CAD.PRICE,
  CADD.COST_DETAIL_DATA_AUDIT_DRILLDOWN_ID AS DRILLDOWN_ID,
  CADD.KEY_TYPE AS REASON,
  CADD.KEY_ID AS REASON_ITEM_ID
FROM
  KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA CAD
INNER JOIN
  KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DRILLDOWN_DATA CADD ON CADD.COST_DETAIL_DATA_AUDIT_ID = CAD.COST_DETAIL_DATA_AUDIT_ID
WHERE
    CADD.KEY_TYPE NOT IN ('Variance' , 'CostDetailData') 
GROUP BY COST_DETAIL_DATA_AUDIT_ID
ORDER BY CADD.COST_DETAIL_DATA_AUDIT_DRILLDOWN_ID ASC) AS T
GROUP BY T.COST_DETAIL_DATA_ID;



SET sql_safe_updates =0;
UPDATE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DUMP_ASC A
INNER JOIN KETTLE_SCM_DEV.COST_DETAIL_DATA CDD
ON CDD.KEY_TYPE = A.KEY_TYPE AND CDD.KEY_ID = A.KEY_ID AND CDD.UNIT_ID = A.UNIT_ID
SET CDD.CREATION_REASON = A.REASON, CDD.CREATION_ITEM_ID = A.REASON_ITEM_ID;

UPDATE KETTLE_SCM.COST_DETAIL_DATA
SET CREATION_REASON = "INITIAL_LOAD", CREATION_ITEM_ID = COST_DETAIL_DATA_ID
WHERE CREATION_REASON IS NULL;