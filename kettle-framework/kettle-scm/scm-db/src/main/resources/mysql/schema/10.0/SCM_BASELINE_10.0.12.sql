CREATE TABLE KETTLE_SCM_STAGE.VENDOR_CONTRACT_INFO (
  VENDOR_CONTRACT_ID INT AUTO_INCREMENT NOT NULL,
  VENDOR_ID INT NOT NULL,
  START_DATE DATE NOT NULL,
  <PERSON><PERSON>_DATE DATE,
  UNSIGNED_DOCUMENT_ID INT,
  VENDOR_SIGNED_DOCUMENT_ID INT,
  AUTH_SIGNED_DOCUMENT_ID INT ,
  RECORD_STATUS VARCHAR(255) NOT NULL,
  TEMPLATE_ID INT ,
  CREATED_BY VARCHAR(255) NOT NULL,
  CONTRACT_REQUESTED_BY VARCHAR(255) NOT NULL,
  CREATION_TIME DATETIME NOT NULL,
  IS_MAIL_TRIGGERED VARCHAR(255) NULL,
  OTP_VERIFIED VARCHAR(255) NULL,
  VENDOR_IP_ADDRESS VARCHAR(255) NULL,
  AUTH_IP_ADDRESS VARCHAR(255) NULL,
  VENDOR_DIGITAL_SIGN_ID INT NULL,
  AUTH_DIGITAL_SIGN_ID INT NULL,
  MAIL_TIME DATETIME NULL,
  CONSTRAINT PK_VENDOR_CONTRACT_INFO PRIMARY KEY (VENDOR_CONTRACT_ID)
);

CREATE TABLE KETTLE_SCM_STAGE.PAGE_REQUEST_DETAIL
(
    REQUEST_ID       INT AUTO_INCREMENT
        PRIMARY KEY,
    EVENT_ID         INT          NOT NULL,
    EVENT_TYPE       VARCHAR(255) NOT NULL,
    CREATED_BY       VARCHAR(255) NOT NULL,
    RECORD_STATUS    VARCHAR(255) NOT NULL,
    REGISTRATION_UTL VARCHAR(255) NOT NULL,
    AUTH_KEY         VARCHAR(255) NOT NULL,
    REQUEST_DATE     DATE         NULL
);

CREATE TABLE KETTLE_SCM_STAGE.VENDOR_CONTRACT_ITEM
(
    CONTRACT_ITEM_ID   INT AUTO_INCREMENT
        PRIMARY KEY,
    VENDOR_CONTRACT_ID INT          NOT NULL,
    VENDOR_ID          INT          NOT NULL,
    SKU_ID             INT          NOT NULL,
    SKU_PACKAGING_ID   INT          NOT NULL,
    SKU_PRICE_DATE_ID  INT          NOT NULL,
    DISPATCH_LOCATION  VARCHAR(255) NOT NULL,
    DELIVERY_LOCATION  VARCHAR(255) NOT NULL,
    CURRENT_PRICE      DECIMAL      NULL,
    NEGOTIATED_PRICE   DECIMAL      NULL,
    START_DATE         DATE         ,
    END_DATE           DATE         ,
    CREATED_BY         VARCHAR(255) NOT NULL,
    CREATION_TIME      DATETIME     NOT NULL,
    TAX_CODE      VARCHAR(200)     NOT NULL,
    TAX_PERCENTAGE     DECIMAL     NOT NULL
);

ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_DATA ADD COLUMN END_DATE DATE ,
ADD IS_PRICE_CHANGE_REQUESTED VARCHAR(2) DEFAULT 'N';

ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_HISTORY ADD COLUMN END_DATE DATE;
ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_HISTORY ADD COLUMN CONTRACT_ID INTEGER;

ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_DATA MODIFY COLUMN START_DATE DATE;
ALTER TABLE KETTLE_SCM_STAGE.SKU_PRICE_HISTORY MODIFY COLUMN START_DATE DATE;


INSERT INTO KETTLE_MASTER_STAGE.ACCESS_CONTROL_LIST_DATA (ACL_MODULE, ACL_MODULE_DESCRIPTION, ACL_STATUS, APPLICATION_NAME)
VALUES ('scm-service.vendor-contract-management.*', 'Vendor Management Resource for Contract Services', 'ACTIVE',
        'SCM_SERVICE');
INSERT INTO KETTLE_MASTER_STAGE.PARTNER_PERMISSION_MAPPING (PARTNER_ID , PERMISSION, ACL_ID, PPM_STATUS)
VALUES( 6,1111, (SELECT ACL_ID FROM KETTLE_MASTER_STAGE.ACCESS_CONTROL_LIST_DATA  WHERE ACL_MODULE ='scm-service.vendor-contract-management.*') ,'ACTIVE');



-- VCM
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management', 'Vendor Contract Management', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCM', '7', 'MENU', 'SHOW', 'SuMo ->Vendor Contract Management', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCM'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');
-- VCMSP
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management Stage Price', 'Vendor Contract Management Stage Price', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMSP', '7', 'SUBMENU', 'SHOW', 'SuMo ->Vendor Contract Management -> Vendor to SKU Price', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management Stage Price'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCMSP'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');
-- VCMRR
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management Raise Request', 'Vendor Contract Management Raise Request', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMRR', '7', 'SUBMENU', 'SHOW', 'SuMo ->Vendor Contract Management -> Vendor to SKU Price', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management Raise Request'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCMRR'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');


-- VCMRP
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management Price Preview', 'Vendor Contract Management Price Preview', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMRP', '7', 'SUBMENU', 'SHOW', 'SuMo ->Vendor Contract Management -> Vendor to SKU Price Preview', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management Price Preview'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCMRP'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');

-- VCMVC
INSERT INTO `KETTLE_MASTER_STAGE`.`USER_ROLE_DATA`(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('SUMO - Vendor Contract Management View Contracts', 'Vendor Contract Management View Contracts', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_STAGE`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`,
                                                 `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMVC', '7', 'MENU', 'SHOW', 'SuMo ->Vendor Contract Management -> View Contracts', 'ACTIVE');
INSERT INTO `KETTLE_MASTER_STAGE`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`,
                                                       `LAST_UPDATE_TIME`)
VALUES ((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO - Vendor Contract Management View Contracts'),
        (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'VCMVC'), 'ACTIVE', '124984',
        '2023-09-03 23:59:59');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('PREVIEW_PRICE', 'Vendor Contract Preview Price', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMPP', '7', 'ACTION', 'VIEW', 'SuMo -> Vendor Contarct Management -> Vendor To Sku Request', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PREVIEW_PRICE'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VCMPP'), 'ACTIVE', '120063', '2023-09-05 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR_CONTRACT_MAIL_VENDOR', 'Vendor Contract Mail Vendor', 'ACTIVE', '7');
INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VARIANCE_EDIT_SUBMISSION', 'Variance Edit Submission', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VEDS', '7', 'ACTION', 'VIEW', 'SuMo -> Update -> Variance Edit', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VARIANCE_EDIT_SUBMISSION'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VEDS'), 'ACTIVE', '120063', '2023-09-05 12:00:00');



INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VCMMV', '7', 'ACTION', 'VIEW', 'SuMo -> Vendor Contarct Management -> View Contracts', 'ACTIVE');

ALTER TABLE `KETTLE_SCM_DEV`.`SKU_DEFINITION`
    ADD COLUMN `IS_BRANDED` VARCHAR(1) NULL DEFAULT NULL AFTER `DISCONTINUED_SKU`,
ADD COLUMN `BRAND` VARCHAR(255) NULL DEFAULT NULL AFTER `IS_BRANDED`;
INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR_CONTRACT_MAIL_VENDOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VCMMV'), 'ACTIVE', '120063', '2023-09-05 12:00:00');


ALTER TABLE KETTLE_SCM_DEV.VENDOR_CONTRACT_INFO ADD COLUMN VENDOR_USER_NAME VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_CONTRACT_INFO ADD COLUMN VENDOR_USER_DESIGNATION VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_CONTRACT_ITEM ADD COLUMN IS_NEW VARCHAR(100);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA ADD COLUMN BY_PASS_CONTRACT VARCHAR(1) DEFAULT 'N';

CREATE TABLE `KETTLE_SCM_DEV`.`LINKED_ADVANCE_FOR_PAYMENT`
(
    `LINKED_PARENT_ADVANCE_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `ADVANCE_PAYMENT_ID`       INT(11) NOT NULL,
    `PAYMENT_REQUEST_ID`       INT(11) NOT NULL,
    PRIMARY KEY (`LINKED_PARENT_ADVANCE_ID`),
    UNIQUE KEY `LINKED_PARENT_ADVANCE_ID_UNIQUE` (`LINKED_PARENT_ADVANCE_ID`)
);

ALTER TABLE `KETTLE_SCM_DEV`.`GATEPASS_ITEM_ASSET_MAPPING`
    ADD COLUMN `RETURN_GATEPASS_ITEM_ID` INT(11) ;

ALTER TABLE `KETTLE_SCM_DEV`.`SALES_PERFORMA_INVOICE`
    ADD COLUMN `CREDIT_NOTE_DOC_ID` INT(11) ,
    ADD COLUMN `DEBIT_NOTE_DOC_ID` INT(11) ,
    ADD COLUMN `CREDIT_NOTE_DOC_URL` VARCHAR(500) ,
    ADD COLUMN `DEBIT_NOTE_DOC_URL` VARCHAR(500) ;


