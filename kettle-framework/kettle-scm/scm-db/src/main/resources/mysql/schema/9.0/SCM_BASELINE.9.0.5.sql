
CREATE TABLE KETTLE_SCM_DEV.VENDOR_LOG_DATA (
  VENDOR_LOG_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
  VENDOR_ID INT(11) NOT NULL ,
  VENDOR_FIRST_NAME VARCHAR(255) ,
  VENDOR_LAST_NAME VARCHAR(255) ,
  VENDOR_STATUS VARCHAR(255) ,
   LOG_DATA VARCHAR(255) NOT NULL,
   UPDATE_TIME TIMESTAMP NOT NULL
  );
ALTER TABLE KETTLE_SCM_DEV.VENDOR_LOG_DATA CHANGE VENDOR_FIRST_NAME ENTITY_NAME VARCHAR(255);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_LOG_DATA CHANGE VENDOR_LAST_NAME VENDOR_NAME VARCHAR(255);
ALTER TABLE KETTLE_SCM_DEV.VENDOR_LOG_DATA ADD LINK TEXT;
