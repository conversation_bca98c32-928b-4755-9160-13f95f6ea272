ALTER TABLE KETTLE_SCM_DEV.ADDRESS_DETAIL_DATA ADD COLUMN STATE_CODE VARCHAR(10) NULL;
ALTER TABLE KETTLE_SCM_DEV.DOCUMENT_DETAIL_DATA ADD COLUMN DOCUMENT_URL VARCHAR(500);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT;
CREATE TABLE KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT (
    PLAN_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    UNIT_ID INTEGER NOT NULL,
    FULFILLMENT_DATE DATETIME NOT NULL,
    GENERATED_BY INTEGER NOT NULL,
    GENERATION_TIME DATETIME NOT NULL,
    LAST_UPDATED_BY INTEGER NOT NULL,
    LAST_UPDATE_TIME DATETIME NOT NULL,
    PLAN_STATUS VARCHAR(10)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.PLAN_ORDER_MAPPING;
CREATE TABLE KETTLE_SCM_DEV.PLAN_ORDER_MAPPING (
    PLAN_ORDER_MAP_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    PLAN_EVENT_ID INTEGER NOT NULL,
    REQUEST_ORDER_ID INTEGER NOT NULL
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.PLAN_ORDER_ITEM;
CREATE TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM (
    PLAN_ITEM_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
    PRODUCT_ID INTEGER NOT NULL,
    PRODUCT_NAME VARCHAR(100) NOT NULL,
    REQUESTED_QUANTITY DECIMAL(20,10) NOT NULL,
    AVAILABLE_QUANTITY DECIMAL(20,10) NOT NULL,
    UNIT_OF_MEASURE VARCHAR(10) NOT NULL,
    UNIT_PRICE DECIMAL(20,10),
    CALCULATED_AMOUNT DECIMAL(20,10),
    CATEGORY VARCHAR(20) NOT NULL,
    ITEM_TYPE VARCHAR(20) NOT NULL,
    PLAN_EVENT_ID INTEGER NOT NULL
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.PRODUCTION_BOOKING;
CREATE TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING (
    BOOKING_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    PRODUCT_ID INTEGER NOT NULL,
    PRODUCT_NAME VARCHAR(100) NOT NULL,
    SKU_ID INTEGER NOT NULL,
    UOM VARCHAR(50) NOT NULL,
    QUANTITY DECIMAL(20 , 10 ) NOT NULL,
    UNIT_ID INTEGER NOT NULL,
    UNIT_PRICE DECIMAL(20 , 10 ),
    TOTAL_COST DECIMAL(20 , 10 ),
    GENERATION_TIME DATETIME NOT NULL,
    CANCELLATION_TIME DATETIME NULL,
    CLOSURE_TIME DATETIME NULL,
    CLOSURE_EVENT_ID INTEGER NULL,
    GENERATED_BY INTEGER NOT NULL,
    CANCELLED_BY INTEGER NULL,
    BOOKING_STATUS VARCHAR(20) NOT NULL
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.BOOKING_CONSUMPTION;
CREATE TABLE KETTLE_SCM_DEV.BOOKING_CONSUMPTION (
    BOOKING_CONSUMPTION_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SKU_ID INTEGER NOT NULL,
    SKU_NAME VARCHAR(100) NOT NULL,
    UOM VARCHAR(50) NOT NULL,
    CALCULATED_QUANTITY DECIMAL(20 , 10 ) NOT NULL,
    UNIT_PRICE DECIMAL(20 , 10 ) NOT NULL,
    TOTAL_COST DECIMAL(20 , 10 ) NOT NULL,
    PRODUCTION_BOOK_ID INTEGER NOT NULL
);


