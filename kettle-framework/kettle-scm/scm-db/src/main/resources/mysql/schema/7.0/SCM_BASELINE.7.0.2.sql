ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD GROSS_BLOCK DECIMAL(16,6);
ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD FIXED_VALUE DECIMAL(16,6);
ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD RECOVERY_TYPE VARCHAR(40);
ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD RECOVERY_AMOUNT DECIMAL(16,6);
ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD RECOVERY_STATUS VARCHAR(40);
ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD WRITE_OFF_TYPE VARCHAR(40);
ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD WRITE_OFF_AMOUNT DECIMAL(16,6);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING;

DROP TABLE IF EXISTS KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING;

CREATE TABLE KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING (
	ASSET_TRANSFER_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
	ASSET_ID INT NOT NULL,
	UNIT_ID INT NOT NULL,
	ASSET_STATUS VARCHAR(50) NOT NULL,
  OWNER_ID INT NOT NULL,
	START_DATE DATETIME NOT NULL,
	END_DATE DATETIME,
	IS_WRITE_OFF VARCHAR(1),
	PRIMARY KEY (ASSET_TRANSFER_MAPPING_ID)
);

CREATE TABLE KETTLE_SCM_DEV.ASSET_RECOVERY(
	ASSET_RECOVERY_ID INT NOT NULL AUTO_INCREMENT,
	ASSET_ID INT NOT NULL,
	UNIT_ID INT NOT NULL,
	ASSET_STATUS VARCHAR(50) NOT NULL,
	RECOVERY_TYPE VARCHAR(50) NOT NULL,
	RECOVERY_EMP_ID INT,
	RECOVERY_UNIT INT ,
	RECOVERY_UNIT_TYPE VARCHAR(50),
	RECOVERY_AMOUNT DECIMAL(16,6) NOT NULL,
	RECOVERY_STATUS VARCHAR(50),
	RECOVERY_INITIATION_DATE DATETIME,
	RECOVERY_DATE DATETIME,
	CREATED_BY INT NOT NULL,
	CREATION_DATE DATETIME NOT NULL,
	PRIMARY KEY (ASSET_RECOVERY_ID)
);

DROP TABLE KETTLE_SCM_DEV.DEPRECIATION_SUMMARY;

CREATE TABLE KETTLE_SCM_DEV.DEPRECIATION_SUMMARY (

	DEPRECIATION_SUMMARY_ID INT NOT NULL AUTO_INCREMENT,
	ASSET_ID INT NOT NULL,
	ASSET_STATUS VARCHAR(50) NOT NULL,
	START_DATE DATETIME NOT NULL,
  END_DATE DATETIME NOT NULL,
  CURRENT_VALUE DECIMAL(16,6) NOT NULL,
  IS_WRITE_OFF VARCHAR(1),
  WRITE_OFF_TYPE VARCHAR(50),
  WRITE_OFF_AMOUNT VARCHAR(1),

	PRIMARY KEY (DEPRECIATION_SUMMARY_ID)

);

CREATE TABLE KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING(
	ASSET_DEPRECIATION_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
	ASSET_ID INT NOT NULL,
	OWNER_ID INT NOT NULL,
	ASSET_STATUS  VARCHAR(50) NOT NULL,
	UNIT_ID INT NOT NULL,
	START_DATE DATETIME NOT NULL,
	END_DATE DATETIME NOT NULL,
	DEPRECIATION_AMOUNT DECIMAL(16,6),
	PRIMARY KEY (ASSET_DEPRECIATION_MAPPING_ID)
);

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD COLUMN IS_WRITE_OFF VARCHAR(1);

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD COLUMN TAX_PERCENTAGE DECIMAL(16,6);

ALTER TABLE KETTLE_SCM_DEV.DEPRECIATION_SUMMARY CHANGE COLUMN WRITE_OFF_AMOUNT WRITE_OFF_AMOUNT DECIMAL(16,6);

DELETE FROM KETTLE_SCM_DEV.ASSET_DEFINITION;

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY ADD COLUMN RECOVERED_AMOUNT DECIMAL(16,6);
ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY ADD COLUMN RECOVERED_BY INT;

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY DROP COLUMN RECOVERY_INITIATION_DATE;
ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY ADD COLUMN SALLARY_DEDUCTION_DATE DATETIME;

ALTER TABLE KETTLE_SCM_DEV.PROFILE_DEFINITION ADD COLUMN UNIQUE_NUMBER_AVAILABLE VARCHAR(10);
ALTER TABLE KETTLE_SCM_DEV.PROFILE_DEFINITION ADD COLUMN UNIQUE_FIELD_NAME VARCHAR(50);

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN VENDOR_GR_TYPE VARCHAR(50);
UPDATE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA SET VENDOR_GR_TYPE = 'REGULAR_ORDER';

ALTER TABLE KETTLE_SCM_DEV.GATEPASS_DATA
CHANGE COLUMN `IS_ASSET_GATE_PASS` `IS_ASSET_GATE_PASS` VARCHAR(40) NOT NULL COMMENT '' ;

UPDATE KETTLE_SCM_DEV.PURCHASE_ORDER SET ORDER_TYPE = 'REGULAR_ORDER';

ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER
CHANGE COLUMN `ORDER_TYPE` `ORDER_TYPE` VARCHAR(26) NOT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD COLUMN UNIQUE_FIELD_NAME VARCHAR(50);
ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION ADD COLUMN UNIQUE_FIELD_VALUE VARCHAR(50);

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY ADD COLUMN EVENT_ID INT ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY
CHANGE COLUMN `RECOVERY_TYPE` `RECOVERY_TYPE` VARCHAR(50) NULL COMMENT '' ,
CHANGE COLUMN `RECOVERY_AMOUNT` `RECOVERY_AMOUNT` DECIMAL(16,6) NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY ADD COLUMN APPROVED_BY INT;

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY ADD COLUMN APPROVAL_DATE DATETIME;

CREATE TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_NOTE(
  TRANSFER_ORDER_NOTE_ID INT NOT NULL AUTO_INCREMENT,
  ASSET_ID INT NOT NULL,
  GSTIN VARCHAR(40),
  UNIT_ID INT,
  UNIT_NAME VARCHAR(255) ,
  ORIGINAL_INVOICE_NO VARCHAR(40),
  ORIGINAL_INVOICE_DATE DATETIME,
  UNIQUE_SEQUENCE VARCHAR(50),
  DATE_OF_TRANSFER DATETIME,
  DOCUMENT_TYPE VARCHAR(40),
  PLACE_OF_SUPPLY VARCHAR(40),
  AMOUNT_WITH_TAX DECIMAL(16,6),
  APPLICABLE_TAX_RATE DECIMAL(16,6),
  TAX_RATE DECIMAL(16,6),
  TAXABLE_VALUE DECIMAL(16,6),
  CESS_AMOUNT DECIMAL(16,6),
  PRE_GET DECIMAL(16,6),
  PRIMARY KEY (TRANSFER_ORDER_NOTE_ID)
);
