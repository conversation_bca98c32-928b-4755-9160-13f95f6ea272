CREATE TABLE `kettle_scm_dev`.`fa_transfer_data` (
  `FA_REQUEST_ID` INT NOT NULL AUTO_INCREMENT,
  `PRODUCT_ID` INT NULL,
  `TRANSFERRED_QTY` DECIMAL(16,6) NULL,
  `SCANNED_QTY` DECIMAL(16,6) NULL,
  `EVENT_ID` INT NULL,
  PRIMARY KEY (`FA_REQUEST_ID`));
ALTER TABLE `kettle_scm_dev`.`stock_event_definition`
ADD COLUMN `BUDGET_TYPE` VARCHAR(45) NULL AFTER `SUB_TYPE`,
ADD COLUMN `RECEIVING_UNIT_ID` INT NULL AFTER `BUDGET_TYPE`;

INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION
( DEVIATION_CODE, DEVIATION_TYPE, DEVIATION_LEVEL, DEVIATION_DETAIL)
 VALUES ( '034', 'REJECTION', 'INVOICE', 'Invalid/Missing Mandatory Documents');

ALTER TABLE `KETTLE_SCM_DEV`.`UNIT_VENDOR_MAPPING`
ADD COLUMN `DELIVERY_PROMISE_TIME` VARCHAR(45) NULL AFTER `NOTIFICATION_TIME`;

CREATE TABLE `KETTLE_SCM_DEV`.`MILK_BREAD_BYPASS` (
`MILK_BREAD_BYPASS_ID` int(11) NOT NULL AUTO_INCREMENT,
`UNIT_ID` int(11) NOT NULL,
`BYPASS_REASON` varchar(250) NOT NULL,
`COMMENT` varchar(100) NOT NULL,
`RO_IDs` varchar(200) NOT NULL,
`MAX_ALLOWED_TIME` timestamp NULL DEFAULT NULL,
`BYPASSED_BY` varchar(150) NOT NULL,
`BYPASSED_TIME` timestamp NULL DEFAULT NULL,
PRIMARY KEY (`MILK_BREAD_BYPASS_ID`)
);

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('MBBP', '7', 'SUBMENU', 'SHOW', 'SuMo -> Goods ->Receiving -> Milk Bread By Pass', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'AREA_MANAGER'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'MBBP'), 'ACTIVE', '120063', '2023-01-12 12:00:00');

ALTER TABLE `kettle_scm_dev`.`stock_event_definition`
ADD COLUMN `LAST_UPDATION_TIME` DATETIME NULL DEFAULT NULL AFTER `RECEIVING_UNIT_ID`;
ALTER TABLE `kettle_scm_dev`.`stock_event_definition`
ADD COLUMN `LAST_UPDATION_TIME` DATETIME NULL DEFAULT NULL AFTER `RECEIVING_UNIT_ID`;
ALTER TABLE `kettle_scm_dev`.`stock_event_definition`
ADD COLUMN `LAST_UPDATION_TIME` DATETIME NULL DEFAULT NULL AFTER `RECEIVING_UNIT_ID`;
ALTER TABLE KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION
ADD COLUMN `RO_ID` INT(11) NULL,
ADD COLUMN `TO_ID` INT(11) NULL;

ALTER TABLE `kettle_scm_dev`.`stock_event_definition`
ADD COLUMN `LAST_UPDATION_TIME` DATETIME NULL DEFAULT NULL AFTER `RECEIVING_UNIT_ID`;
ALTER TABLE `kettle_scm_dev`.`stock_event_definition`
ADD COLUMN `LAST_UPDATION_TIME` DATETIME NULL DEFAULT NULL AFTER `RECEIVING_UNIT_ID`;
ALTER TABLE KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION
ADD COLUMN `RO_ID` INT(11) NULL,
ADD COLUMN `TO_ID` INT(11) NULL;

INSERT INTO `kettle_scm_dev`.`payment_deviation` (`PAYMENT_DEVIATION_ID`, `DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('35', '035', 'REJECTION', 'INVOICE', 'Asset tag not available');
INSERT INTO `kettle_scm_dev`.`payment_deviation` (`PAYMENT_DEVIATION_ID`, `DEVIATION_CODE`, `DEVIATION_TYPE`, `DEVIATION_LEVEL`, `DEVIATION_DETAIL`) VALUES ('36', '036', 'REJECTION', 'INVOICE', 'Penalty Charged');
CREATE TABLE `kettle_scm_dev`.`purchase_order_log` (
`PURCHASE_ORDER_LOG_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PREVIOUS_PACKAGING_QTY` decimal(16,6) DEFAULT NULL,
  `ACCEPTED_PACKAGING_QTY` decimal(16,6) DEFAULT NULL,
  `PURCHASE_ORDER_ITEM_DATA_ID` int(11) DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `LAST_UPDATED_BY` int(11) DEFAULT NULL,
  PRIMARY KEY (`PURCHASE_ORDER_LOG_ID`)
);

  CREATE TABLE `kettle_scm_dev`.`stock_take_app_versions` (
    `ID` INT NOT NULL AUTO_INCREMENT,
    `VERSION_NO` VARCHAR(45) NOT NULL,
    PRIMARY KEY (`ID`));
ALTER TABLE KETTLE_SCM_DEV.ADDITIONAL_DOCUMENTS_MASTER
CHANGE COLUMN CREATED_DATE CREATED_DATE DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ,
CHANGE COLUMN LAST_UPDATED_DATE LAST_UPDATED_DATE DATETIME NULL DEFAULT NULL ;
ALTER TABLE KETTLE_SCM_DEV.ADDITIONAL_DOCUMENTS_MASTER
CHANGE COLUMN CREATED_BY CREATED_BY VARCHAR(255) NOT NULL ;


ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD MANDATORY_REQUIRED_DOCUMENTS VARCHAR(400);

INSERT INTO KETTLE_SCM_DEV.PAYMENT_DEVIATION
( DEVIATION_CODE, DEVIATION_TYPE, DEVIATION_LEVEL, DEVIATION_DETAIL)
 VALUES ( '034', 'REJECTION', 'INVOICE', 'Invalid/Missing Mandatory Documents');

CREATE TABLE `kettle_scm_dev`.`purchase_order_log` (
`PURCHASE_ORDER_LOG_ID` int(11) NOT NULL AUTO_INCREMENT,
  `PREVIOUS_PACKAGING_QTY` decimal(16,6) DEFAULT NULL,
  `ACCEPTED_PACKAGING_QTY` decimal(16,6) DEFAULT NULL,
  `PURCHASE_ORDER_ITEM_DATA_ID` int(11) DEFAULT NULL,
  `LAST_UPDATE_TIME` timestamp NULL DEFAULT NULL,
  `LAST_UPDATED_BY` int(11) DEFAULT NULL,
  PRIMARY KEY (`PURCHASE_ORDER_LOG_ID`)
);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('HOD_GROUP_GOODS', 'Head Of the Department', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('HODGGRP', '7', 'ACTION', 'APPROVE', 'SuMo -> Approval For Payments,Sos,Pos related to advance payment', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'HOD_GROUP_GOODS'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'HODGGRP'), 'ACTIVE', '120063', '2023-05-16 12:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('HOD_GROUP_SERVICES', 'Head Of the Department', 'ACTIVE', '12');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('HODSGRP', '12', 'ACTION', 'APPROVE', 'SuMo -> Approval For Payments,Sos,Pos related to advance payment', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'HOD_GROUP_SERVICES'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'HODSGRP'), 'ACTIVE', '120063', '2023-05-16 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VBLK', '7', 'ACTION', 'UPDATE', 'SuMo -> Goods ->Vendor Management -> Vendors', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_HEAD'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VBLK'), 'ACTIVE', '120063', '2023-05-16 12:00:00');


ALTER TABLE `KETTLE_SCM_DEV`.`VENDOR_DETAIL_DATA`
ADD COLUMN `LAST_BLOCKED_DATE` TIMESTAMP NULL DEFAULT NULL AFTER `UN_BLOCKED_TILL_DATE`,
ADD COLUMN `LAST_BLOCKED_BY` INT(11) NULL DEFAULT NULL AFTER `LAST_BLOCKED_DATE`,
ADD COLUMN `LAST_UN_BLOCKED_DATE` TIMESTAMP NULL DEFAULT NULL AFTER `LAST_BLOCKED_BY`,
ADD COLUMN `LAST_UN_BLOCKED_BY` INT(11) NULL DEFAULT NULL AFTER `LAST_UN_BLOCKED_DATE`;


ALTER TABLE KETTLE_SCM.INVENTORY_DRILLDOWN
ADD COLUMN REVERSE_CONSUMPTION DECIMAL(16,10),
ADD COLUMN REVERSE_BOOKED DECIMAL(16,10);

ALTER TABLE `kettle_scm_dev`.`stock_event_definition`
ADD COLUMN `LAST_UPDATION_TIME` DATETIME NULL DEFAULT NULL AFTER `RECEIVING_UNIT_ID`;
ALTER TABLE KETTLE_SCM.PAYMENT_REQUEST
ADD COLUMN `LAST_QUERIED_DATE` TIMESTAMP NULL DEFAULT NULL AFTER `MANDATORY_REQUIRED_DOCUMENTS`,
ADD COLUMN `LAST_QUERY_RESOLVED_DATE` TIMESTAMP NULL DEFAULT NULL AFTER `LAST_QUERIED_DATE`;

CREATE TABLE KETTLE_SCM.PAYMENT_REQUEST_QUERY (
`PR_QUERY_ID` int(11) NOT NULL AUTO_INCREMENT,
`PR_ID` int(11) DEFAULT NULL,
`PAYMENT_DEVIATION_ID` int(11) NOT NULL,
`QUERY_RAISED_BY` int(11) NOT NULL,
`QUERY_RESOLVED_BY` int(11) DEFAULT NULL,
`RAISED_BY_COMMENT` varchar(500) DEFAULT NULL,
`RESOLVED_BY_COMMENT` varchar(500) DEFAULT NULL,
`UPLOADED_DOCUMENT_ID` int(11) DEFAULT NULL,
`QUERIED_FOR_PR_ID` int(11) DEFAULT NULL,
PRIMARY KEY (`PR_QUERY_ID`)
);

CREATE TABLE  KETTLE_SCM_DEV.ZIP_CODE_DISTANCE_MAPPING(
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `SOURCE_ZIP_CODE` varchar(255) DEFAULT NULL,
  `DESTINATION_ZIP_CODE` varchar(255) DEFAULT NULL,
  `DISTANCE` decimal(19,2) DEFAULT NULL,
  PRIMARY KEY (`ID`)
);
ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_ORDER`
ADD COLUMN `APPROVAL_OF_HOD` INT(20) NULL DEFAULT NULL AFTER `ADVANCE_PAYMENT_ID`;




ALTER TABLE `kettle_scm_dev`.`asset_definition_log`
ADD COLUMN `ASSET_NAME` VARCHAR(45) NULL DEFAULT NULL AFTER `ASSET_STATUS`;

ALTER TABLE `KETTLE_SCM_DEV`.`DAY_CLOSE_EVENT`
ADD COLUMN `VARIANCE_STATUS` VARCHAR(15) NULL DEFAULT NULL AFTER `IS_ORDERING_SUCCESS`,
ADD COLUMN `VARIANCE_UPDATED_BY` INT NULL DEFAULT NULL AFTER `VARIANCE_STATUS`;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_INVENTORY`
ADD COLUMN `ORIGINAL_CLOSING_STOCK` DECIMAL(16,6) NULL DEFAULT '0.000000' AFTER `CLOSING_STOCK`,
ADD COLUMN `ORIGINAL_VARIANCE` DECIMAL(16,6) NULL DEFAULT '0.000000' AFTER `VARIANCE`,
ADD COLUMN `ORIGINAL_VARIANCE_COST` DECIMAL(16,6) NULL DEFAULT NULL AFTER `VARIANCE_COST`,
ADD COLUMN `ORIGINAL_TAX_VARIANCE` DECIMAL(10,2) NULL DEFAULT NULL AFTER `TAX_VARIANCE`;

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('ASSET EDITOR', 'ASSET EDITOR', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ASTEDT', '7', 'ACTION', 'APPROVE', 'SuMo -> asset-management -> asset-list', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'ASSET EDITOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ASTEDT'), 'ACTIVE', '120063', '2023-06-05 12:00:00');

CREATE TABLE `KETTLE_SCM_DEV`.`VARIANCE_EDIT_AGGREGATED_DATA` (
`VARIANCE_EDIT_AGGREGATED_DATA_ID` int(11) NOT NULL AUTO_INCREMENT,
`DAY_CLOSE_EVENT_ID` int(11) NOT NULL,
`AGGREGATE_COST` decimal(16,6) NOT NULL,
`AGGREGATE_TAX` decimal(16,6) NOT NULL,
PRIMARY KEY (`VARIANCE_EDIT_AGGREGATED_DATA_ID`)
) ;

ALTER TABLE `KETTLE_SCM_DEV`.`LIST_DETAIL`
    ADD COLUMN `EMAIL` VARCHAR(45) NULL DEFAULT NULL AFTER `BUDGET_CATEGORY`;

ALTER TABLE `KETTLE_SCM_DEV`.`ADVANCE_PAYMENT_DATA`
ADD COLUMN `ADVANCE_REFUND_DOCUMENT` INT NULL DEFAULT NULL AFTER `MAX_SETTLEMENT_TIME`;



CREATE TABLE KETTLE_SCM.REVERSE_BOOKING_CONSUMPTION (
  BOOKING_CONSUMPTION_ID int(11) NOT NULL AUTO_INCREMENT,
  SKU_ID int(11) NOT NULL,
  SKU_NAME varchar(255) NOT NULL,
  UOM varchar(255) NOT NULL,
  CALCULATED_QUANTITY decimal(16,9) DEFAULT NULL,
  UNIT_PRICE decimal(16,9) DEFAULT NULL,
  TOTAL_COST decimal(16,9) DEFAULT NULL,
  PRODUCTION_BOOK_ID int(11) NOT NULL,
  PRIMARY KEY (BOOKING_CONSUMPTION_ID),
  KEY FK_REVERSE_BOOKING_CONSUMPTION_ON_PRODUCTION_BOOK (PRODUCTION_BOOK_ID),
  CONSTRAINT FK_REVERSE_BOOKING_CONSUMPTION_ON_PRODUCTION_BOOK FOREIGN KEY (PRODUCTION_BOOK_ID) REFERENCES REVERSE_PRODUCTION_BOOKING (BOOKING_ID)
);

CREATE TABLE KETTLE_SCM.REVERSE_BOOKING_CONSUMPTION_ITEM_DRILLDOWN (
  CONSUMPTION_DRILLDOWN_ID int(11) NOT NULL AUTO_INCREMENT,
  BOOKING_CONSUMPTION_ID int(11) DEFAULT NULL,
  QUANTITY decimal(16,9) DEFAULT NULL,
  PRICE decimal(16,9) DEFAULT NULL,
  ADD_TIME datetime NOT NULL,
  EXPIRY_DATE datetime NOT NULL,
  PRIMARY KEY (CONSUMPTION_DRILLDOWN_ID),
  KEY FK_REVERSEBOOKINGCONSUMPTIONITEMDRILLDOWN_ON_BOOKINGCONSUMPTION (BOOKING_CONSUMPTION_ID),
  CONSTRAINT FK_REVERSEBOOKINGCONSUMPTIONITEMDRILLDOWN_ON_BOOKINGCONSUMPTION FOREIGN KEY (BOOKING_CONSUMPTION_ID) REFERENCES REVERSE_BOOKING_CONSUMPTION (BOOKING_CONSUMPTION_ID)
);

CREATE TABLE KETTLE_SCM.REVERSE_PRODUCTION_BOOKING (
  BOOKING_ID int(11) NOT NULL AUTO_INCREMENT,
  PRODUCT_ID int(11) NOT NULL,
  PRODUCT_NAME varchar(255) NOT NULL,
  SKU_ID int(11) NOT NULL,
  UOM varchar(255) NOT NULL,
  QUANTITY decimal(10,9) NOT NULL,
  UNIT_ID int(11) NOT NULL,
  UNIT_PRICE decimal(10,9) DEFAULT NULL,
  TOTAL_COST decimal(10,9) DEFAULT NULL,
  GENERATION_TIME datetime NOT NULL,
  CANCELLATION_TIME datetime DEFAULT NULL,
  CLOSURE_TIME datetime DEFAULT NULL,
  GENERATED_BY int(11) NOT NULL,
  CANCELLED_BY int(11) DEFAULT NULL,
  BOOKING_STATUS varchar(255) DEFAULT NULL,
  CLOSURE_EVENT_ID int(11) DEFAULT NULL,
  EXPIRY_DATE datetime DEFAULT NULL,
  PROFILE varchar(255) DEFAULT NULL,
  AUTO_BOOKING varchar(1) NOT NULL,
  PRIMARY KEY (BOOKING_ID)
);

CREATE TABLE KETTLE_SCM.REVERSE_PRODUCTION_BOOKING_MAPPING (
  PRODUCTION_BOOKING_ID int(11) NOT NULL AUTO_INCREMENT,
  PRODUCT_ID int(11) NOT NULL,
  PRODUCT_NAME varchar(255) DEFAULT NULL,
  SKU_ID int(11) DEFAULT NULL,
  UNIT_OF_MEASURE varchar(255) DEFAULT NULL,
  QUANTITY decimal(10,9) DEFAULT NULL,
  UNIT_ID int(11) NOT NULL,
  GENERATION_TIME datetime DEFAULT NULL,
  CANCELLATION_TIME datetime DEFAULT NULL,
  GENERATED_BY int(11) DEFAULT NULL,
  CANCELLED_BY int(11) DEFAULT NULL,
  MAPPING_STATUS varchar(255) DEFAULT NULL,
  PROFILE varchar(255) DEFAULT NULL,
  LINKED_SKU_ID int(11) NOT NULL,
  LINKED_SKU_NAME varchar(255) DEFAULT NULL,
  LINKED_UNIT_OF_MEASURE varchar(255) DEFAULT NULL,
  LINKED_QUANTITY decimal(10,9) DEFAULT NULL,
  AUTO_PRODUCTION varchar(255) DEFAULT NULL,
  PRIMARY KEY (PRODUCTION_BOOKING_ID)
);



ALTER TABLE KETTLE_SCM.REVERSE_CONSUMPTION
ADD COLUMN REVERSE_CONSUMPTION DECIMAL(16,10),
ADD COLUMN REVERSE_BOOKED DECIMAL(16,10);

ALTER TABLE KETTLE_SCM.PAYMENT_REQUEST
ADD COLUMN `LAST_QUERIED_DATE` TIMESTAMP NULL DEFAULT NULL AFTER `MANDATORY_REQUIRED_DOCUMENTS`,
ADD COLUMN `LAST_QUERY_RESOLVED_DATE` TIMESTAMP NULL DEFAULT NULL AFTER `LAST_QUERIED_DATE`;

CREATE TABLE KETTLE_SCM.PAYMENT_REQUEST_QUERY (
`PR_QUERY_ID` int(11) NOT NULL AUTO_INCREMENT,
`PR_ID` int(11) DEFAULT NULL,
`PAYMENT_DEVIATION_ID` int(11) NOT NULL,
`QUERY_RAISED_BY` int(11) NOT NULL,
`QUERY_RESOLVED_BY` int(11) DEFAULT NULL,
`RAISED_BY_COMMENT` varchar(500) DEFAULT NULL,
`RESOLVED_BY_COMMENT` varchar(500) DEFAULT NULL,
`UPLOADED_DOCUMENT_ID` int(11) DEFAULT NULL,
`QUERIED_FOR_PR_ID` int(11) DEFAULT NULL,
PRIMARY KEY (`PR_QUERY_ID`)
);

CREATE TABLE  KETTLE_SCM_DEV.ZIP_CODE_DISTANCE_MAPPING(
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `SOURCE_ZIP_CODE` varchar(255) DEFAULT NULL,
  `DESTINATION_ZIP_CODE` varchar(255) DEFAULT NULL,
  `DISTANCE` decimal(19,2) DEFAULT NULL,
  PRIMARY KEY (`ID`)
);
ALTER TABLE `KETTLE_SCM_DEV`.`SERVICE_ORDER`
ADD COLUMN `APPROVAL_OF_HOD` INT(20) NULL DEFAULT NULL AFTER `ADVANCE_PAYMENT_ID`;

ALTER TABLE `kettle_scm_dev`.`asset_definition_log`
ADD COLUMN `ASSET_NAME` VARCHAR(45) NULL DEFAULT NULL AFTER `ASSET_STATUS`;

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('ASSET EDITOR', 'ASSET EDITOR', 'ACTIVE', '7');

CREATE TABLE `KETTLE_SCM_DEV`.`DAY_CLOSE_FREQUENCY_MISMATCH_PRODUCTS` (
`DAY_CLOSE_FREQUENCY_MISMATCH_ID` int(11) NOT NULL AUTO_INCREMENT,
`EVENT_ID` int(11) NOT NULL,
`UNIT_ID` int(11) NOT NULL,
`PRODUCT_ID` int(11) NOT NULL,
`TYPE` varchar(45) NOT NULL,
`START_ID` int(11) NOT NULL,
`END_ID` int(11) NOT NULL,
`STOCK_TYPE` varchar(50) NOT NULL,
PRIMARY KEY (`DAY_CLOSE_FREQUENCY_MISMATCH_ID`)
);

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ASTEDT', '7', 'ACTION', 'APPROVE', 'SuMo -> asset-management -> asset-list', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'ASSET EDITOR'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ASTEDT'), 'ACTIVE', '120063', '2023-06-05 12:00:00');



CREATE TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_CHANGE_REQUEST (
                                                             `VENDOR_DETAIL_CHANGE_REQUEST_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                             `VENDOR_ID` INT(11) NOT NULL,
                                                             PRIMARY KEY (`VENDOR_DETAIL_CHANGE_REQUEST_ID`),
                                                             UNIQUE INDEX `VENDOR_DETAIL_CHANGE_REQUEST_ID_UNIQUE` (`VENDOR_DETAIL_CHANGE_REQUEST_ID` ASC));

CREATE TABLE  KETTLE_SCM_DEV.VENDOR_DETAIL_CHANGE_REQUEST_DATA (
                                                                   `VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                                   `VENDOR_DETAIL_CHANGE_REQUEST_ID` INT(11) NOT NULL,
                                                                   `VENDOR_ID` INT(11) NOT NULL,
                                                                   `TABLE_NAME` VARCHAR(45) NOT NULL,
                                                                   `FIELD_NAME` VARCHAR(45) NOT NULL,
                                                                   `COMMENT` VARCHAR(500) NULL DEFAULT NULL,
                                                                   `DISPATCH_ID` INT(11) NULL DEFAULT NULL,
                                                                   PRIMARY KEY (`VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID`),
                                                                   UNIQUE INDEX `VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID_UNIQUE` (`VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID` ASC));



ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA
    ADD COLUMN `CHANGE_REQUEST_ID` INT(11) NULL DEFAULT NULL AFTER `LAST_UN_BLOCKED_BY`

ALTER TABLE `KETTLE_SCM_DEV`.`DAY_CLOSE_EVENT`
ADD COLUMN `VARIANCE_STATUS` VARCHAR(15) NULL DEFAULT NULL AFTER `IS_ORDERING_SUCCESS`,
ADD COLUMN `VARIANCE_UPDATED_BY` INT NULL DEFAULT NULL AFTER `VARIANCE_STATUS`;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_INVENTORY`
ADD COLUMN `ORIGINAL_CLOSING_STOCK` DECIMAL(16,6) NULL DEFAULT '0.000000' AFTER `CLOSING_STOCK`,
ADD COLUMN `ORIGINAL_VARIANCE` DECIMAL(16,6) NULL DEFAULT '0.000000' AFTER `VARIANCE`,
ADD COLUMN `ORIGINAL_VARIANCE_COST` DECIMAL(16,6) NULL DEFAULT NULL AFTER `VARIANCE_COST`,
ADD COLUMN `ORIGINAL_TAX_VARIANCE` DECIMAL(10,2) NULL DEFAULT NULL AFTER `TAX_VARIANCE`;

CREATE TABLE `KETTLE_SCM_DEV`.`VARIANCE_EDIT_AGGREGATED_DATA` (
`VARIANCE_EDIT_AGGREGATED_DATA_ID` int(11) NOT NULL AUTO_INCREMENT,
`DAY_CLOSE_EVENT_ID` int(11) NOT NULL,
`AGGREGATE_COST` decimal(16,6) NOT NULL,
`AGGREGATE_TAX` decimal(16,6) NOT NULL,
PRIMARY KEY (`VARIANCE_EDIT_AGGREGATED_DATA_ID`)
) ;

ALTER TABLE `KETTLE_SCM_DEV`.`LIST_DETAIL`
    ADD COLUMN `EMAIL` VARCHAR(45) NULL DEFAULT NULL AFTER `BUDGET_CATEGORY`;

ALTER TABLE `KETTLE_SCM_DEV`.`VENDOR_SEQUENCE_ID`
ADD COLUMN `FINANCIAL_YEAR` INT(4) NULL DEFAULT NULL AFTER `ID_TYPE`;

ALTER TABLE `KETTLE_SCM_DEV`.`STATE_SEQUENCE_ID`
ADD COLUMN `FINANCIAL_YEAR` INT(4) NULL DEFAULT NULL AFTER `ID_TYPE`;


ALTER TABLE `KETTLE_SCM_DEV`.`ADVANCE_PAYMENT_DATA`
ADD COLUMN `ADVANCE_REFUND_DOCUMENT` INT NULL DEFAULT NULL AFTER `MAX_SETTLEMENT_TIME`;


ALTER TABLE KETTLE_SCM_DEV.GATEPASS_ITEM_ASSET_MAPPING
    ADD COLUMN IS_RETURNED VARCHAR(1) NULL DEFAULT NULL AFTER `GATEPASS_TYPE`;

ALTER TABLE `KETTLE_SCM_DEV`.`DAY_CLOSURE_TXN_EVENT_MAPPING`
CHANGE COLUMN `EVENT_TYPE` `EVENT_TYPE` VARCHAR(25) NOT NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`SCM_TRANSACTION_EVENT_DRILLDOWN`
CHANGE COLUMN `EVENT_TYPE` `EVENT_TYPE` VARCHAR(25) NOT NULL ;

ALTER TABLE `KETTLE_SCM_DEv`.`REVERSE_BOOKING_CONSUMPTION`
ADD COLUMN `WASTAGE_QUANTITY` DECIMAL(20,6) NULL DEFAULT NULL AFTER `TOTAL_COST`;

ALTER TABLE `KETTLE_SCM_DEV`.`REVERSE_PRODUCTION_BOOKING`
ADD COLUMN `WASTAGE_ID` INT(11) NULL DEFAULT NULL AFTER `AUTO_BOOKING`;

ALTER TABLE KETTLE_SCM_DEV.GATEPASS_ITEM_ASSET_MAPPING
    ADD COLUMN IS_RETURNED VARCHAR(1) NULL DEFAULT NULL AFTER `GATEPASS_TYPE`;



CREATE TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_CHANGE_REQUEST (
                                                             `VENDOR_DETAIL_CHANGE_REQUEST_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                             `VENDOR_ID` INT(11) NOT NULL,
                                                             PRIMARY KEY (`VENDOR_DETAIL_CHANGE_REQUEST_ID`),
                                                             UNIQUE INDEX `VENDOR_DETAIL_CHANGE_REQUEST_ID_UNIQUE` (`VENDOR_DETAIL_CHANGE_REQUEST_ID` ASC));

CREATE TABLE  KETTLE_SCM_DEV.VENDOR_DETAIL_CHANGE_REQUEST_DATA (
                                                                   `VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                                   `VENDOR_DETAIL_CHANGE_REQUEST_ID` INT(11) NOT NULL,
                                                                   `VENDOR_ID` INT(11) NOT NULL,
                                                                   `TABLE_NAME` VARCHAR(45) NOT NULL,
                                                                   `FIELD_NAME` VARCHAR(45) NOT NULL,
                                                                   `COMMENT` VARCHAR(500) NULL DEFAULT NULL,
                                                                   `DISPATCH_ID` INT(11) NULL DEFAULT NULL,
                                                                   PRIMARY KEY (`VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID`),
                                                                   UNIQUE INDEX `VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID_UNIQUE` (`VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID` ASC));



ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA
    ADD COLUMN `CHANGE_REQUEST_ID` INT(11) NULL DEFAULT NULL AFTER `LAST_UN_BLOCKED_BY`

CREATE TABLE switch_asset_data (
  ID bigint(20) NOT NULL AUTO_INCREMENT,
  NEW_ASSET_ID int(11) NOT NULL,
  NEW_ASSET_COMMENTS varchar(255) DEFAULT NULL,
  NEW_ASSET_TO_ID int(11) NOT NULL,
  NEW_ASSET_GR_ID int(11) NOT NULL,
  OLD_ASSET_ID int(11) NOT NULL,
  OLD_ASSET_COMMENTS varchar(255) DEFAULT NULL,
  OLD_ASSET_TO_ID int(11) NOT NULL,
  OLD_ASSET_GR_ID int(11) NOT NULL,
  SWITCH_REASON varchar(255) DEFAULT NULL,
  CREATED_BY int(11) NOT NULL,
  REQUESTED_BY int(11) NOT NULL,
  CREATED_BY_UNIT int(11) NOT NULL,
  REQUESTING_UNIT int(11) NOT NULL,
  TICKET_ID varchar(255) NOT NULL,
  AWS_BUCKET varchar(255) DEFAULT NULL,
  AWS_KEY varchar(255) DEFAULT NULL,
  STATUS varchar(255) NOT NULL,
  IS_EMAIL_SENT varchar(255) NOT NULL,
  PRIMARY KEY (ID)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=latin1;

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('STOCK TAKE APP SWITCH ASSETS', 'ACCESS TO STOCK TAKE APP SWITCH ASSETS', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('SWASTA', '7', 'MENU', 'SHOW', 'STOCK TAKE -> SWITCH ASSET', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'STOCK TAKE APP SWITCH ASSETS'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'SWASTA'), 'ACTIVE', '120063', '2023-07-10 12:00:00');

ALTER TABLE KETTLE_SCM_DEV.GATEPASS_ITEM_ASSET_MAPPING
    ADD COLUMN IS_RETURNED VARCHAR(1) NULL DEFAULT NULL AFTER `GATEPASS_TYPE`;

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('rfrsdclose', '7', 'ACTION', 'VIEW', 'SuMo ->update inventory -> re-initiate kettle day close', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SUMO ADMIN'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'rfrsdclose'), 'ACTIVE', '120063', '2023-07-10 12:00:00');





CREATE TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_CHANGE_REQUEST (
                                                             `VENDOR_DETAIL_CHANGE_REQUEST_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                             `VENDOR_ID` INT(11) NOT NULL,
                                                             PRIMARY KEY (`VENDOR_DETAIL_CHANGE_REQUEST_ID`),
                                                             UNIQUE INDEX `VENDOR_DETAIL_CHANGE_REQUEST_ID_UNIQUE` (`VENDOR_DETAIL_CHANGE_REQUEST_ID` ASC));

CREATE TABLE  KETTLE_SCM_DEV.VENDOR_DETAIL_CHANGE_REQUEST_DATA (
                                                                   `VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                                   `VENDOR_DETAIL_CHANGE_REQUEST_ID` INT(11) NOT NULL,
                                                                   `VENDOR_ID` INT(11) NOT NULL,
                                                                   `TABLE_NAME` VARCHAR(45) NOT NULL,
                                                                   `FIELD_NAME` VARCHAR(45) NOT NULL,
                                                                   `COMMENT` VARCHAR(500) NULL DEFAULT NULL,
                                                                   `DISPATCH_ID` INT(11) NULL DEFAULT NULL,
                                                                   PRIMARY KEY (`VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID`),
                                                                   UNIQUE INDEX `VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID_UNIQUE` (`VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID` ASC));



ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA
ADD COLUMN `CHANGE_REQUEST_ID` INT(11) NULL DEFAULT NULL AFTER `LAST_UN_BLOCKED_BY`;

CREATE TABLE `KETTLE_SCM_DEV`.`EMPLOYEE_PAYMENT_CARD_MAPPING`
(
    `EMPLOYEE_PAYMENT_CARD_MAPPING_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `EMPLOYEE_ID`                      INT(11) NOT NULL,
    `PAYMENT_CARD`                     VARCHAR(255) NOT NULL,
    `MAPPING_STATUS`                   VARCHAR(15)  NOT NULL,
    PRIMARY KEY (`EMPLOYEE_PAYMENT_CARD_MAPPING_ID`)
);


ALTER TABLE `KETTLE_SCM_DEV`.`PAYMENT_REQUEST`
ADD COLUMN `PAYMENT_CARD` VARCHAR(255) NULL DEFAULT NULL AFTER `LAST_QUERY_RESOLVED_DATE`,
ADD COLUMN `CARD_PAYMENT_TRANSACTION_NUMBER` VARCHAR(255) NULL DEFAULT NULL AFTER `PAYMENT_CARD`,
ADD COLUMN `CARD_PAYMENT_PROOF` INT NULL DEFAULT NULL AFTER `CARD_PAYMENT_TRANSACTION_NUMBER`,
ADD COLUMN `CARD_PAYMENT_COMMENT` VARCHAR(255) NULL DEFAULT NULL AFTER `CARD_PAYMENT_PROOF`;


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA` (`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR_INVOICING_CANCELLATION_APPROVAL', 'VENDOR_INVOICING_CANCELLATION_APPROVER', 'ACTIVE', '7');
ALTER TABLE `KETTLE_SCM_DEV`.`VENDOR_SEQUENCE_ID`
ADD COLUMN `FINANCIAL_YEAR` INT(4) NOT NULL AFTER `NEXT_VALUE`;


INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES
('VIPDCA', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING -> APPROVE INVOICE -> PENDING DISPATCH CACELLATION APPROVAL -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR_INVOICING_CANCELLATION_APPROVAL'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VIPDCA'), 'ACTIVE', '120063', '2023-07-10 12:00:00');

ALTER TABLE `KETTLE_SCM_DEV`.`SALES_PERFORMA_INVOICE`
    ADD COLUMN `DELIVERY_DATE` DATE NULL DEFAULT NULL AFTER `BILLING_LOCATION_ID`;

ALTER TABLE `KETTLE_SCM_DEV`.`SALES_PERFORMA_STATUS_EVENT`
    CHANGE COLUMN `FROM_STATUS` `FROM_STATUS` VARCHAR(50) NOT NULL ,
    CHANGE COLUMN `TO_STATUS` `TO_STATUS` VARCHAR(50) NOT NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`SALES_PERFORMA_INVOICE`
    CHANGE COLUMN `INVOICE_STATUS` `INVOICE_STATUS` VARCHAR(50) NOT NULL ;

ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_EVENT_DEFINITION`
ADD COLUMN `GR_ID` INT(11) NULL DEFAULT NULL AFTER `TO_ID`;

CREATE TABLE KETTLE_SCM_DEV.FA_GR_DATA (
   FA_GR_DATA_ID INT AUTO_INCREMENT NOT NULL,
   ASSET_ID INT NULL,
   RECEIVED_QTY DECIMAL NULL,
   SCANNED_QTY DECIMAL NULL,
   EVENT_ID INT NULL,
   CONSTRAINT PK_FA_GR_DATA PRIMARY KEY (FA_GR_DATA_ID)
);


ALTER TABLE `KETTLE_SCM_DEV`.`STOCK_EVENT_DEFINITION`
ADD COLUMN `GR_ID` INT(11) NULL DEFAULT NULL AFTER `TO_ID`;

CREATE TABLE KETTLE_SCM_DEV.FA_GR_DATA (
   FA_GR_DATA_ID INT AUTO_INCREMENT NOT NULL,
   ASSET_ID INT NULL,
   RECEIVED_QTY DECIMAL NULL,
   SCANNED_QTY DECIMAL NULL,
   EVENT_ID INT NULL,
   CONSTRAINT PK_FA_GR_DATA PRIMARY KEY (FA_GR_DATA_ID)
);

ALTER TABLE KETTLE_MASTER_STAGE.UNIT_DETAIL
    ADD COLUMN `VARIANCE_ACKNOWLEDGEMENT_REQUIRED` VARCHAR(1) NULL DEFAULT NULL AFTER `LAST_HANDOVER_FROM`;

CREATE TABLE KETTLE_SCM_STAGE.VARIANCE_ACKNOWLEDGEMENT (
                                                           `ID` int(11) NOT NULL AUTO_INCREMENT,
                                                           `BUSINESS_DATE` date DEFAULT NULL,
                                                           `GENERATION_TIME` datetime NOT NULL,
                                                           `UNIT_ID` int(11) NOT NULL,
                                                           `INVENTORY_COST` decimal(16,6) NOT NULL,
                                                           `VARIANCE_COST` decimal(16,6) NOT NULL,
                                                           `VARIANCE_PERCENTAGE` decimal(16,6) NOT NULL,
                                                           `ACKNOWLEDGED` varchar(1) NOT NULL,
                                                           `FREQUENCY` varchar(45) DEFAULT NULL,
                                                           `ACKNOWLEDGED_BY` int(11) DEFAULT NULL,
                                                           `ACKNOWLEDGED_TIME` datetime DEFAULT NULL,
                                                           `CURRENT_EVENT_ID` int(20) DEFAULT NULL,
                                                           `ACKNOWLEDGEMENT_REQUIRED` varchar(1) DEFAULT NULL,
                                                           `ACKNOWLEDGEMENT_TYPE` varchar(45) DEFAULT NULL,
                                                           `COMMENT` varchar(500) DEFAULT NULL,
                                                           PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=504 DEFAULT CHARSET=latin1

CREATE TABLE `KETTLE_SCM_DEV`.`CATEGORY_SUB_CATEGORY_DEBIT_NOTE`
(
    `CATEGORY_SUB_CATEGORY_DEBIT_NOTE_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `CATEGORY_ID`                         INT(11) NOT NULL,
    `SUB_CATEGORY_ID`                     INT(11) NOT NULL,
    `AMOUNT`                              DECIMAL(16, 6) NOT NULL,
    `PAYMENT_REQUEST_ID`                  INT(11) NOT NULL,
    `DEBIT_NOTE_ID`                       INT(11) NOT NULL,
    PRIMARY KEY (`CATEGORY_SUB_CATEGORY_DEBIT_NOTE_ID`),
    UNIQUE KEY `CATEGORY_SUB_CATEGORY_DEBIT_NOTE_ID_UNIQUE` (`CATEGORY_SUB_CATEGORY_DEBIT_NOTE_ID`),
    KEY                                   `CATEGORY_ID_CATEGORY_SUB_CATEGORY_DEBIT_NOTE` (`CATEGORY_ID`) USING BTREE,
    KEY                                   `SUB_CATEGORY_ID_CATEGORY_SUB_CATEGORY_DEBIT_NOTE` (`SUB_CATEGORY_ID`) USING BTREE,
    KEY                                   `AMOUNT_CATEGORY_SUB_CATEGORY_DEBIT_NOTE` (`AMOUNT`) USING BTREE,
    KEY                                   `PAYMENT_REQUEST_ID_CATEGORY_SUB_CATEGORY_DEBIT_NOTE` (`PAYMENT_REQUEST_ID`) USING BTREE,
    KEY                                   `DEBIT_NOTE_ID_CATEGORY_SUB_CATEGORY_DEBIT_NOTE` (`DEBIT_NOTE_ID`) USING BTREE

);


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE PENDING APPROVER L1', 'Approve vendor invoice level 1 ', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VIFAL1', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING ->  PENDING APPROVAL L1 -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE PENDING APPROVER L1'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VIFAL1'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE RETURN_TO_VENDOR APPROVER L2', 'Approve vendor invoice of type RETURN_TO_VENDOR level 2 ', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VIRVFAL2', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING ->  PENDING APPROVAL L2 -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE RETURN_TO_VENDOR APPROVER L2'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VIRVFAL2'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE SCRAP APPROVER L2', 'Approve vendor invoice of type Scrap level 2 ', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VISFAL2', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING ->  PENDING APPROVAL L2 -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE SCRAP APPROVER L2'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VISFAL2'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');

CREATE TABLE SALES_PERFORMA_INVOICE_CREDIT_DEBIT_NOTE (
                                                            `ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                            `INVOICE_ID` VARCHAR(1000) NOT NULL,
                                                            `VENDOR_ID` INT(11) NOT NULL,
                                                            `STATUS` VARCHAR(45) NOT NULL,
                                                            `CREATED_BY` INT(11) NOT NULL,
                                                            `GENERATION_TIME` DATE NOT NULL,
                                                            `APPROVAL_REQUIRED` VARCHAR(1) NOT NULL,
                                                            `APPROVED_BY` INT(11) DEFAULT NULL,
                                                            `UPDATED_AT` DATETIME DEFAULT NULL,
                                                            `CREDIT_NOTE_ID` VARCHAR(45) DEFAULT NULL,
                                                            `CREDIT_NOTE_DOC_ID` INT(11) DEFAULT NULL,
                                                            `CREDIT_NOTE_DOC_URL` VARCHAR(500) DEFAULT NULL,
                                                            `TOTAL_TAX` DECIMAL(15,4) NOT NULL,
                                                            `TOTAL_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                            `NET_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                            `INVOICE_DOC_URL` VARCHAR(500) DEFAULT NULL,
                                                            `INVOICE_DATE` DATE NOT NULL,
                                                            `VENDOR_INVOICE_NUMBER` VARCHAR(100) NOT NULL,
                                                            PRIMARY KEY (`ID`)
) ENGINE=INNODB AUTO_INCREMENT=48 DEFAULT CHARSET=LATIN1;

CREATE TABLE SALES_PERFORMA_INVOICE_CREDIT_DEBIT_NOTE_ITEM (
                                                                 `ITEM_ID` INT(11) NOT NULL AUTO_INCREMENT,
                                                                 `REFERENCE_ID` INT(11) NOT NULL,
                                                                 `ITEM_DESC` VARCHAR(500) NOT NULL,
                                                                 `QUANTITY` DECIMAL(15,4) NOT NULL,
                                                                 `PRICE` DECIMAL(15,4) NOT NULL,
                                                                 `NET_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                                 `TAX_PERCENT` DECIMAL(15,4) NOT NULL,
                                                                 `TAX_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                                 `TOTAL_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                                 PRIMARY KEY (`ITEM_ID`)
) ENGINE=INNODB AUTO_INCREMENT=49 DEFAULT CHARSET=LATIN1;

CREATE TABLE SALES_PERFORMA_CORRECTED_INVOICE (
                                                    `INVOICE_ID` INT(11) NOT NULL,
                                                    `ADDITIONAL_CHARGES` DECIMAL(15,4) DEFAULT NULL,
                                                    `TOTAL_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                    `TOTAL_CORRECTED_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                    `TOTAL_SELLING_COST` DECIMAL(15,4) NOT NULL,
                                                    `TOTAL_CORRECTED_SELLING_COST` DECIMAL(15,4) NOT NULL,
                                                    `TOTAL_TAX` DECIMAL(15,4) NOT NULL,
                                                    `TOTAL_CORRECTED_TAX` DECIMAL(15,4) NOT NULL,
                                                    PRIMARY KEY (`INVOICE_ID`)
) ENGINE=INNODB DEFAULT CHARSET=LATIN1;

CREATE TABLE SALES_PERFORMA_CORRECTED_INVOICE_ITEM (
                                                         `INVOICE_ITEM_ID` INT(11) NOT NULL,
                                                         `SKU_ID` INT(11) NOT NULL,
                                                         `SKU_NAME` VARCHAR(100) NOT NULL,
                                                         `SELLING_PRICE` DECIMAL(15,4) NOT NULL,
                                                         `CORRECTED_SELLING_PRICE` DECIMAL(15,4) NOT NULL,
                                                         `SELLING_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                         `CORRECTED_SELLING_AMOUNT` DECIMAL(15,4) NOT NULL,
                                                         `TOTAL_TAX` DECIMAL(15,4) NOT NULL,
                                                         `CORRECTED_TOTAL_TAX` DECIMAL(15,4) NOT NULL,
                                                         `INVOICE_ID` INT(11) NOT NULL,
                                                         PRIMARY KEY (`INVOICE_ITEM_ID`)
) ENGINE=INNODB DEFAULT CHARSET=LATIN1;


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE CORRECTION DETAILS APPROVER', 'Approve vendor correction details ', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VICDA', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING ->  APPROVE CORRECTION DETAILS -> SHOW', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE CORRECTION DETAILS APPROVER'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VICDA'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('VENDOR INVOICE CREDIT NOTE APPROVER', 'Approve credit note details ', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('VICNA', '7', 'ACTION', 'VIEW', 'SUMO -> VENDOR INVOICING ->  VIEW CREDIT NOTE -> APPROVE CREDIT NOTE', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'VENDOR INVOICE CREDIT NOTE APPROVER'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'VICNA'),
       'ACTIVE', '120063', '2022-07-28 12:00:00');



CREATE TABLE `KETTLE_SCM_DEV`.`PAYMENTS_BLOCK_UN_BLOCK_BY_COMPLIANCE_LOG` (
`PAYMENTS_BLOCK_UN_BLOCK_BY_COMPLIANCE_LOG_ID` int(11) NOT NULL AUTO_INCREMENT,
`VENDOR_ID` int(11) NOT NULL,
`ACTION` varchar(45) NOT NULL,
`LOGGED_AT` timestamp NULL DEFAULT NULL,
`REASON` varchar(10000) DEFAULT NULL,
PRIMARY KEY (`PAYMENTS_BLOCK_UN_BLOCK_BY_COMPLIANCE_LOG_ID`),
UNIQUE KEY `PAYMENTS_BLOCK_UN_BLOCK_BY_COMPLIANCE_LOG_ID_UNIQUE` (`PAYMENTS_BLOCK_UN_BLOCK_BY_COMPLIANCE_LOG_ID`)
);

CREATE TABLE `KETTLE_SCM_DEV`.`VENDOR_COMPLIANCE_DATA` (
`VENDOR_COMPLIANCE_ID` int(11) NOT NULL AUTO_INCREMENT,
`YEAR` int(11) NOT NULL,
`MONTH` int(11) NOT NULL,
`COMPLIANCE_KEY` varchar(30) NOT NULL,
`COMPLIANCE_TYPE` varchar(50) NOT NULL,
`FINANCIAL_YEAR` varchar(100) DEFAULT NULL,
`RETRY_API` varchar(1) DEFAULT NULL,
`KEY_ID` int(11) DEFAULT NULL,
`LOGGED_MESSAGE` varchar(10000) DEFAULT NULL,
`LAST_VALIDATION_TRY_AT` timestamp NULL DEFAULT NULL,
PRIMARY KEY (`VENDOR_COMPLIANCE_ID`),
UNIQUE KEY `VENDOR_COMPLIANCE_ID_UNIQUE` (`VENDOR_COMPLIANCE_ID`)
);

ALTER TABLE `KETTLE_SCM_DEV`.`VENDOR_ACCOUNT_DETAILS`
ADD COLUMN `LAST_PAYMENTS_BLOCKED_ON` TIMESTAMP NULL DEFAULT NULL AFTER `PAYMENT_BLOCKED`,
ADD COLUMN `LAST_PAYMENTS_BLOCKED_REASON` VARCHAR(10000) NULL DEFAULT NULL AFTER `LAST_PAYMENTS_BLOCKED_ON`;

ALTER TABLE `KETTLE_SCM_DEV`.`VENDOR_ACCOUNT_DETAILS`
ADD COLUMN `SECTION_206` VARCHAR(10000) NULL DEFAULT NULL AFTER `LAST_PAYMENTS_BLOCKED_REASON`;


