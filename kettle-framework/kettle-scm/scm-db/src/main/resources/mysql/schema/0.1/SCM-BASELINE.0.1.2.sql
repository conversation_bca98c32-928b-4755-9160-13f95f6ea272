ALTER TABLE KETTLE_SCM_DEV.SCM_ORDER_PACKAGING MODIFY COLUMN NUMBER_OF_UNITS_PACKED DECIMAL(16,6) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.SCM_ORDER_PACKAGING MODIFY COLUMN NUMBER_OF_UNITS_RECEIVED DECIMAL(16,6) DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD COLUMN CALCULATED_AMOUNT DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER ADD COLUMN TOTAL_AMOUNT DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN CALCULATED_AMOUNT DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN TOTAL_AMOUNT DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN TOTAL_AMOUNT DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM ADD COLUMN UNIT_PRICE DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM ADD COLUMN NEGOTIATED_UNIT_PRICE DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM ADD COLUMN CALCULATED_AMOUNT DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN UNIT_PRICE DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN NEGOTIATED_UNIT_PRICE DECIMAL(16,6) DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_PACKAGING_MAPPING ADD COLUMN IS_DEFAULT VARCHAR(1) DEFAULT 'N';
ALTER TABLE KETTLE_SCM_DEV.SCM_ORDER_PACKAGING ADD COLUMN CONVERSION_RATIO DECIMAL(16,6) NOT NULL;


ALTER TABLE KETTLE_SCM_DEV.SCM_ORDER_PACKAGING ADD COLUMN CONVERSION_RATIO DECIMAL(16,6) NOT NULL;

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN LAST_UPDATED_BY INTEGER DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER ADD COLUMN LAST_UPDATED_BY INTEGER DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER ADD COLUMN LAST_UPDATED_BY INTEGER DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN CANCELLED_BY INTEGER DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM MODIFY COLUMN REQUEST_ORDER_ITEM_ID INT DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.SCM_ORDER_PACKAGING ADD COLUMN NUMBER_OF_UNITS_REJECTED DECIMAL(16,6) DEFAULT NULL;
ALTER TABLE KETTLE_SCM_DEV.SCM_ORDER_PACKAGING ADD COLUMN REJECTION_REASON VARCHAR(100) DEFAULT NULL;

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN IS_AUTO_GENERATED VARCHAR(1) DEFAULT 'N';
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN PARENT_GR INTEGER NULL;

ALTER TABLE GOODS_RECEIVED_ITEM MODIFY COLUMN TRANSFER_ORDER_ITEM_ID INTEGER NULL;
ALTER TABLE SCM_ORDER_PACKAGING MODIFY COLUMN TRANSFER_ORDER_ITEM_ID INTEGER NULL;
ALTER TABLE SCM_ORDER_PACKAGING MODIFY COLUMN TRANSFERRED_QUANTITY DECIMAL(16,6) NULL;
