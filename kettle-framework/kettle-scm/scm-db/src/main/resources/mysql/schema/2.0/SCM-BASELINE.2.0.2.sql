DROP TABLE IF EXISTS KETTLE_SCM_DEV.AUDIT_LOGGING;
CREATE TABLE KETTLE_SCM_DEV.AUDIT_LOGGING (
    AUDIT_LOGGING_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    OBJECT_NAME VARCHAR(200) NULL,
    FIELD_NAME VARCHAR(50) NULL,
    SURROGATE_ID INTEGER NOT NULL,
    ACTION_TYPE VARCHAR(1) NOT NULL,
    CHANGED_AT TIMESTAMP NULL,
    CHANGED_BY VARCHAR(50) NULL,
    OLD_VALUE VARCHAR(1000) NULL,
    NEW_VALUE VARCHAR(1000) NULL,
	DESCRIPTION VARCHAR(5000) NULL
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.UNIT_SKU_MAPPING;
CREATE TABLE KETTLE_SCM_DEV.UNIT_SKU_MAPPING (
    UNIT_SKU_MAPPING_ID INTEGER(11) NOT NULL AUTO_INCREMENT,
    UNIT_ID INTEGER(11) NOT NULL,
    SKU_ID INTEGER(11) NOT NULL,
    MAPPING_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL,
    PRIMARY KEY (UNIT_SKU_MAPPING_ID),
    UNIQUE KEY UNIT_SKU_MAPPING_KEY (UNIT_ID , SKU_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.VENDOR_SKU_MAPPING;
CREATE TABLE KETTLE_SCM_DEV.VENDOR_SKU_MAPPING (
    VENDOR_SKU_MAPPING_ID INTEGER(11) NOT NULL AUTO_INCREMENT,
    SKU_ID INTEGER(11) NOT NULL,
    VENDOR_ID INTEGER(11) NOT NULL,
    MAPPING_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL,
    PRIMARY KEY (VENDOR_SKU_MAPPING_ID),
    UNIQUE KEY VENDOR_SKU_MAPPING_KEY (VENDOR_ID , SKU_ID)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.SKU_PRICE_DATA;
CREATE TABLE KETTLE_SCM_DEV.SKU_PRICE_DATA (
    SKU_PRICE_DATA_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    SKU_ID INTEGER NOT NULL,
    SKU_PACKAGING_ID INTEGER NOT NULL,
    VENDOR_ID INTEGER NOT NULL,
    DISPATCH_LOCATION VARCHAR(50) NOT NULL,
    DELIVERY_LOCATION VARCHAR(50) NOT NULL,
    SKU_PRICE DECIMAL(10 , 2 ) NOT NULL,
    START_DATE DATE NOT NULL,
     STATUS VARCHAR(15) NOT NULL
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.SKU_PRICE_HISTORY;
CREATE TABLE KETTLE_SCM_DEV.SKU_PRICE_HISTORY (
    SKU_PRICE_HISTORY_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    SKU_PRICE_DATA_ID INTEGER NOT NULL,
    START_DATE DATE NOT NULL,
    CURRENT_PRICE DECIMAL(10 , 2 ) NOT NULL,
    NEGOTIATED_PRICE DECIMAL(10 , 2 ) NOT NULL,
    RECORD_STATUS VARCHAR(50) NOT NULL,
    CHANGE_TYPE VARCHAR(20) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.UNIT_SKU_VENDOR_MAPPING;
CREATE TABLE KETTLE_SCM_DEV.UNIT_SKU_VENDOR_MAPPING (
    UNIT_SKU_VENDOR_MAPPING_ID INTEGER(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    UNIT_SKU_MAPPING_ID INTEGER(11) NOT NULL,
    VENDOR_ID INTEGER(11) NOT NULL,
    MAPPING_STATUS VARCHAR(15) NOT NULL,
    CREATED_AT TIMESTAMP NULL,
    CREATED_BY VARCHAR(50) NULL,
    UPDATED_AT TIMESTAMP NULL,
    UPDATED_BY VARCHAR(50) NULL,
    UNIQUE KEY UNIT_SKU_VENDOR_MAPPING_KEY (UNIT_SKU_MAPPING_ID , VENDOR_ID)
);