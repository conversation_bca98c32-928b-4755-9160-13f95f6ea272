CREATE TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE (
  INVOICE_ID INT(11) NOT NULL AUTO_INCREMENT,
  VENDOR_ID INT(11) NOT NULL,
  DISPATCH_ID INT(11) NOT NULL,
  TYPE VARCHAR(20) NOT NULL,
  INVOICE_DOC_URL VARCHAR(500) DEFAULT NULL,
  E_WAY_DOC_URL VARCHAR(500) DEFAULT NULL,
  TRANSPORT_ID VARCHAR(100) NOT NULL,
  INVOICE_STATUS VARCHAR(20) NOT NULL,
  TOTAL_COST DECIMAL(15,4) NOT NULL,
  TOTAL_SELLING_COST DECIMAL(15,4) NOT NULL,
  TOTAL_TAX DECIMAL(15,4) NOT NULL,
  ADDITIONAL_CHARGES DECIMAL(15,4) DEFAULT NULL,
  TOTAL_AMOUNT DECIMAL(15,4) NOT NULL,
  SENDING_UNIT_ID INT(11) NOT NULL,
  SENDING_UNIT_NAME VARCHAR(50) NOT NULL,
  NEEDS_APPROVAL VARCHAR(1) NOT NULL,
  COMMENT VARCHAR(100) DEFAULT NULL,
  CREATED_BY INT(11) NOT NULL,
  CANCELLED_BY INT(11) DEFAULT NULL,
  CREATED_AT DATETIME DEFAULT NULL,
  CANCELLED_AT DATETIME DEFAULT NULL,
  CLOSURE_ID INT(11) DEFAULT NULL,
  TRANSPORT_NAME VARCHAR(45) DEFAULT NULL,
  TRANSPORT_MODE VARCHAR(45) DEFAULT NULL,
  DISPATCH_DATE DATE NOT NULL,
  DOCKET_NUMBER VARCHAR(50) NULL,
  PRIMARY KEY (INVOICE_ID),
  UNIQUE KEY INVOICE_ID_UNIQUE (INVOICE_ID),
  KEY VEHICLE_ID (TRANSPORT_ID)
);

CREATE TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE_ITEM (
  INVOICE_ITEM_ID INT(11) NOT NULL AUTO_INCREMENT,
  SKU_ID INT(11) NOT NULL,
  SKU_NAME VARCHAR(100) NOT NULL,
  TAX_CODE VARCHAR(20) NOT NULL,
  UOM VARCHAR(10) NOT NULL,
  PKG_ID INT(11) NOT NULL,
  PKG_NAME VARCHAR(50) NOT NULL,
  PKG_QTY DECIMAL(15,4) NOT NULL,
  CONVERSION_RATIO DECIMAL(15,4) NOT NULL,
  QTY DECIMAL(15,4) NOT NULL,
  CURRENT_PRICE DECIMAL(15,4) DEFAULT NULL,
  MAPPED_PRICE DECIMAL(15,4) NOT NULL,
  SELLING_PRICE DECIMAL(15,4) NOT NULL,
  CURRENT_AMOUNT DECIMAL(15,4) DEFAULT NULL,
  MAPPED_AMOUNT DECIMAL(15,4) NOT NULL,
  SELLING_AMOUNT DECIMAL(15,4) NOT NULL,
  TOTAL_TAX DECIMAL(15,4) NOT NULL,
  INVOICE_ID INT(11) NOT NULL,
  PRIMARY KEY (INVOICE_ITEM_ID),
  KEY INVOICE_ID (INVOICE_ID),
  CONSTRAINT SALES_PERFORMA_INVOICE_ITEM_KEY FOREIGN KEY (INVOICE_ID) REFERENCES SALES_PERFORMA_INVOICE (INVOICE_ID)
);



CREATE TABLE KETTLE_SCM_DEV.SALES_PERFORMA_ITEM_DRILLDOWN (
  DRILLDOWN_ID INT(11) NOT NULL AUTO_INCREMENT,
  INVOICE_ITEM_ID INT(11) NOT NULL,
  PRICE DECIMAL(16,6) NOT NULL,
  QUANTITY DECIMAL(16,6) NOT NULL,
  EXPIRY_DATE DATETIME NOT NULL,
  ADD_TIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (DRILLDOWN_ID),
  KEY INVOICE_ITEM_ID (INVOICE_ITEM_ID),
  CONSTRAINT SALES_PERFORMA_ITEM_DRILLDOWN_KEY FOREIGN KEY (INVOICE_ITEM_ID) REFERENCES SALES_PERFORMA_INVOICE_ITEM (INVOICE_ITEM_ID)
);

CREATE TABLE KETTLE_SCM_DEV.SALES_PERFORMA_ITEM_TAX_DETAIL (
  TAX_DETAIL_ID INT(11) NOT NULL AUTO_INCREMENT,
  TAX_TYPE VARCHAR(10) NOT NULL,
  TAX_CODE VARCHAR(20) NOT NULL,
  PERCENTAGE DECIMAL(15,4) NOT NULL,
  VALUE DECIMAL(15,4) NOT NULL,
  INVOICE_ITEM_ID INT(11) NOT NULL,
  PRIMARY KEY (TAX_DETAIL_ID),
  KEY INVOICE_ITEM_ID (INVOICE_ITEM_ID),
  CONSTRAINT SALES_PERFORMA_ITEM_TAX_DETAIL_KEY FOREIGN KEY (INVOICE_ITEM_ID) REFERENCES SALES_PERFORMA_INVOICE_ITEM (INVOICE_ITEM_ID)
);

CREATE TABLE KETTLE_SCM_DEV.SALES_PERFORMA_STATUS_EVENT (
  EVENT_ID INT(11) NOT NULL AUTO_INCREMENT,
  FROM_STATUS VARCHAR(20) NOT NULL,
  TO_STATUS VARCHAR(20) NOT NULL,
  TRANSITION_STATUS VARCHAR(8) NOT NULL,
  INVOICE_ID INT(11) NOT NULL,
  GENERATED_BY INT(11) NOT NULL,
  GENERATED_AT DATETIME NOT NULL,
  PRIMARY KEY (EVENT_ID),
  KEY INVOICE_ID (INVOICE_ID),
  KEY PERFORMA_STATUS_FROM_STATUS_INDEX (FROM_STATUS) USING BTREE,
  KEY PERFORMA_STATUS_TO_STATUS_INDEX (TO_STATUS) USING BTREE,
  CONSTRAINT SALES_PERFORMA_STATUS_EVENT_KEY FOREIGN KEY (INVOICE_ID) REFERENCES SALES_PERFORMA_INVOICE (INVOICE_ID)
);


CREATE INDEX PERFORMA_STATUS_FROM_STATUS_INDEX on KETTLE_SCM_DEV.SALES_PERFORMA_STATUS_EVENT(FROM_STATUS) USING BTREE;
CREATE INDEX PERFORMA_STATUS_TO_STATUS_INDEX on KETTLE_SCM_DEV.SALES_PERFORMA_STATUS_EVENT(TO_STATUS) USING BTREE;
CREATE INDEX PERFORMA_VENDOR_INDEX on KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE(VENDOR_ID) USING BTREE;
CREATE INDEX PERFORMA_STATUS_INDEX on KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE(INVOICE_STATUS) USING BTREE;
CREATE INDEX PERFORMA_CLOSURE_ID_KEY on KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE(CLOSURE_ID) USING BTREE;
CREATE INDEX GATEPASS_CLOSURE_ID_KEY on KETTLE_SCM_DEV.GATEPASS_ITEM_DATA(CLOSURE_ID) USING BTREE;
CREATE INDEX INVENTORY_CLOSURE_ID_KEY on KETTLE_SCM_DEV.INVENTORY_DRILLDOWN(CLOSURE_EVENT_ID) USING BTREE;

CREATE INDEX EVENT_TYPE_KEY on KETTLE_SCM_DEV.DAY_CLOSE_EVENT_LOG(EVENT_TYPE) USING BTREE;
CREATE INDEX EVENT_STATUS_KEY on KETTLE_SCM_DEV.DAY_CLOSE_EVENT_LOG(EVENT_STATUS) USING BTREE;




INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (NULL, 'VIM', '7', 'MENU', 'SHOW', 'SUMO -> VENDOR INVOICING -> MENU -> VIM -> SHOW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (NULL, 'VIMRINV', '7', 'SUBMENU', 'SHOW', 'SUMO -> VENDOR INVOICING -> RAISE INVOICE -> SHOW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (NULL, 'VIMAINV', '7', 'SUBMENU', 'SHOW', 'SUMO -> VENDOR INVOICING -> APPROVE INVOICE -> SHOW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL ( ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES ('VIMVINV', '7', 'SUBMENU', 'SHOW', 'SUMO -> VENDOR INVOICING -> VIEW INVOICE -> SHOW', 'ACTIVE');



INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (NULL, 'GTPSMG', '7', 'MENU', 'SHOW', 'SUMO -> GATEPASS -> MENU -> GTPSMG -> SHOW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (NULL, 'CRGTPS', '7', 'SUBMENU', 'SHOW', 'SUMO -> GATEPASS -> CREATE -> SHOW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (NULL, 'SEGTPS', '7', 'SUBMENU', 'SHOW', 'SUMO -> GATEPASS -> VIEW -> SHOW', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES (NULL, 'MGGTPS', '7', 'SUBMENU', 'SHOW', 'SUMO -> GATEPASS -> MANAGE -> SHOW', 'ACTIVE');


INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_UNIT_MANAGER'),
 ACTION_DETAIL_ID,'ACTIVE',120502,CURRENT_TIMESTAMP() FROM KETTLE_MASTER_DEV.ACTION_DETAIL
WHERE ACTION_CODE IN ("VIM","VIMRINV","VIMVINV","GTPSMG","CRGTPS","SEGTPS");

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'PURCHASER'),
 ACTION_DETAIL_ID,'ACTIVE',120502,CURRENT_TIMESTAMP() FROM KETTLE_MASTER_DEV.ACTION_DETAIL
WHERE ACTION_CODE IN ("VIM","VIMRINV","VIMVINV");

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_PLANNER'), ACTION_DETAIL_ID,'ACTIVE',120502,CURRENT_TIMESTAMP() FROM KETTLE_MASTER_DEV.ACTION_DETAIL
WHERE ACTION_CODE IN ("VIM","VIMRINV","VIMVINV","GTPSMG","CRGTPS","SEGTPS","MGGTPS","VIMAINV");

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'SCM_HEAD'), ACTION_DETAIL_ID,'ACTIVE',120502,CURRENT_TIMESTAMP() FROM KETTLE_MASTER_DEV.ACTION_DETAIL
WHERE ACTION_CODE IN ("VIM","VIMRINV","VIMVINV","GTPSMG","CRGTPS","SEGTPS","MGGTPS","VIMAINV");

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_EXECUTIVE'), ACTION_DETAIL_ID,'ACTIVE',120502,CURRENT_TIMESTAMP() FROM KETTLE_MASTER_DEV.ACTION_DETAIL
WHERE ACTION_CODE IN ("VIM","VIMVINV","VIMAINV");

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_MANAGER'), ACTION_DETAIL_ID,'ACTIVE',120502,CURRENT_TIMESTAMP() FROM KETTLE_MASTER_DEV.ACTION_DETAIL
WHERE ACTION_CODE IN ("VIM","VIMVINV","VIMAINV");

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
SELECT (SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'FINANCE_HEAD'), ACTION_DETAIL_ID,'ACTIVE',120502,CURRENT_TIMESTAMP() FROM KETTLE_MASTER_DEV.ACTION_DETAIL
WHERE ACTION_CODE IN ("VIM","VIMVINV","VIMAINV");

ALTER TABLE KETTLE_SCM_DEV.VEHICLE_DISPATCH_DATA ADD COLUMN DOCKET_NUMBER VARCHAR(50) NULL;
ALTER TABLE KETTLE_SCM_DEV.VEHICLE_DISPATCH_DATA ADD COLUMN TRANSPORTER_GSTIN VARCHAR(50) NULL;
ALTER TABLE KETTLE_SCM_DEV.VEHICLE_DISPATCH_DATA ADD COLUMN FORCE_EWAY_NOTIFICATION VARCHAR(1) NOT NULL DEFAULT "N";


ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE
ADD COLUMN INVOICE_TYPE VARCHAR(20),
ADD COLUMN SENDING_COMPANY INTEGER,
ADD COLUMN INVOICE_DOC_ID INTEGER,
ADD COLUMN EWAY_DOC_ID INTEGER;

UPDATE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE
SET INVOICE_TYPE="INVOICE", SENDING_COMPANY=1001;

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN TRANSFER_TYPE VARCHAR(20) NOT NULL;
UPDATE KETTLE_SCM_DEV.REQUEST_ORDER SET TRANSFER_TYPE = "TRANSFER";


ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN GENERATED_ID VARCHAR(50);
CREATE TABLE KETTLE_SCM_DEV.VENDOR_SEQUENCE_ID(
SEQUENCE_ID INTEGER NOT NULL AUTO_INCREMENT PRIMARY KEY,
VENDOR_ID INTEGER NOT NULL,
ID_TYPE VARCHAR(50) NOT NULL,
NEXT_VALUE INTEGER NOT NULL
);

###################### FIFO Manager control for Ordering ########################

INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA ( ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS)
 VALUES ( 'FIFO_UNIT_MANAGER', 'FIFO_UNIT_MANAGER', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING(ROLE_ID,ACTION_DETAIL_ID,MAPPING_STATUS,UPDATED_BY,LAST_UPDATE_TIME)
select (SELECT ROLE_ID from KETTLE_MASTER_DEV.USER_ROLE_DATA where ROLE_NAME= "FIFO_UNIT_MANAGER"),
ram.ACTION_DETAIL_ID,ram.MAPPING_STATUS,120502,CURRENT_TIMESTAMP() from KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING ram
INNER JOIN KETTLE_MASTER_DEV.ACTION_DETAIL ad on ram.ACTION_DETAIL_ID = ad.ACTION_DETAIL_ID
where ad.ACTION_CODE IN ('MTM','MTRFOM','MTRFOMV','MTRQOM','MTRQOMV','MTRQOMC','ORDM','ORDRO','ORDAO','ORDSO','ORDASO')
and ROLE_ID=(SELECT ROLE_ID from KETTLE_MASTER_DEV.USER_ROLE_DATA where ROLE_NAME = "SCM_UNIT_MANAGER");


INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
 VALUES (NULL, 'MNP', '7', 'MENU', 'VIEW', 'SuMo-> Manage Payments -> SHOW', 'ACTIVE');

select ROLE_ID,ROLE_NAME,
(SELECT ACTION_DETAIL_ID from KETTLE_MASTER_DEV.ACTION_DETAIL where ACTION_CODE="MNP"),
"ACTIVE",120502,CURRENT_TIMESTAMP() from
KETTLE_MASTER_DEV.USER_ROLE_DATA where ROLE_NAME in
( "FINANCE_EXECUTIVE","SCM_HEAD","SCM_PLANNER","FINANCE_HEAD","FINANCE_MANAGER","PURCHASER","SCM_UNIT_MANAGER");