ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `LAST_TAG_PRINT_DATE` `LAST_TAG_PRINT_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `INVENTORY_DATE` `INVENTORY_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `START_DATE` `START_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `EXPECTED_END_DATE` `EXPECTED_END_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `ACTUAL_END_DATE` `ACTUAL_END_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `REALIZED_DEPRECIATION_DATE` `REALIZED_DEPRECIATION_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `LAST_TRANSFER_DATE` `LAST_TRANSFER_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;


ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `WARRANTY_LAST_DATE` `WARRANTY_LAST_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `AMC_LAST_DATE` `AMC_LAST_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `INSURANCE_LAST_DATE` `INSURANCE_LAST_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION
CHANGE COLUMN `CREATION_DATE` `CREATION_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;


ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION_LOG
CHANGE COLUMN `REALIZED_DEPRECIATION_DATE` `REALIZED_DEPRECIATION_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION_LOG
CHANGE COLUMN `LAST_TAG_PRINT_DATE` `LAST_TAG_PRINT_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION_LOG
CHANGE COLUMN `LAST_TRANSFER_DATE` `LAST_TRANSFER_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION_LOG
CHANGE COLUMN `UPDATION_DATE` `UPDATION_DATE` TIMESTAMP NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING
CHANGE COLUMN `START_DATE` `START_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING
CHANGE COLUMN `END_DATE` `END_DATE` TIMESTAMP  NULL COMMENT '' ;


ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY
CHANGE COLUMN `RECOVERY_DATE` `RECOVERY_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY
CHANGE COLUMN `CREATION_DATE` `CREATION_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY
CHANGE COLUMN `SALLARY_DEDUCTION_DATE` `SALLARY_DEDUCTION_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_RECOVERY
CHANGE COLUMN `APPROVAL_DATE` `APPROVAL_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING
CHANGE COLUMN `END_DATE` `END_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.ASSET_TRANSFER_MAPPING
CHANGE COLUMN `START_DATE` `START_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.DEPRECIATION_SUMMARY
CHANGE COLUMN `END_DATE` `END_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.DEPRECIATION_SUMMARY
CHANGE COLUMN `START_DATE` `START_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_NOTE
CHANGE COLUMN `ORIGINAL_INVOICE_DATE` `ORIGINAL_INVOICE_DATE` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_NOTE
CHANGE COLUMN `DATE_OF_TRANSFER` `DATE_OF_TRANSFER` TIMESTAMP  NULL COMMENT '' ;

ALTER TABLE KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING ADD COLUMN IS_STANDALONE VARCHAR(10);

UPDATE KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING SET IS_STANDALONE = 'N';

ALTER TABLE `KETTLE_SCM_DEV`.`ATTRIBUTE_VALUE`
CHANGE COLUMN `ATTRIBUTE_VALUE_SHORT_CODE` `ATTRIBUTE_VALUE_SHORT_CODE` VARCHAR(255) NULL DEFAULT NULL COMMENT '' ;

ALTER TABLE `KETTLE_SCM_DEV`.`PROFILE_DEFINITION`
ADD UNIQUE INDEX `PROFILE_NAME_UNIQUE` (`PROFILE_NAME` ASC)  COMMENT '';

ALTER TABLE KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION DROP COLUMN AUDIT_TIMESTAMP;

ALTER TABLE KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION DROP COLUMN AUDIT_STATUS;

CREATE TABLE KETTLE_SCM_DEV.ASSET_SCRAPPED_MAPPING (
ASSET_SCRAPPED_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
ASSET_ID INT NOT NULL ,
UNIT_ID INT NOT NULL,
SCRAPPED_AMOUNT DECIMAL(16,6) NOT NULL,
SCRAPPED_DATE TIMESTAMP,
PRIMARY KEY (ASSET_SCRAPPED_MAPPING_ID)
);

UPDATE KETTLE_SCM_DEV.PRODUCT_DEFINITION SET STOCK_KEEPING_FREQUENCY = 'FIXED_ASSETS' where CATEGORY_ID = 3;

UPDATE KETTLE_SCM_DEV.SKU_DEFINITION S , KETTLE_SCM_DEV.PRODUCT_DEFINITION P
SET S.INVENTORY_LIST_ID = 1 WHERE S.LINKED_PRODUCT_ID = P.PRODUCT_ID AND P.CATEGORY_ID = 3;

INSERT INTO `KETTKE_MASTER_DEV`.`USER_ROLE_DATA` ( `ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`) VALUES ( 'STOCK_EVENT_AUDITOR', 'Acces to stock event view', 'ACTIVE');
