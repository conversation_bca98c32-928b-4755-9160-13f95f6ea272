DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_ORDER;
CREATE TABLE KETTLE_SCM_DEV.SERVICE_ORDER (
    SERVICE_ORDER_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SERVICE_ORDER_STATUS VARCHAR(20),
    VEN<PERSON><PERSON>_ID INTEGER,
    DISPATCH_LOCATION_ID INTEGER,
    LAST_UPDATE_TIME TIMESTAMP NULL,
    VENDOR_NOTIFIED VARCHAR(1),
    GENERATION_TIME TIMESTAMP NULL,
    ORDER_RECEIPT_NUMBER VARCHAR(20),
    TOTAL_COST DECIMAL(20 , 6 ),
    TOTAL_TAXES DECIMAL(20 , 6 ),
    TOTAL_AMOUNT DECIMAL(20 , 6 ),
    GENERATED_BY INTEGER,
    LAST_UPDATED_BY INTEGER,
    APPROVED_BY INTEGER,
    FORCE_CLOSED VARCHAR(1),
    COMMENT_TEXT VARCHAR(200)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_ORDER_ITEM;
CREATE TABLE KETTLE_SCM_DEV.SERVICE_ORDER_ITEM (
    SERVICE_ORDER_ITEM_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SERVICE_ORDER_ID INTEGER,
    COST_ELEMENT_ID INTEGER,
    COST_ELEMENT_NAME VARCHAR(100),
    SERVICE_DESCRIPTION VARCHAR(200),
    ASC_CODE VARCHAR(20),
    UNIT_OF_MEASURE VARCHAR(10),
    UNIT_PRICE DECIMAL(20 , 6 ),
    RECEIVED_QUANTITY DECIMAL(20 , 6 ),
    REQUESTED_QUANTITY DECIMAL(20 , 6 ),
    TOTAL_TAX_VALUE DECIMAL(20 , 6 ),
    TAX_RATE DECIMAL(20,6),
    TDS_RATE DECIMAL(20,6),
    TOTAL_COST DECIMAL(20 , 6 )
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA;
CREATE TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA (
    SERVICE_RECEIVED_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    VENDOR_ID INTEGER,
    DELIVERY_LOCATION_ID INTEGER,
    DISPATCH_LOCATION_ID INTEGER,
    SERVICE_RECEIVE_STATUS VARCHAR(20),
    COMPANY_ID INTEGER,
    CREATED_BY INTEGER,
    CREATED_AT TIMESTAMP NULL,
    UPDATED_BY INTEGER,
    UPDATED_AT TIMESTAMP NULL,
    TOTAL_PRICE DECIMAL(20 , 6 ),
    TOTAL_TAX DECIMAL(20 , 6 ),
    TOTAL_AMOUNT DECIMAL(20 , 6 ),
    EXTRA_CHARGES DECIMAL(20 , 6 ),
    TO_BE_PAID VARCHAR(1) DEFAULT 'Y',
    PAYMENT_STATUS VARCHAR(20),
    PAYMENT_REQUEST_ID INTEGER
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM;
CREATE TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM (
    SERVICE_RECEIVED_ITEM_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SERVICE_RECEIVED_ID INTEGER,
    COST_ELEMENT_ID INTEGER,
    COST_ELEMENT_NAME VARCHAR(100),
    UNIT_OF_MEASURE VARCHAR(20),
    UNIT_PRICE DECIMAL(20 , 6 ),
    RECEIVED_QUANTITY DECIMAL(20 , 6 ),
    ASC_CODE VARCHAR(20),
    TOTAL_PRICE DECIMAL(20 , 6 ),
    TOTAL_TAX DECIMAL(20 , 6 ),
    TAX_RATE DECIMAL(20 , 6 ),
    TDS_RATE DECIMAL(20 , 6 ),
    TOTAL_AMOUNT DECIMAL(20 , 6 ),
    SERVICE_DESCRIPTION VARCHAR(100),
    BUSINESS_COST_CENTER_ID INTEGER,
    BUSINESS_COST_CENTER_NAME VARCHAR(100),
    SERVICE_ORDER_ID INTEGER,
    SERVICE_ORDER_ITEM_ID INTEGER
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_ORDER_RECEIVE_ITEM_MAPPING_DATA;
CREATE TABLE KETTLE_SCM_DEV.SERVICE_ORDER_RECEIVE_ITEM_MAPPING_DATA (
    MAPPING_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SERVICE_ORDER_ITEM_ID INTEGER,
    SERVICE_RECEIVED_ITEM_ID INTEGER,
    QUANTITY DECIMAL(20 , 6 )
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.COST_CENTER_DATA;
CREATE TABLE KETTLE_SCM_DEV.COST_CENTER_DATA (
    COST_CENTER_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SHORT_CODE VARCHAR(10) UNIQUE NOT NULL,
    COST_CENTER_DESCRIPTION VARCHAR(100),
    COST_CENTER_NAME VARCHAR(20),
    COST_CENTER_STATUS VARCHAR(20),
    OWNER_ID INTEGER
);


DROP TABLE IF EXISTS KETTLE_SCM_DEV.COST_ELEMENT_DATA;
CREATE TABLE KETTLE_SCM_DEV.COST_ELEMENT_DATA (
    COST_ELEMENT_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    ASC_CODE VARCHAR(10),
    COST_ELEMENT_DESCRIPTION VARCHAR(100),
    COST_ELEMENT_NAME VARCHAR(50),
    COST_ELEMENT_STATUS VARCHAR(20),
    COST_CENTER_ID INTEGER,
    TAX_RATE DECIMAL(10,2),
    TDS_RATE DECIMAL(10,2)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.COST_ELEMENT_PACKAGING_MAP;
CREATE TABLE KETTLE_SCM_DEV.COST_ELEMENT_PACKAGING_MAP (
    PACKAGING_MAPPING_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    COST_ELEMENT_ID INTEGER,
    PACKAGING_ID INTEGER,
    MAPPING_STATUS VARCHAR(20)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM_TAX;
CREATE TABLE KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM_TAX (
    ITEM_TAX_DATA_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SERVICE_RECEIVED_ITEM_ID INTEGER,
    TAX_PERCENTAGE DECIMAL(20 , 6 ),
    TAX_VALUE DECIMAL(20 , 6 ),
    TAX_TYPE VARCHAR(10)
);

DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_ORDER_TO_SERVICE_RECEIVED_MAPPING;
CREATE TABLE KETTLE_SCM_DEV.SERVICE_ORDER_TO_SERVICE_RECEIVED_MAPPING (
    MAPPING_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    SERVICE_ORDER_ID INTEGER,
    SERVICE_RECEIVED_ID INTEGER
);



DROP TABLE IF EXISTS KETTLE_SCM_DEV.SERVICE_ORDER_STATUS_EVENT;
CREATE TABLE KETTLE_SCM_DEV.SERVICE_ORDER_STATUS_EVENT (
    SERVICE_ORDER_STATUS_EVENT_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    FROM_STATUS VARCHAR(10),
    TO_STATUS VARCHAR(10),
    SERVICE_ORDER_ID INTEGER,
    TRANSITION_STATUS VARCHAR(20),
    UPDATE_TIME TIMESTAMP NULL,
    UPDATED_BY INTEGER
);



DROP TABLE IF EXISTS KETTLE_SCM_DEV.BUSINESS_COST_CENTER_DATA;
CREATE TABLE KETTLE_SCM_DEV.BUSINESS_COST_CENTER_DATA (
    BCC_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
    BCC_NAME VARCHAR(50),
    BCC_TYPE VARCHAR(50),
    BCC_CODE VARCHAR(50),
    BCC_STATUS VARCHAR(20),
    LOCATION_ID INTEGER,
    COMPANY_ID INTEGER
);

INSERT INTO KETTLE_SCM_DEV.BUSINESS_COST_CENTER_DATA(BCC_ID,
	BCC_NAME ,
    BCC_TYPE ,
    BCC_CODE ,
    BCC_STATUS,
    LOCATION_ID,
    COMPANY_ID)
SELECT null, UNIT_NAME, UNIT_CATEGORY,UNIT_ID,UNIT_STATUS,LOCATION_DETAIL_ID, COMPANY_ID FROM KETTLE_MASTER_DEV.UNIT_DETAIL;


INSERT INTO KETTLE_SCM_DEV.COST_CENTER_DATA (COST_CENTER_ID, SHORT_CODE, COST_CENTER_DESCRIPTION, COST_CENTER_NAME, COST_CENTER_STATUS, OWNER_ID) VALUES ('1', 'MKT', 'Cost Center to accumilate Marketing Expenses ', 'Marketing', 'ACTIVE', '121218');
INSERT INTO KETTLE_SCM_DEV.COST_CENTER_DATA (COST_CENTER_ID, SHORT_CODE, COST_CENTER_DESCRIPTION, COST_CENTER_NAME, COST_CENTER_STATUS, OWNER_ID) VALUES ('2', 'PRJ', 'Cost Center to accumilate ProjectsExpenses ', 'Projects', 'ACTIVE', '100025');
INSERT INTO KETTLE_SCM_DEV.COST_CENTER_DATA (COST_CENTER_ID, SHORT_CODE, COST_CENTER_DESCRIPTION, COST_CENTER_NAME, COST_CENTER_STATUS, OWNER_ID) VALUES ('3', 'SCM', 'Cost Center to accumilate Supply Chain Expenses ', 'Supply Chain', 'ACTIVE', '120503');
INSERT INTO KETTLE_SCM_DEV.COST_CENTER_DATA (COST_CENTER_ID, SHORT_CODE, COST_CENTER_DESCRIPTION, COST_CENTER_NAME, COST_CENTER_STATUS, OWNER_ID) VALUES ('4', 'MAINT', 'Cost Center to accumilate Maintenance Expenses ', 'Maintenance', 'ACTIVE', '100025');


INSERT INTO KETTLE_SCM_DEV.COST_ELEMENT_DATA (COST_ELEMENT_ID, ASC_CODE, COST_ELEMENT_DESCRIPTION, COST_ELEMENT_NAME, COST_ELEMENT_STATUS, COST_CENTER_ID, TAX_RATE) VALUES ('1', '3919', 'Self Adhesive PVC/Sheets', 'Self Adhesive PVC/Sheets', 'ACTIVE', '1', '18');
INSERT INTO KETTLE_SCM_DEV.COST_ELEMENT_DATA (COST_ELEMENT_ID, ASC_CODE, COST_ELEMENT_DESCRIPTION, COST_ELEMENT_NAME, COST_ELEMENT_STATUS, COST_CENTER_ID, TAX_RATE) VALUES ('2', '9983', 'Hoarding', 'Hoarding', 'ACTIVE', '1', '18');
INSERT INTO KETTLE_SCM_DEV.COST_ELEMENT_DATA (COST_ELEMENT_ID, ASC_CODE, COST_ELEMENT_DESCRIPTION, COST_ELEMENT_NAME, COST_ELEMENT_STATUS, COST_CENTER_ID, TAX_RATE) VALUES ('3', '9983', 'News paper insertions', 'News paper insertions', 'ACTIVE', '1', '18');
INSERT INTO KETTLE_SCM_DEV.COST_ELEMENT_DATA (COST_ELEMENT_ID, ASC_CODE, COST_ELEMENT_DESCRIPTION, COST_ELEMENT_NAME, COST_ELEMENT_STATUS, COST_CENTER_ID, TAX_RATE) VALUES ('4', '995468', 'Transportation', 'Transportation', 'ACTIVE', '1', '18');


INSERT INTO KETTLE_SCM_DEV.PACKAGING_DEFINITION (PACKAGING_ID, PACKAGING_TYPE, PACKAGING_CODE, PACKAGING_NAME, PACKAGING_STATUS, CONVERSION_RATIO, UNIT_OF_MEASURE) VALUES ('167', 'LOOSE', 'NOS', 'NOS', 'ACTIVE', '1.000000', 'NOS');
INSERT INTO KETTLE_SCM_DEV.COST_ELEMENT_PACKAGING_MAP (PACKAGING_MAPPING_ID, COST_ELEMENT_ID, PACKAGING_ID, MAPPING_STATUS) VALUES ('1', '1', '166', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.COST_ELEMENT_PACKAGING_MAP (PACKAGING_MAPPING_ID, COST_ELEMENT_ID, PACKAGING_ID, MAPPING_STATUS) VALUES ('2', '2', '167', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.COST_ELEMENT_PACKAGING_MAP (PACKAGING_MAPPING_ID, COST_ELEMENT_ID, PACKAGING_ID, MAPPING_STATUS) VALUES ('3', '3', '167', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.COST_ELEMENT_PACKAGING_MAP (PACKAGING_MAPPING_ID, COST_ELEMENT_ID, PACKAGING_ID, MAPPING_STATUS) VALUES ('4', '4', '167', 'ACTIVE');


CREATE INDEX SR_ITEM_SID_KEY ON KETTLE_SCM_DEV.SERVICE_RECEIVED_ITEM(SERVICE_RECEIVED_ID) USING BTREE;
CREATE INDEX SR_DELIVERY_LOCATION_ID_KEY ON KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA(DELIVERY_LOCATION_ID) USING BTREE;
CREATE INDEX SR_DISPATCH_LOCATION_ID_KEY ON KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA(DISPATCH_LOCATION_ID) USING BTREE;
CREATE INDEX SR_VENDOR_ID_KEY ON KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA(VENDOR_ID) USING BTREE;
CREATE INDEX SR_COMPANY_ID_KEY ON KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA(COMPANY_ID) USING BTREE;
CREATE INDEX SR_PR_ID_KEY ON KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA(PAYMENT_REQUEST_ID) USING BTREE;
CREATE INDEX SR_CREATION_TIME_KEY ON KETTLE_SCM_DEV.SERVICE_RECEIVED_DATA(CREATED_AT) USING BTREE;


ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST MODIFY COLUMN REQUESTING_UNIT INTEGER;

CREATE TABLE KETTLE_SCM_DEV.EMPLOYEE_COST_CENTER_MAPPING(
MAPPING_ID INTEGER PRIMARY KEY AUTO_INCREMENT,
EMPLOYEE_ID INTEGER NOT NULL,
COST_CENTER_ID INTEGER NOT NULL,
MAPPING_STATUS VARCHAR(10)
);


CREATE INDEX EMPLOYEE_ID_EMPLOYEE_COST_CENTER_MAPPING ON KETTLE_SCM_DEV.EMPLOYEE_COST_CENTER_MAPPING(EMPLOYEE_ID) USING BTREE;
CREATE INDEX COST_CENTER_ID_EMPLOYEE_COST_CENTER_MAPPING ON KETTLE_SCM_DEV.EMPLOYEE_COST_CENTER_MAPPING(COST_CENTER_ID) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.EMPLOYEE_COST_CENTER_MAPPING ADD UNIQUE COST_CENTER_ID_EMPLOYEE_ID_EMPLOYEE_COST_CENTER_MAPPING(EMPLOYEE_ID, COST_CENTER_ID);

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_INVOICE_ITEM ADD COLUMN TDS_RATE DECIMAL(10,6);

