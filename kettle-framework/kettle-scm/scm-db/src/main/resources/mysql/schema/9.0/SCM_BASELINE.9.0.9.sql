CREATE TABLE KETTLE_SCM_DEV.`PRODUCTION_UNIT` (
 `PRODUCTION_UNIT_ID`  INT(11) NOT NULL PRIMARY KEY AUTO_INCREMENT,
 `UNIT_NAME`          VARCHAR(10) DEFAULT NULL,
 `<PERSON><PERSON>_CODE`          VARCHAR(10) DEFAULT NULL,
 `STATUS`             VARCHAR(50) DEFAULT NULL,
);


INSERT INTO KETTLE_SCM_DEV.`PRODUCTION_UNIT` (`PRODUCTION_UNIT_ID`, `UNIT_NAME`, `UNIT_CODE`, `STATUS`) VALUES ('1', 'Chaayos', 'chaayos', 'ACTIVE');
INSERT INTO KETTLE_SCM_DEV.`PRODUCTION_UNIT` (`PRODUCTION_UNIT_ID`, `UNIT_NAME`, `UNIT_CODE`, `STATUS`) VALUES ('2', 'GnT', 'gnt', 'ACTIVE');

ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM  ADD COLUMN PRODUCTION_UNIT int(11);

ALTER TABLE KETTLE_SCM_DEV.UNIT_SKU_MAPPING ADD COLUMN PRODUCTION_UNIT int(11);

ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM ADD COLUMN  RECIPE_REQUIRE varchar(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER ADD COLUMN NUMBER_OF_DAYS int(11);
ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER ADD COLUMN RAISE_BY varchar(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN NUMBER_OF_DAYS int(11);
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN RAISE_BY varchar(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM ADD COLUMN EXCESS_QUANTITY DECIMAL(20,10) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM ADD COLUMN EXCESS_QUANTITY DECIMAL(20,10) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM ADD COLUMN ORIGINAL_QUANTITY DECIMAL(20,10) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.PRODUCTION_PLAN_EVENT ADD COLUMN IS_UPDATED VARCHAR(1);
ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD COLUMN EXCESS_QUANTITY DECIMAL(20,10) NOT NULL;
ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN EXCESS_QUANTITY DECIMAL(20,10);

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Create Regular Order', 'Access to Create Regular Order', 'ACTIVE', '7');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('CEREFO', '7', 'ACTION', 'SHOW', 'SuMo -> REGULAR ORDERING -> CREATE ORDER', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Create Regular Order'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'CEREFO'), 'ACTIVE', '120063', '2021-11-16 00:00:00');

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER_ITEM
ADD COLUMN  EXPIRY_DATE DATETIME NULL;

CREATE TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING_MAPPING
(
    PRODUCTION_BOOKING_ID  INT(11) PRIMARY KEY AUTO_INCREMENT,
    PRODUCT_ID             INT(11) NOT NULL,
    PRODUCT_NAME           VARCHAR(100),
    SKU_ID                 INT(11) NOT NULL,
    UNIT_OF_MEASURE        VARCHAR(10),
    QUANTITY               decimal(10, 2),
    UNIT_ID                INT(11) NOT NULL,
    GENERATION_TIME        datetime,
    CANCELLATION_TIME      datetime,
    GENERATED_BY           INT(11),
    CANCELLED_BY           INT(11),
    MAPPING_STATUS         VARCHAR(50),
    PROFILE                VARCHAR(10),
    LINKED_SKU_ID          INT(11) NOT NULL,
    LINKED_SKU_NAME        VARCHAR(250),
    LINKED_UNIT_OF_MEASURE VARCHAR(10),
    LINKED_QUANTITY        DECIMAL(10, 2),
    AUTO_PRODUCTION        VARCHAR(1)
);

ALTER TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING ADD COLUMN  AUTO_BOOKING varchar(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM  ADD COLUMN PRODUCTION_ID int(11);

UPDATE KETTLE_SCM_DEV.CAPEX_TEMPLATE SET `KEY` = "CAPEX_Template_New.xlsx";