CREATE TABLE `KETTLE_SCM_DEV`.`GATEPASS_DATA` (
  `GATEPASS_ID` INT(11) NOT NULL AUTO_INCREMENT,
  `VENDOR_ID` INT(11) NOT NULL,
  `DISPATCH_LOCATION_ID` INT(11) NOT NULL,
  `OPERATION_TYPE` VARCHAR(30) NOT NULL,
  `RETURNABLE` VARCHAR(1) NOT NULL,
  `EXPECTED_RETURN` INT(11) NULL,
  `STATUS` VARCHAR(15) NOT NULL,
  `RETURN_STATUS` VARCHAR(15) NULL,
  `TOTAL_COST` DECIMAL(16,6) NOT NULL,
  `TOTAL_TAX` DECIMAL(16,6) NOT NULL,
  `ADDITIONAL_CHARGES` DECIMAL(16,6) NULL,
  `COMMENT` VARCHAR(1000) NULL,
  `REASON` VARCHAR(200) NULL,
  `CREATED_BY` INT(11) NOT NULL,
  `CREATED_AT` TIMES<PERSON>MP NOT NULL,
  `SENDING_UNIT` INT(11) NOT NULL,
  `SENDING_COMPANY` INT(11) NOT NULL,
  `NEEDS_APPROVAL` VARCHAR(1) NULL,
  `ISSUE_DATE` DATE NOT NULL,
  `HAS_LOSS` VARCHAR(1) NULL,
   `CANCELLED_BY` INT(11)  NULL,
  `CANCELLED_AT` TIMESTAMP  NULL,
  PRIMARY KEY (`GATEPASS_ID`),
  UNIQUE INDEX `GATEPASS_ID_UNIQUE` (`GATEPASS_ID` ASC));
  
  CREATE TABLE `KETTLE_SCM_DEV`.`GATEPASS_ITEM_DATA` (
  `ITEM_ID` INT NOT NULL AUTO_INCREMENT,
  `GATEPASS_ID` INT NOT NULL,
  `SKU_ID` INT NOT NULL,
  `PRICE` DECIMAL(16,6) NULL,
  `UOM` VARCHAR(10) NOT NULL,
  `TRANSACTION_TYPE` VARCHAR(45) NOT NULL,
  `QUANTITY` DECIMAL(16,6) NULL,
  `TAX` DECIMAL(16,6) NULL,
  `COST` DECIMAL(16,6) NULL,
  `AMOUNT` DECIMAL(16,6) NULL,
  `CLOSURE_ID` INT NULL,
   `CREATED_BY` INT(11)  NULL,
  `CREATED_AT` TIMESTAMP  NULL,
  PRIMARY KEY (`ITEM_ID`),
  INDEX `GATEPASS_ITEM_DATA_FK_ID` (`GATEPASS_ID` ASC),
  CONSTRAINT `GATEPASS_ITEM_DATA_FK`
    FOREIGN KEY (`GATEPASS_ID`)
    REFERENCES `KETTLE_SCM_DEV`.`GATEPASS_DATA` (`GATEPASS_ID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

    
    CREATE TABLE `KETTLE_SCM_DEV`.`GATEPASS_ITEM_DRILDOWN` (
  `ID` INT NOT NULL AUTO_INCREMENT,
  `GATEPASS_ITEM_ID` INT NOT NULL,
  `QUANTITY` DECIMAL(16,6) NOT NULL,
   `PRICE` DECIMAL(16,6) NOT NULL,
  `EXPIRY_DATE` DATETIME NULL,
  `ADD_TIME` TIMESTAMP NULL,
   `REMAINING_QUANTITY` DECIMAL(16,6) NOT NULL,
  PRIMARY KEY (`ID`),
  INDEX `GATEPASS_ITEM_DRILDOWN_FK_ID` (`GATEPASS_ITEM_ID` ASC),
  CONSTRAINT `GATEPASS_ITEM_DRILDOWN_FK`
    FOREIGN KEY (`GATEPASS_ITEM_ID`)
    REFERENCES `KETTLE_SCM_DEV`.`GATEPASS_ITEM_DATA` (`ITEM_ID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
    
    CREATE TABLE `KETTLE_SCM_DEV`.`GATEPASS_TAX_DETAIL` (
  `TAX_ID` INT NOT NULL AUTO_INCREMENT,
  `GATEPASS_ITEM_ID` INT NOT NULL,
  `TAX_TYPE` VARCHAR(8) NOT NULL,
  `TAX_CODE` VARCHAR(10) NOT NULL,
  `TAX_PERCENTAGE` DECIMAL(10,2) NOT NULL,
  `TAX_AMOUNT` DECIMAL(16,6) NOT NULL,
  PRIMARY KEY (`TAX_ID`),
  INDEX `GATEPASS_TAX_DETAIL_FK_ID` (`GATEPASS_ITEM_ID` ASC),
  CONSTRAINT `GATEPASS_TAX_DETAIL_FK`
    FOREIGN KEY (`GATEPASS_ITEM_ID`)
    REFERENCES `KETTLE_SCM_DEV`.`GATEPASS_ITEM_DATA` (`ITEM_ID`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);
    
    CREATE TABLE `KETTLE_SCM_DEV`.`GATEPASS_STATUS_DETAIL` (
  `GATEPASS_STATUS_DETAIL_ID` INT NOT NULL AUTO_INCREMENT,
  GATEPASS_ID INT NOT NULL,
  `FROM_STATUS` VARCHAR(15) NOT NULL,
  `TO_STATUS` VARCHAR(15) NOT NULL,
  `TRANSITION_STATUS` VARCHAR(15) NOT NULL,
  `REASON_TEXT` VARCHAR(45) NULL,
  `UPDATED_BY` INT NOT NULL,
  `UPDATED_AT` TIMESTAMP NULL,
  PRIMARY KEY (`GATEPASS_STATUS_DETAIL_ID`));
  
  CREATE TABLE `KETTLE_SCM_DEV`.`GATEPASS_VENDOR_MAPPING` (
  `DETAIL_ID` INT NOT NULL AUTO_INCREMENT ,
  `VENDOR_ID` INT NOT NULL,
   `UNIT_ID` INT NOT NULL,
  `OPERATION_TYPE` VARCHAR(30) NOT NULL,
  `STATUS` VARCHAR(15) NOT NULL,
  `CREATED_BY` INT NOT NULL,
  `CREATED_AT` TIMESTAMP NULL,
  PRIMARY KEY (`DETAIL_ID`),
  UNIQUE INDEX `GATEPASS_VENDOR_MAPPING_UNIQUE_KEY` (`VENDOR_ID` ASC, `OPERATION_TYPE` ASC, `UNIT_ID` ASC));

ALTER TABLE `KETTLE_SCM_DEV`.`WASTAGE_EVENT` 
CHANGE COLUMN `LINKED_KETTLE_ID` `LINKED_REF_ID` INT(11) NULL DEFAULT NULL ,
CHANGE COLUMN `LINKED_KETTLE_ID_TYPE` `LINKED_REF_ID_TYPE` VARCHAR(20) NULL DEFAULT NULL ;



  
  
  