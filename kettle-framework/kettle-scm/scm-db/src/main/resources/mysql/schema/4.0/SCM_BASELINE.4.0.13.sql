ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_DATA ADD COLUMN `EXPIRY_DATE` DATE NULL;

ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DATA ADD COLUMN `EXPIRY_DATE` DATE NULL;

ALTER TABLE KETTLE_SCM_DEV.COST_DETAIL_AUDIT_DRILLDOWN_DATA ADD COLUMN `EXPIRY_DATE` DATE NULL;

ALTER TABLE KETTLE_SCM_DEV.PRODUCTION_BOOKING ADD COLUMN `EXPIRY_DATE` DATE NULL;

ALTER TABLE KETTLE_SCM_DEV.VENDOR_GR_ITEM_DETAIL ADD COLUMN `EXPIRY_DATE` DATE NULL;

CREATE TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM_DRILLDOWN(
TRANSFER_ORDER_ITEM_DRILLDOWN_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
TRANSFER_ORDER_ITEM_ID INTEGER NOT NULL,
PRICE DECIMAL(16,6) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
EXPIRY_DATE DATE NOT NULL,
ADD_TIME TIMESTAMP NOT NULL,
FOREIGN KEY (TRANSFER_ORDER_ITEM_ID) REFERENCES TRANSFER_ORDER_ITEM (TRANSFER_ORDER_ITEM_ID)
);


CREATE TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM_DRILLDOWN(
GOODS_RECEIVED_ITEM_DRILLDOWN_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
GOODS_RECEIVED_ITEM_ID INTEGER NOT NULL,
PRICE DECIMAL(16,6) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
EXPIRY_DATE DATE NOT NULL,
ADD_TIME TIMESTAMP NOT NULL,
FOREIGN KEY (GOODS_RECEIVED_ITEM_ID) REFERENCES GOODS_RECEIVED_ITEM (GOODS_RECEIVED_ITEM_ID)
);

CREATE TABLE KETTLE_SCM_DEV.WASTAGE_ITEM_DRILLDOWN(
WASTAGE_ITEM_DRILLDOWN_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
WASTAGE_ITEM_ID INTEGER NOT NULL,
PRICE DECIMAL(16,6) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
EXPIRY_DATE DATE NOT NULL,
ADD_TIME TIMESTAMP NOT NULL,
FOREIGN KEY (WASTAGE_ITEM_ID) REFERENCES WASTAGE_ITEM_DATA (WASTAGE_ITEM_ID)
);

CREATE TABLE KETTLE_SCM_DEV.BOOKING_CONSUMPTION_ITEM_DRILLDOWN(
CONSUMPTION_DRILLDOWN_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
BOOKING_CONSUMPTION_ID INTEGER NOT NULL,
PRICE DECIMAL(16,6) NOT NULL,
QUANTITY DECIMAL(16,6) NOT NULL,
EXPIRY_DATE DATE NOT NULL,
ADD_TIME TIMESTAMP NOT NULL,
FOREIGN KEY (BOOKING_CONSUMPTION_ID) REFERENCES BOOKING_CONSUMPTION (BOOKING_CONSUMPTION_ID)
);



UPDATE KETTLE_SCM_DEV.COST_DETAIL_DATA cd,
   KETTLE_SCM_DEV.PRODUCT_DEFINITION pd 
SET
   EXPIRY_DATE = CASE
       WHEN pd.SHELF_LIFE_IN_DAYS <= 0 THEN '9999-12-01'
       ELSE DATE_ADD(DATE(LAST_UPDATE_TMSTMP),
           INTERVAL pd.SHELF_LIFE_IN_DAYS DAY)
   END
WHERE
   cd.KEY_TYPE = 'PRODUCT'
   AND cd.KEY_ID = pd.PRODUCT_ID;
   
   UPDATE KETTLE_SCM_DEV.COST_DETAIL_DATA cd,
   KETTLE_SCM_DEV.SKU_DEFINITION pd 
SET
   EXPIRY_DATE = CASE
       WHEN pd.SHELF_LIFE_IN_DAYS <= 0 THEN '9999-12-01'
       ELSE DATE_ADD(DATE(LAST_UPDATE_TMSTMP),
           INTERVAL pd.SHELF_LIFE_IN_DAYS DAY)
   END
WHERE
   cd.KEY_TYPE = 'SKU'
   AND cd.KEY_ID = pd.SKU_ID;
   
UPDATE KETTLE_SCM_DEV.cost_detail_audit_data cdad ,KETTLE_SCM_DEV.cost_detail_data cda set cdad.EXPIRY_DATE = cda.EXPIRY_DATE WHERE CDAD.COST_DETAIL_DATA_ID = cda.COST_DETAIL_DATA_ID;

UPDATE KETTLE_SCM_DEV.cost_detail_audit_drilldown_data cdadd ,KETTLE_SCM_DEV.cost_detail_audit_data cdad set cdadd.EXPIRY_DATE = cdad.EXPIRY_DATE WHERE CDADD.COST_DETAIL_DATA_AUDIT_ID = CDAD.COST_DETAIL_DATA_AUDIT_ID;

ALTER TABLE KETTLE_SCM_DEV.STOCK_INVENTORY ADD COLUMN STOCK_TYPE VARCHAR(25);
ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES ADD COLUMN STOCK_TYPE VARCHAR(25);
ALTER TABLE KETTLE_SCM_DEV.DAY_CLOSE_EVENT_RANGE ADD COLUMN STOCK_TYPE VARCHAR(25);

CREATE DATABASE KETTLE_SCM_ARCHIVE; 

CREATE TABLE KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_BACKUP_1 LIKE KETTLE_SCM_DEV.STOCK_INVENTORY;
INSERT KETTLE_SCM_ARCHIVE.STOCK_INVENTORY_BACKUP_1 SELECT * FROM KETTLE_SCM_DEV.STOCK_INVENTORY;

CREATE TABLE KETTLE_SCM_ARCHIVE.STOCK_ENTRY_BACKUP_1_1 LIKE KETTLE_SCM_DEV.STOCK_ENTRY;
INSERT KETTLE_SCM_ARCHIVE.STOCK_ENTRY_BACKUP_1 SELECT * FROM KETTLE_SCM_DEV.STOCK_ENTRY;

CREATE TABLE KETTLE_SCM_ARCHIVE.DAY_CLOSE_PRODUCT_VALUES_BACKUP_1 LIKE KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES;
INSERT KETTLE_SCM_ARCHIVE.DAY_CLOSE_PRODUCT_VALUES_BACKUP_1 SELECT * FROM KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES;

CREATE KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_BACKUP_1 LIKE KETTLE_SCM_DEV.DAY_CLOSE_EVENT;
INSERT KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_BACKUP_1 SELECT * FROM KETTLE_SCM_DEV.DAY_CLOSE_EVENT;

CREATE TABLE KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_RANGE_BACKUP_1 LIKE KETTLE_SCM_DEV.DAY_CLOSE_EVENT_RANGE;
INSERT KETTLE_SCM_ARCHIVE.DAY_CLOSE_EVENT_RANGE_BACKUP_1 SELECT * FROM KETTLE_SCM_DEV.DAY_CLOSE_EVENT_RANGE;

set sql_safe_updates=0;
set foreign_key_checks=0;

delete from KETTLE_SCM_DEV.STOCK_ENTRY where UPDATE_EVENT_ID in (
select EVENT_ID from KETTLE_SCM_DEV.DAY_CLOSE_EVENT
where BUSINESS_DATE <= "2017-09-01" and CLOSURE_EVENT_TYPE NOT IN ("OPENING","WH_OPENING","WH_CLOSING"));

delete from KETTLE_SCM_DEV.STOCK_INVENTORY where CURRENT_EVENT_ID in (
select EVENT_ID from KETTLE_SCM_DEV.DAY_CLOSE_EVENT
where BUSINESS_DATE <= "2017-09-01" and CLOSURE_EVENT_TYPE NOT IN ("OPENING","WH_OPENING","WH_CLOSING"));


delete from KETTLE_SCM_DEV.DAY_CLOSE_EVENT_RANGE where EVENT_ID in (
select EVENT_ID from KETTLE_SCM_DEV.DAY_CLOSE_EVENT
where BUSINESS_DATE <= "2017-09-01" and CLOSURE_EVENT_TYPE NOT IN ("OPENING","WH_OPENING","WH_CLOSING"));

delete from KETTLE_SCM_DEV.DAY_CLOSE_PRODUCT_VALUES where EVENT_ID in (
select EVENT_ID from KETTLE_SCM_DEV.DAY_CLOSE_EVENT
where BUSINESS_DATE <= "2017-09-01" and CLOSURE_EVENT_TYPE NOT IN ("OPENING","WH_OPENING","WH_CLOSING"));

delete from KETTLE_SCM_DEV.DAY_CLOSE_EVENT where BUSINESS_DATE <= "2017-09-01"
and CLOSURE_EVENT_TYPE NOT IN ("OPENING","WH_OPENING","WH_CLOSING");
