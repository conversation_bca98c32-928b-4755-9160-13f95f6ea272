update KETTLE_SCM_DEV.PRODUCT_DEFINITION set VARIANCE_TYPE="TBD" where PRODUCT_ID in (100018,100122,100160,100170,
100183,100190,100192,100395,100396,100517,100518,100587,100590,100666,100674,100675,100699,100739,100740,100741,100742,
100752,100753,100755,100756,100758,100764,100774,100775,100776,100777,100779,100795,100796,100797,100811,100819,100828,
100832,100834,100845,100846,100847,100848,100849,100850,100856,100864,100865,100867,100868,100874,100877,100878,100895,
100909,100910,100911,100912,100914,100915,100942,100945,100952,100960,100962,100963,100964,100967,100971,100973,100974,
100975,100976,100978,100980,100982,100983,100984,100985,100990,100994,100995,100996,100997,100998,100999,101003,101005,
101006,101007,101008,101011,101012,101015,101016,101018,101019,101020,101021,101022,101032,101033,101035,101037,101040,
101042,101043,101044,101046,101047,101057,101058,101059,101060,101062,101066,101067,101069,101070,101071,101072,101073,
101074,101075,101076,101080,101081,101082,101087,101088,101092,101093,101094,101096,101097,101098,101099,101100,101101,
101102,101103,101104,101105,101106,101107,101108,101109,101110,101111,101112,101113,101114,101117,101119,101121,101122,
101123,101124,101125,101130,101135,101137,101139,101143,101144,101145,101146,101147,101158,101163,101164,101166,101171,
100104,100310,100360,100380,100520,100522,100535,100588,100607,100608,100609,100610,100725,100726,100727,100728,100729,
100730,100733,100734,100735,100736,100737,100738,100743,100744,100745,100750,100751,100757,100759,100762,100763,100765,
100766,100767,100768,100769,100770,100771,100772,100778,100780,100781,100784,100791,100792,100793,100794,100798,100800,
100801,100802,100803,100804,100808,100812,100813,100814,100815,100816,100817,100820,100821,100822,100823,100824,100829,
100831,100833,100835,100840,100841,100842,100843,100844,100858,100859,100860,100861,100863,100866,100869,100870,100871,
100872,100873,100875,100876,100879,100883,100884,100885,100886,100888,100889,100890,100891,100893,100894,100896,100913,
100916,100917,100918,100919,100920,100921,100922,100923,100924,100925,100926,100927,100928,100929,100930,100931,100932,
100933,100934,100935,100936,100937,100938,100939,100940,100941,100943,100944,100956,100957,100958,100959,100961,100965,
100966,100968,100969,100970,100972,100979,100981,100986,100987,100988,100989,100991,100993,101000,101009,101010,101014,
101017,101023,101024,101025,101038,101039,101041,101045,101048,101049,101050,101051,101052,101053,101054,101056,101061,
101063,101064,101078,101079,101084,101085,101090,101095,101115,101116,101118,101120,101127,101128,101129,101132,101134,
101138,101162,101165,101168,101172,100533,100555,100661,100685,100723,100724,100746,100747,100748,100760,100761,100773,
100782,100783,100785,100786,100787,100788,100789,100790,100799,100805,100806,100807,100809,100810,100825,100826,100827,
100830,100887,100892,100897,100898,100899,100900,100901,100902,100903,100904,100905,100977,100992,101001,101002,101004,
101013,101026,101027,101028,101029,101030,101031,101034,101036,101055,101065,101068,101077,101083,101086,101089,101091,
101126,101131,101133,101136,101141,101159,101160,101161,101169,101170,100010,100011,100025,100034,100081,100083,100167,
100182,100205,100236,100249,100253,100262,100706,100707,100709,100749,100754,100818,100851,100852,100853,100854,100855,
100857,100862,100880,100881,100882,100906,100907,100908,100946,100947,100948,100949,100950,100951,100953,100955,101140,
101142,101148,101149,101150,101151,101152,101153,101154,101155,101156,101157);

UPDATE PRODUCT_DEFINITION SET VARIANCE_TYPE = "COGS_CONTROLLABLES" WHERE PRODUCT_ID IN (100004,100006,100008,100017,100019,
100023,100029,100035,100036,100044,100050,100055,100059,100060,100062,100066,100068,100069,100077,100080,100084,100085,
100088,100092,100093,100095,100096,100113,100114,100120,100123,100127,100128,100130,100131,100141,100144,100159,100166,
100168,100171,100174,100186,100191,100194,100196,100197,100206,100208,100210,100211,100237,100238,100241,100242,100245,
100247,100254,100255,100267,100271,100274,100294,100299,100305,100308,100309,100312,100325,100326,100327,100328,100330,
100331,100332,100333,100340,100341,100344,100356,100359,100362,100365,100370,
100394,100511,100512,100519,100681,100682,100708,100715,100717,100169,100177,100181,100272,100289);

UPDATE PRODUCT_DEFINITION SET VARIANCE_TYPE = "CONSUMABLES_CONTROLLABLES" WHERE PRODUCT_ID IN (100007,100024,100028,100030,
100031,100032,100049,100067,100073,100079,100089,100090,100091,100094,100110,100118,100124,100125,100134,100146,100165,
100175,100178,100198,100217,100235,100261,100263,100277,100285,100286,100287,100288,100290,100293,100295,100296,100302,
100314,100315,100318,100324,100336,100337,100361,100387,100388,100389,100393,100495,100497,100498,100499,100503,100504,
100505,100506,100527,100669,100683);

UPDATE PRODUCT_DEFINITION SET VARIANCE_TYPE = "ZERO_VARIANCE" WHERE PRODUCT_ID IN (100026,100027,100038,100070,100076,
100082,100097,100098,100099,100112,100145,100153,100156,100180,100184,100185,100202,100203,100207,100216,100234,100246,
100259,100269,100270,100273,100297,100298,100329,100343,100346,100347,100591,100599,100600,100601,100671,100716,100007,
100012,100013,100014,100015,100016,100021,100022,100024,100028,100030,100031,100032,100033,100037,100039,100041,100042,
100043,100045,100046,100047,100048,100049,100051,100052,100053,100054,100056,100058,100061,100063,100064,100065,100067,
100072,100073,100074,100075,100078,100079,100086,100087,100089,100090,100091,100094,100100,100101,100102,100103,100105,
100106,100107,100108,100110,100111,100115,100116,100117,100118,100119,100121,100124,100125,100126,100129,100132,100133,
100134,100135,100136,100137,100138,100139,100140,100146,100147,100150,100151,100152,100154,100157,100158,100161,100162,
100163,100164,100165,100173,100175,100176,100178,100188,100189,100198,100200,100212,100213,100215,100217,100219,100220,
100221,100222,100223,100224,100225,100226,100227,100228,100229,100230,100232,100233,100235,100243,100248,100251,100252,
100257,100258,100261,100263,100264,100265,100266,100268,100277,100278,100279,100280,100281,100282,100283,100284,100285,
100286,100287,100288,100290,100293,100295,100296,100300,100301,100302,100303,100304,100307,100311,100314,100315,100316,
100317,100318,100319,100320,100321,100322,100323,100324,100334,100335,100336,100337,100339,100342,100345,100348,100349,
100350,100351,100352,100353,100357,100358,100361,100363,100364,100366,100367,100368,100369,100372,100377,100378,100379,
100384,100385,100386,100387,100388,100389,100390,100391,100392,100393,100489,100490,100491,100492,100493,100494,100495,
100496,100497,100498,100499,100500,100501,100502,100503,100504,100505,100506,100507,100509,100510,100516,100521,100523,
100524,100525,100526,100527,100544,100545,100547,100550,100589,100592,100593,100603,100604,100605,100606,100611,100614,
100665,100667,100668,100669,100673,100677,100679,100680,100683,100684,100686,100687,100688,100690,100691,100692,100697,
100710,100711,100712,100713,100714,100720,100721,100722,100731,100732,100001,100003,100005,100009,100020,100040,100071,
100109,100143,100148,100149,100155,100172,100187,100199,100204,100209,100214,100231,100239,100240,100244,100260,100275,
100276,100291,100292,100338,100354,100355,100373,100375,100376,100381,100382,100383,100397,100398,100399,100400,100401,
100402,100403,100404,100405,100406,100407,100408,100409,100410,100411,100412,100413,100414,100415,100416,100417,100418,
100419,100420,100421,100422,100423,100424,100425,100426,100427,100428,100429,100430,100431,100432,100433,100434,100435,
100436,100437,100438,100439,100440,100441,100442,100443,100444,100445,100446,100447,100448,100449,100450,100451,100452,
100453,100454,100455,100456,100457,100458,100459,100460,100461,100462,100463,100464,100465,100466,100467,100468,100469,
100470,100471,100472,100473,100474,100475,100476,100477,100478,100479,100480,100481,100482,100483,100484,100485,100486,
100487,100488,100508,100513,100514,100515,100528,100529,100530,100531,100532,100534,100536,100537,100538,100539,100540,
100541,100542,100543,100546,100548,100549,100551,100552,100553,100554,100556,100557,100558,100559,100560,100561,100562,
100563,100564,100565,100566,100567,100568,100569,100570,100571,100572,100573,100574,100575,100576,100577,100578,100579,
100580,100581,100582,100583,100584,100585,100586,100602,100612,100613,100615,100616,100617,100618,100619,100620,100624,
100625,100626,100627,100628,100629,100630,100631,100632,100633,100634,100635,100636,100637,100638,100639,100640,100641,
100642,100643,100644,100645,100646,100647,100648,100649,100650,100651,100652,100653,100654,100655,100656,100657,100658,
100659,100660,100662,100663,100664,100670,100672,100676,100678,100689,100693,100694,100695,100696,100698,100700,100701,
100702,100703,100704,100718,100719,100002,100057,100142,100179,100193,100195,100201,100218,100250,100256,100306,100313,
100371,100374,100594,100595,100596,100597,100598,100705);





ALTER TABLE KETTLE_SCM_DEV.SCM_ORDER_PACKAGING ADD COLUMN PRICE_PER_UNIT DECIMAL(10, 2);
ALTER TABLE KETTLE_SCM_DEV.REFERENCE_ORDER_SCM_ITEM ADD COLUMN SUGGESTED_QUANTITY DECIMAL(10, 2);