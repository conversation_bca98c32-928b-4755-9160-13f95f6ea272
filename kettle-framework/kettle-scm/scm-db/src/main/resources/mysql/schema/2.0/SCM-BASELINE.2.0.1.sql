DROP TABLE IF EXISTS KETTLE_SCM_DEV.VENDOR_REGISTRATION_REQUEST_DETAIL;
CREATE TABLE KETTLE_SCM_DEV.VENDOR_REGISTRATION_REQUEST_DETAIL (
    REQUEST_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    VENDOR_NAME VARCHAR(200) NOT NULL,
    REQUEST_DATE TIMESTAMP NOT NULL,
    REQUESTED_BY_ID INTEGER NOT NULL,
    REQUESTED_BY_NAME VARCHAR(100) NOT NULL,
    REQUESTED_FOR_ID INTEGER NOT NULL,
    REQUESTED_FOR_NAME VARCHAR(100) NOT NULL,
    VENDOR_EMAIL VARCHAR(100) NULL,
    COPY_EMAILS VARCHAR(300) NULL,
    VENDOR_LINK INTEGER NULL,
    REQUEST_STATUS VARCHAR(20),
    <PERSON><PERSON><PERSON><PERSON>ATION_URL VARCHAR(200),
    AUTH_KEY VARCHAR(50) UNIQUE NOT NULL
);



CREATE INDEX AUTH_KEY_VENDOR_REGISTRATION_REQUEST_DETAIL ON KETTLE_SCM_DEV.VENDOR_REGISTRATION_REQUEST_DETAIL(AUTH_KEY) USING BTREE;

ALTER TABLE KETTLE_SCM.VENDOR_REGISTRATION_REQUEST_DETAIL ADD COLUMN VENDOR_TYPE VARCHAR(20) NOT NULL;

