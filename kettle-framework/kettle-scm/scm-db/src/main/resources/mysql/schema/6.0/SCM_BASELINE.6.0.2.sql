ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN PAYMENT_DATE TIMESTAMP NULL;

ALTER TABLE KETTLE_SCM_DEV.SERVICE_ORDER ADD COLUMN COST_CENTER_ID INTEGER NULL;
ALTER TABLE KETTLE_SCM_DEV.COST_CENTER_DATA ADD COLUMN COST_CENTER_EMAIL VARCHAR(100) NULL;

set sql_safe_updates=0;
update KETTLE_SCM_DEV.SERVICE_ORDER so INNER JOIN
(
select so.SERVICE_ORDER_ID, group_concat(DISTINCT(ced.COST_CENTER_ID)) as COST_CENTRE
from KETTLE_SCM_DEV.SERVICE_ORDER so
INNER JOIN KETTLE_SCM_DEV.SERVICE_ORDER_ITEM soi on soi.SERVICE_ORDER_ID = so.SERVICE_ORDER_ID
INNER JOIN KETTLE_SCM_DEV.COST_ELEMENT_DATA ced on ced.COST_ELEMENT_ID = soi.COST_ELEMENT_ID
where so.COST_CENTER_ID is null
group by so.SERVICE_ORDER_ID) as Y on so.SERVICE_ORDER_ID = Y.SERVICE_ORDER_ID
set so.COST_CENTER_ID = Y.COST_CENTRE;

select * from KETTLE_SCM_DEV.SERVICE_ORDER where COST_CENTER_ID is null;


UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='1';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='2';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='3';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='4';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='5';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='6';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='7';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='8';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='9';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='10';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='11';
UPDATE KETTLE_SCM_DEV.COST_CENTER_DATA SET COST_CENTER_EMAIL='<EMAIL>' WHERE COST_CENTER_ID='12';

ALTER TABLE KETTLE_SCM_DEV.COMPANY_BANK_MAPPING
ADD COLUMN ACCOUNT_STATUS VARCHAR(10);
CREATE INDEX ACCOUNT_STATUS_COMPANY_BANK_MAPPING ON KETTLE_SCM_DEV.COMPANY_BANK_MAPPING(ACCOUNT_STATUS) USING BTREE;

ALTER TABLE KETTLE_SCM_DEV.VENDOR_DISPATCH_LOCATIONS
ADD COLUMN LOCATION_STATUS VARCHAR(10) NOT NULL DEFAULT 'ACTIVE';

UPDATE KETTLE_SCM_DEV.VENDOR_DISPATCH_LOCATIONS
SET LOCATION_STATUS = "IN_ACTIVE"
WHERE DISPATCH_LOCATION_ID
IN (168,26,27,70,111,138,247,179,844,217,242,348,285,347,385,424,452,483,501,
507,564,630,759,774,849,959,973,975,1018,431,565);

UPDATE KETTLE_SCM_DEV.VENDOR_DISPATCH_LOCATIONS
SET LOCATION_NAME='NEW DELHI'
WHERE DISPATCH_LOCATION_ID='156';
UPDATE KETTLE_SCM_DEV.VENDOR_DISPATCH_LOCATIONS
SET LOCATION_NAME='GURGAON'
WHERE DISPATCH_LOCATION_ID='367';
UPDATE KETTLE_SCM_DEV.VENDOR_DISPATCH_LOCATIONS
SET LOCATION_NAME='NEW DELHI'
WHERE DISPATCH_LOCATION_ID='377';
UPDATE KETTLE_SCM_DEV.VENDOR_DISPATCH_LOCATIONS
SET LOCATION_NAME='NEW DELHI'
WHERE DISPATCH_LOCATION_ID='378';


alter table KETTLE_SCM_DEV.REQUEST_ORDER add column SEARCH_TAG VARCHAR(20) default null;