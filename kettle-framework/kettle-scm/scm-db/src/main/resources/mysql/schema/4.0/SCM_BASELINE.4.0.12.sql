ALTER TABLE KETTLE_SCM_DEV.DEBIT_NOTE_DETAIL ADD COLUMN BUSY_REFERENCE_NUMBER VARCHAR(100) NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`DERIVED_FULFILMENT_TYPE_MAPPING`
CHANGE COLUMN `FULFILMENT_TYPE` `FULFILMENT_TYPE` VARCHAR(30) NULL ;

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN AUTO_PRODUCTION VARCHAR(1) NOT NULL DEFAULT 'N';

ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREP_ITEM ADD COLUMN PARENT_ITEM_ID INT(11) NULL;
ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREP_ITEM ADD COLUMN INSTRUCTIONS VARCHAR(255) NULL;
ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREP_ITEM ADD COLUMN RECIPE_NOTES VARCHAR(255) NULL;
ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREPARATION ADD COLUMN RECIPE_NOTES VARCHAR(255) NULL;

ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREP_ITEM DROP COLUMN RECIPE_NOTES;
ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREPARATION DROP COLUMN RECIPE_NOTES;

ALTER TABLE KETTLE_SCM_DEV.UNIT_DETAIL ADD COLUMN COMPANY_ID INTEGER NOT NULL;

UPDATE KETTLE_SCM_DEV.UNIT_DETAIL SET COMPANY_ID = 1000;

UPDATE `KETTLE_SCM_DEV`.`UNIT_DETAIL` SET `COMPANY_ID`='1001' WHERE `UNIT_ID`='22001';
UPDATE `KETTLE_SCM_DEV`.`UNIT_DETAIL` SET `COMPANY_ID`='1001' WHERE `UNIT_ID`='22002';

ALTER TABLE KETTLE_SCM_DEV.PAYMENT_REQUEST ADD COLUMN COMPANY_ID INT(11) NOT NULL AFTER PAID_ADHOC;

ALTER TABLE KETTLE_SCM_DEV.VENDOR_DETAIL_DATA ADD COLUMN DISCLAIMER_ACCEPTED VARCHAR(1) NOT NULL DEFAULT 'N';
