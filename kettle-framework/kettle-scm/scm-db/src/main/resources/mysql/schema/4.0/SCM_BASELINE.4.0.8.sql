ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION MODIFY COLUMN PRODUCT_CODE VARCHAR(30);

SET SQL_SAFE_UPDATES=0;
UPDATE KETTLE_SCM_DEV.PRODUCT_DEFINITION p SET PRODUCT_CODE = (
CASE p.CATEGORY_ID
    WHEN 1 THEN CONCAT("P-CG00",p.PRODUCT_ID)
    WHEN 2 THEN CONCAT("P-CS00",p.PRODUCT_ID)
    WHEN 3 THEN CONCAT("P-FA00",p.PRODUCT_ID)
    WHEN 4 THEN CONCAT("P-SF00",p.PRODUCT_ID)
END
);
SET SQL_SAFE_UPDATES=1;

ALTER TABLE KETTLE_SCM_DEV.SKU_DEFINITION ADD COLUMN SKU_CODE VARCHAR(30);

set sql_safe_updates=0;
UPDATE SKU_DEFINITION sd LEFT JOIN PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sd.LINKED_PRODUCT_ID
SET sd.SKU_CODE = (
CASE pd.CATEGORY_ID
   WHEN 1 THEN CONCAT("S-CG",LPAD(sd.SKU_ID,8,'0'))
   WHEN 2 THEN CONCAT("S-CS",LPAD(sd.SKU_ID,8,'0'))
   WHEN 3 THEN CONCAT("S-FA",LPAD(sd.SKU_ID,8,'0'))
   WHEN 4 THEN CONCAT("S-SF",LPAD(sd.SKU_ID,8,'0'))
END);
SET SQL_SAFE_UPDATES=1;

ALTER TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM ADD COLUMN PRINT_COUNT INTEGER DEFAULT NULL;

CREATE TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREPARATION(
 ITEM_PREPARATION_ID INT PRIMARY KEY AUTO_INCREMENT,
 RECIPE_ID INT NOT NULL,
 REQUESTED_BY INT NOT NULL,
 PREPARATION_QUANTITY DECIMAL(10,2) NOT NULL,
 REQUESTING_TIME DATETIME NOT NULL,
 PLAN_ITEM_ID INT NOT NULL,
 FOREIGN KEY (PLAN_ITEM_ID) REFERENCES PLAN_ORDER_ITEM(PLAN_ITEM_ID)
);

CREATE TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREPARATION(
 ITEM_PREPARATION_ID INT PRIMARY KEY AUTO_INCREMENT,
 RECIPE_ID INT NOT NULL,
 REQUESTED_BY INT NOT NULL,
 PREPARATION_QUANTITY DECIMAL(10,2) NOT NULL,
 REQUESTING_TIME DATETIME NOT NULL,
 PLAN_ITEM_ID INT NOT NULL,
 FOREIGN KEY (PLAN_ITEM_ID) REFERENCES PLAN_ORDER_ITEM(PLAN_ITEM_ID)
);

CREATE TABLE KETTLE_SCM_DEV.PLAN_ORDER_ITEM_PREP_ITEM(
 ITEM_PREPARATION_ITEM_ID INT PRIMARY KEY AUTO_INCREMENT,
 PRODUCT_ID INT NOT NULL,
 PRODUCT_NAME VARCHAR(250) NOT NULL,
 QUANTITY DECIMAL(10,2) NOT NULL,
 UNIT_OF_MEASURE VARCHAR(10) NOT NULL,
 ITEM_PREPARATION_ID INT NOT NULL,
 FOREIGN KEY (ITEM_PREPARATION_ID) REFERENCES PLAN_ORDER_ITEM_PREPARATION(ITEM_PREPARATION_ID)
);

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`) VALUES ('SHFUD', '7', 'ACTION', 'SHOW', 'Show filtered unit list in manage mappings sub menus', 'ACTIVE');
