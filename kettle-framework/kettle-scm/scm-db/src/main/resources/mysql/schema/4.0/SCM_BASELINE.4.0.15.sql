ALTER TABLE KETTLE_SCM_DEV.VENDOR_GOODS_RECEIVED_DATA ADD COLUMN TO_BE_PAID VARCHAR(1) NOT NULL DEFAULT 'Y';

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN PARTICIPATES_IN_PNL VARCHAR(1) NOT NULL DEFAULT 'Y';




UPDATE PRODUCT_DEFINITION SET PARTICIPATES_IN_PNL = 'N' WHERE PRODUCT_ID IN (100001,
100003,
100005,
100009,
100020,
100040,
100109,
100155,
100172,
100187,
100204,
100252,
100276,
100291,
100292,
100373,
100381,
100382,
100419,
100514,
100515,
100531,
100533,
100543,
100546,
100612,
100613,
100615,
100624,
100629,
100634,
100635,
100642,
100643,
100645,
100657,
100658,
100659,
100689,
100701,
100724,
100760,
100761,
100773,
100892,
100977,
101031,
101065,
101077,
101086,
101091,
101126,
101131,
101173,
101174,
101175,
101176,
101177,
101178,
101186,
101207,
101216,
101233,
101254,
101264,
101265,
101289,
101291,
101298,
101312,
101316,
101332,
101355,
101391);

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER ADD COLUMN BUDGET_APPLIED VARCHAR(1) NOT NULL DEFAULT 'Y';

ALTER TABLE KETTLE_SCM_DEV.REQUEST_ORDER  CHANGE COLUMN `BUDGET_APPLIED` `BUDGET_APPLIED` VARCHAR(1) NULL DEFAULT NULL ;

UPDATE KETTLE_SCM_DEV.REQUEST_ORDER  SET BUDGET_APPLIED = NULL WHERE GENERATION_TIME < '2018-03-01 05:00:00' OR IS_SPECIAL_ORDER = 'Y' OR REFERENCE_ORDER_ID IS NOT NULL;

ALTER TABLE `KETTLE_SCM_DEV`.`REQUEST_ORDER` ADD COLUMN `BUDGET_REASON` VARCHAR(50) NULL DEFAULT NULL AFTER `BUDGET_APPLIED`;