ALTER TABLE KETTLE_SCM_DEV.PURCHASE_ORDER ADD COLUMN ORDER_TYPE VARCHAR(26) AFTER COMPANY_ID;

UPDATE KETTLE_SCM_DEV.PURCHASE_ORDER SET ORDER_TYPE = 'REGULAR_ORDER';

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD IS_BULK_GR_ALLOWED VARCHAR(1);

UPDATE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION` SET `IS_BULK_GR_ALLOWED`='N';

CREATE TABLE KETTLE_SCM_DEV.PROFILE_DEFINITION(
   PROFILE_ID INT NOT NULL AUTO_INCREMENT,
   PROFILE_NAME VARCHAR(100) NOT NULL,
   PROFILE_DESCRIPTION VARCHAR(256),
   CREATION_DATE DATETIME,
   CREATED_BY INT,
   PROFILE_CODE VARCHAR(10),
   PROFILE_STATUS VARCHAR(40),
   PRIMARY KEY ( PROFILE_ID )
);

CREATE TABLE KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING(
   PROFILE_ATTRIBUTE_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
   PROFILE_ID INT NOT NULL,
   ATTRIBUTE_ID INT NOT NULL,
   CREATION_DATE DATETIME,
   CREATED_BY INT,
   IS_DEFINED_AT_PRODUCT VARCHAR(10),
   IS_DEFINED_AT_SKU VARCHAR(10),
   IS_DEFINED_AT_ASSET VARCHAR(10),
   IS_MANDATORY_AT_PRODUCT VARCHAR(10),
   IS_MANDATORY_AT_SKU VARCHAR(10),
   IS_MANDATORY_AT_ASSET VARCHAR(10),
   IS_OVERRIDABLE_AT_PRODUCT VARCHAR(10),
   IS_OVERRIDABLE_AT_ASSET VARCHAR(10),
   LAST_UPDATED_BY INT,
   LAST_UPDATED_DATE DATETIME,
   PRIMARY KEY ( PROFILE_ATTRIBUTE_MAPPING_ID ),
   FOREIGN KEY ( PROFILE_ID ) REFERENCES KETTLE_SCM_DEV.PROFILE_DEFINITION( PROFILE_ID ),
   FOREIGN KEY ( ATTRIBUTE_ID ) REFERENCES KETTLE_SCM_DEV.ATTRIBUTE_DEFINITION( ATTRIBUTE_ID )
);

CREATE TABLE KETTLE_SCM_DEV.ENTITY_ATTRIBUTE_VALUE_MAPPING(
   ENTITY_ATTRIBUTE_VALUE_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
   PROFILE_ID INT NOT NULL,
   ATTRIBUTE_ID INT NOT NULL,
   ATTRIBUTE_VALUE_ID INT NOT NULL,
   PROFILE_ATTRIBUTE_MAPPING_ID INT NOT NULL,
   ENTITY_TYPE VARCHAR(50) NOT NULL,
   ENTITY_ID INT NOT NULL,
   CREATION_DATE DATETIME,
   CREATED_BY INT,
   STATUS VARCHAR(50),
   PRIMARY KEY ( ENTITY_ATTRIBUTE_VALUE_MAPPING_ID )
);

ALTER TABLE KETTLE_SCM_DEV.PRODUCT_DEFINITION ADD COLUMN PROFILE_ID INT;

ALTER TABLE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION`

ADD INDEX `PROFILE_DEFINITION_IBFK_3_IDX` (`PROFILE_ID` ASC)  COMMENT '';

ALTER TABLE `KETTLE_SCM_DEV`.`PRODUCT_DEFINITION`
ADD CONSTRAINT `PROFILE_DEFINITION_IBFK_3`
  FOREIGN KEY (`PROFILE_ID`)
  REFERENCES `KETTLE_SCM_DEV`.`PROFILE_DEFINITION` (`PROFILE_ID`)
  ON DELETE NO ACTION
  ON UPDATE NO ACTION;


CREATE TABLE KETTLE_SCM_DEV.ASSET_DEFINITION(
   ASSET_ID INT NOT NULL AUTO_INCREMENT,
   ASSET_NAME VARCHAR(255) NOT NULL,
   ASSET_IMAGE_URL VARCHAR(255) ,
   UNIT_ID INT NOT NULL,
   UNIT_TYPE VARCHAR(50) NOT NULL,
   ASSET_STATUS VARCHAR(50) NOT NULL,
   SKU_ID INT NOT NULL,
   PRODUCT_ID INT NOT NULL,
   PROFILE_ID INT NOT NULL,
   GR_ID INT NOT NULL,
   GR_ITEM_ID INT NOT NULL,
   VENDOR_ID INT NOT NULL,
   VENDOR_NAME VARCHAR(255),
   OWNER_TYPE VARCHAR(50) NOT NULL,
   OWNER_ID INT NOT NULL,
   FIRST_OWNER_TYPE VARCHAR(50) NOT NULL,
   FIRST_OWNER_ID INT NOT NULL,
   TAG_TYPE VARCHAR(50) ,
   TAG_VALUE INT ,
   TAG_PRINT_COUNT INT,
    LAST_TAG_PRINT_DATE DATETIME,
    LAST_TAG_PRINTED_BY INT ,
    PRICE DECIMAL(16,6),
    TAX DECIMAL(16,6),
    PROCUREMENT_COST DECIMAL(16,6),
    QUANTITY INT NOT NULL,
    LIFE_TIME_TYPE VARCHAR(50),
    LIFE_TIME_VALUE DECIMAL(16,6),
    LIFE_TIME_IN_DAYS INT ,
    INVENTORY_DATE DATETIME,
    START_DATE DATETIME,
	EXPECTED_END_DATE DATETIME,
    ACTUAL_END_DATE DATETIME,
    DEPRECIATION_STRATEGY VARCHAR(50),
    DEPRECIATION_RATE_PA DECIMAL(16,6),
    DAILY_DEPRECIATION_RATE DECIMAL (16,6),
    DEPRECIATION_RESIDUE DECIMAL(16,6),
    REALIZED_DEPRECIATION DECIMAL(16,6),
    REALIZED_DEPRECIATION_DATE  DATETIME,
    LAST_TRANSFER_TYPE VARCHAR(50),
    LAST_TRANSFER_ID INT ,
    LAST_TRANSFER_DATE DATETIME,
    LAST_TRANSFER_BY INT,
    HAS_WARRANTY VARCHAR(50),
	WARRANTY_LAST_DATE DATETIME,
    HAS_AMC VARCHAR(50),
    AMC_LAST_DATE DATETIME,
    HAS_INSURANCE VARCHAR(50),
    INSURANCE_LAST_DATE DATETIME,
	CREATION_DATE DATETIME,
	CREATED_BY INT,

 PRIMARY KEY ( ASSET_ID )

);

CREATE TABLE KETTLE_SCM_DEV.ASSET_DEFINITION_LOG(
	ASSET_DEFINITION_LOG_ID INT NOT NULL AUTO_INCREMENT,
   ASSET_ID INT NOT NULL,
   UNIT_ID INT NOT NULL,
   UNIT_TYPE VARCHAR(50) NOT NULL,
   ASSET_STATUS VARCHAR(50) NOT NULL,
   OWNER_TYPE VARCHAR(50) NOT NULL,
   OWNER_ID INT NOT NULL,
   TAG_TYPE VARCHAR(50) ,
   TAG_VALUE INT ,
   TAG_PRINT_COUNT INT,
    LAST_TAG_PRINT_DATE DATETIME,
    LAST_TAG_PRINTED_BY INT ,

    DEPRECIATION_STRATEGY VARCHAR(50),
    DEPRECIATION_RATE_PA DECIMAL(16,6),
    DAILY_DEPRECIATION_RATE DECIMAL (16,6),
    DEPRECIATION_RESIDUE DECIMAL(16,6),

    REALIZED_DEPRECIATION DECIMAL(16,6),
    REALIZED_DEPRECIATION_DATE  DATETIME,
    LAST_TRANSFER_TYPE VARCHAR(50),
    LAST_TRANSFER_ID INT ,
    LAST_TRANSFER_DATE DATETIME,
    LAST_TRANSFER_BY INT,
	UPDATION_DATE DATETIME,
	UPDATION_BY INT,

 PRIMARY KEY ( ASSET_DEFINITION_LOG_ID )

);

CREATE TABLE KETTLE_SCM_DEV.STOCK_EVENT_DEFINITION (
	EVENT_ID INT NOT NULL AUTO_INCREMENT,
	UNIT_ID INT NOT NULL,
	UNIT_TYPE VARCHAR(255),

	EVENT_TYPE VARCHAR(255) NOT NULL,
	EVENT_STATUS VARCHAR(50) NOT NULL,

	EVENT_INITIATOR_ID INT NOT NULL,
	EVENT_CREATION_DATE DATETIME,
	AUDITOR_ID INT ,
	AUDIT_TIMESTAMP DATETIME,
	AUDIT_STATUS VARCHAR(255) ,
 PRIMARY KEY ( EVENT_ID )

);

CREATE TABLE KETTLE_SCM_DEV.STOCK_EVENT_ASSET_MAPPING (
	STOCK_EVENT_ASSET_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
	EVENT_ID INT NOT NULL,
	UNIT_ID INT NOT NULL,
	ASSET_ID INT NOT NULL,
	ASSET_STATUS VARCHAR(50) NOT NULL,
	CREATION_DATE DATETIME NOT NULL,
	AUDIT_STATUS VARCHAR(255) ,
	AUDITED_BY INT,
	AUDIT_TIMESTAMP DATETIME,
	PRIMARY KEY (STOCK_EVENT_ASSET_MAPPING_ID )
);

CREATE TABLE KETTLE_SCM_DEV.ASSET_DEPRECIATION_MAPPING (
	ASSET_DEPRECIATION_MAPPING_ID INT NOT NULL AUTO_INCREMENT,
	ASSET_ID INT NOT NULL,
	UNIT_ID INT NOT NULL,
	ASSET_STATUS VARCHAR(50) NOT NULL,
OWNER_ID INT NOT NULL,
	START_DATE_DEPRECIATED_VALUE DECIMAL(16,6) NOT NULL,
	START_DATE DATETIME NOT NULL,
	END_DATE DATETIME NOT NULL,
    	DEPRECIATION_RATE_PA DECIMAL(16,6) NOT NULL,
    	DAILY_DEPRECIATION_RATE DECIMAL (16,6) NOT NULL,
	TAX DECIMAL(16,6),

	PRIMARY KEY (ASSET_DEPRECIATION_MAPPING_ID )

);


CREATE TABLE KETTLE_SCM_DEV.DEPRECIATION_SUMMARY (
	DEPRECIATION_SUMMARY_ID INT NOT NULL AUTO_INCREMENT,
	AUDIT_ID INT NOT NULL,
	AUDITOR_ID INT,
	AUDIT_DATE DATETIME NOT NULL,

	ASSET_ID INT NOT NULL,
	OWNER_ID INT NOT NULL,

DEPRECIATION_RATE_PA DECIMAL(16,6) NOT NULL,
    	DAILY_DEPRECIATION_RATE DECIMAL (16,6) NOT NULL,

	START_DATE DATETIME NOT NULL,
END_DATE DATETIME NOT NULL,
START_DATE_VALUE DECIMAL(16,6) NOT NULL,
    	END_DATE_VALUE DECIMAL (16,6) NOT NULL,

	TOTAL_DEPRECIATED_VALUE DECIMAL (16,6) NOT NULL,

	PRIMARY KEY (DEPRECIATION_SUMMARY_ID)
);

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION_LOG CHANGE TAG_VALUE TAG_VALUE VARCHAR(50);

ALTER TABLE KETTLE_SCM_DEV.ASSET_DEFINITION CHANGE TAG_VALUE TAG_VALUE VARCHAR(50);

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER ADD COLUMN TO_TYPE VARCHAR(225);

UPDATE KETTLE_SCM_DEV.TRANSFER_ORDER SET TO_TYPE = 'REGULAR_TRANSFER';

ALTER TABLE  KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD COLUMN ASSOCIATED_ASSET_ID INT;

ALTER TABLE KETTLE_SCM_DEV.TRANSFER_ORDER_ITEM ADD COLUMN ASSOCIATED_ASSET_TAG_VALUE VARCHAR(40);

ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED ADD COLUMN TO_TYPE VARCHAR(255);

UPDATE KETTLE_SCM_DEV.GOODS_RECEIVED SET TO_TYPE = 'REGULAR_TRANSFER';


ALTER TABLE  KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN ASSOCIATED_ASSET_ID INT;


ALTER TABLE KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM ADD COLUMN ASSOCIATED_ASSET_TAG_VALUE VARCHAR(40);

ALTER TABLE KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING ADD COLUMN STATUS VARCHAR(40);

CREATE TABLE KETTLE_SCM_DEV.GATEPASS_ITEM_ASSET_MAPPING (
	GATEPASS_ITEM_ASSET_MAPPING_ID INT NOT NULL AUTO_INCREMENT ,
	GATEPASS_ID INT NOT NULL,
	GATEPASS_ITEM_ID INT NOT NULL,
	ASSET_ID INT NOT NULL,
	ASSET_TAG_VALUE VARCHAR(40) NOT NULL,
	GATEPASS_TYPE VARCHAR(225) NOT NULL,
	PRIMARY KEY (GATEPASS_ITEM_ASSET_MAPPING_ID)
);

ALTER TABLE KETTLE_SCM_DEV.GATEPASS_DATA ADD COLUMN IS_ASSET_GATE_PASS VARCHAR(40);

UPDATE KETTLE_SCM_DEV.GATEPASS_DATA SET IS_ASSET_GATE_PASS = 'N';

ALTER TABLE KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING ADD PARTICIPATE_IN_NAME VARCHAR(10) AFTER IS_OVERRIDABLE_AT_ASSET;

UPDATE KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING SET PARTICIPATE_IN_NAME = 'N';

ALTER TABLE KETTLE_SCM_DEV.PROFILE_ATTRIBUTE_MAPPING ADD PROFILE_ATTRIBUTE_MAPPING_IMG VARCHAR(255);

CREATE  TABLE KETTLE_SCM_DEV.ENTITY_ASSET_MAPPING (
ENTITY_ASSET_MAPPING_ID INT NOT NULL AUTO_INCREMENT ,
	ENTITY_ID INT NOT NULL,
ENTITY_TYPE VARCHAR(255) NOT NULL,
ENTITY_CATEGORY VARCHAR(255) NOT NULL,
	ENTITY_SUB_ID INT NOT NULL,
	ASSET_ID INT NOT NULL,
	ASSET_TAG_VALUE VARCHAR(40) NOT NULL,
	PRIMARY KEY (ENTITY_ASSET_MAPPING_ID)
);

ALTER TABLE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE ADD COLUMN ASSET_ORDER VARCHAR(40);

UPDATE KETTLE_SCM_DEV.SALES_PERFORMA_INVOICE SET ASSET_ORDER = 'N';

