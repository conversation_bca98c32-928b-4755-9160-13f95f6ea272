/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 24-05-2016.
 */
angular.module('scmApp').service('packagingService', ['$rootScope', '$http', 'appUtil', 'apiJson','metaDataService',
    function ($rootScope, $http, appUtil, apiJson,metaDataService) {

    var service = {};
    service.definitions = {};

    function addToType(type, obj, destinationObj) {
        if (appUtil.isEmptyObject(destinationObj[type])) {
            destinationObj[type] = [];
        }
        destinationObj[type].push(obj);
    }


    service.getAllProfiles = function (callback) {
        metaDataService.getAllPackagings(function(definitions){
            if (!appUtil.isEmptyObject(definitions)) {
                service.definitions = {};
                for (var def in definitions) {
                    var obj = definitions[def];
                    routeProfile(obj,service.definitions);
                }
            }
            if(callback!=undefined){
                callback(service.definitions);
            }
        });
    };

    function routeProfile(obj, destinationObj){
        try{
            switch (obj.packagingType) {
                case "CASE":
                    addToType("CASE", obj,destinationObj);
                    break;
                case "INNER":
                    addToType("INNER", obj,destinationObj);
                    break;
                case "LOOSE":
                    addToType("LOOSE", obj,destinationObj);
                    break;
                default :
                    addToType("LOOSE", obj,destinationObj);
                    break;
            }
        }catch(e){
            console.log(obj,destinationObj);
        }
    }

    service.setAllProfiles = function(definitionMap){
        service.definitions = {};
        for (var def in definitionMap) {
            var obj = definitionMap[def];
            routeProfile(obj,service.definitions);
        }
    };

    service.setMappingsByProduct = function(product,packagingMappingList){
        var packagingMappings = [];
        if(appUtil.checkEmpty(packagingMappingList)){
           console.log("No packaging mapping found for ",product.productId,product.productName);
        }else{
            packagingMappingList.filter(function(packagingMapping){
                return packagingMapping.productId == product.productId;
            }).map(function(packagingMapping){
                var profile = service.getProfile(packagingMapping.packagingId);
                packagingMappings.push(profile);
            });
            product.packagingMappings = packagingMappings;
        }
        return product;
    };

    service.getAllPackagingMappings = function(callback){
      metaDataService.getAllPackagingMappings(callback,false);
    };

    service.getProfile = function(id){
        var definitions = service.definitions;
        for(var type in definitions){
            var list = definitions[type];
            for(var index in list){
                if(id!=undefined && id==list[index].packagingId){
                    return angular.copy(list[index]);
                }
            }
        }
        return null;
    };


    service.getPackagingByProduct = function(productId){
        var url = apiJson.urls.productManagement.packagingMappingsByProduct+"?productId="+productId;
        return $http.get(url).then(function success(response) {
            if (typeof response.data === 'object') {
                return response.data;
            } else {
                return $q.reject(response.data);
            }
        }, function error(response) {
            console.log("error:" + response);
        });
    };


    service.addSubPackagings = function(list, item,callback) {
        while (item.subPackagingId!=null){
            item = service.getProfile(item.subPackagingId);
            callback(list,item);
        }
    };


    return service;
}]);

