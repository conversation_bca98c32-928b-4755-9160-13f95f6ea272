/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function() {
    'use strict';

    angular.module('scmApp').factory('PrintService', PrintService);

    PrintService.$inject = ['$rootScope'];
    function PrintService($rootScope) {
        var service = {};

        service.printHTMLOnBilling = printHTMLOnBilling;
        service.printHTMLOnKot = printHTMLOnKot;
        service.isAndroid = isAndroid();
        service.printOnBilling = printOnBilling;
        service.printOnKot = printOnKot;
        $rootScope.printerStatus = false;
        service.loadQZPrinter = loadQZPrinter;
        service.printBarCode = printBarCode;

        return service;

        function isAndroid() {
            var isAndroid = false;
            if (/Android/i.test(navigator.userAgent)) {
                isAndroid = true;
            }
            return isAndroid;
        }

        function printOnBilling(printText, type) {
            if (type == 'RAW') {
                printRawOnBilling(printText);
            } else {
                printHTMLOnBilling(printText);
            }
        }

        function printOnKot(printText, type) {
            if (type == 'RAW') {
                printRawOnKot(printText);
            } else {
                printHTMLOnKot(printText);
            }
        }

        function printHTMLOnBilling(printText){
            printHTML(printText,"Billing");
        }

        function printRawOnBilling(printText){
            printRaw(printText,"Billing");
        }

        function printHTMLOnKot(printText){
            printHTML(printText,"Kot");
        }

        function printRawOnKot(printText){
            printRaw(printText,"Kot");
        }

        function printBarCode(code) {
            textToBase64Barcode(code, "Labelling");
        }


        function printRaw(printText, printerName) {
            var config = qz.configs.create(printerName);
            var data = [{
                type: 'raw',
                data: printText
            }];
            qz.print(config, data).catch(function(e) {
                var message = null;
                if(e.message != null && e.message != ""){
                    message = "<br/>" + e.message;
                }
                bootbox.alert("<h4>Qz is not running!<br/>Please run Qz Tray " + message + "</h4>");
                console.log(e);
                loadQZPrinter();
            });
        }

        function printHTML(printText, printerName) {
            var config = qz.configs.create(printerName);
            var data = [{
                type: 'html',
                format: 'plain',
                data: printText
            }];
            qz.print(config, data).catch(function(e) {
                var message = null;
                if(e.message != null && e.message != ""){
                    message = "<br/>" + e.message;
                }
                bootbox.alert("<h4>Qz is not running!<br/>Please run Qz Tray " + message + "</h4>");
                console.log(e);
                loadQZPrinter();
            });
        }

        function textToBase64Barcode(code,printerName ){
            var qrWrapperCreated = document.createElement('div');
            qrWrapperCreated.style.paddingTop = '15px';
            qrWrapperCreated.style.width = '400px';
            qrWrapperCreated.style.height = '400px';

            document.body.appendChild(qrWrapperCreated);
            var _qrcodeWrapper = document.createElement('div');
             _qrcodeWrapper.style.marginTop = '25px';
            // _qrcodeWrapper.style.marginRight = '10px';
            // _qrcodeWrapper.style.width = '200px';
            // _qrcodeWrapper.style.height = '200px';
            // _qrcodeWrapper.style.cssFloat  = 'left';
            qrWrapperCreated.appendChild(_qrcodeWrapper);
            var _qrcode = document.createElement('div');
            _qrcodeWrapper.appendChild(_qrcode);
            var _qrText = document.createElement('div');
             _qrText.style.marginTop = '10px';
            // _qrText.style.width = '400px';
            // _qrText.style.height = '400px';
            // _qrText.style.cssFloat  = 'right';
             _qrText.style.fontSize = '28px';
            qrWrapperCreated.appendChild(_qrText);
            var _qrAssetName = document.createElement('div');
            var subCatCode = ""
            if(code.subCategoryCode == 'KITCHEN_EQUIPMENT') {
                subCatCode = 'KEQ';
            } else if(code.subCategoryCode == 'IT') {
                subCatCode = 'IT';
            } else if(code.subCategoryCode == 'OFFICE_EQUIPMENT') {
                subCatCode = 'OEQ';
            } else if(code.subCategoryCode == 'FURNITURE') {
                subCatCode = 'FR';
            }
            _qrAssetName.innerHTML = code.assetName + ' (' + subCatCode + ')';
            _qrText.appendChild(_qrAssetName);
            var _qrAssetSubCategory = document.createElement('div');
            _qrAssetSubCategory.innerHTML = "";
            _qrText.appendChild(_qrAssetSubCategory);
            var _qrAssetTagValue = document.createElement('div');
            _qrAssetTagValue.innerHTML = code.assetTagValue + "";
            _qrAssetTagValue.style.marginTop = '10px';
            _qrAssetTagValue.style.fontSize = '36px';
            _qrAssetTagValue.style.fontWeight = 'bold';
            _qrText.appendChild(_qrAssetTagValue);


            var qrcode = new QRCode(_qrcode, {
                text: code.assetTagValue + "",
                height: 200,
                width : 200,
                colorDark : "#000000",
                colorLight : "#ffffff",
                correctLevel : QRCode.CorrectLevel.M
            });

            html2canvas(qrWrapperCreated).then(function (canvas) {
                document.body.appendChild(canvas);
                var base64 = canvas.toDataURL("image/png");
                console.log(base64);

                qrcode.clear();
                _qrAssetName.innerHTML = "";
                _qrAssetSubCategory.innerHTML = "";
                _qrAssetTagValue.innerHTML = "";
                qrWrapperCreated.remove();
                canvas.remove();
                printBarcode1( base64, printerName);
            })

        }



        function printBarcode1(base64, printerName) {
            console.log(base64)
            var barcodeImg = base64.split(",")[1];
            var printData = [
                {
                    type: 'image',
                    format: 'base64',
                    data: barcodeImg
                }
            ];
            var config = qz.configs.create(printerName, {
                orientation: 'portrait',
                colorType: 'grayscale'
            });
            qz.print(config,printData).catch(function(e) {
                var message = null;
                if(e.message != null && e.message != ""){
                    message = "<br/>" + e.message;
                }
                alert("<h4>Qz is not running!<br/>Please run Qz Tray " + message + "</h4>");
                console.log(e);
                loadQZPrinter();
            });
        }


        function loadQZPrinter() {

            qz.security.setCertificatePromise(function(resolve, reject) {

                var cetificate = "-----BEGIN CERTIFICATE-----\n" +
                    "MIIFUDCCAzqgAwIBAgIFMTYwODkwCwYJKoZIhvcNAQEFMIGYMQswCQYDVQQGEwJV\n" +
                    "UzELMAkGA1UECAwCTlkxGzAZBgNVBAoMElFaIEluZHVzdHJpZXMsIExMQzEbMBkG\n" +
                    "A1UECwwSUVogSW5kdXN0cmllcywgTExDMRkwFwYDVQQDDBBxemluZHVzdHJpZXMu\n" +
                    "Y29tMScwJQYJKoZIhvcNAQkBFhhzdXBwb3J0QHF6aW5kdXN0cmllcy5jb20wHhcN\n" +
                    "MjIwODE1MDQwMDAwWhcNMjMwODI3MDQwMDAwWjCCARkxCzAJBgNVBAYMAklOMRIw\n" +
                    "EAYDVQQIDAlOZXcgRGVsaGkxEjAQBgNVBAcMCU5ldyBEZWxoaTEqMCgGA1UECgwh\n" +
                    "U3Vuc2hpbmUgVGVhaG91c2UgUHJpdmF0ZSBMaW1pdGVkMSowKAYDVQQLDCFTdW5z\n" +
                    "aGluZSBUZWFob3VzZSBQcml2YXRlIExpbWl0ZWQxKjAoBgNVBAMMIVN1bnNoaW5l\n" +
                    "IFRlYWhvdXNlIFByaXZhdGUgTGltaXRlZDEgMB4GCSqGSIb3DQEJAQwRbW9oaXRA\n" +
                    "Y2hhYXlvcy5jb20xPDA6BgNVBA0MM3JlbmV3YWwtb2YtNmMxMzNkMjNiZTllYmY1\n" +
                    "MWQ4MWFlMWY2NTYwZDI2MDUzMDgwNGQxNzCCASAwCwYJKoZIhvcNAQEBA4IBDwAw\n" +
                    "ggEKAoIBAQDOxhNxq/6oGXoJp4Q0tAhZ5yo4VcETiqISeOmmydqct5hiRmAY6YJW\n" +
                    "zKNj9Qi185Woy838grn6jq+A7izkB6t/SCy2DLuxU7r8xfURi72ZfXHClaMe4jrM\n" +
                    "uhemBHguHsQTXlDoAWDLrCmdSXgwzZJSOoBRbf0ziANtCtqG3W+a6eBBU2ApaPD0\n" +
                    "3ohARkVcSm9Q6jys7BKDsaz+NgJPltySqO42Z/XKsN4jUIdrlO2rrhnTelyRopiK\n" +
                    "a5YmIHkrF7lUuhCiOIgkOImZMGEYdWRUbWCZCO9t/E33ATm4R7umGad8LzWWweCt\n" +
                    "AOgCBRvap79Y6uOFwrP//4cqwGn4ItuJAgMBAAGjIzAhMB8GA1UdIwQYMBaAFJCm\n" +
                    "ULeE1LnqX/IFhBN4ReipdVRcMAsGCSqGSIb3DQEBBQOCAgEAWJvJNjkaHckuS97R\n" +
                    "6X97zStGQVOeBk+rxew2ZIGx/eXXxV30Tx2Zxr9dP27j4i7w40rGPjpU7e0+EMcl\n" +
                    "s6aSTRgA39Do9ieYE9wv5SRxhU1Y6VdFsVH8QPlAisET5cr/MtU45iL/h6kCvZU5\n" +
                    "KVP1HtK7wmWv5eF/29WS1amRQEwHV4UwXQT8PGT3Onpzzf2/xgLat77umjNkn6gA\n" +
                    "DIYsKlh2JTVJaWov370f1d3NMaonUeYbfqZoychaWbpGiL69v00BH+ecOVmOGuc7\n" +
                    "6CBCz8rwbPukED5uUbVQeeD4ijy0GL7m1N+MB49D+vgdc1yBHOhv38RWekIaJkZw\n" +
                    "MQwnaJom4dMYEfrBSvNEEHtzXyTnmP5FUovKBO/XHbEVTCLqXH/4aAViOtHSf6DX\n" +
                    "+3HULtBmiFd1JEwPkgo9l8dR0E9O5hxuspREEKRIT2+748xRprRXqiX59Ip5f9Oe\n" +
                    "LVLZ2RLqJiXxCvR1wLipt2vX4EM/uE6C6xQ+n65Y3dV+s+ZRcWoOWhVwFN6CAP1X\n" +
                    "QeIRyHu7wsnwBPujC79hpMAVX8n7gkxVNuiPirh6GG7fo582I7flPRW8+HE18q5r\n" +
                    "8ghmyVisvU4bkZ8WhK8MoftM8tKt2NnmLLs0cP1yVzN4UWV/Q7Kz9aGnqYDQLE9P\n" +
                    "/oPWm8KRXZ3bUsmomaXXJi0mwE8=\n" +
                    "-----END CERTIFICATE-----\n" +
                    "--START INTERMEDIATE CERT--\n" +
                    "-----BEGIN CERTIFICATE-----\n" +
                    "MIIFEjCCA/qgAwIBAgICEAAwDQYJKoZIhvcNAQELBQAwgawxCzAJBgNVBAYTAlVT\n" +
                    "MQswCQYDVQQIDAJOWTESMBAGA1UEBwwJQ2FuYXN0b3RhMRswGQYDVQQKDBJRWiBJ\n" +
                    "bmR1c3RyaWVzLCBMTEMxGzAZBgNVBAsMElFaIEluZHVzdHJpZXMsIExMQzEZMBcG\n" +
                    "A1UEAwwQcXppbmR1c3RyaWVzLmNvbTEnMCUGCSqGSIb3DQEJARYYc3VwcG9ydEBx\n" +
                    "emluZHVzdHJpZXMuY29tMB4XDTE1MDMwMjAwNTAxOFoXDTM1MDMwMjAwNTAxOFow\n" +
                    "gZgxCzAJBgNVBAYTAlVTMQswCQYDVQQIDAJOWTEbMBkGA1UECgwSUVogSW5kdXN0\n" +
                    "cmllcywgTExDMRswGQYDVQQLDBJRWiBJbmR1c3RyaWVzLCBMTEMxGTAXBgNVBAMM\n" +
                    "EHF6aW5kdXN0cmllcy5jb20xJzAlBgkqhkiG9w0BCQEWGHN1cHBvcnRAcXppbmR1\n" +
                    "c3RyaWVzLmNvbTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBANTDgNLU\n" +
                    "iohl/rQoZ2bTMHVEk1mA020LYhgfWjO0+GsLlbg5SvWVFWkv4ZgffuVRXLHrwz1H\n" +
                    "YpMyo+Zh8ksJF9ssJWCwQGO5ciM6dmoryyB0VZHGY1blewdMuxieXP7Kr6XD3GRM\n" +
                    "GAhEwTxjUzI3ksuRunX4IcnRXKYkg5pjs4nLEhXtIZWDLiXPUsyUAEq1U1qdL1AH\n" +
                    "EtdK/L3zLATnhPB6ZiM+HzNG4aAPynSA38fpeeZ4R0tINMpFThwNgGUsxYKsP9kh\n" +
                    "0gxGl8YHL6ZzC7BC8FXIB/0Wteng0+XLAVto56Pyxt7BdxtNVuVNNXgkCi9tMqVX\n" +
                    "xOk3oIvODDt0UoQUZ/umUuoMuOLekYUpZVk4utCqXXlB4mVfS5/zWB6nVxFX8Io1\n" +
                    "9FOiDLTwZVtBmzmeikzb6o1QLp9F2TAvlf8+DIGDOo0DpPQUtOUyLPCh5hBaDGFE\n" +
                    "ZhE56qPCBiQIc4T2klWX/80C5NZnd/tJNxjyUyk7bjdDzhzT10CGRAsqxAnsjvMD\n" +
                    "2KcMf3oXN4PNgyfpbfq2ipxJ1u777Gpbzyf0xoKwH9FYigmqfRH2N2pEdiYawKrX\n" +
                    "6pyXzGM4cvQ5X1Yxf2x/+xdTLdVaLnZgwrdqwFYmDejGAldXlYDl3jbBHVM1v+uY\n" +
                    "5ItGTjk+3vLrxmvGy5XFVG+8fF/xaVfo5TW5AgMBAAGjUDBOMB0GA1UdDgQWBBSQ\n" +
                    "plC3hNS56l/yBYQTeEXoqXVUXDAfBgNVHSMEGDAWgBQDRcZNwPqOqQvagw9BpW0S\n" +
                    "BkOpXjAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQAJIO8SiNr9jpLQ\n" +
                    "eUsFUmbueoxyI5L+P5eV92ceVOJ2tAlBA13vzF1NWlpSlrMmQcVUE/K4D01qtr0k\n" +
                    "gDs6LUHvj2XXLpyEogitbBgipkQpwCTJVfC9bWYBwEotC7Y8mVjjEV7uXAT71GKT\n" +
                    "x8XlB9maf+BTZGgyoulA5pTYJ++7s/xX9gzSWCa+eXGcjguBtYYXaAjjAqFGRAvu\n" +
                    "pz1yrDWcA6H94HeErJKUXBakS0Jm/V33JDuVXY+aZ8EQi2kV82aZbNdXll/R6iGw\n" +
                    "2ur4rDErnHsiphBgZB71C5FD4cdfSONTsYxmPmyUb5T+KLUouxZ9B0Wh28ucc1Lp\n" +
                    "rbO7BnjW\n" +
                    "-----END CERTIFICATE-----";
                resolve(cetificate);
            });

            var privateKey = "-----BEGIN PRIVATE KEY-----\n" +
                "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDOxhNxq/6oGXoJ\n" +
                "p4Q0tAhZ5yo4VcETiqISeOmmydqct5hiRmAY6YJWzKNj9Qi185Woy838grn6jq+A\n" +
                "7izkB6t/SCy2DLuxU7r8xfURi72ZfXHClaMe4jrMuhemBHguHsQTXlDoAWDLrCmd\n" +
                "SXgwzZJSOoBRbf0ziANtCtqG3W+a6eBBU2ApaPD03ohARkVcSm9Q6jys7BKDsaz+\n" +
                "NgJPltySqO42Z/XKsN4jUIdrlO2rrhnTelyRopiKa5YmIHkrF7lUuhCiOIgkOImZ\n" +
                "MGEYdWRUbWCZCO9t/E33ATm4R7umGad8LzWWweCtAOgCBRvap79Y6uOFwrP//4cq\n" +
                "wGn4ItuJAgMBAAECggEAFR8Hy63WV4ixrfSH9UO6EifiKMq+rA62TzV9ZXl7PJSm\n" +
                "6sv2QzkwBIqG+p4EH6Tj8c/uqLdYRLsoO37oxDqx1pCya36puft771fPuJT5/yig\n" +
                "kxAqr0wL1k+AfKd6eLiRDgrtcAN7Bhb+EbhYYADRrFdYNekqquW+9teb2piaLrw7\n" +
                "mHG5/T93Weksx5KNYVRqfBPHQYwqTdxCe/HZCKlwryhja65AE4b8WZRCTy5HTZwC\n" +
                "M4DlnTPIQw5rL5kprTOQdL4g99fNqJR2dovW/rcbiYGPgrGjMdh3Q3RjMmNd2TMz\n" +
                "YT5Tp/z5pQMI5Cws8/SfNoI0MyxqaBycIuQkEhAUsQKBgQD/FV/UfzK0Q70TM8XT\n" +
                "CpucQk+mJEUHmhtBUCvllNYgDp7MXL9gYM43LwhL7LwtP8+0X2AWopKyWcrH+b/j\n" +
                "WCwDASPr05QBPOcy8C+OAkoEKmOyFA4A8HuXo4FEfA2KHbtrdomPrj1V3IVcq4Ph\n" +
                "BXHtmBhFUd3ISrgfBfr70gN9WQKBgQDPhEQt7DVEiABd3Ep9f+GRX5324k/KsL1j\n" +
                "sajimRKz0l5sDer/oIn9fIpa/7X+9F4qu+HTKyqrVp5W0BLtH7bEJzuyE1ZPQfuO\n" +
                "sUo6CMHap9GInOJ/9IHyM0uK8+us8FbBWgXY6OjTCHrSSA+8MBKqIqCej6Yzy9cs\n" +
                "lUjxV2eZsQKBgQCDnG2jYDWzGLg3biEFsJV1XCloWZJcR8EEQ+9CNzb7t09rtfbw\n" +
                "LUJ46oVnVzAIUMJEGGmlhCAYcYfVAiFllZqvuijkhnf444mOmKqQpNG3sH9b5EkN\n" +
                "Zwb9yFjsEu2Rc6G94p07SvVOlcchd0VGFt8fbbgoHANIUnUJXLaQs10lkQKBgBEf\n" +
                "uQCRHVQqZN1Z8Euq4dI9MavNwQfYzcgMQQNx3jk4gtIn09yEQt7ICCK6Nypyv9KG\n" +
                "7nuedEbvPuGrCeTHWS2WjzaCofyoVTnRJ27iihyg/IlpaMdmRdLWqMUum6QJsR8D\n" +
                "brgXgB7p9Dil+aZt0Rx4/wgWkoBzsa3cI8jGjaYBAoGBANL7qUad7pwidhwLKfza\n" +
                "zawjpnm1JpzLHp1j0VTRcuVp4sFQ4X7PtbBpLBvrFIgFHouXasPwAXQBXAcAjoXM\n" +
                "wryyAAGw+ch9ZioSFxxs9P22EI2rDRlVoqDl4dOUn1el5e+NnrT7/RAULnxuE2o8\n" +
                "fF4Jf70zqswoB6o7EXCfn5Mz\n" +
                "-----END PRIVATE KEY-----";

            qz.security.setSignaturePromise(function(toSign) {
                return function(resolve, reject) {
                    try {
                        var pk = KEYUTIL.getKey(privateKey);
                        var sig = new KJUR.crypto.Signature({"alg": "SHA1withRSA"});
                        sig.init(pk);
                        sig.updateString(toSign);
                        var hex = sig.sign();
                        resolve(stob64(hextorstr(hex)));
                    } catch (err) {
                        try{
                            $.ajax({
                                method : 'POST',
                                url : '/scm-service/print/v1/printer/secure/url/for',
                                data : toSign,
                                async : false,
                                success : resolve
                            });
                        }catch (e){
                            reject(e);
                        }
                    }
                };
            });

            qz.websocket.setClosedCallbacks(function(evt) {
                console.log(evt);
                $rootScope.$apply(function(){
                    $rootScope.printerStatus = false;
                });
                qz.websocket.disconnect();
                if (evt.reason) {
                    console.log("Connection closed: " + evt.reason);
                }
                printOnBilling("","RAW");
            });

            launchQZ(false);
        }

        // / Connection ///
        function launchQZ(forced) {
            if (!qz.websocket.isActive() || forced) {
                // Retry 5 times, pausing 1 second between each attempt
                startConnection({
                    retries : 100,
                    delay : 1
                });
            }
        }

        function startConnection(config) {
            if (!qz.websocket.isActive()) {
                qz.websocket.connect(config).then(function() {
                    console.log("QZ Connected!");
                    $rootScope.$apply(function(){
                        $rootScope.printerStatus = true;
                    });
                });
            } else {
                console.log("An active connection with QZ already exists.");
            }
        }
    }

})();
