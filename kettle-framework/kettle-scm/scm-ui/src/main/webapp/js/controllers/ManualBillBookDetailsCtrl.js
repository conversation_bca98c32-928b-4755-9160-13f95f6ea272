/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON><PERSON> on 12-08-2017.
 */

'use strict';

angular.module('scmApp')
.controller('ManualBillBookCtrl', ['$scope','appUtil','$toastService','apiJson','$http',
	function ($scope, appUtil,$toastService,apiJson,$http) {
	$scope.manualBBDetails=$scope.manualBillBookDetails;
	$scope.saveManualBillBookDetails = function(){
	    	if($scope.transferOrderDetail != undefined){
		    $scope.manualBBDetails.generatedForUnitId=$scope.transferOrderDetail.generatedForUnitId;
	    	} else if($scope.requestOrderDetail != undefined){
		    $scope.manualBBDetails.generatedForUnitId=$scope.requestOrderDetail.requestUnit;
	    	}else{
	    	    $toastService.create("Requesting Unit Is Undefined!");
	    	    return false;
	    	}
		if(appUtil.isEmptyObject($scope.manualBBDetails) || appUtil.isEmptyObject($scope.manualBBDetails.startNo) || appUtil.isEmptyObject($scope.manualBBDetails.endNo)){
			$toastService.create("Start No and End No both are required!");
			return false;
		}
		
		if(!appUtil.isNumberInteger($scope.manualBBDetails.startNo) || !appUtil.isNumberInteger($scope.manualBBDetails.endNo)){
			$toastService.create("Start No and End No both  can not contain decimal value !");
			return false;
		}
		
		if( $scope.manualBBDetails.endNo - $scope.manualBBDetails.startNo != 399 ){
			$toastService.create("Please verify Start No and End No as these details are invalid !");
			return false;
		}
		
		console.log("$scope.manualBBDetails",$scope.manualBBDetails);
		$http({
			method: "POST",
			url: apiJson.urls.manualBillBookManagement.validateManualBillBook,
			data: $scope.manualBBDetails,
			  headers: {'Content-Type': 'application/json'}
		}).then(function success(response) {
			if(response.data){
				$toastService.create("Manual bill book start no :  " + $scope.manualBBDetails.startNo + " and end no : "
						+$scope.manualBBDetails.endNo +" validated successfully!");
				$scope.updateBillBookDetailsObject($scope.manualBBDetails);
				$scope.cancel();
			}else{
				$toastService.create("Manual bill book already exist!");
			}
		}, function error(response) {
			$toastService.create("Could not create manual bill book details. Please try again later");
			console.log("error:" , response);
		});
		console.log(" $scope.manualBBDetails", $scope.manualBBDetails);
	};

	$scope.cancel=function(){
		$scope.toggleBillBookDetailsView();
	};

}
]
);
