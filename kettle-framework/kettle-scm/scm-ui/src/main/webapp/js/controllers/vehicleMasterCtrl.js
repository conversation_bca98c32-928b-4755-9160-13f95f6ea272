/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limit`ed
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('scmApp')
    .controller('vehicleMasterCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye','$timeout','$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService,
                  $fileUploadService, $alertService, previewModalService, Popeye, $timeout, $window) {

            $scope.init = function () {
                $scope.vehicleList = [];
                $scope.getVehicleList();
                $scope.vehicle = null;
            };
            
            $scope.getVehicleList = function () {
            	$scope.vehicleList = null;
                    $http({
                        method: "GET",
                        url: apiJson.urls.vehicleData.vehicleList
                    }).then(function success(response) {
                    	$scope.vehicleList = response.data;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
            }
            
            $scope.editVehicleData = function(vehicleId){
            	console.log(vehicleId);
                    $http({
                        method: "GET",
                        url: apiJson.urls.vehicleData.singleVehicle+"?vehicleId="+vehicleId
                    }).then(function success(response) {
                    	$scope.vehicle = response.data;
                    	$scope.vehicleName = $scope.vehicle.name;
                    	$scope.registrationNumber = $scope.vehicle.registrationNumber;
                    	$scope.model = $scope.vehicle.model;
                    	$scope.make = $scope.vehicle.make;
                    	$scope.transportMode = $scope.vehicle.transportMode;
                    	$scope.vehicleStatus = $scope.vehicle.vehicleStatus;
                    	$scope.multiDispatch = $scope.vehicle.multiDispatch == true ? "Y" : "N";
                    	$scope.vehicleEditModal($scope.vehicle,vehicleId);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
            
            }
            
            $scope.vehicleEditModal = function (vehicle,vehicleId) {
                var modalInstances = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'vehicleModal.html',
                    controller: 'vehicleModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope: $scope,
                    size: 'lg',
                    resolve: {
                        items: function () {
                            return {
                            	vehicleName: vehicle.name, registrationNumber: vehicle.registrationNumber,
                            	model: vehicle.model, make: vehicle.make,
                            	transportMode: vehicle.transportMode,vehicleStatus: vehicle.status,
                            	multiDispatch: vehicle.multiDispatch,vehicleId: vehicleId, mode: 'edit'
                            }
                        }
                    }
                });
                modalInstances.closed
                .then(function (isSuccessful) {
                    if (isSuccessful) {
                    	$scope.getVehicleList();
                    }
                });
            };
            
            $scope.vehicleModal = function () {
                var modalInstance = Popeye.openModal({
                    ariaLabelledBy: 'modal-title',
                    ariaDescribedBy: 'modal-body',
                    templateUrl: 'vehicleModal.html',
                    controller: 'vehicleModalCtrl',
                    backdrop: 'static',
                    keyboard: false,
                    scope:$scope,
                    size: 'lg',
                    resolve: {
                        items: function () {
                            return {
                            	mode: 'add'
                            }
                        }
                    }
                });
                modalInstance.closed
                .then(function (isSuccessful) {
                    if (isSuccessful) {
                    	$scope.getVehicleList();
                    }
                });
            };
            
    }]);