/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular
    .module('scmApp')
    .controller(
        'BulkTrOrderMgtCtrl',
        [
            '$rootScope',
            '$scope',
            'apiJson',
            '$alertService',
            '$http',
            '$state',
            '$timeout',
            '$window',
            '$location',
            'appUtil',
            '$toastService',
            'metaDataService',
            'previewModalService',
            function ($rootScope, $scope, apiJson, $alertService,$http, $state, $timeout,$window, $location,appUtil, $toastService) {


                $scope.init = function (){
                    var bulkTrObj = appUtil.getBulkTrOrderListObj();
                    $scope.startDate = bulkTrObj.startDate;
                    $scope.endDate = bulkTrObj.endDate;
                    $scope.bulkEventList = bulkTrObj.bulkEventList;
                    $scope.selectedEventId = bulkTrObj.selectedEventId;
                    $scope.selectedEventTOs = bulkTrObj.selectedEventTOs;
                    $scope.allSelected = bulkTrObj.allSelected;
                    $scope.collapsed =true;
                    $scope.selectedTOs = bulkTrObj.selectedTOs;
                    $scope.submit = false;
                }

                $scope.isSubmitDisabled = function (){
                    return appUtil.isEmptyObject($scope.startDate) || appUtil.isEmptyObject($scope.endDate);
                }

                $scope.checkAll = function (){
                            $scope.selectedTOs = [];
                            for(var i = 0;i<$scope.selectedEventTOs.length;i++){
                                $scope.selectedEventTOs[i].checked = !$scope.allSelected;
                                if(!$scope.allSelected){
                                    $scope.selectedTOs.push($scope.selectedEventTOs[i].id);
                                }
                            }
                            $scope.allSelected = !$scope.allSelected;
                }




                $scope.reset = function () {
                    $scope.bulkEventList = [];
                    $scope.selectedEventId = null;
                    $scope.selectedEventTOs = [];
                    $scope.allSelected = false;
                    $scope.collapsed =true;
                    $scope.selectedTOs = [];
                };

                $scope.selectEvent = function (eventId){
                    $scope.selectedEventId = eventId;
                    $scope.getEvent();
                    $scope.selectedTOs = [];
                    $scope.allSelected = false;
                }

                $scope.getEvent = function (callback) {

                    if ($scope.startDate != null) {
                        $http({
                            url: apiJson.urls.transferOrderManagement.findBulkEvent,
                            method: "GET",
                            params: {
                                unitId : appUtil.getUnitData().id,
                                bulkEventId : $scope.selectedEventId
                            }
                        }).then(function (response) {
                            if(callback!=null){
                                callback(response.data);
                            }else{
                                if (!appUtil.checkEmpty(response)) {
                                    $scope.selectedEventTOs = response.data.transferOrderList;
                                    $scope.allSelected = false;
                                }
                            }

                        }, function (response) {
                            console.log(response);
                        });
                    } else {
                        $toastService.create("Select a Event First");
                    }
                };

                $scope.isDisabled = function (eventId){
                    if($scope.selectedTOs.length == 0){
                        return true;
                    }

                    if($scope.selectedEventId == eventId){
                            return false;
                    }

                    return true;
                }


                $scope.setTransferInvoices = function (eventId){
                    $scope.selectedEventId = eventId;
                    $scope.getEvent($scope.setInvoices);
                }

                $scope.setInvoices = function (data) {
                    var eventTOs = [];
                    data.transferOrderList.forEach(function (transfer){
                        eventTOs.push(transfer.id);
                    });
                    if (eventTOs.length> 0) {
                        $http({
                            url: apiJson.urls.transferOrderManagement.setTransferInvoices,
                            method: "POST",
                            data : eventTOs,
                            params : {
                                eventId : $scope.selectedEventId
                            }
                        }).then(function (response) {
                            if (!appUtil.checkEmpty(response)) {
                                if(response.data){
                                    $alertService.alert("Success","successfully inserted Invoices ids At All Transfer Orders ");
                                    $scope.getEvents();
                                }
                            }
                        }, function (response) {
                            $alertService.alert("error while inserting invoices id");
                            console.log(response);
                        });
                    } else {
                        $toastService.create("Select a bulk Event First");
                    }
                };


                $scope.getEvents = function () {
                    $scope.reset();
                    if ($scope.startDate != null) {
                        $http({
                            url: apiJson.urls.transferOrderManagement.findBulkEventsShort,
                            method: "GET",
                            params: {
                                unitId : appUtil.getUnitData().id,
                                startDate : $scope.startDate,
                                endDate : $scope.endDate
                            }
                        }).then(function (response) {
                            if (!appUtil.checkEmpty(response)) {
                                $scope.bulkEventList = response.data;
                            }
                        }, function (response) {
                            console.log(response);
                        });
                    } else {
                        $toastService.create("Select a date first");
                    }
                };


                $scope.openTrOrderAction = function (to) {
                    $rootScope.selectedTrOrderId = to.id;
                    $rootScope.previousScreen = "/menu/BulkTrOrderMgt";
                    appUtil.setBulkTrOrderListObj({
                        startDate : $scope.startDate,
                        endDate : $scope.endDate,
                        bulkEventList : $scope.bulkEventList,
                        selectedEventId :  $scope.selectedEventId,
                        selectedEventTOs : $scope.selectedEventTOs,
                        selectedTOs : $scope.selectedTOs,
                        allSelected : $scope.allSelected
                    });
                    $location.path("/menu/trOrderAction");
                }



                $scope.selectTO = function (toId){
                    var index = $scope.selectedTOs.indexOf(toId);
                    if(index == -1){
                        $scope.selectedTOs.push(toId);
                    }else{
                        $scope.selectedTOs.splice(index,1);
                        $scope.allSelected = false;
                    }
                }

                $scope.getTOsView = function (){
                    var eventId = $scope.selectedEventId;
                    $http({
                        url: apiJson.urls.transferOrderManagement.getTransferOrdersView,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        data: $scope.selectedTOs,
                        params : {
                            isBulkEvent : true
                        },
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                        .success(
                            function (data) {
                                var fulfillmentDate = appUtil
                                    .getFormattedDate($scope.selectedDate);
                                var fileName =  "Bulk-Event-"
                                    + eventId
                                    + ".xlsx";
                                var blob = new Blob(
                                    [data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }).error(function (err) {
                        console.log("Error during getting data", err);
                    });

                }


            }]);
