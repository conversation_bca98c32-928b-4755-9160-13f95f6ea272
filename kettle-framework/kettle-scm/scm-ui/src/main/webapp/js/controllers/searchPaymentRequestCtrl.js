/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('searchPaymentRequestCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$alertService','$fileUploadService','previewModalService','$timeout','Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $alertService, $fileUploadService,previewModalService,$timeout, Popeye) {

            $scope.init = function () {
                $scope.selectView = true;
                $scope.selectedPRStatus = null;
                $scope.itemDeviations = [];
                $scope.invoiceDeviations = [];
                $scope.invoiceRejections = [];
                $scope.adhocPaymentReason = null;
                $scope.selectedStatus = null;
                $scope.unitList = [{id: null, name: ""}];
                $scope.selectedUnit = null;
                $scope.selectedServiceVendor = null;
                $scope.vendorList = [];
                $scope.selectedVendor = null;
                $scope.getEmployeeMappedUnits();
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.selectAllPrs = false;
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.showPreview = previewModalService.showPreview;
                getAllVendors();

                metaDataService.getServiceVendors(function (vendors) {
                   $scope.serviceVendors = vendors;
                });

                metaDataService.getSCMMetaData(function (metadata) {

                    $scope.prRequestTypes = metadata.paymentRequestTypes;
                    $scope.prRequestType = $scope.prRequestTypes == null ? null : $scope.prRequestTypes[0];
                    $scope.prStatusList = [{id:null, name:""}];
                    metadata.paymentRequestStatus.map(function (status, index) {
                        $scope.prStatusList.push({id:index, name:status});
                    });
                    //$scope.prStatusList = $scope.prStatusList.concat(metadata.paymentRequestStatus);
                    metadata.paymentDeviations.map(function (deviation) {
                        if (deviation.deviationType == "DEVIATION") {
                            if (deviation.deviationLevel == "INVOICE_ITEM") {
                                $scope.itemDeviations.push(deviation);
                            } else if (deviation.deviationLevel == "INVOICE") {
                                $scope.invoiceDeviations.push(deviation);
                            }
                        } else if (deviation.deviationType == "REJECTION") {
                            $scope.invoiceRejections.push(deviation);
                        }
                    })
                });

            };
            
            $scope.getEmployeeMappedUnits = function () {
                appUtil.getUnitList().map(function (unit) {
                    if ($rootScope.mappedUnits.indexOf(unit.id) >= 0) {
                        $scope.unitList.push(unit);
                    }
                });
                if ($scope.unitList != null && $scope.unitList.length > 0) {
                    $scope.selectedUnit = $scope.unitList[0];
                }
            };

            $scope.selectUnit = function (unit) {
                $scope.selectedUnit = unit;
                $scope.getMappedVendors();
            };


            function getAllVendors() {
                $http({
                    url: apiJson.urls.vendorManagement.vendorsTrimmed,
                    method: 'GET'
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $scope.vendorList = [];
                    response.map(function (item) {
                        $scope.vendorList.push(item);
                    });
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Could not get vendors list.");
                    }
                });
            };


            $scope.getMappedVendors = function () {
                console.log($scope.selectedUnit);
                if ($scope.selectedUnit.id !== null) {
                    $scope.vendorList = [];
                    $http({
                        method: "GET",
                        url: apiJson.urls.skuMapping.getVendorsForUnitTrimmed + "?unitId=" + $scope.selectedUnit.id
                    }).then(function success(response) {
                        response.data.map(function (item) {
                            //if (item.status === "ACTIVE") {
                                $scope.vendorList.push(item);
                            //}
                        })
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                } else {
                    $scope.vendorList = [{
                        id: null,
                        name: ""
                    }];
                    $scope.selectedVendor = $scope.vendorList[0];
                }

            };

            function getReqObj() {
                var reqObj = null;
                if($scope.prRequestType.shortCode=="GR"){
                    reqObj = {
                        url:apiJson.urls.paymentRequestManagement.getPaymentRequests,
                        params:{
                            unitId: $scope.selectedUnit == null ? null : $scope.selectedUnit.id,
                            startDate: $scope.startDate,
                            endDate: appUtil.formatDate(appUtil.calculatedDate(1, $scope.endDate), 'yyyy-MM-dd'),
                            vendorId: $scope.selectedVendor == null ? null : $scope.selectedVendor.id,
                            prId: $scope.prId,
                            grId: $scope.grId,
                            invoiceNumber: $scope.invoiceNumber,
                            requestType: $scope.prRequestType.code,
                            status:$scope.selectedPRStatus==null?null:$scope.selectedPRStatus.name,
                            paymentDate: $scope.paymentDate == null || $scope.paymentDate.length < 2 ? null:$scope.paymentDate
                        }
                    };
                }else if($scope.prRequestType.shortCode=="SR"){
                    reqObj = {
                        url: apiJson.urls.paymentRequestManagement.getPaymentRequests,
                        params:{
                            startDate: $scope.startDate,
                            endDate: appUtil.formatDate(appUtil.calculatedDate(1, $scope.endDate), 'yyyy-MM-dd'),
                            vendorId: $scope.selectedServiceVendor == null ? null : $scope.selectedServiceVendor.id,
                            companyId: $scope.selectedCompany == null ? null : $scope.selectedCompany.id,
                            prId: $scope.prId,
                            grId: $scope.grId,
                            invoiceNumber: $scope.invoiceNumber,
                            requestType: $scope.prRequestType.code,
                            status:$scope.selectedPRStatus==null?null:$scope.selectedPRStatus.name,
                            paymentDate: $scope.paymentDate == null || $scope.paymentDate.length < 2 ? null:$scope.paymentDate
                        }
                    };
                }
                return reqObj;
            }


            $scope.findPrs = function () {
                $scope.showNoPR = false;
                $scope.selectAllPrs = false;
                if ($scope.startDate == null || $scope.startDate == "") {
                    $toastService.create("Please select start date");
                } else if ($scope.endDate == null || $scope.endDate == "") {
                    $toastService.create("Please select end date");
                } else {
                    //$scope.prRequestType.shortCode=prType.shortCode;
                    var reqObj = getReqObj();
                    $http({
                        method: 'GET',
                        url: reqObj.url,
                        params: reqObj.params
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.prs = response.data;
                            $scope.applySpecialFilters();
                        } else {
                            $scope.prs = [];
                            $scope.showNoPR = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            };

            $scope.backToSelectView = function () {
                $scope.selectView = true;
            };

            $scope.selectAllPaymentRequests = function () {
                $scope.selectAllPrs = !$scope.selectAllPrs;
                $scope.filteredPrs.map(function (pr) {
                    pr.selected = $scope.selectAllPrs;
                });
            };

            $scope.applySpecialFilters = function () {
                $scope.filteredPrs = [];
                if($scope.debitFilter != true && $scope.blockFilter != true && $scope.validFilter != true){
                    $scope.filteredPrs = angular.copy($scope.prs);
                }else{
                    if($scope.blockFilter == true){
                        $scope.filteredPrs = $scope.filteredPrs.concat($scope.prs.filter(function (a) {
                            return a.blocked == true;
                        }));
                    }
                    if($scope.validFilter == true){
                        $scope.filteredPrs = $scope.filteredPrs.concat($scope.prs.filter(function (a) {
                            return a.blocked != true;
                        }));
                    }
                }
            };



            $scope.viewPaymentRequest = function (pr, actionType) {
                $rootScope.showFullScreenLoader = true;
                $scope.selectView = false;
                $scope.actionType = actionType;
                $scope.uploadedDocData = null;
                $scope.selectedPaymentDate = null;
                $scope.viewPr = null;
                $http({
                    url: apiJson.urls.paymentRequestManagement.paymentRequest,
                    method: 'GET',
                    params: {
                        paymentRequestId: pr.paymentRequestId
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.viewPr = response;

                        if (actionType == 'APPROVE'){
                        	$scope.getProposedPaymentDates();	
                        }
                    } else {
                        $toastService.create("Could not find payment request.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            $scope.setAvailableDeviations = function (item, type) {
                $scope.availableDevs = [];
                $scope.selectedItemForDev = item;
                $scope.selectedItemForDevType = type;
                var devList;
                if (type === "INVOICE") {
                    devList = $scope.invoiceDeviations;
                } else {
                    devList = $scope.itemDeviations;
                }
                devList.map(function (dev) {
                    var found = false;
                    item.deviations.map(function (dev1) {
                        if (dev.paymentDeviationId == dev1.paymentDeviation.paymentDeviationId) {
                            found = true;
                        }
                    });
                    if (!found) {
                        dev.checked = false;
                        dev.deviationRemark = null;
                        $scope.availableDevs.push({data: dev});
                    }
                });
            };

            $scope.addDeviations = function (item) {
                var unchecked = [];
                $scope.availableDevs.map(function (dev) {
                    if (dev.checked) {
                        if ($scope.actionType === "UPDATE_INVOICE") {
                            item.deviations.push({
                                mappingId: null,
                                paymentDeviation: dev.data,
                                deviationRemark: dev.remark,
                                currentStatus: "CREATED",
                                createdBy: appUtil.createGeneratedBy(),
                            });
                        } else {
                            item.deviations.push({
                                mappingId: null,
                                paymentDeviation: dev.data,
                                deviationRemark: dev.remark,
                                currentStatus: "ACCEPTED",
                                createdBy: appUtil.createGeneratedBy(),
                                acceptedBy: appUtil.createGeneratedBy(),
                                addType: "NEW"
                            });
                        }
                    } else {
                        unchecked.push(dev);
                    }
                });
                $scope.availableDevs = unchecked;
            };

            $scope.deleteDeviation = function (list, index) {
                var items = list.splice(index, 1);
                $scope.availableDevs.push({data: items[0].paymentDeviation});
            };

            /*$scope.setAvailableRejections = function () {
                $scope.availableRejections = [];
                $scope.invoiceRejections.map(function (dev) {
                    var found = false;
                    $scope.viewPr.paymentInvoice.rejections.map(function (dev1) {
                        if (dev.paymentDeviationId == dev1.paymentDeviation.paymentDeviationId) {
                            found = true;
                        }
                    });
                    if (!found) {
                        dev.checked = false;
                        $scope.availableRejections.push({data: dev});
                    }
                });
            };

            $scope.addRejections = function () {
                $scope.availableRejections.map(function (dev) {
                    if (dev.checked) {
                        $scope.viewPr.paymentInvoice.rejections.push({
                            mappingId: null,
                            paymentDeviation: dev.data,
                            deviationRemark: null,
                            currentStatus: "ACCEPTED",
                            createdBy: appUtil.createGeneratedBy(),
                            acceptedBy: appUtil.createGeneratedBy()
                        });
                    }
                });
            };

            $scope.deleteRejection = function (list, index) {
                var items = list.splice(index, 1);
                $scope.availableRejections.push({data: items[0].paymentDeviation});
            };*/

            /*$scope.deviationAction = function (item, type) {
                if (type == "APPROVE") {
                    item.currentStatus = "ACCEPTED";
                    item.acceptedBy = appUtil.createGeneratedBy();
                } else if (type == "REJECT") {
                    item.currentStatus = "REJECTED";
                    item.rejectedBy = appUtil.createGeneratedBy();
                } else if (type == "REMOVE") {
                    item.currentStatus = "REMOVED";
                    item.removedBy = appUtil.createGeneratedBy();
                }
            };*/

            $scope.changeStatus = function (pr, status) {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.paymentRequestManagement.paymentRequestStatusChange,
                    method: 'POST',
                    data: {
                        paymentRequestId: pr.paymentRequestId,
                        updatedBy: appUtil.createGeneratedBy().id,
                        newStatus: status,
                        paymentType: $scope.prRequestType.code
                    }
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null && response.updated == true) {
                        pr.currentStatus = status;
                        $toastService.create("Status updated successfully.");
                    } else {
                        $toastService.create("Could not update payment request status.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Status update failed");
                    }
                });
            };/*

            $scope.approvePaymentRequest = function () {
                $scope.viewPr.updatedBy = appUtil.createGeneratedBy();
                $scope.viewPr.paymentCycle = $scope.selectedPaymentDate;
                $scope.viewPr.paymentInvoice.paymentAmount = $scope.viewPr.paidAmount;
                var devs = $scope.viewPr.paymentInvoice.deviations;
                $scope.viewPr.paymentInvoice.paymentInvoiceItems.map(function (item) {
                    if (item.deviations.length > 0) {
                        devs.concat(item.deviations);
                    }
                });
                var found = false;
                devs.map(function (dev) {
                    if (!found && dev.currentStatus === "CREATED") {
                        found = true;
                    }
                });
                if (found) {
                    $toastService.create("Please take action on all invoice and item deviations.");
                }/!* else if ($scope.viewPr.proposedAmount < $scope.viewPr.paidAmount) {
                    $toastService.create("Paid amount should not be greater than proposed amount.");
                }*!/ else if ($scope.viewPr.paymentCycle == null) {
                    $toastService.create("Please select payment date.");
                } else {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: apiJson.urls.paymentRequestManagement.approve,
                        method: 'POST',
                        data: $scope.viewPr
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null && response == true) {
                            $toastService.create("Payment request approved successfully.");
                            $scope.backToSelectView();
                            $scope.findPrs();
                        } else {
                            $toastService.create("Could not update payment request status.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                        } else {
                            $toastService.create("Failed to approve request.");
                        }
                    });
                }
            };

            $scope.rejectPaymentRequest = function () {
                if ($scope.viewPr.paymentInvoice.rejections.length == 0) {
                    $toastService.create("Please add reason for rejection.");
                } else {
                    $rootScope.showFullScreenLoader = true;
                    $scope.viewPr.updatedBy = appUtil.createGeneratedBy();
                    $http({
                        url: apiJson.urls.paymentRequestManagement.reject,
                        method: 'POST',
                        data: $scope.viewPr
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null && response == true) {
                            $toastService.create("Payment request rejected successfully.");
                            $scope.backToSelectView();
                            $scope.findPrs();
                        } else {
                            $toastService.create("Could not update payment request status.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                        } else {
                            $toastService.create("Failed to reject request.");
                        }
                    });
                }
            };*/

            $scope.updatePaymentRequest = function (force) {
                if ($scope.viewPr.paymentInvoice.invoiceNumber == null) {
                    $toastService.create("Please provide invoice number.");
                } else if ($scope.viewPr.paymentInvoice.invoiceDate == null) {
                    $toastService.create("Please provide invoice date.");
                } else if ($scope.uploadedDocData == null) {
                    $toastService.create("Please attach invoice document.");
                } else {
                    $scope.viewPr.deviationCount = $scope.viewPr.paymentInvoice.deviations.length;
                    $scope.viewPr.paymentInvoice.paymentInvoiceItems.map(function (item) {
                        $scope.viewPr.deviationCount += item.deviations.length;
                    });
                    $scope.viewPr.paymentInvoice.invoiceDocumentHandle = $scope.uploadedDocData.documentId;
                    $scope.viewPr.updatedBy = appUtil.createGeneratedBy();
                    if(force === true) {
                        $scope.viewPr.forceCreate = true;
                    }
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentRequestUpdateInvoice,
                        method: 'POST',
                        data: $scope.viewPr,
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null) {
                            $alertService.alert("Payment Request updated successfully",
                                "Payment request updated successfully with request id " + response.paymentRequestId, function () {
                                    $scope.backToSelectView();
                                    $scope.findPrs();
                                })
                            //$toastService.create("Payment Request creation successful.");
                        } else {
                            $toastService.create("Payment Request updation failed.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $alertService.alert("Payment Request updation failed", response.errorMsg, function () {
                        }, true)
                    });
                }
            };

            /*$scope.createDebitNote = function () {
                $scope.debitNote = {
                    debitNoteId: null,
                    paymentRequestId: $scope.viewPr.paymentRequestId,
                    invoiceNumber: $scope.viewPr.paymentInvoice.invoiceNumber,
                    amount: $scope.viewPr.proposedAmount-$scope.viewPr.paidAmount,
                    totalTaxes: 0,
                    totalAmount: null,
                    generatedBy: appUtil.createGeneratedBy(),
                    lastUpdatedBy: appUtil.createGeneratedBy(),
                    busyReferenceNumber: null
                };
                $scope.calculateTotalAmount();
            };

            $scope.calculateTotalAmount = function () {
                $scope.debitNote.totalAmount = parseInt($scope.debitNote.amount > 0 ? $scope.debitNote.amount : 0) +
                    parseInt($scope.debitNote.totalTaxes > 0 ? $scope.debitNote.totalTaxes : 0)
            };

            $scope.submitDebitNote = function () {

                if(!appUtil.isEmptyObject($scope.debitNote.busyReferenceNumber)){
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: apiJson.urls.paymentRequestManagement.addDebitNote,
                        method: 'POST',
                        data: $scope.debitNote
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null && response.debitNoteId != null) {
                            $scope.viewPr.debitNote = response;
                            $toastService.create("Debit note added successfully.");
                        } else {
                            $toastService.create("Could not update payment request status.");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response.errorMsg != null) {
                            $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                        } else {
                            $toastService.create("Status update failed");
                        }
                    });
                }else{
                    $toastService.create("Please add busy reference number.");
                }
            };

            $scope.getProposedPaymentDates = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.paymentRequestManagement.paymentDates,
                    method: 'POST',
                    data: $scope.viewPr.paymentRequestId
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.proposedPaymentDates = response;
                        if($scope.selectedPaymentDate == null && response.length > 0){
                        	$scope.selectedPaymentDate = response[0];
                        }
                    } else {
                        $toastService.create("Could not get payment dates.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error getting payment dates.");
                    }
                });
            };*/

            $scope.loadInvoiceDoc = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.paymentRequestManagement.getInvoice,
                    method: 'POST',
                    data: $scope.viewPr.paymentInvoice.invoiceDocumentHandle
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Could not get document detail.");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.errorMsg != null) {
                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                    } else {
                        $toastService.create("Error getting document detail.");
                    }
                });
            };

            /*$scope.downloadPaymentSheet = function () {
                var error = false;
                var reqObj = {
                    paymentRequestIds: [],
                    bankName: $scope.bankName,
                    updatedBy: appUtil.createGeneratedBy().id
                };
                $scope.prs.map(function (pr) {
                    if (pr.selected === true) {
                        if (!error) {
                            if (pr.currentStatus !== "APPROVED" || pr.blocked == true || pr.debitExceeded == true) {
                                $toastService.create("Payment request id: " + pr.paymentRequestId + " is not authorized for payment.");
                                error = true;
                            } else {
                                reqObj.paymentRequestIds.push(pr.paymentRequestId)
                            }
                        }
                    }
                });
                if (reqObj.paymentRequestIds.length == 0) {
                    $toastService.create("No valid payment requests selected for payment initiation.");
                } else if (!error) {
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentSheet,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        data: reqObj,
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    }).success(function (data) {
                        if (appUtil.checkEmpty(data)) {
                            var fileName = "PAYMENT_REQUEST_" + $scope.bankName + "_Sheet_" + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss") + ".xls";
                            var blob = new Blob([data], {
                                type: 'c'
                            }, fileName);
                            saveAs(blob, fileName);
                            $scope.findPrs();
                            $scope.selectAllPrs = false;
                        } else {
                            $toastService.create("Could not fetch payment sheet. Please try again later");
                        }
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                    });
                }
            };

            $scope.blockPaymentRequests = function (type) {
                var error = false;
                var reqObj = {
                    paymentRequestIds: [],
                    bankName: $scope.bankName,
                    updatedBy: appUtil.createGeneratedBy().id,
                    reason:$scope.actionReason
                };
                $scope.filteredPrs.map(function (pr) {
                    if (pr.selected === true) {
                        if (!error) {
                            if (pr.blocked == type) {
                                $toastService.create("Payment request id: " + pr.paymentRequestId + " is "+
                                    (type?"already":"not"+" blocked."));
                                error = true;
                            } else {
                                reqObj.paymentRequestIds.push(pr.paymentRequestId)
                            }
                        }
                    }
                });
                if (reqObj.paymentRequestIds.length == 0) {
                    $toastService.create("No valid payment requests selected for "+(type?"blocking":"unblocking"));
                } else if (!error) {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: type?apiJson.urls.paymentRequestManagement.blockPaymentRequests:
                            apiJson.urls.paymentRequestManagement.unblockPaymentRequests,
                        method: 'POST',
                        data: reqObj
                    }).success(function (data) {
                        if (appUtil.checkEmpty(data)) {
                            $scope.findPrs();
                            $scope.selectAllPrs = false;
                        } else {
                            $toastService.create("Could not update payments. Please try again later");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };*/

            /*$scope.resetPaymentDetail = function (pr) {
                $scope.paymentDetail = {
                    vendorId: pr.vendorId.id,
                    vendorName: pr.vendorId.name,
                    beneficiaryAccountNumber: null,
                    beneficiaryIfscCode: null,
                    debitAccount: null,
                    debitBank: null,
                    paymentType: null,
                    paidAmount: null,
                    paymentDate: null,
                    remarks: null,
                    proposedAmount: null,
                    createdBy: appUtil.createGeneratedBy(),
                    utrNumber: null,
                    paymentRequests: []
                };
                $scope.selectedPaymentType = null;
                $scope.paymentTypeList = [{id:0,name:"NEFT"}, {id:1,name:"IFT"}, {id:2,name:"RTGS"}, {id:3,name:"IMPS"}];
                $scope.viewPr = pr;

                $rootScope.showFullScreenLoader = true;
                $http({
                    url: apiJson.urls.vendorManagement.getAccount+"?vendorId="+pr.vendorId.id,
                    method: 'GET'
                }).success(function (data) {
                    if (data!=null) {
                        $scope.paymentDetail.beneficiaryAccountNumber = data.accountNumber;
                        $scope.paymentDetail.beneficiaryIfscCode = data.ifsc;
                    } else {
                        $toastService.create("Could not fetch vendor account details.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }).error(function (err) {
                    $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.settlePaymentRequests = function () {
                $scope.paymentDetail.paymentType = $scope.selectedPaymentType==null?null:$scope.selectedPaymentType.name;
                if ($scope.paymentDetail.vendorId == null) {
                    $toastService.create("No vendor selected.");
                } else if ($scope.paymentDetail.beneficiaryAccountNumber == null) {
                    $toastService.create("Fill beneficiary account number.");
                } else if ($scope.paymentDetail.beneficiaryIfscCode == null) {
                    $toastService.create("Fill beneficiary IFSC.");
                } else if ($scope.paymentDetail.debitAccount == null) {
                    $toastService.create("Fill debit account number.");
                } else if ($scope.paymentDetail.debitBank == null) {
                    $toastService.create("Select debit bank.");
                } else if ($scope.paymentDetail.paymentType == null) {
                    $toastService.create("Select Payment type.");
                } else if ($scope.paymentDetail.paidAmount == null) {
                    $toastService.create("Fill paid amount.");
                } else if ($scope.paymentDetail.paymentDate == null) {
                    $toastService.create("Fill payment date.");
                } else if ($scope.paymentDetail.utrNumber == null) {
                    $toastService.create("Fill UTR number.");
                } else if ($scope.viewPr.blocked == true || $scope.viewPr.debitExceeded == true || $scope.viewPr.currentStatus != "SENT_FOR_PAYMENT") {
                    $toastService.create("Payment not allowed.");
                } else {
                    $rootScope.showFullScreenLoader = true;
                    $scope.paymentDetail.paymentRequests.push({paymentRequestId:$scope.viewPr.paymentRequestId});
                    $scope.paymentDetail.proposedAmount = $scope.viewPr.paidAmount;
                    $http({
                        url: apiJson.urls.paymentRequestManagement.paymentRequestSettle,
                        method: 'POST',
                        data: $scope.paymentDetail
                    }).success(function (data) {
                        if (data!=null && data == true) {
                            $scope.findPrs();
                            $toastService.create("Payment request settled.");
                        } else {
                            $toastService.create("Could not settle payments. Please try again later");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };*/

            /*$scope.resetPaidAmount = function () {
                $scope.viewPr.paidAmount = $scope.viewPr.proposedAmount;
            };*/

            /*$scope.setAction = function (pr, action) {
                if(pr!=null){
                    $scope.viewPr = pr;
                }
                $scope.action = action;
                $scope.actionReason = null;
            };*/

            /*$scope.submitAction = function () {
                if($scope.actionReason==null || $scope.actionReason.trim().length==0){
                    $toastService.create("Please fill reason.");
                }else {
                    if($scope.action=="ADHOC"){
                        $scope.payAdhoc();
                    }else if($scope.action=="UNBLOCK"){
                        $scope.blockPaymentRequests(false);
                    }else if($scope.action=="BLOCK"){
                        $scope.blockPaymentRequests(true);
                    }
                }
            };*/

            /*$scope.payAdhoc = function () {
                if ($scope.viewPr.currentStatus != "APPROVED") {
                    $toastService.create("Payment request cannot be paid adhoc.");
                } else {
                    $rootScope.showFullScreenLoader = true;
                    $http({
                        url: apiJson.urls.paymentRequestManagement.payAdhoc,
                        method: 'POST',
                        data: {
                            paymentRequestId:$scope.viewPr.paymentRequestId,
                            currentStatus: $scope.viewPr.currentStatus,
                            newStatus:"SENT_FOR_PAYMENT",
                            reason: $scope.actionReason,
                            updatedBy:appUtil.createGeneratedBy().id
                        }
                    }).success(function (data) {
                        if (data!=null && data == true) {
                            $scope.findPrs();
                            $toastService.create("Payment request status updated.");
                        } else {
                            $toastService.create("Could not update request. Please try again later");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }).error(function (err) {
                        $alertService.alert(err.errorTitle, err.errorMsg, null, true);
                        $rootScope.showFullScreenLoader = false;
                    });
                }
            };*/

            $scope.downloadPRInvoice = function(prInvoice){
                metaDataService.downloadDocument(prInvoice);
            };

            $scope.previewPRInvoice = function(prInvoice){
                if(!appUtil.isEmptyObject(prInvoice.documentLink)){
                    $http({
                        method:"POST",
                        url:apiJson.urls.vendorManagement.downloadDocument,
                        data: prInvoice,
                        responseType: 'arraybuffer'
                    }).then(function(response){
                        var arrayBufferView = new Uint8Array( response.data );
                        var blob = new Blob( [ arrayBufferView ], { type: appUtil.mimeTypes[prInvoice.mimeType] } );
                        var urlCreator = window.URL || window.webkitURL;
                        var imageUrl = urlCreator.createObjectURL( blob );
                        var preview = document.getElementById("invoicePreview");
                        preview.innerHTML = "";
                        var img = new Image();
                        img.src = imageUrl;
                        preview.appendChild(img);
                    },function(error){
                        $toastService.create("Could not download the document... Please try again");
                    });
                }else{
                    $toastService.create("Not a valid document... Please check");
                }
            };


            /////////////////////document upload methods/////////////////////////////////

            $scope.resetScanModal = function () {
                $scope.imagesScanned = [];
                document.getElementById('images').innerHTML = "";
                var canvas = document.createElement('canvas');
                canvas.id = "scaleCanvas";
                document.getElementById('images').appendChild(canvas);
                $scope.uploadedDocData = null;
            };

            $scope.resetSnapModal = function () {
                $scope.snapRunning = false;
                if($scope.localstream!=null){
                    $scope.localstream.getTracks()[0].stop();
                }
                $scope.uploadedDocData = null;
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                context.clearRect(0, 0, 640, 480);
            };

            $scope.startSnap = function () {
                var video = document.getElementById('video');
                // Get access to the camera!
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    // Not adding `{ audio: true }` since we only want video now
                    navigator.mediaDevices.getUserMedia({video: true}).then(function (stream) {
                        video.src = window.URL.createObjectURL(stream);
                        $scope.localstream = stream;
                        video.play();
                    });
                }
                $scope.snapRunning = true;
            };

            $scope.snapPicture = function () {
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                var video = document.getElementById('video');
                context.drawImage(video, 0, 0, 640, 480);
                video.pause();
                video.src = "";
                $scope.localstream.getTracks()[0].stop();
                $scope.snapRunning = false;
            };

            function dataURItoBlob(dataURI) {
                var byteString = atob(dataURI.split(',')[1]);
                var ab = new ArrayBuffer(byteString.length);
                var ia = new Uint8Array(ab);
                for (var i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                }
                return new Blob([ab], {type: 'image/png'});
            }

            $scope.uploadFile = function () {
                var canvas = document.getElementById('canvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            $scope.uploadDoc = function () {
                $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    if(file.size > 307200){
                        $toastService.create('File size should not be greater than 300 kb.');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('docType',"PAYMENT_REQUEST_INVOICE");
                        fd.append('mimeType',fileExt.toUpperCase());
                        fd.append('userId',appUtil.getCurrentUser().userId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.uploadedDocData = response;
                            }else{
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                    /*metaDataService.uploadFile("OTHERS", "PAYMENT_REQUEST_INVOICE", file, function (doc) {
                        $scope.uploadedDocData = doc;
                    });*/
                });
            };

            $scope.scanToPng = function () {
                scanner.scan($scope.displayImagesOnPage,
                    {
                        "output_settings": [
                            {
                                "type": "return-base64",
                                "format": "png"
                            }
                        ]
                    }
                );
            };

            $scope.displayImagesOnPage = function (successful, mesg, response) {
                if (!successful) { // On error
                    console.error('Failed: ' + mesg);
                    return;
                }
                if (successful && mesg != null && mesg.toLowerCase().indexOf('user cancel') >= 0) { // User cancelled.
                    console.info('User cancelled');
                    return;
                }
                var scannedImages = scanner.getScannedImages(response, true, false); // returns an array of ScannedImage
                $scope.imagesScanned = [];
                $scope.processScannedImage(scannedImages[0]);
                /*for(var i = 0; (scannedImages instanceof Array) && i < scannedImages.length; i++) {
                    var scannedImage = scannedImages[i];
                    $scope.processScannedImage(scannedImage);
                }*/
            };

            $scope.processScannedImage = function (scannedImage) {
                $scope.imagesScanned.push(scannedImage);
                scaleImage(scannedImage.src);
            };

            function scaleImage(src) {
                var MAX_WIDTH = 1000;
                var image = new Image();
                var canvas = document.getElementById("scaleCanvas");
                image.onload = function () {
                    //var canvas = document.getElementById("scaleCanvas");
                    if (image.width > MAX_WIDTH) {
                        image.height *= MAX_WIDTH / image.width;
                        image.width = MAX_WIDTH;
                    }
                    var ctx = canvas.getContext("2d");
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    canvas.width = image.width;
                    canvas.height = image.height;
                    ctx.drawImage(image, 0, 0, image.width, image.height);
                };
                image.src = src;
            }

            $scope.uploadScannedFile = function () {
                var canvas = document.getElementById('scaleCanvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', appUtil.getCurrentUser().userId);
                $http({
                    url: apiJson.urls.goodsReceivedManagement.uploadGR,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };
            $scope.clubSummaryAmountModal= function (viewData) {
                $scope.getClubbedDataModal(viewData);

            }

            $scope.getClubbedDataModal = function (viewData) {
                var modalInstance = Popeye.openModal({
                    templateUrl: 'clubSummaryAmountModal.html',
                    controller: 'clubSummaryAmountModalCtrl',
                    size: 'lg',
                    windowClass: 'my-modal-popup',
                    modalClass:'my-modal-popup',
                    resolve: {
                        viewData: function () {
                            return viewData;
                        }
                    },
                    click: true,
                    keyboard: false
                });
            };

        }]
    ).controller('clubSummaryAmountModalCtrl', ['$scope', 'appUtil', 'Popeye', 'viewData',
        function ($scope, appUtil, Popeye, viewData) {


            $scope.init = function (){
                $scope.summaryInvoicesList = [];
                $scope.summaryInvoicesList.type=viewData.requestItemMappings[0].paymentRequestType;
                $scope.list =viewData.paymentInvoice.paymentInvoiceItems;
                console.log(viewData.requestItemMappings);
                $scope.summaryList();
            }

            $scope.summaryList = function () {
                var map = new Map();
                for(var x = 0; x < $scope.list.length; x++){
                    if(x == 0 || !map.has($scope.list[x].subCategory)){
                        $scope.list[x].packagingPrice = $scope.list[x].totalAmount;
                        $scope.list[x].tdsRate = $scope.list[x].totalTax;
                        $scope.list[x].totalPrice = $scope.list[x].packagingPrice - $scope.list[x].tdsRate;
                        map.set($scope.list[x].subCategory,$scope.list[x]);
                    }
                    else if(map.has($scope.list[x].subCategory)){
                        var listData = map.get($scope.list[x].subCategory);
                        $scope.list[x].packagingPrice = listData.packagingPrice + $scope.list[x].totalAmount;
                        $scope.list[x].tdsRate = listData.tdsRate + $scope.list[x].totalTax;
                        $scope.list[x].totalPrice = $scope.list[x].packagingPrice - $scope.list[x].tdsRate;
                        map.delete($scope.list[x].subCategory);
                        map.set($scope.list[x].subCategory,$scope.list[x]);
                    }
                    // else{
                    // 	$scope.list[x].packagingPrice = $scope.list[x].totalAmount;
                    // 	$scope.list[x].tdsRate = $scope.list[x].totalTax;
                    // $scope.list[x].totalPrice = $scope.list[x].packagingPrice - $scope.list[x].tdsRate;
                    // 	map.set($scope.list.subCategory,$scope.list[x]);
                    // }

                }

                angular.forEach(map, function (value, key) {
                    $scope.summaryInvoicesList.push(value);
                });
                console.log($scope.summaryInvoicesList);
            }

            $scope.closeModal = function closeModal() {
                Popeye.closeCurrentModal();
            }

        }
    ]
);
