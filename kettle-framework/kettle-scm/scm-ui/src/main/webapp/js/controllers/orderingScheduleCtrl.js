'use strict';

angular.module('scmApp')
    .controller('orderingScheduleCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'recipeService', 'metaDataService', 'previewModalService', '$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $state, recipeService, metaDataService, previewModalService, $alertService) {
                $scope.init = function (){
                   $scope.currentUnit = appUtil.getUnitData();
                   $scope.brandDetails=[{id: 1 ,brandName: "Chaayos" },
                        {id: 3 ,brandName: "Ghee and Turmeric" }];
                   $scope.days = ["MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY","SUNDAY"];
                   $scope.unitOrderingSchedule = [];
                   $scope.fountain9UnitsList = [];
                   $scope.editMode = false;
                   $scope.unitSelected = null;
                    $scope.notFountain9Unit = false;
                   $scope.isKitchenOrWH = appUtil.isWarehouseOrKitchen($scope.currentUnit);
                    $scope.getFountain9Units();
                    $scope.stockInDays = {
                        1:[],
                        3:[]
                    };
                };

                $scope.getFountain9Units = function () {
                    if ($scope.isKitchenOrWH) {
                        $("#addSchedule").hide();
                        $("#viewOrderingSchedules").hide();
                    }
                    $http({
                        method: "GET",
                        url: apiJson.urls.referenceOrderManagement.getFountain9Units,
                        params : {
                            "unitId" : appUtil.getCurrentUser().unitId,
                            "isForceLookUp" : !$scope.isKitchenOrWH
                        }
                    }).then(function success(response) {
                        if (response.data != null && response.data.length > 0) {
                            $scope.fountain9UnitsList = getUnistData(response.data);
                            if (!$scope.isKitchenOrWH) {
                                if (response.data.indexOf($scope.currentUnit.id) != -1) {
                                    $scope.getUnitOrderingSchedule();
                                } else {
                                    $scope.notFountain9Unit = true;
                                    $("#viewOrderingSchedules").hide();
                                }
                            }
                        } else {
                            $toastService.create("No Units Found..!");
                            $scope.fountain9UnitsList = [];
                            // $("#viewOrderingSchedules").hide();
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                function getUnistData(response) {
                    var allUnits = appUtil.getUnitList();
                    var result = [];
                    angular.forEach(allUnits,function (unit){
                       if (response.indexOf(unit.id) != -1) {
                           result.push(unit);
                       }
                    });
                    return result;
                }

                $scope.setSelectedUnit = function (unit) {
                    $scope.unitSelected = unit;
                    $scope.editMode = false;
                    $scope.getUnitOrderingSchedule();
                };

                $scope.getUnitOrderingSchedule = function () {
                    console.log("unit sel is ",$scope.unitSelected);
                    $http({
                        method: "GET",
                        url: apiJson.urls.referenceOrderManagement.getUnitOrderingSchedule,
                        params: {
                            "unitId":$scope.isKitchenOrWH ? $scope.unitSelected .id : $scope.currentUnit.id
                        }
                    }).then(function success(response) {
                        if (response.data != null && response.data.length > 0) {
                            $scope.unitOrderingSchedule = response.data;
                            $scope.displayOrderingSchedules = makeDisplayData(response.data);
                            $("#viewOrderingSchedules").show();
                        } else {
                            $toastService.create("No Ordering Schedule Found ..Please add an Ordering Schedule..!");
                            $scope.unitOrderingSchedule = [];
                            $scope.displayOrderingSchedules = [];
                            $("#viewOrderingSchedules").hide();
                            if ($scope.isKitchenOrWH) {
                                $("#addSchedule").show();
                            }
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                function makeDisplayData(data) {
                    angular.forEach(data,function (obj) {
                       var brandName;
                       angular.forEach($scope.brandDetails,function (brand) {
                            if (obj.brandId == brand.id) {
                                brandName = brand.brandName;
                            }
                       });
                       obj.brandName = brandName;
                       angular.forEach(obj.unitOrderSchedules,function (schedule) {
                            if (schedule.orderingDays != null) {
                                schedule.orderingDays = schedule.orderingDays.toString();
                            }
                       });
                        setFunctionalSchedule(obj);
                    });
                    return data;
                }

                $scope.editSchedule = function () {
                    $scope.editMode = true;
                };

                $scope.closeEditSchedule = function () {
                      $scope.init();
                };

                $scope.addOrderingSchedule = function () {
                    $scope.editMode = true;
                    $("#addSchedule").hide();
                    $("#viewOrderingSchedules").show();
                    $scope.orderingSchedules = createOrderingSchedules();
                    $scope.displayOrderingSchedules = $scope.orderingSchedules;
                    $scope.stockInDays = {
                        1:[],
                        3:[]
                    };
                    console.log("ordering is : ",$scope.orderingSchedules);
                };

                function createOrderingSchedules() {
                    var schedules = [];
                    for (var i=0;i<$scope.brandDetails.length;i++) {
                        var obj = {};
                        obj.unitId = $scope.isKitchenOrWH ? $scope.unitSelected.id :
                            $scope.currentUnit.id;
                        obj.brandId = $scope.brandDetails[i].id;
                        obj.brandName = $scope.brandDetails[i].brandName;
                        obj.functional = false;
                        obj.manual = "Manual";
                        obj.unitOrderSchedules = createUnitOrderSchedules();
                        schedules.push(obj);
                    }
                    return schedules;
                }

                function createUnitOrderSchedules() {
                    var result = [];
                    for (var j=0;j<$scope.days.length;j++) {
                        var innerObj = {};
                        innerObj.orderingDayType = $scope.days[j];
                        innerObj.functional = false;
                        innerObj.orderingDay = false;
                        innerObj.orderingDays = null;
                        innerObj.inStock = false;
                        result.push(innerObj);
                    }
                    return result;
                }

                $scope.closeAddSchedule = function () {
                    $scope.stockInDays = {
                        1:[],
                        3:[]
                    };
                    $("#viewOrderingSchedules").hide();
                    $("#addSchedule").show();
                };

                $scope.submitSchedule = function () {
                    var check = validateAllRules($scope.displayOrderingSchedules);
                    if (check) {
                        var submitOrUpdate = $scope.unitOrderingSchedule.length == 0 ? "Submit" : "Update";
                        var message = "Do you want to " + submitOrUpdate + " the Ordering Schedules";
                        var isUpdate = $scope.unitOrderingSchedule.length == 0 ? false : true;
                        $alertService.confirm("Are you sure?", message, function (result) {
                            if (result) {
                                $http({
                                    method: "POST",
                                    url: apiJson.urls.referenceOrderManagement.addUnitOrderingSchedule,
                                    data: $scope.displayOrderingSchedules,
                                    params: {
                                        "userId": appUtil.getCurrentUser().userId,
                                        "isUpdate": isUpdate
                                    }
                                }).then(function (response) {
                                    if (response != null && response.status == 200 && response.data == true) {
                                        if (isUpdate) {
                                            $toastService.create("Order Schedule Updated Successfully");
                                        } else {
                                            $toastService.create("Order Schedule Submitted Successfully");
                                        }
                                        $scope.init();
                                    }
                                }, function (response) {
                                    if (response.data.errorMsg != null) {
                                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                                    } else {
                                        $toastService.create("Error occurred while Submitting Order Schedule..!");
                                    }
                                    $scope.init();
                                });
                            }
                        });
                    }
                };

                function validateAllRules(schedules) {
                    var totalFunctional = 0;
                    var totalOrderingDays = 0;
                    var check = false;
                    for (var j in schedules) {
                        var schedule = schedules[j].unitOrderSchedules;
                        if (schedules[j].functional) {
                            for (var i = 0; i < schedule.length; i++) {
                                if (schedule[i].functional) {
                                    totalFunctional++;
                                    if (schedule[i].orderingDay) {
                                        if (schedule[i].orderingDays == null || schedule[i].orderingDays == "") {
                                            $toastService.create("Please Check the Ordering Day & Days for Brand : " + schedules[j].brandName + " Of Day : " + schedule[i].orderingDayType);
                                            return false;
                                        }
                                        totalOrderingDays = totalOrderingDays + parseInt(schedule[i].orderingDays);
                                    }
                                    if (!schedule[i].inStock) {
                                        check = true;
                                    }
                                }
                                if (check) {
                                    break;
                                }
                            }
                        }
                        if (check) {
                            break;
                        }
                    }
                    if (check || totalFunctional != totalOrderingDays) {
                        $toastService.create("Check the In STOCK correctly ..!");
                        return false;
                    }
                    return true;
                }

                $scope.setFunctionalFlag = function (item,schedule){
                    if (!schedule.functional) {
                        var idx = $scope.days.indexOf(schedule.orderingDayType);
                        var finIdx = (idx + 7 - 3 ) % 7;
                        var finSchedule = item.unitOrderSchedules[finIdx];
                        if (finSchedule.orderingDay) {
                            var msg = "Marking <b>" + schedule.orderingDayType +"</b> as non functional , marking <b>"+finSchedule.orderingDayType+" as non ordering day ..!";
                            finSchedule.orderingDay = false;
                            var prev = finSchedule.orderingDays;
                            finSchedule.orderingDays = "";
                            console.log("item bef ",item);
                            $scope.setStockDays(item,finSchedule,prev);
                            console.log("item after ",item);
                            $toastService.create(msg);
                        }
                    }
                };

                $scope.checkForFulfilmentDate = function (item,schedule) {
                    if (schedule.orderingDay) {
                        var index = $scope.days.indexOf(schedule.orderingDayType);
                        var fulfilmentDayIdx = (index + 3) % 7;
                        var day = $scope.days[fulfilmentDayIdx];
                        var check = false;
                        for (var i = 0; i < item.unitOrderSchedules.length; i++) {
                            if (day == item.unitOrderSchedules[i].orderingDayType && item.unitOrderSchedules[i].functional) {
                                check = true;
                            }
                        }
                        if (!check) {
                            schedule.orderingDay = false;
                            $toastService.create("Fulfilment date " + day + " can not be a non functional date..!");
                            return false;
                        }
                    }
                    else {
                        var prev = schedule.orderingDays;
                        schedule.orderingDays = "";
                        $scope.setStockDays(item,schedule,prev);
                    }
                }
                
                $scope.setStockDays = function (item, schedule,oldDays) {
                    console.log("item is : ",item);
                    if (oldDays != null) {
                        setFunctionalSchedule(item);
                    }
                    else {
                        getFunctionalSchedule(item,schedule);
                        setFunctionalSchedule(item);
                    }
                }

                function getFunctionalSchedule(item,schedule) {
                    var indexOfOrderingDay = ($scope.days.indexOf(schedule.orderingDayType) + 3 ) % 7;
                    var indexes = getIndexes(indexOfOrderingDay,item.brandId == 1 ? 3 : 2);
                    var scheduledDays = [];
                    var check = false;
                    for (var j in indexes) {
                        var current = item.unitOrderSchedules[indexes[j]];
                        console.log("current is : ",current);
                        if (current.inStock == undefined) {
                            current.inStock = false;
                        }
                        if (current.functional && scheduledDays.length < schedule.orderingDays) {
                            if ($scope.stockInDays[item.brandId].indexOf(current.orderingDayType) == -1) {
                                scheduledDays.push(current);
                            } else {
                                check = true;
                            }
                        }
                    }
                    if (check) {
                        $toastService.create("Please check the IN STOCK flag ..it is repeating..!");
                        schedule.orderingDays = null;
                        return false;
                    }

                    if (scheduledDays.length < schedule.orderingDays) {
                        $toastService.create("Cafe is not functional for "+schedule.orderingDays+" Please mark it functional or reduce the ordering days..!");
                        schedule.orderingDays = null;
                        return false;
                    }
                    else {
                        console.log("days is : ",scheduledDays);
                        angular.forEach(scheduledDays,function (day) {
                            $scope.stockInDays[item.brandId].push(day.orderingDayType);
                            day.inStock = true;
                        });
                    }
                    return true;
                }

                function getIndexes(index,days) {
                    var result = [];
                    for (var i=0;i<days;i++) {
                        var idx = (index + i) % 7;
                        result.push(idx);
                    }
                    return result;
                }

                function setFunctionalSchedule(item) {
                    $scope.stockInDays = {
                        1:[],
                        3:[]
                    };
                    for (var i=0;i<item.unitOrderSchedules.length;i++) {
                        if ($scope.stockInDays[item.brandId].indexOf(item.unitOrderSchedules[i].orderingDayType) == -1) {
                            item.unitOrderSchedules[i].inStock = false;
                        }
                        if (item.unitOrderSchedules[i].orderingDays != null) {
                            var log = getFunctionalSchedule(item,item.unitOrderSchedules[i]);
                            console.log("item is schedule ",item.unitOrderSchedules[i],log);
                        }
                    }
                }
        }]);