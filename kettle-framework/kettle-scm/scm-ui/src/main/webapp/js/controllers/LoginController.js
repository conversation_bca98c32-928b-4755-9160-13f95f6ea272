/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('loginCtrl', ['$rootScope', 'PrintService','authService','$state','$scope','$stateParams','$location','apiJson','$cookieStore','appUtil','$http','metaDataService',
        function ($rootScope,PrintService,authService,$state,$scope,$stateParams,$location, apiJson, $cookieStore, appUtil, $http, metaDataService) {

            $scope.init = function(){
                $scope.showMessage = $stateParams.accessDenied;
                $scope.tagline = "Login";
                $scope.permissions = null;
                $scope.userId = null;
                $scope.passcode=null;
                $scope.unitList = [];
                $scope.getUnitList();
                $scope.fetchUnit($scope.userId);
                $scope.unitSelectOptions = {
                    width: '100%',
                    initSelection: function (element, callback) {
                        callback($(element).data('$ngModelController').$modelValue);
                    }
                };
                $("#userId").focus();
                $scope.changeLoginType("sumo");
                if(true){
                    PrintService.loadQZPrinter();

                }
                if (($cookieStore.get('isQZLoaded') === 'undefined'
                    || !$cookieStore.get('isQZLoaded'))) {
                        $cookieStore.put('isQZLoaded', true);

                }
            };

            $scope.selectUnit = function (unit) {
              $scope.selectedUnit = unit;
            };

            $scope.changeLoginType = function(type){
                $rootScope.loginType = type;
            };

            $scope.fetchUnit = function(userId){
                if(String(userId).length===6){
                    $http({
                        method: 'POST',
                        url: apiJson.urls.unitManagement.userUnits,
                        data : {
                            "employeeId":Number(userId),
                            "onlyActive":true
                        }
                    }).then(function success(response) {
                        if (response.data != null) {
                            $scope.unitList = response.data;
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.getUnitList = function(){
                $http({
                    method: 'GET',
                    url: apiJson.urls.unitMetadata.allUnitsList
                }).then(function success(response) {
                    if (response.data != null) {
                        appUtil.setUnitList(response.data);
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            function getUserObj() {
                var userObj = {
                    userId: $scope.userId,
                    password: $scope.passcode,
                    unitId: 0,
                    terminalId:0,
                    macAddress: $location.search().mac,
                    application: "SERVICE_ORDER"
                };

                if($rootScope.loginType=="sumo"){
                    userObj.unitId = JSON.parse($scope.selectedUnit).id;
                    userObj.application="SCM_SERVICE";
                }
                return userObj;
            }

            // $(document).on("focus", ".select2", function (e) {
            //     if (e.originalEvent) {
            //         var s2element = $(this).siblings("select:enabled");
            //         s2element.select2("open");
            //         // Set focus back to select2 element on closing.
            //         s2element.on("select2:closing", function () {
            //             if (s2element.val()) s2element.select2("focus");
            //         });
            //     }
            // });


            $scope.login = function(){
                $http({
                    method: 'POST',
                    url: apiJson.urls.users.login,
                    data: getUserObj()
                }).then(function success(response) {
                    if (response.data == null || response.data.sessionKeyId == null) {
                        $scope.message = 'Credentials are not correct!';
                        $scope.showMessage = true;
                    } else {
                        $scope.inputLocalStorage(response);
                    }
                }, function error(response) {
                    $scope.message = 'Authentication failed! Please make sure you have access to SUMO';
                    $scope.showMessage = true;
                    console.log("error:" + response);
                });
            };

            $rootScope.switchUnitLogin = function (unitLoginData) {
                $rootScope.resetLocalStorage();
                $http({
                    method: 'POST',
                    url: apiJson.urls.users.switchUnit,
                    data: unitLoginData
                }).then(function success(response) {
                    if (response.data == null || response.data.sessionKeyId == null) {
                        $scope.message = 'Credentials are not correct!';
                        $scope.showMessage = true;
                    } else {
                        $scope.inputLocalStorage(response);
                    }
                }, function error(response) {
                    $scope.message = 'Authentication failed! Please make sure you have access to SUMO';
                    $scope.showMessage = true;
                    console.log("error:" + response);
                });
            }

            $scope.inputLocalStorage = function (response) {
                $scope.message = 'Authentication successful. Redirecting...';
                $scope.showMessage = true;
                authService.setAuthorization(response.data.jwtToken);
                $rootScope.aclData = response.data.acl;
                $rootScope.mappedUnits = response.data.user.units;
                appUtil.setPermissions(response.data.permissions);
                response.data.permissions=null;
                appUtil.setCurrentUser(response.data);
                metaDataService.getBusinessDate();
                if($rootScope.loginType=="sumo"){
                    metaDataService.getUnitData(function(unit){
                        if(unit!=null){
                            appUtil.setUnitData(unit);
                            if(appUtil.isWarehouseOrKitchen(unit)){
                                $http({
                                    method: 'GET',
                                    url: apiJson.urls.warehouseClosing.checkDayCloseThreshold,
                                    params: {
                                        id:unit.id
                                    }
                                }).then(function success(response) {
                                    if(!response.data){
                                        $state.go("metadata");
                                    }else{
                                        $state.go("dayCloseShutdown");
                                    }
                                });
                            }else {
                                $state.go("metadata");
                            }
                        }else {
                            $scope.message = "Failed to fetch Metadata! Please try again";
                            $scope.showMessage = true;
                        }
                    });
                }else{
                    $state.go('menu');
                }
            }

            $rootScope.resetLocalStorage = function(){
                localStorage.removeItem("scmUnitList");
                localStorage.removeItem("scmPermissions");
                localStorage.removeItem("scmCurrentUser");
                localStorage.removeItem("scmMetadata");
                localStorage.removeItem("unitData");
                localStorage.removeItem("scmProductDetails");
                localStorage.removeItem("menuProductCategories");
                localStorage.removeItem("packagingMap");
                localStorage.removeItem("skuProductMap");
                $cookieStore.remove("scmToken");
            }
        }
    ]
).controller('metadataCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson', 'appUtil', 'metaDataService',
        function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, metaDataService) {

            $scope.initMetaData = function (){
                //getting all the metadata after login
                metaDataService.getSCMMetaData(function(metaData){
                    appUtil.metadata = metaData;

                    metaDataService.getScmProductDetails().then(function(){
                        metaDataService.getSkuProductMap().then(function(){
                            metaDataService.getAllPackagingMappings(function(packagingMappings){
                                appUtil.metadata.packagingMappings =  packagingMappings;
                                $state.go('menu');
                            },true);
                        });
                    });

                    metaDataService.getPackagingMap();
                    metaDataService.getMenuProductCategories();
                    metaDataService.getTaxProfiles();
                    metaDataService.getInventoryLists();
                    metaDataService.getCompanyList();
                    metaDataService.getBrandList();

                    metaDataService.getPurchaseRoles(appUtil.getCurrentUser().userId,function(roles){
                        $rootScope.purchaseRoles = roles;
                    });
                    metaDataService.getUOMDefinitions(function(uoms){
                        appUtil.metadata.uomMetadata = uoms;
                    });
                    metaDataService.getProfileDefinitions(function(profiles) {
                        appUtil.metadata.profileDefinitions = profiles;
                    })
                    metaDataService.getCategories(function(categories){
                        appUtil.metadata.categoryDefinitions =  categories;
                    });
                    metaDataService.getAttrDefinitions(function(attributes){
                        appUtil.metadata.attributeDefinitions = attributes;
                    });
                    metaDataService.getAttributeValues(function(attributes){
                        appUtil.metadata.attributeValues =  attributes;
                    });

                }, true);
            };
        }
    ]
).controller('shutDownCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson', 'appUtil', '$http',
        function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, $http) {

            appUtil.getUnitList().forEach(function (unit) {
                if(unit.id==appUtil.getCurrentUser().unitId){
                    $scope.unitName = unit.name;
                }
            });

            $scope.initiate = function(){
                var unitId = appUtil.getUnitData().id;
                var userId = appUtil.getCurrentUser().userId;

                $http({
                    method:"POST",
                    url:apiJson.urls.warehouseClosing.dayClose+"/"+unitId+"/"+userId
                }).then(function(response){
                    if(!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)){
                        $state.go("menu.dayClose");
                    }
                },function(error){
                    console.log("Got error while initiating day close");
                });
            };
        }
    ]
);