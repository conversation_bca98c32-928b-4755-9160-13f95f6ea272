/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limit`ed
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('scmApp')
    .controller('unitDistanceMappingCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye','$timeout','$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService,
                  $fileUploadService, $alertService, previewModalService, Popeye, $timeout, $window) {

            $scope.init = function () {
            	$scope.showAllUnits();
            	$scope.firstMappingId;
            	$scope.secondMappingId;
            };
            
            $scope.showAllUnits = function () {
                    $http({
                        method: "GET",
                        dataType: 'json',
                        data: '',
                        headers: {
                            "Content-Type": "application/json"
                        },
                        url: apiJson.urls.skuMapping.getAllUnit,
                    }).then(
                        function success(response) {
                            $scope.UnitListDetails = appUtil.filterUnitList(response.data);/*.filter(function (value) {
                                return value.code == 'WAREHOUSE' || value.code == 'KITCHEN'
                                    || value.code == 'OFFICE';
                            });*/
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                } 
            
            $scope.searchMappingShow = function(){
                $http({
                	 url: apiJson.urls.skuMapping.getUnitDistance,
                    method: "GET",
                    params : {
            			firstUnitId : $scope.firstUnitMapping.id,
            			secondUnitId : $scope.secondUnitMapping.id
            	       }
                }).then(
                    function success(response) {
                    	if (response.data != null && response.data.length > 0) {
                    		$scope.showDistance = true;
                                var i = 0;
                                for (i = 0; i < response.data.length; i++) {
                                	var fields = response.data[i].split(/-/);
                                	if(i == 0){
                                	$scope.unitOne = fields[0];
                                	$scope.firstMappingId = fields[1];
                                	}else{
                                		$scope.unitSecond = fields[0];
                                    	$scope.secondMappingId = fields[1];
                                	}
                                }
                    	}
                    }, function error(response) {
                        console.log("error:" + response);
                    });
            }
            
            $scope.submitDistanceData = function(){
                $http({
               	 url: apiJson.urls.skuMapping.updateUnitDistanceMapping,
                   method: "POST",
                   params : {
           			firstUnitId : $scope.firstUnitMapping.id,
           			firstMappingId : $scope.firstMappingId,
           			firstDistance: $scope.unitOne,
           			secondUnitId : $scope.secondUnitMapping.id,
           			secondMappingId : $scope.secondMappingId,
           			secondDistance: $scope.unitSecond
           	       }
               }).then(
                   function success(response) {
                	   if(response.data){
                		   $scope.showDistance = false;
            				$toastService.create("Unit Distance is updated successfully");
            				
            			}else{
            				$toastService.create("Something went wrong.");
            			}
                   }, function error(response) {
                       console.log("error:" + response);
                   });
            }
    }]);