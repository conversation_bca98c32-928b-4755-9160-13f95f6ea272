/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 06-06-2016.
 */

'use strict';

angular.module('scmApp').controller('stockTakeCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', '$stateParams',
    'apiJson', 'appUtil', '$http', '$toastService', 'productService', 'metaDataService', 'packagingService', '$timeout', '$alertService',
    function ($rootScope, $scope, authService, $location, $state, $stateParams, apiJson, appUtil,
              $http, $toastService, productService, metaDataService, packagingService, $timeout, $alertService) {

        $scope.init = function () {
            $scope.stockFrequency = [{id: 1, name: 'WEEKLY'}, {id: 1, name: "MONTHLY"}];
            $scope.allUnitsList = [];
            $scope.getAllUnitList();
            $scope.selectedFrequnecy = null;
            $scope.selectedUnit = null;
            $scope.addNewStockFlag = false;
            $scope.newSelceted
            $scope.minDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
            $scope.maxDate = appUtil.formatDate(appUtil.getDate(31), "yyyy-MM-dd");
        };

        $scope.getAllUnitList = function () {
            metaDataService.getUnitList(function (response) {
                $scope.allUnitsList = response.filter(function (value) {
                    return value.code == 'CAFE';
                });
            });
        };

        $scope.setSelectedFrequency = function (selectedFrequency) {
            $scope.selectedFrequency = selectedFrequency;
        };

        $scope.getCalendarEvents = function () {
            console.log($scope.selectedUnit);
            if ($scope.selectedUnit == null) {
                $alertService.alert("Please select unit.");
                return;
            }
            if ($scope.selectedFrequency == null) {
                $alertService.alert("Please select frequency.");
                return;
            }
            $http.post(apiJson.urls.stockManagement.plannedStockEvent
                + "?stockType=" + $scope.selectedFrequency.name, $scope.selectedUnit.id).success(function (response) {
                $scope.plannedEvents = response;
                $scope.plannedEvents.map(function (item) {
                    item.newScheduledDate = new Date(item.scheduledDate);
                });
            }).error(function (response) {
                console.log(response);
            });
        };

        $scope.setEventDate = function (newDate, item) {
            item.newScheduledDate = newDate;
        };

        $scope.updateEvents = function () {
            var req = [];
            $scope.plannedEvents.map(function (item) {
                if (appUtil.formatDate(item.newScheduledDate, 'yyyy-MM-dd') != appUtil.formatDate(item.scheduledDate, 'yyyy-MM-dd')) {
                    item.scheduledDate = appUtil.formatDate(item.newScheduledDate, 'yyyy-MM-dd');
                    item.newScheduledDate = null;
                    req.push(item);
                }
            });
            if (req.length > 0) {
                $http.post(apiJson.urls.stockManagement.updateStockEventCalendar, req).success(function (response) {
                    console.log(response);
                    if (response == true) {
                        $alertService.alert("Success", "Events updated successfully.", function (result) {
                            $scope.getCalendarEvents();
                        }, false);
                    } else {
                        $alertService.alert("Error", "Error in updating events.");
                    }
                }).error(function (response) {
                    console.log(response);
                    if (response != null && response.errorMsg != null) {
                        $alertService.alert("Error", response.errorMsg);
                    } else {
                        $alertService.alert("Error", "Error in updating events.");
                    }
                });
            } else {
                $alertService.alert("No events edited.", "Please select new date before updating.")
            }
        }

        $scope.newStock = function () {
            $scope.addNewStockFlag = true;

        }
        $scope.cancelStockMenu = function () {
            $scope.addNewStockFlag = false;
            $scope.clearAddStock();
        }

        $scope.submitStock = function (stock, unit, date) {
            if (stock== null) {
                $alertService.alert("Please select stock.");
                return;
            }
            if (unit == null) {
                $alertService.alert("Please select unit.");
                return;
            }
            if (date == null) {
                $alertService.alert("Please select date.");
                return;
            }
            $http({
                method: 'POST',
                url: apiJson.urls.stockManagement.addStockEvent + "?stockType=" + stock.name + "&businessDate=" + date,
                data: unit.id
            }).then(function success(response) {
                $toastService.create("added successfully");
                $scope.addNewStockFlag = false;
                $scope.clearAddStock();
            }, function error(response) {
                if (response.data.errorCode == 702) {
                    $toastService.create(response.data.errorMsg);
                }else{
                    $toastService.create("Error in creating event");
                }
                console.log(response);

            });
        }

        $scope.clearAddStock=function(){
            $scope.selectedNewUnit=null;
            $scope.selectedStockTake=null;
            $scope.scheduledDate=null;
        }
    }]);