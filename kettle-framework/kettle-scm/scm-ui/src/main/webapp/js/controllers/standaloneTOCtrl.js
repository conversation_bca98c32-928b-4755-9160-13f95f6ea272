/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('standaloneTOCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService','metaDataService','previewModalService','Popeye','$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,metaDataService,previewModalService, Popeye,$alertService) {

    		var manualBookProductId=100217;
    		initializeBillBookParams();

            $scope.init = function () {
                //$scope.scmUnitList = appUtil.filterCurrentUnit(appUtil.getUnitList());
            	$scope.getAvailableUnits();
            	$scope.avilableCategories=['CAFE','WAREHOUSE','KITCHEN'];
                // $scope.scmProductDetails = appUtil.getActiveScmProducts();
                $scope.currentUnit = appUtil.getUnitData();
                $scope.skuProductMap = appUtil.getSkuProductMap();
                $scope.packagingMap = appUtil.getPackagingMap();
                $scope.isWarehouse = appUtil.isWarehouse();
                $scope.TOProducts = [];
                $scope.comment = null;
                $scope.selectedUnitList=null;
                $scope.isManualBook={};
                $scope.transferOrderDetail = {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    generationUnitId : {id:appUtil.getCurrentUser().unitId,code:"",name:appUtil.findUnitDetail(appUtil.getCurrentUser().unitId).name},
                    generatedForUnitId: null,
                    generatedBy: appUtil.createGeneratedBy(),
                    lastUpdatedBy: appUtil.createGeneratedBy(),
                    status: "CREATED",
                    comment: $scope.comment,
                    requestOrderId: null,
                    transferOrderItems: []
                };
                $scope.showPreview = previewModalService.showPreview;
                var unit = appUtil.getUnitData();
                $scope.isKitchen = appUtil.isKitchen(unit);
                $scope.ignoreInterCafeTransferFlag = false;
                $scope.isCafeToCafe = false;
                $scope.GNT = "GNT";
                $scope.selectedProduct = null;
            };


             function isCafeToCafe(category){
                var unit = appUtil.getUnitData();
                var isCafe = !appUtil.isWarehouseOrKitchen();
                if(isCafe && category === "CAFE"){
                    $scope.isCafeToCafe = true;
                }else{
                    $scope.isCafeToCafe = false;
                }
            }

            $scope.categoryFilter = function(product){
                if(product.categoryDefinition.id != 3 ){
                    return product;
                }

            }

            $scope.changeCategory = function(category) {
                isCafeToCafe(category);
                $scope.selectedUnitList=$scope.scmUnitList.filter(function (unit) {
                    return unit.category===category ;
                });
                $scope.receivingUnit = null;
                $scope.TOProducts = [];
                $scope.selectedProduct = null;
            }

            $scope.getAvailableUnits = function() {
				$http({
					method : "GET",
					url : apiJson.urls.filter.availableUnits,
					params : {
						unitId : appUtil.getCurrentUser().unitId
					}
				}).then(function success(response) {
					if (response.data != null && response.data.length > 0) {
						$scope.scmUnitList = [];
						appUtil.getUnitList().map(function(unit) {
							for (var i = 0; i < response.data.length; i++) {
								if (unit.id == response.data[i]) {
									$scope.scmUnitList.push(unit);
								}
							}
						});
					} else {
						$toastService.create("Something went wrong. Please try again!");
					}
				}, function error(response) {
					console.log("error:" + response);
				});
			};

			$scope.getAvailableProducts = function(selectedUnit) {
				$http({
					method : "GET",
					url : apiJson.urls.filter.availableProducts,
					params : {
                        fulfillmentUnit: appUtil.getCurrentUser().unitId,
                        requestingUnit: selectedUnit.id
					}
				}).then(function success(response) {
					if (response.data != null && response.data.length > 0) {
						$scope.availableProductList = response.data;
					} else {
						$toastService.create("Something went wrong. Please try again!");
					}
				}, function error(response) {
					console.log("error:" + response);
				});
			};

            $scope.setReceivingUnit = function (selectedUnit) {
            	selectedUnit = JSON.parse(selectedUnit);
            	$scope.receivingUnit = selectedUnit;
                $scope.TOProducts = [];
                $scope.selectedProduct = null;
                $scope.scmProductDetails = appUtil.getActiveScmProducts();
                $scope.transferOrderDetail.generatedForUnitId = {
                    id:selectedUnit.id,
                    code:"",
                    name:selectedUnit.name
                };
                $scope.getAvailableProducts(selectedUnit);
            };

			$scope.byProducts = function(product) {
                if($scope.receivingUnit.category == "CAFE" && $scope.currentUnit.family == "CAFE") {
                    if($scope.ignoreInterCafeTransferFlag){
                       return  product != null && product != undefined  && $scope.availableProductList != undefined && $scope.availableProductList != null
                        && $scope.availableProductList.indexOf(product.productId) > -1 &&  product.productName.toLowerCase().includes($scope.GNT.toLowerCase()) ;
                    }else{
                        return product != null && product != undefined && product.interCafeTransfer == true && $scope.availableProductList != undefined && $scope.availableProductList != null
                            && $scope.availableProductList.indexOf(product.productId) > -1;
                    }

                }else{
                    return product != null && product != undefined && $scope.availableProductList != undefined && $scope.availableProductList != null
                        && $scope.availableProductList.indexOf(product.productId) > -1;
                }			};

            $scope.addNewTOItem = function(){
                var selectedProduct = JSON.parse($scope.selectedProduct);
                var added = false;
                $scope.TOProducts.forEach(function (item) {
                    if(item.productId==selectedProduct.productId){
                        $toastService.create("Product already added!");
                        added = true;
                        return false;
                    }
                });
                if(!added){
                    var item = {
                        productId:selectedProduct.productId,
                        productName:selectedProduct.productName,
                        unitOfMeasure: selectedProduct.unitOfMeasure,
                        transferredQuantity:null,
                        skuList: setSkuToProduct(selectedProduct.productId),
                        selectedSku: null,
                        trPackaging: []
                    };
                    item.selectedSku = initializeSelectedSku(item.skuList);
                    $scope.TOProducts.push(item);
                }
            };

            $scope.addPackaging = function (item) {
                $scope.addPackagingData(item);
                //check for semifinished here
                //if ($scope.isKitchen) {
                item.semifinished = $scope.checkSemiFinshedProducts(item);
                if (item.semifinished) {
                    $toastService.create("Update expiry for semi finished products");
                    var semiFinishedDataArray = [];
                    semiFinishedDataArray.push(item);
                    $scope.openShortExpiryModal(semiFinishedDataArray);
                }
                //}
            };

            $scope.addPackagingData = function(item){
                var found = false;
                item.trPackaging.forEach(function(trp){
                    if(trp.skuId==item.selectedSku.skuId){
                        var pfound = false;
                        trp.packagingDetails.forEach(function(pkgd){
                            if(pkgd.packagingDefinitionData.packagingId==item.selectedPackaging.packagingDefinition.packagingId){
                                alert("Packaging already added!");
                                pfound = true;
                                return false;
                            }
                        });
                        if(!pfound){
                            trp.packagingDetails.push({
                                id: null,
                                packagingDefinitionData: item.selectedPackaging.packagingDefinition,
                                numberOfUnitsPacked: null,
                                numberOfUnitsReceived: null,
                                transferredQuantity: null,
                                receivedQuantity:null
                            });
                        }
                        found = true;
                    }
                });

                if(!found){
                	if(item.productId==100217){
                		$scope.isManualBook[item.productId]=true;
                	}
                    item.trPackaging = [];
                    item.trPackaging.push({
                        id: null,
                        skuId: item.selectedSku.skuId,
                        skuName: item.selectedSku.skuName,
                        packagingDetails: [
                            {
                                id: null,
                                packagingDefinitionData: item.selectedPackaging.packagingDefinition,
                                numberOfUnitsPacked: null,
                                numberOfUnitsReceived: null,
                                transferredQuantity: null,
                                receivedQuantity:null
                            }
                        ],
                        requestedQuantity: null,
                        requestedAbsoluteQuantity: null,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: item.selectedSku.unitOfMeasure,
                        unitPrice: item.selectedSku.unitPrice,
                        negotiatedUnitPrice: item.selectedSku.negotiatedUnitPrice,
                        requestOrderItemId: null,
                        purchaseOrderItemId: null,
                        goodReceivedItemId: null
                    });
                }
                //console.log(item);
            };

            $scope.removePackaging = function(trItem, index, item){
            	if(item.productId==100217){
            		initializeBillBookParams();
            	}
                trItem.packagingDetails.splice(index,1);
                if(trItem.packagingDetails.length==0){
                    item.trPackaging.forEach(function (pkg, i) {
                        if(pkg.skuId==trItem.skuId){
                            item.trPackaging.splice(i, 1);
                        }
                    });
                }
                $scope.updateTrItemQty(trItem, item);
            };

            $scope.updatePackagingQty = function(pgd,trItem, roItem){
                if(roItem.productId==100217){
                    pgd.numberOfUnitsPacked=1;
                }
                if(pgd.packagingDefinitionData.packagingType!="LOOSE"){
                    pgd.numberOfUnitsPacked = parseInt(pgd.numberOfUnitsPacked);
                }
                pgd.transferredQuantity = pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
                $scope.updateTrItemQty(trItem, roItem);
            };

            $scope.updateTrItemQty = function(trItem, roItem){
                var qty = null;
                trItem.packagingDetails.forEach(function (item) {
                    qty += item.transferredQuantity;
                });
                trItem.transferredQuantity = qty;
                $scope.updateRoItemQty(roItem);
            };

            $scope.updateRoItemQty = function(roItem){
                var qty = null;
                roItem.trPackaging.forEach(function (item) {
                    qty += item.transferredQuantity;
                });
                roItem.transferredQuantity = qty;
            };


            $scope.createTransferOrderObject = function(){
            	if($scope.isBillBooksDetailFilled){
            		$toastService.create("Fill Manual bill book details to create Transfer order!");
            		return false;
            	}
                if($scope.transferOrderDetail.generatedForUnitId==null){
                    $toastService.create("Please select receiving unit!");
                    return false;
                }else if($scope.TOProducts.length==0){
                    $toastService.create("Please select items to be transferred!");
                    return false;
                }else{
                    filterTOItems();
                    if($scope.transferOrderDetail.transferOrderItems.length > 0
                            && $scope.transferOrderDetail.transferOrderItems.length < 250){
                        $scope.transferOrderDetail.toType = 'REGULAR_TRANSFER';
                        $http({
                            method: "POST",
                            url: apiJson.urls.transferOrderManagement.transferOrder,
                            data: $scope.transferOrderDetail
                        }).then(function success(response) {
                            if (response.data != null) {
                                if (response.data != null && response.data > 0) {
                                    $toastService.create("Transfer order with id " + response.data + " created successfully!");
                                    if($scope.manualBillBookDetails!=undefined){
                                    	$scope.manualBillBookDetails.transferOrderId=response.data;
                                    	$scope.manualBillBookDetails.generatedForUnitId=$scope.transferOrderDetail.generatedForUnitId;
                                    	var url=apiJson.urls.manualBillBookManagement. createManualBillBookEntry;
                                    	metaDataService.createManualBillBookEntry(url,$scope.manualBillBookDetails).then(function(data){
                                    		if(data){
                                    			$toastService.create("Manual bill book with start no: " + $scope.manualBillBookDetails.startNo + " and end no : "
                                    					+$scope.manualBillBookDetails.endNo +" created successfully!");
                                    			initializeBillBookParams();
                                    		}else{
                                    			$toastService.create("Manual bill book with given start no and end no already exist!");
                                    		}
                                    	});
                                    }
                                    $scope.init();
                                    $scope.changeCategory($scope.selectedCategory);
                                } else {
                                    $toastService.create("Something went wrong. Please try again!");
                                }
                            }
                        }, function error(response) {
                            var data = response.data;
                            $alertService.alert(data.errorTitle, data.errorMsg);
                            console.log("error:" + response);
                        });
                    }else{
                        if($scope.transferOrderDetail.transferOrderItems.length == 0){
                            $toastService.create("Item quantity should not be 0!");
                        }

                        if($scope.transferOrderDetail.transferOrderItems.length >= 250){
                            $toastService.create("Items cannot be more than 249!");
                        }
                    }

                }
            };

            function setSkuToProduct(productId){
                var skuList = $scope.skuProductMap[productId];
                skuList.forEach(function(sku){
                    sku.skuPackagings.forEach(function(packaging){
                        packaging.packagingDefinition = $scope.packagingMap[packaging.packagingId];
                    });
                    sku.skuPackagings = filterLoosePackaging(sku.skuPackagings, sku.supportsLooseOrdering);
                });
                skuList = removeInactive(skuList);
                return skuList;
            }

            function initializeSelectedSku(skuList){
                var ret = null;
                if(skuList.length==1){
                    ret = skuList[0];
                }else{
                    skuList.forEach(function (item) {
                        if(item.isDefault){
                            ret = item;
                        }
                    });
                }
                return ret;
            }

            function filterLoosePackaging(pkgList, looseOrdering){
                var ret = pkgList;
                if(!looseOrdering){
                    var pkgs = [];
                    pkgList.forEach(function (pkg) {
                        if(pkg.packagingDefinition.packagingType!="LOOSE"){
                            pkgs.push(pkg);
                        }
                    });
                    ret = pkgs;
                }
                return ret;
            }

            function removeInactive(skuList){
                var skus = [];
                skuList.forEach(function(sku){
                    if(sku.skuStatus=='ACTIVE'){
                        var pkgs = [];
                        sku.skuPackagings.forEach(function(packaging){
                            if(packaging.mappingStatus=='ACTIVE' && packaging.packagingDefinition.packagingStatus=='ACTIVE'){
                                pkgs.push(packaging);
                            }
                        });
                        sku.skuPackagings = pkgs;
                        skus.push(sku);
                    }
                });
                return skus;
            }

            function filterTOItems(){
                var trItems = [];
                $scope.TOProducts.forEach(function(toi){
                    toi.trPackaging.forEach(function (item) {
                        if(item.transferredQuantity!=null && item.transferredQuantity>0){
                            trItems.push(item);
                        }
                    })
                });
                $scope.transferOrderDetail.transferOrderItems = trItems;
            }

            $scope.updateBillBookDetailsObject=function(billBookDetails){
            	$scope.manualBillBookDetails=billBookDetails;
            	$scope.isBillBooksDetailFilled=false;
            };

            $scope.toggleBillBookDetailsView=function(){
            	$scope.addBillBookDetails[manualBookProductId]=!$scope.addBillBookDetails[manualBookProductId];
            };

            $scope.fillManualBBDetails=function(){
            	$scope.toggleBillBookDetailsView();
            };

            $scope.manualDetailsRequired=function(){
            	if($scope.isBillBooksDetailFilled===undefined){
            		$scope.isBillBooksDetailFilled=true;
            	}
            };

            function initializeBillBookParams(){
            	$scope.manualBillBookDetails=undefined;
    			$scope.isBillBooksDetailFilled=undefined;
    			$scope.addBillBookDetails={};
                $scope.addBillBookDetails[manualBookProductId]=false;
            }

            $scope.checkSemiFinshedProducts = function(product){
            	var products = appUtil.getActiveScmProducts();
                var p = null;
                for(var i in products){
                    if(products[i].productId == product.productId){
                        p = products[i];
                        break;
                    }
                }
                if(p != null && p.categoryDefinition.code == 'SEMI_FINISHED'){
                    return true;
                }
                return false;
            };

             $scope.openShortExpiryModal = function(item){
                if(!appUtil.isEmptyObject(item)){
                    var mappingModal = Popeye.openModal({
                        templateUrl: "views/trOrderShortExpiry.html",
                        controller: "trOrderShortExpiryCtrl",
                        modalClass:"semifinishedItemsModal",
                        resolve:{
                        	data:function(){
                                return item;
                            }
                        },
                        click:false,
                        keyboard:false
                    });

                    mappingModal.closed.then(function(data){
                    	if(data== null || data.dataStatus == 'CANCEL'){
                            // do nothing here
                    	} else {
                            // chutiyapa happens here
                            $scope.updateItemWithShortExpiry(data.semiFinishedProducts, data.expiryData);
                    	}
                    });
                }else{
                    $toastService.create("Please add again!");
                }
            };

            $scope.updateItemWithShortExpiry = function(semiFinishedProducts, expiryData){

            	if(semiFinishedProducts == null || semiFinishedProducts.length == 0 ){
            		return;
            	}
            	for(var i in semiFinishedProducts){
            		var item = semiFinishedProducts[i];
            		for(var j in $scope.TOProducts){
            			var roItem = $scope.TOProducts[j];

            			if(item.productId == roItem.productId){
	            			//add packaging
            				var rOrderItem = $scope.TOProducts[j];
	            			var trItem = $scope.TOProducts[j].trPackaging[0];
	            			var pkg = trItem.packagingDetails[0];
	            			var value = 0;
	            			if(item.shortExpiry == null || item.shortExpiry == undefined){
	                    		value = item.freshQuantity;
	                    	} else {
	                    		value = item.freshQuantity + item.shortExpiry
	                    	}
	            			pkg.numberOfUnitsPacked = value;
	            			for(var e in expiryData.inventoryItems){
	            				var exp = expiryData.inventoryItems[e];
	            				if(trItem.skuId == exp.keyId){
	            					if(trItem.drillDowns == null){
	            						trItem.drillDowns =[];
	            					}
	            					trItem.drillDowns = trItem.drillDowns.concat(exp.drillDowns);
	            				}
	            			}
	            			$scope.updatePackagingQty(pkg,trItem,roItem);
            			}
            		}
            	}

            };

            $scope.hasDrillDown = function (trItem, semifinished){
            	return semifinished || (trItem.drillDowns != null && trItem.drillDowns != undefined && trItem.drillDowns.length > 0);
            };
        }]);
