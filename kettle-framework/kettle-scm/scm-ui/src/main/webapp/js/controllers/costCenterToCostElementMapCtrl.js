angular.module('scmApp').controller(
    'costCenterToCostElementMapCtrl', 
    ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
                  previewModalService, Popeye, $timeout, $window) {
    	
            $scope.init = function () {
            	$scope.mappingTable = [];
            	$scope.costCentreList = [];
            	$scope.getCostCentreList();
            	$scope.hideModalGrid = true;
            	$scope.costElements = [];
            	$scope.getCostElement();
            }
            
            $scope.getCostCentreList = function(){
        		$http({
        			url : apiJson.urls.serviceMappingManagement.getCostCentreList,
        			method : 'GET'
        		}).then(function success(response) {
        			if(response.data != null){
        				$scope.costCentreList = response.data;
        			}
        		}, function error(response) {
                       console.log("error:" + response);
                  })
        	}
            
            
            $scope.getCostElement = function(){
				$http({
					method : 'GET',
					url : apiJson.urls.serviceMappingManagement.getCostElementData,
				}).then(function success(response) {
					if(response.data != null){
						$scope.costElements = response.data;
					}
				}, function error(response) {
		               console.log("error:" + response);
		          })
            }
            
            $scope.searchMappings = function () {
                if ($scope.selectedCostCenter == undefined || $scope.selectedCostCenter == null
                    || $scope.selectedCostCenter.id == 0) {
                    return;
                }
                $scope.getVendorCostElementMappingsGrid();
            }
            
            $scope.getVendorCostElementMappingsGrid = function () {
                $scope.gridOptions = $scope.vendorGridOptions();
                $scope.getAllVendorCostMappings();
            }
            
            $scope.getAllVendorCostMappings = function(){
				$http({
					method : 'GET',
					url : apiJson.urls.serviceMappingManagement.getCostCenterToCostElementMapping,
					params : {
						costCenterId : $scope.selectedCostCenter.id
					}
				}).then(function success(response) {
					if(response.data != null){
						$scope.gridOptions.data = response.data;
					}
				}, function error(response) {
		               console.log("error:" + response);
		          })
            }
            
            $scope.getVendorList = function(){
        		$http({
        			url : apiJson.urls.serviceMappingManagement.getVendorList,
        			method : 'GET'
        		}).then(function success(response) {
        			if(response.data != null){
        				$scope.vendors = response.data;
        			}
        		}, function error(response) {
                       console.log("error:" + response);
                  })
        	}
            
            $scope.openVendorToCostElementMapModal = function () {
            	 $scope.newMappingList = [];
                if ($scope.selectedCostCenter != undefined && $scope.selectedCostCenter.id > 0) {
                	 $scope.getModalSkuMappGrid();
                }
            }
            
            $scope.getModalSkuMappGrid = function () {
                $scope.modalGridOptions = $scope.vendorGridOptions();
                $scope.modalGridOptions.data = [];
            }
            
            $scope.vendorGridOptions = function () {
                return {
                    enableFiltering: true,
                    enableColumnResizing: true,
                    enableColumnMenus: false,
                    columnDefs: [{
                        field: 'id',
                        displayName: 'Cost Element Id',
                        //cellTemplate: 'skuIdTemplate.html'
                    }, {
                        field: 'name',
                        displayName: 'Cost Element Name'
                    }, {
                        field: 'status',
                        displayName: 'Cost Element Status'
                    }, {
                        field: 'mappingStatus',
                        displayName: 'Mapping Status'
                    }, {
                        field: 'code',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeRemoveButton.html'
                    }]
                };
            }
            
            $scope.removeRow = function (value) {
                var index = $scope.modalGridOptions.data.indexOf(value);
                $scope.modalGridOptions.data.splice(index, 1);
            }
            
            $scope.cancelModal = function () {
                $scope.hideModalGrid = true;
                $timeout(function () {
                    $('#modalValueDataId').val('').trigger('change');
                });
            }
            
            $scope.addToModalGridData = function () {
                $scope.hideModalGrid = false;
                data = $scope.selectedCostElement;
                if (data == undefined || data == null) {
                    return;
                }
                data.mappingStatus = "NA";
                var index = $scope.newMappingList.indexOf(data);
                if (index > -1) {
                    $toastService.create('Duplicate Mapping');
                } else {
                    $scope.newMappingList.push(data);
                    $scope.modalGridOptions.data = $scope.newMappingList;
                }
            }
            
            $scope.submitModalGridData = function () {
                var list = [];
                var x;
                for (x in $scope.newMappingList) {
                    list.push($scope.newMappingList[x].id);
                }
                var currentUser = appUtil.getCurrentUser();
                payload = {
                    id: $scope.selectedCostCenter.id,
                    mappingIds: list,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }
                $http({
                    url: apiJson.urls.serviceMappingManagement.addCostCentreToCostElementMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response) {
                        $toastService.create('Mappings Added Successfully');
                        $scope.cancelModal();
                        $scope.searchMappings();
                    }
                }, function (response) {
                    console.log("error", response);
                });
            }
            
            $scope.changeStatus = function (value) {
                var costCenterId = null;
                var costElementId = null;
                var status = value.mappingStatus == 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
                costElementId = value.id;
                costCenterId = $scope.selectedCostCenter.id;
                var currentUser = appUtil.getCurrentUser();
                var payload = {
                    costCenterId: costCenterId,
                    costElementId: costElementId,
                    status: status,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }

                $http({
                    url: apiJson.urls.serviceMappingManagement.updateCostelementCostCenterMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data == true) {
                        $scope.updateGridRowStatus(value, status);
                        var msg = 'Mapping '
                        msg = msg + (status == 'ACTIVE' ? 'Activated' : 'Deactivated')
                        msg = msg + ' Successfully'
                        $toastService.create(msg);
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };
            
            $scope.updateGridRowStatus = function (value, status) {
                var x = null;
                var id = null;
                for (x in $scope.gridOptions.data) {
                    if ($scope.gridOptions.data[x].id == value.id) {
                        id = x;
                    }
                }
                if (id != null) {
                    $scope.gridOptions.data[id].mappingStatus = status;
                }
            }
            
           
        
    }
]
);
