angular.module('scmApp').controller(
    'costElementToVendorMapCtrl', 
    ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
                  previewModalService, Popeye, $timeout, $window) {
    	
            $scope.init = function () {
            	$scope.getCostElement();
            	$scope.mappingTable = [];
            	$scope.hideModalGrid = true;
            	$scope.vendors = [];
            	metaDataService.getServiceVendors(function(serviceVendors){
                    $scope.vendors = serviceVendors;
                });
            }
            
            
            $scope.getCostElement = function(){
				$http({
					method : 'GET',
					url : apiJson.urls.serviceMappingManagement.getCostElementData,
				}).then(function success(response) {
					if(response.data != null){
						$scope.costElements = response.data;
					}
				}, function error(response) {
		               console.log("error:" + response);
		          })
            }
            
            $scope.searchMappings = function () {
                if ($scope.selectedCostElement == undefined || $scope.selectedCostElement == null
                    || $scope.selectedCostElement.id == 0) {
                    return;
                }
                $scope.getCostElementVendorMappingsGrid();
            }
            
            $scope.getCostElementVendorMappingsGrid = function () {
                $scope.gridOptions = $scope.vendorGridOptions();
                $scope.getAllCostVendorMappings();
            }
            
            $scope.getAllCostVendorMappings = function(){
				$http({
					method : 'GET',
					url : apiJson.urls.serviceMappingManagement.getCostElementToVendorMapping,
					params : {
						costElementId : $scope.selectedCostElement.id
					}
				}).then(function success(response) {
					if(response.data != null){
						$scope.gridOptions.data = response.data;
					}
				}, function error(response) {
		               console.log("error:" + response);
		          })
            }
            
            $scope.openCostElementToVendorMapModal = function () {
            	 $scope.newMappingList = [];
                if ($scope.selectedCostElement != undefined && $scope.selectedCostElement.id > 0) {
                	 $scope.getModalSkuMappGrid();
                }
            }
            
            $scope.getModalSkuMappGrid = function () {
                $scope.modalGridOptions = $scope.vendorGridOptions();
                $scope.modalGridOptions.data = [];
            }
            
            $scope.vendorGridOptions = function () {
                return {
                    enableFiltering: true,
                    enableColumnResizing: true,
                    enableColumnMenus: false,
                    columnDefs: [{
                        field: 'id',
                        displayName: 'Vendor Id'
                    }, {
                        field: 'name',
                        displayName: 'Vendor Name'
                    }, {
                        field: 'status',
                        displayName: 'Vendor Status'
                    }, {
                        field: 'mappingStatus',
                        displayName: 'Mapping Status'
                    }, {
                        field: 'code',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeRemoveButton.html'
                    }]
                };
            }
            
            $scope.removeRow = function (value) {
                var index = $scope.modalGridOptions.data.indexOf(value);
                $scope.modalGridOptions.data.splice(index, 1);
            }
            
            $scope.cancelModal = function () {
                $scope.hideModalGrid = true;
                $timeout(function () {
                    $('#modalValueDataId').val('').trigger('change');
                });
            }
            
            $scope.addToModalGridData = function () {
                $scope.hideModalGrid = false;
                data = $scope.selectedVendor;
                if (data == undefined || data == null) {
                    return;
                }
                data.mappingStatus = "NA";
                var index = $scope.newMappingList.indexOf(data);
                if (index > -1) {
                    $toastService.create('Duplicate Mapping');
                } else {
                    $scope.newMappingList.push(data);
                    $scope.modalGridOptions.data = $scope.newMappingList;
                }
            }
            
            $scope.submitModalGridData = function () {
                var list = [];
                var x;
                for (x in $scope.newMappingList) {
                    list.push($scope.newMappingList[x].id);
                }
                var currentUser = appUtil.getCurrentUser();
                payload = {
                    id: $scope.selectedCostElement.id,
                    mappingIds: list,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }
                $http({
                    url: apiJson.urls.serviceMappingManagement.addCostElementToVendorMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response) {
                        $toastService.create('Mappings Added Successfully');
                        $scope.cancelModal();
                        $scope.searchMappings();
                    }
                }, function (response) {
                    console.log("error", response);
                });
            }
            
            $scope.changeStatus = function (value) {
                var vendorId = null;
                var skuId = null;
                var status = value.mappingStatus == 'ACTIVE' ? 'IN_ACTIVE' : 'ACTIVE';
                    vendorId = value.id;
                    costElementId = $scope.selectedCostElement.id;
                var currentUser = appUtil.getCurrentUser();
                var payload = {
                    vendorId: vendorId,
                    costElementId: costElementId,
                    status: status,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }

                $http({
                    url: apiJson.urls.serviceMappingManagement.updateCostelementVendorMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data == true) {
                        $scope.updateGridRowStatus(value, status);
                        var msg = 'Mapping '
                        msg = msg + (status == 'ACTIVE' ? 'Activated' : 'Deactivated')
                        msg = msg + ' Successfully'
                        $toastService.create(msg);
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };
            
            $scope.updateGridRowStatus = function (value, status) {
                var x = null;
                var id = null;
                for (x in $scope.gridOptions.data) {
                    if ($scope.gridOptions.data[x].id == value.id) {
                        id = x;
                    }
                }
                if (id != null) {
                    $scope.gridOptions.data[id].mappingStatus = status;
                }
            }
            
           
        
    }
]
);
