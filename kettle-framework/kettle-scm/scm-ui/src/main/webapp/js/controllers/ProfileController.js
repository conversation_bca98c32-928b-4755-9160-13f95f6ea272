/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('profileCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state',
        '$stateParams', 'apiJson', 'appUtil', '$http', '$toastService','Popeye','metaDataService','$alertService','$fileUploadService',
        function ($rootScope, $scope, authService, $location, $state,
                  $stateParams, apiJson, appUtil, $http, $toastService, Popeye, metaDataService,$alertService, $fileUploadService) {

            $scope.profileDefinition = $stateParams.productDef;
            $scope.editMode = false;
            $scope.newMapping = null;
            $scope.addModal = false;

            $scope.selectedProfileId = null;
            $scope.selectedProfileAttributeMappings = null;

            $scope.multiSelectOptions = {
                'width': '100%',
                'multiple': true
            };

            $scope.init = function () {
                $scope.attributeId = null;
                $scope.definedAt = 'SKU';
                $scope.profileBaseUrl = apiJson.profileBaseUrl;
                $scope.addProductFlag = false;
                $scope.selectedProfile = null;
                $scope.profileDefinitions  = appUtil.getMetadata().profileDefinitions;
                console.log($scope.profileDefinition);
                $scope.productSelectOptions = {
                    width: '100%',
                    initSelection: function (element, callback) {
                        callback($(element).data('$ngModelController').$modelValue);
                    }
                };
                if (appUtil.isEmptyObject($stateParams.productDef)) {
                    $scope.editMode = false;
                    createProfileObj();
                    console.log('inside ' + $stateParams.productDef);
                } else {
                    $scope.editMode = true;

                }
                $scope.attributes = [];
                metaDataService.getAttrDefinitions(function (attributes) {
                    var data = attributes;
                    console.log(attributes);
                    for (var key in data) {
                        var subArray = data[key];
                        $scope.attributes = $scope.attributes.concat(subArray);
                    }
                    console.log($scope.attributes);
                });
            };

            $scope.logout = function () {
                $rootScope.logout();
            };

            $scope.createProfile = function () {
                console.log($scope.profileDefinition)
                if($scope.profileDefinition.uniqueNumberAvailable == null){
                    $scope.profileDefinition.uniqueNumberAvailable = false;
                }
                $http({
                    url: apiJson.urls.profileManagement.addProfileDefinition,
                    method: 'POST',
                    data: $scope.profileDefinition,
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    $scope.selectedProfile = response.data;
                    $scope.selectedProfileId = $scope.selectedProfile.profileId;
                    $scope.addProductFlag = false;
                    $scope.profileDefinitions.push($scope.selectedProfile);
                    getProfileAttributeMapping($scope.selectedProfileId);
                }, function error(response) {
                    $toastService.create("Failed to create Profile!! " + response.data.errorMessage);
                    console.log("error:" , response);
                });
            };

            $scope.selectProfile = function(profileId){
                console.log(profileId);
                for(var index in $scope.profileDefinitions) {
                    if($scope.profileDefinitions[index].profileId == profileId){
                        $scope.selectedProfile = $scope.profileDefinitions[index];
                        $scope.selectedProfileId = $scope.profileDefinitions[index].profileId;
                        console.log($scope.selectedProfile);
                        getProfileAttributeMapping($scope.selectedProfileId);
                        $scope.addProductFlag = false;
                        break;
                    }
                }
            };

            $scope.setImageViaUploadDoc = function (profileAttributeMappingId, mapping) {
                $fileUploadService.openFileModal("Upload Product Image","Find",function(file){
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    if(file.size > 204800){
                        $toastService.create('File size should not be greater than 2 Mb');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (appUtil.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('mimeType',fileExt.toUpperCase());
                        fd.append('profileAttributeMappingId',profileAttributeMappingId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.profileManagement.uploadProfileAttributeMappingImg,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response != null) {
                                mapping.associatedImage = response;
                                $toastService.create("Upload successful");
                            }else{
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                });
            };

            function getProfileAttributeMapping(profileId) {
                $http({
                    url: apiJson.urls.profileManagement.profileAttributeMapping,
                    method: 'GET',
                    params : {
                        profileId : profileId
                    },
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    $scope.selectedProfileAttributeMappings = response.data;

                }, function error(response) {
                    console.log("error:" + response);
                    $toastService.create("Failed to get Profile Attribute Mapping!!");
                    $scope.selectedProfileAttributeMappings = [];
                });
            }

            $scope.addProfileClicked = function(){
                createProfileObj();
                $scope.addProductFlag = true;
                $scope.selectedProfile = null;
                $scope.selectedProfileId = null;
            };

            function createProfileObj(){
                $scope.profileDefinition = {};
                $scope.profileDefinition.profileStatus = "ACTIVE";
                $scope.profileDefinition.createdBy = appUtil.createGeneratedBy();
            }

            $scope.changeStatus = function(profileId, status) {
                console.log(profileId, status);
            };

            $scope.createNewMapping = function() {
                $scope.openModal = true;
                console.log('create new mapping row');
                $scope.createNewMappingObj();
                $scope.openAddModal();
            };

            $scope.addMapping = function() {
                console.log("add mapping");
                if($scope.newMapping.attributeId == null || $scope.newMapping.attributeId == ''){
                    $toastService.create('Please Select Attribute Definition First');
                    return;
                }
                var mappingArray = [];
                mappingArray.push($scope.newMapping);

                $http({
                    url: apiJson.urls.profileManagement.addProfileAttributeMapping,
                    method: 'POST',
                    data: mappingArray,
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    if($scope.newMapping.profileAttributeMappingId == null){
                        if($scope.selectedProfileAttributeMappings == null) {
                            $scope.selectedProfileAttributeMappings = [];
                        }
                        $scope.selectedProfileAttributeMappings.push(response.data[0]);
                        $toastService.create("Added Successfully!!");
                        $scope.closeAddModal();
                    } else {
                        for(var index in $scope.selectedProfileAttributeMappings){
                            if($scope.selectedProfileAttributeMappings[index].profileAttributeMappingId == $scope.newMapping.profileAttributeMappingId){
                                $scope.selectedProfileAttributeMappings[index] = response.data[0];
                            }
                        }
                        $toastService.create("Updated Successfully!!");
                        $scope.closeEditModal();
                    }
                    $scope.createNewMappingObj();
                    $scope.openModal = false;
                    $scope.attributeId = null;
                }, function error(response) {
                    $toastService.create("Failed to add attribute mapping!!");
                    console.log("error:" + response);
                });
            };

            $scope.reset = function () {
                $scope.newMapping = {};
            };


             $scope.createNewMappingObj = function() {
                $scope.newMapping = {};
                $scope.newMapping.profileId = $scope.selectedProfileId;
                $scope.newMapping.attributeId = null;
                $scope.newMapping.createdBy = appUtil.createGeneratedBy();
                $scope.newMapping.definedAtProduct = false;
                $scope.newMapping.definedAtSKU = true;
                $scope.definedAt = 'SKU';
                $scope.newMapping.definedAtAsset = false;
                $scope.newMapping.mandatoryAtProduct = false;
                $scope.newMapping.mandatoryAtSKU = false;
                $scope.newMapping.mandatoryAtAsset = false;
                $scope.newMapping.overridableAtProduct = false;
                $scope.newMapping.overridableAtSKU = false;
                $scope.newMapping.participateInName = false;
                $scope.newMapping.updatedBy = appUtil.createGeneratedBy();
                $scope.newMapping.status = "ACTIVE";
                $scope.newMapping.standAlone = false;
            }
            $scope.setAttrubute = function(mapping, attributeId) {
                mapping.attributeId = attributeId;
            };
            $scope.filterByType = function (attribute) {
                for(var index in $scope.selectedProfileAttributeMappings){
                    if($scope.selectedProfileAttributeMappings[index].attributeId == attribute.attributeId){
                        return false;
                    }
                }
                return attribute.attributeType == "ASSET" ;
            };
            $scope.getAttributeName = function(attributeId) {
                for(var index in $scope.attributes) {
                    if($scope.attributes[index].attributeId == attributeId){
                        return $scope.attributes[index].attributeName;
                    }
                }
            };
            $scope.editMapping = function (mapping) {
                $scope.newMapping = {};
                $scope.newMapping.profileAttributeMappingId = mapping.profileAttributeMappingId;
                $scope.newMapping.profileId = mapping.profileId;
                $scope.newMapping.attributeId = mapping.attributeId;
                $scope.newMapping.createdBy = mapping.createdBy;
                $scope.newMapping.definedAtProduct = mapping.definedAtProduct;
                $scope.newMapping.definedAtSKU = mapping.definedAtSKU;
                $scope.newMapping.definedAtAsset = mapping.definedAtAsset;
                if($scope.newMapping.definedAtAsset == true) {
                    $scope.definedAt = 'ASSET';
                } else if ($scope.newMapping.definedAtSKU == true) {
                    $scope.definedAt = 'SKU';
                }
                $scope.newMapping.mandatoryAtProduct = mapping.mandatoryAtProduct;
                $scope.newMapping.mandatoryAtSKU = mapping.mandatoryAtSKU;
                $scope.newMapping.mandatoryAtAsset = mapping.mandatoryAtAsset;
                $scope.newMapping.overridableAtProduct = mapping.overridableAtProduct;
                $scope.newMapping.overridableAtSKU = mapping.overridableAtSKU;
                $scope.newMapping.participateInName = mapping.participateInName;
                $scope.newMapping.updatedBy = appUtil.createGeneratedBy();
                $scope.newMapping.status = mapping.status;
                $scope.newMapping.creationDate = mapping.creationDate;
                $scope.newMapping.standAlone = mapping.standAlone;
                $scope.newMapping.associatedImage = mapping.associatedImage;
                $scope.openEditModal();
            };

            $scope.openEditModal = function () {
                var modal = document.getElementById("_editDefinitionModal");
                modal.style.display = "block";
            };

            $scope.closeEditModal = function () {
                var modal = document.getElementById("_editDefinitionModal");
                modal.style.display = "none";
                window.onclick = function (event) {
                    if (event.target == modal) {
                        modal.style.display = "none";
                    }
                }
            };

            $scope.openAddModal = function () {
                $scope.addModal = true;
                var modal = document.getElementById("_addDefinitionModal");
                if(modal != null){
                    modal.style.display = "block";
                } else {
                    setTimeout(function(){ $scope.openAgain(); }, 1000);
                }

            };

             $scope.openAgain = function(){
                var modal = document.getElementById("_addDefinitionModal");
                modal.style.display = "block";
            }

            $scope.closeAddModal = function () {
                $scope.addModal = false;
                var modal = document.getElementById("_addDefinitionModal");
                modal.style.display = "none";
                window.onclick = function (event) {
                    if (event.target == modal) {
                        modal.style.display = "none";
                    }
                }
            };

            $scope.changeStatus = function(profileId, status){
                var profile = $scope.selectedProfile;
                profile.profileStatus = status;
                $http({
                    url: apiJson.urls.profileManagement.addProfileDefinition,
                    method: 'POST',
                    data: profile,
                    headers: {"Content-Type": "application/json"}
                }).then(function success(response) {
                    $scope.selectedProfile(profileId);
                }, function error(response) {
                    $toastService.create("Failed to update profile status");
                    console.log("error:" + response);
                });
            };
            $scope.definedAtChanged = function(newMapping, definedAt){
                if(definedAt == 'SKU'){
                    newMapping.definedAtAsset = false;
                    newMapping.definedAtSKU = true;
                    newMapping.standAlone = false;
                } else {
                    newMapping.definedAtAsset = true;
                    newMapping.definedAtSKU = false;
                }
                console.log(newMapping);
            }
        }
    ]
);