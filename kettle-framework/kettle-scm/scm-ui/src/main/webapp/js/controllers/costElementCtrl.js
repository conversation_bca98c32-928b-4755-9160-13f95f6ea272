angular.module('scmApp').controller(
		'searchCostElementCtrl',
		[
				'$rootScope',
				'$scope',
				'apiJson',
				'$http',
				'$stateParams',
				'appUtil',
				'$toastService',
				'metaDataService',
				'Popeye',
				'$timeout',
				function($rootScope, $scope, apiJson, $http, $stateParams, appUtil,
						$toastService, metaDataService,Popeye, $timeout) {
					
					$scope.init = function () {
						console.log("running cost element");
						$scope.getCostCenters(false);
						$scope.costCenterCreateVO= {costElement :{}};
						metaDataService.getUOMDefinitions(function (uoms) {
							$scope.uomMetadata = uoms;
						});
					};
					
					$scope.getCostCenters = function (status) {
	                        $http({
	                            url: apiJson.urls.serviceOrderManagement.getCostElementsAll,
	                            method: "GET",
	                        }).then(function (response) {
	                            $scope.allCostCenters = response.data;
	                            $scope.allCostElements = [];
	                            var i= 0
	                            for(i in $scope.allCostCenters){
	                            	var ce = $scope.allCostCenters[i];
	                            		 if(!status){
	                            			 if(ce.status != "ARCHIVE"){
	                            				 $scope.allCostElements.push(ce);
	                            			 }
	                            		 }
	                            		 else{
	                            		 $scope.allCostElements.push(ce);
	                            		 }
	                            	}
	                        }, function error(response) {
	                            console.log(response);
	                        });
	                };
					
	                $scope.openAddNewCostElementModal = function(){
	                	$scope.costCenterCreateVO= {costElement :{}};
	                }
	                
	                $scope.getEmployees = function () {
	                    $http({
	                        method: "GET",
	                        url: apiJson.urls.users.activeUsers
	                    }).then(function success(response) {
	                        $scope.employees = response.data;
	                    }, function error(response) {
	                        console.log("error:" + response);
	                    });
	                };
	                
	                $scope.createCostElement = function() {
	                	 $http({
	                         url: apiJson.urls.serviceOrderManagement.costElementCreate,
	                         method: 'POST',
	                         data: $scope.costCenterCreateVO
	                     }).then(function (response) {
	                         $toastService.create("Cost Element Added!");
	                         $scope.getCostCenters();
	                         $("#addNewCostElement").closeModal();
	                         $timeout(function () {
	                             $('#costCenterSelector').val('').trigger('change');
	                         });
	                     }, function (response) {
	                         console.log("got error", response);
	                     });
	                };
	                
	                $scope.clearCostCenter = function() {
	                	 $scope.costCenter = {};
	                };
	                
	            	$scope.archiveData = function(){
			        	if($scope.archived){
			        		$scope.getCostCenters(true);
			        	}else{
			        		$scope.getCostCenters(false);
			        	}
			           }
	            	
	            	$scope.viewDetailCost = function (costElement){
	                    $http({
	                        method: "GET",
	                        url: apiJson.urls.unitMetadata.getAllTaxCategories
	                    }).then(function success(response) {
	                        var taxes = response.data;
	                        var filteredTaxes = [];
	            		for(var i in taxes){
	            		    if(taxes[i].status == 'ACTIVE'){
	            			    filteredTaxes.push(taxes[i]);
	            		    }
	            		}
	            		$scope.taxCodes = filteredTaxes;
	            		$scope.viewDetailCostModal(costElement)
	                    }, function error(response) {
	                        console.log("error:" + response);
	                    });
	                }
	            	
	            	 $scope.viewDetailCostModal = function (costElement) {
	                     var viewDetailModal = Popeye.openModal({
	                         templateUrl: "viewDetailCostElmnt.html",
	                         controller: "viewDetailCostElmntCtrl",
	                         size: 'lg',
	                         windowClass: 'my-modal-popup',
	                         resolve: {
	                        	 costElement: function () {
	                                 return costElement;
	                             },
	                             taxesCode: function () {
	                                 return $scope.taxCodes;
	                             }
	                         },
	                         click: true,
	                         keyboard: false
	                     });
	                     viewDetailModal.closed.then(function(state) {
	                    	 if(state){
	                    	 $scope.getCostCenters(false);
	                    	 }
			                });
	                 };
	                
}]
).controller('viewDetailCostElmntCtrl', [
	'$scope',
	'apiJson',
	'$http',
	'$stateParams',
	'appUtil',
	'$toastService',
	'metaDataService',
	'Popeye',
	'$timeout',
	'costElement',
	'taxesCode',
	function($scope, apiJson, $http, $stateParams, appUtil,
			$toastService, metaDataService,Popeye, $timeout, costElement, taxesCode) {

	
	$scope.init = function (){
		$scope.periods = ["ACTIVE","IN_ACTIVE", "ARCHIVE"];
		$scope.capexs = ["Yes","No"];
		$scope.taxCodes = taxesCode;
		$scope.list = costElement;
		//$scope.taxCategory = costElement.code;
		setTaxCode(costElement.code);
		$scope.name = costElement.name;
		$scope.code = costElement.code;
		$scope.status = costElement.status;
		$scope.description = costElement.description;
		$scope.selectedIsPrice = costElement.isPriceUpdate;
        $scope.selectedCategory = costElement.category;
        $scope.selectedCapex = costElement.capex;
        $scope.selectedDepartment = costElement.department;
        $scope.selectedDivision = costElement.division;
        $scope.selectedSubCategory = costElement.subCategory;
        $scope.selectedSubSubCategory = costElement.subSubCategory;
		$scope.getCategoryTaxProfiles();
        var map = null;

        $scope.categories = [];
        $scope.departments = [];
        $scope.divisions = [];
        $scope.subCategories = [];
        $scope.subSubCategories = [];
        $scope.getListData();
	}
        $scope.getListData = function (){
            $http({
                method : 'GET',
                url : apiJson.urls.serviceOrderManagement.getListData+"?baseType="+$scope.loginType,
            }).then(function success(response) {
                if(response.data != null){
                    map = response.data;
                    $scope.departments = map["Department"];
                    $scope.divisions = map["Division"];
                    $scope.categories = map["Classification"];
                    if($scope.selectedCategory!=null) {
                        $scope.selectCategory($scope.selectedCategory);
                    }
                }
            }, function error(response) {
                console.log("error:" + response);
            })
        }
        $scope.selectCategory = function(selectedCategory){
            for(var i = 0 ; i < $scope.categories.length ; i++){
                if($scope.categories[i].listDetailId == selectedCategory.listDetailId){
                    $scope.subCategories = $scope.categories[i].listType;
                }
            }
        }

        $scope.selectSubCategory = function(selectedSubCategory){
            for(var i = 0 ; i < $scope.subCategories.length ; i++){
                if($scope.subCategories[i].listTypeId == selectedSubCategory.listTypeId){
                    $scope.subSubCategories = $scope.subCategories[i].listData;
                }
            }
        }
	
	function setTaxCode(code){
        for(var i in $scope.taxCodes){
            if($scope.taxCodes[i].code == code){
                $scope.taxCategory = $scope.taxCodes[i];
                return;
            }
        }
    }
	
	$scope.cancel=function(){
        Popeye.closeCurrentModal();
    };
    
    $scope.getCategoryTaxProfiles = function (){
        $http({
            method: 'GET',
            url: apiJson.urls.unitMetadata.getAllCategoriesTax
        }).then(function success(response) {
        	$scope.taxPercent  = response.data;
        }, function error(response) {
            console.log("error:" + response);
        });
	}
    
    $scope.getTaxPercent = function(){
		for(var x = 0 ; x < $scope.taxPercent.length ; x++){
			if($scope.taxPercent[x].category.code == $scope.taxCategory.code){
				return $scope.taxPercent[x].taxes[0].igst;
			}
		}
	}
	
	$scope.saveStatus = function(){
		if($scope.selectedIsPrice=="" || $scope.selectedIsPrice==null){
 			alert("Please input IsPrice Editable.");
 			return;
 		}
        if($scope.selectedCapex=="" || $scope.selectedCapex==null){
            alert("Please input Capex!");
            return;
        }
        if($scope.selectedDivision==null){
            alert("Please input Division!");
            return;
        }
        if($scope.selectedDepartment==null){
            alert("Please input Department!");
            return;
        }

        if($scope.selectedCategory==null){
            alert("Please input Classification!");
            return;
        }
        if($scope.selectedSubCategory==null){
            alert("Please input Sub Cassification!");
            return;
        }

		var taxPercent = $scope.getTaxPercent();
		var reqObj = {
   			id : $scope.list.id,
   			status : $scope.status,
   			name : $scope.name,
   			description : $scope.description,
   			isPriceUpdate : $scope.selectedIsPrice,
			capex: $scope.selectedCapex,
   			ascCode: $scope.taxCategory.code,
			taxRate: taxPercent,
            category: $scope.selectedCategory,
            department: $scope.selectedDepartment,
            division: $scope.selectedDivision,
            subCategory: $scope.selectedSubCategory,
            subSubCategory: $scope.selectedSubSubCategory,
   	       }
        $http({
           method: "POST",
           url: apiJson.urls.serviceOrderManagement.costElementStatus,
           data: reqObj
       }).then(
           function success(response) {
           	if (response.data == "") {
           	  $toastService.create("Status updated successfully.");
           	 Popeye.closeCurrentModal(true);
            } else {
                $toastService.create(response.data);
           	}
           }, function error(response) {
               console.log("error:" + response);
           });
   }
        $scope.closeModal = function () {
            Popeye.closeCurrentModal(false);
        }
        
    }
]
);
