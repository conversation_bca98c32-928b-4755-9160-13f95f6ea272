'use strict';

angular.module('scmApp')
.controller('createGatepassCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService','metaDataService','previewModalService','$state', '$alertService', 
	function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,metaDataService,previewModalService,$state, $alertService) {

	var availableProductDetails = null;
	$scope.init = function () {
	    $scope.scannedAssetTagValue = "";
		availableProductDetails = appUtil.getActiveScmProducts();
		$scope.scmProductDetails = [];
		$scope.skuProductMap = appUtil.getSkuProductMap(false);
		$scope.packagingMap = appUtil.getPackagingMap();
		$scope.isWarehouse = appUtil.isWarehouse();
		$scope.gatepassProducts = [];
		$scope.showPreview = previewModalService.showPreview;

		$scope.gatepass ={};
		$scope.gatepass.returnable = false;

		$scope.operationList = [
			{name : "QUALITY_CHECK", label : "QUALITY CHECK"},{name : "REPAIR", label : "REPAIR"},
			{name : "NPD", label : "NPD"},{name : "SAMPLE", label : "SAMPLE"},
			{name : "INTERNAL_TXN", label : "INTERNAL TXN"}];
		$scope.daysList = [15, 30, 45];
		$scope.reasonList = [{name : "Reason1", label : "Reason1"},{name : "Reason2", label : "Reason2"}];
		$scope.minIssueDate = appUtil.getDate(-1);
		$scope.maxIssueDate = appUtil.getDate(1);
		$scope.vendorList = [];
//		$scope.getOperationVendorList("QUALITY_CHECK");
		$scope.locationList = [];
        $scope.isForFixedAsset = false;
        getAssetFromUnit(appUtil.getCurrentUser().unitId)
	};

	$scope.updateReturnDays = function(returnable, days){
		if(returnable !== true){
			$scope.gatepass.expectedReturn = null;
		}
	};

	$scope.toggleFixedAssetFlag = function(){

	};

	$scope.onScan = function() {
        console.log($scope.scannedAssetTagValue);
        if($scope.scannedAssetTagValue == null || $scope.scannedAssetTagValue.length != 6){
            return ;
        }
        console.log($scope.scannedAssetTagValue);
        if($scope.gatepassProducts.length == 0) {
            $toastService.create("Please select gate pass product");
            resetScannedAssetTagValue();
            return;
        }
        /*
            Search for asset in available assets
         */
        var assetId = null;
        var associatedSKUId = null;
        for(var index in $scope.availableAssets){
            if($scope.availableAssets[index].tagValue == $scope.scannedAssetTagValue) {
                assetId = $scope.availableAssets[index].assetId;
                associatedSKUId = $scope.availableAssets[index].skuId;
                break;
            }
        }
        if(assetId == null) {
            $toastService.create("Asset tag value is invalid");
            resetScannedAssetTagValue();
            return;
        }
        for(var i in $scope.gatepassProducts) {
            var item = $scope.gatepassProducts[i];
            var foundSku = false;
            for(var j in item.trPackaging) {
                var trItem = item.trPackaging[j];
                if(trItem == null ) {
                    $toastService.create("Please select SKU");
                    resetScannedAssetTagValue();
                    return;
                }
                if(trItem.skuId == associatedSKUId) {
                    foundSku = true;
                    var pgd = trItem.packagingDetails[0];
                    if(pgd == null ){
                        $toastService.create("Please select SKU packaging");
                        resetScannedAssetTagValue();
                        return;
                    }
                    if(pgd.numberOfUnitsPacked == null || pgd.numberOfUnitsPacked == 0) {
                        $toastService.create("Please enter Units Packed: for " + trItem.skuName);
                        resetScannedAssetTagValue();
                        return;
                    }
                    /*
                        Check if already added
                     */
                    for(var assetIndex in trItem.gatepassItemAssetMappings) {
                        var asset = trItem.gatepassItemAssetMappings[assetIndex];
                        if(asset.assetTagValue == $scope.scannedAssetTagValue) {
                            $toastService.create("Asset " + $scope.scannedAssetTagValue + " already added !");
                            resetScannedAssetTagValue();
                            return;
                        }
                    }
                    var isAdded = false;
                    for(var assetIndex in trItem.gatepassItemAssetMappings) {
                        var asset = trItem.gatepassItemAssetMappings[assetIndex];
                        if(asset.assetId == null) {
                            asset.assetTagValue = $scope.scannedAssetTagValue;
                            asset.assetId = assetId;
                            asset.assetValidated = true;
                            isAdded = true;
                            $toastService.create("Asset successfully added ");
                            resetScannedAssetTagValue();
                            return;
                        }
                    }
                    if(isAdded == false) {
                        $toastService.create("Assets are already associated with SKU " + trItem.skuName);
                        resetScannedAssetTagValue();
                        return;
                    }
                }
            }
            if(foundSku == false) {
                $toastService.create("Could not found appropriate sku");
                resetScannedAssetTagValue();
            }
        }
        /*
            After performing operation set it back to empty string
        */
        resetScannedAssetTagValue();
	}

	function resetScannedAssetTagValue() {
        $scope.scannedAssetTagValue = "";
    }

	$scope.keyPressHandler = function(e) {
        if (e.keyCode === 13) {
            e.preventDefault();
            e.stopPropagation();

        }
    }

	function getAssetFromUnit(unitId) {
		$http({
			url: apiJson.urls.assetManagement.getTransferableAssetsFromUnit,
			method: 'GET',
			params : {
				unitId : unitId
			},
			headers: {"Content-Type": "application/json"}
		}).then(function success(response) {
			$scope.availableAssets = response.data;
		}, function error(response) {
			$scope.availableAssets = [];
			console.log("error:" + response);
		});
	}

	$scope.fixedAssetFilter = function(entity){
		if($scope.gatepass.assetGatePass){
			if(entity.categoryDefinition.id == 3){
				return entity;
			}
		} else {
            if(entity.categoryDefinition.id != 3){
                return entity;
            }
		}
	}

	$scope.getOperationVendorList = function(opsType) {
		$http({
			method : "GET",
			url : apiJson.urls.gatepassManagement.vendorList,
			params : {
				opsType : opsType,
				unitId : appUtil.getCurrentUser().unitId,
				status : 'ACTIVE'
			}
		}).then(function success(response) {
			if (response.data != null && response.data.length > 0) {
				$scope.vendorList = response.data;
				resetProductDetails();
			} else {
				$toastService.create("Vendor not available for " + opsType + " operation!");
			}
		}, function error(response) {
			console.log("error:" + response);
		});
	};

	$scope.selectVendor = function(vendor){
		if(vendor != null && vendor != undefined){
			if(vendor.dispatchLocations.length == 0){
				$toastService.create("Dispatch location not present for vendor : " + vendor.entityName + "!");
				return false;
			}
			$scope.gatepass.vendor = appUtil.getIdCodeName(vendor.vendorId);
			$scope.locationList = vendor.dispatchLocations.filter(function (l) {
                return l.status == "ACTIVE";
            });
			resetProductDetails();
			getSkusMapped(vendor);
		}else{
			$scope.selectedVendor = vendor;
			$scope.locationList = null;
			delete $scope.gatepass.vendor;
		}
	};

	function resetProductDetails() {
		$scope.scmProductDetails = [];
	}

	function getSkusMapped(selectedVendor){
		if(!appUtil.isEmptyObject(selectedVendor)){
			metaDataService.getSkusMapped(selectedVendor.vendorId, appUtil.getCurrentUser().unitId,
					function(response){
				if(response.length>0){
					var skuList = response;
					var products  = [];
					for (var i = 0; i < skuList.length; i++) {
						if(products.indexOf(skuList[i].productId) == -1){
							products.push(skuList[i].productId);
						}

					}
					for (var i = 0; i < availableProductDetails.length; i++) {
						if(products.indexOf(availableProductDetails[i].productId) != -1){
							$scope.scmProductDetails.push(availableProductDetails[i]);
						}
					}
				}else{
					$toastService.create("Could not fetch SKU details for this vendor and unit");
				}
			});
		}else{
			$toastService.create("Please select Vendor & Dispatch Location correctly");
		}
	};

	$scope.selectDispatchLocation = function(location){
		if(location != null && location != undefined){
			$scope.selectedDispatchLocation = location;
			$scope.gatepass.dispatchLocation = appUtil.getIdCodeName(location.dispatchId);
		}else{
			$scope.selectedDispatchLocation = location;
			delete $scope.gatepass.dispatchLocation;
		}
	};

	$scope.addNewTOItem = function(){
		var selectedProduct = JSON.parse($scope.selectedProduct);
		var added = false;
		$scope.gatepassProducts.forEach(function (item) {
			if(item.productId==selectedProduct.productId){
				$toastService.create("Product already added!");
				added = true;
				return false;
			}
		});
		if(!added){
			var item = {
					productId:selectedProduct.productId,
					productName:selectedProduct.productName,
					unitOfMeasure: selectedProduct.unitOfMeasure,
					transferredQuantity:null,
					skuList: setSkuToProduct(selectedProduct.productId),
					selectedSku: null,
					trPackaging: []
			}
			item.selectedSku = initializeSelectedSku(item.skuList);
			$scope.gatepassProducts.push(item);
		}
	};

	$scope.addPackaging = function(item){
		var found = false;
		item.trPackaging.forEach(function(trp){
			if(trp.skuId==item.selectedSku.skuId){
				var pfound = false;
				trp.packagingDetails.forEach(function(pkgd){
					if(pkgd.packagingDefinitionData.packagingId==item.selectedPackaging.packagingDefinition.packagingId){
						alert("Packaging already added!");
						pfound = true;
						return false;
					}
				});
				if(!pfound){
					trp.packagingDetails.push({
						id: null,
						packagingDefinitionData: item.selectedPackaging.packagingDefinition,
						numberOfUnitsPacked: null,
						transferredQuantity: null
					});
				}
				found = true;
			}
		});

		if(!found){
			item.trPackaging.push({
				id: null,
				skuId: item.selectedSku.skuId,
				skuName: item.selectedSku.skuName,
				packagingDetails: [{
					id: null,
					packagingDefinitionData: item.selectedPackaging.packagingDefinition,
					numberOfUnitsPacked: null,
					transferredQuantity: null
				}],
				transferredQuantity: null,
				unitOfMeasure: item.selectedSku.unitOfMeasure,
				unitPrice: item.selectedSku.unitPrice,
				negotiatedUnitPrice: item.selectedSku.negotiatedUnitPrice
			});
		}
		//console.log(item);
	};

	$scope.removePackaging = function(trItem, index, item){
		trItem.packagingDetails.splice(index,1);
		if(trItem.packagingDetails.length==0){
			item.trPackaging.forEach(function (pkg, i) {
				if(pkg.skuId==trItem.skuId){
					item.trPackaging.splice(i, 1);
				}
			});
		}
		$scope.updateTrItemQty(trItem, item);
	};

	$scope.updatePackagingQty = function(pgd,trItem, item){
		if(pgd.packagingDefinitionData.packagingType!="LOOSE"){
			pgd.numberOfUnitsPacked = parseInt(pgd.numberOfUnitsPacked);
		}
		pgd.transferredQuantity = pgd.numberOfUnitsPacked * pgd.packagingDefinitionData.conversionRatio;
		$scope.updateTrItemQty(trItem, item);
	};

	$scope.updateTrItemQty = function(trItem, itemLevel){
		var qty = null;
		trItem.packagingDetails.forEach(function (item) {
			qty += item.transferredQuantity;
		});
		trItem.transferredQuantity = qty;
		$scope.updateItemLevelQty(itemLevel);
        addAssetForMapping(trItem, qty)
	};

	function addAssetForMapping (item, qty) {
        if($scope.gatepass.assetGatePass == true) {
            if(item.gatepassItemAssetMappings == undefined){
                item.gatepassItemAssetMappings = [];
                for(var i = 0; i < qty; i++){
                    item.gatepassItemAssetMappings.push(getEmptyGatePassItemAssetMappingObj())
                }
            } else if(item.gatepassItemAssetMappings.length < qty){
                for(var i = item.gatepassItemAssetMappings.length; i < qty; i++){
                    item.gatepassItemAssetMappings.push(getEmptyGatePassItemAssetMappingObj())
                }
            } else {
                for(var i = item.gatepassItemAssetMappings.length - 1; i >= qty; i--){
                    item.gatepassItemAssetMappings.splice(i,1);
                }
            }
        }
	}

	$scope.updateItemLevelQty = function(itemLevel){
		var qty = null;
		itemLevel.trPackaging.forEach(function (item) {
			qty += item.transferredQuantity;
		});
		itemLevel.transferredQuantity = qty;


	};

	function getEmptyGatePassItemAssetMappingObj() {
		var gatepassItemAssetObj = {};
		gatepassItemAssetObj.gatePassItemAssetId = null;
		gatepassItemAssetObj.gatePassId = null;
		gatepassItemAssetObj.assetId = null;
		gatepassItemAssetObj.assetTagValue = null;
		gatepassItemAssetObj.gatePassType = null;
		gatepassItemAssetObj.assetValidated = false;
		return gatepassItemAssetObj;
	}

	$scope.validateAssetTagValue = function(assetTagValue,asset,tritem, index, item) {
        if(assetTagValue == null || assetTagValue.length != 6){
            asset.assetValidated = false;
            asset.assetId = null;
            return
        }
        console.log(tritem);
        var occurenceCounter = 0;
        for(var i in $scope.gatepassProducts) {
            var item2 = $scope.gatepassProducts[i];
            if(item.productId == item2.productId){
                for(var j in item2.trPackaging) {
                    var trItem2 = item2.trPackaging[j];
                    // if(index == j) {
                        for (var assetIndex in trItem2.gatepassItemAssetMappings) {
                            var asset2 = trItem2.gatepassItemAssetMappings[assetIndex];
                            if (asset2.assetTagValue == assetTagValue) {
                            	occurenceCounter++;
                            }
                        }
                    //}
                }
			}
        }

        if(occurenceCounter > 1){
            $toastService.create("Asset " + $scope.scannedAssetTagValue + " already added !");
            asset.assetTagValue = null;
            return;
		}

        for(var index in $scope.availableAssets){
            if($scope.availableAssets[index].tagValue == assetTagValue) {

                if($scope.availableAssets[index].skuId == tritem.skuId){
                    asset.assetId = $scope.availableAssets[index].assetId;
                    asset.assetValidated = true;
                    $toastService.create("Asset Added Successfully");
                    return;
                }
                break;
            }
        }
        asset.assetValidated = false;
        $toastService.create("Asset Tag Value Entered is not associated with selected SKU");
	}

	$scope.createGatepass = function(){
		if($scope.gatepass.vendor == null){
			$toastService.create("Please select vendor!");
			return false;
		}

		if($scope.gatepass.issueDate == null){
            $toastService.create("Please select issue date!");
            return false;
        }

        if($scope.gatepass.operationType == "REPAIR" && !$scope.gatepass.returnable){
            $toastService.create("REPAIR type transactions must be returnable! Please select returnable option");
            return false;
        }

        filterGatepassItems();
        if($scope.gatepass.itemDatas.length>0 && $scope.gatepass.itemDatas.length<250){
            $scope.gatepass.createdBy = appUtil.createGeneratedBy();
            $scope.gatepass.sendingUnit =  {id:appUtil.getCurrentUser().unitId,code:"",name:appUtil.findUnitDetail(appUtil.getCurrentUser().unitId).name};
            $http({
                method: "POST",
                url: apiJson.urls.gatepassManagement.gatepass,
                data: $scope.gatepass
            }).then(function success(response) {
                if (response.data != null) {
                    if (response.data != null && response.data > 0) {
                        $toastService.create("Gate pass with id " + response.data + " created successfully!");
                        $state.go('menu.searchGatepass');
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }
            }, function error(response) {
                $alertService.alert(response.data.errorTitle,response.data.errorMsg,true);
                //console.log("error:" , response);
            });
        }else{
			if($scope.gatepass.itemDatas.length == 0){
				$toastService.create("Item quantity should not be 0!");
			}

			if($scope.gatepass.itemDatas.length >= 250){
				$toastService.create("Items cannot be greater than 249!");
			}
        }


	};

	function setSkuToProduct(productId){
		var skuList = $scope.skuProductMap[productId];
		skuList.forEach(function(sku){
			sku.skuPackagings.forEach(function(packaging){
				packaging.packagingDefinition = $scope.packagingMap[packaging.packagingId];
			});
			sku.skuPackagings = filterLoosePackaging(sku.skuPackagings, sku.supportsLooseOrdering);
		});
		skuList = removeInactive(skuList);
		return skuList;
	}

	function initializeSelectedSku(skuList){
		var ret = null;
		if(skuList.length==1){
			ret = skuList[0];
		}else{
			skuList.forEach(function (item) {
				if(item.isDefault){
					ret = item;
				}
			});
		}
		return ret;
	}

	function filterLoosePackaging(pkgList, looseOrdering){
		var ret = pkgList;
		if(!looseOrdering){
			var pkgs = [];
			pkgList.forEach(function (pkg) {
				if(pkg.packagingDefinition.packagingType!="LOOSE"){
					pkgs.push(pkg);
				}
			});
			ret = pkgs;
		}
		return ret;
	}

	function removeInactive(skuList){
		var skus = [];
		skuList.forEach(function(sku){
			if(sku.skuStatus=='ACTIVE'){
				var pkgs = [];
				sku.skuPackagings.forEach(function(packaging){
					if(packaging.mappingStatus=='ACTIVE' && packaging.packagingDefinition.packagingStatus=='ACTIVE'){
						pkgs.push(packaging);
					}
				});
				sku.skuPackagings = pkgs;
				skus.push(sku);
			}
		});
		return skus;
	};

	function filterGatepassItems(){
		$scope.gatepass.itemDatas = [];
		$scope.gatepassProducts.forEach(function(toi){
			toi.trPackaging.forEach(function (item) {
				if(item.transferredQuantity!=null && item.transferredQuantity>0){
					var gatepassItemObj = getGatepassItemData(item.skuId, item.skuName, item.unitOfMeasure, item.transferredQuantity);
                    if($scope.gatepass.assetGatePass == true) {
						gatepassItemObj.gatepassItemAssetMappings = item.gatepassItemAssetMappings;
						for(var index in item.gatepassItemAssetMappings){
                            gatepassItemObj.price = getAssetCurrentValue(item.gatepassItemAssetMappings[index].assetId);
						}
					}
					console.log(gatepassItemObj);
					$scope.gatepass.itemDatas.push(gatepassItemObj)
				}
			})
		});
	}

	function getAssetCurrentValue( assetId){
        for(var index in $scope.availableAssets){
            if($scope.availableAssets[index].assetId == assetId) {
            	return $scope.availableAssets[index].currentValueWithoutTax;
            }
        }
	}

	function getGatepassItemData(skuId, skuName, uom, qty){
		var itemData = {
				sku : appUtil.getIdCodeName(skuId, skuName),
				uom : uom,
				transType : "TRANSFER",
				quantity : qty
		};
		return itemData;
	}

}]);