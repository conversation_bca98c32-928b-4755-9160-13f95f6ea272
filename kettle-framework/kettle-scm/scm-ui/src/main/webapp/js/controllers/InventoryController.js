/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 22-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('inventoryCtrl', ['$rootScope','$scope','$interval','authService','appUtil','apiJson',
        '$toastService','$http','$alertService','packagingService','pagerService','$state','Popeye','previewModalService', 'metaDataService',
        function ($rootScope,$scope,$interval,authService,appUtil,apiJson,$toastService,$http,$alertService,
                  packagingService,pagerService,$state,Popeye,previewModalService,metaDataService) {


            $scope.frequencies = ["DAILY","WEEKLY","MONTHLY"];
            $scope.checkEmpty = appUtil.checkEmpty;

            $scope.productList = [];
            $scope.submitted = false;
            $scope.isPreview = false;
            $scope.pendingGR = {};
            $scope.pendingWastage = 0;
            $scope.packagingMappings = {};
            $scope.showPreview = previewModalService.showPreview;

            $scope.preview = function(){
                $scope.isPreview = !$scope.isPreview;
            };


            $scope.$on("$destroy", function(){
                saveInventory($scope.productList);
                $interval.cancel($scope.refreshInterval);
            });


            $scope.submit = function(){
                $scope.submitted = true;
                saveInventory($scope.productList);
                var inventoryList = angular.copy($scope.productList);
                inventoryList = inventoryList.filter(function(product){
                    return appUtil.isEmptyObject(product.notAvl) && !appUtil.isEmptyObject(product.stockValue);
                }).map(function(product){
                    delete product.packagingMappings;
                    return product;
                });
                var currentUser = appUtil.getCurrentUser();
                var requestObject = {
                    unit: currentUser.unitId,
                    generatedBy:currentUser.userId,
                    eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                    inventoryResponse: inventoryList
                };

                $http.post(apiJson.urls.stockManagement.updateInventory+"?stockType="+$scope.frequencyValue, requestObject)
                .success(function(response){
                    if(response && (appUtil.isEmptyObject(response.data) || appUtil.isEmptyObject(response.data.errorType))){
                        if(response == 1){
                            $scope.productList = [];
                            $scope.inventoryUpdated = true;
                            appUtil.removeSavedInventoryList();
                            $interval.cancel($scope.refreshInterval);
                            $toastService.create("You have successfully updated inventory");
                            metaDataService.getRegularOrderingEvents(appUtil.getCurrentUser().unitId,function (events) {
                                if (events.length > 0) {
                                    $state.go("menu.refOrderCreateV1", {orderingEvents : events});
                                }
                            });
                        } else if(response == 0){
                            $toastService.create("You are trying to upload wrong inventory list");
                        }else if(response == -1){
                            $toastService.create("Some exception has occurred. Please try again!");
                        }
                    }else if(!appUtil.isEmptyObject(response.data) && !appUtil.isEmptyObject(response.data.errorType)){
                        $toastService.create("You have entered negative stock for some products. Please check and submit again.");
                    }else{
                        $toastService.create("Upload failed. Try again!");
                        $scope.submitted = false;
                    }
                }).error(function(response){
                        if(!appUtil.isEmptyObject(response.payload)){
                            var message =  "Following Products are not available at your Cafe: <br/>";
                            var products = response.payload;
                            var productIds = products.map(function(p){return p.productId;});
                            var names = [];
                            $scope.productList.forEach(function (p) {
                                if(productIds.indexOf(p.productId)!=-1){
                                    names.push(p.productName);
                                    p.stockValue = null;
                                    p["notAvl"] = true;
                                }
                            });
                            message += names.join(", ");
                            message += "<br><b>Removing them from the list. Click Yes to submit again!</b><br>";
                            $alertService.confirm(response.errorType, message, function (result) {
                                if(result){
                                    $scope.submit();
                                }
                            }, false);
                        } else if(response.errorMsg != null) {
                            $alertService.alert("Error submitting stock", response.errorMsg, function (result) {}, false);
                        } else{
                            $toastService.create("Upload Failed! Please try again");
                        }
                        $scope.submitted = false;
                });
            };

            $scope.clearMappings = function(){
                $scope.selectedPackagings = [];
                appUtil.removeSavedInventoryList();
                predictExpectedValues($scope.frequencyValue,true);
                $scope.productList.forEach(function(product){
                    product.stockValue = product.expectedValue;
                    product.variance = 0;
                });
            };

            function checkForPendingGR(unitId) {
            	var promise = $http.get(apiJson.urls.stockManagement.checkPendingGR+"?unit="+unitId)
                    .success(function(response){
                    	console.log("response checkForPendingGR",response);
                        $scope.pendingGR = response;
                        return response.data;
                    }).error(function(response){
                        console.log("Could not get pending GR count");
                    });
            	return promise;
            }

            function checkWastageEntered(unitId) {
                $http.get(apiJson.urls.stockManagement.checkWastageEntered+"?unit="+unitId)
                    .success(function(response){
                        if(!$scope.checkEmpty(response) && response>0){
                            $scope.pendingWastage = response;
                        }
                    }).error(function(response){
                        console.log("Could not get pending GR count");
                    });
            }

            function checkTransfersCreated(unitId) {
                $http.get(apiJson.urls.stockManagement.checkPendingTransfers+"?unitId="+unitId)
                    .success(function(response){
                        if(!$scope.checkEmpty(response) && response>0){
                            $scope.pendingTransfers = response;
                            $toastService.create("Found "+response+" unsettled special order(s). " +
                                "Please settle all special order(s) first.");
                            $state.go('menu.acknowledgeRO');
                        }
                    }).error(function(err){
                    console.log("Could not get pending special order count", err);
                });
            }

            $scope.init = function(){
                $scope.unitId = appUtil.getCurrentUser().unitId;
                $scope.isOpening = false;
                checkTransfersCreated($scope.unitId);
                $scope.checkClosingInitiated($scope.unitId);
                checkInventoryUpdated($scope.unitId);
                checkIfOpeningNeeded($scope.unitId);
                checkForPendingGR($scope.unitId);
                checkWastageEntered($scope.unitId);
                $scope.edited = false;             //edited stock flag initialized to false
                $scope.packagingMappings = appUtil.getMetadata().packagingMappings;
                $scope.inventoryUpdated = false;
                $scope.lastSaved = null;
                var savedInventory = appUtil.getSavedInventoryList();
                if(!$scope.checkEmpty(savedInventory) && (savedInventory.unit == $scope.unitId)){
                    $scope.productList = savedInventory.products;
                    $scope.frequencyValue = savedInventory.stockType;
                    $scope.fixedAssetsOnly = (savedInventory.stockType == "FIXED_ASSETS");
                    $scope.lastSaved = savedInventory.lastSaved;
                    setProducts($scope.productList);
                    $scope.setPage(1);
                }else{
                    appUtil.removeSavedInventoryList();
                    // $scope.selectFrequency("DAILY");
                }
                $scope.refreshInterval = $interval(function(){
                    if(!$scope.checkEmpty($scope.productList)){
                        saveInventory($scope.productList,$scope.selectedPackagings);
                    }
                },10000);
                $scope.inventoryEventValid = true;
                checkAllStockEventsAvailable();
            };

            $scope.clearSavedHistory = function(){
                appUtil.removeSavedInventoryList();
                $scope.productList = null;
                $scope.lastSaved = null;
                // $scope.selectFrequency("DAILY");
            };

            $scope.getProducts = function(){
                if(isEmpty($scope.frequencyValue) || $scope.frequencyValue == undefined){
                    $toastService.create("Please select stock take type event first");
                    return;
                }
                predictExpectedValues($scope.frequencyValue,true);
            };

            $scope.selectFrequencyAtIndex = function(index){
                var savedInventory = appUtil.getSavedInventoryList();
                if($scope.checkEmpty(savedInventory)){
                    $scope.frequencyValue = $scope.frequencies[index];
                } else {
                    $toastService.create("You cannot change the list once saved, Click on Clear Saved history to reset",10000);
                    $scope.frequencyValue = savedInventory.stockType;
                }
                $scope.inventoryEventValid = true;
                $scope.checkStockEventAvailable();
            };

            function checkAllStockEventsAvailable() {
                if (!appUtil.isWarehouseOrKitchen()) {
                    var currentUser = appUtil.getCurrentUser();
                    var requestObject = {
                        unit: currentUser.unitId,
                        generatedBy: currentUser.userId,
                        eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                        inventoryResponse: null
                    };
                    $http({
                        method: "POST",
                        url: apiJson.urls.stockManagement.validateStockEvents,
                        data: requestObject
                    }).then(function (response) {
                        if (response != null) {
                                $scope.availableFrequencies = [];
                                $scope.stockTakeEventTypes = response.data;
                                var flag1 = false;
                                var flag2 = false;
                               for(var i=0; i<$scope.stockTakeEventTypes.length; i++){
                                   if($scope.stockTakeEventTypes[i].valid == true && i==0){
                                       $scope.availableFrequencies[0] = $scope.frequencies[1];
                                       flag1 = true;
                                   }else if($scope.stockTakeEventTypes[i].valid == true && i==1){
                                       if(flag1 == false) {
                                           $scope.availableFrequencies[0] = $scope.frequencies[2];
                                           flag2 = true;
                                       }else {
                                           $scope.availableFrequencies[1] = $scope.frequencies[2];
                                           flag2 = true;
                                       }
                                   }
                               }
                               if(flag1 == false && flag2 == false){
                                   $scope.availableFrequencies[0] = $scope.frequencies[0];
                               }
                               $scope.frequencies = $scope.availableFrequencies;
                            }
                    }, function (response) {
                        console.log(response);
                    })
                }
            };

            $scope.checkStockEventAvailable = function() {
                if(!appUtil.isWarehouseOrKitchen() && ($scope.frequencyValue == "WEEKLY" || $scope.frequencyValue == 'MONTHLY')){
                    var currentUser = appUtil.getCurrentUser();
                    var requestObject = {
                        unit: currentUser.unitId,
                        generatedBy:currentUser.userId,
                        eventType: $scope.isOpening ? "OPENING" : "CLOSING",
                        inventoryResponse: null
                    };
                    $http.post(apiJson.urls.stockManagement.validateStockEvent
                        + "?stockType=" + $scope.frequencyValue, requestObject).success(function (response) {
                        console.log(response);
                        if (response != null) {
                            if(response.valid != null) {
                                $scope.inventoryEventValid = response.valid;
                                $scope.inventoryValidationErrorMsg = response.errorMsg;
                            } else {
                                $scope.inventoryEventValid = false;
                                $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                            }
                        } else {
                            $scope.inventoryEventValid = false;
                            $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                        }
                    }).error(function (response) {
                        $scope.inventoryEventValid = false;
                        if(response.errorMsg != null) {
                            $scope.inventoryValidationErrorMsg = response.errorMsg;
                        } else {
                            $scope.inventoryValidationErrorMsg = "Error checking inventory event validity";
                        }
                    })
                } else {
                    $scope.inventoryEventValid = true;
                    $scope.inventoryValidationErrorMsg = null;
                }
            };

            $scope.selectFrequency = function(frequency){
                $scope.frequencyValue = frequency;
            };

            $scope.checkClosingInitiated = function (unitId) {
                $http({
                    method: "POST",
                    url: apiJson.urls.stockManagement.closingInitiated,
                    data: {
                        unitId: unitId,
                        businessDate: appUtil.getCurrentBusinessDate()
                    }
                }).then(function (response) {
                    $scope.closingInitiated = response.data;
                    if($scope.closingInitiated){
                        predictExpectedValues($scope.frequencyValue,false);
                    }
                }, function () {
                    console.log("Insert");
                })
            };

            $scope.removeMapping = function(selectedPackagings,selected,product){
                selected.value = null;
                calculateVariance(selectedPackagings,product);
            };

            $scope.showFixedAssets = function(frequency){
              return frequency == "FIXED_ASSETS";
            };

            $scope.changeStock = function(product,value,packaging){
                if(!$scope.checkEmpty(value) && parseFloat(value)>=0){
                    var productId = product.productId;
                    var mappings = product.packagingMappings;
                    if(!$scope.checkEmpty(value) && !$scope.checkEmpty(packaging)) {
                        calculateVariance(mappings,product);
                        $scope.edited = true;                   //edited stock flag turn to true
                    }
                }else if(!$scope.checkEmpty(value) && parseFloat(value) < 0){
                    $toastService.create("Please select a value greater than or equal to zero");
                }
            };

            function checkIfOpeningNeeded(unitId) {
                $http({
                    method:'GET',
                    url:apiJson.urls.stockManagement.checkOpening,
                    params:{
                        unitId: unitId
                    }
                }).then(function(response){
                    $scope.isOpening = response.data;
                    if($scope.isOpening){
                        $scope.selectFrequency("MONTHLY");
                    }
                },function(response){
                    console.log(response);
                    $scope.isOpening = false;
                });
            }

            function saveInventory(products){
                if(!$scope.checkEmpty($scope.productList) && $scope.productList.length > 0 && $scope.edited){
                    $toastService.create("Auto saving the inventory list");
                    $scope.lastSaved = Date.now();
                    appUtil.setSavedInventoryList({
                        unit:$scope.unitId,
                        products:!$scope.checkEmpty(products)? products : $scope.productList,
                        stockType:$scope.frequencyValue,
                        lastSaved:$scope.lastSaved
                    });
                    $scope.edited = false; // reset edited flag so that it can be re-saved on edit
                }
            }

            function predictExpectedValues(frequencyValue, refresh){
                var currentUser = appUtil.getCurrentUser();
                $http({
                    method:'POST',
                    url:apiJson.urls.stockManagement.predictExpectedValues,
                    data:{
                        unitId: currentUser.unitId,
                        stockTakeType: frequencyValue,
                        userId: currentUser.userId,
                        stockEventType: $scope.isOpening ? "OPENING" : "CLOSING"
                    }
                }).then(function(response){
                    if(!appUtil.checkEmpty(response)){
                        $scope.fixedAssetsOnly = (frequencyValue == "FIXED_ASSETS");
                        var productList = response.data.inventoryResponse;
                        packagingService.getAllPackagingMappings(function(packagingMappings){
                            $scope.packagingMappings = packagingMappings;
                            setProducts(productList);
                        });
                        var saved = appUtil.getSavedInventoryList();
                        if(!$scope.checkEmpty(saved) && !$scope.checkEmpty(saved.products)){
                            var savedList = angular.copy(saved.products);
                            var savedProductMap = [];

                            savedList.forEach(function(product){
                                savedProductMap[product.productId] = product;
                            });

                            productList.forEach(function(product){
                                var savedProduct = savedProductMap[product.productId];
                                if(!$scope.checkEmpty(savedProduct) &&
                                        !$scope.checkEmpty(savedProduct.packagingMappings)){
                                    calculateVariance(savedProduct.packagingMappings,product);
                                }
                            });
                        }

                        $scope.productList = productList; // assigning products after massaging
                        $scope.setPage(1);

                    }
                },function(response){
                    console.log(response);
                });
            }

            function getProductName(scmProductList, toFind) {
                for(var index in scmProductList){
                    var product = scmProductList[index];
                    if(product.productId == toFind.productId){
                        toFind.productName = product.productName;
                        toFind.unitOfMeasure = product.unitOfMeasure;
                        return toFind;
                    }
                }
            }


             $scope.setPage = function(page){

                 if($scope.pager==undefined){
                     $scope.pager = pagerService.getPager($scope.productList.length,page);
                     $scope.page= page;
                 }else{
                     if (page < 1 || page > $scope.pager.totalPages) {
                         return;
                     }
                     // get pager object from service
                     $scope.pager = pagerService.getPager($scope.productList.length, page);
                     $scope.page = $scope.pager.currentPage;
                     // get current page of items
                 }
                $scope.items = $scope.productList.slice($scope.pager.startIndex, $scope.pager.endIndex + 1);
            };


            function findValue(packaging, selectedPackagings) {
                if(!appUtil.isEmptyObject(selectedPackagings)){
                    for(var i=0;i<selectedPackagings.length;i++){
                        var selected = selectedPackagings[i];
                        if(packaging.packagingId==selected.packagingId){
                            return selected.value;
                        }
                    }
                }
            }

            function setProducts(productList){
                if(productList==undefined){
                    productList = $scope.productList;
                }
                if($scope.checkEmpty(packagingService.definitions)){
                    $scope.packagings = appUtil.getPackagingMap();
                    packagingService.setAllProfiles($scope.packagings);
                }
                $rootScope.showSpinner = true;
                var scmProductList = appUtil.getScmProductDetails();
                productList.forEach(function(product){
                    var productId = product.productId;
                    product = getProductName(scmProductList,product);
                    if(!appUtil.isEmptyObject(product)){
                        var pkgValueMap = {};
                        if(!appUtil.isEmptyObject(product.packagingMappings)){
                            product.packagingMappings.forEach(function (mpg) {
                                pkgValueMap[mpg.packagingId] = mpg.value;
                            });
                        }
                        product = packagingService.setMappingsByProduct(product,$scope.packagingMappings[productId]);
                        product.packagingMappings.forEach(function(pkg){
                            pkg.value = !appUtil.isEmptyObject(pkgValueMap[pkg.packagingId]) ? pkgValueMap[pkg.packagingId]:null;
                        });
                    }
                });
                $rootScope.showSpinner = false;
            }

            function addToMappings(mappings,packaging,value) {
               /* var added = false;
                mappings.forEach(function(mapping){
                    if(mapping.packaging.packagingCode == packaging.packagingCode){
                        added = true;
                        mapping.value = value;
                    }
                });
                if(!added){
                    mappings.push({value:value,packaging:packaging});
                }
                return mappings;*/
            }

            function checkIfMappingsHaveValues(mappings) {
                return mappings.filter(function(mapping){
                    return !appUtil.isEmptyObject(mapping.value);
                }).length>0;
            }

            function calculateVariance(mappings,product){
                if(mappings.length > 0 && checkIfMappingsHaveValues(mappings)) {
                    product.stockValue = 0;
                    mappings.forEach(function (mapping) {
                        if(!appUtil.isEmptyObject(mapping.value)){
                            product.stockValue = parseFloat(product.stockValue)
                                + (parseFloat(mapping.value) * parseFloat(mapping.conversionRatio));
                        }
                    });
                    product.variance = parseFloat(product.expectedValue - product.stockValue);
                }else{
                    product.stockValue = parseFloat(product.expectedValue) >= 0 ? parseFloat(product.expectedValue) : 0;
                    product.variance = 0;
                }
            }

            function checkInventoryUpdated(unitId) {
                $http({
                    method:'POST',
                    url:apiJson.urls.stockManagement.inventoryUpdated,
                    data:{
                        unitId:unitId,
                        businessDate:appUtil.getCurrentBusinessDate()
                    }
                }).then(function(response){
                    $scope.inventoryUpdated = response.data;

                    if(response.data && notFixedAssets()){
                        appUtil.removeSavedInventoryList();
                        $interval.cancel($scope.refreshInterval);
                        $scope.lastSaved = null;
                    }
                },function(response){
                    console.log("got error",response);
                });
            }

            $scope.refreshDayclose = function() {
                $http({
                    method:'POST',
                    url:apiJson.urls.stockManagement.kettleDayCloseFromSumo,
                    params:{
                        unitId:$scope.unitId,
                    }
                }).then(function(response){
                    console.log(response);
                    if(response.data == true) {
                        $scope.checkClosingInitiated($scope.unitId)
                        $toastService.create("Day Closed Successfully Completed From Kettle!");
                    }else{
                        $toastService.create("Day Closed Cannot Be Completed Because Of Some Error!");
                    }
                });
            }


            function notFixedAssets() {
                var inventory = appUtil.getSavedInventoryList();
                return !appUtil.isEmptyObject(inventory) && inventory.stockType!="FIXED_ASSETS";
            }
        }
    ]
);
