'use strict';

angular.module('scmApp')
    .controller('poCreateCtrl', ['$rootScope', '$scope', '$state', 'apiJson', '$http', 'appUtil', 'metaDataService',
        '$toastService','$alertService','previewModalService','Popeye',
        function ($rootScope, $scope, $state, apiJson, $http, appUtil, metaDataService,
                                                   $toastService, $alertService, previewModalService, Popeye) {

            function getKey(skuId,pkgId) {
                return skuId + "-" + pkgId;
            }

            $scope.init = function (){
                $scope.minDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                $scope.maxDate = appUtil.formatDate(appUtil.getDate(365), "yyyy-MM-dd");
                $scope.locationList = [];

                $scope.currentUnit = appUtil.getUnitData();
                $scope.vendorRegCheck = true;
                metaDataService.getVendorsForUnit($scope.currentUnit.id,function(vendorsForUnit){

                        $scope.vendorList = vendorsForUnit.filter(function (vendor) {
                            return vendor.type==="VENDOR";
                        });
                    }
                );
                $scope.poItems = null;
                $scope.showPreview = previewModalService.showPreview;
                $scope.isForFixedAsset = false;
                if(appUtil.isCafe()){
                    $scope.isForFixedAsset = true;
                }
                $scope.selectedQty = null;
                $scope.poComment = null;
                $scope.selectedType = null;
            };

            $scope.initCharges = function(){
                $scope.totalCostIn = 0;
                $scope.totalTaxIn = 0;
                $scope.totalAmountIn = 0 ;
            };
            $scope.selectVendor = function(vendor){
                if(vendor.leadTime==null || vendor.leadTime==0)
                {
                    $alertService.alert("Error!", "No lead time");
                    return;
                }
                if(vendor.status=='ACTIVE') {
                    console.log("inside select vendor")
                    $scope.skuList = [];
                    $scope.poItems = null;
                    $scope.selectedDispatchLocation = null;
                    $scope.selectedVendor = vendor;
                    $scope.locationList = $scope.selectedVendor.dispatchLocations.filter(function (loc) {
                        return loc.status == "ACTIVE";
                    });
                }
                else
                {
                    $toastService.create("selected vendor is not in active status it is in "+vendor.status+" please get it active");
                    //$alertService.alert("selected vendor is not in active status it is in "+vendor.status+" please get it active");

                }
            };


            $scope.toggleFixedAssetFlag = function(){
                if(!appUtil.isEmptyObject($scope.selectedVendor)
                    && !appUtil.isEmptyObject($scope.selectedDispatchLocation)) {
                    $scope.poItems = null;
                    $scope.selectedQty = 0;
                    console.log($scope.selectedQty);
                    $scope.selectedSku = null;
                    $scope.packagingList = null;
                    // document.getElementById("selectedQty").innerText = "";

                   // getSKUPriceAndTaxesForProfile()
                }
            };

            $scope.validateHandOverDate = function(type) {
                console.log("unit data is ",appUtil.getUnitData());
                var unitData = appUtil.getUnitData();
                if (appUtil.isCafe()) {
                    if (unitData.handoverDate == null) {
                        if (type != "CAPEX") {
                            $toastService.create("Can not create PO with type "+type+"  as hand over date is empty..!");
                            $scope.selectedType = null;
                            $scope.displaySelectVendor = false;
                            return;
                        }
                        else {
                            $scope.selectedType = type;
                            $scope.displaySelectVendor = true;
                            return;
                        }
                    }
                    else {
                        var date = new Date(unitData.handoverDate);
                        date.setDate(date.getDate() + 15);
                        var calculatedHandOverDate = date;
                        console.log("hand over date is ",calculatedHandOverDate);
                        console.log("current date is :",new Date(appUtil.getCurrentBusinessDate()));

                        if (new Date(appUtil.getCurrentBusinessDate()) <= calculatedHandOverDate) {
                            if (type != "CAPEX") {
                                $toastService.create("Can not create PO with type " + type + "as cafe is not hand overed yet ..!");
                                $scope.selectedType = null;
                                $scope.displaySelectVendor = false;
                                return;
                            }
                        }
                        else {
                            if (type != "OPEX") {
                                $toastService.create("Can not create PO with type " + type + "as cafe is hand overed ..!");
                                $scope.selectedType = null;
                                $scope.displaySelectVendor = false;
                                return;
                            }
                        }
                    }
                }
                $scope.selectedType = type;
                $scope.displaySelectVendor = true;
            };

            $scope.categoryFilter = function(sku){
                if($scope.isForFixedAsset){
                    if(sku.skuData.category == 3 ){
                        return sku;
                    }
                } else {
                    if(sku.skuData.category != 3 ){
                        return sku;
                    }
                }
            }

            $scope.poFilter = function(purchaseOrder){
                console.log(purchaseOrder)
                if($scope.isForFixedAsset){
                    if(purchaseOrder.orderType == 'FIXED_ASSET_ORDER' ){
                        return purchaseOrder;
                    }
                } else {
                    if(purchaseOrder.orderType == 'REGULAR_ORDER' ){
                        return purchaseOrder;
                    }
                }
            }

            $scope.selectDispatchLocation = function(location){
                $scope.selectedDispatchLocation = location;
                if($scope.selectedDispatchLocation.gstin == null){
                	$scope.vendorRegCheck = false;
                }
                $scope.getPendingOrders();
                $scope.getPriceAndTaxData();
            };

            $scope.getPriceAndTaxData = function(){
                if(!appUtil.isEmptyObject($scope.selectedVendor)
                    && !appUtil.isEmptyObject($scope.selectedDispatchLocation)){
                    getSKUPriceAndTaxesForProfile()
                }else{
                    $toastService.create("Please select Vendor Dispatch Location correctly");
                }
            };

            function getSKUPriceAndTaxesForProfile() {
                var roleIds = $rootScope.purchaseRoles.map(function (role) {return role.id;}).join(",");
                metaDataService.getSkuPricesAndTaxesForProfile($scope.selectedVendor.vendorId,
                    $scope.selectedDispatchLocation.dispatchId, $scope.currentUnit.id, roleIds, function(skuAndTaxData){
                        if(skuAndTaxData.length>0){
                            $scope.skuList = skuAndTaxData;
                            $scope.getConsumptionData($scope.skuList);
                        }else{
                            $toastService.create("Could not fetch prices for the dispatch location");
                            $scope.skuList = [];
                            $scope.packagingList = [];
                        }
                    });
            }

            function makeSkuLookUp(pendingPOs) {
                $scope.skuLookUp = {};
                for(var i in pendingPOs){
                    for(var j in pendingPOs[i].orderItems){
                        if(appUtil.isEmptyObject($scope.skuLookUp[pendingPOs[i].orderItems[j].skuId])){
                            $scope.skuLookUp[pendingPOs[i].orderItems[j].skuId] = {
                                poList:[],
                                received:0,
                                requested:0
                            };
                        }
                        var sku = $scope.skuLookUp[pendingPOs[i].orderItems[j].skuId];
                        if(sku.poList.indexOf(pendingPOs[i].receiptNumber) == -1){
                            sku.poList.push(pendingPOs[i].receiptNumber);
                            sku.received += pendingPOs[i].orderItems[j].receivedQuantity;
                            sku.requested += pendingPOs[i].orderItems[j].requestedAbsoluteQuantity;
                        }
                    }
                }
            }

            $scope.getPendingOrders = function(){
                if($scope.selectedDispatchLocation==null){
                    return;
                }
                $http({
                    method:"GET",
                    url:apiJson.urls.requestOrderManagement.getPendingPOs,
                    params:{
                        vendorId:$scope.selectedVendor.vendorId,
                        deliveryUnitId:$scope.currentUnit.id,
                        dispatchId:$scope.selectedDispatchLocation.dispatchId,
                        startDate:appUtil.formatDate(appUtil.getDate(-365), "yyyy-MM-dd"),
                        endDate:appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd"),
                        purchaseOrderId:$scope.poId,
                        status: $scope.selectedStatus
                    }
                }).then(function(response){
                    if(!appUtil.isEmptyObject(response.data)) {
                        makeSkuLookUp(response.data);
                    }
                },function(error){
                    console.log(error);
                });
            };

            $scope.getConsumptionData = function (skuList) {
                var skuArr = skuList.map(function(sku){
                    return sku.id;
                });
                var skuStr = skuArr.join(",");
                $http({
                    method:"GET",
                    url:apiJson.urls.requestOrderManagement.purchaseConsumption,
                    params:{
                        skus: skuStr,
                        unitId: $scope.currentUnit.id,
                        days: 90
                    }
                }).then(function(response){
                    if(!appUtil.isEmptyObject(response.data)){
                        $scope.getCurrentStock(skuArr,response.data, addToSkuList);
                    }
                },function(error){
                    console.log(error);
                });
            };

            $scope.getCurrentStock = function (skuList,consumptionData, callback) {
                $http({
                    url:apiJson.urls.warehouseClosing.stockAtHand,
                    method:'POST',
                    data:{
                        unitId:$scope.currentUnit.id,
                        skuIds:skuList
                    }
                }).then(function(stock){
                    if(appUtil.isEmptyObject(stock.data)){
                        $toastService.create("could not fetch current stock at hand for this unit");
                    }else{
                        if (typeof callback == "function"){
                            callback(consumptionData, stock.data);
                        }
                    }
                },function(){
                    $toastService.create("could not fetch current stock at hand for this unit");
                });
            };

            function addToSkuList(response, currentStock) {
                var consumption = [];

                for(var i in response){
                    if(appUtil.isEmptyObject(consumption[response[i].id])){
                        consumption[response[i].id] = {};
                        consumption[response[i].id]['total'] = 0;
                    }
                    consumption[response[i].id][response[i].date] = response[i].qty;
                    consumption[response[i].id]['total'] += response[i].qty;
                }

                $scope.skuList.forEach(function (sku) {
                    var consumed=0, total=0;
                    if(!appUtil.isEmptyObject(consumption[sku.id])){
                        total = consumption[sku.id]['total'];
                        delete consumption[sku.id]['total'];
                        consumed = consumption[sku.id];
                    }
                    sku['consumed'] = consumed;
                    sku['totalConsumed'] = total.toFixed(2);
                    sku['currentStock'] = findInCurrentStock(currentStock,sku.id);
                });
            }

            function findInCurrentStock(skuList, skuId) {
                for(var i in skuList){
                    if(skuList[i].skuId == skuId){
                        return skuList[i].stockValue;
                    }
                }
                return 0;
            }

            $scope.clonePO = function(){
                if(appUtil.isEmptyObject($scope.clonablePOs)){
                    $http({
                        method:"GET",
                        url:apiJson.urls.requestOrderManagement.getPurchaseOrders,
                        params:{
                            vendorId:$scope.selectedVendor.vendorId,
                            deliveryUnitId:$scope.currentUnit.id,
                            dispatchId:$scope.selectedDispatchLocation.dispatchId
                        }
                    }).then(function(response){
                        $scope.clonablePOs = response.data;
                    },function(error){
                        console.log(error);
                    });
                }
            };

            function recalculate(orderItems) {
                for(var i in orderItems){
                    var item = orderItems[i];
                    $scope.skuList.forEach(function(skuItem){
                       if(skuItem.id==item.skuId){
                           var pkg = skuItem.skuData.packagings.filter(function(pkg){
                               return item.packagingId==pkg.id;
                           })[0];
                           $scope.addSku(skuItem,pkg,item.packagingQty);

                           return false;//to break loop if found
                       }
                    });
                }
            }


            $scope.cloneThisPO = function(orderItems){
                $alertService.confirm("Are you sure?","",function(result){
                    if(result){
                        $rootScope.showFullScreenLoader = true;
                        recalculate(orderItems);
                        $toastService.create("Items added to the list. Please close to see the changes");
                        $rootScope.showFullScreenLoader = false;
                    }
                });
            };

            $scope.changeValues = function(items){
                $scope.initCharges();
                angular.forEach(items,function(item) {
                    $scope.totalCostIn += item.amount;
                    $scope.totalTaxIn += item.appliedTax;
                });
                $scope.totalAmountIn = $scope.totalCostIn + $scope.totalTaxIn;
                console.log($scope.totalAmountIn);
            };


            $scope.previewPO = function(items){
                if ($scope.selectedType == null) {
                    $toastService.create("Please select the Budget type..!");
                    return;
                }
                if(appUtil.isEmptyObject($scope.selectedDate)){
                    $toastService.create("Please select a proper fulfillment date");
                }else{
                    $scope.totalCost = 0;
                    $scope.totalTax = 0;
                    $scope.totalAmount = 0;
                    $scope.maxSkuLeadTime =0;
                    angular.forEach(items,function(item){
                        $scope.totalCost += item.amount;
                        $scope.totalTax += item.appliedTax;
                        if($scope.maxSkuLeadTime<item.pkg.leadTime && (item.pkg.leadTime!=null || item.pkg.leadTime!=0))
                        {
                            $scope.maxSkuLeadTime=item.pkg.leadTime;
                        }
                    });
                    $scope.totalAmount = $scope.totalCost + $scope.totalTax;

                    var previewModal = Popeye.openModal({
                        templateUrl : 'previewPO.html',
                        controller : "previewPOCtrl",
                        modalClass: 'modal-large',
                        resolve : {
                            items : function() {
                                return items;
                            },
                            totalCost: function(){
                                return $scope.totalCost;
                            },
                            totalTax:function () {
                                return $scope.totalTax;
                            },
                            totalAmount:function () {
                                return $scope.totalAmount;
                            },
                            selectedVendor: function () {
                                return $scope.selectedVendor;
                            },
                            selectedDispatchLocation: function () {
                                return $scope.selectedDispatchLocation;
                            },
                            maxSkuLeadTime: function () {
                                return $scope.maxSkuLeadTime;
                            }
                        },
                        click : false,
                        keyboard : false
                    });

                    previewModal.closed.then(function (result) {
                        if(result){
                            if ($scope.selectedType == "CAPEX") {
                                $scope.openPOBudgetModal();
                            }
                            else {
                                $scope.submit();
                            }
                        }
                    });
                }
            };

            function getFixedAssetBudget(){
                $scope.budgetDetails = {};
                var promise = $http({
                    url: apiJson.urls.requestOrderManagement.getDepartmentBudgetData,
                    method: "GET",
                    params: {
                        unitId: appUtil.getCurrentUser().unitId,
                        isFixedAssetOrGoods: $scope.isForFixedAsset ? "FA_Equipment" : "NRE_Consumable"
                    }
                }).then(function (response) {
                    if (response.status == 200) {
                        $scope.emptyCheck = appUtil.isEmptyObject(response.data);
                        if ($scope.emptyCheck) {
                            $scope.budgetDetails = {};
                            $toastService.create("Cannot find budget details");
                        }
                        else {
                            $scope.budgetDetails = response.data;
                        }
                    }
                }, function (response) {
                    console.log(response);
                });
                return promise;
            }

            $scope.openPOBudgetModal = function(){
                getFixedAssetBudget().then(function (data) {
                    var POBudgetModal = Popeye.openModal({
                        templateUrl: 'departmentPOModal.html',
                        controller: "departmentPOCtrl",
                        modalClass: 'modal-large',
                        resolve: {
                            totalCost: function () {
                                return $scope.totalCost;
                            },
                            totalTax: function () {
                                return $scope.totalTax;
                            },
                            totalAmount: function () {
                                return $scope.totalAmount;
                            },
                            emptyCheck: function () {
                                return $scope.emptyCheck;
                            },
                            budgetDetails: function () {
                                return $scope.budgetDetails;
                            }
                        },
                        click: false,
                        keyboard: false
                    });

                    POBudgetModal.closed.then(function (result) {
                        if (result) {
                            $scope.submit();
                        }
                    });
                });
            };

            $scope.submit = function(){
                $alertService.confirm("Are you sure?","",function(result){
                   if(result){
                       sendRequestForPO($scope.preparePOItems());
                   }
                });
            };

            $scope.changeSelectedDate = function(date){
                $scope.selectedDate = date;
            };

            $scope.addComment = function (poComment) {
                $scope.poComment = poComment;
            }

            function sendRequestForPO(items){

                var reqObj = {
                    deliveryUnitId:$scope.currentUnit.id,
                    dispatchId:$scope.selectedDispatchLocation.dispatchId,
                    vendorId:$scope.selectedVendor.vendorId,
                    fulfilmentDate:$scope.selectedDate,
                    userId:appUtil.getCurrentUser().userId,
                    creationType:"MANUAL",
                    comment : $scope.poComment != null ? $scope.poComment : null,
                    items:items,
                    leadTime: $scope.maxSkuLeadTime,
                    orderType:$scope.isForFixedAsset ? 'FIXED_ASSET_ORDER' : 'REGULAR_ORDER',
                    type: $scope.selectedType
                };

                if(reqObj.items.length>0){
                    $http({
                        method:"POST",
                        url:apiJson.urls.requestOrderManagement.createPurchaseOrder,
                        data:reqObj
                    }).then(function(response) {
                        if (response.data != null) {
                            $scope.init();
                            var message = parseFloat($scope.totalCost) <= parseFloat(10000)
                                ? "Vendor has also been notified. Please check with the Vendor also!"
                                : "Vendor has not been notified since the amount is greater than <b>10000</b>";
                            $alertService.alert("Congratulations!!",
                                "Purchase Order with PO ID: <b>" + response.data + "</b> created successfully! <br>" + message,
                                function () {
                                    $state.go("menu.approvePo", {createdPO: response.data, viewPO: true});
                                }
                            );
                        } else {
                            $toastService.create("Purchase Order could not be created due to some error!!");
                        }
                    },function(error){
                        console.log(error);
                        if (error.data.errorMsg != null) {
                            $alertService.alert(error.data.errorTitle, error.data.errorMsg, null, true);
                        }
                        else {
                            $toastService.create("Purchase Order could not be created due to some error!!");
                        }
                    });
                }
            }


            $scope.selectSku = function(sku){
                $scope.selectedSku = sku;
                $scope.selectedPackaging = null;
                $scope.selectedQty = null;
                $scope.packagingList = null;
                $scope.packagingList = sku.skuData.packagings;
                $scope.selectedPackaging = $scope.packagingList[0];
                // console.log(" after 1 after Selected Pcakaging is ",$scope.selectedPackaging);
                // $scope.selectPkg($scope.packagingList[0]);
            };

            $scope.setSelectedQty = function (qty){
                console.log("before ",$scope.selectedQty);
                $scope.selectedQty = qty;
                console.log("after ",$scope.selectedQty);

            };

            $scope.openChart = function (skuData) {
                var consumptionModal = Popeye.openModal({
                    templateUrl : 'consumption.html',
                    controller : "consumptionCtrl",
                    modalClass: 'modal-medium',
                    resolve : {
                        sku : function() {
                            return skuData;
                        },
                        selectedVendor: function () {
                            return $scope.selectedVendor;
                        },
                        selectedDispatchLocation: function () {
                            return $scope.selectedDispatchLocation;
                        },
                        currentUnit: function () {
                            return $scope.currentUnit;
                        },
                        pendingForSku:function () {
                            if(!appUtil.isEmptyObject($scope.skuLookUp) && !appUtil.isEmptyObject($scope.skuLookUp[skuData.id])){
                                return $scope.skuLookUp[skuData.id];
                            }else{
                                return null;
                            }
                        }
                    },
                    click : true,
                    keyboard : true
                });
            };

            $scope.selectPkg = function(packaging){
                $scope.selectedPackaging = packaging;
                // $scope.selectedPkg = packaging;
                // $scope.setSelectedQty(0);
                // document.getElementById("qtyy").value = 0;
            };

            var getTaxValue = function (taxValue, amount) {
                return {
                    value:(amount*taxValue)/100,
                    percentage:taxValue
                };
            };

            function getAmount(pkg, qty, tax, isExempted) {
                isExempted = isExempted!=undefined ? isExempted : false;
                var price = pkg.price;
                var ratio = pkg.ratio;
                var gst = 0;
                var otherTaxes = 0;
                var amount = parseFloat(price * qty);
                var obj = {
                    amount:amount,
                    cgst:{value:null,percentage:null},
                    igst:{value:null,percentage:null},
                    sgst:{value:null,percentage:null},
                    others:{taxes:[],value:null,percentage:null},
                    tax:null
                };
                
                if(!$scope.vendorRegCheck){
                	obj.tax = 0;
                	return obj;
                }

                if(!isExempted){
                    if(appUtil.isEmptyObject(tax.state)){
                        $alertService.alert("Error!", "No Tax detail found for this product");
                        return;
                    }
                    if($scope.currentUnit.location.state.code.toString() != $scope.selectedDispatchLocation.address.stateCode.toString()){
                        obj.igst = getTaxValue(tax.state.igst,amount);
                        obj.tax = obj.igst.value
                    }else{
                        obj.cgst = getTaxValue(tax.state.cgst,amount);
                        obj.sgst = getTaxValue(tax.state.sgst,amount);
                        obj.tax = obj.cgst.value + obj.sgst.value;
                    }

                    if(tax.others.length>0){
                        for(var index in tax.others){
                            var otherTax = tax.others[index];
                            var valueAndTax = getTaxValue(otherTax.tax,getApplicableAmount(otherTax.applicability,obj));
                            var changedTax = {
                                taxName:otherTax.type,
                                taxCategory:otherTax.type,
                                percentage:valueAndTax.percentage,
                                value:valueAndTax.value
                            };
                            obj.others.taxes.push(changedTax);
                            otherTaxes += valueAndTax.value;
                        }
                        obj.tax += otherTaxes;
                    }
                }
                return obj;
            }

            function getApplicableAmount(applicability,obj){
                if(applicability == "ON_TAX"){
                    return getValue(obj.cgst) + getValue(obj.igst) + getValue(obj.sgst);
                }else{
                    return obj.amount;
                }
            }

            function getValue(taxObj){
                return appUtil.isEmptyObject(taxObj.value) ? 0 : taxObj.value;
            }

            $scope.addSku = function (sku,pkg,qty){
                console.log(sku,pkg,qty);
                if(appUtil.isEmptyObject(sku)){
                    alert("Please Select SKU to Add");
                    return false;
                }
                if (appUtil.isEmptyObject(pkg)){
                    alert("Please Select Packaging to Add");
                    return false;
                }
                if(appUtil.isEmptyObject(qty)){
                    alert("Please Enter Quantity to Add");
                    return false;
                }
                // console.log(document.getElementById("qtyy").value);
                if(appUtil.isEmptyObject($scope.poItems)){
                   $scope.poItems = {};
                }
                var amountAndTax = getAmount(pkg,qty,sku.taxData,sku.taxData.exempted);
                if(!appUtil.isEmptyObject(amountAndTax)){
                    $scope.poItems[getKey(sku.id,pkg.id)] = {
                        sku:sku.skuData,
                        taxes:sku.taxData,
                        pkg:pkg,
                        qty:qty,
                        amount: amountAndTax.amount,
                        appliedTax: amountAndTax.tax,
                        taxText: getTaxText(amountAndTax),
                        edit:false
                    };
                }
                console.log($scope.poItems);
                $scope.changeValues($scope.poItems);
            };

            $scope.editItem = function(id){
                $scope.poItems[id].edit = true;
            };

            function getTaxText(taxes){
                var text = "";
                for(var key in taxes){
                    if(key !="amount" && key != "others" && key !="tax" && !appUtil.isEmptyObject(taxes[key].value)){
                        text += key.toUpperCase() + ":" + taxes[key].percentage + "% ";
                    }
                }
                if(taxes.others.length>0){
                    for(var i in taxes.others){
                        text += taxes.others[i].type + ":" + taxes.others[i].tax + "% ";
                    }
                }
                return text;
            }

            $scope.updateItem = function(item,id){
                var amountAndTax = getAmount(item.pkg,item.qty,item.taxes, item.exemptItem);
                $scope.poItems[id] = {
                    sku:item.sku,
                    taxes:item.taxes,
                    pkg:item.pkg,
                    qty:item.qty,
                    amount: amountAndTax.amount,
                    appliedTax: amountAndTax.tax,
                    edit:false
                };
                $scope.changeValues($scope.poItems);
            };


            $scope.removeItem = function(key){
                console.log("Deeleting item "+ $scope.poItems[key]);
                delete $scope.poItems[key];
                if(Object.keys($scope.poItems).length==0){
                    $scope.poItems =  null;
                }
                console.log($scope.poItems);
                $scope.changeValues($scope.poItems);
            };


            $scope.preparePOItems = function(){
                var purchaseItems = angular.copy($scope.poItems);
                purchaseItems = Object.values(purchaseItems).map(function(item){
                    var amountAndTax = getAmount(item.pkg,item.qty,item.taxes, item.taxes.exempted);
                    return {
                        "skuId":item.sku.id,
                        "skuName":item.sku.name,
                        "requestedQuantity":item.pkg.ratio * item.qty,
                        "unitOfMeasure":item.sku.uom,
                        "hsn":item.sku.hsn,
                        "unitPrice":item.pkg.price,
                        "totalCost":item.amount,
                        "packagingId":item.pkg.id,
                        "packagingName":item.pkg.name,
                        "packagingQty":item.qty,
                        "conversionRatio":item.pkg.ratio,
                        "igst":amountAndTax.igst,
                        "cgst":amountAndTax.cgst,
                        "sgst":amountAndTax.sgst,
                        "otherTaxes":amountAndTax.others.taxes,
                        "type": $scope.selectedType
                    };
                });
                return purchaseItems;
            };

        }
    ]
).controller('consumptionCtrl', ['$scope','sku','selectedVendor','selectedDispatchLocation','currentUnit', 'pendingForSku','appUtil',
    function ($scope, sku, selectedVendor, selectedDispatchLocation, currentUnit, pendingForSku,appUtil) {

        function createChartOptions(sku) {
            //var labels = Object.keys(sku['consumed']).map(function(label){return appUtil.formatDate(label,"yyyy-MM-dd");});
            var data = [];
            var labels = prepareRollingLabels();
            var dateWise = prepareDistribution(sku['consumed']);
            for(var i in dateWise){
                data[i] = parseFloat(dateWise[i]);
            }
            return {
                chart: {type: 'column', marginLeft:70},
                title: {text: 'Consumption over last 90 days'},
                xAxis: {categories: labels},
                yAxis: {title: {text: 'Qty'}, labels:{enabled:true}, min:0},
                series: [{
                    name:sku.skuData.name,
                    data:data
                }]
            };
        }

        function prepareRollingLabels() {
            var date1= appUtil.formatDate(appUtil.getTimeInPast(90),"yyyy-MM-dd");
            var date2= appUtil.formatDate(appUtil.getTimeInPast(60),"yyyy-MM-dd");
            var date3= appUtil.formatDate(appUtil.getTimeInPast(30),"yyyy-MM-dd");
            var date4= appUtil.formatDate(appUtil.getTimeInPast(0),"yyyy-MM-dd");
            return [date1 +" to "+ date2, date2+" to "+date3, date3+" to "+date4];
        }

        function prepareDistribution(consumption) {
            var toDate = new Date();
            var distribution = [0,0,0];
            for(var i in consumption){
                var chunk = Math.abs(Math.ceil(appUtil.datediff(i, toDate) / 30) - 3);
                console.log(chunk);
                if(appUtil.isEmptyObject(distribution[chunk])){
                    distribution[chunk]=0;
                }
                distribution[chunk] = parseFloat(distribution[chunk]) + parseFloat(consumption[i]);
            }
            return distribution;
        }

        $scope.initChart = function () {
            $scope.sku = sku;
            $scope.chartOptions = createChartOptions(sku);
            $scope.selectedVendor = selectedVendor;
            $scope.selectedDispatchLocation = selectedDispatchLocation;
            $scope.currentUnit = currentUnit;
            $scope.pendingForSku = pendingForSku;
        };

    }
]).controller('previewPOCtrl', ['$scope','totalAmount','totalCost',
    'totalTax','items','Popeye','selectedVendor','selectedDispatchLocation', 'maxSkuLeadTime',
    function ($scope, totalAmount, totalCost, totalTax, items, Popeye, selectedVendor, selectedDispatchLocation, maxSkuLeadTime) {

        $scope.initPreview = function () {
            $scope.totalAmount = totalAmount;
            $scope.totalTax = totalTax;
            $scope.totalCost = totalCost;
            $scope.poItems = items;
            $scope.selectedVendor = selectedVendor;
            $scope.selectedDispatchLocation = selectedDispatchLocation;
            $scope.skuLeadTime = maxSkuLeadTime;
        };

        $scope.close = function(){
            Popeye.closeCurrentModal(false);
        };

        $scope.submit = function () {
            Popeye.closeCurrentModal(true);
        };

    }
]).controller('departmentPOCtrl', ['$scope','$http', 'apiJson','appUtil','$toastService','totalAmount','totalCost',
    'totalTax','Popeye','emptyCheck','budgetDetails',
    function ($scope, $http,apiJson,appUtil, $toastService, totalAmount, totalCost, totalTax, Popeye, emptyCheck, budgetDetails) {

        $scope.totalAmount = totalAmount;
        $scope.totalTax = totalTax;
        $scope.totalCost = totalCost;
        $scope.emptyCheck = emptyCheck;
        console.log("empty check is ",emptyCheck,budgetDetails);
        $scope.budgetDetails = budgetDetails;

        $scope.cancel = function(){
            Popeye.closeCurrentModal(false);
        };

        $scope.submit = function () {
            if (appUtil.isEmptyObject($scope.budgetDetails)) {
                $toastService.create("No budget Details Found Can not place Order !");
                return;
            }
            if( $scope.totalAmount > $scope.budgetDetails.remainingAmount){
                $toastService.create("Exceeding Budget limit! Can not place Order !");
                return;
            }
            Popeye.closeCurrentModal(true);
        };

    }
]);