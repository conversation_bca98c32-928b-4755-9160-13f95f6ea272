/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('grActivityCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'Popeye', 'previewModalService','$alertService','metaDataService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, Popeye, previewModalService, $alertService,metaDataService) {

                $scope.isWarehouse = appUtil.isWarehouseOrKitchen();
                $scope.init = function () {
                    $scope.showPendingGr = true;
                    $scope.amountWithoutRejected = 0;
                    $scope.getPendingGRs();
                    $scope.selectedSkus = [];
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.allheaders = ['Pending Disputes' , 'Pending Receivings', 'Raised Disputes' , 'All'];
                  /*  $scope.allheaders = [
                        { name : 'Pending Disputes',checked : false} ,
                        {name : 'Pending Receivings' , checked: false},
                        {name :'Raised Disputes',checked: false} ,{ name :'All' , checked: false}];*/
                    $scope.selectedHeader = 'All';
                    $scope.toId = null;
                    $scope.currentUser = appUtil.getCurrentUser();
                    $scope.userList = [];
                    $scope.selectedUser = null;
                    $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option}}</b>'};
                    metaDataService.getSkuListForUnit($scope.currentUser.unitId,function(skuForUnitList){
                        $scope.skuList = skuForUnitList;
                        $scope.selectedSku = null;
                        console.log($scope.selectedSku);
                    });

                };

                $scope.getSkuFilterData = function (){
                    $scope.getPendingGRs();
                }


                $scope.getGrView = function (header){
                    $http({
                        url: apiJson.urls.goodsReceivedManagement.getGrsView,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        params : {
                            unitId : $scope.currentUser.unitId,
                            type : header
                        },
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                        .success(
                            function (data) {
                                console.log("excell generated");
                                var fileName =  "Goods_Received" ;
                                    + ".xlsx";
                                var blob = new Blob(
                                    [data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }).error(function (err) {
                        console.log("Error during getting data", err);
                    });

                }



                $scope.isFiltered = function (item){
                    if($scope.toId !=null){
                        if($scope.toId == 51){
                            console.log("to id :",item.transferOrderId);
                        }
                        if(item.transferOrderId === $scope.toId){
                            return true;
                        }
                        return false;
                    }else if($scope.selectedUser !=null){
                        if(item.generatedBy.name === $scope.selectedUser){
                            return true;
                        }
                        return  false;
                    }
                    return  true;
                }



                $scope.getPendingGRs = function () {
                    $scope.allGrs = [];
                    $scope.addToList('PendingDisputedGR', 'No pending disputed goods receiving found.', 'Pending Disputes', true)
                    $scope.addToList('PendingGR', 'No pending goods receiving found.', 'Pending Receivings', false)
                    $scope.addToList('RaisedDisputedGR', 'No raised disputes goods receiving found.', 'Raised Disputes', false)
                    $scope.getPendingGRData();
                    $scope.getPendingDisputedGrs();
                    $scope.getRaisedDisputedGrs();



                };

                $scope.getPendingGRData = function () {
                    $scope.pendingGRList = null;
                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.pendingGrs + "?unitId=" + appUtil.getCurrentUser().unitId,
                        params: {
                            skuId : $scope.selectedSku != null ? $scope.selectedSku.id : null
                        }
                    }).then(function success(response) {
                        $scope.updateList('PendingGR', response.data);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.addToList = function (type, message, header, actionForRejected) {
                    var data = {
                        type: type,
                        message: message,
                        header: header,
                        list: null,
                        actionForRejected: actionForRejected
                    };
                    $scope.allGrs.push(data);

                };

                $scope.updateList = function (type, list){
                   for(var gr in list){
                       if($scope.userList.indexOf(list[gr].generatedBy.name) == -1){
                           $scope.userList.push(list[gr].generatedBy.name);
                       }

                   }
                    for (var i in $scope.allGrs) {
                        if ($scope.allGrs[i].type == type) {
                            $scope.allGrs[i].list = list;
                            return;
                        }
                    }

                };

                $scope.getPendingDisputedGrs = function () {
                    $scope.pendingDisputedGRList = null;
                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.pendingDisputedGrs + "?unitId=" + appUtil.getCurrentUser().unitId,
                        params: {
                            skuId : $scope.selectedSku != null ? $scope.selectedSku.id : null
                        }
                    }).then(function success(response) {
                        $scope.updateList('PendingDisputedGR', response.data);
                        //console.log($scope.pendingDisputedGRList);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.getColorCode = function (grDate){
                    var grdate =  appUtil.formatDate(grdate, "yyyy-MM-dd hh:mm:ss");
                    var grDate = new Date(grDate);
                    var currentDate = new Date();
                    grDate.setHours(0,0,0,0);
                    currentDate.setHours(0,0,0,0);
                    if(currentDate.getTime()>grDate.getTime()){
                        return "#cdffcd";
                    }else if(currentDate.getTime()<grDate.getTime()){
                        return 'red';
                    }else{
                        return "#ffffbf";
                    }
                }


                $scope.getRaisedDisputedGrs = function () {
                    $scope.raisedDisputedGRList = null;


                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.raisedDisputedGrs + "?unitId=" + appUtil.getCurrentUser().unitId,
                        params: {
                            skuId : $scope.selectedSku != null ? $scope.selectedSku.id : null
                        }
                    }).then(function success(response) {
                        $scope.updateList('RaisedDisputedGR', response.data);
                        //console.log($scope.raisedDisputedGRList);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.fillReceiving = function (grId) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.goodReceived + "?grId=" + grId
                    }).then(function success(response) {
                        $scope.grDetail = response.data;
                        $scope.setInitialUnitReceived();
                        $scope.showPendingGr = false;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.findReceivingAmount = function(){
                    if (!appUtil.isEmptyObject($scope.grDetail)) {
                        $scope.amountWithoutRejectedDup = 0;
                        $scope.grDetail.goodsReceivedItems.forEach(function (grItem) {
                            grItem.packagingDetails.forEach(function (pkg) {
                                if (pkg.numberOfUnitsRejected == 0 || pkg.numberOfUnitsRejected == null || pkg.numberOfUnitsRejected == undefined) {
                                    console.log("negotiated Price and  tax amount is : ", grItem.unitPrice, grItem.taxAmount);
                                    var unitPrice = grItem.unitPrice != null ? parseFloat(grItem.unitPrice) : 0;
                                    var calAmount = $scope.getTotalCost(grItem.transferredQuantity,unitPrice);
                                    console.log("cal amount is : ",calAmount);
                                    var taxAmount = 0;
                                    if (grItem.taxAmount != null) {
                                        taxAmount = parseFloat(grItem.taxAmount);
                                    }
                                    console.log("tax amount is : ",taxAmount);
                                    // var totalAmount = parseFloat(calAmount) + parseFloat(taxAmount);
                                    var totalAmount = $scope.getAmountAndTax(parseFloat(calAmount),parseFloat(taxAmount));
                                    console.log("total amount is : ",totalAmount);
                                    $scope.amountWithoutRejectedDup = parseFloat(parseFloat($scope.amountWithoutRejectedDup) + parseFloat(totalAmount)).toFixed(6);
                                    console.log("amnt with out rejected new is : ",$scope.amountWithoutRejectedDup);
                                }
                            });
                        });
                        console.log("amount without rejected after adding is : ", $scope.amountWithoutRejected);
                        $scope.grDetail.amountWithoutRejected = parseFloat($scope.amountWithoutRejectedDup) > 0 ? parseFloat($scope.amountWithoutRejectedDup) : 0;

                        if ($scope.grDetail.originalGrForUnitId != null) {
                            if ($scope.grDetail.originalGrForUnitId == appUtil.getCurrentUser().unitId) {
                                $scope.grDetail.updateRecievings = true;
                            }
                            else {
                                $scope.grDetail.updateRecievings = false;
                            }
                        }
                        $scope.settleGR();
                    }
                };

                $scope.validateChecks = function(){
                    if ($scope.grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER'
                        || $scope.grDetail.transferOrderType == 'BROKEN_ASSET_TRANSFER'
                        || $scope.grDetail.transferOrderType == 'RENOVATION_ASSET_TRANSFER') {
                        if (verifyAssetId()) {
                            return;
                        }
                    }
                    var isCommentRequired = false;
                    $scope.grDetail.goodsReceivedItems.forEach(function (grItem) {
                        grItem.packagingDetails.forEach(function (pkg) {
                            if (pkg.numberOfUnitsRejected > 0 && appUtil.isEmptyObject($scope.grDetail.comment)) {
                                if (!isCommentRequired) {
                                    $toastService.create("Please provide comment for rejected unit!");
                                    isCommentRequired = true;
                                }
                            }
                        });
                    });
                    if (isCommentRequired) {
                        return;
                    }

                    var grPreviewModal = Popeye.openModal({
                        templateUrl: "grPreview.html",
                        controller: "grPreviewCtrl",
                        resolve: {
                            grDetail: function () {
                                return $scope.grDetail;
                            }
                        },
                        scope:$scope,
                        click: false,
                        keyboard: false
                    });
                    grPreviewModal.closed
                        .then(function (isSuccessful) {
                            if (isSuccessful) {
                              $scope.checkAssetOrGoods();
                            }
                        });
                };

                $scope.validateAssetTagValue = function (assetTagValue, grItem) {
                    if (assetTagValue == null || assetTagValue.length != 6) {
                        grItem.assetVerified = false;
                        // grItem.associatedAssetId = null;
                        return;
                    }
                    if (assetTagValue == grItem.associatedAssetTagValue) {
                        grItem.assetVerified = true;
                    } else {
                        grItem.assetVerified = false;
                    }
                }

                $scope.setInitialUnitReceived = function () {
                    $scope.grDetail.goodsReceivedItems.forEach(function (grItem) {
                        grItem.receivedQuantity = grItem.transferredQuantity;
                        grItem.packagingDetails.forEach(function (pkg) {
                            pkg.numberOfUnitsReceived = pkg.numberOfUnitsPacked;
                            pkg.receivedQuantity = pkg.transferredQuantity
                        });
                    })
                };

                $scope.updateRejection = function (drilldowns, pkg, grItem ,isDrillDownRequired) {
                    pkg.unitsRejected = 0;
                    pkg.expiryDrillDown = [];
                    var i = 0;
                    for (i = 0; i < drilldowns.length; i++) {
                        var d = drilldowns[i];
                        if(!isDrillDownRequired){
                            d.rejection = 0;
                        }
                        pkg.unitsRejected = pkg.unitsRejected + d.rejection;
                        pkg.expiryDrillDown.push({
                            expiryDate: d.expiryDate,
                            keyId: d.keyId,
                            keyType: d.keyType,
                            price: d.price,
                            quantity: d.rejection
                        });
                    }
                    $scope.calculateReceivedQty(pkg, grItem);
                };

                $scope.isSemiFinished = function (grItem) {
                    return grItem.category == 'Semi Finished';
                };


                $scope.backbuttonClick = function () {
                    $scope.showPendingGr = true;
                    $scope.tIdCheck = null;
                };

                $scope.calculateReceivedQty = function (pkg, grItem) {

                    if (pkg.unitsRejected < 0) {
                        $toastService.create("Negative values are not allowed here");
                        return;
                    }

                    if (appUtil.isFloat(pkg.unitsRejected) && pkg.packagingDefinitionData.conversionRatio > 1) {
                        $toastService.create("Please enter a correct value for rejecting the item.");
                        return;
                    }

                    if (appUtil.isFloat(pkg.unitsRejected) && (pkg.packagingDefinitionData.packagingType === "LOOSE" &&
                        (pkg.packagingDefinitionData.unitOfMeasure === "PC" || pkg.packagingDefinitionData.unitOfMeasure === "SACHET"))) {
                        $toastService.create("Please enter a correct value for rejecting the item.");
                        return;
                    }

                    var received = angular.copy(pkg.numberOfUnitsReceived);
                    pkg.numberOfUnitsReceived = pkg.numberOfUnitsPacked - pkg.unitsRejected;
                    if (pkg.numberOfUnitsReceived >= 0) {
                        pkg.receivedQuantity = pkg.numberOfUnitsReceived * pkg.packagingDefinitionData.conversionRatio;
                        pkg.numberOfUnitsRejected = angular.copy(pkg.unitsRejected);
                        $scope.updateItemPkg(grItem);
                    } else {
                        $toastService.create("You cannot reject more than you have received.");
                        pkg.numberOfUnitsReceived = received;
                    }
                };

                $scope.updateItemPkg = function (grItem) {
                    var qty = 0;
                    grItem.packagingDetails.forEach(function (item) {
                        qty += item.receivedQuantity;
                    });
                    grItem.receivedQuantity = qty;
                };

                function getGRDetail(grId) {
                    var promise = $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.goodReceived + "?grId=" + grId
                    }).then(function success(response) {
                        $scope.grDetail = response.data;
                        $scope.setInitialUnitReceived();
                        console.log("response.data", response.data);
                        return response.data;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                    return promise;
                }

                $scope.getTotalCost = function (selectedQuantity, price) {
                    return (selectedQuantity * price).toFixed(6);
                };

                $scope.getAmountAndTax = function(total_amount,total_tax) {
                    return (total_amount + total_tax).toFixed(6);
                };

                $scope.openAcceptOptions = function ($event, grId) {
                    $scope.budgetDetails = {};
                    getGRDetail(grId).then(function (data) {
                        $scope.amountWithoutRejectedDup = 0;
                        if ($scope.grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER'
                            || $scope.grDetail.transferOrderType == 'BROKEN_ASSET_TRANSFER'
                            || $scope.grDetail.transferOrderType == 'RENOVATION_ASSET_TRANSFER') {
                            $scope.grDetail.goodsReceivedItems.forEach(function (grItem) {
                                grItem.packagingDetails.forEach(function (pkg) {
                                    if (pkg.numberOfUnitsRejected == 0 || pkg.numberOfUnitsRejected == null || pkg.numberOfUnitsRejected == undefined) {
                                        console.log("Changes Logic");
                                        var unitPrice = grItem.negotiatedUnitPrice != null ? parseFloat(grItem.negotiatedUnitPrice) : 0;
                                        var calAmount = $scope.getTotalCost(grItem.transferredQuantity,unitPrice);
                                        console.log("cal amount is : ", calAmount);
                                        var taxAmount = 0;
                                        if (grItem.taxAmount != null) {
                                            taxAmount = parseFloat(grItem.taxAmount);
                                        }
                                        console.log("tax amount is : ", taxAmount);
                                        // var totalAmount = parseFloat(calAmount) + parseFloat(taxAmount);
                                        var totalAmount = $scope.getAmountAndTax(parseFloat(calAmount),parseFloat(taxAmount));
                                        console.log("total amount is : ", totalAmount);
                                        $scope.amountWithoutRejectedDup = parseFloat(parseFloat($scope.amountWithoutRejectedDup) + parseFloat(totalAmount)).toFixed(6);
                                        console.log("amnt with out rejected new is : ", $scope.amountWithoutRejectedDup);
                                    }
                                });
                            });
                        }
                        $scope.grDetail.amountWithoutRejected = parseFloat($scope.amountWithoutRejectedDup) > 0 ? parseFloat($scope.amountWithoutRejectedDup) : 0;
                        var mappingModal = Popeye.openModal({
                            templateUrl: "acceptOption.html",
                            controller: "acceptOptionCtrl",
                            resolve: {
                                grDetail: function () {
                                    return $scope.grDetail;
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        mappingModal.closed
                            .then(function (isSuccessful) {
                                if (isSuccessful) {
                                    $scope.showPendingGr = true;
                                    $scope.getPendingGRs();
                                }
                            });
                    });
                    $event.stopPropagation();
                };

                $scope.acceptDeclinedGoodsReceive = function ($event, grId) {
                    getGRDetail(grId).then(function (data) {
                        var currentUser = appUtil.getCurrentUser();
                        $scope.grDetail.receivedBy = appUtil.createGeneratedBy();
                        $http({
                            method: "PUT",
                            url: apiJson.urls.goodsReceivedManagement.acceptDeclinedGR,
                            data: $scope.grDetail
                        }).then(function success(response) {
                            if (response.data != null && response.data == true) {
                                $toastService.create("Declined Goods Receiving Accepted Successfully!");
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                            $scope.showPendingGr = true;
                            $scope.getPendingGRs();
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    });
                    $event.stopPropagation();
                };


                $scope.openDeclineOptions = function ($event, grId) {
                    getGRDetail(grId).then(function (data) {
                        var mappingModal = Popeye.openModal({
                            templateUrl: "declineOption.html",
                            controller: "declineOptionCtrl",
                            resolve: {
                                grDetail: function () {
                                    return $scope.grDetail;
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        mappingModal.closed
                            .then(function (isSuccessful) {
                                if (isSuccessful) {
                                    $scope.showPendingGr = true;
                                    $scope.getPendingGRs();
                                }
                            });

                    });
                    $event.stopPropagation();
                };

                function verifyAssetId() {
                    for (var i in $scope.grDetail.goodsReceivedItems) {
                        var grItem = $scope.grDetail.goodsReceivedItems[i];
                        console.log(grItem);
                        if (grItem.packagingDetails[0].hasRejection != null
                            && grItem.packagingDetails[0].hasRejection == true) {

                        } else {
                            if (grItem.assetVerified != true) {
                                var position = parseInt(i)+1;
                                $toastService.create("Please enter appropriate asset id for " + position + " GR Item");
                                return true;
                            }
                        }
                    }
                    return false;
                }

                $scope.checkAssetOrGoods = function(){
                    if ($scope.grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER'
                        || $scope.grDetail.transferOrderType == 'BROKEN_ASSET_TRANSFER'
                        || $scope.grDetail.transferOrderType == 'RENOVATION_ASSET_TRANSFER') {
                        $scope.findReceivingAmount();
                    }
                    else{
                        $scope.settleGR();
                    }
                };

                $scope.settleGR = function () {
                    console.log($scope.grDetail);
                    $scope.grDetail.receivedBy = appUtil.createGeneratedBy();
                    $http({
                        method: "PUT",
                        url: apiJson.urls.goodsReceivedManagement.settleGR,
                        data: $scope.grDetail
                    }).then(function success(response) {
                        if (response.data != null && response.data == true) {
                            $toastService.create("Goods Receiving Settled Successfully!");
                            $scope.showPendingGr = true;
                            $scope.getPendingGRs();
                        } else {
                            $http({
                                method: "GET",
                                url: apiJson.urls.goodsReceivedManagement.isGrSettled + "?grId=" + $scope.grDetail.id.toString()
                            }).then(function success(response) {
                                if (response.data == true) {
                                    $scope.showPendingGr = true;
                                    $scope.getPendingGRs();
                                    $toastService.create("GR for GR Id " + $scope.grDetail.id + " is already settled!");
                                } else {
                                    $toastService.create("Something went wrong. Please try again!");
                                }
                            })
                        }

                    }, function error(response) {
                        console.log("error:" + response);
                        if (response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    });
                };

                $scope.cancelGR = function () {
                    $http({
                        method: "PUT",
                        url: apiJson.urls.goodsReceivedManagement.cancelGR,
                        data: $scope.grDetail.id
                    }).then(function success(response) {
                        if (response.data != null && response.data == true) {
                            $toastService.create("Goods Receiving Cancelled Successfully!");
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                        $scope.showPendingGr = true;
                        $scope.getPendingGRs();
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.enableRejectionInput = function (button, pkg ,grItem) {

                    if (pkg.hasRejection != null && pkg.hasRejection == true) {
                        pkg.hasRejection = false
                        pkg.numberOfUnitsRejected = 0;
                        pkg.rejectionReason = null;
                        button.value = "Reject";
                        $scope.updateRejection(grItem.drillDowns,pkg,grItem,false);
                    } else {
                        pkg.hasRejection = true
                        button.value = "Cancel Reject"
                    }
                }

            }
        ]
    ).controller('grPreviewCtrl', ['$scope', 'grDetail','Popeye',
        function ($scope, grDetail,Popeye) {
            $scope.grDetail = grDetail;

            $scope.back = function () {
                Popeye.closeCurrentModal(false);
            };

            $scope.submit = function () {
                Popeye.closeCurrentModal(true);
            };


    }]).
controller('acceptOptionCtrl', ['$scope', 'grDetail', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye','$alertService',
    function ($scope, grDetail, appUtil, $toastService, apiJson, $http, Popeye, $alertService) {

            $scope.grDetail = grDetail;
            $scope.currentUnit = appUtil.getCurrentUser();
            $scope.isSuccessful = true;

            $scope.rejectedGoodsReceivedFound = function () {
                if (appUtil.isEmptyObject($scope.grDetail.rejectGRComment)) {
                    $toastService.create("Please Provide reason!");
                    return;
                }

                if ($scope.grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER'
                    || $scope.grDetail.transferOrderType == 'BROKEN_ASSET_TRANSFER'
                    || $scope.grDetail.transferOrderType == 'RENOVATION_ASSET_TRANSFER') {
                    if ($scope.grDetail.originalGrForUnitId != null) {
                        if ($scope.grDetail.originalGrForUnitId == $scope.currentUnit.unitId) {
                            $scope.grDetail.updateRecievings = true;
                        }
                        else {
                            $scope.grDetail.updateRecievings = false;
                        }
                    }
                }
                $scope.grDetail.receivedBy = appUtil.createGeneratedBy();
                $http({
                    method: "PUT",
                    url: apiJson.urls.goodsReceivedManagement.rejectedFoundGR,
                    data: $scope.grDetail
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        $toastService.create("Goods Receiving Settled Successfully!");
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                    closeModal();
                }, function error(response) {
                    $scope.isSuccessful = false;
                    closeModal();
                    if (response.data.errorMsg != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                    console.log("error:" + response);
                });
            };

            $scope.rejectedGoodsReceivedLost = function () {
                if (appUtil.isEmptyObject($scope.grDetail.rejectGRComment)) {
                    $toastService.create("Please Provide reason!");
                    return;
                }
                $scope.grDetail.receivedBy = appUtil.createGeneratedBy();
                $http({
                    method: "PUT",
                    url: apiJson.urls.goodsReceivedManagement.rejectedLostGR,
                    data: $scope.grDetail
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        $toastService.create("Goods Receiving Loss Registered Successfully!");
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                    closeModal();
                }, function error(response) {
                    $scope.isSuccessful = false;
                    console.log("error:" + response);
                    closeModal();
                });
            };

            function closeModal() {
                Popeye.closeCurrentModal($scope.isSuccessful);
            }

        }
    ]
).controller('declineOptionCtrl', ['$scope', 'grDetail', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye',
        function ($scope, grDetail, appUtil, $toastService, apiJson, $http, Popeye) {
            $scope.grDetail = grDetail;
            //  console.log("$scope.grDetail",$scope.grDetail);
            $scope.isSuccessful = true;

            $scope.rejectedGoodsReceivedDecline = function () {
                if (appUtil.isEmptyObject($scope.grDetail.rejectGRComment)) {
                    $toastService.create("Please Provide reason!");
                    return;
                }
                $scope.grDetail.cancelledBy = appUtil.createGeneratedBy();
                $http({
                    method: "PUT",
                    url: apiJson.urls.goodsReceivedManagement.declineGR,
                    data: $scope.grDetail
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        $toastService.create("Goods Receiving Declined Successfully!");
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                    closeModal();
                }, function error(response) {
                    $scope.isSuccessful = false;
                    closeModal();
                    console.log("error:" + response);
                });
            };

            $scope.cancel = function () {
                $scope.isSuccessful = false;
                closeModal();
            };

            function closeModal(value) {
                Popeye.closeCurrentModal($scope.isSuccessful);
            }

        }
    ]
);
