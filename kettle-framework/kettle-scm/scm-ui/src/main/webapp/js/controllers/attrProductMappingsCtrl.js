/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 11-05-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('attrProductMappingsCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'appUtil',
            '$toastService', '$stateParams', '$http', 'metaDataService', 'apiJson', 'productService',
            function ($rootScope, $scope, authService, $location, $state, appUtil, $toastService, $stateParams, $http,
                      metaDataService, apiJson, productService) {

                $scope.init = function () {
                    $scope.products = {};
                    $scope.mappingsArray = [];
                    $scope.mappings = [];
                    $scope.categories = [];
                    $scope.selectedMappings = [];
                    $scope.attributes = null;
                    $scope.category = null;
                    $scope.editMode = true;
                    $scope.mappingToEdit = {};
                    $scope.stateProductId = $stateParams.product;
                    metaDataService.getCategories(function (categories) {
                        $scope.categories = categories;
                    });
                    metaDataService.getAttrDefinitions(function (attributes) {
                        $scope.attributes = attributes;
                    });
                    productService.getAllProducts(function (products) {
                        $scope.products = products;
                        $scope.selectProduct($scope.stateProductId);
                    });

                    getAllMappings();
                };

                $scope.categorySelectOptions = {
                    width: '100%'
                };

                $scope.selectProduct = function (productId) {
                    if (productId != null && productId != "") {
                        $scope.productId = productId;
                        console.log(productId);
                    }
                };

                function getAllMappings() {
                    $scope.mappingsArray = [];
                    metaDataService.getCategoryAttributeMappings(function(mappings){
                        $scope.mappingsArray = mappings;
                        /*var data = mappings;
                        for (var key in data) {
                            var subArray = data[key];
                            $scope.mappingsArray = $scope.mappingsArray.concat(subArray);
                        }*/
                    });
                }

                $scope.getMappings = function (category) {
                    var category = JSON.parse(category);
                    if (category != undefined && category != null) {
                        $scope.selectedMappings = $scope.mappingsArray[category.categoryId];
                        /*$scope.selectedMappings = [];
                        $scope.mappingsArray.forEach(function (mapping) {
                            if (mapping.categoryDefinition.id == category.categoryId) {
                                $scope.selectedMappings.push(mapping);
                            }
                        });*/
                    }
                };

                /*$scope.getAttribute = function (id) {
                    var attribute = appUtil.getAttribute(id);
                    return !appUtil.checkEmpty(attribute) ? attribute.attributeName : 'Not found';
                };*/

                $scope.editMapping = function(mappingToEdit){
                    $scope.mappingToEdit['mapping'] = mappingToEdit;
                    $scope.mappingToEdit.attributeName = $scope.getAttribute(mappingToEdit.attributeDefinition.id);
                    $scope.mappingToEdit.status = mappingToEdit.mappingStatus == "ACTIVE";
                };

                //will work on need base only
                $scope.prepareAttributes = function (mappings,editMode) {
                    $scope.editMode = editMode;
                    var categoryMappingsMap = {};
                    $scope.mappings = [];
                    if (mappings != null) {
                        mappings.forEach(function (mapping) {
                            categoryMappingsMap[mapping.attributeDefinition.id] = mapping;
                        });
                        $scope.mappings = mappings;
                    }
                    for (var key in $scope.attributes) {
                        for (var attr in $scope.attributes[key]) {
                            var attribute = $scope.attributes[key][attr];
                            if (categoryMappingsMap != {} && categoryMappingsMap[attribute.attributeId] != null) {
                                /*attribute.checked = true;
                                attribute.mapping = categoryMappingsMap[attribute.attributeId];*/
                            } else {
                                $scope.attributes[key][attr] = prepareMapping($scope.attributes[key][attr]);
                            }
                        }
                    }
                    return $scope.attributes;
                };

                function prepareMapping(attribute) {
                    attribute.checked = false;
                    var category = JSON.parse($scope.category);
                    attribute.mapping = {
                        attributeDefinition: {id:attribute.attributeId,code:attribute.attributeCode,name:attribute.attributeName},
                        categoryDefinition: {id:category.categoryId,code:category.categoryCode,name:category.categoryName},
                        mappingStatus: "ACTIVE",
                        mandatory: false,
                        usedInNaming: false,
                        mappingOrder: 0
                    };
                    return attribute;
                }

                $scope.submitMappings = function (editMode,mappings) {
                    console.log(mappings);
                    var reqObj = mappings.filter(function(mapping){
                        console.log(mapping.categoryAttributeMappingId === undefined);
                        return mapping.categoryAttributeMappingId === undefined;
                    });
                    console.log(mappings, editMode);
                    if (reqObj.length != 0) {
                        $http({
                            method: 'POST',
                            url: apiJson.urls.categoryManagement.attributeMapping,
                            data: reqObj
                        }).then(function success(response) {
                            console.log("Status for added mappings", response.data);
                            if (response.data) {
                                var status = "added";
                                var message  = "Mappings "+status+" succesfully";
                                $toastService.create(message, function () {
                                    if($scope.editMode){
                                        $scope.mappingToEdit = {};
                                    }
                                    getAllMappings();
                                });
                            }
                        }, function error(response) {
                            console.log("Encountered an error",response);
                        });
                    }
                };
                $scope.reset = function(){
                    getAllMappings();
                    $scope.getMappings(JSON.parse($scope.category).categoryId);
                };

                $scope.changeState = function (isChecked, attribute) {
                    /* if (!isChecked) {
                      prepareMapping(attribute);
                      var toRemove = -1;
                      for(var index in $scope.mappings){
                      if($scope.mappings[index].attributeDefinition.id == attribute.attributeId){
                      toRemove = index;
                      }
                      }
                      $scope.mappings.splice(toRemove,toRemove!= -1 ? 1 : 0);
                      }else{
                      var category = JSON.parse($scope.category);
                      var attrMapping = attribute.mapping;
                      if(attrMapping){

                      }
                      attrMapping['mappingStatus'] = "ACTIVE";
                      attrMapping['attributeDefinition'] = {
                      id:attribute.attributeId,
                      code:attribute.attributeCode,
                      name:attribute.attributeName
                      };
                      attrMapping['categoryDefinition'] = {
                      id:category.categoryId,
                      code:category.categoryCode,
                      name:category.categoryName
                      };
                      $scope.mappings.push(attrMapping);
                      if(attrMapping.usedInNaming && attrMapping.mappingOrder == 0){
                      $toastService.create("Mapping Order should be greater than 0");
                      }
                      }*/
                };
            }
        ]
    );

