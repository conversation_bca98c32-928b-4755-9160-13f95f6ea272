/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('refOrderCreateCtrlV1', ['$rootScope', '$scope','$stateParams', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'recipeService', 'metaDataService', 'previewModalService', '$alertService',
        function ($rootScope, $scope,$stateParams, apiJson, $http, appUtil, $toastService, $state, recipeService, metaDataService, previewModalService, $alertService) {

            $scope.init = function () {
                $scope.metadataCategories = appUtil.getMenuProductCategories();
                $scope.productList = appUtil.getScmProductDetails();
                $scope.selectedCategories = [];
                $scope.expiryProduct = {};
                $scope.listOfEvents = [];
                $scope.warehouseordering = true;
                $scope.exceptionalProducts = [];
                $scope.expiryDataCheck = false;
                $scope.getCategories();
                // $scope.getExpiryProduct();
                $scope.noOfDays = 2;
                $scope.dineInSale=null;
                $scope.deliverySale=null;
                $scope.hasCategoryBuffer = false;
                $scope.showFulfillmentDateSelection = true;
                $scope.showMenuItemList = false;
                $scope.totalSale = 0;
                $scope.brandList = appUtil.getBrandList();
                $scope.brandList.forEach(function (value) {
                    if (value.brandCode == 'CH') {
                        $scope.chaayosId = value.brandId;
                    } else if (value.brandCode == 'GNT') {
                        $scope.gntId = value.brandId;
                    } else if (value.brandCode == 'DC') {
                        $scope.dcId = value.brandId;
                    }
                })
                $scope.setDates(appUtil.getDate(2), $scope.noOfDays);
                $scope.comment = null;
                $scope.minRefOrderFulFillmentDate = appUtil.getDate(1);
                $scope.maxRefOrderFulFillmentDate = appUtil.getDate(7);
                console.log($scope.minRefOrderFulFillmentDate + "," + $scope.maxRefOrderFulFillmentDate);
                metaDataService.getUnitProductData(function (unit) {
                    $scope.unitData = unit;
                    $scope.scmProductDetails = appUtil.getScmProductDetails();
                    $scope.menuCategories = recipeService.createMenuItemCategories();
                });
                $scope.brandDetails=[{id: 1 ,brandName: "Chaayos" },
                    {id: 3 ,brandName: "Ghee and Turmeric" },
                    {id: 0 ,brandName: "BOTH" }];
                $scope.selectedBrandDetails =null;
                $scope.showPreview = previewModalService.showPreview;
                // $scope.getSalesPercentage();
                $scope.raiseBy = false;
                var aclData = $rootScope.aclData.action;
                //console.log(aclData);
                if (aclData["CEREFO"] == true) {
                    console.log("acl data ");
                    $scope.raiseBy = true;
                }
                $scope.regularOrderingEvent = null;
                metaDataService.getRegularOrderingEvents(appUtil.getCurrentUser().unitId,function (events){
                    $scope.listOfEvents = events;
                    console.log("events are : ",events);
                    if ($scope.listOfEvents.length > 0) {
                        $scope.regularOrderingEvent = $scope.listOfEvents[0];
                        $scope.setDates();
                        $scope.selectedBrandDetails = setBrandDetails($scope.regularOrderingEvent.brand);
                    }
                });
            };

            function setBrandDetails(brand) {
                var result = {};
                for (var i=0;i<$scope.brandDetails.length;i++) {
                    if ($scope.brandDetails[i].brandName == brand) {
                        result = $scope.brandDetails[i];
                        break;
                    }
                }
                return result;
            }

            $scope.getCategories = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.unitMetadata.listTypes
                }).then(function success(response) {
                    $scope.categoryLists = response.data;
                    $scope.productCategory = $scope.categoryLists.CATEGORY;
                });
            }

            $scope.getExpiryProduct = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.referenceOrderManagement.expiryProductData
                }).then(function success(response) {
                    $scope.expiryProdEntry = response.data;
                    for (var i = 0; i < $scope.expiryProdEntry.length; i++) {
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id] = {};
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].id = $scope.expiryProdEntry[i].id;
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].productName = $scope.expiryProdEntry[i].code;
                        $scope.expiryProduct[$scope.expiryProdEntry[i].id].quantity = 0;
                    }
                    console.log(typeof $scope.expiryProduct);
                });
            }

            $scope.getNewExpiryProduct = function () {
                $scope.expiryDataCheck = false;
                var lastDate = $scope.OrderingDaysFinal[$scope.OrderingDaysFinal.length -1];
                $http({
                    method: 'GET',
                    url: apiJson.urls.referenceOrderManagement.getNewExpiryProductData,
                    params: {
                        "unitId":appUtil.getCurrentUser().unitId,
                        "days":$scope.noOfDays,
                        "lastDate": lastDate
                    }
                }).then(function success(response) {
                    $scope.expiryProduct = {};
                    $scope.expiryProdEntry = response.data;
                    for (var i = 0; i < $scope.expiryProdEntry.length; i++) {
                        if ($scope.expiryProduct[$scope.expiryProdEntry[i].productId] == undefined || $scope.expiryProduct[$scope.expiryProdEntry[i].productId] == null) {
                            $scope.expiryProduct[$scope.expiryProdEntry[i].productId] = {};
                            $scope.expiryProduct[$scope.expiryProdEntry[i].productId].productId = $scope.expiryProdEntry[i].productId;
                            $scope.expiryProduct[$scope.expiryProdEntry[i].productId].productName = $scope.expiryProdEntry[i].productName;
                            for (var j=0;j<$scope.dataEntry.length;j++) {
                                var product = $scope.expiryProduct[$scope.expiryProdEntry[i].productId];
                                var dateString = $scope.dataEntry[j].date;
                                product[dateString] = 0;
                                var date = appUtil.formatDate(new Date($scope.expiryProdEntry[i].expiryDate), "yyyy-MM-dd");
                                if (date == $scope.dataEntry[j].date) {
                                    product[date] = product[date] + $scope.expiryProdEntry[i].expiryQuantity;
                                }
                            }
                            console.log("current days inside is  : ",$scope.expiryProduct);
                        }
                        else {
                            var dateStr = appUtil.formatDate(new Date($scope.expiryProdEntry[i].expiryDate), "yyyy-MM-dd");
                            var productDup = $scope.expiryProduct[$scope.expiryProdEntry[i].productId];
                            var qty = productDup[dateStr];
                            if (qty == undefined || qty == null) {
                                qty = 0;
                            }
                            productDup[dateStr] = qty + $scope.expiryProdEntry[i].expiryQuantity;
                            console.log("qty is : ",productDup[dateStr]);
                        }
                    }
                    console.log($scope.expiryProduct);
                });
                $scope.getDayWiseExpiryProduct();
            }

            $scope.getDayWiseExpiryProduct = function () {
                $scope.expiryDataCheck = false;
                $http({
                    method: 'GET',
                    url: apiJson.urls.stockManagement.getDayWiseExpiryProduct,
                    params: {
                        "unitId":appUtil.getCurrentUser().unitId,
                        "dates": $scope.OrderingDaysFinal,
                        "isAggregated" : false,
                        "firstOrderingDate" : $scope.onlyOrderingDays[0]
                    }
                }).then(function success(response) {
                    console.log("response is of : ",response.data);
                    $scope.dayWiseExpiryProduct = response.data;
                    $scope.totalDayWiseExpiry = response.data["totalStock"];
                    $scope.expiryDataCheck = true;
                });
            }


            $scope.filterCategory = function (category) {
                // console.log(category)
                return category.detail.id != 8 // filter out combo(id==8)  categories
            }

            function makeDateString(date) {
                var newDate = new Date(date);
                var result = newDate.getFullYear() + "-" + (newDate.getMonth()+1) + "-" + (newDate.getDate());
                console.log("result Date is : ",result);
                return result;
            }

            $scope.setDates = function (date, days) {
                $scope.dataEntry = [];
                $scope.OrderingDaysFinal = [];
                $scope.remainingDaysFinal = [];
                $scope.onlyOrderingDays = [];
                $scope.fulfillmentDate = $scope.regularOrderingEvent != null ? makeDateString($scope.regularOrderingEvent.fulfilmentDate) : makeDateString(date);
                $scope.noOfDays = $scope.regularOrderingEvent != null ? $scope.regularOrderingEvent.orderingDays : days;
                $scope.fulfillmentDay = $scope.getDayOfWeekFromStr($scope.fulfillmentDate);
                $scope.stockLastingDate = appUtil.calculatedDate($scope.noOfDays - 1, $scope.fulfillmentDate);
                $scope.stockLastingDay = $scope.getDayOfWeek($scope.stockLastingDate);
                console.log(appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd"));
                console.log("Fulfilment Date is : ",$scope.fulfillmentDate);
                var regularOrderingDate = appUtil.getRegularOrderingDate();
                $scope.remainingDays = appUtil.datediffRO(regularOrderingDate, $scope.fulfillmentDate);
                for (var i = 1; i <= $scope.remainingDays - 1; i++) {
                    $scope.dataEntry.push({
                        dayType: 'REMAINING_DAY',
                        date:  appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"),
                        brands: [{
                            id: $scope.chaayosId,
                            saleAmount: 0,
                            deliverySalePercentage: 0
                        },
                            {
                                id: $scope.gntId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            },
                            {
                                id: $scope.dcId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            }],
                        
                    })
                    $scope.OrderingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                    $scope.remainingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, regularOrderingDate), "yyyy-MM-dd"));
                }
                for (var i = 0; i <= $scope.noOfDays - 1; i++) {
                    $scope.dataEntry.push({
                        dayType: 'ORDERING_DAY',
                        date: appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"),
                        brands: [{
                            id: $scope.chaayosId,
                            saleAmount: 0,
                            deliverySalePercentage: 0
                        },
                            {
                                id: $scope.gntId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            },
                            {
                                id: $scope.dcId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            }]
                    })
                    $scope.OrderingDaysFinal.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                    $scope.onlyOrderingDays.push(appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"));
                }
                console.log("data entry is :",$scope.dataEntry);
                console.log("Ordering days final is  : ",$scope.OrderingDaysFinal);
                if (appUtil.isEmptyObject($scope.noOfDays)) {
                    $toastService.create("Please Enter Ordering Days..!");
                    return false;
                }
                else {
                    $scope.getNewExpiryProduct();
                }
            };

            $scope.getDayOfWeek = getDayOfWeek;
            $scope.getDayOfWeekFromStr = getDayOfWeekFromStr;
            $scope.getDaysOfWeek = getDaysOfWeek;


            $scope.getSalesPercentage = function () {
                console.log("uit data",$scope.unitData)
                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    salesData: $scope.dataEntry,

                };


                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.salesPercentage,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        // $toastService.create("Reference order estimates calculated successfully!");
                        $scope.dataEntry = response.data.salesData;
                        $scope.dataEntry.forEach(function (data) {
                            data.date=$scope.dateformatting(data.date)
                        })
                        console.log($scope.dataEntry)
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            }


            $scope.getReferenceQuantitiesV1 = function () {

                if ($scope.hasCategoryBuffer) {
                    if (!$scope.categoryBuffer) {
                        alert("please enter category buffer percentage or unselect buffer box");
                        return;
                    }
                    if ($scope.selectedCategories.length == 0) {
                        alert("please select product categories on which buffer has to be applied");
                        return;
                    }
                }
                if($scope.menuCategories==null || ($scope.menuCategories!=null && $scope.menuCategories.length==0)){
                    $toastService.create("Wait to load category list.");
                    return;
                }
                if($scope.selectedBrandDetails==null || $scope.selectedBrandDetails===undefined){
                    $toastService.create("Select Brand");
                    return;
                }
                if (!$scope.expiryDataCheck) {
                    $toastService.create("Wait to load Expiring products data ..!");
                    return;
                }
                var brandMenuCategoriesDetail = $scope.selectedBrandDetails.brandName!=="BOTH" ? getBrandWiseMenuCategories(angular.copy($scope.menuCategories)) : $scope.menuCategories;
                $scope.showFulfillmentDateSelection = false;
                $scope.showMenuItemList = true;
                var brandName=  $scope.selectedBrandDetails.brandName=== "BOTH" ? null : $scope.selectedBrandDetails.brandName;
                var inputData = {
                    unitId: appUtil.getCurrentUser().unitId,
                    fulfillmentDate: $scope.fulfillmentDate,
                    noOfDays: $scope.noOfDays,
                    brandName: brandName,
                    categoryList: brandMenuCategoriesDetail,
                    salesData: $scope.dataEntry,
                    bufferedCategoryList: $scope.selectedCategories,
                    categoryBufferPercentage: $scope.categoryBuffer
                };
                $scope.totalSale = 0;
                $http({
                    method: "POST",
                    url: apiJson.urls.referenceOrderManagement.suggestingOrderEstimatesV1,
                    // url: apiJson.urls.referenceOrderManagement.referenceOrderEstimates,
                    data: inputData
                }).then(function success(response) {
                    if (response.data != null) {
                        $toastService.create("Reference order estimates calculated successfully!");
                        $scope.menuCategories = response.data.categoryList;
                        $scope.dineInSale = response.data.dineInSale;
                        $scope.deliverySale = response.data.deliverySale;
                        $scope.totalSale = response.data.totalSale;
                        $scope.refreshDate = response.data.refreshDate;
                        $scope.makeDayWiseMenuOrderings();
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                        $scope.totalSale = 0;
                    }
                }, function error(response) {
                    $toastService.create("Error Occurred While getting predictions from fountain 9..!");
                    $scope.totalSale = 0;
                    console.log("error:" + response);
                });
            };

            $scope.makeDayWiseMenuOrderings = function () {
                $scope.dayWiseMenuProducts = {};
                console.log("day entry is :",$scope.dataEntry);
                $scope.dataEntry.forEach(function (entry) {
                    if (entry.dayType == "ORDERING_DAY") {
                        var date = entry.date;
                        var copyOfMenu = angular.copy($scope.menuCategories);
                        $scope.dayWiseMenuProducts[date] = getCopyOfDayMenu(copyOfMenu,date,false);
                    }
                    else {
                        var date = entry.date;
                        var copyOfMenu = angular.copy($scope.menuCategories);
                        $scope.dayWiseMenuProducts[date] = getCopyOfDayMenu(copyOfMenu,date,true);
                    }
                });
                console.log("day wise are : ",$scope.dayWiseMenuProducts);
                $scope.makeDayWiseScmProducts($scope.dayWiseMenuProducts,$scope.dataEntry);
            };

            $scope.makeDayWiseScmProducts = function (menuProductsCategories, dataEntryData) {
                $scope.dayWiseScmProducts = {};
                angular.forEach(menuProductsCategories,function (value,key) {
                    recipeService.getScmProductsFromDayWiseMenu($scope.unitData, value, function (scmProducts) {
                        $scope.dayWiseScmProducts[key] = scmProducts;
                    });
                });
                console.log("scm list of all days is : ",$scope.dayWiseScmProducts);
                $scope.makeScmProductWiseMap(angular.copy($scope.dayWiseScmProducts),dataEntryData);
            };

            $scope.makeScmProductWiseMap = function (scmProducts,dataEntryData) {
                $scope.copyOfExpiryProduct = angular.copy($scope.expiryProduct);
                $scope.copyOfTotalStock = angular.copy($scope.totalDayWiseExpiry);
                console.log("copy of total stock is : ",$scope.copyOfTotalStock);
                $scope.getScmProductsAfterExpiry(scmProducts,dataEntryData);
            };

            $scope.getScmProductsAfterExpiry = function (scmProducts,dataEntryData) {
                $scope.scmProductWiseMap = {};
                console.log("entries are ss: ",dataEntryData);
                for (var i=0; i<$scope.OrderingDaysFinal.length; i++) {
                    var prods = scmProducts[$scope.OrderingDaysFinal[i]];
                    if (prods != undefined && prods != null) {
                        var dup = angular.copy($scope.OrderingDaysFinal);
                        for (var j=0;j<prods.length;j++) {
                            if ($scope.copyOfTotalStock[prods[j].id] != undefined && $scope.copyOfTotalStock[prods[j].id] != null) {
                                var arr = getArray(dup, i);
                                console.log("current prod is and date is  : ", prods[j],$scope.OrderingDaysFinal[i]);
                                var finalQty = checkForExpiry(prods[j], arr);
                                if ($scope.remainingDaysFinal.indexOf($scope.OrderingDaysFinal[i]) == -1) {
                                    if ($scope.scmProductWiseMap[prods[j].id] == undefined || $scope.scmProductWiseMap[prods[j].id] == null) {
                                        $scope.scmProductWiseMap[prods[j].id] = parseFloat(finalQty);
                                    } else {
                                        var qty = $scope.scmProductWiseMap[prods[j].id];
                                        $scope.scmProductWiseMap[prods[j].id] = qty + parseFloat(finalQty);
                                    }
                                }
                                else {
                                    console.log("Changed data for remaining days qty ..!");
                                }
                            }
                        }
                    }
                }
                console.log("scm Product wise map is : ",$scope.scmProductWiseMap);
                $scope.getFinalExpiryProductMap();
            }

            $scope.getFinalExpiryProductMap = function () {
                $scope.finalExpiryProductMap = {};
                $scope.originalExpiryProductMap = {};
                console.log("copy of expiry Total Stock  product is : ",$scope.copyOfTotalStock);
                angular.forEach($scope.copyOfTotalStock,function (expiryProduct,key) {
                    var totalExpiry = 0;
                    angular.forEach($scope.dataEntry,function (entry) {
                        if (expiryProduct[entry.date] != undefined && expiryProduct[entry.date] != null) {
                            totalExpiry = totalExpiry + expiryProduct[entry.date];
                        }
                    });
                    $scope.finalExpiryProductMap[key] = totalExpiry;
                });
                console.log("expiry product is : ",$scope.expiryProduct);
                angular.forEach($scope.expiryProduct,function (expiry) {
                    var total = 0;
                    console.log("expiry is : ",expiry);
                    angular.forEach($scope.dataEntry,function (entry) {
                        if (expiry[entry.date] != undefined && expiry[entry.date] != null) {
                            total = total + expiry[entry.date];
                        }
                    });
                    $scope.originalExpiryProductMap[expiry.productId] = total;
                });
                console.log("final expiry products are : ",$scope.finalExpiryProductMap);
                console.log("Original expiry products are : ",$scope.originalExpiryProductMap);
                $scope.getProductWiseStock();
            };

            $scope.getProductWiseStock = function () {
                $scope.productWiseStock = {};
                angular.forEach($scope.dayWiseExpiryProduct['inStock'],function (value,key) {
                    var total = 0;
                    angular.forEach($scope.dataEntry,function (entry) {
                        if (value[entry.date] != undefined && value[entry.date] != null) {
                            total = total + value[entry.date];
                        }
                    });
                    if ($scope.productWiseStock[key] != undefined && $scope.productWiseStock != null) {
                        $scope.productWiseStock[key].stockAtHand = $scope.productWiseStock[key].stockAtHand  + total;
                        $scope.productWiseStock[key].inTransit = $scope.productWiseStock[key].inTransit  + 0;
                    }
                    else {
                        $scope.productWiseStock[key] = {};
                        $scope.productWiseStock[key].stockAtHand = total;
                        $scope.productWiseStock[key].inTransit = 0;
                    }
                });

                angular.forEach($scope.dayWiseExpiryProduct['inTransit'],function (value,key) {
                    var total = 0;
                    angular.forEach($scope.dataEntry,function (entry) {
                        if (value[entry.date] != undefined && value[entry.date] != null) {
                            total = total + value[entry.date];
                        }
                    });
                    if ($scope.productWiseStock[key] != undefined && $scope.productWiseStock != null) {
                        $scope.productWiseStock[key].inTransit = $scope.productWiseStock[key].inTransit + total;
                        $scope.productWiseStock[key].stockAtHand = $scope.productWiseStock[key].stockAtHand + 0;
                    }
                    else {
                        $scope.productWiseStock[key] = {};
                        $scope.productWiseStock[key].inTransit = total;
                        $scope.productWiseStock[key].stockAtHand = 0;
                    }
                });
                console.log("Product wise stock of Stock at hand and intransit is : ",$scope.productWiseStock);
            };

            function getArray(dup,index) {
                var res = [];
                for (var i=0;i<dup.length;i++) {
                    if (i >= index) {
                        res.push(dup[i]);
                    }
                }
                return res;
            }

            function checkForExpiry(product,dayEntry) {
                for (var i=0;i<dayEntry.length;i++) {
                    var currentQty = parseFloat(product.orderingQuantity);
                    var expiryProduct = $scope.copyOfTotalStock[product.id];
                    if (expiryProduct != undefined && expiryProduct != null) {
                        var dayExpiry = expiryProduct[dayEntry[i]];
                        if (product.id == 100179) {
                            console.log("day expiry is : ",angular.copy(dayExpiry));
                        }
                        if (dayExpiry != undefined && dayExpiry != null) {
                            console.log("Before updating the expiry quantity is for product id: ",product.id,angular.copy(expiryProduct));
                            console.log("Date is and current qty is : ",dayEntry[i],angular.copy(currentQty));
                            if (currentQty > 0) {
                                if (currentQty >= dayExpiry) {
                                    currentQty = currentQty - dayExpiry;
                                    expiryProduct[dayEntry[i]] = 0;
                                    product.orderingQuantity = currentQty;
                                    console.log("After updating the expiry quantity is for product id: ",product.id,angular.copy(expiryProduct));
                                    console.log("After updating the expiry quantity total expiry is :  ",angular.copy($scope.copyOfTotalStock));
                                    if (currentQty == 0) {
                                        break;
                                    }
                                }
                                else {
                                    expiryProduct[dayEntry[i]] = dayExpiry - currentQty;
                                    currentQty = 0;
                                    product.orderingQuantity = currentQty;
                                    console.log("After updating the expiry quantity is for product id: ",product.id,angular.copy(expiryProduct));
                                    console.log("After updating the expiry quantity total expiry is :  ",angular.copy($scope.copyOfTotalStock));
                                    break;
                                }
                            }
                            else {
                                break;
                            }
                        }
                    }
                }
                return product.orderingQuantity;
            }

            function getCopyOfDayMenu(menuCategory,date,isSales) {
                for(var category in menuCategory){
                    for(var product in menuCategory[category].productList){
                        var qty = 0;
                        if (isSales) {
                            qty = menuCategory[category].productList[product].dateRemaining[date];
                        }
                        else {
                            qty = menuCategory[category].productList[product].dateOrderings[date];
                        }
                        if( qty != undefined && qty != null){
                            menuCategory[category].productList[product].quantity = qty;
                            menuCategory[category].productList[product].requestedQuantity = qty;
                        }
                        else {
                            menuCategory[category].productList[product].quantity = 0;
                            menuCategory[category].productList[product].requestedQuantity = 0;
                        }
                    }
                }
                return menuCategory;
            }

            function getBrandWiseMenuCategories(menuCategory){

                for(var category in menuCategory){
                    var productListCopy=[];
                    for(var product in menuCategory[category].productList){
                        if(menuCategory[category].productList[product].brandId== $scope.selectedBrandDetails.id){
                            console.log(menuCategory[category].productList[product].brandId + ":::::"+$scope.selectedBrandDetails.id);
                            productListCopy.push(menuCategory[category].productList[product]);
                        }
                    }
                    menuCategory[category].productList=productListCopy;
                }
                return menuCategory;
            }

            $scope.updateProductQuantity = function (product, variant) {
                var totalQuantity = 0;
                variant.orderedQuantity = parseInt(variant.orderedQuantity);
                product.variants.forEach(function (variant) {
                    totalQuantity += variant.orderedQuantity;
                });
                product.quantity = totalQuantity;
            }

            $scope.clearRequestOrder = function () {
                if (confirm("Are you sure?")) {
                    $scope.scmProductList = [];
                    $scope.comment = null;
                    $scope.menuCategories = recipeService.createMenuItemCategories();
                    $scope.showFulfillmentDateSelection = true;
                    $scope.showMenuItemList = false;
                    $scope.selectedCategories = [];
                    $scope.categoryBuffer = null;
                    $scope.hasCategoryBuffer = false;
                    $scope.dataEntry.forEach(function (data) {
                        data.brands.forEach(function (brand) {
                            brand.saleAmount = 0
                            brand.deliverySalePercentage=0
                        })
                    });

                }
            }
            $scope.resetMenuItem = function () {
                $scope.menuCategories = recipeService.createMenuItemCategories();
            }

            $scope.updateMenuProductQty = function (product) {
                product.requestedQuantity = parseInt(product.requestedQuantity);
            }

            $scope.createSCMProductList = function () {
                $scope.scmProductList = [];
                recipeService.getProductsFromCategoryListNewRegularOrdering($scope.unitData, $scope.menuCategories, $scope.copyOfTotalStock,
                    $scope.originalExpiryProductMap,$scope.finalExpiryProductMap,$scope.scmProductWiseMap,$scope.productWiseStock,function (itemList) {
                    var prods = [];
                    itemList.forEach(function (item) {
                        if (item.selectedFulfillmentType != "EXTERNAL" && item.selectedFulfillmentType != null) {
                            item.stock = item.stockAtHand + item.inTransit;
                            item.stockAtHand = item.stockAtHand == 0 ? item.stockAtHand : parseFloat(item.stockAtHand).toFixed(1);
                            item.inTransit = item.inTransit == 0 ? item.inTransit : parseFloat(item.inTransit).toFixed(1);
                            if (item.checkStockAtHand != undefined && item.checkStockAtHand != null) {
                                item.checkStock = item.checkStockAtHand + item.checkInTransit;
                                item.checkStockAtHand = item.checkStockAtHand == 0 ? item.checkStockAtHand : parseFloat(item.checkStockAtHand).toFixed(1);
                                item.checkInTransit = item.checkInTransit == 0 ? item.checkInTransit : parseFloat(item.checkInTransit).toFixed(1);
                            }
                            prods.push(item);
                        }
                    });
                    //prods = setMinOrderQuantity(prods);
                    $scope.scmProductList = prods;
                    console.log(prods);
                    return prods;
                });
                $scope.showMenuItemList = false;
            };

            $scope.hideWareHouseItems = function (check) {
                $scope.warehouseordering = check;
            };

            $scope.backToMenuItemList = function () {
                if (confirm("Are you sure?")) {
                    $scope.scmProductList = [];
                    $scope.showMenuItemList = true;
                    $scope.comment = null;
                }
            }

            $scope.updateOrderingQty = function (product) {
                if (product.critical) {
                    var qty = parseInt(product.packagingQuantity);
                    if (qty > product.duplicatePackagingQuantity) {
                        $toastService.create("Final Ordering Quantity can not be greater than Suggested for Critical Products..!");
                        product.packagingQuantity = product.duplicatePackagingQuantity;
                        product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                        return false;
                    }
                    else {
                        product.packagingQuantity = parseInt(product.packagingQuantity);
                        product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                    }
                }
                else {
                    product.packagingQuantity = parseInt(product.packagingQuantity);
                    product.orderingQuantity = product.packagingQuantity * product.conversionRatio;
                }
            }


            function validateFulfillment(reqOrder) {
                var returnList = [];
                for (var i in reqOrder.referenceOrderScmItems) {
                    if (appUtil.isEmptyObject(reqOrder.referenceOrderScmItems[i].fulfillmentType)) {
                        returnList.push(reqOrder.referenceOrderScmItems[i].productName);
                    }
                }
                return returnList;
            }

            $scope.dateformatting = function (startDate) {
                var year = new Date(startDate).getFullYear();
                var month = new Date(startDate).getMonth() + 1;
                var day = new Date(startDate).getDate();
                if (day >= 1 && day < 10)
                    day = '0' + day;
                if (month >= 1 && month < 10)
                    month = '0' + month;
                return year + "-" + month + "-" + day;
            }

            $scope.sendReferenceOrder = function (action) {
                var data = createRoObject(action);
                var validateOrder = validateFulfillment(data);
                if ($scope.fulfillmentDate == null) {
                    $toastService.create('Please fill fulfillment date!');
                    return false;
                } else if (data.referenceOrderScmItems.length == 0) {
                    $toastService.create('Please select at least a few products to order!');
                    return false;
                } else if (validateOrder.length > 0) {
                    $alertService.alert('Products found without fulfillment types', validateOrder.join(","), null, true);
                    return false;
                } else {
                    data.refOrderSource="FOUNTAIN9_DATA_SOURCE_ORDERING";
                    if (confirm("Are you sure you want to create the order?")) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.referenceOrderManagement.newReferenceOrder,
                            data: data
                        }).then(function success(response) {
                            console.log(response);
                            if (response.data != null && response.data.referenceOrderId > 0) {
                                $toastService.create("Reference order with id " + response.data.referenceOrderId + " created successfully!");
                                if (response.data.regularOrderEvents.length > 0) {
                                    $state.go("menu.refOrderCreateV1", {orderingEvents : response.data.regularOrderEvents});
                                }
                                else {
                                    $rootScope.orderingEvents = [];
                                    $state.go("menu.reqOrderMgt");
                                }
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    }
                }
            }

            function createRoObject(status) {
                return {
                    id: null,
                    generationTime: null,
                    initiationTime: null,
                    lastUpdateTime: null,
                    requestUnit: appUtil.createRequestUnit(),
                    generatedBy: appUtil.createGeneratedBy(),
                    fulfillmentUnit: null,
                    fulfillmentDate: new Date($scope.fulfillmentDate),
                    status: status,
                    comment: $scope.comment,
                    referenceOrderMenuItems: getMenuItems(),
                    referenceOrderScmItems: getScmItems(),
                    numberOfDays:$scope.noOfDays,
                    raiseBy:$scope.raiseBy,
                    orderEvent: $scope.regularOrderingEvent,
                    refreshDate: $scope.refreshDate
                }
            }

            function getMenuItems() {
                var products = [];
                $scope.menuCategories.forEach(function (category) {
                    var productList = [];
                    category.productList.forEach(function (product) {
                        if (product.requestedQuantity > 0) {
                            var variantList = [];
                            product.variants.forEach(function (variant) {
                                if (variant.orderedQuantity > 0) {
                                    variantList.push({
                                        id: null,
                                        name: variant.name,
                                        conversionQuantity: variant.conversionQuantity,
                                        orderedQuantity: variant.orderedQuantity
                                    });
                                }
                            });
                            product.variants = variantList;
                            productList.push({
                                id: null,
                                productId: product.productId,
                                productName: product.productName,
                                dimension: product.dimension,
                                requestedQuantity: product.requestedQuantity,
                                requestedAbsoluteQuantity: product.requestedQuantity,
                                transferredQuantity: null,
                                receivedQuantity: null,
                                quantity: product.quantity,
                                dineInQuantity: product.dineInQuantity,
                                deliveryQuantity: product.deliveryQuantity,
                                takeawayQuantity: product.takeawayQuantity,
                                variants: product.variants
                            })
                        }
                    });
                    if (products.length == 0) {
                        products = productList;
                    } else {
                        products = products.concat(productList);
                    }
                });
                return products;
            }

            function getScmItems() {
                var scmProducts = [];
                $scope.scmProductList.forEach(function (product) {
                    if (product.orderingQuantity > 0) {
                        //console.log(product);
                        if ($scope.warehouseordering) {
                            scmProducts.push({
                                id: null,
                                productId: product.id,
                                productName: product.name,
                                suggestedQuantity: product.suggestedQuantity,
                                requestedQuantity: product.orderingQuantity,
                                requestedAbsoluteQuantity: product.orderingQuantity,
                                transferredQuantity: null,
                                receivedQuantity: null,
                                fulfillmentType: product.selectedFulfillmentType,
                                unitOfMeasure: product.unitOfMeasure
                            });
                        }
                        else {
                            if (product.selectedFulfillmentType != 'WAREHOUSE') {
                                scmProducts.push({
                                    id: null,
                                    productId: product.id,
                                    productName: product.name,
                                    suggestedQuantity: product.suggestedQuantity,
                                    requestedQuantity: product.orderingQuantity,
                                    requestedAbsoluteQuantity: product.orderingQuantity,
                                    transferredQuantity: null,
                                    receivedQuantity: null,
                                    fulfillmentType: product.selectedFulfillmentType,
                                    unitOfMeasure: product.unitOfMeasure
                                });
                            }
                        }
                    }
                });
                return scmProducts;
            }


            function weekDays() {
                var days = [
                    {id: 1, value: 'Sunday'},
                    {id: 2, value: 'Monday'},
                    {id: 3, value: 'Tuesday'},
                    {id: 4, value: 'Wednesday'},
                    {id: 5, value: 'Thursday'},
                    {id: 6, value: 'Friday'},
                    {id: 7, value: 'Saturday'}];
                return days;
            }

            function getDayOfWeek(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                console.log('Selected Date', date.getDay());
                return weekDays()[date.getDay()].value;
            }

            function getDaysOfWeek() {
                return "NA";
            }

            function getDayOfWeekFromStr(date) {
                if (angular.isUndefined(date)) {
                    return "";
                }
                return weekDays()[new Date(date).getDay()].value;
            }
            $scope.update = function (){
                $scope.raiseBy=!$scope.raiseBy;
            }

        }]).filter('toArray', function () {
    return function (obj, addKey) {
        if (!angular.isObject(obj)) return obj;
        if (addKey === false) {
            return Object.keys(obj).map(function (key) {
                return obj[key];
            });
        } else {
            return Object.keys(obj).map(function (key) {
                var value = obj[key];
                return angular.isObject(value) ?
                    Object.defineProperty(value, '$key', {enumerable: false, value: key}) :
                    {$key: key, $value: value};
            });
        }
    };
});
