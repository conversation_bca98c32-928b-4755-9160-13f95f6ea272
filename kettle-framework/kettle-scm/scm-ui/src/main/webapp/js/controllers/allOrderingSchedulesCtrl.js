'use strict';

angular.module('scmApp')
    .controller('allOrderingSchedulesCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService) {
            $scope.init = function () {
                $scope.allSchedules = [];
                $scope.days = ["MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY","SUNDAY"];
                $scope.gridOptions = {
                    enableGridMenu: true,
                    exporterExcelFilename: 'download.xlsx',
                    exporterExcelSheetName: 'Sheet1',
                    enableColumnMenus: true,
                    saveFocus: false,
                    enableRowSelection: false,
                    enableFiltering: true,
                    saveScroll: true,
                    enableSelectAll: false,
                    multiSelect: false,
                    enableColumnResizing: true,
                    exporterMenuPdf : false,
                    exporterMenuExcel : false
                };
                $scope.getAllOrderingSchedules();

            };

            $scope.gridColumns  = function () {
                return [{
                    field: 'unitName',
                    name: 'Unit Name',
                    enableCellEdit: false,
                    displayName: 'unit Name',
                    width:200
                }, {
                    field: 'brand',
                    name: 'Brand',
                    enableCellEdit: false,
                    displayName: 'Brand',
                    width:200
                }, {
                    field: 'functional',
                    name: 'Functional',
                    enableCellEdit: false,
                    displayName: 'Functional',
                    width:120
                },{
                    field: 'monday',
                    name: 'Monday',
                    enableCellEdit: false,
                    displayName: 'Monday',
                    cellTemplate: 'deliveryScheduleMonday.html'
                },{
                    field: 'tuesday',
                    name: 'Tuesday(Functional,Ordering,Days)',
                    enableCellEdit: false,
                    displayName: 'Tuesday',
                    cellTemplate: 'deliveryScheduleTuesday.html'
                },{
                    field: 'wednesday',
                    name: 'Wednesday',
                    enableCellEdit: false,
                    displayName: 'Wednesday',
                    cellTemplate: 'deliveryScheduleWednesday.html'
                },{
                    field: 'thursday',
                    name: 'Thursday',
                    enableCellEdit: false,
                    displayName: 'Thursday',
                    cellTemplate: 'deliveryScheduleThursday.html'
                },{
                    field: 'friday',
                    name: 'Friday',
                    enableCellEdit: false,
                    displayName: 'Friday',
                    cellTemplate: 'deliveryScheduleFriday.html'
                },{
                    field: 'saturday',
                    name: 'Saturday',
                    enableCellEdit: false,
                    displayName: 'Saturday',
                    cellTemplate: 'deliveryScheduleSaturday.html'
                },{
                    field: 'sunday',
                    name: 'Sunday',
                    enableCellEdit: false,
                    displayName: 'Sunday',
                    cellTemplate: 'deliveryScheduleSunday.html'
                }
                ]
            }

            $scope.getAllOrderingSchedules = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.referenceOrderManagement.getAllOrderingSchedules,
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.allSchedules = response.data;
                        $scope.setGridItems($scope.allSchedules);
                        $scope.gridOptions.columnDefs = $scope.gridColumns();
                        $scope.gridOptions.data = $scope.gridItems;
                    }
                    else {
                        $toastService.create("No Schedules Found ..!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            function getOnOrOff(value) {
                if (value != undefined && value != null && value) {
                    return "ON";
                }
                return "OFF";
            }

            function getOrdering(value) {
                if (value != undefined && value != null && value) {
                    return "Y";
                }
                return "-";
            }

            function getYorN(value) {
                if (value != undefined && value != null && value) {
                    return "Y";
                }
                return "N";
            }

            function getOrderingDays(value) {
                if (value != undefined && value != null) {
                    return value;
                }
                return "-";
            }

            $scope.setGridItems = function (schedules){
                $scope.gridItems = [];
                for(var i = 0;i<schedules.length ; i++){
                    var unit = schedules[i];
                    var mapOfDays = {};
                    for (var j=0;j<unit.unitOrderSchedules.length;j++) {
                        mapOfDays[unit.unitOrderSchedules[j].orderingDayType] = unit.unitOrderSchedules[j];
                    }
                    var item = {
                        unitName:appUtil.findUnitDetail(unit.unitId).name,
                        brand:unit.brandId == 1 ? "Chaayos" : "Ghee n Turmeric",
                        functional: getYorN(unit.functional),

                        mondayFunctional : getOnOrOff(mapOfDays[$scope.days[0]].functional) ,
                        mondayOrdering : getOrdering(mapOfDays[$scope.days[(0 + 7 -3)%7]].orderingDay) ,
                        mondayDays : getOrderingDays(mapOfDays[$scope.days[(0 + 7 -3)%7]].orderingDays),

                        tuesdayFunctional : getOnOrOff(mapOfDays[$scope.days[1]].functional) ,
                        tuesdayOrdering : getOrdering(mapOfDays[$scope.days[(1 + 7 -3)%7]].orderingDay) ,
                        tuesdayDays : getOrderingDays(mapOfDays[$scope.days[(1 + 7 -3)%7]].orderingDays),

                        wednesdayFunctional : getOnOrOff(mapOfDays[$scope.days[2]].functional) ,
                        wednesdayOrdering : getOrdering(mapOfDays[$scope.days[(2 + 7 -3)%7]].orderingDay) ,
                        wednesdayDays : getOrderingDays(mapOfDays[$scope.days[(2 + 7 -3)%7]].orderingDays),

                        thursdayFunctional : getOnOrOff(mapOfDays[$scope.days[3]].functional) ,
                        thursdayOrdering : getOrdering(mapOfDays[$scope.days[(3 + 7 -3)%7]].orderingDay) ,
                        thursdayDays : getOrderingDays(mapOfDays[$scope.days[(3 + 7 -3)%7]].orderingDays),

                        fridayFunctional : getOnOrOff(mapOfDays[$scope.days[4]].functional) ,
                        fridayOrdering : getOrdering(mapOfDays[$scope.days[(4 + 7 -3)%7]].orderingDay) ,
                        fridayDays : getOrderingDays(mapOfDays[$scope.days[(4 + 7 -3)%7]].orderingDays),

                        saturdayFunctional : getOnOrOff(mapOfDays[$scope.days[5]].functional) ,
                        saturdayOrdering : getOrdering(mapOfDays[$scope.days[(5 + 7 -3)%7]].orderingDay) ,
                        saturdayDays : getOrderingDays(mapOfDays[$scope.days[(5 + 7 -3)%7]].orderingDays),

                        sundayFunctional : getOnOrOff(mapOfDays[$scope.days[6]].functional) ,
                        sundayOrdering : getOrdering(mapOfDays[$scope.days[(6 + 7 -3)%7]].orderingDay) ,
                        sundayDays : getOrderingDays(mapOfDays[$scope.days[(6 + 7 -3)%7]].orderingDays),
                    };
                    $scope.gridItems.push(item);
                }

            }
        }]);