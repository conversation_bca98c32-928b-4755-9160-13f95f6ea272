/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('adhocOrderCreateCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', '$state', 'metaDataService', '$stateParams', 'previewModalService', 'Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, $state, metaDataService, $stateParams, previewModalService, Popeye) {

            function addItem(selectedProduct, rqstQty, rqstAbsQty) {
                var found = false;
                var rqstQty = appUtil.isEmptyObject(rqstQty) ? 0 : rqstQty;
                var rqstAbsQty = appUtil.isEmptyObject(rqstAbsQty) ? 0 : rqstAbsQty;

                $scope.addedRoItems.forEach(function (roi) {
                    if (roi.productId == selectedProduct.productId) {
                        found = true;
                        $toastService.create("Product already added!");
                        return false;
                    }
                });

                if (!found) {
                    if (selectedProduct.productId == 100217) {
                        $scope.isManualBook[selectedProduct.productId] = true;
                    }
                    var obj = {
                        id: null,
                        productId: selectedProduct.productId,
                        productName: selectedProduct.productName,
                        requestedQuantity: rqstQty,
                        requestedAbsoluteQuantity: rqstAbsQty,
                        transferredQuantity: null,
                        receivedQuantity: null,
                        unitOfMeasure: selectedProduct.unitOfMeasure,
                        unitPrice: selectedProduct.unitPrice,
                        negotiatedUnitPrice: selectedProduct.negotiatedUnitPrice,
                        reason: null,
                        comment: null
                    };

                    var packagingDef = appUtil.getPackagingMap();
                    $scope.productPackaging[obj.productId].forEach(function (pack) {
                        if (pack.mappingStatus == 'ACTIVE' && pack.isDefault) {
                            obj.packagingName = packagingDef[pack.packagingId].packagingName;
                            obj.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                        }
                    });
                    if($scope.unitProductPackagingMap[obj.productId] !=null && $scope.unitProductPackagingMap[obj.productId]!= undefined) {
                        $scope.unitProductPackagingMap[obj.productId].forEach(function (pack) {
                            if($scope.receivingUnit.id == pack.fulfillmentUnitId) {
                                obj.packagingName = packagingDef[pack.packagingId].packagingName;
                                obj.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                            }
                        });
                    }
                    $scope.addedRoItems.unshift(obj);
                }
            }

            function addCloneItemsToRO(items) {
                $toastService.create("Adding cloned items to the Request Order! Please wait...");
                $scope.finalAvailableProducts = [];
                for(var i=0;i<$scope.scmProductDetails.length;i++){
                    var res=$scope.byProducts($scope.scmProductDetails[i]);
                    if(res){
                        $scope.finalAvailableProducts.push($scope.scmProductDetails[i]);
                    }
                }
                console.log("final available products are : ",$scope.finalAvailableProducts);
                var notInProds =[];
                var bool = false;
                for(var i in items){
                    for(var j=0;j<$scope.finalAvailableProducts.length;j++){
                        if(items[i].productId == $scope.finalAvailableProducts[j].productId){
                            addItem($scope.finalAvailableProducts[j], items[i].requestedAbsoluteQuantity, items[i].requestedAbsoluteQuantity);
                            bool=false;
                            break;
                        }
                        else{
                            bool = true;
                        }
                    }
                    if(bool){
                        notInProds.push(items[i]);
                    }
                }
                console.log("not there in list items are : ",notInProds);
                console.log("Till now added items are : ",$scope.addedRoItems);
            }

            function openBudgetExceedeModal(orderResponse) {
                var mappingModal = Popeye.openModal({
                    templateUrl: "budgetExceededModal.html",
                    controller: "budgetExceededModalCtrl",
                    resolve: {
                        orderResponse: function () {
                            return orderResponse;
                        }
                    },
                    click: false,
                    keyboard: false
                });
                mappingModal.closed.then(function () {});
            }


            $scope.init = function () {
                $scope.assetOrder = $stateParams.assetOrder;
                $scope.clonedItems = $stateParams.clonedItems;
                $scope.fulfilmentUnitName = $stateParams.fulfilmentUnit;
                $scope.availableProductList =[];
                //$scope.scmUnitList = appUtil.filterCurrentUnit(appUtil.getUnitList());
                $scope.getAvailableUnits();
                metaDataService.getScmProductDetails();
                metaDataService.getUnitData();
                metaDataService.getPackagingMap();
                $scope.currentUnit = appUtil.getUnitData();
                $scope.scmProductDetails = appUtil.getActiveScmOrderingProducts($scope.assetOrder);
                $scope.comment = null;
                $scope.minRefOrderFulFillmentDate = appUtil.getDate(0);
                $scope.maxRefOrderFulFillmentDate = appUtil.getDate(30);
                $scope.addedRoItems = [];
                $scope.selectedProduct = $scope.scmProductDetails[0];
                $scope.isManualBook = {};

                $scope.productPackaging = [];
                metaDataService.getAllPackagingMappings(function (packagingMap) {
                    $scope.productPackaging = packagingMap;
                },true);

                if (!appUtil.isEmptyObject($scope.clonedItems)) {
                    $toastService.create("Select a fulfillment unit first! Valid cloned items will be added after that");
                }
                $scope.showPreview = previewModalService.showPreview;
                $scope.reasons = ["SHORT_TREND","BULK_ORDER","PRODUCT_MISSING_IN_RO","EXCESS_ON_HAND_EXPIRY","WRONG_KETTLE_END_INVENTORY","OTHER"];
                $scope.selecetedReason = null;
                $scope.enterredComment = null;
                $scope.isFountain9Unit = false;
                $scope.checkFountain9Unit();
            };

            $scope.checkFountain9Unit = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.referenceOrderManagement.getFountain9Units,
                    params : {
                        "unitId" : appUtil.getCurrentUser().unitId,
                        "isForceLookUp" : false
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        if (response.data.indexOf(appUtil.getCurrentUser().unitId) != -1) {
                            $scope.isFountain9Unit = true;
                        }
                    } else {
                        $scope.isFountain9Unit = false;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                    $scope.isFountain9Unit = false;
                });
            };

            $scope.getAvailableUnits = function () {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableUnits,
                    params: {
                        unitId: appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.scmUnitList = [];
                        appUtil.getUnitList().map(function (unit) {
                            var i = 0;
                            for (i = 0; i < response.data.length; i++) {
                                if (unit.id == response.data[i]) {
                                    $scope.scmUnitList.push(unit);
                                }
                            }
                        });
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.selectCategory = function (selectedUnit) {
                if (selectedUnit != undefined) {
                    var result = confirm("Are you sure? All products will be cleared if you change the unit");
                    if (result) {
                        selectedUnit = JSON.parse(selectedUnit);
                        $scope.receivingUnit = selectedUnit;
                        $scope.scmProductDetails = appUtil.getActiveScmOrderingProducts($scope.assetOrder);
                        $scope.getUnitProductPackagingMappings(function () {
                            $scope.getAvailableProducts(selectedUnit);
                            $scope.category = selectedUnit.category;
                            $scope.selectedProduct = null;
                            $scope.addedRoItems = [];
                        });
                    }

                }
            };


            $scope.getUnitProductPackagingMappings = function (callback) {
                $http({
                    method: "GET",
                    url: apiJson.urls.productManagement.defaultPackagingMappings,
                    params: {
                        unitId : appUtil.getCurrentUser().unitId
                    }
                }).then(function success(response) {
                    if (response.data != null) {
                        $scope.unitProductPackagingMap = response.data;
                        callback();
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getAvailableProducts = function (selectedUnit) {
                $http({
                    method: "GET",
                    url: apiJson.urls.filter.availableProducts,
                    params: {
                        requestingUnit: appUtil.getCurrentUser().unitId,
                        fulfillmentUnit: selectedUnit.id
                    }
                }).then(function success(response) {
                    if (response.data != null && response.data.length > 0) {
                        $scope.availableProductList = response.data;
                        if(!appUtil.isEmptyObject($scope.clonedItems)){
                            addCloneItemsToRO($scope.clonedItems);
                        }
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };


            $scope.byProducts = function (product) {
                if ($scope.receivingUnit.category == "CAFE" && $scope.currentUnit.family == "CAFE") {
                    return product != null && product != undefined && product.interCafeTransfer == true && $scope.availableProductList != undefined && $scope.availableProductList != null
                        && $scope.availableProductList.indexOf(product.productId) > -1;
                } else {
                    return product != null && product != undefined && $scope.availableProductList != undefined && $scope.availableProductList != null
                        && $scope.availableProductList.indexOf(product.productId) > -1;
                }
            };

            $scope.changeAllReasons = function (reason) {
                $scope.selecetedReason = reason;
                for (var i = 0; i < $scope.addedRoItems.length; i++) {
                    $scope.addedRoItems[i].reason = reason;
                }
            };

            $scope.changeAllComments = function (comment) {
                $scope.enterredComment = comment;
                for (var i = 0; i < $scope.addedRoItems.length; i++) {
                    $scope.addedRoItems[i].comment = comment;
                }
            };

            function checkForComment() {
                if ((!$scope.isFountain9Unit && !$scope.assetOrder) || $scope.assetOrder) {
                    return true;
                }
                else {
                    for (var i = 0; i < $scope.addedRoItems.length; i++) {
                        if ($scope.addedRoItems[i].comment != null && $scope.addedRoItems[i].comment.length > 100) {
                            $toastService.create("Comment Should not exceed 100 characters for product : " + $scope.addedRoItems[i].productName);
                            return false;
                        }
                    }
                    return true;
                }
            }

            function checkForReason() {
                if ((!$scope.isFountain9Unit && !$scope.assetOrder) || $scope.assetOrder) {
                    return true;
                }
                else {
                    for (var i = 0; i < $scope.addedRoItems.length; i++) {
                        if (!appUtil.isEmptyObject($scope.addedRoItems[i].reason)) {
                            if ($scope.addedRoItems[i].reason == "OTHER") {
                                if ($scope.addedRoItems[i].comment == null) {
                                    $toastService.create("Please Enter minimum 3 letters in the comment for product : " + $scope.addedRoItems[i].productName);
                                    return false;
                                } else {
                                    if ($scope.addedRoItems[i].comment == "") {
                                        $toastService.create("Please Enter the comment for product : " + $scope.addedRoItems[i].productName);
                                        return false;
                                    }
                                    if ($scope.addedRoItems[i].comment.length < 3) {
                                        $toastService.create("Please Enter minimum 3 letters in the comment for product : " + $scope.addedRoItems[i].productName);
                                        return false;
                                    }
                                }
                            }
                        }
                        else {
                            $toastService.create("Please Select the reason for the product : " + $scope.addedRoItems[i].productName);
                            return false;
                        }
                    }
                    return true;
                }
            }

            $scope.createRoObject = function (budgetStatus) {
            	if (!appUtil.isEmptyObject($scope.clonedItems)) {
            		if(JSON.parse($scope.selectedUnit).name != $scope.fulfilmentUnitName){
                    $toastService.create("Fulfillment Unit selected is not correct.");
                    return false;
            		}
            	}
            	console.log($scope.fulfilmentUnitName );
                if ($scope.addedRoItems.length >= 250) {
                    $toastService.create("Please make sure there are not more than 249 items in the Request Order");
                    return false;
                } else if ($scope.selectedUnit == null) {
                    $toastService.create("Please select unit!");
                    return false;
                } else if ($scope.fulfillmentDate == null) {
                    $toastService.create("Please select fulfillment date!");
                    return false;
                } else if ($scope.assetOrder && ($scope.comment == undefined || $scope.comment == null || $scope.comment.trim() == '' || $scope.comment.length <= 20)) {
                    $toastService.create("Please specify an elaborated comment with more than 20 characters!");
                    return false;
                } else if (!checkForComment()) {
                    return false;
                } else if (!checkForReason()) {
                    return false;
                }
                else {
                    var ro = {
                        id: null,
                        generationTime: null,
                        lastUpdateTime: null,
                        requestUnit: appUtil.createRequestUnit(),
                        generatedBy: appUtil.createGeneratedBy(),
                        specialOrder: false,
                        fulfillmentUnit: {
                            id: JSON.parse($scope.selectedUnit).id,
                            code: "",
                            name: JSON.parse($scope.selectedUnit).name
                        },
                        fulfillmentDate: new Date($scope.fulfillmentDate),
                        searchTag: $scope.searchTag,
                        referenceOrderId: null,
                        status: 'CREATED',
                        comment: $scope.comment,
                        purchaseOrderId: null,
                        transferOrderId: null,
                        goodsReceivedId: null,
                        requestOrderItems: $scope.addedRoItems,
                        assetOrder: $scope.assetOrder,
                        applyBudget: budgetStatus,
                        budgetReason: (budgetStatus ? null : 'FORCE_SUBMIT')
                    };
                    if (ro.requestOrderItems.length == 0) {
                        $toastService.create("Please add at least a few items!");
                        return false;
                    } else if ($scope.invalidQuantity(ro.requestOrderItems)) {
                        $toastService.create("Requested item quantity cannot be 0 or less!");
                        return false;
                    } else {
                        $http({
                            method: "POST",
                            url: apiJson.urls.requestOrderManagement.requestOrder,
                            data: ro
                        }).then(function success(response) {
                            if (response.data != null && response.data.length > 0) {
                                if (response.data[0].budgetExceeded) {
                                    openBudgetExceedeModal(response.data);
                                    //$toastService.create("You have exceeded your budget!");
                                } else {
                                    $toastService.create("Request order with id " + response.data[0].orderId + " created successfully!");
                                    $state.go("menu.reqOrderMgt");
                                }
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    }
                }
            };


            $scope.removeItem = function (index) {
                $scope.addedRoItems.splice(index, 1);
                if ($scope.addedRoItems.length == 0) {
                    $scope.selecetedReason = null;
                    $scope.enterredComment = null;
                }
            };

            $scope.invalidQuantity = function (items) {
                var returnVal = false;
                items.forEach(function (val) {
                    if (val.requestedQuantity == null || angular.isUndefined(val.requestedQuantity) || val.requestedQuantity <= 0) {
                        returnVal = true;
                    }
                });
                return returnVal;
            };

            $scope.addNewRoItem = function () {

                if ($scope.selectedUnit == null) {
                    $toastService.create("Please select unit!");
                    return false;
                }

                if ($scope.selectedProduct == null) {
                    $toastService.create("Select a different product");
                    return;
                }
                addItem(JSON.parse($scope.selectedProduct), 0, 0);
            };

            $scope.calculateRequestQty = function (roi) {

                if (roi.productId == 100217) {
                    roi.packagingQuantity = 1;
                }

                var unit = JSON.parse($scope.selectedUnit);
                if (unit.category == "WAREHOUSE" || unit.category == "KITCHEN") {
                    roi.packagingQuantity = parseInt(roi.packagingQuantity);
                }
                roi.requestedQuantity = roi.packagingQuantity * roi.conversionRatio;
                roi.requestedAbsoluteQuantity = roi.requestedQuantity;
            };

            function getRoItems() {
                var roi = {
                    id: null,
                    productId: null,
                    productName: null,
                    requestedQuantity: null,
                    requestedAbsoluteQuantity: null,
                    transferredQuantity: null,
                    receivedQuantity: null,
                    unitOfMeasure: null
                }
            }
        }]
    ).controller('budgetExceededModalCtrl', ['$scope', 'orderResponse', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye',
            function ($scope, orderResponse, appUtil, $toastService, apiJson, $http, Popeye) {
                $scope.orderResponse = orderResponse;
                //  	console.log("$scope.orderResponse",$scope.orderResponse);

                $scope.cancel = function () {
                    closeModal();
                };

                function closeModal() {
                    Popeye.closeCurrentModal();
                }

            }
        ]
    );