/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('createServicePRCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService','Popeye',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location,
                  $toastService, metaDataService, $fileUploadService, $alertService, Popeye) {

            $scope.init = function () {
            	$scope.srs = [];
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.selectView = true;
                $scope.itemDeviations = [];
                $scope.invoiceDeviations = [];
                $scope.currentUser = appUtil.getCurrentUser();

                metaDataService.getSCMMetaData(function (metadata) {
                    $scope.prRequestTypes = metadata.paymentRequestTypes.filter(function (prType) {
                        return prType.shortCode == "SR";
                    });
                    $scope.prRequestType = $scope.prRequestTypes == null ? null : $scope.prRequestTypes[0];
                    metadata.paymentDeviations.map(function (deviation) {
                        if (deviation.deviationType == "DEVIATION") {
                            if (deviation.deviationLevel == "INVOICE_ITEM") {
                                $scope.itemDeviations.push(deviation);
                            } else if (deviation.deviationLevel == "INVOICE") {
                                $scope.invoiceDeviations.push(deviation);
                            }
                        }
                    });
                }, true);

                metaDataService.getServiceVendors(function (vendors) {
                    $scope.vendorList = vendors;
                });

                metaDataService.getCompanyList(function (companies) {
                    $scope.companies = companies;
                });

                metaDataService.getStates("active",function(states){
                    $scope.locations = states;
                });
                $scope.filledPr = false;
            };

            $scope.selectCompany = function(selectedCompany){
              $scope.selectedCompany = selectedCompany;
            };

            $scope.selectLocation = function(selectedLocation){
                $scope.selectedLocation = selectedLocation;
            };

            $scope.findSrs = function () {
                $scope.showNoGR = false;
                var params = {};
                var flag = true;
                if (appUtil.isEmptyObject($scope.startDate)) {
                    $toastService.create("Please select start date");
                    flag = false;
                }
                if (appUtil.isEmptyObject($scope.endDate)) {
                    $toastService.create("Please select end date");
                    flag = false;
                }
                if (appUtil.isEmptyObject($scope.selectedVendor)) {
                    $toastService.create("Please select vendor");
                    flag = false;
                }
                // if(appUtil.isEmptyObject($scope.selectedLocation)){
                //     $toastService.create("Please select delivery location");
                //     flag = false;
                // }
                if(appUtil.isEmptyObject($scope.selectedCompany)){
                    $toastService.create("Please select company");
                    flag = false;
                }
                if (!appUtil.isEmptyObject($scope.selectedLocation) && $scope.selectedLocation !== undefined) {
                    params["stateId"] = $scope.selectedLocation.id;
                }
                else{
                    params["stateId"] = null;
                }

                if(flag) {
                    $scope.srs = [];
                    $http({
                        method: 'GET',
                        url: apiJson.urls.serviceReceivedManagement.findReceivingsForPayment,
                        params: {
                            startDate: $scope.startDate,
                            endDate: $scope.endDate,
                            vendorId: $scope.selectedVendor.id,
                            companyId: $scope.selectedCompany.id,
                            stateId: params.stateId,
                            userId: $scope.currentUser.userId
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.srs = response.data;
                        } else {
                            $scope.showNoGR = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            };

            $scope.backToSelectView = function () {
                $scope.selectView = true;
                $scope.paymentRequest = null;
                $scope.uploadedDocData = null;
                $scope.filledPr = false;
            };

            $scope.createPR = function () {
                $scope.uploadedDocData = null;
                $scope.selectView = false;
            };

            $scope.prLocation = function(selectedPrLocation){
                $scope.selectPrLocation = selectedPrLocation;
            }

            $scope.setAvailableDeviations = function (item, type) {
                $scope.availableInvoiceDevs = [];
                $scope.availableItemDevs = [];
                var devList = [], availableList = [];
                if (type === "INVOICE") {
                    devList = angular.copy($scope.invoiceDeviations);
                } else {
                    devList = angular.copy($scope.itemDeviations);
                }
                devList.map(function (iDev) {
                    var found = false;
                    if(!appUtil.isEmptyObject(item.deviations)){
                        item.deviations.map(function (dev) {
                            if (iDev.paymentDeviationId === dev.paymentDeviation.paymentDeviationId) {
                                found = true;
                            }
                        });
                    }

                    if (!found) {
                        availableList.push({data: iDev});
                    }
                });
                if (type === "INVOICE") {
                    $scope.availableInvoiceDevs = availableList;
                } else {
                    $scope.selectedItemForDeviation = item;
                    $scope.availableItemDevs = availableList;
                }
            };

            $scope.addDeviations = function (item, type) {
                var devList;
                if(appUtil.isEmptyObject(item.deviations)){
                    item.deviations = [];
                }

                if (type === "INVOICE") {
                    devList = angular.copy($scope.availableInvoiceDevs);
                } else {
                    devList = angular.copy($scope.availableItemDevs);
                }
                devList.map(function (dev) {
                    if (dev.checked) {
                        item.deviations.push({
                            mappingId: null,
                            paymentDeviation: dev.data,
                            deviationRemark: dev.remark,
                            currentStatus: "CREATED",
                            createdBy: appUtil.createGeneratedBy()
                        });
                    }
                });
            };

            $scope.removeDeviation = function (list, index) {
                list.splice(index, 1);
            };

            $scope.showDeviationInput = function (item) {
                item.showDeviationInput = true;
            };

            $scope.updatePRQty = function(item){
            	if(item.receivedQuantity < item.invoiceQuantity){
                    $toastService.create("Invoice Quantity cannot be greater than received quantity");
                    item.invoiceQuantity = parseInt(item.invoiceQuantity.toString().slice(0, -1));
                    return;
                }
            	var taxPercent = 0;
                for(var x = 0; x < item.taxes.length ; x++ ){
                        taxPercent = taxPercent + item.taxes[x].percentage;
                }
            	var totalCost = getTotalPrice(item);
            	var taxPrice = getTaxPrice(totalCost,taxPercent);
            	var totalAmount = getTotalAmount(totalCost,taxPrice);
            	item.totalCost = totalCost;
            	item.totalTax = taxPrice;
                item.totalAmount = totalAmount;
                $scope.calculateTotal();
            }

            $scope.fillPrAmount = function () {
                if (!$scope.filledPr) {
                    $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.forEach(function (item) {
                        item.invoiceAmount = parseFloat((item.receivedQuantity*item.unitPrice).toFixed(6));
                        $scope.updatePRAmount(item);
                    });
                    $scope.filledPr = true;
                }
                else {
                    $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.forEach(function (item) {
                        item.invoiceAmount = 0;
                        $scope.updatePRAmount(item);
                    });
                    $scope.filledPr = false;
                }
            };


            $scope.updatePRAmount = function(item){
                item.invoiceQuantity = roundToSixDecimal(item.invoiceAmount / item.unitPrice);
                if(item.receivedQuantity < item.invoiceQuantity){
                    $toastService.create("Invoice Quantity cannot be greater than received quantity");
                    item.invoiceAmount = parseInt(item.invoiceAmount.toString().slice(0, -1));
                    item.invoiceQuantity = item.invoiceAmount / parseInt(item.unitPrice);
                    return;
                }
                var taxPercent = 0;
                for(var x = 0; x < item.taxes.length ; x++ ){
                    taxPercent = taxPercent + item.taxes[x].percentage;
                }
                var remainingQuantity=item.receivedQuantity-item.invoiceQuantity;
                var totalCost = getTotalPrice(item);
                var taxPrice = getTaxPrice(totalCost,taxPercent);
                var remainingTotalAmount=remainingQuantity*item.unitPrice;
                var remainingTaxAmount=getTaxPrice(remainingTotalAmount,taxPercent);
                var totalAmount = getTotalAmount(totalCost,taxPrice);
                item.totalCost = totalCost;
                item.totalTax = taxPrice;
                item.totalAmount = totalAmount;
                item.remainingTotalAmount=roundToSixDecimal(remainingTotalAmount);
                item.remainingTaxAmount=remainingTaxAmount;
                $scope.calculateTotal();
            }

            $scope.calculateTotal = function(){
                $scope.paidAmount = 0;
				for (var i = 0; i < $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.length; i++) {
					if ($scope.paymentRequest.paymentInvoice.paymentInvoiceItems[i].invoiceQuantity != undefined
						&& $scope.paymentRequest.paymentInvoice.paymentInvoiceItems[i].invoiceQuantity != null) {
                		$scope.paidAmount = parseFloat($scope.paidAmount) + parseFloat($scope.paymentRequest.paymentInvoice.paymentInvoiceItems[i].totalAmount);
                		$scope.paymentRequest.paymentInvoice.paymentAmount = $scope.paidAmount;
                		$scope.paymentRequest.paymentInvoice.invoiceAmount = $scope.paidAmount;
                		$scope.paymentRequest.proposedAmount = $scope.paidAmount;
                		$scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount = $scope.paidAmount;
                	}
                }
            }

            function getTotalPrice(item) {
                var cost = parseFloat(item.invoiceQuantity * item.unitPrice);
                return cost.toFixed(2);
            }

            function getTaxPrice(receivedCost,taxRate) {
                return ((receivedCost*taxRate)/100).toFixed(2);
            }

            function getTotalAmount(totalCost,totalTax){
				return (parseFloat(totalCost) + parseFloat(totalTax)).toFixed(2);
			}

            $scope.submitPaymentRequest = function (forceCreated) {
            	var check = true;
            	$scope.paymentRequest.paymentInvoice.paymentInvoiceItems.map(function (item) {
            		if(item.invoiceQuantity == null || item.invoiceQuantity == 0){
                        check = false;
                     }
            	});
            	if(!check){
                    $toastService.create("Invoice Quantity cannot be empty.");
                    return;
                 }
                if(appUtil.isEmptyObject($scope.selectPrLocation)){
                    $toastService.create("Please select a state before creating Payment Request!");
                    return;
                }
                if(appUtil.isEmptyObject($scope.paymentRequest.paymentInvoice.invoiceDate)){
                    $toastService.create("Please select a valid invoice date before creating Payment Request!");
                    return;
                }
                if(appUtil.isEmptyObject($scope.paymentRequest.paymentInvoice.invoiceNumber)){
                    $toastService.create("Please enter a valid invoice number before creating Payment Request!");
                    return;
                }
                if(appUtil.isEmptyObject($scope.uploadedDocData)){
                    $toastService.create("Please upload invoice first before creating Payment Request!");
                    return;
                }
                var totalAmount = 0;
                $scope.paymentRequest.deviationCount = $scope.paymentRequest.paymentInvoice.deviations.length;
                $scope.paymentRequest.paymentInvoice.invoiceDocumentHandle = $scope.uploadedDocData.documentId;
                $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.map(function (item) {
                    if(!appUtil.isEmptyObject(item.deviations)){
                    	$scope.paymentRequest.deviationCount += item.deviations.length;
                    }
                    item.skuId = item.costElementId;
                    item.skuName = item.costElementName;
                    item.hsn = item.ascCode;
                    item.skuDate = item.costElementDate;
                    item.toSkuDate = item.costElementToDate;
                    item.uom = item.unitOfMeasure;
                    item.quantity = item.invoiceQuantity;
                    item.conversionRatio = 1;
                    item.totalPrice = item.totalCost;
                    item.packagingName = item.unitOfMeasure;
                    item.serviceReceivedId = item.serviceReceivedId;
                    item.serviceReceivedItemId = item.id;
                    totalAmount = (parseFloat(totalAmount) + parseFloat(item.totalAmount)).toFixed(2);
                    item.taxes = item.taxes.map(function(tax){
                        return {
                            taxType: tax.taxName,
                            taxPercentage: tax.percentage,
                            taxValue: tax.value
                        };
                    });
                });
                $scope.paymentRequest.invoiceNumber  = $scope.paymentRequest.paymentInvoice.invoiceNumber;
                $scope.paymentRequest.grDocType = 'INVOICE';
                $scope.paymentRequest.paymentInvoice.paymentAmount = totalAmount;
                $scope.paymentRequest.paymentInvoice.invoiceAmount = totalAmount;
                $scope.paymentRequest.paidAmount = totalAmount;
                $scope.paymentRequest.proposedAmount = totalAmount;
                $scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount = totalAmount;
                $scope.paymentRequest.state = $scope.selectPrLocation,
                $scope.paymentRequest.forceCreate = forceCreated;
                $http({
                    url: apiJson.urls.serviceReceivedManagement.createPaymentRequest,
                    method: 'POST',
                    data: $scope.paymentRequest,
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response != null) {
                        $alertService.alert("Payment Request creation successful",
                            "Payment request created successfully with request id " + response.paymentRequestId, function () {
                                $scope.init();
                            });
                    } else {
                        $toastService.create("Payment Request creation failed.");
                    }
                    $scope.selectPrLocation = null;
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $alertService.alert("Payment Request creation failed", response.errorMsg, function () {
                    }, true)
                });
            };

            $scope.initiatePR = function () {
                var selectedSRs = angular.copy($scope.srs).filter(function (sr) {
                    return sr.checked;
                });
                if(selectedSRs.length==0){
                    $toastService.create("Please select at leat 1 Receiving to go raise payment request!!");
                    return;
                }
                createPaymentRequestObject(selectedSRs);
                $scope.selectView = false;
            };

            $scope.downloadPRInvoice = function (prInvoice) {
                metaDataService.downloadDocument(prInvoice);
            };

            $scope.previewPRInvoice = function(prInvoice){
                if(!appUtil.isEmptyObject(prInvoice.documentLink)){
                    $http({
                        method:"POST",
                        url:apiJson.urls.vendorManagement.downloadDocument,
                        data: prInvoice,
                        responseType: 'arraybuffer'
                    }).then(function(response){
                        var arrayBufferView = new Uint8Array( response.data );
                        var blob = new Blob( [ arrayBufferView ], { type: appUtil.mimeTypes[prInvoice.mimeType] } );
                        var urlCreator = window.URL || window.webkitURL;
                        var imageUrl = urlCreator.createObjectURL( blob );
                        var preview = document.getElementById("invoicePreview");
                        preview.innerHTML = "";
                        var img = new Image();
                        img.src = imageUrl;
                        preview.appendChild(img);
                    },function(error){
                        $toastService.create("Could not download the document... Please try again");
                    });
                }else{
                    $toastService.create("Not a valid document... Please check");
                }
            };

            /////////////////////document upload methods/////////////////////////////////

            $scope.resetScanModal = function () {
                $scope.imagesScanned = [];
                document.getElementById('images').innerHTML = "";
                var canvas = document.createElement('canvas');
                canvas.id = "scaleCanvas";
                document.getElementById('images').appendChild(canvas);
                $scope.uploadedDocData = null;
            };

            $scope.resetSnapModal = function () {
                $scope.snapRunning = false;
                if ($scope.localstream != null) {
                    $scope.localstream.getTracks()[0].stop();
                }
                $scope.uploadedDocData = null;
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                context.clearRect(0, 0, 640, 480);
            };

            $scope.startSnap = function () {
                var video = document.getElementById('video');
                // Get access to the camera!
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    // Not adding `{ audio: true }` since we only want video now
                    navigator.mediaDevices.getUserMedia({video: true}).then(function (stream) {
                        video.src = window.URL.createObjectURL(stream);
                        $scope.localstream = stream;
                        video.play();
                    });
                }
                $scope.snapRunning = true;
            };

            $scope.snapPicture = function () {
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                var video = document.getElementById('video');
                context.drawImage(video, 0, 0, 640, 480);
                video.pause();
                video.src = "";
                $scope.localstream.getTracks()[0].stop();
                $scope.snapRunning = false;
            };

            function dataURItoBlob(dataURI) {
                var byteString = atob(dataURI.split(',')[1]);
                var ab = new ArrayBuffer(byteString.length);
                var ia = new Uint8Array(ab);
                for (var i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                }
                return new Blob([ab], {type: 'image/png'});
            }

            $scope.uploadFile = function () {
                var canvas = document.getElementById('canvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', $scope.currentUser.userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            $scope.uploadDoc = function () {
                $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    var fileLimit = fileExt.toLowerCase() == 'png' ?1024000  : 5120000;
                    if(file.size > fileLimit){
                        var msg = ""
                        if(fileExt.toLowerCase() == 'png'){
                            msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
                        }
                        $toastService.create('File size should not be greater than ' + fileLimit/1024000 +  ' MB.' + msg);
                        return;
                    }

                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', $scope.currentUser.userId);
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.uploadedDocData = response;
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if(response.errorCode == 702){
                                $alertService.alert(response.errorTitle, response.errorMsg,true)
                            }else{
                                $toastService.create("Upload failed");
                            }
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                });
            };

            $scope.scanToPng = function () {
                scanner.scan($scope.displayImagesOnPage,
                    {
                        "output_settings": [
                            {
                                "type": "return-base64",
                                "format": "png"
                            }
                        ]
                    }
                );
            };

            $scope.displayImagesOnPage = function (successful, mesg, response) {
                if (!successful) { // On error
                    console.error('Failed: ' + mesg);
                    return;
                }
                if (successful && mesg != null && mesg.toLowerCase().indexOf('user cancel') >= 0) { // User cancelled.
                    console.info('User cancelled');
                    return;
                }
                var scannedImages = scanner.getScannedImages(response, true, false); // returns an array of ScannedImage
                $scope.imagesScanned = [];
                $scope.processScannedImage(scannedImages[0]);
            };

            $scope.processScannedImage = function (scannedImage) {
                $scope.imagesScanned.push(scannedImage);
                scaleImage(scannedImage.src);
            };

            function scaleImage(src) {
                var MAX_WIDTH = 1000;
                var image = new Image();
                var canvas = document.getElementById("scaleCanvas");
                image.onload = function () {
                    //var canvas = document.getElementById("scaleCanvas");
                    if (image.width > MAX_WIDTH) {
                        image.height *= MAX_WIDTH / image.width;
                        image.width = MAX_WIDTH;
                    }
                    var ctx = canvas.getContext("2d");
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    canvas.width = image.width;
                    canvas.height = image.height;
                    ctx.drawImage(image, 0, 0, image.width, image.height);
                };
                image.src = src;
            }

            $scope.uploadScannedFile = function () {
                var canvas = document.getElementById('scaleCanvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', $scope.currentUser.userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            ///////////////////utility functions/////////////////////////////

            function addNewInvoiceItem(invoiceItems, item) {
                var invoiceItem = {
                    skuId: item.costElementId,
                    skuName: item.costElementName,
                    hsn: item.ascCode,
                    uom: item.unitOfMeasure,
                    conversionRatio: 1,
                    quantity: item.receivedQuantity,
                    totalAmount: item.totalAmount,
                    totalTax: item.totalTaxes,
                    totalPrice: item.totalCost,
                    unitPrice: item.unitPrice,
                    taxes: [],
                    deviations: [],
                    showDeviationInput: true
                };
                item.taxes.map(function (tax) {
                    invoiceItem.taxes.push({
                        taxDetailId: null,
                        taxType: tax.taxCategory,
                        taxPercentage: tax.percentage,
                        taxValue: tax.value
                    });
                });
                invoiceItems.push(invoiceItem);
            }

            function addToExistingItem(item, grItem) {
                item.quantity += grItem.receivedQuantity;
                item.totalAmount += grItem.amountPaid;
                item.totalTax += grItem.totalTax;
                item.totalPrice += grItem.totalCost;
            }

            function getInvoiceItems(selectedSRs) {
                var items = [];
                for(var i in selectedSRs){
                    var rcvItems = selectedSRs[i].serviceReceiveItems;
                    Array.prototype.push.apply(items, rcvItems);
                }
                return items;
            }

            function getInvoiceAmount(selectedSRs) {
                var amount = 0;
                for(var i in selectedSRs){
                    amount += parseFloat(selectedSRs[i].totalAmount);
                }
                return amount;
            }

            function getTotalCost(selectedSRs){
                var totalCost = 0;
                for(var i in selectedSRs){
                    totalCost += (selectedSRs[i].totalAmount - selectedSRs[i].totalTaxes);
                }
                return totalCost;
            }

            function createPaymentRequestObject(selectedSRs) {
                var invoiceItems = getInvoiceItems(selectedSRs);
                console.log(invoiceItems)
                invoiceItems.forEach(function (item){
                    item.totalAmount = 0;
                    item.totalCost = 0;
                    item.totalTax = 0;
                    item.invoiceAmount = 0;
                    item.remainingTotalAmount=roundToSixDecimal(item.receivedQuantity*item.unitPrice);
                    item.remainingTaxAmount=getTaxPrice(item.remainingTotalAmount,item.taxRate);
                });
               var amount = calculateProposedAmount(invoiceItems);
                var invoice = {
                    invoiceNumber: null,
                    invoiceDate: null,
                    invoiceDocumentHandle: "",
                    paymentInvoiceItems: invoiceItems,
                    paymentAmount: 0,
                    invoiceAmount: 0,
                    calculatedInvoiceAmount: 0,
                    deviations: []
                };

                $scope.paymentRequest = {
                    type: $scope.prRequestType.code,
                    vendorId: {id: $scope.selectedVendor.id},
                    createdBy: appUtil.createGeneratedBy(),
                    paymentInvoice: invoice,
                    invoiceNumber : invoice.invoiceNumber,
                    proposedAmount: 0,
                    paidAmount: 0,
                    amountsMatch: false,
                    deviationCount: 0,
                    companyId :$scope.selectedCompany.id,
                    company: $scope.selectedCompany,
                    deliveryLocation: $scope.selectedLocation,
                    requestItemMappings: addGrItemMapping(selectedSRs),
                    grDocType : 'INVOICE'
                };
            }

            $scope.changeProposedAmount = function (tdsRate) {
                if (!appUtil.isEmptyObject(tdsRate)) {
                    var items = $scope.paymentRequest.paymentInvoice.paymentInvoiceItems;
                    $scope.paymentRequest.proposedAmount = calculateProposedAmount(items);
                }
            };
            function roundToSixDecimal(price) {
                return Number(price.toFixed(6));
            }
            function calculateProposedAmount(items) {
                var amount = 0;
                for(var i in items){
                    var item = items[i];
                    amount = parseFloat(amount) + parseFloat(parseFloat(item.totalCost)) + parseFloat(item.totalTax);
                }
                return amount.toFixed(2);
            }

            function getTds(totalCost, tdsRate) {
                var tds = appUtil.isEmptyObject(tdsRate) ? 0 : parseFloat(tdsRate);
                return parseFloat(parseFloat(totalCost) * (tds/100)).toFixed(2);
            }

            function addGrDataToInvoice(gr) {
                $scope.paymentRequest.proposedAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paidAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount += gr.billAmount;
                $scope.paymentRequest.paymentInvoice.extraCharges += gr.extraCharges;
                $scope.paymentRequest.paymentInvoice.invoiceAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paymentInvoice.paymentAmount += (gr.billAmount + gr.extraCharges);
            }

            function addGrItemMapping(selectedSRs) {
                var mappings = [];
                for(var i in selectedSRs){
                    mappings.push({
                        paymentRequestType: $scope.prRequestType.code,
                        paymentRequestItemId: selectedSRs[i].id
                    });
                }
                return mappings;
            }

            $scope.viewDetail = function(sr){
                var viewDetailModal = Popeye.openModal({
                    templateUrl: "viewSRDetail.html",
                    controller: "viewSRDetailCtrl",
                    resolve: {
                        sr: function(){
                            return sr;
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });
            };
        }
    ]
);
