/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('trOrderMgtCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService','previewModalService','$alertService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService,previewModalService,$alertService) {
            $scope.init = function () {
                $scope.today = appUtil.getDate(0);
                $scope.tomorrow = appUtil.getDate(1);
                var trOrderListObj = appUtil.getTrOrderListObj();
                $scope.scmOrderStatusList = [];
                $scope.scmOrderStatusList.push("");
                $scope.scmOrderStatusList = $scope.scmOrderStatusList.concat(appUtil.getMetadata().scmOrderStatus);
                $scope.status = trOrderListObj.status == null ? $scope.scmOrderStatusList[0] : trOrderListObj.status;
                $scope.transferOrderList = trOrderListObj.transferOrderList;
                $scope.transferOrderId = trOrderListObj.transferOrderId;
                $scope.startDate = trOrderListObj.startDate == null ? appUtil.formatDate(new Date(), "yyyy-MM-dd") : trOrderListObj.startDate;
                $scope.endDate = trOrderListObj.endDate == null ? appUtil.formatDate(new Date(), "yyyy-MM-dd") : trOrderListObj.endDate;
                $scope.unitList = [];
                $scope.unitList.push({
                    id: null,
                    name: ""
                });
                $scope.unitList = $scope.unitList.concat(appUtil.getUnitList());
                $scope.generatedForUnit = trOrderListObj.generatedForUnit == null ? $scope.unitList[0] : trOrderListObj.generatedForUnit;
                $scope.transferOrderList = trOrderListObj.transferOrderList;
                // $scope.findTransferOrders();
                if ($scope.transferOrderList.length == 0 || $scope.transferOrderList == null) {
                    $scope.findTransferOrders();
                } else {
                    $scope.getIncrementalTrOrders($scope.transferOrderList);
                }
                $scope.allSelected = trOrderListObj.allSelected;
                $scope.selectedTOs = trOrderListObj.selectedTOs;
            }



            $scope.findTransferOrders = function () {
                $scope.allSelected = false;
                $scope.selectedTOs = [];
                if ($scope.startDate == null || $scope.startDate.trim() == '' || $scope.endDate == null || $scope.endDate.trim() == '') {
                    $toastService.create("Please fill start date and end date properly!");
                    return false;
                } else if ($scope.startDate != null && $scope.endDate != null && $scope.startDate > $scope.endDate) {
                    $toastService.create("Please fill end date greater than or equal to start date properly!");
                    return false;
                } else {
                    $scope.enableDownload = (appUtil.getUnitData().family != "CAFE") && ($scope.startDate == $scope.endDate);
                    var url = apiJson.urls.transferOrderManagement.transferOrderFind + "?generationUnitId=" +
                        appUtil.getCurrentUser().unitId + "&startDate=" + $scope.startDate + "&endDate=" + $scope.endDate;
                    if ($scope.transferOrderId != null) {
                        url += "&transferOrderId=" + $scope.transferOrderId;
                    }
                    if ($scope.generatedForUnit.id != null) {
                        url += "&generatedForUnitId=" + $scope.generatedForUnit.id;
                    }
                    if ($scope.status != null && $scope.status != "") {
                        url += "&status=" + $scope.status;
                    }
                    $http({
                        method: "GET",
                        url: url
                    }).then(function success(response) {
                        $scope.transferOrderList = response.data;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            };

            $scope.getIncrementalTrOrders = function (transferOrderList) {
                var ids = [];
                for (var order in transferOrderList) {
                    ids.push(transferOrderList[order]['id']);
                }
                var stringIds = ids.join();
                var requestBody = {ids: stringIds};
                if ($scope.startDate == null || $scope.startDate.trim() == '' || $scope.endDate == null || $scope.endDate.trim() == '') {
                    $toastService.create("Please fill start date and end date properly!");
                    return false;
                } else if ($scope.startDate != null && $scope.endDate != null && $scope.startDate > $scope.endDate) {
                    $toastService.create("Please fill end date greater than or equal to start date properly!");
                    return false;
                } else {
                    $scope.enableDownload = (appUtil.getUnitData().family != "CAFE") && ($scope.startDate == $scope.endDate);
                    var url = apiJson.urls.transferOrderManagement.incrementalTransferOrder + "?generationUnitId=" +
                        appUtil.getCurrentUser().unitId + "&startDate=" + $scope.startDate + "&endDate=" + $scope.endDate;
                    if ($scope.transferOrderId != null) {
                        url += "&transferOrderId=" + $scope.transferOrderId;
                    }
                    if ($scope.generatedForUnit.id != null) {
                        url += "&generatedForUnitId=" + $scope.generatedForUnit.id;
                    }
                    if ($scope.status != null && $scope.status != "") {
                        url += "&status=" + $scope.status;
                    }
                    url += "&ids=" + stringIds;
                    $http({
                        method: "GET",
                        url: url
                    }).then(function success(response) {
                        for (var order in response.data) {
                            $scope.transferOrderList.push(response.data[order]);
                        }
                        }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            }

            $scope.downloadTorqusTOs = function () {
                if (!appUtil.checkEmpty($scope.startDate) && !appUtil.checkEmpty(appUtil.getCurrentUser())) {
                    $http({
                        url: apiJson.urls.transferOrderManagement.getTorqusTO,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        data: {
                            unitId: appUtil.getCurrentUser().unitId,
                            businessDate: $scope.startDate
                        },
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    }).success(function (data) {
                        if (appUtil.checkEmpty(data)) {
                            var fileName = "Torqus_Transfer_Order_Sheet_" + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss") + ".xls";
                            var blob = new Blob([data], {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                            saveAs(blob, fileName);
                        } else {
                            $toastService.create("Could not fetch Torqus View. Please try again later");
                        }
                    }).error(function (err) {
                        console.log("Error during getting data", err);
                    });
                }

            };

            $scope.rowClick = function (toId){
                if($scope.enableDownload && $scope.transferOrderList.length > 0){
                    return;
                }
                $scope.openTrOrderAction(toId);
            }

            $scope.checkAll = function (){
                $scope.selectedTOs = [];
                for(var i = 0;i<$scope.transferOrderList.length;i++){
                    $scope.transferOrderList[i].checked = !$scope.allSelected;
                    if(!$scope.allSelected){
                        $scope.selectedTOs.push($scope.transferOrderList[i].id);
                    }
                }
                $scope.allSelected = !$scope.allSelected;
            }

            $scope.selectTO = function (toId){
                var index = $scope.selectedTOs.indexOf(toId);
                if(index == -1){
                    $scope.selectedTOs.push(toId);
                }else{
                    $scope.selectedTOs.splice(index,1);
                    $scope.allSelected = false;
                }
            }

            $scope.openTrOrderAction = function (toId) {
                $rootScope.selectedTrOrderId = toId;
                $rootScope.previousScreen = "/menu/trOrderMgt";
                appUtil.setTrOrderListObj({
                    startDate: $scope.startDate,
                    endDate: $scope.endDate,
                    transferOrderId: $scope.transferOrderId,
                    generatedForUnit: $scope.generatedForUnit,
                    status: $scope.status,
                    transferOrderList: $scope.transferOrderList,
                    selectedTOs : $scope.selectedTOs,
                    allSelected : $scope.allSelected
                });
                $location.path("/menu/trOrderAction");
            }

            $scope.getTOsView = function (){
                var selectedDate =  appUtil.getFormattedDate($scope.startDate);
                $http({
                    url: apiJson.urls.transferOrderManagement.getTransferOrdersView,
                    method: 'POST',
                    responseType: 'arraybuffer',
                    data: $scope.selectedTOs,
                    params : {
                        isBulkEvent : false
                    },
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                })
                    .success(
                        function (data) {
                            var fileName =  "TRANSFER-ORDERS-"
                                + selectedDate
                                + ".xlsx";
                            var blob = new Blob(
                                [data],
                                {
                                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                }, fileName);
                            saveAs(blob, fileName);
                        }).error(function (err) {
                    console.log("Error during getting data", err);
                });

            }



            $scope.getDataAndPrint = function (toId) {
                $scope.selectedTrOrderId = toId;
                if (!angular.isUndefined($scope.selectedTrOrderId) && $scope.selectedTrOrderId != null) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.transferOrderManagement.transferOrder + "?transferOrderId=" + $scope.selectedTrOrderId
                    }).then(function success(response) {
                        $scope.transferOrderDetail = response.data;
                        $scope.generatedForUnitData = appUtil.findUnitDetail($scope.transferOrderDetail.generatedForUnitId.id);
                        $scope.calculateTotalPrice();
                        //$scope.ewayBillNumber = $scope.getewayBill();
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                    $window.print();
                }
            }

            $scope.printPage = function (toId) {
                $rootScope.selectedTrOrderId = toId;
                appUtil.setTrOrderListObj({
                    startDate: $scope.startDate,
                    endDate: $scope.endDate,
                    transferOrderId: $scope.transferOrderId,
                    generatedForUnit: $scope.generatedForUnit,
                    status: $scope.status,
                    transferOrderList: $scope.transferOrderList
                });
                $scope.unitData = appUtil.getUnitData();
                //$scope.scmProductDetails = appUtil.getScmProductDetails();
                $scope.transferOrderDetail = null;
                $scope.ewayBillNumber = null;
                $scope.unitList = appUtil.getUnitList();
                $scope.isCafe = appUtil.isCafe();
                $scope.formatDate = appUtil.formatDate;
                $scope.today = new Date();
                $scope.generatedForUnitData = null;
                $scope.totalPrice = null;
                $scope.totalPriceInWords = '';
                $scope.showPreview = previewModalService.showPreview;
                $scope.companyMap = appUtil.getCompanyMap();
                $scope.getTrOrderDetail();
            }

            $scope.backToTrOrderMgt = function(){
                $location.path("/menu/trOrderMgt");
            };

            $scope.acknowledge = function(tr){
                $alertService.confirm("Are you sure?","You are going to acknowledge external transfer.", function(response){
                    if(response){
                        $http({
                            method: "POST",
                            url: apiJson.urls.transferOrderManagement.approveExternalTransfer,
                            data:{
                                id:$scope.selectedTrOrderId,
                                userId:appUtil.getCurrentUser().userId
                            }
                        }).then(function(response) {
                            if(!appUtil.isEmptyObject(response) && response.data){
                                $toastService.create("Successfully approved!");
                                $scope.transferOrderDetail.externalTransferDetail.status="ACKNOWLEDGED";
                            }
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    }
                });
            };

            $scope.getTrOrderDetail = function(){
             $scope.getewayBill();
            }

            $scope.getTrOrderDetailed = function(print){
                //$scope.getewayBill();
                if(!angular.isUndefined($scope.selectedTrOrderId) && $scope.selectedTrOrderId!=null){
                    $http({
                        method: "GET",
                        url: apiJson.urls.transferOrderManagement.transferOrder+"?transferOrderId="+$scope.selectedTrOrderId
                    }).then(function success(response) {
                        $scope.transferOrderDetail = response.data;
                        $scope.generatedForUnitData = appUtil.findUnitDetail($scope.transferOrderDetail.generatedForUnitId.id);
                        $scope.calculateTotalPrice();
                        if(print) {
                            setTimeout(function () {
                                window.print();
                            }, 500);
                        }

                        //$scope.ewayBillNumber = $scope.getewayBill();
                    }, function error(response) {
                        if(print) {
                            setTimeout(function () {
                                window.print();
                            }, 500);
                        }
                    });

                }
            };

            $scope.getewayBill = function(){
                if(!angular.isUndefined($scope.selectedTrOrderId) && $scope.selectedTrOrderId!=null){
                  $http({
                        method: "GET",
                        url: apiJson.urls.transferOrderManagement.getEwayBillNumber+"?transferOrderId="+$scope.selectedTrOrderId
                    }).then(function(response) {
                        $scope.ewayBillNumber = response.data;
                        $scope.getTrOrderDetailed(true);
                    }, function error(response) {
                        $scope.getTrOrderDetailed(true);
                    });
                }
            }

            $scope.cancelTransferOrder = function(){
                if(!angular.isUndefined($scope.selectedTrOrderId) && $scope.selectedTrOrderId!=null){
                    $http({
                        method: "PUT",
                        url: apiJson.urls.transferOrderManagement.cancelTransferOrder,
                        data:{
                            transferOrderId:$scope.selectedTrOrderId,
                            updatedBy:appUtil.createGeneratedBy().id
                        }
                    }).then(function success(response) {
                        if(response.data!=null && response.data==$scope.selectedTrOrderId){
                            $toastService.create("Transfer order with id "+response.data+" cancelled successfully!");
                            for(var i=0 ; i < $scope.transferOrderDetail.transferOrderItems.length; i++){
                                if($scope.transferOrderDetail.transferOrderItems[i].productId==100217){
                                    var url=apiJson.urls.manualBillBookManagement. cancelManualBillBookEntry;
                                    metaDataService.cancelManualBillBookEntry(url,$scope.selectedTrOrderId).then(function(data){
                                        if(data){
                                            $toastService.create("Manual bill book of Transfer order with id "+response.data+" removed successfully!");
                                        }else{
                                            $toastService.create("Something went wrong.Manual bill book of Transfer order with id "+response.data+
                                                " cannot be removed!");
                                        }
                                    });
                                }
                            }
                            $scope.backToTrOrderMgt();
                        }else{
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                        if (response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    });
                }
            }

            $scope.calculateTotalPrice = function () {
                var total = 0;
                $scope.availableTaxes = [];
                $scope.transferOrderDetail.transferOrderItems.forEach(function (item) {
                    total += (item.total + item.tax);
                    $scope.calculateTaxes(item);
                });
                $scope.totalPrice = Math.round(total);
                $scope.totalPriceInWords = appUtil.inWords(Math.round(total));
            }


            $scope.calculateTaxes = function (item) {

            $scope.calculateTaxes= function(item){
                item.gst = 0;
                item.gstPercentage = 0;
                item.other = 0;
                item.otherPercentage = 0;

                if (item.taxes == null || item.taxes.length == 0) {
                    return;
                }
                for (var i in item.taxes) {
                    if ($scope.availableTaxes.indexOf(item.taxes[i].code) < 0) {
                        $scope.availableTaxes.push(item.taxes[i].code);
                    }
                    if (item.taxes[i].type == 'GST') {
                        item.gst = item.gst + item.taxes[i].value;
                        item.gstPercentage = item.taxes[i].percentage;
                    } else {
                        item.other = item.other + item.taxes[i].value
                    }
                }
                if (item.total > 0) {
                    item.otherPercentage = item.other / item.total * 100;
                }
            }

                if(item.taxes == null || item.taxes.length == 0){
                    return;
                }
                for(var i in item.taxes){
                    if($scope.availableTaxes.indexOf(item.taxes[i].code) < 0){
                        $scope.availableTaxes.push(item.taxes[i].code);
                    }
                    if(item.taxes[i].type == 'GST'){
                        item.gst = item.gst + item.taxes[i].value ;
                        item.gstPercentage = item.taxes[i].percentage;
                    }else{
                        item.other = item.other + item.taxes[i].value
                    }
                }
                if(item.total > 0){
                    item.otherPercentage = item.other/item.total*100;
                }
            }
            $scope.getTorqusTO = function(toID) {
                $scope.selectedTrOrderId = toID;
                $scope.getTrOrderDetailed(false);
                $http({
                    url: apiJson.urls.transferOrderManagement.getTorqusTO+"/"+toID,
                    method: 'POST',
                    responseType: 'arraybuffer',
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(function(data){

                    var fileName = "Transfer_Order_Sheet_" + toID + "_" +
                        $scope.transferOrderDetail.generatedForUnitId.name+ "_"+ appUtil.formatDate(Date.now(),"dd-MM-yyyy-hh-mm-ss") + ".xlsx";
                    var blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    },fileName);
                    saveAs(blob, fileName);
                }).error(function(err){
                    console.log("Error during getting data",err);
                });
            }

            $scope.doSomething = function (){

            }
        }]);
