/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('refOrderActionCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location','previewModalService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, previewModalService) {

            $scope.init = function () {
                $scope.selectedRefOrderId = $rootScope.selectedRefOrderId;
                $scope.getRefOrderDetail();
                $scope.unitData = appUtil.getUnitData();
                $scope.referenceOrderDetail = null;
                $scope.unitList = appUtil.getUnitList();
                $scope.showPreview = previewModalService.showPreview;
            };

            $scope.backToRefOrderFind = function(){
                $location.path("/menu/refOrderFind");
            }

            $scope.getRefOrderDetail = function(){
                if(!angular.isUndefined($scope.selectedRefOrderId) && $scope.selectedRefOrderId!=null){
                    $http({
                        method: "GET",
                        url: apiJson.urls.referenceOrderManagement.referenceOrder+"?referenceOrderId="+$scope.selectedRefOrderId
                    }).then(function success(response) {
                        console.log(response.data);
                        $scope.referenceOrderDetail = response.data;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            }

        }]);
