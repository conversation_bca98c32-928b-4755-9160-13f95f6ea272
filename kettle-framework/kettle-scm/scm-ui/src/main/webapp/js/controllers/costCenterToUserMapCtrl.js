angular.module('scmApp').controller(
		'costCenterToUserMapCtrl',
		[
				'$rootScope',
				'$scope',
				'apiJson',
				'$http',
				'appUtil',
				'$toastService',
				'metaDataService',
				function($rootScope, $scope, apiJson, $http, appUtil,
						$toastService, metaDataService) {
					
					$scope.init = function () {
						console.log("running cost Map");
						$scope.getCostCenters();
						$scope.getEmployees();
					}
					
					$scope.getCostCenters = function () {
                        $http({
                            url: apiJson.urls.serviceOrderManagement.costCentersAll,
                            method: "GET",
                        }).then(function (response) {
                            $scope.allCostCenters = response.data;
                            console.log($scope.allCostElements);
                        }, function (response) {
                            console.log(response);
                        });
					};
	                
					$scope.searchEmployeeMapping = function (employee) {
                        $http({
                            url: apiJson.urls.serviceOrderManagement.costCenters,
                            method: "GET",
                            params : {empId: employee.id}
                        }).then(function (response) {
                            $scope.employeeMappings = response.data;
                            $scope.allCostElements = [];
                            var i= 0
                            for(i in $scope.employeeMappings){
                            	var c = $scope.employeeMappings[i];
                            	var j =0;
                            	if(c.elements.length > 0){
	                            	for(j in c.elements){
	                            		 var ce =  c.elements[j];
	                            		 ce.costCenter = c;
	                            		 $scope.allCostElements.push(ce);
	                            	}
                            	} else {
                            		var ce2 = {};
                            		ce2.costCenter = c;
                            		$scope.allCostElements.push(ce2);
                            	}
                            }
                        }, function (response) {
                            console.log(response);
                        });
					};
					
					$scope.getEmployees = function () {
	                    $http({
	                        method: "GET",
	                        url: apiJson.urls.users.activeUsers
	                    }).then(function success(response) {
	                        $scope.employeeList = response.data;
	                    }, function error(response) {
	                        console.log("error:" + response);
	                    });
	                };
	                
	                $scope.createEmployeeCostCenterMap = function() {
	                	 var payload = {
	                			 employee: $scope.selectedEmployee,
	                	 		 costCenter : $scope.selectedCostCenter
	                	 };
	                	 $http({
	                         url: apiJson.urls.serviceOrderManagement.createEmployeeCostCenterMap,
	                         method: 'POST',
	                         data: payload
	                     }).then(function (response) {
	                    	 if(response.errorMessage != null){
	                    		 $toastService.create("Mapping Failed!");
	                    	 } else {
		                         $toastService.create("Mapping Added!");
		                         $scope.clear();
		                         $scope.getCostCenters();
		                         $("#addNewMappingModal").closeModal();
		                         $timeout(function () {
		                             $('#selectCostCenter').val('').trigger('change');
		                             $('#employeeSelector').val('').trigger('change');
		                         });
	                    	 }
	                     }, function error(response) {
	                    	 $toastService.create("Mapping Added!");
	                         console.log("got error", response);
	                     });
	                };
	                
	                $scope.clear = function() {
	                	 $scope.costCenter = {};
	                	 $scope.allCostElements = [];
	                };
					
				} ]);