angular.module('scmApp').controller(
    'serviceOrderNewCtrl',
    ['$rootScope', '$scope', '$state', 'apiJson', '$http', 'appUtil', 'metaDataService', '$fileUploadService',
        '$toastService', '$alertService', 'Popeye', function ($rootScope, $scope, $state, apiJson, $http, appUtil, metaDataService,
                                                              $fileUploadService, $toastService, $alertService, Popeye) {

        $scope.init = function () {
            $scope.getCostCenters();
            $scope.locationList = [];
            $scope.serviceItems = [];
            $scope.defaultUnits = [];
            $scope.defaultBulkUnits = {};
            $scope.costElements = [];
            $scope.showExpandedView = false;
            $scope.soType = ["CAPEX", "OPEX"];
            $scope.serviceBccItems = null;
            $scope.srItems = [];
            $scope.selectedTagName = null;
            $scope.isDisabled = true;
            $scope.uploadedDocData = null;
            metaDataService.getCompanyList(function (companies) {
                $scope.companyList = companies;
            });
            metaDataService.getAllBusinessCostCenters(function (bcc) {
                $scope.units = bcc;
            });
            $scope.isRegular = "REGULAR_SO";
        }

        $scope.setCapexOpex = function () {
            if ($scope.selectedType != undefined || $scope.selectedType != null) {
                $scope.isRegular = "REGULAR_SO";
            }
        };

        $scope.getCostCenters = function () {
            var currentUser = appUtil.getCurrentUser();
            $http({
                url: apiJson.urls.serviceOrderManagement.costCenters,
                method: "GET",
                params: {
                    empId: currentUser.userId
                }
            }).then(function (response) {
                $scope.costCenters = response.data;
                $scope.costCentersSize = $scope.costCenters.length;
            }, function (response) {
                console.log(response);
            });
        };

        $scope.getVendorList = function (selectedCenter) {
            $scope.tagList = [];
            $scope.selectedLocation = null;
            $scope.costElements = [];
            $scope.selectedElement = null;
            $scope.isDisabled = true;
            $scope.srItems = [];
            $scope.vendorList = [];
            $scope.locationList = [];
            $(".selectedQuantity").val('');
            $(".selectedUnitPrice").val('');
            $scope.uom = null;
            $scope.selectedLocation = null;
            var currentUser = appUtil.getCurrentUser();
            $http({
                url: apiJson.urls.serviceOrderManagement.getMappedVendorList,
                method: "GET",
                params: {
                    costCenterId: selectedCenter.id
                }
            }).then(function (response) {
                $scope.vendorList = response.data;
                $scope.vendorListSize = $scope.vendorList.length;
            }, function (response) {
                console.log(response);
            });
        };

        $scope.selectVendorLoc = function (vendor) {
            $scope.tagList = [];
            $scope.isDisabled = true;
            $scope.selectedLocation = null;
            $scope.costElements = [];
            $scope.selectedElement = null;
            $scope.srItems = [];
            $(".selectedQuantity").val('');
            $(".selectedUnitPrice").val('');
            $scope.uom = null;
            $scope.serviceItems = [];
            $scope.selectedDispatchLocation = null;
            $scope.locationList = null;
            $scope.selectedVendor = vendor;
            metaDataService.getVendorLocations($scope.selectedVendor.vendorId, function (locations) {
                $scope.locationList = locations;
            });
            $scope.locationListSize = $scope.locationList.length;
        };

        $scope.getCostElementList = function (selectedLocation) {
            $scope.getTagNamesList();
            var currentUser = appUtil.getCurrentUser();
            $scope.selectedLocation = selectedLocation;
            $http({
                url: apiJson.urls.serviceOrderManagement.getMappedCostElements,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id,
                    vendorId: $scope.selectedVendor.vendorId
                }
            }).then(function (response) {
                $scope.costElements = response.data;
                $scope.costElementsSize = $scope.costElements.length;
            }, function (response) {
                console.log(response);
            });
        };

        $scope.getCloneTagList = function (tag) {
            $http({
                url: apiJson.urls.serviceOrderManagement.getTagList,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id,
                    tagName: tag
                }
            }).then(function (response) {
                $scope.tagList = response.data;
                $scope.tagListSize = $scope.tagList.length;
            }, function (response) {
                console.log(response);
            });
        }

        $scope.getTagNamesList = function () {
            $http({
                url: apiJson.urls.serviceOrderManagement.getTagNamesList,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id
                }
            }).then(function (response) {
                $scope.tagNamesList = response.data;
                $scope.tagNamesListSize = $scope.tagNamesList.length;
            }, function (response) {
                console.log(response);
            });
        };

        $scope.validateDate = function () {
            if ($(".costElementDate").val() == null || $(".costElementDate").val().trim() == '') {
                $toastService.create("Input Cost Element From Date ");
                return;
            }
            var fromDate = new Date($(".costElementDate").val());
            var toDate = new Date($(".costElementToDate").val());
            if (toDate < fromDate) {
                $toastService.create("To Date can not be less than From Date..!");
                $(".costElementToDate").val('');
                return false;
            }
        };

        $scope.setSoType = function (type) {
            $scope.isRegular = type;
            console.log("type is : ", $scope.isRegular);
            $scope.srItems = [];
            $scope.serviceItems = [];
            $scope.defaultBulkUnits = {};
            $scope.defaultUnits = {};
            $scope.receivings = [];
            $scope.selectedElement = null;
            $(".selectedUnitPrice").val('');
            $toastService.create("All the allocations and cost elements data is cleared..!");
        };

        $scope.downloadSampleBulkSO = function () {
            var soUnits = [];
            $scope.units.forEach(function (unit) {
                var obj = {
                    unitId : unit.code,
                    unitName : unit.name,
                    allocatedCost : 0
                };
                soUnits.push(obj);
            });

            $http({
                url: apiJson.urls.serviceOrderManagement.downloadSampleBilkSO,
                method: 'POST',
                data : soUnits,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            }).then(function success(response) {
                if (!appUtil.checkEmpty(response)) {
                    var fileName = "Sample_So"  + ".xlsx";
                    var blob = new Blob([response.data], {
                        type: 'c'
                    }, fileName);
                    saveAs(blob, fileName);
                } else {
                    $toastService.create("Could not generate Sample Bulk SO Sheet. Please try again later");
                }
            },function error(response){
                $toastService.create("Could not generate Sample Bulk SO Sheet... Please try again later");
            });
        };

        $scope.uploadBulkSo = function () {
            if ($scope.selectedElement == undefined || $scope.selectedElement == null) {
                $toastService.create("Please Select a Cost Element");
                return;
            }
            if ($scope.defaultBulkUnits[$scope.selectedElement.id] != undefined && $scope.defaultBulkUnits[$scope.selectedElement.id] != null) {
                $toastService.create("Selected Cost Element Allocations are already added..!");
                return;
            }
            if ($(".costElementDate").val() == null || $(".costElementDate").val().trim() == '') {
                $toastService.create("Input Cost Element From Date ");
                return;
            }
            if ($(".costElementToDate").val() == null || $(".costElementToDate").val().trim() == '') {
                $toastService.create("Input Cost Element To Date ");
                return;
            }
            if ($(".selectedUnitPrice").val() == null || $(".selectedUnitPrice").val().trim() == '' || isNaN($(".selectedUnitPrice").val())) {
                $toastService.create("Input Updated Price.");
                return;
            }
            $fileUploadService.openFileModal("Upload Bulk SO Sheet", "Find", function (file) {
                $scope.readUploadSOFile(file);
            });
        };

        $scope.readUploadSOFile = function (file) {
            $scope.listOfUnits = [];
            var fd = new FormData(document.forms[0]);
            fd.append("file", file);
            $http({
                url: apiJson.urls.serviceOrderManagement.readUploadedSo,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).then(function success(response) {
                if (response.data.length != 0){
                    console.log("got data ",response.data);
                    $scope.listOfUnits = response.data;
                    $scope.showUploadedSheet($scope.listOfUnits);
                }
                else{
                    $toastService.create("No units present in the uploaded sheet..!");
                }
            },function error(response) {
                if (response.data.errorMsg != null) {
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                } else {
                    $toastService.create("Error in uploaded sheet.");
                    console.log("response is ",response);
                }
            });
        };

        $scope.showUploadedSheet = function(units){
            var uploadedSheet = Popeye.openModal({
                ariaLabelledBy: 'modal-title',
                ariaDescribedBy: 'modal-body',
                templateUrl: 'uploadedSoSheet.html',
                controller: 'uploadedSoSheetCtrl',
                backdrop: 'static',
                keyboard: false,
                scope:$scope,
                size: 'lg',
                resolve: {
                    result : function () {
                        return units;
                    },
                    costElement : function () {
                        return $scope.selectedElement;
                    }
                }
            });
            uploadedSheet.closed.then(function (obj) {
                if (obj.isSuccessful) {
                    $scope.addItemBulk(units,obj.totalOfAllocatedCost);
                }
            });
        };

        $scope.createBulkDefaultBcc = function (units) {
          var result = {};
            $scope.units.forEach(function (unit) {
             units.forEach(function (defaultUnit) {
                if (defaultUnit.unitId == unit.code) {
                    var tempUnit = unit;
                    unit.allocatedCost = defaultUnit.allocatedCost;
                    result[unit.code] = unit;
                }
             });
          });
          return result;
        };

        $scope.addItemBulk = function (units,allocatedCost) {
            $scope.defaultBulkUnits[$scope.selectedElement.id] = $scope.createBulkDefaultBcc(units);
            var quantity = (allocatedCost/$scope.selectedCost.currentPrice);
            $scope.createItem(true,quantity,$scope.selectedCost.currentPrice,$scope.defaultBulkUnits[$scope.selectedElement.id]);
            $scope.receivings = $scope.calculateReceivings($scope.srItems);
        };

        $scope.selectCostElement = function (selectedElement) {
            $scope.isDisabled = true;
            $http({
                url: apiJson.urls.serviceOrderManagement.getVendorData,
                method: "GET",
                params: {
                    locationId: $scope.selectedLocation.id
                }
            }).then(function (response) {
                if (response.data) {
                    $scope.tax = selectedElement.taxRate;
                }
                else {
                    $scope.tax = 0;
                }
                $scope.selectedElement = selectedElement;
                $scope.uom = selectedElement.uom;
                if ($scope.selectedElement.isPriceUpdate == 'Yes') {
                    $scope.isDisabled = false;
                }
                $scope.getCostElementPrice($scope.selectedElement.id);
            }, function (response) {
                console.log(response);
            });
        }

        $scope.getCostElementPrice = function (costElementId) {
            $http({
                url: apiJson.urls.serviceOrderManagement.getSelectedCostElement,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id,
                    vendorId: $scope.selectedVendor.vendorId,
                    costElementId: costElementId
                }
            }).then(function (response) {
                $scope.selectedCost = response.data;
                //$scope.selectedUnitPrice = $scope.selectedCost.currentPrice;
                $(".selectedUnitPrice").val($scope.selectedCost.currentPrice);
            }, function (response) {
                console.log(response);
            });
        }

        $scope.removeItem = function (item, index, items) {
            Object.keys($scope.srItems).forEach(function (key) {
                if (key.startsWith(item.costElementId + "_")) {
                    delete $scope.srItems[key];
                }
            });
            items.splice(index, 1);
            if ($scope.defaultBulkUnits[item.costElementId] != undefined || $scope.defaultBulkUnits[item.costElementId] != null) {
                delete $scope.defaultBulkUnits[item.costElementId];
            }
            $scope.receivings = $scope.calculateReceivings($scope.srItems)
        };

        $scope.addItem = function (selectedElement) {
            if ($scope.selectedType == "CAPEX") {
                if ($scope.defaultUnits == undefined || $scope.defaultUnits.length == 0) {
                    $toastService.create("Please Select Default Allocations Before Adding cost Element");
                    return;
                }
            }
            if ($scope.selectedElement == undefined || $scope.selectedElement == null) {
                $toastService.create("Please Select Cost Element..!");
                return;
            }
            if ($(".selectedQuantity").val() == null || $(".selectedQuantity").val().trim() == '' || isNaN($(".selectedQuantity").val())) {
                $toastService.create("Input Quantity Data.");
                return;
            }
            if ($(".selectedUnitPrice").val() == null || $(".selectedUnitPrice").val().trim() == '' || isNaN($(".selectedUnitPrice").val())) {
                $toastService.create("Input Updated Price.");
                return;
            }
            if ($(".costElementDate").val() == null || $(".costElementDate").val().trim() == '') {
                $toastService.create("Input Cost Element From Date ");
                return;
            }
            if ($(".costElementToDate").val() == null || $(".costElementToDate").val().trim() == '') {
                $toastService.create("Input Cost Element To Date ");
                return;
            }
            for (var x = 0; x < $scope.serviceItems.length; x++) {
                if ($scope.serviceItems[x].costElementId == selectedElement.id) {
                    $toastService.create("Same Cost Element Cannot Be Added. Change Quantity In Existing Cost Element.");
                    return;
                }
            }
            $scope.createItem(false);
            /*var currentUser = appUtil.getCurrentUser();
            $http({
                url: apiJson.urls.serviceOrderManagement.getSelectedCostElement,
                method: "GET",
                params : {
                    costCenterId : $scope.selectedCenter.id,
                    vendorId : $scope.selectedVendor.vendorId,
                    costElementId : selectedElement.id
                }
            }).then(function (response) {
                $scope.selectedCost = response.data;
                $scope.createItem();
            }, function (response) {
                console.log(response);
            });*/
            $scope.receivings = $scope.calculateReceivings($scope.srItems)
        }

        $scope.setSelectedTagName = function(tagName){
            $scope.selectedTagName = tagName;
            $scope.getCloneTagList(tagName);

        };

        $scope.cloneSOWithTag = function(){
            console.log("data is :",$scope.tagList);
            $scope.selectTag($scope.tagList[0]);
        };

        $scope.selectTag = function (selectedTag) {
            $scope.serviceItems = [];
            $scope.costELementCloneList = [];
            var map = new Map();
            for (var i = 0; i < selectedTag.orderItems.length; i++) {
                for (var x = 0; x < $scope.costElements.length; x++) {
                    if ($scope.costElements[x].id == selectedTag.orderItems[i].costElementId) {
                        if (map.has(selectedTag.orderItems[i].costElementId)) {
                            var listData = map.get(selectedTag.orderItems[i].costElementId);
                            var reqObj = {
                                costElementId: selectedTag.orderItems[i].costElementId,
                                ascCode: $scope.costElements[x].code,
                                costElementName: $scope.costElements[x].name,
                                serviceDescription: $scope.costElements[x].description,
                                unitOfMeasure: $scope.costElements[x].uom,
                                currentPrice: selectedTag.orderItems[i].unitPrice,
                                taxRate: $scope.costElements[x].taxRate,
                                quantity: selectedTag.orderItems[i].requestedQuantity + listData.quantity,
                                deptName: $scope.costElements[x].department.name
                            };
                            map.delete(selectedTag.orderItems[i].costElementId);
                            map.set(selectedTag.orderItems[i].costElementId, reqObj);
                        }
                        else {
                            var reqObj = {
                                costElementId: selectedTag.orderItems[i].costElementId,
                                ascCode: $scope.costElements[x].code,
                                costElementName: $scope.costElements[x].name,
                                serviceDescription: $scope.costElements[x].description,
                                unitOfMeasure: $scope.costElements[x].uom,
                                currentPrice: selectedTag.orderItems[i].unitPrice,
                                taxRate: $scope.costElements[x].taxRate,
                                quantity: selectedTag.orderItems[i].requestedQuantity,
                                deptName: $scope.costElements[x].department.name
                            };
                            map.set(selectedTag.orderItems[i].costElementId, reqObj);
                        }
                    }
                }
            }
            angular.forEach(map, function (value, key) {
                // $scope.costELementCloneList.push(value);
                //$scope.createItemForClone(value);
                $scope.findPrice(value);
            });
        }

        $scope.findPrice = function (selectedElement) {
            $http({
                url: apiJson.urls.serviceOrderManagement.getSelectedCostElement,
                method: "GET",
                params: {
                    costCenterId: $scope.selectedCenter.id,
                    vendorId: $scope.selectedVendor.vendorId,
                    costElementId: selectedElement.costElementId
                }
            }).then(function (response) {
                selectedElement.currentPrice = response.data.currentPrice;
                $scope.createItemForClone(selectedElement);
            }, function (response) {
                console.log(response);
            });
        }

        $scope.createItemForClone = function (costElement) {
            var totalCost = $scope.getTotalCost(costElement.quantity, costElement.currentPrice);
            var totalTax = $scope.getTotalTax(totalCost, costElement.taxRate);
            var amountPaid = $scope.getTotalAmount(totalCost, totalTax);
            var item = {
                ascCode: costElement.ascCode,
                costElementId: costElement.costElementId,
                costElementName: costElement.costElementName,
                departmentName: costElement.deptName,
                serviceDescription: costElement.serviceDescription,
                requestedQuantity: costElement.quantity,
                unitOfMeasure: costElement.unitOfMeasure,
                costElementDate: costElement.costElementDate,
                costElementToDate: costElement.costElementToDate,
                unitPrice: costElement.currentPrice,
                totalCost: totalCost,
                totalTax: totalTax,
                amountPaid: amountPaid,
                taxRate: costElement.taxRate,
                type: $scope.selectedType,
                edit: false
            };
            $scope.serviceItems.push(item);
            $scope.createDefaultBccs($scope.defaultUnits);
        }

        $scope.createItem = function (isBulk,quantity,price,defaultUnits) {
            var totalcost = 0;
            if (isBulk) {
                totalCost = $scope.getTotalCost(quantity,price);
            }
            else {
                totalCost = $scope.getTotalCost($(".selectedQuantity").val(), $(".selectedUnitPrice").val());
            }
            var totalTax = $scope.getTotalTax(totalCost, $scope.tax);
            var amountPaid = $scope.getTotalAmount(totalCost, totalTax);
            var item = {
                ascCode: $scope.selectedElement.code,
                costElementId: $scope.selectedElement.id,
                costElementName: $scope.selectedElement.name,
                departmentName: $scope.selectedElement.department.name,
                serviceDescription: $scope.selectedElement.description,
                requestedQuantity: isBulk ? quantity : parseFloat($(".selectedQuantity").val()),
                unitOfMeasure: $scope.selectedElement.uom,
                costElementDate: $(".costElementDate").val(),
                costElementToDate:$(".costElementToDate").val(),
                unitPrice: isBulk ? price :$(".selectedUnitPrice").val(),
                totalCost: totalCost,
                totalTax: totalTax,
                amountPaid: amountPaid,
                taxRate: $scope.tax,
                type: $scope.selectedType,
                edit: false
            };
            $scope.serviceItems.unshift(item);
            $scope.costElements = $scope.costElements;
            $(".selectedQuantity").val('');
            $(".selectedUnitPrice").val('');
            if (isBulk) {
                $scope.createDefaultBccItem(defaultUnits, item,true);
            }
            else {
                $scope.createDefaultBccItem($scope.defaultUnits, item,false);
            }
        };

        $scope.createDefaultBccItem = function (defaultUnits, item, isBulk) {
            var keys = Object.keys(defaultUnits);
            var len = keys.length;

            for (var key in defaultUnits) {
                if (appUtil.isEmptyObject($scope.srItems)) {
                    $scope.srItems = {};
                }
                if (isBulk) {
                    console.log("Allocated Cost is : ",defaultUnits[key].allocatedCost);
                    var quantity = defaultUnits[key].allocatedCost/item.unitPrice;
                    console.log("Quanity is : ",quantity);
                    defaultUnits[key].qty = quantity;
                }
                else {
                    var singleQuantity = (parseFloat(item.requestedQuantity) / len).toFixed(5);
                    console.log("checking quantity at 341: " + singleQuantity)
                    var singlePrice = parseFloat(parseFloat(item.totalCost) / len).toFixed(2);
                    defaultUnits[key].allocatedCost = singlePrice;
                    defaultUnits[key].qty = singleQuantity;
                }

                var qty = parseFloat(parseFloat(defaultUnits[key].allocatedCost) / parseFloat(item.unitPrice)).toFixed(5);
                console.log("checking quantity at 348: " + qty)
                var tax = parseFloat(parseFloat(defaultUnits[key].allocatedCost) * (parseFloat(item.taxRate) / 100)).toFixed(2);
                var keys = item.costElementId + "_" + defaultUnits[key].id;
                var obj = prepareObj(defaultUnits[key], item, qty, tax);
                if (appUtil.isEmptyObject($scope.srItems[keys])) {
                    $scope.srItems[keys] = {};
                }
                // add item specific to SO item and to that specific unit
                $scope.srItems[keys] = obj;
            }
        }

        $scope.editItem = function (id) {
            $scope.serviceItems[id].edit = true;
        };

        $scope.updateItem = function (item, id) {
            //var tax = "18";
            var totalCost = $scope.getTotalCost(item.requestedQuantity, item.unitPrice);
            var totalTax = $scope.getTotalTax(totalCost, item.taxRate);
            var amountPaid = $scope.getTotalAmount(totalCost, totalTax);
            $scope.serviceItems[id] = {
                ascCode: item.ascCode,
                costElementId: item.costElementId,
                costElementName: item.costElementName,
                departmentName: item.departmentName,
                serviceDescription: item.serviceDescription,
                requestedQuantity: item.requestedQuantity,
                unitOfMeasure: item.unitOfMeasure,
                costElementDate: item.costElementDate,
                costElementToDate: item.costElementToDate,
                unitPrice: item.unitPrice,
                totalCost: totalCost,
                totalTax: totalTax,
                amountPaid: amountPaid,
                taxRate: item.taxRate,
                type: item.type,
                edit: false
            };
            if (!appUtil.isEmptyObject($scope.srItems)) {
                for (var key in $scope.defaultUnits) {
                    var srIt = $scope.srItems[$scope.serviceItems[id].costElementId + '_' + $scope.defaultUnits[key].id];
                    srIt.requestedQuantity = item.requestedQuantity;
                    srIt.totalCost = parseFloat(totalCost);
                    srIt.totalTax = parseFloat(totalTax);
                    srIt.totalAmount = parseFloat(amountPaid);
                }
            }
            $scope.receivings = $scope.calculateReceivings($scope.srItems)
        };

        $scope.getTotalAmount = function (totalCost, totalTax) {
            return (parseFloat(totalCost) + parseFloat(totalTax)).toFixed(2);
            ;
        }

        $scope.getTotalCost = function (selectedQuantity, price) {
            return (selectedQuantity * price).toFixed(2);
            ;
        }

        $scope.getTotalTax = function (price, tax) {
            return ((price * tax) / 100).toFixed(2);
        }


        $scope.showAllocations = function () {
            if (appUtil.isEmptyObject($scope.selectedType)) {
                $toastService.create("Please select Type for Service Order Before Assigining Allocations");
                return;
            }
            var allocateCostModal = Popeye.openModal({
                templateUrl: "allocateCostModal.html",
                controller: "allocateCostModalCtrl",
                resolve: {

                    units: function () {
                        return angular.copy($scope.units);
                    },
                    companies: function () {
                        return angular.copy($scope.companyList);
                    },
                    type: function () {
                        return angular.copy($scope.selectedType);
                    }
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });

            allocateCostModal.closed.then(function (units) {
                $scope.defaultUnits = units;
                $scope.createDefaultBccs($scope.defaultUnits);
            });
        }

        $scope.createDefaultBccs = function (defaultUnits) {
            if ($scope.serviceItems != null) {
                var keys = Object.keys(defaultUnits);
                var len = keys.length;

                for (var key in defaultUnits) {
                    for (var i = 0; i < $scope.serviceItems.length; i++) {
                        var item = angular.copy($scope.serviceItems[i]);
                        if (appUtil.isEmptyObject($scope.srItems)) {
                            $scope.srItems = {};
                        }
                        var singleQuantity = (parseFloat(item.requestedQuantity) / len).toFixed(5);
                        console.log("checking quantity at 454: "+singleQuantity)
                        var singlePrice = parseFloat(parseFloat(item.totalCost) / len).toFixed(2);
                        defaultUnits[key].allocatedCost = singlePrice;
                        defaultUnits[key].qty = singleQuantity;



                        var qty = parseFloat(parseFloat(defaultUnits[key].allocatedCost) / parseFloat(item.unitPrice)).toFixed(5);
                        console.log("checking quantity at 462: "+qty)
                        var tax = parseFloat(parseFloat(defaultUnits[key].allocatedCost) * (parseFloat(item.taxRate) / 100)).toFixed(2);
                        var keys = item.costElementId + "_" + defaultUnits[key].id;
                        var obj = prepareObj(defaultUnits[key], item, qty, tax);
                        if (appUtil.isEmptyObject($scope.srItems[keys])) {
                            $scope.srItems[keys] = {};
                        }
                        // add item specific to SO item and to that specific unit
                        $scope.srItems[keys] = obj;

                    }
                }
            }
        }

        function prepareObj(bcc, item, qty, tax) {
            return {
                businessCostCenterId: bcc.id,
                businessCostCenterName: bcc.name,
                costElementId: item.costElementId,
                costElementName: item.costElementName,
                departmentName: item.departmentName,
                costElementDate: item.costElementDate,
                costElementToDate: item.costElementToDate,
                company: bcc.company,
                location: bcc.location,
                state: bcc.state,
                requestedQuantity: parseFloat(qty),
                ascCode: item.ascCode,
                serviceDescription: item.serviceDescription,
                unitOfMeasure: item.unitOfMeasure,
                unitPrice: item.unitPrice,
                totalCost: parseFloat(bcc.allocatedCost),
                totalAmount: parseFloat(parseFloat(bcc.allocatedCost) + parseFloat(tax)),
                taxRate: parseFloat(item.taxRate),
                totalTax: parseFloat(tax),
                type: item.type,
                unitId: bcc.code
            };
        }


        $scope.viewAllocatedUnits = function (index, itemSelected) {
            $scope.item = $scope.serviceItems[index];
            if ($scope.isRegular == "REGULAR_SO") {
                if ($scope.defaultUnits == undefined || $scope.defaultUnits.length == 0) {
                    $toastService.create("Please Select Default Allocations.");
                    return;
                }
            }
            var allocateCostModal = Popeye.openModal({
                templateUrl: "allocatedCostModal.html",
                controller: "allocatedCostModalCtrl",
                resolve: {

                    allUnits: function () {
                        return angular.copy($scope.units);
                    },
                    selectedUnits: function () {
                        return $scope.isRegular =="REGULAR_SO" ? angular.copy($scope.defaultUnits) : angular.copy($scope.defaultBulkUnits[itemSelected.costElementId]);
                    },
                    item: function () {
                        return angular.copy($scope.item);
                    },
                    srItems: function () {
                        return angular.copy($scope.srItems);
                    },
                    companies: function () {
                        return angular.copy($scope.companyList);
                    },
                    costElementId: function () {
                        return angular.copy(itemSelected.costElementId);
                    }
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });

            allocateCostModal.closed.then(function (allItems) {
                if (!appUtil.isEmptyObject(allItems)) {
                    $scope.srItems = angular.copy(allItems);
                    $scope.receivings = $scope.calculateReceivings($scope.srItems);
                }
            });
        };


        $scope.nextPage = function () {
            if (appUtil.isEmptyObject($scope.srItems)) {
                $toastService.create("Please assign price to bcc's!");
                return;
            }
            if (appUtil.isEmptyObject($scope.selectedType)) {
                $toastService.create("Please select Type for Service Order");
                return;
            }
            $scope.showExpandedView = true;
            $scope.srItemsDup = angular.copy($scope.srItems);
            $scope.receivings = $scope.calculateReceivings($scope.srItems);
        }

        $scope.calculateReceivings = function (srItems) {
            $scope.bccList = [];
            var receivings = {};
            for (var key in srItems) {
                addToMap(receivings, srItems[key]);
            }
            angular.forEach(receivings, function (value, k) {
                var totalCost = 0;
                var totalAmount = 0;
                var totalTax = 0;
                for (var x = 0; x < value.items.length; x++) {
                    totalCost = parseFloat(totalCost) + parseFloat(value.items[x].totalCost);
                    totalAmount = parseFloat(totalAmount) + parseFloat(value.items[x].totalAmount);
                    totalTax = parseFloat(totalTax) + parseFloat(value.items[x].totalTax);
                }
                value.totalCost = totalCost;
                value.totalAmount = totalAmount;
                value.totalTax = totalTax;
                value.tagName = '';
                value.tag = true;

            });
            return receivings;
        }

        $scope.getSummaryData = function (receivings) {
            var amount = 0;
            angular.forEach(receivings, function (value, key) {
                for (var x = 0; x < value.length; x++) {
                    amount += value[x].totalCost;
                }
            });
        }

        function addToMap(map, obj) {
            var key = obj.company.id + "_" + obj.state.id;
            if (appUtil.isEmptyObject(map[key])) {
                map[key] = {
                    company: obj.company,
                    location: obj.location,
                    state: obj.state,
                    items: []
                };
            }
            map[key].items.push(obj);
            $scope.bccList.push(obj);
        }

        $scope.removeItemFromRcv = function (index, rcv, key) {
            var dup = rcv.items[index];
            console.log("duplicate item is : ",dup);
            for (var j in $scope.srItemsDup) {
                var item = $scope.srItemsDup[j];
                if (JSON.stringify(dup) == JSON.stringify(item)) {
                    delete $scope.srItemsDup[j];
                    break;
                }
            }
            rcv.items.splice(index, 1);
            if (rcv.items.length == 0) {
                delete $scope.receivings[key];
            }
            if (Object.keys($scope.receivings).length == 0) {
                $scope.receivings = undefined;
            }
            rcv.totalCost = 0;
            rcv.totalAmount = 0;
            rcv.totalTax = 0;
            for (var x = 0; x < rcv.items.length; x++) {
                rcv.totalCost = parseFloat(rcv.totalCost) + parseFloat(rcv.items[x].totalCost);
                rcv.totalAmount = parseFloat(rcv.totalAmount) + parseFloat(rcv.items[x].totalAmount);
                rcv.totalTax = parseFloat(rcv.totalTax) + parseFloat(rcv.items[x].totalTax);
            }
            $scope.receivings = $scope.calculateReceivings($scope.srItemsDup);
        };

        $scope.submitSo = function (receivings) {
            console.log("items are : ",$scope.receivings);
            var enteredTagName = null;
            for(var receiving in $scope.receivings){
                console.log(receiving);
                console.log($scope.receivings[receiving]);
                console.log("tag is : ",$scope.receivings[receiving].tagName);
                enteredTagName = $scope.receivings[receiving].tagName;
            }
            if(enteredTagName !=null && enteredTagName != "") {
                if ($scope.tagNamesList.includes(enteredTagName)) {
                    $toastService.create("Tag name already exists in database. Please enter a new tag name!")
                    return false;
                }
            }
            $scope.getDepartmentSOModal();
        };

        $scope.getDepartmentSOModal = function () {
            var modalInstance = Popeye.openModal({
                templateUrl: 'departmentSOModal.html',
                controller: 'departmentSOModalCtrl',
                size: 'lg',
                windowClass: 'my-modal-popup',
                resolve: {
                    receivingList: function () {
                        return $scope.bccList;
                    },
                    type: function () {
                        return angular.copy($scope.selectedType);
                    }
                },
                click: true,
                keyboard: false
            });

            modalInstance.closed.then(function (obj) {
                if (!obj.isClosed) {
                    $scope.budgetCheck = obj.checked;
                    $scope.submitData();
                }
            });
        };

        $scope.submitData = function () {
            if ($scope.budgetCheck == undefined && $scope.selectedType == "CAPEX") {
                return;
            }
            if ($scope.budgetCheck && $scope.selectedType == "CAPEX") {
                $toastService.create("Cannot Create Service Order. Budget Exceeded!!!");
                return;
            }
            $alertService.confirm("Are you sure on saving Service Order?", "", function (result) {
                if (result) {
                    sendRequestForSO();
                }
            });
        };

        function prepareRcvngs(receivings) {
            var allRcvngs = [];
            if (!appUtil.isEmptyObject(receivings)) {
                for (var key in receivings) {
                    var rcv = receivings[key];
                    allRcvngs.push({
                        dispatchLocationId: $scope.selectedLocation.id,
                        vendorId: $scope.selectedVendor.vendorId,
                        costCenterId: $scope.selectedCenter.id,
                        type: $scope.selectedType,
                        userId: appUtil.getCurrentUser().userId,
                        items: rcv.items,
                        tagName: rcv.tagName
                    });
                }
            }
            return allRcvngs;
        }

        function sendRequestForSO() {
            var reqObj = prepareRcvngs($scope.receivings);
            console.log("Doc data is : ",$scope.uploadedDocData);

            if (reqObj.length > 0) {
                $http({
                    method: "POST",
                    url: apiJson.urls.serviceOrderManagement.createServiceOrder,
                    params:{
                        "documentId":$scope.uploadedDocData == null ? null : $scope.uploadedDocData.documentId
                    },
                    data: reqObj
                }).then(function (response) {
                    if (response.data != null) {
                        $scope.init();
                        $alertService.alert("Congratulations!!",
                            "Service Order with ID: <b>" + response.data + "</b> created successfully! <br>",
                            function () {
                                $state.go("menu.viewSO", {createdSO: response.data, viewSO: true});
                            }
                        );
                    } else {
                        $toastService.create("Service Order could not be created due to some error!!");
                    }
                }, function (error) {
                    console.log(error);
                    $toastService.create("Service Order could not be created due to some error!!");
                });
            }
        }

        $scope.goBack = function () {
            $scope.showExpandedView = false;
            /* $scope.receivings = {};
             $scope.srItems = [];*/
        };

        $scope.addTag = function (r) {
            r.tag = false;
        }

        /////////////////////document upload methods/////////////////////////////////

        $scope.downloadSoDocument = function(doc){
            metaDataService.downloadDocument(doc);
        };

        $scope.resetScanModal = function () {
            $scope.imagesScanned = [];
            document.getElementById('images').innerHTML = "";
            var canvas = document.createElement('canvas');
            canvas.id = "scaleCanvas";
            document.getElementById('images').appendChild(canvas);
            $scope.uploadedDocData = null;
        };

        $scope.resetSnapModal = function () {
            $scope.snapRunning = false;
            if ($scope.localstream != null) {
                $scope.localstream.getTracks()[0].stop();
            }
            $scope.uploadedDocData = null;
            var canvas = document.getElementById('canvas');
            var context = canvas.getContext('2d');
            context.clearRect(0, 0, 640, 480);
        };

        $scope.startSnap = function () {
            var video = document.getElementById('video');
            // Get access to the camera!
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                // Not adding `{ audio: true }` since we only want video now
                navigator.mediaDevices.getUserMedia({video: true}).then(function (stream) {
                    video.src = window.URL.createObjectURL(stream);
                    $scope.localstream = stream;
                    video.play();
                });
            }
            $scope.snapRunning = true;
        };

        $scope.snapPicture = function () {
            var canvas = document.getElementById('canvas');
            var context = canvas.getContext('2d');
            var video = document.getElementById('video');
            context.drawImage(video, 0, 0, 640, 480);
            video.pause();
            video.src = "";
            $scope.localstream.getTracks()[0].stop();
            $scope.snapRunning = false;
        };

        function dataURItoBlob(dataURI) {
            var byteString = atob(dataURI.split(',')[1]);
            var ab = new ArrayBuffer(byteString.length);
            var ia = new Uint8Array(ab);
            for (var i = 0; i < byteString.length; i++) {
                ia[i] = byteString.charCodeAt(i);
            }
            return new Blob([ab], {type: 'image/png'});
        }

        $scope.uploadFile = function () {
            var canvas = document.getElementById('canvas');
            var blob = dataURItoBlob(canvas.toDataURL("image/png"));
            var fd = new FormData(document.forms[0]);
            fd.append("file", blob);
            fd.append('type', "OTHERS");
            fd.append('docType', "SERVICE_ORDER");
            fd.append('mimeType', "PNG");
            fd.append('userId', appUtil.getCurrentUser().userId);
            $http({
                url: apiJson.urls.serviceOrderManagement.uploadSoDocument,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (!appUtil.isEmptyObject(response)) {
                    $toastService.create("Upload successful");
                    $scope.uploadedDocData = response;
                } else {
                    $toastService.create("Upload failed");
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Upload failed");
            });
        };

        $scope.uploadDoc = function () {
            $fileUploadService.openFileModal("Upload Document", "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                if(file.size > 307200){
                    $toastService.create('File size should not be greater than 300 kb.');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                    var mimeType = fileExt.toUpperCase();
                    var fd = new FormData();
                    fd.append('type', "OTHERS");
                    fd.append('docType', "SERVICE_ORDER");
                    fd.append('mimeType', fileExt.toUpperCase());
                    fd.append('userId', appUtil.getCurrentUser().userId);
                    fd.append('file', file);
                    $http({
                        url: apiJson.urls.serviceOrderManagement.uploadSoDocument,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (!appUtil.isEmptyObject(response)) {
                            $toastService.create("Upload successful");
                            $scope.uploadedDocData = response;
                        } else {
                            $toastService.create("Upload failed");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $toastService.create("Upload failed");
                    });
                } else {
                    $toastService.create('Upload Failed , File Format not Supported');
                }
                /*metaDataService.uploadFile("OTHERS","PAYMENT_REQUEST_INVOICE",file, function(doc){
                    $scope.uploadedDocData = doc;
                });*/
            });
        };

        $scope.scanToPng = function () {
            scanner.scan($scope.displayImagesOnPage,
                {
                    "output_settings": [
                        {
                            "type": "return-base64",
                            "format": "png"
                        }
                    ]
                }
            );
        };

        $scope.displayImagesOnPage = function (successful, mesg, response) {
            if (!successful) { // On error
                console.error('Failed: ' + mesg);
                return;
            }
            if (successful && mesg != null && mesg.toLowerCase().indexOf('user cancel') >= 0) { // User cancelled.
                console.info('User cancelled');
                return;
            }
            var scannedImages = scanner.getScannedImages(response, true, false); // returns an array of ScannedImage
            $scope.imagesScanned = [];
            $scope.processScannedImage(scannedImages[0]);
            /*for(var i = 0; (scannedImages instanceof Array) && i < scannedImages.length; i++) {
                var scannedImage = scannedImages[i];
                $scope.processScannedImage(scannedImage);
            }*/
        };

        $scope.processScannedImage = function (scannedImage) {
            $scope.imagesScanned.push(scannedImage);
            scaleImage(scannedImage.src);
        };

        function scaleImage(src) {
            var MAX_WIDTH = 1000;
            var image = new Image();
            var canvas = document.getElementById("scaleCanvas");
            image.onload = function () {
                //var canvas = document.getElementById("scaleCanvas");
                if (image.width > MAX_WIDTH) {
                    image.height *= MAX_WIDTH / image.width;
                    image.width = MAX_WIDTH;
                }
                var ctx = canvas.getContext("2d");
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                canvas.width = image.width;
                canvas.height = image.height;
                ctx.drawImage(image, 0, 0, image.width, image.height);
            };
            image.src = src;
        }

        $scope.uploadScannedFile = function () {
            var canvas = document.getElementById('scaleCanvas');
            var blob = dataURItoBlob(canvas.toDataURL("image/png"));
            var fd = new FormData(document.forms[0]);
            fd.append("file", blob);
            fd.append('type', "OTHERS");
            fd.append('docType', "SERVICE_ORDER");
            fd.append('mimeType', "PNG");
            fd.append('userId', appUtil.getCurrentUser().userId);
            $http({
                url: apiJson.urls.serviceOrderManagement.uploadSoDocument,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (!appUtil.isEmptyObject(response)) {
                    $toastService.create("Upload successful");
                    $scope.uploadedDocData = response;
                } else {
                    $toastService.create("Upload failed");
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Upload failed");
            });
        };


        $scope.previewSoDocument = function(doc){
            if(!appUtil.isEmptyObject(doc.documentLink)){
                $http({
                    method:"POST",
                    url:apiJson.urls.vendorManagement.downloadDocument,
                    data: doc,
                    responseType: 'arraybuffer'
                }).then(function(response){
                    var arrayBufferView = new Uint8Array( response.data );
                    var blob = new Blob( [ arrayBufferView ], { type: appUtil.mimeTypes[doc.mimeType] } );
                    var urlCreator = window.URL || window.webkitURL;
                    var imageUrl = urlCreator.createObjectURL( blob );
                    var preview = document.getElementById("documentPreview");
                    preview.innerHTML = "";
                    var img = new Image();
                    img.src = imageUrl;
                    preview.appendChild(img);
                },function(error){
                    $toastService.create("Could not download the document... Please try again");
                });
            }else{
                $toastService.create("Not a valid document... Please check");
            }
        };


    }]).controller("allocateCostModalCtrl",
    ['$rootScope', '$window', '$scope', 'appUtil', 'apiJson', '$http', '$toastService', 'Popeye', 'units', 'companies', 'type',
        function ($rootScope, $window, $scope, appUtil, apiJson, $http, $toastService, Popeye, units, companies, type) {


            $scope.initCostModal = function () {
                $scope.unitList = units
                for (var i = 0; i < $scope.unitList.length; i++) {
                    $scope.unitList[i].dataCheck = false;
                    $scope.unitList[i].checkBoxcheck = false;
                }
                $scope.units = $scope.unitList;
                $scope.companies = companies;
                $scope.mumbCheck = true;
                $scope.bangCheck = true;
                $scope.ncrCheck = true;
                $scope.naviMumbaiCheck = true;
                $scope.puneCheck = true;
                $scope.chennaiCheck = true;
                $scope.hyderabadCheck = true;
                $scope.type = type;
            };

            $scope.isCheck = function ($e, code) {
                if ($scope.type == "CAPEX") {
                    if ($e.target.checked) {
                        for (var i = 0; i < $scope.unitList.length; i++) {
                            if ($scope.unitList[i].code == code) {
                                $scope.unitList[i].checkBoxcheck = false;
                            }
                            else {
                                $scope.unitList[i].checkBoxcheck = true;
                            }
                        }
                    } else {
                        for (var i = 0; i < $scope.unitList.length; i++) {
                            $scope.unitList[i].checkBoxcheck = false;
                        }
                    }
                }
            }

            $scope.submit = function () {
                var unitId;
                if ($scope.type == "CAPEX") {
                    for (var key in $scope.units) {
                        if ($scope.units[key].dataCheck) {
                            unitId = $scope.units[key].code;
                        }
                    }
                    $http({
                        url: apiJson.urls.serviceOrderManagement.checkUnitBudget,
                        method: "GET",
                        params: {
                            unitId: unitId
                        }
                    }).then(function (response) {
                        if (response.data) {
                            $scope.submitData();
                        }
                        else {
                            $toastService.create("No budget is Allocated For This Unit.");
                        }
                    }, function (response) {
                        console.log(response);
                    });
                }
                else {
                    $scope.submitData();
                }
            }

            $scope.submitData = function () {
                var returnResult = {};
                for (var key in $scope.units) {
                    if ($scope.units[key].dataCheck) {
                        returnResult[key] = $scope.units[key];
                    }
                }
                Popeye.closeCurrentModal(returnResult);
                console.log("result is ",returnResult);
            }

            $scope.cancel = function () {
                Popeye.closeCurrentModal();
            };

        }]).controller("allocatedCostModalCtrl",
    ['$rootScope', '$scope', 'appUtil', '$toastService', 'metaDataService', 'Popeye', 'allUnits', 'selectedUnits', 'item', 'srItems', 'companies', 'costElementId',
        function ($rootScope, $scope, appUtil, $toastService, metaDataService, Popeye, allUnits, selectedUnits, item, srItems, companies, costElementId) {


            $scope.initCostModal = function () {
                $scope.unitList = allUnits;
                $scope.item = item;
                $scope.totalAmt = item.totalCost;
                $scope.selectedUnits = selectedUnits;
                $scope.allItems = srItems;
                $scope.companies = companies;
                $scope.isDisabled = true;
                $scope.mumbCheck = true;
                $scope.bangCheck = true;
                $scope.ncrCheck = true;
                $scope.puneCheck = true;
                $scope.naviMumbaiCheck = true;
                $scope.chennaiCheck = true;
                $scope.hyderabadCheck = true;
                $scope.costElementId = costElementId;
                $scope.createUnitList();
                $scope.updateUsedAmount();
                metaDataService.getAllBusinessCostCenters(function (bcc) {
                    $scope.oldUnitList = bcc;
                });
            };

            $scope.resetModel = function () {
                $scope.usedAmt = 0;
                $scope.remainingAmt = 0;
                $scope.isDisabled = false;
                //$scope.allItems = [];
                $scope.units = $scope.oldUnitList;
                $scope.unitList = $scope.oldUnitList;
                for (var key in $scope.allItems) {
                    if (key.startsWith($scope.item.costElementId + "_")) {
                        delete $scope.allItems[key];
                    }
                }
            }

            $scope.editModel = function () {
                $scope.isDisabled = false;
            }

            $scope.createUnitList = function () {
                var i = 0;
                for (var key in $scope.allItems) {
                    i++;
                    if ($scope.allItems[key].costElementId == $scope.costElementId) {
                        for (var j = 0; j < $scope.unitList.length; j++) {
                            if ($scope.allItems[key].businessCostCenterId == $scope.unitList[j].id) {
                                $scope.unitList[j].allocatedCost = $scope.allItems[key].totalCost;
                                $scope.unitList[j].qty = $scope.allItems[key].requestedQuantity;
                                //$scope.createDefaultBcc($scope.unitList[j]);
                            }
                            else {
                                if (i == 1) {
                                    $scope.unitList[j].allocatedCost = '';
                                    $scope.unitList[j].qty = 0;
                                }
                            }
                        }
                    }
                }
                $scope.units = $scope.unitList;
            }


            $scope.createUnitListOld = function () {
                var keys = Object.keys($scope.selectedUnits);
                var len = keys.length;
                var i = 0;
                for (var key in $scope.selectedUnits) {
                    i++;
                    for (var j = 0; j < $scope.unitList.length; j++) {
                        if ($scope.selectedUnits[key].id == $scope.unitList[j].id) {
                            $scope.unitList[j].allocatedCost = $scope.selectedUnits[key].allocatedCost;
                            $scope.unitList[j].qty = $scope.selectedUnits[key].qty;
                            //$scope.createDefaultBcc($scope.unitList[j]);
                        }
                        else {
                            if (i == 1) {
                                $scope.unitList[j].allocatedCost = '';
                                $scope.unitList[j].qty = 0;
                            }
                        }
                    }
                }
                $scope.units = $scope.unitList;
            }

            $scope.createDefaultBcc = function (unit) {
                var item = angular.copy($scope.item);
                if (appUtil.isEmptyObject($scope.allItems)) {
                    $scope.allItems = {};
                }
                var qty = parseFloat(parseFloat(unit.allocatedCost) / parseFloat(item.unitPrice)).toFixed(2);
                var tax = parseFloat(parseFloat(unit.allocatedCost) * (parseFloat(item.taxRate) / 100)).toFixed(2);
                var key = item.costElementId + "_" + unit.id;
                var obj = prepareObj(unit, item, qty, tax);
                if (appUtil.isEmptyObject($scope.allItems[key])) {
                    $scope.allItems[key] = {};
                }
                // add item specific to SO item and to that specific unit
                $scope.allItems[key] = obj;
            }

            $scope.updateUsedAmount = function () {
                $scope.usedAmt = 0;
                $scope.remainingAmt = 0;
                for (var key in $scope.allItems) {
                    if ($scope.allItems[key].costElementId == $scope.costElementId) {
                        for (var j = 0; j < $scope.unitList.length; j++) {
                            if ($scope.allItems[key].businessCostCenterId == $scope.unitList[j].id) {
                                $scope.usedAmt = (parseFloat($scope.usedAmt) + parseFloat($scope.unitList[j].allocatedCost == "" ? 0 : $scope.unitList[j].allocatedCost)).toFixed(2);
                            }
                        }
                    }
                }
                $scope.remainingAmt = (parseFloat($scope.totalAmt) - parseFloat($scope.usedAmt)).toFixed(2);
            }


            function getTotalCost() {
                var cost = 0;
                $scope.units.forEach(function (unit) {
                    cost += (appUtil.isEmptyObject(unit.allocatedCost) || isNaN(unit.allocatedCost))
                        ? 0 : parseFloat(unit.allocatedCost);
                });
                return cost;
            }

            function prepareObj(bcc, item, qty, tax) {
                console.log("checking quantity" + qty);
                return {
                    businessCostCenterId: bcc.id,
                    businessCostCenterName: bcc.name,
                    costElementId: item.costElementId,
                    costElementName: item.costElementName,
                    costElementDate: item.costElementDate,
                    costElementToDate: item.costElementToDate,
                    departmentName: item.departmentName,
                    company: bcc.company,
                    location: bcc.location,
                    state: bcc.state,
                    requestedQuantity: parseFloat(qty),
                    ascCode: item.ascCode,
                    serviceDescription: item.serviceDescription,
                    unitOfMeasure: item.unitOfMeasure,
                    unitPrice: item.unitPrice,
                    totalCost: parseFloat(bcc.allocatedCost),
                    totalAmount: parseFloat(parseFloat(bcc.allocatedCost) + parseFloat(tax)),
                    taxRate: parseFloat(item.taxRate),
                    totalTax: parseFloat(tax),
                    type: item.type,
                    unitId: bcc.code
                };
            }

            $scope.updateQty = function (unit) {
                var item = angular.copy($scope.item);
                var allocatedTillNow = getTotalCost(unit.id);
                if (!appUtil.isEmptyObject(item.totalCost) && !isNaN(item.totalCost)
                    && parseFloat(item.totalCost) >= allocatedTillNow) {
                    if (appUtil.isEmptyObject($scope.allItems)) {
                        $scope.allItems = {};
                    }
                    console.log("creating quantity" + unit.allocatedCost + "   " + item.unitPrice)
                    var qty = parseFloat(parseFloat(unit.allocatedCost) / parseFloat(item.unitPrice)).toFixed(2);
                    var tax = parseFloat(parseFloat(unit.allocatedCost) * (parseFloat(item.taxRate) / 100)).toFixed(2);
                    var key = item.costElementId + "_" + unit.id;
                    var obj = prepareObj(unit, item, qty, tax);
                    if (appUtil.isEmptyObject($scope.allItems[key])) {
                        $scope.allItems[key] = {};
                    }
                    // add item specific to SO item and to that specific unit
                    $scope.allItems[key] = obj;
                    unit.qty = isNaN(qty) ? 0 : qty;
                    $scope.totalAllocated = allocatedTillNow;
                    $scope.updateUsedAmount();
                } else {
                    if (appUtil.isEmptyObject(unit.allocatedCost)) {
                        $toastService.create("Select a valid cost to be allocated");
                    }
                    if (parseFloat(item.totalCost) < allocatedTillNow) {
                        $toastService.create("Total Allocated Cost cannot be less than cost across units");
                    }
                }
            };


            $scope.cancel = function () {
                Popeye.closeCurrentModal();
            };

            $scope.submit = function () {
                var returnResult = {};
                for (var key in $scope.allItems) {
                    if (!appUtil.isEmptyObject($scope.allItems[key].totalCost)
                        && !isNaN($scope.allItems[key].totalCost)) {
                        returnResult[key] = $scope.allItems[key];
                    }
                }
                Popeye.closeCurrentModal(returnResult);
            };


        }]).controller('departmentSOModalCtrl', ['$scope', 'appUtil', 'apiJson', '$http', 'Popeye', 'receivingList', '$toastService', '$alertService', 'type',
    function ($scope, appUtil, apiJson, $http, Popeye, receivingList, $toastService, $alertService, type) {


        $scope.init = function () {
            $scope.summaryDepartmentList = [];
            $scope.summaryItem = [];
            $scope.list = receivingList;
            $scope.type = type;
            $scope.summaryList();

        }

        $scope.summaryList = function () {
            var map = new Map();
            for (var x = 0; x < $scope.list.length; x++) {
                if (map.has($scope.list[x].departmentName)) {
                    var listData = map.get($scope.list[x].departmentName);
                    $scope.list[x].totalAmountDup = listData.totalAmountDup + $scope.list[x].totalAmount;
                    $scope.list[x].totalTaxDup = listData.totalTaxDup + $scope.list[x].totalTax;
                    $scope.list[x].totalCostDup = listData.totalCostDup + $scope.list[x].totalCost;
                    map.delete($scope.list[x].departmentName);
                    map.set($scope.list[x].departmentName, $scope.list[x]);
                }
                else {
                    $scope.list[x].totalAmountDup = $scope.list[x].totalAmount;
                    $scope.list[x].totalTaxDup = $scope.list[x].totalTax;
                    $scope.list[x].totalCostDup = $scope.list[x].totalCost;
                    map.set($scope.list[x].departmentName, $scope.list[x]);
                }
            }

            if ($scope.type == "CAPEX") {
                $scope.getDepartmentData(map);
            } else {
                angular.forEach(map, function (value, key) {
                    $scope.summaryItem.push(value);
                });
                $scope.closeModal({checked : false,
                                         isClosed : false});
            }
        }

        $scope.getDepartmentData = function (map) {
            angular.forEach(map, function (value, key) {
                $scope.summaryDepartmentList.push(value);
            });
            $http({
                url: apiJson.urls.serviceOrderManagement.getDepartmentData,
                method: "POST",
                data: $scope.summaryDepartmentList
            }).then(function (response) {
                if (response.data) {
                    $scope.summaryItem = response.data
                }
            }, function (response) {
                console.log(response);
            });
        }

        $scope.submit = function () {
            var check = false;
            for (var x = 0; x < $scope.summaryItem.length; x++) {
                if ($scope.summaryItem[x].totalAmountDup > $scope.summaryItem[x].remainingAmount) {
                    $toastService.create("Budget Exceeded for " + $scope.summaryItem[x].departmentName + " Department.");
                    check = true;
                }
            }
            if (!check) {
                $scope.closeModal({checked : check,
                    isClosed : false});
            }
        }

        $scope.cancel = function () {
            $scope.closeModal({checked : true,
                isClosed : true});
        }

        $scope.closeModal = function closeModal(obj) {
            Popeye.closeCurrentModal(obj);
        }


    }]).controller('uploadedSoSheetCtrl', ['$rootScope','$scope', 'appUtil','apiJson','$http' ,'Popeye','result','costElement',
    function ($rootScope,$scope, appUtil,apiJson,$http ,Popeye,result,costElement) {

        $scope.init = function (){
            $scope.result = result;
            $scope.totalOfAllocatedCost = $scope.getTotal(result);
            $scope.costElement = costElement;
        };

        $scope.getTotal = function (units) {
            var total = 0;
            units.forEach(function (unit) {
                total = total + unit.allocatedCost;
            });
            return total;
        };

        $scope.cancel = function(){
            Popeye.closeCurrentModal({isSuccessful :false});
        }

        $scope.submit = function(){
            Popeye.closeCurrentModal({isSuccessful : true,
                totalOfAllocatedCost : $scope.totalOfAllocatedCost});
        }
    }]);