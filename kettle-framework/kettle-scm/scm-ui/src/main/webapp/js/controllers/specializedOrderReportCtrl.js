/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('specializedOrderReportCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location','$toastService',
        function ($rootScope, $scope, apiJson, $http, appUtil,$location, $toastService) {

            $scope.init = function () {
                $scope.requestDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.getSpecializedOrderReport();
                $scope.unitList = appUtil.getUnitList();
            };

            $scope.getSpecializedOrderReport = function(){
                $scope.reportData = [];
                $http({
                    method: "GET",
                    url: apiJson.urls.requestOrderManagement.getSpecializedOrderReport+"?requestDate="+$scope.requestDate
                }).then(function success(response) {
                    console.log(response);
                    $scope.unitList.forEach(function (unit) {
                        $scope.reportData.push({
                            unit:unit,
                            orders:response.data[unit.id]
                        });
                    });
                }, function error(response) {
                    console.log("error:" + response);
                });
            }

        }]);
