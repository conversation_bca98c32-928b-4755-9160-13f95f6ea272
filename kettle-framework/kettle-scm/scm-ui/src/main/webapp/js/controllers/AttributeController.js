/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 11-05-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('attributeCtrl', ['$rootScope','$scope','authService','$location','$state','appUtil','$stateParams',
        function ($rootScope,$scope,authService,$location,$state,appUtil,$stateParams) {

            $scope.init = function(){
             /* if(appUtil.isEmptyObject($scope.productDefinition)){
                  $state.go('menu.addProduct');
              }else{
                  console.log($scope.productDefinition);
              }*/
            };

    }]);
