String.prototype.trim = function () {
	return this.replace(/^\s+|\s+$/g, "");
};

function getQueryParameter(parameterName) {
	try {
		var queryString = window.top.location.search.substring(1);
		parameterName = parameterName + "=";
		if (queryString.length > 0) {
			begin = queryString.indexOf(parameterName);
			if (begin != -1) {
				begin += parameterName.length;
				end = queryString.indexOf("&", begin);
				if (end == -1) {
					end = queryString.length;
				}
				return unescape(queryString.substring(begin, end));
			}
		}
		return "failure";
	} catch (Exception) {}
}

function checkForAttr($obj, $attr) {
	var attr = $obj.attr($attr);
	if (typeof attr !== 'undefined' && attr !== false) {
		return true;
	}
}

function validateTemperature(temp) {
	var stripped = temp;
	var RE = /^\d*$/;

	if (!RE.test(stripped)) {
		return false;
	}
	return true;
}

function validateNumber(num) {
	var txt = num;
	var RE = /^\d*$/;

	if (!RE.test(txt)) {
		return false;
	}
	return true;
}

function numberOnly(num) {
	var intRegex = /^\d+$/;
	var floatRegex = /^((\d+(\.\d *)?)|((\d*\.)?\d+))$/;
	// var str = $('#myTextBox').val();
	if (!(intRegex.test(num) || floatRegex.test(num))) {
		return false;
	}
	return true;
}

function validateScale(val) {
	var oprant = val;
	var RE = /^\d*\.?\d*$/;

	if (!RE.test(oprant)) {
		return false;
	}
	return true;
}

function alphaNumeric(id) {
	var alNum = /^([a-zA-Z0-9]+)$/;
	// only letters and numbers
	if (isEmpty(value) || !alNum.test(id)) {
		return false;
	}
	return true;
}

function alphaFilingNumeric(id) {
    var alNum = /^([a-zA-Z0-9]+)$/;
    // only letters and numbers
    if ( !alNum.test(id)) {
        return false;
    }
    return true;
}

function namesInitial(id) {
	var alNum = /^([a-zA-Z]+)$/;
	// only letters
	if (!alNum.test(id)) {
		return false;
	}
	return true;
}

function isEmpty(value) {
    return value==undefined || value==null;
}
function validateNames(value) {
	var alNum = /^([a-zA-Z]+)$/;
	// only letters
	if (isEmpty(value) || !alNum.test(value)) {
		return false;
	}
	return true;
}

function validateNamesNonSpecialChar(value) {
	var illegalChars = "!@#~$%^&*()_+=[]\\\;/{}|\":<>?.";
	var strToSearch = value;
	for (var i = 0; i < strToSearch.length; i++) {
		if (illegalChars.indexOf(strToSearch.charAt(i)) != -1) {
			return false;
		}
	}
	return true;
}

function validateNamesNonSpecialCharPlusComma(value) {
	var illegalChars = "'!@#$~%^&*()_+=-[]\\\;/{}|\":<>?.,";
	var strToSearch = value;
	for (var i = 0; i < strToSearch.length; i++) {
		if (illegalChars.indexOf(strToSearch.charAt(i)) != -1) {
			return false;
		}
	}
	return true;
}

function validatePassword(value) {

	//var legalChars= "^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{6,20}$"; 
	var legalChars = /^[A-Za-z0-9.!@#$%^&*()_ ]{6,20}$/;
	if (isEmpty(value) || !legalChars.test(value)) {
		return false;
	}
	return true;
}

function validateTitle(value) {
	//var legalChars="^[-A-Za-z0-9,/()&:. ]*[A-Za-z][-A-Za-z0-9,/()&:. ]*$";
	var legalChars="^(?=.{1,128}$)[A-Za-z.]*[A-Za-z][-A-Za-z0-9,/()&:. ]*$";
	if (!value.match(legalChars)) 
		return false;
	return true;
}

function validateLocations(value) {
	var illegalChars = "0123456789";
	var strToSearch = value;
	for (var i = 0; i < strToSearch.length; i++) {
		if (illegalChars.indexOf(strToSearch.charAt(i)) != -1) {
			return false;
		}
	}
	return true;
}

/*
 * function checkRadio(type1,type2,type3){ var check1=type1; var check2=type2;
 * var check3=type3;
 * 
 * if (check1 == false && check2 == false && check3 == false){ return false; }
 * else{ return true; } }
 */

function checkDigit(digit) {
	var num = digit;
	var RE = /^\d*$/;

	if (!RE.test(num)) {
		return false;
	}
	return true;
}

function checkdate(d1, m1, y1, d2, m2, y2) {
	var startd, startm, starty;
	var endd, endm, endy;
	startd = d1;
	startm = m1;
	starty = y1;
	endd = d2;
	endm = m2;
	endy = y2;
	if ((endd > startd) && (endm > startm) && (endy > starty)) {
		return true;
	}

}

function validateDigitsOnly(val) {
	var reg = new RegExp('^[0-9]+$');
	return (reg.test(val));
}

function validatePhoneNumber(value) {
	var reg = new RegExp('^[0-9]{10}$');
	return (reg.test(value));
}

function validateEmail(email) {
	var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
	return isEmpty(email) || re.test(email);
}

function testForRadio(val, error_obj, check_error) {
	if (typeof val == 'undefined') {
		$(error_obj).show();
		check_error = 1;
	}
	return check_error;
}

function validateLength(length,element){
	var flag = true;
	var $this = element;
	$this.removeClass('error').next('p.help-block').text("");
	var val = $this.val();
	flag = length <= val.length;
	if(!flag){
		$this.addClass('error').next('p.help-block').append("Field should not be greater than "+ length + "characters.<br/>");
	}
	return flag;
}

function validateForm(formElement){
	var $this = $('form[name="'+formElement+'"]');
	var validFlag = true;
	$this.find('input,select').each(function(element){
		var $element = $(this);
		if($element.attr("validate")!=undefined && $element.attr("validate")!=null){
			if(!validateInput($element.attr('validate'),$element)){
				validFlag = false;
				return false; //for breaking the loop
			}
		}
	});
	return validFlag;
}

function validatePan(val) {
	var reg = /^[A-Za-z]{5}\d{4}[A-Za-z]{1}$/g;
	return (reg.test(val.toUpperCase()));
}

function validateGst(val) {
	var reg = /^[A-Za-z0-9]{2}[A-Za-z]{5}\d{4}[A-Za-z]\d[Z][A-Za-z0-9]$/g;
	return (reg.test(val.toUpperCase()));
}

function validateCIN(val){
	var reg = /^(U|L){1}\d{5}[A-Z]{2}\d{4}(PTC|PLC|FLC|FTC|LLP|GAP|GAT|GOI|NPL|SGC|ULL|ULT|OPC)\d{6}$/g;
	return (reg.test(val.toUpperCase()));
}

function validateRegdNumber(val){
    var reg = /^[A-Z]{2}[0-9]{1,2}(?:[A-Z])?(?:[A-Z]*)?[0-9]{4}$/g;
    return (reg.test(val.toUpperCase()));
}



function validateInput(validation,element) {
	var flag = true;
	var $this = element;
	$this.removeClass('error').next('p.help-block').text("");
	var validations = validation.split(",");
	var val = $this.val();

	if(isEmpty(val)){
		return false;
	}

	for (var i = 0; i < validations.length; i++) {
		if (validations[i] == "notnull") {
			if (val == "") {
				$this.addClass('error').next('p.help-block').append("Field should not be blank" + '<br/>');
				flag = false;
				break;
			}else{
				$this.removeClass('error').next('p.help-block').append("");
			}
		}
		else if (validations[i] == "name") {
			if (val.trim().length>0 && !validateNames(val)) {
				console.log("inside if statement of validation", $this.addClass('error').next('p.help-block'));
				$this.addClass('error').next('p.help-block').append("Name should be in proper format" + '<br/>');
				flag = false;
			}
		}
		else if (validations[i] == "phone") {
			if (val.trim().length>0 && !validatePhoneNumber(val)) {
				$this.addClass('error').next('p.help-block').append("Phone no. should be in proper format" + '<br/>');
				flag = false;
			}
		}
		else if (validations[i] == "email") {
			if (val.trim().length>0 && !validateEmail(val)) {
				$this.addClass('error').next('p.help-block').append("Email should be in proper format" + '<br/>');
				flag = false;
			}
		}
		if(validations[i]=="password") {
			if(val.trim().length>0 && !validatePassword(val)) {
				$this.addClass('error').next('p.help-block').append("Password can be alphanumeric and special characters. Length 6-20 characters."+'<br/>');
				flag = false;
			}
		}

		if(validations[i]=="zip") {
			if(!alphaNumeric(val)) {
				$this.addClass('error').next('p.help-block').append("Postal Code should be only alphanumeric"+'<br/>');
				flag = false;
			}
		}
		if(validations[i]=="city") {
			if(!validateNamesNonSpecialCharPlusComma(val)) {
				$this.addClass('error').next('p.help-block').append("Field should be only alphanumeric"+'<br/>');
				flag = false;
			}
		}
		if(validations[i]=="title") {
			if(!validateTitle(val)) {
				$this.addClass('error').next('p.help-block').append("Title should contains atleast a alphabet, at most 128 characters and only [space(,)/&-:.] special character allowed."+'<br/>');
				flag = false;
			}
		}
		if(validations[i]=="pan") {
			if(val.trim().length>0 && !validatePan(val)) {
				$this.addClass('error').next('p.help-block').append("PAN should have just 10 characters. First 5 should be alphabets, next 4 should be digits and last character should be an alphabet"+'<br/>');
				flag = false;
			}
		}
		if(validations[i]=="gst") {
			if(val.trim().length>0 && !validateGst(val)) {
				$this.addClass('error').next('p.help-block').append("GSTIN should have just 15 characters. First 2 should be the state code, next 10 should be your PAN number, last three should be alphanumeric with 'Z' in between"+'<br/>');
				flag = false;
			}
		}
		if(validations[i]=="cin") {
			if(val.trim().length>0 && !validateCIN(val)) {
				$this.addClass('error').next('p.help-block').append("CIN should have just 21 characters. First character is listing status, next 5 should be the industry code, next  2 should be state code, next four 4 be year of incorporation, next 3 should be type of ownership and last 6 digits should be your ROC number"+'<br/>');
				flag = false;
			}
		}
	}
	return flag;
}
