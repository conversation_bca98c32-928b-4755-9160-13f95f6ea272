var express = require('express');
var path = require('path');
var app = express();

var port = process.env.PORT || 8585; // set our port
//var staticdir = process.env.NODE_ENV === 'production' ? 'dist.prod' : 'dist.dev'; // get static files dir

// send all requests to index.html so browserHistory in React Router works
app.get("/", function (req, res) {
    res.sendFile(path.join(__dirname, 'indexnew.html'));
});

app.use(express.static(__dirname + '/')); // set the static files location /public/img will be /img for users

// start app ===============================================
app.listen(port);                   // startup our app at http://localhost:80
console.log('Starting server on port ' + port);       // shoutout to the user
exports = module.exports = app;             // expose app