<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
  <style>
  .budget-exceeded-card {
      border: 2px red solid;
    padding-top: 15px;
  }
 .popeye-modal-container .popeye-modal {
    position: relative;
    text-align: left;
    vertical-align: middle;
    display: inline-block;
    width: 80%;
    border-radius: 3px;
    border: none;
    z-index: 1300;
    padding: 2em 1.5em 1.5em;
    background: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,.4);
}

.budget-exceeded-label {
    color: #EFE8E8;
    background-color: #D82F2F;
    border-color: #ebccd1;
    border-radius: 8px;
    font-size: 20px;
    padding: 10px  !IMPORTANT;
    margin-bottom:0px;
}
  </style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div data-ng-if="!assetOrder" class="col s12 m6 l6">
                <h3>Ad-hoc Order</h3>
            </div>
            <div data-ng-if="assetOrder" class="col s12 m6 l6">
                <h3>Asset Order</h3>
            </div>
            <div class="col s12 m6 l6 menuItemList">

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12 m6 l6">
            <label>Select Fulfillment Unit:</label>
            <select ui-select2 ng-model="selectedUnit" ng-change="selectCategory(selectedUnit)" data-placeholder="Select Unit">
            	<option value=""></option>
                <option ng-repeat="unit in scmUnitList track by unit.id" value="{{unit}}">{{unit.name}}</option>
            </select>
        </div>
        <div class="col s12 m6 l6">
            <label>Select Product:</label>
            <select ui-select2 ng-model="selectedProduct" data-placeholder="Enter name of product">
                <option value=""></option>
                <option ng-repeat="product in scmProductDetails | filter : byProducts" value="{{product}}">{{product.productName}}</option>
            </select>
            <input type="button" class="btn" value="ADD PRODUCT" data-ng-click="addNewRoItem()" />
        </div>
    </div>


    <div class="row">
        <div class="row">
            <div class="col s12">
                <ul class="collection" data-ng-if="addedRoItems.length>0">
                    <li class="collection-item list-head">
                        <div class="row" style="margin-bottom: 0px;">
                            <div class="col s1">#</div>
                            <div data-ng-class="{'col s2' : !assetOrder,'col s3':((!isFountain9Unit && !assetOrder) || assetOrder)}">Product Name</div>
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">Requested Qty.</div>
                            <!--<div class="col s2">Requested Absolute Qty.</div>-->
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">Packaging Qty</div>
                            <div class="col s2">Packaging Name</div>
                            <div class="col s2" data-ng-if="isFountain9Unit && !assetOrder">
                                Reason
                                <br>
                                <select style="color: black;width: 170px;" ng-model="selecetedReason" data-ng-options="reason as reason for reason in reasons"
                                data-ng-change="changeAllReasons(selecetedReason)"></select>
                            </div>
                            <div class="col s2" data-ng-if="isFountain9Unit && !assetOrder">
                                Comment <span data-ng-if="selecetedReason == 'OTHER'"><b>Min : 3 letters</b></span>
                                <br>
                                <textarea data-ng-model="enterredComment"  data-ng-change="changeAllComments(enterredComment)" style="resize: none;color: black;" data-ng-if="selecetedReason != null"></textarea>
                                <span data-ng-if="enterredComment.length > 0">{{100-enterredComment.length}} Characters Remaining</span>
                                <span style="color: red" data-ng-if="enterredComment.length > 100">{{enterredComment.length - 100}} Characters Exceeded</span>
                            </div>
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">Remove</div>
                        </div>
                    </li>
                    <li class="collection-item" data-ng-repeat="roi in addedRoItems track by $index">
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s1">{{$index + 1}}</div>
                            <div data-ng-class="{'col s2' : !assetOrder,'col s3':((!isFountain9Unit && !assetOrder) || assetOrder)}">
                                <a data-ng-click="showPreview($event, roi.productId,'PRODUCT')">{{roi.productName}} ({{roi.unitOfMeasure}})</a>
                            </div>
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">{{(!isNaN(roi.requestedQuantity)) ?  (roi.requestedQuantity) : undefined}}</div>
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">
                            <div data-ng-if="!isManualBook[roi.productId]"><input data-ng-model="roi.packagingQuantity" type="number" min="0" data-ng-change="calculateRequestQty(roi)" /></div>
                            <div data-ng-if="isManualBook[roi.productId]"><span data-ng-init="calculateRequestQty(roi)">{{roi.packagingQuantity}}</span></div>
                            </div>
                            <!--<div class="col s2">{{roi.requestedAbsoluteQuantity==null?0:roi.requestedAbsoluteQuantity}}</div>-->
                            <div class="col s2">{{roi.packagingName}}</div>
                            <div class="col s2" data-ng-if="isFountain9Unit && !assetOrder">
                                <select class="input-field" ng-model="roi.reason" style="margin-top: 0px; height: 2rem;" data-ng-options="reason as reason for reason in reasons">
                                </select>
                            </div>
                            <div class="col s2" data-ng-if="isFountain9Unit && !assetOrder">
                                <textarea data-ng-model="roi.comment" style="resize: none"></textarea>
                                <span data-ng-if="roi.comment.length >0 && roi.comment.length <=100">{{100-roi.comment.length}} Characters Remaining</span>
                                <span style="color: red" data-ng-if="roi.comment.length > 100">{{roi.comment.length - 100}} Characters Exceeded</span>
                            </div>
                            <div data-ng-class="{'col s1' : !assetOrder ,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}"><button class="btn btn-small" data-ng-click="removeItem($index)">Remove</button></div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row">
            <div class="col s12 m6 l6">
                <div class="form-element">
                    <label for="inputCreated">Fulfillment date</label>
                    <input input-date type="text" name="created" id="inputCreated" ng-model="fulfillmentDate"
                           container="" format="yyyy-mm-dd" select-years="1" min="{{minRefOrderFulFillmentDate}}"
                           max="{{maxRefOrderFulFillmentDate}}" />
                </div>
                <!-- <div class="form-element" style="width: 40%">
                    <label>Add Search Tag (optional)</label>
                    <input type="text" maxlength="20" data-ng-model="searchTag"/>
                </div> -->
            </div>
            <div class="col s12 m6 l6">
                <div class="form-element">
                    <label>Comment (optional)</label>
                    <textarea data-ng-model="comment"></textarea>
                </div>
            </div>
        </div>

        <div class="col s12 form-element">
            <input type="button" class="btn" value="SUBMIT" data-ng-click="createRoObject(true)" />
            <input type="button" class="btn btn-warning" value="FORCE SUBMIT" data-ng-click="createRoObject(false)"  acl-action="SOROIBFS" 
            style="float: right;background-color: #d83333;" />
        </div>
    </div>
</div>

<script type="text/ng-template" id="budgetExceededModal.html"> 
<div>
	<div class="row ">
		<div class="col s12 alert budget-exceeded-label  text-center">Request
			Order can not be created because you have excceded budget
			allocated.Details of budget and products are given below :</div>
	</div>
	<div class="row budget-exceeded-card"
		data-ng-repeat="resp in  orderResponse">
		<div class="col s4">
			<label>Budget Amount</label>{{resp.budgetAmount}}
		</div>
		<div class="col s4">
			<label>Current Amount</label>{{resp.currentAmount}}
		</div>
		<div class="col s4">
			<label>Requested Amount</label>{{resp.requestedAmount}}
		</div>
		<div class="row" style ="margin-bottom: 0px !important;">
			<div class="col s12" style="margin-top: 7px;">
				<label>Product name of this category are : </label>
			</div>
			<div class="row" >
				<div class="col s12" >
					<span data-ng-repeat="products in  resp.products">
							<span  style="color:red;"	> {{$index+1}}. {{products}}</span><span data-ng-if="$index < (resp.products.length - 1)">,</span>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<!--  <button class="btn green"  data-ng-click="rejectedGoodsReceivedDecline()">Confirm</button> -->
		<button class="btn red" style="float: right;" data-ng-click="cancel()">Cancel</button>
	</div>
</div>
</script>