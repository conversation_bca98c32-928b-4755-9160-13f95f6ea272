<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12 m6 l6">
                <h4>Standalone Transfer Order</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12 m6 l6">
            <label>Select Product:</label>
            <select ui-select2 ng-model="selectedProduct" data-placeholder="Enter name of product">
                <option value=""></option>
                <option ng-repeat="product in scmProductDetails" value="{{product}}">{{product.productName}}</option>
            </select>
            <input type="button" class="btn" value="ADD PRODUCT" data-ng-click="addNewGRItem()"/>
        </div>
    </div>


    <div class="row" data-ng-show="GRProducts.length>0">
        <form name="grForm" novalidate>
            <ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
                <li class="collection-item list-head">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s3">Product Name</div>
                        <div class="col s3">Received Quantity</div>
                        <div class="col s3">Unit Of Measure</div>
                    </div>
                </li>
                <li style="margin-bottom: 10px; border:#ddd 1px solid;"
                    data-ng-repeat="item in GRProducts track by item.productId">
                    <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid;">
                        <div class="col s3">{{item.productName}}</div>
                        <div class="col s3">{{item.receivedQuantity==null?0:item.receivedQuantity}}</div>
                        <div class="col s3">{{item.unitOfMeasure}}</div>
                    </div>
                    <div class="row">
                        <div class="col s5">
                            <label>Select SKU</label>
                            <select data-ng-model="item.selectedSku"
                                    data-ng-options="sku as sku.skuName for sku in item.skuList track by sku.skuId"></select>
                        </div>
                        <div class="col s5">
                            <label>Select Packaging:</label>
                            <select data-ng-model="item.selectedPackaging"
                                    data-ng-options="packaging as packaging.packagingDefinition.packagingName group by packaging.packagingDefinition.packagingType for packaging
                                     in item.selectedSku.skuPackagings | orderBy: 'packagingDefinition.packagingType' track by packaging.packagingId"
                                    data-ng-init="item.selectedPackaging=item.selectedSku.skuPackagings[0]"></select>
                        </div>
                        <div class="col s2">
                            <input type="button" value="Add" class="btn" data-ng-click="addPackaging(item)"
                                   style="margin-top:20px;"/>
                        </div>
                    </div>
                    <div class="row" data-ng-repeat="grItem in item.grPackaging track by $index">
                        <div class="col s12">
                            <div class="row">
                                <div class="col s6">{{grItem.skuName}}</div>
                                <div class="col s6">Received Qty: {{grItem.receivedQuantity}} &nbsp;
                                    {{grItem.unitOfMeasure}}
                                </div>
                            </div>
                            <div class="row" data-ng-repeat="pgd in grItem.packagingDetails track by $index">
                                <div class="col s4">{{pgd.packagingDefinitionData.packagingName}}</div>
                                <div class="col s2"><label>Units Packed:</label>
                                    <input type="number" name="pkgQty[$index]" min="0.000001"
                                           data-ng-model="pgd.numberOfUnitsPacked"
                                           ng-change="updatePackagingQty(pgd,grItem,item)" required/>
                                    <p ng-show="grForm.pkgQty[$index].$error.required || grForm.pkgQty[$index].$error.min"
                                       class="errorMessage">Please enter valid quantity.</p>
                                </div>
                                <div class="col s2"><label>Received Qty:</label> {{pgd.receivedQuantity}}</div>
                                <div class="col s2"><label>Unit Of Measure:</label>
                                    {{pgd.packagingDefinitionData.unitOfMeasure}}
                                </div>
                                <div class="col s2">
                                    <button class="btn btn-small" data-ng-click="removePackaging(grItem,$index, item)">
                                        Remove
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="row">
                <div class="col s12 form-element">
                    <label>Comment(optional):</label>
                    <textarea data-ng-model="comment"></textarea>
                </div>
                <div class="col s12 form-element">
                    <input type="button" data-ng-if="grForm.$valid" data-ng-click="createGRObject()" class="btn right"
                           value="Submit"/>
                </div>
            </div>
        </form>
    </div>
</div>