<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    td,th{
        padding:10px 5px !important;
    }
    .select2.select2-container{width: 100% !important;}
    #images{
        text-align: center;
    }
    img#scanned {
        height: 400px; /** Sets the display size */
        margin-right: 12px;
        margin-top: 20px;
    }
    .custom-modal{
         width: 50% !important;
    }
</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h5 class="left">Create Request for Service Receiving</h5>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==true">
        <div class="row">
            <div class="col s4">
                <label>Select Request Type</label>
                <select data-ng-model="prRequestType" data-ng-options="item as item.name for item in prRequestTypes"></select>
            </div>
        </div>

        <div data-ng-show="prRequestType.shortCode=='SR'">
            <div class="row">
                <div class="col s6 m4">
                    <label>Start date*</label>
                    <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s6 m4">
                    <label>End date*</label>
                    <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s12 m4">
                    <label>Select Vendor*</label>
                    <select ui-select2 id="vendorListx" name="vendorListx" data-ng-model="selectedVendor"
                            data-ng-options="item as item.name for item in vendorList track by item.id"></select>
                </div>
                <div class="col s6">
                    <label>Select Company</label>
                    <select ui-select2 id="companies" name="companies" data-ng-model="selectedCompany"
                            data-ng-change="selectCompany(selectedCompany)"
                            data-ng-options="item as item.name for item in companies track by item.id"></select>
                </div>
                <div class="col s6">
                    <label>Select Delivery Location</label>
                    <select ui-select2 id="locations" name="locations" data-ng-model="selectedLocation"
                            data-ng-change="selectLocation(selectedLocation)"
                            data-ng-options="item as item.name for item in locations track by item.id"></select>
                </div>
                <div class="col s12">
                    <input type="button" class="btn" value="Find" data-ng-click="findSrs()" style="margin-top: 25px;" />
                </div>
            </div>

            <div class="row" data-ng-show="srs.length>0">
                <div class="col s12">
                    <table class="table bordered" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>SR Id</th>
                            <th>Company Name</th>
                            <th>Dispatch Location</th>
                            <th>Delivery Location</th>
                            <th>Receiving Date</th>
                            <th>Bill Amount</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="sr in srs track by sr.id">
                            <td class="underline pointer" data-ng-click="viewDetail(sr)">{{sr.id}}</td>
                            <td>{{sr.company.name}}</td>
                            <td>{{sr.location.name}}</td>
                            <td>{{sr.deliveryLocation.name}}</td>
                            <td>{{sr.creationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</td>
                            <td>{{sr.totalAmount}}</td>
                            <td>
                                <span>
                                    <input id="sr-{{sr.id}}" type="checkbox" data-ng-model="sr.checked" />
                                    <label for="sr-{{sr.id}}">select</label>
                                </span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <button class="btn" data-ng-click="initiatePR()" style="margin-top: 25px;">
                        Initiate Payment Request
                    </button>
                </div>
            </div>
            <div data-ng-if="showNoGR" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No SR with pending payment request found.</div>
        </div>
    </div>

    <div data-ng-show="selectView==false">
        <div class="row">
            <div class="col s6">
                <label>Select Payment Request Location</label>
                <select ui-select2 id="selectPrLocation" name="selectPrLocation" data-ng-model="selectPrLocation"
                        data-ng-change="prLocation(selectPrLocation)"
                        data-ng-options="item as item.name for item in locations track by item.id"></select>
            </div>
            <div class="col s4">
                <button style="margin-top: 20px;margin-left: 50px;" class="btn btn-medium center" data-ng-click="fillPrAmount()">{{filledPr ? "Clear Amount" : "Fill Amount"}}</button>
            </div>
            <div class="col s2">
                <input type="button" class="btn right" value="BACK" data-ng-click="backToSelectView()" />
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                        <tr>
                            <th>SR Item ID</th>
                            <th>Cost Element</th>
                            <th>Business Cost Center</th>
                            <th>Quantity</th>
                            <th>Invoice Quantity</th>
                            <th>Remaining Amount</th>
                            <th>Remaining Tax Amount</th>
                            <th>Invoice Amount</th>
                            <th>Unit Price</th>
                            <th>Cost</th>
                            <th>Tax</th>
                            <th>Total Tax</th>
                            <th>Amount</th>
                            <!--<th>TDS(in %)</th>-->
                            <th>Deviations</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-repeat="item in paymentRequest.paymentInvoice.paymentInvoiceItems">
                            <td>{{item.id}}</td>
                            <td>
                                {{item.costElementName}}[{{item.ascCode}}]({{item.costElementDate| date :'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})<br>
                                <i>({{item.serviceDescription}})</i>
                            </td>
                            <td>{{item.businessCostCenterName}}</td>
                            <td>{{item.receivedQuantity}}</td>
                            <td ng-if="item.invoiceQuantity">{{item.invoiceQuantity}}</td>
                            <td ng-if="!item.invoiceQuantity">0</td>
                            <td>{{item.remainingTotalAmount}}</td>
                            <td>{{item.remainingTaxAmount}}</td>
                            <td><input type="number" min="0"
											step="0.001" ng-model="item.invoiceAmount"
											data-ng-change="updatePRAmount(item)" style="width:100px"></td>
<!--                            <td><input type="number" min="0"-->
<!--                                       step="0.001" ng-model="item.invoiceQuantity"-->
<!--                                       data-ng-change="updatePRQty(item)" style="width:100px"></td>-->
                            <td>{{item.unitPrice}}</td>
                            <td>{{item.totalCost}}</td>
                            <td><span data-ng-repeat="tax in item.taxes">{{tax.taxName}}@{{tax.percentage}}%,</span></td>
                            <td>{{item.totalTax}}</td>
                            <td>{{item.totalAmount}}</td>
                            <!--<td>
                                <input data-ng-change="changeProposedAmount(item.tdsRate)"
                                        style="width:40px;" type="number" data-ng-model="item.tdsRate" step="0.01" />
                            </td>-->
                            <td>
                                <span data-ng-repeat="deviation in item.deviations" style="background:#efefef;border:#ddd 1px solid;border-radius: 2px;">
                                    {{deviation.paymentDeviation.deviationDetail}}: {{deviation.deviationRemark}}
                                    <span class="pointer" data-ng-click="removeDeviation(item.deviations, $index)">&times</span>
                                </span>
                                <button style="width:30px;" class="btn btn-small" data-ng-click="setAvailableDeviations(item,'INVOICE_ITEM')"
                                        data-target="itemDevModal" modal>&plus;</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col s6">
                <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                    <p><b>Created By:</b> {{paymentRequest.createdBy.name}}</p>
                    <p><b>Raised For</b> {{paymentRequest.company.name}} [{{paymentRequest.deliveryLocation.name}}]</p>
                    <p><b>Base Amount:</b> {{paymentRequest.paymentInvoice.paymentAmount | number  : 6}}</p>
                    <p><b>Total Bill Amount:</b> {{paymentRequest.paymentInvoice.invoiceAmount | number  : 6}}</p>
                    <p><b>Proposed Amount:</b> {{paymentRequest.proposedAmount| number  : 6}}</p>
                </div>
            </div>
            <div class="col s6">
                <div class="row">
                    <div class="col s4">
                        <label>Invoice Number </label>
                        <input type="text" data-ng-model="paymentRequest.paymentInvoice.invoiceNumber">
                    </div>
                    <div class="col s4">
                        <label>Invoice Date</label>
                        <input type="text" input-date container="" format="yyyy-mm-dd" data-ng-model="paymentRequest.paymentInvoice.invoiceDate">
                    </div>
                    <div class="col s4" style="margin-top: 1.5rem;">
                        <input id="amountsMatch" type="checkbox" data-ng-model="paymentRequest.amountsMatch" />
                        <label for="amountsMatch">Amounts match</label>
                    </div>
                </div>
                <div class="row">
                    <input type="button" value="Scan" class="btn btn-small" data-target='scanModal' modal data-ng-click="resetScanModal()" />
                    <input type="button" value="Upload" data-ng-click="uploadDoc()" class="btn btn-small" />
                    <input type="button" value="Preview" data-ng-if="uploadedDocData!=null"
                           data-ng-click="previewPRInvoice(uploadedDocData)" class="btn btn-small" data-target="invoicePreviewModal" modal />
                    <input type="button" value="Download" data-ng-if="uploadedDocData!=null"
                           data-ng-click="downloadPRInvoice(uploadedDocData)" class="btn btn-small" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col s6 left">
                <span data-ng-repeat="deviation in paymentRequest.paymentInvoice.deviations"
                      style="background:#efefef;border:#ddd 1px solid;border-radius: 2px; margin-right: 5px;">
                    {{deviation.paymentDeviation.deviationDetail}}: {{deviation.deviationRemark}}
                    <span class="pointer"
                          data-ng-click="removeDeviation(paymentRequest.paymentInvoice.deviations, $index)">&times</span>
                </span>
                <br/>
                <input type="button" value="Add Deviation" class="btn margin-top-5" data-target="invoiceDevModal"
                       data-ng-click="setAvailableDeviations(paymentRequest.paymentInvoice,'INVOICE')" modal />
            </div>
            <div class="col s6 right" data-ng-if="paymentRequest.invoiceDocumentHandle != ''">
                <input type="button" class="btn" value="Create Payment Request" data-ng-click="submitPaymentRequest(false)" />
                <input type="button" class="btn" value="Create Force Payment Request" data-ng-click="submitPaymentRequest(true)" />
            </div>
        </div>
    </div>
</div>

<div id="snapModal" class="modal">
    <div class="modal-content">
        <h4>Take Snapshot</h4>
        <video data-ng-show="snapRunning" id="video" width="640" height="480" autoplay></video><br />
        <button data-ng-click="startSnap()" class="btn btn-small">Start</button>
        <button data-ng-click="snapPicture()" class="btn btn-small">Snap Photo</button><br />
        <canvas data-ng-hide="snapRunning" id="canvas" width="640" height="480"></canvas><br />
    </div>
    <div class="modal-footer">
        <button data-ng-click="uploadFile()" class="btn btn-small modal-action modal-close">Upload</button>
        <button class="btn btn-small modal  -action modal-close red" style="margin-right: 20px;">Cancel</button>
    </div>
</div>

<div id="scanModal" class="modal">
    <div class="modal-content">
        <h3>Scan document</h3>
        <button type="button" data-ng-click="scanToPng()">Scan</button>
        <div id="images" style="margin-top: 20px;height: 480px;overflow: auto;">
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="uploadScannedFile()">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="invoiceDevModal" class="modal">
    <div class="modal-content">
        <h5>Add deviations</h5>
        <div class="row" data-ng-repeat="dev in availableInvoiceDevs track by $index">
            <div class="col s4">{{dev.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="dev.remark" /></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="{{$index}}" data-ng-model="dev.checked" />
                    <label for="{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(paymentRequest.paymentInvoice, 'INVOICE')">Submit</button>
    </div>
</div>

<div id="itemDevModal" class="modal">
    <div class="modal-content">
        <h5>Add deviations</h5>
        <div class="row" data-ng-repeat="dev in availableItemDevs track by $index">
            <div class="col s4">{{dev.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="dev.remark" /></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="{{$index}}" data-ng-model="dev.checked" />
                    <label for="{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(selectedItemForDeviation, 'INVOICE_ITEM')">Submit</button>
    </div>
</div>

<div id="invoicePreviewModal" class="modal">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <div id="invoicePreview" style="max-height: 370px; overflow: auto;"></div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" style="margin-right: 20px;">Close</button>
    </div>
</div>

