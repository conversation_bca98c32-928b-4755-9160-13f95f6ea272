<style>
    .amount-rows{
        padding: 3px;
        font-size: 16px;
    }

    .modal-large{
        width:70%;
    }

    #invoiceItemTable td,th{
        padding: 3px 5px !important;
    }

    #otherVehicle input{
        width:10.5rem;
        padding:0.40rem;
        margin-right:2px;
        margin-bottom: 2px;
    }

    .reqSearchModal{
        width: 80% !important;
        min-height: 50% !important;
    }

    .reqSearchModal .footer{
        padding-top: 1rem;
        bottom: 0.5rem;
        position: relative;
        right: 0.5rem;
        width: 100%;
    }

</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="searchingCard row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Raise Vendor Invoice Request</h4>
                <div class="form-element" style="padding-top: 24px;margin-bottom: 0px;">
                    <input
                            id="isForFixedAsset"
                            data-ng-model="isForFixedAsset"
                            data-ng-change="resetAll()"
                            type="checkbox" />
                    <label
                            class="black-text"
                            for="isForFixedAsset">For Fixed Asset</label>
                </div>
            </div>
            <div class="row">
                <div class="col s4">
                    <label>Dispatch Unit</label>
                    <div class="left">{{currentUnit.name}}</div>
                </div>
                <div class="col s4">
                    <label>Enter PO*</label>
                    <input type="text" placeholder="Purchased order  number"
                           data-ng-model="purchasedOrderNumber">
                </div>
                <div class="col s4">
                    <label>Select PO Date*</label>
                    <input input-date type="text" ng-model="purchasedOrderDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s3">
                    <label
                            class="black-text"
                            for="requestForLable">Business Types</label>
                    <select ui-select2="selectedUnit3" id="types" name="types" data-ng-model="bussType" data-ng-change="changeBusinessType(bussType)"
                            data-ng-options="businessType as businessType.name for businessType in businessTypeList">
                    </select>
                </div>
                <div class="col s3">
                    <label>Transaction Type</label>
                    <select id="types" name="types" data-ng-model="txnType" data-ng-change="changeType(txnType)">
                        <option data-ng-repeat="type in txnTypes" value="{{type}}">{{type}}</option>
                    </select>
                </div>
                <div class="col s3">
                    <label>Invoice Type</label>
                    <select id="invoiceTypes" name="types" data-ng-model="invoiceType"
                            data-ng-change="changeInvoiceType(invoiceType)">
                        <option data-ng-repeat="invoiceType in invoiceTypes" value="{{invoiceType}}">{{invoiceType}}</option>
                    </select>
                </div>
                <div class="col s3" data-ng-show="txnType != null && txnType != undefined">
                    <label>Select Vendor</label>
                    <select class="select2Input" ui-select2="select2Options" id="vendorList" name="vendorList" data-ng-model="selectedVendor"
                            data-ng-options="vendor as vendor.entityName for vendor in vendorList track by vendor.vendorId"
                            data-ng-change="selectVendor(selectedVendor)"></select>
                </div>
            </div>

            <div class="row margin" data-ng-show="txnType != null && txnType != undefined" >
                <div class="col s3" data-ng-if="deliveryLocations.length>0">
                    <label>Select Delivery Location</label>
                    <select class="select2Input" ui-select2="select2Options" id="deliveryLocationList" name="deliveryLocationList" data-ng-model="selectedLocation"
                            data-ng-options="location as location.locationName for location in deliveryLocations"
                            data-ng-change="selectDeliveryLocation(selectedLocation)"></select>
                </div>
                <div class="col s9" data-ng-show="selectedDeliveryLocation!=null">
                    <table>
                        <thead>
                        <tr>
                            <th>Selected Delivery Address</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>
                                {{selectedDeliveryLocation.address.line1}}<br>
                                {{selectedDeliveryLocation.address.line2}}<br>
                                {{selectedDeliveryLocation.address.city}},
                                {{selectedDeliveryLocation.address.state}},
                                {{selectedDeliveryLocation.address.country}} - {{selectedDeliveryLocation.address.zipcode}},
                                Contact#{{selectedDeliveryLocation.address.addressContact}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row" data-ng-show="txnType != null && txnType != undefined" >
                <div class="col s3" data-ng-if="billingLocations.length>0">
                    <label>Select Billing Location</label>
                    <select class="select2Input" ui-select2="select2Options" id="billingLocationList" name="billinigLocationList" data-ng-model="selectedBillingLocation"
                            data-ng-options="location as location.locationName for location in billingLocations "
                            data-ng-change="selectBillingLocation(selectedBillingLocation)"></select>
                </div>
                <div class="col s9" data-ng-show="selectedBillingLocation!=null">
                    <table>
                        <thead>
                        <tr>
                            <th>Selected Billing Address</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>
                                {{selectedBillingLocation.address.line1}}<br>
                                {{selectedBillingLocation.address.line2}}<br>
                                {{selectedBillingLocation.address.city}},
                                {{selectedBillingLocation.address.state}},
                                {{selectedBillingLocation.address.country}} - {{selectedBillingLocation.address.zipcode}},
                                Contact#{{selectedBillingLocation.address.addressContact}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col s5" data-ng-if="locationList.length>0">
                    <label>Upload PO*</label>
                    <input type="button" value="Upload File" data-ng-click="uploadDoc()" class="btn btn-small" />
                    <input type="button" value="Download PO" data-ng-if="uploadedDocData!=null"
                           data-ng-click="downloadPO(uploadedDocData)" class="btn btn-small" />
                </div>
                <div class="col s5" data-ng-if="locationList.length>0">
                    <button data-ng-click="getPriceAndTaxData()" style="width: 90px; margin-top:1.6rem;" class="btn btn-small">Search</button>
                </div>
            </div>

            <div class="row margin0" style="background-color: #f3ecec;padding: 15px 0px;margin: 3px;">
                <div class="col s3">
                    <label>Select Transport Mode</label>
                    <select data-ng-options="mode as mode.name for mode in transportModes track by mode.id"
                            data-ng-model="selectedTransportMode" data-ng-change="getVehicles(selectedTransportMode)"></select>
                </div>
                <div class="col s4">
                    <label>Select Vehicle</label>
                    <select data-ng-options="vehicle as vehicle.name for vehicle in vehicles track by (selectedTransportMode.name+'_'+vehicle.vehicleId)"
                            data-ng-model="selectedVehicle" data-ng-change="selectVehicle(selectedVehicle)"></select>
                </div>
                <div id="otherVehicle" class="col s5" data-ng-if="showOtherVehicleFields">
                    <div class="row margin0">
                        <label>Enter Details</label>
                        <input type="text" placeholder="Rgd. Number/GSTIN"
                               style="text-transform: uppercase;"
                               data-ng-focus="validateAndSaveId(transportId)"
                               data-ng-change="validateAndSaveId(transportId)"
                               data-ng-model="transportId">
                        <span style="cursor: pointer;" data-ng-if="showValidError" tooltipped data-tooltip="{{validateErrorText}}">
                            <i class="fa fa-times red-text"></i>
                        </span>
                        <input type="text" placeholder="Name"
                               style="text-transform: uppercase;"
                               data-ng-change = "validateAndSaveName(transportName)"
                               data-ng-model="transportName">
                        <input data-ng-if="selectedTransportMode.name!='ROAD'"  type="text" placeholder="Docket number"
                               style="text-transform: uppercase;"
                               data-ng-change = "validateAndSaveDocket(docketNumber)"
                               data-ng-model="docketNumber">
                    </div>
                </div>
            </div>
            <div class="row" data-ng-show="isForFixedAsset == true">
                <dev draggable1 class="col s3">
                    <label style="color: whitesmoke;">Focus to scan   </label>
                    <input id="_draggableInputId" type="text" max-length="6" name="scan" data-ng-model="scannedAssetTagValue"  data-ng-change="onScan()" stopEvent>
                </dev>
            </div>

            <hr style="margin: 25px 0px;">

            <div class="row" data-ng-show="selectedDeliveryLocation.dispatchId != null">
                <div class="col s6">
                    <h5 style="margin-top:5px;">Add Items to Invoice</h5>
                </div>
                <div class="col s6">
                    <div class="right">
                        <button class="btn" style="margin-right:5px;" data-ng-if="txnType == 'B2B_SALES'"
                                data-ng-click="searchByRequest()">CLONE FROM REQUEST</button>
                        <button class="btn" data-ng-click="cloneInvoice()"
                                href="#cloneInvoice" modal>CLONE FROM INVOICE</button>
                    </div>
                </div>
                <div class="col s12">
                    <div class="row">
                        <div class="col s4">
                            <label>Select SKU</label>
                            <select class="select2Input" data-ui-select2="select2Options"
                                    data-ng-model="selectedSku" data-ng-change="selectSku(selectedSku)"
                                    data-ng-options="sku as sku.skuData.name for sku in skuList | filter:categoryFilter track by sku.id"></select>
                        </div>
                        <div class="col s2">
                            <label>Select Packaging</label>
                            <select data-ng-model="selectedPkg" data-ng-change="selectPkg(selectedPkg)"
                                    data-ng-disabled="selectedSku.skuData==null"
                                    data-ng-init="selectedPkg = packagingList[0]"
                                    data-ng-options="pkg as pkg.name for pkg in packagingList track by (selectedSku.id + pkg.id)"></select>
                        </div>
                        <div class="col s2">
                            <label>Select Price</label>
                            <input type="number" min="0" step="0.001" data-ng-disabled="txnType != 'SCRAP'" ng-model="selectedPrice">
                        </div>
                        <div class="col s2">
                            <label>Select Quantity</label>
                            <input type="number" min="0" step="0.001" ng-model="selectedQty">
                        </div>
                        <div class="col s2" style="padding-top: 20px;">
                            <button class="btn" data-ng-click="addSku(selectedSku,selectedPackaging,selectedPrice,selectedQty)">Add</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" data-ng-show="invoiceItems!=null">
                <div class="right margin-right-5 margin-bottom-5">
                    <button data-ng-if="invoiceItems!=null" data-ng-click="resetInvoiceItems()"
                            class="btn btn-warning">Clear All</button>
                </div>
                <table id="invoiceItemTable" class="bordered striped po-table">
                    <thead>
                    <tr fsm-sticky-header scroll-body="#invoiceItemTable" scroll-stop='70'>
                        <th class="center-align">SKU</th>
                        <th class="center-align">UOM</th>
                        <th class="center-align">Pkg</th>
                        <th class="center-align">Pkg Qty</th>
                        <th class="center-align">Ratio</th>
                        <th class="center-align">Total Qty</th>
                        <th class="center-align">Price</th>
                        <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                        <th class="center-align">Taxes</th>
                        <th class="center-align" tooltipped data-tooltip="Edit/Remove SKU Item">Actions*</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="(id,item) in invoiceItems track by $index">
                        <td class="center-align" tooltipped data-tooltip="{{item.sku.hsn}}"><a data-ng-click="showPreview($event, item.sku.id,'SKU')">{{item.sku.name}}</a></td>
                        <td class="center-align">{{item.sku.uom}}</td>
                        <td class="center-align">{{item.pkg.name}}</td>
                        <td class="center-align" data-ng-if="!item.edit">{{item.qty.toFixed(2)}}</td>
                        <td class="center-align" data-ng-if="item.edit">
                            <input style="width: 40px;" type="number" min="1" ng-model="item.qty">
                            <button class="btn btn-xs-small" data-ng-click="updateItem(item,id)">Update</button>
                        </td>
                        <td class="center-align">{{item.pkg.ratio.toFixed(2)}}</td>
                        <td class="center-align">{{(item.pkg.ratio * item.qty).toFixed(2)}}</td>
                        <td class="center-align">
                            <!--<input style="width: 40px;" type="number" min="1" ng-model="item.price">-->
                            {{item.price.toFixed(2)}}
                        </td>
                        <td class="center-align">{{item.amount.toFixed(2)}}</td>
                        <td class="center-align" tooltipped data-tooltip="{{item.taxText}}">{{item.appliedTax.toFixed(2)}}</td>
                        <td class="center-align">
                            <button ng-click="removeItem(id)" tooltipped data-tooltip="Remove SKU from List"
                                    class="btn btn-floating red"><i class="material-icons">close</i>
                            </button>
                            <div class="row">
                                <div class="col s4" data-ng-repeat="asset in item.associatedAssetMappings">
                                    <label>Enter 6 digit Asset Tag Value:</label>
                                    <input type="text"

                                           name="assetTag"
                                           data-ng-model="asset.assetTagValue"
                                           data-ng-minlength="0"
                                           data-ng-maxlength="6"
                                           ng-change="validateAssetTagValue(asset.assetTagValue,asset,item)" required/>
                                    <p ng-show="asset.assetTagValue == null" class="errorMessage">Asset Tag Value is required.</p>
                                    <p ng-show="asset.assetTagValue !=  null && asset.assetTagValue.length > 6" class="errorMessage">Asset Tag Value is too large.</p>
                                    <p ng-show="asset.assetTagValue != null && asset.assetTagValue.length != 6" class="errorMessage">Asset Tag Value is too small.</p>
                                    <div data-ng-if="asset.assetTagValue == null || !asset.assetValidated">
                                        <p  class="errorMessage">Enter Valid Asset Tag Value.</p>
                                    </div>
                                </div>
                            </div>
                        </td>

                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row" data-ng-show="invoiceItems!=null && invoiceItems!={}">
                <div class="col s12">
                    <div class="row">
                        <div class="col s8">
                            <div class="row margin0">
                                <div class="col s2">
                                    <label>Comment</label>
                                </div>
                                <div class="col s10">
                                    <textarea data-ng-maxlength="5000" data-ng-model="comment"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col s4">
                            <div class="row">
                                <div class="col s6">
                                    <label>Additional Charges</label>
                                </div>
                                <div class="col s6">
                                    <input type="number" step="0.001" data-ng-model="additional"/>
                                </div>
                            </div>
                            <div class="row margin0">
                                <div class="col s6">
                                    <label style="display: inline-block;margin-bottom:0px;" for="inputCreated">Dispatch Date:</label>
                                </div>
                                <div class="col s6">
                                    <input style="max-width: 200px;" input-date type="text" name="created" id="inputCreated"
                                           data-ng-model="selectedDate" data-ng-change="changeSelectedDate(selectedDate)"
                                           container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                                           max="{{maxDate}}" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col s12">
                    <button class="btn right" data-ng-hide="selectedDate==null || transportId==null"
                            data-ng-click="previewInvoice(invoiceItems, additional)"
                            href="#previewInvoice" modal>PREVIEW & SUBMIT</button>
                    <div class="grey-text right" data-ng-hide="selectedDate!=null && transportId!=null">
                        Make sure dispatch date and vehicle details are selected
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Modal to Preview Invoice Order -->
<!-- Modal Structure -->

<div id="previewInvoice" class="modal modal-large">
    <div class="modal-content">
        <div class="row margin0">
            <h6>Performa Invoice for: <b> {{selectedVendor.entityName}}-{{selectedDispatchLocation.locationName}}</b></h6>
        </div>
        <hr>
        <div class="row" style="width: 98%;">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th class="center-align">SKU</th>
                    <th class="center-align">Unit Of Measure</th>
                    <th class="center-align">Packaging</th>
                    <th class="center-align">Packaging Qty</th>
                    <th class="center-align">Conversion Ratio</th>
                    <th class="center-align">Total Qty</th>
                    <th class="center-align">Price</th>
                    <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                    <th class="center-align">Taxes</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="(id,item) in invoiceItems track by $index">
                    <td class="center-align"><a data-ng-click="showPreview($event, item.sku.id,'SKU')">{{item.sku.name}}</a></td>
                    <td class="center-align">{{item.sku.uom}}</td>
                    <td class="center-align">{{item.pkg.name}}</td>
                    <td class="center-align">{{item.qty.toFixed(2)}}</td>
                    <td class="center-align">{{item.pkg.ratio.toFixed(2)}}</td>
                    <td class="center-align">{{(item.pkg.ratio * item.qty).toFixed(2)}}</td>
                    <td class="center-align">{{item.pkg.price.toFixed(2)}}</td>
                    <td class="center-align">{{item.amount.toFixed(2)}}</td>
                    <td class="center-align">{{item.appliedTax.toFixed(2)}}</td>
                </tr>
                </tbody>
            </table>
            <hr>
            <div class="row">
                <div class="right">
                    <div class="amount-rows"><strong>Taxable Cost:</strong>&nbsp;<div class="right">Rs.{{totalCost.toFixed(2)}}</div></div>
                    <div class="amount-rows"><strong>Total Taxes:</strong>&nbsp;<div class="right">Rs.{{totalTax.toFixed(2)}}</div></div>
                    <div class="amount-rows"><strong>Additional Charges:</strong>&nbsp;<div class="right">Rs.{{additional.toFixed(2)}}</div></div>
                    <div class="amount-rows"><strong>Total Amount:</strong>&nbsp;<div class="right">Rs.{{totalAmount.toFixed(2)}}</div></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat">Cancel</button>
        <button class="modal-action modal-close waves-effect waves-green btn" data-ng-click="submit(invoiceItems)">Submit</button>
    </div>
</div>


<!-- Modal to clone Invoice Orders -->
<!-- Modal Structure -->
<div id="cloneInvoice" class="modal modal-large">
    <div class="modal-content">
        <div class="row margin0">
            <h5 class="col s11">
                Previous Invoices for: <b>{{selectedVendor.entityName}}[{{selectedDispatchLocation.city}}]</b>
            </h5>
            <div class="col s1">
                <button class="modal-action modal-close waves-effect waves-green btn-flat right"><i class="fa fa-close"></i></button>
            </div>
        </div>
        <div class="row" data-ng-if="clonableInvoices !=undefined && clonableInvoices.length>0">
            <ul class="col s12" data-collapsible="accordion" watch>
                <li class="row" data-ng-repeat="inv in clonableInvoices track by inv.id">
                    <div class="col s10 collapsible-header waves-effect waves-light lighten-5">
                        <b>INVOICE#</b>{{inv.id}} created on {{inv.createdAt | date:'dd/MM/yyyy @ h:mma'}}
                        <div class="right">
                            <span class="chip">{{inv.status}}</span>
                            <i class="fa fa-caret-down right"></i>
                        </div>
                    </div>
                    <div class="col s2">
                        <button class="btn btn-small right margin-top-10 modal-action modal-close"
                                data-ng-click="cloneThisInvoice(inv.items)">Clone</button>
                    </div>
                    <div class="collapsible-body">
                        <table class="bordered striped">
                            <thead>
                            <tr>
                                <th class="center-align">SKU</th>
                                <th class="center-align">UOM</th>
                                <th class="center-align">Pkg</th>
                                <th class="center-align">Pkg Qty</th>
                                <th class="center-align">Ratio</th>
                                <th class="center-align">Total Qty</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in inv.items track by $index">
                                <td class="center-align">{{item.sku.name}}</td>
                                <td class="center-align">{{item.uom}}</td>
                                <td class="center-align">{{item.pkg.name}}</td>
                                <td class="center-align">{{item.pkgQty.toFixed(2)}}</td>
                                <td class="center-align">{{item.ratio.toFixed(2)}}</td>
                                <td class="center-align">{{item.qty.toFixed(2)}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
        </div>
        <div class="row" data-ng-if="clonableInvoices==undefined || clonableInvoices==null || clonableInvoices.length==0">
            <p style="text-align: center;">No Previous Invoices available to clone</p>
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
    </div>
</div>
<script type="text/ng-template" id="invReqSearch.html">
    <div class="modal-content" data-ng-init = "initModal()">
        <div class="row margin0">
            <div class="col s12">
                <div class="row">
                    Searching Request Orders for: <b>{{vendor}}</b>
                </div>
                <div class="row">
                    <div class="col s6">
                        <label> Select Requesting Unit</label>
                        <select data-ng-model = "selectedUnit"
                                data-ng-change = "selectUnit(selectedUnit)"
                                data-ng-options="unit as unit.name for unit in units"></select>
                    </div>
                    <div class="col s6">
                        <button style="margin-top: 1.4rem;" class="btn" data-ng-click = "getOrders()">Get Orders</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row margin0" style="min-height:20%;"
             data-ng-hide = "mappedProducts!=null && mappedProducts!={}"
             data-ng-if="orders!=null && orders.length > 0">
            <ul class="collection">
                <li class="collection-item" data-ng-repeat="order in orders">
                    <input type="checkbox" style="position: relative; opacity: 1;left: 0;" data-ng-model="order.checked" data-ng-init="order.checked=false">
                    {{order.id}} for {{order.fulfillmentDate | date:'dd/MM/yyyy'}}
                    <div class="secondary-content">
                        Total Products: {{order.requestOrderItems.length}}
                    </div>
                </li>
            </ul>
        </div>

        <div class="row margin0"
             data-ng-if="mappedProducts!=null && mappedProducts!={}">
            <label>Mapped Products</label>
            <ul class="collection">
                <li class="collection-item" data-ng-repeat="(id,product) in mappedProducts">
                    <div data-ng-if = "product.skus.length == 1">
                        <label>Found {{product.selectedSku.skuData.name}} for {{product.name}}</label>
                    </div>
                    <div data-ng-if="product.skus.length > 1">
                        <label>Select SKU for {{product.name}}</label>
                        <select data-ng-model = "product.selectedSku"
                                data-ng-init="product.selectedSku=product.skus[0]"
                                data-ng-options="sku as sku.skuData.name for sku in product.skus"></select>
                    </div>
                </li>
            </ul>
        </div>
        <div class="footer">
            <button data-ng-if="orders!=null && orders.length>0"
                    data-ng-hide="mappedProducts!=null && mappedProducts!={}"
                    class="btn" data-ng-click="checkOrders()">Club Orders</button>
            <button data-ng-if="mappedProducts!=null && mappedProducts!={}"
                    class="btn" data-ng-click="submitToInvoice()">Add to Invoice</button>
            <button class="btn red right" data-ng-click="cancel()">Close</button>
        </div>
    </div>
</script>
