<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    td,th{
        padding:10px 5px !important;
    }
    .select2.select2-container{width: 100% !important;}
    #images{
        text-align: center;
    }
    img#scanned {
        height: 400px; /** Sets the display size */
        margin-right: 12px;
        margin-top: 20px;
    }
</style>

<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h5 class="left">Create Payment Request</h5>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==true">
        <div class="row">
            <div class="col s4">
                <label>Select Request Type</label>
                <select data-ng-model="prRequestType" data-ng-options="item as item.name for item in prRequestTypes"></select>
            </div>
            <div class="col s8" data-ng-show="prRequestType.shortCode=='GR'">
                <label>Select GR Type</label>
                <span style="margin-right: 20px">
                    <input name="group1" id="t1" type="radio" ng-model="grType" value="INVOICE"/>
                    <label for="t1">Invoice</label>
                </span>
                <span style="margin-right: 20px">
                    <input name="group1" id="t2" type="radio" ng-model="grType" value="DELIVERY_CHALLAN"/>
                    <label for="t2">Delivery Challan</label>
                </span>
            </div>
        </div>

        <div data-ng-show="prRequestType.shortCode=='GR'">

            <div class="row" data-ng-show="grType!=null">
                <div class="col s2">
                    <label>Start date*</label>
                    <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>End date*</label>
                    <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s3">
                    <label>Select Unit*</label>
                    <select ui-select2 id="unitListx" name="unitListx" data-ng-model="selectedUnit"
                            data-ng-change="selectUnit(selectedUnit)"
                            data-ng-options="item as item.name for item in unitList track by item.id"></select>
                </div>
                <div class="col s3">
                    <label>Select Vendor*</label>
                    <select ui-select2 id="vendorListx" name="vendorListx" data-ng-model="selectedVendor"
                            data-ng-options="item as item.name for item in vendorList track by item.id"></select>
                </div>
                <div class="col s2">
                    <input type="button" class="btn" value="Find" data-ng-click="findGrs()" style="margin-top: 25px;" />
                </div>
            </div>

            <div class="row" data-ng-show="grs.length>0">
                <div class="respTable col s12">
                    <table class="row table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>GR Id</th>
                            <th>Company Name</th>
                            <th>Receiving Date</th>
                            <th>Bill Amount</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="gr in grs track by gr.id">
                            <td data-ng-click="">{{gr.id}}</td>
                             <td>{{companyMap[gr.companyId].name}}</td>
                            <td>{{gr.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</td>
                            <td>{{gr.billAmount}}</td>
                            <td data-ng-if="grType=='INVOICE'">
                                <input type="button" class="btn btn-small" value="Create PR" data-ng-click="createPR(gr, false)">
                                <input type="button" acl-action="FPRA" class="btn btn-small" value="Force Create PR" data-ng-click="createPR(gr, true)">
                            </td>
                            <td data-ng-if="grType=='DELIVERY_CHALLAN'">
                                <span>
                                    <input id="gr-{{gr.id}}" type="checkbox" data-ng-model="gr.checked" />
                                    <label for="gr-{{gr.id}}">select</label>
                                </span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <input data-ng-if="grType=='DELIVERY_CHALLAN'" type="button" class="btn" value="Initiate Payment Request" data-ng-click="initiatePR(false)" style="margin-top: 25px;" />
                    <input data-ng-if="grType=='DELIVERY_CHALLAN'" type="button" class="btn" value="Force Initiate Payment Request" data-ng-click="initiatePR(true)" style="margin-top: 25px;" />
                </div>
            </div>
            <div data-ng-if="showNoGR" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No GR with pending payment request found.</div>
        </div>
    </div>

    <div data-ng-show="selectView==false">
        <div class="row">
            <div class="col s12">
                <input type="button" class="btn right" value="BACK" data-ng-click="backToSelectView()" />
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <span data-ng-if="grType=='INVOICE'" style="margin-right: 20px;"><b>Gr Number:</b> {{selectedGr.id}}</span>
                <span><b>Vendor:</b> {{selectedGr.generatedForVendor.name}}</span>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                        <tr>
                            <th>Sku Id</th>
                            <th>Sku Name</th>
                            <th>Packaging</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Amount</th>
                            <th>Tax</th>
                            <th>Total Tax</th>
                            <th>Total Amount</th>
                            <th data-ng-if="grType=='INVOICE'">Deviations</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-repeat="item in paymentRequest.paymentInvoice.paymentInvoiceItems">
                            <td><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuId}}</a></td>
                            <td>{{item.skuName}}[{{item.hsn}}]</td>
                            <td>{{item.packagingName}}</td>
                            <td>{{item.quantity}}</td>
                            <td>{{item.unitPrice}}</td>
                            <td>{{item.totalPrice}}</td>
                            <td><span data-ng-repeat="tax in item.taxes">{{tax.taxType}}@{{tax.taxPercentage}}%,</span></td>
                            <td>{{item.totalTax}}</td>
                            <td>{{item.totalAmount}}</td>
                            <td data-ng-if="grType=='INVOICE'">
                                <span data-ng-repeat="deviation in item.deviations" style="background:#efefef;border:#ddd 1px solid;border-radius: 2px;">
                                    {{deviation.paymentDeviation.deviationDetail}}: {{deviation.deviationRemark}}
                                    <span style="cursor: pointer;" data-ng-click="removeDeviation(item.deviations, $index)">&times</span>
                                </span>
                                <button style="width:30px;" class="btn btn-small" data-ng-click="setAvailableDeviations(item,'INVOICE_ITEM')"
                                        data-target="itemDevModal" modal>&plus;</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col s6">
                <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                    <p><b>Created By:</b> {{createdByUser.name}}</p>
                    <p><b>Requesting Unit:</b> {{createdByUnit.name}}</p>
                    <p><b>Basic Amount:</b> {{paymentRequest.paymentInvoice.calculatedInvoiceAmount}}</p>
                    <p><b>Other Charges:</b> {{paymentRequest.paymentInvoice.extraCharges}}</p>
                    <p><b>Total Bill Amount:</b> {{paymentRequest.paymentInvoice.paymentAmount}}</p>
                    <p><b>Proposed Amount:</b> {{paymentRequest.proposedAmount}}</p>
                </div>
            </div>
            <div class="col s6" data-ng-if="grType=='INVOICE'">
                <p><label>Invoice Number:</label> <input type="text" data-ng-model="paymentRequest.paymentInvoice.invoiceNumber" /></p>
                <p><label>Invoice Date:</label> <input input-date type="text" ng-model="paymentRequest.paymentInvoice.invoiceDate" container="" format="yyyy-mm-dd" /></p>
                <p>
                    <span>
                    	<input id="companyInvoiceUploadId" type="checkbox" data-ng-model="companyConfirmed" data-ng-disabled ="uploadedDocData!=null" />
	                    <label for="companyInvoiceUploadId" data-ng-class="{'stpl-data': selectedGr.companyId ==1000, 'dkc-data': selectedGr.companyId == 1001}">Attach Invoice Of {{companyMap[selectedGr.companyId].name}} </label>
                	</span>
                    <span data-ng-if="companyConfirmed">
	                    <input type="button" value="Scan Doc" class="btn btn-small" data-target='scanModal' modal data-ng-click="resetScanModal()" />
	                    <input type="button" value="Snapshot" class="btn btn-small" data-target='snapModal' modal modal data-ng-click="resetSnapModal()" />
	                    <input type="button" value="Upload File" data-ng-click="uploadDoc()" class="btn btn-small" />
                        <input type="button" value="Preview Invoice" data-ng-if="uploadedDocData!=null"
                               data-ng-click="previewPRInvoice(uploadedDocData)" class="btn btn-small" data-target="invoicePreviewModal" modal />
                        <input type="button" value="Download Invoice" data-ng-if="uploadedDocData!=null"
                               data-ng-click="downloadPRInvoice(uploadedDocData)" class="btn btn-small" />
                    </span>
                </p>
            </div>
        </div>
        <div class="row">
            <div class="col s6" data-ng-if="grType=='INVOICE'">
                <label data-ng-if="paymentRequest.paymentInvoice.deviations.length > 0">Deviations</label>
                <div data-ng-repeat="deviation in paymentRequest.paymentInvoice.deviations"
                     style="background: #ddd; padding: 10px; position: relative; margin: 5px 0;">
                    <b>{{deviation.paymentDeviation.deviationDetail}}</b>: {{deviation.deviationRemark}}
                    <span data-ng-click="removeDeviation(paymentRequest.paymentInvoice.deviations, $index)"
                          style="position:absolute;top:0;right:0;padding:5px 10px;cursor:pointer;font-size:24px;">&times;</span>
                </div>
                <input type="button" value="Add Deviation" class="btn" data-target="invoiceDevModal"
                       data-ng-click="setAvailableDeviations(paymentRequest.paymentInvoice,'INVOICE')" modal />
            </div>
            <div class="col s6" data-ng-if="grType=='INVOICE'">
                <label>Remarks</label>
                <textarea data-ng-model="paymentRequest.remarks"></textarea>
            </div>
        </div>
        <div class="row" data-ng-if="grType=='INVOICE'">
            <div class="col s12">
                <span>
                    <input id="amountsMatch" type="checkbox" data-ng-model="paymentRequest.amountsMatch" />
                    <label for="amountsMatch">Amounts match</label>
                </span>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <input type="button" data-ng-if="grType=='INVOICE'" class="btn" value="Create Payment Request" data-ng-click="submitPaymentRequest()" />
                <input type="button" data-ng-if="grType=='DELIVERY_CHALLAN'" class="btn" value="Initiate Payment Request" data-ng-click="submitPaymentRequest()" />
            </div>
        </div>
    </div>

</div>

<div id="snapModal" class="modal">
    <div class="modal-content">
        <h4>Take Snapshot</h4>
        <video data-ng-show="snapRunning" id="video" width="640" height="480" autoplay></video><br />
        <button data-ng-click="startSnap()" class="btn btn-small">Start</button>
        <button data-ng-click="snapPicture()" class="btn btn-small">Snap Photo</button><br />
        <canvas data-ng-hide="snapRunning" id="canvas" width="640" height="480"></canvas><br />
    </div>
    <div class="modal-footer">
        <button data-ng-click="uploadFile()" class="btn btn-small modal-action modal-close">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Cancel</button>
    </div>
</div>

<div id="scanModal" class="modal">
    <div class="modal-content">
        <h3>Scan document</h3>
        <button type="button" data-ng-click="scanToPng()">Scan</button>
        <div id="images" style="margin-top: 20px;height: 480px;overflow: auto;">
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="uploadScannedFile()">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="invoiceDevModal" class="modal">
    <div class="modal-content">
        <h5>Add deviations</h5>
        <div class="row" data-ng-repeat="dev in availableInvoiceDevs track by $index">
            <div class="col s4">{{dev.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="dev.remark" /></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="{{$index}}" data-ng-model="dev.checked" />
                    <label for="{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(paymentRequest.paymentInvoice, 'INVOICE')">Submit</button>
    </div>
</div>

<div id="itemDevModal" class="modal">
    <div class="modal-content">
        <h5>Add deviations</h5>
        <div class="row" data-ng-repeat="dev in availableItemDevs track by $index">
            <div class="col s4">{{dev.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="dev.remark" /></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="{{$index}}" data-ng-model="dev.checked" />
                    <label for="{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(selectedItemForDeviation, 'INVOICE_ITEM')">Submit</button>
    </div>
</div>

<div id="invoicePreviewModal" class="modal">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <div id="invoicePreview" style="max-height: 370px; overflow: auto;"></div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" style="margin-right: 20px;">Close</button>
    </div>
</div>