<style>
    .amount-rows{
        padding: 5px;
        font-size: 20px;
    }

    .modal-large{
       width:90% !important;
    }

    .modal-medium{
        width:50% !important;
    }
</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 searchingCard custom-listing-li">

            <div class="row">
                <div class="col s6">
                    <h4>Create Purchase Order</h4>
                </div>

                <div class="col s6">

                    <div class="form-element" style="padding-top: 24px;margin-bottom: 0px;">
                        <input
                                id="isForFixedAsset"
                                data-ng-model="isForFixedAsset"
                                data-ng-change="toggleFixedAssetFlag();selectedQty = 0"
                                type="checkbox" />
                        <label
                                class="black-text"
                                for="isForFixedAsset">For Fixed Asset</label>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col s6">
                    <div class="row">
                        <div class="col s4">
                            <b>Unit for Delivery</b>
                        </div>
                        <div class="col s8">
                            <div class="left">{{currentUnit.name}}</div>
                        </div>
                    </div>
                </div>
                <div class="col s6">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Type</b>
                        </div>
                        <div class="col s8">
                            <select data-ng-model="selectedType" data-ng-change="validateHandOverDate(selectedType)">
                                <option value="OPEX">OPEX</option>
                                <option value="CAPEX">CAPEX</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" data-ng-if="displaySelectVendor">
                <div class="col s5">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Vendor</b>
                        </div>
                        <div class="col s8">
                            <select ui-select2="selectedVendor" id="vendorList" name="vendorList" data-ng-model="selectedVendor"
                                    data-ng-options="vendor as vendor.entityName for vendor in vendorList track by vendor.vendorId"
                                    data-ng-change="selectVendor(selectedVendor)"></select>
                        </div>
                    </div>
                </div>
                <div class="col s7" data-ng-if="locationList.length>0 && selectedVendor.status=='ACTIVE'">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Dispatch Location </b>
                        </div>
                        <div class="col s6">
                            <select id="locationList" name="locationList" data-ng-model="selectedLocation"
                                    data-ng-options="location as (location.city + ' (' + location.locationName + ')')
                                                        for location in locationList track by location.dispatchId"
                                    data-ng-change="selectDispatchLocation(selectedLocation)"></select>
                        </div>
                        <div class="col s2">
                            <button data-ng-click="getPriceAndTaxData()" style="width: 75px;" class="btn btn-xs-small">Search</button>
                        </div>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row" data-ng-if="selectedDispatchLocation.dispatchId != null">
                <div class="col s12">
                    <div class="row">
                        <div class="col s3">
                            <h5 style="margin-top:5px;">Add Items to PO</h5>
                        </div>
                        <div class="col s9">
                            <div class="row margin0">
                                <div class="col s6">
                                    <div class="row margin0" style="margin-top:5px;">
                                        <label style="display: inline-block;margin-bottom:0px;" for="inputCreated">Delivery Date:</label>
                                        <input style="max-width: 200px;" input-date type="text" name="created" id="inputCreated"
                                               data-ng-model="selectedDate" data-ng-change="changeSelectedDate(selectedDate)"
                                               container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                                               max="{{maxDate}}" />
                                    </div>
                                </div>
                                <div class="col s6">
                                    <button class="btn" data-ng-click="clonePO()" href="#clonePO" modal>CLONE PO</button>
                                    <button data-ng-show="poItems!=null && poItems!={}" class="btn" data-ng-click="previewPO(poItems)">PREVIEW & SUBMIT</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col s12">
                    <div class="row">
                        <div class="col s6">
                            <label>Add Comments</label>
                            <input type="text" data-ng-model="poComment" data-ng-change="addComment(poComment)"/>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col s5">
                            <label>Select SKU</label>
                            <select
                                    ui-select2
                                    data-ng-model="selectedSku"
                                    data-ng-options="sku as sku.skuData.name for sku in skuList  | filter:categoryFilter | orderBy : 'skuData.name' track by sku.id"
                                    data-ng-change="selectSku(selectedSku)"></select>
                        </div>
                        <div class="col s2">
                            <label>Select Packaging</label>
                            <select data-ng-model="selectedPkg" data-ng-change="selectPkg(selectedPkg)" data-ng-disabled="selectedSku.skuData==null"
                                    data-ng-options="pkg as pkg.name for pkg in packagingList track by (selectedSku.id + pkg.id)"></select>
                        </div>
                        <div class="col s2">
                            <label>Select Quantity </label>
                            <input  type="number" step="0.001" data-ng-model="selectedQty">
                            <span style="color: red;" data-ng-if="selectedQty < 0">Enter a value greater than or equal to 0</span>
                        </div>
                        <div class="col s3" style="padding-top: 20px;">
                            <button class="btn waves-effect waves-light" data-ng-disabled="selectedQty < 0"
                                    data-ng-click="addSku(selectedSku,selectedPackaging,selectedQty)">Add</button>
                            <button data-ng-if="selectedSku!=null"
                                    class="btn waves-effect waves-light red lighten-2"
                                    tooltipped data-tooltip="Click to see consumption trend"
                                    data-ng-click="openChart(selectedSku)">
                                {{selectedSku.totalConsumed}}<i style="line-height: 30px;" class="tiny material-icons">info_outline</i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row" data-ng-init="initCharges()">
                <br>
                <div class="col s9" style="margin-left:105px;">
                    <table class="bordered striped" data-ng-show="poItems!=null">
                        <thead>
                            <tr>
                                <th>Total Tax</th>
                                <th>Taxable Amount</th>
                                <th>Total Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{totalTaxIn.toFixed(2)}}</td>
                                <td>{{totalCostIn.toFixed(2)}}</td>
                                <td>{{totalAmountIn.toFixed(2)}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <br>
            </div>
            <hr>
            <div class="respTable standardView">
            <div class="row" data-ng-show="poItems!=null">
                <table id="poItemTable" class="bordered striped po-table">
                    <thead>
                        <tr fsm-sticky-header scroll-body="#poItemTable" scroll-stop='65'>
                            <th class="center-align">S.No</th>
                            <th class="center-align">SKU</th>
                            <th class="center-align">UOM</th>
                            <th class="center-align">Pkg</th>
                            <th class="center-align">Pkg Qty</th>
                            <th class="center-align">Ratio</th>
                            <th class="center-align">Total Qty</th>
                            <th class="center-align">Price</th>
                            <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                            <th class="center-align">Taxes</th>
                            <th class="center-align" tooltipped data-tooltip="Edit/Remove SKU Item">Actions*</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-repeat="(id,item) in poItems track by $index">
                            <td class="center-align">{{$index+1}}</td>
                            <td class="center-align" tooltipped data-tooltip="{{item.sku.hsn}}"><a data-ng-click="showPreview($event, item.sku.id,'SKU')">{{item.sku.name}}</a></td>
                            <td class="center-align">{{item.sku.uom}}</td>
                            <td class="center-align">{{item.pkg.name}}</td>
                            <td class="center-align" data-ng-if="!item.edit">{{item.qty.toFixed(2)}}</td>
                            <td class="center-align" data-ng-if="item.edit">
                                <input style="width: 40px;" type="number" ng-model="item.qty">
                                <button class="btn btn-xs-small" data-ng-disabled="item.qty < 0" data-ng-click="updateItem(item,id)">Update</button>
                                <span style="color: red;" data-ng-if="item.qty < 0">Enter a value greater than or equal to 0</span>
                            </td>
                            <td class="center-align">{{item.pkg.ratio.toFixed(2)}}</td>
                            <td class="center-align">{{(item.pkg.ratio * item.qty).toFixed(2)}}</td>
                            <td class="center-align">{{item.pkg.price.toFixed(2)}}</td>
                            <td class="center-align">{{item.amount.toFixed(2)}}</td>
                            <td class="center-align" tooltipped data-tooltip="{{item.taxText}}">{{item.appliedTax.toFixed(2)}}</td>
                            <td class="center-align">
                                <button data-ng-show="!item.edit" ng-click="editItem(id)" tooltipped data-tooltip="Edit Packaging Quantity"
                                        class="btn btn-floating green"><i class="material-icons">edit</i></button>
                                <button ng-click="removeItem(id)" tooltipped data-tooltip="Remove SKU from List"
                                        class="btn btn-floating red"><i class="material-icons">close</i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            </div>

            <div class="TableMobileView" id="poItemTable"  data-ng-show="poItems!=null">
                <ul class="collection striped" data-ng-show="vendorList.length>0">
                    <li class="collection-item" data-ng-repeat="(id,item) in poItems track by $index">
                        <div class="row">
                        <div class="col">SKU</div>
                            <div class="col" tooltipped data-tooltip="{{item.sku.hsn}}"><a data-ng-click="showPreview($event, item.sku.id,'SKU')">{{item.sku.name}}</a></div>
                        </div>
                        <div class="row">
                        <div class="col">UOM</div>
                            <div class="col">{{item.sku.uom}}</div>
                        </div>
                        <div class="row">
                        <div class="col">Pkg</div>
                            <div class="col">{{item.pkg.name}}</div>
                        </div>
                        <div class="row">
                        <div class="col">Pkg Qty</div>
                            <div class="col" data-ng-if="!item.edit">{{item.qty.toFixed(2)}}</div>
                            <div class="col" data-ng-if="item.edit">
                                <input style="width: 40px;" type="number" ng-model="item.qty">
                                <button class="btn btn-xs-small" data-ng-disabled="item.qty < 0" data-ng-click="updateItem(item,id)">Update</button>
                                <span style="color: red;" data-ng-if="item.qty < 0">Enter a value greater than or equal to 0</span>
                            </div>
                        </div>
                        <div class="row">
                        <div class="col">Ratio</div>
                            <div class="col">{{item.pkg.ratio.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                        <div class="col">Total Qty</div>
                            <div class="col">{{(item.pkg.ratio * item.qty).toFixed(2)}}</div>
                        </div>
                        <div class="row">
                        <div class="col">Price</div>
                            <div class="col">{{item.pkg.price.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                        <div class="col" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</div>
                            <div class="col">{{item.amount.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                        <div class="col">Taxes</div>
                            <div class="col" tooltipped data-tooltip="{{item.taxText}}">{{item.appliedTax.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                        <div class="col" tooltipped data-tooltip="Edit/Remove SKU Item">Actions*</div>
                            <div class="col">
                                <button data-ng-show="!item.edit" ng-click="editItem(id)" tooltipped data-tooltip="Edit Packaging Quantity"
                                        class="btn btn-floating green"><i class="material-icons">edit</i></button>
                                <button ng-click="removeItem(id)" tooltipped data-tooltip="Remove SKU from List"
                                        class="btn btn-floating red"><i class="material-icons">close</i></button>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>


<!-- Modal to clone Purchase Orders -->
<!-- Modal Structure -->
<div id="clonePO" class="modal modal-large">
    <div class="modal-content">
        <div class="row margin0">
            <h5 class="col s11">
                Previous Purchase Orders for: <b>{{selectedVendor.entityName}}[{{selectedDispatchLocation.city}}]</b>
            </h5>
            <div class="col s1">
                <button class="modal-action modal-close waves-effect waves-green btn-flat right"><i class="fa fa-close"></i></button>
            </div>
        </div>
        <div class="row" data-ng-if="clonablePOs !=undefined && clonablePOs.length>0">
            <ul class="col s12" data-collapsible="accordion" watch>
                <li class="row" data-ng-repeat="po in clonablePOs | filter:poFilter  track by po.id">
                    <div class="col s10 collapsible-header waves-effect waves-light lighten-5">
                        {{po.receiptNumber}} created on {{po.generationTime | date:'dd/MM/yyyy @ h:mma'}}
                        <div class="right">
                            <span class="chip">{{po.status}}</span>
                            <i class="fa fa-caret-down right"></i>
                        </div>
                    </div>
                    <div class="col s2">
                        <button class="btn btn-small right margin-top-10 modal-action modal-close"
                                data-ng-click="cloneThisPO(po.orderItems)">Clone</button>
                    </div>
                    <div class="collapsible-body">
                        <table class="bordered striped">
                            <thead>
                            <tr>
                                <th class="center-align">S.No</th>
                                <th class="center-align">SKU</th>
                                <th class="center-align">UOM</th>
                                <th class="center-align">Pkg</th>
                                <th class="center-align">Pkg Qty</th>
                                <th class="center-align">Total Qty</th>
                                <th class="center-align">Price</th>
                                <th class="center-align" tooltipped data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*</th>
                                <th class="center-align">Taxes</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in po.orderItems track by $index">
                                <td class="center-align">{{$index+1}}</td>
                                <td class="center-align">{{item.skuName}}</td>
                                <td class="center-align">{{item.unitOfMeasure}}</td>
                                <td class="center-align">{{item.packagingName}}</td>
                                <td class="center-align">{{item.packagingQty}}</td>
                                <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                                <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                <td class="center-align">{{item.totalCost}}</td>
                                <td class="center-align">{{item.totalTax}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
        </div>
        <div class="row" data-ng-if="clonablePOs==undefined || clonablePOs==null || clonablePOs.length==0">
            <p style="text-align: center;">No Previous Orders available to clone</p>
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
    </div>
</div>




<!-- Modal to Preview Purchase Order -->
<!-- Modal Structure -->
<script type="text/ng-template" id="previewPO.html">
    <div id="previewPO" data-ng-init="initPreview()">
        <div class="modal-content" style="overflow-x: auto; max-height: 350px;">
            <div class="row">
                <h5>Preview Order for: <b>{{selectedVendor.entityName}}[{{selectedDispatchLocation.city}}]</b></h5>
            </div>
            <div class="row" style="width: 98%;">
                <table class="bordered striped standardView">
                    <thead>
                    <tr>
                        <th class="center-align">S.No</th>
                        <th class="center-align">SKU</th>
                        <th class="center-align">Unit Of Measure</th>
                        <th class="center-align">Packaging</th>
                        <th class="center-align">Packaging Qty</th>
                        <th class="center-align">Conversion Ratio</th>
                        <th class="center-align">Total Qty</th>
                        <th class="center-align">Price</th>
                        <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                        <th class="center-align">Taxes</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="(id,item) in poItems track by $index">
                        <td class="center-align">{{$index+1}}</td>
                        <td class="center-align"><a data-ng-click="showPreview($event, item.sku.id,'SKU')">{{item.sku.name}}</a></td>
                        <td class="center-align">{{item.sku.uom}}</td>
                        <td class="center-align">{{item.pkg.name}}</td>
                        <td class="center-align">{{item.qty.toFixed(2)}}</td>
                        <td class="center-align">{{item.pkg.ratio.toFixed(2)}}</td>
                        <td class="center-align">{{(item.pkg.ratio * item.qty).toFixed(2)}}</td>
                        <td class="center-align">{{item.pkg.price.toFixed(2)}}</td>
                        <td class="center-align">{{item.amount.toFixed(2)}}</td>
                        <td class="center-align">{{item.appliedTax.toFixed(2)}}</td>
                    </tr>
                    </tbody>
                </table>
                <div class="bordered striped TableMobileView">
                    <ul class="collection striped Mobilemodal">
                    <li class="collection-item" data-ng-repeat="(id,item) in poItems track by $index">
                        <div class="row">
                            <div class="col">SKU</div>
                            <div class="col"><a data-ng-click="showPreview($event, item.sku.id,'SKU')">{{item.sku.name}}</a></div>
                        </div>
                        <div class="row">
                            <div class="col">Unit Of Measure</div>>
                            <div class="col">{{item.sku.uom}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Packaging</div>
                            <div class="col">{{item.pkg.name}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Packaging Qty</div>
                            <div class="col">{{item.qty.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Conversion Ratio</div>
                            <div class="col">{{item.pkg.ratio.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Total Qty</div>
                            <div class="col">{{(item.pkg.ratio * item.qty).toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Price</div>
                            <div class="col">{{item.pkg.price.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</div>
                            <div class="col">{{item.amount.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Taxes</div>
                            <div class="col">{{item.appliedTax.toFixed(2)}}</div>
                        </div>
                    </li>
                    </ul>
                    <tbody>
                    <tr >









                    </tr>
                    </tbody>
                </div>
                <hr>
                <div class="row">
                    <div class="right">
                        <div class="amount-rows"><strong>Taxable Cost:</strong>&nbsp;<div class="right">Rs.{{totalCost.toFixed(2)}}</div></div>
                        <div class="amount-rows"><strong>Total Taxes:</strong>&nbsp;<div class="right">Rs.{{totalTax.toFixed(2)}}</div></div>
                        <div class="amount-rows"><strong>Total Amount:</strong>&nbsp;<div class="right">Rs.{{totalAmount.toFixed(2)}}</div></div>
                        <div class="amount-rows"><strong>Lead Time(Days):</strong>&nbsp;<div class="right">{{skuLeadTime}}</div></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer right">
            <button class="waves-effect waves-green btn-flat" data-ng-click="close()">Cancel</button>
            <button class="waves-effect waves-green btn" data-ng-click="submit()">Submit</button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="consumption.html">
    <div class="row" data-ng-init="initChart()">
        <div class="col s12">
            <hc-chart options="chartOptions">Placeholder for generic chart</hc-chart>
        </div>
        <div class="col s12"><b>Stock At Hand:</b> {{sku.currentStock}} {{sku.skuData.uom}}</div>
        <div data-ng-if="pendingForSku != null" class="col s12">
            <span><b>Open POs:</b> {{(pendingForSku.poList!=null && pendingForSku.poList.length>0) ? pendingForSku.poList.join(", ") : 'None'}}</span><br/>
            <span><b>Pending Quantity:</b> {{(pendingForSku.requested - pendingForSku.received).toFixed(2)}}</span>
        </div>
    </div>
</script>

<script type="text/ng-template" id="departmentPOModal.html">
    <div class="modal-header">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col s12">
                <div data-ng-if="emptyCheck">
                    <h5>No Budget Found </h5>
                    <br>
                    <div class="row">
                        <button class="btn red pull-right"  data-ng-click="cancel()">Close</button>
                    </div>
                </div>
                <br>
            </div>
            <div class="col s12" data-ng-if="!emptyCheck">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Department Name</th>
                        <th>Total Cost</th>
                        <th>Total Tax</th>
                        <th>Total Amount</th>
                        <th>Original Amount</th>
                        <th>Budget Amount</th>
                        <th>Remaining Amount</th>
                        <th>Running Amount</th>
                        <th>Receiving Amount</th>
                        <th>Paid Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                        <tr ng-class="{'red': totalAmount > budgetDetails.remainingAmount}">
                        <td style="color: black;">{{budgetDetails.departmentName}}</td>
                        <td style="color: black;">{{totalCost.toFixed(2)}}</td>
                        <td style="color: black;">{{totalTax.toFixed(2)}}</td>
                        <td style="color: black;">{{totalAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.originalAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.budgetAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.remainingAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.runningAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.receivingAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.paidAmount.toFixed(2)}}</td>
                    </tr>
                    </tbody>
                </table>
                <br>
                <div class="row">
                    <button class="btn red"  data-ng-click="cancel()">Cancel</button>
                    <button class="btn green" style="float: right;" data-ng-click="submit()">Settle</button>
                </div>
            </div>
        </div>
    </div>
</script>