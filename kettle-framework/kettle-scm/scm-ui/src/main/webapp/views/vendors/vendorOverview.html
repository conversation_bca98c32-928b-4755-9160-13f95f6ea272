<style>
    #vendor-overview li.tab {
        color: #fff !important;
        text-align: center;
        padding: 0 !important;
    }

    #vendor-overview li.tab a {
        background-color: #e84a17;
        color: #fff;
        display: block;
        padding: 0.75rem;
        text-transform: uppercase;
        border: 1px solid #e84a17;
        border-left: 1px solid #fff;
        border-right: 1px solid #fff;
    }

    #vendor-overview li.tab a.active {
        color: #e84a17;
        font-weight: 700;
        background-color: #fff;
        border: 1px solid #e84a17;
    }

    .bold {
        font-weight: 700;
    }

    #vendor-overview #basic .col {
        padding: 0 0.75rem;
    }

    #vendor-overview .locationDiv {
        background-color: #f6f6f6;
        padding: 1rem;
    }

    .uppercase-text {
        font-size: 12px;
        word-break: break-all;
        text-transform: uppercase;
    }
</style>
<div id="vendor-overview" class="row white z-depth-3" data-ng-init="init()">
    <div class="col s12">
        <h3 class="left"><span data-ng-if="!editMode">View</span><span data-ng-if="editMode">Edit</span> Vendor Details
        </h3>
        <button class="btn right margin-top-20" data-ng-click="goBack()">Back</button>
    </div>
    <div class="col s12">
        <h5 class="left">{{basicDetail.entityName}} ({{basicDetail.type}}) &nbsp; <span class="chip">{{basicDetail.status}}</span>
        </h5>
        <div class="right" data-ng-if="showApproveBtn">
            <input style="margin-top:25px; max-width: 150px; display: inline;"
                   data-ng-change="changeCreditDays(creditDays)" type="number" placeholder="Enter Credit Days"
                   data-ng-model="creditDays" min="0"/>
            <button class="btn" data-ng-click="approve(creditDays)">Approve</button>
        </div>
    </div>
    <div class="row center margin-top-20" data-ng-if="basicDetail==null">
        <h5 class="alert"> No record found. Please verify if vendor has been added correctly.</h5>
    </div>
    <div class="row" data-ng-if="basicDetail!=null">
        <div class="row">
            <div class="col s12">
                <ul tabs reload="allTabContentLoaded">
                    <li class="tab col s3"><a class="active" href="#basic">Basic Details</a></li>
                    <li class="tab col s3"><a href="#company">Company Details</a></li>
                    <li class="tab col s3"><a href="#account">Account Details</a></li>
                    <li class="tab col s3"><a href="#locations">Dispatch Locations</a></li>
                </ul>
            </div>
        </div>
        <div class="row">
            <div id="basic" class="col s12">
                <div class="row">
                    <div class="col s6"
                         data-ng-repeat="(key,value) in basicDetail | hideFilter:'companyDetails,accountDetails,dispatchLocations,registrationId,link,vendorEditedData'">
                        <div class="row">
                            <div class="col s6">
                                <span class="bold" data-change-form data-ng-model="key" ng-style="isEdited(key) && {'background-color': 'yellow'}"></span> :
                            </div>
                            <div class="col s6">
                               <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="requestedBy" >
                                        {{value.name}}
                                    </span>
                                    <span ng-switch-when="updatedBy">
                                        {{value.name}}
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-when="vendorAddress" create-address data-ng-model="value"></span>
                                    <span ng-switch-default>
                                        {{value}}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" data-ng-if="basicDetail.status == 'ACTIVE'" type="button"
                            ng-click="partialdeactivateVendor(basicDetail.vendorId)"
                            style="float: right;margin-top: 15px;margin-right: 20px;">Deactivate Vendor
                    </button>
                    <button class="btn btn-primary"
                            data-ng-if="basicDetail.status == 'PENDING_APPROVAL_FOR_DEACTIVATION'" type="button"
                            ng-click="reactivateVendor(basicDetail.vendorId)"
                            style="float: right;margin-top: 15px;margin-right: 50px;">REACTIVATE
                    </button>
                    <button class="btn btn-primary"
                            data-ng-if="basicDetail.status == 'PENDING_APPROVAL_FOR_DEACTIVATION'" type="button"
                            ng-click="fullydeactivateVendor(basicDetail.vendorId)"
                            style="float: right;margin-top: 15px;margin-right: 20px;">DEACTIVATE
                    </button>
                </div>
            </div>
            <div id="company" class="col s12">
                <div class="row" data-ng-if="basicDetail.companyDetails!=null">
                    <div class="col s6"
                         data-ng-repeat="(key,value) in basicDetail.companyDetails | hideFilter:'basicDetail,companyId,updatedBy,vendorDetail'">
                        <div class="row" data-ng-if="value!=null">
                            <div class="col s6">
                                <span class="bold" data-change-form data-ng-model="key" ng-style="isEdited(key) && {'background-color': 'yellow'}"></span> :
                            </div>
                            <div class="col s6">
                               <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="panDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="arcDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="cinDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="cstDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="serviceTaxDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="vatDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                   <span ng-switch-when="msmeDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>

                                    <span ng-switch-when="updateTime">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-when="companyAddress" create-address data-ng-model="value"></span>
                                    <span ng-switch-default>
                                        {{value}}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row center" data-ng-if="basicDetail.companyDetails==null">
                    No Company Details found for this Vendor
                </div>
            </div>
            <div id="account" class="col s12">
                <div class="row" data-ng-if="basicDetail.accountDetails!=null">
                    <div class="col s6"
                         data-ng-repeat="(key,value) in basicDetail.accountDetails | hideFilter:'basicDetail,accountId,paymentCycle,updatedBy,vendorDetail'">
                        <div class="row">
                            <div class="col s6">
                                <span class="bold" data-change-form data-ng-model="key" ng-style="isEdited(key) && {'background-color': 'yellow'}"></span> :
                            </div>
                            <div class="col s6">
                               <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="cancelledCheque">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                    <span ng-switch-default>
                                        {{value}}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <input type="button" class="btn" value="Block Payments" acl-action="VNBLKP"
                           data-ng-if="!basicDetail.accountDetails.paymentBlocked"
                           data-ng-click="blockPayments(basicDetail)"/>
                    <input type="button" class="btn" value="Un Block Payments" acl-action="VNUBLP"
                           data-ng-if="basicDetail.accountDetails.paymentBlocked"
                           data-ng-click="unBlockPayments(basicDetail)"/>
                </div>
                <div class="row center" data-ng-if="basicDetail.accountDetails==null">
                    No Account Details found for this Vendor
                </div>
            </div>
            <div id="locations" class="col s12">
                <div class="row locationDiv" data-ng-if="basicDetail.dispatchLocations.length!=0"
                     data-ng-repeat="location in basicDetail.dispatchLocations">
                    <h4>{{location.locationName}}</h4>
                    <button class="btn btn-primary" type="button">Deactive Vendor</button>
                    <hr>
                    <div class="col s6"
                         data-ng-repeat="(key,value) in location | hideFilter:'vendorDetail,locationId,locationName,updatedBy'">
                        <div class="row">
                            <div class="col s6">
                                <span class="bold" data-change-form data-ng-model="key" ng-style="isEdited(key) && {'background-color': 'yellow'}"></span> :
                            </div>
                            <div class="col s6">
                               <span class="uppercase-text" ng-switch="key">
                                    <span ng-switch-when="address" create-address data-ng-model="value"></span>
                                    <span ng-switch-when="notificationType">
                                        <span data-ng-repeat="notification in value"
                                              class="chip">{{notification}}</span>
                                    </span>
                                    <span ng-switch-when="updateTime">
                                        {{value | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>

                                    <span ng-switch-when="applyTax">
                                        {{value ? "YES": "NO"}}
                                    </span>

                                    <span ng-switch-when="gstinDocument">
                                        {{value.documentLink}} <i class="pointer fa fa-download fa-2x"
                                                                  data-ng-click="download(value)"></i>
                                    </span>

                                    <span ng-switch-default>
                                        {{value}}
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row center" data-ng-if="basicDetail.dispatchLocations.length==0">
                    No Dispatch Locations found for this Vendor
                </div>
            </div>
        </div>
    </div>
</div>