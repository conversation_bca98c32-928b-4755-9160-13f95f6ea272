<style>
.grid {
	width: 900px;
	height: 550px;
	padding: 0 !important;
}

.modalgrid {
	width: 900px;
	height: 220px;
	padding: 0 !important;
}

.popeye-modal-container .popeye-modal{
    width: 900px;
    height: 400px;
	}
	
</style>
<div
	class="row white z-depth-3 custom-listing-li"
	data-ng-init="init()">
	<div class="col s12">
		<h3>Cost Element to Vendor Mapping</h3>
	</div>
	<div class="col s12 m12 l6 xl6">
		<!-- <div class="row margin0">
			<label class="black-text">Category</label> <select
				ui-select2="selectedCategoryType"
				data-ng-model="mappingType"
				data-ng-options="category as category.name for category in categories track by category.listDetailId"
				data-ng-change="getSubCategory(mappingType)">
			</select>
		</div>
		<div class="row margin0">
			<label class="black-text">Sub Category</label> <select
				ui-select2="selectedSubCategoryType"
				data-ng-model="subCat"
				data-ng-options="mapping as mapping.name for mapping in subCategories track by mapping.listTypeId"
				data-ng-change="getSubSubCategory(subCat)">
			</select>
		</div>
		<div class="row margin0">
			<label class="black-text">Sub Sub-Category</label> <select
				ui-select2="selectedMappingType"
				id="mappingTypeId"
				name="mappingType"
				data-ng-model="subSubCat"
				data-ng-options="mapping as mapping.name for mapping in subSubCategories track by mapping.listDataId"
				data-ng-change="getCostElement(subSubCat)">
			</select>
		</div> -->
		<div class="row margin0">
			<label class="black-text">Cost Element</label> <select
				ui-select2="selectedMappingType"
				id="mappingTypeId"
				name="mappingType"
				data-ng-model="selectedCostElement"
				data-ng-options="mapping as mapping.name for mapping in costElements track by mapping.id"
				data-ng-change="searchMappings(selectedCostElement)">
			</select>
		</div>
		
	</div>
	<div
		class="col s12">
		<div class="row">
			<div class="col s6 m6 l6 xl6">
				<button
					data-ng-disabled="selectedCostElement == null"
					data-ng-click="searchMappings()"
					class="btn left"
					class="btn left">Search</button>
			</div>
			<div class="col s6 m6 l6 xl6">
				<span 
				data-ng-if="selectedCostElement != 'undefined' && selectedCostElement.id > 0"
				data-ng-click="openCostElementToVendorMapModal()"
					> <a href="#addNewMapping"
						class="btn right"
					   modal
						style="margin-right: 10px"><b>+</b> Add New Mapping</a>
				</span>
			</div>
		</div>
	</div>
	</div>
	<div
	class="row"
	id="gridView"
	style="padding: 10px 0;">
	<div
		class="col s12"
		ng-show="selectedCostElement != null">
		<div
			class="col s6 card-panel red lighten-2"
			align="left">
			<h6 class="white-text center">
				<b>Updating Mappings for {{selectedCostElement.name}}</b>
			</h6>
	</div>
	</div>
	<div
		class="col s12"
		data-ng-if="gridOptions.data != null">
		<div
			id="mappingsGrid"
			ui-grid="gridOptions"
			ui-grid-resize-columns
			ui-grid-move-columns
			class="grid col s12"></div>
	</div>
	</div>


<div
	id="addNewMapping"
	class="modal modal-mx">
	<div class="modal-content">
		<div class="row">
			<div class="col s12">
				<h5>Add Cost Element to Vendor Mapping</h5>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label
					class="black-text">Cost Element Name: </label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<label class="black-text"> {{selectedCostElement.name}} </label>
			</div>
			<div class="row">
				<div class="col s12 m12 l3 xl3">
					<label
						class="black-text">Vendor Name: </label>
				</div>
				<div class="col s12 m12 l9 xl9">
					<select
					id="modalValueDataId"
						ui-select2="selectedValue"
						name="modalValueData"
					    data-ng-model="selectedVendor"
						data-ng-options="vendor as vendor.name for vendor in vendors | orderBy : 'name' track by vendor.id"
						style="width: 100%">
					</select>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col s12">
				<button
					class="waves-effect waves-green btn right"
					style="margin-right: 10px"
					data-ng-click="addToModalGridData()">
					<b>+</b> Add
				</button>
			</div>
		</div>
		<div
			class="row"
			id="modalGridView">
			<div
				class="col s12"
				data-ng-if="!hideModalGrid">
				<div
					id="gridSaveState"
					ui-grid="modalGridOptions"
					class="modalgrid col s12"
					ui-grid-resize-columns
					ui-grid-move-columns></div>
			</div>
		</div>
		<div class="row">
			<div class="col s12">
				<button
					class="modal-action modal-close waves-effect waves-green btn right"
					style="margin-right: 10px"
					ng-click="submitModalGridData()">Submit</button>
				<button
					class="modal-action modal-close waves-effect waves-green btn right"
					style="margin-right: 10px"
					ng-click="cancelModal()">Cancel</button>
			</div>
		</div>
	</div>
</div>
<script
	type="text/ng-template"
	id="statusChangeRemoveButton.html">
      <div class="ui-grid-cell-contents">
		<button 
			data-ng-class="{'btn btn-xs-small':row.entity.mappingStatus=='IN_ACTIVE', 'btn red btn-xs-small':row.entity.mappingStatus=='ACTIVE'}"
			ng-click="grid.appScope.changeStatus(row.entity)"
			ng-if="row.entity.mappingStatus!='NA'">
			<span ng-if="row.entity.mappingStatus=='IN_ACTIVE'">Activate</span>
			<span ng-if="row.entity.mappingStatus=='ACTIVE'">Deactivate</span>
		</button>
		<button 
			class="btn btn-xs-small" 
			ng-click="grid.appScope.removeRow(row.entity)"
			ng-if="row.entity.mappingStatus=='NA'">
			<span>Remove</span>
		</button>
      </div>
</script>
<script
		type="text/ng-template"
		id="skuIdTemplate.html">
	<div class="ui-grid-cell-contents">
		<a style="cursor: pointer" data-ng-click="grid.appScope.showPreview($event, row.entity.id,'SKU')">{{row.entity.id}}</a>
	</div>
</script>