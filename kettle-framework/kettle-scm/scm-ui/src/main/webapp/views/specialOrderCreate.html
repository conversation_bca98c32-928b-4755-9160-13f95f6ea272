<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Specialized Ordering</h3>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12 m6 l6">
            <label>Select Date:</label>
            <input input-date type="text" ng-model="fulfillmentDate"
                   container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                   max="{{maxDate}}" />
        </div>
        <div class="col s12 m6 l6">
            <input type="button" class="btn" value="GUIDE ME" data-ng-click="getEstimateQty()" style="margin-top: 20px;" />
        </div>
    </div>


    <div class="row" data-ng-if="productList.length>0">
        <div class="row">
            <div class="col s12">
                <ul class="collection">
                    <li class="collection-item list-head">
                        <div class="row" style="margin-bottom: 0px;">
                            <div class="col s4">Product Name</div>
                            <div class="col s2">Requested Qty.</div>
                            <!--<div class="col s1">Requested Absolute Qty.</div>-->
                            <div class="col s2">Packaging Qty</div>
                            <div class="col s2">Packaging Name</div>
                            <div class="col s2">Select Vendor</div>
                        </div>
                    </li>
                    <li class="collection-item" data-ng-repeat="roi in productList track by $index" data-ng-if="vendorList[roi.productId].length > 0">
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s4">{{roi.productName}} ({{roi.unitOfMeasure}})</div>
                            <div class="col s2">{{roi.requestedQuantity}}</div>
                            <div class="col s2"><input data-ng-change="calculateRequestQty(roi)"
                                    data-ng-disabled="vendorList[roi.productId]==null || vendorList[roi.productId].length == 0"
                                    data-ng-model="roi.packagingQuantity" type="number" min="0"/>
                            </div>
                            <div class="col s2">{{roi.packagingName}}</div>
                            <!--<div class="col s2" data-ng-if="vendorList[roi.productId] == null || vendorList[roi.productId].length == 0">
                                No vendor mapped
                            </div>-->
                            <div class="col s2" data-ng-if="vendorList[roi.productId]!=null && vendorList[roi.productId].length > 0">
                                <select data-ng-model="roi.vendor"
                                        data-ng-change = "changeVendor(roi,roi.vendor)"
                                        data-ng-init = "changeVendor(roi,vendorList[roi.productId][0])"
                                        data-ng-options="vendor as vendor.entityName for vendor in vendorList[roi.productId] track by vendor.vendorId"></select>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row">
            <div class="col s12 m6 l6">
                <div class="form-element">
                    <label>Comment(optional)</label>
                    <textarea data-ng-model="comment"></textarea>
                </div>
            </div>
        </div>

        <div class="col s12 form-element">
            <input type="button" class="btn" value="RESET" data-ng-click="clearProductList()" />
            <!--<input type="button" class="btn" value="SUBMIT" data-ng-click="createRoObject()" />-->
            <a class='btn' href='#previewModal' modal>PREVIEW</a>
        </div>
    </div>

    <!-- Modal Structure -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <h4>Order Preview</h4>
            <ul class="collection">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s4">Product Name</div>
                        <div class="col s2">Requested Qty.</div>
                        <div class="col s2">Packaging Qty</div>
                        <div class="col s2">Packaging Name</div>
                        <div class="col s2">Selected Vendor</div>
                    </div>
                </li>
                <li class="collection-item" data-ng-repeat="roi in productList track by $index" data-ng-if="roi.requestedQuantity>0">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s4">{{roi.productName}} ({{roi.unitOfMeasure}})</div>
                        <div class="col s2">{{roi.requestedQuantity}}</div>
                        <div class="col s2">{{roi.packagingQuantity}}</div>
                        <div class="col s2">{{roi.packagingName}}</div>
                        <div class="col s2">{{roi.vendor!=null?roi.vendor.entityName:''}}</div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="modal-footer">
            <button class="modal-action modal-close btn" data-ng-click="createRoObject()">SUBMIT</button>
            <button class="modal-action modal-close btn red left" >CLOSE</button>
        </div>
    </div>

</div>

