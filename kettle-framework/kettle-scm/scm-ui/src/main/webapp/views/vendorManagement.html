<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Vendor Management</h3>
            </div>
            <div acl-action="SVMVED" class="row right" style="
    display: inline; font-size: 16px;
"> Send Vendor RO Notification : <button  data-ng-click="sendRONotification()" style="width: 100px;" class="btn btn-xs-medium">Send </button></div>
        </div>
    </div>

    <!--<div class="row">
        <label>Search Vendor</label>
        <select ui-select2  ng-options="vendor as vendor for vendor in vendorList"></select>
    </div>-->
    <div class="row">
        <div class="col s4">
            <label class="black-text" for="selectedStatus">Search By Status</label>
            <select ui-select2="selectedStatus" id="selectedStatus" name="selectedStatus" data-ng-model="selectedStatus"
                    data-ng-options="status for status in vendorStatusList"
                    data-ng-change="getVendors(selectedStatus)"></select>
        </div>
        <div class="col s4" data-ng-if="vendorList.length>0">
            <label class="black-text">Search By Name</label>
            <input type="text" id="vendorName" name="vendorName" data-ng-model="searchText.text" ng-change="changeSearchText(searchText.text)">
        </div>
        <div class="col s4">
            <label>Select Vendor</label>
            <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" data-ng-model="vendorSelected"
                    ng-options="vendor as vendor.name for vendor in vendors"
                    data-ng-change="getSelectedVendor(vendorSelected)"></select>
        </div>
        <div class="row" data-ng-show="vendorList.length>0">
            <div class="col s6">
                Filter: <input
                    type="text"
                    ng-model="search"
                    ng-change="filter()"
                    placeholder="Filter"
                    class="form-control"/>
            </div>
            <div class="col s6">
                Results per page: <select
                    data-ng-model="entryLimit"
                    class="form-control">
                <option value="50">50</option>
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="100">100</option>
                <option value="200">200</option>
                <option value="500">500</option>
                <option value="1000">1000</option>
                <option value="2000">2000</option>
            </select>
            </div>
        </div>
        <div class="col s12">
            <ul class="collection striped " data-ng-show="vendorList.length>0">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s1">Id
                            <div>
                                <span><a style="color: #05141c" ng-click="sort_by('id');">Sort By Id</a></span>
                            </div>
                        </div>
                        <div class="col s2">First Name</div>
                        <div class="col s1">Last Name</div>
                        <div class="col s2">Entity Name</div>
                        <div class="col s1">Status</div>
                        <div class="col s2">Action</div>
                        <div class="col s1">Lead Time</div>
                        <div class="col s1" data-ng-if="selectedStatus == 'ACTIVE'">Credit Cycle</div>
                        <div  class="col s1">TDS</div>
                    </div>
                </li>
                <li class="collection-item clickable"

                data-ng-repeat="vd in filtered = (vendorList | filter:search | filter : byEntityName | orderBy : 'vendorId' | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit track by vd.vendorId">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s1">{{vd.vendorId}}</div>
                        <div class="col s2">{{vd.firstName}}</div>
                        <div class="col s1">{{vd.lastName}}</div>
                        <div class="col s2">{{vd.entityName}}</div>
                        <div class="col s1" style="word-wrap: break-word;">{{vd.status}}</div>
                        <div class="col s2">
                            <a class="btn btn-small vBtn" data-ng-click="viewVendor(vd)" acl-action="SVMVVI" >View</a><br>
                            <a ng-if="vd.status == 'COMPLETED' || vd.status == 'ACTIVE'" class="btn btn-small vBtn" style="margin-top:5px"
                               data-ng-click="generateEditRequest(vd)" acl-action="SVMVED">Edit</a>
                            <a ng-if="vd.link != null && (vd.status == 'INITIATED' || vd.status == 'IN_PROCESS')" style="margin-top:5px"
                               class="btn btn-small vBtn" href="{{vd.link}}" target="_blank" acl-action="SVMVED">Edit
                                Page</a>
                        </div>
                        <div class="col s1" data-ng-if="vd.status == 'ACTIVE'">
                            <input type="number" id="leadTime" data-ng-model="vd.leadTime" placeholder="Lead Time" style="width:56px;margin-left: -25px">
                            <a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-sub-menu="VOMRPO"
                               data-ng-click="editLeadTime(vd.vendorId,vd.leadTime)" >&#x2705;</a>
                        </div>
                        <div class="col s1" data-ng-if="vd.status == 'ACTIVE'">
                                <input type="number" id="creditCyle" data-ng-model="vd.creditDays" placeholder="Credit Days" style="width:56px;margin-left: -25px">
                                  <a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-action="PRACK"
                                        data-ng-click="editCreditCycle(vd.companyId,vd.creditDays)" >&#x2705;</a>
                            </div>
                        <div  class="col s1">
                            <select  id="tdsApplicable"
                                    name="IsTDS" data-ng-model="vd.tds"
                                    data-ng-change="isTdsApplicable(vd.tds,$index)"
                                    validate="notnull" required>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                            </select>
                            <div>
                            <div data-ng-if="vd.tds!=null">
                                <button class="btn" data-ng-click="uploadTDS($index)">Upload</button>
                            </div>
                            <div  data-ng-if="vd.tdsDocument!=null">
                                <span>{{vd.tdsDocument.documentLink}}</span>
                                <i class="material-icons pointer" data-ng-click="vd.tdsDocument=null">edit</i>
                            </div>
                            </div>
                        </div>
                        </div>
                </li>
            </ul>
    </div>
        <!--<div class="TableMobileView">-->
        <!--<ul class="collection striped" data-ng-show="vendorList.length>0">-->
            <!--<li class="collection-item"  data-ng-repeat="vd in vendorList | filter : byEntityName | orderBy : 'vendorId' track by vd.vendorId">-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Id</div> <div class="col s1">{{vd.vendorId}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">First Name</div>  <div class="col s2">{{vd.firstName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">Last Name</div>  <div class="col s1">{{vd.lastName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">Entity Name</div>  <div class="col s2">{{vd.entityName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Status</div> <div class="col s1" style="word-wrap: break-word;">{{vd.status}}</div>-->
                <!--</div>-->
                <!--<div class="row" ng-if="vd.status == 'COMPLETED' || vd.status == 'ACTIVE' || vd.link != null && (vd.status == 'INITIATED' || vd.status == 'IN_PROCESS')">-->
                    <!--<div class="col s2">Action</div> <div class="col s2">-->
                    <!--<a class="btn btn-small vBtn" data-ng-click="viewVendor(vd)" acl-action="SVMVVI" >View</a><br>-->
                    <!--<a ng-if="vd.status == 'COMPLETED' || vd.status == 'ACTIVE'" class="btn btn-small vBtn" style="margin-top:5px"-->
                       <!--data-ng-click="generateEditRequest(vd)" acl-action="SVMVED">Edit</a>-->
                    <!--<a ng-if="vd.link != null && (vd.status == 'INITIATED' || vd.status == 'IN_PROCESS')" style="margin-top:5px"-->
                       <!--class="btn btn-small vBtn" href="{{vd.link}}" target="_blank" acl-action="SVMVED">Edit-->
                        <!--Page</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row" data-ng-if="vd.status == 'ACTIVE'">-->
                    <!--<div class="col s1">Lead Time</div>-->
                    <!--<div class="col s1" data-ng-if="vd.status == 'ACTIVE'">-->
                        <!--<input type="number" id="leadTime" data-ng-model="vd.leadTime" placeholder="Lead Time" style="width:56px;margin-left: -25px">-->
                        <!--<a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-sub-menu="VOMRPO"-->
                           <!--data-ng-click="editLeadTime(vd.vendorId,vd.leadTime)" >&#x2705;</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row" data-ng-if="vd.status == 'ACTIVE' || selectedStatus == 'ACTIVE'">-->
                    <!--<div class="col s1" data-ng-if="selectedStatus == 'ACTIVE'">Credit Cycle</div>-->
                    <!--<div class="col s1" data-ng-if="vd.status == 'ACTIVE'">-->
                        <!--<input type="number" id="creditCyle" data-ng-model="vd.creditDays" placeholder="Credit Days" style="width:56px;margin-left: -25px">-->
                        <!--<a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-action="PRACK"-->
                           <!--data-ng-click="editCreditCycle(vd.companyId,vd.creditDays)" >&#x2705;</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div  class="col s1">TDS</div>-->
                    <!--<div  class="col s1">-->
                        <!--<select  id="tdsApplicable"-->
                                 <!--name="IsTDS" data-ng-model="vd.tds"-->
                                 <!--data-ng-change="isTdsApplicable(vd.tds,$index)"-->
                                 <!--validate="notnull" required>-->
                            <!--<option value="true">Yes</option>-->
                            <!--<option value="false">No</option>-->
                        <!--</select>-->
                        <!--<div>-->
                            <!--<div data-ng-if="vd.tds!=null">-->
                                <!--<button class="btn" data-ng-click="uploadTDS($index)">Upload</button>-->
                            <!--</div>-->
                            <!--<div  data-ng-if="vd.tdsDocument!=null">-->
                                <!--<span>{{vd.tdsDocument.documentLink}}</span>-->
                                <!--<i class="material-icons pointer" data-ng-click="vd.tdsDocument=null">edit</i>-->
                            <!--</div>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</li>-->
        <!--</ul>-->
        <!--</div>-->

        <div class="col s12 ">
            <ul class="collection striped" data-ng-show="vendorDetail != null">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s1">Id</div>
                        <div class="col s2">First Name</div>
                        <div class="col s1">Last Name</div>
                        <div class="col s2">Entity Name</div>
                        <div class="col s1">Status</div>
                        <div class="col s2">Action</div>
                        <div class="col s1">Lead Time</div>
                        <div class="col s1" data-ng-if="selectedStatus == 'ACTIVE'">Credit Cycle</div>
                    </div>
                </li>
                <li class="collection-item clickable">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s1">{{vendorDetail.vendorId}}</div>
                        <div class="col s2">{{vendorDetail.firstName}}</div>
                        <div class="col s2">{{vendorDetail.lastName}}</div>
                        <div class="col s2">{{vendorDetail.entityName}}</div>
                        <div class="col s1" style="word-wrap: break-word;">{{vendorDetail.status}}</div>
                        <div class="col s2">
                            <a class="btn btn-small vBtn" data-ng-click="viewVendor(vendorDetail)" acl-action="SVMVVI" >View</a>
                            <a ng-if="vendorDetail.status == 'COMPLETED' || vendorDetail.status == 'ACTIVE'" class="btn btn-small vBtn" style="margin-top:5px"
                               data-ng-click="generateEditRequest(vendorDetail)" acl-action="SVMVED">Edit</a>
                            <a ng-if="vendorDetail.link != null && (vendorDetail.status == 'INITIATED' || vendorDetail.status == 'IN_PROCESS')" style="margin-top:5px"
                               class="btn btn-small vBtn" href="{{vendorDetail.link}}" target="_blank" acl-action="SVMVED">Edit
                                Page</a>
                        </div>
                        <div class="col s1" data-ng-if="vendorDetail.status == 'ACTIVE'">
                            <input type="number" id="leadTime" data-ng-model="vendorDetail.leadTime" placeholder="Lead Time" style="width:56px;margin-left: -25px">
                            <a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-sub-menu="VOMRPO"
                               data-ng-click="editLeadTime(vendorDetail.vendorId,vendorDetail.leadTime)" >&#x2705;</a>
                        </div>
                        <div class="col s1" data-ng-if="vendorDetail.status == 'ACTIVE'">
                            <input type="number" id="creditCyle" data-ng-model="vendorDetail.companyDetails.creditDays" placeholder="Credit Days" style="width:56px;margin-left: -25px">
                            <a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-action="PRACK"
                               data-ng-click="editCreditCycle(vendorDetail.companyDetails.companyId,vendorDetail.creditDays)" >&#x2705;</a>
                        </div>

                    </div>
                </li>
            </ul>
        </div>
    <!--<div class="TableMobileView">-->
        <!--<ul class="collection" data-ng-show="vendorDetail != null">-->
            <!--<li class="collection-item clickable">-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Id</div>-->
                    <!--<div class="col s1">{{vendorDetail.vendorId}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">First Name</div>-->
                    <!--<div class="col s2">{{vendorDetail.firstName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Last Name</div>-->
                    <!--<div class="col s2">{{vendorDetail.lastName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">Entity Name</div>-->
                    <!--<div class="col s2">{{vendorDetail.entityName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Status</div>-->
                    <!--<div class="col s1" style="word-wrap: break-word;">{{vendorDetail.status}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">Action</div>-->
                    <!--<div class="col s2">-->
                        <!--<a class="btn btn-small vBtn" data-ng-click="viewVendor(vendorDetail)" acl-action="SVMVVI">View</a>-->
                        <!--<a ng-if="vendorDetail.status == 'COMPLETED' || vendorDetail.status == 'ACTIVE'"-->
                           <!--class="btn btn-small vBtn" style="margin-top:5px"-->
                           <!--data-ng-click="generateEditRequest(vendorDetail)" acl-action="SVMVED">Edit</a>-->
                        <!--<a ng-if="vendorDetail.link != null && (vendorDetail.status == 'INITIATED' || vendorDetail.status == 'IN_PROCESS')"-->
                           <!--style="margin-top:5px" class="btn btn-small vBtn" href="{{vendorDetail.link}}" target="_blank"-->
                           <!--acl-action="SVMVED">Edit-->
                            <!--Page</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row" data-ng-if="vendorDetail.status == 'ACTIVE'">-->
                    <!--<div class="col s1">Lead Time</div>-->
                    <!--<div class="col s1" data-ng-if="vendorDetail.status == 'ACTIVE'">-->
                        <!--<input type="number" id="leadTime" data-ng-model="vendorDetail.leadTime" placeholder="Lead Time"-->
                               <!--style="width:56px;margin-left: -25px">-->
                        <!--<a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-sub-menu="VOMRPO"-->
                           <!--data-ng-click="editLeadTime(vendorDetail.vendorId,vendorDetail.leadTime)">&#x2705;</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row" data-ng-if="selectedStatus == 'ACTIVE' || vendorDetail.status == 'ACTIVE'">-->
                    <!--<div class="col s1" data-ng-if="selectedStatus == 'ACTIVE'">Credit Cycle</div>-->
                    <!--<div class="col s1" data-ng-if="vendorDetail.status == 'ACTIVE'">-->
                        <!--<input type="number" id="creditCyle" data-ng-model="vendorDetail.companyDetails.creditDays"-->
                               <!--placeholder="Credit Days" style="width:56px;margin-left: -25px">-->
                        <!--<a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-action="PRACK"-->
                           <!--data-ng-click="editCreditCycle(vendorDetail.companyDetails.companyId,vendorDetail.creditDays)">&#x2705;</a>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</li>-->
        <!--</ul>-->
    <!--</div>-->
    </div>
</div>