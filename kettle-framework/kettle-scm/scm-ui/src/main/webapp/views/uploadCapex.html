<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
  <style>
  .popeye-modal{
        width: 65% !important;
    }
	.red{
		color: red;
	}
	.green{
		color: green;
	}
    </style>

<div class="row white z-depth-3 custom-listing-li" style="margin-top:-20px" data-ng-init="init()">
	<div class="col s12">
		<div class="row">
                <div class="right mappingBtn">
                    <a class="btn" data-ng-click="capexModal()">ADD</a>
                </div>
                <div class="mappingBtn" style="margin-top: 10px;font-size: 40px;">
                    Capex List
                </div>
            </div>
	</div>

	<div class="row">
	
		<div class="col s12 margin-bottom-15">
                <div class="col s4">
                    <label>Select Unit</label>
                    <select id="units" ui-select2="{allowClear:true, placeholder: 'Select Unit'}" data-ng-model="capexUnit"  
									data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
                </div>
                <div class="col s3">
                    <label>Select Status</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Status'}" ng-model="capexStatus"
								data-ng-options="statusList as statusList for statusList in statusLists"></select>
                </div>
                <div class="col s2">
                    <label>Select Version</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Version'}"
                            ng-options="version as version for version in versionList"
                            data-ng-model="capexVersion"></select>
                </div>
                <div class="col s1">
                    <button class="btn btn-small margin-top-20" data-ng-click="getCapexRequestList(capexUnit, capexStatus, capexVersion)">Find</button>
                </div>
            </div>
	
		<div class="col s12" data-ng-if="capexList.length==0" style="padding:30px;color:gray;font-size: 20px;">
                <div class="row margin0 center">
                    No Capex Request found for the selected criteria
                </div>
            </div>
	
		<div class="col s12" ng-if="capexList.length>0">
			<ul class="collection menuItemList">
				<li class="collection-item z-depth-1 list-head">
					<div class="row">
						<!-- <div class="col s1">Id</div> -->
						<div class="col s1">Request Id</div>
						<div class="col s1">Type</div>
						<div class="col s1">Unit Name</div>
						<div class="col s1">Access Key</div>
						<div class="col s1">Version</div>
						<div class="col s1">Link</div>
						<div class="col s1">Status</div>
						<div class="col s1">Uploaded Amount</div>
						<div class="col s1">Comapre Budget</div>
						<div class="col s1">Download By</div>
						<div class="col s1">Action</div>
					</div>
				</li>
				<li class="collection-item z-depth-1" data-ng-repeat="item in capexList | orderBy:'item.version':true">
					<div class="row">
					    <div class="col s1">{{item.capexRequestId == null || item.capexRequestId == "" ? "NA" : item.capexRequestId}}</div>
						<div class="col s1">{{item.type == null || item.type == "" ? "NA" : item.type}}</div>
						<div class="col s1" style="word-break: break-all;"><a href='' style="text-decoration:underline;" data-ng-click="fetchBudgetForUnit(item,'VIEW')">{{item.unitName == null || item.unitName == "" ? "NA" : item.unitName}}</a></div>
						<div class="col s1" style="word-break: break-word;">{{item.accessKey == null || item.accessKey == "" ? "" : "***"}}{{item.accessKey == null || item.accessKey == "" ? "NA" : item.accessKey.substr(item.accessKey.length - 4)}}</div>
						<div class="col s1">{{item.version == null || item.version == "" ? "NA" : item.version}}</div>
						<!-- <div class="col s2"><a class="btn btn-small vBtn" data-ng-click="downloadfile(item)">Download</a></div> -->
						<div class="col s1"><a href='' style="text-decoration:underline;" data-ng-click="downloadfile(item)">Download File</a></div>
						<div class="col s1" style="word-break: break-all;">{{item.status == null || item.status == "" ? "NA" : item.status}}</div>
						<div class="col s1" data-ng-if="item.uploadedAmount != null">{{item.uploadedAmount}}</div>
						<div class="col s1" data-ng-if="item.uploadedAmount == null">-</div>
						<div class="col s1"><a class="btn btn-medium vBtn" data-ng-click="showBudgetComparisonView(item)" >Compare</a></div>
						<div class="col s1">{{item.downloadedBy == null || item.downloadedBy == "" ? "NA" : item.downloadedBy}}</div>
						<div class="col s2"><a class="btn btn-small vBtn"  data-ng-if="item.status == 'CREATED'" data-ng-click="uploadDoc(item)" CPMDUP>Upload</a>
						<div class="col s2"><a class="btn btn-small vBtn" data-ng-if="item.status == 'DOCUMENT_UPLOADED'" style="margin-left: 20px;" data-ng-click="fetchBudgetForUnit(item,'CLOSED_L1')" acl-action="CPMDCL1">close_L1</a>
						<div class="col s2"><a class="btn btn-small vBtn" data-ng-if="item.status == 'CLOSED_L1'" style="margin-left: 20px;" data-ng-click="fetchBudgetForUnit(item,'CLOSED_L2')" acl-action="CPMDCL2">close_L2</a>
						<div class="col s2"><a class="btn btn-small vBtn" data-ng-if="item.status == 'CLOSED_L2'" style="margin-left: 20px;" data-ng-click="fetchBudgetForUnit(item,'CLOSED_L3')" acl-action="CPMDCL3">close_L3</a>
						<a class="btn btn-small vBtn" data-ng-if="item.status == 'PENDING_APPROVAL_L1'" data-ng-click="approveCapex(item)" acl-action="CPMDAPL1">Approve_L1</a>
						<a class="btn btn-small vBtn" data-ng-if="item.status == 'PENDING_APPROVAL_L2'" data-ng-click="approveCapex(item)" acl-action="CPMDAPL2">Approve_L2</a>
						<a class="btn btn-small vBtn" data-ng-if="item.status == 'PENDING_APPROVAL_L3'" data-ng-click="approveCapex(item)" acl-action="CPMDAPL3">Approve_L3</a>
						<a class="btn btn-small vBtn" style="margin-top:4px;" data-ng-if="item.status == 'CANCELLED' || item.status.includes('REJECTED') || item.status == 'APPROVED' || item.status == 'CANCEL_CLOSURE'" data-ng-click="editRequest(item)" acl-action="CPMDED">Edit</a>
						<!--<a class="btn btn-small vBtn" style="margin-top:34px;margin-left: -10px;" data-ng-if="item.status == 'CREATED'" data-ng-click="changeStatus(item.id,'CANCELLED','')" acl-action="CPMDCAN">Cancel</a>-->
						<a class="btn btn-small vBtn" data-ng-if="item.status == 'PENDING_APPROVAL_L1'" style="margin-top:5px;" data-ng-click="approvalStatusChange(item.id,'REJECTED',item.status.substr(item.status.length - 2),item)" acl-action="CPMDAPL1">Reject_L1</a>
						<a class="btn btn-small vBtn" data-ng-if="item.status == 'PENDING_APPROVAL_L2'" style="margin-top:5px;" data-ng-click="approvalStatusChange(item.id,'REJECTED',item.status.substr(item.status.length - 2),item)" acl-action="CPMDAPL2">Reject_L2</a>
						<a class="btn btn-small vBtn" data-ng-if="item.status == 'PENDING_APPROVAL_L3'" style="margin-top:5px;" data-ng-click="approvalStatusChange(item.id,'REJECTED',item.status.substr(item.status.length - 2),item)" acl-action="CPMDAPL3">Reject_L3</a>
						<a class="btn btn-small vBtn" style="margin-top:3px;" data-ng-if="item.status == 'PENDING_APPROVAL_L1'" data-ng-click="changeStatus(item.id,'CANCELLED','')" acl-action="CPMDCAN">Cancel</a>
						<a class="btn btn-small vBtn" data-ng-if="item.status == 'PENDING_APPROVAL_L1'" data-ng-click="changeStatus(item.id,'ARCHIVED','')"  style="margin-top:4px;"acl-action="CPMDAPL1">Archive</a>
						<div class="col s2"><a class="btn btn-small vBtn" style="height: 40px;;margin-left: -10px;margin-top: 3px;" data-ng-if="item.status == 'APPROVED' || item.status == 'CANCEL_CLOSURE'" data-ng-click="initiateClosureState(item.id,'INITIATE_CLOSURE','')" acl-action="CPMDIC">Initiate Closure</a>
						<div class="col s2"><a class="btn btn-small vBtn" style="height: 40px;margin-left: -25px;" data-ng-if="item.status == 'INITIATE_CLOSURE'" data-ng-click="openDocumentModel(item)">Document Upload</a>
						<div class="col s2"><a class="btn btn-small vBtn" style="height:40px; margin-top:6px;margin-left: -35px;" data-ng-if="item.status == 'INITIATE_CLOSURE'" data-ng-click="changeStatus(item.id,'APPROVED','')" acl-action="CPMDCC">Cancel Closure</a>
						<div class="col s2"><a class="btn btn-small vBtn" style="height:40px; margin-top:6px;margin-left: -50px;" data-ng-if="item.status == 'DOCUMENT_UPLOADED'" data-ng-click="openDocumentModel(item)">Edit Document</a>
						
						</div>
					</div>
				</li>
			</ul>
		</div>
	</div>
</div>

<script type="text/ng-template" id="capexModal.html">
<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Create Capex</h3>
    <hr>
</div>
<div class="modal-body" id="modal-body">
	<div class="row">
		<div class="col s12" style="margin-top: 10px;">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Type</label> <select ng-model="type"
								data-ng-options="type as type for type in types"></select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Unit</label>
                            <select data-ng-model="selectedUnit"  
									data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
						</div>

					</div>
				</div>
			</div>
		</div>
	</div>
	</div>

		<div class="modal-footer">
			<button class="btn btn-primary" type="button"
				ng-click="createCapex(type,selectedUnit)" acl-action="CPMDAD" data-ng-if="showErrors != true">Create</button>
			<button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
		</div>
</script>
<script type="text/ng-template" id="departmentView.html">
<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Department Budget List</h3>
    <hr>
</div>
<div class="modal-body" id="modal-body">
<div class="row">
                <div class="col s12">
                    <table class="table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
							<th>Department Id</th>
                            <th>Department Name</th>
                            <th>Total Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="items in summaryDepartmentList">
							<td>{{items.deptId}}</td>
                            <td>{{items.deptName}}</td>
 							<td>{{items.amount.toFixed(2)}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
				<div class="col s12 right-align">
					<div class="row" style="line-height: 35px;font-size: 17px;">
						<b>Total Amount:</b> Rs.{{amountTotal.toFixed(2)}}<br/> 
					</div>
				</div>
				</div>
				<div class="row">
					<button class="right btn"  data-ng-click="submit()">Submit</button>
					<button class="right btn" style="margin-right:5px;" data-ng-click="cancelUpload()">Cancel</button>
            </div>
	</div>
		</script>

<script type="text/ng-template" id="budgetModal.html">
<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Department Budget List</h3>
	<div data-ng-if="displayItem.lastApprovalDate != null">
	<h6><b>Last Budget Approved Date &nbsp; : &nbsp;<span>{{displayItem.lastApprovalDate | date:"dd-MM-yyyy"}}</span></b></h6>
	</div>
	<button class="right btn btn-xs-small" ng-click="viewClosureView()" data-ng-show="!showExpandedView && !closureCheck" style=" margin-top: -45px;"> <span>Next >></span> </button>
    <hr>
</div>
<div class="modal-body" id="modal-body">
<div class="row">
                <div class="col s12" data-ng-show="!showExpandedView">
                    <table class="table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>Department Name</th>
							<th>Initial Amount</th>
							<th>Original Amount</th>
                            <th>Budget Amount</th>
							<th>Remaining Amount</th>
							<th>Running Amount</th>
							<th>Receiving Amount</th>
							<th>Paid Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="items in summaryDepartmentList">
                            <td>{{items.departmentName}}</td>
							<td>{{items.initialAmount.toFixed(2)| number}}</td>
							<td>{{items.originalAmount.toFixed(2)| number}}</td>
							<td>{{items.budgetAmount.toFixed(2)| number}}</td>
							<td>{{items.remainingAmount.toFixed(2)| number}}</td>
							<td>{{items.runningAmount.toFixed(2)| number}}</td>
							<td>{{items.receivingAmount.toFixed(2)| number}}</td>
							<td>{{items.paidAmount.toFixed(2)| number}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
	<div class="col s12" data-ng-show="showExpandedView && !closureCheck">
		<div class="col s12">
			<div class="col s8">
				<label>Feasibility Report</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Feasibility_Report')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Projected P & L</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Projected_P&L')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Possession Letter</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Possession_Letter')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Drawing Set</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Drawing_Set')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Capex Closure Sheet</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Capex_Closure_Sheet')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Accepted Work Order Copy & BOQ</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Accepted_Work_Order_Copy&BOQ')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Invoice for Advance</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Invoice_for_Advance')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Civil Final Invoice</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Civil_Final_Invoice')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Warranty Paper / Equipment Invoice</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Warranty_Paper')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Site Completion Certificate</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Site_Completion_Certificate')">Download</button>
			</div>
		</div>
		<div class="col s12" style="margin-top: 15px;">
			<div class="col s8">
				<label>Time Spent Report</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" data-ng-click="downloadDocSheet('Time_Spent_Report')">Download</button>
			</div>
		</div>
			<div class="col s12" style="margin-top:20px;">
				<label>Comment*:</label>
				<textarea data-ng-model="comment" disabled required="required"></textarea>
			</div>
	</div>
				</div>
				<div class="row">
					<button class="right btn" data-ng-show="showExpandedView && !closureCheck"  data-ng-click="submit()">Submit</button>
					<button class="right btn" style="margin-right:5px;"  data-ng-click="cancel()">Cancel</button>
            </div>
	</div>
		</script>

<script type="text/ng-template" id="documentModal.html">
<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Unit Capex CLosure Data</h3>
	<hr>
</div>
<div class="modal-body" id="modal-body">
	<div class="row" style="margin-top:30px;">
	<li class="collection-item" data-ng-repeat="sheet in sheetMap">
		<div class="col s12" style="margin-top: -17px;margin-bottom: 10px;">
			<div class="col s8">
				<label>{{sheet.name}}</label>
			</div>
			<div class="col s4">
				<button class="btn btn-small" ng-if="!sheet.status"
					data-ng-click="uploadDocSheet(sheet.name)">Upload</button>
			</div>
			<div class="col s4">
				<button class="btn btn-small" style="background-color: green;" ng-if="sheet.status"
					data-ng-click="uploadDocSheet(sheet.name)">Uploaded</button>
			</div>
		</div>
		</li>
		<div class="col s12" style="margin-top:20px;">
			<label>Comment*:</label>
			<textarea data-ng-model="comment" required="required"></textarea>
		</div>
		<div class="row">
			<button class="right btn" data-ng-click="submit()">Submit</button>
			<button class="right btn" style="margin-right: 5px;"
				data-ng-click="cancel()">Cancel</button>
		</div>
	</div>
</div>
</script>

<script type="text/ng-template" id="budgetComparisionModal.html">
	<div class="modal-header" data-ng-init="init()">
		<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Comparision of Budget</h3>
		<div data-ng-if="currentItem.lastApprovalDate != null">
			<h6><b>Last Budget Approved Date &nbsp; : &nbsp;<span>{{currentItem.lastApprovalDate | date:"dd-MM-yyyy"}}</span></b></h6>
		</div>
		<hr>
	</div>
	<div class="modal-body" id="modal-body">
		<div class="row" style="margin-top:30px;">
			<div data-ng-if="emptyResult">
				<h5>No result Found</h5>
			</div>
			<div data-ng-if="!emptyResult">
				<div class="col s12">
					<table class="table bordered striped" style="border: #ccc 1px solid;">
						<thead>
						<tr>
							<th style="text-align: center">S.No</th>
							<th style="text-align: center">Department Name</th>
							<th style="text-align: center">Last Approved Original Amount</th>
							<th style="text-align: center">Current Original Amount</th>
							<th style="text-align: center">Last Approved Budget Amount</th>
						</tr>
						</thead>
						<tbody>
						<tr data-ng-repeat="item in result track by $index" data-ng-class="{'green' : item.lastApprovedOriginalAmount!=null &&
											item.currentOriginalAmount !=null && item.currentOriginalAmount < item.lastApprovedOriginalAmount,
										'red' :item.lastApprovedOriginalAmount!=null &&
											item.currentOriginalAmount !=null && item.currentOriginalAmount > item.lastApprovedOriginalAmount}">
							<td style="text-align: center;color: black;">{{$index+1}}</td>
							<td style="padding-left:10px;color: black;">{{item.departmentName}}</td>
							<td style="text-align: center;color: black;">{{item.lastApprovedOriginalAmount}}</td>
							<td style="text-align: center;color: black;">{{item.currentOriginalAmount}}</td>
							<td style="text-align: center;color: black;">{{item.lastApprovedBudgetAmount.toFixed(2)| number}}</td>
						</tr>
						</tbody>
					</table>
				</div>
				<div class="col s12 right-align">
					<div class="col s7 left-align">
						<div class="row" style="line-height: 35px;font-size: 17px;">
							<b>Total Of Last Approved Original Amount:</b> Rs.{{currentAndLastApprovedOriginalAmount.sumOfLastApprovedOriginalAmountForApproval.toFixed(2)}}<br/>
						</div>
					</div>
					<div class="col s5 right-align">
						<div class="row" style="line-height: 35px;font-size: 17px;">
							<b>Total Of Current Original Amount:</b> Rs.{{currentAndLastApprovedOriginalAmount.sumOfCurrentOriginalAmountForApproval.toFixed(2)}}<br/>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="row pull-right">
			<button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
		</div>
	</div>
</script>