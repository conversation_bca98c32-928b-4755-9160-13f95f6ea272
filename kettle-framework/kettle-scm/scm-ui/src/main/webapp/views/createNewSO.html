<style>
.amount-rows {
	padding: 5px;
	font-size: 20px;
}

.modal-large {
	width: 80% !important;
}

.modal-medium {
	width: 50% !important;
}

.custom-modal {
	width: 50% !important;
}

.custom-modal ul li div.row {
	margin: 0 !important;
	padding: 5px !important;
	border-radius: 3px !important;
}

.custom-modal ul li div.row div {
	line-height: 28px;
}

.custom-modal ul li:nth-child(even) {
	background-color: #f4f0f0;
}

.custom-col-header {
	font-size: 16px;
	font-weight: 700;
	color: #6a5353;
}

h5 {
	font-weight: 700;
}

ul.unit-collection {
	overflow: auto;
	max-height: 28rem;
}
.red{
color:red;
}
.popeye-modal-container .popeye-modal{
    width: 900px;
	}

#images{
	text-align: center;
}
img#scanned {
	height: 400px; /** Sets the display size */
	margin-right: 12px;
	margin-top: 20px;
}
</style>
<div class="row" data-ng-init="init()">
	<div class="col s12">
		<div class="row white z-depth-3 custom-listing-li">
			<div class="col s12">
				<h4>Create Service Order</h4>
			</div>
			<div class="row">
				<div class="col s6">
					<div class="row">
						<div class="col s5">
							<b>Select Cost Center ({{costCentersSize}}) :</b>
						</div>
						<div class="col s7" data-ng-show="!showExpandedView">
							<select ui-select2 id="centerList" name="centerList"
								data-ng-model="selectedCenter"
								data-ng-options="center as center.name for center in costCenters track by center.id"
								data-ng-change="getVendorList(selectedCenter)"></select>
						</div>
						<div class="col s7" data-ng-show="showExpandedView">
                            <span>{{selectedCenter.name}}</span>
                        </div>
					</div>
				</div>
				<div class="col s6">
					<div class="row">
						<div class="col s5">
							<b>Select Type :</b>
						</div>
						<div class="col s7" data-ng-show="!showExpandedView">
							<select data-ng-model="selectedType" data-ng-change="setCapexOpex()">
								<option value="OPEX">OPEX</option>
      							<option value="CAPEX" acl-action="SORCAP">CAPEX</option>
							</select>
						</div>
						<div class="col s7" data-ng-show="showExpandedView">
                            <span>{{selectedType}}</span>
                        </div>
					</div>
				</div>
			</div>
			<div class="row" data-ng-if="vendorList.length>0">
				<div class="col s6">
					<div class="row">
						<div class="col s5">
							<b>Select Vendor ({{vendorListSize}}) :</b>
						</div>
						<div class="col s7" data-ng-show="!showExpandedView">
							<select ui-select2="selectedVendor" id="vendorList"
								name="vendorList" data-ng-model="selectedVendor"
								data-ng-options="vendor as vendor.entityName for vendor in vendorList track by vendor.vendorId"
								data-ng-change="selectVendorLoc(selectedVendor)"></select>
						</div>
						<div class="col s7" data-ng-show="showExpandedView">
                            <span>{{selectedVendor.entityName}}</span>
                        </div>
					</div>
				</div>
				<div class="col s6" data-ng-if="locationList.length>0">
					<div class="row">
						<div class="col s5">
							<b>Select Dispatch Location ({{locationList.length}}) :</b>
						</div>
						<div class="col s7" data-ng-show="!showExpandedView">
							<select ui-select2 id="locationList" name="locationList"
								data-ng-model="selectedLocation"
								data-ng-options="location as location.name for location in locationList track by location.id"
								data-ng-change="getCostElementList(selectedLocation)"></select>
						</div>
						<div class="col s7" data-ng-show="showExpandedView">
                            <span>{{selectedLocation.name}}</span>
                        </div>
					</div>
				</div>
			</div>
			<div class="row" data-ng-show="!showExpandedView" data-ng-if="selectedLocation.id != null">
				<div class="col s6">
					<div class="row">
						<div class="col s5">
							<b>Select Tag ({{tagNamesListSize}}) :</b>
						</div>
						<div class="col s7" data-ng-show="!showExpandedView">
							<select ui-select2="selectedtag" id="tagList"
								name="vendorList" data-ng-model="selectedTag" data-ng-change="setSelectedTagName(selectedTag)"
								data-ng-options="tag for tag in tagNamesList"></select>
						</div>
						<div class="col s7" data-ng-show="showExpandedView">
                            <span>{{tags.tagName}}</span>
                        </div>
					</div>
				</div>
				<div class="col s6" data-ng-if="selectedLocation.id != null">
					<div class="row">
						 <button class="btn" data-ng-click="cloneSOWithTag()">CLONE SO</button>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col s9" style="margin-left:105px;">
				<div class="row" style="margin-top: 30px;" data-ng-repeat="rcv in receivings">
					<table class="bordered striped">
						<thead>
						<tr>
							<th>Total Tax</th>
							<th>Taxable Amount</th>
							<th>Total Amount</th>
						</tr>
						</thead>
						<tbody>
						<tr>
							<td>{{rcv.totalTax.toFixed(2)}}</td>
							<td>{{rcv.totalCost.toFixed(2)}}</td>
							<td>{{rcv.totalAmount.toFixed(2)}}</td>
						</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
             
		<div class="row white z-depth-3 custom-listing-li" style="padding:5px;" data-ng-show="!showExpandedView"
			 data-ng-if="selectedLocation.id != null">
				<div class="col s12" style="margin-top:30px;">
					<div class="row">
						<div class="col s4" data-ng-if="isRegular == 'REGULAR_SO'">
							Default Allocation :
							<button class="btn btn-xs-small" ng-click="showAllocations()" style="margin-left: 10px">
								Allocate
							</button>
						</div>
						<div class="col s4" data-ng-if="isRegular != 'REGULAR_SO'">
							<button class="btn btn-medium" data-ng-click="downloadSampleBulkSO()">
								Download Sample
							</button>
						</div>
						<div class="col s8 right-align" data-ng-if="selectedType == 'OPEX'">
							<label style="color: red"><b>NOTE : On Changing <span class="yellow">SO type</span> all the Entered Cost Elements Data will be cleared..!</b></label>
							<input type="radio" data-ng-model="isRegular" value="REGULAR_SO" data-ng-change="setSoType(isRegular)" id="regular" >
							<label class="form-check-label" for="regular">
								Regular
							</label>
							<input type="radio" data-ng-model="isRegular" value="BULK_SO" data-ng-change="setSoType(isRegular)" id="bulk">
							<label class="form-check-label" for="bulk">
								Bulk
							</label>
						</div>
					</div>
				</div>
			<div class="row">
				<div class="col s2">
					<label>Select Cost ELement ({{costElementsSize}})</label> <select
						ui-select2 
						data-ng-model="selectedElement"
						data-ng-options="element as element.name for element in costElements track by element.id"
						data-ng-change="selectCostElement(selectedElement)"></select>
				</div>
				<div class="col s2">
					<label>From Date</label>
					<input input-date type="text" class="costElementDate" ng-model="costElementDate" container="" format="yyyy-mm-dd" />
				</div>
				<div class="col s2">
					<label>To Date</label>
					<input input-date type="text" class="costElementToDate" ng-model="costElementToDate" container="" format="yyyy-mm-dd" ng-change="validateDate()" />
				</div>
				<div class="col s2">
					<label>UOM</label> <input type="text" disabled ng-model="uom" />
				</div>
				<div class="col s2">
					<label>Unit Price</label> <input class="selectedUnitPrice" type="text" ng-disabled="isDisabled" min=1
						ng-model="selectedUnitPrice" />
				</div>
				<div class="col s2" data-ng-if="isRegular == 'REGULAR_SO'">
					<label>Quantity</label> <input type="text" class="selectedQuantity"
						data-ng-model="selectedQuantity" />
					<span style="color: red;" data-ng-if="selectedQuantity < 0">Enter a value greater than or equal to 0</span>
				</div>
				<div class="col s2" data-ng-if="isRegular != 'REGULAR_SO'">
					<label>Upload Bulk SO</label>
					<button class="btn btn-medium" data-ng-click="uploadBulkSo()">Upload</button>
				</div>
			</div>
			<div class="row">
				<div class="col s2">
					<button class="btn" data-ng-disabled="selectedQuantity < 0" data-ng-if="isRegular == 'REGULAR_SO'"
							ng-click="addItem(selectedElement)">
						<span>ADD</span>
					</button>
				</div>
			</div>
			<div class="row"
				data-ng-show="serviceItems!=null && serviceItems.length>0">
				<div class="col s12">
					<div class="row">
						<div class="col s12">
							<h5 style="margin-top: 5px;">Add Items to Service Order</h5>
						</div>
					</div>
				</div>
				<div class="col s12">
					<div class="row">
								<table class="highlight">
									<thead>
										<tr>
											<th>S.No</th>
											<th>Cost Element</th>
											<th>Description</th>
											<th>Base Rate</th>
											<th>Tax Rate</th>
											<th>Quantity</th>
											<th>Cost</th>
											<th>Total Tax</th>
											<th>Total Amount</th>
											<th>Action</th>
										</tr>
									</thead>

									<tbody>
										<tr data-ng-repeat="item in serviceItems track by $index">
											<td>{{$index+1}}</td>
											<td tooltipped
												data-tooltip="SAC Code: {{item.ascCode}}, Tax:{{item.taxRate}}">{{item.costElementName}}</td>
											<td> {{item.serviceDescription}}
												<!-- <div data-ng-if="!item.edit">
													{{item.serviceDescription}}</div> -->

											</td>
											<td>{{item.unitPrice}}</td>
											<td>{{item.taxRate}}</td>
											<td data-ng-if="!item.edit">{{item.requestedQuantity.toFixed(2)}}</td>
											 <td data-ng-if="item.edit">
                                <input style="width: 40px;" type="number" ng-model="item.requestedQuantity">
                                <button class="btn btn-xs-small" data-ng-disabled="item.requestedQuantity < 0" data-ng-click="updateItem(item,$index)">Update</button>
												 <span style="color: red;" data-ng-if="item.requestedQuantity < 0">Enter a value greater than or equal to 0</span>
                            </td>
											<td>{{item.totalCost}}</td>
											<td>{{item.totalTax}}</td>
											<td>{{item.amountPaid}}</td>
											<td><button data-ng-if="isRegular == 'REGULAR_SO'" data-ng-show="!item.edit" ng-click="editItem($index)" tooltipped data-tooltip="Edit Quantity"
                                        class="btn btn-floating green"><i class="material-icons">edit</i></button>
                               			 <button ng-click="removeItem(item,$index,serviceItems)" tooltipped data-tooltip="Remove SO item from List"
                                        class="btn btn-floating red"><i class="material-icons">close</i></button> <br>
                                        <a class="btn btn-small vBtn" style="margin-top:6px" data-ng-click="viewAllocatedUnits($index,item)">Allocated</a>
                                        </td>
											
											<!--<td><a
												class="btn btn-small vBtn"
												data-ng-click="viewAllocatedUnits($index)">Allocated</a> <br><a class="btn btn-small vBtn" style="margin-left:5px"
												data-ng-click="removeItem(serviceItems,$index)">Remove</a> </td>
											<!-- <td><button class="btn" data-ng-click="removeItem(serviceItems,$index)">Remove</button></td> -->
										</tr>
									</tbody>
								</table>
								<div class="col s12 right">
									<button class="right btn" style="margin-top: 50px" data-ng-click="nextPage()">Next</button>
								</div>
							</div>
				</div>
			</div>
		</div>
		
		 <div id="expandedSoView" class="row white z-depth-3 custom-listing-li"
             style="padding:5px;" data-ng-show="showExpandedView">
              <h5 style="min-height: 35px;">
                <button class="right btn btn-medium" data-ng-click="goBack()">Cancel SO</button>
            </h5>
			<div class="col s12" style="margin-top: -30px;">
				<h4 style="text-align-last: center;">Service Order Detail</h4>
			</div>
			<br>
			 
			<div class="row">
				<div class="col s9" style="margin-left:105px;">
					<div class="row" style="margin-top: 30px;" data-ng-repeat="(key,rcv) in receivings">
						<label style="color:#f50606;text-align-last: center;">Company: {{rcv.company.code}}</label>
						<label style="color:#f50606;text-align-last: center;">State: {{rcv.state.name}}</label>
						<table class="bordered striped">
							<thead>
								<tr>
									<th>S.No</th>
									<th>Cost Center</th>
									<th>Cost Element</th>
									<th>Unit Price</th>
									<th>Quantity</th>
									<th>Tax Rate</th>
									<th>Cost</th>
									<th>Tax</th>
									<th></th>
								</tr>
							</thead>
							<tbody>
								<tr data-ng-repeat="item in rcv.items track by $index">
									<td>{{$index+1}}</td>
									<td>{{item.businessCostCenterName}}</td>
									<td class="pointer" data-tooltip="{{item.serviceDescription}}"
										tooltipped>{{item.costElementName}}[{{item.ascCode}}] ({{item.costElementDate| date :'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})</td>
									<td>{{item.unitPrice}}</td>
									<td>{{item.requestedQuantity}}</td>
									<td>{{item.taxRate.toFixed(2)}}</td>
									<td>{{item.totalCost.toFixed(2)}}</td>
									<td>{{item.totalTax.toFixed(2)}}</td>
									<td class="pointer"
										data-ng-click="removeItemFromRcv($index, rcv, key)"><i
										class="material-icons">clear</i></td>
								</tr>
							</tbody>
						</table>
						<div class="row right-align" style="line-height: 30px;">
            					<b>Taxable Amount:</b> Rs.{{rcv.totalCost.toFixed(2)}}<br/>
           						<b>Total Taxes:</b> Rs.{{rcv.totalTax.toFixed(2)}}<br/>
          						<b>Paid Amount:</b> Rs.{{rcv.totalAmount.toFixed(2)}}
      					  </div>
						<div class="row margin0 left">
							<p>
								<span>
									<input id="documentUploadSO" type="checkbox" data-ng-model="isDocumentUploaded" data-ng-disabled ="uploadedDocData!=null" />
									<label for="documentUploadSO">Upload Document</label>
								</span>
								<span data-ng-if="isDocumentUploaded">
									<input type="button" value="Scan Doc" class="btn btn-small" data-target='scanModal' modal data-ng-click="resetScanModal()" />
									<input type="button" value="Snapshot" class="btn btn-small" data-target='snapModal' modal modal data-ng-click="resetSnapModal()" />
									<input type="button" value="Upload File" data-ng-click="uploadDoc()" class="btn btn-small" />
									<input type="button" value="Preview Document" data-ng-if="uploadedDocData!=null"
										   data-ng-click="previewSoDocument(uploadedDocData)" class="btn btn-small" data-target="soDocumentPreviewModal" modal />
									<input type="button" value="Download Document" data-ng-if="uploadedDocData!=null"
										   data-ng-click="downloadSoDocument(uploadedDocData)" class="btn btn-small" style="width: 100px"/>
								</span>
							</p>
						</div>
						<div class="row margin0 right"
							data-ng-if="receivings != undefined && receivings != {} && rcv.tag" ng-click="addTag(rcv)">
							<button class="btn">ADD TAG</button>
						</div>
      					<div class="row margin0 right" data-ng-if="!rcv.tag">
						<input type="text" placeholder="Name of tag" data-ng-change="addTagName(rcv)" data-ng-model="rcv.tagName" />
						</div>
					</div>
					<div class="row margin0 center"
						data-ng-if="receivings != undefined && receivings != {}">
						<button class="btn" data-ng-click="submitSo(receivings)">Preview
							& Submit</button>
					</div>
				</div>
			</div>
		</div>
		
		
	</div>
</div>


<!-- Cost Allocation Modal -->
<script type="text/ng-template" id="allocateCostModal.html">
    <div class="row" data-ng-init="initCostModal()">
        <h5 style="color:#6a5353;">Allocate Cost</h5>
				<div class="row">
					<div class="col s2">
                        <input type="checkbox" data-ng-click="filterManual()"
       					ng-model="ncrCheck" style="position:inherit;opacity:1"><label>NCR</label>
                    </div>
					<div class="col s2">
                        <input type="checkbox" data-ng-click="filterManual()"
       					ng-model="mumbCheck" style="position:inherit;opacity:1"><label>MUMBAI</label>
                    </div>
					<div class="col s2" style="margin-left:20px">
                        <input type="checkbox" data-ng-click="filterManual()"
       					ng-model="bangCheck" style="position:inherit;opacity:1"><label>BANGLORE</label>
                    </div>
					<div class="col s2" style="margin-left:20px">
						<input type="checkbox" data-ng-click="filterManual()"
							   ng-model="puneCheck" style="position:inherit;opacity:1"><label>Pune</label>
					</div>
					<div class="col s2" style="margin-left:20px">
						<input type="checkbox" data-ng-click="filterManual()"
							   ng-model="naviMumbaiCheck" style="position:inherit;opacity:1"><label>Navi Mumbai</label>
					</div>
					<div class="col s2" style="margin-left:20px">
						<input type="checkbox" data-ng-click="filterManual()"
							   ng-model="chennaiCheck" style="position:inherit;opacity:1"><label>Chennai</label>
					</div>
					<div class="col s2" style="margin-left:20px">
						<input type="checkbox" data-ng-click="filterManual()"
							   ng-model="hyderabadCheck" style="position:inherit;opacity:1"><label>Hyderabad</label>
					</div>
				</div>
<div class="row">
    <div class="col-lg-4">
    	Filter:
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
</div>
        <hr>
        <ul class="collection-with-header unit-collection">
            <li class="collection-header">
                <div class="row">
					<div class="col s2">
                        <span class="custom-col-header">Select</span>
                    </div>
                    <div class="col s6">
                        <span class="custom-col-header">Unit Name</span>
                    </div>
                </div>
            </li>
            <li class="collection-item" data-ng-repeat="unit in units | filter:search | orderBy : 'name'">

                <div data-ng-if="unit.location.name == 'Bangalore'">
					<div class="row" ng-show="bangCheck == true">
				
					<div class="col s2">
                        <input type="checkbox"
       					ng-model="unit.dataCheck" ng-disabled="unit.checkBoxcheck" data-ng-click="isCheck($event, unit.code)" style="position:inherit;opacity:1">
                    </div>
                    <div class="col s6">
                        {{unit.name}} ({{unit.company.code}})
                    </div>
					</div>
                </div>
  				
				<div data-ng-if="unit.location.name == 'Mumbai'">
					<div class="row" ng-show="mumbCheck == true">
				
					<div class="col s2">
                        <input type="checkbox"
       					ng-model="unit.dataCheck" ng-disabled="unit.checkBoxcheck" data-ng-click="isCheck($event, unit.code)" style="position:inherit;opacity:1">
                    </div>
                    <div class="col s6">
                        {{unit.name}} ({{unit.company.code}})
                    </div>
					</div>
                </div>
                
                <div data-ng-if="unit.location.name == 'Gurgaon' || unit.location.name == 'New Delhi' || unit.location.name == 'Noida' || unit.location.name == 'Mohali' || unit.location.name == 'Ambala'
					 || unit.location.name == 'Chandigarh' || unit.location.name == 'Karnal' || unit.location.name == 'Faridabad' || unit.location.name == 'Ghaziabad'">
					<div class="row" ng-show="ncrCheck == true">
					<div class="col s2">
                        <input type="checkbox"
       					ng-model="unit.dataCheck" ng-disabled="unit.checkBoxcheck" data-ng-click="isCheck($event, unit.code)" style="position:inherit;opacity:1">
                    </div>
                    <div class="col s6">
                        {{unit.name}} ({{unit.company.code}})
                    </div>
					</div>
                </div>

				<div data-ng-if="unit.location.name == 'Pune'">
					<div class="row" ng-show="puneCheck == true">

						<div class="col s2">
							<input type="checkbox"
								   ng-model="unit.dataCheck" ng-disabled="unit.checkBoxcheck" data-ng-click="isCheck($event, unit.code)" style="position:inherit;opacity:1">
						</div>
						<div class="col s6">
							{{unit.name}} ({{unit.company.code}})
						</div>
					</div>
				</div>

				<div data-ng-if="unit.location.name == 'Navi Mumbai'">
					<div class="row" ng-show="naviMumbaiCheck == true">

						<div class="col s2">
							<input type="checkbox"
								   ng-model="unit.dataCheck" ng-disabled="unit.checkBoxcheck" data-ng-click="isCheck($event, unit.code)" style="position:inherit;opacity:1">
						</div>
						<div class="col s6">
							{{unit.name}} ({{unit.company.code}})
						</div>
					</div>
				</div>

				<div data-ng-if="unit.location.name == 'Chennai'">
					<div class="row" ng-show="chennaiCheck == true">

						<div class="col s2">
							<input type="checkbox"
								   ng-model="unit.dataCheck" ng-disabled="unit.checkBoxcheck" data-ng-click="isCheck($event, unit.code)" style="position:inherit;opacity:1">
						</div>
						<div class="col s6">
							{{unit.name}} ({{unit.company.code}})
						</div>
					</div>
				</div>

				<div data-ng-if="unit.location.name == 'Hyderabad'">
					<div class="row" ng-show="hyderabadCheck == true">

						<div class="col s2">
							<input type="checkbox"
								   ng-model="unit.dataCheck" ng-disabled="unit.checkBoxcheck" data-ng-click="isCheck($event, unit.code)" style="position:inherit;opacity:1">
						</div>
						<div class="col s6">
							{{unit.name}} ({{unit.company.code}})
						</div>
					</div>
				</div>
            </li>
        </ul>
    </div>
    <hr>
    <div class="row">
        <button class="btn right margin-right-5" data-ng-click="submit()">Submit</button>
        <button class="btn right margin-right-5" data-ng-click="cancel()">Cancel</button>
    </div>
</script>

<script type="text/ng-template" id="allocatedCostModal.html">
<!-- Cost Allocation Modal -->
    <div class="row" data-ng-init="initCostModal()">
        <h5 style="color:#6a5353;">Allocated Cost</h5>
				<div class="row">
					<div class="col s2">
                        <input type="checkbox" data-ng-click="filterManual()"
       					ng-model="ncrCheck" style="position:inherit;opacity:1"><label>NCR</label>
                    </div>
					<div class="col s2">
                        <input type="checkbox" data-ng-click="filterManual()"
       					ng-model="mumbCheck" style="position:inherit;opacity:1"><label>MUMBAI</label>
                    </div>
					<div class="col s2" style="margin-left:20px">
                        <input type="checkbox" data-ng-click="filterManual()"
       					ng-model="bangCheck" style="position:inherit;opacity:1"><label>BANGLORE</label>
                    </div>
					<div class="col s2" style="margin-left:20px">
						<input type="checkbox" data-ng-click="filterManual()"
							   ng-model="puneCheck" style="position:inherit;opacity:1"><label>Pune</label>
					</div>
					<div class="col s2" style="margin-left:20px">
						<input type="checkbox" data-ng-click="filterManual()"
							   ng-model="naviMumbaiCheck" style="position:inherit;opacity:1"><label>Navi Mumbai</label>
					</div>
					<div class="col s2" style="margin-left:20px">
						<input type="checkbox" data-ng-click="filterManual()"
							   ng-model="chennaiCheck" style="position:inherit;opacity:1"><label>Chennai</label>
					</div>
					<div class="col s2" style="margin-left:20px">
						<input type="checkbox" data-ng-click="filterManual()"
							   ng-model="hyderabadCheck" style="position:inherit;opacity:1"><label>Hyderabad</label>
					</div>
				</div>
		<button class="right btn btn-xs-small" ng-click="editModel()" style=" margin-top: -45px;"> <span>Edit</span> </button>
		<button class="right btn btn-xs-small" ng-click="resetModel()" style=" margin-top: -80px;"> <span>Clear All</span> </button>
		<div class="row" style="margin-top:22px;">
    		<div class="col-lg-4">
    		Filter:
    		<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    		</div>
		</div>
        <hr>
        <ul class="collection-with-header unit-collection">
            <li class="collection-header">
                <div class="row">
                    <div class="col s6">
                        <span class="custom-col-header">Unit Name</span>
                    </div>
					<div class="col s3">
                        <span class="custom-col-header">Requested Qty</span>
                    </div>
					<div class="col s3">
                        <span class="custom-col-header">Cost Allocated</span>
                    </div>
                </div>
            </li>
            <li class="collection-item" data-ng-repeat="unit in units | filter:search | orderBy : 'name'">

  				 <div data-ng-if="unit.location.name == 'Bangalore'">
					<div class="row" ng-show="bangCheck == true">
                    <div class="col s6">
                        {{unit.name}} ({{unit.company.code}})
                    </div>
					<div class="col s3">
                        {{unit.qty!=undefined ? unit.qty : 0}}
                    </div>
					<div class="col s3">
                        <input type="text" step="0.01" min="0" ng-disabled="isDisabled"
                               data-ng-change="updateQty(unit)" data-ng-model="unit.allocatedCost">
                    </div>
					</div>
				</div>

				<div data-ng-if="unit.location.name == 'Mumbai'">
					<div class="row" ng-show="mumbCheck == true">
                    <div class="col s6">
                        {{unit.name}} ({{unit.company.code}})
                    </div>
					<div class="col s3">
                        {{unit.qty!=undefined ? unit.qty : 0}}
                    </div>
					<div class="col s3">
                        <input type="text" step="0.01" min="0" ng-disabled="isDisabled"
                               data-ng-change="updateQty(unit)" data-ng-model="unit.allocatedCost">
                    </div>
					</div>
				</div>

				<div data-ng-if="unit.location.name == 'Gurgaon' || unit.location.name == 'New Delhi' || unit.location.name == 'Noida' || unit.location.name == 'Mohali' || unit.location.name == 'Ambala'
					 || unit.location.name == 'Chandigarh'|| unit.location.name == 'Karnal' || unit.location.name == 'Faridabad' || unit.location.name == 'Ghaziabad'">
					<div class="row" ng-show="ncrCheck == true">
                    <div class="col s6">
                        {{unit.name}} ({{unit.company.code}})
                    </div>
					<div class="col s3">
                        {{unit.qty!=undefined ? unit.qty : 0}}
                    </div>
					<div class="col s3">
                        <input type="text" step="0.01" min="0" ng-disabled="isDisabled"
                               data-ng-change="updateQty(unit)" data-ng-model="unit.allocatedCost">
                    </div>
					</div>
				</div>

				<div data-ng-if="unit.location.name == 'Pune'">
					<div class="row" ng-show="puneCheck == true">
						<div class="col s6">
							{{unit.name}} ({{unit.company.code}})
						</div>
						<div class="col s3">
							{{unit.qty!=undefined ? unit.qty : 0}}
						</div>
						<div class="col s3">
							<input type="text" step="0.01" min="0" ng-disabled="isDisabled"
								   data-ng-change="updateQty(unit)" data-ng-model="unit.allocatedCost">
						</div>
					</div>
				</div>

				<div data-ng-if="unit.location.name == 'Navi Mumbai'">
					<div class="row" ng-show="naviMumbaiCheck == true">
						<div class="col s6">
							{{unit.name}} ({{unit.company.code}})
						</div>
						<div class="col s3">
							{{unit.qty!=undefined ? unit.qty : 0}}
						</div>
						<div class="col s3">
							<input type="text" step="0.01" min="0" ng-disabled="isDisabled"
								   data-ng-change="updateQty(unit)" data-ng-model="unit.allocatedCost">
						</div>
					</div>
				</div>
				<div data-ng-if="unit.location.name == 'Chennai'">
					<div class="row" ng-show="chennaiCheck == true">
						<div class="col s6">
							{{unit.name}} ({{unit.company.code}})
						</div>
						<div class="col s3">
							{{unit.qty!=undefined ? unit.qty : 0}}
						</div>
						<div class="col s3">
							<input type="text" step="0.01" min="0" ng-disabled="isDisabled"
								   data-ng-change="updateQty(unit)" data-ng-model="unit.allocatedCost">
						</div>
					</div>
				</div>

				<div data-ng-if="unit.location.name == 'Hyderabad'">
					<div class="row" ng-show="hyderabadCheck == true">
						<div class="col s6">
							{{unit.name}} ({{unit.company.code}})
						</div>
						<div class="col s3">
							{{unit.qty!=undefined ? unit.qty : 0}}
						</div>
						<div class="col s3">
							<input type="text" step="0.01" min="0" ng-disabled="isDisabled"
								   data-ng-change="updateQty(unit)" data-ng-model="unit.allocatedCost">
						</div>
					</div>
				</div>
                
            </li>
        </ul>
    </div>
    <hr>
						<div class="row right-align" style="line-height: 30px;">
            					<b>Total Amount:</b> Rs.{{totalAmt}}<br/>
           						<b>Used Amount:</b> Rs.{{usedAmt}}<br/>
          						<b>Remaining Amount:</b> Rs.{{remainingAmt}}
      					  </div>
    <div class="row">
        <button class="btn right margin-right-5" data-ng-click="submit()">Submit</button>
        <button class="btn right margin-right-5" data-ng-click="cancel()">Cancel</button>
    </div>
</script>

<script type="text/ng-template" id="departmentSOModal.html">
<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
    <hr>
</div>
<div class="modal-body" id="modal-body">
<div class="row">
                <div class="col s12">
                    <table class="table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>Department Name</th>
                            <th>Total Cost</th>
                            <th>Total Tax</th>
                            <th>Total Amount</th>
							<th>Original Amount</th>
							<th>Budget Amount</th>
							<th>Remaining Amount</th>
							<th>Running Amount</th>
							<th>Receiving Amount</th>
							<th>Paid Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="items in summaryItem" ng-class="{'red': items.totalAmountDup > items.remainingAmount}">
                            <td style="color: black;">{{items.departmentName}}</td>
 							<td style="color: black;">{{items.totalCostDup.toFixed(2)}}</td>
 							<td style="color: black;">{{items.totalTaxDup.toFixed(2)}}</td>
 							<td style="color: black;">{{items.totalAmountDup.toFixed(2)}}</td>
							<td style="color: black;">{{items.originalAmount.toFixed(2)}}</td>
							<td style="color: black;">{{items.budgetAmount.toFixed(2)}}</td>
							<td style="color: black;">{{items.remainingAmount.toFixed(2)}}</td>
							<td style="color: black;">{{items.runningAmount.toFixed(2)}}</td>
							<td style="color: black;">{{items.receivingAmount.toFixed(2)}}</td>
							<td style="color: black;">{{items.paidAmount.toFixed(2)}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
				</div>
				<div class="row">
					<button class="right btn" data-ng-click="submit()">Submit</button>
					<button class="btn right margin-right-5" data-ng-click="cancel()">Cancel</button>
            </div>
	</div>
		</script>

<div id="snapModal" class="modal">
	<div class="modal-content">
		<h4>Take Snapshot</h4>
		<video data-ng-show="snapRunning" id="video" width="640" height="480" autoplay></video><br />
		<button data-ng-click="startSnap()" class="btn btn-small">Start</button>
		<button data-ng-click="snapPicture()" class="btn btn-small">Snap Photo</button><br />
		<canvas data-ng-hide="snapRunning" id="canvas" width="640" height="480"></canvas><br />
	</div>
	<div class="modal-footer">
		<button data-ng-click="uploadFile()" class="btn btn-small modal-action modal-close">Upload</button>
		<button class="btn btn-small modal-action modal-close" style="margin-right: 20px;background-color:#F44336 !important">Cancel</button>
	</div>
</div>

<div id="scanModal" class="modal">
	<div class="modal-content">
		<h3>Scan document</h3>
		<button type="button" data-ng-click="scanToPng()">Scan</button>
		<div id="images" style="margin-top: 20px;height: 480px;overflow: auto;">
		</div>
	</div>
	<div class="modal-footer">
		<button class="btn btn-small modal-action modal-close" data-ng-click="uploadScannedFile()">Upload</button>
		<button class="btn btn-small modal-action modal-close" style="margin-right: 20px;background-color:#F44336 !important">Close</button>
	</div>
</div>

<div id="soDocumentPreviewModal" class="modal">
	<div class="modal-content">
		<div class="row" style="margin-bottom: 0px;">
			<div class="col s12">
				<div id="documentPreview" style="max-height: 370px; overflow: auto;"></div>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button class="btn modal-action modal-close" style="margin-right: 20px;">Close</button>
	</div>
</div>

<script type="text/ng-template" id="uploadedSoSheet.html">
	<div class="modal-header" data-ng-init="init()">
		<h4 class="modal-title" id="modal-title" style="margin-top: 0px;">Uploaded Bulk So Allocations for - {{costElement.name}}</h4>
		<hr>
	</div>
	<div class="modal-body" id="modal-body">
		<div class="row" style="margin-top:30px;">
			<div>
				<div class="col s12">
					<table class="table bordered striped" style="border: #ccc 1px solid;">
						<thead>
						<tr>
							<th style="text-align: center">S.No</th>
							<th style="text-align: center">Unit Id</th>
							<th style="text-align: center">Unit Name</th>
							<th style="text-align: center">Allocated Cost</th>
						</tr>
						</thead>
						<tbody>
						<tr data-ng-repeat="item in result track by $index">
							<td style="text-align: center;color: black;">{{$index+1}}</td>
							<td style="padding-left:10px;color: black;">{{item.unitId}}</td>
							<td style="text-align: center;color: black;">{{item.unitName}}</td>
							<td style="text-align: center;color: black;">{{item.allocatedCost}}</td>
						</tr>
						</tbody>
					</table>
				</div>
				<div class="col s10 right-align" style="margin-top: 20px;font-size: 20px">
					<b>Total Of Allocated Cost : </b> {{totalOfAllocatedCost}}<br/>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="pull-left">
				<button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
			</div>
			<div class="pull-right">
				<button class="btn btn-warning" type="button" ng-click="submit()">Submit</button>
			</div>
		</div>
	</div>
</script>

