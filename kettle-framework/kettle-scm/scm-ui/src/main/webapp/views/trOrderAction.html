<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	<div class="col s12" id="printHide">
		<div class="row">
			<div class="col s1">
				<input type="button" data-ng-click="backToTrOrderMgt()" value="Back"
					class="btn" style="margin-top: 25px;" />
			</div>
			<div class="col s9">
				<h3>Transfer Order Detail</h3>
			</div>
			<div class="col s2">
				<button data-ng-if="transferOrderDetail.external && transferOrderDetail.externalTransferDetail.status=='INITIATED'"
						data-ng-click="acknowledge(transferOrderDetail)"
						acl-action="ACKTREP"
						style="margin-top: 25px;"
						class="btn right">Acknowledge</button>
		</div>
		</div>
		<div class="row" data-ng-if="!transferOrderDetail.external">
			<div class="col s2">
				<label>Transfer order Id</label>{{transferOrderDetail.id}}
			</div>
			<div class="col s2">
				<label>Request order Id</label>{{transferOrderDetail.requestOrderId}}
			</div>
			<div class="col s2">
				<label>GR Id</label>{{transferOrderDetail.goodsReceivedId}}
			</div>
			<div class="col s4">
				<label>Generated for unit</label>{{transferOrderDetail.generatedForUnitId.name}}
			</div>
			<div class="col s2">
				<label>Status</label>{{transferOrderDetail.status}}
			</div>
		</div>

		<div class="row" data-ng-if="transferOrderDetail.external">
			<div class="col s2">
				<label>Transfer order Id</label>{{transferOrderDetail.id}}
			</div>
			<div class="col s2">
				<label>Vendor Name</label>{{transferOrderDetail.externalTransferDetail.vendorName}}
			</div>
			<div class="col s2">
				<label>Location Name</label>{{transferOrderDetail.externalTransferDetail.locationName}}
			</div>
			<div class="col s2">
				<label>Transfer Status</label>{{transferOrderDetail.status}}
			</div>
			<div class="col s2">
				<label>Approval Status</label>{{transferOrderDetail.externalTransferDetail.status}}
			</div>
			<div class="col s2">
				<label>Total Amount (with no Taxes)</label>Rs. {{transferOrderDetail.totalAmount}}
			</div>
		</div>

		<div class="row">
			<div class="col s3">
				<label>Creation time</label>{{transferOrderDetail.generationTime |
				date:'dd-MM-yyyy hh:mm:ss':'+0530'}}
			</div>
			<div class="col s3">
				<label>Last updated</label>{{transferOrderDetail.lastUpdateTime |
				date:'dd-MM-yyyy hh:mm:ss':'+0530'}}
			</div>
			<div class="col s3">
				<label>Generated by</label>{{transferOrderDetail.generatedBy.name}}
			</div>
			<div class="col s3">
				<label>Last updated by</label>{{transferOrderDetail.lastUpdatedBy.name==null?'NA':transferOrderDetail.lastUpdatedBy.name}}
			</div>
		</div>
		<div class="row">
			<div class="col s9">
				<label>Comment</label>{{transferOrderDetail.comment==null?'No
				comments':transferOrderDetail.comment}}
			</div>
			<div class="col s3">
				<button print-btn class="btn btn-primary" tooltipped
					data-tooltip="Print">
					<i class="fa fa-print"></i>
				</button>
				<button class="btn btn-primary"
					data-ng-click="getTorqusTO(transferOrderDetail.id)" tooltipped
					data-tooltip="Download">
					<i class="fa fa-download"></i>
				</button>
			</div>
		</div>


		<div class="row">
			<div class="col s12">
				<ul class="collection">
					<li class="collection-item list-head">
						<div class="row">
							<div class="col s2">Product Name</div>
							<div class="col s2">Requested Qty.</div>
							<div class="col s2">Absolute Qty.</div>
							<div class="col s2">Transferred Qty.</div>
							<div class="col s2">Received Qty.</div>
							<div class="col s2">Unit of Measure</div>
						</div>
					</li>
					<li class="collection-item"
						data-ng-repeat="item in transferOrderDetail.transferOrderItems track by $index">
						<div class="row sku-title-strip">
							<div class="col s2"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}} [{{item.skuId}}]</a></div>
							<div class="col s2">{{item.requestedQuantity}}</div>
							<div class="col s2">{{item.requestedAbsoluteQuantity}}</div>
							<div class="col s2">{{item.transferredQuantity}}</div>
							<div class="col s2">{{item.receivedQuantity}}</div>
							<div class="col s2">{{item.unitOfMeasure}}</div>
						</div>
						<div class="row" style="margin-bottom: 0px;">
							<div class="col s2">
								<label>Packaging</label>
							</div>
							<div class="col s2">
								<label>Units packed</label>
							</div>
							<div class="col s2">
								<label>Transferred Qty.</label>
							</div>
							<div class="col s2">
								<label>Units Received</label>
							</div>
							<div class="col s2">
								<label>Received Qty.</label>
							</div>
							<div class="col s2">
								<label>Unit of Measure</label>
							</div>
						</div>
						<div class="row" data-ng-repeat="pkg in item.packagingDetails"
							style="margin-bottom: 0px;">
							<div class="col s2">{{pkg.packagingDefinitionData.packagingName}}</div>
							<div class="col s2">{{pkg.numberOfUnitsPacked==null?0:pkg.numberOfUnitsPacked}}</div>
							<div class="col s2">{{pkg.transferredQuantity==null?0:pkg.transferredQuantity}}</div>
							<div class="col s2">{{pkg.numberOfUnitsReceived==null?0:pkg.numberOfUnitsReceived}}</div>
							<div class="col s2">{{pkg.receivedQuantity==null?0:pkg.receivedQuantity}}</div>
							<div class="col s2">{{pkg.packagingDefinitionData.unitOfMeasure}}</div>
						</div>
					</li>
				</ul>
			</div>
		</div>
		<div class="row">
			<div class="col s12">
				<div class="row">
					<div class="col s12 m6 l6">
						<label>Comment(optional)</label>
						<p>{{transferOrderDetail.comment}}</p>
					</div>
				</div>
				<div class="row">
					<div class="col s12">
						<input type="button" class="btn" value="CANCEL"
							data-ng-if="transferOrderDetail.status=='CREATED' && !isCafe"
							data-ng-click="cancelTransferOrder()" acl-action="MTTOMC" />
						<button print-btn class="btn btn-primary" tooltipped
							data-tooltip="Print">
							<i class="fa fa-print"></i>
						</button>
						<button class="btn btn-primary"
							data-ng-click="getTorqusTO(transferOrderDetail.id)" tooltipped
							data-tooltip="Download">
							<i class="fa fa-download"></i>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!--   printable TO section   -->
	<div style="width: 100%;" id="printSection">
		<div class="row"  data-ng-if="transferOrderDetail.type != 'TRANSFER'">
			<!--   print-only  -->
			<div class="col s12">
				<p style="text-align: center;">
					<b><em><span style="font-family: 'Cambria', serif;">Form
								GST INV &ndash; 1<br />
						</span></em></b><b><span
						style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
						style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[transferOrderDetail.sourceCompany.id].name}}<br />
					</span></b><span
						style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line1}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line2}}, <br /> {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.city}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.state}}, <br /> {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.country}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.zipCode}}<br />
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Invoice</span></b> <br />
					<b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">E-Way Bill Number: {{ewayBillNumber == null || ewayBillNumber == "" ?'NA':ewayBillNumber}}</span></b>
				</p>
				<table
					style="border-collapse: collapse; border: none;"
					cellpadding="0cm 5.4pt">
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Unit Details</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">

								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Unit Details </span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Billed To/Shipped To</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.name}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.name}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].name}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.receivingCompany.id].name}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.line1}},
										{{unitData.address.line2}}, <br />
										{{unitData.address.city}},
										{{unitData.address.state}}, <br />
										{{unitData.address.country}},
										{{unitData.address.zipCode}}
									</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.address}}
									</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.location.state.name}}/{{unitData.location.state.code}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.state}}/{{generatedForUnitData.stateCode}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.tin}}</span>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.tin}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].cin}}</span>
								</p>
							</td>

							<td
								style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.receivingCompany.id].cin}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">FSSAI</span>
								</p>
							</td>
							<td style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.fssai}}</span>
								</p>
							</td>
						</tr>

					</tbody>
				</table>
				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>

				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				<table>
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
								colspan="2">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Details
											</span></b>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice
										No.</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.invoiceId}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">

							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Order ID
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.id}}</span>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date
										of Invoice</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.generationTime
										| date:'dd-MM-yyyy':'+0530'}}</span>
								</p>
							</td>
						</tr>

						<tr style="height: 12.00pt; page-break-inside: avoid;">

							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Order ID
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.requestOrderId}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Mode
										of Transport : </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">By
										Road / By Train / By Air</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">

							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.transferOrderItems.length}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vehicle
										No :</span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">____________________</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place
										Of Supply:</span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.city}}</span>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>

				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				<table
					style="border-collapse: collapse; border: none;"
					width="765">
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="100">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
								</p>
							</td>
							<td
								style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="61">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
								</p>
							</td>
							<td
								style="width: 33.9pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="45">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">RO Qty.</span></b>
								</p>
							</td>
							<td
								style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice Qty.</span></b>
								</p>
							</td>
							<td
								style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Uom</span></b>
								</p>
							</td>
							<td
								style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="67">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
								</p>
							</td>
							<td
								style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="84">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
								</p>
							</td>
							<td
								style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;" width="89">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>
								</p>
							</td>
							<td data-ng-repeat="t in availableTaxes"
								style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
								width="{{132/availableTaxes.length}}">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t}}</span></b>
								</p>
							</td>
							<td
								style="width: 66.8pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="89">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total</span></b>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt;  page-break-inside: avoid;"
							data-ng-repeat="item in transferOrderDetail.transferOrderItems track by $index">
							<td
								style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="100">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.skuName}} [{{item.skuCode}}]</span>
								</p>
							</td>
							<td
								style="width: 46.1pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="61">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.code}}</span>
								</p>
							</td>
							<td
								style="width: 33.9pt; border-top: none; border-left: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="45">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.requestedQuantity}}</span>
								</p>
							</td>
							<td
								style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.transferredQuantity}}</span>
								</p>
							</td>
							<td
								style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.unitOfMeasure}}</span>
								</p>
							</td>
							<td
								style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="67">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.negotiatedUnitPrice
										| currency :'': 2}}</span>
								</p>
							</td>
							<td
								style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="84">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.total | currency :'': 2}}</span>
								</p>
							</td>
							<td
								style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								 width="89">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"
										ng-repeat="packaging in item.packagingDetails">
										{{packaging.numberOfUnitsPacked}} --
										{{packaging.packagingDefinitionData.packagingName}} <br />
									</span>
								</p>
							</td>
							<!-- <td
								style="width: 49.2pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="66">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.gst}} ({{item.gstPercentage}}%)</span>
								</p>
							</td> -->
							<td data-ng-repeat="t in item.taxes"
								style="border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="{{132/item.taxes.length}}">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t.value}} ({{t.percentage}}%)</span>
								</p>
							</td>
							<td
								style="width: 66.8pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="89">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{(item.total + item.tax)
										| currency :'': 2}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								colspan="{{8 + availableTaxes.length}}" width="676">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>
								</p>
							</td>
							<td
								style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="89">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice
										| currency :'': 2}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								colspan="{{8 + availableTaxes.length}}" width="676">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif;">Total
											Invoice Value (In figure)</span></b>
								</p>
							</td>
							<td
								style="width: 66.8pt; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="89">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice
										| currency :'': 2}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								colspan="{{9 + availableTaxes.length}}" width="765">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total
											Invoice Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
									<b><span
										style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalPriceInWords}}</span></b>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
				<p style="margin: .0001pt 0; line-height: normal;">
					<span
						style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				<p style="line-height: normal; margin: 0cm 0cm">
					<b><span style="font-family: 'Cambria', serif;">Certified
							that the particulars and the amount indicated given above are
							true and correct </span></b>
				</p>
				<p style="line-height: normal; margin: 5px 0cm 5px">
					<b> <i><span style="font-family: 'Cambria', serif;">
                     Disclaimer : I/We hereby certify that food/foods mentioned in this invoice is/are warranted to be of the nature and quality which it/these purports/purported to be. </span></i></b>
				</p>
				<table
					style="border-collapse: collapse; border: none;"
					width="765">
					<tbody>
						<tr style="height: 20.00pt; page-break-inside: avoid;">
							<td
								style="width: 404.0pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
								width="520">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 14.0pt; font-family: 'Cambria', serif; color: black;">TERMS
											OF SALE</span></b>
								</p>
							</td>
							<td
								style="width: 6.0cm; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
								width="246">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-family: 'Cambria', serif; color: black;">For
											{{companyMap[transferOrderDetail.sourceCompany.id].name}}</span></b>
								</p>
							</td>
						</tr>
						<tr style="height: 73.6pt; page-break-inside: avoid;">
							<td
								style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
								width="510">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">1)Goods
										once sold will not be taken back or exchanged&nbsp; </span>
								</p>
								<p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">2)Seller is
										not responsible for any loss or damaged of goods in transit</span>
								</p>
								<p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">3)Buyer
										undertakes to submit prescribed declaration to sender on
										demand.</span>
								</p>
								<p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">4)Disputes if
										any will be subject to seller court jurisdiction</span>
								</p>
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
								</p>
							</td>
							<td
								style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
								width="256">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
								</p>
							</td>
						</tr>
						<tr style="height: 17.45pt; page-break-inside: avoid;">
							<td
								style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
								width="520">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
								</p>
							</td>
							<td
								style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
								width="246">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span style="font-family: 'Cambria', serif;">Authorised
											Signatory</span></b>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
				<p>
					<span
						style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">Reg. Address: {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line1}},
										{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line2}},
										{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.city}},
										{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.state}},
										{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.country}},
										Pin No:
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.zipCode}} </span>
				</p>
			</div>
		</div>

		<div class="row" data-ng-if="transferOrderDetail.type == 'TRANSFER'">
			<!--   print-only  -->
			<div class="col s12">
				<p style="text-align: center;">
					<b><span
						style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
						style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[transferOrderDetail.sourceCompany.id].name}}<br />
					</span></b><span
						style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line1}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line2}}, <br /> {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.city}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.state}}, <br /> {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.country}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.zipCode}}<br />
						</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">STOCK TRANSFER NOTE (INTERNAL)</span></b> <br />
						<b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">E-Way Bill Number: E-Way Bill Number: {{ewayBillNumber == null || ewayBillNumber == "" ?'NA':ewayBillNumber}}</span></b>
				</p>
				<table
					style="border-collapse: collapse; border: none;"
					cellpadding="0cm 5.4pt">
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Unit Details</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">

								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Unit Details </span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">

								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.name}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.name}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].name}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.receivingCompany.id].name}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.line1}},
										{{unitData.address.line2}}, <br />
										{{unitData.address.city}},
										{{unitData.address.state}}, <br />
										{{unitData.address.country}},
										{{unitData.address.zipCode}}
									</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.address}}
									</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.location.state.name}}/{{unitData.location.state.code}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.state}}/{{generatedForUnitData.stateCode}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.tin}}</span>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.tin}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].cin}}</span>
								</p>
							</td>

							<td
								style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.receivingCompany.id].cin}}</span>
								</p>
							</td>
						</tr>

					</tbody>
				</table>
				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>

				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				<table>
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
								colspan="2">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Details
											</span></b>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice
										No.</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.invoiceId}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">

							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Order ID
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.id}}</span>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date
										of Invoice</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.generationTime
										| date:'dd-MM-yyyy':'+0530'}}</span>
								</p>
							</td>
						</tr>

						<tr style="height: 12.00pt; page-break-inside: avoid;">

							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Order ID
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.requestOrderId}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Mode
										of Transport : </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">By
										Road / By Train / By Air</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">

							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.transferOrderItems.length}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vehicle
										No :</span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">____________________</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place
										Of Supply:</span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.city}}</span>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				<table
					style="border-collapse: collapse; border: none;"
					width="765">
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="100">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
								</p>
							</td>
					<!--		<td
								style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="61">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
								</p>
							</td>-->
							<td
								style="width: 33.9pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="45">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">RO Qty.</span></b>
								</p>
							</td>
							<td
								style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transferred Qty.</span></b>
								</p>
							</td>
							<td
								style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit</span></b>
								</p>
							</td>
							<td
								style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="67">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
								</p>
							</td>
							<td
								style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;" width="89">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>
								</p>
							</td>
							<td
								style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="84">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
								</p>
							</td>

						</tr>
						<tr style="height: 12.00pt;  page-break-inside: avoid;"
							data-ng-repeat="item in transferOrderDetail.transferOrderItems track by $index">
							<td
								style="width: 74.85pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="100">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.skuName}}</span>
								</p>
							</td>
							<!--<td
								style="width: 46.1pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="61">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.code}}</span>
								</p>
							</td>-->
							<td
								style="width: 33.9pt; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="45">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.requestedQuantity}}</span>
								</p>
							</td>
							<td
								style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.transferredQuantity}}</span>
								</p>
							</td>
							<td
								style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.unitOfMeasure}}</span>
								</p>
							</td>
							<td
								style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="67">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.negotiatedUnitPrice
										| currency :'': 2}}</span>
								</p>
							</td>

							<td
								style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								 width="89">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"
										data-ng-repeat="packaging in item.packagingDetails">
										{{packaging.numberOfUnitsPacked}} --
										{{packaging.packagingDefinitionData.packagingName}} <br />
									</span>
								</p>
							</td>
							<td
								style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="84">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.total | currency :'': 2}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								colspan="7" width="676">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>
								</p>
							</td>
							<td
								style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="89">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice
										| currency :'': 2}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								colspan="8" width="765">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total
											Invoice Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
									<b><span
										style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalPriceInWords}}</span></b>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
				<p style="margin: .0001pt 0; line-height: normal;">
					<span
						style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				<p style="line-height: normal; margin: 0cm 0cm .0001pt">
					<b><span style="font-family: 'Cambria', serif;">This is to certify that the material has been dispatched as per the above details.</span></b>
				</p>

				<table
					style="border-collapse: collapse; border: none;"
					width="765">
					<tbody>
						<tr style="height: 20.00pt; page-break-inside: avoid;">
							<td
								style="width: 6.0cm; border: solid windowtext 1.0pt; border-left: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
								width="246">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-family: 'Cambria', serif; color: black;">For
											{{companyMap[transferOrderDetail.sourceCompany.id].name}} ({{unitData.name}})</span></b>
								</p>
							</td>

						</tr>
						<tr style="height: 73.6pt; page-break-inside: avoid;">

							<td
								style="width: 6.0cm; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
								width="246">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
								</p>
							</td>

						</tr>
						<tr style="height: 17.45pt; page-break-inside: avoid;">
							<td
								style="width: 6.0cm; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
								width="246">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span style="font-family: 'Cambria', serif;">Authorised
											Signatory</span></b>
								</p>
							</td>

						</tr>
					</tbody>
				</table>
				<p>
					<span
						style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">Reg. Address: {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line1}},
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.city}},
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.state}},
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.country}}, Pin No:
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.zipCode}}</span>
				</p>
			</div>
		</div>
	</div>
	 <!--   printable TO section   -->
	<!--<div style="width: 100%;" id="printSection1">
		<div class="row">
			print-only
			<div class="col s12">
				<p style="font-size: 9pt;">{{formatDate(today, 'dd/MM/yyyy')}}</p>
				<p class="center-align companyName" style="font-size: 12pt;">{{unitData.division.company.name}}</p>
				<p style="font-size: 12pt; text-align: center;">{{unitData.division.company.registeredAddress.line1}},
					{{unitData.division.company.registeredAddress.city}},
					{{unitData.division.company.registeredAddress.state}},
					{{unitData.division.company.registeredAddress.country}}, Pin No:
					{{unitData.division.company.registeredAddress.zipCode}}</p>
				<p style="font-size: 12pt; text-align: center;">Stock Transfer
					Note/ Delivery Challan</p>
				<div style="border-bottom: #000 1px dashed;"></div>
				<p>
					<span>Site: {{transferOrderDetail.generatedForUnitId.name}}</span>
					<span class="right">TIN: {{generatedForUnitData.tin}}</span>
				</p>
				<p>
					<span>Email: {{generatedForUnitData.email}}</span> <span
						class="right">Phone: {{generatedForUnitData.contact}}</span>
				</p>
				<p>
					<span>Site address: {{generatedForUnitData.address}}</span>
				</p>
				<div style="border-bottom: #000 1px dashed;"></div>
				<p>
					<span>TO No. {{transferOrderDetail.id}}</span> <span class="right">TO
						Date: {{transferOrderDetail.generationTime |
						date:'dd-MM-yyyy':'+0530'}}</span>
				</p>
				<p>
					<span>RO No. {{transferOrderDetail.requestOrderId}}</span> <span
						class="right">RO Date:
						{{transferOrderDetail.requestOrderTime |
						date:'dd-MM-yyyy':'+0530'}}</span>
				</p>
				<p>
					<span>Supplier.
						{{transferOrderDetail.generationUnitId.name}}</span> <span class="right">Supplier
						TIN. {{unitData.tin}}</span>
				</p>
				<p>Supplier Address: {{unitData.address.line1}}
					{{unitData.address.line2}} {{unitData.address.line3}},
					{{unitData.address.city}}, Pin No: {{unitData.address.zipCode}}</p>
				<div style="border-bottom: #000 1px dashed;"></div>
				<table>
					<tr style="border-bottom: #000 1px dashed;">
						<th>Item Name</th>
						<th>Requested Quantity</th>
						<th>Transferred Quantity</th>
						<th>Unit</th>
						<th>Price</th>
						<th>Amount</th>
						<th>Packaging Qty</th>
					</tr>
					<tr
						data-ng-repeat="item in transferOrderDetail.transferOrderItems track by $index"
						style="border-bottom: #000 1px dashed; font-size: 12pt; page-break-inside: avoid;">
						<td>{{item.skuName}}</td>
						<td>{{item.requestedQuantity}}</td>
						<td>{{item.transferredQuantity}}</td>
						<td>{{item.unitOfMeasure}}</td>
						<td>{{item.negotiatedUnitPrice | currency :'': 2}}</td>
						<td>{{(item.negotiatedUnitPrice*item.transferredQuantity) |
							currency :'': 2}}</td>
						<td><span data-ng-repeat="packaging in item.packagingDetails">
								{{packaging.numberOfUnitsPacked}} --
								{{packaging.packagingDefinitionData.packagingName}} </span><br /></td>
					</tr>
				</table>
				<p style="padding: 20px 0;">
					<span>Total items:
						{{transferOrderDetail.transferOrderItems.length}}</span> <span
						class="right">Gross order value: {{totalPrice | currency
						:'': 2}}</span>
				</p>
				<p>
					<span>Payment terms: ______________________</span>
				</p>
				<p>
					<span>Driver Name: ______________________</span> <span
						class="right">Vehicle Number: ______________________</span>
				</p>
				<p>
					<span>Dispatch Time: ______________________</span> <span
						class="right">Delivery Time: ______________________</span>
				</p>
				<p>
					<span>Time at which café manager reached the outlet :
						______________________</span>
				</p>
				<div style="border-bottom: #000 1px solid;"></div>
				<p>
					<span>Authorized by: ______________________</span> <span
						class="right">Date: ______________________</span>
				</p>
				<p>
					<span>Please notify us immediately if you are unable to ship
						as specified</span>
				</p>
			</div>
		</div>
	</div>
	-->




</div>
