<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .popeye-modal{
        width: 80% !important;
    }

    .box {
        display: inline-block;
        width: 10px;
        height: 10px;
        border: 1px solid rgba(0, 0, 0, .2);
    }

    .yellow {
        background: #ffffbf;
    }

    .green{
        background: #cdffcd;
    }

    .searchingCard {
        display: flex;
        flex-direction: column;
        margin:0px;
        width:100% !important;
    }
    .searchingCard .col {
        margin-left:0px !important;
        margin-right: 0px !important;
        width:100% !important;
    }

    .searchingCard .btn {
        width:100% !important;
    }

</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3 class="left">Pending Receiving</h3>
                <select data-ng-show="showPendingGr" data-ng-model="selectedHeader"
                        data-ng-options=' header for header in allheaders'>
                </select>
                <span class="right" style="margin: 5px;">Current : <div class="box yellow"></div> </span>
                <span class="right" style="margin: 5px;">Past : <div class="box green"></div> </span>
                <input type="button" class="btn right" value="BACK" data-ng-click="showPendingGr = true" data-ng-hide="showPendingGr" style="margin-top: 35px;" />
                <input type="button" class="btn right" value="REFRESH" data-ng-click="init()" data-ng-show="showPendingGr" style="margin-top: 35px;" />
            </div>
        </div>
    </div>
    <div class="row margin-bottom-10" data-ng-show="showPendingGr">
            <div class="col s3">
                <div class="form-group">
                    <label class="control-label">Select Sku</label>
                    <select id="skus" ui-select2="{allowClear:true, placeholder: 'Select Sku'}"
                            ng-options="sku as sku.name for sku in skuList"
                            data-ng-change="true" data-ng-model="selectedSku"></select>

                </div>

            </div>
            <div class="col s3">
                <label>TO ID</label>
                <input type="number" ng-model="toId"/>
            </div>
           <div class="col s3">
            <label>Select User Name</label>
           <select id="users" ui-select2="{allowClear:true, placeholder: 'Select user'}"
                    ng-options="user for user in userList"
                    data-ng-change="true" data-ng-model="selectedUser"></select>

           </div>

            <div class="col s1">
                <button class="btn btn-small margin-top-20" data-ng-click="getSkuFilterData()">Find</button>
            </div>
    </div>


    <div class="row">
        <div class="col s12 refOrderListTable" data-ng-show="showPendingGr" data-ng-if="item.header == selectedHeader || selectedHeader == 'All'" data-ng-repeat="item in allGrs track by $index">
            <h5>{{item.header}}</h5>
            <button class="btn btn-medium" data-ng-hide="item.list == null || item.list.length == 0" data-ng-click="getGrView(item.header)"> Download Sheet</button>
            <ul class="collection menuItemList" data-ng-hide="item.list == null || item.list.length == 0">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s1">Id</div>
                        <div class="col s1">TO ID</div>
                        <div class="col s2">Generation Time</div>
                        <div class="col s2">Generated By</div>
                        <div class="col s2" data-ng-show="item.type != 'RaisedDisputedGR'">Generation Unit</div>
                        <div class="col s2" data-ng-show="item.type == 'RaisedDisputedGR'">Generated For Unit</div>
                        <div class="col s1">Status</div>
                        <div class="col s3">Action</div>
                    </div>
                </li>
                <li class="collection-item z-depth-1" data-ng-if="isFiltered(gr)" data-ng-repeat="gr in item.list track by gr.id" >
                    <div class="row" data-ng-if="isFiltered(gr)" ng-style="{'background-color':getColorCode(gr.generationTime)}">
                        <div class="col s1 clickable" data-ng-click="fillReceiving(gr.id)"><span style="border-bottom: 1px dashed #000">{{gr.id}}</span></div>
                        <div class="col s1">{{gr.transferOrderId}}</div>
                        <div class="col s2">{{gr.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
                        <div class="col s2">{{gr.generatedBy.name}}</div>
                        <div class="col s2" data-ng-show="item.type != 'RaisedDisputedGR'">{{gr.generationUnitId.name}}</div>
                        <div class="col s2" data-ng-show="item.type == 'RaisedDisputedGR'">{{gr.generatedForUnitId.name}}</div>
                        <div class="col s2">{{gr.status}}</div>
                        <div class="col s3"><div data-ng-show="gr.rejectedGR">
                            <button class="btn green" data-ng-click="fillReceiving(gr.parentGR)" >See Original GR</button>
                            <div data-ng-show="item.actionForRejected && gr.rejectedGR" style="padding-top: 6px;">
                                <button class="btn green" data-ng-click="openAcceptOptions($event,gr.id)" >Accept GR</button>
                                <button class="btn red" data-ng-show="isWarehouse"
                                        data-ng-click="openDeclineOptions($event,gr.id)" style="margin-top: 6px;">Decline GR</button>
                            </div>
                </div>

                        <div class="col s1"><span style="color: red;font-weight: bold;" data-ng-show="gr.specialOrder==true">(Special)</span></div>
                    </div>
                    </div>
                </li>
            </ul>
            <div data-ng-show="item.list == null || item.list.length == 0">{{item.message}}</div>
        </div>
        <div class="col s12" data-ng-hide="showPendingGr">
            <div class="row sku-pkg-row">
                <div class="col s2"><label>GR Id:</label>{{grDetail.id}}</div>
                <div class="col s2"><label>Generation Time:</label>{{grDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
                <div class="col s2"><label>Last Updated:</label>{{grDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
                <div class="col s2"><label>Sent By:</label>{{grDetail.generationUnitId.name}}</div>
                <div class="col s2"><label>Generated By:</label>{{grDetail.generatedBy.name}}</div>
                <div class="col s2"><label>Status:</label>{{grDetail.status}}</div>

            </div>
            <div class="row sku-pkg-row">
                <div class="col s4" data-ng-show="grDetail.comment != null"><label>Comment</label><br />{{grDetail.comment}}</div>
                <div class="col s4" data-ng-show="grDetail.parentGRComment != null"><label>Original GR Comment</label><br />{{grDetail.parentGRComment}}</div>
                <div class="col s4" data-ng-show="grDetail.rejectGRComment != null"><label>Rejected GR analysis</label><br />{{grDetail.rejectGRComment}}</div>
            </div>
            <ul class="collection menuItemList z-depth-1-half" style="margin-bottom: 50px">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s2">Sku Name</div>
                        <div class="col s2">Transferred Quantity</div>
                        <div class="col s2">Received Quantity</div>
                        <div class="col s2">Unit Of Measure</div>
                        <div class="col s2">Excess Quantity</div>
                        <div class="col s2" data-ng-show="grItem.vendor != null">Vendor</div>
                    </div>
                </li>
                <li data-ng-repeat="grItem in grDetail.goodsReceivedItems track by grItem.id">
                    <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid;">
                        <div class="col s2"><a data-ng-click="showPreview($event, grItem.skuId,'SKU')">{{grItem.skuName}}</a></div>
                        <div class="col s2">{{grItem.transferredQuantity==null?0:grItem.transferredQuantity}}</div>
                        <div class="col s2">{{grItem.receivedQuantity==null?0:grItem.receivedQuantity}}</div>
                        <div class="col s2">{{grItem.unitOfMeasure}}</div>
                        <div class="col s2">{{grItem.excessQuantity}}</div>
                        <div class="col s2" data-ng-show="grItem.vendor != null">{{grItem.vendor.name}}</div>
                    </div>
                    <div class="row sku-pkg-row" data-ng-repeat="pkg in grItem.packagingDetails | orderBy : 'packagingName' track by $index" style="border-bottom: #eee 1px solid;padding: 20px 10px;">
						<div class= "row">
	                        <div class="col s2">
                                <label>Packaging:</label>{{pkg.packagingDefinitionData.packagingName}}
                            </div>
                            <div class="col s1">
                                <label>Units Packed:</label>{{pkg.numberOfUnitsPacked==null?0:pkg.numberOfUnitsPacked}}
                            </div>
                            <div class="col s2">
                                <label>Transferred Qty:</label>{{pkg.transferredQuantity==null?0:pkg.transferredQuantity}}
                                {{pkg.packagingDefinitionData.unitOfMeasure}}
                            </div>
                            <div class="col s1">
                                <label>Units Received:</label>{{pkg.numberOfUnitsReceived==null?0:pkg.numberOfUnitsReceived}}
                            </div>
                            <div class="col s1">
                                <label>Received Qty:</label>{{pkg.receivedQuantity==null?0:pkg.receivedQuantity}} {{pkg.packagingDefinitionData.unitOfMeasure}}
                            </div>
                            <div class="col s2" data-ng-show="(grDetail.status != 'CANCELLED' && grDetail.status != 'SETTLED' && !grDetail.rejectedGR)"
                                 data-ng-if="grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER'
                                 || grDetail.transferOrderType == 'BROKEN_ASSET_TRANSFER' || grDetail.transferOrderType == 'RENOVATION_ASSET_TRANSFER'">
                                <label>Tag Value ending in {{grItem.associatedAssetTagValue | limitTo: -2}}:</label>
                                <input type="text"
                                       name="assetTag"
                                       data-ng-model="grItem.tagValue"
                                       data-ng-minlength="0"
                                       data-ng-maxlength="6"
                                       ng-change="validateAssetTagValue(grItem.tagValue,grItem)" required/>
                                <p ng-show="grItem.tagValue == null" class="errorMessage">Asset Tag Value is required.</p>
                                <p ng-show="grItem.tagValue != null && grItem.tagValue.length > 6" class="errorMessage">Asset Tag Value is too large.</p>
                                <p ng-show="grItem.tagValue != null && grItem.tagValue.length < 6" class="errorMessage">Asset Tag Value is too small.</p>
                                <div data-ng-if="grItem.assetVerified == null || !grItem.assetVerified">
                                    <p  class="errorMessage">Enter Valid Asset Tag Value.</p>
                                </div>
                            </div>
                            <div class="col s2"
                                 data-ng-show="grDetail.status != 'CANCELLED' && grDetail.status != 'SETTLED' && !grDetail.rejectedGR && grItem.productId!=100217">
                                <button class="btn" data-ng-click="enableRejectionInput(this, pkg,grItem)" >{{pkg.hasRejection == true? "Cancel" : "Reject"}}</button>
                            </div>
                        </div>
                        <!--<div class="row" data-ng-show="pkg.hasRejection == true && !isSemiFinished(grItem)" style="padding-top:20px;">
	                        <div class="col"><label>Rejected Units:</label>
	                        	<input type="number" min="0" placeholder="Rejected Units" data-ng-model="pkg.unitsRejected"
                                       data-ng-change="calculateReceivedQty(pkg, grItem)"/>
	                        </div>
		                    <div class="col"><label>Reason:</label>
			                    <div>
								    <select data-ng-model="pkg.rejectionReason" data-ng-init="pkg.rejectionReason='Less Quantity'">
			                        	<option value="Less Quantity">Less Quantity</option>
			                        	<option value="Damaged">Damaged</option>
			                        	<option value="Wrong">Wrong</option>
								    </select>
								</div>
		                    </div>
                        </div> -->

                        <div class="row" data-ng-show="pkg.hasRejection == true" style="padding-top:20px;">
							<div class="row" style="margin: 20px;">
								<table>
									<tr>
										<th>Expiration Date</th>
										<th>Quantity</th>
										<th>Rejected Quantity</th>
									</tr>
									<tr data-ng-repeat="expiry in grItem.drillDowns">
										<td>{{expiry.expiryDate | date : 'yyyy-MM-dd HH:mm:ss'}}</td>
										<td>{{expiry.quantity}}</td>
										<td><input
											type="number"
											min="0"
											placeholder="Rejected Units"
											data-ng-model="expiry.rejection"
											data-ng-change="updateRejection(grItem.drillDowns,pkg,grItem,true)" />
										</td>
									</tr>
								</table>
							</div>
							<div class="col"><label>Total Rejected Units:</label>
	                        	<input data-ng-disabled="true" type="number" min="0" placeholder="Rejected Units" data-ng-model="pkg.unitsRejected"
                                       data-ng-change="calculateReceivedQty(pkg, grItem)"/>
	                        </div>
		                    <div class="col"><label>Reason:</label>
			                    <div>
								    <select data-ng-model="pkg.rejectionReason" data-ng-init="pkg.rejectionReason='Less Quantity'">
			                        	<option value="Less Quantity">Less Quantity</option>
			                        	<option value="Damaged">Damaged</option>
			                        	<option value="Wrong">Wrong</option>
								    </select>
								</div>
		                    </div>
                        </div>

                    </div>
                </li>
            </ul>
            <div class="row">
                <div class="col s12 form-element" data-ng-show="grDetail.status != 'CANCELLED' && grDetail.status != 'SETTLED' && !grDetail.rejectedGR">
                    <label>Comment:</label>
                    <textarea data-ng-model="grDetail.comment"></textarea>
                </div>
                <div class="col s6 form-element">
                    <input type="button" class="btn" value="BACK" data-ng-click="backbuttonClick()" />
                </div>
                <div class="col s6 right-align form-element"
                     data-ng-show="grDetail.status != 'CANCELLED' && grDetail.status != 'SETTLED' && !grDetail.rejectedGR">
                    <!--<span>Enter Transfer ID:</span>
                    <input style="display:inline-block; max-width:90px;" placeholder="Transfer Id" type="text" data-ng-model="tIdCheck" />
                    <a class="btn" href="#grPreview" modal acl-action="RECPRA"
                    data-ng-show="grDetail.transferOrderId == null || grDetail.transferOrderId == tIdCheck" >SETTLE</a>-->
                    <a class="btn" acl-action="RECPRA" data-ng-click="validateChecks()">SETTLE</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="grPreview.html">
    <div class="modal-content" >
        <div class="col s12">
            <div class="row">
                <div class="col s12">
                    <h5 class="left">Receiving Preview</h5>
                </div>
            </div>
    </div>
    <div class="col s12">
            <div class="row sku-pkg-row">
                <div class="col s2"><label>GR Id:</label><br />{{grDetail.id}}</div>
                <div class="col s2"><label>Generation Time:</label><br />{{grDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
                <div class="col s2"><label>Last Updated:</label><br />{{grDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
                <div class="col s2"><label>Generation Unit:</label><br />{{grDetail.generationUnitId.name}}</div>
                <div class="col s2"><label>Generated By:</label><br />{{grDetail.generatedBy.name}}</div>
                <div class="col s2"><label>Status:</label><br />{{grDetail.status}}</div>
            </div>
            <ul class="collection menuItemList z-depth-1-half" style="margin-bottom: 50px">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s3">Sku Name</div>
                        <div class="col s3">Transferred Quantity</div>
                        <div class="col s3">Received Quantity</div>
                        <div class="col s3">Unit Of Measure</div>
                    </div>
                </li>
                <li data-ng-repeat="grItem in grDetail.goodsReceivedItems track by grItem.id">
                    <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid;">
                        <div class="col s3"><a data-ng-click="showPreview($event, grItem.skuId,'SKU')">{{grItem.skuName}}</a></div>
                        <div class="col s3">{{grItem.transferredQuantity==null?0:grItem.transferredQuantity}}</div>
                        <div class="col s3">{{grItem.receivedQuantity==null?0:grItem.receivedQuantity}}</div>
                        <div class="col s3">{{grItem.unitOfMeasure}}</div>
                    </div>
                    <div class="row sku-pkg-row" data-ng-repeat="pkg in grItem.packagingDetails track by pkg.id" style="border-bottom: #eee 1px solid;padding: 20px 10px;">
                    	<div class="row" >
	                        <div class="col s2"><label>Packaging:</label>{{pkg.packagingDefinitionData.packagingName}}</div>
	                        <div class="col s2"><label>Units Packed:</label>{{pkg.numberOfUnitsPacked}}</div>
	                        <div class="col s2"><label>Units Received:</label>{{pkg.numberOfUnitsReceived}}</div>
	                        <div class="col s2"><label>Transferred Qty:</label>
                                {{pkg.transferredQuantity}} &nbsp; {{pkg.packagingDefinitionData.unitOfMeasure}}</div>
<!--                             <div class="col s2">{{pkg.numberOfUnitsReceived==null?0:pkg.numberOfUnitsReceived}}</div>
                            <div class="col s2">{{pkg.receivedQuantity==null?0:pkg.receivedQuantity}}</div> -->
	                        <div class="col s2"><label>Received Qty:</label>{{pkg.receivedQuantity}} &nbsp; {{pkg.packagingDefinitionData.unitOfMeasure}}</div>
                        </div>
                        <div data-ng-if="pkg.numberOfUnitsRejected > 0" class="row" style="padding-top:20px">
                        	<div class="col s2"><label>Rejected Units:</label>{{pkg.numberOfUnitsRejected}}</div>
		                    <div class="col s2"><label>Reason:</label>{{pkg.rejectionReason}}</div>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="row">
                <div class="col s12 form-element">
                    <label>Comment:</label>{{grDetail.comment}}
                </div>
                <div class="col s2 form-element">
                    <!--  <input type="button" class="btn" value="BACK" data-ng-click="cancelpreviewGR()" /> -->
                    <a class="modal-action modal-close waves-effect btn" data-ng-click="back()">BACK</a>
                </div>
                <div class="col s10 right-align form-element">
                	<a class="modal-action modal-close waves-effect btn" data-ng-click="submit()">SUBMIT</a>
                    <!-- <input type="button" class="btn" value="SUBMIT" />  -->
                </div>
            </div>
        </div>
        </div>
</script>

<script type="text/ng-template" id="acceptOption.html">
  <div class="row sku-pkg-row">
     <div class="col s2"><label>GR Id:</label>{{grDetail.id}}</div>
     <div class="col s2"><label>Generation Time:</label>{{grDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
     <div class="col s2"><label>Last Updated:</label>{{grDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
     <div class="col s2"><label>Sent By:</label>{{grDetail.generationUnitId.name}}</div>
     <div class="col s2"><label>Generated By:</label>{{grDetail.generatedBy.name}}</div>
     <div class="col s2"><label>Status:</label>{{grDetail.status}}</div>
  </div>
  <div class="row sku-pkg-row">
      <div class="col s4" data-ng-show="grDetail.comment != null"><label>Comment By Sending Unit</label><br />{{grDetail.comment}}</div>
      <div class="col s4" data-ng-show="grDetail.parentGRComment != null"><label>Comment by Receiving Unit</label><br />{{grDetail.parentGRComment}}</div>
      <div class="col s4" data-ng-show="grDetail.rejectGRComment != null"><label>Rejected GR Analysis</label><br />{{grDetail.rejectGRComment}}</div>
  </div>
  <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid;">
      <div class="col s4"><a data-ng-click="showPreview($event, grDetail.goodsReceivedItems[0].skuId,'SKU')">{{grDetail.goodsReceivedItems[0].skuName}}</a></div>
      <div class="col s4">{{grDetail.goodsReceivedItems[0].transferredQuantity==null?0:grDetail.goodsReceivedItems[0].transferredQuantity}}</div>
      <div class="col s4">{{grDetail.goodsReceivedItems[0].unitOfMeasure}}</div>
  </div>
  <div class="row">
      <div class="form-element">
         <label>Comment after Analysis</label>
            <textarea data-ng-model="grDetail.rejectGRComment" name="rejectComment" ng-required="true"></textarea>
       </div>
  </div>

  <div class="row">
    <button class="btn green"  data-ng-click="rejectedGoodsReceivedFound()">Found Inventory</button>
 	<button class="btn red" style="float: right;" data-ng-if="grDetail.transferOrderType == 'REGULAR_TRANSFER'" data-ng-click="rejectedGoodsReceivedLost()">Lost Inventory</button>
   </div>
</script>

<script type="text/ng-template" id="declineOption.html">
  <div class="row sku-pkg-row">
     <div class="col s2"><label>GR Id:</label>{{grDetail.id}}</div>
     <div class="col s2"><label>Generation Time:</label>{{grDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
     <div class="col s2"><label>Last Updated:</label>{{grDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</div>
     <div class="col s2"><label>Sent By:</label>{{grDetail.generationUnitId.name}}</div>
     <div class="col s2"><label>Generated By:</label>{{grDetail.generatedBy.name}}</div>
     <div class="col s2"><label>Status:</label>{{grDetail.status}}</div>
  </div>
  <div class="row sku-pkg-row">
      <div class="col s4" data-ng-show="grDetail.comment != null"><label>Comment by Sending Unit</label><br />{{grDetail.comment}}</div>
      <div class="col s4" data-ng-show="grDetail.parentGRComment != null"><label>Comment by Receiving Unit</label><br />{{grDetail.parentGRComment}}</div>
      <div class="col s4" data-ng-show="grDetail.rejectGRComment != null"><label>Rejected GR Analysis</label><br />{{grDetail.rejectGRComment}}</div>
  </div>
  <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid;">
      <div class="col s4"><a data-ng-click="showPreview($event, grDetail.goodsReceivedItems[0].skuId,'SKU')">
          {{grDetail.goodsReceivedItems[0].skuName}}</a></div>
      <div class="col s4">{{grDetail.goodsReceivedItems[0].transferredQuantity==null?0:grDetail.goodsReceivedItems[0].transferredQuantity}}</div>
      <div class="col s4">{{grDetail.goodsReceivedItems[0].unitOfMeasure}}</div>
  </div>
  <div class="row">
      <div class="form-element">
         <label>Reason for Declining</label>
            <textarea data-ng-model="grDetail.rejectGRComment" name="rejectComment" ng-required="true"></textarea>
       </div>
  </div>
  <div class="row">
    <button class="btn green"  data-ng-click="rejectedGoodsReceivedDecline()">Confirm</button>
 	<button class="btn red" style="float: right;" data-ng-click="cancel()">Cancel</button>
   </div>
</script>
