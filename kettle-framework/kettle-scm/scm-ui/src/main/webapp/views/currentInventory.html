<div class="row white z-depth-2" data-ng-init="initStock()">
    <div class="col s9">
        <h4 class="left" style="display: inline-block;">Current Stock At Hand</h4>
    </div>
    <div class="col s3 pull-right" style="margin-right: auto;margin-top: 10px">
        <button class="btn right" acl-action="RDK" data-ng-click="removeDuplicateKey()">Remove Duplicate Keys</button>
    </div>
    <div class="col s9">
        <select data-ng-model="selectedSkus" ui-select2 multiple>
            <option data-ng-repeat="sku in selectList track by $index"
                    value ="{{sku.skuId}}">{{sku.skuName}}</option>
        </select>
    </div>
    <div class="col s3">
        <button class="btn right" data-ng-click="getStock(selectedSkus)">Get Stock</button>
        <button class="btn right margin-right-5" data-ng-click="downloadStock()">Download</button>
    </div>
    <div class="col s12">
        <div class="row">
            <p data-ng-if="stock==null || stock.length==0" class="no-negative-inventory">
                No inventory items found
            </p>
            <table class="bordered inventory-table" data-ng-if="stock!=null && stock.length>0">
                <thead>
                <tr>
                    <th class="center-align">SKU</th>
                    <th class="center-align" tooltipped data-tooltip="Opening Stock">OPENING*</th>
                    <th class="center-align" tooltipped data-tooltip="Received">GR*</th>
                    <th class="center-align" tooltipped data-tooltip="ReceivedWithInitiatedGR">GRI*</th>
                    <th class="center-align" tooltipped data-tooltip="Bookings">BKG*</th>
                    <th class="center-align" tooltipped data-tooltip="Transferred">TO*</th>
                    <th class="center-align" tooltipped data-tooltip="Wasted">WASTE*</th>
                    <th class="center-align" tooltipped data-tooltip="Consumption">CONSP*</th>
                    <th class="center-align pointer" tooltipped data-tooltip="Closing Stock" ng-click="sortItems(stock)">CLOSING*</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="item in inventoryItems track by $index">
                    <td class="center-align" tooltipped
                        data-tooltip="Last closed on: {{item.closingDate!=null ? item.closingDate : 'N/A' }}">
                        {{item.name}} [{{item.uom}}]
                    </td>
                    <td class="center-align">{{item.opening}}</td>
                    <td class="center-align">{{item.received}}</td>
                    <td class="center-align">{{item.receivedWithInitiatedGr}}</td>
                    <td class="center-align">{{item.booked}}</td>
                    <td class="center-align">{{item.transferred}}</td>
                    <td class="center-align">{{item.wasted}}</td>
                    <td class="center-align">{{item.consumption}}</td>
                    <td class="center-align" data-ng-class="{'red white-text':item.stockValue<0,
                                                       'yellow black-text':item.stockValue==0}">{{item.stockValue}}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="row margin-10">
        <div class="col s6 margin-bottom-5">
            <ul ng-if="pager.totalPages>1" class="pagination">
                <li class="waves-effect" data-ng-if="pager.currentPage != 1">
                    <a tooltipped data-tooltip="Previous Page" ng-click="setPage(pager.currentPage - 1)">
                        <i class="material-icons">chevron_left</i>
                    </a>
                </li>
                <li class="active white-text">{{pager.currentPage}}</li>
                <li class="waves-effect" data-ng-if="pager.currentPage != pager.totalPages">
                    <a tooltipped data-tooltip="Next Page" ng-click="setPage(pager.currentPage + 1)">
                        <i class="material-icons">chevron_right</i>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>