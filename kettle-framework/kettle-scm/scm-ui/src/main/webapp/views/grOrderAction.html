<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s2">
                <input type="button" data-ng-click="backToGrOrderMgt()" value="Back" class="btn" style="margin-top:28px;" />
            </div>
            <div class="col s10">
                <h3>Goods Receive Detail</h3>
            </div>
        </div>
        <div class="row">
            <div class="col s2"><label>Goods Receive Id</label>{{goodsReceiveDetail.id}}</div>
            <div class="col s2"><label>Request Order Id</label>{{goodsReceiveDetail.requestOrderId}}</div>
            <div class="col s2"><label>Transfer Order Id</label>{{goodsReceiveDetail.transferOrderId}}</div>
            <div class="col s4"><label>Generation unit</label>{{goodsReceiveDetail.generationUnitId.name}}</div>
            <div class="col s2"><label>Status</label>{{goodsReceiveDetail.status}}</div>
        </div>
        <div class="row">
            <div class="col s3"><label>Creation Time</label>{{goodsReceiveDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
            <div class="col s3"><label>Last updated</label>{{goodsReceiveDetail.lastUpdateTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
            <div class="col s3"><label>Generated By</label>{{goodsReceiveDetail.generatedBy.name}}</div>
            <div class="col s3"><label>Received By</label>{{goodsReceiveDetail.receivedBy.name==null?'NA':goodsReceiveDetail.receivedBy.name}}</div>
        </div>
        <div class="row">
            <div class="col s3"><label>Cancelled By</label>{{goodsReceiveDetail.cancelledBy.name==null?'NA':goodsReceiveDetail.cancelledBy.name}}</div>
            <div class="col s6"><label>Comment</label>{{goodsReceiveDetail.comment==null?'No comments':goodsReceiveDetail.comment}}</div>
            <div class="col s2"><button print-btn class="btn btn-primary" tooltipped data-tooltip="Print"><i class="fa fa-print"></i></button></div>
        </div>

        <div class="row">
            <div class="col s12">
                <ul class="collection">
                    <li class="collection-item list-head">
                        <div class="row">
                            <div class="col s6">Product Name</div>
                            <div class="col s2">Transferred Qty.</div>
                            <div class="col s2">Received Qty.</div>
                            <div class="col s2">Unit of Measure</div>
                        </div>
                    </li>
                    <li class="collection-item" data-ng-repeat="item in goodsReceiveDetail.goodsReceivedItems | orderBy : 'skuName' track by $index">
                        <div class="row sku-title-strip">
                            <div class="col s6"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}} [{{item.skuId}}]</a></div>
                            <div class="col s2">{{item.transferredQuantity}}</div>
                            <div class="col s2">{{item.receivedQuantity==null ? 0 : item.receivedQuantity}}</div>
                            <div class="col s2">{{item.unitOfMeasure}}</div>
                        </div>
                        <div class="row" style="margin-bottom: 0px;">
                            <div class="col s2"><label>Packaging</label></div>
                            <div class="col s2"><label>Units Packed</label></div>
                            <div class="col s2"><label>Transferred Qty</label></div>
                            <div class="col s2"><label>Units received</label></div>
                            <div class="col s2"><label>Received Qty</label></div>
                            <div class="col s2"><label>Unit of measure</label></div>
                        </div>
                        <div class="row" data-ng-repeat="pkg in item.packagingDetails | orderBy : 'packagingName' " style="margin-bottom: 0px;">
                            <div class="col s2">{{pkg.packagingDefinitionData.packagingName}}</div>
                            <div class="col s2">{{pkg.numberOfUnitsPacked==null?0:pkg.numberOfUnitsPacked}}</div>
                            <div class="col s2">{{pkg.transferredQuantity==null?0:pkg.transferredQuantity}}</div>
                            <div class="col s2">{{pkg.numberOfUnitsReceived==null?0:pkg.numberOfUnitsReceived}}</div>
                            <div class="col s2">{{pkg.receivedQuantity==null?0:pkg.receivedQuantity}}</div>
                            <div class="col s2">{{pkg.packagingDefinitionData.unitOfMeasure}}</div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <!-- <div class="row">
            <div class="col s12">
                <div class="row">
                    
                </div>
                <div class="row">
                    <div class="col s12">
                        <input type="button" class="btn" value="CANCEL" data-ng-if="goodsReceiveDetail.status=='CREATED'" data-ng-click="cancelGoodsReceiveOrder()" />
                    </div>
                </div>
            </div>
        </div> -->
        <div class="row">
            <div class="col s2"><button print-btn class="btn btn-primary" tooltipped data-tooltip="Print"><i class="fa fa-print"></i></button></div>
        </div>
    </div>


    <!--   printable GR section   -->
    <div style="padding: 20px 50px; width:100%;" id="printSection">
		<div class="row" >
			<!--   print-only  -->
			<div class="col s12">
				<p style="text-align: center;">
					<b><span
						style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
						style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[goodsReceiveDetail.sourceCompany.id].name}}<br />
					</span></b><span
						style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[goodsReceiveDetail.sourceCompany.id].registeredAddress.line1}},
						{{companyMap[goodsReceiveDetail.sourceCompany.id].registeredAddress.line2}}, <br /> {{companyMap[goodsReceiveDetail.sourceCompany.id].registeredAddress.city}},
						{{companyMap[goodsReceiveDetail.sourceCompany.id].registeredAddress.state}}, <br /> {{companyMap[goodsReceiveDetail.sourceCompany.id].registeredAddress.country}},
						{{companyMap[goodsReceiveDetail.sourceCompany.id].registeredAddress.zipCode}}<br />
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Stock Receiving Note</span></b>
				</p>
				<table
					style="border-collapse: collapse; border: none;"
					cellpadding="0cm 5.4pt">
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Unit Details</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Unit Details </span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Billed To/Shipped To</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{goodsReceiveDetail.generationUnitId.name}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{goodsReceiveDetail.generatedForUnitId.name}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[goodsReceiveDetail.sourceCompany.id].name}}</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[goodsReceiveDetail.receivingCompany.id].name}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generationUnitData.address}}
									</span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
										{{unitData.address.line1}},
										{{unitData.address.line2}}, <br />
										{{unitData.address.city}},
										{{unitData.address.state}}, <br />
										{{unitData.address.country}},
										{{unitData.address.zipCode}}
									</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">  {{generationUnitData.state}}/{{generationUnitData.stateCode}}  </span>
								</p>
							</td>
							<td
								style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.location.state.name}}/{{unitData.location.state.code}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generationUnitData.tin}}</span>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"> {{unitData.tin}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[goodsReceiveDetail.sourceCompany.id].cin}}</span>
								</p>
							</td>
							
							<td
								style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[goodsReceiveDetail.receivingCompany.id].cin}}</span>
								</p>
							</td>
						</tr>
						
					</tbody>
				</table>
				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>

				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				<table>
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
								colspan="2">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Details
											</span></b>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Document 
										No.</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{goodsReceiveDetail.id}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							
							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Order ID
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{goodsReceiveDetail.transferOrderId}}</span>
								</p>
							</td>
							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Document Date
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{goodsReceiveDetail.generationTime| date:'dd-MM-yyyy':'+0530'}}</span>
								</p>
							</td>
						</tr>
						
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							
							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Order ID
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{goodsReceiveDetail.requestOrderId}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							
							<td
								style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items
										</span>
								</p>
							</td>
							<td
								style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{goodsReceiveDetail.goodsReceivedItems.length}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Document Type</span>
								</p>
							</td>
							<td
								style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{goodsReceiveDetail.documentType}}</span>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				
				<p>
					<span
						style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
				</p>
				<table
					style="border-collapse: collapse; border: none;"
					width="765">
					<tbody>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="100">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Product Description</span></b>
								</p>
							</td>
							<td
								style="width: 33.9pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="45">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transferred Qty.</span></b>
								</p>
							</td>
							<td
								style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Received Qty.</span></b>
								</p>
							</td>
							<td
								style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Uom</span></b>
								</p>
							</td>
							<td
								style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="67">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
								</p>
							</td>
							<td
								style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="84">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
								</p>
							</td>
							<td
								style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;" width="89">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt;  page-break-inside: avoid;"
							data-ng-repeat="item in goodsReceiveDetail.goodsReceivedItems track by $index">
							<td
								style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="100">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.skuName}}</span>
								</p>
							</td>
							<td
								style="width: 33.9pt; border-top: none; border-left: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="45">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.transferredQuantity}}</span>
								</p>
							</td>
							<td
								style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.receivedQuantity}}</span>
								</p>
							</td>
							<td
								style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="49">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.unitOfMeasure}}</span>
								</p>
							</td>
							<td
								style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="67">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.negotiatedUnitPrice
										| currency :'': 2}}</span>
								</p>
							</td>
							<td
								style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="84">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{(item.negotiatedUnitPrice*item.receivedQuantity) | currency :'': 2}}</span>
								</p>
							</td>
							<td
								style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								 width="89">
								<p
									style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"
										ng-repeat="packaging in item.packagingDetails">
										{{packaging.numberOfUnitsPacked}} --
										{{packaging.packagingDefinitionData.packagingName}} <br />
									</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								colspan="10" width="676">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif;">Total
											Invoice Value (In figure)</span></b>
								</p>
							</td>
							<td
								style="width: 66.8pt; border-top: 1pt solid windowtext; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								width="89">
								<p style="margin: .0001pt 0; line-height: normal;">
									<span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice
										| currency :'': 2}}</span>
								</p>
							</td>
						</tr>
						<tr style="height: 12.00pt; page-break-inside: avoid;">
							<td
								style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
								colspan="11" width="765">
								<p style="margin: .0001pt 0; line-height: normal;">
									<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total
											Invoice Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
									<b><span
										style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalPriceInWords}}</span></b>
								</p>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
    </div>


</div>