<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Search Transfer Order</h3>
            </div>
        </div>
        <div class="row searchingCard">
            <div class="col s2">
                <label>Start date</label>
                <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
            </div>
            <div class="col s2">
                <label>End date</label>
                <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
            </div>
            <div class="col s2">
                <label>Generated for Unit</label>
                <select data-ng-model="generatedForUnit" data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
            </div>
            <div class="col s2">
                <label>Transfer Order Id</label>
                <input type="text" data-ng-model="transferOrderId" />
            </div>
            <div class="col s2">
                <label>Status</label>
                <select data-ng-model="status" data-ng-change="show(status)" data-ng-options="item as item for item in scmOrderStatusList"></select>
            </div>
            <div class="col s1">
                <input type="button" class="btn btn-small" value="Find" data-ng-click="findTransferOrders()" style="margin-top: 24px;" acl-action="MTTOMV" />
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12" data-ng-if="transferOrderList.length > 0">
            <input type="button" class="btn left" value="Select All" data-ng-click="checkAll()" style="margin-top: 24px;" />
            <input type="button" class="btn right" value="Download Selected TO's"
                   data-ng-class="{disabled:selectedTOs.length==0}" data-ng-disabled="selectedTOs.length==0" data-ng-click="getTOsView()" style="margin-top: 24px;" />
        </div>
        <div class="col s12">
            <ul class="collection striped" data-ng-show="transferOrderList.length>0" >
                <li class="collection-item list-head" style="display: flex">
                    <div class="row center-align" style="width: 90%">
                        <div class="col s1">
                            TO Id</div>
                        <div class="col s3">Generated For Unit</div>
                        <div class="col s2">Request Order id</div>
                        <div class="col s2">Created By</div>
                        <div class="col s2">Generation Time</div>
                        <div class="col s2">Status</div>
                    </div>
                    <div style="width: 10%">Actions</div>
                </li>
                <li class="collection-item  center-align" style="display: flex" data-ng-repeat="to in transferOrderList | orderBy : '-id' track by to.id" >
                    <div class="row" style="margin-bottom: 0px;margin-left:0px; margin-right:0px;width: 90%">
                        <div class="col s1 "><input  id="TO-{{to.id}}" type="checkbox"
                                                    data-ng-click="selectTO(to.id)" data-ng-model="to.checked"><label for="TO-{{to.id}}">{{to.id}}</label></div>
                        <div class="col s3">
                            {{to.generatedForUnitId.name}}
                            <span class="chip" style="font-size: 12px;" data-ng-if="to.external">EXTERNAL TRANSFER</span>
                        </div>
                        <div class="col s2">{{to.requestOrderId==null?'NA':to.requestOrderId}}</div>
                        <div class="col s2">{{to.generatedBy.name}}</div>
                        <div class="col s2" style="font-size: 12px;">{{to.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                        <div class="col s2"><span class="badge">{{to.status}}</span></div>
                    </div>
                    <div style="width: 10%">
                    <button data-ng-click="printPage(to.id)" class="btn btn-primary" tooltipped
                            data-tooltip="Print">
                        <i class="fa fa-print"></i>
                    </button>
                    <button class="btn btn-primary"
                            data-ng-click="getTorqusTO(to.id)" tooltipped
                            data-tooltip="Download"><i class="fa fa-download"></i></button>
                    <button class="btn btn-xs-small" data-ng-click="openTrOrderAction(to.id)">
                        TO View
                    </button></div>
                </li>
            </ul>
            <p data-ng-show="transferOrderList.length==0">No transfer orders found for selected criteria!</p>
        </div>
    </div>
    <div style="width: 100%;" id="printSection">
        <div class="row"  data-ng-if="transferOrderDetail.type != 'TRANSFER'">
            <!--   print-only  -->
            <div class="col s12">
                <p style="text-align: center;">
                    <b><em><span style="font-family: 'Cambria', serif;">Form
								GST INV &ndash; 1<br />
						</span></em></b><b><span
                        style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
                        style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[transferOrderDetail.sourceCompany.id].name}}<br />
					</span></b><span
                        style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line1}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line2}}, <br /> {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.city}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.state}}, <br /> {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.country}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.zipCode}}<br />
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Invoice</span></b> <br />
                    <b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">E-Way Bill Number: {{ewayBillNumber == null || ewayBillNumber == "" ?'NA':ewayBillNumber}}</span></b>
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        cellpadding="0cm 5.4pt">
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Unit Details</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">

                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Unit Details </span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Billed To/Shipped To</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.name}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.name}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].name}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.receivingCompany.id].name}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.line1}},
										{{unitData.address.line2}}, <br />
										{{unitData.address.city}},
										{{unitData.address.state}}, <br />
										{{unitData.address.country}},
										{{unitData.address.zipCode}}
									</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.address}}
									</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.location.state.name}}/{{unitData.location.state.code}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.state}}/{{generatedForUnitData.stateCode}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.tin}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.tin}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].cin}}</span>
                            </p>
                        </td>

                        <td
                                style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.receivingCompany.id].cin}}</span>
                            </p>
                        </td>

                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">FSSAI</span>
                            </p>
                        </td>
                        <td style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.fssai}}</span>
                            </p>
                        </td>
                    </tr>

                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>

                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <table>
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
                                colspan="2">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Details
											</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice
										No.</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.invoiceId}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">

                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Order ID
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.id}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date
										of Invoice</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.generationTime
                                    | date:'dd-MM-yyyy':'+0530'}}</span>
                            </p>
                        </td>
                    </tr>

                    <tr style="height: 12.00pt; page-break-inside: avoid;">

                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Order ID
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.requestOrderId}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Mode
										of Transport : </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">By
										Road / By Train / By Air</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">

                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.transferOrderItems.length}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vehicle
										No :</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">____________________</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place
										Of Supply:</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.city}}</span>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>

                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        width="765">
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="100">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="61">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 33.9pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="45">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">RO Qty.</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice Qty.</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Uom</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="67">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="84">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
                            </p>
                        </td>
                        <td
                                style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;" width="89">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>
                            </p>
                        </td>
                        <td data-ng-repeat="t in availableTaxes"
                            style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                            width="{{132/availableTaxes.length}}">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t}}</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 66.8pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="89">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total</span></b>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt;  page-break-inside: avoid;"
                        data-ng-repeat="item in transferOrderDetail.transferOrderItems track by $index">
                        <td
                                style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="100">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.skuName}} [{{item.skuCode}}]</span>
                            </p>
                        </td>
                        <td
                                style="width: 46.1pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="61">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.code}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 33.9pt; border-top: none; border-left: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="45">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.requestedQuantity}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.transferredQuantity}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.unitOfMeasure}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="67">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.negotiatedUnitPrice
                                    | currency :'': 2}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="84">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.total | currency :'': 2}}</span>
                            </p>
                        </td>
                        <td
                                style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="89">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"
                                            ng-repeat="packaging in item.packagingDetails">
										{{packaging.numberOfUnitsPacked}} --
										{{packaging.packagingDefinitionData.packagingName}} <br />
									</span>
                            </p>
                        </td>
                        <!-- <td
                            style="width: 49.2pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="66">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.gst}} ({{item.gstPercentage}}%)</span>
                            </p>
                        </td> -->
                        <td data-ng-repeat="t in item.taxes"
                            style="border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="{{132/item.taxes.length}}">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t.value}} ({{t.percentage}}%)</span>
                            </p>
                        </td>
                        <td
                                style="width: 66.8pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="89">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{(item.total + item.tax)
                                    | currency :'': 2}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                colspan="{{8 + availableTaxes.length}}" width="676">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="89">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice
                                    | currency :'': 2}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                colspan="{{8 + availableTaxes.length}}" width="676">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif;">Total
											Invoice Value (In figure)</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 66.8pt; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="89">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice
                                    | currency :'': 2}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                colspan="{{9 + availableTaxes.length}}" width="765">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total
											Invoice Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                                <b><span
                                        style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalPriceInWords}}</span></b>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p style="margin: .0001pt 0; line-height: normal;">
					<span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <p style="line-height: normal; margin: 0cm 0cm ">
                    <b><span style="font-family: 'Cambria', serif;">Certified
							that the particulars and the amount indicated given above are
							true and correct </span></b>
                </p>
                <p style="line-height: normal; margin: 5px 0cm 5px">
                   <b> <i><span style="font-family: 'Cambria', serif;">
                     Disclaimer : I/We hereby certify that food/foods mentioned in this invoice is/are warranted to be of the nature and quality which it/these purports/purported to be. </span></i></b>
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        width="765">
                    <tbody>
                    <tr style="height: 20.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 404.0pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                                width="520">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 14.0pt; font-family: 'Cambria', serif; color: black;">TERMS
											OF SALE</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 6.0cm; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-family: 'Cambria', serif; color: black;">For
											{{companyMap[transferOrderDetail.sourceCompany.id].name}}</span></b>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 73.6pt; page-break-inside: avoid;">
                        <td
                                style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                                width="510">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">1)Goods
										once sold will not be taken back or exchanged&nbsp; </span>
                            </p>
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">2)Seller is
										not responsible for any loss or damaged of goods in transit</span>
                            </p>
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">3)Buyer
										undertakes to submit prescribed declaration to sender on
										demand.</span>
                            </p>
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span style="font-family: 'Cambria', serif;">4)Disputes if
										any will be subject to seller court jurisdiction</span>
                            </p>
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                            </p>
                        </td>
                        <td
                                style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                                width="256">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 17.45pt; page-break-inside: avoid;">
                        <td
                                style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
                                width="520">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                            </p>
                        </td>
                        <td
                                style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span style="font-family: 'Cambria', serif;">Authorised
											Signatory</span></b>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">Reg. Address: {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line1}},
										{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line2}},
										{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.city}},
										{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.state}},
										{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.country}},
										Pin No:
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.zipCode}} </span>
                </p>
            </div>
        </div>

        <div class="row" data-ng-if="transferOrderDetail.type == 'TRANSFER'">
            <!--   print-only  -->
            <div class="col s12">
                <p style="text-align: center;">
                    <b><span
                            style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
                        style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[transferOrderDetail.sourceCompany.id].name}}<br />
					</span></b><span
                        style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line1}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line2}}, <br /> {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.city}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.state}}, <br /> {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.country}},
						{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.zipCode}}<br />
						</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">STOCK TRANSFER NOTE (INTERNAL)</span></b> <br />
                    <b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">E-Way Bill Number: E-Way Bill Number: {{ewayBillNumber == null || ewayBillNumber == "" ?'NA':ewayBillNumber}}</span></b>
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        cellpadding="0cm 5.4pt">
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Unit Details</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">

                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Unit Details </span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">

                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.name}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.name}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].name}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.receivingCompany.id].name}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.line1}},
										{{unitData.address.line2}}, <br />
										{{unitData.address.city}},
										{{unitData.address.state}}, <br />
										{{unitData.address.country}},
										{{unitData.address.zipCode}}
									</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.address}}
									</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.location.state.name}}/{{unitData.location.state.code}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.state}}/{{generatedForUnitData.stateCode}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.tin}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{generatedForUnitData.tin}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.sourceCompany.id].cin}}</span>
                            </p>
                        </td>

                        <td
                                style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[transferOrderDetail.receivingCompany.id].cin}}</span>
                            </p>
                        </td>
                    </tr>

                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>

                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <table>
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
                                colspan="2">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Details
											</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice
										No.</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.invoiceId}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">

                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Order ID
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.id}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date
										of Invoice</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.generationTime
                                    | date:'dd-MM-yyyy':'+0530'}}</span>
                            </p>
                        </td>
                    </tr>

                    <tr style="height: 12.00pt; page-break-inside: avoid;">

                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Request Order ID
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.requestOrderId}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Mode
										of Transport : </span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">By
										Road / By Train / By Air</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">

                        <td
                                style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items
										</span>
                            </p>
                        </td>
                        <td
                                style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{transferOrderDetail.transferOrderItems.length}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vehicle
										No :</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">____________________</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place
										Of Supply:</span>
                            </p>
                        </td>
                        <td
                                style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.city}}</span>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <table
                        style="border-collapse: collapse; border: none;"
                        width="765">
                    <tbody>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="100">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
                            </p>
                        </td>
                        <!--		<td
                                    style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                    width="61">
                                    <p
                                        style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                        <b><span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
                                    </p>
                                </td>-->
                        <td
                                style="width: 33.9pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="45">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">RO Qty.</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transferred Qty.</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="67">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
                            </p>
                        </td>
                        <td
                                style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;" width="89">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="84">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
                            </p>
                        </td>

                    </tr>
                    <tr style="height: 12.00pt;  page-break-inside: avoid;"
                        data-ng-repeat="item in transferOrderDetail.transferOrderItems track by $index">
                        <td
                                style="width: 74.85pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="100">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.skuName}}</span>
                            </p>
                        </td>
                        <!--<td
                            style="width: 46.1pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="61">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.code}}</span>
                            </p>
                        </td>-->
                        <td
                                style="width: 33.9pt; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="45">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.requestedQuantity}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.transferredQuantity}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="49">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.unitOfMeasure}}</span>
                            </p>
                        </td>
                        <td
                                style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="67">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.negotiatedUnitPrice
                                    | currency :'': 2}}</span>
                            </p>
                        </td>

                        <td
                                style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="89">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"
                                            data-ng-repeat="packaging in item.packagingDetails">
										{{packaging.numberOfUnitsPacked}} --
										{{packaging.packagingDefinitionData.packagingName}} <br />
									</span>
                            </p>
                        </td>
                        <td
                                style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="84">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.total | currency :'': 2}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                colspan="7" width="676">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>
                            </p>
                        </td>
                        <td
                                style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="89">
                            <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice
                                    | currency :'': 2}}</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt; page-break-inside: avoid;">
                        <td
                                style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                colspan="8" width="765">
                            <p style="margin: .0001pt 0; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total
											Invoice Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                                <b><span
                                        style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalPriceInWords}}</span></b>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p style="margin: .0001pt 0; line-height: normal;">
					<span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                </p>
                <p style="line-height: normal; margin: 0cm 0cm .0001pt">
                    <b><span style="font-family: 'Cambria', serif;">This is to certify that the material has been dispatched as per the above details.</span></b>
                </p>

                <table
                        style="border-collapse: collapse; border: none;"
                        width="765">
                    <tbody>
                    <tr style="height: 20.00pt; page-break-inside: avoid;">
                        <td
                                style="width: 6.0cm; border: solid windowtext 1.0pt; border-left: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-family: 'Cambria', serif; color: black;">For
											{{companyMap[transferOrderDetail.sourceCompany.id].name}} ({{unitData.name}})</span></b>
                            </p>
                        </td>

                    </tr>
                    <tr style="height: 73.6pt; page-break-inside: avoid;">

                        <td
                                style="width: 6.0cm; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
                            </p>
                        </td>

                    </tr>
                    <tr style="height: 17.45pt; page-break-inside: avoid;">
                        <td
                                style="width: 6.0cm; border-top: none; border-left: solid windowtext 1.0pt; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"
                                width="246">
                            <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span style="font-family: 'Cambria', serif;">Authorised
											Signatory</span></b>
                            </p>
                        </td>

                    </tr>
                    </tbody>
                </table>
                <p>
					<span
                            style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">Reg. Address: {{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.line1}},
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.city}},
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.state}},
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.country}}, Pin No:
					{{companyMap[transferOrderDetail.sourceCompany.id].registeredAddress.zipCode}}</span>
                </p>
            </div>
        </div>
    </div>
</div>
