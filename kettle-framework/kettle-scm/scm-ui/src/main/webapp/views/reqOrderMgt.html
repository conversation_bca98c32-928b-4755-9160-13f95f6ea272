<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Search Request Order</h3>
            </div>
        </div>
        <div class="row searchingCard">
            <div class="col s2" style="width: 11%">
                <label>Start date</label>
                <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
            </div>
            <div class="col s2" style="width: 11%">
                <label>End date</label>
                <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
            </div>
            <div class="col s2">
                <label>Fulfilling Unit</label>
                <select data-ng-model="fulfillingUnit" data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
            </div>
            <div class="col s2" style="width: 12%">
                <label>Request Order Id</label>
                <input type="text" data-ng-model="requestOrderId" />
               <!-- <label>Vendor</label>
                <select data-ng-model="vendorId" data-ng-change="show(vendorId)" data-ng-options="vendor.vendorId as vendor.vendorName for vendor in vendorList"></select>
                <label>Product Name</label>
                <select data-ng-model="productId" data-ng-change="show(productId)" data-ng-options="product.productId as product.productName for product in productList"></select>-->
            </div>
            <div class="col s2" style="width: 11%">
                <label>Status</label>
                <select data-ng-model="status" data-ng-change="show(status)" data-ng-options="item as item for item in scmOrderStatusList"></select>
            </div>
            <!-- <div class="col s2">
                <label>Search Tag</label>
                <input type="text" data-ng-model="searchTag"/>
            </div> -->
            <div class="col s2">
                <input type="button" class="btn btn-small" value="Find" data-ng-click="findRequestOrders()" style="margin-top: 24px;" acl-action="MTRQOMV" />
            </div>
        </div>
    </div>


    <div class="row">
        <div class="col s12">
            <ul class="collection striped" data-ng-show="requestOrderList.length>0">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s1">RO Id</div>
                        <div class="col s2">Fulfillment Unit</div>
                        <div class="col s1">Created By</div>
                        <div class="col s2">Generation Time</div>
                        <div class="col s2">Fulfillment Date</div>
                        <div class="col s2">Comment</div>
                        <div class="col s2">Status</div>
                    </div>
                </li>
                <li class="collection-item clickable" data-ng-class="{'highlight-last-created' : isLastCreated(ro)}" data-ng-repeat="ro in requestOrderList | orderBy : '-id' track by ro.id" data-ng-click="openReqOrderAction(ro.id)">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s1" data-ng-style="!(ro.applyBudget) && {'color':'red'}">{{ro.id}}</div>
                        <div class="col s2">{{ro.fulfillmentUnit.name}}</div>
                        <div class="col s1">{{ro.generatedBy.name}}</div>
                        <div class="col s2">{{ro.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</div>
                        <div class="col s2">{{ro.fulfillmentDate | date:'dd-MM-yyyy':'+0530'}}</div>
                        <div class="col s2">{{ro.comment==null?"No comment":ro.comment}}</div>
                        <div class="col s2"><span class="badge">{{ro.status}}</span>
                            <span style="color: red;font-weight: bold;" data-ng-show="ro.specialOrder==true">(Special)</span>
                        </div>
                    </div>
                </li>
            </ul>
            <p data-ng-show="requestOrderList.length==0">No orders found for selected criteria!</p>
        </div>
    </div>
</div>