<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Specialized Ordering</h3>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12">
            <input input-date type="text" ng-model="requestDate" container="" format="yyyy-mm-dd" />
            <input type="button" class="btn right" value="REFRESH" data-ng-click="getSpecializedOrderReport()" style="margin-top: 20px;" acl-action="ADSORV" />
        </div>
    </div>

    <div class="row">
        <div class="col s12">
            <ul class="collection">
                <li class="collection-item list-head">
                    <div class="row" style="margin: 0px;">
                        <div class="col s4">Unit Name</div>
                        <div class="col s8">Request Order Details</div>
                    </div>
                </li>
                <li class="collection-item" data-ng-repeat="data in reportData | orderBy: 'unit.name'">
                    <div class="row" style="margin: 0px;">
                        <div class="col s4">{{data.unit.name}}</div>
                        <div class="col s8" style="border-left: #ccc 1px solid;">
                            <div data-ng-repeat="order in data.orders" class="orderStrip">
                                {{order.id}} ({{order.status}}): <span class="productBadge" data-ng-repeat="item in order.requestOrderItems">{{item.productName}}</span>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>

