<style>
    .grid {
        width: 900px;
        height: 550px;
        padding: 0 !important;
    }

    .modalgrid {
        width: 900px;
        height: 220px;
        padding: 0 !important;
    }
</style>
<div
        class="row white z-depth-3 custom-listing-li"
        data-ng-init="init()">
    <div class="col s12">
        <h3>Vendor to SKU Mapping</h3>
    </div>
    <div class="col s12 m12 l6 xl6">
        <div class="row margin0">
            <label class="black-text">Mapping Type</label> <select
                ui-select2="selectedMappingType"
                id="mappingTypeId"
                name="mappingType"
                data-ng-model="mappingType"
                data-ng-options="mapping as mapping.name for mapping in mappingList track by mapping.id"
                data-ng-change="getMappingTypeData(mappingType)">
        </select>
        </div>
        <div class="row margin0">
            <label class="black-text">Values</label> <select
                ui-select2="selectedValue"
                id="valueDataId"
                name="valueData"
                data-ng-model="valueDataType"
                data-ng-options="valueData as (valueData.name + ' [' + valueData.category + ']') for valueData in valueDataList | orderBy : 'name' track by valueData.id"
                data-ng-change="searchMappings()"></select>
        </div>
    </div>
    <div
            class="col s12"
            style="">
        <div class="row">
            <div class="col s6 m6 l6 xl6">
                <button
                        data-ng-disabled="valueDataType == null"
                        data-ng-click="searchMappings()"
                        class="btn left"
                        acl-action="SMMVSVI">Search
                </button>
            </div>

            <div class="col s6 m6 l6 xl6">
				<span
                        data-ng-if="valueDataType != 'undefined' && valueDataType.id > 0"
                        data-ng-click="openNewMappingModal()"> <a
                        href="#addNewMapping"
                        class="btn right"
                        style="margin-right: 10px"
                        modal
                        acl-action="SMMVSAD"><b>+</b> Add New Mapping</a>
				</span>
            </div>
        </div>
    </div>
</div>
<div
        class="row"
        id="gridView"
        style="padding: 10px 0;">
    <div
            class="col s12"
            ng-show="valueDataType != null">
        <div
                class="col s6 card-panel red lighten-2"
                align="left">
            <h6 class="white-text center">
                <b>Updating Mappings for {{valueDataType.name}}</b>
            </h6>
        </div>
    </div>
    <div
            class="col s12"
            data-ng-if="gridOptions.data != null">
        <div
                id="mappingsGrid"
                ui-grid="gridOptions"
                ui-grid-resize-columns
                ui-grid-move-columns
                class="grid col s12"></div>
    </div>
</div>
<div
        id="addNewMapping"
        class="modal modal-mx">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <h5>Add Vendor to SKU Mapping</h5>
            </div>
        </div>
        <div class="row">
            <div class="col s12 m12 l3 xl3">
                <label
                        data-ng-if="mapperValue.type='VENDOR'"
                        class="black-text">{{mapperValue.sourceType}} Name</label>
            </div>
            <div class="col s12 m12 l9 xl9">
                <label class="black-text"> {{mapperValue.name}} </label>
            </div>
            <div class="row">
                <div class="col s12 m12 l3 xl3">
                    <label
                            data-ng-if="mapperValue.type='SKU'"
                            class="black-text">{{mapperValue.targetType}} Name</label>
                </div>
                <div class="col s12 m12 l9 xl9">
                    <select
                            ui-select2="selectedValue"
                            id="modalValueDataId"
                            name="modalValueData"
                            data-ng-model="modalValueDataType"
                            data-ng-options="modalValueData as (modalValueData.name + ' [' + modalValueData.category + ']') for modalValueData in modalValueDataList | orderBy : 'name' track by modalValueData.id"
                            data-ng-change="addToNewMappingUpdateList(modalValueData)"
                            style="width: 100%">
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <button
                        class="waves-effect waves-green btn right"
                        style="margin-right: 10px"
                        data-ng-click="addToModalGridData()">
                    <b>+</b> Add
                </button>
            </div>
        </div>
        <div
                class="row"
                id="modalGridView">
            <div
                    class="col s12"
                    data-ng-if="!hideModalGrid">
                <div
                        id="gridSaveState"
                        ui-grid="modalGridOptions"
                        class="modalgrid col s12"
                        ui-grid-resize-columns
                        ui-grid-move-columns></div>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <button
                        class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px"
                        ng-click="submitModalGridData()">Submit
                </button>
                <button
                        class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px"
                        ng-click="cancelModal()">Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<div id="addAlias"
     class="modal modal-mx">
    <div class="modal-content">
    </div>
    <div class="row">
        <div class="col s12">
            <h5>Add Name </h5>
        </div>

        <div class="row">
            <div class="col s12 ">
                <label>Add Name:</label>
                <input  type="text" ng-model="addAliasInSku"/>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <button
                        class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px"
                        ng-click="onSubmittingAliasName(addAliasInSku)">Submit
                </button>
                <button
                        class="modal-action modal-close waves-effect waves-green btn right"
                        style="margin-right: 10px"
                        ng-click="cancelAliasModal()">Cancel
                </button>
            </div>
        </div>
    </div>
</div>
<script
        type="text/ng-template"
        id="statusChangeButton.html">
    <div class="ui-grid-cell-contents">
        <button
                data-ng-class="{'btn btn-xs-small':row.entity.mappingStatus=='IN_ACTIVE', 'btn red btn-xs-small':row.entity.mappingStatus=='ACTIVE'}"
                ng-click="grid.appScope.changeStatus(row.entity)"
                ng-if="row.entity.mappingStatus!='NA'" acl-action="SMMVSUP">
            <span ng-if="row.entity.mappingStatus=='IN_ACTIVE'">Activate</span>
            <span ng-if="row.entity.mappingStatus=='ACTIVE'">Deactivate</span>
        </button>
        <button
                class="btn btn-xs-small"
                ng-click="grid.appScope.removeRow(row.entity)"
                ng-if="row.entity.mappingStatus=='NA'" acl-action="SMMVSUP">
            <span>Remove</span>
        </button>
    </div>
</script>
<script
        type="text/ng-template"
        id="skuIdTemplate.html">
    <div class="ui-grid-cell-contents">
        <a style="cursor: pointer" data-ng-click="grid.appScope.showPreview($event, row.entity.id,'SKU')">{{row.entity.id}}</a>
    </div>
</script>
<script
        type="text/ng-template"
        id="addAliasName.html">
    <div class="ui-grid-cell-contents">
            <a
               ng-if=" !row.entity.alias "
               href="#addAlias"
               modal
               acl-action="SMMVSAD"
               style="text-decoration: none;
                     color:white">
                <button
                        class="btn btn-xs-small "
                        ng-click="grid.appScope.addAlias(row.entity)"><b>+</b> Add name
                </button>
            </a>

           <a
                ng-if="row.entity.alias"
                href="#addAlias"
                modal
                acl-action="SMMVSAD"
                style="text-decoration: none;
                     color:white">
            <button
                    class="btn btn-xs-small "
                    ng-click="grid.appScope.addAlias(row.entity)"> {{row.entity.alias}}
            </button>
        </a>
    </div>
</script>