
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
  <div class="col s12">
    <div class="row white z-depth-3 custom-listing-li">
      <div class="col s12">
        <h4>Search Category</h4>
        <div class="input-field">
          <select ui-select2="selectOptions" ng-model="categoryId" data-ng-change="selectCategory(categoryId)" data-placeholder="Select a category">
            <option ng-repeat="category in categories"
                    ng-selected="{{category.categoryId == selectedCategory}}"
                    value="{{category.categoryId}}">{{category.categoryName}}</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row simpleDemo">
  <div class="col s6 overflow">

    <div ng-if="checkEmpty(selectedMasterValueList)">
      <p class="flow-text">No Active Attribute Mappings found</p>
    </div>


    <div ng-if="!checkEmpty(selectedMasterValueList)" ng-repeat="(attributeId, list) in selectedMasterValueList track by $index" class="row">
      <div class="panel panel-info">
        <div class="panel-heading">
          <h5 class="panel-title">{{getAttribute(attributeId).attributeName}}</h5>
        </div>
        <ul dnd-list="list" dnd-drop="checkIfExists(list, item, index)">
          <li ng-repeat="item in list | filter : {attributeValueStatus:'ACTIVE'}"
              dnd-acceptedTypes = "attributeId"
              dnd-draggable="item"
              dnd-effect-allowed="copy">
            <span>{{item.attributeValue}}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="col s6">
    <div class="row white z-depth-2">
      <div class="row margin0">
        <div class="col s6">
          <h5 class="left">Drag & drop to add Attributes</h5>
        </div>
        <div class="col s6">
          <button class="right btn margin-top-10" data-ng-click="submit()">Add to Category</button>
        </div>
      </div>

      <div class="row margin0">
        <ul dnd-list="selectedProfiles" dnd-drop="onDrop(selectedProfiles, item, index)">
          <li data-ng-if = "!checkEmpty(item)"
              ng-repeat="item in selectedProfiles track by $index"
              dnd-moved="selectedProfiles.splice($index, 1)"
              dnd-effect-allowed="move"
              dnd-selected="selectedProfiles.selected = item"
              ng-class="{'selected': selectedProfiles.selected === item}">
            <div class="row margin0">
              <span class="left">{{item.attributeValue}}</span>
              <button class="right removeBtn btn"
                      ng-click="removeFromSelected(selectedProfiles, item.attributeValueId)">Remove</button>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="row white z-depth-2" data-ng-if ="!checkEmpty(mappedProfiles)">
      <ul id="mappingView" class="collection with-header">
        <li class="collection-item active">Added Profiles to Product</li>
        <li class="collection-item" data-ng-if = "!checkEmpty(item)"
            ng-repeat="item in mappedProfiles track by $index">
          <div class="row margin0">
            <span class="left">{{item.attributeValue.name}}</span>
            <button data-ng-if="item.mappingStatus=='ACTIVE'" class="right removeBtn btn"
                    ng-click="updateStatus(item.categoryAttributeValueId,false)">Disable</button>
            <button data-ng-if="item.mappingStatus=='IN_ACTIVE'" class="right removeBtn btn"
                    ng-click="updateStatus(item.categoryAttributeValueId,true)">Enable</button>
          </div>
        </li>
      </ul>
    </div>
  </div>
</div>
