<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .select2.select2-container {
        width: 100% !important;
    }
    .frame {
        width: fit-content !important;
        height: fit-content !important;
    }
    .modal1 {
        display: none; /* Hidden by default */
        position: fixed; /* Stay in place */
        z-index: 999; /* Sit on top */
        left: 0;
        top: 0;
        width: 100%; /* Full width */
        height: 100vh; /* Full height */
        overflow: auto; /* Enable scroll if needed */
        background-color: rgb(0,0,0); /* Fallback color */
        background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
    }

    /* modal1 Content/Box */
    .modal1-content {
        background-color: #fefefe;
        margin: 15% auto; /* 15% from the top and centered */
        padding: 20px;
        border: 1px solid #888;
        width: 50%; /* Could be more or less, depending on screen size */
    }

    /* The Close Button */
    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }

    .close:hover,
    .close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }
</style>
<div data-ng-init="init()">
    <div id="productDef" data-ng-model="profileDefinition" class="row scm-form">

        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <div class="" row>
                    <h4>Search Profile</h4>
                    <div style="display: inline-block;">
                        <a class="waves-effect waves-light btn right"
                           data-ng-click="addProfileClicked()" style="margin: 1rem;" acl-action="SPPUPP">
                            ADD PROFILE
                        </a>
                    </div>
                </div>

                <div class="" row>
                    <div class="input-field">
                        <select ui-select2="productSelectOptions" ng-model="profileId"
                                data-ng-change="selectProfile(profileId)" data-placeholder="Enter name of a Profile">
                            <option ng-repeat="profile in profileDefinitions track by profile.profileId"
                                    value="{{profile.profileId}}">{{profile.profileName}}
                            </option>
                        </select>
                    </div>
                </div>

            </div>
        </div>
        <div class="row" data-ng-show="addProductFlag">
            <div class="col s6">
                <div class="left">
                    <h5 style="margin:0px;">Profile Definition</h5>
                </div>
            </div>
        </div>
        <form id="product" data-ng-show="addProductFlag" name="profileForm" class="white z-depth-3 scm-form" novalidate>
            <div class="row">
                <div class="col s12 m6 l6">
                    <div class="form-element">
                        <label class=" black-text" for="profileName">Profile Name</label>
                        <input id="profileName" name="profileName" data-ng-model="profileDefinition.profileName"
                               type="text" ng-minlength="1" ng-maxlength="100" allow-pattern="(\d|[a-z])" ng-pattern-restrict="^$|^[A-Za-z0-9]+" required>
                        <p ng-show="profileForm.profileName.$error.maxlength" class="errorMessage">Profile name is too
                            large.</p>
                        <p ng-show="profileForm.profileName.$error.minlength" class="errorMessage">Profile name is too
                            small.</p>
                        <p ng-show="profileForm.profileName.$error.pattern" class="errorMessage">Profile name can contain only alphanumeric</p>
                        <p ng-show="profileForm.profileName.$error.required" class="errorMessage">Profile name is
                            required.</p>
                    </div>

                    <div class="form-element">
                        <label class=" black-text" for="profileDescription">Profile Description</label>
                        <textarea id="profileDescription" name="profileDescription"
                                  data-ng-model="profileDefinition.profileDescription" data-ng-maxlength="300"
                                  required></textarea>
                        <p ng-show="profileForm.profileDescription.$error.maxlength" class="errorMessage">Profile
                            description is too large.</p>
                        <p ng-show="profileForm.profileDescription.$error.required" class="errorMessage">Profile
                            description is required.</p>
                    </div>
                    <div class="form-element">
                        <input id="uniqueNumberAvailable"
                               data-ng-model="profileDefinition.uniqueNumberAvailable" type="checkbox"/>
                        <label class="black-text" for="uniqueNumberAvailable">Unique Number Available</label>
                    </div>
                    <div class="form-element" data-ng-if="profileDefinition.uniqueNumberAvailable == true">
                        <label class=" black-text" for="uniqueFieldName">Unique Number Name</label>
                        <input id="uniqueFieldName" name="uniqueFieldName" data-ng-model="profileDefinition.uniqueFieldName"
                               type="text" ng-maxlength="50" placeholder="Serial Number" required>
                        <p ng-show="profileForm.uniqueFieldName.$error.maxlength" class="errorMessage">Unique Field Name is too
                            large.</p>
                        <p ng-show="profileForm.uniqueFieldName.$error.required" class="errorMessage">Unique Field Name is
                            required.</p>
                    </div>
                </div>
            </div>
        </form>
        <div class="row" style="margin-top:10px;" data-ng-show="addProductFlag">
            <div class="col s12">
                <button data-ng-if="!editMode && profileForm.$valid" data-ng-click="createProfile()" class="btn right"
                        acl-action="SPAAPD"> Create
                    <i class="material-icons right">send</i>
                </button>
            </div>
        </div>

        <div data-ng-show=" selectedProfileId != null" class="row product-detail white z-depth-3">
            <div class="col s12">
                <div class="row" style="margin-bottom:0px;">
                    <div class="col s7" style="padding-left:0px;">
                        <h3>{{selectedProfile.profileName}}</h3>
                    </div>
                    <div class="col s5">
                        <!--<a class="waves-effect waves-light btn right"-->
                        <!--data-ng-click="" style="margin: 1rem;" acl-action="SPPUPP">EDIT</a>-->
                        <div class="input-field right" style="margin-right: 10px;"
                             data-ng-if="selectedProfile.profileStatus == 'IN_ACTIVE'">
                            <a id="profileStatus" data-ng-click="changeStatus(selectedProfile.profileId, 'ACTIVE')"
                               class="waves-effect waves-light btn validate" acl-action="SPPAPC">Activate</a>
                        </div>
                        <div class="input-field right"
                             data-ng-if="selectedProfile.profileStatus == 'ACTIVE'">
                            <a id="profileActiveStatus"
                               class="waves-effect waves-light btn validate"
                               data-ng-click="changeStatus(selectedProfile.profileId, 'IN_ACTIVE')"
                               acl-action="SPPDPE">Deactivate</a>
                        </div>
                    </div>
                    <div class="col s12">
                        <div class="col s6">Unique Number Availabe ? {{selectedProfile.uniqueNumberAvailable}}</div>
                        <div data-ng-if="selectedProfile.uniqueNumberAvailable" class="col s6">Unique Field Name: {{selectedProfile.uniqueFieldName}}</div>
                    </div>
                </div>
            </div>
            <div class="col s12">
                <div class="input-field right">
                    <a class="btn" data-ng-click="createNewMapping();openAddModal();" >Add Mapping</a>
                </div>
            </div>
            <div class="col s12">
                <div class="row">
                    <div data-ng-if="selectedProfileAttributeMappings.length == 0">
                        There is no mapping For this Profile
                    </div>
                    <table data-ng-if="selectedProfileAttributeMappings.length > 0" class="responsive-table centered">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>Attribute Name</th>
                            <th>Is Defied At SKU</th>
                            <th>Is Defied At Asset</th>
                            <th>Is Mandatory At SKU</th>
                            <th>Is Mandatory At Asset</th>
                            <th>Is Overridable At Asset</th>
                            <th>Participate In Name</th>
                            <th>Is Stand Alone</th>
                            <th>Status</th>
                            <th>Actions</th>
                            <!--<th>Add Image</th>-->
                            <!--<th>Preview Image</th>-->
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="mapping in selectedProfileAttributeMappings">
                            <td>
                                {{mapping.profileAttributeMappingId}}
                            </td>
                            <td>
                                {{getAttributeName(mapping.attributeId)}}
                            </td>

                            <td>
                                {{mapping.definedAtSKU}}
                            </td>
                            <td>
                                {{mapping.definedAtAsset}}
                            </td>
                            <td>
                                {{mapping.mandatoryAtSKU}}
                            </td>
                            <td>
                                {{mapping.mandatoryAtAsset}}
                            </td>
                            <td>
                                {{mapping.overridableAtSKU}}
                            </td>
                            <td>
                                {{mapping.participateInName}}
                            </td>
                            <td>
                                {{mapping.standAlone}}
                            </td>
                            <td>
                                {{mapping.status}}
                            </td>
                            <td>
                                <div class="row" style="min-width: 300px;">
                                    <div class="col s5">
                                        <a class="btn"   data-ng-click="editMapping(mapping)" >Edit</a>
                                    </div>
                                    <div class="col s6">
                                        <a class="waves-effect waves-light btn right"
                                           data-ng-click="setImageViaUploadDoc(mapping.profileAttributeMappingId, mapping)"
                                           acl-action="SPPUPP">SET IMAGE
                                        </a>
                                    </div>
                                    <div class="col s1">
                                        <p data-ng-if="mapping.associatedImage != null">
                                            <a href="{{'#' + mapping.profileAttributeMappingId + '_image'}}" modal
                                               acl-action="RECPRA">
                                                <i class="fa fa-eye" aria-hidden="true"></i>
                                            </a>
                                        <div class="modal" id="{{mapping.profileAttributeMappingId + '_image'}}"
                                             style="width: fit-content;height: fit-content; overflow:visible;top: 16%;">
                                            <div class="frame" style="margin-top: 40px;">
                                                <img style="    max-height: 70vh;max-width: 70vw;"
                                                     data-ng-if="mapping.associatedImage != null"
                                                     data-ng-src="{{profileBaseUrl}}{{mapping.associatedImage}}"/>
                                            </div>
                                            <div class="col s2 form-element">
                                                <a class="modal-action modal-close waves-effect btn"
                                                   href="javascript:void(0)">BACK</a>
                                            </div>
                                        </div>
                                        </p>
                                    </div>
                                </div>
                            </td>
                        </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal Structure -->
<!--<div id="edit"  class="modal modal-large">-->
    <!--<div class="modal-content">-->
        <!--<div class="row">-->
            <!--<div class="col s12">-->
                <!--<p style="font-size: 18px;">Add Attribute for:-->
                    <!--{{selectedProfile.profileName}}-->
                <!--</p>-->

                    <!--<div class="row">-->
                        <!--<div data-ng-show="newMapping.profileAttributeMappingId == null" class="col s6"-->
                             <!--style="display:inline !important;">-->
                            <!--<h5>Select Attribute Definition</h5>-->
                        <!--</div>-->
                        <!--<div data-ng-show="newMapping.profileAttributeMappingId == null" class="col s6">-->
                            <!--<select ui-select2="selectOptions" ng-model="attributeId"-->
                                    <!--data-ng-change="setAttrubute(newMapping,attributeId)"-->
                                    <!--data-placeholder="Enter name of a attribute">-->
                                <!--<option ng-repeat="attribute in attributes | filter:filterByType"-->
                                        <!--value="{{attribute.attributeId}}">{{attribute.attributeName}}-->
                                <!--</option>-->
                            <!--</select>-->
                        <!--</div>-->
                        <!--<div data-ng-show="newMapping.profileAttributeMappingId != null">-->
                            <!--Attribute Name : {{getAttributeName(newMapping.attributeId)}}-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="col s6">-->
                        <!--<label>Defined At</label>-->
                        <!--<input type="radio" data-ng-model="definedAt" data-ng-change="definedAtChanged(newMapping, definedAt)" value="SKU" id="sku">-->
                        <!--<label class="form-check-label" for="sku">-->
                            <!--SKU-->
                        <!--</label>-->
                        <!--<input type="radio" data-ng-model="definedAt" data-ng-change="definedAtChanged(newMapping, definedAt)"  value="ASSET" id="asset">-->
                        <!--<label class="form-check-label" for="asset">-->
                            <!--ASSET-->
                        <!--</label>-->
                        <!--<div class="form-element">-->
                            <!--<label>Mapping Status</label>-->
                            <!--<input type="radio" data-ng-model="newMapping.status" value="ACTIVE" id="active1">-->
                            <!--<label class="form-check-label" for="active1">-->
                                <!--ACTIVE-->
                            <!--</label>-->
                            <!--<input type="radio" data-ng-model="newMapping.status" value="IN_ACTIVE" id="inActive1">-->
                            <!--<label class="form-check-label" for="inActive1">-->
                                <!--IN ACTIVE-->
                            <!--</label>-->
                        <!--</div>-->
                        <!--<div class="form-element">-->
                            <!--<input id="mandatoryAtSKU" ng-disabled="newMapping.definedAtAsset"-->
                                   <!--data-ng-model="newMapping.mandatoryAtSKU" type="checkbox"/>-->
                            <!--<label class="black-text" for="mandatoryAtSKU">Mandatory for SKU</label>-->
                        <!--</div>-->
                    <!--</div>-->
                    <!--<div class="col s6">-->
                        <!--<div class="form-element">-->
                            <!--<input id="mandatoryAtAsset"-->
                                   <!--data-ng-model="newMapping.mandatoryAtAsset" type="checkbox"/>-->
                            <!--<label class="black-text" for="mandatoryAtAsset">Mandatory for Asset</label>-->
                        <!--</div>-->
                        <!--<div class="form-element">-->
                            <!--<input id="overridableAtSKU" ng-disabled="newMapping.definedAtAsset"-->
                                   <!--data-ng-model="newMapping.overridableAtSKU" type="checkbox"/>-->
                            <!--<label class="black-text" for="overridableAtSKU">Is Overridable At Asset</label>-->
                        <!--</div>-->
                        <!--<div class="form-element">-->
                            <!--<input id="participateInName" ng-disabled="newMapping.definedAtAsset"-->
                                   <!--data-ng-model="newMapping.participateInName" type="checkbox"/>-->
                            <!--<label class="black-text" for="participateInName">Participates In Name</label>-->
                        <!--</div>-->
                        <!--<div class="form-element">-->
                            <!--<input id="standAlone" ng-disabled="!newMapping.definedAtAsset"-->
                                   <!--data-ng-model="newMapping.standAlone" type="checkbox"/>-->
                            <!--<label class="black-text" for="standAlone">Is Stand Alone</label>-->
                        <!--</div>-->
                    <!--</div>-->


                <!--<div class="right-align">-->
                    <!--<button id="closeBtn" class="modal-action modal-close waves-effect waves-green btn" data-ng-click="reset()">-->
                        <!--Cancel-->
                    <!--</button>-->
                    <!--<button class="modal-action  waves-effect waves-green btn" data-ng-click="addMapping()">-->
                        <!--Submit-->
                    <!--</button>-->
                <!--</div>-->
            <!--</div>-->
        <!--</div>-->
    <!--</div>-->
<!--</div>-->

<div id="_editDefinitionModal" class="modal1">

    <div class="modal1-content">
        <div class="modal1-header">
            <button style="background: none;border: none;float: right;" data-ng-click="closeEditModal()"><span class="close">&times;</span></button>
            <h5>Edit Attribute Mapping</h5>
        </div>
        <div class="modal1-body">
            <div class="row">
                <div class="col s12">
                    <p style="font-size: 18px;">Edit Attribute for:
                        {{selectedProfile.profileName}}
                    </p>

                    <div class="row">
                        <!--<div data-ng-show="newMapping.profileAttributeMappingId == null" class="col s6"-->
                             <!--style="display:inline !important;">-->
                            <!--<h5>Select Attribute Definition</h5>-->
                        <!--</div>-->
                        <!--<div data-ng-show="newMapping.profileAttributeMappingId == null" class="col s6">-->
                            <!--<select ui-select2="selectOptions" ng-model="attributeId"-->
                                    <!--data-ng-change="setAttrubute(newMapping,attributeId)"-->
                                    <!--data-placeholder="Enter name of a attribute">-->
                                <!--<option ng-repeat="attribute in attributes | filter:filterByType"-->
                                        <!--value="{{attribute.attributeId}}">{{attribute.attributeName}}-->
                                <!--</option>-->
                            <!--</select>-->
                        <!--</div>-->
                        <div data-ng-show="newMapping.profileAttributeMappingId != null">
                            Attribute Name : {{getAttributeName(newMapping.attributeId)}}
                        </div>
                    </div>
                    <div class="col s6">
                        <label>Defined At</label>
                        <input type="radio" data-ng-model="definedAt" data-ng-change="definedAtChanged(newMapping, definedAt)" value="SKU" id="sku1">
                        <label class="form-check-label" for="sku1">
                            SKU
                        </label>
                        <input type="radio" data-ng-model="definedAt" data-ng-change="definedAtChanged(newMapping, definedAt)"  value="ASSET" id="asset1">
                        <label class="form-check-label" for="asset1">
                            ASSET
                        </label>
                        <div class="form-element">
                            <label>Mapping Status</label>
                            <input type="radio" data-ng-model="newMapping.status" value="ACTIVE" id="active">
                            <label class="form-check-label" for="active">
                                ACTIVE
                            </label>
                            <input type="radio" data-ng-model="newMapping.status" value="IN_ACTIVE" id="inActive">
                            <label class="form-check-label" for="inActive">
                                IN ACTIVE
                            </label>
                        </div>
                        <div class="form-element">
                            <input id="mandatoryAtSKU1" ng-disabled="newMapping.definedAtAsset"
                                   data-ng-model="newMapping.mandatoryAtSKU" type="checkbox"/>
                            <label class="black-text" for="mandatoryAtSKU1">Mandatory for SKU</label>
                        </div>
                    </div>
                    <div class="col s6">
                        <div class="form-element">
                            <input id="mandatoryAtAsset1"ng-disabled="!newMapping.definedAtAsset"
                                   data-ng-model="newMapping.mandatoryAtAsset" type="checkbox"/>
                            <label class="black-text" for="mandatoryAtAsset1">Mandatory for Asset</label>
                        </div>
                        <div class="form-element">
                            <input id="overridableAtSKU1" ng-disabled="newMapping.definedAtAsset"
                                   data-ng-model="newMapping.overridableAtSKU" type="checkbox"/>
                            <label class="black-text" for="overridableAtSKU1">Is Overridable At Asset</label>
                        </div>
                        <div class="form-element">
                            <input id="participateInName1" ng-disabled="newMapping.definedAtAsset"
                                   data-ng-model="newMapping.participateInName" type="checkbox"/>
                            <label class="black-text" for="participateInName1">Participates In Name</label>
                        </div>
                        <div class="form-element">
                            <input id="standAlone1" ng-disabled="!newMapping.definedAtAsset"
                                   data-ng-model="newMapping.standAlone" type="checkbox"/>
                            <label class="black-text" for="standAlone1">Is Stand Alone</label>
                        </div>
                    </div>



                </div>
            </div>
        </div>
        <div class="modal1-footer">
            <div class="right-align">
                <button id="closeBtn1" class="modal-action modal-close waves-effect waves-green btn" data-ng-click="closeEditModal()">
                    Cancel
                </button>
                <button class="modal-action  waves-effect waves-green btn" data-ng-click="addMapping()">
                    Submit
                </button>
            </div>
        </div>
    </div>

</div>

<div data-ng-if="addModal" id="_addDefinitionModal" class="modal1" >

    <div class="modal1-content">
        <div class="modal1-header">
            <button style="background: none;border: none;float: right;" data-ng-click="closeAddModal()"><span class="close">&times;</span></button>
            <h5>Add Attribute Mapping</h5>
        </div>
        <div class="modal1-body">
            <div class="row">
                <div class="col s12">
                    <p style="font-size: 18px;">Add Attribute for:
                        {{selectedProfile.profileName}}
                    </p>

                    <div class="row">
                        <div data-ng-show="newMapping.profileAttributeMappingId == null" class="col s6"
                             style="display:inline !important;">
                            <h5>Select Attribute Definition</h5>
                        </div>
                        <div data-ng-show="newMapping.profileAttributeMappingId == null" class="col s6">
                            <select ui-select2="selectOptions" ng-model="attributeId"
                                    data-ng-change="setAttrubute(newMapping,attributeId)"
                                    data-placeholder="Enter name of a attribute">
                                <option ng-repeat="attribute in attributes | filter:filterByType"
                                        value="{{attribute.attributeId}}">{{attribute.attributeName}}
                                </option>
                            </select>
                        </div>
                        <!--<div data-ng-show="newMapping.profileAttributeMappingId != null">-->
                        <!--Attribute Name : {{getAttributeName(newMapping.attributeId)}}-->
                        <!--</div>-->
                    </div>
                    <div class="col s6">
                        <label>Defined At</label>
                        <input type="radio" data-ng-model="definedAt" data-ng-change="definedAtChanged(newMapping, definedAt)" value="SKU" id="sku2">
                        <label class="form-check-label" for="sku2">
                            SKU
                        </label>
                        <input type="radio" data-ng-model="definedAt" data-ng-change="definedAtChanged(newMapping, definedAt)"  value="ASSET" id="asset2">
                        <label class="form-check-label" for="asset2">
                            ASSET
                        </label>
                        <div class="form-element">
                            <label>Mapping Status</label>
                            <input type="radio" data-ng-model="newMapping.status" value="ACTIVE" id="active2">
                            <label class="form-check-label" for="active2">
                                ACTIVE
                            </label>
                            <input type="radio" data-ng-model="newMapping.status" value="IN_ACTIVE" id="inActive2">
                            <label class="form-check-label" for="inActive2">
                                IN ACTIVE
                            </label>
                        </div>
                        <div class="form-element">
                            <input id="mandatoryAtSKU2" ng-disabled="newMapping.definedAtAsset"
                                   data-ng-model="newMapping.mandatoryAtSKU" type="checkbox"/>
                            <label class="black-text" for="mandatoryAtSKU2">Mandatory for SKU</label>
                        </div>
                    </div>
                    <div class="col s6">
                        <div class="form-element">
                            <input id="mandatoryAtAsset2" ng-disabled="!newMapping.definedAtAsset"
                                   data-ng-model="newMapping.mandatoryAtAsset" type="checkbox"/>
                            <label class="black-text" for="mandatoryAtAsset2">Mandatory for Asset</label>
                        </div>
                        <div class="form-element">
                            <input id="overridableAtSKU2" ng-disabled="newMapping.definedAtAsset"
                                   data-ng-model="newMapping.overridableAtSKU" type="checkbox"/>
                            <label class="black-text" for="overridableAtSKU2">Is Overridable At Asset</label>
                        </div>
                        <div class="form-element">
                            <input id="participateInName2" ng-disabled="newMapping.definedAtAsset"
                                   data-ng-model="newMapping.participateInName" type="checkbox"/>
                            <label class="black-text" for="participateInName2">Participates In Name</label>
                        </div>
                        <div class="form-element">
                            <input id="standAlone2" ng-disabled="!newMapping.definedAtAsset"
                                   data-ng-model="newMapping.standAlone" type="checkbox"/>
                            <label class="black-text" for="standAlone2">Is Stand Alone</label>
                        </div>
                    </div>



                </div>
            </div>
        </div>
        <div class="modal1-footer">
            <div class="right-align">
                <button id="closeBtn2" class="modal-action modal-close waves-effect waves-green btn" data-ng-click="closeAddModal()">
                    Cancel
                </button>
                <button class="modal-action  waves-effect waves-green btn" data-ng-click="addMapping()">
                    Submit
                </button>
            </div>
        </div>
    </div>

</div>