<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .scrollabe-table {
        overflow-x: auto !important;
        white-space: nowrap !important;
    }

    .scrollabe-table thead tr th {
        text-align: center !important;
    }

    tr td {
        text-align: center !important;
    }
</style>
<div class="row" data-ng-init="init()">
    <div class="row white z-depth-3">
        <div class="col s12">
            <h3>Asset Recovery</h3>
        </div>
        <div class="row searchingCard">
            <div class="col s3">
                <label>Recovery Status</label>
                <select data-ng-model="recoveryStatus"
                        data-ng-options="type as type for type in recoveryStatusList"></select>
            </div>
            <div class="col s6">
                <div>
                    <label>Select Unit</label>
                    <select data-ng-model="selectedUnitData"
                            data-ng-options="unit as unit.name for unit in unitList track by unit.id"></select>
                </div>
            </div>
            <div class="col s3 margin-top-10">
                <button
                        class="btn btn-small"
                        ng-click="getAssetForRecovery()"
                        style="margin-top: 14px;width: 115px;" acl-action="TRNPPV">Get Records
                </button>
            </div>
        </div>
        <!--<div class="row">-->
            <!--<div class="col-s3">-->
                <!--<label>Filter:</label>-->
                <!--<input-->
                    <!--type="text"-->
                    <!--ng-model="search"-->
                    <!--placeholder="Filter"/>-->
            <!--</div>-->
        <!--</div>-->
    </div>
        <div class="row white z-depth-3" data-ng-if="assetRecoveryList.length > 0
        && (assetRecoveryListType == 'RECOVERED' || assetRecoveryListType == 'INITIATED')">
        <div class="row">
            <table class="table table-bordered scrollabe-table">
                <thead>
                <tr style="background-color: darkseagreen;">
                    <th>ID</th>
                    <th>Asset ID</th>
                    <th>Asset Name</th>
                    <th>Tag Value</th>
                    <th>Employee ID</th>
                    <th>Employee Name</th>
                    <th>Recovery Amount</th>
                    <th>Recovery Initiation Date</th>
                    <th>
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="2" align="center">Recovered</td>
                            </tr>
                            <tr>
                                <td>
                                    <input type="checkbox"
                                           style="width: 33px; height: 20px; position:inherit; opacity:1"
                                           data-ng-model="checkBoxAll"
                                           ng-click="checkAllRecovered(checkBoxAll)"/>
                                </td>
                            </tr>
                        </table>
                    </th>
                    <th>
                        <table style="width: 100%;">
                            <tr>
                                <td colspan="2" align="center">Salary Reduction Date</td>
                            </tr>
                            <!--<tr>-->
                                <!--<td>-->
                                    <!--<input style="max-width: 200px;" input-date type="text"-->
                                           <!--name="created"-->
                                           <!--data-ng-model="salaryAllDeductionDate" ng-change="allDeductionDate(salaryAllDeductionDate)"-->
                                           <!--container="" format="yyyy-mm-dd" select-years="1" required/>-->
                                <!--</td>-->
                            <!--</tr>-->
                        </table>
                    </th>
                </tr>
                </thead>
                <tbody>
                <!--<tr data-ng-repeat="recovery in filtered = (assetRecoveryList | filter:search ) track by recovery.assetRecoveryId">-->
                <tr data-ng-repeat="recovery in assetRecoveryList track by recovery.assetRecoveryId">
                    <td>{{recovery.assetRecoveryId}}</td>
                    <td>{{recovery.assetId}}</td>
                    <td>{{recovery.assetDefinition.assetName}}</td>
                    <td>{{recovery.assetDefinition.tagValue}}</td>
                    <td>{{recovery.recoveryEmpId}}</td>
                    <td>{{recovery.recoveryEmp.name}}</td>
                    <td>{{recovery.recoveryAmount}}</td>
                    <td>{{recovery.creationDate | date:'dd/MM/yyyy'}}</td>
                    <td>
                        <input id="{{'recovered_' + recovery.assetRecoveryId}}"
                               data-ng-disabled="recovery.recoveryStatus == 'RECOVERED'" type="checkbox"
                               data-ng-model="recovery.recovered"/>
                        <label class="black-text " for="{{'recovered_' + recovery.assetRecoveryId}}"></label>
                    </td>
                    <td>
                        <input style="max-width: 200px;" input-date type="text"
                               ng-disabled="recovery.recoveryStatus == 'RECOVERED'"
                               name="created" id="{{reduction_date + recovery.assetRecoveryId}}}"
                               data-ng-model="recovery.salaryDeductionDate"
                               container="" format="yyyy-mm-dd" select-years="1" required/>
                    </td>
                </tr>
                </tbody>
            </table>
            <button data-ng-if="toggleRecoverButton" style="float: right;margin-right: 10px;"
                    class="modal-action modal-close waves-effect waves-green btn" data-ng-click="setAsRecovered()">
                Recover
            </button>
        </div>
    </div>
    <div class="row white z-depth-3" data-ng-if="assetRecoveryList.length > 0
        && ( assetRecoveryListType == 'PENDING_LOST')">
        <div class="row">
            <table class="table table-bordered scrollabe-table">
                <thead>
                <tr style="background-color: darkseagreen;">
                    <th>ID</th>
                    <th>Asset ID</th>
                    <th>Asset Name</th>
                    <th>Tag Value</th>
                    <th>Employee ID</th>
                    <th>Employee Name</th>
                    <th>Found</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="recovery in assetRecoveryList track by recovery.assetRecoveryId">
                    <td>{{recovery.assetRecoveryId}}</td>
                    <td>{{recovery.assetId}}</td>
                    <td>{{recovery.assetDefinition.assetName}}</td>
                    <td>{{recovery.assetDefinition.tagValue}}</td>
                    <td>{{recovery.recoveryEmpId}}</td>
                    <td>{{recovery.recoveryEmp.name}}</td>
                    <td>
                        <input id="{{'pending_lost_' + recovery.assetRecoveryId}}"
                               data-ng-disabled="recovery.recoveryStatus != 'PENDING_LOST'" type="checkbox"
                               data-ng-model="recovery.found"/>
                        <label class="black-text " for="{{'pending_lost_' + recovery.assetRecoveryId}}"></label>
                    </td>
                </tr>
                </tbody>
            </table>
            <button  style="float: right;margin-right: 10px;"
                    class="modal-action modal-close waves-effect waves-green btn" data-ng-click="submitRecovery()">
                Submit
            </button>
        </div>
    </div>
    <div class="row white z-depth-3" data-ng-if="assetRecoveryList.length == 0" style="padding: 16px;">
        Could not find any recovery record for specified unit and status
    </div>

</div>