<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3">
            <div class="col s12">
                <h4 data-ng-if="!showViewActions">Pending Purchase Orders Requests</h4>
                <h4 data-ng-if="showViewActions">View Purchase Orders</h4>
            </div>
            <div class="searchingCard">
            <div class="col s12 margin-bottom-15">
                <div class="col s2">
                    <label>Select Start date</label>
                    <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select End date</label>
                    <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select Unit</label>
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Unit'}" ng-options="userMappingUnit as userMappingUnit.name for userMappingUnit in userMappingUnits"
                            data-ng-change="selectUnit(unitSelected)" data-ng-model="unitSelected"></select>
                </div>
                <div class="col s2">
                <label>Purchase Order Id</label>
                <input type="number" placeholder="PO ID" name="poId" id="poId" ng-model="poId"/>
                </div>
                 <div class="col s2">
                    <label>Select Status</label>
                    <select data-ng-model="selectedStatus"
                            data-ng-options="type as type for type in txnStatus"></select>
                </div>

                <div class="col s2">
                    <label>Select Vendor</label>
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" ng-options="vendor as vendor.entityName for vendor in vendors"
                            data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                </div>

                <div class="col s2">
                    <label>Select SKU</label>
                    <select id="skuList" ui-select2="{allowClear:true, placeholder: 'Select SKU'}" ng-options="sku.id as sku.name for sku in skus"
                            data-ng-change="selectSKU(skuSelected)" data-ng-model="skuSelected"></select>
                </div>
            </div>
            <div class="col s12 margin-bottom-15">
                 <div class="col s3">
                    <button class="btn margin-top-20" data-ng-click="getPOs()" acl-action="VOMVPOV" style="margin-left: 410px;">Find</button>
                </div>
                </div>
        </div>
            <hr>
            <div class="col s12" data-ng-if="poRequest.length==0" style="padding:30px;color:gray;font-size: 20px;">
                <div class="row margin0 center" ng-if="!showViewActions">
                    No Pending Orders found for Approval
                </div>
                <div class="row margin0 center" ng-if="showViewActions">
                    No Purchase Orders found for the selected criteria
                </div>
            </div>
            <div class="col s3 right" data-ng-if="poRequest.length>0">
                <button class="btn margin-top-20" data-ng-click="downloadExcell()" >Download Excell</button>
            </div>
            <div class="col s12" ng-if="poRequest.length>0">
                <div class="respTable standardView">
                <div class="row">
                    <ul class="collection striped center" >
                        <li class="collection-item list-head">
                            <div class="row" style="font-size:12px">
                                <div class="col s1">ID</div>
                                <div class="col s1">Receipt Number</div>
                                <div class="col s1">Generation Time</div>
                                <div class="col s1">Location Name</div>
                                <div class="col s1">Vendor name</div>
                                <div class="col s1">Delivery Date</div>
                                <div class="col s1">Bill Amount</div>
                                <div class="col s1">Paid Amount</div>
                                <div class="col s1">Status</div>
                                <div class="col s1">Expiry Date</div>
                                <div class="col s1">Lead Time</div>
                                <div class="col s1" data-ng-if="isPurchaser" align="center">Actions</div>
                            </div>
                        </li>
                        <li class="collection-item " style="padding:5px;" data-ng-repeat="poR in poRequest track by $index">
                            <div class="row margin0" data-ng-class="{'red white-text': poR.id==createdPO}" style="font-size:12px;">
                                <div class="col s1">{{poR.id}}</div>
                                <div class="col s1"><a href="#viewPODetails" data-ng-click="showDetails($index)" modal>{{poR.receiptNumber}}</a></div>
                                <div class="col s1">{{poR.generationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                                <div class="col s1">{{poR.dispatchLocation.city}}[{{poR.dispatchLocation.locationName}}]</div>
                                <div class="col s1">{{poR.generatedForVendor.name}}</div>
                                <div class="col s1">{{poR.fulfillmentDate | date :'dd-MM-yyyy'}}</div>
                                <div class="col s1">{{poR.billAmount.toFixed(2)}}</div>
                                <div class="col s1">{{poR.paidAmount.toFixed(2)}}</div>
                                <div class="col s1">{{poR.status}}</div>
                                <div class="col s1">
                                    <span data-ng-if="poR.status=='CREATED' || poR.expiryDate==null">-</span>
                                    {{poR.expiryDate | date :'dd-MM-yyyy @ h:mma'}}
                                </div>

                                <div class="col s1">
                                    <span  data-ng-if="poR.leadTime==null">-</span>
                                    {{poR.leadTime}}
                                </div>

                                <div class="col s1" data-ng-if="!showViewActions && poR.status=='CREATED'">
                                    <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                            a href="#viewPOApproveDetails" data-ng-click="showApproveDetails(poR.id,$index)" modal>Actions</button>

                                    <span data-ng-if="poR.status!='CREATED'">
                                        No Actions available for this PO
                                    </span>
                                </div>
                                <div class="col s1" data-ng-if="showViewActions && isPurchaser">

                                    <a class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                       href="#descriptionModal" data-toggle="modal"
                                       data-ng-if="((poR.status=='IN_PROGRESS' || poR.status=='APPROVED') && (poR.expiryStatus=='EXPIRED' || poR.expiryStatus=='UN_EXPIRED') && !poR.hideExtend)"
                                       data-ng-click="description(poR,$index)" modal>Extend</a>

                                    <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                            data-ng-if="(poR.status=='CREATED' || poR.status=='APPROVED')"
                                            data-ng-click="cancelPO(poR.id,$index)" acl-action="VOMVPOC">Cancel</button>

                                    <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                            data-ng-if="poR.status=='IN_PROGRESS'"
                                            data-ng-click="closePO(poR.id,$index)" acl-action="VOMVPOCL">Close</button>

                                    <span data-ng-if="!(poR.status=='CREATED' || poR.status=='IN_PROGRESS' || poR.status=='APPROVED')">
                                        No Actions available for this PO
                                    </span>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                </div>
                <div class="TableMobileView">
                    <div class="row">
                        <ul class="collection striped center">
                            <li class="collection-item" data-ng-repeat="poR in poRequest track by $index"
                                data-ng-class="{'red white-text': poR.id==createdPO}">
                                <div class="row">
                                    <div class="col s1">ID</div>
                                    <div class="col s1">{{poR.id}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Receipt Number</div>
                                    <div class="col s1"><a href="#viewPODetails" data-ng-click="showDetails($index)"
                                                           modal>{{poR.receiptNumber}}</a></div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Generation Time</div>
                                    <div class="col s1">{{poR.generationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Location Name</div>
                                    <div class="col s1">{{poR.dispatchLocation.city}}[{{poR.dispatchLocation.locationName}}]</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Vendor name</div>
                                    <div class="col s1">{{poR.generatedForVendor.name}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Delivery Date</div>
                                    <div class="col s1">{{poR.fulfillmentDate | date :'dd-MM-yyyy'}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Bill Amount</div>
                                    <div class="col s1">{{poR.billAmount.toFixed(2)}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Paid Amount</div>
                                    <div class="col s1">{{poR.paidAmount.toFixed(2)}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Status</div>
                                    <div class="col s1">{{poR.status}}</div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Expiry Date</div>
                                    <div class="col s1">
                                        <span data-ng-if="poR.status=='CREATED' || poR.expiryDate==null">-</span>
                                        {{poR.expiryDate | date :'dd-MM-yyyy @ h:mma'}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col s1">Lead Time</div>
                                    <div class="col s1">
                                        <span data-ng-if="poR.leadTime==null">-</span>
                                        {{poR.leadTime}}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col s1" data-ng-if="isPurchaser" align="center">Actions</div>
                                    <div class="col s1" data-ng-if="!showViewActions && poR.status=='CREATED'">
                                        <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10" a
                                                href="#viewPOApproveDetails" data-ng-click="showApproveDetails(poR.id,$index)"
                                                modal>Actions</button>

                                        <span data-ng-if="poR.status!='CREATED'">
                            No Actions available for this PO
                        </span>
                                    </div>
                                    <div class="col s1" data-ng-if="showViewActions && isPurchaser">

                                        <a class="btn btn-xs-small vBtn margin-right-5 margin-top-10" href="#descriptionModal"
                                           data-toggle="modal"
                                           data-ng-if="((poR.status=='IN_PROGRESS' || poR.status=='APPROVED') && (poR.expiryStatus=='EXPIRED' || poR.expiryStatus=='UN_EXPIRED') && !poR.hideExtend)"
                                           data-ng-click="description(poR,$index)" modal>Extend</a>

                                        <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                                data-ng-if="(poR.status=='CREATED' || poR.status=='APPROVED')"
                                                data-ng-click="cancelPO(poR.id,$index)" acl-action="VOMVPOC">Cancel</button>

                                        <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10"
                                                data-ng-if="poR.status=='IN_PROGRESS'" data-ng-click="closePO(poR.id,$index)"
                                                acl-action="VOMVPOCL">Close</button>

                                        <span
                                                data-ng-if="!(poR.status=='CREATED' || poR.status=='IN_PROGRESS' || poR.status=='APPROVED')">
                            No Actions available for this PO
                        </span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Modal Structure -->
<div id="viewPODetails" class="modal modal-large">
    <div class="modal-content">
        <div class="row">
            <h5>
                Purchase Order Number: <b>{{selectedPO.receiptNumber}}</b>
                <button data-ng-if="selectedPO.poInvoice!=null" class="btn right" data-ng-click="downloadPO(selectedPO.poInvoice)"
                        data-tooltip="Download PO" tooltipped>
                    <i class="fa fa-download"></i>
                </button>
            </h5>
            <p><b>Created for:</b> {{selectedPO.generatedForVendor.name}} [{{selectedPO.dispatchLocation.city}}]</p>
            <p><b>GSTIN:</b> {{selectedPO.dispatchLocation.gstStatus=="REGISTERED"
                ? selectedPO.dispatchLocation.gstin : selectedPO.dispatchLocation.gstStatus}}</p>
        </div>
        <div class="row margin0 standardView">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th class="center-align">SKU</th>
                    <th class="center-align">Unit Of Measure</th>
                    <th class="center-align">Packaging</th>
                    <th class="center-align">Packaging Qty</th>
                    <th class="center-align">Pending Qty</th>
                    <th class="center-align">Total Qty</th>
                    <th class="center-align">Price</th>
                    <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                    <th class="center-align">Taxes</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="item in selectedPO.orderItems track by $index">
                    <td class="center-align">{{item.skuName}}</td>
                    <td class="center-align">{{item.unitOfMeasure}}</td>
                    <td class="center-align">{{item.packagingName}}</td>
                    <td class="center-align">{{item.packagingQty}}</td>
                    <td class="center-align">{{item.requestedQuantity.toFixed(2) - item.receivedQuantity.toFixed(2)}}</td>
                    <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                    <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                    <td class="center-align">{{item.totalCost}}</td>
                    <td class="center-align">{{item.totalTax}}</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="TableMobileView">
            <ul class="collection striped center">
                <li class="collection-item" data-ng-repeat="item in selectedPO.orderItems track by $index">
                    <div class="row">
                        <div class="col">SKU</div>
                        <div class="col">{{item.skuName}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Unit Of Measure</div>
                        <div class="col">{{item.unitOfMeasure}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Packaging</div>
                        <div class="col">{{item.packagingName}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Packaging Qty</div>
                        <div class="col">{{item.packagingQty}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Pending Qty</div>
                        <div class="col">{{item.requestedQuantity.toFixed(2) - item.receivedQuantity.toFixed(2)}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Total Qty</div>
                        <div class="col">{{item.requestedQuantity.toFixed(2)}}</div>
                    </div>
                    <div class="row">
                        <div class="col">Price</div>
                        <div class="col">{{item.unitPrice.toFixed(2)}}</div>
                    </div>
                    <div class="row">
                        <div class="col" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</div>
                        <div class="col">{{item.totalCost}}</div>

                    </div>
                    <div class="row">
                        <div class="col">Taxes</div>
                        <div class="col">{{item.totalTax}}</div>
                    </div>
                    </tr>
                </li>
            </ul>
        </div>

        <div class="row right-align" style="line-height: 30px;">
            <b>Taxable Amount:</b> Rs.{{selectedPO.billAmount}}<br/>
            <b>Total Taxes:</b> Rs.{{selectedPO.totalTaxes}}<br/>
            <b>Paid Amount:</b> Rs.{{selectedPO.paidAmount}}
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
    </div>
</div>


<!--Modal Approve Structure-->
<div id="viewPOApproveDetails" class="modal modal-large">
    <div class="modal-content">
        <div class="row">
            <h5>
                Purchase Order Number: <b>{{selectedPOR.receiptNumber}}</b>
                <button data-ng-if="selectedPOR.poInvoice!=null" class="btn right" data-ng-click="downloadPO(selectedPOR.poInvoice)"
                        data-tooltip="Download PO" tooltipped>
                    <i class="fa fa-download"></i>
                </button>
            </h5>
            <p><b>Created for:</b> {{selectedPOR.generatedForVendor.name}} [{{selectedPOR.dispatchLocation.city}}]</p>
            <p><b>GSTIN:</b> {{selectedPOR.dispatchLocation.gstStatus=="REGISTERED"
                ? selectedPOR.dispatchLocation.gstin : selectedPOR.dispatchLocation.gstStatus}}</p>
        </div>
        <div class="row margin0">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th class="center-align">SKU</th>
                    <th class="center-align">Unit Of Measure</th>
                    <th class="center-align">Packaging</th>
                    <th class="center-align">Packaging Qty</th>
                    <th class="center-align">Pending Qty</th>
                    <th class="center-align">Total Qty</th>
                    <th class="center-align">Price</th>
                    <th class="center-align" tooltipped data-tooltip="Amount = Price * Quantity">Amount*</th>
                    <th class="center-align">Taxes</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="item in selectedPOR.orderItems track by $index">
                    <td class="center-align"><a data-ng-model="selectedSku" data-ng-click="openViewChart(item)">{{item.skuName}}</a></td>
                    <td class="center-align">{{item.unitOfMeasure}}</td>
                    <td class="center-align">{{item.packagingName}}</td>
                    <td class="center-align">{{item.packagingQty}}</td>
                    <td class="center-align">{{item.requestedQuantity.toFixed(2) - item.receivedQuantity.toFixed(2)}}</td>
                    <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                    <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                    <td class="center-align">{{item.totalCost}}</td>
                    <td class="center-align">{{item.totalTax}}</td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="row right-align" style="line-height: 30px;">
            <b>Taxable Amount:</b> Rs.{{selectedPOR.billAmount}}<br/>
            <b>Total Taxes:</b> Rs.{{selectedPOR.totalTaxes}}<br/>
            <b>Paid Amount:</b> Rs.{{selectedPOR.paidAmount}}
        </div>
        <div class="col s1" style="margin-bottom: 2rem">
            <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10 modal-action modal-close" data-ng-if="selectedPOR.billAmount>10000 && selectedPOR.billAmount<=20000"
                    acl-action="VAPOL1" data-ng-click="getApprove(selectedpoRId,seletedpoRIndex)">ApproveL1</button>
            <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10 modal-action modal-close" data-ng-if="selectedPOR.billAmount>20000 && selectedPOR.billAmount<=100000"
                    acl-action="VAPOL2" data-ng-click="getApprove(selectedpoRId,seletedpoRIndex)">ApproveL2</button>
            <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10 modal-action modal-close" data-ng-if="selectedPOR.billAmount>100000"
                    acl-action="VAPOL3" data-ng-click="getApprove(selectedpoRId,seletedpoRIndex)">ApproveL3</button>
            <button class="btn btn-xs-small vBtn margin-right-5 margin-top-10 modal-action modal-close"
                    data-ng-click="getReject(selectedpoRId,seletedpoRIndex)">Reject</button>
        </div>
    </div>
    <div class="modal-footer">
        <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
    </div>
</div>
<!-- Modal description Structure -->


<div class="modal fade" id="descriptionModal" role="dialog" tabindex="-1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">


            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel_description"> Provide Extension Description! </h4>
            </div>

            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="row">
                            <div>
                                <label>Select Reason</label>
                                <select data-ng-model="selectedReason" data-placeholder="Select Reason">
                                    <option value=""></option>
                                    <option ng-repeat="reason in availableReasons">{{reason}}</option>
                                </select>
                            </div>
                        </div>
                        <div class="row" id="dialog">
                            <div>
                                <label>Add Extension Description</label>
                                <input class="form-control" type="text" maxlength="200"
                                       data-ng-model="extensionDescription" placeholder="Add Description"
                                       required/>
                            </div>
                        </div>

                        <div class="row margin0" style="margin-top:5px;">
                            <label style="display: inline-block;margin-bottom:0px;" for="inputCreated">Select Extend date:</label>
                            <input style="max-width: 200px;" input-date type="text" name="created" id="inputCreated"
                                   data-ng-model="extendedDate" data-ng-change="changeSelectedDate(extendedDate)"
                                   container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                                   max="{{maxDate}}" />
                        </div>


                        <div class="row">
                        <div>
                        <button class=" modal-action modal-close btn btn-xs-small vBtn margin-right-5 margin-top-10" style="font-size: 15px; height: 30px; margin-top: 50px"
                                data-ng-click="extendPO(selectedpoR.id,selectedpoR.generatedForVendor.id,extensionDescription,selectedReason)">Submit</button>
                        </div>
                        <div class="modal-footer">
                            <button class="modal-action modal-close waves-effect waves-green btn-flat">Close</button>
                        </div>
                        <div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>

<script type="text/ng-template" id="consumptionApprove.html">
    <div class="row" data-ng-init="initChart()">
        <div class="col s12">
            <hc-chart options="chartOptions">Placeholder for generic chart</hc-chart>
        </div>
        <div class="col s12"><b>Stock At Hand:</b> {{sku.currentStock}} {{sku.skuData.uom}}</div>
        <div data-ng-if="pendingForSku != null" class="col s12">
            <span><b>Open POs:</b> {{(pendingForSku.poList!=null && pendingForSku.poList.length>0) ? pendingForSku.poList.join(", ") : 'None'}}</span><br/>
            <span><b>Pending Quantity:</b> {{(pendingForSku.requested - pendingForSku.received).toFixed(2)}}</span>
        </div>
    </div>
</script>
