package com.stpl.tech.scm.core.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.NotificationFallbackService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.VendorDetail;

/**
 * Created by Chaayos on 22-09-2016.
 */
@Service
public class NotificationFallbackServiceImpl implements NotificationFallbackService{

    Logger LOG = LoggerFactory.getLogger(NotificationFallbackServiceImpl.class);

    @Autowired
    private SCMNotificationService scmNotificationService;
    @Autowired
    private RequestOrderManagementService requestOrderManagementService;


    @Autowired
    private SCMCache scmCache;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendVendorRONotification(List<Integer> vendorIds, Date fulfillmentDate, boolean isMilk,
			NotificationType type) {
		List<VendorDetail> vendorDetails = new ArrayList<>();
		for (Integer vendorId : vendorIds) {
			vendorDetails.add(scmCache.getVendorDetails().get(vendorId));
		}
		List<RequestOrder> requestOrderList = requestOrderManagementService
				.getSpecializedROForFulfillmentDate(fulfillmentDate, false);
		scmNotificationService.sendVendorRONotification(true, vendorDetails, requestOrderList, isMilk, type);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void sendVendorGRNotification(List<Integer> vendorIds, Date fulfillmentDate, boolean isMilk,
			NotificationType type) {
		List<VendorDetail> vendorDetails = new ArrayList<>();
		for (Integer vendorId : vendorIds) {
			vendorDetails.add(scmCache.getVendorDetails().get(vendorId));
		}
		List<RequestOrder> requestOrders = requestOrderManagementService
				.getSpecializedROForFulfillmentDate(fulfillmentDate, true);
		scmNotificationService.sendVendorGRNotification(true, vendorDetails, requestOrders, isMilk, type);
	}


}
