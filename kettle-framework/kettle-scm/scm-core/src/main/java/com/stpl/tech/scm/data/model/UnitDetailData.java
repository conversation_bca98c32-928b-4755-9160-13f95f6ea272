/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * UnitDetailData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_DETAIL")
public class UnitDetailData implements java.io.Serializable {

    private int unitId;
    private String unitName;
    private UnitCategoryData unitCategory;
    private String unitEmail;
    private String unitRegion;
    private String unitStatus;
    private String tin;
    private Integer closingStartTime;
    private Integer closingEndTime;
    private Integer companyId;
    private String shortCode;

    public UnitDetailData() {
    }

    public UnitDetailData(int unitId, String unitName, UnitCategoryData unitCategory, String unitEmail, String unitStatus) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.unitCategory = unitCategory;
        this.unitEmail = unitEmail;
        this.unitStatus = unitStatus;
    }

    @Id
    @Column(name = "UNIT_ID", unique = true, nullable = false)
    public int getUnitId() {
        return this.unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME", nullable = false)
    public String getUnitName() {
        return this.unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CATEGORY_ID", nullable = false)
    public UnitCategoryData getUnitCategory() {
        return this.unitCategory;
    }

    public void setUnitCategory(UnitCategoryData unitCategory) {
        this.unitCategory = unitCategory;
    }

    @Column(name = "UNIT_EMAIL", nullable = false, length = 50)
    public String getUnitEmail() {
        return this.unitEmail;
    }

    public void setUnitEmail(String unitEmail) {
        this.unitEmail = unitEmail;
    }

    @Column(name = "UNIT_STATUS", nullable = false, length = 15)
    public String getUnitStatus() {
        return this.unitStatus;
    }

    public void setUnitStatus(String unitStatus) {
        this.unitStatus = unitStatus;
    }

    @Column(name = "TIN_NUMBER", nullable = true)
    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    @Column(name = "CLOSURE_FROM_TIME")
    public Integer getClosingStartTime() {
        return closingStartTime;
    }

    public void setClosingStartTime(Integer closingStartTime) {
        this.closingStartTime = closingStartTime;
    }

    @Column(name = "CLOSURE_TO_TIME")
    public Integer getClosingEndTime() {
        return closingEndTime;
    }

    public void setClosingEndTime(Integer closingEndTime) {
        this.closingEndTime = closingEndTime;
    }

    @Column(name = "COMPANY_ID")
	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

    @Column(name = "UNIT_REGION")
    public String getUnitRegion() {
        return unitRegion;
    }

    public void setUnitRegion(String unitRegion) {
        this.unitRegion = unitRegion;
    }

    @Column(name = "SHORT_CODE")
    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }
}
