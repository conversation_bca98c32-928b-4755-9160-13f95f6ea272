package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 19-04-2017.
 */
@Entity
@Table(name = "VENDOR_ACCOUNT_DETAILS")
public class VendorAccountDetailData {

    private Integer accountDetailId;
    private VendorDetailData vendorDetail;
    private String accountNumber;
    private String ifscCode;
    private String accountType;
    private String micrCode;
    private DocumentDetailData uploadedChequeDocumentID;
    private String contactNumber;
    private String accountContactName;
    private String accountContactEmail;
    private String accountKind;
    private Integer updatedBy;
    private Date updatedAt;
    private BigDecimal debitBalance;
    private String paymentBlocked = "N";
    private Date lastPaymentsBlockedOn;
    private String paymentsBlockedReason;
    private String section206;



    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ACCOUNT_DETAIL_ID", nullable = false, unique = true)
    public Integer getAccountDetailId() {
        return accountDetailId;
    }

    public void setAccountDetailId(Integer accountDetailId) {
        this.accountDetailId = accountDetailId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VENDOR_ID",nullable = false)
    public VendorDetailData getVendorDetail() {
        return vendorDetail;
    }

    public void setVendorDetail(VendorDetailData vendorDetail) {
        this.vendorDetail = vendorDetail;
    }

    @Column(name = "ACCOUNT_NUMBER", nullable = false)
    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    @Column(name = "IFSC_CODE", nullable = false)
    public String getIfscCode() {
        return ifscCode;
    }

    public void setIfscCode(String ifscCode) {
        this.ifscCode = ifscCode;
    }

    @Column(name = "ACCOUNT_TYPE", nullable = false)
    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    @Column(name = "MICR_CODE", nullable = false)
    public String getMicrCode() {
        return micrCode;
    }

    public void setMicrCode(String micrCode) {
        this.micrCode = micrCode;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CHEQUE_DOCUMENT", nullable = false)
    public DocumentDetailData getUploadedChequeDocumentID() {
        return uploadedChequeDocumentID;
    }

    public void setUploadedChequeDocumentID(DocumentDetailData uploadedChequeDocumentID) {
        this.uploadedChequeDocumentID = uploadedChequeDocumentID;
    }

    @Column(name = "ACCOUNT_CONTACT_NUMBER", nullable = false)
    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    @Column(name = "ACCOUNT_CONTACT_NAME", nullable = false)
    public String getAccountContactName() {
        return accountContactName;
    }

    public void setAccountContactName(String accountContactName) {
        this.accountContactName = accountContactName;
    }

    @Column(name = "ACCOUNT_CONTACT_EMAIL", nullable = false)
    public String getAccountContactEmail() {
        return accountContactEmail;
    }

    public void setAccountContactEmail(String accountContactEmail) {
        this.accountContactEmail = accountContactEmail;
    }

    @Column(name = "KIND_OF_ACCOUNT", nullable = false)
    public String getAccountKind() {
        return accountKind;
    }

    public void setAccountKind(String accountKind) {
        this.accountKind = accountKind;
    }


    @Column(name = "UPDATED_BY", nullable = false)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "UPDATED_AT", nullable = false)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "DEBIT_BALANCE", nullable = false)
    public BigDecimal getDebitBalance() {
        return debitBalance;
    }

    public void setDebitBalance(BigDecimal debitBalance) {
        this.debitBalance = debitBalance;
    }

    @Column(name = "PAYMENT_BLOCKED", nullable = false, length = 1)
    public String getPaymentBlocked() {
        return paymentBlocked;
    }

    public void setPaymentBlocked(String paymentBlocked) {
        this.paymentBlocked = paymentBlocked;
    }

    @Column(name = "LAST_PAYMENTS_BLOCKED_ON", nullable = false)
    public Date getLastPaymentsBlockedOn() {
        return lastPaymentsBlockedOn;
    }

    public void setLastPaymentsBlockedOn(Date lastPaymentsBlockedOn) {
        this.lastPaymentsBlockedOn = lastPaymentsBlockedOn;
    }

    @Column(name = "LAST_PAYMENTS_BLOCKED_REASON", nullable = false)
    public String getPaymentsBlockedReason() {
        return paymentsBlockedReason;
    }

    public void setPaymentsBlockedReason(String paymentsBlockedReason) {
        this.paymentsBlockedReason = paymentsBlockedReason;
    }

    @Column(name = "SECTION_206")
    public String getSection206() {
        return section206;
    }

    public void setSection206(String section206) {
        this.section206 = section206;
    }
}
