/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * SkuAttributeValueData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SKU_ATTRIBUTE_VALUE", uniqueConstraints = @UniqueConstraint(columnNames = {"SKU_ID", "ATTRIBUTE_ID"}))
public class SkuAttributeValueData implements java.io.Serializable {

    private Integer skuAttributeValueId;
    private int skuId;
    private int attributeId;
    private int attributeValueId;
    private String mappingStatus;

    public SkuAttributeValueData() {
    }

    public SkuAttributeValueData(int skuId, int attributeId, int attributeValueId, String mappingStatus) {
        this.skuId = skuId;
        this.attributeId = attributeId;
        this.attributeValueId = attributeValueId;
        this.mappingStatus = mappingStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "SKU_ATTRIBUTE_VALUE_ID", unique = true, nullable = false)
    public Integer getSkuAttributeValueId() {
        return this.skuAttributeValueId;
    }

    public void setSkuAttributeValueId(Integer skuAttributeValueId) {
        this.skuAttributeValueId = skuAttributeValueId;
    }

    @Column(name = "SKU_ID", nullable = false)
    public int getSkuId() {
        return this.skuId;
    }

    public void setSkuId(int skuId) {
        this.skuId = skuId;
    }

    @Column(name = "ATTRIBUTE_ID", nullable = false)
    public int getAttributeId() {
        return this.attributeId;
    }

    public void setAttributeId(int attributeId) {
        this.attributeId = attributeId;
    }

    @Column(name = "ATTRIBUTE_VALUE_ID", nullable = false)
    public int getAttributeValueId() {
        return this.attributeValueId;
    }

    public void setAttributeValueId(int attributeValueId) {
        this.attributeValueId = attributeValueId;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 15)
    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

}
