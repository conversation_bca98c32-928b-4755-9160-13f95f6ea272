package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.AssetRecoveryDefinition;
import com.stpl.tech.scm.notification.email.template.AssetLostNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.ScmMissingPricesNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

public class ScmMissingPricesNotification  extends EmailNotification {

   private ScmMissingPricesNotificationTemplate template;
    private EnvType envType;
  ;
    private List<String> emails;

    public  ScmMissingPricesNotification(ScmMissingPricesNotificationTemplate template,EnvType envType,List<String> emails){
        this.template = template;
        this.envType = envType;
        this.emails = emails;
    }


    @Override
    public String[] getToEmails() {
        return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } : (String[])this.emails.toArray();
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {

        String subject ="SCM Missing Prices Notification";
        if (SCMUtil.isDev(envType)) {
            subject = "Dev " + subject;
        }

        return  subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
