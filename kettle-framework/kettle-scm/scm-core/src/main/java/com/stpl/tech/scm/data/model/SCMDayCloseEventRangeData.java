/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "DAY_CLOSE_EVENT_RANGE")
public class SCMDayCloseEventRangeData {

    private Integer dayCloseEventRangeId;
    private SCMDayCloseEventData eventId;
    private String type;
    private int startId;
    private int endId;
    private String stockType;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_RANGE_ID", unique = true, nullable = false)
    public Integer getDayCloseEventRangeId() {
        return dayCloseEventRangeId;
    }

    public void setDayCloseEventRangeId(Integer dayCloseEventRangeId) {
        this.dayCloseEventRangeId = dayCloseEventRangeId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "EVENT_ID", nullable = false)
    public SCMDayCloseEventData getEventId() {
        return eventId;
    }

    public void setEventId(SCMDayCloseEventData eventId) {
        this.eventId = eventId;
    }

    @Column(name = "TYPE", nullable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "START_ID", nullable = false)
    public int getStartId() {
        return startId;
    }

    public void setStartId(int startId) {
        this.startId = startId;
    }

    @Column(name = "END_ID", nullable = false)
    public int getEndId() {
        return endId;
    }

    public void setEndId(int endId) {
        this.endId = endId;
    }

    @Column(name = "STOCK_TYPE", nullable = false)
    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }
}
