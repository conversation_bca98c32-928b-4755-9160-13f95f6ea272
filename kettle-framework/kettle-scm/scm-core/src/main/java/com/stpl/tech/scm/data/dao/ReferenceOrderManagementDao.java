package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.ExceptionDateEntry;
import com.stpl.tech.scm.data.model.ForecastReportResponse;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.ReferenceOrderData;
import com.stpl.tech.scm.data.model.RegularOrderUnitBrandData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.domain.model.EstimationShortData;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 13-06-2016.
 */
public interface ReferenceOrderManagementDao extends SCMAbstractDao {

    public List<ReferenceOrderData> getReferenceOrders(Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer referenceOrderId);

    public List<RequestOrderData> getRequestOrderFromReferenceOrder(int referenceOrderId);

    public void  getEstimationData(int brandId, int unitId, Date targetDate, BigDecimal targetSale,BigDecimal dineInSale,BigDecimal deliverySale, String dates, Integer CategoryBuffer, Integer buffer, String productIds, String orderType);

    public List<Integer> getRequestIdsForEstimation(int unitId, List<Date> businessDate);

    public List<EstimationShortData> getEstimationQuantity(int unitId, String requestIds);

    public List<IdCodeName> getExpiryProduct();

    public List<ExceptionDateEntry> getExceptionDate(Date businessDate);

    public boolean getExceptionDateWithUnitId(Date businessDate,Integer unitId);

    public ExceptionDateEntry deleteEntry(ExceptionDateEntry entry);

    public BigDecimal getSalesPercentage(int unitId, int brandId, String businessDate ,String saleType);

    void cloneDayCloseDataForUnit(int newUnitId, int cloningUnitId, Date businessDate);

    int checkIfDataExistForRegularOrderingForCafe(int unitId);

    public List<RegularOrderUnitBrandData> getUnitOrderingSchedule(Integer unitId,Boolean isFunctional);

    public List<String> getFountain9Units();

    public SCMDayCloseEventData findUnsuccessfulOrdering(Integer unitId);

    public List<Object[]> getNewExpiryProducts(Integer unitId);

    public List<ForecastReportResponse> getForecastResponses(Date startDate, Date endDate);

    public List<RegularOrderUnitBrandData> getAllOrderingSchedules();

    public RegularOrderUnitBrandData getMaxCap(int unitId, int brandId);

    public List<RequestOrderData> getLastWeekAdhocOrders(List<Date> dates, Integer unitId);

    public Boolean checkFountain9Enabled(Integer unitId);

    public boolean checkForOrderingSchedule(Integer unitId);

    public void updateForecastData(Integer id, String refreshDate, Integer unitId);

    public List<RequestOrderData> checkForF9Orders(Integer requestingUnitId, Integer fulfilmentUnitId, Date fulfilmentDate);

    public List<FulfillmentUnitMappingData> getFulfilmentUnits(Integer requestingUnitId);

    public List<Integer> getUnitsWithSchedules();
}
