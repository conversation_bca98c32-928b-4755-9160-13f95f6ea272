package com.stpl.tech.scm.core.service;

import java.math.BigDecimal;
import java.util.List;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.data.model.AdditionalDocumentsMaster;
import com.stpl.tech.scm.domain.model.CostElement;
import com.stpl.tech.scm.domain.model.CostElementPriceUpdate;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;

public interface ServiceMappingManagementService {

	List<CostElement> getCostElementData();

	List<IdCodeNameStatus> searchCostELementVendorMappings(Integer costElementId);

	boolean addCostELementVendorMapping(int employeeId, String employeeName, int id, List<Integer> mappingIds);

	List<IdCodeNameStatus> allVendors();

	boolean updateCostElementVendorMapping(int employeeId, String employeeName, int vendorId, int costElementId, String status);

	boolean addVendorCostElementMapping(int employeeId, String employeeName, int id, List<Integer> mappingIds);

	List<IdCodeNameStatus> searchVendorToCostElementMappings(Integer vendorId);

	List<IdCodeNameStatus> allCostCentre();

	boolean addCostElementCostCenterMapping(int employeeId, String employeeName, int id, List<Integer> mappingIds);

	List<IdCodeNameStatus> searchCostELementCostCenterMappings(Integer costElementId);

	boolean updateCostElementCostCenterMapping(int employeeId, String employeeName, int vendorId, int costCenterId,
			String status);

	boolean addCostCenterToCostElementMapping(int employeeId, String employeeName, int id, List<Integer> mappingIds);

	List<IdCodeNameStatus> searchCostCenterToCostElementMappings(Integer costCenterId);

	List<CostElementPriceUpdate> getPricedCostElements(Integer vendorId, Integer costCenterId);

	boolean updateStatusCostElementPriceMapping(int employeeId, String employeeName, int costElementId, String status);

	boolean addCostElementPriceMapping(Integer vendorId, Integer costCenterId,
			Integer costElementId, BigDecimal price, Integer employeeId, String employeeName);

	boolean updateCostElementPriceMapping(Integer costElementMappingId, BigDecimal price, Integer employeeId,
			String employeeName);

	List<CostElementPriceUpdate> getClonePriceForCostElementId(String costElementIds, Integer vendorId,Integer costCenterId);

    boolean addMasterDocument(String documentName,Integer createdBy) throws DataUpdationException;

	List<IdCodeName> searchCostElementDocumentMappings(Integer costElementId);

	boolean addCostElementDocumentMapping(int employeeId, String employeeName, int id, List<Integer> mappingIds);

	List<AdditionalDocumentsMaster> getDocumentList();

	boolean updateCostElementDocumentMapping(int documentId, int costElementId);

	List<AdditionalDocumentsMaster> getAdditionalDocs();

}
