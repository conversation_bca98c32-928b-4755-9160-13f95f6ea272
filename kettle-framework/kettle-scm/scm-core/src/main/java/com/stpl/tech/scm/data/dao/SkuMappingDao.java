package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.data.model.PageRequestDetail;
import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.data.model.PurchaseProfile;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.data.model.SkuPriceHistory;
import com.stpl.tech.scm.data.model.UnitSkuMapping;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.data.model.VendorContractItem;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.PriceStatus;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.VendorContract;
import com.stpl.tech.scm.domain.model.VendorContractVO;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by Mohit
 */
public interface SkuMappingDao extends SCMAbstractDao {

	public boolean updatePrices(SkuPriceUpdate data) throws SumoException;

	public List<SkuPriceDetail> searchPricesBySku(int skuId, String deliveryLocation);

	public List<IdCodeNameStatus> searchSkuMappingsForUnit(int unitId);
	public List<UnitSkuVendorMapping> searchActiveVendorMappingsForUnit(int unitId);

	public List<IdCodeNameStatus> searchUnitMappingsForSku(int skuId);

	public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds);

    public List<UnitSkuMapping> getUnitSkuMappingForUnitId(int unitId,List<Integer> skuIds);
	public List<UnitSkuMapping> getUnitSkuMappingForSKUS(int skuId,List<Integer> unitId);

	public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds);

	public List<IdCodeNameStatus> searchSkuMappingsForVendor(int vendorId);

	public List<UnitVendorSkuMapping> searchSkuMappingsForVendorAndUnit(int unitId, int vendorId);

	public List<Integer> searchSkuMappingIdsForVendorAndUnit(int unitId, int vendorId);

	public List<UnitVendorSkuMapping> updateSkuMappingsForVendorAndUnit(UpdateUnitVendorSkuMapping data);

	public List<unitSkuMappingDetail> getSkusProfileForUnit(int unit, List<Integer> sku);

	public List<IdCodeNameStatus> searchVendorMappingsForSku(int skuId);

	public List<IdCodeNameStatus> searchVendorMappingsForBusiness(int businessId);

	public boolean updateVendorSkuMapping(int employeeId, String name, int vendorId, int skuId, String status);

	public List<IdCodeNameStatus> allActiveUnits();

	public List<IdCodeNameStatus> allActiveSKU();

	public List<IdCodeNameStatus> allActiveVendors();

	public boolean updateSkuProfiles(Map<Integer,String> skuListWithInventoryListId, int unitId, String profile,Map<String,Integer> inventoryListName);

	/**
	 * @param employeeId
	 * @param employeeName
	 * @param vendorId
	 * @param skuIds
	 * @return
	 */
	public boolean addSkuMappingsForVendor(int employeeId, String employeeName, int vendorId, List<IdCodeName> skuIds);

	/**
	 * @param employeeId
	 * @param employeeName
	 * @param skuId
	 * @param vendorIds
	 * @return
	 */
	public boolean addVendorMappingsForSku(int employeeId, String employeeName, int skuId, List<Integer> vendorIds);


	public boolean addVendorMappingsForBusiness(int employeeId, String employeeName, int businessId, List<Integer> vendorIds);

	/**
	 * @param vendorId
	 * @param dispatchLocation
	 * @return
	 */


	public List<SkuPriceDetail> searchPricesByVendor(int vendorId, String dispatchLocation);

	/**
	 * @param skuIds
	 * @param dispatchLocation
	 * @param locationCode
	 * @return
	 */
	public List<SkuPriceDetail> searchPricesByDispatchLocation(List<Integer> skuIds, String dispatchLocation, String locationCode);


	/**
	 * @param data
	 * @return
	 */
	public boolean cancelPriceUpdate(SkuPriceUpdate data);

	/**
	 * @param data
	 * @return
	 */
	public boolean updatePriceStatus(SkuPriceUpdate data);

	public boolean addPrice(SkuPriceUpdate data);

	public void updateSkuPricesFromCurrentDay(SkuPriceUpdate data);

	public List<SkuPriceDetail> searchSkuPricesForVendorAndUnit(int unitId, int vendorId, String dispatchLocation, String deliveryLocation);

	public List<SkuPriceDetail> getSkuPackagingPriceForVendorsForUnit(int unitId, Set<Integer> vendorIds, String deliveryLocation);

	public List<SkuPriceDetail> searchPricesByVendorDeliveryLocation(int vendorId, String deliveryLocation, boolean isUpdated, boolean setUpdated);

    public List<UnitSkuMapping> searchSkuMappingsForUnitAndVendor(int unitId, int vendorId);

	public List<SkuPriceDetail> getSkuPackagingPriceForUnit(int unitId, String deliveryLocation);

    public List<PurchaseProfile> getPurchaseMappings(List<Integer> profiles);

	public List<String> getDistanceOfUnits(int firstUnitId, int secondUnitId);

	public boolean updateUnitDistanceMappingData(int firstUnitId, Integer firstMappingId, BigDecimal firstDistance,
			int secondUnitId, Integer secondMappingId, BigDecimal secondDistance,boolean saveZipDis) throws SumoException;


	void saveZipCodeDistance(int firstUnitId, int secondUnitId, BigDecimal firstDistance, BigDecimal secondDistance) throws SumoException;

	public int getVendorSkuMappingId(int skuId,int vendorId);

	public boolean updateSkuLeadTime(int vendorId,int leadTime);

    public Integer findProductionLine(int unitId, int skuId);

	public int findSKUID(int productId);

	public ProductionUnitData findProductionLine(int productIonUnitId);

    public List<SkuDefinitionData> findAllSkuDefinition(int productId);

	public List<UnitSkuMapping> getActiveUnitSkuMappings(List<Integer> skuIds ,List<Integer> unitIds);

	public UnitSkuMapping findSkuMappingBySkuAndUnit(Integer skuId,Integer unitId );

	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMappingByStatus(Integer skuId, Integer packagingId , List<String> statuses);

	public Boolean updateUnitSkuPackagingTaxMapping(Integer employeeId, Integer skuId,Integer packagingId  , Map<Integer,String> unitTaxMap);

	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMappingByUnit(List<Integer> skuIds,Integer unitId);


	void processPriceRequestForVendor(SkuPriceUpdate val, PriceStatus key, PriceStatus currentStatus, Set<Integer> processedId) throws SumoException;

    List<SkuPriceDetail> vendorPriceChangeAsPerStatus(Integer vendorId,List<String> status);

	List<SkuPriceHistory>  saveVendorPriceChange(VendorContract vendorContract);

	List<VendorContractVO> getVendorContract(Integer vendorId, PriceStatus status, Date startDate, Date endDate, Integer vendorContractId);

	boolean cancelVendorContract(VendorContract vendorContractId) throws SumoException;

	void applyContractOnSKU(VendorContract vendorContract, IdCodeName templateDetail) throws SumoException;

	void applyContract();

	List<VendorContractItem> findAllVendorContract(Integer vendorContractId);

	PageRequestDetail findByToken(String token) throws VendorRegistrationException;

	VendorContractVO getVendorContractVoByContractId(Integer eventId) throws SumoException;

	void expiryContract();

	PageRequestDetail findPageRequestByEventTypeAndRecordStatus(String vendorContractEmployee, Integer vendorContractId);

    void cancelAllContractMailRequest(Integer vendorContractId, String eventType);

	List<VendorContractInfo> closePreviousContract(int vendorId);

    void checkPendingVendorContract(int vendorId) throws SumoException;
}
