package com.stpl.tech.scm.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import com.stpl.tech.scm.data.dao.SkuMappingDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.EWayHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.TransportManagementDao;
import com.stpl.tech.scm.data.model.ConsignmentData;
import com.stpl.tech.scm.data.model.EWayStatusUpdateEvent;
import com.stpl.tech.scm.data.model.EwayBillData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.VehicleData;
import com.stpl.tech.scm.data.model.VehicleDispatchData;
import com.stpl.tech.scm.domain.model.ConsignmentType;
import com.stpl.tech.scm.domain.model.DispatchStatus;
import com.stpl.tech.scm.domain.model.EWayBillStatus;
import com.stpl.tech.scm.domain.model.EWayResponse;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VehicleDispatch;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

/**
 * <AUTHOR>
 *
 */
@Repository
public class TransportManagementDaoImpl extends SCMAbstractDaoImpl implements TransportManagementDao {

	private static final Logger LOG = LoggerFactory.getLogger(TransportManagementDaoImpl.class);

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private SkuMappingDao skuMappingDao;

	@SuppressWarnings("unchecked")
	@Override
	public List<Integer> getTransferOrdersForEway(List<Integer> unitIds, Date startDate, Date endDate, List<Integer> receivingUnitIds) {
		List<String> toStatusList = new ArrayList<>();
		toStatusList.add(SCMOrderStatus.CANCELLED.name());
		toStatusList.add(SCMOrderStatus.SETTLED.name());
		StringBuilder queryString = new StringBuilder("SELECT T.id FROM TransferOrderData T WHERE T.status NOT IN :statusList " +
				" AND T.generationUnitId IN :unitIds AND T.generationTime >= :startDate AND T.generationTime <= :endDate");
		if(receivingUnitIds.size() > 0){
			queryString.append("  AND T.generatedForUnitId IN :receivingUnitIds");
		}
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("statusList", toStatusList);
		query.setParameter("unitIds", unitIds);
		if(receivingUnitIds.size()>0){
			query.setParameter("receivingUnitIds", receivingUnitIds);
		}
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", SCMUtil.getNextDate(endDate));
		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Integer> getProcessedTransferOrders(List<Integer> availableTO) {
		if (availableTO == null || availableTO.isEmpty()) {
			return new ArrayList<>();
		}
		List<String> eWayStatusList = new ArrayList<>();
		eWayStatusList.add(EWayBillStatus.CANCELLED.name());
		Query query = manager
				.createQuery(" SELECT T.transferOrder.id FROM EwayBillData T WHERE T.status NOT IN :statusList "
						+ " AND T.transferOrder.id IN :toList");
		query.setParameter("toList", availableTO);
		query.setParameter("statusList", eWayStatusList);
		return query.getResultList();
	}

	@Override
	public List<TransferOrder> findAllTransferOrders(List<Integer> availableTO) {
		List<TransferOrder> list = new ArrayList<>();
		List<TransferOrderData> result = findAllTransferOrdersData(availableTO);
		for (TransferOrderData to : result) {
			list.add(SCMDataConverter.convert(to, false, scmCache, masterDataCache));
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<TransferOrderData> findAllTransferOrdersData(List<Integer> availableTO) {
		if (availableTO == null || availableTO.isEmpty()) {
			return new ArrayList<>();
		}
		Query query = manager.createQuery("FROM TransferOrderData T WHERE T.id IN :availableTO ");
		query.setParameter("availableTO", availableTO);
		return query.getResultList();
	}

	@Override
	public VehicleDispatchData findDispatchData(Integer vehicleId, Date d) {
		try {
			Query query = manager.createQuery(
					"FROM VehicleDispatchData T WHERE T.vehicle.vehicleId = :vehicleId AND T.dispatchDate = :dispatchDate");
			query.setParameter("vehicleId", vehicleId);
			query.setParameter("dispatchDate", SCMUtil.getDate(d));
			return (VehicleDispatchData) query.getSingleResult();
		} catch (NoResultException nre) {
			return null;
		}
	}

	@Override
	public boolean createConsignment(Integer dispatchId, List<Integer> toList) throws SumoException {

		long startTime = System.currentTimeMillis();
		VehicleDispatchData dispatch = find(VehicleDispatchData.class, dispatchId);

		Map<Integer, List<TransferOrderData>> unitToTransferOrderMap = EWayHelper
				.createUnitToTransferOrderMap(findAllTransferOrdersData(toList));

		Map<Integer, ConsignmentData> consignmentMap = EWayHelper.createConsignementMap(dispatch.getConsignmentList());

		LOG.info("----------- STEP 1 Dispatch data fetched and consignment created, ----------- ,{} milliseconds ",
				System.currentTimeMillis() - startTime);
		startTime = System.currentTimeMillis();
		for (Integer unitId : unitToTransferOrderMap.keySet()) {
			ConsignmentData cd = consignmentMap.get(unitId);
			if (cd == null) {
				cd = new ConsignmentData();
				cd.setDispatchId(dispatch.getDispatchId());
				cd.setStatus(DispatchStatus.CREATED.name());
				cd.setConsignmentUnitId(unitId);
				UnitDetail unit = scmCache.getUnitDetail(unitId);
				if (SCMUtil.isWareHouseOrKitchen(unit)) {
					cd.setConsignmentType(ConsignmentType.TRANSFER.name());
				} else {
					cd.setConsignmentType(ConsignmentType.RETURN.name());
				}
				cd = add(cd, false);
			}
			cd.getEwaybills()
					.addAll(createEWayBills(dispatch.getVehicle(), unitToTransferOrderMap.get(unitId), cd, 10000));

			LOG.info("----------- consignment created, ----------- ,{} milliseconds ",
					System.currentTimeMillis() - startTime);
			startTime = System.currentTimeMillis();
		}
		return true;
	}

	public List<EwayBillData> createEWayBills(VehicleData vehicle, List<TransferOrderData> transferOrders,
			ConsignmentData cd, Integer empId) throws SumoException {
		List<EwayBillData> events = new ArrayList<>();
		String status = EWayBillStatus.CREATED.name();
		for (TransferOrderData to : transferOrders) {
			events.add(createEWayBill(vehicle, cd, status, empId, to));
		}
		manager.flush();
		return events;
	}

	private EwayBillData createEWayBill(VehicleData vehicle, ConsignmentData cd, String status, Integer createdBy,
			TransferOrderData transferOrder) throws SumoException {
		EwayBillData event = new EwayBillData();
		event.setDistance(BigDecimal.ZERO);
		event.setEwayBillNumber(null);
		event.setStatus(status);
		event.setVehicle(vehicle);
		event.setTransferOrder(transferOrder);
		event.setConsignmentId(cd.getConsigmentId());
		int srcUnit = transferOrder.getGenerationUnitId();
		int desUnit  = transferOrder.getGeneratedForUnitId();
		String srcZipCode = masterDataCache.getUnit(srcUnit).getAddress().getZipCode();
		String desZipCode = masterDataCache.getUnit(desUnit).getAddress().getZipCode();
		BigDecimal distance = scmCache.getZipCodeDistanceMapping(srcZipCode, desZipCode);
		if (distance == null) {
			String title = "distance mapping not Found";
			String msg = srcUnit + "," +   desUnit ;
			throw new SumoException(title,msg);
		}
        BigDecimal dis = scmCache.getUnitDistanceMapping(srcUnit,desUnit);
		if(dis==null || dis.equals(BigDecimal.ZERO)){
			skuMappingDao.updateUnitDistanceMappingData(srcUnit,null,distance,desUnit,null,distance,false);
		}
		event.setDistance(distance);
		event.setEwayRequired(AppConstants.NO);
		event = add(event, false);
		createEWayStatusUpdateEvent(event, status, createdBy);
		return event;
	}

	private void createEWayStatusUpdateEvent(EwayBillData event, String status, Integer createdBy) throws SumoException {
		EWayStatusUpdateEvent eventUpdate = new EWayStatusUpdateEvent();
		eventUpdate.setCreatedAt(SCMUtil.getCurrentTimestamp());
		eventUpdate.setStatus(status);
		eventUpdate.setCreatedBy(createdBy);
		eventUpdate.setEventId(event.getId());
		add(eventUpdate, false);
		event.getEventStatusList().add(eventUpdate);
	}

	@Override
	public void updateEwayData(List<EWayResponse> entityList) {
		for (EWayResponse response : entityList) {
			EwayBillData data = findEWaybyTransferOrder(response.getToId(), EWayBillStatus.PREPARED);
			if (data != null && !AppUtils.isBlank(response.getEwbNo())) {
				data.setEwayBillNumber(response.getEwbNo());
				data.setStatus(EWayBillStatus.UPLOADED.name());
			}
		}
		flush();
	}

	private EwayBillData findEWaybyTransferOrder(int toId, EWayBillStatus status) {
		try {
			Query query = manager
					.createQuery("FROM EwayBillData T WHERE T.transferOrder.id = :toId AND T.status = :status");
			query.setParameter("toId", toId);
			query.setParameter("status", status.name());
			return (EwayBillData) query.getSingleResult();
		} catch (NoResultException nre) {
			return null;
		}
	}

	@Override
	public void updateEWayStatus(List<EwayBillData> events, EWayBillStatus status) {
		if (events == null || events.isEmpty()) {
			return;
		}
		List<Integer> list = new ArrayList<>();
		for (EwayBillData e : events) {
			list.add(e.getId());
		}
		Query query = manager.createQuery("UPDATE EwayBillData T SET T.status = :status WHERE T.id IN :list");
		query.setParameter("list", list);
		query.setParameter("status", status.name());
		query.executeUpdate();
	}

	@Override
	public void checkAndMarkCompleteDispatch(List<EWayResponse> list) {

		Integer dispatchId = null;
		for (EWayResponse e : list) {
			EwayBillData data = findEWaybyTransferOrder(e.getToId(), EWayBillStatus.UPLOADED);
			if (data != null) {
				ConsignmentData con = find(ConsignmentData.class, data.getConsignmentId());
				dispatchId = con.getDispatchId();
				break;
			}
		}
		if (dispatchId != null && dispatchId > 0) {
			checkandMarkDispatchAsCompleted(dispatchId);
		}
	}

	private void checkandMarkDispatchAsCompleted(Integer dispatchId) {
		List<Integer> list = new ArrayList<>();
		VehicleDispatchData dispatch = find(VehicleDispatchData.class, dispatchId);
		for (ConsignmentData cd : dispatch.getConsignmentList()) {
			for (EwayBillData eway : cd.getEwaybills()) {
				if (AppUtils.getStatus(eway.getEwayRequired())
						&& !EWayBillStatus.UPLOADED.name().equals(eway.getStatus())) {
					list.add(eway.getId());
					break;
				}
			}
		}
		if (list.isEmpty()) {
			for (ConsignmentData cd : dispatch.getConsignmentList()) {
				cd.setStatus(DispatchStatus.SETTLED.name());
			}
			dispatch.setStatus(DispatchStatus.SETTLED.name());
			flush();
		}
	}

	@Override
	public VehicleDispatch getDispatchData(Integer dispatchId) {
		VehicleDispatchData dispatch = find(VehicleDispatchData.class, dispatchId);
		return SCMDataConverter.convert(dispatch, scmCache, masterDataCache, true);
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<VehicleDispatchData> getVehicleDispatchHistory(Integer vehicleId, Date startDate, Date endDate,
			String registrationNumber) {
		String str = "FROM VehicleDispatchData T WHERE T.vehicle.vehicleId = :vehicleId AND T.dispatchDate >= :startDate AND  T.dispatchDate <= :endDate";
		if (registrationNumber != null) {
			str = str + " and vehicleNumber = :registrationNumber";
		}
		Query query = manager.createQuery(str);
		query.setParameter("vehicleId", vehicleId);
		query.setParameter("startDate", SCMUtil.getDate(startDate));
		query.setParameter("endDate", SCMUtil.getNextDate(endDate));
		if (registrationNumber != null) {
			query.setParameter("registrationNumber", registrationNumber);
		}
		return query.getResultList();
	}

	@Override
	public void updateConsignmentStatus(Integer dispatchId, DispatchStatus status) {
		Query query = manager
				.createQuery("UPDATE ConsignmentData T SET T.status = :status WHERE T.dispatchId = :dispatchId");
		query.setParameter("dispatchId", dispatchId);
		query.setParameter("status", status.name());
		query.executeUpdate();
	}

	@Override
	public boolean cancelConsignment(Integer consignmentId) throws SumoException {
		ConsignmentData cd = find(ConsignmentData.class, consignmentId);
		if (cd != null && DispatchStatus.CREATED.name().equals(cd.getStatus())) {
			for (EwayBillData ebd : cd.getEwaybills()) {
				if (EWayBillStatus.CREATED.name().equals(ebd.getStatus())) {
					delete(ebd);
				} else {
					throw new SumoException("EWay Bill" + ebd.getId() + " is in " + ebd.getStatus() + " status");
				}
			}
			delete(cd);
			return true;
		}
		return false;
	}

	@Override
	public boolean cancelEWay(Integer ewayId) {
		EwayBillData eway = find(EwayBillData.class, ewayId);
		if (eway != null && EWayBillStatus.CREATED.name().equals(eway.getStatus())) {
			ConsignmentData cd = find(ConsignmentData.class, eway.getConsignmentId());
			if (cd != null && DispatchStatus.CREATED.name().equals(cd.getStatus())) {
				VehicleDispatchData vdd = find(VehicleDispatchData.class, cd.getDispatchId());
				if (vdd != null && DispatchStatus.CREATED.name().equals(vdd.getStatus())) {
					delete(eway);
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public List<Integer> findVehicleDataByDates(Date date) {
		try {
			Query query = manager.createQuery("SELECT v.vehicle.vehicleId FROM VehicleDispatchData v WHERE v.dispatchDate >= :date");
			query.setParameter("date",date);
			return (List<Integer>) query.getResultList();
		}
		catch (Exception e) {
			LOG.error("Error Occurred while fetching vehicle dispatch data from database  ::: ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<Integer> getToListOfVehicle(Integer vehicleId, Date date) {
		try {
			Query query = manager.createNativeQuery("SELECT E.TRANSFER_ORDER_ID FROM VEHICLE_DISPATCH_DATA VDD INNER JOIN CONSIGNMENT_DATA CD " +
								"ON VDD.DISPATCH_ID = CD.DISPATCH_ID INNER JOIN EWAY_BILL_DATA E ON CD.CONSIGNMENT_ID=E.CONSIGNMENT_ID " +
								"WHERE VDD.VEHICLE_ID =:vehicleId AND VDD.DISPATCH_DATE  >= :date");
			query.setParameter("vehicleId",vehicleId);
			query.setParameter("date", date);
			return (List<Integer>) query.getResultList();
		}
		catch (Exception e) {
			LOG.error("Error Occurred while fetching vehicle dispatch data from database  ::: ",e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<TransferOrderData> getTODataByIds(List<Integer> toIds) {
		try {
			Query query = manager.createQuery("FROM TransferOrderData t WHERE t.id IN(:toIds)");
			query.setParameter("toIds",toIds);
			return (List<TransferOrderData>) query.getResultList();
		}
		catch (Exception e) {
			LOG.error("Error Occurred while fetching vehicle dispatch data from database  ::: ",e);
			return new ArrayList<>();
		}
	}

}
