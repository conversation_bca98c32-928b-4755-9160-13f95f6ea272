package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.POExpiredEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class POExpiredEmailNotification extends EmailNotification {
    private POExpiredEmailNotificationTemplate poExpiredEmailNotificationTemplate;
    private EnvType envType;
    private String[] emails;

    public POExpiredEmailNotification() {

    }

    public POExpiredEmailNotification(POExpiredEmailNotificationTemplate poExpiredEmailNotificationTemplate,
                                           EnvType envType, String[] emails) {
        this.poExpiredEmailNotificationTemplate = poExpiredEmailNotificationTemplate;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        List<String> mails = new ArrayList<>();
        Arrays.asList(emails).forEach(email -> {
            mails.add(email);
        });
        String[] simpleArray = new String[mails.size()];
        return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } : mails.toArray(simpleArray);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject;
        subject = "Purchase Order Receipt Number # "+poExpiredEmailNotificationTemplate.getPurchaseOrderData().getReceiptNumber()+" will Expired on "
                + SCMUtil.getFormattedTime(poExpiredEmailNotificationTemplate.getPurchaseOrderData().getExpiryDate(), "EEE dd MMM yyyy");
        if (SCMUtil.isDev(envType)) {
            subject = "Dev " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return poExpiredEmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
