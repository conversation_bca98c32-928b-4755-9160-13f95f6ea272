package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "SKU_PACKAGING_TAX_MAPPING")
public class SkuPackagingTaxMapping {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "SKU_PACKAGING_TAX_MAPPING_ID")
    private Integer skuPackagingTaxMappingId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "SKU_ID")
    private Integer skuId;

    @Column(name = "PACKAGING_ID")
    private Integer packagingId;

    @Column(name = "MAPPING_STATUS")
    private String mappingStatus;

    @Column(name = "TAX_CODE")
    private String taxCode;

    @Column(name = "CREATED_AT")
    private Date createdAt;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "UPDATED_AT")
    private Date updatedAt;

    @Column(name = "UPDATED_BY")
    private Integer updatedBy;

    public Integer getSkuPackagingTaxMappingId() {
        return this.skuPackagingTaxMappingId;
    }

    public void setSkuPackagingTaxMappingId(Integer skuPackagingTaxMappingId) {
        this.skuPackagingTaxMappingId = skuPackagingTaxMappingId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getSkuId() {
        return this.skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public Integer getPackagingId() {
        return this.packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

    public String getTaxCode() {
        return this.taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public Date getCreatedAt() {
        return this.createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Integer getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getUpdatedBy() {
        return this.updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }
}
