package com.stpl.tech.scm.core.util.model;

import com.stpl.tech.scm.data.model.CostDetailDataWh;

import java.util.ArrayList;
import java.util.List;

public class CostDataEntriesWh extends CostDataEntries{
    private List<CostDetailDataWh> data;
    private List<CostDetailDataWh> errors;

    public List<CostDetailDataWh> getData() {
        if (data == null) {
            data = new ArrayList<>();
        }
        return data;
    }

    public void setData(List<CostDetailDataWh> data) {
        this.data = data;
    }

    public List<CostDetailDataWh> getErrors() {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        return errors;
    }

    public void setErrors(List<CostDetailDataWh> errors) {
        this.errors = errors;
    }
}
