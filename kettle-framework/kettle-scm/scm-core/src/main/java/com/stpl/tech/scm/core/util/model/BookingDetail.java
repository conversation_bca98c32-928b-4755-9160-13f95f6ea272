package com.stpl.tech.scm.core.util.model;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.scm.data.model.BookingConsumptionData;
import com.stpl.tech.scm.domain.model.ProductionBooking;

public class BookingDetail {

	private ProductionBooking booking;
	private List<BookingConsumptionData> list = new ArrayList<BookingConsumptionData>();

	public ProductionBooking getBooking() {
		return booking;
	}

	public void setBooking(ProductionBooking booking) {
		this.booking = booking;
	}

	public List<BookingConsumptionData> getList() {
		return list;
	}

	public void setList(List<BookingConsumptionData> list) {
		this.list = list;
	}

}
