package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.data.model.VendorDetailChangeRequest;
import com.stpl.tech.scm.domain.model.SkuPriceDataObject;
import com.stpl.tech.scm.domain.model.SkuPriceHistoryObject;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.IdCodeNameType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.UnitVendorMapping;
import com.stpl.tech.scm.domain.model.VendorAccountDetail;
import com.stpl.tech.scm.domain.model.VendorBlockResponse;
import com.stpl.tech.scm.domain.model.VendorCompanyDetail;
import com.stpl.tech.scm.domain.model.VendorDebitBalanceVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorEditVO;
import com.stpl.tech.scm.domain.model.VendorRegistrationRequest;
import com.stpl.tech.scm.core.util.model.VendorDetailRequestChanges;
import com.stpl.tech.scm.domain.model.VendorEditedType;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.Map;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
public interface SCMVendorManagementService {

    public VendorDetail viewVendor(int vendorId);

    public Collection<IdCodeNameType> getAllVendorName();

    public List<VendorDetail> viewAllVendors();

    public VendorDetail addVendor(VendorDetail vendorDetail) throws SumoException;

    public VendorDetail updateVendor(VendorDetail vendorDetail);

    public boolean deactivateVendor(int vendorId);

    public boolean activateVendor(int vendorId);

    public UnitVendorMapping unitVendorAdd(UnitVendorMapping mapping) throws SumoException;

    public boolean unitVendorActivate(int mappingId);

    public boolean unitVendorDeactivate(int mappingId);

    public List<UnitVendorMapping> getUnitVendors(int unitId);

	public boolean addVendorRegistrationRequest(VendorRegistrationRequest request) throws SumoException;

	public List<VendorRegistrationRequest> getAllVendorRegistrationRequests(Date statDate, Date endDate, boolean getPendingOnly);

	public List<VendorRegistrationRequest> getAllVendorRegistrationRequests(List<String> status);

	public boolean cancelVendorRegistrationRequest(Integer id);

    public VendorRegistrationRequest validateRequest(String id) throws VendorRegistrationException, UnsupportedEncodingException;

    public VendorDetail saveBasicDetails(VendorDetail detail) throws VendorRegistrationException;

    public String getRegistrationAuthToken();

    public VendorDetail getVendor(Integer vendorId);

    public DocumentDetail uploadDocument(FileType type,MimeType mimeType, DocUploadType docType, Integer vendorId, MultipartFile file);
    public DocumentDetail uploadTdsDocument(FileType type,MimeType mimeType, DocUploadType docType, Integer vendorId, MultipartFile file,Boolean tdsStatus);

    public VendorDetail saveCompanyDetails(VendorCompanyDetail detail) throws SumoException;

    public VendorDetail saveAccountDetails(VendorAccountDetail detail) throws SumoException;

    public VendorDetail saveDispatchLocations(Set<VendorDispatchLocation> detailList, Set<Integer> removedLocations, Integer vendorId) throws VendorRegistrationException, SumoException;

    public boolean approveVendor(int creditCycle, int requestId, int userId, int leadTime);

	/**
	 * @param requestStatus
	 * @return
	 */
	public List<VendorDetail> getAllVendor(String requestStatus, boolean getAccount);

    public List<VendorDetail> getAllShortVendor(String requestStatus, boolean getAccount);


    public VendorDetail getShortVendor(Integer vendorId);


	/**
	 * @param vendorId
	 * @param userId
	 * @return
	 */
	public boolean editVendorRequest(int vendorId, int userId) throws SumoException;

	public boolean unitVendorUpdate(UnitVendorMapping mapping);

    public List<VendorDetail> getAllVendors();

    public boolean blockVendorPayments(VendorEditVO request) throws SumoException;

    public boolean unBlockVendorPayments(VendorEditVO request) throws SumoException;

    public List<VendorDebitBalanceVO> uploadDebitBalanceSheet(MultipartFile file) throws IOException, SumoException;

    public boolean uploadTDSMailingSheet(
            MimeType mimeType, String emails, String financialYear, String vendorName, String panNumber, String quarter,
            MultipartFile file) throws IOException, SumoException;

    public boolean saveDebitBalances(List<VendorDebitBalanceVO> vendorDebitBalanceVOS) throws SumoException;

    public List<VendorDetail> getAllVendor(String requestStatus, boolean getAccount, Integer companyId);

    public Collection<IdCodeNameType> viewAllVendorsTrimmed();

	public Collection<IdCodeName> getServiceVendors();

	public Collection<IdCodeNameStatus> getVendorLocations(int vendorId);

	public boolean updateCreditCycle(Integer companyId, Integer creditCycle);

	public boolean checkDuplicatePanCheck(String vendorType, String vendorPan);

	public String checkVendorDeactivateStatus(Integer vendorId);

	public boolean reactivateVendor(Integer vendorId);

	public boolean saveLeadTime(int vendorId,int leadTime);

    public void downloadFile(HttpServletResponse response, DocumentDetail document) throws IOException;

    public DocumentDetail getDocumentDetailById(Integer documentId);

    public boolean checkDuplicateVendorName(String vendorName,String vendorType, String city , String state);

    public boolean checkForApprovedVendorAtleastOnce(Integer vendorId);

    public List<SkuPriceHistoryObject> getPriceHistory(SkuPriceDataObject reqObj);

    public Boolean updateVendorUnblockTillDate(Integer vendorId, String unblockTillDate, Integer updatedBy);

    VendorBlockResponse getPendingOrdersToBlockVendor(Integer vendorId);

    Boolean blockUnblockVendor(Integer vendorId, Integer updatedBy, Boolean isBlock);

    public boolean setVendorEditRequest(Integer userId,Integer vendorId, Object vendorDetailChangeRequest) throws Exception;

    void convertEditRequest(VendorDetailChangeRequest vendorDetailChangeRequest, Map.Entry<String, Object> entry, VendorEditedType tableName, Integer vendorId) throws Exception;

    public List<VendorDetailRequestChanges> getRequestedChanges(Integer vendorId) throws Exception;

    boolean checkVendorCompliance(List<Integer> vendorIds, List<String> complianceTypes, Boolean isForceRetry);
    public List<VendorDetail> getAllVendorFromCache(String requestStatus, boolean getAccount);

}
