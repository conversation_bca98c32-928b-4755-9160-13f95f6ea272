package com.stpl.tech.scm.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.IdGeneratorServiceScm;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.SCMVendorManagementService;
import com.stpl.tech.scm.core.service.SkuMappingService;
import com.stpl.tech.scm.core.util.MultiPartFileHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.VendorDetailRequestChanges;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.AuditChangeLogDao;
import com.stpl.tech.scm.data.dao.SCMVendorManagementDao;
import com.stpl.tech.scm.data.dao.ServiceReceiveManagementDao;
import com.stpl.tech.scm.data.dao.VendorComplianceDataAuditLogDao;
import com.stpl.tech.scm.data.dao.VendorRegistrationRequestDao;
import com.stpl.tech.scm.data.model.AddressDetailData;
import com.stpl.tech.scm.data.model.AgreementDetail;
import com.stpl.tech.scm.data.model.AgreementSignRecord;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.PaymentRequestData;
import com.stpl.tech.scm.data.model.PaymentsBlockUnBlockByComplianceLog;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderServiceReceiveMappingData;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.data.model.VendorAccountDetailData;
import com.stpl.tech.scm.data.model.VendorCompanyDebitMapping;
import com.stpl.tech.scm.data.model.VendorCompanyDetailData;
import com.stpl.tech.scm.data.model.VendorComplianceData;
import com.stpl.tech.scm.data.model.VendorDetailChangeRequest;
import com.stpl.tech.scm.data.model.VendorDetailChangeRequestData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorDispatchLocationDetailData;
import com.stpl.tech.scm.data.model.VendorEditedDetail;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.VendorLogData;
import com.stpl.tech.scm.data.model.VendorRegistrationRequestDetail;
import com.stpl.tech.scm.data.model.VendorTdsCertificateSentData;
import com.stpl.tech.scm.data.mongo.AuditChangeLog;
import com.stpl.tech.scm.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.scm.data.mongo.EFiledList;
import com.stpl.tech.scm.data.mongo.VendorComplianceDataAuditLog;
import com.stpl.tech.scm.domain.model.AddressDetail;
import com.stpl.tech.scm.domain.model.BusinessType;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.IdCodeNameType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SandBoxAuthResponse;
import com.stpl.tech.scm.domain.model.SkuPriceDataObject;
import com.stpl.tech.scm.domain.model.SkuPriceHistoryObject;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitVendorMapping;
import com.stpl.tech.scm.domain.model.VendorAccountDetail;
import com.stpl.tech.scm.domain.model.VendorBlockGrSr;
import com.stpl.tech.scm.domain.model.VendorBlockItem;
import com.stpl.tech.scm.domain.model.VendorBlockResponse;
import com.stpl.tech.scm.domain.model.VendorCompanyDetail;
import com.stpl.tech.scm.domain.model.VendorComplianceDataDTO;
import com.stpl.tech.scm.domain.model.VendorComplianceType;
import com.stpl.tech.scm.domain.model.VendorComplianceValidations;
import com.stpl.tech.scm.domain.model.VendorDebitBalanceVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorEditVO;
import com.stpl.tech.scm.domain.model.VendorEditedData;
import com.stpl.tech.scm.domain.model.VendorEditedFieldStatus;
import com.stpl.tech.scm.domain.model.VendorEditedFieldType;
import com.stpl.tech.scm.domain.model.VendorEditedType;
import com.stpl.tech.scm.domain.model.VendorRegistrationRequest;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.domain.model.VendorType;
import com.stpl.tech.scm.notification.email.VendorComplianceEmailNotification;
import com.stpl.tech.scm.notification.email.VendorRegistrationCompleteNotification;
import com.stpl.tech.scm.notification.email.VendorRegistrationRequestNotification;
import com.stpl.tech.scm.notification.email.template.VendorComplianceEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorRegistrationCompleteNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorRegistrationRequestNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Chaayos on 07-05-2016.
 */

@Service
public class SCMVendorManagementServiceImpl implements SCMVendorManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(SCMVendorManagementServiceImpl.class);

    @Autowired
    private SCMVendorManagementDao vendorManagementDao;

    @Autowired
    private VendorRegistrationRequestDao registrationDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private EnvProperties props;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private SkuMappingService mappingService;

    @Autowired
    private PaymentRequestManagementService paymentRequestManagementService;

    @Autowired
    private SCMMetadataService scmMetadataService;

    @Autowired
    private AuditChangeLogDao auditChangeLogDao;

    @Autowired
    private ServiceReceiveManagementDao serviceReceiveManagementDao;

    @Autowired
    private IdGeneratorServiceScm idService;

    @Autowired
    private VendorComplianceDataAuditLogDao vendorComplianceDataAuditLogDao;

    @Autowired
    private RequestOrderManagementService requestOrderManagementService;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public VendorDetail viewVendor(int vendorId) {
        VendorDetailData vendorDetail = vendorManagementDao.find(VendorDetailData.class, vendorId);
        List<VendorEditedDetail> vendorEditedDetail=registrationDao.findVendorEditedDetailByVendorId(vendorId);
        VendorEditedData vendorEditedData=SCMDataConverter.convertVendorEditedDetailtoDomainData(vendorEditedDetail);
        VendorDetail vendorDetail1= SCMDataConverter.convertVendor(vendorDetail, masterDataCache.getEmployee(vendorDetail.getUpdatedBy()),
                masterDataCache.getEmployee(vendorDetail.getRequestedBy()));
          vendorDetail1.setVendorEditedData(vendorEditedData);
        return vendorDetail1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDetail> viewAllVendors() {
        List<VendorDetail> vendorDetails = new ArrayList<VendorDetail>(scmCache.getVendorDetails().values());
        return vendorDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<IdCodeNameType> viewAllVendorsTrimmed() {
        List<VendorDetail> vendorDetails = new ArrayList<VendorDetail>(scmCache.getVendorDetails().values());
        Map<Integer, IdCodeNameType> vendorDetailMap = new HashMap<>();
        for (VendorDetail detail : vendorDetails) {
			if (VendorStatus.ACTIVE.equals(detail.getStatus())) {
				vendorDetailMap.put(detail.getVendorId(), SCMUtil.generateIdCodeNameType(detail.getVendorId(), "",
						detail.getEntityName(), detail.getType().name(), detail.getStatus().name()));
			}
        }
        return vendorDetailMap.values();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<IdCodeNameType> getAllVendorName() {
        List<VendorStatus> statusList = Arrays.asList(VendorStatus.ACTIVE,VendorStatus.COMPLETED,
                VendorStatus.ACTIVE,VendorStatus.IN_ACTIVE,VendorStatus.IN_PROCESS);
        List<VendorDetail> vendorDetailData = scmCache.getVendorDetails().values().stream().filter(
                vendorDetail -> statusList.contains(vendorDetail.getStatus())).collect(Collectors.toList());
        Map<Integer, IdCodeNameType> vendorDetailMap = new HashMap<>();
        for (VendorDetail detail : vendorDetailData) {
                vendorDetailMap.put(detail.getVendorId(), SCMUtil.generateIdCodeNameType(detail.getVendorId(), "",
                    detail.getEntityName(), detail.getType().name(), detail.getStatus().name(), detail.getVendorBlocked(), detail.getBlockedReason(), detail.getUnblockedTillDate()));
        }
        return vendorDetailMap.values();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<IdCodeName> getServiceVendors() {
        List<VendorDetail> vendorDetails = new ArrayList<VendorDetail>(scmCache.getVendorDetails().values());
        Map<Integer, IdCodeName> vendorDetailMap = new HashMap<>();
        for (VendorDetail detail : vendorDetails) {
            VendorCompanyDetail companyDetail = detail.getCompanyDetails();
            if (companyDetail != null) {
                BusinessType businessType = companyDetail.getBusinessType();
                if (VendorStatus.ACTIVE.equals(detail.getStatus()) && !VendorType.CUSTOMER.equals(detail.getType())) {
                        /*&& (BusinessType.SERVICES.equals(businessType)
						|| BusinessType.GOODS_AND_SERVICES.equals(businessType))) {*/
                    vendorDetailMap.put(detail.getVendorId(),
                            SCMUtil.generateIdCodeName(detail.getVendorId(), businessType.name(), detail.getEntityName()));
                }
            }
        }
        return vendorDetailMap.values();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<IdCodeNameStatus> getVendorLocations(int vendorId) {
        VendorDetail vd = scmCache.getVendorDetail(vendorId);
        List<IdCodeNameStatus> list = new ArrayList<>();
        if (vd != null) {
            for (VendorDispatchLocation l : vd.getDispatchLocations()) {
                list.add(SCMUtil.generateIdCodeNameStatus(l.getDispatchId(), l.getState(), l.getCity(), l.getStatus().toString()));
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VendorDetail addVendor(VendorDetail vendorDetail) throws SumoException {
        VendorDetailData vendorDetailData = vendorManagementDao.add(SCMDataConverter.convert(vendorDetail, false),
                true);
        if (vendorDetailData != null) {
            String updatedBy = masterDataCache.getEmployee(vendorDetailData.getUpdatedBy());
            String requestedBy = masterDataCache.getEmployee(vendorDetailData.getRequestedBy());
            vendorDetail = SCMDataConverter.convertVendor(vendorDetailData, updatedBy, requestedBy);
            VendorLogData vendorLogData=new VendorLogData();
            updateVendorLogData(vendorLogData,vendorDetailData,updatedBy);

            scmCache.getVendorDetails().put(vendorDetailData.getVendorId(), vendorDetail);
            return vendorDetail;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VendorDetail updateVendor(VendorDetail vendorDetail) {
        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class,
                vendorDetail.getVendorId());
        if (vendorDetailData != null) {
            vendorDetailData.setEntityName(vendorDetail.getEntityName());
            vendorDetailData.setVendorId(vendorDetail.getVendorId());
            vendorDetailData.setPrimaryEmail(vendorDetail.getPrimaryEmail());
            vendorDetailData.setSecondaryEmail(vendorDetail.getSecondaryEmail());
            vendorDetailData.setPrimaryContact(vendorDetail.getPrimaryContact());
            vendorDetailData.setSecondaryContact(vendorDetail.getSecondaryContact());
            vendorDetailData.setVendorAddress(SCMDataConverter.convert(vendorDetail.getVendorAddress()));
            vendorDetailData.setLastName(vendorDetail.getLastName());
            vendorDetailData.setFirstName(vendorDetail.getFirstName());
            vendorDetailData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
            vendorDetailData.setUpdatedBy(vendorDetail.getUpdatedBy().getId());
            vendorDetailData.setType(vendorDetail.getType().name());
            vendorDetailData = vendorManagementDao.update(vendorDetailData, true);
            if (vendorDetailData != null) {
                String updatedBy = masterDataCache.getEmployee(vendorDetailData.getUpdatedBy());
                String requestedBy = masterDataCache.getEmployee(vendorDetailData.getRequestedBy());
                vendorDetail = SCMDataConverter.convertVendor(vendorDetailData, updatedBy, requestedBy);
                VendorLogData vendorLogData=new VendorLogData();
                updateVendorLogData(vendorLogData,vendorDetailData,updatedBy);
                scmCache.getVendorDetails().put(vendorDetailData.getVendorId(), vendorDetail);
                return vendorDetail;
            }
        }
        LOG.info("Vendor id: {} not found!", vendorDetail.getVendorId());
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateVendor(int vendorId) {
        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
        if (vendorDetailData != null) {
            vendorDetailData.setStatus(VendorStatus.IN_ACTIVE.name());
            vendorDetailData = vendorManagementDao.update(vendorDetailData, true);
            if (vendorDetailData != null) {
                String updatedBy = masterDataCache.getEmployee(vendorDetailData.getUpdatedBy());
                String requestedBy = masterDataCache.getEmployee(vendorDetailData.getRequestedBy());
                VendorLogData vendorLogData=new VendorLogData();
                updateVendorLogData(vendorLogData,vendorDetailData,updatedBy);
                scmCache.getVendorDetails().put(vendorDetailData.getVendorId(),
                        SCMDataConverter.convertVendor(vendorDetailData, updatedBy, requestedBy));
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateVendor(int vendorId) {
        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
        if (vendorDetailData != null) {
            vendorDetailData.setStatus(VendorStatus.ACTIVE.name());
            vendorDetailData = vendorManagementDao.update(vendorDetailData, true);
            if (vendorDetailData != null) {
                String updatedBy = masterDataCache.getEmployee(vendorDetailData.getUpdatedBy());
                String requestedBy = masterDataCache.getEmployee(vendorDetailData.getRequestedBy());
                VendorLogData vendorLogData=new VendorLogData();
                updateVendorLogData(vendorLogData,vendorDetailData,updatedBy);
                scmCache.getVendorDetails().put(vendorDetailData.getVendorId(),
                        SCMDataConverter.convertVendor(vendorDetailData, updatedBy, requestedBy));
                return true;
            }
        }
        return false;
    }

    private  void updateVendorLogData(VendorLogData vendorLogData,VendorDetailData vendorDetailData,String updatedBy) {

        try {
            vendorLogData.setVendorId(vendorDetailData.getVendorId());
            vendorLogData.setStatus((vendorDetailData.getStatus()));
            vendorLogData.setEntityName(vendorDetailData.getEntityName());
            vendorLogData.setVendorName(vendorDetailData.getFirstName() + " " + vendorDetailData.getLastName());
            vendorLogData.setUpdateTime(SCMUtil.getCurrentDateHourIST());
            vendorLogData.setLogData(vendorDetailData.getStatus() + "ED by " + updatedBy);
            vendorLogData = vendorManagementDao.add(vendorLogData, true);
            if (vendorLogData == null) {
                throw new SumoException("error in adding log data for vendor");
            }
        } catch (SumoException e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean checkForApprovedVendorAtleastOnce(Integer vendorId){
        return vendorManagementDao.checkIfVendorApprovedAtleastOnce(vendorId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateVendorUnblockTillDate(Integer vendorId, String unblockTillDate, Integer updatedBy) {
        try {
            VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
            if (Objects.nonNull(vendorDetailData)) {
                vendorDetailData.setUnblockedTillDate(AppUtils.getDate(unblockTillDate, "yyyy-MM-dd"));
                vendorDetailData.setVendorBlocked(null);
                vendorDetailData.setUpdatedBy(updatedBy);
                vendorDetailData.setUpdatedAt(AppUtils.getCurrentTimestamp());
                vendorDetailData = vendorManagementDao.update(vendorDetailData, true);
                VendorDetail vendorDetail = scmCache.getVendorDetail(vendorId);
                if (Objects.nonNull(vendorDetail)) {
                    vendorDetail.setVendorBlocked(AppConstants.NO);
                    vendorDetail.setUnblockedTillDate(vendorDetailData.getUnblockedTillDate());
                    scmCache.getVendorDetails().put(vendorId, vendorDetail);
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            LOG.error("Exception Occurred While updating the Vendor Unblock Till Date ::: ", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public VendorBlockResponse getPendingOrdersToBlockVendor(Integer vendorId) {
        return VendorBlockResponse.builder().vendorId(vendorId).vendorName(scmCache.getVendorDetail(vendorId).getEntityName())
                .details(getPendingOrdersData(vendorId)).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean blockUnblockVendor(Integer vendorId, Integer updatedBy, Boolean isBlock) {
        try {
            VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
            if (Objects.nonNull(vendorDetailData)) {
                if (isBlock) {
                    vendorDetailData.setVendorBlocked(AppConstants.YES);
                    vendorDetailData.setVendorBlockedReason("MANUAL");
                    vendorDetailData.setLastBlockedBy(updatedBy);
                    vendorDetailData.setUnblockedTillDate(null);
                    vendorDetailData.setLastBlockedDate(AppUtils.getCurrentTimestamp());
                } else {
                    vendorDetailData.setVendorBlocked(null);
                    vendorDetailData.setVendorBlockedReason(null);
                    vendorDetailData.setLastUnBlockedBy(updatedBy);
                    vendorDetailData.setLastUnBlockedDate(AppUtils.getCurrentTimestamp());
                }
                vendorDetailData.setUpdatedBy(updatedBy);
                vendorDetailData.setUpdatedAt(AppUtils.getCurrentTimestamp());
                vendorDetailData = vendorManagementDao.update(vendorDetailData, true);
                VendorDetail vendorDetail = scmCache.getVendorDetail(vendorId);
                if (Objects.nonNull(vendorDetail)) {
                    if (isBlock) {
                        vendorDetail.setVendorBlocked(vendorDetailData.getVendorBlocked());
                        vendorDetail.setBlockedReason(vendorDetailData.getVendorBlockedReason());
                        vendorDetail.setUnblockedTillDate(null);
                        vendorDetail.setLastBlockedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(vendorDetailData.getLastBlockedBy()), vendorDetailData.getLastBlockedBy()));
                        vendorDetailData.setLastBlockedDate(vendorDetailData.getLastBlockedDate());
                    } else {
                        vendorDetail.setVendorBlocked(null);
                        vendorDetail.setBlockedReason(null);
                        if (Objects.nonNull(vendorDetailData.getLastUnBlockedBy())) {
                            vendorDetail.setLastUnBlockedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(vendorDetailData.getLastUnBlockedBy()), vendorDetailData.getLastUnBlockedBy()));
                        }
                        if (Objects.nonNull(vendorDetailData.getLastUnBlockedDate())) {
                            vendorDetailData.setLastUnBlockedDate(vendorDetailData.getLastUnBlockedDate());
                        }
                    }
                    scmCache.getVendorDetails().put(vendorId, vendorDetail);
                }
                if (!isBlock) {
                    LOG.info("Unblock Vendor :: Checking for Pending Vendor Advances ::: ");
                    paymentRequestManagementService.refreshVendorAdvancePaymentsCache(vendorId);
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            LOG.error("Exception Occurred While Blocking/Unblocking Vendor ::: ", e);
            return false;
        }
    }

    private Map<String, List<VendorBlockItem>> getPendingOrdersData(Integer vendorId) {
        Map<String, List<VendorBlockItem>> result = new HashMap<>();
        List<PurchaseOrderData> purchaseOrderDataList = vendorManagementDao.getPendingPos(vendorId);
        List<ServiceOrderData> serviceOrderDataList = vendorManagementDao.getPendingSos(vendorId);
        List<VendorBlockItem> posList = new ArrayList<>();
        List<VendorBlockItem> sosList = new ArrayList<>();
        if (Objects.nonNull(purchaseOrderDataList) && !purchaseOrderDataList.isEmpty()) {
            posList = purchaseOrderDataList.stream().map(e -> {
                VendorBlockItem vendorBlockItem = VendorBlockItem.builder().poSoId(e.getId()).poSoType(e.getType()).poSoCreateDate(e.getGenerationTime())
                                .poSoStatus(e.getStatus()).poSoCreatedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(e.getGeneratedBy()), e.getGeneratedBy())).build();
                List<VendorBlockGrSr> vendorBlockGrSrs = new ArrayList<>();
                e.getGrMappingList().forEach(grm -> {
                    VendorGoodsReceivedData vendorGoodsReceivedData = grm.getVendorGoodsReceivedData();
                    if (!vendorGoodsReceivedData.getGrStatus().equalsIgnoreCase(SCMOrderStatus.CANCELLED.value())) {
                        VendorBlockGrSr vendorBlockGrSr = VendorBlockGrSr.builder().grSrId(grm.getVendorGoodsReceivedData().getGoodsReceivedId())
                                .grSrStatus(vendorGoodsReceivedData.getGrStatus()).build();
                        PaymentRequestData paymentRequestData = vendorGoodsReceivedData.getPaymentRequestData();
                        if (Objects.nonNull(paymentRequestData)) {
                            vendorBlockGrSr.setPrId(paymentRequestData.getId());
                            vendorBlockGrSr.setPrStatus(paymentRequestData.getCurrentStatus());
                            vendorBlockGrSr.setPrCreatedAt(paymentRequestData.getCreationTime());
                            vendorBlockGrSr.setPrCreatedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(paymentRequestData.getCreatedBy()),paymentRequestData.getCreatedBy()));
                        }
                        vendorBlockGrSrs.add(vendorBlockGrSr);
                    }
                });
                vendorBlockItem.setVendorBlockGrSrs(vendorBlockGrSrs);
                return vendorBlockItem;
            }).collect(Collectors.toList());
        }
        result.put("PURCHASE_ORDER", posList);

        if (Objects.nonNull(serviceOrderDataList) && !serviceOrderDataList.isEmpty()) {
            sosList = serviceOrderDataList.stream().map(e -> {
                VendorBlockItem vendorBlockItem = VendorBlockItem.builder().poSoId(e.getId()).poSoType(e.getType()).poSoCreateDate(e.getGenerationTime())
                        .poSoStatus(e.getStatus()).poSoCreatedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(e.getGeneratedBy()), e.getGeneratedBy())).build();
                List<VendorBlockGrSr> vendorBlockGrSrs = new ArrayList<>();
                List<ServiceOrderServiceReceiveMappingData> soSrList = serviceReceiveManagementDao.findSrsWithSoId(e.getId());
                soSrList.forEach(soSrData -> {
                    ServiceReceivedData receivedData = soSrData.getServiceReceivedData();
                    if (Objects.isNull(receivedData.getPaymentRequestData()) && !receivedData.getServiceReceiveStatus().equalsIgnoreCase(SCMOrderStatus.CANCELLED.value())) {
                        VendorBlockGrSr vendorBlockGrSr = VendorBlockGrSr.builder().grSrId(receivedData.getServiceReceivedId())
                                .grSrStatus(receivedData.getServiceReceiveStatus()).build();
                        PaymentRequestData paymentRequestData = receivedData.getPaymentRequestData();
                        if (Objects.nonNull(receivedData.getPaymentRequestData())) {
                            vendorBlockGrSr.setPrId(paymentRequestData.getId());
                            vendorBlockGrSr.setPrStatus(paymentRequestData.getCurrentStatus());
                            vendorBlockGrSr.setPrCreatedAt(paymentRequestData.getCreationTime());
                            vendorBlockGrSr.setPrCreatedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(paymentRequestData.getCreatedBy()),paymentRequestData.getCreatedBy()));
                        }
                        vendorBlockGrSrs.add(vendorBlockGrSr);
                    }
                });
                vendorBlockItem.setVendorBlockGrSrs(vendorBlockGrSrs);
                return vendorBlockItem;
            }).collect(Collectors.toList());
        }
        result.put("SERVICE_ORDER", sosList);
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean editVendorRequest(int vendorId, int userId) throws SumoException {
        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
        if (vendorDetailData != null) {
            vendorDetailData.setStatus(VendorStatus.IN_PROCESS.name());
            vendorDetailData = vendorManagementDao.update(vendorDetailData, true);
            rejectPreviousRequests(vendorDetailData.getVendorId());
            VendorRegistrationRequest request=create(vendorDetailData, userId);
            addVendorRegistrationRequest(request);
            String updatedBy = masterDataCache.getEmployee(vendorDetailData.getUpdatedBy());
            VendorLogData vendorLogData=new VendorLogData();
            VendorRegistrationRequestDetail registrationRequest = registrationDao.addVendorRegistrationRequest(request);
            String link = SCMUtil.getRegistrationLink(registrationRequest.getRegistrationUrl(),
                registrationRequest.getAuthKey());
            vendorLogData.setLink(link);
            updateVendorLogData(vendorLogData,vendorDetailData,updatedBy);
            scmMetadataService.addVendorToCache(vendorDetailData);
            return true;
        }
        return false;
    }

    private void rejectPreviousRequests(Integer vendorId) {
        List<VendorRegistrationRequestDetail> requests = vendorManagementDao
                .findCompletedRegistrationRequests(vendorId);
        if (requests != null) {
            for (VendorRegistrationRequestDetail req : requests) {
                req.setRequestStatus(VendorStatus.CANCELLED.name());
                vendorManagementDao.update(req, false);
            }
        }
    }

    /**
     * @param vendorDetailData
     * @return
     */
    private VendorRegistrationRequest create(VendorDetailData vendorDetailData, int userId) {
        VendorRegistrationRequest req = new VendorRegistrationRequest();
        req.setEmail(props.getVendorEditEmailNotification());
        req.setRequestById(userId);
        req.setRequestByName(masterDataCache.getEmployee(userId));
        req.setRequestForId(userId);
        req.setRequestForName(masterDataCache.getEmployee(userId));
        req.setRequestDate(AppUtils.getCurrentBusinessDate());
        req.setRequestStatus(VendorStatus.valueOf(vendorDetailData.getStatus()));
        req.setVendorId(vendorDetailData.getVendorId());
        req.setVendorName(vendorDetailData.getEntityName());
        req.setVendorType(VendorType.valueOf(vendorDetailData.getType()));
        return req;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<UnitVendorMapping> getUnitVendors(int unitId) {
        List<UnitVendorMappingData> unitVendorMappingDataList = vendorManagementDao
                .getUnitVendorMappingByUnitId(unitId);
        List<UnitVendorMapping> mapping = new ArrayList<>();
        unitVendorMappingDataList.forEach(unitVendorMappingData -> {
            VendorDetail vendorDetail = scmCache.getVendorDetail(unitVendorMappingData.getVendorId());
            UnitBasicDetail unitDetail = masterDataCache.getUnitBasicDetail(unitVendorMappingData.getUnitId());
            mapping.add(SCMDataConverter.convert(unitVendorMappingData, vendorDetail, unitDetail));
        });
        return mapping;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public UnitVendorMapping unitVendorAdd(UnitVendorMapping mapping) throws SumoException {
        if (mapping != null) {
            UnitVendorMappingData unitVendorMappingData = new UnitVendorMappingData();
            unitVendorMappingData.setMappingStatus(mapping.getMappingStatus().name());
            unitVendorMappingData.setUnitId(mapping.getUnit().getId());
            unitVendorMappingData.setVendorId(mapping.getVendor().getId());
            unitVendorMappingData.setFulfillmentType(mapping.getFulFillmentType().name());
            unitVendorMappingData.setEmailNotification(AppUtils.setStatus(mapping.isEmailNotification()));
            unitVendorMappingData.setSmsNotification(AppUtils.setStatus(mapping.isSmsNotification()));
            unitVendorMappingData.setNotifyDaysBefore(mapping.getNoOfDays());
            unitVendorMappingData.setFulfillmentLeadDays(mapping.getFulfillmentLeadDays());
            unitVendorMappingData.setNotificationTime(mapping.getNotificationTime());
            unitVendorMappingData.setDeliveryPromiseTime(mapping.getDeliveryPromiseTime());
            unitVendorMappingData.setCreatedBy(mapping.getCreatedBy().getId());
            unitVendorMappingData.setCreationTime(AppUtils.getCurrentDate());
            unitVendorMappingData.setDispatchLocationId(mapping.getDispatchLocationId());
            unitVendorMappingData = vendorManagementDao.add(unitVendorMappingData, true);
            if (unitVendorMappingData != null) {
                VendorDetail vendorDetail = scmCache.getVendorDetail(unitVendorMappingData.getVendorId());
                UnitBasicDetail unitDetail = masterDataCache.getUnitBasicDetail(unitVendorMappingData.getUnitId());
                return SCMDataConverter.convert(unitVendorMappingData, vendorDetail, unitDetail);
            } else {
                LOG.error("Error adding unit vendor mapping: {}", new Gson().toJson(mapping));
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean unitVendorUpdate(UnitVendorMapping mapping) {
        if (mapping != null) {
            UnitVendorMappingData unitVendorMappingData = vendorManagementDao.find(UnitVendorMappingData.class, mapping.getUnitVendorMappingId());
            unitVendorMappingData.setFulfillmentType(mapping.getFulFillmentType().name());
            unitVendorMappingData.setEmailNotification(
                    AppUtils.setStatus(mapping.isEmailNotification() == null ? false : mapping.isEmailNotification()));
            unitVendorMappingData.setSmsNotification(
                    AppUtils.setStatus(mapping.isSmsNotification() == null ? false : mapping.isSmsNotification()));
            unitVendorMappingData.setNotifyDaysBefore(mapping.getNoOfDays());
            unitVendorMappingData.setFulfillmentLeadDays(mapping.getFulfillmentLeadDays());
            unitVendorMappingData.setNotificationTime(mapping.getNotificationTime());
            unitVendorMappingData.setDeliveryPromiseTime(mapping.getDeliveryPromiseTime());
            unitVendorMappingData.setLastUpdatedBy(mapping.getLastUpdatedBy().getId());
            unitVendorMappingData.setLastUpdateTime(AppUtils.getCurrentBusinessDate());
            unitVendorMappingData.setDispatchLocationId(mapping.getDispatchLocationId());
            unitVendorMappingData = vendorManagementDao.update(unitVendorMappingData, true);
            if (unitVendorMappingData != null) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean unitVendorActivate(int mappingId) {
        UnitVendorMappingData unitVendorMappingData = vendorManagementDao.find(UnitVendorMappingData.class, mappingId);
        if (unitVendorMappingData != null) {
            unitVendorMappingData.setMappingStatus(SwitchStatus.ACTIVE.name());
            unitVendorMappingData = vendorManagementDao.update(unitVendorMappingData, true);
            if (unitVendorMappingData != null) {
                return true;
            } else {
                LOG.error("Error activating unit vendor mapping: {}", mappingId);
            }
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addVendorRegistrationRequest(VendorRegistrationRequest request) throws SumoException {
        List<VendorRegistrationRequestDetail> notCompletedRequests = registrationDao.findInitiatedRequestsByName(request.getVendorName().toUpperCase().trim() , request.getVendorType().value());
        if(Objects.isNull(request.getVendorId()) && Objects.nonNull(notCompletedRequests) && !notCompletedRequests.isEmpty()){
            String title = "";
            String  msg = "Request Email : " + "<b>" + notCompletedRequests.get(0).getEmail() + "</b>" + "<br><br>" +
                    "Requested By : " +  "<b>"+ notCompletedRequests.get(0).getRequestByName() + "</b>" ;
            if(notCompletedRequests.get(0).getRequestStatus().equals(VendorStatus.INITIATED.value())){
                title += "There is a  Request Already In INITIATED Status For This Vendor , Please Cancel Previous Request";
            }else{
                title += "There is a  Request Already In IN_PROCESS Status For This Vendor";
            }
            throw new SumoException(title,msg);
        }

        VendorRegistrationRequestDetail registrationRequest = registrationDao.addVendorRegistrationRequest(request);
        boolean isNew = request.getVendorId() == null || request.getVendorId() == 0;
        if (registrationRequest != null) {
            try {
                List<String> emails = new ArrayList<>();
                emails.add(registrationRequest.getEmail());
                if (registrationRequest.getCopyEmails() != null) {
                    String[] e = registrationRequest.getCopyEmails().split(",");
                    for (String s : e) {
                        emails.add(s);
                    }
                }
                String link = SCMUtil.getRegistrationLink(registrationRequest.getRegistrationUrl(),
                        registrationRequest.getAuthKey());
                VendorRegistrationRequestNotificationTemplate template = new VendorRegistrationRequestNotificationTemplate(
                        request, link, props.getBasePath());
                VendorRegistrationRequestNotification notification = new VendorRegistrationRequestNotification(template,
                        props.getEnvType(), emails, isNew);
                notification.sendEmail();
            } catch (Exception e) {
                LOG.error("Error while sending registration email to vendor {}", registrationRequest.getVendorName(),
                        e);
                registrationDao.updateVendorStatus(registrationRequest.getId(), VendorStatus.FAILED);
                return false;
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean unitVendorDeactivate(int mappingId) {
        UnitVendorMappingData unitVendorMappingData = vendorManagementDao.find(UnitVendorMappingData.class, mappingId);
        if (unitVendorMappingData != null) {
            unitVendorMappingData.setMappingStatus(SwitchStatus.IN_ACTIVE.name());
            unitVendorMappingData = vendorManagementDao.update(unitVendorMappingData, true);
            if (unitVendorMappingData != null) {
                return true;
            } else {
                LOG.error("Error deactivating unit vendor mapping: {}", mappingId);
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorRegistrationRequest> getAllVendorRegistrationRequests(Date startDate, Date endDate,
                                                                            boolean getPendingOnly) {
        return registrationDao.getAllVendorRegistrationRequest(startDate, endDate, getPendingOnly);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorRegistrationRequest> getAllVendorRegistrationRequests(List<String> status) {
        return registrationDao.getAllVendorRegistrationRequest(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelVendorRegistrationRequest(Integer id) {
        return registrationDao.cancelVendorRegistrationRequest(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean blockVendorPayments(VendorEditVO request) throws SumoException {
        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, request.getVendorId());
        VendorAccountDetailData vendorAccountDetailData = vendorDetailData.getAccountDetails();
        if (Objects.nonNull(vendorAccountDetailData)) {
            vendorAccountDetailData.setPaymentBlocked(SCMUtil.setStatus(true));
            if (Objects.nonNull(request.getBlockedReason())) {
                vendorAccountDetailData.setPaymentsBlockedReason(request.getBlockedReason());
                vendorAccountDetailData.setLastPaymentsBlockedOn(AppUtils.getCurrentTimestamp());
            }
            vendorManagementDao.update(vendorAccountDetailData, true);
            VendorDetail vendorDetail = scmCache.getVendorDetail(request.getVendorId());
            if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getAccountDetails())) {
                vendorDetail.getAccountDetails().setPaymentBlocked(true);
                scmCache.getVendorDetails().put(request.getVendorId(), vendorDetail);
                paymentRequestManagementService.blockAllPRsForVendor(request);
            }
        } else {
            LOG.info("Vendor Account Details are Unavailable for Vendor Id : {}",request.getVendorId());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean unBlockVendorPayments(VendorEditVO request) throws SumoException {
        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, request.getVendorId());
        VendorAccountDetailData vendorAccountDetailData = vendorDetailData.getAccountDetails();
        if (Objects.nonNull(vendorAccountDetailData)) {
            vendorAccountDetailData.setPaymentBlocked(SCMUtil.setStatus(false));
            vendorManagementDao.update(vendorAccountDetailData, true);
            VendorDetail vendorDetail = scmCache.getVendorDetail(request.getVendorId());
            if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getAccountDetails())) {
                vendorDetail.getAccountDetails().setPaymentBlocked(false);
                scmCache.getVendorDetails().put(request.getVendorId(), vendorDetail);
                paymentRequestManagementService.unBlockAllPRsForVendor(request);
            }
        } else {
            LOG.info("Vendor Account Details are Unavailable for Vendor Id : {}",request.getVendorId());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VendorRegistrationRequest validateRequest(String id)
            throws VendorRegistrationException, UnsupportedEncodingException {
        id = URLDecoder.decode(id, "UTF-8");
        VendorRegistrationRequestDetail request = registrationDao.findByToken(id);
        if (request != null) {
//            if (!SCMUtil.validateCreationTime(request.getRequestDate(), 15)
//                    && request.getRequestStatus().equals(VendorStatus.INITIATED)) {
//                //request.setRequestStatus(VendorStatus.EXPIRED.name());
//                registrationDao.update(request, true);
//            }
            Map<String,String> disclaimerMap = vendorManagementDao.getAllActiveAgreements("VENDOR").stream().
                    collect(Collectors.toMap(AgreementDetail::getSubtype,AgreementDetail::getValue));

            VendorRegistrationRequest result = SCMDataConverter.convert(request);
            result.setDisclaimerMap(disclaimerMap);
            return result;

        }
        return null;
    }


 private void   updateEditedField(List<VendorEditedDetail>  vendorEditedDetail,String fieldName,String editedType,Integer vendorId)
 {
     boolean notExistField=true;
    for(VendorEditedDetail vendorEditedDetail1:vendorEditedDetail)
    {
        if(vendorEditedDetail1.getEditedType().equals(editedType) && vendorEditedDetail1.getFieldName().equals(fieldName))
        {
            notExistField=false;
            vendorEditedDetail1.setStatus(VendorEditedFieldStatus.UPDATED.name());
            registrationDao.update(vendorEditedDetail1,true);
            break;
        }
    }
    if(notExistField)
    {
        VendorEditedDetail vendorEditedDetail1=new VendorEditedDetail();
        vendorEditedDetail1.setVendorId(vendorId);
        vendorEditedDetail1.setStatus(VendorEditedFieldStatus.UPDATED.name());
        vendorEditedDetail1.setEditedType(editedType);
        vendorEditedDetail1.setFieldName(fieldName);
        registrationDao.update(vendorEditedDetail1,true);
    }

 }
 private void maintainEditedAddressDetail(List<VendorEditedDetail>  vendorEditedDetail ,AddressDetailData previousAddress,AddressDetail newAddress,String editedField,String editedType,Integer vendorId)
    {
        if(previousAddress!=null && newAddress!=null) {
            if(previousAddress.getCity() !=null && !previousAddress.getCity().equals(newAddress.getCity()))
            {
                updateEditedField(vendorEditedDetail,editedField,editedType,vendorId);

            }
           else if((previousAddress.getLine1()==null && newAddress.getLine1()!=null) ||(previousAddress.getLine1()!=null && !previousAddress.getLine1().equals(newAddress.getLine1())))
            {
                updateEditedField(vendorEditedDetail,editedField,editedType,vendorId);

            }
           else if((previousAddress.getLine2()==null && newAddress.getLine2()!=null) ||(previousAddress.getLine2()!=null && !previousAddress.getLine2().equals(newAddress.getLine2())))
            {
                updateEditedField(vendorEditedDetail,editedField,editedType,vendorId);

            }
           else if((previousAddress.getLine3()==null && newAddress.getLine3()!=null) ||(previousAddress.getLine3()!=null && !previousAddress.getLine3().equals(newAddress.getLine3())))
            {
                updateEditedField(vendorEditedDetail,editedField,editedType,vendorId);
            }

        }
        else if(previousAddress==null && newAddress!=null)
        {
            updateEditedField(vendorEditedDetail,editedField,editedType,vendorId);

        }
        else if(newAddress!=null)
        {
            if(previousAddress.getCity() !=null && !previousAddress.getCity().equals(newAddress.getCity()))
            {
                updateEditedField(vendorEditedDetail,editedField,editedType,vendorId);

            }
            else if(newAddress.getLine1()!=null || newAddress.getLine2()!=null || newAddress.getLine3()!=null)
            {
                updateEditedField(vendorEditedDetail,editedField,editedType,vendorId);

            }
        }


    }

    private  void  maintainVendorEditedField(List<VendorEditedDetail>  vendorEditedDetail,VendorDetailData vendorDetailData,VendorDetail detail)
{
    if(vendorEditedDetail==null)
    {
        vendorEditedDetail=new ArrayList<>();
    }

        if(vendorDetailData !=null && detail !=null) {
            if ((vendorDetailData.getEntityName() == null && detail.getEntityName() != null) || (vendorDetailData.getEntityName() != null && !vendorDetailData.getEntityName().equals(StringUtils.capitalize(detail.getEntityName())))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ENTITY_NAME.name(), VendorEditedType.PERSONAL_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorDetailData.getFirstName() == null && detail.getFirstName() != null) || (vendorDetailData.getFirstName() != null && !vendorDetailData.getFirstName().equals(StringUtils.capitalize(detail.getFirstName())))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.FIRST_NAME.name(), VendorEditedType.PERSONAL_DETAIL.name(), vendorDetailData.getVendorId());

            }
            if ((vendorDetailData.getLastName() == null && detail.getLastName() != null) || (vendorDetailData.getLastName() != null && !vendorDetailData.getLastName().equals(StringUtils.capitalize(detail.getLastName())))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.LAST_NAME.name(), VendorEditedType.PERSONAL_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorDetailData.getPrimaryContact() == null && detail.getPrimaryContact() != null) || (vendorDetailData.getPrimaryContact() != null && !vendorDetailData.getPrimaryContact().equals(detail.getPrimaryContact()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.PRIMARY_CONTACT.name(), VendorEditedType.PERSONAL_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorDetailData.getSecondaryContact() == null && detail.getSecondaryContact() != null) || (vendorDetailData.getSecondaryContact() != null && !vendorDetailData.getSecondaryContact().equals(detail.getSecondaryContact()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.SECONDARY_CONTACT.name(), VendorEditedType.PERSONAL_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorDetailData.getPrimaryEmail() == null && detail.getPrimaryEmail() != null) || (vendorDetailData.getPrimaryEmail() != null && !vendorDetailData.getPrimaryEmail().equals(StringUtils.lowerCase(detail.getPrimaryEmail())))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.PRIMARY_EMAIL.name(), VendorEditedType.PERSONAL_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorDetailData.getSecondaryEmail() == null && detail.getSecondaryEmail() != null) || (vendorDetailData.getSecondaryEmail() != null && !vendorDetailData.getSecondaryEmail().equals(StringUtils.lowerCase(detail.getSecondaryEmail())))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.SECONDARY_EMAIL.name(), VendorEditedType.PERSONAL_DETAIL.name(), vendorDetailData.getVendorId());
            }

            AddressDetailData previousAddress=vendorDetailData.getVendorAddress();
            AddressDetail newAddress=detail.getVendorAddress();
            maintainEditedAddressDetail(vendorEditedDetail ,previousAddress,newAddress,VendorEditedFieldType.VENDOR_ADDRESS.name(),VendorEditedType.PERSONAL_DETAIL.name(),vendorDetailData.getVendorId());
        }



}


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VendorDetail saveBasicDetails(VendorDetail detail) throws VendorRegistrationException {
        try {

            VendorRegistrationRequestDetail requestDetail = registrationDao.find(VendorRegistrationRequestDetail.class,
                    detail.getRegistrationId());
            Boolean isEdited = detail.getVendorId() != null ? true : false;
            VendorDetailData vendorDetailData;

            if (detail.getVendorId() != null) {
                vendorDetailData = registrationDao.find(VendorDetailData.class, detail.getVendorId());
                List<VendorEditedDetail> vendorEditedDetail=registrationDao.findVendorEditedDetailByVendorId(detail.getVendorId());
                maintainVendorEditedField(vendorEditedDetail,vendorDetailData,detail);
                vendorDetailData = updateDetails(detail, vendorDetailData);
            } else {
                vendorDetailData = SCMDataConverter.convert(detail, false);
            }

            vendorDetailData.setStatus(VendorStatus.IN_PROCESS.name());
            vendorDetailData.setRequestedBy(requestDetail.getRequestForId());
            vendorDetailData.setUpdatedBy(requestDetail.getRequestById());
            vendorDetailData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
            vendorDetailData.setType(requestDetail.getVendorType());
            vendorDetailData.setDisclaimerAccepted(AppConstants.getValue(detail.isDisclaimerAccepted()));
            AddressDetailData addressDetailData = SCMDataConverter.convert(detail.getVendorAddress());

            addressDetailData = (addressDetailData.getAddressId() != null
                    ? registrationDao.update(addressDetailData, true) : registrationDao.add(addressDetailData, true));
            if (addressDetailData.getAddressId() != null) {
                vendorDetailData.setVendorAddress(addressDetailData);
            }
            vendorDetailData = vendorDetailData.getVendorId() != null ? registrationDao.update(vendorDetailData, true)
                    : registrationDao.add(vendorDetailData, true);
            if (vendorDetailData.getVendorId() != null) {
                String updatedBy = masterDataCache.getEmployee(vendorDetailData.getUpdatedBy());
                String requestedBy = masterDataCache.getEmployee(vendorDetailData.getRequestedBy());
                detail  = scmMetadataService.addVendorToCache(vendorDetailData);
                requestDetail.setRequestStatus(VendorStatus.IN_PROCESS.name());
                requestDetail.setVendorId(vendorDetailData.getVendorId());
                registrationDao.update(requestDetail, true);
                detail.setRegistrationId(detail.getRegistrationId());
                List<AgreementDetail> agreements = vendorManagementDao.getAllActiveAgreements(AppConstants.AGREEMENT_TYPE_VENDOR);
                for(AgreementDetail agreement: agreements){
                    AgreementSignRecord agreementRecord = getAgreementRecord(vendorDetailData.getVendorId(),agreement.getAgreementId(), requestDetail.getRequestById(),isEdited);
                    registrationDao.add(agreementRecord,true);
                }
            }

        } catch (Exception e) {
            String errorMessage = "Encountered error while saving basic details for vendor";
            LOG.error(errorMessage, e);
            throw new VendorRegistrationException(errorMessage, e);
        }
        return detail;
    }

    private AgreementSignRecord getAgreementRecord(Integer vendorId, Integer agreementId , Integer requesterUserId, Boolean isEdited ){
        AgreementSignRecord record = new AgreementSignRecord();
        record.setVendorId(vendorId);
        record.setAgreementId(agreementId);
        record.setAgreementSignDate(AppUtils.getCurrentTimestamp());
        record.setRequesterUserId(requesterUserId);
        record.setRecordReason( (isEdited ? AppConstants.EDITED_VENDOR : AppConstants.CREATED_VENDOR) );

        return record;
    }


    private VendorDetailData updateDetails(VendorDetail detail, VendorDetailData vendorDetailData) {
        vendorDetailData.setEntityName(StringUtils.capitalize(detail.getEntityName()));
        vendorDetailData.setFirstName(StringUtils.capitalize(detail.getFirstName()));
        vendorDetailData.setLastName(StringUtils.capitalize(detail.getLastName()));
        vendorDetailData.setType(detail.getType().name());
        vendorDetailData.setStatus(detail.getStatus().name());

        vendorDetailData.setRequestedBy(detail.getRequestedBy() != null ? detail.getRequestedBy().getId() : null);
        vendorDetailData.setUpdatedBy(detail.getUpdatedBy() != null ? detail.getUpdatedBy().getId() : null);
        vendorDetailData.setUpdatedAt(SCMUtil.getCurrentTimestamp());

        vendorDetailData.setPrimaryContact(detail.getPrimaryContact());
        vendorDetailData.setSecondaryContact(detail.getSecondaryContact());
        vendorDetailData.setPrimaryEmail(StringUtils.lowerCase(detail.getPrimaryEmail()));
        if (detail.getSecondaryEmail() == null || detail.getSecondaryEmail().equalsIgnoreCase("")) {
            vendorDetailData.setSecondaryEmail(null);
        } else {
            vendorDetailData.setSecondaryEmail(StringUtils.lowerCase(detail.getSecondaryEmail()));
        }

        return vendorDetailData;
    }

    @Override
    public String getRegistrationAuthToken() {
        return props.getVendorClientToken();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public VendorDetail getVendor(Integer vendorId) {
        VendorDetail detail = null;
        VendorDetailData vendor = vendorManagementDao.find(VendorDetailData.class, vendorId);
        if (vendor != null) {
            String updatedBy = masterDataCache.getEmployee(vendor.getUpdatedBy());
            String requestedBy = masterDataCache.getEmployee(vendor.getRequestedBy());
            detail = SCMDataConverter.convertVendor(vendor, updatedBy, requestedBy);
            return detail;
        }
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer vendorId,
                                         MultipartFile file) {
        String fileName = MultiPartFileHelper.getVendorUploadFileName(type, vendorId, mimeType);
        FileDetail s3File = null;
        try {
            s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "vendors", fileName, file);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setUploadTypeId(vendorId);
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail
                    .setUpdatedBy(SCMUtil.generateIdCodeName(SCMServiceConstants.SYSTEM_USER, "SYSTEM", "SYSTEM"));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = registrationDao.add(SCMDataConverter.convert(documentDetail), true);
            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }
        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadTdsDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer vendorId,
                                         MultipartFile file,Boolean tdsStatus) {
        String fileName = MultiPartFileHelper.getVendorUploadFileName(type, vendorId, mimeType);
        FileDetail s3File = null;
        try {
            s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "vendors", fileName, file);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setUploadTypeId(vendorId);
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail
                .setUpdatedBy(SCMUtil.generateIdCodeName(SCMServiceConstants.SYSTEM_USER, "SYSTEM", "SYSTEM"));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = registrationDao.add(SCMDataConverter.convert(documentDetail), true);
            if (data.getDocumentId() != null) {
                VendorDetailData vendorDetailData=registrationDao.find(VendorDetailData.class,vendorId);
                vendorDetailData.setTdsDocument(data);
                if(tdsStatus) {
                    vendorDetailData.setTds(SCMServiceConstants.SCM_CONSTANT_YES);
                }
                else
                {
                    vendorDetailData.setTds(SCMServiceConstants.SCM_CONSTANT_NO);
                }
                registrationDao.update(vendorDetailData,true);
                return SCMDataConverter.convert(data);
            }
        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }

    private void maintainEditedDataForCompanyDetail(List<VendorEditedDetail> vendorEditedDetail,VendorDetailData vendorDetailData,VendorCompanyDetailData companyData,AddressDetail newAddress)
    {
        if(vendorEditedDetail==null)
        {
            vendorEditedDetail=new ArrayList<>();
        }
        VendorCompanyDetailData vendorCompanyDetailData=vendorDetailData.getCompanyDetails();

        if(vendorCompanyDetailData!=null && companyData!=null) {
            if ((vendorCompanyDetailData.getMsmeRegistered() == null && companyData.getMsmeRegistered() != null) || (vendorCompanyDetailData.getMsmeRegistered() != null && !vendorCompanyDetailData.getMsmeRegistered().equals(companyData.getMsmeRegistered()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.MSME_REGISTERED.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getCIN() == null && companyData.getCIN() != null) || (vendorCompanyDetailData.getCIN() != null && !vendorCompanyDetailData.getCIN().equals(companyData.getCIN()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CIN.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getCinDocument() == null && companyData.getCinDocument() != null)) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CIN_DOCUMENT.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getCompanyName() == null && companyData.getCompanyName() != null) || (vendorCompanyDetailData.getCompanyName() != null && !vendorCompanyDetailData.getCompanyName().equals(companyData.getCompanyName()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.COMPANY_NAME.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getCompanyType() == null && companyData.getCompanyType() != null) || (vendorCompanyDetailData.getCompanyType() != null && !vendorCompanyDetailData.getCompanyType().equals(companyData.getCompanyType()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.COMPANY_TYPE.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getPAN() == null && companyData.getPAN() != null) || (vendorCompanyDetailData.getPAN() != null && !vendorCompanyDetailData.getPAN().equals(companyData.getPAN()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.PAN.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getARC() == null && companyData.getARC() != null) || (vendorCompanyDetailData.getARC() != null && !vendorCompanyDetailData.getARC().equals(companyData.getARC()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ARC.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getCST() == null && companyData.getCST() != null) || (vendorCompanyDetailData.getCST() != null && !vendorCompanyDetailData.getCST().equals(companyData.getCST()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CST.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getMsmeDocument() == null && companyData.getMsmeDocument() != null)) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.MSME_DOCUMENT.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getVatDocument() == null && companyData.getVatDocument() != null) ) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.VAT_DOCUMENT.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorCompanyDetailData.getPanDocument() == null && companyData.getPanDocument() != null)) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.PAN_DOCUMENT.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());

            }
            if ((vendorCompanyDetailData.getRegisteredCompanyName() == null && companyData.getRegisteredCompanyName() != null) || (vendorCompanyDetailData.getRegisteredCompanyName() != null && !vendorCompanyDetailData.getRegisteredCompanyName().equals(companyData.getRegisteredCompanyName()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.REGISTERED_NAME.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());

            }
            AddressDetailData previousAddress=vendorCompanyDetailData.getCompanyAddress();
            maintainEditedAddressDetail(vendorEditedDetail ,previousAddress,newAddress,VendorEditedFieldType.COMPANY_ADDRESS.name(),VendorEditedType.COMPANY_DETAIL.name(),vendorDetailData.getVendorId());

        }
        else if(companyData!=null){
             if (companyData.getMsmeRegistered() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.MSME_REGISTERED.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getCIN() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CIN.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getCinDocument() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CIN_DOCUMENT.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getCompanyName() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.COMPANY_NAME.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getCompanyType() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.COMPANY_TYPE.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getPAN() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.PAN.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getARC() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ARC.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getCST() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CST.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getMsmeDocument() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.MSME_DOCUMENT.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getVatDocument() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.VAT_DOCUMENT.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (companyData.getPanDocument() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.PAN_DOCUMENT.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());

            }
            if (companyData.getRegisteredCompanyName() != null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.REGISTERED_NAME.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());

            }
            if (companyData.getCompanyAddress()!=null){
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.COMPANRY_ADDRESS.name(), VendorEditedType.COMPANY_DETAIL.name(), vendorDetailData.getVendorId());

            }
        }

    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VendorDetail saveCompanyDetails(VendorCompanyDetail detail) throws SumoException {
        VendorDetail vendorDetail = null;
        VendorDetailData vendorData = registrationDao.find(VendorDetailData.class, detail.getVendorDetail().getId());



        VendorCompanyDetailData companyData = SCMDataConverter.convert(detail, null);
        companyData.setVendorDetail(vendorData);
        AddressDetailData addressDetailData = SCMDataConverter.convert(detail.getCompanyAddress());

           List<VendorEditedDetail> vendorEditedDetail=registrationDao.findVendorEditedDetailByVendorId(detail.getVendorDetail().getId());
           maintainEditedDataForCompanyDetail(vendorEditedDetail,vendorData,companyData,detail.getCompanyAddress());

        addressDetailData = (addressDetailData.getAddressId() != null ? registrationDao.update(addressDetailData, true)
                : registrationDao.add(addressDetailData, true));
        if (addressDetailData.getAddressId() != null) {
            companyData.setCompanyAddress(addressDetailData);
        }
        companyData = (companyData.getCompanyDetailId() != null ? registrationDao.update(companyData, true)
                : registrationDao.add(companyData, true));
        if (companyData.getCompanyDetailId() != null) {
            vendorData.setCompanyDetails(companyData);
            registrationDao.update(vendorData, true);
            String updatedBy = masterDataCache.getEmployee(vendorData.getUpdatedBy());
            String requestedBy = masterDataCache.getEmployee(vendorData.getRequestedBy());
            vendorDetail = scmMetadataService.addVendorToCache(vendorData);
        }
        return vendorDetail;
    }


    private  void maintainEditedDataForAccountDetail(List<VendorEditedDetail> vendorEditedDetail,VendorAccountDetailData accountDetailData,VendorDetailData vendorDetailData)
    {
        VendorAccountDetailData  vendorAccountDetailData=vendorDetailData.getAccountDetails();

        if(vendorEditedDetail==null)
        {
            vendorEditedDetail=new ArrayList<>();
        }
        if(vendorAccountDetailData !=null && accountDetailData !=null) {
            if ((vendorAccountDetailData.getAccountContactName() == null && accountDetailData.getAccountContactName() != null) || (vendorAccountDetailData.getAccountContactName() != null && !vendorAccountDetailData.getAccountContactName().equals(accountDetailData.getAccountContactName()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_CONTACT_NAME.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorAccountDetailData.getAccountContactEmail() == null && accountDetailData.getAccountContactEmail() != null) || (vendorAccountDetailData.getAccountContactEmail() != null && !vendorAccountDetailData.getAccountContactEmail().equals(accountDetailData.getAccountContactEmail()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_CONTACT_EMAIL.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorAccountDetailData.getAccountNumber() == null && accountDetailData.getAccountNumber() != null) || (vendorAccountDetailData.getAccountNumber() != null && !vendorAccountDetailData.getAccountNumber().equals(accountDetailData.getAccountNumber()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_NUMBAR.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorAccountDetailData.getAccountType() == null && accountDetailData.getAccountType() != null) || (vendorAccountDetailData.getAccountType() != null && !vendorAccountDetailData.getAccountType().equals(accountDetailData.getAccountType()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_TYPE.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorAccountDetailData.getIfscCode() == null && accountDetailData.getIfscCode() != null) || (vendorAccountDetailData.getIfscCode() != null && !vendorAccountDetailData.getIfscCode().equals(accountDetailData.getIfscCode()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.IFSC_CODE.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorAccountDetailData.getAccountKind() == null && accountDetailData.getAccountKind() != null) || (vendorAccountDetailData.getAccountKind() != null && !vendorAccountDetailData.getAccountKind().equals(accountDetailData.getAccountKind()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_KIND.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorAccountDetailData.getContactNumber() == null && accountDetailData.getContactNumber() != null) || (vendorAccountDetailData.getContactNumber() != null && !vendorAccountDetailData.getContactNumber().equals(accountDetailData.getContactNumber()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CONTACT_NUMBER.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorAccountDetailData.getUploadedChequeDocumentID() == null && accountDetailData.getUploadedChequeDocumentID() != null)) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CANCELED_CHEQUE.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((vendorAccountDetailData.getMicrCode() == null && accountDetailData.getMicrCode() != null) || (vendorAccountDetailData.getMicrCode() != null && !vendorAccountDetailData.getMicrCode().equals(accountDetailData.getMicrCode()))) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.MICRE_CODE.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());

            }
        }
        else if(accountDetailData!=null)
        {
            if (accountDetailData.getAccountContactName() != null)   {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_CONTACT_NAME.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (accountDetailData.getAccountContactEmail() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_CONTACT_EMAIL.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (accountDetailData.getAccountNumber() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_NUMBAR.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (accountDetailData.getAccountType() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_TYPE.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ((accountDetailData.getIfscCode() != null)) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.IFSC_CODE.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if ( accountDetailData.getAccountKind() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.ACCOUNT_KIND.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (accountDetailData.getContactNumber() != null ) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CONTACT_NUMBER.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (accountDetailData.getUploadedChequeDocumentID() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.CANCELED_CHEQUE.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());
            }
            if (accountDetailData.getMicrCode() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.MICRE_CODE.name(), VendorEditedType.ACCOUNT_DETAIL.name(), vendorDetailData.getVendorId());

            }
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VendorDetail saveAccountDetails(VendorAccountDetail detail) throws SumoException {
        VendorDetail vendorDetail = null;
        VendorDetailData vendorData = registrationDao.find(VendorDetailData.class, detail.getVendorDetail().getId());

        VendorAccountDetailData accountDetailData = SCMDataConverter.convert(detail, null);
        accountDetailData.setVendorDetail(vendorData);
        List<VendorEditedDetail> vendorEditedDetail=registrationDao.findVendorEditedDetailByVendorId(detail.getVendorDetail().getId());
        maintainEditedDataForAccountDetail(vendorEditedDetail,accountDetailData,vendorData);

        accountDetailData = (accountDetailData.getAccountDetailId() != null
                ? registrationDao.update(accountDetailData, true) : registrationDao.add(accountDetailData, true));
        if (accountDetailData.getAccountDetailId() != null) {
            vendorData.setAccountDetails(accountDetailData);
            registrationDao.update(vendorData, true);
            String updatedBy = masterDataCache.getEmployee(vendorData.getUpdatedBy());
            String requestedBy = masterDataCache.getEmployee(vendorData.getRequestedBy());
            vendorDetail = scmMetadataService.addVendorToCache(vendorData);
        }
        return vendorDetail;
    }



    private void maintainEditedDataForDispatchLocation(List<VendorEditedDetail> vendorEditedDetail,VendorDispatchLocationDetailData locationDetailData ,VendorDetailData vendorDetailData,AddressDetail newAddress)
    {
        if(vendorEditedDetail==null)
        {
            vendorEditedDetail=new ArrayList<>();
        }

        Set<VendorDispatchLocationDetailData> previousDispatchLocation=vendorDetailData.getDispatchLocations();

        boolean notExistingLocation=true;
        if(previousDispatchLocation!=null && locationDetailData!=null) {
            for (VendorDispatchLocationDetailData location : previousDispatchLocation) {
                if (location.getDispatchLocationId().equals(locationDetailData.getDispatchLocationId())) {

                    if ((location.getEmailId() == null && locationDetailData.getEmailId() != null) || (location.getEmailId() != null && !location.getEmailId().equals(locationDetailData.getEmailId()))) {
                        updateEditedField(vendorEditedDetail, VendorEditedFieldType.EMAIL_ID.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

                    }
                    if ((location.getTIN() == null && locationDetailData.getTIN() != null) || (location.getTIN() != null && !location.getTIN().equals(locationDetailData.getTIN()))) {
                        updateEditedField(vendorEditedDetail, VendorEditedFieldType.TIN.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

                    }
                    if ((locationDetailData.getGSTIN() != null && location.getGSTIN() == null) || (location.getGSTIN() != null && !location.getGSTIN().equals(locationDetailData.getGSTIN()))) {
                        updateEditedField(vendorEditedDetail, VendorEditedFieldType.GSTIN.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

                    }
                    if ((location.getGstinDocument() == null && locationDetailData.getGstinDocument() != null)) {
                        updateEditedField(vendorEditedDetail, VendorEditedFieldType.GSTIN_DOCUMENT.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

                    }
                    if ((locationDetailData.getLocationName() != null && location.getLocationName() == null) || (location.getLocationName() != null && !location.getLocationName().equals(locationDetailData.getLocationName()))) {
                        updateEditedField(vendorEditedDetail, VendorEditedFieldType.LOCATION_NAME.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

                    }
                    if ((locationDetailData.getNotificationType() != null && location.getNotificationType() == null) || (location.getNotificationType() != null && !location.getNotificationType().equals(locationDetailData.getNotificationType()))) {
                        updateEditedField(vendorEditedDetail, VendorEditedFieldType.NOTIFICATION.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

                    }
                    if ((locationDetailData.getApplyTax() != null && location.getApplyTax() == null) || (location.getApplyTax() != null && !location.getApplyTax().equals(locationDetailData.getApplyTax()))) {
                        updateEditedField(vendorEditedDetail, VendorEditedFieldType.APPLY_TAX.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

                    }
                    AddressDetailData previousAddress=location.getLocationAddress();
                    maintainEditedAddressDetail(vendorEditedDetail ,previousAddress,newAddress,VendorEditedFieldType.DISPATCH_ADDRESS.name(),VendorEditedType.DISPATCH_LOCATION.name(),vendorDetailData.getVendorId());

                    notExistingLocation = false;
                    break;
                }
            }

        }
        if (notExistingLocation && locationDetailData !=null) {
            if (locationDetailData.getLocationAddress() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.DISPATCH_ADDRESS.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

            }
            if (locationDetailData.getEmailId() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.EMAIL_ID.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

            }
            if (locationDetailData.getTIN() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.TIN.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

            }
            if (locationDetailData.getGSTIN() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.GSTIN.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

            }
            if (locationDetailData.getGstinDocument() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.GSTIN_DOCUMENT.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

            }
            if (locationDetailData.getApplyTax() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.APPLY_TAX.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

            }
            if (locationDetailData.getLocationName() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.LOCATION_NAME.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

            }
            if (locationDetailData.getNotificationType() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.NOTIFICATION.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());

            }
            if (locationDetailData.getGstStatus() != null) {
                updateEditedField(vendorEditedDetail, VendorEditedFieldType.GST_STATUS.name(), VendorEditedType.DISPATCH_LOCATION.name(), vendorDetailData.getVendorId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public VendorDetail saveDispatchLocations(Set<VendorDispatchLocation> detailList,
                                              Set<Integer> removedLocations, Integer vendorId)
            throws VendorRegistrationException, SumoException {
        VendorDetail vendorDetail = null;
        VendorDetailData vendorData = registrationDao.find(VendorDetailData.class, vendorId);

        Set<VendorDispatchLocationDetailData> locationDetailDataSet = new HashSet<>();

        if(removedLocations!=null){
            for (Integer dispatchId : removedLocations) {
                VendorDispatchLocationDetailData locationDetailData = registrationDao.find(VendorDispatchLocationDetailData.class, dispatchId);
                if(locationDetailData!=null){
                    locationDetailData.setStatus(SwitchStatus.IN_ACTIVE.name());
                    registrationDao.update(locationDetailData,false);
                }
            }
        }

        for (VendorDispatchLocation detail : detailList) {
            VendorDispatchLocationDetailData locationDetailData = SCMDataConverter.convert(detail, null);
            locationDetailData.setVendorDetail(vendorData);
            AddressDetailData addressDetailData = SCMDataConverter.convert(detail.getAddress());

            List<VendorEditedDetail> vendorEditedDetail=registrationDao.findVendorEditedDetailByVendorId(vendorId);
            maintainEditedDataForDispatchLocation(vendorEditedDetail,locationDetailData,vendorData,detail.getAddress());

            addressDetailData = addressDetailData.getAddressId() != null
                    ? registrationDao.update(addressDetailData, false) : registrationDao.add(addressDetailData, true);
            if (addressDetailData.getAddressId() != null) {
                locationDetailData.setLocationAddress(addressDetailData);
            }
            locationDetailData = locationDetailData.getDispatchLocationId() != null
                    ? registrationDao.update(locationDetailData, false)
                    : registrationDao.add(locationDetailData, false);
            if (locationDetailData.getDispatchLocationId() != null) {
                locationDetailDataSet.add(locationDetailData);
            }

        }

        if (!locationDetailDataSet.isEmpty()) {

            vendorData.setDispatchLocations(locationDetailDataSet);
            vendorData = registrationDao.update(vendorData, false);
            changeVendorDetailStatus(vendorData);
            String updatedBy = masterDataCache.getEmployee(vendorData.getUpdatedBy());
            String requestedBy = masterDataCache.getEmployee(vendorData.getRequestedBy());
            vendorDetail = scmMetadataService.addVendorToCache(vendorData);
        }

        registrationDao.flush();
        return vendorDetail;
    }

   private void resetVendorEditedData( List<VendorEditedDetail> vendorEditedDetailList)
   {
     if(vendorEditedDetailList!=null)
     {
         for(VendorEditedDetail vendorEditedDetail :vendorEditedDetailList)
         {
             vendorEditedDetail.setStatus(VendorEditedFieldStatus.APPROVED.name());
             registrationDao.update(vendorEditedDetail,true);
         }
     }

   }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean approveVendor(int creditCycle, int requestId, int userId, int leadTime) {
        boolean flag = false;
        try {

            VendorRegistrationRequestDetail requestDetail = registrationDao.find(VendorRegistrationRequestDetail.class,
                    requestId);
            if (requestDetail != null && requestDetail.getVendorId() != null
                    && requestDetail.getRequestStatus().equals(VendorStatus.COMPLETED.name())) {
                boolean isOld = vendorManagementDao.checkIfVendorApprovedAtleastOnce(requestDetail.getVendorId());
                VendorDetailData vendor = registrationDao.find(VendorDetailData.class, requestDetail.getVendorId());
                if (vendor != null && vendor.getStatus().equals(VendorStatus.COMPLETED.name())) {
                    VendorCompanyDetailData company = vendor.getCompanyDetails();
                    if (company != null) {
                        company.setCreditDays(creditCycle);
                        company = registrationDao.update(company, true);
                        vendor.setCompanyDetails(company);
                        vendor.setUpdatedBy(userId);
                        vendor.setUpdatedAt(AppUtils.getCurrentTimestamp());
                        vendor.setLeadTime(leadTime);
                        // activate vendor with credit cycle updated
                        vendor.setStatus(VendorStatus.ACTIVE.name());
                        registrationDao.update(vendor, true);
                        // request is approved
                        List<VendorEditedDetail> vendorEditedDetailList=registrationDao.findVendorEditedDetailByVendorId(vendor.getVendorId());
                        resetVendorEditedData(vendorEditedDetailList);
                        requestDetail.setRequestStatus(VendorStatus.APPROVED.name());
                        registrationDao.update(requestDetail, true);
                        // Update SCM Cache after approval
                        String updatedBy = masterDataCache.getEmployee(vendor.getUpdatedBy());
                        VendorLogData vendorLogData=new VendorLogData();
                        updateVendorLogData(vendorLogData,vendor,updatedBy);
                        String requestedBy = masterDataCache.getEmployee(vendor.getRequestedBy());
                        VendorDetail vendorDetail = SCMDataConverter.convertVendor(vendor, updatedBy, requestedBy);
                        scmCache.getVendorDetails().put(vendor.getVendorId(), vendorDetail);
                        flag = true;

                        notificationService.sendVendorApprovalNotification(vendorDetail);

                        try {


                            List<String> internalEmail = new ArrayList<>();
                            internalEmail.add("<EMAIL>");
                            internalEmail.add("<EMAIL>");
//                            internalEmail.add(vendorDetail.getPrimaryEmail());
//                            internalEmail.add(vendorDetail.getSecondaryEmail());

                            VendorRegistrationCompleteNotificationTemplate template;
//			            saveAuditLog(Integer keyId , String keyType ,Integer changedBy , Object newObject , String changeType)
                            if (isOld) {

                                AuditChangeLog oldVendorData = auditChangeLogDao.findByKeyId(vendor.getVendorId(), AuditChangeLogTypes.VENDOR.value());
                                Map<String, Pair<Object, Object>> vendorDifference = scmMetadataService.findDiff((VendorDetail) oldVendorData.getNewObject(), vendorDetail);

                                scmMetadataService.saveAuditLog(requestDetail.getVendorId(), AuditChangeLogTypes.VENDOR.value(), userId, vendorDetail
                                        , AuditChangeLogTypes.UPDATE_ENTRY.value());
                                template = new VendorRegistrationCompleteNotificationTemplate(
                                        props.getBasePath(), vendorDetail, vendorDifference);

                            } else {
                                scmMetadataService.saveAuditLog(requestDetail.getVendorId(), AuditChangeLogTypes.VENDOR.value(), userId, vendorDetail
                                        , AuditChangeLogTypes.NEW_ENTRY.value());

                                template = new VendorRegistrationCompleteNotificationTemplate(
                                        props.getBasePath(), vendorDetail);

                            }
                            VendorRegistrationCompleteNotification notification = new VendorRegistrationCompleteNotification(
                                    template, props.getEnvType(), internalEmail, isOld);
                            try {
                                notification.sendEmail();
                            } catch (EmailGenerationException e) {
                                e.printStackTrace();
                            }

                        } catch (Exception e){
                            LOG.error(" Error saving Vendor Log :::::: Unable to send email :::: for Vendor :::: {} :::::",vendor.getVendorId(),e);
                        }

                    } else {
                        throw new DataUpdationException("Could not find company details for the request");
                    }
                } else {
                    throw new DataUpdationException("Not a valid request for approval since the status is different");
                }
            } else {
                throw new DataUpdationException("Not a valid request for approval");
            }
        } catch (Exception e) {
            LOG.error("Encountered error while approving vendor request :::: {}", requestId, e);
        }
        return flag;
    }

    private void changeVendorDetailStatus(VendorDetailData vendorData) throws VendorRegistrationException {
        if (vendorData != null && vendorData.getStatus() != null
                && vendorData.getStatus().equals(VendorStatus.IN_PROCESS.name())) {
            boolean vendorFlag = vendorData.getVendorId() != null;
            boolean companyFlag = vendorData.getCompanyDetails() != null
                    && vendorData.getCompanyDetails().getCompanyDetailId() != null;
            boolean accountFlag = vendorData.getType().equals(VendorType.CUSTOMER.name())
                    || (vendorData.getAccountDetails() != null
                    && vendorData.getAccountDetails().getAccountDetailId() != null);
            boolean locations = vendorData.getDispatchLocations() != null
                    && !vendorData.getDispatchLocations().isEmpty();

            if (vendorFlag && companyFlag && accountFlag && locations) {
                vendorData.setStatus(VendorStatus.COMPLETED.name());
                VendorRegistrationRequestDetail vendorRequest = registrationDao
                        .getActiveVendorRequest(vendorData.getVendorId());
                vendorRequest.setRequestStatus(VendorStatus.COMPLETED.name());
                registrationDao.update(vendorRequest, false);
                registrationDao.update(vendorData, false);
                registrationDao.flush();
                notificationService.sendVendorCompletionNotification(vendorRequest);
            }
        }
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.SCMVendorManagementService#getAllVendor(
     * java.lang.String)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDetail> getAllVendor(String requestStatus, boolean getAccount) {
        return convert(vendorManagementDao.getAllVendor(requestStatus), requestStatus, getAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDetail> getAllVendorFromCache(String requestStatus, boolean getAccount) {
        return getVendorsByStatus(requestStatus,getAccount);
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDetail> getAllShortVendor(String requestStatus, boolean getAccount) { // short converter
        return trimVendor(getVendorsByStatus(requestStatus,getAccount));
    }

    private List<VendorDetail> getVendorsByStatus(String status , Boolean getAccount){
       return  scmCache.getVendorDetails().values().stream().filter(vendorDetail ->status == null ||
               vendorDetail.getStatus().name().equals(status)).map(vendorDetail -> {
                   if(Boolean.FALSE.equals(getAccount)){
                     vendorDetail.setAccountDetails(null);
                   }
                   return vendorDetail;
       }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public VendorDetail getShortVendor(Integer vendorId) {
        VendorDetailData vendorDetail = vendorManagementDao.find(VendorDetailData.class, vendorId);// short converter
        VendorDetail detail = SCMDataConverter.convertVendor(vendorDetail, masterDataCache.getEmployee(vendorDetail.getUpdatedBy()),
            masterDataCache.getEmployee(vendorDetail.getRequestedBy()));
        if (Objects.nonNull(vendorDetail.getLastBlockedBy())) {
            detail.setLastBlockedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(vendorDetail.getLastBlockedBy()), vendorDetail.getLastBlockedBy()));
        }
        if (Objects.nonNull(vendorDetail.getLastBlockedDate())) {
            detail.setLastBlockedDate(vendorDetail.getLastBlockedDate());
        }
        if (Objects.nonNull(vendorDetail.getLastUnBlockedDate())) {
            detail.setLastUnBlockedDate(vendorDetail.getLastUnBlockedDate());
        }
        if (Objects.nonNull(vendorDetail.getLastUnBlockedBy())) {
            detail.setLastUnBlockedBy(SCMUtil.getCreatedBy(masterDataCache.getEmployee(vendorDetail.getLastUnBlockedBy()), vendorDetail.getLastUnBlockedBy()));
        }
        return detail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDetail> getAllVendor(String requestStatus, boolean getAccount, Integer companyId) {
        return convert(vendorManagementDao.getAllVendor(requestStatus), requestStatus, getAccount);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDetail> getAllVendors() {
        return convert(vendorManagementDao.getAllVendor(), null, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDebitBalanceVO> uploadDebitBalanceSheet(MultipartFile file) throws IOException, SumoException {
        InputStream excelFile = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(excelFile);
        Sheet dataTypeSheet = workbook.getSheetAt(0);
        Row headerRow = dataTypeSheet.getRow(0);
        if (headerRow.getCell(0).getStringCellValue().equals("Vendor Id") &&
                headerRow.getCell(1).getStringCellValue().equals("Vendor Name") &&
                headerRow.getCell(2).getStringCellValue().equals("Debit Balance") &&
                headerRow.getCell(3).getStringCellValue().equals("Company Id") &&
                headerRow.getCell(4).getStringCellValue().equals("Company Name")) {
            List<VendorDebitBalanceVO> vendorDebitBalanceVOS = new ArrayList<>();
            for (int i = 1; i <= dataTypeSheet.getLastRowNum(); i++) {
                Row row = dataTypeSheet.getRow(i);
                VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class,
                        new Double(row.getCell(0).getNumericCellValue()).intValue());
                if (vendorDetailData != null) {
                    Cell cell2 = row.getCell(2);
                    BigDecimal balance = (cell2.getCellType().equals(1)) ? BigDecimal.ZERO : new BigDecimal(row.getCell(2).getNumericCellValue());
                    VendorDebitBalanceVO vendorDebitBalanceVO = new VendorDebitBalanceVO(vendorDetailData.getVendorId(),
                            row.getCell(1).getStringCellValue(), balance, row.getCell(4).getStringCellValue(), (int) row.getCell(3).getNumericCellValue());
                    vendorDebitBalanceVOS.add(vendorDebitBalanceVO);
                }
            }
            return vendorDebitBalanceVOS;
        } else {
            throw new SumoException("Sheet is not valid");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean uploadTDSMailingSheet(
            MimeType mimeType, String emails, String financialYear,
            String vendorName, String panNumber, String quarter,
            MultipartFile receivedFile) throws IOException, SumoException {

        String[] vendorEmailList;
        if(Objects.nonNull(emails)) {
            vendorEmailList = emails.split(",");
        }else {
            vendorEmailList = new String[]{};
        }

        try{
            File tdsCertificateFile = fileArchiveService.convertFromMultiPart(receivedFile.getOriginalFilename(), receivedFile);
            notificationService.sendTDSCertificateToVendor(vendorName, quarter, financialYear, vendorEmailList,
                    tdsCertificateFile);
            String fileName = receivedFile.getOriginalFilename();
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "Vendor_TDS_Certificates", fileName, receivedFile);

            VendorTdsCertificateSentData dbObject = new VendorTdsCertificateSentData();
            dbObject.setVendorName(vendorName);
            dbObject.setQuarter(quarter);
            dbObject.setFinancialYear(financialYear);
            dbObject.setVendorEmailAddress(emails);
            dbObject.setPanNumber(panNumber);
//            dbObject.setUploadPath(props.getS3Bucket() + "/" + "Vendor_TDS_Certificates" + "/" + fileName);
            dbObject.setUploadPath(s3File.getUrl());
            dbObject.setSendTime(SCMUtil.getCurrentTimestamp());
            vendorManagementDao.add(dbObject, true);
            LOG.info("Sending TDS certificate to {} {} {} {} {}", emails, financialYear, vendorName, panNumber, quarter);
        }catch (Exception e) {
            LOG.info("Error {}", e);
            throw new SumoException("Exception while sending mail");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveDebitBalances(List<VendorDebitBalanceVO> vendorDebitBalanceVOS) throws SumoException {
        for (VendorDebitBalanceVO vendorDebitBalanceVO : vendorDebitBalanceVOS) {
            if (vendorDebitBalanceVO.getVendorId() != null && vendorDebitBalanceVO.getDebitBalance() != null) {
                VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class,
                        vendorDebitBalanceVO.getVendorId());
                if (vendorDetailData != null) {
                    VendorCompanyDebitMapping mapping = null;
                    for (VendorCompanyDebitMapping vMapping : vendorDetailData.getDebitMappings()) {
                        if (vMapping.getCompanyId() == vendorDebitBalanceVO.getCompanyId()) {
                            mapping = vMapping;
                        }
                    }
                    if (mapping == null) {
                        VendorCompanyDebitMapping debitMapping = SCMDataConverter.convert(vendorDebitBalanceVO,
                                vendorDetailData);
                        debitMapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                        vendorManagementDao.add(debitMapping, true);
                    } else {
                        mapping.setDebitBalance(vendorDebitBalanceVO.getDebitBalance());
                        mapping.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                        mapping = vendorManagementDao.update(mapping, true);
                        if (mapping == null) {
                            throw new SumoException(
                                    "Error updating debit balance for vendor: " + vendorDebitBalanceVO.getVendorId()
                                            + " : " + vendorDebitBalanceVO.getEntityName());
                        }
                    }
                }
            }
        }
        return true;
    }

    private List<VendorDetail> convert(List<VendorDetailData> list, String requestStatus, boolean getAccount) {
        if (list == null) {
            return new ArrayList<>();
        }
        Map<Integer, String> regLinkMap = new HashMap<>();
        if (requestStatus == null || (VendorStatus.IN_PROCESS.name().equals(requestStatus)
                || VendorStatus.INITIATED.name().equals(requestStatus))) {
            List<String> statues = Arrays.asList(VendorStatus.IN_PROCESS.name(), VendorStatus.INITIATED.name());
            List<VendorRegistrationRequest> data = registrationDao.getAllVendorRegistrationRequest(statues);
            if (data != null) {
                data.forEach(o -> regLinkMap.put(o.getVendorId(), o.getLink()));
            }
        }
        return list.stream().map(o -> {
            String updatedBy = masterDataCache.getEmployee(o.getUpdatedBy());
            String requestedBy = masterDataCache.getEmployee(o.getRequestedBy());
            VendorDetail detail = SCMDataConverter.convertVendor(o, updatedBy, requestedBy);
            VendorDetail detailTrimmed = detail.trim(); // to trim down the object structure
            if (getAccount && detail.getAccountDetails() != null) {
                detailTrimmed.setAccountDetails(detail.getAccountDetails());
            }
            detailTrimmed.setLink(regLinkMap.get(detailTrimmed.getVendorId()));
            return detailTrimmed;
        }).collect(Collectors.toList());
    }



    private List<VendorDetail> convertShort(List<VendorDetailData> list, String requestStatus, boolean getAccount) {
        if (list == null) {
            return new ArrayList<>();
        }
        Map<Integer, String> regLinkMap = new HashMap<>();
        if (requestStatus == null || (VendorStatus.IN_PROCESS.name().equals(requestStatus)
                || VendorStatus.INITIATED.name().equals(requestStatus))) {
            List<String> statues = Arrays.asList(VendorStatus.IN_PROCESS.name(), VendorStatus.INITIATED.name());
            List<VendorRegistrationRequest> data = registrationDao.getAllVendorRegistrationRequest(statues);
            if (data != null) {
                data.forEach(o -> regLinkMap.put(o.getVendorId(), o.getLink()));
            }
        }
        return list.stream().map(o -> {
            String updatedBy = masterDataCache.getEmployee(o.getUpdatedBy());
            String requestedBy = masterDataCache.getEmployee(o.getRequestedBy());
            VendorDetail detail = SCMDataConverter.convertShortVendor(o, updatedBy, requestedBy, masterDataCache);
//            VendorDetail detailTrimmed = detail.trim(); // to trim down the object structure
//            if (getAccount && detail.getAccountDetails() != null) {
//                detailTrimmed.setAccountDetails(detail.getAccountDetails());
//            }
//            detailTrimmed.setLink(regLinkMap.get(detailTrimmed.getVendorId()));
            return detail;
        }).collect(Collectors.toList());
    }


    private List<VendorDetail> trimVendor(List<VendorDetail> list) {
        if (list == null) {
            return new ArrayList<>();
        }
        return list.stream().map(o -> {
            String updatedBy = masterDataCache.getEmployee(o.getUpdatedBy().getId());
            String requestedBy = masterDataCache.getEmployee(o.getRequestedBy().getId());
            VendorDetail detail = SCMDataConverter.convertShortVendor(o, updatedBy, requestedBy, masterDataCache);
            return detail;
        }).collect(Collectors.toList());
    }



    @Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateCreditCycle(Integer companyId, Integer creditCycle) {
		VendorCompanyDetailData companyDetailData = vendorManagementDao.find(VendorCompanyDetailData.class, companyId);
		companyDetailData.setCreditDays(creditCycle);
		vendorManagementDao.update(companyDetailData, true);
        VendorDetail vendorDetail = scmCache.getVendorDetail(companyDetailData.getVendorDetail().getVendorId());
        vendorDetail.setCreditDays(companyDetailData.getCreditDays());
        scmCache.getVendorDetails().put(companyDetailData.getVendorDetail().getVendorId(), vendorDetail);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean checkDuplicatePanCheck(String vendorType, String vendorPan) {
		return registrationDao.findDuplicatePanVendor(vendorType,vendorPan);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean checkDuplicateVendorName(String vendorName,String vendorType, String city , String state) {
        return registrationDao.findDuplicateVendorName(vendorName,vendorType,city,state);
    }



	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String checkVendorDeactivateStatus(Integer vendorId) {
		String validationData = registrationDao.findVendorStatus(vendorId);
		if(validationData == null || validationData.equalsIgnoreCase("")) {
	        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
	        if (vendorDetailData != null) {
	            vendorDetailData.setStatus(VendorStatus.PENDING_APPROVAL_FOR_DEACTIVATION.name());
	            vendorDetailData = vendorManagementDao.update(vendorDetailData, true);

	            if (vendorDetailData != null) {
	                String updatedBy = masterDataCache.getEmployee(vendorDetailData.getUpdatedBy());
                    VendorLogData vendorLogData=new VendorLogData();
                    updateVendorLogData(vendorLogData,vendorDetailData,updatedBy);
	                String requestedBy = masterDataCache.getEmployee(vendorDetailData.getRequestedBy());
	                scmCache.getVendorDetails().put(vendorDetailData.getVendorId(),
	                        SCMDataConverter.convertVendor(vendorDetailData, updatedBy, requestedBy));
	            }
	        }
		}
		return validationData;
	}

	@Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean reactivateVendor(Integer vendorId) {
        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
        if (vendorDetailData != null) {
            vendorDetailData.setStatus(VendorStatus.COMPLETED.name());
            vendorDetailData = vendorManagementDao.update(vendorDetailData, true);
            if (vendorDetailData != null) {
                String updatedBy = masterDataCache.getEmployee(vendorDetailData.getUpdatedBy());
                VendorLogData vendorLogData=new VendorLogData();
                updateVendorLogData(vendorLogData,vendorDetailData,updatedBy);
                String requestedBy = masterDataCache.getEmployee(vendorDetailData.getRequestedBy());
                scmCache.getVendorDetails().put(vendorDetailData.getVendorId(),
                        SCMDataConverter.convertVendor(vendorDetailData, updatedBy, requestedBy));
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveLeadTime(int vendorId,int leadTime) {
        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
        if (vendorDetailData != null) {
            vendorDetailData.setLeadTime(leadTime);
            vendorDetailData = vendorManagementDao.update(vendorDetailData, false);
            mappingService.updateSkuLeadTime(vendorId, leadTime);
            scmCache.getVendorDetail(vendorId).setLeadTime(leadTime);
          return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void downloadFile(HttpServletResponse response,  DocumentDetail document) throws IOException{
        FileDetail fileDetail = new FileDetail(document.getS3Bucket(),document.getS3Key(),
                document.getFileUrl());
        File file = fileArchiveService.getFileFromS3(props.getBasePath()+File.separator+"s3", fileDetail);
        if (file!=null) {
            response.setContentType(document.getMimeType().value());
            response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
            byte[] bytesArray = new byte[(int) file.length()];
            response.setContentLength(bytesArray.length);
            try {
                OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = new FileInputStream(file);
                int counter = 0;
                while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                    outputStream.write(bytesArray, 0, counter);
                    outputStream.flush();
                }
                outputStream.close();
                inputStream.close();
            } catch (IOException e) {
                LOG.error("Encountered error while writing file to response stream",e);
                throw e;
            } finally {
                response.getOutputStream().flush();
                file.delete(); // delete the temporary file created after completing request
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public DocumentDetail getDocumentDetailById(Integer documentId){
        DocumentDetailData documentDetailData = vendorManagementDao.find(DocumentDetailData.class,documentId);
        DocumentDetail documentDetail = SCMDataConverter.convert(documentDetailData);
        return  documentDetail;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceHistoryObject> getPriceHistory(SkuPriceDataObject reqObj){
        List<SkuPriceHistoryObject> skuPriceHistory = new ArrayList<>();
        List<Integer> skuPriceDataId = vendorManagementDao.getSkuPriceDataId(reqObj);
        List<Pair<Integer,Integer>> skuPackagingId = vendorManagementDao.getskuPackagingId(reqObj);
        Map<Integer,Integer> skuPackagingIdMap = skuPackagingId.stream().collect(Collectors.toMap(Pair::getKey,Pair::getValue,(k1, k2)->{
            return k1;
        }));
        if(!skuPriceDataId.isEmpty()) {
            skuPriceHistory = vendorManagementDao.getPriceHistory(skuPriceDataId);

        }
        for(SkuPriceHistoryObject object : skuPriceHistory){
            object.setPackagingId(skuPackagingIdMap.get(object.getSkuPriceDataId()));
        }
        return skuPriceHistory;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setVendorEditRequest(Integer userId,Integer vendorId, Object vendorChangeRequest) throws Exception {
        try {
            Map<String, Object> map = (HashMap<String, Object>) vendorChangeRequest;
            String[] nestedTableMap = {"companyDetails", "accountDetails", "vendorEditedData"};
            String[] dispatchLocationTableMap ={"dispatchLocations"};

            VendorDetailChangeRequest vendorDetailChangeRequest = new VendorDetailChangeRequest();
            vendorDetailChangeRequest.setVendorId(vendorId);
            vendorDetailChangeRequest = vendorManagementDao.add(vendorDetailChangeRequest, true);

            VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
            if(Objects.nonNull(vendorDetailData)){
                vendorDetailData.setChangeRequestId(vendorDetailChangeRequest.getVendorDetailChangeRequestId());
            }else {
                throw new SumoException("No Vendor found for vendorId : {} ", String.valueOf(vendorId));
            }
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if(Objects.nonNull(entry.getValue())){
                    if (ArrayUtils.contains(nestedTableMap, entry.getKey()) || ArrayUtils.contains(dispatchLocationTableMap,entry.getKey())) {
                        if(ArrayUtils.contains(dispatchLocationTableMap,entry.getKey())){
                            List<Map<String, Object>> locations = (List<Map<String, Object>>) entry.getValue();
                            for(Map<String, Object> location : locations) {
                                for(Map.Entry<String,Object> locationDetail :location.entrySet()){
                                    VendorEditedType vendorEditedType =VendorEditedType.DISPATCH_LOCATION;
                                    convertEditRequest(vendorDetailChangeRequest,locationDetail,vendorEditedType,vendorId);
                                }
                            }
                        }else{
                            Map<String, Object> details = (HashMap<String, Object>) entry.getValue();
                            for(Map.Entry<String,Object> detail :details.entrySet()){
                                VendorEditedType vendorEditedType =null;
                                if(entry.getKey().equals("companyDetails")){
                                    vendorEditedType = VendorEditedType.COMPANY_DETAIL;
                                }else if(entry.getKey().equals("accountDetails")) {
                                    vendorEditedType = VendorEditedType.ACCOUNT_DETAIL;
                                }
                                if(Objects.nonNull(vendorEditedType)){
                                    convertEditRequest(vendorDetailChangeRequest,detail,vendorEditedType,vendorId);
                                }
                            }
                        }
                    } else {
                        convertEditRequest(vendorDetailChangeRequest,entry,VendorEditedType.PERSONAL_DETAIL,vendorId);
                    }
                }
            }
            return editVendorRequest(vendorId,userId);
        } catch (Exception e) {
            LOG.error(" Error making vendor detail change req for vendor id : {}", vendorId);
            LOG.error(e.getMessage());
            throw e;
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void convertEditRequest(VendorDetailChangeRequest vendorDetailChangeRequest, Map.Entry<String, Object> entry, VendorEditedType tableName, Integer vendorId) throws Exception {
        try{
            Map<String, String> field = (HashMap<String, String>) entry.getValue();
            if (Objects.nonNull(field.get("changesRequired"))) {
                boolean changesRequired = Boolean.parseBoolean(String.valueOf(field.get("changesRequired")));
                if (changesRequired) {
                    VendorDetailChangeRequestData vendorDetailChangeRequestData = new VendorDetailChangeRequestData();
                    vendorDetailChangeRequestData.setComment(field.get("comment"));
                    vendorDetailChangeRequestData.setVendorDetailChangeRequest(vendorDetailChangeRequest);
                    vendorDetailChangeRequestData.setVendorId(vendorId);
                    if(Objects.nonNull(field.get("dispatchId"))){
                        vendorDetailChangeRequestData.setDispatchId(Integer.valueOf(field.get("dispatchId")));
                    }
                    vendorDetailChangeRequestData.setTableName(String.valueOf(tableName));
                    vendorDetailChangeRequestData.setFieldName(entry.getKey());
                    vendorManagementDao.add(vendorDetailChangeRequestData,true);
                }
            }
        }catch (Exception e){
            LOG.error(" Error saving vendor detail chnage req for vendor id : {}", vendorId);
            LOG.error(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<VendorDetailRequestChanges> getRequestedChanges(Integer vendorId ) throws Exception {
        try{
            VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
            Integer changeRequestId = vendorDetailData.getChangeRequestId();
            VendorDetailChangeRequest vendorDetailChangeRequest = vendorManagementDao.find(VendorDetailChangeRequest.class, changeRequestId);
            List<VendorDetailChangeRequestData> vendorDetailChangeRequestDataList = vendorDetailChangeRequest.getVendorDetailChangeRequestDataList();
            List<VendorDetailRequestChanges> vendorDetailRequestChangesList = new ArrayList<>();
            for(VendorDetailChangeRequestData vendorDetailChangeRequestData : vendorDetailChangeRequestDataList){
                VendorDetailRequestChanges vendorDetailRequestChanges = new VendorDetailRequestChanges();
                vendorDetailRequestChanges.setTableName(vendorDetailChangeRequestData.getTableName());
                vendorDetailRequestChanges.setVendorId(vendorDetailChangeRequestData.getVendorId());
                vendorDetailRequestChanges.setFieldName(vendorDetailChangeRequestData.getFieldName());
                vendorDetailRequestChanges.setComment(vendorDetailChangeRequestData.getComment());
                vendorDetailRequestChanges.setDispatchId(vendorDetailChangeRequestData.getDispatchId());
                vendorDetailRequestChangesList.add(vendorDetailRequestChanges);

            }

            return  vendorDetailRequestChangesList;

        }catch (Exception e){
            LOG.error(" Error getting requested changes");
            LOG.error(e.getMessage());
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean checkVendorCompliance(List<Integer> vendorIds, List<String> complianceTypes, Boolean isForceRetry) {
        try {
            LOG.info("::: Checking vendor compliance :::");
            Gson gson = new Gson();
            List<VendorComplianceType> vendorComplianceTypes = new ArrayList<>();
            if (Objects.nonNull(complianceTypes)) {
                for (String complianceType : complianceTypes) {
                    vendorComplianceTypes.add(VendorComplianceType.valueOf(complianceType));
                }
            } else {
                vendorComplianceTypes.addAll(Arrays.asList(VendorComplianceType.values()));
            }

            List<VendorDetail> vendorDetails = new ArrayList<>();
            if (Objects.nonNull(vendorIds)) {
                for (Integer vendorId : vendorIds) {
                    if (Objects.nonNull(scmCache.getVendorDetail(vendorId))) {
                        vendorDetails.add(scmCache.getVendorDetail(vendorId));
                    }
                }
            } else {
                vendorDetails.addAll(scmCache.getVendorDetails().values());
            }
            Set<String> complianceKeys = new HashSet<>();
            boolean checkedVendorData = false;

            HttpPost httpPost = new HttpPost(props.getSandBoxBaseUrl() + "authenticate");
            httpPost.setHeader("x-api-key", props.getSandBoxXApiKey());
            httpPost.setHeader("x-api-secret", props.getSandBoxXSecretKey());
            httpPost.setHeader("x-api-version", props.getSandBoxXApiVersion());

            HttpResponse response = WebServiceHelper.postRequest(httpPost);

            SandBoxAuthResponse sandBoxAuthResponse = WebServiceHelper.convertResponseUsingSerializer(response, SandBoxAuthResponse.class);

            LOG.info("::: Got SandBoxAuthResponse :::");

            Map<Integer, Pair<Set<String>, String>> vendorWithKeys = new HashMap<>();
            vendorDetails.forEach(vendorDetail -> {
                if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getStatus()) && vendorDetail.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)) {
                    if (Objects.nonNull(vendorDetail.getCompanyDetails()) && Objects.nonNull(vendorDetail.getCompanyDetails().getPan()) && !vendorDetail.getCompanyDetails().getPan().equalsIgnoreCase("")) {
                        addVendorWithKeys(vendorDetail, vendorWithKeys, vendorDetail.getCompanyDetails().getPan(), "PAN");
                    }
                    if (Objects.nonNull(vendorDetail.getDispatchLocations())) {
                        for (VendorDispatchLocation vendorDispatchLocation : vendorDetail.getDispatchLocations()) {
                            if (Objects.nonNull(vendorDispatchLocation.getGstin()) && !vendorDispatchLocation.getGstin().equalsIgnoreCase("") && Objects.nonNull(vendorDispatchLocation.getStatus())
                                        && vendorDispatchLocation.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)) {
                                addVendorWithKeys(vendorDetail, vendorWithKeys, vendorDispatchLocation.getGstin(), "GSTIN");
                            }
                        }
                    }
                }
            });

            Map<String, Set<Integer>> complianceKeyWithVendors = new HashMap<>();

            for (VendorComplianceType complianceType : vendorComplianceTypes) {
                Set<String> uniqueKeys = new HashSet<>();
                Set<String> uniqueKeysToAdd = new HashSet<>();
                vendorDetails.forEach(vendorDetail -> {
                    if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getStatus()) && vendorDetail.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)) {
                        if (complianceType.equals(VendorComplianceType.SECTION_206)) {
                            if (Objects.nonNull(vendorDetail.getCompanyDetails()) && Objects.nonNull(vendorDetail.getCompanyDetails().getPan()) && !vendorDetail.getCompanyDetails().getPan().equalsIgnoreCase("")) {
                                complianceKeys.add(vendorDetail.getCompanyDetails().getPan());
                            }
                        } else {
                            for (VendorDispatchLocation vendorDispatchLocation : vendorDetail.getDispatchLocations()) {
                                if (Objects.nonNull(vendorDispatchLocation.getGstin()) && !vendorDispatchLocation.getGstin().equalsIgnoreCase("") && Objects.nonNull(vendorDispatchLocation.getStatus())
                                        && vendorDispatchLocation.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)) {
                                    complianceKeys.add(vendorDispatchLocation.getGstin());
                                }
                            }
                        }
                    }
                });

                List<VendorComplianceData> vendorComplianceDataList = vendorManagementDao.findVendorCompliancesByComplianceType(Collections.singletonList(complianceType.value()), complianceKeys);
                if (Objects.nonNull(isForceRetry) && isForceRetry) {
                    for (VendorComplianceData vendorComplianceData : vendorComplianceDataList) {
                        vendorComplianceData.setRetryApi(AppConstants.YES);
                        vendorComplianceData.setKeyId(null);
                        vendorComplianceData.setLoggedMessage(null);
                        vendorManagementDao.update(vendorComplianceData, true);
                    }
                    vendorComplianceDataList = vendorManagementDao.findVendorCompliancesByComplianceType(Collections.singletonList(complianceType.value()), complianceKeys);
                }
                Map<String, VendorComplianceData> vendorComplianceDataMap = new HashMap<>();
                for (VendorComplianceData vendorComplianceData : vendorComplianceDataList) {
                    String key = vendorComplianceData.getYear() + "_" + vendorComplianceData.getMonth() + "_" + vendorComplianceData.getComplianceKey();
                    vendorComplianceDataMap.put(key, vendorComplianceData);
                }
                vendorDetails.forEach(vendorDetail -> {
                    if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getStatus()) && vendorDetail.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)) {
                        if (complianceType.equals(VendorComplianceType.SECTION_206)) {
                            if (Objects.nonNull(vendorDetail.getCompanyDetails()) && Objects.nonNull(vendorDetail.getCompanyDetails().getPan()) && !vendorDetail.getCompanyDetails().getPan().equalsIgnoreCase("")) {
                                String key = AppUtils.getYear(AppUtils.getCurrentTimestamp()) + "_" + AppUtils.getMonth(AppUtils.getCurrentTimestamp()) + "_" + vendorDetail.getCompanyDetails().getPan();
                                if (vendorComplianceDataMap.containsKey(key)) {
                                    if (vendorComplianceDataMap.get(key).getRetryApi().equalsIgnoreCase(AppConstants.YES)) {
                                        uniqueKeys.add(vendorDetail.getCompanyDetails().getPan());
                                    }
                                } else {
                                    uniqueKeys.add(vendorDetail.getCompanyDetails().getPan());
                                    uniqueKeysToAdd.add(vendorDetail.getCompanyDetails().getPan());
                                }
                                complianceKeys.add(vendorDetail.getCompanyDetails().getPan());
                                addToComplianceKeyWithVendors(vendorDetail.getCompanyDetails().getPan(), vendorDetail, complianceKeyWithVendors);
                            }
                        } else {
                            for (VendorDispatchLocation vendorDispatchLocation : vendorDetail.getDispatchLocations()) {
                                if (Objects.nonNull(vendorDispatchLocation.getGstin()) && !vendorDispatchLocation.getGstin().equalsIgnoreCase("") && Objects.nonNull(vendorDispatchLocation.getStatus())
                                        && vendorDispatchLocation.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)) {
                                    String key = AppUtils.getYear(AppUtils.getCurrentTimestamp()) + "_" + AppUtils.getMonth(AppUtils.getCurrentTimestamp()) + "_" + vendorDispatchLocation.getGstin();
                                    if (vendorComplianceDataMap.containsKey(key)) {
                                        if (vendorComplianceDataMap.get(key).getRetryApi().equalsIgnoreCase(AppConstants.YES)) {
                                            uniqueKeys.add(vendorDispatchLocation.getGstin());
                                        }
                                    } else {
                                        uniqueKeys.add(vendorDispatchLocation.getGstin());
                                        uniqueKeysToAdd.add(vendorDispatchLocation.getGstin());
                                    }
                                    complianceKeys.add(vendorDispatchLocation.getGstin());
                                    addToComplianceKeyWithVendors(vendorDispatchLocation.getGstin(), vendorDetail, complianceKeyWithVendors);
                                }
                            }
                        }
                    }
                });

                if (!uniqueKeysToAdd.isEmpty()) {
                    addVendorComplianceDataForMonth(uniqueKeysToAdd, complianceType.value());
                }
                vendorComplianceDataList.clear();
                vendorComplianceDataList = vendorManagementDao.findVendorCompliancesByComplianceType(Collections.singletonList(complianceType.value()), complianceKeys);
                vendorComplianceDataMap.clear();
                for (VendorComplianceData vendorComplianceData : vendorComplianceDataList) {
                    String key = vendorComplianceData.getYear() + "_" + vendorComplianceData.getMonth() + "_" + vendorComplianceData.getComplianceKey();
                    vendorComplianceDataMap.put(key, vendorComplianceData);
                }
                for (String key : uniqueKeys) {
                    try {
                        URIBuilder builder = null;
                        switch (complianceType) {
                            case GSTIN_VERIFICATION:
                                builder = new URIBuilder(props.getSandBoxBaseUrl() + "gsp/public/gstin/" + key);
                                break;
                            case GST_RETURN:
                                String url = props.getSandBoxBaseUrl() + "gsp/public/gstr?gstin=" + key + "&financial_year=" + getVendorComplianceFinancialYer(true);
                                builder = new URIBuilder(url);
                                break;
                            case SECTION_206:
                                builder = new URIBuilder(props.getSandBoxBaseUrl() + "itd/reporting-portal/tds/206-ab/" + key);
                                break;
                        }
                        HttpGet httpGet = new HttpGet(builder.toString());
                        checkedVendorData = true;
                        httpGet.setHeader("x-api-key", props.getSandBoxXApiKey());
                        httpGet.setHeader("x-api-secret", props.getSandBoxXSecretKey());
                        httpGet.setHeader("x-api-version", props.getSandBoxXApiVersion());
                        httpGet.setHeader("Authorization", sandBoxAuthResponse.getAccessToken());
                        HttpResponse response1 = WebServiceHelper.getRequest(httpGet);
                        VendorComplianceDataAuditLog vendorComplianceDataAuditLog = WebServiceHelper.convertResponseUsingSerializer(response1, VendorComplianceDataAuditLog.class);
                        vendorComplianceDataAuditLog.setVendorComplianceDataAuditLogId(idService.getNextId(VendorComplianceDataAuditLog.class));
                        vendorComplianceDataAuditLog.setCurrentTime(AppUtils.getCurrentTimestamp());
                        vendorComplianceDataAuditLog.setCurrentTimeIST(AppUtils.getCurrentTimeISTString());
                        vendorComplianceDataAuditLog.setYear(AppUtils.getYear(AppUtils.getCurrentTimestamp()));
                        vendorComplianceDataAuditLog.setMonth(AppUtils.getMonth(AppUtils.getCurrentTimestamp()));
                        vendorComplianceDataAuditLog.setComplianceKey(key);
                        vendorComplianceDataAuditLog.setComplianceType(complianceType.value());
                        if (complianceType.equals(VendorComplianceType.GST_RETURN)) {
                            vendorComplianceDataAuditLog.setFinancialYear(getVendorComplianceFinancialYer(false));
                        }
                        LOG.info("JSOn of Audit Log is : {}", gson.toJson(vendorComplianceDataAuditLog));
                        vendorComplianceDataAuditLogDao.save(vendorComplianceDataAuditLog);
                        String checkKey = vendorComplianceDataAuditLog.getYear() + "_" + vendorComplianceDataAuditLog.getMonth() + "_" + vendorComplianceDataAuditLog.getComplianceKey();
                        if (Objects.nonNull(vendorComplianceDataAuditLog.getCode())) {
                            if (vendorComplianceDataAuditLog.getCode() == 200) {
                                if (vendorComplianceDataMap.containsKey(checkKey)) {
                                    VendorComplianceData vendorComplianceData = vendorComplianceDataMap.get(checkKey);
                                    vendorComplianceData.setRetryApi(AppConstants.NO);
                                    vendorComplianceData.setLastValidationTryAt(AppUtils.getCurrentTimestamp());
                                    vendorComplianceData.setKeyId(vendorComplianceDataAuditLog.getVendorComplianceDataAuditLogId());
                                    vendorManagementDao.update(vendorComplianceData, true);
                                    vendorComplianceDataMap.put(checkKey, vendorComplianceData);
                                }
                            }
                            if (vendorComplianceDataAuditLog.getCode() == 422) {
                                if (vendorComplianceDataMap.containsKey(checkKey)) {
                                    VendorComplianceData vendorComplianceData = vendorComplianceDataMap.get(checkKey);
                                    vendorComplianceData.setRetryApi(AppConstants.NO);
                                    vendorComplianceData.setLastValidationTryAt(AppUtils.getCurrentTimestamp());
                                    vendorManagementDao.update(vendorComplianceData, true);
                                    vendorComplianceDataMap.put(checkKey, vendorComplianceData);
                                }
                            }
                        }
                    } catch (Exception e) {
                        LOG.error("Exception while checking vendor Compliance ::: ", e);
                        VendorComplianceDataAuditLog vendorComplianceDataAuditLog = new VendorComplianceDataAuditLog();
                        vendorComplianceDataAuditLog.setVendorComplianceDataAuditLogId(idService.getNextId(VendorComplianceDataAuditLog.class));
                        vendorComplianceDataAuditLog.setCurrentTime(AppUtils.getCurrentTimestamp());
                        vendorComplianceDataAuditLog.setCurrentTimeIST(AppUtils.getCurrentTimeISTString());
                        vendorComplianceDataAuditLog.setYear(AppUtils.getYear(AppUtils.getCurrentTimestamp()));
                        vendorComplianceDataAuditLog.setMonth(AppUtils.getMonth(AppUtils.getCurrentTimestamp()));
                        vendorComplianceDataAuditLog.setComplianceKey(key);
                        vendorComplianceDataAuditLog.setComplianceType(complianceType.value());
                        if (complianceType.equals(VendorComplianceType.GST_RETURN)) {
                            vendorComplianceDataAuditLog.setFinancialYear(getVendorComplianceFinancialYer(false));
                        }
                        vendorComplianceDataAuditLog.setCode(999);
                        vendorComplianceDataAuditLog.setMessage(e.getMessage());
                        String checkKey = vendorComplianceDataAuditLog.getYear() + "_" + vendorComplianceDataAuditLog.getMonth() + "_" + vendorComplianceDataAuditLog.getComplianceKey();
                        if (Objects.nonNull(vendorComplianceDataAuditLog.getCode())) {
                            if (vendorComplianceDataMap.containsKey(checkKey)) {
                                VendorComplianceData vendorComplianceData = vendorComplianceDataMap.get(checkKey);
                                vendorComplianceData.setLastValidationTryAt(AppUtils.getCurrentTimestamp());
                                vendorManagementDao.update(vendorComplianceData, true);
                                vendorComplianceDataMap.put(checkKey, vendorComplianceData);
                            }
                        }
                        vendorComplianceDataAuditLogDao.save(vendorComplianceDataAuditLog);
                    }
                }
            }
            List<VendorComplianceData> vendorComplianceDataList = vendorManagementDao.findVendorCompliancesByComplianceType(getComplianceTypeValues(vendorComplianceTypes), complianceKeys);
            boolean isAllVerified = true;
            for (VendorComplianceData vendorComplianceData : vendorComplianceDataList) {
                if (vendorComplianceData.getRetryApi().equalsIgnoreCase(AppConstants.YES)) {
                    isAllVerified = false;
                    break;
                }
            }
            if ((isAllVerified && checkedVendorData) || (checkedVendorData && AppUtils.getDay(AppUtils.getCurrentTimestamp()) == 17) || Objects.nonNull(isForceRetry)) {
                LOG.info("Applying Vendor Validations :: ");
                List<VendorComplianceDataDTO> vendorComplianceDataDTOList = vendorManagementDao.getLatestCompliances();
                Map<String, Integer> complianceKeyWithLatestValidation = new HashMap<>();
                for (VendorComplianceDataDTO complianceDataDTO : vendorComplianceDataDTOList) {
                    String key = complianceDataDTO.getComplianceType() + "#" + complianceDataDTO.getComplianceKey();
                    if (complianceKeyWithLatestValidation.containsKey(key)) {
                        Integer currentValue = complianceKeyWithLatestValidation.get(key);
                        if (currentValue.compareTo(complianceDataDTO.getVendorComplianceId()) < 0) {
                            complianceKeyWithLatestValidation.put(key, complianceDataDTO.getVendorComplianceId());
                        }
                    } else {
                        complianceKeyWithLatestValidation.put(key, complianceDataDTO.getVendorComplianceId());
                    }
                }
                applyValidationsOnVendors(vendorComplianceDataList, complianceKeys, complianceKeyWithVendors, vendorComplianceTypes, complianceKeyWithLatestValidation, vendorWithKeys);
            }
            return true;
        } catch (Exception e) {
            LOG.error("Exception while checking vendor Compliance ::: ", e);
        }
        return false;
    }

    private void addVendorWithKeys(VendorDetail vendorDetail, Map<Integer, Pair<Set<String>, String>> vendorWithKeys, String complianceKey, String type) {
        Pair<Set<String>, String> keys;
        if (vendorWithKeys.containsKey(vendorDetail.getVendorId())) {
            keys = vendorWithKeys.get(vendorDetail.getVendorId());
        } else {
            keys = new Pair<>();
        }
        if (type.equalsIgnoreCase("GSTIN")) {
            Set<String> distinctKeys = new HashSet<>();
            distinctKeys.add(complianceKey);
            keys.setKey(distinctKeys);
        } else {
            keys.setValue(complianceKey);
        }
        vendorWithKeys.put(vendorDetail.getVendorId(), keys);
    }

    private void addToComplianceKeyWithVendors(String complianceKey, VendorDetail vendorDetail, Map<String, Set<Integer>> complianceKeyWithVendors) {
        Set<Integer> vendorIds;
        if (complianceKeyWithVendors.containsKey(complianceKey)) {
            vendorIds = complianceKeyWithVendors.get(complianceKey);
        } else {
            vendorIds = new HashSet<>();
        }
        vendorIds.add(vendorDetail.getVendorId());
        complianceKeyWithVendors.put(complianceKey, vendorIds);
    }

    private void applyValidationsOnVendors(List<VendorComplianceData> vendorComplianceDataList, Set<String> complianceKeys, Map<String, Set<Integer>> complianceKeyWithVendors,
                                           List<VendorComplianceType> vendorComplianceTypes, Map<String, Integer> complianceKeyWithLatestValidation, Map<Integer, Pair<Set<String>, String>> vendorWithKeys) throws SumoException {
        List<VendorComplianceDataAuditLog> vendorComplianceDataAuditLogs = vendorComplianceDataAuditLogDao.findVendorComplianceDataAuditLogsByMonthAndYear(
                AppUtils.getMonth(AppUtils.getCurrentTimestamp()), AppUtils.getYear(AppUtils.getCurrentTimestamp()), complianceKeys);
        LOG.info("Got {} entries from Mongo :: Started Applying Validations", vendorComplianceDataAuditLogs.size());
        Map<Integer, VendorComplianceDataAuditLog> vendorComplianceDataAuditLogMap = vendorComplianceDataAuditLogs.stream()
                .collect(Collectors.toMap(VendorComplianceDataAuditLog::getVendorComplianceDataAuditLogId, Function.identity()));
        Map<String, Map<String, List<VendorComplianceDataAuditLog>>> complianceMap = new HashMap<>();
        for (VendorComplianceDataAuditLog auditLog : vendorComplianceDataAuditLogs) {
            Map<String, List<VendorComplianceDataAuditLog>> innerMap;
            if (complianceMap.containsKey(auditLog.getComplianceType())) {
                innerMap = complianceMap.get(auditLog.getComplianceType());
                List<VendorComplianceDataAuditLog> logs;
                if (innerMap.containsKey(auditLog.getComplianceKey())) {
                    logs = innerMap.get(auditLog.getComplianceKey());
                } else {
                    logs = new ArrayList<>();
                }
                logs.add(auditLog);
                innerMap.put(auditLog.getComplianceKey(), logs);
            } else {
                innerMap = new HashMap<>();
                List<VendorComplianceDataAuditLog> logs = new ArrayList<>();
                logs.add(auditLog);
                innerMap.put(auditLog.getComplianceKey(), logs);
            }
            complianceMap.put(auditLog.getComplianceType(), innerMap);
        }

        Map<Integer, VendorComplianceValidations> complianceValidationsMap = new HashMap<>();

        Set<Integer> uniqueVendors = complianceKeyWithVendors.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());

        for (Integer vendorId : uniqueVendors) {
            complianceValidationsMap.put(vendorId, new VendorComplianceValidations());
        }

        for (VendorComplianceData vendorComplianceData : vendorComplianceDataList) {
            Map<String, List<VendorComplianceDataAuditLog>> logs = complianceMap.get(vendorComplianceData.getComplianceType());
            StringBuilder msg = new StringBuilder();
            if (Objects.nonNull(logs) || vendorComplianceDataAuditLogMap.containsKey(vendorComplianceData.getKeyId())) {
                List<VendorComplianceDataAuditLog> responses = null;
                if (Objects.isNull(vendorComplianceData.getKeyId())) {
                    responses = logs.get(vendorComplianceData.getComplianceKey());
                } else {
                    if (vendorComplianceDataAuditLogMap.containsKey(vendorComplianceData.getKeyId())) {
                        responses = Collections.singletonList(vendorComplianceDataAuditLogMap.get(vendorComplianceData.getKeyId()));
                    } else {
                        responses = logs.get(vendorComplianceData.getComplianceKey());
                    }
                }
                if (Objects.nonNull(responses)) {
                    for (VendorComplianceDataAuditLog log : responses) {
                        if (log.getCode() != 200) {
                            if (msg.toString().equalsIgnoreCase("")) {
                                msg.append(log.getMessage());
                            } else {
                                msg.append(",").append(log.getMessage());
                            }
                        } else {
                            if (Objects.nonNull(log.getData())) {
                                if (vendorComplianceData.getComplianceType().equalsIgnoreCase(VendorComplianceType.GSTIN_VERIFICATION.value())) {
                                    if (Objects.nonNull(log.getData().getGstinStatus())) {
                                        msg.append(log.getData().getGstinStatus());
                                    } else {
                                        if (Objects.nonNull(log.getData().getErrorCode()) || Objects.nonNull(log.getData().getMessage())) {
                                            msg = new StringBuilder();
                                            if (Objects.nonNull(log.getData().getErrorCode())) {
                                                msg.append(log.getData().getErrorCode()).append("-");
                                            }
                                            if (Objects.nonNull(log.getData().getMessage())) {
                                                msg.append(log.getData().getMessage());
                                            }
                                        }
                                    }
                                } else if (vendorComplianceData.getComplianceType().equalsIgnoreCase(VendorComplianceType.GST_RETURN.value())) {
                                    if (Objects.nonNull(log.getData().getEFiledlist()) && log.getData().getEFiledlist().size() > 0) {
                                        List<String> last3Months = getLast3Months();
                                        Map<String, Pair<Boolean, String>> last3MonthFilingStatus = new HashMap<>();
                                        for (String month : last3Months) {
                                            last3MonthFilingStatus.put(month, new Pair<>(Boolean.FALSE, null));
                                        }
                                        for (EFiledList eFiledList : log.getData().getEFiledlist()) {
                                            if (Objects.nonNull(eFiledList.getTaxPeriod()) && last3MonthFilingStatus.containsKey(eFiledList.getTaxPeriod())) {
                                                if (last3MonthFilingStatus.containsKey(eFiledList.getTaxPeriod())) {
                                                    Pair<Boolean, String> filingStatus = last3MonthFilingStatus.get(eFiledList.getTaxPeriod());
                                                    StringBuilder filingMsg = new StringBuilder();
                                                    filingMsg.append("Filed ").append(eFiledList.getReturnType())
                                                            .append(" For ").append(eFiledList.getTaxPeriod()).append(" On ")
                                                            .append(eFiledList.getDateOfFiling()).append(" Through ")
                                                            .append(eFiledList.getModeOfFiling()).append(" (ARN : ").append(eFiledList.getArnNumber()).append(" ) ");
                                                    if (eFiledList.getIsValid().equalsIgnoreCase(AppConstants.YES)) {
                                                        filingStatus.setKey(Boolean.TRUE);
                                                        filingMsg.append("- VALID");
                                                    } else {
                                                        filingStatus.setKey(Boolean.FALSE);
                                                        filingMsg.append("- NOT_VALID");
                                                    }
                                                    if (Objects.nonNull(filingStatus.getValue())) {
                                                        filingMsg.append(",").append(filingStatus.getValue());
                                                    }
                                                    filingStatus.setValue(filingMsg.toString());
                                                    last3MonthFilingStatus.put(eFiledList.getTaxPeriod(), filingStatus);
                                                }
                                            }
                                        }
                                        boolean last3MonthsFiled = true;
                                        StringBuilder filingMsg = new StringBuilder();
                                        for (Map.Entry<String, Pair<Boolean, String>> entry : last3MonthFilingStatus.entrySet()) {
                                            Pair<Boolean, String> statusOfFiling = entry.getValue();
                                            if (statusOfFiling.getKey().equals(Boolean.FALSE)) {
                                                last3MonthsFiled = false;
                                            }
                                            if (filingMsg.toString().equalsIgnoreCase("")) {
                                                if (Objects.nonNull(statusOfFiling.getValue())) {
                                                    filingMsg.append(statusOfFiling.getValue());
                                                } else {
                                                    filingMsg.append("NO FILING INFORMATION FOUND FOR ").append(entry.getKey());
                                                }
                                            } else {
                                                if (Objects.nonNull(statusOfFiling.getValue())) {
                                                    filingMsg.append(",").append(statusOfFiling.getValue());
                                                } else {
                                                    filingMsg.append(",").append("NO FILING INFORMATION FOUND FOR ").append(entry.getKey());
                                                }
                                            }
                                        }
                                        if (last3MonthsFiled) {
                                            msg.append("FILED - ").append(filingMsg.toString());
                                        } else {
                                            msg.append("NOT_FILED - ").append(filingMsg.toString());
                                        }
                                    } else {
                                        if (Objects.nonNull(log.getData().getErrorCode()) || Objects.nonNull(log.getData().getMessage())) {
                                            msg = new StringBuilder();
                                            if (Objects.nonNull(log.getData().getErrorCode())) {
                                                msg.append(log.getData().getErrorCode()).append("-");
                                            }
                                            if (Objects.nonNull(log.getData().getMessage())) {
                                                msg.append(log.getData().getMessage());
                                            }
                                        }
                                    }
                                } else {
                                    boolean notOperative = false;
                                    if (Objects.nonNull(log.getData().getPanStatus())) {
                                        if (!log.getData().getPanStatus().equalsIgnoreCase("Operative")) {
                                            notOperative = true;
                                            if (log.getData().getPanStatus().equalsIgnoreCase("-")) {
                                                msg.append("Inoperative").append(log.getData().getName()).append(" ( ").append(log.getData().getPan()).append(" )");
                                            } else {
                                                msg.append("Inoperative").append(" ( ").append(log.getData().getPan()).append(" Name : ")
                                                        .append(log.getData().getName()).append(" PAN Allotment Date : ").append(Objects.nonNull(log.getData().getPanAllotmentDate())
                                                                ? new Date(log.getData().getPanAllotmentDate()) : "-").append(" For Financial Year : ").append(Objects.nonNull(log.getData().getFinancialYear()) ?
                                                                log.getData().getFinancialYear() : "-").append(" specified_person_us_206ab_&_206cca : ").append(Objects.nonNull(log.getData().getSpecifiedPerson206ABAnd206CCA()) ?
                                                                log.getData().getSpecifiedPerson206ABAnd206CCA() : "-")
                                                        .append(" )");
                                            }
                                        }
                                    } else {
                                        notOperative = true;
                                        msg.append("NO_DATA_FOUND");
                                    }
                                    if (!notOperative) {
                                        if (Objects.nonNull(log.getData().getSpecifiedPerson206ABAnd206CCA())) {
                                            if (log.getData().getSpecifiedPerson206ABAnd206CCA().equalsIgnoreCase(AppConstants.YES)) {
                                                msg.append("DEDUCTION -");
                                                msg.append(log.getData().getPanStatus()).append(" ( ").append(log.getData().getPan()).append(" Name : ")
                                                        .append(log.getData().getName()).append(" PAN Allotment Date : ").append(Objects.nonNull(log.getData().getPanAllotmentDate())
                                                                ? new Date(log.getData().getPanAllotmentDate()) : "-").append(" For Financial Year : ").append(Objects.nonNull(log.getData().getFinancialYear()) ?
                                                                log.getData().getFinancialYear() : "-").append(" specified_person_us_206ab_&_206cca : ").append(Objects.nonNull(log.getData().getSpecifiedPerson206ABAnd206CCA()) ?
                                                                log.getData().getSpecifiedPerson206ABAnd206CCA() : "-")
                                                        .append(" )");
                                            } else {
                                                msg.append("NO_DEDUCTION -");
                                                msg.append(log.getData().getPanStatus()).append(" ( ").append(log.getData().getPan()).append(" Name : ")
                                                        .append(log.getData().getName()).append(" PAN Allotment Date : ").append(Objects.nonNull(log.getData().getPanAllotmentDate())
                                                                ? new Date(log.getData().getPanAllotmentDate()) : "-").append(" For Financial Year : ").append(Objects.nonNull(log.getData().getFinancialYear()) ?
                                                                log.getData().getFinancialYear() : "-").append(" specified_person_us_206ab_&_206cca : ").append(Objects.nonNull(log.getData().getSpecifiedPerson206ABAnd206CCA()) ?
                                                                log.getData().getSpecifiedPerson206ABAnd206CCA() : "-")
                                                        .append(" )");
                                            }
                                        } else {
                                            msg.append("NO_DATA_FOUND");
                                        }
                                    }
                                }
                            } else {
                                msg = new StringBuilder();
                                msg.append("NO_DATA_FOUND");
                            }
                        }
                    }
                } else {
                    msg.append("NO_LOGS_FOUND");
                }
            }
            Set<Integer> vendorsWithCurrentKey = complianceKeyWithVendors.get(vendorComplianceData.getComplianceKey());
            if (Objects.nonNull(vendorsWithCurrentKey)) {
                for (Integer vendor : vendorsWithCurrentKey) {
                    VendorComplianceValidations complianceValidations = complianceValidationsMap.get(vendor);
                    if (Objects.nonNull(complianceValidations)) {
                        if (vendorComplianceData.getComplianceType().equalsIgnoreCase(VendorComplianceType.GSTIN_VERIFICATION.value())) {
                            Map<String, String> stringStringMap = null;
                            if (Objects.nonNull(complianceValidations.getGstInValidation())) {
                                stringStringMap = complianceValidations.getGstInValidation();
                            } else {
                                stringStringMap = new HashMap<>();
                            }
                            stringStringMap.put(vendorComplianceData.getComplianceKey(), msg.toString());
                            complianceValidations.setGstInValidation(stringStringMap);
                        } else if (vendorComplianceData.getComplianceType().equalsIgnoreCase(VendorComplianceType.GST_RETURN.value())) {
                            Map<String, String> stringStringMap = null;
                            if (Objects.nonNull(complianceValidations.getGstReturnValidation())) {
                                stringStringMap = complianceValidations.getGstReturnValidation();
                            } else {
                                stringStringMap = new HashMap<>();
                            }
                            stringStringMap.put(vendorComplianceData.getComplianceKey(), msg.toString());
                            complianceValidations.setGstReturnValidation(stringStringMap);
                        } else {
                            Map<String, String> stringStringMap = null;
                            if (Objects.nonNull(complianceValidations.getSection206Validation())) {
                                stringStringMap = complianceValidations.getSection206Validation();
                            } else {
                                stringStringMap = new HashMap<>();
                            }
                            stringStringMap.put(vendorComplianceData.getComplianceKey(), msg.toString());
                            complianceValidations.setSection206Validation(stringStringMap);
                        }
                    }
                }
            }
            vendorComplianceData.setLoggedMessage(msg.toString());
            vendorManagementDao.update(vendorComplianceData, true);
        }
        for (VendorComplianceType vendorComplianceType : VendorComplianceType.values()) {
            for (Integer vendor : uniqueVendors) {
                Pair<Set<String>, String> keyType = vendorWithKeys.get(vendor);
                Set<String> currentKey = null;
                if (Objects.nonNull(keyType)) {
                    if (vendorComplianceType.value().equalsIgnoreCase(VendorComplianceType.SECTION_206.value())) {
                        Set<String> stringSet = new HashSet<>();
                        stringSet.add(keyType.getValue());
                        currentKey = stringSet;
                    } else {
                        currentKey = keyType.getKey();
                    }
                    VendorComplianceValidations complianceValidations = complianceValidationsMap.get(vendor);
                    if (Objects.nonNull(complianceValidations)) {
                        if (vendorComplianceType.value().equalsIgnoreCase(VendorComplianceType.GSTIN_VERIFICATION.value())) {
                            if (Objects.isNull(complianceValidations.getGstInValidation())) {
                                if (Objects.nonNull(currentKey)) {
                                    for (String innerKey : currentKey) {
                                        String key = vendorComplianceType.value() + "#" + innerKey;
                                        if (complianceKeyWithLatestValidation.containsKey(key)) {
                                            VendorComplianceData vendorComplianceData = vendorManagementDao.find(VendorComplianceData.class, complianceKeyWithLatestValidation.get(key));
                                            Map<String, String> stringStringMap = null;
                                            if (Objects.nonNull(complianceValidations.getGstInValidation())) {
                                                stringStringMap = complianceValidations.getGstInValidation();
                                            } else {
                                                stringStringMap = new HashMap<>();
                                            }
                                            stringStringMap.put(vendorComplianceData.getComplianceKey(), vendorComplianceData.getLoggedMessage());
                                            complianceValidations.setGstInValidation(stringStringMap);
                                        } else {
                                            Map<String, String> stringStringMap = null;
                                            if (Objects.nonNull(complianceValidations.getGstInValidation())) {
                                                stringStringMap = complianceValidations.getGstInValidation();
                                            } else {
                                                stringStringMap = new HashMap<>();
                                            }
                                            stringStringMap.put(innerKey, "NO_DATA_FOUND");
                                            complianceValidations.setGstInValidation(stringStringMap);
                                        }
                                    }
                                }
                            }
                        } else if (vendorComplianceType.value().equalsIgnoreCase(VendorComplianceType.GST_RETURN.value())) {
                            if (Objects.isNull(complianceValidations.getGstReturnValidation())) {
                                if (Objects.nonNull(currentKey)) {
                                    for (String innerKey : currentKey) {
                                        String key = vendorComplianceType.value() + "#" + innerKey;
                                        if (complianceKeyWithLatestValidation.containsKey(key)) {
                                            VendorComplianceData vendorComplianceData = vendorManagementDao.find(VendorComplianceData.class, complianceKeyWithLatestValidation.get(key));
                                            Map<String, String> stringStringMap = null;
                                            if (Objects.nonNull(complianceValidations.getGstReturnValidation())) {
                                                stringStringMap = complianceValidations.getGstReturnValidation();
                                            } else {
                                                stringStringMap = new HashMap<>();
                                            }
                                            stringStringMap.put(vendorComplianceData.getComplianceKey(), vendorComplianceData.getLoggedMessage());
                                            complianceValidations.setGstReturnValidation(stringStringMap);
                                        } else {
                                            Map<String, String> stringStringMap = null;
                                            if (Objects.nonNull(complianceValidations.getGstReturnValidation())) {
                                                stringStringMap = complianceValidations.getGstReturnValidation();
                                            } else {
                                                stringStringMap = new HashMap<>();
                                            }
                                            stringStringMap.put(innerKey, "NO_DATA_FOUND");
                                            complianceValidations.setGstReturnValidation(stringStringMap);
                                        }
                                    }
                                }
                            }
                        } else {
                            if (Objects.isNull(complianceValidations.getSection206Validation())) {
                                if (Objects.nonNull(currentKey)) {
                                    for (String innerKey : currentKey) {
                                        String key = vendorComplianceType.value() + "#" + innerKey;
                                        if (complianceKeyWithLatestValidation.containsKey(key)) {
                                            VendorComplianceData vendorComplianceData = vendorManagementDao.find(VendorComplianceData.class, complianceKeyWithLatestValidation.get(key));
                                            Map<String, String> stringStringMap = null;
                                            if (Objects.nonNull(complianceValidations.getSection206Validation())) {
                                                stringStringMap = complianceValidations.getSection206Validation();
                                            } else {
                                                stringStringMap = new HashMap<>();
                                            }
                                            stringStringMap.put(vendorComplianceData.getComplianceKey(), vendorComplianceData.getLoggedMessage());
                                            complianceValidations.setSection206Validation(stringStringMap);
                                        } else {
                                            Map<String, String> stringStringMap = null;
                                            if (Objects.nonNull(complianceValidations.getSection206Validation())) {
                                                stringStringMap = complianceValidations.getSection206Validation();
                                            } else {
                                                stringStringMap = new HashMap<>();
                                            }
                                            stringStringMap.put(innerKey, "NO_DATA_FOUND");
                                            complianceValidations.setSection206Validation(stringStringMap);
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        complianceValidationsMap.put(vendor, new VendorComplianceValidations());
                    }
                }
            }
        }
        vendorComplianceDataList = vendorManagementDao.findVendorCompliancesByComplianceType(getComplianceTypeValues(vendorComplianceTypes), complianceKeys);
        blockVendorPayments(complianceValidationsMap, vendorComplianceDataList, complianceKeyWithVendors, getComplianceTypeValues(vendorComplianceTypes));
    }

    private void blockVendorPayments(Map<Integer, VendorComplianceValidations> complianceValidationsMap, List<VendorComplianceData> vendorComplianceDataList, Map<String, Set<Integer>> complianceKeyWithVendors, List<String> complianceTypeValues) throws SumoException {
        LOG.info(":::: Started Blocking Vendor Payments :::");
        Map<Integer, String> alreadyBlockedVendors = new HashMap<>();
        Map<Integer, String> alreadyUnBlockedVendors = new HashMap<>();
        for (VendorComplianceData vendorComplianceData : vendorComplianceDataList) {
            try {
                if (Objects.nonNull(vendorComplianceData.getKeyId())) {
                    if (complianceKeyWithVendors.containsKey(vendorComplianceData.getComplianceKey())) {
                        Set<Integer> vendorIds = complianceKeyWithVendors.get(vendorComplianceData.getComplianceKey());
                        for (Integer vendorId : vendorIds) {
                            if (complianceValidationsMap.containsKey(vendorId)) {
                                boolean shouldBlock = false;
                                boolean isGstInValidated = false;
                                boolean isGstReturnValidated = false;
                                boolean isSection206Validated = false;
                                StringBuilder blockedReason = new StringBuilder();
                                StringBuilder unBlockedReason = new StringBuilder();
                                VendorComplianceValidations complianceValidations = complianceValidationsMap.get(vendorId);
                                if (Objects.nonNull(complianceValidations.getGstInValidation())) {
                                    Map<String, String> stringStringMap = complianceValidations.getGstInValidation();
                                    if (Objects.nonNull(stringStringMap)) {
                                        for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                                            if (Objects.nonNull(entry.getValue())) {
                                                if (entry.getValue().equalsIgnoreCase("Inactive") || entry.getValue().equalsIgnoreCase("Cancelled") || entry.getValue().equalsIgnoreCase("Suspended")) {
                                                    shouldBlock = true;
                                                    isGstInValidated = false;
                                                    if (blockedReason.toString().equalsIgnoreCase("")) {
                                                        blockedReason.append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    } else {
                                                        blockedReason.append(",").append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    }
                                                }
                                                if (entry.getValue().equalsIgnoreCase(AppConstants.ACTIVE)) {
                                                    isGstInValidated = true;
                                                    if (unBlockedReason.toString().equalsIgnoreCase("")) {
                                                        unBlockedReason.append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    } else {
                                                        unBlockedReason.append(",").append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                if (Objects.nonNull(complianceValidations.getGstReturnValidation())) {
                                    Map<String, String> stringStringMap = complianceValidations.getGstReturnValidation();
                                    if (Objects.nonNull(stringStringMap)) {
                                        for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                                            if (Objects.nonNull(entry.getValue())) {
                                                if (entry.getValue().startsWith("NOT_FILED")) {
                                                    shouldBlock = true;
                                                    isGstReturnValidated = false;
                                                    if (blockedReason.toString().equalsIgnoreCase("")) {
                                                        blockedReason.append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    } else {
                                                        blockedReason.append(",").append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    }
                                                }
                                                if (entry.getValue().startsWith("FILED")) {
                                                    isGstReturnValidated = true;
                                                    if (unBlockedReason.toString().equalsIgnoreCase("")) {
                                                        unBlockedReason.append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    } else {
                                                        unBlockedReason.append(",").append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                if (Objects.nonNull(complianceValidations.getSection206Validation())) {
                                    Map<String, String> stringStringMap = complianceValidations.getSection206Validation();
                                    if (Objects.nonNull(stringStringMap)) {
                                        for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
                                            if (Objects.nonNull(entry.getValue())) {
                                                if (entry.getValue().startsWith("Inoperative")) {
                                                    shouldBlock = true;
                                                    if (blockedReason.toString().equalsIgnoreCase("")) {
                                                        blockedReason.append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    } else {
                                                        blockedReason.append(",").append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    }
                                                }

                                                if (entry.getValue().startsWith("NO_DEDUCTION")) {
                                                    isSection206Validated = true;
                                                    storeTdsDeductionData(null, vendorId);
                                                    if (unBlockedReason.toString().equalsIgnoreCase("")) {
                                                        unBlockedReason.append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    } else {
                                                        unBlockedReason.append(",").append(entry.getValue()).append(" ( ").append(entry.getKey()).append(" )");
                                                    }
                                                }

                                                if (entry.getValue().startsWith("DEDUCTION")) {
                                                    storeTdsDeductionData(entry.getValue(), vendorId);
                                                }
                                            }
                                        }
                                    }
                                }
                                if (shouldBlock) {
                                    if (!alreadyBlockedVendors.containsKey(vendorId)) {
                                        alreadyBlockedVendors.put(vendorId, blockedReason.toString() + "-" + vendorComplianceData.getComplianceKey());
                                        VendorEditVO vendorEditVO = new VendorEditVO();
                                        vendorEditVO.setUserId(AppConstants.SYSTEM_EMPLOYEE_ID);
                                        vendorEditVO.setVendorId(vendorId);
                                        vendorEditVO.setBlockedReason(blockedReason.toString());
                                        try {
                                            blockVendorPayments(vendorEditVO);
                                        } catch (Exception e) {
                                            LOG.error("Unable To Block Vendor Payments For Vendor Id : {} ::", vendorId, e);
                                        }
                                    } else {
                                        alreadyBlockedVendors.put(vendorId, Objects.nonNull(alreadyBlockedVendors.get(vendorId)) ?
                                                (alreadyBlockedVendors.get(vendorId).equalsIgnoreCase(blockedReason.toString()) || alreadyBlockedVendors.get(vendorId).contains(blockedReason.toString())) ?
                                                        blockedReason.toString() : alreadyBlockedVendors.get(vendorId) + "," + blockedReason.toString() : blockedReason.toString());
                                    }
                                }

                                if (isGstInValidated && isGstReturnValidated && isSection206Validated) {
                                    if (!alreadyUnBlockedVendors.containsKey(vendorId)) {
                                        VendorEditVO vendorEditVO = new VendorEditVO();
                                        vendorEditVO.setUserId(AppConstants.SYSTEM_EMPLOYEE_ID);
                                        vendorEditVO.setVendorId(vendorId);
                                        vendorEditVO.setBlockedReason(blockedReason.toString());
                                        VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
                                        if (Objects.nonNull(vendorDetailData) && Objects.nonNull(vendorDetailData.getAccountDetails()) && Objects.nonNull(vendorDetailData.getAccountDetails().getPaymentBlocked()) &&
                                                vendorDetailData.getAccountDetails().getPaymentBlocked().equalsIgnoreCase(AppConstants.YES)) {
                                            try {
//                                            unBlockVendorPayments(vendorEditVO);
                                                alreadyUnBlockedVendors.put(vendorId, unBlockedReason.toString());
                                            } catch (Exception e) {
                                                LOG.error("Unable To Block Vendor Payments For Vendor Id : {} ::", vendorId, e);
                                            }
                                        }
                                    } else {
                                        alreadyUnBlockedVendors.put(vendorId, Objects.nonNull(alreadyUnBlockedVendors.get(vendorId)) ?
                                                (alreadyUnBlockedVendors.get(vendorId).equalsIgnoreCase(unBlockedReason.toString()) || alreadyUnBlockedVendors.get(vendorId).contains(unBlockedReason.toString())) ?
                                                        unBlockedReason.toString() : alreadyUnBlockedVendors.get(vendorId) + "," + unBlockedReason.toString() : unBlockedReason.toString());
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                LOG.error("Exception Occurred While Applying Validation for Compliance Key : {} ",vendorComplianceData.getComplianceKey(), e);
            }
        }
        List<String> vendorsReasons = new ArrayList<>();
        List<String> vendorsUnblockReasons = new ArrayList<>();
        for (Map.Entry<Integer, String> entry : alreadyBlockedVendors.entrySet()) {
            Integer vendorId = entry.getKey();
            String reason = entry.getValue();
            addPaymentBlockUnblockLog(vendorId, reason, "BLOCK");
            VendorDetail vendorDetail = scmCache.getVendorDetail(vendorId);
            if (Objects.nonNull(vendorDetail)) {
                vendorsReasons.add(vendorDetail.getEntityName() + "#@" + vendorDetail.getVendorId() + "#@" + (Objects.nonNull(reason) ? reason : "NO_DATA"));
            }
        }
        for (Map.Entry<Integer, String> entry : alreadyUnBlockedVendors.entrySet()) {
            Integer vendorId = entry.getKey();
            String reason = entry.getValue();
            addPaymentBlockUnblockLog(vendorId, reason, "UN_BLOCK");
            VendorDetail vendorDetail = scmCache.getVendorDetail(vendorId);
            if (Objects.nonNull(vendorDetail)) {
                vendorsUnblockReasons.add(vendorDetail.getEntityName() + "#@" + vendorDetail.getVendorId() + "#@" + (Objects.nonNull(reason) ? reason : "NO_DATA"));
            }
        }
        VendorComplianceEmailNotificationTemplate vendorComplianceEmailNotificationTemplate = new VendorComplianceEmailNotificationTemplate(props.getBasePath(), vendorsReasons, vendorsUnblockReasons);
        VendorComplianceEmailNotification notification = new VendorComplianceEmailNotification(vendorComplianceEmailNotificationTemplate,
                props.getEnvType(), Arrays.asList("<EMAIL>", "<EMAIL>"));
        generateVendorComplianceEmail(vendorsReasons, notification, vendorsUnblockReasons);
    }

    private void addPaymentBlockUnblockLog(Integer vendorId, String reason, String action) throws SumoException {
        try {
            PaymentsBlockUnBlockByComplianceLog paymentsBlockUnBlockByComplianceLog = new PaymentsBlockUnBlockByComplianceLog();
            paymentsBlockUnBlockByComplianceLog.setAction(action);
            paymentsBlockUnBlockByComplianceLog.setVendorId(vendorId);
            paymentsBlockUnBlockByComplianceLog.setLoggedAt(AppUtils.getCurrentTimestamp());
            paymentsBlockUnBlockByComplianceLog.setReason(reason);
            vendorManagementDao.add(paymentsBlockUnBlockByComplianceLog, true);
        } catch (Exception e) {
            LOG.error("Exception Occurred While Saving Payment Block Unblock Log :: ", e);
        }
    }

    private void generateVendorComplianceEmail(List<String> vendorsReasons, VendorComplianceEmailNotification notification, List<String> vendorsUnblockReasons) {
        LOG.info(":: Generating VendorCompliance Email ::");
        Workbook workbook = new XSSFWorkbook();
        String fileName = "Vendor_Compliance" + AppUtils.getCurrentTimeISTStringWithNoColons();
        try {
            Sheet sheet = workbook.createSheet();
            workbook.setSheetName(0, "BLOCKED_VENDORS");
            int rowCount = 0;
            int columnCount = 0;
            Row row = sheet.createRow(rowCount++);
            List<String> fieldNames = new ArrayList<>();
            fieldNames.add("Vendor Name");
            fieldNames.add("Vendor Id");
            fieldNames.add("Blocked Reason");
            for (String fieldName : fieldNames) {
                Cell cell = row.createCell(columnCount++);
                cell.setCellValue(fieldName);
                cell.setCellStyle(requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook));
            }
            for (int k = 0; k < vendorsReasons.size(); k++) {
                String[] rowValues = vendorsReasons.get(k).split("#@");
                Row rows = sheet.createRow(rowCount++);
                for (int i = 0; i < columnCount; i++) {
                    Cell cell = rows.createCell(i);
                    cell.setCellValue(rowValues[i]);
                }
            }

            Sheet sheet2 = workbook.createSheet();
            workbook.setSheetName(1, "UN_BLOCKED_VENDORS");
            int rowCount2 = 0;
            int columnCount2 = 0;
            Row row2 = sheet2.createRow(rowCount2++);
            List<String> fieldNames2 = new ArrayList<>();
            fieldNames2.add("Vendor Name");
            fieldNames2.add("Vendor Id");
            fieldNames2.add("Un Blocked Reason");
            for (String fieldName : fieldNames2) {
                Cell cell = row2.createCell(columnCount2++);
                cell.setCellValue(fieldName);
                cell.setCellStyle(requestOrderManagementService.getHeaderStyle((XSSFWorkbook) workbook));
            }
            for (int k = 0; k < vendorsUnblockReasons.size(); k++) {
                String[] rowValues = vendorsUnblockReasons.get(k).split("#@");
                Row rows = sheet2.createRow(rowCount2++);
                for (int i = 0; i < columnCount2; i++) {
                    Cell cell = rows.createCell(i);
                    cell.setCellValue(rowValues[i]);
                }
            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            try {
                workbook.write(bos);
            } catch (IOException e1) {
                LOG.error("Error Occurred While writing into the Workbook... ::: ", e1);
            }

            File fileToUpload = new File(props.getBasePath() + fileName);
            byte[] barray = null;
            try {
                barray = bos.toByteArray();
            } catch (Exception e) {
                LOG.error("Error While Creating File");
            }
            try {
                LOG.info("Trying to send Email Of Vendor Compliance ::  ");
                List<AttachmentData> attachments = new ArrayList<>();
                AttachmentData roSheet = null;
                roSheet = new AttachmentData(barray, fileName,
                        AppConstants.EXCEL_MIME_TYPE);
                attachments.add(roSheet);
                notification.sendRawMail(attachments);
                fileToUpload.delete();
            } catch (Exception e) {
                LOG.info("error sending email ::: ", e);
            }
        } catch (Exception e) {
            LOG.error("error While Generating Excel ::: ", e);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void storeTdsDeductionData(String section206Validation, Integer vendorId) {
        try {
            VendorDetailData vendorDetailData = vendorManagementDao.find(VendorDetailData.class, vendorId);
            VendorAccountDetailData vendorAccountDetailData = vendorDetailData.getAccountDetails();
            if (Objects.nonNull(vendorAccountDetailData)) {
                vendorAccountDetailData.setSection206(section206Validation);
                vendorManagementDao.update(vendorAccountDetailData, true);
                VendorDetail vendorDetail = scmCache.getVendorDetail(vendorId);
                if (Objects.nonNull(vendorDetail) && Objects.nonNull(vendorDetail.getAccountDetails())) {
                    vendorDetail.getAccountDetails().setSection206(section206Validation);
                    scmCache.getVendorDetails().put(vendorId, vendorDetail);
                }
            } else {
                LOG.info("Cannot Find Vendor Account Details For Vendor Id :: {}",vendorId);
            }
        } catch (Exception e) {
            LOG.error("Failed to Store TDS Deduction Data :: ",e);
        }
    }

    private List<String> getLast3Months() {
        List<String> result = new ArrayList<>();
        Date currentDate = AppUtils.getCurrentTimestamp();
        Calendar c = AppUtils.getCalender();
        c.setTime(currentDate);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        c.add(Calendar.MONTH, -1);
        result.add((AppUtils.getMonth(c.getTime()) < 10 ? ("0" + AppUtils.getMonth(c.getTime())) : AppUtils.getMonth(c.getTime())) + "" + AppUtils.getYear(c.getTime()));
        c.add(Calendar.MONTH, -1);
        result.add((AppUtils.getMonth(c.getTime()) < 10 ? ("0" + AppUtils.getMonth(c.getTime())) : AppUtils.getMonth(c.getTime())) + "" + AppUtils.getYear(c.getTime()));
        c.add(Calendar.MONTH, -1);
        result.add((AppUtils.getMonth(c.getTime()) < 10 ? ("0" + AppUtils.getMonth(c.getTime())) : AppUtils.getMonth(c.getTime())) + "" + AppUtils.getYear(c.getTime()));
        return result;
    }

    private List<String> getComplianceTypeValues(List<VendorComplianceType> vendorComplianceTypes) {
        List<String> result = new ArrayList<>();
        for (VendorComplianceType vendorComplianceType : vendorComplianceTypes) {
            result.add(vendorComplianceType.value());
        }
        return result;
    }

    private String getVendorComplianceFinancialYer(boolean isUrl) {
        int month = AppUtils.getMonth(AppUtils.getCurrentDate());
        if (month >= 4) {
            if (isUrl) {
                return "FY%20" + (AppUtils.getYear(AppUtils.getCurrentDate())) + "-" + ((AppUtils.getYear(AppUtils.getCurrentDate()) + 1) % 100);
            } else {
                return "FY " + (AppUtils.getYear(AppUtils.getCurrentDate())) + "-" + ((AppUtils.getYear(AppUtils.getCurrentDate()) + 1) % 100);
            }
        } else {
            if (isUrl) {
                return "FY%20" + (AppUtils.getYear(AppUtils.getCurrentDate()) - 1) + "-" + (AppUtils.getYear(AppUtils.getCurrentDate()) % 100);
            } else {
                return "FY " + (AppUtils.getYear(AppUtils.getCurrentDate()) - 1) + "-" + (AppUtils.getYear(AppUtils.getCurrentDate()) % 100);
            }
        }
    }

    private void addVendorComplianceDataForMonth(Set<String> complianceKeys, String complianceType) {
        List<VendorComplianceData> vendorComplianceDataList = new ArrayList<>();
        for (String complianceKey : complianceKeys) {
            VendorComplianceData vendorComplianceData = new VendorComplianceData();
            vendorComplianceData.setComplianceType(complianceType);
            vendorComplianceData.setYear(AppUtils.getYear(AppUtils.getCurrentTimestamp()));
            vendorComplianceData.setMonth(AppUtils.getMonth(AppUtils.getCurrentTimestamp()));
            vendorComplianceData.setComplianceKey(complianceKey);
            vendorComplianceData.setRetryApi(AppConstants.YES);
            if (complianceType.equalsIgnoreCase(VendorComplianceType.GST_RETURN.value())) {
                vendorComplianceData.setFinancialYear(getVendorComplianceFinancialYer(false));
            }
            vendorComplianceDataList.add(vendorComplianceData);
        }
        vendorManagementDao.addAll(vendorComplianceDataList);
    }
}
