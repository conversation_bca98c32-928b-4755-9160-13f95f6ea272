package com.stpl.tech.scm.data.dao;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.data.model.AdvancePaymentAuditLogData;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.CompanyBankMapping;
import com.stpl.tech.scm.data.model.DebitNoteDetailData;
import com.stpl.tech.scm.data.model.GoodsRecievedToPaymentRequestMapping;
import com.stpl.tech.scm.data.model.InvoiceDeviationMappingData;
import com.stpl.tech.scm.data.model.PaymentCalendarData;
import com.stpl.tech.scm.data.model.PaymentInvoiceData;
import com.stpl.tech.scm.data.model.PaymentRequestData;
import com.stpl.tech.scm.data.model.PaymentRequestItemMappingData;
import com.stpl.tech.scm.data.model.PaymentRequestLogData;
import com.stpl.tech.scm.data.model.PaymentRequestStatusLogData;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.domain.model.HolidayActivateDeactivate;
import com.stpl.tech.scm.domain.model.HolidayDetails;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.PaymentRequestType;

import java.util.Date;
import java.util.List;

public interface PaymentRequestManagementDao extends SCMAbstractDao {

    public List<PaymentRequestData> getPaymentRequests(Integer vendorId, Integer unitId, Integer prId, Integer grId, String invoiceNumber,
                                                       Date startDate, Date endDate, String status, Integer companyId, Date paymentDate, String requestType);

    public List<PaymentRequestData> getPaymentRequestsForProcess(PaymentRequestType type, Integer vendorId, Integer unitId, Integer prId, String invoiceNumber,
                                                                 Date startDate, Date endDate, Integer companyId, String dateType, List<String> statusList);

    public List<PaymentRequestData> getGRPaymentRequestsForProcess(PaymentRequestType type, Integer vendorId, Integer unitId, Integer prId, String invoiceNumber,
                                                                 Date startDate, Date endDate, Integer companyId, String dateType, List<String> statusList);

    public List<VendorGoodsReceivedData> findVendorGRFromPaymentRequest(int paymentRequestId);

    public List<VendorGoodsReceivedData> findPaymentRequestFromVendorGR(int grId);

    public List<PaymentRequestStatusLogData> findStatusChangeLogsByPaymentRequestId(int paymentRequestId);

    public List<PaymentRequestLogData> findPRLogsByPaymentRequest(int paymentRequestId);

    public List<PaymentRequestItemMappingData> findMappedItemsByPaymenrRequestId(int paymentRequestId);

    public List<PaymentRequestItemMappingData> findMappedItemsByGRId(int vendorGRId, PaymentRequestType paymentRequestType);

    public PaymentInvoiceData findInvoiceByPaymentRequestId(int paymentRequestId);

    public List<InvoiceDeviationMappingData> findDeviations(int itemId, String deviationLevel);

    public List<DebitNoteDetailData> findDebitNoteByPaymentRequest(int paymentRequestId);

    public DebitNoteDetailData findDebitNoteByPaymentRequestId(int paymentRequestId);

    public List<DebitNoteDetailData> getDebitNotes(Integer vendorId, Integer unitId, Integer prId, Integer dnId, String invoiceNumber,
                                                   Date startDate, Date endDate, Integer companyId, String status);

    public List<PaymentCalendarData> getPaymentDatesFromCalendar(Integer cycleTag, Date prDate, Date invoiceDate, int companyId);

    public List<PaymentRequestData> findUnfinishedPaymentsForVendor(Integer vendorId);

    public List<CompanyBankMapping> getBanksOfComapny(int companyId, String bankCode);

    public List<PaymentRequestData> findAllSentPaymentsForVendor(int vendorId);

	public PaymentInvoiceData findPaymentInvoice(Integer id);

    public List<VendorGoodsReceivedData> getRejectedPrForGr(Integer unitId, Integer vendorId);

    public GoodsRecievedToPaymentRequestMapping getRejectedPaymentMapping(Integer grId);

    public boolean updatePrToGRMapping(Integer id, VendorGoodsReceivedData vendorGoodsReceivedData, PaymentRequestStatus paymentRequestStatus);

    public Boolean isHoliday(Date date);

    public boolean addHoliday(HolidayDetails holidayDetails);

    public List<HolidayDetails> listOfHolidays(String holidayType, Integer year);

    public boolean activaeteDeActivateHoliday(HolidayActivateDeactivate request);

    public SpecializedOrderInvoiceData findSpecializedOrderInvoiceByPrId(Integer prId);

     public List<PaymentRequestData> getSRPaymentRequests(Integer vendorId, Integer unitId, Integer prId, Integer grId,Integer srId, String invoiceNumber, Date startDate, Date endDate, String status, Integer companyId, Date paymentDate, String requestType);

     public List<Pair<String,String>> getMandatoryReqDoc(Integer prId);

    public AdvancePaymentData getVendorAdvancePayment(Integer vendorId, List<String> statusList, String advanceType, Integer poId, Integer soId);

    public AdvancePaymentData getAdvancePaymentByPr(Integer id);

    public List<AdvancePaymentData> getAllVendorAdvances(Date startDate, Date endDate, Integer vendorId, List<String> statusList, List<Integer> advancePaymentIds);

    public AdvancePaymentData getVendorAdjustedAdvance(AdvancePaymentData vendorAdvancePayment);

    public List<AdvancePaymentData> getNonRefundAdvancesForBlocking(Date currentDate, List<String> nonRefundStatusList, Integer vendorId);

    List<AdvancePaymentData> getRefundProcessedVendorAdvance();

    List<VendorDetailData> getBlockedVendorsDueToAdvance();

    List<AdvancePaymentData> getAllRunningAdvances();

    List<PaymentRequestData> getQueriedPrsToReject();

    List<String> getEmployeePaymentCards(Integer userId);

    List<AdvancePaymentData> getAdvancePaymentByAdvanceIds(List<Integer> advancePaymentIds);

    List<AdvancePaymentData> getAllParentAdvances(AdvancePaymentData advancePaymentData);

    List<AdvancePaymentData> getAllAdvancesByAdvanceStatus(AdvancePaymentData advancePaymentData, String advanceStatus);

    List<AdvancePaymentAuditLogData> getAdvancePaymentAuditLog(PaymentRequestData paymentRequestData);
}
