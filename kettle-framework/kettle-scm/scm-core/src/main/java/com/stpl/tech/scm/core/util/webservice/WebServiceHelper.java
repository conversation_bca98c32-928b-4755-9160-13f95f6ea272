package com.stpl.tech.scm.core.util.webservice;

import com.stpl.tech.util.JSONSerializer;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;

public class WebServiceHelper {

	private static HttpClient WEB_SERVICE_CLIENT;
	private static HttpClient WEB_SERVICE_CLIENT_300;
	private static final int SOCKET_TIMEOUT_300 = 10;
	private static final int CONNECTION_TIME_OUT_300 = 10;
	private static final int DEFAULT_SOCKET_TIMEOUT = 15;
	private static final int DEFAULT_CONNECTION_TIME_OUT = 15;

	private static final Logger LOG = LoggerFactory.getLogger(WebServiceHelper.class);

	public static <T> T postWithAuth(String endPoint, String token, Object body, Class<T> clazz) {
		try {
			RestTemplate restTemplate = new RestTemplate();
			HttpHeaders requestHeaders = new HttpHeaders();
			requestHeaders.set("auth-internal", token);
			HttpEntity<?> requestEntity;
			if (body != null) {
				requestEntity = new HttpEntity(body, requestHeaders);
			} else {
				requestEntity = new HttpEntity(requestHeaders);
			}
			MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
			jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON_UTF8));
			restTemplate.getMessageConverters().add(jackson);
			return restTemplate.postForObject(endPoint, requestEntity, clazz);
		} catch (Exception e) {
			LOG.error("ERROR", e);
			throw e;
		}
	}

	public static <T> T convertResponseUsingSerializer(HttpResponse response, Class<T> responseClazz)
			throws IllegalStateException, IOException {
		T response1 = convertResponse(response, responseClazz);
		closeHttpResponse(response);
		return response1;
	}

	public static <T> T convertResponse(HttpResponse response, Class<T> responseClazz)
			throws IllegalStateException, IOException {
		if (response != null) {
			BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
			StringBuffer result = new StringBuffer();
			String line = "";
			while ((line = reader.readLine()) != null) {
				result.append(line);
			}
			//LOG.info("recorded response :::: {}", result.toString());
			EntityUtils.consume(response.getEntity());
			return convertUsingSerializer(result.toString(), responseClazz);
		}
		return null;
	}

	public static <T> T convertUsingSerializer(String input, Class<T> output) throws IOException {
		return JSONSerializer.toJSON(input, output);
	}

	private static void closeHttpResponse(HttpResponse response) {
		try {
			response.getEntity().getContent().close();
		} catch (Exception e) {
			LOG.error("ERROR Occurred While Closing the HTTP Response ::: ", e);
		}
	}

	public static HttpResponse postRequest(HttpPost requestObject) throws ClientProtocolException, IOException {
		return executeRequest(requestObject, false);
	}

	public static HttpResponse getRequest(HttpGet requestObject) throws ClientProtocolException, IOException {
		return executeRequest(requestObject, false);
	}

	public static <T> T exchangeWithAuth(String endpoint, String token, HttpMethod method, Class<T> clazz, String body,
			Map<String, ?> uriVariables) throws URISyntaxException {
		return exchangeWithAuth(endpoint, token, method, clazz, body, uriVariables, MediaType.APPLICATION_JSON_UTF8);
	}

	public static <T> T exchangeWithAuth(String endpoint, String token, HttpMethod method, Class<T> clazz, String body,
			Map<String, ?> uriVariables, MediaType type) throws URISyntaxException {
		try {
			RestTemplate restTemplate = new RestTemplate();
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Arrays.asList(type));
			// TODO Check this if something went wrong
			headers.setContentType(type);
			headers.set("auth-internal", token);
			HttpEntity<String> entity = new HttpEntity<String>(body, headers);
			if (uriVariables != null) {
				endpoint += "?";
				for (String key : uriVariables.keySet()) {
					endpoint = endpoint + key + "=" + uriVariables.get(key).toString() + "&";
				}
				endpoint = endpoint.substring(0, endpoint.length() - 1);
			}
			URI uri = new URI(endpoint);
			return (T) restTemplate.exchange(uri, method, entity, clazz).getBody();
		} catch (Exception e) {
			LOG.error("ERROR", e);
			throw e;
		}
	}

	public static HttpResponse postWithBearer(String endpoint, String token, Object body, Map<String,?> uriVariables,Boolean isForProductProjections) throws Exception {
		try {
			if (uriVariables != null) {
				endpoint += "?";
				StringBuilder endpointBuilder = new StringBuilder(endpoint);
				for (String key : uriVariables.keySet()) {
					endpointBuilder.append(key).append("=").append(uriVariables.get(key).toString());
				}
				endpoint = endpointBuilder.toString();
			}
			HttpPost request = new HttpPost(endpoint);
			if(token != null) {
				request.setHeader("Authorization", "Bearer "+token);
			}
			StringEntity entity = new StringEntity(JSONSerializer.toJSON(body), ContentType.APPLICATION_JSON);
			request.setEntity(entity);
			return executeRequest(request,isForProductProjections);
		}
		catch (Exception e) {
			LOG.error("ERROR While Request {}", endpoint, e);
			throw e;
		}
	}

	private static HttpClient getHttpClientInstance(int socketTimeOut, int connectionTimeOut) {
		if (WEB_SERVICE_CLIENT_300 == null) {
			RequestConfig config = RequestConfig.custom().setSocketTimeout(socketTimeOut * 1000)
					.setConnectTimeout(connectionTimeOut * 1000).setConnectionRequestTimeout(connectionTimeOut * 1000)
					.build();
			WEB_SERVICE_CLIENT = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
		}
		return WEB_SERVICE_CLIENT;
	}

	private static HttpResponse executeRequest(HttpUriRequest requestObject, Boolean isForProductProjections)
			throws IOException {
		HttpResponse response;
		if (isForProductProjections) {
			response = getHttpClientInstance().execute(requestObject);
		}
		else {
			response = getHttpClientInstance(DEFAULT_SOCKET_TIMEOUT, DEFAULT_CONNECTION_TIME_OUT).execute(requestObject);
		}
		// clearing all the resources of the HTTP web service client
		if (response == null || response.getEntity() == null) {
			requestObject.abort();
		}
		return response;
	}

	private static HttpClient getHttpClientInstance() {
		if (WEB_SERVICE_CLIENT_300 == null) {
			RequestConfig config = RequestConfig.custom().build();
			WEB_SERVICE_CLIENT_300 = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
		}
		return WEB_SERVICE_CLIENT_300;
	}

	public static <E> Collection<E> makeCollection(Iterable<E> iterable) {
		Collection<E> list = new ArrayList<E>();
		for (E item : iterable) {
			list.add(item);
		}
		return list;
	}
}
