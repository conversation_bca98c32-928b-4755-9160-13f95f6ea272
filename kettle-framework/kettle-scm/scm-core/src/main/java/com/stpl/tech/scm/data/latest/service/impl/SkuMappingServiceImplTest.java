/**
 * 
 */
package com.stpl.tech.scm.data.latest.service.impl;

import java.util.Arrays;
import java.util.List;

import com.stpl.tech.scm.domain.model.IdCodeName;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.stpl.tech.scm.core.service.SkuMappingService;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.util.AppConstants;

/**
 * <AUTHOR>
 *
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = { ScmTestConfig.class })
@Ignore
public class SkuMappingServiceImplTest {

	@Autowired
	private SkuMappingService service;

	@Test
	public void testActiveSku() {
		List<IdCodeNameStatus> skus = service.allSKU();
		System.out.println("allActiveSKU Sku Size : " + skus.size());
	}

	@Test
	public void testActiveUnits() {
		List<IdCodeNameStatus> skus = service.allUnits();
		System.out.println("allActiveUnits Units Size : " + skus.size());
	}

	@Test
	public void testActiveVendors() {
		List<IdCodeNameStatus> skus = service.allVendors();
		System.out.println("allActiveVendors Vendors Size : " + skus.size());
	}

	@Test
	public void testActiveVendorsmappingsForSku() {
		List<IdCodeNameStatus> skus = service.searchVendorMappingsForSku(234);
		System.out.println("searchVendorMappingsForSku Vendors Mapping Size : " + skus.size());
		printArray(skus);
	}

	@Test
	public void testActiveSkuMappingsForVendor() {
		List<IdCodeNameStatus> skus = service.searchSkuMappingsForVendor(2);
		System.out.println("searchSkuMappingsForVendor SKU  Mapping Size : " + skus.size());
		printArray(skus);
	}

	@Test
	public void searchUnitMappingsForSku() {
		List<IdCodeNameStatus> skus = service.searchUnitMappingsForSku(234);
		System.out.println("searchUnitMappingsForSku Vendors Mapping Size : " + skus.size());
	}

	@Test
	public void searchSkuMappingsForUnit() {
		List<IdCodeNameStatus> skus = service.searchSkuMappingsForUnit(10000);
		System.out.println("searchSkuMappingsForUnit SKU  Mapping Size : " + skus.size());
		printArray(skus);
	}

	@Test
	public void updateSkuMappingsForUnit() {
		service.updateSkuMappingsForUnit(100001, "Nitin Saluja", 10001, Arrays.asList(101, 102));
		List<IdCodeNameStatus> skus = service.searchSkuMappingsForUnit(10001);
		System.out.println("searchSkuMappingsForUnit SKU  Mapping Size : " + skus.size());
		service.updateSkuMappingsForUnit(100001, "Nitin Saluja", 10001, Arrays.asList(103, 101));
		skus = service.searchSkuMappingsForUnit(10001);
		System.out.println("searchSkuMappingsForUnit SKU  Mapping Size : " + skus.size());
		printArray(skus);
		service.updateUnitMappingsForSku(100001, "Nitin Saluja", 101, Arrays.asList(10001, 10002));
		skus = service.searchUnitMappingsForSku(101);
		System.out.println("searchUnitMappingsForSku SKU  Mapping Size : " + skus.size());
		printArray(skus);
	}
	
	@Test
	public void updateSkuMappingsForVendor() {
		service.addSkuMappingsForVendor(100001, "Mohit Malik", 1, Arrays.asList(new IdCodeName(101,null,null),new IdCodeName(102,null,null)));
		List<IdCodeNameStatus> skus = service.searchSkuMappingsForVendor(1);
		System.out.println("searchSkuMappingsForVendor SKU  Mapping Size : " + skus.size());
		service.updateVendorSkuMapping(100001, "Mohit Malik", 1, 101, AppConstants.IN_ACTIVE);
		skus = service.searchSkuMappingsForVendor(1);
		System.out.println("searchSkuMappingsForVendor SKU  Mapping Size : " + skus.size());
		printArray(skus);
		service.updateVendorSkuMapping(100001, "Mohit Malik", 1, 101, AppConstants.ACTIVE);
		skus = service.searchSkuMappingsForVendor(1);
		System.out.println("searchSkuMappingsForVendor SKU  Mapping Size : " + skus.size());
		printArray(skus);
		service.updateVendorSkuMapping(100001, "Mohit Malik", 1, 102, AppConstants.IN_ACTIVE);
		skus = service.searchSkuMappingsForVendor(1);
		System.out.println("searchSkuMappingsForVendor SKU  Mapping Size : " + skus.size());
		printArray(skus);
		service.addSkuMappingsForVendor(100001, "Mohit Malik", 1, Arrays.asList(  new IdCodeName(103,null,null),new IdCodeName(104,null,null)));
		skus = service.searchSkuMappingsForVendor(1);
		System.out.println("searchSkuMappingsForVendor SKU  Mapping Size : " + skus.size());
		printArray(skus);
	}

	private <T> void printArray(List<T> list) {
		for (T t : list) {
			System.out.println(t);
		}
	}

}
