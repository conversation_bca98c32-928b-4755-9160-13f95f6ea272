package com.stpl.tech.scm.core.service.impl;

import com.google.common.base.Stopwatch;
import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.InventoryService;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.PurchaseOrderCreationException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.service.AbstractStockManagementService;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.FetchTransferOrderService;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.PurchaseOrderManagementService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.CreateVendorGrVO;
import com.stpl.tech.scm.core.util.model.PurchaseOrderCreateVO;
import com.stpl.tech.scm.core.util.model.UsedPOModel;
import com.stpl.tech.scm.core.util.model.UsedSKUModel;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.GoodsReceiveManagementDao;
import com.stpl.tech.scm.data.dao.PaymentRequestManagementDao;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.PurchaseOrderManagementDao;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.BudgetAuditDetailData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.DebitNoteDetailData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.FaTransferData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemDrilldown;
import com.stpl.tech.scm.data.model.InvoiceExcessQuantity;
import com.stpl.tech.scm.data.model.ItemTaxDetailData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderItemData;
import com.stpl.tech.scm.data.model.PurchaseOrderVendorGRMappingData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.data.model.SCMOrderPackagingData;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.data.model.StockEventDefinitionData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.data.model.TransferOrderItemDrilldown;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedItemData;
import com.stpl.tech.scm.data.model.VendorGrPoItemMappingData;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.BulkRequestVO;
import com.stpl.tech.scm.domain.model.CapexStatus;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.GrItemQuantityUpdation;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.BudgetAuditDetailData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.DebitNoteDetailData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.FaGrData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemDrilldown;
import com.stpl.tech.scm.data.model.InvoiceExcessQuantity;
import com.stpl.tech.scm.data.model.ItemTaxDetailData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderItemData;
import com.stpl.tech.scm.data.model.PurchaseOrderVendorGRMappingData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.data.model.SCMOrderPackagingData;
import com.stpl.tech.scm.data.model.SpecializedOrderInvoiceData;
import com.stpl.tech.scm.data.model.StockEventAssetMappingDefinitionData;
import com.stpl.tech.scm.data.model.StockEventDefinitionData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.data.model.TransferOrderItemDrilldown;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedItemData;
import com.stpl.tech.scm.data.model.VendorGrPoItemMappingData;
import com.stpl.tech.scm.data.transport.model.GrStockEvent;
import com.stpl.tech.scm.domain.model.AdvancePaymentStatus;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.BulkRequestVO;
import com.stpl.tech.scm.domain.model.CapexStatus;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GoodsReceivedItem;
import com.stpl.tech.scm.domain.model.GrItemQuantityUpdation;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.InventoryItemDrilldown;
import com.stpl.tech.scm.domain.model.InvoiceDocType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.OtherTaxDetail;
import com.stpl.tech.scm.domain.model.POCreationType;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.PendingMilkBread;
import com.stpl.tech.scm.domain.model.PercentageDetail;
import com.stpl.tech.scm.domain.model.OtherTaxDetail;
import com.stpl.tech.scm.domain.model.POCreationType;
import com.stpl.tech.scm.domain.model.PercentageDetail;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.PurchaseOrderItem;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RejectedGr;
import com.stpl.tech.scm.domain.model.RejectedGrItem;
import com.stpl.tech.scm.domain.model.SCMOrderPackaging;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SkuBasicDetail;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SpecializedOrderInvoice;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventStatusType;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockTakeSubType;
import com.stpl.tech.scm.domain.model.TaxDetail;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.TransferOrderItem;
import com.stpl.tech.scm.domain.model.TransferOrderType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorGR;
import com.stpl.tech.scm.domain.model.VendorGRItem;
import com.stpl.tech.scm.domain.model.VendorGrType;
import com.stpl.tech.scm.domain.model.VendorPoGRItems;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.PurchaseOrderItem;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RejectedGr;
import com.stpl.tech.scm.domain.model.RejectedGrItem;
import com.stpl.tech.scm.domain.model.SCMOrderPackaging;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SkuBasicDetail;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SpecializedOrderInvoice;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.TaxDetail;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.TransferOrderItem;
import com.stpl.tech.scm.domain.model.TransferOrderType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorGR;
import com.stpl.tech.scm.domain.model.VendorGRItem;
import com.stpl.tech.scm.domain.model.VendorGrType;
import com.stpl.tech.scm.domain.model.VendorPoGRItems;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.notification.email.template.RejectedGREmailNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * Created by Rahul Singh on 22-06-2016.
 */
@Service
@Log4j2
public class GoodsReceiveManagementServiceImpl extends AbstractStockManagementService
        implements GoodsReceiveManagementService {

    @Autowired
    private GoodsReceiveManagementDao goodsReceiveManagementDao;

    @Autowired
    private SCMAssetManagementService scmAssetManagementService;

    @Autowired
    private PriceManagementDao priceDao;

    @Autowired
    private EnvProperties props;

    @Autowired
    private PurchaseOrderManagementDao purchaseOrderManagementDao;

    @Autowired
    private FetchTransferOrderService fetchTransferOrderService;

    @Autowired
    private StockManagementDao stockManagementDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private PurchaseOrderManagementService purchaseOrderService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private ServiceOrderManagementDao serviceOrderManagementDao;

    @Autowired
    private PaymentRequestManagementDao paymentRequestManagementDao;

    @Autowired
    private PaymentRequestManagementServiceImpl paymentRequestManagementService;

    @Autowired
    private RequestOrderManagementService requestOrderManagementService;

    @Autowired
    private TransferOrderManagementService transferOrderManagementService;

    @Autowired
    private SCMAssetManagementDao scmAssetManagementDao;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public GoodsReceivedData createGoodReceiveFromTransferOrder(TransferOrderData transferOrderData,
                                                                RequestOrderData requestOrderData, List<TransferOrderItemData> transferOrderItemDatas,
                                                                PurchaseOrderData purchaseOrderData) throws SumoException {
        GoodsReceivedData goodsReceivedData = SCMDataConverter.convert(transferOrderData,
                AppConstants.SYSTEM_EMPLOYEE_ID, requestOrderData, purchaseOrderData, false, false,scmCache);
        goodsReceivedData = goodsReceiveManagementDao.add(goodsReceivedData, true);
        if (goodsReceivedData != null) {
            List<GoodsReceivedItemData> goodsReceivedItemDatas = new ArrayList<GoodsReceivedItemData>();
            for (TransferOrderItemData transferOrderItemData : transferOrderItemDatas) {
                GoodsReceivedItemData goodsReceivedItemData = SCMDataConverter.convert(transferOrderItemData,
                        goodsReceivedData, false,scmCache);
                SkuDefinition skuDefinition = scmCache.getSkuDefinition(goodsReceivedItemData.getSkuId());
                ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
                goodsReceivedItemData.setCategoryId(productDefinition.getCategoryDefinition().getId());
                goodsReceivedItemData.setSubCategoryId(productDefinition.getSubCategoryDefinition().getId());
                goodsReceivedItemData = goodsReceiveManagementDao.add(goodsReceivedItemData, false);
                List<SCMOrderPackagingData> scmOrderPackagingDatas = new ArrayList<SCMOrderPackagingData>();
                for (SCMOrderPackagingData scmOrderPackagingData : transferOrderItemData.getPackagingDetails()) {
                    scmOrderPackagingData.setGoodsReceivedItemData(goodsReceivedItemData);
                    goodsReceiveManagementDao.update(scmOrderPackagingData, false);
                    scmOrderPackagingDatas.add(scmOrderPackagingData);
                }
                List<GoodsReceivedItemDrilldown> drilldownList = new ArrayList<GoodsReceivedItemDrilldown>();
                for (TransferOrderItemDrilldown drilldown : transferOrderItemData.getItemDrilldowns()) {
                    GoodsReceivedItemDrilldown toDrilldown = SCMDataConverter.convert(drilldown, goodsReceivedItemData);
                    goodsReceiveManagementDao.add(toDrilldown, false);
                    drilldownList.add(toDrilldown);
                }
                goodsReceivedItemData.setPackagingDetails(scmOrderPackagingDatas);
                goodsReceivedItemData.setItemDrilldowns(drilldownList);
                goodsReceivedItemDatas.add(goodsReceivedItemData);
            }
            goodsReceivedData.setGoodsReceivedItemDatas(goodsReceivedItemDatas);
            goodsReceiveManagementDao.flush();
        }
        return goodsReceivedData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GoodsReceived> getPendingGrs(Integer unitId, Integer skuId , Boolean fetchRejected) {
        List<GoodsReceivedData> goodsReceivedDataList = goodsReceiveManagementDao.getPendingGrs(unitId, null , fetchRejected);
        if (Objects.nonNull(skuId)) {
            List<GoodsReceived> goodsReceivedList = getGrListWithGrItemsFromGrDataList(goodsReceivedDataList, true);
            return filterGRsBySkuId(goodsReceivedList, skuId);
        } else {
            return getGrListWithGrItemsFromGrDataList(goodsReceivedDataList, false);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GoodsReceived> getPendingGrsWithGrItems(Integer unitId) {
        List<GoodsReceivedData> goodsReceivedDataList = goodsReceiveManagementDao.getPendingGrs(unitId, null,false);
        return getGrListWithGrItemsFromGrDataList(goodsReceivedDataList, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GoodsReceived> getPendingDisputedGrs(Integer unitId , Integer skuId , Boolean setChild ) {
        List<GoodsReceivedData> goodsReceivedDataList = getRejectedPendingGrs(unitId);
        if(Objects.nonNull(skuId)){
            List<GoodsReceived> goodsReceivedList = getGrListWithGrItemsFromGrDataList(goodsReceivedDataList,true);
            return filterGRsBySkuId(goodsReceivedList,skuId);
        }else{
            List<GoodsReceived> goodsReceivedList =   getGrListWithGrItemsFromGrDataList(goodsReceivedDataList, setChild);
            goodsReceivedList.stream().forEach(gr ->{
                if(Objects.isNull(gr.getParentGR())){
                    gr.setRejectable(true);
                    return;
                }
                GoodsReceivedData parentGr = goodsReceiveManagementDao.find(GoodsReceivedData.class,gr.getParentGR());
                if(Objects.isNull(parentGr.getIsRejectedGR()) || AppConstants.NO.equalsIgnoreCase(parentGr.getIsRejectedGR())){
                    gr.setRejectable(true);
                }else{
                    gr.setRejectable(false);
                }

            });
            return  goodsReceivedList;
        }
    }

    private List<GoodsReceived> filterGRsBySkuId(List<GoodsReceived> goodsReceivedList , Integer skuId){
        if(Objects.isNull(skuId)){
            return goodsReceivedList;
        }
        List<GoodsReceived> filteredGRs = new ArrayList<>();
        for(GoodsReceived goodsReceived : goodsReceivedList){
            for(GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()){
                if(goodsReceivedItem.getSkuId() == skuId){
                    filteredGRs.add(goodsReceived);
                    break;
                }
            }
        }
        return filteredGRs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GoodsReceived> getRaisedDisputedGrs(Integer unitId , Integer skuId , Boolean setChild) {
        List<GoodsReceivedData> goodsReceivedDataList = goodsReceiveManagementDao.getRaisedDisputedGrs(unitId);
        if(Objects.nonNull(skuId)){
            List<GoodsReceived> goodsReceivedList = getGrListWithGrItemsFromGrDataList(goodsReceivedDataList,true);
            return filterGRsBySkuId(goodsReceivedList,skuId);
        }else{
            return getGrListWithGrItemsFromGrDataList(goodsReceivedDataList, setChild);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean isGrSettled(Integer grId) {
        GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class, grId);
        if (SCMOrderStatus.SETTLED.name().equals(goodsReceivedData.getStatus())) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public GoodsReceived getGoodsReceivedDetail(int grId) {
        GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class, grId);
        IdCodeName generatedBy = SCMUtil.generateIdCodeName(goodsReceivedData.getGeneratedBy(), "",
                masterDataCache.getEmployees().get(goodsReceivedData.getGeneratedBy()));
        IdCodeName receivedBy = null;
        if (goodsReceivedData.getReceivedBy() != null) {
            receivedBy = SCMUtil.generateIdCodeName(goodsReceivedData.getReceivedBy(), "",
                    masterDataCache.getEmployees().get(goodsReceivedData.getReceivedBy()));
        }
        IdCodeName cancelledBy = null;
        if (goodsReceivedData.getCancelledBy() != null) {
            cancelledBy = SCMUtil.generateIdCodeName(goodsReceivedData.getCancelledBy(), "",
                    masterDataCache.getEmployees().get(goodsReceivedData.getCancelledBy()));
        }
        IdCodeName generatedForUnit = SCMUtil.generateIdCodeName(goodsReceivedData.getGeneratedForUnitId(), "",
                masterDataCache.getUnitBasicDetail(goodsReceivedData.getGeneratedForUnitId()).getName());

        IdCodeName generationUnit = SCMUtil.generateIdCodeName(goodsReceivedData.getGenerationUnitId(), "",
                masterDataCache.getUnitBasicDetail(goodsReceivedData.getGenerationUnitId()).getName());
        UnitCategory generationUnitCategory = masterDataCache.getUnit(goodsReceivedData.getGenerationUnitId()).getFamily();
        UnitCategory generatedForUnitCategory = masterDataCache.getUnit(goodsReceivedData.getGeneratedForUnitId()).getFamily();
        Address generationUnitAddress = masterDataCache.getUnit(goodsReceivedData.getGenerationUnitId()).getAddress();
        Address generatedForUnitAddress = masterDataCache.getUnit(goodsReceivedData.getGeneratedForUnitId()).getAddress();
        GoodsReceived goodsReceived = SCMDataConverter.convert(goodsReceivedData, generatedBy, receivedBy, cancelledBy,
                generatedForUnit, generationUnit, true, scmCache, masterDataCache);
        goodsReceived.setGenerationUnitCategory(generationUnitCategory);
        goodsReceived.setGenerationUnitAddress(generationUnitAddress);
        goodsReceived.setGeneratedForUnitAddress(generatedForUnitAddress);
        goodsReceived.setGeneratedForUnitCategory(generatedForUnitCategory);
        GoodsReceivedData originalGr = getOriginalGrData(goodsReceivedData);
        if(originalGr !=null){            goodsReceived.setOriginalGrForUnitId(originalGr.getGeneratedForUnitId());
            if (originalGr.getTransferOrderData() != null && originalGr.getTransferOrderData().getType() != null) {
                goodsReceived.setType(originalGr.getTransferOrderData().getType());
            }
        }
        return goodsReceived;
    }

    @Override
    public GoodsReceivedData getOriginalGrData(GoodsReceivedData goodsReceivedData) {
        try {
            if (goodsReceivedData.getParentGR() == null) {
                return goodsReceivedData;
            } else {
                return getOriginalGrData(goodsReceivedData.getParentGR());
            }
        } catch (Exception e) {
            log.error("Exception occurred while finding Original GR Data ::: ",e);
            return null;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean settleGoodsReceivedDetail(GoodsReceived goodsReceived)
            throws InventoryUpdateException, SumoException, ParseException {

        Stopwatch watch = Stopwatch.createUnstarted();
        StockEventDefinitionData latestNSOEvent = scmAssetManagementDao.getLatestNSOEventByUnit(goodsReceived.getGeneratedForUnitId().getId(), StockEventStatusType.COMPLETED.value(), StockTakeSubType.NSO.value());
        if(Objects.nonNull(latestNSOEvent) && masterDataCache.getUnitBasicDetail(goodsReceived.getGeneratedForUnitId().getId()).getHandOverDate() == null
                && SCMUtil.getDateDifferenceInHours(latestNSOEvent.getLastUpdationTime(),SCMUtil.getCurrentBusinessDate()) > 24 ){
            throw new SumoException("NSO Stock Take Event In Process. Please Go to Kettle Admin and put Handover Date to continue creating Transfer Orders");
        }
        log.info("{} - {}", StockEventType.RECEIVED, goodsReceived.getId());
        if (goodsReceived.getTransferOrderId() != null) {
            watch.start();
            log.info("{} - Step 1 : Verify Transfer Order for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            TransferOrder transferOrder = fetchTransferOrderService
                    .getTransferOrderDetail(goodsReceived.getTransferOrderId());

            log.info("{} - Step 1 : Verify Transfer Order for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
            if (transferOrder == null || SCMOrderStatus.CANCELLED.equals(transferOrder.getStatus())) {
                log.error("Unable to Settle Goods Received as Transfer Order is CANCELLED or DOES NOT EXIST");
                return false;
            }
        }

        Map<Integer, Map<Date, BigDecimal>> rejectedItemMap = new HashMap<>();
        boolean hasSavedDrilldown = true;
        goodsReceived.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class,
                goodsReceived.getId());
        if (SCMOrderStatus.SETTLED.name().equalsIgnoreCase(goodsReceivedData.getStatus())) {
            log.error("Unable to Settle Goods Received as it is already Sett" +
                    "led for GR {}", goodsReceived.getId());
            return false;
        }
        log.info("Start to settle good received Id {}", goodsReceivedData.getId());


        goodsReceivedData.setReceivedBy(goodsReceived.getReceivedBy().getId());
        if (goodsReceived.getRejectGRComment() != null) {
            goodsReceivedData.setRejectGRComment(goodsReceived.getRejectGRComment());
        }
        goodsReceivedData.setComment(goodsReceived.getComment());
        goodsReceivedData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        goodsReceivedData.setStatus(SCMOrderStatus.SETTLED.value());
        if (goodsReceivedData.getRequestOrderData() != null) {
            watch.start();
            log.info("{} - Step 2 : Change Request Order for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            RequestOrderData requestOrderData = goodsReceivedData.getRequestOrderData();
            requestOrderData.setStatus(SCMOrderStatus.SETTLED.value());
            requestOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
            goodsReceivedData.setRequestOrderData(requestOrderData);
            log.info("{} - Step 2 : Change Request Order for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
        if (goodsReceivedData.getTransferOrderData() != null) {
            watch.start();
            log.info("{} - Step 3 : Change Transfer Order for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            TransferOrderData transferOrderData = goodsReceivedData.getTransferOrderData();
            transferOrderData.setStatus(SCMOrderStatus.SETTLED.value());
            transferOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
            goodsReceivedData.setTransferOrderData(transferOrderData);
            log.info("{} - Step 3 : Change Transfer Order for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
        goodsReceivedData.setTotalAmount(SCMUtil.convertToBigDecimal(getGoodReceivedAmount(goodsReceived)));
        Map<Integer, GoodsReceivedItem> receivedItemMap = new HashMap<Integer, GoodsReceivedItem>();
        watch.start();
        log.info("{} - Step 4 : Fetching Goods Received Items for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
        for (GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()) {
            receivedItemMap.put(goodsReceivedItem.getId(), goodsReceivedItem);
        }
        log.info("{} - Step 4 : Fetching Goods Received Items for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
        Set<Integer> skuIds = new HashSet<>();
        for (GoodsReceivedItemData goodsReceivedItemData : goodsReceivedData.getGoodsReceivedItemDatas()) {
            watch.start();
            log.info("{} - Step 5 : Updating packaging Data for items for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            GoodsReceivedItem goodsReceivedItem = receivedItemMap.get(goodsReceivedItemData.getId());
            goodsReceivedItemData
                    .setReceivedQuantity(SCMUtil.convertToBigDecimal(goodsReceivedItem.getReceivedQuantity()));
            for (SCMOrderPackagingData scmOrderPackagingData : goodsReceivedItemData.getPackagingDetails()) {
                for (SCMOrderPackaging scmOrderPackaging : goodsReceivedItem.getPackagingDetails()) {
                    if (scmOrderPackaging.getId().intValue() == scmOrderPackagingData.getId().intValue()) {
                        scmOrderPackagingData.setReceivedQuantity(
                                SCMUtil.convertToBigDecimal(scmOrderPackaging.getReceivedQuantity()));
                        scmOrderPackagingData.setNumberOfUnitsReceived(
                                SCMUtil.convertToBigDecimal(scmOrderPackaging.getNumberOfUnitsReceived()));
                        scmOrderPackagingData.setNumberOfUnitsRejected(
                                SCMUtil.convertToBigDecimal(scmOrderPackaging.getNumberOfUnitsRejected()));
                        scmOrderPackagingData.setRejectionReason(scmOrderPackaging.getRejectionReason());
                        goodsReceiveManagementDao.update(scmOrderPackagingData, false);
                    }
                }
            }
            log.info("{} - Step 5 : Updating packaging Data for items for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
            watch.start();
            log.info("{} - Step 6 : Updating GR Item Drilldowns for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            boolean result = updateGoodsReceivedItemDrillDowns(goodsReceivedItemData, goodsReceivedItem,
                    rejectedItemMap);
            if (!result && hasSavedDrilldown) {
                hasSavedDrilldown = result;
            }
            log.info("{} - Step 6 : Updating GR Item Drilldowns for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
            watch.start();
            log.info("{} - Step 7 : Updating GR for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            goodsReceivedItemData.setTaxAmount(getGrItemTaxAmount(goodsReceivedItemData));
            BigDecimal grItemAmountWithoutTax = goodsReceivedItemData.getNegotiatedUnitPrice()
                    .multiply(goodsReceivedItemData.getReceivedQuantity());
            goodsReceivedItemData.setCalculatedAmount(grItemAmountWithoutTax.setScale(2, RoundingMode.HALF_EVEN).add(goodsReceivedItemData.getTaxAmount()).
                    setScale(2,RoundingMode.HALF_EVEN));
            goodsReceivedItemData = goodsReceiveManagementDao.update(goodsReceivedItemData, false);
            log.info("{} - Step 7 : Updating GR for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));

            // Update Transfer Order Item for the GR item
            watch.start();
            log.info("{} - Step 8 : Updating TO for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            TransferOrderItemData transferOrderItemData = goodsReceivedItemData.getTransferOrderItemData();
            if (transferOrderItemData != null) {
                transferOrderItemData.setReceivedQuantity(goodsReceivedItemData.getReceivedQuantity());
                goodsReceiveManagementDao.update(transferOrderItemData, false);
            }
            log.info("{} - Step 8 : Updating TO for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
            // Update Request Order Item for the GR item
            watch.start();
            log.info("{} - Step 9 : Updating RO for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            RequestOrderItemData requestOrderItemData = goodsReceivedItemData.getRequestOrderItemData();
            if (requestOrderItemData != null) {
                requestOrderItemData.setReceivedQuantity(goodsReceivedItemData.getReceivedQuantity());
                goodsReceiveManagementDao.update(requestOrderItemData, false);
            }
            log.info("{} - Step 9 : Updating RO for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
        }
        if (!skuIds.isEmpty()) {
            List<Integer> availableSku = getAvailableSkusForUnit(goodsReceived.getGeneratedForUnitId().getId());
            List<String> missingSkus = new ArrayList<>();
            skuIds.forEach(skuId -> {
                if (!availableSku.contains(skuId)) {
                    String message = scmCache.getSkuDefinition(skuId).getSkuName() + "[ " + skuId + " ]";
                    missingSkus.add(message);
                }
            });
            if (!missingSkus.isEmpty()) {
                throw new SumoException("Missing Unit Sku Mappings ..!",
                        "Please Update Unit Sku Mappings For Unit : " + masterDataCache.getUnit(goodsReceived.getGeneratedForUnitId().getId()).getName() + " For the Following Sku's : <br><b>"
                        + Arrays.toString(missingSkus.toArray()) + "</b>"
                );
            }
        }
        updateGRTotalAmountWithTax(goodsReceivedData);
        goodsReceivedData = goodsReceiveManagementDao.update(goodsReceivedData, false);
        List<RejectedGrItem> rejectedGrItems = new ArrayList<>();
        // goodsReceiveManagementDao.flush();
        goodsReceivedData.setDocIdsPorImages(goodsReceived.getDocIdsPorImages());
        if (goodsReceivedData != null) {
            if(Objects.isNull(goodsReceived.isSpecialOrder()) || goodsReceived.isSpecialOrder() == false ) {
                watch.start();
                log.info("{} - Step 10 : Creating Rejected GR for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
                createGRforRejectedItems(goodsReceived, goodsReceivedData, rejectedItemMap, rejectedGrItems);
                log.info("{} - Step 10 : Creating Rejected GR for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
            }
            watch.start();
            log.info("{} - Step 11 : Publish GR for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            publishGoodsRecived(goodsReceivedData);
            log.info("{} - Step 11 : Publish GR for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));

            try {
                watch.start();
                log.info("{} - Step 12 : Add Receiving for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
                priceDao.addReceiving(goodsReceived, false);
                log.info("{} - Step 12 : Add Receiving for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
            } catch (Exception e) {
                log.info("{} - Step 12 : Add Receiving for ID {} Failed ", StockEventType.RECEIVED, goodsReceived.getId());
                throw e;
            }

            log.info(":::Checking If GR is For Capex RO :::");
            GoodsReceivedData originalGr = getOriginalGrData(goodsReceivedData);
            if(Objects.nonNull(originalGr.getTransferOrderData())) {
                RequestOrderData originalRO = originalGr.getTransferOrderData().getRequestOrderData();
                if (Objects.nonNull(originalRO) && Objects.nonNull(originalRO.getType()) && originalRO.getType().equalsIgnoreCase(CapexStatus.CAPEX.value())) {
                    BigDecimal totalAmount = goodsReceivedData.getTotalAmount();
                    Boolean isRejectedGr = originalGr.getGeneratedForUnitId() != goodsReceivedData.getGeneratedForUnitId();
                    log.info("::: Gr Is For Capex RO Updating Budget for Gr : {} and isRejected : {}", originalGr.getId(), isRejectedGr);
                    updateBudgetDetailsForInternalGR(totalAmount, originalGr.getId(), BudgetAuditActions.GR_ID,
                            originalGr.getGeneratedForUnitId(), originalRO.getAssetOrder(), isRejectedGr, goodsReceived.getReceivedBy().getId());
                }
            }

            // add business logic for transfer of asset
            watch.start();
            log.info("{} - Step 13 : Transfer Assets Against GR for ID {} Started", StockEventType.RECEIVED, goodsReceived.getId());
            String toType = goodsReceivedData.getTransferOrderType();
            if (toType.equals(TransferOrderType.BROKEN_ASSET_TRANSFER.value())
                    || toType.equals(TransferOrderType.FIXED_ASSET_TRANSFER.value())
                    || toType.equals(TransferOrderType.RENOVATION_ASSET_TRANSFER.value())) {
                if (goodsReceived.getType() != null && goodsReceived.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    if (goodsReceived.getAmountWithoutRejected() != null) {
                        BigDecimal total = new BigDecimal(goodsReceived.getAmountWithoutRejected().toString());
                        log.info("Received Total Amount is {}",total);
                        if (total.compareTo(BigDecimal.ZERO) > 0) {
                            if (goodsReceived.getUpdateRecievings() != null && goodsReceived.getUpdateRecievings()) {
                                if (!updateBudgetDetailsForSettle(goodsReceivedData, total)) {
                                    throw new SumoException("Error in Budget Updation", "Cannot Update Budget for <b>FIXED_ASSETS(FA_Equipment)</b> while Settling..!");
                                }
                            } else {
                                if (!updateBudgetDetailsForRejectd(goodsReceivedData, total)) {
                                    throw new SumoException("Error in Budget Updation", "Cannot Update Budget for <b>FIXED_ASSETS(FA_Equipment)</b> ..!");
                                }
                            }
                        }
                    }
                }

                return scmAssetManagementService.transferAssetAgainstGR(goodsReceivedData, rejectedGrItems,goodsReceived.getEventId());
            }

            sendRejectedGrMail(rejectedGrItems);
            log.info("{} - Step 13 : Transfer Assets Against GR for ID {} Completed in {}", StockEventType.RECEIVED, goodsReceived.getId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
        } else {

            String message = String.format(
                    "Found goods received data for grId %d as null, rolling back the transaction",
                    goodsReceived.getId());
            log.error(message);
            throw new SumoException(message);

        }
        updateMilkBreadCache(goodsReceived);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<String> getPorImageUrls(int grId) throws IOException {
        List<String> imageDocUrls = new ArrayList<>();
        GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class, grId);
        String imageDocIds = goodsReceivedData.getDocIdsPorImages();
        if(Objects.isNull(imageDocIds)){
            return null;
        }
        List<String> docIds = Arrays.asList(imageDocIds.split(","));
        for(String documentId : docIds){
            if(Objects.nonNull(documentId) && !documentId.equals("")){
                DocumentDetailData documentDetailData = goodsReceiveManagementDao.find(DocumentDetailData.class,Integer.valueOf(documentId));
                imageDocUrls.add(documentDetailData.getFileUrl());
            }
        }
        return imageDocUrls;
    }

    private List<Integer> getAvailableSkusForUnit(Integer unitId) {
        Set<Integer> s = scmCache.getAvailableSKUForUnit(unitId);
        if (s == null) {
            return new ArrayList<Integer>();
        }
        return new ArrayList<Integer>(s);
    }

    private void sendRejectedGrMail(List<RejectedGrItem> rejectedGrItems) {
        try {
            if (rejectedGrItems.size() > 0) {
                log.info("sending Rejected Gr mail");
                RejectedGr rejectedGr = scmAssetManagementService.getRejectedGr(rejectedGrItems);
                RejectedGREmailNotificationTemplate rejectedGREmailNotificationTemplate = new RejectedGREmailNotificationTemplate(rejectedGrItems,
                        masterDataCache.getUnitBasicDetail(rejectedGrItems.get(0).getRejectedUnitId()),props.getBasePath(),rejectedGr);
                notificationService.setRejectedGrNotification(rejectedGREmailNotificationTemplate);
            }
        }
        catch (Exception e ) {
            log.error("Error Occurred While sending Rejected GR mail :: ",e);
        }
    }


    private boolean updateBudgetDetailsForRejectd(GoodsReceivedData goodsReceivedData, BigDecimal total) {
        log.info("Updating Budget Audit Details For Fixed Asset (Rejected GR)");
        try {
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(goodsReceivedData.getGenerationUnitId());
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(goodsReceivedData.getGenerationUnitId(), AppConstants.FIXED_ASSETS);
            List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(), BudgetAuditActions.RUNNING_AMOUNT.value());
            for (String action : actions) {
                BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
                budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
                budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
                budgetAuditDetail.setActionBy(goodsReceivedData.getGeneratedBy());
                budgetAuditDetail.setAction(BudgetAuditActions.REJECTED.value());
                budgetAuditDetail.setKeyType(BudgetAuditActions.GR_ID.value());
                budgetAuditDetail.setKeyValue(goodsReceivedData.getId());
                budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
                if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
                    budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(total));
                    budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                } else {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                    budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(total));
                    budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                }
                serviceOrderManagementDao.add(budgetAuditDetail, true);
            }
            capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(total));
            capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(total));
            CapexBudgetDetailData finalData = serviceOrderManagementDao.update(capexBudgetData, true);
            if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                log.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        }
        catch (Exception e){
            log.error("Exception Occurred while updating budget for Fixed Assets Settle ::: ",e);
            return false;
        }
    }

    private Boolean updateBudgetDetailsForSettle(GoodsReceivedData goodsReceivedData, BigDecimal total) {
        log.info("Updating Budget Audit Details For Fixed Asset Settle");
        try {
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(goodsReceivedData.getGeneratedForUnitId());
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(goodsReceivedData.getGeneratedForUnitId(), AppConstants.FIXED_ASSETS);
            List<String> actions = Arrays.asList( BudgetAuditActions.RECEIVING_AMOUNT.value(),BudgetAuditActions.PAID_AMOUNT.value(),BudgetAuditActions.RUNNING_AMOUNT.value());
            for (String action : actions) {
                BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
                budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
                budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
                budgetAuditDetail.setActionBy(goodsReceivedData.getGeneratedBy());
                budgetAuditDetail.setAction(BudgetAuditActions.CREATED.value());
                budgetAuditDetail.setKeyType(BudgetAuditActions.GR_ID.value());
                budgetAuditDetail.setKeyValue(goodsReceivedData.getId());
                budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
                if(action.equalsIgnoreCase(BudgetAuditActions.RECEIVING_AMOUNT.value())){
                    budgetAuditDetail.setAmountType(BudgetAuditActions.RECEIVING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getReceivingAmount());
                    budgetAuditDetail.setFinalValue(capexBudgetData.getReceivingAmount().add(total));
                    budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                }
                else if (action.equalsIgnoreCase(BudgetAuditActions.PAID_AMOUNT.value())){
                    budgetAuditDetail.setAmountType(BudgetAuditActions.PAID_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getPaidAmount());
                    budgetAuditDetail.setFinalValue(capexBudgetData.getPaidAmount().add(total));
                    budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
                }
                else {
                    budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                    budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                    budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(total));
                    budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
                }
                serviceOrderManagementDao.add(budgetAuditDetail, true);
            }
            capexBudgetData.setReceivingAmount(capexBudgetData.getReceivingAmount().add(total));
            capexBudgetData.setPaidAmount(capexBudgetData.getPaidAmount().add(total));
            capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(total));
            CapexBudgetDetailData finalData = serviceOrderManagementDao.update(capexBudgetData, true);
            if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                log.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        }
        catch (Exception e){
            log.error("Exception Occurred while updating budget for Fixed Assets Settle ::: ",e);
            return false;
        }
    }

    private boolean updateGoodsReceivedItemDrillDowns(GoodsReceivedItemData data, GoodsReceivedItem item,
                                                      Map<Integer, Map<Date, BigDecimal>> rejectedItemMap) {
        log.info("updateGoodsReceivedItemDrillDowns : item id : " + data.getId());
        List<InventoryItemDrilldown> ddList = new ArrayList<>();
        Map<Date, BigDecimal> quantityMap = new HashMap<>();
        boolean hasSavedDrilldown = true;
        for (GoodsReceivedItemDrilldown dd : data.getItemDrilldowns()) {

            dd.setRejection(
                    item.getDrillDowns().stream().filter(p -> p.getKeyId() == dd.getGoodsReceivedItemDrilldownId())
                            .findFirst().get().getRejection());
            ddList.add(SCMDataConverter.convert(dd));
            BigDecimal rejectedQuanty = AppUtils.subtract(SCMUtil.convertToBigDecimal(item.getTransferredQuantity()),
                    SCMUtil.convertToBigDecimal(item.getReceivedQuantity()));
            if (rejectedQuanty.compareTo(BigDecimal.ZERO) > 0) {
                if (rejectedQuanty.compareTo(dd.getQuantity()) > 0) {
                    quantityMap.put(dd.getExpiryDate(), dd.getQuantity());
                    rejectedQuanty = AppUtils.subtract(rejectedQuanty, dd.getQuantity());
                } else {
                    quantityMap.put(dd.getExpiryDate(), rejectedQuanty);
                    rejectedQuanty = BigDecimal.ZERO;
                }
            }
        }
        item.setDrillDowns(ddList);
        if (ddList.size() == 0) {
            hasSavedDrilldown = false;
        }
        if (quantityMap.size() > 0) {
            rejectedItemMap.put(data.getId(), quantityMap);
        }
        return hasSavedDrilldown;
    }

    private void publishGoodsRecived(GoodsReceivedData goodsReceivedOrderData) {
        try {
            List<ProductQuantityData> details = new ArrayList<>();
            for (GoodsReceivedItemData data : goodsReceivedOrderData.getGoodsReceivedItemDatas()) {
                Integer productId = scmCache.getSkuDefinition(data.getSkuId()).getLinkedProduct().getId();
                for (GoodsReceivedItemDrilldown dd : data.getItemDrilldowns()) {
                    BigDecimal q = AppUtils.subtract(dd.getQuantity(), dd.getRejection());
                    if (q != null && BigDecimal.ZERO.compareTo(q) < 0) {
                        details.add(new ProductQuantityData(productId, q, data.getUnitOfMeasure(),
                                dd.getExpiryDate(), dd.getPrice()));
                    }
                }
            }
            if (details.size() > 0) {
                QuantityResponseData response = new QuantityResponseData(goodsReceivedOrderData.getGeneratedForUnitId(),
                        details, InventoryAction.ADD, InventorySource.GOODS_RECEIVED, goodsReceivedOrderData.getId(), SCMUtil.getCurrentTimestamp());
                inventoryService.publishInventorySQSFifo(props.getInventoryQueuePrefix(), response);

            }
        } catch (Exception e) {
            log.error("Error while publishing inventory for GR with id " + goodsReceivedOrderData.getId());
        }
    }

    private GoodsReceivedItemData copyGoodsReceivedItemDataForDecline(GoodsReceivedItem declinedGoodsReceivedItem,
                                                                      GoodsReceivedData declinedGoodReceivedData) {
        GoodsReceivedItemData declinedGoodsReceivedItemData = new GoodsReceivedItemData();
        declinedGoodsReceivedItemData.setGoodsReceivedData(declinedGoodReceivedData);
        declinedGoodsReceivedItemData
                .setNegotiatedUnitPrice(new BigDecimal(declinedGoodsReceivedItem.getNegotiatedUnitPrice().toString()));
        if (declinedGoodsReceivedItem.getTaxAmount() != null) {
            declinedGoodsReceivedItemData.setTaxAmount(new BigDecimal(declinedGoodsReceivedItem.getTaxAmount()));
        }
        Float totalPackedQuantity = 0.0f;
        for (SCMOrderPackaging scmOrderPackaging : declinedGoodsReceivedItem.getPackagingDetails()) {
            totalPackedQuantity += scmOrderPackaging.getNumberOfUnitsPacked()
                    * (scmOrderPackaging.getConversionRatio());
        }

        declinedGoodsReceivedItemData.setTransferredQuantity(SCMUtil.convertToBigDecimal(totalPackedQuantity));
        declinedGoodsReceivedItemData.setSkuId(declinedGoodsReceivedItem.getSkuId());
        declinedGoodsReceivedItemData.setSkuName(declinedGoodsReceivedItem.getSkuName());
        declinedGoodsReceivedItemData.setUnitOfMeasure(declinedGoodsReceivedItem.getUnitOfMeasure());
        declinedGoodsReceivedItemData
                .setUnitPrice(SCMUtil.convertToBigDecimal(declinedGoodsReceivedItem.getUnitPrice()));
        declinedGoodsReceivedItemData.setAssociatedAssetTagValue(declinedGoodsReceivedItem.getAssociatedAssetTagValue());
        declinedGoodsReceivedItemData.setAssociatedAssetId(declinedGoodsReceivedItem.getAssociatedAssetId());
        /*
         * declinedGoodsReceivedItemData
         * .setCalculatedAmount(SCMUtil.multiply(declinedGoodsReceivedItemData.
         * getReceivedQuantity(),
         * declinedGoodsReceivedItemData.getNegotiatedUnitPrice()));
         */
        SkuDefinition skuDefinition = scmCache.getSkuDefinition(declinedGoodsReceivedItem.getSkuId());
        ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
        declinedGoodsReceivedItemData.setCategoryId(productDefinition.getCategoryDefinition().getId());
        declinedGoodsReceivedItemData.setSubCategoryId(productDefinition.getSubCategoryDefinition().getId());
        return declinedGoodsReceivedItemData;
    }

    private GoodsReceivedItemData copyGoodsReceivedItemDataForReject(GoodsReceivedItem goodsReceivedItem,
                                                                     GoodsReceivedData rejectedGoodReceivedData) {
        GoodsReceivedItemData rejectedGoodsReceivedItemData = new GoodsReceivedItemData();
        rejectedGoodsReceivedItemData.setGoodsReceivedData(rejectedGoodReceivedData);
        rejectedGoodsReceivedItemData
                .setNegotiatedUnitPrice(new BigDecimal(goodsReceivedItem.getNegotiatedUnitPrice().toString()));
        if (goodsReceivedItem.getTaxAmount() != null ) {
            rejectedGoodsReceivedItemData.setTaxAmount(SCMUtil.convertToBigDecimal(goodsReceivedItem.getTaxAmount()));
        }
        rejectedGoodsReceivedItemData.setSkuId(goodsReceivedItem.getSkuId());
        rejectedGoodsReceivedItemData.setSkuName(goodsReceivedItem.getSkuName());
        rejectedGoodsReceivedItemData.setUnitOfMeasure(goodsReceivedItem.getUnitOfMeasure());
        rejectedGoodsReceivedItemData.setUnitPrice(SCMUtil.convertToBigDecimal(goodsReceivedItem.getUnitPrice()));
        rejectedGoodsReceivedItemData.setAssociatedAssetId(goodsReceivedItem.getAssociatedAssetId());
        rejectedGoodsReceivedItemData.setAssociatedAssetTagValue(goodsReceivedItem.getAssociatedAssetTagValue());
        SkuDefinition skuDefinition = scmCache.getSkuDefinition(goodsReceivedItem.getSkuId());
        ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
        if (Objects.nonNull(goodsReceivedItem.getCategoryId())) {
            rejectedGoodsReceivedItemData.setCategoryId(goodsReceivedItem.getCategoryId());
        }
        else {
            rejectedGoodsReceivedItemData.setCategoryId(productDefinition.getCategoryDefinition().getId());
        }
        if (Objects.nonNull(goodsReceivedItem.getSubCategoryId())) {
            rejectedGoodsReceivedItemData.setSubCategoryId(goodsReceivedItem.getSubCategoryId());
        }
        else {
            rejectedGoodsReceivedItemData.setSubCategoryId(productDefinition.getSubCategoryDefinition().getId());
        }
        return rejectedGoodsReceivedItemData;
    }

    private void addSCMOrderPackagingDataReject(SCMOrderPackaging scmOrderPackagingData,
                                                GoodsReceivedItemData rejectedGoodsReceivedItemData) {
        SCMOrderPackagingData rejectPackagingData = new SCMOrderPackagingData();
        rejectPackagingData.setConversionRatio(SCMUtil.convertToBigDecimal(scmOrderPackagingData.getConversionRatio()));
        rejectPackagingData.setGoodsReceivedItemData(rejectedGoodsReceivedItemData);
        rejectPackagingData
                .setNumberOfUnitsPacked(SCMUtil.convertToBigDecimal(scmOrderPackagingData.getNumberOfUnitsRejected()));
        rejectPackagingData.setPackagingDefinitionData(
                SCMDataConverter.convert(scmOrderPackagingData.getPackagingDefinitionData()));
        BigDecimal transferredQty = SCMUtil.multiplyWithScale10(rejectPackagingData.getNumberOfUnitsPacked(),
                rejectPackagingData.getConversionRatio());
        rejectPackagingData.setTransferredQuantity(transferredQty);
        rejectedGoodsReceivedItemData.setTransferredQuantity(transferredQty);
        rejectPackagingData.setPricePerUnit(SCMUtil.convertToBigDecimal(scmOrderPackagingData.getPricePerUnit()));
        if (rejectedGoodsReceivedItemData.getPackagingDetails() == null) {
            rejectedGoodsReceivedItemData.setPackagingDetails(new ArrayList<>());
        }
        rejectedGoodsReceivedItemData.getPackagingDetails().add(rejectPackagingData);
    }

    private void addSCMOrderPackagingDataDecline(SCMOrderPackaging scmOrderPackagingData,
                                                 GoodsReceivedItemData declinedGoodsReceivedItemData) {
        SCMOrderPackagingData declinedPackagingData = new SCMOrderPackagingData();
        declinedPackagingData
                .setConversionRatio(SCMUtil.convertToBigDecimal(scmOrderPackagingData.getConversionRatio()));
        declinedPackagingData.setGoodsReceivedItemData(declinedGoodsReceivedItemData);
        declinedPackagingData
                .setNumberOfUnitsPacked(SCMUtil.convertToBigDecimal(scmOrderPackagingData.getNumberOfUnitsPacked()));
        declinedPackagingData.setPackagingDefinitionData(
                SCMDataConverter.convert(scmOrderPackagingData.getPackagingDefinitionData()));
        declinedPackagingData.setTransferredQuantity(SCMUtil.multiply(declinedPackagingData.getNumberOfUnitsPacked(),
                declinedPackagingData.getConversionRatio()));
        declinedPackagingData.setPricePerUnit(SCMUtil.convertToBigDecimal(scmOrderPackagingData.getPricePerUnit()));
        if (declinedGoodsReceivedItemData.getPackagingDetails() == null) {
            declinedGoodsReceivedItemData.setPackagingDetails(new ArrayList<>());
        }
        declinedGoodsReceivedItemData.getPackagingDetails().add(declinedPackagingData);
    }

    private void addLostItemsToWastage(GoodsReceivedData goodsReceivedData) throws InventoryUpdateException, DataNotFoundException {
        if (SCMOrderStatus.SETTLED.name().equalsIgnoreCase(goodsReceivedData.getStatus())) {
            List<WastageEvent> wastageEventList = new ArrayList<>();
            for (GoodsReceivedItemData goodsReceivedItemData : goodsReceivedData.getGoodsReceivedItemDatas()) {
                SkuDefinition skuDefinition = scmCache.getSkuDefinition(goodsReceivedItemData.getSkuId());
                ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
                if(productDefinition.getCategoryDefinition().getId().equals(SCMServiceConstants.CATEGORY_FIXED_ASSETS)){
                    continue;
                }
                for (SCMOrderPackagingData scmOrderPackagingData : goodsReceivedItemData.getPackagingDetails()) {
                    wastageEventList
                            .add(createWastage(goodsReceivedData, goodsReceivedItemData, scmOrderPackagingData));
                }
            }
            if (wastageEventList != null && wastageEventList.size() > 0) {
                addWastage(wastageEventList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public HashMap<String, Integer> getPendingGrsStatusWise(Integer unitId, Integer daysBeforeOrAfter) {
        Date updatedAt = daysBeforeOrAfter != null ? SCMUtil.getDayBeforeOrAfterCurrentDay(daysBeforeOrAfter) : null;
        List<GoodsReceivedData> goodsReceivedDataList = goodsReceiveManagementDao.getPendingGrs(unitId, updatedAt,false);
        List<GoodsReceivedData> rejectedGRList = goodsReceiveManagementDao.getRejectedPendingGrs(unitId, updatedAt);
        List<GoodsReceivedData> allGR = goodsReceiveManagementDao.getPendingGrs(unitId, SCMUtil.getCurrentTimestamp(),false);
        HashMap<String, Integer> statusmap = new HashMap<>();
        long specialGR = 0;
        if (allGR != null && !allGR.isEmpty()) {
            specialGR = allGR.stream()
                    .filter(gr -> gr.getRequestOrderData() != null && AppConstants.YES.equalsIgnoreCase(gr.getRequestOrderData().getIsSpecialOrder()))
                    .count();
        }
        statusmap.put("pendingGR", goodsReceivedDataList.size());
        statusmap.put("specialGR", (int) specialGR);
        statusmap.put("rejectedPendingGR", rejectedGRList.size());
        return statusmap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GoodsReceivedData> getRejectedPendingGrs(Integer unitId) {
        return goodsReceiveManagementDao.getRejectedPendingGrs(unitId, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean rejectedGoodsReceivedFound(GoodsReceived goodsReceived) throws InventoryUpdateException, SumoException, ParseException {
        GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class,
                goodsReceived.getId());
        if (SCMOrderStatus.SETTLED.name().equalsIgnoreCase(goodsReceivedData.getStatus())) {
            log.error("Unable to Settle Goods Received as it is already Settled");
            return false;
        }
        settleGoodsReceivedDetail(goodsReceived);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean rejectedGoodsReceivedLost(GoodsReceived goodsReceived) throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException {
        GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class,
                goodsReceived.getId());
        if (SCMOrderStatus.SETTLED.name().equalsIgnoreCase(goodsReceivedData.getStatus())) {
            log.error("Unable to Settle Goods Received as it is already Settled");
            return false;
        }

        settleGoodsReceivedDetail(goodsReceived);
        if (goodsReceivedData != null) {
		    /*
		        If GR is for asset then skip booking wastage
		     */
            if (!goodsReceived.isAssetOrder()) {
                addLostItemsToWastage(goodsReceivedData);
            }

        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void settleRejectedGoodsReceived(GoodsReceivedData goodsReceivedData) throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException {

        if (SCMOrderStatus.SETTLED.name().equalsIgnoreCase(goodsReceivedData.getStatus())) {
            log.error("Unable to Settle Goods Received as it is already Settled");
        }
        GoodsReceived goodsReceived = getGoodsReceivedDetail(goodsReceivedData.getId());
        goodsReceived.setReceivedBy(SCMUtil.getSystemUser());
        settleGoodsReceivedDetail(goodsReceived);
        if (goodsReceivedData != null) {
		    /*
		        If GR is for asset then do not book wastage
		     */
            if (!goodsReceived.isAssetOrder()) {
                addLostItemsToWastage(goodsReceivedData);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean rejectedGoodsReceivedDecline(GoodsReceived goodsReceived) throws SumoException {
        GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class,
                goodsReceived.getId());
        if (SCMOrderStatus.SETTLED.name().equalsIgnoreCase(goodsReceivedData.getStatus())) {
            log.error("Unable to Settle Goods Received as it is already Settled");
            return false;
        }
        goodsReceivedData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        goodsReceivedData.setStatus(SCMOrderStatus.CANCELLED.value());
        goodsReceivedData.setCancelledBy(goodsReceived.getCancelledBy().getId());
        goodsReceivedData.setRejectGRComment(goodsReceived.getRejectGRComment());

        goodsReceivedData = goodsReceiveManagementDao.update(goodsReceivedData, true);
        if (goodsReceivedData != null) {
            createGRforDeclinedItems(goodsReceived, goodsReceivedData);
        }
        return true;
    }

    private void createGRforRejectedItems(GoodsReceived goodsReceived, GoodsReceivedData goodsReceivedData,
                                          Map<Integer, Map<Date, BigDecimal>> rejectedItemMap, List<RejectedGrItem> rejectedGrItems) throws SumoException {
        if (SCMOrderStatus.SETTLED.name().equalsIgnoreCase(goodsReceivedData.getStatus())) {
            log.info("Start creating gr for rejcted Items for grId {}", goodsReceivedData.getId());
            Map<Integer, GoodsReceivedItemData> rejectedItems = new HashMap<>();
            for (GoodsReceivedItem goodsReceivedItemData : goodsReceived.getGoodsReceivedItems()) {
                for (SCMOrderPackaging pkd : goodsReceivedItemData.getPackagingDetails()) {
                    if (pkd.getNumberOfUnitsRejected() != null && pkd.getNumberOfUnitsRejected() > 0.0f) {
                        GoodsReceivedData rejectedGoodReceivedData = new GoodsReceivedData();
                        rejectedGoodReceivedData.setComment(pkd.getRejectionReason());
                        rejectedGoodReceivedData.setGeneratedBy(goodsReceived.getReceivedBy().getId());
                        rejectedGoodReceivedData.setGeneratedForUnitId(goodsReceivedData.getGenerationUnitId());
                        rejectedGoodReceivedData.setReceivingCompanyId(goodsReceivedData.getSourceCompanyId());
                        rejectedGoodReceivedData.setGenerationUnitId(goodsReceivedData.getGeneratedForUnitId());
                        rejectedGoodReceivedData.setSourceCompanyId(goodsReceivedData.getReceivingCompanyId());
                        rejectedGoodReceivedData.setGenerationTime(SCMUtil.getCurrentTimestamp());
                        rejectedGoodReceivedData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                        rejectedGoodReceivedData.setIsRejectedGR(AppUtils.setStatus(true));
                        rejectedGoodReceivedData.setStatus(SCMOrderStatus.INITIATED.name());
                        rejectedGoodReceivedData.setAutoGenerated(AppUtils.setStatus(true));
                        rejectedGoodReceivedData.setParentGR(goodsReceivedData);
                        rejectedGoodReceivedData.setParentGRComment(goodsReceivedData.getComment());
                        rejectedGoodReceivedData.setTransferOrderType(goodsReceivedData.getTransferOrderType());

                        GoodsReceivedItemData rejectedGoodsReceivedItemData = copyGoodsReceivedItemDataForReject(
                                goodsReceivedItemData, rejectedGoodReceivedData);
                        if(Objects.nonNull(rejectedGoodsReceivedItemData.getTaxAmount())){
                            BigDecimal taxPerUnit = AppUtils.divideWithScale10(SCMUtil.convertToBigDecimal(goodsReceivedItemData.getTaxAmount()),
                                    SCMUtil.convertToBigDecimal(goodsReceivedItemData.getTransferredQuantity()));
                            BigDecimal rejectedQty = SCMUtil.convertToBigDecimal(AppUtils.subtract(pkd.getTransferredQuantity(),
                                    pkd.getReceivedQuantity()));
                            BigDecimal taxAmount = AppUtils.multiplyWithScale10(rejectedQty,taxPerUnit);
                            rejectedGoodsReceivedItemData.setTaxAmount(taxAmount);
                        }
                        rejectedGoodReceivedData.getGoodsReceivedItemDatas().add(rejectedGoodsReceivedItemData);
                        addSCMOrderPackagingDataReject(pkd, rejectedGoodsReceivedItemData);

                        for (InventoryItemDrilldown dd : goodsReceivedItemData.getDrillDowns()) {
                            if (BigDecimal.ZERO.compareTo(dd.getRejection()) < 0) {
                                rejectedGoodsReceivedItemData.getItemDrilldowns().add(SCMDataConverter
                                        .convertToGoodsReceivedItemDrilldown(dd, rejectedGoodsReceivedItemData));
                            }
                        }

                        // add to rejected items map to send notification
                        rejectedItems.put(goodsReceivedItemData.getId(), rejectedGoodsReceivedItemData);

                        // persist Goods Received ! check for child
                        if (rejectedGoodReceivedData.getGoodsReceivedItemDatas() != null
                                && !rejectedGoodReceivedData.getGoodsReceivedItemDatas().isEmpty()) {
                            goodsReceiveManagementDao.add(rejectedGoodReceivedData, false);
                            addRejectedGrItem(rejectedGrItems,rejectedGoodsReceivedItemData,rejectedGoodReceivedData);
                        }
                    }
                }
            }
            goodsReceiveManagementDao.flush();

            // send notification of rejected items for a GR in case of reject of
            // GR items is done
            UnitBasicDetail generatedFor = masterDataCache.getUnitBasicDetail(goodsReceivedData.getGenerationUnitId());
            if (generatedFor != null && !rejectedItems.keySet().isEmpty()
                    && (SCMUtil.isWareHouse(generatedFor.getCategory())
                    || SCMUtil.isKitchen(generatedFor.getCategory()))) {
                notificationService.sendGrRejectionNotification(goodsReceivedData, rejectedItems);
            }
        }
    }

    private void addRejectedGrItem(List<RejectedGrItem> rejectedGrItems, GoodsReceivedItemData rejectedGoodsReceivedItemData, GoodsReceivedData rejectedGoodReceivedData) {
        try {
            RejectedGrItem rejectedGrItem = new RejectedGrItem();
            GoodsReceivedData originalGoodsReceivedData = getOriginalGrData(rejectedGoodReceivedData);
            rejectedGrItem.setGrId(rejectedGoodReceivedData.getId());
            rejectedGrItem.setOriginalGrId(originalGoodsReceivedData.getId());
            rejectedGrItem.setToId(originalGoodsReceivedData.getTransferOrderData().getId());
            if (Objects.nonNull(originalGoodsReceivedData.getRequestOrderData()) && Objects.nonNull(originalGoodsReceivedData.getRequestOrderData().getId())) {
                rejectedGrItem.setRoId(originalGoodsReceivedData.getRequestOrderData().getId());
            }
            rejectedGrItem.setGrItemId(rejectedGoodsReceivedItemData.getId());
            Map<String ,Integer> originalIdMap= getOriginalGrToRoItemId(originalGoodsReceivedData,rejectedGoodsReceivedItemData);
            if (Objects.nonNull(originalIdMap.get("grItemId"))) {
                rejectedGrItem.setOriginalGrItemId(originalIdMap.get("grItemId"));
            }
            if (Objects.nonNull(originalIdMap.get("toItemId"))) {
                rejectedGrItem.setToItemId(originalIdMap.get("toItemId"));
            }
            if (Objects.nonNull(originalIdMap.get("roItemId"))) {
                rejectedGrItem.setRoItemId(originalIdMap.get("roItemId"));
            }
            rejectedGrItem.setRejectedQuantity(rejectedGoodsReceivedItemData.getTransferredQuantity());
            rejectedGrItem.setRejectedBy(masterDataCache.getEmployee(rejectedGoodReceivedData.getGeneratedBy()));
            rejectedGrItem.setRejectedUnitId(rejectedGoodReceivedData.getGenerationUnitId());
            rejectedGrItem.setSkuId(rejectedGoodsReceivedItemData.getSkuId());
            rejectedGrItem.setSkuName(rejectedGoodsReceivedItemData.getSkuName());
            rejectedGrItems.add(rejectedGrItem);
        }
        catch (Exception e) {
            log.error("Exception Occurred While adding Rejected Gr Items :: ",e);
        }
    }

    private Map<String ,Integer> getOriginalGrToRoItemId(GoodsReceivedData originalGoodsReceivedData, GoodsReceivedItemData rejectedGoodsReceivedItemData) {
        Map<String ,Integer> originalIdMap = new HashMap<>();
        try {
            for (GoodsReceivedItemData itemData : originalGoodsReceivedData.getGoodsReceivedItemDatas()) {
                if (rejectedGoodsReceivedItemData.getSkuId() == itemData.getSkuId()) {
                    originalIdMap.put("grItemId",itemData.getId());
                    originalIdMap.put("toItemId",itemData.getTransferOrderItemData().getId());
                    if (Objects.nonNull(itemData.getRequestOrderItemData()) && Objects.nonNull(itemData.getRequestOrderItemData().getId())) {
                        originalIdMap.put("roItemId", itemData.getRequestOrderItemData().getId());
                    }
                    return originalIdMap;
                }
            }
        }
        catch (Exception e) {
            log.error("Error Occurred While getting Original Gr Item Id :: ",e);
        }
        return originalIdMap;
    }

    private void createGRforDeclinedItems(GoodsReceived goodsReceived, GoodsReceivedData goodsReceivedData) throws SumoException {
        if (SCMOrderStatus.CANCELLED.name().equalsIgnoreCase(goodsReceivedData.getStatus())) {
            GoodsReceivedData declinedGoodReceivedData = new GoodsReceivedData();
            declinedGoodReceivedData.setGeneratedBy(goodsReceived.getCancelledBy().getId());
            declinedGoodReceivedData.setGeneratedForUnitId(goodsReceivedData.getGenerationUnitId());
            declinedGoodReceivedData.setGenerationUnitId(goodsReceivedData.getGeneratedForUnitId());
            declinedGoodReceivedData.setReceivingCompanyId(goodsReceivedData.getSourceCompanyId());
            declinedGoodReceivedData.setSourceCompanyId(goodsReceivedData.getReceivingCompanyId());
            declinedGoodReceivedData.setComment(goodsReceivedData.getComment());
            declinedGoodReceivedData.setGenerationTime(SCMUtil.getCurrentTimestamp());
            declinedGoodReceivedData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
            declinedGoodReceivedData.setIsRejectedGR(AppUtils.setStatus(true));
            declinedGoodReceivedData.setStatus(SCMOrderStatus.CREATED.name());
            declinedGoodReceivedData.setAutoGenerated(AppUtils.setStatus(true));
            declinedGoodReceivedData.setParentGR(goodsReceivedData);
            declinedGoodReceivedData.setParentGRComment(goodsReceivedData.getRejectGRComment());
            declinedGoodReceivedData.setTransferOrderType(goodsReceivedData.getTransferOrderType());
            for (GoodsReceivedItem goodsReceivedItemData : goodsReceived.getGoodsReceivedItems()) {
                for (SCMOrderPackaging pkd : goodsReceivedItemData.getPackagingDetails()) {
                    GoodsReceivedItemData declinedGoodsReceivedItemData = copyGoodsReceivedItemDataForDecline(
                            goodsReceivedItemData, declinedGoodReceivedData);
                    declinedGoodReceivedData.getGoodsReceivedItemDatas().add(declinedGoodsReceivedItemData);
                    addSCMOrderPackagingDataDecline(pkd, declinedGoodsReceivedItemData);
                    for (GoodsReceivedItemData itemData : goodsReceivedData.getGoodsReceivedItemDatas()) {
                        if (goodsReceivedItemData.getId().equals(itemData.getId())) {
                            for (GoodsReceivedItemDrilldown dd : itemData.getItemDrilldowns()) {
                                getItemDrilldown(dd, declinedGoodsReceivedItemData);
                            }
                        }
                    }
                }
            }

            // persist Goods Received ! check for child
            if (declinedGoodReceivedData.getGoodsReceivedItemDatas() != null
                    && !declinedGoodReceivedData.getGoodsReceivedItemDatas().isEmpty()) {
                goodsReceiveManagementDao.add(declinedGoodReceivedData, true);
            }
        }
    }

    private void getItemDrilldown(GoodsReceivedItemDrilldown dd, GoodsReceivedItemData data) {
        GoodsReceivedItemDrilldown toDrilldown = new GoodsReceivedItemDrilldown();
        toDrilldown.setReceivedItemData(data);
        toDrilldown.setQuantity(dd.getQuantity());
        toDrilldown.setPrice(dd.getPrice());
        toDrilldown.setAddTime(AppUtils.getCurrentTimestamp());
        toDrilldown.setExpiryDate(dd.getExpiryDate());
        data.getItemDrilldowns().add(toDrilldown);
    }

    private WastageEvent createWastage(GoodsReceivedData goodsReceivedData, GoodsReceivedItemData goodsReceivedItemData,
                                       SCMOrderPackagingData scmOrderPackagingData) {
        boolean isCafe = SCMUtil
                .isCafe(masterDataCache.getUnitBasicDetail(goodsReceivedData.getGeneratedForUnitId()).getCategory());
        WastageEvent event = new WastageEvent();
        event.setLinkedGrId(goodsReceivedData.getId());
        event.setBusinessDate(SCMUtil.getDate(goodsReceivedData.getLastUpdateTime()));
        event.setGenerationTime(SCMUtil.getCurrentTimestamp());
        event.setGrReason(goodsReceivedData.getComment());
        WastageData wastage = new WastageData();
        SkuDefinition skuDef = scmCache.getSkuDefinitions().get(goodsReceivedItemData.getSkuId());
        ProductDefinition productDef = scmCache.getProductDefinitions().get(skuDef.getLinkedProduct().getId());
        ProductBasicDetail product = SCMDataConverter.convert(productDef);
        wastage.setProduct(product);
        wastage.setQuantity(SCMUtil.multiply(scmOrderPackagingData.getNumberOfUnitsReceived(),
                scmOrderPackagingData.getPackagingDefinitionData().getConversionRatio()));
        wastage.setPrice(goodsReceivedItemData.getNegotiatedUnitPrice());
        wastage.setComment(goodsReceivedData.getComment());
        wastage.setCost(SCMUtil.multiply(wastage.getQuantity(), goodsReceivedItemData.getNegotiatedUnitPrice()));
        wastage.setProductId(product.getProductId());

        for (GoodsReceivedItemDrilldown itemDrillDown : goodsReceivedItemData.getItemDrilldowns()) {
            wastage.getDrillDowns().add(SCMDataConverter.convert(itemDrillDown));
        }

        if (!isCafe) {
            SkuBasicDetail sku = SCMDataConverter.convert(skuDef);
            wastage.setSku(sku);
            wastage.setSkuId(sku.getSkuId());
        }

        event.getItems().add(wastage);
        event.setStatus(StockEventStatus.SETTLED);
        event.setUnitId(goodsReceivedData.getGeneratedForUnitId());
        event.setGeneratedBy(SCMServiceConstants.SYSTEM_USER);
        return event;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelGoodsReceivedDetail(Integer grId) {
        GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class, grId);
        if (goodsReceivedData != null) {
            goodsReceivedData.setStatus(SCMOrderStatus.CANCELLED.value());
            goodsReceivedData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
            goodsReceivedData = (GoodsReceivedData) goodsReceiveManagementDao.update(goodsReceivedData, true);
            if (goodsReceivedData != null) {
                return true;
            }
        } else {
            log.error("Good Receive Entry with Id: {} not found!", grId);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GoodsReceived> findGoodsReceived(Integer generationUnitId, Integer generatedForUnitId, Date startDate,
                                                 Date endDate, SCMOrderStatus status, Integer goodsReceiveOrderId , Integer skuId) {
        List<GoodsReceivedData> goodsReceivedDataList = goodsReceiveManagementDao.findGoodsReceived(generationUnitId,
                generatedForUnitId, startDate, endDate, status, goodsReceiveOrderId);
        return filterGRsBySkuId(getGrListWithGrItemsFromGrDataList(goodsReceivedDataList,Objects.nonNull(skuId)),skuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GoodsReceived> findMilkGrsForPayment(Integer deliveryUnitId, Date startDate, Date endDate, Integer vendorId) {
        List<GoodsReceivedData> goodsReceivedDataList = goodsReceiveManagementDao.findGoodsReceived(deliveryUnitId,
                deliveryUnitId, startDate, endDate, SCMOrderStatus.SETTLED, null);
        goodsReceivedDataList = goodsReceivedDataList.stream().filter(gr -> (Objects.nonNull(gr.getRequestOrderData()) &&
                gr.getRequestOrderData().getIsSpecialOrder().equals(AppConstants.YES) && Objects.isNull(gr.getInvoiceId()) && Objects.nonNull(gr.getRequestOrderData().getVendorId()) &&
                gr.getRequestOrderData().getVendorId().equals(vendorId))).collect(Collectors.toList());
        List<GoodsReceived> filteredMilkGrs = filterByDiaryAndMilkItems(getGrListWithGrItemsFromGrDataList(goodsReceivedDataList, true));

        return addTaxField(filteredMilkGrs);

    }

    private List<GoodsReceived> filterByDiaryAndMilkItems(List<GoodsReceived> grs) {
        List<Integer> milkAndBakeryProductIds = new ArrayList<Integer>(Arrays.asList(100234, 100246, 100259, 100776, 100856, 101303, 103083, 103188, 103306, 103307, 103345,
                100811, 101302, 100145));
        return grs.stream().filter(gr -> {
            Boolean valid = false;
            for (GoodsReceivedItem grItem : gr.getGoodsReceivedItems()) {
                if (milkAndBakeryProductIds.contains(grItem.getProductId())) {
                    valid = true;
                    break;
                }
            }
            return valid;
        }).collect(Collectors.toList());

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SpecializedOrderInvoice> getMilkInvoicesForPayment(Integer deliveryUnitId, Date startDate, Date endDate, Integer vendorId) {
        List<SpecializedOrderInvoiceData> specializedOrderInvoiceDataList = goodsReceiveManagementDao.findMilkInvoicesForPayment(deliveryUnitId, vendorId, startDate, endDate);
        List<SpecializedOrderInvoice> resultList = new ArrayList<>();
        for (SpecializedOrderInvoiceData specializedOrderInvoiceData : specializedOrderInvoiceDataList) {
            List<GoodsReceivedData> goodsReceivedDataList = goodsReceiveManagementDao.findGrsByInvoiceId(specializedOrderInvoiceData.getUnitId(),
                    specializedOrderInvoiceData.getSpecializedOrderInvoiceId());
            resultList.add(convert(specializedOrderInvoiceData, goodsReceivedDataList));
        }
        return resultList;
    }

    private SpecializedOrderInvoice convert(SpecializedOrderInvoiceData specializedOrderInvoiceData, List<GoodsReceivedData> goodsReceivedDataList) {
        SpecializedOrderInvoice specializedOrderInvoice = new SpecializedOrderInvoice();
        specializedOrderInvoice.setUnitId(specializedOrderInvoiceData.getUnitId());
        specializedOrderInvoice.setInvoiceUrl(specializedOrderInvoiceData.getInvoiceUrl());
        specializedOrderInvoice.setInvoiceId(specializedOrderInvoiceData.getInvoiceId());
        specializedOrderInvoice.setDocumentId(specializedOrderInvoiceData.getDocumentId());
        specializedOrderInvoice.setSpecializedOrderInvoiceId(specializedOrderInvoiceData.getSpecializedOrderInvoiceId());
        specializedOrderInvoice.setVendorId(specializedOrderInvoiceData.getVendorId());
        specializedOrderInvoice.setGenerationTime(specializedOrderInvoiceData.getGenerationTime());
        List<GoodsReceived> grs = getGrListWithGrItemsFromGrDataList(goodsReceivedDataList, true);
        grs = addTaxField(grs);
        specializedOrderInvoice.setGoodsReceived(getAggregatedObject(grs));


        return specializedOrderInvoice;
    }

    @Override
    public GoodsReceived getAggregatedObject(List<GoodsReceived> goodsReceivedList) {
        Map<Integer, GoodsReceivedItem> goodsReceivedItemMap = new HashMap<>();
        GoodsReceived goodsReceived = goodsReceivedList.get(0);
        Float totalAmount = Float.valueOf(0);
        for (GoodsReceived gr : goodsReceivedList) {
            totalAmount = totalAmount + gr.getTotalAmount();
            for (GoodsReceivedItem grItem : gr.getGoodsReceivedItems()) {
                if (!goodsReceivedItemMap.containsKey(grItem.getSkuId())) {
                    goodsReceivedItemMap.put(grItem.getSkuId(), grItem);
                } else {
                    GoodsReceivedItem item = goodsReceivedItemMap.get(grItem.getSkuId());
                    item.setReceivedQuantity(item.getReceivedQuantity() + grItem.getReceivedQuantity());
                    item.setTransferredQuantity(item.getTransferredQuantity() + grItem.getTransferredQuantity());
                    item.setCalculatedAmount(item.getCalculatedAmount() + grItem.getCalculatedAmount());
                    item.setTaxAmount(item.getTaxAmount() + grItem.getTaxAmount());
                    goodsReceivedItemMap.put(grItem.getSkuId(), item);
                }

            }
        }
        goodsReceived.setTotalAmount(totalAmount);
        goodsReceived.setGoodsReceivedItems(new ArrayList<>(goodsReceivedItemMap.values()));
        for (GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()) {
            if (Objects.nonNull(goodsReceivedItem.getTaxes()) && goodsReceivedItem.getTaxes().size() > 0) {
                BigDecimal taxableAmount = BigDecimal.valueOf(goodsReceivedItem.getUnitPrice() * goodsReceivedItem.getReceivedQuantity());
                for (TaxDetail taxDetail : goodsReceivedItem.getTaxes()) {
                    taxDetail.setTotal(taxableAmount);
                    taxDetail.setTaxable(taxableAmount);
                    BigDecimal taxValue = SCMUtil.divideWithScale10(SCMUtil.multiplyWithScale10(taxableAmount, taxDetail.getPercentage()), BigDecimal.valueOf(100));
                    ;
                    taxDetail.setValue(taxValue);
                }

            }
        }
        return goodsReceived;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean createGrForExcessPrQty(Integer invoiceId , Integer unitId , Integer vendorId) throws SumoException, InventoryUpdateException, ParseException, DataNotFoundException, TransferOrderCreationException {
        List<GoodsReceivedData> goodsReceivedDataList = goodsReceiveManagementDao.findGrsByInvoiceId(unitId, invoiceId);
        AtomicReference<Boolean> isGrRequired = new AtomicReference<>(false);
        Map<Integer, InvoiceExcessQuantity> invoiceExcessQuantityMap = goodsReceiveManagementDao.findPrExcessQuantity(unitId, invoiceId).stream().map(invoice -> {
                    if (invoice.getExcessQty().compareTo(BigDecimal.ZERO) > 0) {
                        isGrRequired.set(true);
                    }
                    return invoice;
                }).
                collect(Collectors.toMap(InvoiceExcessQuantity::getSkuId, Function.identity(), (i1,i2) ->{
                    if(i1.getExcessQty().compareTo(BigDecimal.ZERO) > 0){
                        return  i1;
                    }else{
                       return i2;
                    }
                }));
        if(isGrRequired.get().equals(Boolean.FALSE)){
            return false;
        }


        GoodsReceivedData goodsReceivedData = saveAggregatedGrObjectForDeviation(goodsReceivedDataList, invoiceExcessQuantityMap);
        GoodsReceived goodsReceived = SCMDataConverter.convert(goodsReceivedData, new IdCodeName(goodsReceivedData.getReceivedBy(), "", ""),
                new IdCodeName(goodsReceivedData.getReceivedBy(), "", ""), null,
                new IdCodeName(goodsReceivedData.getGenerationUnitId(), "", ""), new IdCodeName(goodsReceivedData.getGeneratedForUnitId(), "", ""),
                true, scmCache, masterDataCache);

        //Auto RO
        RequestOrderData requestOrderData = requestOrderManagementService.createROFromGR(goodsReceived,vendorId);
        goodsReceived.setRequestOrderId(requestOrderData.getId());

        Map<Integer,Integer> productRoItemMap = new HashMap<>();
        for(RequestOrderItemData roItem : requestOrderData.getRequestOrderItemDatas()){
            productRoItemMap.put(roItem.getProductId(),roItem.getId());
        }


        //Auto TO
        Map<Integer,Map<Integer,GoodsReceivedItem>> skuItemMap = new HashMap<>();
        skuItemMap.put(goodsReceived.getGenerationUnitId().getId(),new HashMap<>());
        for(GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()){
            skuItemMap.get(goodsReceived.getGenerationUnitId().getId()).put(goodsReceivedItem.getSkuId(),goodsReceivedItem);
        }

        TransferOrder transferOrder =  transferOrderManagementService.getTransferObjects(skuItemMap,unitId,AppConstants.SYSTEM_EMPLOYEE_ID,false).get(0);
        transferOrder.setRequestOrderId(requestOrderData.getId());
        transferOrder.setVendorId(vendorId);
        for(TransferOrderItem toItem : transferOrder.getTransferOrderItems()){
            toItem.setRequestOrderItemId(productRoItemMap.get(toItem.getProductId()));
        }
        int sourceCompanyId = masterDataCache.getUnit(unitId).getCompany()
                .getId();
        int receivingCompanyId = masterDataCache.getUnit(goodsReceived.getGenerationUnitId().getId()).getCompany()
                .getId();
        boolean isDifferentCompany = sourceCompanyId != receivingCompanyId;

        String toInvoiceId = transferOrderManagementService.getInvoiceId(unitId,
                goodsReceived.getGenerationUnitId().getId(), isDifferentCompany);
        Integer toId = transferOrderManagementService.createTransferOrder(transferOrder, false, toInvoiceId);

        //Auto GR

        GoodsReceivedData grData =  transferOrderManagementService.getGrByTO(toId);

        grData.setInvoiceId(invoiceId);
        grData.setAutoGenerated(AppUtils.setStatus(true));

        goodsReceiveManagementDao.update(grData,true);


        GoodsReceived gr = SCMDataConverter.convert(grData, new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", ""),
                new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", ""), null,
                new IdCodeName(goodsReceivedData.getGenerationUnitId(), "", ""), new IdCodeName(grData.getGeneratedForUnitId(), "", ""),
                true, scmCache, masterDataCache);
        gr.setTransferOrderType(TransferOrderType.REGULAR_TRANSFER);
        gr.setAutoGenerated(true);
        gr.setInvoiceId(invoiceId);
        for(GoodsReceivedItem item : gr.getGoodsReceivedItems()){
            item.setReceivedQuantity(item.getTransferredQuantity());
            for(SCMOrderPackaging packaging : item.getPackagingDetails()){
                packaging.setReceivedQuantity(packaging.getTransferredQuantity());
            }
        }

        settleGoodsReceivedDetail(gr);

        return  true;

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean autoGRUrgentSpecializedOrder(Integer toId) throws InventoryUpdateException, ParseException, SumoException {
        try {
            log.info("Trying To Auto GR Orders with transfer order Id ::::{}",toId);

            GoodsReceivedData grData =  transferOrderManagementService.getGrByTO(toId);
            grData.setAutoGenerated(AppUtils.setStatus(true));
            goodsReceiveManagementDao.update(grData,true);
            GoodsReceived gr = SCMDataConverter.convert(grData, new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", ""),
                    new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", ""), null,
                    new IdCodeName(grData.getGenerationUnitId(), "", ""), new IdCodeName(grData.getGeneratedForUnitId(), "", ""),
                    true, scmCache, masterDataCache);
            gr.setTransferOrderType(TransferOrderType.REGULAR_TRANSFER);
            gr.setAutoGenerated(true);
            for(GoodsReceivedItem item : gr.getGoodsReceivedItems()){
                item.setReceivedQuantity(item.getTransferredQuantity());
                for(SCMOrderPackaging packaging : item.getPackagingDetails()){
                    packaging.setReceivedQuantity(packaging.getTransferredQuantity());
                    if(Objects.nonNull(packaging.getReceivedQuantity()/packaging.getConversionRatio())){
                        packaging.setNumberOfUnitsReceived(packaging.getReceivedQuantity()/packaging.getConversionRatio());
                    }
                    else {
                        throw new SumoException("Error While Getting Number Of Units Received");
                    }
                }
            }
            settleGoodsReceivedDetail(gr);
            return  true;
        }
        catch (SumoException e){
            log.error("Error While Creating Auto GR For Specialized order with TO :: {}",toId);
            log.error("ERROR :::",e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean autoGRSpecializedOrder(Integer toId, TransferOrder transferOrder) throws InventoryUpdateException, ParseException, SumoException {
        try {
            log.info("Trying To Auto GR Orders with transfer order Id ::::{}", toId);
            GoodsReceivedData grData = transferOrderManagementService.getGrByTO(toId);
            goodsReceiveManagementDao.update(grData, true);
            GoodsReceived gr = SCMDataConverter.convert(grData, new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", ""),
                    new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", ""), null,
                    new IdCodeName(grData.getGenerationUnitId(), "", ""), new IdCodeName(grData.getGeneratedForUnitId(), "", ""),
                    true, scmCache, masterDataCache);
            gr.setTransferOrderType(TransferOrderType.REGULAR_TRANSFER);
            gr.setReceivedBy(transferOrder.getGeneratedBy());
            gr.setDocIdsPorImages(transferOrder.getDocIdsPorImages());
            for (GoodsReceivedItem item : gr.getGoodsReceivedItems()) {
                BigDecimal rejectedQuantity = BigDecimal.ZERO;
                item.setReceivedQuantity(item.getPackagingDetails().get(0).getReceivedQuantity());
                for (SCMOrderPackaging scmOrderPackaging : item.getPackagingDetails()) {
                    scmOrderPackaging.setNumberOfUnitsRejected(scmOrderPackaging.getNumberOfUnitsPacked()-scmOrderPackaging.getNumberOfUnitsReceived());
                    rejectedQuantity = BigDecimal.valueOf(AppUtils.multiply(scmOrderPackaging.getNumberOfUnitsRejected(), scmOrderPackaging.getConversionRatio()));
                }
                for (InventoryItemDrilldown inventoryItemDrilldown : item.getDrillDowns()) {
                    if (Objects.nonNull(inventoryItemDrilldown.getQuantity()) && rejectedQuantity.compareTo(BigDecimal.ZERO) > 0) {
                        if (inventoryItemDrilldown.getQuantity().compareTo(rejectedQuantity) > 0) {
                            inventoryItemDrilldown.setRejection(rejectedQuantity);
                            rejectedQuantity = BigDecimal.ZERO;
                        } else {
                            inventoryItemDrilldown.setRejection(inventoryItemDrilldown.getQuantity());
                            rejectedQuantity = rejectedQuantity.subtract(inventoryItemDrilldown.getQuantity());
                        }
                    }
                }
                List<InventoryItemDrilldown> packagingInventoryList = new ArrayList<>();
                for (InventoryItemDrilldown inventoryItemDrilldown : item.getDrillDowns()) {
                    InventoryItemDrilldown packagingInventory = new InventoryItemDrilldown();
                    packagingInventory.setKeyId(inventoryItemDrilldown.getKeyId());
                    packagingInventory.setKeyType(inventoryItemDrilldown.getKeyType());
                    packagingInventory.setPrice(inventoryItemDrilldown.getPrice());
                    packagingInventory.setQuantity(inventoryItemDrilldown.getRejection());
                    packagingInventory.setExpiryDate(inventoryItemDrilldown.getExpiryDate());
                    packagingInventoryList.add(packagingInventory);
                }
                item.getPackagingDetails().get(0).setExpiryDrillDown(packagingInventoryList);
            }

            settleGoodsReceivedDetail(gr);
            return true;
        } catch (SumoException e) {
            log.error("Error While Creating Auto GR For TO :: {}", toId);
            log.error("ERROR :::", e);
            throw e;
        }
    }

        @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadPOD(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, Integer grId) {
        Unit generatedForUnitId = masterDataCache.getUnit(getGoodsReceivedDetail(grId).getGeneratedForUnitId().getId());
        Unit generationUnitId = masterDataCache.getUnit(getGoodsReceivedDetail(grId).getGenerationUnitId().getId());
        if(generationUnitId.getFamily().equals(generatedForUnitId.getFamily()) &&
                (Objects.equals(generationUnitId.getFamily().value(), UnitCategory.WAREHOUSE.value()) || Objects.equals(generationUnitId.getFamily().value(), UnitCategory.KITCHEN.value()))
                &&(Objects.equals(generatedForUnitId.getFamily().value(), UnitCategory.WAREHOUSE.value()) || Objects.equals(generatedForUnitId.getFamily().value(), UnitCategory.KITCHEN.value()))
                && generationUnitId.getAddress().getCity()!=generatedForUnitId.getAddress().getCity()) {
            String fileName = "INTERNAL_GR_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                    + mimeType.name().toLowerCase();
            log.info("File name is {}", fileName);
            String baseDir = "INTERNAL_GR_" + File.separator + SCMUtil.getCurrentYear() + File.separator
                    + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
            File destinationFile = null;
            MultipartFile compressedFile = null;

            if (mimeType.equals(MimeType.PDF)) {
                log.info("#########Uploaded FIle is PDF , Trying To Compress ###### ");
                //Compression of PDF
                try {
                    compressedFile = paymentRequestManagementService.compressPdf(destinationFile, file, compressedFile);
                    log.info("###### size before compression : {}", file.getSize());
                    log.info("###### Size After Compression : {}", compressedFile.getSize());
                } catch (SumoException e) {
                    log.info("######Error While Compressing File , Uploading Without Compression :::::", e);
                    compressedFile = null;
                } catch (DocumentException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else if (mimeType.equals(MimeType.PNG)) {
                log.info("#######Uploaded File Is Of PNG Format. Uploading Without Compression .");

            } else {
                log.info("#######Uploaded File is Of {} Format, Trying To Compress", mimeType.extension());
                try {
                    byte[] imageByte = paymentRequestManagementService.compressImage(file, mimeType.extension());
                    compressedFile = new MockMultipartFile(file.getName(),
                            file.getOriginalFilename(), file.getContentType(), imageByte);
                    log.info("###### size before compression : {}", file.getSize());
                    log.info("###### Size After Compression : {}", compressedFile.getSize());
                } catch (Exception e) {
                    log.info("#######Error While Compressing Image , Uploading Without Compression");
                    compressedFile = null;
                }
            }
            try {
                FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, fileName, file);
                DocumentDetail documentDetail = new DocumentDetail();
                documentDetail.setMimeType(mimeType);
                documentDetail.setUploadType(docType);
                documentDetail.setFileType(type);
                documentDetail.setDocumentLink(fileName);
                documentDetail.setS3Key(s3File.getKey());
                documentDetail.setFileUrl(s3File.getUrl());
                documentDetail.setS3Bucket(s3File.getBucket());
                documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
                documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
                DocumentDetailData data = goodsReceiveManagementDao.add(SCMDataConverter.convert(documentDetail), true);
                if (data.getDocumentId() != null) {
                    return SCMDataConverter.convert(data);
                }
            } catch (Exception e) {
                log.error("Encountered error while uploading document", e);
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadProofOfRejection(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, Integer grId,String fileNameExt) {

        String fileName = "REJECTION_GR_" +fileNameExt+"_"+ SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                + mimeType.name().toLowerCase();
        log.info("File name is {}  {}", fileName,fileNameExt);
        String baseDir = "REJECTION_GR_" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        MultipartFile compressedFile = null;

        if (mimeType.equals(MimeType.PNG)) {
            log.info("#######Uploaded File Is Of PNG Format. Uploading Without Compression .");
        } else {
            log.info("#######Uploaded File is Of {} Format, Trying To Compress", mimeType.extension());
            try {
                byte[] imageByte = paymentRequestManagementService.compressImage(file, mimeType.extension());
                compressedFile = new MockMultipartFile(file.getName(),
                        file.getOriginalFilename(), file.getContentType(), imageByte);
                log.info("###### size before compression : {}", file.getSize());
                log.info("###### Size After Compression : {}", compressedFile.getSize());
            } catch (Exception e) {
                log.info("#######Error While Compressing Image , Uploading Without Compression");
                compressedFile = null;
            }
        }
        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, fileName, file);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = goodsReceiveManagementDao.add(SCMDataConverter.convert(documentDetail), true);
            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }
        } catch (Exception e) {
            log.error("Encountered error while uploading document", e);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateMilkBreadCache(GoodsReceived goodsReceived) {
        try {
            GoodsReceivedData goodsReceivedData = goodsReceiveManagementDao.find(GoodsReceivedData.class, goodsReceived.getId());
            if (Objects.nonNull(goodsReceivedData.getRequestOrderData())) {
                RequestOrderData requestOrderData = goodsReceivedData.getRequestOrderData();
                PendingMilkBread pendingMilkBread = scmCache.getPendingMilkBread(goodsReceivedData.getRequestOrderData().getRequestUnitId());
                if (Objects.nonNull(pendingMilkBread) && Objects.nonNull(requestOrderData.getIsSpecialOrder()) &&
                        requestOrderData.getIsSpecialOrder().equalsIgnoreCase(AppConstants.YES) && Objects.nonNull(requestOrderData.getIsNotified()) &&
                        requestOrderData.getIsNotified().equalsIgnoreCase(AppConstants.YES) && requestOrderData.getFulfillmentDate().compareTo(AppUtils.getBusinessDate()) == 0
                        && pendingMilkBread.getRoIds().contains(requestOrderData.getId())) {
                    Set<Integer> roIds = pendingMilkBread.getRoIds();
                    log.info("Removing RO ID : {} from milk bread cache", requestOrderData.getId());
                    roIds.remove(requestOrderData.getId());
                    if (roIds.isEmpty()) {
                        pendingMilkBread.setReceivingDone(Boolean.TRUE);
                        pendingMilkBread.getVendorIds().clear();
                        pendingMilkBread.setMaxLimitTime(null);
                    } else {
                        pendingMilkBread.setReceivingDone(Boolean.FALSE);
                    }
                    scmCache.setPendingMilkBread(requestOrderData.getRequestUnitId(), pendingMilkBread);
                }
            }
        } catch (Exception e) {
            log.error("Exception Occurred While updating the scm milk bread cache from GR : ",e);
        }
    }

    private GoodsReceivedData copyGoodReceivedData(GoodsReceived input) {
        GoodsReceivedData result = new GoodsReceivedData();
        result.setGenerationTime(input.getGenerationTime());
        result.setGenerationUnitId(input.getGenerationUnitId().getId());
        result.setGeneratedForUnitId(input.getGeneratedForUnitId().getId());
        result.setSourceCompanyId(input.getSourceCompany().getId());
        result.setReceivingCompanyId(input.getReceivingCompany().getId());
        result.setReceivedBy(input.getReceivedBy().getId());
        result.setGeneratedBy(input.getGeneratedBy().getId());
        result.setStatus(input.getStatus().value());
        result.setLastUpdateTime(input.getLastUpdateTime());
        result.setTotalAmount(BigDecimal.valueOf(input.getTotalAmount()));
        result.setTransferOrderType(input.getTransferOrderType().value());
        result.setInvoiceId(input.getInvoiceId());
        return result;
    }

    private GoodsReceivedItemData copyGrItemData(GoodsReceivedItem input) {
        GoodsReceivedItemData result = new GoodsReceivedItemData();
        result.setSkuId(input.getSkuId());
        result.setSkuName(input.getSkuName());
        result.setTransferredQuantity(BigDecimal.valueOf(input.getTransferredQuantity()));
        result.setReceivedQuantity(BigDecimal.valueOf(input.getReceivedQuantity()));
        result.setUnitOfMeasure(input.getUnitOfMeasure());
        result.setUnitPrice(BigDecimal.valueOf(input.getUnitPrice()));
        result.setNegotiatedUnitPrice(BigDecimal.valueOf(input.getNegotiatedUnitPrice()));
        result.setCalculatedAmount(BigDecimal.valueOf(input.getCalculatedAmount()));
        if (Objects.nonNull(input.getTaxAmount())) {
            result.setTaxAmount(BigDecimal.valueOf(input.getTaxAmount()));
        }
        return result;
    }

    private GoodsReceivedItemDrilldown copyGrItemDrillDown(InventoryItemDrilldown input) {
        GoodsReceivedItemDrilldown result = new GoodsReceivedItemDrilldown();
        result.setPrice(input.getPrice());
        result.setQuantity(input.getQuantity());
        result.setRejection(input.getRejection());
        result.setAddTime(SCMUtil.getCurrentTimestamp());
        result.setExpiryDate(input.getExpiryDate());
        return result;
    }

    private SCMOrderPackagingData copyGrItemPackagingData(SCMOrderPackaging input) {
        SCMOrderPackagingData result = new SCMOrderPackagingData();
        result.setPackagingDefinitionData(SCMDataConverter.convert(input.getPackagingDefinitionData()));
        result.setNumberOfUnitsPacked(BigDecimal.valueOf(input.getNumberOfUnitsPacked()));
        if (Objects.nonNull(input.getNumberOfUnitsReceived())) {
            result.setNumberOfUnitsReceived(BigDecimal.valueOf(input.getNumberOfUnitsReceived()));
        }
        if (Objects.nonNull(input.getNumberOfUnitsRejected())) {
            result.setNumberOfUnitsRejected(BigDecimal.valueOf(input.getNumberOfUnitsRejected()));
        }
        result.setTransferredQuantity(BigDecimal.valueOf(input.getTransferredQuantity()));
        if (Objects.nonNull(input.getReceivedQuantity())) {
            result.setReceivedQuantity(BigDecimal.valueOf(input.getReceivedQuantity()));
        }
        result.setConversionRatio(BigDecimal.valueOf(input.getConversionRatio()));
        result.setPricePerUnit(BigDecimal.valueOf(input.getPricePerUnit()));
        return result;
    }





    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public GoodsReceivedData saveAggregatedGrObjectForDeviation(List<GoodsReceivedData> goodsReceivedList, Map<Integer, InvoiceExcessQuantity> invoiceExcessQuantityMap)
            throws SumoException {
        Map<Integer, GoodsReceivedItem> goodsReceivedItemMap = new HashMap<>();
        GoodsReceivedData goodsReceivedData = goodsReceivedList.get(0);
        GoodsReceived goodsReceived = SCMDataConverter.convert(goodsReceivedData, new IdCodeName(goodsReceivedData.getReceivedBy(), "", ""),
                new IdCodeName(goodsReceivedData.getReceivedBy(), "", ""), null,
                new IdCodeName(goodsReceivedData.getGenerationUnitId(), "", ""), new IdCodeName(goodsReceivedData.getGeneratedForUnitId(), "", ""),
                true, scmCache, masterDataCache);
        goodsReceived.setInvoiceId(goodsReceivedData.getInvoiceId());


        BigDecimal totalAmount = BigDecimal.valueOf(0);


        goodsReceived.setGenerationTime(SCMUtil.getCurrentTimestamp());
        goodsReceived.setStatus(SCMOrderStatus.CREATED);
        goodsReceived.setRequestOrderId(null);
        goodsReceived.setTransferOrderId(null);
        goodsReceived.setLastUpdateTime(SCMUtil.getCurrentTimestamp());


        GoodsReceivedData clonedGr = copyGoodReceivedData(goodsReceived);
        clonedGr.setAutoGenerated(AppUtils.setStatus(true));
        //clonedGr = goodsReceiveManagementDao.add(clonedGr, true);
        List<GoodsReceivedItemData> goodsReceivedItemDataList = new ArrayList<>();

        for (GoodsReceivedItem grItem : goodsReceived.getGoodsReceivedItems()) {
            if (!invoiceExcessQuantityMap.containsKey(grItem.getSkuId())) {
                continue;
            }
            if (!goodsReceivedItemMap.containsKey(grItem.getSkuId())) {
                BigDecimal taxAmountPerUom = Objects.nonNull(grItem.getTaxAmount()) ? SCMUtil.divideWithScale10(BigDecimal.valueOf(grItem.getTaxAmount()),
                        BigDecimal.valueOf(grItem.getReceivedQuantity()))
                        : BigDecimal.ZERO;
                InvoiceExcessQuantity invoiceExcessQuantity = invoiceExcessQuantityMap.get(grItem.getSkuId());
                if (invoiceExcessQuantity.getExcessQty().compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }
                grItem.setReceivedQuantity(invoiceExcessQuantity.getExcessQty().floatValue());
                grItem.setTransferredQuantity(invoiceExcessQuantity.getExcessQty().floatValue());
                grItem.setTaxAmount(SCMUtil.multiplyWithScale10(taxAmountPerUom, invoiceExcessQuantity.getExcessQty()).floatValue());
                grItem.setCalculatedAmount(SCMUtil.multiply(invoiceExcessQuantity.getUnitPrice(), invoiceExcessQuantity.getExcessQty()).floatValue());
                grItem.setRequestOrderItemId(null);
                grItem.setTransferOrderItemId(null);


                GoodsReceivedItemData clonedGrItem = copyGrItemData(grItem);
                SkuDefinition skuDefinition = scmCache.getSkuDefinition(clonedGrItem.getSkuId());
                ProductDefinition productDefinition = scmCache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
                clonedGrItem.setCategoryId(productDefinition.getCategoryDefinition().getId());
                clonedGrItem.setSubCategoryId(productDefinition.getSubCategoryDefinition().getId());
                clonedGrItem.setGoodsReceivedData(clonedGr);

                //clonedGrItem = goodsReceiveManagementDao.add(clonedGrItem, true);

                GoodsReceivedItemDrilldown clonedItemDrillDown = new GoodsReceivedItemDrilldown();
                for (InventoryItemDrilldown goodsReceivedItemDrilldown : grItem.getDrillDowns()) {
                    goodsReceivedItemDrilldown.setQuantity(invoiceExcessQuantity.getExcessQty());
                    goodsReceivedItemDrilldown.setRejection(null);
                    clonedItemDrillDown = copyGrItemDrillDown(goodsReceivedItemDrilldown);
                    clonedItemDrillDown.setReceivedItemData(clonedGrItem);
                    //goodsReceiveManagementDao.add(clonedItemDrillDown, true);
                    break;
                }
                clonedGrItem.getItemDrilldowns().add(clonedItemDrillDown);


                SCMOrderPackagingData clonedPackagingData = new SCMOrderPackagingData();
                for (SCMOrderPackaging scmOrderPackagingData : grItem.getPackagingDetails()) {
                    BigDecimal units = SCMUtil.divideWithScale10(invoiceExcessQuantity.getExcessQty(), BigDecimal.valueOf(scmOrderPackagingData.getConversionRatio()));

                    scmOrderPackagingData.setNumberOfUnitsPacked(units.floatValue());
                    scmOrderPackagingData.setNumberOfUnitsReceived(units.floatValue());
                    scmOrderPackagingData.setNumberOfUnitsRejected(Float.valueOf(0));
                    scmOrderPackagingData.setTransferredQuantity(SCMUtil.multiplyWithScale10(units, BigDecimal.valueOf(scmOrderPackagingData.getConversionRatio())).floatValue());
                    scmOrderPackagingData.setReceivedQuantity(SCMUtil.multiplyWithScale10(units, BigDecimal.valueOf(scmOrderPackagingData.getConversionRatio())).floatValue());
                    scmOrderPackagingData.setRejectionReason(null);
                    clonedPackagingData = copyGrItemPackagingData(scmOrderPackagingData);
                    clonedPackagingData.setGoodsReceivedItemData(clonedGrItem);
                    //goodsReceiveManagementDao.add(clonedPackagingData, true);
                    break;
                }

                clonedGrItem.getPackagingDetails().add(clonedPackagingData);

                totalAmount = SCMUtil.add(BigDecimal.valueOf(grItem.getCalculatedAmount()), totalAmount);
                goodsReceivedItemMap.put(grItem.getSkuId(), grItem);
                goodsReceivedItemDataList.add(clonedGrItem);
                }

            }
        clonedGr.setTotalAmount(totalAmount);
        clonedGr.setGoodsReceivedItemDatas(goodsReceivedItemDataList);
        //clonedGr = goodsReceiveManagementDao.update(clonedGr, true);
        return clonedGr;
    }


    private List<GoodsReceived> addTaxField(List<GoodsReceived> goodsReceivedList) {
        goodsReceivedList.stream().forEach(gr -> {
            gr.getGoodsReceivedItems().stream().forEach(grItem -> {
                TransferOrderItemData transferOrderItemData = goodsReceiveManagementDao.find(TransferOrderItemData.class, grItem.getTransferOrderItemId());
                grItem.setHsnCode(transferOrderItemData.getTaxCode());
                List<TaxDetail> taxDetailList = new ArrayList<>();
                transferOrderItemData.getOrderItemTaxes().forEach(taxDetail -> {
                    taxDetailList.add(SCMDataConverter.convert(taxDetail));
                });
                grItem.setTaxes(taxDetailList);
            });
        });
        return goodsReceivedList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int createVendorGR(CreateVendorGrVO grVO) throws DataUpdationException, SumoException, InventoryUpdateException {
        Unit deliveryUnit = masterDataCache.getUnit(grVO.getDeliveryUnitId());
        VendorDetail vendorDetail = scmCache.getVendorDetail(grVO.getVendorId());
        if (InvoiceDocType.INVOICE.name().equals(grVO.getDocType().name())) {
            int grId = validateInvoiceNumber(deliveryUnit.getId(), vendorDetail.getVendorId(), grVO.getDocNumber(),
                    grVO.getForceSummit());
            if (grId > 0) {
                throw new DataUpdationException(String.format(
                        "Cannot Create Vendor GR as a GR with same Invoice Number Already existing in the system with the GR Id [%s]for the Unit : %d, vendor : %d and doc number : %s",
                        grId, deliveryUnit.getId(), vendorDetail.getVendorId(), grVO.getDocNumber()));

            }
        }
        validateForAdvanceGrs(grVO);
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendorDetail,
                grVO.getDispatchId());

        VendorGoodsReceivedData vendorGoodsReceivedData = new VendorGoodsReceivedData();
        vendorGoodsReceivedData.setVendorGRType(grVO.getVendorGrType().value());
        Date currentTime = SCMUtil.getCurrentTimestamp();
        vendorGoodsReceivedData.setDeliveryUnitId(deliveryUnit.getId());
        vendorGoodsReceivedData.setDispatchId(dispatchLocation.get().getDispatchId());
        vendorGoodsReceivedData.setGeneratedForVendor(vendorDetail.getVendorId());
        vendorGoodsReceivedData.setCreationType(grVO.getCreationType().name());
//			if(grVO.getVendorGrType().equals(VendorGrType.FIXED_ASSET_ORDER)){
//				vendorGoodsReceivedData.setGrStatus(SCMOrderStatus.INITIATED.name());
//			} else {
//				vendorGoodsReceivedData.setGrStatus(SCMOrderStatus.CREATED.name());
//			}
        //            FOR KITCHEN TYPE UNIT GR GET CREATED AND APPROVED AT SAME TIME
        if (deliveryUnit.getFamily().equals(UnitCategory.KITCHEN)) {
            if (!grVO.getVendorGrType().equals(VendorGrType.FIXED_ASSET_ORDER)) {
                vendorGoodsReceivedData.setGrStatus(SCMOrderStatus.CREATED.name());
            } else {
                vendorGoodsReceivedData.setGrStatus(SCMOrderStatus.INITIATED.name());
            }
            vendorGoodsReceivedData.setApprovedBy(grVO.getUserId());
        } else {
            vendorGoodsReceivedData.setGrStatus(SCMOrderStatus.INITIATED.name());
        }
        vendorGoodsReceivedData.setCreatedBy(grVO.getUserId());
        vendorGoodsReceivedData.setCreatedAt(currentTime);
        vendorGoodsReceivedData.setUpdatedAt(currentTime);
        vendorGoodsReceivedData.setExtraCharges(grVO.getExtraCharges());
        vendorGoodsReceivedData.setDocNumber(grVO.getDocNumber());
        vendorGoodsReceivedData.setDocumentDate(SCMUtil.parseDate(grVO.getDocDate()));
        vendorGoodsReceivedData.setCompanyId(deliveryUnit.getCompany().getId());
        vendorGoodsReceivedData.setAmountMatched(grVO.isAmountMatched() ? SCMServiceConstants.SCM_CONSTANT_YES
                : SCMServiceConstants.SCM_CONSTANT_NO);
        vendorGoodsReceivedData.setDocType(grVO.getDocType().name());


        vendorGoodsReceivedData = addAmountDetails(grVO, vendorGoodsReceivedData);
        // upload document for the time being disabled for Vendor GR
        if (grVO.getUploadId() != null) {
            DocumentDetailData grDocument = goodsReceiveManagementDao.find(DocumentDetailData.class,
                    grVO.getUploadId());
            if (grDocument != null) {
                vendorGoodsReceivedData.setGrDocument(grDocument);
            }
        }

        if (grVO.getType() != null) {
            vendorGoodsReceivedData.setType(grVO.getType());
        }
        vendorGoodsReceivedData.setComment(grVO.getComment());


        // finally save Goods Received Data
        vendorGoodsReceivedData = goodsReceiveManagementDao.add(vendorGoodsReceivedData, true);
        if (deliveryUnit.getFamily().equals(UnitCategory.KITCHEN) && !grVO.getVendorGrType().equals(VendorGrType.FIXED_ASSET_ORDER)) {
            if (vendorGoodsReceivedData.getType()!= null && vendorGoodsReceivedData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                if (vendorGoodsReceivedData.getExtraCharges() != null) {
                    if (!updateBudgetForExtraCharges(vendorGoodsReceivedData, true)) {
                        throw new SumoException("Error in Budget Updation", "Cannot Update Budget(Extra Charges) while Creating Goods Receiving..!");
                    }
                }

                if (vendorGoodsReceivedData.getExtraGrAmount() != null) {
                    if (!updateBudgetForExtraGrAMount(vendorGoodsReceivedData, true)) {
                        throw new SumoException("Error in Budget Updation", "Cannot Update Budget(Extra GR Amount) while Creating Goods Receiving..!");
                    }
                }

                if (!updateBudgetDetailsForRecievings(vendorGoodsReceivedData, true)) {
                    throw new SumoException("Error in Budget Updation", "Cannot Update Budget while Creating Goods Receiving..!");
                }
            }
        }
        notificationService.sendGrQualityCheckNotification(vendorGoodsReceivedData);

        if (vendorGoodsReceivedData.getGoodsReceivedId() != null) {
            List<VendorGRItem> grItemList = grVO.getItems();
            // create Excess GR
            List<UsedPOModel> poSet = grVO.getUsedPOList();
            if (grVO.getExtraGrItems() != null && !grVO.getExtraGrItems().isEmpty()) {
                String type = grVO.getType() != null ? grVO.getType() : "OPEX";
                Pair<UsedPOModel, Map<String, VendorGRItem>> poAndItemMap = createExcessGrOrder(
                        grVO.getExtraGrItems(), grVO.getDeliveryUnitId(), grVO.getVendorId(), grVO.getDispatchId(),
                        grVO.getUserId(), grVO.getVendorGrType().toString(),type);
                grItemList.addAll(poAndItemMap.getSecond().values());
                poSet.add(poAndItemMap.getFirst());
            }

            Set<PurchaseOrderData> purchaseOrderDataSet = settlePurchaseOrders(poSet, grVO.getUserId());
            vendorGoodsReceivedData.setPoMappingList(addMappings(purchaseOrderDataSet, vendorGoodsReceivedData));
            vendorGoodsReceivedData.setGrItemList(settleItems(grItemList, vendorGoodsReceivedData));
        }

        // return
        // SCMDataConverter.convert(vendorGoodsReceivedData,scmCache,masterDataCache,
        // true);

            if(grVO.getVendorGrType().equals(VendorGrType.FIXED_ASSET_ORDER) || deliveryUnit.getFamily().equals(UnitCategory.KITCHEN)){
            VendorGR vendorGR = SCMDataConverter.convert(vendorGoodsReceivedData, scmCache, masterDataCache, true);
            priceDao.addReceiving(vendorGR, false);
        }
//			VendorGR vendorGR = SCMDataConverter.convert(vendorGoodsReceivedData, scmCache, masterDataCache, true);
//			priceDao.addReceiving(vendorGR, false);
        return vendorGoodsReceivedData.getGoodsReceivedId();

    }

    private void validateForAdvanceGrs(CreateVendorGrVO grVO) throws SumoException {
        StringBuilder msg = new StringBuilder("");
        Set<Integer> pendingGrs = new HashSet<>();
        Set<Integer> pendingPrs = new HashSet<>();
        for (UsedPOModel usedPOModel : grVO.getUsedPOList()) {
            PurchaseOrderData purchaseOrderData = purchaseOrderManagementDao.find(PurchaseOrderData.class, usedPOModel.getId());
            if (Objects.nonNull(purchaseOrderData.getAdvancePaymentDatas()) && !purchaseOrderData.getAdvancePaymentDatas().isEmpty()) {
                for (AdvancePaymentData advancePaymentData : purchaseOrderData.getAdvancePaymentDatas()) {
                    VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(grVO.getVendorId())
                            .advanceStatus("ALL").advanceType(SCMServiceConstants.PO_ADVANCE).poId(usedPOModel.getId()).build();
                    VendorAdvancePayment advance = null;
                    advance = paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
                    if (Objects.nonNull(advance.getAdvanceStatus()) && advance.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.INITIATED.value())) {
                        String error = "Advance Payment Id : " + advance.getAdvancePaymentId() + " [ " + advance.getPrAmount() + " ]<br>";
                        msg.append(error);
                    }
                    if (!advance.getPendingGrs().isEmpty()) {
                        pendingGrs.addAll(advance.getPendingGrs());
                    }
                    if (!advance.getPendingPrs().isEmpty()) {
                        pendingPrs.addAll(advance.getPendingPrs());
                    }
                }
            }
        }
        if (!msg.toString().equalsIgnoreCase("")) {
            throw new SumoException("Please Settle the Vendor Advance..!", "Please Settle the Vendor Advance for this PO first  <br>" + msg.toString());
        }
        if (!pendingGrs.isEmpty()) {
            throw new SumoException("Please Settle the Pending GR's..!", "Please Settle all the Pending GR's related to this PO : "
                    + Arrays.asList(pendingGrs.toArray()));
        }
        if (!pendingPrs.isEmpty()) {
            throw new SumoException("Please Settle the Pending PR's..!", "Please Settle all the Pending PR's related to this PO : "
                    + Arrays.asList(pendingPrs.toArray()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean approveRegularVendorGR(VendorGR vendorGR, Integer userId) throws DataUpdationException, SumoException, InventoryUpdateException {
        boolean flag = false;
        BigDecimal totalApprovedQty = BigDecimal.ZERO;
        for (VendorPoGRItems vendorPoGRItems : vendorGR.getVendorPoGRItems()) {
            VendorGrPoItemMappingData vendorGrPoItemMappingData = goodsReceiveManagementDao.find(VendorGrPoItemMappingData.class, vendorPoGRItems.getMappingId());
            if (vendorGrPoItemMappingData != null) {
                vendorGrPoItemMappingData.setAcceptedQty(vendorPoGRItems.getGrItemAcceptedQuantity());
                vendorGrPoItemMappingData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                vendorGrPoItemMappingData.setLastUpdatedBy(userId);
                vendorGrPoItemMappingData.setUpdationreason(vendorPoGRItems.getUpdationreason());
                vendorGrPoItemMappingData.setDescription(vendorPoGRItems.getDescription());
                goodsReceiveManagementDao.update(vendorGrPoItemMappingData, true);
            }

            BigDecimal difference = SCMUtil.subtract(vendorGrPoItemMappingData.getPackagingQty(), vendorGrPoItemMappingData.getAcceptedQty());
            totalApprovedQty = SCMUtil.add(totalApprovedQty, vendorGrPoItemMappingData.getAcceptedQty());
            if (difference.compareTo(BigDecimal.ZERO) != 0) { //IF DIFFERENCE IS NOT O
                //SUBTRACT DIFFERENCE FROM RECEIVED QUANTITY IN PO ITEM(AFTER CONVERTNG IN COVERSION RATIO)
                PurchaseOrderItemData poitem = purchaseOrderManagementDao.find(PurchaseOrderItemData.class, vendorPoGRItems.getPoItemId());
                if (poitem != null) {
                    poitem.setReceivedQuantity(SCMUtil.subtract(poitem.getReceivedQuantity(), SCMUtil.multiply(SCMUtil.convertToBigDecimal(difference), poitem.getConversionRatio())));
                    goodsReceiveManagementDao.update(poitem, true);
                }

                //SUBTRACT DIFFERENCE FROM PACKAGING QUANTITY AND RECEIVED QUANTITY TO BE CALCULATED ON REVISED PACKAGING QUANTITY IN GR ITEMS
                VendorGoodsReceivedItemData vendorGoodsReceivedItemData = goodsReceiveManagementDao.find(VendorGoodsReceivedItemData.class, vendorPoGRItems.getGrItemId());
                if (vendorGoodsReceivedItemData != null) {
                    vendorGoodsReceivedItemData.setPackagingQuantity(SCMUtil.subtract(vendorGoodsReceivedItemData.getPackagingQuantity(), difference));
                    vendorGoodsReceivedItemData.setReceivedQty(
                            AppUtils.multiplyWithScale(SCMUtil.convertToBigDecimal(vendorGoodsReceivedItemData.getPackagingQuantity()), vendorGoodsReceivedItemData.getConversionRatio(),6));
                    vendorGoodsReceivedItemData.setTotalPrice(AppUtils.multiplyWithScale(vendorGoodsReceivedItemData.getUnitPrice(), vendorGoodsReceivedItemData.getPackagingQuantity(), 6));

                    //CALCUlATED REVISED TAX FOR VENDOR GOOD RECEIVED DATA
                    BigDecimal totaltax = BigDecimal.ZERO;
                    for (ItemTaxDetailData itemTaxDetailData : vendorGoodsReceivedItemData.getTaxes()) {
                        BigDecimal a = AppUtils.divideWithScale(vendorGoodsReceivedItemData.getTotalPrice(), new BigDecimal(100), 6);
                        BigDecimal b = (AppUtils.multiplyWithScale(a, itemTaxDetailData.getTaxPercentage(), 6));
                        totaltax = SCMUtil.add(totaltax, b);
                        itemTaxDetailData.setTaxValue(b);
                        goodsReceiveManagementDao.update(itemTaxDetailData, true);
                    }
                    vendorGoodsReceivedItemData.setTotalTax(totaltax);
                    vendorGoodsReceivedItemData.setTotalAmount(SCMUtil.add(vendorGoodsReceivedItemData.getTotalPrice(), vendorGoodsReceivedItemData.getTotalTax()));
                    goodsReceiveManagementDao.update(vendorGoodsReceivedItemData, true);
                }
            }
            //CLOSED PO CALL FOR COMPLETED PURCHASE ORDER
            PurchaseOrderData purchaseOrderData = purchaseOrderManagementDao.find(PurchaseOrderData.class, vendorPoGRItems.getPurchaseOrderId());
            if (purchaseOrderData != null) {
                updatePurchaseOrder(purchaseOrderData, userId);
            }
        }

        // UPDATE VENDOR GOODS RECEIVED DATA WITH CREATED STATUS WITH UPDATED AMOUNT TAX
        VendorGoodsReceivedData vendorGoodsReceivedData = goodsReceiveManagementDao.find(VendorGoodsReceivedData.class, vendorGR.getId());
        if (vendorGoodsReceivedData != null) {
            if (totalApprovedQty.compareTo(BigDecimal.ZERO) == 0) {
                String rejectDocId = "REJ-" + SCMUtil.getTimeWithoutMillisISTString(SCMUtil.getCurrentTimestamp()) + "-" + vendorGoodsReceivedData.getDocNumber();
                vendorGoodsReceivedData.setDocNumber(rejectDocId);
                vendorGoodsReceivedData.setGrStatus(SCMOrderStatus.CANCELLED.name());
                goodsReceiveManagementDao.update(vendorGoodsReceivedData, true);
                return true;
            }
            vendorGoodsReceivedData = updatedAddAmountDetails(vendorGoodsReceivedData.getGrItemList(), vendorGoodsReceivedData);
            if (vendorGoodsReceivedData.getType()!= null && vendorGoodsReceivedData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                if (vendorGoodsReceivedData.getExtraCharges() != null) {
                    if (!updateBudgetForExtraCharges(vendorGoodsReceivedData, true)) {
                        throw new SumoException("Error in Budget Updation", "Cannot Update Budget(Extra Charges) while Creating Goods Receiving..!");
                    }
                }

                if (vendorGoodsReceivedData.getExtraGrAmount() != null) {
                    if (!updateBudgetForExtraGrAMount(vendorGoodsReceivedData, true)) {
                        throw new SumoException("Error in Budget Updation", "Cannot Update Budget(Extra Gr Amount) while Creating Goods Receiving..!");
                    }
                }

                if (!updateBudgetDetailsForRecievings(vendorGoodsReceivedData, true)) {
                    throw new SumoException("Error in Budget Updation", "Cannot Update Budget Details For These Receivings..!");
                }
            }
            vendorGoodsReceivedData.setApprovedBy(userId);
            vendorGoodsReceivedData.setGrStatus(SCMOrderStatus.CREATED.name());
            goodsReceiveManagementDao.update(vendorGoodsReceivedData, true);

            //ADD RECEIVINGS
            VendorGR vendorGR1 = SCMDataConverter.convert(vendorGoodsReceivedData, scmCache, masterDataCache, true);
            priceDao.addReceiving(vendorGR1, false);
            flag = true;
        }
        return flag;
    }


    private int validateInvoiceNumber(int unitId, Integer vendorId, String docNumber, Boolean isForced)
            throws DataUpdationException {

        if (props.vendorGrInvoiceNumberValidation()) {
            if (isForced != null && isForced) {
                return -1;
            } else {
                try {
                    List<Integer> grIds = goodsReceiveManagementDao.getExistingGrInFinancialYear(unitId, vendorId,
                            docNumber);
                    if (grIds == null || grIds.size() == 0) {
                        return -1;
                    } else {
                        return grIds.get(0);
                    }

                } catch (Exception e) {
                    log.error("Error while looking up for Existing Gr In Financial Year", e);
                    return -1;
                }
            }
        } else {
            return -1;
        }

    }

    private VendorGoodsReceivedData addAmountDetails(CreateVendorGrVO grVO, VendorGoodsReceivedData vendorGoodsReceivedData) {
        List<VendorGRItem> items = new ArrayList<>();
        List<VendorGRItem> extraGrItems = new ArrayList<>();

        items.addAll(grVO.getItems());

        if (grVO.getExtraGrItems() != null && !grVO.getExtraGrItems().isEmpty()) {
            items.addAll(grVO.getExtraGrItems());
            extraGrItems.addAll(grVO.getExtraGrItems());
        }

        items.forEach(item -> {
            item.setExpiryDate(AppUtils.getDayBeforeOrAfterCurrentDay(
                    scmCache.getSkuDefinition(item.getSkuId()).getShelfLifeInDays()));
        });

        BigDecimal totalGrPrice = SCMUtil.convertToBigDecimal(items.stream()
                .mapToDouble(value -> value.getTotalCost() != null ? value.getTotalCost().doubleValue() : 0.0d)
                .sum());
        BigDecimal totalGrTax = SCMUtil.convertToBigDecimal(items.stream()
                .mapToDouble(value -> value.getTotalTax() != null ? value.getTotalTax().doubleValue() : 0.0d)
                .sum());
        BigDecimal totalGrAmount = totalGrPrice.add(totalGrTax);

        if (extraGrItems.size() > 0){
            BigDecimal totalExtraGrPrice = SCMUtil.convertToBigDecimal(extraGrItems.stream()
                    .mapToDouble(value -> value.getTotalCost() != null ? value.getTotalCost().doubleValue() : 0.0d)
                    .sum());
            BigDecimal totalExtraGrTax = SCMUtil.convertToBigDecimal(extraGrItems.stream()
                    .mapToDouble(value -> value.getTotalTax() != null ? value.getTotalTax().doubleValue() : 0.0d)
                    .sum());
            BigDecimal totalExtraGrAmount = totalExtraGrPrice.add(totalExtraGrTax);
            log.info("Extra Gr items Price is {}",totalExtraGrAmount);
            vendorGoodsReceivedData.setExtraGrAmount(totalExtraGrAmount);
        }

        vendorGoodsReceivedData.setTotalTaxes(totalGrTax);
        vendorGoodsReceivedData.setTotalAmount(totalGrAmount);
        vendorGoodsReceivedData.setTotalPrice(totalGrPrice);

        return vendorGoodsReceivedData;
    }

    private boolean updateBudgetForExtraGrAMount(VendorGoodsReceivedData vendorGoodsReceivedData, Boolean isCreatedOrCancelled) {
        try {
            log.info("Updating Budget for Extra GR Amount. {} ",vendorGoodsReceivedData.getExtraGrAmount());
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(vendorGoodsReceivedData.getDeliveryUnitId());
            String isFAOrGoods = vendorGoodsReceivedData.getVendorGRType().equalsIgnoreCase(AppConstants.FIXED_ASSET_ORDER) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(vendorGoodsReceivedData.getDeliveryUnitId(), isFAOrGoods);
            BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
            budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
            budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
            budgetAuditDetail.setActionBy(vendorGoodsReceivedData.getCreatedBy());
            if (isCreatedOrCancelled) {
                budgetAuditDetail.setAction(BudgetAuditActions.CREATED.value());
            }
            else {
                budgetAuditDetail.setAction(BudgetAuditActions.CANCELLED.value());
            }
            budgetAuditDetail.setKeyType(BudgetAuditActions.VGR_ID.value());
            budgetAuditDetail.setKeyValue(vendorGoodsReceivedData.getGoodsReceivedId());
            budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
            budgetAuditDetail.setAmountType(BudgetAuditActions.EXTRA_RECEIVING.value());
            budgetAuditDetail.setPreviousValue(capexBudgetData.getExtraReceiving());
            if (isCreatedOrCancelled) {
                budgetAuditDetail.setFinalValue(capexBudgetData.getExtraReceiving().add(vendorGoodsReceivedData.getExtraGrAmount()));
                budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
            }
            else {
                budgetAuditDetail.setFinalValue(capexBudgetData.getExtraReceiving().subtract(vendorGoodsReceivedData.getExtraGrAmount()));
                budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
            }
            serviceOrderManagementDao.add(budgetAuditDetail, true);
            if (isCreatedOrCancelled) {
                capexBudgetData.setExtraReceiving(capexBudgetData.getExtraReceiving().add(vendorGoodsReceivedData.getExtraGrAmount()));
            }
            else {
                capexBudgetData.setExtraReceiving(capexBudgetData.getExtraReceiving().subtract(vendorGoodsReceivedData.getExtraGrAmount()));
            }
            CapexBudgetDetailData finalData =serviceOrderManagementDao.update(capexBudgetData, true);
            if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                log.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        }
        catch (Exception e){
            log.error("Exception Occurred while updating budget of GR(Extra GR Amount) ::: ",e);
            return false;
        }
    }

    private boolean updateBudgetForExtraCharges(VendorGoodsReceivedData vendorGoodsReceivedData,Boolean isCreatedOrCancelled) {
        try {
            log.info("Updating Budget for Extra Charges. {} ",vendorGoodsReceivedData.getExtraCharges());
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(vendorGoodsReceivedData.getDeliveryUnitId());
            String isFAOrGoods = vendorGoodsReceivedData.getVendorGRType().equalsIgnoreCase(AppConstants.FIXED_ASSET_ORDER) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(vendorGoodsReceivedData.getDeliveryUnitId(), isFAOrGoods);
            BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
            budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
            budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
            budgetAuditDetail.setActionBy(vendorGoodsReceivedData.getCreatedBy());
            if (isCreatedOrCancelled) {
                budgetAuditDetail.setAction(BudgetAuditActions.CREATED.value());
            }
            else {
                budgetAuditDetail.setAction(BudgetAuditActions.CANCELLED.value());
            }
            budgetAuditDetail.setKeyType(BudgetAuditActions.VGR_ID.value());
            budgetAuditDetail.setKeyValue(vendorGoodsReceivedData.getGoodsReceivedId());
            budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
            budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
            budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
            if (isCreatedOrCancelled) {
                budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().subtract(vendorGoodsReceivedData.getExtraCharges()));
                budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
            }
            else {
                budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(vendorGoodsReceivedData.getExtraCharges()));
                budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
            }
            serviceOrderManagementDao.add(budgetAuditDetail, true);
            if (isCreatedOrCancelled) {
                capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().subtract(vendorGoodsReceivedData.getExtraCharges()));
            }
            else {
                capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(vendorGoodsReceivedData.getExtraCharges()));
            }
            CapexBudgetDetailData finalData =serviceOrderManagementDao.update(capexBudgetData, true);
            if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                log.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        }
        catch (Exception e){
            log.error("Exception Occurred while updating budget for GR(Extra Charges) ::: ",e);
            return false;
        }
    }

    private Boolean updateBudgetDetailsForRecievings(VendorGoodsReceivedData vendorGoodsReceivedData , Boolean isCreatedOrCancelledGr) {
        log.info("Updating Budget Audit Details For Goods Receiving");
        try {
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(vendorGoodsReceivedData.getDeliveryUnitId());
            String isFAOrGoods = vendorGoodsReceivedData.getVendorGRType().equalsIgnoreCase(AppConstants.FIXED_ASSET_ORDER) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(vendorGoodsReceivedData.getDeliveryUnitId(), isFAOrGoods);
            List<String> actions = Arrays.asList(BudgetAuditActions.RECEIVING_AMOUNT.value(), BudgetAuditActions.RUNNING_AMOUNT.value());
            for (String action : actions) {
                BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
                budgetAuditData.setCapexBudgetDetailId(capexBudgetData.getId());
                budgetAuditData.setCapexAuditId(capexAuditDetail.getId());
                if (action.equalsIgnoreCase(BudgetAuditActions.RECEIVING_AMOUNT.value())) {
                    budgetAuditData.setAmountType(BudgetAuditActions.RECEIVING_AMOUNT.value());
                    budgetAuditData.setPreviousValue(capexBudgetData.getReceivingAmount());
                    if (isCreatedOrCancelledGr) {
                        if (vendorGoodsReceivedData.getExtraCharges() != null) {
                            budgetAuditData.setFinalValue(capexBudgetData.getReceivingAmount().add(vendorGoodsReceivedData.getTotalAmount().add(vendorGoodsReceivedData.getExtraCharges())));
                        }
                        else {
                            budgetAuditData.setFinalValue(capexBudgetData.getReceivingAmount().add(vendorGoodsReceivedData.getTotalAmount()));
                        }
                        budgetAuditData.setActionType(BudgetAuditActions.ADDITION.value());
                        budgetAuditData.setAction(BudgetAuditActions.CREATED.value());
                    } else {
                        if (vendorGoodsReceivedData.getExtraCharges() != null) {
                            budgetAuditData.setFinalValue(capexBudgetData.getReceivingAmount().subtract(vendorGoodsReceivedData.getTotalAmount().add(vendorGoodsReceivedData.getExtraCharges())));
                        }
                        else {
                            budgetAuditData.setFinalValue(capexBudgetData.getReceivingAmount().subtract(vendorGoodsReceivedData.getTotalAmount()));
                        }
                        budgetAuditData.setActionType(BudgetAuditActions.REDUCTION.value());
                        budgetAuditData.setAction(BudgetAuditActions.CANCELLED.value());
                    }
                }
                else {
                    budgetAuditData.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                    budgetAuditData.setPreviousValue(capexBudgetData.getRunningAmount());
                    if (isCreatedOrCancelledGr) {
                        budgetAuditData.setFinalValue(capexBudgetData.getRunningAmount().subtract(vendorGoodsReceivedData.getTotalAmount()));
                        budgetAuditData.setActionType(BudgetAuditActions.REDUCTION.value());
                        budgetAuditData.setAction(BudgetAuditActions.CREATED.value());
                    } else {
                        budgetAuditData.setFinalValue(capexBudgetData.getRunningAmount().add(vendorGoodsReceivedData.getTotalAmount()));
                        budgetAuditData.setActionType(BudgetAuditActions.ADDITION.value());
                        budgetAuditData.setAction(BudgetAuditActions.CANCELLED.value());
                    }
                }
                budgetAuditData.setKeyType(BudgetAuditActions.VGR_ID.value());
                budgetAuditData.setKeyValue(vendorGoodsReceivedData.getGoodsReceivedId());
                budgetAuditData.setActionBy(vendorGoodsReceivedData.getCreatedBy());
                budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
                serviceOrderManagementDao.add(budgetAuditData, true);
            }
            if (isCreatedOrCancelledGr) {
                if (vendorGoodsReceivedData.getExtraCharges() != null) {
                    capexBudgetData.setReceivingAmount(capexBudgetData.getReceivingAmount().add(vendorGoodsReceivedData.getTotalAmount().add(vendorGoodsReceivedData.getExtraCharges())));
                }
                else {
                    capexBudgetData.setReceivingAmount(capexBudgetData.getReceivingAmount().add(vendorGoodsReceivedData.getTotalAmount()));
                }
                capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(vendorGoodsReceivedData.getTotalAmount()));
            }
            else {
                if (vendorGoodsReceivedData.getExtraCharges() != null) {
                    capexBudgetData.setReceivingAmount(capexBudgetData.getReceivingAmount().subtract(vendorGoodsReceivedData.getTotalAmount().add(vendorGoodsReceivedData.getExtraCharges())));
                }
                else {
                    capexBudgetData.setReceivingAmount(capexBudgetData.getReceivingAmount().subtract(vendorGoodsReceivedData.getTotalAmount()));
                }
                capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().add(vendorGoodsReceivedData.getTotalAmount()));
            }
            CapexBudgetDetailData finalData =serviceOrderManagementDao.update(capexBudgetData, true);
            if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                log.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("Exception Occurred while updating budget for Vendor Goods Receivings ::: ",e);
            return false;
        }
    }

    private Boolean updateBudgetDetailsForInternalGR(BigDecimal totalAmount, Integer keyId, BudgetAuditActions actionType, Integer unitId,
                                                     String isAssetOrder, Boolean isRejectedGr, Integer createdBy) {
        log.info("Updating Budget Audit Details For Internal Goods Receiving For KeyType : {} and keyId : {} ", actionType.value(), keyId);
        try {
            CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(unitId);
            String isFAOrGoods = Boolean.valueOf(AppUtils.getStatus(isAssetOrder)).equals(Boolean.TRUE) ? AppConstants.FIXED_ASSETS : AppConstants.GOODS;
            CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(unitId, isFAOrGoods);
            List<String> actions = isRejectedGr.equals(Boolean.TRUE) ? Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(), BudgetAuditActions.RUNNING_AMOUNT.value()) :
                    Arrays.asList(BudgetAuditActions.RECEIVING_AMOUNT.value(), BudgetAuditActions.PAID_AMOUNT.value());
            String reason = isRejectedGr.equals(Boolean.TRUE) ? BudgetAuditActions.REJECTED.value() : BudgetAuditActions.CREATED.value();
            for (String action : actions) {
                BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
                budgetAuditData.setCapexBudgetDetailId(capexBudgetData.getId());
                budgetAuditData.setCapexAuditId(capexAuditDetail.getId());
                budgetAuditData.setAmountType(action);
                if (action.equalsIgnoreCase(BudgetAuditActions.RECEIVING_AMOUNT.value())) {
                    budgetAuditData.setPreviousValue(capexBudgetData.getReceivingAmount());
                    budgetAuditData.setFinalValue(capexBudgetData.getReceivingAmount().add(totalAmount));
                    budgetAuditData.setActionType(BudgetAuditActions.ADDITION.value());
                    budgetAuditData.setAction(reason);

                } else if (action.equalsIgnoreCase(BudgetAuditActions.RUNNING_AMOUNT.value())) {
                    budgetAuditData.setPreviousValue(capexBudgetData.getRunningAmount());
                    budgetAuditData.setFinalValue(capexBudgetData.getRunningAmount().subtract(totalAmount));
                    budgetAuditData.setActionType(BudgetAuditActions.REDUCTION.value());
                    budgetAuditData.setAction(reason);
                } else if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                    budgetAuditData.setPreviousValue(capexBudgetData.getRemainingAmount());
                    String budgetAction = totalAmount.compareTo(BigDecimal.ZERO) < 0 ? BudgetAuditActions.REDUCTION.value() :
                            BudgetAuditActions.ADDITION.value();
                    budgetAuditData.setActionType(budgetAction);
                    budgetAuditData.setAction(reason);
                    budgetAuditData.setFinalValue(capexBudgetData.getRemainingAmount().add(totalAmount));
                }else if (action.equalsIgnoreCase(BudgetAuditActions.PAID_AMOUNT.value())){
                    budgetAuditData.setPreviousValue(capexBudgetData.getPaidAmount());
                    budgetAuditData.setActionType(BudgetAuditActions.ADDITION.value());
                    budgetAuditData.setAction(reason);
                    budgetAuditData.setFinalValue(capexBudgetData.getPaidAmount().add(totalAmount));
                }
                budgetAuditData.setKeyType(BudgetAuditActions.GR_ID.value());
                budgetAuditData.setKeyValue(keyId);
                budgetAuditData.setActionBy(createdBy);
                budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
                serviceOrderManagementDao.add(budgetAuditData, true);
            }
            if (isRejectedGr.equals(Boolean.FALSE)) {
                capexBudgetData.setReceivingAmount(capexBudgetData.getReceivingAmount().add(totalAmount));
                capexBudgetData.setPaidAmount(capexBudgetData.getPaidAmount().add(totalAmount));
            } else {
                capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(totalAmount));
                capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(totalAmount));
            }
            CapexBudgetDetailData finalData = serviceOrderManagementDao.update(capexBudgetData, true);
            if (!purchaseOrderService.validateBudgetAmounts(finalData)) {
                log.info("Budget Values went into negative...! Please Check");
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("Exception Occurred while updating budget for Internal Goods Receivings ::: ", e);
            return false;
        }
    }

    private VendorGoodsReceivedData updatedAddAmountDetails(List<VendorGoodsReceivedItemData> vendorGoodsReceivedItemData, VendorGoodsReceivedData vendorGoodsReceivedData) {
        List<VendorGoodsReceivedItemData> items = vendorGoodsReceivedItemData;


        items.forEach(item -> {
            item.setExpiryDate(AppUtils.getDayBeforeOrAfterCurrentDay(
                    scmCache.getSkuDefinition(item.getSkuId()).getShelfLifeInDays()));
        });

        BigDecimal totalGrPrice = SCMUtil.convertToBigDecimal(items.stream()
                .mapToDouble(value -> value.getTotalPrice() != null ? value.getTotalPrice().doubleValue() : 0.0d)
                .sum());
        BigDecimal totalGrTax = SCMUtil.convertToBigDecimal(items.stream()
                .mapToDouble(value -> value.getTotalTax() != null ? value.getTotalTax().doubleValue() : 0.0d)
                .sum());
        BigDecimal totalGrAmount = totalGrPrice.add(totalGrTax);

        vendorGoodsReceivedData.setTotalTaxes(totalGrTax);
        vendorGoodsReceivedData.setTotalAmount(totalGrAmount);
        vendorGoodsReceivedData.setTotalPrice(totalGrPrice);

        return vendorGoodsReceivedData;
    }

    private Pair<UsedPOModel, Map<String, VendorGRItem>> createExcessGrOrder(List<VendorGRItem> extraGrItems,
                                                                             int deliveryUnitId, int vendorId, int dispatchId, int userId, String orderType, String type) throws PurchaseOrderCreationException, SumoException {
        PurchaseOrderCreateVO vo = new PurchaseOrderCreateVO();
        vo.setCreationType(POCreationType.SYSTEM);
        vo.setDeliveryUnitId(deliveryUnitId);
        vo.setVendorId(vendorId);
        vo.setDispatchId(dispatchId);
        vo.setUserId(userId);
        vo.setOrderType(orderType);
        vo.setFulfilmentDate(SCMUtil.getFormattedDate(SCMUtil.getCurrentBusinessDate()));
        if (type != null) {
            vo.setType(type);
        }
        vo.setItems(extraGrItems.stream().map(item -> convertToPoItem(item,type)).collect(Collectors.toList()));
        Map<String, VendorGRItem> extraGrItemMap = extraGrItems.stream()
                .collect(Collectors.toMap(
                        vendorGRItem -> SCMUtil.getSkuPkgKey(vendorGRItem.getSkuId(), vendorGRItem.getPackagingId()),
                        Function.<VendorGRItem>identity()));
        Pair<UsedPOModel, Map<String, VendorGRItem>> poItemMap = purchaseOrderService.createPurchaseOrder(vo,
                extraGrItemMap);
        return poItemMap;
    }

    private PurchaseOrderItem convertToPoItem(VendorGRItem item, String type) {
        PurchaseOrderItem poItem = new PurchaseOrderItem();

        poItem.setSkuId(item.getSkuId());
        poItem.setSkuName(item.getSkuName());
        poItem.setPackagingId(item.getPackagingId());
        poItem.setPackagingName(item.getPackagingName());
        poItem.setPackagingQty(item.getPackagingQty());
        poItem.setUnitPrice(item.getUnitPrice());
        poItem.setNegotiatedUnitPrice(item.getUnitPrice());
        poItem.setRequestedQuantity(item.getReceivedQuantity());
        poItem.setRequestedAbsoluteQuantity(item.getReceivedQuantity());
        poItem.setConversionRatio(item.getConversionRatio());
        poItem.setTotalCost(item.getTotalCost());
        poItem.setTotalTax(item.getTotalTax());
        poItem.setAmountPaid(item.getAmountPaid());
        poItem.setHsn(item.getHsn());
        poItem.setUnitOfMeasure(item.getUnitOfMeasure());
        poItem.setCgst(getTax(item.getTaxes(), SCMServiceConstants.CGST));
        poItem.setSgst(getTax(item.getTaxes(), SCMServiceConstants.SGST));
        poItem.setIgst(getTax(item.getTaxes(), SCMServiceConstants.IGST));
        poItem.setOtherTaxes(getOtherTaxes(item.getTaxes()));
        poItem.setType(type);
        return poItem;
    }

    private List<OtherTaxDetail> getOtherTaxes(List<OtherTaxDetail> taxes) {
        List<String> gstTaxes = Arrays.asList(SCMServiceConstants.IGST, SCMServiceConstants.CGST,
                SCMServiceConstants.SGST);
        return taxes.stream().filter(taxDetail -> !gstTaxes.contains(taxDetail.getTaxCategory()))
                .collect(Collectors.toList());
    }

    private PercentageDetail getTax(List<OtherTaxDetail> taxes, String taxCategory) {
        Optional<OtherTaxDetail> tax = taxes.stream()
                .filter(taxDetail -> taxDetail.getTaxCategory().equals(taxCategory)).findFirst();
        return tax.isPresent() ? new PercentageDetail(tax.get().getPercentage(), tax.get().getValue()) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelVendorGR(int grId, int userId) throws DataUpdationException, SumoException {
        boolean flag = false;
        VendorGoodsReceivedData vendorGR = goodsReceiveManagementDao.find(VendorGoodsReceivedData.class, grId);
        if (vendorGR != null) {
            if (vendorGR.getGrStatus().equalsIgnoreCase(SCMOrderStatus.CREATED.name())) {
                if (vendorGR.getType()!= null && vendorGR.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    if (vendorGR.getExtraCharges() != null) {
                        if (!updateBudgetForExtraCharges(vendorGR, false)) {
                            throw new SumoException("Error in Budget Updation", "Cannot Update Budget(Extra Charges) while Creating Goods Receiving..!");
                        }
                    }

                    if (vendorGR.getExtraGrAmount() != null) {
                        if (!updateBudgetForExtraGrAMount(vendorGR, false)) {
                            throw new SumoException("Error in Budget Updation", "Cannot Update Budget(Extra GR Amount) while Creating Goods Receiving..!");
                        }
                    }

                    if (!updateBudgetDetailsForRecievings(vendorGR, false)) {
                        throw new SumoException("Error in Budget Updation", "Cannot Update Budget while Cancelling Goods Receivings..!");
                    }
                }
            }
            List<PurchaseOrderVendorGRMappingData> mappingDataList = vendorGR.getPoMappingList();
            List<VendorGoodsReceivedItemData> itemList = vendorGR.getGrItemList();
            if (mappingDataList != null) {
                Set<Integer> updatedPurchaseOrders = openPurchaseOrders(itemList);
                if (!updatedPurchaseOrders.isEmpty()) {
                    for (PurchaseOrderVendorGRMappingData mappingData : mappingDataList) {
                        PurchaseOrderData po = mappingData.getPurchaseOrderData();
                        if (updatedPurchaseOrders.contains(po.getId())
                                && PurchaseOrderStatus.CLOSED.name().equals(po.getStatus())) {
                            throw new SumoException("PO is Already Closed..!","Can not cancel the GR as the PO is already Closed..!");
//                            PurchaseOrderStatus toStatus = PurchaseOrderStatus.IN_PROGRESS;
//                            boolean generated = purchaseOrderService.generatePOStatusEvent(po, toStatus, userId);
//                            if (generated) {
//                                // UPDATE THE STATUS OF PO
//                                po.setStatus(toStatus.name());
//                            }
                        }
                        po.setLastUpdatedBy(userId);
                        po.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                        goodsReceiveManagementDao.update(po, false);
                    }
                    goodsReceiveManagementDao.flush();
                }
                if (vendorGR.getClosureEventId() != null) {
                    vendorGR.setInvalidGR(SCMServiceConstants.SCM_CONSTANT_YES);
                } else {
                    vendorGR.setGrStatus(PurchaseOrderStatus.CANCELLED.name());
                }
                vendorGR.setUpdatedBy(userId);
                vendorGR.setUpdatedAt(SCMUtil.getCurrentTimestamp());
                goodsReceiveManagementDao.update(vendorGR, true);
                flag = true;
            }
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setVendorGRtoCreated(int grId, int userId) throws SumoException{
        boolean flag = false;
        VendorGoodsReceivedData vendorGR = goodsReceiveManagementDao.find(VendorGoodsReceivedData.class, grId);
        if (vendorGR != null) {
            if(Objects.equals(vendorGR.getGrStatus(), "CREATED")){
                return false;
            }
            if (vendorGR.getType()!= null && vendorGR.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                log.info("updating budget for GR Id:  {}", vendorGR.getGoodsReceivedId());
                if (vendorGR.getExtraCharges() != null) {
                    if (!updateBudgetForExtraCharges(vendorGR, true)) {
                        throw new SumoException("Error in Budget Updation", "Cannot Update Budget(Extra Charges) while Creating Goods Receiving..!");
                    }
                }

                if (vendorGR.getExtraGrAmount() != null) {
                    if (!updateBudgetForExtraGrAMount(vendorGR, true)) {
                        throw new SumoException("Error in Budget Updation", "Cannot Update Budget(Extra GR Amount) while Creating Goods Receiving..!");
                    }
                }

                if (!updateBudgetDetailsForRecievings(vendorGR, true)) {
                    throw new SumoException("Error in Budget Updation", "Cannot Update Budget Details For These Receivings..!");
                }
            }
            vendorGR.setGrStatus(PurchaseOrderStatus.CREATED.name());
            vendorGR.setUpdatedBy(userId);
            vendorGR.setUpdatedAt(SCMUtil.getCurrentTimestamp());
            goodsReceiveManagementDao.update(vendorGR, true);
            flag = true;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
                                         MultipartFile file) {

           String fileName = "VENDOR_GR_" + SCMUtil.getCurrentTimeISTStringWithNoColons();

        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "GR", fileName, file);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = goodsReceiveManagementDao.add(SCMDataConverter.convert(documentDetail), true);
            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }
        } catch (Exception e) {
            log.error("Encountered error while uploading document", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorGR> findRegularVendorsGrs(Integer unitId, Integer vendorId) {
        List<VendorGoodsReceivedData> vendorGRs = goodsReceiveManagementDao.findRegularVendorsGrs(unitId, vendorId);
        if (vendorGRs != null) {
            List<VendorGR> vendorGRList = vendorGRs.stream()
                    .map(gr -> SCMDataConverter.convert(gr, scmCache, masterDataCache, true))
                    .collect(Collectors.<VendorGR>toList());
            for (VendorGR vGr : vendorGRList) {
                List<VendorPoGRItems> vendorPoGRItems = goodsReceiveManagementDao.mapping(vGr.getId());
                if (vendorPoGRItems != null && !vendorPoGRItems.isEmpty()) {
                    vGr.setVendorPoGRItems(vendorPoGRItems);
                }
            }
            return vendorGRList;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GrItemQuantityUpdation> grItemQuantityDeviations() {
        List<GrItemQuantityUpdation> grItemQuantityUpdationList = goodsReceiveManagementDao.grItemQuantityDeviations();
        return grItemQuantityUpdationList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorGR> findVendorGRsRejected(Integer vendorId, Integer dispatchId, int deliveryUnitId,
                                                Date startDate, Date endDate, Integer goodReceivedId) {
        if (startDate == null && endDate == null) {
            startDate = SCMUtil.getCurrentBusinessDate();
            endDate = startDate;
        }
        endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);
        List<VendorGoodsReceivedData> vendorGRs = goodsReceiveManagementDao.findVendorGRs(vendorId, dispatchId,
                deliveryUnitId, null, startDate, endDate, goodReceivedId);
        if (vendorGRs != null) {
            List<VendorGR> vendorGRList = vendorGRs.stream()
                    .map(gr -> SCMDataConverter.convert(gr, scmCache, masterDataCache, true))
                    .collect(Collectors.<VendorGR>toList());
            for (VendorGR vGr : vendorGRList) {
                List<VendorPoGRItems> vendorPoGRItems = goodsReceiveManagementDao.rejectedGr(vGr.getId());
                List<VendorPoGRItems> list = new ArrayList<>();
                if (vendorPoGRItems != null && !vendorPoGRItems.isEmpty()) {
                    for (VendorPoGRItems item : vendorPoGRItems) {
                        item.setRejectedQty(SCMUtil.subtract(item.getActualPackagingQty(), item.getGrItemAcceptedQuantity()));
                        list.add(item);
                    }
                }
                if (list != null && !vendorPoGRItems.isEmpty()) {
                    vGr.setVendorPoGRItems(vendorPoGRItems);
                }
            }

            return vendorGRList;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorGR> findVendorGRs(Integer vendorId, Integer dispatchId, int deliveryUnitId, List<Integer> skus,
                                        Date startDate, Date endDate, Integer goodReceivedId) {
        if (startDate == null && endDate == null) {
            startDate = SCMUtil.getCurrentBusinessDate();
            endDate = startDate;
        }
        endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);
        List<VendorGoodsReceivedData> vendorGRs = goodsReceiveManagementDao.findVendorGRs(vendorId, dispatchId,
                deliveryUnitId, skus, startDate, endDate, goodReceivedId);
        if (vendorGRs != null) {
            List<VendorGR> vendorGRList = vendorGRs.stream()
                    .map(gr -> SCMDataConverter.convert(gr, scmCache, masterDataCache, true))
                    .collect(Collectors.<VendorGR>toList());

            for(VendorGR vGr : vendorGRList){
                if(vGr.getPaymentRequestId() != null){
                    DebitNoteDetailData debitNoteDetailData = paymentRequestManagementDao
                        .findDebitNoteByPaymentRequestId(vGr.getPaymentRequestId());
                    if (debitNoteDetailData != null && debitNoteDetailData.getDebitNoteDocId() != null) {
                        DocumentDetailData documentDetailData = goodsReceiveManagementDao.find(DocumentDetailData.class, debitNoteDetailData.getDebitNoteDocId());
                        if(documentDetailData != null) {
                            vGr.setDebitNote(SCMDataConverter.convert(documentDetailData));
                        }
                    }
                }
            }
            return vendorGRList;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorGR> findVendorGRsForPo(Integer vendorId, Integer dispatchId, Integer deliveryUnitId, List<Integer> skus,
                                             Date startDate, Date endDate, Integer paymentRequestId, Integer goodsReceivedId) {
        if (startDate == null && endDate == null) {
            startDate = SCMUtil.getCurrentBusinessDate();
            endDate = startDate;
        }
        endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);
        List<VendorGoodsReceivedData> vendorGRs = goodsReceiveManagementDao.findVendorGRsForPo(vendorId, dispatchId,
                deliveryUnitId, skus, startDate, endDate, paymentRequestId, goodsReceivedId);
        if (vendorGRs != null) {
            List<VendorGR> vendorGRList = vendorGRs.stream()
                    .map(gr -> SCMDataConverter.convert(gr, scmCache, masterDataCache, true))
                    .collect(Collectors.<VendorGR>toList());
            return vendorGRList;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorGR> findVendorGRsForPayment(Integer vendorId, Integer dispatchId, int deliveryUnitId,
                                                  Date startDate, Date endDate) {
        if (startDate == null && endDate == null) {
            startDate = SCMUtil.getCurrentBusinessDate();
            endDate = startDate;
        }
        if (startDate.compareTo(SCMUtil.getPaymentSystemLaunchDate()) < 0) {
            startDate = SCMUtil.getPaymentSystemLaunchDate();
        }
        endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);
        List<VendorGoodsReceivedData> vendorGRs = goodsReceiveManagementDao.findVendorGRsForPayment(vendorId,
                dispatchId, deliveryUnitId, startDate, endDate);
        if (vendorGRs != null) {
            Map<Integer, com.stpl.tech.master.domain.model.Pair<List<Integer>, List<Integer>>> advances = getAdvanceGrs(vendorGRs);
            List<Integer> grIds = vendorGRs.stream().filter(g -> g.getVendorGRType().equals(VendorGrType.FIXED_ASSET_ORDER.value())).map(gr -> gr.getGoodsReceivedId()).collect(Collectors.toList());
            Map<Integer, Boolean> validForPrMap = goodsReceiveManagementDao.getInitiatedAssetForGr(grIds).stream().collect
                    (Collectors.toMap(AssetDefinitionData::getGrId,asset -> true ,(key1,key2)->{
                        return key1;
                    }));
            List<VendorGR> vendorGRList = vendorGRs.stream()
                    .map(gr -> {
                        VendorGR vendorGR = SCMDataConverter.convert(gr, scmCache, masterDataCache, true);
                        if (validForPrMap.containsKey(vendorGR.getId())) {
                            vendorGR.setValidForPR(false);
                        } else {
                            vendorGR.setValidForPR(true);
                        }
                        vendorGR.setAdvanceGrs(advances.getOrDefault(gr.getGoodsReceivedId(), new com.stpl.tech.master.domain.model.Pair<>(null, new ArrayList<>())).getValue());
                        List<VendorAdvancePayment> vendorAdvancePayments = new ArrayList<>();
                        if (Objects.nonNull(advances.get(gr.getGoodsReceivedId()))) {
                            List<Integer> advancePaymentIds = advances.get(gr.getGoodsReceivedId()).getKey();
                            for (Integer advancePaymentId : advancePaymentIds) {
                                VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(gr.getGeneratedForVendor())
                                        .advanceStatus("ALL").advanceType(SCMServiceConstants.PO_ADVANCE).build();
                                VendorAdvancePayment advance = null;
                                try {
                                    AdvancePaymentData advancePaymentData = goodsReceiveManagementDao.find(AdvancePaymentData.class, advancePaymentId);
                                    advance = paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
                                } catch (Exception e) {
                                    log.info("Exception during Vendor Advance ..!", e);
                                }
                                vendorGR.setVendorAdvancePayment(advance);
                                vendorAdvancePayments.add(advance);
                            }
                            if (!vendorAdvancePayments.isEmpty()) {
                                vendorGR.setVendorAdvancePayments(vendorAdvancePayments);
                            }
                        }
                        return vendorGR;
                    })
                    .collect(Collectors.<VendorGR>toList());
            return vendorGRList;
        }
        return null;
    }

    private Map<Integer, com.stpl.tech.master.domain.model.Pair<List<Integer>, List<Integer>>> getAdvanceGrs(List<VendorGoodsReceivedData> vendorGoodsReceivedDataList) {
        Map<Integer, com.stpl.tech.master.domain.model.Pair<List<Integer>, List<Integer>>> result = new HashMap<>();
        vendorGoodsReceivedDataList.forEach(vgrd -> {
            List<Integer> grIds = new ArrayList<>();
            boolean isMultiPo = false;
            Integer tempPoId = null;
            for (PurchaseOrderVendorGRMappingData purchaseOrderVendorGRMappingData : vgrd.getPoMappingList()) {
                if (Objects.nonNull(tempPoId)) {
                    if (!tempPoId.equals(purchaseOrderVendorGRMappingData.getPurchaseOrderData().getId())) {
                        isMultiPo = true;
                    }
                } else {
                    tempPoId = purchaseOrderVendorGRMappingData.getPurchaseOrderData().getId();
                }
                if (isMultiPo) {
                    break;
                }
            }
            if (!isMultiPo) {
                PurchaseOrderData purchaseOrderData = goodsReceiveManagementDao.find(PurchaseOrderData.class, tempPoId);
                if (Objects.nonNull(purchaseOrderData.getAdvancePaymentDatas()) && !purchaseOrderData.getAdvancePaymentDatas().isEmpty()) {
                    List<PurchaseOrderVendorGRMappingData> poGrList = goodsReceiveManagementDao.findGrsWithPoId(tempPoId);
                    poGrList.forEach(poGrData -> {
                        VendorGoodsReceivedData receivedData = poGrData.getVendorGoodsReceivedData();
                        if (Objects.isNull(receivedData.getPaymentRequestData()) && !receivedData.getGrStatus().equalsIgnoreCase(SCMOrderStatus.CANCELLED.value())) {
                            grIds.add(receivedData.getGoodsReceivedId());
                        }
                    });
                    List<Integer> adpIds = new ArrayList<>();
                    List<AdvancePaymentData> advancePaymentDataList = purchaseOrderData.getAdvancePaymentDatas();
                    for (AdvancePaymentData paymentData : advancePaymentDataList) {
                        if (!adpIds.contains(paymentData.getAdvancePaymentId())) {
                            adpIds.add(paymentData.getAdvancePaymentId());
                        }
                    }
                    result.put(vgrd.getGoodsReceivedId(), new com.stpl.tech.master.domain.model.Pair<>(adpIds, grIds));
                }
            }
        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean setVendorGRsForNoPayment(BulkRequestVO request) throws SumoException {
        if (request != null) {
            if (request.getIds().size() > 0) {
                for (Integer grId : request.getIds()) {
                    VendorGoodsReceivedData vendorGoodsReceivedData = goodsReceiveManagementDao
                            .find(VendorGoodsReceivedData.class, grId);
                    if (vendorGoodsReceivedData != null) {
                        if (vendorGoodsReceivedData.getPaymentRequestData() == null) {
                            if (vendorGoodsReceivedData.getCreatedAt()
                                    .compareTo(SCMUtil.getPaymentSystemLaunchDate()) >= 0) {
                                if (masterDataCache.getEmployee(request.getActionBy()) != null) {
                                    vendorGoodsReceivedData.setToBePaid(SCMServiceConstants.SCM_CONSTANT_NO);
                                    vendorGoodsReceivedData.setUpdatedBy(request.getActionBy());
                                    vendorGoodsReceivedData = goodsReceiveManagementDao.update(vendorGoodsReceivedData,
                                            true);
                                    if (vendorGoodsReceivedData == null) {
                                        throw new SumoException("Error updating vendor GR data.");
                                    }
                                } else {
                                    throw new SumoException("Employee taking action is not valid!");
                                }
                            } else {
                                throw new SumoException("GR id: " + grId
                                        + " is created before 1 Nov 2017 and hence not valid for no payment.");
                            }
                        } else {
                            throw new SumoException(
                                    "Payment request for GR id: " + grId + " has already been generated.");
                        }
                    } else {
                        throw new SumoException("GR id: " + grId + " is not valid.");
                    }
                }
            } else {
                throw new SumoException("No GR selected for non payment!");
            }
        } else {
            throw new SumoException("Please add request body!");
        }
        return true;
    }

    private Set<Integer> openPurchaseOrders(List<VendorGoodsReceivedItemData> vendorGRItems)
            throws DataUpdationException {
        try {
            Set<Integer> purchaseOrders = new HashSet<>();
            for (VendorGoodsReceivedItemData grItem : vendorGRItems) {
                List<VendorGrPoItemMappingData> poItemList = grItem.getPoItemList();
                for (VendorGrPoItemMappingData mappingData : poItemList) {
                    PurchaseOrderItemData poItem = mappingData.getPoItem();
                    BigDecimal received = SCMUtil.multiply(mappingData.getPackagingQty(), poItem.getConversionRatio());
                    poItem.setReceivedQuantity(SCMUtil.subtract(poItem.getReceivedQuantity(), received));
                    goodsReceiveManagementDao.update(poItem, false);
                    purchaseOrders.add(poItem.getPurchaseOrderData().getId());
                }
                goodsReceiveManagementDao.flush();
            }
            return purchaseOrders;
        } catch (Exception e) {
            log.error("Error while updating Purchase Order Item", e);
            throw new DataUpdationException("Could not update purchase order items");
        }
    }

    private Set<PurchaseOrderData> settlePurchaseOrders(List<UsedPOModel> usedPOList, int userId)
            throws DataUpdationException, SumoException {
        // Update Purchase Order
        // Create PurchaseOrderEvent if not already created
        Set<PurchaseOrderData> purchaseOrderDataSet = new HashSet<>();
        for (UsedPOModel usedPO : usedPOList) {
            PurchaseOrderData po = goodsReceiveManagementDao.find(PurchaseOrderData.class, usedPO.getId());
            Unit deliveryUnit = masterDataCache.getUnit(po.getDeliveryLocationId());
            Map<String, UsedSKUModel> skuMap = usedPO.getSkus().stream().collect(Collectors
                    .toMap(model -> SCMUtil.getSkuPkgKey(model.getId(), model.getPkg()), Function.identity()));
            boolean itemReceived = false;
            for (PurchaseOrderItemData item : po.getPurchaseOrderItemDatas()) {
                UsedSKUModel skuModel = skuMap.get(SCMUtil.getSkuPkgKey(item.getSkuId(), item.getPackagingId()));
                if (skuModel != null) {
                    BigDecimal receivedQty = SCMUtil.convertToBigDecimal(item.getReceivedQuantity());
                    item.setReceivedQuantity(receivedQty.add(skuModel.getQty()));
                    goodsReceiveManagementDao.update(item, false);
                    itemReceived = true;
                }
            }
            goodsReceiveManagementDao.flush();
            if (itemReceived) {
                // update TO IN_PROGRESS in case its getting settled in one go
                if (PurchaseOrderStatus.APPROVED.equals(PurchaseOrderStatus.valueOf(po.getStatus()))) {
                    purchaseOrderService.generatePOStatusEvent(po, PurchaseOrderStatus.IN_PROGRESS, userId);
                    // GRs initiated for the first time
                    po.setInitiationTime(SCMUtil.getCurrentTimestamp());
                    po.setStatus(PurchaseOrderStatus.IN_PROGRESS.name());
                }
                if(po.getOrderType().equals(VendorGrType.FIXED_ASSET_ORDER.name()) || deliveryUnit.getFamily().equals(UnitCategory.KITCHEN)) {
                    updatePurchaseOrder(po, userId);
                }
//				updatePurchaseOrder(po, userId);
            }
            purchaseOrderDataSet.add(po);
        }
        return purchaseOrderDataSet;
    }

    private void updatePurchaseOrder(PurchaseOrderData po, int userId) throws DataUpdationException {
        try {
            if (po.getAdvancePaymentDatas().isEmpty() || isAllAdvancesCompleted(po)) {
                boolean isFulfilled = purchaseOrderService.checkIfFulfilled(po);
                if (isFulfilled) {
                    boolean updated = purchaseOrderService.generatePOStatusEvent(po, PurchaseOrderStatus.CLOSED, userId);
                    if (updated) {
                        po.setStatus(PurchaseOrderStatus.CLOSED.name());
                    }
                }
                po.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                po.setLastUpdatedBy(userId);
                goodsReceiveManagementDao.update(po, true);
            }
        } catch (Exception e) {
            log.error("Error while updating purchase order {}", po.getId(), e);
            throw new DataUpdationException("Could not update PO for the given GR");
        }
    }

    private boolean isAllAdvancesCompleted(PurchaseOrderData po) {
        if (!po.getAdvancePaymentDatas().isEmpty()) {
            for (AdvancePaymentData advancePaymentData : po.getAdvancePaymentDatas()) {
                if (!advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.COMPLETED.value())) {
                    return false;
                }
            }
        }
        return true;
    }

    private List<VendorGoodsReceivedItemData> settleItems(List<VendorGRItem> items,
                                                          VendorGoodsReceivedData vendorGoodsReceivedData) throws SumoException {
        List<VendorGoodsReceivedItemData> vendorItemList = new ArrayList<>();
        for (VendorGRItem item : items) {
            VendorGoodsReceivedItemData itemData = new VendorGoodsReceivedItemData();
            itemData.setUnitPrice(item.getUnitPrice());
            itemData.setConversionRatio(item.getConversionRatio());
            itemData.setPackagingName(item.getPackagingName());
            itemData.setPackagingId(item.getPackagingId());
            itemData.setPackagingQuantity(item.getPackagingQty());
            itemData.setReceivedQty(
                    AppUtils.multiplyWithScale(SCMUtil.convertToBigDecimal(item.getPackagingQty()), item.getConversionRatio(),6));
            itemData.setHsn(item.getHsn());
            itemData.setUnitOfMeasure(item.getUnitOfMeasure());
            itemData.setSkuId(item.getSkuId());
            itemData.setSkuName(item.getSkuName());
            itemData.setTotalTax(item.getTotalTax());
            itemData.setTotalPrice(item.getTotalCost());
            itemData.setTotalAmount(item.getAmountPaid());
            itemData.setExpiryDate(item.getExpiryDate());
            itemData.setGoodsReceivedData(vendorGoodsReceivedData);
            itemData = goodsReceiveManagementDao.add(itemData, false);

            List<ItemTaxDetailData> taxDetailDataList = new ArrayList<>();
            for (OtherTaxDetail tax : item.getTaxes()) {
                ItemTaxDetailData taxData = new ItemTaxDetailData();
                taxData.setTaxType(tax.getTaxCategory());
                taxData.setTaxPercentage(tax.getPercentage());
                taxData.setTaxValue(tax.getValue());
                taxData.setGrItem(itemData);
                taxData = goodsReceiveManagementDao.add(taxData, false);
                taxDetailDataList.add(taxData);
            }
            goodsReceiveManagementDao.flush();

            // Saving Mapping Data for Mapping PO Items to GR Items
            if (item.getUsedPOItems() != null && !item.getUsedPOItems().isEmpty()) {
                Map<Integer, BigDecimal> poItemList = item.getUsedPOItems();
                for (Integer poItemId : poItemList.keySet()) {
                    BigDecimal packagingQty = poItemList.get(poItemId);
                    PurchaseOrderItemData purchaseItem = goodsReceiveManagementDao.find(PurchaseOrderItemData.class,
                            poItemId);
                    if (purchaseItem != null && packagingQty != null) {
                        VendorGrPoItemMappingData mappingData = new VendorGrPoItemMappingData();
                        mappingData.setPoItem(purchaseItem);
                        mappingData.setGrItem(itemData);
                        mappingData.setPackagingQty(packagingQty);
                        mappingData.setAcceptedQty(packagingQty);
                        mappingData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                        mappingData.setLastUpdatedBy(vendorGoodsReceivedData.getCreatedBy());
                        goodsReceiveManagementDao.add(mappingData, false);
                    }
                }
                goodsReceiveManagementDao.flush();
            }
            itemData.setTaxes(taxDetailDataList);
            vendorItemList.add(itemData);
        }
        return vendorItemList;
    }

    private List<PurchaseOrderVendorGRMappingData> addMappings(Set<PurchaseOrderData> purchaseOrderDataSet,
                                                               VendorGoodsReceivedData vendorGoodsReceivedData) throws SumoException {
        List<PurchaseOrderVendorGRMappingData> mappingDataList = new ArrayList<>();
        for (PurchaseOrderData po : purchaseOrderDataSet) {
            PurchaseOrderVendorGRMappingData mappingData = new PurchaseOrderVendorGRMappingData();
            mappingData.setPurchaseOrderData(po);
            mappingData.setVendorGoodsReceivedData(vendorGoodsReceivedData);
            mappingDataList.add(goodsReceiveManagementDao.add(mappingData, false));
        }
        goodsReceiveManagementDao.flush();
        return mappingDataList;
    }

    private Float getGoodReceivedAmount(GoodsReceived goodsReceived) {
        Float total = 0.0f;
        for (GoodsReceivedItem goodsReceivedItem : goodsReceived.getGoodsReceivedItems()) {
            float received = goodsReceivedItem.getReceivedQuantity() != null ? goodsReceivedItem.getReceivedQuantity()
                    : 0.0f;
            if (goodsReceivedItem.getNegotiatedUnitPrice() != null) {
                total = total + goodsReceivedItem.getNegotiatedUnitPrice().floatValue() * received;
            }
        }
        return total;
    }

    private BigDecimal getGrItemTaxAmount(GoodsReceivedItemData goodsReceivedItemData){
        if(Objects.isNull(goodsReceivedItemData.getTaxAmount())){
            return BigDecimal.ZERO;
        }
        BigDecimal singleQtyTax = AppUtils.divideWithScale10(goodsReceivedItemData.getTaxAmount(),goodsReceivedItemData.getTransferredQuantity());
        return  AppUtils.multiplyWithScale10(goodsReceivedItemData.getReceivedQuantity(),singleQtyTax);
    }

    private void updateGRTotalAmountWithTax(GoodsReceivedData goodsReceivedData){
        BigDecimal totalTax = BigDecimal.ZERO;
        for(GoodsReceivedItemData grItem : goodsReceivedData.getGoodsReceivedItemDatas()){
            totalTax = totalTax.add(grItem.getTaxAmount());
        }
        goodsReceivedData.setTotalAmount(goodsReceivedData.getTotalAmount().add(totalTax));
    }



    private List<GoodsReceived> getGrListFromGrDataList(List<GoodsReceivedData> goodsReceivedDataList ) {
        return getGrListWithGrItemsFromGrDataList(goodsReceivedDataList, false);
    }

    private List<GoodsReceived> getGrListWithGrItemsFromGrDataList(List<GoodsReceivedData> goodsReceivedDataList, boolean getChild) {
        List<GoodsReceived> goodsReceivedList = new ArrayList<>();
        for (GoodsReceivedData goodsReceivedData : goodsReceivedDataList) {
            IdCodeName generatedBy = SCMUtil.generateIdCodeName(goodsReceivedData.getGeneratedBy(), "",
                    masterDataCache.getEmployees().get(goodsReceivedData.getGeneratedBy()));
            IdCodeName receivedBy = null;
            if (goodsReceivedData.getReceivedBy() != null) {
                receivedBy = SCMUtil.generateIdCodeName(goodsReceivedData.getReceivedBy(), "",
                        masterDataCache.getEmployees().get(goodsReceivedData.getReceivedBy()));
            }
            IdCodeName cancelledBy = null;
            if (goodsReceivedData.getCancelledBy() != null) {
                cancelledBy = SCMUtil.generateIdCodeName(goodsReceivedData.getCancelledBy(), "",
                        masterDataCache.getEmployees().get(goodsReceivedData.getCancelledBy()));
            }
            IdCodeName generatedForUnit = SCMUtil.generateIdCodeName(goodsReceivedData.getGeneratedForUnitId(), "",
                    masterDataCache.getUnitBasicDetail(goodsReceivedData.getGeneratedForUnitId()).getName());
            IdCodeName generationUnit = SCMUtil.generateIdCodeName(goodsReceivedData.getGenerationUnitId(), "",
                    masterDataCache.getUnitBasicDetail(goodsReceivedData.getGenerationUnitId()).getName());
            goodsReceivedList.add(SCMDataConverter.convert(goodsReceivedData, generatedBy, receivedBy, cancelledBy,
                    generatedForUnit, generationUnit, getChild, scmCache, masterDataCache));
        }
        return goodsReceivedList;
    }

    @Override
    public StockManagementDao getDao() {
        return stockManagementDao;
    }

    @Override
    public SCMCache getScmCache() {
        return scmCache;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.AbstractStockManagementService#getPriceDao ()
     */
    @Override
    public PriceManagementDao getPriceDao() {
        return priceDao;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.AbstractStockManagementService#
     * getInventoryService()
     */
    @Override
    public InventoryService getInventoryService() {
        return inventoryService;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.AbstractStockManagementService#
     * getMasterDataCache()
     */
    @Override
    public MasterDataCache getMasterDataCache() {
        return masterDataCache;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.AbstractStockManagementService#getProperties()
     */
    @Override
    public EnvProperties getProperties() {
        return props;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getPendingPoSoRoStatus(String handOverData, Integer unitId) {
        UnitDetail unit = scmCache.getUnitDetail(unitId);
        return goodsReceiveManagementDao.findStatusForPoSoRo(handOverData, unitId, unit.getUnitName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AssetDefinition> getAssetsForPayment(List<Integer> grIds) {

        List<AssetDefinition> assetDefinitionList = new ArrayList<>();
        List<AssetDefinitionData> assetDefinitionDataList = goodsReceiveManagementDao.getAssetForGr(grIds);
        for(AssetDefinitionData assetDefinitionData : assetDefinitionDataList){
            AssetDefinition assetDefinition = scmAssetManagementService.convertAssetDefinitionDataToAssetDefinition(assetDefinitionData,false);
            if(Objects.nonNull(assetDefinition)){
                assetDefinitionList.add(assetDefinition);
            }
        }
        return  assetDefinitionList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean initiateGREvent(GoodsReceived goodsReceived, Integer unitId, Integer userId) throws SumoException {

     try{
         List<StockEventDefinitionData> res =   scmAssetManagementDao.getInitiatedEventForUnit(unitId);
         if(!res.isEmpty()){
             throw new SumoException("EVENT_EXISTS","Can Not Initiate GR Event, Please Complete initiated "+ res.get(0).getEventType() + " event, for GR Id : "+res.get(0).getGrId());
         }


        StockEventDefinitionData stockEventDefinitionData = new StockEventDefinitionData();
        stockEventDefinitionData.setGrId(goodsReceived.getId());
        stockEventDefinitionData.setRoId(goodsReceived.getRequestOrderId());
        stockEventDefinitionData.setToId(goodsReceived.getTransferOrderId());
        stockEventDefinitionData.setBudgetType(goodsReceived.getType());
        stockEventDefinitionData.setSubType(goodsReceived.getTransferOrderType().name());
        stockEventDefinitionData.setEventStatus("INITIATED");
        stockEventDefinitionData.setUnitId(unitId);
        stockEventDefinitionData.setReceivingUnitId(unitId);
        stockEventDefinitionData.setInitiatedBy(userId);
        stockEventDefinitionData.setEventType(StockEventType.ASSET_RECEIVING.value());
        stockEventDefinitionData.setUnitType(masterDataCache.getUnit(unitId).getFamily().value());
        stockEventDefinitionData.setLastUpdationTime(SCMUtil.getCurrentTimestamp());
        stockEventDefinitionData.setEventCreationDate(SCMUtil.getCurrentTimestamp());
        stockEventDefinitionData = goodsReceiveManagementDao.add(stockEventDefinitionData,true);

        List<GoodsReceivedItem> grItems =  goodsReceived.getGoodsReceivedItems();
        List<FaGrData> faGrDataList = new ArrayList<>();
        List<StockEventAssetMappingDefinitionData> assetData = new ArrayList<>();
        for(GoodsReceivedItem item : grItems){
        Float qty =  item.getReceivedQuantity();
        if(qty>0){
            FaGrData faGrData = new FaGrData();
            faGrData.setAssetId(item.getAssociatedAssetId());
            faGrData.setReceivedQty(BigDecimal.valueOf(qty));
            faGrData.setEventId(stockEventDefinitionData.getEventId());
            faGrData.setScannedQty(BigDecimal.valueOf(0));
            faGrDataList.add(faGrData);
            }
        }
        goodsReceiveManagementDao.addAll(faGrDataList);
        return true;
    }catch (Exception e){
         log.error("####### Error in initiateGREvent ######, {}",e.getMessage());
         e.printStackTrace();
        throw new SumoException("GR_EVENT_ERROR",e.getMessage());
     }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public GrStockEvent getGrStockEvent(Integer grId) throws SumoException {
       try {
           StockEventDefinitionData stockEventDefinitionData =  scmAssetManagementDao.getEventFromGr(grId,"INITIATED");
           if(stockEventDefinitionData!=null) {
               List<FaGrData> faGrDataList = scmAssetManagementDao.getFaGrDataForEvent(stockEventDefinitionData.getEventId());
               GrStockEvent grStockEvent = new GrStockEvent();
               grStockEvent.setStockEventDefinitionData(stockEventDefinitionData);
               grStockEvent.setFaGrDataList(faGrDataList);
               return grStockEvent;
           }
           return  null;
       }catch (Exception e){
           log.error("####### Error in getGrStockEvent  ######, {}",e.getMessage());
           e.printStackTrace();
           throw new SumoException("GR_EVENT_ERROR",e.getMessage());
       }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean cancelGrEvent(Integer grId) throws SumoException {
        try {
            StockEventDefinitionData stockEventDefinitionData =  scmAssetManagementDao.getEventFromGr(grId,"INITIATED");
            if(stockEventDefinitionData!=null){
            stockEventDefinitionData.setEventStatus("ABANDONED");
            stockEventDefinitionData.setLastUpdationTime(SCMUtil.getCurrentTimestamp());
            goodsReceiveManagementDao.update(stockEventDefinitionData,true);
            log.info("################ Gr Event Cancel - EventId : {} - GrId : {} ####################",stockEventDefinitionData.getEventId(),grId);
            return true;
            }
            log.info("################ Gr Event Cancel [Event Not Found]  GrId : {} ####################",grId);
            return false;
        }catch (Exception e){
            log.error("####### Error in cancelGrEvent  ######, {}",e.getMessage());
            e.printStackTrace();
            throw new SumoException("GR_EVENT_ERROR",e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  Boolean settleGrEvent(Integer eventId, Integer grId) throws SumoException {
        try {
            StockEventDefinitionData stockEventDefinitionData = scmAssetManagementDao.getEventFromGr(grId,"INITIATED");
           if(stockEventDefinitionData!=null){
               stockEventDefinitionData.setEventStatus("COMPLETED");
               stockEventDefinitionData.setLastUpdationTime(SCMUtil.getCurrentTimestamp());
               goodsReceiveManagementDao.update(stockEventDefinitionData,true);
               log.info("################ Gr Event Settled - EventId : {} - GrId : {} ####################",stockEventDefinitionData.getEventId(),grId);
               return true;
           }
            log.info("################ Gr Event not found, grId : {} ####################",grId);
           return true;
        }catch (Exception e){
            log.error("####### Error in settleGrEvent - GrId : {}  ######, {}",grId,e.getMessage());
            e.printStackTrace();
            throw new SumoException("GR_EVENT_ERROR",e.getMessage());
        }
    }
}
