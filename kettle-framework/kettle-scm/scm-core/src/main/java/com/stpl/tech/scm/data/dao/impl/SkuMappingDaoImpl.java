/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SCMError;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.mapper.VendorContractMapper;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.util.SCMConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.model.PageRequestDetail;
import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.data.model.PurchaseProfile;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.data.model.SkuPriceData;
import com.stpl.tech.scm.data.model.SkuPriceHistory;
import com.stpl.tech.scm.data.model.UnitCategoryData;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.data.model.UnitSkuMapping;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.data.model.VendorContractItem;
import com.stpl.tech.scm.data.model.VendorSkuMapping;
import com.stpl.tech.scm.data.model.ZipCodeDistanceMapping;
import com.stpl.tech.scm.data.model.businessVendorMapping;
import com.stpl.tech.scm.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.scm.domain.model.ChangeType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.PriceStatus;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.StatusType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.VendorContract;
import com.stpl.tech.scm.domain.model.VendorContractVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Mohit
 */

@Repository
public class SkuMappingDaoImpl extends SCMAbstractDaoImpl implements SkuMappingDao {

	private static final Logger LOG = LoggerFactory.getLogger(SkuMappingDaoImpl.class);

	@Autowired
	private SCMCache cache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private SCMMetadataService scmMetadataService;

	@Autowired
	private SCMConstants scmConstants;
	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updatePrices(com.stpl.tech.scm.
	 * domain.model.SkuPriceUpdate)
	 */
	@Override
	public boolean updatePrices(SkuPriceUpdate data) throws SumoException {
		cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data, PriceStatus.CREATED);
		cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data, PriceStatus.PENDING);
		cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data, PriceStatus.REACTIVATION_REQUESTED);
		cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data, PriceStatus.DEACTIVATION_REQUESTED);
		checkApprovedPriceUpdate(ChangeType.PRICE_UPDATE, data, List.of(PriceStatus.APPROVED.name(),
				PriceStatus.REACTIVATION_ACCEPTED.name(),PriceStatus.DEACTIVATION_ACCEPTED.name()));
		SkuPriceData current = manager.find(SkuPriceData.class, data.getDetail().getKeyId());
		SkuPriceHistory price = new SkuPriceHistory();
		price.setCreatedAt(AppUtils.getCurrentTimestamp());
		price.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
		price.setCurrentPrice(current.getPrice());
		price.setNegotiatedPrice(data.getDetail().getUpdated().getValue());
		price.setRecordStatus(PriceStatus.CREATED.name());
		price.setSkuPriceDataId(data.getDetail().getKeyId());
//		price.setStartDate(data.getDetail().getUpdated().getDate());
		price.setChangeType(ChangeType.PRICE_UPDATE.name());
		current.setIsPriceChangeRequested(AppConstants.YES);
		manager.persist(price);
		manager.flush();
		return true;
	}

	private void checkApprovedPriceUpdate(ChangeType priceUpdate, SkuPriceUpdate data, List<String> status) throws SumoException {
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and spd.recordStatus in (:status)");
		query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
		query.setParameter("status", status);
		List<SkuPriceHistory> history = query.getResultList();
		if (!history.isEmpty()) {
			throw new SumoException("Price for SKU "+data.getDetail().getSku().getName()+" Found in Approved State");
		}
	}

	@Override
	public void updateSkuPricesFromCurrentDay(SkuPriceUpdate data) {
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and spd.recordStatus = :status and spd.changeType = :changeType");
		query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
		query.setParameter("status", PriceStatus.CREATED.name());
		query.setParameter("changeType", ChangeType.PRICE_UPDATE.name());
		List<SkuPriceHistory> history = query.getResultList();
		BigDecimal price = null;
		if (history != null) {
			for (SkuPriceHistory p : history) {
				p.setUpdatedAt(AppUtils.getCurrentTimestamp());
				p.setUpdatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
				p.setRecordStatus(PriceStatus.APPLIED.name());
				p.setChangeType(ChangeType.PRICE_UPDATE.name());
				price = p.getNegotiatedPrice();
			}
		}
		SkuPriceData current = manager.find(SkuPriceData.class, data.getDetail().getKeyId());
		current.setPrice(price);
		manager.flush();
	}

	@Override
	public 	void processPriceRequestForVendor(SkuPriceUpdate val, PriceStatus key, PriceStatus currentStatus, Set<Integer> processedId) throws SumoException {
		checkApprovedPriceUpdate(ChangeType.PRICE_UPDATE, val, List.of(PriceStatus.APPROVED.name(),
				PriceStatus.REACTIVATION_ACCEPTED.name(),PriceStatus.DEACTIVATION_ACCEPTED.name()));
		if (!processedId.contains(val.getDetail().getKeyId())){
			cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, val, PriceStatus.PENDING);
			cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, val, PriceStatus.REACTIVATION_REQUESTED);
			cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, val, PriceStatus.DEACTIVATION_REQUESTED);
		}
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and spd.recordStatus = :status and spd.changeType = :changeType order by 1 desc");
		query.setParameter("skuPriceDataId", val.getDetail().getKeyId());
		query.setParameter("status", currentStatus.name());
		query.setParameter("changeType", ChangeType.PRICE_UPDATE.name());
		query.setMaxResults(1);
		List<SkuPriceHistory> history = query.getResultList();
		if (history != null) {
			for (SkuPriceHistory p : history) {
				p.setUpdatedAt(AppUtils.getCurrentTimestamp());
				p.setUpdatedBy(getName(val.getEmployeeName(), val.getEmployeeId()));
				p.setRecordStatus(key.name());
				p.setChangeType(ChangeType.PRICE_UPDATE.name());
				SkuPriceData data = manager.find(SkuPriceData.class,p.getSkuPriceDataId());
				if (Objects.nonNull(data)) {
					data.setIsPriceChangeRequested(AppConstants.YES);
				} else {
					throw new SumoException("Price Data Not Found"+ p.getSkuPriceDataId());
				}
				processedId.add(val.getDetail().getKeyId());
			}
		}
		manager.flush();
	}

	@Override
	public boolean addPrice(SkuPriceUpdate data) {
		// cancelExistingPriceUpdates(ChangeType.PRICE_UPDATE, data);

		SkuPriceData current = new SkuPriceData();
		current.setPackagingId(data.getDetail().getPkg().getId());
		current.setSkuId(data.getDetail().getSku().getId());
		current.setVendorId(data.getDetail().getVendor().getId());
		current.setDispatchLocation(data.getDetail().getDispatch().getCode());
		current.setDeliveryLocation(data.getDetail().getDelivery().getCode());
		current.setPrice(data.getDetail().getCurrent().getValue());
//		current.setStartDate(AppUtils.getCurrentTimestamp());
		current.setStatus(AppConstants.ACTIVE);
		current.setLeadTime(data.getDetail().getLeadTime());
		manager.persist(current);
        try {
			scmMetadataService.saveAuditLog(current.getSkuPriceKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), data.getEmployeeId(), ((Object) current),
					AuditChangeLogTypes.NEW_ENTRY.value());
		}catch (Exception e){
			LOG.info("Error While Saving Audit Log Data In Mongo",e);
		}

		SkuPriceHistory price = new SkuPriceHistory();
		price.setCreatedAt(AppUtils.getCurrentTimestamp());
		price.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
		price.setCurrentPrice(current.getPrice());
		price.setNegotiatedPrice(current.getPrice());
		price.setRecordStatus(PriceStatus.CREATED.name());
		price.setSkuPriceDataId(current.getSkuPriceKeyId());
//		price.setStartDate(AppUtils.getCurrentTimestamp());
		price.setChangeType(ChangeType.PRICE_UPDATE.name());
		manager.persist(price);
		manager.flush();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updatePrices(com.stpl.tech.scm.
	 * domain.model.SkuPriceUpdate)
	 */
	@Override
	public boolean cancelPriceUpdate(SkuPriceUpdate data) {
		cancelExistingPriceUpdates(ChangeType.PRICE_CANCEL, data, PriceStatus.CREATED);
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updatePrices(com.stpl.tech.scm.
	 * domain.model.SkuPriceUpdate)
	 */
	@Override
	public boolean updatePriceStatus(SkuPriceUpdate data) {
		SkuPriceData current = manager.find(SkuPriceData.class, data.getDetail().getKeyId());
		current.setStatus(data.getDetail().getStatus());
		SkuPriceHistory price = new SkuPriceHistory();
		price.setCreatedAt(AppUtils.getCurrentTimestamp());
		price.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
		price.setCurrentPrice(current.getPrice());
		price.setNegotiatedPrice(current.getPrice());
		price.setRecordStatus(AppConstants.ACTIVE.equals(data.getDetail().getStatus()) ? PriceStatus.ACTIVATED.name()
				: PriceStatus.DEACTIVATED.name());
		price.setSkuPriceDataId(data.getDetail().getKeyId());
		price.setStartDate(current.getStartDate());
		price.setChangeType(ChangeType.STATUS.name());
		manager.persist(price);
		manager.flush();
		return true;
	}

	private void cancelExistingPriceUpdates(ChangeType type, SkuPriceUpdate data, PriceStatus created) {
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and spd.recordStatus = :status");
		query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
		query.setParameter("status", created.name());
		List<SkuPriceHistory> history = query.getResultList();
		if (history != null) {
			for (SkuPriceHistory p : history) {
				p.setUpdatedAt(AppUtils.getCurrentTimestamp());
				p.setUpdatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
				if (PriceStatus.REACTIVATION_REQUESTED.name().equalsIgnoreCase(p.getRecordStatus())) {
					p.setRecordStatus(PriceStatus.APPLIED.name());
				} else if (PriceStatus.DEACTIVATION_REQUESTED.name().equalsIgnoreCase(p.getRecordStatus())) {
					p.setRecordStatus(PriceStatus.APPLIED.name());
				} else {
					p.setRecordStatus(PriceStatus.CANCELLED.name());
				}
				p.setChangeType(type.name());
			}
		}
		manager.flush();
	}

	private void applyPriceUpdates(ChangeType type, SkuPriceUpdate data, PriceStatus created, VendorContractInfo item) {
		cancelPriceUpdate(data);
		Query query = manager.createQuery(
				"from SkuPriceHistory spd where spd.skuPriceDataId = :skuPriceDataId and spd.recordStatus = :status");
		query.setParameter("skuPriceDataId", data.getDetail().getKeyId());
		query.setParameter("status", PriceStatus.APPROVED.name());
		List<SkuPriceHistory> history = query.getResultList();
		if (history != null) {
			for (SkuPriceHistory p : history) {
				p.setUpdatedAt(AppUtils.getCurrentTimestamp());
				p.setUpdatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
				p.setRecordStatus(created.name());
				p.setChangeType(type.name());
				p.setStartDate(item.getStartDate());
				p.setEndDate(item.getEndDate());
				p.setContractId(item.getVendorContractId());

				// update sku price data
				SkuPriceData skuPriceData = manager.find(SkuPriceData.class,p.getSkuPriceDataId());
				if (PriceStatus.APPLIED.equals(created)){
					skuPriceData.setStartDate(item.getStartDate());
					skuPriceData.setEndDate(item.getEndDate());
					skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
					skuPriceData.setSkuPriceKeyId(p.getSkuPriceHistoryId());
					skuPriceData.setPrice(p.getNegotiatedPrice());
				} else {
					skuPriceData.setStatus(AppConstants.IN_ACTIVE);
				}
			}
		}
		manager.flush();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#searchPrices(int,
	 * java.lang.String)
	 */
	@Override
	public List<SkuPriceDetail> searchPricesBySku(int skuId, String deliveryLocation) {
		Query query = manager
				.createQuery("from SkuPriceData spd where spd.skuId = :skuId and spd.deliveryLocation = :location");
		query.setParameter("location", deliveryLocation);
		query.setParameter("skuId", skuId);

		return convert(query.getResultList(), true, false);
	}

	@Override
	public List<SkuPriceDetail> searchPricesByDispatchLocation(List<Integer> skuIds, String dispatchLocation,
			String locationCode) {
		String queryStr = "FROM SkuPriceData spd WHERE spd.skuId in :skuIdList "
				+ "and spd.deliveryLocation=:deliveryLocation and spd.dispatchLocation = :location";
		Query query = manager.createQuery(queryStr);
		query.setParameter("location", dispatchLocation);
		query.setParameter("deliveryLocation", locationCode);
		query.setParameter("skuIdList", skuIds);

		return convert(query.getResultList(), true, false);
	}

	private List<SkuPriceDetail> convert(List<SkuPriceData> results, boolean getUpdated, boolean setUpdated) {
		List<SkuPriceDetail> list = new ArrayList<>();
		results.stream().forEach((record) -> {
			SkuPriceDetail price = SCMDataConverter.convert(cache, masterDataCache, record,getUpdated);
			if (setUpdated && Objects.nonNull(price.getUpdated().getValue())) {
				list.add(price);
			} else if (!setUpdated) {
				list.add(price);
			}
		});
		return list;
	}

	private List<SkuPriceDetail> convert(List<SkuPriceData> results) {
		List<SkuPriceDetail> list = new ArrayList<>();
		results.stream().forEach((record) -> {
			SkuPriceDetail price = SCMDataConverter.convert(cache, masterDataCache, record,true);
			list.add(price);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#searchPrices(int,
	 * java.lang.String)
	 */
	@Override
	public List<SkuPriceDetail> searchPricesByVendor(int vendorId, String dispatchLocation) {
		Query query = manager.createQuery(
				"from SkuPriceData spd where spd.vendorId = :vendorId and spd.dispatchLocation = :location and spd.status = :status");
		query.setParameter("location", dispatchLocation);
		query.setParameter("vendorId", vendorId);
		query.setParameter("status", AppConstants.ACTIVE);
		return convert(query.getResultList(), true, false);
	}

	@Override
	public List<SkuPriceDetail> searchPricesByVendorDeliveryLocation(int vendorId, String deliveryLocation, boolean isUpdated, boolean setUpdated) {
		StringBuilder ddl = new StringBuilder("From SkuPriceData spd where spd.vendorId = :vendorId ");
		if (Objects.nonNull(deliveryLocation)) {
			ddl.append("and spd.deliveryLocation = :location");
		}
		Query query = manager.createQuery(ddl.toString());
		if (Objects.nonNull(deliveryLocation)) {
			query.setParameter("location", deliveryLocation);
		}
		query.setParameter("vendorId", vendorId);
		return convert(query.getResultList(), isUpdated, setUpdated);

	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#searchSkuMappingsForUnit(int)
	 */
	@Override
	public List<IdCodeNameStatus> searchSkuMappingsForUnit(int unitId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
		"select DISTINCT usm.skuId, sdd.skuName, sdd.skuStatus, usm.mappingStatus, sdd.linkedProduct.categoryDefinition.categoryName, " +
			"sdd.linkedProduct.subCategoryDefinition.name, usm.profile, sdd.linkedProduct.recipeRequired, inv.listName," +
			"case when usm.productionUnit is NULL then 'NULL' else pd.productionUnitName end AS NAME, usm.packagingId  , usm.taxCode, usm.voDisContinuedFrom, usm.roDisContinuedFrom " +
			"from UnitSkuMapping usm, SkuDefinitionData sdd,InventoryListTypeData inv , ProductionUnitData  pd" +
			" where sdd.skuId = usm.skuId and usm.unitId = :unitId and usm.inventoryList = inv.id " +
			"and (usm.productionUnit is null or usm.productionUnit= pd.id ) ");
		query.setParameter("unitId", unitId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setMappingStatus((String) record[3]);
			o.setCategory((String) record[4]);
			o.setSubCategory((String) record[5]);
			if (record[6] == null) {
				o.setProfile("NULL");
			} else {
				o.setProfile((String) record[6]);}
			if(record[7] != null){
			o.setRecipeRequired(SCMUtil.getStatus((String) record[7]));}
			o.setInventoryList((String)record[8]);
			o.setProductionUnit((String) record[9]);
			o.setPackagingId((Integer) record[10]);
			o.setTaxCategoryCode((String) record[11]);
			if (Objects.nonNull(record[12])) {
				o.setVoDisContinuedFrom(AppUtils.getFormattedTime((Date) record[12], "yyyy-MM-dd"));
			}
			if (Objects.nonNull(record[13])) {
				o.setRoDisContinuedFrom(AppUtils.getFormattedTime((Date) record[13], "yyyy-MM-dd"));
			}
			list.add(o);
		});

		return list;
	}



	@Override
	public List<UnitSkuVendorMapping> searchActiveVendorMappingsForUnit(int unitId) {
		Query query = manager.createQuery("SELECT F FROM UnitSkuMapping E, UnitSkuVendorMapping F"
				+ " where F.unitSkuMapping.unitSkuMappingId=E.unitSkuMappingId and E.unitId = :unitId"
				+ " and E.mappingStatus = :status");
		query.setParameter("unitId", unitId).setParameter("status", SwitchStatus.ACTIVE.name());
		List<UnitSkuVendorMapping> vendorMappings = query.getResultList();
		return vendorMappings;
	}


    @Override
    public List<UnitSkuMapping> searchSkuMappingsForUnitAndVendor(int unitId, int vendorId) {
        Query query = manager.createQuery("SELECT DISTINCT E FROM UnitSkuMapping E, UnitSkuVendorMapping F"
                + " where F.unitSkuMapping.unitSkuMappingId=E.unitSkuMappingId and E.unitId = :unitId"
                + " and E.mappingStatus = :status and F.mappingStatus = :status and F.vendorId = :vendorId");
        query.setParameter("unitId", unitId)
                .setParameter("vendorId",vendorId)
                .setParameter("status", SwitchStatus.ACTIVE.name());
        List<UnitSkuMapping> skuMapping = query.getResultList();
        return skuMapping;
    }



	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#searchUnitMappingsForSku(int)
	 */
	@Override
	public List<IdCodeNameStatus> searchUnitMappingsForSku(int skuId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select distinct vd.unitId, vd.unitName,vd.unitStatus,vsm.mappingStatus,vd.unitCategory.name,inv.listName ," +
					"case when vsm.productionUnit is null then 'NULL' else pd.productionUnitName end as NAME , vsm.packagingId , vsm.taxCode " +
						", vsm.voDisContinuedFrom, vsm.roDisContinuedFrom" +
					" from UnitSkuMapping vsm, UnitDetailData vd,InventoryListTypeData inv ,ProductionUnitData pd" +
					" where vd.unitId = vsm.unitId and vsm.skuId = :skuId and vsm.inventoryList = inv.id" +
					" and (vsm.productionUnit is null or vsm.productionUnit=pd.id)");
		query.setParameter("skuId", skuId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setMappingStatus((String) record[3]);
			o.setCategory((String) record[4]);
			o.setInventoryList((String)record[5]);
			o.setProductionUnit((String) record[6]);
			o.setPackagingId((Integer) record[7]);
			o.setTaxCategoryCode((String) record[8]);
			if (Objects.nonNull(record[9])) {
				o.setVoDisContinuedFrom(AppUtils.getFormattedTime((Date) record[9], "yyyy-MM-dd"));
			}
			if (Objects.nonNull(record[10])) {
				o.setRoDisContinuedFrom(AppUtils.getFormattedTime((Date) record[10], "yyyy-MM-dd"));
			}
			list.add(o);
		});
		return list;
	}

	@Override
	public boolean updateSkuProfiles(Map<Integer,String> skuListWithInventoryListId, int unitId, String profile,Map<String,Integer> inventoryListName){
		updateSkuProfile(skuListWithInventoryListId.keySet().stream().collect(Collectors.toList()), unitId,profile);
		Set<Integer> skus = getAllSkusForUnit(unitId);

		skuListWithInventoryListId.forEach((skuID,inventoryName)->{
			if (!skus.contains(skuID)) {
				UnitSkuMapping mapping = new UnitSkuMapping();
				mapping.setUnitId(unitId);
				mapping.setSkuId(skuID);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setProfile(profile);
				mapping.setInventoryList(inventoryListName.get(inventoryName));
				manager.persist(mapping);
			}
		});
		manager.flush();
		scmCache.refreshProductRecipeKeyMapping();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForUnit(int,
	 * java.lang.String, int, java.util.List)
	 */
	@Override
	public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds) {
		activateUnitSkuMapping(employeeId, name, unitId, skuIds);
		deactivateUnitSkuMapping(employeeId, name, unitId, skuIds);
		Set<Integer> skus = getAllSkusForUnit(unitId);
		for (Integer skuId : skuIds) {
			if (!skus.contains(skuId)) {
				UnitSkuMapping mapping = new UnitSkuMapping();
				mapping.setUnitId(unitId);
				mapping.setSkuId(skuId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(name, employeeId));
				if(mapping.getProfile()==null)
				{
					mapping.setProfile("P0");
				}
				mapping.setInventoryList(1);
				//set default
				manager.persist(mapping);
			}
		}
		manager.flush();
		scmCache.refreshProductRecipeKeyMapping();
		return true;
	}

	@Override
	public List<UnitSkuMapping> getUnitSkuMappingForUnitId(int unitId,List<Integer> skuIds){
		if(skuIds.isEmpty()){
			return new ArrayList<>();
		}
		Query query=manager.createQuery("FROM UnitSkuMapping WHERE skuId IN ( :skuIds ) and unitId = :unitId");
		query.setParameter("skuIds",skuIds);
		query.setParameter("unitId",unitId);
		return query.getResultList();
	}

	@Override

	public List<UnitSkuMapping> getUnitSkuMappingForSKUS(int skuId,List<Integer> unitIds)
	{
		Query query=manager.createQuery("FROM UnitSkuMapping WHERE unitId IN ( :unitIds ) and skuId = :skuId");
		query.setParameter("skuId",skuId);
		query.setParameter("unitIds",unitIds);
		return query.getResultList();
	}

	@Override
	public List<UnitSkuMapping> getActiveUnitSkuMappings(List<Integer> skuIds ,List<Integer> unitIds){
		Query query=manager.createQuery("FROM UnitSkuMapping WHERE unitId IN ( :unitIds ) and skuId IN ( :skuIds ) " +
		" and mappingStatus = :status ");
		query.setParameter("skuIds",skuIds);
		query.setParameter("unitIds",unitIds);
		query.setParameter("status",StatusType.ACTIVE.value());
		return query.getResultList();
	}


	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateUnitMappingsForSku(int,
	 * java.lang.String, int, java.util.List)
	 */
	@Override
	public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds) {
		activateSkuUnitMapping(employeeId, name, skuId, unitIds);
		deactivateSkuUnitMapping(employeeId, name, skuId, unitIds);
		Set<Integer> units = getAllUnitsForSku(skuId);
		for (Integer unitId : unitIds) {
			if (!units.contains(unitId)) {
				UnitSkuMapping mapping = new UnitSkuMapping();
				mapping.setUnitId(unitId);
				mapping.setSkuId(skuId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(name, employeeId));
				if(mapping.getProfile()==null)
				{
					mapping.setProfile("P0");
				}
				mapping.setInventoryList(1);
				manager.persist(mapping);
			}
		}
		manager.flush();
		scmCache.refreshProductRecipeKeyMapping();
		return true;
	}

	private Set<Integer> getAllUnitsForSku(int skuId) {

		Set<Integer> units = new HashSet<>();
		Query query = manager.createQuery("select unitId from UnitSkuMapping where skuId = :skuId");
		query.setParameter("skuId", skuId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			units.addAll(list);
		}
		return units;
	}

	private Set<Integer> getAllSkusForUnit(int unitId) {

		Set<Integer> skus = new HashSet<>();
		Query query = manager.createQuery("select skuId from UnitSkuMapping where unitId = :unitId");
		query.setParameter("unitId", unitId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			skus.addAll(list);
		}
		return skus;
	}

	private boolean updateSkuProfile(List<Integer> skuIds, int unitId, String profiles) {
		if (skuIds.isEmpty()) {
			return true;
		}
		Query query = manager.createQuery(
				"update UnitSkuMapping set profile = :profile, updatedAt = :updateAt  where "
						+ " skuId IN ( :skuIds ) and unitId = :unitId and mappingStatus = :mappingStatus");
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("profile", profiles);
		query.setParameter("skuIds", skuIds);
		query.setParameter("unitId", unitId);
		query.setParameter("updateAt",AppUtils.getCurrentTimestamp());
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean activateUnitSkuMapping(int employeeId, String name, int unitId, List<Integer> skuIds) {
		if (skuIds.isEmpty()) {
			return true;
		}
		Query query = manager.createQuery(
				"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId IN ( :skuIds ) and unitId = :unitId and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuIds", skuIds);
		query.setParameter("unitId", unitId);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean deactivateUnitSkuMapping(int employeeId, String name, int unitId, List<Integer> skuIds) {
		Query query = null;
		if (skuIds.isEmpty()) {
			query = manager.createQuery(
					"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ "  unitId = :unitId and mappingStatus = :mappingStatus");
		} else {
			query = manager.createQuery(
					"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ " skuId NOT IN ( :skuIds ) and unitId = :unitId and mappingStatus = :mappingStatus");
			query.setParameter("skuIds", skuIds);
		}
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("unitId", unitId);
		query.executeUpdate();
		return true;
	}

	private boolean deactivateBusinessVendorMapping(int employeeId, String employeeName, int businessId, List<Integer> vendorIds) {
		Query query = null;
		if (vendorIds.isEmpty()) {
			query = manager.createQuery(
					"update businessVendorMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ "  businessId = :businessId and mappingStatus = :mappingStatus");
		} else {
			query = manager.createQuery(
					"update businessVendorMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ " vendorId NOT IN ( :vendorIds ) and businessId = :businessId and mappingStatus = :mappingStatus");
			query.setParameter("vendorIds", vendorIds);
		}
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("updatedBy", getName(employeeName, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("businessId", businessId);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean activateSkuUnitMapping(int employeeId, String name, int skuId, List<Integer> unitIds) {
		Query query = manager.createQuery(
				"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId = :skuId and unitId IN ( :unitIds) and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("unitIds", unitIds);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean deactivateSkuUnitMapping(int employeeId, String name, int skuId, List<Integer> unitIds) {
		Query query = manager.createQuery(
				"update UnitSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId = :skuId and unitId NOT IN ( :unitIds) and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("unitIds", unitIds);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#searchSkuMappingsForVendor(int)
	 */
	@Override
	public List<IdCodeNameStatus> searchSkuMappingsForVendor(int vendorId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.skuId, vd.skuName,vd.skuStatus,vsm.mappingStatus, vd.linkedProduct.categoryDefinition.categoryName, " +
						"vd.linkedProduct.subCategoryDefinition.name, vsm.alias from VendorSkuMapping vsm, SkuDefinitionData vd where vd.skuId = vsm.skuId and vsm.vendorId = :vendorId");
		query.setParameter("vendorId", vendorId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setMappingStatus((String) record[3]);
			o.setCategory((String) record[4]);
			o.setSubCategory((String) record[5]);
			o.setAlias((String) record[6]);
			list.add(o);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#searchVendorMappingsForSku(int)
	 */
	@Override
	public List<IdCodeNameStatus> searchVendorMappingsForSku(int skuId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.vendorId, vd.entityName,vd.type,vd.status,vsm.mappingStatus from VendorSkuMapping vsm, VendorDetailData vd where vd.id = vsm.vendorId and vsm.skuId = :skuId");
		query.setParameter("skuId", skuId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setCategory((String) record[2]);
			o.setStatus((String) record[3]);
			o.setMappingStatus((String) record[4]);
			list.add(o);
		});
		return list;
	}

	@Override
	public List<IdCodeNameStatus> searchVendorMappingsForBusiness(int businessId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.vendorId, vd.entityName,vd.type,vd.status,vsm.mappingStatus from businessVendorMapping vsm, VendorDetailData vd where vd.id = vsm.vendorId and vsm.businessId = :businessId");
		query.setParameter("businessId", businessId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setCategory((String) record[2]);
			o.setStatus((String) record[3]);
			o.setMappingStatus((String) record[4]);
			list.add(o);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#allActiveUnits()
	 */
	@Override
	public List<IdCodeNameStatus> allActiveUnits() {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select ud.unitId, ud.unitName, ud.unitCategory, ud.unitStatus, ud.companyId, ud.unitRegion from UnitDetailData ud where ud.unitStatus = :status");
		query.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
            try{
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1] + "(" + masterDataCache.getCompany((Integer) record[4]).getShortCode() +")");
			o.setCode(((UnitCategoryData) record[2]).getCode());
			o.setCategory(((UnitCategoryData) record[2]).getName());
			o.setStatus((String) record[3]);
			o.setSubCategory((String) record[5]);
			list.add(o);
            }catch (Exception e){
                LOG.error("Error while fetching unit for {} ", record[4]);
            }
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#allActiveSKU()
	 */
	@Override
	public List<IdCodeNameStatus> allActiveSKU() {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select sdd.skuId, sdd.skuName,sdd.skuStatus, sdd.linkedProduct.categoryDefinition.categoryName, sdd.linkedProduct.subCategoryDefinition.name,inv.listName, sdd.linkedProduct.recipeRequired ,sdd.taxCategoryCode from SkuDefinitionData sdd,InventoryListTypeData inv where sdd.skuStatus = :status and sdd.inventoryList = inv.id ");
		query.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setCategory((String) record[3]);
			o.setSubCategory((String) record[4]);
			o.setInventoryList((String)record[5]);
			if(((String) record[6]).equals("Y")) {
				o.setRecipeRequired(true);
			}else {
				o.setRecipeRequired(false);
			}
			o.setTaxCategoryCode((String) record[7]);
			list.add(o);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#allActiveVendors()
	 */
	@Override
	public List<IdCodeNameStatus> allActiveVendors() {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery("select id, entityName, status,type,byPassContract from VendorDetailData where status = :status");
		query.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setCategory((String) record[3]);
			o.setByPassContract((String) record[4]);
			list.add(o);
		});
		return list;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	@Override
	public boolean updateVendorSkuMapping(int employeeId, String name, int vendorId, int skuId, String status) {
		Query query = manager.createQuery(
				"update VendorSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId = :skuId and vendorId = :vendorId");
		query.setParameter("status", status);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("vendorId", vendorId);
		query.executeUpdate();
		return true;
	}
	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean activateVendorSkuMapping(int employeeId, String name, int vendorId, List<Integer> skuIds) {
		Query query = manager.createQuery(
				"update VendorSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId IN ( :skuIds ) and vendorId = :vendorId and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuIds", skuIds);
		query.setParameter("vendorId", vendorId);
		query.executeUpdate();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#updateSkuMappingsForVendor(int,
	 * java.lang.String, int, int, java.lang.String)
	 */
	private boolean activateSkuVendorMapping(int employeeId, String name, int skuId, List<Integer> vendorIds) {
		Query query = manager.createQuery(
				"update VendorSkuMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " skuId = :skuId and vendorId IN ( :vendorIds) and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(name, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("vendorIds", vendorIds);
		query.executeUpdate();
		return true;
	}

	private boolean activateBusinessVendorMapping(int employeeId, String name, int businessId, List<Integer> vendorIds) {
		if(!vendorIds.isEmpty()) {
			Query query = manager.createQuery(
					"update businessVendorMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ " businessId = :businessId and vendorId IN ( :vendorIds) and mappingStatus = :mappingStatus");
			query.setParameter("status", AppConstants.ACTIVE);
			query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
			query.setParameter("updatedBy", getName(name, employeeId));
			query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
			query.setParameter("businessId", businessId);
			query.setParameter("vendorIds", vendorIds);
			query.executeUpdate();
		}
		return true;
	}

	private Set<Integer> getAllVendorsForSku(int skuId) {

		Set<Integer> vendors = new HashSet<>();
		Query query = manager.createQuery("select vendorId from VendorSkuMapping where skuId = :skuId");
		query.setParameter("skuId", skuId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			vendors.addAll(list);
		}
		return vendors;
	}

	private Set<Integer> getAllVendorsForBusinessId(int businessId) {

		Set<Integer> vendors = new HashSet<>();
		Query query = manager.createQuery("select vendorId from businessVendorMapping where businessId = :businessId");
		query.setParameter("businessId", businessId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			vendors.addAll(list);
		}
		return vendors;
	}

	private Set<Integer> getAllSkusForVendor(int vendorId) {

		Set<Integer> skus = new HashSet<>();
		Query query = manager.createQuery("select skuId from VendorSkuMapping where vendorId = :vendorId");
		query.setParameter("vendorId", vendorId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			skus.addAll(list);
		}
		return skus;
	}

	private String getName(String name, int id) {
		return name + " [" + id + "]";
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#addSkuMappingsForVendor(int,
	 * java.lang.String, int, java.util.List)
	 */
	@Override
	public boolean addSkuMappingsForVendor(int employeeId, String employeeName, int vendorId, List<IdCodeName> skuIds) {
		List<Integer> skuIdList = skuIds.stream().map(idCodeName -> {
			return idCodeName.getId();
		}).collect(Collectors.toList());
		activateVendorSkuMapping(employeeId, employeeName, vendorId, skuIdList);
		Set<Integer> skus = getAllSkusForVendor(vendorId);
		for (IdCodeName skuId : skuIds) {
			if (!skus.contains(skuId.getId())) {
				VendorSkuMapping mapping = new VendorSkuMapping();
				mapping.setVendorId(vendorId);
				mapping.setSkuId(skuId.getId());
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(employeeName, employeeId));
				if (skuId.getCode() != null) {
					mapping.setAlias(skuId.getCode());
				}
				manager.persist(mapping);
			}
		}
		manager.flush();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.scm.data.dao.SkuMappingDao#addVendorMappingsForSku(int,
	 * java.lang.String, int, java.util.List)
	 */

	@Override
	public boolean addVendorMappingsForBusiness(int employeeId, String employeeName, int businessId, List<Integer> vendorIds) {
		activateBusinessVendorMapping(employeeId, employeeName, businessId, vendorIds);
		deactivateBusinessVendorMapping(employeeId, employeeName, businessId, vendorIds);
		Set<Integer> vendors = getAllVendorsForBusinessId(businessId);
		for (Integer vendorId : vendorIds) {
			if (!vendors.contains(vendorId)) {
				businessVendorMapping mapping = new businessVendorMapping();
				mapping.setVendorId(vendorId);
				mapping.setBusinessId(businessId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(employeeName, employeeId));
				manager.persist(mapping);
			}
		}
		manager.flush();
		return true;
	}

	@Override
	public boolean addVendorMappingsForSku(int employeeId, String employeeName, int skuId, List<Integer> vendorIds) {
		activateSkuVendorMapping(employeeId, employeeName, skuId, vendorIds);
		Set<Integer> vendors = getAllVendorsForSku(skuId);
		for (Integer vendorId : vendorIds) {
			if (!vendors.contains(vendorId)) {
				VendorSkuMapping mapping = new VendorSkuMapping();
				mapping.setVendorId(vendorId);
				mapping.setSkuId(skuId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(getName(employeeName, employeeId));
				manager.persist(mapping);
			}
		}
		manager.flush();
		return true;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#
	 * searchSkuMappingsForVendorAndUnit(int, int)
	 */
	@Override
	public List<UnitVendorSkuMapping> searchSkuMappingsForVendorAndUnit(int unitId, int vendorId) {
		List<UnitVendorSkuMapping> list = new ArrayList<UnitVendorSkuMapping>();
		VendorDetail vendor = cache.getVendorDetail(vendorId);
		IdCodeName data = SCMUtil.generateIdCodeName(vendor.getVendorId(), "", vendor.getEntityName());
		Query query = manager.createNativeQuery(
				"SELECT usm.UNIT_SKU_MAPPING_ID, usm.SKU_ID, sd.SKU_NAME, usvm.UNIT_SKU_VENDOR_MAPPING_ID, usvm.MAPPING_STATUS FROM "
						+ " UNIT_SKU_MAPPING usm INNER JOIN "
						+ " VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID INNER JOIN "
						+ " SKU_DEFINITION sd ON sd.SKU_ID = vsm.SKU_ID LEFT OUTER JOIN "
						+ " UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID AND usvm.VENDOR_ID = vsm.VENDOR_ID WHERE "
						+ " usm.UNIT_ID = :unitId AND usm.MAPPING_STATUS = :status "
						+ " and vsm.MAPPING_STATUS = :status AND vsm.VENDOR_ID = :vendorId");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("unitId", unitId);
		query.setParameter("vendorId", vendorId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			UnitVendorSkuMapping o = new UnitVendorSkuMapping();
			o.setUnitSkuMappingId((Integer) record[0]);
			o.setSku(SCMUtil.generateIdCodeName((Integer) record[1], "", (String) record[2]));
			o.setVendor(data);
			o.setKeyId((Integer) record[3]);
			String status = (String) record[4];
			if (status != null) {
				o.setStatus(SwitchStatus.valueOf(status));
			} else {
				o.setStatus(SwitchStatus.IN_ACTIVE);
			}
			list.add(o);
		});
		return list;
	}

	@Override
	public List<Integer> searchSkuMappingIdsForVendorAndUnit(int unitId, int vendorId) {
		List<Integer> list = new ArrayList<>();
		VendorDetail vendor = cache.getVendorDetail(vendorId);
		IdCodeName data = SCMUtil.generateIdCodeName(vendor.getVendorId(), "", vendor.getEntityName());
		Query query = manager.createNativeQuery("SELECT usm.SKU_ID FROM UNIT_SKU_MAPPING usm "
				+ "INNER JOIN VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID LEFT OUTER JOIN "
				+ "UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID AND usvm.VENDOR_ID = vsm.VENDOR_ID"
				+ "WHERE usm.UNIT_ID = :unitId AND usm.MAPPING_STATUS = :status "
				+ "AND vsm.MAPPING_STATUS = :status AND vsm.VENDOR_ID = :vendorId");
		query.setParameter("status", SwitchStatus.ACTIVE.name());
		query.setParameter("unitId", unitId);
		query.setParameter("vendorId", vendorId);
		list = query.getResultList();
		return list;
	}

	@Override
	public List<SkuPriceDetail> searchSkuPricesForVendorAndUnit(int unitId, int vendorId, String dispatchLocation, String deliveryLocation) {
		try {
			Query query = manager.createNativeQuery("SELECT usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION,"
					+ " spd.DELIVERY_LOCATION, spd.SKU_PRICE, vsm.SKU_ALIAS, spd.LEAD_TIME, spd.SKU_PRICE_DATA_ID FROM  UNIT_SKU_MAPPING usm "
					+ " INNER JOIN VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID "
					+ " INNER JOIN SKU_PRICE_DATA spd ON spd.SKU_ID = usm.SKU_ID AND vsm.VENDOR_ID = spd.VENDOR_ID "
					+ " INNER JOIN UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID "
					+ " AND usvm.VENDOR_ID = vsm.VENDOR_ID WHERE usm.UNIT_ID = :unitId "
					+ " AND usm.MAPPING_STATUS = :status AND vsm.MAPPING_STATUS = :status "
					+ " AND vsm.VENDOR_ID = :vendorId AND spd.STATUS = :status AND spd.DISPATCH_LOCATION = :dispatchLocation"
					+ " AND spd.DELIVERY_LOCATION=:deliveryLocation");

			query.setParameter("status", SwitchStatus.ACTIVE.name())
			.setParameter("unitId", unitId)
			.setParameter("vendorId", vendorId)
			.setParameter("dispatchLocation",dispatchLocation)
			.setParameter("deliveryLocation",deliveryLocation);
			List<Object[]> list = query.getResultList();

			/*usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION, spd.DELIVERY_LOCATION, spd.SKU_PRICE*/
			return createSkuPriceDetailList(list);
		} catch (Exception e) {
			LOG.error("Encountered error while getting prices for vendor and unit", e);
		}
		return null;
	}

	@Override
	public List<SkuPriceDetail> getSkuPackagingPriceForVendorsForUnit(int unitId, Set<Integer> vendorIds, String deliveryLocation) {
		try {
			Query query = manager.createNativeQuery("SELECT usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION,"
				+ " spd.DELIVERY_LOCATION, spd.SKU_PRICE, vsm.SKU_ALIAS, spd.LEAD_TIME ,spd.SKU_PRICE_DATA_ID FROM  UNIT_SKU_MAPPING usm "
				+ " INNER JOIN VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID"
				+ " INNER JOIN SKU_PRICE_DATA spd ON spd.SKU_ID = usm.SKU_ID AND vsm.VENDOR_ID = spd.VENDOR_ID "
				+ " INNER JOIN UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID "
				+ " AND usvm.VENDOR_ID = vsm.VENDOR_ID WHERE usm.UNIT_ID = :unitId "
				+ " AND usm.MAPPING_STATUS = :status AND vsm.MAPPING_STATUS = :status "
				+ " AND vsm.VENDOR_ID IN :vendorIds AND spd.STATUS = :status"
				+ " AND spd.DELIVERY_LOCATION=:deliveryLocation");

			query.setParameter("status", SwitchStatus.ACTIVE.name())
				.setParameter("unitId", unitId)
				.setParameter("vendorIds", vendorIds)
				.setParameter("deliveryLocation",deliveryLocation);
			List<Object[]> list = query.getResultList();

			//usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION, spd.DELIVERY_LOCATION, spd.SKU_PRICE
			return createSkuPriceDetailList(list);
		} catch (Exception e) {
			LOG.error("Encountered error while getting prices for vendor and unit", e);
		}
		return null;
	}

	@Override
	public List<SkuPriceDetail> getSkuPackagingPriceForUnit(int unitId, String deliveryLocation) {
		try {
			Query query = manager.createNativeQuery("SELECT usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION,"
					+ " spd.DELIVERY_LOCATION, spd.SKU_PRICE, vsm.SKU_ALIAS, spd.LEAD_TIME,spd.SKU_PRICE_DATA_ID FROM  UNIT_SKU_MAPPING usm "
					+ " INNER JOIN VENDOR_SKU_MAPPING vsm ON usm.SKU_ID = vsm.SKU_ID"
					+ " INNER JOIN SKU_PRICE_DATA spd ON spd.SKU_ID = usm.SKU_ID "
					+ " INNER JOIN UNIT_SKU_VENDOR_MAPPING usvm ON usvm.UNIT_SKU_MAPPING_ID = usm.UNIT_SKU_MAPPING_ID "
					+ " AND usvm.VENDOR_ID = vsm.VENDOR_ID WHERE usm.UNIT_ID = :unitId "
					+ " AND usm.MAPPING_STATUS = :status AND vsm.MAPPING_STATUS = :status "
					+ " AND spd.STATUS = :status AND spd.DELIVERY_LOCATION=:deliveryLocation");

			query.setParameter("status", SwitchStatus.ACTIVE.name())
					.setParameter("unitId", unitId)
					.setParameter("deliveryLocation",deliveryLocation);
			List<Object[]> list = query.getResultList();

			//usm.SKU_ID, spd.SKU_PACKAGING_ID, spd.VENDOR_ID, spd.DISPATCH_LOCATION, spd.DELIVERY_LOCATION, spd.SKU_PRICE
			return createSkuPriceDetailList(list);
		} catch (Exception e) {
			LOG.error("Encountered error while getting prices for vendor and unit", e);
		}
		return null;
	}
	@Override
	public List<unitSkuMappingDetail> getSkusProfileForUnit(int unitId, List<Integer> sku) {
		try {
			List<unitSkuMappingDetail> skuList = new ArrayList<>();
			Query query = manager.createNativeQuery("SELECT usm.UNIT_SKU_MAPPING_ID, usm.SKU_ID, usm.UNIT_ID, usm.MAPPING_STATUS, usm.PROFILE FROM  UNIT_SKU_MAPPING usm "
					+ "where usm.UNIT_ID =:unitId and usm.SKU_Id IN :skuId and usm.MAPPING_STATUS =:mappingStatus");

			query.setParameter("mappingStatus", SwitchStatus.ACTIVE.name())
					.setParameter("unitId", unitId)
					.setParameter("skuId", sku);
			List<Object[]> list = query.getResultList();
			    list.stream().forEach((record) -> {
				unitSkuMappingDetail o = new unitSkuMappingDetail();
				o.setUnitSkuMappingId((Integer) record[0]);
				o.setSkuId((Integer) record[1]);
				o.setUnitId((Integer) record[2]);
				o.setMappingStatus((String) record[3]);
				o.setProfile((String) record[4]);
				skuList.add(o);
			});
			return skuList;
		} catch (Exception e) {
			LOG.error("Encountered error while getting profiles for vendor and unit", e);
		}
		return null;
	}

	private List<SkuPriceDetail> createSkuPriceDetailList(List<Object[]> list) throws TransferOrderCreationException {
		if (list != null && !list.isEmpty()) {
            List<SkuPriceDetail> skuPriceDetailList = new ArrayList<>();
            for(Object[] price : list){
                try{
                    SkuPriceDetail updatedPrice = new SkuPriceDetail();
                    updatedPrice.setDelivery(SCMDataConverter.convertToIdCodeName(masterDataCache.getAllLocations().get(price[4].toString())));
                    updatedPrice.setDispatch(SCMDataConverter.convertToIdCodeName(masterDataCache.getAllLocations().get(price[3].toString())));
                    updatedPrice.setPkg(SCMDataConverter.convertToPackagingData(cache.getPackagingDefinition((Integer) price[1])));
                    updatedPrice.setSku(SCMDataConverter.convertToIdCodeName(cache.getSkuDefinition((Integer) price[0])));
                    updatedPrice.setVendor(SCMDataConverter.convertToIdCodeName(cache.getVendorDetail((Integer) price[2])));
                    updatedPrice.setStatus(SwitchStatus.ACTIVE.name());
                    updatedPrice.setLeadTime((Integer) price[7]);
                    updatedPrice.getCurrent().setDate(SCMUtil.getCurrentTimestamp());
                    updatedPrice.getCurrent().setValue((BigDecimal) price[5]);
                    if(price[6] != null) {
						updatedPrice.getSku().setCode(price[6].toString());
					}
					if (getSkuPriceHistoryForSku((Integer) price[8])){
						skuPriceDetailList.add(updatedPrice);
					}
                }catch(Exception e){
                    String message = "Error while finding updated price for vendor sku price " + JSONSerializer.toJSON(price);
                    LOG.error(message, e);
                    throw new TransferOrderCreationException(new SCMError("Error while fetching price", message, 701));
                }
            }
            return skuPriceDetailList;
		}
		return null;
	}

	private boolean getSkuPriceHistoryForSku(Integer sku) {
		Query query = manager.createQuery("from SkuPriceHistory where skuPriceDataId=:skuPriceDataId and recordStatus not in (:recordStatus) " +
				"and changeType=:changeType order by 1 desc");
		query.setParameter("skuPriceDataId",sku);
		query.setParameter("recordStatus",List.of(PriceStatus.CREATED.name(),PriceStatus.PENDING.name(),PriceStatus.REJECTED.name(),
				PriceStatus.APPROVED.name(),
				PriceStatus.CANCELLED.name()));
		query.setParameter("changeType",ChangeType.PRICE_UPDATE.name());
		query.setMaxResults(1);
		if (!query.getResultList().isEmpty()) {
			SkuPriceHistory skuPriceHistory = (SkuPriceHistory) query.getResultList().get(0);
			return PriceStatus.APPLIED.name().equalsIgnoreCase(skuPriceHistory.getRecordStatus()) ||
			PriceStatus.REACTIVATION_REQUESTED.name().equalsIgnoreCase(skuPriceHistory.getRecordStatus()) ||
			PriceStatus.REACTIVATION_ACCEPTED.name().equalsIgnoreCase(skuPriceHistory.getRecordStatus()) ||
			PriceStatus.DEACTIVATION_ACCEPTED.name().equalsIgnoreCase(skuPriceHistory.getRecordStatus()) ||
			PriceStatus.DEACTIVATION_REQUESTED.name().equalsIgnoreCase(skuPriceHistory.getRecordStatus());
		}
		return false;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.SkuMappingDao#
	 * updateSkuMappingsForVendorAndUnit(com.stpl.tech.scm.domain.model.
	 * UpdateUnitVendorSkuMapping)
	 */
	@Override
	public List<UnitVendorSkuMapping> updateSkuMappingsForVendorAndUnit(UpdateUnitVendorSkuMapping data) {
		UnitSkuMapping unitMapping = null;
		if (data.getMapping().getKeyId() == null) {
			// create new active mapping
			UnitSkuVendorMapping mapping = new UnitSkuVendorMapping();
			mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
			mapping.setCreatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
			mapping.setMappingStatus(AppConstants.ACTIVE);
			mapping.setVendorId(data.getMapping().getVendor().getId());
			unitMapping = manager.find(UnitSkuMapping.class, data.getMapping().getUnitSkuMappingId());
			mapping.setUnitSkuMapping(unitMapping);
			manager.persist(mapping);
		} else {
			UnitSkuVendorMapping mapping = manager.find(UnitSkuVendorMapping.class, data.getMapping().getKeyId());
			mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
			mapping.setUpdatedBy(getName(data.getEmployeeName(), data.getEmployeeId()));
			mapping.setMappingStatus(data.getMapping().getStatus().name());
			unitMapping = mapping.getUnitSkuMapping();
		}
		manager.flush();
		return searchSkuMappingsForVendorAndUnit(unitMapping.getUnitId(), data.getMapping().getVendor().getId());
	}

	@Override
	public List<PurchaseProfile> getPurchaseMappings(List<Integer> profiles) {
		Query query = manager.createQuery("FROM PurchaseProfile E WHERE E.roleId IN (:roles)");
		query.setParameter("roles",profiles);
		return query.getResultList();
	}

	@Override
	public boolean updateSkuLeadTime(int vendorId, int leadTime) {
		Query query = manager.createQuery(
				"update SkuPriceData SPD set SPD.leadTime =:leadTime  where  SPD.vendorId =:vendorId");
		query.setParameter("leadTime",leadTime);
		query.setParameter("vendorId",vendorId);
		query.executeUpdate();
		return true;
	}

	@Override
	public List<String> getDistanceOfUnits(int firstUnitId, int secondUnitId) {
		List<String> distances = new ArrayList<String>();
		int temp;
		for(int i = 0 ; i < 2 ; i++) {
			if( i == 1) {
				temp = firstUnitId;
				firstUnitId = secondUnitId;
				secondUnitId = temp;
			}
			try {
			Query query = manager.createQuery("SELECT E FROM UnitDistanceMappingData E where E.sourceUnitId=:firstUnitId AND E.destinationUnitId=:secondUnitId");
			query.setParameter("firstUnitId", firstUnitId).setParameter("secondUnitId", secondUnitId);
			 UnitDistanceMappingData data = (UnitDistanceMappingData) query.getSingleResult();
			 distances.add(data.getDistance().toString() +"-"+ data.getMappingId());
			}catch (Exception e) {
				distances.add(0 +"-"+ null);
			}
		}
		return distances;
	}

	@Override
	public boolean updateUnitDistanceMappingData(int firstUnitId, Integer firstMappingId, BigDecimal firstDistance,
												 int secondUnitId, Integer secondMappingId, BigDecimal secondDistance, boolean saveZipDis) throws SumoException {
		BigDecimal check = new BigDecimal(0);
		UnitDistanceMappingData data = new UnitDistanceMappingData();
		if(saveZipDis) {
			saveZipCodeDistance(firstUnitId, secondUnitId, firstDistance, secondDistance);
		}
		if(firstMappingId != null) {
			data.setMappingId(firstMappingId);
			data.setSourceUnitId(firstUnitId);
			data.setDestinationUnitId(secondUnitId);
			data.setDistance(firstDistance.equals(check) ?secondDistance : firstDistance);
			manager.merge(data);
			manager.flush();
		}else {
			data.setSourceUnitId(firstUnitId);
			data.setDestinationUnitId(secondUnitId);
			data.setDistance(firstDistance.equals(check) ?secondDistance : firstDistance);
			manager.persist(data);
		}
		UnitDistanceMappingData unitData = new UnitDistanceMappingData();
		if(secondMappingId != null) {
			unitData.setMappingId(secondMappingId);
			unitData.setSourceUnitId(secondUnitId);
			unitData.setDestinationUnitId(firstUnitId);
			unitData.setDistance(secondDistance.equals(check) ?firstDistance : secondDistance);
			manager.merge(unitData);
			manager.flush();
		} else {
			unitData.setSourceUnitId(secondUnitId);
			unitData.setDestinationUnitId(firstUnitId);
			unitData.setDistance(secondDistance.equals(check) ?firstDistance : secondDistance);
			manager.persist(unitData);
		}
		manager.flush();
		cache.refreshUnitDistanceMapping();
		return true;
	}

	@Override
	public void saveZipCodeDistance(int firstUnitId, int secondUnitId, BigDecimal firstDistance, BigDecimal secondDistance) throws SumoException {

		BigDecimal check = BigDecimal.ZERO;
		if(firstDistance.equals(check) && secondDistance.equals(check)){
			throw  new SumoException("for unit first distance and second distance both are zero");
		}
		String srcZipCode = masterDataCache.getUnit(firstUnitId).getAddress().getZipCode();
		String desZipCode = masterDataCache.getUnit(secondUnitId).getAddress().getZipCode();
		ZipCodeDistanceMapping res = findDistanceByZipCode(srcZipCode,desZipCode);
		BigDecimal dis = firstDistance.equals(check) ? secondDistance : firstDistance;

		if(res==null){
			ZipCodeDistanceMapping zipCodeDistanceMapping = new ZipCodeDistanceMapping();
			zipCodeDistanceMapping.setSourceZipCode(srcZipCode);
			zipCodeDistanceMapping.setDestinationZipCode(desZipCode);
			zipCodeDistanceMapping.setDistance(dis);
			manager.persist(zipCodeDistanceMapping);
		}else if (!res.getDistance().equals(dis)){
			res.setDistance(dis);
			manager.persist(res);
		}
		manager.flush();
		cache.refreshZipCodeDistanceMapping();
	}
	private ZipCodeDistanceMapping findDistanceByZipCode(String sourceZipCode, String destinationZipCode){
		Query query = manager.createQuery("FROM ZipCodeDistanceMapping as z where sourceZipCode=:srcZipCode and destinationZipCode=:desZipCode");
		query.setParameter("srcZipCode",sourceZipCode);
		query.setParameter("desZipCode",destinationZipCode);
		ZipCodeDistanceMapping res=null;
		try{
		 res	= (ZipCodeDistanceMapping)query.getSingleResult();
		}catch (NoResultException ignored){}
		if(res==null){
			query.setParameter("srcZipCode",destinationZipCode);
			query.setParameter("desZipCode",sourceZipCode);
			try{
				res	= (ZipCodeDistanceMapping)query.getSingleResult();
			}catch (NoResultException ignored){}
		}
		return res;
	}


	@Override
	public int getVendorSkuMappingId(int skuId,int vendorId){
		Query query = manager.createQuery("SELECT vendorSkuMappingId FROM VendorSkuMapping WHERE skuId=:skuId and vendorId=:vendorId  ");
		query.setParameter("skuId",skuId);
		query.setParameter("vendorId",vendorId);
		int vendorSkuMappingId=(Integer) query.getSingleResult();
		System.out.println("in dao impl"+vendorSkuMappingId);
		return vendorSkuMappingId;
	}

	@Override
	public ProductionUnitData findProductionLine(int productIonUnitId) {
		return manager.find(ProductionUnitData.class, productIonUnitId);
	}

	@Override
	public Integer findProductionLine(int unitId, int skuId) {
		Query query = manager.createQuery("SELECT usm.productionUnit FROM UnitSkuMapping usm WHERE usm.unitId=:unitId and usm.skuId=:skuId");
		query.setParameter("unitId", unitId);
		query.setParameter("skuId", skuId);
		try{
			Integer productionLine = (Integer) query.getSingleResult();
			LOG.info("production line is  {}", productionLine);
			if (productionLine == null) {
				productionLine = -1;
			}
			return productionLine;
		}catch(NoResultException e){
			LOG.info("No production Line ");
			return -1;
		}catch (NonUniqueResultException e){
			return (Integer) query.getResultList().get(0);
		}
	}

	@Override
	public int findSKUID(int productId) {
		Query query = manager.createQuery(
				"SELECT sdd.skuId FROM SkuDefinitionData sdd WHERE sdd.linkedProduct.productId=:productId and sdd.skuStatus = :skuStatus");
		query.setParameter("productId", productId);
		query.setParameter("skuStatus", ProductStatus.ACTIVE.name());
		try{
			return (int) query.getSingleResult();
		}catch (NonUniqueResultException e){
			return (int) query.getResultList().get(0);
		}
		//return (int) query.getSingleResult();
	}

	@Override
	public List<SkuDefinitionData> findAllSkuDefinition(int productId) {
		Query query=manager.createQuery("FROM SkuDefinitionData sdd  where sdd.linkedProduct.productId=:productId and sdd.skuStatus=:status ");
		query.setParameter("productId",productId);
		query.setParameter("status", AppConstants.ACTIVE);
		return (List<SkuDefinitionData>) query.getResultList();
	}

	@Override
	public UnitSkuMapping findSkuMappingBySkuAndUnit(Integer skuId,Integer unitId){
		Query query=manager.createQuery("FROM UnitSkuMapping usm  where usm.skuId = :skuId and usm.unitId = :unitId and usm.mappingStatus = :status ");
		query.setParameter("skuId",skuId).setParameter("unitId",unitId);
		query.setParameter("status", AppConstants.ACTIVE);
		return (UnitSkuMapping) query.getSingleResult();
	}

	@Override
	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMappingByStatus(Integer skuId,Integer packagingId,List<String> statuses){
		Query query=manager.createQuery("FROM SkuPackagingTaxMapping sptm  where sptm.skuId = :skuId and sptm.packagingId = :packagingId and sptm.mappingStatus in :statuses ");
		query.setParameter("skuId",skuId).setParameter("packagingId",packagingId).setParameter("statuses", statuses);
		List<SkuPackagingTaxMapping> resultList = query.getResultList();
		return Objects.nonNull(resultList) ? resultList : new ArrayList<>();
	}

	@Override
	public List<SkuPackagingTaxMapping> findAllUnitSkuPackagingTaxMappingByUnit(List<Integer> skuIds,Integer unitId){
		Query query=manager.createQuery("FROM SkuPackagingTaxMapping sptm  where sptm.skuId in :skuIds and sptm.unitId = :unitId and sptm.mappingStatus = :status ");
		query.setParameter("skuIds",skuIds).setParameter("unitId", unitId).setParameter("status",AppConstants.ACTIVE);
		List<SkuPackagingTaxMapping> resultList = query.getResultList();
		return Objects.nonNull(resultList) ? resultList : new ArrayList<>();
	}

	private Boolean activateUnitSkuPackagingTaxMapping(Integer employeeId, Integer skuId,Integer packagingId, List<Integer> unitIds) {
		if (unitIds.isEmpty()) {
			return true;
		}
		Query query = manager.createQuery(
				"update SkuPackagingTaxMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " unitId IN ( :unitIds ) and skuId = :skuId and packagingId = :packagingId and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", employeeId);
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("packagingId",packagingId);
		query.setParameter("skuId", skuId);
		query.setParameter("unitIds", unitIds);
		query.executeUpdate();
		return true;
	}

	private boolean deactivateUnitSkuPackagingTaxMapping(Integer employeeId, Integer skuId, Integer packagingId, List<Integer> unitIds) {
		Query query = null;
		if (unitIds.isEmpty()) {
			query = manager.createQuery(
					"update SkuPackagingTaxMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ "  skuId = :skuId  and packagingId = :packagingId  and mappingStatus = :mappingStatus");
		} else {
			query = manager.createQuery(
					"update SkuPackagingTaxMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
							+ " unitId NOT IN ( :unitIds ) and skuId = :skuId and packagingId = :packagingId and mappingStatus = :mappingStatus");
			query.setParameter("unitIds", unitIds);
		}
		query.setParameter("status", AppConstants.IN_ACTIVE);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("updatedBy", employeeId);
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("skuId", skuId);
		query.setParameter("packagingId", packagingId);
		query.executeUpdate();
		return true;
	}

	private Set<Integer> getAllUnitsForSku_PackagingId(Integer skuId, Integer packagingId) {

		Set<Integer> units = new HashSet<>();
		Query query = manager.createQuery("select unitId from SkuPackagingTaxMapping where skuId = :skuId and packagingId = :packagingId");
		query.setParameter("skuId", skuId).setParameter("packagingId",packagingId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			units.addAll(list);
		}
		return units;
	}

	@Override
	public Boolean updateUnitSkuPackagingTaxMapping(Integer employeeId, Integer skuId,Integer packagingId  , Map<Integer,String> unitTaxMap) {
		List<Integer> unitIds = new ArrayList<Integer>(unitTaxMap.keySet());
		activateUnitSkuPackagingTaxMapping(employeeId,skuId,packagingId,unitIds);
		deactivateUnitSkuPackagingTaxMapping(employeeId,skuId,packagingId,unitIds);
		Set<Integer> units = getAllUnitsForSku_PackagingId(skuId,packagingId);
		List<SkuPackagingTaxMapping> newMappings = new ArrayList<>();
		for (Integer unitId : unitIds) {
			if (!units.contains(unitId)) {
				SkuPackagingTaxMapping mapping= new SkuPackagingTaxMapping();
				mapping.setUnitId(unitId);
				mapping.setSkuId(skuId);
				mapping.setPackagingId(packagingId);
				mapping.setMappingStatus(AppConstants.ACTIVE);
				mapping.setCreatedAt(AppUtils.getCurrentTimestamp());
				mapping.setCreatedBy(employeeId);
				mapping.setTaxCode(unitTaxMap.get(unitId));

				newMappings.add(mapping);
			}
		}
		if(!newMappings.isEmpty()){
			addAll(newMappings);
		}
		manager.flush();

		return true;
	}


	@Override
	public List<SkuPriceDetail> vendorPriceChangeAsPerStatus(Integer vendorId,List<String> status) {
		Query query = manager.createQuery("FROM SkuPriceData where isPriceChangeRequested = :isPriceChangeRequested and vendorId =:vendorId");
		query.setParameter("isPriceChangeRequested",AppConstants.YES);
		query.setParameter("vendorId",vendorId);
		List<SkuPriceData> data = query.getResultList();
		Map<Integer, SkuPriceData> vendorSkuId = data.stream()
				.collect(Collectors.toMap(SkuPriceData::getSkuPriceKeyId, sku -> sku));
		Query query1 = manager.createQuery("FROM SkuPriceHistory where skuPriceDataId in (:vendorSkuId) and recordStatus in (:recordStatus)");
		query1.setParameter("vendorSkuId",vendorSkuId.keySet());
		query1.setParameter("recordStatus",status);
		List<SkuPriceHistory> skuPriceHistories = query1.getResultList();
		List<SkuPriceData> resultant = new ArrayList<>();
		skuPriceHistories.forEach(price -> {
			SkuPriceData skuPriceData = vendorSkuId.get(price.getSkuPriceDataId());
			skuPriceData.getUpdateList().add(price);
			resultant.add(skuPriceData);
		});
		return convert(resultant, true, false);
	}

	@Override
	public List<SkuPriceHistory> saveVendorPriceChange(VendorContract vendorContract) {
		List<SkuPriceData> data = getSkuPriceData(VendorContractInfo.builder().vendorId(vendorContract.getVendorId()).build());
		Map<Integer, SkuPriceData> vendorSkuId = data.stream()
				.collect(Collectors.toMap(SkuPriceData::getSkuPriceKeyId, sku -> sku));
		List<SkuPriceHistory> history = getSkuPriceHistories(vendorSkuId,
				List.of(PriceStatus.PENDING.name(),PriceStatus.REACTIVATION_REQUESTED.name(),PriceStatus.DEACTIVATION_REQUESTED.name()));
		if (history != null) {
			for (SkuPriceHistory p : history) {
				p.setUpdatedAt(AppUtils.getCurrentTimestamp());
				p.setUpdatedBy(getName(vendorContract.getEmployeeName(), vendorContract.getEmployeeId()));
				if (PriceStatus.DEACTIVATION_REQUESTED.name().equalsIgnoreCase(p.getRecordStatus())
						&& PriceStatus.APPROVED.equals(vendorContract.getStatus())){
					p.setRecordStatus(PriceStatus.DEACTIVATION_ACCEPTED.name());
				}else if (PriceStatus.REACTIVATION_REQUESTED.name().equalsIgnoreCase(p.getRecordStatus())
						&& PriceStatus.APPROVED.equals(vendorContract.getStatus())){
					p.setRecordStatus(PriceStatus.REACTIVATION_ACCEPTED.name());
				}else if (PriceStatus.DEACTIVATION_REQUESTED.name().equalsIgnoreCase(p.getRecordStatus())
						&& PriceStatus.REJECTED.equals(vendorContract.getStatus())){
					p.setRecordStatus(PriceStatus.APPLIED.name());
				}else if (PriceStatus.REACTIVATION_REQUESTED.name().equalsIgnoreCase(p.getRecordStatus())
						&& PriceStatus.REJECTED.equals(vendorContract.getStatus())){
					p.setRecordStatus(PriceStatus.APPLIED.name());
				} else {
					p.setRecordStatus(vendorContract.getStatus().name());
				}
				p.setChangeType(ChangeType.PRICE_UPDATE.name());
			}
		}
		manager.flush();
		return history;
	}

	private List<SkuPriceHistory> getSkuPriceHistories(Map<Integer, SkuPriceData> vendorSkuId,List<String> status) {
		Query query = manager.createQuery("FROM SkuPriceHistory where skuPriceDataId in (:vendorSkuId) and recordStatus in (:recordStatus)");
		query.setParameter("vendorSkuId", vendorSkuId.keySet());
		query.setParameter("recordStatus", status);
		List<SkuPriceHistory> history = query.getResultList();
		return history;
	}

	private List<SkuPriceData> getSkuPriceData(VendorContractInfo vendorContract) {
		Query skuPriceQuery = manager.createQuery("FROM SkuPriceData where isPriceChangeRequested = :isPriceChangeRequested and vendorId =:vendorId");
		skuPriceQuery.setParameter("isPriceChangeRequested",AppConstants.YES);
		skuPriceQuery.setParameter("vendorId", vendorContract.getVendorId());
		List<SkuPriceData> data = skuPriceQuery.getResultList();
		return data;
	}

	@Override
	public List<VendorContractVO> getVendorContract(Integer vendorId, PriceStatus status, Date startDate, Date endDate, Integer vendorContractId) {
		StringBuilder builder = new StringBuilder("From VendorContractInfo where recordStatus=:recordStatus");
		if (Objects.nonNull(vendorId)) {
			builder.append(" and vendorId =:vendorId ");
		}
		if (Objects.nonNull(startDate)) {
			builder.append(" and startDate >=:startDate ");
		}
		if (Objects.nonNull(endDate)) {
			builder.append(" and startDate <=:endDate ");
		}
		if (Objects.nonNull(vendorContractId)) {
			builder.append(" and vendorContractId =:vendorContractId ");
		}
		Query query = manager.createQuery(builder.toString());
		query.setParameter("recordStatus",status.name());
		if (Objects.nonNull(vendorId)) {
			query.setParameter("vendorId",vendorId);
		}
		if (Objects.nonNull(startDate)) {
			query.setParameter("startDate",startDate);
		}
		if (Objects.nonNull(endDate)) {
			query.setParameter("endDate",endDate);
		}
		if (Objects.nonNull(vendorContractId)) {
			query.setParameter("vendorContractId",vendorContractId);
		}
		List<VendorContractInfo> vendorContractInfos = query.getResultList();
		List<VendorContractVO> vendorContractVOS = new ArrayList<>();
		vendorContractInfos.forEach(vendorContractInfo -> {
			List<VendorContractItem> vendorContractItems = getVendorContractItems(vendorContractInfo);
			VendorContractVO vendorContractVO = VendorContractMapper.INSTANCE.toDomain(vendorContractInfo);

			vendorContractVO.setVendorName(scmCache.getVendorDetail(vendorContractVO.getVendorId()).getEntityName());
			vendorContractItems.forEach(item -> {
				vendorContractVO.getVendorContractItemVOS().add(SCMDataConverter.convert(cache, masterDataCache, item));
			});
			vendorContractVOS.add(vendorContractVO);
		});
		return vendorContractVOS;
	}

	private List<VendorContractItem> getVendorContractItems(VendorContractInfo vendorContractInfo) {
		Query contractItemQuery = manager.createQuery("From VendorContractItem where vendorContractId =:vendorContractId");
		contractItemQuery.setParameter("vendorContractId", vendorContractInfo.getVendorContractId());
		List<VendorContractItem> vendorContractItems = contractItemQuery.getResultList();
		return vendorContractItems;
	}

	@Override
	public boolean cancelVendorContract(VendorContract vendorContractId) throws SumoException {
		VendorContractInfo vendorContractInfo = manager.find(VendorContractInfo.class,vendorContractId.getVendorContractId());
		if (Objects.isNull(vendorContractInfo)) {
			throw new SumoException("No Contract Present with Contract Id  :: " + vendorContractId.getVendorContractId());
		}
		if (PriceStatus.APPLIED.name().equalsIgnoreCase(vendorContractInfo.getRecordStatus())) {
			throw new SumoException("Contract Already Applied");
		}
		vendorContractInfo.setRecordStatus(PriceStatus.CANCELLED.name());
		vendorContractId.setVendorId(vendorContractInfo.getVendorId());
		List<SkuPriceData> data = getSkuPriceData(vendorContractInfo);
		cancelAllContractMailRequest(vendorContractId.getVendorContractId(),"VENDOR_CONTRACT");
		cancelAllContractMailRequest(vendorContractId.getVendorContractId(),"VENDOR_CONTRACT_EMPLOYEE");
		Map<Integer, SkuPriceData> vendorSkuId = data.stream()
				.collect(Collectors.toMap(SkuPriceData::getSkuPriceKeyId, sku -> sku));
		List<SkuPriceHistory> history = getSkuPriceHistories(vendorSkuId,List.of(PriceStatus.APPROVED.name(),
				PriceStatus.REACTIVATION_ACCEPTED.name(),PriceStatus.DEACTIVATION_ACCEPTED.name()));
		history.forEach( val -> {
			if (PriceStatus.REACTIVATION_ACCEPTED.name().equalsIgnoreCase(val.getRecordStatus())) {
				val.setRecordStatus(PriceStatus.APPLIED.name());
			} else if (PriceStatus.DEACTIVATION_ACCEPTED.name().equalsIgnoreCase(val.getRecordStatus())) {
				val.setRecordStatus(PriceStatus.APPLIED.name());
			} else {
				val.setRecordStatus(PriceStatus.CANCELLED.name());
			}
		});
		manager.flush();
		if (!history.isEmpty()) {
			LOG.info("Item Processed {}",history.size());
		}
		return true;
	}

	@Override
	public void applyContractOnSKU(VendorContract vendorContract, IdCodeName templateDetail) throws SumoException {
		VendorContractInfo vendorContractInfo = manager.find(VendorContractInfo.class,vendorContract.getVendorContractId());
		if (Objects.isNull(vendorContractInfo)) {
			throw new SumoException("No Contract Present with Contract Id  :: " + vendorContract.getVendorContractId());
		}
		if (Objects.isNull(vendorContractInfo.getEndDate())) {
			vendorContractInfo.setEndDate(AppUtils.addDays(vendorContractInfo.getStartDate(), Integer.parseInt(templateDetail.getName())));
		}
		List<SkuPriceData> data = getSkuPriceData(vendorContractInfo);
		Map<Integer, SkuPriceData> vendorSkuId = data.stream()
				.collect(Collectors.toMap(SkuPriceData::getSkuPriceKeyId, sku -> sku));
		List<SkuPriceHistory> history = getSkuPriceHistories(vendorSkuId,List.of(PriceStatus.APPROVED.name(),
				PriceStatus.REACTIVATION_ACCEPTED.name(),PriceStatus.DEACTIVATION_ACCEPTED.name()));
		history.forEach(item -> {
			if (PriceStatus.APPLIED.equals(vendorContract.getStatus()) &&
					PriceStatus.APPROVED.name().equalsIgnoreCase(item.getRecordStatus())) {
				item.setUpdatedAt(AppUtils.getCurrentTimestamp());
				item.setUpdatedBy(getName(vendorContract.getEmployeeName(), vendorContract.getEmployeeId()));
				item.setRecordStatus(vendorContract.getStatus().name());
				item.setChangeType(ChangeType.PRICE_UPDATE.name());
				item.setStartDate(vendorContractInfo.getStartDate());
				item.setEndDate(vendorContractInfo.getEndDate());
				item.setContractId(vendorContractInfo.getVendorContractId());
				SkuPriceData skuPriceData = manager.find(SkuPriceData.class, item.getSkuPriceDataId());
				skuPriceData.setStartDate(item.getStartDate());
				skuPriceData.setStatus(AppConstants.ACTIVE);
				skuPriceData.setEndDate(vendorContractInfo.getEndDate());
				skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
				skuPriceData.setPrice(item.getNegotiatedPrice());
			} else if (PriceStatus.APPLIED.equals(vendorContract.getStatus()) &&
					(PriceStatus.DEACTIVATION_ACCEPTED.name().equalsIgnoreCase(item.getRecordStatus()))) {
				item.setUpdatedAt(AppUtils.getCurrentTimestamp());
				item.setUpdatedBy(getName(vendorContract.getEmployeeName(), vendorContract.getEmployeeId()));
				item.setRecordStatus(PriceStatus.DEACTIVATED.name());
				item.setChangeType(ChangeType.PRICE_UPDATE.name());
				item.setStartDate(vendorContractInfo.getStartDate());
				item.setEndDate(vendorContractInfo.getEndDate());
				item.setContractId(vendorContractInfo.getVendorContractId());
				SkuPriceData skuPriceData = manager.find(SkuPriceData.class, item.getSkuPriceDataId());
				skuPriceData.setStartDate(vendorContractInfo.getStartDate());
				skuPriceData.setEndDate(vendorContractInfo.getEndDate());
				skuPriceData.setStatus(AppConstants.IN_ACTIVE);
				skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
				skuPriceData.setPrice(item.getNegotiatedPrice());
			} else if (PriceStatus.APPLIED.equals(vendorContract.getStatus()) &&
					(PriceStatus.REACTIVATION_ACCEPTED.name().equalsIgnoreCase(item.getRecordStatus()))) {
				item.setRecordStatus(PriceStatus.DEACTIVATED.name());
				SkuPriceHistory skuPriceHistory = new SkuPriceHistory();
				skuPriceHistory.setCreatedAt(AppUtils.getCurrentTimestamp());
				skuPriceHistory.setCreatedBy(getName(vendorContract.getEmployeeName(), vendorContract.getEmployeeId()));
				skuPriceHistory.setRecordStatus(PriceStatus.APPLIED.name());
				skuPriceHistory.setChangeType(ChangeType.PRICE_UPDATE.name());
				skuPriceHistory.setStartDate(vendorContractInfo.getStartDate());
				skuPriceHistory.setEndDate(vendorContractInfo.getEndDate());
				skuPriceHistory.setCurrentPrice(item.getCurrentPrice());
				skuPriceHistory.setNegotiatedPrice(item.getNegotiatedPrice());
				skuPriceHistory.setContractId(vendorContractInfo.getVendorContractId());
				skuPriceHistory.setSkuPriceDataId(item.getSkuPriceDataId());
				manager.persist(skuPriceHistory);
				SkuPriceData skuPriceData = manager.find(SkuPriceData.class, skuPriceHistory.getSkuPriceDataId());
				skuPriceData.setStartDate(vendorContractInfo.getStartDate());
				skuPriceData.setEndDate(vendorContractInfo.getEndDate());
				skuPriceData.setStatus(AppConstants.ACTIVE);
				skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
				skuPriceData.setPrice(skuPriceHistory.getNegotiatedPrice());
			} else if (PriceStatus.REJECTED.equals(vendorContract.getStatus()) &&
					PriceStatus.APPROVED.name().equalsIgnoreCase(item.getRecordStatus())) {
				item.setUpdatedAt(AppUtils.getCurrentTimestamp());
				item.setUpdatedBy(getName(vendorContract.getEmployeeName(), vendorContract.getEmployeeId()));
				item.setRecordStatus(PriceStatus.REJECTED.name());
				item.setChangeType(ChangeType.PRICE_UPDATE.name());
				item.setContractId(vendorContractInfo.getVendorContractId());
				SkuPriceData skuPriceData = manager.find(SkuPriceData.class, item.getSkuPriceDataId());
				skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
			} else if (PriceStatus.REJECTED.equals(vendorContract.getStatus()) &&
					PriceStatus.REACTIVATION_ACCEPTED.name().equalsIgnoreCase(item.getRecordStatus()) ||
					PriceStatus.DEACTIVATION_ACCEPTED.name().equalsIgnoreCase(item.getRecordStatus())) {
				item.setUpdatedAt(AppUtils.getCurrentTimestamp());
				item.setUpdatedBy(getName(vendorContract.getEmployeeName(), vendorContract.getEmployeeId()));
				item.setRecordStatus(PriceStatus.APPLIED.name());
				SkuPriceData skuPriceData = manager.find(SkuPriceData.class, item.getSkuPriceDataId());
				skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
			} else if (PriceStatus.EXPIRED.equals(vendorContract.getStatus())) {
				item.setRecordStatus(PriceStatus.EXPIRED.name());
				SkuPriceData skuPriceData = manager.find(SkuPriceData.class, item.getSkuPriceDataId());
				skuPriceData.setStatus(AppConstants.IN_ACTIVE);
				skuPriceData.setIsPriceChangeRequested(AppConstants.NO);
			}
		});
		manager.flush();
	}

	@Override
	public void applyContract(){
		Query query = manager.createQuery("From VendorContractInfo where startDate = :startDate and recordStatus =:recordStatus");
		query.setParameter("recordStatus",PriceStatus.APPLIED.name());
		query.setParameter("startDate",AppUtils.getBusinessDate());
		List<VendorContractInfo> vendorContractInfos = query.getResultList();
		if (!vendorContractInfos.isEmpty()){
			vendorContractInfos.forEach(val -> {
				try {
					IdCodeName templateDetail = scmConstants.getVendorContractTemplate().get(val.getTemplateId());
					applyContractOnSKU(VendorContract.builder().vendorContractId(val.getVendorContractId()).status(PriceStatus.APPLIED).build(), templateDetail);
				} catch (SumoException e) {
					LOG.error("Exception in");
				}
			});
		}
	}

	@Override
	public void expiryContract(){
		Query query = manager.createQuery("From VendorContractInfo where endDate = :startDate and recordStatus =:recordStatus");
		query.setParameter("recordStatus",PriceStatus.APPLIED.name());
		query.setParameter("endDate",AppUtils.getBusinessDate());
		List<VendorContractInfo> vendorContractInfos = query.getResultList();
		if (!vendorContractInfos.isEmpty()){
			vendorContractInfos.forEach(val -> {
				try {
					IdCodeName templateDetail = scmConstants.getVendorContractTemplate().get(val.getTemplateId());
					applyContractOnSKU(VendorContract.builder().vendorContractId(val.getVendorContractId()).status(PriceStatus.EXPIRED).build(), templateDetail);
				} catch (SumoException e) {
					LOG.error("Exception in");
				}
			});
		}
	}

	@Override
	public List<VendorContractItem> findAllVendorContract(Integer vendorContractId) {
		Query query = manager.createQuery("From VendorContractItem where vendorContractId=:vendorContractId");
		query.setParameter("vendorContractId",vendorContractId);
		return query.getResultList();
	}

	@Override
	public PageRequestDetail findByToken(String token) throws VendorRegistrationException {
		try {
			Query query = manager.createQuery("FROM PageRequestDetail E"
					+ " WHERE E.authKey = :authKey order by E.requestDate desc");
			query.setParameter("authKey", token);
			PageRequestDetail result = (PageRequestDetail) query.getSingleResult();
			if (result != null) {
				if (VendorStatus.INITIATED.equals(VendorStatus.valueOf(result.getRecordStatus()))) {
					return result;
				} else {
					throw new VendorRegistrationException(
							"Not a valid request. Please contact Chaayos to resolve conflicts.");
				}
			}
		} catch (Exception e) {
			LOG.error("Exception occurred while fetching request", e);
			throw new VendorRegistrationException("Could not find request", e);
		}
		return null;
	}

	@Override
	public VendorContractVO getVendorContractVoByContractId(Integer eventId) throws SumoException {
		VendorContractInfo vendorContractInfo = manager.find(VendorContractInfo.class, eventId);
		if (Objects.isNull(vendorContractInfo)) {
			throw new SumoException("Contract Not Found");
		}
		if (PriceStatus.REJECTED.name().equalsIgnoreCase(vendorContractInfo.getRecordStatus()) ||
				PriceStatus.CANCELLED.name().equalsIgnoreCase(vendorContractInfo.getRecordStatus())) {
			throw new SumoException("Contract Not Valid");
		}
		List<VendorContractItem> vendorContractItems = getVendorContractItems(vendorContractInfo);
		VendorContractVO vendorContractVO = VendorContractMapper.INSTANCE.toDomain(vendorContractInfo);
		vendorContractVO.setVendorName(scmCache.getVendorDetail(vendorContractVO.getVendorId()).getEntityName());
		vendorContractItems.forEach(item -> vendorContractVO.getVendorContractItemVOS().add(SCMDataConverter.convert(cache, masterDataCache, item)));
		return vendorContractVO;
	}

	@Override
	public PageRequestDetail findPageRequestByEventTypeAndRecordStatus(String vendorContractEmployee, Integer vendorContractId) {
		try {
			Query query = manager.createQuery("from PageRequestDetail where recordStatus = :recordStatus and eventId = :vendorContractId and eventType= :eventType");
			query.setParameter("recordStatus", VendorStatus.COMPLETED.name());
			query.setParameter("vendorContractId", vendorContractId);
			query.setParameter("eventType", vendorContractEmployee);

			PageRequestDetail pageRequestDetail = (PageRequestDetail) query.getSingleResult();
			return pageRequestDetail;
		} catch (NoResultException e ) {
			return null;
		}
	}@Override
	public void cancelAllContractMailRequest(Integer vendorContractId, String eventType) {
		try {
			Query query = manager.createQuery("from PageRequestDetail where recordStatus = :recordStatus and eventId = :vendorContractId and eventType= :eventType");
			query.setParameter("recordStatus", VendorStatus.INITIATED.name());
			query.setParameter("vendorContractId", vendorContractId);
			query.setParameter("eventType", eventType);

			List<PageRequestDetail> pageRequestDetail = query.getResultList();
			pageRequestDetail.forEach( request -> {
				request.setRecordStatus(VendorStatus.IN_ACTIVE.name());
			});
		} catch (NoResultException e ) {

		}
	}

	@Override
	public List<VendorContractInfo> closePreviousContract(int vendorId) {
		Query query = manager.createQuery("from VendorContractInfo where vendorId =:vendorId and recordStatus=:recordStatus");
		query.setParameter("recordStatus",PriceStatus.APPLIED.name());
		query.setParameter("vendorId",vendorId);
		List<VendorContractInfo> vendorContractInfos = query.getResultList();
		vendorContractInfos.forEach(contract -> {
			contract.setRecordStatus(PriceStatus.DEACTIVATED.name());
		});
		manager.flush();
		return vendorContractInfos;
	}

	@Override
	public void checkPendingVendorContract(int vendorId) throws SumoException {
		Query query = manager.createQuery("from VendorContractInfo where vendorId =:vendorId and recordStatus not in (:recordStatus)");
		query.setParameter("recordStatus",List.of(PriceStatus.APPLIED.name(),PriceStatus.CANCELLED.name(),PriceStatus.REJECTED.name()));
		query.setParameter("vendorId",vendorId);
		if (query.getResultList().size()>0) {
			throw new SumoException("Contract Already in Process");
		}
	}
}
