/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@SuppressWarnings("serial")
@Entity
@Table(name = "TRANSFER_ORDER_NOTE")
public class TransferOrderNoteData implements java.io.Serializable {

    private Integer transferOrderNoteId;
    private Integer assetId;
    private String gstin;
    private Integer unitId;
    private String unitName;
    private String originalInvoiceNo;
    private Date originalInvoiceDate;
    private String uniqueSequence;
    private Date dateOfTransfer;
    private String documentType;
    private String placeOfSupply;
    private BigDecimal amountWithTax;
    private BigDecimal applicableTaxRate;
    private BigDecimal taxRate;
    private BigDecimal taxableValue;
    private BigDecimal cessAmount;
    private BigDecimal preGst;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TRANSFER_ORDER_NOTE_ID", nullable = false, unique = true)
    public Integer getTransferOrderNoteId() {
        return transferOrderNoteId;
    }

    public void setTransferOrderNoteId(Integer transferOrderNoteId) {
        this.transferOrderNoteId = transferOrderNoteId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "GSTIN", nullable = false)
    public String getGstin() {
        return gstin;
    }

    public void setGstin(String gstin) {
        this.gstin = gstin;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME", nullable = false)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "ORIGINAL_INVOICE_NO", nullable = false)
    public String getOriginalInvoiceNo() {
        return originalInvoiceNo;
    }

    public void setOriginalInvoiceNo(String originalInvoiceNo) {
        this.originalInvoiceNo = originalInvoiceNo;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "ORIGINAL_INVOICE_DATE", length = 19)
    public Date getOriginalInvoiceDate() {
        return originalInvoiceDate;
    }

    public void setOriginalInvoiceDate(Date originalInvoiceDate) {
        this.originalInvoiceDate = originalInvoiceDate;
    }

    @Column(name = "UNIQUE_SEQUENCE", nullable = false)
    public String getUniqueSequence() {
        return uniqueSequence;
    }

    public void setUniqueSequence(String uniqueSequence) {
        this.uniqueSequence = uniqueSequence;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DATE_OF_TRANSFER", length = 19)
    public Date getDateOfTransfer() {
        return dateOfTransfer;
    }

    public void setDateOfTransfer(Date dateOfTransfer) {
        this.dateOfTransfer = dateOfTransfer;
    }

    @Column(name = "DOCUMENT_TYPE", nullable = false)
    public String getDocumentType() {
        return documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    @Column(name = "PLACE_OF_SUPPLY", nullable = false)
    public String getPlaceOfSupply() {
        return placeOfSupply;
    }

    public void setPlaceOfSupply(String placeOfSupply) {
        this.placeOfSupply = placeOfSupply;
    }

    @Column(name = "AMOUNT_WITH_TAX", nullable = false)
    public BigDecimal getAmountWithTax() {
        return amountWithTax;
    }

    public void setAmountWithTax(BigDecimal amountWithTax) {
        this.amountWithTax = amountWithTax;
    }

    @Column(name = "APPLICABLE_TAX_RATE", nullable = false)
    public BigDecimal getApplicableTaxRate() {
        return applicableTaxRate;
    }

    public void setApplicableTaxRate(BigDecimal applicableTaxRate) {
        this.applicableTaxRate = applicableTaxRate;
    }

    @Column(name = "TAX_RATE", nullable = false)
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    @Column(name = "TAXABLE_VALUE", nullable = false)
    public BigDecimal getTaxableValue() {
        return taxableValue;
    }

    public void setTaxableValue(BigDecimal taxableValue) {
        this.taxableValue = taxableValue;
    }

    @Column(name = "CESS_AMOUNT", nullable = false)
    public BigDecimal getCessAmount() {
        return cessAmount;
    }

    public void setCessAmount(BigDecimal cessAmount) {
        this.cessAmount = cessAmount;
    }

    @Column(name = "PRE_GET", nullable = false)
    public BigDecimal getPreGst() {
        return preGst;
    }

    public void setPreGst(BigDecimal preGst) {
        this.preGst = preGst;
    }
}