package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.FullfillmentReportGenerateService;
import com.stpl.tech.scm.data.transport.model.FullfillmentData;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataUnitLevel;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataWarehouseLevel;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class FullfillmentReportGenerateServiceImpl implements FullfillmentReportGenerateService {


    private static final Logger LOG = LoggerFactory.getLogger(FullfillmentReportGenerateServiceImpl.class);
    @Autowired
    private EnvProperties env;

    public void generateWarehouseLevelSheet(ArrayList<FullfillmentDataWarehouseLevel> result , String sheetName, Boolean isNew, String fileName){
        try{
            XSSFWorkbook workbook;
            if(isNew){
                workbook = new XSSFWorkbook();
            }else{
                FileInputStream file = new FileInputStream( env.getBasePath()+"/"+fileName);
                workbook = new XSSFWorkbook(file);
                file.close();
            }

            //Creating a Spread Sheet
            XSSFSheet spreadsheet = workbook.createSheet(sheetName);
            XSSFRow row = spreadsheet.createRow(0);
            XSSFCell cell;
            cell = row.createCell(0);
            cell.setCellValue("TRANSFERRING_UNIT");
            cell = row.createCell(1);
            cell.setCellValue("TOTAL_FF_%");
            cell = row.createCell(2);
            cell.setCellValue("CRITICAL_FF_%");
            cell = row.createCell(3);
            cell.setCellValue("TOTAL_MINUS_BAKERY_FF_%");
            cell = row.createCell(4);
            cell.setCellValue("BAKERY_FF_%");

            int i = 1;
            for(FullfillmentDataWarehouseLevel f : result){
                row = spreadsheet.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(f.getTransferingUnit());
                cell = row.createCell(1);
                cell.setCellValue(Math.round(f.getAvgImFPer() * 100.0) / 100.0 +"%");
                cell = row.createCell(2);
                cell.setCellValue(f.getCriticalAvg()==0 && f.getIsCriticalProd()=="N" ? "No Critical Product" : Math.round(f.getCriticalAvg() * 100.0) / 100.0 + "%");
                cell = row.createCell(3);
                cell.setCellValue(Math.round(f.getWithoutBakeryAvgFPer() * 100.0)/100.0 + " %");
                cell = row.createCell(4);
                cell.setCellValue(f.getBakeryFP() == -1 ? "N/A" : Math.round(f.getBakeryFP() * 100.0 ) / 100.0+" %");

                i++;
            }
            if(isNew){
                File file = new File(env.getBasePath()+"/"+fileName);
                FileOutputStream out = new FileOutputStream(file);
                workbook.write(out);
                out.close();
            }else{
                FileOutputStream outFile = new FileOutputStream(env.getBasePath()+"/"+fileName);
                workbook.write(outFile);
                outFile.close();
            }
        }catch (Exception e){
            LOG.error("###### Error while generating WarehouseLevelSheet ###### : {}",e.getMessage());
            e.printStackTrace();
        }
    }

    public void generateUnitLevelDetailFullFillmentSheet(ArrayList<FullfillmentDataUnitLevel> result , String fileName, String sheetName){
        try{
            FileInputStream file = new FileInputStream(env.getBasePath()+"/"+fileName);
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            file.close();
            //Creating a Spread Sheet
            XSSFSheet spreadsheet = workbook.createSheet(sheetName);
            XSSFRow row = spreadsheet.createRow(0);
            XSSFCell cell;
            cell = row.createCell(0);
            cell.setCellValue("TRANSFERRING_UNIT");
            cell = row.createCell(1);
            cell.setCellValue("TRANSFERRING_UNIT_ID");
            cell = row.createCell(2);
            cell.setCellValue("REQUESTING_UNIT");
            cell = row.createCell(3);
            cell.setCellValue("REQUESTING_UNIT_ID");
            cell = row.createCell(4);
            cell.setCellValue("FP_Weighted_Avg");
            cell = row.createCell(5);
            cell.setCellValue("Impacted_FP_Weighted_Avg");

            int i = 1;
            for(FullfillmentDataUnitLevel f : result){
                row = spreadsheet.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(f.getTransferringUnit());
                cell = row.createCell(1);
                cell.setCellValue(f.getTransferringUnitId());
                cell = row.createCell(2);
                cell.setCellValue(f.getRequestingUnit());
                cell = row.createCell(3);
                cell.setCellValue(f.getRequestingUnitId());
                cell = row.createCell(4);
                cell.setCellValue(f.getFPerWeightedAvg());
                cell = row.createCell(5);
                cell.setCellValue(f.getImFPerWeightedAvg()==-1 ? f.getFPerWeightedAvg() : f.getImFPerWeightedAvg());
                i++;
            }
            FileOutputStream outFile = new FileOutputStream(env.getBasePath()+"/"+fileName);
            workbook.write(outFile);
            outFile.close();

        }catch (Exception e){
            LOG.error("#### Error while generating UnitLevelDetailFullFillmentSheet : {} ",e.getMessage());
            e.printStackTrace();
        }
    }

    public void generateDetailFullFillmentSheet(List<FullfillmentData> result, String fileName, String sheetName){
        try{
            FileInputStream file = new FileInputStream(env.getBasePath()+"/"+fileName);
            XSSFWorkbook workbook = new XSSFWorkbook(file);
            file.close();
            //Creating a Spread Sheet
            XSSFSheet spreadsheet = workbook.createSheet(sheetName);
            XSSFRow row = spreadsheet.createRow(0);
            XSSFCell cell;
            cell = row.createCell(0);
            cell.setCellValue("TRANSFERRING_UNIT");
            cell = row.createCell(1);
            cell.setCellValue("TRANSFERRING_UNIT_ID");
            cell = row.createCell(2);
            cell.setCellValue("REQUESTING_UNIT");
            cell = row.createCell(3);
            cell.setCellValue("REQUESTING_UNIT_ID");
            cell = row.createCell(4);
            cell.setCellValue("REQUEST_ORDER_ID");
            cell = row.createCell(5);
            cell.setCellValue("REQUEST_ORDER_ITEM_ID");
            cell = row.createCell(6);
            cell.setCellValue("PRODUCT_NAME");
            cell = row.createCell(7);
            cell.setCellValue("PRODUCT_ID");
            cell = row.createCell(8);
            cell.setCellValue("UNIT_OF_MEASURE");
            cell = row.createCell(9);
            cell.setCellValue("REQUESTED_ABSOLUTE_QUANTITY");
            cell = row.createCell(10);
            cell.setCellValue("TRANSFERRED_QUANTITY");
            cell = row.createCell(11);
            cell.setCellValue("RECEIVED_QUANTITY");
            cell = row.createCell(12);
            cell.setCellValue("IS_CRITICAL");
            cell = row.createCell(13);
            cell.setCellValue("FULLFILLMENT_PERCENTAGE");
            cell = row.createCell(14);
            cell.setCellValue("IMPACTED_FULLFILLMENT_PERCENTAGE");
            cell = row.createCell(15);
            cell.setCellValue("IS_IMPACTED");

            int i = 1;
            for(FullfillmentData f : result){
                row = spreadsheet.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(f.getTransferringUnit());
                cell = row.createCell(1);
                cell.setCellValue(f.getTransferringUnitId());
                cell = row.createCell(2);
                cell.setCellValue(f.getRequestingUnit());
                cell = row.createCell(3);
                cell.setCellValue(f.getRequestingUnitId());
                cell = row.createCell(4);
                cell.setCellValue(f.getRequestOrderId());
                cell = row.createCell(5);
                cell.setCellValue(f.getRequestOrderItemId());
                cell = row.createCell(6);
                cell.setCellValue(f.getProductName());
                cell = row.createCell(7);
                cell.setCellValue(f.getProductId());
                cell = row.createCell(8);
                cell.setCellValue(f.getUnitOfMeasure());
                cell = row.createCell(9);
                cell.setCellValue(f.getRequestedAbsoluteQuantity());
                cell = row.createCell(10);
                cell.setCellValue(f.getTransferredQuantity());
                cell = row.createCell(11);
                cell.setCellValue(f.getReceivedQuantity());
                cell = row.createCell(12);
                cell.setCellValue(f.getIsCritical());
                cell = row.createCell(13);
                cell.setCellValue(f.getFullfillmentPercentage());
                cell = row.createCell(14);
                cell.setCellValue(f.getImpactedFullfillmentPercentage() == -1 ? f.getFullfillmentPercentage() : f.getImpactedFullfillmentPercentage());
                cell = row.createCell(15);
                cell.setCellValue(f.getIsImpacted()==null?"N":f.getIsImpacted());
                i++;
            }
            FileOutputStream outFile = new FileOutputStream(env.getBasePath()+"/"+fileName);
            workbook.write(outFile);
            outFile.close();

        }catch (Exception e){
            LOG.error("#### Error while generating DetailFullFillmentSheet : {} ",e.getMessage());
            e.printStackTrace();
        }
    }
}
