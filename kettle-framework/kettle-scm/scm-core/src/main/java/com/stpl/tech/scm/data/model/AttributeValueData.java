/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * AttributeValueData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ATTRIBUTE_VALUE", uniqueConstraints = @UniqueConstraint(columnNames = {"ATTRIBUTE_ID",
    "ATTRIBUTE_VALUE"}))
public class AttributeValueData implements java.io.Serializable {

    private Integer attributeValueId;
    private AttributeDefinitionData attributeDefinition;
    private String attributeValue;
    private String attributeValueShortCode;
    private String attributeValueStatus;

    public AttributeValueData() {
    }

    public AttributeValueData(AttributeDefinitionData attributeDefinition, String attributeValue,
                              String attributeValueStatus) {
        this.attributeDefinition = attributeDefinition;
        this.attributeValue = attributeValue;
        this.attributeValueStatus = attributeValueStatus;
    }

    public AttributeValueData(AttributeDefinitionData attributeDefinition, String attributeValue,
                              String attributeValueShortCode, String attributeValueStatus) {
        this.attributeDefinition = attributeDefinition;
        this.attributeValue = attributeValue;
        this.attributeValueShortCode = attributeValueShortCode;
        this.attributeValueStatus = attributeValueStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "ATTRIBUTE_VALUE_ID", unique = true, nullable = false)
    public Integer getAttributeValueId() {
        return this.attributeValueId;
    }

    public void setAttributeValueId(Integer attributeValueId) {
        this.attributeValueId = attributeValueId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ATTRIBUTE_ID", nullable = false)
    public AttributeDefinitionData getAttributeDefinition() {
        return this.attributeDefinition;
    }

    public void setAttributeDefinition(AttributeDefinitionData attributeDefinition) {
        this.attributeDefinition = attributeDefinition;
    }

    @Column(name = "ATTRIBUTE_VALUE", nullable = false)
    public String getAttributeValue() {
        return this.attributeValue;
    }

    public void setAttributeValue(String attributeValue) {
        this.attributeValue = attributeValue;
    }

    @Column(name = "ATTRIBUTE_VALUE_SHORT_CODE", length = 255)
    public String getAttributeValueShortCode() {
        return this.attributeValueShortCode;
    }

    public void setAttributeValueShortCode(String attributeValueShortCode) {
        this.attributeValueShortCode = attributeValueShortCode;
    }

    @Column(name = "ATTRIBUTE_VALUE_STATUS", nullable = false, length = 15)
    public String getAttributeValueStatus() {
        return this.attributeValueStatus;
    }

    public void setAttributeValueStatus(String attributeValueStatus) {
        this.attributeValueStatus = attributeValueStatus;
    }

}
