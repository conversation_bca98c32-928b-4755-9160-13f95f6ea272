/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.util;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;

import org.apache.poi.ss.usermodel.*;
import org.joda.time.DateTime;
import org.joda.time.Seconds;
import org.joda.time.Weeks;
import org.slf4j.Logger;
import org.springframework.web.multipart.MultipartFile;

import com.google.gson.Gson;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitSubCategory;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.domain.model.CategoryCode;
import com.stpl.tech.scm.domain.model.DerivedMapping;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.IdCodeNameType;
import com.stpl.tech.scm.domain.model.MonkWastageDetail;
import com.stpl.tech.scm.domain.model.ObjectFactory;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.model.WastageEventType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;


public class SCMUtil extends AppUtils {

	public static final int COLUMN_WIDTH = 15;
	public static final int NAME_COLUMN_WIDTH = 35;
	public static final String FULL_AUDIT_DAY_CLOSE = "FULL_AUDIT_DAY_CLOSE";

	private static final TimeZone TIME_ZONE = TimeZone.getTimeZone("Asia/Kolkata");


	private static ObjectFactory objectFactory = new ObjectFactory();

	public static BigDecimal convertToBigDecimal(int value) {
		Float number = Float.valueOf(value);
		return convertToBigDecimal(number);
	}

	public static Style getHeaderStyle(WorkbookContext workbookContext) {
		Style style = new Style() {
			@Override
			public void enrich(WorkbookContext workbookContext, CellStyle cellStyle) {
				Font font = workbookContext.toNativeWorkbook().createFont();
				font.setFontHeightInPoints((short) 12);
				font.setBold(true);
				font.setColor(IndexedColors.WHITE.getIndex());
				cellStyle.setFont(font);
				cellStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
				cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
			}
		};
		CellStyle headerStyle = workbookContext.toNativeWorkbook().createCellStyle();
		style.enrich(workbookContext,headerStyle);


		return style;
	}

	public static Style getCellStyle(WorkbookContext workbookContext) {
		Style style = new Style() {
			@Override
			public void enrich(WorkbookContext workbookContext, CellStyle cellStyle) {
				Font font = workbookContext.toNativeWorkbook().createFont();
				font.setFontHeightInPoints((short) 12);

				cellStyle.setFont(font);
				cellStyle.setAlignment(HorizontalAlignment.RIGHT);
			}
		};
		CellStyle headerStyle = workbookContext.toNativeWorkbook().createCellStyle();
		style.enrich(workbookContext,headerStyle);


		return style;
	}


	public static BigDecimal convertToBigDecimal(Float value) {
		if (value != null) {
			return new BigDecimal(value, new MathContext(6, RoundingMode.HALF_UP));
		} else {
			return new BigDecimal(0.0f, new MathContext(6, RoundingMode.HALF_UP));
		}
	}

	public static BigDecimal convertToBigDecimal(BigDecimal value) {
		return value != null ? value : BigDecimal.ZERO;
	}

	public static IdCodeName generateIdCodeName(Integer id, String code, String name) {
		IdCodeName idCodeName = objectFactory.createIdCodeName();
		idCodeName.setId(id);
		idCodeName.setCode(code);
		idCodeName.setName(name);
		return idCodeName;
	}

	public static IdCodeNameType generateIdCodeNameType(Integer id, String code, String name, String type, String status) {
		IdCodeNameType idCodeName = objectFactory.createIdCodeNameType();
		idCodeName.setId(id);
		idCodeName.setCode(code);
		idCodeName.setName(name);
		idCodeName.setType(type);
		idCodeName.setStatus(status);
		return idCodeName;
	}
	public static IdCodeNameType generateIdCodeNameType(Integer id, String code, String name, String type, String status,
														String vendorBlocked, String vendorBlockedReason, Date unblockedTill) {
		IdCodeNameType idCodeName = objectFactory.createIdCodeNameType();
		idCodeName.setId(id);
		idCodeName.setCode(code);
		idCodeName.setName(name);
		idCodeName.setType(type);
		idCodeName.setStatus(status);
		idCodeName.setVendorBlocked(vendorBlocked);
		idCodeName.setBlockedReason(vendorBlockedReason);
		idCodeName.setUnblockedTillDate(unblockedTill);
		return idCodeName;
	}

	public static IdCodeNameStatus generateIdCodeNameStatus(Integer id, String code, String name, String status) {
		IdCodeNameStatus idCodeName = objectFactory.createIdCodeNameStatus();
		idCodeName.setId(id);
		idCodeName.setCode(code);
		idCodeName.setName(name);
		idCodeName.setStatus(status);
		return idCodeName;
	}

	public static IdCodeName getSystemUser() {
		return generateIdCodeName(SCMServiceConstants.SYSTEM_USER, "SYSTEM", "SYSTEM");
	}

	public static String writeStockFile(String rootPath, MultipartFile file, Logger log, String name,
			String parentDir) {
		if (!file.isEmpty()) {
			log.info("File Size is ::: ", file.getSize());
			try {
				return write(file.getBytes(), rootPath, parentDir, "stockFiles", name, log);
			} catch (IOException e) {
				log.error("Could not read file ::::", e);
				return null;
			}
		} else {
			return null;
		}
	}

	public static String write(byte[] bytes, String rootPath, String parentDir, String typeOfFile, String fileName,
			Logger log) {
		try {
			// Creating the directory to store file
			File dir = new File(rootPath + File.separator + typeOfFile + File.separator + parentDir);
			if (!dir.exists()) {
				dir.mkdirs();
			}
			// Create the file on server
			File serverFile = new File(dir.getAbsolutePath() + File.separator + fileName);
			BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream(serverFile));
			stream.write(bytes);
			stream.close();
			log.info("Server File Location=" + serverFile.getAbsolutePath());
			return serverFile.getAbsolutePath();
		} catch (Exception e) {
			log.error("Encountered error while parsing file ::::", e);
			return null;
		}
	}

	public static Date getCurrentBusinessDate() {
		Calendar calendar = Calendar.getInstance(TIME_ZONE);
		int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
		// format

		// to be changed for Warehouse and Kitchen or not?
		if (hour < 6) {
			calendar.add(Calendar.DATE, -1);
		}

		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	// to find the coming thursday from the given date
	public static Date getNextThursday(Date businessdate)
	{
		Calendar calendar = Calendar.getInstance(TIME_ZONE);
		calendar.setTime(businessdate);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		getUpcomingWeekDateByDay(calendar, Calendar.THURSDAY);
		return calendar.getTime();

	}
	public static Date getNextWeeklyInventoryDate() {
		Calendar calendar = Calendar.getInstance(TIME_ZONE);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		getUpcomingWeekDateByDay(calendar, Calendar.THURSDAY);
		if(calendar.DAY_OF_MONTH == calendar.getActualMaximum(Calendar.DAY_OF_MONTH)) {
			getUpcomingWeekDateByDay(calendar, Calendar.THURSDAY);
		}
		return calendar.getTime();
	}

	public static void getUpcomingWeekDateByDay(Calendar calendar, int day) {
		if(calendar.get(Calendar.DAY_OF_WEEK) == day) {
			calendar.add(Calendar.DATE, 1);
		}
		while (calendar.get(Calendar.DAY_OF_WEEK) != day) {
			calendar.add(Calendar.DATE, 1);
		}
	}

	public static Date getNextMonthlyInventoryDate() {
		Calendar calendar = Calendar.getInstance(TIME_ZONE);
		calendar.add(Calendar.DAY_OF_MONTH, 5);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		return calendar.getTime();
	}

	public static void main(String[] args) {
		WastageEvent wastageEvent = new WastageEvent();
		wastageEvent.setUnitId(26016);
		wastageEvent.setBusinessDate(SCMUtil.getBusinessDate());
		wastageEvent.setGeneratedBy(120056);
		wastageEvent.setGenerationTime(getCurrentTimestamp());
		wastageEvent.setStatus(StockEventStatus.SETTLED);
		wastageEvent.setType(WastageEventType.PRODUCT);
		WastageData wastageData = new WastageData();
		wastageData.setProductId(100238);
		wastageData.setQuantity(BigDecimal.ONE);
		List<MonkWastageDetail> monkDrilldowns = new ArrayList<>();
		for(int i=1;i<5;i++){
			MonkWastageDetail drilldown = new MonkWastageDetail();
			drilldown.setChaiMonk("CHAI_MONK"+i);
			drilldown.setQuantity(0.25);
			drilldown.setOrderId(0);
			drilldown.setMonkEvent("Day Cleaning");
			drilldown.setTaskId(0);
			monkDrilldowns.add(drilldown);
		}
		wastageData.setWasteDrillDown(monkDrilldowns);
		wastageEvent.getItems().add(wastageData);
		System.out.println(JSONSerializer.toJSON(wastageEvent));
	}

	public static Date getScmBusinessDate(Date date) {
		Calendar calendar = getCalender();
		calendar.setTime(date); // format
		int hour = calendar.get(Calendar.HOUR_OF_DAY); // gets hour in 24h
		if (hour < 5) {
			calendar.add(Calendar.DATE, -1);
		}
		calendar.set(Calendar.HOUR, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	public static long getDateDifferenceInHours(Date date1, Date date2){
		Calendar calendar1 = Calendar.getInstance();
		Calendar calendar2 = Calendar.getInstance();
		calendar1.setTime(date1);
		calendar2.setTime(date2);
		long time1 = calendar1.getTimeInMillis();
		long time2 = calendar2.getTimeInMillis();
		long timeDiff = Math.abs(time2 - time1);
		long hourDiff = timeDiff / (1000 * 60 * 60);
		return hourDiff;
	}

	public static int getListTypeForUnit() {
		DateTime curDate = getCurrentTimeIST();
		Seconds difference = SCMUtil.getTimeDifference(curDate,
				DateTime.parse(SCMServiceConstants.PHASE_2_ROLLOUT_DATE));
		Integer dayOfWeek = curDate.getDayOfWeek() == 0 ? 7 : curDate.getDayOfWeek();
		Weeks w = difference.toStandardWeeks();
		int listNum = (7 * (w.getWeeks() % 2) + dayOfWeek) + 1;
		System.out.println(listNum);
		return listNum;
	}

	public static boolean validateCreationTime(Date requestDate, int daysOfExpiry) {
		Date currentDate = SCMUtil.getCurrentBusinessDate();
		Long timeDiff = currentDate.getTime() - requestDate.getTime();
		timeDiff = (timeDiff / (3600000 * 24));
		return timeDiff.intValue() <= daysOfExpiry;
	}

	public static Date getEndOfBusinessDay(Date businessDate) {
		Calendar c = new GregorianCalendar(TIME_ZONE);
		c.setTime(businessDate);
		c.add(Calendar.DATE, 1);
		c.set(Calendar.HOUR_OF_DAY, 5); // anything 0 - 23
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		return c.getTime();
	}

	public static Date getPreviousBusinessDate(Date businessDate) {
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessDate);
		calendar.set(Calendar.HOUR_OF_DAY, 0); // gets hour in 24h format
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.add(Calendar.DATE, -1);
		return calendar.getTime();
	}

	public static Date getPreviousBusinessDateIST(Date businessDate , int days) {
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessDate);
		calendar.set(Calendar.HOUR_OF_DAY, 5); // gets hour in 24h format
		calendar.set(Calendar.MINUTE, 30);
		calendar.set(Calendar.SECOND, 0);
		calendar.add(Calendar.DATE, -days);
		return calendar.getTime();
	}

	public static List<Date> getSecondOrFourthSaturday(Date businessdate){
		List<Date> result = new ArrayList<>();
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessdate);
		//for second saturday
		for(int i=8;i<15;i++) {
			calendar.set(Calendar.DATE, i);
			if(Calendar.SATURDAY == calendar.get(Calendar.DAY_OF_WEEK)) {
				result.add(calendar.getTime());
			}
		}
		//for fourth saturday
		for(int i=22;i<29;i++) {
			calendar.set(Calendar.DATE, i);
			if(Calendar.SATURDAY == calendar.get(Calendar.DAY_OF_WEEK)) {
				result.add(calendar.getTime());
			}
		}
		return result;
	}

	public static StockTakeType getRequiredFrequency(Date businessDate) {
		Calendar calendar = Calendar.getInstance(TIME_ZONE);
		calendar.setTime(businessDate != null ? businessDate : getCurrentBusinessDate());
		if (calendar.get(Calendar.DAY_OF_MONTH) == getLastDayOfCurrentMonth(calendar.getTime())
				.get(Calendar.DAY_OF_MONTH)) {
			return StockTakeType.MONTHLY;
		} else {
			return StockTakeType.DAILY;
		}
	}

	public static String getFormattedDate(Date date) {
		return new DateTime(date.getTime()).toString(AppConstants.DATE_FORMATTER);
	}
	public  static  String getFormatedDate(Date date)
	{
		return new DateTime(date.getTime()).toString(AppConstants.DATE_MONTH_FORMATTER);
	}

	public static String getSkuPkgKey(int skuId, int packagingId) {
		return skuId + "_" + packagingId;
	}

	public static Date getNextEventDate(Date businessDate, StockTakeType frequency) {
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessDate);
		switch (frequency) {
		case DAILY:
			calendar.add(Calendar.DATE, 1);
			break;
		case WEEKLY:
			calendar.add(Calendar.DATE, 7);
			break;
		case MONTHLY:
			calendar.add(Calendar.MONTH, 1);
			calendar.set(Calendar.DAY_OF_MONTH, 1);
			break;
		default:
			break;
		}
		return calendar.getTime();
	}

	public static StockTakeType checkTypeOfEvent(Date businessDate) {
		StockTakeType frequency = StockTakeType.DAILY;
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessDate);
		if (calendar.get(Calendar.DAY_OF_MONTH) == getLastDayOfCurrentMonth(businessDate).get(Calendar.DAY_OF_MONTH)) {
			frequency = StockTakeType.MONTHLY;
		} else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) {
			frequency = StockTakeType.WEEKLY;
		}
		return frequency;
	}

	public static boolean checkIfWeekly(Date businessDate) {
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessDate);
		if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.THURSDAY
				|| calendar.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) {
			return true;
		}
		return false;
	}

	public static Calendar getLastDayOfCurrentMonth(Date businessDate) {
		Calendar cal = new GregorianCalendar();
		cal.setTime(businessDate);
		cal.set(Calendar.DATE, 1);
		cal.add(Calendar.MONTH, 1);
		cal.add(Calendar.DATE, -1);
		return cal;
	}

	public static boolean checkDate(Date date, Date businessDate) {
		System.out.println(date.getTime());
		System.out.println(businessDate.getTime());
		return getDate(businessDate).equals(getDate(date));
	}

	public static <T, E> T clone(E object, Class<T> clazz) {
		Gson gson = new Gson();
		String str = gson.toJson(object);
		return gson.fromJson(str, clazz);
	}

	public static String generateRandomPOId() {
		return "PO-" + generateRandomOrderId();
	}

	public static BigDecimal convertToBigDecimal(Double sum) {
		return convertToBigDecimal(sum.floatValue());
	}

	public static boolean isOffice(UnitCategory category) {
		return category != null && category.equals(UnitCategory.OFFICE);
	}

	public static boolean isWareHouse(UnitCategory family) {
		return family != null && family.equals(UnitCategory.WAREHOUSE);
	}

	public static boolean isKitchen(UnitCategory family) {
		return family != null && family.equals(UnitCategory.KITCHEN);
	}

	public static boolean isCafe(UnitCategory family) {
		List<UnitCategory> backEndUnitTypes = Arrays.asList(UnitCategory.KITCHEN, UnitCategory.WAREHOUSE);
		return family != null && !backEndUnitTypes.contains(family);
	}

	public static final String getRegistrationLink(String url, String auth) {
		return url + "?id=" + auth;
	}

	public static boolean hasProductFulFillmentTypeMappings(ProductDefinition productDefinition) {
		return FulfillmentType.DERIVED.equals(productDefinition.getFulfillmentType())
				&& productDefinition.getDefaultFulfillmentType() != null
				&& productDefinition.getDerivedMappings() != null
				&& productDefinition.getDerivedMappings().stream().filter(
						defaultType -> !productDefinition.getDefaultFulfillmentType().equals(defaultType.getType()))
						.count() > 0;
	}

	public static FulfillmentType getFulfillmentType(ProductDefinition product, Integer unitId) {
		FulfillmentType type = product.getFulfillmentType();
		if (type.equals(FulfillmentType.DERIVED)) {
			if (product != null && !product.getDerivedMappings().isEmpty()) {
				Optional<DerivedMapping> optionalType = product.getDerivedMappings().stream()
						.filter(derivedMapping -> unitId.equals(derivedMapping.getUnit())).findAny();
				type = optionalType.isPresent() ? optionalType.get().getType() : product.getDefaultFulfillmentType();
			} else {
				type = product.getDefaultFulfillmentType();
			}
		}
		return type;
	}

	public static String generateProductOrSkuCode(Class<?> type, String categoryCode, Integer productId) {
		String predecessor = null;
		if (type == ProductDefinition.class) {
			predecessor = "P";
		}
		if (type == SkuDefinition.class) {
			predecessor = "S";
		}
		if (predecessor != null && categoryCode != null && CategoryCode.getCodeFromCategory(categoryCode) != null
				&& productId != null) {
			String code = String.format("%08d", productId);
			return predecessor + "-" + CategoryCode.getCodeFromCategory(categoryCode) + code;
		}
		return null;
	}

	public static Optional<VendorDispatchLocation> getDispatchLocation(VendorDetail vendor, int dispatchId) {
		return vendor.getDispatchLocations().stream()
				.filter(vendorDispatchLocation -> vendorDispatchLocation.getDispatchId().equals(dispatchId))
				.findFirst();
	}

	/**
	 * check cafe based on unit category id
	 *
	 * @param u
	 * @return true if unit is cafe
	 */
	public static boolean isCafe(UnitDetail u) {
		return u != null && u.getUnitCategoryId() == SCMServiceConstants.CAFE;
	}

	/**
	 * check warehouse based on unit category id
	 *
	 * @param u
	 * @return true if unit is warehouse
	 */
	public static boolean isWareHouse(UnitDetail u) {
		return u != null && u.getUnitCategoryId() == SCMServiceConstants.WAREHOUSE;
	}

	/**
	 * check kitchen based on unit category id
	 *
	 * @param u
	 * @return true if unit is kitchen
	 */
	public static boolean isKitchen(UnitDetail u) {
		return u != null && u.getUnitCategoryId() == SCMServiceConstants.KITCHEN;
	}

	/**
	 * check unit is warehouse or kitchen based on unit category id
	 *
	 * @param u
	 * @return true if unit either warehouse or kitchen
	 */
	public static boolean isWareHouseOrKitchen(UnitDetail u) {
		return isWareHouse(u) || isKitchen(u);
	}

	public static Boolean isNpdUnit(UnitDetail u){
		return  u.getUnitId() == 26167;
	}

	/**
	 * check company for the unit based on unit company id
	 *
	 * @param u
	 * @return true if company is DKC
	 */
	public static boolean isDkcUnit(UnitDetail u) {
		return u.getCompanyId() == 1001;
	}

	/**
	 * check company for the unit based on unit company id
	 *
	 * @param u
	 * @return true if company is STPL
	 */
	public static boolean isStplUnit(UnitDetail u) {
		return u.getCompanyId() == 1000;
	}

	/**
	 * produces concatenated key, made a function to keep the sequence generic
	 *
	 * @param id
	 * @param str
	 * @return concatenation of both
	 */
	public static String getkey(int id, String str) {
		return id + str;
	}

	/**
	 * Check if the fulfillment type for the product is
	 * {@link 'FulfillmentType.DERIVED'}
	 *
	 * @param p
	 * @return
	 */
	public static boolean isDerivedFulfillmentType(ProductDefinition p) {
		return FulfillmentType.DERIVED.equals(p.getFulfillmentType());
	}

	/**
	 * Check if the fulfillment type is EXTERNAL {@link 'FulfillmentType.EXTERNAL'}
	 *
	 * @param type
	 * @return
	 */
	public static boolean isExternalFulfillmentType(FulfillmentType type) {
		return FulfillmentType.EXTERNAL.equals(type);
	}

	/**
	 * Check if unit is {@link 'UnitCategory.WAREHOUSE'} or
	 * {@link 'UnitCategory.KITCHEN'}
	 *
	 * @param u
	 * @return
	 */
	public static boolean isWareHouseOrKitchen(UnitBasicDetail u) {
		return u != null
				&& (UnitCategory.WAREHOUSE.equals(u.getCategory()) || UnitCategory.KITCHEN.equals(u.getCategory()));
	}

	public static boolean isKitchenOrWareHouseFullfillmentType(FulfillmentType fulfillmentType) {
		return isKitchenFullfillmentType(fulfillmentType) || isWareHouseFullfillmentType(fulfillmentType);
	}

	private static boolean isWareHouseFullfillmentType(FulfillmentType fulfillmentType) {
		return FulfillmentType.WAREHOUSE.equals(fulfillmentType);
	}

	public static boolean isKitchenFullfillmentType(FulfillmentType fulfillmentType) {
		return FulfillmentType.KITCHEN.equals(fulfillmentType);
	}

	public static  boolean isWarehouseFullfillmentType(FulfillmentType fulfillmentType){
		return FulfillmentType.WAREHOUSE.equals(fulfillmentType);
	}

	public static boolean isMonthEnd(Date businessDate) {
		Date d = AppUtils.getLastDayOfMonth(businessDate);
		if (AppUtils.getDate(d).equals(AppUtils.getDate(businessDate))) {
			return true;
		} else {
			return false;
		}
	}

	public static Date getPaymentSystemLaunchDate() {
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.set(Calendar.HOUR_OF_DAY, 0); // gets hour in 24h format
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.DATE, 1);
		calendar.set(Calendar.MONTH, 10);
		calendar.set(Calendar.YEAR, 2017);
		return calendar.getTime();
	}

	public static String filterSpecialCharacters(String dirtyString) {
		return dirtyString.replaceAll("[^a-zA-Z0-9@ ]", "");
	}

	public static String createEWayBillDateFormat(Date date) {
		return new SimpleDateFormat("dd/MM/yyyy").format(date);
	}

	public static boolean isCheckMonthly(Date businessDate) {

		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessDate);

		Calendar c2 = new GregorianCalendar(TIME_ZONE);
		c2.setTime(getMonthlyCheckDate(businessDate));

		return calendar.get(Calendar.DATE) >= c2.get(Calendar.DATE);

	}

	public static Date getMonthlyCheckDate(Date businessDate) {
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessDate);
		calendar.set(Calendar.HOUR_OF_DAY, 0); // gets hour in 24h format
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.DATE, 20);
		return calendar.getTime();
	}

	public static Date getMonthlyCheckDateEnd(Date businessDate) {
		Calendar calendar = new GregorianCalendar(TIME_ZONE);
		calendar.setTime(businessDate);
		calendar.set(Calendar.HOUR_OF_DAY, 0); // gets hour in 24h format
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.DATE, 5);
		calendar.add(Calendar.MONTH, 1);
		return calendar.getTime();
	}

	public static Date getLastDayOfThisMonth(Date businessDate) {
		Calendar cal = new GregorianCalendar(TIME_ZONE);
		cal.setTime(businessDate);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.add(Calendar.MONTH, 1);
		cal.add(Calendar.DATE, -1);
		return cal.getTime();
	}

	public static Date addMonth(Date date, int i) {
		Calendar cal = new GregorianCalendar(TIME_ZONE);
		cal.setTime(date);
		cal.add(Calendar.MONTH, i);
		return cal.getTime();
	}

	public static Date addDays(Date date, int i) {
		Calendar cal = new GregorianCalendar(TIME_ZONE);
		cal.setTime(date);
		cal.add(Calendar.DATE, i);
		return cal.getTime();
	}

	public static boolean isSunday(Date date){
		Calendar cal = getCalender();
		cal.setTime(date);
		if(Calendar.SUNDAY == cal.get(Calendar.DAY_OF_WEEK)){
			return true;
		}
		else {
			return false;
		}
	}

	public static boolean isMonthly(StockTakeType type) {
		return StockTakeType.MONTHLY.equals(type);
	}

	public static Date getFirstDayOfThisMonth(Date businessDate) {
		Calendar cal = new GregorianCalendar(TIME_ZONE);
		cal.setTime(businessDate);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		return cal.getTime();
	}

	public static boolean isMaintenanceStore(UnitSubCategory usc) {
		return usc != null && (UnitSubCategory.MAINTENANCE.equals(usc) || UnitSubCategory.FA_WH_MAINTENANCE.equals(usc));
	}

	public static boolean isDistributionStore(UnitSubCategory usc) {
		return usc != null && UnitSubCategory.DISTRIBUTION.equals(usc);
	}

	public static boolean isInventoryWarehouseOrKitchen(UnitSubCategory usc) {
		return usc != null && UnitSubCategory.INVENTORY.equals(usc);
	}

	public static boolean isInternal(UnitSubCategory usc) {
		return usc != null && UnitSubCategory.INTERNAL.equals(usc);
	}

	public static boolean isValidDate(String inDate) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		dateFormat.setLenient(false);
		try {
			dateFormat.parse(inDate.trim());
		} catch (ParseException pe) {
			return false;
		}
		return true;
	}


	public static String getCreatedBy(String employee, Integer createdBy) {
		return employee + "[" + createdBy + "]";
	}

	public static Date getLastDayOfPreviousMonth(Date date) {

		Calendar cal = getCalender();
		cal.setTime(date);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.add(Calendar.DATE, -1);
		return cal.getTime();
	}

	public static boolean isBefore(Date dateToBeChecked, Date checkedAgainst) {

		Calendar cal = getCalendar(dateToBeChecked);
		return cal.getTime().before(checkedAgainst);
	}

    public static BigDecimal getFivePlusPercentage(BigDecimal amount) {
		BigDecimal percentage = new BigDecimal("0.05");
		BigDecimal calculatedPercentage = amount.multiply(percentage);
		return amount.add(calculatedPercentage).setScale(0, BigDecimal.ROUND_HALF_UP);
    }
}
