package com.stpl.tech.scm.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "GATEPASS_STATUS_DETAIL")
public class GatepassStatusDetailData {
	private Integer id;
	private Integer gatepassId;
	private String toStatus;
	private String fromStatus;
	private String transitionStatus;
	private String reason;
	private int updatedBy;
	private Date updatedAt;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "GATEPASS_STATUS_DETAIL_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	@Column(name = "GATEPASS_ID", nullable = false)
	public Integer getGatepassId() {
		return gatepassId;
	}

	public void setGatepassId(Integer gatepassId) {
		this.gatepassId = gatepassId;
	}

	@Column(name = "TO_STATUS", nullable = false)
	public String getToStatus() {
		return toStatus;
	}

	public void setToStatus(String toStatus) {
		this.toStatus = toStatus;
	}

	@Column(name = "FROM_STATUS", nullable = false)
	public String getFromStatus() {
		return fromStatus;
	}

	public void setFromStatus(String fromStatus) {
		this.fromStatus = fromStatus;
	}

	@Column(name = "TRANSITION_STATUS", nullable = false)
	public String getTransitionStatus() {
		return transitionStatus;
	}

	public void setTransitionStatus(String transitionStatus) {
		this.transitionStatus = transitionStatus;
	}

	@Column(name = "REASON_TEXT", nullable = false)
	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	@Column(name = "UPDATED_BY", nullable = false)
	public int getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(int updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "UPDATED_AT", nullable = false)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

}
