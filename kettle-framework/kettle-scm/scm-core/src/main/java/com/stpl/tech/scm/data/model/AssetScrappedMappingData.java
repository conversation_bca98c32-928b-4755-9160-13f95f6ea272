package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "ASSET_SCRAPPED_MAPPING")
public class AssetScrappedMappingData {

    private Integer assetScrappedMappingId;

    private Integer assetId;

    private Integer unitId;

    private BigDecimal scrappedAmount;

    private Date scrappedDate;



    public AssetScrappedMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ASSET_SCRAPPED_MAPPING_ID", unique = true, nullable = false)
    public Integer getAssetScrappedMappingId() {
        return assetScrappedMappingId;
    }

    public void setAssetScrappedMappingId(Integer assetScrappedMappingId) {
        this.assetScrappedMappingId = assetScrappedMappingId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }


    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "SCRAPPED_AMOUNT", nullable = false)
    public BigDecimal getScrappedAmount() {
        return scrappedAmount;
    }

    public void setScrappedAmount(BigDecimal scrappedAmount) {
        this.scrappedAmount = scrappedAmount;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "SCRAPPED_DATE", length = 19)
    public Date getScrappedDate() {
        return scrappedDate;
    }

    public void setScrappedDate(Date scrappedDate) {
        this.scrappedDate = scrappedDate;
    }

}
