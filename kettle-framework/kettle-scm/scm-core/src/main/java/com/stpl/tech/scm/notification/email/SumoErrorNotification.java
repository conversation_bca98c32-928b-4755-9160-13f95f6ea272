/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.notification.email;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.master.core.external.acl.service.Notification;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class SumoErrorNotification extends EmailNotification implements Notification {

	private static final Logger LOG = LoggerFactory.getLogger(SumoErrorNotification.class);

	private enum NotificationType {
		EXCEPTION, SERVER_ERROR, MSG_LIST
	}

	private String subject;
	private String error;
	private Exception exception;
	private EnvType env;
	private Date errorDate;
	private List<String> errorMessages;
	private NotificationType notificationType;
	private String endPoint;
	private String payload;
	private List<String> toEmailList;
	private List<String> toSlackList;

	public SumoErrorNotification(String subject, String error, Exception e, EnvType env, String endPoint, String refCreateRequest) {
		this.subject = subject;
		this.error = error;
		this.exception = e;
		this.env = env;
		this.errorDate = AppUtils.getCurrentTimestamp();
		this.notificationType = NotificationType.EXCEPTION;
		this.endPoint = endPoint;
		this.payload = refCreateRequest;
	}

	public SumoErrorNotification(String subject, String error, EnvType env, String endPoint, String payload) {
		this.subject = subject;
		this.error = error;
		this.env = env;
		this.endPoint = endPoint;
		this.payload = payload;
		this.errorDate = AppUtils.getCurrentTimestamp();
		this.notificationType = NotificationType.SERVER_ERROR;
	}

	public String subject() {
		return (TransactionUtils.isDev(getEnvironmentType()) ? "DEV : " : "") + "ERROR Notification : " + subject + " on "
				+ new SimpleDateFormat("yyyy-MM-dd").format(errorDate);
	}

	public String body() throws EmailGenerationException {

		StringBuffer body = new StringBuffer(
				"<html><p><b>Error Notification : </b>" + subject + "<br/> <b>Error Generation Timestamp: </b>"
						+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(errorDate) + "</p>");

		body.append("<p><b> Error Summary: </b>" + error + "<br/></p>");
		if (NotificationType.EXCEPTION.equals(notificationType)) {
			body.append("<b> Error: </b>" + exception.getMessage() == null ? "NO STACK TRACE"
					: exception.getMessage().replace("\n", "\n <br/>") + "<br/><b>Full Stack Trace:</b><br/><p>" + ExceptionUtils.getStackTrace(exception).replace("\n", "\n <br/>")
							+ "</p><br/><b> URL : </b> " + endPoint + "<br/> <b> Payload : </b>" + payload + "</html>");

		}
		if (NotificationType.SERVER_ERROR.equals(notificationType)) {
			body.append("</p><br/><b> URL : </b> " + endPoint + "<br/> <b> Payload : </b>" + payload + "</html>");
		}

		return body.toString();
	}

	public String getFromEmail() {
		return "<EMAIL>";
	}

	public String[] getToEmails() {
		return toEmailList != null && !toEmailList.isEmpty() ? toEmailList.toArray(new String[toEmailList.size()])
				: new String[] { "<EMAIL>" };
	}

	@Override
	public void sendEmail() {
		try {
			super.sendEmail();
			if (AppUtils.isProd(env) && toSlackList != null && !toSlackList.isEmpty()) {
				for (String channel : toSlackList) {
					SlackNotificationService.getInstance().sendNotification(env, "SUMO", channel, this);
				}
			}
			SlackNotificationService.getInstance().sendNotification(env, "SUMO", SlackNotification.SYSTEM_ERRORS,
					this);
		} catch (Exception e) {
			LOG.error("Error Notification Failure", e);
		}
	}

	@Override
	public String getNotificationMessage() {
		StringBuffer body = new StringBuffer("Error Notification : " + subject + "\nError Generation Timestamp: "
				+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(errorDate));
		body.append("\nError Summary: " + error);
		if (NotificationType.EXCEPTION.equals(notificationType)) {			
			String stackTrace = org.apache.commons.lang.exception.ExceptionUtils.getStackTrace(exception);
			body.append("Error: " + exception.getMessage() + "Full Stack Trace: "
					+ stackTrace.substring(0, stackTrace.length() / 10));
			
		} else if (NotificationType.MSG_LIST.equals(notificationType)) {
			
			body = body.append("\nError Messages");
			if (errorMessages != null) {
				for (String msg : errorMessages) {
					body.append("\n" + msg);
				}
			}
		}
		return body.toString();
	}

	@Override
	public EnvType getEnvironmentType() {
		return env;
	}

	public void setToEmailList(List<String> toList) {
		this.toEmailList = toList;
	}

	public List<String> getToEmailList() {
		return toEmailList;
	}

	public void setToSlackList(List<String> toSlackList) {
		this.toSlackList = toSlackList;
	}
	
}
