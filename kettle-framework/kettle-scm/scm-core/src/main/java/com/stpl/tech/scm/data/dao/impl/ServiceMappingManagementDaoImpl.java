package com.stpl.tech.scm.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import javax.persistence.Query;

import com.paytm.pg.App;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.model.PaymentMode;
import com.stpl.tech.scm.data.model.AdditionalDocumentsMaster;
import com.stpl.tech.scm.data.model.CostElementDocumentMapping;
import com.stpl.tech.scm.domain.model.IdCodeName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.analysis.function.Add;
import org.springframework.stereotype.Repository;

import com.stpl.tech.scm.data.dao.ServiceMappingManagementDao;
import com.stpl.tech.scm.data.model.CostElementCostCenterMapping;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.CostElementVendorMappingData;
import com.stpl.tech.scm.data.model.VendorCostCenterCostElementMapping;
import com.stpl.tech.scm.data.model.VendorSkuMapping;
import com.stpl.tech.scm.domain.model.CostElementPriceUpdate;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Repository
@Slf4j
public class ServiceMappingManagementDaoImpl extends SCMAbstractDaoImpl implements ServiceMappingManagementDao{
	
	@Override
	public List<CostElementData> getCostElement() {
		Query query = manager.createQuery(
				"FROM CostElementData E");
		List<CostElementData> costElementData = query.getResultList();
		return costElementData;
	}

	@Override
	public List<IdCodeNameStatus> getCostELementVendorMappings(Integer costElementId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.vendorId, vd.entityName,vd.type,vd.status,vsm.mappingStatus from CostElementVendorMappingData vsm, VendorDetailData vd where vd.id = vsm.vendorId and vsm.costElementId = :costElementId");
		query.setParameter("costElementId", costElementId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setCategory((String) record[2]);
			o.setStatus((String) record[3]);
			o.setMappingStatus((String) record[4]);
			list.add(o);
		});
		return list;
	}

	@Override
	public boolean addCostElementVendorMappings(int employeeId, String employeeName, int costElementId,
			List<Integer> vendorIds) {
		//activateCostElementVendorMapping(employeeId, employeeName, costElementId, vendorIds);
		Set<Integer> vendors = getAllVendorsForCostElement(costElementId);
		for (Integer vendorId : vendorIds) {
			if (!vendors.contains(vendorId)) {
				CostElementVendorMappingData costElementVendor = new CostElementVendorMappingData();
				costElementVendor.setCostElementId(costElementId);
				costElementVendor.setVendorId(vendorId);
				costElementVendor.setMappingStatus(AppConstants.ACTIVE);
				costElementVendor.setCreatedAt(AppUtils.getCurrentTimestamp());
				costElementVendor.setCreatedBy(getName(employeeName, employeeId));
				manager.persist(costElementVendor);
			}
		}
		manager.flush();
		return true;
	}

	private boolean activateCostElementVendorMapping(int employeeId, String employeeName, int costElementId,
			List<Integer> vendorIds) {
		Query query = manager.createQuery(
				"update CostElementVendorMappingData set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " costElementId = :costElementId and vendorId IN ( :vendorIds) and mappingStatus = :mappingStatus");
		query.setParameter("status", AppConstants.ACTIVE);
		query.setParameter("mappingStatus", AppConstants.IN_ACTIVE);
		query.setParameter("updatedBy", getName(employeeName, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("costElementId", costElementId);
		query.setParameter("vendorIds", vendorIds);
		query.executeUpdate();
		return true;
	}
	
	private String getName(String name, int id) {
		return name + " [" + id + "]";
	}
	

	private Set<Integer> getAllVendorsForCostElement(int costElementId) {
		Set<Integer> vendors = new HashSet<>();
		Query query = manager.createQuery("select vendorId from CostElementVendorMappingData where costElementId = :costElementId");
		query.setParameter("costElementId", costElementId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			vendors.addAll(list);
		}
		return vendors;
	}

	@Override
	public List<IdCodeNameStatus> getAllVendorList() {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery("select id, entityName, status,type from VendorDetailData where status = :status");
		query.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setCategory((String) record[3]);
			list.add(o);
		});
		return list;
	}

	@Override
	public boolean updateCostElementVendorMapping(int employeeId, String employeeName, int vendorId, int costElementId,
			String status) {
		Query query = manager.createQuery(
				"update CostElementVendorMappingData set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " costElementId = :costElementId and vendorId = :vendorId");
		query.setParameter("status", status);
		query.setParameter("updatedBy", getName(employeeName, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("costElementId", costElementId);
		query.setParameter("vendorId", vendorId);
		query.executeUpdate();
		
		Query querySec = manager.createQuery(
				"FROM VendorCostCenterCostElementMapping E where costElementId = :costElementId and vendorId = :vendorId");
		querySec.setParameter("costElementId", costElementId);
		querySec.setParameter("vendorId", vendorId);
		List<VendorCostCenterCostElementMapping> mappings = querySec.getResultList();
		for(VendorCostCenterCostElementMapping mapping : mappings) {
			mapping.setVendorCostCenterCostElementMappingId(mapping.getVendorCostCenterCostElementMappingId());
			mapping.setMappingStatus(AppConstants.IN_ACTIVE);
			manager.merge(mapping);
		}

		return true;
	}

	@Override
	public boolean addVendorCostElementMappings(int employeeId, String employeeName, int vendorId,
			List<Integer> costElementIds) {
		//activateVendorSkuMapping(employeeId, employeeName, vendorId, skuIds);
		Set<Integer> costElements = getAllVendorToCostElement(vendorId);
		for (Integer costElementId : costElementIds) {
			if (!costElements.contains(costElementId)) {
			CostElementVendorMappingData costElementVendor = new CostElementVendorMappingData();
			costElementVendor.setCostElementId(costElementId);
			costElementVendor.setVendorId(vendorId);
			costElementVendor.setMappingStatus(AppConstants.ACTIVE);
			costElementVendor.setCreatedAt(AppUtils.getCurrentTimestamp());
			costElementVendor.setCreatedBy(getName(employeeName, employeeId));
			manager.persist(costElementVendor);
			}
		}
		manager.flush();
		return true;
	}

	private Set<Integer> getAllVendorToCostElement(int vendorId) {
		Set<Integer> costElements = new HashSet<>();
		Query query = manager.createQuery("select costElementId from CostElementVendorMappingData where vendorId = :vendorId");
		query.setParameter("vendorId", vendorId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			costElements.addAll(list);
		}
		return costElements;
	}

	@Override
	public List<IdCodeNameStatus> getVendorCostElementMappings(Integer vendorId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.costElementId, vd.costElementName,vd.costElementStatus,vsm.mappingStatus from CostElementVendorMappingData vsm, CostElementData vd where vd.id = vsm.costElementId and vsm.vendorId = :vendorId");
		query.setParameter("vendorId", vendorId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setMappingStatus((String) record[3]);
			/*
			 * o.setCategory((String) record[4]); o.setSubCategory((String) record[5]);
			 */
			list.add(o);
		});
		return list;
	}

	@Override
	public List<IdCodeNameStatus> getAllCostCentres() {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery("select id, costCenterName, costCenterStatus,shortCode from CostCenterData where costCenterStatus = :status");
		query.setParameter("status", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setCategory((String) record[3]);
			list.add(o);
		});
		return list;
	}

	@Override
	public boolean addCostElementCostCenterMappings(int employeeId, String employeeName, int costElementId,
			List<Integer> costCentersId) {
				Set<Integer> costCenters = getAllCostCenterForCostElement(costElementId);
				for (Integer costCenter : costCentersId) {
					if (!costCenters.contains(costCenter)) {
						CostElementCostCenterMapping costElementCostCenter = new CostElementCostCenterMapping();
						costElementCostCenter.setCostElementId(costElementId);
						costElementCostCenter.setCostCenterId(costCenter);
						costElementCostCenter.setMappingStatus(AppConstants.ACTIVE);
						costElementCostCenter.setCreatedAt(AppUtils.getCurrentTimestamp());
						costElementCostCenter.setCreatedBy(getName(employeeName, employeeId));
						manager.persist(costElementCostCenter);
					}
				}
				manager.flush();
				return true;
	}

	private Set<Integer> getAllCostCenterForCostElement(int costElementId) {
		Set<Integer> costCenters = new HashSet<>();
		Query query = manager.createQuery("select costCenterId from CostElementCostCenterMapping where costElementId = :costElementId");
		query.setParameter("costElementId", costElementId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			costCenters.addAll(list);
		}
		return costCenters;
	}

	@Override
	public List<IdCodeNameStatus> searchCostELementCostCenterMapping(Integer costElementId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.costCenterId, vd.costCenterName,vd.shortCode,vd.costCenterStatus,vsm.mappingStatus from CostElementCostCenterMapping vsm, CostCenterData vd where vd.id = vsm.costCenterId and vsm.costElementId = :costElementId");
		query.setParameter("costElementId", costElementId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setCategory((String) record[2]);
			o.setStatus((String) record[3]);
			o.setMappingStatus((String) record[4]);
			list.add(o);
		});
		return list;
	}

	@Override
	public boolean updateCostElementCostCenterMappings(int employeeId, String employeeName, int costElementId,
			int costCenterId, String status) {
		Query query = manager.createQuery(
				"update CostElementCostCenterMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " costElementId = :costElementId and costCenterId = :costCenterId");
		query.setParameter("status", status);
		query.setParameter("updatedBy", getName(employeeName, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("costElementId", costElementId);
		query.setParameter("costCenterId", costCenterId);
		query.executeUpdate();
		
		
		Query querySec = manager.createQuery(
				"FROM VendorCostCenterCostElementMapping E where costElementId = :costElementId and costCenterId = :costCenterId");
		querySec.setParameter("costElementId", costElementId);
		querySec.setParameter("costCenterId", costCenterId);
		List<VendorCostCenterCostElementMapping> mappings = querySec.getResultList();
		for(VendorCostCenterCostElementMapping mapping : mappings) {
			mapping.setVendorCostCenterCostElementMappingId(mapping.getVendorCostCenterCostElementMappingId());
			mapping.setMappingStatus(AppConstants.IN_ACTIVE);
			manager.merge(mapping);
		}
		
		return true;

	}

	@Override
	public boolean addCostCenterToCostElementMappings(int employeeId, String employeeName, int costCenterId,
			List<Integer> costElementIds) {
		Set<Integer> costElements = getAllCostElementForCostCenter(costCenterId);
		for (Integer costElementId : costElementIds) {
			if (!costElements.contains(costElementId)) {
				CostElementCostCenterMapping costElementCostCenter = new CostElementCostCenterMapping();
				costElementCostCenter.setCostElementId(costElementId);
				costElementCostCenter.setCostCenterId(costCenterId);
				costElementCostCenter.setMappingStatus(AppConstants.ACTIVE);
				costElementCostCenter.setCreatedAt(AppUtils.getCurrentTimestamp());
				costElementCostCenter.setCreatedBy(getName(employeeName, employeeId));
				manager.persist(costElementCostCenter);
			}
		}
		manager.flush();
		return true;
}

	private Set<Integer> getAllCostElementForCostCenter(int costCenterId) {
		Set<Integer> costElements = new HashSet<>();
		Query query = manager.createQuery("select costElementId from CostElementCostCenterMapping where costCenterId = :costCenterId");
		query.setParameter("costCenterId", costCenterId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			costElements.addAll(list);
		}
		return costElements;
	}

	@Override
	public List<IdCodeNameStatus> searchCostCenterToCostElementMappings(Integer costCenterId) {
		List<IdCodeNameStatus> list = new ArrayList<IdCodeNameStatus>();
		Query query = manager.createQuery(
				"select vsm.costElementId, vd.costElementName,vd.costElementStatus,vsm.mappingStatus from CostElementCostCenterMapping vsm, CostElementData vd where vd.id = vsm.costElementId and vsm.costCenterId = :costCenterId");
		query.setParameter("costCenterId", costCenterId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeNameStatus o = new IdCodeNameStatus();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setMappingStatus((String) record[3]);
			/*
			 * o.setCategory((String) record[4]); o.setSubCategory((String) record[5]);
			 */
			list.add(o);
		});
		return list;
	}

	@Override
	public List<CostElementPriceUpdate> getPricedCostElements(Integer vendorId, Integer costCenterId) {
		Query query = manager
				.createQuery("select costElementId from CostElementVendorMappingData where vendorId = :vendorId");
		query.setParameter("vendorId", vendorId);
		List<Integer> list = query.getResultList();

		Query querySec = manager.createQuery(
				"select costElementId from CostElementCostCenterMapping where costCenterId = :costCenterId");
		querySec.setParameter("costCenterId", costCenterId);
		List<Integer> listSec = querySec.getResultList();

		if (listSec != null && list != null) {
			list.retainAll(listSec);
		}

		List<CostElementPriceUpdate> costElementPriceUpdate = new ArrayList<CostElementPriceUpdate>();
		if (list.isEmpty()) {
			return costElementPriceUpdate;
		}
		Query queryMain = manager.createQuery(
				"select vsm.costElementId, vd.costElementName,vsm.mappingStatus,vsm.price,vsm.id from VendorCostCenterCostElementMapping vsm, CostElementData vd where vd.id = vsm.costElementId and vsm.costCenterId = :costCenterId and vsm.vendorId = :vendorId and vsm.costElementId IN :costElementIds");
		queryMain.setParameter("costElementIds", list);
		queryMain.setParameter("costCenterId", costCenterId);
		queryMain.setParameter("vendorId", vendorId);
		List<Object[]> results = queryMain.getResultList();
		List<Integer> existingIdsInCostTable = new ArrayList<Integer>();
		results.stream().forEach((record) -> {
			CostElementPriceUpdate o = new CostElementPriceUpdate();
			existingIdsInCostTable.add((Integer) record[0]);
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setStatus((String) record[2]);
			o.setCurrentPrice((BigDecimal) record[3]);
			o.setCostElementPriceId((Integer) record[4]);
			costElementPriceUpdate.add(o);
		});
		List<Integer> costElementsNew = new ArrayList<Integer>();
		for (Integer id : list) {
			if (!existingIdsInCostTable.contains(id)) {
				costElementsNew.add(id);
			}
		}
		if (!costElementsNew.isEmpty()) {
			getNewCostElementsData(costElementsNew, costElementPriceUpdate);
		}
		return costElementPriceUpdate;
	}

	private List<CostElementPriceUpdate> getNewCostElementsData(List<Integer> list, List<CostElementPriceUpdate> costElementPriceUpdate) {
		Query queryMain = manager.createQuery(
				"select vd.costElementId, vd.costElementName from CostElementData vd where vd.costElementId IN :costElementIds");
		queryMain.setParameter("costElementIds", list);
		List<Object[]> results = queryMain.getResultList();
		results.stream().forEach((record) -> {
			CostElementPriceUpdate o = new CostElementPriceUpdate();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setCurrentPrice(BigDecimal.ZERO);
			costElementPriceUpdate.add(o);
		});
		return costElementPriceUpdate;
	}

	@Override
	public boolean updateStatusCostElementPriceMappings(int employeeId, String employeeName, int costElementId,
			String status) {
		Query query = manager.createQuery(
				"update VendorCostCenterCostElementMapping set mappingStatus = :status, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " id = :costElementId");
		query.setParameter("status", status);
		query.setParameter("updatedBy", getName(employeeName, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("costElementId", costElementId);
		query.executeUpdate();
		return true;
	}

	@Override
	public boolean addCostElementPriceMapping(Integer vendorId, Integer costCenterId, Integer costElementId,
			BigDecimal price, Integer employeeId, String employeeName) {
		VendorCostCenterCostElementMapping costElementVendor = new VendorCostCenterCostElementMapping();
		costElementVendor.setCostElementId(costElementId);
		costElementVendor.setVendorId(vendorId);
		costElementVendor.setCostCenterId(costCenterId);
		costElementVendor.setPrice(price);
		costElementVendor.setMappingStatus(AppConstants.ACTIVE);
		costElementVendor.setCreatedAt(AppUtils.getCurrentTimestamp());
		costElementVendor.setCreatedBy(getName(employeeName, employeeId));
		manager.persist(costElementVendor);
		return true;
		
	}

	@Override
	public boolean updateCostElementPriceMappings(Integer costElementMappingId, BigDecimal price, Integer employeeId,
			String employeeName) {

		Query query = manager.createQuery(
				"update VendorCostCenterCostElementMapping set price = :price, updatedBy = :updatedBy, updatedAt = :updatedAt  where "
						+ " id = :costElementMappingId ");
		query.setParameter("updatedBy", getName(employeeName, employeeId));
		query.setParameter("updatedAt", AppUtils.getCurrentTimestamp());
		query.setParameter("costElementMappingId", costElementMappingId);
		query.setParameter("price", price);
		query.executeUpdate();
		return true;
	
	}

	@Override
	public AdditionalDocumentsMaster addMasterDocument(AdditionalDocumentsMaster additionalDocumentsMaster) throws DataUpdationException{
		try {

			Query query = manager.createQuery("SELECT c.id FROM AdditionalDocumentsMaster c WHERE lower(c.documentName) = :documentName");
			query.setParameter("documentName",additionalDocumentsMaster.getDocumentName().toLowerCase());
			List<Integer> existingMasterDocument = query.getResultList();
			if(existingMasterDocument.isEmpty()){
				manager.persist(additionalDocumentsMaster);
				manager.flush();
				return additionalDocumentsMaster;
			} else {
				throw new DataUpdationException("Duplicate Entry");
			}
		}catch (Exception e){
			log.error("Error ",e);
			throw new DataUpdationException("Duplicate Entry");
		}
	}

	public List<IdCodeName> getCostElementDocumentMappings(Integer costElementId) {
		List<IdCodeName> list = new ArrayList<IdCodeName>();
		Query query = manager.createQuery(
				"select cdm.documentId,ad.documentName,ad.documentCode from CostElementDocumentMapping cdm, AdditionalDocumentsMaster ad where ad.id = cdm.documentId and cdm.costElementId = :costElementId");
		query.setParameter("costElementId", costElementId);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeName o = new IdCodeName();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			o.setCode((String) record[2]);
			list.add(o);
		});
		return list;
	}

	@Override
	public boolean addCostElementDocumentMappings(int employeeId, String employeeName, int costElementId,
												List<Integer> documentIds) {
		//activateCostElementVendorMapping(employeeId, employeeName, costElementId, vendorIds);
		Set<Integer> documents = getAllDocumentsForCostElement(costElementId);
		for (Integer documentId : documentIds) {
			if (!documents.contains(documentId)) {
				CostElementDocumentMapping costElementDocument = new CostElementDocumentMapping();
				costElementDocument.setCostElementId(costElementId);
				costElementDocument.setDocumentId(documentId);
				costElementDocument.setCreatedAt(AppUtils.getCurrentTimestamp());
				costElementDocument.setCreatedBy(getName(employeeName, employeeId));
				manager.persist(costElementDocument);
			}
		}
		manager.flush();
		return true;
	}

	private Set<Integer> getAllDocumentsForCostElement(int costElementId) {
		Set<Integer> documents = new HashSet<>();
		Query query = manager.createQuery("select documentId from CostElementDocumentMapping where costElementId = :costElementId");
		query.setParameter("costElementId", costElementId);
		List<Integer> list = query.getResultList();
		if (list != null) {
			documents.addAll(list);
		}
		return documents;
	}


	public List<AdditionalDocumentsMaster> getDocumentList(){
		Query query = manager.createQuery("From AdditionalDocumentsMaster A where status=:status");
		query.setParameter("status", AppConstants.ACTIVE);
		List<AdditionalDocumentsMaster> getDocumentList = query.getResultList();
		return getDocumentList;
	}

	public boolean updateCostElementDocumentMapping(int documentId, int costElementId){
		Query query = manager.createQuery("delete from CostElementDocumentMapping where documentId =:documentId and costElementId=:costElementId");
		query.setParameter("documentId",documentId);
		query.setParameter("costElementId",costElementId);
		query.executeUpdate();
		return true;
	}

	public List<AdditionalDocumentsMaster> getAdditionalDocs(){
		Query query = manager.createQuery("From AdditionalDocumentsMaster A");
		List<AdditionalDocumentsMaster> getAdditionalDocs = query.getResultList();
		return getAdditionalDocs;
	}

}
