/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * CategoryAttributeMappingData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CATEGORY_ATTRIBUTE_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = {"CATEGORY_ID",
    "ATTRIBUTE_ID"}))
public class CategoryAttributeMappingData implements java.io.Serializable {

    private Integer categoryAttributeMappingId;
    private CategoryDefinitionData categoryDefinition;
    private AttributeDefinitionData attributeDefinition;
    private String isMandatory;
    private int mappingOrder;
    private String isUsedInNaming;
    private String mappingStatus;

    public CategoryAttributeMappingData() {
    }

    public CategoryAttributeMappingData(CategoryDefinitionData categoryDefinition,
                                        AttributeDefinitionData attributeDefinition, String isMandatory, int mappingOrder, String isUsedInNaming,
                                        String mappingStatus) {
        this.categoryDefinition = categoryDefinition;
        this.attributeDefinition = attributeDefinition;
        this.isMandatory = isMandatory;
        this.mappingOrder = mappingOrder;
        this.isUsedInNaming = isUsedInNaming;
        this.mappingStatus = mappingStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CATEGORY_ATTRIBUTE_MAPPING_ID", unique = true, nullable = false)
    public Integer getCategoryAttributeMappingId() {
        return this.categoryAttributeMappingId;
    }

    public void setCategoryAttributeMappingId(Integer categoryAttributeMappingId) {
        this.categoryAttributeMappingId = categoryAttributeMappingId;
    }

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "CATEGORY_ID", nullable = false)
    public CategoryDefinitionData getCategoryDefinition() {
        return this.categoryDefinition;
    }

    public void setCategoryDefinition(CategoryDefinitionData categoryDefinition) {
        this.categoryDefinition = categoryDefinition;
    }

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "ATTRIBUTE_ID", nullable = false)
    public AttributeDefinitionData getAttributeDefinition() {
        return this.attributeDefinition;
    }

    public void setAttributeDefinition(AttributeDefinitionData attributeDefinition) {
        this.attributeDefinition = attributeDefinition;
    }

    @Column(name = "IS_MANDATORY", nullable = false, length = 1)
    public String getIsMandatory() {
        return this.isMandatory;
    }

    public void setIsMandatory(String isMandatory) {
        this.isMandatory = isMandatory;
    }

    @Column(name = "MAPPING_ORDER", nullable = false)
    public int getMappingOrder() {
        return this.mappingOrder;
    }

    public void setMappingOrder(int mappingOrder) {
        this.mappingOrder = mappingOrder;
    }

    @Column(name = "IS_USED_IN_NAMING", nullable = false, length = 1)
    public String getIsUsedInNaming() {
        return this.isUsedInNaming;
    }

    public void setIsUsedInNaming(String isUsedInNaming) {
        this.isUsedInNaming = isUsedInNaming;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 15)
    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

}
