package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "ASSET_TRANSFER_MAPPING")
public class AssetTransferMappingData {

    private Integer assetTransferMappingId;

    private Integer assetId;

    private Integer unitId;

    private String assetStatus;

    private Integer ownerId;

    private Date startDate;

    private Date endDate;

    private String isWriteOff;


    public AssetTransferMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ASSET_TRANSFER_MAPPING_ID", unique = true, nullable = false)
    public Integer getAssetTransferMappingId() {
        return assetTransferMappingId;
    }

    public void setAssetTransferMappingId(Integer assetTransferMappingId) {
        this.assetTransferMappingId = assetTransferMappingId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "ASSET_STATUS", nullable = false)
    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    @Column(name = "OWNER_ID", nullable = false)
    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "START_DATE", length = 19)
    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "END_DATE", length = 19)
    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Column(name = "IS_WRITE_OFF", length = 1)
    public String getIsWriteOff() {
        return isWriteOff;
    }

    public void setIsWriteOff(String isWriteOff) {
        this.isWriteOff = isWriteOff;
    }
}
