package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Getter
@Setter
@Table(name = "OUTWARD_REGISTER")
public class OutwardRegisterData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DATE_TIME")
    private Date dateTime;

    @Column(name = "CHALLAN_NUMBER")
    private String challanNumber;

    @Column(name = "ADDRESS_OF_BUYER")
    private String addressOfBuyer;

    @Column(name = "DETAILS_OF_ARTICLE")
    private String detailsOfArticle;

    @Column(name = "QUANTITY")
    private BigDecimal quantity;

    @Column(name = "AMOUNT")
    private BigDecimal amount;

    @Column(name = "NAME_OF_DELIVERER")
    private String nameOfDeliverer;

    @Column(name = "VEHICLE_NUMBER_TYPE")
    private String vehicleNumberType;

    @Column(name = "SIGNATURE_OF_SECURITY")
    private String signatureOfSecurity;

    @Column(name = "REMARKS")
    private String remarks;

    @Column(name = "INVOICE_ID")
    private Integer invoiceId;

    @Column(name = "BUSINESS_TYPE")
    private String businessType;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "SUBMISSION_DATE_TIME")
    private Date submissionDateTime;

}
