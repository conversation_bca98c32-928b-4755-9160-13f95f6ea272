package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "CAPEX_BUDGET_DETAIL")
public class CapexBudgetDetailData {

	private Integer id;
	private Integer unitId;
	private String budgetType;
	private Integer departmentId;
	private String departmentName;
	private BigDecimal originalAmount;
	private BigDecimal budgetAmount;
	private BigDecimal runningAmount;
	private BigDecimal receivingAmount;
	private BigDecimal remainingAmount;
	private BigDecimal paidAmount;
	private BigDecimal initialAmount;
	private String status;
	private Integer capexRequestId;
	private Integer capexAuditId;
	private Integer capexAuditBudgetId;
	private BigDecimal extraReceiving;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "UNIT_ID")
	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	@Column(name = "BUDGET_TYPE")
	public String getBudgetType() {
		return budgetType;
	}

	public void setBudgetType(String budgetType) {
		this.budgetType = budgetType;
	}

	@Column(name = "DEPARTMENT_ID")
	public Integer getDepartmentId() {
		return departmentId;
	}

	public void setDepartmentId(Integer departmentId) {
		this.departmentId = departmentId;
	}

	@Column(name = "DEPARTMENT_NAME")
	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	@Column(name = "BUDGET_AMOUNT")
	public BigDecimal getBudgetAmount() {
		return budgetAmount;
	}

	public void setBudgetAmount(BigDecimal budgetAmount) {
		this.budgetAmount = budgetAmount;
	}

	@Column(name = "RUNNING_AMOUNT")
	public BigDecimal getRunningAmount() {
		return runningAmount;
	}

	public void setRunningAmount(BigDecimal runningAmount) {
		this.runningAmount = runningAmount;
	}

	@Column(name = "RECEIVED_AMOUNT")
	public BigDecimal getReceivingAmount() {
		return receivingAmount;
	}

	public void setReceivingAmount(BigDecimal receivingAmount) {
		this.receivingAmount = receivingAmount;
	}

	@Column(name = "REMAINING_AMOUNT")
	public BigDecimal getRemainingAmount() {
		return remainingAmount;
	}

	public void setRemainingAmount(BigDecimal remainingAmount) {
		this.remainingAmount = remainingAmount;
	}

	@Column(name = "STATUS")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "ORIGINAL_AMOUNT")
	public BigDecimal getOriginalAmount() {
		return originalAmount;
	}

	public void setOriginalAmount(BigDecimal originalAmount) {
		this.originalAmount = originalAmount;
	}

	@Column(name = "PAID_AMOUNT")
	public BigDecimal getPaidAmount() {
		return paidAmount;
	}

	public void setPaidAmount(BigDecimal paidAmount) {
		this.paidAmount = paidAmount;
	}

	@Column(name = "INITIAL_AMOUNT")
	public BigDecimal getInitialAmount() {
		return initialAmount;
	}

	public void setInitialAmount(BigDecimal initialAmount) {
		this.initialAmount = initialAmount;
	}

	@Column(name = "CAPEX_REQUEST_ID")
	public Integer getCapexRequestId() {
		return capexRequestId;
	}

	public void setCapexRequestId(Integer capexRequestId) {
		this.capexRequestId = capexRequestId;
	}

	@Column(name = "CAPEX_AUDIT_ID")
	public Integer getCapexAuditId() {
		return capexAuditId;
	}

	public void setCapexAuditId(Integer capexAuditId) {
		this.capexAuditId = capexAuditId;
	}

	@Column(name = "CAPEX_BUDGET_AUDIT_ID")
	public Integer getCapexAuditBudgetId() {
		return capexAuditBudgetId;
	}

	public void setCapexAuditBudgetId(Integer capexAuditBudgetId) {
		this.capexAuditBudgetId = capexAuditBudgetId;
	}

	@Column(name = "EXTRA_RECEIVING")
	public BigDecimal getExtraReceiving() {
		return extraReceiving;
	}

	public void setExtraReceiving(BigDecimal extraReceiving) {
		this.extraReceiving = extraReceiving;
	}
}
