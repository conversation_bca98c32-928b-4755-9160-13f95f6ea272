package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.data.dao.ReferenceOrderManagementDao;
import com.stpl.tech.scm.data.model.ExceptionDateEntry;
import com.stpl.tech.scm.data.model.ForecastReportResponse;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.ReferenceOrderData;
import com.stpl.tech.scm.data.model.RegularOrderUnitBrandData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.domain.model.EstimationShortData;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by Rahul Singh on 13-06-2016.
 */

@Repository
public class ReferenceOrderManagementDaoImpl extends SCMAbstractDaoImpl implements ReferenceOrderManagementDao {


    private static final Logger LOG = LoggerFactory.getLogger(ReferenceOrderManagementDaoImpl.class);

    @Override
    public List<ReferenceOrderData> getReferenceOrders(Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer referenceOrderId) {
        String queryString = "FROM ReferenceOrderData r WHERE ";
        if (referenceOrderId != null) {
            queryString += "r.id = :referenceOrderId ";
        } else {
            queryString += "r.generationTime >= :startDate and r.generationTime < :endDate ";
        }
        if (requestingUnitId != null) {
            queryString += "and r.requestUnitId = :requestingUnitId ";
        }
        if (status != null) {
            queryString += "and r.status = :status ";
        }
        queryString += "order by r.generationTime";
        Query query = manager.createQuery(queryString);
        if (referenceOrderId != null) {
            query.setParameter("referenceOrderId", referenceOrderId);
        } else {
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
        }
        if (requestingUnitId != null) {
            query.setParameter("requestingUnitId", requestingUnitId);
        }
        if (status != null) {
            query.setParameter("status", status.value());
        }
        return query.getResultList();
    }

    @Override
    public List<IdCodeName> getExpiryProduct() {

        Integer categoryId = 4;
        Integer dayOne = 1;
        Integer dayTwo = 4;
        String fulfilmentType = "KITCHEN";
        String varianceType = "ZERO_VARIANCE";
        String type = "Y";
        String status = "ACTIVE";
        Query query = manager.createQuery("FROM ProductDefinitionData P where categoryDefinition.categoryId = :categoryId and shelfLifeInDays >= :dayOne and " +
                "shelfLifeInDays <= :dayTwo and varianceType = :varianceType and availableForCafe = :type and availableForCafeInventory= :type " +
                "and productStatus =:status");
        query.setParameter("categoryId", categoryId);
        query.setParameter("dayOne", dayOne);
        query.setParameter("dayTwo", dayTwo);
        query.setParameter("type", type);
        query.setParameter("varianceType", varianceType);
        query.setParameter("status", status);
        List<ProductDefinitionData> results = query.getResultList();
        List<IdCodeName> finalResult = new ArrayList<>();
        for (ProductDefinitionData result : results) {
            IdCodeName obj = new IdCodeName(result.getProductId(), result.getProductName(), result.getProductDescription());
            finalResult.add(obj);
        }
        return finalResult;
    }

    @Override
    public List<ExceptionDateEntry> getExceptionDate(Date businessDate) {
        Query query = manager.createQuery("FROM ExceptionDateEntry E Where businessDate = :businessDate");
        query.setParameter("businessDate", businessDate);
        return query.getResultList();
    }

    @Override
    public boolean getExceptionDateWithUnitId(Date businessDate, Integer unitId) {
        Query query = manager.createQuery("FROM ExceptionDateEntry E Where businessDate = :businessDate and unitId= :unitId and status=:status");
        query.setParameter("businessDate", businessDate);
        query.setParameter("unitId", unitId);
        query.setParameter("status", AppConstants.ACTIVE);
        if (query.getResultList().size() != 0) {
            return true;
        }
        return false;
    }

    @Override
    public ExceptionDateEntry deleteEntry(ExceptionDateEntry entry) {
        Integer unitId = entry.getUnitId();
        Date businessDate = entry.getBusinessDate();
        Query query = manager.createQuery("DELETE FROM ExceptionDateEntry WHERE unitId = :unitId and businessDate = :businessDate ");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDate", businessDate);
        query.executeUpdate();
        return null;
    }

    @Override
    public List<RequestOrderData> getRequestOrderFromReferenceOrder(int referenceOrderId) {
        Query query = manager.createQuery("SELECT r FROM RequestOrderData r WHERE r.referenceOrderData.id = :referenceOrderId");
        query.setParameter("referenceOrderId", referenceOrderId);
        return query.getResultList();
    }


    public int createEstimationRequestQuery(int brandId, int unitId, Date targetDate, BigDecimal targetSale, String dates, String orderType, BigDecimal dineInSale, BigDecimal deliverySale) {
        Query query = manager.createNativeQuery("CALL ESTIMATE_QUERY_REQUEST_PROC(:brandId,:unitId,:targetDate,:targetSale,:dates,:orderType,:generationDate,:dineInSale,:deliverySale)");
        query.setParameter("brandId", brandId);
        query.setParameter("unitId", unitId);
        query.setParameter("targetDate", AppUtils.getSQLFormattedDate(targetDate));
//        query.setParameter("estimationDateCount", estimationDateCount);
        query.setParameter("targetSale", targetSale);
        query.setParameter("dineInSale", dineInSale);
        query.setParameter("deliverySale", deliverySale);
        query.setParameter("dates", dates);
        query.setParameter("orderType", orderType);
        query.setParameter("generationDate", AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate()));
        int data = 0;

        try {
            data = (Integer) query.getSingleResult();
            return data;

        } catch (NoResultException | NonUniqueResultException ex) {
            LOG.error("Error while getting sales  percentage Data");
        }
        return data;

    }

    @Override
    public void getEstimationData(int brandId, int unitId, Date targetDate, BigDecimal targetSale, BigDecimal dineInSale, BigDecimal deliverySale, String dates, Integer categoryBuffer, Integer buffer, String productIds, String orderType) {

        int requestId = createEstimationRequestQuery(brandId, unitId, targetDate, targetSale, dates, orderType, dineInSale, deliverySale);

        Query query = manager.createNativeQuery("CALL PROC_ESTIMATE_REQUEST_DATA(:brandId,:unitId,:targetDate,:targetSale,:dineInSale,:deliverySale,:dates,:categoryBuffer,:buffer,:productIds,:requestId)");
        query.setParameter("brandId", brandId);
        query.setParameter("unitId", unitId);
        query.setParameter("targetDate", AppUtils.getSQLFormattedDate(targetDate));
//        query.setParameter("estimationDateCount", estimationDateCount);
        query.setParameter("targetSale", targetSale);
        query.setParameter("dineInSale", dineInSale);
        query.setParameter("deliverySale", deliverySale);
        query.setParameter("dates", dates);
        query.setParameter("categoryBuffer", categoryBuffer);
        query.setParameter("buffer", buffer);
        query.setParameter("productIds", productIds);
//        query.setParameter("productCount", productCount);
        query.setParameter("requestId", requestId);
        List<Object[]> list = query.getResultList();


//        List<EstimationShortData> estimateShortDataList = new ArrayList<>();
//        if(list!=null) {
//            for (Object[] obj : list) {
//                EstimationShortData estimationShortData = new EstimationShortData(
//                        (Integer) obj[0], (String) obj[1], (Integer) obj[2], (String) obj[3], Integer.parseInt(((BigInteger) obj[4]).toString()), (Integer) obj[5],(BigDecimal) obj[6]);
//
//                estimateShortDataList.add(estimationShortData);
//
//            }
//        }
//        return estimateShortDataList;
    }


    @Override
    public List<Integer> getRequestIdsForEstimation(int unitId, List<Date> targetDate) {
        Query query = manager.createQuery("SELECT r.id FROM EstimateQueryRequestData r WHERE r.unitId = :unitId AND r.targetDate IN (:targetDate) AND r.status =:status");
        query.setParameter("unitId", unitId);
        query.setParameter("targetDate", targetDate);
        query.setParameter("status", AppConstants.ACTIVE);
        return (List<Integer>) query.getResultList();

    }

    @Override
    public List<EstimationShortData> getEstimationQuantity(int unitId, String requestIds) {
        Query query = manager.createNativeQuery("CALL PROC_ESTIMATE_AGGREGATE_DATA(:requestIds,:unitId)");
        query.setParameter("requestIds", requestIds);
        query.setParameter("unitId", unitId);
        List<Object[]> list = query.getResultList();


        List<EstimationShortData> estimateShortDataList = new ArrayList<>();
        if (list != null) {
            for (Object[] obj : list) {
                EstimationShortData estimationShortData = new EstimationShortData(
                        (Integer) obj[0], (String) obj[1], (Integer) obj[2], (String) obj[3], Integer.parseInt(((BigDecimal) obj[4]).toString()), (Integer) obj[5], (BigDecimal) obj[6]);

                estimateShortDataList.add(estimationShortData);

            }
        }
        return estimateShortDataList;

    }

    @Override
    public BigDecimal getSalesPercentage(int unitId, int brandId, String businessDate, String saleType) {
        Query query = manager.createNativeQuery("SELECT SUM(r.NET_SALES) FROM DAY_CLOSE_SALES_DATA r WHERE r.UNIT_ID = :unitId AND FIND_IN_SET(r.BUSINESS_DATE,:businessDate)>0 AND r.BRAND_ID =:brandId AND r.SALE_TYPE=:saleType");
        query.setParameter("unitId", unitId);
        query.setParameter("businessDate", businessDate);
        query.setParameter("brandId", brandId);
        query.setParameter("saleType", saleType);
        BigDecimal data = BigDecimal.ZERO;
        try {
            data = (BigDecimal) query.getSingleResult();
            return data;
        } catch (NoResultException | NonUniqueResultException e) {
            LOG.error("Error while getting sales  percentage Data");
        }

        return data;
    }

    @Override
    public void cloneDayCloseDataForUnit(int newUnitId, int cloningUnitId,Date businessDate) {
       try {
           Query query = manager.createNativeQuery("CALL CLONE_UNIT_DAY_CLOSE_ESTIMATE_DATA(:newUnitId,:cloningUnitId,:businessDate)");
           query.setParameter("newUnitId", newUnitId);
           query.setParameter("cloningUnitId", cloningUnitId);
           query.setParameter("businessDate", businessDate);
           query.executeUpdate();
       }catch (Exception e){
           LOG.error("Error while cloning regular order data for unit: {}",newUnitId,e);
       }
    }

    public int checkIfDataExistForRegularOrderingForCafe(int unitId) {
        Query query = manager.createNativeQuery("SELECT count(*) FROM DAY_CLOSE_SALES_DATA r WHERE r.UNIT_ID = :unitId");
        query.setParameter("unitId", unitId);
        int data = 0;
        try {
            data = (int) query.getSingleResult();
            return data;
        } catch (Exception e) {
            LOG.error("Error while checking day closes sales data for cafe: {}",e,unitId);
        }
        return data;
    }

    @Override
    public List<RegularOrderUnitBrandData> getUnitOrderingSchedule(Integer unitId,Boolean isFunctional) {
        List<RegularOrderUnitBrandData> result = new ArrayList<>();
        try {
            String queryString = "FROM RegularOrderUnitBrandData r WHERE r.unitId =:unitId";
            if (isFunctional) {
                queryString = queryString + " AND r.isFunctional =:functional";
            }
            Query query = manager.createQuery(queryString);
            query.setParameter("unitId",unitId);
            if (isFunctional) {
                query.setParameter("functional",AppConstants.YES);
            }
            result = query.getResultList();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding Unit Ordering Schedule ",e);
        }
        return result;
    }

    @Override
    public List<RegularOrderUnitBrandData> getAllOrderingSchedules() {
        List<RegularOrderUnitBrandData> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("FROM RegularOrderUnitBrandData r");
            result = query.getResultList();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding All Unit Ordering Schedule ",e);
        }
        return result;
    }

    @Override
    public RegularOrderUnitBrandData getMaxCap(int unitId, int brandId) {
        try {
            Query query = manager.createQuery("FROM RegularOrderUnitBrandData r WHERE r.unitId=:unitId AND r.brandId=:brandId");
            query.setParameter("unitId",unitId).setParameter("brandId",brandId);
            return (RegularOrderUnitBrandData) query.getSingleResult();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding max cap of unit ::: ",e);
            return null;
        }
    }

    @Override
    public List<RequestOrderData> getLastWeekAdhocOrders(List<Date> dates, Integer unitId) {
        List<RequestOrderData> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("FROM RequestOrderData r WHERE r.requestUnitId =:requestUnitId AND r.status <>:status " +
                    "AND r.fulfillmentDate >=:startTime AND r.generationTime <=:endTime AND " +
                    "r.assetOrder =:assetOrder AND r.referenceOrderData IS NULL ORDER BY r.id DESC");
            query.setParameter("requestUnitId",unitId).setParameter("status",AppConstants.CANCELLED).setParameter("startTime",dates.get(0))
                    .setParameter("endTime",dates.get(dates.size() - 1)).setParameter("assetOrder",AppConstants.NO);
            result = (List<RequestOrderData>) query.getResultList();
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While finding Last Week Adhoc Orders ::: ",e);
        }
        return result;
    }

    @Override
    public List<String> getFountain9Units() {
        List<String> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT r.cafeId FROM ForecastReportResponse r GROUP BY r.cafeId");
            result = query.getResultList();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding Fountain 9 units ",e);
        }
        return result;
    }

    @Override
    public Boolean checkFountain9Enabled(Integer unitId) {
        Boolean result = false;
        String cafeId = unitId+"%";
        try {
            Query query = manager.createQuery("FROM ForecastReportResponse r WHERE r.cafeId LIKE :cafeId ORDER BY 1 DESC");
            query.setParameter("cafeId",cafeId).setMaxResults(1);
            List<ForecastReportResponse> list = query.getResultList();
            result = !list.isEmpty();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While checking Fountain 9 unit enabled or not.. :: ",e);
        }
        return result;
    }

    @Override
    public boolean checkForOrderingSchedule(Integer unitId) {
        try {
            Query query = manager.createQuery("FROM RegularOrderUnitBrandData r WHERE r.unitId=:unitId");
            query.setParameter("unitId",unitId);
            List<RegularOrderUnitBrandData> result = query.getResultList();
            return result.isEmpty();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While checking For Ordering Schedule.. :: ",e);
        }
        return false;
    }

    @Override
    public void updateForecastData(Integer id, String refreshDate, Integer unitId) {
        String cafeId = unitId + "%";
        try {
            Query query = manager.createNativeQuery("UPDATE REGULAR_ORDERING_FORECAST_DATA r SET r.REFERENCE_ORDER_ID =:id " +
                    "WHERE r.CAFE_ID LIKE :cafeId AND r.REFRESH_DATE =:refreshDate");
            query.setParameter("id",id).setParameter("cafeId",cafeId).setParameter("refreshDate",refreshDate);
            query.executeUpdate();
            Query query2 = manager.createNativeQuery("UPDATE REGULAR_ORDERING_SCM_FORECAST_DATA r SET r.REFERENCE_ORDER_ID =:id " +
                    "WHERE r.CAFE_ID LIKE :cafeId AND r.REFRESH_DATE =:refreshDate");
            query2.setParameter("id",id).setParameter("cafeId",cafeId).setParameter("refreshDate",refreshDate);
            query2.executeUpdate();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While Updating the reference Order Id in Forecast report data :: ",e);
        }
    }

    @Override
    public List<RequestOrderData> checkForF9Orders(Integer requestingUnitId, Integer fulfilmentUnitId, Date fulfilmentDate) {
        List<RequestOrderData> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("FROM RequestOrderData r WHERE r.requestUnitId =:requestingUnitId " +
                    "AND r.fulfillmentUnitId =:fulfilmentUnitId AND r.fulfillmentDate =:fulfillmentDate AND r.referenceOrderData.refOrderSource =:refOrderSource" +
                    " AND r.status =:status AND r.childRO IS NULL");
            query.setParameter("requestingUnitId",requestingUnitId).setParameter("fulfilmentUnitId",fulfilmentUnitId).
                    setParameter("fulfillmentDate",fulfilmentDate).setParameter("refOrderSource",AppConstants.F9_ORDERING)
                    .setParameter("status",SCMOrderStatus.CANCELLED.value());
            result = (List<RequestOrderData>) query.getResultList();
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While Checking for F9 Orders :: ",e);
        }
        return result;
    }

    @Override
    public List<FulfillmentUnitMappingData> getFulfilmentUnits(Integer requestingUnitId) {
        List<FulfillmentUnitMappingData> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("FROM FulfillmentUnitMappingData f WHERE f.requestingUnitId =:requestingUnitId");
            query.setParameter("requestingUnitId",requestingUnitId);
            result = (List<FulfillmentUnitMappingData>) query.getResultList();
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While getting Fulfilment Unit Mappings :: ",e);
        }
        return result;
    }

    @Override
    public List<Integer> getUnitsWithSchedules() {
        List<Integer> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("SELECT r.unitId FROM RegularOrderUnitBrandData r GROUP BY r.unitId");
            result = (List<Integer>) query.getResultList();
        } catch (Exception e) {
            LOG.error("Exception Occurred while getting units with schedules :: ",e);
        }
        return result;
    }

    @Override
    public SCMDayCloseEventData findUnsuccessfulOrdering(Integer unitId) {
        try {
            Query query = manager.createQuery("FROM SCMDayCloseEventData s WHERE s.unitId =:unitId AND s.orderingSuccess =:orderingSuccess ORDER BY 1 DESC");
            query.setParameter("unitId",unitId).setParameter("orderingSuccess",AppConstants.NO);
            query.setMaxResults(1);
            return (SCMDayCloseEventData) query.getSingleResult();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding unsuccessful Ordering events ");
            return null;
        }
    }

    @Override
    public List<Object[]> getNewExpiryProducts(Integer unitId) {
        try {
            Query query = manager.createNativeQuery("SELECT cd.KEY_ID,pd.PRODUCT_NAME, EXPIRY_DATE, SUM(cd.QUANTITY) QUANTITY " +
                    "FROM COST_DETAIL_DATA cd,PRODUCT_DEFINITION pd " +
                    "WHERE UNIT_ID =:unitId AND KEY_TYPE = 'PRODUCT' AND cd.KEY_ID = pd.PRODUCT_ID" +
                    " AND pd.CATEGORY_ID = 4" +
                    " AND pd.SHELF_LIFE_IN_DAYS >= 1" +
                    " AND SHELF_LIFE_IN_DAYS <= 4" +
                    " AND VARIANCE_TYPE = 'ZERO_VARIANCE'" +
                    " AND AVAILABLE_AT_CAFE = 'Y'" +
                    " AND AVAILABLE_FOR_CAFE_INVENTORY = 'Y'" +
                    " AND PRODUCT_STATUS = 'ACTIVE'" +
                    " AND cd.QUANTITY > 0 AND cd.EXPIRY_DATE > DATE_ADD(current_date() , INTERVAL -1 DAY)" +
                    " AND cd.EXPIRY_DATE < DATE_ADD(current_date() , INTERVAL pd.SHELF_LIFE_IN_DAYS+7 DAY)" +
                    " GROUP BY cd.KEY_ID , EXPIRY_DATE" +
                    " ORDER BY pd.PRODUCT_NAME");
            query.setParameter("unitId",unitId);
            return (List<Object[]>) query.getResultList();
        }
        catch (Exception e) {
            LOG.error("Error Occurred While finding Expiry Products ",e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ForecastReportResponse> getForecastResponses(Date startDate, Date endDate) {
        List<ForecastReportResponse> result = new ArrayList<>();
        try {
            Query query = manager.createQuery("FROM ForecastReportResponse f WHERE f.date >=:startDate AND f.date <=:endDate");
            query.setParameter("startDate",startDate).setParameter("endDate",endDate);
            result = (List<ForecastReportResponse>) query.getResultList();
            return result;
        }
        catch (Exception e) {
            LOG.error("Exception Occurred While getting forecast data ::: ",e);
        }
        return result;
    }
}
