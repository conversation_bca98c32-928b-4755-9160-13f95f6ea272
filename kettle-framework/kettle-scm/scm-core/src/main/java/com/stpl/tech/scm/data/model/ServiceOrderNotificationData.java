package com.stpl.tech.scm.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-05-2017.
 */

@Entity
@Table(name = "SERVICE_ORDER_NOTIFICATION")
public class ServiceOrderNotificationData {


    private int notificationId;
    private String contact;
    private String message;
    private String notificationCarrier; //SMS?EMAIL
    private String vendorNotificationType;
    private ServiceOrderData serviceOrderId;
    private String serviceClient;
    private String notificationSent;
    private Date notificationTime;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "NOTIFICATION_ID", nullable = false)
    public int getNotificationId() {
        return notificationId;
    }

    public void setNotificationId(int notificationId) {
        this.notificationId = notificationId;
    }

    @Column(name = "CONTACT", nullable = true)
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Column(name = "MESSAGE", nullable = true)
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Column(name = "SERVICE_CLIENT", nullable = true)
    public String getServiceClient() {
        return serviceClient;
    }

    public void setServiceClient(String serviceClient) {
        this.serviceClient = serviceClient;
    }

    @Column(name = "NOTIFICATION_CARRIER_TYPE", nullable = true)
    public String getNotificationCarrier() {
        return notificationCarrier;
    }

    public void setNotificationCarrier(String notificationCarrier) {
        this.notificationCarrier = notificationCarrier;
    }

    @Column(name = "NOTIFICATION_TYPE", nullable = true)
    public String getVendorNotificationType() {
        return vendorNotificationType;
    }

    public void setVendorNotificationType(String vendorNotificationType) {
        this.vendorNotificationType = vendorNotificationType;
    }


    @Column(name = "NOTIFICATION_SENT", nullable = true)
    public String getNotificationSent() {
        return notificationSent;
    }

    public void setNotificationSent(String notificationSent) {
        this.notificationSent = notificationSent;
    }

    @Column(name = "NOTIFICATION_TIME", nullable = true)
    public Date getNotificationTime() {
        return notificationTime;
    }

    public void setNotificationTime(Date notificationTime) {
        this.notificationTime = notificationTime;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "Service_ORDER_ID", nullable = false)
    public ServiceOrderData getServiceOrderId() {
        return serviceOrderId;
    }

    public void setServiceOrderId(ServiceOrderData purchaseOrderId) {
        this.serviceOrderId = purchaseOrderId;
    }
}
