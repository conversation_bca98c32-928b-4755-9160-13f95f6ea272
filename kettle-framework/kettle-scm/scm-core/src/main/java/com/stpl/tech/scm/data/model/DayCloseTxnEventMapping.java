package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 12-06-2017.
 */
@Entity
@Table(name = "DAY_CLOSURE_TXN_EVENT_MAPPING")
public class DayCloseTxnEventMapping {

    private Integer eventMappingId;
    private SCMDayCloseEventData closureEvent;
    private Integer txnId;
    private String eventType;
    private String externalTxn = "N";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_MAPPING_ID", nullable = false, unique = true)
    public Integer getEventMappingId() {
        return eventMappingId;
    }

    public void setEventMappingId(Integer eventMappingId) {
        this.eventMappingId = eventMappingId;
    }

    @ManyToOne
    @JoinColumn(name = "CLOSURE_ID", nullable = false)
    public SCMDayCloseEventData getClosureEvent() {
        return closureEvent;
    }

    public void setClosureEvent(SCMDayCloseEventData closureEvent) {
        this.closureEvent = closureEvent;
    }

    @Column(name = "TRANSACTION_EVENT_ID")
    public Integer getTxnId() {
        return txnId;
    }

    public void setTxnId(Integer txnId) {
        this.txnId = txnId;
    }

    @Column(name = "EVENT_TYPE")
    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    @Column(name = "IS_EXTERNAL")
    public String getExternalTxn() {
        return externalTxn;
    }

    public void setExternalTxn(String externalTxn) {
        this.externalTxn = externalTxn;
    }
}
