package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "VENDOR_CONTRACT_INFO")
public class VendorContractInfo {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "VENDOR_CONTRACT_ID", unique = true, nullable = false)
    private Integer vendorContractId;
    @Column(name = "VENDOR_ID", nullable = false)
    private Integer vendorId;
    @Temporal(TemporalType.DATE)
    @Column(name = "START_DATE", nullable = false)
    private Date startDate;
    @Temporal(TemporalType.DATE)
    @Column(name = "END_DATE")
    private Date endDate;
    @Column(name = "UNSIGNED_DOCUMENT_ID")
    private Integer unsignedDocumentId;
    @Column(name = "VENDOR_SIGNED_DOCUMENT_ID")
    private Integer signedDocumentId;
    @Column(name = "AUTH_SIGNED_DOCUMENT_ID")
    private Integer authSignedDocumentId;
    @Column(name = "RECORD_STATUS", nullable = false)
    private String recordStatus;
    @Column(name = "TEMPLATE_ID")
    private Integer templateId;
    @Column(name = "CREATED_BY", nullable = false)
    private String createdBy;
    @Column(name = "CONTRACT_REQUESTED_BY", nullable = false)
    private String contractRequestedBy;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false)
    private Date creationTime;
    @Column(name = "IS_MAIL_TRIGGERED")
    private String isMailTriggered;
    @Column(name = "OTP_VERIFIED")
    private String isOtpVerified;
    @Column(name = "VENDOR_IP_ADDRESS")
    private String ipAddress;
    @Column(name = "AUTH_IP_ADDRESS")
    private String authIpAddress;
    @Column(name = "VENDOR_DIGITAL_SIGN_ID")
    private Integer digitalSignID;
    @Column(name = "AUTH_DIGITAL_SIGN_ID")
    private Integer authDigitalSignID;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MAIL_TIME")
    private Date mailTime;
    @Column(name = "VENDOR_USER_NAME")
    private String vendorUserName;
    @Column(name = "VENDOR_USER_DESIGNATION")
    private String vendorUserDesignation;

}
