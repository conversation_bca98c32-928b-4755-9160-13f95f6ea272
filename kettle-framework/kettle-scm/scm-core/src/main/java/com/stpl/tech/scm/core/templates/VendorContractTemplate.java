package com.stpl.tech.scm.core.templates;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.domain.model.VendorContractItemVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class VendorContractTemplate extends AbstractVelocityTemplate {
    private VendorContractInfo vendorContractInfo;
    private List<VendorContractItemVO> vendorContractItem;
    private VendorDetail vendorDetail;
    private EmployeeBasicDetail employeeBasicDetail;
    private String basePath;
    private String templateName;
    private String vendorSignature;
    private String authSignedSignature;
    private String signedIPSignature;
    private String authSignedIPSignature;
    private String vendorContractString;

    @Override
    public String getTemplatePath() {
        return "templates/"+templateName+".html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + vendorContractInfo.getVendorContractId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendorContractInfo", vendorContractInfo);
        stringObjectMap.put("vendorContractItem", vendorContractItem);
        stringObjectMap.put("vendorDetail", vendorDetail);
        stringObjectMap.put("startDate", AppUtils.getSQLFormattedDate(vendorContractInfo.getStartDate()));
        stringObjectMap.put("endDate", AppUtils.getSQLFormattedDate(vendorContractInfo.getEndDate()));
        stringObjectMap.put("currentBizDate", AppUtils.getSQLFormattedDate(AppUtils.getBusinessDate()));
        stringObjectMap.put("basePath", basePath);
        stringObjectMap.put("vendorSignature", vendorSignature);
        stringObjectMap.put("authVendorSignature", authSignedSignature);
        stringObjectMap.put("employeeBasicDetail", employeeBasicDetail);
        stringObjectMap.put("signedIPSignature", signedIPSignature);
        stringObjectMap.put("authSignedIPSignature", authSignedIPSignature);
        stringObjectMap.put("vendorContractString", vendorContractString.isEmpty() ? null : vendorContractString);
        return stringObjectMap;
    }
}
