/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.stpl.tech.master.tax.model.TaxationDetailDao;


/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "TRANSFER_ORDER_ITEM_INVOICE_TAX_DETAIL")
public class TransferOrderItemInvoiceTaxDetail implements java.io.Serializable, TaxationDetailDao {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6909018841091451616L;
	private Integer orderItemInvoiceTaxDetailId;
	private TransferOrderItemInvoice orderItemInvoice;
	private String taxType;
	private String taxCode;
	private BigDecimal taxPercentage;
	private BigDecimal totalTax;
	private BigDecimal totalAmount;
	private BigDecimal taxableAmount;

	public TransferOrderItemInvoiceTaxDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ITEM_INVOICE_TAX_DETAIL_ID", unique = true, nullable = false)
	public Integer getOrderItemInvoiceTaxDetailId() {
		return this.orderItemInvoiceTaxDetailId;
	}

	public void setOrderItemInvoiceTaxDetailId(Integer orderItemTaxDetailId) {
		this.orderItemInvoiceTaxDetailId = orderItemTaxDetailId;
	}

	@Column(name = "TAX_CODE", nullable = false, length = 20)
	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@Column(name = "TAX_TYPE", nullable = false, length = 20)
	public String getTaxType() {
		return taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ITEM_INVOICE_ID", nullable = true)
	public TransferOrderItemInvoice getOrderItemInvoice() {
		return orderItemInvoice;
	}

	public void setOrderItemInvoice(TransferOrderItemInvoice orderItemInvoice) {
		this.orderItemInvoice = orderItemInvoice;
	}

	@Column(name = "TAX_PERCENTAGE", precision = 10)
	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}

	@Column(name = "TOTAL_AMOUNT", precision = 10)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}


	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}


	@Column(name = "TAXABLE_AMOUNT", precision = 10)
	public BigDecimal getTaxableAmount() {
		return taxableAmount;
	}


	public void setTaxableAmount(BigDecimal taxableAmount) {
		this.taxableAmount = taxableAmount;
	}

}