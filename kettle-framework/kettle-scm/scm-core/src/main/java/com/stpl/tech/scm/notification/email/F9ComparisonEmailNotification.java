package com.stpl.tech.scm.notification.email;

import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.F9ComparisonEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Objects;

public class F9ComparisonEmailNotification extends EmailNotification {

    private F9ComparisonEmailNotificationTemplate notificationTemplate;
    private EnvType envType;
    private List<String> emails;

    public F9ComparisonEmailNotification() {
    }

    public F9ComparisonEmailNotification(F9ComparisonEmailNotificationTemplate notificationTemplate, EnvType envType, List<String> emails) {
        this.notificationTemplate = notificationTemplate;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        emails.add("<EMAIL>");
        emails.add("<EMAIL>");
        emails.add("<EMAIL>");
        emails.add("<EMAIL>");
        String[] simpleArray = new String[emails.size()];
        return SCMUtil.isDev(envType) ? new String[]{"<EMAIL>"} : emails.toArray(simpleArray);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Sales Vs Fountain9 for unit : " + notificationTemplate.getUnit().getName() + " from : " + notificationTemplate.getDates().get(0) + " To : "
                + notificationTemplate.getDates().get(notificationTemplate.getDates().size() - 1);
        if (SCMUtil.isDev(envType)) {
            subject = "Dev " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return notificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
