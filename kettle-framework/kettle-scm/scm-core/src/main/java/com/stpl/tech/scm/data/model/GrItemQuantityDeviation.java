package com.stpl.tech.scm.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "GR_ITEM_QUANTITY_DEVIATION")
public class GrItemQuantityDeviation {
    private Integer grItemDeviationId;
    private String deviationReason;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GRITEM_QUANTITY_DEVIATION", unique = true, nullable = false)
    public Integer getGrItemDeviationId() {
        return grItemDeviationId;
    }

    public void setGrItemDeviationId(Integer grItemDeviationId) {
        this.grItemDeviationId = grItemDeviationId;
    }

    @Column(name = "DEVIATION_DETAIL", nullable = false)
    public String getDeviationReason() {
        return deviationReason;
    }

    public void setDeviationReason(String deviationReason) {
        this.deviationReason = deviationReason;
    }
}
