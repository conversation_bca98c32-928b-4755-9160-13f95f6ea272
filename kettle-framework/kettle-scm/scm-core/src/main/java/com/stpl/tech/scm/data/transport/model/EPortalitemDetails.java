package com.stpl.tech.scm.data.transport.model;

import org.bouncycastle.pqc.math.linearalgebra.BigEndianConversions;

import java.math.BigDecimal;

public class EPortalitemDetails {

    private String SlNo;
    private String PrdDesc;
    private String IsServc = "N";
    private String HsnCd;
    private BigDecimal Qty;
    private BigDecimal FreeQty;
    private String Unit;
    private BigDecimal UnitPrice;
    private BigDecimal TotAmt;
    private BigDecimal Discount;
    private BigDecimal PreTaxVal;
    private BigDecimal AssAmt;
    private BigDecimal GstRt;
    private BigDecimal IgstAmt;
    private BigDecimal CgstAmt;
    private BigDecimal SgstAmt;
    private BigDecimal CesRt;
    private BigDecimal CesAmt;
    private BigDecimal CesNonAdvlAmt;
    private BigDecimal StateCesRt;
    private BigDecimal StateCesAmt;
    private BigDecimal StateCesNonAdvlAmt;
    private BigDecimal OthChrg;
    private BigDecimal TotItemVal;

    public String getSlNo() {
        return SlNo;
    }

    public void setSlNo(String slNo) {
        SlNo = slNo;
    }

    public String getPrdDesc() {
        return PrdDesc;
    }

    public void setPrdDesc(String prdDesc) {
        PrdDesc = prdDesc;
    }

    public String getIsServc() {
        return IsServc;
    }

    public void setIsServc(String isServc) {
        IsServc = isServc;
    }

    public String getHsnCd() {
        return HsnCd;
    }

    public void setHsnCd(String hsnCd) {
        HsnCd = hsnCd;
    }

    public BigDecimal getQty() {
        return Qty;
    }

    public void setQty(BigDecimal qty) {
        Qty = qty;
    }

    public BigDecimal getFreeQty() {
        return FreeQty;
    }

    public void setFreeQty(BigDecimal freeQty) {
        FreeQty = freeQty;
    }

    public String getUnit() {
        return Unit;
    }

    public void setUnit(String unit) {
        Unit = unit;
    }

    public BigDecimal getUnitPrice() {
        return UnitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        UnitPrice = unitPrice;
    }

    public BigDecimal getTotAmt() {
        return TotAmt;
    }

    public void setTotAmt(BigDecimal totAmt) {
        TotAmt = totAmt;
    }

    public BigDecimal getDiscount() {
        return Discount;
    }

    public void setDiscount(BigDecimal discount) {
        Discount = discount;
    }

    public BigDecimal getPreTaxVal() {
        return PreTaxVal;
    }

    public void setPreTaxVal(BigDecimal preTaxVal) {
        PreTaxVal = preTaxVal;
    }

    public BigDecimal getAssAmt() {
        return AssAmt;
    }

    public void setAssAmt(BigDecimal assAmt) {
        AssAmt = assAmt;
    }

    public BigDecimal getGstRt() {
        return GstRt;
    }

    public void setGstRt(BigDecimal gstRt) {
        GstRt = gstRt;
    }

    public BigDecimal getIgstAmt() {
        return IgstAmt;
    }

    public void setIgstAmt(BigDecimal igstAmt) {
        IgstAmt = igstAmt;
    }

    public BigDecimal getCgstAmt() {
        return CgstAmt;
    }

    public void setCgstAmt(BigDecimal cgstAmt) {
        CgstAmt = cgstAmt;
    }

    public BigDecimal getSgstAmt() {
        return SgstAmt;
    }

    public void setSgstAmt(BigDecimal sgstAmt) {
        SgstAmt = sgstAmt;
    }

    public BigDecimal getCesRt() {
        return CesRt;
    }

    public void setCesRt(BigDecimal cesRt) {
        CesRt = cesRt;
    }

    public BigDecimal getCesAmt() {
        return CesAmt;
    }

    public void setCesAmt(BigDecimal cesAmt) {
        CesAmt = cesAmt;
    }

    public BigDecimal getCesNonAdvlAmt() {
        return CesNonAdvlAmt;
    }

    public void setCesNonAdvlAmt(BigDecimal cesNonAdvlAmt) {
        CesNonAdvlAmt = cesNonAdvlAmt;
    }

    public BigDecimal getStateCesRt() {
        return StateCesRt;
    }

    public void setStateCesRt(BigDecimal stateCesRt) {
        StateCesRt = stateCesRt;
    }

    public BigDecimal getStateCesAmt() {
        return StateCesAmt;
    }

    public void setStateCesAmt(BigDecimal stateCesAmt) {
        StateCesAmt = stateCesAmt;
    }

    public BigDecimal getStateCesNonAdvlAmt() {
        return StateCesNonAdvlAmt;
    }

    public void setStateCesNonAdvlAmt(BigDecimal stateCesNonAdvlAmt) {
        StateCesNonAdvlAmt = stateCesNonAdvlAmt;
    }

    public BigDecimal getOthChrg() {
        return OthChrg;
    }

    public void setOthChrg(BigDecimal othChrg) {
        OthChrg = othChrg;
    }

    public BigDecimal getTotItemVal() {
        return TotItemVal;
    }

    public void setTotItemVal(BigDecimal totItemVal) {
        TotItemVal = totItemVal;
    }
}
