package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@Table(name = "TRANSFER_ORDER_ASSET_CORRECTION")
public class TransferOrderAssetCorrectionData {
    private Integer assetCorrectionId;
    private Integer transferOrderId;
    private Integer transferOrderItemId;
    private Integer assetId;
    private String previousAssetTag;
    private String updatedAssetTag;
    private String status;
    private String updatedBy;
    private Date lastUpdatedTime;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ASSET_CORRECTION_ID", nullable = false, unique = true)
    public Integer getAssetCorrectionId() {
        return assetCorrectionId;
    }

    public void setAssetCorrectionId(Integer assetCorrectionId) {
        this.assetCorrectionId = assetCorrectionId;
    }

    @Column(name = "TRANSFER_ORDER_ID", nullable = false)
    public Integer getTransferOrderId() {
        return transferOrderId;
    }

    public void setTransferOrderId(Integer transferOrderId) {
        this.transferOrderId = transferOrderId;
    }

    @Column(name = "TRANSFER_ORDER_ITEM_ID", nullable = false)
    public Integer getTransferOrderItemId() {
        return transferOrderItemId;
    }

    public void setTransferOrderItemId(Integer transferOrderItemId) {
        this.transferOrderItemId = transferOrderItemId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "PREVIOUS_ASSET_TAG", nullable = false)
    public String getPreviousAssetTag() {
        return previousAssetTag;
    }

    public void setPreviousAssetTag(String previousAssetTag) {
        this.previousAssetTag = previousAssetTag;
    }

    @Column(name = "UPDATED_ASSET_TAG", nullable = false)
    public String getUpdatedAssetTag() {
        return updatedAssetTag;
    }

    public void setUpdatedAssetTag(String updatedAssetTag) {
        this.updatedAssetTag = updatedAssetTag;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "UPDATED_BY", nullable = false)
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_UPDATED_TIME", nullable = false)
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }
}
