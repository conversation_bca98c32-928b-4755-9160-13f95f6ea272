package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "PAYMENT_REQUEST", uniqueConstraints=
@UniqueConstraint(columnNames={"VENDOR_ID", "INVOICE_NUMBER"}))
public class PaymentRequestData {

    private Integer id;
    private String type;
    private Integer vendorId;
    private String invoiceNumber;
    private Integer createdBy;
    private Date creationTime;
    private String currentStatus;
    private Date lastUpdated;
    private BigDecimal proposedAmount;
    private BigDecimal paidAmount;
    private BigDecimal duplicatePaidAmount;
    private AdvancePaymentData advancePaymentData;
    private BigDecimal advanceAmount;
    private String amountsMatch;
    private String isBlocked = "N";
    private Integer blockedBy;
    private Integer requestingUnit;
    private String grDocType;
    private Integer deviationCount;
    private String remarks;
    private String paidAdhoc = "N";
    private PaymentCalendarData paymentCalendarData;
    private PRPaymentDetailData prPaymentDetailData;
    private Integer companyId;
    private Date paymentDate;
    private String filingNumber;
    private String stateCode;
    private String stateName;
    private Date vendorPaymentDate;
    private String extraChargesType;
    private BigDecimal extraChargesSgst;
    private BigDecimal extraChargesCgst;
    private BigDecimal extraChargesIgst;
    private BigDecimal extraChargesWithOutTax;

    private String businessCostCentreName;

    private String mandatoryRequiredDocument;
    private Date lastQueriedDate;
    private Date lastQueryResolvedDate;
    private List<PaymentRequestQueryData> paymentRequestQueryData;
    private String paymentCard;
    private String cardPaymentTransactionNumber;
    private Integer cardPaymentProof;
    private String cardPaymentComment;
    private List<AdvancePaymentData> advancePaymentDataList;
    private List<LinkedPaymentsForAdvance> linkedPaymentsForAdvance;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_REQUEST_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "PAYMENT_REQUEST_TYPE", nullable = false, length = 200)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "VENDOR_ID")
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "CURRENT_STATUS", nullable = false, length = 50)
    public String getCurrentStatus() {
        return currentStatus;
    }

    public void setCurrentStatus(String currentStatus) {
        this.currentStatus = currentStatus;
    }

    @Column(name = "LAST_UPDATED", nullable = false)
    public Date getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(Date lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    @Column(name = "PROPOSED_AMOUNT", nullable = false)
    public BigDecimal getProposedAmount() {
        return proposedAmount;
    }

    public void setProposedAmount(BigDecimal proposedAmount) {
        this.proposedAmount = proposedAmount;
    }

    @Column(name = "PAID_AMOUNT", nullable = false)
    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    @Column(name = "DUPLICATE_PAID_AMOUNT", nullable = false)
    public BigDecimal getDuplicatePaidAmount() {
        return duplicatePaidAmount;
    }

    public void setDuplicatePaidAmount(BigDecimal duplicatePaidAmount) {
        this.duplicatePaidAmount = duplicatePaidAmount;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ADVANCE_PAYMENT_ID", nullable = false)
    public AdvancePaymentData getAdvancePaymentData() {
        return advancePaymentData;
    }

    public void setAdvancePaymentData(AdvancePaymentData advancePaymentData) {
        this.advancePaymentData = advancePaymentData;
    }

    @Column(name = "ADVANCE_AMOUNT", nullable = true)
    public BigDecimal getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(BigDecimal advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    @Column(name = "AMOUNTS_MATCH", nullable = false, length = 1)
    public String getAmountsMatch() {
        return amountsMatch;
    }

    public void setAmountsMatch(String amountsMatch) {
        this.amountsMatch = amountsMatch;
    }

    @Column(name = "IS_BLOCKED", length = 1, nullable = false)
    public String getIsBlocked() {
        return isBlocked;
    }

    public void setIsBlocked(String isBlocked) {
        this.isBlocked = isBlocked;
    }

    @Column(name = "BLOCKED_BY")
    public Integer getBlockedBy() {
        return blockedBy;
    }

    public void setBlockedBy(Integer blockedBy) {
        this.blockedBy = blockedBy;
    }

    @Column(name = "REQUESTING_UNIT", nullable = false)
    public Integer getRequestingUnit() {
        return requestingUnit;
    }

    public void setRequestingUnit(Integer requestingUnit) {
        this.requestingUnit = requestingUnit;
    }

    @Column(name = "GR_DOC_TYPE", length = 50)
    public String getGrDocType() {
        return grDocType;
    }

    public void setGrDocType(String grDocType) {
        this.grDocType = grDocType;
    }

    @Column(name = "DEVIATION_COUNT", nullable = false)
    public Integer getDeviationCount() {
        return deviationCount;
    }

    public void setDeviationCount(Integer deviationCount) {
        this.deviationCount = deviationCount;
    }

    @Column(name = "INVOICE_NUMBER")
    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAYMENT_DETAIL_ID")
    public PRPaymentDetailData getPrPaymentDetailData() {
        return prPaymentDetailData;
    }

    public void setPrPaymentDetailData(PRPaymentDetailData prPaymentDetailData) {
        this.prPaymentDetailData = prPaymentDetailData;
    }

    @Column(name = "REMARKS")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAYMENT_CYCLE")
    public PaymentCalendarData getPaymentCalendarData() {
        return paymentCalendarData;
    }

    public void setPaymentCalendarData(PaymentCalendarData paymentCalendarData) {
        this.paymentCalendarData = paymentCalendarData;
    }

    @Column(name = "PAID_ADHOC", nullable = false, length = 1)
    public String getPaidAdhoc() {
        return paidAdhoc;
    }

    public void setPaidAdhoc(String paidAdhoc) {
        this.paidAdhoc = paidAdhoc;
    }
    
    @Column(name = "COMPANY_ID")
	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	@Column(name = "PAYMENT_DATE", nullable = true)
    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    @Column(name = "FILING_NO")
    public String getFilingNumber() {
        return filingNumber;
    }

    public void setFilingNumber(String filingNumber) {
        this.filingNumber = filingNumber;
    }

    @Column(name = "STATE_CODE")
    public String getStateCode() {
        return stateCode;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode;
    }

    @Column(name = "STATE_NAME")
    public String getStateName() {
        return stateName;
    }

    public void setStateName(String stateName) {
        this.stateName = stateName;
    }

    @Column(name = "VENDOR_PAYMENT_DATE")
    public Date getVendorPaymentDate() {
        return vendorPaymentDate;
    }

    public void setVendorPaymentDate(Date vendorPaymentDate) {
        this.vendorPaymentDate = vendorPaymentDate;
    }

    @Column(name = "EXTRA_CHARGES_TYPE")
    public String getExtraChargesType() {
        return extraChargesType;
    }

    public void setExtraChargesType(String extraChargesType) {
        this.extraChargesType = extraChargesType;
    }

    @Column(name = "EXTRA_CHARGES_SGST")
    public BigDecimal getExtraChargesSgst() {
        return extraChargesSgst;
    }

    public void setExtraChargesSgst(BigDecimal extraChargesSgst) {
        this.extraChargesSgst = extraChargesSgst;
    }

    @Column(name = "EXTRA_CHARGES_CGST")
    public BigDecimal getExtraChargesCgst() {
        return extraChargesCgst;
    }

    public void setExtraChargesCgst(BigDecimal extraChargesCgst) {
        this.extraChargesCgst = extraChargesCgst;
    }

    @Column(name = "EXTRA_CHARGES_IGST")
    public BigDecimal getExtraChargesIgst() {
        return extraChargesIgst;
    }

    public void setExtraChargesIgst(BigDecimal extraChargesIgst) {
        this.extraChargesIgst = extraChargesIgst;
    }

    @Column(name = "EXTRA_CHARGES_WITHOUT_TAX")
    public BigDecimal getExtraChargesWithOutTax() {
        return extraChargesWithOutTax;
    }

    public void setExtraChargesWithOutTax(BigDecimal extraChargesWithOutTax) {
        this.extraChargesWithOutTax = extraChargesWithOutTax;
    }

    @Transient
    public String getBusinessCostCentreName() {
        return businessCostCentreName;
    }

    public void setBusinessCostCentreName(String businessCostCentreName) {
        this.businessCostCentreName = businessCostCentreName;
    }

    @Column(name = "MANDATORY_REQUIRED_DOCUMENTS")
    public String getMandatoryRequiredDocument() {
        return mandatoryRequiredDocument;
    }

    public void setMandatoryRequiredDocument(String mandatoryRequiredDocument) {
        this.mandatoryRequiredDocument = mandatoryRequiredDocument;
    }

    @Column(name = "LAST_QUERIED_DATE", nullable = true)
    public Date getLastQueriedDate() {
        return lastQueriedDate;
    }

    public void setLastQueriedDate(Date lastQueriedDate) {
        this.lastQueriedDate = lastQueriedDate;
    }

    @Column(name = "LAST_QUERY_RESOLVED_DATE", nullable = true)
    public Date getLastQueryResolvedDate() {
        return lastQueryResolvedDate;
    }

    public void setLastQueryResolvedDate(Date lastQueryResolvedDate) {
        this.lastQueryResolvedDate = lastQueryResolvedDate;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "prId")
    public List<PaymentRequestQueryData> getPaymentRequestQueryData() {
        return paymentRequestQueryData;
    }

    public void setPaymentRequestQueryData(List<PaymentRequestQueryData> paymentRequestQueryData) {
        this.paymentRequestQueryData = paymentRequestQueryData;
    }

    @Column(name = "PAYMENT_CARD", nullable = true)
    public String getPaymentCard() {
        return paymentCard;
    }

    public void setPaymentCard(String paymentCard) {
        this.paymentCard = paymentCard;
    }

    @Column(name = "CARD_PAYMENT_TRANSACTION_NUMBER", nullable = true)
    public String getCardPaymentTransactionNumber() {
        return cardPaymentTransactionNumber;
    }

    public void setCardPaymentTransactionNumber(String cardPaymentTransactionNumber) {
        this.cardPaymentTransactionNumber = cardPaymentTransactionNumber;
    }

    @Column(name = "CARD_PAYMENT_PROOF", nullable = true)
    public Integer getCardPaymentProof() {
        return cardPaymentProof;
    }

    public void setCardPaymentProof(Integer cardPaymentProof) {
        this.cardPaymentProof = cardPaymentProof;
    }

    @Column(name = "CARD_PAYMENT_COMMENT", nullable = true)
    public String getCardPaymentComment() {
        return cardPaymentComment;
    }

    public void setCardPaymentComment(String cardPaymentComment) {
        this.cardPaymentComment = cardPaymentComment;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "advancePaymentId")
    public List<AdvancePaymentData> getAdvancePaymentDataList() {
        return advancePaymentDataList;
    }

    public void setAdvancePaymentDataList(List<AdvancePaymentData> advancePaymentDataList) {
        this.advancePaymentDataList = advancePaymentDataList;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "paymentRequestData")
    public List<LinkedPaymentsForAdvance> getLinkedPaymentsForAdvance() {
        return linkedPaymentsForAdvance;
    }

    public void setLinkedPaymentsForAdvance(List<LinkedPaymentsForAdvance> linkedPaymentsForAdvance) {
        this.linkedPaymentsForAdvance = linkedPaymentsForAdvance;
    }
}
