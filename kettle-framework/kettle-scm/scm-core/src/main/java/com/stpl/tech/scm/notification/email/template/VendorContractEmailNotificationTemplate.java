package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.domain.model.VendorContract;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
public class VendorContractEmailNotificationTemplate extends AbstractVelocityTemplate {

    private VendorDetail vendor;
    private EmployeeBasicDetail employeeBasicDetail;
    private VendorContractInfo vendorContract;
    private String basePath;
    private String documentUrl;
    private String vendorUrl;
    private String templateName;

    @Override
    public String getTemplatePath() {
        return "templates/"+templateName;
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor-contract/" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendor", vendor);
        stringObjectMap.put("vendorContract", vendorContract);
        stringObjectMap.put("employeeBasicDetail", employeeBasicDetail);
        stringObjectMap.put("documentUrl", documentUrl);
        stringObjectMap.put("vendorUrl", vendorUrl);
        return stringObjectMap;
    }

}
