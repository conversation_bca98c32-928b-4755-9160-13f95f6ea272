/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.mongo.VendorComplianceDataAuditLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Set;

public interface VendorComplianceDataAuditLogDao extends MongoRepository<VendorComplianceDataAuditLog, String> {

    @Query("{'month' : ?0 , 'year' : ?1,'complianceKey' : { $in : ?2 } }")
    List<VendorComplianceDataAuditLog> findVendorComplianceDataAuditLogsByMonthAndYear(Integer month, Integer year, Set<String> complianceKeys);
}