package com.stpl.tech.scm.data.model;

import com.stpl.tech.master.domain.model.Address;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 02-07-2018.
 */
@Entity
@Table(name = "SALES_PERFORMA_INVOICE")
public class SalesPerformaDetailData {

    private Integer invoiceId;
    private Integer vendor;
    private Integer dispatchLocation;
    private String type;
    private String invoiceDocUrl;
    private String eWayDocUrl;
    private String deliveredDocUrl;
    private String transportMode;
    private String transportName;
    private String transportId;
    private String status;
    private BigDecimal totalCost;
    private BigDecimal totalSellingCost;
    private BigDecimal totalTax;            // on Selling Cost
    private BigDecimal additionalCharges;   // freight charges, etc.
    private BigDecimal totalAmount;         // selling cost + tax + additional charges
    private Integer sendingUnit;
    private String sendingUnitName;
    private String needsApproval;
    private String comment;
    private Integer createdBy;
    private Integer cancelledBy;
    private Date createdAt;
    private Date cancelledAt;
    private Integer closureId;
    private Date dispatchDate;
    private Integer sendingCompany;
    private String docketNumber;
    private String invoiceType;
    private String generatedId;
    private String uploadDocId;

    private List<SalesPerformaInvoiceItemData> invoiceItems;
    private Integer invoiceDocumentId;
    private Integer ewayDocumentId;
    private Integer deliveredDocumentId;
    private String assetOrder;
    private String purchasedOrderNumber;
    private Date purchasedOrderDate;

    private String billingType;
    private String businessType;
    private Integer poDocumentId;
    private String irnNo;
    private Integer cancelDocId;
    private String uploadedAckNo;
    private String uploadedEwayNo;
    private String signedQrCode;
    private Integer generatedBarcodeId;
    private Integer billingLocation;
    private Date dateOfDelivery;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "INVOICE_ID", nullable = false, unique = true)
    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public Integer getVendor() {
        return vendor;
    }

    public void setVendor(Integer vendor) {
        this.vendor = vendor;
    }

    @Column(name = "DISPATCH_ID", nullable = false)
    public Integer getDispatchLocation() {
        return dispatchLocation;
    }

    public void setDispatchLocation(Integer dispatchLocation) {
        this.dispatchLocation = dispatchLocation;
    }

    @Column(name = "TYPE", nullable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "INVOICE_DOC_URL")
    public String getInvoiceDocUrl() {
        return invoiceDocUrl;
    }

    public void setInvoiceDocUrl(String invoiceDocUrl) {
        this.invoiceDocUrl = invoiceDocUrl;
    }

    @Column(name = "E_WAY_DOC_URL")
    public String geteWayDocUrl() {
        return eWayDocUrl;
    }

    public void seteWayDocUrl(String eWayBillUrl) {
        this.eWayDocUrl = eWayBillUrl;
    }

    @Column(name = "INVOICE_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "TOTAL_COST", nullable = false)
    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    @Column(name = "TOTAL_TAX", nullable = false)
    public BigDecimal getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(BigDecimal totalTax) {
        this.totalTax = totalTax;
    }

    @Column(name = "ADDITIONAL_CHARGES", nullable = false)
    public BigDecimal getAdditionalCharges() {
        return additionalCharges;
    }

    public void setAdditionalCharges(BigDecimal additionalCharges) {
        this.additionalCharges = additionalCharges;
    }

    @Column(name = "TOTAL_SELLING_COST", nullable = false)
    public BigDecimal getTotalSellingCost() {
        return totalSellingCost;
    }

    public void setTotalSellingCost(BigDecimal totalSellingCost) {
        this.totalSellingCost = totalSellingCost;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = false)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Column(name = "SENDING_UNIT_ID", nullable = false)
    public Integer getSendingUnit() {
        return sendingUnit;
    }

    public void setSendingUnit(Integer sendingUnit) {
        this.sendingUnit = sendingUnit;
    }

    @Column(name = "SENDING_UNIT_NAME", nullable = false)
    public String getSendingUnitName() {
        return sendingUnitName;
    }

    public void setSendingUnitName(String sendingUnitName) {
        this.sendingUnitName = sendingUnitName;
    }

    @Column(name = "NEEDS_APPROVAL", nullable = false)
    public String getNeedsApproval() {
        return needsApproval;
    }

    public void setNeedsApproval(String needsApproval) {
        this.needsApproval = needsApproval;
    }

    @Column(name = "COMMENT")
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CANCELLED_BY")
    public Integer getCancelledBy() {
        return cancelledBy;
    }

    public void setCancelledBy(Integer cancelledBy) {
        this.cancelledBy = cancelledBy;
    }

    @Column(name = "CREATED_AT", nullable = false)
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Column(name = "CANCELLED_AT")
    public Date getCancelledAt() {
        return cancelledAt;
    }

    public void setCancelledAt(Date cancelledAt) {
        this.cancelledAt = cancelledAt;
    }

    @Column(name = "CLOSURE_ID")
    public Integer getClosureId() {
        return closureId;
    }

    public void setClosureId(Integer closureId) {
        this.closureId = closureId;
    }

    @Column(name = "TRANSPORT_MODE")
    public String getTransportMode() {
        return transportMode;
    }

    public void setTransportMode(String transportMode) {
        this.transportMode = transportMode;
    }

    @Column(name = "TRANSPORT_NAME")
    public String getTransportName() {
        return transportName;
    }

    public void setTransportName(String transportName) {
        this.transportName = transportName;
    }

    @Column(name = "TRANSPORT_ID")
    public String getTransportId() {
        return transportId;
    }

    public void setTransportId(String transportId) {
        this.transportId = transportId;
    }

    @Column(name = "DISPATCH_DATE")
    public Date getDispatchDate() {
        return dispatchDate;
    }

    public void setDispatchDate(Date dispatchDate) {
        this.dispatchDate = dispatchDate;
    }

    @Column(name = "DOCKET_NUMBER")
    public String getDocketNumber() {
        return docketNumber;
    }

    public void setDocketNumber(String docketNumber) {
        this.docketNumber = docketNumber;
    }

    @Column(name = "SENDING_COMPANY")
    public Integer getSendingCompany() {
        return sendingCompany;
    }

    public void setSendingCompany(Integer sendingCompany) {
        this.sendingCompany = sendingCompany;
    }

    @Column(name = "INVOICE_TYPE")
    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    @Column(name = "INVOICE_DOC_ID")
    public Integer getInvoiceDocumentId() {
        return invoiceDocumentId;
    }

    public void setInvoiceDocumentId(Integer invoiceDocumentId) {
        this.invoiceDocumentId = invoiceDocumentId;
    }

    @Column(name = "EWAY_DOC_ID")
    public Integer getEwayDocumentId() {
        return ewayDocumentId;
    }

    public void setEwayDocumentId(Integer ewayDocumentId) {
        this.ewayDocumentId = ewayDocumentId;
    }

    @Column(name = "GENERATED_ID")
    public String getGeneratedId() {
        return generatedId;
    }

    public void setGeneratedId(String generatedId) {
        this.generatedId = generatedId;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "invoice")
    public List<SalesPerformaInvoiceItemData> getInvoiceItems() {
        return invoiceItems;
    }

    public void setInvoiceItems(List<SalesPerformaInvoiceItemData> invoiceItems) {
        this.invoiceItems = invoiceItems;
    }

    @Column(name = "ASSET_ORDER", nullable = false)
    public String getAssetOrder() {
        return assetOrder;
    }

    public void setAssetOrder(String assetOrder) {
        this.assetOrder = assetOrder;
    }

    @Column(name = "DELIVERED_DOC_URL")
    public String getDeliveredDocUrl() {
        return deliveredDocUrl;
    }

    public void setDeliveredDocUrl(String deliveredDocUrl) {
        this.deliveredDocUrl = deliveredDocUrl;
    }

    @Column(name = "DELIVERED_DOC_ID")
    public Integer getDeliveredDocumentId() {
        return deliveredDocumentId;
    }

    public void setDeliveredDocumentId(Integer deliveredDocumentId) {
        this.deliveredDocumentId = deliveredDocumentId;
    }

    @Column(name = "PURCHASED_ORDER_NUMBER")
    public String getPurchasedOrderNumber() {
        return purchasedOrderNumber;
    }

    public void setPurchasedOrderNumber(String purchasedOrderNumber) {
        this.purchasedOrderNumber = purchasedOrderNumber;
    }

    @Column(name = "PURCHASED_ORDER_DATE")
    public Date getPurchasedOrderDate() {
        return purchasedOrderDate;
    }

    public void setPurchasedOrderDate(Date purchasedOrderDate) {
        this.purchasedOrderDate = purchasedOrderDate;
    }

    @Column(name = "BILLING_TYPE")
    public String getBillingType() {
        return billingType;
    }

    public void setBillingType(String billingType) {
        this.billingType = billingType;
    }

    @Column(name = "BUSINESS_TYPE")
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    @Column(name = "PO_DOC_ID")
    public Integer getPoDocumentId() {
        return poDocumentId;
    }

    public void setPoDocumentId(Integer poDocumentId) {
        this.poDocumentId = poDocumentId;
    }

    @Column(name = "IRN_NO")
    public String getIrnNo() {
        return irnNo;
    }

    public void setIrnNo(String irnNo) {
        this.irnNo = irnNo;
    }

    @Column(name = "CANCEL_DOC_ID")
    public Integer getCancelDocId() {
        return cancelDocId;
    }

    public void setCancelDocId(Integer cancelDocId) {
        this.cancelDocId = cancelDocId;
    }


    @Column(name = "UPLOAD_DOC_ID")
    public String getUploadDocId() {
        return uploadDocId;
    }

    public void setUploadDocId(String uploadDocId) {
        this.uploadDocId = uploadDocId;
    }


    @Column(name = "UPLOADED_ACK_NO")
    public String getUploadedAckNo() {
        return uploadedAckNo;
    }

    public void setUploadedAckNo(String uploadedAckNo) {
        this.uploadedAckNo = uploadedAckNo;
    }

    @Column(name = "UPLOADED_EWAY_NO")
    public String getUploadedEwayNo() {
        return uploadedEwayNo;
    }

    public void setUploadedEwayNo(String uploadedEwayNo) {
        this.uploadedEwayNo = uploadedEwayNo;
    }

    @Column(name = "SIGNED_QR_CODE")
    public String getSignedQrCode() {
        return signedQrCode;
    }

    public void setSignedQrCode(String signedQrCode) {
        this.signedQrCode = signedQrCode;
    }

    @Column(name = "BARCODE_ID")
    public Integer getGeneratedBarcodeId() {
        return generatedBarcodeId;
    }

    public void setGeneratedBarcodeId(Integer generatedBarcodeId) {
        this.generatedBarcodeId = generatedBarcodeId;
    }

    @Column(name = "BILLING_LOCATION_ID", nullable = false)
    public Integer getBillingLocation() {
        return billingLocation;
    }


    public void setBillingLocation(Integer billingLocation) {
        this.billingLocation = billingLocation;
    }

    @Column(name = "DELIVERY_DATE", nullable = true)
    public Date getDateOfDelivery() {return dateOfDelivery;}

    public void setDateOfDelivery(Date dateOfDelivery) {this.dateOfDelivery = dateOfDelivery;}
}
