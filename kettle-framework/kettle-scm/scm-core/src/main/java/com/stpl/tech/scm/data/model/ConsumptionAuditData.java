package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 22-11-2018.
 */
@Entity
@Table(name = "CONSUMPTION_AUDIT_DATA")
public class ConsumptionAuditData implements Serializable{

    private static final long serialVersionUID = 3453416548403885578L;

    private Integer auditDetailId;
    private int transactionItemId;
    private String transactionType;
    private int costDetailId;
    private int keyId;
    private String keyType;
    private BigDecimal quantity;
    private BigDecimal price;
    private String uom;
    private Date lastUpdatedTime;
    private Date expiryDate;
    private String creationReason;
    private Integer creationItemId;
    private String exception;
    private BigDecimal costDetailQuantity;


    public ConsumptionAuditData() {}

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "AUDIT_DETAIL_ID")
    public Integer getAuditDetailId() {
        return auditDetailId;
    }

    public void setAuditDetailId(Integer auditDetailId) {
        this.auditDetailId = auditDetailId;
    }

    @Column(name = "KEY_ID")
    public int getKeyId() {
        return keyId;
    }

    public void setKeyId(int keyId) {
        this.keyId = keyId;
    }

    @Column(name = "KEY_TYPE")
    public String getKeyType() {
        return keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    @Column(name = "TRANSACTION_ITEM_ID")
    public int getTransactionItemId() {
        return transactionItemId;
    }

    public void setTransactionItemId(int transactionItemId) {
        this.transactionItemId = transactionItemId;
    }

    @Column(name = "TRANSACTION_TYPE")
    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    @Column(name = "COST_DETAIL_ID")
    public int getCostDetailId() {
        return costDetailId;
    }

    public void setCostDetailId(int costDetailId) {
        this.costDetailId = costDetailId;
    }

    @Column(name = "QUANTITY")
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "PRICE")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "UOM")
    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }

    @Column(name = "LAST_UPDATE_TIME")
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }

    @Column(name = "EXPIRY_DATE")
    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Column(name = "CREATION_REASON")
    public String getCreationReason() {
        return creationReason;
    }

    public void setCreationReason(String creationReason) {
        this.creationReason = creationReason;
    }

    @Column(name = "CREATION_ITEM_ID")
    public Integer getCreationItemId() {
        return creationItemId;
    }

    public void setCreationItemId(Integer creationItemId) {
        this.creationItemId = creationItemId;
    }

    @Column(name = "EXCEPTION_REASON")
    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        this.exception = exception;
    }

    @Column(name = "COST_DETAIL_QUANTITY")
    public BigDecimal getCostDetailQuantity() {
        return costDetailQuantity;
    }

    public void setCostDetailQuantity(BigDecimal costDetailQuantity) {
        this.costDetailQuantity = costDetailQuantity;
    }

}
