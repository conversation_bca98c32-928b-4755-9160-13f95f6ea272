/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Entity
@Table(name = "DAY_CLOSE_EVENT")
public class SCMDayCloseEventData {
    private Integer eventId;
    private Date businessDate;
    private Date generationTime;
    private String status;
    private Integer dayClosureId;
    private int unitId;
    private String dayCloseEventType;
    private String eventFrequencyType;
    private Date updatedAt;
    private Integer updatedBy;
    private Integer createdBy;
    private String subCategories;
    private String orderingSuccess;
    private String varianceStatus;
    private Integer varianceUpdatedBy;

    private List<DayCloseEventLogData> eventLogDataList = new ArrayList<>(0);
    private List<DayCloseTxnEventMapping> eventMappingList = new ArrayList<>(0);
    private List<RegularOrderingEvent> regularOrderingEvents = new ArrayList<>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID", unique = true, nullable = false)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "BUSINESS_DATE", nullable = false)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Column(name = "GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "DAY_CLOSURE_ID", nullable = true)
    public Integer getDayClosureId() {
        return dayClosureId;
    }

    public void setDayClosureId(Integer dayClosureId) {
        this.dayClosureId = dayClosureId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "CLOSURE_EVENT_TYPE", nullable = false)
    public String getDayCloseEventType() {
        return dayCloseEventType;
    }

    public void setDayCloseEventType(String dayCloseEventType) {
        this.dayCloseEventType = dayCloseEventType;
    }

    @Column(name = "CLOSURE_EVENT_FREQUENCY", nullable = false)
    public String getEventFrequencyType() {
        return eventFrequencyType;
    }

    public void setEventFrequencyType(String eventFrequencyType) {
        this.eventFrequencyType = eventFrequencyType;
    }

    @Column(name = "UPDATED_AT", nullable = true)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "UPDATED_BY", nullable = true)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "CREATED_BY", nullable = true)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }


    @OneToMany(fetch = FetchType.LAZY, mappedBy = "dayCloseEventData")
    public List<DayCloseEventLogData> getEventLogDataList() {
        return eventLogDataList;
    }

    public void setEventLogDataList(List<DayCloseEventLogData> eventLogDataList) {
        this.eventLogDataList = eventLogDataList;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "closureEvent")
    public List<DayCloseTxnEventMapping> getEventMappingList() {
        return eventMappingList;
    }

    public void setEventMappingList(List<DayCloseTxnEventMapping> eventMappingList) {
        this.eventMappingList = eventMappingList;
    }

    @Column(name = "SUB_CATEGORIES", nullable = true)
    public String getSubCategories() {
        return subCategories;
    }

    public void setSubCategories(String subCategories) {
        this.subCategories = subCategories;
    }

    @Column(name = "IS_ORDERING_SUCCESS", nullable = true)
    public String getOrderingSuccess() {
        return orderingSuccess;
    }

    public void setOrderingSuccess(String orderingSuccess) {
        this.orderingSuccess = orderingSuccess;
    }

        @OneToMany(fetch = FetchType.LAZY, mappedBy = "dayCloseEventData")
    public List<RegularOrderingEvent> getRegularOrderingEvents() {
        return regularOrderingEvents;
    }

    public void setRegularOrderingEvents(List<RegularOrderingEvent> regularOrderingEvents) {
        this.regularOrderingEvents = regularOrderingEvents;
    }

    @Column(name = "VARIANCE_STATUS", nullable = true)
    public String getVarianceStatus() {
        return varianceStatus;
    }

    public void setVarianceStatus(String varianceStatus) {
        this.varianceStatus = varianceStatus;
    }

    @Column(name = "VARIANCE_UPDATED_BY", nullable = true)
    public Integer getVarianceUpdatedBy() {
        return varianceUpdatedBy;
    }

    public void setVarianceUpdatedBy(Integer varianceUpdatedBy) {
        this.varianceUpdatedBy = varianceUpdatedBy;
    }
}

