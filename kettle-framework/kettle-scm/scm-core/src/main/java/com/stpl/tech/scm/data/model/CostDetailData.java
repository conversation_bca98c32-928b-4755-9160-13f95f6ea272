/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * ProductRecipeCost generated by hbm2java
 */
@SuppressWarnings("serial")
@MappedSuperclass
public  class CostDetailData  implements java.io.Serializable   {

	private Integer costDetailDataId;
	private int keyId;
	private String keyType;
	private int unitId;
	private BigDecimal quantity;
	private BigDecimal price;
	private String uom;
	private String latest;
	private Date lastUpdatedTime;
	private Date expiryDate;

	private String creationReason;
	private Integer creationItemId;

	public CostDetailData() {

	}

	public CostDetailData(int keyId, String keyType, int unitId, BigDecimal quantity, BigDecimal price, String uom,
			String latest) {
		super();
		this.keyId = keyId;
		this.keyType = keyType;
		this.unitId = unitId;
		this.quantity = quantity;
		this.price = price;
		this.uom = uom;
		this.latest = latest;
	}

	/**
	 * @return the productRecipeCostId
	 */
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COST_DETAIL_DATA_ID", unique = true, nullable = false)
	public Integer getCostDetailDataId() {
		return costDetailDataId;
	}

	/**
	 * @param costDetailDataId
	 *            the costDetailDataId to set
	 */
	public void setCostDetailDataId(Integer costDetailDataId) {
		this.costDetailDataId = costDetailDataId;
	}

	/**
	 * @return the keyId
	 */
	@Column(name = "KEY_ID", nullable = false)
	public int getKeyId() {
		return keyId;
	}

	/**
	 * @param keyId
	 *            the keyId to set
	 */
	public void setKeyId(int keyId) {
		this.keyId = keyId;
	}

	/**
	 * @return the keyType
	 */
	@Column(name = "KEY_TYPE", nullable = false)
	public String getKeyType() {
		return keyType;
	}

	/**
	 * @param keyType
	 *            the keyType to set
	 */
	public void setKeyType(String keyType) {
		this.keyType = keyType;
	}

	/**
	 * @return the dineInCost
	 */
	@Column(name = "PRICE", precision = 16)
	public BigDecimal getPrice() {
		return price;
	}

	/**
	 * @param dineInCost
	 *            the dineInCost to set
	 */
	public void setPrice(BigDecimal dineInCost) {
		this.price = dineInCost;
	}

	@Column(name = "QUANTITY", precision = 16)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	/**
	 * @return the lastUpdatedTime
	 */
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
	public Date getLastUpdatedTime() {
		return lastUpdatedTime;
	}

	/**
	 * @param lastUpdatedTime
	 *            the lastUpdatedTime to set
	 */
	public void setLastUpdatedTime(Date lastUpdatedTime) {
		this.lastUpdatedTime = lastUpdatedTime;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "UNIT_OF_MEASURE", nullable = true)
	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	@Column(name = "IS_LATEST", nullable = true)
	public String getLatest() {
		return latest;
	}

	public void setLatest(String latest) {
		this.latest = latest;
	}

	@Column(name = "EXPIRY_DATE", nullable = true)
	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

    @Column(name = "CREATION_REASON", nullable = true)
    public String getCreationReason() {
        return creationReason;
    }

    public void setCreationReason(String creationReason) {
        this.creationReason = creationReason;
    }

    @Column(name = "CREATION_ITEM_ID", nullable = true)
    public Integer getCreationItemId() {
        return creationItemId;
    }

    public void setCreationItemId(Integer creationItemId) {
        this.creationItemId = creationItemId;
    }
}