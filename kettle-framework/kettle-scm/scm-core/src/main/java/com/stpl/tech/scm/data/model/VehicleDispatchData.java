package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "VEHICLE_DISPATCH_DATA")
public class VehicleDispatchData {

	private Integer dispatchId;
	private VehicleData vehicle;
    private String docketNumber;
    private String transporterGstin;
	private Date dispatchDate;
	private String status;
	private List<ConsignmentData> consignmentList;
    private String forceEway = "N";
	private String vehicleNumber;


	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "DISPATCH_ID", unique = true, nullable = false)
	public Integer getDispatchId() {
		return dispatchId;
	}

	public void setDispatchId(Integer dispatchId) {
		this.dispatchId = dispatchId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "VEHICLE_ID", nullable = true)
	public VehicleData getVehicle() {
		return vehicle;
	}

	public void setVehicle(VehicleData vehicle) {
		this.vehicle = vehicle;
	}

	@Column(name = "DISPATCH_DATE", nullable = false)
	public Date getDispatchDate() {
		return dispatchDate;
	}

	public void setDispatchDate(Date dispatchDate) {
		this.dispatchDate = dispatchDate;
	}

    @Column(name = "DOCKET_NUMBER", nullable = true)
    public String getDocketNumber() {
        return docketNumber;
    }

    public void setDocketNumber(String docketNumber) {
        this.docketNumber = docketNumber;
    }

    @Column(name = "TRANSPORTER_GSTIN", nullable = true)
    public String getTransporterGstin() {
        return transporterGstin;
    }

    public void setTransporterGstin(String transporterGstin) {
        this.transporterGstin = transporterGstin;
    }

    @Column(name = "DISPATCH_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "dispatchId")
	public List<ConsignmentData> getConsignmentList() {
		return consignmentList;
	}

	public void setConsignmentList(List<ConsignmentData> consignmentList) {
		this.consignmentList = consignmentList;
	}

    @Column(name = "FORCE_EWAY_NOTIFICATION", nullable = false)
    public String getForceEway() {
        return forceEway;
    }

    public void setForceEway(String forceEway) {
        this.forceEway = forceEway;
    }

	public void setVehicleNumber(String vehicleNumber) {
		this.vehicleNumber = vehicleNumber;
	}

	@Column(name = "VEHICLE_NUMBER")
	public String getVehicleNumber() {
		return vehicleNumber;
	}


}
