package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.criteria.CriteriaBuilder;
import java.math.BigDecimal;

@Entity
@Table(name = "FA_GR_DATA")
@Getter
@Setter
public class FaGrData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "FA_GR_DATA_ID")
    private Integer faGrDataId;
    @Column(name = "ASSET_ID")
    private Integer assetId;
    @Column(name = "RECEIVED_QTY")
    private BigDecimal receivedQty;
    @Column(name = "SCANNED_QTY")
    private  BigDecimal scannedQty;
    @Column(name="EVENT_ID")
    private Integer eventId;


}
