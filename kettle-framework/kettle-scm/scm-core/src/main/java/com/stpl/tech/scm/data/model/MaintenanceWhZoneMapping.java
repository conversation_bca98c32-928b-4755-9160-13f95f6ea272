package com.stpl.tech.scm.data.model;

import javax.persistence.*;

@Entity
@Table(name = "MAINTENANCE_WH_ZONE_MAPPING")
public class MaintenanceWhZoneMapping {
    @Id
    @Column(name = "MAPPING_ID")
    private Integer mappingId;

    @Column(name = "ZON<PERSON>")
    private String zone;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "MAPPING_STATUS")
    private String mappingStatus;

    public Integer getMappingId() {
        return this.mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    public String getZone() {
        return this.zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getMappingStatus() {
        return mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }
}
