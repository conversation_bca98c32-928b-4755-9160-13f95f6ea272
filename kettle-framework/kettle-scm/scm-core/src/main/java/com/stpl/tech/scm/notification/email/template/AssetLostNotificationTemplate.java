package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;

import com.stpl.tech.scm.data.model.StockEventDefinitionData;
import com.stpl.tech.scm.domain.model.AssetRecoveryDefinition;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class AssetLostNotificationTemplate extends AbstractVelocityTemplate {

	private List<AssetRecoveryDefinition> AssetRecoveryDefinitionList;
	private String basePath;

	public AssetLostNotificationTemplate() {

	}

	public AssetLostNotificationTemplate(List<AssetRecoveryDefinition> AssetRecoveryDefinitionList,
										 String basePath) {
		this.AssetRecoveryDefinitionList = AssetRecoveryDefinitionList;
		this.basePath = basePath;
	}

	@Override
	public String getTemplatePath() {
		return "templates/AssetLostTemplate.html";
	}

	@Override
	public String getFilepath() {
		return basePath + "/vendor/requestOrders/" + AssetRecoveryDefinitionList.get(0).getUnitId()
				+ "_" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("AssetRecoveryDefinitionList", AssetRecoveryDefinitionList);
		return stringObjectMap;
	}

}
