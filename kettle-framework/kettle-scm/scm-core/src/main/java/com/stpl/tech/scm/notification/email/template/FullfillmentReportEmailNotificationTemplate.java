package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.data.transport.model.FullfillmentDataUnitLevel;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataWarehouseLevel;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.MathTool;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FullfillmentReportEmailNotificationTemplate extends AbstractVelocityTemplate {

    ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelLastDay;
    ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelLastThiryDay;

    List<FullfillmentDataUnitLevel> mostImpacted;

    String basePath;

    public FullfillmentReportEmailNotificationTemplate(){

    }

    public FullfillmentReportEmailNotificationTemplate(ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelLastDay,
                                                       ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelLastThiryDay, String basePath,
                                                       List<FullfillmentDataUnitLevel> mostImpacted){
        this.fullfillmentDataWarehouseLevelLastDay= fullfillmentDataWarehouseLevelLastDay;
        this.fullfillmentDataWarehouseLevelLastThiryDay = fullfillmentDataWarehouseLevelLastThiryDay;
        this.basePath = basePath;
        this.mostImpacted = mostImpacted;
    }
    @Override
    public String getTemplatePath() {
        return "templates/FullfillmentReportTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath+"/fullfillment-report/fullfillment-report-temp-"+System.currentTimeMillis()+".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String,Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("ffDetailLastDay",fullfillmentDataWarehouseLevelLastDay);
        stringObjectMap.put("ffDetailLastThD",fullfillmentDataWarehouseLevelLastThiryDay);
        stringObjectMap.put("mostImpacted",mostImpacted);
        stringObjectMap.put("mathTool",new MathTool());

        DateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
        Date endDate = AppUtils.getPreviousDate(AppUtils.getCurrentTimestamp());
        int month = AppUtils.getMonth(endDate);
        int year  = AppUtils.getYear(endDate);
        Date startDate = AppUtils.getStartDateOfMonth(year,month);
        stringObjectMap.put("prevDay",dateFormat.format(endDate));
        stringObjectMap.put("thisMonth",dateFormat.format(startDate)+" - "+dateFormat.format(endDate));
        return stringObjectMap;
    }
}
