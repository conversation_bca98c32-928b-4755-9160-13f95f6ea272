package com.stpl.tech.scm.notification.email.template;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.notification.email.template.VendorPOEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorCancelledPOEmailNotification extends EmailNotification {

    private Date deliveryDate;
    private VendorPOEmailNotificationTemplate vendorPOEmailNotificationTemplate;
	private EnvType envType;
	private String[] emails;
	private String subjectOfEmail;

	public VendorCancelledPOEmailNotification() {
	}

	public VendorCancelledPOEmailNotification(VendorPOEmailNotificationTemplate vendorPOEmailNotificationTemplate,
                                     EnvType envType, String[] emails, Date deliveryDate) {
		this.vendorPOEmailNotificationTemplate = vendorPOEmailNotificationTemplate;
		this.envType = envType;
		this.emails = emails;
		this.deliveryDate = deliveryDate;
	}

	@Override
	public String[] getToEmails() {
		if (SCMUtil.isDev(envType)) {
			return new String[] { "<EMAIL>" };
		} else {
			Set<String> mails = new HashSet<>();
			Arrays.asList(emails).forEach(email -> mails.add(email)); // adding vendor CC emails
			VendorDetail vendorDetail = vendorPOEmailNotificationTemplate.getVendorDetail();
			String unitEmail = vendorPOEmailNotificationTemplate.getDeliveryUnit().getUnitEmail();
			if (vendorDetail.getPrimaryEmail() != null) {
				mails.add(vendorDetail.getPrimaryEmail());
			}
			if (vendorDetail.getSecondaryEmail() != null) {
				mails.add(vendorDetail.getSecondaryEmail());
			}
			if (vendorPOEmailNotificationTemplate.getDispatchLocation().getContactEmail() != null) {
				mails.add(vendorPOEmailNotificationTemplate.getDispatchLocation().getContactEmail());
			}
			if(unitEmail!=null){
				mails.add(unitEmail);
			}
			String[] simpleArray = new String[mails.size()];
			return mails.toArray(simpleArray);
		}
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		subjectOfEmail = String.format("Cancelled Purchase Order From Chaayos(%s) to %s for %s",
				vendorPOEmailNotificationTemplate.getDeliveryUnit().getName(),
				vendorPOEmailNotificationTemplate.getVendorDetail().getEntityName(),
				SCMUtil.getFormattedTime(this.deliveryDate, "EEE dd MMM yyyy"));
		if (SCMUtil.isDev(envType)) {
			subjectOfEmail = " [DEV] : " + subjectOfEmail;
		}
		return subjectOfEmail;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return vendorPOEmailNotificationTemplate.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}


	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}
