package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.CostDataEntries;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.VarianceVO;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.ErrorsVO;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.util.List;

@Repository
@Primary
@Lazy
public class PriceManagementDaoImpl implements PriceManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(PriceManagementDaoImpl.class);
    private static final String CONSUMPTION_AUDIT_EXCEPTION = "INSUFFICIENT_RECEIVING";

    @PersistenceContext(unitName = "SCMDataSourcePUName")
    @Qualifier(value = "SCMDataSourceEMFactory")
    protected EntityManager manager;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private MutexFactory<Integer> factory;

    @Autowired
    private MasterDataCache cache;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    EnvProperties props;

    @Autowired
    @Qualifier("wh")
    private PriceManagementDao whPriceDao;

    @Autowired
    @Qualifier("cafe")
    private PriceManagementDao cafePriceDao;

    @Override
    public <T extends ReceivingVO> T addReceiving(T rec, boolean canellation) throws InventoryUpdateException {
        Integer unitId = rec.getUnitId();
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhKitchen)) {
            return whPriceDao.addReceiving(rec, canellation);
        } else {
            return cafePriceDao.addReceiving(rec, canellation);
        }
    }

    @Override
    public <T extends ConsumptionVO> T reduceConsumable(T rec, boolean cancellation) throws InventoryUpdateException, DataNotFoundException {
        Integer unitId = rec.getUnitId();
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhKitchen)) {
            return whPriceDao.reduceConsumable(rec, cancellation);
        } else {
            return cafePriceDao.reduceConsumable(rec, cancellation);
        }
    }

    @Override
    public <T extends ConsumptionVO> T checkConsumableData(T rec) throws InventoryUpdateException, DataNotFoundException {

        Integer unitId = rec.getUnitId();
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhKitchen)) {
            return whPriceDao.checkConsumableData(rec);
        } else {
            return cafePriceDao.checkConsumableData(rec);
        }
    }

    @Override
    public List<CostDetailData> getCurrentPrice(PriceUpdateEntryType keyType, int unitId, Integer keyId, boolean current) {
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhKitchen)) {
            return whPriceDao.getCurrentPrice(keyType, unitId, keyId, current);
        } else {
            return cafePriceDao.getCurrentPrice(keyType, unitId, keyId, current);
        }
    }

    @Override
    public List<CostDetailData> getCurrentPrices(PriceUpdateEntryType keyType, int unitId, List<Integer> keyIds, boolean current) {
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhKitchen)) {
            return whPriceDao.getCurrentPrices(keyType, unitId, keyIds, current);
        } else {
            return cafePriceDao.getCurrentPrices(keyType, unitId, keyIds, current);
        }
    }

    @Override
    public CostDetailData addNewCostDetailEntry(int unitId, InventoryItemVO item, BigDecimal oldPrice, boolean cancellation, boolean isLatest) throws InventoryUpdateException {
        ;
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhKitchen)) {
            return whPriceDao.addNewCostDetailEntry(unitId, item, oldPrice, cancellation, isLatest);
        } else {
            return cafePriceDao.addNewCostDetailEntry(unitId, item, oldPrice, cancellation, isLatest);
        }
    }

    @Override
    public CostDetailData updateCostDetailEntry(int costDetailId, BigDecimal oldPrice, InventoryItemVO item, boolean cancellation) {
        if (item.getKeyType().equals(PriceUpdateEntryType.SKU)) {
            return whPriceDao.updateCostDetailEntry(costDetailId, oldPrice, item, cancellation);
        } else {
            return cafePriceDao.updateCostDetailEntry(costDetailId, oldPrice, item, cancellation);
        }
    }

    @Override
    public List<CostDetailData> getPriceDetailsForUnit(int unitId) {
        Boolean isWhOrKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhOrKitchen)) {
            return whPriceDao.getPriceDetailsForUnit(unitId);
        } else {
            return cafePriceDao.getPriceDetailsForUnit(unitId);
        }
    }

    @Override
    public void deleteObsoletePrices(Integer unit, PriceUpdateEntryType entryType) throws InventoryUpdateException {
        Integer unitId = unit;
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if(Boolean.TRUE.equals(isWhKitchen)){
            whPriceDao.deleteObsoletePrices(unit, entryType);
        } else {
            cafePriceDao.deleteObsoletePrices(unit, entryType);
        }

    }

    @Override
    public <T extends ErrorsVO> T verifyPriceData(T rec) {
        Integer unitId = rec.getUnitId();
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if(Boolean.TRUE.equals(isWhKitchen)){
            return whPriceDao.verifyPriceData(rec);
        } else {
            return cafePriceDao.verifyPriceData(rec);
        }
    }

    @Override
    public void overrideInventory(VarianceVO all) throws DataNotFoundException, InventoryUpdateException {
        Integer unitId = all.getUnitId();
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if(Boolean.TRUE.equals(isWhKitchen)){
            whPriceDao.overrideInventory(all);
        } else {
            cafePriceDao.overrideInventory(all);
        }
    }

    @Override
    public List<CostDetailData> fixPricing(List<CostDetailData> currentPriceList) {
        if (currentPriceList.size() > 0) {
            Integer unitId = currentPriceList.get(0).getUnitId();
            Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
            if(Boolean.TRUE.equals(isWhKitchen)){
                return whPriceDao.fixPricing(currentPriceList);
            } else {
                return cafePriceDao.fixPricing(currentPriceList);
            }
        }
        return currentPriceList;
    }

    @Override
    public <T extends ConsumptionVO> T updateLatestFlag(T rec) {
        Integer unitId = rec.getUnitId();
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhKitchen)) {
            return whPriceDao.updateLatestFlag(rec);
        } else {
            return cafePriceDao.updateLatestFlag(rec);
        }
    }

    @Override
    public CostDataEntries getOrCreateCurrentPrices(PriceUpdateEntryType keyType, int unitId, List<Integer> keyIds, boolean current, boolean createMissig,
                                                    Boolean missingToPickFromProduct) throws DataNotFoundException {
        Boolean isWhKitchen = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(unitId));
        if (Boolean.TRUE.equals(isWhKitchen)) {
            return whPriceDao.getOrCreateCurrentPrices(keyType, unitId, keyIds, current, createMissig, missingToPickFromProduct);
        } else {
            return cafePriceDao.getOrCreateCurrentPrices(keyType, unitId, keyIds, current, createMissig, missingToPickFromProduct);
        }
    }

    @Override
    public List<CostDetailData> getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType keyType, List<Integer> keyIds,
                                                                            List<String> regions, Boolean isLatest) throws DataNotFoundException{
        if(PriceUpdateEntryType.PRODUCT.equals(keyType)){
           return cafePriceDao.getAllCurrentPriceForAnyUnitInRegionByKeyId(keyType,keyIds, regions,isLatest);
        }else{
           return whPriceDao.getAllCurrentPriceForAnyUnitInRegionByKeyId(keyType,keyIds, regions,isLatest);
        }
    }



}
