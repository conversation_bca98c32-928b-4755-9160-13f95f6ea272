package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderServiceReceiveMappingData;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemData;
import com.stpl.tech.scm.domain.model.PaymentRequest;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ServiceReceiveManagementDao extends SCMAbstractDao {

	List<ServiceReceivedData> findServiceReceives(Integer bccId,Integer vendorId, Integer locationId, Date startDate, Date endDate, List<Integer> costElements, Integer serviceOrderId , boolean isShort);

	List<ServiceReceivedData> getLinkedSrForSo(Integer vendorId, Integer locationId, Date startDate, Date endDate, List<Integer> costElements, Integer paymentRequestId);

    List<ServiceReceivedData> findServiceReceivesForPayment(Integer vendorId, Integer companyId, Integer locationId, Date startDate, Date endDate, List<Integer> costElements);

	List<ServiceReceivedData> findServiceReceivingForPaymentRequest(Integer paymentRequestId);

	String getBudgetCategory(int costElementId);

	List<ServiceOrderData> findSoForPr(Integer paymentRequestId);

	void removeServiceReceiveDrillDowns(Integer id);

	List<ServiceReceivedItemData> findSrItemsBySoId(Integer soId);

    List<ServiceReceivedItemData> findSrItemsBySoIds(Set<Integer> soIds);

	List<Integer> getSrIds(Set<Integer> srIds);

	List<ServiceOrderData> getSoData(Set<Integer> soIds);

    List<String> findSrByPrIds(PaymentRequest paymentRequest);

    List<ServiceOrderServiceReceiveMappingData> findSrsWithSoId(Integer tempSoId);

	List<ServiceReceivedData> findAllSrs(List<Integer> srIds);
}
