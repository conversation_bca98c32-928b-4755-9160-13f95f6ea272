package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 10-06-2016.
 */
@Entity
@Table(name = "REFERENCE_ORDER")
public class ReferenceOrderData {

    private Integer id;
    private Date generationTime;
    private Date initiationTime;
    private Date lastUpdateTime;
    private int requestUnitId;
    private int generatedBy;
    private Integer fulfillmentUnitId;
    private Date fulfillmentDate;
    private String status;
    private String comment;
    private List<ReferenceOrderMenuItemData> referenceOrderMenuItemDatas = new ArrayList<ReferenceOrderMenuItemData>(0);
    private List<ReferenceOrderScmItemData> referenceOrderScmItemDatas = new ArrayList<ReferenceOrderScmItemData>(0);
    private Integer numberOfDays;
    private String raiseBy;
    private String refOrderSource;
    private List<ForecastReportResponse> forecastReportResponses = new ArrayList<>(0);
    private List<ForecastReportScmResponse> forecastReportScmResponses = new ArrayList<>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "REFERENCE_ORDER_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "REQUEST_UNIT_ID", nullable = false)
    public int getRequestUnitId() {
        return requestUnitId;
    }

    public void setRequestUnitId(int requestUnitId) {
        this.requestUnitId = requestUnitId;
    }

    @Column(name = "GENERATED_BY", nullable = false)
    public int getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(int generatedBy) {
        this.generatedBy = generatedBy;
    }

    @Column(name = "FULFILLMENT_UNIT_ID", nullable = true)
    public Integer getFulfillmentUnitId() {
        return fulfillmentUnitId;
    }

    public void setFulfillmentUnitId(Integer fulfillmentUnitId) {
        this.fulfillmentUnitId = fulfillmentUnitId;
    }

    @Column(name = "REFERENCE_ORDER_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "referenceOrderData")
    public List<ReferenceOrderMenuItemData> getReferenceOrderMenuItemDatas() {
        return referenceOrderMenuItemDatas;
    }

    public void setReferenceOrderMenuItemDatas(List<ReferenceOrderMenuItemData> referenceOrderMenuItemDatas) {
        this.referenceOrderMenuItemDatas = referenceOrderMenuItemDatas;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "referenceOrderData")
    public List<ReferenceOrderScmItemData> getReferenceOrderScmItemDatas() {
        return referenceOrderScmItemDatas;
    }

    public void setReferenceOrderScmItemDatas(List<ReferenceOrderScmItemData> referenceOrderScmItemDatas) {
        this.referenceOrderScmItemDatas = referenceOrderScmItemDatas;
    }

    @Column(name = "FULFILLMENT_DATE", nullable = false)
    public Date getFulfillmentDate() {
        return fulfillmentDate;
    }

    public void setFulfillmentDate(Date fulfillmentDate) {
        this.fulfillmentDate = fulfillmentDate;
    }

    @Column(name = "COMMENT", nullable = true, length = 1000)
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Column(name = "INITIATION_TIME", nullable = true)
    public Date getInitiationTime() {
        return initiationTime;
    }

    public void setInitiationTime(Date initiationTime) {
        this.initiationTime = initiationTime;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @Column(name ="NUMBER_OF_DAYS",nullable = false )//nullable = false
    public Integer getNumberOfDays() {
        return numberOfDays;
    }

    public void setNumberOfDays(Integer numberOfDays) {
        this.numberOfDays = numberOfDays;
    }

    @Column(name="RAISE_BY",nullable = false)
    public String getRaiseBy() {
        return raiseBy;
    }

    public void setRaiseBy(String raiseBy) {
        this.raiseBy = raiseBy;
    }

    @Column(name="REF_ORDER_SOURCE")
    public String getRefOrderSource() {
        return refOrderSource;
    }

    public void setRefOrderSource(String refOrderSource) {
        this.refOrderSource = refOrderSource;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "referenceOrderData")
    public List<ForecastReportResponse> getForecastReportResponses() {
        return forecastReportResponses;
    }

    public void setForecastReportResponses(List<ForecastReportResponse> forecastReportResponses) {
        this.forecastReportResponses = forecastReportResponses;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "referenceOrderData")
    public List<ForecastReportScmResponse> getForecastReportScmResponses() {
        return forecastReportScmResponses;
    }

    public void setForecastReportScmResponses(List<ForecastReportScmResponse> forecastReportScmResponses) {
        this.forecastReportScmResponses = forecastReportScmResponses;
    }
}
