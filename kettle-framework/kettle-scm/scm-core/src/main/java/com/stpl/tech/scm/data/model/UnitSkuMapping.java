/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * UnitSkuMapping generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_SKU_MAPPING")
public class UnitSkuMapping implements java.io.Serializable {

	private Integer unitSkuMappingId;
	private int unitId;
	private int skuId;
	private String mappingStatus;
	private String createdBy;
	private Date createdAt;
	private String updatedBy;
	private Date updatedAt;
	private List<UnitSkuVendorMapping> vendors = new ArrayList<UnitSkuVendorMapping>(0);
	private String profile;
	private Integer inventoryList;
	private Integer productionUnit;
	private Integer packagingId;
	private String taxCode;
	private Date voDisContinuedFrom;
	private Date roDisContinuedFrom;

	public UnitSkuMapping() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_SKU_MAPPING_ID", unique = true, nullable = false)
	public Integer getUnitSkuMappingId() {
		return this.unitSkuMappingId;
	}

	public void setUnitSkuMappingId(Integer unitProductMappingId) {
		this.unitSkuMappingId = unitProductMappingId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "SKU_ID", nullable = false)
	public int getSkuId() {
		return this.skuId;
	}

	public void setSkuId(int productId) {
		this.skuId = productId;
	}

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return this.mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	@Column(name = "CREATED_BY", length = 50)
	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_AT", nullable = true, length = 19)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "UPDATED_BY", length = 50)
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATED_AT", nullable = true, length = 19)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitSkuMapping")
	public List<UnitSkuVendorMapping> getVendors() {
		return vendors;
	}

	public void setVendors(List<UnitSkuVendorMapping> vendors) {
		this.vendors = vendors;
	}

	@Column(name = "PROFILE", nullable = true, length = 32)
	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	@Column(name = "INVENTORY_LIST_ID", nullable = false)
	public Integer getInventoryList() {
		return inventoryList;
	}

	public void setInventoryList(Integer inventoryList) {
		this.inventoryList = inventoryList;
	}

	@Column(name ="PRODUCTION_UNIT")
	public Integer getProductionUnit() {
		return productionUnit;
	}

	public void setProductionUnit(Integer productionUnit) {
		this.productionUnit = productionUnit;
	}

	@Column(name ="PACKAGING_ID")
	public Integer getPackagingId() {
		return packagingId;
	}

	public void setPackagingId(Integer packagingId) {
		this.packagingId = packagingId;
	}

	@Column(name="TAX_CODE")
	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@Column(name = "VO_DISCONTINUED_FROM", nullable = true)
	public Date getVoDisContinuedFrom() {
		return voDisContinuedFrom;
	}

	public void setVoDisContinuedFrom(Date voDisContinuedFrom) {
		this.voDisContinuedFrom = voDisContinuedFrom;
	}

	@Column(name = "RO_DISCONTINUED_FROM", nullable = true)
	public Date getRoDisContinuedFrom() {
		return roDisContinuedFrom;
	}

	public void setRoDisContinuedFrom(Date roDisContinuedFrom) {
		this.roDisContinuedFrom = roDisContinuedFrom;
	}
}
