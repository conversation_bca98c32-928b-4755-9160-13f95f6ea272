package com.stpl.tech.scm.core.templates;

import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.domain.model.SalesPerformaInvoice;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import java.util.HashMap;
import java.util.Map;

public class DebitNoteTemplate extends AbstractVelocityTemplate {

    private SalesPerformaInvoice salesPerformaInvoice;
    private String basePath;
    private Company company;
    private Unit unitData;
    private String totalAmountInWords;
    private String barCodeLink;
    private String businessDate;
    private String debitNoteNo;


    public DebitNoteTemplate() {
    }

    public DebitNoteTemplate(SalesPerformaInvoice salesPerformaInvoice, String basePath,
                                         Company company, Unit unitData, String totalAmountInWords, String barCodeLink, String businessDate,String debitNoteNo) {
        this.salesPerformaInvoice = salesPerformaInvoice;
        this.basePath = basePath;
        this.company=company;
        this.unitData=unitData;
        this.totalAmountInWords=totalAmountInWords;
        this.barCodeLink = barCodeLink;
        this.businessDate = businessDate;
        this.debitNoteNo = debitNoteNo;
    }

    @Override
    public String getTemplatePath() {
        return "templates/B2BInvoiceDebitNote.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + salesPerformaInvoice.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("invoice", salesPerformaInvoice);
        stringObjectMap.put("totalAmountInWords", totalAmountInWords);
        stringObjectMap.put("unitData",unitData);
        stringObjectMap.put("company",company);
        stringObjectMap.put("dateTool", new DateTool());
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("barCodeLink", barCodeLink);
        stringObjectMap.put("businessDate", businessDate);
        stringObjectMap.put("debitNoteNo", debitNoteNo);
        stringObjectMap.put("basePath", basePath);
        return stringObjectMap;
    }
}
