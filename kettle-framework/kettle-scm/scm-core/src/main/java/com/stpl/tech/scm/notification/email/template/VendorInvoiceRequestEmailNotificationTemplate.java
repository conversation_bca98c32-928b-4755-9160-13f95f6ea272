package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorGR;
import com.stpl.tech.scm.domain.model.VendorGRItem;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.poi.ss.usermodel.CellStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorInvoiceRequestEmailNotificationTemplate extends AbstractVelocityTemplate {

    private final static Logger LOG = LoggerFactory.getLogger(VendorInvoiceRequestEmailNotificationTemplate.class);

    private VendorDetail vendorDetail;
    private PaymentRequest paymentRequest;
    private List<VendorGR> vendorGRs;
    private String basePath;
    private String filePath;
    private String fileName;
    private String mimeType = AppConstants.EXCEL_MIME_TYPE;
    private WorkbookContext workbookCtx;
    private EnvType envType;
    String receivingUnit;
    private boolean generated = false;

    public VendorInvoiceRequestEmailNotificationTemplate() {

    }

    public VendorInvoiceRequestEmailNotificationTemplate(WorkbookContextFactory ctxFactory, VendorDetail vendorDetail, PaymentRequest paymentRequest,
                                                         String basePath, EnvType envType, String receivingUnit, List<VendorGR> vendorGRs) {
        this.vendorDetail = vendorDetail;
        this.basePath = basePath;
        this.paymentRequest = paymentRequest;
        this.filePath = basePath + "/vendor_invoice_request" + File.separator;
        this.fileName = "Vendor_Invoice_Request_" + paymentRequest.getPaymentRequestId() + ".xlsx";
        this.workbookCtx = ctxFactory.createWorkbook();
        this.envType = envType;
        this.receivingUnit = receivingUnit;
        this.vendorGRs = vendorGRs;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorInvoiceRequestEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor/invoiceRequest/" + vendorDetail.getVendorId() + "/" + paymentRequest.getPaymentRequestId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendorDetail", vendorDetail);
        stringObjectMap.put("paymentRequest", paymentRequest);
        return stringObjectMap;
    }

    public String getShetPath(){
        return filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public boolean isGenerated(){
        return generated;
    }

    public String getMimeType() {
        return mimeType;
    }

    public VendorDetail getVendorDetail() {
        return vendorDetail;
    }


    public void renderInvoiceRequestSheet() {

        SheetContext sheetCtx = workbookCtx.createSheet("Delivery Challan Data");
        Style headerStyle = SCMUtil.getHeaderStyle(workbookCtx);

        sheetCtx.nextRow().mergeCells(10).text(this.vendorDetail.getEntityName()); // heading
        sheetCtx.nextRow().mergeCells(10).text(this.receivingUnit); // heading
        sheetCtx.nextRow().mergeCells(10).text("PR ID: " + this.paymentRequest.getPaymentRequestId().toString()); // heading
        sheetCtx.nextRow().setTextStyle(headerStyle)
                .text("GR ID")
                .text("Delivery Challan Number").setColumnWidth(SCMUtil.NAME_COLUMN_WIDTH)
                .text("Delivery Challan Date").setColumnWidth(SCMUtil.NAME_COLUMN_WIDTH)
                .text("Product ID")
                .text("Product Name").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("HSN Code").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Unit of Measure").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("GR Quantity").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Unit Price").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Tax").setColumnWidth(SCMUtil.COLUMN_WIDTH)
                .text("Total Amount").setColumnWidth(SCMUtil.COLUMN_WIDTH);

        BigDecimal totalAmount = BigDecimal.ZERO;

        for (VendorGR vendorGR : vendorGRs) {
            for (VendorGRItem vendorGRItem : vendorGR.getGrItems()) {
                sheetCtx.nextRow().text(vendorGR.getId().toString())
                        .text(vendorGR.getReceiptNumber())
                        .text(SCMUtil.getFormattedDate(vendorGR.getGrDocumentDate()))
                        .number(vendorGRItem.getSkuId())
                        .text(vendorGRItem.getSkuName())
                        .text(vendorGRItem.getHsn())
                        .text(vendorGRItem.getUom())
                        .number(vendorGRItem.getReceivedQuantity())
                        .number(vendorGRItem.getUnitPrice())
                        .number(vendorGRItem.getTotalTax())
                        .number(vendorGRItem.getAmountPaid());
                totalAmount = SCMUtil.add(totalAmount, vendorGRItem.getAmountPaid());
            }
        }
        sheetCtx.nextRow().nextRow().text("")
                .text("")
                .text("")
                .text("")
                .text("")
                .text("")
                .text("")
                .text("")
                .text("")
                .text("Total Amount to Pay")
                .number(totalAmount);
        BigDecimal totalExtraCharge = BigDecimal.ZERO;

        SheetContext extraChangesSheet = workbookCtx.createSheet("Extra Charges");
        extraChangesSheet.nextRow().setTextStyle(headerStyle)
                .text("GR ID")
                .text("Delivery Challan Number")
                .text("Extra Changes");
        for (VendorGR vendorGR : vendorGRs) {
            extraChangesSheet.nextRow().text(vendorGR.getId().toString())
                        .text(vendorGR.getReceiptNumber())
                        .number(vendorGR.getExtraCharges()==null? BigDecimal.ZERO:vendorGR.getExtraCharges());
            totalExtraCharge = SCMUtil.add(totalExtraCharge, vendorGR.getExtraCharges()==null? BigDecimal.ZERO:vendorGR.getExtraCharges());
        }
        extraChangesSheet.nextRow().nextRow().text("")
                .text("Total Extra Charge")
                .number(totalExtraCharge);
    }

    public void generateReport(byte[] content) throws IOException {
        String writtenPath = SCMUtil.write(content, this.filePath, "", "",
                this.fileName, LOG);
        if (writtenPath != null) {
            this.filePath = writtenPath;
            this.generated = true;
        }
    }

    public WorkbookContext getWorkbook(){
        return this.workbookCtx;
    }

}
