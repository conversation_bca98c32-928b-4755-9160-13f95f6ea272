/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.mongo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EFiledList {

    @SerializedName("arn")
    private String arnNumber;

    @SerializedName("ret_prd")
    private String taxPeriod;

    @SerializedName("mof")
    private String modeOfFiling;

    @SerializedName("dof")
    private String dateOfFiling;

    @SerializedName("rtntype")
    private String returnType;

    @SerializedName("status")
    private String filingStatus;

    @SerializedName("valid")
    private String isValid;
}
