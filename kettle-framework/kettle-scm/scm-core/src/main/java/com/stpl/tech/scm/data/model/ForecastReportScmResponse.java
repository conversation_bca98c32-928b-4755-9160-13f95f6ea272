package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "REGULAR_ORDERING_SCM_FORECAST_DATA")
public class ForecastReportScmResponse {

    private Integer scmForecastId;
    private Date date;
    private String cafeId;
    private Integer skuId;
    private String skuName;
    private BigDecimal actual;
    private BigDecimal predictedLpi;
    private BigDecimal predictedBaseline;
    private BigDecimal predictedUpi;
    private BigDecimal predictedFinal;
    private BigDecimal price;
    private BigDecimal actualValue;
    private BigDecimal predictedBaselineValue;
    private BigDecimal value;
    private BigDecimal safetyStock;
    private Date refreshDate;
    private ReferenceOrderData referenceOrderData;
    private String suggestionsRequestedBy;
    private String nonFunctional;

    public ForecastReportScmResponse() {
    }

    public ForecastReportScmResponse(Date date, String cafeId, Integer skuId, String skuName, BigDecimal actual, BigDecimal predictedLpi, BigDecimal predictedBaseline,
                                     BigDecimal predictedUpi, BigDecimal predictedFinal, BigDecimal price, BigDecimal actualValue, BigDecimal predictedBaselineValue,
                                     BigDecimal value, BigDecimal safetyStock, Date refreshDate, String suggestionsRequestedBy) {
        this.date = date;
        this.cafeId = cafeId;
        this.skuId = skuId;
        this.skuName = skuName;
        this.actual = actual;
        this.predictedLpi = predictedLpi;
        this.predictedBaseline = predictedBaseline;
        this.predictedUpi = predictedUpi;
        this.predictedFinal = predictedFinal;
        this.price = price;
        this.actualValue = actualValue;
        this.predictedBaselineValue = predictedBaselineValue;
        this.value = value;
        this.safetyStock = safetyStock;
        this.refreshDate = refreshDate;
        this.suggestionsRequestedBy = suggestionsRequestedBy;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SCM_FORECAST_ID")
    public Integer getScmForecastId() {
        return scmForecastId;
    }

    public void setScmForecastId(Integer scmForecastId) {
        this.scmForecastId = scmForecastId;
    }

    @Column(name = "DATE")
    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    @Column(name = "CAFE_ID")
    public String getCafeId() {
        return cafeId;
    }

    public void setCafeId(String cafeId) {
        this.cafeId = cafeId;
    }

    @Column(name = "SKU_ID")
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "SKU_NAME")
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "ACTUAL")
    public BigDecimal getActual() {
        return actual;
    }

    public void setActual(BigDecimal actual) {
        this.actual = actual;
    }

    @Column(name = "PREDICTED_LPI")
    public BigDecimal getPredictedLpi() {
        return predictedLpi;
    }

    public void setPredictedLpi(BigDecimal predictedLpi) {
        this.predictedLpi = predictedLpi;
    }

    @Column(name = "PREDICTED_BASELINE")
    public BigDecimal getPredictedBaseline() {
        return predictedBaseline;
    }

    public void setPredictedBaseline(BigDecimal predictedBaseline) {
        this.predictedBaseline = predictedBaseline;
    }

    @Column(name = "PREDICTED_UPI")
    public BigDecimal getPredictedUpi() {
        return predictedUpi;
    }

    public void setPredictedUpi(BigDecimal predictedUpi) {
        this.predictedUpi = predictedUpi;
    }

    @Column(name = "PREDICTED_FINAL")
    public BigDecimal getPredictedFinal() {
        return predictedFinal;
    }

    public void setPredictedFinal(BigDecimal predictedFinal) {
        this.predictedFinal = predictedFinal;
    }

    @Column(name = "PRICE")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "ACTUAL_VALUE")
    public BigDecimal getActualValue() {
        return actualValue;
    }

    public void setActualValue(BigDecimal actualValue) {
        this.actualValue = actualValue;
    }

    @Column(name = "PREDICTED_BASELINE_VALUE")
    public BigDecimal getPredictedBaselineValue() {
        return predictedBaselineValue;
    }

    public void setPredictedBaselineValue(BigDecimal predictedBaselineValue) {
        this.predictedBaselineValue = predictedBaselineValue;
    }

    @Column(name = "VALUE")
    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    @Column(name = "SAFETY_STOCK")
    public BigDecimal getSafetyStock() {
        return safetyStock;
    }

    public void setSafetyStock(BigDecimal safetyStock) {
        this.safetyStock = safetyStock;
    }

    @Column(name = "REFRESH_DATE")
    public Date getRefreshDate() {
        return refreshDate;
    }

    public void setRefreshDate(Date refreshDate) {
        this.refreshDate = refreshDate;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REFERENCE_ORDER_ID")
    public ReferenceOrderData getReferenceOrderData() {
        return referenceOrderData;
    }

    public void setReferenceOrderData(ReferenceOrderData referenceOrderData) {
        this.referenceOrderData = referenceOrderData;
    }

    @Column(name = "SUGGESTIONS_REQUESTED_BY")
    public String getSuggestionsRequestedBy() {
        return suggestionsRequestedBy;
    }

    public void setSuggestionsRequestedBy(String suggestionsRequestedBy) {
        this.suggestionsRequestedBy = suggestionsRequestedBy;
    }

    @Column(name = "NON_FUNCTIONAL")
    public String getNonFunctional() {
        return nonFunctional;
    }

    public void setNonFunctional(String nonFunctional) {
        this.nonFunctional = nonFunctional;
    }
}
