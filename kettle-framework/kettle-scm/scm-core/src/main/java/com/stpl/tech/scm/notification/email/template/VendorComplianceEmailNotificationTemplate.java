/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VendorComplianceEmailNotificationTemplate extends AbstractVelocityTemplate {
    private String basePath;
    private List<String> blockedVendors;
    private List<String> unBlockedVendors;


    public VendorComplianceEmailNotificationTemplate(String basePath, List<String> blockedVendors, List<String> unBlockedVendors) {
        this.basePath = basePath;
        this.blockedVendors = blockedVendors;
        this.unBlockedVendors = unBlockedVendors;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorComplianceEmailTemplate.html";
    }


    @Override
    public String getFilepath() {
        return basePath + "/vendor_compliance" + "/" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("blockedVendors", blockedVendors);
        stringObjectMap.put("unBlockedVendors", unBlockedVendors);
        return stringObjectMap;
    }
}
