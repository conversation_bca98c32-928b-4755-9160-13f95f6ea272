package com.stpl.tech.scm.data.model;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "CONSIGNMENT_DATA")
public class ConsignmentData {

	private Integer consigmentId;
	private String status;
	private String consignmentType;
	private List<EwayBillData> ewaybills = new ArrayList<>();
	private Integer dispatchId;
	private Integer ConsignmentUnitId;


	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CONSIGNMENT_ID", unique = true, nullable = false)
	public Integer getConsigmentId() {
		return consigmentId;
	}

	public void setConsigmentId(Integer consigmentId) {
		this.consigmentId = consigmentId;
	}

	@Column(name = "CONSIGNMENT_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "consignmentId")
	public List<EwayBillData> getEwaybills() {
		return ewaybills;
	}

	public void setEwaybills(List<EwayBillData> ewaybills) {
		this.ewaybills = ewaybills;
	}

	@Column(name = "DISPATCH_ID", nullable = false)
	public Integer getDispatchId() {
		return dispatchId;
	}

	public void setDispatchId(Integer dispatchId) {
		this.dispatchId = dispatchId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public Integer getConsignmentUnitId() {
		return ConsignmentUnitId;
	}

	public void setConsignmentUnitId(Integer consignmentUnitId) {
		ConsignmentUnitId = consignmentUnitId;
	}

	@Column(name = "CONSIGNMENT_TYPE", nullable = false)
	public String getConsignmentType() {
		return consignmentType;
	}

	public void setConsignmentType(String consignmentType) {
		this.consignmentType = consignmentType;
	}

	
}
