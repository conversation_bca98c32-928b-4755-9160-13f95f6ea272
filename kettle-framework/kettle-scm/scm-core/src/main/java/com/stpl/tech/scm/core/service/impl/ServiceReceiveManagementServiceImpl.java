package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.ServiceReceiveManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.ServiceReceiveVO;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.dao.ServiceReceiveManagementDao;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.BudgetAuditDetailData;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.CostCenterData;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderItemData;
import com.stpl.tech.scm.data.model.ServiceOrderServiceReceiveMappingData;
import com.stpl.tech.scm.data.model.ServiceOrderStatusEventData;
import com.stpl.tech.scm.data.model.ServiceReceivedData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemDrilldownData;
import com.stpl.tech.scm.data.model.ServiceReceivedItemTaxData;
import com.stpl.tech.scm.domain.model.AdvancePaymentStatus;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.BulkRequestVO;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.BusinessCostCenterType;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SREmailShort;
import com.stpl.tech.scm.domain.model.ServiceOrderEmailShort;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceReceive;
import com.stpl.tech.scm.domain.model.ServiceReceiveItem;
import com.stpl.tech.scm.domain.model.ServiceReceiveShort;
import com.stpl.tech.scm.domain.model.ServiceReceiveTaxDetail;
import com.stpl.tech.scm.domain.model.ServiceReceiveType;
import com.stpl.tech.scm.domain.model.ServiceReceivedItemDrilldown;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.state.SCMTransitionStatus;
import com.stpl.tech.scm.notification.email.template.SrEmailNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ServiceReceiveManagementServiceImpl implements ServiceReceiveManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(ServiceReceiveManagementServiceImpl.class);

	@Autowired
	private ServiceReceiveManagementDao serviceReceiveManagementDao;

	@Autowired
	private ServiceOrderManagementDao serviceOrderManagementDao;

	@Autowired
	private EnvProperties props;

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private SCMNotificationService notificationService;

	@Autowired
	private PaymentRequestManagementService paymentRequestManagementService;



	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Integer createServiceReceive(ServiceReceiveVO srVO) throws SumoException {
		//set of all unique SO from all SR items
		Set<ServiceOrderData> serviceOrderDataSet = new HashSet<>();
		for (ServiceReceiveItem item : srVO.getItems()) {
			//get SO for the particular SR_VO_ITEM
			ServiceOrderData serviceOrderData = serviceOrderManagementDao.find(ServiceOrderData.class, item.getServiceOrderId());
			if (serviceOrderData != null) {
				if (!serviceOrderDataSet.contains(item.getServiceOrderId())) {
					serviceOrderDataSet.add(serviceOrderData);
					List<AdvancePaymentData> advancePaymentDataList = serviceOrderData.getAdvancePaymentDatas();
					Set<Integer> adjustInitiatedList = new HashSet<>();
					Set<Integer> refundInitiatedList = new HashSet<>();
					if (Objects.nonNull(advancePaymentDataList) && !advancePaymentDataList.isEmpty()) {
						for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
							if (advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.ADJUST_INITIATED.value())) {
								adjustInitiatedList.add(advancePaymentData.getAdvancePaymentId());
							}
							if (advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.REFUND_INITIATED.value())) {
								refundInitiatedList.add(advancePaymentData.getAdvancePaymentId());
							}
						}
					}
					if (!adjustInitiatedList.isEmpty()) {
						throw new SumoException("Vendor Advance related to this SO is in Adjustment stage : "+
								Arrays.asList(adjustInitiatedList.toArray()) +" ..!","Please Complete the adjustment Process and Do the receiving...!");
					}
					if (!refundInitiatedList.isEmpty()) {
						throw new SumoException("Vendor Advance related to this SO is in Refund stage : "+
								Arrays.asList(refundInitiatedList.toArray()) +" ..!","Please Complete the Refund Process and Do the receiving...!");
					}
				}
			}
		}
		//for Service Order Check if threshold has been satisfied
		for (ServiceOrderData item : serviceOrderDataSet) {
			BigDecimal totalAmount = item.getTotalAmount();
			BigDecimal totalRecievingAmount = BigDecimal.ZERO;
			BigDecimal updatedAmount = BigDecimal.ZERO;
			for (ServiceReceiveItem item1 : srVO.getItems()) {
				if (item1.getServiceOrderId().equals(item.getId())) {
					updatedAmount = SCMUtil.add(updatedAmount, item1.getTotalAmount());
				}
			}
			//get all previously received items against that so
			List<ServiceReceivedItemData> serviceReceivedItemData = serviceOrderManagementDao.getServiceReceivedItemData(item.getId());
			if (serviceReceivedItemData != null && !serviceReceivedItemData.isEmpty()) {
				for (ServiceReceivedItemData itemData : serviceReceivedItemData) {
					if (itemData.getServiceOrderId().equals(item.getId())) {
						totalRecievingAmount = SCMUtil.add(totalRecievingAmount, itemData.getTotalAmount());
					}
				}
			}
			updatedAmount = SCMUtil.add(updatedAmount, totalRecievingAmount);
			BigDecimal deviationAmount = SCMUtil.add(SCMUtil.multiplyWithScale10(SCMUtil.divide(totalAmount, new BigDecimal(100)), new BigDecimal(5)), totalAmount);
			if (updatedAmount.compareTo(deviationAmount) > 0) {
				throw new SumoException("Rejected because Amount Entered is greater then 5% of Total Amount as Total Amount is: " + totalAmount + " And Final Receiving Amount is: " + updatedAmount + "" +
						" For Service Order: " + item.getId());
			}
		}
		//creating SR entity object, sr items entity objects, updating budget, mapping SO_SR, updating sr_item tax table
		ServiceReceivedData srdata = createServiceReceiveData(srVO);
		// updates the SO items to received qty
		updateServiceOrders(srdata);

		Boolean sendMail = validateForMail(srdata);
		if (srdata.getType().equalsIgnoreCase("REGULAR")) {
			if (sendMail) {
				LOG.info("Sending Service Received Email");
				sendSrEmail(srdata);
			}
			else {
				LOG.info("Not sending Sr Email");
			}
		}
		return srdata.getServiceReceivedId();
	}

	private void sendSrEmail(ServiceReceivedData srdata) {
		Map<Integer, List<SREmailShort>> map = getSoSrMap(srdata);
		Map<Integer, ServiceOrderEmailShort> soDataMap = getSoDataMap(map.keySet());
		String subject = getSubjectOfEmail(srdata,map,soDataMap);
		SrEmailNotificationTemplate emailTemplate = new SrEmailNotificationTemplate(
				srdata, masterDataCache.getEmployee(srdata.getCreatedBy()), props.getBasePath(), masterDataCache.getCompany(srdata.getCompanyId()).getName(),
				props.getEnvType(), map, soDataMap,subject);
		try {
			String[] emails = new String[]{"<EMAIL>"};
			notificationService.sendSRNotification(emailTemplate, emails);
		} catch (Exception e) {
			LOG.error("Error Occurred while send Service Received Email :: ", e);
		}
	}

	private String getSubjectOfEmail(ServiceReceivedData srdata, Map<Integer, List<SREmailShort>> map, Map<Integer, ServiceOrderEmailShort> soDataMap) {
		String subject = "Service Received";
		try {
			subject = subject +" - "+ srdata.getServiceReceivedId()+" - Vendors "+getVendorNameOrCount(soDataMap)+" - Units "+getUnitNamesOrCount(map)+
					" Created by : "+masterDataCache.getEmployee(srdata.getCreatedBy())+" on : "+srdata.getCreatedAt();
		}
		catch (Exception e) {
			LOG.error("Error Occurred While generating the subject of email of SR :: ",e);
		}
		return subject;
	}

	private String getUnitNamesOrCount(Map<Integer, List<SREmailShort>> map) {
		Set<String> unitNames = new HashSet<>();
		for (Map.Entry<Integer,List<SREmailShort>> data : map.entrySet()) {
			for (SREmailShort srEmailShort : data.getValue()) {
				unitNames.add(srEmailShort.getBusinessCostCenterName());
			}
		}
		if (unitNames.size() > 1) {
			return "["+unitNames.size()+"]";
		}
		else {
			return Arrays.toString(unitNames.toArray());
		}
	}

	private String getVendorNameOrCount(Map<Integer, ServiceOrderEmailShort> soDataMap) {
		Set<String> vendorNames = new HashSet<>();
		for (Map.Entry<Integer,ServiceOrderEmailShort> data : soDataMap.entrySet()) {
			vendorNames.add(data.getValue().getVendorName());
		}
		if (vendorNames.size() > 1) {
			return "["+vendorNames.size()+"]";
		}
		else {
			return Arrays.toString(vendorNames.toArray());
		}
	}


	private Boolean validateForMail(ServiceReceivedData srdata) {
		// Hardcoded BCC ID's of BCC_CODE(26130,24002,26100,26185,26282,26299,26327,26422,26427,26429,26426,26495,26496)
		List<Integer> unitList = new ArrayList<>(Arrays.asList(645,644,580,579,578,573,485,454,436,314,203,172,22));
		for (ServiceReceivedItemData data : srdata.getServiceItemList()) {
			if (unitList.contains(data.getBusinessCostCenterId())) {
				return true;
			}
		}
		return false;
	}

	private Map<Integer, ServiceOrderEmailShort> getSoDataMap(Set<Integer> soIds) {
		Map<Integer, ServiceOrderEmailShort> result = new HashMap<>();
		List<ServiceOrderData> soData = serviceReceiveManagementDao.getSoData(soIds);
		for (ServiceOrderData data : soData) {
			ServiceOrderEmailShort entry = new ServiceOrderEmailShort();
			entry.setSoId(data.getId());
			entry.setCreatedBy(masterDataCache.getEmployee(data.getGeneratedBy()) +"["+data.getGeneratedBy()+"]");
			entry.setSoType(data.getType());
			VendorDetail vendor = scmCache.getVendorDetail(data.getVendorId());
			Optional<VendorDispatchLocation> dispatchLocation = vendor.getDispatchLocations().stream()
					.filter(location -> location.getDispatchId().equals(data.getDispatchLocationId())).findAny();
			entry.setDispatchLocation(dispatchLocation.isPresent() ? dispatchLocation.get() : null);
			entry.setVendorName(vendor.getEntityName());
			CostCenterData costCenterData = serviceOrderManagementDao.find(CostCenterData.class,data.getCostCenterId());
			entry.setCostCenterName(costCenterData.getCostCenterName());
			BigDecimal pendingAmount = BigDecimal.ZERO;
			for (ServiceOrderItemData itemData : data.getServiceOrderItemDataList()) {
				BigDecimal pendingQuantity = itemData.getRequestedQuantity().subtract(itemData.getReceivedQuantity());
				BigDecimal itemPendingAmount = pendingQuantity.multiply(itemData.getUnitPrice());
				BigDecimal itemPendingTax = AppUtils.divide(itemPendingAmount.multiply(itemData.getTaxRate()),new BigDecimal(100));
				pendingAmount = pendingAmount.add(itemPendingAmount.add(itemPendingTax));
			}
			entry.setPendingAmount(pendingAmount);
			entry.setTotalAmount(data.getTotalAmount());
			entry.setGeneratedDate(data.getGenerationTime());
			result.put(data.getId(),entry);
		}
		return result;
	}

	private Map<Integer, List<SREmailShort>> getSoSrMap(ServiceReceivedData srdata) {
		Map<Integer, List<SREmailShort>> result = new HashMap<>();
		Map<Integer, List<ServiceReceivedItemData>> srResult = new HashMap<>();
		Set<Integer> srItemIds = new HashSet<>();
		for (ServiceReceivedItemData data : srdata.getServiceItemList()) {
			if (srResult.containsKey(data.getServiceOrderId())) {
				srResult.get(data.getServiceOrderId()).add(data);
			}
			else {
				List<ServiceReceivedItemData> list= new ArrayList<>();
				list.add(data);
				srResult.put(data.getServiceOrderId(),list);
			}
			srItemIds.add(data.getItemId());
		}
		List<ServiceReceivedItemData> serviceReceivedItemDataList = serviceReceiveManagementDao.findSrItemsBySoIds(srResult.keySet());
		Set<Integer> srIds = new HashSet<>();
		for (ServiceReceivedItemData data : serviceReceivedItemDataList) {
			srIds.add(data.getServiceReceivedDataId());
		}
		List<Integer> notCancelledSrs = serviceReceiveManagementDao.getSrIds(srIds);
		LOG.info("size of not cancelled srs are : {}",notCancelledSrs.size());
		for (ServiceReceivedItemData data : serviceReceivedItemDataList) {
			if (notCancelledSrs.contains(data.getServiceReceivedDataId())) {
				if (result.containsKey(data.getServiceOrderId())) {
					result.get(data.getServiceOrderId()).add(SCMDataConverter.convert(data,srItemIds));
				} else {
					List<SREmailShort> list = new ArrayList<>();
					list.add(SCMDataConverter.convert(data,srItemIds));
					result.put(data.getServiceOrderId(), list);
				}
			}
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Integer updateProvisionalServiceReceive(ServiceReceiveVO srVO, Integer srId, List<Integer> removedDrillDownIds)
			throws SumoException {

		ServiceReceivedData serviceReceivedData = serviceReceiveManagementDao.find(ServiceReceivedData.class, srId);
		checkThresholdForUpdateProvisional(srVO, serviceReceivedData);

		serviceReceivedData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
		// update sr data amount details
		addAmountDetails(srVO,serviceReceivedData);

		// update sr item data & so item data
		for(ServiceReceiveItem srItem: srVO.getItems()) {
			//get the corresponding SR item data service rec gives sr data id
			ServiceReceivedItemData srItemData = serviceReceivedData.getServiceItemList().stream()
					.filter(ele -> srItem.getServiceOrderItemId().equals(ele.getServiceOrderItemId()))
					.findFirst()
					.orElse(null);

			if(!Objects.nonNull(srItemData)) {
				throw new SumoException("Could not find SR ITEM in db");
			}
			srItem.setId(srItemData.getItemId());

			//get the corresponding so item data
			ServiceOrderItemData soItemData = serviceReceiveManagementDao.find(ServiceOrderItemData.class, srItem.getServiceOrderItemId());

			List<Integer> serviceReceiveItemTaxIds = new ArrayList<>();
			if(soItemData != null && srItemData != null) {
				BigDecimal finalQtyDifference = SCMUtil.subtract(srItem.getReceivedQuantity(), srItemData.getReceivedQuantity());
				soItemData.setReceivedQuantity(SCMUtil.add(soItemData.getReceivedQuantity(), finalQtyDifference));

				srItemData.setReceivedQuantity(srItem.getReceivedQuantity());
				srItemData.setPendingInvoiceQuantity(srItem.getReceivedQuantity());
				srItemData.setTotalPrice(srItem.getTotalCost());
				//save tax ids
				srItem.getTaxes().forEach(taxEle ->
						serviceReceiveItemTaxIds.add(taxEle.getServiceReceiveTaxDetailId())
				);
				srItem.getTaxes().clear();
				// calculate tax for each sr item and set to sr item data
				calculateTaxes(serviceReceivedData.getVendorId(), srItem, serviceReceivedData.getDispatchLocationId(),
						serviceReceivedData.getDeliveryLocationId());
				srItemData.setTotalAmount(srItem.getTotalAmount());
				srItemData.setTotalTax(srItem.getTotalTax());
			}

			// updating list of tax details for sr item
			List<ServiceReceiveTaxDetail> srItemTaxList = srItem.getTaxes();
			if (srItemTaxList != null && !srItemTaxList.isEmpty()) {
				int i = 0;
				for (ServiceReceiveTaxDetail taxEntry : srItemTaxList) {
					if (i < serviceReceiveItemTaxIds.size()) {
						Integer queryId = serviceReceiveItemTaxIds.get(i++);
						ServiceReceivedItemTaxData taxData = serviceReceiveManagementDao
								.find(ServiceReceivedItemTaxData.class, queryId);
						if(Objects.nonNull(taxData)) {
							taxData.setTaxType(taxEntry.getTaxName());
							taxData.setTaxPercentage(taxEntry.getPercentage());
							taxData.setTaxValue(taxEntry.getValue());
						}else {
							throw new SumoException("SR Item Tax Not Found!");
						}

					} else {
						throw new SumoException("SR Item Tax Could not be updated!");
					}
				}
			}
		}

		//update sr_item_data drillDown
		for(ServiceReceiveItem item : srVO.getItems()) {

			List<ServiceReceivedItemDrilldown> itemDrillDownList = item.getServiceReceivedItemDrillDown();
			if(itemDrillDownList != null && !itemDrillDownList.isEmpty()) {
				//find all by id, if not exists then persist to database
				for(ServiceReceivedItemDrilldown itemDrilldown : itemDrillDownList) {
					Integer searchDrillDownId = itemDrilldown.getServiceReceivedItemDrilldownId();
					ServiceReceivedItemDrilldownData itemDrilldownData = null;
					if(Objects.nonNull(searchDrillDownId)) {
						itemDrilldownData = serviceReceiveManagementDao
								.find(ServiceReceivedItemDrilldownData.class, searchDrillDownId);

						if(Objects.nonNull(itemDrilldownData)) {
							// update
							itemDrilldownData.setDescription(itemDrilldown.getDescription());
							itemDrilldownData.setReceivedQuantity(itemDrilldown.getReceivedQuantity());
							itemDrilldownData.setNos(itemDrilldown.getNos());
							if (Objects.nonNull(itemDrilldown.getMultiplier())) {
								itemDrilldownData.setMultiplier(itemDrilldown.getMultiplier());
							}
							itemDrilldownData.setSourceUom(itemDrilldown.getSourceUom());
							itemDrilldownData.setHeight(itemDrilldown.getHeight());
							itemDrilldownData.setLength(itemDrilldown.getLength());
							itemDrilldownData.setWidth(itemDrilldown.getWidth());
							itemDrilldownData.setIsExclusionEntry(itemDrilldown.getIsExclusionEntry());
						}
					} else {
						// add new
						ServiceReceivedItemDrilldownData serviceReceivedItemDrilldownData = SCMDataConverter.convert(itemDrilldown);
						serviceReceivedItemDrilldownData.setServiceReceivedItemId(item.getId());
						serviceReceiveManagementDao.add(serviceReceivedItemDrilldownData, false);
					}
				}

			}
		}

		// delete drillDowns
		if(Objects.nonNull(removedDrillDownIds)) {
			for(Integer i : removedDrillDownIds) {
				serviceReceiveManagementDao.removeServiceReceiveDrillDowns(i);
			}
		}

		return srId;
	}

	private void checkThresholdForUpdateProvisional(ServiceReceiveVO srVO, ServiceReceivedData serviceReceivedData) throws SumoException {
		//check threshold
		Set<Integer> serviceOrderIds = new HashSet<>();

		// find all service orders involved
		for (ServiceReceiveItem srItem : srVO.getItems()) {
			serviceOrderIds.add(srItem.getServiceOrderId());
		}


		for (Integer soId : serviceOrderIds) {
			ServiceOrderData soData = serviceOrderManagementDao.find(ServiceOrderData.class, soId);

			BigDecimal totalAmount = soData.getTotalAmount();
			BigDecimal updatedAmount = BigDecimal.ZERO;

			for (ServiceReceiveItem srItem : srVO.getItems()) {
				if (srItem.getServiceOrderId().equals(soId)) {
					//add new values for this SO item for a sr item
					updatedAmount = SCMUtil.add(updatedAmount, srItem.getTotalAmount());
				}
			}

			for (ServiceReceivedItemData srItemData : serviceReceivedData.getServiceItemList()) {
				if (srItemData.getServiceOrderId().equals(soId)) {
					// fetch previous values for the same sr item
					updatedAmount = SCMUtil.subtract(updatedAmount, srItemData.getTotalAmount());
				}
			}

			// get all service order item data for SO that have been received till now
			for (ServiceOrderItemData i : soData.getServiceOrderItemDataList()) {
				BigDecimal itemCostPrice = SCMUtil.multiplyWithScale10(i.getReceivedQuantity(), i.getUnitPrice());
				BigDecimal itemTaxPrice = SCMUtil.multiplyWithScale10(itemCostPrice, SCMUtil.divideWithScale10(i.getTaxRate(), BigDecimal.valueOf(100)));
				BigDecimal itemTotalCost = SCMUtil.add(itemCostPrice, itemTaxPrice);
				updatedAmount = SCMUtil.add(updatedAmount, itemTotalCost);
			}

			BigDecimal deviationAmount = SCMUtil.add(SCMUtil.multiplyWithScale10(SCMUtil.divide(totalAmount, new BigDecimal(100)), new BigDecimal(5)), totalAmount);
			if (updatedAmount.compareTo(deviationAmount) > 0) {
				throw new SumoException("Rejected Request for editing SR because so receiving is greater then 5% of Total Amount as Total Amount is: " + totalAmount + " And Final Receiving Amount is: " + updatedAmount + "" +
						" For Service Order: " + soId);
			}
		}
	}

	private void updateSoAfterApproval(ServiceReceivedData srData) throws SumoException {
		Set<Integer> targetSoList = new HashSet<Integer>();
		for (ServiceReceivedItemData receiveItem : srData.getServiceItemList()) {
			targetSoList.add(receiveItem.getServiceOrderId());
		}
		for (Integer soId : targetSoList) {
			List<ServiceOrderStatus> statusList = Arrays.asList(ServiceOrderStatus.APPROVED,
					ServiceOrderStatus.IN_PROGRESS,ServiceOrderStatus.CANCELLED);
			ServiceOrderData so = serviceOrderManagementDao.findServiceOrders(null,null,soId,statusList,null,null,
					null,true,false,null).get(0);
			if (so != null) {
				boolean pending = false;
				BigDecimal totalAmount = BigDecimal.ZERO;
				BigDecimal soAmount = so.getTotalAmount();
                List<ServiceReceivedItemData> serviceReceivedItemDataList = serviceReceiveManagementDao.findSrItemsBySoId(soId);
				for(ServiceReceivedItemData serviceReceivedItemData : serviceReceivedItemDataList){
					ServiceReceivedData serviceReceivedData = serviceReceiveManagementDao.find(ServiceReceivedData.class,serviceReceivedItemData.getServiceReceivedDataId());
					if(serviceReceivedData.getServiceReceiveStatus().equals(ServiceOrderStatus.CREATED.value())){
						totalAmount = SCMUtil.add(totalAmount,serviceReceivedItemData.getTotalAmount());
					}
				}
				LOG.info("SO Amount : {} , total Receiving : {}",soAmount,totalAmount);
				if(soAmount.compareTo(totalAmount)>0){
					pending = true;
				}
				if (!pending) {
					if (so.getAdvancePaymentDatas().isEmpty() || isAllAdvancesCompleted(so)) {
						so.setStatus(ServiceOrderStatus.CLOSED.name());
						if (so.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
							for (ServiceOrderItemData i : so.getServiceOrderItemDataList()) {
								updateBudgetAuditDetailForClosed(i, srData, BigDecimal.ZERO, srData.getCreatedBy());
							}
						}
					}
				}
				else{
					so.setStatus(ServiceOrderStatus.IN_PROGRESS.name());
				}
				updateServiceOrderStatusEvent(so,srData);

			}
		}
	}

	private void updateServiceOrders(ServiceReceivedData srData) throws SumoException {
		Set<Integer> targetSoList = new HashSet<Integer>();
		for (ServiceReceivedItemData receiveItem : srData.getServiceItemList()) {
			targetSoList.add(receiveItem.getServiceOrderId());
			ServiceOrderItemData orderItem = serviceReceiveManagementDao.find(ServiceOrderItemData.class,
					receiveItem.getServiceOrderItemId());
			//get the service order item corresponding to sr item TODO
			if (orderItem.getReceivedQuantity() == null) {
				orderItem.setReceivedQuantity(receiveItem.getReceivedQuantity());
			} else {
				//add to rec qty
				orderItem.setReceivedQuantity(orderItem.getReceivedQuantity().add(receiveItem.getReceivedQuantity()));
			}
		}

		if(srData.getServiceReceiveStatus().equalsIgnoreCase(ServiceOrderStatus.PROVISIONAL.value())) {
			return;
		}

		//all uniq so ids
		for (Integer soId : targetSoList) {
			ServiceOrderData so = serviceReceiveManagementDao.find(ServiceOrderData.class, soId);
			if (so != null) {
				boolean pending = false;
				BigDecimal totalAmount = BigDecimal.ZERO;
				BigDecimal soAmount = so.getTotalAmount();
//				for (ServiceOrderItemData i : so.getServiceOrderItemDataList()) {
//					BigDecimal requested = SCMUtil.convertToBigDecimal(i.getRequestedQuantity());
//					BigDecimal received = SCMUtil.convertToBigDecimal(i.getReceivedQuantity());
//					if (requested.compareTo(received) > 0) {
//						pending = true;
//					}
//				}
				for(ServiceOrderItemData i : so.getServiceOrderItemDataList()){
					BigDecimal itemRecievedCost = SCMUtil.multiplyWithScale10(i.getReceivedQuantity(),i.getUnitPrice());
					BigDecimal itemRecievedTax = SCMUtil.multiplyWithScale10(itemRecievedCost,SCMUtil.divideWithScale10(i.getTaxRate(),BigDecimal.valueOf(100)));
					BigDecimal itemTotalReceivedCost = SCMUtil.add(itemRecievedCost,itemRecievedTax);
					totalAmount = SCMUtil.add(totalAmount,itemTotalReceivedCost);
				}
				if(soAmount.compareTo(totalAmount)>0){
					pending = true;
				}
				if (!pending) {
					if (so.getAdvancePaymentDatas().isEmpty() || isAllAdvancesCompleted(so)) {
						so.setStatus(ServiceOrderStatus.CLOSED.name());
						if (so.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
							for (ServiceOrderItemData i : so.getServiceOrderItemDataList()) {
								updateBudgetAuditDetailForClosed(i, srData, BigDecimal.ZERO, srData.getCreatedBy());
							}
						}
					}
				} else {
					so.setStatus(ServiceOrderStatus.IN_PROGRESS.name());
				}
				updateServiceOrderStatusEvent(so, srData);
			}
		}
	}

	private boolean isAllAdvancesCompleted(ServiceOrderData so) {
		if (!so.getAdvancePaymentDatas().isEmpty()) {
			for (AdvancePaymentData advancePaymentData : so.getAdvancePaymentDatas()) {
				if (!advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.COMPLETED.value())) {
					return false;
				}
			}
		}
		return true;
	}

	private void updateServiceOrderStatusEvent(ServiceOrderData so, ServiceReceivedData srData) {
			ServiceOrderStatusEventData eventData = serviceOrderManagementDao.findServiceOrderStatus(so.getId());
			eventData.setFromStatus(eventData.getToStatus());
			eventData.setToStatus(so.getStatus());
			eventData.setServiceOrderId(so.getId());
			eventData.setUpdatedBy(srData.getCreatedBy());
			eventData.setUpdateTime(SCMUtil.getCurrentTimestamp());
			eventData.setTransitionStatus(SCMTransitionStatus.SUCCESS.name());
			serviceReceiveManagementDao.update(eventData, true);
	}

	private void updateBudgetAuditDetailForClosed(ServiceOrderItemData itemData, ServiceReceivedData srData,
			BigDecimal pendingPrice, Integer userId) throws SumoException {
		List<BusinessCostCenter> bccs = getBusinessCostCentersData();
		CostElementData costElement = serviceReceiveManagementDao.find(CostElementData.class, itemData.getCostElementId());
		BusinessCostCenter bcc = bccs.stream().filter(b -> b.getId().equals(itemData.getBusinessCostCenterId()))
				.findFirst().orElse(null);
		CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(Integer.parseInt(bcc.getCode()));
		CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(Integer.parseInt(bcc.getCode()),
				costElement.getDepartment().getName());
		List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(),
				BudgetAuditActions.RUNNING_AMOUNT.value());
		for (String action : actions) {
			//sr closure - remaining amount and running amount
			BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
			budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
			budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
			budgetAuditDetail.setActionBy(userId);
			budgetAuditDetail.setAction(BudgetAuditActions.CLOSED.value());
			budgetAuditDetail.setKeyType(BudgetAuditActions.SR_ID.value());
			budgetAuditDetail.setKeyValue(srData.getServiceReceivedId());
			budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
			if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
				budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
				budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
				budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(pendingPrice));
				budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
			} else {
				budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
				budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
				budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(pendingPrice));
				budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
			}
			serviceReceiveManagementDao.add(budgetAuditDetail, true);
		}
		capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(pendingPrice));
		capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(pendingPrice));
		serviceReceiveManagementDao.update(capexBudgetData, true);
	}

	private ServiceReceivedData createServiceReceiveData(ServiceReceiveVO srVO) throws SumoException {
		try {
			VendorDetail vendorDetail = scmCache.getVendorDetail(srVO.getVendorId());
			Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendorDetail,
					srVO.getDispatchLocationId());

			ServiceReceivedData serviceReceivedData = new ServiceReceivedData();
			Date currentTime = SCMUtil.getCurrentTimestamp();

			serviceReceivedData.setDeliveryLocationId(srVO.getDeliveryLocationId());
			serviceReceivedData.setDeliveryStateId(srVO.getDeliveryStateId());
			serviceReceivedData.setDispatchLocationId(dispatchLocation.get().getDispatchId());
			serviceReceivedData.setVendorId(vendorDetail.getVendorId());

			if(Boolean.TRUE.equals(srVO.getIsProvisional())) {
				serviceReceivedData.setServiceReceiveStatus(ServiceOrderStatus.PROVISIONAL.name());
				serviceReceivedData.setType(ServiceReceiveType.PROVISIONAL.value());
			}else {
				serviceReceivedData.setServiceReceiveStatus(ServiceOrderStatus.CREATED.name());
				serviceReceivedData.setType(ServiceReceiveType.REGULAR.value());
			}
			serviceReceivedData.setCreatedBy(srVO.getUserId());
			serviceReceivedData.setCreatedAt(currentTime);
			serviceReceivedData.setUpdatedAt(currentTime);
			serviceReceivedData.setCompanyId(srVO.getCompanyId());
			serviceReceivedData.setBusinessDate(AppUtils.getBusinessDate());
			//adding all items price to setting it to Sr obj
			serviceReceivedData = addAmountDetails(srVO, serviceReceivedData);

			serviceReceivedData = serviceReceiveManagementDao.add(serviceReceivedData, true);

			if (serviceReceivedData.getServiceReceivedId() != null) {
				List<ServiceReceiveItem> srItemList = srVO.getItems();
				Set<Integer> serviceOrders = new HashSet<>();
				// create SR item data , update budget for each item, collection of ServiceOrders
				serviceReceivedData.setServiceItemList(settleItems(srItemList, serviceReceivedData, serviceOrders, srVO.getIsProvisional()));
				// map the service receiving to the Service ORDER
				serviceReceivedData.setServiceOrderMappingList(settleMappings(serviceReceivedData, serviceOrders));
			}

			return serviceReceivedData;
		} catch (Exception e) {
			LOG.error("Encountered error while doing vendor GR :::::", e);
			throw new SumoException(e);
		}
	}

	private List<ServiceOrderServiceReceiveMappingData> settleMappings(ServiceReceivedData serviceReceivedData,
			Set<Integer> serviceOrders) throws SumoException {
		List<ServiceOrderServiceReceiveMappingData> mappingList = new ArrayList<>();
		for (Integer soId : serviceOrders) {
			ServiceOrderServiceReceiveMappingData mappingData = new ServiceOrderServiceReceiveMappingData();
			ServiceOrderData so = serviceOrderManagementDao.find(ServiceOrderData.class, soId);
			mappingData.setServiceOrderData(so);
			mappingData.setServiceReceivedData(serviceReceivedData);
			mappingList.add(serviceReceiveManagementDao.add(mappingData, false));
		}
		serviceReceiveManagementDao.flush();
		return mappingList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<Integer> createServiceReceive(List<ServiceReceiveVO> srVOs) throws SumoException {
		List<Integer> srs = new ArrayList<>();
		for (ServiceReceiveVO vo : srVOs) {
			srs.add(createServiceReceive(vo));
		}
		return srs;
	}

	private ServiceReceivedData addAmountDetails(ServiceReceiveVO srVO, ServiceReceivedData vendorGoodsReceivedData) {
		List<ServiceReceiveItem> items = new ArrayList<>();
		items.addAll(srVO.getItems());
		for (ServiceReceiveItem item : srVO.getItems()) {
			item.setTotalCost(item.getUnitPrice().multiply(item.getReceivedQuantity()));
			item.setTotalTax((item.getTotalCost().multiply(item.getTaxRate())).divide(new BigDecimal(100)));
			item.setTotalAmount(item.getTotalCost().add(item.getTotalTax()));
		}
		//total sr price
		BigDecimal totalSrPrice = SCMUtil.convertToBigDecimal(items.stream().filter(i -> i.getTotalCost() != null)
				.map(ServiceReceiveItem::getTotalCost).reduce(BigDecimal.ZERO, BigDecimal::add));
		//total Sr tax
		BigDecimal totalSrTax = SCMUtil.convertToBigDecimal(items.stream().filter(i -> i.getTotalTax() != null)
				.map(ServiceReceiveItem::getTotalTax).reduce(BigDecimal.ZERO, BigDecimal::add));
		BigDecimal totalSrAmount = totalSrPrice.add(totalSrTax);
		vendorGoodsReceivedData.setTotalTaxes(totalSrTax);
		vendorGoodsReceivedData.setTotalAmount(totalSrAmount);
		vendorGoodsReceivedData.setPrice(totalSrPrice);
		return vendorGoodsReceivedData;
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean approveSR(Integer srId) throws SumoException {
		LOG.info("Approve SR API is called for SrID : {}",srId);
		ServiceReceivedData serviceReceivedData = serviceReceiveManagementDao.find(ServiceReceivedData.class,srId);
		serviceReceivedData.setServiceReceiveStatus(ServiceOrderStatus.CREATED.value());
		serviceReceivedData = serviceReceiveManagementDao.update(serviceReceivedData,false);
		for(ServiceReceivedItemData serviceReceivedItemData : serviceReceivedData.getServiceItemList()){
			ServiceOrderData serviceOrderData = serviceOrderManagementDao.find(ServiceOrderData.class,serviceReceivedItemData.getServiceOrderId());
			if(serviceOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())){
				ServiceReceiveItem serviceReceiveItem = 	SCMDataConverter.convert(serviceReceivedItemData,scmCache);
				updatebudgetAuditDetail(serviceReceiveItem,serviceReceivedData);
			}
		}
		updateSoAfterApproval(serviceReceivedData);
		Boolean sendMail = validateForMail(serviceReceivedData);
		if (sendMail) {
			sendSrEmail(serviceReceivedData);
		}
		return true;
	}

	@Override
	public void getBusinessCostCentres(List<BusinessCostCenterData> businessCostCenterDataList, List<BusinessCostCenter> businessCostCenters) {
		for(BusinessCostCenterData businessCostCenter :businessCostCenterDataList){
			businessCostCenters.add(SCMDataConverter.convertToIdCodeName(businessCostCenter,masterDataCache));
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Date> getMinMaxDateForSrs(List<Integer> srIds) {
		Map<String, Date> result = new HashMap<>();
		Date minDate = null;
		Date maxDate = null;
		List<ServiceReceivedData> serviceReceivedDataList = serviceReceiveManagementDao.findAllSrs(srIds);
		if (Objects.nonNull(serviceReceivedDataList) && !serviceReceivedDataList.isEmpty()) {
			minDate = serviceReceivedDataList.get(0).getCreatedAt();
			maxDate = serviceReceivedDataList.get(0).getCreatedAt();
			for (ServiceReceivedData serviceReceivedData : serviceReceivedDataList) {
				if (minDate.compareTo(serviceReceivedData.getCreatedAt()) > 0) {
					minDate = serviceReceivedData.getCreatedAt();
				}
				if (maxDate.compareTo(serviceReceivedData.getCreatedAt()) < 0) {
					maxDate = serviceReceivedData.getCreatedAt();
				}
			}
		}
		result.put("MIN_DATE",minDate);
		result.put("MAX_DATE",maxDate);
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Integer> reCheckAllSrsForAdvance(Integer srId) {
		ServiceReceivedData serviceReceivedData = serviceReceiveManagementDao.find(ServiceReceivedData.class, srId);
		Map<Integer, Pair<List<Integer>, List<Integer>>> advances = getAdvanceSrs(Collections.singletonList(serviceReceivedData));
		Pair<List<Integer>, List<Integer>> result = advances.get(srId);
		if (Objects.nonNull(result)) {
			return result.getValue();
		}
		return new ArrayList<>();
	}


	private List<ServiceReceivedItemData> settleItems(List<ServiceReceiveItem> items,
			ServiceReceivedData serviceReceivedData, Set<Integer> serviceOrders, Boolean isProvisional) throws SumoException {
		List<ServiceReceivedItemData> serviceItemList = new ArrayList<>();
		for (ServiceReceiveItem item : items) {
			//creating new entity obj service_rec_item data
			ServiceReceivedItemData itemData = new ServiceReceivedItemData();
			itemData.setUnitPrice(item.getUnitPrice());
			itemData.setReceivedQuantity(item.getReceivedQuantity());
			itemData.setAscCode(item.getAscCode());
			if(item.getCostElementDate()!=null){
				itemData.setRecievedcostElementDate(item.getCostElementDate());
			}
			if(item.getCostElementToDate()!=null){
				itemData.setRecievedcostElementToDate(item.getCostElementToDate());
			}
			itemData.setUnitOfMeasure(item.getUnitOfMeasure());
			itemData.setCostElementId(item.getCostElementId());
			itemData.setTaxRate(item.getTaxRate());
			itemData.setTdsRate(item.getTdsRate());
			itemData.setServiceDescription(item.getServiceDescription());
			itemData.setCostElementName(item.getCostElementName());
			itemData.setPendingInvoiceQuantity(item.getReceivedQuantity());
			itemData.setInvoiceQuantity(BigDecimal.ZERO);
			itemData.setServiceReceivedDataId(serviceReceivedData.getServiceReceivedId());
			itemData.setServiceOrderId(item.getServiceOrderId());
			itemData.setServiceOrderItemId(item.getServiceOrderItemId());
			itemData.setBusinessCostCenterId(item.getBusinessCostCenterId());
			itemData.setBusinessCostCenterName(item.getBusinessCostCenterName());
			itemData.setTotalPrice(item.getTotalCost());
			calculateTaxes(serviceReceivedData.getVendorId(), item, serviceReceivedData.getDispatchLocationId(),
					serviceReceivedData.getDeliveryLocationId());
			itemData.setTotalTax(item.getTotalTax());
			itemData.setTotalAmount(item.getTotalAmount());
			String budgetCategory = serviceReceiveManagementDao.getBudgetCategory(item.getCostElementId());
			itemData.setBudgetCategory(budgetCategory);
			itemData = serviceReceiveManagementDao.add(itemData, false);

//			adding drilldown
			List<ServiceReceivedItemDrilldownData> serviceReceivedItemDrilldownDataList = new ArrayList<>();
			if(Objects.nonNull(item.getServiceReceivedItemDrillDown())) {
				for (ServiceReceivedItemDrilldown serviceReceivedItemDetailsData : item.getServiceReceivedItemDrillDown()) {
					ServiceReceivedItemDrilldownData serviceReceivedItemDrilldownData = SCMDataConverter.convert(serviceReceivedItemDetailsData);
					serviceReceivedItemDrilldownData.setServiceReceivedItemId(itemData.getItemId());
					serviceReceivedItemDrilldownData = serviceReceiveManagementDao.add(serviceReceivedItemDrilldownData, false);
					serviceReceivedItemDrilldownDataList.add(serviceReceivedItemDrilldownData);
				}
				itemData.setServiceReceivedItemDrilldownData(serviceReceivedItemDrilldownDataList);
			}
			List<ServiceReceivedItemTaxData> taxDetailDataList = new ArrayList<>();
			// setting list of tax details for that item service received item tax
			if (item.getTaxes() != null && !item.getTaxes().isEmpty()) {
				for (ServiceReceiveTaxDetail tax : item.getTaxes()) {
					ServiceReceivedItemTaxData taxData = new ServiceReceivedItemTaxData();
					taxData.setTaxType(tax.getTaxName());
					taxData.setTaxPercentage(tax.getPercentage());
					taxData.setTaxValue(tax.getValue());
					taxData.setServiceReceivedItemId(itemData.getItemId());
					taxData = serviceReceiveManagementDao.add(taxData, false);
					taxDetailDataList.add(taxData);
				}
				itemData.setTaxes(taxDetailDataList);
			}
			serviceItemList.add(itemData);
			if(item.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value()) && !Boolean.TRUE.equals(isProvisional)){
				updatebudgetAuditDetail(item,serviceReceivedData);
			}
			serviceOrders.add(item.getServiceOrderId());
		}
		return serviceItemList;
	}

	private void updatebudgetAuditDetail(ServiceReceiveItem item, ServiceReceivedData serviceReceivedData) throws SumoException {
		List<BusinessCostCenter> bccs = getBusinessCostCentersData();
		CostElementData costElement = serviceReceiveManagementDao.find(CostElementData.class, item.getCostElementId());
		BusinessCostCenter bcc = bccs.stream()
				.filter(b -> b.getId().equals(item.getBusinessCostCenterId())).findFirst().orElse(null);
		CapexAuditDetailData capexAuditDetail = serviceOrderManagementDao.findCapexAuditData(Integer.parseInt(bcc.getCode()));
		CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(Integer.parseInt(bcc.getCode()), costElement.getDepartment().getName());
		BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
		budgetAuditData.setCapexBudgetDetailId(capexBudgetData.getId());
		budgetAuditData.setCapexAuditId(capexAuditDetail.getId());
		budgetAuditData.setAmountType(BudgetAuditActions.RECEIVING_AMOUNT.value());
		budgetAuditData.setPreviousValue(capexBudgetData.getReceivingAmount());
		budgetAuditData.setFinalValue(capexBudgetData.getReceivingAmount().add(item.getTotalAmount()));
		budgetAuditData.setActionType(BudgetAuditActions.ADDITION.value());
		budgetAuditData.setAction(BudgetAuditActions.CREATED.value());
		budgetAuditData.setKeyType(BudgetAuditActions.SR_ID.value());
		budgetAuditData.setKeyValue(serviceReceivedData.getServiceReceivedId());
		budgetAuditData.setActionBy(serviceReceivedData.getCreatedBy());
		budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
		serviceReceiveManagementDao.add(budgetAuditData, true);
		capexBudgetData.setReceivingAmount(capexBudgetData.getReceivingAmount().add(item.getTotalAmount()));
		serviceReceiveManagementDao.update(capexBudgetData, true);
	}

	private void calculateTaxes(Integer vendorId, ServiceReceiveItem item, Integer dispatchLocationId,
			Integer deliveryLocationId) {

		VendorDetail vendor = scmCache.getVendorDetail(vendorId);
		Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendor, dispatchLocationId);
		Location to = masterDataCache.getLocationbyId(deliveryLocationId);
		String fromState = dispatchLocation.get().getAddress().getStateCode();
		int fromStateId = Integer.parseInt(fromState);
		int toStateId = Integer.parseInt(to.getState().getCode());
		BigDecimal taxRate = item.getTaxRate();
		BigDecimal totalTax = BigDecimal.ZERO;
		if (fromStateId == toStateId) {
			if (taxRate != null && taxRate.compareTo(BigDecimal.ZERO) > 0) {
				totalTax = setTaxes(item, taxRate, SCMServiceConstants.SGST, totalTax);
				totalTax = setTaxes(item, taxRate, SCMServiceConstants.CGST, totalTax);
			}
		} else {
			if (taxRate != null && taxRate.compareTo(BigDecimal.ZERO) > 0) {
				totalTax = setTaxes(item, taxRate, SCMServiceConstants.IGST, totalTax);
			}
		}
		item.setTotalTax(totalTax);
		item.setTotalCost(SCMUtil.multiplyWithScale10(item.getUnitPrice(), item.getReceivedQuantity()));
		item.setTotalAmount(SCMUtil.add(totalTax, item.getTotalCost()));
	}

	private BigDecimal setTaxes(ServiceReceiveItem item, BigDecimal taxRate, String taxType, BigDecimal totalTax) {
		BigDecimal taxPercentage = getTaxPercentage(taxType, taxRate);
		ServiceReceiveTaxDetail taxDetail = new ServiceReceiveTaxDetail();
		taxDetail.setTaxCategory(item.getAscCode());
		taxDetail.setTaxName(taxType);
		taxDetail.setPercentage(taxPercentage);
		BigDecimal tax = SCMUtil.multiplyWithScale10(item.getTotalCost(),
				SCMUtil.divideWithScale10(taxPercentage, new BigDecimal(100)));
		taxDetail.setValue(tax);
		item.getTaxes().add(taxDetail);
		return SCMUtil.add(totalTax, tax);
	}

	private BigDecimal getTaxPercentage(String taxType, BigDecimal taxRate) {
		BigDecimal percentage = BigDecimal.ZERO;
		switch (taxType) {
		case "IGST":
			percentage = percentage.add(taxRate);
			break;
		case "SGST":
			percentage = percentage.add(SCMUtil.divideWithScale10(taxRate, BigDecimal.valueOf(2)));
			break;
		case "CGST":
			percentage = percentage.add(SCMUtil.divideWithScale10(taxRate, BigDecimal.valueOf(2)));
			break;
		}
		return percentage;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean cancelServiceReceive(int srId, int userId) throws SumoException {
		ServiceReceivedData serviceReceive = serviceReceiveManagementDao.find(ServiceReceivedData.class, srId);
		if (serviceReceive != null) {
			serviceReceive.setUpdatedBy(userId);
			serviceReceive.setUpdatedAt(SCMUtil.getCurrentTimestamp());

			Set<Integer> serviceOrderIds = new HashSet<>();
			// fix received quantity in service order item
			for (ServiceReceivedItemData serviceReceivedItemData : serviceReceive.getServiceItemList()) {
				ServiceOrderItemData serviceOrderItemData = serviceOrderManagementDao.find(ServiceOrderItemData.class,
						serviceReceivedItemData.getServiceOrderItemId());
				serviceOrderItemData.setReceivedQuantity(AppUtils.subtract(serviceOrderItemData.getReceivedQuantity(),
						serviceReceivedItemData.getReceivedQuantity()));
				serviceOrderIds.add(serviceOrderItemData.getServiceOrderId());
				if(serviceOrderItemData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())
						&& !serviceReceive.getServiceReceiveStatus().equalsIgnoreCase(ServiceOrderStatus.PROVISIONAL.value())){
					updateCancelledbudgetAuditDetail(serviceReceivedItemData,serviceReceive,userId);
				}
			}
			serviceReceive.setServiceReceiveStatus(ServiceOrderStatus.CANCELLED.name());
			serviceReceiveManagementDao.update(serviceReceive, true);

			// open service orders // do not reopen if force closed
			for (Integer i : serviceOrderIds) {
				ServiceOrderData so = serviceOrderManagementDao.find(ServiceOrderData.class, i);
				if (ServiceOrderStatus.CLOSED.name().equals(so.getStatus()) && !SCMServiceConstants.SCM_CONSTANT_YES.equalsIgnoreCase(so.getForceClosed())) {
					so.setStatus(ServiceOrderStatus.IN_PROGRESS.name());
				}
			}

		}
		return true;
	}

	private void updateCancelledbudgetAuditDetail(ServiceReceivedItemData item, ServiceReceivedData serviceReceivedData, Integer userId) throws SumoException {
		List<BusinessCostCenter> bccs = getBusinessCostCentersData();
		CostElementData costElement = serviceReceiveManagementDao.find(CostElementData.class, item.getCostElementId());
		BusinessCostCenter bcc = bccs.stream()
				.filter(b -> b.getId().equals(item.getBusinessCostCenterId())).findFirst().orElse(null);
		CapexBudgetDetailData capexBudgetData = serviceOrderManagementDao.findBudgetUnit(Integer.parseInt(bcc.getCode()), costElement.getDepartment().getName());
		BudgetAuditDetailData budgetAuditData = new BudgetAuditDetailData();
		budgetAuditData.setCapexBudgetDetailId(capexBudgetData.getId());
		budgetAuditData.setAmountType(BudgetAuditActions.RECEIVING_AMOUNT.value());
		budgetAuditData.setPreviousValue(capexBudgetData.getReceivingAmount());
		budgetAuditData.setFinalValue(capexBudgetData.getReceivingAmount().subtract(item.getTotalAmount()));
		budgetAuditData.setActionType(BudgetAuditActions.REDUCTION.value());
		budgetAuditData.setAction(BudgetAuditActions.CANCELLED.value());
		budgetAuditData.setKeyType(BudgetAuditActions.SR_ID.value());
		budgetAuditData.setKeyValue(serviceReceivedData.getServiceReceivedId());
		budgetAuditData.setActionBy(userId);
		budgetAuditData.setActionTime(SCMUtil.getCurrentTimestamp());
		serviceReceiveManagementDao.add(budgetAuditData, true);
		capexBudgetData.setReceivingAmount(capexBudgetData.getReceivingAmount().subtract(item.getTotalAmount()));
		serviceReceiveManagementDao.update(capexBudgetData, true);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId,
			MultipartFile file) {
		String fileName = "SERVICE_RECEIVE_INVOICE" + SCMUtil.getCurrentTimeISTStringWithNoColons();
		try {
			FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "GR", fileName, file);
			DocumentDetail documentDetail = new DocumentDetail();
			documentDetail.setMimeType(mimeType);
			documentDetail.setUploadType(docType);
			documentDetail.setFileType(type);
			documentDetail.setDocumentLink(fileName);
			documentDetail.setS3Key(s3File.getKey());
			documentDetail.setFileUrl(s3File.getUrl());
			documentDetail.setS3Bucket(s3File.getBucket());
			documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
			documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
			DocumentDetailData data = serviceReceiveManagementDao.add(SCMDataConverter.convert(documentDetail), true);
			if (data.getDocumentId() != null) {
				return SCMDataConverter.convert(data);
			}
		} catch (Exception e) {
			LOG.error("Encountered error while uploading document", e);
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ServiceReceive> findServiceReceive(Integer vendorId, Integer locationId, Integer userId, Date startDate,
			Date endDate, Integer serviceOrderId) {
		List<ServiceReceivedData> serviceReceiveDataList = findServiceReceivedDataList(null,vendorId,locationId,userId,startDate,endDate,serviceOrderId , false);
		if (Objects.nonNull(serviceReceiveDataList)) {
			List<ServiceReceive> serviceReceiveList = serviceReceiveDataList.stream().
					map(x -> SCMDataConverter.convert(x, scmCache, masterDataCache,null, true) ).
					collect(Collectors.toList());
			return serviceReceiveList;
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ServiceReceiveShort> findServiceReceiveShort(Integer bccId,Integer vendorId, Integer locationId, Integer userId, Date startDate,
															 Date endDate, Integer serviceOrderId) {
		List<ServiceReceivedData> serviceReceiveDataList = findServiceReceivedDataList(bccId,vendorId,locationId,userId,startDate,endDate,serviceOrderId,true);
		if (Objects.nonNull(serviceReceiveDataList)) {
			List<ServiceReceiveShort> serviceReceiveShortList = serviceReceiveDataList.stream().
					map(x -> SCMDataConverter.convertToServiceReceiveShort(x, scmCache, masterDataCache, true) ).
					collect(Collectors.toList());
			return serviceReceiveShortList;
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ServiceReceivedData> findServiceReceivedDataList(Integer bccId,Integer vendorId, Integer locationId, Integer userId, Date startDate,
															 Date endDate, Integer serviceOrderId , boolean isShort) {

		if (startDate == null && endDate == null) {
			startDate = SCMUtil.getCurrentBusinessDate();
			endDate = startDate;
		}
		endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);

		List<Integer> costElements = null;
		/*if (userId != null) {
			costElements = serviceOrderManagementDao.findCostElementsForEmployee(userId);
			if (costElements == null || costElements.isEmpty()) {
				return new ArrayList<>();
			}
		}*/

		List<ServiceReceivedData> serviceReceiveDataList = serviceReceiveManagementDao.findServiceReceives(bccId,vendorId,
				locationId, startDate, endDate, costElements, serviceOrderId,isShort);
		return serviceReceiveDataList;
	}

	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ServiceReceive> getLinkedSrForSo(Integer vendorId, Integer locationId, Integer userId, Date startDate,
												   Date endDate, Integer paymentRequestId) {

		if (startDate == null && endDate == null) {
			startDate = SCMUtil.getCurrentBusinessDate();
			endDate = startDate;
		}
		endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);

		List<Integer> costElements = null;
		if (userId != null) {
			costElements = serviceOrderManagementDao.findCostElementsForEmployee(userId);
			if (costElements == null || costElements.isEmpty()) {
				return new ArrayList<>();
			}
		}
		Map<Integer,String> mapBusinessCenterIdCode = getBusinessCostCentersData().stream().collect(Collectors.toMap(BusinessCostCenter::getId,BusinessCostCenter::getCode));
		List<ServiceReceivedData> serviceReceiveDataList = serviceReceiveManagementDao.getLinkedSrForSo(vendorId,
				locationId, startDate, endDate, costElements, paymentRequestId);
		if (serviceReceiveDataList != null) {
			List<ServiceReceive> list = new ArrayList<>();
			for (ServiceReceivedData d : serviceReceiveDataList) {
				list.add(SCMDataConverter.convert(d, scmCache, masterDataCache, mapBusinessCenterIdCode, true));
			}
			return list;
		}
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ServiceReceive> findServiceReceiveForPayment(Integer vendorId, Integer companyId, Integer userId,
			Integer stateId, Date startDate, Date endDate) {
		LOG.info("Request to fetch all the service receivings pending for payment by user {} ", userId);
		if (startDate == null && endDate == null) {
			startDate = SCMUtil.getCurrentBusinessDate();
			endDate = startDate;
		}
		endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);
		List<Integer> costElements = null;
		if (userId != null) {
			costElements = serviceOrderManagementDao.findCostElementsForEmployee(userId);
			if (costElements == null || costElements.isEmpty()) {
				return new ArrayList<>();
			}
		}
		List<ServiceReceivedData> serviceReceiveDataList = serviceReceiveManagementDao
				.findServiceReceivesForPayment(vendorId, companyId, stateId, startDate, endDate, costElements);
		Map<Integer, Pair<List<Integer>, List<Integer>>> advances = getAdvanceSrs(serviceReceiveDataList);
		if (serviceReceiveDataList != null) {
			List<ServiceReceive> list = new ArrayList<>();
			for (ServiceReceivedData d : serviceReceiveDataList) {
				ServiceReceive serviceReceive = SCMDataConverter.convert(d, scmCache, masterDataCache,null, true);
				serviceReceive.setAdvanceSrs(advances.getOrDefault(d.getServiceReceivedId(), new Pair<>(null, new ArrayList<>())).getValue());
				if (Objects.nonNull(advances.get(d.getServiceReceivedId()))) {
					List<VendorAdvancePayment> vendorAdvancePayments = new ArrayList<>();
					Pair<List<Integer>, List<Integer>> adpAndSrs = advances.get(d.getServiceReceivedId());
					if (Objects.nonNull(adpAndSrs.getKey()) && !adpAndSrs.getKey().isEmpty()) {
						for (Integer advancePaymentId : adpAndSrs.getKey()) {
							VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(d.getVendorId())
									.advanceStatus("ALL").advanceType(SCMServiceConstants.SO_ADVANCE).build();
							VendorAdvancePayment advance = null;
							try {
								AdvancePaymentData advancePaymentData = serviceOrderManagementDao.find(AdvancePaymentData.class, advancePaymentId);
								advance = paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
								if (Objects.nonNull(advance)) {
									vendorAdvancePayments.add(advance);
								}
							} catch (Exception e) {
								LOG.info("Exception during Vendor Advance ..!", e);
							}
						}
					}
					if (!vendorAdvancePayments.isEmpty()) {
						serviceReceive.setVendorAdvancePayments(vendorAdvancePayments);
					}
				}
				list.add(serviceReceive);
			}
			return list;
		}
		return null;
	}

	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<Integer, Pair<List<Integer>, List<Integer>>> getAdvanceSrs(List<ServiceReceivedData> serviceReceivedData) {
		//<srId, ADPId,LIST<SRIDS>>
		Map<Integer, Pair<List<Integer>, List<Integer>>> result = new HashMap<>();
		serviceReceivedData.forEach(srd -> {
			List<Integer> srIds = new ArrayList<>();
			boolean isMultiSo = false;
			Integer tempSoId = null;
			for (ServiceReceivedItemData serviceReceivedItemData : srd.getServiceItemList()) {
				if (Objects.nonNull(tempSoId)) {
					if (!tempSoId.equals(serviceReceivedItemData.getServiceOrderId())) {
						isMultiSo = true;
					}
				} else {
					tempSoId = serviceReceivedItemData.getServiceOrderId();
				}
				if (isMultiSo) {
					break;
				}
			}
			if (!isMultiSo) {
				ServiceOrderData serviceOrderData = serviceOrderManagementDao.find(ServiceOrderData.class, tempSoId);
				if (Objects.nonNull(serviceOrderData.getAdvancePaymentDatas())) {
					List<Integer> adpIds = new ArrayList<>();
					for (AdvancePaymentData advancePaymentData : serviceOrderData.getAdvancePaymentDatas()) {
						List<ServiceOrderServiceReceiveMappingData> soSrList = serviceReceiveManagementDao.findSrsWithSoId(tempSoId);
						soSrList.forEach(soSrData -> {
							ServiceReceivedData receivedData = soSrData.getServiceReceivedData();
							if (Objects.isNull(receivedData.getPaymentRequestData()) && !receivedData.getServiceReceiveStatus().equalsIgnoreCase(SCMOrderStatus.CANCELLED.value())) {
								if (!srIds.contains(receivedData.getServiceReceivedId())) {
									srIds.add(receivedData.getServiceReceivedId());
								}
							}
						});
						if (!adpIds.contains(advancePaymentData.getAdvancePaymentId())) {
							adpIds.add(advancePaymentData.getAdvancePaymentId());
						}
					}
					result.put(srd.getServiceReceivedId(), new Pair<>(adpIds, srIds));
				}
			}
		});
		return result;
	}

	@Override
	public Boolean setServiceReceiveForNoPayment(BulkRequestVO request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<BusinessCostCenter> getBusinessCostCentersData() {
		List<BusinessCostCenter> list = new ArrayList<>();
		for (BusinessCostCenterData d : serviceReceiveManagementDao.findAll(BusinessCostCenterData.class)) {
			if (AppConstants.ACTIVE.equals(d.getStatus())) {
				list.add(SCMDataConverter.convertToIdCodeName(d, masterDataCache));
			}
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean createBusinessCostCentersData(BusinessCostCenter businessCostCenter) throws SumoException {
		BusinessCostCenterData data = new BusinessCostCenterData();
		data.setCode(businessCostCenter.getCode());
		data.setCompanyId(businessCostCenter.getCompany().getId());
		data.setLocationId(businessCostCenter.getLocation().getId());
		data.setName(businessCostCenter.getName());
		data.setStatus(AppConstants.ACTIVE);
		data.setType(businessCostCenter.getType() != null ? businessCostCenter.getType()
				: BusinessCostCenterType.OTHERS.name());
		serviceReceiveManagementDao.add(data, true);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<ServiceReceive> searchServiceReceivingForPaymentRequest(Integer paymentRequestId) {
		List<ServiceReceivedData> serviceReceiveDataList = serviceReceiveManagementDao
				.findServiceReceivingForPaymentRequest(paymentRequestId);
		if (serviceReceiveDataList != null) {
			List<ServiceReceive> list = new ArrayList<>();
			for (ServiceReceivedData d : serviceReceiveDataList) {
				list.add(SCMDataConverter.convert(d, scmCache, masterDataCache,null, true));
			}
			return list;
		}
		return null;
	}



}
