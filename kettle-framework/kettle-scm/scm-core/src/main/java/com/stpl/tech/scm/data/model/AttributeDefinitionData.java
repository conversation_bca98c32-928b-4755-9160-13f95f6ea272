/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * AttributeDefinitionData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ATTRIBUTE_DEFINITION", uniqueConstraints = @UniqueConstraint(columnNames = "ATTRIBUTE_NAME"))
public class AttributeDefinitionData implements java.io.Serializable {

    private Integer attributeId;
    private String attributeName;
    private String attributeType;
    private String attributeCode;
    private String attributeShortCode;
    private String attributeDescription;
    private String attributeStatus;

    public AttributeDefinitionData() {
    }

    public AttributeDefinitionData(String attributeName, String attributeType, String attributeCode,
                                   String attributeStatus) {
        this.attributeName = attributeName;
        this.attributeType = attributeType;
        this.attributeCode = attributeCode;
        this.attributeStatus = attributeStatus;
    }

    public AttributeDefinitionData(String attributeName, String attributeType, String attributeCode,
                                   String attributeShortCode, String attributeDescription, String attributeStatus) {
        this.attributeName = attributeName;
        this.attributeType = attributeType;
        this.attributeCode = attributeCode;
        this.attributeShortCode = attributeShortCode;
        this.attributeDescription = attributeDescription;
        this.attributeStatus = attributeStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "ATTRIBUTE_ID", unique = true, nullable = false)
    public Integer getAttributeId() {
        return this.attributeId;
    }

    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }

    @Column(name = "ATTRIBUTE_NAME", unique = true, nullable = false)
    public String getAttributeName() {
        return this.attributeName;
    }

    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    @Column(name = "ATTRIBUTE_TYPE", nullable = false, length = 50)
    public String getAttributeType() {
        return this.attributeType;
    }

    public void setAttributeType(String attributeType) {
        this.attributeType = attributeType;
    }

    @Column(name = "ATTRIBUTE_CODE", nullable = false, length = 50)
    public String getAttributeCode() {
        return this.attributeCode;
    }

    public void setAttributeCode(String attributeCode) {
        this.attributeCode = attributeCode;
    }

    @Column(name = "ATTRIBUTE_SHORT_CODE", length = 10)
    public String getAttributeShortCode() {
        return this.attributeShortCode;
    }

    public void setAttributeShortCode(String attributeShortCode) {
        this.attributeShortCode = attributeShortCode;
    }

    @Column(name = "ATTRIBUTE_DESCRIPTION", length = 1000)
    public String getAttributeDescription() {
        return this.attributeDescription;
    }

    public void setAttributeDescription(String attributeDescription) {
        this.attributeDescription = attributeDescription;
    }

    @Column(name = "ATTRIBUTE_STATUS", nullable = false, length = 15)
    public String getAttributeStatus() {
        return this.attributeStatus;
    }

    public void setAttributeStatus(String attributeStatus) {
        this.attributeStatus = attributeStatus;
    }

}
