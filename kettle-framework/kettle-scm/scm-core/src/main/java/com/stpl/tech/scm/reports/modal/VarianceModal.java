package com.stpl.tech.scm.reports.modal;

import java.math.BigDecimal;

public class VarianceModal {

	private int id;
	private String name;
	private String uom;
	private String category;
	private String subCategory;
	private String typeOfVariance;
	private BigDecimal openingStock;
	private BigDecimal transferred;
	private BigDecimal received;
	private BigDecimal wasted;
	private BigDecimal booking;
	private BigDecimal reverseBooking;
	private BigDecimal consumption;
	private BigDecimal reverseConsumption;
	private BigDecimal closingStock;
	private BigDecimal expectedValue;
	private BigDecimal variance;
	private BigDecimal unitCost;
	private BigDecimal varianceCost;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	public String getCategory() {
		return category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public String getSubCategory() {
		return subCategory;
	}

	public void setSubCategory(String subCategory) {
		this.subCategory = subCategory;
	}

	public String getTypeOfVariance() {
		return typeOfVariance;
	}

	public void setTypeOfVariance(String typeOdVariance) {
		this.typeOfVariance = typeOdVariance;
	}

	public BigDecimal getOpeningStock() {
		return openingStock;
	}

	public void setOpeningStock(BigDecimal openingStock) {
		this.openingStock = openingStock;
	}

	public BigDecimal getTransferred() {
		return transferred;
	}

	public void setTransferred(BigDecimal transferred) {
		this.transferred = transferred;
	}

	public BigDecimal getReceived() {
		return received;
	}

	public void setReceived(BigDecimal received) {
		this.received = received;
	}

	public BigDecimal getWasted() {
		return wasted;
	}

	public void setWasted(BigDecimal wasted) {
		this.wasted = wasted;
	}

	public BigDecimal getConsumption() {
		return consumption;
	}

	public void setConsumption(BigDecimal consumption) {
		this.consumption = consumption;
	}

	public BigDecimal getClosingStock() {
		return closingStock;
	}

	public void setClosingStock(BigDecimal closingStock) {
		this.closingStock = closingStock;
	}

	public BigDecimal getExpectedValue() {
		return expectedValue;
	}

	public void setExpectedValue(BigDecimal expectedValue) {
		this.expectedValue = expectedValue;
	}

	public BigDecimal getVariance() {
		return variance;
	}

	public void setVariance(BigDecimal variance) {
		this.variance = variance;
	}

	public BigDecimal getUnitCost() {
		return unitCost;
	}

	public void setUnitCost(BigDecimal unitCost) {
		this.unitCost = unitCost;
	}

	public BigDecimal getVarianceCost() {
		return varianceCost;
	}

	public void setVarianceCost(BigDecimal varianceCost) {
		this.varianceCost = varianceCost;
	}

    public BigDecimal getBooking() {
        return booking;
    }

    public void setBooking(BigDecimal booking) {
        this.booking = booking;
    }

	public BigDecimal getReverseBooking() {
		return reverseBooking;
	}

	public void setReverseBooking(BigDecimal reverseBooking) {
		this.reverseBooking = reverseBooking;
	}

	public BigDecimal getReverseConsumption() {
		return reverseConsumption;
	}

	public void setReverseConsumption(BigDecimal reverseConsumption) {
		this.reverseConsumption = reverseConsumption;
	}
}
