package com.stpl.tech.scm.data.dao;

import java.util.List;

import com.stpl.tech.scm.data.model.GatepassData;
import com.stpl.tech.scm.data.model.GatepassVendorMappingData;
import com.stpl.tech.scm.domain.model.SearchGatepass;

public interface GatepassManagementDao extends SCMAbstractDao{

	public List<GatepassVendorMappingData> getVendorMappingList(String opsType, Integer unitId, Integer vendorId, String status);

	public List<GatepassData> getGatepass(SearchGatepass gatepass);

}
