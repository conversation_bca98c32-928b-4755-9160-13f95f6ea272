package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.service.*;
import com.stpl.tech.scm.core.util.AssetHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.GoodsReceiveManagementDao;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.SCMProductManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.data.transport.model.GrStockEvent;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.scm.notification.email.template.RejectedGREmailNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.stpl.tech.scm.data.converter.SCMDataConverter.convert;


/**
 * Created by Abhishek Sirohi .
 */
@Service

public class SCMAssetManagementServiceImpl implements SCMAssetManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(SCMAssetManagementServiceImpl.class);

    @Autowired
    private StockManagementDao stockManagementDao;

    @Autowired
    private SCMProfileManagementService scmProfileManagementService;

    @Autowired
    private SCMAssetManagementDao scmAssetManagementDao;

    @Autowired
    private SCMProductManagementDao scmProductManagementDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    private AtomicLong atomicLongForAssetTagValue;

    private AtomicLong atomicLongForTONoteUniqueSequence;

    @Autowired
    private SCMNotificationService scmNotificationService;

    @Autowired
    EnvProperties props;

    @Autowired
    private GoodsReceiveManagementDao goodsReceiveManagementDao;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private ProductionBookingService bookingService;

    @Autowired
    private TransferOrderManagementService transferOrderManagementService;

    @Autowired
    private PriceManagementDao priceDao;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private SCMReportingService reportingService;

    @Autowired
    private WarehouseStockManagementService warehouseStockManagementService;

    @Autowired
    private StockManagementService stockManagementService;

    @Autowired
    private TaxDataCache taxCache;
    /*
     * postConstructMethod is used to set maximum value of
     * atomicLongForAssetTagValue if some value is already available else it will
     * assign a default value for first time *********(AAA110)
     */
    @PostConstruct
    public void postConstructMethod() {
        String alphanumericValue = scmAssetManagementDao.getMaximumAssetTagValue();
        atomicLongForAssetTagValue = alphanumericValue != null ? new AtomicLong(Long.parseLong(alphanumericValue, 36))
                : new AtomicLong(*********L);
        String maxUniqueSequence = scmAssetManagementDao.getMaxToNoteUniqueSequence();
        atomicLongForTONoteUniqueSequence = maxUniqueSequence != null
                ? new AtomicLong(Long.parseLong(maxUniqueSequence.split("/")[2]))
                : new AtomicLong(111111L);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public synchronized StockTakeInitResponse initiateNewStockEvent(StockEventDefinition stockEventDefinition, String isSplit) throws SumoException {
        StockTakeInitResponse response = new StockTakeInitResponse();
        Map<String, String> categoryStatusMap = new HashMap<>();
        List<StockEventDefinitionData> stockEventDefinitionDataList = scmAssetManagementDao.getEventByUnit(stockEventDefinition.getUnitId(),
                StockEventStatusType.IN_PROCESS.value(), null);
        if(Objects.nonNull(stockEventDefinitionDataList) && !stockEventDefinitionDataList.isEmpty()){
            throw new SumoException("Invalid Request","there is Already Event in IN Process State");
        }
        List<StockEventDefinitionData> stockEventDefinitionDataListNso = scmAssetManagementDao.getNsoCompletedEvent(stockEventDefinition.getUnitId());
        if(!stockEventDefinitionDataListNso.isEmpty()){
            StockEventDefinitionData latestEvent = stockEventDefinitionDataListNso.get(0);
            if(SCMUtil.getDateDifferenceInHours(latestEvent.getLastUpdationTime(),SCMUtil.getCurrentBusinessDate()) < 24){
                throw  new SumoException("Invalid Request","There is Already NSO Event in Completed State In Less Than 24hr");
            }
        }

        if(Objects.equals(stockEventDefinition.getSubType(),StockTakeSubType.MANAGER_HANDOVER.value())){
            Integer unitCafeManagerId = masterDataCache.getUnit(stockEventDefinition.getUnitId()).getUnitCafeManager();
            Integer unitManagerId = masterDataCache.getUnit(stockEventDefinition.getUnitId()).getUnitManager().getId();
            Integer cafeManagerId = masterDataCache.getUnit(stockEventDefinition.getUnitId()).getCafeManager().getId();
            LOG.info(" Unit Cafe Manager Id from Cache ::::::: {} for unit ::: {}", unitCafeManagerId , stockEventDefinition.getUnitId());
            if(!(Objects.equals(stockEventDefinition.getInitiatedBy().getId(),unitCafeManagerId)
                    || Objects.equals(stockEventDefinition.getInitiatedBy().getId(),unitManagerId)
                    || Objects.equals(stockEventDefinition.getInitiatedBy().getId(),cafeManagerId) )){
                LOG.info("Error Creating Manager Handover Event started by ({}_{}) for Unit ({})",
                        stockEventDefinition.getInitiatedBy().getId().toString(),
                        masterDataCache.getEmployeeBasicDetail(stockEventDefinition.getInitiatedBy().getId()).getName(),
                        stockEventDefinition.getUnitId());
                throw new SumoException("Invalid Request","Manager Handover Event Can Only be Initiated by Current Active Unit Manager");
            }
        }

        String appVersion = scmCache.getStockTakeAppVersion();
        stockEventDefinition.setEventCreationDate(SCMUtil.getCurrentTimestamp());
        stockEventDefinition.setEventStatus(StockEventStatusType.IN_PROCESS);
//        stockEventDefinition.setIsSplit(isSplit);
        StockEventDefinitionData stockEventDefinitionData = convert(stockEventDefinition);
        stockEventDefinitionData = scmAssetManagementDao.add(stockEventDefinitionData, true);
        Integer eventId = stockEventDefinitionData.getEventId();
        if (stockEventDefinitionData != null) {
            StockEventDefinition stockEventDefinition1 = convertToStockEventDefinition(stockEventDefinitionData);
            List<AssetDefinition> availableAssets = viewAllTransferableAssetsFromUnit(stockEventDefinition.getUnitId(), false);
            if(stockEventDefinitionData.getSubType().equals(StockTakeSubType.DAILY_DAYCLOSE.value()) ||
                    stockEventDefinitionData.getSubType().equals(StockTakeSubType.WEEKLY_DAYCLOSE.value()) ||
                    stockEventDefinitionData.getSubType().equals(StockTakeSubType.MONTHLY_DAYCLOSE.value())){
                availableAssets = filterAssetsForDayClose(availableAssets,stockEventDefinition.getUnitId(),null, stockEventDefinition1.getSubType());
            }
            List<Integer> nonScannableProductIds = getNonScannableAssetMappings();
            List<StockEventAssetMappingDefinition> assetList = availableAssets.stream().map(asset -> convertToStockEventItem(asset,
                    nonScannableProductIds.contains(asset.getProductId()))).collect(Collectors.toList());
            stockEventDefinition1.setAvailableAssets(assetList);
            Set<String> uniqueCategories = new HashSet<String>();
            for(StockEventAssetMappingDefinition asset: assetList){
                uniqueCategories.add(asset.getSubCategory());
            }
            for(String subCategory: uniqueCategories){
                categoryStatusMap.put(subCategory, "#");
            }
            List<StockEventAssetMappingDefinitionData> assetMappingDataList = assetList.stream().map(assetData -> convertToAssetMappingData(assetData,
                    eventId)).collect(Collectors.toList());
            scmAssetManagementDao.addAll(assetMappingDataList);
            stockEventDefinition1.setAppVersion(appVersion);
            response.setStockEventDefinition(Arrays.asList(stockEventDefinition1));
            response.setStockTakeStatusSubCategoryMap(categoryStatusMap);
            return response;
        } else {
            throw new SumoException("Stock Event creation failure",
                    "Error creating Stock Event. Please contact support.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public synchronized StockEventDefinition initiateChildStockEvent(StockEventDefinition stockEventDefinition) throws SumoException {
        List<StockEventDefinitionData> childStockEventData = scmAssetManagementDao.getParentOrChildEvent(null,stockEventDefinition.getParentId(), null);
        List<StockEventDefinitionData> currentUsersChildEvents = childStockEventData.stream().filter(e -> e.getInitiatedBy().equals(stockEventDefinition.getInitiatedBy().getId()) &&
                e.getEventStatus().equalsIgnoreCase(StockEventStatusType.IN_PROCESS.value())).collect(Collectors.toList());
        List<StockEventDefinitionData> currentSubCategoryEvent = childStockEventData.stream().filter(e -> e.getSubCategory().equalsIgnoreCase(stockEventDefinition.getSubCategory()) &&
                (e.getEventStatus().equalsIgnoreCase(StockEventStatusType.IN_PROCESS.value()) || e.getEventStatus().equalsIgnoreCase(StockEventStatusType.COMPLETED.value()))).collect(Collectors.toList());
        if(!childStockEventData.isEmpty() && (!currentUsersChildEvents.isEmpty() || !currentSubCategoryEvent.isEmpty())) {
            StockEventDefinitionData eventDefinitionData = !currentUsersChildEvents.isEmpty() ? currentUsersChildEvents.get(0) : currentSubCategoryEvent.get(0);
            StringBuilder stringBuilder = new StringBuilder("Event ");
            String msg = eventDefinitionData.getSubCategory() +" already " + eventDefinitionData.getEventStatus() + " created by : " + SCMUtil.getCreatedBy(masterDataCache.getEmployee(eventDefinitionData.getInitiatedBy()),eventDefinitionData.getInitiatedBy()) + " at "
                    + AppUtils.getDateString(eventDefinitionData.getEventCreationDate(), "dd-MM-yyyy HH:mm:ss");
            stringBuilder.append(msg);
            if (Objects.nonNull(eventDefinitionData.getDeviceInfo())) {
                String [] deviceInfo = eventDefinitionData.getDeviceInfo().split("#");
                stringBuilder.append(" on Device : ").append(deviceInfo[0]).append(" - ").append(deviceInfo[1]);
            }
            throw new SumoException("Invalid Request", stringBuilder.toString());
        }
        List<StockEventDefinitionData> parentStockEventData = scmAssetManagementDao.getParentOrChildEvent(stockEventDefinition.getParentId(), null, null);
        if(Objects.isNull(parentStockEventData) || parentStockEventData.isEmpty()){
            throw new SumoException("Invalid Request","No Active Parent Event for this event.");
        }
        String appVersion = scmCache.getStockTakeAppVersion();
        stockEventDefinition.setEventCreationDate(SCMUtil.getCurrentTimestamp());
        stockEventDefinition.setEventStatus(StockEventStatusType.IN_PROCESS);
        StockEventDefinitionData stockEventDefinitionData = convert(stockEventDefinition);
        stockEventDefinitionData.setEventId(null);
        stockEventDefinitionData.setDeviceInfo(stockEventDefinition.getDeviceInfo());
        stockEventDefinitionData = scmAssetManagementDao.add(stockEventDefinitionData, true);
        Integer eventId = stockEventDefinitionData.getEventId();
        if (stockEventDefinitionData != null) {
            StockEventDefinition stockEventDefinition1 = convertToStockEventDefinition(stockEventDefinitionData);
            List<AssetDefinition> availableAssets = viewAllTransferableAssetsFromUnit(stockEventDefinition.getUnitId(), false);
            if(stockEventDefinitionData.getSubType().equals(StockTakeSubType.DAILY_DAYCLOSE.value()) ||
                    stockEventDefinitionData.getSubType().equals(StockTakeSubType.WEEKLY_DAYCLOSE.value()) ||
                    stockEventDefinitionData.getSubType().equals(StockTakeSubType.MONTHLY_DAYCLOSE.value())){
                availableAssets = filterAssetsForDayClose(availableAssets,stockEventDefinition.getUnitId(),stockEventDefinition.getSubCategory(), stockEventDefinition1.getSubType());
            }
            if (stockEventDefinitionData.getSubType().equals(StockTakeSubType.REGULAR.value())) {
                availableAssets = availableAssets.stream().filter(assetDefinition ->  stockEventDefinition.getSubCategory().equals(assetDefinition.getSubCategoryDefinition().getName()))
                        .collect(Collectors.toList());
            }
            List<Integer> nonScannableProductIds = getNonScannableAssetMappings();
            List<StockEventAssetMappingDefinition> assetList = availableAssets.stream().map(asset -> convertToStockEventItem(asset,
                    nonScannableProductIds.contains(asset.getProductId()))).collect(Collectors.toList());
            stockEventDefinition1.setAvailableAssets(assetList);
            List<StockEventAssetMappingDefinitionData> assetMappingDataList = assetList.stream().map(assetData -> convertToAssetMappingData(assetData,
                    eventId)).collect(Collectors.toList());
            scmAssetManagementDao.addAll(assetMappingDataList);
            stockEventDefinition1.setAppVersion(appVersion);
            stockEventDefinition1.setParentId(stockEventDefinition.getParentId());
            stockEventDefinition1.setSubCategory(stockEventDefinition.getSubCategory());
            return stockEventDefinition1;
        } else {
            throw new SumoException("Stock Event creation failure",
                    "Error creating Stock Event. Please contact support.");
        }
    }

    private List<AssetDefinition> filterAssetsForDayClose(List<AssetDefinition> availableAssets, Integer unitId, String subCategory, String subType) {
        List<AssetDefinition> assetList = availableAssets;
        UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitId);
        String unitCategory = unit.getCategory().value();
//        Map<Integer,Integer> inventoryListWithMappedSku = new HashMap<>();
//            if(SCMUtil.isCafe(masterDataCache.getUnitBasicDetail(unitId).getCategory())){
//                inventoryListWithMappedSku = stockManagementDao.getMappedSkuWithInventoryListCafe();
//            }else{
//                inventoryListWithMappedSku = stockManagementDao.getMappedSkuWithInventoryList(unitId);
//            }
            Map<String, List<Integer>> frequencyClassMapping = new HashMap<>();
            if(Objects.equals(subType, StockTakeSubType.WEEKLY_DAYCLOSE.value())){
                List<Integer> classIdWeeklyList = stockManagementDao.getClassListFromUnitType(unitCategory,null,StockTakeFrequencyEnum.WEEKLY.value());
                frequencyClassMapping.put(StockTakeFrequencyEnum.WEEKLY.value(), classIdWeeklyList);
            } else if(Objects.equals(subType, StockTakeSubType.MONTHLY_DAYCLOSE.value())){
                List<Integer> classIdMonthlyList = stockManagementDao.getClassListFromUnitType(unitCategory,null,StockTakeFrequencyEnum.MONTHLY.value());
                frequencyClassMapping.put(StockTakeFrequencyEnum.MONTHLY.value(), classIdMonthlyList);
            }
            assetList = getFixedAssetsForDayClose(availableAssets, frequencyClassMapping, subType);
            if(!Objects.isNull(subCategory)){
            assetList = assetList.stream().filter(assetDefinition ->  subCategory.equals(assetDefinition.getSubCategoryDefinition().getName()))
                    .collect(Collectors.toList());
            }
        return assetList;
    }

    private List<AssetDefinition> getFixedAssetsForDayClose(List<AssetDefinition> availableAssets, Map<String, List<Integer>> frequencyClassMapping, String subType) {
        List<Integer> inventoryLists = new ArrayList<>();
//        if(Objects.equals(subType, StockTakeSubType.DAILY_DAYCLOSE.value())){
//            inventoryLists.add(25);
//        }
        if(Objects.equals(subType, StockTakeSubType.WEEKLY_DAYCLOSE.value())){
            inventoryLists.addAll(frequencyClassMapping.get(StockTakeFrequencyEnum.WEEKLY.value()));
        }
        else if(Objects.equals(subType, StockTakeSubType.MONTHLY_DAYCLOSE.value())){
            inventoryLists.addAll(frequencyClassMapping.get(StockTakeFrequencyEnum.MONTHLY.value()));
        }

        List<Integer> finalInventoryLists = inventoryLists;
        return availableAssets.stream()
                .filter(assetDefinition ->  finalInventoryLists.contains(scmCache.getProductDefinition(assetDefinition.getProductId()).getClassificationId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<StockEventDefinitionData> getParentOrChildEvent(Integer eventId, Integer parentId, String subCategory){
        return scmAssetManagementDao.getParentOrChildEvent(eventId, parentId, subCategory);
    }

    @Override
    public List<StockEventAssetMappingDefinitionData> findAssetsByEventId(Integer eventId){
        return scmAssetManagementDao.findAssetsByEventId(eventId);
    }

    @Override
    public List<StockEventDefinitionData> getLastStockTakeEventByAsset(Integer assetId, String eventStatus){
        return scmAssetManagementDao.getLastStockTakeEventByAsset(assetId, eventStatus);
    }

    @Override
    public StockEventDefinitionData getLatestNSOEventByUnit(int unitId, String eventStatus ,String subtype) {
        return scmAssetManagementDao.getLatestNSOEventByUnit(unitId, eventStatus, subtype);
    }

    @Override
    public List<String> getSubTypeListForApp(Integer unitId) throws SumoException {
        List<String> list = new ArrayList<String>();
        Boolean isCafe = SCMUtil.isCafe(masterDataCache.getUnitBasicDetail(unitId).getCategory());
        list.add("AUDIT");
        list.add("REGULAR");
        list.add("NSO");
        list.add("MANAGER_HANDOVER");
        FixedAssetDayCloseResponseObject response = null;
        if(Boolean.TRUE.equals(isCafe)){
            response = stockManagementService.getAssetsToDayCloseToday(unitId);
        }else{
            response = warehouseStockManagementService.getAssetsToDayCloseToday(unitId);
        }
        if(Objects.nonNull(response.getAvailableAssetsWeekly()) || Boolean.TRUE.equals(response.getBlockWeekly())){
            list.add("WEEKLY_DAYCLOSE");
        }
        if(Objects.nonNull(response.getAvailableAssetsMonthly()) || Boolean.TRUE.equals(response.getBlockMonthly())){
            list.add("MONTHLY_DAYCLOSE");
        }
        return list;
    }

    @Override
    public Boolean checkFaDaycloseEnabled(Integer unitId){
        String flag = masterDataCache.getUnitBasicDetail(unitId).getFaDaycloseEnabled();
        if(Objects.equals(flag, "Y")){
            return true;
        }
        return false;
    }

    @Override
    public Map<String, Pair<Date,Long>> getPendingDayCloseEvents(Integer unitId){
        Map<String, Pair<Date, Long>> events = new HashMap<>();
        long dayDiff = -1;
        Date lastDailyDayClose = stockManagementDao.getLastDayCloseDone(unitId, StockTakeSubType.DAILY_DAYCLOSE.value());
        if(Objects.nonNull(lastDailyDayClose))
        {
            dayDiff = SCMUtil.getDayDifference(lastDailyDayClose, SCMUtil.getCurrentTimestamp());
            events.put(StockTakeSubType.DAILY_DAYCLOSE.value(), new Pair<>(lastDailyDayClose , dayDiff));
        }
        Date lastWeeklyDayClose = stockManagementDao.getLastDayCloseDone(unitId, StockTakeSubType.WEEKLY_DAYCLOSE.value());
        if(Objects.nonNull(lastWeeklyDayClose))
        {
            dayDiff = SCMUtil.getDayDifference(lastWeeklyDayClose, SCMUtil.getCurrentTimestamp());
            events.put(StockTakeSubType.WEEKLY_DAYCLOSE.value(), new Pair<>(lastWeeklyDayClose , dayDiff));
        }
        Date lastMonthlyDayClose = stockManagementDao.getLastDayCloseDone(unitId, StockTakeSubType.MONTHLY_DAYCLOSE.value());
        if(Objects.nonNull(lastMonthlyDayClose))
        {
            dayDiff = SCMUtil.getDayDifference(lastMonthlyDayClose, SCMUtil.getCurrentTimestamp());
            events.put(StockTakeSubType.MONTHLY_DAYCLOSE.value(), new Pair<>(lastMonthlyDayClose ,dayDiff));
        }
        return events;
    }

    private StockEventAssetMappingDefinitionData convertToAssetMappingData(StockEventAssetMappingDefinition stockEventAssetMappingDefinition, Integer eventId) {
        StockEventAssetMappingDefinitionData stockEventAssetMappingDefinitionData = new StockEventAssetMappingDefinitionData();
        stockEventAssetMappingDefinitionData.setAssetId(stockEventAssetMappingDefinition.getAssetId());
        stockEventAssetMappingDefinitionData.setAssetStatus(AssetStatusType.PENDING_LOST.value());
        if (stockEventAssetMappingDefinition.getAuditDate() != null) {
            stockEventAssetMappingDefinitionData.setAuditDate(stockEventAssetMappingDefinition.getAuditDate());
            stockEventAssetMappingDefinitionData.setAuditedBy(stockEventAssetMappingDefinition.getAuditedBy().getId());
            stockEventAssetMappingDefinitionData.setAuditStatus(stockEventAssetMappingDefinition.getAuditStatus());
        }

        stockEventAssetMappingDefinitionData.setCreationDate(SCMUtil.getCurrentTimestamp());
        stockEventAssetMappingDefinitionData.setEventId(eventId);
        stockEventAssetMappingDefinitionData.setUnitId(stockEventAssetMappingDefinition.getUnitId());
        stockEventAssetMappingDefinitionData.setManualChecked(AppUtils.setStatus(stockEventAssetMappingDefinition.getNonScannable()));
        return stockEventAssetMappingDefinitionData;
    }

    private StockEventAssetMappingDefinition convertToStockEventItem(AssetDefinition assetDefinition, Boolean isNonScannable) {
        StockEventAssetMappingDefinition result = new StockEventAssetMappingDefinition();

        ProductDefinition productDefinition = scmCache.getProductDefinition(assetDefinition.getProductId());
        result.setAssetId(assetDefinition.getAssetId());
        result.setAssetName(assetDefinition.getAssetName());
        result.setSkuName(scmCache.getSkuDefinition(assetDefinition.getSkuId()).getSkuName());
        result.setAssetStatus(assetDefinition.getAssetStatus());
        result.setUnitId(assetDefinition.getUnitId());
        result.setAssetTagValue(assetDefinition.getTagValue());
        result.setNonScannable(isNonScannable);
        result.setExists(true);
        result.setFound(false);
        result.setProductId(productDefinition.getProductId());
        result.setProductName(productDefinition.getProductName());
        result.setSubCategory(assetDefinition.getSubCategoryDefinition().getName());
        return result;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public StockEventDefinition viewEvent(int eventId) {
        StockEventDefinitionData stockEventDefinitionData = scmAssetManagementDao.getStockEventById(eventId);
        if (stockEventDefinitionData != null) {
            return convertToStockEventDefinition(stockEventDefinitionData);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean printAssetTagValue(int assetId, int printedBy) throws SumoException {
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class, assetId);
        AssetDefinitionDataLog assetDefinitionDataLog = logCurrentStatus(assetDefinitionData);
        if(SCMUtil.isCafe(masterDataCache.getUnitBasicDetail(assetDefinitionData.getUnitId()).getCategory())){
            StockEventDefinitionData currentEvent = scmAssetManagementDao.findEventbyAssetId(assetId, assetDefinitionData.getUnitId());
            if(Objects.nonNull(currentEvent)){
                Map<Integer,String> assetMap = scmAssetManagementDao.findAssetsByEventId(currentEvent.getEventId()).stream()
                        .collect(Collectors.toMap(StockEventAssetMappingDefinitionData::getAssetId,StockEventAssetMappingDefinitionData::getAssetStatus));
                if(Objects.nonNull(assetMap.get(assetId)) ) {
                        if( Objects.equals(assetMap.get(assetId),AssetStatusType.PENDING_LOST.value())){
                    ApprovalDetailData approval = new ApprovalDetailData();
                    approval.setAssetId(assetId);
                    approval.setEventId(currentEvent.getEventId());
                    approval.setSkuId(assetDefinitionData.getSKUId());
                    approval.setUnitId(assetDefinitionData.getUnitId());
                    approval.setType(ApprovalType.LOST_TAG.value());
                    approval.setStatus(ApprovalStatus.PENDING.value());
                    approval.setSkuName(scmCache.getSkuDefinition(assetDefinitionData.getSKUId()).getSkuName());
                    approval.setCost(assetDefinitionData.getProcurementCost());
                    approval.setRequestedBy(currentEvent.getInitiatedBy());
                    approval.setRequestedTo(masterDataCache.getUnitBasicDetail(assetDefinitionData.getUnitId()).getUnitManagerId());
                    approval.setRequestDate(SCMUtil.getCurrentTimestamp());
                    scmAssetManagementDao.add(approval,false);
                }else{
                    throw new SumoException("Asset Already Scanned");
                }
            }else{
                    throw new SumoException("Asset Not Part of Current Event");
                }
            }else {
                throw new SumoException("No Ongoing Stock Take Event for this Asset found at this Unit");
            }
        }
        if (assetDefinitionDataLog != null) {
            assetDefinitionData.setTagPrintCount(assetDefinitionData.getTagPrintCount() + 1);
            assetDefinitionData.setLastTagPrintedBy(printedBy);
            assetDefinitionData.setLastTagPrintDate(SCMUtil.getCurrentTimestamp());
            scmAssetManagementDao.update(assetDefinitionData, true);
            return true;
        } else {
            return false;
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean printBulkAssetTagValue(List<Integer> assetIds, Integer printedBy) throws SumoException {
        List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.getAssetsByIds(assetIds);
        for (AssetDefinitionData assetDefinitionData : assetDefinitionDataList) {
            AssetDefinitionDataLog assetDefinitionDataLog = logCurrentStatus(assetDefinitionData);
            if (assetDefinitionDataLog != null) {
                assetDefinitionData.setTagPrintCount(assetDefinitionData.getTagPrintCount() + 1);
                assetDefinitionData.setLastTagPrintedBy(printedBy);
                assetDefinitionData.setLastTagPrintDate(SCMUtil.getCurrentTimestamp());
            }
        }
        scmAssetManagementDao.update(assetDefinitionDataList, true);
        return true;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean markNotFoundInAudit(String assetTag, Integer updatedBy) {
        try {
            LOG.info("Trying to update the Tag : {} as  it is not found in audit", assetTag);
            AssetDefinitionData assetDefinitionData = scmAssetManagementDao.getAssetByTagValue(assetTag);
            if (Objects.nonNull(assetDefinitionData)) {
                AssetDefinition assetDefinition = updateAssetStatus(assetDefinitionData, AssetStatusType.PENDING_LOST);
                AssetRecoveryDefinitionData assetRecoveryDefinitionData = convert(assetDefinition,
                        updatedBy, updatedBy, SCMUtil.getCurrentTimestamp(), null);
                scmAssetManagementDao.add(assetRecoveryDefinitionData, true);
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While Marking the asset as LOST or FOUND :: ", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public StockEventDefinition updateStockEvent(StockEventDefinition stockEventDefinition) throws SumoException {
        StockEventDefinitionData stockEventDefinitionData = scmAssetManagementDao.find(StockEventDefinitionData.class,
                stockEventDefinition.getEventId());
        if (stockEventDefinitionData != null) {
            List<StockEventDefinitionData> updateEventList = new ArrayList<>();
            StockEventDefinitionData currentData = convert(stockEventDefinition);
            if(Objects.nonNull(stockEventDefinition.getParentId())){
                List<StockEventAssetMappingDefinitionData> child = new ArrayList<>();
                List<StockEventAssetMappingDefinitionData> parent = new ArrayList<>();
                Map<Integer, StockEventAssetMappingDefinitionData> eventAssets = scmAssetManagementDao.findAssetsByEventId(stockEventDefinition.getEventId()).stream().filter(e -> !e.getAssetStatus().equalsIgnoreCase(AssetStatusType.PENDING_LOST.value())).collect(Collectors.toMap(StockEventAssetMappingDefinitionData::getAssetId, Function.identity()));
                Map<Integer, StockEventAssetMappingDefinitionData> parentEventAssets = scmAssetManagementDao.findAssetsByEventId(stockEventDefinition.getParentId()).stream().filter(e -> !e.getAssetStatus().equalsIgnoreCase(AssetStatusType.PENDING_LOST.value())).collect(Collectors.toMap(StockEventAssetMappingDefinitionData::getAssetId, Function.identity()));

                for (Map.Entry<Integer,StockEventAssetMappingDefinitionData> entry : eventAssets.entrySet()) {
                    if (parentEventAssets.containsKey(entry.getKey())) {
                        entry.getValue().setAssetStatus(AssetStatusType.PENDING_LOST.name());
                        child.add(entry.getValue());
                        parentEventAssets.get(entry.getKey()).setAssetStatus(AssetStatusType.PENDING_LOST.name());
                        parent.add(parentEventAssets.get(entry.getKey()));
                    }
                }
                scmAssetManagementDao.update(child, true);
                scmAssetManagementDao.update(parent, true);

            }
            currentData.setLastUpdationTime(SCMUtil.getCurrentTimestamp());
            updateEventList.add(currentData);
            if(currentData.getEventStatus().equals(StockEventStatusType.ABANDONED.value()) && Objects.nonNull(currentData.getSplit()) && currentData.getSplit().equals("Y")){
                List<StockEventDefinitionData> childEvents = scmAssetManagementDao.getParentOrChildEvent(null, currentData.getEventId(), null);
                for(StockEventDefinitionData event: childEvents){
                    event.setEventStatus(StockEventStatusType.ABANDONED.value());
                }
                updateEventList.addAll(childEvents);
            }
            currentData = scmAssetManagementDao.update(updateEventList, true).get(0);
            if (currentData != null) {
                stockEventDefinition = convertToStockEventDefinition(currentData);
                return stockEventDefinition;
            } else {
                throw new SumoException("Stock Event Could not be updated",
                        "Error updating Stock Event. Please contact support.");
            }
        } else {
            throw new SumoException("Stock Event Could not be found",
                    "Error updating Stock Event. Please contact support.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean transferAssetAgainstGR(GoodsReceivedData goodsReceivedData, List<RejectedGrItem> rejectedGrItems,Integer eventId) throws SumoException, InventoryUpdateException {

        TransferOrderType transferOrderType = TransferOrderType.fromValue(goodsReceivedData.getTransferOrderType());
        UnitDetailData unitDetailData = scmAssetManagementDao.find(UnitDetailData.class,
                goodsReceivedData.getGeneratedForUnitId());
        for (GoodsReceivedItemData goodsReceivedItemData : goodsReceivedData.getGoodsReceivedItemDatas()) {
            /*
             * Check if GRItem is disputed , if yes then do nothing
             */
            SCMOrderPackagingData scmOrderPackaging = goodsReceivedItemData.getPackagingDetails().get(0);
            if (scmOrderPackaging.getNumberOfUnitsRejected() != null
                    && scmOrderPackaging.getNumberOfUnitsRejected().compareTo(new BigDecimal(0.0f)) > 0) {
                continue;
            }

            /*
             * Get previous LOG
             */
            AssetDefinitionDataLog previousLog = scmAssetManagementDao
                    .getLatestAssetDefinitionDataLog(goodsReceivedItemData.getAssociatedAssetId());
            // LOG Previous Data
            AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                    goodsReceivedItemData.getAssociatedAssetId());
            if (assetDefinitionData.getAssetStatus().equals(AssetStatusType.DECAPITALIZED.value())) {
                if (Objects.isNull(goodsReceivedData.getParentGR())) {
                    assetDefinitionData.setUnitType(unitDetailData.getUnitCategory().getCode());
                    assetDefinitionData.setUnitId(unitDetailData.getUnitId());
                    assetDefinitionData.setIsInTransit(AppUtils.setStatus(false));
                    BigDecimal currentValue = AssetHelper.getCurrentValueOfAsset(assetDefinitionData);
                    Date endDate = SCMUtil.getCurrentTimestamp();
                    assetDefinitionData.setActualEndDate(endDate);
                    assetDefinitionData.setWriteOffAmount(currentValue);
                    assetDefinitionData.setIsWriteOff("Y");
                    assetDefinitionData.setWriteOffType(AssetStatusType.DECAPITALIZED.value());
                    scmAssetManagementDao.flush();
                    String tagValue = assetDefinitionData.getTagValue();
                    GoodsReceived goodsReceived = SCMDataConverter.convert(goodsReceivedData, new IdCodeName(
                            goodsReceivedData.getGeneratedBy(),"",""), new IdCodeName(goodsReceivedData.getReceivedBy(),"",""),
                            new IdCodeName(goodsReceivedData.getCancelledBy(),"",""),
                            new IdCodeName(goodsReceivedData.getGeneratedForUnitId(),"",""),new IdCodeName(goodsReceivedData.getGenerationUnitId(),"","")
                            , true, scmCache, masterDataCache);
                    List<GoodsReceivedItem> itemList = goodsReceived.getGoodsReceivedItems().stream().filter(grItem -> grItem.getAssociatedAssetTagValue().
                            equals(tagValue)).collect(Collectors.toList());
                    itemList.get(0).getDrillDowns().get(0).setKeyType(PriceUpdateEntryType.SKU.name());
                    goodsReceived.setGoodsReceivedItems(itemList);
                    goodsReceived.setTransferOrderType(TransferOrderType.REGULAR_TRANSFER);

                    priceDao.addReceiving(goodsReceived,false);
                } else {
                    assetDefinitionData.setIsInTransit(AppUtils.setStatus(false));
                    assetDefinitionData.setUnitType(unitDetailData.getUnitCategory().getCode());
                    assetDefinitionData.setUnitId(unitDetailData.getUnitId());
                    assetDefinitionData.setAssetStatus(AssetStatusType.READY_FOR_USE.value());
                }

                continue;
            }

            AssetDefinitionDataLog assetDefinitionDataLog = logCurrentStatus(assetDefinitionData);
            updateAssetDataOnGR(assetDefinitionData, goodsReceivedData, unitDetailData.getUnitCategory().getCode());
            AssetStatusType nextStatus = AssetStatusType.valueOf(assetDefinitionData.getAssetStatus());
            boolean updateOwner = true;
            if (transferOrderType.equals(TransferOrderType.RENOVATION_ASSET_TRANSFER)) {
                nextStatus = AssetStatusType.IN_RENOVATION;
                updateOwner = false;
            } else if (transferOrderType.equals(TransferOrderType.BROKEN_ASSET_TRANSFER)) {

            } else if (transferOrderType.equals(TransferOrderType.FIXED_ASSET_TRANSFER)) {
                if (assetDefinitionData.getAssetStatus().equals(AssetStatusType.CREATED.value())
                        || assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_RENOVATION.value())
                        || assetDefinitionData.getAssetStatus().equals(AssetStatusType.READY_FOR_USE.value())) {
                    nextStatus = AssetStatusType.IN_USE;
                }
                /*
                 * If asset is transferred to WAREHOUSE in IN_USE OR CREATED STATUS THEN SET ITS
                 * STATUS TO READY_FOR_USE
                 */
                if (unitDetailData.getUnitCategory().getCode().equals(UnitCategory.WAREHOUSE.value())) {
                    if (assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_USE.value())) {
                        nextStatus = AssetStatusType.READY_FOR_USE;
                    } else if (assetDefinitionData.getAssetStatus().equals(AssetStatusType.CREATED.value())) {
                        nextStatus = AssetStatusType.READY_FOR_USE;
                    }
                }
            } else {
                throw new SumoException("Unexpected transfer order recieved",
                        "Error while doing GR. Please contact support.");
            }
            assetDefinitionData.setIsInTransit(AppUtils.setStatus(false));
            assetDefinitionData = updateOwnerAndUnitAndTransferData(assetDefinitionData,
                    unitDetailData.getUnitCategory().getCode(), unitDetailData.getUnitId(), updateOwner, nextStatus);
            assetDefinitionData = scmAssetManagementDao.update(assetDefinitionData, true);
            /*
             * Create TransferOrderNote if applicable
             */
            createTransferOrderNote(goodsReceivedData, assetDefinitionData, previousLog, assetDefinitionDataLog);
        }
        try {
            if (rejectedGrItems.size() > 0) {
                LOG.info("sending Rejected Gr mail");
                RejectedGr rejectedGr = getRejectedGr(rejectedGrItems);
                RejectedGREmailNotificationTemplate rejectedGREmailNotificationTemplate = new RejectedGREmailNotificationTemplate(rejectedGrItems,
                        masterDataCache.getUnitBasicDetail(rejectedGrItems.get(0).getRejectedUnitId()), props.getBasePath(), rejectedGr);
                notificationService.setRejectedGrNotification(rejectedGREmailNotificationTemplate);
            }
        } catch (Exception e) {
            LOG.error("Error Occurred While sending Rejected GR mail :: ", e);
        }
       return goodsReceiveManagementService.settleGrEvent(eventId,goodsReceivedData.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public RejectedGr getRejectedGr(List<RejectedGrItem> rejectedGrItems) {
        RejectedGr result = new RejectedGr();
        TransferOrderData transferOrderData = scmAssetManagementDao.find(TransferOrderData.class, rejectedGrItems.get(0).getToId());
        result.setTransferOrderId(rejectedGrItems.get(0).getToId());
        result.setTransferringUnitName(masterDataCache.getUnitBasicDetail(transferOrderData.getGenerationUnitId()).getName());
        result.setTransferredBy(masterDataCache.getEmployee(transferOrderData.getGeneratedBy()));
        result.setTransferType(transferOrderData.getToType());
        result.setGrId(rejectedGrItems.get(0).getOriginalGrId());
        result.setToGenerationTime(transferOrderData.getGenerationTime());
        RequestOrderData requestOrderData = Objects.nonNull(rejectedGrItems.get(0).getRoId()) ?
                scmAssetManagementDao.find(RequestOrderData.class, rejectedGrItems.get(0).getRoId()) : null;
        if (Objects.nonNull(requestOrderData)) {
            result.setRoId(rejectedGrItems.get(0).getRoId());
            result.setRoBy(masterDataCache.getEmployee(requestOrderData.getGeneratedBy()));
            result.setRoGenerationTime(requestOrderData.getGenerationTime());
        }
        return result;
    }

    private void createTransferOrderNote(GoodsReceivedData goodsReceivedData, AssetDefinitionData assetDefinitionData,
                                         AssetDefinitionDataLog previousLog, AssetDefinitionDataLog currentLog) throws SumoException {
        Unit receivingUnit = masterDataCache.getUnit(goodsReceivedData.getGeneratedForUnitId());
        Unit sendingUnit = masterDataCache.getUnit(goodsReceivedData.getGenerationUnitId());
        if (previousLog != null) {
            if (previousLog.getUnitId().equals(receivingUnit.getId())
                    && receivingUnit.getFamily().value().equals(UnitCategory.WAREHOUSE.value())) {
                if (!sendingUnit.getLocation().getState().getCode()
                        .equals(receivingUnit.getLocation().getState().getCode())) {
                    if (Objects.nonNull(currentLog.getLastTransferId())) {
                        TransferOrderData originalTO = scmAssetManagementDao.find(TransferOrderData.class,
                                currentLog.getLastTransferId());
                        if (Objects.nonNull(goodsReceivedData.getTransferOrderData())) {
                            try {
                                LOG.info("Trying to create Transfer Order Note");
                                TransferOrderData returnTO = goodsReceivedData.getTransferOrderData();
                                Long creditNoteUniqueLong = atomicLongForTONoteUniqueSequence.addAndGet(1L);
                                Long debitNoteUniqueLong = atomicLongForTONoteUniqueSequence.addAndGet(1L);
                                TransferOrderNoteData creditNote = convert(assetDefinitionData, receivingUnit,
                                        sendingUnit, TransferOrderNoteType.CREDIT_NOTE, originalTO, returnTO, creditNoteUniqueLong,
                                        goodsReceivedData);
                                TransferOrderNoteData debitNote = convert(assetDefinitionData, receivingUnit,
                                        sendingUnit, TransferOrderNoteType.DEBIT_NOTE, originalTO, returnTO, debitNoteUniqueLong,
                                        goodsReceivedData);
                                creditNote = scmAssetManagementDao.add(creditNote, true);
                                LOG.info("Credit Note Created with TransferOrderNoteData id : "
                                        + creditNote.getTransferOrderNoteId());
                                debitNote = scmAssetManagementDao.add(debitNote, true);
                                LOG.info(
                                        "Debit Note Created with TransferOrderNoteData id : " + debitNote.getTransferOrderNoteId());
                            } catch (Exception e) {
                                LOG.error("Exception Occurred While Creating Transfer Order Note Data for GR ID : {} ::: ", goodsReceivedData.getId(), e);
                            }
                        }
                    }
                }

            } else {
                LOG.info("TransferOrderNote entry is not applicable for asset : " + assetDefinitionData.getAssetId()
                        + " new Unit : " + assetDefinitionData.getUnitId() + " previous Unit : "
                        + previousLog.getUnitId());
            }
        }
    }

    /*
     * updateAssetDataOnGR method updates the unit detail and set latest transfer
     * details
     */
    private void updateAssetDataOnGR(AssetDefinitionData assetDefinitionData, GoodsReceivedData goodsReceivedData,
                                     String unitType) {
        assetDefinitionData.setUnitType(unitType);
        assetDefinitionData.setUnitId(goodsReceivedData.getGeneratedForUnitId());
        assetDefinitionData.setLastTransferType(goodsReceivedData.getTransferOrderType());
        if (goodsReceivedData.getTransferOrderData() != null) {
            assetDefinitionData.setLastTransferId(goodsReceivedData.getTransferOrderData().getId());
            assetDefinitionData.setLastTransferedBy(goodsReceivedData.getTransferOrderData().getGeneratedBy());
            assetDefinitionData.setLastTransferDate(goodsReceivedData.getTransferOrderData().getGenerationTime());
        } else {
            GoodsReceivedData originalGrData = goodsReceiveManagementService.getOriginalGrData(goodsReceivedData);
            if (Objects.nonNull(originalGrData) && Objects.nonNull(originalGrData.getTransferOrderData())
                    && Objects.nonNull(originalGrData.getTransferOrderData().getId())) {
                assetDefinitionData.setLastTransferId(originalGrData.getTransferOrderData().getId());
                assetDefinitionData.setLastTransferedBy(originalGrData.getTransferOrderData().getGeneratedBy());
                assetDefinitionData.setLastTransferDate(originalGrData.getTransferOrderData().getGenerationTime());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean transferAssetAgainstGatepass(List<GatepassItemAssetMapping> gatepassItemAssetMappings,
                                                GatepassData gatepassData) throws SumoException {

        for (GatepassItemAssetMapping gatepassItemAssetMapping : gatepassItemAssetMappings) {
            AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                    gatepassItemAssetMapping.getAssetId());
            logCurrentStatus(assetDefinitionData);
            /*
             * Save GatepassItemAssetMapping
             */
            GatepassItemAssetMappingData gatepassItemAssetMappingData = convert(gatepassItemAssetMapping);
            gatepassItemAssetMappingData = scmAssetManagementDao.add(gatepassItemAssetMappingData, true);
            gatepassItemAssetMapping = convert(gatepassItemAssetMappingData);
            GatepassOperationType gatepassOperationType = gatepassItemAssetMapping.getGatePassType();
            AssetStatusType nextStatus;
            if (gatepassOperationType.equals(GatepassOperationType.REPAIR)) {
                nextStatus = AssetStatusType.IN_REPAIR;
                assetDefinitionData = updateOwnerAndUnitAndTransferData(assetDefinitionData, "REPAIR_VENDOR",
                        gatepassData.getVendorId(), false, nextStatus);
            } else if (gatepassOperationType.equals(GatepassOperationType.INTERNAL_TXN)
                    || gatepassOperationType.equals(GatepassOperationType.NPD)
                    || gatepassOperationType.equals(GatepassOperationType.QUALITY_CHECK)
                    || gatepassOperationType.equals(GatepassOperationType.SAMPLE)) {
                if (gatepassData.getReturnable() == null || !AppUtils.getStatus(gatepassData.getReturnable())) {
                    nextStatus = AssetStatusType.SETTLED;

                } else {
                    nextStatus = AssetStatusType.PENDING_RETURN;
                }
                VendorDetailData vendorDetailData = scmAssetManagementDao.find(VendorDetailData.class,
                        gatepassData.getVendorId());
                assetDefinitionData = updateOwnerAndUnitAndTransferData(assetDefinitionData, vendorDetailData.getType(),
                        gatepassData.getVendorId(), false, nextStatus);
            } else {
                throw new SumoException("Gatepass Operation type not supported for asset",
                        "Error creating gatepass. Please contact support.");
            }

            scmAssetManagementDao.update(assetDefinitionData, true);
        }
        return true;
    }

    @Override
    public AssetDefinitionData updateOwnerAndUnitAndTransferData(AssetDefinitionData assetDefinitionData,
                                                                 String unitType, Integer unitId, boolean changeOwner, AssetStatusType nextStatus) throws SumoException {
        assetDefinitionData.setUnitType(unitType);
        assetDefinitionData.setUnitId(unitId);
        assetDefinitionData.setAssetStatus(nextStatus.value());
        Date startDate = SCMUtil.getCurrentTimestamp();
        if (changeOwner) {
            assetDefinitionData.setOwnerType(unitType);
            assetDefinitionData.setOwnerId(unitId);
        }
        AssetTransferMappingData assetTransferMappingData = scmAssetManagementDao
                .getLatestAssetDepreciationMapping(assetDefinitionData.getAssetId());
        if (assetTransferMappingData != null) {
            if (!AppUtils.getStatus(assetTransferMappingData.getIsWriteOff())) {
                assetTransferMappingData.setEndDate(SCMUtil.getCurrentTimestamp());
            }
            assetTransferMappingData = scmAssetManagementDao.update(assetTransferMappingData, true);
            LOG.info("Update assetTransferMappingData of " + assetTransferMappingData.getAssetId() + " set end date "
                    + assetTransferMappingData.getEndDate());
        } else {
            // set Start Date and Expected End Date in AssetDefinitionData
            assetDefinitionData.setStartDate(startDate);
            int lifeTimeInDays = assetDefinitionData.getLifeTimeInDays();
            Date expectedEndDate = AppUtils.getDayBeforeOrAfterDay(startDate, lifeTimeInDays);
            assetDefinitionData.setExpectedEndDate(expectedEndDate);
        }

        AssetTransferMappingData assetTransferMappingDataNext = convert(assetDefinitionData,
                startDate);
        if (assetDefinitionData.getRecoveryStatus() != null
                && assetDefinitionData.getRecoveryStatus().equals(AssetRecoveryStatusType.PENDING_LOST.value())
                && !nextStatus.equals(AssetStatusType.LOST_IDENTIFIED)) {
            assetDefinitionData.setRecoveryStatus(AssetRecoveryStatusType.FOUND_ASSET.value());
        }
        if (nextStatus.equals(AssetStatusType.SETTLED) || nextStatus.equals(AssetStatusType.SCRAPPED)
                || nextStatus.equals(AssetStatusType.LOST_IDENTIFIED)) {
            Date endDate = SCMUtil.getCurrentTimestamp();
            assetTransferMappingDataNext.setEndDate(endDate);
            assetTransferMappingDataNext.setIsWriteOff("Y");
            /*
             * Set Actual End date , and write off amount before setting is write off flag
             * true, also set is write off flag and write off type
             */
            BigDecimal currentValue = AssetHelper.getCurrentValueOfAsset(assetDefinitionData);
            assetDefinitionData.setActualEndDate(endDate);
            assetDefinitionData.setWriteOffAmount(currentValue);
            assetDefinitionData.setIsWriteOff("Y");
            assetDefinitionData.setWriteOffType(nextStatus.value());
            if (nextStatus.equals(AssetStatusType.LOST_IDENTIFIED)) {
                assetDefinitionData.setRecoveryType(nextStatus.value());
                BigDecimal recoveryAmount = currentValue
                        .compareTo(assetDefinitionData.getGrossBlock().divide(new BigDecimal(2))) > 0 ? currentValue
                        : assetDefinitionData.getGrossBlock().divide(new BigDecimal(2));
                assetDefinitionData.setRecoveryAmount(recoveryAmount);
                assetDefinitionData.setRecoveryStatus(AssetRecoveryStatusType.INITIATED.value());
                BigDecimal totalRecoveryAmount = assetDefinitionData.getProcurementCost();
                long dayDiff = SCMUtil.getActualDayDifference(AppUtils.getDate(assetDefinitionData.getStartDate()), SCMUtil.getCurrentBusinessDate());
                if(dayDiff > 1095){
                    totalRecoveryAmount =  SCMUtil.multiply(BigDecimal.valueOf(0.6), totalRecoveryAmount);
                }else if(dayDiff > 730){
                    totalRecoveryAmount =  SCMUtil.multiply(BigDecimal.valueOf(0.8), totalRecoveryAmount);
                }else if(dayDiff > 365) {
                    totalRecoveryAmount = SCMUtil.multiply(BigDecimal.valueOf(0.9), totalRecoveryAmount);
                }
                assetDefinitionData.setTotalRecoverAmount(totalRecoveryAmount);
            }
            if (nextStatus.equals(AssetStatusType.SCRAPPED)) {
                AssetScrappedMappingData assetScrappedMappingData = convert(
                        assetDefinitionData.getAssetId(), assetDefinitionData.getUnitId(), currentValue, endDate);
                AssetTransferMappingData lastInUse = scmAssetManagementDao
                        .getLastEntryWithStatus(assetDefinitionData.getAssetId(), AssetStatusType.IN_USE.value());
                if (lastInUse != null) {
                    assetScrappedMappingData.setUnitId(lastInUse.getUnitId());
                }
                assetScrappedMappingData = scmAssetManagementDao.add(assetScrappedMappingData, true);
            }
        } else if (nextStatus.equals(AssetStatusType.LOST_ADJUSTED)) {
            Date endDate = SCMUtil.getCurrentTimestamp();
            assetTransferMappingDataNext.setEndDate(endDate);
            assetTransferMappingDataNext.setIsWriteOff("Y");
            assetDefinitionData.setRecoveryStatus(AssetRecoveryStatusType.RECOVERED.value());
        } else if (nextStatus.equals(AssetStatusType.PENDING_LOST)) {
            assetDefinitionData.setRecoveryType(nextStatus.value());
            assetDefinitionData.setRecoveryStatus(AssetRecoveryStatusType.PENDING_LOST.value());
        }

        scmAssetManagementDao.add(assetTransferMappingDataNext, true);
        return assetDefinitionData;
    }

    private List<StockEventAssetMappingDefinitionData> removeDuplicateRows(List<StockEventAssetMappingDefinitionData> assetsList){
            Map<Integer,Boolean> foundMap = new HashMap<>();
            List<StockEventAssetMappingDefinitionData> uniqueList = new ArrayList<>();
            for(StockEventAssetMappingDefinitionData asset : assetsList){
                if(!foundMap.containsKey(asset.getAssetId())){
                    foundMap.put(asset.getAssetId(),true);
                    uniqueList.add(asset);
                }
            }
            return uniqueList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public AssetDefinitionDataLog logCurrentStatus(AssetDefinitionData assetDefinitionData) throws SumoException {
        AssetDefinitionDataLog assetDefinitionDataLog = convert(assetDefinitionData);
        assetDefinitionDataLog.setUpdationDate(SCMUtil.getCurrentTimestamp());
        assetDefinitionDataLog = scmAssetManagementDao.add(assetDefinitionDataLog, false);
        LOG.info("Logged Asset snapshot with assetId " + assetDefinitionDataLog.getAssetId());
        return assetDefinitionDataLog;
    }

    @Override
    public List<ProfileAttributeMapping> getProfileAttributeMappingsByProfile(int profileId, String status) {
        List<ProfileAttributeMappingData> profileAttributeMappingDataList = scmAssetManagementDao
                .getProfileAttributeMappingByProfileId(profileId, status);
        List<ProfileAttributeMapping> list = new ArrayList<>();
        if (profileAttributeMappingDataList != null) {
            for (ProfileAttributeMappingData profileAttributeMappingData : profileAttributeMappingDataList) {
                IdCodeName createdBy = SCMUtil.generateIdCodeName(profileAttributeMappingData.getCreatedBy(), "",
                        masterDataCache.getEmployees().get(profileAttributeMappingData.getCreatedBy()));
                IdCodeName updatedBy = SCMUtil.generateIdCodeName(profileAttributeMappingData.getUpdatedBy(), "",
                        masterDataCache.getEmployees().get(profileAttributeMappingData.getUpdatedBy()));
                ProfileAttributeMapping profileAttributeMapping = convert(profileAttributeMappingData,
                        createdBy, updatedBy);
                list.add(profileAttributeMapping);
            }
        }
        return list;
    }

    private  StockEventDefinition  stockEventForTransfer(List<StockEventAssetMappingDefinitionData> currentList,
                                                             Map<Integer, AssetDefinition> assetDefinitionMap,
                                                             List<AssetDefinition> availableAssets,
                                                             String appVersion,
                                                             StockEventDefinitionData stockEventDefinitionData){
        List<StockEventAssetMappingDefinitionData> duplicateList = getDuplicateEntries(currentList);
        for(StockEventAssetMappingDefinitionData stockEventAssetMappingDefinitionData : duplicateList){
            scmAssetManagementDao.delete(stockEventAssetMappingDefinitionData);
        }
        currentList = scmAssetManagementDao.findAssetsByEventId(stockEventDefinitionData.getEventId()).stream()
                .filter(e -> !Objects.equals(e.getAssetStatus(), AssetStatusType.MARKED_LOST.value())).collect(Collectors.toList());
        List<StockEventAssetMappingDefinition> scannedAssetList = currentList.stream()
                .map(asset -> SCMDataConverter.convert(asset, assetDefinitionMap, new IdCodeName(stockEventDefinitionData.getAuditedBy(), "", ""),scmCache))
                .collect(Collectors.toList());
        Map<Integer,Boolean> scannedAssetsMap = new HashMap<>();
        scannedAssetsMap = scannedAssetList.stream().collect(Collectors.toMap(StockEventAssetMappingDefinition::getAssetId,StockEventAssetMappingDefinition::isFound));
        Map<Integer, Boolean> finalScannedAssetsMap = scannedAssetsMap;
        List<StockEventAssetMappingDefinition> assetList = availableAssets.stream().map(asset -> convertToStockEventItem(asset,
                false)).filter(item -> !finalScannedAssetsMap.containsKey(item.getAssetId())).collect(Collectors.toList());
        List<FaTransferData> faTransferDataList = scmAssetManagementDao.fetchFaTransferData(stockEventDefinitionData.getEventId());

        Map<Integer, org.apache.commons.math3.util.Pair<BigDecimal,BigDecimal>> productReqMap = new HashMap<>();
        for(FaTransferData faTransferData : faTransferDataList){
            BigDecimal scannedQty = Objects.nonNull(faTransferData.getScannedQty()) ? faTransferData.getScannedQty() : BigDecimal.ZERO;

            productReqMap.put(faTransferData.getProductId(),new Pair<>(faTransferData.getTransferredQty(),scannedQty));
        }


            StockEventDefinition sed = convertToStockEventDefinition(stockEventDefinitionData);
            sed.setAvailableAssets(assetList);
            sed.setProductRequestQtyMap(productReqMap);
            sed.setScannedAssets(scannedAssetList);
            sed.setAppVersion(appVersion);
            sed.setDeviceInfo(stockEventDefinitionData.getDeviceInfo());
         return sed;
    }

    private StockEventDefinition stockEventForReceiving(StockEventDefinitionData stockEventDefinitionData, String appVersion) throws SumoException {
       GrStockEvent grStockEvent = goodsReceiveManagementService.getGrStockEvent(stockEventDefinitionData.getGrId());
        List<FaGrData> faGrDataList = grStockEvent.getFaGrDataList();
        List<StockEventAssetMappingDefinition> assetList = new ArrayList<>();
        List<StockEventAssetMappingDefinition> scannedAssetList = new ArrayList<>();
        Map<Integer, Pair<BigDecimal,BigDecimal>> productReqMap = new HashMap<>();
        for(FaGrData f : faGrDataList){
            AssetDefinitionData assetDefinitionData =  scmAssetManagementDao.find(AssetDefinitionData.class,f.getAssetId());
            AssetDefinition  assetDefinition = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData,false);
            StockEventAssetMappingDefinition stockEventAssetMappingDefinition = convertToStockEventItem(assetDefinition,false);
            if(f.getScannedQty().compareTo(BigDecimal.valueOf(0))==0){
               assetList.add(stockEventAssetMappingDefinition);
            } else if(f.getScannedQty().compareTo(BigDecimal.valueOf(1)) == 0){
                scannedAssetList.add(stockEventAssetMappingDefinition);
            }
            productReqMap.put(f.getAssetId(),new Pair<>(f.getReceivedQty(),f.getScannedQty()));
        }
        StockEventDefinition sed = convertToStockEventDefinition(stockEventDefinitionData);
        sed.setAvailableAssets(assetList);
        sed.setProductRequestQtyMap(productReqMap);
        sed.setScannedAssets(scannedAssetList);
        sed.setAppVersion(appVersion);
        sed.setDeviceInfo(stockEventDefinitionData.getDeviceInfo());
        return sed;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public StockTakeInitResponse  getAllEventByUnit(int unitId, String eventStatus ,Integer roId, Integer userId) throws SumoException {
        StockTakeInitResponse response = new StockTakeInitResponse();
        List<StockEventDefinition> list = new ArrayList<>();
        Map<String, String> categoryStatusMap = new HashMap<>();
        Map<Integer, StockEventAssetMappingDefinitionData> parentEventAssets = new HashMap<>();
        Map<Integer, StockEventAssetMappingDefinitionData> childEventAssets = new HashMap<>();
        Map<Integer, String> assetSubCategoryMap = new HashMap<>();
        List<StockEventDefinitionData> stockEventDefinitionDataList = new ArrayList<>();
        List<StockEventDefinitionData> PendingApprovalEventList = scmAssetManagementDao.getEventByUnit(unitId,
                StockEventStatusType.PENDING_APPROVAL.value(),roId);
        if(!PendingApprovalEventList.isEmpty()){
//            StockEventDefinition sed = convertToStockEventDefinition(PendingApprovalEventList.get(0));
//            if(Objects.nonNull(sed)){
//                response.setStockEventDefinition(Arrays.asList(sed));
//            }
//            return response;
            stockEventDefinitionDataList = PendingApprovalEventList;
        }else{
            stockEventDefinitionDataList = scmAssetManagementDao.getEventByUnit(unitId,
                    eventStatus,roId);
        }
        String appVersion = scmCache.getStockTakeAppVersion();
        List<AssetDefinition> availableAssets = viewAllTransferableAssetsFromUnit(unitId, false);
        if (stockEventDefinitionDataList != null && !stockEventDefinitionDataList.isEmpty()) {
            if(stockEventDefinitionDataList.get(0).getSubType().equals(StockTakeSubType.DAILY_DAYCLOSE.value()) ||
                    stockEventDefinitionDataList.get(0).getSubType().equals(StockTakeSubType.WEEKLY_DAYCLOSE.value()) ||
                    stockEventDefinitionDataList.get(0).getSubType().equals(StockTakeSubType.MONTHLY_DAYCLOSE.value())){
                availableAssets = filterAssetsForDayClose(availableAssets,unitId,null, stockEventDefinitionDataList.get(0).getSubType());
            }
            Map<Integer, AssetDefinition> assetDefinitionMap = availableAssets.stream().collect(Collectors.toMap(AssetDefinition::getAssetId,
                    Function.identity()));
            List<StockEventAssetMappingDefinitionData> currentList = scmAssetManagementDao.findAssetsByEventId(stockEventDefinitionDataList.get(0).getEventId()).stream()
                    .filter(e -> !Objects.equals(e.getAssetStatus(), AssetStatusType.MARKED_LOST.value())).collect(Collectors.toList());
            if (eventStatus.equalsIgnoreCase(StockEventStatusType.INITIATED.value())) {
               for(StockEventDefinitionData s : stockEventDefinitionDataList ){
                   if(s.getEventType().equalsIgnoreCase(StockEventType.TRANSFER_OUT.value())){
                       StockEventDefinition sed = stockEventForTransfer(currentList,assetDefinitionMap, availableAssets, appVersion,s);
                      list.add(sed);
                   } else if(s.getEventType().equalsIgnoreCase(StockEventType.ASSET_RECEIVING.value())){
                      StockEventDefinition sed =  stockEventForReceiving(s,appVersion);
                      list.add(sed);
                   }
               }
            } else {
                List<Integer> nonScannableProductIds = new ArrayList<>();
                nonScannableProductIds = scmAssetManagementDao.findNonScannableMapping(null, true).stream().map(product -> product.getProductId()).
                        collect(Collectors.toList());
                for (StockEventDefinitionData stockEventDefinitionData : stockEventDefinitionDataList) {
                    StockEventDefinition sed = convertToStockEventDefinition(stockEventDefinitionData);
                    List<StockEventAssetMappingDefinitionData> updateList = new ArrayList<>();
                    List<StockEventAssetMappingDefinitionData> deleteList = new ArrayList<>();
                    List<StockEventAssetMappingDefinitionData> duplicateList = getDuplicateEntries(currentList);
                    for(StockEventAssetMappingDefinitionData stockEventAssetMappingDefinitionData : duplicateList){
                        scmAssetManagementDao.delete(stockEventAssetMappingDefinitionData);
                    }
                    currentList = scmAssetManagementDao.findAssetsByEventId(stockEventDefinitionData.getEventId()).stream()
                            .filter(e -> !Objects.equals(e.getAssetStatus(), AssetStatusType.MARKED_LOST.value())).collect(Collectors.toList());
                    Map<Integer,StockEventAssetMappingDefinitionData> currentListMap = currentList.stream().collect(Collectors.toMap(StockEventAssetMappingDefinitionData::getAssetId,
                            Function.identity()));
                    if(!Objects.equals(stockEventDefinitionData.getEventStatus(),StockEventStatusType.PENDING_APPROVAL.value())){
                    if(Objects.nonNull(stockEventDefinitionData.getSubType()) &&
                            (stockEventDefinitionData.getSubType().equals(StockTakeSubType.DAILY_DAYCLOSE.value()) ||
                            stockEventDefinitionData.getSubType().equals(StockTakeSubType.WEEKLY_DAYCLOSE.value()) ||
                            stockEventDefinitionData.getSubType().equals(StockTakeSubType.MONTHLY_DAYCLOSE.value()))){
                        availableAssets = filterAssetsForDayClose(availableAssets,unitId,null, sed.getSubType());
                    }
                        for(AssetDefinition asset : availableAssets){
                            if(!currentListMap.containsKey(asset.getAssetId())){
                                StockEventAssetMappingDefinitionData assetMappingDefinitionData = convert(convertToStockEventItem(asset,nonScannableProductIds.contains(asset.getProductId())),asset);
                                assetMappingDefinitionData.setEventId(stockEventDefinitionData.getEventId());
                                assetMappingDefinitionData.setAssetStatus(AssetStatusType.PENDING_LOST.value());
                                if(nonScannableProductIds.contains(asset.getProductId())){
                                    assetMappingDefinitionData.setManualChecked(AppUtils.setStatus(true));
                                }
                                currentList.add(assetMappingDefinitionData);
                                updateList.add(assetMappingDefinitionData);
                            }
                            currentListMap.remove(asset.getAssetId());
                        }
                        deleteList.addAll(currentListMap.values());
                        if(!updateList.isEmpty()){
                            updateList = removeDuplicateRows(updateList);
                            scmAssetManagementDao.addAll(updateList);
                        }
                        if(!deleteList.isEmpty()){
                            for(StockEventAssetMappingDefinitionData assetMappingDefinitionData : deleteList){
                                scmAssetManagementDao.delete(assetMappingDefinitionData);
                            }
                            currentList = scmAssetManagementDao.findAssetsByEventId(stockEventDefinitionData.getEventId()).stream()
                                    .filter(e -> !Objects.equals(e.getAssetStatus(), AssetStatusType.MARKED_LOST.value())).collect(Collectors.toList());
                        }
                    }

                    List<StockEventAssetMappingDefinition> assetList = currentList.stream()
                            .map(asset -> SCMDataConverter.convert(asset, assetDefinitionMap, new IdCodeName(stockEventDefinitionData.getAuditedBy(), "", ""),
                                    scmCache))
                            .collect(Collectors.toList());
                    assetSubCategoryMap = assetList.stream().collect(Collectors.toMap(StockEventAssetMappingDefinition::getAssetId, StockEventAssetMappingDefinition::getSubCategory));
                    List<FaStockEventExtraScannedItems> extraScannedItemsList = scmAssetManagementDao.findExtraScannedAssetsByEventId(sed.getEventId());
                    if(extraScannedItemsList.isEmpty()){
                        sed.setExtraScannedItems(new ArrayList<>());
                    }else{
                        List<StockEventAssetMappingDefinition> extraScannedAssets = new ArrayList<>();
                        List<FaStockEventExtraScannedItems> extraScannedUpdateList = new ArrayList<>();
                        List<FaStockEventExtraScannedItems> extraScannedItemsRemoveList = new ArrayList<>();
                        Map<Integer,Boolean> uniqueAssetsMap = new HashMap<>();
                        for(FaStockEventExtraScannedItems item : extraScannedItemsList){
                            if(Objects.nonNull(item.getAssetId())){
                                if(uniqueAssetsMap.containsKey(item.getAssetId())){
                                    extraScannedItemsRemoveList.add(item);
                                    continue;
                                }else{
                                    uniqueAssetsMap.put(item.getAssetId(),true);
                                }
                            }
                            AssetDefinitionData assetDefinitionData  = scmAssetManagementDao.find(AssetDefinitionData.class,item.getAssetId());
                            extraScannedAssets.add(convertFromExtraScannedObject(item,assetDefinitionData));
                        }
                        sed.setExtraScannedItems(extraScannedAssets);
                        if(!extraScannedUpdateList.isEmpty()){
                            scmAssetManagementDao.update(extraScannedUpdateList,true);
                        }
                        if(!extraScannedItemsRemoveList.isEmpty()){
                            for(FaStockEventExtraScannedItems duplicateItem : extraScannedItemsRemoveList){
                                scmAssetManagementDao.delete(duplicateItem);
                            }
                        }
                    }
                    sed.setAppVersion(appVersion);
                    sed.setAvailableAssets(assetList);
                    list.add(sed);
                }
            }

            Set<String> uniqueCategories = new HashSet<>();

            for (StockEventAssetMappingDefinition asset: list.get(0).getAvailableAssets()){
                uniqueCategories.add(asset.getSubCategory());
            }

            for (String uniqueSubCategory : uniqueCategories) {
                categoryStatusMap.put(uniqueSubCategory,"#");
            }
            List<StockEventAssetMappingDefinitionData> addAssetList = new ArrayList<>();
            List<StockEventDefinitionData> childStockEventsList =
                    scmAssetManagementDao.getParentOrChildEvent(null, list.get(0).getEventId(),null);
            if(childStockEventsList != null && !childStockEventsList.isEmpty()) {
                List<StockEventDefinition> childEventsList = filterChildEvents(list.get(0), childStockEventsList);
                Map<Integer, String> finalAssetSubCategoryMap = assetSubCategoryMap;
                for(StockEventDefinition event: childEventsList){
                    if(event.getInitiatedBy().getId().equals(userId) && event.getEventStatus().value().equals(StockEventStatusType.IN_PROCESS.value())){
                        parentEventAssets = currentList.stream().filter(e -> finalAssetSubCategoryMap.get(e.getAssetId()).equals(event.getSubCategory())).collect(Collectors.toMap(StockEventAssetMappingDefinitionData::getAssetId, Function.identity()));
                        childEventAssets = scmAssetManagementDao.findAssetsByEventId(event.getEventId()).stream().collect(Collectors.toMap(StockEventAssetMappingDefinitionData::getAssetId, Function.identity()));

                        for(Integer assetId: parentEventAssets.keySet()){
                            if(!childEventAssets.containsKey(assetId)){
                                StockEventAssetMappingDefinitionData parentEvent  = parentEventAssets.get(assetId);
                                StockEventAssetMappingDefinitionData newAsset = new StockEventAssetMappingDefinitionData();
                                newAsset.setEventId(event.getEventId());
                                newAsset.setUnitId(parentEvent.getUnitId());
                                newAsset.setAssetId(parentEvent.getAssetId());
                                newAsset.setAssetStatus(parentEvent.getAssetStatus());
                                newAsset.setCreationDate(parentEvent.getCreationDate());
                                newAsset.setAuditedBy(parentEvent.getAuditedBy());
                                newAsset.setAuditDate(parentEvent.getAuditDate());
                                newAsset.setAssetStatus(parentEvent.getAssetStatus());
                                newAsset.setManualChecked(parentEvent.getManualChecked());

                                addAssetList.add(newAsset);
                            }
                        }
                        for(Integer assetId: childEventAssets.keySet()){
                            if(!parentEventAssets.containsKey(assetId)){
                                scmAssetManagementDao.delete(childEventAssets.get(assetId));
                            }
                        }
                        scmAssetManagementDao.addAll(addAssetList);
                        list.add(event);
                    }
                    categoryStatusMap.put(event.getSubCategory(),SCMUtil.getCreatedBy(masterDataCache.getEmployee(event.getInitiatedBy().getId()),event.getInitiatedBy().getId()) + " # " + event.getEventStatus().toString());
                }
            }
            list.get(0).setStockTakeStatusSubCategoryMap(categoryStatusMap);

        }
        response.setStockEventDefinition(list);
        response.setStockTakeStatusSubCategoryMap(categoryStatusMap);

        return response;
    }

    private List<StockEventDefinition> filterChildEvents(StockEventDefinition parentEvent, List<StockEventDefinitionData> childEvents){
        List<StockEventDefinition> result = new ArrayList<>();


            for (StockEventDefinitionData child : childEvents) {
                StockEventDefinition childEvent = convertToStockEventDefinition(child);
                    childEvent.setEventId(child.getEventId());
                    childEvent.setParentId(parentEvent.getEventId());
                    childEvent.setEventStatus(StockEventStatusType.valueOf(child.getEventStatus()));
                    childEvent.setInitiatedBy(new IdCodeName(child.getInitiatedBy(),"",""));
                    childEvent.setEventCreationDate(child.getEventCreationDate());
                    childEvent.setSubCategory(child.getSubCategory());
                    if (Objects.nonNull(parentEvent.getAvailableAssets())) {
                        childEvent.setAvailableAssets(parentEvent.getAvailableAssets().stream().filter(assetDefinition -> child.getSubCategory().equals(assetDefinition.getSubCategory()))
                                .collect(Collectors.toList()));
                    }
                    if (Objects.nonNull(parentEvent.getScannedAssets())) {
                        childEvent.setScannedAssets(parentEvent.getScannedAssets().stream().filter(assetDefinition -> child.getSubCategory().equals(assetDefinition.getSubCategory()))
                                .collect(Collectors.toList()));
                    }
                    if (Objects.nonNull(parentEvent.getExtraScannedItems())) {
                        childEvent.setExtraScannedItems(parentEvent.getExtraScannedItems().stream().filter(assetDefinition -> child.getSubCategory().equals(assetDefinition.getSubCategory()))
                                .collect(Collectors.toList()));
                    }
                result.add(childEvent);
            }
        return result;
    }

    private  List<StockEventAssetMappingDefinitionData> getDuplicateEntries(List<StockEventAssetMappingDefinitionData> assetList){
        Map<Integer,StockEventAssetMappingDefinitionData> duplicateEntryMap = new HashMap<>();
        List<StockEventAssetMappingDefinitionData> duplicateEntryList = new ArrayList<>();
        for(StockEventAssetMappingDefinitionData asset : assetList){
            if(Objects.isNull(asset.getAssetId())){
                continue;
            }
            if(!duplicateEntryMap.containsKey(asset.getAssetId())){
                duplicateEntryMap.put(asset.getAssetId(),asset);
            }else{
                if(!asset.getAssetStatus().equalsIgnoreCase(duplicateEntryMap.get(asset.getAssetId()).getAssetStatus())){
                    if(duplicateEntryMap.get(asset.getAssetId()).getAssetStatus().equalsIgnoreCase(AssetStatusType.CREATED.value())){
                        duplicateEntryList.add(duplicateEntryMap.get(asset.getAssetId()));
                    }else{
                        duplicateEntryList.add(asset);
                    }
                }else{
                    duplicateEntryList.add(asset);
                }
            }
        }
        return duplicateEntryList;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public StockEventAssetMappingDefinitionRequest verifyAndCreateEventAssetMapping(
            StockEventAssetMappingDefinitionRequest request) throws SumoException {
        StockEventDefinitionData stockEventDefinitionData = scmAssetManagementDao.find(StockEventDefinitionData.class,
                request.getEventId());
        if (stockEventDefinitionData != null) {
            if (stockEventDefinitionData.getEventStatus().equals(StockEventStatusType.IN_PROCESS.value())) {
                if (stockEventDefinitionData.getUnitId().equals(request.getUnitId())) {
                    List<AssetDefinition> assetDefinitions = viewAllTransferableAssetsFromUnit(request.getUnitId(),
                            false);
                    if (request.getStatus().equals(EventAssetMappingStatus.VERIFY)) {
                        return verifyStockTake(request, assetDefinitions);
                    } else if (request.getStatus().equals(EventAssetMappingStatus.CREATE)) {
                        return createStockTake(request, stockEventDefinitionData);
                    } else {
                        throw new SumoException("Event Mapping status not supported",
                                "Event mapping status not supported. Please contact support.");
                    }
                } else {
                    throw new SumoException(
                            "Stock event unit :  " + request.getUnitId() + " and unit is not mapped "
                                    + stockEventDefinitionData.getUnitId(),
                            "Stock event unit is not appropriate. Please contact support.");
                }

            } else {
                throw new SumoException("Stock event is already " + stockEventDefinitionData.getEventStatus(),
                        "Stock event status is not appropriate. Please contact support.");
            }
        } else {
            throw new SumoException("Could not found stock event",
                    "Could not found stock event. Please contact support.");
        }
    }

    private StockEventAssetMappingDefinitionRequest verifyStockTake(StockEventAssetMappingDefinitionRequest request,
                                                                    List<AssetDefinition> assetDefinitions) {
        List<StockEventAssetMappingDefinition> list = new ArrayList<>();
        List<Integer> nonScannableProductId = new ArrayList<>();
        nonScannableProductId = scmAssetManagementDao.findNonScannableMapping(null, true).stream().map(product -> product.getProductId()).
                collect(Collectors.toList());
        Map<String, StockEventAssetMappingDefinition> stockEventAssetMappingDefinitionMap = new HashMap<>();
        for (StockEventAssetMappingDefinition assetMappingDefinition : request.getStockEventAssetMappingDefinitions()) {
            stockEventAssetMappingDefinitionMap.put(assetMappingDefinition.getAssetTagValue(), assetMappingDefinition);
        }
        Map<String, AssetDefinition> assetDefinitionHashMap = new HashMap<>();
        for (AssetDefinition assetDefinition : assetDefinitions) {
            assetDefinitionHashMap.put(assetDefinition.getTagValue(), assetDefinition);
        }
        for (Map.Entry<String, AssetDefinition> entry : assetDefinitionHashMap.entrySet()) {
            Boolean nonScannable = false;
            if (nonScannableProductId.contains(entry.getValue().getProductId())) {
                nonScannable = true;
            }
            if (stockEventAssetMappingDefinitionMap.containsKey(entry.getValue().getTagValue())) {
                StockEventAssetMappingDefinition stockEventAssetMappingDefinition = stockEventAssetMappingDefinitionMap
                        .get(entry.getValue().getTagValue());
                if (stockEventAssetMappingDefinition.isFound()) {
                    stockEventAssetMappingDefinition.setAssetId(entry.getValue().getAssetId());

                }
                stockEventAssetMappingDefinitionMap.remove(entry.getValue().getTagValue());
                stockEventAssetMappingDefinition.setAssetName(entry.getValue().getAssetName());
                stockEventAssetMappingDefinition.setExists(true);
                stockEventAssetMappingDefinition.setNonScannable(nonScannable);
                list.add(stockEventAssetMappingDefinition);
            } else {
                StockEventAssetMappingDefinition stockEventAssetMappingDefinition = convert(entry.getValue(), request);
                stockEventAssetMappingDefinition.setFound(false);
                stockEventAssetMappingDefinition.setAssetName(entry.getValue().getAssetName());
                stockEventAssetMappingDefinition.setExists(true);
                stockEventAssetMappingDefinition.setNonScannable(nonScannable);
                list.add(stockEventAssetMappingDefinition);
            }
        }
        for (Map.Entry<String, StockEventAssetMappingDefinition> entry : stockEventAssetMappingDefinitionMap
                .entrySet()) {
            StockEventAssetMappingDefinition stockEventAssetMappingDefinition = entry.getValue();
            stockEventAssetMappingDefinition.setExists(false);
            stockEventAssetMappingDefinition.setNonScannable(false);
            list.add(stockEventAssetMappingDefinition);
        }
        request.setStockEventAssetMappingDefinitions(list);
        return request;
    }

    private StockEventAssetMappingDefinitionRequest createStockTake(StockEventAssetMappingDefinitionRequest request,
                                                                    StockEventDefinitionData stockEventDefinitionData) throws SumoException {
        LOG.info("Stock event CREATE for" + request.getUnitId() + " against event " + request.getEventId());
        List<StockEventAssetMappingDefinition> list = new ArrayList<>();
        boolean assetLost = false;
        for (StockEventAssetMappingDefinition assetMappingDefinition : request.getStockEventAssetMappingDefinitions()) {
            if (assetMappingDefinition.isExists()) {
                assetMappingDefinition.setCreationDate(SCMUtil.getCurrentTimestamp());
                if (assetMappingDefinition.isFound()) {
                    // Create Asset Event mapping
                    AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                            assetMappingDefinition.getAssetId());
                    AssetDefinition assetDefinition = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData,
                            false);
                    StockEventAssetMappingDefinitionData data = convert(assetMappingDefinition,
                            assetDefinition);
                    if (Objects.nonNull(assetMappingDefinition.getNonScannable()) && assetMappingDefinition.getNonScannable().equals(Boolean.TRUE)) {
                        data.setManualChecked(AppUtils.setStatus(true));
                    } else {
                        data.setManualChecked(AppUtils.setStatus(false));
                    }
                    data = scmAssetManagementDao.add(data, true);
                    StockEventAssetMappingDefinition eventAssetMappingDefinition = convert(data, null, null,scmCache);
                    list.add(eventAssetMappingDefinition);
                } else {
                    // mark asset as Pending_Lost
                    LOG.info("Asset " + assetMappingDefinition.getAssetId() + " Not Found at unit "
                            + request.getUnitId() + " during event " + request.getEventId());
                    assetLost = true;
                    AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                            assetMappingDefinition.getAssetId());
                    AssetDefinition assetDefinition = updateAssetStatus(assetDefinitionData,
                            AssetStatusType.PENDING_LOST);
                    // create entry in recovery table
                    AssetRecoveryDefinitionData assetRecoveryDefinitionData = convert(assetDefinition,
                            stockEventDefinitionData.getInitiatedBy(), stockEventDefinitionData.getInitiatedBy(),
                            SCMUtil.getCurrentTimestamp(), stockEventDefinitionData);
                    scmAssetManagementDao.add(assetRecoveryDefinitionData, false);

                    StockEventAssetMappingDefinitionData data = convert(assetMappingDefinition,
                            assetDefinition);
                    if (Objects.nonNull(assetMappingDefinition.getNonScannable()) && assetMappingDefinition.getNonScannable().equals(Boolean.TRUE)) {
                        data.setManualChecked(AppUtils.setStatus(true));
                    } else {
                        data.setManualChecked(AppUtils.setStatus(false));
                    }
                    data = scmAssetManagementDao.add(data, true);
                    StockEventAssetMappingDefinition eventAssetMappingDefinition = convert(data, null, null,scmCache);
                    list.add(eventAssetMappingDefinition);

                }
            }
        }
        StockEventStatusType eventStatusType = assetLost ? StockEventStatusType.PENDING_LOST_CONFIRMATION
                : StockEventStatusType.COMPLETED;
        updateStockEventStatus(stockEventDefinitionData, eventStatusType);
        request.setStockEventAssetMappingDefinitions(list);

        return request;
    }

    private FaStockEventExtraScannedItems convertToExtraScannedObject(StockEventAssetMappingDefinition item,
                                                                      AssetDefinitionData assetDefinition){
        FaStockEventExtraScannedItems result = new FaStockEventExtraScannedItems();
        result.setAssetId(item.getAssetId());
        result.setEventId(item.getEventId());
        result.setScannedTime(AppUtils.getCurrentTimestamp());
        result.setAssetStatus(assetDefinition.getAssetStatus());
        result.setOwnerUnitId(assetDefinition.getUnitId());

        return  result;
    }

    private StockEventAssetMappingDefinition convertFromExtraScannedObject(FaStockEventExtraScannedItems item , AssetDefinitionData assetDefinition){
        StockEventAssetMappingDefinition result = new StockEventAssetMappingDefinition();
        result.setAssetId(item.getAssetId());
        result.setAssetName(assetDefinition.getAssetName());
        result.setUnitId(item.getOwnerUnitId());
        result.setUnitName(masterDataCache.getUnit(assetDefinition.getUnitId()).getName());
        result.setAssetTagValue(assetDefinition.getTagValue());
        return  result;
    }


    private List<StockEventAssetMappingDefinition> filterOutDuplicateEntries(List<StockEventAssetMappingDefinition> extraScannedAssets){
        Map<Integer,Boolean> uniqueAssetMap = new HashMap<>();
        List<StockEventAssetMappingDefinition> uniqueAssets = new ArrayList<>();
        for(StockEventAssetMappingDefinition asset : extraScannedAssets){
            if(Objects.isNull(asset.getAssetId())){
                uniqueAssets.add(asset);
                continue;
            }
            if(!uniqueAssetMap.containsKey(asset.getAssetId())){
                uniqueAssets.add(asset);
            }
            uniqueAssetMap.put(asset.getAssetId(),true);
        }
        return  uniqueAssets;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean pauseFAStockTakeEvent(StockEventAssetMappingDefinitionRequest request) throws SumoException {
        List<StockEventDefinitionData> events = scmAssetManagementDao.getParentOrChildEvent(request.getEventId(),null,request.getSubCategory());
        LOG.info("Number of Events ::: {}",events.size());
        if(Objects.nonNull(events.get(0).getDeviceInfo()) && !Objects.equals(events.get(0).getDeviceInfo(), request.getDeviceInfo())){
            LOG.error("Request Event Id :::: {}", request.getEventId());
            LOG.error("Current Device Info ::::{}", request.getDeviceInfo());
            LOG.error("Child Event Id :::: {}",events.get(0).getEventId());
            throw new SumoException(String.format("DEVICE_INFO_MISMATCH"),"DEVICE_INFO_MISMATCH#"+events.get(0).getDeviceInfo());
        }
        Map<Integer, Boolean> assetFoundMap = new HashMap<>();
        assetFoundMap = request.getStockEventAssetMappingDefinitions().stream().collect(Collectors.toMap(StockEventAssetMappingDefinition::getAssetId,
                StockEventAssetMappingDefinition::isFound, (a1, a2) -> {
                    return a1;
                }));
        StockEventDefinitionData stockEventDefinitionData = scmAssetManagementDao.find(StockEventDefinitionData.class,request.getEventId());
        if(stockEventDefinitionData.getEventType().equalsIgnoreCase(StockEventType.TRANSFER_OUT.value())){
            func1(request);
        } else if (stockEventDefinitionData.getEventType().equalsIgnoreCase(StockEventType.ASSET_RECEIVING.value())){
            assetReceivingStockEvent(request);
        } else{
            func2(request, assetFoundMap);
        }
        return true;
    }

    public void assetReceivingStockEvent(StockEventAssetMappingDefinitionRequest stockEventAssetMappingDefinitionRequest) throws SumoException {
   try{
       List<StockEventAssetMappingDefinition> scannedAsset = stockEventAssetMappingDefinitionRequest.getStockEventAssetMappingDefinitions();
       List<FaGrData> faGrDataList = scmAssetManagementDao.getFaGrDataForEvent(stockEventAssetMappingDefinitionRequest.getEventId());

       Map<Integer,StockEventAssetMappingDefinition> scannedAssetMap = scannedAsset.stream().collect(Collectors.toMap(StockEventAssetMappingDefinition::getAssetId,Function.identity()));
       for(FaGrData f : faGrDataList){
           if(scannedAssetMap.containsKey(f.getAssetId())){
               f.setScannedQty(BigDecimal.valueOf(1));
           }
       }
       scmAssetManagementDao.update(faGrDataList,true);
   }catch (Exception e){
       LOG.info("#### ERROR in stock event asset receiving #####, {}", e.getMessage());
       throw new SumoException("ASSET_RECEIVING_ERROR",e.getMessage());
   }

    }
    public void func1(StockEventAssetMappingDefinitionRequest request) throws SumoException {
        List<StockEventAssetMappingDefinitionData> assetScannedList = new ArrayList<>();
        Map<Integer,FaTransferData>  faTransferDataMap = new HashMap<>();
        faTransferDataMap =  scmAssetManagementDao.fetchFaTransferData(request.getEventId()).stream().
                collect(Collectors.toMap(FaTransferData::getProductId,Function.identity()));
        Map<Integer,FaTransferData> updateMap = new HashMap<>();
        for(StockEventAssetMappingDefinition item : request.getStockEventAssetMappingDefinitions()){
            StockEventAssetMappingDefinitionData scannedItem = convertToAssetMappingData(item, request.getEventId());
            scannedItem.setAssetStatus(AssetStatusType.CREATED.value());
            FaTransferData faTransferData = faTransferDataMap.get(item.getProductId());
            if(faTransferData == null){
                String productName = scmCache.getProductDefinition(item.getProductId()).getProductName();
                throw  new SumoException("PRODUCT_MISMATCH","Product not matched for this transfer : "+productName+", Tag vlaue : "+item.getAssetTagValue());
            }
            BigDecimal scannedQty = Objects.nonNull(faTransferData.getScannedQty()) ? faTransferData.getScannedQty() : BigDecimal.ZERO;
            faTransferData.setScannedQty(SCMUtil.add(scannedQty,BigDecimal.ONE));
            faTransferDataMap.put(item.getProductId(),faTransferData);
            updateMap.put(item.getProductId(),faTransferData);
            assetScannedList.add(scannedItem);
        }
        if(!updateMap.isEmpty()){
            List<FaTransferData> updateValues  = new ArrayList<>(updateMap.values());
            scmAssetManagementDao.update(updateValues,true);
        }
        scmAssetManagementDao.addAll(assetScannedList);
    }

    public void func2(StockEventAssetMappingDefinitionRequest request, Map<Integer, Boolean> assetFoundMap) {
        List<StockEventAssetMappingDefinitionData> stockEventAssetMappingDefinitionDataList = scmAssetManagementDao.findAssetsByEventId(request.getEventId());
        LOG.info("Number of Assets ::: {}",stockEventAssetMappingDefinitionDataList.size());
        if(Objects.nonNull(request.getExtraScannedItems()) && !request.getExtraScannedItems().isEmpty()){
            List<FaStockEventExtraScannedItems> newExtraItemsList = new ArrayList<>();
            Map<Integer,String> isAlreadyScannedMap = new HashMap<>();
            List<FaStockEventExtraScannedItems> faStockEventExtraScannedItemsList = scmAssetManagementDao.findExtraScannedAssetsByEventId(request.getEventId());
            isAlreadyScannedMap = faStockEventExtraScannedItemsList.stream().collect(Collectors.toMap(FaStockEventExtraScannedItems::getAssetId,
                    FaStockEventExtraScannedItems::getAssetStatus));

            List<StockEventAssetMappingDefinition> extraScannedAsset   = filterOutDuplicateEntries(request.getExtraScannedItems());
            request.setExtraScannedItems(extraScannedAsset);

            for(StockEventAssetMappingDefinition item : request.getExtraScannedItems()){
                if(!isAlreadyScannedMap.containsKey(item.getAssetId())){
                    AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,item.getAssetId());
                    item.setEventId(request.getEventId());
                    isAlreadyScannedMap.put(item.getAssetId(),assetDefinitionData.getAssetStatus());
                    newExtraItemsList.add(convertToExtraScannedObject(item,assetDefinitionData));
                }
            }
            if(!newExtraItemsList.isEmpty()){
                scmAssetManagementDao.addAll(newExtraItemsList);
                LOG.info("Number of Extra Scanned Assets ::: {}",newExtraItemsList.size());
            }

        }
        List<StockEventAssetMappingDefinitionData> updateList = new ArrayList<>();
        List<StockEventAssetMappingDefinitionData> parentUpdateList = new ArrayList<>();
        Integer createdCount =0;
        Integer lostCount = 0;
        for (StockEventAssetMappingDefinitionData assetData : stockEventAssetMappingDefinitionDataList) {
            if (Boolean.TRUE.equals(assetFoundMap.get(assetData.getAssetId())) &&
                    assetData.getAssetStatus().equalsIgnoreCase(AssetStatusType.PENDING_LOST.value())) {
                assetData.setAssetStatus(AssetStatusType.CREATED.value());
                updateList.add(assetData);
                createdCount++;
            } else if (Boolean.FALSE.equals(assetFoundMap.get(assetData.getAssetId())) && assetData.getAssetStatus().
                    equalsIgnoreCase(AssetStatusType.CREATED.value())) {
                assetData.setAssetStatus(AssetStatusType.PENDING_LOST.value());
                updateList.add(assetData);
                lostCount++;
            }
        }
        LOG.info("Found Assets ::::: {}",createdCount);
        LOG.info("PendingLost Assets ::::: {}",lostCount);
        createdCount =0;
        lostCount = 0;
        if(Objects.nonNull(request.getParentId())){
            List<StockEventAssetMappingDefinitionData> stockParentEventAssetMappingDefinitionDataList = scmAssetManagementDao.findAssetsByEventId(request.getParentId());
            for (StockEventAssetMappingDefinitionData assetData : stockParentEventAssetMappingDefinitionDataList) {
                if (Boolean.TRUE.equals(assetFoundMap.get(assetData.getAssetId())) &&
                        assetData.getAssetStatus().equalsIgnoreCase(AssetStatusType.PENDING_LOST.value())) {
                    assetData.setAssetStatus(AssetStatusType.CREATED.value());
                    parentUpdateList.add(assetData);
                    createdCount++;
                } else if (Boolean.FALSE.equals(assetFoundMap.get(assetData.getAssetId())) && assetData.getAssetStatus().
                        equalsIgnoreCase(AssetStatusType.CREATED.value())) {
                    assetData.setAssetStatus(AssetStatusType.PENDING_LOST.value());
                    parentUpdateList.add(assetData);
                    lostCount++;
                }
            }
            LOG.info("Corresponsing Found Assets in Parent ::::: {}",createdCount);
            LOG.info("Corresponsing PendingLost Assets in Parent ::::: {}",lostCount);
            scmAssetManagementDao.update(parentUpdateList, true);
        }

        scmAssetManagementDao.update(updateList, true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean submitFAStockTakeEvent(StockEventAssetMappingDefinitionRequest request) throws SumoException {
        List<StockEventDefinitionData> events = scmAssetManagementDao.getParentOrChildEvent(request.getEventId(),null,null);
        if(Objects.nonNull(events.get(0).getDeviceInfo()) && !Objects.equals(events.get(0).getDeviceInfo(), request.getDeviceInfo())){
            LOG.error("Request Event Id :::: {}", request.getEventId());
            LOG.error("Current Device Info ::::{}", request.getDeviceInfo());
            LOG.error("Child Event Id :::: {}",events.get(0).getEventId());
            throw new SumoException(String.format("DEVICE_INFO_MISMATCH"),"DEVICE_INFO_MISMATCH#"+events.get(0).getDeviceInfo());
        }
        Map<Integer, Boolean> assetFoundMap = new HashMap<>();
        List<StockEventDefinitionData> currentEvent = scmAssetManagementDao.getParentOrChildEvent(request.getEventId(),null,request.getSubCategory());
        assetFoundMap = request.getStockEventAssetMappingDefinitions().stream().collect(Collectors.toMap(StockEventAssetMappingDefinition::getAssetId,
                StockEventAssetMappingDefinition::isFound));
        List<AssetDefinition>  assetList = viewAllTransferableAssetsFromUnit(request.getUnitId(), false);
        if(currentEvent.get(0).getSubType().equals(StockTakeSubType.DAILY_DAYCLOSE.value()) ||
                currentEvent.get(0).getSubType().equals(StockTakeSubType.WEEKLY_DAYCLOSE.value()) ||
                currentEvent.get(0).getSubType().equals(StockTakeSubType.MONTHLY_DAYCLOSE.value())){
            assetList = filterAssetsForDayClose(assetList,request.getUnitId(),null, currentEvent.get(0).getSubType());
        }
        Set<String> uniqueCategories = new HashSet<String>();
        Map<String, String> categoryStatusMap = new HashMap<>();
        for(AssetDefinition asset: assetList){
            uniqueCategories.add(asset.getSubCategoryDefinition().getName());
        }
        if(!Objects.isNull(request.getSubCategory())){
            assetList.stream().filter(assetDefinition ->  request.getSubCategory().equals(assetDefinition.getSubCategoryDefinition().getName())).collect(Collectors.toList());
        }
        Map<Integer, AssetDefinition> assetMap = assetList.stream().collect(Collectors.toMap(AssetDefinition::getAssetId, Function.identity()));
        List<StockEventAssetMappingDefinitionData> stockEventAssetMappingDefinitionDataList = scmAssetManagementDao.findAssetsByEventId(request.getEventId()).stream()
                .filter(e -> !Objects.equals(e.getAssetStatus(), AssetStatusType.MARKED_LOST.value())).collect(Collectors.toList());
        List<Integer> lostAssetsList = new ArrayList<>();
        List<StockEventAssetMappingDefinitionData> updateList = new ArrayList<>();
        StockEventDefinitionData stockEventDefinitionData = scmAssetManagementDao.find(StockEventDefinitionData.class, request.getEventId());
        if(!stockEventDefinitionData.getEventStatus().equalsIgnoreCase(StockEventStatusType.IN_PROCESS.value())){
            throw new SumoException("Invalid Event","Event is in Valid Status!!");
        }
        Boolean isAssetLost = false;
        for (StockEventAssetMappingDefinitionData assetData : stockEventAssetMappingDefinitionDataList) {
            if (Boolean.TRUE.equals(assetFoundMap.get(assetData.getAssetId()))) {
                assetData.setAssetStatus(assetMap.get(assetData.getAssetId()).getAssetStatus().value());
            } else {
//                AssetDefinitionData assetDefinition = scmAssetManagementDao.find(AssetDefinitionData.class, assetData.getAssetId());
//                throw new SumoException(assetDefinition.getTagValue() +  " Asset Is Not Scanned , Please Refresh!!");
//                assetData.setAssetStatus(AssetStatusType.MARKED_LOST.value());
                // mark asset as Pending_Lost
                LOG.info("Asset " + assetData.getAssetId() + " Not Found at unit "
                        + request.getUnitId() + " during event " + request.getEventId());
                List<ApprovalDetailData> pendingApprovals = stockManagementDao.getAssetApprovals(null,null,stockEventDefinitionData.getEventId(),null, ApprovalStatus.PENDING.value());
                Boolean alreadyAdded = false;
                if(Objects.nonNull(pendingApprovals) && !pendingApprovals.isEmpty()){
                    for(ApprovalDetailData approval : pendingApprovals){
                        if(Objects.equals(approval.getAssetId(),assetData.getAssetId())){
                            if(Objects.equals(approval.getType(),ApprovalType.LOST_TAG.value())){
                                throw new SumoException(" Cannot Submit Event ", "Cannot Submit Event : Tag Printed for Asset( Id:"+ approval.getAssetId().toString() +" ) cannot be marked as LOST ");
                            }else{
                                alreadyAdded = true;
                                break;
                            }

                        }
                    }
                    if(alreadyAdded){
                        continue;
                    }
                }
                AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                        assetData.getAssetId());
                ApprovalDetailData approval = new ApprovalDetailData();
                    approval.setAssetId(assetData.getAssetId());
                    approval.setEventId(request.getEventId());
                    approval.setSkuId(assetDefinitionData.getSKUId());
                    approval.setUnitId(request.getUnitId());
                    approval.setType(ApprovalType.LOST_ASSET.value());
                    approval.setStatus(ApprovalStatus.PENDING.value());
                    approval.setSkuName(scmCache.getSkuDefinition(assetDefinitionData.getSKUId()).getSkuName());
                    approval.setCost(assetDefinitionData.getProcurementCost());
                    approval.setRequestedBy(currentEvent.get(0).getInitiatedBy());
                    approval.setRequestedTo(masterDataCache.getUnitBasicDetail(request.getUnitId()).getUnitManagerId());
                    approval.setRequestDate(SCMUtil.getCurrentTimestamp());
                scmAssetManagementDao.add(approval,false);
                isAssetLost = true;
                lostAssetsList.add(assetData.getAssetId());
            }
        }

        if (Boolean.TRUE.equals(isAssetLost)) {
            stockEventDefinitionData.setEventStatus(StockEventStatusType.PENDING_APPROVAL.value());
        } else {
            stockEventDefinitionData.setEventStatus(StockEventStatusType.COMPLETED.value());
        }
        if(Objects.equals(stockEventDefinitionData.getSubType(),StockTakeSubType.MANAGER_HANDOVER.value()) ||
                Objects.equals(stockEventDefinitionData.getSubType(),StockTakeSubType.NSO.value())){
            Unit unit = masterDataCache.getUnit(stockEventDefinitionData.getUnitId());
            unit.setLastHandoverFrom(stockEventDefinitionData.getInitiatedBy().toString()+"_"+masterDataCache.getEmployeeBasicDetail(stockEventDefinitionData.getInitiatedBy()).getName());
            unit.setUnitCafeManager(stockEventDefinitionData.getAuditedBy());
//            unit.setLastHandoverDate(AppUtils.getCurrentTimestamp());
            String unitUpdateUrl = props.getMasterServiceBasePath() + "/master-service/rest/v1/unit-metadata/" + "unit/update/manager";
            try{
//                Integer unitUpdateResponse = WebServiceHelper.postWithAuth(UnitUpdateUrl,props.getAuthToken(),
//                        new Object({"unitId" : stockEventDefinitionData.getUnitId()}), Integer.class);

                Map<String, String> uriVariables = new HashMap<>();
                uriVariables.put("unitId", stockEventDefinitionData.getUnitId().toString());
                uriVariables.put("lastHandoverFrom", stockEventDefinitionData.getInitiatedBy().toString());
                uriVariables.put("lastHandoverDate", encodeValue(AppUtils.formatDate(AppUtils.getCurrentTimestamp(), "yyyy-MM-dd HH:mm:ss")));
                uriVariables.put("unitCafeManagerId", stockEventDefinitionData.getAuditedBy().toString());
                Integer res = WebServiceHelper.exchangeWithAuth(unitUpdateUrl, props.getAuthToken(), HttpMethod.POST, Integer.class,
                        null, uriVariables);
            }catch(Exception e){
                LOG.error(e.getMessage());
                throw new SumoException("Error Updating Manager","Error Occurred while updating cafe manager.");
            }
        }
        stockEventDefinitionData.setLastUpdationTime(SCMUtil.getCurrentTimestamp());
        scmAssetManagementDao.update(stockEventAssetMappingDefinitionDataList, true);
        if(Objects.nonNull(request.getParentId())){
            List<StockEventAssetMappingDefinitionData> stockParentEventAssetMappingDefinitionDataList = scmAssetManagementDao.findAssetsByEventId(request.getParentId());
            for (StockEventAssetMappingDefinitionData assetData : stockParentEventAssetMappingDefinitionDataList) {
                if (Boolean.TRUE.equals(assetFoundMap.get(assetData.getAssetId()))) {
                    assetData.setAssetStatus(assetMap.get(assetData.getAssetId()).getAssetStatus().value());
                }
//                else if (lostAssetsList.contains(assetData.getAssetId())) {
//                    assetData.setAssetStatus(AssetStatusType.MARKED_LOST.value());
//                }
            }
            scmAssetManagementDao.update(stockParentEventAssetMappingDefinitionDataList, true);
        }
        if(!Objects.isNull(request.getSubCategory())){
            boolean allDone = true;
            List<StockEventDefinitionData> list = scmAssetManagementDao.getParentOrChildEvent(null,request.getParentId(),null);
            for(StockEventDefinitionData event : list){
                categoryStatusMap.put(event.getSubCategory(), event.getEventStatus());
            }
            for(String category : uniqueCategories){
                if(categoryStatusMap.get(category) == null){
                    allDone = false;
                    break;
                }
                if (!categoryStatusMap.get(category).equalsIgnoreCase(StockEventStatusType.COMPLETED.value())){
                    allDone = false;
                    break;
                }
            }
            if(allDone){
                //update parent event status
                List<StockEventDefinitionData> eventList = scmAssetManagementDao.getParentOrChildEvent(request.getParentId(), null,null);
                StockEventDefinitionData parentEvent = eventList.get(0);
                if (Boolean.TRUE.equals(isAssetLost)) {
                    parentEvent.setEventStatus(StockEventStatusType.PENDING_APPROVAL.value());
                } else {
                    parentEvent.setEventStatus(StockEventStatusType.COMPLETED.value());
                }
                scmAssetManagementDao.update(parentEvent, true);
                try{
                    sendFAStockTakeReport(request.getParentId(),true);
                }catch(Exception e){
                    LOG.info(e.getMessage());
                }
            }
        }else{
            List<ApprovalDetailData> pendingTagApprovals = stockManagementDao.getAssetApprovals(null,null,stockEventDefinitionData.getEventId(),null,ApprovalStatus.PENDING.value());
            if(!pendingTagApprovals.isEmpty()){
                stockEventDefinitionData.setEventStatus(StockEventStatusType.PENDING_APPROVAL.value());
            }else{
                try{
                    sendFAStockTakeReport(request.getEventId(),true);
                }catch(Exception e){

                    LOG.info(e.getMessage());
                }
            }
        }


        return true;

    }

    private String encodeValue(String value) throws UnsupportedEncodingException{
        return URLEncoder.encode(value, StandardCharsets.UTF_8.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateDeviceInfo(Integer eventId, String deviceInfo) throws SumoException {
        List<StockEventDefinitionData> events = scmAssetManagementDao.getParentOrChildEvent(eventId,null,null);
        if(Objects.nonNull(events) && !events.isEmpty()){
            events.get(0).setDeviceInfo(deviceInfo);
            scmAssetManagementDao.update(events.get(0),true);
            return true;
        }
        return false;
    }


    private void updateStockEventStatus(StockEventDefinitionData stockEventDefinitionData,
                                        StockEventStatusType eventStatusType) throws SumoException {
        stockEventDefinitionData.setEventStatus(eventStatusType.value());
        StockEventDefinition stockEventDefinition = convertToStockEventDefinition(stockEventDefinitionData);
        updateStockEvent(stockEventDefinition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<GatepassItemAssetMapping> getGatepassItemAssetMappingsForGatepassItemId(int gatepassItemId) {
        List<GatepassItemAssetMappingData> gatepassItemAssetMappingData = scmAssetManagementDao
                .getAssociatedAssetMappingForGatepassItemId(gatepassItemId);
        List<GatepassItemAssetMapping> gatepassItemAssetMappings = new ArrayList<>();
        if (gatepassItemAssetMappingData != null) {
            for (GatepassItemAssetMappingData assetMappingData : gatepassItemAssetMappingData) {
                GatepassItemAssetMapping gatepassItemAssetMapping = convert(assetMappingData);
                gatepassItemAssetMappings.add(gatepassItemAssetMapping);
            }
        }
        return gatepassItemAssetMappings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean transferAssetOnGatepassReturn(Gatepass gatepass) throws SumoException {
        GatepassOperationType gatepassOperationType = gatepass.getOperationType();
        for (GatepassItem gatepassItem : gatepass.getItemDatas()) {
            List<GatepassItemAssetMapping> gatePassItemAssetMappings = gatepassItem.getGatepassItemAssetMappings();
            for (GatepassItemAssetMapping gatepassItemAssetMapping : gatePassItemAssetMappings) {

                AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                        gatepassItemAssetMapping.getAssetId());
                /*
                 * Get latest asset status before it was sent out by gate Pass
                 */
                AssetDefinitionDataLog previousLog = scmAssetManagementDao
                        .getLatestAssetDefinitionDataLog(assetDefinitionData.getAssetId());
                AssetStatusType previousStatus = AssetStatusType.valueOf(previousLog.getAssetStatus());
                /*
                 * LOG asset status
                 */
                logCurrentStatus(assetDefinitionData);
                AssetStatusType nextStatus;
                if (gatepassOperationType.equals(GatepassOperationType.REPAIR)) {
                    if (gatepassItemAssetMapping.isFixed()) {
                        nextStatus = AssetStatusType.READY_FOR_USE;
                    } else {
                        nextStatus = AssetStatusType.BROKEN;
                    }

                } else if (gatepassOperationType.equals(GatepassOperationType.QUALITY_CHECK)
                        || gatepassOperationType.equals(GatepassOperationType.SAMPLE)
                        || gatepassOperationType.equals(GatepassOperationType.NPD)
                        || gatepassOperationType.equals(GatepassOperationType.INTERNAL_TXN)) {
                    nextStatus = previousStatus;
                } else {
                    throw new SumoException("Gatepass Operation type not supported for asset",
                            "Error returning gatepass. Please contact support.");
                }

                UnitDetailData unitDetailData = scmAssetManagementDao.find(UnitDetailData.class,
                        gatepass.getSendingUnit().getId());
                assetDefinitionData = updateOwnerAndUnitAndTransferData(assetDefinitionData,
                        unitDetailData.getUnitCategory().getCode(), unitDetailData.getUnitId(), false, nextStatus);
                scmAssetManagementDao.update(assetDefinitionData, true);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public List<EntityAssetMapping> createEntityAssetMapping(List<EntityAssetMapping> entityAssetMappings)
            throws SumoException {
        List<EntityAssetMapping> list = new ArrayList<>();
        for (EntityAssetMapping mapping : entityAssetMappings) {
            EntityAssetMappingData entityAssetMappingData = convert(mapping);
            entityAssetMappingData = scmAssetManagementDao.add(entityAssetMappingData, true);
            mapping = convert(entityAssetMappingData);
            list.add(mapping);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<EntityAssetMapping> getAssociatedEntityAssetMapping(int entityId, int entitySubId, String entityType) {
        List<EntityAssetMapping> list = new ArrayList<>();
        List<EntityAssetMappingData> entityAssetMappingDataList = scmAssetManagementDao
                .getAssociatedEntityAssetMapping(entityId, entitySubId, entityType);
        if (entityAssetMappingDataList != null) {
            for (EntityAssetMappingData entityAssetMappingData : entityAssetMappingDataList) {
                EntityAssetMapping entityAssetMapping = convert(entityAssetMappingData);
                list.add(entityAssetMapping);
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean transferAssetAgainstInvoice(SalesPerformaInvoice salesPerformaInvoice) throws SumoException {
        SalesPerformaType salesPerformaType = salesPerformaInvoice.getType();
        for (SalesPerformaInvoiceItem salesPerformaInvoiceItem : salesPerformaInvoice.getItems()) {
            List<EntityAssetMapping> entityAssetMappings = getAssociatedEntityAssetMapping(salesPerformaInvoice.getId(),
                    salesPerformaInvoiceItem.getId(), EntityAssetMappingType.INVOICE.value());
            for (EntityAssetMapping mapping : entityAssetMappings) {
                AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                        mapping.getAssetId());
                AssetStatusType nextStatus = AssetStatusType.valueOf(assetDefinitionData.getAssetStatus());
                if (salesPerformaType.equals(SalesPerformaType.B2B_SALES)
                        || salesPerformaType.equals(SalesPerformaType.RETURN_TO_VENDOR)) {
                    nextStatus = AssetStatusType.SETTLED;
                } else if (salesPerformaType.equals(SalesPerformaType.SCRAP)) {
                    nextStatus = AssetStatusType.SCRAPPED;
                }
                String unitType = salesPerformaInvoice.getType().value();
                Integer unitId = salesPerformaInvoice.getVendor().getId();

                assetDefinitionData = updateOwnerAndUnitAndTransferData(assetDefinitionData, unitType, unitId, false,
                        nextStatus);
                scmAssetManagementDao.update(assetDefinitionData, true);
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public AssetDefinition updateAssetStatus(AssetDefinitionData assetDefinitionData, AssetStatusType assetStatusType)
            throws SumoException {
        logCurrentStatus(assetDefinitionData);

        assetDefinitionData = updateOwnerAndUnitAndTransferData(assetDefinitionData, assetDefinitionData.getUnitType(),
                assetDefinitionData.getUnitId(), true, assetStatusType);

        assetDefinitionData = scmAssetManagementDao.update(assetDefinitionData, true);
        return convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public List<AssetDefinition> massAssetPutToUse() throws SumoException {
        List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.findAll(AssetDefinitionData.class);
        List<AssetDefinition> list = new ArrayList<>();
        for (AssetDefinitionData data : assetDefinitionDataList) {
            if (data.getUnitType().equals(UnitCategory.CAFE.value())
                    && data.getAssetStatus().equals(AssetStatusType.CREATED.value())) {
                list.add(updateAssetStatus(data, AssetStatusType.IN_USE));
            }
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public AssetDefinition updateAssetStatus(int assetId, String assetStatus) throws SumoException {
        // AssetDefinition assetDefinition = scmCache.getAssetDefinition(assetId);
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class, assetId);
        return updateAssetStatus(assetDefinitionData, AssetStatusType.valueOf(assetStatus));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public List<AssetDefinition> addNewAssets(List<AssetDefinition> assetDefinitions) throws SumoException {
        List<AssetDefinition> list = new ArrayList<>();
        if(Objects.nonNull(assetDefinitions.get(0).getGrItemId())){
           List<AssetDefinitionData> grItemAssets =   scmAssetManagementDao.getAllAssetWithGRItemId(assetDefinitions.get(0).getGrItemId());
           if(Objects.nonNull(grItemAssets) && !grItemAssets.isEmpty()){
               throw new SumoException("Invalid Request","Assets Are Already Registered With This Gr Item Id!!");
           }
        }for (AssetDefinition ad : assetDefinitions) {
            populateAssetData(ad);
            ProductDefinitionData product = scmAssetManagementDao.find(ProductDefinitionData.class, ad.getProductId());
            AssetDefinitionData assetDefinitionData = convert(product, ad);
            assetDefinitionData = scmAssetManagementDao.add(assetDefinitionData, true);
            if (assetDefinitionData != null) {
                AssetDefinition assetDefinition = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData,
                        true);
                addAssociatedProfileAttributeMapping(assetDefinition);
                list.add(assetDefinition);
            } else {
                throw new SumoException("Asset creation failure", "Error creating asset. Please contact support.");
            }

        }
        return list;
    }

    // TODO REMOVE THIS ASS SOON AS BACKDATED ASSET REGISTRATION GETS COMPLETED
    // This method is filling backdated assets with bogus values which will be
    // updated as soon as details are collected through queries
    private void populateBackDatedAsset(AssetDefinition assetDefinition) {
        assetDefinition.setCurrentValue(BigDecimal.ZERO.floatValue());
        Integer skuId = assetDefinition.getSkuId();
        SkuDefinitionData skuDefinitionData = scmAssetManagementDao.find(SkuDefinitionData.class, skuId);
        SubCategoryDefinitionData subCategoryDefinitionData = skuDefinitionData.getLinkedProduct().getSubCategoryDefinition();
        UnitDetailData unitDetailData = scmAssetManagementDao.find(UnitDetailData.class, assetDefinition.getUnitId());
        assetDefinition.setUnitType(unitDetailData.getUnitCategory().getCode());
        assetDefinition.setCreationDate(SCMUtil.getCurrentTimestamp());
        assetDefinition.setActualEndDate(null);
        assetDefinition.setAmcLastDate(null);
        assetDefinition.setAssetImageUrl(skuDefinitionData.getSkuImage());
        assetDefinition.setAssetName(skuDefinitionData.getSkuName());
        assetDefinition.setDailyDepreciationRate(BigDecimal.ZERO.floatValue());
        assetDefinition.setDepreciationRatePa(BigDecimal.ZERO.floatValue());
        assetDefinition.setDepreciationResidue(BigDecimal.ZERO.floatValue());
        assetDefinition.setDepreciationStrategy(AppConstants.ASSET_DEPRECIATION_STRATEGY);
        assetDefinition.setFirstOwnerId(assetDefinition.getUnitId());
        assetDefinition.setFirstOwnerType(assetDefinition.getUnitType());
        assetDefinition.setGrId(0);
        assetDefinition.setGrItemId(0);
        assetDefinition.setHasAMC(false);
        assetDefinition.setHasInsurance(false);
        assetDefinition.setAssetStatus(AssetStatusType.CREATED);
        assetDefinition.setHasWarranty(false);
        assetDefinition.setInsuranceLastDate(null);
        assetDefinition.setInventoryDate(SCMUtil.getCurrentTimestamp());
        assetDefinition.setLastTagPrintDate(SCMUtil.getCurrentTimestamp());
        LifeTimeType lifeTimeType = LifeTimeType.MONTHS;
        assetDefinition.setLifeTimeType(lifeTimeType);
        Integer lifeInDays = -1;
        if(Objects.nonNull(subCategoryDefinitionData.getLifeTimeCategoryMonths().floatValue())){
            assetDefinition.setLifeTimeValue(subCategoryDefinitionData.getLifeTimeCategoryMonths().floatValue());
            lifeInDays = subCategoryDefinitionData.getLifeTimeCategoryMonths() * 30;
        } else if(Objects.nonNull(subCategoryDefinitionData.getShelfLifeInDays())){
            assetDefinition.setLifeTimeInDays(subCategoryDefinitionData.getShelfLifeInDays());
            lifeInDays = subCategoryDefinitionData.getShelfLifeInDays();
        }
        assetDefinition.setStartDate(SCMUtil.getCurrentTimestamp());
        Date expectedEndDate = AppUtils.getDayBeforeOrAfterDay(SCMUtil.getCurrentTimestamp(), (lifeInDays > 0) ? lifeInDays : 0);
        assetDefinition.setExpectedEndDate(expectedEndDate);
        assetDefinition.setOwnerId(assetDefinition.getUnitId());
        assetDefinition.setOwnerType(assetDefinition.getUnitType());
        assetDefinition.setPrice(BigDecimal.ZERO.floatValue());
        assetDefinition.setProcurementCost(BigDecimal.ZERO.floatValue());
        assetDefinition.setQuantity(1);
        assetDefinition.setRealizedDepreciation(BigDecimal.ZERO.floatValue());
        assetDefinition.setTagPrintCount(1);

        assetDefinition.setTagType(TagType.QR_CODE);
        Long assetTagValueInLong = atomicLongForAssetTagValue.addAndGet(1L);
        assetDefinition.setTagValue(Long.toString(assetTagValueInLong, 36));
        assetDefinition.setTax(BigDecimal.ZERO.floatValue());
        assetDefinition.setVendorId(0);
        assetDefinition.setVendorName("");
        assetDefinition.setWarrantyLastDate(null);
        /////////////////////////////////////
        assetDefinition.setGrossBlock(BigDecimal.ZERO.floatValue());
        assetDefinition.setFixedValue(BigDecimal.ZERO.floatValue());
        assetDefinition.setTaxPercentage(BigDecimal.ZERO.floatValue());
        assetDefinition.setSubCategoryDefinition(null);
    }

    private void populateAssetData(AssetDefinition assetDefinition) throws SumoException {
        assetDefinition.setCreationDate(SCMUtil.getCurrentTimestamp());
        assetDefinition.setInventoryDate(SCMUtil.getCurrentTimestamp());
        Integer skuId = assetDefinition.getSkuId();
        SkuDefinitionData skuDefinitionData = scmAssetManagementDao.find(SkuDefinitionData.class, skuId);
        ProductDefinitionData productDefinitionData = skuDefinitionData.getLinkedProduct();
        if (productDefinitionData.getProfileDefinitionData() == null) {
            throw new SumoException("Asset creation failure",
                    "Product " + productDefinitionData.getProductName() + " does not have profile attached to it");
        }
        if (SCMUtil.getStatus(productDefinitionData.getProfileDefinitionData().getUniqueNumberAvailable())) {
            assetDefinition.setUniqueFieldName(productDefinitionData.getProfileDefinitionData().getUniqueFieldName());
        }
        assetDefinition.setProductId(productDefinitionData.getProductId());
        assetDefinition.setProfileId(productDefinitionData.getProfileDefinitionData().getProfileId());
        Long assetTagValueInLong = atomicLongForAssetTagValue.addAndGet(1L);
        assetDefinition.setTagValue(Long.toString(assetTagValueInLong, 36));
        Float grossBlock = (assetDefinition.getPrice() + assetDefinition.getTax()) * assetDefinition.getQuantity();
        assetDefinition.setGrossBlock(grossBlock);
        Float procurementCost = grossBlock * (props.getMaxApplicableDepreciationPercentage());
        assetDefinition.setProcurementCost(procurementCost);
        Float fixedValue = grossBlock - procurementCost;
        assetDefinition.setFixedValue(fixedValue);
    }

    private void addAssociatedProfileAttributeMapping(AssetDefinition assetDefinition) {
        List<ProfileAttributeMapping> profileAttributeMappingList = scmCache.getProfileAttributeMappings()
                .get(assetDefinition.getProfileId());
        List<ProfileAttributeMapping> profileAttributeMappingListForAsset = new ArrayList<>();
        if (profileAttributeMappingList != null) {
            for (ProfileAttributeMapping pam : profileAttributeMappingList) {
                if (pam.isDefinedAtAsset() || pam.isOverridableAtSKU()) {
                    profileAttributeMappingListForAsset.add(pam);
                }
            }
            assetDefinition.setProfileAttributeMappingList(profileAttributeMappingListForAsset);
        } else {
            assetDefinition.setProfileAttributeMappingList(null);
        }

    }

    @Override
    public AssetDefinition convertAssetDefinitionDataToAssetDefinition(AssetDefinitionData assetDefinitionData,
                                                                        boolean setAttributes) {
        IdCodeName createdBy = SCMUtil.generateIdCodeName(assetDefinitionData.getCreatedBy(), "",
                masterDataCache.getEmployees().get(assetDefinitionData.getCreatedBy()));
        IdCodeName lastTagPrintedBy = assetDefinitionData.getLastTagPrintedBy() != null
                ? SCMUtil.generateIdCodeName(assetDefinitionData.getLastTagPrintedBy(), "",
                masterDataCache.getEmployees().get(assetDefinitionData.getLastTagPrintedBy()))
                : null;
        IdCodeName lastTransferBy = assetDefinitionData.getLastTransferedBy() != null
                ? SCMUtil.generateIdCodeName(assetDefinitionData.getLastTransferedBy(), "",
                masterDataCache.getEmployees().get(assetDefinitionData.getLastTransferedBy()))
                : null;
        BigDecimal currentValue = AssetHelper.getCurrentValueOfAsset(assetDefinitionData);
        ProductDefinitionData productDefinitionData = scmAssetManagementDao.find(ProductDefinitionData.class, assetDefinitionData.getProduct().getProductId());
        SubCategoryDefinitionData subCategoryDefinitionData = scmProductManagementDao.find(SubCategoryDefinitionData.class, productDefinitionData.getSubCategoryDefinition().getId());
//		SubCategoryDefinitionData subCategoryDefinitionData = assetDefinitionData.getProduct().getSubCategoryDefinition();
//        ProductDefinitionData productDefinitionData = scmAssetManagementDao.find(ProductDefinitionData.class, assetDefinitionData.getProductId());
//        IdCodeName subCategory = SCMUtil.generateIdCodeName(productDefinitionData.getSubCategoryDefinition().getId(),
//                productDefinitionData.getSubCategoryDefinition().getCode(),
//                productDefinitionData.getSubCategoryDefinition().getName());
        AssetDefinition assetDefinition = convert(assetDefinitionData, createdBy, lastTagPrintedBy,
                lastTransferBy, currentValue, null);
        assetDefinition.setSubCategoryDefinition(new IdCodeName(subCategoryDefinitionData.getId(), "", subCategoryDefinitionData.getName()));
        if (subCategoryDefinitionData.getLifeTimeCategoryMonths() == null) {
            assetDefinition.setLifeTimeCategoryMonths(0);
        } else {
            assetDefinition.setLifeTimeCategoryMonths(subCategoryDefinitionData.getLifeTimeCategoryMonths());
        }
        if (setAttributes) {
            List<EntityAttributeValueMapping> entityAttributeValueMappings = scmProfileManagementService
                    .getEntityAttributeMappings(assetDefinition.getAssetId(), EntityType.ASSET.value());
            assetDefinition.setEntityAttributeValueMappings(entityAttributeValueMappings);
            List<ProfileAttributeMapping> profileAttributeMappingList = scmProfileManagementService
                    .getAllAttributeMappingsForProfile(assetDefinition.getProfileId(), StatusType.ACTIVE.value());
            List<ProfileAttributeMapping> profileAttributeMappings = new ArrayList<>();
            for (ProfileAttributeMapping mapping : profileAttributeMappingList) {
                if (mapping.isOverridableAtSKU() || mapping.isDefinedAtAsset()) {
                    profileAttributeMappings.add(mapping);
                }
            }
            assetDefinition.setProfileAttributeMappingList(profileAttributeMappings);
        }

        return assetDefinition;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public AssetDefinition addNewAsset(AssetDefinition ad) throws SumoException {
        populateAssetData(ad);
        ProductDefinitionData product = scmAssetManagementDao.find(ProductDefinitionData.class, ad.getProductId());
        AssetDefinitionData assetDefinitionData = convert(product, ad);
        assetDefinitionData = scmAssetManagementDao.add(assetDefinitionData, true);
        if (assetDefinitionData != null) {
            AssetDefinition assetDefinition = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, true);
            addAssociatedProfileAttributeMapping(assetDefinition);
            return assetDefinition;
        } else {
            throw new SumoException("Asset creation failure", "Error creating asset. Please contact support.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public AssetDefinition createBackDatedAsset(AssetDefinition assetDefinition) throws SumoException {
        populateBackDatedAsset(assetDefinition);
        ProductDefinitionData product = scmAssetManagementDao.find(ProductDefinitionData.class,
                assetDefinition.getProductId());
        assetDefinition.setProfileId(product.getProfileDefinitionData().getProfileId());
        AssetDefinitionData assetDefinitionData = convert(product, assetDefinition);
        assetDefinitionData = scmAssetManagementDao.add(assetDefinitionData, true);
        if (assetDefinitionData != null) {
            AssetDefinition assetDefinition1 = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, true);
            if (assetDefinition.getEntityAttributeValueMappings() != null) {
                List<EntityAttributeValueMapping> list = new ArrayList<>();
                List<EntityAttributeValueMapping> standAloneList = new ArrayList<>();
                for (EntityAttributeValueMapping mapping : assetDefinition.getEntityAttributeValueMappings()) {
                    if (mapping.getAttributeValueId() != null) {
                        mapping.setEntityId(assetDefinitionData.getAssetId());
                        list.add(mapping);
                    } else if (mapping.getAttributeValue() != null) {
                        mapping.setEntityId(assetDefinitionData.getAssetId());
                        standAloneList.add(mapping);
                    }
                }
                List<EntityAttributeValueMapping> entityAttributeValueMappings = scmProfileManagementService
                        .addGenericAttributeValueMappings(list);
                assetDefinition1.setEntityAttributeValueMappings(entityAttributeValueMappings);
                assetDefinition1.getEntityAttributeValueMappings().addAll(
                        scmProfileManagementService.addGenericAttributeValueMappingsAndAttribute(standAloneList));
            }
            return assetDefinition1;
        } else {
            throw new SumoException("Asset creation failure",
                    "Error creating back dated asset. Please contact support.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public AssetDefinition updateAsset(AssetDefinition assetDefinition) throws SumoException {
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                assetDefinition.getAssetId());
        if (assetDefinitionData != null) {
            AssetDefinitionData ad = convert(assetDefinitionData.getProduct(), assetDefinition);
            ad = scmAssetManagementDao.update(ad, true);
            if (ad != null) {
                return convertAssetDefinitionDataToAssetDefinition(ad, false);
            } else {
                throw new SumoException("Error in updating Asset", "Error updating asset. Please contact support.");
            }
        } else {
            throw new SumoException("Asset could not be found", "Error updating asset. Please contact support.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public AssetDefinition completeAssetGeneration(AssetDefinition assetDefinition) throws SumoException {
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                assetDefinition.getAssetId());
        if (assetDefinitionData != null) {
            /*
             * complete asset registration
             */
            completeAssetCreation(assetDefinition);
            SkuDefinitionData skuDefinitionData = scmAssetManagementDao.find(SkuDefinitionData.class,
                    assetDefinition.getSkuId());
            assetDefinition.setAssetImageUrl(skuDefinitionData.getSkuImage());
            AssetDefinitionData ad = convert(assetDefinitionData.getProduct(), assetDefinition);
            ad = scmAssetManagementDao.update(ad, true);
            if (ad != null) {
                AssetDefinition assetDefinition1 = convertAssetDefinitionDataToAssetDefinition(ad, true);
                if (assetDefinition.getEntityAttributeValueMappings() != null) {
                    List<EntityAttributeValueMapping> list = new ArrayList<>();
                    List<EntityAttributeValueMapping> standAloneList = new ArrayList<>();
                    for (EntityAttributeValueMapping mapping : assetDefinition.getEntityAttributeValueMappings()) {
                        if (mapping.getAttributeValueId() != null) {
                            list.add(mapping);
                        } else if (mapping.getAttributeValue() != null) {
                            mapping.setEntityId(assetDefinitionData.getAssetId());
                            standAloneList.add(mapping);
                        }
                    }
                    List<EntityAttributeValueMapping> entityAttributeValueMappings = scmProfileManagementService
                            .addGenericAttributeValueMappings(list);
                    assetDefinition1.setEntityAttributeValueMappings(entityAttributeValueMappings);
                    assetDefinition1.getEntityAttributeValueMappings().addAll(
                            scmProfileManagementService.addGenericAttributeValueMappingsAndAttribute(standAloneList));
                }
                return assetDefinition1;
            } else {
                throw new SumoException("Error in updating Asset",
                        "Error completing  asset generation. Please contact support.");
            }
        } else {
            throw new SumoException("Asset could not be found",
                    "Error completing asset generation. Please contact support.");
        }
    }

    private void completeAssetCreation(AssetDefinition assetDefinition) {
        assetDefinition.setTagPrintCount(1);
        assetDefinition.setAssetStatus(AssetStatusType.CREATED);
        assetDefinition.setLastTagPrintDate(SCMUtil.getCurrentTimestamp());
        int lifeTimeInDays = 0;
        if (assetDefinition.getLifeTimeType().equals(LifeTimeType.YEARS)) {
            lifeTimeInDays = 365 * assetDefinition.getLifeTimeValue().intValue();
        } else if (assetDefinition.getLifeTimeType().equals(LifeTimeType.MONTHS)) {
            lifeTimeInDays = 30 * assetDefinition.getLifeTimeValue().intValue();
        } else if (assetDefinition.getLifeTimeType().equals(LifeTimeType.DAYS)) {
            lifeTimeInDays = assetDefinition.getLifeTimeValue().intValue();
        }
        assetDefinition.setLifeTimeInDays(lifeTimeInDays);
        assetDefinition.setDepreciationStrategy(AppConstants.ASSET_DEPRECIATION_STRATEGY);
        double depreciationRate = assetDefinition.getProcurementCost() / assetDefinition.getLifeTimeInDays();
        DecimalFormat df = new DecimalFormat("0.000000");
        df.setRoundingMode(RoundingMode.FLOOR);
        Float roundedOfDepreciationRate = Float.valueOf(df.format(depreciationRate));
        assetDefinition.setDailyDepreciationRate(roundedOfDepreciationRate);
        Float depreciationResidue = assetDefinition.getProcurementCost()
                - assetDefinition.getLifeTimeInDays() * assetDefinition.getDailyDepreciationRate();
        assetDefinition.setDepreciationResidue(depreciationResidue);
        Float depreciationRatePA = 100 / (assetDefinition.getLifeTimeInDays().floatValue() / 365);
        assetDefinition.setRealizedDepreciation(0.00f);
        assetDefinition.setDepreciationRatePa(depreciationRatePA);
    }

    private void completeAssetCreation(AssetDefinitionData assetDefinitionData) {
        assetDefinitionData.setTagPrintCount(1);
        assetDefinitionData.setAssetStatus(AssetStatusType.CREATED.name());
        assetDefinitionData.setLastTagPrintDate(SCMUtil.getCurrentTimestamp());
        int lifeTimeInDays = 0;
        if (assetDefinitionData.getLifeTimeType().equals(LifeTimeType.YEARS.value())) {
            lifeTimeInDays = 365 * assetDefinitionData.getLifeTimeValue().intValue();
        } else if (assetDefinitionData.getLifeTimeType().equals(LifeTimeType.MONTHS.value())) {
            lifeTimeInDays = 30 * assetDefinitionData.getLifeTimeValue().intValue();
        } else if (assetDefinitionData.getLifeTimeType().equals(LifeTimeType.DAYS.value())) {
            lifeTimeInDays = assetDefinitionData.getLifeTimeValue().intValue();
        }
        assetDefinitionData.setLifeTimeInDays(lifeTimeInDays);
        assetDefinitionData.setDepreciationStrategy(AppConstants.ASSET_DEPRECIATION_STRATEGY);
        double depreciationRate = SCMUtil.divide(assetDefinitionData.getProcurementCost(), BigDecimal.valueOf(assetDefinitionData.getLifeTimeInDays())).doubleValue() ;
        DecimalFormat df = new DecimalFormat("0.000000");
        df.setRoundingMode(RoundingMode.FLOOR);
        Float roundedOfDepreciationRate = Float.valueOf(df.format(depreciationRate));
        assetDefinitionData.setDailyDepreciationRate(BigDecimal.valueOf(roundedOfDepreciationRate));
        Float depreciationResidue = SCMUtil.subtract( assetDefinitionData.getProcurementCost() ,
                 SCMUtil.multiply(BigDecimal.valueOf(assetDefinitionData.getLifeTimeInDays()), assetDefinitionData.getDailyDepreciationRate())).floatValue();
        assetDefinitionData.setDepreciationResidue(BigDecimal.valueOf(depreciationResidue));
        Float depreciationRatePA = 100 / (assetDefinitionData.getLifeTimeInDays().floatValue() / 365);
        assetDefinitionData.setRealizedDepreciation(BigDecimal.valueOf(0.00f));
        assetDefinitionData.setDepreciationRatePa(BigDecimal.valueOf(depreciationRatePA));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public AssetDefinition viewAsset(int assetId) {
        LOG.info("Getting asset by assetId: " + assetId);
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class, assetId);
        if (assetDefinitionData != null) {
            return convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, false);
        } else {
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public AssetDefinitionSlimObject viewAssetByTagValue(String tagValue) {
        LOG.info("Getting asset having tag value: " + tagValue);
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.getAssetByTagValue(tagValue);
        return SCMDataConverter
                .convertToSlimObject(convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, true), scmCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AssetDefinition> viewAllAssets() {
        List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.findAll(AssetDefinitionData.class);
        return getConvertedAssetList(assetDefinitionDataList, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AssetDefinition> viewAllAssetsFromUnit(int unitId) {
        return getConvertedAssetList(scmAssetManagementDao.getAllAssetFromUnit(unitId), false);
    }

    private List<AssetDefinition> getConvertedAssetList(List<AssetDefinitionData> assetDefinitionDataList,
                                                        boolean setAttribute) {
        List<AssetDefinition> assetDefinitionList = new ArrayList<>();
        if (assetDefinitionDataList != null) {
            assetDefinitionDataList.forEach(assetDefinitionData -> {
                AssetDefinition ad = null;
                try {
                    ad = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, setAttribute);
                } catch (Exception e) {
                    try {
                        String text = "Error converting data to domain model for asset Id "
                                + assetDefinitionData.getAssetId() + " " + JSONSerializer.toJSON(e);

//						SlackNotificationService.getInstance().send(props.getEnvType(), null, SlackNotification.SUPPLY_CHAIN, text);
                        SlackNotificationService.getInstance().sendNotification(props.getEnvType(), null, SlackNotification.SUPPLY_CHAIN, text);
                    } catch (Exception ex) {
                        LOG.error("Error sending slack ", ex);
                    }

                    //LOG.error(text, assetDefinitionData);
                    LOG.error("Error ", e);
                }
                if (ad != null) {
                    assetDefinitionList.add(ad);
                }
            });
            return assetDefinitionList;
        } else {
            return assetDefinitionList;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AssetDefinition> viewAllAssetsFromUnitWithStatus(int unitId, String assetStatus) {
        return getConvertedAssetList(scmAssetManagementDao.getAllAssetFromUnitWithStatus(unitId, assetStatus), false);
    }

    @Override
    public List<AssetDefinition> viewAllTransferableAssetsFromUnit(int unitId, boolean removeAssetWithPriceZero) {
        LOG.info("Getting transferable asset from unit: " + unitId);
        List<AssetDefinition> assetDefinitionList = new ArrayList<>();
        List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.getAllAssetFromUnit(unitId);
        if (assetDefinitionDataList != null) {
            for (AssetDefinitionData assetDefinitionData : assetDefinitionDataList) {
                Boolean notInTransit = Objects.isNull(assetDefinitionData.getIsInTransit()) ||
                        assetDefinitionData.getIsInTransit().equalsIgnoreCase(SCMUtil.NO);
                if (notInTransit &&
                        (assetDefinitionData.getAssetStatus().equals(AssetStatusType.BROKEN.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.CREATED.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_USE.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.READY_FOR_USE.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_RENOVATION.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_REPAIR.value()))) {
                    AssetDefinition ad = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, false);
                    if (removeAssetWithPriceZero) {
                        if (assetDefinitionData.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                            assetDefinitionList.add(ad);
                        }
                    } else {
                        assetDefinitionList.add(ad);
                    }

                }
            }
            return assetDefinitionList;
        } else {
            LOG.info("Could not find any transferable asset at unit: " + unitId);
            return assetDefinitionList;
        }
    }

    @Override
    public List<FixedAssetCompactDefinition> viewAllFixedAssetsFromUnit(int unitId) {
        LOG.info("Getting fixed asset from unit: " + unitId);
        List<FixedAssetCompactDefinition> fixedAssetList = new ArrayList<>();
                fixedAssetList = scmAssetManagementDao.getAllFixedAssetFromUnit(unitId);
        if (fixedAssetList != null) {
            return fixedAssetList;
        } else {
            LOG.info("Could not find any fixed asset at unit: " + unitId);
            return fixedAssetList;
        }
    }

    @Override
    public List<AssetDefinition> viewAllTransferableAssetsFromUnitByProducts(int unitId, List<Integer> productIds, boolean removeAssetWithPriceZero) {
        LOG.info("Getting transferable asset from unit: {}", unitId);
        List<AssetDefinition> assetDefinitionList = new ArrayList<>();
        List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.getAllAssetFromUnitByProductIds(unitId, productIds);
        if (assetDefinitionDataList != null) {
            for (AssetDefinitionData assetDefinitionData : assetDefinitionDataList) {
                Boolean notInTransit = Objects.isNull(assetDefinitionData.getIsInTransit()) ||
                        assetDefinitionData.getIsInTransit().equalsIgnoreCase(SCMUtil.NO);
                if (notInTransit &&
                        (assetDefinitionData.getAssetStatus().equals(AssetStatusType.BROKEN.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.CREATED.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_USE.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.READY_FOR_USE.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_RENOVATION.value())
                                || assetDefinitionData.getAssetStatus().equals(AssetStatusType.IN_REPAIR.value()))
                        ) {
                    AssetDefinition ad = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, false);
                    if (removeAssetWithPriceZero) {
                        if (assetDefinitionData.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                            assetDefinitionList.add(ad);
                        }
                    } else {
                        assetDefinitionList.add(ad);
                    }

                }
            }
            return assetDefinitionList;
        } else {
            LOG.info("Could not find any transferable asset at unit: " + unitId);
            return assetDefinitionList;
        }
    }

    @Override
    public List<AssetDefinition> getAllAssetWithGRItemId(int grItemId) {
        return getConvertedAssetList(scmAssetManagementDao.getAllAssetWithGRItemId(grItemId), true);
    }

    private StockEventDefinition convertToStockEventDefinition(StockEventDefinitionData stockEventDefinitionData) {
        IdCodeName createdBy = SCMUtil.generateIdCodeName(stockEventDefinitionData.getInitiatedBy(), "",
                masterDataCache.getEmployees().get(stockEventDefinitionData.getInitiatedBy()));
        IdCodeName auditedBy = stockEventDefinitionData.getAuditedBy() != null
                ? SCMUtil.generateIdCodeName(stockEventDefinitionData.getAuditedBy(), "",
                masterDataCache.getEmployees().get(stockEventDefinitionData.getAuditedBy()))
                : null;
        return convert(stockEventDefinitionData, createdBy, auditedBy);
    }

    @Override
    public List<AssetDepreciationMappingData> createAssetDepreciationMapping(AssetDefinitionData assetDefinitionData,
                                                                             Date startDate, Date endDate) throws SumoException {
        List<AssetDepreciationMappingData> list = new ArrayList<>();
        logCurrentStatus(assetDefinitionData);
        Date lifeTimeEndDate = assetDefinitionData.getExpectedEndDate();
        BigDecimal totalDepreciation = new BigDecimal(0.00f);
        List<AssetTransferMappingData> assetTransferMappingDataList = scmAssetManagementDao
                .getAssetTransferMapping(assetDefinitionData.getAssetId(), startDate, endDate);
        BigDecimal remainingDepreciation = assetDefinitionData.getProcurementCost()
                .add(assetDefinitionData.getDepreciationResidue())
                .subtract(assetDefinitionData.getRealizedDepreciation());
        boolean isAssetWriteOff = false;
        for (AssetTransferMappingData data : assetTransferMappingDataList) {
            AssetDepreciationMappingData depreciationMappingData = convert(assetDefinitionData, data);
            Date mappingStartDate = data.getStartDate().after(startDate) ? data.getStartDate() : startDate;
            Date mappingEndDate;
            Date calculationMappingEndDate;
            boolean plusOne = false;
            if (data.getEndDate() == null) {
                plusOne = true;
                mappingEndDate = endDate;
                calculationMappingEndDate = endDate;
            } else {
                mappingEndDate = AppUtils.isSameDate(data.getEndDate(), data.getStartDate()) ? data.getEndDate()
                        : AppUtils.getPreviousDate(data.getEndDate());
                calculationMappingEndDate = AppUtils.isSameDate(data.getEndDate(), data.getStartDate())
                        ? data.getEndDate()
                        : data.getEndDate();
                if (AppUtils.isSameDate(startDate, data.getEndDate())) {
                    mappingEndDate = data.getEndDate();
                    calculationMappingEndDate = data.getEndDate();
                }
            }

            depreciationMappingData.setStartDate(AppUtils.onlyDate(mappingStartDate));
            depreciationMappingData.setEndDate(AppUtils.onlyDate(mappingEndDate));
            BigDecimal depreciation = new BigDecimal(0.00f);
            if (lifeTimeEndDate.before(startDate)) {
                // asset has been depreciated by now completely
                depreciationMappingData.setDepreciationAmount(depreciation);
            } else if (AppUtils.getStatus(data.getIsWriteOff())) {
                isAssetWriteOff = true;

                if (data.getAssetStatus().equals(AssetStatusType.SCRAPPED.value())) {
                    // book write off amount at at in use , if last in use is not present then book
                    // on current
                    AssetTransferMappingData lastInUse = scmAssetManagementDao
                            .getLastEntryWithStatus(assetDefinitionData.getAssetId(), AssetStatusType.IN_USE.value());
                    if (lastInUse != null) {
                        depreciationMappingData.setOwnerId(lastInUse.getOwnerId());
                        depreciationMappingData.setUnitId(lastInUse.getUnitId());
                    }
//                    else {
//                         business as usual book on last unit
//                    }
                    depreciation = assetDefinitionData.getWriteOffAmount();
                } else if (data.getAssetStatus().equals(AssetStatusType.LOST_IDENTIFIED.value())) {
                    depreciation = assetDefinitionData.getWriteOffAmount();
                } else if (data.getAssetStatus().equals(AssetStatusType.DISCARDED.value())) {
                    depreciation = assetDefinitionData.getWriteOffAmount();
                }
                depreciationMappingData.setDepreciationAmount(depreciation);
            } else {
                int daysCount = AppUtils.getAbsDaysDiff(calculationMappingEndDate, mappingStartDate);
                if (plusOne) {
                    daysCount = daysCount + 1;
                }
                if (daysCount == 0) {
                    continue;
                }
                LOG.info("calculationMappingEndDate " + calculationMappingEndDate + " mappingStartDate "
                        + mappingStartDate + " " + "daysCount " + daysCount + " startDate " + startDate);
                depreciation = assetDefinitionData.getDailyDepreciationRate().multiply(new BigDecimal(daysCount));
                if (depreciation.compareTo(remainingDepreciation) > 0) {
                    // asset has been depreciated completely
                    depreciation = remainingDepreciation;
                    remainingDepreciation = new BigDecimal("0.00");
                } else {
                    remainingDepreciation = remainingDepreciation.subtract(depreciation);
                }
                depreciationMappingData.setDepreciationAmount(depreciation);
            }

            depreciationMappingData = scmAssetManagementDao.add(depreciationMappingData, true);
            list.add(depreciationMappingData);
            totalDepreciation = totalDepreciation.add(depreciation);
        }

        BigDecimal realizedDepreciation = assetDefinitionData.getRealizedDepreciation().add(totalDepreciation);
        assetDefinitionData.setRealizedDepreciation(realizedDepreciation);
        assetDefinitionData.setRealizedDepreciationDate(endDate);
        assetDefinitionData = scmAssetManagementDao.update(assetDefinitionData, true);

        if (list.size() > 0) {
            AssetDepreciationMappingData lastEntry = list.get(list.size() - 1);
            AssetDepreciationSummaryData assetDepreciationSummaryData = convert(assetDefinitionData,
                    lastEntry, isAssetWriteOff);
            scmAssetManagementDao.add(assetDepreciationSummaryData, true);
        }

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public List<AssetDepreciationMappingData> createAssetDepreciationBetween(Date startDate, Date endDate)
            throws SumoException {
        List<AssetDefinitionData> assetDefinitionDatas = scmAssetManagementDao.getAssetForDepreciation(startDate);
        List<AssetDepreciationMappingData> assetDepreciationMappingData = new ArrayList<>();
        if (assetDefinitionDatas != null) {
            for (AssetDefinitionData data : assetDefinitionDatas) {
                assetDepreciationMappingData.addAll(createAssetDepreciationMapping(data, startDate, endDate));
            }
        }
        return assetDepreciationMappingData;
    }

    @Override
    public BigDecimal getCurrentValueOfAsset(int assetId) {
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class, assetId);
        return AssetHelper.getCurrentValueOfAsset(assetDefinitionData);
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public void depreciationCalculationOnLastDayOfMonth() throws SumoException {
        final Calendar c = Calendar.getInstance();
        if (c.get(Calendar.DATE) == c.getActualMaximum(Calendar.DATE)) {
            int month = c.get(Calendar.MONTH) + 1;
            int year = c.get(Calendar.YEAR);
            Date firstDateOfMonth = AppUtils.getStartOfMonth2(year, month);
            Date lastDateOfMonth = AppUtils.getEndOfMonth(year, month);
            LOG.info("--------------------------------------------------\n");
            LOG.info("---  Calculating Asset Depreciation ---- from ---- " + firstDateOfMonth + " ---- to ---- "
                    + lastDateOfMonth + " \n");
            createAssetDepreciationBetween(firstDateOfMonth, lastDateOfMonth);
            LOG.info("--------------------------------------------------\n");
        }
    }

    @Override
    public List<String> getStockTakeList() {
        return Stream.of(StockEventType.values()).map(StockEventType::name).collect(Collectors.toList());
    }

    @Override
    public Map<String, Boolean> getStockTakeSubMap() {
        Map<String, Boolean> stockTakeSubTypeMap = new HashMap<>();
        Stream.of(StockTakeSubType.values()).forEach(type -> {
            stockTakeSubTypeMap.put(type.value(), type.isAuditorCompulsory());
        });
        return stockTakeSubTypeMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AssetRecoveryDefinition> getAllAssetsInRecoveryFromUnit(int unitId, String recoveryStatus) {
        List<AssetRecoveryDefinition> list = new ArrayList<>();
        LOG.info(
                "Getting assets for recovery Associated with unit " + unitId + " and recoveryStatus " + recoveryStatus);
        List<AssetRecoveryDefinitionData> assetRecoveryDefinitionDataList = scmAssetManagementDao
                .getAllAssetInRecoveryFromUnit(unitId, recoveryStatus);
        if (assetRecoveryDefinitionDataList != null) {
            for (AssetRecoveryDefinitionData data : assetRecoveryDefinitionDataList) {
                list.add(convertToRecoveryDefinition(data));
            }
        }
        return list;
    }

    public AssetRecoveryDefinition convertToRecoveryDefinition(AssetRecoveryDefinitionData data) {
        IdCodeName recoveredBy = data.getRecoveredBy() != null ? SCMUtil.generateIdCodeName(data.getRecoveredBy(), "",
                masterDataCache.getEmployees().get(data.getRecoveredBy())) : null;
        IdCodeName recoveredFrom = data.getRecoveryEmpId() != null ? SCMUtil.generateIdCodeName(data.getRecoveryEmpId(),
                "", masterDataCache.getEmployees().get(data.getRecoveryEmpId())) : null;
        IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
                masterDataCache.getEmployees().get(data.getCreatedBy()));
        IdCodeName approvedBy = data.getApprovedBy() != null ? SCMUtil.generateIdCodeName(data.getApprovedBy(), "",
                masterDataCache.getEmployees().get(data.getApprovedBy())) : null;
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                data.getAssetId());
        AssetDefinition assetDefinition = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, false);
        Unit unit = masterDataCache.getUnit(data.getUnitId());
        if (Objects.nonNull(data.getEventId())) {
            StockEventDefinition stockEventDefinition = viewEvent(data.getEventId());
            return convert(data, createdBy, recoveredBy, recoveredFrom, assetDefinition, approvedBy, unit,
                    stockEventDefinition.getAuditedBy());
        } else {
            return convert(data, createdBy, recoveredBy, recoveredFrom, assetDefinition, approvedBy, unit,
                    recoveredFrom != null ? recoveredFrom : SCMUtil.generateIdCodeName(125200,
                            "", masterDataCache.getEmployees().get(125200)));
        }
    }

    @Override
    public AssetRecovery convertToRecoveryDefinition(AssetRecoveryData data) throws SumoException {
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                data.getAssetId());
        AssetDefinition assetDefinition = convertAssetDefinitionDataToAssetDefinition(assetDefinitionData, false);
        List<StockEventDefinitionData> event = scmAssetManagementDao.getParentOrChildEvent(data.getEventId(),null,null);
        StockEventDefinition stockEventDefinition = convertToStockEventDefinition(event.get(0));
        List<AssetRecoveryDetail> list = new ArrayList<>();
        IdCodeName unit = SCMUtil.generateIdCodeName(stockEventDefinition.getUnitId(), "",
                masterDataCache.getUnitBasicDetail(stockEventDefinition.getUnitId()).getName());
        List<ApprovalDetailData> approvals = stockManagementDao.getAssetApprovals(null,null,stockEventDefinition.getEventId(),data.getAssetId(), ApprovalStatus.APPROVED.value());
        ApprovalDetail approval = new ApprovalDetail();
        if(Objects.nonNull(approvals) && !approvals.isEmpty()){
            if(approvals.size() > 1){
                approvals = approvals.stream().filter(e-> Objects.equals(e.getType(), "LOST_ASSET")).collect(Collectors.toList());
            }
            approval = SCMDataConverter.convertToApprovalDetail(approvals.get(0), scmCache, masterDataCache);
        }
        try{
            List<AssetRecoveryDetailData> recoveryDetailData = scmAssetManagementDao.getAssetRecoveryDetail(data.getRecoveryId(),null);
            for(AssetRecoveryDetailData recoveryData : recoveryDetailData) {
                list.add(convertToAssetRecoveryDetail(recoveryData));
            }
        }catch(Exception e){
            LOG.info("Failed to fetch recovery Detail for event (" + data.getEventId() + ") for unit (" + data.getUnitId() + ") for asset (" + data.getAssetId() + ")");
        }
        return convert(data, assetDefinition,stockEventDefinition,list,unit,approval,masterDataCache);
    }

    @Override
    public AssetRecoveryDetail convertToAssetRecoveryDetail(AssetRecoveryDetailData data) {

        IdCodeName recoveredFrom = null;
        if(Objects.equals(data.getRecoveryType(),RecoveryType.EMPLOYEE.value())){
            recoveredFrom = data.getRecoveredFrom() != null ? SCMUtil.generateIdCodeName(Integer.parseInt(data.getRecoveredFrom()), "",
                    masterDataCache.getEmployees().get(Integer.parseInt(data.getRecoveredFrom()))) : null;
        }else if(Objects.equals(data.getRecoveryType(),RecoveryType.INSURANCE.value())){
            recoveredFrom = data.getRecoveredFrom() != null ? SCMUtil.generateIdCodeName(null, "",
                    data.getRecoveredFrom()) : null;
        }
        return convert(data, recoveredFrom);
    }

    @Override
    public AssetRecoveryDetailData convertToAssetRecoveryDetailData(AssetRecoveryDetail data) {
        return convert(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean recoverAssetList(List<AssetRecoveryDefinition> assetRecoveryDefinitions) throws SumoException {
        for (AssetRecoveryDefinition assetRecoveryDefinition : assetRecoveryDefinitions) {
            AssetRecoveryDefinitionData data = scmAssetManagementDao.find(AssetRecoveryDefinitionData.class,
                    assetRecoveryDefinition.getAssetRecoveryId());
            data.setRecoveryDate(AppUtils.getCurrentTimestamp());
            data.setRecoveryStatus(AssetRecoveryStatusType.RECOVERED.value());
            data.setRecoveredAmount(data.getRecoveryAmount());
            data.setRecoveredBy(assetRecoveryDefinition.getRecoveredBy().getId());
            data.setSalaryDeductionDate(assetRecoveryDefinition.getSalaryDeductionDate());
            AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                    assetRecoveryDefinition.getAssetId());
            AssetDefinition assetDefinition = updateAssetStatus(assetDefinitionData, AssetStatusType.LOST_ADJUSTED);
            if (assetDefinition != null) {
                data.setAssetStatus(assetDefinition.getAssetStatus().value());
                scmAssetManagementDao.update(data, true);
            } else {
                throw new SumoException("Error recovering asset", "Error recovering asset. Please contact support.");
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public boolean initiateAssetRecovery(List<AssetRecoveryDefinition> assetRecoveryDefinitions) throws SumoException {

        List<AssetRecoveryDefinition> list = new ArrayList<>();
        for (AssetRecoveryDefinition assetRecoveryDefinition : assetRecoveryDefinitions) {
            AssetRecoveryDefinitionData data = scmAssetManagementDao.find(AssetRecoveryDefinitionData.class,
                    assetRecoveryDefinition.getAssetRecoveryId());
            AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                    assetRecoveryDefinition.getAssetId());
            if (assetRecoveryDefinition.getFound()) {
                LOG.info("Asset Found , hence setting Recovery Status to Found of asset id: "
                        + assetRecoveryDefinition.getAssetId() + " " + " and assetRecoveryDefinition Id: "
                        + assetRecoveryDefinition.getAssetRecoveryId());
                AssetDefinitionDataLog previousLog = scmAssetManagementDao
                        .getLatestAssetDefinitionDataLog(assetRecoveryDefinition.getAssetId());
                AssetStatusType previousStatus = AssetStatusType.valueOf(previousLog.getAssetStatus());
                AssetDefinition assetDefinition = updateAssetStatus(assetDefinitionData, previousStatus);
                if (assetDefinition != null) {
                    data.setApprovalDate(AppUtils.getCurrentTimestamp());
                    data.setApprovedBy(assetRecoveryDefinition.getApprovedBy().getId());
                    data.setAssetStatus(assetDefinition.getAssetStatus().value());
                    data.setRecoveryStatus(assetDefinition.getRecoveryStatus());
                    scmAssetManagementDao.update(data, true);
                } else {
                    throw new SumoException("Error rejecting asset recovery",
                            "Error rejecting asset recovering. Please contact support.");
                }
            } else {
                LOG.info("Asset Lost , hence setting Recovery Status to LOST_IDENTIFIED of asset id: "
                        + assetRecoveryDefinition.getAssetId() + " " + " and assetRecoveryDefinition Id: "
                        + assetRecoveryDefinition.getAssetRecoveryId());
                AssetDefinition assetDefinition = updateAssetStatus(assetDefinitionData,
                        AssetStatusType.LOST_IDENTIFIED);
                if (assetDefinition != null) {
                    data.setApprovalDate(AppUtils.getCurrentTimestamp());
                    data.setApprovedBy(assetRecoveryDefinition.getApprovedBy().getId());
                    data.setAssetStatus(assetDefinition.getAssetStatus().value());
                    data.setRecoveryStatus(assetDefinition.getRecoveryStatus());
                    data.setRecoveryAmount(new BigDecimal(assetDefinition.getRecoveryAmount()));
                    data.setRecoveryType(assetDefinition.getRecoveryType());
                    data = scmAssetManagementDao.update(data, true);
                    list.add(convertToRecoveryDefinition(data));
                } else {
                    throw new SumoException("Error initiating asset recovery",
                            "Error initiating asset recovering. Please contact support.");
                }
            }

        }

        if (list.size() > 0) {
            int unitId = list.get(0).getUnitId();
            List<String> emails = getEmailHeirarchy(unitId, true);
            scmNotificationService.sendAssetLostNotification(list, emails);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<String> assetStatusTypeList() {
        return Arrays.asList(AssetStatusType.values()).stream().map(i -> i.value()).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getDepreciationOnUnitByDate(int ownerId, Date businessDate) {
        DateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        LOG.info("Calculating Daily depreciation for ownerId : " + ownerId + " Date : " + sdf.format(businessDate));
        List<String> depreciableStatusList = AssetHelper.depreciableStatusList.stream().map(s -> s.value())
                .collect(Collectors.toList());
        List<AssetTransferMappingData> assetTransferMappingDataList = scmAssetManagementDao
                .getAssetTransferMappingWithOwnerAndStatus(ownerId, businessDate, depreciableStatusList);
        BigDecimal totalDailyDepreciation = BigDecimal.ZERO;
        if (assetTransferMappingDataList != null) {
            for (AssetTransferMappingData data : assetTransferMappingDataList) {
                /*
                 * If expected End Date is after business date then simply return daily
                 * depreciation else asset has been depreciated completely
                 */
                AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                        data.getAssetId());
                BigDecimal depreciation = BigDecimal.ZERO;
                // && AppUtils.getAbsDaysDiff(businessDate, data.getStartDate()) > 0
                if (assetDefinitionData.getExpectedEndDate().after(businessDate)) {
                    depreciation = new BigDecimal(assetDefinitionData.getDailyDepreciationRate().floatValue());
                }
                totalDailyDepreciation = totalDailyDepreciation.add(depreciation);
                LOG.info("********** assetId " + data.getAssetId() + " assetStatus " + data.getAssetStatus()
                        + " depreciation " + depreciation);
            }
        }
        LOG.info(" Total Claculate Depreciation on unitId " + ownerId + " Total Depreciation = "
                + totalDailyDepreciation);
        return totalDailyDepreciation;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getLostAssetAmountOnUnitByDate(int unitId, Date businessDate) {
        BigDecimal totalDepreciation = getAssetTransferMappingByOwnerAndStartAndEndDateAndStatus(unitId, businessDate,
                businessDate, AssetStatusType.LOST_IDENTIFIED.value());
        LOG.info(" \n Lost Asset Daily at unit " + unitId + " businessDate " + businessDate + " Amount "
                + totalDepreciation);
        return totalDepreciation;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getDamageAssetAmountOnUnitByDate(int unitId, Date businessDate) {
        // TODO HANDLE SCRAPPED SEPERATELY - Done
        BigDecimal totalScrapped = getAssetScrappedMappingAmountByUnitAndDate(unitId, businessDate);

        LOG.info(" \n SCRAPPED Asset Daily at unit " + unitId + " businessDate " + businessDate + " Amount "
                + totalScrapped);
        BigDecimal totalDiscarded = getAssetTransferMappingByOwnerAndStartAndEndDateAndStatus(unitId, businessDate,
                businessDate, AssetStatusType.DISCARDED.value());
        LOG.info(" \n DISCARDED Asset Daily at unit " + unitId + " businessDate " + businessDate + " Amount "
                + totalDiscarded);
        return totalScrapped.add(totalDiscarded);
    }

    private BigDecimal getAssetScrappedMappingAmountByUnitAndDate(int unitId, Date businessDate) {
        BigDecimal amount = BigDecimal.ZERO;
        List<AssetScrappedMappingData> assetScrappedMappingData = scmAssetManagementDao
                .getAssetScrappedMappingByUnitAndBusinessDate(unitId, businessDate);
        if (assetScrappedMappingData != null) {
            for (AssetScrappedMappingData data : assetScrappedMappingData) {
                amount = amount.add(data.getScrappedAmount());
            }
        }
        return amount;
    }

    private BigDecimal getAssetScrappedMappingAmountByUnitAndDateRange(int unitId, Date startDate, Date endDate) {
        BigDecimal amount = BigDecimal.ZERO;
        List<AssetScrappedMappingData> assetScrappedMappingData = scmAssetManagementDao
                .getAssetScrappedMappingByUnitAndBusinessDateRange(unitId, startDate, endDate);
        if (assetScrappedMappingData != null) {
            for (AssetScrappedMappingData data : assetScrappedMappingData) {
                amount = amount.add(data.getScrappedAmount());
            }
        }
        return amount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getDepreciationOnUnitByMonth(int ownerId, Date monthStartDate) {
        List<AssetDepreciationMappingData> assetDepreciationMappingData = scmAssetManagementDao
                .getDepreciationRegisteredFromToDateOnUnit(ownerId, AppUtils.getFirstDayOfMonth(monthStartDate),
                        AppUtils.getLastDayOfMonth(monthStartDate));
        BigDecimal totalDepreciation = BigDecimal.ZERO;
        if (assetDepreciationMappingData != null) {
            for (AssetDepreciationMappingData data : assetDepreciationMappingData) {
                totalDepreciation = totalDepreciation.add(data.getDepreciationAmount());
            }
        }
        return totalDepreciation;
    }

    @Override
    public Map<Integer, BigDecimal> getDepreciationOnUnitForCategories(int ownerId, Date monthStartDate) {
        List<AssetDepreciationMappingData> assetDepreciationMappingData = scmAssetManagementDao
                .getDepreciationRegisteredFromToDateOnUnit(ownerId, AppUtils.getFirstDayOfMonth(monthStartDate),
                        AppUtils.getLastDayOfMonth(monthStartDate));
        Map<Integer, BigDecimal> depreciationOnUnit = new HashMap<>();
        if (assetDepreciationMappingData != null) {
            for (AssetDepreciationMappingData data : assetDepreciationMappingData) {
                Integer categoryId ;
				if (Objects.nonNull(data.getSubCategoryId())) {
					categoryId = data.getSubCategoryId();
				}
				else {
					categoryId = data.getAssetDefinition().getProduct().getSubCategoryDefinition().getId();
				}
                if (depreciationOnUnit.containsKey(categoryId)) {
                    depreciationOnUnit.put(categoryId,
                            depreciationOnUnit.get(categoryId).add(data.getDepreciationAmount()));
                    continue;
                } else {
                    depreciationOnUnit.put(categoryId, data.getDepreciationAmount());
                    continue;
                }
            }
        }
        return depreciationOnUnit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getLostAssetAmountOnUnitByMonth(int unitId, Date monthStartDate) {
        BigDecimal totalDepreciation = getAssetTransferMappingByOwnerAndStartAndEndDateAndStatus(unitId,
                AppUtils.getFirstDayOfMonth(monthStartDate), AppUtils.getLastDayOfMonth(monthStartDate),
                AssetStatusType.LOST_IDENTIFIED.value());
        LOG.info(" \n Lost Asset Monthly at unit " + unitId + " monthStartDate "
                + AppUtils.getFirstDayOfMonth(monthStartDate) + " Amount " + totalDepreciation);
        return totalDepreciation;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getDamageAssetAmountOnUnitByMonth(int unitId, Date monthStartDate) {

        BigDecimal totalScrapped = getAssetScrappedMappingAmountByUnitAndDateRange(unitId,
                AppUtils.getFirstDayOfMonth(monthStartDate), AppUtils.getLastDayOfMonth(monthStartDate));
        LOG.info(" \n Scrapped Asset Monthly at unit " + unitId + " monthStartDate "
                + AppUtils.getFirstDayOfMonth(monthStartDate) + " Amount " + totalScrapped);
        BigDecimal totalDiscarded = getAssetTransferMappingByOwnerAndStartAndEndDateAndStatus(unitId,
                AppUtils.getFirstDayOfMonth(monthStartDate), AppUtils.getLastDayOfMonth(monthStartDate),
                AssetStatusType.DISCARDED.value());
        LOG.info(" \n Discarded Asset Monthly at unit " + unitId + " monthStartDate "
                + AppUtils.getFirstDayOfMonth(monthStartDate) + " Amount " + totalDiscarded);
        return totalScrapped.add(totalDiscarded);
    }

    private BigDecimal getAssetTransferMappingByOwnerAndStartAndEndDateAndStatus(int ownerId, Date startDate,
                                                                                 Date endDate, String assetStatus) {
        List<AssetTransferMappingData> list = scmAssetManagementDao
                .getAssetTransferMappingBetweenDateWithStatusAndOwner(ownerId, startDate, endDate, assetStatus);
        BigDecimal totalLost = BigDecimal.ZERO;
        if (list != null) {
            for (AssetTransferMappingData data : list) {
                AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class,
                        data.getAssetId());
                LOG.info("---------- " + data.getAssetId() + " " + data.getAssetStatus() + " Amount "
                        + assetDefinitionData.getWriteOffAmount());
                totalLost = totalLost.add(assetDefinitionData.getWriteOffAmount());
            }
        }
        return totalLost;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getDepreciationOnUnit(int unitId, Date businessDate, StockTakeType type) {
        if (SCMUtil.isMonthly(type)) {
            LOG.info("\nStart Calculating Monthly depreciation on unit : " + unitId);
            BigDecimal amount = getDepreciationOnUnitByMonth(unitId, businessDate);
            LOG.info("\nEnd Calculating Monthly depreciation Asset Amount " + amount + "  on unit : " + unitId
                    + " Monthly amount " + amount);
            return amount;
        } else {
            LOG.info("\nStart Calculating Daily depreciation on unit : " + unitId);
            BigDecimal amount = getDepreciationOnUnitByDate(unitId, businessDate);
            LOG.info("\nEnd Calculating Daily depreciation Asset Amount " + amount + "  on unit : " + unitId
                    + " Monthly amount " + amount);
            return amount;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getLostAssetAmountOnUnit(int unitId, Date monthStartDate, StockTakeType type) {
        if (SCMUtil.isMonthly(type)) {
            LOG.info("\nStart Calculating Monthly Lost Asset Amount on unit : " + unitId);
            BigDecimal amount = getLostAssetAmountOnUnitByMonth(unitId, monthStartDate);
            LOG.info("\nEnd Calculating Monthly Lost Asset Amount " + amount + "  on unit : " + unitId
                    + " Monthly amount " + amount);
            return amount;
        } else {
            LOG.info("\nStart Calculating Daily Lost Asset Amount on unit : " + unitId);
            BigDecimal amount = getLostAssetAmountOnUnitByDate(unitId, monthStartDate);
            LOG.info("\nEnd Calculating Daily Lost Asset Amount " + amount + "  on unit : " + unitId + " Daily Amount "
                    + amount);
            return amount;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getDamageAssetAmountOnUnit(int unitId, Date monthStartDate, StockTakeType type) {
        if (SCMUtil.isMonthly(type)) {
            LOG.info("\nStart Calculating Monthly damage Asset Amount on unit : " + unitId);
            BigDecimal amount = getDamageAssetAmountOnUnitByMonth(unitId, monthStartDate);
            LOG.info("\nEnd Calculating Monthly damage Asset Amount on unit : " + unitId + " Amount " + amount);
            return amount;
        } else {
            LOG.info("\nStart Calculating Daily damage Asset Amount on unit : " + unitId);
            BigDecimal amount = getDamageAssetAmountOnUnitByDate(unitId, monthStartDate);
            LOG.info("\nEnd Calculating Daily damage Asset Amount on unit : " + unitId + " Amount " + amount);
            return amount;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AssetDefinitionSlimObject> viewAllAssetsSlimFromUnitByName(int unitId, String name) {
        List<AssetDefinition> assetDefinitionList = getConvertedAssetList(
                scmAssetManagementDao.viewAllAssetsSlimFromUnitByName(unitId, name), false);
        List<AssetDefinitionSlimObject> response = new ArrayList<>();
        if (assetDefinitionList != null) {
            assetDefinitionList.forEach(a -> {
                response.add(SCMDataConverter.convertToSlimObject(a, scmCache));
            });
        }
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<AssetDefinitionSlimObject> viewAllAssetsSlimFromUnit(int unitId) {
        List<AssetDefinition> assetDefinitionList = getConvertedAssetList(
                scmAssetManagementDao.getAllAssetFromUnit(unitId), false);
        List<AssetDefinitionSlimObject> response = new ArrayList<>();
        if (assetDefinitionList != null) {
            assetDefinitionList.forEach(a -> {
                response.add(SCMDataConverter.convertToSlimObject(a, scmCache));
            });
        }
        return response;
    }

    public List<String> getEmailHeirarchy(Integer unitId, boolean unitEmailInclude) {
        List<String> emails = new ArrayList<>();
        Unit unit = masterDataCache.getUnit(unitId);
        if (unitEmailInclude) {
            emails.add(unit.getUnitEmail());
        }
        // user-management emails.add("<EMAIL>");
        EmployeeBasicDetail employee = masterDataCache.getEmployeeBasicDetail(unit.getCafeManager().getId());
        while (employee != null) {
            if (employee.getEmailId() != null && employee.getEmailId().trim().length() > 0) {
                emails.add(employee.getEmailId());
            }
            if (!employee.getDesignation().equals("DGM") && employee.getReportingManagerId() != null) {
                employee = masterDataCache.getEmployeeBasicDetail(employee.getReportingManagerId());
            } else {
                employee = null;
            }
            emails.add("<EMAIL>");
        }
        return emails;
    }

    @Override
    public Boolean setInTransit(List<Integer> assetIds, Boolean status) {
        List<AssetDefinitionData> assetDefinitionDataList = new ArrayList<>();
        scmAssetManagementDao.getAssetsByIds(assetIds).stream().map(asset -> {
            asset.setIsInTransit(AppUtils.setStatus(status));
            assetDefinitionDataList.add(asset);
            return asset;
        }).collect(Collectors.toList());
        scmAssetManagementDao.update(assetDefinitionDataList, true);
        return true;
    }

    private Integer transferMonkConsumables(Map<Integer, BigDecimal> skuQuantityMap, Integer unitId, Integer generatedForUnitId, Integer userId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException {
        Map<Integer, Map<Integer, GoodsReceivedItem>> unitWiseDistribution = new HashMap<>();
//        Integer generatedForUnitId = 26033;
        unitWiseDistribution.put(generatedForUnitId, new HashMap<>());
        skuQuantityMap.keySet().stream().forEach(skuId -> {
            unitWiseDistribution.get(generatedForUnitId).put(skuId, transferOrderManagementService.setSkuDistribution(skuId, skuQuantityMap.get(skuId).floatValue(), unitId, 3, true));
        });
        List<TransferOrder> transferOrders = transferOrderManagementService.getTransferObjects(unitWiseDistribution, unitId, userId, true);
        int sourceCompanyId = masterDataCache.getUnit(unitId).getCompany()
                .getId();
        int receivingCompanyId = masterDataCache.getUnit(generatedForUnitId).getCompany()
                .getId();
        boolean isDifferentCompany = sourceCompanyId != receivingCompanyId;

        String invoiceId = transferOrderManagementService.getInvoiceId(unitId,
                generatedForUnitId, isDifferentCompany);
        if(Objects.equals(unitId, generatedForUnitId)){
            return transferOrderManagementService.createTransferOrder(transferOrders.get(0), true, invoiceId);
        }
        return transferOrderManagementService.createTransferOrder(transferOrders.get(0), false, invoiceId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Integer> autoTransferOfMonkIngredients(Map<Integer, BigDecimal> skuQuantityMap, List<Integer> assetIds, Integer unitId, Integer userId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException, ParseException {
        List<Integer> toIds = new ArrayList<>();

        List<AssetDefinitionData> assetDefinitionDataList = new ArrayList<>();

        if (Objects.nonNull(assetIds) && !assetIds.isEmpty()) {
            assetDefinitionDataList = scmAssetManagementDao.getAssetsByIds(assetIds);
            toIds.add(transferAssets(assetDefinitionDataList, unitId, userId,26033));
        }

        if (Objects.nonNull(skuQuantityMap) && !skuQuantityMap.isEmpty()) {
            toIds.add(transferMonkConsumables(skuQuantityMap, unitId, 26033, userId));
        }


        if (Objects.nonNull(assetDefinitionDataList) && !assetDefinitionDataList.isEmpty()) {
            assetDefinitionDataList.stream().map(assetDefinitionData -> {
                assetDefinitionData.setAssetStatus(AssetStatusType.DECAPITALIZED.value());
                return assetDefinitionData;
            }).collect(Collectors.toList());


            scmAssetManagementDao.update(assetDefinitionDataList, true);
        }

        return toIds;
    }

    private TransferOrder createToObject(List<AssetDefinitionData> assetDefinitionDataList, Integer unitId, Integer userId, Integer receivingUnitId) {
        TransferOrder transferOrder = new TransferOrder();
        transferOrder.setBudgetType(CapexStatus.OPEX.value());
        transferOrder.setGeneratedBy(new IdCodeName(userId, null, ""));
        transferOrder.setGenerationUnitId(new IdCodeName(unitId, null, ""));
        transferOrder.setLastUpdatedBy(new IdCodeName(userId, null, ""));
        transferOrder.setGeneratedForUnitId(new IdCodeName(receivingUnitId, null, ""));
        transferOrder.setStatus(SCMOrderStatus.CREATED);
        transferOrder.setToType(TransferOrderType.FIXED_ASSET_TRANSFER);
        transferOrder.setTransferOrderItems(createTOItemObjects(assetDefinitionDataList));
        transferOrder.setTotalCost((double) 0);
        transferOrder.getTransferOrderItems().forEach(item -> {
            transferOrder.setTotalCost(transferOrder.getTotalCost() + item.getTax().doubleValue() + item.getUnitPrice());
        });
        return transferOrder;

    }

    private List<TransferOrderItem> createTOItemObjects(List<AssetDefinitionData> assetDefinitionDataList) {
        List<TransferOrderItem> transferOrderItemList = new ArrayList<>();
        for (AssetDefinitionData assetDefinitionData : assetDefinitionDataList) {
            BigDecimal currentValue = AssetHelper.getCurrentValueOfAsset(assetDefinitionData);
            BigDecimal onePlusPercentage = BigDecimal.ONE.add(assetDefinitionData.getTaxPercentage().divide(new BigDecimal(100)));
            BigDecimal currentValueWithoutTax = currentValue.divide(onePlusPercentage, 6, RoundingMode.HALF_EVEN);
            BigDecimal taxPercentage = Objects.nonNull(assetDefinitionData.getTaxPercentage()) ? assetDefinitionData.getTaxPercentage() : BigDecimal.ZERO;
            BigDecimal taxPrice = BigDecimal.valueOf(((currentValueWithoutTax.doubleValue() * (taxPercentage.doubleValue() / 100)) * 1));
            SkuDefinition sku = scmCache.getSkuDefinition(assetDefinitionData.getSKUId());

            TransferOrderItem transferOrderItem = new TransferOrderItem();
            transferOrderItem.setSkuId(assetDefinitionData.getSKUId());
            transferOrderItem.setProductId(assetDefinitionData.getProduct().getProductId());
            transferOrderItem.setAssociatedAssetId(assetDefinitionData.getAssetId());
            transferOrderItem.setAssociatedAssetTagValue(assetDefinitionData.getTagValue());
            transferOrderItem.setUnitPrice(currentValueWithoutTax.doubleValue());
            transferOrderItem.setNegotiatedUnitPrice(currentValueWithoutTax.doubleValue());
            transferOrderItem.setTax(taxPrice);
            transferOrderItem.setTransferredQuantity(1F);
            transferOrderItem.setUnitOfMeasure(sku.getUnitOfMeasure());
            transferOrderItem.setSkuName(sku.getSkuName());
            transferOrderItem.setPackagingDetails(getPackagingObject(sku.getSkuId(), 1F, 3));
            transferOrderItemList.add(transferOrderItem);
        }
        return transferOrderItemList;
    }


    List<SCMOrderPackaging> getPackagingObject(Integer skuId, Float quantity, Integer packagingId) {
        List<SCMOrderPackaging> packagings = new ArrayList<>();
        if (Objects.nonNull(packagingId)) {
            PackagingDefinition packagingDefinition = scmCache.getPackagingDefinition(packagingId);
            SCMOrderPackaging scmOrderPackaging = new SCMOrderPackaging();
            scmOrderPackaging.setNumberOfUnitsPacked(quantity);
            scmOrderPackaging.setTransferredQuantity(SCMUtil.multiply(scmOrderPackaging.getNumberOfUnitsPacked(), packagingDefinition.getConversionRatio()));
            scmOrderPackaging.setPackagingDefinitionData(packagingDefinition);
            packagings.add(scmOrderPackaging);
        }

        return packagings;
    }

    @Override
    public Integer transferAssets(List<AssetDefinitionData> assetDefinitionDataList, Integer unitId, Integer userId, Integer receivingUnitId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException {
        TransferOrder transferOrder = createToObject(assetDefinitionDataList, unitId, userId, receivingUnitId);
        int sourceCompanyId = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId()).getCompany()
                .getId();
        int receivingCompanyId = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId()).getCompany()
                .getId();
        boolean isDifferentCompany = sourceCompanyId != receivingCompanyId;

        String invoiceId = transferOrderManagementService.getInvoiceId(transferOrder.getGenerationUnitId().getId(),
                transferOrder.getGeneratedForUnitId().getId(), isDifferentCompany);
        return transferOrderManagementService.createTransferOrder(transferOrder, false, invoiceId);
    }



    @Override
    public Map<Integer, List<AssetDefinition>> getMonkIngredientsAssets(Integer productId, Integer unitId) throws DataNotFoundException, SumoException {
        Map<Integer, List<AssetDefinition>> skuQuantityMap = new HashMap<>();
        List<Integer> productList = new ArrayList<>();
        productList = bookingService.calculateConsumption(26033, productId, BigDecimal.ONE).getBookingConsumption().stream().map(c -> c.getProductId())
                .collect(Collectors.toList());
        List<AssetDefinition> assets = viewAllTransferableAssetsFromUnitByProducts(unitId, productList, false);
        LOG.info("Asset Size : {}", assets.size());
        assets.forEach(asset -> {
            if (!skuQuantityMap.containsKey(asset.getSkuId())) {
                skuQuantityMap.put(asset.getSkuId(), new ArrayList<>());
                skuQuantityMap.get(asset.getSkuId()).add(asset);
            } else {
                skuQuantityMap.get(asset.getSkuId()).add(asset);
            }
        });
        return skuQuantityMap;
    }

    @Override
    public Map<Integer, BigDecimal> getMonkIngredientsStock(List<Integer> productIds, Integer unitId) {
        List<CostDetailData> inventory = priceDao.getCurrentPrices(PriceUpdateEntryType.PRODUCT,
                unitId, productIds, false);
        return inventory.stream()
                .collect(Collectors.groupingBy(CostDetailData::getKeyId,
                        Collectors.reducing(BigDecimal.ZERO, CostDetailData::getQuantity, SCMUtil::add)
                ));
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, BigDecimal> getNonAssetStockInAssetInventory() {
        List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.getAllNonAssetStockInAssetInventory(null);
        Map<Integer, BigDecimal> assetQtyMap = new HashMap<>();
        for (AssetDefinitionData assetDefinitionData : assetDefinitionDataList) {
            if (!assetQtyMap.containsKey(assetDefinitionData.getProduct().getProductId())) {
                assetQtyMap.put(assetDefinitionData.getProduct().getProductId(), BigDecimal.ZERO);

            }
            BigDecimal currentQty = assetQtyMap.get(assetDefinitionData.getProduct().getProductId());
            currentQty = SCMUtil.add(currentQty, BigDecimal.ONE);
            assetQtyMap.put(assetDefinitionData.getProduct().getProductId(), currentQty);
        }
        return assetQtyMap;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean transferAndConvertNonAssetsInAssetInventory(Map<Integer, BigDecimal> productQtyMap, Integer userId) throws DataNotFoundException,
            TransferOrderCreationException, InventoryUpdateException, SumoException, ParseException {
        Integer receivingUnitId = 26142;
        List<Integer> productIds = new ArrayList<>(productQtyMap.keySet());
        List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.getAllNonAssetStockInAssetInventory(productIds);
        List<AssetDefinitionData> transferredAssetsList = new ArrayList<>();
        for (AssetDefinitionData assetDefinition : assetDefinitionDataList) {
            BigDecimal availableQty = productQtyMap.get(assetDefinition.getProduct().getProductId());
            if (availableQty.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            availableQty = SCMUtil.subtract(availableQty, BigDecimal.ONE);
            productQtyMap.put(assetDefinition.getProduct().getProductId(), availableQty);

            transferredAssetsList.add(assetDefinition);


            TransferOrder transferOrder = createToObject(new ArrayList<>(Arrays.asList(assetDefinition)), assetDefinition.getUnitId(), userId, receivingUnitId);
            int sourceCompanyId = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId()).getCompany()
                    .getId();
            int receivingCompanyId = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId()).getCompany()
                    .getId();
            boolean isDifferentCompany = sourceCompanyId != receivingCompanyId;

            String invoiceId = transferOrderManagementService.getInvoiceId(transferOrder.getGenerationUnitId().getId(),
                    transferOrder.getGeneratedForUnitId().getId(), isDifferentCompany);
            Integer toId = transferOrderManagementService.createTransferOrder(transferOrder, false, invoiceId);
            GoodsReceivedData goodsReceivedData = scmAssetManagementDao.getGrForTO(toId);

            GoodsReceived goodsReceived = SCMDataConverter.convert(goodsReceivedData, new IdCodeName(goodsReceivedData.getReceivedBy(), "", ""),
                    new IdCodeName(goodsReceivedData.getReceivedBy(), "", ""), null,
                    new IdCodeName(goodsReceivedData.getGenerationUnitId(), "", ""), new IdCodeName(goodsReceivedData.getGeneratedForUnitId(), "", ""),
                    true, scmCache, masterDataCache);
            goodsReceived.setTransferOrderType(TransferOrderType.REGULAR_TRANSFER);
            goodsReceived.getGoodsReceivedItems().get(0).setReceivedQuantity(Float.valueOf(1));
            goodsReceived.getGoodsReceivedItems().get(0).getPackagingDetails().get(0).setReceivedQuantity(Float.valueOf(1));
            goodsReceiveManagementService.settleGoodsReceivedDetail(goodsReceived);
        }
        convertNonAssetStockInAssetInventory(transferredAssetsList);

        return true;

    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean convertNonAssetStockInAssetInventory(List<AssetDefinitionData> assetDefinitionDataList) throws SumoException {
        //List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.getAllNonAssetStockInAssetInventory(productIds);
        for (AssetDefinitionData assetDefinitionData : assetDefinitionDataList) {
            logCurrentStatus(assetDefinitionData);
            assetDefinitionData.setAssetStatus(AssetStatusType.DECAPITALIZED.value());
            assetDefinitionData.setIsInTransit(AppUtils.setStatus(false));
            BigDecimal currentValue = AssetHelper.getCurrentValueOfAsset(assetDefinitionData);
            Date endDate = SCMUtil.getCurrentTimestamp();
            assetDefinitionData.setActualEndDate(endDate);
            assetDefinitionData.setWriteOffAmount(currentValue);
            assetDefinitionData.setIsWriteOff("Y");
            assetDefinitionData.setWriteOffType(AssetStatusType.DECAPITALIZED.value());
        }

        scmAssetManagementDao.update(assetDefinitionDataList, true);
        return true;
    }

    @Override
    public List<Integer> getNonScannableAssetMappings() {
        return scmAssetManagementDao.findNonScannableMapping(null, true).stream().map(product -> product.getProductId()).
                collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean saveNonScannableAssetMappings(List<Integer> productIds) {
        List<NonScannableAssetProducts> mappedProducts = scmAssetManagementDao.findNonScannableMapping(null, true);
        List<NonScannableAssetProducts> updateList = new ArrayList<>();
        List<NonScannableAssetProducts> newList = new ArrayList<>();
        for (NonScannableAssetProducts mappedProduct : mappedProducts) {
            if (productIds.contains(mappedProduct.getProductId())) {
                productIds.remove(mappedProduct.getProductId());
                if (mappedProduct.getMappingStatus().equals(AppConstants.IN_ACTIVE)) {
                    mappedProduct.setMappingStatus(AppConstants.ACTIVE);
                    updateList.add(mappedProduct);
                }
            } else if (!productIds.contains(mappedProduct.getProductId()) && mappedProduct.getMappingStatus().equals(AppConstants.ACTIVE)) {
                mappedProduct.setMappingStatus((AppConstants.IN_ACTIVE));
                updateList.add(mappedProduct);
            }
        }

        for (Integer productId : productIds) {
            NonScannableAssetProducts newMappedProduct = new NonScannableAssetProducts();
            newMappedProduct.setProductId(productId);
            newMappedProduct.setMappingStatus(AppConstants.ACTIVE);
            newList.add(newMappedProduct);
        }
        scmAssetManagementDao.update(updateList, true);
        scmAssetManagementDao.addAll(newList);
        return true;
    }


    private TransferOrder setRequestOrderDetails(TransferOrder transferOrder ,RequestOrderData requestOrderData){
        transferOrder.setRequestOrderId(requestOrderData.getId());
        Map<Integer,Integer> productRoItemIdMap =  requestOrderData.getRequestOrderItemDatas().stream().collect(Collectors.toMap(RequestOrderItemData::getProductId,
                RequestOrderItemData::getId));
        for(TransferOrderItem item : transferOrder.getTransferOrderItems()){
            item.setRequestOrderItemId(productRoItemIdMap.get(item.getProductId()));
        }
        return transferOrder;
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer getFaTransferSubmitEvent(Integer eventId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException {
           List<StockEventAssetMappingDefinitionData> stockEventAssets = scmAssetManagementDao.findAssetsByEventId(eventId) ;
        StockEventDefinitionData stockEvent =scmAssetManagementDao.getStockEventById(eventId) ;
        if(!stockEvent.getEventStatus().equalsIgnoreCase(StockEventStatusType.INITIATED.value())){
            throw new SumoException("Can;t Make Transfer!! ","Event Is Already Submitted/Cancelled");
        }
        List<Integer> assetIds = stockEventAssets.stream().map(asset -> asset.getAssetId()).collect(Collectors.toList());
        List<AssetDefinitionData> assetDefinitionDataList = scmAssetManagementDao.getAssetsByIds(assetIds);

        TransferOrder transferOrder = createToObject(assetDefinitionDataList, stockEvent.getUnitId(), stockEvent.getInitiatedBy()
                , stockEvent.receivingUnitId);
        if(Objects.nonNull(stockEvent.getRoId())){
            RequestOrderData requestOrderData = scmAssetManagementDao.find(RequestOrderData.class,stockEvent.getRoId());
            transferOrder = setRequestOrderDetails(transferOrder,requestOrderData);
        }
        transferOrder.setBudgetType(stockEvent.getBudgetType());
        transferOrder.setToType(TransferOrderType.valueOf(stockEvent.getSubType()));
        int sourceCompanyId = masterDataCache.getUnit(transferOrder.getGenerationUnitId().getId()).getCompany()
                .getId();
        int receivingCompanyId = masterDataCache.getUnit(transferOrder.getGeneratedForUnitId().getId()).getCompany()
                .getId();
        boolean isDifferentCompany = sourceCompanyId != receivingCompanyId;

        String invoiceId = transferOrderManagementService.getInvoiceId(transferOrder.getGenerationUnitId().getId(),
                transferOrder.getGeneratedForUnitId().getId(), isDifferentCompany);
        Integer toId =  transferOrderManagementService.createTransferOrder(transferOrder, false, invoiceId);
        stockEvent.setEventStatus(StockEventStatusType.COMPLETED.value());
        stockEvent.setToId(toId);
        stockEvent.setLastUpdationTime(SCMUtil.getCurrentTimestamp());

        scmAssetManagementDao.update(stockEvent,true);
        return  toId;

    }





    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  Boolean saveExcessAssetFoundData(MultipartFile file , String comment , String userDescription , Integer eventId) throws SumoException {
        FaStockEventExtraScannedItems extraItem = new FaStockEventExtraScannedItems();
        extraItem.setComment(comment);
        extraItem.setUserDescription(userDescription);
        extraItem.setEventId(eventId);
        extraItem.setIsSettled(AppUtils.setStatus(false));
        extraItem = scmAssetManagementDao.add(extraItem,true);
        DocumentDetail documentDetail = null;
        if(Objects.nonNull(file)){
            String fileName = "EXTRA_ITEM" + eventId + "_" + extraItem.getFaStockEventExtraScannedItemsId() + ".png";
            documentDetail = uploadImage(FileType.OTHERS,MimeType.PNG,fileName,file,eventId);
            extraItem.setDocId(documentDetail.getDocumentId());
        }
        return  true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Boolean sendFAStockTakeReport(Integer eventId, Boolean submit) {

        StockEventDefinitionData event = scmAssetManagementDao.getStockEventById(eventId);
        String initiatorEmail = masterDataCache.getEmployeeBasicDetail(event.getInitiatedBy()).getEmailId();

        Date businessDate;
        Integer unitId = event.getUnitId();
        String eventSubtype = event.getEventType();
        if(event.getLastUpdationTime() != null) {
            businessDate = event.getLastUpdationTime();
        }else
        {
            businessDate = event.getEventCreationDate();
        }

        return reportingService.sendStockTakeReport(businessDate, unitId, eventId, eventSubtype, initiatorEmail, submit);
    }

    @Override
    public Map<String, List<AssetRecovery>> getAssetsPendingRecovery(Integer unitId, String recoveryStatus, String assetName, Integer assetId, String startDate, String endDate) throws SumoException {
        Map<String, List<AssetRecovery>> statusAssetMap = new HashMap<>();
        List<AssetRecoveryData> recoveryAssets = new ArrayList<>();
        List<AssetRecovery> list = new ArrayList<>();
        Date start = null;
        Date end = null;
        if(Objects.nonNull(startDate)){
            start = AppUtils.getDate(startDate,"yyyy-MM-dd");
        }
        if(Objects.nonNull(endDate)){
            end = AppUtils.getDate(endDate,"yyyy-MM-dd");
        }
        recoveryAssets = scmAssetManagementDao.getAssetsInRecoveryFromUnit(null,unitId,recoveryStatus,assetId,start,end);
        if (recoveryAssets != null) {
            for (AssetRecoveryData data : recoveryAssets) {
                list.add(convertToRecoveryDefinition(data));
            }
        }
        if(Objects.nonNull(assetName) && Objects.isNull(assetId)){
            list = list.stream().filter(e -> Objects.equals(e.getAssetDefinition().getAssetName(), assetName)).collect(Collectors.toList());
        }
        statusAssetMap = list.stream().collect(Collectors.groupingBy(AssetRecovery::getRecoveryStatus));
        return statusAssetMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean submitRecovery(Integer recoveryId, List<AssetRecoveryDetail> recoveryList) throws SumoException {
        List<AssetRecoveryDetailData> insuranceRecoveryList = new ArrayList<>();
        List<AssetRecoveryDetailData> employeeRecoveryList = new ArrayList<>();
        List<AssetRecoveryDetailData> writeOffList = new ArrayList<>();
        Boolean insuranceRecoveryComplete = false;
        Boolean employeeRecoveryComplete = false;
        if(!recoveryList.isEmpty()){
            for(AssetRecoveryDetail detail : recoveryList){
                if(Objects.equals(detail.getRecoveryType(),RecoveryType.EMPLOYEE.value())){
                    employeeRecoveryList.add(convertToAssetRecoveryDetailData(detail));
                }else if(Objects.equals(detail.getRecoveryType(),RecoveryType.INSURANCE.value())){
                    insuranceRecoveryList.add(convertToAssetRecoveryDetailData(detail));
                }else if(Objects.equals(detail.getRecoveryType(),RecoveryType.WRITE_OFF.value())){
                    writeOffList.add(convertToAssetRecoveryDetailData(detail));
                }
            }
        }
        List<AssetRecoveryData> recoveryDataList = scmAssetManagementDao.getAssetsInRecoveryFromUnit(recoveryId,null,null,null,null,null);
        AssetRecoveryData recoveryData = recoveryDataList.get(0);
        if(!insuranceRecoveryList.isEmpty()){
            recoveryData.setInsuranceRecoveryStatus(AssetRecoveryStatusType.RECOVERED.value());
            recoveryData.setInsuranceRecoveryAmount(insuranceRecoveryList.get(0).getAmountRecovered());
            scmAssetManagementDao.addAll(insuranceRecoveryList);
            insuranceRecoveryComplete = true;
        }
        if(!employeeRecoveryList.isEmpty()){
            recoveryData.setEmployeeRecoveryStatus(AssetRecoveryStatusType.RECOVERED.value());
            BigDecimal totalEmployeeRecoveryAmount = BigDecimal.ZERO;
            for(AssetRecoveryDetailData recovery :employeeRecoveryList){
                totalEmployeeRecoveryAmount = totalEmployeeRecoveryAmount.add(recovery.getAmountRecovered());
            }
            recoveryData.setEmployeeRecoveryAmount(totalEmployeeRecoveryAmount);
            scmAssetManagementDao.addAll(employeeRecoveryList);
            employeeRecoveryComplete = true;
        }
        if(Objects.equals(recoveryData.getInsuranceRecoveryStatus(), AssetRecoveryStatusType.RECOVERED.value()) &&
                Objects.equals(recoveryData.getEmployeeRecoveryStatus() , AssetRecoveryStatusType.RECOVERED.value())){
            if(!writeOffList.isEmpty()){
                scmAssetManagementDao.addAll(writeOffList);
                recoveryData.setWriteOffAmount(writeOffList.get(0).getAmountRecovered());
            }
            BigDecimal totalAmountRecovered = BigDecimal.ZERO;
            if(Objects.nonNull(recoveryData.getInsuranceRecoveryAmount())){
                totalAmountRecovered = totalAmountRecovered.add(recoveryData.getInsuranceRecoveryAmount());
            }
            if(Objects.nonNull(recoveryData.getEmployeeRecoveryAmount())){
                totalAmountRecovered = totalAmountRecovered.add(recoveryData.getEmployeeRecoveryAmount());
            }
//            if(Objects.nonNull(recoveryData.getWriteOffAmount())){
//                totalAmountRecovered = totalAmountRecovered.add(recoveryData.getWriteOffAmount());
//            }
            recoveryData.setRecoveryStatus(AssetRecoveryStatusType.RECOVERED.value());
            recoveryData.setAmountRecovered(totalAmountRecovered);
            scmAssetManagementDao.update(recoveryData,true);
            try{
                AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class, recoveryData.getAssetId());
                AssetDefinition assetDefinition = updateAssetStatus(assetDefinitionData,
                        AssetStatusType.LOST_ADJUSTED);
            }catch(Exception e){
                LOG.info("Unable to update asset status for asset ::::: {}", recoveryData.getAssetId());
                throw new SumoException("Error Completing Recovery Process");
            }
            return true;
        }else if(insuranceRecoveryComplete || employeeRecoveryComplete){
            recoveryData.setRecoveryStatus(AssetRecoveryStatusType.PARTIAL_RECOVERED.value());
            scmAssetManagementDao.update(recoveryData,true);
            return true;
        }

        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean editAssetName(Integer assetId, String newName, Integer userId) throws SumoException {
        AssetDefinitionData assetDefinitionData = scmAssetManagementDao.find(AssetDefinitionData.class, assetId);
        AssetDefinitionDataLog assetDefinitionDataLog = logCurrentStatus(assetDefinitionData);
        if (assetDefinitionDataLog != null) {
            assetDefinitionData.setAssetName(newName);
            assetDefinitionDataLog.setUpdatedBy(userId);
            scmAssetManagementDao.update(assetDefinitionData, true);
            return true;
        } else {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getStockTakeAppVersion(boolean reloadCache) {
        if (reloadCache) {
            return scmAssetManagementDao.getLatestStockTakeVersion();
        } else {
            return scmCache.getStockTakeAppVersion();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean saveInvalidTag(Integer eventId, String tagValue) throws SumoException {
        try{
            InvalidTagScannedData data = new InvalidTagScannedData();
            data.setEventId(eventId);
            data.setTagValue(tagValue);
            data.setScanTime(SCMUtil.getCurrentTimestamp());
            scmAssetManagementDao.add(data,false);
            LOG.info("Added invalid asset tag ::::: {} , for event ID ::::: {}", tagValue, eventId);
            return true;
        }catch (Exception e){
            LOG.error("Failed to add invalid asset tag ::::: {} , for event ID ::::: {}", tagValue, eventId);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean convertFaToConsumable(List<Integer> assetIds, Integer userId) throws DataNotFoundException, TransferOrderCreationException, InventoryUpdateException, SumoException, ParseException {
        List<Integer> toIds = new ArrayList<>();
        List<AssetDefinitionData> assetDefinitionDataList = new ArrayList<>();
        Map<Integer,List<AssetDefinitionData>> unitAssetMapping = new HashMap<>();

        if (Objects.nonNull(assetIds) && !assetIds.isEmpty()) {
            assetDefinitionDataList = scmAssetManagementDao.getAssetsByIds(assetIds);
            for(AssetDefinitionData assetDefinitionData : assetDefinitionDataList){
                if(Objects.nonNull(unitAssetMapping.get(assetDefinitionData.getUnitId()))){
                    unitAssetMapping.get(assetDefinitionData.getUnitId()).add(assetDefinitionData);
                }else{
                    List<AssetDefinitionData> list = new ArrayList<>();
                    list.add(assetDefinitionData);
                    unitAssetMapping.put(assetDefinitionData.getUnitId(), list);
                }
            }
            for(Integer unitId : unitAssetMapping.keySet()){
                toIds.add(transferAssets(unitAssetMapping.get(unitId), unitId, AppConstants.SYSTEM_EMPLOYEE_ID, unitId));
            }
            for(AssetDefinitionData assetDefinitionData : assetDefinitionDataList){
                assetDefinitionData.setAssetStatus(AssetStatusType.DECAPITALIZED.value());
            }
            scmAssetManagementDao.flush();
            CategoryConversionData categoryConversionData = new CategoryConversionData();
            categoryConversionData.setOldCategory(SCMServiceConstants.CATEGORY_FIXED_ASSETS);
            categoryConversionData.setNewCategory(SCMServiceConstants.CATEGORY_CONSUMABLE);
            categoryConversionData.setType(CategoryConversionType.FA_TO_CONSUMABLE.value());
            categoryConversionData.setConversionDate(SCMUtil.getCurrentTimestamp());
            categoryConversionData.setConvertedBy(userId);
            categoryConversionData.setQuantity(0);
            categoryConversionData = scmAssetManagementDao.add(categoryConversionData,true);
            List<CategoryConversionItemData> list = new ArrayList<>();
            for(Integer toId : toIds){
                GoodsReceivedData grData =  transferOrderManagementService.getGrByTO(toId);
                try{
                    grData.setAutoGenerated(AppUtils.setStatus(true));
                    goodsReceiveManagementDao.update(grData,true);
                    GoodsReceived gr = SCMDataConverter.convert(grData, new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", ""),
                            new IdCodeName(AppConstants.SYSTEM_EMPLOYEE_ID, "", ""), null,
                            new IdCodeName(grData.getGenerationUnitId(), "", ""), new IdCodeName(grData.getGeneratedForUnitId(), "", ""),
                            true, scmCache, masterDataCache);
                    gr.setTransferOrderType(TransferOrderType.REGULAR_TRANSFER);
                    gr.setAutoGenerated(true);
                    for(GoodsReceivedItem item : gr.getGoodsReceivedItems()){
                        item.setReceivedQuantity(item.getTransferredQuantity());
                        for(SCMOrderPackaging packaging : item.getPackagingDetails()){
                            packaging.setReceivedQuantity(packaging.getTransferredQuantity());
                        }
                        CategoryConversionItemData categoryConversionItemData = new CategoryConversionItemData();
                        categoryConversionItemData.setConversionId(categoryConversionData.getConversionId());
                        categoryConversionItemData.setGrId(gr.getId());
                        categoryConversionItemData.setAmount(item.getPrice());
                        categoryConversionItemData.setAssetId(item.getAssociatedAssetId());
                        categoryConversionItemData.setProductId(item.getProductId());
                        list.add(categoryConversionItemData);
                    }
                    goodsReceiveManagementService.settleGoodsReceivedDetail(gr);
                }catch (Exception e){
                    LOG.error("Unable to settle gr for category conversion for grId :::: {}",grData.getId());
                }
            }
            scmAssetManagementDao.addAll(list);
            categoryConversionData.setQuantity(list.size());
            scmAssetManagementDao.update(categoryConversionData,false);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public Boolean convertConsumableToFa(List<Integer> skuIds, Integer userId) throws SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException {
        Boolean conversionSuccess = false;
        Set<Integer> productIdSet = new HashSet<>();
        Map<Integer, Integer> skuProductMap = new HashMap<>();
        Map<Integer, List<CostDetailData>> warehouseSkuMap = new HashMap<>();
        Map<Integer, List<CostDetailData>> cafeProductMap = new HashMap<>();
        Map<Integer, Map<Integer, BigDecimal>> skuQtyMapCafe = new HashMap<>();
        Map<Integer, Map<Integer, BigDecimal>> skuQtyMapWH = new HashMap<>();
        List<CategoryConversionItemData> list = new ArrayList<>();
        for(Integer skuId : skuIds){
            int productId = scmCache.getSkuDefinition(skuId).getLinkedProduct().getId();
            productIdSet.add(productId);
            skuProductMap.put(skuId,productId);
        }
        List<Integer> productIds = productIdSet.stream().collect(Collectors.toList());
        List<CostDetailData> warehouseItems = stockManagementDao.fetchProductInventory(PriceUpdateEntryType.SKU.value(), skuIds);
        List<CostDetailData> cafeItems = stockManagementDao.fetchProductInventory(PriceUpdateEntryType.PRODUCT.value(), productIds);
        productIds = cafeItems.stream().collect(Collectors.groupingBy(CostDetailData::getKeyId)).keySet().stream().collect(Collectors.toList());
        List<Integer> skuList = (List<Integer>) skuProductMap.keySet().stream().collect(Collectors.toList());
        List<Integer> finalProductIds = productIds;
        skuList = skuList.stream().filter(skuId -> finalProductIds.contains(skuProductMap.get(skuId))).collect(Collectors.toList());
        List<AssetDefinitionData> updateList = new ArrayList<>();

        if(!cafeItems.isEmpty()){
            cafeProductMap = cafeItems.stream().collect(Collectors.groupingBy(CostDetailData::getUnitId));
            for(Integer unitId : cafeProductMap.keySet()){
                List<CostDetailData> cafeData = cafeProductMap.get(unitId);
                Map<Integer, BigDecimal> skuMap = new HashMap<>();
                for(CostDetailData costDetailData :cafeData){
                    for(Integer skuId : skuList){
                        if(Objects.equals(skuProductMap.get(skuId), costDetailData.getKeyId())){
                            skuMap.put(skuId, costDetailData.getQuantity());
                        }
                    }
                }
                if(Objects.isNull(skuQtyMapCafe.get(unitId))){
                    skuQtyMapCafe.put(unitId, new HashMap<>());
                }
                skuQtyMapCafe.get(unitId).putAll(skuMap);
            }
            CategoryConversionData categoryConversionData = new CategoryConversionData();
            categoryConversionData.setOldCategory(SCMServiceConstants.CATEGORY_CONSUMABLE);
            categoryConversionData.setNewCategory(SCMServiceConstants.CATEGORY_FIXED_ASSETS);
            categoryConversionData.setType(CategoryConversionType.CONSUMABLE_TO_FA.value());
            categoryConversionData.setConversionDate(SCMUtil.getCurrentTimestamp());
            categoryConversionData.setConvertedBy(userId);
            categoryConversionData.setQuantity(0);
            categoryConversionData = scmAssetManagementDao.add(categoryConversionData,true);
            for(Integer unitId : skuQtyMapCafe.keySet()){
                Integer toId = transferMonkConsumables(skuQtyMapCafe.get(unitId), unitId, unitId, userId);
                List<TransferOrder> transferOrderList = transferOrderManagementService.findTransferOrders(null,null,null,null,null,toId);
                Map<Integer, BigDecimal> skuPriceMap = new HashMap<>();
                if(!transferOrderList.isEmpty()){
                    TransferOrder finalOutput = priceDao.reduceConsumable(transferOrderList.get(0), false);
                    for(TransferOrderItem transferOrderItem : finalOutput.getTransferOrderItems()){
                        skuPriceMap.put(transferOrderItem.getSkuId(), transferOrderItem.getPrice());
                    }
                    finalOutput.setExternal(false);
                }
                for(Integer skuId : skuQtyMapCafe.get(unitId).keySet()){
                    for(int counter = 1 ; counter <= skuQtyMapCafe.get(unitId).get(skuId).intValue(); counter++){
                        AssetDefinition assetDefinition = new AssetDefinition();
                        assetDefinition.setProductId(scmCache.getSkuDefinition(skuId).getLinkedProduct().getId());
                        assetDefinition.setSkuId(skuId);
                        assetDefinition.setUnitId(unitId);
                        assetDefinition.setCreatedBy(new IdCodeName(userId,"",masterDataCache.getEmployeeBasicDetail(userId).getName()));
                        assetDefinition.setLastTagPrintedBy(new IdCodeName(userId,"",masterDataCache.getEmployeeBasicDetail(userId).getName()));
                        assetDefinition = createBackDatedAsset(assetDefinition);
                        AssetDefinitionData asset = scmAssetManagementDao.find(AssetDefinitionData.class,
                                assetDefinition.getAssetId());
                        ProductDefinitionData product = scmAssetManagementDao.find(ProductDefinitionData.class,
                                assetDefinition.getProductId());
                        asset.setPrice(skuPriceMap.get(skuId));
                        int stateId = masterDataCache.getUnit(asset.getUnitId()).getLocation().getState().getId();
                        String hsnCode = product.getTaxCategoryCode();
                        TaxData taxData = taxCache.getTaxData(stateId, hsnCode);
                        BigDecimal sgstTaxPercentage = taxData.getState().getSgst();
                        BigDecimal cgstTaxPercentage = taxData.getState().getCgst();
                        BigDecimal sgstTax = AppUtils.percentOfWithScale10(asset.getPrice(), sgstTaxPercentage);
                        BigDecimal cgstTax = AppUtils.percentOfWithScale10(asset.getPrice(), cgstTaxPercentage);
                        asset.setTax(SCMUtil.add(sgstTax, cgstTax));
                        asset.setTaxPercentage(SCMUtil.add(sgstTaxPercentage, cgstTaxPercentage));
                        asset.setGrossBlock(SCMUtil.add(asset.getPrice(), asset.getTax()));
                        asset.setProcurementCost(SCMUtil.multiply(asset.getGrossBlock() , BigDecimal.valueOf(0.95)));
                        completeAssetCreation(asset);
                        updateList.add(asset);
                        CategoryConversionItemData categoryConversionItemData = new CategoryConversionItemData();
                        categoryConversionItemData.setConversionId(categoryConversionData.getConversionId());
                        categoryConversionItemData.setGrId(asset.getGrId());
                        categoryConversionItemData.setAmount(asset.getProcurementCost());
                        categoryConversionItemData.setAssetId(asset.getAssetId());
                        categoryConversionItemData.setProductId(asset.getProduct().getProductId());
                        list.add(categoryConversionItemData);
                    }
                }
            }
            if(!list.isEmpty()){
                scmAssetManagementDao.addAll(list);
                categoryConversionData.setQuantity(list.size());
                scmAssetManagementDao.update(categoryConversionData,false);
            }
            conversionSuccess = true;
        }
        if(!warehouseItems.isEmpty()){

            warehouseSkuMap = warehouseItems.stream().collect(Collectors.groupingBy(CostDetailData::getUnitId));
            for(Integer unitId : warehouseSkuMap.keySet()){
                if(Objects.isNull(skuQtyMapWH.get(unitId))){
                    skuQtyMapWH.put(unitId, new HashMap<>());
                }
                skuQtyMapWH.get(unitId).putAll(warehouseSkuMap.get(unitId).stream().collect(Collectors.toMap(CostDetailData::getKeyId, CostDetailData::getQuantity)));
            }
            CategoryConversionData categoryConversionData = new CategoryConversionData();
            categoryConversionData.setOldCategory(SCMServiceConstants.CATEGORY_CONSUMABLE);
            categoryConversionData.setNewCategory(SCMServiceConstants.CATEGORY_FIXED_ASSETS);
            categoryConversionData.setType(CategoryConversionType.CONSUMABLE_TO_FA.value());
            categoryConversionData.setConversionDate(SCMUtil.getCurrentTimestamp());
            categoryConversionData.setConvertedBy(userId);
            categoryConversionData.setQuantity(0);
            categoryConversionData = scmAssetManagementDao.add(categoryConversionData,true);
            for(Integer unitId : skuQtyMapWH.keySet()){
                Integer toId = transferMonkConsumables(skuQtyMapWH.get(unitId), unitId, unitId, userId);
                List<TransferOrder> transferOrderList = transferOrderManagementService.findTransferOrders(null,null,null,null,null,toId);
                Map<Integer, BigDecimal> skuPriceMap = new HashMap<>();
                if(!transferOrderList.isEmpty()){
                    TransferOrder finalOutput = priceDao.reduceConsumable(transferOrderList.get(0), false);
                    for(TransferOrderItem transferOrderItem : finalOutput.getTransferOrderItems()){
                        skuPriceMap.put(transferOrderItem.getSkuId(), transferOrderItem.getPrice());
                    }
                    finalOutput.setExternal(false);
                }
                for(Integer skuId : skuQtyMapWH.get(unitId).keySet()){
                    for(int counter = 1 ; counter <= skuQtyMapWH.get(unitId).get(skuId).intValue(); counter++){
                        AssetDefinition assetDefinition = new AssetDefinition();
                        assetDefinition.setProductId(scmCache.getSkuDefinition(skuId).getLinkedProduct().getId());
                        assetDefinition.setSkuId(skuId);
                        assetDefinition.setUnitId(unitId);
                        assetDefinition.setCreatedBy(new IdCodeName(userId,"",masterDataCache.getEmployeeBasicDetail(userId).getName()));
                        assetDefinition.setLastTagPrintedBy(new IdCodeName(userId,"",masterDataCache.getEmployeeBasicDetail(userId).getName()));
                        assetDefinition = createBackDatedAsset(assetDefinition);
                        AssetDefinitionData asset = scmAssetManagementDao.find(AssetDefinitionData.class,
                                assetDefinition.getAssetId());
                        ProductDefinitionData product = scmAssetManagementDao.find(ProductDefinitionData.class,
                                assetDefinition.getProductId());
                        asset.setPrice(skuPriceMap.get(skuId));
                        int stateId = masterDataCache.getUnit(asset.getUnitId()).getLocation().getState().getId();
                        String hsnCode = product.getTaxCategoryCode();
                        TaxData taxData = taxCache.getTaxData(stateId, hsnCode);
                        BigDecimal sgstTaxPercentage = taxData.getState().getSgst();
                        BigDecimal cgstTaxPercentage = taxData.getState().getCgst();
                        BigDecimal sgstTax = AppUtils.percentOfWithScale10(asset.getPrice(), sgstTaxPercentage);
                        BigDecimal cgstTax = AppUtils.percentOfWithScale10(asset.getPrice(), cgstTaxPercentage);
                        asset.setTax(SCMUtil.add(sgstTax, cgstTax));
                        asset.setTaxPercentage(SCMUtil.add(sgstTaxPercentage, cgstTaxPercentage));
                        asset.setGrossBlock(SCMUtil.add(asset.getPrice(), asset.getTax()));
                        asset.setProcurementCost(SCMUtil.multiply(asset.getGrossBlock() , BigDecimal.valueOf(0.95)));
                        completeAssetCreation(asset);
                        updateList.add(asset);
                        CategoryConversionItemData categoryConversionItemData = new CategoryConversionItemData();
                        categoryConversionItemData.setConversionId(categoryConversionData.getConversionId());
                        categoryConversionItemData.setGrId(asset.getGrId());
                        categoryConversionItemData.setAmount(asset.getPrice());
                        categoryConversionItemData.setAssetId(asset.getAssetId());
                        categoryConversionItemData.setProductId(asset.getProduct().getProductId());
                        list.add(categoryConversionItemData);
                    }
                }
            }
            if(!list.isEmpty()){
                scmAssetManagementDao.addAll(list);
                categoryConversionData.setQuantity(list.size());
                scmAssetManagementDao.update(categoryConversionData,false);
            }
            conversionSuccess = true;
        }
        if(!updateList.isEmpty()){
            scmAssetManagementDao.update(updateList, false);
        }
        return conversionSuccess;
    }


    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadImage(FileType type, MimeType mimeType,String fileName,
                                         MultipartFile file ,Integer eventId) {
        FileDetail s3File = null;
        try {
            s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "excessAssets", fileName, file);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setUploadTypeId(eventId);
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(DocUploadType.INVOICE_BARCODE);
            documentDetail.setFileType(FileType.OTHERS);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail
                    .setUpdatedBy(SCMUtil.generateIdCodeName(SCMServiceConstants.SYSTEM_USER, "SYSTEM", "SYSTEM"));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = scmAssetManagementDao.add(SCMDataConverter.convert(documentDetail), true);
            if (data.getDocumentId() != null) {
                return SCMDataConverter.convert(data);
            }
        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }
}
