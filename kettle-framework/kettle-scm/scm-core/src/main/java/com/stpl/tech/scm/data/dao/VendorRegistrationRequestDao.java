package com.stpl.tech.scm.data.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.data.model.VendorEditedDetail;
import com.stpl.tech.scm.data.model.VendorRegistrationRequestDetail;
import com.stpl.tech.scm.domain.model.VendorRegistrationRequest;
import com.stpl.tech.scm.domain.model.VendorStatus;

public interface VendorRegistrationRequestDao extends SCMAbstractDao {

	public List<VendorRegistrationRequest> getAllVendorRegistrationRequest(Date startDate, Date endDate,
			boolean getPendingOnly);

	public VendorRegistrationRequestDetail addVendorRegistrationRequest(VendorRegistrationRequest request) throws SumoException;

	public boolean cancelVendorRegistrationRequest(Integer id);

	public boolean updateVendorStatus(Integer id, VendorStatus failed);

	public VendorRegistrationRequestDetail findByToken(String id) throws VendorRegistrationException;

	public VendorRegistrationRequestDetail getActiveVendorRequest(Integer vendorId) throws VendorRegistrationException;

	/**
	 * @param statues
	 * @return
	 */
	public List<VendorRegistrationRequest> getAllVendorRegistrationRequest(List<String> statues);

	public boolean findDuplicatePanVendor(String vendorType, String vendorPan);

	public String findVendorStatus(int vendorId);
	 List<VendorEditedDetail> findVendorEditedDetailByVendorId(Integer vendorId);

	public List<VendorRegistrationRequestDetail> findInitiatedRequestsByName(String name , String type);

	public boolean findDuplicateVendorName(String vendorName,String vendorType, String city , String state);
}
