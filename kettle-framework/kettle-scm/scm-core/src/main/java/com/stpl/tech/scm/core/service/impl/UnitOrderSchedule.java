package com.stpl.tech.scm.core.service.impl;

public class UnitOrderSchedule {

    private Integer id;
    private String orderingDayType;
    private Boolean functional;
    private Boolean orderingDay;
    private Integer orderingDays;

    public UnitOrderSchedule() {
    }

    public UnitOrderSchedule(Integer id, String orderingDayType, Integer orderingDays) {
        this.id = id;
        this.orderingDayType = orderingDayType;
        this.orderingDays = orderingDays;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderingDayType() {
        return orderingDayType;
    }

    public void setOrderingDayType(String orderingDayType) {
        this.orderingDayType = orderingDayType;
    }

    public Boolean getFunctional() {
        return functional;
    }

    public void setFunctional(Boolean functional) {
        this.functional = functional;
    }

    public Boolean getOrderingDay() {
        return orderingDay;
    }

    public void setOrderingDay(Boolean orderingDay) {
        this.orderingDay = orderingDay;
    }

    public Integer getOrderingDays() {
        return orderingDays;
    }

    public void setOrderingDays(Integer orderingDays) {
        this.orderingDays = orderingDays;
    }
}
