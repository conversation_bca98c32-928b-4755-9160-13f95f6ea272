/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@Table(name = "PAYMENTS_BLOCK_UN_BLOCK_BY_COMPLIANCE_LOG")
public class PaymentsBlockUnBlockByComplianceLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENTS_BLOCK_UN_BLOCK_BY_COMPLIANCE_LOG_ID",nullable = false, unique = true)
    private Integer paymentsBlockUnBlockByComplianceLogId;

    @Column(name = "VENDOR_ID")
    private Integer vendorId;

    @Column(name = "ACTION")
    private String action;

    @Column(name = "LOGGED_AT")
    private Date loggedAt;

    @Column(name = "REASON")
    private String reason;
}
