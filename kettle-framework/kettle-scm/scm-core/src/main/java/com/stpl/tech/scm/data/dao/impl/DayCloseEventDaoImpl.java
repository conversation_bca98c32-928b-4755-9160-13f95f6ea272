package com.stpl.tech.scm.data.dao.impl;


import java.util.Date;
import java.util.List;

import javax.net.ssl.SSLEngineResult.Status;
import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.scm.data.dao.DayCloseEventDao;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventType;

@Repository
public class DayCloseEventDaoImpl extends SCMAbstractDaoImpl implements DayCloseEventDao {

	@Override
	public SCMDayCloseEventData getDayCloseDataOfUnit(int unitId, StockEventType dayCloseEventType) {
			Query query = manager.createQuery(
					"SELECT E FROM SCMDayCloseEventData E where E.unitId = :unitId and E.status = :status and E.dayCloseEventType = :dayCloseEventType ORDER BY E.eventId desc");
			query.setParameter("unitId", unitId).setParameter("status", StockEventStatus.CLOSED.toString()).setParameter("dayCloseEventType", dayCloseEventType.toString());
			return (SCMDayCloseEventData) query.getResultList().get(0);
	}
	
}
