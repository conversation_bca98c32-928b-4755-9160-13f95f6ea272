/**
 *
 */
package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.UnitBusinessType;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.util.model.SkuDataAndTaxData;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.data.transport.model.VendorOTPValidationDomain;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.PriceStatus;
import com.stpl.tech.scm.domain.model.SkuData;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.SkuPriceUpdateDetail;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.VendorContract;
import com.stpl.tech.scm.domain.model.VendorContractVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public interface SkuMappingService {

	public boolean updatePrices(SkuPriceUpdate data) throws SumoException;

	public List<IdCodeNameStatus> searchSkuMappingsForUnit(int unitId);

	public List<IdCodeNameStatus> searchUnitMappingsForSku(int skuId);

	public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds);

	public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds);

    public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds, Map<Integer, String> inventoryListwithMappedSkus,
											Map<Integer,String> productionUnitMapped, Map<Integer, String> mappedPackagingIds , Map<Integer,String> mappedTaxCodes,
											Map<Integer,Date> mappedVoDiscontinuedFrom, Map<Integer,Date> mappedRoDiscontinuedFrom);

    public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds, Map<Integer, String> mappedInventoryWithSkuId,Map<Integer,String> productionUnitMapped,
											Map<Integer, String> mappedPackagingIds , Map<Integer,String> mappedTaxCodes,
											Map<Integer,Date> mappedVoDiscontinuedFrom, Map<Integer,Date> mappedRoDiscontinuedFrom);

    public boolean updateSkuProfiles(Map<Integer, String> skuListWithInventoryListId, int unitId, String profiles);

    public List<IdCodeNameStatus> searchSkuMappingsForVendor(int vendorId);

	public List<IdCodeNameStatus> searchVendorMappingsForSku(int skuId);

	public List<IdCodeNameStatus> searchVendorMappingsForBusiness(int businessId);

	public boolean updateVendorSkuMapping(int employeeId, String name, int vendorId, int skuId, String status);

	public List<IdCodeNameStatus> allUnits();

	public List<IdCodeNameStatus> allSKU();

	public List<IdCodeNameStatus> allVendors();


	public List<IdCodeName> getBusinessTypes();

	public List<unitSkuMappingDetail> getSkusProfileForUnit(int unit, List<Integer> sku);

	/**
	 * @param employeeId
	 * @param employeeName
	 * @param id
	 * @param skuIds
	 * @return
	 */
	public boolean addSkuMappingsForVendor(int employeeId, String employeeName, int id, List<IdCodeName> skuIds);

	//@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addSkuMappingsForVendorAlias(IdCodeName request) throws SumoException;

	/**
	 * @param employeeId
	 * @param employeeName
	 * @param id
	 * @param mappingIds
	 * @return
	 */
	public boolean addVendorMappingsForSku(int employeeId, String employeeName, int id, List<Integer> mappingIds);


	public boolean addVendorMappingsForBusiness(int employeeId, String employeeName, int skuId, List<Integer> vendorIds);

	/**
	 * @param skuId
	 * @param deliveryLocation
	 * @return
	 */
	public List<SkuPriceDetail> searchPricesBySku(int skuId, String deliveryLocation);

	public List<SkuPriceDetail> searchPricesByVendor(int vendorId, String deliveryLocation);

	/**
	 * @param data
	 * @return
	 */
	public boolean cancelPriceUpdate(SkuPriceUpdate data);

	/**
	 * @param data
	 * @return
	 */
	public boolean updatePriceStatus(SkuPriceUpdate data);

	public List<UnitVendorSkuMapping> searchSkuMappingsForVendorAndUnit(int unitId, int vendorId);

	public List<UnitVendorSkuMapping> updateSkuMappingsForVendorAndUnit(UpdateUnitVendorSkuMapping data);

	public List<SkuDataAndTaxData> getSkuAndTaxData(int dispatchId,int unitId,int vendorId , Boolean toPickFromUnitMapping);

	public List<SkuPriceDetail> getSkuPackagingPriceForVendorsForUnit(int unitId, Set<Integer> vendorIds, String deliveryLocationCode);

	public boolean addPrice(SkuPriceUpdate data);

	public Collection<VendorDetail> searchVendorsForUnit(int unitId);

	public Collection<IdCodeName> searchVendorsForUnitTrimmed(int unitId);

    public Collection<SkuData> getSkusMappedToVendor(int unitId, int vendorId);

	public List<SkuPriceDetail> getSkuPackagingPriceForUnit(int unitId, String deliveryLocationCode);

    public List<IdCodeNameStatus> unitsByBusinessType(UnitBusinessType businessType);

	public List<Integer> getSubCategories(List<Integer> profiles);

	public List<String> getUnitDistanceMapping(int firstUnitId,int secondUnitId);

	public boolean updateMappingsForUnit(int firstUnitId, Integer firstMappingId, BigDecimal firstDistance, int secondUnitId,
			Integer secondMappingId, BigDecimal secondDistance) throws SumoException;

	public boolean updateLeadTime(IdIndex data);

	public boolean updateSkuLeadTime(int vendorId,int leadTime);

	public Map<Integer,Integer> getSibLingSkusInventoryListId(List<Integer> unitIds , Integer skuId);

	public Map<Integer, SkuPackagingTaxMapping> getAllUnitSkuPackagingTaxMappingByStatus(Integer skuId , Integer packagingId , List<String> statuses);

	public Boolean updateUnitSkuPackagingTaxMapping(Integer skuId, Integer packagingId, Map<Integer, String> unitToTaxMap, Integer employeeId);

	public String getHsnCodeFromUnitSkuMapping(SkuDefinition sku , Integer unitId);


	Boolean converUnitDistanceToZipCodeDistance() throws SumoException;

    boolean generateSkuPriceUpdateRequest(List<SkuPriceUpdate> data) throws SumoException;

	List<SkuPriceDetail> getVendorPriceChange(int vendorId, String location);

	boolean processPriceRequestForVendor(SkuPriceUpdateDetail data);

    List<SkuPriceDetail> previewVendorPriceChange(Integer vendorId);

	boolean saveVendorPriceChange(VendorContract vendorContract) throws SumoException;

	List<VendorContractVO> getVendorContract(Integer vendorId, PriceStatus status, Date startDate, Date endDate, Integer vendorContractId);

	boolean cancelVendorContract(VendorContract vendorContractId) throws SumoException;

    DocumentDetail generateContractUsingTemplate(Integer vendorContractId, Integer empCode, Integer templateId, List<VendorContractInfo> vendorContractInfos) throws SumoException, TemplateRenderingException;

	boolean mapVendorContractWithDocument(VendorContract vendorContract) throws SumoException;

	String getContractDocument(Integer documentId) throws SumoException;

    boolean triggerVendorContractMail(VendorContract vendorContractId) throws SumoException, EmailGenerationException;

	boolean vendorAcceptance(VendorContract vendorContract) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException;

	boolean contractApplied(VendorContract vendorContract) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException;

	void applyContract();

    boolean triggerEmailOtpForVendor(Integer vendorId) throws SumoException;
    boolean triggerEmailOtpForEmployee(Integer employeeId) throws SumoException;

	boolean validateVendorOtp(VendorOTPValidationDomain otp) throws SumoException;

	VendorContractVO validateRequest(String token) throws UnsupportedEncodingException, VendorRegistrationException, SumoException;

	DocumentDetail saveDigitalSignature(MultipartFile file, Integer vendorContractId) throws SumoException;

	void expiryContract();
}

