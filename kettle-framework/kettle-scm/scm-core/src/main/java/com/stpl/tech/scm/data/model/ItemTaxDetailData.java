package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-05-2017.
 */

@Entity
@Table(name = "ITEM_TAX_DETAIL_DATA")
public class ItemTaxDetailData {

    private Integer taxDetailId;
    private PurchaseOrderItemData purchaseItem;
    private VendorGoodsReceivedItemData grItem;
    private String taxType;
    private BigDecimal taxPercentage;
    private BigDecimal taxValue;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ITEM_TAX_DETAIL_ID", unique = true, nullable = false)
    public Integer getTaxDetailId() {
        return taxDetailId;
    }

    public void setTaxDetailId(Integer taxDetailId) {
        this.taxDetailId = taxDetailId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "GR_ITEM_ID", nullable = true)
    public VendorGoodsReceivedItemData getGrItem() {
        return grItem;
    }

    public void setGrItem(VendorGoodsReceivedItemData grItem) {
        this.grItem = grItem;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PO_ITEM_ID", nullable = true)
    public PurchaseOrderItemData getPurchaseItem() {
        return purchaseItem;
    }

    public void setPurchaseItem(PurchaseOrderItemData purchaseItem) {
        this.purchaseItem = purchaseItem;
    }

    @Column(name = "TAX_TYPE", nullable = false)
    public String getTaxType() {
        return taxType;
    }

    public void setTaxType(String taxType) {
        this.taxType = taxType;
    }

    @Column(name = "TAX_PERCENTAGE", nullable = false)
    public BigDecimal getTaxPercentage() {
        return taxPercentage;
    }

    public void setTaxPercentage(BigDecimal taxPercentage) {
        this.taxPercentage = taxPercentage;
    }

    @Column(name = "TAX_VALUE", nullable = false)
    public BigDecimal getTaxValue() {
        return taxValue;
    }

    public void setTaxValue(BigDecimal taxValue) {
        this.taxValue = taxValue;
    }

}
