package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.DispatchEWayRequestTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

/**
 * Created by Chaayos on 31-03-2018.
 */
public class DispatchEWayRequestNotification extends EmailNotification {

	private DispatchEWayRequestTemplate template;
	private EnvType envType;
	private Integer dispatchId;

	public DispatchEWayRequestNotification() {

	}

	public DispatchEWayRequestNotification(DispatchEWayRequestTemplate template, EnvType envType,
			Integer dispatchId) {
		this.template = template;
		this.envType = envType;
		this.dispatchId = dispatchId;
	}

	@Override
	public String[] getToEmails() {

		return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } : new String[] { "<EMAIL>" };
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subject = "Eway Request for Dispatch - " + dispatchId + " on "
				+ SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
		if (SCMUtil.isDev(envType)) {
			subject = "Dev " + subject;
		}
		return subject;
	}

	public String body() throws EmailGenerationException {
		try {
			return template.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}
