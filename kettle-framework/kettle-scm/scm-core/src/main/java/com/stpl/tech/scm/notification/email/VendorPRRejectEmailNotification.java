package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.VendorPRRejectEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorPRRejectEmailNotification extends EmailNotification {

	private VendorPRRejectEmailNotificationTemplate vendorPRRejectEmailNotificationTemplate;
	private EnvType envType;
	private String[] emails;

	public VendorPRRejectEmailNotification() {

	}

	public VendorPRRejectEmailNotification(VendorPRRejectEmailNotificationTemplate vendorPRRejectEmailNotificationTemplate,
                                           EnvType envType, String[] emails) {
		this.vendorPRRejectEmailNotificationTemplate = vendorPRRejectEmailNotificationTemplate;
		this.envType = envType;
		this.emails = emails;
	}

	@Override
	public String[] getToEmails() {
		List<String> mails = new ArrayList<>();
		Arrays.asList(emails).forEach(email -> {
			mails.add(email);
		});
		String[] simpleArray = new String[mails.size()];
		return SCMUtil.isDev(envType) ? new String[] { "<EMAIL>" } : mails.toArray(simpleArray);
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subject;
		if (Objects.nonNull(vendorPRRejectEmailNotificationTemplate.getUnitBasicDetail())) {
			subject = String.format("Payment Request(%s) #" + vendorPRRejectEmailNotificationTemplate.getPaymentRequest().getPaymentRequestId() + " rejected "
							+ SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy"), vendorPRRejectEmailNotificationTemplate.getUnitBasicDetail().getName());
		}
		else {
			subject = "Payment Request #"+vendorPRRejectEmailNotificationTemplate.getPaymentRequest().getPaymentRequestId()+" rejected "
					+ SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
		}
		if (SCMUtil.isDev(envType)) {
			subject = "Dev " + subject;
		}
		return subject;
	}

	@Override
	public String body() throws EmailGenerationException {
		try {
			return vendorPRRejectEmailNotificationTemplate.getContent();
		} catch (TemplateRenderingException e) {
			throw new EmailGenerationException("Failed to render the template", e);
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}
}
