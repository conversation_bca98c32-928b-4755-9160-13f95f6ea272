package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.scm.core.service.MappingCacheService;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.model.ProductionUnitData;

import java.util.ArrayList;
import java.util.List;

@Service
public class MappingCacheServiceImpl implements MappingCacheService {

	@Autowired
	private SkuMappingDao dao;

	@Autowired
	private MasterDataCache masterDataCache;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Integer findProductionLine(int unitId, int skuId) {

		return dao.findProductionLine(unitId, skuId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public int findSKUID(int productId) {
		return dao.findSKUID(productId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public ProductionUnitData findProductionLine(int productIonUnitId) {
		return dao.findProductionLine(productIonUnitId);
	}

	@Override
	public List<SkuDefinition> findAllSkuDefinition(int productId) {
		List<SkuDefinitionData>sdd=dao.findAllSkuDefinition(productId);
		List<SkuDefinition> listOfSkuData=new ArrayList<>();
		for(SkuDefinitionData data:sdd){
			IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "",
				masterDataCache.getEmployees().get(data.getCreatedBy()));
			listOfSkuData.add(SCMDataConverter.convert(data, createdBy));
		}
		return listOfSkuData;
	}

}
