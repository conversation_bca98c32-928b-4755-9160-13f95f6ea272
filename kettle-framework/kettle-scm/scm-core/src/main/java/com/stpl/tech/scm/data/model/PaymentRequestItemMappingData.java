package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "PAYMENT_REQUEST_ITEM_MAPPING")
public class PaymentRequestItemMappingData {

    private Integer id;
    private Integer paymentRequestId;
    private String paymentRequestType;
    private Integer paymentRequestItemId;
    private Integer linkedPaymentRequestId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_REQUEST_ITEM_MAPPING_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "PAYMENT_REQUEST_ID", nullable = false)
    public Integer getPaymentRequestId() {
        return paymentRequestId;
    }

    public void setPaymentRequestId(Integer paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }

    @Column(name = "PAYMENT_REQUEST_TYPE", nullable = false)
    public String getPaymentRequestType() {
        return paymentRequestType;
    }

    public void setPaymentRequestType(String paymentRequestType) {
        this.paymentRequestType = paymentRequestType;
    }

    @Column(name = "PAYMENT_REQUEST_ITEM_ID", nullable = false)
    public Integer getPaymentRequestItemId() {
        return paymentRequestItemId;
    }

    public void setPaymentRequestItemId(Integer paymentRequestItemId) {
        this.paymentRequestItemId = paymentRequestItemId;
    }

    @Column(name = "LINKED_PAYMENT_REQUEST_ID")
    public Integer getLinkedPaymentRequestId() {
        return linkedPaymentRequestId;
    }

    public void setLinkedPaymentRequestId(Integer linkedPaymentRequestId) {
        this.linkedPaymentRequestId = linkedPaymentRequestId;
    }
}
