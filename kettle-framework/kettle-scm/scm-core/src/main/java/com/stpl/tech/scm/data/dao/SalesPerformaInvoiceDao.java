package com.stpl.tech.scm.data.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.data.model.OutwardRegisterData;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteDetail;
import com.stpl.tech.scm.data.model.SalesPerformaInvoiceCreditDebitNoteItemDetail;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 03-07-2018.
 */
public interface SalesPerformaInvoiceDao extends SCMAbstractDao{

    public List<SalesPerformaDetailData> getInvoices(Integer sendingUnit, Date startDate, Date endDate,
                                                     List<String> status, Integer vendorId, Integer dispatchId,String businessType , String trnsType);

    public int getNextInvoiceId(int vendorId, String type, Integer financialYear);

    public SalesPerformaDetailData findByGeneratedId(String generatedId);

    public List<Integer> getSubmittedInvoices(List<Integer> invoiceIdList);

    List<OutwardRegisterData> getOutWardRegisterEntry(Integer sendingUnit, Date startDate, Date endDate ,String businessType);

    public List<Integer> getOldInvoiceIds(Date endDate, List<String> status);

    public int getNextInvoiceId(Integer vendorId, String name);

    public List<SalesPerformaInvoiceCreditDebitNoteDetail> getCreditDebitNoteDetails(Date startDate, Date endDate, String status ,Integer vendorId);

    public List<SalesPerformaInvoiceCreditDebitNoteItemDetail> getCreditDebitNoteDetails(Integer id);

    public List<String> getUsedInvoiceIds();
}
