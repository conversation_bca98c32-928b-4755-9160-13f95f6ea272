package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "GATEPASS_TAX_DETAIL")
public class GatepassTaxDetail {
	private Integer id;
	private GatepassItemData itemData;
	private String taxType;
	private String taxCode;
	private BigDecimal taxPercentage;
	private BigDecimal taxAmount;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "TAX_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "GATEPASS_ITEM_ID", nullable = true)
	public GatepassItemData getItemData() {
		return itemData;
	}

	public void setItemData(GatepassItemData itemData) {
		this.itemData = itemData;
	}

	@Column(name = "TAX_TYPE", nullable = false)
	public String getTaxType() {
		return taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

	@Column(name = "TAX_CODE", nullable = false)
	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@Column(name = "TAX_PERCENTAGE", nullable = false)
	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

	@Column(name = "TAX_AMOUNT", nullable = false)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

}
