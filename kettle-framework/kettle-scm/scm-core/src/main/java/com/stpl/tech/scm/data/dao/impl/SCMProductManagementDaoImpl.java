package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.data.dao.SCMProductManagementDao;
import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.ProductFulfillmentTypeData;
import com.stpl.tech.scm.data.model.ProductPackagingMappingData;
import com.stpl.tech.scm.data.model.SkuAttributeValueData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingMappingData;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
@Repository
public class SCMProductManagementDaoImpl extends SCMAbstractDaoImpl implements SCMProductManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(SCMProductManagementDaoImpl.class);

    @Override
    public List<ProductFulfillmentTypeData> getProductFulfillmentTypes(int productId) {
        Query query = manager
                .createQuery("FROM ProductFulfillmentTypeData p WHERE p.productDefinitionData.productId = :productId");
        query.setParameter("productId", productId);
        return query.getResultList();
    }

    @Override
    public List<ProductPackagingMappingData> getPackagingMappingsForProduct(int productId) {
        Query query = manager.createQuery("FROM ProductPackagingMappingData p WHERE p.productId = :productId");
        query.setParameter("productId", productId);
        return query.getResultList();
    }

    @Override
    public List<SkuDefinitionData> getSkuAgainstProduct(int productId) {
        Query query = manager.createQuery("FROM SkuDefinitionData p WHERE p.linkedProduct.productId = :productId");
        query.setParameter("productId", productId);
        return query.getResultList();
    }

    @Override
    public List<SkuPackagingMappingData> getPackagingMappingsForSku(int skuId) {
        Query query = manager.createQuery("FROM SkuPackagingMappingData p WHERE p.skuId = :skuId");
        query.setParameter("skuId", skuId);
        return query.getResultList();
    }

    @Override
    public SkuAttributeValueData fetchSkuAttributeByType(int skuId, int attributeId) {
        Query query = manager
                .createQuery("FROM SkuAttributeValueData p WHERE p.skuId = :skuId AND p.attributeId = :attributeId");
        query.setParameter("skuId", skuId);
        query.setParameter("attributeId", attributeId);
        List<SkuAttributeValueData> list = query.getResultList();
        if (list != null & !list.isEmpty()) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<UnitSkuVendorMapping> getUnitSkuVendorMappingsByUnitId(int unitId) {
        Query query = manager
                .createQuery("FROM UnitSkuVendorMapping u WHERE u.unitSkuMapping.unitId = :unitId" +
                        " AND u.unitSkuMapping.mappingStatus = :mappingStatus AND u.mappingStatus = :mappingStatus");
        query.setParameter("unitId", unitId);
        query.setParameter("mappingStatus", SwitchStatus.ACTIVE.value());
        return query.getResultList();
    }

    @Override
    public void updateIsDefaultFlag(Integer skuId, Integer productId) {
        Query query = manager.createQuery("UPDATE SkuDefinitionData E SET E.isDefault = :notDefault" +
                " where E.linkedProduct.productId = :productId and E.skuId != :skuId");
        query.setParameter("productId", productId);
        query.setParameter("notDefault", SCMServiceConstants.SCM_CONSTANT_NO);
        query.setParameter("skuId", skuId);
        query.executeUpdate();
        manager.flush();
    }

    @Override
    public PackagingDefinitionData findByPackagingTypeAndPackagingCode(PackagingType packagingType, String packagingCode) {
        Query query = manager
                .createQuery("FROM PackagingDefinitionData u WHERE u.packagingType = :packagingType" +
                        " AND u.packagingCode = :packagingCode");
        query.setParameter("packagingCode", packagingCode);
        query.setParameter("packagingType", packagingType.name());
        try {
            return (PackagingDefinitionData) query.getSingleResult();
        } catch (NoResultException e) {
            return null;
        }
    }

    @Override
    public List<UnitProductPackagingMapping> findPackagingByUnitId(int unitId) {
        List<UnitProductPackagingMapping> unitProductPackagingMappingList = new ArrayList<>();
        Query query = manager
            .createNativeQuery("select pd.PRODUCT_ID, fum.FULFILLING_UNIT_ID, pft.FULFILLMENT_TYPE," +
                "max(usm.PACKAGING_ID)" +
                "from UNIT_SKU_MAPPING usm, SKU_DEFINITION sku, PRODUCT_DEFINITION pd, PRODUCT_FULFILLMENT_TYPE_DATA pft, FULFILLMENT_UNIT_MAPPING fum" +
                " where fum.REQUESTING_UNIT_ID = :unitId AND fum.MAPPING_STATUS = 'ACTIVE'" +
                " and fum.FULFILLMENT_TYPE =pft.FULFILLMENT_TYPE" +
                " and pft.PRODUCT_DEFINITION_ID = pd.PRODUCT_ID and pd.PRODUCT_ID = sku.LINKED_PRODUCT_ID" +
                " and sku.SKU_ID = usm.SKU_ID" +
                " and usm.MAPPING_STATUS = 'ACTIVE'" +
                "and sku.SKU_STATUS = 'ACTIVE'" +
                "and pd.PRODUCT_STATUS = 'ACTIVE'" +
                "and usm.PACKAGING_ID IS NOT NULL" +
                " and pft.PRODUCT_FULFILLMENT_TYPE_STATUS = 'ACTIVE'" +
                "and usm.UNIT_ID = fum.FULFILLING_UNIT_ID" +
                " group by pd.PRODUCT_ID");
        query.setParameter("unitId", unitId);
        try {
            List<Object[]> list = query.getResultList();
            if (list != null) {
                for (Object[] obj : list) {
                    UnitProductPackagingMapping unitProductPackagingMapping = new UnitProductPackagingMapping();
                    unitProductPackagingMapping.setProductId((Integer) obj[0]);
                    unitProductPackagingMapping.setFulfillmentUnitId((Integer) obj[1]);
                    unitProductPackagingMapping.setFulfillmentType(FulfillmentType.fromValue((String) obj[2]));
                    unitProductPackagingMapping.setPackagingId((Integer) obj[3]);
                    unitProductPackagingMappingList.add(unitProductPackagingMapping);
                }
            }
            return unitProductPackagingMappingList;
        }catch (Exception e){
            return null;
        }
    }

    @Override
    public List<Object[]> getUnitSkuPackagingMappings(int unitId) {
        try{
            Query query = manager.createNativeQuery("SELECT pd.PRODUCT_ID, max(usm.PACKAGING_ID) FROM UNIT_SKU_MAPPING usm,SKU_DEFINITION sku," +
                    "PRODUCT_DEFINITION pd " +
                    "WHERE usm.UNIT_ID = :unitId AND usm.MAPPING_STATUS = 'ACTIVE'" +
                    "AND pd.PRODUCT_ID = sku.LINKED_PRODUCT_ID " +
                    "AND sku.SKU_ID = usm.SKU_ID " +
                    "AND sku.SKU_STATUS = 'ACTIVE' " +
                    "AND pd.PRODUCT_STATUS = 'ACTIVE' " +
                    "AND usm.PACKAGING_ID IS NOT NULL" +
                    " GROUP BY pd.PRODUCT_ID");
            query.setParameter("unitId", unitId);
            List<Object[]> list = query.getResultList();
            return list;
        }
        catch (Exception e){
            LOG.error("Exception occurred while fetching data from database :: ",e);
            return null;
        }
    }

}
