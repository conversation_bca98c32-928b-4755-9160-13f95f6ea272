/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "ADVANCE_PAYMENT_STATUS_LOGS")
public class AdvancePaymentStatusLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ADVANCE_PAYMENT_STATUS_LOG_ID", nullable = false, unique = true)
    private Integer advancePaymentStatusLogId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ADVANCE_PAYMENT_ID", nullable = false)
    private AdvancePaymentData advancePaymentData;

    @Column(name = "FROM_STATUS", nullable = false)
    private String fromStatus;

    @Column(name = "TO_STATUS", nullable = false)
    private String toStatus;

    @Column(name = "LOGGED_BY", nullable = false)
    private Integer loggedBy;

    @Column(name = "LOGGED_AT", nullable = true)
    private Date loggedAt;

    public Integer getAdvancePaymentStatusLogId() {
        return this.advancePaymentStatusLogId;
    }

    public void setAdvancePaymentStatusLogId(Integer advancePaymentStatusLogId) {
        this.advancePaymentStatusLogId = advancePaymentStatusLogId;
    }

    public AdvancePaymentData getAdvancePaymentData() {
        return advancePaymentData;
    }

    public void setAdvancePaymentData(AdvancePaymentData advancePaymentData) {
        this.advancePaymentData = advancePaymentData;
    }

    public String getFromStatus() {
        return this.fromStatus;
    }

    public void setFromStatus(String fromStatus) {
        this.fromStatus = fromStatus;
    }

    public String getToStatus() {
        return this.toStatus;
    }

    public void setToStatus(String toStatus) {
        this.toStatus = toStatus;
    }

    public Integer getLoggedBy() {
        return loggedBy;
    }

    public void setLoggedBy(Integer loggedBy) {
        this.loggedBy = loggedBy;
    }

    public Date getLoggedAt() {
        return loggedAt;
    }

    public void setLoggedAt(Date loggedAt) {
        this.loggedAt = loggedAt;
    }
}
