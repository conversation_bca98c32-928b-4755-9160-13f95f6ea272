package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.PendingTransferOrder;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.TaxDetail;
import com.stpl.tech.scm.domain.model.TransferredAsset;
import com.stpl.tech.util.EnvType;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON> on 22-06-2016.
 */
public interface TransferOrderManagementDao extends SCMAbstractDao {

    public List<TransferOrderData> findTransferOrders(Integer fulfillingUnitId, Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer transferOrderId);

    BigInteger getTransferOrdersCount();

    public List<PendingTransferOrder> findPendingTransferOrders(Integer selectedStateCode, Integer fulfillingUnitId, Integer requestingUnitId, Date startDate, Date endDate, EnvType env);

    List<PendingTransferOrder> findPartiallyCompleteTransferOrders(Integer selectedStateCode, Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, EnvType env);

    public List<TransferOrderData> incrementalTransferOrders(Integer fulfillingUnitId, Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer transferOrderId, String ids);

    public GoodsReceivedData getGRFromTransferOrder(int transferOrderId);

    /**
     * @param detail
     * @param taxes
     */
    void setTaxDetail(boolean applyTaxes, TransferOrderData detail, List<TaxDetail> taxes);

    /**
     * @param orderId
     * @param stateId
     * @param detail
     * @param item
     * @param invoices
     */
	/*
	 * void setTaxDetail(boolean applyTaxes, int orderId, int stateId,
	 * TransferOrderItemData detail, TransferOrderItem item, Map<String,
	 * TransferOrderItemInvoice> invoices);
	 */

    /**
     * @param stateId
     * @param type
     * @return
     */
    int getNextStateInvoiceId(int stateId, String type);

    int getNextStateInvoiceId(int stateId, String type, Integer financialYear);

    public TransferOrderData findByRequestOrderId(Integer requestOrderId);

    public String getEwayBill(int transferOrderId);

    public boolean getAlreadyTransferredRO(List<Integer> roIds);

    List<TransferOrderItemData> findByTransferId(Integer transferOrderId);

    public TransferOrderData findByRequestOrderIds(List<Integer> requestOrderIds);

    public List<BulkTransferEventData> findBulkEventByDates(Integer unitId, Date startDate ,
                                                            Date endDate ,
                                                            Integer bulkEventId , Boolean isShort);

    public List<TransferOrderData> findByTransferIds(List<Integer> toIds);

    public Map<String, List<TransferredAsset>> findAssetTransfersByUnitId(Integer unitId);

    public List<TransferOrderAssetCorrectionData> findAssetCorrectionDataByIds(List<Integer> toItemIds);

    public List<GoodsReceivedData> findGRDataByToIds(List<Integer> toIds);

    public List<Integer> filterGrData(List<Integer> grIds);

    public Optional<List<MultiPackagingAdjustmentsData>> getAdjustedPackaging(List<Integer> roIds);

    public Boolean checkIfUnitIsExcludedForDayCloseCheck(Integer unitId);

    public TransferOrderData findTransfersByGeneratedId(String invoiceId);

    public TransferOrderEInvoice findEInvoiceByTOID(Integer toId);
}
