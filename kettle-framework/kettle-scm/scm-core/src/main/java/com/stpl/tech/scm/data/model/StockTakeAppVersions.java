package com.stpl.tech.scm.data.model;

import javax.persistence.*;

@Entity
@Table(name = "STOCK_TAKE_APP_VERSIONS")
public class StockTakeAppVersions {
    @Id
    @Column(name = "ID")
    private Integer id;

    @Column(name = "VERSION_NO")
    private String versionNo;

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getVersionNo() {
        return this.versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }
}
