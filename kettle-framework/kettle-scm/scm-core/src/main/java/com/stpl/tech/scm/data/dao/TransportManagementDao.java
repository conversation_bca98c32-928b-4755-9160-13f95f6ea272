package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.EwayBillData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.VehicleDispatchData;
import com.stpl.tech.scm.domain.model.*;

import java.util.Date;
import java.util.List;

public interface TransportManagementDao extends SCMAbstractDao {

	public List<Integer> getTransferOrdersForEway(List<Integer> unitIds, Date startDate, Date endDate, List<Integer> receivingUnitIds);

	public List<Integer> getProcessedTransferOrders(List<Integer> availableTO);

	public List<TransferOrder> findAllTransferOrders(List<Integer> availableTO);

	public List<TransferOrderData> findAllTransferOrdersData(List<Integer> toList);

	public VehicleDispatchData findDispatchData(Integer vehicleId, Date d);

	public boolean createConsignment(Integer dispatchId, List<Integer> toList) throws SumoException;

	public void updateEwayData(List<EWayResponse> entityList);

	public void updateEWayStatus(List<EwayBillData> events, EWayBillStatus prepared);

	public void checkAndMarkCompleteDispatch(List<EWayResponse> list);

	public VehicleDispatch getDispatchData(Integer dispatchId);

	public List<VehicleDispatchData> getVehicleDispatchHistory(Integer vehicleId, Date startDate, Date endDate, String registrationNumber);

	public void updateConsignmentStatus(Integer dispatchId, DispatchStatus settled);

	public boolean cancelConsignment(Integer consignmentId) throws SumoException;

	public boolean cancelEWay(Integer ewayId);

    public List<Integer> findVehicleDataByDates(Date date);

	public List<Integer> getToListOfVehicle(Integer vehicleId,Date dates);

	public List<TransferOrderData> getTODataByIds(List<Integer> toIds);
}
