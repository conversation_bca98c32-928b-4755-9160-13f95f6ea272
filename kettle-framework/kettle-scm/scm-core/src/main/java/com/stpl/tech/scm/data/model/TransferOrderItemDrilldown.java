/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;


@SuppressWarnings("serial")
@Entity
@Table(name = "TRANSFER_ORDER_ITEM_DRILLDOWN")
public class TransferOrderItemDrilldown implements java.io.Serializable{

	private Integer transferOrderItemDrilldownId;
	private TransferOrderItemData orderItem;
	private BigDecimal quantity;
	private BigDecimal price;
	private Date addTime;
	private Date expiryDate;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TRANSFER_ORDER_ITEM_DRILLDOWN_ID", unique = true, nullable = false)
	public Integer getTransferOrderItemDrilldownId() {
		return transferOrderItemDrilldownId;
	}

	public void setTransferOrderItemDrilldownId(Integer transferOrderItemDrilldownId) {
		this.transferOrderItemDrilldownId = transferOrderItemDrilldownId;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TRANSFER_ORDER_ITEM_ID", nullable = true)
	public TransferOrderItemData getOrderItem() {
		return orderItem;
	}

	public void setOrderItem(TransferOrderItemData orderItem) {
		this.orderItem = orderItem;
	}
	
	@Column(name = "QUANTITY", precision = 16)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}
	@Column(name = "PRICE", precision = 16)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal cost) {
		this.price = cost;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXPIRY_DATE", nullable = false, length = 10)
	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

}