package com.stpl.tech.scm.data.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "CAPEX_REQUEST_STATUS_LOG")
public class CapexRequestStatusLog implements Serializable {

	
	private Integer id;
	private String fromStatus;
	private String toStatus;
	private Integer updatedBy;
	private Date updateTime;
	private Integer capexRequestId;
	private Integer capexAuditId;
	private String remarks;
	private String status;
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CAPEX_REQUEST_STATUS_LOG_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "FROM_STATUS", nullable = false)
    public String getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(String fromStatus) {
        this.fromStatus = fromStatus;
    }

    @Column(name = "TO_STATUS", nullable = false)
    public String getToStatus() {
        return toStatus;
    }

    public void setToStatus(String toStatus) {
        this.toStatus = toStatus;
    }

    @Column(name = "UPDATED_BY", nullable = false)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "UPDATE_TIME", nullable = false)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Column(name = "REMARKS")
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Column(name = "CAPEX_REQUEST_ID")
	public Integer getCapexRequestId() {
		return capexRequestId;
	}

	public void setCapexRequestId(Integer capexRequestId) {
		this.capexRequestId = capexRequestId;
	}

	@Column(name = "CAPEX_AUDIT_ID")
	public Integer getCapexAuditId() {
		return capexAuditId;
	}

	public void setCapexAuditId(Integer capexAuditId) {
		this.capexAuditId = capexAuditId;
	}

	@Column(name = "STATUS")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	
	
    
}
