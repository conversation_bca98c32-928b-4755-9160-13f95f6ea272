/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "WASTAGE_ITEM_DATA")
public class SCMWastageData {
	private Integer wastageItemId;
	private SCMWastageEventData wastage;
	private ProductDefinitionData product;
	private BigDecimal quantity;
	private SkuDefinitionData sku;
	private BigDecimal price;
	private BigDecimal cost;
	private String comment;
	private List<WastageDataDrilldown> items = new ArrayList<>(0);
	private String taxType;
	private BigDecimal taxPercentage;
	private BigDecimal tax;
	private Integer productionId;
	private String enteredComment;

    public SCMWastageData() {
    }

    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "WASTAGE_ITEM_ID", unique = true, nullable = false)
	public Integer getWastageItemId() {
		return wastageItemId;
	}

	public void setWastageItemId(Integer wastageItemId) {
		this.wastageItemId = wastageItemId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "WASTAGE_ID", nullable = false)
	public SCMWastageEventData getWastage() {
		return wastage;
	}

	public void setWastage(SCMWastageEventData wastage) {
		this.wastage = wastage;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCT_ID")
	public ProductDefinitionData getProduct() {
		return product;
	}

	public void setProduct(ProductDefinitionData product) {
		this.product = product;
	}

	@Column(name = "QUANTITY", nullable = false)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SKU_ID", nullable = true)
	public SkuDefinitionData getSku() {
		return sku;
	}

	public void setSku(SkuDefinitionData skuId) {
		this.sku = skuId;
	}

	@Column(name = "PRICE", nullable = true)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Column(name = "COST", nullable = true)
	public BigDecimal getCost() {
		return cost;
	}

	public void setCost(BigDecimal cost) {
		this.cost = cost;
	}

	@Column(name = "COMMENT", nullable = true)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "wastageData")
	public List<WastageDataDrilldown> getItems() {
		if(items == null) {
			items = new ArrayList<>();
		}
		return items;
	}

	public void setItems(List<WastageDataDrilldown> items) {
		this.items = items;
	}

    @Column(name = "TAX_TYPE", nullable = true)
	public String getTaxType() {
		return taxType;
	}

	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}

    @Column(name = "TAX_PERCENTAGE", nullable = true)
	public BigDecimal getTaxPercentage() {
		return taxPercentage;
	}

	public void setTaxPercentage(BigDecimal taxPercentage) {
		this.taxPercentage = taxPercentage;
	}

    @Column(name = "TAX", nullable = true)
	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	@Column(name = "PRODUCTION_ID", nullable = true)
	public Integer getProductionId() {
		return productionId;
	}

	public void setProductionId(Integer productionId) {
		this.productionId = productionId;
	}

	@Column(name = "ENTERED_COMMENT", nullable = true)
	public String getEnteredComment() {
		return enteredComment;
	}

	public void setEnteredComment(String enteredComment) {
		this.enteredComment = enteredComment;
	}
}
