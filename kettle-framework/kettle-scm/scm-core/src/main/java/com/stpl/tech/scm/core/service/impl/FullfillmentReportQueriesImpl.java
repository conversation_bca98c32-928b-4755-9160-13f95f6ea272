package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.scm.core.service.FullfillmentReportQueries;
import com.stpl.tech.util.EnvType;
import org.springframework.stereotype.Service;

@Service
public class FullfillmentReportQueriesImpl implements FullfillmentReportQueries {
    @Override
    public String getFullfillmentQueryLastThirtyDay(EnvType envType) {
        if(envType==EnvType.PROD || envType==EnvType.SPROD){
            return "SELECT T.*, (T.FINAL_RECEIVED_QUANTITY / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 as FULLFILLMENT_PERCENTAGE, \n" +
                    "(CASE WHEN T.PRODUCT_ID IN (SELECT \n" +
                    "    umsm.SCM_PRODUCT_ID\n" +
                    "FROM\n" +
                    "    KETTLE_MASTER.UNIT_PRODUCT_MAPPING upm,\n" +
                    "    KETTLE_MASTER.UNIT_PRODUCT_PRICING upp,\n" +
                    "    KETTLE_MASTER.REF_LOOKUP rl,\n" +
                    "    KETTLE_MASTER.PRODUCT_DETAIL pd,\n" +
                    "    KETTLE_MASTER.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                    "WHERE\n" +
                    "    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                    "        AND umsm.INGREDIENT_TYPE = 'INGREDIENT'\n" +
                    "        AND umsm.PRODUCT_CLASSIFICATION = 'MENU'\n" +
                    "        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                    "        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                    "        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                    "        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                    "        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                    "        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                    "        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                    "        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                    "        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                    "        AND upm.UNIT_ID = T.REQUESTING_UNIT_ID) THEN 'Y' ELSE 'N' END) as IS_CRITICAL from\n" +
                    "(\n" +
                    "SELECT\n" +
                    "RO_ITEM_DETAILS.TRANSFR_UNIT as TRANSFERRING_UNIT,\n" +
                    "RO_ITEM_DETAILS.TRANSFR_UNIT_ID as TRANSFERRING_UNIT_ID,\n" +
                    "    RO_ITEM_DETAILS.REQ_UNIT as REQUESTING_UNIT,\n" +
                    "    RO_ITEM_DETAILS.REQ_UNIT_ID as REQUESTING_UNIT_ID,\n" +
                    "RO_ITEM_DETAILS.RO_ID REQUEST_ORDER_ID,\n" +
                    "RO_ITEM_DETAILS.TO_ID ,\n" +
                    "RO_ITEM_DETAILS.GR_ID,\n" +
                    "RO_ITEM_DETAILS.RO_ITEM_ID as REQUEST_ORDER_ITEM_ID,\n" +
                    "    RO_ITEM_DETAILS.GOODS_RECEIVED_ITEM_ID,    \n" +
                    "RO_ITEM_DETAILS.RO_STATUS,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TO_STATUS)\n" +
                    "        ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                    "END AS TO_STATUS,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.GR_STATUS)\n" +
                    "        ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                    "END AS GR_STATUS,\n" +
                    "    RO_ITEM_DETAILS.PROD_ID as PRODUCT_ID,\n" +
                    "    RO_ITEM_DETAILS.SKU_ID,\n" +
                    "    RO_ITEM_DETAILS.PROD_NAME as PRODUCT_NAME,\n" +
                    "    RO_ITEM_DETAILS.UOM as UNIT_OF_MEASURE,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.REQ_ABS_QTY)\n" +
                    "        ELSE RO_ITEM_DETAILS.REQ_ABS_QTY\n" +
                    "END AS REQUESTED_ABSOLUTE_QUANTITY,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TRANSFRD_QTY)\n" +
                    "        ELSE RO_ITEM_DETAILS.TRANSFRD_QTY\n" +
                    "END AS TRANSFERRED_QUANTITY,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.RECVD_QTY)\n" +
                    "        ELSE RO_ITEM_DETAILS.RECVD_QTY\n" +
                    "END AS RECVD_QTY,\n" +
                    "    GR3_SETTLED.GR3_STATUS,\n" +
                    "    GR3_SETTLED.GR3_QTY,\n" +
                    "    GR2_DETAILS.GR2_STATUS,\n" +
                    "    GR2_DETAILS.GR2_QTY,\n" +
                    "    -- CASE\n" +
                    "-- WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is not null)\n" +
                    "-- THEN IF( COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, 1, COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) ) * 100\n" +
                    "--         WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is null) THEN 0\n" +
                    "--         WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                    "--         WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN 'NOT_SETTLED'\n" +
                    "--         WHEN (GR3_SETTLED.GR3_STATUS = 'SETTLED' or GR3_SETTLED.GR3_STATUS = 'CREATED') THEN ( IF ( (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, 1, (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY ) ) * 100\n" +
                    "--         WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN ( RO_ITEM_DETAILS.RECVD_QTY / RO_ITEM_DETAILS.REQ_ABS_QTY ) * 100\n" +
                    "--         WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN 'NOT_SETTLED'\n" +
                    "-- ELSE 'NULL'\n" +
                    "-- END AS FF_PERC,\n" +
                    "    CASE\n" +
                    "WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is not null)\n" +
                    "THEN IF( COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, MAX(RO_ITEM_DETAILS.REQ_ABS_QTY), COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY),0) )\n" +
                    "        WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is null) THEN 0\n" +
                    "        WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                    "        WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN -1 \n" +
                    "        WHEN (GR3_SETTLED.GR3_STATUS = 'SETTLED' or GR3_SETTLED.GR3_STATUS = 'CREATED') THEN IF ( (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, RO_ITEM_DETAILS.REQ_ABS_QTY, RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY )\n" +
                    "        WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN RO_ITEM_DETAILS.RECVD_QTY\n" +
                    "        WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN -1 \n" +
                    "ELSE 'NULL'\n" +
                    "END AS FINAL_RECEIVED_QUANTITY\n" +
                    "FROM\n" +
                    "( SELECT\n" +
                    "ud1.UNIT_NAME as TRANSFR_UNIT,\n" +
                    "ro.FULFILLMENT_UNIT_ID as TRANSFR_UNIT_ID,\n" +
                    "        ud2.UNIT_NAME as REQ_UNIT,\n" +
                    "ro.REQUEST_UNIT_ID as REQ_UNIT_ID,\n" +
                    "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                    "gr.TRANSFER_ORDER_ID as TO_ID,\n" +
                    "gr.GOODS_RECEIVED_ID as GR_ID,\n" +
                    "roi.REQUEST_ORDER_ITEM_ID as RO_ITEM_ID,\n" +
                    "gri.GOODS_RECEIVED_ITEM_ID,\n" +
                    "ro.REQUEST_ORDER_STATUS as RO_STATUS,\n" +
                    "tr.TRANSFER_ORDER_STATUS as TO_STATUS,\n" +
                    "gr.GOODS_RECEIVED_STATUS as GR_STATUS,\n" +
                    "roi.PRODUCT_ID as PROD_ID,\n" +
                    "gri.SKU_ID,    \n" +
                    "roi.PRODUCT_NAME as PROD_NAME,    \n" +
                    "roi.UNIT_OF_MEASURE as UOM,    \n" +
                    "roi.REQUESTED_ABSOLUTE_QUANTITY as REQ_ABS_QTY,\n" +
                    "gri.TRANSFERRED_QUANTITY as TRANSFRD_QTY,\n" +
                    "gri.RECEIVED_QUANTITY as RECVD_QTY\n" +
                    "FROM\n" +
                    "KETTLE_SCM.REQUEST_ORDER ro\n" +
                    "INNER JOIN KETTLE_SCM.REQUEST_ORDER_ITEM roi on ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID    \n" +
                    "LEFT JOIN KETTLE_SCM.TRANSFER_ORDER tr on ro.REQUEST_ORDER_ID = tr.REQUEST_ORDER_ID\n" +
                    "LEFT JOIN KETTLE_SCM.GOODS_RECEIVED gr on tr.TRANSFER_ORDER_ID = gr.TRANSFER_ORDER_ID\n" +
                    "LEFT JOIN KETTLE_SCM.GOODS_RECEIVED_ITEM gri on gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID and roi.REQUEST_ORDER_ITEM_ID = gri.REQUEST_ORDER_ITEM_ID\n" +
                    "INNER JOIN KETTLE_SCM.UNIT_DETAIL ud1 on ro.FULFILLMENT_UNIT_ID = ud1.UNIT_ID\n" +
                    "INNER JOIN KETTLE_SCM.UNIT_DETAIL ud2 on ro.REQUEST_UNIT_ID = ud2.UNIT_ID        \n" +
                    "where\n" +
                    "ro.REQUEST_ORDER_STATUS in ('ACKNOWLEDGED', 'SETTLED', 'TRANSFERRED') and\n" +
                    "        roi.REQUESTED_ABSOLUTE_QUANTITY <> 0 and\n" +
                    "ro.FULFILLMENT_UNIT_ID in (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and ro.REQUEST_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                    "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and        \n" +
                    "DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1) ) as RO_ITEM_DETAILS\n" +
                    "LEFT JOIN\n" +
                    "( SELECT\n" +
                    "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                    "gr1.GOODS_RECEIVED_ID as GR_ID,\n" +
                    "gr3.GOODS_RECEIVED_STATUS as GR3_STATUS,\n" +
                    "gri3.SKU_ID,  \n" +
                    "gri3.TRANSFERRED_QUANTITY as GR3_QTY\n" +
                    "FROM\n" +
                    "KETTLE_SCM.GOODS_RECEIVED gr3\n" +
                    "INNER JOIN KETTLE_SCM.GOODS_RECEIVED_ITEM gri3 on gr3.GOODS_RECEIVED_ID = gri3.GOODS_RECEIVED_ID\n" +
                    "INNER JOIN KETTLE_SCM.GOODS_RECEIVED gr2 on gr2.GOODS_RECEIVED_ID = gr3.PARENT_GR\n" +
                    "INNER JOIN KETTLE_SCM.GOODS_RECEIVED gr1 on gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                    "INNER JOIN KETTLE_SCM.REQUEST_ORDER ro on gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                    "WHERE\n" +
                    "gr3.PARENT_GR is not NULL and gr2.PARENT_GR is not NULL and gr1.REQUEST_ORDER_ID is not NULL and\n" +
                    "gr3.GENERATION_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and gr3.GENERATED_FOR_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                    "gr3.GENERATION_TIME between SUBDATE(DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY),8) and SUBDATE(CURRENT_DATE, 1) and\n" +
                    "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and\n" +
                    "DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1)\n" +
                    ") as GR3_SETTLED on GR3_SETTLED.RO_ID = RO_ITEM_DETAILS.RO_ID and GR3_SETTLED.GR_ID = RO_ITEM_DETAILS.GR_ID and GR3_SETTLED.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                    "    LEFT JOIN\n" +
                    "( SELECT\n" +
                    "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                    "gr1.GOODS_RECEIVED_ID as GR_ID,\n" +
                    "gr2.GOODS_RECEIVED_STATUS as GR2_STATUS,\n" +
                    "gri2.SKU_ID,\n" +
                    "gri2.TRANSFERRED_QUANTITY as GR2_QTY\n" +
                    "FROM\n" +
                    "KETTLE_SCM.GOODS_RECEIVED gr2\n" +
                    "INNER JOIN KETTLE_SCM.GOODS_RECEIVED_ITEM gri2 on gr2.GOODS_RECEIVED_ID = gri2.GOODS_RECEIVED_ID\n" +
                    "INNER JOIN KETTLE_SCM.GOODS_RECEIVED gr1 on gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                    "INNER JOIN KETTLE_SCM.REQUEST_ORDER ro on gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                    "WHERE\n" +
                    "gr2.PARENT_GR is not NULL and gr1.PARENT_GR is NULL and\n" +
                    "gr2.GOODS_RECEIVED_STATUS in ('SETTLED', 'INITIATED') and gr1.REQUEST_ORDER_ID is not NULL and\n" +
                    "gr2.GENERATED_FOR_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and gr2.GENERATION_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                    "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and\n" +
                    "DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,1)\n" +
                    ") as GR2_DETAILS on GR2_DETAILS.RO_ID = RO_ITEM_DETAILS.RO_ID and GR2_DETAILS.GR_ID = RO_ITEM_DETAILS.GR_ID and GR2_DETAILS.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                    "group by RO_ITEM_DETAILS.RO_ITEM_ID) AS T;";
        }else{
            return "SELECT T.*, (T.FINAL_RECEIVED_QUANTITY / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 as FULLFILLMENT_PERCENTAGE,\n" +
                    "(CASE WHEN T.PRODUCT_ID IN (SELECT \n" +
                    "    umsm.SCM_PRODUCT_ID\n" +
                    "FROM\n" +
                    "    KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm,\n" +
                    "    KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp,\n" +
                    "    KETTLE_MASTER_DUMP.REF_LOOKUP rl,\n" +
                    "    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd,\n" +
                    "    KETTLE_MASTER_DUMP.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                    "WHERE\n" +
                    "    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                    "        AND umsm.INGREDIENT_TYPE = 'INGREDIENT'\n" +
                    "        AND umsm.PRODUCT_CLASSIFICATION = 'MENU'\n" +
                    "        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                    "        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                    "        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                    "        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                    "        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                    "        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                    "        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                    "        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                    "        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                    "        AND upm.UNIT_ID = T.REQUESTING_UNIT_ID) THEN 'Y' ELSE 'N' END) as IS_CRITICAL from\n" +
                    "(\n" +
                    "SELECT\n" +
                    "RO_ITEM_DETAILS.TRANSFR_UNIT as TRANSFERRING_UNIT,\n" +
                    "RO_ITEM_DETAILS.TRANSFR_UNIT_ID as TRANSFERRING_UNIT_ID,\n" +
                    "    RO_ITEM_DETAILS.REQ_UNIT as REQUESTING_UNIT,\n" +
                    "    RO_ITEM_DETAILS.REQ_UNIT_ID as REQUESTING_UNIT_ID,\n" +
                    "RO_ITEM_DETAILS.RO_ID REQUEST_ORDER_ID,\n" +
                    "RO_ITEM_DETAILS.TO_ID ,\n" +
                    "RO_ITEM_DETAILS.GR_ID,\n" +
                    "RO_ITEM_DETAILS.RO_ITEM_ID as REQUEST_ORDER_ITEM_ID,\n" +
                    "    RO_ITEM_DETAILS.GOODS_RECEIVED_ITEM_ID,    \n" +
                    "RO_ITEM_DETAILS.RO_STATUS,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TO_STATUS)\n" +
                    "        ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                    "END AS TO_STATUS,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.GR_STATUS)\n" +
                    "        ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                    "END AS GR_STATUS,\n" +
                    "    RO_ITEM_DETAILS.PROD_ID as PRODUCT_ID,\n" +
                    "    RO_ITEM_DETAILS.SKU_ID,\n" +
                    "    RO_ITEM_DETAILS.PROD_NAME as PRODUCT_NAME,\n" +
                    "    RO_ITEM_DETAILS.UOM as UNIT_OF_MEASURE,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.REQ_ABS_QTY)\n" +
                    "        ELSE RO_ITEM_DETAILS.REQ_ABS_QTY\n" +
                    "END AS REQUESTED_ABSOLUTE_QUANTITY,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TRANSFRD_QTY)\n" +
                    "        ELSE RO_ITEM_DETAILS.TRANSFRD_QTY\n" +
                    "END AS TRANSFERRED_QUANTITY,\n" +
                    "    CASE\n" +
                    "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.RECVD_QTY)\n" +
                    "        ELSE RO_ITEM_DETAILS.RECVD_QTY\n" +
                    "END AS RECVD_QTY,\n" +
                    "    GR3_SETTLED.GR3_STATUS,\n" +
                    "    GR3_SETTLED.GR3_QTY,\n" +
                    "    GR2_DETAILS.GR2_STATUS,\n" +
                    "    GR2_DETAILS.GR2_QTY,\n" +
                    "    -- CASE\n" +
                    "-- WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is not null)\n" +
                    "-- THEN IF( COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, 1, COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) ) * 100\n" +
                    "--         WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is null) THEN 0\n" +
                    "--         WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                    "--         WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN 'NOT_SETTLED'\n" +
                    "--         WHEN (GR3_SETTLED.GR3_STATUS = 'SETTLED' or GR3_SETTLED.GR3_STATUS = 'CREATED') THEN ( IF ( (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, 1, (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY ) ) * 100\n" +
                    "--         WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN ( RO_ITEM_DETAILS.RECVD_QTY / RO_ITEM_DETAILS.REQ_ABS_QTY ) * 100\n" +
                    "--         WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN 'NOT_SETTLED'\n" +
                    "-- ELSE 'NULL'\n" +
                    "-- END AS FF_PERC,\n" +
                    "    CASE\n" +
                    "WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is not null)\n" +
                    "THEN IF( COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, MAX(RO_ITEM_DETAILS.REQ_ABS_QTY), COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY),0) )\n" +
                    "        WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is null) THEN 0\n" +
                    "        WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                    "        WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN -1\n" +
                    "        WHEN (GR3_SETTLED.GR3_STATUS = 'SETTLED' or GR3_SETTLED.GR3_STATUS = 'CREATED') THEN IF ( (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, RO_ITEM_DETAILS.REQ_ABS_QTY, RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY )\n" +
                    "        WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN RO_ITEM_DETAILS.RECVD_QTY\n" +
                    "        WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN -1\n" +
                    "ELSE 'NULL'\n" +
                    "END AS FINAL_RECEIVED_QUANTITY\n" +
                    "FROM\n" +
                    "( SELECT\n" +
                    "ud1.UNIT_NAME as TRANSFR_UNIT,\n" +
                    "ro.FULFILLMENT_UNIT_ID as TRANSFR_UNIT_ID,\n" +
                    "        ud2.UNIT_NAME as REQ_UNIT,\n" +
                    "ro.REQUEST_UNIT_ID as REQ_UNIT_ID,\n" +
                    "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                    "gr.TRANSFER_ORDER_ID as TO_ID,\n" +
                    "gr.GOODS_RECEIVED_ID as GR_ID,\n" +
                    "roi.REQUEST_ORDER_ITEM_ID as RO_ITEM_ID,\n" +
                    "gri.GOODS_RECEIVED_ITEM_ID,\n" +
                    "ro.REQUEST_ORDER_STATUS as RO_STATUS,\n" +
                    "tr.TRANSFER_ORDER_STATUS as TO_STATUS,\n" +
                    "gr.GOODS_RECEIVED_STATUS as GR_STATUS,\n" +
                    "roi.PRODUCT_ID as PROD_ID,\n" +
                    "gri.SKU_ID,    \n" +
                    "roi.PRODUCT_NAME as PROD_NAME,    \n" +
                    "roi.UNIT_OF_MEASURE as UOM,    \n" +
                    "roi.REQUESTED_ABSOLUTE_QUANTITY as REQ_ABS_QTY,\n" +
                    "gri.TRANSFERRED_QUANTITY as TRANSFRD_QTY,\n" +
                    "gri.RECEIVED_QUANTITY as RECVD_QTY\n" +
                    "FROM\n" +
                    "KETTLE_SCM_DUMP.REQUEST_ORDER ro\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM roi on ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID    \n" +
                    "LEFT JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER tr on ro.REQUEST_ORDER_ID = tr.REQUEST_ORDER_ID\n" +
                    "LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr on tr.TRANSFER_ORDER_ID = gr.TRANSFER_ORDER_ID\n" +
                    "LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri on gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID and roi.REQUEST_ORDER_ITEM_ID = gri.REQUEST_ORDER_ITEM_ID\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud1 on ro.FULFILLMENT_UNIT_ID = ud1.UNIT_ID\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud2 on ro.REQUEST_UNIT_ID = ud2.UNIT_ID        \n" +
                    "where\n" +
                    "ro.REQUEST_ORDER_STATUS in ('ACKNOWLEDGED', 'SETTLED', 'TRANSFERRED') and\n" +
                    "        roi.REQUESTED_ABSOLUTE_QUANTITY <> 0 and\n" +
                    "ro.FULFILLMENT_UNIT_ID in (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and ro.REQUEST_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                    "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and        \n" +
                    "DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,2) ) as RO_ITEM_DETAILS\n" +
                    "LEFT JOIN\n" +
                    "( SELECT\n" +
                    "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                    "gr1.GOODS_RECEIVED_ID as GR_ID,\n" +
                    "gr3.GOODS_RECEIVED_STATUS as GR3_STATUS,\n" +
                    "gri3.SKU_ID,  \n" +
                    "gri3.TRANSFERRED_QUANTITY as GR3_QTY\n" +
                    "FROM\n" +
                    "KETTLE_SCM_DUMP.GOODS_RECEIVED gr3\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri3 on gr3.GOODS_RECEIVED_ID = gri3.GOODS_RECEIVED_ID\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr2 on gr2.GOODS_RECEIVED_ID = gr3.PARENT_GR\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 on gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro on gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                    "WHERE\n" +
                    "gr3.PARENT_GR is not NULL and gr2.PARENT_GR is not NULL and gr1.REQUEST_ORDER_ID is not NULL and\n" +
                    "gr3.GENERATION_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and gr3.GENERATED_FOR_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                    "gr3.GENERATION_TIME between SUBDATE(DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY),8) and SUBDATE(CURRENT_DATE, 2) and\n" +
                    "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and\n" +
                    "DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,2)\n" +
                    ") as GR3_SETTLED on GR3_SETTLED.RO_ID = RO_ITEM_DETAILS.RO_ID and GR3_SETTLED.GR_ID = RO_ITEM_DETAILS.GR_ID and GR3_SETTLED.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                    "    LEFT JOIN\n" +
                    "( SELECT\n" +
                    "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                    "gr1.GOODS_RECEIVED_ID as GR_ID,\n" +
                    "gr2.GOODS_RECEIVED_STATUS as GR2_STATUS,\n" +
                    "gri2.SKU_ID,\n" +
                    "gri2.TRANSFERRED_QUANTITY as GR2_QTY\n" +
                    "FROM\n" +
                    "KETTLE_SCM_DUMP.GOODS_RECEIVED gr2\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri2 on gr2.GOODS_RECEIVED_ID = gri2.GOODS_RECEIVED_ID\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 on gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                    "INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro on gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                    "WHERE\n" +
                    "gr2.PARENT_GR is not NULL and gr1.PARENT_GR is NULL and\n" +
                    "gr2.GOODS_RECEIVED_STATUS in ('SETTLED', 'INITIATED') and gr1.REQUEST_ORDER_ID is not NULL and\n" +
                    "gr2.GENERATED_FOR_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and gr2.GENERATION_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                    "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and\n" +
                    "DATE(ro.FULFILLMENT_DATE) between DATE_SUB(LAST_DAY(SUBDATE(NOW(),1)),INTERVAL DAY(LAST_DAY(SUBDATE(NOW(),1))) - 1 DAY) and SUBDATE(CURRENT_DATE,2)\n" +
                    ") as GR2_DETAILS on GR2_DETAILS.RO_ID = RO_ITEM_DETAILS.RO_ID and GR2_DETAILS.GR_ID = RO_ITEM_DETAILS.GR_ID and GR2_DETAILS.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                    "group by RO_ITEM_DETAILS.RO_ITEM_ID) AS T;";
        }

    }

    @Override
    public String getFullfillmentQueryLastDay(EnvType envType) {
        if(envType==EnvType.PROD || envType==EnvType.SPROD){
                return "SELECT T.*, (T.FINAL_RECEIVED_QUANTITY / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 as FULLFILLMENT_PERCENTAGE, \n" +
                        "(CASE WHEN T.PRODUCT_ID IN (SELECT \n" +
                        "    umsm.SCM_PRODUCT_ID\n" +
                        "FROM\n" +
                        "    KETTLE_MASTER.UNIT_PRODUCT_MAPPING upm,\n" +
                        "    KETTLE_MASTER.UNIT_PRODUCT_PRICING upp,\n" +
                        "    KETTLE_MASTER.REF_LOOKUP rl,\n" +
                        "    KETTLE_MASTER.PRODUCT_DETAIL pd,\n" +
                        "    KETTLE_MASTER.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                        "WHERE\n" +
                        "    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                        "        AND umsm.INGREDIENT_TYPE = 'INGREDIENT'\n" +
                        "        AND umsm.PRODUCT_CLASSIFICATION = 'MENU'\n" +
                        "        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                        "        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                        "        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                        "        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                        "        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                        "        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                        "        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                        "        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                        "        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                        "        AND upm.UNIT_ID = T.REQUESTING_UNIT_ID) THEN 'Y' ELSE 'N' END) as IS_CRITICAL from\n" +
                        "(\n" +
                        "SELECT\n" +
                        "RO_ITEM_DETAILS.TRANSFR_UNIT as TRANSFERRING_UNIT,\n" +
                        "RO_ITEM_DETAILS.TRANSFR_UNIT_ID as TRANSFERRING_UNIT_ID,\n" +
                        "    RO_ITEM_DETAILS.REQ_UNIT as REQUESTING_UNIT,\n" +
                        "    RO_ITEM_DETAILS.REQ_UNIT_ID as REQUESTING_UNIT_ID,\n" +
                        "RO_ITEM_DETAILS.RO_ID REQUEST_ORDER_ID,\n" +
                        "RO_ITEM_DETAILS.TO_ID ,\n" +
                        "RO_ITEM_DETAILS.GR_ID,\n" +
                        "RO_ITEM_DETAILS.RO_ITEM_ID as REQUEST_ORDER_ITEM_ID,\n" +
                        "    RO_ITEM_DETAILS.GOODS_RECEIVED_ITEM_ID,    \n" +
                        "RO_ITEM_DETAILS.RO_STATUS,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TO_STATUS)\n" +
                        "        ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                        "END AS TO_STATUS,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.GR_STATUS)\n" +
                        "        ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                        "END AS GR_STATUS,\n" +
                        "    RO_ITEM_DETAILS.PROD_ID as PRODUCT_ID,\n" +
                        "    RO_ITEM_DETAILS.SKU_ID,\n" +
                        "    RO_ITEM_DETAILS.PROD_NAME as PRODUCT_NAME,\n" +
                        "    RO_ITEM_DETAILS.UOM as UNIT_OF_MEASURE,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.REQ_ABS_QTY)\n" +
                        "        ELSE RO_ITEM_DETAILS.REQ_ABS_QTY\n" +
                        "END AS REQUESTED_ABSOLUTE_QUANTITY,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TRANSFRD_QTY)\n" +
                        "        ELSE RO_ITEM_DETAILS.TRANSFRD_QTY\n" +
                        "END AS TRANSFERRED_QUANTITY,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.RECVD_QTY)\n" +
                        "        ELSE RO_ITEM_DETAILS.RECVD_QTY\n" +
                        "END AS RECVD_QTY,\n" +
                        "    GR3_SETTLED.GR3_STATUS,\n" +
                        "    GR3_SETTLED.GR3_QTY,\n" +
                        "    GR2_DETAILS.GR2_STATUS,\n" +
                        "    GR2_DETAILS.GR2_QTY,\n" +
                        "    -- CASE\n" +
                        "-- WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is not null)\n" +
                        "-- THEN IF( COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, 1, COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) ) * 100\n" +
                        "--         WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is null) THEN 0\n" +
                        "--         WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                        "--         WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN 'NOT_SETTLED'\n" +
                        "--         WHEN (GR3_SETTLED.GR3_STATUS = 'SETTLED' or GR3_SETTLED.GR3_STATUS = 'CREATED') THEN ( IF ( (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, 1, (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY ) ) * 100\n" +
                        "--         WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN ( RO_ITEM_DETAILS.RECVD_QTY / RO_ITEM_DETAILS.REQ_ABS_QTY ) * 100\n" +
                        "--         WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN 'NOT_SETTLED'\n" +
                        "-- ELSE 'NULL'\n" +
                        "-- END AS FF_PERC,\n" +
                        "    CASE\n" +
                        "WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is not null)\n" +
                        "THEN IF( COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, MAX(RO_ITEM_DETAILS.REQ_ABS_QTY), COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY),0) )\n" +
                        "        WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is null) THEN 0\n" +
                        "        WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                        "        WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN -1 \n" +
                        "        WHEN (GR3_SETTLED.GR3_STATUS = 'SETTLED' or GR3_SETTLED.GR3_STATUS = 'CREATED') THEN IF ( (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, RO_ITEM_DETAILS.REQ_ABS_QTY, RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY )\n" +
                        "        WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN RO_ITEM_DETAILS.RECVD_QTY\n" +
                        "        WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN -1 \n" +
                        "ELSE 'NULL'\n" +
                        "END AS FINAL_RECEIVED_QUANTITY\n" +
                        "FROM\n" +
                        "( SELECT\n" +
                        "ud1.UNIT_NAME as TRANSFR_UNIT,\n" +
                        "ro.FULFILLMENT_UNIT_ID as TRANSFR_UNIT_ID,\n" +
                        "        ud2.UNIT_NAME as REQ_UNIT,\n" +
                        "ro.REQUEST_UNIT_ID as REQ_UNIT_ID,\n" +
                        "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                        "gr.TRANSFER_ORDER_ID as TO_ID,\n" +
                        "gr.GOODS_RECEIVED_ID as GR_ID,\n" +
                        "roi.REQUEST_ORDER_ITEM_ID as RO_ITEM_ID,\n" +
                        "gri.GOODS_RECEIVED_ITEM_ID,\n" +
                        "ro.REQUEST_ORDER_STATUS as RO_STATUS,\n" +
                        "tr.TRANSFER_ORDER_STATUS as TO_STATUS,\n" +
                        "gr.GOODS_RECEIVED_STATUS as GR_STATUS,\n" +
                        "roi.PRODUCT_ID as PROD_ID,\n" +
                        "gri.SKU_ID,    \n" +
                        "roi.PRODUCT_NAME as PROD_NAME,    \n" +
                        "roi.UNIT_OF_MEASURE as UOM,    \n" +
                        "roi.REQUESTED_ABSOLUTE_QUANTITY as REQ_ABS_QTY,\n" +
                        "gri.TRANSFERRED_QUANTITY as TRANSFRD_QTY,\n" +
                        "gri.RECEIVED_QUANTITY as RECVD_QTY\n" +
                        "FROM\n" +
                        "KETTLE_SCM.REQUEST_ORDER ro\n" +
                        "INNER JOIN KETTLE_SCM.REQUEST_ORDER_ITEM roi on ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID    \n" +
                        "LEFT JOIN KETTLE_SCM.TRANSFER_ORDER tr on ro.REQUEST_ORDER_ID = tr.REQUEST_ORDER_ID\n" +
                        "LEFT JOIN KETTLE_SCM.GOODS_RECEIVED gr on tr.TRANSFER_ORDER_ID = gr.TRANSFER_ORDER_ID\n" +
                        "LEFT JOIN KETTLE_SCM.GOODS_RECEIVED_ITEM gri on gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID and roi.REQUEST_ORDER_ITEM_ID = gri.REQUEST_ORDER_ITEM_ID\n" +
                        "INNER JOIN KETTLE_SCM.UNIT_DETAIL ud1 on ro.FULFILLMENT_UNIT_ID = ud1.UNIT_ID\n" +
                        "INNER JOIN KETTLE_SCM.UNIT_DETAIL ud2 on ro.REQUEST_UNIT_ID = ud2.UNIT_ID        \n" +
                        "where\n" +
                        "ro.REQUEST_ORDER_STATUS in ('ACKNOWLEDGED', 'SETTLED', 'TRANSFERRED') and\n" +
                        "        roi.REQUESTED_ABSOLUTE_QUANTITY <> 0 and\n" +
                        "ro.FULFILLMENT_UNIT_ID in (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and ro.REQUEST_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                        "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and        \n" +
                        "ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 1) ) as RO_ITEM_DETAILS\n" +
                        "LEFT JOIN\n" +
                        "( SELECT\n" +
                        "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                        "gr1.GOODS_RECEIVED_ID as GR_ID,\n" +
                        "gr3.GOODS_RECEIVED_STATUS as GR3_STATUS,\n" +
                        "gri3.SKU_ID,  \n" +
                        "gri3.TRANSFERRED_QUANTITY as GR3_QTY\n" +
                        "FROM\n" +
                        "KETTLE_SCM.GOODS_RECEIVED gr3\n" +
                        "INNER JOIN KETTLE_SCM.GOODS_RECEIVED_ITEM gri3 on gr3.GOODS_RECEIVED_ID = gri3.GOODS_RECEIVED_ID\n" +
                        "INNER JOIN KETTLE_SCM.GOODS_RECEIVED gr2 on gr2.GOODS_RECEIVED_ID = gr3.PARENT_GR\n" +
                        "INNER JOIN KETTLE_SCM.GOODS_RECEIVED gr1 on gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                        "INNER JOIN KETTLE_SCM.REQUEST_ORDER ro on gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                        "WHERE\n" +
                        "gr3.PARENT_GR is not NULL and gr2.PARENT_GR is not NULL and gr1.REQUEST_ORDER_ID is not NULL and\n" +
                        "gr3.GENERATION_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and gr3.GENERATED_FOR_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                        "gr3.GENERATION_TIME between SUBDATE(CURRENT_DATE, 8) and SUBDATE(CURRENT_DATE, 1) and\n" +
                        "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and\n" +
                        "ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 1)\n" +
                        ") as GR3_SETTLED on GR3_SETTLED.RO_ID = RO_ITEM_DETAILS.RO_ID and GR3_SETTLED.GR_ID = RO_ITEM_DETAILS.GR_ID and GR3_SETTLED.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                        "    LEFT JOIN\n" +
                        "( SELECT\n" +
                        "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                        "gr1.GOODS_RECEIVED_ID as GR_ID,\n" +
                        "gr2.GOODS_RECEIVED_STATUS as GR2_STATUS,\n" +
                        "gri2.SKU_ID,\n" +
                        "gri2.TRANSFERRED_QUANTITY as GR2_QTY\n" +
                        "FROM\n" +
                        "KETTLE_SCM.GOODS_RECEIVED gr2\n" +
                        "INNER JOIN KETTLE_SCM.GOODS_RECEIVED_ITEM gri2 on gr2.GOODS_RECEIVED_ID = gri2.GOODS_RECEIVED_ID\n" +
                        "INNER JOIN KETTLE_SCM.GOODS_RECEIVED gr1 on gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                        "INNER JOIN KETTLE_SCM.REQUEST_ORDER ro on gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                        "WHERE\n" +
                        "gr2.PARENT_GR is not NULL and gr1.PARENT_GR is NULL and\n" +
                        "gr2.GOODS_RECEIVED_STATUS in ('SETTLED', 'INITIATED') and gr1.REQUEST_ORDER_ID is not NULL and\n" +
                        "gr2.GENERATED_FOR_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and gr2.GENERATION_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                        "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and\n" +
                        "ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 1)\n" +
                        ") as GR2_DETAILS on GR2_DETAILS.RO_ID = RO_ITEM_DETAILS.RO_ID and GR2_DETAILS.GR_ID = RO_ITEM_DETAILS.GR_ID and GR2_DETAILS.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                        "group by RO_ITEM_DETAILS.RO_ITEM_ID) AS T;";
        }else{
                return "SELECT T.*, (T.FINAL_RECEIVED_QUANTITY / T.REQUESTED_ABSOLUTE_QUANTITY) * 100 as FULLFILLMENT_PERCENTAGE,\n" +
                        "(CASE WHEN T.PRODUCT_ID IN (SELECT \n" +
                        "    umsm.SCM_PRODUCT_ID\n" +
                        "FROM\n" +
                        "    KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm,\n" +
                        "    KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp,\n" +
                        "    KETTLE_MASTER_DUMP.REF_LOOKUP rl,\n" +
                        "    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd,\n" +
                        "    KETTLE_MASTER_DUMP.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                        "WHERE\n" +
                        "    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                        "        AND umsm.INGREDIENT_TYPE = 'INGREDIENT'\n" +
                        "        AND umsm.PRODUCT_CLASSIFICATION = 'MENU'\n" +
                        "        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                        "        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                        "        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                        "        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                        "        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                        "        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                        "        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                        "        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                        "        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                        "        AND upm.UNIT_ID = T.REQUESTING_UNIT_ID) THEN 'Y' ELSE 'N' END) as IS_CRITICAL from\n" +
                        "(\n" +
                        "SELECT\n" +
                        "RO_ITEM_DETAILS.TRANSFR_UNIT as TRANSFERRING_UNIT,\n" +
                        "RO_ITEM_DETAILS.TRANSFR_UNIT_ID as TRANSFERRING_UNIT_ID,\n" +
                        "    RO_ITEM_DETAILS.REQ_UNIT as REQUESTING_UNIT,\n" +
                        "    RO_ITEM_DETAILS.REQ_UNIT_ID as REQUESTING_UNIT_ID,\n" +
                        "RO_ITEM_DETAILS.RO_ID REQUEST_ORDER_ID,\n" +
                        "RO_ITEM_DETAILS.TO_ID ,\n" +
                        "RO_ITEM_DETAILS.GR_ID,\n" +
                        "RO_ITEM_DETAILS.RO_ITEM_ID as REQUEST_ORDER_ITEM_ID,\n" +
                        "    RO_ITEM_DETAILS.GOODS_RECEIVED_ITEM_ID,    \n" +
                        "RO_ITEM_DETAILS.RO_STATUS,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TO_STATUS)\n" +
                        "        ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                        "END AS TO_STATUS,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.GR_STATUS)\n" +
                        "        ELSE RO_ITEM_DETAILS.TO_STATUS\n" +
                        "END AS GR_STATUS,\n" +
                        "    RO_ITEM_DETAILS.PROD_ID as PRODUCT_ID,\n" +
                        "    RO_ITEM_DETAILS.SKU_ID,\n" +
                        "    RO_ITEM_DETAILS.PROD_NAME as PRODUCT_NAME,\n" +
                        "    RO_ITEM_DETAILS.UOM as UNIT_OF_MEASURE,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.REQ_ABS_QTY)\n" +
                        "        ELSE RO_ITEM_DETAILS.REQ_ABS_QTY\n" +
                        "END AS REQUESTED_ABSOLUTE_QUANTITY,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.TRANSFRD_QTY)\n" +
                        "        ELSE RO_ITEM_DETAILS.TRANSFRD_QTY\n" +
                        "END AS TRANSFERRED_QUANTITY,\n" +
                        "    CASE\n" +
                        "WHEN RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' THEN MAX(RO_ITEM_DETAILS.RECVD_QTY)\n" +
                        "        ELSE RO_ITEM_DETAILS.RECVD_QTY\n" +
                        "END AS RECVD_QTY,\n" +
                        "    GR3_SETTLED.GR3_STATUS,\n" +
                        "    GR3_SETTLED.GR3_QTY,\n" +
                        "    GR2_DETAILS.GR2_STATUS,\n" +
                        "    GR2_DETAILS.GR2_QTY,\n" +
                        "    -- CASE\n" +
                        "-- WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is not null)\n" +
                        "-- THEN IF( COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, 1, COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) ) * 100\n" +
                        "--         WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is null) THEN 0\n" +
                        "--         WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                        "--         WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN 'NOT_SETTLED'\n" +
                        "--         WHEN (GR3_SETTLED.GR3_STATUS = 'SETTLED' or GR3_SETTLED.GR3_STATUS = 'CREATED') THEN ( IF ( (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, 1, (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY ) ) * 100\n" +
                        "--         WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN ( RO_ITEM_DETAILS.RECVD_QTY / RO_ITEM_DETAILS.REQ_ABS_QTY ) * 100\n" +
                        "--         WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN 'NOT_SETTLED'\n" +
                        "-- ELSE 'NULL'\n" +
                        "-- END AS FF_PERC,\n" +
                        "    CASE\n" +
                        "WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is not null)\n" +
                        "THEN IF( COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY), 0) / MAX(RO_ITEM_DETAILS.REQ_ABS_QTY) > 1, MAX(RO_ITEM_DETAILS.REQ_ABS_QTY), COALESCE(MAX(RO_ITEM_DETAILS.RECVD_QTY),0) )\n" +
                        "        WHEN (RO_ITEM_DETAILS.RO_STATUS = 'SETTLED' and GR3_STATUS is null and GR2_STATUS is null and GOODS_RECEIVED_ITEM_ID is null) THEN 0\n" +
                        "        WHEN RO_ITEM_DETAILS.RO_STATUS = 'ACKNOWLEDGED' THEN 0\n" +
                        "        WHEN RO_ITEM_DETAILS.RO_STATUS = 'TRANSFERRED' THEN -1\n" +
                        "        WHEN (GR3_SETTLED.GR3_STATUS = 'SETTLED' or GR3_SETTLED.GR3_STATUS = 'CREATED') THEN IF ( (RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY) / RO_ITEM_DETAILS.REQ_ABS_QTY > 1, RO_ITEM_DETAILS.REQ_ABS_QTY, RO_ITEM_DETAILS.RECVD_QTY + GR3_SETTLED.GR3_QTY )\n" +
                        "        WHEN (GR2_DETAILS.GR2_STATUS = 'SETTLED') THEN RO_ITEM_DETAILS.RECVD_QTY\n" +
                        "        WHEN (GR2_DETAILS.GR2_STATUS = 'INITIATED') THEN -1\n" +
                        "ELSE 'NULL'\n" +
                        "END AS FINAL_RECEIVED_QUANTITY\n" +
                        "FROM\n" +
                        "( SELECT\n" +
                        "ud1.UNIT_NAME as TRANSFR_UNIT,\n" +
                        "ro.FULFILLMENT_UNIT_ID as TRANSFR_UNIT_ID,\n" +
                        "        ud2.UNIT_NAME as REQ_UNIT,\n" +
                        "ro.REQUEST_UNIT_ID as REQ_UNIT_ID,\n" +
                        "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                        "gr.TRANSFER_ORDER_ID as TO_ID,\n" +
                        "gr.GOODS_RECEIVED_ID as GR_ID,\n" +
                        "roi.REQUEST_ORDER_ITEM_ID as RO_ITEM_ID,\n" +
                        "gri.GOODS_RECEIVED_ITEM_ID,\n" +
                        "ro.REQUEST_ORDER_STATUS as RO_STATUS,\n" +
                        "tr.TRANSFER_ORDER_STATUS as TO_STATUS,\n" +
                        "gr.GOODS_RECEIVED_STATUS as GR_STATUS,\n" +
                        "roi.PRODUCT_ID as PROD_ID,\n" +
                        "gri.SKU_ID,    \n" +
                        "roi.PRODUCT_NAME as PROD_NAME,    \n" +
                        "roi.UNIT_OF_MEASURE as UOM,    \n" +
                        "roi.REQUESTED_ABSOLUTE_QUANTITY as REQ_ABS_QTY,\n" +
                        "gri.TRANSFERRED_QUANTITY as TRANSFRD_QTY,\n" +
                        "gri.RECEIVED_QUANTITY as RECVD_QTY\n" +
                        "FROM\n" +
                        "KETTLE_SCM_DUMP.REQUEST_ORDER ro\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM roi on ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID    \n" +
                        "LEFT JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER tr on ro.REQUEST_ORDER_ID = tr.REQUEST_ORDER_ID\n" +
                        "LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr on tr.TRANSFER_ORDER_ID = gr.TRANSFER_ORDER_ID\n" +
                        "LEFT JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri on gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID and roi.REQUEST_ORDER_ITEM_ID = gri.REQUEST_ORDER_ITEM_ID\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud1 on ro.FULFILLMENT_UNIT_ID = ud1.UNIT_ID\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud2 on ro.REQUEST_UNIT_ID = ud2.UNIT_ID        \n" +
                        "where\n" +
                        "ro.REQUEST_ORDER_STATUS in ('ACKNOWLEDGED', 'SETTLED', 'TRANSFERRED') and\n" +
                        "        roi.REQUESTED_ABSOLUTE_QUANTITY <> 0 and\n" +
                        "ro.FULFILLMENT_UNIT_ID in (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and ro.REQUEST_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                        "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and        \n" +
                        "ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 2) ) as RO_ITEM_DETAILS\n" +
                        "LEFT JOIN\n" +
                        "( SELECT\n" +
                        "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                        "gr1.GOODS_RECEIVED_ID as GR_ID,\n" +
                        "gr3.GOODS_RECEIVED_STATUS as GR3_STATUS,\n" +
                        "gri3.SKU_ID,  \n" +
                        "gri3.TRANSFERRED_QUANTITY as GR3_QTY\n" +
                        "FROM\n" +
                        "KETTLE_SCM_DUMP.GOODS_RECEIVED gr3\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri3 on gr3.GOODS_RECEIVED_ID = gri3.GOODS_RECEIVED_ID\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr2 on gr2.GOODS_RECEIVED_ID = gr3.PARENT_GR\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 on gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro on gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                        "WHERE\n" +
                        "gr3.PARENT_GR is not NULL and gr2.PARENT_GR is not NULL and gr1.REQUEST_ORDER_ID is not NULL and\n" +
                        "gr3.GENERATION_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and gr3.GENERATED_FOR_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                        "gr3.GENERATION_TIME between SUBDATE(CURRENT_DATE, 8) and SUBDATE(CURRENT_DATE, 2) and\n" +
                        "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and\n" +
                        "ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 2)\n" +
                        ") as GR3_SETTLED on GR3_SETTLED.RO_ID = RO_ITEM_DETAILS.RO_ID and GR3_SETTLED.GR_ID = RO_ITEM_DETAILS.GR_ID and GR3_SETTLED.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                        "    LEFT JOIN\n" +
                        "( SELECT\n" +
                        "ro.REQUEST_ORDER_ID as RO_ID,\n" +
                        "gr1.GOODS_RECEIVED_ID as GR_ID,\n" +
                        "gr2.GOODS_RECEIVED_STATUS as GR2_STATUS,\n" +
                        "gri2.SKU_ID,\n" +
                        "gri2.TRANSFERRED_QUANTITY as GR2_QTY\n" +
                        "FROM\n" +
                        "KETTLE_SCM_DUMP.GOODS_RECEIVED gr2\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri2 on gr2.GOODS_RECEIVED_ID = gri2.GOODS_RECEIVED_ID\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr1 on gr1.GOODS_RECEIVED_ID = gr2.PARENT_GR\n" +
                        "INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro on gr1.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID\n" +
                        "WHERE\n" +
                        "gr2.PARENT_GR is not NULL and gr1.PARENT_GR is NULL and\n" +
                        "gr2.GOODS_RECEIVED_STATUS in ('SETTLED', 'INITIATED') and gr1.REQUEST_ORDER_ID is not NULL and\n" +
                        "gr2.GENERATED_FOR_UNIT_ID IN (26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and gr2.GENERATION_UNIT_ID NOT IN (26072, 26423 , 26416, 26422, 26495, 26515, 26506, 26514, 26427, 26496, 26426, 26429) and\n" +
                        "ro.IS_SPECIAL_ORDER = 'N' and ro.ASSET_ORDER = 'N' and ro.TRANSFER_TYPE = 'TRANSFER' and\n" +
                        "ro.FULFILLMENT_DATE = SUBDATE(CURRENT_DATE, 2)\n" +
                        ") as GR2_DETAILS on GR2_DETAILS.RO_ID = RO_ITEM_DETAILS.RO_ID and GR2_DETAILS.GR_ID = RO_ITEM_DETAILS.GR_ID and GR2_DETAILS.SKU_ID = RO_ITEM_DETAILS.SKU_ID\n" +
                        "group by RO_ITEM_DETAILS.RO_ITEM_ID) AS T;";
        }
    }

    @Override
    public String getMenuToScmQuery(EnvType envType) {
        if(envType==EnvType.PROD || envType==EnvType.SPROD){
                return "SELECT \n" +
                        "    upm.PRODUCT_ID,\n" +
                        "    rl.RL_NAME,\n" +
                        "    upp.RECIPE_PROFILE,\n" +
                        "    umsm.SCM_PRODUCT_ID,\n" +
                        "    umsm.SCM_PRODUCT_QUANTITY,\n" +
                        "     (CASE WHEN umsm.INGREDIENT_TYPE= \"INGREDIENT\" and  umsm.PRODUCT_CLASSIFICATION = \"MENU\" THEN \"Y\" ELSE \"N\" END) as IS_CRITICAL \n" +
                        "FROM\n" +
                        "    KETTLE_MASTER.UNIT_PRODUCT_MAPPING upm,\n" +
                        "    KETTLE_MASTER.UNIT_PRODUCT_PRICING upp,\n" +
                        "    KETTLE_MASTER.REF_LOOKUP rl,\n" +
                        "    KETTLE_MASTER.PRODUCT_DETAIL pd,\n" +
                        "    KETTLE_MASTER.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                        "WHERE\n" +
                        "    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                        "        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                        "        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                        "        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                        "        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                        "        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                        "        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                        "        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                        "        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                        "        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                        "        AND upm.UNIT_ID = ? ;";
        }else{
            return "SELECT \n" +
                    "    upm.PRODUCT_ID,\n" +
                    "    rl.RL_NAME,\n" +
                    "    upp.RECIPE_PROFILE,\n" +
                    "    umsm.SCM_PRODUCT_ID,\n" +
                    "    umsm.SCM_PRODUCT_QUANTITY,\n" +
                    "     (CASE WHEN umsm.INGREDIENT_TYPE= \"INGREDIENT\" and  umsm.PRODUCT_CLASSIFICATION = \"MENU\" THEN \"Y\" ELSE \"N\" END) as IS_CRITICAL \n" +
                    "FROM\n" +
                    "    KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm,\n" +
                    "    KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp,\n" +
                    "    KETTLE_MASTER_DUMP.REF_LOOKUP rl,\n" +
                    "    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd,\n" +
                    "    KETTLE_MASTER_DUMP.MENU_TO_SCM_PRODUCT_MAP umsm\n" +
                    "WHERE\n" +
                    "    upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID\n" +
                    "        AND umsm.MENU_PRODUCT_ID = pd.PRODUCT_ID\n" +
                    "        AND umsm.RECIPE_PROFILE = upp.RECIPE_PROFILE\n" +
                    "        AND umsm.MENU_PRODUCT_DIMENSION = rl.RL_NAME\n" +
                    "        AND pd.PRODUCT_ID = upm.PRODUCT_ID\n" +
                    "        AND pd.PRODUCT_STATUS = 'ACTIVE'\n" +
                    "        AND pd.IS_INVENTORY_TRACKED = 'Y'\n" +
                    "        AND rl.RL_ID = upp.DIMENSION_CODE\n" +
                    "        AND upp.PRICING_STATUS = 'ACTIVE'\n" +
                    "        AND upm.PRODUCT_STATUS = 'ACTIVE'\n" +
                    "        AND upm.UNIT_ID = ? ;";
        }

    }
}
