package com.stpl.tech.scm.data.dao;

import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.data.model.PlanOrderItemData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.domain.model.ProductionPlanEvent;
import com.stpl.tech.scm.domain.model.UnitPlanItemRequest;

public interface ProductionPlanManagementDao extends SCMAbstractDao {

	public List<ProductionPlanEvent> getPlansByFulfilmentDate(Date start, Date end, int fulfillmentUnit);

	public PlanOrderItemData findItemByProductAndEvent(int productId, int eventId, String itemType);

	public List<UnitPlanItemRequest> findRoItemByPlanItem(List<Integer> productIds, int eventId, Date fulfilmentDate);

	public List<RequestOrderItemData> findRoItemByPlanEvent(int eventId, int productId);

}
