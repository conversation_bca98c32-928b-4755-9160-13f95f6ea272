/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.dao;

import java.util.Collection;
import java.util.List;

import com.stpl.tech.scm.core.exception.SumoException;

public interface SCMAbstractDao {
    public <T> T update(T data, boolean flush);

    public <T> List<T> update(List<T> dataList, boolean flush);

    public <T> T add(T data, boolean flush) throws SumoException;

    public <T> List<T> addAll(Collection<T> list);

    public <T> List<T> findAll(Class<T> data);

    public <T, R> T find(Class<T> data, R key);

    public <T> void delete(T data);

    public void flush();
}
