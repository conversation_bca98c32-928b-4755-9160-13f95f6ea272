package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.PurchaseOrder;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.Map;

public class POExpiredEmailNotificationTemplate extends AbstractVelocityTemplate {


    private final static Logger LOG = LoggerFactory.getLogger(VendorPRRejectEmailNotificationTemplate.class);

    private VendorDetail vendorDetail;
    private PurchaseOrderData purchaseOrderData;
    private String basePath;
    private Unit deliveryUnit;

    public POExpiredEmailNotificationTemplate() {

    }

    public POExpiredEmailNotificationTemplate(VendorDetail vendorDetail, PurchaseOrderData purchaseOrderData, Unit deliveryUnit,
                                                   String basePath) {
        this.vendorDetail = vendorDetail;
        this.basePath = basePath;
        this.purchaseOrderData = purchaseOrderData;
        this.deliveryUnit = deliveryUnit;
    }

    @Override
    public String getTemplatePath() {
        return "templates/POExpiredEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor/purchaseorderExpired/" + vendorDetail.getVendorId() + "/" + purchaseOrderData.getId() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendorDetail", vendorDetail);
        stringObjectMap.put("purchaseOrderData", purchaseOrderData);
        stringObjectMap.put("dateTool", new DateTool());
        stringObjectMap.put("deliveryUnit", deliveryUnit);
        return stringObjectMap;
    }

    public VendorDetail getVendorDetail() {
        return vendorDetail;
    }

    public PurchaseOrderData getPurchaseOrderData() {
        return purchaseOrderData;
    }
}
