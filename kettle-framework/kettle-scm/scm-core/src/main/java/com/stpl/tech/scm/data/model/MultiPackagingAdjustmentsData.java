package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "MULTI_PACKAGING_ADJUSTMENTS")
public class MultiPackagingAdjustmentsData {

    private Integer itemPackagingId;
    private Integer packagingId;
    private String isChecked;
    private BigDecimal quantity;
    private RequestOrderItemData requestOrderItemData;
    private String lastUpdatedBy;
    private Date lastUpdatedTime;

    public MultiPackagingAdjustmentsData() {
    }

    public MultiPackagingAdjustmentsData(Integer packagingId, String isChecked, BigDecimal quantity) {
        this.packagingId = packagingId;
        this.isChecked = isChecked;
        this.quantity = quantity;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ITEM_PACKAGING_ID", nullable = false, unique = true)
    public Integer getItemPackagingId() {
        return itemPackagingId;
    }

    public void setItemPackagingId(Integer itemPackagingId) {
        this.itemPackagingId = itemPackagingId;
    }

    @Column(name = "PACKAGING_ID", nullable = false)
    public Integer getPackagingId() {
        return packagingId;
    }

    public void setPackagingId(Integer packagingId) {
        this.packagingId = packagingId;
    }

    @Column(name = "IS_CHECKED", nullable = false)
    public String getIsChecked() {
        return isChecked;
    }

    public void setIsChecked(String isChecked) {
        this.isChecked = isChecked;
    }

    @Column(name = "QUANTITY", nullable = false)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = RequestOrderItemData.class)
    @JoinColumn(name = "REQUEST_ORDER_ITEM_ID", nullable = false)
    public RequestOrderItemData getRequestOrderItemData() {
        return requestOrderItemData;
    }

    public void setRequestOrderItemData(RequestOrderItemData requestOrderItemData) {
        this.requestOrderItemData = requestOrderItemData;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = false)
    public String getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(String lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "LAST_UPDATED_TIME", nullable = false)
    public Date getLastUpdatedTime() {
        return lastUpdatedTime;
    }

    public void setLastUpdatedTime(Date lastUpdatedTime) {
        this.lastUpdatedTime = lastUpdatedTime;
    }
}
