package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.CapexRequestDetailData;
import com.stpl.tech.scm.domain.model.DepartmentBudgetVO;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CapexEmailNotificationTemplate extends AbstractVelocityTemplate {

    private String approvedBy;
    private String basePath;
    private EnvType envType;
    private String subjectOfEmail;
    private List<DepartmentBudgetVO> budgets;
    private String unitName;
    private String isApproved;
    private BigDecimal totalAmount;
    private List<CapexBudgetDetailData> budgetDetailData;
    private String uploadedBy;
    private CapexAuditDetailData capexAuditDetailData;
    private String userId;
    private CapexRequestDetailData capexRequestDetailData;

    public CapexEmailNotificationTemplate() {
    }

    public CapexEmailNotificationTemplate(String approvedBy, String basePath, EnvType envType, String subjectOfEmail, List<DepartmentBudgetVO> budgets, String unitName, String isApproved,
                                          BigDecimal totalAmount, List<CapexBudgetDetailData> budgetDetailData, String uploadedBy, CapexAuditDetailData capexAuditDetailData,
                                          String userId, CapexRequestDetailData capexRequestDetailData) {
        this.approvedBy = approvedBy;
        this.basePath = basePath;
        this.envType = envType;
        this.subjectOfEmail = subjectOfEmail;
        this.budgets = budgets;
        this.unitName = unitName;
        this.isApproved = isApproved;
        this.totalAmount = totalAmount;
        this.budgetDetailData = budgetDetailData;
        this.uploadedBy = uploadedBy;
        this.capexAuditDetailData = capexAuditDetailData;
        this.userId = userId;
        this.capexRequestDetailData = capexRequestDetailData;
    }

    @Override
    public String getTemplatePath() {
        return "templates/CapexEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/CapexTemplate.html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("approvedBy", approvedBy);
        stringObjectMap.put("userId", userId);
        stringObjectMap.put("subjectOfEmail", subjectOfEmail);
        stringObjectMap.put("capexRequestDetailData", capexRequestDetailData);
        stringObjectMap.put("basePath", basePath);
        stringObjectMap.put("envType", envType);
        stringObjectMap.put("budgets", budgets);
        stringObjectMap.put("unitName", unitName);
        stringObjectMap.put("isApproved", isApproved);
        stringObjectMap.put("totalAmount", totalAmount);
        stringObjectMap.put("budgetDetailData", budgetDetailData);
        stringObjectMap.put("uploadedBy", uploadedBy);
        stringObjectMap.put("capexAuditDetailData", capexAuditDetailData);
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("dateTool", new DateTool());
        return stringObjectMap;
    }

    public String getSubjectOfEmail() {
        return subjectOfEmail;
    }
}
