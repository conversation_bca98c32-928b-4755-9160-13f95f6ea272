package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "PAYMENT_INVOICE")
public class PaymentInvoiceData {

    private Integer id;
    private String invoiceNumber;
    private Integer invoiceDocumentHandle;
    private PaymentRequestData paymentRequestData;
    private BigDecimal calculatedInvoiceAmount;
    private BigDecimal extraCharges;
    private BigDecimal invoiceAmount;
    private BigDecimal paymentAmount;
    private Date invoiceDate;
    private List<PaymentInvoiceItemData> paymentInvoiceItemData = new ArrayList<>(0);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_INVOICE_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "INVOICE_NUMBER")
    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    @Column(name = "INVOICE_DOCUMENT_HANDLE")
    public Integer getInvoiceDocumentHandle() {
        return invoiceDocumentHandle;
    }

    public void setInvoiceDocumentHandle(Integer invoiceDocumentHandle) {
        this.invoiceDocumentHandle = invoiceDocumentHandle;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PAYMENT_REQUEST_ID", nullable = false)
    public PaymentRequestData getPaymentRequestData() {
        return paymentRequestData;
    }

    public void setPaymentRequestData(PaymentRequestData paymentRequestData) {
        this.paymentRequestData = paymentRequestData;
    }

    @Column(name = "PAYMENT_AMOUNT", nullable = false)
    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "paymentInvoiceData")
    public List<PaymentInvoiceItemData> getPaymentInvoiceItemData() {
        return paymentInvoiceItemData;
    }

    public void setPaymentInvoiceItemData(List<PaymentInvoiceItemData> paymentInvoiceItemData) {
        this.paymentInvoiceItemData = paymentInvoiceItemData;
    }

    @Column(name = "CALCULATED_AMOUNT", nullable = false)
    public BigDecimal getCalculatedInvoiceAmount() {
        return calculatedInvoiceAmount;
    }

    public void setCalculatedInvoiceAmount(BigDecimal calculatedInvoiceAmount) {
        this.calculatedInvoiceAmount = calculatedInvoiceAmount;
    }

    @Column(name = "INVOICE_AMOUNT", nullable = false)
    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    @Column(name = "EXTRA_CHARGES", nullable = false)
    public BigDecimal getExtraCharges() {
        return extraCharges;
    }

    public void setExtraCharges(BigDecimal extraCharges) {
        this.extraCharges = extraCharges;
    }

    @Column(name = "INVOICE_DATE", nullable = false)
    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }
}
