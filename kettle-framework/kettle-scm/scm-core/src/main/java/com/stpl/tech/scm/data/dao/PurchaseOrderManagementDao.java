package com.stpl.tech.scm.data.dao;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderItemData;
import com.stpl.tech.scm.domain.model.PurchaseOrder;
import com.stpl.tech.scm.domain.model.PurchaseOrderExtendedStatus;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 22-06-2016.
 */
public interface PurchaseOrderManagementDao extends SCMAbstractDao {
    public List<PurchaseOrderData> findClonablePurchaseOrders(int vendorId, int deliveryUnitId, int dispatchId);

    public int getNextStateInvoiceId(int stateId, String type);

    public int getNextStateInvoiceId(int stateId, String type, Integer financialYear);

    public List<PurchaseOrderData> findPurchaseOrdersByStatus(Integer vendorId, Integer dispatchId, int deliveryUnitId, List<Integer> skus, List<PurchaseOrderStatus> statusList, Date startDate, Date endDate, Integer PurchaseOrderId, PurchaseOrderStatus status, PurchaseOrderExtendedStatus expiryStatus);

    List<Integer> getSkuPriceDataId(Integer skuId, Integer skuPackagingId, Integer vendorId,
                                    String dispatchLocation, String deliveryLocation);

    List<Pair<BigDecimal,String>> getSkuPriceHistory(Integer skuPriceDataId);

    public List<PurchaseOrderItemData> checkExtraGrEligibility(Integer unitId, Integer vendorId, Integer dispatchId, List<Integer> ids, List<Integer> poIds);

    public List<Object[]> getConsumptionForPurchase(int daysInPast, List<Integer> skus, Integer unitId);

    public  List<PurchaseOrderData> getAllToBeExpirePurchaseOrder();

    public List<PurchaseOrderData> getAllExpiredAndNeedToClosePurchaseOrders();

    public List<PurchaseOrderData> getAllExpiredAndExpiringPurchaseOrders(Date fromDate);

    public List<PurchaseOrderData> getPosForAdvance(Integer vendorId);
}
