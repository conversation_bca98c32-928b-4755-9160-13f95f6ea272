/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.core.mapper;

import com.stpl.tech.scm.data.model.PaymentRequestQueryData;
import com.stpl.tech.scm.data.model.SCMProductInventoryData;
import com.stpl.tech.scm.domain.model.PaymentRequestQuery;
import com.stpl.tech.scm.domain.model.VarianceEditItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DomainDataMapper {

    DomainDataMapper INSTANCE = Mappers.getMapper(DomainDataMapper.class);

    @Mapping(source = "prId.id", target = "prId")
    @Mapping(source = "paymentDeviationData.id", target = "paymentDeviationId")
    @Mapping(source = "paymentDeviationData.deviationDetail", target = "paymentDeviationDetail")
    PaymentRequestQuery toPaymentRequestQuery(PaymentRequestQueryData paymentRequestQueryData);

    VarianceEditItem toVarianceEditItem(SCMProductInventoryData scmProductInventoryData);

}
