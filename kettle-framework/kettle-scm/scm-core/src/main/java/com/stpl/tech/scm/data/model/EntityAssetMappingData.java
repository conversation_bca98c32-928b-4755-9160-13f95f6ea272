/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@SuppressWarnings("serial")
@Entity
@Table(name = "ENTITY_ASSET_MAPPING",
        uniqueConstraints = @UniqueConstraint(
                columnNames = {
                        "ENTITY_ID",
                        "ENTITY_TYPE",
                        "ENTITY_SUB_ID",
                        "ASSET_ID"
                }))
public class EntityAssetMappingData implements java.io.Serializable {

    private Integer entityAssetMappingId;
    private Integer entityId;
    private String entityType;
    private String entityCategory;
    private Integer entitySubId;
    private Integer assetId;
    private String assetTagValue;


    public EntityAssetMappingData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ENTITY_ASSET_MAPPING_ID", unique = true, nullable = false)
    public Integer getEntityAssetMappingId() {
        return entityAssetMappingId;
    }

    public void setEntityAssetMappingId(Integer entityAssetMappingId) {
        this.entityAssetMappingId = entityAssetMappingId;
    }

    @Column(name = "ENTITY_ID", nullable = false)
    public Integer getEntityId() {
        return entityId;
    }

    public void setEntityId(Integer entityId) {
        this.entityId = entityId;
    }

    @Column(name = "ENTITY_TYPE", nullable = false)
    public String getEntityType() {
        return entityType;
    }

    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    @Column(name = "ENTITY_CATEGORY", nullable = false)
    public String getEntityCategory() {
        return entityCategory;
    }

    public void setEntityCategory(String entityCategory) {
        this.entityCategory = entityCategory;
    }

    @Column(name = "ENTITY_SUB_ID", nullable = false)
    public Integer getEntitySubId() {
        return entitySubId;
    }

    public void setEntitySubId(Integer entitySubId) {
        this.entitySubId = entitySubId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "ASSET_TAG_VALUE", nullable = false)
    public String getAssetTagValue() {
        return assetTagValue;
    }

    public void setAssetTagValue(String assetTagValue) {
        this.assetTagValue = assetTagValue;
    }

}
