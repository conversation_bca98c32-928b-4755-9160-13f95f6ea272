/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.latest.service.impl;

import java.util.Properties;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Created by Rahul Singh on 03-05-2016.
 */

@Configuration
@EnableTransactionManagement
@ComponentScan(basePackages = {"com.stpl.tech.scm.data.latest"})
@EnableJpaRepositories(basePackages = {"com.stpl.tech.scm.data.latest.service.impl"}, entityManagerFactoryRef = "SCMDataSourceEMFactory", transactionManagerRef = "SCMDataSourceTM")
public class ScmTestConfig {

    @Autowired
    private Environment env;

    public ScmTestConfig() {
        super();
    }
    // beans

    @Bean(name = "SCMDataSourceEMFactory")
    public LocalContainerEntityManagerFactoryBean scmEntityManagerFactory() {
        final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(scmDataSource());
        em.setPackagesToScan("com.stpl.tech.scm.data.model");
        final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        em.setJpaProperties(masterAdditionalProperties());
        em.setPersistenceUnitName("SCMDataSourcePUName");
        return em;
    }

    @Bean(name = "SCMDataSource")
    public DataSource scmDataSource() {
        final DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(env.getProperty("scm.jdbc.driverClassName"));
        dataSource.setUrl(env.getProperty("scm.jdbc.url"));
        dataSource.setUsername(env.getProperty("scm.jdbc.user"));
        dataSource.setPassword(env.getProperty("scm.jdbc.pass"));
        return dataSource;
    }

    @Bean(name = "SCMDataSourceTM")
    public PlatformTransactionManager scmTransactionManager() {
        final JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(scmEntityManagerFactory().getObject());
        return transactionManager;
    }

    @Bean(name = "SCMDataSourceET")
    public PersistenceExceptionTranslationPostProcessor scmExceptionTranslation() {
        return new PersistenceExceptionTranslationPostProcessor();
    }

    final Properties masterAdditionalProperties() {
        final Properties hibernateProperties = new Properties();
        hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getProperty("hibernate.hbm2ddl.auto"));
        hibernateProperties.setProperty("hibernate.dialect", env.getProperty("hibernate.dialect"));
        hibernateProperties.setProperty("hibernate.show_sql", env.getProperty("hibernate.show_sql"));
        return hibernateProperties;
    }
}
