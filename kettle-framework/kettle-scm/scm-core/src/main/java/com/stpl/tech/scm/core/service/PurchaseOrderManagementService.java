package com.stpl.tech.scm.core.service;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.scm.core.exception.PurchaseOrderCreationException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.model.AutoPOCreationResponse;
import com.stpl.tech.scm.core.util.model.ConsumptionView;
import com.stpl.tech.scm.core.util.model.PurchaseOrderCreateVO;
import com.stpl.tech.scm.core.util.model.UsedPOModel;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.domain.model.CapexBudgetDetail;
import com.stpl.tech.scm.domain.model.PurchaseOrder;
import com.stpl.tech.scm.domain.model.PurchaseOrderDetails;
import com.stpl.tech.scm.domain.model.PurchaseOrderExtendedStatus;
import com.stpl.tech.scm.domain.model.PurchaseOrderExtendedStatusLog;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.VendorGRItem;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import org.springframework.data.util.Pair;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Chaayos on 22-06-2016.
 */
public interface PurchaseOrderManagementService {

    public Integer createPurchaseOrder(PurchaseOrderCreateVO createVO) throws PurchaseOrderCreationException, SumoException;

    public List<PurchaseOrder> getClonableOrders(int vendorId, int deliveryUnitId, int dispatchId);

    public List<PurchaseOrder> findOrdersByStatus(Integer vendorId, Integer dispatchId, List<Integer> skus, int deliveryUnitId, List<PurchaseOrderStatus> statusList, Date startDate, Date endDate, Integer PurchaseOrderId, PurchaseOrderStatus status,PurchaseOrderExtendedStatus expiryStatus);

    public boolean approvePurchaseOrder(Integer poId, Integer userId) throws DocumentException, TemplateRenderingException, IOException;

    public boolean rejectPurchaseOrder(Integer poId, Integer userId) throws SumoException;

    public boolean cancelPurchaseOrder(Integer poId, Integer userId) throws SumoException;

    public boolean generatePOStatusEvent(PurchaseOrderData purchaseOrder, PurchaseOrderStatus toStatus, int userId) throws SumoException;

    public boolean closePurchaseOrder(Integer poId, Integer userId) throws SumoException;

    public boolean checkIfFulfilled(PurchaseOrderData po);

    public List<Integer> checkExtraGrEligibility(Integer unitId, Integer vendorId, Integer dispatchId,List<Integer> poIds, List<Integer> skus);

    public Pair<UsedPOModel, Map<String, VendorGRItem>> createPurchaseOrder(PurchaseOrderCreateVO vo, Map<String, VendorGRItem> extraGrItems) throws PurchaseOrderCreationException, SumoException;

    public List<ConsumptionView> getConsumptionForPurchase(int daysInPast, String skus, int unitId);

    public AutoPOCreationResponse createPurchaseOrder(PurchaseOrderCreateVO vo,
			List<RequestOrderItem> requestOrderItems) throws PurchaseOrderCreationException, SumoException;

    void sendCLosedPoEmailNotification(PurchaseOrderData purchaseOrderData, Integer userId)
            throws SumoException;

    public PurchaseOrderExtendedStatusLog extendPurchaseOrder(PurchaseOrderExtendedStatusLog purchaseOrderExtendedStatusLog) throws SumoException;


    public void updatePoStatus() throws SumoException,EmailGenerationException;

    public CapexBudgetDetail getDepartmentBudgetData(Integer unitId, String isFixedAssetOrGoods);

    public Boolean validateBudgetAmounts(CapexBudgetDetailData capexBudgetDetailData);
    public Boolean updateApprovedPurchaseOrder(PurchaseOrderDetails purchaseOrderDetails) throws SumoException;

    public List<PurchaseOrder> getPosForAdvance(Integer vendorId) throws SumoException;

    void createAutoLogsForPurchaseOrder(PurchaseOrderData purchaseOrderData) throws SumoException;
}
