/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * UnitSkuMapping generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "VENDOR_SKU_MAPPING")
public class VendorSkuMapping implements java.io.Serializable {

	private Integer vendorSkuMappingId;
	private int skuId;
	private int vendorId;
	private String mappingStatus;
	private String createdBy;
	private Date createdAt;
	private String updatedBy;
	private Date updatedAt;
	private String alias;


	public VendorSkuMapping() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "VENDOR_SKU_MAPPING_ID", unique = true, nullable = false)
	public Integer getVendorSkuMappingId() {
		return this.vendorSkuMappingId;
	}

	public void setVendorSkuMappingId(Integer unitProductMappingId) {
		this.vendorSkuMappingId = unitProductMappingId;
	}

	@Column(name = "SKU_ID", nullable = false)
	public int getSkuId() {
		return this.skuId;
	}

	public void setSkuId(int productId) {
		this.skuId = productId;
	}

	@Column(name = "VENDOR_ID", nullable = false)
	public int getVendorId() {
		return this.vendorId;
	}

	public void setVendorId(int vendorId) {
		this.vendorId = vendorId;
	}

	@Column(name = "MAPPING_STATUS", nullable = false, length = 15)
	public String getMappingStatus() {
		return this.mappingStatus;
	}

	public void setMappingStatus(String mappingStatus) {
		this.mappingStatus = mappingStatus;
	}

	@Column(name = "CREATED_BY", length = 50)
	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_AT", nullable = true, length = 19)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "UPDATED_BY", length = 50)
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATED_AT", nullable = true, length = 19)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Column(name = "SKU_ALIAS", nullable = true, length = 100)
	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}
}
