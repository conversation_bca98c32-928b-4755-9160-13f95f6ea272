/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.VendorAdvancesEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

public class VendorAdvancesEmailNotification  extends EmailNotification {

    private VendorAdvancesEmailNotificationTemplate notificationTemplate;
    private EnvType envType;
    private List<String> emails;

    public VendorAdvancesEmailNotification() {
    }

    public VendorAdvancesEmailNotification(VendorAdvancesEmailNotificationTemplate notificationTemplate, EnvType envType, List<String> emails) {
        this.notificationTemplate = notificationTemplate;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        emails.add("<EMAIL>");
        String[] simpleArray = new String[emails.size()];
        return SCMUtil.isDev(envType) ? new String[]{"<EMAIL>"} : emails.toArray(simpleArray);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Vendor Advances " + SCMUtil.getFormattedDate(SCMUtil.getCurrentDateIST());
        if (SCMUtil.isDev(envType)) {
            subject = "Dev " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return notificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
