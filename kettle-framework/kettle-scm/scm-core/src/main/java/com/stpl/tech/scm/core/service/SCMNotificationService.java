package com.stpl.tech.scm.core.service;

import java.io.File;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.scm.core.exception.PurchaseOrderCreationException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderNotificationData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderNotificationData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.VendorRegistrationRequestDetail;
import com.stpl.tech.scm.domain.model.AssetRecoveryDefinition;
import com.stpl.tech.scm.domain.model.LostAssetEmailObject;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.notification.email.RejectedGREmailNotification;
import com.stpl.tech.scm.notification.email.template.*;

/**
 * Created by Chaayos on 22-09-2016.
 */
public interface SCMNotificationService {

    public void sendVendorRONotification(boolean enabled, List<VendorDetail> vendorDetails, List<RequestOrder> requestOrders, boolean isMilk, NotificationType type);

    public List<Integer> sendVendorRONotification(boolean enabled, List<RequestOrder> requestOrders, NotificationType type);

    public void sendAssetOrderNotification(RequestOrder ro);

    public void sendVendorGRNotification(boolean enabled, List<VendorDetail> vendorDetails, List<RequestOrder> requestOrders , boolean isMilk, NotificationType type);

    public PurchaseOrderNotificationData sendPONotification(PurchaseOrderData po, VendorDetail vendor, VendorPOEmailNotificationTemplate template, File poInvoice) throws PurchaseOrderCreationException;

    public void sendPOClosureNotification(PurchaseOrderData po, PurchaseOrderStatus status, boolean isAutoClosure);

    public void poPendingForApprovalNotification(PurchaseOrderData po);

    public void sendGrRejectionNotification(GoodsReceivedData gr, Map<Integer, GoodsReceivedItemData> rejectedItems);

    public void sendDayClosureNotification(SCMDayCloseEventData dayCloseEvent);

    public void sendDayClosureAlarmNotification(SCMDayCloseEventData dayCloseEvent, BigDecimal variance);

    public void sendDayClosureAlarmNotification(int unitId, String message);

    public void sendVendorApprovalNotification(VendorDetail vendorDetail);

    public void sendVendorCompletionNotification(VendorRegistrationRequestDetail vendorDetail);

    public void sendInventoryErrorNotification(String message);

    public void sendInvoiceNotification(SalesPerformaDetailData performa, Integer userId);

    public void sendInvoiceErrorNotification(StringBuilder message, SlackNotification channel);

	public ServiceOrderNotificationData sendSONotification(ServiceOrderData serviceOrderData, VendorDetail vendor,
                                                           VendorSOEmailNotificationTemplate template, File poInvoice, File uploadedFile, String uploadedMimetype, String[] emails, Boolean sendMail,Boolean logStore) throws SumoException;

    public void sendAssetLostNotification(List<AssetRecoveryDefinition> assetRecoveryDefinitions, List<String> emails);

    public void sendLostAssetNotification(LostAssetEmailObject lostAsset, List<String> emails, Boolean isInsuranceRecovery);

	PurchaseOrderNotificationData sendCancelledPONotification(PurchaseOrderData purchaseOrderData, VendorDetail vendor,
			VendorPOEmailNotificationTemplate template, File poInvoice) throws PurchaseOrderCreationException;

    public void sendRejectedPONotification(PurchaseOrderData purchaseOrderData, VendorDetail vendor,
			VendorPOEmailNotificationTemplate template, File poInvoice) throws PurchaseOrderCreationException;

	public PurchaseOrderNotificationData sendClosedPONotification(PurchaseOrderData purchaseOrderData,
			VendorDetail vendor, VendorClosedPOEmailNotificationTemplate emailTemplate, File first) throws PurchaseOrderCreationException;

    public void sendGrQualityCheckNotification(VendorGoodsReceivedData vendorGr);

    public Boolean sendProjectionsNotification(ProductProjectionsEmailNotificationTemplate emailTemplate, File uploadedFile, String[] emailIds);

    public Boolean sendMeasurementBookEmail(MeasurementBookTemplate emailTemplate, File uploadedFile, String employeeName , List<String> toEmails , List<String> ccEmails);

    public void sendSRNotification(SrEmailNotificationTemplate emailTemplate, String[] emails);

    public boolean sendTDSCertificateToVendor(String vendorName, String quarter, String financialYear,
                                              String[] vendorEmailList, File tdsCertificateFile
    ) throws SumoException;

    public void setRejectedGrNotification(RejectedGREmailNotificationTemplate rejectedGREmailNotificationTemplate);
}
