/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@SuppressWarnings("serial")
@Entity
@Table(name = "STOCK_EVENT_ASSET_MAPPING" ,uniqueConstraints = {@UniqueConstraint(columnNames = {"EVENT_ID", "ASSET_ID"})})
public class    StockEventAssetMappingDefinitionData implements java.io.Serializable {


    private Integer eventAssetMappingId;


    private Integer eventId;
    /*
        UnitId signifies Location of Asset
     */
    private Integer unitId;

    private Integer assetId;

    private String assetStatus;

    private Date creationDate;

    private Integer auditedBy;


    private Date auditDate;

    private String auditStatus;

    private String manualChecked;

    public StockEventAssetMappingDefinitionData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "STOCK_EVENT_ASSET_MAPPING_ID", unique = true, nullable = false)
    public Integer getEventAssetMappingId() {
        return eventAssetMappingId;
    }

    public void setEventAssetMappingId(Integer eventAssetMappingId) {
        this.eventAssetMappingId = eventAssetMappingId;
    }

    @Column(name = "EVENT_ID", nullable = false)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "ASSET_STATUS", nullable = false)
    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "AUDIT_STATUS", nullable = true)
    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    @Column(name = "AUDITED_BY", nullable = true)
    public Integer getAuditedBy() {
        return auditedBy;
    }

    public void setAuditedBy(Integer auditedBy) {
        this.auditedBy = auditedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "AUDIT_TIMESTAMP", length = 19)
    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    @Column(name = "MANUAL_CHECKED", nullable = true)
    public String getManualChecked() {
        return manualChecked;
    }

    public void setManualChecked(String manualChecked) {
        this.manualChecked = manualChecked;
    }
}