package com.stpl.tech.scm.invoice;

import com.lowagie.text.Document;
import com.lowagie.text.Row;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 20-05-2017.
 */
public abstract class AbstractInvoice<T> {

    private Row headerRow;
    private Row footerRow;


    public Row getHeaderRow() {
        return headerRow;
    }

    public Row getFooterRow() {
        return footerRow;
    }


    public abstract Document getContent(T t);
}
