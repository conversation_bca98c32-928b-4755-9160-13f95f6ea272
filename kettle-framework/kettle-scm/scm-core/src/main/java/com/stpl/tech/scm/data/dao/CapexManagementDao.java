package com.stpl.tech.scm.data.dao;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.CapexAuditDetail;
import com.stpl.tech.scm.domain.model.CapexBudgetDetail;
import com.stpl.tech.scm.domain.model.PurchaseOrder;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceOrder;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;

public interface CapexManagementDao extends SCMAbstractDao {

	CapexAuditDetailData findcapexAuditData(int id);

	List<CapexBudgetDetailData> findBudgetAudit(Integer unitId, Integer caapexRequestId);

	List<CapexRequestDetailData> getUnitVerify(Integer unitId);

	CapexAuditDetailData findAuditHistory(Integer capexRequestId);

	CapexAuditDetailData findCapexDataForVersion(int numericCellValue, String numericCellValue2);

	List<CapexAuditDetail> findList(List<Integer> capexRequestIds, String version, String status);

	List<Integer> findIds(Integer unitId, String version, String status);

	CapexRequestStatusLog findCapexStatusLog(Integer id, Integer capexRequestId);

	List<CapexBudgetDetail> findBudgetDetails(Integer unitId, Integer capexRequestId);

	String findSOSrPrInOpenedStates(Integer id);

    public List<CapexBudgetDetailData> findCapexBudgetDetailData(Integer unitId, Integer capexRequestId);

    public List<CapexBudgetAuditDetail> findCapexBudgetAuditDetailData(Integer capexAuditDetailId);

	public List<ServiceOrderData> findServiceOrderFromCapexIdAndDepartment(Integer capexRequestId, Integer departmentId, Integer vendorId,List<ServiceOrderStatus> statusList);
	public List<PurchaseOrderData> findPurchaseOrderFromCapexIdAndDepartment(Integer capexRequestId, Integer departmentId, Integer vendorId, List<PurchaseOrderStatus> statusList);

	public Integer soVendorCount(Integer capexRequestId, Integer departmentId);
	public Integer poVendorCount(Integer capexRequestId,Integer departmentId);

	public Integer getUnitFromCapexId(Integer capexRequestId);
    boolean isSoPoFromExpiredCapex(Integer soPoId, Date validDate, String type, Set<Integer> checkUnits);
}
