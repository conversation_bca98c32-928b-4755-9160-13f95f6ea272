package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.NegativeStockException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.model.AcknowledgeTransactionVO;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.RegularOrderUnitBrandData;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.DayCloseTxnItem;
import com.stpl.tech.scm.domain.model.Disclaimer;
import com.stpl.tech.scm.domain.model.FixedAssetDayCloseResponseObject;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductStockForUnit;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuStockForUnit;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 13-06-2017.
 */
public interface WarehouseStockManagementService {

    public DayCloseEvent initiateDayClose(int unitId, int userId , Boolean isAllListType , List<String> subCategories) throws SumoException;

    public List<Integer> settleRejectedPendingGR(int unitId, int userId) throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException;

    public boolean cancelDayCloseEvent(int eventId, int userId);

    public DayCloseEvent getActiveWarehouseDayCloseEvent(int unitId);

    public List<DayCloseTxnItem> getWastagesForClosing(int unitId, List<Integer> skuList);

    public List<DayCloseTxnItem> getTransfersForClosing(int unitId, List<Integer> skuList) throws DataUpdationException;

    public List<DayCloseTxnItem> getBookingsForClosing(int unitId, List<Integer> skuList);

    public List<SkuStockForUnit> getInventoryForClosing(int unitId , Boolean isAllListType , List<Integer> subCategoryIds) throws DataUpdationException;

    public List<DayCloseTxnItem> getReceivingsForClosing(int unitId, List<Integer> skuList) throws DataUpdationException;

    public boolean acknowledgeTransactions(int userId, int event, DayCloseEventLogType type, List<DayCloseTxnItem> items) throws DataUpdationException;

    public boolean acknowledgeBookings(int id, int event, DayCloseEventLogType type, List<DayCloseTxnItem> items, boolean isBooking) throws DataUpdationException;

    public List<ProductStockForUnit> submitInventory(int userId, int eventId, List<DayCloseTxnItem> items ,  Boolean isPreview) throws DataUpdationException, InventoryUpdateException, SumoException;

    public boolean correctInventory(int userId,Integer eventId, List<ProductStockForUnit> items) throws DataUpdationException, SumoException, InventoryUpdateException;

    public Boolean submitDayClose(int userId, int eventId) throws DataUpdationException, InventoryUpdateException, NegativeStockException;

    public List<ProductStockForUnit> getInventorySummary(int eventId);

    public List<ProductStockForUnit> getInventorySummary(List<Integer> skuList, int unitId) throws DataUpdationException;

    public boolean getThresholdDayCloseTime(int unitId);

	public Map<Integer, BigDecimal> getInventorySummaryForProducts(Set<Integer> productIds, int unitId);

    public List<Integer> getSkuListForUnit(Integer unitId);

    public List<DayCloseTxnItem> getBookingConsumptions(int unitId, List<Integer> skuList);

    public List<CostDetailData> getCurrentPrices(PriceUpdateEntryType product, int fulfillmentUnitId, List<Integer> keyIds, boolean current);

    public Map<Integer, String> getSkuClosings(Integer unitId, List<Integer> skus);

    public List<DayCloseTxnItem> getGatepasses(int unitId, List<Integer> skuList);

    public List<DayCloseTxnItem> getGatepassReturns(int unitId, List<Integer> skuList);

    public List<DayCloseTxnItem> getInvoices(int unitId, List<Integer> skuList);

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    Map<String,List<DayCloseTxnItem>> getInvoicesMap(int unitId, List<Integer> skuList);

    public boolean ifAlreadyUpdated(DayCloseEventLogType type, int event);

    AcknowledgeTransactionVO getBookingsSummaryForClosing(int unitId, List<Integer> skuList, boolean loadDrilldowns);

    AcknowledgeTransactionVO getReverseBookingsSummaryForClosing(int unitId, List<Integer> skuList, boolean loadDrilldowns);

    AcknowledgeTransactionVO  getBookingConsumptionsSummary(int unitId, List<Integer> skuList);

	List<DayCloseTxnItem> getBookingsForClosingByBookingIds(int unitId, List<Integer> bookingIds, boolean isBooking);

    public List<ProductStockForUnit> getNegativeStocks(int userId, int eventId, List<DayCloseTxnItem> items ) throws DataUpdationException, InventoryUpdateException, SumoException;

    public List<SkuDefinition> filterBySubCategory(List<SkuDefinition> taggedInventoryListSkus , List<Integer> subCategoryIds);

    public void generateAndSendVarianceReport(Integer eventId);

    public List<RegularOrderUnitBrandData> getRoUnitBrandData(int unitId);

    public List<RegularOrderEvent> getRegularOrderingEvents(Integer unitId);

    public Disclaimer checkIfDayCloseDoneInWeek(Integer unitId);

    public FixedAssetDayCloseResponseObject getAssetsToDayCloseToday(Integer unitId);
}
