package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.util.model.CreateVendorGrVO;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.InvoiceExcessQuantity;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.data.transport.model.GrStockEvent;
import com.stpl.tech.scm.domain.model.BulkRequestVO;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.GoodsReceived;
import com.stpl.tech.scm.domain.model.GrItemQuantityUpdation;
import com.stpl.tech.scm.domain.model.AssetDefinition;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SpecializedOrderInvoice;
import com.stpl.tech.scm.domain.model.VendorGR;
import com.stpl.tech.scm.domain.model.TransferOrder;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
public interface GoodsReceiveManagementService {

    public GoodsReceivedData createGoodReceiveFromTransferOrder(TransferOrderData transferOrderData, RequestOrderData requestOrderData,
                                                                List<TransferOrderItemData> transferOrderItemDatas, PurchaseOrderData purchaseOrderData) throws SumoException;

    public List<GoodsReceived> getPendingGrs(Integer unitId , Integer skuId , Boolean fetchRejected);

    public List<GoodsReceived> getPendingGrsWithGrItems(Integer unitId);

    public List<GoodsReceived> getPendingDisputedGrs(Integer unitId , Integer skuId , Boolean setChild);



    public List<GoodsReceived> getRaisedDisputedGrs(Integer unitId , Integer skuId , Boolean setChild);

    public boolean isGrSettled(Integer grId);

    public List<String> getPorImageUrls(int grId ) throws IOException;

    public HashMap<String, Integer> getPendingGrsStatusWise(Integer unitId, Integer numberOfDaysInPast);

    public List<GoodsReceivedData> getRejectedPendingGrs(Integer unitId);

    public GoodsReceived getGoodsReceivedDetail(int grId);

    public GoodsReceivedData getOriginalGrData(GoodsReceivedData goodsReceivedData);

    public boolean settleGoodsReceivedDetail(GoodsReceived goodsReceived) throws InventoryUpdateException, SumoException, ParseException;

    public boolean cancelGoodsReceivedDetail(Integer grId);

    public boolean rejectedGoodsReceivedFound(GoodsReceived goodsReceived) throws InventoryUpdateException, SumoException, ParseException;

    public boolean rejectedGoodsReceivedLost(GoodsReceived goodsReceived) throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException;

    public void settleRejectedGoodsReceived(GoodsReceivedData goodsReceivedData) throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException;

    public boolean rejectedGoodsReceivedDecline(GoodsReceived goodsReceived) throws SumoException;

    public List<GoodsReceived> findGoodsReceived(Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer goodsReceiveOrderId ,
                                                 Integer skuId);

    /*
     * Vendor Receivings related code here onwards
     */
    public int createVendorGR(CreateVendorGrVO grVO) throws DataUpdationException, SumoException, InventoryUpdateException;

    public boolean approveRegularVendorGR(VendorGR vendorGR, Integer userId) throws DataUpdationException, SumoException, InventoryUpdateException;

    public boolean cancelVendorGR(int grId, int userId) throws DataUpdationException, SumoException;

    public DocumentDetail uploadDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file);

    public List<VendorGR> findVendorGRs(Integer vendorId, Integer dispatchId, int deliveryUnitId, List<Integer> skus, Date startDate, Date endDate, Integer goodReceivedId);

    public List<VendorGR> findVendorGRsRejected(Integer vendorId, Integer dispatchId, int deliveryUnitId, Date startDate, Date endDate, Integer goodReceivedId);

    public List<VendorGR> findVendorGRsForPayment(Integer vendorId, Integer dispatchId, int deliveryUnitId, Date startDate, Date endDate);

    public Boolean setVendorGRsForNoPayment(BulkRequestVO request) throws SumoException;

    public PriceManagementDao getPriceDao();

    public boolean setVendorGRtoCreated(int grId, int userId) throws SumoException;

    public String getPendingPoSoRoStatus(String handOverData, Integer unitId);

    public List<VendorGR> findVendorGRsForPo(Integer vendorId, Integer dispatchId, Integer deliveryUnitId, List<Integer> skus, Date startDate, Date endDate, Integer paymentRequestId, Integer goodsReceivedId);

    public List<VendorGR> findRegularVendorsGrs(Integer unitId, Integer vendorId);

    public List<GrItemQuantityUpdation> grItemQuantityDeviations();

    public List<GoodsReceived> findMilkGrsForPayment(Integer deliveryUnitId , Date startDate,Date endDate , Integer vendorId);

    public List<SpecializedOrderInvoice> getMilkInvoicesForPayment(Integer deliveryUnitId, Date startDate, Date endDate, Integer vendorId);

    public GoodsReceived getAggregatedObject(List<GoodsReceived> goodsReceivedList);

    public GoodsReceivedData saveAggregatedGrObjectForDeviation(List<GoodsReceivedData> goodsReceivedList , Map<Integer, InvoiceExcessQuantity> invoiceExcessQuantityMap)
            throws SumoException;

    public Boolean createGrForExcessPrQty(Integer invoiceId , Integer unitId,  Integer vendorId) throws SumoException, InventoryUpdateException, ParseException, DataNotFoundException, TransferOrderCreationException;

    public Boolean autoGRUrgentSpecializedOrder(Integer toId) throws InventoryUpdateException, ParseException, SumoException;

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean autoGRSpecializedOrder(Integer toId, TransferOrder transferOrder) throws InventoryUpdateException, ParseException, SumoException;

    public DocumentDetail uploadPOD(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, Integer grId);

    public DocumentDetail uploadProofOfRejection(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, Integer grId,String fileNameExt);

    public List<AssetDefinition> getAssetsForPayment(List<Integer> grIds);

    public Boolean initiateGREvent(GoodsReceived gr, Integer unitId, Integer userId) throws SumoException;

    public GrStockEvent getGrStockEvent(Integer grId) throws SumoException;

    Boolean cancelGrEvent(Integer grId) throws SumoException;

    Boolean settleGrEvent(Integer eventId, Integer grId) throws SumoException;
}
