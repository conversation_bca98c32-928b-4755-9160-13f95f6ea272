package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.ProductData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.ProductProjectionsSevice;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.dao.ProductProjectionsDao;
import com.stpl.tech.scm.data.model.ForecastReportResponse;
import com.stpl.tech.scm.data.model.ProductProjectionsDetailsData;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.DerivedMapping;
import com.stpl.tech.scm.domain.model.ForecastAuthRequest;
import com.stpl.tech.scm.domain.model.ForecastAuthResponse;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnitDetail;
import com.stpl.tech.scm.domain.model.ProductProjectionsUnits;
import com.stpl.tech.scm.domain.model.ProductionItemType;
import com.stpl.tech.scm.domain.model.RefCreateRequest;
import com.stpl.tech.scm.domain.model.ReferenceOrderMenuItem;
import com.stpl.tech.scm.domain.model.ReferenceOrderMenuVariant;
import com.stpl.tech.scm.domain.model.ReferenceOrderScmItem;
import com.stpl.tech.scm.domain.model.RequestScmItem;
import com.stpl.tech.scm.domain.model.SubCategoryDefinition;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.http.HttpResponse;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class ProductProjectionsServiceImpl implements ProductProjectionsSevice {

    private static final Logger LOG = LoggerFactory.getLogger(ProductProjectionsServiceImpl.class);

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private ProductProjectionsDao productProjectionsDao;

    @Autowired
    private SCMProductManagementService scmProductManagementService;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private EnvProperties env;

    @Autowired
    private SCMNotificationService notificationService;

    @Override
    public List<ForecastReportResponse> getForecastReport(HttpResponse response) throws IOException {
        BufferedReader in = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
        String line = null;
        List<ForecastReportResponse> forecastReportResponseList = new ArrayList<>();
        int counter = 0;
        while ((line = in.readLine()) != null ) {
            if (counter == 0) {
                counter += 1;
                continue;
            }
            String[] data = line.split(",");
            forecastReportResponseList.add(new ForecastReportResponse(AppUtils.parseDate(data[0]), data[1], data[2],
                    data[3], data[4], data[5], data[6], data[7], data[8], data[9], data[10], data[11],
                    BigDecimal.valueOf(Double.parseDouble(data[12])), BigDecimal.valueOf(Double.parseDouble(data[13])),
                    BigDecimal.valueOf(Double.parseDouble(data[14])), BigDecimal.valueOf(Double.parseDouble(data[15])),
                    BigDecimal.valueOf(Double.parseDouble(data[16])), BigDecimal.valueOf(Double.parseDouble(data[17])),
                    BigDecimal.valueOf(Double.parseDouble(data[18])), BigDecimal.valueOf(Double.parseDouble(data[19])),
                    BigDecimal.valueOf(Double.parseDouble(data[20])), AppUtils.getCurrentTimestamp(),null));
        }
        try {
            in.close();
        } catch (Exception e) {
            LOG.error("Error in closing buffered reader", e);
        }
        return forecastReportResponseList;
    }

    @Override
    public List<ProductProjectionsUnitDetail> getUnitInputData(Date startDate, Date endDate,Integer unitId) {
        List<ProductProjectionsUnitDetail> result = new ArrayList<>();
        List<UnitBasicDetail> activeEnabledUnits = new ArrayList<>();
        List<UnitBasicDetail> activeDisabledUnits = new ArrayList<>();

        if (unitId == null) {
            activeEnabledUnits = masterCache.getAllUnits().stream().filter((unit) -> (unit.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)
                    && unit.isLive() && unit.getCategory().value().equalsIgnoreCase(AppConstants.CAFE)
                    && unitNameCheck(unit.getName().toLowerCase()))).collect(Collectors.toList());
            activeDisabledUnits = masterCache.getAllUnits().stream().filter((unit) -> (unit.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)
                    && !unit.isLive() && unit.getCategory().value().equalsIgnoreCase(AppConstants.CAFE)
                    && unitNameCheck(unit.getName().toLowerCase()))).collect(Collectors.toList());
        }
        else {
            activeEnabledUnits = masterCache.getAllUnits().stream().filter((unit) -> (unit.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE)
                    && unit.isLive() && unit.getId() == unitId && unit.getCategory().value().equalsIgnoreCase(AppConstants.CAFE)
                    && unitNameCheck(unit.getName().toLowerCase()))).collect(Collectors.toList());
        }
        LOG.info("active enabled units {}",activeEnabledUnits.size());
        LOG.info("active disabled units {}",activeDisabledUnits.size());

        for (UnitBasicDetail detail:activeEnabledUnits){
            if(masterCache.getUnitwisePartnerBrandMappingMetaData(detail.getId()).size() > 0) {
                for(UnitPartnerBrandMappingData brandMappingData : getDistinctBrands(masterCache.getUnitwisePartnerBrandMappingMetaData(detail.getId()))) {
                    ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
                    entry.setStartDate(startDate);
                    entry.setEndDate(endDate);
                    entry.setBrandCode(masterCache.getBrandMetaData().get(brandMappingData.getBrandId()).getBrandName());
                    entry.setUnitId(detail.getId());
                    entry.setUnitName(detail.getName());
                    entry.setUnitCostCenter(detail.getCostCenterName());
                    entry.setStatus(detail.getStatus().value() + "[ENABLED]");
                    result.add(entry);
                }
            }
        }

        for (UnitBasicDetail detail:activeDisabledUnits){
            if(masterCache.getUnitwisePartnerBrandMappingMetaData(detail.getId()).size() > 0) {
                for(UnitPartnerBrandMappingData brandMappingData : getDistinctBrands(masterCache.getUnitwisePartnerBrandMappingMetaData(detail.getId()))) {
                    ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
                    if (detail.getProbableOpeningDate() != null) {
                        LOG.info("checked probable date for unit {}", detail.getName());
                        Date duplicateStartDate = getStartDate(startDate, endDate, detail.getProbableOpeningDate());
                        if (duplicateStartDate != null) {
                            entry.setStartDate(duplicateStartDate);
                            entry.setEndDate(endDate);
                        } else {
                            continue;
                        }
                    } else {
                        continue;
                    }
                    entry.setBrandCode(masterCache.getBrandMetaData().get(brandMappingData.getBrandId()).getBrandName());
                    entry.setUnitId(detail.getId());
                    entry.setUnitName(detail.getName());
                    entry.setUnitCostCenter(detail.getCostCenterName());
                    entry.setStatus(detail.getStatus().value() + "[DISABLED]");
                    if (detail.getProbableOpeningDate() != null) {
                        entry.setCafeOpeningDate(detail.getProbableOpeningDate());
                    }
                    if (detail.getSalesClonedFrom() != null) {
                        entry.setSalesClonedFrom(detail.getSalesClonedFrom());
                    }
                    else {
                        continue;
                    }
                    result.add(entry);
                }
            }
        }
        return result.stream().sorted(Comparator.comparing(ProductProjectionsUnitDetail::getUnitId)).collect(Collectors.toList());
    }

    private Boolean unitNameCheck(String unitName) {
        if (unitName.contains("odc") || unitName.contains("zomato") ||  unitName.contains("zomaro") ||  unitName.contains("zepto") ||
                unitName.contains("partner test") || unitName.contains("bazaar")) {
            LOG.info("Leaving unit : {}",unitName);
            return false;
        }
        else {
            return true;
        }
    }

    private Integer getProductIdFromSkuId(String skuID){
        String[] list=skuID.split("_");
        return Integer.parseInt(list[0]);
    }

    private String getDimensionFromSkuId(String skuID){
        String[] list=skuID.split("_");
        return list[1];
    }

    private Collection<UnitPartnerBrandMappingData> getDistinctBrands(Collection<UnitPartnerBrandMappingData> unitwisePartnerBrandMappingMetaData) {
        Map<Integer,UnitPartnerBrandMappingData> result = new HashMap<>();
        for (UnitPartnerBrandMappingData data: unitwisePartnerBrandMappingMetaData){
            if (!result.containsKey(data.getBrandId())){
                result.put(data.getBrandId(),data);
            }
        }
        return result.values();
    }

    private Date getStartDate(Date startDate,Date endDate, Date probableOpeningDate) {
        if (probableOpeningDate.compareTo(startDate) >=0 && probableOpeningDate.compareTo(endDate) <= 0){
            if (probableOpeningDate.compareTo(startDate) > 0){
                return probableOpeningDate;
            }
            else{
                return startDate;
            }
        }
        else{
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductProjectionsUnitDetail> readUploadFile(MultipartFile file)  throws IOException,SumoException {
        List<ProductProjectionsUnitDetail> result = new ArrayList<>();
        List<ProductProjectionsUnitDetail> finalResult = new ArrayList<>();
        Set<Integer> exceptionRows = new HashSet<>();
        InputStream excelFile = file.getInputStream();
        XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
        XSSFSheet sheet1 = workbook.getSheetAt(0);
        int lastRowNumber = sheet1.getLastRowNum();
        LOG.info("last row of the sheet is {}", lastRowNumber);
        for (int i = 1; i <= lastRowNumber; i++) {
            ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
            XSSFRow currentRow = sheet1.getRow(i);
            try {
                if (currentRow.getCell(0).getStringCellValue() != null) {
                    entry.setStartDate(AppUtils.getDate(currentRow.getCell(0).getStringCellValue(),"dd-MM-yyyy"));
                }
            }
            catch (Exception e){
                exceptionRows.add(i+1);
            }
            try {
                if (currentRow.getCell(1).getStringCellValue() != null) {
                    entry.setEndDate(AppUtils.getDate(currentRow.getCell(1).getStringCellValue(),"dd-MM-yyyy"));
                }
            }
             catch (Exception e){
                exceptionRows.add(i+1);
            }
            try {
                Integer unitId = new Double(currentRow.getCell(3).getNumericCellValue()).intValue();
                entry.setUnitId(unitId);
            }
            catch (Exception e){
                exceptionRows.add(i+1);
            }
            result.add(entry);
        }

        for (ProductProjectionsUnitDetail data : result ) {
            UnitBasicDetail detail = masterCache.getUnitBasicDetail(data.getUnitId());
            if (masterCache.getUnitwisePartnerBrandMappingMetaData(data.getUnitId()).size() > 0) {
                for (UnitPartnerBrandMappingData brandMappingData : getDistinctBrands(masterCache.getUnitwisePartnerBrandMappingMetaData(data.getUnitId()))) {
                    ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
                    entry.setBrandCode(masterCache.getBrandMetaData().get(brandMappingData.getBrandId()).getBrandName());
                    entry.setUnitId(detail.getId());
                    entry.setUnitName(detail.getName());
                    entry.setUnitCostCenter(detail.getCostCenterName());
                    if (detail.getStatus().value().equalsIgnoreCase(AppConstants.ACTIVE) && detail.isLive()) {
                        entry.setStatus(detail.getStatus().value() + "[ENABLED]");
                        entry.setStartDate(data.getStartDate());
                        entry.setEndDate(data.getEndDate());
                    }
                    else {
                        entry.setStatus(detail.getStatus().value() + "[DISABLED]");
                        if (detail.getProbableOpeningDate() != null) {
                            LOG.info("came here for unit {}", detail.getName());
                            Date duplicateStartDate = getStartDate(data.getStartDate(), data.getEndDate(), detail.getProbableOpeningDate());
                            LOG.info("result is {} :: {}",detail.getProbableOpeningDate(),duplicateStartDate);
                            if (duplicateStartDate != null) {
                                entry.setStartDate(duplicateStartDate);
                                entry.setEndDate(data.getEndDate());
                            } else {
                                continue;
                            }
                        } else {
                            continue;
                        }
                    }
                    if (detail.getProbableOpeningDate() != null) {
                        entry.setCafeOpeningDate(detail.getProbableOpeningDate());
                    }
                    if (detail.getSalesClonedFrom() != null) {
                        entry.setSalesClonedFrom(detail.getSalesClonedFrom());
                    }
                    finalResult.add(entry);
                }
            }
        }

        if (exceptionRows.size() > 0) {
            String message = "Error Occurred While Reading Uploaded Sheet.Please Fill all the values Correctly at Rows : "
                    + Arrays.toString(exceptionRows.toArray());
            throw new SumoException("Verify Uploaded Sheet..!", message);
        }
        return finalResult.stream().sorted(Comparator.comparing(ProductProjectionsUnitDetail::getUnitId)).collect(Collectors.toList());
    }

    @Override
    public List<ProductProjectionsUnitDetail> getMenuOutput(List<ForecastReportResponse> forecastReportResponseList, ProductProjectionsUnitDetail unitDetail, ProductProjectionsUnitDetail inactiveUnit) {
        try {
            List<ProductProjectionsUnitDetail> result = new ArrayList<>();
            Map<Integer, Product> productsMap = masterCache.getUnitProductDetails().get(unitDetail.getUnitId()).stream().collect(Collectors.toMap(Product::getId, Function.identity()));
            for (ForecastReportResponse response : forecastReportResponseList) {
                ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
                try {
                    if (inactiveUnit != null) {
                        if (response.getDate().compareTo(inactiveUnit.getStartDate()) < 0) {
                            continue;
                        }
                        else {
                            entry.setStartDate(inactiveUnit.getStartDate());
                            entry.setEndDate(inactiveUnit.getEndDate());
                        }
                    }
                    else {
                        entry.setStartDate(unitDetail.getStartDate());
                        entry.setEndDate(unitDetail.getEndDate());
                    }
                    entry.setBrandCode(response.getBrand());
                    entry.setOrderSource(response.getOrderSource());
                    entry.setUnitId(unitDetail.getUnitId());
                    entry.setUnitName(unitDetail.getUnitName());
                    entry.setUnitCostCenter(unitDetail.getUnitCostCenter());
                    entry.setStatus(unitDetail.getStatus());
                    entry.setCafeOpeningDate(unitDetail.getCafeOpeningDate());
                    entry.setBusinessDate(response.getDate());
                    entry.setMenuProductId(getProductIdFromSkuId(response.getSkuId()));
                    entry.setMenuProductName(masterCache.getProduct(entry.getMenuProductId()).getName());
                    entry.setQuantity(response.getPredictedFinal());
                    entry.setPrice(response.getPrice());
                    entry.setSales(response.getValue());
                    entry.setDimension(getDimensionFromSkuId(response.getSkuId()));
                    Product productData = productsMap.get(getProductIdFromSkuId(response.getSkuId()));
                    if (productData!=null) {
                        Optional<ProductPrice> productPrice = productData.getPrices().stream().filter(e -> entry.getDimension().equalsIgnoreCase(e.getDimension()) && e.getRecipe() != null).findFirst();
                        if (productPrice.isPresent()) {
                            Integer recipeId = productPrice.get().getRecipe().getRecipeId();
                            entry.setRecipeId(recipeId);
                        }
                    }
                    entry.setMenuProductCategory(masterCache.getProductCategory(masterCache.getProduct(entry.getMenuProductId()).getType()).getDetail().getName());
                    entry.setMenuProductCategoryId(masterCache.getProductCategory(masterCache.getProduct(entry.getMenuProductId()).getType()).getDetail().getId());
                    entry.setMenuProductSubCategory(masterCache.getProductSubCategory(masterCache.getProduct(entry.getMenuProductId()).getSubType()).getName());
                    result.add(entry);
                }
                catch (Exception e) {
                    //LOG.error("Error while fetching data from cache during creation of menu output :: ");
                    result.add(entry);
                }
            }
            return result;
        }
        catch (Exception e) {
            LOG.error("Error Occurred while Creating Menu Output data :: ");
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public HashMap<String, Object> generateProductProjections(ProductProjectionsUnits projectionsData, Boolean isUnitsUploaded) throws SumoException, IOException {
        try {
            List<ProductProjectionsUnitDetail> unitsInputData;
            ProductProjectionsDetailsData data;
            if (isUnitsUploaded) {
                data = createProductProjectionsEntry(projectionsData.getUnitsData().get(0).getStartDate(),
                        projectionsData.getUnitsData().get(0).getEndDate(), projectionsData.getGeneratedBy(), true);
                return getProjections(projectionsData.getUnitsData(), data, projectionsData.getGeneratedBy());
            } else {
                unitsInputData = getUnitInputData(AppUtils.getDate(projectionsData.getStartDate(), "yyyy-MM-dd")
                        , AppUtils.getDate(projectionsData.getEndDate(), "yyyy-MM-dd"), null);
                data = createProductProjectionsEntry(AppUtils.getDate(projectionsData.getStartDate(), "yyyy-MM-dd"),
                        AppUtils.getDate(projectionsData.getEndDate(), "yyyy-MM-dd"), projectionsData.getGeneratedBy(), false);
                return getProjections(unitsInputData, data, projectionsData.getGeneratedBy());
            }
        }
        catch (Exception e) {
            LOG.error("Exception Occurred while generating projections :: ",e);
            throw new SumoException("Can not generate Product Projections");
        }
    }

    private HashMap<String,Object> getProjections(List<ProductProjectionsUnitDetail> unitsInputData, ProductProjectionsDetailsData projectionsDetailsData,
                                                  String generatedBy) throws IOException, SumoException {
        try {
            long startOfProjections = System.currentTimeMillis();
            Map<String, Integer> projectionCounters = new HashMap<>();
            Map<String, Long> projectionTimers = new HashMap<>();
            HashMap<String, Object> finalResult = new HashMap<>();
            Set<Integer> unitsIds = new HashSet<>();
            Set<Integer> unitsIdsActive = new HashSet<>();
            Set<Integer> unitsIdsInActive = new HashSet<>();

            for (ProductProjectionsUnitDetail entry : unitsInputData) {
                if (!unitsIds.contains(entry.getUnitId())) {
                    unitsIds.add(entry.getUnitId());
                    if (entry.getStatus().equalsIgnoreCase("ACTIVE[ENABLED]")) {
                        unitsIdsActive.add(entry.getUnitId());
                    } else {
                        unitsIdsInActive.add(entry.getUnitId());
                    }
                }
            }
            LOG.info("SIZE | L1 - getProjections() - Total Units is : {}, Active Units is : {}, In-Active Units is : {}", unitsIds.size(), unitsIdsActive.size(), unitsIdsInActive.size());
            List<ProductProjectionsUnitDetail> menuOutput = new ArrayList<>();
            List<ProductProjectionsUnitDetail> scmOutput = new ArrayList<>();
            List<ProductProjectionsUnitDetail> detailedScmOutput = new ArrayList<>();
            long startOfFountain9 = System.currentTimeMillis();
            LOG.info("Fetching data from Fountain 9 API ...");
            ForecastAuthRequest forecastAuthRequest = new ForecastAuthRequest(env.getFountain9UserName(), env.getFountain9Password(), env.getFountain9IsSuperUser());
            ForecastAuthResponse forecastAuthResponse = WebServiceHelper.postWithAuth(env.getFountain9AccessTokenUrl(), null, forecastAuthRequest, ForecastAuthResponse.class);
            RefCreateRequest refCreateRequest = getRefCreateRequest(projectionsDetailsData);
            Map<String, Integer> uriVariables = new HashMap<>();
            uriVariables.put("dataset_id", 4);
            HttpResponse httpResponse = WebServiceHelper.postWithBearer(env.getFountain9ForecastReportUrl(), forecastAuthResponse.getAccess(), refCreateRequest, uriVariables, true);
            List<ForecastReportResponse> forecastReportResponseList = getForecastReport(httpResponse);
            try {
                if (httpResponse.getEntity() != null && httpResponse.getEntity().getContent() != null) {
                    httpResponse.getEntity().getContent().close();
                }
            } catch (Exception e) {
                LOG.error("Error in closing response content", e);
            }
            LOG.info("TIMING | L1 - getProjections().Fountain9APIFetch is : {}", System.currentTimeMillis() - startOfFountain9);
            LOG.info("SIZE | L1 - getProjections() - FORECAST LIST is {}", forecastReportResponseList.size());

            Map<Integer, List<ForecastReportResponse>> unitWiseResponseList = getUnitWiseResponseList(forecastReportResponseList);
            Map<Integer, List<ProductProjectionsUnitDetail>> menuOutputMap = new HashMap<>();
            Map<Integer, List<ProductProjectionsUnitDetail>> scmOutputMap = new HashMap<>();
            Map<Integer, List<ProductProjectionsUnitDetail>> detailedScmOutputMap = new HashMap<>();
            long startOfActiveUnits = System.currentTimeMillis();
            for (Integer unitId : unitsIdsActive) {
                List<ForecastReportResponse> forecastResponseListForUnit = unitWiseResponseList.getOrDefault(unitId, null);
                if (forecastResponseListForUnit != null && forecastResponseListForUnit.size() > 0) {
                    ProductProjectionsUnitDetail data = unitsInputData.stream().filter(e -> e.getUnitId().equals(unitId)).findFirst().get();
                    long getMenuOutputAndSorting = System.currentTimeMillis();
                    List<ProductProjectionsUnitDetail> sortedMenuList = getMenuOutput(forecastResponseListForUnit, data, null).stream()
                            .sorted(Comparator.comparing(ProductProjectionsUnitDetail::getBusinessDate)).collect(Collectors.toList());
                    LOG.info("TIMING | L1 - getProjections()-(())-getMenuOutput for UnitID : {} is : {}", unitId, System.currentTimeMillis() - getMenuOutputAndSorting);
                    LOG.info("SIZE | L1 - getProjections(()) - Of MenuOutput List for UnitID: {} is {}", unitId, sortedMenuList.size());
                    menuOutput.addAll(sortedMenuList);
                    if (menuOutputMap.containsKey(unitId)) {
                        List<ProductProjectionsUnitDetail> list = menuOutputMap.get(unitId);
                        list.addAll(sortedMenuList);
                        menuOutputMap.put(unitId, list);
                    } else {
                        menuOutputMap.put(unitId, sortedMenuList);
                    }

                    if (sortedMenuList.size() > 0) {
                        long startOfScmOutput = System.currentTimeMillis();
                        List<ProductProjectionsUnitDetail> scmList = getScmOutput(sortedMenuList, projectionCounters, projectionTimers);
                        LOG.info("TIMING | L1 - getProjections()-(())-getSCMOutput for UnitID : {} is : {}", unitId, System.currentTimeMillis() - startOfScmOutput);
                        LOG.info("SIZE | L1 - getProjections-(()) - Of SCM Output List for UnitID: {} is {}", unitId, scmList.size());
                        scmOutput.addAll(scmList);
                        if (scmOutputMap.containsKey(unitId)) {
                            List<ProductProjectionsUnitDetail> list = scmOutputMap.get(unitId);
                            list.addAll(convert(scmList));
                            scmOutputMap.put(unitId, list);
                        } else {
                            scmOutputMap.put(unitId, convert(scmList));
                        }
                        List<ProductProjectionsUnitDetail> semiFinishedScmList = scmList.stream().filter(e -> e.getScmProductCategory().equalsIgnoreCase("Semi Finished"))
                                .collect(Collectors.toList());
                        long startOfSemiFinishedOutput = System.currentTimeMillis();
                        List<ProductProjectionsUnitDetail> semiFinishedOutput = getSemiFinishedOutput(semiFinishedScmList);
                        LOG.info("TIMING | L1 - getProjections()-(())-getSemiFinishedOutput for UnitID : {} is : {}", unitId, System.currentTimeMillis() - startOfSemiFinishedOutput);
                        LOG.info("SIZE | L1 - getProjections-(()) - Of Semi-Finished List for UnitID: {} is {}", unitId, semiFinishedOutput.size());
                        scmList.removeAll(semiFinishedScmList);
                        List<ProductProjectionsUnitDetail> copyOfScmList = convert(scmList);
                        detailedScmOutput.addAll(copyOfScmList);
                        detailedScmOutput.addAll(semiFinishedOutput);

                        if (detailedScmOutputMap.containsKey(unitId)) {
                            List<ProductProjectionsUnitDetail> list = detailedScmOutputMap.get(unitId);
                            list.addAll(copyOfScmList);
                            list.addAll(semiFinishedOutput);
                            detailedScmOutputMap.put(unitId, list);
                        } else {
                            List<ProductProjectionsUnitDetail> list = new ArrayList<>();
                            list.addAll(copyOfScmList);
                            list.addAll(semiFinishedOutput);
                            detailedScmOutputMap.put(unitId, list);
                        }
                    }
                }
            }
            LOG.info("TIMING | L1 - getProjections() - Active Units Projections Total is : {}", System.currentTimeMillis() - startOfActiveUnits);

            long startOfInActiveUnits = System.currentTimeMillis();
            for (Integer unitId : unitsIdsInActive) {
                ProductProjectionsUnitDetail inactiveUnit = unitsInputData.stream().filter(e -> e.getUnitId().equals(unitId)).findFirst().get();
                if (menuOutputMap.containsKey(inactiveUnit.getSalesClonedFrom()) && menuOutputMap.get(inactiveUnit.getSalesClonedFrom()).size() > 0) {
                    if (menuOutputMap.get(inactiveUnit.getSalesClonedFrom()).get(0).getStartDate().compareTo(inactiveUnit.getStartDate()) == 0 &&
                            menuOutputMap.get(inactiveUnit.getSalesClonedFrom()).get(0).getEndDate().compareTo(inactiveUnit.getEndDate()) == 0) {
                        List<ProductProjectionsUnitDetail> copyOfMenuOutput = convert(menuOutputMap.get(inactiveUnit.getSalesClonedFrom()), inactiveUnit);
                        menuOutput.addAll(copyOfMenuOutput);
                        if (scmOutputMap.containsKey(inactiveUnit.getSalesClonedFrom()) && scmOutputMap.get(inactiveUnit.getSalesClonedFrom()).size() > 0) {
                            List<ProductProjectionsUnitDetail> copyOfScmOutput = convert(scmOutputMap.get(inactiveUnit.getSalesClonedFrom()), inactiveUnit);
                            scmOutput.addAll(copyOfScmOutput);

                            if (detailedScmOutputMap.containsKey(inactiveUnit.getSalesClonedFrom()) && detailedScmOutputMap.get(inactiveUnit.getSalesClonedFrom()).size() > 0) {
                                List<ProductProjectionsUnitDetail> copyOfDetailedScmOutput = convert(detailedScmOutputMap.get(inactiveUnit.getSalesClonedFrom()), inactiveUnit);
                                detailedScmOutput.addAll(copyOfDetailedScmOutput);
                            }
                        }
                    } else {
                        LOG.info("Clone Data not found for unit : {} in the map .. Trying to get it :: ", unitId);
                        List<ForecastReportResponse> forecastResponseListForUnit = unitWiseResponseList.getOrDefault(inactiveUnit.getSalesClonedFrom(), null);
                        cloneDataForInActiveUnits(unitsInputData, menuOutput, scmOutput, detailedScmOutput, inactiveUnit, forecastResponseListForUnit, projectionCounters, projectionTimers);
                    }
                } else {
                    LOG.info("Clone Data not found for unit : {} .. Trying to get it ", unitId);
                    List<ForecastReportResponse> forecastResponseListForUnit = unitWiseResponseList.getOrDefault(inactiveUnit.getSalesClonedFrom(), null);
                    cloneDataForInActiveUnits(unitsInputData, menuOutput, scmOutput, detailedScmOutput, inactiveUnit, forecastResponseListForUnit, projectionCounters, projectionTimers);
                }
            }
            LOG.info("TIMING | L1 - getProjections() - In-Active Units Projections Total is : {}", System.currentTimeMillis() - startOfInActiveUnits);

            Map<Integer, ProductProjectionsUnitDetail> finalDetailedScmOutput = new HashMap<>();


            for (ProductProjectionsUnitDetail data : detailedScmOutput) {
                if (finalDetailedScmOutput.containsKey(data.getScmProductId())) {
                    ProductProjectionsUnitDetail foundData = finalDetailedScmOutput.get(data.getScmProductId());
                    foundData.setQuantity(foundData.getQuantity().add(data.getQuantity()));
                    String quantity = String.format("%.3f", foundData.getQuantity());
                    foundData.setQuantity(new BigDecimal(quantity));
                    finalDetailedScmOutput.put(data.getScmProductId(), foundData);
                } else {
                    ProductProjectionsUnitDetail foundData = data;
                    String quantity = String.format("%.3f", foundData.getQuantity());
                    foundData.setQuantity(new BigDecimal(quantity));
                    finalDetailedScmOutput.put(data.getScmProductId(), data);
                }
            }

            List<ProductProjectionsUnitDetail> finalOfDetailedScm = new ArrayList<>();
            for (Map.Entry entry : finalDetailedScmOutput.entrySet()) {
                finalOfDetailedScm.add((ProductProjectionsUnitDetail) entry.getValue());
            }

            finalResult.put("unitsInputData", unitsInputData);
            finalResult.put("menuOutput", menuOutput);
            finalResult.put("scmOutput", scmOutput);
            finalResult.put("finalOfDetailedScm", finalOfDetailedScm);
            finalResult.put("projectionsDetailsData", projectionsDetailsData);

            LOG.info("TIMING | L1 - getProjections().TOTAL : {} ms", System.currentTimeMillis() - startOfProjections);
            return finalResult;
        }
        catch (Exception e) {
            LOG.error("Error Occurred While Generating Projections :: ",e);
            SlackNotificationService.getInstance().sendNotification(env.getEnvType(), "SUMO",
                    SlackNotification.SUPPLY_CHAIN, "Error Occurred While Generating Product Projections " + e);
            throw new SumoException("Can not generate Product Projections");
        }
    }

    private void cloneDataForInActiveUnits(List<ProductProjectionsUnitDetail> unitsInputData, List<ProductProjectionsUnitDetail> menuOutput,
                                           List<ProductProjectionsUnitDetail> scmOutput, List<ProductProjectionsUnitDetail> detailedScmOutput,
                                           ProductProjectionsUnitDetail inactiveUnit, List<ForecastReportResponse> forecastResponseListForUnit,
                                           Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {
        if (forecastResponseListForUnit != null && forecastResponseListForUnit.size() > 0) {
            LOG.info("size of the forecast response for unit is : {}", forecastResponseListForUnit.size());
            Optional<ProductProjectionsUnitDetail> data = unitsInputData.stream().filter(e -> e.getUnitId().equals(inactiveUnit.getSalesClonedFrom())).findFirst();
            ProductProjectionsUnitDetail data2 = null;
            if (data.isPresent()) {
                data2 = data.get();
            } else {
                List<ProductProjectionsUnitDetail> unitInputData = getUnitInputData(inactiveUnit.getStartDate(), inactiveUnit.getEndDate(),
                        inactiveUnit.getSalesClonedFrom());
                LOG.info("Size of the input data is {}", unitInputData.size());
                if (unitInputData.size() > 0) {
                    data2 = unitInputData.get(0);
                }
            }
            if (data2 != null) {
                List<ProductProjectionsUnitDetail> sortedMenuList = getMenuOutput(forecastResponseListForUnit, data2 ,inactiveUnit).stream()
                        .sorted(Comparator.comparing(ProductProjectionsUnitDetail::getBusinessDate)).collect(Collectors.toList());
                LOG.info("size of menu output is {}", sortedMenuList.size());
                List<ProductProjectionsUnitDetail> copyOfMenuOutput = convert(sortedMenuList, inactiveUnit);
                menuOutput.addAll(copyOfMenuOutput);

                if (sortedMenuList.size() > 0) {
                    List<ProductProjectionsUnitDetail> scmList = getScmOutput(sortedMenuList, projectionCounters, projectionTimers);
                    LOG.info("Got result of scm list is {}", scmList.size());
                    List<ProductProjectionsUnitDetail> copyOfScmOutput = convert(scmList, inactiveUnit);
                    scmOutput.addAll(copyOfScmOutput);

                    List<ProductProjectionsUnitDetail> semiFinishedScmList = scmList.stream().filter(e -> e.getScmProductCategory().equalsIgnoreCase("Semi Finished"))
                            .collect(Collectors.toList());
                    List<ProductProjectionsUnitDetail> semiFinishedOutput = getSemiFinishedOutput(semiFinishedScmList);
                    LOG.info("Semi finished list is {}", semiFinishedOutput.size());
                    scmList.removeAll(semiFinishedScmList);
                    List<ProductProjectionsUnitDetail> copyOfScmList = convert(scmList);
                    List<ProductProjectionsUnitDetail> list = new ArrayList<>();
                    list.addAll(copyOfScmList);
                    list.addAll(semiFinishedOutput);
                    List<ProductProjectionsUnitDetail> copyOfDetailedScmOutput = convert(list, inactiveUnit);
                    detailedScmOutput.addAll(copyOfDetailedScmOutput);
                }
            }
        }
    }

    private List<ProductProjectionsUnitDetail> convert(List<ProductProjectionsUnitDetail> availableDataForCloning, ProductProjectionsUnitDetail inactiveUnit) {
        List<ProductProjectionsUnitDetail> result = new ArrayList<>();
        for (ProductProjectionsUnitDetail data : availableDataForCloning) {
            ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
            entry.setStartDate(data.getStartDate());
            entry.setEndDate(data.getEndDate());
            entry.setBrandCode(data.getBrandCode());
            entry.setOrderSource(data.getOrderSource());
            entry.setUnitId(inactiveUnit.getUnitId());
            entry.setUnitName(inactiveUnit.getUnitName());
            entry.setUnitCostCenter(inactiveUnit.getUnitCostCenter());
            entry.setFulfillmentUnit(data.getFulfillmentUnit());
            entry.setStatus(inactiveUnit.getStatus());
            entry.setSalesClonedFrom(inactiveUnit.getSalesClonedFrom());
            entry.setCafeOpeningDate(inactiveUnit.getCafeOpeningDate());
            entry.setBusinessDate(data.getBusinessDate());
            entry.setMenuProductId(data.getMenuProductId());
            entry.setMenuProductName(data.getMenuProductName());
            entry.setMenuProductCategoryId(data.getMenuProductCategoryId());
            entry.setMenuProductCategory(data.getMenuProductCategory());
            entry.setDimension(data.getDimension());
            entry.setQuantity(data.getQuantity());
            entry.setPrice(data.getPrice());
            entry.setSales(data.getSales());
            entry.setRecipeId(data.getRecipeId());
            entry.setScmProductId(data.getScmProductId());
            entry.setScmProductName(data.getScmProductName());
            entry.setScmProductCategory(data.getScmProductCategory());
            entry.setScmProductSubCategory(data.getScmProductSubCategory());
            entry.setUom(data.getUom());
            entry.setScmRecipeId(data.getScmRecipeId());
            result.add(entry);
        }
        return result;
    }

    private List<ProductProjectionsUnitDetail> convert(List<ProductProjectionsUnitDetail> scmList) {
        List<ProductProjectionsUnitDetail> result = new ArrayList<>();
        for (ProductProjectionsUnitDetail data : scmList) {
            ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
            entry.setStartDate(data.getStartDate());
            entry.setEndDate(data.getEndDate());
            entry.setBrandCode(data.getBrandCode());
            entry.setOrderSource(data.getOrderSource());
            entry.setUnitId(data.getUnitId());
            entry.setUnitName(data.getUnitName());
            entry.setUnitCostCenter(data.getUnitCostCenter());
            entry.setFulfillmentUnit(data.getFulfillmentUnit());
            entry.setStatus(data.getStatus());
            entry.setSalesClonedFrom(data.getSalesClonedFrom());
            entry.setCafeOpeningDate(data.getCafeOpeningDate());
            entry.setBusinessDate(data.getBusinessDate());
            entry.setMenuProductId(data.getMenuProductId());
            entry.setMenuProductName(data.getMenuProductName());
            entry.setMenuProductCategoryId(data.getMenuProductCategoryId());
            entry.setMenuProductCategory(data.getMenuProductCategory());
            entry.setDimension(data.getDimension());
            entry.setQuantity(data.getQuantity());
            entry.setPrice(data.getPrice());
            entry.setSales(data.getSales());
            entry.setRecipeId(data.getRecipeId());
            entry.setScmProductId(data.getScmProductId());
            entry.setScmProductName(data.getScmProductName());
            entry.setScmProductCategory(data.getScmProductCategory());
            entry.setScmProductSubCategory(data.getScmProductSubCategory());
            entry.setUom(data.getUom());
            entry.setScmRecipeId(data.getScmRecipeId());
            result.add(entry);
        }
        return result;
    }

    private RefCreateRequest getRefCreateRequest(ProductProjectionsDetailsData projectionsDetailsData) {
        RefCreateRequest refCreateRequest=new RefCreateRequest();
        refCreateRequest.setStart_date(AppUtils.getSQLFormattedDate(projectionsDetailsData.getStartDate()));
        refCreateRequest.setEnd_date(AppUtils.getSQLFormattedDate(projectionsDetailsData.getEndDate()));
        refCreateRequest.setMetric(env.getFountain9Metric());
        refCreateRequest.setTime_granularity(env.getFountain9TimeGranularity());
        refCreateRequest.setCalculate_safety_stock(true);
//        refCreateRequest.setLevel(masterCache.getUnit(referenceOrder.getUnitId()).getCostCenterName());
//        refCreateRequest.setMax_capping_safety_stock_multiplier();
        refCreateRequest.setColumns(Arrays.asList(env.getFountain9Columns().split(",")));
        return refCreateRequest;
    }

    @Override
    public List<ProductProjectionsUnitDetail> getScmOutput(List<ProductProjectionsUnitDetail> sortedMenuList, Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {
        long startGetSCMOutputTime = System.currentTimeMillis();
        Map<Integer, Collection> unitWiseProductDetailMapCache = new HashMap<>();
        List<RequestScmItem> scmProductList = new ArrayList<>();
        Integer currentUnitId = sortedMenuList.get(0).getUnitId();
        Map<Integer,ReferenceOrderScmItem> finalProductsList = new HashMap<>();
        Unit unit = masterCache.getUnit(currentUnitId);
        long getScmOutputTempTime = System.currentTimeMillis();
        List<ReferenceOrderMenuItem> listOfProducts = getListOfProducts(sortedMenuList,unit);
        long getListofProductsTime = System.currentTimeMillis() - getScmOutputTempTime;
        LOG.info("TIMING | L2 - getScmOutput().getListOfProducts for UnitID : {} is : {}", currentUnitId, getListofProductsTime);
        getScmOutputTempTime = System.currentTimeMillis();
        getProductsFromRecipe(unit,listOfProducts,scmProductList, unitWiseProductDetailMapCache, projectionCounters, projectionTimers);
        long getProductsFromRecipeTime = System.currentTimeMillis() - getScmOutputTempTime;
        LOG.info("TIMING | L2 - getScmOutput().getProductsFromRecipe for UnitID : {} is : {}", currentUnitId, getProductsFromRecipeTime);

//        Map<Integer, BigDecimal> currentStock = stockManagementService.getCurrentStock(unit.getId());
//
//        for (RequestScmItem item : scmProductList) {
//            item.setStockAtHand(currentStock.containsKey(item.getId()) ? currentStock.get(item.getId()).floatValue()
//                    : BigDecimal.ZERO.floatValue());
//        }

        scmProductList.forEach(item -> {
            if (item.getUnitOfMeasure().equalsIgnoreCase("PC") || item.getUnitOfMeasure().equalsIgnoreCase("SACHET")) {
                item.setOrderingQuantity((float) Math.ceil(item.getOrderingQuantity()));
            }
            else {
                item.setOrderingQuantity(item.getOrderingQuantity());
            }
        });

        getScmOutputTempTime = System.currentTimeMillis();
        setMOQToProducts(unit,scmProductList);
        long setMOQToProductsTime = System.currentTimeMillis() - getScmOutputTempTime;
        LOG.info("TIMING | L2 - getScmOutput().setMOQToProductsTime for UnitID : {} is : {}", currentUnitId, setMOQToProductsTime);
        List<ProductProjectionsUnitDetail> result = new ArrayList<>();

        for (RequestScmItem data : scmProductList) {
            try {
                ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
                entry.setStartDate(sortedMenuList.get(0).getStartDate());
                entry.setEndDate(sortedMenuList.get(0).getEndDate());
                entry.setUnitId(sortedMenuList.get(0).getUnitId());
                entry.setUnitName(sortedMenuList.get(0).getUnitName());
                entry.setUnitCostCenter(sortedMenuList.get(0).getUnitCostCenter());
                entry.setStatus(sortedMenuList.get(0).getStatus());
                entry.setCafeOpeningDate(sortedMenuList.get(0).getCafeOpeningDate());
                entry.setFulfillmentUnit(scmCache.getFulfillmentUnit(data.getSelectedFulfillmentType(), entry.getUnitId()));
                entry.setScmProductId(data.getId());
                entry.setScmProductName(data.getName());
                entry.setScmProductCategory(data.getCategoryName());
                entry.setScmProductSubCategory(data.getSubCategoryName());
                entry.setUom(data.getUnitOfMeasure());
                String quantity = String.format("%.3f", data.getOrderingQuantity());
                entry.setQuantity(new BigDecimal(quantity));
                result.add(entry);
            }
            catch (Exception e) {
                LOG.error("Exception occurred while creating scm output ",e);
            }
        }
        scmProductList.clear();
        LOG.info("TIMING | L2 - getScmOutput().RemainingBalance for UnitID : {} is : {}", currentUnitId,
                (System.currentTimeMillis() - startGetSCMOutputTime) - (getListofProductsTime + getProductsFromRecipeTime + setMOQToProductsTime));
        LOG.info("TIMING | L3 - getScmOutput().getProductsFromRecipe.addProductIngredients.TOTAL - Unit Id is : {}, Count is : {} and Time is : {}", currentUnitId, projectionCounters.get("ADD_PRODUCT_ING"), projectionTimers.get("ADD_PRODUCT_ING"));
        LOG.info("TIMING | L4 - getScmOutput().getProductsFromRecipe.addProductIngredients.addSCMToProductList.TOTAL - Unit Id is : {}, Count is : {} and Time is : {}", currentUnitId, projectionCounters.get("ADD_SCM_PROD"), projectionTimers.get("ADD_SCM_PROD"));
        LOG.info("TIMING | L3 - getScmOutput().getProductsFromRecipe.addProductAddons.TOTAL - Unit Id is : {}, Count is : {} and Time is : {}", currentUnitId, projectionCounters.get("ADD_PROD_ADDONS"), projectionTimers.get("ADD_PROD_ADDONS"));
        LOG.info("TIMING | L3 - getScmOutput().getProductsFromRecipe.addProductDineInConsumables.TOTAL - Unit Id is : {}, Count is : {} and Time is : {}", currentUnitId, projectionCounters.get("ADD_PROD_DINE_CNSM"), projectionTimers.get("ADD_PROD_DINE_CNSM"));
        LOG.info("TIMING | L3 - getScmOutput().getProductsFromRecipe.addProductDeliveryConsumables.TOTAL - Unit Id is : {}, Count is : {} and Time is : {}", currentUnitId, projectionCounters.get("ADD_PROD_DEL_CNSM"), projectionTimers.get("ADD_PROD_DEL_CNSM"));
        LOG.info("TIMING | L3 - getScmOutput().getProductsFromRecipe.addProductTakeawayConsumables.TOTAL - Unit Id is : {}, Count is : {} and Time is : {}", currentUnitId, projectionCounters.get("ADD_PROD_TKWY_CNSM"), projectionTimers.get("ADD_PROD_TKWY_CNSM"));
        LOG.info("TIMING | L3 - getScmOutput().getProductsFromRecipe.cacheFetchPortion.TOTAL - Unit Id is : {}, Count is : {} and Time is : {}", currentUnitId, projectionCounters.get("ADD_PROD_RECIPE_CACHE"), projectionTimers.get("ADD_PROD_RECIPE_CACHE"));
        return result;
    }

    @Override
    public List<ProductProjectionsUnitDetail> getSemiFinishedOutput(List<ProductProjectionsUnitDetail> semiFinishedScmList) {
        return calculateScmItemForSemiFinished(semiFinishedScmList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ProductProjectionsDetailsData createProductProjectionsEntry(Date startDate, Date endDate, String generatedBy, boolean isUploaded) throws SumoException{
        ProductProjectionsDetailsData entry = new ProductProjectionsDetailsData();
        entry.setStartDate(startDate);
        entry.setEndDate(endDate);
        entry.setGeneratedBy(generatedBy);
        if (isUploaded) {
            entry.setUploadedBy(generatedBy);
        }
        entry.setCreationTime(AppUtils.getCurrentTimestamp());

        entry = productProjectionsDao.add(entry,false);
        return entry;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateProjectionsData(String downloadedPath, ProductProjectionsDetailsData projectionsDetailsData) {
        projectionsDetailsData.setDownloadedPath(downloadedPath);
        projectionsDetailsData.setEndTime(AppUtils.getCurrentTimestamp());
        productProjectionsDao.update(projectionsDetailsData,true);
    }

    @Override
    public Map<Integer, List<ForecastReportResponse>> getUnitWiseResponseList(List<ForecastReportResponse> forecastReportResponseList) {
        Map<Integer, List<ForecastReportResponse>> result = new HashMap<>();
        for (ForecastReportResponse response : forecastReportResponseList) {
            if (result.containsKey(getUnitId(response.getCafeId()))) {
                List<ForecastReportResponse> list = result.get(getUnitId(response.getCafeId()));
                list.add(response);
                result.put(getUnitId(response.getCafeId()), list);
            } else {
                List<ForecastReportResponse> list = new ArrayList<>();
                list.add(response);
                result.put(getUnitId(response.getCafeId()), list);
            }
        }
        return result;
    }

    private Integer getUnitId(String cafeId) {
        String arr[] = cafeId.split("_");
        return Integer.parseInt(arr[0]);
    }

    private List<ProductProjectionsUnitDetail> calculateScmItemForSemiFinished(List<ProductProjectionsUnitDetail> semiFinishedScmList) {
        Map<String, Map<Integer, ProductProjectionsUnitDetail>> itemMap = new HashMap<>();
        itemMap.put(ProductionItemType.REQUESTED.name(), new HashMap<>());
        itemMap.put(ProductionItemType.ADDITIONAL.name(), new HashMap<>());
        itemMap.put(ProductionItemType.SKU.name(), new HashMap<>());
        for (ProductProjectionsUnitDetail data : semiFinishedScmList) {
            addItemDataToMap(data.getScmProductId(), data.getQuantity(), ProductionItemType.REQUESTED.name(),
                    itemMap,data.getUnitId(),data);
            calculateItemsByRecipe(data.getScmProductId(), data.getUnitId(), data.getQuantity(),
                    ProductionItemType.REQUESTED.name(), itemMap,data);
        }
        Map<Integer, ProductProjectionsUnitDetail> skuMap = itemMap.get(ProductionItemType.SKU.name());
        List<ProductProjectionsUnitDetail> result = new ArrayList<>();
        for (Map.Entry data : skuMap.entrySet()) {
            result.add((ProductProjectionsUnitDetail)data.getValue());
        }
        return result;
    }

    private void calculateItemsByRecipe(Integer productId, Integer unitId, BigDecimal parentQuantity, String name, Map<String, Map<Integer, ProductProjectionsUnitDetail>> itemMap, ProductProjectionsUnitDetail data) {
        try {
            RecipeDetail recipe = recipeCache.getScmRecipe(scmCache.getRecipeProfile(unitId, productId));
            ProductDefinition product = scmCache.getProductDefinition(productId);
            if (recipe == null) {
                if (product.isRecipeRequired()) {
                    throw new Exception("Recipe Missing for the product:" + product.getProductName());
                }
            }
            for (IngredientProductDetail pd : recipe.getIngredient().getComponents()) {
                ProductDefinition def = scmCache.getProductDefinition(pd.getProduct().getProductId());
                BigDecimal quantity = parentQuantity.multiply(pd.getQuantity());
                quantity = AppUtils.getQuantityByYield(quantity, pd.getYield());
                if (def != null && def.isRecipeRequired() && def.isAutoProduction()) {
                    addItemDataToMap(pd.getProduct().getProductId(), quantity, ProductionItemType.ADDITIONAL.name(),
                            itemMap,unitId,data);
                    calculateItemsByRecipe(pd.getProduct().getProductId(), unitId, quantity, ProductionItemType.ADDITIONAL.name(),
                            itemMap, data);
                } else {
                    addItemDataToMap(pd.getProduct().getProductId(), quantity, ProductionItemType.SKU.name(), itemMap,unitId,data);
                }
            }
        }
        catch (Exception e) {
            //LOG.error("Error occurred while calculating items by recipe for product ID {} and unit id is : {}",productId,unitId);
        }
    }

    private void addItemDataToMap(Integer productId, BigDecimal quantity, String itemType, Map<String, Map<Integer, ProductProjectionsUnitDetail>> itemMap, Integer unitId, ProductProjectionsUnitDetail data) {
        ProductProjectionsUnitDetail itemData = null;
        if (itemMap.get(itemType).containsKey(productId)) {
            itemData = itemMap.get(itemType).get(productId);
        }
        else {
            itemData = createNewProjectionItem(productId, itemType,unitId,data);
        }
        itemData.setQuantity(itemData.getQuantity().add(quantity));
        itemMap.get(itemType).put(productId, itemData);
    }

    private ProductProjectionsUnitDetail createNewProjectionItem(Integer productId, String itemType, Integer unitId, ProductProjectionsUnitDetail data) {
        ProductProjectionsUnitDetail entry = new ProductProjectionsUnitDetail();
        ProductDefinition productDef = scmCache.getProductDefinition(productId);
        entry.setStartDate(data.getStartDate());
        entry.setEndDate(data.getEndDate());
        entry.setUnitId(data.getUnitId());
        entry.setUnitName(data.getUnitName());
        entry.setUnitCostCenter(data.getUnitCostCenter());
        entry.setStatus(data.getStatus());
        entry.setFulfillmentUnit(data.getFulfillmentUnit());
        entry.setScmProductId(productDef.getProductId());
        entry.setScmProductName(productDef.getProductName());
        entry.setUom(productDef.getUnitOfMeasure());
        entry.setScmProductCategory(productDef.getCategoryDefinition().getName());
        entry.setScmProductSubCategory(productDef.getSubCategoryDefinition().getName());
        entry.setQuantity(BigDecimal.ZERO);
        return entry;
    }

    private void setMOQToProducts(Unit unit, List<RequestScmItem> scmProductList) {
        Map<Integer, List<UnitProductPackagingMapping>> unitProductPackagingMap = scmProductManagementService.viewUnitProductPackagingMapping(unit.getId());
        Map<Integer, List<ProductPackagingMapping>> productPackaging = scmProductManagementService.viewAllProductPackagingMapping();
        Map<Integer, PackagingDefinition> packagingDef = scmProductManagementService.getAllPackagingMap();
        scmProductList.forEach(item -> {
            productPackaging.get(item.getId()).forEach(pack -> {
                if (pack.getMappingStatus().value().equalsIgnoreCase("ACTIVE") && pack.isIsDefault()) {
                    item.setPackagingName(packagingDef.get(pack.getPackagingId()).getPackagingName());
                    item.setConversionRatio(packagingDef.get(pack.getPackagingId()).getConversionRatio());
                    item.setPackagingQuantity((int) Math.ceil((item.getOrderingQuantity()/item.getConversionRatio())));
                    item.setOrderingQuantity(item.getPackagingQuantity() * item.getConversionRatio());
                }
            });
            if (unitProductPackagingMap != null && unitProductPackagingMap.get(item.getId()) != null) {
                unitProductPackagingMap.get(item.getId()).forEach(pack -> {
                    item.setPackagingName(packagingDef.get(pack.getPackagingId()).getPackagingName());
                    item.setConversionRatio(packagingDef.get(pack.getPackagingId()).getConversionRatio());
                });
            }
        });
    }

    private void getProductsFromRecipe(Unit unit, List<ReferenceOrderMenuItem> listOfProducts, List<RequestScmItem> scmProductList, Map<Integer, Collection> unitWiseProductDetailMapCache,
                                       Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {
        long getProductsFromRecipeCacheTime = System.currentTimeMillis();
        if(!unitWiseProductDetailMapCache.containsKey(unit.getId())) {
            unitWiseProductDetailMapCache.put(unit.getId(), masterCache.getUnitProductDetails().get(unit.getId()));
        }
        Collection productDetailsForUnit = unitWiseProductDetailMapCache.get(unit.getId());
        long  getProductsFromRecipeCacheTotalTime = System.currentTimeMillis() - getProductsFromRecipeCacheTime;
        projectionCounters.put("ADD_PROD_RECIPE_CACHE", !projectionCounters.containsKey("ADD_PROD_RECIPE_CACHE") ? 0 : projectionCounters.get("ADD_PROD_RECIPE_CACHE") + 1 );
        projectionTimers.put("ADD_PROD_RECIPE_CACHE", !projectionTimers.containsKey("ADD_PROD_RECIPE_CACHE") ? 0 : projectionTimers.get("ADD_PROD_RECIPE_CACHE") + getProductsFromRecipeCacheTotalTime );
        listOfProducts.forEach(product -> {
            if (product.getQuantity() != null && product.getQuantity() >= 0) {
                productDetailsForUnit.forEach(item -> {
                    if (((Product)item).getId() == product.getProductId()) {
                        ((Product)item).getPrices().forEach(price -> {
                            if (price.getDimension().equalsIgnoreCase(product.getDimension()) && price.getRecipe() != null) {
                                addProductIngredients(unit,product, price, scmProductList, projectionCounters, projectionTimers);
                                addProductAddons(unit,product, price, scmProductList, unitWiseProductDetailMapCache, projectionCounters, projectionTimers);
                                addProductDineInConsumables(unit,product, price, scmProductList, projectionCounters, projectionTimers);
                                addProductDeliveryConsumables(unit,product, price, scmProductList, projectionCounters, projectionTimers);
                                addProductTakeawayConsumables(unit,product, price, scmProductList, projectionCounters, projectionTimers);
                            }
                        });
                    }
                });
            }
        });
    }

    private void addProductTakeawayConsumables(Unit unit, ReferenceOrderMenuItem product, ProductPrice price, List<RequestScmItem> scmProductList, Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {
        long  addProductTakeawayInConsumablesTime = System.currentTimeMillis();
        if (product.getTakeawayQuantity() != null || unit.getFamily().value().equalsIgnoreCase("TAKE_AWAY")) {
            Float val = product.getTakeawayQuantity() != null ? product.getTakeawayQuantity() : product.getQuantity();
            price.getRecipe().getTakeawayConsumables().forEach(takeawayConsumable -> {
                if (takeawayConsumable.getStatus().equalsIgnoreCase("ACTIVE")) {
                    addToScmProductsList(takeawayConsumable.getProduct(), val * takeawayConsumable.getQuantity().floatValue() , new Float(0),unit,scmProductList, projectionCounters, projectionTimers);
                }
            });
        }
        long  addProductTakeawayInConsumablesTotalTime = System.currentTimeMillis() - addProductTakeawayInConsumablesTime;
        projectionCounters.put("ADD_PROD_TKWY_CNSM", !projectionCounters.containsKey("ADD_PROD_TKWY_CNSM") ? 0 : projectionCounters.get("ADD_PROD_TKWY_CNSM") + 1 );
        projectionTimers.put("ADD_PROD_TKWY_CNSM", !projectionTimers.containsKey("ADD_PROD_TKWY_CNSM") ? 0 : projectionTimers.get("ADD_PROD_TKWY_CNSM") + addProductTakeawayInConsumablesTotalTime );
    }

    private void addProductDeliveryConsumables(Unit unit, ReferenceOrderMenuItem product, ProductPrice price, List<RequestScmItem> scmProductList, Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {
        long  addProductDeliveryInConsumablesTime = System.currentTimeMillis();
        if (product.getDeliveryQuantity() != null || unit.getFamily().value().equalsIgnoreCase("DELIVERY")) {
            Float val = product.getDeliveryQuantity() != null ? product.getDeliveryQuantity() : product.getQuantity();
            price.getRecipe().getDeliveryConsumables().forEach(deliveryConsumable -> {
                if (deliveryConsumable.getStatus().equalsIgnoreCase("ACTIVE")) {
                    addToScmProductsList(deliveryConsumable.getProduct(), val * deliveryConsumable.getQuantity().floatValue() , new Float(0), unit, scmProductList, projectionCounters, projectionTimers);
                }
            });
        }
        long  addProductDeliveryInConsumablesTotalTime = System.currentTimeMillis() - addProductDeliveryInConsumablesTime;
        projectionCounters.put("ADD_PROD_DEL_CNSM", !projectionCounters.containsKey("ADD_PROD_DEL_CNSM") ? 0 : projectionCounters.get("ADD_PROD_DEL_CNSM") + 1 );
        projectionTimers.put("ADD_PROD_DEL_CNSM", !projectionTimers.containsKey("ADD_PROD_DEL_CNSM") ? 0 : projectionTimers.get("ADD_PROD_DEL_CNSM") + addProductDeliveryInConsumablesTotalTime );
    }

    private void addProductDineInConsumables(Unit unit, ReferenceOrderMenuItem product, ProductPrice price, List<RequestScmItem> scmProductList, Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {
        long  addProductDineInConsumablesTime = System.currentTimeMillis();
        if (product.getDineInQuantity() != null || unit.getFamily().value().equalsIgnoreCase("CAFE")) {
            Float val = product.getDineInQuantity() != null ? product.getDineInQuantity() : product.getQuantity();
            price.getRecipe().getDineInConsumables().forEach(dineInConsumable -> {
                if (dineInConsumable.getStatus().equalsIgnoreCase("ACTIVE")) {
                    addToScmProductsList(dineInConsumable.getProduct(), val * dineInConsumable.getQuantity().floatValue() , new Float(0), unit, scmProductList, projectionCounters, projectionTimers);
                }
            });
        }
        long  addProductDineInConsumablesTotalTime = System.currentTimeMillis() - addProductDineInConsumablesTime;
        projectionCounters.put("ADD_PROD_DINE_CNSM", !projectionCounters.containsKey("ADD_PROD_DINE_CNSM") ? 0 : projectionCounters.get("ADD_PROD_DINE_CNSM") + 1 );
        projectionTimers.put("ADD_PROD_DINE_CNSM", !projectionTimers.containsKey("ADD_PROD_DINE_CNSM") ? 0 : projectionTimers.get("ADD_PROD_DINE_CNSM") + addProductDineInConsumablesTotalTime );
    }

    private void addProductAddons(Unit unit, ReferenceOrderMenuItem product, ProductPrice price, List<RequestScmItem> scmProductList, Map<Integer, Collection> unitWiseProductDetailMapCache,
                                  Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {
        long addProductAddonsTime = System.currentTimeMillis();
        List<ReferenceOrderMenuItem> addOnList = new ArrayList<>();
        price.getRecipe().getAddons().forEach(addon -> {
            if (addon.getStatus().equalsIgnoreCase("ACTIVE")) {
                ReferenceOrderMenuItem item = new ReferenceOrderMenuItem();
                item.setProductId(addon.getProduct().getProductId());
                item.setProductName(addon.getProduct().getName());
                item.setDimension(addon.getDimension().getCode());
                item.setQuantity(product.getQuantity() * addon.getQuantity().floatValue());
                item.setVariants(new ArrayList<>());
                addOnList.add(item);
            }
        });
        long  addProductAddonsTotalTime = System.currentTimeMillis() - addProductAddonsTime;
        projectionCounters.put("ADD_PROD_ADDONS", !projectionCounters.containsKey("ADD_PROD_ADDONS") ? 0 : projectionCounters.get("ADD_PROD_ADDONS") + 1 );
        projectionTimers.put("ADD_PROD_ADDONS", !projectionTimers.containsKey("ADD_PROD_ADDONS") ? 0 : projectionTimers.get("ADD_PROD_ADDONS") + addProductAddonsTotalTime );
        getProductsFromRecipe(unit, addOnList, scmProductList, unitWiseProductDetailMapCache, projectionCounters, projectionTimers);
    }

    private void addProductIngredients(Unit unit, ReferenceOrderMenuItem product, ProductPrice price, List<RequestScmItem> scmProductList, Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {

        long addProductIngTime = System.currentTimeMillis();

        price.getRecipe().getIngredient().getComponents().forEach(component -> {
            if (component.getStatus().equalsIgnoreCase("ACTIVE")) {
                addToScmProductsList(component.getProduct(),product.getQuantity() * component.getQuantity().floatValue(), new Float(0), unit, scmProductList, projectionCounters, projectionTimers);
            }
        });

        product.getVariants().forEach(variant -> {
            price.getRecipe().getIngredient().getVariants().forEach(xvariant -> {
                if (xvariant.getStatus().equalsIgnoreCase("ACTIVE")) {
                    xvariant.getDetails().forEach(xvarDetail -> {
                        if (xvarDetail.getAlias().equalsIgnoreCase(variant.getName())) {
                            if (variant.getVariantLevelOrdering()) {
                                addToScmProductsList(xvariant.getProduct(),variant.getOrderedQuantity() * xvarDetail.getQuantity().floatValue(),new Float(0), unit, scmProductList, projectionCounters, projectionTimers);
                            }
                            else {
                                addToScmProductsList(xvariant.getProduct(),product.getQuantity() * xvarDetail.getQuantity().floatValue(),new Float(0), unit, scmProductList, projectionCounters, projectionTimers);
                            }
                        }
                    });
                }
            });
        });

        price.getRecipe().getIngredient().getProducts().forEach(prod -> {
            if (prod.getStatus().equalsIgnoreCase("ACTIVE")) {
                int plength = prod.getDetails().size();
                prod.getDetails().forEach(det -> {
                    if (det.isDefaultSetting()) {
                        addToScmProductsList(det.getProduct(),((product.getQuantity()/2) * det.getQuantity().floatValue()),new Float(0), unit, scmProductList, projectionCounters, projectionTimers);
                    }
                    else {
                        addToScmProductsList(det.getProduct(),((product.getQuantity()/((2*plength)-1)) * det.getQuantity().floatValue()),new Float(0), unit, scmProductList, projectionCounters, projectionTimers);
                    }
                });
            }
        });
        long  addProductIngTotal = System.currentTimeMillis() - addProductIngTime;
        projectionCounters.put("ADD_PRODUCT_ING", !projectionCounters.containsKey("ADD_PRODUCT_ING") ? 0 : projectionCounters.get("ADD_PRODUCT_ING") + 1 );
        projectionTimers.put("ADD_PRODUCT_ING", !projectionTimers.containsKey("ADD_PRODUCT_ING") ? 0 : projectionTimers.get("ADD_PRODUCT_ING") + addProductIngTotal );
    }

    private List<ReferenceOrderMenuItem> getListOfProducts(List<ProductProjectionsUnitDetail> sortedMenuList, Unit unit) {
        List<ReferenceOrderMenuItem> result = new ArrayList<>();
        Collection<Product> prodList = masterCache.getUnitProductDetails().get(unit.getId());
//        Collection<Product> prodList = masterCache.getUnit(unit.getId()).getProducts();
        sortedMenuList.forEach(menuItem -> {
            List<Product> filtered = prodList.stream().filter(e -> e.getId() == menuItem.getMenuProductId()).collect(Collectors.toList());
            filtered.forEach(p -> {
            if (p != null) {
                if (p.getTaxCode() != null) {
                    p.getPrices().forEach(price -> {
                        if (!p.getClassification().value().equals("FREE_ADDON") && p.getStatus().value().equals("ACTIVE") && menuItem.getDimension().equalsIgnoreCase(price.getDimension())) {
                            ReferenceOrderMenuItem item = new ReferenceOrderMenuItem();
                            item.setProductId(p.getId());
                            item.setProductName(p.getName());
                            item.setDimension(menuItem.getDimension());
                            item.setQuantity(menuItem.getQuantity().floatValue());
                            item.setVariants(getVariants(p, item.getDimension()));
                            result.add(item);
                        }
                    });
                }
            }
        });
        });
        return result;
    }

    private List<ReferenceOrderMenuVariant> getVariants(Product p, String dimension) {
        List<ReferenceOrderMenuVariant> result = new ArrayList<>();
        p.getPrices().forEach(price ->{
            if (price.getRecipe() != null && price.getDimension().equalsIgnoreCase(dimension)) {
                price.getRecipe().getIngredient().getVariants().forEach(variant -> {
                    if (variant.getStatus().equalsIgnoreCase("ACTIVE")) {
                        if (variant.getProduct().isVariantLevelOrdering()) {
                            variant.getDetails().forEach(detail -> {
                                ReferenceOrderMenuVariant entry= new ReferenceOrderMenuVariant();
                                entry.setName(detail.getAlias());
                                entry.setConversionQuantity(detail.getQuantity().floatValue());
                                entry.setDefault(detail.isDefaultSetting());
                                entry.setDimension(price.getDimension());
                                entry.setVariantLevelOrdering(variant.getProduct().isVariantLevelOrdering());
                                result.add(entry);
                            });
                        }
                        else {
                            variant.getDetails().forEach(detail -> {
                                if (detail.getStatus().equalsIgnoreCase("ACTIVE") && detail.isDefaultSetting()) {
                                    ReferenceOrderMenuVariant entry= new ReferenceOrderMenuVariant();
                                    entry.setName(detail.getAlias());
                                    entry.setConversionQuantity(detail.getQuantity().floatValue());
                                    entry.setDefault(detail.isDefaultSetting());
                                    entry.setDimension(price.getDimension());
                                    entry.setVariantLevelOrdering(variant.getProduct().isVariantLevelOrdering());
                                    result.add(entry);
                                }
                            });
                        }
                    }
                });
            }
        });
        return result;
    }

    private void addToScmProductsList(ProductData product, Float quantity, Float saleQuantity, Unit unit, List<RequestScmItem> scmProductList, Map<String, Integer> projectionCounters, Map<String, Long> projectionTimers) {
        long addToScmProdTime = System.currentTimeMillis();
        boolean found = false;
        for (RequestScmItem item : scmProductList) {
            if (item.getId() == product.getProductId()) {
                item.setSuggestedQuantity((item.getSuggestedQuantity() + quantity));
                item.setOrderingQuantity(item.getSuggestedQuantity());
                item.setSaleQuantity((item.getSaleQuantity() + saleQuantity));
                found = true;
                break;
            }
        }
        if (!found) {
            List<ProductDefinition> activeScmProducts = scmProductManagementService.viewAllProducts(unit.getId(), false).stream()
                    .filter(e -> (e.isAvailableAtCafe() || (unit.getFamily().value().equalsIgnoreCase("WAREHOUSE") || unit.getFamily().value().equalsIgnoreCase("KITCHEN")))).
                            filter(e -> e.getProductStatus().value().equalsIgnoreCase("ACTIVE")).collect(Collectors.toList());
            activeScmProducts.forEach(item -> {
                if (item.getProductStatus().value().equalsIgnoreCase("ACTIVE") && item.getProductId() == product.getProductId()
                        && item.getFulfillmentType() != null) {
                    try {
                        CategoryDefinition categoryDef = null;
                        if (Objects.nonNull(item.getCategoryDefinition()) && Objects.nonNull(item.getCategoryDefinition().getId()) &&
                            Objects.nonNull(scmCache.getCategoryDefinitions()) && Objects.nonNull(scmCache.getCategoryDefinitions().get(item.getCategoryDefinition().getId()))) {
                            categoryDef = scmCache.getCategoryDefinitions().get(item.getCategoryDefinition().getId());
                        }
                        SubCategoryDefinition subCategoryDef = null;
                        if (Objects.nonNull(item.getSubCategoryDefinition()) && Objects.nonNull(item.getSubCategoryDefinition().getId())
                        && Objects.nonNull(scmCache.getSubCategoryDefinitions()) && Objects.nonNull(scmCache.getSubCategoryDefinitions().get(item.getSubCategoryDefinition().getId()))) {
                            subCategoryDef = scmCache.getSubCategoryDefinitions().get(item.getSubCategoryDefinition().getId());
                        }
                        RequestScmItem entry = new RequestScmItem();
                        entry.setId(item.getProductId());
                        entry.setName(item.getProductName());
                        if (Objects.nonNull(categoryDef)) {
                            entry.setCategoryName(categoryDef.getCategoryName());
                        }
                        else {
                            entry.setCategoryName("NA");
                        }
                        if (Objects.nonNull(subCategoryDef)) {
                            entry.setSubCategoryName(subCategoryDef.getSubCategoryName());
                        }
                        else {
                            entry.setSubCategoryName("NA");
                        }
                        entry.setSuggestedQuantity(quantity);
                        entry.setSaleQuantity(saleQuantity);
                        entry.setUnitOfMeasure(item.getUnitOfMeasure());
                        entry.setOrderingQuantity(quantity);
                        entry.setSelectedFulfillmentType(getFulfilmentType(item, unit));
                        scmProductList.add(entry);
                    }
                    catch (Exception e) {
                        LOG.error("Error while adding product to productList :: ");
                    }
                }
            });
        }
        long  addToScmProdTotalTime = System.currentTimeMillis() - addToScmProdTime;
        projectionCounters.put("ADD_SCM_PROD", !projectionCounters.containsKey("ADD_SCM_PROD")? 0 : projectionCounters.get("ADD_SCM_PROD") + 1 );
        projectionTimers.put("ADD_SCM_PROD", !projectionTimers.containsKey("ADD_SCM_PROD") ? 0 : projectionTimers.get("ADD_SCM_PROD") + addToScmProdTotalTime );
    }

    private FulfillmentType getFulfilmentType(ProductDefinition product, Unit unit) {
        FulfillmentType fulfillmentType = product.getFulfillmentType();
        if (product.getFulfillmentType().value().equalsIgnoreCase("DERIVED")) {
            if (product.getDerivedMappings().size() > 0) {
                for (DerivedMapping maping : product.getDerivedMappings()) {
                    if (maping.getUnit() == unit.getId()) {
                        fulfillmentType = maping.getType();
                        break;
                    }
                }
            }
            else {
                fulfillmentType = product.getDefaultFulfillmentType();
            }
        }
        return fulfillmentType;
    }
}
