/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;


@Entity
@Table(name = "COMPANY_BANK_MAPPING")
public class CompanyBankMapping implements java.io.Serializable {

	
	private static final long serialVersionUID = 6056330838231520834L;
	private Integer bankMappingId;
	private int companyId;
	private String bankCode;
	private String bankName;
	private String bankAccountNo;
	private String bankIFSCCode;
	private String benificiaryName;
	private String clientCode;
	private String accountStatus;
	
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COMPANY_BANK_MAPPING_ID", unique = true, nullable = false)
	public Integer getBankMappingId() {
		return bankMappingId;
	}

	public void setBankMappingId(Integer bankMappingId) {
		this.bankMappingId = bankMappingId;
	}
	
	@Column(name = "COMPANY_ID", nullable = false)
	public int getCompanyId() {
		return companyId;
	}

	public void setCompanyId(int companyId) {
		this.companyId = companyId;
	}
	
	@Column(name = "BANK_CODE", nullable = false)
	public String getBankCode() {
		return bankCode;
	}

	public void setBankCode(String bankCode) {
		this.bankCode = bankCode;
	}
	
	@Column(name = "BANK_NAME", nullable = false)
	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	@Column(name = "BANK_ACCOUNT_NO", nullable = false, unique = true)
	public String getBankAccountNo() {
		return bankAccountNo;
	}

	public void setBankAccountNo(String bankAccountNo) {
		this.bankAccountNo = bankAccountNo;
	}
	
	@Column(name = "BANK_IFSC_CODE", nullable = false)
	public String getBankIFSCCode() {
		return bankIFSCCode;
	}

	public void setBankIFSCCode(String bankIFSCCode) {
		this.bankIFSCCode = bankIFSCCode;
	}
	
	@Column(name = "BENIFICIARY_NAME", nullable = false)
	public String getBenificiaryName() {
		return benificiaryName;
	}

	public void setBenificiaryName(String benificiaryName) {
		this.benificiaryName = benificiaryName;
	}

	@Column(name = "CLIENT_CODE", nullable = true)
	public String getClientCode() {
		return clientCode;
	}

	public void setClientCode(String clientCode) {
		this.clientCode = clientCode;
	}

	@Column(name = "ACCOUNT_STATUS", nullable = true)
	public String getAccountStatus() {
		return accountStatus;
	}

	public void setAccountStatus(String accountStaus) {
		this.accountStatus = accountStaus;
	}
	
	
}
