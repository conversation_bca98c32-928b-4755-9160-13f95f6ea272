package com.stpl.tech.scm.data.model;

import javax.persistence.*;

@Entity
@Table(name = "SCM_DAY_CLOSE_CHECK_EXCLUSION_LIST")
public class ScmDayCloseCheckExclusionList {
    @Id
    @Column(name = "SCM_DAY_CLOSE_CHECK_EXCLUSION_LIST_ID")
    private Integer scmDayCloseCheckExclusionListId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "STATUS")
    private String status;

    public Integer getScmDayCloseCheckExclusionListId() {
        return this.scmDayCloseCheckExclusionListId;
    }

    public void setScmDayCloseCheckExclusionListId(Integer scmDayCloseCheckExclusionListId) {
        this.scmDayCloseCheckExclusionListId = scmDayCloseCheckExclusionListId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
