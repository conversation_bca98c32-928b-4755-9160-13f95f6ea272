package com.stpl.tech.scm.core.util.webservice;

/**
 * Created by <PERSON><PERSON><PERSON> on 06-10-2016.
 */
public class KettleServiceClientEndpoints {

	public static final String KETTLE_SERVICE_ENTRY_POINT = "/kettle-service/rest/v1/";
    public static final String GET_UNIT_ITEM_ESTIMATE = KETTLE_SERVICE_ENTRY_POINT + "pos-metadata/get-item-estimate";
    public static final String VALIDATE_KETTLE_DAY_CLOSE = KETTLE_SERVICE_ENTRY_POINT + "pos-metadata/validate-kettle-day-close";
    public static final String GET_PNL_UNITS = KETTLE_SERVICE_ENTRY_POINT + "budget-metadata/pnl-units";
    public static final String GET_PNL_UNITS_BY_BUSINESS_DATE = KETTLE_SERVICE_ENTRY_POINT + "budget-metadata/pnl-units-by-date";
    public static final String GET_PNL_UNITS_BY_BUSINESS_DATE_FINALIZED = KETTLE_SERVICE_ENTRY_POINT + "budget-metadata/pnl-units-by-date/finalized";    
    public static final String PUT_PNL_DATA = KETTLE_SERVICE_ENTRY_POINT + "budget-metadata/save-pnl-units";
    public static final String PUT_PNL_DATA_FINALIZED = KETTLE_SERVICE_ENTRY_POINT + "budget-metadata/save-pnl-units/finalized";
    public static final String APPLY_BUDGET_CONSTRAINT = KETTLE_SERVICE_ENTRY_POINT + "budget-metadata/budget-check-RO";
    public static final String GET_DAY_CLOSE_ESTIMATES_DATA = KETTLE_SERVICE_ENTRY_POINT + "order-management/day-close-estimates-data";

}
