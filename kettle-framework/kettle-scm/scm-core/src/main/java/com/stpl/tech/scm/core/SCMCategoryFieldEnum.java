package com.stpl.tech.scm.core;

public enum SCMCategoryFieldEnum {
	SUB_CATEGORY_UTILITY("consumableUtility", 14), SUB_CATEGORY_STATIONERY("consumableStationary",
			12), SUB_CATEGORY_UNIFORM("consumableUniform", 13), SUB_CATEGORY_EQUIPMENT("consumableEquipment",
					11), SUB_CATEGORY_CUTLERY("consumableCutlery", 10), CATEGORY_FIXED_ASSETS("fixedAssets",
							3), SUB_CATEGORY_MARKETING("consumableMarketing",
									15), SUB_CATEGORY_OTHERS("consumableOthers", -1);
	private final String key;
	private final int value;

	SCMCategoryFieldEnum(String key, int value) {
		this.key = key;
		this.value = value;
	}

	public String getKey() {
		return key;
	}

	public int getValue() {
		return value;
	}
	
	public static void main(String[] args) {
		System.out.println(SUB_CATEGORY_CUTLERY.getKey());
		System.out.println(SUB_CATEGORY_CUTLERY.getValue());
		System.err.println(SUB_CATEGORY_CUTLERY.valueOf(SCMCategoryFieldEnum.class, "SUB_CATEGORY_UTILITY"));
	}
}
