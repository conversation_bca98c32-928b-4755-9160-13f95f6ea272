package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 11-06-2016.
 */
@Entity
@Table(name = "GOODS_RECEIVED_ITEM")
public class GoodsReceivedItemData {

	private Integer id;
	private int skuId;
	private String skuName;
	private BigDecimal transferredQuantity;
	private BigDecimal receivedQuantity;
	private String unitOfMeasure;
	private BigDecimal unitPrice;
	private BigDecimal negotiatedUnitPrice;
	private BigDecimal calculatedAmount;
	private List<SCMOrderPackagingData> packagingDetails = new ArrayList<SCMOrderPackagingData>(0);
	private RequestOrderItemData requestOrderItemData;
	private PurchaseOrderItemData purchaseOrderItemData;
	private TransferOrderItemData transferOrderItemData;
	private GoodsReceivedData goodsReceivedData;
	private List<GoodsReceivedItemDrilldown> itemDrilldowns = new ArrayList<GoodsReceivedItemDrilldown>(0);
	private Integer associatedAssetId;
	private  String associatedAssetTagValue;
	private BigDecimal excessQuantity;
	private BigDecimal taxAmount;
	private Integer categoryId;
	private Integer subCategoryId;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "GOODS_RECEIVED_ITEM_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "SKU_ID", nullable = false)
	public int getSkuId() {
		return skuId;
	}

	public void setSkuId(int skuId) {
		this.skuId = skuId;
	}

	@Column(name = "SKU_NAME", nullable = false)
	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	@Column(name = "TRANSFERRED_QUANTITY", nullable = true)
	public BigDecimal getTransferredQuantity() {
		return transferredQuantity;
	}

	public void setTransferredQuantity(BigDecimal transferredQuantity) {
		this.transferredQuantity = transferredQuantity;
	}

	@Column(name = "RECEIVED_QUANTITY", nullable = true)
	public BigDecimal getReceivedQuantity() {
		return receivedQuantity;
	}

	public void setReceivedQuantity(BigDecimal receivedQuantity) {
		this.receivedQuantity = receivedQuantity;
	}

	@Column(name = "UNIT_OF_MEASURE", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@Column(name = "UNIT_PRICE", nullable = true)
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	@Column(name = "NEGOTIATED_UNIT_PRICE", nullable = true)
	public BigDecimal getNegotiatedUnitPrice() {
		return negotiatedUnitPrice;
	}

	public void setNegotiatedUnitPrice(BigDecimal negotiatedUnitPrice) {
		this.negotiatedUnitPrice = negotiatedUnitPrice;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "goodsReceivedItemData", cascade = CascadeType.PERSIST)
	public List<SCMOrderPackagingData> getPackagingDetails() {
		return packagingDetails;
	}

	public void setPackagingDetails(List<SCMOrderPackagingData> packagingDetails) {
		this.packagingDetails = packagingDetails;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REQUEST_ORDER_ITEM_ID", nullable = true)
	public RequestOrderItemData getRequestOrderItemData() {
		return requestOrderItemData;
	}

	public void setRequestOrderItemData(RequestOrderItemData requestOrderItemData) {
		this.requestOrderItemData = requestOrderItemData;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TRANSFER_ORDER_ITEM_ID", nullable = true)
	public TransferOrderItemData getTransferOrderItemData() {
		return transferOrderItemData;
	}

	public void setTransferOrderItemData(TransferOrderItemData transferOrderItemData) {
		this.transferOrderItemData = transferOrderItemData;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "GOODS_RECEIVED_ID", nullable = false)
	public GoodsReceivedData getGoodsReceivedData() {
		return goodsReceivedData;
	}

	public void setGoodsReceivedData(GoodsReceivedData goodsReceivedData) {
		this.goodsReceivedData = goodsReceivedData;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PURCHASE_ORDER_ITEM_ID", nullable = true)
	public PurchaseOrderItemData getPurchaseOrderItemData() {
		return purchaseOrderItemData;
	}

	public void setPurchaseOrderItemData(PurchaseOrderItemData purchaseOrderItemData) {
		this.purchaseOrderItemData = purchaseOrderItemData;
	}

	@Column(name = "CALCULATED_AMOUNT", nullable = true)
	public BigDecimal getCalculatedAmount() {
		return calculatedAmount;
	}

	public void setCalculatedAmount(BigDecimal calculatedAmount) {
		this.calculatedAmount = calculatedAmount;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "receivedItemData", cascade = CascadeType.PERSIST)
	public List<GoodsReceivedItemDrilldown> getItemDrilldowns() {
		return itemDrilldowns;
	}

	public void setItemDrilldowns(List<GoodsReceivedItemDrilldown> itemDrilldowns) {
		this.itemDrilldowns = itemDrilldowns;
	}

	@Column(name = "ASSOCIATED_ASSET_ID", nullable = true)
	public Integer getAssociatedAssetId() {
		return associatedAssetId;
	}

	public void setAssociatedAssetId(Integer associatedAssetId) {
		this.associatedAssetId = associatedAssetId;
	}

	@Column(name = "ASSOCIATED_ASSET_TAG_VALUE", nullable = true)
	public String getAssociatedAssetTagValue() {
		return associatedAssetTagValue;
	}

	public void setAssociatedAssetTagValue(String associatedAssetTagValue) {
		this.associatedAssetTagValue = associatedAssetTagValue;
	}

	@Column(name = "EXCESS_QUANTITY", nullable = true)
	public BigDecimal getExcessQuantity() {
		return excessQuantity;
	}

	public void setExcessQuantity(BigDecimal excessQuantity) {
		this.excessQuantity = excessQuantity;
	}

	@Column(name = "TAX_AMOUNT", nullable = true)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

	@Column(name = "CATEGORY_ID", nullable = false)
	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	@Column(name = "SUB_CATEGORY_ID", nullable = false)
	public Integer getSubCategoryId() {
		return subCategoryId;
	}

	public void setSubCategoryId(Integer subCategoryId) {
		this.subCategoryId = subCategoryId;
	}
}
