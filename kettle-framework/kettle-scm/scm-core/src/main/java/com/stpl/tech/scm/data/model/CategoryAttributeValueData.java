/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * CategoryAttributeValueData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CATEGORY_ATTRIBUTE_VALUE", uniqueConstraints = @UniqueConstraint(columnNames = {
    "CATEGORY_ATTRIBUTE_MAPPING_ID", "ATTRIBUTE_VALUE_ID"}))
public class CategoryAttributeValueData implements java.io.Serializable {

    private Integer categoryAttributeValueId;
    private CategoryAttributeMappingData categoryAttributeMapping;
    private AttributeValueData attributeValue;
    private String mappingStatus;

    public CategoryAttributeValueData() {
    }

    public CategoryAttributeValueData(CategoryAttributeMappingData categoryAttributeMapping,
                                      AttributeValueData attributeValue, String mappingStatus) {
        this.categoryAttributeMapping = categoryAttributeMapping;
        this.attributeValue = attributeValue;
        this.mappingStatus = mappingStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "CATEGORY_ATTRIBUTE_VALUE_ID", unique = true, nullable = false)
    public Integer getCategoryAttributeValueId() {
        return this.categoryAttributeValueId;
    }

    public void setCategoryAttributeValueId(Integer categoryAttributeValueId) {
        this.categoryAttributeValueId = categoryAttributeValueId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CATEGORY_ATTRIBUTE_MAPPING_ID", nullable = false)
    public CategoryAttributeMappingData getCategoryAttributeMapping() {
        return this.categoryAttributeMapping;
    }

    public void setCategoryAttributeMapping(CategoryAttributeMappingData categoryAttributeMapping) {
        this.categoryAttributeMapping = categoryAttributeMapping;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ATTRIBUTE_VALUE_ID", nullable = false)
    public AttributeValueData getAttributeValue() {
        return this.attributeValue;
    }

    public void setAttributeValue(AttributeValueData attributeValue) {
        this.attributeValue = attributeValue;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 15)
    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }

}
