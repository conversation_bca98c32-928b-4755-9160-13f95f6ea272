package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.FilterDefs;

@Entity
@Table(name = "PRICE_UPDATE_ERROR")
@FilterDefs({@FilterDef(name="entry", defaultCondition="entryType = 'ENTRY'"), @FilterDef(name="drillDown", defaultCondition="entryType = 'DRILLDOWN'")})
public class PriceUpdateEntryError {

	private Integer id;
	private String entryType;
	private int entryId;
	private int eventId;
	private String errorMessage;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ERROR_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}


	@Column(name = "ENTRY_TYPE", nullable = false)
	public String getEntryType() {
		return entryType;
	}

	public void setEntryType(String keyType) {
		this.entryType = keyType;
	}

	@Column(name = "ENTRY_ID", nullable = false)
	public int getEntryId() {
		return entryId;
	}

	public void setEntryId(int keyId) {
		this.entryId = keyId;
	}

	@Column(name = "ERROR_MESSAGE", nullable = false)
	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String keyName) {
		this.errorMessage = keyName;
	}

	/**
	 * @return the eventId
	 */
	@Column(name = "EVENT_ID", nullable = false)
	public int getEventId() {
		return eventId;
	}

	/**
	 * @param eventId the eventId to set
	 */
	public void setEventId(int eventId) {
		this.eventId = eventId;
	}


	
}
