/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@SuppressWarnings("serial")
@Entity
@Table(name = "ASSET_DEFINITION_LOG")
public class AssetDefinitionDataLog implements java.io.Serializable {


    private Integer assetDefinitionLogId;

    private Integer assetId;


    /*
        UnitId signifies Location of Asset
     */
    private Integer unitId;


    private String unitType;


    private String assetStatus;

    private String assetName;

    private String ownerType;

    private Integer ownerId;

    private String tagType;

    private String tagValue;
    private int tagPrintCount;

    private Date lastTagPrintDate;

    private Integer lastTagPrintedBy;


    private String depreciationStrategy;
    private BigDecimal depreciationRatePa;
    private BigDecimal dailyDepreciationRate;
    private BigDecimal depreciationResidue;
    private BigDecimal realizedDepreciation;


    private Date realizedDepreciationDate;

    private String lastTransferType;
    private Integer lastTransferId;

    private Date lastTransferDate;

    private Integer lastTransferedBy;


    private Date updationDate;

    private Integer updatedBy;


    public AssetDefinitionDataLog() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ASSET_DEFINITION_LOG_ID", unique = true, nullable = false)
    public Integer getAssetDefinitionLogId() {
        return assetDefinitionLogId;
    }

    public void setAssetDefinitionLogId(Integer assetDefinitionLogId) {
        this.assetDefinitionLogId = assetDefinitionLogId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }


    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }


    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_TYPE", nullable = false, length = 50)
    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    @Column(name = "ASSET_STATUS", nullable = false, length = 50)
    public String getAssetStatus() {
        return assetStatus;
    }

    public void setAssetStatus(String assetStatus) {
        this.assetStatus = assetStatus;
    }


    @Column(name = "OWNER_TYPE", nullable = false, length = 50)
    public String getOwnerType() {
        return ownerType;
    }

    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }

    @Column(name = "OWNER_ID", nullable = false)
    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }


    @Column(name = "TAG_TYPE", nullable = true, length = 50)
    public String getTagType() {
        return tagType;
    }

    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    @Column(name = "TAG_VALUE", nullable = true, length = 50)
    public String getTagValue() {
        return tagValue;
    }

    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    @Column(name = "TAG_PRINT_COUNT", nullable = true)
    public int getTagPrintCount() {
        return tagPrintCount;
    }

    public void setTagPrintCount(int tagPrintCount) {
        this.tagPrintCount = tagPrintCount;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_TAG_PRINT_DATE", length = 19)
    public Date getLastTagPrintDate() {
        return lastTagPrintDate;
    }

    public void setLastTagPrintDate(Date lastTagPrintDate) {
        this.lastTagPrintDate = lastTagPrintDate;
    }

    @Column(name = "LAST_TAG_PRINTED_BY", nullable = true)
    public Integer getLastTagPrintedBy() {
        return lastTagPrintedBy;
    }

    public void setLastTagPrintedBy(Integer lastTagPrintedBy) {
        this.lastTagPrintedBy = lastTagPrintedBy;
    }


    @Column(name = "DEPRECIATION_STRATEGY", nullable = false, length = 50)
    public String getDepreciationStrategy() {
        return depreciationStrategy;
    }

    public void setDepreciationStrategy(String depreciationStrategy) {
        this.depreciationStrategy = depreciationStrategy;
    }

    @Column(name = "DEPRECIATION_RATE_PA", nullable = true)
    public BigDecimal getDepreciationRatePa() {
        return depreciationRatePa;
    }

    public void setDepreciationRatePa(BigDecimal depreciationRatePa) {
        this.depreciationRatePa = depreciationRatePa;
    }

    @Column(name = "DAILY_DEPRECIATION_RATE", nullable = true)
    public BigDecimal getDailyDepreciationRate() {
        return dailyDepreciationRate;
    }

    public void setDailyDepreciationRate(BigDecimal dailyDepreciationRate) {
        this.dailyDepreciationRate = dailyDepreciationRate;
    }

    @Column(name = "DEPRECIATION_RESIDUE", nullable = true)
    public BigDecimal getDepreciationResidue() {
        return depreciationResidue;
    }

    public void setDepreciationResidue(BigDecimal depreciationResidue) {
        this.depreciationResidue = depreciationResidue;
    }

    @Column(name = "REALIZED_DEPRECIATION", nullable = true)
    public BigDecimal getRealizedDepreciation() {
        return realizedDepreciation;
    }

    public void setRealizedDepreciation(BigDecimal realizedDepreciation) {
        this.realizedDepreciation = realizedDepreciation;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REALIZED_DEPRECIATION_DATE", length = 19)
    public Date getRealizedDepreciationDate() {
        return realizedDepreciationDate;
    }

    public void setRealizedDepreciationDate(Date realizedDepreciationDate) {
        this.realizedDepreciationDate = realizedDepreciationDate;
    }

    @Column(name = "LAST_TRANSFER_TYPE", nullable = true, length = 50)
    public String getLastTransferType() {
        return lastTransferType;
    }

    public void setLastTransferType(String lastTransferType) {
        this.lastTransferType = lastTransferType;
    }

    @Column(name = "LAST_TRANSFER_ID", nullable = true)
    public Integer getLastTransferId() {
        return lastTransferId;
    }

    public void setLastTransferId(Integer lastTransferId) {
        this.lastTransferId = lastTransferId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_TRANSFER_DATE", length = 19)
    public Date getLastTransferDate() {
        return lastTransferDate;
    }


    public void setLastTransferDate(Date lastTransferDate) {
        this.lastTransferDate = lastTransferDate;
    }

    @Column(name = "LAST_TRANSFER_BY", nullable = true)
    public Integer getLastTransferedBy() {
        return lastTransferedBy;
    }

    public void setLastTransferedBy(Integer lastTransferedBy) {
        this.lastTransferedBy = lastTransferedBy;
    }


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATION_DATE", length = 19)
    public Date getUpdationDate() {
        return updationDate;
    }

    public void setUpdationDate(Date updationDate) {
        this.updationDate = updationDate;
    }

    @Column(name = "UPDATION_BY", nullable = false)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }


    @Column(name = "ASSET_NAME", nullable = false)
    public String getAssetName() {
        return assetName;
    }

    public void setAssetName(String assetName) {
        this.assetName = assetName;
    }

}