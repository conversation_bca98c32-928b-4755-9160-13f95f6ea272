package com.stpl.tech.scm.data.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "CAPEX_AUDIT_DETAIL")
public class CapexAuditDetailData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2565093697355543375L;

	private Integer id;
	private Integer capexRequestId;
	private String downloadedBy;
	private String uploadedBy;
	private String downloadedPath;
	private String uploadedPath;
	private String accessKey;
	private String status;
	private String version;
	private List<CapexBudgetAuditDetail> capexBudgetAuditDetail;
	private Date approvedDate;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ID")
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "CAPEX_REQUEST_ID")
	public Integer getCapexRequestId() {
		return capexRequestId;
	}

	public void setCapexRequestId(Integer capexRequestId) {
		this.capexRequestId = capexRequestId;
	}

	@Column(name = "DOWNLOAD_BY")
	public String getDownloadedBy() {
		return downloadedBy;
	}

	public void setDownloadedBy(String downloadedBy) {
		this.downloadedBy = downloadedBy;
	}

	@Column(name = "UPLOADED_BY")
	public String getUploadedBy() {
		return uploadedBy;
	}

	public void setUploadedBy(String uploadedBy) {
		this.uploadedBy = uploadedBy;
	}

	@Column(name = "DOWNLOADED_PATH")
	public String getDownloadedPath() {
		return downloadedPath;
	}

	public void setDownloadedPath(String downloadedPath) {
		this.downloadedPath = downloadedPath;
	}

	@Column(name = "UPLOADED_PATH")
	public String getUploadedPath() {
		return uploadedPath;
	}

	public void setUploadedPath(String uploadedPath) {
		this.uploadedPath = uploadedPath;
	}

	@Column(name = "ACCESS_KEY")
	public String getAccessKey() {
		return accessKey;
	}

	public void setAccessKey(String accessKey) {
		this.accessKey = accessKey;
	}

	@Column(name = "STATUS")
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	
	@Column(name = "VERSION")
	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "capexAuditDetailId")
	public List<CapexBudgetAuditDetail> getCapexBudgetAuditDetail() {
		return capexBudgetAuditDetail;
	}

	public void setCapexBudgetAuditDetail(List<CapexBudgetAuditDetail> capexBudgetAuditDetail) {
		this.capexBudgetAuditDetail = capexBudgetAuditDetail;
	}

	@Column(name = "APPROVED_DATE")
	public Date getApprovedDate() {
		return approvedDate;
	}

	public void setApprovedDate(Date approvedDate) {
		this.approvedDate = approvedDate;
	}
}
