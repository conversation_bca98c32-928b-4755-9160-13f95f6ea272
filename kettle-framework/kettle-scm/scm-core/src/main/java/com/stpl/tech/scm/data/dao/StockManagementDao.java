/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.dao;

import com.stpl.tech.kettle.core.data.budget.vo.ConsumablesAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.InventoryAggregate;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.ThresholdType;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.model.BookingConsumptionDataVO;
import com.stpl.tech.scm.core.util.model.ProductionBookingDataVO;
import com.stpl.tech.scm.data.model.ApprovalDetailData;
import com.stpl.tech.scm.data.model.BookingConsumptionData;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.DayCloseEventLogData;
import com.stpl.tech.scm.data.model.DayCloseInventoryDrillDown;
import com.stpl.tech.scm.data.model.VarianceAcknowledgementData;
import com.stpl.tech.scm.data.model.DayCloseTxnEventDrillDownData;
import com.stpl.tech.scm.data.model.GatepassItemData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.GoodsReceivedItemData;
import com.stpl.tech.scm.data.model.ProductionBookingData;
import com.stpl.tech.scm.data.model.RegularOrderingEvent;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventRangeData;
import com.stpl.tech.scm.data.model.SCMProductConsumptionData;
import com.stpl.tech.scm.data.model.SCMProductInventoryData;
import com.stpl.tech.scm.data.model.SCMWastageData;
import com.stpl.tech.scm.data.model.SCMWastageEventData;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.StockEntryEventData;
import com.stpl.tech.scm.data.model.StockEventCalendarData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.TransferOrderItemData;
import com.stpl.tech.scm.data.model.UnitOrderScheduleData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.WastageLimitLookup;
import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.domain.model.VarianceAcknowledgementDetail;
import com.stpl.tech.scm.reports.modal.CafeUnsettledTOModal;
import com.stpl.tech.scm.reports.modal.CafeWastageModal;
import com.stpl.tech.scm.reports.modal.VarianceModal;
import com.stpl.tech.scm.reports.modal.VarianceSummaryModal;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface StockManagementDao extends SCMAbstractDao {
    public List<SCMWastageEventData> getAllWastageEventsForUnit(int unit, Date businessDate);

    public SCMWastageEventData addWastageEvent(WastageEvent wastageEvent);

    public SCMDayCloseEventData createConsumptionEvent(StockEventType eventType, int unitId, StockTakeType frequency, StockEventStatus status, Date businessDate, Integer dayClosureId);

    public SCMDayCloseEventRangeData createDayCloseEventRecord(SCMDayCloseEventData event, int startOrderId, int endOrderId, StockEventType consumption, StockTakeType frequency);

    public SCMDayCloseEventData getOpeningEventForUnit(int unitId);

    public SCMDayCloseEventData checkClosingInitiated(int unit, Date businessDate);

    public boolean cancelDayCloseForUnit(int unitId, int closureId);

    public List<TransferOrderItemData> getTransferList(int unit, Date businessDate, Date lastBusinessDate, List<String> statuslist, boolean excludeSpecilizedOrders);

    public List<GoodsReceivedItemData> getReceivingList(int unit, Date businessDate, Date lastBusinessDate);

    public List<SCMWastageData> getWastageEventList(int unit, Date businessDate, Date lastBusinessDate);

    public SCMDayCloseEventData getLastDayCloseEvent(int unitId, StockEventType eventType);//, StockTakeType frequency);

    public Boolean checkInventoryLoaded(Integer unit, Date businessDate, StockTakeType stockType);

    List<StockEventCalendarData> checkPendingCalendarEventByType(Integer unitId, StockTakeType type, Date businessDate);

    boolean closePendingCalendarEventsByType(Integer unitId, StockTakeType type, Date businessDate);

    List<StockEventCalendarData> getPendingCalendarEventForUnit(Integer unitId, Date businessDate);

    List<StockEventCalendarData> getPlannedCalendarEventByType(Integer unitId, StockTakeType type, Date businessDate);

    public SCMDayCloseEventData getInitiatedDayCloseEvents(Integer unit, Date businessDate);

    public SCMDayCloseEventData getLastDayCloseEventOfType(int unit, StockTakeType frequency, StockEventType eventType);

    public List<Pair<StockTakeType, Integer>> getLatestClosureEvents(Integer unit);

    public Map<Integer, BigDecimal> getCurrentStock(Integer unit);

    public SCMDayCloseEventData createStockTakeEvent(StockEventType eventType, int unitId, Date currentBusinessDate, StockTakeType stockType);

    public Map<Integer,BigDecimal> getStockForUnit(int unitId,int eventId);

    public SCMDayCloseEventData getStockEventForUnit(int unitId, Integer dayClosureId, List<String> stockTakeTypes);

    public List<SCMProductConsumptionData> getConsumptionForBusinessDate(int unitId, Date businessDate, Date previousDate);

    public List<SCMProductConsumptionData> getConsumption(int event);


    /*****************************Warehouse Closing Methods after this*****************************************/

    public SCMDayCloseEventData getActiveDayCloseEvent(int unitId);

    public List<SCMWastageEventData> getWastagesForWHUnit(int unitId, List<Integer> skuList);

    public List<TransferOrderData> getTransferItemsForWHUnit(int unitId, List<Integer> skuList);

    public List<GatepassItemData> getGatepassItemsForWHUnit(int unitId, List<Integer> skuList, String transType);

    public List<GoodsReceivedData> getGRsForWHUnit(int unitId, List<Integer> skuList);

    public List<VendorGoodsReceivedData> getVendorGRItemsForWHUnit(int unitId, List<Integer> skuList);

    public List<ProductionBookingData> getProductionBookingsForWHUnit(int unitId, List<Integer> skuList);

    Collection<ProductionBookingDataVO> getReserveProductionBookingsAggregateForWHUnit(int unitId, List<Integer> bookingIds,
                                                                                       boolean loadDrilldowns);

    public List<StockEntryEventData> getActiveOpeningEventForUnit(int unitId, List<Integer> skuList);

    public List<Integer> getSkuListForUnit(int unitId);

    public List<DayCloseTxnEventDrillDownData> getDrillDownData(SCMDayCloseEventData eventData);

    public List<DayCloseInventoryDrillDown> getInventoryList(SCMDayCloseEventData eventData);

    public List<Integer> getMonthlySkuList(int unitId);

	public List<VarianceModal> getCafeVarianceStock(int unitId, Date businessDate, StockTakeType varianceType);

	public List<CafeWastageModal> getCafeWastage(int unitId, StockTakeType varianceType, Date businessDate);

	public List<CafeUnsettledTOModal> getCafeUnsettledTO(int unitId, StockTakeType varianceType, Date businessDate);

	public List<VarianceModal> getWareHouseVarianceStock(SCMDayCloseEventData eventData);

	/**
	 * @param eventId
	 */
	public void generateDailySummary(Integer eventId);

	/**
	 * @param eventId
	 * @return
	 */
	public List<VarianceSummaryModal> getDailySummary(Integer eventId);

    public List<SkuDefinition> getTaggedInventoryListForWarehouse(int unitId);
    public Map<Integer, Integer> getMappedSkuWithInventoryList(int unitId);
    public Map<Integer, Integer> getMappedSkuWithInventoryListCafe();
    public List<SkuDefinition> getTaggedInventoryListForWarehouse(int unitId, Map<Integer, Integer> mappedSkuwithInventoryList , Boolean isAllListType);

    public List<ApprovalDetailData> getAssetApprovals(Integer approvalRequestId, Integer unitId, Integer eventId, Integer assetId, String status) throws SumoException;

    List<BookingConsumptionDataVO> getReverseBookingConsumptionsAggregate(int unitId,
                                                                          List<Integer> bookingConsumptionIds);

    public List<BookingConsumptionData> getBookingConsumptions(int unitId, List<Integer> skuList);

    List<Integer> getReverseBookingConsumptionsIds(int unitId, List<Integer> skuList);

    public Map<Integer, String> getSkuClosingDates(Integer unitId, List<Integer> skus);

	public List<Integer> getDayCloseEventForBusinessDate(int unitId, Date businessDate, StockTakeType type);

	public List<Pair<String, Pair<BigDecimal, BigDecimal>>> getCOGSAggregate(List<Integer> events);

	public InventoryAggregate getStockVarianceAmount(Integer eventId, StockTakeType type);

	public InventoryAggregate getStockVarianceAmountMTD(int unitId, Date businessDate, StockTakeType type);

	public Integer getSCMDayCloseEventForBusinessDate(int unitId, Date businessDate);

	public Map<Integer, Pair<BigDecimal, BigDecimal>> getConsumableAggregate(int unitId, Date businessDate, StockTakeType type);

    public boolean cancelAllPreviousEvents(int unitId, Date businessDate);

	public ConsumablesAggregate getFixedAssetsAggregate(int unitId, Date businessDate, StockTakeType type, Date handoverDate);

	public List<Pair<Integer, BigDecimal>> getConsumableAggregateForMonth(int unitId, Date monthStart, Date businessDate);

	public List<Pair<Integer, BigDecimal>> getRequestedConsumableAggregate(int unitId, Date monthStartDate, Date businessDate);

	public List<Pair<Integer, BigDecimal>> getTransferredConsumableAggregateForMonth(int unitId, Date monthStartDate, Date businessDate);

	public BigDecimal getRequestedFixedAssetsAggregate(int unitId, Date monthStartDate, Date businessDate);

	public BigDecimal getFixedAssetsAggregateForMonth(int unitId, Date monthStartDate, Date businessDate);

	public BigDecimal getTransferredFixedAssetsForMonth(int unitId, Date monthStartDate, Date businessDate);

    Map<Integer, BigDecimal> getFATaxAggregate(int unitId, Date businessDate, ThresholdType type, BigDecimal threshold,
                                 StockTakeType stockTakeType, Date handoverDate, boolean beforehandOver);

    public BigDecimal getTaxAggregate(int unitId, Date businessDate, List<Integer> categories, StockTakeType stockTakeType);

	public BigDecimal getFATaxAggregate(int unitId, Date businessDate, ThresholdType type,  BigDecimal threshold, StockTakeType stockTakeType);

	public List<SalesPerformaDetailData> getInvoicesForWhUnit(int unitId, List<Integer> skuList);

    public DayCloseEventLogData findByClosureEvent(int eventData, DayCloseEventLogType type);

    public Integer checkSpecialOrders(int unitId, Date fulfillmentDate);

	public SCMDayCloseEventData fetchUnitLastDayClose(int unitId);

	public List<SCMWastageData> getWastageEventListByDayCloseEventRangeData(int unitId, Date businessDate, StockTakeType type);

	public Integer getDayCloseEventIdOfType(int unitId, StockTakeType monthly, StockEventType stockTake,
			Date monthlyCheckDate, Date monthlyCheckDateEnd);

	public List<Integer> getUnitsWithMonthlyDone(Date date);

	public Integer getDayCloseEvent(int unitId, Date businessDate, StockTakeType type);

    Integer getSCMDayCloseEventForBusinessDateForClosingType(int unitId, Date businessDate);

	public SCMDayCloseEventData getLastDayCloseEventOfTypeOnBusinessDate(int unit, StockTakeType stockTakeType,
			StockEventType eventType, Date businessDate);

    public List<SCMDayCloseEventData> getInitiatedStockEvents(Integer unitId);

    public SCMDayCloseEventRangeData getTransfersRangeData(SCMDayCloseEventData event,StockTakeType stockTakeType);

    public void updateTransfers(SCMDayCloseEventData stockEvent, int startId, int endId, StockTakeType stockTakeType);
    @Deprecated
	public int removeDuplicateKey();

	StockEventCalendarData addStockCalendarEvent(Integer unitId, StockTakeType stockType, Date nextDate)
			throws SumoException;

	public Map<Integer, Pair<BigDecimal, BigDecimal>> getConsumableTaxAggregate(int unitId, Date businessDate,
			StockTakeType stockTakeType);

	List<Integer> getProductionBookingsIdsForWHUnit(int unitId, List<Integer> skuList);

    List<Integer> getReverseProductionBookingsIdsForWHUnit(int unitId, List<Integer> skuList);

    Collection<ProductionBookingDataVO> getProductionBookingsAggregateForWHUnit(int unitId, List<Integer> bookingIds, boolean loadDrilldowns);

	public List<Integer> getBookingConsumptionsIds(int unitId, List<Integer> skuList);

	public List<BookingConsumptionDataVO> getBookingConsumptionsAggregate(int unitId,
			List<Integer> bookingConsumptionIds);

	List<ProductionBookingData> getProductionBookingsDetailsForWHUnit(int unitId, List<Integer> bookingIds);

    List<ReverseProductionBookingData> getReverseProductionBookingsDetailsForWHUnit(int unitId, List<Integer> bookingIds);

	List<Integer> fetchDuplicateKeyWh();

    public List<Integer> fetchDuplicateKeyCafe();

	int updateDuplicateKeyLatestFlag(List<Integer> ids , Boolean isCafe);

	public void updatePriceAndVarianceCost(Integer eventId);

	public void deleteAllProductStockDrillDownUpdateEvent(Integer eventId);

    public WastageLimitLookup findWastageLimitById(int id);

    public List<SCMWastageEventData> getAllWastageEventsForDates(String startDate, String endDate, Integer unitId);

    public List<RegularOrderingEvent> checkOrderingEvents(SCMDayCloseEventData dayClose, String brand, UnitOrderScheduleData unitOrderScheduleData, Date fulfilmentDate);

    public List<RequestOrderData> getAcknowledgedROProducts(Integer unitId, Date firstOrderingDate);

    public List<RequestOrderData> getRosFromRoId(Integer id);

    public void insertCostDetailDumpData();

    public void insertToItemInTransitDumpData();

    SCMDayCloseEventData getSumoDayCloseStatus(Integer unitId, Date previousDate);

    List<SCMDayCloseEventData> verifyDayCloses(List<Integer> unitIds);

    public List<CostDetailData> fetchUnitProductInventory(Integer unitId, String keyType);

    List<CostDetailData> fetchProductInventory(String keyType, List<Integer> keyIds);

    public SCMDayCloseEventData checkLastClosingInitiated(int unit , Date lastOperationDate);

    public Boolean checkStockUpdated(Integer unit, Date businessDate, StockTakeType stockType);

    SCMDayCloseEventData getLastSuccessfulDayCloseEventData(Integer unitId);

    List<SCMProductInventoryData> getScmProductInventoryDataList(SCMDayCloseEventData dayCloseEventData, boolean getByCurrentStockType);

    List<SCMProductInventoryData> getScmProductInventoryDataListWithIds(List<Integer> stockingIds);

    List<StockEntryEventData> getStockEntryEventDataForDayCloseId(Integer eventId);

    public Date getLastDayCloseDone(Integer unitId, String subType);

    public List<VarianceAcknowledgementData> getVarianceAcknowledgementData(Date startDate ,Date endDate, List<Integer> unitId, String isAcknowledged,String acknowledgementType);

    public VarianceAcknowledgementData getVarianceAcknowledgementData(Integer unitId, Date businessDate);

    public VarianceAcknowledgementData getLatestNonAcknowledgedData(Integer unitId, String isAcknowledged, String acknowledgementType);

    List<SCMProductInventoryData> getScmProductInventoryDataListWithEventIdAndFrequencyType(Integer eventId,String frequencyType);

    public List<VarianceAcknowledgementData> getVarianceAcknowledgementData(String acknowledgementType,Integer unitId,Date date);

    public List<Integer> getClassListFromUnitType(String unitCategory, String type, String frequency);

    public Integer getInventoryDayForUnit(String unitCategory, String type, String frequency);

}
