/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Where;

/**
 * SkuPriceKey generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SKU_PRICE_DATA")
public class SkuPriceData implements java.io.Serializable {

	private Integer skuPriceKeyId;
	private int packagingId;
	private int skuId;
	private int vendorId;
	private String dispatchLocation;
	private String deliveryLocation;
	private BigDecimal price;
	private Date startDate;
	private Date endDate;
	private String status;
	private Integer leadTime;
	private List<SkuPriceHistory> updateList;
	private String isPriceChangeRequested;

	public SkuPriceData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SKU_PRICE_DATA_ID", unique = true, nullable = false)
	public Integer getSkuPriceKeyId() {
		return this.skuPriceKeyId;
	}

	public void setSkuPriceKeyId(Integer unitProductMappingId) {
		this.skuPriceKeyId = unitProductMappingId;
	}

	@Column(name = "SKU_PACKAGING_ID", nullable = false)
	public int getPackagingId() {
		return this.packagingId;
	}

	public void setPackagingId(int unitId) {
		this.packagingId = unitId;
	}

	@Column(name = "SKU_ID", nullable = false)
	public int getSkuId() {
		return this.skuId;
	}

	public void setSkuId(int productId) {
		this.skuId = productId;
	}

	@Column(name = "VENDOR_ID", nullable = false)
	public int getVendorId() {
		return this.vendorId;
	}

	public void setVendorId(int vendorId) {
		this.vendorId = vendorId;
	}

	@Column(name = "DISPATCH_LOCATION", nullable = false, length = 50)
	public String getDispatchLocation() {
		return this.dispatchLocation;
	}

	public void setDispatchLocation(String dispatchLocation) {
		this.dispatchLocation = dispatchLocation;
	}

	@Column(name = "DELIVERY_LOCATION", nullable = false, length = 50)
	public String getDeliveryLocation() {
		return deliveryLocation;
	}

	public void setDeliveryLocation(String deliveryLocation) {
		this.deliveryLocation = deliveryLocation;
	}

	@Column(name = "SKU_PRICE", precision = 10)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", nullable = false, length = 10)
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@Column(name = "STATUS", nullable = false, length = 15)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@OneToMany(fetch = FetchType.EAGER, mappedBy = "skuPriceDataId")
	@Where(clause = "RECORD_STATUS = 'CREATED' and CHANGE_TYPE = 'PRICE_UPDATE'")
	public List<SkuPriceHistory> getUpdateList() {
		return updateList;
	}

	public void setUpdateList(List<SkuPriceHistory> updateList) {
		this.updateList = updateList;
	}

	@Column(name = "LEAD_TIME", nullable = false)
	public Integer getLeadTime() {
		return leadTime;
	}

	public void setLeadTime(Integer leadTime) {
		this.leadTime = leadTime;
	}

	@Column(name = "IS_PRICE_CHANGE_REQUESTED")
	public String getIsPriceChangeRequested() {
		return isPriceChangeRequested;
	}

	public void setIsPriceChangeRequested(String isPriceChangeRequested) {
		this.isPriceChangeRequested = isPriceChangeRequested;
	}
}
