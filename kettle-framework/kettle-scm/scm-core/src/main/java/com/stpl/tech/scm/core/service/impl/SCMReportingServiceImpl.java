/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.service.impl;

import com.itextpdf.tool.xml.exceptions.NotImplementedException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.StockTakeException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMReportingService;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.reports.*;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SCMReportingServiceImpl implements SCMReportingService {
	private static final Logger LOG = LoggerFactory.getLogger(SCMReportingServiceImpl.class);
	private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();

	@Autowired
	SCMCache scmCache;

	@Autowired
	MasterDataCache masterDataCache;

	@Autowired
	EnvProperties props;

	@Autowired
	StockManagementDao stockManagementDao;

	@Autowired
	SCMAssetManagementDao scmAssetManagementDao;

	@Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean sendVarianceReport(Date businessDate, Integer unitId, boolean isFixedAssets) {
        UnitDetail u = scmCache.getUnitDetail(unitId);
		List<StockTakeType> stockTakeTypes = !isFixedAssets
				? Arrays.asList(StockTakeType.DAILY, StockTakeType.WEEKLY, StockTakeType.MONTHLY)
				: Arrays.asList(StockTakeType.FIXED_ASSETS);
		for (StockTakeType stockType : stockTakeTypes) {
			try {
				VarianceReport view = new CafeVarianceReport(ctxFactory, stockManagementDao,
						props.getBasePath(), props.getEnvType(), stockType, unitId, u.getUnitName(), u.getUnitEmail(),
						businessDate, props);

				if (!isFixedAssets) {
					LOG.info("Varaince Report Email Generation for unit {}, business Date {} , stockType {}", unitId,
							businessDate, stockType);
					// renders all views here
					if (view.renderSummaryView()) {
						LOG.info("render variance for unit {}, business Date {} , stockType {} ", unitId, businessDate,
								stockType);
						view.renderVariance();
						LOG.info("render Wastage for unit {}, business Date {} , stockType {} ", unitId, businessDate,
								stockType);
						view.renderWastage();
						LOG.info("render Unsettled TO for unit {}, business Date {} , stockType {} ", unitId,
								businessDate, stockType);
						view.renderUnsettledTO();
					} else {
						StringBuffer message = new StringBuffer(
								"Stock variance summary failed to render for Stock Type ");
						message.append(stockType.name()).append(" for Unit ").append(unitId);
						LOG.info(message.toString());
						throw new StockTakeException(message.toString());
					}
				}else{
					LOG.info("FIXED_ASSETS Varaince Report Email Generation for unit {}, business Date {} , stockType {}", unitId,
							businessDate, stockType);
					view.renderVariance();
				}

				List<String> toEmailIds = masterDataCache.getEmailHeirarchy(unitId, true);
				toEmailIds.add("<EMAIL>");
				sendVarianceReportNotification(view, toEmailIds.toArray(new String[toEmailIds.size()]));
			} catch (StockTakeException ste) {
				LOG.info(ste.getMessage());
			} catch (NotImplementedException nie) {
				LOG.info(nie.getMessage());
			} catch (Exception e) {
				LOG.error("Exception occurred while writing variance file :::: for unitId {}", unitId, e);
				StringBuilder message = new StringBuilder("Failed to send variance report mail \n");
				message.append("Unit Details :: " + u.getUnitName() + "\n");
				message.append("Encountered error ::::: " +e.getMessage());
				SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
						SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
			}
		}
		return false;
	}

	private void sendVarianceReportNotification(VarianceReport view,String[] toEmails) throws IOException, EmailGenerationException {
		WorkbookContext workbook = view.getWorkbook();
		if (workbook != null) {
			view.generateReport(view.getFilePath(), workbook.toNativeBytes());
			VarianceReportNotification reportNotification = new VarianceReportNotification(view,toEmails);
			List<AttachmentData> attachments = new ArrayList<>();
			if (view.isGenerated()) {
				File file = new File(view.getFilePath());
				if (file.exists()) {
					AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)),
							view.getFileName(), view.getMimeType());
					attachments.add(reports);
					reportNotification.sendRawMail(attachments);
				} else {
					StringBuilder message = new StringBuilder("Failed to send variance report mail \n");
					message.append("Unit Details :: " + view.getUnitName() + "\n");
					SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
							SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
				}
			} else {
				LOG.info("VarianceReportNotification view is not generated for {}, {}", view.getUnitName(),
						view.getBusinessDate());
			}
		} else {
			StringBuilder message = new StringBuilder("Empty Workbook for variance report mail \n");
			message.append("Unit Details :: " + view.getUnitName() + "\n");
			SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
					SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
		}
	}

	@Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean sendWhVarianceReport(SCMDayCloseEventData eventData) {
            UnitDetail u = scmCache.getUnitDetail(eventData.getUnitId());
			try {
				VarianceReport view = new WareHouseVarianceReport(ctxFactory, stockManagementDao,
						props.getBasePath(), props.getEnvType(), eventData,scmCache);

				// renders all views here
				view.renderVariance();
				view.renderNegativeWriteOff();
                String[] toEmails = new String[] { view.getEmailId(), "<EMAIL>" };
				sendVarianceReportNotification(view,toEmails);

			} catch (StockTakeException ste) {
				LOG.info(ste.getMessage());
			} catch (Exception e) {
				LOG.error("Exception occurred while writing variance file :::: for unitId {}", eventData.getUnitId(),
						e);
				StringBuilder message = new StringBuilder("Failed to send variance report mail \n");
				message.append("Unit Details :: " + u.getUnitName() + "\n");
				SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
						SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
			}
		return false;
	}

	private void sendStockTakeReportNotification(FixedAssetStockTakeReport view,String[] toEmails, Boolean submit) throws IOException, EmailGenerationException {
		WorkbookContext workbook = view.getWorkbook();
		if (workbook != null) {
			view.generateReport(view.getFilePath(), workbook.toNativeBytes());
			StockTakeNotification reportNotification = new StockTakeNotification(view,toEmails,submit);
			List<AttachmentData> attachments = new ArrayList<>();
			if (view.isGenerated()) {
				File file = new File(view.getFilePath());
				if (file.exists()) {
					AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)),
							view.getFileName(), view.getMimeType());
					attachments.add(reports);
					reportNotification.sendRawMail(attachments);
				} else {
					StringBuilder message = new StringBuilder("Failed to send stock take report mail \n");
					message.append("Unit Details :: " + view.getUnitName() + "\n");
					SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
							SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
				}
			} else {
				LOG.info("VarianceReportNotification view is not generated for {}, {}", view.getUnitName(),
						view.getBusinessDate());
			}
		} else {
			StringBuilder message = new StringBuilder("Empty Workbook for stock take report mail \n");
			message.append("Unit Details :: " + view.getUnitName() + "\n");
			SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
					SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean sendStockTakeReport(Date businessDate, Integer unitId, Integer eventId, String eventSubtype, String initiatorEmail, Boolean submit) {
		UnitDetail u = scmCache.getUnitDetail(unitId);

			try {
				FixedAssetStockTakeReport view = new FAStockTakeReport(ctxFactory, unitId, u.getUnitName(),
						eventId, eventSubtype, props.getBasePath(), businessDate,
						props.getEnvType(), u.getUnitEmail(), scmAssetManagementDao,props);


					LOG.info("Stock Take Report Email Generation for unit {}, business Date {}", unitId, businessDate);
					// renders all views here
						LOG.info("render summary for unit {}, business Date {}", unitId, businessDate);
						view.renderSummary();
						LOG.info("render scanned for unit {}, business Date {}", unitId, businessDate);
						view.renderScanned();
						LOG.info("render not found for unit {}, business Date {}", unitId, businessDate);
						view.renderNotFound();
						LOG.info("render extra found TO for unit {}, business Date {}", unitId, businessDate);
						view.renderFoundExtra();


				List<String> toEmailIds = masterDataCache.getEmailHeirarchy(unitId, true);
				toEmailIds.add("<EMAIL>");
				toEmailIds.add("<EMAIL>");
				if(initiatorEmail != null){
					toEmailIds.add(initiatorEmail);
				}
				toEmailIds = toEmailIds.stream().filter(email -> !Objects.equals(email, "")).collect(Collectors.toList());
				sendStockTakeReportNotification(view, toEmailIds.toArray(new String[toEmailIds.size()]), submit);
				return true;
			} catch (StockTakeException ste) {
				LOG.info(ste.getMessage());
			} catch (NotImplementedException nie) {
				LOG.info(nie.getMessage());
			} catch (Exception e) {
				LOG.error("Exception occurred while writing stock take file :::: for unitId {}", unitId, e);
				StringBuilder message = new StringBuilder("Failed to send stock take report mail \n");
				message.append("Unit Details :: " + u.getUnitName() + "\n");
				message.append("Encountered error ::::: " +e.getMessage());
				SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "Kettle",
						SlackNotification.VARIANCE_REPORT_FAILURES, message.toString());
			}

		return false;
	}
}
