package com.stpl.tech.scm.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 11-06-2016.
 */
@Entity
@Table(name = "EXTERNAL_TRANSFER_DETAILS")
public class ExternalTransferDetailData {

    private Integer id;
    private Date lastUpdateTime;
    private int vendorId;
    private int dispatchId;
    private Integer lastUpdatedBy;
    private String approvalStatus;
    private String locationName;
    private String vendorName;
    private TransferOrderData transferOrderData;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EXT_TR_DETAIL_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "UPDATED_AT", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TRANSFER_ORDER_ID", nullable = false)
    public TransferOrderData getTransferOrderData() {
        return this.transferOrderData;
    }

    public void setTransferOrderData(TransferOrderData transferOrderData) {
        this.transferOrderData = transferOrderData;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public int getVendorId() {
        return vendorId;
    }

    public void setVendorId(int vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "DISPATCH_ID", nullable = false)
    public int getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(int dispatchId) {
        this.dispatchId = dispatchId;
    }

    @Column(name = "UPDATED_BY", nullable = false)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "APPROVAL_STATUS", nullable = false)
    public String getApprovalStatus() {
        return approvalStatus;
    }

    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    @Column(name = "LOCATION_NAME", nullable = false)
    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    @Column(name = "VENDOR_NAME", nullable = false)
    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }
}
