package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "AGREEMENT_SIGN_RECORD")
public class AgreementSignRecord {

    private Integer recordId;
    private Integer vendorId;
    private Integer agreementId;
    private Date agreementSignDate;
    private Integer requesterUserId;
    private String recordReason;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "RECORD_ID", nullable = false)
    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "AGREEMENT_ID", nullable = false)
    public Integer getAgreementId() {
        return agreementId;
    }

    public void setAgreementId(Integer agreementId) {
        this.agreementId = agreementId;
    }

    @Column(name = "AGREEMENT_SIGN_DATE", nullable = false)
    public Date getAgreementSignDate() {
        return agreementSignDate;
    }

    public void setAgreementSignDate(Date agreementSignDate) {
        this.agreementSignDate = agreementSignDate;
    }

    @Column(name = "REQUESTER_USER_ID", nullable = false)
    public Integer getRequesterUserId() {
        return requesterUserId;
    }

    public void setRequesterUserId(Integer requesterUserId) {
        this.requesterUserId = requesterUserId;
    }

    @Column(name = "RECORD_REASON", nullable = false)
    public String getRecordReason() {
        return recordReason;
    }

    public void setRecordReason(String recordReason) {
        this.recordReason = recordReason;
    }
}
