package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "REGULAR_ORDERING_FORECAST_DATA")
public class ForecastReportResponse {
    private Integer id;
    private Date date;
    private String brand;
    private String cafeId;
    private String city;
    private String l1Category;
    private String l2Category;
    private String l3Category;
    private String l4Category;
    private String motherWarehouse;
    private String orderSource;
    private String skuId;
    private String skuName;
    private BigDecimal actual;
    private BigDecimal predictedLpi;
    private BigDecimal predictedBaseline;
    private BigDecimal predictedUpi;
    private BigDecimal predictedFinal;
    private BigDecimal price;
    private BigDecimal actualValue;
    private BigDecimal predictedBaselineValue;
    private BigDecimal value;
    private Date refreshDate;
    private BigDecimal safetyStock;
    private ReferenceOrderData referenceOrderData;
    private String nonFunctional;

    public ForecastReportResponse() {
    }

    public ForecastReportResponse(Date date, String brand, String cafeId, String city, String l1Category, String l2Category, String l3Category, String l4Category, String motherWarehouse, String orderSource, String skuId, String skuName, BigDecimal actual, BigDecimal predictedLpi, BigDecimal predictedBaseline, BigDecimal predictedUpi, BigDecimal predictedFinal, BigDecimal price, BigDecimal actualValue, BigDecimal predictedBaselineValue, BigDecimal value, Date refreshDate , BigDecimal safetyStock) {
        this.date = date;
        this.brand = brand;
        this.cafeId = cafeId;
        this.city = city;
        this.l1Category = l1Category;
        this.l2Category = l2Category;
        this.l3Category = l3Category;
        this.l4Category = l4Category;
        this.motherWarehouse = motherWarehouse;
        this.orderSource = orderSource;
        this.skuId = skuId;
        this.skuName = skuName;
        this.actual = actual;
        this.predictedLpi = predictedLpi;
        this.predictedBaseline = predictedBaseline;
        this.predictedUpi = predictedUpi;
        this.predictedFinal = predictedFinal;
        this.price = price;
        this.actualValue = actualValue;
        this.predictedBaselineValue = predictedBaselineValue;
        this.value = value;
        this.refreshDate = refreshDate;
        this.safetyStock = safetyStock;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "FORECAST_ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DATE")
    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    @Column(name = "BRAND")
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    @Column(name = "CAFE_ID")
    public String getCafeId() {
        return cafeId;
    }

    public void setCafeId(String cafeId) {
        this.cafeId = cafeId;
    }

    @Column(name = "CITY")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name = "L1_CATEGORY")
    public String getL1Category() {
        return l1Category;
    }

    public void setL1Category(String l1Category) {
        this.l1Category = l1Category;
    }

    @Column(name = "L2_CATEGORY")
    public String getL2Category() {
        return l2Category;
    }

    public void setL2Category(String l2Category) {
        this.l2Category = l2Category;
    }

    @Column(name = "L3_CATEGORY")
    public String getL3Category() {
        return l3Category;
    }

    public void setL3Category(String l3Category) {
        this.l3Category = l3Category;
    }

    @Column(name = "L4_CATEGORY")
    public String getL4Category() {
        return l4Category;
    }

    public void setL4Category(String l4Category) {
        this.l4Category = l4Category;
    }

    @Column(name = "MOTHER_WAREHOUSE")
    public String getMotherWarehouse() {
        return motherWarehouse;
    }

    public void setMotherWarehouse(String motherWarehouse) {
        this.motherWarehouse = motherWarehouse;
    }

    @Column(name = "ORDER_SOURCE")
    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    @Column(name = "SKU_ID")
    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    @Column(name = "SKU_NAME")
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "ACTUAL")
    public BigDecimal getActual() {
        return actual;
    }

    public void setActual(BigDecimal actual) {
        this.actual = actual;
    }

    @Column(name = "PREDICTED_LPI")
    public BigDecimal getPredictedLpi() {
        return predictedLpi;
    }

    public void setPredictedLpi(BigDecimal predictedLpi) {
        this.predictedLpi = predictedLpi;
    }

    @Column(name = "PREDICTED_BASELINE")
    public BigDecimal getPredictedBaseline() {
        return predictedBaseline;
    }

    public void setPredictedBaseline(BigDecimal predictedBaseline) {
        this.predictedBaseline = predictedBaseline;
    }

    @Column(name = "PREDICTED_UPI")
    public BigDecimal getPredictedUpi() {
        return predictedUpi;
    }

    public void setPredictedUpi(BigDecimal predictedUpi) {
        this.predictedUpi = predictedUpi;
    }

    @Column(name = "PREDICTED_FINAL")
    public BigDecimal getPredictedFinal() {
        return predictedFinal;
    }

    public void setPredictedFinal(BigDecimal predictedFinal) {
        this.predictedFinal = predictedFinal;
    }

    @Column(name = "PRICE")
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Column(name = "ACTUAL_VALUE")
    public BigDecimal getActualValue() {
        return actualValue;
    }

    public void setActualValue(BigDecimal actualValue) {
        this.actualValue = actualValue;
    }

    @Column(name = "PREDICTED_BASELINE_VALUE")
    public BigDecimal getPredictedBaselineValue() {
        return predictedBaselineValue;
    }

    public void setPredictedBaselineValue(BigDecimal predictedBaselineValue) {
        this.predictedBaselineValue = predictedBaselineValue;
    }

    @Column(name = "VALUE")
    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REFRESH_DATE")
    public Date getRefreshDate() {
        return refreshDate;
    }

    public void setRefreshDate(Date refreshDate) {
        this.refreshDate = refreshDate;
    }

    @Column(name = "SAFETY_STOCK")
    public BigDecimal getSafetyStock() {
        return safetyStock;
    }

    public void setSafetyStock(BigDecimal safetyStock) {
        this.safetyStock = safetyStock;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REFERENCE_ORDER_ID")
    public ReferenceOrderData getReferenceOrderData() {
        return referenceOrderData;
    }

    public void setReferenceOrderData(ReferenceOrderData referenceOrderData) {
        this.referenceOrderData = referenceOrderData;
    }

    @Column(name = "NON_FUNCTIONAL")
    public String getNonFunctional() {
        return nonFunctional;
    }

    public void setNonFunctional(String nonFunctional) {
        this.nonFunctional = nonFunctional;
    }
}
