package com.stpl.tech.scm.data.transport.model;

import java.math.BigDecimal;

public class EwayBillDetails {

    private String TransId;
    private String TransName;
    private String TransMode;
    private Integer Distance;
    private String TransDocNo;
    private String TransDocDt;
    private String VehNo;
    private String VehType;

    public String getTransId() {
        return TransId;
    }

    public void setTransId(String transId) {
        TransId = transId;
    }

    public String getTransName() {
        return TransName;
    }

    public void setTransName(String transName) {
        TransName = transName;
    }

    public String getTransMode() {
        return TransMode;
    }

    public void setTransMode(String transMode) {
        TransMode = transMode;
    }

    public Integer getDistance() {
        return Distance;
    }

    public void setDistance(Integer distance) {
        Distance = distance;
    }

    public String getTransDocNo() {
        return TransDocNo;
    }

    public void setTransDocNo(String transDocNo) {
        TransDocNo = transDocNo;
    }

    public String getTransDocDt() {
        return TransDocDt;
    }

    public void setTransDocDt(String transDocDt) {
        TransDocDt = transDocDt;
    }

    public String getVehNo() {
        return VehNo;
    }

    public void setVehNo(String vehNo) {
        VehNo = vehNo;
    }

    public String getVehType() {
        return VehType;
    }

    public void setVehType(String vehType) {
        VehType = vehType;
    }
}
