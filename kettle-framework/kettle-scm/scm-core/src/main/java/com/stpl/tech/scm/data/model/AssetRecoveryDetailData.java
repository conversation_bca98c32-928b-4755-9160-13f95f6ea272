package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "ASSET_RECOVERY_DETAIL")
public class AssetRecoveryDetailData {

    private Integer recoveryDetailId;
    private Integer recoveryId;
    private String recoveryType;
    private String recoveryStatus;
    private BigDecimal amountRecovered;
    private String recoveredFrom;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "RECOVERY_DETAIL_ID", unique = true, nullable = false)
    public Integer getRecoveryDetailId() {
        return recoveryDetailId;
    }

    public void setRecoveryDetailId(Integer recoveryDetailId) {
        this.recoveryDetailId = recoveryDetailId;
    }

    @Column(name = "RECOVERY_ID", nullable = false)
    public Integer getRecoveryId() {
        return recoveryId;
    }

    public void setRecoveryId(Integer recoveryId) {
        this.recoveryId = recoveryId;
    }

    @Column(name = "RECOVERY_TYPE", nullable = false)
    public String getRecoveryType() {
        return recoveryType;
    }

    public void setRecoveryType(String recoveryType) {
        this.recoveryType = recoveryType;
    }

    @Column(name = "RECOVERY_STATUS", nullable = false)
    public String getRecoveryStatus() {
        return recoveryStatus;
    }

    public void setRecoveryStatus(String recoveryStatus) {
        this.recoveryStatus = recoveryStatus;
    }

    @Column(name = "AMOUNT_RECOVERED")
    public BigDecimal getAmountRecovered() {
        return amountRecovered;
    }

    public void setAmountRecovered(BigDecimal amountRecovered) {
        this.amountRecovered = amountRecovered;
    }

    @Column(name = "RECOVERED_FROM")
    public String getRecoveredFrom() {
        return recoveredFrom;
    }

    public void setRecoveredFrom(String recoveredFrom) {
        this.recoveredFrom = recoveredFrom;
    }
}
