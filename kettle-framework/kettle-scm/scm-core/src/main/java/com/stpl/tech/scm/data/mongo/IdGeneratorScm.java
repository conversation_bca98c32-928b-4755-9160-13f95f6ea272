/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.mongo;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IdGeneratorScm", propOrder = { "_id","name", "nextId" })
@Document(collection="IdGeneratorsScm")
public class IdGeneratorScm implements Serializable {

    private static final long serialVersionUID = 9196734885916273962L;
    @Id
    private String _id;
    @Field
    protected String name;
    @Field
    protected int nextId;


    public String get_id() {
        return _id;
    }

    public void set_id(String objectId) {
        this._id = objectId;
    }

    /**
     * Gets the value of the name property.
     *
     * @return possible object is {@link String }
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the value of the name property.
     *
     * @param value allowed object is {@link String }
     */
    public void setName(String value) {
        this.name = value;
    }

    /**
     * @return the nextId
     */
    public int getNextId() {
        return nextId;
    }

    /**
     * @param nextId the nextId to set
     */
    public void setNextId(int nextId) {
        this.nextId = nextId;
    }
}

