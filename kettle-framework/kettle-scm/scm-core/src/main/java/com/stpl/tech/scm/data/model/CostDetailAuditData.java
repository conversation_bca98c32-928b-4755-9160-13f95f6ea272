/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * ProductRecipeCost generated by hbm2java
 */
@SuppressWarnings("serial")
@MappedSuperclass
public class CostDetailAuditData implements java.io.Serializable {

	private Integer costDetailAuditDataId;
	private int costDetailDataId;
	private int keyId;
	private String keyType;
	private int unitId;
	private BigDecimal quantity;
	private BigDecimal oldPrice;
	private BigDecimal price;
	private String transactionType;
	private String cancellation;
	private String uom;
	private Date expiryDate;
	private Date addTime;

	/**
	 * @return the productRecipeCostId
	 */
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COST_DETAIL_DATA_AUDIT_ID", unique = true, nullable = false)
	public Integer getCostDetailAuditDataId() {
		return costDetailAuditDataId;
	}

	/**
	 * @param costDetailDataId
	 *            the costDetailDataId to set
	 */
	public void setCostDetailAuditDataId(Integer costDetailDataId) {
		this.costDetailAuditDataId = costDetailDataId;
	}

	@Column(name = "COST_DETAIL_DATA_ID", nullable = false)
	public int getCostDetailDataId() {
		return costDetailDataId;
	}

	public void setCostDetailDataId(int costDetailDataId) {
		this.costDetailDataId = costDetailDataId;
	}

	@Column(name = "KEY_ID")
	public int getKeyId() {
		return keyId;
	}

	public void setKeyId(int keyId) {
		this.keyId = keyId;
	}

	@Column(name = "KEY_TYPE")
	public String getKeyType() {
		return keyType;
	}

	public void setKeyType(String keyType) {
		this.keyType = keyType;
	}

	@Column(name = "QUANTITY", precision = 16)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Column(name = "OLD_PRICE", precision = 16)
	public BigDecimal getOldPrice() {
		return oldPrice;
	}

	public void setOldPrice(BigDecimal oldPrice) {
		this.oldPrice = oldPrice;
	}

	@Column(name = "PRICE", precision = 16)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal cost) {
		this.price = cost;
	}

	@Column(name = "UNIT_OF_MEASURE", nullable = false)
	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXPIRY_DATE", nullable = false, length = 10)
	public Date getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(Date expiryDate) {
		this.expiryDate = expiryDate;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "TRANSACTTION_TYPE", nullable = false)
	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String trigger) {
		this.transactionType = trigger;
	}
	
	@Column(name = "IS_CANCELLATION", nullable = false)
	public String getCancellation() {
		return cancellation;
	}

	public void setCancellation(String cancellation) {
		this.cancellation = cancellation;
	}
	
	

}