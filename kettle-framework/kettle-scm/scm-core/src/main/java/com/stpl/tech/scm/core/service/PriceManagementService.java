/**
 * 
 */
package com.stpl.tech.scm.core.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.ScmRecipeProductCost;
import com.stpl.tech.scm.domain.vo.ConsumptionVO;
import com.stpl.tech.scm.domain.vo.ReceivingVO;

/**
 * <AUTHOR>
 *
 */
public interface PriceManagementService {

	// While adding the receivings, we need to do the following
	// 1. create the entry in audit table as well as the drill down table.
	// 2. Incase prices differ from the latest then we need to add a new row and
	// mark it as latest.
	// 3. Incase the prices match then we need to topup the inventory.
	// 4. Incase the prices are not found then a new entry is made.

	public ReceivingVO addReceiving(ReceivingVO rec);

	// While reducing the consumption we need to do the following
	// 1. get all the inventory data for all the listed products in the
	// consumption VO.
	// 2. Try to fulfill all the required inventory for consumable using the
	// fifo for quantity of a product or a cafe.
	// 3. Get the weighted mean of the product/sku and set against each item.
	// 4. Set the drilldowns for each entry from the inventory entries being
	// consumed at that point of time.
	public ConsumptionVO reduceConsumbale(ConsumptionVO rec);

	
	public List<CostDetail> getPriceDetails(int unitId,String keyType,int keyId);
	
	public boolean addPriceDetails(CostDetail costDetail) throws InventoryUpdateException;
	
	public boolean updatePriceDetails(CostDetail costDetail);
	
	public List<CostDetail> getPriceDetailsForUnit(int unitId);

	public Map<Integer, BigDecimal> getWeightedPriceDetailsForProducts(int unitId, String keyType,List<Integer> productIds);

	Map<Integer, ScmRecipeProductCost> getScmRecipeCost(String region, List<Integer> productIdList);
}
