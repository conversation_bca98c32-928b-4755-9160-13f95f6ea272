package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SCMProfileManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;
import com.stpl.tech.scm.data.dao.SCMProductManagementDao;
import com.stpl.tech.scm.data.model.CategoryDefinitionData;
import com.stpl.tech.scm.data.model.DerivedMappingData;
import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.PlanOrderItemData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.ProductFulfillmentTypeData;
import com.stpl.tech.scm.data.model.ProductPackagingMappingData;
import com.stpl.tech.scm.data.model.ProfileDefinitionData;
import com.stpl.tech.scm.data.model.SkuAttributeValueData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.SkuPackagingMappingData;
import com.stpl.tech.scm.data.model.SubCategoryDefinitionData;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.scm.domain.model.CategoryDefinition;
import com.stpl.tech.scm.domain.model.DerivedMapping;
import com.stpl.tech.scm.domain.model.EntityAttributeValueMapping;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PlanOrderItem;
import com.stpl.tech.scm.domain.model.ProductBasicDetail;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPackagingMapping;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.ProfileDefinition;
import com.stpl.tech.scm.domain.model.SkuAttributeValue;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitProductPackagingMapping;
import com.stpl.tech.scm.domain.model.UnitProductsVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
@Service

public class SCMProductManagementServiceImpl implements SCMProductManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(SCMProductManagementServiceImpl.class);

    @Autowired
    private SCMProductManagementDao scmProductManagementDao;

    @Autowired
    private SCMMetadataDao scmMetadataDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private TaxDataCache taxCache;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private SCMProfileManagementService scmProfileManagementService;

    @Autowired
    private MappingCache mappingCacheService;

    @Autowired
    private SCMMetadataService scmMetadataService;




    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductDefinition viewProduct(int productId) {
        return scmCache.getProductDefinition(productId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductDefinition>  viewAllProducts(Integer unitId, boolean getArchived) {
        UnitCategory family = masterDataCache.getUnit(unitId).getFamily();
        boolean isFrontEndUnit = SCMUtil.isCafe(family) || SCMUtil.isOffice(family);
        List<ProductDefinition> products = new ArrayList<>();
        List<ProductDefinition> cacheProducts = scmCache.getProductDefinitions().values().stream().collect(Collectors.toList());
        if (!getArchived) {
            cacheProducts = scmCache.getProductDefinitions().values().stream()
                    .filter(productDefinition -> !productDefinition.getProductStatus().equals(ProductStatus.ARCHIVED))
                    .collect(Collectors.toList());
        }
        if (isFrontEndUnit) {
            cacheProducts.stream()
                    .filter(p -> p.isAvailableAtCafe() || p.isAvailableForCafeInventory())
                    .forEach(product -> {
                        ProductDefinition productDefinition = cloneProduct(product);
                        products.add(productDefinition);
                    });
        } else {
            products.addAll(cacheProducts);
        }
        return products;
    }

    private ProductDefinition cloneProduct(ProductDefinition definition) {
        ProductDefinition productDefinition = new ProductDefinition();
        productDefinition.setUnitOfMeasure(definition.getUnitOfMeasure());
        productDefinition.setCategoryDefinition(definition.getCategoryDefinition());
        productDefinition.setSubCategoryDefinition(definition.getSubCategoryDefinition());
        productDefinition.setCreatedBy(definition.getCreatedBy());
        productDefinition.setCreationDate(definition.getCreationDate());
        productDefinition.setFulfillmentType(definition.getFulfillmentType());
        productDefinition.setDefaultFulfillmentType(definition.getDefaultFulfillmentType());
        productDefinition.getDerivedMappings().addAll(definition.getDerivedMappings());
        productDefinition.setAvailableAtCafe(definition.isAvailableAtCafe());
        productDefinition.setInterCafeTransfer(definition.isInterCafeTransfer());
        productDefinition.setAvailableForCafeInventory(definition.isAvailableForCafeInventory());
        productDefinition.setHasCase(definition.isHasCase());
        productDefinition.setHasInner(definition.isHasInner());
        productDefinition.setBulkGRAllowed(definition.isBulkGRAllowed());
        productDefinition.setProductCode(definition.getProductCode());
        productDefinition.setProductDescription(definition.getProductDescription());
        productDefinition.setProductId(definition.getProductId());
        productDefinition.setProductName(definition.getProductName());
        productDefinition.setProductStatus(definition.getProductStatus());
        productDefinition.setShelfLifeInDays(definition.getShelfLifeInDays());
        productDefinition.setStockKeepingFrequency(definition.getStockKeepingFrequency());
        productDefinition.setSupportsLooseOrdering(definition.isSupportsLooseOrdering());
        productDefinition.setVariantLevelOrdering(definition.isVariantLevelOrdering());
        productDefinition.setProductImage(definition.getProductImage());
        productDefinition.setSupportsSpecialOrdering(definition.isSupportsSpecialOrdering());
        productDefinition.setTaxCode(definition.getTaxCode());
        productDefinition.setUnitPrice(definition.getUnitPrice());
        productDefinition.setNegotiatedUnitPrice(definition.getNegotiatedUnitPrice());
        productDefinition.setParticipatesInRecipe(definition.isParticipatesInRecipe());
        productDefinition.setParticipatesInCafeRecipe(definition.isParticipatesInCafeRecipe());
        productDefinition.setAssetOrdering(definition.getAssetOrdering());
        productDefinition.setVarianceType(definition.getVarianceType());
        productDefinition.setKitchenVarianceType(definition.getKitchenVarianceType());
        productDefinition.setProfileId(definition.getProfileId());
        return productDefinition;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SCMProductManagementService#
     * viewAllBasicProducts()
     */
    @Override
    public List<ProductBasicDetail> viewAllBasicProducts(Integer unitId, boolean getArchived) {
        List<ProductBasicDetail> products = new ArrayList<>();
        if (unitId == null) {
            scmCache.getProductDefinitions().values().forEach((record) -> {
                products.add(SCMDataConverter.convert(record));
            });
        } else {
            viewAllProducts(unitId, getArchived).forEach((record) -> {
                products.add(SCMDataConverter.convert(record));
            });
        }
        return products;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SCMProductManagementService#
     * viewAllBasicProducts()
     */
    @Override
    public List<ProductBasicDetail> viewAllBasicProductsForRecipe(Boolean isScm) {
        List<ProductBasicDetail> products = new ArrayList<>();
        scmCache.getProductDefinitions().values().forEach((record) -> {
            if (isRecipeProduct(record, isScm)) {
                products.add(SCMDataConverter.convert(record));
            }
        });
        return products;
    }

    private boolean isRecipeProduct(ProductDefinition record, Boolean isScm) {
        if (isScm) {
            return ProductStatus.ACTIVE.equals(record.getProductStatus()) && (record.isParticipatesInRecipe() || record.isRecipeRequired());
        }
        else {
            return ProductStatus.ACTIVE.equals(record.getProductStatus()) && (record.isParticipatesInCafeRecipe() || record.isRecipeRequired());
        }

    }

    @Override
    public List<ProductDefinition> viewAllProductsForCafeInventory() {
        return scmCache.getProductDefinitions().values().stream()
                .filter(productDefinition -> ProductStatus.ACTIVE.equals(productDefinition.getProductStatus())
                        && productDefinition.isAvailableForCafeInventory()
                ).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, ProductDefinition> getAllProductMaps() {
        return scmCache.getProductDefinitions();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, ProductDefinition> getAllVariantProductMaps() {
        Map<Integer, ProductDefinition> variantProductMap = new HashMap<Integer, ProductDefinition>();
        for (ProductDefinition productDefinition : scmCache.getProductDefinitions().values()) {
            if (productDefinition.isSupportsLooseOrdering()) {
                variantProductMap.put(productDefinition.getProductId(), productDefinition);
            }
        }
        return variantProductMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ProductDefinition addNewProduct(ProductDefinition productDefinition , Integer userId) throws DataUpdationException, SumoException {
        productDefinition.setCreationDate(SCMUtil.getCurrentDateIST());
        productDefinition.setProductStatus(ProductStatus.IN_ACTIVE);
        CategoryDefinition categoryDefinition = scmCache.getCategoryDefinitions()
                .get(productDefinition.getCategoryDefinition().getId());
        ProductDefinitionData pdd = SCMDataConverter.convert(productDefinition, categoryDefinition,
                scmCache.getSubCategoryDefinitions().get(productDefinition.getSubCategoryDefinition().getId()));
        if (productDefinition.getCategoryDefinition().getId() == 3) {
            ProfileDefinition profileDefinition = scmCache.getProfileDefinition(productDefinition.getProfileId());
            ProfileDefinitionData profileDefinitionData = SCMDataConverter.convert(profileDefinition);
            pdd.setProfileDefinitionData(profileDefinitionData);
        }
        pdd.setDivisionId(productDefinition.getDivisionId());
        pdd.setDepartmentId(productDefinition.getDepartmentId());
        pdd.setClassificationId(productDefinition.getClassificationId());
        pdd.setSubClassificationId(productDefinition.getSubClassificationId());

        pdd = scmProductManagementDao.add(pdd, true);
        if (pdd != null) {
            String code = SCMUtil.generateProductOrSkuCode(ProductDefinition.class,
                    pdd.getCategoryDefinition().getCategoryCode(), pdd.getProductId());
            if (code != null) {
                pdd.setProductCode(code);
                pdd = scmProductManagementDao.update(pdd, true);
                if (pdd != null) {
                    IdCodeName createdBy = SCMUtil.generateIdCodeName(pdd.getCreatedBy(), "",
                            masterDataCache.getEmployees().get(pdd.getCreatedBy()));
                    addDefaultPackaging(pdd);
                    List<DerivedMapping> mappings = saveDerivedMappings(pdd, productDefinition);
                    productDefinition = SCMDataConverter.convert(pdd, createdBy);
                    if (mappings != null) {
                        productDefinition.getDerivedMappings().clear();
                        productDefinition.getDerivedMappings().addAll(mappings);
                    }
                    scmCache.getProductDefinitions().put(pdd.getProductId(), SCMDataConverter.convert(pdd, createdBy));
                    try {
                        LOG.info("Trying To Send Diff Email On Product : {} , updated By {} ",productDefinition.getProductName() ,
                                masterDataCache.getEmployee(userId));
                        scmMetadataService.saveAuditLog(pdd.getProductId(), AuditChangeLogTypes.PRODUCT.value(), pdd.getCreatedBy(),productDefinition
                                , AuditChangeLogTypes.NEW_ENTRY.value());
                        List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>" , "<EMAIL>"));
                        scmMetadataService.sendDiffEmail(new ProductDefinition(),productDefinition,masterDataCache.getEmployee(userId),
                                "ProductDefination",productDefinition.getProductId(),toEmails, "Product : " + productDefinition.getProductName() , true);
                    }catch (Exception e){
                        LOG.info("Error While Saving Audit Log Data In Mongo",e);
                    }

                    return productDefinition;
                } else {
                    throw new SumoException("Product creation failure", "Error generating product code.");
                }
            } else {
                throw new SumoException("Product creation failure", "Error generating product code.");
            }
        } else {
            throw new SumoException("Product creation failure", "Error creating product. Please contact support.");
        }
    }

    private List<DerivedMapping> saveDerivedMappings(ProductDefinitionData product, ProductDefinition productDefinition) throws DataUpdationException {
        try {
            if (SCMUtil.hasProductFulFillmentTypeMappings(productDefinition)) {
                Map<Integer, DerivedMappingData> existingMappings = product.getDerivedMappingDataList().stream()
                        .collect(Collectors.toMap(DerivedMappingData::getUnitId, Function.identity()));

                List<DerivedMapping> derivedMappings = productDefinition.getDerivedMappings();
                List<DerivedMapping> updatedMappings = new ArrayList<>();

                for (DerivedMapping mapping : derivedMappings) {
                    DerivedMappingData mappingData = existingMappings.get(mapping.getUnit());
                    if (mappingData != null) { // update existing mappings
                        if (mapping.getType() != null) {
                            mappingData.setFulfillmentType(mapping.getType().name());
                        }
                        mappingData.setUnitId(mapping.getUnit());
                        mappingData.setProduct(product);
                        scmProductManagementDao.update(mappingData, false);
                    } else { // add new mappings
                        mappingData = new DerivedMappingData();
                        mappingData.setProduct(product);
                        if (mapping.getType() != null) {
                            mappingData.setFulfillmentType(mapping.getType().name());
                        }
                        mappingData.setUnitId(mapping.getUnit());
                        scmProductManagementDao.add(mappingData, false);
                    }
                    updatedMappings.add(mapping);
                }

                scmProductManagementDao.flush();
                return updatedMappings;
            }
        } catch (Exception e) {
            LOG.error("Exception occurred while creating derived mappings for the product", e);
            throw new DataUpdationException("Error while creating product's derived mappings");
        }
        return null;
    }

    /**
     * @param pdd
     */
    private void addDefaultPackaging(ProductDefinitionData pdd) throws SumoException {
        ProductPackagingMapping ppm = new ProductPackagingMapping();
        ppm.setIsDefault(true);
        ppm.setMappingStatus(SwitchStatus.ACTIVE);
        ppm.setProductId(pdd.getProductId());
        ppm.setPackagingId(scmCache.getLoosePackagingDefinition(UnitOfMeasure.valueOf(pdd.getUnitOfMeasure())).getPackagingId());
        addNewProductPackagingMapping(Arrays.asList(ppm));
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public ProductDefinition updateProduct(ProductDefinition productDefinition,Integer userId) throws DataUpdationException, SumoException {
        ProductDefinitionData pdd = scmProductManagementDao.find(ProductDefinitionData.class,
                productDefinition.getProductId());
        IdCodeName generatedBy = SCMUtil.generateIdCodeName(pdd.getCreatedBy(), "",
                masterDataCache.getEmployees().get(pdd.getCreatedBy()));
        ProductDefinition oldProduct = SCMDataConverter.convert(pdd,generatedBy);
        if (pdd != null) {
            CategoryDefinitionData categoryDefinitionData = scmProductManagementDao
                .find(CategoryDefinitionData.class, productDefinition.getCategoryDefinition().getId());
            pdd.setCategoryDefinition(categoryDefinitionData);

            SubCategoryDefinitionData subCategoryDefinitionData = scmProductManagementDao
                    .find(SubCategoryDefinitionData.class, productDefinition.getSubCategoryDefinition().getId());
            pdd.setSubCategoryDefinition(subCategoryDefinitionData);
            // pdd.setCategoryDefinition(); // product category cannot be
            // changed
            // pdd.setUpdatedBy(); // product Created By cannot be changed
            // pdd.setUpdationDate(); // product Creation Date cannot be changed
            if (productDefinition.getCategoryDefinition().getId() == 3) {
                ProfileDefinition profileDefinition = scmCache.getProfileDefinition(productDefinition.getProfileId());
                ProfileDefinitionData profileDefinitionData = SCMDataConverter.convert(profileDefinition);
                pdd.setProfileDefinitionData(profileDefinitionData);
            }
            pdd.setHasCase(SCMUtil.setStatus(productDefinition.isHasCase()));
            pdd.setHasInner(SCMUtil. setStatus(productDefinition.isHasInner()));
            pdd.setRecipeRequired(SCMUtil.setStatus(productDefinition.isRecipeRequired()));
            pdd.setIsBulkGRAllowed(SCMUtil.setStatus(productDefinition.isBulkGRAllowed()));
            pdd.setProductCode(productDefinition.getProductCode());
            pdd.setProductDescription(productDefinition.getProductDescription());
            pdd.setProductId(productDefinition.getProductId());
            pdd.setProductName(productDefinition.getProductName());
            pdd.setProductStatus(productDefinition.getProductStatus().value());
            pdd.setShelfLifeInDays(productDefinition.getShelfLifeInDays());
            pdd.setStockKeepingFrequency(productDefinition.getStockKeepingFrequency().value());
            pdd.setSupportsLooseOrdering(SCMUtil.setStatus(productDefinition.isSupportsLooseOrdering()));
            pdd.setUnitOfMeasure(productDefinition.getUnitOfMeasure());
            pdd.setProductImage(productDefinition.getProductImage());
            pdd.setUnitPrice(SCMUtil.convertToBigDecimal(productDefinition.getUnitPrice()));
            pdd.setNegotiatedUnitPrice(SCMUtil.convertToBigDecimal(productDefinition.getNegotiatedUnitPrice()));
            pdd.setVariantLevelOrdering(SCMUtil.setStatus(productDefinition.isVariantLevelOrdering()));
            pdd.setSupportsSpecializedOrdering(SCMUtil.setStatus(productDefinition.isSupportsSpecialOrdering()));
            pdd.setTaxCategoryCode(productDefinition.getTaxCode());
            pdd.setAssetOrdering(SCMUtil.setStatus(productDefinition.getAssetOrdering()));
            pdd.setParticipatesInRecipe(SCMUtil.setStatus(productDefinition.isParticipatesInRecipe()));
            pdd.setParticipatesInCafeRecipe(SCMUtil.setStatus(productDefinition.isParticipatesInCafeRecipe()));
            pdd.setFulfillmentType(productDefinition.getFulfillmentType().name());
            pdd.setVarianceType(productDefinition.getVarianceType().name());
            if (Objects.nonNull(productDefinition.getKitchenVarianceType())) {
                pdd.setKitchenVarianceType(productDefinition.getKitchenVarianceType().name());
            }
            pdd.setAutoProduction(SCMUtil.setStatus(productDefinition.isAutoProduction()));
            pdd.setParticipatesInPnl(SCMUtil.setStatus(productDefinition.isParticipatesInPnl()));
            pdd.setSubClassificationId(productDefinition.getSubClassificationId());
            pdd.setClassificationId(productDefinition.getClassificationId());
            pdd.setDepartmentId(productDefinition.getDepartmentId());
            pdd.setDivisionId(productDefinition.getDivisionId());
            if (FulfillmentType.DERIVED.equals(productDefinition.getFulfillmentType())) {
                pdd.setDefaultFulfillmentType(productDefinition.getDefaultFulfillmentType().name());
            }
            pdd.setAvailableForCafeInventory(productDefinition.isAvailableForCafeInventory() ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
            pdd.setAvailableForCafe(productDefinition.isAvailableAtCafe() ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
            pdd.setInterCafeTransfer(productDefinition.isInterCafeTransfer() ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
            pdd = scmProductManagementDao.update(pdd, true);
            if (pdd != null) {
                IdCodeName createdBy = SCMUtil.generateIdCodeName(pdd.getCreatedBy(), "",
                        masterDataCache.getEmployees().get(pdd.getCreatedBy()));
                List<DerivedMapping> mappings = saveDerivedMappings(pdd, productDefinition);
                productDefinition = SCMDataConverter.convert(pdd, createdBy);
                if (mappings != null) {
                    productDefinition.getDerivedMappings().clear();
                    productDefinition.getDerivedMappings().addAll(mappings);
                }
                scmCache.getProductDefinitions().remove(pdd.getProductId());
                scmCache.getProductDefinitions().put(pdd.getProductId(), productDefinition);
                updateProductInSkus(productDefinition);
                try{
                    LOG.info("Trying To Send Diff Email On Product : {} , updated By {} ",productDefinition.getProductName() ,
                            masterDataCache.getEmployee(userId));
                    List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>" , "<EMAIL>"));
                    scmMetadataService.sendDiffEmail(oldProduct,productDefinition,masterDataCache.getEmployee(userId),
                            "ProductDefination",oldProduct.getProductId(),toEmails, "Product : " + oldProduct.getProductName() , false);
                    scmMetadataService.saveAuditLog(productDefinition.getProductId(),AuditChangeLogTypes.PRODUCT.value(), userId, (productDefinition),
                            AuditChangeLogTypes.UPDATE_ENTRY.value());
                }catch (Exception e){
                    LOG.info("Error While Saving Audit Log Data In Mongo",e);
                }

                return productDefinition;
            }
        }
        LOG.error("Product with Product productId {} not found to update!", productDefinition.getProductId());
        return null;
    }

    private void updateProductInSkus(ProductDefinition productDefinition) {
        try {
            Map<Integer, List<SkuDefinition>> skuProductMap = viewAllSkuByProduct(productDefinition.getProductId());
            List<SkuDefinition> skus = skuProductMap.get(productDefinition.getProductId());
            if (skus == null) {
                return;
            }
            IdCodeName product = SCMUtil.generateIdCodeName(productDefinition.getProductId(),
                    productDefinition.getTaxCode(), productDefinition.getProductName());
            skus.forEach(skuDefinition -> {
                skuDefinition.setLinkedProduct(product);
                scmCache.getSkuDefinitions().remove(skuDefinition.getSkuId());
                scmCache.getSkuDefinitions().put(skuDefinition.getSkuId(), skuDefinition);
            });
        } catch (Exception e) {
            LOG.error("Error while updating product in sku cache ::::: {}", productDefinition.getProductId(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateProduct(int productId) throws DataUpdationException {
        ProductDefinitionData pdd = scmProductManagementDao.find(ProductDefinitionData.class, productId);
        validateActivation(pdd);
        if (pdd != null) {
            pdd.setProductStatus(ProductStatus.ACTIVE.value());
            pdd = (ProductDefinitionData) scmProductManagementDao.update(pdd, true);
            if (pdd != null) {
                IdCodeName createdBy = SCMUtil.generateIdCodeName(pdd.getCreatedBy(), "",
                        masterDataCache.getEmployees().get(pdd.getCreatedBy()));
                scmCache.getProductDefinitions().put(pdd.getProductId(), SCMDataConverter.convert(pdd, createdBy));
                return true;
            }
        }
        LOG.error("Product with Product productId {} not found to activate!", productId);
        return false;
    }

    /**
     * @param pdd
     * @throws DataUpdationException
     */
    private void validateActivation(ProductDefinitionData pdd) throws DataUpdationException {
        StringBuffer error = new StringBuffer();
        if (pdd.getTaxCategoryCode() == null) {
            error.append("\nTax Category for a product Cannot Be Null ");
        }
        if (taxCache.getTaxCategory(pdd.getTaxCategoryCode()) == null) {
            error.append("\nTax Category for a product Cannot Be Found " + pdd.getTaxCategoryCode());
        }

        if (taxCache.getHsnStateTaxDataMap().get(pdd.getTaxCategoryCode()) == null) {
            error.append("\nGST for Tax Category for a product Cannot Be Found " + pdd.getTaxCategoryCode());
        }
        List<ProductPackagingMapping> packagings = getProductPackagingMappingByProduct(pdd.getProductId());
        if (packagings == null || packagings.size() == 0) {
            error.append("\nPackagings for a product Cannot Be Found ");
        }
        if (error.length() != 0) {
            throw new DataUpdationException(
                    "Error while activating product " + pdd.getProductName() + error.toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateProduct(int productId) {
        ProductDefinitionData pdd = scmProductManagementDao.find(ProductDefinitionData.class, productId);
        if (pdd != null && deactivateSKUs(pdd)) {
            pdd.setProductStatus(ProductStatus.IN_ACTIVE.value());
            pdd = scmProductManagementDao.update(pdd, true);
            if (pdd != null) {
                return updateDataInProductCache(pdd);
            }
        }
        LOG.error("Product with Product productId {} could not be deactivated!", productId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean archiveProduct(int productId) {
        ProductDefinitionData pdd = scmProductManagementDao.find(ProductDefinitionData.class, productId);
        if (pdd != null) {
            pdd.setProductStatus(ProductStatus.ARCHIVED.value());
            pdd = scmProductManagementDao.update(pdd, true);
            if (pdd != null && updateDataInProductCache(pdd)) {
                for (SkuDefinitionData sku : pdd.getSkuDefinitionDataList()) {
                    sku.setSkuStatus(ProductStatus.ARCHIVED.value());
                    scmProductManagementDao.update(sku, false);
                }
                scmProductManagementDao.flush();
            }
        }
        LOG.error("Product with Product productId {} could not be archived!", productId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String uploadProductImage(MimeType mimeType, Integer productId, MultipartFile file) throws SumoException {
        if (productId != null) {
            ProductDefinitionData pdd = scmProductManagementDao.find(ProductDefinitionData.class, productId);
            if (pdd != null) {
                String fileName = productId + "." + mimeType.name().toLowerCase();
                String baseDir = "scm-service/product_image";
                try {
                    FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, fileName, file, true);
                    if (s3File != null) {
                        pdd.setProductImage(fileName);
                        pdd = scmProductManagementDao.add(pdd, true);
                        if (pdd != null) {
                            if (pdd != null) {
                                updateDataInProductCache(pdd);
                            }
                            return fileName;
                        } else {
                            throw new SumoException("Error updating product image entry.");
                        }
                    } else {
                        throw new SumoException("Error uploading image.");
                    }
                } catch (Exception e) {
                    LOG.error("Encountered error while uploading product image", e);
                }
            } else {
                throw new SumoException("Product is not valid.");
            }
        } else {
            throw new SumoException("Provide product identifier.");
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String uploadSkuImage(MimeType mimeType, Integer skuId, MultipartFile file) throws SumoException {
        if (skuId != null) {
            SkuDefinitionData sdd = scmProductManagementDao.find(SkuDefinitionData.class, skuId);
            if (sdd != null) {
                String fileName = skuId + "." + mimeType.name().toLowerCase();
                String baseDir = "scm-service/sku_image";
                try {
                    FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3ProductBucket(), baseDir, fileName, file, true);
                    if (s3File != null) {
                        sdd.setSkuImage(fileName);
                        sdd = scmProductManagementDao.add(sdd, true);
                        if (sdd != null) {
                            IdCodeName createdBy = SCMUtil.generateIdCodeName(sdd.getCreatedBy(), "",
                                    masterDataCache.getEmployees().get(sdd.getCreatedBy()));
                            scmCache.getSkuDefinitions().put(sdd.getSkuId(), SCMDataConverter.convert(sdd, createdBy));
                            return fileName;
                        } else {
                            throw new SumoException("Error updating sku image entry.");
                        }
                    } else {
                        throw new SumoException("Error uploading image.");
                    }
                } catch (Exception e) {
                    LOG.error("Encountered error while uploading sku image", e);
                }
            } else {
                throw new SumoException("Sku is not valid.");
            }
        } else {
            throw new SumoException("Sku product identifier.");
        }
        return null;
    }

    private boolean updateDataInProductCache(ProductDefinitionData pdd) {
        IdCodeName createdBy = SCMUtil.generateIdCodeName(pdd.getCreatedBy(), "",
                masterDataCache.getEmployees().get(pdd.getCreatedBy()));
        scmCache.getProductDefinitions().remove(pdd.getProductId());
        scmCache.getProductDefinitions().put(pdd.getProductId(), SCMDataConverter.convert(pdd, createdBy));
        return true;
    }

    private boolean deactivateSKUs(ProductDefinitionData pdd) {
        boolean flag = true;
        try {
            List<SkuDefinitionData> skuDefinitionDataList = pdd.getSkuDefinitionDataList();
            if (!skuDefinitionDataList.isEmpty()) {
                skuDefinitionDataList.forEach(sdd -> {
                    sdd.setSkuStatus(SwitchStatus.IN_ACTIVE.name());
                    scmProductManagementDao.update(sdd, false);
                    IdCodeName createdBy = SCMUtil.generateIdCodeName(sdd.getCreatedBy(), "",
                            masterDataCache.getEmployees().get(sdd.getCreatedBy()));
                    SkuDefinition skuDefinition = SCMDataConverter.convert(sdd, createdBy);
                    scmCache.getSkuDefinitions().remove(sdd.getSkuId());
                    scmCache.getSkuDefinitions().put(sdd.getSkuId(), skuDefinition);
                    scmCache.getSkuDefinitions().put(sdd.getSkuId(), skuDefinition);
                });
                scmProductManagementDao.flush();
            }
        } catch (Exception e) {
            LOG.error("Error while updating status of the SKU ::::", e);
            return false;
        }
        return flag;
    }

    // Packaging Definition Resources

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<PackagingDefinition> viewAllPackaging() {
        List<PackagingDefinition> packagingDefinitions = new ArrayList<PackagingDefinition>(
                scmCache.getPackagingDefinitions().values());
        return packagingDefinitions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, PackagingDefinition> getAllPackagingMap() {
        return scmCache.getPackagingDefinitions();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PackagingDefinition viewPackaging(int packagingId) {
        return scmCache.getPackagingDefinitions().get(packagingId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addNewPackaging(List<PackagingDefinition> packagingDefinitions) throws SumoException {
        boolean flag = false;
        for (PackagingDefinition packagingDefinition : packagingDefinitions) {
            PackagingDefinitionData definition = scmProductManagementDao.findByPackagingTypeAndPackagingCode(packagingDefinition.getPackagingType(),
                    packagingDefinition.getPackagingCode());
            if (definition == null) {
                PackagingDefinitionData packagingDefinitionData = scmProductManagementDao
                        .add(SCMDataConverter.convert(packagingDefinition), true);
                if (packagingDefinitionData != null) {
                    scmCache.getPackagingDefinitions().put(packagingDefinitionData.getPackagingId(),
                            SCMDataConverter.convert(packagingDefinitionData));
                    flag = true;
                }
            } else {
                throw new SumoException("Packaging already exists!");
            }
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePackaging(PackagingDefinition packagingDefinition) {
        PackagingDefinitionData pdd = scmProductManagementDao.find(PackagingDefinitionData.class,
                packagingDefinition.getPackagingId());
        if (pdd != null) {
            pdd.setPackagingCode(packagingDefinition.getPackagingCode());
            pdd.setUnitOfMeasure(packagingDefinition.getUnitOfMeasure());
            pdd.setPackagingType(packagingDefinition.getPackagingType().value());
            pdd.setPackagingStatus(packagingDefinition.getPackagingStatus().value());
            pdd.setConversionRatio(SCMUtil.convertToBigDecimal(packagingDefinition.getConversionRatio()));
            pdd.setPackagingId(packagingDefinition.getPackagingId());
            pdd.setPackagingName(packagingDefinition.getPackagingName());
            pdd = scmProductManagementDao.update(pdd, true);
            if (pdd != null) {
                scmCache.getPackagingDefinitions().put(pdd.getPackagingId(), SCMDataConverter.convert(pdd));
                return true;
            }
        }
        LOG.error("Packaging with productId {} not found to update!", packagingDefinition.getPackagingId());
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activatePackaging(int packagingDefinitionId) {
        PackagingDefinitionData pdd = scmProductManagementDao.find(PackagingDefinitionData.class,
                packagingDefinitionId);
        if (pdd != null) {
            pdd.setPackagingStatus(SwitchStatus.ACTIVE.value());
            pdd = (PackagingDefinitionData) scmProductManagementDao.update(pdd, true);
            if (pdd != null) {
                scmCache.getPackagingDefinitions().put(pdd.getPackagingId(), SCMDataConverter.convert(pdd));
                return true;
            }
        }
        LOG.error("Packaging with productId {} not found to activate!", packagingDefinitionId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivatePackaging(int packagingDefinitionId) {
        PackagingDefinitionData pdd = scmProductManagementDao.find(PackagingDefinitionData.class,
                packagingDefinitionId);
        if (pdd != null) {
            pdd.setPackagingStatus(SwitchStatus.IN_ACTIVE.value());
            pdd = (PackagingDefinitionData) scmProductManagementDao.update(pdd, true);
            if (pdd != null) {
                scmCache.getPackagingDefinitions().put(pdd.getPackagingId(), SCMDataConverter.convert(pdd));
                return true;
            }
        }
        LOG.error("Packaging with productId {} not found to deactivate!", packagingDefinitionId);
        return false;
    }

    // TODO need to relook at this method, needs refinement
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String, Set<Map<String, Integer>>> getAllSubPackagingMapping() {
        Map<String, Set<Map<String, Integer>>> stringListMap = new TreeMap<String, Set<Map<String, Integer>>>();
        Map<Integer, PackagingDefinition> integerPackagingDefinitionDataMap = scmCache.getPackagingDefinitions();
        for (PackagingDefinition packagingDefinition : integerPackagingDefinitionDataMap.values()) {
            Set<Map<String, Integer>> maps = stringListMap.get(packagingDefinition.getPackagingType());
            if (maps == null) {
                maps = new HashSet<Map<String, Integer>>();
            }
            Map<String, Integer> stringIntegerMap = new HashMap<String, Integer>();
            stringIntegerMap.put(packagingDefinition.getPackagingType().value(), packagingDefinition.getPackagingId());
            PackagingDefinition subPackaging = integerPackagingDefinitionDataMap
                    .get(packagingDefinition.getSubPackagingId());
            while (subPackaging != null) {
                stringIntegerMap.put(subPackaging.getPackagingType().value(), subPackaging.getPackagingId());
                subPackaging = integerPackagingDefinitionDataMap.get(subPackaging.getSubPackagingId());
            }
            maps.add(stringIntegerMap);
            stringListMap.put(packagingDefinition.getPackagingType().value(), maps);
        }
        return stringListMap;
    }

    // Product Packaging Mapping Resources

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<ProductPackagingMapping>> viewAllProductPackagingMapping() {
        Map<Integer, List<ProductPackagingMapping>> productDefinitionListMap = new TreeMap<Integer, List<ProductPackagingMapping>>();
        for (ProductPackagingMapping productPackagingMapping : scmCache.getProductPackagingMappings().values()) {
            List<ProductPackagingMapping> productPackagingMappings = productDefinitionListMap
                    .get(productPackagingMapping.getProductId());
            if (productPackagingMappings == null) {
                productPackagingMappings = new ArrayList<>();
            }
            if (!productPackagingMappings.contains(productPackagingMapping)
                    && productPackagingMapping.getMappingStatus().equals(SwitchStatus.ACTIVE)) {

                productPackagingMappings.add(productPackagingMapping);
                productDefinitionListMap.put(productPackagingMapping.getProductId(), productPackagingMappings);
            }
        }
        return productDefinitionListMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<UnitProductPackagingMapping>> viewUnitProductPackagingMapping(int unitId) {
        Map<Integer, List<UnitProductPackagingMapping>> unitProductPackagingListMap = new TreeMap<>();
        List<UnitProductPackagingMapping> unitProductPackagingMappings = scmProductManagementDao.findPackagingByUnitId(unitId);

        for (UnitProductPackagingMapping unitProductPackagingMapping : unitProductPackagingMappings) {
            if (!unitProductPackagingListMap.containsKey(unitProductPackagingMapping.getProductId())) {
                unitProductPackagingListMap.put(unitProductPackagingMapping.getProductId(), new ArrayList<UnitProductPackagingMapping>());
            }
            if (unitProductPackagingListMap.containsKey(unitProductPackagingMapping.getProductId())) {
                unitProductPackagingListMap.get(unitProductPackagingMapping.getProductId()).add(unitProductPackagingMapping);
            }
        }
        return unitProductPackagingListMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ProductPackagingMapping viewProductPackagingMapping(int productPackagingMappingId) {
        return scmCache.getProductPackagingMappings().get(productPackagingMappingId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductPackagingMapping> addNewProductPackagingMapping(List<ProductPackagingMapping> productPackagingMappings) throws SumoException {
        List<ProductPackagingMapping> mappings = new ArrayList<>();
        for (ProductPackagingMapping productPackagingMapping : productPackagingMappings) {
            if (productPackagingMapping.getProductId() != 0 && productPackagingMapping.getPackagingId() != 0) {
                ProductPackagingMappingData productPackagingMappingData = scmProductManagementDao
                        .add(SCMDataConverter.convert(productPackagingMapping), false);
                if (productPackagingMappingData != null) {
                    ProductPackagingMapping mapping = SCMDataConverter.convert(productPackagingMappingData);
                    scmCache.getProductPackagingMappings().put(
                            productPackagingMappingData.getProductPackagingMappingId(), mapping);
                    mappings.add(mapping);
                } else {
                    throw new SumoException("Error adding product packaging mapping", "Couldn't add product packaging mapping please try again.");
                }
            } else {
                throw new SumoException("Error adding product packaging mapping", "Couldn't add product packaging mapping please try again.");
            }
        }
        scmProductManagementDao.flush();
        return mappings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateProductPackagingMapping(List<ProductPackagingMapping> productPackagingMappings) {
        for (ProductPackagingMapping productPackagingMapping : productPackagingMappings) {
            ProductPackagingMappingData productPackagingMappingData = scmProductManagementDao
                    .find(ProductPackagingMappingData.class, productPackagingMapping.getProductPackagingMappingId());
            if (productPackagingMappingData != null) {
                productPackagingMappingData.setPackagingId(productPackagingMapping.getPackagingId());
                productPackagingMappingData.setProductId(productPackagingMapping.getProductId());
                productPackagingMappingData
                        .setProductPackagingMappingId(productPackagingMapping.getProductPackagingMappingId());
                productPackagingMappingData.setMappingStatus(productPackagingMapping.getMappingStatus().value());
                productPackagingMappingData = (ProductPackagingMappingData) scmProductManagementDao
                        .update(productPackagingMappingData, false);
                scmCache.getProductPackagingMappings().put(productPackagingMappingData.getProductPackagingMappingId(),
                        SCMDataConverter.convert(productPackagingMappingData));
            } else {
                LOG.error("Product packaging mapping productId: not found for update!");
            }
        }
        scmProductManagementDao.flush();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateProductPackagingMapping(int productPackagingMappingId) {
        ProductPackagingMappingData productPackagingMappingData = scmProductManagementDao
                .find(ProductPackagingMappingData.class, productPackagingMappingId);
        if (productPackagingMappingData != null) {
            productPackagingMappingData.setMappingStatus(SwitchStatus.IN_ACTIVE.value());
            productPackagingMappingData = scmProductManagementDao.update(productPackagingMappingData, true);
            scmCache.getProductPackagingMappings().put(productPackagingMappingData.getProductPackagingMappingId(),
                    SCMDataConverter.convert(productPackagingMappingData));
            return true;
        }
        LOG.error("Product packaging mapping productId: {} not found for deactivation!", productPackagingMappingId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateProductPackagingMapping(int productPackagingMappingId) {
        ProductPackagingMappingData productPackagingMappingData = scmProductManagementDao
                .find(ProductPackagingMappingData.class, productPackagingMappingId);
        if (productPackagingMappingData != null) {
            productPackagingMappingData.setMappingStatus(SwitchStatus.ACTIVE.value());
            productPackagingMappingData = scmProductManagementDao.update(productPackagingMappingData, true);
            scmCache.getProductPackagingMappings().put(productPackagingMappingData.getProductPackagingMappingId(),
                    SCMDataConverter.convert(productPackagingMappingData));
            return true;
        }
        LOG.error("Product packaging mapping productId: {} not found for activation!", productPackagingMappingId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setDefaultProductPackaging(int productPackagingMappingId) throws DataUpdationException {
        try {
            ProductPackagingMappingData productPackagingMappingData = scmProductManagementDao
                    .find(ProductPackagingMappingData.class, productPackagingMappingId);
            List<ProductPackagingMappingData> productPackagingMappingDataList = scmProductManagementDao
                    .getPackagingMappingsForProduct(productPackagingMappingData.getProductId());
            Map<Integer, ProductPackagingMapping> cacheMap = scmCache.getProductPackagingMappings();
            for (ProductPackagingMappingData data : productPackagingMappingDataList) {
                if (data.getProductPackagingMappingId().intValue() == productPackagingMappingId) {
                    data.setIsDefault(SCMUtil.setStatus(true));
                } else {
                    data.setIsDefault(SCMUtil.setStatus(false));
                }
                cacheMap.put(data.getProductPackagingMappingId(), SCMDataConverter.convert(data));
            }
            return true;
        } catch (Exception ex) {
            LOG.info("Error setting default product packaging ", ex.getMessage());
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductPackagingMapping> getProductPackagingMappingByProduct(int productId) {
        List<ProductPackagingMapping> productPackagingMappings = new ArrayList<ProductPackagingMapping>();
        for (ProductPackagingMapping productPackagingMapping : scmCache.getProductPackagingMappings().values()) {
            if (productPackagingMapping.getProductId() == productId) {
                productPackagingMappings.add(productPackagingMapping);
            }
        }
        return productPackagingMappings;
    }

    // SKU Definition Resources

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SkuDefinition viewSku(int skuId) {
        return scmCache.getSkuDefinitions().get(skuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuDefinition> viewAllSku() {
        List<SkuDefinition> skus = new ArrayList<SkuDefinition>();
        for (SkuDefinition skuDefinition : scmCache.getSkuDefinitions().values()) {
            SkuDefinition sku = SCMUtil.clone(skuDefinition, SkuDefinition.class);
            getPackaging(sku);
            skus.add(sku);
        }
        return skus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuDefinition> viewAllSku(int productId) {
        List<SkuDefinition> skus = new ArrayList<SkuDefinition>();
        for (SkuDefinition skuDefinition : scmCache.getSkuDefinitions().values()) {
        	if(skuDefinition.getLinkedProduct().getId() != productId) {
        		continue;
        	}
            SkuDefinition sku = SCMUtil.clone(skuDefinition, SkuDefinition.class);
            getPackaging(sku);
            skus.add(sku);
        }
        return skus;
    }

    private void getPackaging(SkuDefinition sku){
        Map<Integer, SkuAttributeValue> attributes = scmCache.getSkuAttributeValues();
        Map<Integer, SkuPackagingMapping> packagingList = scmCache.getSkuPackagingMappings();
        if (attributes != null) {
            Set<SkuAttributeValue> skuAttributes = null;
            skuAttributes = attributes.values().stream()
                    .filter(skuAttributeValue -> sku.getSkuId().equals(skuAttributeValue.getSkuId())).distinct()
                    .collect(Collectors.toSet());
            sku.getSkuAttributes().clear();
            sku.getSkuAttributes().addAll(skuAttributes);
        }
        if (packagingList != null) {
            Set<SkuPackagingMapping> packagingListForSKU = null;
            packagingListForSKU = packagingList.values().stream()
                    .filter(skuPackagingMapping -> sku.getSkuId().equals(skuPackagingMapping.getSkuId())).distinct()
                    .collect(Collectors.toSet());
            sku.getSkuPackagings().clear();
            sku.getSkuPackagings().addAll(packagingListForSKU);
        }
    }

    @Override
    public Map<Integer,List<SkuDefinition>> viewAllActiveSkuByUnitId( Integer unitId , List<Integer> productIds) throws SumoException {
        Map<Integer,List<SkuDefinition>> productToSkuMap = new HashMap<>();
        Set<Integer> skuIds = scmCache.getAvailableSKUForUnit(unitId);
        for (Integer skuId : skuIds) {
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuId);
            if(!skuDefinition.getSkuStatus().equals(SwitchStatus.ACTIVE) || !productIds.contains(skuDefinition.getLinkedProduct().getId())) {
                continue;
            }
            if(!productToSkuMap.containsKey(skuDefinition.getLinkedProduct().getId())){
                productToSkuMap.put(skuDefinition.getLinkedProduct().getId(),new ArrayList<>());
            }
            SkuDefinition sku = SCMUtil.clone(skuDefinition, SkuDefinition.class);
            getPackaging(sku);
            productToSkuMap.get(skuDefinition.getLinkedProduct().getId()).add(sku);
        }
        return productToSkuMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SkuDefinition addNewSku(SkuDefinition skuDefinition , Integer userId) throws SumoException {
        if (isFirstSKUForProduct(skuDefinition.getLinkedProduct().getId())) {
            skuDefinition.setIsDefault(true);
        }
        skuDefinition.setCreationDate(SCMUtil.getCurrentTimestamp());
        skuDefinition.setSkuStatus(SwitchStatus.IN_ACTIVE);
        skuDefinition.setPriceLastUpdated(SCMUtil.getCurrentTimestamp());
        List<EntityAttributeValueMapping> entityAttributeValueMappingList = skuDefinition.getEntityAttributeValueMappings();
        SkuDefinitionData skuDefinitionData = scmProductManagementDao.add(getSkuDefinitionDataObject(skuDefinition), true);
        if (skuDefinitionData != null) {
            String code = SCMUtil.generateProductOrSkuCode(SkuDefinition.class,
                    skuDefinitionData.getLinkedProduct().getCategoryDefinition().getCategoryCode(), skuDefinitionData.getSkuId());
            if (code != null) {
                skuDefinitionData.setSkuCode(code);
                skuDefinitionData = scmProductManagementDao.update(skuDefinitionData, true);
                if (skuDefinitionData != null) {
                    for (SkuAttributeValue skuAttributeValue : skuDefinition.getSkuAttributes()) {
                        skuAttributeValue.setSkuId(skuDefinitionData.getSkuId());
                    }
                    for (SkuPackagingMapping skuPackaging : skuDefinition.getSkuPackagings()) {
                        skuPackaging.setSkuId(skuDefinitionData.getSkuId());
                    }
                    addNewSkuAttributeValues(skuDefinition.getSkuAttributes());
                    addNewSkuPackagingMapping(skuDefinition.getSkuPackagings());
                    IdCodeName createdBy = SCMUtil.generateIdCodeName(skuDefinitionData.getCreatedBy(), "",
                            masterDataCache.getEmployees().get(skuDefinitionData.getCreatedBy()));
                    List<SkuAttributeValue> skuAttributeValueList = skuDefinition.getSkuAttributes();
                    skuDefinition = SCMDataConverter.convert(skuDefinitionData, createdBy);
                    if(!skuAttributeValueList.isEmpty()){
                        skuDefinition.setSkuAttributes(skuAttributeValueList);
                    }
                    updateIsDefaultFlagForSkus(skuDefinition);
                    /*
                        register entity mapping against attributes if linked product is of type FIXED_ASSET
                     */
                    if (isEntityMappingRequired(skuDefinition.getLinkedProduct().getId())) {
                        List<EntityAttributeValueMapping> list = new ArrayList<EntityAttributeValueMapping>();
                        for (EntityAttributeValueMapping valueMapping : entityAttributeValueMappingList) {
                            valueMapping.setEntityId(skuDefinitionData.getSkuId());
                            if (valueMapping.getAttributeValueId() != null) {
                                list.add(valueMapping);
                            }
                        }
                        List<EntityAttributeValueMapping> entityAttributeValueMappings = scmProfileManagementService
                                .addGenericAttributeValueMappings(list);
                        skuDefinition.setEntityAttributeValueMappings(entityAttributeValueMappings);
                    }
                    scmCache.getSkuDefinitions().put(skuDefinitionData.getSkuId(), skuDefinition);
                    mappingCacheService.removeSkuListForProduct(skuDefinition.getLinkedProduct().getId());
                    try{
                        LOG.info("Trying To Send Diff Email On Sku : {} , updated By {} ",skuDefinition.getSkuName() ,
                                masterDataCache.getEmployee(userId));
                        List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>","<EMAIL>"));
                        scmMetadataService.sendDiffEmail(new SkuDefinition(),skuDefinition,masterDataCache.getEmployee(userId),
                                "SkuDefination", skuDefinition.getSkuId(), toEmails, "SKU : " + skuDefinition.getSkuName() , true);
                    }catch (Exception e){
                        LOG.info("Error While Finding Diff On SKU : {} ::::::",skuDefinition.getSkuName(),e);
                    }
                    return skuDefinition;
                } else {
                    throw new SumoException("Sku creation failure", "Could not update SKU code.");
                }
            } else {
                throw new SumoException("Sku creation failure", "Could not create SKU code.");
            }
        } else {
            throw new SumoException("Sku creation failure", "Could not create SKU. Please contact support.");
        }
    }

    private boolean isFirstSKUForProduct(int productId) {
        List<SkuDefinitionData> skuDefinitionDataList = scmProductManagementDao.getSkuAgainstProduct(productId);
        if (skuDefinitionDataList.size() == 0) {
            return true;
        }
        return false;
    }

    private boolean isEntityMappingRequired(int procuctId) {
        ProductDefinitionData productDefinitionData = scmProductManagementDao.find(ProductDefinitionData.class, procuctId);
        if (productDefinitionData.getCategoryDefinition().getCategoryId() == 3 && productDefinitionData.getProfileDefinitionData() != null) {
            return true;
        }
        return false;
    }




    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SkuDefinition updateSku(SkuDefinition skuDefinition , Integer userId) throws SumoException {
        SkuDefinitionData sdd = scmProductManagementDao.find(SkuDefinitionData.class, skuDefinition.getSkuId());
        SkuDefinition oldSku = SCMDataConverter.convert(sdd,skuDefinition.getCreatedBy());
        SkuDefinition newSku = null;
        if (sdd != null) {
            sdd.setSkuDescription(skuDefinition.getSkuDescription());
            sdd.setShelfLifeInDays(skuDefinition.getShelfLifeInDays());
            sdd.setSupportsLooseOrdering(SCMUtil.setStatus(skuDefinition.isSupportsLooseOrdering()));
            sdd.setHasCase(SCMUtil.setStatus(skuDefinition.isHasCase()));
            sdd.setHasInner(SCMUtil.setStatus(skuDefinition.isHasInner()));
            sdd.setIsDefault(SCMUtil.setStatus(skuDefinition.isIsDefault()));
            // sdd.setSkuId(skuDefinition.getSkuId());
            sdd.setSkuName(skuDefinition.getSkuName());
            sdd.setSkuStatus(skuDefinition.getSkuStatus().value());
            sdd.setSupportsLooseOrdering(SCMUtil.setStatus(skuDefinition.isSupportsLooseOrdering()));
            sdd.setInventoryList(skuDefinition.getInventoryList());
            if(Objects.nonNull(skuDefinition.isIsBranded())){
                sdd.setIsBranded(SCMUtil.setStatus(skuDefinition.isIsBranded()));
            }
            sdd.setBrand(skuDefinition.getBrand());

            // price should be updated from sku price update UI
            // sdd.setUnitPrice(BigDecimal.valueOf(skuDefinition.getUnitPrice()));
            // sdd.setNegotiatedUnitPrice(BigDecimal.valueOf(skuDefinition.getNegotiatedUnitPrice()));
            sdd.setTorqusSKU(skuDefinition.getTorqusSkuName());
            sdd.setTaxCategoryCode(skuDefinition.getTaxCode());
            if (Objects.nonNull(skuDefinition.getVoDisContinuedFrom())) {
                sdd.setVoDisContinuedFrom(AppUtils.getDate(skuDefinition.getVoDisContinuedFrom()));
            } else {
                sdd.setVoDisContinuedFrom(skuDefinition.getVoDisContinuedFrom());
            }
            if (Objects.nonNull(skuDefinition.getRoDisContinuedFrom())) {
                sdd.setRoDisContinuedFrom(AppUtils.getDate(skuDefinition.getRoDisContinuedFrom()));
            } else {
                sdd.setRoDisContinuedFrom(skuDefinition.getRoDisContinuedFrom());
            }
            sdd = scmProductManagementDao.update(sdd, true);
            newSku = SCMDataConverter.convert(sdd,skuDefinition.getCreatedBy());
            addUpdateSkuAttributeValues(skuDefinition);
            // this has another UI
            // addUpdateSkuPackagingMapping(skuDefinition);
            List<EntityAttributeValueMapping> entityAttributeValueMappingList = skuDefinition.getEntityAttributeValueMappings();
            if (isEntityMappingRequired(skuDefinition.getLinkedProduct().getId())) {
                List<EntityAttributeValueMapping> list = new ArrayList<>();
                List<EntityAttributeValueMapping> updateList = new ArrayList<>();
                for (EntityAttributeValueMapping valueMapping : entityAttributeValueMappingList) {
                    if (valueMapping.getEntityAttributeValueMappingId() != null) {
                        // update previous entity
                        valueMapping.setEntityId(sdd.getSkuId());
                        if (valueMapping.getAttributeValueId() != null) {
                            updateList.add(valueMapping);
                        }
                    } else {
                        // Add new entity
                        valueMapping.setEntityId(sdd.getSkuId());
                        if (valueMapping.getAttributeValueId() != null) {
                            list.add(valueMapping);
                        }
                    }
                }
                skuDefinition.setEntityAttributeValueMappings(scmProfileManagementService
                        .addGenericAttributeValueMappings(list));
                skuDefinition.getEntityAttributeValueMappings().addAll(
                        scmProfileManagementService.updateGenericAttributeValueMappings(updateList));
            }
            if (sdd != null) {
                IdCodeName createdBy = SCMUtil.generateIdCodeName(sdd.getCreatedBy(), "",
                        masterDataCache.getEmployees().get(sdd.getCreatedBy()));
                skuDefinition = SCMDataConverter.convert(sdd, createdBy);
                updateIsDefaultFlagForSkus(skuDefinition);
                scmCache.getSkuDefinitions().put(sdd.getSkuId(), skuDefinition);
                for (SkuAttributeValue value : scmCache.getSkuAttributeValues().values()) {
                    if (value.getSkuId() == sdd.getSkuId()) {
                        skuDefinition.getSkuAttributes().add(value);
                    }
                }
                for (SkuPackagingMapping value : scmCache.getSkuPackagingMappings().values()) {
                    if (value.getSkuId() == sdd.getSkuId()) {
                        skuDefinition.getSkuPackagings().add(value);
                    }
                }
                mappingCacheService.removeSkuListForProduct(skuDefinition.getLinkedProduct().getId());
                try{
                    LOG.info("Trying To Send Diff Email On Sku : {} , updated By {} ",skuDefinition.getSkuName() ,
                            masterDataCache.getEmployee(userId));
                    List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>","<EMAIL>"));
                    scmMetadataService.sendDiffEmail(oldSku,newSku,masterDataCache.getEmployee(userId),
                            "skuDefinition", newSku.getSkuId(), toEmails, "SKU : " + oldSku.getSkuName() , false);
                }catch (Exception e){
                    LOG.info("Error While Finding Diff On SKU : {} ::::::",oldSku.getSkuName(),e);
                }
                return skuDefinition;
            }
        }
        LOG.error("Sku with Sku productId {} not found to update!", skuDefinition.getSkuId());
        return null;
    }

    private void updateIsDefaultFlagForSkus(SkuDefinition skuDefinition) {
        try {
            if (skuDefinition.isIsDefault()) {
                Integer productId = skuDefinition.getLinkedProduct().getId();
                scmCache.getSkuDefinitions().values().stream()
                        .filter(sku -> sku.getLinkedProduct().getId().equals(productId)
                                && !sku.getSkuId().equals(skuDefinition.getSkuId()))
                        .forEach(sku -> {
                            sku.setIsDefault(false);
                        });
                scmProductManagementDao.updateIsDefaultFlag(skuDefinition.getSkuId(), productId);
            }
        } catch (Exception e) {
            LOG.error("Error while updating sku definition default flag", e);
        }
    }

    private void addUpdateSkuAttributeValues(SkuDefinition skuDefinition) throws SumoException {
        for (SkuAttributeValue skuAttributeValue : skuDefinition.getSkuAttributes()) {
            skuAttributeValue.setSkuId(skuDefinition.getSkuId());
            SkuAttributeValueData skuAttributeValueData = scmProductManagementDao
                    .fetchSkuAttributeByType(skuDefinition.getSkuId(), skuAttributeValue.getAttributeId());
            if (skuAttributeValueData == null) {
                skuAttributeValueData = scmProductManagementDao.add(SCMDataConverter.convert(skuAttributeValue), false);
            } else {
                skuAttributeValueData.setAttributeValueId(skuAttributeValue.getAttributeValueId());
                scmProductManagementDao.update(skuAttributeValueData, false);
            }
            if (skuAttributeValueData != null) {
                scmCache.getSkuAttributeValues().put(skuAttributeValueData.getSkuAttributeValueId(),
                        SCMDataConverter.convert(skuAttributeValueData));
            }
        }
        scmProductManagementDao.flush();
    }

    private void addUpdateSkuPackagingMapping(SkuDefinition skuDefinition) throws SumoException {
        List<SkuPackagingMappingData> packList = scmProductManagementDao
                .getPackagingMappingsForSku(skuDefinition.getSkuId());

        // turn all inactive
        for (int i = 0; i < packList.size(); i++) {
            packList.get(i).setMappingStatus(SwitchStatus.IN_ACTIVE.name());
        }

        for (SkuPackagingMapping skuPackagingMapping : skuDefinition.getSkuPackagings()) {
            boolean exists = false;
            for (SkuPackagingMappingData data : packList) {
                if (data.getPackagingId() == skuPackagingMapping.getPackagingId()) {
                    exists = true;
                    data.setMappingStatus(SwitchStatus.ACTIVE.name());
                    scmProductManagementDao.add(data, false);
                }
            }
            if (!exists) {
                addNewSkuPackagingMapping(skuPackagingMapping, false);
            }
            scmProductManagementDao.flush();
        }

        packList = scmProductManagementDao.getPackagingMappingsForSku(skuDefinition.getSkuId());
        packList.forEach(p -> {
            scmCache.getSkuPackagingMappings().put(p.getSkuPackagingMappingId(), SCMDataConverter.convert(p));
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateSku(int skuId) {
        SkuDefinitionData sdd = scmProductManagementDao.find(SkuDefinitionData.class, skuId);
        if (sdd != null) {
            sdd.setSkuStatus(SwitchStatus.ACTIVE.value());
            sdd = scmProductManagementDao.update(sdd, true);
            if (sdd != null) {
                IdCodeName createdBy = SCMUtil.generateIdCodeName(sdd.getCreatedBy(), "",
                        masterDataCache.getEmployees().get(sdd.getCreatedBy()));
                scmCache.getSkuDefinitions().put(sdd.getSkuId(), SCMDataConverter.convert(sdd, createdBy));
                return true;
            }
        }
        LOG.error("Sku with Sku productId {} not found to activate!", skuId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateSku(int skuId) {
        SkuDefinitionData sdd = scmProductManagementDao.find(SkuDefinitionData.class, skuId);
        if (sdd != null) {
            sdd.setSkuStatus(SwitchStatus.IN_ACTIVE.value());
            sdd = scmProductManagementDao.update(sdd, true);
            if (sdd != null) {
                IdCodeName createdBy = SCMUtil.generateIdCodeName(sdd.getCreatedBy(), "",
                        masterDataCache.getEmployees().get(sdd.getCreatedBy()));
                scmCache.getSkuDefinitions().put(sdd.getSkuId(), SCMDataConverter.convert(sdd, createdBy));
                return true;
            }
        }
        LOG.error("Sku with Sku productId {} not found to deactivate!", skuId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<SkuPackagingMapping>> viewAllSkuPackagingMapping() {
        Map<Integer, List<SkuPackagingMapping>> integerListMap = new TreeMap<Integer, List<SkuPackagingMapping>>();
        for (SkuPackagingMapping skuPackagingMapping : scmCache.getSkuPackagingMappings().values()) {
            List<SkuPackagingMapping> skuPackagingMappings = integerListMap.get(skuPackagingMapping.getSkuId());
            if (skuPackagingMappings == null) {
                skuPackagingMappings = new ArrayList<SkuPackagingMapping>();
            }
            if (!skuPackagingMappings.contains(skuPackagingMapping)) {
                skuPackagingMappings.add(skuPackagingMapping);
                integerListMap.put(skuPackagingMapping.getSkuId(), skuPackagingMappings);
            }
        }
        return integerListMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SkuPackagingMapping viewSkuPackagingMapping(int skuPackagingMappingId) {
        return scmCache.getSkuPackagingMappings().get(skuPackagingMappingId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<SkuPackagingMapping> addNewSkuPackagingMapping(List<SkuPackagingMapping> skuPackagingMappings) throws SumoException {
        List<SkuPackagingMapping> mappings = new ArrayList<>();
        for (SkuPackagingMapping skuPackagingMapping : skuPackagingMappings) {
            SkuPackagingMapping mapping = addNewSkuPackagingMapping(skuPackagingMapping, true);
            mappings.add(mapping);
        }
        return mappings;
    }

    private SkuPackagingMapping addNewSkuPackagingMapping(SkuPackagingMapping skuPackagingMapping, boolean refreshCache) throws SumoException {
        SkuPackagingMappingData skuPackagingMappingData = new SkuPackagingMappingData();
        skuPackagingMappingData.setMappingStatus(skuPackagingMapping.getMappingStatus().value());
        skuPackagingMappingData.setPackagingId(skuPackagingMapping.getPackagingId());
        skuPackagingMappingData.setSkuId(skuPackagingMapping.getSkuId());
        skuPackagingMappingData.setSkuPackagingMappingId(skuPackagingMapping.getSkuPackagingMappingId());
        skuPackagingMappingData = scmProductManagementDao.add(skuPackagingMappingData, true);
        if (skuPackagingMappingData != null & refreshCache) {
            SkuPackagingMapping mapping = SCMDataConverter.convert(skuPackagingMappingData);
            scmCache.getSkuPackagingMappings().put(skuPackagingMappingData.getSkuPackagingMappingId(),
                    mapping);
            return mapping;
        }
        if (skuPackagingMappingData == null) {
            throw new SumoException("Error adding Sku Packaging mapping.", "Error adding Sku Packaging mapping please try again.");
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuPackagingMapping(List<SkuPackagingMapping> skuPackagingMappings) {
        for (SkuPackagingMapping skuPackagingMapping : skuPackagingMappings) {
            SkuPackagingMappingData skuPackagingMappingData = scmProductManagementDao
                    .find(SkuPackagingMappingData.class, skuPackagingMapping.getSkuPackagingMappingId());
            if (skuPackagingMappingData != null) {
                skuPackagingMappingData.setMappingStatus(skuPackagingMapping.getMappingStatus().value());
                skuPackagingMappingData.setPackagingId(skuPackagingMapping.getPackagingId());
                skuPackagingMappingData.setSkuId(skuPackagingMapping.getSkuId());
                skuPackagingMappingData.setSkuPackagingMappingId(skuPackagingMapping.getSkuPackagingMappingId());
                skuPackagingMappingData = scmProductManagementDao.update(skuPackagingMappingData, false);
                if (skuPackagingMappingData != null) {
                    scmCache.getSkuPackagingMappings().put(skuPackagingMappingData.getSkuPackagingMappingId(),
                            SCMDataConverter.convert(skuPackagingMappingData));
                }
            }
            LOG.error("Sku packaging mapping with productId {} not found to update!",
                    skuPackagingMappingData.getSkuPackagingMappingId());
        }
        scmProductManagementDao.flush();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateSkuPackagingMapping(int skuPackagingMappingId) {
        SkuPackagingMappingData skuPackagingMappingData = scmProductManagementDao.find(SkuPackagingMappingData.class,
                skuPackagingMappingId);
        if (skuPackagingMappingData != null) {
            skuPackagingMappingData.setMappingStatus(SwitchStatus.ACTIVE.value());
            skuPackagingMappingData = scmProductManagementDao.update(skuPackagingMappingData, true);
            if (skuPackagingMappingData != null) {
                scmCache.getSkuPackagingMappings().put(skuPackagingMappingData.getSkuPackagingMappingId(),
                        SCMDataConverter.convert(skuPackagingMappingData));
                return true;
            }
        }
        LOG.error("Sku packaging mapping with productId {} not found to activate!", skuPackagingMappingId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateSkuPackagingMapping(int skuPackagingMappingId) {
        SkuPackagingMappingData skuPackagingMappingData = scmProductManagementDao.find(SkuPackagingMappingData.class,
                skuPackagingMappingId);
        if (skuPackagingMappingData != null) {
            skuPackagingMappingData.setMappingStatus(SwitchStatus.IN_ACTIVE.value());
            skuPackagingMappingData = scmProductManagementDao.update(skuPackagingMappingData, true);
            if (skuPackagingMappingData != null) {
                scmCache.getSkuPackagingMappings().put(skuPackagingMappingData.getSkuPackagingMappingId(),
                        SCMDataConverter.convert(skuPackagingMappingData));
                return true;
            }
        }
        LOG.error("Sku packaging mapping with productId {} not found to deactivate!", skuPackagingMappingId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setDefaultSkuPackaging(int skuPackagingMappingId) throws DataUpdationException {
        try {
            SkuPackagingMappingData skuPackagingMappingData = scmProductManagementDao
                    .find(SkuPackagingMappingData.class, skuPackagingMappingId);
            List<SkuPackagingMappingData> skuPackagingMappingDatas = scmProductManagementDao
                    .getPackagingMappingsForSku(skuPackagingMappingData.getSkuId());
            Map<Integer, SkuPackagingMapping> cacheMap = scmCache.getSkuPackagingMappings();
            for (SkuPackagingMappingData data : skuPackagingMappingDatas) {
                if (data.getSkuPackagingMappingId().intValue() == skuPackagingMappingId) {
                    data.setIsDefault(SCMUtil.setStatus(true));
                } else {
                    data.setIsDefault(SCMUtil.setStatus(false));
                }
                cacheMap.put(data.getSkuPackagingMappingId(), SCMDataConverter.convert(data));
            }
            return true;
        } catch (Exception ex) {
            LOG.info("Error setting default sku packaging ", ex.getMessage());
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, List<SkuAttributeValue>> viewAllSkuAttributeValues() {
        Map<Integer, List<SkuAttributeValue>> skuToSkuAttributeValueMap = new TreeMap<Integer, List<SkuAttributeValue>>();
        for (SkuAttributeValue skuAttributeValue : scmCache.getSkuAttributeValues().values()) {
            List<SkuAttributeValue> skuAttributeValues = skuToSkuAttributeValueMap
                    .get(skuAttributeValue.getAttributeId());
            if (skuAttributeValues == null) {
                skuAttributeValues = new ArrayList<SkuAttributeValue>();
            }
            if (!skuAttributeValues.contains(skuAttributeValue)) {
                skuAttributeValues.add(skuAttributeValue);
                skuToSkuAttributeValueMap.put(skuAttributeValue.getAttributeId(), skuAttributeValues);
            }
        }
        return skuToSkuAttributeValueMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SkuAttributeValue viewSkuAttributeValue(int skuAttributeValueId) {
        return scmCache.getSkuAttributeValues().get(skuAttributeValueId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addNewSkuAttributeValues(List<SkuAttributeValue> skuAttributeValues) throws SumoException {
        for (SkuAttributeValue skuAttributeValue : skuAttributeValues) {
            SkuAttributeValueData skuAttributeValueData = scmProductManagementDao.add(SCMDataConverter.convert(skuAttributeValue), false);
            if (skuAttributeValueData != null) {
                scmCache.getSkuAttributeValues().put(skuAttributeValueData.getSkuAttributeValueId(),
                        SCMDataConverter.convert(skuAttributeValueData));
            }
        }
        scmProductManagementDao.flush();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuAttributeValues(List<SkuAttributeValue> skuAttributeValues) {
        for (SkuAttributeValue skuAttributeValue : skuAttributeValues) {
            SkuAttributeValueData skuAttributeValueData = scmProductManagementDao.update(SCMDataConverter.convert(skuAttributeValue), false);
            if (skuAttributeValueData != null) {
                scmCache.getSkuAttributeValues().put(skuAttributeValueData.getSkuAttributeValueId(),
                        SCMDataConverter.convert(skuAttributeValueData));
            }
        }
        scmProductManagementDao.flush();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateSkuAttributeValue(int skuAttributeValueId) {
        SkuAttributeValue skuAttributeValue = scmCache.getSkuAttributeValues().get(skuAttributeValueId);
        if (skuAttributeValue != null) {
            skuAttributeValue.setMappingStatus(SwitchStatus.ACTIVE);
            SkuAttributeValueData skuAttributeValueData = scmProductManagementDao.update(SCMDataConverter.convert(skuAttributeValue), true);
            scmCache.getSkuAttributeValues().put(skuAttributeValueData.getSkuAttributeValueId(),
                    SCMDataConverter.convert(skuAttributeValueData));
            return true;
        }
        LOG.error("Sku attribute value with skuAttributeValueId {} not found to activate!", skuAttributeValueId);
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean deactivateSkuAttributeValue(int skuAttributeValueId) {
        SkuAttributeValue skuAttributeValue = scmCache.getSkuAttributeValues().get(skuAttributeValueId);
        if (skuAttributeValue != null) {
            skuAttributeValue.setMappingStatus(SwitchStatus.IN_ACTIVE);
            SkuAttributeValueData skuAttributeValueData = scmProductManagementDao.update(SCMDataConverter.convert(skuAttributeValue), true);
            scmCache.getSkuAttributeValues().put(skuAttributeValueData.getSkuAttributeValueId(),
                    SCMDataConverter.convert(skuAttributeValueData));
            return true;
        }
        LOG.error("Sku attribute value with skuAttributeValueId {} not found to activate!", skuAttributeValueId);
        return false;
    }

    @Override
    public Map<Integer, List<SkuDefinition>> viewAllSkuByProduct() {
        Map<Integer, List<SkuDefinition>> skuByProductMap = new HashMap<>();
        List<SkuDefinition> skus = viewAllSku();
        for (SkuDefinition sku : skus) {
            List<SkuDefinition> definitionList = skuByProductMap.get(sku.getLinkedProduct().getId());
            if (definitionList == null) {
                definitionList = new ArrayList<>();
            }
            definitionList.add(sku);
            skuByProductMap.put(sku.getLinkedProduct().getId(), definitionList);
        }
        return skuByProductMap;
    }

	@Override
	public Map<Integer, List<SkuDefinition>> viewAllSkuByProduct(int productId) {
		Map<Integer, List<SkuDefinition>> skuByProductMap = new HashMap<>();
		List<SkuDefinition> skus = viewAllSku(productId);
		skuByProductMap.put(productId, skus);
		return skuByProductMap;
	}


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDetail> getAllVendorDetails() {
        return new ArrayList<VendorDetail>(scmCache.getVendorDetails().values());
    }

    private SkuDefinitionData getSkuDefinitionDataObject(SkuDefinition skuDefinition) {
        SkuDefinitionData skuDefinitionData = new SkuDefinitionData();
        skuDefinitionData.setSupportsLooseOrdering(SCMUtil.setStatus(skuDefinition.isSupportsLooseOrdering()));
        skuDefinitionData.setSkuStatus(skuDefinition.getSkuStatus().value());
        skuDefinitionData.setSkuName(skuDefinition.getSkuName());
        skuDefinitionData.setSkuId(skuDefinition.getSkuId());
        skuDefinitionData.setCreatedBy(skuDefinition.getCreatedBy().getId());
        skuDefinitionData.setCreationDate(skuDefinition.getCreationDate());
        skuDefinitionData.setHasCase(SCMUtil.setStatus(skuDefinition.isHasCase()));
        skuDefinitionData.setHasInner(SCMUtil.setStatus(skuDefinition.isHasInner()));
        skuDefinitionData.setLinkedProduct(
                scmProductManagementDao.find(ProductDefinitionData.class, skuDefinition.getLinkedProduct().getId()));
        skuDefinitionData.setShelfLifeInDays(skuDefinition.getShelfLifeInDays());
        skuDefinitionData.setSkuDescription(skuDefinition.getSkuDescription());
        skuDefinitionData.setUnitOfMeasure(skuDefinition.getUnitOfMeasure());
        skuDefinitionData.setNegotiatedUnitPrice(SCMUtil.convertToBigDecimal(skuDefinition.getNegotiatedUnitPrice()));
        skuDefinitionData.setUnitPrice(SCMUtil.convertToBigDecimal(skuDefinition.getUnitPrice()));
        skuDefinitionData.setPriceLastUpdated(skuDefinition.getPriceLastUpdated());
        skuDefinitionData.setTorqusSKU(skuDefinition.getTorqusSkuName());
        skuDefinitionData.setIsDefault(SCMUtil.setStatus(skuDefinition.isIsDefault()));
        skuDefinitionData.setInventoryList(skuDefinition.getInventoryList());
        skuDefinitionData.setTaxCategoryCode(skuDefinition.getTaxCode());
        if (Objects.nonNull(skuDefinition.getVoDisContinuedFrom())) {
            skuDefinitionData.setVoDisContinuedFrom(AppUtils.getDate(skuDefinition.getVoDisContinuedFrom()));
        }
        if (Objects.nonNull(skuDefinition.getRoDisContinuedFrom())) {
            skuDefinitionData.setRoDisContinuedFrom(AppUtils.getDate(skuDefinition.getRoDisContinuedFrom()));
        }
        if(Objects.nonNull(skuDefinition.isIsBranded())){
            skuDefinitionData.setIsBranded(SCMUtil.setStatus(skuDefinition.isIsBranded()));
        }
        skuDefinitionData.setBrand(skuDefinition.getBrand());

        return skuDefinitionData;
    }

    private boolean containsFulfillmentType(List<ProductFulfillmentTypeData> productFulfillmentTypeDatas,
                                            ProductFulfillmentTypeData productFulfillmentTypeData) {
        for (ProductFulfillmentTypeData productFulfillmentTypeData1 : productFulfillmentTypeDatas) {
            if (productFulfillmentTypeData1.getFulfillmentType()
                    .equals(productFulfillmentTypeData.getFulfillmentType())) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<VendorDetail> getUnitVendorDetails(int unitId) {

        List<VendorDetail> vendorDetails = new ArrayList<VendorDetail>();
        List<UnitVendorMappingData> mappings = scmMetadataDao.getVendorMappings(unitId);
        if (mappings != null && mappings.size() > 0) {
            for (UnitVendorMappingData data : mappings) {
                if (data.getMappingStatus().equals(SwitchStatus.ACTIVE.name())) {
                    VendorDetail vendorDetail = scmCache.getVendorDetail(data.getVendorId());
                    if (vendorDetail.getStatus().equals(VendorStatus.ACTIVE)) {
                        vendorDetails.add(vendorDetail);
                    }
                }
            }
        }
        return vendorDetails;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, Set<VendorDetail>> getUnitProductVendors(UnitProductsVO request) throws SumoException {
        if (request.getUnitId() != null && request.getProductIds().size() > 0) {
            Map<Integer, Set<VendorDetail>> productVendorsMap = new HashMap<>();
            List<UnitSkuVendorMapping> unitSkuVendorMappings = scmProductManagementDao.getUnitSkuVendorMappingsByUnitId(request.getUnitId());
            unitSkuVendorMappings.forEach(unitSkuVendorMapping -> {
                if (scmCache.getSkuDefinition(unitSkuVendorMapping.getUnitSkuMapping().getSkuId()).getSkuStatus().equals(SwitchStatus.ACTIVE)) {
                    request.getProductIds().forEach(productId -> {
                        if (scmCache.getSkuDefinition(unitSkuVendorMapping.getUnitSkuMapping().getSkuId()).getLinkedProduct().getId().equals(productId)) {
                            Set<VendorDetail> vendors = productVendorsMap.get(productId);
                            if (vendors == null) {
                                vendors = new HashSet<>();
                            }
                            vendors.add(scmCache.getVendorDetail(unitSkuVendorMapping.getVendorId()));
                            productVendorsMap.put(productId, vendors);
                        }
                    });
                }
            });
            return productVendorsMap;
        } else {
            if (request.getUnitId() == null) {
                throw new SumoException("Failed to fetch unit product vendors.", "No unit selected in order to fetch unit product vendors.");
            }
            if (request.getProductIds().size() == 0) {
                throw new SumoException("Failed to fetch unit product vendors.", "Product list is empty");
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PlanOrderItem getProductForUnit(int productId, int unitId) throws SumoException {
        PlanOrderItemData planOrderItemData = scmMetadataDao.getPlanOrderData(productId, unitId);
        if (planOrderItemData != null) {
            PlanOrderItem planOrderItem = SCMDataConverter.convert(planOrderItemData, scmCache);
            return planOrderItem;
        } else {
            throw new SumoException("Could Not Find Production Plan For Given Product Id and Unit Id!");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, Integer> getUnitSkuPackagingMappings(int unitId) {
        Map<Integer,Integer> result = new HashMap<>();
        List<Object[]> list = scmProductManagementDao.getUnitSkuPackagingMappings(unitId);
        if(list !=null && !list.isEmpty()){
            for(Object[] obj : list){
                Integer productId = (Integer)obj[0];
                Integer packagingId = (Integer)obj[1];
                if(!result.containsKey(productId)){
                    result.put(productId,packagingId);
                }
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateSubCategoryShelfLife(Integer id , Integer defaultShelfLife , String range){
        SubCategoryDefinitionData subCategoryDefinitionData = scmProductManagementDao.find(SubCategoryDefinitionData.class,id);
        subCategoryDefinitionData.setShelfLifeRange(range);
        subCategoryDefinitionData.setShelfLifeInDays(defaultShelfLife);
        scmProductManagementDao.flush();
        scmCache.refreshCategory();
        return true;
    }
}
