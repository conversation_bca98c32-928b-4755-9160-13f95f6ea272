package com.stpl.tech.scm.data.dao.impl;

import java.util.List;

import javax.persistence.Query;

import org.springframework.stereotype.Repository;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.GatepassManagementDao;
import com.stpl.tech.scm.data.model.GatepassData;
import com.stpl.tech.scm.data.model.GatepassVendorMappingData;
import com.stpl.tech.scm.domain.model.SearchGatepass;

@Repository
public class GatepassManagementDaoImpl extends SCMAbstractDaoImpl implements GatepassManagementDao {


	@Override
	public List<GatepassVendorMappingData> getVendorMappingList(String operationType, Integer unitId, Integer vendorId, String status) {
		StringBuilder query = new StringBuilder("FROM GatepassVendorMappingData g WHERE 1=1 ");
		if (status != null) {
			query.append(" and g.status = :status ");
		}
		if (operationType != null) {
			query.append(" and g.operationType = :operationType ");
		}
		if (unitId != null) {
			query.append(" and g.unitId = :unitId ");
		}
		if(vendorId != null) {
			query.append(" and g.vendorId = :vendorId ");
		}
		Query queryString = manager.createQuery(query.toString());
		if (operationType != null) {
			queryString.setParameter("operationType", operationType);
		}
		if (unitId != null) {
			queryString.setParameter("unitId", unitId);
		}
		if (status != null) {
			queryString.setParameter("status", status);
		}
		if(vendorId != null) {
			queryString.setParameter("vendorId", vendorId);
		}
		return queryString.getResultList();
	}

	@Override
	public List<GatepassData> getGatepass(SearchGatepass gatepass) {
		StringBuilder queryString = new StringBuilder("FROM GatepassData g WHERE g.sendingUnit = :sendingUnit ");
		if (gatepass.getOperationType() != null) {
			queryString.append(" and g.operationType = :operationType ");
		}
		if (gatepass.getStatus() != null) {
			queryString.append(" and g.status = :status ");
		}
		if(gatepass.getVendorSelected() !=null){
			queryString.append(" and g.vendorId = :vendorSelected");
		}
		if (gatepass.getStartDate() != null && gatepass.getStartDate().length() > 0) {
			queryString.append(" and g.createdAt >= :startDate ");
		}
		if (gatepass.getEndDate() != null && gatepass.getEndDate().length() > 0) {
			queryString.append("and g.createdAt <= :endDate ");
		}

		queryString.append("order by g.createdAt desc");
		Query query = manager.createQuery(queryString.toString());
		query.setParameter("sendingUnit", gatepass.getUnitId());
		if (gatepass.getOperationType() != null) {
			query.setParameter("operationType", gatepass.getOperationType());
		}
		if (gatepass.getStatus() != null) {
			query.setParameter("status", gatepass.getStatus());
		}
		if(gatepass.getVendorSelected() !=null){
			query.setParameter("vendorSelected",gatepass.getVendorSelected());
		}
		if (gatepass.getStartDate() != null && gatepass.getStartDate().length() > 0) {
			query.setParameter("startDate", SCMUtil.parseDate(gatepass.getStartDate()));
		}
		if (gatepass.getEndDate() != null && gatepass.getEndDate().length() > 0) {
			query.setParameter("endDate", SCMUtil.parseDate(gatepass.getEndDate()));
		}

		return query.getResultList();
	}
	
}
