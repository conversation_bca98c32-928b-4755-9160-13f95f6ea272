package com.stpl.tech.scm.core.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.FetchTransferOrderService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.TransferOrderManagementDao;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.TransferOrder;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@Service
public class FetchTransferOrderServiceImpl implements FetchTransferOrderService {

    private static final Logger LOG = LoggerFactory.getLogger(FetchTransferOrderServiceImpl.class);

    @Autowired
    private TransferOrderManagementDao transferOrderManagementDao;

    @Autowired
    private MasterDataCache masterDataCache;
    @Autowired
    private SCMCache scmCache;

    @Autowired
    EnvProperties props;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public TransferOrder getTransferOrderDetail(int transferOrderId) {
        TransferOrderData transferOrderData = transferOrderManagementDao.find(TransferOrderData.class, transferOrderId);
        LOG.info("Good received for transfer order Id {}", transferOrderId);
        if (transferOrderData != null) {
            IdCodeName generatedBy = SCMUtil.generateIdCodeName(transferOrderData.getGeneratedBy(), "",
                    masterDataCache.getEmployees().get(transferOrderData.getGeneratedBy()));
            IdCodeName lastUpdatedBy = null;
            if (transferOrderData.getLastUpdatedBy() != null) {
                lastUpdatedBy = SCMUtil.generateIdCodeName(transferOrderData.getLastUpdatedBy(), "",
                        masterDataCache.getEmployees().get(transferOrderData.getLastUpdatedBy()));
            }
            Unit generatedForUnit = masterDataCache.getUnit(transferOrderData.getGeneratedForUnitId());
            Unit generationUnit = masterDataCache.getUnit(transferOrderData.getGenerationUnitId());
            GoodsReceivedData goodsReceivedData = transferOrderManagementDao
                    .getGRFromTransferOrder(transferOrderData.getId());
            TransferOrder transferOrder = SCMDataConverter.convert(transferOrderData, generatedBy, lastUpdatedBy,
                    generatedForUnit, generationUnit, goodsReceivedData, true, scmCache, masterDataCache);
            return transferOrder;
        }
        LOG.info("Transfer order with id {} not found for get detail.", transferOrderId);
        return null;
    }

}
