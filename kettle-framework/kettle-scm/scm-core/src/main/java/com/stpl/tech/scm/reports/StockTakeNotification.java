package com.stpl.tech.scm.reports;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

import java.text.SimpleDateFormat;

public class StockTakeNotification extends EmailNotification {

    private FixedAssetStockTakeReport stockTakeReport;
    String[] toEmails;
    private Boolean submit;

    public StockTakeNotification(FixedAssetStockTakeReport stockTakeReport, String[] toEmails, Boolean submit) {
        this.stockTakeReport = stockTakeReport;
        this.toEmails = toEmails;
        this.submit = submit;
    }

    @Override
    public String[] getToEmails() {
        return SCMUtil.isProd(getEnvironmentType()) ? toEmails : new String[] { "<EMAIL>" };
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        return (SCMUtil.isDev(getEnvironmentType()) ? "[DEV] " : "") +( !submit ? "[IN PROGRESS] " : "[COMPLETE] ") + "Fixed Asset Stock Take Report for : " + stockTakeReport.getUnitName() + " for "
                + new SimpleDateFormat("yyyy-MM-dd").format(stockTakeReport.getBusinessDate());
    }

    @Override
    public String body() throws EmailGenerationException {
        return "<html><p>" + " Stock Take Report for : " + stockTakeReport.getUnitName() + " for "
                + new SimpleDateFormat("yyyy-MM-dd").format(stockTakeReport.getBusinessDate()) + "</p></html>";
    }

    @Override
    public EnvType getEnvironmentType() {
        return stockTakeReport.getEnv();
    }

    public void setToEmails(String[] toEmails){
        this.toEmails = SCMUtil.isProd(getEnvironmentType()) ? toEmails: new String[] { "<EMAIL>" };
    }
}
