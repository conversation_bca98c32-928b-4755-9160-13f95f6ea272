package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 12-06-2017.
 */
@Entity
@Table(name = "SCM_TRANSACTION_EVENT_DRILLDOWN")
public class DayCloseTxnEventDrillDownData {

    private Integer eventId;
    private String eventType;
    private Integer skuId;
    private BigDecimal skuPrice;
    private BigDecimal skuQuantity;
    private BigDecimal skuCost;
    private SCMDayCloseEventData event;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TRANSACTION_EVENT_ID", nullable = false, unique = true)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "EVENT_TYPE")
    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    @Column(name = "SKU_ID")
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "PRICE")
    public BigDecimal getSkuPrice() {
        return skuPrice;
    }

    public void setSkuPrice(BigDecimal skuPrice) {
        this.skuPrice = skuPrice;
    }

    @Column(name = "QUANTITY")
    public BigDecimal getSkuQuantity() {
        return skuQuantity;
    }

    public void setSkuQuantity(BigDecimal skuQuantity) {
        this.skuQuantity = skuQuantity;
    }

    @Column(name = "COST")
    public BigDecimal getSkuCost() {
        return skuCost;
    }

    public void setSkuCost(BigDecimal skuCost) {
        this.skuCost = skuCost;
    }

    @ManyToOne(targetEntity = SCMDayCloseEventData.class, fetch = FetchType.LAZY)
    @JoinColumn(name = "CLOSURE_EVENT_ID", nullable = false)
    public SCMDayCloseEventData getEvent() {
        return event;
    }

    public void setEvent(SCMDayCloseEventData event) {
        this.event = event;
    }
}
