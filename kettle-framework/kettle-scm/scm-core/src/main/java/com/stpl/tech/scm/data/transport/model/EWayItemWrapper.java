package com.stpl.tech.scm.data.transport.model;

import java.math.BigDecimal;

public class EWayItemWrapper {

	private int itemNo; // serial number not TO item number
	private String productName;
	private String productDesc;
	private String hsnCode;
	private BigDecimal quantity = BigDecimal.ZERO;
	private String qtyUnit;
	private BigDecimal taxableAmount = BigDecimal.ZERO;
	private BigDecimal sgstRate = BigDecimal.ZERO;
	private BigDecimal cgstRate = BigDecimal.ZERO;
	private BigDecimal igstRate = BigDecimal.ZERO;
	private BigDecimal cessRate = BigDecimal.ZERO;
	private BigDecimal cessNonAdvol = BigDecimal.ZERO;

	public EWayItemWrapper() {
	}

	public int getItemNo() {
		return itemNo;
	}

	public void setItemNo(int itemNo) {
		this.itemNo = itemNo;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getProductDesc() {
		return productDesc;
	}

	public void setProductDesc(String productDesc) {
		this.productDesc = productDesc;
	}

	public String getHsnCode() {
		return hsnCode;
	}

	public void setHsnCode(String hsnCode) {
		this.hsnCode = hsnCode;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public String getQtyUnit() {
		return qtyUnit;
	}

	public void setQtyUnit(String qtyUnit) {
		this.qtyUnit = qtyUnit;
	}

	public BigDecimal getTaxableAmount() {
		return taxableAmount;
	}

	public void setTaxableAmount(BigDecimal taxableAmount) {
		this.taxableAmount = taxableAmount;
	}

	public BigDecimal getSgstRate() {
		return sgstRate;
	}

	public void setSgstRate(BigDecimal sgstRate) {
		this.sgstRate = sgstRate;
	}

	public BigDecimal getCgstRate() {
		return cgstRate;
	}

	public void setCgstRate(BigDecimal cgstRate) {
		this.cgstRate = cgstRate;
	}

	public BigDecimal getIgstRate() {
		return igstRate;
	}

	public void setIgstRate(BigDecimal igstRate) {
		this.igstRate = igstRate;
	}

	public BigDecimal getCessRate() {
		return cessRate;
	}

	public void setCessRate(BigDecimal cessRate) {
		this.cessRate = cessRate;
	}

	public BigDecimal getCessNonAdvol() {
		return cessNonAdvol;
	}

	public void setCessNonAdvol(BigDecimal cessNonAdvol) {
		this.cessNonAdvol = cessNonAdvol;
	}

}
