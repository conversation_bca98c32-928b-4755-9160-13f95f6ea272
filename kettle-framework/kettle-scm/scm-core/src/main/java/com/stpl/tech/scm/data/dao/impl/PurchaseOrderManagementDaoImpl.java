package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.PurchaseOrderManagementDao;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.PurchaseOrderItemData;
import com.stpl.tech.scm.data.model.StateSequenceId;
import com.stpl.tech.scm.domain.model.PurchaseOrder;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@Repository
public class PurchaseOrderManagementDaoImpl extends SCMAbstractDaoImpl implements PurchaseOrderManagementDao {

    private static final Logger LOG = LoggerFactory.getLogger(PurchaseOrderManagementDaoImpl.class);

    @Override
    public List<PurchaseOrderData> findClonablePurchaseOrders(int vendorId, int deliveryUnitId, int dispatchId) {
        List<PurchaseOrderData> purchaseOrderDataList = null;

        String queryString = "FROM PurchaseOrderData r WHERE r.generatedForVendor = :vendorId " +
                "AND r.dispatchLocationId = :dispatchId AND r.deliveryLocationId = :deliveryUnitId " +
                "AND r.status not in (:cancelled) ORDER BY id DESC";

        List<String> rejectedStatuses = Arrays.asList(PurchaseOrderStatus.CANCELLED.name(),
                PurchaseOrderStatus.REJECTED.name());

        Query query = manager.createQuery(queryString);
        query.setParameter("vendorId",vendorId);
        query.setParameter("dispatchId",dispatchId);
        query.setParameter("deliveryUnitId",deliveryUnitId);
        query.setParameter("cancelled", rejectedStatuses);
        query.setMaxResults(15);
        purchaseOrderDataList = query.getResultList();
        return purchaseOrderDataList;
    }

    @Override
    public List<PurchaseOrderData> findPurchaseOrdersByStatus(Integer vendorId, Integer dispatchId, int deliveryUnitId,
                                                              List<Integer> skus, List<PurchaseOrderStatus> statusList,
                                                              Date startDate, Date endDate, Integer PurchaseOrderId, PurchaseOrderStatus status,PurchaseOrderExtendedStatus expiryStatus) {
        List<PurchaseOrderData> purchaseOrderDataList = null;
        List<String> statuses = statusList.stream().map(PurchaseOrderStatus::name).collect(Collectors.toList());
        if(statuses!=null && !statuses.isEmpty()){

            StringBuilder queryString = new StringBuilder("SELECT DISTINCT r FROM PurchaseOrderData r, PurchaseOrderItemData s");
            queryString.append(" WHERE r.deliveryLocationId = :deliveryUnitId and r.id = s.purchaseOrderData.id");
            queryString.append("  AND r.generationTime >= :startDate");
            queryString.append(" AND r.generationTime <= :endDate");


            if(expiryStatus!=null){
                queryString.append(" AND (r.expiryStatus != :expiryStatus OR r.expiryStatus IS NULL)");
            }

            if(vendorId!=null){
                queryString.append(" AND r.generatedForVendor = :vendorId");
            }
            
            if (status != null) {
                queryString.append(" AND r.status = :status ");
            }else {
            	queryString.append(" AND r.status in (:statusList)");
            }
            
            if (PurchaseOrderId != null) {
                queryString.append(" AND r.id = :id ");
            }

            if(dispatchId!=null){
                queryString.append(" AND r.dispatchLocationId = :dispatchId");
            }

            if(skus!=null && !skus.isEmpty()){
                queryString.append(" AND s.skuId in (:skus)");
            }

            queryString.append(" ORDER BY r.id ASC");

            Query query = manager.createQuery(queryString.toString());

           
            query.setParameter("deliveryUnitId", deliveryUnitId);
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
            if(expiryStatus!=null) {
                query.setParameter("expiryStatus", PurchaseOrderExtendedStatus.EXPIRED.value());
            }

            if(vendorId!=null){
                query.setParameter("vendorId", vendorId);
            }

            if(dispatchId!=null) {
                query.setParameter("dispatchId", dispatchId);
            }
            
            if (status != null) {
                query.setParameter("status", status.name());
            }else {
            	 query.setParameter("statusList", statuses);
            }
            
            if (PurchaseOrderId != null) {
                query.setParameter("id", PurchaseOrderId);
            }

            if(skus!=null && !skus.isEmpty()){
                query.setParameter("skus", skus);
            }
            purchaseOrderDataList = query.getResultList();
        }
        return purchaseOrderDataList;
    }
    @Override
    public List<Integer> getSkuPriceDataId(Integer skuId, Integer skuPackagingId, Integer vendorId, String dispatchLocation, String deliveryLocation){

            StringBuilder queryString = new StringBuilder("SELECT s.skuPriceKeyId " +
                    "FROM SkuPriceData s");
            queryString.append(" WHERE s.skuId = :skuId AND");
            queryString.append(" s.packagingId = :skuPackagingId AND");
            queryString.append(" s.vendorId = :vendorId AND");
            queryString.append(" s.dispatchLocation = :dispatchLocation AND");
            queryString.append(" s.deliveryLocation = :deliveryLocation");
            Query query = manager.createQuery(queryString.toString());
            query.setParameter("skuId", skuId);
            query.setParameter("skuPackagingId", skuPackagingId);
            query.setParameter("vendorId", vendorId);
            query.setParameter("dispatchLocation", dispatchLocation);
            query.setParameter("deliveryLocation", deliveryLocation);

            return query.getResultList();
    }

    @Override
    public List<Pair<BigDecimal, String>> getSkuPriceHistory(Integer skuPriceDataId) {
        List<Pair<BigDecimal, String>> skuPriceHistoryList = new ArrayList<Pair<BigDecimal, String>>();
        StringBuilder queryString = new StringBuilder("SELECT s.negotiatedPrice, s.createdAt " +
                "FROM SkuPriceHistory s");
        queryString.append(" WHERE s.skuPriceDataId = :skuPriceDataId AND s.changeType = :changeType ");
        queryString.append(" ORDER BY s.createdAt DESC" );
        Query query = manager.createQuery(queryString.toString());
        query.setParameter("skuPriceDataId", skuPriceDataId);
        query.setParameter("changeType", ChangeType.PRICE_UPDATE.name());
        query.setMaxResults(5);
        List<Object[]> result = query.getResultList();
        for(Object[] o : result){
            skuPriceHistoryList.add(new Pair<>((BigDecimal) o[0], AppUtils.getDateString(AppUtils.covertDateIST(((Timestamp) o[1]).getTime()),"dd MMM yyyy HH:mm:ss")));
        }
        return skuPriceHistoryList;
    }

    @Override
    public List<PurchaseOrderItemData> checkExtraGrEligibility(Integer unitId, Integer vendorId, Integer dispatchId,
                                                               List<Integer> poIds, List<Integer> skus) {

        String queryStr = "SELECT DISTINCT F FROM PurchaseOrderData E, PurchaseOrderItemData F " +
                "WHERE E.id = F.purchaseOrderData.id AND E.status IN (:statusList) and E.id NOT in (:poIds) " +
                "AND E.generatedForVendor = :vendorId AND E.dispatchLocationId = :dispatchId AND E.deliveryLocationId = :unitId " +
                "AND F.skuId IN (:skus)";
        Query query = manager.createQuery(queryStr);
        query.setParameter("unitId", unitId)
            .setParameter("vendorId", vendorId)
            .setParameter("dispatchId", dispatchId)
            .setParameter("poIds", poIds)
            .setParameter("skus", skus)
            .setParameter("statusList", Arrays.asList(PurchaseOrderStatus.APPROVED.name(),PurchaseOrderStatus.IN_PROGRESS.name()));

        return query.getResultList();
    }

    @Override
    public List<Object[]> getConsumptionForPurchase(int daysInPast, List<Integer> skus, Integer unitId) {
        Date toDate = SCMUtil.getCurrentBusinessDate();
        Date fromDate = SCMUtil.getDayBeforeOrAfterDay(toDate,-daysInPast);
        String queryStr = "SELECT E.skuId, E.closureEvent.businessDate as BUSINESS_DATE, SUM(COALESCE(E.transferred,0) + COALESCE(E.consumed,0) + COALESCE(E.wasted,0)) as CONSUMED " +
                "FROM DayCloseInventoryDrillDown E " +
                "WHERE E.closureEvent.businessDate >= :fromDate AND  E.closureEvent.businessDate < :toDate AND E.skuId IN (:skus) and E.closureEvent.status = :closed " +
                "AND E.closureEvent.dayCloseEventType = :eventType AND E.closureEvent.unitId = :unitId GROUP BY E.skuId, E.closureEvent.businessDate";

        Query query = manager.createQuery(queryStr);
        query.setParameter("unitId", unitId)
                .setParameter("eventType", StockEventType.WH_CLOSING.name())
                .setParameter("fromDate", fromDate)
                .setParameter("toDate", toDate)
                .setParameter("skus", skus)
                .setParameter("closed", StockEventStatus.CLOSED.name());
        return query.getResultList();
    }

    @Override
    public int getNextStateInvoiceId(int stateId, String type) {

        Query query = manager.createQuery("FROM StateSequenceId E where E.stateId = :stateId and E.idType = :idType and E.financialYear IS NULL");
        query.setParameter("idType", type);
        query.setParameter("stateId", stateId);
        StateSequenceId sequence = null;
        try {
            sequence = (StateSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(stateId, type);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    @Override
    public int getNextStateInvoiceId(int stateId, String type, Integer financialYear) {
        Query query = manager.createQuery("FROM StateSequenceId E where E.stateId = :stateId and E.idType = :idType and E.financialYear =:financialYear");
        query.setParameter("idType", type);
        query.setParameter("stateId", stateId);
        query.setParameter("financialYear", financialYear);
        StateSequenceId sequence = null;
        try {
            sequence = (StateSequenceId) query.getSingleResult();
        } catch (NoResultException e) {
            sequence = addStateSequenceId(stateId, type, financialYear);
        }
        int currentValue = sequence.getNextValue();
        sequence.setNextValue(currentValue + 1);
        return currentValue;
    }

    private StateSequenceId addStateSequenceId(int stateId, String type, Integer financialYear) {
        StateSequenceId info = new StateSequenceId(stateId, type, 1, financialYear);
        manager.persist(info);
        return info;
    }

    private StateSequenceId addStateSequenceId(int stateId, String type) {
        StateSequenceId info = new StateSequenceId(stateId, type, 1);
        manager.persist(info);
        return info;

    }


    @Override
    public List<PurchaseOrderData> getAllToBeExpirePurchaseOrder() {
        Date toDate = SCMUtil.getCurrentBusinessDate();
        System.out.println("current date is:"+ toDate);
        Date fromDate = SCMUtil.getDayBeforeOrAfterDay(toDate,-7);
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.expiryDate = :expiryDate and E.advancePaymentData IS NULL");
        query.setParameter("expiryDate", SCMUtil.getCurrentBusinessDate());
        return query.getResultList();
    }

    @Override
    public List<PurchaseOrderData> getAllExpiredAndNeedToClosePurchaseOrders() {
        Date toDate = SCMUtil.getCurrentBusinessDate();
        Date fromDate = SCMUtil.getDayBeforeOrAfterDay(toDate,-7);
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.expiryDate = :expiryDate and E.expiryStatus = :expiryStatus ");
        query.setParameter("expiryDate", AppUtils.getDayBeforeOrAfterDay(SCMUtil.getCurrentBusinessDate(),-7));
        query.setParameter("expiryStatus",PurchaseOrderExtendedStatus.EXPIRED.value());
        return query.getResultList();
    }
    @Override
    public List<PurchaseOrderData> getAllExpiredAndExpiringPurchaseOrders(Date fromDate) {
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.expiryDate = :expiryDate and E.expiryStatus =:expiryStatus and E.advancePaymentData IS NULL");
        query.setParameter("expiryDate", fromDate);
        query.setParameter("expiryStatus",PurchaseOrderExtendedStatus.UN_EXPIRED.value());
        return query.getResultList();
    }

    @Override
    public List<PurchaseOrderData> getPosForAdvance(Integer vendorId) {
        Query query = manager.createQuery("FROM PurchaseOrderData E where E.generatedForVendor = :vendorId and E.status =:status" +
                " and E.expiryStatus IN(:expiryStatus)");
        query.setParameter("vendorId", vendorId);
        query.setParameter("status",PurchaseOrderStatus.APPROVED.value());
        query.setParameter("expiryStatus",Arrays.asList(PurchaseOrderExtendedStatus.UN_EXPIRED.value(), PurchaseOrderExtendedStatus.EXTENDED.value()));
        return query.getResultList();
    }
}
