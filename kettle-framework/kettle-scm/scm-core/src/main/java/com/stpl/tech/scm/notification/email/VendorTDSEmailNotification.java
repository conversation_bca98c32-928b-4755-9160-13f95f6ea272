package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.TDSEmailTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Objects;

public class VendorTDSEmailNotification extends EmailNotification {

    private TDSEmailTemplate tdsEmailTemplate;
    private EnvType envType;
    private List<String> toEmails;
    private String quarter;
    private String financialYear;
    private String vendorName;

    public VendorTDSEmailNotification(TDSEmailTemplate tdsEmailTemplate, EnvType envType, List<String> toEmails, String quarter, String financialYear, String vendorName) {
        this.tdsEmailTemplate = tdsEmailTemplate;
        this.envType = envType;
        this.toEmails = toEmails;
        this.quarter = quarter;
        this.financialYear = financialYear;
        this.vendorName = vendorName;
    }

    @Override
    public String[] getToEmails() {
        if(!Objects.nonNull(this.toEmails)) {
            return new String[0];
        }
        String[] retArray = new String[this.toEmails.size()];
        return SCMUtil.isDev(envType) ? new String[]{"<EMAIL>"} : this.toEmails.toArray(retArray);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "TDS Certificate for " + quarter + " " + financialYear
                + " : " + vendorName;
        if (SCMUtil.isDev(envType)) {
            subject = "[Dev] " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return tdsEmailTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
