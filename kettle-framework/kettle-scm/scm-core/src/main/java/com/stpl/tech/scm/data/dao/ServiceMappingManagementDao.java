package com.stpl.tech.scm.data.dao;

import java.math.BigDecimal;
import java.util.List;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.scm.data.model.AdditionalDocumentsMaster;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.domain.model.CostElementPriceUpdate;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import org.apache.commons.math3.analysis.function.Add;

public interface ServiceMappingManagementDao extends SCMAbstractDao {

	List<CostElementData> getCostElement();

	List<IdCodeNameStatus> getCostELementVendorMappings(Integer costElementId);

	boolean addCostElementVendorMappings(int employeeId, String employeeName, int costElementId,
			List<Integer> vendorIds);

	List<IdCodeNameStatus> getAllVendorList();

	boolean updateCostElementVendorMapping(int employeeId, String employeeName, int vendorId, int costElementId,
			String status);

	boolean addVendorCostElementMappings(int employeeId, String employeeName, int vendorId,
			List<Integer> costElementIds);

	List<IdCodeNameStatus> getVendorCostElementMappings(Integer vendorId);

	List<IdCodeNameStatus> getAllCostCentres();

	boolean addCostElementCostCenterMappings(int employeeId, String employeeName, int costElementId,
			List<Integer> costCentersId);

	List<IdCodeNameStatus> searchCostELementCostCenterMapping(Integer costElementId);

	boolean updateCostElementCostCenterMappings(int employeeId, String employeeName, int costElementId,
			int costCenterId, String status);

	boolean addCostCenterToCostElementMappings(int employeeId, String employeeName, int costCenterId,
			List<Integer> costElementIds);

	List<IdCodeNameStatus> searchCostCenterToCostElementMappings(Integer costCenterId);

	List<CostElementPriceUpdate> getPricedCostElements(Integer vendorId, Integer costCenterId);

	boolean updateStatusCostElementPriceMappings(int employeeId, String employeeName, int costElementId, String status);

	boolean addCostElementPriceMapping(Integer vendorId, Integer costCenterId, Integer costElementId, BigDecimal price,
			Integer employeeId, String employeeName);

	boolean updateCostElementPriceMappings(Integer costElementMappingId, BigDecimal price, Integer employeeId,
			String employeeName);

	AdditionalDocumentsMaster addMasterDocument(AdditionalDocumentsMaster additionalDocumentsMaster) throws DataUpdationException;

	List<IdCodeName> getCostElementDocumentMappings(Integer costElementId);

	boolean addCostElementDocumentMappings(int employeeId, String employeeName, int costElementId,
										 List<Integer> documentIds);

	List<AdditionalDocumentsMaster> getDocumentList();

	boolean updateCostElementDocumentMapping(int documentId, int costElementId);

	List<AdditionalDocumentsMaster> getAdditionalDocs();
}
