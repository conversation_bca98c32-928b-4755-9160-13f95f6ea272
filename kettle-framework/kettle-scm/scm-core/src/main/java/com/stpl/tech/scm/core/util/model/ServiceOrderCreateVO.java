package com.stpl.tech.scm.core.util.model;

import com.stpl.tech.scm.domain.model.ServiceOrderItem;

import java.util.List;

/**
 * Copyright (C) $today.year, Sunshine Teahouse Private Limited - All Rights
 * Reserved Unauthorized copying of this file, via any medium is strictly
 * prohibited Proprietary and confidential
 */
public class ServiceOrderCreateVO {

	private int userId;
	private int dispatchLocationId;
	private int vendorId;
	private String comment;
	private List<ServiceOrderItem> items;
	private Integer costCenterId;
	private String type;
	private String tagName;

	public int getUserId() {
		return userId;
	}

	public void setUserId(int userId) {
		this.userId = userId;
	}

	public int getDispatchLocationId() {
		return dispatchLocationId;
	}

	public void setDispatchLocationId(int dispatchLocationId) {
		this.dispatchLocationId = dispatchLocationId;
	}

	public int getVendorId() {
		return vendorId;
	}

	public void setVendorId(int vendorId) {
		this.vendorId = vendorId;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public List<ServiceOrderItem> getItems() {
		return items;
	}

	public void setItems(List<ServiceOrderItem> items) {
		this.items = items;
	}

	public Integer getCostCenterId() {
		return costCenterId;
	}

	public void setCostCenterId(Integer costCenterId) {
		this.costCenterId = costCenterId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}
	
	
	
	
}
