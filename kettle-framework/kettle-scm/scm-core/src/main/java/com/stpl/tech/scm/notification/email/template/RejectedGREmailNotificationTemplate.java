package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.domain.model.RejectedGr;
import com.stpl.tech.scm.domain.model.RejectedGrItem;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import org.apache.velocity.tools.generic.MathTool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RejectedGREmailNotificationTemplate extends AbstractVelocityTemplate {

    private List<RejectedGrItem> rejectedGrItems;
    private UnitBasicDetail receivingUnit;
    private String basePath;
    private RejectedGr rejectedGr;

    public RejectedGREmailNotificationTemplate() {
    }

    public RejectedGREmailNotificationTemplate(List<RejectedGrItem> rejectedGrItems, UnitBasicDetail receivingUnit, String basePath, RejectedGr rejectedGr) {
        this.rejectedGrItems = rejectedGrItems;
        this.receivingUnit = receivingUnit;
        this.basePath = basePath;
        this.rejectedGr = rejectedGr;
    }

    @Override
    public String getTemplatePath() {
        return "templates/RejectedGREmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/rejectedGr/" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("rejectedGrItems", rejectedGrItems);
        stringObjectMap.put("unit", receivingUnit);
        stringObjectMap.put("mathTool", new MathTool());
        stringObjectMap.put("rejectedGr", rejectedGr);
        return stringObjectMap;
    }

    public List<RejectedGrItem> getRejectedGrItems() {
        return rejectedGrItems;
    }

    public UnitBasicDetail getReceivingUnit() {
        return receivingUnit;
    }
}
