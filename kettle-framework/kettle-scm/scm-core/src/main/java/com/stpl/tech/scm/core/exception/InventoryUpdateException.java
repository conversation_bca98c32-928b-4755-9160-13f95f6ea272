package com.stpl.tech.scm.core.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by chaayos on 17-05-2017.
 */
@ResponseStatus(value = HttpStatus.NOT_ACCEPTABLE, reason = "Inventory Update Exception.")
public class InventoryUpdateException extends Exception{

    private static final long serialVersionUID = 6163156256372353453L;

    private SCMError code;

    public InventoryUpdateException() {
    }

    public InventoryUpdateException(String message) {
        super(message);
        this.code = new SCMError("Error in Inventory update", message, 702);
    }

    public InventoryUpdateException(String title, String message) {
        super(message);
        this.code = new SCMError(title, message, 702);
    }


    public InventoryUpdateException(SCMError code) {
        super(code.getErrorMsg());
        this.code = code;
    }

    public SCMError getCode() {
        return code;
    }

    public InventoryUpdateException(Throwable cause) {
        super(cause);
    }

    public InventoryUpdateException(String message, Throwable cause) {
        super(message, cause);
    }

    public InventoryUpdateException(String message, Throwable cause, boolean enableSuppression,
                         boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
