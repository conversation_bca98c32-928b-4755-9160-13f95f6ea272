package com.stpl.tech.scm.core.util.model;

import com.stpl.tech.scm.domain.model.ServiceReceiveItem;

import java.util.List;

public class ServiceReceiveVO {
	private int userId;
	private int dispatchLocationId;
	private int companyId;
	private int deliveryLocationId;
	private int vendorId;
	private List<ServiceReceiveItem> items;
	private Integer deliveryStateId;
	private Boolean isProvisional;
	private List<Integer> removeDrillDownIdList;

	public List<Integer> getRemoveDrillDownIdList() {
		return removeDrillDownIdList;
	}

	public void setRemoveDrillDownIdList(List<Integer> removeDrillDownIdList) {
		this.removeDrillDownIdList = removeDrillDownIdList;
	}

	public Boolean getIsProvisional() {
		return isProvisional;
	}

	public void setIsProvisional(Boolean provisional) {
		isProvisional = provisional;
	}

	public int getUserId() {
		return userId;
	}

	public void setUserId(int userId) {
		this.userId = userId;
	}

	public int getDispatchLocationId() {
		return dispatchLocationId;
	}

	public void setDispatchLocationId(int dispatchLocationId) {
		this.dispatchLocationId = dispatchLocationId;
	}

	public int getVendorId() {
		return vendorId;
	}

	public void setVendorId(int vendorId) {
		this.vendorId = vendorId;
	}

	public int getCompanyId() {
		return companyId;
	}

	public void setCompanyId(int companyId) {
		this.companyId = companyId;
	}

	public int getDeliveryLocationId() {
		return deliveryLocationId;
	}

	public void setDeliveryLocationId(int deliveryLocationId) {
		this.deliveryLocationId = deliveryLocationId;
	}

	public List<ServiceReceiveItem> getItems() {
		return items;
	}

	public void setItems(List<ServiceReceiveItem> items) {
		this.items = items;
	}

	public Integer getDeliveryStateId() {
		return deliveryStateId;
	}

	public void setDeliveryStateId(Integer deliveryStateId) {
		this.deliveryStateId = deliveryStateId;
	}
}
