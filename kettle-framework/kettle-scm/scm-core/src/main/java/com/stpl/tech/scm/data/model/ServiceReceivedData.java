package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "SERVICE_RECEIVED_DATA")
public class ServiceReceivedData {

	private Integer serviceReceivedId;
	private Integer vendorId;
	private Integer dispatchLocationId;
	private Integer updatedBy;
	private Integer createdBy;
	private BigDecimal totalAmount;
	private BigDecimal totalTaxes;
	private BigDecimal price;
	private BigDecimal extraCharges;
	private Date updatedAt;
	private Date createdAt;
	private String serviceReceiveStatus;
	private String paymentStatus;
	private PaymentRequestData paymentRequestData;
	private List<ServiceReceivedItemData> serviceItemList = new ArrayList<>(0);
	private List<ServiceOrderServiceReceiveMappingData> serviceOrderMappingList = new ArrayList<>(0);
	private Integer companyId;
	private Integer deliveryLocationId;
	private Integer deliveryStateId;
	private String toBePaid = "Y";
	private Date businessDate;
	private String type;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SERVICE_RECEIVED_ID", nullable = false, unique = true)
	public Integer getServiceReceivedId() {
		return serviceReceivedId;
	}

	public void setServiceReceivedId(Integer serviceReceivedId) {
		this.serviceReceivedId = serviceReceivedId;
	}

	@Column(name = "UPDATED_BY", nullable = true)
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "UPDATED_AT", nullable = true)
	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Column(name = "CREATED_AT", nullable = false)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	@Column(name = "VENDOR_ID", nullable = false)
	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer generatedForVendor) {
		this.vendorId = generatedForVendor;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "TOTAL_TAX", nullable = false)
	public BigDecimal getTotalTaxes() {
		return totalTaxes;
	}

	public void setTotalTaxes(BigDecimal totalTaxes) {
		this.totalTaxes = totalTaxes;
	}

	@Column(name = "TOTAL_PRICE", nullable = false)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal totalPrice) {
		this.price = totalPrice;
	}

	@Column(name = "EXTRA_CHARGES", nullable = true)
	public BigDecimal getExtraCharges() {
		return extraCharges;
	}

	public void setExtraCharges(BigDecimal extraCharges) {
		this.extraCharges = extraCharges;
	}

	@Column(name = "PAYMENT_STATUS", length = 100)
	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PAYMENT_REQUEST_ID")
	public PaymentRequestData getPaymentRequestData() {
		return paymentRequestData;
	}

	public void setPaymentRequestData(PaymentRequestData paymentRequestData) {
		this.paymentRequestData = paymentRequestData;
	}

	@Column(name = "COMPANY_ID")
	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	@Column(name = "TO_BE_PAID", length = 1, nullable = false)
	public String getToBePaid() {
		return toBePaid;
	}

	public void setToBePaid(String toBePaid) {
		this.toBePaid = toBePaid;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceReceivedData")
	public List<ServiceOrderServiceReceiveMappingData> getServiceOrderMappingList() {
		return serviceOrderMappingList;
	}

	public void setServiceOrderMappingList(List<ServiceOrderServiceReceiveMappingData> serviceOrderMappingList) {
		this.serviceOrderMappingList = serviceOrderMappingList;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "serviceReceivedDataId")
	public List<ServiceReceivedItemData> getServiceItemList() {
		return serviceItemList;
	}

	public void setServiceItemList(List<ServiceReceivedItemData> serviceItemList) {
		this.serviceItemList = serviceItemList;
	}

	@Column(name = "DISPATCH_LOCATION_ID")
	public Integer getDispatchLocationId() {
		return dispatchLocationId;
	}

	public void setDispatchLocationId(Integer dispatchLocationId) {
		this.dispatchLocationId = dispatchLocationId;
	}

	@Column(name = "SERVICE_RECEIVE_STATUS")
	public String getServiceReceiveStatus() {
		return serviceReceiveStatus;
	}

	public void setServiceReceiveStatus(String serviceReceiveStatus) {
		this.serviceReceiveStatus = serviceReceiveStatus;
	}

	@Column(name = "DELIVERY_LOCATION_ID")
	public Integer getDeliveryLocationId() {
		return deliveryLocationId;
	}

	public void setDeliveryLocationId(Integer deliveryLocationId) {
		this.deliveryLocationId = deliveryLocationId;
	}

	@Column(name = "DELIVERY_STATE_ID")
	public Integer getDeliveryStateId() {
		return deliveryStateId;
	}

	public void setDeliveryStateId(Integer deliveryStateId) {
		this.deliveryStateId = deliveryStateId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE")
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name="TYPE")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
